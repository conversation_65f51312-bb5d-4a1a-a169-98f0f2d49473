table 1. 
 CREATE TABLE `inventory_modules_staging`.`platform_notifications` (
  `id` INT NOT NULL,
  `template_name` VARCHAR(100) NULL,
  `slug_name` VARCHAR(255) NULL,
  `header` VA<PERSON><PERSON><PERSON>(255) NULL,
  `body` VA<PERSON>HAR(255) NULL,
  `footer` VARCHAR(255) NULL,
  `copy_right` VARCHAR(255) NULL,
  `status` INT(1) NULL,
  `deleted_at` DATETIME NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `template_name_UNIQUE` (`template_name` ASC));

table 2.
  CREATE TABLE `inventory_modules_staging`.`notification_type` (
  `id` INT NOT NULL,
  `type` VARCHAR(50) NULL,
  PRIMARY KEY (`id`));

  table 3.

  CREATE TABLE `inventory_modules_staging`.`partner_notifications` (
  `id` INT(10) NOT NULL,
  `platform_notification_id` INT(10) NULL,
  `notification_type_id` INT(10) NULL DEFAULT 0,
  `status` INT(1) NULL DEFAULT 0,
  <PERSON><PERSON><PERSON><PERSON> KEY (`id`));


ALTER TABLE tickets
ADD COLUMN affiliate_business_id int(11) unsigned DEFAULT NULL;
Deployed

// Change for Hour case 

ALTER TABLE tickets
CHANGE COLUMN paid_hour paid_hour DECIMAL(8,2) DEFAULT NULL;
- Deployed

ALTER TABLE `inventory_modules_staging`.`facilities` 
ADD COLUMN `is_permit_purchase_enabled` ENUM('0', '1') NULL DEFAULT '1' AFTER `is_checkout_enabled`;

// Permit Type Document List

CREATE TABLE `permit_type_master` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) DEFAULT NULL,
  `permit_type_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `permit_type_desc` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `is_status` enum('0','1') DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE `permit_type_document_list` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) DEFAULT NULL,
  `permit_type_id` int(11) DEFAULT NULL,
  `document_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `document_type` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `document_key` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



CREATE TABLE `permit_request_documents` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) DEFAULT NULL,
  `facility_id` int(11) DEFAULT NULL,
  `permit_request_id` int(11) DEFAULT NULL,
  `user_permit_request_id` int(11) DEFAULT NULL,
  `document_key` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `document_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE `user_permit_vehicle_mapping` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `permit_vehicle_id` int(11) NOT NULL,
  `permit_request_id` int(11) NOT NULL,
  `facility_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

ALTER TABLE facilities
ADD COLUMN is_mobile_facility int(1) COLLATE utf8_unicode_ci DEFAULT '1';

#ALTER TABLE document_type RENAME permit_type_document_list;

ALTER TABLE `permit_type_document_list` 
CHANGE COLUMN `facility_id` `permit_type_id` int(11) DEFAULT NULL;

ALTER TABLE permit_requests
ADD COLUMN permit_type int(11) unsigned DEFAULT NULL;


ALTER TABLE permit_requests
ADD COLUMN session_id varchar(100) DEFAULT NULL;


ALTER TABLE permit_requests
ADD COLUMN is_payment_authrize int(11) unsigned DEFAULT NULL;


ALTER TABLE user_permit_requests
  ADD COLUMN is_payment_authrize int(11) unsigned DEFAULT NULL,
	ADD COLUMN name_on_card varchar(100) DEFAULT NULL,
	ADD COLUMN card_last_four varchar(50) DEFAULT NULL,
	ADD COLUMN card_type varchar(50) DEFAULT NULL,
	ADD COLUMN card_name varchar(50) DEFAULT NULL,
	ADD COLUMN expiry varchar(10) DEFAULT NULL,
	ADD COLUMN session_id varchar(100) DEFAULT NULL,
	ADD COLUMN tx_state_text varchar(100) DEFAULT NULL,
	ADD COLUMN tx_state varchar(100) DEFAULT NULL,
	ADD COLUMN result_reason varchar(255) DEFAULT NULL,
	ADD COLUMN currency_used varchar(50) DEFAULT NULL;

ALTER TABLE `inventory_modules_staging`.`users` 
CHANGE COLUMN `user_type` `user_type` ENUM('1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12') NOT NULL DEFAULT '1' COMMENT '1 superadmin, 2 admin, 3 partner, 4 partneer user 5=>user' ;

ALTER TABLE users
ADD COLUMN user_parent_id int(11) unsigned DEFAULT NULL;
- Deployed on preprod

ALTER TABLE users
ADD COLUMN slug VARCHAR(50) DEFAULT NULL;
-  Deployed on Preprod

ALTER TABLE permit_request_documents
ADD COLUMN doc_name varchar(50) DEFAULT NULL,
ADD COLUMN doc_key varchar(50) DEFAULT NULL;
- Deployed on preprod

ALTER TABLE permit_requests
ADD COLUMN permit_type_name varchar(50) DEFAULT NULL;
- Deployed on preprod

ALTER TABLE user_permit_requests
ADD COLUMN permit_type_name varchar(50) DEFAULT NULL;
- Deployed on preprod

ALTER TABLE `inventory_modules_staging`.`permit_rate_descriptions` 
ADD COLUMN `label` VARCHAR(255) NULL DEFAULT NULL AFTER `deleted_at`;
- Deployed on preprod

########   VIJAY : 14-09-2023  Change in RM For Report : 
ALTER TABLE `inventory_modules_staging`.`report_menus` ADD COLUMN `user_id` INT NULL DEFAULT NULL AFTER `updated_at`;


CREATE TABLE `customerportal_permissions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) NOT NULL,
  `rm_id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `display_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `web` text COLLATE utf8_unicode_ci,
  `type` enum('0','1','2') COLLATE utf8_unicode_ci DEFAULT '0',
  `parent_id` int(11) DEFAULT NULL,
  `list_order` int(4) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE `customerportal_permissions_master` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `display_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `web` text COLLATE utf8_unicode_ci,
  `is_default` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '0',
  `type` enum('0','1','2') COLLATE utf8_unicode_ci DEFAULT '0',
  `parent_id` int(11) DEFAULT NULL,
  `list_order` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


ALTER TABLE reservations
ADD COLUMN reservation_amount double(10,2) DEFAULT NULL,
ADD COLUMN parking_amount double(10,2) DEFAULT NULL;

ALTER TABLE `inventory_modules_staging`.`anet_transactions` 
DROP FOREIGN KEY `anet_transactions_user_id_foreign`;
ALTER TABLE `inventory_modules_staging`.`anet_transactions` 
CHANGE COLUMN `user_id` `user_id` INT(10) UNSIGNED NULL DEFAULT 0 ;
ALTER TABLE `inventory_modules_staging`.`anet_transactions` 
ADD CONSTRAINT `anet_transactions_user_id_foreign`
  FOREIGN KEY (`user_id`)
  REFERENCES `inventory_modules_staging`.`users` (`id`);

ALTER TABLE `inventory_modules_staging`.`anet_transactions` 
DROP FOREIGN KEY `anet_transactions_user_id_foreign`;
ALTER TABLE `inventory_modules_staging`.`anet_transactions` 
CHANGE COLUMN `user_id` `user_id` INT(10) UNSIGNED NULL DEFAULT 0 ,
DROP INDEX `anet_transactions_user_id_foreign` ;

CREATE TABLE `customer_permissions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) NOT NULL,
  `rm_id` int(11) DEFAULT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

- Deployed on preprod

CREATE TABLE parkengage_preprod.`customerportal_permissions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) DEFAULT NULL,
  `rm_id` int(11) DEFAULT NULL,
  `customer_permission_id` int(11) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `display_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `web` text COLLATE utf8_unicode_ci,
  `type` enum('0','1','2') COLLATE utf8_unicode_ci DEFAULT '0',
  `parent_id` int(11) DEFAULT NULL,
  `list_order` int(4) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_default` int(1) DEFAULT '0',
  `title` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-Deployed  on preprod


CREATE TABLE `customerportal_permissions_master` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `display_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `web` text COLLATE utf8_unicode_ci,
  `is_default` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '0',
  `type` enum('0','1','2') COLLATE utf8_unicode_ci DEFAULT '0',
  `parent_id` int(11) DEFAULT NULL,
  `list_order` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
-Deployed  on preprod

#########Ashutosh 22-09-2023 change in reservation reservation_amount,parking_amount set to default.
ALTER TABLE `reservations` ADD `reservation_amount` DOUBLE(10,2) NULL DEFAULT '0.00' AFTER `license_plate`, ADD `parking_amount` DOUBLE(10,2) NULL DEFAULT '0.00' AFTER `reservation_amount`;

#######Ashutosh 26-09-2023 Ashutosh 
ALTER TABLE `facilities` ADD `citation_format` VARCHAR(255) NULL;


CREATE TABLE `parkengage_prestage`.`business_qrcode` (
  `id` INT NOT NULL,
  `business_qrcode` VARCHAR(45) NOT NULL,
  `facility_id` INT(10) NULL,
  `policy_id` INT(10) NULL,
  `business_id` INT(10) NULL,
  `title` VARCHAR(45) NULL,
  `qrcode_logo` VARCHAR(45) NULL,
  `qrcode_type` INT(10) NULL,
  `deleted_at` DATETIME NULL,
  PRIMARY KEY (`id`));


  // this key ki added in facilities table for show in mobile app,pls see for  reference PIMS-5324
  ALTER TABLE `inventory_modules_staging`.`facilities` 
  ADD COLUMN `is_lot` TINYINT(1) NULL DEFAULT '0' AFTER `is_lpr_cloud_payment_enabled`;
  
DataTrans Payment Change:

ALTER TABLE facility_payment_details
ADD COLUMN data_trans_url varchar(255) DEFAULT NULL,
ADD COLUMN data_trans_username varchar(100) DEFAULT NULL,
ADD COLUMN data_trans_password varchar(100) DEFAULT NULL,
ADD COLUMN data_trans_payment_env varchar(100) DEFAULT NULL;


//This column is used for whitelist the permit with skidata
ALTER TABLE `inventory_modules_staging`.`permit_requests` 
ADD COLUMN `skidata_id` VARCHAR(255) NULL AFTER `permit_type_name`;

// store unique VALUES
ALTER TABLE `inventory_modules_staging`.`permit_requests` 
ADD COLUMN `skidata_value` VARCHAR(255) NULL AFTER `skidata_id`;

ALTER TABLE facility_payment_details
ADD COLUMN data_trans_username_web varchar(100) DEFAULT NULL,
ADD COLUMN data_trans_password_web varchar(100) DEFAULT NULL;

#Vijay : 09-10-2023 New Townsend Change :pims -5375

CREATE TABLE `inventory_modules_staging`.`frontend_facilities` (
  `id` INT NOT NULL,
  `facility_id` INT NULL DEFAULT 0,
  `partner_id` INT NULL DEFAULT 0,
  `is_active_mobile` TINYINT(1) NULL DEFAULT 0,
  `is_active_web` TINYINT(1) NULL DEFAULT 0,
  `active` TINYINT(1) NULL DEFAULT 1,
  `created_at` TIMESTAMP NULL DEFAULT NULL,
  `updated_at` TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `inventory_modules_staging`.`frontend_facilities` 
CHANGE COLUMN `id` `id` INT(11) NOT NULL AUTO_INCREMENT ;
ALTER TABLE `inventory_modules_staging`.`frontend_facilities` 
ADD COLUMN `deleted_at` TIMESTAMP NULL DEFAULT NULL AFTER `updated_at`;


ALTER TABLE `inventory_modules_staging`.`users` 
ADD COLUMN `is_rm_enabled` TINYINT(1) NULL DEFAULT 0 COMMENT '0 => RM Not Enabled, 1=> RM Enabled this flag is used to check frontend facility list' AFTER `default_rm`;

#####

ALTER TABLE `overstay_tickets` 
ADD COLUMN `payment_method` varchar(100) DEFAULT NULL;


ALTER TABLE `reservations` 
ADD COLUMN `payment_method` varchar(100) DEFAULT NULL;

ALTER TABLE `inventory_modules_staging`.`facility_payment_details` 
ADD COLUMN `payment_method` Text NULL ,
ADD COLUMN `dataTrans_cancel_url` Text NULL ,
ADD COLUMN `dataTrans_error_url` Text NULL ;

payment_method

#Vijay : 12-10-2023 Townsend - Overnight charges implementation :pims -5397
CREATE TABLE `inventory_modules_staging`.`category_facility_overnight_durations` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `rate_category_id` INT NULL DEFAULT 0,
  `facility_id` INT NULL DEFAULT 0,
  `partner_id` INT NULL DEFAULT 0,
  `active` TINYINT(1) NULL DEFAULT 0,
  `overnigth_start_time` TIMESTAMP NULL DEFAULT NULL,
  `overnight_end_time` TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (`id`));
ALTER TABLE `inventory_modules_staging`.`category_facility_overnight_durations` 
CHANGE COLUMN `overnigth_start_time` `overnigth_start_time` TIME NULL DEFAULT NULL ,
CHANGE COLUMN `overnight_end_time` `overnight_end_time` TIME NULL DEFAULT NULL ;


INSERT INTO `inventory_modules_staging`.`rate_categories` (`category`, `created_at`, `updated_at`, `partner_id`) VALUES ('Overnight', '2023-10-12 06:23:00', '2023-10-12 06:23:00', '358642');
INSERT INTO `inventory_modules_staging`.`category_facility_overnight_durations` (`id`, `rate_category_id`, `facility_id`, `partner_id`, `active`, `overnigth_start_time`, `overnight_end_time`) VALUES ('1', '316', '204', '358642', '1', '00:00:00', '30:00:00');

!!!!!

ALTER TABLE `facility_payment_details` 
ADD COLUMN `datacap_ecommerce_token` varchar(100) default NULL AFTER datacap_token;


#######
for Dynamic base URL 16/10/2023    Ashish

ALTER TABLE inventory_modules_staging.mobile_device_version ADD `base_url` VARCHAR(255) NULL DEFAULT NULL AFTER `app_theme_color`;
DATACAP_AUTHONLY_URL

ALTER TABLE `facility_payment_details` 
ADD COLUMN `datacap_authonly_url` varchar(100) default NULL AFTER datacap_ecommerce_token;


DATACAP_PAYMENT_PRE_AUTH_URL

ALTER TABLE `facility_payment_details` 
ADD COLUMN `datacap_preauth_url` varchar(100) default NULL AFTER datacap_authonly_url;

ALTER TABLE `event_categories` 
ADD COLUMN `rm_id` int(11) NULL AFTER partner_id,
ADD COLUMN `created_by` int(11) NULL AFTER rm_id; done

ALTER TABLE `events` 
ADD COLUMN `rm_id` int(11) NULL,
ADD COLUMN `created_by` int(11) NULL; done




for Dynamic base URL 19/10/2023    Ashish

ALTER TABLE inventory_modules_staging.mobile_device_version ADD `client_id` VARCHAR(255) NULL DEFAULT NULL AFTER `base_url`; done

ALTER TABLE inventory_modules_staging.mobile_device_version ADD `client_secret` VARCHAR(255) NULL DEFAULT NULL AFTER `client_id`; done

ALTER TABLE inventory_modules_staging.mobile_device_version ADD `app_key` VARCHAR(255) NULL DEFAULT NULL AFTER `client_secret`; done

ALTER table users ADD COLUMN attendent_type varchar(50) DEFAULT NULL; done



ALTER table ticket_citations ADD COLUMN is_reopen int(1) DEFAULT NULL;


CREATE TABLE `inventory_modules_staging`.`event_user` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `partner_id` INT NOT NULL,
    `rm_id` INT NOT NULL,
    `user_id` INT NOT NULL,
    `event_id` INT NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);


ALTER table infractions ADD COLUMN rm_id int(11) DEFAULT NULL;

ALTER table ticket_citations ADD COLUMN rm_id int(11) DEFAULT NULL;

ALTER table ticket_citations ADD COLUMN is_admin_citation int(1) DEFAULT '0';

ALTER table reservations ADD COLUMN terminal_id VARCHAR(255) DEFAULT NULL;

#VIJAY 
ALTER TABLE `inventory_modules_staging`.`ticket_extends` 
ADD COLUMN `discount_amount` DOUBLE(10,2) NULL DEFAULT 0 AFTER `base_length`,
ADD COLUMN `comment` VARCHAR(255) NULL DEFAULT NULL AFTER `discount_amount`;


ALTER table reservations ADD COLUMN is_event_scan ENUM('0', '1') NULL DEFAULT '0';

ALTER table reservations ADD COLUMN event_scan_at timestamp NULL DEFAULT NULL AFTER is_event_scan;

ALTER table reservations ADD COLUMN checkin_gate int(11) NULL DEFAULT NULL;

ALTER table planet_payment_profile ADD COLUMN is_default ENUM('0', '1') NULL DEFAULT '0';

ALTER table datacap_payment_profile ADD COLUMN is_default ENUM('0', '1') NULL DEFAULT '0';


ALTER TABLE `inventory_modules`.`reservations` 
ADD COLUMN `booking_source` VARCHAR(255) NULL DEFAULT NULL AFTER `parking_amount`;
ALTER table permit_requests ADD COLUMN acknowledge ENUM('0', '1') NULL DEFAULT '0';

ALTER table user_permit_requests ADD COLUMN acknowledge ENUM('0', '1') NULL DEFAULT '0';

ALTER table reservations ADD COLUMN session_id VARCHAR(255) NULL DEFAULT NULL;


CREATE TABLE `cruise` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(10) DEFAULT NULL,
  `facility_id` int(11) DEFAULT NULL,
  `cruise_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `is_active` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

ALTER table gates ADD COLUMN cruise_id int(11) NULL DEFAULT NULL;

ALTER table reservations ADD COLUMN checkin_gate int(11) NULL DEFAULT NULL;

ALTER TABLE reservations CHANGE checkin_gate cruise_id int(11) NULL DEFAULT NULL;

ALTER table reservations ADD COLUMN cruise_id int(11) NULL DEFAULT NULL;

ALTER table warnings ADD COLUMN rm_id int(11) DEFAULT NULL;

ALTER table warnings ADD COLUMN meter_number varchar(255) DEFAULT NULL;

ALTER table warnings ADD COLUMN beat varchar(255) DEFAULT NULL;
// CREATE infraction for multiple facility
  CREATE TABLE `inventory_modules_staging`.`infraction_facility` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `infraction_id` INT NULL,
  `facility_id` INT NULL,
  `deleted_at` DATETIME NULL,
  PRIMARY KEY (`id`));

ALTER TABLE `facilities` 
ADD COLUMN `is_promocode_validation_enabled` ENUM('0', '1','2','3') NULL DEFAULT '0';

//store favicon in brand setting table and facility brand table
   ALTER TABLE `inventory_modules_staging`.`brand_settings` 
ADD COLUMN `favicon` TEXT NULL DEFAULT NULL AFTER `sidebar_style`;

   ALTER TABLE `inventory_modules_staging`.`facility_brand_settings` 
ADD COLUMN `favicon` TEXT NULL DEFAULT NULL AFTER `banner`;



   ALTER TABLE `permit_type_document_list` 
ADD COLUMN `file_size_limit` int(11) NULL DEFAULT NULL AFTER `document_key`;


CREATE TABLE `attendent_type` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `partner_id` INT(11) NOT NULL,
    `rm_id` INT(11) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `value` VARCHAR(255) NOT NULL,
    `status` ENUM('0', '1') NULL DEFAULT '1',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);

   ALTER TABLE `tickets` 
ADD COLUMN `is_priceband_apply` ENUM('0', '1') NULL DEFAULT '0';

   ALTER TABLE `ticket_extends` 
ADD COLUMN `is_priceband_apply` ENUM('0', '1') NULL DEFAULT '0';

//sql Query for cruise 
CREATE TABLE `cruise_facility` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `cruise_id` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `parking_start_time` time DEFAULT NULL,
  `parking_end_time` time DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `is_active` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '1',
  'cruise_id' int(10) DEFAULT NULL,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=579 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE `cruise_facility` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `cruise_id` int(10) unsigned NOT NULL,
  `facility_id` int(10) unsigned NOT NULL
 
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

ALTER TABLE cruise
ADD slug varchar(255)  NOT NULL;

 ALTER TABLE `inventory_modules_staging`.`cruise` 
ADD COLUMN `description` VARCHAR(255) NULL DEFAULT NULL AFTER `slug`;


schedule_id

ALTER TABLE reservations
ADD schedule_id int(11) NULL DEFAULT NULL;




ALTER TABLE cruise
ADD start_time int(11) NULL DEFAULT NULL,
ADD end_time int(11) NULL DEFAULT NULL;

ALTER TABLE cruise
DROP COLUMN facility_id;

ALTER TABLE tickets
ADD event_user_id int(11) NULL DEFAULT NULL;

ALTER TABLE reservations
ADD event_user_id int(11) NULL DEFAULT NULL;


ALTER TABLE events
MODIFY COLUMN parking_start_time timestamp;

ALTER TABLE events
MODIFY COLUMN parking_end_time timestamp;


ALTER TABLE `inventory_modules_staging`.`cruise_schedule` 
CHANGE COLUMN `parking_start_time` `parking_start_time` TIMESTAMP NULL DEFAULT NULL ,
CHANGE COLUMN `parking_end_time` `parking_end_time` TIMESTAMP NULL DEFAULT NULL ;


`deleted_at` timestamp NULL DEFAULT NULL,

ALTER TABLE `inventory_modules_staging`.`frontend_facilities` 
ADD COLUMN `deleted_at` TIMESTAMP NULL DEFAULT NULL AFTER `updated_at`;

permit history table

CREATE TABLE `permit_requests_renew_history` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `permit_request_id` int(10) unsigned NOT NULL,
  `user_id` int(10) unsigned NOT NULL,
  `facility_id` int(10) unsigned NOT NULL,
  `anet_transaction_id` int(10) unsigned DEFAULT NULL,
  `tracking_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `permit_rate` decimal(10,2) NOT NULL,
  `permit_rate_id` int(11) DEFAULT NULL,
  `approved_on` datetime DEFAULT NULL,
  `account_number` varchar(45) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `monthly_duration_value` varchar(45) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `no_of_days` int(10) DEFAULT '0',
  `partner_id` int(10) DEFAULT '0',
  `license_number` varchar(45) DEFAULT NULL,
  `mer_reference` varchar(45) DEFAULT NULL,
  `image_front` text,
  `image_back` text,
  `user_consent` char(1) DEFAULT NULL,
  `vehicle_id` varchar(45) DEFAULT NULL,
  `is_admin` char(1) DEFAULT '0',
  `ex_month` varchar(10) DEFAULT NULL,
  `ex_year` varchar(10) DEFAULT NULL,
  `payment_gateway` varchar(50) DEFAULT NULL,
  `permit_type` int(11) unsigned DEFAULT NULL,
  `is_payment_authrize` int(11) unsigned DEFAULT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `permit_type_name` varchar(50) DEFAULT NULL,
  `skidata_id` varchar(255) DEFAULT NULL,
  `skidata_value` varchar(255) DEFAULT NULL,
  `acknowledge` enum('0','1') DEFAULT '0',
  `facility_zone_id` int(10) DEFAULT NULL,
  `desired_start_date` date DEFAULT NULL,
  `desired_end_date` date DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


ALTER TABLE `facility_configurations` 
ADD COLUMN `is_prorate_apply` ENUM('0', '1') NULL DEFAULT '0';


CREATE TABLE `warning_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `image_name` varchar(45) DEFAULT NULL,
  `warning_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


# Vijay : 05-02-2024 :  Pave PIMS--6586

ALTER TABLE `inventory_modules_staging`.`facility_configurations` 
ADD COLUMN `is_min_max_stay_same_enable` TINYINT(1) NULL DEFAULT 0 COMMENT 'Rate table Min and Max Stay can same value by enable this flag\\\\nand this flag also handel with daily max case.\\n0 => defailt \\n1 => enable ' AFTER `is_barcode_enabled`;

#ALKA
ALTER TABLE `service_permissions` 
ADD COLUMN `actions` varchar(256) default NULL AFTER parent_id;

ALTER TABLE `service_permissions` 
ADD COLUMN `deleted_at` timestamp NULL DEFAULT NULL AFTER actions;
###End

// This sql is used  to show the rate on flag base free or paid
ALTER TABLE `inventory_modules_staging`.`permit_rates` 
ADD COLUMN `is_promotion` INT(1) NULL DEFAULT 0 AFTER `updated_at`;

ALTER TABLE `inventory_modules_staging`.`permit_rate_descriptions` 
ADD COLUMN `is_promotion` INT(1) NULL DEFAULT 0 AFTER `updated_at`;


### Alka :: 12-02-2023 add columns and insert values for mapco reservation sms notifications(reminder)

ALTER TABLE `inventory_modules_staging`.`platform_notifications`
ADD COLUMN `type` varchar(256) NULL DEFAULT NULL AFTER `deleted_at`,
ADD COLUMN `url` varchar(256) NULL DEFAULT NULL AFTER `type`;

ALTER TABLE `inventory_modules_staging`.`partner_notifications`
ADD COLUMN `scheduled_flag` ENUM('0', '1') NOT NULL DEFAULT '0' COMMENT '0 before_time, 1 after_time' AFTER `copy_right`,
ADD COLUMN `time_in_minutes` varchar(256) NULL DEFAULT NULL AFTER `scheduled_flag`;


INSERT INTO `platform_notifications` (`id`,`template_name`, `slug_name`,`body`, `status`, `type`,`url`) VALUES ('10','24 hours reminder', '24-hours-reminder','You have an upcoming reservation in the next 24 hours. You can review or update it by visiting the following link -', '1', 'reservation','https://staging-breeze.parkengage.com/worldport-la');

INSERT INTO `platform_notifications` (`id`,`template_name`, `slug_name`,`body`, `status`, `type`,`url`) VALUES ('11','check-in reminder', 'check-in-reminder', 'Your Reservation has already started. The reservation cannot be cancelled at this time but you can still change the reservation end date and the return flight information by visiting the following link -', '1', 'reservation','https://staging-breeze.parkengage.com/worldport-la');

INSERT INTO `platform_notifications` (`id`,`template_name`, `slug_name`,`body`, `status`, `type`,`url`) VALUES ('12','check-out reminder', 'check-out-reminder', 'The scheduled end time of your reservation has already passed. The reservation cannot be cancelled at this time but you can still change the reservation end date by visiting the following link -','1', 'reservation','https://staging-breeze.parkengage.com/worldport-la');


INSERT INTO `partner_notifications` (`id`,`partner_id`, `platform_notification_id`,`notification_type_id`, `status`, `scheduled_flag`,`time_in_minutes`) VALUES ('2','357719', '10', '2','1', '0','1440');

INSERT INTO `partner_notifications` (`id`,`partner_id`, `platform_notification_id`,`notification_type_id`, `status`, `scheduled_flag`,`time_in_minutes`) VALUES ('3','357719', '11', '2','1', '1','5');

INSERT INTO `partner_notifications` (`id`,`partner_id`, `platform_notification_id`,`notification_type_id`, `status`, `scheduled_flag`,`time_in_minutes`) VALUES ('4','357719', '12', '2','1', '1','5');

####END

#Date: 12/02/2024, Author: Lokesh Chaudhary
ALTER TABLE `facilities` ADD `reservation_grace_period_minute` TINYINT NOT NULL AFTER `grace_period_minute`;
CREATE TABLE reservations_history LIKE reservations;
ALTER TABLE `reservations_history` ADD `res_id` INT NOT NULL COMMENT 'Reservation Id' AFTER `facility_id`, ADD `reservation_mode` ENUM('0','1') NOT NULL COMMENT '0=>Edit, 1=>Extend' AFTER `res_id`;
ALTER TABLE `reservations_history` CHANGE `reservation_mode` `reservation_mode` ENUM('0','1','2') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '0=>Original, 1=>Edit, 2=>Extend';

CREATE TABLE `flight_details`(
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `reservation_id` INT NOT NULL,
    `airline_name` VARCHAR(255) NOT NULL,
    `flight_no` VARCHAR(255) NOT NULL,
    `arrival_time` DATETIME NOT NULL,
    `departure_time` DATETIME NOT NULL,
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL,
    `deleted_at` TIMESTAMP NULL DEFAULT NULL,
);

ALTER TABLE `inventory_modules_staging`.`flight_details` 
ADD CONSTRAINT `reservation_id`
  FOREIGN KEY (`reservation_id`)
  REFERENCES `inventory_modules_staging`.`reservations` (`id`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;

  ALTER TABLE `reservations` ADD `is_acknowledge` TINYINT NOT NULL DEFAULT '0' AFTER `ticketech_guid`;
  ALTER TABLE `reservations_history` ADD `is_acknowledge` TINYINT NOT NULL DEFAULT '0' AFTER `ticketech_guid`;


### ALKA :: 15-Feb-2024 Create Notification log table
CREATE TABLE `notification_logs`(
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `reservation_id` INT NOT NULL,
    `platform_notification_id` INT NOT NULL,
    `status` ENUM('0', '1') NOT NULL DEFAULT '0' COMMENT '0 msg not send, 1 msg send',
    `error` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL
);

  ALTER TABLE notification_logs
  ADD COLUMN start_timestamp DATETIME NULL DEFAULT NULL AFTER `status`;
#####END

#Date: 15/02/2024, Author: Lokesh Chaudhary
  ALTER TABLE `reservations` ADD `estimated_checkout_time` DATETIME NULL DEFAULT NULL AFTER `length`;
  ALTER TABLE `reservations_history` ADD `estimated_checkout_time` DATETIME NULL DEFAULT NULL AFTER `length`;



ALTER TABLE `user_permit_requests` ADD  `user_consent` char(1) DEFAULT NULL;

#Date: 16/02/2024, Author: Lokesh Chaudhary
  ALTER TABLE `facility_configurations` ADD `before_cancel_time` INT NULL DEFAULT '60' COMMENT 'minutes accepted' AFTER `device_gate_api_url`;

##ALKA:: 20-Feb-2024 Create table for facility tax
  CREATE TABLE `facility_taxes`(
      `id` INT AUTO_INCREMENT PRIMARY KEY,
      `facility_id` INT NOT NULL,
      `display_name` VARCHAR(255) NULL DEFAULT NULL,
      `service_id` TINYINT DEFAULT '0',
      `value` DOUBLE(10,2) NULL DEFAULT NULL,
      `days` VARCHAR(255) NULL DEFAULT NULL,
      `start_time` TIME NULL DEFAULT NULL,
      `end_time` TIME NULL DEFAULT NULL,
      `active` ENUM('0', '1') NOT NULL DEFAULT '0' COMMENT '0 Inactive, 1 Active',
      `deleted_at` timestamp NULL DEFAULT NULL,
      `created_at` TIMESTAMP NULL DEFAULT NULL,
      `updated_at` TIMESTAMP NULL DEFAULT NULL
  );
  
###End

##ALKA:: 20-Feb-2024 Create table for facility multiple services
CREATE TABLE `modules`(
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `module_name` VARCHAR(255) NULL DEFAULT NULL,
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL
);
Run seeder command for dummy data:: php artisan db:seed --class=ModuleSeeder

CREATE TABLE `service_modules`(
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `module_id` INT DEFAULT '0',
    `facility_id` INT DEFAULT '0',
    `user_id` INT DEFAULT '0',
    `membership_plan_id` INT DEFAULT '0',
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL
);
###End

#Date: 21/02/2024, Author: Lokesh Chaudhary
ALTER TABLE `reservations_history` CHANGE `reservation_mode` `reservation_mode` ENUM('0','1','2') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '0=>Original, 1=>Edit, 2=>Extend';



#Vijay 
ALTER TABLE `inventory_modules_staging`.`facility_configurations` 
ADD COLUMN `zero_amount_event_booking_enable` TINYINT(1) NULL DEFAULT 0 COMMENT '0 => Default \n1 => enable' AFTER `is_min_max_stay_same_enable`;

ALTER TABLE `inventory_modules_staging`.`facilities` 
ADD COLUMN `is_service_update` TINYINT(1) NULL DEFAULT 0 COMMENT '0 => default no change 1=> service update enable \n' AFTER `cloud_check_vehicle_enabled`;


ALTER TABLE facility_payment_details
ADD COLUMN ios_mid varchar(255) DEFAULT NULL;

ALTER TABLE facility_payment_details
ADD COLUMN ios_mid varchar(255) DEFAULT NULL;


##ALKA:: 20-Feb-2024 Create table for facility multiple services

  ALTER TABLE facility_taxes
  ADD COLUMN type ENUM('0', '1') NOT NULL DEFAULT '0' COMMENT '0 => percentage 1=> Amount' AFTER end_time;

  ALTER TABLE facility_taxes
  ADD COLUMN is_process_fee ENUM('0', '1') NOT NULL DEFAULT '0' AFTER type ;
###End

 ALTER TABLE facility_payment_details
  ADD COLUMN heartland_service_url varchar(255) DEFAULT NULL,
  ADD COLUMN heartland_secret_api_key varchar(255) DEFAULT NULL,
  ADD COLUMN heartland_developer_id varchar(255) DEFAULT NULL,
  ADD COLUMN heartland_version_number varchar(255) DEFAULT NULL,
  ADD COLUMN heartland_payment_env varchar(255) DEFAULT NULL;
#santosh
alter table  reservations add column payment_comment varchar(255) default null after event_user_id;

#Date: 27/02/2024, Author: Lokesh Chaudhary
ALTER TABLE `facilities` ADD `reservation_start_time` INT NOT NULL DEFAULT '60' COMMENT 'Before Reservation Time in minutes' AFTER `garage_code`;
ALTER TABLE `facilities` ADD `country_code` VARCHAR(10) NOT NULL DEFAULT 'US' AFTER `reservation_start_time`;
ALTER TABLE `facility_configurations` ADD `reservation_start_time` INT NOT NULL DEFAULT '60' COMMENT 'Before Reservation Time in Minutes' AFTER `device_gate_api_url`;

#Date: 28/02/2024, Author: Lokesh Chaudhary
CREATE TABLE `terms_and_conditions`(
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `facility_id` INT(10) UNSIGNED NOT NULL,
    `content` LONGTEXT,
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (`facility_id`) REFERENCES `facilities`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
);

ALTER TABLE `facility_configurations` ADD `terms_condition_content` LONGTEXT NULL DEFAULT NULL AFTER `before_cancel_time`;
ALTER TABLE `brand_settings` ADD `terms_condition_content` LONGTEXT NULL DEFAULT NULL AFTER `reservation_heading`;

#Date: 29/02/2024, Author: Lokesh Chaudhary
ALTER TABLE `users` ADD `user_prefrences` ENUM('0','1','2','3') NOT NULL DEFAULT '0' COMMENT '0=>Nothing, 1=>SMS, 2=>Email, 3=>Both' AFTER `remember_token`;

#santosh used for update the warning and citation table
//WARNING
ALTER TABLE `inventory_modules_staging`.`warnings` 
ADD COLUMN `amount` DOUBLE(10,2) NULL DEFAULT NULL AFTER `attendant_name`,
ADD COLUMN `paid_date` DATETIME NULL DEFAULT NULL AFTER `amount_comment`;
ADD COLUMN `is_avoid` INT NULL DEFAULT NULL AFTER `avoid_comment`;



//CITATION
ALTER TABLE `inventory_modules_staging`.`ticket_citations` 
ADD COLUMN `amount` DOUBLE(10,2) NULL DEFAULT NULL AFTER `reservation_id`,
ADD COLUMN `amount_comment` VARCHAR(255) NULL DEFAULT NULL AFTER `amount`,
ADD COLUMN `paid_date` DATETIME NULL DEFAULT NULL AFTER `amount_comment`;

ALTER TABLE `inventory_modules_staging`.`ticket_citations` 
ADD COLUMN `avoid_comment` VARCHAR(255) NULL DEFAULT NULL AFTER `paid_date`,
ADD COLUMN `is_avoid` INT NULL DEFAULT NULL AFTER `avoid_comment`;

ALTER TABLE `facilities` ADD `terms_condition_content` LONGTEXT NULL DEFAULT NULL AFTER `country_code`;
ALTER TABLE `facility_brand_settings` ADD `terms_condition_content` LONGTEXT NULL DEFAULT NULL AFTER `info_email`;


ALTER TABLE `user_permit_vehicles` 
ADD COLUMN `partner_id` INT NULL DEFAULT NULL;

##Alka
ALTER TABLE `notification_logs` 
ADD COLUMN `deleted_at` timestamp NULL DEFAULT NULL;



ALTER TABLE `reservations` 
ADD COLUMN `payment_token` text NULL DEFAULT NULL;

ALTER TABLE `facility_payment_details` 
ADD COLUMN `heartland_public_api_key` VARCHAR(255) NULL DEFAULT NULL AFTER heartland_version_number,
ADD COLUMN `heartland_single_token_slug` VARCHAR(255) NULL DEFAULT NULL AFTER heartland_public_api_key;

ALTER TABLE `facility_payment_details` 
ADD COLUMN `heartland_mid` VARCHAR(255) NULL DEFAULT NULL AFTER dataTrans_error_url;



state_name

state_id

ALTER TABLE `user_permit_vehicles` 
ADD COLUMN `state_id` int(11) NULL DEFAULT NULL,
ADD COLUMN `state_name` VARCHAR(45) NULL DEFAULT NULL;


CREATE TABLE `heartland_payment_profile` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `partner_id` int(11) DEFAULT NULL,
  `card_last_four` varchar(16) DEFAULT NULL,
  `card_type` varchar(50) DEFAULT NULL,
  `card_name` varchar(50) DEFAULT NULL,
  `expiry` varchar(10) DEFAULT NULL,
  `token` text,
  `card_holder_id` text,
  `result_reason` varchar(255) DEFAULT NULL,
  `currency_used` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `RefNo` varchar(100) DEFAULT NULL,
  `InvoiceNo` varchar(100) DEFAULT NULL,
  `AuthCode` varchar(100) DEFAULT NULL,
  `is_default` enum('0','1') DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;



CREATE TABLE `heartland_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `ticket_id` int(11) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `partner_id` int(11) DEFAULT NULL,
  `card_last_four` varchar(16) DEFAULT NULL,
  `card_type` varchar(50) DEFAULT NULL,
  `card_name` varchar(50) DEFAULT NULL,
  `expiry` varchar(10) DEFAULT NULL,
  `token` text,
  `tx_state_text` varchar(255) DEFAULT NULL,
  `tx_state` varchar(100) DEFAULT NULL,
  `result_reason` varchar(255) DEFAULT NULL,
  `currency_used` varchar(50) DEFAULT NULL,
  `invoice_no` varchar(50) DEFAULT NULL,
  `ref_id` varchar(255) DEFAULT NULL,
  `session_id` varchar(50) DEFAULT NULL,
  `is_payment_complete` enum('0','1') DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `response_message` varchar(255) DEFAULT NULL,
  `reader_used` varchar(45) DEFAULT NULL,
  `trans_id` varchar(255) DEFAULT NULL,
  `total` decimal(10,2) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `transaction_retry` enum('0','1','2','3') DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

###ALka ::Date::07-03-2024 garage and zone management

CREATE TABLE `garages`(
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `garage_name` varchar(255) DEFAULT NULL,
    `garage_code` VARCHAR(255) NULL DEFAULT NULL,
    `type` ENUM('0', '1') NOT NULL DEFAULT '0' COMMENT '0 garage, 1 lot',
    `partner_id` INT(11) DEFAULT NULL,
    `owner_id` INT(11) DEFAULT NULL,
    `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
    `facility_type` VARCHAR(255) NULL DEFAULT NULL,
    `open_gated_enabled` TINYINT(1) NULL DEFAULT '0',
    `check_vehicle_enabled` TINYINT(1) NULL DEFAULT '0',
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL
);

ALTER TABLE `facilities` 
ADD COLUMN `garage_id` INT(10) UNSIGNED NOT NULL,
FOREIGN KEY (`garage_id`) REFERENCES `garages`(`id`) ON DELETE NO ACTION ON UPDATE NO ACTION

###END
#santosh cash payment 
ALTER TABLE `inventory_modules_staging`.`reservations` 
ADD COLUMN `is_cash_payment` ENUM('0', '1') NULL DEFAULT '0' AFTER `payment_token`;


# is_pemit_qrcode

ALTER TABLE `facility_configurations` 
ADD COLUMN `is_pemit_qrcode` ENUM('0', '1') NULL DEFAULT '0';

# rm_id
ALTER TABLE `promotions` 
ADD COLUMN `rm_id` INT(11) NULL DEFAULT NULL;


ALTER TABLE `promo_usages` 
ADD COLUMN `rm_id` INT(11) NULL DEFAULT NULL;



## Vijay : Pave Overnight 
ALTER TABLE `inventory_modules_staging`.`category_facility_overnight_durations` 
ADD COLUMN `overnight_booking_time_till` TIME NULL DEFAULT NULL AFTER `overnight_end_time`;

ALTER TABLE `facility_configurations` 
ADD COLUMN `is_reservation_edit` ENUM('0', '1') NULL DEFAULT '0';

ALTER TABLE `facility_configurations` 
ADD COLUMN `is_card_controller_connected` ENUM('0', '1') NULL DEFAULT '0';


#ALKA:: 29-march-2024 add data for woodman notification

INSERT INTO `platform_notifications` (`template_name`, `slug_name`,`body`, `status`, `type`,`url`) VALUES ('extend time reminder', 'extend-time-reminder','Lorem epsum content by visiting the following link -', '1', 'lpr','https://staging-breeze.parkengage.com/worldport-la');

INSERT INTO `partner_notifications` (`partner_id`, `platform_notification_id`,`notification_type_id`, `status`, `scheduled_flag`,`time_in_minutes`) VALUES ('358642', '13','2', '1', '0','15');

##End
ALTER TABLE `facility_configurations` 
ADD COLUMN `n-before-checkin` INT NULL DEFAULT '0' COMMENT 'minutes accepted';

##End

CREATE TABLE `suite_master`(
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` varchar(255) DEFAULT NULL,
    `created_at` TIMESTAMP NULL DEFAULT NULL,
    `updated_at` TIMESTAMP NULL DEFAULT NULL
);


suite_id

ALTER TABLE `permit_requests` 
ADD COLUMN `suite_id` INT(11) DEFAULT NULL;


#Date: 12/02/2024, Author: Lokesh Chaudhary
ALTER TABLE `facility_configurations` ADD `max_vehicle` INT NOT NULL DEFAULT '5' COMMENT 'Vehicle count for registered user' AFTER `terms_condition_content`;

##Date: 04-April-2024, Author: Alka
CREATE TABLE `authorized_drivers` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) DEFAULT NULL,
  `facility_id` int(11) DEFAULT NULL,
  `permit_request_id` int(11) DEFAULT NULL,
  `user_permit_request_id` int(11) DEFAULT NULL,
  `driver_name` varchar(255) DEFAULT NULL,
  `signature` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

##Alka 
ALTER TABLE `user_permit_vehicles` 
ADD COLUMN `vehicle_number` varchar(255) DEFAULT NULL;

ALTER TABLE `permit_vehicles` 
ADD COLUMN `vehicle_number` varchar(255) DEFAULT NULL;
##end

ALTER TABLE reservations
ADD COLUMN event_id int(11) unsigned DEFAULT NULL;

ALTER TABLE events
ADD COLUMN event_cash_rate decimal(10,2) DEFAULT '0.00' AFTER event_rate;

#ALKA
ALTER TABLE `facility_configurations` 
ADD COLUMN `is_manual_permit_amount_enabled` ENUM('0', '1') NULL DEFAULT '0';

##END

ALTER TABLE `facility_configurations` 
ADD COLUMN `is_sbm_qrcode` ENUM('0', '1') NULL DEFAULT '0';


ALTER TABLE `reservations` 
ADD COLUMN `device_type` varchar(45) NULL DEFAULT NULL;


#Date: 10/04/2024, Author: Lokesh Chaudhary
ALTER TABLE whitelist_users
ADD COLUMN event_id int 11 DEFAULT '0' AFTER partner_id;

ALTER TABLE whitelist_users
ADD COLUMN name VARCHAR(255) DEFAULT NULL AFTER id;


#Date: 11/04/2024, Author: Lokesh Chaudhary
ALTER TABLE whitelist_users
MODIFY event_id varchar(255);

CREATE TABLE whitelist_user_events (
    id INT NOT NULL AUTO_INCREMENT,
    whitelist_users_id INT NOT NULL,
    facility_id INT(10) UNSIGNED NOT NULL,
    event_id INT(10) UNSIGNED NOT NULL,
    event_name VARCHAR(255) NULL,
    PRIMARY KEY (id),
    FOREIGN KEY (whitelist_users_id) REFERENCES whitelist_users(id),
    FOREIGN KEY (facility_id) REFERENCES facilities(id),
    FOREIGN KEY (event_id) REFERENCES events(id)
);

ALTER TABLE whitelist_user_events
ADD created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE whitelist_user_events
ADD deleted_at TIMESTAMP NULL DEFAULT NULL;
ALTER TABLE whitelist_user_events
ADD partner_id int(11) DEFAULT NULL AFTER id;
ALTER TABLE `brand_settings` 
ADD COLUMN `customer_banner` TEXT NULL DEFAULT NULL,
ADD COLUMN `customer_banner_heading` TEXT NULL DEFAULT NULL,
ADD COLUMN `customer_banner_text` TEXT NULL DEFAULT NULL;

ALTER TABLE `facility_brand_settings` 
ADD COLUMN `customer_banner` TEXT NULL DEFAULT NULL,
ADD COLUMN `customer_banner_heading` TEXT NULL DEFAULT NULL,
ADD COLUMN `customer_banner_text` TEXT NULL DEFAULT NULL;

#Date: 19/04/2024, Author: Lokesh Chaudhary
ALTER TABLE facility_configurations
ADD COLUMN base_url VARCHAR(255) DEFAULT NULL AFTER partner_id;

#Date: 22/04/2024, Author: Lokesh Chaudhary
ALTER TABLE reservations
ADD COLUMN refund_amount double(10,2) DEFAULT '0' AFTER refund_status;

ALTER TABLE reservations_history
ADD COLUMN refund_amount double(10,2) DEFAULT '0' AFTER refund_status;

# The below key added for yenkee permit Data : 24-04-24
ALTER TABLE permit_requests
ADD COLUMN hid_card_number varchar(50) NULL DEFAULT NULL,
ADD COLUMN account_name varchar(50) NULL DEFAULT NULL;

# account name 
CREATE TABLE `account_name_master` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `facility_id` int(11) DEFAULT NULL,
  `partner_id` int(11) DEFAULT NULL,
  `rm_id` int(11) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `remarks` text COLLATE utf8_unicode_ci,
  `is_active` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


#Date: 26/04/2024, Author: Lokesh Chaudhary
ALTER TABLE reservations
ADD COLUMN utc_start_timestamp TIMESTAMP NULL DEFAULT NULL AFTER start_timestamp;

ALTER TABLE reservations
ADD COLUMN thirdparty_code VARCHAR(100) DEFAULT NULL AFTER ticketech_code;

ALTER TABLE reservations_history
ADD COLUMN utc_start_timestamp TIMESTAMP NULL DEFAULT NULL AFTER start_timestamp;

ALTER TABLE reservations_history
ADD COLUMN thirdparty_code VARCHAR(100) DEFAULT NULL AFTER ticketech_code;

ALTER TABLE reservations
ADD COLUMN utc_end_timestamp TIMESTAMP NULL DEFAULT NULL AFTER end_timestamp;

ALTER TABLE reservations_history
ADD COLUMN utc_end_timestamp TIMESTAMP NULL DEFAULT NULL AFTER end_timestamp;


ALTER TABLE reservations
ADD COLUMN hub_unique_id VARCHAR(100) DEFAULT NULL AFTER ticketech_code;

ALTER TABLE reservations_history
ADD COLUMN hub_unique_id VARCHAR(100) DEFAULT NULL AFTER ticketech_code;

#Date: 29/04/2024, Author: Lokesh Chaudhary

ALTER TABLE facility_configurations
ADD COLUMN is_send_url TINYINT(1) DEFAULT 0 AFTER base_url;  - done

#Date: 29/04/2024, Author: Dushyant Dogra 
'CREATE TABLE `permit_rate_criterias` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `permit_rate_id` int(11) DEFAULT NULL,
  `label` varchar(200) DEFAULT NULL,
  `value` varchar(10) NOT NULL,
  `entry_time_begin` time DEFAULT NULL,
  `entry_time_end` time DEFAULT NULL,
  `exit_time_begin` time DEFAULT NULL,
  `exit_time_end` time DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `permit_rate_id` (`permit_rate_id`),
  KEY `value` (`value`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=latin1'

ALTER TABLE tickets
ADD COLUMN refund_amount int(11) unsigned DEFAULT NULL,
ADD COLUMN refund_type ENUM('1','2') NULL DEFAULT NULL COMMENT "1 - full refund, 2 - partial refund",
ADD COLUMN refund_remarks  VARCHAR(255) NULL,
ADD COLUMN refund_date datetime DEFAULT NULL,
ADD COLUMN refund_by int(11) unsigned DEFAULT NULL,
ADD COLUMN refund_transaction_id VARCHAR(255) NULL,
ADD COLUMN refund_status VARCHAR(255) NULL;

ALTER TABLE tickets
ADD COLUMN refund_transaction_id VARCHAR(255) NULL;

#Date: 07/05/2024, Author: Dushyant Dogra 
ALTER TABLE gates
ADD COLUMN gateid INT(11),
ADD INDEX idx_gateid (gateid);

ALTER TABLE adams
ADD COLUMN adamid INT(11),
ADD INDEX idx_adamid (adamid);


ALTER TABLE tickets
ADD COLUMN refund_status VARCHAR(255) NULL;



#Date: 07/05/2024, Author: Dushyant Dogra 
ALTER TABLE `permit_rate_criterias`  ADD partner_id int(11);
ALTER TABLE `permit_rate_criterias`  ADD  index(partner_id)
ALTER TABLE `permit_rate_criterias` CHANGE `value` `days` varchar(20) not null;

CREATE TABLE `permit_rate_criteria_mapping` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `facility_id` int(11) DEFAULT NULL,
  `permit_rate_id` int(11) COLLATE utf8_unicode_ci DEFAULT NULL,
  `permit_rate_criteria_id` int(11) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
   KEY `permit_rate_criteria_id` (`permit_rate_criteria_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci

#Date: 09/05/2024, Author: Dushyant Dogra 
CREATE TABLE `permit_services` (
   `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
   `partner_id` int(11) DEFAULT NULL,
   `permit_service_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
   `permit_service_desc` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
   `open_always` int(1) DEFAULT NULL,
   `permit_service_rate` decimal(10,2) NOT NULL DEFAULT '0.00',
   `is_status` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '1',
   `created_at` timestamp NULL DEFAULT NULL,
   `updated_at` timestamp NULL DEFAULT NULL,
   PRIMARY KEY (`id`)
 ) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci

CREATE TABLE `permit_service_type_master_mapping` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `permit_service_id` int(11) DEFAULT NULL,
  `permit_type_master_id` int(11) DEFAULT NULL,
   `created_at` timestamp NULL DEFAULT NULL,
   `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci

insert into permit_services(partner_id,permit_service_name,permit_service_desc,open_always,is_status,created_at,updated_at) values('1111','CARPOOL','',1,'1',now(),now())
insert into permit_services(partner_id,permit_service_name,permit_service_desc,open_always,is_status,permit_service_rate,created_at,updated_at) values('1111','RESERVED Business Hours','Space reserved 7am-3pm',0,'1',125,now(),now())
insert into permit_services(partner_id,permit_service_name,permit_service_desc,open_always,is_status,permit_service_rate,created_at,updated_at) values('1111','RESERVED 24x7','Space Reserved 24/7',1,'1',350,now(),now())
insert into permit_service_type_master_mapping(permit_service_id,permit_type_master_id) values(1,6),(2,6),(3,6),(1,5),(2,5),(3,5);
update permit_service_type_master_mapping set created_at=now(),updated_at=now() where id <15;

#Date: 09/05/2024, Author: Dushyant Dogra
INSERT INTO `service_permissions`(`display_name`,`created_at`,`updated_at`,`is_default`,`list_order`) VALUES('Business Account',now(),now(),1,21);


#Date: 14/05/2024, Author: Dushyant Dogra
ALTER TABLE permit_requests ADD permit_final_amount decimal(10,2) DEFAULT '0.00';
ALTER TABLE user_permit_requests ADD permit_final_amount decimal(10,2) DEFAULT '0.00';


CREATE TABLE `permit_request_service_mapping` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) DEFAULT NULL,
  `facility_id` int(11) DEFAULT NULL,
  `permit_request_id` int(11) DEFAULT NULL,
  `user_permit_request_id` int(11) DEFAULT NULL,
  `permit_service_id` int(11) DEFAULT NULL,
  `permit_service_rate` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_facility_id` (`facility_id`),
  KEY `idx_user_permit_request_id` (`user_permit_request_id`),
  KEY `idx_permit_service_id` (`permit_service_id`)
) 

#Date: 15/05/2024, Author: Lokesh Chaudhary 
ALTER TABLE `reservations` ADD `is_hub_zeag` ENUM('0','1','2','3') NOT NULL COMMENT '0 => All, 1 => Belongs to ROC/HUb 2 => Created on Hub, 3 => Deleted on Hub' AFTER `utc_start_timestamp`;

ALTER TABLE `reservations_history` ADD `is_hub_zeag` ENUM('0','1','2','3') NOT NULL COMMENT '0 => All, 1 => Belongs to ROC/HUb 2 => Created on Hub, 3 => Deleted on Hub' AFTER `utc_start_timestamp`;

ALTER TABLE `facilities` CHANGE `reservation_grace_period_minute` `reservation_grace_period_minute` INT NOT NULL;

#Date: 15/05/2024, Author: Dushyant Dogra
CREATE TABLE `users_permit_type_mapping` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `account_name_id` int(10) unsigned NOT NULL,
  `permit_type_id` int(10) unsigned NOT NULL,
  `capacity` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_permit_type_id` (`permit_type_id`)
) 

#ALKA :: 16-May-2024
ALTER TABLE permit_requests
ADD COLUMN user_remark varchar(100) DEFAULT NULL;

ALTER TABLE user_permit_requests
ADD COLUMN user_remark varchar(100) DEFAULT NULL;

#END

#By Alka 
ALTER TABLE facility_configurations
ADD COLUMN after_expire_days int(11) DEFAULT 0;

#Date: 17/05/2024, Author: Dushyant Dogra
ALTER TABLE permit_request_service_mapping ADD permit_type_master_id int(10) null;
ALTER TABLE permit_request_service_mapping ADD INDEX(permit_request_id);


ALTER TABLE permit_request_service_mapping ADD INDEX(permit_request_id);


ALTER TABLE `facility_configurations` 
ADD COLUMN `account_name_enabled` ENUM('0', '1') NULL DEFAULT '0';
#Date: 23-05=2024, Author: Dushyant
ALTER TABLE users_permit_type_mapping ADD COLUMN email_domain_validation BOOLEAN DEFAULT FALSE;
ALTER TABLE users_permit_type_mapping ADD COLUMN email_domain VARCHAR(300) DEFAULT null;

// need to deploy on prod
ALTER TABLE users_permit_type_mapping ADD COLUMN  `remaining_capacity` int(11) DEFAULT NULL,
ALTER TABLE `permit_rates` 
CHANGE COLUMN `total_usage` `total_usage` INT(11) NULL DEFAULT NULL ,
CHANGE COLUMN `remaining_usage` `remaining_usage` INT(11) NULL DEFAULT NULL ;


ALTER TABLE user_permit_requests
ADD COLUMN user_type_id VARCHAR(50) NULL DEFAULT NULL;

ALTER TABLE permit_requests
ADD COLUMN user_type_id VARCHAR(50) NULL DEFAULT NULL;

#Date: 28-05-2024, Author: Dushyant Dogra
ALTER TABLE permit_services ADD is_variable_pay int(1) DEFAULT 0;
UPDATE permit_services SET is_variable_pay=1 WHERE id=4;
UPDATE permit_services SET permit_service_rate=0.00 WHERE id=4;

ALTER TABLE permit_requests ADD is_antipass_enabled int(1) DEFAULT 0;

ALTER TABLE user_permit_requests ADD is_negotiable int(1) DEFAULT NULL;
ALTER TABLE permit_services CHANGE is_variable_pay is_negotiable int(1) DEFAULT 0;
ALTER TABLE permit_services CHANGE negotiate is_purchase int(1) DEFAULT 1;

UPDATE `inventory_modules_staging`.`permit_services` SET `is_purchase` = '1' WHERE (`id` = '1');
UPDATE `inventory_modules_staging`.`permit_services` SET `is_purchase` = '0' WHERE (`id` = '2');
UPDATE `inventory_modules_staging`.`permit_services` SET `is_purchase` = '0' WHERE (`id` = '3');
UPDATE `inventory_modules_staging`.`permit_services` SET `is_purchase` = '0' WHERE (`id` = '4');

ALTER TABLE permit_services ADD facility_id int(10) default null;
update permit_services set facility_id=407;




ALTER TABLE `facility_configurations` 
ADD COLUMN `is_negotiated_price` ENUM('0', '1') NULL DEFAULT '0';
#Date: 29/05/2024, Author: Dushyant Dogra
ALTER TABLE facility_configurations ADD is_want_configure_services_enabled enum('0','1') DEFAULT '0';

ALTER TABLE promotions ADD
`promotion_validation_type` enum('0','1','2') COLLATE utf8_unicode_ci DEFAULT '0' COMMENT '0 for email, 1 for phone, 2 for license plate' AFTER hours_off_discount;

#Date: 4 june 2024, Author: Alka
ALTER TABLE rates
ADD COLUMN day_type TINYINT(1) NULL DEFAULT NULL COMMENT '0 => Single day, 1=> multiday'  AFTER free_time_end;

ALTER TABLE `facility_configurations` 
ADD COLUMN `is_auto_print` ENUM('0', '1') NULL DEFAULT '0';

#Date: 06/06/2024, Author: Dushyant Dogra
alter table users_permit_type_mapping MODIFY account_name_id int(10) null;
alter table users_permit_type_mapping MODIFY permit_type_id int(10) null;


#Author: Alka, Date: 07 June 2024 
ALTER TABLE facility_configurations
ADD COLUMN permit_reminder_days int(11) DEFAULT 0;


CREATE TABLE `ticket_details` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_id` int(10) unsigned NOT NULL,
  `qr_code` Text Default NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` DATETIME NULL,
  PRIMARY KEY (`id`)
)

#Author: Alka, Date: 10 June 2024
ALTER TABLE inventory_modules_staging.facility_configurations
ADD COLUMN permit_renew_notrenew_d int(11) DEFAULT 0;

ALTER TABLE inventory_modules_staging.facility_configurations
ADD COLUMN permit_partner_reminder_d int(11) DEFAULT 0;

ALTER TABLE permit_requests
ADD COLUMN admin_user_id int(11) unsigned DEFAULT NULL;

#Author: Dushyant, Date: 13 June 2024

CREATE TABLE `lpr_license_plates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `license_plate` varchar(255) DEFAULT NULL,
  `image` text,
  `partner_id` int(10) DEFAULT NULL,
  `facility_id` int(10) DEFAULT NULL,
  `gate` varchar(45) DEFAULT NULL,
  `make` varchar(255) DEFAULT NULL,
  `model` varchar(255) DEFAULT NULL,
  `year` varchar(45) DEFAULT NULL,
  `color` varchar(45) DEFAULT NULL,
  `plate_state` varchar(45) DEFAULT NULL,
  `plate_type` varchar(45) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `gate_type` varchar(45) DEFAULT NULL,
  `entry_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) 

ALTER TABLE tickets ADD lpr_session_id text null;

ALTER TABLE permit_services_facility_mapping ADD
`is_status` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '1',
ADD `is_purchase` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '1',
ADD `is_negotiable` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '0';

#Date: 17/06/2024, Author: Lokesh Chaudhary
ALTER TABLE `promotion_users`
ADD `license_plate` varchar(255) COLLATE 'latin1_swedish_ci' NULL AFTER `email`,
ADD `phone` bigint NULL AFTER `license_plate`;

CREATE TABLE `promocode_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `partner_id` int(10) DEFAULT NULL,
  `facility_id` int(10) DEFAULT NULL,
  `promotion_id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `state` varchar(255) DEFAULT NULL,
  `zipcode` varchar(45) DEFAULT NULL,
  `phone` bigint(20) DEFAULT NULL,
  `license_plate` varchar(45) DEFAULT NULL,
  `card_last_four` varchar(45) DEFAULT NULL,
  `cvv` varchar(45) DEFAULT NULL,
  `expiry` varchar(45) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (promotion_id) REFERENCES promotions(id)
)

ALTER TABLE `promocode_requests`
ADD `promocode` varchar(255) COLLATE 'latin1_swedish_ci' NULL AFTER `promotion_id`;

ALTER TABLE `promocode_requests`
ADD `term_condition_heading` varchar(255) COLLATE 'latin1_swedish_ci' NULL;

ALTER TABLE `mobile_device_version`
ADD `android_soft_status` enum('0','1') COLLATE 'utf8_unicode_ci' NULL DEFAULT '0' AFTER `android_status`,
ADD `ios_soft_status` enum('0','1') COLLATE 'utf8_unicode_ci' NULL DEFAULT '0' AFTER `ios_status`,
ADD `app_name` varchar(255) COLLATE 'utf8_unicode_ci' NULL AFTER `title`,
ADD `android_app_download_url` text COLLATE 'utf8_unicode_ci' NULL AFTER `app_name`,
ADD `ios_app_download_url` text COLLATE 'utf8_unicode_ci' NULL AFTER `android_app_download_url`;

//END


#Date: 18/06/2024, Author: Shalu Singh
ALTER TABLE facility_configurations ADD
`is_booking_cancel_enable` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '0';


ALTER TABLE permit_rates 
ADD `is_hide_vehicle` enum('0','1') DEFAULT '0',
ADD `is_hide_permit_service` enum('0','1') DEFAULT '0';

is_hide_vehicle

is_hide_permit_service


ALTER TABLE permit_rates 
ADD `is_hide_staff` enum('0','1') DEFAULT '0';


ALTER TABLE `permit_type_master` 
ADD COLUMN `is_promotion` INT(1) NULL DEFAULT 0;

#Date: 18/06/2024, Author: Shalu Singh
ALTER TABLE facility_configurations ADD
`is_permit_payment_enable` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '0';
#END

ALTER TABLE `permit_rate_descriptions` 
ADD COLUMN `order` INT(1) NULL DEFAULT 1;


ALTER TABLE `permit_requests` 
ADD COLUMN `discount_amount` DOUBLE(10,2) NULL DEFAULT 0;


ALTER TABLE `user_permit_requests` 
ADD COLUMN `discount_amount` DOUBLE(10,2) NULL DEFAULT 0;


Vijay : 21-06-2024

CREATE TABLE `promo_usages_audit_trail` (
   `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
   `user_id` int(11) NOT NULL,
   `email` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
   `promocode` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
   `reservation_id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
   `discount_amount` double(8,2) NOT NULL DEFAULT '0.00',
   `add_remove_flag` tinyint(1) DEFAULT NULL,
   `created_at` timestamp NULL DEFAULT NULL,
   `updated_at` timestamp NULL DEFAULT NULL,
   `ticket_id` int(11) DEFAULT NULL,
   PRIMARY KEY (`id`)
 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci



ALTER TABLE permit_type_master 
ADD `is_hide_staff` enum('0','1') DEFAULT '0';

ALTER TABLE permit_type_master 
ADD `is_hide_vehicle` enum('0','1') DEFAULT '0',
ADD `is_hide_permit_service` enum('0','1') DEFAULT '0';


ALTER TABLE permit_requests 
ADD `promocode` VARCHAR(255) DEFAULT NULL,
ADD `negotiated_amount` DECIMAL(10,2) DEFAULT NULL;


ALTER TABLE permit_requests_renew_history 
ADD `permit_final_amount` DECIMAL(10,2) DEFAULT NULL,
ADD `user_remark` VARCHAR(100) DEFAULT NULL,
ADD `user_type_id` VARCHAR(50) DEFAULT NULL,
ADD `is_antipass_enabled` int(1) DEFAULT 0,
ADD `admin_user_id` int(11) UNSIGNED DEFAULT NULL,
ADD `discount_amount` DECIMAL(10,2) DEFAULT NULL,
ADD `promocode` VARCHAR(255) DEFAULT NULL,
ADD `negotiated_amount` DECIMAL(10,2) DEFAULT NULL;

#Author: dushyant, Date: 24/06/2024
ALTER TABLE permit_type_master ADD email_domain_validation tinyint(1) null default 0;
ALTER TABLE permit_type_master ADD email_domain varchar(300) null;





ALTER TABLE `facilities` 
ADD COLUMN `permit_processing_fee` DECIMAL(10,2) NULL DEFAULT '0.00';

ALTER TABLE `inventory_modules_staging`.`facility_configurations` 
DROP COLUMN `permit_processing_fee`;




ALTER TABLE permit_requests
ADD COLUMN processing_fee DECIMAL(10,2) NULL DEFAULT '0.00';

ALTER TABLE user_permit_requests
ADD COLUMN processing_fee DECIMAL(10,2) NULL DEFAULT '0.00';

ALTER TABLE permit_requests_renew_history
ADD COLUMN processing_fee DECIMAL(10,2) NULL DEFAULT '0.00';


ALTER TABLE permit_requests
ADD COLUMN grand_total DECIMAL(10,2) NULL DEFAULT '0.00';

ALTER TABLE user_permit_requests
ADD COLUMN grand_total DECIMAL(10,2) NULL DEFAULT '0.00';

ALTER TABLE permit_requests_renew_history
ADD COLUMN grand_total DECIMAL(10,2) NULL DEFAULT '0.00';


#dushyant 26/06/2024
CREATE TABLE `permit_service_criterias` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `label` varchar(200) DEFAULT NULL,
  `days` varchar(20) NOT NULL,
  `entry_time_begin` time DEFAULT NULL,
  `entry_time_end` time DEFAULT NULL,
  `exit_time_begin` time DEFAULT NULL,
  `exit_time_end` time DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `partner_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `value` (`days`),
  KEY `partner_id` (`partner_id`)
) ;

CREATE TABLE `permit_service_criteria_mapping` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `facility_id` int(11) DEFAULT NULL,
  `permit_service_id` int(11) DEFAULT NULL,
  `permit_service_criteria_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `permit_service_criteria_id` (`permit_service_criteria_id`)
) ;

insert into permit_service_criterias(label, `days`, entry_time_begin, entry_time_end, exit_time_begin, exit_time_end, created_at, updated_at, deleted_at, partner_id) VALUES( '24 X 7', '1,2,3,4,5,6,7', '00:00:00', '23:59:59', '00:00:00', '23:59:59', '2024-05-27 03:44:09', '2024-05-27 03:44:09', NULL, '19349');
insert into permit_service_criterias(label, `days`, entry_time_begin, entry_time_end, exit_time_begin, exit_time_end, created_at, updated_at, deleted_at, partner_id) VALUES( '7AM - 3PM', '1,2,3,4,5,6,7', '07:00:00', '15:00:00', '07:00:00', '15:00:00', '2024-05-27 03:44:09', '2024-05-27 03:44:09', NULL, '19349');

insert into permit_service_criteria_mapping(facility_id,permit_service_id,permit_service_criteria_id,created_at,updated_at)
values(407,2,2,now(),now()),(407,3,1,now(),now());

#ALKA 
ALTER TABLE user_permit_requests 
ADD `promocode` VARCHAR(255) DEFAULT NULL;

#Date: 27/06/2024, Author: Alka
ALTER TABLE `facility_configurations` ADD `is_bolo_restrict` ENUM('0', '1') NOT NULL DEFAULT '0' COMMENT '0 allow, 1 restrict';


heartland_payment_profile

pincode

ALTER TABLE heartland_payment_profile 
ADD `zipcode` VARCHAR(255) DEFAULT NULL;

ALTER TABLE permit_rate_descriptions
ADD `permit_start_date` DATE DEFAULT NULL, 
ADD `permit_end_date` DATE DEFAULT NULL;

ALTER TABLE permit_rate_descriptions
ADD `permit_frequency` int(2) DEFAULT NULL;


ALTER TABLE permit_requests
ADD COLUMN refund_amount decimal(10,2) unsigned DEFAULT NULL,
ADD COLUMN refund_type ENUM('1','2') NULL DEFAULT NULL COMMENT "1 - full refund, 2 - partial refund",
ADD COLUMN refund_remarks  VARCHAR(255) NULL,
ADD COLUMN refund_date datetime DEFAULT NULL,
ADD COLUMN refund_by int(11) unsigned DEFAULT NULL,
ADD COLUMN refund_transaction_id VARCHAR(255) NULL,
ADD COLUMN refund_status VARCHAR(255) NULL;


ALTER TABLE tickets
ADD COLUMN `cancelled_at` timestamp NULL DEFAULT NULL;

ALTER TABLE `user_permit_vehicles` 
ADD COLUMN `country` varchar(255) DEFAULT NULL;

ALTER TABLE `permit_vehicles` 
ADD COLUMN `country` varchar(255) DEFAULT NULL;



update states set id='52' where state_code='AB';
update states set id='53' where state_code='BC';
update states set id='54' where state_code='MB';
update states set id='55' where state_code='NB';
update states set id='56' where state_code='NF';
update states set id='57' where state_code='NT';
update states set id='58' where state_code='NS';
update states set id='59' where state_code='NU';
update states set id='60' where state_code='ON';
update states set id='61' where state_code='PE';
update states set id='62' where state_code='SK';
update states set id='63' where state_code='YK';
ALTER TABLE `inventory_modules_staging`.`states` 
CHANGE COLUMN `id` `id` INT(11) NOT NULL AUTO_INCREMENT ,
ADD PRIMARY KEY (`id`);
;

#Date: 12/07/2024, Author: Lokesh Chaudhary
CREATE TABLE `ticket_citation_appeals` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `citation_id` int(11) DEFAULT NULL,
  `partner_id` int(10) UNSIGNED  NULL,
  `facility_id` int(10) UNSIGNED  NULL,
  `user_remark` text DEFAULT NULL,
  `admin_remark` text DEFAULT NULL,
  `appealed_by` int(11) DEFAULT NULL,
  `appeal_update_by` int(11) DEFAULT NULL,
  `appeal_status` ENUM('0','1','2','3','4') DEFAULT '0' COMMENT '0=>NA,1=>Initiated,2=>Rejected,3=>Partial Refund,4=>Approved',
  `discount_amount` varchar(255) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
);

#Date 22/07/2024, Author: Dushyant Dogra
alter table permit_requests add  request_reason text null;
alter table user_permit_requests add  request_reason text null;
alter table permit_rate_descriptions add is_display_reason enum('0','1') default '0';
alter table permit_rates add is_display_reason enum('0','1') default '0';


#Vijay : USM Reservation Overnight change
ALTER TABLE `inventory_modules`.`category_facility_overnight_durations` 
ADD COLUMN `is_overnight_sameday` TINYINT(1) NULL DEFAULT 0 COMMENT '0 => next day or after mindnight , 1 => before mindnight start ' AFTER `overnight_booking_time_till`;
# Ujjwal - add payment details in permit_requests table

Alter table permit_requests 
add  payment_card_id int(11) null Default NULL;

Alter table user_permit_requests 
add  payment_card_id int(11) null Default NULL;

#Date 26/07/2024, Author: Dushyant Dogra
alter table permit_requests add is_prorate_apply enum("0","1") default "0";
alter table user_permit_requests add is_prorate_apply enum("0","1") default "0";

#Date 05-08-2024, Author: Dushyant Dogra #PIMS-10032
CREATE TABLE `users_permit_bundle_mapping` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `account_name_id` int(10) DEFAULT NULL,
  `permit_bundle` int(11) DEFAULT NULL,
  `user_consent` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `email_domain_validation` tinyint(1) DEFAULT '0',
  `email_domain` varchar(300) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1



alter table user_permit_requests add is_prorate_apply enum("0","1") default "0";

ALTER TABLE permit_requests
ADD COLUMN `consent_updated_at` DATETIME DEFAULT NULL;

ALTER TABLE permit_requests_renew_history
ADD COLUMN `consent_updated_at` DATETIME DEFAULT NULL;





#Date 07-08-2024, Author:Dushyant Dogra #PIMS-10032
alter table permit_requests add `business_id` int(10) unsigned NULL DEFAULT NULL;
alter table user_permit_requests add `business_id` int(10) unsigned NULL DEFAULT NULL;

#Date:08-08-2024 Dushyant PIMS-10032
CREATE TABLE `users_payment_bundle_settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `anet_transaction_id` int(10) unsigned DEFAULT NULL,
  `permit_bundle` int(11) DEFAULT NULL,
  `user_consent` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '1',
  `capacity` varchar(45) DEFAULT NULL,
  `discount_amount` double(10,2) DEFAULT '0.00',
  `promocode` varchar(255) DEFAULT NULL,
  `final_amount` decimal(10,2) DEFAULT NULL,
  `processing_fee` decimal(10,2) DEFAULT '0.00',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_anet_transaction_id` (`anet_transaction_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1

alter table users_permit_type_mapping modify permit_negotiable_price decimal(10,2) NULL default NULL;
alter table users_permit_type_mapping modify permit_price decimal(10,2) NULL default NULL;

#12-08-2024 PIMS-10032 dushyant 
rename table users_payment_bundle_settings to users_payment_bundle_mapping;
alter table users_payment_bundle_mapping add remaining_capacity int(10)  null default null;


 ALTER TABLE `brand_settings` 
ADD COLUMN `jira_board_key` VARCHAR(100) NULL DEFAULT NULL;

   ALTER TABLE `facility_brand_settings` 
ADD COLUMN `jira_board_key` VARCHAR(100) NULL DEFAULT NULL;

#13-08-2024 PIMS-10032 dushyant 
alter table users_permit_type_mapping add deleted_at timestamp NULL DEFAULT NULL

#Alka 13 August 2024 , Add columns for Buyout
ALTER TABLE `permit_requests` 
ADD COLUMN `total_usage` INT(11) DEFAULT 0,
ADD COLUMN `remaining_usage` INT(11) DEFAULT 0;

ALTER table facility_configurations ADD COLUMN is_usage_check ENUM('0', '1') NULL DEFAULT '0';

#End

#Lokesh Chaudhary: 20-Aug-2024
ALTER TABLE `reservations` 
ADD COLUMN `hub_zeag_response` JSON NULL 
AFTER `is_hub_zeag`;

ALTER TABLE `reservations_history` 
ADD COLUMN `hub_zeag_response` JSON NULL 
AFTER `is_hub_zeag`;

#13-08-2024 PIMS-10336 dushyant 
alter table permit_rate_descriptions add is_hide_validity enum('0','1') default '0';
alter table permit_type_master add is_hide_validity enum('0','1') default '0';
#Alka: 20 Aug 2024
alter table category_facility_overnight_durations add column deleted_at timestamp NULL DEFAULT NULL;
alter table category_facility_overnight_durations add column rate_id INT NULL DEFAULT 0 after is_overnight_sameday;

#Lokesh Chaudhary: 21-Aug-2024
  ALTER TABLE tickets
  MODIFY COLUMN checkout_mode ENUM('0','1','2','3','4','5','6','7','8','9') 
  NULL DEFAULT '0' 
  COMMENT '1 for Web, 2 for SMS, 3 For IM30, 4 for LPR, 5 for Admin, 6 for Android, 7 for iOS, 8 for attendant tablet, 9 for HubZeag';

  ALTER TABLE `flight_details`
  CHANGE `airline_name` `arrival_airline_name` varchar(255) COLLATE 'latin1_swedish_ci' NULL AFTER `reservation_id`,
  CHANGE `flight_no` `arrival_flight_no` varchar(255) COLLATE 'latin1_swedish_ci' NULL AFTER `arrival_airline_name`,
  CHANGE `arrival_time` `arrival_time` datetime NULL AFTER `arrival_flight_no`,
  DROP `departure_time`,
  ADD `departure_airline_name` varchar(255) COLLATE 'latin1_swedish_ci' NULL AFTER `arrival_time`,
  ADD `departure_flight_no` varchar(255) COLLATE 'latin1_swedish_ci' NULL AFTER `departure_airline_name`,
  ADD `departure_time` datetime NULL AFTER `departure_flight_no`;

#Lokesh Chaudhary: 22-Aug-2024
  ALTER TABLE `facility_configurations` 
  ADD COLUMN `booking_duration_hours` int(11) NULL DEFAULT NULL COMMENT 'Advance Booking Hours from Current Time' AFTER before_cancel_time;

#dushyant PIMS-8246 22-08-2024
alter table tickets add `checkout_facility_id` int(10) unsigned DEFAULT NULL;

#Alka
ALTER table inventory_modules_staging.permit_requests ADD COLUMN ticket_count INT(11) NULL DEFAULT '0';



### VIJAY transient case for user creation 
ALTER TABLE `inventory_modules_staging`.`users` 
CHANGE COLUMN `password` `password` VARCHAR(255) NULL DEFAULT NULL ;


#Lokesh Chaudhary: 28-Aug-2024
  ALTER TABLE `facility_configurations` 
  ADD COLUMN `is_inventory_check` enum('0','1') NULL DEFAULT '0' COMMENT '0=Not Check Inventory, 1=> Check Inventory';


  CREATE TABLE `permit_tenure_master` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) unsigned NULL,
  `facility_id` int(11) unsigned NULL,
  `name` varchar(100) NULL DEFAULT NULL,
  `status` enum('0','1') COLLATE utf8_unicode_ci DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1


CREATE TABLE `permit_tenure_mapping` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) unsigned NULL,
  `facility_id` int(11) unsigned NULL,
  `permit_rate_id` int(11) unsigned NOT NULL,
  `permit_rate_description_id` int(11) unsigned DEFAULT NULL,
  `permit_tenure_id` int(11) unsigned NOT NULL,
  `permit_tenure_name` varchar(100) DEFAULT NULL,
  `permit_start_day` int(4) DEFAULT NULL,
  `permit_start_month` int(4) DEFAULT NULL,
  `permit_start_year` int(4) DEFAULT NULL,
  `permit_end_day` int(4) DEFAULT NULL,
  `permit_end_month` int(4) DEFAULT NULL,
  `permit_end_year` int(4) DEFAULT NULL,
  `permit_frequency` int(1) DEFAULT NULL,
  `rate` decimal(10,2) DEFAULT '0.00',
  `status` enum('0','1') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;



#Alka
ALTER table inventory_modules_staging.permit_rates
ADD COLUMN `prorate_disable` ENUM('0', '1') NULL DEFAULT '0';


ALTER table inventory_modules_staging.permit_tenure_mapping
Add `partner_id` int(11) unsigned NULL After `id`,
Add `facility_id` int(11) unsigned NULL After `partner_id`;

ALTER table inventory_modules_staging.permit_tenure_master
Add `partner_id` int(11) unsigned NULL After `id`,
Add `facility_id` int(11) unsigned NULL After `partner_id`;
#Date 03-09-2024, Author:Dushyant Dogra #PIMS-10032
alter table permit_rate_descriptions add show_carpool_service enum('0','1') COLLATE utf8_unicode_ci DEFAULT '0'


ALTER TABLE `brand_settings` ADD `terms_condition_content` LONGTEXT NULL DEFAULT NULL;


#Date 05-09-2024, Author:Dushyant Dogra #PIMS-10032 phase2
ALTER TABLE users_permit_type_mapping ADD paid_capacity int(10) null default 0;

#Lokesh Chaudhary: 11-Sep-2024
CREATE TABLE `airlines` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `partner_id` INT NULL DEFAULT NULL,
    `facility_id` INT NULL DEFAULT NULL,
    `name` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
);

ALTER TABLE `flight_details`
    ADD `partner_id` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `reservation_id`,
    ADD `facility_id` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `partner_id`,
    ADD `user_id` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `facility_id`;
    ADD `arrival_id` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `user_id` COMMENT 'Primary key of airlines table',
    ADD `departure_id` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `arrival_id` COMMENT 'Primary key of airlines table';

# End

ALTER TABLE reservations_history ADD length_type enum('0','1','2') null default '0';

ALTER TABLE reservations_history ADD altered_length decimal(10,2) null default '0.00';

length_type enum('0','1','2') 0

altered_length decimal(10,2)  0.00

#Alka
ALTER table inventory_modules_staging.user_permit_requests
ADD COLUMN `reject_reason` VARCHAR(255) NOT NULL;

#Date 12-09-2024, Author:Dushyant Dogra #PIMS-10032 phase 2
alter table permit_requests_renew_history add `business_id` int(10) unsigned NULL DEFAULT NULL;

ALTER TABLE `permit_requests` 
ADD COLUMN `payment_token` text NULL DEFAULT NULL;

ALTER TABLE `permit_requests_renew_history` 
ADD COLUMN `payment_token` text NULL DEFAULT NULL;


ALTER TABLE `affiliate_business` 
ADD COLUMN `latitude` decimal(11,7) NULL DEFAULT '0.00',
ADD COLUMN `longitude` decimal(11,7) NULL DEFAULT '0.00',
ADD COLUMN `range` int(11) NULL DEFAULT NULL;

ALTER TABLE `promo_usages` 
ADD COLUMN `partner_id` int(11) NULL DEFAULT NULL,
ADD COLUMN `permit_request_id` int(11) NULL DEFAULT NULL;


#Lokesh Chaudhary: 24-Sep-2024
  ALTER TABLE `permit_vehicles` 
  ADD COLUMN `make_id` int(10) NULL DEFAULT NULL After make,
  ADD COLUMN `model_id` int(10) NULL DEFAULT NULL After model;

ALTER TABLE facility_payment_details
ADD COLUMN data_trans_merchant_name varchar(255) DEFAULT NULL AFTER dataTrans_error_url,
ADD COLUMN data_trans_gpay_merchant varchar(255) DEFAULT NULL AFTER data_trans_merchant_name;

#Alka
ALTER TABLE `permit_requests` 
ADD COLUMN `permit_payment_remarks` VARCHAR(255) DEFAULT NULL;


#Kuldeep : 24-Sep-2024
#Ticket no:- 10825
ALTER TABLE `permit_vehicles` 
ADD COLUMN `color_id` int(10) DEFAULT NULL AFTER `color`;

ALTER TABLE `permit_requests` 
Add `transaction_retry` enum('0','1','2','3') DEFAULT '0';




#DD : 25-Sep-2024 PIMS-10727
CREATE TABLE `reservation_checkin_neighbor_history` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `facility_id` int(10) unsigned NOT NULL,
    `partner_id` int(10) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `cancelled_at` timestamp NULL DEFAULT NULL,
    `is_ticket` enum('0','1','2') DEFAULT '0',
    `license_plate` varchar(45) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_facility_id` (`facility_id`),
    KEY `idx_partner_id` (`partner_id`),
    KEY `idx_license_plate` (`license_plate`)
  )


ALTER TABLE `anet_transactions` 
ADD COLUMN `card_brand_transaction_id` varchar(100) NULL DEFAULT NULL;


ALTER TABLE `heartland_payment_profile` 
ADD COLUMN `card_brand_transaction_id` varchar(100) NULL DEFAULT NULL;

ALTER TABLE `datacap_transactions` 
ADD COLUMN `card_brand_transaction_id` varchar(100) NULL DEFAULT NULL,
ADD COLUMN `AuthCode` varchar(100) NULL DEFAULT NULL;

#27-09-2024 DD PIMS-10864
alter table user_permit_vehicles add `is_default` int(2) NOT NULL DEFAULT '0';

#Date: 08/10/2024, Author: Lokesh Chaudhary
ALTER TABLE `datacap_transactions` 
ADD COLUMN `reservation_id` int(10) NULL DEFAULT NULL AFTER ticket_id;

ALTER TABLE `reservations` 
ADD COLUMN `is_charged` enum('0', '1') NULL DEFAULT '0' AFTER total,
ADD COLUMN `charged_at` datetime NULL DEFAULT NULL AFTER is_charged;

ALTER TABLE `reservations`
ADD `charged_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'Charged amount from ROC Cron' AFTER `is_charged`;
//END


#vijay : 10-10-2024 : Add card in profile

CREATE TABLE `partner_payment_gatways` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `partner_id` int(11) DEFAULT NULL,
   `facility_payment_type_id` varchar(16) DEFAULT NULL,
   `payment_mid` varchar(50) DEFAULT NULL,   
   `facility_ids` text NULL DEFAULT NULL,
   `created_at` datetime DEFAULT NULL,
   `updated_at` datetime DEFAULT NULL,
   `deleted_at` timestamp NULL DEFAULT NULL,
   PRIMARY KEY (`id`)
 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

 ALTER TABLE `inventory_modules_staging`.`partner_payment_gatways` 
RENAME TO  `inventory_modules_staging`.`partner_payment_gateways` ;


ALTER TABLE `inventory_modules_staging`.`datacap_payment_profile` 
ADD COLUMN `facility_payment_type_id` TINYINT(1) NULL DEFAULT 0 AFTER `is_default`;

ALTER TABLE `inventory_modules_staging`.`datacap_payment_profile` 
CHANGE COLUMN `facility_payment_type_id` `facility_payment_type_id` TINYINT(1) NULL DEFAULT '0' AFTER `partner_id`;

ALTER TABLE `inventory_modules_staging`.`datacap_payment_profile` 
ADD COLUMN `partner_payment_gateway_id` TINYINT(1) NULL DEFAULT 0 AFTER `facility_payment_type_id`;

ALTER TABLE `inventory_modules_staging`.`planet_payment_profile` 
ADD COLUMN `facility_payment_type_id` TINYINT(1) NULL DEFAULT 0 AFTER `is_default`;

ALTER TABLE `inventory_modules_staging`.`planet_payment_profile` 
ADD COLUMN `partner_payment_gateway_id` TINYINT(1) NULL DEFAULT 0 AFTER `is_default`;

ALTER TABLE `inventory_modules_staging`.`heartland_payment_profile` 
ADD COLUMN `facility_payment_type_id` TINYINT(1) NULL DEFAULT 0 AFTER `card_brand_transaction_id`,
ADD COLUMN `partner_payment_gateway_id` TINYINT(1) NULL DEFAULT 0 AFTER `facility_payment_type_id`;


## Vijay Close 


#Alka: 11-Oct-2024: Add facility promocode

  CREATE TABLE `promotion_facilities` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `facility_id` int(11) unsigned NULL,
  `promotion_id` int(11) unsigned NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1

#End


ALTER TABLE facility_payment_details
  ADD COLUMN heartland_merchant_name varchar(255) DEFAULT NULL;

ALTER TABLE facility_payment_details
  ADD COLUMN heartland_merchant_id varchar(255) DEFAULT NULL;
  
ALTER TABLE facility_payment_details
  ADD COLUMN heartland_applepay_identifier varchar(255) DEFAULT NULL;


#Kuldeep : 17-Oct-2024
#Ticket no:- 11159
ALTER TABLE `inventory_modules`.`mst_colors` ADD COLUMN `deleted_at` TIMESTAMP NULL DEFAULT NULL AFTER `name`;
ALTER TABLE `inventory_modules`.`mst_colors` ADD COLUMN `created_at` TIMESTAMP NULL DEFAULT NULL AFTER `deleted_at`;
ALTER TABLE `inventory_modules`.`mst_colors` ADD COLUMN `updated_at` TIMESTAMP NULL DEFAULT NULL AFTER `created_at`;




ALTER TABLE `inventory_modules_staging`.`facilities` 
CHANGE COLUMN `garage_id` `garage_id` INT(10) UNSIGNED NOT NULL DEFAULT 0 ;


#Lokesh Chaudhary: 24-Sep-2024
ALTER TABLE `reservations_history`
MODIFY COLUMN `is_hub_zeag` ENUM('0', '1', '2', '3', '4') NOT NULL 
COMMENT '0 => All, 1 => Belongs to ROC/HUb, 2 => Created on Hub, 3 => Updated on Hub, 4 => Cancelled on Hub';

ALTER TABLE `reservations`
MODIFY COLUMN `is_hub_zeag` ENUM('0', '1', '2', '3', '4') NOT NULL 
COMMENT '0 => All, 1 => Belongs to ROC/HUb, 2 => Created on Hub, 3 => Updated on Hub, 4 => Cancelled on Hub';


ALTER TABLE reservations_history
ADD COLUMN payable_amount double(10,2) DEFAULT '0' AFTER altered_length;

#Lokesh Chaudhary: 14-Nov-2024
CREATE TABLE `service_masters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `service_type` varchar(255) NOT NULL,
  `required_fields` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);

#Kuldeep: 18-Nov-2024
ALTER TABLE `inventory_modules_staging`.`facilities` 
ADD COLUMN `is_mob_device_mode` varchar(50) DEFAULT NULL AFTER `is_checkout_enabled`;

#Alka: 20 Nov 2024
ALTER TABLE permit_requests
  ADD COLUMN service_prorate decimal(10,2) DEFAULT NULL,
  ADD COLUMN permit_prorate decimal(10,2) DEFAULT NULL;

ALTER TABLE permit_request_service_mapping
  ADD COLUMN service_prorate decimal(10,2) DEFAULT NULL;

ALTER TABLE user_permit_requests
  ADD COLUMN service_prorate decimal(10,2) DEFAULT NULL,
  ADD COLUMN permit_prorate decimal(10,2) DEFAULT NULL;

#DD: 20-Nov-2024
ALTER TABLE reservations ADD multi_facility_id varchar(1000) default null;
ALTER TABLE users ADD display_own_permits_only enum('0','1') default '0';


#Alka: 25 Nov 2024
  CREATE TABLE `facility_closures` (
      `id` INT AUTO_INCREMENT PRIMARY KEY,
      `facility_id` int(11) unsigned NULL,
      `close_from` TIMESTAMP NULL DEFAULT NULL,
      `close_to` TIMESTAMP NULL DEFAULT NULL,
      `message` VARCHAR(255) DEFAULT NULL,
      `mode` ENUM('0','1', '2') NOT NULL DEFAULT '0' COMMENT '0 both, 1 sms, 2 email',
      `schedule_time` TIMESTAMP NULL DEFAULT NULL,
      `week_days` VARCHAR(255) DEFAULT NULL,
      `status` INT(1) NULL DEFAULT 0,
      `closure_type` ENUM('0','1', '2','3') NULL DEFAULT NULL COMMENT '0 all day, 1 week days, 2 hours,3 specific date',
      `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` TIMESTAMP NULL DEFAULT NULL
  );

#DD: 27-11-2024
alter table users add payment_mode enum('0','1') default '0';

#DD: 02-12-2024 pims-11704
alter table permit_requests add renew_type enum('0','1') default '0';

ALTER TABLE `permit_rate_descriptions` 
CHANGE COLUMN `is_hide_staff` `is_hide_staff` ENUM('0', '1') NULL DEFAULT '1' ;


ALTER TABLE `permit_rates` 
CHANGE COLUMN `is_hide_staff` `is_hide_staff` ENUM('0', '1') NULL DEFAULT '1' ;


ALTER TABLE user_passes
ADD COLUMN session_id varchar(100) DEFAULT NULL;

ALTER TABLE `user_passes` 
ADD COLUMN `payment_token` text NULL DEFAULT NULL;


#Sagar: 09/12/2024, PIMS - 11744
INSERT INTO `inventory_modules_staging`.`rate_types` (`rate_type`, `is_create`, `created_at`, `updated_at`, `is_member_rate`) 
VALUES ('Promo Board Rates', '1', '2024-12-09 11:06:00', '2024-12-09 11:06:00', '0');

ALTER TABLE `inventory_modules_staging`.`promotions` 
COLLATE = utf8_estonian_ci ,
ADD COLUMN `rate_id` INT(11) NULL DEFAULT NULL AFTER `rm_id`;

#KT: 11-12-2024
ALTER TABLE `inventory_modules_staging`.`blacklisted_vehicles` 
ADD COLUMN `facility_id` int(11) NULL DEFAULT NULL AFTER `id`,
ADD COLUMN `partner_id` int(45) NULL DEFAULT NULL AFTER `status`,
ADD COLUMN `is_active` enum('0','1') NULL AFTER `partner_id`,
CHANGE COLUMN `deleted_at` `deleted_at` TIMESTAMP NULL DEFAULT NULL AFTER `is_active`;

ALTER TABLE `inventory_modules_staging`.`blacklisted_vehicles` 
ADD UNIQUE INDEX `license_plate_number_UNIQUE` (`license_plate_number` ASC);
;
#end

#KT: 13-12-2024 
ALTER TABLE `inventory_modules_staging`.`blacklisted_vehicles` 
CHANGE COLUMN `plate_type` `plate_type` ENUM('1', '2', '3') CHARACTER SET 'utf8' COLLATE 'utf8_unicode_ci' NULL DEFAULT '1' ;


#Date: 12/12/2024, Author: Lokesh Chaudhary
ALTER table reservations ADD COLUMN is_update ENUM('0', '1') NULL DEFAULT '0' COMMENT '1 => Reservation Updated, 0 => Reservation Created';

#Sagar: 12/12/2024, PIMS - 11744
CREATE TABLE promo_rate_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(255) NOT NULL,
    status TINYINT(1) DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

#Sagar: 17/12/2024, PIMS - 11744
ALTER TABLE `inventory_modules_staging`.`rates` 
ADD COLUMN `promo_category_id` INT(11) NULL DEFAULT NULL AFTER `display_time_end`;


#Sagar: 17/12/2024, PIMS - 11744
CREATE TABLE promo_rate_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(255) NOT NULL,
    status TINYINT(1) DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

#Sagar: 17/12/2024, PIMS - 11744
ALTER TABLE `inventory_modules_staging`.`rates` 
ADD COLUMN `promo_category_id` INT(11) NULL DEFAULT NULL AFTER `display_time_end`;

#Sagar: 17/12/2024, PIMS - 11744
ALTER TABLE `promotions` CHANGE `rate_id` `rate_category_id` VARCHAR(100) NULL DEFAULT NULL;

#Sagar: 17/12/2024, PIMS - 11744
ALTER TABLE `promotions` ADD `usage_type` INT NULL DEFAULT NULL COMMENT '1=>one_time | 2=>multiple | 3=>unlimited | 4=>day_wise' AFTER `rate_category_id`;

#Sagar: 17/12/2024, PIMS - 11744
ALTER TABLE `promotions` ADD `is_validity_check` TINYINT NOT NULL DEFAULT '1' COMMENT '1=>yes|0=>no' AFTER `usage_type`;

#DATE: 12/12/2024, Author: DD
alter table permit_rate_descriptions add is_hide_upload_id enum('0','1') default '1';
insert into permit_rate_descriptions values(null,'Visiting Staff/Faculty','Visiting Staff/Faculty','Yearly','Yearly',1,363361,1,'0',now(),null,null,'Visiting Staff/Faculty',0,51000,40,1,'0','0','0','0',1,'@yopmail.com','2024-12-01','2025-06-30',12,'0','0','0','0','0');

 insert into permit_rates values(null,87,407,0,'1','Visiting Staff/Faculty','0','0',NULL,NULL,NULL,'1','0',NULL,NOW(),NOW(
),'0','0','0','0','0','0','0','0','00:00:00','00:00:00','00:00:00','00:00:00','0','0','0','0','0','0','0');

 insert into permit_type_master_rate_desc_mapping values(null,87,5,now(),null);

#KT: 18-12-2024 
ALTER TABLE `inventory_modules_staging`.`blacklisted_vehicles` 
ADD COLUMN `make_id` INT(11) NULL AFTER `make_model`,
ADD COLUMN `model_id` INT(11) NULL AFTER `make_id`,
ADD COLUMN `color_id` INT(11) NULL AFTER `color`,
ADD COLUMN `state_id` INT(11) NULL DEFAULT NULL AFTER `is_active`,
ADD COLUMN `created_by` INT(11) NULL DEFAULT NULL AFTER `deleted_at`;

PIMS-11910
ALTER TABLE `affiliate_business` 
ADD COLUMN `logo` text NULL DEFAULT NULL,
ADD COLUMN `banner` text NULL DEFAULT NULL,
ADD COLUMN `color` varchar(255) NULL DEFAULT NULL,
ADD COLUMN `rgb_color` varchar(255) NULL DEFAULT NULL,
ADD COLUMN `secondary_color` varchar(255) NULL DEFAULT NULL,
ADD COLUMN `secondary_rgb_color` varchar(255) NULL DEFAULT NULL,
ADD COLUMN `favicon` text NULL DEFAULT NULL;


ALTER TABLE customer_permissions
ADD COLUMN `subordiante_id` int(11) NULL DEFAULT NULL AFTER rm_id;


ALTER TABLE customerportal_permissions
ADD COLUMN `subordiante_id` int(11) NULL DEFAULT NULL AFTER rm_id;

#KT 30-12-2024
#PIMS-6861
CREATE TABLE notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    partner_id INT NOT NULL,
    facility_id INT NOT NULL,
    notification_time INT DEFAULT 30, -- Time in minutes
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

#DD 03-01-2024 pims-12065
 alter table facility_configurations add cancel_type enum('0','1') default '0';



#Date: 08/Jan/2025, Author: Lokesh Chaudhary
ALTER TABLE `reservations`
MODIFY `is_hub_zeag` ENUM('0', '1', '2', '3', '4', '5', '6', '7', '8') NOT NULL 
COMMENT '0 => Others, 1 => Belongs to ROC/HUb, 2 => Created on Hub, 3 => Updated on Hub, 4 => Cancelled on Hub, 5 => Failed on Create, 6 => Failed on Update, 7-8 => Additional States';

ALTER TABLE `reservations_history`
MODIFY `is_hub_zeag` ENUM('0', '1', '2', '3', '4', '5', '6', '7', '8') NOT NULL 
COMMENT '0 => Others, 1 => Belongs to ROC/HUb, 2 => Created on Hub, 3 => Updated on Hub, 4 => Cancelled on Hub, 5 => Failed on Create, 6 => Failed on Update, 7-8 => Additional States';

#date: 10-01-2025, Author: DD
 alter table all_failed_transactions add license_plate json default null;
 alter table all_failed_transactions add (card_last_four varchar(16) default null,card_type varchar(50) default null,card_name varchar(50) default null,expiry varchar(10) default null);
#promocode changes
 ALTER TABLE `inventory_modules_staging`.`promotions` 
ADD COLUMN `no_of_times` INT(2) NULL DEFAULT NULL COMMENT '1 for one times,0 for multiple times it only for hours default null' AFTER `is_validity_check`;
ALTER TABLE `inventory_modules_staging`.`promotions` 
ADD COLUMN `applicable_to_hours` INT(2) NULL DEFAULT NULL COMMENT '0 for =>Less than the defined hours 1 for =>equal/Greater than the defined hours' AFTER `no_of_times`;


#Dev: Sagar | PIMS-11888 | 14-01-2025
ALTER TABLE `inventory_modules_staging`.`facility_configurations` ADD `is_prepay_enabled` TINYINT NOT NULL DEFAULT '0' AFTER `is_lpr_registered_user_payment_enabled`;

ALTER TABLE `inventory_modules_staging`.`promotions` 
ADD COLUMN `vaild_hours` VARCHAR(45) NULL DEFAULT NULL AFTER `applicable_to_hours`;


#DD , pims-12258 , 20-01-2025

alter table permit_rate_descriptions add (is_hide_campusId enum('0','1') default '1',is_hide_employeeID enum('0','1') default '1');
alter table permit_rate_descriptions add (is_required_campusId enum('0','1') default '0',is_required_employeeID enum('0','1') default '0');

alter table permit_type_master add (is_hide_campusId enum('0','1') default '1',is_hide_employeeID enum('0','1') default '1');
alter table permit_requests add user_campus_id varchar(50) default null;

CREATE TABLE `users_permit_service_mapping` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `permit_service_id` int(10) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
)

alter table all_failed_transactions modify license_plate text default null;
alter table user_permit_requests add user_campus_id varchar(50) default null;
alter table permit_requests_renew_history add user_campus_id varchar(50) default null;

#Date: 24/Jan/2025, Author: Lokesh Chaudhary
ALTER TABLE reservations
ADD COLUMN checkin_status VARCHAR(255) NULL DEFAULT NULL COMMENT 'Hub Zeag checkin_status' AFTER is_ticket;

ALTER TABLE user_passes
ADD COLUMN refund_amount int(11) unsigned DEFAULT NULL,
ADD COLUMN refund_type ENUM('1','2') NULL DEFAULT NULL COMMENT "1 - full refund, 2 - partial refund",
ADD COLUMN refund_remarks  VARCHAR(255) NULL,
ADD COLUMN refund_date datetime DEFAULT NULL,
ADD COLUMN refund_by int(11) unsigned DEFAULT NULL,
ADD COLUMN refund_transaction_id VARCHAR(255) NULL,
ADD COLUMN refund_status VARCHAR(255) NULL;


ALTER TABLE reservations
ADD COLUMN refund_type ENUM('1','2') NULL DEFAULT NULL COMMENT "1 - full refund, 2 - partial refund" AFTER refund_amount,
ADD COLUMN refund_remarks  VARCHAR(255) NULL AFTER refund_type,
ADD COLUMN refund_date datetime DEFAULT NULL AFTER refund_remarks,
ADD COLUMN refund_by int(11) unsigned DEFAULT NULL AFTER refund_date;

ALTER TABLE reservations_history
ADD COLUMN refund_type ENUM('1','2') NULL DEFAULT NULL COMMENT "1 - full refund, 2 - partial refund" AFTER refund_amount,
ADD COLUMN refund_remarks  VARCHAR(255) NULL AFTER refund_type,
ADD COLUMN refund_date datetime DEFAULT NULL AFTER refund_remarks,
ADD COLUMN refund_by int(11) unsigned DEFAULT NULL AFTER refund_date;


#PIMS-12423 #dd #24-01-2025
alter table warnings add vin varchar(255) default null;
alter table ticket_citations add vin varchar(255) default null;


ALTER TABLE user_validate_master
ADD COLUMN partner_id int(11) unsigned DEFAULT NULL;

ALTER TABLE user_validate_master
ADD COLUMN status int(1) unsigned DEFAULT '1';

CREATE TABLE `user_validate_master_history` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `capping_id` int(10) unsigned NOT NULL,
  `user_id` int(11) NOT NULL,
  `partner_id` int(11) unsigned DEFAULT NULL,
  `monthly_maximum_amount` decimal(8,2) DEFAULT NULL,
  `monthly_remaining_amount` decimal(8,2) DEFAULT NULL,
  `paid_type` enum('0','1','2','3','4') COLLATE utf8_unicode_ci DEFAULT '0' COMMENT '1 for hour off, 2 for amount off, 3 for Percent off, 4 for day off',
  `month` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `year` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `max_hour` decimal(8,2) DEFAULT NULL,
  `max_remaining_hour` decimal(8,2) DEFAULT NULL,
  `max_dollar` decimal(8,2) DEFAULT NULL,
  `max_remaining_dollar` decimal(8,2) DEFAULT NULL,
  `full_amount_max` decimal(8,2) DEFAULT NULL,
  `full_amount_remaining` decimal(8,2) DEFAULT NULL,
  `status` int(1) unsigned DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
#Date: 12/02/2024, Author: Lokesh Chaudhary
ALTER TABLE reservations 
MODIFY length_type ENUM('0', '1', '2', '3') NULL DEFAULT '3' 
COMMENT '0 => Normal Length, 1 => Extend, 2 => Reduced, 3 => Not Alter Length';

ALTER TABLE reservations_history 
MODIFY length_type ENUM('0', '1', '2', '3') NULL DEFAULT '3' 
COMMENT '0 => Normal Length, 1 => Extend, 2 => Reduced, 3 => Not Alter Length';
#End

#date 31-01-2025 for user_pases
ALTER TABLE user_passes
ADD name VARCHAR(255) NULL,
    pass_type ENUM('0', '1', '2', '3') DEFAULT '0';

ALTER TABLE rate_categories
ADD is_usage_finite INT DEFAULT 0;

#end


#Date: 10/Feb/2025, Author: Lokesh Chaudhary
CREATE TABLE `user_history` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `anon` tinyint(1) NOT NULL DEFAULT '1',
  `user_type` tinyint(4) NOT NULL DEFAULT '5',
  `created_by` int(11) NOT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `address` text,
  `address2` text,
  `city` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `pincode` varchar(20) DEFAULT NULL,
  `service_id` int(11) DEFAULT NULL,
  `common_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
)
#end

ALTER TABLE `facility_configurations` 
ADD COLUMN `is_display_validate_button` ENUM('0', '1') NULL DEFAULT '0';


ALTER TABLE facility_configurations ADD COLUMN custom_driveup_enabled_event_app ENUM('0', '1') NOT NULL DEFAULT '0';

#KT: 14-02-2025
ALTER TABLE `notification_settings` 
ADD COLUMN `reservation_last_run` varchar(255) NOT NULL;
ADD COLUMN `transient_last_run` varchar(255) NOT NULL;
ADD COLUMN `permit_last_run` varchar(255) NOT NULL;

# PIMS-12326 | Dev: Sagar | 17/02/2025
CREATE TABLE `masqurade_trailing_data` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
   `masqurade_parent_id` bigint(20) unsigned DEFAULT NULL,
   `event_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
   `event_id` bigint(20) unsigned NOT NULL,
   `event_action` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'create',
   `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
   `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   PRIMARY KEY (`id`),
   KEY `masqurade_parent_id` (`masqurade_parent_id`),
   KEY `event_id` (`event_id`)
 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

#DD 17-03-2025
alter table permit_requests add third_party_permit enum('0','1') default '0';


ALTER TABLE facility_payment_details
ADD COLUMN heartland_gpay_merchant varchar(100) DEFAULT NULL;

#Date: 21/Feb/2025, Author: Lokesh Chaudhary
ALTER TABLE `facility_configurations` 
ADD COLUMN `is_oversize_enabled` ENUM('0', '1') NULL DEFAULT '0'
COMMENT '1 Active, 0 Inactive';

ALTER TABLE `tickets` 
ADD COLUMN `is_vehicle_oversize` ENUM('0', '1') NULL DEFAULT '0'
COMMENT '1 Active, 0 Inactive';
#end

#PIMS-12902 | Dev: sagar | 24-02-2025
INSERT INTO `services` (`id`, `full_name`, `short_name`, `slug`, `list_order`, `created_at`, `updated_at`, `is_offline`, `deleted_at`, `is_display`) VALUES ('13', 'Support', 'SUP', 'support', '13', '2025-02-24 16:34:54', '2025-02-24 16:34:54', '0', null, '1');

ALTER TABLE `request_demos` ADD COLUMN `description` LONGTEXT NULL;

#Date: 24-02-2025, Author: DD, pims-12870
create table lpr_payload(id int(11) auto_increment primary key,feed_time datetime not null,location varchar(10) not null,license_plate
varchar(30) not null,state varchar(20) default null,session_id varchar(200) not null,created_at datetime,updated_at timestamp);
alter table lpr_payload add api_type enum('0','1') default '0';

ALTER TABLE `permit_email_rate_mapping` ADD COLUMN `partner_id` int(11) NULL Default NULL After permit_rate_id;


ALTER TABLE `affiliate_business` 
ADD COLUMN `subordinate_id` int(11) NULL DEFAULT NULL After rm_id;


ALTER TABLE `users` 
ADD COLUMN `subordinate_id` int(11) NULL DEFAULT NULL After user_parent_id;


//promo code 
ALTER TABLE promo_codes DROP INDEX promocode_UNIQUE;

//
CREATE TABLE `third_party_parkchirp` (
    `id` INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `facilityId` VARCHAR(255) DEFAULT NULL,
    `sublotId` DOUBLE DEFAULT NULL,
    `parkerId` VARCHAR(50) DEFAULT NULL,
    `parkerFirstName` VARCHAR(50) DEFAULT NULL,
    `parkerLastName` VARCHAR(50) DEFAULT NULL,
    `parkerFullName` VARCHAR(100) DEFAULT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `startDate` DATETIME DEFAULT NULL,
    `endDate` DATETIME DEFAULT NULL,
    `subscriptionId` VARCHAR(50) DEFAULT NULL,
    `subscriptionName` VARCHAR(100) DEFAULT NULL,
    `accountId` VARCHAR(50) DEFAULT NULL,
    `vehicles` JSON DEFAULT NULL,
    `createdAt` TIMESTAMP NULL DEFAULT NULL
);

//
ALTER TABLE users ADD COLUMN thirdparty_userid VARCHAR(255) NULL UNIQUE AFTER parker_id;

//
ALTER TABLE permit_requests 
ADD COLUMN third_party_type INT(11) DEFAULT 0 NULL,
ADD COLUMN third_party_subscription_id VARCHAR(255) NULL,
ADD COLUMN third_party_user_id VARCHAR(255) NULL,
ADD COLUMN third_party_account_id VARCHAR(255) NULL;

#KT: 07-03-2025 | PIMS:13075
ALTER TABLE `inventory_modules_staging`.`permit_vehicles` 
ADD COLUMN `style_id` INT(11) NULL DEFAULT NULL AFTER `country`;



ALTER TABLE `inventory_modules_staging`.`user_permit_vehicles` 
ADD COLUMN `style_id` INT(11) NULL DEFAULT NULL AFTER `country`;

ALTER TABLE `inventory_modules_staging`.`permit_vehicles` 
ADD COLUMN `style` INT(11) NULL DEFAULT NULL AFTER `style_id`;

ALTER TABLE `inventory_modules_staging`.`user_permit_vehicles` 
ADD COLUMN `style` INT(11) NULL DEFAULT NULL AFTER `style_id`;


#Date: 11/03/2025, Author: Lokesh Chaudhary
Alter table facility_payment_details add column test_amount decimal(10, 2) defailt '3.00';
// END

# PIMS-12326 | Dev: Sagar | 17/02/2025
CREATE TABLE `masqurade_trailing_data` (
   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
   `masqurade_parent_id` bigint(20) unsigned DEFAULT NULL,
   `event_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
   `event_id` bigint(20) unsigned NOT NULL,
   `event_action` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'create',
   `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
   `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   PRIMARY KEY (`id`),
   KEY `masqurade_parent_id` (`masqurade_parent_id`),
   KEY `event_id` (`event_id`)
 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

 # PIMS-12999 | Dev: Sagar | 20/03/2025
 ALTER TABLE `users`
ADD COLUMN `vanguard_username` VARCHAR(255) COLLATE utf8_unicode_ci DEFAULT NULL AFTER `vanguard_access_token`,
ADD COLUMN `vanguard_password` VARCHAR(255) COLLATE utf8_unicode_ci DEFAULT NULL AFTER `vanguard_username`;


CREATE TABLE `facility_qrcodes` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `partner_id` int(11) DEFAULT NULL,
  `facility_id` int(11) DEFAULT NULL,
  `qrcode_number` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` enum('0','1') DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
#DD pims-13318
create table mst_vehicle_types(id int(10) unsigned not null auto_increment primary key,name varchar(255) not null,code varchar(255) default null,partner_id int(10) default null, created_at timestamp null default null,updated_at timestamp null default null,deleted_at timestamp null default null);
alter table permit_vehicles add (vehicle_type_id int(11) default null);
alter table user_permit_vehicles add (vehicle_type_id int(11) default null);

ALTER TABLE `promotions` 
ADD COLUMN `rm_id` INT(11) NULL DEFAULT NULL;
#DD pims-13379
alter table permit_requests add permit_type_master_id int(10) default null;
alter table user_permit_requests add permit_type_master_id int(10) default null;
#dd pims-12704
alter table permit_rates add is_thirdparty enum('0','1') default '0';
 alter table permit_rate_descriptions add is_thirdparty enum('0','1') default '0';

#PIMS-13534 | Dev:Sagar | 11-04-2025
ALTER TABLE `events`
ADD `display_rate_with_event` TINYINT(1) NOT NULL DEFAULT 0 AFTER `created_by`;

#dd pims-12704
INSERT INTO `permit_rate_descriptions` (`name`, `name_alias`, `hours_description`, `description`, `active_status`, `partner_id`, `type_id`, `is_resident`, `created_at`, `label`, `is_promotion`, `campus_id`, `order`, `is_hide_staff`, `is_hide_vehicle`, `is_hide_permit_service`, `is_purchase`, `is_negotiable`, `email_domain_validation`, `permit_start_date`, `permit_frequency`, `is_display_reason`, `is_hide_validity`, `show_carpool_service`, `is_hide_carpool_toogle`, `is_hide_upload_id`, `is_hide_campusId`, `is_hide_employeeID`, `is_required_campusId`, `is_required_employeeID`, `is_thirdparty`) VALUES ('Employee', 'Employee', 'Monthly', 'Monthly', '1', '40867', '1', '0', '2025-04-14 06:36:48', 'Employee', '0', '40867', '1', '1', '0', '1', '1', '0', '0', '2025-03-01', '6', '0', '1', '1', '1', '1', '0', '1', '1', '0', '1');

INSERT INTO `permit_rates` ( `permit_rate_description_id`, `facility_id`, `rate`, `active`, `name`, `is_unlimited`, `total_usage`, `remaining_usage`, `user_type`, `created_at`, `updated_at`, `is_promotion`, `monday`, `tuesday`, `wednesday`, `thursday`, `friday`, `saturday`, `sunday`, `entry_time_begin`, `entry_time_end`, `exit_time_begin`, `exit_time_end`, `is_hide_vehicle`, `is_hide_permit_service`, `is_hide_staff`, `is_display_reason`, `penality_amount`, `prorate_disable`, `is_hide_carpool_toogle`, `is_thirdparty`) VALUES ( '84', '272', '0.00', '1', 'Employee', '0', '0', '0', '1', '2025-04-14 02:40:36', '2025-04-14 02:40:36', '0', '0', '0', '0', '0', '0', '0', '0', '00:00:00', '00:00:00', '00:00:00', '00:00:00', '0', '0', '1', '0', '0', '0', '0', '1');

#PIMS-13530 | Dev: Sagar | Fee audit trail table
CREATE TABLE `fee_audit_trail` (
   `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
   `tax_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `old_tax_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `tax_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 = Amount, 1 = Percentage',
   `old_tax_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 = Amount, 1 = Percentage',
   `citation_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `old_citation_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `drive_up_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `old_drive_up_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `permit_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `old_permit_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `pass_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `old_pass_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `reservation_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `old_reservation_processing_fee` decimal(10,2) NOT NULL DEFAULT '0.00',
   `additional_fee` decimal(10,2) DEFAULT NULL,
   `old_additional_fee` decimal(10,2) DEFAULT NULL,
   `surcharge_fee` decimal(10,2) DEFAULT NULL,
   `old_surcharge_fee` decimal(10,2) DEFAULT NULL,
   `oversize_fee` decimal(10,2) DEFAULT NULL,
   `old_oversize_fee` decimal(10,2) DEFAULT NULL,
   `modified_by` int(11) NOT NULL,
   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   PRIMARY KEY (`id`)
 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci


 ALTER TABLE facility_payment_details
  ADD COLUMN heartland_device_id varchar(255) DEFAULT NULL,
  ADD COLUMN heartland_site_id varchar(255) DEFAULT NULL,
  ADD COLUMN heartland_website_id varchar(255) DEFAULT NULL;

#DD pims-13318,
insert into mst_vehicle_types(name,code,partner_id,created_at) VALUES('HEV (Hybrid Electric Vehicle)','HEV',NULL,NOW()),('PHEV (Plug-in Hybrid Electric Vehicle)','PHEV',NULL,NOW());
insert into mst_vehicle_types(name,code,partner_id,created_at) VALUES('HEV (Hybrid Electric Vehicle)','HEV',NULL,NOW()),('PHEV (Plug-in Hybrid Electric Vehicle)','PHEV',19349,NOW());
insert into mst_vehicle_types(name,code,partner_id,created_at) VALUES('BEV/EV (Fully Electric)','BEV/EV',NULL,NOW()),('Conventional (Gas or Diesel powered)','Conventional',NULL,NOW());
insert into mst_vehicle_types(name,code,partner_id,created_at) VALUES('BEV/EV (Fully Electric)','BEV/EV',NULL,NOW()),('Conventional (Gas or Diesel powered)','Conventional',19349,NOW());
INSERT INTO `customerportal_permissions_master` (`id`, `partner_id`, `name`, `display_name`, `description`, `is_default`, `type`, `list_order`, `created_at`, `updated_at`) VALUES (NULL, '19349', 'Get Vehicle Type', 'Get Vehicle Type', 'Get Vehicle Type', '0', '3', '25', '2025-03-07 10:48:51', '2025-03-07 10:48:51');

#dd pims-13537
alter table users add event_duration_hours decimal(10,2) default '0';
update users set event_duration_hours='24' where id in (363362,26380);

#dd pims-13713
alter table facilities add allow_refund enum('0','1') default '0';
alter table facilities add is_refund_hourly enum('0','1') default '0';

#dd pims-13373
ALTER TABLE user_permit_requests ADD is_antipass_enabled int(1) DEFAULT 0;

ALTER TABLE ticket_additional_info ADD user_pass_id int(11) DEFAULT 0;

#dd pims-12870
alter table lpr_payload add response text default null;

#pims-13373 #dd
alter table facility_configurations add  `is_antipass_enabled` enum('0','1')  DEFAULT '0';
alter table permit_requests add  `passback_status` enum('0','1','2','3')  DEFAULT '0';
ALTER TABLE permit_requests ADD `passback_status` ENUM('0','1','2','3') DEFAULT '0' COMMENT '0 = No check-in/checkout, 1 = Check-in, 2 = Checkout, 3 = Reset';
ALTER TABLE permit_requests_renew_history ADD `passback_status` ENUM('0','1','2','3') DEFAULT '0' COMMENT '0 = No check-in/checkout, 1 = Check-in, 2 = Checkout, 3 = Reset';

#PIMS-13857 | Dev: Sagar | Date: 08/05/2025
CREATE TABLE master_facility_short_code (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    short_code VARCHAR(255) NOT NULL UNIQUE,
    type TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 = GARAGE_CODE, 1 = QRCODE_SERIES',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '1 = active, 0 = inactive',
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE short_code_facility_mapping (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    short_code_id BIGINT UNSIGNED NOT NULL,
    facility_id BIGINT UNSIGNED NOT NULL,
    partner_id BIGINT UNSIGNED NOT NULL,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (short_code_id) REFERENCES master_facility_short_code(id) ON DELETE CASCADE
);
#Date: 08/05/2025, Author: Lokesh Chaudhary
ALTER TABLE users add COLUMN max_vehicle int(11)  default 0;
#END

#Date: 12/05/2025, Author: Lokesh Chaudhary
ALTER TABLE business_qrcode ADD enabled_ticket_creation_flow tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 = Disable Ticket Creation, 1 = Enable Ticket Creation';
#END

#dd pims-13916
alter table permit_vehicles add prev_license_plate varchar(255) default null;
alter table user_permit_vehicles add prev_license_plate varchar(255) default null;

#sunil pims-134
ALTER TABLE `inventory_modules_staging`.`facilities` 
ADD COLUMN `tax_with_surcharge_enable` TINYINT(1) NULL DEFAULT 0 AFTER `is_refund_hourly`;

#PIMS-14141 | Dev: Sagar
CREATE TABLE master_portal_list (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    portal_name VARCHAR(255) NOT NULL,
    status TINYINT(1) DEFAULT 1 COMMENT '1 = Active, 0 = Inactive',
    created_at TIMESTAMP NULL DEFAULT NULL,
    updated_at TIMESTAMP NULL DEFAULT NULL
);

#PIMS-14141 | Dev: Sagar
CREATE TABLE partner_portal_url_mapping (
    id int(10) unsigned NOT NULL AUTO_INCREMENT,
    partner_id int(10) unsigned NOT NULL,
    portal_id int(10) unsigned DEFAULT NULL,
    portal_url text NOT NULL,
    status tinyint(1) DEFAULT '1' COMMENT '1 = Active, 0 = Inactive',
    created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY partner_id (partner_id),
    KEY fk_portal_id (portal_id),
    CONSTRAINT fk_portal_id FOREIGN KEY (portal_id) REFERENCES master_portal_list (id) ON DELETE CASCADE,
    CONSTRAINT partner_portal_url_mapping_ibfk_1 FOREIGN KEY (partner_id) REFERENCES users (id) ON DELETE CASCADE
  ) ENGINE=InnoDB DEFAULT CHARSET=latin1

#pims-13899 DD
alter table permit_requests add stop_renew_consent enum('0','1') default '0';
alter table user_permit_requests add stop_renew_consent enum('0','1') default '0';

#PIMS-14141 | Dev: Sagar
CREATE TABLE `da_dashboards` (
   `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
   `name` varchar(255) NOT NULL,
   `embedded_id` varchar(255) NOT NULL,
   `rls` text NOT NULL,
   `ui_config` text,
   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   `deleted_at` timestamp NULL DEFAULT NULL,
   PRIMARY KEY (`id`)
 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `partner_mapped_dashboard_list` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `partner_id` int(10) unsigned NOT NULL,
   `dashboard_id` int(11) unsigned NOT NULL,
   `membership_plan_id` int(11) unsigned DEFAULT NULL,
   `created_at` datetime DEFAULT NULL,
   `updated_at` datetime DEFAULT NULL,
   `user_type` tinyint(3) unsigned DEFAULT '3' COMMENT '1=Super Admin, 3=Partner, 4=Subordinate, 8=Clerk, 10=Business, 11=Cashier, 12=RM',
   PRIMARY KEY (`id`),
   KEY `fk_partner` (`partner_id`),
   KEY `fk_dashboard` (`dashboard_id`),
   KEY `fk_membership_plan` (`membership_plan_id`),
   CONSTRAINT `fk_dashboard` FOREIGN KEY (`dashboard_id`) REFERENCES `da_dashboards` (`id`) ON DELETE CASCADE,
   CONSTRAINT `fk_membership_plan` FOREIGN KEY (`membership_plan_id`) REFERENCES `membership_plans` (`id`) ON DELETE SET NULL,
   CONSTRAINT `fk_partner` FOREIGN KEY (`partner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;


# PIMS-14029 - Add below key for hybrid flow

ALTER TABLE `ticket_details` 
ADD COLUMN `is_overnight_enabled` TINYINT(1) NULL DEFAULT 0,
ADD COLUMN `is_fast_track_enabled` TINYINT(1) NULL DEFAULT 0;

ALTER TABLE `ticket_extends` 
ADD COLUMN `is_overnight_enabled` TINYINT(1) NULL DEFAULT 0,
ADD COLUMN `is_fast_track_enabled` TINYINT(1) NULL DEFAULT 0;

#PIMS-13713 #dd
 CREATE TABLE `ticket_releases` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_id` int(11) DEFAULT NULL,
  `facility_id` int(11) DEFAULT NULL,
  `partner_id` int(11) DEFAULT NULL,
  `anet_transaction_id` int(11) DEFAULT NULL,
  `ticket_number` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `length` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `total` double(10,2) DEFAULT NULL,
  `grand_total` double(10,2) DEFAULT NULL,
  `checkin_time` datetime DEFAULT NULL,
  `checkout_time` datetime DEFAULT NULL,
  `tax_fee` double(10,2) DEFAULT NULL,
  `processing_fee` double(10,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `discount_amount` double(10,2) DEFAULT '0.00',
  `comment` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `parking_amounts` double(10,2) DEFAULT '0.00',
  `additional_fee` double(10,2) DEFAULT '0.00',
  `surcharge_fee` double(10,2) DEFAULT '0.00',
  `oversize_fee` double(10,2) DEFAULT '0.00',
  PRIMARY KEY (`id`)
)

ALTER TABLE overstay_tickets 
ADD COLUMN surcharge_fee DOUBLE(10,2) DEFAULT 0.00;


ALTER TABLE overstay_tickets 
ADD COLUMN additional_fee DOUBLE(10,2) DEFAULT 0.00;

#dd pims-13713
alter table facilities add is_refund_day enum('0','1') default '0';



### PIMS-14556 - 14-07-2025
ALTER TABLE permit_rate_descriptions
ADD COLUMN permit_frequency_unit ENUM('day', 'month', 'quarterly' , 'year') DEFAULT null
AFTER permit_frequency;

update permit_rate_descriptions set permit_frequency_unit = 'month'  where permit_frequency < 13 and permit_frequency_unit is null;
### END  - PIMS-14556 - 14-07-2025 ########

#PIMS-122776 || Dev:Sagar || Date: 10/07/2025
ALTER TABLE `tickets`
ADD COLUMN `is_third_party` TINYINT(1) NOT NULL DEFAULT 0 
COMMENT '0 => Default, 1 => Ticket created via third-party system';


UPDATE permit_rate_descriptions 
SET permit_frequency_unit = 'day' , permit_frequency = 7  
WHERE permit_frequency = 13 
  AND permit_frequency_unit IS NULL;


CREATE TABLE `user_fast_track_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,  
  `partner_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `card_last_four` varchar(16) DEFAULT NULL,
  `card_type` varchar(50) DEFAULT NULL,
  `card_name` varchar(50) DEFAULT NULL,
  `expiry` varchar(10) DEFAULT NULL,
  `token` text,  
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_fast_track_user_details_user_id` (`user_id`),
  KEY `idx_fast_track_user_details_partner_id` (`partner_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;


CREATE TABLE `fast_track_vehicle_mapping` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `permit_vehicle_id` int(11) NOT NULL,
  `fast_track_id` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_fast_track_mapping_request` (`permit_request_id`),
  KEY `idx_fast_track_mapping_vehicle` (`permit_vehicle_id`),
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;  


ALTER TABLE `ticket_details`
ADD COLUMN `is_overnight_enabled` tinyint(1) DEFAULT '0';

ALTER TABLE `ticket_details`
ADD COLUMN `is_fast_track_enabled` tinyint(1) DEFAULT '0';

COMMENT '0 => Default, 1 => Ticket created via third-party system';

CREATE TABLE `ticket_details` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_id` int(10) unsigned NOT NULL,
  `qr_code` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `ref_number` varchar(255) DEFAULT NULL COMMENT 'used when sync tickets offline to cloud',
  `is_overnight_enabled` tinyint(1) DEFAULT '0',
  `is_fast_track_enabled` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8507 DEFAULT CHARSET=latin1;

ALTER TABLE `third_party_parkchirp`
ADD COLUMN `phone`	varchar(32)	NULL DEFAULT NULL AFTER parkerEmail;