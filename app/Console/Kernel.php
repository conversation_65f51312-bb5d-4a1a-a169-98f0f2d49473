<?php

namespace App\Console;

use App\Console\Commands\GenerateInventoryForNextWeek;
use App\Console\Commands\GenerateInventoryForNextWeekFacilityWise;

use App\Console\Commands\UpdateExistingInventory;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Artisan;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [

        //for invetory 

        Commands\Inventory\UpdateInventory::class,
        Commands\Inventory\UpdateAvailability::class,
        //        Commands\Reservation\ReservationBulkCancellation::class,

        // Clean and update scripts
        Commands\Clean\CleanHoursOfOperation::class,
        Commands\Clean\MissingImages::class,
        Commands\Clean\CleanImageDirectories::class,

        // Generate Inventory
        GenerateInventoryForNextWeek::class,
        GenerateInventoryForNextWeekFacilityWise::class,
        UpdateExistingInventory::class,

        Commands\Reservation\Email::class,
        Commands\Reservation\EmailCancellation::class,

        Commands\Notification\Citation\ProcessCitation::class,
        Commands\ParkChrip\GetData::class,

        Commands\Cron\RequestDemo::class,
        Commands\Cron\PartnerRegistrationEmail::class,
        Commands\Cron\UserRegistrationEmail::class,
        Commands\Cron\DisableUserAfterTrialExpiration::class,
        Commands\Cron\ContactUs::class,
        Commands\Cron\MembershipExpiredWithEmail::class,
        Commands\Cron\MembershipPaymentEmail::class,
        Commands\Cron\MembershipReminderEmail::class,
        Commands\Cron\PartnerMultiplePlanRegistrationEmail::class,
        Commands\Cron\PromotionExpired::class,
        Commands\Cron\Autopay::class,
        Commands\Cron\MembershipCancellationEmail::class,
        Commands\Cron\PaymentCheckin::class,
        Commands\Cron\ConfirmCheckin::class,
        Commands\Cron\TouchlessPaymentCheckin::class,
        Commands\Cron\TouchlessCheckin::class,
        Commands\Cron\TouchlessPrepaidCheckin::class,
        //Commands\Cron\NotifyPrepaidReservationCheckout::class,
        //Commands\Cron\NotifyPrepaidReservationCheckin::class,
        Commands\MonthlyPermitUser\Email::class,
        Commands\UserPass\Email::class,
        Commands\Cron\TouchlessAtlantaPaymentCheckin::class,
        Commands\Cron\TouchlessAtlantaCheckin::class,
        Commands\Cron\TouchlessAtlantaPrepaidCheckin::class,
        Commands\Cron\TouchlessPermitPaymentCheckin::class,
        Commands\Cron\TouchlessPermitCheckin::class,
        Commands\Cron\TouchlessPermitPrepaidCheckin::class,
        Commands\Cron\PartnerRevenueBalance::class,
        Commands\Cron\PartnerEmailInvoice::class,
        Commands\Cron\TouchlessAutogatePaymentCheckin::class,
        Commands\Cron\PartnerAutogateRevenueBalance::class,
        Commands\Cron\PartnerAutogateEmailInvoice::class,
        Commands\Cron\MapcoReservationEmail::class,
        Commands\Cron\MapcoPrepaidCheckin::class,
        Commands\Cron\MapcoCancelReservationEmail::class,
        Commands\Cron\AtlantaCheckinCheckout::class,
        Commands\Cron\MapcoCheckinCheckout::class,
        Commands\Cron\MapcoOtpEmail::class,
        Commands\Cron\MapcoRevenueBalance::class,
        Commands\Cron\MapcoEmailInvoice::class,
        Commands\Cron\MapcoReservationEmailCheckin::class,
        Commands\Cron\NotifyKeepAlive::class,
        Commands\Cron\SendEmailCheckin::class,
        Commands\Cron\SendEmailUserpassDetails::class,
        Commands\Cron\GatedUngatedUserRegistrationEmail::class,
        Commands\Cron\UserPermitRequestConfirmationEmail::class,
        //Are
        Commands\Are\UserPermitPaymentCancelEmail::class,
        Commands\Are\CheckinEmailSend::class,
        Commands\Are\WaitingListInvitationEmail::class,
        Commands\Are\WaitingListConfirmationEmail::class,
        Commands\Are\UserPassesAdminEmail::class,
        Commands\Are\VehicleDetailsEmail::class,
        Commands\Are\PermitBookingEmailAdmin::class,
        Commands\Are\PermitCancelEmail::class,
        Commands\Are\UserPermitCancelEmail::class,
        Commands\Are\UserpassDeleteEmail::class,
        Commands\Diamond\PermitBookingEmail::class,
        Commands\Diamond\DiamondAutogatePaymentCheckin::class,
        Commands\Diamond\WaitingListInvitationEmail::class,
        Commands\Diamond\WaitingListConfirmationEmail::class,
        Commands\Diamond\PermitBookingCancelEmail::class,
        Commands\Cron\EventExpired::class,
        Commands\Business\BusinessClerkEmail::class,
        Commands\Business\BusinessEmail::class,
        Commands\Business\BusinessAccountEmail::class,
        Commands\Are\PermitAutoRenewEmail::class,

        Commands\Pave\PaveUngatePaymentCheckin::class,
        Commands\Pave\PaveGatedConfirmCheckin::class,
        Commands\Pave\PaveGatedCheckinPayment::class,
        Commands\Pave\PaveTicketAutoPayPayment::class,

        Commands\Cron\NotifyCronPaymentStatus::class,      #Kuldeep 22-11-2024
        Commands\Cron\NotifyCronTicketPaymentStatus::class,      #Kuldeep 11-12-2024


        Commands\Townsend\CashierShiftReport::class,        // Vijay -20-06-2023
        Commands\Townsend\ValidationReport::class,        // Vijay -03-07-2023
        Commands\Townsend\CheckinPayment::class,
        Commands\Townsend\ConfirmCheckin::class,
        Commands\Cron\AttendantAccountEmail::class,
        // WLA
        Commands\worldpord\dailyRevenueReport::class,        // Vijay 26-12-2023
        Commands\worldpord\openTicket::class,               // Vijay 05-12-2023
        Commands\Reports\RevenueReportPermit::class,               // Vijay 03-08-2024

        Commands\Customer\CustomerCheckinPushNotification::class,
        Commands\Customer\CustomerCheckoutPushNotification::class,
        Commands\Customer\CustomerCheckinCheckoutEmail::class,

        Commands\Pave\SendPushNotificationCheckoutReminder::class,
        Commands\Pave\SubordinateAccountEmail::class,
        Commands\Cron\GateDown::class,
        Commands\Pave\SendPushNotificationStartReservationReminder::class,
        Commands\Pave\SendPushNotificationMakeReservation::class,
        Commands\Pave\SendPushNotificationEndReservationReminder::class,

        // Intrapark Auto Pay Cron
        Commands\IntraPark\IntraParkAutoPayPayment::class,
        Commands\IntraPark\PermitAutoRenewIntraPark::class,
        //Classic
        Commands\Classic\ConfirmCheckin::class,

        //spothero Reservation cron
        Commands\Cron\SpotheroReservation::class,
        // Cashier account creation
        Commands\cashier\CashierAccountEmail::class,
        Commands\Business\BusinessEmailQRCode::class,

        Commands\Cron\ReadLicensePlate::class,
        Commands\MQConsumerCommand::class,
        # United Auto pay Cron Datacap Kernel File Path  
        Commands\United\UnitedTicketAutoPayPayment::class,
        # United Auto pay Cron Heartland  Kernel File Path  
        Commands\United\UnitedTicketAutoPayHeartland::class,
        Commands\United\PermitAutoRenewUnited::class,
        Commands\United\PermitTenDaysAutoRenewUnited::class,
        Commands\United\PermitAutoRenewUnitedHL::class,
        Commands\Cron\PavDailyTransactionsEmail::class,   // Deployed : 06-06-2024

        //spothero Reservation cron
        //Commands\Cron\UnitedSpotheroReservation::class,
        // mapco sms reminder
        Commands\Reservation\SmsReminder::class,
        // USM Permit Auto Renew
        Commands\Usm\PermitAutoRenewUsm::class,
        // USM Permit Diff Charge
        Commands\Usm\PermitDiffernceAmountChargeUsm::class,

        // USM Permit Auto Renew Yearly
        Commands\Usm\PermitYearlyAutoRenewUsm::class,

        // USM Permit Auto Renew New
        Commands\Usm\PermitAutoRenewUsmCron::class,
        // Pci auto pay new facility
        Commands\Pci\PciTicketAutoPayPayment::class,

        // Yenkee Permit Auto Renew
        Commands\Yenkee\PermitAutoRenewYenkee::class,
        // Kstreet Permit Auto Renew
        Commands\Kstreet\PermitAutoRenewKstreet::class,
        // Pci auto pay new facility woodman
        Commands\Pci\WoodmanTicketAutoPayPaymentHeartland::class,

        //WoodMan
        Commands\Woodman\Notifications::class,
        Commands\Woodman\ExtendTimeNotification::class,
        Commands\Cron\UpdatePasswordSendMail::class,
        Commands\Woodman\WoodmanCheckinCheckout::class,
        //Pave Permit Auto Renew
        Commands\Pave\PermitAutoRenewPave::class,
        //Colonial Permit Auto Renew
        Commands\Colonial\PermitAutoRenewColonial::class,

        // Yankeen CityParking Permit Renew
        Commands\Yenkee\NotifyRenewPermit::class,

        // Yankeen CityParking payment due user list
        Commands\Yenkee\PaymentDueExcelsheet::class,

        // Yenkee renew and not renew permit
        Commands\Yenkee\PermitRenewNotRenewList::class,

        // Hub Zeag
        Commands\HubZeag\HubCheckin::class,
        Commands\HubZeag\HubReservation::class,
        Commands\HubZeag\HubReservationDelete::class,
        Commands\HubZeag\HubFailedNotification::class,
        // Void Worldport 20 days older ticket 
        Commands\Pci\WorldPortVoidTicket::class,


        //Colonial Dock 79
        Commands\Colonial\ColonialCheckinCheckout::class,
        Commands\yankee\dailyRevenueReport::class,        // Deployed : 03-06-2024
        //USM Auto Pay Cron Datacap
        Commands\Usm\UsmTicketAutoPayPayment::class,
        Commands\Usm\UsmTicketAutoPayPaymentHeartland::class,
        Commands\Usm\PermitPaymentReminderMailUsm::class,
        Commands\Usm\PermitCancelMail::class,
        #KT : 24-01-2025
        Commands\Usm\MassEmailPermit::class,
        Commands\Usm\PermitInviteMail::class,

        // Notify Email, Alka
        Commands\Notification\NotifyEmail::class,

        Commands\Demo\EmailCitation::class,

        Commands\Diamond\MarkCitation::class,

        // City Parking Inc Cron Heartland
        Commands\CityParkingInc\CityParkingIncTicketAutoPayPaymentHeartland::class,
        // City Parking Inc Cron Datacap
        Commands\CityParkingInc\CityParkingIncTicketAutoPayPayment::class,
        // Clerk Capping Renew
        Commands\CityParkingInc\ClerkCappingRenew::class,
        // USM Permit Auto Renew for weekly
        Commands\Usm\PermitWeeklyAutoRenewUsm::class,
        // USM Permit Auto Renew for permit semeseter/tenure wise
        Commands\Usm\PermitTenureAutoRenewUsm::class,
        //USM Auto checkout
        Commands\Usm\AutoCheckout::class,
        // Import permit data from SFT CSV file PIMS--12258 DD 
        Commands\Manlo\ImportPermitTypeSft::class,
        // Import permit bulk upload from SFT CSV file PIMS--12704 DD 
        Commands\Cron\PermitBulkImport::class,
        //command for update business role subordinate capacity for permit expired
        Commands\Are\PermitAutoExpireBusinessRole::class,

        // close all open ticket via lpr PIMS--12870 DD 
        Commands\Cron\CloseLprOpenTicket::class,

        // Reservation Roc Post charge
        Commands\Reservation\ReservationAutoPayPayment::class,          // Vijay For Manual Charge;

        Commands\Reservation\ReservationAutoPayPaymentHeartland::class,
        Commands\ReportShuduler\DailyReport::class,
        Commands\ReportShuduler\Promocode::class,
        Commands\Townsend\ClearLicensePlate::class,
        Commands\Are\DiamondPermitRenew::class,

        Commands\Cron\sendUserPassesEmail::class,
        Commands\Manlo\PermitAutoRenewManlo::class,
        Commands\CityParkingInc\PermitAutoRenewCityParking::class,
        Commands\PreferredParking\PreferredParkingTicketAutoPay::class,

        # Evolution Auto pay Cron Heartland Kernel File Path
        Commands\Evolution\EvolutionTicketAutoPayHeartland::class,
        # Evolution Auto pay Cron Heartland Kernel File Path Manual Charge
        Commands\Evolution\EvolutionTicketAutoPayManualChargeHeartland::class,
        # Evolution Auto Renew Cron Heartland Kernel File Path
        Commands\Evolution\EvolutionPermitAutoRenew::class,
        // Evolution Permit Auto Renew With Rabbit Mq
        Commands\Evolution\EvolutionPermitAutoRenewMq::class,
        # PEDemo Auto pay Cron Heartland Kernel File Path 
        Commands\PEDemo\PeDemoTicketAutoPayHeartland::class,
        # Manlo Auto pay Cron Heartland  Kernel File Path  
        Commands\Manlo\TicketAutoPayHeartland::class,
        // Daily report email changes

        Commands\Cron\TransientAlertController::class,      #Kuldeep 29-01-2025 Deployment
        // Daily report email changes
        Commands\DailyRevenueReport\KittredgeStreet::class,
        Commands\MetroDailyReport\MetroReport::class,

        //dev:sagar for send single notification
        Commands\Woodman\UngatedLPRNotification::class,

        //dev:vikrant to export citation for menlo
        Commands\Manlo\ExportCitation::class,

        Commands\SweepReport\PartnerWiseSweepReport::class,

        // ParkPayRide Transient Payment Cron
        Commands\ParkPayRide\ParkPayRideTicketAutoPayPayment::class,
        // ParkPayRide Permit Renewal Payment Cron
        Commands\ParkPayRide\PermitAutoRenewParkPayRide::class,
        # Park2Visit Auto pay Cron Heartland  Kernel File Path  
        Commands\Park2Visit\Park2VisitTicketAutoPayHeartland::class,
        # Park2Visit Auto pay Cron Datacap Kernel File Path  
        Commands\Park2Visit\Park2VisitTicketAutoPayDc::class,
        Commands\RavpassDailyReport\RavpassDailyReport::class,
        // Colonial Ticket Charge Cron
        Commands\Colonial\ColonialParkingTicketAutoPayPayment::class,

        // VP : PIMS - 14602 Admin Aletrs
        Commands\Alerts\TransientPaymentAlert::class,
        Commands\Entrata\EntrataApi::class,

        // Payment Reminder 
        Commands\Permit\SendPaymentReminderCommand::class,

    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        //Ticket Renew
        $schedule->command('pave:ticket-autopay')->everyMinute();
        $schedule->command('cityparkinginc:ticket-autopay')->everyMinute();
        $schedule->command('event-expired')->cron('0 8 * * *');
        $schedule->command('united:ten-days-permit-renew')->daily();
        // $schedule->command('notification:extent-time-reminder 215900')->cron('* * * * *');

        // $schedule->command('pave:push-notification-before-checkout-reminder')->everyMinute();

        // $schedule->command('notification:extent-time-reminder 215900')->cron('* * * * *');

        // $schedule->command('notification:extent-time-reminder 54682')->cron('* * * * *');

        // $schedule->command('notification:extent-time-reminder 169163')->cron('* * * * *');

        // $schedule->command('notification:extent-time-reminder 91860')->cron('* * * * *');
        // $schedule->command('preferredparking:ticket-autopay')->cron('* * * * *');
    }

    // Let us queue on specific queues
    public function queueOn($command, $queue = 'default', array $parameters = [])
    {
        $this->app['Illuminate\Contracts\Queue\Queue']->push(
            'Illuminate\Foundation\Console\QueuedJob',
            [$command, $parameters],
            $queue
        );
    }
}
