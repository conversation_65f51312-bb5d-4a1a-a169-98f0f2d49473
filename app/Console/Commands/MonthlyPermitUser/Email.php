<?php

namespace App\Console\Commands\MonthlyPermitUser;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;


/**
 * Emails reservation stub to user
 */
class Email extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monthlypermituser:email {permit_request_id} {type_id}';

 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email success to monthly permit user.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/monthlypermituser')->createLogger('monthlypermituser');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $monthly_request_id = $this->argument('permit_request_id');
        $type_id = $this->argument('type_id');
        $monthlyRequest = PermitRequest::where('id',$monthly_request_id)
                            ->first();

        if (!$monthlyRequest) {
            throw new NotFoundException('No user with that ID.');
        }

        $monthlyRequest['desired_start_date'] = $this->getDaySufixFormat($monthlyRequest->desired_start_date);
        $monthlyRequest['desired_end_date'] = $this->getDaySufixFormat($monthlyRequest->desired_end_date);
        $brand_setting = BrandSetting::where('user_id', $monthlyRequest->partner_id)->first();
        
        if($type_id == ''){
            $view='monthlypermituser.email';
            $view_text=$view.'-plain-text';
            if(!View::exists($view_text))
            {
                $view_text=$view;
            } 
             try{ 
                $this->log->info("monthly permit user email sending: $monthlyRequest->email");
                 Mail::send(
                    ['text'=>$view_text,'html'=>$view],
                    ['data' => $monthlyRequest, 'brand_setting' => $brand_setting],
                    function ($message) use ($monthlyRequest) {
                        $message->to($monthlyRequest->email)->subject("Your permit has been confirmed");
                        $message->from(config('parkengage.default_sender_email'));
                    }
                 );             
                $this->log->info("email sent: $monthlyRequest->email");

                $accountSid = env('TWILIO_ACCOUNT_SID');
                $authToken  = env('TWILIO_AUTH_TOKEN');
                $client = new Client($accountSid, $authToken);
                $desiredStartDate = date("d F, Y", strtotime($monthlyRequest->desired_start_date));
                $desiredEndDate = date("d F, Y", strtotime($monthlyRequest->desired_end_date));
                $permitRate = $monthlyRequest->permit_rate;
                $facilityName = ucwords($monthlyRequest->facility->full_name);
                $this->log->info("twilio sms about to send : $monthlyRequest->phone");
                try
                {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                    // the number you'd like to send the message to
                        $monthlyRequest->phone,
                   array(
                         // A Twilio phone number you purchased at twilio.com/console
                         'from' => env('TWILIO_PHONE'),
                         // the body of the text message you'd like to send
                         //'body' => "Fine"
                         'body' => 
            "Thank you for booking monthly permit with $facilityName. Your permit has been confirmed.\nPermit # : $monthlyRequest->account_number \nStart Date : $desiredStartDate \nEnd Date : $desiredEndDate \nAmount Charged : $$permitRate"
                     )
                 );
                }catch (RestException $e)
                {
                    //throw new ApiGenericException($e->getMessage());
                    //return "success";
                  $this->log->error("twilio sms sent : $e");
                }
            }catch (\Exception $e)
            {
                $errorMessage=array();
                $errorMessage['message']=$e;
                $this->log->error('Issue in email sending:',$errorMessage);
            }
        }else{
            $view='monthlypermituser.commuter-email';
            $view_text=$view.'-plain-text';
            if(!View::exists($view_text))
            {
                $view_text=$view;
            } 
             try{ 
                $this->log->info("commuter monthly permit user email sending: $monthlyRequest->email");
                 Mail::send(
                    ['text'=>$view_text,'html'=>$view],
                    ['data' => $monthlyRequest, 'brand_setting' => $brand_setting],
                    function ($message) use ($monthlyRequest) {
                        $message->to($monthlyRequest->email)->subject("Your permit has been confirmed");
                        $message->from(config('parkengage.default_sender_email'));
                    }
                 );             
                $this->log->info("email sent: $monthlyRequest->email");
            }catch (\Exception $e)
            {
                $errorMessage=array();
                $errorMessage['message']=$e;
                $this->log->error('Issue in email sending:',$errorMessage);
            }
        }
    }

    public function getDaySufixFormat($date){
        $day = date('d', strtotime($date));
        $monthYear = date('F, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
          $second_last_digit = substr($number, -2, 1);
          $suffix = 'th';
          if ($second_last_digit != '1')
          {
            switch ($last_digit)
            {
              case '1':
                $suffix = 'st';
                break;
              case '2':
                $suffix = 'nd';
                break;
              case '3':
                $suffix = 'rd';
                break;
              default:
                break;
            }
          }
          if ((string) $number === '1') $suffix = 'st';
          return $number.$suffix . ' '.$monthYear;
    }
}
