<?php

namespace App\Console\Commands\Demo;

use App\Exceptions\NotFoundException;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\TicketCitationAppeal;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use Illuminate\Support\Facades\Mail;

class EmailCitation extends Command
{
    protected $log;
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'citation-appeal-status:email
                            {citation_id}
                            {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Citation Notifications';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/citation/email')->createLogger('status_email');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->log->info('Under handle function');

            $citation_id = $this->argument('citation_id');
            $email = $this->argument('email');
            $this->log->info("Email for citation_id: {$citation_id} and Email: {$email}");

            $citationAppeal = TicketCitationAppeal::with(['citation', 'citation.facility', 'citation.user', 'citation.permit','citation.ticketCitationInfraction.ticketCitationInfractionReason'])->where('citation_id', $citation_id)->first();
            if(!$citationAppeal){
              throw new NotFoundException('No Appeal Found.');
            }

            $facility = Facility::find($citationAppeal->citation->facility_id);
            if (!$facility) {
                throw new NotFoundException('No facility with that ID.');
            }

            $brandSetting = FacilityBrandSetting::where('facility_id', $facility->id)->first();
    
            if ($brandSetting) {
                $facility->facility_logo_id = $brandSetting->id;
            } else {
                $settings = BrandSetting::where('user_id', $facility->owner_id)->first();
                $facility->logo_id = $settings->id;
            }

            $appealStatus = $citationAppeal->appeal_message;
            $citationNumber = $citationAppeal->citation->citation_number;
            $this->log->info('Citation Logo id '. $facility->logo_id ." Facility Id: {$facility->id}, Appleal Status: {$appealStatus} , Citation No: $citationNumber");
            
            Mail::send(
                "demo.citation-appeal", ['facility'=> $facility, 'citationAppeal' => $citationAppeal], function ($message) use($email, $appealStatus, $citationNumber) {
                    $message->to($email);
                    $message->subject("Appeal {$appealStatus} against Citation Number: {$citationNumber}");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Mail sent to {$email}"); 

        } catch (\Throwable $e) {
            $errorMessage = array();
            $errorMessage['message'] = $e->getMessage();
            $errorMessage['line number'] = $e->getLine();
            $errorMessage['file'] = $e->getFile();
            $this->log->info('Issue in notify email sending' . json_encode($errorMessage));
            $this->log->info("Queue ended"); 
        }
        
    }
}
