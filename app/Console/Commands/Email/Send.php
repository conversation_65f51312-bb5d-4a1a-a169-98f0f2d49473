<?php

namespace App\Console\Commands\Email;

use Exception;

use App\Services\Mailers\UserMailer;
use App\Services\LoggerFactory;

use Illuminate\Console\Command;

abstract class Send extends Command
{
    protected $email;
    protected $subject;
    protected $content;
    protected $logs;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/emails')->createLogger('sent_email');
    }

    public function handle()
    {
        if (!isset($this->content) || !isset($this->subject)) {
            throw new Exception("You must set content and subject on the child class.");
        }

        print_r("\nBegin sending:\n");

        if (isset($this->email)) {
            return $this->send($this->email);
        }

        if (isset($this->emails) && is_array($this->emails)) {
            foreach ($this->emails as $email) {
                $this->send($email);
            }
            return;
        }

        print_r('You must set either $this->emails or $this->email to send emails.');
    }

    protected function send($email)
    {
        (new UserMailer())->queueMailTo($email, $this->subject, ['body' => $this->content], 'email.generic');
        $this->log->info("Email with subject {$this->subject} queued to {$email}.");
        print_r("Sent email to to {$email}.\n");
    }
}
