<?php

namespace App\Console\Commands\Email;

use Illuminate\Console\Command;

use League\Csv\Reader;

class ExpirationDateCorrection extends Send
{

    protected $signature = 'email:expiration-date-correction
                            { --file= : File to read emails to send to. }';

    protected $description = 'Send correction for expiration date emails sent to wrong email addresses.';

    public function handle()
    {
        $name = config('icon.name');
        $this->subject = "Re: $name Email";
        $this->content = "Please disregard the email you received that indicated your credit card on file was expired.  This email was directed to you in error and was a mistake.  We apologize for any inconvenience.";

        // Read in emails from file passed in args
        $csv = Reader::createFromPath($this->option('file'));
        $this->emails = iterator_to_array($csv->fetchColumn(0), false);

        parent::handle();
    }
}
