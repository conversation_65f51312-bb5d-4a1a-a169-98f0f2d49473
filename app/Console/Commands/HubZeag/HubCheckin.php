<?php

namespace App\Console\Commands\HubZeag;

use App\Classes\CommonFunctions;
use App\Models\Reservation;
use App\Models\Ticket;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Illuminate\Console\Command;

class <PERSON>b<PERSON><PERSON>ckin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hubzeag:checkin_data {--ticketech_code= : If want to run for specific reservation.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the list of checkin data';

    protected $log;
    protected $method;
    protected $partnerId;
    protected $facilityId;


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/roc/hubapi/checkin')->createLogger('chechein_api');
        $this->method = "GET";
        $this->partnerId = config('parkengage.PARTNER_MAPCO');
        $this->facilityId = config('parkengage.ROC_FACILITY');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->log->info("Hub Zeag Checkin Command Started for Partner ID: {$this->partnerId} and Facility ID: {$this->facilityId}");
        $query = Reservation::query()
            // ->whereIn('is_hub_zeag', [2, 3, 4]) // Use integers if the column stores integers, otherwise keep as strings.
            ->where('facility_id', $this->facilityId)     // Assuming 'facility_id' is an integer.
            ->where('partner_id', $this->partnerId);     // Assuming 'partner_id' is an integer.

        // Check if a specific 'ticketech_code' is provided as an option
        if ($this->option('ticketech_code')) {
            $query->where('ticketech_code', $this->option('ticketech_code'));
        } else {
            $query->whereIn('is_ticket', [0, 1]);   // Same as above, use integers or strings depending on the column type.
        }

        $current_time = date('Y-m-d H:i:s');
        $query->where('start_timestamp', '<', $current_time);

        // Ensure that the 'cancelled_at' column is null
        // $reservations = $query->whereNull('cancelled_at')->get();

        $reservations = $query->get();
        $this->log->info("Reservation Count: " . $reservations->count());
        // dd($reservations->count());

        $base_url = config('parkengage.HUB_ZEAG.base_url');


        // Set the public and private keys
        $public_key = config('parkengage.HUB_ZEAG.public_key');
        $private_key = config('parkengage.HUB_ZEAG.private_key');

        // Capture the current date/time for the request in UTC
        $messageDate = Carbon::now('Asia/Kolkata')->format('Y-m-d\TH:i:sP');
        // dd($messageDate);

        // Create the message representation, composed of the Verb, resource, content hash (empty), and request date/time
        $representation = "$this->method|/api/reservation||" . $messageDate;

        // Create the hashed message representation using HMAC SHA1 and encode it in base64
        $hmacSHA1 = base64_encode(hash_hmac('sha1', $representation, $private_key, true));

        // Set the headers
        $headers = [
            'x-message-date: ' . $messageDate,
            'x-ctr-signature: CTR ' . $public_key . ':' . $hmacSHA1,
            'Content-Type: application/json'
        ];

        $this->log->info("Configruation for Api done");

        $input = [];

        // Actuall Implementaion
        if (count($reservations) > 0) {
            $this->log->info("Processing check-in/out data for " . count($reservations) . " reservations");

            foreach ($reservations as $index => $reservation) {
                $this->logReservationDetails($reservation, $index);

                $url = "{$base_url}/api/reservation?bookingid={$reservation->ticketech_code}";
                $responses = CommonFunctions::makeRequest($url, $this->method, $headers);
                $this->log->info("Hub Zeag Response: " . json_encode($responses));
                // dd($responses);

                if ($this->isNoContentResponse($responses)) {
                    $this->log->info("No check in/Out found");
                    continue;
                }

                $this->processReservationResponses($responses, $reservation);
            }
        } else {
            $this->log->info("No reservations found for check-in/out processing.");
        }
    }


    /**
     * Log reservation details.
     */
    private function logReservationDetails($reservation, $index)
    {
        $this->log->info("Reservation ID: {$reservation->id}, Ticket Code: {$reservation->ticketech_code}, Status: {$reservation->is_ticket}, Run Count: " . ($index + 1));
    }

    /**
     * Check if the response indicates no content.
     */
    private function isNoContentResponse($responses)
    {
        return isset($responses['http_code']) && $responses['http_code'] == 204;
    }

    /**
     * Process each response for the reservation.
     */
    private function processReservationResponses($responses, $reservation)
    {
        $checkin = is_array($responses) ? (object)$responses : $responses;
        $this->log->info("Reservation Found: Booking ID {$checkin->BookingId}, Ticket Code {$reservation->ticketech_code}");

        if (!$reservation) {
            $this->log->info("No matching reservation found for Booking ID: {$checkin->BookingId}");
        }

        $ticketStatus = $this->getTicketStatus($checkin->ReservationStatus);
        $this->log->info("Hub Booking ID: {$checkin->BookingId}, Status: {$checkin->ReservationStatus}, Ticket Status: {$ticketStatus}");

        $this->processTicketStatus($reservation, $checkin, $ticketStatus);
    }

    /**
     * Get ticket status based on reservation status.
     */
    private function getTicketStatus($reservationStatus)
    {
        switch ($reservationStatus) {
            case 'Arrived':
                return 1;
            case 'Exited':
                return 2;
            default:
                return 0;
        }
    }


    /**
     * Process ticket status for the reservation.
     */
    private function processTicketStatus($reservation, $checkin, $ticketStatus)
    {
        $existingTicket = Ticket::where('reservation_id', $reservation->id)->first();
        if ($ticketStatus === 2) {
            $this->handleCheckout($reservation, $checkin, $existingTicket);
        } elseif ($ticketStatus === 1) {
            $this->handleCheckin($reservation, $checkin, $existingTicket);
        } else if ($ticketStatus === 0) {
            $this->log->info("No action required for this reservation.");
            $reservation->update(['checkin_status' => $checkin->ReservationStatus ?? NULL]);
        }
        // dd($existingTicket, $ticketStatus);
    }

    /**
     * Handle checkout logic.
     */
    private function handleCheckout($reservation, $checkin, $existingTicket)
    {
        $this->log->info("Processing checkout for Reservation ID: {$reservation->id}");

        if ($existingTicket) {
            $existingTicket->update([
                'checkout_time' => $this->utcToNewYork($checkin->ExitDateTime),
                'checkin_time' => $this->utcToNewYork($checkin->EntryDateTime),
                'is_checkout' => 1,
                'checkout_mode' => 9,
            ]);
            $reservation->update(['is_ticket' => 2, 'checkin_status' => $checkin->ReservationStatus ?? NULL]);
            $this->log->info("Checkout updated for Ticket: {$existingTicket->ticket_number}");
        } else {
            $this->log->info("No ticket found for Reservation ID: {$reservation->id}");
            $this->handleCheckin($reservation, $checkin, $existingTicket);
        }
    }

    /**
     * Handle check-in logic.
     */
    private function handleCheckin($reservation, $checkin, $existingTicket)
    {
        $this->log->info("Processing check-in for Reservation ID: {$reservation->id}");

        if ($existingTicket) {
            $this->log->info("Check-in already exists with Ticket Number: {$existingTicket->ticket_number}");
            return;
        }

        $ticketNumber = $this->checkTicketNumber();
        $input = $this->prepareCheckinData($reservation, $checkin, $ticketNumber);
        Ticket::create($input);
        $reservation->update(['is_ticket' => 1, 'checkin_status' => $checkin->ReservationStatus ?? NULL]);
        $this->log->info("Check-in created with Ticket Number: {$ticketNumber}, Booking ID: {$checkin->BookingId} and Created Data: " . json_encode($input));
    }

    /**
     * Prepare check-in data.
     */
    private function prepareCheckinData($reservation, $checkin, $ticketNumber)
    {
        $startTime = Carbon::parse($reservation->start_timestamp); // Ensure it's a Carbon instance
        $endTime = $startTime->copy()->addHours($reservation->length); // Use copy() to avoid modifying $startTime
        // dd($this->utcToNewYork($checkin->EntryDateTime), $checkin->EntryDateTime, $checkin->ExitDateTime, $checkin->BookingId);

        return [
            'user_id' => $reservation->user_id,
            'facility_id' => $reservation->facility_id,
            'is_checkin' => 1,
            'ticket_number' => $ticketNumber,
            'ticket_security_code' => rand(1000, 9999),
            'partner_id' => $reservation->facility->owner_id,
            'reservation_id' => $reservation->id,
            'check_in_datetime' => $startTime->toDateTimeString(),
            'checkout_datetime' => $endTime->toDateTimeString(),
            'estimated_checkout' => $endTime->toDateTimeString(),
            'checkin_time' => $this->utcToNewYork($checkin->EntryDateTime),
            'checkout_time' => $this->utcToNewYork($checkin->ExitDateTime),
            'payment_date' => $endTime->toDateTimeString(),
            'total' => "0.00",
            'grand_total' => "0.00",
            'length' => $reservation->length,
            'license_plate' => $reservation->license_plate,
            'device_type' => "Hub-Zeag",
        ];
    }


    public function utcToNewYork($dateTime)
    {
        if (empty($dateTime)) {
            return null;
        }

        // Ensure the input is clean and validate the format
        $dateTime = trim($dateTime);
        return date('Y-m-d H:i:s', strtotime($dateTime));
    }


    public function checkTicketNumber()
    {
        $ticket = 'PE' . rand(1000, 9999) . rand(100, 999);
        $isExist = Ticket::where('ticket_number', $ticket)->first();
        if ($isExist) {
            return $ticket = $this->checkTicketNumber();
        }
        return $ticket;
    }
}
