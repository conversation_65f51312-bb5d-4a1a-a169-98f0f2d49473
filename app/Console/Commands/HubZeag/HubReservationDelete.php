<?php

namespace App\Console\Commands\HubZeag;

use Carbon\Carbon;
use App\Models\Reservation;
use App\Services\LoggerFactory;
use Illuminate\Console\Command;
use App\Classes\CommonFunctions;
use App\Models\ReservationHistroy;
use App\Exceptions\ApiGenericException;
use Illuminate\Support\Facades\Artisan;

class HubReservationDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hubzeag:reservation_delete {reservation_id}, {method}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel Reservation on Hub Zeag';

    /**
     * Create a new command instance.
     *
     * @return void
     */



    protected $log;
    protected $reservation_id;
    protected $method;
    protected $carbon;

    public function __construct()
    {
        parent::__construct();
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/roc/hubapi/delete')->createLogger('delete_api');
        $this->carbon = new Carbon();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->reservation_id = $this->argument('reservation_id');
        $this->method = $this->argument('method');

        // Set the public and private keys
        $public_key = config('parkengage.HUB_ZEAG.public_key');
        $private_key = config('parkengage.HUB_ZEAG.private_key');

        // Capture the current date/time for the request in UTC
        $messageDate = Carbon::now('Asia/Kolkata')->format('Y-m-d\TH:i:sP');
        // dd($messageDate);

        // Create the message representation, composed of the Verb, resource, content hash (empty), and request date/time
        $representation = "$this->method|/api/reservation||" . $messageDate;

        // Create the hashed message representation using HMAC SHA1 and encode it in base64
        $hmacSHA1 = base64_encode(hash_hmac('sha1', $representation, $private_key, true));

        // Set the headers
        $headers = [
            'x-message-date: ' . $messageDate,
            'x-ctr-signature: CTR ' . $public_key . ':' . $hmacSHA1,
            'Content-Type: application/json'
        ];

        try {
            $reservation = Reservation::where('id', $this->reservation_id)->first();
            if (!$reservation) {
                $this->log->info('Reservation not found!');
                //   throw new ApiGenericException('Reservation not Found');
            }

            $base_url = config('parkengage.HUB_ZEAG.base_url');
            $url = $base_url . '/api/reservation?bookingId=' . $reservation->ticketech_code;

            $this->log->info('Reservation ticketech_code ' . $reservation->ticketech_code . " Reservation id: " . $this->reservation_id);

            $response = CommonFunctions::makeRequest($url, $this->method, $headers);
            $this->log->info('HubParking Api Response Data ' . json_encode($response));
            if (in_array($response['http_code'], [201, 200])) {
                $this->log->info('Deleted on Hub Zeag');
                $reservation = Reservation::find($this->reservation_id);
                $reservation->is_hub_zeag = 4;
                $reservation->cancelled_at = date("Y-m-d H:i:s");
            } else {
                $reservation->is_hub_zeag = 7;
                Artisan::call('hubzeag:failed_notification', [
                    '--ticketech_code' => $reservation->ticketech_code,
                ]);
            }
            $reservation->hub_zeag_response = json_encode($response);
            $reservation->save();
            return $response;
        } catch (\Throwable $th) {
            $this->log->info('Error ' . $th->getMessage() . ' File Name :  ' . $th->getFile() . ' Line ' . $th->getLine());
            $errorMessage = 'ROC Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine();
            $data['partner_key'] = "ROC";
            $data['exception'] = "Exception in Check-in checkout Mail Request : " . $errorMessage;
        }
    }
}
