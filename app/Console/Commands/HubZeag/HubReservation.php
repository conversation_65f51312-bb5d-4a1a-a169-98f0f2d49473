<?php

namespace App\Console\Commands\HubZeag;

use App\Classes\CommonFunctions;
use App\Models\Reservation;
use App\Models\ReservationHistroy;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Illuminate\Console\Command;

class HubReservation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'hubzeag:reservation {reservation_id}, {method}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create and Update Reservation';

    /**
     * Create a new command instance.
     *
     * @return void
     */

    

    protected $create;
    protected $update;
    protected $reservation_id;
    protected $method;
    protected $carbon;

    public function __construct()
    {
        parent::__construct();
        $logFactory = new LoggerFactory();
        $this->create = $logFactory->setPath('logs/roc/hubapi/create')->createLogger('create_api');
        $this->update = $logFactory->setPath('logs/roc/hubapi/update')->createLogger('update_api');
        $this->carbon = new Carbon();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->reservation_id = $this->argument('reservation_id');
        $this->method = $this->argument('method');
        $log = ""; $headers = [];
        if($this->method == "PUT"){
            $log = $this->update;
            // $headers = array(
            //     'x-message-date: 2024-05-16T13:06:26+05:30',
            //     'x-ctr-signature: CTR 90a6c5257af59be12005b95e5931ac3a:XS+QcZETc9x8SL1fEwEzUac+TD8=',
            //     'Content-Type: application/json'
            // );
        }else{
            $log = $this->create;
            // $headers = array(
            //     'x-message-date: 2024-05-16T11:36:08+05:30',
            //     'x-ctr-signature: CTR 90a6c5257af59be12005b95e5931ac3a:Ap2lZP4jXawGw5Tc+QzPN1O1io8=',
            //     'Content-Type: application/json'
            // );
        }

        // Set the public and private keys
        $public_key = config('parkengage.HUB_ZEAG.public_key');
        $private_key = config('parkengage.HUB_ZEAG.private_key');

        // Capture the current date/time for the request in UTC
        $messageDate = Carbon::now('Asia/Kolkata')->format('Y-m-d\TH:i:sP');
        // dd($messageDate);

        // Create the message representation, composed of the Verb, resource, content hash (empty), and request date/time
        $representation = "$this->method|/api/reservation||" . $messageDate;

        // Create the hashed message representation using HMAC SHA1 and encode it in base64
        $hmacSHA1 = base64_encode(hash_hmac('sha1', $representation, $private_key, true));

        // Set the headers
        $headers = [
            'x-message-date: ' . $messageDate,
            'x-ctr-signature: CTR ' . $public_key . ':' . $hmacSHA1,
            'Content-Type: application/json'
        ];

        // dd($headers1, $headers);
        try {
            $reservation = Reservation::with(['facility', 'reservations_history', 'user'])->where('id', $this->reservation_id)->first();
            // dd($reservation->toArray());

            $reservation_count = ReservationHistroy::where('res_id', $this->reservation_id)->count();

            // Post Data
            $data['BookingId'] = $reservation->ticketech_code;
            $data['BookingVersion'] = $reservation_count;
            $data['ScheduledArrivalDateTime'] = $reservation->utc_start_timestamp;
            $data['ArrivalGraceMinutes'] = $reservation->facility->reservation_start_time;
            $data['ScheduledDepartureDateTime'] = $reservation->utc_end_timestamp;
            $data['TotalGraceMinutes'] = $reservation->facility->reservation_grace_period_minute;

            $data['ParkhouseId'] = 208;
            $data['OverstayParkhouseId'] = 209;

            $data['AccessIdentifierType'] = "BarCode";
            $data['AccessIdentifier'] = $reservation->hub_unique_id;
            $data['AccessMediaReminder'] = $reservation->thirdparty_code;;

            $name = explode(" ", $reservation->user->name);
            $log->info('User Name: ' . $reservation->user->name . " " . json_encode($name));

            $data['FirstName'] = $name[0];
            $data['LastName'] = $name[1];
            $data['VehiclePlate'] = $reservation->license_plate;
            $data['ParkingAmount'] = $reservation->total;
            $data['AddOnAmount'] = 0;

            $log->info('Local time: '.$reservation->start_timestamp . ' UTC Time Zone: '. $data['ScheduledArrivalDateTime']);

            $post_data = json_encode($data);

            $base_url = config('parkengage.HUB_ZEAG.base_url');
            $url = $base_url . '/api/reservation';

            $log->info('HubParking Api Request Data ' . $post_data. ', Url: ' .$url. ' Headers: '. json_encode($headers));
                        
            $response = CommonFunctions::makeRequest($url, $this->method, $headers, $data);
            $log->info('HubParking Api Response Data ' . json_encode($response));
            if(in_array($response['http_code'] , [201, 200])){
                $this->create->info('Saved on Hub Zeag');
                $reservation = Reservation::find($this->reservation_id);
                $reservation->is_hub_zeag = 2;
                $reservation->save();
            }
            return $response;
           
        } catch (\Throwable $th) {
            $log->info('Error ' . $th->getMessage() . ' File Name :  ' . $th->getFile() . ' Line ' . $th->getLine());
            $errorMessage = 'ROC Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine();
            $data['partner_key'] = "ROC";
            $data['exception'] = "Exception in Check-in checkout Mail Request : " . $errorMessage;
        }
    }
}
