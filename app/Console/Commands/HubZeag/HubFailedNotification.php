<?php

namespace App\Console\Commands\HubZeag;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\Facility;
use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;
use App\Models\Reservation;
use Exception;

class HubFailedNotification extends Command
{
    protected $signature = 'hubzeag:failed_notification {--ticketech_code= : If want to run for specific reservation.}';

    protected $description = 'Reservation process failed for Hub Zeag in case like: create, update and cancel.';

    protected $log;
    protected $partnerId;
    protected $facilityId;
    protected $carbon;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/roc/notification')->createLogger('process-failed-status');
        $this->partnerId = config('parkengage.PARTNER_MAPCO');
        $this->facilityId = config('parkengage.ROC_FACILITY');
        $this->carbon = new Carbon();
    }

    public function handle()
    {
        try {
            $this->log->info("Cron started");

            $partner = User::find($this->partnerId);
            $partnerName = $partner ? $partner->name : "Unknown Partner";

            $facility = Facility::find($this->facilityId);
            $facilityName = $facility ? $facility->full_name : "Unknown Facility";

            $timezone = QueryBuilder::setCustomTimezone($this->facilityId);
            $this->log->info("Processing for Partner ID: " . $this->partnerId . ", Facility ID: " . $this->facilityId . ", Timezone: " . $timezone);

            $current_time = $this->carbon->now();
            $threshold_time = $this->carbon->now()->subMinutes(30);

            $this->log->info("Current time: $current_time | Threshold time: $threshold_time");

            // Fetch null anet
            $pendingReservations = $this->getPendingReservations($this->facilityId, $current_time, $threshold_time);
            // dd($pendingReservations->count());

            if ($pendingReservations->isEmpty()) {
                $this->log->info("All Reservation synced at Hub Zeag found for Partner ID: $this->partnerId and Facility ID: $this->facilityId.");
                return;
            }

            $this->log->info("Found {$pendingReservations->count()} Failed  to create, udpate, and cancel for Partner ID: $this->partnerId, Facility ID: $this->facilityId. Sending notifications...");
            $this->notifyDevTeam($pendingReservations, $partnerName, $facilityName);

            $this->log->info("Cron ended successfully for Partner ID: $this->partnerId and Facility ID: $this->facilityId.");
        } catch (Exception $e) {
            $errorMessage = sprintf(
                "Error: %s in file %s on line %d",
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
            );

            $this->log->error($errorMessage);
        }
    }

    public function getPendingReservations($facilityId , $current_time, $threshold_time)
    {
        $query = Reservation::where('facility_id', $facilityId)
                ->whereNull('anet_transaction_id')
                ->whereNull('cancelled_at');
        
        // Add condition for `ticketech_code` if it is set
        if ($this->option('ticketech_code')) {
            $query->where('ticketech_code', $this->option('ticketech_code'));
        }            
        // $query->whereDate('created_at', '>', '2024-11-08')            
        $query->whereIn('is_hub_zeag', ['1', '5', '6', '7']);
               
        return $query->orderBy('id', 'desc')->get();
    }

    protected function notifyDevTeam($reservations, $partnerName, $facilityName)
    {
        $data = [
            'count' => $reservations->count(),
            'timestamp' => $this->carbon->now()->toDateTimeString(),
            'reservations' => $reservations->map(function ($reservation) {
                switch ($reservation->is_hub_zeag) {
                    case 0:
                        $hub_status = "Not belongs to ROC/Hub Zeag";
                        break;
                
                    case 1:
                        // Belongs to ROC/Hub
                        $hub_status = "Not Created on Hub Zeag";
                        break;
                
                    case 2:
                        // Belongs to ROC/Hub
                        $hub_status = "Created Successfully";
                        break;
                
                    case 3:
                        // Updated on Hub
                        $hub_status = "Updated Successfully";
                        break;
                
                    case 4:
                        // Cancelled on Hub
                        $hub_status = "Cancelled Successfully";
                        break;
                
                    case 5:
                        // Failed on Create
                        $hub_status = "Failed to create";
                        break;
                
                    case 6:
                        // Failed on Update
                        $hub_status = "Failed to update";
                        break;
                
                    case 7:
                        // Failed on Cancell
                        $hub_status = "Failed to Cancell";

                    case 8:
                        // Additional States
                        break;
                
                    default:
                        // Handle unexpected states
                        $hub_status = "N/A";
                        break;
                }
                
                return [
                    'ticketech_code' => $reservation->ticketech_code,
                    'created_date' => $this->carbon->parse($reservation->created_at)->format('Y-m-d H:i:s'),
                    'updated_date' => $this->carbon->parse($reservation->updated_at)->format('Y-m-d H:i:s'),
                    'start_timestamp' => $this->carbon->parse($reservation->start_timestamp)->format('Y-m-d H:i:s'),
                    'end_timestamp' => $this->carbon->parse($reservation->end_timestamp)->format('Y-m-d H:i:s'),
                    'hub_status' => $hub_status,
                    'hub_error' => $reservation->hub_zeag_response ?? "N/A",
                ];
            })->toArray(),
        ];

        $recipients = config("parkengage.notify.recipient_notify");

        try {
            Mail::send(
                'email.hub-failed-reservations',
                ['data' => $data],
                function ($message) use ($recipients, $partnerName, $facilityName) {
                    $message->to($recipients)
                        ->subject("Failed reservation on hub zeag for $facilityName ");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );

            $this->log->info("Notifications sent to recipients: " . implode(", ", $recipients));
        } catch (Exception $e) {
            $this->log->error("Failed to send notification emails: {$e->getMessage()}");
        }
    }
}
