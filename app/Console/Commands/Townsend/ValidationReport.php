<?php

namespace App\Console\Commands\Townsend;

use App\Exceptions\ApiGenericException;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ValidationReport extends Command
{
    protected $log;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'townsend:validation-report';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This report is used to send daily Validation report';
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/cronTownSend')->createLogger('townsend');
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            // $checkInTime  = date('Y-m-d') . ' 00-00-00';
            $partner_id = config('parkengage.townsend.partner_id');
            $fromDate =  date('Y-m-') . '01';
            $toDate =  date('Y-m-') . '31';
            $fromDate  = $fromDate . ' 00-00-00';
            $toDate  = $toDate . ' 23-59-59';

            $sql_query = "SELECT t.ticket_number, f.full_name as GarageName, t.checkin_time,t.checkout_time as actual_checkout_datetime ,t.length as total_length, t.orignal_length, t.processing_fee, t.tax_fee, t.total,t.grand_total,t.discount_amount, t.promocode, t.paid_amount as validate_amount, t.payment_date, IF(t.paid_by >0 ,(SELECT IF(u.user_type='10',ab.business_name,u.name) as name  from users as u
            inner join affiliate_business as ab ON ab.id = u.business_id 
            where u.id IN (t.paid_by) ),'-') as paid_by_affiliate,
            IF(t.policy_id > 0, (SELECT policy_name from business_policy WHERE id IN (t.policy_id)),'' ) as policyName,
            t.paid_remark, t.paid_date
            FROM tickets as t
            inner join facilities as f on f.id = t.facility_id
            WHERE t.checkin_time >='$fromDate' and t.checkin_time <='$toDate' and t.paid_amount > 0 AND t.partner_id ='$partner_id'  order by t.id DESC";
            $reports = DB::select($sql_query);
            $count = count($reports);
            $finalCodes1 = [];
            $TotalRevenue = 0;
            if ($count > 0) {
                $count = count($reports);
                if ($count <= 0) {
                    throw new ApiGenericException('No record found');
                }
                $excelSheetName = ucwords(str_replace(' ', '', 'ValidationReport'));
                $finalCodes1 = [];
                $increment1 = 1;
                $totalTickets = $count;
                $totalRevenue = 0;
                $totalValidatedAmount = 0;
                foreach ($reports as $key => $value) {
                    $finalCodes1[$key]['Garage Name'] = $value->GarageName;
                    $finalCodes1[$key]['Ticket Number'] = $value->ticket_number;
                    $finalCodes1[$key]['Check-In Date'] = Carbon::parse($value->checkin_time)->format('m-d-Y H:i:s');
                    $finalCodes1[$key]['Check-Out Date'] = Carbon::parse($value->actual_checkout_datetime)->format('m-d-Y H:i:s');;
                    $finalCodes1[$key]['Ticket Amonut'] = floatval($value->total);
                    // $finalCodes1[$key]['Duration'] = floatval($value->total_length);
                    $finalCodes1[$key]['Processing Fee'] = floatval($value->processing_fee);
                    $finalCodes1[$key]['Amount Paid'] = floatval($value->grand_total);
                    $finalCodes1[$key]['Validated Amount'] = floatval($value->validate_amount);
                    $finalCodes1[$key]['Policy name'] = floatval($value->policyName);
                    $finalCodes1[$key]['Validation Date'] = Carbon::parse($value->paid_date)->format('m-d-Y H:i:s');;
                    $finalCodes1[$key]['Validator Name'] = isset($value->paid_by_affiliate) ? $value->paid_by_affiliate : '';
                    $totalRevenue += $value->grand_total;
                    $totalValidatedAmount += $value->validate_amount;
                    $increment1++;
                }
                Excel::create(
                    $excelSheetName,
                    function ($excel) use ($finalCodes1, $excelSheetName) {
                        if (isset($finalCodes1) && !empty($finalCodes1)) {
                            $excel->sheet($excelSheetName, function ($sheet) use ($finalCodes1) {
                                $sheet->fromArray($finalCodes1, null, 'A1', false, true);
                            });
                        } else {
                            $this->log->info("No Record Found ");
                        }
                    }
                )->store('xls');
                // storeFile In Server Dir
                $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                $this->log->info("Path Of file and name11 {$path_to_file}");

                $data['report_name']        = 'validation_report';
                $data['totalTickets']       = $totalTickets;
                $data['overAllTotal']       = $totalRevenue;
                $data['validatedAmount']    = $totalValidatedAmount;
                $data['mail_body']          = "PFB summary of today’s Validation Report. A detailed report is also attached.";

                Mail::send('townsend.cashier-shift-report', $data, function ($message)  use ($excelSheetName) {
                    $message->to(config('parkengage.townsend.validation_report_emails'));
                    $message->subject(config('parkengage.townsend.validation_report_subject'));
                    $message->from(env('INFO_MAIL_PARKENGAGE_FROM'), env('MAIL_USERNAME'));
                    $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                    $this->log->info("Path Of file and name 222 {$path_to_file}");
                    if (file_exists($path_to_file)) {
                        $this->log->info("Path Of file and name 333 {$path_to_file}");
                        $message->attach($path_to_file);
                    }
                });
                $this->log->info("Mail Sent Successfully {$path_to_file}");
            } else {
                $this->log->info("No Record Found  ");
            }
        } catch (\Throwable $th) {
            $data['partner_key'] = "Townsend";
            $data['exception'] = "Exception in Townsend Validation Report Sending mail : " . $th->getMessage();
            Mail::send('townsend.email-exception', $data, function ($message) {
                $message->to(config('parkengage.email_exceptions'));
                $message->subject("Email Exception :- Townsend Daily Revenue Report");
                $message->from(env('INFO_MAIL_PARKENGAGE_FROM'), env('MAIL_USERNAME'));
            });
            $this->log->error('Exception in Townsend Validation Report Sending mail ' . $th->getMessage());
            // FacadesLog::error('Error while sending daily summary email file ' . $th->getMessage() . 'line ' . $th->getLine());
        }
    }
}
