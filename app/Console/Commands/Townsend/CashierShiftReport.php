<?php

namespace App\Console\Commands\Townsend;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Services\LoggerFactory;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class CashierShiftReport extends Command
{
    protected $log;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'townsend:cashier-shift-report';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This report is used to send daily cashier shift report daily';
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/cronTownSend')->createLogger('townsend');
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $partner_id = config('parkengage.townsend.partner_id');
        try {
            $checkInTime  = date('Y-m-d') . ' 00-00-00';
            // $checkInTime  =  '2023-07-09 00-00-00';
            $checkOutTime    = date('Y-m-d') . ' 23-59-59';
            $sql_query = "SELECT count(td.rate_description) as trx_count, td.rate_description,td.rate_amount, td.total as total, td.tax_fee as tax_fee, td.processing_fee as processing_fee, sum(td.grand_total) as grand_total, sum(td.discount_amount) as validate_amonut, f.full_name,f.garage_code 
            from transaction_data as td
            inner join tickets as t on t.id = td.ticket_id
            inner join facilities as f on f.id = td.facility_id
            where td.partner_id IN ('$partner_id') and t.checkin_time >='$checkInTime' and t.checkin_time <='$checkOutTime' and t.is_checkout ='1' group by td.rate_description";
            $reports = DB::select($sql_query);
            $count = count($reports);
            $finalCodes1 = [];
            $TotalRevenue = 0;
            if ($count > 0) {
                $excelSheetName = ucwords(str_replace(' ', '', 'DailyRevenueReport')) . '-' . date('m-d-Y');
                $finalCodes1 = [];
                $increment1 = 1;
                $TotalRevenue = 0;
                $totalTickets = 0;
                $netValue = 0;
                $ticketAmount = 0;
                $grossAmount = 0;
                $validatedAmount = 0;
                $processingFee = 0;
                $taxFee = 0;
                $locationName = '';
                $garageCode = '';
                foreach ($reports as $key => $value) {
                    $locationName = $value->full_name;
                    $garageCode = $value->garage_code;
                    $finalCodes1[$key]['Rate'] = $value->rate_description;
                    $finalCodes1[$key]['No of Tickets'] = intval($value->trx_count);
                    if ($value->rate_description == 'Grace Period') {
                        $finalCodes1[$key]['Ticket Amount'] = 0;
                        $finalCodes1[$key]['Gross Amount'] = 0;
                        $finalCodes1[$key]['Processing Fee'] = 0;
                        $finalCodes1[$key]['Net Value'] = 0;
                        $finalCodes1[$key]['Validated Amount'] = 0;
                        // $finalCodes1[$key]['Total'] = 0;
                    } else {
                        $finalCodes1[$key]['Ticket Amount'] = floatval($value->total);;
                        $finalCodes1[$key]['Gross Amount'] = floatval($value->grand_total);;
                        $finalCodes1[$key]['Processing Fee'] = floatval($value->processing_fee * $value->trx_count);
                        $finalCodes1[$key]['Net Value'] = intval($value->rate_amount);
                        $finalCodes1[$key]['Validated Amount'] = floatval($value->validate_amonut);;
                        // $finalCodes1[$key]['Total'] = (floatval($value->grand_total) - floatval($value->validate_amonut));
                        $TotalRevenue += ($value->grand_total);
                        $this->log->info(" rate_description {$value->rate_description} processing_fee {$value->processing_fee}");
                    }
                    $totalTickets += $value->trx_count;
                    $netValue += $value->rate_amount;
                    $ticketAmount += $value->total;
                    $grossAmount += $value->grand_total;
                    $validatedAmount += $value->validate_amonut;
                    // $taxFee += $value->tax_fee;
                    $processingFee += floatval($value->processing_fee * $value->trx_count);
                    $increment1++;
                }
                Excel::create(
                    $excelSheetName,
                    function ($excel) use ($finalCodes1, $excelSheetName, $count, $totalTickets, $TotalRevenue, $netValue, $processingFee, $taxFee, $locationName, $garageCode, $ticketAmount, $grossAmount, $validatedAmount) {
                        if (isset($finalCodes1) && !empty($finalCodes1)) {
                            $excel->sheet($excelSheetName, function ($sheet) use ($finalCodes1, $count, $totalTickets, $TotalRevenue, $netValue, $processingFee, $taxFee, $locationName, $garageCode, $ticketAmount, $grossAmount, $validatedAmount) {
                                $sheet->cell('A8:D8', function ($row) {
                                    $row->setBackground('#0000FF');
                                    $row->setFontColor('#ffffff');
                                });
                                $sheet->cell('A10:G10', function ($row) {
                                    $row->setBackground('#0000FF');
                                    $row->setFontColor('#ffffff');
                                });
                                $colorCode = $count + 11;
                                $row_name = 'A' . $colorCode . ':G' . $colorCode;
                                $sheet->cell($row_name, function ($row) {
                                    $row->setBackground('#0000FF');
                                    $row->setFontColor('#ffffff');
                                    // $row->setFontSize(16);
                                    // Set font
                                    $row->setFont(array(
                                        'family'     => 'Calibri',
                                        'size'       => '12',
                                        'bold'       =>  true
                                    ));
                                });
                                // Format a range with e.g. leading zeros
                                // $sheet->setColumnFormat(array(
                                //     'B10:F10' => '0'
                                // ));
                                $sheet->row(1, array('', 'Daily Revenue Report'));
                                $sheet->row(2, array('', ''));
                                $sheet->row(3, array('Date', date('d-m-Y', strtotime('now'))));
                                $sheet->row(4, array('Location Name', $locationName));
                                $sheet->row(5, array('Location ID', $garageCode));
                                $sheet->row(6, array('', '', '', ''));
                                $sheet->row(7, array('', '', '', ''));
                                $sheet->row(8, array('Transient Counter', $totalTickets, 'Ticket Revenue', $TotalRevenue));
                                // $sheet->row(7, array('Total', $totalTickets, 'Total Revenue', $TotalRevenue));
                                $sheet->row(9, array('', '', '', ''));
                                $sheet->fromArray($finalCodes1, null, 'A10', false, true);
                                $sheet->row($count + 11, array('Total', $totalTickets, $ticketAmount, $grossAmount, $processingFee, $netValue, $validatedAmount));
                            });
                        } else {
                            $this->log->info("No Record Found ");
                        }
                    }
                )->store('xls');
                // storeFile In Server Dir
                $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                $this->log->info("Path Of file and name11 {$path_to_file}");
                $data['totalTickets']   = $totalTickets;
                $data['netValue']       = $netValue;
                $data['overAllTotal']   = $TotalRevenue;
                $data['report_name']    = 'daily_revenue_report';
                $data['location_name']  = $locationName;
                $data['mail_body']      = "PFB summary of today’s Revenue Report. A detailed report is also attached.";

                Mail::send('townsend.cashier-shift-report', $data, function ($message)  use ($excelSheetName) {
                    // $message->bcc(['<EMAIL>']);
                    $message->to(config('parkengage.townsend.revenue_report_emails'));
                    $message->subject(config('parkengage.townsend.revenue_report_subject'));
                    $message->from(env('INFO_MAIL_PARKENGAGE_FROM'), env('MAIL_USERNAME'));
                    $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                    $this->log->info("Path Of file and name 222 {$path_to_file}");
                    if (file_exists($path_to_file)) {
                        $this->log->info("Path Of file and name 333 {$path_to_file}");
                        $message->attach($path_to_file);
                    }
                });
                $this->log->info("Mail Sent Successfully {$path_to_file}");
            } else {
                $this->log->info("No Record Found  ");
            }
        } catch (\Throwable $th) {
            $data['partner_key'] = "Townsend";
            $data['exception'] = "Exception in Townsend Cashier Shift Report Sending mail " . $th->getMessage();
            Mail::send('townsend.email-exception', $data, function ($message) {
                $message->to(config('parkengage.email_exceptions'));
                $message->subject("Email Exception :- Townsend Daily Revenue Report");
                $message->from(env('INFO_MAIL_PARKENGAGE_FROM'), env('MAIL_USERNAME'));
            });
            $this->log->error('Exception in Townsend Cashier Shift Report Sending mail ' . $th->getMessage());
            // FacadesLog::error('Error while sending daily summary email file ' . $th->getMessage() . 'line ' . $th->getLine());
        }
    }
}
