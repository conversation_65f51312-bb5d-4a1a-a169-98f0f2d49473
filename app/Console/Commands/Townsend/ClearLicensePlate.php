<?php

namespace App\Console\Commands\Townsend;

use Mail;
use Exception;
use File;
use App\Models\Ticket;
use App\Models\Facility;
use App\Models\ParkEngage\Gate;
use App\Models\OverstayTicket;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Classes\ParkengageGateApi;
use App\Models\ParkEngage\LicensePlate;


class ClearLicensePlate extends Command
{

    protected $signature = 'townsend-clear-license-plate';

    protected $description = 'If vehicle not present then flush license plate.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/parkengage/flush-license-plate')->createLogger('flush-license-plate');
    }

    public function handle()
    {   
        $facility = Facility::find(33);
        $gates = Gate::where("facility_id", $facility->id)->get();
        if ($facility->adam_host != '') {
            foreach($gates as $key=>$gate){
                $params = ['gate_id' => $gate->gate];
                $response = ParkengageGateApi::isvehicleAvailable($params, $facility->adam_host);
                $this->log->info("vehicle command complete status : " . $response['data'][0]);
                if (isset($response['data'][0]) && $response['data'][0] == "false") {
                    LicensePlate::where("facility_id", $facility->id)->where("gate", $gate->gate)->delete();
                }
                $this->log->info("Cleared");
            }
            
        }
       
    }
    
}

