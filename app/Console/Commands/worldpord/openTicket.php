<?php

namespace App\Console\Commands\worldpord;

use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\Rate;
use App\Services\LoggerFactory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Mail;

class openTicket extends Command
{
    protected $partnerId;
    protected $partnerRmId;
    protected $facilityId;
    protected $checkinTime;
    protected $checkoutTime;
    protected $log;
    protected $rmWiseFacilities;
    protected $facilityWiseTicketsCountArray;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "openticket:report
                            { --partnerId= : partner ID }
                            { --partnerRmId= : partner RM ID }
                            { --facilityId= : facility ID }
                            { --startDate= : optional start date}
                            { --endDate= : optional end date}";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This Command is use to send open ticket on every monday or based on date range also.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/crons')->createLogger('opentickets');
    }

    public function setUp()
    {
        $this->log->info('Executing cron Send Every monday Open Tickets.');

        $todaytDate = new Carbon();
        $endDate = $todaytDate->subDay();

        $todayStartDate = new Carbon();
        $startDate = $todayStartDate->subDay(7);

        $this->partnerId = $this->option('partnerId') ?: 0;
        $this->partnerRmId = $this->option('partnerRmId') ?: 0;
        $this->facilityId = $this->option('facilityId') ?: 0;
        // $this->partnerId = config('parkengage.townsend.partner_id');
        $this->checkinTime = $this->option('startDate') ?: $startDate->format('Y-m-d ') . '00:00:00';
        $this->checkoutTime = $this->option('endDate') ?: $endDate->format('Y-m-d ') . '23:59:59';
        if ($this->partnerId <= 0) {
            throw new Exception("Please Enter Partner ID");
        }
        if ($this->partnerRmId <= 0) {
            throw new Exception("Please Enter RM ID");
        }
        if ($this->facilityId <= 0) {
            throw new Exception("Please Enter Facility");
        }
        // $this->brandSettings
        $this->log->info('Executing cron Send Every monday Open Tickets with start date : ' . $this->checkinTime . ' End Date : ' . $this->checkoutTime);
        /* print_r('Executing cron Send Every monday Open Tickets with start date : ' . $this->checkinTime . ' End Date : ' . $this->checkoutTime);
        die; */
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->setUp();
            $openTickets = [];
            $checkinArray = [];

            $discountAmount = 0;
            $TotalTicketAmount = 0;
            $TotalRevenueNonValidated = 0;
            $totalTicketsNonValidated = 0;

            $this->log->info('WLA 11.');
            $brandSetting = BrandSetting::where('user_id', $this->partnerId)->first();
            $color = $brandSetting->color;
            $validatedCheckinAmountNonValidated = $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = 0;
            if ($this->partnerId == 2980 && $this->partnerRmId == '6131') {

                $this->log->info('WLA 22.');
                $openTickets = [];
                $this->facilityWiseTicketsCountArray = [];
                $dataforCategoryWiseData = $this->getRmWiseData();
                $dataforCategoryWise = $dataforCategoryWiseData['driveup'][0];
                if (count($dataforCategoryWise) > 0) {


                    $this->log->info('WLA 44.');
                    $excelSheetName = 'Open Tickets Report-' . date('m-d-Y');

                    Excel::create(
                        $excelSheetName,
                        function ($excel) use (
                            $color,
                            $dataforCategoryWise,
                            $openTickets,
                            $totalTicketsNonValidated
                        ) {
                            $this->log->info('WLA 55 .' . json_encode($this->rmWiseFacilities));
                            if (isset($this->rmWiseFacilities) && !empty($this->rmWiseFacilities)) {
                                $this->log->info('WLA 66 .');
                                foreach ($this->rmWiseFacilities as $key => $facilitiy) {
                                    $openTickets = [];
                                    $totalTicketsNonValidated = 0;
                                    $this->log->info('WLA 77 .');
                                    $facility = Facility::where(['id' => $facilitiy])->first();
                                    $locationName       = $facility->full_name;
                                    $shortName          = $facility->short_name;
                                    $garageCode         = $facility->garage_code;
                                    $showTicketAmount   = $facility->rate_per_hour;
                                    $rowKey = 0;
                                    foreach ($dataforCategoryWise as $key => $value) {
                                        if ($value->facility_id == $facilitiy) {
                                            $openTickets[$rowKey]['Ticket Number']     = $value->ticket_number;
                                            $openTickets[$rowKey]['License Plate']     = $value->license_plate;
                                            $openTickets[$rowKey]['Check-In At']       = date('m-d-Y H:i:s', strtotime($value->checkin_time));
                                            $rowKey++;
                                            $totalTicketsNonValidated++;
                                        }
                                    }
                                    $ticketsforBlade['facilty_id']      = $facility->id;
                                    $ticketsforBlade['facility_name']    = $facility->full_name;
                                    $ticketsforBlade['tickets_counts']  = $totalTicketsNonValidated;
                                    $this->log->info('Print Faciliy Da ta : ' . json_encode($ticketsforBlade));
                                    array_push($this->facilityWiseTicketsCountArray, $ticketsforBlade);

                                    // Tickets Cashiered 
                                    $excel->sheet($shortName, function ($sheet) use (
                                        $openTickets,
                                        $color,
                                        $totalTicketsNonValidated,
                                        $locationName,
                                        $shortName,
                                        $garageCode
                                    ) {
                                        $driveupTotal = 10;
                                        $topSpace = 7;

                                        $this->log->info('WLA 88 .');

                                        $sheet->setWidth(array(
                                            'A'     => 35,
                                            'B'     =>  35,
                                            'C'     =>  35/* ,
                                            'D'     =>  17.57,
                                            'E'     =>  17.34,
                                            'F'     =>  15.57,
                                            'G'    =>   21,
                                            'H'    =>   16.86,
                                            'I'    =>   18 */
                                        ));

                                        $sheet->cell('A6:C6', function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                        });
                                        $colorCode = count($openTickets) + 7;

                                        //  print_r($countNonValidated);die;

                                        $row_name = 'A' . $colorCode . ':C' . $colorCode;
                                        $sheet->cell($row_name, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');

                                            $row->setFont(array(
                                                'family'     => 'Calibri',
                                                'size'       => '12',
                                                'bold'       =>  true
                                            ));
                                        });

                                        $this->log->info('WLA 99 .');
                                        // $sheet->row(1, 'Daily Revenue Report');
                                        // define width colom A,B, witdh static here
                                        $sheet->getColumnDimension('A')->setWidth(36);
                                        $sheet->getColumnDimension('B')->setWidth(36);
                                        $sheet->getColumnDimension('C')->setWidth(36);
                                        /* $sheet->getColumnDimension('D')->setWidth(17.57);
                                        $sheet->getColumnDimension('E')->setWidth(17.34);
                                        $sheet->getColumnDimension('F')->setWidth(15.57);
                                        $sheet->getColumnDimension('G')->setWidth(21);
                                        $sheet->getColumnDimension('H')->setWidth(16.86);
                                        $sheet->getColumnDimension('I')->setWidth(18); */

                                        /* $sheet->getStyle("A")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet->getStyle("C")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet->getStyle("D")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet->getStyle("H")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet->getStyle("I")->getNumberFormat()->setFormatCode('0.00'); */
                                        // $sheet->getStyle("C")->getNumberFormat()->setFormatCode('0');

                                        //end width for colom here
                                        //set header for excel work
                                        $sheet->mergeCells('A1:C1');
                                        //$sheet->mergeCells('F1:J1');
                                        $sheet->getRowDimension(1)->setRowHeight(60);
                                        $sheet->setCellValue('A1', 'Open Ticket Report');
                                        $sheet->cell('A1:C1', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('30');
                                        });
                                        $sheet->cell('A6:C6', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                        });
                                        $this->log->info('WLA 1010 .');

                                        //end set header work

                                        //set second header work here Metro @ Showplace Square Garage
                                        // $sheet->mergeCells('A2:A2');
                                        if (date('Y-m-d', strtotime($this->checkinTime)) == date('Y-m-d', strtotime($this->checkoutTime))) {
                                            $cellValue = "Report Date - " .  date('m-d-Y', strtotime($this->checkinTime));
                                        } else {

                                            $cellValue = "Report Date Range - \n" .  date('m-d-Y', strtotime($this->checkinTime)) .  ' - ' . date('m-d-Y', strtotime($this->checkoutTime));
                                        }
                                        $cellValue .= "\nPrint Date - " .  date('m-d-Y', strtotime('now'));
                                        $sheet->setCellValue('A2', "$cellValue");
                                        $sheet->getStyle('A2')->getAlignment()->setWrapText(true);
                                        // Set the height of cell H2 (adjust as needed)
                                        $sheet->getRowDimension(2)->setRowHeight(80);
                                        $sheet->getRowDimension(3)->setRowHeight(50);
                                        // Set the height to 40 units
                                        // Set the width of cell H2 (adjust as needed)
                                        // $sheet->getColumnDimension('H')->setWidth(20);
                                        $sheet->cell('A2:A2', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D6DCE4');
                                            $cell->setFontColor('#000000');
                                            $cell->setFontSize('16');
                                        });

                                        $location = "Location Name \r" . $locationName;
                                        $sheet->mergeCells('B2:B2');
                                        $sheet->setCellValue('B2', "$location");
                                        $sheet->getStyle('B2')->getAlignment()->setWrapText(true);

                                        $sheet->cell('B2:B2', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#ffffff');
                                            $cell->setFontColor('#000000');
                                            $cell->setFontSize('18');
                                        });
                                        $sheet->mergeCells('C2:C2');
                                        $locationId = "Location ID \n" . $garageCode;
                                        $sheet->setCellValue('C2', "$locationId");
                                        $sheet->getStyle('C2')->getAlignment()->setWrapText(true);

                                        $sheet->cell('C2', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D6DCE4');
                                            $cell->setFontColor('#040D12');
                                            $cell->setFontSize('18');
                                        });
                                        $this->log->info('WLA 111 .');

                                        $sheet->mergeCells('A3:B3');
                                        $sheet->setCellValue('A3', "Open Ticket Counter");
                                        $sheet->setBorder('A3:C3', 'thin', '#ffffff');
                                        // $sheet->setBorderColor('white');
                                        $sheet->cell('A3:B3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('18');
                                        });

                                        $sheet->setCellValue('C3', $totalTicketsNonValidated);
                                        $sheet->cell('C3:C3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('18');
                                        });

                                        $this->log->info('WLA 1212 .');
                                        $sheet->mergeCells('A4:C4');
                                        $sheet->mergeCells('A5:C5');
                                        $sheet->setCellValue('A5', 'Tickets Cashiered DriveUp');

                                        $sheet->cell('A5', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D9E1F2');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });
                                        // $this->log->info('WLA 1313 . ' . json_encode($openTickets));
                                        $sheet->fromArray($openTickets, [], 'A6', false, true);  // this is use in case of not ticket listing

                                    });
                                }
                            }
                        }
                    )->store('xls');

                    $this->log->info(json_encode($this->facilityWiseTicketsCountArray));
                    $this->log->info('Open Tickets File Save Successfully');
                    $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                    $data['totalTickets']   = 1;
                    // $data['netValue']       = $TotalRevenueNonValidated + $TotalCheckinRevenueNonValidated;
                    // $data['overAllTotal']   = $TotalRevenueNonValidated + $TotalCheckinRevenueNonValidated;
                    $data['report_name']    = 'open_ticket_report';
                    $data['facilityWiseTicketsCount']    = $this->facilityWiseTicketsCountArray;
                    // $data['location_name']  = 'TEST';
                    $data['themeColor']     = $color;
                    $data['mail_body']      = "PFB summary for today’s Open Tickets Report. A detailed report is also attached which has two separate tabs for “World Cruise Center Parking” and “Catalina” facilities.";

                    Mail::send('townsend.cashier-shift-report', $data, function ($message)  use ($excelSheetName) {
                        // $message->bcc(['<EMAIL>']);
                        $message->to(config('parkengage.townsend.open_ticket_emails'));
                        $message->subject(config('parkengage.townsend.open_ticket_subject'));
                        $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                        $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                        $this->log->info("Mail Sent success with failname :  {$path_to_file}");
                        if (file_exists($path_to_file)) {
                            // $this->log->info("Path Of file and name 333 {$path_to_file}");
                            $message->attach($path_to_file);
                        }
                    });
                } else {
                    $this->log->info('No Ticket Found On date : ' . date('Y-m-d'));
                }
                // $this->log->info('WLA 33. ' . json_encode($openTickets));


            } else if ($this->facilityId == 33) {
                // Need To add code for Townsend
            }
        } catch (\Throwable $th) {
            //throw $th;
            $this->log->error('WLA Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine());
        }
    }

    public function getRmWiseData()
    {
        $this->rmWiseFacilities = [];
        $RmFacilities = "SELECT facility_id FROM user_facilities WHERE user_id IN (" . $this->partnerRmId . ") AND deleted_at is null ";
        $facilities = DB::select($RmFacilities);
        foreach ($facilities as $key => $value) {
            array_push($this->rmWiseFacilities, $value->facility_id);
        }
        $emplodeFacilities = implode(",", $this->rmWiseFacilities);

        $openTickets = "SELECT t.facility_id,  t.ticket_number,t.license_plate, t.checkin_time, t.checkout_time,t.grand_total,t.card_type,t.card_last_four, t.expiry, t.vp_device_checkin
        FROM tickets as t
        WHERE  t.partner_id IN (" . $this->partnerId . ") AND t.facility_id IN (" . $emplodeFacilities . ") AND t.checkin_time >='" . $this->checkinTime . "' AND t.checkin_time <='" . $this->checkoutTime . "' AND t.is_checkin='1' AND t.is_checkout='0' AND t.deleted_at is null order by t.facility_id ASC ,t.id DESC";

        $this->log->info('Open Ticket Main Query : ' . $openTickets);
        $openTicketsResult = DB::select($openTickets);

        $dataarray['driveup'] = array($openTicketsResult);
        return $dataarray;
    }
}
