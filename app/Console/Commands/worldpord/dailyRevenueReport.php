<?php

namespace App\Console\Commands\worldpord;

use App\Exceptions\ApiGenericException;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\Rate;
use App\Services\LoggerFactory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Mail;

use Illuminate\Support\Fluent;
use PHPExcel_Worksheet_Drawing;

class dailyRevenueReport extends Command
{
    protected $partnerId;
    protected $partnerRmId;
    protected $facilityId;
    protected $checkinTime;
    protected $checkoutTime;
    protected $totalTickets;
    protected $totalAmounts;
    protected $log;
    protected $isSummaryEnable;
    protected $color;
    protected $brandSetting;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "wla:daily-revenue-report
                            { --partnerId= : partner ID }
                            { --facilityId= : facility ID }
                            { --startDate= : optional start date}
                            { --endDate= : optional end date}";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is used to send daily revenue excel default or Date range baseed ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/crons')->createLogger('wla');
        // $this->partnerId = config('parkengage.townsend.partner_id');
        // $this->partnerRmId = config('parkengage.townsend.partner_id');
        $this->totalTickets = 0;
        $this->totalAmounts = 0;
        $this->isSummaryEnable = false;
        $this->color = '#0C4A7A'; #$color = "#0C4A7A";
    }
    // setting up start and end date parameters
    public function setUp()
    {
        $this->log->info('Executing cron Send Daily Email.');

        $startDateCurrentMonth = new Carbon();
        $startDate = $startDateCurrentMonth->subDay();

        $endDateNextMonth = new Carbon('first day of next month');
        $endDate = $endDateNextMonth->subDay();

        $this->partnerId = $this->option('partnerId') ?: 0;
        $this->facilityId = $this->option('facilityId') ?: 0;
        // $this->partnerId = config('parkengage.townsend.partner_id');
        $this->checkinTime = $this->option('startDate') ?: $startDate->format('Y-m-d ') . '00:00:00';
        $this->checkoutTime = $this->option('endDate') ?: $startDate->format('Y-m-d ') . '23:59:59';
        if ($this->partnerId <= 0) {
            throw new Exception("Please Enter Partner ID");
        }
        if ($this->facilityId <= 0) {
            throw new Exception("Please Enter Facility");
        }
        // $this->brandSettings
        $this->log->info('Executing cron for monthly cancellation with start date : ' . $this->checkinTime . ' End Date : ' . $this->checkoutTime);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->setUp();
            $finalCodes5 = [];
            $checkinArray = [];

            $discountAmount = 0;
            $TotalTicketAmount = 0;
            $TotalRevenueNonValidated = 0;
            $totalTicketsNonValidated = 0;
            $netValueNonValidated = 0;
            $processingFeeNonValidated = 0;
            $totalDiscountAmountNonValidated = 0;
            $validatedAmountNonValidated = 0;
            $totalDriveUpDuration = 0;
            $totalReservationDurtion = 0;
            $permitTicketCount = 0;
            $totalPayment = 0;


            $facility_id = $this->facilityId;
            $facility = Facility::where(['id' => $this->facilityId])->first();
            $this->log->info('WLA 11.');

            $this->log->info('WLA 1122.');
            $rates = Rate::where(['facility_id' => $facility->id])->withTrashed()->orderby('description', 'asc')->get();
            $this->log->info('WLA 1133.');
            $this->log->info('WLA 11444.');
            $locationName = $facility->full_name;
            $shortName    = $facility->short_name;
            $garageCode = $facility->garage_code;
            $showTicketAmount = $facility->rate_per_hour;

            $this->brandSetting = $brandSetting = BrandSetting::where('user_id', $this->partnerId)->first();
            $color = $this->brandSetting->color;
            $this->color = $this->brandSetting->color;


            $validatedCheckinAmountNonValidated = $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = 0;
            if ($this->partnerId == 2980  && in_array($this->facilityId, [125, 142])) {

                $this->log->info('WLA 22.');
                $excelSheetName2 = ucwords('Payment Breakdown');
                $finalCodes2 = [];
                $TotalPaymentReceived = 0;
                $TotalValidatedAmount = 0;
                $totalCashServiceAmount = 0;
                $finalCodes2[0]['total'] = 0;
                $finalCodes2[0]['discount'] = 0;
                $finalCodes2[1]['total'] = 0;
                $finalCodes2[1]['discount'] = 0;

                $dataforCategoryWiseData = $this->getRmWiseData();
                // dd($dataforCategoryWiseData);
                $rowKey = 0;
                $dataforCategoryWise = $dataforCategoryWiseData['driveup'][0];
                if (count($dataforCategoryWise) > 0) {
                    foreach ($dataforCategoryWise as $key => $value) {
                        $finalCodes5[$rowKey]['Rate ($)']               = floatval($value->payAmount);
                        $finalCodes5[$rowKey]['Ticket Number']          = '-';
                        $finalCodes5[$rowKey]['No of Tickets']          = intval($value->ticketCount);
                        $finalCodes5[$rowKey]['Duration (Hours)']       = intval($value->lenghtInMints);
                        $finalCodes5[$rowKey]['Ticket Amount ($)']      = floatval($showTicketAmount);
                        $finalCodes5[$rowKey]['Gross Amount ($)']       = floatval($value->Pay_grand_total);
                        // $finalCodes5[$rowKey]['Processing Fees ($)'] = floatval($dataforCategoryWise[$rowKey]['faidprocessing_fee']);
                        // $finalCodes5[$rowKey]['Net Amount ($)'] = floatval($dataforCategoryWise[$rowKey]['fnet_amount']);
                        $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($value->discount_amount);

                        $driveupDataTickets = "SELECT count(distinct(t.id)) AS ticketCount,
                            t.grand_total AS grand_total,
                            t.grand_total  AS payAmount,  
                            t.tax_fee   AS tax_fee,   
                            t.processing_fee AS processing_fee,
                            t.grand_total AS Pay_grand_total,
                            t.parking_amount AS net_amount,
                            t.discount_amount AS discount_amount,
                            t.paid_amount AS validated_amount,
                            t.ticket_number,
                            f.full_name,
                            t.length,
                            CASE
                                WHEN t.is_overstay = '1' THEN CEIL(COALESCE(t.length, 0) + COALESCE(ot.length, 0))
                                ELSE CEIL(COALESCE(t.length, 0))
                            END AS lenghtInMints,
                            sum(ot.grand_total)  as overstayGrandTotal, 
                            sum(ot.discount_amount) as overstayDiscount
                            from tickets as t
                            inner join facilities as f on f.id = t.facility_id
                            left join overstay_tickets as ot on t.id = ot.ticket_id
                            where  t.partner_id IN (" . $this->partnerId . ") and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "'  and t.paid_type='9' AND t.facility_id = '" . $this->facilityId . "' and t.is_checkout ='1' and t.deleted_at is null and t.reservation_id is null and t.grand_total = '" . $value->payAmount . "' group by t.ticket_number order by t.grand_total";
                        // $this->log->info('WLA 33 Query . ' . json_encode($driveupDataTickets));
                        $driveupDataTicketResults = DB::select($driveupDataTickets);

                        $temp_index = $rowKey;
                        $temp_overstayAmount = 0;
                        $temp_overstayDiscountAmount = 0;
                        if (count($driveupDataTicketResults) > 0) {

                            $rowKey++;
                            foreach ($driveupDataTicketResults as $tkey => $ticket) {
                                if ($this->isSummaryEnable) {
                                    $finalCodes5[$rowKey]['Rate ($)']               = '';
                                    $finalCodes5[$rowKey]['Ticket Number']          = $ticket->ticket_number;
                                    $finalCodes5[$rowKey]['No of Tickets']          = intval($ticket->ticketCount);
                                    $finalCodes5[$rowKey]['Duration (Hours)']       = intval($ticket->lenghtInMints);
                                    $finalCodes5[$rowKey]['Ticket Amount ($)']      = floatval($showTicketAmount);
                                    $finalCodes5[$rowKey]['Gross Amount ($)']       = floatval($ticket->Pay_grand_total + $ticket->overstayGrandTotal);
                                    $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($ticket->discount_amount  + $ticket->overstayDiscount);
                                    $rowKey++;
                                }
                                $temp_overstayAmount += $ticket->overstayGrandTotal;
                                $temp_overstayDiscountAmount += $ticket->overstayDiscount;
                            }
                        }
                        // $rowKey++;
                        $finalCodes5[$temp_index]['Gross Amount ($)'] += $temp_overstayAmount;
                        $finalCodes5[$temp_index]['Discount Amount ($)'] += $temp_overstayDiscountAmount;

                        $TotalTicketAmount                  += '0.00';
                        $netValueNonValidated               += '0.00';
                        $totalDriveUpDuration               += $value->lenghtInMints;
                        $totalTicketsNonValidated           += $value->ticketCount;
                        $TotalRevenueNonValidated           += floatval($value->Pay_grand_total + $temp_overstayAmount);;
                        $processingFeeNonValidated          += floatval($value->processing_fee);
                        $validatedAmountNonValidated        += floatval($value->validated_amount);
                        $totalDiscountAmountNonValidated    += floatval($value->discount_amount  + $temp_overstayDiscountAmount);
                    }
                    // dd($totalDiscountAmountNonValidated);
                    $this->log->info('WLA 33. ');

                    // Reservation Data
                    $dataforCategoryWiseCheckin = $dataforCategoryWiseData['checkin'][0];
                    $validatedCheckinAmountNonValidated = $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = $discountAmount = $processingFee = $grossAmount = $discountAmountCheckin = 0;
                    $rowKey = 0;
                    // dd(count($dataforCategoryWiseCheckin));
                    if (count($dataforCategoryWiseCheckin) > 0) {
                        foreach ($dataforCategoryWiseCheckin as $key => $value) {
                            $checkinArray[$rowKey]['Rate ($)']              = floatval($value->payAmount);
                            $checkinArray[$rowKey]['Ticket Number']         = '';
                            $checkinArray[$rowKey]['Booking ID']            = ''; ///$value->ticketech_code;
                            $checkinArray[$rowKey]['No of Tickets']         = intval($value->ticketCount);
                            $checkinArray[$rowKey]['Duration (Hours)']      = intval($value->lenghtInMints);
                            $checkinArray[$rowKey]['Ticket Amount ($)']     = floatval('2.00');
                            $checkinArray[$rowKey]['Gross Amount ($)']      = floatval($value->Pay_grand_total + $value->overstayGrandTotal);
                            // $checkinArray[$rowKey]['Processing Fees ($)'] = floatval($dataforCategoryWise[$rowKey]['faidprocessing_fee']);
                            // $checkinArray[$rowKey]['Net Amount ($)'] = floatval($dataforCategoryWise[$rowKey]['fnet_amount']);
                            $checkinArray[$rowKey]['Discount Amount ($)'] = floatval($value->discount_amount  + $value->overstayDiscount);

                            $checkinDriveupDataTickets = "SELECT count(r.id) AS ticketCount,
                                r.total AS grand_total,
                                r.total  AS payAmount,  
                                r.tax_fee   AS tax_fee,   
                                r.processing_fee AS processing_fee,
                                r.total AS Pay_grand_total,
                                r.parking_amount AS net_amount,
                                r.discount AS discount_amount,
                                t.ticket_number,
                                r.ticketech_code, 
                                CASE
                                    WHEN t.is_overstay = '1' THEN CEIL(COALESCE(t.length, 0) + COALESCE(ot.length, 0))
                                    ELSE CEIL(COALESCE(r.length, 0))
                                END AS lenghtInMints,
                                sum(ot.grand_total)  as overstayGrandTotal, 
                                sum(ot.discount_amount) as overstayDiscount
                                from reservations as r
                                inner join anet_transactions as ant on ant.id = r.anet_transaction_id
                                inner join facilities as f on f.id = r.facility_id
                                left join tickets as t on t.reservation_id = r.id
                                left join overstay_tickets as ot on t.id = ot.ticket_id
                                where  r.partner_id IN (" . $this->partnerId . ") and r.start_timestamp >='" . $this->checkinTime . "' and r.start_timestamp <='" . $this->checkoutTime . "'  AND r.facility_id = '" . $this->facilityId . "' and t.deleted_at is null and r.id IN (" . $value->reservation_ids . ") group by r.id order by r.id";
                            $this->log->info('WLA 33 Query . ' . $checkinDriveupDataTickets);
                            $chekinDriveupDataTicketResults = DB::select($checkinDriveupDataTickets);

                            if (count($chekinDriveupDataTicketResults) > 0 && $this->isSummaryEnable) {
                                $rowKey++;
                                foreach ($chekinDriveupDataTicketResults as $tkey => $ticket) {
                                    $checkinArray[$rowKey]['Rate ($)']              = '';
                                    $checkinArray[$rowKey]['Ticket Number']         = $ticket->ticket_number;;
                                    $checkinArray[$rowKey]['Booking ID']            = $ticket->ticketech_code;
                                    $checkinArray[$rowKey]['No of Tickets']         = $ticket->ticketCount;
                                    $checkinArray[$rowKey]['Duration (Hours)']      = intval($ticket->lenghtInMints);
                                    $checkinArray[$rowKey]['Ticket Amount ($)']     = floatval($showTicketAmount);
                                    $checkinArray[$rowKey]['Gross Amount ($)']      = floatval($ticket->Pay_grand_total + $ticket->overstayGrandTotal);
                                    $checkinArray[$rowKey]['Discount Amount ($)']   = floatval($ticket->discount_amount  + $ticket->overstayDiscount);
                                    $rowKey++;
                                }
                            }
                            // $rowKey++;

                            $TotalTicketAmount          += floatval($value->payAmount);
                            $processingFeeNonValidated  += floatval($value->processing_fee);
                            $totalReservationDurtion    += $value->lenghtInMints;
                            $totalTicketsCheckinNonValidated += $value->ticketCount;
                            $TotalCheckinRevenueNonValidated += floatval($value->Pay_grand_total + $value->overstayGrandTotal);
                            $netValueNonValidated += 0;
                            $validatedCheckinAmountNonValidated = floatval($value->discount_amount  + $value->overstayDiscount);
                        }
                    } else {
                        $checkinArray[$rowKey]['Rate ($)']              = '';
                        $checkinArray[$rowKey]['Ticket Number']         = '';
                        $checkinArray[$rowKey]['No of Tickets']         = '';
                        $checkinArray[$rowKey]['Duration (Hours)']      = '';
                        $checkinArray[$rowKey]['Ticket Amount ($)']     = '';
                        $checkinArray[$rowKey]['Gross Amount ($)']      = '';
                        $checkinArray[$rowKey]['Discount Amount ($)']   = '';
                    }
                    $this->log->info('WLA 44. ' . json_encode($checkinArray));

                    // Cash or Offline payment
                    $driveUpOfflinePayment = "SELECT t.grand_total AS sum_offline_amount, t.discount_amount as discountAmount,t.is_offline_payment,t.partner_id ,t.processing_fee as processingFee,ot.grand_total as overstayGrandTotal, ot.discount_amount as overstayDiscount
                    FROM tickets AS t
                    left join overstay_tickets as ot on t.id = ot.ticket_id
                    where  t.partner_id in (" . $this->partnerId . ") and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "'  and t.paid_type='9'  and t.is_checkout ='1' and t.deleted_at is null and (t.is_offline_payment IN('1') or ot.is_offline_payment IN('1')) AND t.facility_id = " . $this->facilityId . " order by t.id";
                    $driveUpCashReport = DB::select($driveUpOfflinePayment);
                    foreach ($driveUpCashReport as $key => $value) {
                        if ($value->is_offline_payment == 1) {
                            $finalCodes2[0]['payment_type'] = 'Cash';
                            $finalCodes2[0]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                            $finalCodes2[0]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                        } else if ($value->is_offline_payment == 2 || $value->is_offline_payment == 3) {
                            $finalCodes2[1]['payment_type'] = 'Card';
                            $finalCodes2[1]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                            $finalCodes2[1]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                        }
                        $totalCashServiceAmount += $value->processingFee;
                        // $cashticketCount = $cashticketCount + $value->ticketCount;
                        $TotalPaymentReceived +=  $value->sum_offline_amount + $value->overstayGrandTotal;
                        $TotalValidatedAmount +=  $value->discountAmount + $value->overstayDiscount;
                    }

                    ##########################################################
                    // Non Cash or card breakdown
                    $DriveUpnonCashPayment = "SELECT CASE
                      WHEN card_type IN ('MC', 'MASTERCARD') THEN 'MASTERCARD'
                      when card_type IN ('VS','VISA') THEN 'VISA'
                      WHEN checkout_card_type IN ('MC', 'MASTERCARD') THEN 'MASTERCARD'
                      when checkout_card_type IN ('VS','VISA','VISA CREDIT') THEN 'VISA'
                      when checkout_card_type IN ('AMEX') THEN 'AMEX'
                      ELSE card_type
                      END AS combined_card_type, SUM(t.grand_total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                      (SELECT sum(overstay_tickets.grand_total) FROM overstay_tickets where ticket_id IN (t.id)) as overstayGrandTotal,
                      (SELECT sum(overstay_tickets.discount_amount) FROM overstay_tickets where ticket_id IN (t.id)) as overstayDiscount
                      FROM tickets as t
                      WHERE t.partner_id IN (" . $this->partnerId . ") AND t.checkout_time >='" . $this->checkinTime . "' AND t.checkout_time <='" . $this->checkoutTime . "' AND t.is_offline_payment IN ('0','2','3') and t.deleted_at is null  and t.is_checkout ='1' AND t.facility_id = " . $this->facilityId . " GROUP BY combined_card_type,t.is_overstay"; #td.rate_description     
                    $driveUpCCReport = DB::select($DriveUpnonCashPayment);

                    // Calculate reservation Payment             
                    // Non Cash or card breakdown
                    $checkinDriveUpnonCashPayment = "SELECT CASE
                        when ant.card_type IN ('VS','VISA') THEN 'VISA'
                        WHEN ant.card_type IN ('MC', 'MASTERCARD') THEN 'MASTERCARD'
                        when ant.card_type IN ('DCVR') THEN 'DCVR'
                        WHEN ant.card_type IN ('AMEX') THEN 'AMEX'	
                        ELSE ant.card_type
                        END AS combined_card_type,count(r.id) as ticketCount,t.ticket_number, #r.ticketech_code,
                        SUM(r.total) as total_amount,SUM(r.discount) as discountAmount, SUM(r.processing_fee) as processingFee,SUM(r.tax_fee) as tax_fee,
                        (SELECT sum(overstay_tickets.grand_total) FROM overstay_tickets where ticket_id IN (t.id)) as overstayGrandTotal,
                        (SELECT sum(overstay_tickets.discount_amount) FROM overstay_tickets where ticket_id IN (t.id)) as overstayDiscount
                        FROM reservations as r
                        left join anet_transactions as ant on ant.id = r.anet_transaction_id
                        left join tickets as t on t.reservation_id = r.id 
                        WHERE r.partner_id IN (" . $this->partnerId . ") AND r.start_timestamp >='" . $this->checkinTime . "' AND r.start_timestamp <='" . $this->checkoutTime . "' AND r.deleted_at is null AND r.facility_id = '" . $this->facilityId . "'  GROUP BY combined_card_type"; #td.rate_description     

                    $checkinDriveUpCCReport = DB::select($checkinDriveUpnonCashPayment);
                    // dd($finalCodes2);
                    //CC Report sheet data
                    // $excelSheetName3 = ucwords(str_replace(' ', '', 'Cc Amount ')) . date('m-d-Y');
                    $finalCodes3 = [];
                    $TotalCc = 0;
                    $ccticketCount = 0;

                    // $ccReportCount = count($driveUpCCReport);
                    $cards = ['AMEX', 'DCVR', 'MASTERCARD', 'VISA'];
                    foreach ($cards as $cardkey => $card) {
                        $totalcardPay = $grossAmount = $processingFees = $netAmonut = $discountAmount = $ticketCount = 0;
                        foreach ($driveUpCCReport as $key => $value) {
                            if ($card == $value->combined_card_type) {
                                if ($value->total_amount <= 0) continue;
                                $ticketCount += intval($value->ticketCount);
                                $discountAmount += floatval($value->discountAmount + $value->overstayDiscount);
                                $processingFees += floatval($value->processingFee);
                                $totalcardPay += floatval($value->total_amount + $value->overstayGrandTotal);

                                $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                            }
                            // $TotalPaymentReceived = $TotalPaymentReceived+$value->total_amount;
                        }

                        if (isset($checkinDriveUpCCReport) && count($checkinDriveUpCCReport) > 0) {
                            foreach ($checkinDriveUpCCReport as $key => $value) {
                                if ($card == $value->combined_card_type) {
                                    $totalcardPay += floatval($value->total_amount + $value->overstayGrandTotal);

                                    $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                    $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                }
                            }
                        }


                        $TotalCc += $totalcardPay;
                        $ccticketCount += $ticketCount;
                        $totalCashServiceAmount += $processingFees;
                        $TotalValidatedAmount += $discountAmount;
                    }

                    $sql_query4 = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                        sum(t.total) as total,                
                        sum(t.grand_total) as paidAmount,                
                        sum(t.parking_amount) as parking_amount,
                        sum(t.paid_amount) as validated_amount,
                        sum(t.discount_amount) as discount_amount,
                        t.affiliate_business_id, ab.business_name as BusinessName
                        FROM tickets as t        
                        inner join affiliate_business as ab on ab.id = t.affiliate_business_id 
                        WHERE t.partner_id in (" . $this->partnerId . ") and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "'  and t.affiliate_business_id is not null and paid_by > 0 AND t.facility_id = " . $this->facilityId . " GROUP BY t.affiliate_business_id order by t.affiliate_business_id";
                    $validationReport = DB::select($sql_query4);

                    $count = $rCount = 0;
                    //Non Revenue Report data
                    $excelSheetName4 = ucwords('Non Revenue');
                    $excelSheetName5 = ucwords('Tickets Cashiered');
                    $finalCodes4 = [];
                    $reservationTickets = [];

                    $validationReportCount = count($validationReport);
                    $i = 0;
                    $validationAmountTotal = 0;

                    $validationPaidAmountTotal = 0;
                    $totalGrossAmount = 0;
                    $validationTicketTotal = 0;
                    $validatedGTotal = 0;
                    $totalNetAmount = 0;
                    $totalServiceAmount = 0;
                    $validatedTotal = 0;
                    $gTotal = 0;
                    foreach ($validationReport as $key => $value) {

                        $finalCodes4[$i]['Business Name'] = $value->BusinessName;
                        $finalCodes4[$i]['Policy Name'] = '-';
                        $finalCodes4[$i]['No of Tickets'] = floatval($value->ticket_count);
                        $finalCodes4[$i]['Gross Amount ($)'] = floatval($value->total);
                        $finalCodes4[$i]['Processing Fee ($)'] = floatval($value->processingFee);
                        $finalCodes4[$i]['Net Amount ($)'] = floatval($value->total - $value->processingFee - $value->discount_amount);
                        $finalCodes4[$i]['Validated Amount ($)'] = floatval($value->validated_amount);
                        $finalCodes4[$i]['Paid Amount ($)'] = floatval($value->paidAmount);
                        $finalCodes4[$i]['Total Revenue ($)'] =  floatval($value->paidAmount + $value->validated_amount);


                        // $gTotal = $value->paidAmount + $value->validated_amount;
                        $grossTotal = ($value->parking_amount + $value->processingFee) * $value->ticket_count;
                        // $serviceFee = floatval($value->processingFee * $value->ticket_count);

                        $validationTicketTotal  += $value->ticket_count;
                        $totalGrossAmount       += floatval($value->total);
                        $totalServiceAmount     += floatval($value->processingFee);
                        $totalNetAmount         += floatval($value->total - $value->processingFee - $value->discount_amount);
                        $validationAmountTotal  += floatval($value->validated_amount);
                        $validationPaidAmountTotal += floatval($value->paidAmount);
                        $validatedGTotal += floatval($value->paidAmount + $value->validated_amount);;

                        // $totalGrossAmount += $grossTotal;


                        // policy query according to business
                        $policy_query = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                                    sum(t.total) as total,
                                    sum(t.grand_total) as paidAmount,
                                    sum(t.parking_amount) as parking_amount,
                                    sum(t.discount_amount) as discount_amount,
                                    sum(t.paid_amount) as validated_amount,t.affiliate_business_id,t.policy_id,
                                    p.policy_name as policyName,
                                    ab.business_name as BusinessName
                                    FROM tickets as t
                                    inner join affiliate_business as ab on ab.id = t.affiliate_business_id
                                    inner join business_policy as p on p.id = t.policy_id 
                                    WHERE t.user_id > 0  and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "' and t.partner_id IN ('" . $this->partnerId . "') and t.affiliate_business_id =$value->affiliate_business_id and t.paid_by > 0 GROUP BY t.affiliate_business_id,t.policy_id order by t.affiliate_business_id";

                        $policyReport = DB::select($policy_query);

                        if (isset($policyReport) && !empty($policyReport)) {
                            $i++;
                            foreach ($policyReport as $k => $policy) {
                                // $gTotal = $policy->paidAmount + $policy->validated_amount;
                                // $grossTotal = ($policy->parking_amount + $policy->processingFee) * $policy->ticket_count;
                                // $serviceFee = floatval($policy->processingFee * $policy->ticket_count);
                                $finalCodes4[$i]['Business Name'] = '';
                                $finalCodes4[$i]['Policy Name'] = $policy->policyName;
                                $finalCodes4[$i]['No of Ticket'] = floatval($policy->ticket_count);
                                $finalCodes4[$i]['Gross Total ($)'] = floatval($policy->total);
                                $finalCodes4[$i]['Processing Fee ($)'] = floatval($policy->processingFee);
                                $finalCodes4[$i]['Net Amount ($)'] = floatval($policy->total - $policy->processingFee - $value->discount_amount);
                                $finalCodes4[$i]['Validation Amount ($)'] = floatval($policy->validated_amount);
                                $finalCodes4[$i]['Paid Amount ($)'] = floatval($policy->paidAmount);
                                $finalCodes4[$i]['Total Revenue ($)'] =  floatval($policy->paidAmount + $policy->validated_amount);
                                $i++;
                            }
                        }
                        $i++;
                    }
                    $reservationAmount = $rTticketCount = $rTicketAmount = $rGrossAmount = $rProcessingFees = $rNetAmonut = $rDiscountAmount = 0;
                    $categoryCount = 0;


                    // $excelSheetName = ucwords(str_replace(' ', '', 'Daily Revenue Report ')) . date('m-d-Y');
                    $excelSheetName = $shortName . '-Daily Revenue Report-' . date('m-d-Y');
                    //
                    Excel::create(
                        $excelSheetName,
                        function ($excel) use (
                            $color,
                            $finalCodes2,
                            $count,
                            $locationName,
                            $garageCode,
                            $TotalPaymentReceived,
                            $finalCodes3,
                            $TotalCc,
                            $finalCodes4,
                            $validationPaidAmountTotal,
                            $totalServiceAmount,
                            $validationAmountTotal,
                            $validationTicketTotal,
                            $totalGrossAmount,
                            $TotalValidatedAmount,
                            $validatedGTotal,
                            $processingFeeNonValidated,
                            $finalCodes5,
                            $checkinArray,
                            $excelSheetName5,
                            $totalTicketsNonValidated,
                            $TotalRevenueNonValidated,
                            $validatedAmountNonValidated,
                            $validatedCheckinAmountNonValidated,
                            $TotalCheckinRevenueNonValidated,
                            $totalTicketsCheckinNonValidated,
                            $rTticketCount,
                            $totalDriveUpDuration,
                            $totalReservationDurtion,
                            $shortName,
                            $totalDiscountAmountNonValidated
                        ) {
                            if (isset($finalCodes5) && !empty($finalCodes5)) {
                                // Tickets Cashiered 
                                $excel->sheet($excelSheetName5, function ($sheet5) use (
                                    $finalCodes5,
                                    $checkinArray,
                                    $color,
                                    $totalTicketsNonValidated,
                                    $TotalRevenueNonValidated,
                                    $locationName,
                                    $garageCode,
                                    $validatedAmountNonValidated,
                                    $processingFeeNonValidated,
                                    $finalCodes2,
                                    $count,
                                    $TotalPaymentReceived,
                                    $finalCodes3,
                                    $TotalCc,
                                    $finalCodes4,
                                    $validationPaidAmountTotal,
                                    $totalServiceAmount,
                                    $validationAmountTotal,
                                    $validationTicketTotal,
                                    $totalGrossAmount,
                                    $TotalValidatedAmount,
                                    $validatedGTotal,
                                    $validatedCheckinAmountNonValidated,
                                    $TotalCheckinRevenueNonValidated,
                                    $totalTicketsCheckinNonValidated,
                                    $rTticketCount,
                                    $totalDriveUpDuration,
                                    $totalReservationDurtion,
                                    $shortName,
                                    $totalDiscountAmountNonValidated
                                ) {
                                    $driveupTotal = 10;
                                    $topSpace = 7;

                                    $sheet5->setWidth(array(
                                        'A'     => 21,
                                        'B'     =>  24,
                                        'C'     =>  16,
                                        'D'     =>  17.57,
                                        'E'     =>  17.34,
                                        'F'     =>  15.57,
                                        'G'    =>   21,
                                        'H'    =>   16.86,
                                        'I'    =>   18
                                    ));

                                    $sheet5->cell('A6:I6', function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                    });
                                    $colorCode = count($finalCodes5) + 7;

                                    //  print_r($countNonValidated);die;

                                    $row_name = 'A' . $colorCode . ':I' . $colorCode;
                                    $sheet5->cell($row_name, function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');

                                        $row->setFont(array(
                                            'family'     => 'Calibri',
                                            'size'       => '12',
                                            'bold'       =>  true
                                        ));
                                    });


                                    // $sheet5->row(1, 'Daily Revenue Report');
                                    // define width colom A,B, witdh static here
                                    $sheet5->getColumnDimension('A')->setWidth(21);
                                    $sheet5->getColumnDimension('B')->setWidth(24);
                                    $sheet5->getColumnDimension('C')->setWidth(16);
                                    $sheet5->getColumnDimension('D')->setWidth(17.57);
                                    $sheet5->getColumnDimension('E')->setWidth(17.34);
                                    $sheet5->getColumnDimension('F')->setWidth(15.57);
                                    $sheet5->getColumnDimension('G')->setWidth(21);
                                    $sheet5->getColumnDimension('H')->setWidth(16.86);
                                    $sheet5->getColumnDimension('I')->setWidth(18);

                                    $sheet5->getStyle("A")->getNumberFormat()->setFormatCode('0.00');
                                    // $sheet5->getStyle("C")->getNumberFormat()->setFormatCode('0.00');
                                    // $sheet5->getStyle("D")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("H")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("I")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("C3")->getNumberFormat()->setFormatCode('0');

                                    //end width for colom here
                                    //set header for excel work                                   
                                    $this->addLogoInExcelHeader($sheet5);

                                    $sheet5->mergeCells('A2:C2');
                                    if (date('Y-m-d', strtotime($this->checkinTime)) == date('Y-m-d', strtotime($this->checkoutTime))) {
                                        $cellValue = "Report Date - " .  date('m-d-Y', strtotime($this->checkinTime));
                                    } else {

                                        $cellValue = "Report Date Range - " .  date('m-d-Y', strtotime($this->checkinTime)) .  ' - ' . date('m-d-Y', strtotime($this->checkoutTime));
                                    }
                                    $cellValue .= "\nPrint Date - " .  date('m-d-Y', strtotime('now'));
                                    $sheet5->setCellValue('A2', "$cellValue");
                                    $sheet5->getStyle('A2')->getAlignment()->setWrapText(true);
                                    // Set the height of cell H2 (adjust as needed)
                                    $sheet5->getRowDimension(2)->setRowHeight(80);
                                    $sheet5->getRowDimension(3)->setRowHeight(50);
                                    // Set the height to 40 units
                                    // Set the width of cell H2 (adjust as needed)
                                    // $sheet5->getColumnDimension('H')->setWidth(20);
                                    $sheet5->cell('A2:C2', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center'); // Center vertically
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D6DCE4');
                                        $cell->setFontColor('#000000');
                                        $cell->setFontSize('16');
                                    });

                                    $location = "Location Name \r" . $locationName;
                                    $sheet5->mergeCells('D2:G2');
                                    $sheet5->setCellValue('D2', "$location");
                                    $sheet5->getStyle('D2')->getAlignment()->setWrapText(true);

                                    $sheet5->cell('D2:G2', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center'); // Center vertically
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D6DCE4');
                                        $cell->setFontColor('#000000');
                                        $cell->setFontSize('18');
                                    });
                                    $sheet5->mergeCells('H2:I2');
                                    $locationId = "Location ID \n" . $garageCode;
                                    $sheet5->setCellValue('H2', "$locationId");
                                    $sheet5->getStyle('H2')->getAlignment()->setWrapText(true);

                                    $sheet5->cell('H2:I2', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center'); // Center vertically
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D6DCE4');
                                        $cell->setFontColor('#040D12');
                                        $cell->setFontSize('18');
                                    });

                                    $sheet5->mergeCells('A3:B3');
                                    $sheet5->setCellValue('A3', "Transient Counter");
                                    $sheet5->setBorder('A3:I3', 'thin', '#ffffff');
                                    // $sheet5->setBorderColor('white');
                                    $sheet5->cell('A3:B3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('18');
                                    });

                                    $sheet5->mergeCells('C3:E3');
                                    if ($totalTicketsCheckinNonValidated > 0) {
                                        $totalTicketsNonValidated += $totalTicketsCheckinNonValidated;
                                    }

                                    $sheet5->setCellValue('C3', $totalTicketsNonValidated + $validationTicketTotal + $rTticketCount);
                                    $sheet5->cell('C3:E3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('18');
                                    });

                                    $sheet5->mergeCells('F3:H3');
                                    $sheet5->setCellValue('F3', "Ticket Revenue ($)");
                                    $sheet5->cell('F3:H3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('18');
                                    });
                                    if ($totalTicketsCheckinNonValidated > 0) {
                                        $totalTicketsNonValidated -= $totalTicketsCheckinNonValidated;
                                    }
                                    // $sheet5->mergeCells('I3:J3');
                                    // $TotalRevenueNonValidated += $TotalCheckinRevenueNonValidated;
                                    $sheet5->setCellValue('I3', $TotalRevenueNonValidated + $TotalCheckinRevenueNonValidated);
                                    // $TotalRevenueNonValidated -= $TotalCheckinRevenueNonValidated;

                                    $sheet5->cell('I3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('18');
                                    });

                                    $reservationCount = $topSpace + count($finalCodes5) + 2;
                                    $sheet5->mergeCells('A4:I4');
                                    $sheet5->mergeCells('A5:I5');
                                    $sheet5->setCellValue('A5', 'Tickets Cashiered DriveUp');

                                    $sheet5->cell('A5', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D9E1F2');
                                        $cell->setFontColor('#272829');
                                        $cell->setFontSize('12');
                                    });

                                    $sheet5->fromArray($finalCodes5, [], 'A6', false, true);  // this is use in case of not ticket listing

                                    // now color for the rows that grouped
                                    $rowNumber = 0;
                                    // print_r(json_encode($finalCodes5));
                                    foreach ($finalCodes5 as $rowkey => $tickets) {
                                        if (strlen($tickets['Ticket Number']) == '') {
                                            $colorCoderowkey =  $rowkey + $topSpace;
                                            $row_name = 'A' . $colorCoderowkey . ':I' . $colorCoderowkey;
                                            $sheet5->cell($row_name, function ($row) use ($color) {
                                                $row->setFontColor('#272829');
                                                $row->setFont(array(
                                                    'family'     => 'Calibri',
                                                    'size'       => '12',
                                                    'bold'       =>  true
                                                ));
                                            });
                                        }
                                    }


                                    $colorRowKey = $topSpace;
                                    foreach ($finalCodes5 as $key => $value) {
                                        $sheet5->cell('A' . $colorRowKey . ':G' . $colorRowKey, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                        });
                                        $colorRowKey++;
                                    }
                                    //for last total result center alligment
                                    $colorRowKey += 1;
                                    $sheet5->cell('A' . $colorRowKey . ':G' . $colorRowKey, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                    });
                                    //end centr aligment

                                    $sheet5->mergeCells('A' . $reservationCount . ':I' . $reservationCount);
                                    $sheet5->setCellValue('A' . $reservationCount, 'Tickets Cashiered Reservation');
                                    $sheet5->cell('A' . $reservationCount, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D9E1F2');
                                        $cell->setFontColor('#272829');
                                        $cell->setFontSize('12');
                                    });
                                    $i = count($finalCodes5) + 10;
                                    // dd($i);
                                    if (count($checkinArray) > 1) {
                                        $sheet5->cell('A' . $i . ':I' . $i, function ($cell) use ($color) {
                                            $cell->setAlignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('12');
                                        });

                                        $sheet5->setCellValue('A' . $i, 'Rate ($)');
                                        $sheet5->setCellValue('B' . $i, 'Ticket Number');
                                        $sheet5->setCellValue('C' . $i, 'Booking ID');
                                        $sheet5->setCellValue('D' . $i, 'No of Tickets');
                                        $sheet5->setCellValue('E' . $i, 'Duration (Hours)');
                                        $sheet5->setCellValue('F' . $i, 'Ticket Amount ($)');
                                        $sheet5->setCellValue('G' . $i, 'Gross Total ($)');
                                        $sheet5->setCellValue('H' . $i, 'Discount Amount ($)');

                                        $i++;
                                        foreach ($checkinArray as $key => $value) {
                                            $sheet5->cell('A' . $i . ':I' . $i, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center');
                                            });

                                            $this->log->info('Checkin data Index : ' . $i . " Tikcet nUmber " . $value['Ticket Number'] . " Booking ID " . $value['Booking ID']);
                                            $sheet5->setCellValue('A' . $i, $value['Rate ($)']);
                                            $sheet5->setCellValue('B' . $i, $value['Ticket Number']);
                                            $sheet5->setCellValue('C' . $i, $value['Booking ID']);
                                            $sheet5->setCellValue('D' . $i, $value['No of Tickets']);
                                            $sheet5->setCellValue('E' . $i, isset($value['Duration (Hours)']) ? $value['Duration (Hours)'] : $value['Duration (Hours)']);
                                            $sheet5->setCellValue('F' . $i, isset($value['Ticket Amount ($)']) ? $value['Ticket Amount ($)'] : $value['Ticket Amount ($)']);
                                            $sheet5->setCellValue('G' . $i, isset($value['Gross Amount ($)']) ? number_format($value['Gross Amount ($)'], 2) : number_format($value['Gross Total ($)'], 2));
                                            $sheet5->setCellValue('H' . $i, $value['Discount Amount ($)']);
                                            $i++;
                                        }
                                    } else {
                                        $sheet5->cell('A' . $i . ':I' . $i, function ($cell) use ($color) {
                                            $cell->setAlignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('12');
                                        });
                                        $sheet5->setCellValue('A' . $i, 'Rate ($)');
                                        $sheet5->setCellValue('B' . $i, 'Ticket Number');
                                        $sheet5->setCellValue('C' . $i, 'Booking ID');
                                        $sheet5->setCellValue('D' . $i, 'No of Tickets');
                                        $sheet5->setCellValue('E' . $i, 'Duration (Hours)');
                                        $sheet5->setCellValue('F' . $i, 'Ticket Amount ($)');
                                        $sheet5->setCellValue('G' . $i, 'Gross Amount ($)');
                                        $sheet5->setCellValue('H' . $i, 'Discount Amount ($)');
                                    }

                                    $sheet5->cell('A' . $i . ':G' . $i, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                    });


                                    // Drive up Total 
                                    $tcell = count($finalCodes5) + 7;
                                    $sheet5->getStyle("F" . $tcell)->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("G" . $tcell)->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->row(count($finalCodes5) + 7, array('Total', '', $totalTicketsNonValidated, $totalDriveUpDuration, '',  $TotalRevenueNonValidated, $totalDiscountAmountNonValidated));
                                    $colorCode = count($finalCodes5) + count($checkinArray) + 11;
                                    //for centr alligment
                                    $centreColomNumber = count($finalCodes5) + 7;
                                    $sheet5->cell('A' . $centreColomNumber . ':G' . $centreColomNumber, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                    });
                                    //end centre alligment

                                    // Reservation Total
                                    $sheet5->row(count($finalCodes5) + count($checkinArray) + 11, array('Total', '', '', $totalTicketsCheckinNonValidated,  $totalReservationDurtion, '', $TotalCheckinRevenueNonValidated, $validatedCheckinAmountNonValidated));
                                    //for centre total coun
                                    $centreColomNumber1 = count($finalCodes5) + count($checkinArray) + 11;
                                    $sheet5->cell('A' . $centreColomNumber1 . ':G' . $centreColomNumber1, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                    });
                                    //for centr
                                    $revenuSheetRowStart = count($finalCodes5) + count($checkinArray) + 13;
                                    $rowNumber = count($finalCodes5) + count($checkinArray) + 14;



                                    $sheet5->cell('A' . $rowNumber . ':I' . $rowNumber, function ($cell) use ($color) {
                                        $cell->setAlignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('12');
                                    });
                                    $sheet5->mergeCells('A' . $revenuSheetRowStart . ':I' . $revenuSheetRowStart);
                                    // $sheet5->mergeCells('E' .$revenuSheetRowStart .':I' .$revenuSheetRowStart);
                                    $sheet5->setCellValue('A' . $revenuSheetRowStart, 'Non Revenue Tickets');
                                    $sheet5->cell('A' . $revenuSheetRowStart, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D9E1F2');
                                        $cell->setFontColor('#272829');
                                        $cell->setFontSize('12');
                                    });

                                    $count = count($finalCodes4);

                                    $colorCode = $count + $rowNumber + 1;
                                    $row_name = 'A' . $colorCode . ':I' . $colorCode;
                                    $sheet5->cell($row_name, function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                        $row->setFont(array(
                                            'family'     => 'Calibri',
                                            'size'       => '12',
                                            'bold'       =>  true
                                        ));
                                    });


                                    $colorCode = count($finalCodes5) + count($checkinArray) + 11;
                                    $row_name = 'A' . $colorCode . ':I' . $colorCode;
                                    $sheet5->cell($row_name, function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                        $row->setFont(array(
                                            'family'     => 'Calibri',
                                            'size'       => '12',
                                            'bold'       =>  true
                                        ));
                                    });


                                    // end non revenu data report here
                                    // $sheet5->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
                                    $x = count($finalCodes4) + $rowNumber;
                                    $sheet5->setCellValue('A' . $rowNumber, 'Business Name');
                                    $sheet5->setCellValue('B' . $rowNumber, 'Policy Name');
                                    $sheet5->setCellValue('C' . $rowNumber, 'No of Tickets');
                                    $sheet5->setCellValue('D' . $rowNumber, 'Gross Amount ($)');
                                    // $sheet5->setCellValue('E' . $rowNumber, 'Processing Fee ($)');
                                    // $sheet5->setCellValue('F' . $rowNumber, 'Net Amount ($)');
                                    $sheet5->setCellValue('E' . $rowNumber, 'Validated Amount ($)');
                                    $sheet5->setCellValue('F' . $rowNumber, 'Paid Amount ($)');
                                    $sheet5->setCellValue('G' . $rowNumber, 'Total Revenue ($)');

                                    $i = $rowNumber + 1;
                                    foreach ($finalCodes4 as $key => $value) {
                                        $sheet5->getStyle("D" . $i)->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->setCellValue('A' . $i, $value['Business Name']);
                                        $sheet5->setCellValue('B' . $i, $value['Policy Name']);
                                        $sheet5->setCellValue('C' . $i, isset($value['No of Tickets']) ? $value['No of Tickets'] : $value['No of Ticket']);
                                        $sheet5->setCellValue('D' . $i, isset($value['Gross Amount ($)']) ? number_format($value['Gross Amount ($)'], 2) : number_format($value['Gross Total ($)'], 2));
                                        // $sheet5->setCellValue('E' . $i, isset($value['Processing Fee ($)']) ? $value['Processing Fee ($)'] : $value['Processing Fee ($)']);
                                        // $sheet5->setCellValue('F' . $i, $value['Net Amount ($)']);
                                        $sheet5->setCellValue('E' . $i, isset($value['Validated Amount ($)']) ? $value['Validated Amount ($)'] : $value['Validation Amount ($)']);
                                        $sheet5->setCellValue('F' . $i, $value['Paid Amount ($)']);
                                        $sheet5->setCellValue('G' . $i, $value['Total Revenue ($)']);
                                        $i++;
                                    }
                                    $sheet5->getStyle("D" . $i)->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->setCellValue('A' . $i, '');
                                    $sheet5->setCellValue('B' . $i, 'Total');
                                    $sheet5->setCellValue('C' . $i, $validationTicketTotal);
                                    $sheet5->setCellValue('D' . $i, $totalGrossAmount);
                                    // $sheet5->setCellValue('E' . $i, $totalServiceAmount);
                                    // $sheet5->setCellValue('F' . $i, $totalNetAmount);
                                    $sheet5->setCellValue('E' . $i, floatval($validationAmountTotal));
                                    $sheet5->setCellValue('F' . $i,  $validationPaidAmountTotal);
                                    $sheet5->setCellValue('G' . $i, $validatedGTotal);
                                    $sheet5->cell('A' . $i . ':G' . $i, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                    });
                                    //payment breakdown issue
                                    $jCell = $i + 2;

                                    $sheet5->mergeCells('A' . $jCell . ':I' . $jCell);
                                    //$sheet5->mergeCells('E' . $jCell . ':I' . $jCell);

                                    $sheet5->setCellValue('A' . $jCell, 'Payment breakdown');

                                    $sheet5->cell('A' . $jCell, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D9E1F2');
                                        $cell->setFontColor('#272829');
                                        $cell->setFontSize('12');
                                    });



                                    // // Total Payment Received
                                    // $totalPayment = $TotalPaymentReceived + $TotalCc;
                                    $totalPayment = $TotalRevenueNonValidated + $TotalCheckinRevenueNonValidated;
                                    // $totalPayment = $grossAmountNonValidated;
                                    $newCell = $jCell + 1;
                                    $sheet5->getStyle('B' . $newCell)->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->setCellValue('A' . $newCell, 'Total Revenue ($)');
                                    $sheet5->setCellValue('B' . $newCell, $totalPayment + $validationAmountTotal + $TotalValidatedAmount);

                                    // total validated Amount
                                    $ValidatedCell = $jCell + 2;
                                    $sheet5->getStyle('B' . $ValidatedCell)->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->setCellValue('A' . $ValidatedCell, 'Total Validated Amt ($)');
                                    $sheet5->setCellValue('B' . $ValidatedCell, $validationAmountTotal);

                                    $sheet5->setCellValue('E' . $newCell, 'Total Payment Received ($)');
                                    $sheet5->setCellValue('F' . $newCell, $totalPayment);

                                    $sheet5->setCellValue('E' . $ValidatedCell, 'Discount Amount ($)');
                                    $sheet5->setCellValue('F' . $ValidatedCell, floatval($TotalValidatedAmount));

                                    /* $sheet5->getStyle('B' . $newCell)->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->setCellValue('A' . $newCell, 'Total Payment Received ($)');
                                    $sheet5->setCellValue('B' . $newCell, $totalPayment);
                                    // total validated Amount
                                    $ValidatedCell = $jCell + 2;
                                    $sheet5->getStyle('B' . $ValidatedCell)->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->setCellValue('A' . $ValidatedCell, 'Total Validated Amt ($)');
                                    $sheet5->setCellValue('B' . $ValidatedCell, $validationAmountTotal);

                                    $sheet5->setCellValue('E' . $newCell, 'Total Revenue ($)');
                                    $sheet5->setCellValue('F' . $newCell, $totalPayment + $validationAmountTotal);

                                    $sheet5->setCellValue('E' . $ValidatedCell, 'Net Revenue ($)');
                                    $sheet5->setCellValue('F' . $ValidatedCell, floatval(($totalPayment + $validationAmountTotal) - ($totalServiceAmount + $processingFeeNonValidated))); */



                                    $nonCashCell = $ValidatedCell + 2;
                                    // print_r($nonCashCell);die;
                                    $sheet5->cell('A' . $nonCashCell . ':I' . $nonCashCell, function ($row) use ($color) {
                                        $row->setFontColor('#ffffff');
                                        $row->setBackground($color);
                                        $row->setFont(array(
                                            'family'     => 'Calibri',
                                            'size'       => '12',
                                            'bold'       =>  true
                                        ));

                                        $row->setFontWeight('bold');
                                        $row->setValignment('center');
                                    });

                                    $sheet5->row($nonCashCell, array('Non-Cash Receipts'));
                                    $sheet5->setCellValue('D' . $nonCashCell, 'Cash Receipts');
                                    $cardTupeCell = $nonCashCell + 1;
                                    $totalCell = $nonCashCell + 2;
                                    $sheet5->cell($cardTupeCell, function ($cell) use ($color) {
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#EDEDED');
                                        $cell->setFontColor('#272829');
                                        $cell->setFontSize('12');
                                    });

                                    $sheet5->setCellValue('A' . $cardTupeCell, 'Card Type');
                                    $sheet5->setCellValue('B' . $cardTupeCell, 'Total ($)');

                                    $sheet5->setCellValue('D' . $cardTupeCell, 'Payment Type');
                                    $sheet5->setCellValue('E' . $cardTupeCell, 'Total ($)');
                                    // $sheet5->setCellValue('F' . $cardTupeCell, 'Discount ($)');
                                    $i = $cardTupeCell + 1;
                                    $j = $cardTupeCell + 1;
                                    foreach ($finalCodes3 as $key => $value) {
                                        if ($value['total'] > 0) {
                                            $sheet5->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                                            $sheet5->setCellValue('A' . $i, $value['no_cash_receipts']);
                                            $sheet5->setCellValue('B' . $i, $value['total']);
                                            $i++;
                                        }
                                    }
                                    foreach ($finalCodes2 as $key => $val) {
                                        if ($val['total'] > 0) {
                                            $sheet5->setCellValue('D' . $j, $val['payment_type']);
                                            $sheet5->setCellValue('E' . $j, $val['total']);
                                            // $sheet5->setCellValue('F' . $j, $val['discount']);
                                            $j++;
                                        }
                                    }
                                    $cellColor = 'A' . $i . ':B' . $i;
                                    $sheet5->cell($cellColor, function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                        $row->setFont(array(
                                            'family'     => 'Calibri',
                                            'size'       => '12',
                                            'bold'       =>  true
                                        ));
                                    });

                                    $cellColor = 'D' . $j . ':E' . $j;
                                    $sheet5->cell($cellColor, function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                        $row->setFont(array(
                                            'family'     => 'Calibri',
                                            'size'       => '12',
                                            'bold'       =>  true
                                        ));
                                    });
                                    $sheet5->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                                    // echo$i;die;
                                    $sheet5->row($i, array('Total', $TotalCc));
                                    $sheet5->setCellValue('D' . $j, 'Total');
                                    $sheet5->setCellValue('E' . $j, $TotalPaymentReceived);
                                    // $sheet5->setCellValue('F' . $j, ($TotalValidatedAmount));
                                    // //end payment breakdown issue
                                    //merge cell for formatting last data
                                    if ($i < $j) {
                                        $i = $i + 1;
                                    }
                                    $k = $i + 1;
                                    $rowVerticalFormate = 'J1:J' . $k;
                                    $sheet5->mergeCells($rowVerticalFormate);
                                    //horigental cell 
                                    $rowHorizentalFormate = 'A' . $k . ':I' . $k;
                                    $sheet5->mergeCells($rowHorizentalFormate);
                                });
                            }
                        }
                    )->store('xls');
                    $this->log->info('WLA File Save Successfully');
                    $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                    $data['totalTickets']   = $totalTicketsNonValidated;
                    $data['netValue']       = $TotalRevenueNonValidated + $TotalCheckinRevenueNonValidated;
                    $data['overAllTotal']   = $TotalRevenueNonValidated + $TotalCheckinRevenueNonValidated;
                    $data['report_name']    = 'daily_revenue_report';
                    $data['location_name']  = $locationName;
                    $data['themeColor']     = $color;
                    $data['mail_body']      = "PFB summary of today’s Revenue Report. A detailed report is also attached.";

                    Mail::send('townsend.cashier-shift-report', $data, function ($message)  use ($excelSheetName) {
                        // $message->bcc(['<EMAIL>']);
                        $message->to(config('parkengage.townsend.revenue_report_emails'));
                        $message->subject(config('parkengage.townsend.revenue_report_subject'));
                        $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                        $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                        $this->log->info("Mail Sent success with failname :  {$path_to_file}");
                        if (file_exists($path_to_file)) {
                            // $this->log->info("Path Of file and name 333 {$path_to_file}");
                            $message->attach($path_to_file);
                        }
                    });
                } else {
                    $data['totalTickets']   = 0;
                    $data['netValue']       = 0;
                    $data['overAllTotal']   = 0;
                    $data['report_name']    = 'daily_revenue_report';
                    $data['location_name']  = "Wordport ";
                    $data['mail_body']      = "As no tickets were checked-out, the revenue report could not be generated.";
                    Mail::send('daily-report-email.daily-email-format', $data, function ($message) {
                        $message->to(config('parkengage.townsend.revenue_report_emails'));
                        $message->subject('Daily Revenue Report - ' . date('jS F, Y'));
                        $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));

                        $this->log->info('No transaction report email sent for Wordport.');
                    });
                    $this->log->info('No Ticket Found On date : ' . date('jS F, Y'));
                }
                // $this->log->info('WLA 33. ' . json_encode($finalCodes5));


            } else if (in_array($this->facilityId, [153])) {
                // Woodman Daily Revenue Scheduled CRON  

                $count  = 0;
                // $rCount = count($reservationData);
                $excelSheetName = ucwords(str_replace(' ', '', 'Daily Revenue Report'));

                // Non Validated
                $finalCodes5 = [];
                $checkinArray = [];
                $TotalRevenueNonValidated = 0;

                // $totalTicketsNonValidated = 0;
                $netValueNonValidated = 0;
                $validatedAmountNonValidated = 0;
                $processingFeeNonValidated = 0;

                $excelSheetName5 = ucwords('Daily Revenue Report');

                // New code
                //$usersarr = array(1, 3, 12);
                // dd($totalTicketsCheckinNonValidated);
                //offline sheet data
                $finalCodes2 = [];
                $TotalPaymentReceived = 0;
                $TotalValidatedAmount = 0;

                // $cashReportCount = count($driveUpCashReport);
                $finalCodes2[0]['total'] = 0;
                $finalCodes2[0]['discount'] = 0;
                $finalCodes2[1]['total'] = 0;
                $finalCodes2[1]['discount'] = 0;

                $TotalTicketAmount = 0;
                $totalDriveUpDuration = $totalReservationDurtion = 0;
                $totalDiscountAmountNonValidated = $validatedCheckinAmountNonValidated = $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = 0;
                $finalCodes4 = [];
                $reservationTickets = [];
                if (isset($this->facilityId) && !empty($this->facilityId)) {

                    $checkData = "SELECT * from tickets as t where t.facility_id in(" . $this->facilityId . ")  and t.deleted_at is null and  t.partner_id IN ('" . $this->partnerId . "') and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "' and t.paid_type='9' group by t.is_overstay,t.id ";

                    $checkDataResult = DB::select($checkData);
                    if (empty($checkDataResult) && empty($this->facilityId)) {
                        $this->log->info("No Ticket Found on Facility {$this->facilityId} : On date : " . date('Y-m-d'));
                        throw new ApiGenericException('There is no data for this date.');
                    }

                    if ($this->facilityId > 0) {
                        $validationAmountTotal = 0;

                        $validationPaidAmountTotal = 0;
                        $totalGrossAmount = 0;
                        $validationTicketTotal = 0;
                        $validatedGTotal = 0;
                        $totalNetAmount = 0;
                        $totalServiceAmount = 0;
                        $finalCodes3 = [];
                        $TotalCc = 0;
                        $rTticketCount = $rTicketAmount = $rGrossAmount = $rProcessingFees = $rNetAmonut = $rDiscountAmount = 0;
                        $TotalTicketAmount = 0;
                        $allgetedFacility = "";

                        $excelSheetName = $shortName . '-Daily Revenue Report-' . date('m-d-Y');

                        Excel::create(
                            $excelSheetName,
                            function ($excel) use (
                                $color,
                                $finalCodes2,
                                $count,
                                $TotalPaymentReceived,
                                $finalCodes3,
                                $TotalCc,
                                $finalCodes4,
                                $validationPaidAmountTotal,
                                $totalServiceAmount,
                                $validationAmountTotal,
                                $validationTicketTotal,
                                $totalGrossAmount,
                                $TotalValidatedAmount,
                                $validatedGTotal,
                                $totalNetAmount,
                                $processingFeeNonValidated,
                                $finalCodes5,
                                $checkinArray,
                                $excelSheetName5,
                                $totalTicketsNonValidated,
                                $TotalRevenueNonValidated,
                                $netValueNonValidated,
                                $validatedAmountNonValidated,
                                $totalDiscountAmountNonValidated,
                                $validatedCheckinAmountNonValidated,
                                $TotalCheckinRevenueNonValidated,
                                $totalTicketsCheckinNonValidated,
                                $reservationTickets,
                                $totalDriveUpDuration,
                                $totalReservationDurtion,
                                $TotalTicketAmount

                            ) {
                                // foreach ($UserFacilities as $u_key => $UserFacility) {
                                // dd($UserFacility);
                                $totalGrossAmount = $totalServiceAmount = $totalNetAmount = $validationAmountTotal = $validationPaidAmountTotal = $validatedGTotal = 0;
                                //$facilities=[];
                                $totalCashServiceAmount = $TotalPaymentReceived = $TotalCc = $totalServiceAmount = $processingFeeNonValidated = 0;
                                $totalTicketsNonValidated = $validationTicketTotal = $rTticketCount = 0;
                                $finalCodes5 = [];
                                $reservationCheckinArray = [];
                                $finalCodes4 = [];
                                $reservationTickets = [];
                                $TotalValidatedAmount = $totalCashServiceAmount = $ccticketCount = $TotalRevenueNonValidated = 0;
                                $totalExtentionCount = $totalExtendedAmount = $processingFeeNonValidated = $validatedAmountNonValidated = 0;

                                $facilities = Facility::where(['id' => $this->facilityId])->first();
                                $rates = Rate::where(['facility_id' => $this->facilityId])->withTrashed()->orderby('price', 'asc')->get();
                                $facilityID = " AND t.facility_id = '" . $this->facilityId . "' ";

                                $SearchString = " t.paid_type='9' AND  t.partner_id IN (" . $this->partnerId . ") and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "' AND t.facility_id IN (" . $this->facilityId . ")";
                                $validationSearchString = " t.paid_type !='9' AND  t.partner_id IN (" . $this->partnerId . ") and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "' AND t.facility_id IN (" . $this->facilityId . ")";


                                $rowKey = 0;
                                $outerLoopIndex = 0;
                                // Drive Up Data
                                // $excelrowCount = $increment1 = 0;
                                $driveupData = "SELECT COUNT(distinct(t.id)) AS ticketCount,
                                COALESCE(t.grand_total, 0) AS grand_total,
                                COALESCE(t.grand_total, 0) AS payAmount,
                                COALESCE(t.processing_fee, 0) AS processingFeePerTicket,
                                COALESCE(IF(t.grand_total > 0,t.parking_amount,t.grand_total), 0) AS parkingAmount,
                                SUM(COALESCE(t.tax_fee, 0)) AS tax_fee,
                                
                                (t.processing_fee* COUNT(distinct(t.id))) AS processing_fee,
                                SUM(COALESCE(t.grand_total, 0)) AS Pay_grand_total,
                                SUM(COALESCE(IF(t.grand_total > 0,t.parking_amount,t.grand_total), 0)) AS net_amount,
                                SUM(COALESCE(t.discount_amount, 0)) AS discount_amount,
                                SUM(COALESCE(t.paid_amount, 0)) AS validated_amount,
                                t.length,
                                t.total,
                                t.is_extended,
                                case when  t.is_extended = '1' then
                                    ((SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) + t.grand_total )
                                ELSE
                                    t.grand_total
                                END
                                AS final_sum,
                                count(te.id) as no_of_extentions,
                                sum(te.grand_total)  as overstayGrandTotal, 
                                sum(te.discount_amount) as overstayDiscount,
                                CASE
                                    WHEN t.is_extended = '1' THEN sum(CEIL(COALESCE(t.length, 0)  ))
                                    ELSE SUM(CEIL(COALESCE(t.length, 0)))
                                END AS lenghtInMints             
                                from tickets as t
                                left join ticket_extends as te on te.ticket_id = t.id
                                where $SearchString  and t.is_checkout ='1' and t.deleted_at is null and t.reservation_id is null and t.permit_request_id is null group by t.grand_total order by t.grand_total";
                                // dd($driveupData);

                                $dataforCategoryWise = DB::select($driveupData);
                                foreach ($dataforCategoryWise as $key => $value) {
                                    $finalCodes5[$rowKey]['Rate ($)']               = floatval($value->parkingAmount);
                                    $finalCodes5[$rowKey]['No of Tickets']          = intval($value->ticketCount);
                                    $finalCodes5[$rowKey]['Ticket Number']          = '-';
                                    // $finalCodes5[$rowKey]['Transaction Date']       = '-';
                                    $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($value->total) ? floatval($value->total) : '0.00';
                                    $finalCodes5[$rowKey]['No of Extensions']        = floatval($value->no_of_extentions);
                                    $finalCodes5[$rowKey]['Extension Amount ($)']   = floatval($value->overstayGrandTotal);
                                    $finalCodes5[$rowKey]['Net Amount ($)']         = floatval($value->net_amount);
                                    $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($value->processing_fee);
                                    $finalCodes5[$rowKey]['Total Collected ($)']    = floatval($value->Pay_grand_total + ($value->overstayGrandTotal ?? 0));
                                    $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($value->discount_amount  + ($value->overstayDiscount ?? 0));

                                    $driveupDataTickets = "SELECT t.id AS ticketCount,
                                                    t.discount_amount AS discount_amount,
                                                    t.ticket_number,
                                                    t.length,
                                                    t.total,
                                                    t.grand_total,
                                                    t.processing_fee,
                                                    t.parking_amount,
                                                    count(tex.id) as no_of_extentions,
                                                    SUM(tex.grand_total) as overstayGrandTotal, 
                                                    SUM(tex.discount_amount) as overstayDiscount
                                                    from tickets  t
                                                    left join ticket_extends as tex on t.id = tex.ticket_id
                                                    where  $SearchString and t.is_checkout ='1' and t.deleted_at is null and t.reservation_id is null and t.grand_total = " . $value->payAmount . " group by t.ticket_number order by t.grand_total";
                                    $driveupDataTicketResults = DB::select($driveupDataTickets);

                                    $a[] = $driveupDataTicketResults;
                                    $extedGrandTotal = $extedDiscount = $extedNetTotal = 0;
                                    if (count($driveupDataTicketResults) > 0) {

                                        $rowKey++;
                                        foreach ($driveupDataTicketResults as $tkey => $ticket) {

                                            if ($this->isSummaryEnable) {
                                                $finalCodes5[$rowKey]['Rate ($)']               = '-';
                                                $finalCodes5[$rowKey]['No of Tickets']          = '-';
                                                $finalCodes5[$rowKey]['Ticket Number']          = $ticket->ticket_number;
                                                // $finalCodes5[$rowKey]['Transaction Date']       = '-';
                                                $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($ticket->total) ? floatval($ticket->total) : '0.00';
                                                $finalCodes5[$rowKey]['No of Extensions']       = floatval($value->no_of_extentions);
                                                $finalCodes5[$rowKey]['Extension Amount ($)']   = floatval($value->overstayGrandTotal);
                                                $finalCodes5[$rowKey]['Net Amount ($)']         = floatval($ticket->parking_amount + ($ticket->overstayGrandTotal ?? 0));
                                                $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($ticket->processing_fee);
                                                $finalCodes5[$rowKey]['Total Collected ($)']    = floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
                                                $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                                                $rowKey++;
                                            }

                                            $extedNetTotal += floatval($ticket->parking_amount + ($ticket->overstayGrandTotal ?? 0));
                                            $extedGrandTotal += floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
                                            $extedDiscount += floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                                        }
                                    }

                                    $totalExtendedAmount           += floatval($value->overstayGrandTotal);
                                    $finalCodes5[$outerLoopIndex]['Net Amount ($)']         = floatval($extedNetTotal);
                                    $finalCodes5[$outerLoopIndex]['Total Collected ($)']    = floatval(($extedGrandTotal));
                                    $finalCodes5[$outerLoopIndex]['Discount Amount ($)']    = floatval(($extedDiscount));
                                    $rowKey++;


                                    $TotalTicketAmount              += '0.00';
                                    $netValueNonValidated           += '0.00';
                                    // $totalDriveUpDuration           += $value->lenghtInMints;
                                    $totalDriveUpDuration           += 0;
                                    $totalTicketsNonValidated       += $value->ticketCount;
                                    $totalExtentionCount            += $value->no_of_extentions;
                                    $TotalRevenueNonValidated       += floatval(($extedGrandTotal));;
                                    $processingFeeNonValidated      += floatval($value->processing_fee);
                                    $validatedAmountNonValidated    += floatval($value->validated_amount);
                                    $totalDiscountAmountNonValidated += floatval(($extedDiscount));
                                }
                                // dd($totalDiscountAmountNonValidated);

                                //resertion data end
                                $reservationData = "SELECT COUNT(distinct(t.id)) AS ticketCount,
                                    COALESCE(t.grand_total, 0) AS grand_total,
                                    COALESCE(t.grand_total, 0) AS payAmount,
                                    SUM(COALESCE(t.tax_fee, 0)) AS tax_fee,
                                    SUM(COALESCE(t.processing_fee, 0)) AS processing_fee,
                                    SUM(COALESCE(t.grand_total, 0)) AS Pay_grand_total,
                                    SUM(COALESCE(t.parking_amount, 0)) AS net_amount,
                                    SUM(COALESCE(t.discount_amount, 0)) AS discount_amount,
                                    SUM(COALESCE(t.paid_amount, 0)) AS validated_amount,
                                    group_concat(t.id) as reservation_ids,
                                    r.ticketech_code,
                                    t.ticket_number,
                                    f.full_name,
                                    t.length,
                                    t.total,
                                    CASE
                                        WHEN t.is_overstay = '1' THEN sum(CEIL(COALESCE(t.length, 0) + COALESCE(tex.length, 0)))
                                        ELSE SUM(CEIL(COALESCE(t.length, 0)))
                                    END AS lenghtInMints,
                                    sum(tex.grand_total) as overstayGrandTotal,
                                    sum(tex.discount_amount) as overstayDiscount
                                    from tickets as t
                                    inner join reservations as r on r.id = t.reservation_id
                                    inner join facilities as f on f.id = t.facility_id
                                    left join ticket_extends as tex on t.id = tex.ticket_id
                                    where  $SearchString and t.is_checkout ='1' and t.deleted_at is null and r.deleted_at is null and t.reservation_id is not null group by t.grand_total,t.is_overstay order by t.grand_total";

                                $reservationDataResult = DB::select($reservationData);
                                // dd($reservationDataResult);

                                $validatedCheckinAmountNonValidated = $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = $discountAmount = $processingFee = $grossAmount = $discountAmountCheckin = 0;
                                $rowKey = 0;
                                // dd(count($reservationDataResult));
                                if (count($reservationDataResult) > 0) {
                                    foreach ($reservationDataResult as $key => $value) {
                                        $reservationCheckinArray[$rowKey]['Rate ($)']              = floatval($value->payAmount);
                                        $reservationCheckinArray[$rowKey]['Ticket Number']     = $reservationDataResult->account_number;
                                        $reservationCheckinArray[$rowKey]['No of Tickets']     = intval($value->tracking_code);
                                        $reservationCheckinArray[$rowKey]['Ticket Amount ($)'] = floatval('2.00');
                                        $reservationCheckinArray[$rowKey]['Duration (Hours)']    = intval($value->lenghtInMints);
                                        $reservationCheckinArray[$rowKey]['Gross Amount ($)']  = floatval($value->Pay_grand_total + ($value->overstayGrandTotal ?? 0));
                                        // $reservationCheckinArray[$rowKey]['Processing Fees ($)'] = floatval($dataforCategoryWise[$rowKey]['faidprocessing_fee']);
                                        // $reservationCheckinArray[$rowKey]['Net Amount ($)'] = floatval($dataforCategoryWise[$rowKey]['fnet_amount']);
                                        $reservationCheckinArray[$rowKey]['Discount Amount ($)'] = floatval($value->discount_amount  + ($value->overstayDiscount ?? 0));

                                        $driveupDataTickets = "SELECT t.id AS ticketCount,
                                                            t.grand_total AS grand_total,
                                                            t.grand_total  AS payAmount,  
                                                            t.tax_fee   AS tax_fee,   
                                                            t.processing_fee AS processing_fee,
                                                            t.grand_total AS Pay_grand_total,
                                                            t.parking_amount AS net_amount,
                                                            t.discount_amount AS discount_amount,
                                                            t.paid_amount AS validated_amount,
                                                            t.ticket_number,
                                                            f.full_name,
                                                            t.length,
                                                            CASE
                                                                    WHEN t.is_overstay = '1' THEN CEIL(COALESCE(t.length, 0) + COALESCE(tex.length, 0))
                                                                    ELSE CEIL(COALESCE(t.length, 0))
                                                            END AS lenghtInMints,
                                                            (SELECT sum(tex.grand_total) FROM ticket_extends as tex where tex.ticket_id IN (t.id)) as overstayGrandTotal, 
                                                            (SELECT sum(texd.discount_amount) FROM ticket_extends as texd where texd.ticket_id IN (t.id)) as overstayDiscount
                                                            from tickets as t
                                                            inner join facilities as f on f.id = t.facility_id
                                                            left join ticket_extends as tex on t.id = tex.ticket_id
                                                            where  $SearchString and t.is_checkout ='1' and t.deleted_at is null and t.reservation_id is not null and t.grand_total = '" . $value->payAmount . "' $facilityID order by t.grand_total";
                                        // $this->log->info('WLA 33 Query . ' . json_encode($driveupDataTickets));
                                        $driveupDataTicketResults = DB::select($driveupDataTickets);
                                        if (count($driveupDataTicketResults) > 0 && $this->isSummaryEnable) {
                                            $rowKey++;
                                            foreach ($driveupDataTicketResults as $tkey => $ticket) {
                                                $reservationCheckinArray[$rowKey]['Rate ($)']              = '';
                                                $reservationCheckinArray[$rowKey]['Ticket Number']     = $ticket->ticket_number;
                                                $reservationCheckinArray[$rowKey]['No of Tickets']     = 1;
                                                $reservationCheckinArray[$rowKey]['Ticket Amount ($)'] = floatval('2.00');
                                                $reservationCheckinArray[$rowKey]['Duration (Hours)']    = intval($ticket->lenghtInMints);
                                                $reservationCheckinArray[$rowKey]['Gross Amount ($)']  = floatval($ticket->Pay_grand_total + ($ticket->overstayGrandTotal ?? 0));
                                                $reservationCheckinArray[$rowKey]['Discount Amount ($)'] = floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                                                $rowKey++;
                                            }
                                        }
                                        $rowKey++;

                                        $TotalTicketAmount          += floatval($value->payAmount);
                                        $processingFeeNonValidated  += floatval($value->processing_fee);
                                        $totalReservationDurtion    += $value->lenghtInMints;
                                        $totalTicketsCheckinNonValidated += $value->ticketCount;
                                        $TotalCheckinRevenueNonValidated += floatval($value->Pay_grand_total + $value->overstayGrandTotal);
                                        $netValueNonValidated += 0;
                                        // $validatedCheckinAmountNonValidated = floatval($value->discount_amount  + ($value->overstayDiscount ?? 0));
                                    }
                                }
                                // }

                                // $permitSearchString = " pr.deleted_at IS NULL AND pr.status ='1' AND pr.cancelled_at is null AND  t.partner_id IN (" . $this->partnerId . ") and pr.created_at >='" . $this->checkinTime . "' and pr.created_at <='" . $this->checkoutTime . "' AND pr.facility_id IN (" . $this->facilityId . ")";

                                // Permint search String
                                $permitSearchString = " pr.partner_id IN ('" . $this->partnerId . "')  AND pr.created_at >='" . $this->checkinTime . "' and pr.created_at <='" . $this->checkoutTime . "' AND pr.facility_id IN (" . $this->facilityId . ") and pr.deleted_at IS NULL AND pr.status ='1' and pr.cancelled_at is null ";



                                $permitData = "SELECT pr.permit_rate, count(DISTINCT pr.id) as permit_count, sum(ant.total) as per_grand_total, pr.facility_id, GROUP_CONCAT(DISTINCT pr.account_number) as permit_id,GROUP_CONCAT(DISTINCT pr.id) as tickets FROM permit_requests as pr
                                    inner join anet_transactions as ant on ant.id = pr.anet_transaction_id          
                                    where $permitSearchString group by pr.permit_rate order by pr.permit_rate asc";
                                // dd('1');
                                $permitResults = DB::select($permitData);

                                $permitGrossTotalAmount = $permitTicketCount = $permitCount = $permitRowIndex  = 0;
                                $permitTickets = [];
                                foreach ($permitResults as $key => $value) {
                                    $permitTickets[$permitRowIndex]['Rate'] = $value->permit_rate;
                                    $permitTickets[$permitRowIndex]['PermitCount'] = $value->permit_count;
                                    $permitTickets[$permitRowIndex]['NoofTickets'] = floatval(0);
                                    $permitTickets[$permitRowIndex]['PermitNumber'] = '-';
                                    $permitTickets[$permitRowIndex]['GrossAmount'] = $value->per_grand_total;

                                    $permitTicketsResults = explode(",", $value->permit_id);
                                    $permitRowIndex++;
                                    // $totatPermitTicket=0;
                                    if (count($permitTicketsResults) > 0 && $this->isSummaryEnable) {
                                        foreach ($permitTicketsResults as $trow => $account_number) {
                                            $permitAmountSql = "SELECT pr.permit_rate, pr.id as permitRowID , ant.total, pr.facility_id               
                                            FROM permit_requests as pr
                                            inner join anet_transactions as ant on ant.id = pr.anet_transaction_id
                                            left join tickets as t on t.permit_request_id = pr.id
                                            where $permitSearchString and  account_number IN ($account_number)
                                            group by pr.permit_rate order by pr.permit_rate asc";
                                            $permitAmount = DB::select($permitAmountSql);

                                            // Print checkin against Permit Tickets 
                                            $permitRowID = $permitAmount[0]->permitRowID;
                                            $permitTickets_sql = "SELECT id, total,ticket_number FROM tickets WHERE permit_request_id IN ($permitRowID) and deleted_at is null order by id asc";
                                            $permitTicketsNumberResults = DB::select($permitTickets_sql);
                                            $count = count($permitTicketsNumberResults) > 0 ? count($permitTicketsNumberResults) : 0;
                                            $permitTicketCount += $count;

                                            // dd($permitAmount[0]->total);
                                            $permitTickets[$permitRowIndex]['Rate'] = '-';
                                            $permitTickets[$permitRowIndex]['PermitCount'] = '-';
                                            $permitTickets[$permitRowIndex]['NoofTickets'] = floatval($count);
                                            $permitTickets[$permitRowIndex]['PermitNumber'] = $account_number;
                                            $permitTickets[$permitRowIndex]['GrossAmount'] = $permitAmount[0]->total;
                                            $permitRowIndex++;
                                        }
                                    }
                                    $permitCount += $value->permit_count;
                                    $permitGrossTotalAmount += $value->per_grand_total;
                                }

                                // dd($permitArray);
                                // dd($permitTickets);
                                $pemitAggrgatedData = [];
                                if (count($permitResults) > 0) {
                                    $pemitAggrgatedData['totalPermitTicket'] = $permitTicketCount;
                                    $pemitAggrgatedData['totalPermitCount'] = $permitCount;
                                    $pemitAggrgatedData['totalGrossAmount'] = $permitGrossTotalAmount;
                                }

                                // dd($pemitAggrgatedData);
                                // Cash or Offline payment - for transient
                                $driveUpOfflinePayment = "SELECT SUM(t.grand_total) AS sum_offline_amount, SUM(t.discount_amount) as discountAmount,t.is_offline_payment,t.partner_id ,SUM(t.processing_fee) as processingFee, SUM(tex.grand_total) as overstayGrandTotal, SUM(tex.discount_amount) as overstayDiscount, group_concat(t.id) as ticket_ids
                                FROM tickets AS t
                                left join ticket_extends as tex on t.id = tex.ticket_id
                                where  $SearchString  and t.is_checkout ='1' and t.deleted_at is null and (t.is_offline_payment IN('1') ) $facilityID order by t.id";

                                $driveUpCashReport = DB::select($driveUpOfflinePayment);
                                // dd($driveUpOfflinePayment);

                                // Non Cash or card breakdown
                                $DriveUpnonCashPayment = "SELECT CASE
                                    WHEN card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    when card_type IN ('VS','VISA','Visa','visa','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    WHEN checkout_card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    when checkout_card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    when checkout_card_type IN ('AMEX') THEN 'AMEX'
                                    ELSE card_type
                                    END AS combined_card_type, SUM(t.grand_total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                                    (SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) as overstayGrandTotal,
                                    (SELECT sum(ticket_extends.discount_amount) FROM ticket_extends where ticket_id IN (t.id)) as overstayDiscount,
                                    group_concat(t.id) as ticket_ids
                                    FROM tickets as t
                                    WHERE $SearchString AND t.is_offline_payment IN ('0','2','3') and t.deleted_at is null  and t.is_checkout ='1' $facilityID GROUP BY combined_card_type,t.id"; #td.rate_description     
                                $driveUpCCReport = DB::select($DriveUpnonCashPayment);

                                //**Pemrit Card Payment  */
                                $permitCardPayment = "SELECT CASE
                                    when ant.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    WHEN ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    when ant.card_type IN ('DCVR') THEN 'DCVR'
                                    WHEN ant.card_type IN ('AMEX') THEN 'AMEX'  
                                    WHEN ant.card_type IN ('Disc') THEN 'DISC'  
                                    ELSE ant.card_type
                                    END AS combined_card_type, count(pr.id) as ticketCount, t.ticket_number, #pr.ticketech_code,
                                    SUM(pr.permit_rate) as total_amount,
                                    (SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) as overstayGrandTotal,
                                    (SELECT sum(ticket_extends.discount_amount) FROM ticket_extends where ticket_id IN (t.id)) as overstayDiscount
                                    FROM permit_requests as pr
                                    inner join anet_transactions as ant on ant.id = pr.anet_transaction_id
                                    left join tickets as t on t.permit_request_id = pr.id 
                                    WHERE $permitSearchString and ant.card_type is not null GROUP BY combined_card_type,t.is_overstay,t.ticket_number"; #td.rate_description     

                                // dd($checkinDriveUpnonCashPayment);
                                $permitCCReportPayment = DB::select($permitCardPayment);

                                // dd($permitCCReportPayment);
                                // Calculate reservation Payment             
                                // Non Cash or card breakdown
                                $checkinDriveUpnonCashPayment = "SELECT CASE
                                    WHEN t.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    when t.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    when ant.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    WHEN ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    ELSE t.card_type
                                    END AS combined_card_type, SUM(t.grand_total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                                    (SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) as overstayGrandTotal,
                                    (SELECT sum(ticket_extends.discount_amount) FROM ticket_extends where ticket_id IN (t.id)) as overstayDiscount
                                    FROM tickets as t
                                    inner join reservations as r on r.id = t.reservation_id
                                    inner join anet_transactions as ant on ant.id = r.anet_transaction_id
                                    WHERE $SearchString AND t.is_offline_payment='0' and t.deleted_at is null and t.reservation_id is not null and t.deleted_at is null and r.deleted_at is null  and t.is_checkout ='1' $facilityID GROUP BY combined_card_type,t.is_overstay"; #td.rate_description     

                                $checkinDriveUpCCReport = DB::select($checkinDriveUpnonCashPayment);
                                // dd($checkinDriveUpCCReport);
                                $sql_query4 = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                                    sum(t.total) as total,                
                                    sum(t.grand_total) as paidAmount,                
                                    sum(t.parking_amount) as parking_amount,
                                    sum(t.paid_amount) as validated_amount,
                                    sum(t.discount_amount) as discount_amount,
                                    t.affiliate_business_id, ab.business_name as BusinessName
                                    FROM tickets as t        
                                    inner join affiliate_business as ab on ab.id = t.affiliate_business_id 
                                    WHERE  $validationSearchString and t.affiliate_business_id is not null and paid_by > 0 $facilityID GROUP BY t.affiliate_business_id order by t.affiliate_business_id";
                                $validationReport = DB::select($sql_query4);

                                // dd($validationReport);

                                foreach ($driveUpCashReport as $key => $value) {
                                    if ($value->is_offline_payment == 1) {
                                        $finalCodes2[0]['payment_type'] = 'Cash';
                                        $finalCodes2[0]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                                        $finalCodes2[0]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                                    } else if ($value->is_offline_payment == 2 || $value->is_offline_payment == 3) {
                                        $finalCodes2[1]['payment_type'] = 'Card';
                                        $finalCodes2[1]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                                        $finalCodes2[1]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                                    }
                                    $totalCashServiceAmount += $value->processingFee;
                                    // $cashticketCount = $cashticketCount + $value->ticketCount;
                                    $TotalPaymentReceived +=  $value->sum_offline_amount + $value->overstayGrandTotal;
                                    $TotalValidatedAmount +=  $value->discountAmount + $value->overstayDiscount;
                                }

                                // dd($driveUpCashReport, $finalCodes2, $TotalPaymentReceived);

                                //Credit Card Payment Section 
                                $permitCardPayment = "SELECT CASE
                                    when ant.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    WHEN ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    when ant.card_type IN ('DCVR') THEN 'DCVR'
                                    WHEN ant.card_type IN ('AMEX') THEN 'AMEX'  
                                    WHEN ant.card_type IN ('Disc') THEN 'DISC'  
                                    ELSE ant.card_type
                                    END AS combined_card_type, count(pr.id) as ticketCount, #pr.ticketech_code,
                                    SUM(ant.total) as total_amount               
                                    FROM permit_requests as pr
                                    inner join anet_transactions as ant on ant.id = pr.anet_transaction_id                
                                    WHERE $permitSearchString and ant.card_type is not null and pr.status ='1' and pr.cancelled_at is null GROUP BY combined_card_type "; #td.rate_description     
                                $permitCCReportPayment = DB::select($permitCardPayment);
                                // dd($driveUpCCReport,$checkinDriveUpCCReport,$permitCCReportPayment);


                                $TotalValidatedAmount = $totalCashServiceAmount = $ccticketCount = 0;
                                $cards = ['AMEX', 'DCVR', 'MASTERCARD', 'VISA', 'DISC'];

                                foreach ($cards as $cardkey => $card) {
                                    $totalcardPay  = $processingFees  = $discountAmount = $ticketCount = 0;
                                    if (isset($driveUpCCReport) && count($driveUpCCReport) > 0) {
                                        foreach ($driveUpCCReport as $key => $value) {
                                            if (strcasecmp($card, $value->combined_card_type) == 0) {

                                                if ($value->total_amount <= 0) continue;
                                                $ticketCount += intval($value->ticketCount);
                                                $discountAmount += floatval($value->discountAmount + $value->overstayDiscount);
                                                $processingFees += floatval($value->processingFee);
                                                $totalcardPay += floatval($value->total_amount + $value->overstayGrandTotal);
                                                $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                                $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                            }
                                        }
                                    }


                                    if (isset($checkinDriveUpCCReport) && count($checkinDriveUpCCReport) > 0) {
                                        foreach ($checkinDriveUpCCReport as $key => $value) {
                                            if ($card == $value->combined_card_type) {
                                                $totalcardPay += floatval($value->total_amount + $value->overstayGrandTotal);
                                                $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                            }
                                        }
                                    }
                                    // Permit card Payment
                                    // dd($permitCCReportPayment);
                                    if (isset($permitCCReportPayment) && count($permitCCReportPayment) > 0) {
                                        foreach ($permitCCReportPayment as $key => $value) {
                                            if ($card == $value->combined_card_type) {
                                                $totalcardPay += floatval($value->total_amount);

                                                $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                                $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                            }
                                        }
                                    }


                                    $TotalCc += $totalcardPay;
                                    $ccticketCount += $ticketCount;
                                    $totalCashServiceAmount += $processingFees;
                                    $TotalValidatedAmount += $discountAmount;
                                }
                                // dd($finalCodes2, $TotalPaymentReceived, $finalCodes3, $totalcardPay,$driveUpCCReport,$checkinDriveUpCCReport, $permitCCReportPayment);
                                $i = 0;
                                foreach ($validationReport as $key => $value) {
                                    $finalCodes4[$i]['Business Name'] = $value->BusinessName;
                                    $finalCodes4[$i]['Policy Name'] = '-';
                                    $finalCodes4[$i]['No of Tickets'] = floatval($value->ticket_count);
                                    $finalCodes4[$i]['Gross Amount ($)'] = floatval($value->total);
                                    $finalCodes4[$i]['Processing Fee ($)'] = floatval($value->processingFee);
                                    $finalCodes4[$i]['Net Amount ($)'] = floatval($value->total - $value->processingFee - $value->discount_amount);
                                    $finalCodes4[$i]['Validated Amount ($)'] = floatval($value->validated_amount);
                                    $finalCodes4[$i]['Paid Amount ($)'] = floatval($value->paidAmount);
                                    $finalCodes4[$i]['Total Revenue ($)'] =  floatval($value->paidAmount + $value->validated_amount);

                                    // $gTotal = $value->paidAmount + $value->validated_amount;
                                    // $grossTotal = ($value->parking_amount + $value->processingFee) * $value->ticket_count;
                                    // $serviceFee = floatval($value->processingFee * $value->ticket_count);

                                    $validationTicketTotal  += $value->ticket_count;
                                    $totalGrossAmount       += floatval($value->total);
                                    $totalServiceAmount     += floatval($value->processingFee);
                                    $totalNetAmount         += floatval($value->total - $value->processingFee - $value->discount_amount);
                                    $validationAmountTotal  += floatval($value->validated_amount);
                                    $validationPaidAmountTotal += floatval($value->paidAmount);
                                    $validatedGTotal += floatval($value->paidAmount + $value->validated_amount);;

                                    // $totalGrossAmount += $grossTotal;
                                    // policy query according to business
                                    $policy_query = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                                            sum(t.total) as total,
                                            sum(t.grand_total) as paidAmount,
                                            sum(t.parking_amount) as parking_amount,
                                            sum(t.discount_amount) as discount_amount,
                                            sum(t.paid_amount) as validated_amount,t.affiliate_business_id,t.policy_id,
                                            p.policy_name as policyName,
                                            ab.business_name as BusinessName
                                            FROM tickets as t
                                            inner join affiliate_business as ab on ab.id = t.affiliate_business_id
                                            inner join business_policy as p on p.id = t.policy_id 
                                            WHERE $SearchString and t.affiliate_business_id =$value->affiliate_business_id and t.paid_by > 0 GROUP BY t.affiliate_business_id,t.policy_id order by t.affiliate_business_id";

                                    $policyReport = DB::select($policy_query);

                                    if (isset($policyReport) && !empty($policyReport)) {
                                        $i++;
                                        foreach ($policyReport as $k => $policy) {
                                            // $gTotal = $policy->paidAmount + $policy->validated_amount;
                                            // $grossTotal = ($policy->parking_amount + $policy->processingFee) * $policy->ticket_count;
                                            // $serviceFee = floatval($policy->processingFee * $policy->ticket_count);
                                            $finalCodes4[$i]['Business Name'] = '';
                                            $finalCodes4[$i]['Policy Name'] = $policy->policyName;
                                            $finalCodes4[$i]['No of Ticket'] = floatval($policy->ticket_count);
                                            $finalCodes4[$i]['Gross Total ($)'] = floatval($policy->total);
                                            $finalCodes4[$i]['Processing Fee ($)'] = floatval($policy->processingFee);
                                            $finalCodes4[$i]['Net Amount ($)'] = floatval($policy->total - $policy->processingFee - $value->discount_amount);
                                            $finalCodes4[$i]['Validation Amount ($)'] = floatval($policy->validated_amount);
                                            $finalCodes4[$i]['Paid Amount ($)'] = floatval($policy->paidAmount);
                                            $finalCodes4[$i]['Total Revenue ($)'] =  floatval($policy->paidAmount + $policy->validated_amount);
                                            $i++;
                                        }
                                    }
                                    $i++;
                                }

                                $locationName = $facilities->full_name;
                                $garageCode = $facilities->garage_code;
                                // dd($sql_query4, $finalCodes4);
                                //$validatedAmountNonValidated    += floatval($value->validated_amount);
                                // $totalDiscountAmountNonValidated += floatval($value->discount_amount  + $value->overstayDiscount);
                                // print_r($reservationTickets);die;
                                if (isset($finalCodes5) && !empty($finalCodes5)) {
                                    // Tickets Cashiered 
                                    $excel->sheet($excelSheetName5, function ($sheet5) use (
                                        $finalCodes5,
                                        $reservationTickets,
                                        $reservationCheckinArray,
                                        $permitTickets,
                                        $pemitAggrgatedData,
                                        $permitTicketCount,
                                        $color,
                                        $totalTicketsNonValidated,
                                        $TotalRevenueNonValidated,
                                        $locationName,
                                        $garageCode,
                                        $totalDiscountAmountNonValidated,
                                        $processingFeeNonValidated,
                                        $finalCodes2,
                                        $TotalPaymentReceived,
                                        $finalCodes3,
                                        $TotalCc,
                                        $finalCodes4,
                                        $validationPaidAmountTotal,
                                        $totalServiceAmount,
                                        $validationAmountTotal,
                                        $validationTicketTotal,
                                        $totalGrossAmount,
                                        $TotalValidatedAmount,
                                        $validatedGTotal,
                                        $totalTicketsCheckinNonValidated,
                                        $rTticketCount,
                                        $totalDriveUpDuration,
                                        $facilities,
                                        $permitGrossTotalAmount,
                                        $permitCount,
                                        $totalExtentionCount,
                                        $totalExtendedAmount
                                    ) {
                                        // dd('vijay');
                                        $topSpace = 7;
                                        $sheet5->setWidth(array(
                                            'A'     => 21,
                                            'B'     =>  21,
                                            'C'     =>  18,
                                            'D'     =>  17.57,
                                            'E'     =>  17.34,
                                            'F'     =>  15.57,
                                            'G'    =>   21,
                                            'H'    =>   16.86,
                                            'I'    =>   18,
                                            'J'    =>   18,
                                        ));

                                        // $sheet5->row(1, 'Daily Revenue Report');
                                        // define width colom A,B, witdh static here
                                        // $sheet5->getColumnDimension('A')->setWidth(21);
                                        // $sheet5->getColumnDimension('B')->setWidth(24);
                                        // $sheet5->getColumnDimension('C')->setWidth(16);
                                        $sheet5->getColumnDimension('D')->setWidth(17.57);
                                        $sheet5->getColumnDimension('E')->setWidth(17.34);
                                        $sheet5->getColumnDimension('F')->setWidth(15.57);
                                        $sheet5->getColumnDimension('G')->setWidth(21);
                                        $sheet5->getColumnDimension('H')->setWidth(16.86);
                                        // $sheet5->getColumnDimension('I')->setWidth(18);

                                        // $sheet5->getStyle("A")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("D")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("H")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("I")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("J")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("C3")->getNumberFormat()->setFormatCode('0');
                                        //end width for colom here

                                        $rowRange = '';
                                        //set header for excel work                                
                                        $colorCode = count($finalCodes5) + 7;
                                        $row_name = 'A' . $colorCode . ':J' . $colorCode;
                                        // dd(count($finalCodes5), $finalCodes5, $colorCode);
                                        $sheet5->cell($row_name, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');

                                            $row->setFont(array(
                                                'family'     => 'Calibri',
                                                'size'       => '12',
                                                'bold'       =>  true
                                            ));
                                        });


                                        $this->addLogoInExcelHeader($sheet5);


                                        $sheet5->mergeCells('A2:D2');
                                        // $sheet5->mergeCells('A2:C2');
                                        if (date('Y-m-d', strtotime($this->checkinTime)) == date('Y-m-d', strtotime($this->checkoutTime))) {
                                            $cellValue = "Report Date - " .  date('m-d-Y', strtotime($this->checkinTime));
                                        } else {

                                            $cellValue = "Report Date Range - " .  date('m-d-Y', strtotime($this->checkinTime)) .  ' - ' . date('m-d-Y', strtotime($this->checkoutTime));
                                        }
                                        $cellValue .= "\nPrint Date - " .  date('m-d-Y', strtotime('now'));
                                        $sheet5->setCellValue('A2', "$cellValue");
                                        $sheet5->getStyle('A2')->getAlignment()->setWrapText(true);
                                        // Set the height of cell H2 (adjust as needed)
                                        $sheet5->getRowDimension(2)->setRowHeight(80);
                                        $sheet5->getRowDimension(3)->setRowHeight(50);
                                        $sheet5->cell('A2:D2', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            //$cell->setFontWeight('bold');
                                            $cell->setBackground('#D6DCE4');
                                            $cell->setFontColor('#000000');
                                            $cell->setFontSize('18');
                                        });
                                        $location = "Location Name \r" . $locationName;
                                        $sheet5->mergeCells('E2:G2');
                                        $sheet5->setCellValue('E2', "$location");
                                        $sheet5->getStyle('E2')->getAlignment()->setWrapText(true);
                                        $sheet5->cell('E2:G2', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D6DCE4');
                                            $cell->setFontColor('#000000');
                                            $cell->setFontSize('18');
                                        });
                                        $sheet5->mergeCells('H2:J2');
                                        $locationId = "Location ID \n" . $garageCode;
                                        $sheet5->setCellValue('H2', "$locationId");
                                        $sheet5->getStyle('H2')->getAlignment()->setWrapText(true);

                                        $sheet5->cell('H2:J2', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D6DCE4');
                                            $cell->setFontColor('#040D12');
                                            $cell->setFontSize('18');
                                        });


                                        // Ticket Count and Revenue Row Start !!!!
                                        $sheet5->mergeCells('A3:B3');
                                        $sheet5->setCellValue('A3', "Total Tickets");

                                        $sheet5->cell('A3:B3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#191D88');
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('12');
                                        });

                                        $sheet5->mergeCells('C3:E3');
                                        if ($totalTicketsCheckinNonValidated > 0) {
                                            $totalTicketsNonValidated += $totalTicketsCheckinNonValidated;
                                        }
                                        // dd($totalTicketsNonValidated,$validationTicketTotal,$permitTicketCount);
                                        // dd($totalTicketsNonValidated, $validationTicketTotal, $rTticketCount);
                                        $this->totalTickets = ($totalTicketsNonValidated + $validationTicketTotal + $permitTicketCount);
                                        $sheet5->setCellValue('C3', $this->totalTickets);
                                        if ($totalTicketsCheckinNonValidated > 0) {
                                            $totalTicketsNonValidated -= $totalTicketsCheckinNonValidated;
                                        }

                                        $sheet5->cell('C3:E3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('12');
                                        });

                                        $sheet5->mergeCells('F3:H3');
                                        $sheet5->setCellValue('F3', "Total Revenue ($)");
                                        $sheet5->cell('F3:H3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#191D88');
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('12');
                                        });

                                        $sheet5->mergeCells('I3:J3');
                                        $sheet5->cell('I3:J3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('12');
                                        });
                                        // Ticket Count and Revenue Row Close Here  !!!!



                                        // Drive Up Section Start Here.
                                        $sheet5->mergeCells('A5:J5');
                                        $sheet5->setCellValue('A5', 'Tickets Cashiered DriveUp');
                                        $sheet5->cell('A5', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D9E1F2');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });

                                        // Color Row For Heading 
                                        $sheet5->cell('A6:J6', function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                        });
                                        $sheet5->fromArray($finalCodes5, [], 'A6', false, true);
                                        //fro center the result
                                        $countAlltick = 6;
                                        $topSpace = 7;
                                        // dd($finalCodes5);
                                        $ir = 0;
                                        foreach ($finalCodes5 as $rowkey => $tickets) {
                                            $countAlltick++;
                                            $sheet5->cell('A' . $countAlltick . ':H' . $countAlltick, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center'); // Center vertically
                                            });
                                        }
                                        $non_colorCode = count($finalCodes5) + $topSpace;
                                        // print_r($non_colorCode);die;
                                        $row_name = 'A' . $non_colorCode . ':J' . $non_colorCode;
                                        $sheet5->cell($row_name, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                            $row->setAlignment('center');
                                            $row->setValignment('center');

                                            $row->setFont(array(
                                                'family' => 'Calibri',
                                                'size' => '12',
                                                'bold' => true
                                            ));
                                        });


                                        // Drive Up Total Row
                                        $sheet5->row($non_colorCode, array('Total ($)', $totalTicketsNonValidated, '-',  '-', $totalExtentionCount, $totalExtendedAmount, ($TotalRevenueNonValidated - $processingFeeNonValidated), $processingFeeNonValidated, $TotalRevenueNonValidated, $totalDiscountAmountNonValidated));
                                        // dd(count($finalCodes5) + 10);
                                        // Drive Up Section Close Here !!!


                                        /* Reservation Related Code */
                                        // dd(count($permitTickets), $permitTickets);
                                        if (count($reservationCheckinArray) > 0) {
                                            $j = count($finalCodes5) + $topSpace + 2;
                                            // dd($j);
                                            $sheet5->mergeCells('A' . $j . ':J' . $j);
                                            $sheet5->setCellValue('A' . $j, 'Tickets Cashiered Reservation');
                                            $sheet5->cell('A' . $j, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center');
                                                $cell->setFontWeight('bold');
                                                $cell->setBackground('#D9E1F2');
                                                $cell->setFontColor('#ffffff');
                                                $cell->setFontSize('12');
                                            });

                                            $i = count($finalCodes5) + $topSpace + 3;
                                            foreach ($reservationCheckinArray as $key => $value) {
                                                $sheet5->cell('A' . $i . ':G' . $i, function ($cell) use ($color) {
                                                    $cell->setAlignment('center'); // Center horizontally
                                                    $cell->setValignment('center'); // Center vertically
                                                });
                                                // dd($value);
                                                $sheet5->setCellValue('A' . $i, $value['Rate ($)']);
                                                $sheet5->setCellValue('B' . $i, $value['Ticket Number']);
                                                $sheet5->setCellValue('C' . $i, $value['No of Tickets']);
                                                $sheet5->setCellValue('D' . $i, $value['Ticket Amount ($)']);
                                                $sheet5->setCellValue('E' . $i, $value['Duration (Hours)']);
                                                $sheet5->setCellValue('F' . $i, $value['Gross Amount ($)']);
                                                $sheet5->setCellValue('G' . $i, $value['Discount Amount ($)']);
                                                $i++;
                                            }
                                            $i++;

                                            //reservation counter total start here
                                            $resrvation_colorCode = count($finalCodes5) + count($reservationTickets) + $topSpace + 2; // color for reser top

                                            $sheet5->row($resrvation_colorCode, array('Total ($)', '-', $totalTicketsNonValidated, '-', $totalDriveUpDuration, $TotalRevenueNonValidated, $totalDiscountAmountNonValidated));
                                            $sheet5->cell('A' . $resrvation_colorCode . ':J' . $resrvation_colorCode, function ($cell) use ($color) {
                                                $cell->setAlignment('center');
                                                $cell->setValignment('center');
                                                $cell->setFontWeight('bold');
                                                $cell->setBackground('#EC33FF');
                                                $cell->setFontColor('#272829');
                                                $cell->setFontSize('12');
                                            });

                                            //End reservation counter total start here
                                        }
                                        /* End Reservation Related Code */


                                        // !!!!!!!! Permit Start here :
                                        // dd($permitrRowNumber);
                                        //$eventSheetRowStart= count($result) + 14;
                                        $permitSheetRowStart = 0;
                                        $permitrRowNumber = count($reservationCheckinArray) + count($finalCodes5) + $topSpace + 2;
                                        $permitSheetRowStart = $permitrRowNumber;

                                        $sheet5->getStyle('E' . $permitSheetRowStart)->getNumberFormat()->setFormatCode('0');

                                        $sheet5->mergeCells('A' . $permitSheetRowStart . ':J' . $permitSheetRowStart);
                                        $sheet5->setCellValue('A' . $permitSheetRowStart, 'Tickets Cashiered Permit');
                                        $sheet5->cell('A' . $permitSheetRowStart, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D9E1F2');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });

                                        $rowNumber1 = $permitSheetRowStart + 1;
                                        $sheet5->cell('A' . $rowNumber1 . ':J' . $rowNumber1, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                        });
                                        $sheet5->cell('A' . $rowNumber1 . ':J' . $rowNumber1, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                        });

                                        $sheet5->setCellValue('A' . $rowNumber1, 'Rate');
                                        $sheet5->setCellValue('B' . $rowNumber1, 'Permit Count');
                                        $sheet5->setCellValue('C' . $rowNumber1, 'Permit Number');
                                        $sheet5->setCellValue('D' . $rowNumber1, 'Checkin Count');
                                        $sheet5->setCellValue('E' . $rowNumber1, 'Total Revenue ($)');
                                        // $sheet5->setCellValue('F' . $rowNumber1, 'Ticket Number');

                                        $kevent = $rowNumber1 + 1;
                                        $event_processing = $totalNetAmount = 0;
                                        $permitGrossAmount = 0;
                                        $totalPermitTicket = 0;
                                        // dd('22',$rowNumber1,$kevent,$permitTickets);
                                        foreach ($permitTickets as $key => $eval) {
                                            $sheet5->getStyle('D' . $kevent)->getNumberFormat()->setFormatCode('0');
                                            $sheet5->cell('A' . $kevent . ':I' . $kevent, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center');
                                            });

                                            // $event_base_rate += $eval['EventRate'];
                                            $sheet5->setCellValue('A' . $kevent, $eval['Rate']);
                                            $sheet5->setCellValue('B' . $kevent, $eval['PermitCount']);
                                            $sheet5->setCellValue('C' . $kevent, $eval['PermitNumber']);
                                            $sheet5->setCellValue('D' . $kevent, $eval['NoofTickets']);
                                            $sheet5->setCellValue('E' . $kevent, $eval['GrossAmount']);
                                            // $sheet5->setCellValue('F' . $kevent, $eval['TicketNumber']);

                                            if (is_numeric($eval['PermitNumber'])) {
                                                $totalPermitTicket += $eval['NoofTickets'];
                                                $permitGrossAmount += floatval($eval['GrossAmount']);
                                            }
                                            // if (!empty($eval['PermitNumber'])) {
                                            // $totalPermitTicket += $eval['NoofTickets'];
                                            // $permitGrossAmount += floatval($eval['GrossAmount']);
                                            // }
                                            $kevent++;
                                        }

                                        $sheet5->getStyle('D' . $kevent)->getNumberFormat()->setFormatCode('0');
                                        $totalRowForPermit = $kevent;
                                        $sheet5->setCellValue('A' . $totalRowForPermit, 'Total ($)');
                                        $sheet5->setCellValue('B' . $totalRowForPermit, $permitCount);
                                        $sheet5->setCellValue('C' . $totalRowForPermit, '');
                                        $sheet5->setCellValue('D' . $totalRowForPermit, $totalPermitTicket);
                                        $sheet5->setCellValue('E' . $totalRowForPermit, $permitGrossAmount);
                                        $sheet5->setCellValue('F' . $totalRowForPermit, '');

                                        $sheet5->cell('A' . $totalRowForPermit . ':J' . $totalRowForPermit, function ($cell) use ($color) {
                                            $cell->setAlignment('center');
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('12');
                                        });
                                        // dd('1',$permitTickets);
                                        // !!!! Permit close here .


                                        /** Non Revenue Section Start Here : eValidation */
                                        // $revenuSheetRowStart = count($finalCodes5) + count($reservationCheckinArray) + count($permitTickets) + $topSpace + 5;
                                        // $rowNumber = count($finalCodes5) + count($reservationCheckinArray) + count($permitTickets) + $topSpace + 6;
                                        $jCell = $i = $revenuSheetRowStart = $totalRowForPermit;
                                        $validationPaidAmountTotal = 0;
                                        if (count($finalCodes4) > 0) {
                                            $revenuSheetRowStart = $totalRowForPermit + 2;
                                            $rowNumber = $revenuSheetRowStart + 1;
                                            $sheet5->mergeCells('A' . $revenuSheetRowStart . ':J' . $revenuSheetRowStart);
                                            // $sheet5->mergeCells('E' .$revenuSheetRowStart .':I' .$revenuSheetRowStart);
                                            $sheet5->setCellValue('A' . $revenuSheetRowStart, 'Non Revenue Tickets');
                                            $sheet5->cell('A' . $revenuSheetRowStart, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center');
                                                $cell->setFontWeight('bold');
                                                $cell->setBackground('#D9E1F2');
                                                $cell->setFontColor('#272829');
                                                $cell->setFontSize('12');
                                            });
                                            $sheet5->cell('A' . $rowNumber . ':J' . $rowNumber, function ($cell) use ($color) {
                                                $cell->setAlignment('center');
                                                $cell->setFontWeight('bold');
                                                $cell->setBackground($color);
                                                $cell->setFontColor('#ffffff');
                                                $cell->setFontSize('12');
                                            });
                                            // dd($count);

                                            $sheet5->setCellValue('A' . $rowNumber, 'Business Name');
                                            $sheet5->setCellValue('B' . $rowNumber, 'Policy Name');
                                            $sheet5->setCellValue('C' . $rowNumber, 'No of Tickets');
                                            $sheet5->setCellValue('D' . $rowNumber, 'Gross Amount ($)');
                                            $sheet5->setCellValue('E' . $rowNumber, 'Processing Fee ($)');
                                            $sheet5->setCellValue('F' . $rowNumber, 'Net Amount ($)');
                                            $sheet5->setCellValue('G' . $rowNumber, 'Validated Amount ($)');
                                            $sheet5->setCellValue('H' . $rowNumber, 'Paid Amount ($)');
                                            $sheet5->setCellValue('I' . $rowNumber, 'Total Revenue ($)');

                                            $i = $rowNumber + 1;
                                            // dd($i);
                                            foreach ($finalCodes4 as $key => $value) {
                                                $sheet5->cell('A' . $i . ':I' . $i, function ($cell) use ($color) {
                                                    $cell->setAlignment('center'); // Center horizontally
                                                    $cell->setValignment('center'); // Center vertically
                                                });
                                                $sheet5->setCellValue('A' . $i, $value['Business Name']);
                                                $sheet5->setCellValue('B' . $i, $value['Policy Name']);
                                                $sheet5->setCellValue('C' . $i, isset($value['No of Tickets']) ? $value['No of Tickets'] : $value['No of Ticket']);
                                                $sheet5->setCellValue('D' . $i, isset($value['Gross Amount ($)']) ? $value['Gross Amount ($)'] : $value['Gross Total ($)']);
                                                $sheet5->setCellValue('E' . $i, isset($value['Processing Fee ($)']) ? $value['Processing Fee ($)'] : $value['Processing Fee ($)']);
                                                $sheet5->setCellValue('F' . $i, $value['Net Amount ($)']);
                                                $sheet5->setCellValue('G' . $i, isset($value['Validated Amount ($)']) ? $value['Validated Amount ($)'] : $value['Validation Amount ($)']);
                                                $sheet5->setCellValue('H' . $i, $value['Paid Amount ($)']);
                                                $sheet5->setCellValue('I' . $i, $value['Total Revenue ($)']);
                                                $i++;
                                            }
                                            $sheet5->cell('A' . $i . ':J' . $i, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center'); // Center vertically
                                            });

                                            $sheet5->setCellValue('A' . $i, 'Total ($)');
                                            $sheet5->setCellValue('B' . $i, '');
                                            $sheet5->setCellValue('C' . $i, $validationTicketTotal);
                                            $sheet5->setCellValue('D' . $i, $totalGrossAmount);
                                            $sheet5->setCellValue('E' . $i, $totalServiceAmount);
                                            $sheet5->setCellValue('F' . $i, $totalNetAmount);
                                            $sheet5->setCellValue('G' . $i, $validationAmountTotal);
                                            $sheet5->setCellValue('H' . $i, $validationPaidAmountTotal);
                                            $sheet5->setCellValue('I' . $i, $validatedGTotal);

                                            /** for color of non revenue total row */
                                            $nonRevenueTotal = count($finalCodes4) + $rowNumber + 1;
                                            $row_name = 'A' . $nonRevenueTotal . ':J' . $nonRevenueTotal;
                                            $sheet5->cell($row_name, function ($row) use ($color) {
                                                $row->setBackground($color);
                                                $row->setFontColor('#ffffff');
                                                $row->setFont(array(
                                                    'family' => 'Calibri',
                                                    'size' => '12',
                                                    'bold' => true
                                                ));
                                            });
                                        }
                                        $totalAmountForNonValidated = $validationPaidAmountTotal + $TotalRevenueNonValidated + $permitGrossTotalAmount;
                                        $sheet5->mergeCells('I3:J3');
                                        $sheet5->setCellValue('I3', '$' . $totalAmountForNonValidated);

                                        /** End Non Revenue Section: eValidation */


                                        /** payment breakdown Section Started. **/
                                        $jCell = $i + 2;
                                        // dd($i, $jCell);
                                        $sheet5->mergeCells('A' . $jCell . ':J' . $jCell);
                                        //$sheet5->mergeCells('E' . $jCell . ':I' . $jCell);

                                        $sheet5->setCellValue('A' . $jCell, 'Payment Breakdown');

                                        $sheet5->cell('A' . $jCell, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D9E1F2');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });



                                        // // Total Payment Received
                                        // dd($TotalPaymentReceived, $TotalCc, $validationPaidAmountTotal, $permitGrossTotalAmount, $TotalRevenueNonValidated);
                                        // $totalPayment = $TotalPaymentReceived + $TotalCc + $validationPaidAmountTotal + $permitGrossTotalAmount + $TotalRevenueNonValidated;
                                        $totalPayment =  $TotalRevenueNonValidated + $permitGrossTotalAmount + $validationPaidAmountTotal;

                                        $this->totalAmounts = $totalPayment;
                                        // $totalPayment = $grossAmountNonValidated;
                                        $newCell = $jCell + 1;
                                        $sheet5->getStyle('B' . $newCell)->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->setCellValue('A' . $newCell, 'Total Payment Received ($)');
                                        $sheet5->setCellValue('B' . $newCell, $totalPayment);
                                        // total validated Amount
                                        $ValidatedCell = $jCell + 2;
                                        $sheet5->getStyle('B' . $ValidatedCell)->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->setCellValue('A' . $ValidatedCell, 'Total Validated Amount ($)');
                                        $sheet5->setCellValue('B' . $ValidatedCell, $validationAmountTotal);

                                        $sheet5->setCellValue('E' . $newCell, 'Total Revenue ($)');
                                        $sheet5->setCellValue('F' . $newCell, ($totalPayment + $validationAmountTotal));

                                        $sheet5->setCellValue('E' . $ValidatedCell, 'Net Revenue ($)');
                                        $sheet5->setCellValue('F' . $ValidatedCell, floatval($totalPayment + $validationAmountTotal - ($processingFeeNonValidated)));



                                        $nonCashCell = $ValidatedCell + 2;
                                        $sheet5->cell('A' . $nonCashCell . ':J' . $nonCashCell, function ($row) use ($color) {
                                            $row->setFontColor('#ffffff');
                                            $row->setBackground($color);
                                            $row->setFont(array(
                                                'family' => 'Calibri',
                                                'size' => '12',
                                                'bold' => true
                                            ));

                                            $row->setFontWeight('bold');
                                            $row->setValignment('center');
                                        });

                                        $sheet5->row($nonCashCell, array('Non-Cash Receipts'));
                                        $sheet5->setCellValue('D' . $nonCashCell, 'Cash Receipts');
                                        $cardTupeCell = $nonCashCell + 1;
                                        $totalCell = $nonCashCell + 2;
                                        $sheet5->cell($cardTupeCell, function ($cell) use ($color) {
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#EDEDED');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });

                                        $sheet5->cell('B' . $cardTupeCell, function ($cell) use ($color) {
                                            $cell->setAlignment('right'); // Center horizontally
                                            $cell->setValignment('right'); // Center vertically
                                        });
                                        $sheet5->cell('E' . $cardTupeCell, function ($cell) use ($color) {
                                            $cell->setAlignment('right'); // Center horizontally
                                            $cell->setValignment('right'); // Center vertically
                                        });
                                        $sheet5->cell('F' . $cardTupeCell, function ($cell) use ($color) {
                                            $cell->setAlignment('right'); // Center horizontally
                                            $cell->setValignment('right'); // Center vertically
                                        });
                                        $sheet5->setCellValue('A' . $cardTupeCell, 'Card Type');
                                        $sheet5->setCellValue('B' . $cardTupeCell, 'Total ($)');

                                        $sheet5->setCellValue('D' . $cardTupeCell, 'Payment Type');
                                        $sheet5->setCellValue('E' . $cardTupeCell, 'Total ($)');
                                        $sheet5->setCellValue('F' . $cardTupeCell, 'Discount ($)');
                                        $i = $cardTupeCell + 1;
                                        $j = $cardTupeCell + 1;
                                        foreach ($finalCodes3 as $key => $value) {
                                            if ($value['total'] > 0) {
                                                $sheet5->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                                                $sheet5->setCellValue('A' . $i, $value['no_cash_receipts']);
                                                $sheet5->setCellValue('B' . $i, $value['total']);
                                                $i++;
                                            }
                                        }
                                        foreach ($finalCodes2 as $key => $val) {
                                            if ($val['total'] > 0) {
                                                $sheet5->setCellValue('D' . $j, $val['payment_type']);
                                                $sheet5->setCellValue('E' . $j, $val['total']);
                                                $sheet5->setCellValue('F' . $j, $val['discount']);
                                                $j++;
                                            }
                                        }
                                        $cellColor = 'A' . $i . ':B' . $i;
                                        $sheet5->cell($cellColor, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                            $row->setFont(array(
                                                'family' => 'Calibri',
                                                'size' => '12',
                                                'bold' => true
                                            ));
                                        });

                                        $cellColor = 'D' . $j . ':F' . $j;
                                        $sheet5->cell($cellColor, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                            $row->setFont(array(
                                                'family' => 'Calibri',
                                                'size' => '12',
                                                'bold' => true
                                            ));
                                        });
                                        $sheet5->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                                        // echo$i;die;
                                        $sheet5->row($i, array('Total ($)', $TotalCc));
                                        $sheet5->setCellValue('D' . $j, 'Total ($)');
                                        $sheet5->setCellValue('E' . $j, $TotalPaymentReceived);
                                        $sheet5->setCellValue('F' . $j, ($TotalValidatedAmount));
                                        // //end payment breakdown issue
                                        //merge cell for formatting last data
                                        if ($i < $j) {
                                            $i = $i + 1;
                                        }
                                        $k = $i + 1;
                                        $rowVerticalFormate = 'J1:J' . $k;
                                        // $sheet5->mergeCells($rowVerticalFormate);
                                        //horigental cell
                                        $rowHorizentalFormate = 'A' . $k . ':J' . $k;
                                        $sheet5->mergeCells($rowHorizentalFormate);
                                    });
                                }
                                // else {
                                //     $this->log->info("No Record Found ");
                                // }
                                // }
                            }
                        )->store('xls');

                        $this->log->info('Woodman Daily Revenue Save Successfully');
                        $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');

                        $data['totalTickets']   = ($this->totalTickets);
                        $data['netValue']       = $this->totalAmounts;
                        $data['overAllTotal']   = $this->totalAmounts;
                        $data['report_name']    = 'daily_revenue_report';
                        $data['location_name']  = $locationName;
                        $data['themeColor']     = $color;
                        $data['mail_body']      = "PFB summary of today’s Revenue Report. A detailed report is also attached.";

                        $this->log->info('Woodman Print Data ' . json_encode($data));
                        if ($this->totalTickets > 0) {
                            Mail::send('townsend.cashier-shift-report', $data, function ($message)  use ($excelSheetName) {
                                // $message->bcc(['<EMAIL>']);
                                $message->to(config('parkengage.townsend.woodman_revenue_emails'));
                                $message->subject(config('parkengage.townsend.woodman_revenue_subject'));
                                $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                                $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                                $this->log->info("woodman Mail Sent success with failname :  {$path_to_file}");
                                if (file_exists($path_to_file)) {
                                    // $this->log->info("Path Of file and name 333 {$path_to_file}");
                                    $message->attach($path_to_file);
                                }
                            });
                        }
                    } else {
                        $this->log->info("No Facility Found ");
                        // no Facility Found 
                    }
                }
            }
        } catch (\Throwable $th) {
            //throw $th;
            $this->log->error('WLA Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine());
            $errorMessage = 'WLA Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine();
            // $data['partner_key'] = "PCI";
            $data['exception'] = "Exception in PCI Daily Revenue Report " . $errorMessage;
            Mail::send('email-exception', $data, function ($message) {
                $message->to(config('email_exceptions'));
                $message->subject("Email Exception :- PCI Daily Revenue Report");
                $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
            });
        }
    }

    public function getRmWiseData()
    {
        $driveupData = "SELECT COUNT(distinct(t.id)) AS ticketCount,
                COALESCE(t.grand_total, 0) AS grand_total,
                COALESCE(t.grand_total, 0) AS payAmount,
                SUM(COALESCE(t.tax_fee, 0)) AS tax_fee,
                SUM(COALESCE(t.processing_fee, 0)) AS processing_fee,
                SUM(COALESCE(t.grand_total, 0)) AS Pay_grand_total,
                SUM(COALESCE(t.parking_amount, 0)) AS net_amount,
                SUM(COALESCE(t.discount_amount, 0)) AS discount_amount,
                SUM(COALESCE(t.paid_amount, 0)) AS validated_amount,
                f.full_name,
                t.length,
                CASE
                    WHEN t.is_overstay = '1' THEN sum(CEIL(COALESCE(t.length, 0) + COALESCE(ot.length, 0)))
                    ELSE SUM(CEIL(COALESCE(t.length, 0)))
                END AS lenghtInMints,
                sum(ot.grand_total) as overstayGrandTotal,
                sum(ot.discount_amount) as overstayDiscount
                from tickets as t
                inner join facilities as f on f.id = t.facility_id
                left join overstay_tickets as ot on t.id = ot.ticket_id
                where  t.permit_request_id is null AND t.partner_id IN (" . $this->partnerId . ") and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "'  and t.paid_type='9' AND t.facility_id = '" . $this->facilityId . "' and t.is_checkout ='1' and t.deleted_at is null and t.reservation_id is null group by t.grand_total,t.is_overstay order by t.grand_total";

        $this->log->info('Main Query ' . $driveupData);
        $driveupDataResult = DB::select($driveupData);

        $reservationData = "SELECT COUNT(r.id) AS ticketCount,		
        COALESCE(r.total, 0) AS payAmount,
        SUM(COALESCE(r.total, 0)) AS Pay_grand_total,
        SUM(COALESCE(r.tax_fee, 0)) AS tax_fee,
        SUM(COALESCE(r.processing_fee, 0)) AS processing_fee,
        SUM(COALESCE(r.discount, 0)) AS discount_amount,
        f.full_name,
        t.length,
        group_concat(r.id) as reservation_ids,
        CASE
            WHEN t.is_overstay = '1' THEN sum(CEIL(COALESCE(r.length, 0) + COALESCE(ot.length, 0)))
        ELSE SUM(CEIL(COALESCE(r.length, 0)))
        END AS lenghtInMints,
        sum(ot.grand_total) as overstayGrandTotal,
        sum(ot.discount_amount) as overstayDiscount
        from reservations as r
        inner join anet_transactions as ant on ant.id = r.anet_transaction_id
        inner join facilities as f on f.id = r.facility_id
        left join tickets as t on t.reservation_id = r.id
        left join overstay_tickets as ot on t.id = ot.ticket_id
            where  r.partner_id IN (" . $this->partnerId . ") and r.start_timestamp >='" . $this->checkinTime . "' and r.start_timestamp <='" . $this->checkoutTime . "'  AND r.facility_id = '" . $this->facilityId . "'  and t.deleted_at is null and r.deleted_at is null group by r.total order by r.total";
        $reservationDataResult = DB::select($reservationData);

        $this->log->info('Reservation  Query ' . $reservationData);

        $dataarray['driveup'] = array($driveupDataResult);
        $dataarray['checkin'] = array($reservationDataResult);
        return $dataarray;
    }


    public function addLogoInExcelHeader($sheet)
    {
        /* Code for logo*/
        $drawing = new PHPExcel_Worksheet_Drawing();

        // For Local
        if (env('ISLOCAL')) {
            $drawing->setPath(public_path('assets/media/images/breeze.png'));
        } else {
            // For Dynamic Live
            $drawing->setPath(storage_path('app/brand-settings/' . $this->brandSetting->logo));
        }
        $drawing->setCoordinates('A1');

        // Adjust the dimensions of the image if needed
        $drawing->setWidth(150);
        $drawing->setHeight(50);
        $drawing->setOffsetX(25);
        $drawing->setOffsetY(10);
        // Add image to worksheet
        $sheet->getDrawingCollection()->append($drawing);
        /* End Code for logo*/


        // !! Text Section 
        $color = $this->color;
        $sheet->mergeCells('A1:J1');
        $sheet->getRowDimension(1)->setRowHeight(60);
        $sheet->setCellValue('A1', 'Daily Revenue Report');
        $sheet->cell('A1:J1', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            // $cell->setBackground($color);
            // $cell->setFontColor('#ffffff');
            $cell->setFontSize('30');
        });
    }
}
