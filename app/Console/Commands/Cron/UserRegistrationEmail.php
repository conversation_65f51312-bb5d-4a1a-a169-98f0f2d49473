<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\User;
use Auth;
use App\Models\ParkEngage\BrandSetting;


class UserRegistrationEmail extends Command
{

    protected $signature = 'email:user-registration {id}';

    protected $description = 'Send User Membership Email.';

    public function handle()
    {
		$id = $this->argument('id');
        $user = User::with('membershipPlans')->find($id);
		$membership_plan = $user->latestMembershipPlan();
		$brand_setting = BrandSetting::where('user_id', $user->created_by)->first();
		
        Mail::send(
            "parkengage.user_registration_email", ['user' => $user, 'membership_plan' => $membership_plan,'brand_setting' => $brand_setting], function ($message) use($user) {
                $message->to($user->email)->subject("Welcome to ParkEngage");
                $message->from(config('parkengage.default_sender_email'));
            }
        );
       
    }
    
}
