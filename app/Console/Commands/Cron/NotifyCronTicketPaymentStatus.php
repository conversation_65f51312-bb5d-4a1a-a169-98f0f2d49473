<?php

namespace App\Console\Commands\Cron;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\Ticket;
use App\Models\Facility;
use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;
use Exception;

class NotifyCronTicketPaymentStatus extends Command
{
    protected $signature = 'notify:cron-ticket-payment-status';

    protected $description = 'Notify if ticket payment cron is not running for more than 30 minutes.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/notify-cron-ticket-payment-status')->createLogger('notify-cron-ticket-payment-status');
    }

    public function handle()
    {
        try {
            $this->log->info("Cron started");

            // $partners = DB::table('users')->where('user_type','3')->distinct()->pluck('created_by');

            $partners = [3307, 19349];

            if (empty($partners)) {
                $this->log->info("No partner IDs found.");
                return;
            }

            foreach ($partners as $partnerId) {
                $facilities = Facility::where('owner_id', $partnerId)->get();

                if ($facilities->isEmpty()) {
                    $this->log->warning("No Facility Found");
                    continue;
                }

                foreach ($facilities as $facility) {
                    $partnerName = User::where('id', $partnerId)->value('name');
                    $facilityId = $facility->id;
                    $facilityName = $facility->full_name ?? "Unknown Facility";

                    $timezone = QueryBuilder::setCustomTimezone($facilityId);
                    $thresholdTime = Carbon::parse('now')->format('Y-m-d H:i:s');

                    $this->log->info("Processing For Facility: $facilityName For End time : $thresholdTime");

                    $tickets = Ticket::select(
                        'tickets.ticket_number',
                        'tickets.created_at',
                        'tickets.updated_at',
                        'tickets.checkin_time',
                        'tickets.checkout_time',
                        'tickets.is_checkout',
                        'datacap_transactions.transaction_retry'
                    )
                        ->join('datacap_transactions', 'tickets.id', '=', 'datacap_transactions.ticket_id')
                        ->where('tickets.facility_id', '=', $facilityId)
                        ->whereNull('tickets.anet_transaction_id')
                        ->where('datacap_transactions.is_payment_complete', '=', '0')
                        ->where('tickets.is_checkout', '=', '0')
                        ->where('datacap_transactions.transaction_retry', '=', '3')
                        ->where('tickets.estimated_checkout', '<', $thresholdTime)
                        ->get();

                    if ($tickets->isEmpty()) {
                        $this->log->info("No tickets found for Facility: $facilityName ");
                        continue;
                    }

                    $this->log->info("Found {$tickets->count()} tickets for Facility: $facilityName. Sending notifications...");
                    $this->notifyDevTeam($tickets, $facilityName, $timezone, $partnerName);
                }
            }

            $this->log->info("Cron ended successfully.");
        } catch (Exception $e) {
            $errorMessage = sprintf(
                "Error: %s in file %s on line %d",
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
            );

            $this->log->error($errorMessage);
        }
    }

    protected function notifyDevTeam($tickets, $facilityName, $timezone, $partnerName)
    {
        $data = [
            'count' => $tickets->count(),
            'partner_name' => $partnerName,
            'facility_name' => $facilityName,
            'updated_date' => Carbon::now($timezone)->toDateTimeString(),
            'tickets' => $tickets->map(function ($ticket) {
                return [
                    'ticket_number'     => $ticket->ticket_number,
                    'checkin_time'      => $ticket->checkin_time, // Ensure this field exists
                    'checkout_time'     => $ticket->checkout_time, // Ensure this field exists
                    'is_checkout'       => $ticket->is_checkout, // Ensure this field exists
                    'created_date'      => Carbon::parse($ticket->created_at)->format('Y-m-d H:i:s'),
                    'transaction_retry' => $ticket->transaction_retry,
                ];
            })->toArray(),
        ];

        $recipients = config('parkengage.notify.recipient_notify', ['<EMAIL>']);

        try {
            sleep(30);
            Mail::send(
                'email.notify-cron-ticket-payment',
                ['data' => $data],
                function ($message) use ($recipients) {
                    $message->to($recipients)
                        ->subject("Alert : Payment fail for listed tickets");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );

            $this->log->info("Notifications sent to recipients: " . implode(", ", $recipients));
        } catch (Exception $e) {
            $this->log->error("Failed to send notification emails: {$e->getMessage()}");
        }
    }




    protected function notifyDevTeamOld($tickets)
    {
        $data = [
            'count' => $tickets->count(),
            'timestamp' => Carbon::now()->toDateTimeString(),
            'tickets' => $tickets->map(function ($ticket) {
                return [
                    'ticket_number' => $ticket->ticket_number,
                    'created_date' => Carbon::parse($ticket->created_at)->format('Y-m-d H:i:s'),
                    'estimated_checkout' => Carbon::parse($ticket->estimated_checkout)->format('Y-m-d H:i:s'),
                    'updated_date' => Carbon::parse($ticket->updated_at)->format('Y-m-d H:i:s'),
                    'transaction_retry' => $ticket->transaction_retry,
                ];
            })->toArray(),
        ];

        $recipients = config("parkengage.notify.recipient_notify");

        try {
            Mail::send(
                'email.notify-cron-ticket-payment',
                ['data' => $data],
                function ($message) use ($recipients) {
                    $message->to($recipients)
                        ->subject("Payment Cron Alert: Null Transactions Detected");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );

            $this->log->info("Notifications sent to recipients: " . implode(", ", $recipients));
        } catch (Exception $e) {
            $this->log->error("Failed to send notification emails: {$e->getMessage()}");
        }
    }
}
