<?php

namespace App\Console\Commands\Cron;

use Mail;
use File;
use Exception;
use Carbon\Carbon;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use App\Models\ParkEngage\UserMembership;
use App\Services\LoggerFactory;

class MembershipExpiredWithEmail extends Command
{

    protected $signature = 'membership-expired-with-email';

    protected $description = 'Expire membership plan with email.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
    
        $this->log = $logFactory->setPath('logs/parkengage')->createLogger('membership_expired');
    }
    public function handle()
    {
       $start_date = date("Y-m-d"); 
       $userMembership = UserMembership::with('users','membershipPlan')->whereDate('end_date' , '<', $start_date)->where('is_active', '1')->get();
       $userIds = '';
       if($userMembership){
           foreach ($userMembership as $key => $value) {
              if((count($value->users)) > 0 && (count($value->membershipPlan) > 0)){
               $userIds .= $value->users->id .',';

               Mail::send(
                "parkengage.membership-expired-email", ['data' => $value], function ($message) use($value) {
                    $message->to($value->users->email)->subject("Membership Plan Expired");
                    $message->from(config('parkengage.default_sender_email'));
                }
                );
               $value->is_active = '2';
               $value->save();
             }
           }
        }
        $this->log->info("Membership expired user ids -- $userIds ");
       echo ($userIds);
        
        
       
    }
    
}
