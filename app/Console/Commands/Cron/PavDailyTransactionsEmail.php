<?php

namespace App\Console\Commands\Cron;

use Response;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Services\Pdf;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\ApiGenericException;
use App\Models\Ticket;
use App\Models\ParkEngage\BrandSetting;
use Illuminate\Support\Facades\DB;

class PavDailyTransactionsEmail extends Command
{

    protected $signature = 'pav:transactions';

    protected $description = 'transactions and total transaction revenue in a daily report that is auto-generated.aa';


    protected $log;
    //pre
    const PAVE_PARTNER = '3307';
    const FACILITY_ID = '144';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/partner-revenue')->createLogger('partner-revenue');
    }


    public function handle()
    {

        $checkInTime  = date('Y-m-d') . ' 00:00:00';
        $checkOutTime    = date('Y-m-d') . ' 23:59:59';
        $checkInTime  = '2024-05-29 00:00:00';
        $checkOutTime    = '2024-05-29 23:59:59';
        $partnerId = self::PAVE_PARTNER;
        $facility_id = self::FACILITY_ID;
        $brandSetting = BrandSetting::where('user_id',  $partnerId)->first();
        $color = $brandSetting->color;
        $dailyTicket = "SELECT COUNT(t.ticket_number) AS trx_count,t.checkin_time,t.checkout_time,t.rate_id,t.is_overstay, t.ticket_number, t.reservation_id,  sum(t.total) as total, sum(t.tax_fee) as tax_fee, sum(t.processing_fee) as processing_fee, sum(t.grand_total) as grand_total, sum(t.parking_amount) as net_amount, sum(t.discount_amount) as validate_amonut,
        (SELECT sum(ot.grand_total) FROM overstay_tickets as ot where ot.ticket_id IN (t.id)) as overstayGrandTotal,
        (SELECT sum(otd.discount_amount) FROM overstay_tickets as otd where otd.ticket_id IN (t.id)) as overstayDiscount
        from tickets as t
        left join facilities as f on f.id = t.facility_id
        where f.active='1' and t.deleted_at is null and t.partner_id IN ('$partnerId') and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime'  and t.facility_id=$facility_id ";
        $dailyTicketResult = DB::select($dailyTicket);
        $total = 0;
        $tax_fee  = $processing_fee = 0;
        $trx_count = 0;

        foreach ($dailyTicketResult as $value) {
            $total += $value->net_amount;
            $tax_fee += $value->tax_fee;
            $trx_count += $value->trx_count;
            $processing_fee += $value->processing_fee;
        }
        $finalTotalAmount = $total + $tax_fee + $processing_fee;
        $date = date("M j, Y", strtotime($checkInTime));
        //  print_r(count($dailyTicketResult ));die;
        try {
            $result = Mail::send(
                "pav-daily-reservation.pav-daily-reservation-report",
                ["transcationcount" => $trx_count, "date" => $date, "finalTotalAmount" => $finalTotalAmount, "color" => $color],
                function ($message) use ($checkInTime) {
                    $message->to(["<EMAIL>"])->subject("Daily Transactions Details - " . date("M j, Y", strtotime($checkInTime)));
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            if ($result) {
                //  dd('email send');
                return "Email sent successfully!";
            } else {

                // Email sending failed
                echo "Failed to send email.";
            }
        } catch (Exception $e) {
            //    dd($e);
            $this->log->error($e);
        }
    }
}
