<?php

namespace App\Console\Commands\Cron;

use Response;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\UserPass;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\ShareConfig;

use App\Models\ParkEngage\RevenueBalance;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\ApiGenericException;

class UpdatePasswordSendMail extends Command
{

    protected $signature = 'update-password-mail {data}';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/user-password-update')->createLogger('password-mail-update');
    }


    public function handle()
    {
        // $id = $this->argument('id');
        // $facility_id = $this->argument('faclity_id');
        $data=$this->argument('data');
        // dd($data);
        try {


            // dd($data);
            $result = Mail::send(
              "platform.email.update_password",
              ['data' => $data],
              function ($message) use ($data) {
                $message->to([$data['receiver']])->subject("Password Changed");
                $message->from(config('parkengage.default_sender_email'));
              }
            );
    



            // if ($result) {
            //     // Email sent successfully
            //     echo "Email sent successfully!";
            // } else {
            //     // Email sending failed
            //     echo "Failed to send email.";
            // }
            // echo "send";
            // print_r($billingDetails);die;
        } catch (Exception $e) {
            $this->log->error($e);
        }
    }
}
