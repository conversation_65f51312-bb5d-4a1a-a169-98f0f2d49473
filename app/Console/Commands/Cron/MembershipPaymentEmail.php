<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\User;
use Auth;


class MembershipPaymentEmail extends Command
{

    protected $signature = 'email:membership-payment {id}';

    protected $description = 'Send User Membership Payment Email.';

    public function handle()
    {
        $id = $this->argument('id');
        $user = User::with('membershipPlans')->find($id);

        //user mail
        Mail::send(
            "parkengage.membership-payment-email", ['user' => $user], function ($message) use($user) {
                $message->to($user->email)->subject("Thank you for the payment");
                $message->from(config('parkengage.default_sender_email'));
            }
        );
       
    }
    
}
