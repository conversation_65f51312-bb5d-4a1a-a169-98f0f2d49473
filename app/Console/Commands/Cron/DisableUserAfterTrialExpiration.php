<?php

namespace App\Console\Commands\Cron;

use Illuminate\Console\Command;

use Carbon\Carbon;
use App\Models\User;

class DisableUserAfterTrialExpiration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'disable:user-after-trial-expiration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $users = User::with('membershipPlans')->where('user_type', User::USER_TYPES['partner'])->orderBy('created_at', 'desc')->get();
		$now = date('Y-m-d H:i:s');
		foreach($users as $user) {
			foreach($user->membershipPlans as $membership_plan) {
				if($membership_plan->pivot->end_date <= $now 
					&& $membership_plan->is_trial_ended != 1) {
					$membership_plan->pivot->is_trial_ended = 1;
					$membership_plan->pivot->save();
				}
			}
		}
    }
}
