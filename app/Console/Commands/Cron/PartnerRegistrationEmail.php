<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\User;
use Auth;


class PartnerRegistrationEmail extends Command
{

    protected $signature = 'email:partner-registration {membership_plan_id}';

    protected $description = 'Send User Membership Email.';

    public function handle()
    {
		$user = User::with('membershipPlans')->find(Auth::user()->id);
		$membership_plan = $user->latestMembershipPlan();
		
        //admin mail
        Mail::send(
            "parkengage.partner_registration_email_admin", ['user' => $user, 'membership_plan' => $membership_plan], function ($message) {
                $message->to(config('parkengage.admin_email'))->subject("New user registration for membership plan");
                $message->from(config('parkengage.default_sender_email'));
            }
        );

        //user mail
        Mail::send(
            "parkengage.partner_registration_email_user", ['user' => $user, 'membership_plan' => $membership_plan], function ($message) use($user) {
                $message->to($user->email)->subject("Welcome to ParkEngage");
                $message->from(config('parkengage.default_sender_email'));
            }
        );
       
    }
    
}
