<?php

namespace App\Console\Commands\Cron;

use Response;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\UserPass;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\RevenueBalance;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\ApiGenericException;

class PartnerEmailInvoice extends Command
{

    protected $signature = 'partner-email-invoice';

    protected $description = 'Send last month revenue balance invoice of each partner at the starting of the month.';


    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/partner-revenue')->createLogger('partner-revenue');
    }


    public function handle()
    {   
        $month = date('Y-m', strtotime("-1 month"));
        $from_date  = date($month.'-01');
        $mid_date  = date($month.'-15');
        $to_date  = date($month.'-t');
        $midDateString = strtotime($mid_date);   
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);
        
        /*$month = date('m', strtotime("-1 month"));
        $from_date  = date('Y-'.$month.'-01');
        $mid_date  = date('Y-'.$month.'-15');
        $to_date  = date('Y-'.$month.'-t');
        $midDateString = strtotime($mid_date);   
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);*/

        $users = User::where('user_type', '3')->where('id','45')->get();
        $this->log->info("Partner Invoice Cron is about to start");
        if($users){
            foreach($users as $user){
                
                //generate invoice and send email  
                $billingDetails = User::with('userPaymentGatewayDetail')->where('id', $user->id)->first();
                $last_from_date = date("Y-m-d",strtotime($from_date."-1 month"));
                $last_date_year_month  = date("Y-m-d",strtotime($from_date));                
	        $last_to_date = date("Y-m",strtotime($last_date_year_month."-1 month"));
                  $last_mid_date  = date($last_to_date.'-15');
                  $lastMidDateString = strtotime($last_mid_date);   
                  $lastLastdate = strtotime(date("Y-m-t", $lastMidDateString));
                  $last_to_date = date("Y-m-d", $lastLastdate);
                 
/*$last_from_date = "2021-12-01";
$last_to_date = "2021-12-31";*/
#$from_date="2021-12-01";
#$to_date="2021-12-31";

                  //last month revenye
                  $lastRevenueBalance = RevenueBalance::where('partner_id', $user->id)->whereDate('from_date', '=' ,$last_from_date)->whereDate('to_date', '=' , $last_to_date)->orderBy('id', 'DESC')->first();
                  $revenueBalance = RevenueBalance::where('partner_id', $user->id)->whereDate('from_date', '=' ,$from_date)->whereDate('to_date', '=' , $to_date)->orderBy('id', 'DESC')->first();
                  if($revenueBalance){
                    $countBookingShare = 0;
                    $countpassShare = 0;
                    $bookingShareAmount = 0;
                    $passShareAmount = 0;
                    $passRate = 0;
                    $bookingRate = 0;
                    $paidAmount = 0;  
                    $paidDate = '';
                    $lastPaidAmount = 0;  
                    $lastPaidDate = '';

                    //last month revenue
                    $lastRevenueHistory = RevenuePaymentHistory::where('partner_id', $user->id)->whereDate('from_date', '=' ,$last_from_date)->whereDate('to_date', '=' , $last_to_date)->get();

                    if($lastRevenueHistory){
                      foreach ($lastRevenueHistory as $k => $v) {
                        $lastPaidAmount += $v->total;
                        $lastPaidDate = $v->created_at;
                      }
                      $billingDetails->last_paid_amount = $lastPaidAmount;
                      $billingDetails->last_paid_amount_date = $lastPaidDate;
                    }else{
                      $billingDetails->last_paid_amount = $lastPaidAmount;
                      $billingDetails->last_paid_amount_date = '';
                    }
                    $revenueHistory = RevenuePaymentHistory::where('partner_id', $user->id)->whereDate('from_date', '=' ,$from_date)->whereDate('to_date', '=' , $to_date)->get();
                    if($revenueHistory){
                      foreach ($revenueHistory as $k => $v) {
                        $paidAmount += $v->total;
                        $paidDate = $v->created_at;
                      }
                      $billingDetails->paid_amount = $paidAmount;
                      $billingDetails->paid_amount_date = $paidDate;
                    }else{
                      $billingDetails->paid_amount = $paidAmount;
                      $billingDetails->paid_amount_date = '';
                    }
                    $billingDetails->revenue_balance = $revenueBalance;
                    $billingDetails->last_revenue_balance = $lastRevenueBalance;
                    $billingDetails->user = $user;
                    $pdf = (new RevenueBalance())->generateInvoice($billingDetails, Pdf::class);
                  
		 try{
                        Mail::send(
                        "billing-invoice.email-invoice", ["data" => $billingDetails], function ($message) use($user, $pdf, $from_date, $to_date) {
                         $message->to(["<EMAIL>","<EMAIL>"])->subject("Invoice Details Of ". date("M j, Y", strtotime($from_date)) . ' To '. date("M j, Y", strtotime($to_date)));

                           //$message->to(["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"])->subject("Invoice Details Of ". date("M j, Y", strtotime($from_date)) . ' To '. date("M j, Y", strtotime($to_date)));
                            $message->from(config('parkengage.default_sender_email'));
                            $message->attachData($pdf,"AAA Parking - Invoice - ".date("Ym", strtotime($from_date)).".pdf");
                        }
                        );
                    }catch(Exception $e){
                     $this->log->error($e);   
                    }
                    $this->log->info("billing invoice email sent to ". $user->email);
                }else{
                    $this->log->info("Sorry! No billing generated for this month.");
                  
                }

                
                


                 



            }//foreach close
        }//if close
        
    }
    
}

