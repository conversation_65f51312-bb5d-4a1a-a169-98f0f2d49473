<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\Gate;
use App\Models\ParkEngage\Adam;
use App\Models\Facility;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Classes\ParkengageGateApi;

class GateDown extends Command
{

    protected $signature = 'gate:down-check';

    protected $description = 'Send notification of Gate Down.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/parkengage/gate-down')->createLogger('gate-down');
    }


    public function handle()
    {
        try{
            
        $this->log->info("Checking Gate Down or not cron start");
        //$adams = Adam::with('facility','gates')->whereNull('deleted_at')->where('is_active',1)->where('id',21)->get();
         $adams = Adam::with('facility','gates')->whereNull('deleted_at')->where('is_active',1)->get();
            
      
         
        foreach($adams as $adam)
        {
           // $this->log->info("Adam details".$adam);
            if(isset($adam->facility) && $adam->facility->is_gated_facility == 1)
            {   

                        $gateDetails = UserPaymentGatewayDetail::whereNotNull('host')->where('user_id', $adam->partner_id)->first();
                        if(isset($gateDetails) && $gateDetails->host !=''){
                        $gate = Gate::where('id',$adam->gate_id)->whereNull('deleted_at')->where('is_active',1)->first();

                        if($gate)
                        {
                            $params = ['gate_id'=>$gate->gate];
                            
                            $response = ParkengageGateApi::isGateDown($params, $gateDetails->host);
                            
                            if($response['success'] == false)
                            {
                                $this->log->info('Email sent start');
                                $this->log->info("Gate Number: {$adam->gate_id},Facility id: {$adam->facility_id}");

                                
                                    $data['facility_name'] = $adam->facility->full_name;
                                    $data['gate_name'] = isset($adam->gates->gate_name)?$adam->gates->gate_name:null;
                                    $data['gate_type'] = isset($adam->gates->gate_type)?$adam->gates->gate_type:null;
                                    $data['adam_name'] = $adam->name;
                                    $data['adam_ip'] = $adam->ip;
                                    $data['adam_port'] = $adam->port_number;
                               
                                    Mail::send(
                                        "parkengage.notify-gate-down", ['data' => $data], function ($message) use($data) {
                                            $message->to(['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'])->subject("Alert! gate is down for facility ".$data["facility_name"]);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );


                                   $this->log->info("email sent end"); 
                               

                            }

                        }

                    }
            
            
            }



           

           
        } // foreach end

        
    }catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
           
            $this->log->info("error log created.");
        }
       
       $this->log->info("cron completed"); 
}
   

     
       
}
    


