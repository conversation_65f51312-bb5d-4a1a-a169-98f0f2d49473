<?php

namespace App\Console\Commands\Cron;

use Mail;
use File;
use Exception;
use Auth;
use Carbon\Carbon;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use App\Models\ParkEngage\UserMembership;
use App\Services\LoggerFactory;

class MembershipCancellationEmail extends Command
{

    protected $signature = 'email:membership-cancellation';

    protected $description = 'Membership cancellation email';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
    
        $this->log = $logFactory->setPath('logs/parkengage')->createLogger('membership_expired');
    }
    public function handle()
    {
		$user = User::find(Auth::user()->id);
		$membership_plan = $user->lastUpdatedMembershipPlan();
		
        //admin mail
        Mail::send(
            "parkengage.membership-cancellation-email", ["user" => $user, "membership_plan" => $membership_plan], function ($message) use($user) {
                $message->to($user->email)->subject("Membership Cancellation Notification");
                $message->from(config('parkengage.default_sender_email'));
            }
        );
    }
    
}
