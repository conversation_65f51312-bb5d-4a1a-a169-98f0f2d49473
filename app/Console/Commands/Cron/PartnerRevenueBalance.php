<?php

namespace App\Console\Commands\Cron;

use Response;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\UserPass;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\RevenueBalance;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\ApiGenericException;

class PartnerRevenueBalance extends Command
{

    protected $signature = 'partner-revenue-balance';

    protected $description = 'Add last month revenue balance of each partner at the starting of the month.';


    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/partner-revenue')->createLogger('partner-revenue');
    }


    public function handle()
    {   
        $month = date('Y-m', strtotime("-1 month"));
        $from_date  = date($month.'-01');
        $mid_date  = date($month.'-15');
        $to_date  = date($month.'-t');
        $midDateString = strtotime($mid_date);   
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);
        /*$month = date('m', strtotime("-1 month"));
        $from_date  = date('Y-'.$month.'-01');
        $mid_date  = date('Y-'.$month.'-15');
        $to_date  = date('Y-'.$month.'-t');
        $midDateString = strtotime($mid_date);   
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);*/
           
        $users = User::where('user_type', '3')->where('id','45')->get();
        $this->log->info("Cron is about to start");
        if($users){
            foreach($users as $user){
                $totalBalance = 0;
                $totalCurrentMonthBalance = 0;
                $totalRemainder = 0;
                $userConfig = UserPaymentGatewayDetail::where('user_id', $user->id)->first();
                if(!$userConfig){
                    $this->log->error("Partner does not have configuration : ".$user->id);
                   continue;
                }

             

                $checkinShare = $userConfig->checkin_share ? $userConfig->checkin_share : 0;
                $passShare = $userConfig->pass_share ? $userConfig->pass_share : 0;
                $passCheckinShare = $userConfig->pass_checkin_share ? $userConfig->pass_checkin_share : 0;
                $userPasses = UserPass::where('partner_id', $user->id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNotNull('anet_transaction_id')->get();

                $this->log->error("Get all the passes : ".$user->id);
                $total = 0;
                $passCount = 0;
                $bookingCount = 0;
                $totalShare = 0;
                $passShareAmount = 0;
                $bookingShareAmount = 0;
                $totalPassDays = 0;
                $data = [];
try{    

            if(count($userPasses) > 0){
                    $this->log->info("Get all the passes of : ".$user->id);
 
                   foreach ($userPasses as $userPass) {
                        $total += $total + $userPass->total;
                        $data['pass_share'] = $passShare;
                        $data['pass_booking_share'] = $passCheckinShare;
                        $passShareAmount += ($passShare + $passCheckinShare) * $userPass->total_days;
                        $totalShare += ($passShare + $passCheckinShare) * $userPass->total_days;
                        $totalCurrentMonthBalance +=($passShare + $passCheckinShare) * $userPass->total_days;
                        $totalPassDays += $userPass->total_days;
                        $passCount++;
                    }
                }//userpass close
}catch(Exception $e)
{
 $this->log->info("exception : ".json_encode($e));


}
try{
                $reservations = Reservation::where('partner_id', $user->id)->whereNull('user_pass_id')->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNotNull('anet_transaction_id')->get();
                if(count($reservations) > 0){
                    $this->log->info("Get all the booking of : ".$user->id);
                    if($checkinShare > 0){
                        foreach ($reservations as $reservation) {
                            $total += $total + $reservation->total;
                            $data['booking_share'] = $checkinShare;
                            $totalShare += $checkinShare;                            
                            $bookingShareAmount += $checkinShare;
                            $totalCurrentMonthBalance += $checkinShare;
                            $bookingCount++;
                        }
                    }
                }//reservation close
}catch(Exception $e)
{
 $this->log->info("exception : ".json_encode($e));


}

                 if(count($data) > 0){

                    $configTotal = $userConfig->total_balance;
                    $data['total'] = $total;
                    $data['pass_count'] = $passCount;
                    $data['booking_count'] = $bookingCount;
                    $data['total_share'] = $totalShare;
                    $data['partner_id'] = $user->id;
                    $data['from_date'] = $from_date;
                    $data['to_date'] = $to_date;
                    $data['remainder'] = $configTotal;
                    $data['final_amount'] = $totalShare + $configTotal;
                    $data['booking_share_amount'] = $bookingShareAmount;
                    $data['pass_share_amount'] = $passShareAmount;
                    $data['pass_days'] = $totalPassDays;
                    RevenueBalance::create($data);

                    $userConfig->last_cron_run = date("Y-m-d");
                    $userConfig->total_balance = $userConfig->total_balance + $totalCurrentMonthBalance;
                    $userConfig->current_month_balance = $totalCurrentMonthBalance;
                    $userConfig->remainder = $userConfig->remainder + $totalRemainder;
                   $userConfig->save();    
                    $this->log->info("Save all the balance of user : ".$user->id);
                 }    

                //generate invoice and send email  
                /*$billingDetails = User::with('userPaymentGatewayDetail')->where('id', $user->id)->first();
                $last_from_date = date("Y-m-d",strtotime($from_date."-1 month"));
                  $last_to_date = date("Y-m",strtotime($to_date."-1 month"));
                  $last_mid_date  = date($last_to_date.'-15');
                  $lastMidDateString = strtotime($last_mid_date);   
                  $lastLastdate = strtotime(date("Y-m-t", $lastMidDateString));
                  $last_to_date = date("Y-m-d", $lastLastdate);
                  
                  //last month revenye
                  $lastRevenueBalance = RevenueBalance::where('partner_id', $user->id)->whereDate('from_date', '=' ,$last_from_date)->whereDate('to_date', '=' , $last_to_date)->orderBy('id', 'DESC')->first();
                  $revenueBalance = RevenueBalance::where('partner_id', $user->id)->whereDate('from_date', '=' ,$from_date)->whereDate('to_date', '=' , $to_date)->orderBy('id', 'DESC')->first();
                  if($revenueBalance){
                    $countBookingShare = 0;
                    $countpassShare = 0;
                    $bookingShareAmount = 0;
                    $passShareAmount = 0;
                    $passRate = 0;
                    $bookingRate = 0;
                    $paidAmount = 0;  
                    $paidDate = '';
                    $lastPaidAmount = 0;  
                    $lastPaidDate = '';

                    //last month revenue
                    $lastRevenueHistory = RevenuePaymentHistory::where('partner_id', $user->id)->whereDate('from_date', '=' ,$last_from_date)->whereDate('to_date', '=' , $last_to_date)->get();

                    if($lastRevenueHistory){
                      foreach ($lastRevenueHistory as $k => $v) {
                        $lastPaidAmount += $v->total;
                        $lastPaidDate = $v->created_at;
                      }
                      $billingDetails->last_paid_amount = $lastPaidAmount;
                      $billingDetails->last_paid_amount_date = $lastPaidDate;
                    }else{
                      $billingDetails->last_paid_amount = $lastPaidAmount;
                      $billingDetails->last_paid_amount_date = '';
                    }
                    $revenueHistory = RevenuePaymentHistory::where('partner_id', $user->id)->whereDate('from_date', '=' ,$from_date)->whereDate('to_date', '=' , $to_date)->get();
                    if($revenueHistory){
                      foreach ($revenueHistory as $k => $v) {
                        $paidAmount += $v->total;
                        $paidDate = $v->created_at;
                      }
                      $billingDetails->paid_amount = $paidAmount;
                      $billingDetails->paid_amount_date = $paidDate;
                    }else{
                      $billingDetails->paid_amount = $paidAmount;
                      $billingDetails->paid_amount_date = '';
                    }
                    $billingDetails->revenue_balance = $revenueBalance;
                    $billingDetails->last_revenue_balance = $lastRevenueBalance;
                    $pdf = (new RevenueBalance())->generateInvoice($billingDetails, Pdf::class);
                    try{
                        Mail::send(
                        "billing-invoice.email-invoice", ["data" => $billingDetails], function ($message) use($user, $pdf, $from_date, $to_date) {
                            $message->to($user->email)->subject("Invoice Details Of ". date("M j, Y", strtotime($from_date)) . ' To '. date("M j, Y", strtotime($to_date)));
                            $message->from(config('parkengage.default_sender_email'));
                            $message->attachData($pdf,"invoice-".date("d-m-Y").".pdf");
                        }
                        );
                    }catch(Exception $e){
                     $this->log->error($e);   
                    }
                    $this->log->info("pdf generated and email sent to ". $user->email);
                }else{
                  
                }*/              
                


                 



            }//foreach close
        }//if close
        
    }
    
}

