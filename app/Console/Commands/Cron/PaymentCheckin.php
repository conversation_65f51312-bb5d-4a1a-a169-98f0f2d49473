<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;


class PaymentCheckin extends Command
{

    protected $signature = 'email:checkin-payment {id} {type}';

    protected $description = 'Send checkin payment email.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/checkin-checkout')->createLogger('checkin-checkout');
    }

    public function handle()
    {
        try{
       $id = $this->argument('id');
       $type = $this->argument('type');
       if($type == 'normal'){
            $this->log->info("Mail start");
            $data = Ticket::with('facility')->where('id', $id)->first();
            //user mail
            try{
            Mail::send(
                "checkin-checkout.email-payment", ['data' => $data], function ($message) use($data) {
                    $message->to($data->user->email)->subject("ParkEngage Booking Details Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Mail sent to ".$data->user->email);
            }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
       }else{
            $data = OverstayTicket::with('facility')->where('id', $id)->first();
            $ticket = Ticket::where('ticket_number', $data->ticket_number)->first();
            //user mail
            Mail::send(
                "checkin-checkout.overstay-email-payment", ['data' => $data,'ticket' => $ticket], function ($message) use($data) {
                    $message->to($data->user->email)->subject("ParkEngage Overstay Booking Details Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
       }

       //$this->log->info("Mail done");
       }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
       
       
    }
    
}

