<?php
namespace App\Console\Commands\Cron;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Models\Ticket;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use App\Services\LoggerFactory;
use Carbon\Carbon;

class CloseLprOpenTicket extends Command
{
    protected $signature = 'close:lpr-import-tickets {partner_id}';
    protected $description = 'Close all open ticket from lpr';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/lpr/close-open-ticket')->createLogger('close-open-ticket');
    }
    
    public function handle()
    {
        $success = [];$move = [];$sftp='';$desired_date =  date('Y-m-d');
        try {            
            $partner_id  = $this->argument('partner_id');
            $this->log->info("Close all open ticket from lpr: ".$desired_date." for PartnerId: ".$partner_id);
            if(empty($partner_id) || is_null($partner_id)){
                $this->log->info("Please check partner_id");
                die;
            } 
            
            $updatedRows = Ticket::where('is_checkin', '0')
            ->where('partner_id', $partner_id)
            ->where('is_checkout', '0')
            ->where('is_closed', '0')
            ->whereNotNull("lpr_session_id")
            ->where('checkin_time', '<', Carbon::now()->subMinutes(30))
            ->update([
                'is_closed' => 1,
                'closed_date' => Carbon::now()
            ]);
            $this->log->info("Update done, No of rows updated: ".$updatedRows);
        }catch (\Exception $e) {
            $msg = "Error Date: ".$desired_date.", PartnerId: ".$partner_id.", Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            die;
        }
    }
    
}
