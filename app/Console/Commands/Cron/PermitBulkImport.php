<?php
namespace App\Console\Commands\Cron;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Models\PermitRate;
use App\Models\ParkEngage\PermitEmailRateMapping;
use Illuminate\Support\Facades\DB;
use League\Csv\Reader;
use App\Models\Facility;
use App\Models\BlackListedVehicle;
use App\Models\PermitRateDescription;
use App\Models\PermitVehicle;
use App\Models\PermitRequest;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use App\Services\LoggerFactory;
use Illuminate\Support\Facades\File;
use App\Models\PermitVehicleMapping;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
class PermitBulkImport extends Command
{
    protected $signature = 'import:permit-bulk-upload {partner_id}';
    protected $description = 'Import permit bulk upload data from SFT CSV file';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/are/permitupload')->createLogger('uploadpermit');
    }
    
    public function handle()
    {
        $success = [];$move = [];$sftp='';
        try {
            $dataByEmail = [];
			$permit_final_amount="0.00"; $permit_rate="0.00";
            $desired_start_date =   date('Y-m-d');
            $partner_id  = $this->argument('partner_id');

            if($partner_id == config('parkengage.PARTNER_CITYPARKING')){
                $facility_name = config('parkengage.YACHT_GARAGE_CODE');
                $permit_type_name = '';
            }else{
                die("no partner");
            }

            $userDetails = User::find($partner_id);
            $partner = $userDetails->name;
            
            $remoteDir = $directory = '/opt/uploads/';
            $destinationDir = $directory.'moved-files/';

            if (File::exists($directory)) {
                // Get all files (non-recursive) from the directory.
                $files = File::files($directory);
                if(count($files) > 0){
                    foreach ($files as $file) {
                        
                    }
                }else{
                    echo "no files exist.";
                    die;
                }
            } else {
                //echo "Directory does not exist.";
                $error['auth'][] = $this->errorMessage("Directory does not exist."); 
                $this->sendMail($error,$partner,$success,$move);
                die;
            }


           /*  $sftp = new SFTP($host, $port);
            if (!$sftp->login($username, $password)) {
                $error['auth'][] = $this->errorMessage("Authentication failed"); 
                $this->sendMail($error,$partner,$success,$move);
                die;
            } */
           
            /* $allEntries = $sftp->nlist($remoteDir);
            if ($allEntries === false) {
                die("Failed to list files in $remoteDir");
            } */
            $error = [];
            $adminUser = User::where('email', '<EMAIL>')->first(); // Replace with your logic

            if (!$adminUser) {
                $error['auth'][] = $this->errorMessage('Admin user not found.'); 
                $this->sendMail($error,$partner,$success,$move);
                die;
            }

            /* $files = array_filter($allEntries, function ($entry) use ($sftp, $remoteDir) {
                // Skip current and parent directory
                if ($entry === '.' || $entry === '..') {
                    return false;
                }
                
                // Check if it's a directory; if it is, exclude it.
                // Ensure a trailing slash is added to $remoteDir if needed.
                $remotePath = rtrim($remoteDir, '/') . '/' . $entry;
                return !$sftp->is_dir($remotePath);
            }); */

            // Log in the user
            Auth::login($adminUser);
            
            foreach ($files as $file) {
                echo $file;echo "\n";
                if ($file === '.' || $file === '..') {
                    continue; // Skip current & parent directory entries
                }

                if (is_object($file) && method_exists($file, 'getFilename')) {
                    $file = $file->getFilename();
                } else {
                    // Otherwise, treat it as a string path
                    $file = basename($file);
                }

                $this->infoMessage("file: ".$file); 

                // Validate if it's a CSV file
                if (pathinfo($file, PATHINFO_EXTENSION) !== 'csv') {
                    $this->error("Skipping non-CSV file: $file\n");
                    continue;
                }

                $sourcePath  = $remoteDir . $file;echo "\n";

                // Read CSV File Content
                //$csvContent = $sftp->get($sourcePath);
              //  $csvContent = File::get($sourcePath);
                $csvContent = File::get($sourcePath);
                $csvContent = mb_convert_encoding($csvContent, 'UTF-8', 'ISO-8859-1');
                if ($csvContent === false) {
                    $error[$file][]= $this->errorMessage("Failed to read file: $sourcePath\n"); 
                    $moveResponse = $this->moveFile($destinationDir,$file,$sourcePath,$sftp);
                    if($moveResponse!='FAIL'){
                        $move[$file] = $moveResponse;
                    }
                    continue;
                }
                
                $rows = explode("\n", trim($csvContent));
                if (count($rows) < 2) { // At least one data row needed
                    $error[$file][]= $this->errorMessage("CSV file is empty or invalid"); 
                    $moveResponse = $this->moveFile($destinationDir,$file,$sourcePath,$sftp);
                    if($moveResponse!='FAIL'){
                        $move[$file] = $moveResponse;
                    }
                    continue;
                }
        
                // Validate header row
                $header = str_getcsv($rows[0]);
                
                $expectedHeaders = ['start date','end date','license plate number','make','model','color','first name','last name', 'email', 'primary address - formatted line 1', 'primary address - formatted line 2', 'city', 'primary home address - state', 'primary home address - postal code','permit status'];
               

                // Trim and clean the headers from the CSV
                $cleanedHeaders = array_map(function ($head) {
                    $trimmedHead = strtolower(trim($head));
                    $trimmedHead = preg_replace('/\x{FEFF}/u', '', $trimmedHead);
                    $trimmedHead = str_replace('*', '', $trimmedHead); // Remove asterisks
                    return $trimmedHead;
                }, $header);

                // Find missing headers
                $missingHeaders = array_diff($expectedHeaders, $cleanedHeaders);

                $extraHeaders = array_diff($cleanedHeaders, $expectedHeaders);

                if (!empty($missingHeaders)) {
                    $error[$file][] = $this->errorMessage("Invalid CSV headers. Missing Column: " . implode(", ", $missingHeaders)); 
                    $moveResponse   = $this->moveFile($destinationDir,$file,$sourcePath,$sftp);
                    if($moveResponse!='FAIL'){
                        $move[$file] = $moveResponse;
                    }
                    continue;
                }

                // Check if there are extra headers
                if (!empty($extraHeaders)) {
                    $error[$file][] = $this->errorMessage("Invalid CSV headers. Invalid Column: " . implode(", ", $extraHeaders)); 
                    $moveResponse   = $this->moveFile($destinationDir,$file,$sourcePath,$sftp);
                    if($moveResponse!='FAIL'){
                        $move[$file] = $moveResponse;
                    }
                    continue;
                }

                $misalignedHeaders = array_diff_assoc($cleanedHeaders, $expectedHeaders); // Columns out of position

                // Check if there are extra headers
                if (!empty($misalignedHeaders)) {
                    $error[$file][] = $this->errorMessage("Invalid CSV headers. Misaligned Column: " . implode(", ", $misalignedHeaders)); 
                    $moveResponse   = $this->moveFile($destinationDir,$file,$sourcePath,$sftp);
                    if($moveResponse!='FAIL'){
                        $move[$file] = $moveResponse;
                    }
                    continue;
                }

                //print_r($header);die;

                $facility = Facility::where('garage_code', trim($facility_name))->where('owner_id', $partner_id)->orderBy('id', 'DESC')->first();
                if ($facility) {
                    $facilityid = $facility->id;
                } else {
                    $error[$file][]= $this->errorMessage("Permit Not Created as Facility Id Not Found: ".$facility_name); 
                    continue;
                }

                if(empty($permit_type_name)){
                    $permit_type_name = 'Employee';
                    $rate = PermitRate::where('active', 1)->where('facility_id', $facilityid)->where('name', trim($permit_type_name))->first();
                }else{
                    $rate = PermitRate::where('active', 1)->where('facility_id', $facilityid)->where('name', trim($permit_type_name))->first();
                }
                if (!$rate) {
                    if($permit_type_name==''){
                        $error[$file][]= $this->errorMessage("Permit Type Not Found: ".$permit_type_name); 
                        continue;
                    }                      
                    $permit_rate_id         = NULL;
                    if(isset($price) && $price!=''){
                        $permit_rate    = $price;    
                    }else{
                        $permit_rate    = 0;
                    }                        
                    $type_id        = '';
                }else{
                    $permit_rate_id         = $rate->id;
                    if(isset($price) && $price!=''){
                        $permit_rate    = $price;    
                    }else{
                        $permit_rate    =  $rate->rate;
                    } 
                    $permit_type_name   = $rate->name;
                    $ratedescrption     = PermitRateDescription::where('id', $rate->permit_rate_description_id)->first();
                    $type_id            = isset($ratedescrption->type_id) ? $ratedescrption->type_id : '';
                }
                $permit_final_amount    = $permit_rate; 

                $this->infoMessage("info2 "); 

                // Process each row
                foreach (array_slice($rows, 1) as $row) {
                    $price=0;
                    $record = str_getcsv($row);
                    if (!$record || count($record) < 14) {
                        $error[$file][]= $this->errorMessage("Empty Data."); 
                        $moveResponse = $this->moveFile($destinationDir,$file,$sourcePath,$sftp);
                        if($moveResponse!='FAIL'){
                            $move[$file] = $moveResponse;
                        }
                        continue;
                    }
                    for($i=0;$i<count($record);$i++){
                        $record[$i] = preg_replace('/\s/u', '', $record[$i]);
                    }

					//$facility_name      = !empty(trim($record[0])) ? trim($record[0]) : ''; //garagecode
                    //$permit_type_name   = !empty(trim($record[1])) ? trim($record[1]) : '';
                    //$price              = !empty(trim($record[2])) ? trim($record[2]) : '';
                    //$externalPermitId   = !empty(trim($record[3])) ? trim($record[3]) : null;
                    $desired_start_date = !empty(trim($record[0])) ? trim($record[0]) : '';
                    $desired_end_date   = !empty(trim($record[1])) ? trim($record[1]) : '';
					$license_plate      = !empty(trim($record[2])) ? trim($record[2]) : '';
					$make               = !empty(trim($record[3])) ? trim($record[3]) : null;
					$model              = !empty(trim($record[4])) ? trim($record[4]) : null;
					$color              = !empty(trim($record[5])) ? trim($record[5]) : null;
					$first_name         = !empty(trim($record[6])) ? trim($record[6]) : '';
					$last_name          = !empty(trim($record[7])) ? trim($record[7]) : '';
					$email              = !empty(trim($record[8])) ? trim($record[8]) : '';
					//$phone              = !empty(trim($record[13])) ? trim($record[13]) : '';
					$address1           = !empty(trim($record[9])) ? trim($record[9]) : '';
					$address2           = !empty(trim($record[10])) ? trim($record[10]) : '';
					$city               = !empty(trim($record[11])) ? trim($record[11]) : '';
					$state              = !empty(trim($record[12])) ? trim($record[12]) : null;
					$zipcode            = !empty(trim($record[13])) ? trim($record[13]) : null;
                    $permit_status      = !empty(trim($record[14])) ? trim($record[14]) : '';
                    $accountNumber      = rand(100, 999) . rand(100, 999) . rand(10, 99);
                   
                    //$cancelled_at       = !empty(trim($record[18])) ? trim($record[18]) : '';

                    if(isset($desired_start_date) && !empty($desired_start_date)){
                        $validatorDate = QueryBuilder::validateAndFormatDate($desired_start_date);
                        if(!$validatorDate){
                            $error[$file][]= $this->errorMessage("Invalid start date format- ".$desired_start_date." Correct Format: ".date('Y-m-d'));
                            continue;
                        }else{
                            $desired_start_date= $validatorDate;
                        }
                    }else{
                        $desired_start_date= date('Y-m-d');
                    }

                    if(isset($desired_end_date) && !empty($desired_end_date)){
                        $validatorDate = QueryBuilder::validateAndFormatDate($desired_end_date);
                        if(!$validatorDate){
                            $error[$file][]= $this->errorMessage("Invalid end date format- ".$desired_end_date." Correct Format: ".date('Y-m-d'));
                            continue;
                        }else{
                            $desired_end_date= $validatorDate;
                        }
                    }else{
                        $desired_end_date= date('Y-m-t');
                    }

                    $startDate  = Carbon::parse($desired_start_date);
                    $endDate    = Carbon::parse($desired_end_date);

                    if ($startDate->gt($endDate)) {
                        $error[$file][]= $this->errorMessage("End date (".$desired_end_date.") is less than Start Date- (".$desired_start_date.") for License Plate: ".$license_plate." and email: ".$email);
                        continue;
                    }

                    $this->infoMessage("email ".$email);                    

                    if (!$email) {
                        $error[$file][]= $this->errorMessage("Email- '$email' not found for License Plate: ".$license_plate); 
                        continue;
                    }
                    
                   /*  $permit_processing_fee = isset($facility->permit_processing_fee) ? $facility->permit_processing_fee : '0.00';
					if ($permit_final_amount == '0.00') {
						$permit_processing_fee = "0.00";
					}
                     */
                    #license plate valid , blacklist or not
                    
                    $vehicleFilter = PermitRequest::licensePlateFilter($license_plate,$make,$model);
                    if($vehicleFilter){
                        $error[$file][]= $this->errorMessage($vehicleFilter); 
                        continue;
                    }

                    $vehicle = [
                        'license_plate' => $license_plate,
                        'make' => $make,
                        'model' => $model,
                        'color' => $color
                    ];

                    $postdata = [
                        'first_name' => $first_name,
                        'last_name' => $last_name,
                        'email' => $email,
                        'facility_id' => $facilityid,
                        'vehicleList' => [$vehicle], // Start with the current vehicle
                        'address1' => $address1,
                        'address2' => $address2,
                        'city' => $city,
                        'state' => $state,
                        'zipcode' => $zipcode,
                        'permit_rate_id' => $permit_rate_id,
                        "permit_rate" => isset($permit_rate) ? $permit_rate : 0,
                        "permit_type_name" => isset($permit_type_name) ? $permit_type_name : '',
                        "type_id" => $type_id,
                        'desired_start_date' => $desired_start_date,
                        'desired_end_date' => $desired_end_date,
                        'permit_final_amount' => $permit_final_amount,
                        'partner_id' => $partner_id,
                        'name' => $first_name." ".$last_name,
                        "third_party_type" => 2,
                        "account_number" => $accountNumber
                    ];

                    $postdata['vehicleList'] = json_encode($postdata['vehicleList']);
                    //print_r($postdata);
                   
                    try{

                        /*** 1 step user create or update section */
                        $existUser = User::where('email', $email)->where('created_by', $partner_id)->first();
                        if(!isset($existUser->id)){
                            $existUser='';
                            $postdata['confirm_password'] = $first_name.date('md');                            
                        }
                        $request = (object) $postdata;
                        $this->infoMessage("info6 ".$email); 
                        
                        $user = User::fetchOrCreateUserThirdParty($request, $partner_id, $name = '', false, $existUser);
                        $this->log->info("User Details:" . json_encode($user));
                        if (isset($user->id)) {
                            $userId = $user->id;
                        } else {
                            $error[$file][]= $this->errorMessage('User not exist. Please check again for email: '.$email); 
                            continue;
                        } 

                        #max vehicle count
                    
                        $licenseMaxCheck = PermitRequest::licenseMaxCheck($facility,$user,$request,$partner_id);
                        if($licenseMaxCheck){
                            $error[$file][]= $this->errorMessage($licenseMaxCheck); 
                            continue;
                        }
                    
                        if(strtolower($permit_status)=='cancel'){
                            $request = (object) $postdata;
                            $permitRes = PermitRequest::savePermitRequestThirdParty($facility, $user, $request, $partner_id, 1);
                            if(!$permitRes){
                                $error[$file][]= $this->errorMessage("Permit not exist: ".$license_plate); 
                                continue;
                            }else{
                                $success[$file][]= $postdata['vehicleList'];  
                                continue;
                            }
                        }elseif(strtolower($permit_status)=='update'){
                            $status=2;
                        }elseif(strtolower($permit_status)=='new'){
                            $status=0;
                        }else{
                            $error[$file][]= $this->errorMessage('Provide PermitStatus for email: '.$email.' and LP: '.$license_plate); 
                            continue;
                        }
    
                        /* $isExpire = PermitVehicle::with('permitVehicleMapping')->where('license_plate_number', $license_plate)->first();
                        if (isset($isExpire->permitVehicleMapping->permit_request_id) && !empty($isExpire->permitVehicleMapping->permit_request_id)) {
                            $mapPermit = PermitRequest::where('id',$isExpire->permitVehicleMapping->permit_request_id)->where('permit_rate_id',$permit_rate_id)->first();
                            if (isset($mapPermit) && strtotime($mapPermit->desired_end_date) >= strtotime(date("Y-m-d"))) {
                                $error[$file][]= $this->errorMessage("Permit Not Created as License Plate already exist: ".$license_plate); 
                                continue;
                            }
                        } */

                        /*** 2 step Permit Request create or update section */
                            
                        /* 
                        $request = new \Illuminate\Http\Request();
                        $request->replace($postdata);

                        $controller = app(\App\Http\Controllers\ParkEngage\AreRegisterController::class);
                        $response = $controller->createPermitByAdmin($request); */
                        $permitRes = PermitRequest::savePermitRequestThirdParty($facility, $user, $request, $partner_id, $status);
                        if(!isset($permitRes->id)){
                            $error[$file][]= $this->errorMessage($permitRes." and LP: ".$license_plate); 
                            continue;
                        }
                        if(isset($permitRes->renew)){
                            $success[$file][]= "Email: ".$email." & License Plate: ".$license_plate." is renewed succesfully!" ;  
                        }
                       

                        /*** 3 step Permit Vehicle create or update section */

                        $vehicle_id = '';
                        if (isset($request->vehicleList) && !empty($request->vehicleList)) {
                            $saveVehicle = PermitRequest::saveOrUpdatePermitVehicle($permitRes, $user, $request, $partner_id);
                            $this->log->info("saveVehicle:" . $saveVehicle);
                            if(!$saveVehicle){
                                $permitIds[] = $permitRes->id;
                                $error[$file][]= $this->errorMessage('Error in permit vehicle create: '.$postdata['vehicleList']); 
                                //$permitRes->delete();
                            }else{
                                $vehicleResponse = json_decode($saveVehicle);
                                if(count($vehicleResponse->error) > 0 && !isset($permitRes->renew)){
                                    $permitIds[] = $permitRes->id;
                                    $error[$file][]= $this->errorMessage('Error in permit vehicle mappping: '.implode(',',$vehicleResponse->error)." and Email: ".$email); 
                                }
                            }       
                            if(isset($vehicleResponse->success) && count($vehicleResponse->success) > 0){
                                $success[$file][]= "Email: ".$email." VehicleList: ".implode(',',$vehicleResponse->success);  
                            }       
                        }else{
                            $permitIds[] = $permitRes->id;
                            $error[$file][]= $this->errorMessage('Permit not created for email: '.$email.' and vehiclelist: '. $postdata['vehicleList']); 
                        }
                    } catch (NotFoundHttpException $e) {
                        if(isset($permitRes->id)){
                            $permitIds[] = $permitRes->id;
                        }
                        $error[$file][]= $this->errorMessage('NotFoundHttpException: ' . $e->getMessage()); 
                        continue;
                    } catch (\Throwable $e) {
                        if(isset($permitRes->id)){
                            $permitIds[] = $permitRes->id;
                        }
                        $error[$file][]= $this->errorMessage($e->getMessage()); 
                        continue;
                    }
                }
                $response = '';

                
                $moveResponse = $this->moveFile($destinationDir,$file,$sourcePath,$sftp);
                if($moveResponse!='FAIL'){
                    $move[$file] = $moveResponse;
                }
            }

            echo "Data:\n";
            if(empty($error) && empty($success)){
                echo "No csv file found";
            }
            else{
                if(isset($permitIds)){
                    foreach($permitIds as $permitId){
                        $permitvehicle = PermitVehicleMapping::where('permit_request_id',$permitId)->first();
                        if(!$permitvehicle){
                            PermitRequest::where('id',$permitId)->delete();
                        }
                    }
                }
                try{                    
                    $this->sendMail($error,$partner,$success,$move);    
                }catch (\Exception $e) {
                    $error['auth'][] = $this->errorMessage('An error occurred on mail: ' . $e->getMessage()); 
                }
                echo "\nError:\n";            
                print_r($error);
                echo "\nSuccess:\n";
                print_r($success);
            }
            die;

        }catch (\Exception $e) {
            $error['auth'][] = $this->errorMessage('An error occurred: ' . $e->getMessage()); 
            $this->sendMail($error,$partner_id,$success,$move);
            die;
        }

       
    }

    public function moveFile($destinationDir,$file,$sourcePath,$sftp){
        $destinationPath = $destinationDir . date('YmdHis')."-".$file;

        if (!File::exists($destinationDir)) {
            File::makeDirectory($destinationDir, 0755, true);
        }

        if (File::move($sourcePath, $destinationPath)) {
            echo "Moved: $file to $destinationPath\n";
            return $destinationPath;
        } else {
            $this->info("File failed to move to processed folder.");
            echo "Failed to move: $file\n";
            return 'FAIL';
        }

        // Move file to the destination folder
        /* if ($sftp->rename($sourcePath, $destinationPath)) {
            echo "Moved: $file to $destinationPath\n";
            return $destinationPath;
        } else {
            $this->info("File failed to move to processed folder.");
            echo "Failed to move: $file\n";
            return 'FAIL';
        } */
    }

    public function errorMessage($msg){
        $this->log->error($msg);
        return $msg; 
    }

    public function infoMessage($msg){
        $this->log->info($msg);
        return $msg; 
    }

    protected function sendMail($error,$partner,$success,$move)
    {
        
        $recipients = config("parkengage.notify.permit_bulk_upload");
        try {
            Mail::send(
                'email.import-permit-bulk-notify',
                ['errors' => $error, 'success' => $success, 'move' => $move],
                function ($message) use ($recipients, $partner) {
                    $message->to($recipients)
                        ->subject("Import Permit Bulk Upload Alert, Partner $partner");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            echo "Mail Sent to ".implode(", ", $recipients);
            $this->log->info("Import Bulk Import Notifications sent to recipients: " . implode(", ", $recipients));
        } catch (Exception $e) {
            echo "Mail sent failed to ".implode(", ", $recipients);
            $this->log->error("Import Bulk Import Failed to send notification emails: {$e->getMessage()}");
        }
    }
}