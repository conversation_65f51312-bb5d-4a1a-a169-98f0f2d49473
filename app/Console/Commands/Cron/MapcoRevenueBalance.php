<?php

namespace App\Console\Commands\Cron;

use Response;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\RevenueBalance;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\ApiGenericException;
use App\Models\PromoUsage;

class MapcoRevenueBalance extends Command
{

  protected $signature = 'mapco-revenue-balance';

  protected $description = 'Add last month revenue balance of each partner at the starting of the month.';


  protected $log;

  public function __construct(LoggerFactory $logFactory)
  {
    parent::__construct();
    $this->log = $logFactory->setPath('logs/parkengage/mapco/partner-revenue')->createLogger('partner-revenue');
  }


  public function handle()
  {
    $facility_id = 32;
    $month = date('Y-m', strtotime("-1 month"));
    $from_date  = date($month . '-01');
    $mid_date  = date($month . '-15');
    $to_date  = date($month . '-t');
    $midDateString = strtotime($mid_date);
    $lastdate = strtotime(date("Y-m-t", $midDateString));
    $to_date = date("Y-m-d", $lastdate);
    $users = User::where('user_type', '3')->where('id', '1553')->get();
    $this->log->info("Cron is about to start");
    if ($users) {
      foreach ($users as $user) {
        $totalBalance = 0;
        $totalCurrentMonthBalance = 0;
        $totalRemainder = 0;
        $userConfig = UserPaymentGatewayDetail::where('user_id', $user->id)->first();
        if (!$userConfig) {
          $this->log->error("Partner does not have configuration : " . $user->id);
          continue;
        }

        $monthlyAmount  = $userConfig->monthly_amount;
        $transactionShare = 0.09;
        $promocodeShare = 0.75;
        $passShare = $userConfig->pass_share ? $userConfig->pass_share : 0;
        $userPasses = Reservation::with("rate")->where('partner_id', $user->id)->where('facility_id', $facility_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull("cancelled_at")->whereNotNull("anet_transaction_id")->get();
        $this->log->info("Get all the passes : " . $user->id);
        $total = 0;
        $passCount = 0;
        $bookingCount = 0;
        $totalShare = 0;
        $passShareAmount = 0;
        $bookingShareAmount = 0;
        $promocodeShareAmount = 0;
        $promocodeCount = 0;
        $singleEventCount = 0;
        $singleEventShareAmount = 0;
        $transactionCount = count($userPasses);
        $transactionAmount = $transactionCount * $transactionShare;
        $data = [];
        try {
          if (count($userPasses) > 0) {
            $this->log->info("Get all the passes of : " . $user->id);
            foreach ($userPasses as $userPass) {
              if (isset($userPass->rate->id)) {
                //echo $userPass->rate->total_usage.'--';
                $usagePass = (float) ($passShare * $userPass->rate->total_usage);
                //echo $usagePass;
                $passShareAmount = $passShareAmount + $usagePass;
                $passCount++;
              } else {
                $singleEventShareAmount += $passShare;
                $singleEventCount++;
              }
              //echo $passShareAmount."<br>";
              //echo $passShareAmount;

              $promoUsage = PromoUsage::where("user_id", $user->id)->where("reservation_id", $userPass->id)->first();
              if ($promoUsage) {
                $promocodeShareAmount += $promocodeShare;
                $promocodeCount++;
              }
              $totalCurrentMonthBalance = $passShareAmount + $promocodeShareAmount + $singleEventShareAmount;

              $total = $total + $userPass->total;

              //$passShareAmount += $passShareAmount;
              $totalShare += $passShareAmount;
            }
          } //userpass close
          $data['total'] = $total;
          $data['pass_share'] = $passShare;
          $data['pass_share_amount'] = $passShareAmount;
          $data['promocode_count'] = $promocodeCount;
          $data['promocode_share_amount'] = $promocodeShareAmount;
          $data['pass_count'] = $passCount;
          $data['total_share'] = $passShareAmount + $promocodeShareAmount + $singleEventShareAmount + $monthlyAmount + $transactionAmount;
          $data['booking_share'] = $passShare;
          $data['booking_count'] = $singleEventCount;
          $data['booking_share_amount'] = $singleEventShareAmount;
          $data['promocode_share'] = $promocodeShare;

          //dd($$passShareAmount, $promocodeCount, $singleEventCount, $passCount);
        } catch (Exception $e) {
          $this->log->info("exception : " . json_encode($e->getMessage(), $e->getLine()));
        }

        if (count($data) > 0) {

          $configTotal = $userConfig->total_balance;
          //$data['total'] = $total;
          //$data['pass_count'] = $passCount;
          //$data['total_share'] = $totalShare;
          $data['partner_id'] = $user->id;
          $data['from_date'] = $from_date;
          $data['to_date'] = $to_date;
          $data['remainder'] = $configTotal;
          $data['final_amount'] = $passShareAmount + $promocodeShareAmount + $singleEventShareAmount + $configTotal + $monthlyAmount + $transactionAmount;
          $data['pass_share_amount'] = $passShareAmount;
          $data['promocode_count'] = $promocodeCount;
          $data['promocode_share_amount'] = $promocodeShareAmount;
          $data['monthly_amount'] = $monthlyAmount;
          $data['transaction_share'] = $transactionShare;
          $data['transaction_count'] = $transactionCount;
          $data['transaction_amount'] = $transactionAmount;

          RevenueBalance::create($data);

          $userConfig->last_cron_run = date("Y-m-d");
          $userConfig->total_balance = $userConfig->total_balance + $totalCurrentMonthBalance + $monthlyAmount + $transactionAmount;
          $userConfig->current_month_balance = $totalCurrentMonthBalance + $monthlyAmount + $transactionAmount;
          $userConfig->remainder = $userConfig->remainder + $totalRemainder;
          $userConfig->save();
          $this->log->info("Save all the balance of user : " . $user->id);
        }
      } //foreach close
    } //if close

  }
}
