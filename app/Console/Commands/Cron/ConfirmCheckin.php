<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;


class Confirm<PERSON><PERSON>ckin extends Command
{

    protected $signature = 'email:confirm-checkin {id} {type}';

    protected $description = 'Send checkin confirmation email.';

    public function handle()
    {
       $id = $this->argument('id');
       $type = $this->argument('type');
       if($type == 'checkin'){
            $data = Ticket::with('facility')->where('id', $id)->first();
            //user mail
            Mail::send(
                "checkin-checkout.email-checkin", ['data' => $data], function ($message) use($data) {
                    $message->to($data->user->email)->subject("ParkEngage Prepaid Check-In Confirmation Details Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
       }else{
            $data = OverstayTicket::with('facility')->where('id', $id)->first();
            $ticket = Ticket::where('ticket_number', $data->ticket_number)->first();
            //user mail
            Mail::send(
                "checkin-checkout.prepaid-overstay-email-payment", ['data' => $data,'ticket' => $ticket], function ($message) use($data) {
                    $message->to($data->user->email)->subject("ParkEngage Prepaid Overstay Booking Details Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
       }
       
       
    }
    
}

