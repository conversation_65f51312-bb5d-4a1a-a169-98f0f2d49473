<?php

namespace App\Console\Commands\Cron;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use Illuminate\Support\Collection;
use Storage;
use DB;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\ParkEngage\BrandSetting;


/**
 * Emails reservation stub to user
 */
class AttendantAccountEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendant-account-create:email {id}/{password}';

 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Invitation Email to Attendant Membership Email.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/attendant')->createLogger('attendant');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
		$user_id = $this->argument('id');   
		
        $userDetails = User::whereNull('deleted_at')->where('id',$user_id)->first();
        if (!$userDetails) {
            throw new NotFoundException('No user with that ID.');
        }
        $brand_setting = '';
        if(($userDetails->created_by== config('parkengage.PARTNER_PAVE')) || ($userDetails->created_by== config('parkengage.PARTNER_UNITED')) || ($userDetails->created_by== config('parkengage.PARTNER_PCI'))){
            $brand_setting = BrandSetting::where('user_id', $userDetails->created_by)->first();
        }
		//$userData = [];
		//$userData['userDetails'] = $userDetails;
		$userDetails->loginurl = env('WEB_ADMIN_URL');          
        $userDetails->password = $this->argument('password');
        $userDetails->brand_setting = $brand_setting;
        $view='attendant.email';
        $view_text=$view.'-plain-text';
        if(!View::exists($view_text))
        {
            $view_text=$view;
        } 
         try{ 
            $this->log->info("Attendant user email sending: $userDetails->email");
             Mail::send(
                ['text'=>$view_text,'html'=>$view],
                ['data' => $userDetails],
                function ($message) use ($userDetails) {
                    $message->to($userDetails->email)->subject("Welcome to ParkEngage");
                    $message->from(config('parkengage.default_sender_email'));
                }
             );             
            $this->log->info("email sent: $userDetails->email");
        }catch (\Exception $e)
        {
            $errorMessage=array();
            $errorMessage['message']=$e;
            $this->log->error('Issue in email sending:',$errorMessage);
        }
    }
}


