<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use App\Services\LoggerFactory;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\LicensePlate;
use Carbon\Carbon;

class ReadLicensePlate extends Command
{
    use DispatchesJobs;

    protected $signature = 'read-license-plate {license_plate} {is_checkin_or_checkout} {ticket} {response_type} {msg} {queue_name} {gate_type}';

    protected $description = 'Send request demo email.';

    const QUEUE_NAME = 'omniq';   
	const QUEUE_ENTRY = 'read-license-plate';	
    const QUEUE_EXIT_NAME = 'read-license-plate-exit';	

    public function __construct()
    {  
        parent::__construct();
        $logFactory = new LoggerFactory();     
        $this->log = $logFactory->setPath('logs/read-license-plate')->createLogger('read-license-plate');
        
    }

    public function handle()
    {
        try{
        $license_plate = $this->argument('license_plate');
		$is_checkin_or_checkout = $this->argument('is_checkin_or_checkout');
        $ticket = $this->argument('ticket');
        $response_type = $this->argument('response_type');
        $msg = $this->argument('msg');
        $queue_name = $this->argument('queue_name');
        $gate_type = $this->argument('gate_type');

        if($is_checkin_or_checkout=='1'){
            if($queue_name == ''){
                $queue_name = self::QUEUE_ENTRY;
            }
			$this->log->info($queue_name. "Queue Check-in start  $license_plate");
			//$this->dispatch((new LicensePlate($license_plate))->delay(Carbon::now()->addSeconds(10))->onQueue($queue_name));
			//$this->log->info($queue_name. "Queue Check-in end  $license_plate");   	
            
		}else if($is_checkin_or_checkout=='0'){
            if($queue_name == ''){
                $queue_name = self::QUEUE_EXIT_NAME;
            }
			$this->log->info($queue_name. "Queue Check-Out start  $license_plate");
			//$this->dispatch((new LicensePlate($license_plate))->delay(Carbon::now()->addSeconds(10))->onQueue($queue_name));
			//$this->log->info($queue_name. "Queue Check-Out end  $license_plate");   
		}
        $msgResponse = [];
        if($msg != ''){
            $msgResponse = $msg;
            //['msg' => $msg]
        }
        $msgRespone = (object) $msgResponse;
        $this->log->info("Json response  ".json_encode(['eticket_id'=>$ticket['ticket_number'],'license_plate'=>$ticket['license_plate'], 'data'=>$msgRespone, 'response_type' => $response_type]));
        $myArray = json_encode(['eticket_id'=>$ticket['ticket_number'],'license_plate'=>$ticket['license_plate'], 'data'=>$msgRespone, 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
		if($is_checkin_or_checkout=='1'){
            if($queue_name == ''){
                $queue_name = self::QUEUE_ENTRY;
            }
			$queue->pushRaw($myArray, $queue_name);
		}else if($is_checkin_or_checkout=='0'){
            if($queue_name == ''){
                $queue_name = self::QUEUE_EXIT_NAME;
            }
			$queue->pushRaw($myArray, $queue_name);
        }
		return $queue;die;
        echo $license_plate;
    }catch(\Exception $e){
        $this->log->info("Error : ". json_encode($e));   
    }

    }
    
}
