<?php

namespace App\Console\Commands\Cron;

use Storage;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\MapcoQrcodeCheckin;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\Ticket;
use App\Models\ParkEngage\Gate;
use App\Models\AuthorizeNetTransaction;


class SendEmailUserpassDetails extends Command
{

    protected $signature = 'send:userpass-details-email {id}';

    protected $description = 'Send email to user for Booking Details';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/mapco-reservation')->createLogger('mapco-reservation');
    }

    public function handle()
    {
        $reservation_id = $this->argument('id');  
        $this->log->info("Mail start");
        $data = Reservation::with(['user', 'userPass','userPass.transaction','rate','facility'])->where('id',$reservation_id)->first();
        //$data['email']= '<EMAIL>';
        if($data->userPass)
        {
            $data['bookpass']= 'Yes';
        }
        else{
            $data['bookpass']= 'NA';
        }
        

        //$email='<EMAIL>';

            try{
                    Mail::send(
                       "userpass.sendpassdetailsuser-email", ['data' => $data], function ($message) use($data) {
                        $message->to($data->user->email)->subject("Booking Details - ". $data->ticketech_code);
                        //$message->to($data['email'])->subject("Pass Details - ". $data->ticketech_code);
                        $message->from(config('parkengage.default_sender_email'));
                       //$message->attachData($pdf,$pdfName.".pdf");
                    }
                );

                $this->log->info("Mail sent to ".$data->user->email);
            } catch(Exception $e) {
                $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                $this->log->error($msg);
                $this->log->info("Queue ended");            
            }
       
    }

    
}