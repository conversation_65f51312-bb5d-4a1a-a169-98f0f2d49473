<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use <PERSON><PERSON>lio\Rest\Client;
use <PERSON><PERSON><PERSON>\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Services\LoggerFactory;


class MapcoPrepaidCheckin extends Command
{

    protected $signature = 'email:touchless-parking-mapco-prepaid-confirm-checkin {id} {type}';

    protected $description = 'Send checkin confirmation email.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/parkengage/mapco')->createLogger('mapco-email');
    }


    public function handle()
    {
       $id = $this->argument('id');
       $type = $this->argument('type');
       if($type == 'checkin'){
            $this->log->info("Email sending about to start");
            $data = Ticket::with(['facility','user'])->where('id', $id)->first();
            //user mail
            if($data->user->email != ''){
                try{
                    Mail::send(
                        "mapco.prepaid-email-checkin", ['data' => $data], function ($message) use($data) {
                            $message->to($data->user->email)->subject("Mapco Check-In Confirmation Details Ticket : ". $data->ticket_number);
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                }catch (\Exception $e) {
                    $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                    $this->log->error($msg);
                    Mail::send(
                        "mapco.alert-error", ['data' => $msg], function ($message) use($msg) {
                            $message->to(['<EMAIL>','<EMAIL>','<EMAIL>'])->subject("Error : Mapco Checkin Alert ");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Mapco error email sent.");
                }
                $this->log->info("Email sent to ". $data->user->email .'user id : '. $data->user->id);

            }
            if($data->user->phone != ''){
                $ticket_number = base64_encode($data->ticket_number);
                $facilityName =  ucwords($data->facility->full_name);
                $accountSid = env('TWILIO_ACCOUNT_SID');
                $authToken  = env('TWILIO_AUTH_TOKEN');
                $url = env('TOUCHLESS_APP_URL');
                $client = new Client($accountSid, $authToken);
                /*$encrypt = Crypt::encrypt(json_encode(['gate'=>'1', 'facility_id'=> $result->facility_id]));*/
                try
                {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                    // the number you'd like to send the message to
                        $data->user->phone,
                array(
                        // A Twilio phone number you purchased at twilio.com/console
                        'from' => env('TWILIO_PHONE'),
                        // the body of the text message you'd like to send
                        //'body' => "Fine"
                        /*'body' => "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number. Use the following link to Check-Out. $url/touchless-parking-atlanta-prepaid-checkout-details/$ticket_number"*/
                        'body' => "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number."
                    )
                );

                $this->log->info("SMS sent to ". $data->user->phone .'user id : '. $data->user->id);
                }catch (RestException $e)
                {
                    $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                    $this->log->error($msg);
                    Mail::send(
                        "mapco.alert-error", ['data' => $msg], function ($message) use($msg) {
                            $message->to(['<EMAIL>','<EMAIL>','<EMAIL>'])->subject("Error : Mapco Checkin Alert ");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Mapco error email sent.");
                }
            }
       }else{
            $data = OverstayTicket::with('facility')->where('id', $id)->first();
            $ticket = Ticket::where('ticket_number', $data->ticket_number)->first();
            //user mail
            try{
            Mail::send(
                "atlanta-checkin-checkout.touchless-parking-prepaid-overstay-email-payment", ['data' => $data,'ticket' => $ticket], function ($message) use($data) {
                    $message->to($data->user->email)->subject("ParkEngage Touchless Overstay Booking Details Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            }catch (\Exception $e) {
                $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                $this->log->error($msg);
                Mail::send(
                    "mapco.alert-error", ['data' => $msg], function ($message) use($msg) {
                        $message->to(['<EMAIL>','<EMAIL>','<EMAIL>'])->subject("Error : Mapco Checkin Alert ");
                        $message->from(config('parkengage.default_sender_email'));
                    }
                );
                $this->log->info("Mapco error email sent.");
            }
       }
       
       
    }
    
}

