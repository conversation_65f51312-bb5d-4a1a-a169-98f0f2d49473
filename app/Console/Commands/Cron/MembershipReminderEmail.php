<?php

namespace App\Console\Commands\Cron;

use Mail;
use Auth;
use File;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\ParkEngage\UserMembership;
use App\Models\ParkEngage\Service;
use App\Services\LoggerFactory;


class MembershipReminderEmail extends Command
{

    protected $signature = 'email:membership-reminder';

    protected $description = 'Send User Membership Erminder Email for active users.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
    
        $this->log = $logFactory->setPath('logs/parkengage')->createLogger('membership_reminder');
    }

    public function handle()
    {
        try{
        $nextDate = date('Y-m-d',strtotime('+3 day'));
        $userMembership = UserMembership::with('users','membershipPlan')->whereDate('end_date' , '=', $nextDate)->where('is_active', '1')->get();
        $userIds = '';
        if(count($userMembership) >  0){
            foreach ($userMembership as $key => $value) {
                    if((count($value->users)) > 0 && (count($value->membershipPlan) > 0)){
                        $userIds .= $value->users->id .',';
                        $service = Service::find($value->membershipPlan->service_id);
                        Mail::send(
                            "parkengage.membership-reminder-email", ['data' => $value, 'service' => $service], function ($message) use($value) {
                                $message->to($value->users->email)->subject("Membership Plan Expiration Reminder");
                                $message->from(config('parkengage.default_sender_email'));
                            }
                        );
               }
            }
        }
            $this->log->info("Membership reminder email send to user ids -- $userIds ");
            echo ($userIds);
        }catch(\Exception $e){
            $this->log->error($e->getMessage());
        }
    }
    
}
