<?php

namespace App\Console\Commands\Cron;

use App\Http\Helpers\MailHelper;
use Mail;
use Exception;
use File;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\User;
use Auth;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\BrandSetting;
use Hash;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\FacilityBrandSetting;

class GatedUngatedUserRegistrationEmail extends Command
{
    protected $signature = 'email:gated-ungated-user-registration {id}/{password}/{base_url?}/{facility_id?}';
    protected $log;

    protected $description = 'Send User Gated and Ungated Email.';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/useraccount')->createLogger('useraccount');
    }

    public function handle()
    {
        $id = $this->argument('id');
        $facility_id = $this->argument('facility_id');
        $user = User::find($id);
        if (!$user) {
            $this->log->info("No user with that ID.");
            //  throw new NotFoundException('No user with that ID.');
        }
        $partner_name = isset($user->partner_name->company_name) ? $user->partner_name->company_name : 'ParkEngage';
        $brand_setting = '';
        if ($user) {
            //$brand_setting = BrandSetting::where('user_id', $user->created_by)->first();

            $facility_brand_setting = FacilityBrandSetting::where('facility_id', $facility_id)->first();
            if ($facility_brand_setting) {
                $user->facility_logo_id = $facility_brand_setting->id;
            } else {
                $brand_setting = BrandSetting::where('user_id', $user->created_by)->first();
                $user->logo_id  = $brand_setting->id;
            }
        }
        $userPaymentDetails = UserPaymentGatewayDetail::where('user_id', $user->created_by)->first();
        $user->loginurl = '';
        $base_url = $this->argument('base_url');
        if ($base_url) {
            $user->loginurl = $base_url;
        } else if ($userPaymentDetails->user_id == config('parkengage.PARTNER_DIAMOND')) {
            $user->loginurl = env('WEB_URL_WAILUKU');
        } else if ($userPaymentDetails->user_id != config('parkengage.PARTNER_DIAMOND')) {
            $user->loginurl = env('WEB_URL_CUSTOMERPORTAL') . "/" . $userPaymentDetails->touchless_payment_url;
        }
        $user->password = $this->argument('password');

        try {
            $this->log->info("Send User registration email on : $user->email");
            // Change Request PIMS-12502 : Vijay - 30-01-2025 
            $partnerId = $user->created_by;
            $subject = "Welcome to {$partner_name}";
            MailHelper::sendEmail($user->email, 'parkengage.gated_ungated_user_registration_email', ['subject' => $subject, 'user' => $user, 'brand_setting' => $brand_setting], $partnerId);
            // Change Close : PIMS-12502

            // Mail::send(
            //     "parkengage.gated_ungated_user_registration_email",
            //     ['user' => $user, 'brand_setting' => $brand_setting],
            //     function ($message) use ($user, $partner_name) {
            //         $message->to($user->email)->subject("Welcome to " . $partner_name);
            //         $message->from(config('parkengage.default_sender_email'));
            //     }
            // );
            $this->log->info("email sent: $user->email");
        } catch (\Exception $e) {
            $errorMessage = array();
            $errorMessage['message'] = $e;
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error("Issue in email sending : {$msg}");
        }
    }
}
