<?php

namespace App\Console\Commands\Cron;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\Facility;
use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;
use App\Models\Reservation;
use Exception;

class NotifyCronPaymentStatus extends Command
{
    protected $signature = 'notify:cron-payment-status';

    protected $description = 'Notify if payment cron is not running for more than 30 minutes.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/notify-cron-payment-status')->createLogger('notify-cron-payment-status');
    }

    public function handle()
    {
        try {
            $this->log->info("Cron started");

            $partnerId = 1553;
            $facilityId = 157;

            // $partnerId = 357982;
            // $facilityId = 398;

            $partner = User::find($partnerId);
            $partnerName = $partner ? $partner->name : "Unknown Partner";

            $facility = Facility::find($facilityId);
            $facilityName = $facility ? $facility->full_name : "Unknown Facility";

            $timezone = QueryBuilder::setCustomTimezone($facilityId);
            $this->log->info("Processing for Partner ID: $partnerId, Facility ID: $facilityId, Timezone: $timezone");

            $current_time = Carbon::now();
            $threshold_time = Carbon::now()->subMinutes(30);

            $this->log->info("Current time: $current_time | Threshold time: $threshold_time");

            // Fetch null anet
            $pendingReservations = $this->getPendingReservations($facilityId, $current_time, $threshold_time);

            if ($pendingReservations->isEmpty()) {
                $this->log->info("No null 'anet_transaction_id' entries found for Partner ID: $partnerId and Facility ID: $facilityId.");
                return;
            }

            $this->log->info("Found {$pendingReservations->count()} null 'anet_transaction_id' entries for Partner ID: $partnerId, Facility ID: $facilityId. Sending notifications...");
            $this->notifyDevTeam($pendingReservations, $partnerName, $facilityName);

            $this->log->info("Cron ended successfully for Partner ID: $partnerId and Facility ID: $facilityId.");
        } catch (Exception $e) {
            $errorMessage = sprintf(
                "Error: %s in file %s on line %d",
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
            );

            $this->log->error($errorMessage);
        }
    }

    public function getPendingReservations($facilityId, $current_time, $threshold_time)
    {
        return Reservation::select(
            'reservations.id',
            'reservations.ticketech_code',
            'reservations.created_at',
            'reservations.deleted_at',
            'reservations.updated_at',
            'reservations.start_timestamp',
            'reservations.end_timestamp',
            'reservations.is_charged',
            'reservations.is_hub_zeag',
            'reservations.total',
            'dt.transaction_retry'
        )
            ->from('reservations')
            ->leftJoin('datacap_transactions as dt', 'reservations.id', '=', 'dt.reservation_id')
            ->where(['reservations.facility_id' => $facilityId, 'reservations.is_charged' => '2'])
            ->whereNull('reservations.anet_transaction_id')
            ->whereNull('reservations.cancelled_at')
            ->where('reservations.end_timestamp', '<=', $current_time)
            ->whereIn('reservations.is_hub_zeag', [1, 2, 3])
            ->orderBy('reservations.id', 'desc')
            ->get();
        /* ->where(function ($query) use ($current_time, $threshold_time) {
                $query->where('reservations.end_timestamp', '<', $current_time);
            }) */
    }

    protected function notifyDevTeam($reservations, $partnerName, $facilityName)
    {
        $data = [
            'count' => $reservations->count(),
            'timestamp' => Carbon::now()->toDateTimeString(),
            'reservations' => $reservations->map(function ($reservation) {
                return [
                    'ticketech_code' => $reservation->ticketech_code,
                    'created_date' => Carbon::parse($reservation->created_at)->format('Y-m-d H:i:s'),
                    'updated_date' => Carbon::parse($reservation->updated_at)->format('Y-m-d H:i:s'),
                    'start_timestamp' => Carbon::parse($reservation->start_timestamp)->format('Y-m-d H:i:s'),
                    'end_timestamp' => Carbon::parse($reservation->end_timestamp)->format('Y-m-d H:i:s'),
                    'is_charged' => $reservation->is_charged,
                    'is_hub_zeag' => $reservation->is_hub_zeag,
                    'transaction_retry' => $reservation->transaction_retry ?? 'N/A',
                    'total' => $reservation->total ?? '0'
                ];
            })->toArray(),
        ];

        $recipients = config("parkengage.notify.reservation");

        try {
            Mail::send(
                'email.notify-cron-payment-status',
                ['data' => $data],
                function ($message) use ($recipients, $partnerName, $facilityName) {
                    $message->to($recipients)
                        ->subject("No Payment attempted for Facility $facilityName");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );

            $this->log->info("Notifications sent to recipients: " . implode(", ", $recipients));
        } catch (Exception $e) {
            $this->log->error("Failed to send notification emails: {$e->getMessage()}");
        }
    }
}
