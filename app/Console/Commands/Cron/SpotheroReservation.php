<?php

namespace App\Console\Commands\Cron;

use Illuminate\Support\Collection;
use Illuminate\Console\Command;

use App\Models\Reservation;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;

use App\Services\LoggerFactory;
use App\Classes\SpotHero;

use Exception;
use Config;
use DB;
use Carbon\Carbon;
use Mail;
use App\Models\ThirdpartyReservation;

class SpotheroReservation extends Command
{


    protected $signature = 'spothero:reservation';

    protected $description = 'Spothero reservation using cron';

    protected $log;

    protected $spotHero;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->spotHero = new SpotHero();
        $this->log = $logFactory->setPath('logs/parkengage/spothero_reservation')->createLogger('spothero-reservation');
    }

    public function setCustomTimezone($facility_id)
    {

        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($partnerTimezone->timezone != '') {
                config(['app.timezone' => $partnerTimezone->timezone]);
                date_default_timezone_set($partnerTimezone->timezone);
            }
        }
    }

    function convertUtcToLosAngelesTime($utcDateTimeStr, $timezone)
    {
        // Create a DateTime object for the UTC time
        $utcDateTime = new \DateTime($utcDateTimeStr, new \DateTimeZone('UTC'));

        // Set the time zone to Los Angeles
        $losAngelesTimeZone = new \DateTimeZone($timezone);
        $utcDateTime->setTimezone($losAngelesTimeZone);

        // Format the Los Angeles time as a string
        $losAngelesDateTimeStr = $utcDateTime->format('Y-m-d H:i:s');

        return $losAngelesDateTimeStr;
    }

    public function handle()
    {
        try {
            $this->log->info("Queue Started");
            //fetch all facility from Facility table with gated facility and spothero facility id
            $facilities = Facility::where('spothero_facility_id', '!=', null)->get();
            //->where('is_gated_facility', 1)
            $count = 0;
            if (isset($facilities) && !empty($facilities)) {
                $emailArray = array();
                foreach ($facilities as $key => $val) {

                    $thirdParty = DB::table('facility_reservation_configurations')->where('facility_id', $val->id)->first();
                    if (!isset($thirdParty) || empty($thirdParty)) {
                        continue;                   //if tirdParty not found skip iteration
                    }

                    $arrayData = $this->spotHero->spotheroReservationCron($thirdParty->url, $thirdParty->secret_key);

                    $this->log->info("Data Fetched From Cron. ");
                    $this->log->info(json_encode($arrayData));
                    if (!isset($arrayData['entry'])) {
                        continue;
                    }
                    $contents = array_column($arrayData['entry'], "content");
                    if (count($contents) > 0) {
                        //skip this if response come more than one case
                    } else {
                        if (isset($arrayData['entry']['content'])) {
                            $contents[] = (array) $arrayData['entry']['content'];
                        } else {
                            continue;
                        }
                    }
                    $this->setCustomTimezone($val->id);
                    $this->log->info("Time Zone Changed " . $val->id);

                    $facility_ids = array_column($contents, 'facilityId');
                    if (isset($contents) && !empty($contents)) {
                        $this->log->info("contents OK ");
                        // $checkRecordExist = ThirdpartyReservation::where('facility_id',$val->id)->orderBy('published_date', 'DESC')->first();
                        // $old_published_date = date('Y-m-d H:i:s',strtotime($checkRecordExist->published_date));
                        foreach ($contents as $k => $content) {
                            if ($val->spothero_facility_id != $content['facilityId']) {  // skip the non-related data data of spothero 
                                $this->log->info("skip the non-related data data of spothero " . $content['facilityId'] . '-' . $val->spothero_facility_id . '-' . $val->spothero_facility_id);
                                continue;
                            }
                            $checkRecordExist = ThirdpartyReservation::where('orderId', $content['orderId'])->first();
                            if (isset($checkRecordExist) && !empty($checkRecordExist)) {
                                $old_published_date = date('Y-m-d H:i:s', strtotime($checkRecordExist->published_date));
                                $new_published_date = date('Y-m-d H:i:s', strtotime($content['published']));
                                $this->log->info("spthero 11  " . $content['orderId'] . "  " . $content['barcode']);
                                if ($new_published_date <= $old_published_date) {
                                    continue;
                                }
                            }
                            $this->log->info("spthero 22  " . $content['facilityId'] . '-' . $val->spothero_facility_id . "  " . $content['orderId'] . "  " . $content['barcode']);
                            $endTime = Carbon::parse($content['exitDate'])->format('Y-m-d H:i:s');
                            $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $endTime);

                            $startTime = Carbon::parse($content['entryDate'])->format('Y-m-d H:i:s');
                            $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $startTime);

                            $startTime = $this->convertUtcToLosAngelesTime($startTime, $val->timezone);
                            $endTime = $this->convertUtcToLosAngelesTime($endTime, $val->timezone);

                            $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $startTime);
                            $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $endTime);

                            $diff_in_hours = $endTime->diffInRealHours($startTime);
                            $diff_in_mins = $endTime->diffInRealMinutes($startTime);
                            $diff_in_secs = $endTime->diffInSeconds($startTime);

                            // calculate minutes if less then 1 hr
                            if ($diff_in_mins < 60) {
                                $diff_in_hours = number_format($diff_in_mins / 100, 2);
                            }
                            // calculate minutes if grater then 1 hr and convert it to hours and minutes
                            if ($diff_in_mins > 59) {
                                $diff_in_hours = floor($diff_in_mins / 60) . '.' . ($diff_in_mins -   floor($diff_in_mins / 60) * 60);
                            }

                            if ($diff_in_secs < 60) {
                                $diff_in_hours = .01;
                            }

                            $data['length'] = $diff_in_hours;
                            $data['facility_id'] = $val->id;
                            $data['start_timestamp'] = date("Y-m-d H:i:s", strtotime($startTime));
                            $data['total'] = '0.00';

                            $license = null;
                            if (isset($content['permitValue']) && !empty($content['permitValue'])) {
                                $data['total'] = $content['permitValue'];
                            }

                            $data['ticketech_code'] = $content['barcode'];
                            $data['loyalty_program'] = 0;
                            if (isset($content['license']) && !empty($content['license'])) {
                                $license = $content['license'];
                            }
                            $data['license_plate'] = $license;
                            $data['partner_id'] = $val->owner_id;
                            $data['thirdparty_integration_id'] = $thirdParty->thirdparty_integration_id;
                            // if canceled record found;
                            if ($content['status'] == 'cancelled') {
                                $data['cancelled_at'] =  date('Y-m-d H:i:s', strtotime($content['published']));
                            }
                            // Check Reservation status is changed then update the existing reservation
                            $checkReservation = Reservation::where('ticketech_code', $content['barcode'])->first();

                            if (isset($checkReservation) && !empty($checkReservation)) {
                                $this->log->info("reservation checked  " . $content['barcode']);
                                if ($content['status'] == 'cancelled' && ($checkReservation->cancelled_at == null || $checkReservation->cancelled_at == '')) {
                                    $updateData = [
                                        'cancelled_at' => date('Y-m-d H:i:s', strtotime($content['published']))
                                    ];
                                    $reservation = Reservation::find($checkReservation->id)->update($updateData);

                                    $thirdpartyReservationUpdate = ThirdpartyReservation::where('barcode', $content['barcode'])->first();
                                    if (isset($thirdpartyReservationUpdate) && !empty($thirdpartyReservationUpdate)) {

                                        if (isset($content['firstName']) && !empty($content['firstName'])) {
                                            $thirdpartyReservationUpdate->firstName = $content['firstName'];
                                        }
                                        if (isset($content['lastName']) && !empty($content['lastName'])) {
                                            $thirdpartyReservationUpdate->lastName = $content['lastName'];
                                        }
                                        if (isset($content['oversizedFee']) && !empty($content['oversizedFee'])) {
                                            $thirdpartyReservationUpdate->oversizedFee = $content['oversizedFee'];
                                        }
                                        if (isset($content['oversizedFeeAmount']) && !empty($content['oversizedFeeAmount'])) {
                                            $thirdpartyReservationUpdate->oversizedFeeAmount = $content['oversizedFeeAmount'];
                                        }
                                        if (isset($content['oversizedFeeAmount']) && !empty($content['oversizedFeeAmount'])) {
                                            $thirdpartyReservationUpdate->oversizedFeeAmount = $content['oversizedFeeAmount'];
                                        }
                                        $thirdpartyReservationUpdate->entryDate = $content['entryDate'];
                                        $thirdpartyReservationUpdate->exitDate = $content['exitDate'];
                                        $thirdpartyReservationUpdate->status = $content['status'];
                                        $thirdpartyReservationUpdate->published_date = date('Y-m-d H:i:s', strtotime($content['published']));
                                        $thirdpartyReservationUpdate->updated_at = date('Y-m-d H:i:s');
                                        $thirdpartyReservationUpdate->save();
                                    }
                                } else {
                                    $thirdpartyReservationUpdate = ThirdpartyReservation::where('barcode', $content['barcode'])->first();

                                    if (!empty($thirdpartyReservationUpdate)) {

                                        if (isset($content['firstName']) && !empty($content['firstName'])) {
                                            $thirdpartyReservationUpdate->firstName = $content['firstName'];
                                        }
                                        if (isset($content['lastName']) && !empty($content['lastName'])) {
                                            $thirdpartyReservationUpdate->lastName = $content['lastName'];
                                        }
                                        if (isset($content['oversizedFee']) && !empty($content['oversizedFee'])) {
                                            if ($thirdpartyReservationUpdate->oversizedFee == true) {
                                                $thirdpartyReservationUpdate->oversizedFee = $content['oversizedFee'];
                                            }
                                        }
                                        /* if (isset($content['oversizedFeeAmount']) && !empty($content['oversizedFeeAmount'])) {
                                            $thirdpartyReservationUpdate->oversizedFeeAmount = $content['oversizedFeeAmount'];
                                        }
                                        if (isset($content['oversizedFeeAmount']) && !empty($content['oversizedFeeAmount'])) {
                                            $thirdpartyReservationUpdate->oversizedFeeAmount = $content['oversizedFeeAmount'];
                                        } */
                                        $thirdpartyReservationUpdate->entryDate = $content['entryDate'];
                                        $thirdpartyReservationUpdate->exitDate = $content['exitDate'];
                                        $thirdpartyReservationUpdate->published_date = date('Y-m-d H:i:s', strtotime($content['published']));
                                        $thirdpartyReservationUpdate->license = $license;
                                        $thirdpartyReservationUpdate->status = $content['status'];
                                        $thirdpartyReservationUpdate->save();
                                        $reservation = Reservation::find($checkReservation->id)->update($data);
                                    }
                                }
                            } else {

                                $this->log->info("reservation unchecked else " . $content['barcode']);

                                if ($content['rateType'] == 'hourly') {
                                    $reservation = Reservation::create($data);
                                }
                                //save data in thirdparty 
                                if (isset($content['firstName']) && !empty($content['firstName'])) {
                                    $thirdPartyData['firstName'] = $content['firstName'];
                                }
                                if (isset($content['lastName']) && !empty($content['lastName'])) {
                                    $thirdPartyData['lastName'] = $content['lastName'];
                                }

                                if (isset($content['oversizedFee']) && !empty($content['oversizedFee'])) {
                                    $thirdPartyData['oversizedFee'] = $content['oversizedFee'];
                                }

                                if (isset($content['oversizedFeeAmount']) && !empty($content['oversizedFeeAmount'])) {
                                    $thirdPartyData['oversizedFeeAmount'] = $content['oversizedFeeAmount'];
                                }

                                $thirdPartyData['facility_id'] = $val->id;
                                $thirdPartyData['thirdparty_integration_id'] = $thirdParty->thirdparty_integration_id;
                                $thirdPartyData['partner_id'] = $val->owner_id;
                                $thirdPartyData['published_date'] = date('Y-m-d H:i:s', strtotime($content['published']));
                                $thirdPartyData['status'] = $content['status'];
                                $thirdPartyData['entryDate'] = $content['entryDate'];
                                $thirdPartyData['exitDate'] = $content['exitDate'];
                                $thirdPartyData['thirdparty_facility_id'] = $content['facilityId'];
                                $thirdPartyData['orderId'] = $content['orderId'];
                                $thirdPartyData['barcode'] = $content['barcode'];
                                $thirdPartyData['license'] = $license;

                                if (isset($content['permitValue']) && !empty($content['permitValue'])) {
                                    $thirdPartyData['permitValue'] = $content['permitValue'];
                                }

                                if (isset($content['remittanceValue']) && !empty($content['remittanceValue'])) {
                                    $thirdPartyData['remittanceValue'] = $content['remittanceValue'];
                                }
                                if (isset($content['inOut']) && !empty($content['inOut'])) {
                                    $thirdPartyData['inOut'] = $content['inOut'];
                                }
                                if (isset($content['rateType']) && !empty($content['rateType'])) {
                                    $thirdPartyData['rateType'] = $content['rateType'];
                                }
                                ThirdpartyReservation::create($thirdPartyData);
                            }


                            //email send for success and failed data
                            $emailArray[$count]['url'] = $thirdParty->url;
                            $emailArray[$count]['barcode'] =  $content['barcode'];
                            $emailArray[$count]['license_plate'] = $license;

                            if (isset($reservation) && !empty($reservation)) {
                                $emailArray[$count]['status'] = 'success';
                                $count++;
                            } else {
                                $emailArray[$count]['status'] = 'failed';
                                $count++;
                            }
                            $this->log->info("Reservation Data saved");
                        }
                        // dd($emailArray);
                        // if (isset($emailArray) && !empty($emailArray)) {
                        //     Mail::send(
                        //         "third-party-emails.reservation-cron-info",
                        //         ['data' => $emailArray, 'is_failed' => '0'],
                        //         function ($message) use ($emailArray) {
                        //             $message->to(['<EMAIL>', '<EMAIL>'])->subject("Cron Info : Third Party Reservation");
                        //             $message->from(config('parkengage.default_sender_email'));
                        //         }
                        //     );
                        // }
                        $this->log->info("Reservation Data saved : " . json_encode($emailArray));
                    } else {
                        $msg = 'No data fetched from Third Party API :- ' . $thirdParty->url;
                        /*Mail::send(
                            "third-party-emails.reservation-cron-info", ['is_failed' => '1','msg'=>$msg], function ($message){
                                $message->to(['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'])->subject("Cron Info : Third Party Reservation");
                                $message->from(config('parkengage.default_sender_email'));
                            }
                        );*/
                        $this->log->info($msg);
                    }
                }
            } else {
                $this->log->info("No Facility Found.");
            }
            $this->log->info("Queue ended");
        } catch (Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");

            Mail::send(
                "third-party-emails.reservation-cron-info",
                ['is_failed' => '1', 'msg' => $msg],
                function ($message) {
                    $message->to(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'])->subject("Cron Info : Third Party Reservation");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
        }
    }
}
