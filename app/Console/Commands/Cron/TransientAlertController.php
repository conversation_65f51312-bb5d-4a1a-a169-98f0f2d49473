<?php

namespace App\Console\Commands\Cron;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\Ticket;
use App\Models\Facility;
use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;
use Exception;

class TransientAlertController extends Command
{
    protected $signature = 'notify:cron-transient-booked-status';

    protected $description = 'Notify if transient not booked for a dynamically configured duration.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/notify-cron-transient-booking-status')->createLogger('notify-cron-transient-booked-status');
    }

    public function handle()
    {
        try {
            $this->log->info("Cron started");

            // $partners = DB::table('users')->where('user_type', '3')->distinct()->pluck('created_by');
            $partners = [3307, 19349];

            if (empty($partners)) {
                $this->log->info("No partner IDs found.");
                return;
            }

            foreach ($partners as $partnerId) {
                $facilities = Facility::where('owner_id', $partnerId)->get();

                if ($facilities->isEmpty()) {
                    $this->log->warning("No Facility Found for Partner ID: $partnerId");
                    continue;
                }

                $partnerName = User::where('id', $partnerId)->value('name');
                $ticketData = [];
                $notificationTime = DB::table('notification_settings')
                    ->where('partner_id', $partnerId)
                    ->value('notification_time') ?? 30;

                $thresholdTime = Carbon::now()->subMinutes($notificationTime)->format('Y-m-d H:i:s');

                foreach ($facilities as $facility) {
                    $facilityId = $facility->id;
                    $facilityName = $facility->full_name ?? "Unknown Facility";

                    $lastTicket = Ticket::where('facility_id', $facilityId)
                        ->where('created_at', '<', $thresholdTime)
                        ->latest('created_at')
                        ->first();

                    $ticketData[] = [
                        'facility_name' => $facilityName,
                        'zone_id' => $facility->garage_code,
                        'last_ticket_number' => $lastTicket ? $lastTicket->ticket_number : 'N/A',
                        'created_time' => $lastTicket && $lastTicket->created_at ? $lastTicket->created_at->toDateTimeString() : 'N/A',
                    ];
                }

                if (!empty($ticketData)) {
                    $this->log->info("Sending notification for Partner ID: $partnerId");
                    $this->notifyDevTeam($partnerName, $ticketData, $notificationTime);
                }
            }

            $this->log->info("Cron ended successfully.");
        } catch (Exception $e) {
            $errorMessage = sprintf(
                "Error: %s in file %s on line %d",
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
            );

            $this->log->error($errorMessage);
        }
    }


    protected function notifyDevTeam($partnerName, $ticketData, $notificationTime)
    {
        $localTime = Carbon::now()->setTimezone('Asia/Kolkata')->toDateTimeString();
        $data = [
            'partner_name' => $partnerName,
            'ticket_data' => $ticketData,
            'notification_time' => $notificationTime,
            'cron_last_run' => $localTime,
        ];

        $recipients = config('parkengage.notify.recipient_notify', ['<EMAIL>']);

        try {
            Mail::send(
                'email.notify-transient',
                ['data' => $data],
                function ($message) use ($recipients, $partnerName, $notificationTime) {
                    $message->to($recipients)
                        ->subject("Alert: {$partnerName} - No tickets created in last {$notificationTime} mins");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );

            $this->log->info("Notifications sent to recipients: " . implode(", ", $recipients));
        } catch (Exception $e) {
            $this->log->error("Failed to send notification emails: {$e->getMessage()}");
        }
    }
}
