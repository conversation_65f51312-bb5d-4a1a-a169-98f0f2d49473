<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use Carbon\Carbon;
// to check autopaymethods
use App\Models\ParkEngage\AutopayMethod;
// to use logging system
use App\Services\LoggerFactory;
use App\Models\User;

use Illuminate\Console\Command;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Models\ParkEngage\UserMembership;

use App\Jobs\ProcessAutopayment;

/**
 * Queue up autopayments at the start of each month.
 * Gets a list of monthly parking users with Autopay active before today and queues jobs
 *
 * @package App\Console\Commands\Cron\
 */
class Autopay extends Command
{
    use DispatchesJobs;

    const QUEUE_NAME = 'auto-renew-partner-membership';

    protected $signature = "cron:auto-renew-partner-membership
                            { --user_id= : User ID to process }
                            { --full : Process the full auto renew job }
                            { --dry : Do a dry run of the Auto Renew job. A 3 second time out will be used in place of hitting Authorize.Net }
                            { --limit= : Limit processing to this number of users}";

    protected $description = "Processes memberhip renew for all recurring users";

    protected $count;
    protected $total_count;

    // To store error log
    protected $errorLog;
    
    protected $totalCountLog;
    // To store user information for logging parpose
    protected $userDescription;

    protected $methods;
    // Including logging dependency
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->errorLog = $logFactory->setPath('logs/auto_renew_membership')->createLogger('autorenew-excluded-users');
        $this->totalCountLog = $logFactory->setPath('logs/auto_renew_membership_count')->createLogger('autorenew-total-count-logs');
    }

    public function handle()
    {
        // Get all accounts with autopay methods created before today
        $user_id = $this->option('user_id');
        $full = $this->option('full');
        $dry = $this->option('dry');
        $limit = $this->option('limit');
        
        //query on membership table
         $query = UserMembership::query();
        
         // Necessary to add 1 day
         $current_date = date('Y-m-d', strtotime('+1 day'));

        if ($user_id) {
            
            $arr_users_membership = $query::where('user_id', $user_id)->get();
            
        } else if ($full || $dry) {
          
            // Get users that have not had autopay run this month
           //Query corrections
           //$users_membership = UserMembership::where('is_active', 1)->where('end_date', '<=', $current_date)->orderBy('user_id');
            $query
            ->where(function($query) use($current_date){
                 $query->where('last_autorenew', '<', $current_date)
                ->orWhereNull('last_autorenew');
            })
            ->where('is_active', 1)
            ->where('end_date', '<=', $current_date)
            /* To test for particular IDs */
            // ->whereIn('id', [310, 313])
            ->whereHas(
                    'autopayMethods', function ($query) {
                        // You must have enabled autopay by 11:59 of the last day of the previous month to be added to autopay
                        //$query->where('created_at', '<=', Carbon::now()->subMonth()->endOfMonth()->toDateTimeString())->orWhereNull('created_at');
                    }
                );        
            
              // Set limit if provided
            if ($limit = $this->option('limit')) {
                $query->take($limit);
            }

              $arr_users_membership = $query->get();
        }

        /* To test the queue */
        // foreach($arr_users_membership as $user_membership) {
        //     $this->dispatch((new ProcessAutopayment($user_membership, $dry))); //->onQueue(self::QUEUE_NAME));
        // }
    
        $this->count = $this->total_count = $arr_users_membership->count();
        
        $this->totalCountLog->info("Total Record count From Database to Proceed is- {$this->total_count}");        
         
        // We can push the whole user on to the queue because laravel will send only the model identifier
        // See https://laravel.com/docs/5.2/queues#job-class-structure
        $arr_users_membership->each(
            function ($user_membership) use ($dry) {
            
            try{

                $this->userDescription = "User ID: {$user_membership->user_id} membership_plan_id: {$user_membership->membership_plan_id}";

                 $this->methods = AutopayMethod::where('user_membership_id', '=', $user_membership->id)->orderBy('order', 'asc')->get();
                              
                   // To check if cim record deleted or not found in anet_monthly_parking_cims table
                    $cimflag=0;
                    $this->methods->each(function($method) use ($user_membership,&$cimflag){
                        if(!$method->paymentProfile->cim){
                            $this->errorLog->error("Customer Profile doesn't exist for autopay method {$method->id} of {$this->userDescription}");
                            $cimflag++;
                        }
                    });
                      
                    if($this->methods->count()!==$cimflag){
                        $this->dispatch((new ProcessAutopayment($user_membership, $dry))->onQueue(self::QUEUE_NAME));
                    }
                    else{
                         $this->errorLog->error("No Customer Profile is set up for {$this->userDescription}");
                        $this->count--;
                    }
                 
                // }
            }catch(\Exception $e)
            {
                 $this->errorLog->error("Some Error while Adding this membership payment to Queue- {$this->userDescription}- Error is {$e->getMessage()}");
                 $this->count--;
                 
            }   
                
            }
        );

        $this->sendStartNotification();

        print_r("{$this->count} auto payments queued for processing.");
    }

    protected function sendStartNotification()
    {
        $message = "Membership auto renewal process has been commenced. $this->count membership account(s) have been added into queue for auto renewal.";

        Mail::send(
            'parkengage.autopay-commenced-email', ['body' => $message], function ($message) {
                $emails = array_merge([config('parkengage.admin_email')], ([config('parkengage.technical_emails')]));

                $message->subject("Membership Auto Renewal Job Commenced");
                $message->to($emails);
                $message->from(config('parkengage.default_sender_email'));
            }
        );
    }
}
