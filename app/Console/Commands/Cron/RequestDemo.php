<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use App\Models\ParkEngage\RequestDemo as RD;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;

class RequestDemo extends Command
{

    protected $signature = 'email:request-demo {id}';

    protected $description = 'Send request demo email.';

    public function handle()
    {
       $id = $this->argument('id');
       $data = RD::getAllRequestDemoById($id);
       
        //admin mail
        Mail::send(
            "parkengage.request_demo", ['data' => $data], function ($message) {
                $message->to(config('parkengage.info_sender_email'))->subject("New user request for demo");
                $message->from(config('parkengage.default_sender_email'));
            }
        );

        //user mail
        Mail::send(
            "parkengage.request_demo_user", ['data' => $data], function ($message) use($data) {
                $message->to($data->email)->subject("Welcome to ParkEngage");
                $message->from(config('parkengage.default_sender_email'));
            }
        );
       
    }
    
}
