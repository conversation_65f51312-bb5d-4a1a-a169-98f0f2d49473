<?php

namespace App\Console\Commands\Cron;

use Response;
use Mail;
use Exception;
use File;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\RevenueBalance;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\ApiGenericException;
use App\Models\Ticket;
use App\Models\ParkEngage\TicketCitation;
use Carbon\Carbon;

class PartnerAutogateRevenueBalance extends Command
{

    protected $signature = 'partner-autogate-revenue-balance';

    protected $description = 'Add last month revenue balance of autogate partner at the starting of the month.';


    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/partner-revenue-autogate')->createLogger('partner-revenue-autogate');
    }


    public function handle()
    {   
        $month = date('Y-m', strtotime("-1 month"));
        //$month = date('Y-m', strtotime("m"));
        $from_date  = date($month.'-01');
        $mid_date  = date($month.'-15');
        $to_date  = date($month.'-t');
        $midDateString = strtotime($mid_date);   
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);
        /*$month = date('m', strtotime("-1 month"));
        $from_date  = date('Y-'.$month.'-01');
        $mid_date  = date('Y-'.$month.'-15');
        $to_date  = date('Y-'.$month.'-t');
        $midDateString = strtotime($mid_date);   
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);*/
           
        $users = User::where('user_type', '3')->where('id','1165')->get();
        $this->log->info("Cron is about to start");
        if($users){
            foreach($users as $user){
                $totalBalance = 0;
                $totalCurrentMonthBalance = 0;
                $totalRemainder = 0;
                $userConfig = UserPaymentGatewayDetail::where('user_id', $user->id)->first();
                if(!$userConfig){
                    $this->log->error("Partner does not have configuration : ".$user->id);
                   continue;
                }

             

                $checkinShare = $userConfig->checkin_share ? $userConfig->checkin_share : 0;
                $citationShare = $userConfig->citation_share ? $userConfig->citation_share : 0;
                $monthlyAmount = $userConfig->monthly_amount ? $userConfig->monthly_amount : 0;

                $total = 0;
                $bookingCount = 0;
                $totalShare = 0;
                $passShareAmount = 0;
                $checkinShareAmount = 0;
                $totalPassDays = 0;
                $citationShareAmount = 0;
                $citationCount = 0;
                $overstayShareAmount = 0;
                $overstayCount = 0;
                $data = [];

try{        
                $tickets = Ticket::with('overstay')->where('partner_id', $user->id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->orderBy('id', "DESC")->get();
                if(count($tickets) > 0){
                    $this->log->info("Get all the tickets of : ".$user->id);
                    foreach ($tickets as $key=>$value) {
                        if(count($value->overstay) == 0){
                        if(strtotime(date("Y-m-d", strtotime($value->check_in_datetime))) == strtotime(date("Y-m-d", strtotime($value->checkout_datetime))) ){
                            //$totalShareRevenue += $checkinShare;  
                            $total += $value->total;        
                            $data['checkin_share'] = $checkinShare;
                            $totalShare += $checkinShare;                            
                            $checkinShareAmount += $checkinShare;
                            $totalCurrentMonthBalance += $checkinShare;
                            $bookingCount++;
                        }else{
                            $total += $value->total;
                              $checkinDate = Carbon::parse(date("Y-m-d", strtotime($value->check_in_datetime)));
                              $checkoutDate = date("Y-m-d", strtotime($value->checkout_datetime));
                              //$totalShareRevenue += $checkinDate->diffInDays($checkoutDate) * $checkinShare;
                           //   $totalShareRevenue += $checkinDate->diffInDays($checkoutDate) * $checkinShare;  
                            //$total += $value->total;
                            $data['checkin_share'] = $checkinShare;
                            $totalShare += $checkinDate->diffInDays($checkoutDate) * $checkinShare;                            
                            $checkinShareAmount += $checkinDate->diffInDays($checkoutDate) * $checkinShare;
                            $totalCurrentMonthBalance += $checkinDate->diffInDays($checkoutDate) * $checkinShare;
                            $bookingCount++;
                        }
                            
                        }else{
                            if($value->overstay){
                              $checkinDate = Carbon::parse(date("Y-m-d", strtotime($value->check_in_datetime)));
                              $checkoutDate = Carbon::parse(date("Y-m-d", strtotime($value->checkout_datetime)));/*date("Y-m-d", strtotime($value->checkout_datetime));*/
                              $totalTicketDays = $checkinDate->diffInDays($checkoutDate);
                              //echo $checkinDate.'--'.$checkoutDate.'--'.$totalTicketDays.'--br--';
                              $totalTicketDays ++;
                              $totalOverstayDays = 0;
                              $data['checkin_share'] = $checkinShare;
                              $data['overstay_share'] = $citationShare;
                            foreach ($value->overstay as $k => $v) {
                                    $overstayCheckinDate = Carbon::parse(date("Y-m-d", strtotime($v->check_in_datetime)));
                                    $overstayCheckoutDate = date("Y-m-d", strtotime($v->checkout_datetime));
                                    $totalOverstayDays += $overstayCheckinDate->diffInDays($overstayCheckoutDate) + 1;
                                    $total += $v->total;
                                    $overstayCount ++;
                                    //echo $checkinDate.'--'.$checkoutDate.'--'.$totalTicketDays.'--br--';
                            }
                            //echo $totalTicketDays.'--'.$totalOverstayDays.'--br--';
                                $ticketDays = $totalTicketDays - $totalOverstayDays;
                                //$totalShareRevenue += ($ticketDays) * $checkinShare;
                                //$totalShareRevenue += $totalOverstayDays * $checkinShare;
                                $total += $value->total;
                                $totalShare += ($ticketDays) * $checkinShare;                            
                                $checkinShareAmount += ($ticketDays) * $checkinShare;
                                $overstayShareAmount += $totalOverstayDays * $citationShare;
                                $totalShare += $overstayShareAmount;
                                $totalCurrentMonthBalance += ($ticketDays) * $checkinShare + $overstayShareAmount;
                                $bookingCount++;
                            }
                        }
                    }
                    
                }//ticket close

}catch(Exception $e)
{
 $this->log->info("exception : ".json_encode($e));


}

$citations = TicketCitation::where('partner_id',$user->id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->get();

        $citationRevenue = 0;
        if(count($citations) > 0){  
          foreach ($citations as $key => $value) {
              //$total += $value->grand_total;
              //$totalShareRevenue += $citationShare;
                $citationShareAmount += $citationShare;
              if(strtotime(date("Y-m-d", strtotime($value->checkin_time))) == strtotime(date("Y-m-d", strtotime($value->checkout_time))) ){
                //$totalShare += $citationShare;
                if($value->anet_transaction_id != ''){
                    $total += $value->total;
                    $data['citation_share'] = $citationShare;
                    $totalShare += $citationShare;                            
                    $citationShareAmount += $citationShare;
                    $totalCurrentMonthBalance += $citationShare;
                    
                }
                $citationCount++;
              }else{
                if($value->anet_transaction_id != ''){
                    $checkinDate = Carbon::parse(date("Y-m-d", strtotime($value->checkin_time)));
                    $checkoutDate = date("Y-m-d", strtotime($value->checkout_time));
                    //$totalShareRevenue += $checkinDate->diffInDays($checkoutDate) * $checkinShare;

                    $total += $value->total;
                    $data['citation_share'] = $citationShare;
                    $totalShare += $citationShare;                            
                    $citationShareAmount += $citationShare + ($checkinDate->diffInDays($checkoutDate) * $citationShare);
                    $totalCurrentMonthBalance += $citationShareAmount;
                }
                $citationCount++;  
              }
          }
        }
//dd($overstayShareAmount, $overstayCount);
                 if(count($data) > 0){

                    $configTotal = $userConfig->total_balance;
                    $data['total'] = $total;
                    $data['total_share'] = $checkinShareAmount + $citationShareAmount + $overstayShareAmount;
                    $data['partner_id'] = $user->id;
                    $data['from_date'] = $from_date;
                    $data['to_date'] = $to_date;
                    $data['remainder'] = $configTotal;
                    $data['final_amount'] = $checkinShareAmount + $citationShareAmount + $overstayShareAmount  + $monthlyAmount + $configTotal;
                    $data['checkin_count'] = $bookingCount;
                    $data['overstay_count'] = $overstayCount;
                    $data['citation_count'] = $citationCount;
                    $data['checkin_share_amount'] = $checkinShareAmount;
                    $data['citation_share_amount'] = $citationShareAmount;
                    $data['overstay_share_amount'] = $overstayShareAmount;
                    $data['monthly_amount'] = $monthlyAmount;
                    
                    RevenueBalance::create($data);

                    $userConfig->last_cron_run = date("Y-m-d");
                    $userConfig->total_balance = $userConfig->total_balance + $totalCurrentMonthBalance;
                    $userConfig->current_month_balance = $totalCurrentMonthBalance;
                    $userConfig->remainder = $userConfig->remainder + $totalRemainder;
                   $userConfig->save();    
                    $this->log->info("Save all the balance of user : ".$user->id);
                 }    

                
            }//foreach close
        }//if close
        
    }
    
}

