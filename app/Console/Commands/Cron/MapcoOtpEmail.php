<?php

namespace App\Console\Commands\Cron;

use Storage;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\ParkEngage\MapcoQrcode;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Services\Image;
use App\Services\Pdf;

class MapcoOtpEmail extends Command
{

    protected $signature = 'mapco:otp-email {id} {email} {otp}';

    protected $description = 'Send otp to user';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/mapco-otp-email')->createLogger('mapco-otp-email');
    }

    public function handle()
    {
        

           $id = $this->argument('id');  
           $email = $this->argument('email');  
           $otp = $this->argument('otp');  
            $data=['ticketech_code'=>$id,'email'=>$email,'otp'=>$otp];
          //  $type = $this->argument('type'); 
            $this->log->info("Mail start");
          // $data = Reservation::with(['transaction','facility','user', 'rate','mapcoQrCode.event','mapcoQrCode.eventCategory.eventCategoryEvent.event'])->where('id',$id)->first();


        
            
            //user mail
            try{
                   //$data->user->email
                    Mail::send(
                       "mapco.otp", ['data' => $data], function ($message) use($data) {
                        $message->to($data['email'])->subject("Your Mapco Parking OTP Confirmation- ". $data['ticketech_code']);
                        $message->from(config('parkengage.default_sender_email'));
                        
                    }
                );
               

                $this->log->info("Mail sent to ".$data['email']);
            } catch(Exception $e) {
                $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                $this->log->error($msg);
                $this->log->info("Queue ended");            
            }
       
    }

     public function generateBarcodeJpgNew($qrcode)
    {   
        $html = $this->generateBarcodeHtml($qrcode);

        $image = app()->make(Image::class);
        $image->setOption('width', '420');
        return $image->getOutputFromHtmlString($html);
    }

    public function generateBarcodeHtml($qrcode)
    {
        return view('mapco.Qrcodegenerate', ['barcode' => $qrcode]);
        
    }



}