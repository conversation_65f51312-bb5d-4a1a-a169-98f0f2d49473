<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;

use Illuminate\Support\Collection;
use Illuminate\Console\Command;

use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

use App\Console\Commands\Supervisor\SupervisorCommand;

class MonitorQueues extends Command
{

    protected $signature = 'cron:monitor-queues';

    protected $description = 'Checks that supervisor queues are still running and notifies via email if they are not.';

    public function handle()
    {
        $log = $this->createLog();

        $this->getSupervisorQueues()->each(
            function ($queue) use ($log) {
                if ($this->isQueueActive($queue)) {
                    return $log->info("Queue $queue is active.");
                }

                $log->error("Queue $queue is not active.");
                $this->sendNotificationEmail($queue);
            }
        );
    }

    public function getSupervisorQueues(): Collection
    {
        $configs = File::files(base_path(SupervisorCommand::SUPERVISOR_DIR));

        return collect($configs)->map(
            function ($fileName) {
                $file = File::basename($fileName);
                $fileArray = explode('.', $file);
                return $fileArray[0];
            }
        );
    }

    public function isQueueActive(string $queueName): bool
    {
        $process = new Process("ps -axo command | grep php | grep $queueName");
        $process->run();
        $output = $process->getOutput();

        $lines = collect(explode("\n", $output))->filter(
            function ($line) {
                return !!$line && !str_contains($line, 'ps -axo command');
            }
        );

        return $lines->count() > 0;
    }

    // Note, email MUST be send without queueing, as if the email queue is down then no
    // notification email will be sent
    public function sendNotificationEmail(string $queueName)
    {
        $body = "The queue $queueName has no active processes, please check the server status to ensure correct operation.";
        return Mail::send(
            "email.generic", ['body' => $body], function ($message) use ($queueName) {
                $message->to(config('icon.technical_emails'))->subject("Queue $queueName has stopped running");
                $message->from(env('MAIL_DEFAULT_FROM'));
            }
        );
    }

    protected function createLog()
    {
        return app()->make('LoggerFactory')->setPath('logs/supervisor')->createLogger('queue_status');
    }
}
