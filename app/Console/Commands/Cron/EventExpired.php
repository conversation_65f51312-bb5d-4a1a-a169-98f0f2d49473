<?php

namespace App\Console\Commands\Cron;

use Mail;
use File;
use Exception;
use Carbon\Carbon;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use App\Models\Event;
use App\Services\LoggerFactory;

class EventExpired extends Command
{

    protected $signature = 'event-expired';

    protected $description = 'Expire Events.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
    
        $this->log = $logFactory->setPath('logs/parkengage')->createLogger('event_expired');
    }
    public function handle()
    {
      $start_date = date("Y-m-d"); 
      $events = Event::whereDate('end_time' , '<', $start_date)->where('is_active', '1')->get();
       
      $ids = '';
      if($events){
        foreach ($events as $key => $value) {

          $ids .= $value->id .',';
          $value->is_active = '0';
          $value->save();
        }
		$this->log->info("Event expired ids -- $ids ");
      }
      $this->log->info("Event expired ids -- $ids ");
      echo ($ids);
        
        
       
    }
    
}
