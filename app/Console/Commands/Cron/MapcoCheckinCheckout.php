<?php

namespace App\Console\Commands\Cron;

use Storage;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\ParkEngage\MapcoQrcode;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Services\Image;
use App\Models\Ticket;
use App\Models\Event;
class MapcoCheckinCheckout extends Command
{

    protected $signature = 'mapco:checkin-checkout';

    protected $description = 'Update Ticket table checkout column which event end is expire';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/cron-mapco-checkin-checkout')->createLogger('mapco-checkin-checkout');
    }

    public function handle()
    {
            $facility_id = 32;
            $this->log->info("checkout start");
            try{
                $today = date("Y-m-d");
                $ticketData = Ticket::/*whereDate('checkout_datetime','<',$today)->*/where(['facility_id'=>$facility_id, 'is_checkout' => '0'])->get();
                if(count($ticketData) > 0){
                    $this->log->info("Before checkout email about to send");
                    //user mail
                    $ticketCountBeforeCheckout = count($ticketData);
                    $ticketCountAfterCheckout = 0;
                    $data = $ticketCountBeforeCheckout;
                    Mail::send(
                        "mapco.alert-before-checkout", ['data' => $data], function ($message) use($data) {
                            $message->to(['<EMAIL>','<EMAIL>'])->subject("Mapco Cron Alert before Checkout");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Before checkout email sent");
                    $gates = \DB::table('gates')
                                    ->select('gate')
                                    ->where('facility_id', $facility_id)
                                    ->where('gate_type', 'exit')
                                    ->first();
                    foreach($ticketData as $val)
                    {
                        
                        if($val->checkout_datetime != ''){
                            $endDate = date("Y-m-d", strtotime($val->checkout_datetime));
                            if(strtotime($endDate) <= strtotime($today)){
                                $status=Ticket::where(['ticket_number'=> $val->ticket_number,'facility_id'=> $facility_id])->update(['is_checkout' => '1', 'checkout_time' => date("Y-m-d H:i:s"), "remark" => "checkout by cron", "checkout_gate" => $gates->gate]);                        
                                $this->log->info("Checkout update for Ticket No ".$val->ticket_number);    
                            }
                            $ticketCountAfterCheckout ++;
                        }else{
                            if($val->event_id != ''){
                                $event = Event::whereDate("end_time", '<', $today)->where('id',$val->event_id)->first();
                                if($event){
                                
                                    $status=Ticket::where(['ticket_number'=> $val->ticket_number,'facility_id'=> $facility_id])->update(['is_checkout' => '1', 'checkout_time' => date("Y-m-d H:i:s"), "remark" => "checkout by cron", "checkout_gate" => $gates->gate]);
                                    $this->log->info("Checkout update for Ticket No ".$val->ticket_number);
                                    $ticketCountAfterCheckout ++;
                                }
                            }
                        }
                        
                        
                    }

                    if($ticketCountAfterCheckout > 0){
                        $this->log->info("After checkout email about to send");
                        Mail::send(
                            "mapco.alert-after-checkout", ['data' => $ticketCountAfterCheckout], function ($message) use($ticketCountAfterCheckout) {
                                $message->to(['<EMAIL>','<EMAIL>'])->subject("Mapco Cron Alert After Checkout");
                                $message->from(config('parkengage.default_sender_email'));
                            }
                        );
                        $this->log->info("After checkout email sent");
                    }
                }

                
            } catch(Exception $e) {
                $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                $this->log->error($msg);
                Mail::send(
                    "mapco.alert-error-checkout", ['data' => $msg], function ($message) use($msg) {
                        $message->to(['<EMAIL>','<EMAIL>'])->subject("Error : Mapco Cron Alert Checkout");
                        $message->from(config('parkengage.default_sender_email'));
                    }
                );
                $this->log->info("Queue ended");            
            }
       
    }

   



}