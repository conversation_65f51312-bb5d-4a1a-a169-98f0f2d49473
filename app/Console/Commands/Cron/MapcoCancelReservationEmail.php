<?php

namespace App\Console\Commands\Cron;

use App\Http\Helpers\QueryBuilder;
use Storage;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\ParkEngage\MapcoQrcode;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;

class MapcoCancelReservationEmail extends Command
{

    protected $signature = 'mapco:cancel-reservation-email {id}';

    protected $description = 'Send email to user after make reservation payment';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/mapco-reservation-cancel')->createLogger('mapco-reservation-cancel');
    }

    public function handle()
    {
        
      try {
          $this->log->info("Mail start");

          $id = $this->argument('id');  
            
          $data = Reservation::with(['transaction','facility','user', 'rate','mapcoQrCode.event','mapcoQrCode.eventCategory.eventCategoryEvent.event'])->where('id',$id)->first();
          foreach($data->mapcoQrCode as $key=>$value){
            
            //$this->generateBarcodeJpgNew($value->qrcode);
            $imageBarcodeFileName = str_random(10) . '_mapco.png';
            Storage::put($imageBarcodeFileName, $this->generateBarcodeJpgNew($value->qrcode));
            $data['mapcoQrCode'][$key]['updated_qrcode'] = $imageBarcodeFileName;            
          }

          $brand_setting = BrandSetting::where('user_id', $data->partner_id)->first();

          $this->log->info("===============Reached 1=====================");
          $fac_brand_setting = FacilityBrandSetting::where('facility_id', $data->facility->id)->first();
          if ($fac_brand_setting) {
            $this->log->info("===============Reached 2 =====================");
            $data->facility_logo_id = $fac_brand_setting->id;
          } else {
            $this->log->info("===============Reached 3 =====================");
            $data->logo_id = $brand_setting->id;
          }
          $this->log->info("===============Reached 4 =====================");

          $pdf = (new MapcoQrcode())->generatePdf($data, Pdf::class);
          $this->log->info("===============Reached 5 =====================");

          $pdfName = '';
          if(isset($data->rate->description)){
            $pdfName = $data->rate->description;
          }else{
            $pdfName = $data->mapcoQrCode[0]->event->slug;
          }
          $this->log->info("===============Reached 6 =====================");

          if(in_array($data->partner_id, [config('parkengage.PARTNER_MAPCO')])){  
            $view = "mapco.cancel-reservation";
            $title = "Your Booking has been canceled - ";
            $phone = QueryBuilder::formatPhoneNumber(substr($data->user->phone, -10), false);
            $data->user->phone = $phone;
          }else{
            $view = "townsend.cancel-reservation-event";
            $title = "Your Event Booking Cancel Confirmation- ";
          }  
          $data->brand_setting = $brand_setting;
          //dd($data->transaction);
              
          //user mail
          Mail::send(
              $view,  ['data' => $data], function ($message) use($data, $pdf, $pdfName,$title) {
              $message->to($data->user->email)->subject($title. $data->ticketech_code);
              $message->from(config('parkengage.default_sender_email'));
              //$message->attachData($pdf,$pdfName.".pdf");
              }
          );

          $this->log->info("Mail sent to ".$data->user->email);
        } catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
       
    }

    public function generateBarcodeJpgNew($qrcode)
    {   
        $html = $this->generateBarcodeHtml($qrcode);

        $image = app()->make(Image::class);
        $image->setOption('width', '420');
        return $image->getOutputFromHtmlString($html);
    }

    public function generateBarcodeHtml($qrcode)
    {
        return view('mapco.Qrcodegenerate', ['barcode' => $qrcode]);
        
    }



}