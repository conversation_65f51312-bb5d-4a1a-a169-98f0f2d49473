<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\UserPermitRequest;
use App\Models\UserPermitVehicle;
use App\Models\UserPermitVehicleMapping;
use Auth;
use App\Models\ParkEngage\BrandSetting;
use App\Services\LoggerFactory;
use App\Models\User;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\PermitRateCriteriaMapping;
use App\Models\PermitRateCriteria;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Models\ParkEngage\FacilityPaymentDetail;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Http\Helpers\MailHelper;


class UserPermitRequestConfirmationEmail extends Command
{

    protected $signature = 'email:user-permit-request-confirmation {id}';

    protected $description = 'Send User Permit Request Confirmation Email.';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/permit-request')->createLogger('permit-request');
    }

    public function handle()
    {
        $id = $this->argument('id');
        $user = UserPermitRequest::find($id);
        if (!$user) {
            $this->log->info("No user with that ID.");
        }
         //PMIS-14836 
        $monthlyRequest = UserPermitRequest::with(['facility.facilityConfiguration', 'user', 'transaction'])->where('id', $id)->first();
        //0-Admin,1-Customer,2-Both 
        if (isset($monthlyRequest->facility->facilityConfiguration) && ($monthlyRequest->facility->facilityConfiguration->permit_email == 0)) {
        return true;
        } elseif(isset($monthlyRequest->facility->facilityConfiguration) && ($monthlyRequest->facility->facilityConfiguration->permit_email == 1) && ($monthlyRequest->facility->facilityConfiguration->cust_email_admin_email == 0) && $monthlyRequest->is_admin == 0 ) {
        return true;
        } elseif(isset($monthlyRequest->facility->facilityConfiguration) && ($monthlyRequest->facility->facilityConfiguration->permit_email == 1) && ($monthlyRequest->facility->facilityConfiguration->cust_email_admin_email == 1) && $monthlyRequest->is_admin == 1 ){
        return true;
        }
        //PMIS-14836
        $permit_vehicle = UserPermitVehicleMapping::where('permit_request_id', $id)->pluck('permit_vehicle_id');
        $vehicle = UserPermitVehicle::wherein('id', $permit_vehicle)->get();
        if ($user->facility_id == config('parkengage.WAILUKU_FACILITY')) {
            $view = "are.user_permit_request_confirmation_email";
        } else if ($user->facility_id != config('parkengage.WAILUKU_FACILITY') && $user->partner_id == config('parkengage.PARTNER_DIAMOND')) {
            $view = 'are-diamond.user_permit_request_confirmation_email';
        } else {
            $view = "are.user_permit_request_email";
        }
        $brand_setting = '';
        $brand_setting = BrandSetting::where('user_id', $user->partner_id)->first();
        $partnerDetails = User::where('id', $user->partner_id)->first();
        #add parking time in email
        $permit_rate_criteria_id = PermitRateCriteriaMapping::where('permit_rate_id', $user->permit_rate_id)->pluck('permit_rate_criteria_id');
        if (count($permit_rate_criteria_id) > 0) {
            $user->permit_rate_criteria = PermitRateCriteria::wherein('id', $permit_rate_criteria_id)->get();
            $user->permit_rate_criteria = $this->formatPermitRateCriteria($user->permit_rate_criteria);
        } else {
            $formated_time = [];
            $formated_time[] = [
                'days' => "All Days",
                'entry_time_begin' => "12:00 AM",
                'entry_time_end' => "11:59 PM",
                'exit_time_begin' => "12:00 AM",
                'exit_time_end' => "11:59 PM",
            ];
            $user->permit_rate_criteria = $formated_time;
        }
        #end add parking time in email
        $permit_services = PermitRequestServiceMapping::where('user_permit_request_id', $user->id)->pluck('permit_service_id');
        //$services        = PermitServices::whereIn('id',$permit_services)->get();

        $services  = PermitServices::with([
            'permit_service_criteria_mappings' => function ($query) use ($permit_services) {
                $query->whereIn('permit_service_id', $permit_services)
                    ->with('criteria');
            }
        ])
            ->select('permit_services.*')
            ->whereIn('id', $permit_services)
            ->orderBy('permit_services.id', 'asc')
            ->get();

        if (count($services) > 0) {
            $services = $this->formatPermitServiceCriteria($services);
        }

        $permit_validity = '';
        $permitRateDescHour = array();
        $permitRateDetails    = PermitRate::find($user->permit_rate_id);
        if ($permitRateDetails) {
            $permitRateDescHour = PermitRateDescription::find($permitRateDetails->permit_rate_description_id);

            if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "1")) {
                $permit_validity = '1 Month';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "2")) {
                $permit_validity = '2 Month';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "3")) {
                $permit_validity = 'Quarterly';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "4")) {
                $permit_validity = '4 Month';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "5")) {
                $permit_validity = '5 Month';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "6")) {
                $permit_validity = 'Half Yearly';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "7")) {
                $permit_validity = '7 Month';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "8")) {
                $permit_validity = '8 Month';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "9")) {
                $permit_validity = '9 Month';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "10")) {
                $permit_validity = '10 Month';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "11")) {
                $permit_validity = '11 Month';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "12")) {
                $permit_validity = '1 Year';
            } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "13")) {
                $permit_validity = 'Weekly';
            } else {
                $permit_validity = '1 Month';
            }

            /*
            if($permitRateDescHour && (strtolower($permitRateDescHour->hours_description)== "yearly")){
                $permit_validity = '1 Year';
            }else if($permitRateDescHour && (strtolower($permitRateDescHour->hours_description)== "mtm")){
                $permit_validity = '1 Month';
            }
            else if($permitRateDescHour && (strtolower($permitRateDescHour->hours_description)== "monthly")){
                $permit_validity = '1 Month';
            }
            */
            if ($permitRateDescHour && $permitRateDescHour->is_hide_validity == "1") {
                $permit_validity = '';
            }
        }
        $user->permit_rate_data = $user->permit_rate;


        // Add Card details
        $facilityPaymentDetails = FacilityPaymentDetail::where('facility_id', $user->facility_id)->first();
        $permitUser = User::where('created_by', $user->partner_id)->where('email', $user->email)->first();
        $paymentProfile = '';
        if (($facilityPaymentDetails) && $facilityPaymentDetails->facility_payment_type_id == '1') {
            $paymentProfile = PlanetPaymentProfile::where('user_id', $permitUser->id)->first();
        } else if (($facilityPaymentDetails) && $facilityPaymentDetails->facility_payment_type_id == '2') {
            $paymentProfile = DatacapPaymentProfile::where('user_id', $permitUser->id)->first();
        } else if (($facilityPaymentDetails) && $facilityPaymentDetails->facility_payment_type_id == '4') {
            $paymentProfile = HeartlandPaymentProfile::where('user_id', $permitUser->id)->first();
        }
        #PIMS-10032 dushyant phase2
        if (is_null($user->business_id) || empty($user->business_id)) {
        } else {
            #DD PIMS-11544
            if (isset($permitUser->payment_mode) && !$permitUser->payment_mode) {
                $new_permit_amt = ($user->negotiated_amount ?? $user->permit_final_amount);
                $user->permit_rate = $new_permit_amt;
            }
        }

        try {
            $this->log->info("User email sending: $user->email");

            MailHelper::sendEmail($user->email, $view, ['subject' => "Your permit request has been submitted successfully", 'user' => $user, 'vehicle' => $vehicle, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'paymentProfile' => $paymentProfile], $user->partner_id);


            // Mail::send(
            // 	$view, ['user' => $user,'vehicle' =>$vehicle, 'brand_setting' => $brand_setting,'partner_details' => $partnerDetails,'services' => $services,'permitRateDescHour' => $permitRateDescHour,'permit_validity'=>$permit_validity,'paymentProfile'=>$paymentProfile], function ($message) use($user) {
            // 		$message->to($user->email)->subject("Your permit request has been submitted successfully");
            // 		$message->from(config('parkengage.default_sender_email'));
            // 	}
            // );
            $this->log->info("email sent: $user->email");
            if ($user->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                $view = "are.alert-user-permit-request";
                Mail::send(
                    $view,
                    ['user' => $user, 'vehicle' => $vehicle, 'brand_setting' => $brand_setting],
                    function ($message) use ($user) {
                        $message->to([config('parkengage.WAILUKU.USER_1'), config('parkengage.WAILUKU.USER_2'), config('parkengage.WAILUKU.USER_2')])->subject("New permit request has been received");
                        $message->from(config('parkengage.default_sender_email'));
                    }
                );
            }
        } catch (\Exception $e) {
            $errorMessage = array();
            $errorMessage['message'] = $e;
            $this->log->error('Issue in email sending:', $errorMessage);
        }
    }

    #dushyant 06-06-2024
    #add parking time in email
    private function formatPermitRateCriteria($criteria)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        $formatted = [];

        foreach ($criteria as $item) {
            $days = explode(',', $item->days);
            sort($days); // Sort days to match the sequence of $allDays
            if ($days == $allDays) {
                $dayNamesStr = 'All Days';
            } else {
                $dayNames = array_map(function ($day) use ($daysMap) {
                    return $daysMap[$day];
                }, $days);
                $dayNamesStr = implode(',', $dayNames);
            }

            $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
            $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
            $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

            // Adjust exit time if it's greater than 24 hours
            $exit_time = explode(":", $item->exit_time_end);
            if ($exit_time[0] > 23) {
                $next_hr = $exit_time[0] - 24;
                $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                $exit_time_overflow = ' (next day)';
            } else {
                $exit_time_overflow = '';
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
            }


            $formatted[] = [
                'days' => $dayNamesStr,
                'entry_time_begin' => $entry_time_begin,
                'entry_time_end' => $entry_time_end,
                'exit_time_begin' => $exit_time_begin,
                'exit_time_end' => $exit_time_end . $exit_time_overflow,
            ];
        }

        return $formatted;
    }
    #add parking time in email end

    private function formatPermitServiceCriteria($services)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);

        $i = 0;
        foreach ($services as $service) {
            if (count($service->permit_service_criteria_mappings) > 0) {
                $formatted = [];
                foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
                    $item = $permit_service_criteria_mapping->criteria;
                    $days = explode(',', $item->days);
                    sort($days); // Sort days to match the sequence of $allDays
                    if ($days == $allDays) {
                        $dayNamesStr = 'All Days';
                    } else {
                        $dayNames = array_map(function ($day) use ($daysMap) {
                            return $daysMap[$day];
                        }, $days);
                        $dayNamesStr = implode(',', $dayNames);
                    }

                    $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
                    $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
                    $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

                    // Adjust exit time if it's greater than 24 hours
                    $exit_time = explode(":", $item->exit_time_end);
                    if ($exit_time[0] > 23) {
                        $next_hr = $exit_time[0] - 24;
                        $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                        $exit_time_overflow = ' (next day)';
                    } else {
                        $exit_time_overflow = '';
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                    }


                    $formatted[] = [
                        'days' => $dayNamesStr,
                        'entry_time_begin' => $entry_time_begin,
                        'entry_time_end' => $entry_time_end,
                        'exit_time_begin' => $exit_time_begin,
                        'exit_time_end' => $exit_time_end . $exit_time_overflow,
                    ];
                }
                $services[$i]->criteria = $formatted;
            }
            $i++;
        }

        return $services;
    }
}
