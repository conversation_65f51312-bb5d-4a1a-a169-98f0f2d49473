<?php

namespace App\Console\Commands\Cron;

use App\Http\Helpers\MailHelper;
use Storage;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\MapcoQrcodeCheckin;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\Ticket;
use App\Models\ParkEngage\Gate;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\BrandSetting;
use App\Http\Helpers\QueryBuilder;
use App\Models\ParkEngage\TicketExtend;

class SendEmailCheckin extends Command
{

  protected $signature = 'send:checkin-checkout-email {id}/{email}';

  protected $description = 'Send email to for Check-in, Checkout Details';

  protected $log;
  const PCI_PARTNER_ID = 2980;
  public function __construct(LoggerFactory $logFactory)
  {
    parent::__construct();
    $this->log = $logFactory->setPath('logs/mapco-reservation')->createLogger('mapco-reservation');
  }

  public function handle()
  {
    $id = $this->argument('id');
    $email = $this->argument('email');
    $this->log->info("Mail start");
    $data = Ticket::with(['userPass.transaction', 'reservation.transaction', 'transaction', 'facility', 'user'])->where('id', $id)->first();
    $data->parkingamount = $data->parking_amount;
    if (!$data->transaction) {
      if (isset($data->reservation)) {
        $response = AuthorizeNetTransaction::where('id', $data->reservation->anet_transaction_id)->first();
        // $data['payment_last_four']=$response->payment_last_four;
        // $data['card_type']=$response->card_type;
        if (isset($response) && !empty($response)) {
          $data['payment_last_four'] = $response->payment_last_four;
          $data['card_type'] = $response->card_type;
        } else {
          $data['payment_last_four'] = '';
          $data['card_type'] = '';
        }
      }
    }

    // $grandTotal = $data->grand_total;
    $total = $grandTotal = $taxFee = $discountAmount = $parkingAmount = $additional_fee = $surcharge_fee = 0;
    if ($data->is_extended == '1') {
      $ticketExtends = TicketExtend::with(['transaction'])->where('ticket_number', $data->ticket_number)->orderby('id', 'asc')->get();
      foreach ($ticketExtends as $key => $value) {
        $total                += ($value->total);
        $parkingAmount        += ($value->total - ($value->tax_fee + $value->processing_fee + $value->additional_fee));
        $discountAmount       += $value->discount_amount;
        $grandTotal           += $value->grand_total;
        $additional_fee       += $value->additional_fee;
        $surcharge_fee        += $value->surcharge_fee;
        $taxFee               += $value->tax_fee;
        $data->checkout_time  = $value->checkout_time;
        $this->log->info("Ticket Extend Data Found -- " . $grandTotal);
      }
      $data->total              += sprintf("%.2f", $total);
      $data->grand_total        += sprintf("%.2f", $grandTotal);
      $data->parking_amount     += sprintf("%.2f", $parkingAmount);
      $data->discount_amount    += sprintf("%.2f", $discountAmount);
      $data->tax_fee            += sprintf("%.2f", $taxFee);
      $data->additional_fee     += sprintf("%.2f", $additional_fee);
      $data->surcharge_fee      += sprintf("%.2f", $surcharge_fee);
      $data->ticket_extends     = $ticketExtends;
    }

    if(in_array($data->partner_id,config('parkengage.PARTNER_GROUP_DISCOUNT'))) 
        {
            $data->ticketadditionalinfo = $data->ticketadditionalinfo() ?? null;

            if(!empty($data->ticketadditionalinfo)) 
            {
                $parkingAmount = 0;
                if (!empty($ticketExtends) && $ticketExtends->count() > 0) 
                {
                    $parkingAmount = 0;
                   
                    foreach ($ticketExtends as $key => $exted) 
                    {
                        $parkingAmount += ($exted->new_parking_amount);
                    }
                   
                    $parkingAmount  = $data->parkingamount + $parkingAmount;
                    
                } 
                else 
                {
                    $parkingAmount  = $data->parking_amount;
                }
                $data->parking_amount = number_format($parkingAmount,2);

                $data->processing_fee = $data->ticketadditionalinfo->new_processing_fee;
                
                $data->tax_fee = $data->ticketadditionalinfo->new_tax_amount;

                $data->discount_amount = $data->ticketadditionalinfo->new_discount_amount;

                $total = $data->ticketadditionalinfo->new_processing_fee + $data->ticketadditionalinfo->new_tax_amount + $data->ticketadditionalinfo->new_parking_amount;
                $data->total = number_format($total,2);
                $data->grand_total =  number_format($total,2);
                
            }
        }

    $gates = Gate::where('facility_id', $data->facility_id)->get();
    if ($gates) {
      foreach ($gates as $key => $value) {
        if ($value->gate == $data->checkin_gate) {
          $data['checkin_gate_name'] = $value->gate_name;
        }
        if ($value->gate == $data->checkout_gate) {
          $data['checkout_gate_name'] = $value->gate_name;
        }
      }
    } else {
      $data['checkin_gate_name'] = '-';
      $data['checkout_gate_name'] = '-';
    }

    $data['email'] = $email;


    //dd($data->transaction);

    //user mail
    try {
      $data['checkin_time'] = date("d-m-Y, h:i A", strtotime($data->checkin_time));
      $data['checkout_time'] = date("d-m-Y, h:i A", strtotime($data->checkout_time));

      if ($data['reservation_id'] == null && $data['user_pass_id'] == null) {
        $data['drive'] = 1;
        $data['title'] = 'Drive-Up Details:';
        if ($data->is_checkin == '1' && $data->is_checkout == '0' &&  $data->anet_transaction_id == NULL) {
          $url = env('TOUCHLESS_WEB_URL');

          $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $data->facility->owner_id)->first();

          if ($checkPaymentUrl->user_id == self::PCI_PARTNER_ID) {
            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($data) {
              $join->on('user_facilities.user_id', '=', 'users.id');
              $join->where('user_facilities.facility_id', "=", $data->facility_id);
            })->where('created_by', $data->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
            $pay = ($getRM->slug) ? $getRM->slug : '';
          } else {
            $pay = ($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : 'pay';
          }



          $grace_period = $data->facility->grace_period_minute;
          $data['msg'] = "Use the following link to PAY and EXIT.";
          $ticket_number = base64_encode($data->ticket_number);
          if ($data->facility->is_gated_facility == '1') {
            $data['url'] = "$url/$pay/$ticket_number";
          } else {
            $url = env('RECEIPT_URL');
            $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $data->partner_id);
            if ($dynamicReceiptUrl) {
              $url = $dynamicReceiptUrl->value;
            }
            $data['url'] = "$url/$pay/ticket/$data->ticket_number";
          }
        } elseif ($data->is_checkin == '1' && $data->is_checkout == '1' || $data->anet_transaction_id != '') {

          $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $data->facility->owner_id)->first();
          $url = env('RECEIPT_URL');
          $name = isset($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : 'receipt';
          $data['url'] = "$url/$name/ticket/" . $data->ticket_number;
          $data['msg'] = "Please use the following link to download E-Receipt";
        }
      } else {
        $data['check'] = 1;
        $data['title'] = 'Check-In Details:';
      }

      if ($data['is_checkin'] == 0) {
        $data['title'] = 'Check-Out Details:';
      }
      // dd($data);
      $brand_setting = FacilityBrandSetting::where('facility_id', $data->facility_id)->first();
      if ($brand_setting) {
        $data->facility_logo_id = $brand_setting->id;
        $data->color = $brand_setting->color ?? "#4a90e2";
      } else {
        $settings = BrandSetting::where('user_id', $data->partner_id)->first();
        $data->logo_id = $settings->id;
        $data->color = $brand_setting->color ?? "#4a90e2";
      }

      $pdf = (new MapcoQrcodeCheckin())->generatePdf($data, Pdf::class);
      $pdfName = '';
      if (isset($data->transaction->description)) {
        $pdfName = $data['title'] . " - " . $data->ticket_number;
      } else {
        $pdfName = $data['title'] . " - " . $data->ticket_number;
      }

      if ($data->transaction) {
        if (isset($data->transaction->expiration)) {
          $str = $data->transaction->expiration;
          $insertstr = '/';
          $pos = 2;
          $data['expiration'] = substr($str, 0, $pos) . $insertstr . substr($str, $pos);
        } else {
          $str = $data->reservation->transaction->expiration;
          $insertstr = '/';
          $pos = 2;
          $data['expiration'] = substr($str, 0, $pos) . $insertstr . substr($str, $pos);
        }
      }

      $data['email'] = $email;

      try {
        $this->log->info("Email start to send : " . $data['email'] . " Partner Data " . $data->partner_id);
        // Change Request PIMS-12502 : Vijay - 30-01-2025 
        $subject = $data['title'] . " - " . $data->ticket_number;
        $filedata['type']       = 'runtime';
        $filedata['content']    = $pdf;
        $filedata['filename']   = $pdfName . ".pdf";
        $filedata['format']     = 'pdf';

        MailHelper::sendEmail($data['email'], 'atlanta-checkin-checkout.sendchkinchkout-email', ['subject' => $subject, 'data' => $data, 'brand_setting' => $brand_setting, 'filedata' => $filedata], $data->partner_id, 'runtime');
        $this->log->info("Mail sent to success : " . $data['email']);
      } catch (\Throwable $th) {

        $msg = "Error Message: " . $th->getMessage() . ", File: " . $th->getFile() . ", Line Number: " . $th->getLine();
        $this->log->error("Error in sending email {$msg} ");
        // dd($msg);
      }

      /* 
      Mail::send(
        "atlanta-checkin-checkout.sendchkinchkout-email",
        ['data' => $data],
        function ($message) use ($data, $pdf, $pdfName) {
          $message->to($data['email'])->subject($data['title'] . " - " . $data->ticket_number);
          $message->from(config('parkengage.default_sender_email'));
          $message->attachData($pdf, $pdfName . ".pdf");
        }
      );
    */
      $this->log->info("Mail sent to " . $data['email']);
    } catch (Exception $e) {
      $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
      $this->log->error($msg);
      $this->log->info("Queue ended");
    }
  }


  public function generateBarcodeJpgNew($qrcode)
  {
    $html = $this->generateBarcodeHtml($qrcode);

    $image = app()->make(Image::class);
    $image->setOption('width', '420');
    return $image->getOutputFromHtmlString($html);
  }

  public function generateBarcodeHtml($qrcode)
  {
    return view('platform.email.Qrcodegenerate', ['barcode' => $qrcode]);
  }
}
