<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use Twilio\Rest\Client;
use <PERSON><PERSON><PERSON>\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\BrandSetting;
use App\Models\User;
use App\Models\ParkEngage\UserPaymentGatewayDetail;

class TouchlessAtlantaPrepaidCheckin extends Command
{

    protected $signature = 'email:touchless-parking-atlanta-prepaid-confirm-checkin {id} {type}';

    protected $description = 'Send checkin confirmation email.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/parkengage/touchless-flow')->createLogger('checkin-checkout-email');
    }

    public function handle()
    {
        try{
       $id = $this->argument('id');
       $type = $this->argument('type');
       $this->log->info("Prepaid Mail start");
       if($type == 'checkin'){
            $data = Ticket::with(['facility','user','userPass','reservation','permit'])->where('id', $id)->first();
            //user mail
            // condition checked for spothero Ashutosh 29-09-2023
            $facility_brand_setting = FacilityBrandSetting::where('facility_id', $data->facility_id)->first();
            if($facility_brand_setting){
                $data->facility_logo_id = $facility_brand_setting->id;
            }else{
                $data->logo_id = BrandSetting::where('user_id', $data->partner_id)->first();
            }
            
            if(isset($data->user->email) && !empty($data->user->email)){
                Mail::send(
                    "atlanta-checkin-checkout.touchless-parking-prepaid-email-checkin", ['data' => $data], function ($message) use($data) {
                        $message->to($data->user->email)->subject("ParkEngage Touchless Prepaid Check-In Confirmation Details Ticket : ". $data->ticket_number);
                        $message->from(config('parkengage.default_sender_email'));
                    }
                );
            }
            
            $this->log->info("Mail sent to ". $data->user->email);

            if($data->permit_request_id != ''){
                return true;
            }
            
            $ticket_number = base64_encode($data->ticket_number);
            $facilityName =  ucwords($data->facility->full_name);
            $accountSid = env('TWILIO_ACCOUNT_SID');
            $authToken  = env('TWILIO_AUTH_TOKEN');
            $url = env('TOUCHLESS_APP_URL');
            $client = new Client($accountSid, $authToken);
            /*$encrypt = Crypt::encrypt(json_encode(['gate'=>'1', 'facility_id'=> $result->facility_id]));*/
             // condition checked for spothero Ashutosh 29-09-2023
             $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $data->facility->owner_id)->first();
            if ($checkPaymentUrl->user_id == config('parkengage.PARTNER_PCI')) {
                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($data) {
                    $join->on('user_facilities.user_id', '=', 'users.id');
                    $join->where('user_facilities.facility_id', "=", $data->facility->id);
                })->where('created_by', $data->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                $pay = ($getRM->slug) ? $getRM->slug : '';
            } else {
                $pay = ($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : 'pay';
            }
            $bookingID = '';
            $body = '';
            if(config('parkengage.PARTNER_AAA') == $data->partner_id){
                $body = "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number. For Check-Out, please scan the QR code at exit gate.";
            }else{
                $url = env('TOUCHLESS_WEB_URL');
                if(isset($data->reservation->id)){
                    $bookingID = $data->reservation->ticketech_code;
                    $body = "Thank you for Check-In with " . $facilityName . ". The ticket number against your booking $bookingID is $data->ticket_number. For Check-Out, please scan the QR code at exit gate.Use the following link to PAY and EXIT ".$url/$pay/$ticket_number;
                }else{
                    $grace_period = $data->facility->grace_period_minute;
                    $body = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $data->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$pay/$ticket_number";
                }
            }
            
            
            if(isset( $data->user->phone) && !empty($data->user->phone)){
                try
                {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                    // the number you'd like to send the message to
                        $data->user->phone,array(
                        // A Twilio phone number you purchased at twilio.com/console
                        'from' => env('TWILIO_PHONE'),
                        // the body of the text message you'd like to send
                        //'body' => "Fine"
                        /*'body' => "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number. Use the following link to Check-Out. $url/touchless-parking-atlanta-prepaid-checkout-details/$ticket_number"*/
                        'body' => $body
                    )
                );
                }catch (RestException $e)
                {
                    
                }
            }
            $this->log->info("SMS sent");
       }else{
            $data = OverstayTicket::with('facility')->where('id', $id)->first();
            $ticket = Ticket::where('ticket_number', $data->ticket_number)->first();
            $facility_brand_setting = FacilityBrandSetting::where('facility_id', $data->facility_id)->first();
            if($facility_brand_setting){
                $data->facility_logo_id = $facility_brand_setting->id;
            }else{
                $data->logo_id = BrandSetting::where('user_id', $data->partner_id)->first();
            }
            //user mail
            if(isset($data->user->email) && !empty($data->user->email)){
                Mail::send(
                    "atlanta-checkin-checkout.touchless-parking-prepaid-overstay-email-payment", ['data' => $data,'ticket' => $ticket], function ($message) use($data) {
                        $message->to($data->user->email)->subject("ParkEngage Touchless Overstay Booking Details Ticket : ". $data->ticket_number);
                        $message->from(config('parkengage.default_sender_email'));
                    }
                );
            }
       }

    }catch(Exception $e) {
        $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
        $this->log->error($msg);
        $this->log->info("Queue ended");            
    }
       
       
    }
    
}

