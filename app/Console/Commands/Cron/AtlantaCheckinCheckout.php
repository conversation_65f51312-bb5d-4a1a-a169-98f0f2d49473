<?php

namespace App\Console\Commands\Cron;

use Storage;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\ParkEngage\MapcoQrcode;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Services\Image;
use App\Models\Ticket;

class AtlantaCheckinCheckout extends Command
{

    protected $signature = 'atlanta:checkin-checkout';

    protected $description = 'Update Ticket table is_checkout wehan estimated_checkout time expire';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/cron-atlanta-checkin-checkout')->createLogger('atlanta-checkin-checkout');
    }

    public function handle()
    {
        
          
            $facility_id = 30;  
            
            $this->log->info("checkout start");
        
         

            try{
                $today = date("Y-m-d");
                $ticketData = Ticket::whereDate('checkout_datetime','<',$today)->where('facility_id',$facility_id)->where('is_checkout','0')->orderby('id','DESC')->get();
                
                $ticketCountBeforeCheckout = count($ticketData);
                $ticketCountAfterCheckout = 0;

                $driveupTicketData = Ticket::where('facility_id',$facility_id)->where('is_checkout','0')->whereDate('check_in_datetime','<',$today)->where('vp_device_checkin','1')->orderby('id','DESC')->get();
                $ticketCountBeforeCheckout += count($driveupTicketData);
                $data = $ticketCountBeforeCheckout;

                $this->log->info("Before checkout email about to send");
                //user mail
            Mail::send(
                "atlanta-checkin-checkout.alert-before-checkout", ['data' => $data], function ($message) use($data) {
                    $message->to(['<EMAIL>','<EMAIL>'])->subject("AAA Cron Alert before Checkout");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Before checkout email sent");
                if(count($ticketData)){
                foreach($ticketData as $val)
                {
                    if($val->is_checkout!=1 || $val->is_checkout!='1')
                    {
                        $gates = \DB::table('gates')
                            ->select('gate')
                            ->where('facility_id', $facility_id)
                            ->where('gate_type', 'exit')
                            ->first();
                        $updateRecord =[

                            'is_checkout' => '1',
                            'checkout_time' => date('Y-m-d h:i:s'),
                            'remark' => 'checkout by cron',
                            "checkout_gate" => $gates->gate
                        ];

                            $status=Ticket::where('id',$val->id)->update($updateRecord);
                            $this->log->info("Checkout update for Ticket No ".$val->ticket_number);
                            $ticketCountAfterCheckout ++;
                    } 
                }
            }
                
                if(count($driveupTicketData) > 0){
                    foreach($driveupTicketData as $key=>$value){
                        $date1 = $value->check_in_datetime;
                        $date2 = date('Y-m-d H:i:s');
                        $timestamp1 = strtotime($date1);
                        $timestamp2 = strtotime($date2);
                        $hour = abs($timestamp2 - $timestamp1)/(60*60); 
                        if($hour > 24){
                            $gates = \DB::table('gates')
                            ->select('gate')
                            ->where('facility_id', $facility_id)
                            ->where('gate_type', 'exit')
                            ->first();
                            $updateRecord =[
                            'is_checkout' => '1',
                            'checkout_time' => date('Y-m-d h:i:s'),
                            'remark' => 'checkout by cron',
                            "checkout_gate" => $gates->gate
                            ];
                            $status=Ticket::where('id',$value->id)->update($updateRecord);
                            $this->log->info("Checkout update for Ticket No ".$value->ticket_number);
                            $ticketCountAfterCheckout ++;
                        }
                    }
                }
                if($ticketCountAfterCheckout > 0){
                    $this->log->info("After checkout email about to send");
                    Mail::send(
                        "atlanta-checkin-checkout.alert-after-checkout", ['data' => $ticketCountAfterCheckout], function ($message) use($ticketCountAfterCheckout) {
                            $message->to(['<EMAIL>','<EMAIL>'])->subject("AAA Cron Alert After Checkout");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("After checkout email sent");
                }
                
            } catch(Exception $e) {
                $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                $this->log->error($msg);
                $this->log->info("Queue ended");            
            }
       
    }

   



}