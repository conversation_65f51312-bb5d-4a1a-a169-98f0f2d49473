<?php

namespace App\Console\Commands\Cron;

use Exception;
use File;

use Illuminate\Console\Command;

// The snappy library we use to generate jpgs and pdfs is leaving temp files
// on the server. This cron job will clean them up.
class CleanTempFiles extends Command
{

    protected $signature = 'cron:clean-temp-files';

    protected $description = "Cleans temp files left over by the knp/snappy library being used to generate .jpg and .pdf files.";

    public static $patternsToDelete = [
        "knp_snappy*.jpg",
        "knp_snappy*.html"
    ];

    public function handle()
    {
        $tempDir = config('snappy.temp_dir');

        if ($tempDir === '/') {
            throw new Exception("You cannot use the root directory as a tmp dir.");
        }

        foreach (self::$patternsToDelete as $pattern) {
            collect(glob($tempDir . '/' . $pattern))->each(
                function ($file) {
                    File::delete($file);
                }
            );
        }
    }
}
