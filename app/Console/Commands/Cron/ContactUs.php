<?php

namespace App\Console\Commands\Cron;

use Mail;
use Exception;
use File;
use App\Models\ParkEngage\ContactUs as CU;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;


class ContactUs extends Command
{

    protected $signature = 'email:contact-us {id}';

    protected $description = 'Send contact us email.';

    public function handle()
    {
       $id = $this->argument('id');
       $data = CU::where('id', $id)->first();
       
        //admin mail
        Mail::send(
            "parkengage.contact_us", ['data' => $data], function ($message) {
                $message->to(config('parkengage.info_sender_email'))->subject("New contact us query");
                $message->from(config('parkengage.default_sender_email'));
            }
        );

        //user mail
        Mail::send(
            "parkengage.contact_us_user", ['data' => $data], function ($message) use($data) {
                $message->to($data->email)->subject("Thank you for contact us");
                $message->from(config('parkengage.default_sender_email'));
            }
        );
       
    }
    
}

