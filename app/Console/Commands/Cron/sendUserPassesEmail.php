<?php

namespace App\Console\Commands\Cron;

use Illuminate\Console\Command;
use Excel;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use PHPExcel_Worksheet_Drawing;
use Mail;

class sendUserPassesEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send:user-passes-email {pass}/{email}/{request}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send excel sheet of passes list in the email';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        set_time_limit(0);

        $passes = $this->argument('pass') ?? [];
        $emails = $this->argument('email') ?? [];
        $request = $this->argument('request') ?? [];

        $partnerId = $request['partner_id'] ?? null;
        $facilityId = $request['facility_id'] ?? null;

        $locationName = 'All';
        $garageCode = 'All';
        $currentDate = date('m-d-Y');

        $fromDate = isset($request['from_date'])
            ? date('Y-m-d', strtotime($request['from_date']))
            : date('Y-m-d');
        $toDate = isset($request['to_date'])
            ? date('Y-m-d', strtotime($request['to_date']))
            : date('Y-m-d');

        // $excelSheetName = ucwords(str_replace(' ', '', 'User Passes List'));
        $startDay = $this->getOrdinalSuffix(date('j', strtotime($fromDate))); // Day with ordinal suffix
        $endDay =  $this->getOrdinalSuffix(date('j', strtotime($toDate)));


        // $excelSheetName = ucwords('Passes Summary -') . date("j M, Y", strtotime($fromDate)) . '-' . date("j M, Y", strtotime($toDate));
        // Generate Excel sheet name with ordinal suffix for start and end days
        $excelSheetName = 'Passes Summary - (' . $startDay . ' ' . date('M, Y', strtotime($fromDate)) . ' - ' . $endDay . ' ' . date('M, Y', strtotime($toDate)) . ')';
        //dd($excelSheetName);
        // Prepare data for Excel
        $columns = [];
        //dd($passes);
        foreach ($passes as $pass) {
            $columns[] = [
                'Pass Id' => $pass['pass_code'] ?? '-',
                'License Plate' => isset($pass['license_plate']) ? $pass['license_plate'] : '-',
                'User Name' => isset($pass['user']['name']) ? $pass['user']['name'] : '-',
                'Email ID' => $pass['email'] ?? '-',
                'Total Days' => $pass['total_days'] ?? '-',
                'Consumed' => isset($pass['consume_days']) ? $pass['consume_days'] : '-',
                'Card No (Last 4 Digits)' => isset($pass['payment_last_four']) ? $pass['payment_last_four'] : '-',
                'Card Type' => isset($pass['card_type']) ? $pass['card_type'] : '-',
                'Card Expiry' => isset($pass['expiration']) ? $pass['expiration'] : '-',
                'Amount' => isset($pass['total']) ? $pass['total'] : '-',
                'Processing Fee' => isset($pass['processing_fee']) ? $pass['processing_fee'] : '-',
                'Discount' => isset($pass['discount_amount']) ? $pass['discount_amount'] : '-',
                'PromoCode' => isset($pass['promocode']) ? $pass['promocode'] : '-',
                'Refund Amount' => isset($pass['refund_amount']) ? $pass['refund_amount'] : '-',
                'Facility Name' => isset($pass['facility']['full_name']) ? $pass['facility']['full_name'] : '-',
                'Purchased Date' => isset($pass['purchased_on']) ? $pass['purchased_on'] : '-',
                'Expiry Date' => isset($pass['end_time']) ? $pass['end_time'] : '-',
                'Refund Date' => isset($pass['refund_date']) ? $pass['refund_date'] : '-',
                'Refund Remarks' => isset($pass['refund_remarks']) ? $pass['refund_remarks'] : '-',
            ];
        }
        // dd($columns);
        $getLogoId = 0;
        $color = "#0C4A7A";

        if ($partnerId) {
            $brandSetting = BrandSetting::where('user_id', $partnerId)->first();
            if ($brandSetting) {
                $getLogoId = $brandSetting->id;
                $color = $brandSetting->color;
            }

            if ($facilityId) {
                $facility = Facility::find($facilityId);
                if ($facility) {
                    $locationName = $facility->full_name;
                    $garageCode = $facility->garage_code;
                }
            } else {
                $facility = Facility::where('owner_id', $partnerId)->first();
                if ($facility) {
                    $locationName = $facility->full_name;
                    $garageCode = $facility->garage_code;
                }
            }
        }
        $updatedValues = [
            'totalTickets' => 0,
            'totalRevenue' => 0.0
        ];
        // Generate and store Excel
        Excel::create($excelSheetName, function ($excel) use ($columns, $excelSheetName, $brandSetting, $color, $locationName, $garageCode, $fromDate, $toDate, &$updatedValues) {
            $excel->setTitle($excelSheetName);
            $excel->setCreator('UserPassesList')->setCompany('ParkEngage');

            if (empty($columns)) {
                throw new ApiGenericException('Sorry! No Data Found.');
            }

            $excel->sheet('Permit List', function ($sheet) use ($columns, $brandSetting, $color, $fromDate, $toDate, $locationName, $garageCode, &$updatedValues) {
                $this->addLogoInExcelHeader($sheet, 'User Pass List', $brandSetting, $color);
                $headerSpace = 4;
                $this->getBrandHeaderSection($sheet, $color, $fromDate, $toDate, $locationName, $garageCode);
                $sheet->mergeCells('A3:S3');
                $sheet->setCellValue('A3', 'User Pass List');
                $sheet->cell('A3:S3', function ($cell) use ($color) {
                    $cell->setAlignment('center'); // Center horizontally
                    $cell->setValignment('center');
                    $cell->setFontWeight('bold');
                    $cell->setBackground('#D9E1F2');
                    $cell->setFontColor('#272829');
                    $cell->setFontSize('12');
                });
                // Style Header Row
                $sheet->cell("A$headerSpace:S$headerSpace", function ($row) use ($color) {
                    $row->setBackground($color);
                    $row->setFontColor('#ffffff');
                });

                // Add data to sheet
                $sheet->fromArray($columns, null, 'A' . "$headerSpace", false, true);
                $j = count($columns) + 4;
                $i = 4;
                $getTotal = 0;
                foreach ($columns as $Key => $value) {
                    $getTotal +=  $value['Amount'];
                    $sheet->cell('A' . $i . ':S' . $i, function ($cell) use ($color) {
                        $cell->setAlignment('center'); // Center horizontally
                        $cell->setValignment('center'); // Center vertically
                    });
                    $i++;
                }
                //$i += 1;
                $sheet->cell('A' . $i . ':S' . $i, function ($cell) use ($color) {
                    $cell->setAlignment('center'); // Center horizontally
                    $cell->setValignment('center'); // Center vertically
                });
                $updatedValues['totalTickets'] = count($columns);
                $updatedValues['totalRevenue'] = round($getTotal, 2);
                // for ($i = 4; $i <= $j; $i++) {
                //   $sheet->cell('A' . $i . ':S' . $i, function ($cell) use ($color) {
                //     $cell->setAlignment('center'); // Center horizontally
                //     $cell->setValignment('center'); // Center vertically
                //   });
                // }
            });
        })->store('xls');

        // Send emails
        foreach ($emails as $email) {
            Mail::send(
                [],
                [],
                function ($message) use ($excelSheetName, $email, $fromDate, $toDate, &$updatedValues) {
                    $dateRange =  date("d F, Y", strtotime($fromDate)) . '-' . date("d F, Y", strtotime($toDate));

                    $subject = 'Passes Summary - ' . $dateRange;
                    $message->to($email)->subject($subject);
                    $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                    $htmlBody = "
                    <p>Hi Team,</p>
                    <p>PFB summary of checkin Checkout Ticket Report. A detailed report is also attached.</p>
                    <table style='width: 50%; border-collapse: collapse; font-size: 12px; text-align: left;'>
                       
                        <tbody>
                             <tr>
                                 <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'> No of Passes</td>
                                 <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'>Revenue ($)</td>
                             </tr>
        
                            <tr>
                                <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'>{$updatedValues['totalTickets']}</td>
                                <td style='border: 1px solid #dddddd; padding: 8px; text-align: center;'> {$updatedValues['totalRevenue']}</td>
                            </tr>
        
                        </tbody>
                    </table>
                    <p>Thanks,<br>Team Parkengage</p>";


                    $message->setBody($htmlBody, 'text/html');
                    $filePath = storage_path('exports/' . $excelSheetName . '.xls');
                    if (file_exists($filePath)) {
                        $message->attach($filePath);
                    }
                }
            );
        }
    }
    public function addLogoInExcelHeader($excel, $title = "Pass List", $brandSetting, $color = '#191D88')
    {
        /* Code for Logo */
        $logoPath = public_path('assets/media/images/breeze.png');

        // Check if a brand logo is provided
        if (isset($brandSetting) && !empty($brandSetting->logo)) {
            $dynamicLogoPath = storage_path('app/brand-settings/' . $brandSetting->logo);

            if (file_exists($dynamicLogoPath)) {
                $logoPath = $dynamicLogoPath;
            } else {
                logger()->warning("Brand logo not found at: {$dynamicLogoPath}. Using default logo.");
            }
        } else {
            logger()->info("Brand logo is not set. Using default logo.");
        }

        // Set the logo in Excel
        $drawing = new PHPExcel_Worksheet_Drawing();
        $drawing->setPath($logoPath);
        $drawing->setCoordinates('A1');
        $drawing->setWidth(150);
        $drawing->setHeight(50);
        $drawing->setOffsetX(25);
        $drawing->setOffsetY(10);
        $excel->getDrawingCollection()->append($drawing);

        /* End Code for Logo */

        // Header Title Section
        // $excel->mergeCells('D1:Q1'); // Merge cells for title
        // $excel->getRowDimension(1)->setRowHeight(60);
        // $excel->setCellValue('A1', $title); // Set the header title

        // Apply styling to header
        $excel->cell('A1', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            $cell->setFontSize(30);
            $cell->setFontColor('#ffffff');
            $cell->setBackground('#ffffff'); // Set background color
        });
    }

    public function getBrandHeaderSection($excel, $color, $fromDate, $toDate, $locationName, $garageCode)
    {
        $excel->mergeCells('A2:F2');
        $cellValue = "Report Date Range - " . date('m-d-Y', strtotime($fromDate)) . ' - ' . date('m-d-Y', strtotime($toDate));
        $cellValue .= "\nPrint Date - " . date('m-d-Y', strtotime('now'));
        $excel->setCellValue('A2', $cellValue);
        $excel->getStyle('A2')->getAlignment()->setWrapText(true);
        $excel->getRowDimension(2)->setRowHeight(80);

        $excel->cell('A2:Q2', function ($cell) use ($color) {
            $cell->setAlignment('center');
            $cell->setValignment('center');
            $cell->setFontWeight('bold');
            $cell->setBackground($color);
            $cell->setFontColor('#ffffff');
            $cell->setFontSize(18);
        });

        // Add location name
        $location  = "Location Name \n"  .  $locationName;
        $excel->mergeCells('G2:N2');
        $excel->setCellValue('G2', $location);
        $excel->getStyle('G2')->getAlignment()->setWrapText(true);

        $excel->cell('G2:N2', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            $cell->setBackground($color);
            $cell->setFontColor('#ffffff');
        });

        // Add garage code
        $locationId = "Location ID \n" . $garageCode;
        $excel->mergeCells('O2:S2');
        $excel->setCellValue('O2', $locationId);
        $excel->getStyle('O2')->getAlignment()->setWrapText(true);

        $excel->cell('O2:S2', function ($cell) use ($color) {
            $cell->setAlignment('center');
            $cell->setValignment('center');
        });

        // Main title row
        $excel->mergeCells('A1:S1');
        $excel->getRowDimension(1)->setRowHeight(60);
        $excel->setCellValue('A1', "User Pass List");
        $excel->cell('A1:S1', function ($cell) use ($color) {
            $cell->setAlignment('center');
            $cell->setValignment('center');
            $cell->setFontWeight('bold');
            $cell->setBackground($color);
            $cell->setFontColor('#ffffff');
            $cell->setFontSize(30);
        });
    }

    function getOrdinalSuffix($day)
    {
        if (in_array($day % 10, [1, 2, 3]) && !in_array($day % 100, [11, 12, 13])) {
            $suffix = ['st', 'nd', 'rd'][$day % 10 - 1];
        } else {
            $suffix = 'th';
        }
        return $day . $suffix;
    }
}
