<?php

namespace App\Console\Commands\Cron;

use Mail;
use File;
use Exception;
use Carbon\Carbon;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use App\Models\Promotion;
use App\Models\PromoCode;
use App\Services\LoggerFactory;

class PromotionExpired extends Command
{

    protected $signature = 'promotion-expired';

    protected $description = 'Expire promotion plan.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
    
        $this->log = $logFactory->setPath('logs/parkengage')->createLogger('promotion_expired');
    }
    public function handle()
    {
      $start_date = date("Y-m-d"); 
      $promotions = Promotion::whereDate('valid_to' , '<', $start_date)->where('status', '1')->get();
       
      $ids = '';
      if($promotions){
        foreach ($promotions as $key => $value) {

          $ids .= $value->id .',';
          $value->status = '0';
          $value->save();
          //update promocode
          PromoCode::where('promotion_id', '=', $value->id)->update(array('status' => '0'));
        }
      }
      $this->log->info("Promotion expired ids -- $ids ");
      echo ($ids);
        
        
       
    }
    
}
