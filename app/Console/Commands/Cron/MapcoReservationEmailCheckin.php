<?php

namespace App\Console\Commands\Cron;

use Storage;
use Mail;
use Exception;
use File;
use App\Models\Reservation;
use App\Models\User;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\MapcoQrcodeCheckin;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\Ticket;
use App\Models\ParkEngage\Gate;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;


class MapcoReservationEmailCheckin extends Command
{

  protected $signature = 'mapco:reservation-email-checkin {id}/{email}';

  protected $description = 'Send email to for Check-in, Checkout Details';

  protected $log;

  public function __construct(LoggerFactory $logFactory)
  {
    parent::__construct();
    $this->log = $logFactory->setPath('logs/mapco-reservation')->createLogger('mapco-reservation');
  }

  public function handle()
  {
    try {

      $id = $this->argument('id');
      $email = $this->argument('email');

      // $type = $this->argument('type'); 
      $this->log->info("Mail start");
      $data = Ticket::with(['userPass.transaction', 'reservation.transaction', 'transaction', 'facility', 'user'])->where('id', $id)->first();

      //return $data;
      //dd($data);

      $gates = Gate::where('facility_id', $data->facility_id)->get();
      if ($gates) {
        foreach ($gates as $key => $value) {
          if ($value->gate == $data->checkin_gate) {
            $data['checkin_gate_name'] = $value->gate_name;
          }
          if ($value->gate == $data->checkout_gate) {
            $data['checkout_gate_name'] = $value->gate_name;
          }
        }
      } else {
        $data['checkin_gate_name'] = '';
        $data['checkout_gate_name'] = '';
      }

      $data['email'] = $email;

      // Code Added by Lokesh
      $this->log->info("===============Reached 0 =====================");
      if (isset($data->facility->is_service_update) && $data->facility->is_service_update == 1) {
        $this->log->info("===============Reached 1=====================");
        $fac_brand_setting = FacilityBrandSetting::where('facility_id', $data->facility->id)->first();
        if ($fac_brand_setting) {
          $this->log->info("===============Reached 2 =====================");
          $data->facility_logo_id = $fac_brand_setting->id;
        } else {
          $this->log->info("===============Reached 3 =====================");
          $brand_setting = BrandSetting::where('user_id', $data->partner_id)->first();
          $data->logo_id = $brand_setting->id;
        }
      }

      $this->log->info("===============Reached 4 =====================");



      //user mail

      $data['checkin_time'] = date("d-m-Y, h:i A", strtotime($data->checkin_time));
      $data['checkout_time'] = date("d-m-Y, h:i A", strtotime($data->checkout_time));

      if ($data['reservation_id'] == null && $data['user_pass_id'] == null) {
        $data['drive'] = 1;
        $data['title'] = 'Drive-Up Details:';
      } else {
        $data['check'] = 1;
        $data['title'] = 'Check-In Details:';
      }
      if ($data['is_checkin'] == 0) {
        $data['title'] = 'Check-Out Details:';
      }

      $pdf = (new MapcoQrcodeCheckin())->generatePdf($data, Pdf::class);

      $pdfName = '';
      if (isset($data->transaction->description)) {
        $pdfName = $data['title'] . " - " . $data->ticket_number;
      } else {
        $pdfName = $data['title'] . " - " . $data->ticket_number;
      }

      if ($data->transaction) {
        if (isset($data->transaction->expiration)) {
          $str = $data->transaction->expiration;
          $insertstr = '/';
          $pos = 2;
          $data['expiration'] = substr($str, 0, $pos) . $insertstr . substr($str, $pos);
        } else {
          $str = $data->reservation->transaction->expiration;
          $insertstr = '/';
          $pos = 2;
          $data['expiration'] = substr($str, 0, $pos) . $insertstr . substr($str, $pos);
        }
      }



      $data['email'] = $email;
      Mail::send(
        "mapco.chkinchkoutdetails-email",
        ['data' => $data],
        function ($message) use ($data, $pdf, $pdfName) {
          $message->to($data['email'])->subject($data['title'] . " - " . $data->ticket_number);
          $message->from(config('parkengage.default_sender_email'));
          $message->attachData($pdf, $pdfName . ".pdf");
        }
      );


      $this->log->info("Mail sent to " . $data['email']);
    } catch (Exception $e) {
      $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
      $this->log->error($msg);
      $this->log->info("Queue ended");
    }
  }

  public function generateBarcodeJpgNew($qrcode)
  {
    $html = $this->generateBarcodeHtml($qrcode);

    $image = app()->make(Image::class);
    $image->setOption('width', '420');
    return $image->getOutputFromHtmlString($html);
  }

  public function generateBarcodeHtml($qrcode)
  {
    return view('mapco.Qrcodegenerate', ['barcode' => $qrcode]);
  }
}
