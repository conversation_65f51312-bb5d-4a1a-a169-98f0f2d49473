<?php

namespace App\Console\Commands\Pave;

use Illuminate\Support\Facades\Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Http\Helpers\MailHelper;
use View;
use App\Services\LoggerFactory;
use Twilio\Exceptions\RestException;
use Illuminate\Support\Collection;
use Storage;
use Illuminate\Support\Facades\DB;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\PasswordReset;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use Carbon\Carbon;
use App\Http\Helpers\QueryBuilder;

/**
 * Emails reservation stub to user
 */
class SubordinateAccountEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subordinate-account-create:email {id}/{password}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Invitation Email to Subordinate Membership Email.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/pave/subordinate')->createLogger('subordinate');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $user_id = $this->argument('id');
        $userDetails = User::whereNull('deleted_at')->where('id', $user_id)->first();
        if (!$userDetails) {
            $this->log->info("No user with that ID.");
        }

        $brand_setting = '';
        $facility = DB::table('user_facilities')->where('user_id', $userDetails->user_parent_id)->whereNull('deleted_at')->pluck('facility_id');

        $facility_brand_setting = FacilityBrandSetting::whereIn('facility_id', $facility)->first();

        if ($facility_brand_setting) {
            $userDetails->facility_logo_id = $facility_brand_setting->id;
            $rgb_color = json_decode($facility_brand_setting->rgb_color, true);
            $userDetails->background_color = 'rgba(' . $rgb_color['r'] . ',' . $rgb_color['g'] . ',' . $rgb_color['b'] . ',' . $rgb_color['a'] . ')';
        } else {
            $brand_setting = BrandSetting::where('user_id', $userDetails->created_by)->first();

            if ($brand_setting) {
                $userDetails->logo_id = $brand_setting->id;
                $rgb_color = json_decode($brand_setting->rgb_color, true);

                $userDetails->background_color = 'rgba(' . $rgb_color['r'] . ',' . $rgb_color['g'] . ',' . $rgb_color['b'] . ',' . $rgb_color['a'] . ')';
            } else {
            }
        }

        $secret = OauthClient::where('partner_id', $userDetails->created_by)->first();
        // Password Change
        // Generate password reset token
        $reset = PasswordReset::firstOrNew(['email' => $userDetails->email]);
        $reset->generatePasswordResetToken();
        $reset->created_at = Carbon::now()->toDateTimeString();
        $reset->save();

        $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("WEB_URL", $userDetails->created_by);

        if ($dynamicReceiptUrl) {
            $url = $dynamicReceiptUrl->value;
            $reset_password_url = $url . "/generate-new-password/";
        } else {
            $reset_password_url = env('WEB_URL') . "/generate-new-password/";
        }

        $userDetails->password = $reset_password_url . $reset->token . "?is_create=1";


        if ($dynamicReceiptUrl) {
            $url = $dynamicReceiptUrl->value;
            $userDetails->loginurl = $url;
        } else {
            $userDetails->loginurl = env('WEB_ADMIN_URL');
        }

        $view = 'subordinate.email';
        $view_text = $view . '-plain-text';
        if (!View::exists($view_text)) {
            $view_text = $view;
        }
        if ($userDetails->created_by == config('parkengage.PARTNER_PAVE')) {
            $subject = "Welcome to Parking Payments";
        } else {
            $subject = "Welcome to ParkEngage";
        }

        // PIMS-12502 : Vijay - 30-01-2025
        if (isset($userDetails->PartnerName->company_name)) {
            $subject = "Welcome to " . ucfirst($userDetails->PartnerName->company_name);
        } else {
            $subject = "Welcome to ParkEngage";
        }

        try {

            $this->log->info("Subordinate user email sending: {$userDetails->email} Created by : {$userDetails->created_by} ");

            // Change Request PIMS-12502 : Vijay - 30-01-2025 
            $subject = $subject;
            MailHelper::sendEmail($userDetails->email, ['text' => $view_text, 'html' => $view], ['subject' => $subject, 'data' => $userDetails, 'brand_setting' => $brand_setting], $userDetails->created_by);
            // Change Close : PIMS-12502
            $this->log->info("email sent: $userDetails->email");

            /* 
             Mail::send(
                ['text'=>$view_text,'html'=>$view],
                ['data' => $userDetails],
                function ($message) use ($userDetails, $subject) {
                    $message->to($userDetails->email)->subject($subject);
                    $message->from(config('parkengage.default_sender_email'));
                }
             );             
            */
        } catch (\Exception $e) {
            $errorMessage = array();
            $errorMessage['message'] = $e;
            $this->log->error('Issue in email sending:', $errorMessage);
        }
    }
}
