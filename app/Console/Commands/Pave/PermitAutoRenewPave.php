<?php

namespace App\Console\Commands\Pave;

use Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\PermitRate;
use App\Classes\DatacapPaymentGateway;
use App\Models\PermitRequestRenewHistory;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Http\Helpers\QueryBuilder; #pims-14610

/**
 * Emails reservation stub to user
 */
class PermitAutoRenewPave extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pave:permit-renew';
    protected $log;
 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly permit renew.';

	const PARTNER_ID = '3307';
	//const FACILITY_ID = '430'; 
    // Prod 161
	
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/pave/')->createLogger('autorenew');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try{ 
            $permit_end_date = date('Y-m-d', strtotime('last day of previous month'));
            $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));
            
            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');
            //->where('facility_id',self::FACILITY_ID)
            $monthlyRequest = PermitRequest::with(['facility.FacilityPaymentDetails','user','PermitVehicle'])->where('partner_id',self::PARTNER_ID)->whereDate('desired_end_date', '<=', $permit_end_date)->whereDate('desired_start_date', '>=', $permit_start_date)->whereNull('cancelled_at')->whereNull('deleted_at')->where('user_consent',1)->whereNotNull('anet_transaction_id')->get();
		    //dd($permit_start_date,$permit_end_date,$monthlyRequest,self::PARTNER_ID,self::FACILITY_ID);
            if (!$monthlyRequest) {
                //throw new NotFoundException('Monthly Request Not Found.');
                $this->log->info("Monthly Request Not Found.");
            }
		    $this->log->info("Permit Found" . json_encode($monthlyRequest));
            // Create Permit Request History
           // $this->createPermitRequestHistory($monthlyRequest);                     
		    $count = 0;
		    foreach($monthlyRequest as $key=>$val){
                //dd($val->permit_rate_id);
                $permitRate = PermitRate::where('id',$val->permit_rate_id)->first();    
                //dd($permitRate);
                $this->log->info("Permit Rate Found" . json_encode($permitRate));
                if($permitRate->rate=='0.00'){
                    $maxDays=date('t');
                    $val->no_of_days          = $maxDays;    
                    $val->permit_rate         = $permitRate->rate; 
                    $val->desired_start_date  = $desired_start_date;
                    $val->desired_end_date    = $desired_end_date;
                    $val->grace_end_date        = QueryBuilder::fetchPermitGraceEndDate($val); #pims-14610
                    $val->save();
                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
					if($facility_brand_setting){
						$val->facility_logo_id = $facility_brand_setting->id;
					}else{
						$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
						$val->logo_id  = $brand_setting->id;
					}
					$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
					Mail::send(
                        "pave.permit-renew", ['data' => $val, 'brand_setting' => $brand_setting], function ($message) use($val) {
                            $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Mail sent to ". $val->user->email);                   
                }else if($permitRate->rate >"0.00"){
                    if($permitRate){
                        $amount = ($val->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $permitRate->rate;
                        $ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $val->facility->FacilityPaymentDetails->datacap_script_url;
                        
                        $paymentProfile = DatacapPaymentProfile::where('user_id',$val->user_id)->first();
                        $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                        //dd($amount,$ecommerce_mid,$url,$paymentProfile);
                        if($paymentProfile){
                            $data['Token'] = $paymentProfile->token;
                            if ($amount > 0) {
                                //$amount = number_format($amount, 2);
                                $data['Amount'] = $amount;
                                $data['Token'] = $paymentProfile->token;
                                $data['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                $data["CardHolderID"] = "Allow_V2";
                                $this->log->info("Payment Request Data --" . json_encode($data)."--". json_encode($ecommerce_mid). "--" . json_encode($url));
                                $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                if($paymentResponse["Status"] =="Error"){
                                    $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                    if($paymentResponse["Message"] =="Open Testing Account Number Not Found for this Merchant ID"){
                                        $this->log->info("Invalid payment information. Please verify and try again or use another card ." . json_encode($paymentResponse));
                                    }else{
                                        $this->log->info("Invalid payment information. Please verify and try again or use another card." . json_encode($paymentResponse));
                                    }
                                }else if($paymentResponse["Status"] == "Declined"){
                                    // Failure mail send to user
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if($expiry){
                                        $expiry_data = str_split($expiry, 2);
                                        //dd($expiry,$expiry_data);
                                        $val->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                    }else{
                                        $val->card_expiry = "-";
                                    }
									$facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
									if($facility_brand_setting){
										$val->facility_logo_id = $facility_brand_setting->id;
									}else{
										$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
										$val->logo_id  = $brand_setting->id;
									}
									$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                
                                    Mail::send(
                                        "pave.permit-renew-fail", ['data' => $val, 'brand_setting' => $brand_setting], function ($message) use($val) {
                                            $message->to([$val->user->email,config('parkengage.WAILUKU.USER_1')])->subject("Your Permit #". $val->account_number ."  could not be renewed due to Payment Failure");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to ". $val->user->email);   
                                }
                                if ($paymentResponse['Status'] == 'Approved') {
                                    QueryBuilder::createPermitRequestHistoryNew($val);
                                    $this->log->info("Payment Transaction Data Datacap Payment -- " . json_encode($paymentResponse));
                                    $request = new Request([
                                        'total'   => $amount,
                                        'card_last_four' => $paymentProfile->card_last_four,
                                        'expiration' => $paymentProfile->expiry
                                    ]);
                                    $this->log->info("Save Transaction Data Request --" . json_encode($request)."--".json_encode($val->user_id));
                                    $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request,$paymentResponse, $val->user_id,'');
                                    $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($authorized_anet_transaction));  
                                    $maxDays=date('t');
                                    $val->no_of_days          = $maxDays;    
                                    $val->anet_transaction_id = $authorized_anet_transaction->id;
                                    $val->permit_rate         = $permitRate->rate; 
                                    $val->desired_start_date  = $desired_start_date;
                                    $val->desired_end_date    = $desired_end_date;
                                    $val->grace_end_date        = QueryBuilder::fetchPermitGraceEndDate($val); #pims-14610
                                    $val->save();
                                    //user mail
                                        //dd($paymentProfile);
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if($expiry){
                                        $expiry_data = str_split($expiry, 2);
                                        //dd($expiry,$expiry_data);
                                        $val->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                    }else{
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
									if($facility_brand_setting){
										$val->facility_logo_id = $facility_brand_setting->id;
									}else{
										$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
										$val->logo_id  = $brand_setting->id;
									}
									$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                
                                    Mail::send(
                                        "pave.permit-renew", ['data' => $val, 'brand_setting' => $brand_setting], function ($message) use($val) {
                                            $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to ". $val->user->email);                       
                            
                                }
                            }
                        }
                    }
                }
                else{
                    $this->log->info("Permit Rate Not Found");
                }
			    $count++;				
		    }
			$msg = "Total Count of Permit Renew: ".$count;
			$this->log->info($msg);
			return $msg;
        }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
        
    }

    public function createPermitRequestHistory($monthlyRequest)
	{
	  	if($monthlyRequest){
            $permitRenewDate = date('Y-m-d h:i:s');
            foreach($monthlyRequest as $k => $v){
                // $permitData = PermitRequestRenewHistory::firstOrNew(['partner_id' => self::PARTNER_ID, 'anet_transaction_id' => $v->anet_transaction_id]);
                $permitData = new PermitRequestRenewHistory();
				
                $permitData->permit_request_id = $v->id;
                $permitData->user_id = $v->user_id;
                $permitData->facility_id = $v->facility_id;                
                $permitData->anet_transaction_id = $v->anet_transaction_id;
                $permitData->tracking_code = $v->tracking_code;
                $permitData->email = $v->email;                
                $permitData->name = $v->name;
                $permitData->phone = $v->phone;
                $permitData->permit_rate = $v->permit_rate;
                $permitData->permit_rate_id = $v->permit_rate_id;
                $permitData->approved_on = $v->approved_on;
                $permitData->account_number = $v->account_number;
                $permitData->monthly_duration_value = $v->monthly_duration_value;
                $permitData->no_of_days = $v->no_of_days;
                $permitData->partner_id = $v->partner_id;
                $permitData->license_number = $v->license_number;
                $permitData->mer_reference = $v->mer_reference;
                $permitData->image_front = $v->image_front;
                $permitData->image_back = $v->image_back;
                $permitData->user_consent = $v->user_consent;
                $permitData->vehicle_id = $v->vehicle_id;
                $permitData->is_admin = $v->is_admin;
                $permitData->ex_month = $v->ex_month;
                $permitData->ex_year = $v->ex_year;
                $permitData->payment_gateway = $v->payment_gateway;
                $permitData->permit_type = $v->permit_type;
                $permitData->is_payment_authrize = $v->is_payment_authrize;
                $permitData->session_id = $v->session_id;
                $permitData->permit_type_name = $v->permit_type_name;
                $permitData->skidata_id = $v->skidata_id;
                $permitData->skidata_value = $v->skidata_value;
                $permitData->acknowledge = $v->acknowledge;
                $permitData->facility_zone_id = $v->facility_zone_id;
                $permitData->desired_start_date = $v->desired_start_date;
                $permitData->desired_end_date = $v->desired_end_date;
                $permitData->cancelled_at = $v->cancelled_at;
                $permitData->created_at = $permitRenewDate;
                $permitData->updated_at = $permitRenewDate;
                $permitData->deleted_at = $v->deleted_at;                

                $permitData->hid_card_number = $v->hid_card_number;
                $permitData->account_name = $v->account_name;
                $permitData->permit_final_amount = $v->permit_final_amount;
                $permitData->user_remark = $v->user_remark;
                $permitData->user_type_id = $v->user_type_id;
                $permitData->is_antipass_enabled = $v->is_antipass_enabled;
                $permitData->admin_user_id = $v->admin_user_id;
                $permitData->discount_amount = $v->discount_amount;
                $permitData->promocode = $v->promocode;
                $permitData->negotiated_amount = $v->negotiated_amount;

                $permitData->refund_amount          = $v->refund_amount;
			    $permitData->refund_type            = $v->refund_type;
			    $permitData->refund_remarks         = $v->refund_remarks;
			    $permitData->refund_date            = $v->refund_date;
			    $permitData->refund_by              = $v->refund_by;
			    $permitData->refund_transaction_id  = $v->refund_transaction_id;
			    $permitData->refund_status          = $v->refund_status;

                $permitData->save();
            }            
        }
        return true;
	}

    /* public function createPermitRequestHistoryNew($monthlyRequest)
	{
	  	if($monthlyRequest){
            $permitRenewDate = date('Y-m-d h:i:s');
                // $permitData = PermitRequestRenewHistory::firstOrNew(['partner_id' => self::PARTNER_ID, 'anet_transaction_id' => $monthlyRequest->anet_transaction_id]);
                $permitData = new PermitRequestRenewHistory();
				
                $permitData->permit_request_id = $monthlyRequest->id;
                $permitData->user_id = $monthlyRequest->user_id;
                $permitData->facility_id = $monthlyRequest->facility_id;                
                $permitData->anet_transaction_id = $monthlyRequest->anet_transaction_id;
                $permitData->tracking_code = $monthlyRequest->tracking_code;
                $permitData->email = $monthlyRequest->email;                
                $permitData->name = $monthlyRequest->name;
                $permitData->phone = $monthlyRequest->phone;
                $permitData->permit_rate = $monthlyRequest->permit_rate;
                $permitData->permit_rate_id = $monthlyRequest->permit_rate_id;
                $permitData->approved_on = $monthlyRequest->approved_on;
                $permitData->account_number = $monthlyRequest->account_number;
                $permitData->monthly_duration_value = $monthlyRequest->monthly_duration_value;
                $permitData->no_of_days = $monthlyRequest->no_of_days;
                $permitData->partner_id = $monthlyRequest->partner_id;
                $permitData->license_number = $monthlyRequest->license_number;
                $permitData->mer_reference = $monthlyRequest->mer_reference;
                $permitData->image_front = $monthlyRequest->image_front;
                $permitData->image_back = $monthlyRequest->image_back;
                $permitData->user_consent = $monthlyRequest->user_consent;
                $permitData->vehicle_id = $monthlyRequest->vehicle_id;
                $permitData->is_admin = $monthlyRequest->is_admin;
                $permitData->ex_month = $monthlyRequest->ex_month;
                $permitData->ex_year = $monthlyRequest->ex_year;
                $permitData->payment_gateway = $monthlyRequest->payment_gateway;
                $permitData->permit_type = $monthlyRequest->permit_type;
                $permitData->is_payment_authrize = $monthlyRequest->is_payment_authrize;
                $permitData->session_id = $monthlyRequest->session_id;
                $permitData->permit_type_name = $monthlyRequest->permit_type_name;
                $permitData->skidata_id = $monthlyRequest->skidata_id;
                $permitData->skidata_value = $monthlyRequest->skidata_value;
                $permitData->acknowledge = $monthlyRequest->acknowledge;
                $permitData->facility_zone_id = $monthlyRequest->facility_zone_id;
                $permitData->desired_start_date = $monthlyRequest->desired_start_date;
                $permitData->desired_end_date = $monthlyRequest->desired_end_date;
                $permitData->cancelled_at = $monthlyRequest->cancelled_at;
                $permitData->created_at = $permitRenewDate;
                $permitData->updated_at = $permitRenewDate;
                $permitData->deleted_at = $monthlyRequest->deleted_at;
                
                $permitData->hid_card_number = $monthlyRequest->hid_card_number;
                $permitData->account_name = $monthlyRequest->account_name;
                $permitData->permit_final_amount = $monthlyRequest->permit_final_amount;
                $permitData->user_remark = $monthlyRequest->user_remark;
                $permitData->user_type_id = $monthlyRequest->user_type_id;
                $permitData->is_antipass_enabled = $monthlyRequest->is_antipass_enabled;
                $permitData->admin_user_id = $monthlyRequest->admin_user_id;
                $permitData->discount_amount = $monthlyRequest->discount_amount;
                $permitData->promocode = $monthlyRequest->promocode;
                $permitData->negotiated_amount = $monthlyRequest->negotiated_amount;

                $permitData->refund_amount          = $monthlyRequest->refund_amount;
			    $permitData->refund_type            = $monthlyRequest->refund_type;
			    $permitData->refund_remarks         = $monthlyRequest->refund_remarks;
			    $permitData->refund_date            = $monthlyRequest->refund_date;
			    $permitData->refund_by              = $monthlyRequest->refund_by;
			    $permitData->refund_transaction_id  = $monthlyRequest->refund_transaction_id;
			    $permitData->refund_status          = $monthlyRequest->refund_status;

                $permitData->save();
                       
        }
        return true;
	} */
}
