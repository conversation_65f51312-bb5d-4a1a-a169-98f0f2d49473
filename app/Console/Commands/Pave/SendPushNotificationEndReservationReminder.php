<?php

namespace App\Console\Commands\Pave;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use View;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\Facility;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\AuthorizeNetTransaction;
use App\Models\OauthClient;
use Auth;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Notification;
use App\Models\Devicetoken;
use App\Classes\PushNotification;
use App\Models\Reservation;




/**
 * Emails reservation stub to user
 */
class SendPushNotificationEndReservationReminder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pave:push-notification-before-end-reservation-reminder';



 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send push notification to customer as Reservation End Reminder before 15 minutes from ending reservation.';

	const PARTNER_ID = '359007';
    const NOTIFICATION_RESERVATION_END_REMINDER = "RESERVATION_END_REMINDER";

	
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/Pave/')->createLogger('end-reservation-push-notification');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $facilities = Facility::where('owner_id',self::PARTNER_ID)->get();
		if (!$facilities) {
            throw new NotFoundException('Facilities Not Found.');
        }

            foreach($facilities as $val)
            {
                $this->setCustomTimezone($val->id);
                $current_time = date('Y-m-d H:i:s');
                $this->log->info("Current Time: ".$current_time);
                $reservation = Reservation::where('facility_id', $val->id)->where('partner_id', self::PARTNER_ID)->whereNull("cancelled_at")->whereDate("start_timestamp", '>=', date("Y-m-d 00:00:00"))->get();

                if(!$reservation){
                    throw new ApiGenericException("No Any Reservation found.");
                }
                foreach($reservation as $reservationDetails)
                {
                    $secs = strtotime($reservationDetails->length)-strtotime("00:00:00");
                    $finaltimestamp = date("Y-m-d H:i:s",strtotime($reservationDetails->start_timestamp)+$secs);

                    $this->log->info("Reservation Data info: ".json_encode($reservationDetails));
                    $customerDetails = User::where('id',$reservationDetails->user_id)->orderBy('id', 'desc')->first();

                    if($customerDetails && $customerDetails->is_notification_enabled)
                    {
                        $this->log->info("Got user ".$customerDetails->id);
                        $deviceTokens = Devicetoken::where('user_id',$customerDetails->id)->orderBy('id', 'desc')->get();
                        //$this->log->info("DevoiceToken Data --". $deviceTokens);                       

                        if(count($deviceTokens)>0)
                        { 
                            $this->log->info("Got device token for ".$customerDetails->id);
                            $notificationData = Notification::where('user_id',$reservationDetails->user_id)->where('reservation_code',$reservationDetails->ticketech_code)->where('event_type',self::NOTIFICATION_RESERVATION_END_REMINDER)->orderBy('id', 'desc')->first();

                            if(!$notificationData)
                            {
                                $this->log->info("Records not found in Notification tbl for ".$customerDetails->id);                           
                                $from_time = strtotime($current_time); 
                                $to_time = strtotime($finaltimestamp); 
                                $dmin = round(abs($from_time - $to_time) / 60,2);
                                $this->log->info("Remaining Minutes: ".$dmin);
                                if($dmin <= '15.00')
                                {
                                    $this->log->info("inside condition, time is less than or equal 15 min");
                                    $reservationTicket = Ticket::where('reservation_id', $reservationDetails->id)->first();

                                    if(!$reservationTicket)
                                    {
                                        $message = "Hi"." ".ucfirst($customerDetails->name).","." "."Your booking will be expire soon.";
                                        $title = "Parking Payments";

                                        $msg_payload = array (
                                                        'message' => $message,               
                                                        'title' => $title,               
                                                        'set_payload'=> array('title' =>$title,
                                                        'notification_type'=>self::NOTIFICATION_RESERVATION_END_REMINDER,
                                                        'ticket_number'=>$reservationDetails->ticketech_code)  // add new payload 
                                        );  
                                        $this->log->info("Message Payload info: ".json_encode($msg_payload));
                                    }
                                    else{
                                        $this->log->info("No Notification sent for : ".$reservationDetails->ticketech_code);
                                    }







                                    
                              
                                    // foreach($deviceTokens as $deviceToken)
                                    // { 
                                    //     $deviceType = isset($deviceToken->device_type)?$deviceToken->device_type:'';
                                    //     $deviceToken = isset($deviceToken->device_token)?$deviceToken->device_token:'';
                                    //     $userid = $customerDetails->id;
                                    //     $this->sendPushNotification($msg_payload, $deviceType, $deviceToken,$userid); 
                                        
                                    //     $this->log->info("Send Notification Message: '{$message}' and title: '{$title}' for user_id '{$reservationDetails->user_id}'");
                                    // }
                                    // $notificationData = new Notification();
                                    // $notificationData->user_id = $reservationDetails->user_id;
                                    // $notificationData->title = $title;
                                    // $notificationData->message = $message;
                                    // $notificationData->reservation_code = $reservationDetails->ticketech_code;
                                    // $notificationData->device_type = $deviceType;
                                    // $notificationData->event_type = self::NOTIFICATION_RESERVATION_START_REMINDER;
                                    // $notificationData->save();
                                }  
                                else{
                                    $this->log->info("Greater than 15 minutes for ".$customerDetails->id);
                                }     
                            } 
                            else{
                                $this->log->error("Records found in notification table for ".$customerDetails->id);
                            }                           
                        }
                        else{
                            $this->log->error("Device token not found for ".$customerDetails->id);
                        }

                    }
                    else{
                        $this->log->error("Notification not enable for this user_id {$reservationDetails->user_id}");
                    }
                }
            }
            
       						
    }
	
	public function setCustomTimezone($facility_id){
      $facility = Facility::find($facility_id);
      $secret = OauthClient::where('partner_id', $facility->owner_id)->first();        
      $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
      if($partnerTimezone){
        if($partnerTimezone->timezone != ''){
            config(['app.timezone' => $partnerTimezone->timezone]);
            date_default_timezone_set($partnerTimezone->timezone);
        }
      }
    }

    public function sendPushNotification($msg_payload, $deviceType, $deviceToken,$userid )
    {	
        $this->log->info("Newtoken ".$deviceToken);
        return $response = PushNotification::sendPushNotification($deviceType, $deviceToken, $msg_payload,$userid);        
    }
	
}
