<?php

namespace App\Console\Commands\Pave;

use Mail;
use Exception;
use File;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\BrandSetting;

class PaveGatedConfirmCheckin extends Command
{

    protected $signature = 'email:pave-parking-gated-confirm-checkin {id} {type}';

    protected $description = 'Send checkin confirmation email.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/paveGatedConfirm')->createLogger('checkin-checkout-email');
    }

    public function handle()
    {
       try{ 
       $id = $this->argument('id');
       $type = $this->argument('type');
       $this->log->info("Mail start");
       if($type == 'checkin'){
            $data = Ticket::with(['facility','user'])->where('id', $id)->first();
            //user mail
            $facility_brand_setting = FacilityBrandSetting::where('facility_id', $data->facility_id)->first();
            if($facility_brand_setting){
                $data->facility_logo_id = $facility_brand_setting->id;
            }else{
                $data->logo_id = BrandSetting::where('user_id', $data->partner_id)->first();
            }
            Mail::send(
                "pave.touchless-parking-email-checkin", ['data' => $data], function ($message) use($data) {
                    $message->to($data->user->email)->subject("ParkEngage Touchless Check-In Confirmation Details Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Mail sent to ". $data->user->email);
       }else{
            $data = OverstayTicket::with('facility')->where('id', $id)->first();
            $ticket = Ticket::where('ticket_number', $data->ticket_number)->first();
            //user mail
            Mail::send(
                "pave.touchless-parking-overstay-email-payment", ['data' => $data,'ticket' => $ticket], function ($message) use($data) {
                    $message->to($data->user->email)->subject("ParkEngage Touchless Overstay Booking Details Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
       }
    }catch(Exception $e) {
        $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
        $this->log->error($msg);
        $this->log->info("Queue ended");            
    }
       
       
    }
    
}

