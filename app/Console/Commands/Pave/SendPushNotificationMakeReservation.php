<?php

namespace App\Console\Commands\Pave;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use View;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\Facility;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\AuthorizeNetTransaction;
use App\Models\OauthClient;
use Auth;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Notification;
use App\Models\Devicetoken;
use App\Classes\PushNotification;
use App\Models\Reservation;




/**
 * Emails reservation stub to user
 */
class SendPushNotificationMakeReservation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pave:push-notification-make-reservation {user_id} {user_email} {facility}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send push notification to customer as reservation create.';

   

    const WELCOME_CHECK_IN_MSG = "Welcome ";
    const NOTIFICATION_CHECK_IN = "RESERVATION";
    const WELCOME_CHECK_IN_MSG1 = " You have successfully booked your parking at ";



	
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/Pave/')->createLogger('make-reservation-push-notification');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $user_id = $this->argument('user_id');
        $user_email = $this->argument('user_email');
        $facility_name = $this->argument('facility');
        $this->log->info("received parameters, User id: ".$user_id.' Facility '.$facility_name);



        try {
            $customerDetails = false;
            $customerDetails = User::where('id',$user_id)->first();
            
            $this->log->info("User info: ".json_encode($customerDetails));


            
          if($customerDetails && $customerDetails->is_notification_enabled)
          {
             //send Push notification
             $deviceTokens = Devicetoken::where('user_id',$customerDetails->id)->orderBy('id', 'desc')->get();  
             
                         
             if(count($deviceTokens)>0)
             {
                $message = self::WELCOME_CHECK_IN_MSG."".ucfirst($customerDetails->name).",".self::WELCOME_CHECK_IN_MSG1." ".$facility_name; 
                $title = "Reservation Created Successfully";
            
                $reservation = Reservation::where('user_id', $customerDetails->id)->orderBy('id', 'desc')->first();

                $msg_payload = array (
                                'message' => $message,               
                                'title' => $title,               
                                'set_payload'=> array('title' =>$title,
                                'notification_type'=>self::NOTIFICATION_CHECK_IN,
                                'ticket_number'=>$reservation->ticketech_code)  // add new payload 
                );
                $this->log->info("Message Payload info: ".json_encode($msg_payload));


                     $notificationData = Notification::where('user_id',$customerDetails->id)->where('reservation_code',$reservation->ticketech_code)->where('event_type',self::NOTIFICATION_CHECK_IN)->orderBy('id', 'desc')->first();

                     if(!$notificationData)
                     {
                        foreach($deviceTokens as $deviceToken)
                        {
                            $deviceType = isset($deviceToken->device_type)?$deviceToken->device_type:'';
                            $deviceToken = isset($deviceToken->device_token)?$deviceToken->device_token:'';
                            $this->sendPushNotification($msg_payload, $deviceType, $deviceToken,$customerDetails->id);
                            $this->log->info("Send Notification Message: '{$message}' and title: '{$title}' for user_id '{$customerDetails->user_id}'");
                        }
                        $notificationData = new Notification();
                        $notificationData->user_id = $customerDetails->id;
                        $notificationData->title = $title;
                        $notificationData->message = $message;
                        $notificationData->reservation_code = $reservation->ticketech_code;
                        $notificationData->device_type = $deviceType;
                        $notificationData->event_type = self::NOTIFICATION_CHECK_IN;   
                        $notificationData->save();

                        $this->log->info("Notification Message Saved: '{$message}' and title: '{$title}' for user_id '{$customerDetails->user_id}'");
                     }


         

            }
             }
        } catch (Exception $e) {
            $this->log->error("error, ".$e->getMessage());

            throw $e;
        }
     						
    }

    public function setCustomTimezone($facility_id){
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();        
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if($partnerTimezone){
          if($partnerTimezone->timezone != ''){
              config(['app.timezone' => $partnerTimezone->timezone]);
              date_default_timezone_set($partnerTimezone->timezone);
          }
        }
      }
  
      public function sendPushNotification($msg_payload, $deviceType, $deviceToken,$userid )
      {	
          $this->log->info("Newtoken ".$deviceToken);
          return $response = PushNotification::sendPushNotification($deviceType, $deviceToken, $msg_payload,$userid);        
      }
	
}
