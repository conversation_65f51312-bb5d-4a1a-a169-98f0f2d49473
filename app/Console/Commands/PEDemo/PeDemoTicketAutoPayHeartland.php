<?php

namespace App\Console\Commands\PEDemo;

use Exception;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Models\Ticket;
use App\Models\Facility;
use App\Models\AuthorizeNetTransaction;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\TicketExtend;
use App\Models\ParkEngage\DatacapTransaction;
use App\Classes\HeartlandPaymentGateway;
use Illuminate\Http\Request;
use App\Http\Helpers\QueryBuilder;

/**
 * Emails reservation stub to user
 */
class PeDemoTicketAutoPayHeartland extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'pedemo:ticket-autopay-heartland';
    protected $log;

    /**
     * The console command description.
     *
     * @var string
     */

    const PARTNER_ID = '54682';
    const FACILITY_ID = '199';
    const IS_GATED_FACILITY = '0';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/PeDemo/')->createLogger('autopay-heartland');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $facilities = Facility::with(['FacilityPaymentDetails'])->where('owner_id', self::PARTNER_ID)->where('is_gated_facility', self::IS_GATED_FACILITY)->where('active', '1')->get();

        if (!$facilities) {
            $this->log->info("Facility Data Not Found --");
        }
        if ($facilities) {
            foreach ($facilities as $val) {
                $this->setCustomTimezone($val->id);
                $current_time = date('Y-m-d H:i:s');
                $this->log->info("Current Time Capture Data --" . $current_time);
                $this->log->info("Facility ID --" . $val->id);
                $ticket = Ticket::where('facility_id', $val->id)->whereNull('anet_transaction_id')
                    ->where(function ($query) use ($current_time) {
                        $query->where('estimated_checkout', '<', $current_time)
                            ->orWhere('stop_parking_time', '<', $current_time);
                    })
                    ->where('paid_type', '!=', '0')->where('is_closed', '0')->whereNull('reservation_id')->whereNull('permit_request_id')->orderby('id', 'desc')->limit(15)->get();
                $this->log->info("Existing Ticket Data --" . $ticket);

                if ($ticket) {
                    foreach ($ticket as $details) {
                        // get textend details 
                        $grandTotal = $details->grand_total;
                        $discount_amount = isset($details->discount_amount) ? $details->discount_amount : '0.00';
                        if ($details->is_extended == '1') {
                            // get total amount  
                            $ticketExtends = TicketExtend::where('ticket_number', $details->ticket_number)->orderby('id', 'asc')->get();
                            foreach ($ticketExtends as $key => $value) {
                                $grandTotal += $value->grand_total;
                                $discount_amount += $value->discount_amount;
                            }
                            $this->log->info("Extend Ticket Amount : -- " . $grandTotal);
                            $this->log->info("Extend Ticket Discount Amount : -- " . $discount_amount);
                        }

                        if (isset($val->FacilityPaymentDetails) && !empty($val->FacilityPaymentDetails) && ($val->FacilityPaymentDetails->facility_payment_type_id) == '4') {

                            $cardCheck = DatacapTransaction::whereNull('deleted_at')->where('ticket_id', $details->id)->where('is_payment_complete', '0')->where('transaction_retry', '<', '3')->orderBy('id','desc')->first();
                            $this->log->info("Payment PreAuth Data --" . $cardCheck);
                            $paid_amount = isset($details->paid_amount) ? $details->paid_amount : '0.00';
                            $final_amount = $grandTotal - $discount_amount - $paid_amount;
                            $amount = ($val->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $final_amount;

                            if ($cardCheck) {
                                if ($details->anet_transaction_id != '') {
                                    continue;
                                }
                                $data['transactionId'] = $cardCheck->trans_id;
                                $tran_id = $cardCheck->ref_id;

                                $this->log->info("Payment Discount Amount --" . $discount_amount);
                                $this->log->info("Payment Paid Amount --" . $paid_amount);
                                $this->log->info("Payment Final Amount --" . $final_amount);

                                $this->log->info("Payment Amount --" . $amount);

                                if ($final_amount > 0) {
                                    $this->log->info("Payment Amount inside if--" . $amount);
                                    try {
                                        $this->log->info("Payment Amount inside try--" . $amount);

                                        $paymentRequest = new Request([
                                            'transactionId'   => $cardCheck->trans_id,
                                            'token'  => $cardCheck->token,
                                            'Amount' => $amount
                                        ]);
                                        $this->log->info("Payment request -- " . json_encode($paymentRequest->all()));
                                        if ($details->device_type == "web") {
                                            $this->log->info("Payment Amount inside web--" . $details->device_type);
                                            if ($details->is_extended == '1') {
                                                $this->log->info("Payment Amount inside extend--" . $amount);
                                                // get total amount  
                                                $ticketExtendsTime = TicketExtend::where('ticket_number', $details->ticket_number)->orderby('id', 'desc')->first();

                                                //if (isset($ticketExtendsTime->checkout_time) && $ticketExtendsTime->checkout_time < $current_time) {
                                                if (isset($ticketExtendsTime->checkout_time)) {
                                                    $this->log->info("Payment Amount inside extend1--" . $amount);

                                                    $paymentResponse = HeartlandPaymentGateway::chargePreauthCardPayment($paymentRequest, $val);
                                                    //*********************Set All Response Message */
                                                    //QueryBuilder::setAllTransactions(json_encode($paymentResponse),"Heartland",$details->ticket_number);
                                                    //QueryBuilder::setAllTransactions($paymentResponse,);
                                                    //end ********************************End Response Message
                                                    $this->log->info("Payment response inside extend-- " . json_encode($paymentResponse));
                                                } else {
                                                    continue;
                                                }
                                            } else {
                                                $this->log->info("Payment Amount inside extend2--" . $amount);

                                                $paymentResponse = HeartlandPaymentGateway::chargePreauthCardPayment($paymentRequest, $val);
                                                //*********************Set All Response Message */
                                                //QueryBuilder::setAllTransactions(json_encode($paymentResponse),"Heartland",$details->ticket_number);
                                                //end ********************************End Response Message
                                                $this->log->info("Payment response without extend-- " . json_encode($paymentResponse));
                                            }
                                        } else if ($details->device_type == "Self Checkin App - Android") {

                                            $this->log->info("Payment Amount inside Android--" . $details->device_type);
                                            if ($details->is_extended == '1') {
                                                $this->log->info("Payment Amount inside extend Android--" . $amount);
                                                // get total amount  
                                                $ticketExtendsTime = TicketExtend::where('ticket_number', $details->ticket_number)->orderby('id', 'desc')->first();

                                                //if (isset($ticketExtendsTime->checkout_time) && $ticketExtendsTime->checkout_time < $current_time) {
                                                if (isset($ticketExtendsTime->checkout_time)) {
                                                    $this->log->info("Payment Amount inside extend1 Android--" . $amount);

                                                    $paymentResponse = HeartlandPaymentGateway::chargePreauthCardPayment($paymentRequest, $val);
                                                    //*********************Set All Response Message */
                                                    //QueryBuilder::setAllTransactions(json_encode($paymentResponse),"Heartland",$details->ticket_number);
                                                    //end ********************************End Response Message
                                                    $this->log->info("Payment response inside extend-- " . json_encode($paymentResponse));
                                                } else {
                                                    continue;
                                                }
                                            } else {
                                                $this->log->info("Payment Amount inside extend2 Android--" . $amount);

                                                $paymentResponse = HeartlandPaymentGateway::chargePreauthCardPayment($paymentRequest, $val);
                                                //*********************Set All Response Message */
                                                //QueryBuilder::setAllTransactions(json_encode($paymentResponse),"Heartland",$details->ticket_number);
                                                //end ********************************End Response Message
                                                $this->log->info("Payment response without extend-- " . json_encode($paymentResponse));
                                            }
                                        } else if ($details->device_type == "IM30") {
                                            //dd($paymentRequest->all());
                                            $this->log->info("Payment Amount inside IM30--" . $details->device_type);
                                            if ($details->is_extended == '1') {
                                                $this->log->info("Payment Amount inside extend IM30--" . $amount);
                                                // get total amount  
                                                $ticketExtendsTime = TicketExtend::where('ticket_number', $details->ticket_number)->orderby('id', 'desc')->first();

                                                //if (isset($ticketExtendsTime->checkout_time) && $ticketExtendsTime->checkout_time < $current_time) {
                                                if (isset($ticketExtendsTime->checkout_time)) {
                                                    $this->log->info("Payment Amount inside extend1 IM30--" . $amount);
                                                    //dd($paymentRequest->all(),'inside extend IM30');
                                                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByTokenIM30($paymentRequest, $val);
                                                    //*********************Set All Response Message */
                                                    //QueryBuilder::setAllTransactions(json_encode($paymentResponse),"Heartland",$details->ticket_number);
                                                    //end ********************************End Response Message
                                                    //dd($paymentResponse,'inside extend');
                                                    $this->log->info("Payment response inside extend IM30 raw -- " . $paymentResponse);
                                                    $this->log->info("Payment response inside extend IM30 -- " . json_encode($paymentResponse));
                                                } else {
                                                    continue;
                                                }
                                            } else {
                                                $this->log->info("Payment Amount inside extend2 IM30 --" . $amount);

                                                $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByTokenIM30($paymentRequest, $val);
                                                //*********************Set All Response Message */
                                                // QueryBuilder::setAllTransactions(json_encode($paymentResponse),"Heartland",$details->ticket_number);
                                                //end ********************************End Response Message
                                                $this->log->info("Payment response without extend IM30-- " . json_encode($paymentResponse));
                                            }
                                        }

                                        $this->log->info("Payment Response -- " . json_encode($paymentResponse));

                                        //    if (($paymentResponse) && ($paymentResponse->responseMessage == 'Success')) {
                                        if (($paymentResponse) && (in_array($paymentResponse->responseMessage, ['Success', 'APPROVAL']))) {
                                            $this->log->info("Payment Amount inside extend3--" . $amount);
                                            $request = new Request([
                                                'total'   => $final_amount,
                                                'card_last_four' => $cardCheck->card_last_four,
                                                'expiration' => $cardCheck->expiry,
                                                'expiration_date' => $cardCheck->expiry
                                            ]);
                                            $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $details->user_id);
                                            $this->log->info("Payment Transaction Data  --" . $authorized_anet_transaction);
                                            $paymentTransaction = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first();

                                            if ($paymentTransaction) {
                                                $paymentTransaction->anet_trans_id = $cardCheck->trans_id;
                                                $paymentTransaction->card_type = $cardCheck->card_type;
                                                $paymentTransaction->save();
                                            }
                                            $details->anet_transaction_id = $authorized_anet_transaction->id;
                                            $details->payment_date = $current_time;
                                            $details->is_closed = 1;
                                            $details->is_checkout = 1;
                                            $details->closed_date = $current_time;
                                            $details->save();
                                            $cardCheck->is_payment_complete = '1';
                                            $cardCheck->transaction_retry = ++$cardCheck->transaction_retry;
                                            $cardCheck->save();
                                            //************* update ANt tble Refrence key */ 
                                            QueryBuilder::setReferenceKey($authorized_anet_transaction->id, $details->ticket_number);
                                            //*********************Set All Response Message */
                                            //QueryBuilder::setAllTransactions(json_encode($paymentResponse),"Heartland",$details->ticket_number);
                                            //end ********************************End Response Message
                                            //************* End update ANt tble Refrence key */ 
                                        } else {
                                            $cardCheck->transaction_retry = ++$cardCheck->transaction_retry;
                                            $cardCheck->save();
                                            QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), 'heartland', $details->ticket_number, 'peDemo', $details->user_id, null, $details);//PIMS-14941
                                        }
                                        /*
                                        if (($paymentResponse) && ($paymentResponse->responseMessage == 'APPROVAL')) {
                                            $this->log->info("Payment Amount inside APPROVAL--" . $amount);
                                            $request = new Request([
                                                'total'   => $final_amount,
                                                'card_last_four' => $cardCheck->card_last_four,
                                                'expiration' => $cardCheck->expiry,
                                                'expiration_date' => $cardCheck->expiry
                                            ]);
                                            $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $details->user_id);
                                            $this->log->info("Payment Transaction Data  --" . $authorized_anet_transaction);
                                            $paymentTransaction = AuthorizeNetTransaction::where('id',$authorized_anet_transaction->id)->first();
                                        /*
                                            if($paymentTransaction){
                                                $paymentTransaction->anet_trans_id = $cardCheck->trans_id;
                                                $paymentTransaction->card_type = $cardCheck->card_type;
                                                $paymentTransaction->save();                                                
                                            }
                                            $details->anet_transaction_id = $authorized_anet_transaction->id;
                                            $details->payment_date = $current_time;
                                            $details->is_closed = 1;
                                            $details->is_checkout = 1;
                                            $details->closed_date = $current_time;
                                            $details->save();
                                            $cardCheck->is_payment_complete = '1';
                                            $cardCheck->transaction_retry = ++$cardCheck->transaction_retry;
                                            $cardCheck->save();
                                            //************* update ANt tble Refrence key 
                                            // QueryBuilder::setReferenceKey($authorized_anet_transaction->id, $details->ticket_number);
                                            //*********************Set All Response Message 
                                            //QueryBuilder::setAllTransactions(json_encode($paymentResponse),"Heartland",$details->ticket_number);
                                            //end ********************************End Response Message
                                            //************* End update ANt tble Refrence key 
                                        
                                        } else {
                                            $cardCheck->transaction_retry = ++$cardCheck->transaction_retry;
                                            $cardCheck->save();
                                        }
                                        */
                                    } catch (Exception $e) {
                                        $this->log->info('catch block');
                                        $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                                        $this->log->error($msg);
                                        $cardCheck->transaction_retry = ++$cardCheck->transaction_retry;
                                        $cardCheck->save();
                                    }
                                } else if ($final_amount == "0.00") {

                                    $details->is_closed = 1;
                                    $details->is_checkout = 1;
                                    $details->closed_date = $current_time;
                                    $details->save();
                                    $cardCheck->is_payment_complete = '1';
                                    $cardCheck->transaction_retry = ++$cardCheck->transaction_retry;
                                    $cardCheck->save();
                                }
                            } else {
                                $discount_amount = isset($details->discount_amount) ? $details->discount_amount : '0.00';
                                $paid_amount = isset($details->paid_amount) ? $details->paid_amount : '0.00';
                                $final_amount = $grandTotal - $discount_amount - $paid_amount;
                                $amount = $final_amount;

                                if ($final_amount == "0.00") {
                                    $details->is_closed = 1;
                                    $details->is_checkout = 1;
                                    $details->closed_date = $current_time;
                                    $details->save();
                                }
                            }
                        } else {
                            $this->log->info("Payment Settings not Found for this Facility -- " . json_encode($val));
                            continue;
                        }
                    }
                }
            }
        }
        return 1;
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if (($facility) && ($facility->timezone != '')) {
            date_default_timezone_set($facility->timezone);
        } else {
            if ($partnerTimezone) {
                if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }
}
