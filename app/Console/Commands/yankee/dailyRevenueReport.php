<?php

namespace App\Console\Commands\yankee;

use App\Exceptions\ApiGenericException;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\Rate;
use App\Services\LoggerFactory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Mail;

use Illuminate\Support\Fluent;
use PHPExcel_Worksheet_Drawing;

class dailyRevenueReport extends Command
{
    protected $partnerId;
    protected $partnerRmId;
    protected $facilityId;
    protected $checkinTime;
    protected $checkoutTime;
    protected $totalTickets;
    protected $totalTax;
    protected $totalAmounts;
    protected $log;
    protected $isSummaryEnable;
    protected $color;
    protected $brandSetting;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "cityparking:daily-revenue-report
                            { --partnerId= : partner ID }
                            { --facilityId= : facility ID }
                            { --startDate= : optional start date}
                            { --endDate= : optional end date}";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is used to send daily revenue excel default or Date range baseed ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/crons')->createLogger('cityparking');
        // $this->partnerId = config('parkengage.townsend.partner_id');
        // $this->partnerRmId = config('parkengage.townsend.partner_id');
        $this->totalTickets = 0;
        $this->totalAmounts = 0;
        $this->totalTax = 0;
        $this->isSummaryEnable = TRUE;
        $this->color = '#0C4A7A'; #$color = "#0C4A7A";
        error_reporting(0);
    }
    // setting up start and end date parameters
    public function setUp()
    {
        $this->log->info('Execuing cron Send Daily Email.');

        // $startDateCurrentMonth = new Carbon();
        // $startDate = $startDateCurrentMonth->subDay();

        // $endDateNextMonth = new Carbon('first day of next month');
        // $endDate = $endDateNextMonth->subDay();

        $startDate = Carbon::now()->subDays(1)->format('Y-m-d ') . '04:30:00';
        $endDate = Carbon::now()->format('Y-m-d ') . '04:30:00';

        $this->partnerId = $this->option('partnerId') ?: 0;
        $this->facilityId = $this->option('facilityId') ?: 0;
        // $this->partnerId = config('parkengage.townsend.partner_id');

        // $this->checkinTime = $this->option('startDate') ? date('Y-m-d', strtotime($this->option('startDate'))) . ' 04:30:00' : date('Y-m-d',strtotime('-1 days')) . ' 04:30:00';
        // $this->checkoutTime = $this->option('endDate') ? date('Y-m-d', strtotime('+1 day',strtotime($this->option('endDate')))) . ' 04:30:00':date('Y-m-d') .' 04:30:00';

        $this->checkinTime = $this->option('startDate') ? date('Y-m-d', strtotime($this->option('startDate'))) . ' 04:30:00' : $startDate;
        $this->checkoutTime = $this->option('endDate') ? date('Y-m-d', strtotime('+1 day', strtotime($this->option('endDate')))) . ' 04:30:00' : $endDate;

        // dd($startDate,$endDate,$this->checkinTime,$this->checkoutTime);

        // $this->checkinTime = '2024-03-01 00:00:00';
        // $this->checkoutTime = '2024-03-31 23:59:59';

        if ($this->partnerId <= 0) {
            throw new Exception("Please Enter Partner ID");
        }
        if ($this->facilityId <= 0) {
            throw new Exception("Please Enter Facility");
        }
        // $this->brandSettings
        $this->log->info('Execuing cron for monthly cancellation with start date : ' . $this->checkinTime . ' End Date : ' . $this->checkoutTime);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->setUp();
            $finalCodes5 = [];
            $TotalTicketAmount = 0;
            $TotalRevenueNonValidated = 0;
            $totalTicketsNonValidated = 0;
            $netValueNonValidated = 0;
            $processingFeeNonValidated = 0;
            $totalDiscountAmountNonValidated = 0;
            $validatedAmountNonValidated = 0;
            $totalDriveUpDuration = 0;
            $totalReservationDurtion = 0;
            $facility = Facility::where(['id' => $this->facilityId])->first();
            $this->log->info('CityParking 11.');

            $this->log->info('CityParking 1122.');
            $rates = Rate::where(['facility_id' => $facility->id])->withTrashed()->orderby('description', 'asc')->get();
            $this->log->info('CityParking 1133.');
            $this->log->info('CityParking 11444.');
            $locationName = $facility->full_name;
            $shortName    = $facility->short_name;

            $this->brandSetting = BrandSetting::where('user_id', $this->partnerId)->first();
            $color = $this->brandSetting->color;
            $this->color = $this->brandSetting->color;
            $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = 0;
            if ($this->partnerId == 26380 && in_array($this->facilityId, [155])) {
                // City Parking Daily Revenue Scheduled CRON  
                $count  = 0;
                $excelSheetName = ucwords(str_replace(' ', '', 'Daily Revenue Report'));

                // Non Validated
                $finalCodes5 = [];
                $checkinArray = [];
                $TotalRevenueNonValidated = 0;

                // $totalTicketsNonValidated = 0;
                $netValueNonValidated = 0;
                $validatedAmountNonValidated = 0;
                $processingFeeNonValidated = 0;

                $excelSheetName5 = ucwords('Daily Revenue Report');

                //offline sheet data
                $finalCodes2 = [];
                $TotalPaymentReceived = 0;
                $TotalDiscountAmount = 0;

                $finalCodes2[0]['total'] = 0;
                $finalCodes2[0]['discount'] = 0;
                $finalCodes2[1]['total'] = 0;
                $finalCodes2[1]['discount'] = 0;

                $TotalTicketAmount = 0;
                $totalDriveUpDuration = $totalReservationDurtion = 0;
                $totalDiscountAmountNonValidated = $validatedCheckinAmountNonValidated = $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = 0;
                $finalCodes4 = [];
                $reservationTickets = [];
                if (isset($this->facilityId) && !empty($this->facilityId)) {

                    $checkData = "SELECT * from tickets as t where t.facility_id in(" . $this->facilityId . ")  and t.deleted_at is null and  t.partner_id IN ('" . $this->partnerId . "') and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "' and t.paid_type='9' group by t.is_overstay,t.id ";

                    $checkDataResult = DB::select($checkData);
                    if (empty($checkDataResult) || empty($this->facilityId)) {
                        $this->log->info("No Ticket Found on Facility {$this->facilityId} : On date : " . date('Y-m-d'));
                        throw new ApiGenericException('There is no data for this date.');
                    }

                    if ($this->facilityId > 0) {
                        $validationAmountTotal = 0;

                        $validationPaidAmountTotal = 0;
                        $totalGrossAmount = 0;
                        $validationTicketTotal = 0;
                        $validatedGTotal = 0;
                        $totalNetAmount = 0;
                        $totalServiceAmount = 0;
                        $finalCodes3 = [];
                        $TotalCc = 0;
                        $TotalTicketAmount = 0;
                        $excelSheetName = $shortName . '-Daily Revenue Report-' . date('m-d-Y');

                        Excel::create(
                            $excelSheetName,
                            function ($excel) use (
                                $color,
                                $finalCodes2,
                                $count,
                                $TotalPaymentReceived,
                                $finalCodes3,
                                $TotalCc,
                                $finalCodes4,
                                $validationPaidAmountTotal,
                                $totalServiceAmount,
                                $validationAmountTotal,
                                $validationTicketTotal,
                                $totalGrossAmount,
                                $TotalDiscountAmount,
                                $validatedGTotal,
                                $totalNetAmount,
                                $processingFeeNonValidated,
                                $finalCodes5,
                                $excelSheetName5,
                                $totalTicketsNonValidated,
                                $TotalRevenueNonValidated,
                                $netValueNonValidated,
                                $validatedAmountNonValidated,
                                $totalDiscountAmountNonValidated,
                                $TotalCheckinRevenueNonValidated,
                                $totalTicketsCheckinNonValidated,
                                $reservationTickets,
                                $totalDriveUpDuration,
                                $totalReservationDurtion,
                                $TotalTicketAmount

                            ) {
                                // foreach ($UserFacilities as $u_key => $UserFacility) {
                                // dd($UserFacility);
                                $totalGrossAmount = $totalServiceAmount = $totalNetAmount = $validationAmountTotal = $validationPaidAmountTotal = $validatedGTotal = 0;
                                //$facilities=[];
                                $totalCashServiceAmount = $TotalPaymentReceived = $TotalCc = $totalServiceAmount = $processingFeeNonValidated = 0;
                                $totalTicketsNonValidated = $validationTicketTotal = $rTticketCount = 0;
                                $finalCodes5 = [];
                                $reservationCheckinArray = [];
                                $finalCodes4 = [];
                                $reservationTickets = [];
                                $ccticketCount = 0;
                                $totalExtentionCount = $totalExtendedAmount = $processingFeeNonValidated = $validatedAmountNonValidated = $totalFinalAmt = 0;
                                $facilities = Facility::where(['id' => $this->facilityId])->first();
                                // $rates = Rate::where(['facility_id' => $this->facilityId])->withTrashed()->orderby('price', 'asc')->get();
                                $facilityID = " AND t.facility_id = '" . $this->facilityId . "' ";

                                $tranisent_tax_total = $total_transient_net_amount = 0;

                                $SearchString = " t.paid_type='9' AND  t.partner_id IN (" . $this->partnerId . ") and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "' AND t.facility_id IN (" . $this->facilityId . ")";
                                $validationSearchString = " t.paid_type !='9' AND  t.partner_id IN (" . $this->partnerId . ") and t.checkout_time >='" . $this->checkinTime . "' and t.checkout_time <='" . $this->checkoutTime . "' AND t.facility_id IN (" . $this->facilityId . ")";
                                $rowKey = 0;
                                $driveupData = "SELECT count(distinct(t.id)) as ticketCount, t.check_in_datetime, t.total as total, t.anet_transaction_id,t.is_offline_payment, CASE 
                                WHEN t.is_overstay = '1' THEN CEIL(COALESCE(t.length, 0) + COALESCE(ot.length, 0))
                                ELSE ceil(COALESCE(t.length,0))
                                END AS lenghtInMints, sum(t.tax_fee) as tax_fee, 
                                t.grand_total as group_rate,
                                sum(t.processing_fee) as processing_fee, 
                                sum(t.grand_total) as grand_total, 
                                sum(t.parking_amount) as net_amount, 
                                sum(t.discount_amount) as validated_amount, 
                                f.full_name,
                                f.garage_code,
                                GROUP_CONCAT(t.id) as tickets,
                                sum(ot.grand_total) as overstayGrandTotal,
                                sum(ot.discount_amount) as overstayDiscount 
                                -- (SELECT sum(ot.grand_total) FROM overstay_tickets as ot where ot.ticket_id IN (t.id)) as overstayGrandTotal,
                                -- (SELECT sum(otd.discount_amount) FROM overstay_tickets as otd where otd.ticket_id IN (t.id)) as overstayDiscount
                                from tickets as t
                                inner join facilities as f on f.id = t.facility_id
                                left join overstay_tickets as ot on t.id = ot.ticket_id
                                where  $SearchString AND 
                                t.permit_request_id is null and t.reservation_id is null and t.event_id is null and 
                                t.deleted_at is null group by t.grand_total order by t.grand_total";
                                $totalTicketsNonValidated = 0;

                                $dataforCategoryWise = DB::select($driveupData);
                                // dd($dataforCategoryWise);
                                foreach ($dataforCategoryWise as $key => $value) {
                                    $finalCodes5[$rowKey]['Rate ($)']               = floatval($value->group_rate);
                                    $finalCodes5[$rowKey]['No of Tickets']          = intval($value->ticketCount);
                                    $finalCodes5[$rowKey]['Ticket Number']          = '-';
                                    // $finalCodes5[$rowKey]['Transaction Date']       = '-';
                                    // $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($value->total) ? floatval($value->total) : '0.00';
                                    // $finalCodes5[$rowKey]['No of Extensions']        = floatval($value->no_of_extentions);
                                    // $finalCodes5[$rowKey]['Extension Amount ($)']   = floatval($value->overstayGrandTotal);
                                    // $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($value->processing_fee);
                                    $finalCodes5[$rowKey]['Gross Amount ($)']    = floatval($value->grand_total + ($value->overstayGrandTotal ?? 0));
                                    $finalCodes5[$rowKey]['Tax ($)']          = floatval($value->tax_fee);
                                    $finalCodes5[$rowKey]['Net Revenue ($)']         = floatval($value->net_amount + $value->overstayGrandTotal);
                                    $finalCodes5[$rowKey]['Discount ($)']    = floatval($value->validated_amount  + ($value->overstayDiscount ?? 0));
                                    // $transientTickets[$rowIndex]['Final Amount ($)'] = floatval($value->grand_total+ $value->overstayGrandTotal+ $value->overstayDiscount+$value->validate_amonut);
                                    // dd($value->grand_total, $value->overstayGrandTotal, $value->overstayDiscount,$value->validated_amount);
                                    $finalCodes5[$rowKey]['Final Amount ($)'] = floatval($value->grand_total + $value->overstayGrandTotal + $value->overstayDiscount + $value->validated_amount);
                                    $driveupDataTickets = "SELECT 
                                                    t.id, 
                                                    t.total, 
                                                    t.grand_total,
                                                    t.length,
                                                    t.processing_fee,
                                                    t.ticket_number, 
                                                    t.discount_amount,
                                                    t.tax_fee,
                                                    t.parking_amount as net_amount, 
                                                    sum(ot.grand_total) as overstayGrandTotal,
                                                    sum(ot.discount_amount) as overstayDiscount 
                                                    FROM tickets as t
                                                    left join overstay_tickets as ot on t.id = ot.ticket_id
                                                    where  $SearchString 
                                                    and t.deleted_at is null and t.event_id is null and t.reservation_id is null and t.permit_request_id is null and t.grand_total = " . $value->group_rate . " group by t.ticket_number order by t.created_at";
                                    $driveupDataTicketResults = DB::select($driveupDataTickets);
                                    // dd($driveupDataTicketResults,$dataforCategoryWise);
                                    $extedGrandTotal = $extedDiscount = $finalTotal = 0;
                                    if (count($driveupDataTicketResults) > 0) {
                                        $rowKey++;
                                        foreach ($driveupDataTicketResults as $tkey => $ticket) {
                                            if ($this->isSummaryEnable) {
                                                $finalCodes5[$rowKey]['Rate ($)']               = '-';
                                                $finalCodes5[$rowKey]['No of Tickets']          = '-';
                                                $finalCodes5[$rowKey]['Ticket Number']          = $ticket->ticket_number;
                                                // $finalCodes5[$rowKey]['Transaction Date']       = '-';
                                                // $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($ticket->total) ? floatval($ticket->total) : '0.00';
                                                // $finalCodes5[$rowKey]['No of Extensions']       = floatval($value->no_of_extentions);
                                                // $finalCodes5[$rowKey]['Extension Amount ($)']   = floatval($value->overstayGrandTotal);
                                                // $finalCodes5[$rowKey]['Net Revenue ($)']         = floatval($ticket->parking_amount + ($ticket->overstayGrandTotal ?? 0));
                                                // $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($ticket->processing_fee);

                                                $finalCodes5[$rowKey]['Gross Amount ($)']        = floatval($ticket->grand_total + $ticket->overstayGrandTotal);
                                                $finalCodes5[$rowKey]['Tax ($)']                 = floatval($ticket->tax_fee);
                                                $finalCodes5[$rowKey]['Net Revenue ($)']         = floatval($ticket->net_amount + $ticket->overstayGrandTotal);
                                                $finalCodes5[$rowKey]['Discount ($)']            = floatval($ticket->discount_amount + $ticket->overstayDiscount);
                                                $finalCodes5[$rowKey]['Final Amount ($)']        = floatval($ticket->grand_total + $ticket->overstayGrandTotal + ($ticket->discount_amount + $ticket->overstayDiscount));
                                                $rowKey++;
                                                // dd($finalCodes5);
                                            }

                                            $finalTotal += floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0) + ($ticket->overstayDiscount ?? 0) + ($ticket->discount_amount ?? 0));
                                            $extedGrandTotal += floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
                                            $extedDiscount += floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                                        }
                                    }

                                    $tranisent_tax_total += floatval($value->tax_fee);
                                    $total_transient_net_amount += floatval($value->net_amount + $value->overstayGrandTotal);


                                    $totalExtendedAmount           += floatval($value->overstayGrandTotal);
                                    // $finalCodes5[$outerLoopIndex]['Gross Amount ($)']         = floatval($extedGrandTotal);
                                    // $finalCodes5[$outerLoopIndex]['Discount ($)']    = floatval(($extedDiscount));
                                    // $finalCodes5[$outerLoopIndex]['Final Amount ($)']    = floatval(($finalTotal));
                                    $rowKey++;

                                    $TotalTicketAmount              += '0.00';
                                    $netValueNonValidated           += '0.00';
                                    // $totalDriveUpDuration           += $value->lenghtInMints;
                                    $totalDriveUpDuration           += 0;
                                    $totalTicketsNonValidated       += $value->ticketCount;
                                    // $totalExtentionCount            += $value->no_of_extentions;
                                    //Total Grand Total
                                    $TotalRevenueNonValidated       += floatval(($extedGrandTotal));;
                                    $processingFeeNonValidated      += floatval($value->processing_fee);
                                    //Total Final Amount
                                    $totalFinalAmt +=  floatval($value->grand_total + $value->overstayGrandTotal + $value->overstayDiscount + $value->validated_amount);
                                    $validatedAmountNonValidated    += floatval($value->validated_amount);
                                    //Total Disocunt
                                    $totalDiscountAmountNonValidated += floatval(($extedDiscount));
                                }

                                // dd($totalFinalAmt);
                                //resertion data end
                                $reservationData = "SELECT COUNT(distinct(t.id)) AS ticketCount,
                                    COALESCE(t.grand_total, 0) AS grand_total,
                                    COALESCE(t.grand_total, 0) AS payAmount,
                                    SUM(COALESCE(t.tax_fee, 0)) AS tax_fee,
                                    SUM(COALESCE(t.processing_fee, 0)) AS processing_fee,
                                    SUM(COALESCE(t.grand_total, 0)) AS Pay_grand_total,
                                    SUM(COALESCE(t.parking_amount, 0)) AS net_amount,
                                    SUM(COALESCE(t.discount_amount, 0)) AS discount_amount,
                                    SUM(COALESCE(t.paid_amount, 0)) AS validated_amount,
                                    group_concat(t.id) as reservation_ids,
                                    r.ticketech_code,
                                    t.ticket_number,
                                    f.full_name,
                                    t.length,
                                    t.total,
                                    CASE
                                        WHEN t.is_overstay = '1' THEN sum(CEIL(COALESCE(t.length, 0) + COALESCE(tex.length, 0)))
                                        ELSE SUM(CEIL(COALESCE(t.length, 0)))
                                    END AS lenghtInMints,
                                    sum(tex.grand_total) as overstayGrandTotal,
                                    sum(tex.discount_amount) as overstayDiscount
                                    from tickets as t
                                    inner join reservations as r on r.id = t.reservation_id
                                    inner join facilities as f on f.id = t.facility_id
                                    left join ticket_extends as tex on t.id = tex.ticket_id
                                    where  $SearchString and t.is_checkout ='1' and t.deleted_at is null and r.deleted_at is null and t.reservation_id is not null group by t.grand_total,t.is_overstay order by t.grand_total";

                                $reservationDataResult = DB::select($reservationData);
                                $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = $discountAmount = 0;
                                $rowKey = 0;

                                //for event tickets

                                $partner_id  = $this->partnerId;
                                $checkInTime =  $this->checkinTime;
                                $checkInTime1 = $this->checkoutTime;

                                $event_query = "SELECT  count(distinct(t.id)) as ticketCount, e.id, e.title, e.event_rate, e.base_event_hours, e.driveup_event_rate,sum(t.grand_total) as sum_grand_total,sum(t.tax_fee) as sum_tax_fee
                                FROM events as e
                                inner join tickets as t on t.event_id = e.id
                                WHERE e.deleted_at is null and t.deleted_at is null and e.partner_id IN ('$partner_id')  and e.start_time >='$checkInTime' and e.start_time <='$checkInTime1' group by e.id";
                                // dd($event_query);
                                $eventTicketResults =  DB::select($event_query);

                                $eventRowIndex  = 0;
                                $eventTickets = [];
                                foreach ($eventTicketResults as $key => $value) {
                                    $eventTickets[$eventRowIndex]['EventName'] = $value->title;
                                    $eventTickets[$eventRowIndex]['EventRate'] = $value->event_rate;
                                    $eventTickets[$eventRowIndex]['NoofTickets'] = $value->ticketCount;
                                    // $eventTickets[$eventRowIndex]['Processing Fee ($)'] = $ticket->grand_total;
                                    // $eventTickets[$eventRowIndex]['Net Revenue ($)'] = $ticket->grand_total;
                                    $eventTickets[$eventRowIndex]['TicketNumber'] = '';
                                    $eventTickets[$eventRowIndex]['TaxAmount'] = $value->sum_tax_fee;
                                    $eventTickets[$eventRowIndex]['GrossAmount'] = $value->sum_grand_total;

                                    // Tickets 
                                    $eventTickets_sql = "SELECT t.id, t.total, t.grand_total,t.length,t.processing_fee,t.tax_fee,t.ticket_number FROM tickets as t inner join events as e on t.event_id = e.id  WHERE e.deleted_at is null and t.deleted_at is null and e.partner_id IN ('$partner_id')  and e.start_time >='$checkInTime' and e.start_time <='$checkInTime1' and t.event_id IN ($value->id)  order by t.id asc";
                                    $eventTicketsResults = DB::select($eventTickets_sql);
                                    if (count($eventTicketsResults) > 0) {
                                        $eventRowIndex++;
                                        foreach ($eventTicketsResults as $trow => $ticket) {
                                            $eventTickets[$eventRowIndex]['EventName'] = '-';
                                            $eventTickets[$eventRowIndex]['EventRate'] = '-';
                                            $eventTickets[$eventRowIndex]['NoofTickets'] = '-';
                                            $eventTickets[$eventRowIndex]['TicketNumber'] = $ticket->ticket_number;
                                            $eventTickets[$eventRowIndex]['TaxAmount'] = $ticket->tax_fee;
                                            $eventTickets[$eventRowIndex]['GrossAmount'] = $ticket->grand_total;
                                            $eventRowIndex++;
                                        }
                                    }
                                    // else {
                                    $eventRowIndex++;
                                    // }
                                }

                                // dd($eventTicketResults);
                                if (count($reservationDataResult) > 0) {
                                    foreach ($reservationDataResult as $key => $value) {
                                        $reservationCheckinArray[$rowKey]['Rate ($)']              = floatval($value->payAmount);
                                        $reservationCheckinArray[$rowKey]['Ticket Number']     = $reservationDataResult->account_number;
                                        $reservationCheckinArray[$rowKey]['No of Tickets']     = intval($value->tracking_code);
                                        $reservationCheckinArray[$rowKey]['Ticket Amount ($)'] = floatval('2.00');
                                        $reservationCheckinArray[$rowKey]['Duration (Hours)']    = intval($value->lenghtInMints);
                                        $reservationCheckinArray[$rowKey]['Gross Amount ($)']  = floatval($value->Pay_grand_total + ($value->overstayGrandTotal ?? 0));
                                        // $reservationCheckinArray[$rowKey]['Processing Fees ($)'] = floatval($dataforCategoryWise[$rowKey]['faidprocessing_fee']);
                                        // $reservationCheckinArray[$rowKey]['Net Revenue ($)'] = floatval($dataforCategoryWise[$rowKey]['fnet_amount']);
                                        $reservationCheckinArray[$rowKey]['Discount ($)'] = floatval($value->discount_amount  + ($value->overstayDiscount ?? 0));

                                        $driveupDataTickets = "SELECT t.id AS ticketCount,
                                                            t.grand_total AS grand_total,
                                                            t.grand_total  AS payAmount,  
                                                            t.tax_fee   AS tax_fee,   
                                                            t.processing_fee AS processing_fee,
                                                            t.grand_total AS Pay_grand_total,
                                                            t.parking_amount AS net_amount,
                                                            t.discount_amount AS discount_amount,
                                                            t.paid_amount AS validated_amount,
                                                            t.ticket_number,
                                                            f.full_name,
                                                            t.length,
                                                            CASE
                                                                    WHEN t.is_overstay = '1' THEN CEIL(COALESCE(t.length, 0) + COALESCE(tex.length, 0))
                                                                    ELSE CEIL(COALESCE(t.length, 0))
                                                            END AS lenghtInMints,
                                                            (SELECT sum(tex.grand_total) FROM ticket_extends as tex where tex.ticket_id IN (t.id)) as overstayGrandTotal, 
                                                            (SELECT sum(texd.discount_amount) FROM ticket_extends as texd where texd.ticket_id IN (t.id)) as overstayDiscount
                                                            from tickets as t
                                                            inner join facilities as f on f.id = t.facility_id
                                                            left join ticket_extends as tex on t.id = tex.ticket_id
                                                            where  $SearchString and t.is_checkout ='1' and t.deleted_at is null and t.reservation_id is not null and t.grand_total = '" . $value->payAmount . "' $facilityID order by t.grand_total";
                                        // $this->log->info('WLA 33 Query . ' . json_encode($driveupDataTickets));
                                        $driveupDataTicketResults = DB::select($driveupDataTickets);
                                        if (count($driveupDataTicketResults) > 0 && $this->isSummaryEnable) {
                                            $rowKey++;
                                            foreach ($driveupDataTicketResults as $tkey => $ticket) {
                                                $reservationCheckinArray[$rowKey]['Rate ($)']              = '';
                                                $reservationCheckinArray[$rowKey]['Ticket Number']     = $ticket->ticket_number;
                                                $reservationCheckinArray[$rowKey]['No of Tickets']     = 1;
                                                $reservationCheckinArray[$rowKey]['Ticket Amount ($)'] = floatval('2.00');
                                                $reservationCheckinArray[$rowKey]['Duration (Hours)']    = intval($ticket->lenghtInMints);
                                                $reservationCheckinArray[$rowKey]['Gross Amount ($)']  = floatval($ticket->Pay_grand_total + ($ticket->overstayGrandTotal ?? 0));
                                                $reservationCheckinArray[$rowKey]['Discount ($)'] = floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                                                $rowKey++;
                                            }
                                        }
                                        $rowKey++;
                                        $TotalTicketAmount          += floatval($value->payAmount);
                                        $processingFeeNonValidated  += floatval($value->processing_fee);
                                        $totalReservationDurtion    += $value->lenghtInMints;
                                        $totalTicketsCheckinNonValidated += $value->ticketCount;
                                        $TotalCheckinRevenueNonValidated += floatval($value->Pay_grand_total + $value->overstayGrandTotal);
                                        $netValueNonValidated += 0;
                                        // $validatedCheckinAmountNonValidated = floatval($value->discount_amount  + ($value->overstayDiscount ?? 0));
                                    }
                                }
                                // }    

                                // Permint search String
                                $permitSearchString = " pr.partner_id IN ('" . $this->partnerId . "')  AND pr.created_at >='" . $this->checkinTime . "' and pr.created_at <='" . $this->checkoutTime . "' AND pr.facility_id IN (" . $this->facilityId . ") and pr.deleted_at IS NULL AND pr.status ='1' and pr.cancelled_at is null ";

                                $permitData = "SELECT pr.permit_rate, count(DISTINCT pr.id) as permit_count, sum(ant.total) as per_grand_total, pr.facility_id, GROUP_CONCAT(DISTINCT pr.account_number) as permit_id,GROUP_CONCAT(DISTINCT pr.id) as tickets FROM permit_requests as pr
                                    inner join anet_transactions as ant on ant.id = pr.anet_transaction_id          
                                    where $permitSearchString group by pr.permit_rate order by pr.permit_rate asc";
                                $permitResults = DB::select($permitData);
                                $permitGrossTotalAmount = $permitTicketCount = $permitCount  =  $permitRowIndex  = 0;
                                $permitTickets = [];
                                foreach ($permitResults as $key => $value) {
                                    $permitTickets[$permitRowIndex]['Rate'] = $value->permit_rate;
                                    $permitTickets[$permitRowIndex]['PermitCount'] = $value->permit_count;
                                    $permitTickets[$permitRowIndex]['NoofTickets'] = floatval(0);
                                    $permitTickets[$permitRowIndex]['PermitNumber'] = '-';
                                    $permitTickets[$permitRowIndex]['GrossAmount'] = $value->per_grand_total;
                                    $permitTicketsResults = explode(",", $value->permit_id);
                                    $permitRowIndex++;
                                    if (count($permitTicketsResults) > 0 && $this->isSummaryEnable) {
                                        foreach ($permitTicketsResults as $trow => $account_number) {
                                            $permitAmountSql = "SELECT pr.permit_rate, pr.id as permitRowID , ant.total, pr.facility_id               
                                            FROM permit_requests as pr
                                            inner join anet_transactions as ant on ant.id = pr.anet_transaction_id
                                            left join tickets as t on t.permit_request_id = pr.id
                                            where $permitSearchString and  account_number IN ($account_number)
                                            group by pr.permit_rate order by pr.permit_rate asc";
                                            $permitAmount = DB::select($permitAmountSql);
                                            // Print checkin against Permit Tickets 
                                            $permitRowID = $permitAmount[0]->permitRowID;
                                            $permitTickets_sql = "SELECT id, total,ticket_number FROM tickets WHERE permit_request_id IN ($permitRowID) and deleted_at is null order by id asc";
                                            $permitTicketsNumberResults = DB::select($permitTickets_sql);
                                            $count = count($permitTicketsNumberResults) > 0 ? count($permitTicketsNumberResults) : 0;
                                            $permitTicketCount += $count;
                                            $permitTickets[$permitRowIndex]['Rate'] = '-';
                                            $permitTickets[$permitRowIndex]['PermitCount'] = '-';
                                            $permitTickets[$permitRowIndex]['NoofTickets'] = floatval($count);
                                            $permitTickets[$permitRowIndex]['PermitNumber'] = $account_number;
                                            $permitTickets[$permitRowIndex]['GrossAmount'] = $permitAmount[0]->total;
                                            $permitRowIndex++;
                                        }
                                    }
                                    $permitCount += $value->permit_count;
                                    $permitGrossTotalAmount += $value->per_grand_total;
                                }
                                $pemitAggrgatedData = [];
                                if (count($permitResults) > 0) {
                                    $pemitAggrgatedData['totalPermitTicket'] = $permitTicketCount;
                                    $pemitAggrgatedData['totalPermitCount'] = $permitCount;
                                    $pemitAggrgatedData['totalGrossAmount'] = $permitGrossTotalAmount;
                                }

                                // Cash or Offline payment - for transient

                                $driveUpOfflinePayment = "SELECT SUM(t.grand_total) AS sum_offline_amount,SUM(t.discount_amount) as discountAmount,t.is_offline_payment,t.partner_id ,count(t.id) as ticketCount,SUM(t.processing_fee) as processingFee,
                                (SELECT sum(overstay_tickets.grand_total) FROM overstay_tickets where ticket_id IN (t.id)) as overstayGrandTotal,
                                (SELECT sum(overstay_tickets.discount_amount) FROM overstay_tickets where ticket_id IN (t.id)) as overstayDiscount
                                -- SUM(tex.grand_total) as overstayGrandTotal, SUM(tex.discount_amount) as overstayDiscount
                                FROM tickets AS t    
                                left join ticket_extends as tex on t.id = tex.ticket_id
                                WHERE  $SearchString and t.user_id > 0  AND  t.deleted_at is null and 
                                t.event_id is null and 
                                t.permit_request_id is null and 
                                t.is_offline_payment IN('1')  $facilityID GROUP BY is_offline_payment,t.id";
                                $driveUpCashReport = DB::select($driveUpOfflinePayment);

                                // Non Cash or card breakdown
                                $DriveUpNonCashPayment = "SELECT CASE              
                                when ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                when ant.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                when ant.card_type IN ('DCVR') THEN 'DCVR'
                                WHEN ant.card_type IN ('AMEX') THEN 'AMEX'
                                ELSE ant.card_type
                                END AS combined_card_type, SUM(t.grand_total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                                (SELECT sum(overstay_tickets.grand_total) FROM overstay_tickets where ticket_id IN (t.id)) as overstayGrandTotal,
                                (SELECT sum(overstay_tickets.discount_amount) FROM overstay_tickets where ticket_id IN (t.id)) as overstayDiscount
                                FROM tickets as t
                                inner join anet_transactions as ant ON ant.id = t.anet_transaction_id
                                WHERE t.partner_id IN ('$this->partnerId') AND 
                                t.checkout_time >='$this->checkinTime' AND
                                t.checkout_time <='$this->checkoutTime' AND
                                t.event_id is null and 
                                t.permit_request_id is null and 
                                t.is_offline_payment IN ('0','2','3') and  t.deleted_at is null and t.facility_id  = $this->facilityId GROUP BY combined_card_type,t.is_overstay";

                                $driveUpCCReport = DB::select($DriveUpNonCashPayment);
                                // dd($driveUpCCReport);


                                //Event Cash Query - Shalu
                                $eventCashQuery = "SELECT SUM(t.grand_total) AS sum_offline_amount,SUM(t.discount_amount) as discountAmount,t.is_offline_payment,t.partner_id ,count(t.id) as ticketCount,SUM(t.processing_fee) as processingFee,
                                (SELECT sum(overstay_tickets.grand_total) FROM overstay_tickets where ticket_id IN (t.id)) as overstayGrandTotal,
                                (SELECT sum(overstay_tickets.discount_amount) FROM overstay_tickets where ticket_id IN (t.id)) as overstayDiscount
                                --    SUM(tex.grand_total) as overstayGrandTotal, SUM(tex.discount_amount) as overstayDiscount
                                FROM tickets AS t     
                                inner join  events as e on e.id = t.event_id
                                left join ticket_extends as tex on t.id = tex.ticket_id      
                                WHERE t.user_id > 0  and t.partner_id IN ('$partner_id') AND e.partner_id  IN ('$partner_id') and e.deleted_at is null and
                                e.start_time >= '$checkInTime' and
                                t.deleted_at is null and 
                                e.start_time <= '$checkInTime1' AND
                                e.deleted_at is null and 
                                t.event_id is not null and 
                                t.is_offline_payment IN('1')  $facilityID GROUP BY is_offline_payment,t.id";
                                $eventCashReport = DB::select($eventCashQuery);

                                //END Event Cash Query - Shalu

                                //Event Card Query - Shalu

                                $eventCardQuery = "SELECT CASE              
                                    when ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    when ant.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    when ant.card_type IN ('DCVR') THEN 'DCVR'
                                    WHEN ant.card_type IN ('AMEX') THEN 'AMEX'
                                    ELSE ant.card_type
                                END AS combined_card_type, SUM(t.grand_total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                                (SELECT sum(overstay_tickets.grand_total) FROM overstay_tickets where ticket_id IN (t.id)) as overstayGrandTotal,
                                (SELECT sum(overstay_tickets.discount_amount) FROM overstay_tickets where ticket_id IN (t.id)) as overstayDiscount
                                FROM tickets as t
                                inner join anet_transactions as ant ON ant.id = t.anet_transaction_id
                                inner join  events as e on e.id = t.event_id
                                WHERE t.partner_id IN ('$partner_id') AND  e.partner_id  IN ('$partner_id') and e.deleted_at is null and
                                e.start_time >='$checkInTime' AND
                                e.start_time <='$checkInTime1' AND
                                t.deleted_at is null and 
                                e.deleted_at is null and 
                                t.event_id is not null and 
                                t.is_offline_payment IN ('0','2','3') $facilityID GROUP BY combined_card_type,t.is_overstay";
                                $eventCardReport = DB::select($eventCardQuery);
                                // dd($eventCardReport,$driveUpCCReport);

                                // End Event Card Query - Shalu


                                //**Pemrit Card Payment  */
                                $permitCardPayment = "SELECT CASE
                                    when ant.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    WHEN ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    when ant.card_type IN ('DCVR') THEN 'DCVR'
                                    WHEN ant.card_type IN ('AMEX') THEN 'AMEX'  
                                    WHEN ant.card_type IN ('Disc') THEN 'DISC'  
                                    ELSE ant.card_type
                                    END AS combined_card_type, count(pr.id) as ticketCount, t.ticket_number, #pr.ticketech_code,
                                    SUM(pr.permit_rate) as total_amount,
                                    (SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) as overstayGrandTotal,
                                    (SELECT sum(ticket_extends.discount_amount) FROM ticket_extends where ticket_id IN (t.id)) as overstayDiscount
                                    FROM permit_requests as pr
                                    inner join anet_transactions as ant on ant.id = pr.anet_transaction_id
                                    left join tickets as t on t.permit_request_id = pr.id 
                                    WHERE $permitSearchString and ant.card_type is not null GROUP BY combined_card_type,t.is_overstay,t.ticket_number"; #td.rate_description     
                                $permitCCReportPayment = DB::select($permitCardPayment);

                                // Calculate reservation Payment             
                                // Non Cash or card breakdown
                                $checkinDriveUpnonCashPayment = "SELECT CASE
                                    WHEN t.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    when t.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    when ant.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    WHEN ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    ELSE t.card_type
                                    END AS combined_card_type, SUM(t.grand_total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                                    (SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) as overstayGrandTotal,
                                    (SELECT sum(ticket_extends.discount_amount) FROM ticket_extends where ticket_id IN (t.id)) as overstayDiscount
                                    FROM tickets as t
                                    inner join reservations as r on r.id = t.reservation_id
                                    inner join anet_transactions as ant on ant.id = r.anet_transaction_id
                                    WHERE $SearchString AND t.is_offline_payment='0' and t.deleted_at is null and t.reservation_id is not null and t.deleted_at is null and r.deleted_at is null  and t.is_checkout ='1' $facilityID GROUP BY combined_card_type,t.is_overstay"; #td.rate_description     

                                $checkinDriveUpCCReport = DB::select($checkinDriveUpnonCashPayment);
                                $sql_query4 = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                                    sum(t.total) as total,                
                                    sum(t.grand_total) as paidAmount,                
                                    sum(t.parking_amount) as parking_amount,
                                    sum(t.paid_amount) as validated_amount,
                                    sum(t.discount_amount) as discount_amount,
                                    t.affiliate_business_id, ab.business_name as BusinessName
                                    FROM tickets as t        
                                    inner join affiliate_business as ab on ab.id = t.affiliate_business_id 
                                    WHERE  $validationSearchString and t.affiliate_business_id is not null and paid_by > 0 $facilityID GROUP BY t.affiliate_business_id order by t.affiliate_business_id";
                                $validationReport = DB::select($sql_query4);
                                foreach ($driveUpCashReport as $key => $value) {
                                    if ($value->is_offline_payment == 1) {
                                        $finalCodes2[0]['payment_type'] = 'Cash';
                                        $finalCodes2[0]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                                        $finalCodes2[0]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                                    } else if ($value->is_offline_payment == 2 || $value->is_offline_payment == 3) {
                                        $finalCodes2[1]['payment_type'] = 'Card';
                                        $finalCodes2[1]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                                        $finalCodes2[1]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                                    }
                                    $totalCashServiceAmount += $value->processingFee;
                                    $TotalPaymentReceived +=  $value->sum_offline_amount + $value->overstayGrandTotal;
                                    $TotalDiscountAmount +=  $value->discountAmount + $value->overstayDiscount;
                                }


                                //Credit Card Payment Section 
                                $permitCardPayment = "SELECT CASE
                                    when ant.card_type IN ('VS','VISA','VISA CREDIT','VISA DEBIT') THEN 'VISA'
                                    WHEN ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                                    when ant.card_type IN ('DCVR') THEN 'DCVR'
                                    WHEN ant.card_type IN ('AMEX') THEN 'AMEX'  
                                    WHEN ant.card_type IN ('Disc') THEN 'DISC'  
                                    ELSE ant.card_type
                                    END AS combined_card_type, count(pr.id) as ticketCount, #pr.ticketech_code,
                                    SUM(ant.total) as total_amount               
                                    FROM permit_requests as pr
                                    inner join anet_transactions as ant on ant.id = pr.anet_transaction_id                
                                    WHERE $permitSearchString and ant.card_type is not null and pr.status ='1' and pr.cancelled_at is null GROUP BY combined_card_type "; #td.rate_description     
                                $permitCCReportPayment = DB::select($permitCardPayment);

                                $totalCashServiceAmount = $ccticketCount = 0;
                                $cards = ['AMEX', 'DCVR', 'MASTERCARD', 'VISA', 'DISC'];

                                foreach ($cards as $cardkey => $card) {
                                    $totalcardPay  = $processingFees  = $discountAmount = $ticketCount = 0;
                                    if (isset($driveUpCCReport) && count($driveUpCCReport) > 0) {
                                        foreach ($driveUpCCReport as $key => $value) {
                                            if (strcasecmp($card, $value->combined_card_type) == 0) {
                                                if ($value->total_amount <= 0) continue;
                                                $ticketCount += intval($value->ticketCount);
                                                $discountAmount += floatval($value->discountAmount + $value->overstayDiscount);
                                                $processingFees += floatval($value->processingFee);
                                                $totalcardPay += floatval($value->total_amount + $value->overstayGrandTotal);
                                                $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                                $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                            }
                                        }
                                    }

                                    if (isset($eventCardReport) && count($eventCardReport) > 0) {
                                        foreach ($eventCardReport as $key => $e_value) {
                                            // Drive Up Card Payment
                                            if ($card == $e_value->combined_card_type) {
                                                if ($e_value->total_amount <= 0) continue;
                                                $this->log->info("classicBasicReport INNER LOOP " . json_encode($e_value->combined_card_type));
                                                $ticketCount += intval($e_value->ticketCount);
                                                $discountAmount += floatval($e_value->discountAmount + $e_value->overstayDiscount);
                                                $processingFees += floatval($e_value->processingFee);
                                                $totalcardPay += floatval($e_value->total_amount + $e_value->overstayGrandTotal);
                                                $finalCodes3[$cardkey]['no_cash_receipts'] = $e_value->combined_card_type;
                                                $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                            }
                                        }
                                    }


                                    if (isset($checkinDriveUpCCReport) && count($checkinDriveUpCCReport) > 0) {
                                        foreach ($checkinDriveUpCCReport as $key => $value) {
                                            if ($card == $value->combined_card_type) {
                                                $totalcardPay += floatval($value->total_amount + $value->overstayGrandTotal);
                                                $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                            }
                                        }
                                    }
                                    // Permit card Payment
                                    if (isset($permitCCReportPayment) && count($permitCCReportPayment) > 0) {
                                        foreach ($permitCCReportPayment as $key => $value) {
                                            if ($card == $value->combined_card_type) {
                                                $totalcardPay += floatval($value->total_amount);

                                                $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                                $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                            }
                                        }
                                    }


                                    $TotalCc += $totalcardPay;
                                    $ccticketCount += $ticketCount;
                                    $totalCashServiceAmount += $processingFees;
                                    $TotalDiscountAmount += $discountAmount;
                                }

                                $i = 0;
                                foreach ($validationReport as $key => $value) {
                                    $finalCodes4[$i]['Business Name'] = $value->BusinessName;
                                    $finalCodes4[$i]['Policy Name'] = '-';
                                    $finalCodes4[$i]['No of Tickets'] = floatval($value->ticket_count);
                                    $finalCodes4[$i]['Gross Amount ($)'] = floatval($value->total);
                                    $finalCodes4[$i]['Processing Fee ($)'] = floatval($value->processingFee);
                                    $finalCodes4[$i]['Net Revenue ($)'] = floatval($value->total - $value->processingFee - $value->discount_amount);
                                    $finalCodes4[$i]['Validated Amount ($)'] = floatval($value->validated_amount);
                                    $finalCodes4[$i]['Paid Amount ($)'] = floatval($value->paidAmount);
                                    $finalCodes4[$i]['Total Revenue ($)'] =  floatval($value->paidAmount + $value->validated_amount);

                                    // $gTotal = $value->paidAmount + $value->validated_amount;
                                    // $grossTotal = ($value->parking_amount + $value->processingFee) * $value->ticket_count;
                                    // $serviceFee = floatval($value->processingFee * $value->ticket_count);

                                    $validationTicketTotal  += $value->ticket_count;
                                    $totalGrossAmount       += floatval($value->total);
                                    $totalServiceAmount     += floatval($value->processingFee);
                                    $totalNetAmount         += floatval($value->total - $value->processingFee - $value->discount_amount);
                                    $validationAmountTotal  += floatval($value->validated_amount);
                                    $validationPaidAmountTotal += floatval($value->paidAmount);
                                    $validatedGTotal += floatval($value->paidAmount + $value->validated_amount);;

                                    // $totalGrossAmount += $grossTotal;
                                    // policy query according to business
                                    $policy_query = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                                            sum(t.total) as total,
                                            sum(t.grand_total) as paidAmount,
                                            sum(t.parking_amount) as parking_amount,
                                            sum(t.discount_amount) as discount_amount,
                                            sum(t.paid_amount) as validated_amount,t.affiliate_business_id,t.policy_id,
                                            p.policy_name as policyName,
                                            ab.business_name as BusinessName
                                            FROM tickets as t
                                            inner join affiliate_business as ab on ab.id = t.affiliate_business_id
                                            inner join business_policy as p on p.id = t.policy_id 
                                            WHERE $SearchString and t.affiliate_business_id =$value->affiliate_business_id and t.paid_by > 0 GROUP BY t.affiliate_business_id,t.policy_id order by t.affiliate_business_id";

                                    $policyReport = DB::select($policy_query);

                                    if (isset($policyReport) && !empty($policyReport)) {
                                        $i++;
                                        foreach ($policyReport as $k => $policy) {
                                            // $gTotal = $policy->paidAmount + $policy->validated_amount;
                                            // $grossTotal = ($policy->parking_amount + $policy->processingFee) * $policy->ticket_count;
                                            // $serviceFee = floatval($policy->processingFee * $policy->ticket_count);
                                            $finalCodes4[$i]['Business Name'] = '';
                                            $finalCodes4[$i]['Policy Name'] = $policy->policyName;
                                            $finalCodes4[$i]['No of Ticket'] = floatval($policy->ticket_count);
                                            $finalCodes4[$i]['Gross Total ($)'] = floatval($policy->total);
                                            $finalCodes4[$i]['Processing Fee ($)'] = floatval($policy->processingFee);
                                            $finalCodes4[$i]['Net Revenue ($)'] = floatval($policy->total - $policy->processingFee - $value->discount_amount);
                                            $finalCodes4[$i]['Validation Amount ($)'] = floatval($policy->validated_amount);
                                            $finalCodes4[$i]['Paid Amount ($)'] = floatval($policy->paidAmount);
                                            $finalCodes4[$i]['Total Revenue ($)'] =  floatval($policy->paidAmount + $policy->validated_amount);
                                            $i++;
                                        }
                                    }
                                    $i++;
                                }

                                $locationName = $facilities->full_name;
                                $garageCode = $facilities->garage_code;
                                if (count($finalCodes5) > 0 || count($eventTicketResults) > 0 || count($permitTickets) > 0) {
                                    // Tickets Cashiered 
                                    $excel->sheet($excelSheetName5, function ($sheet5) use (
                                        $finalCodes5,
                                        $reservationTickets,
                                        $reservationCheckinArray,
                                        $permitTickets,
                                        $permitTicketCount,
                                        $color,
                                        $totalTicketsNonValidated,
                                        $TotalRevenueNonValidated,
                                        $locationName,
                                        $garageCode,
                                        $totalDiscountAmountNonValidated,
                                        $processingFeeNonValidated,
                                        $finalCodes2,
                                        $TotalPaymentReceived,
                                        $finalCodes3,
                                        $TotalCc,
                                        $finalCodes4,
                                        $validationPaidAmountTotal,
                                        $totalServiceAmount,
                                        $validationAmountTotal,
                                        $validationTicketTotal,
                                        $totalGrossAmount,
                                        $TotalDiscountAmount,
                                        $validatedGTotal,
                                        $totalTicketsCheckinNonValidated,
                                        $totalDriveUpDuration,
                                        $permitGrossTotalAmount,
                                        $permitCount,
                                        $eventTickets,
                                        $totalFinalAmt,
                                        $tranisent_tax_total,
                                        $total_transient_net_amount
                                    ) {
                                        $topSpace = 7;
                                        $sheet5->setWidth(array(
                                            'A'     => 18,
                                            'B'     =>  20,
                                            'C'     =>  16,
                                            'D'     =>  17.57,
                                            'E'     =>  17.34,
                                            'F'     =>  15.57,
                                            'G'    =>   21,
                                            'H'    =>   16.86,
                                            'I'    =>   18,
                                            'K'    =>   18,
                                            'L'    =>   18

                                        ));

                                        $sheet5->getColumnDimension('E')->setWidth(17.34);
                                        $sheet5->getColumnDimension('F')->setWidth(15.57);
                                        $sheet5->getStyle("A")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("H")->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->getStyle("I5")->getNumberFormat()->setFormatCode('0.00');

                                        $this->addLogoInExcelHeader($sheet5);

                                        $sheet5->mergeCells('A2:D2');
                                        // $sheet5->mergeCells('A2:C2');
                                        if (date('Y-m-d', strtotime($this->checkinTime)) == date('Y-m-d', strtotime($this->checkoutTime))) {
                                            $cellValue = "Report Date - " .  date('m-d-Y', strtotime($this->checkinTime));
                                        } else {

                                            $cellValue = "Report Date Range - " .  date('m-d-Y', strtotime($this->checkinTime)) .  ' - ' . date('m-d-Y', strtotime($this->checkoutTime));
                                        }
                                        $cellValue .= "\nPrint Date - " .  date('m-d-Y', strtotime('now'));
                                        $sheet5->setCellValue('A2', "$cellValue");
                                        $sheet5->getStyle('A2')->getAlignment()->setWrapText(true);
                                        // Set the height of cell H2 (adjust as needed)
                                        $sheet5->getRowDimension(2)->setRowHeight(80);
                                        $sheet5->getRowDimension(3)->setRowHeight(40);
                                        $sheet5->getRowDimension(5)->setRowHeight(40);

                                        $sheet5->cell('A2:D2', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D6DCE4');
                                            $cell->setFontColor('#000000');
                                            $cell->setFontSize('18');
                                        });
                                        $location = "Location Name \r" . $locationName;
                                        $sheet5->mergeCells('E2:G2');
                                        $sheet5->setCellValue('E2', "$location");
                                        $sheet5->getStyle('E2')->getAlignment()->setWrapText(true);
                                        $sheet5->cell('E2:G2', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D6DCE4');
                                            $cell->setFontColor('#000000');
                                            $cell->setFontSize('18');
                                        });
                                        $sheet5->mergeCells('H2:J2');
                                        $locationId = "Location ID \n" . $garageCode;
                                        $sheet5->setCellValue('H2', "$locationId");
                                        $sheet5->getStyle('H2')->getAlignment()->setWrapText(true);

                                        $sheet5->cell('H2:J2', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D6DCE4');
                                            $cell->setFontColor('#040D12');
                                            $cell->setFontSize('18');
                                        });

                                        $sheet5->mergeCells('A3:C3');
                                        $sheet5->setCellValue('A3', "Transient Counter");

                                        $sheet5->cell('A3:C3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('15');
                                        });

                                        $sheet5->mergeCells('D3:E3');
                                        $sheet5->setCellValue('D3', $totalTicketsNonValidated);
                                        $sheet5->cell('D3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('15');
                                        });

                                        $sheet5->mergeCells('F3:H3');
                                        $sheet5->setCellValue('F3', "Total Permits");
                                        $sheet5->cell('F3:H3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('15');
                                        });

                                        $sheet5->mergeCells('I3:J3');
                                        $sheet5->getStyle("I3")->getNumberFormat()->setFormatCode('0');
                                        $sheet5->setCellValue('I3', $permitCount);
                                        $sheet5->cell('I3', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('15');
                                        });
                                        //Spaace between count tickets row
                                        $sheet5->mergeCells('A4:J4');
                                        //End Spaace between count tickets row

                                        $sheet5->mergeCells('A5:C5');
                                        $sheet5->setCellValue('A5', "Total Events Tickets");
                                        $sheet5->cell('A5', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('15');
                                        });

                                        // $sheet5->setCellValue('I3', $permit_count); Commenting this as value have been update at last
                                        $sheet5->mergeCells('D5:E5');

                                        $sheet5->cell('D5', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('15');
                                        });


                                        $sheet5->mergeCells('F5:H5');
                                        $sheet5->setCellValue('F5', "Ticket Revenue ($)");
                                        $sheet5->cell('F5', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('15');
                                        });

                                        // $sheet5->setCellValue('L3', $permit_count); Commenting this as value have been update at last
                                        $sheet5->mergeCells('I5:J5');
                                        $sheet5->cell('I5', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('15');
                                        });



                                        // Drive Up Section Start Here.
                                        $sheet5->mergeCells('A6:J6');
                                        $sheet5->mergeCells('A7:J7');

                                        $sheet5->setCellValue('A7', 'Tickets Cashiered DriveUp');
                                        $sheet5->cell('A7', function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D9E1F2');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });

                                        // Color Row For Heading 
                                        $sheet5->cell('A8:J8', function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                        });

                                        $sheet5->getColumnDimension('D')->setWidth(17.57);

                                        $sheet5->fromArray($finalCodes5, [], 'A8', false, true);
                                        $topSpace = 9;

                                        //for center the result
                                        $transient_total_row = count($finalCodes5) + $topSpace;
                                        for ($t_start = 8; $t_start <= $transient_total_row; $t_start++) {
                                            $sheet5->cell('A' . $t_start . ':J' . $t_start, function ($cell) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center'); // Center vertically
                                            });
                                            $sheet5->getStyle("D" . $t_start)->getNumberFormat()->setFormatCode('0.00');
                                        }



                                        $row_name = 'A' . $transient_total_row . ':J' . $transient_total_row;
                                        $sheet5->cell($row_name, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                            $row->setAlignment('center');
                                            $row->setValignment('center');
                                        });

                                        $sheet5->getStyle("D" . $transient_total_row)->getNumberFormat()->setFormatCode('0.00');


                                        // Drive Up Total Row
                                        $sheet5->row($transient_total_row, array('Total ($)', $totalTicketsNonValidated, '-', ($TotalRevenueNonValidated - $processingFeeNonValidated), $tranisent_tax_total, $total_transient_net_amount, $totalDiscountAmountNonValidated, $totalFinalAmt));
                                        // Drive Up Section Close Here !!

                                        /* Reservation Related Code */
                                        if (count($reservationCheckinArray) > 0) {
                                            $j = count($finalCodes5) + $topSpace + 2;
                                            $sheet5->mergeCells('A' . $j . ':E' . $j);
                                            $sheet5->setCellValue('A' . $j, 'Tickets Cashiered Reservation');
                                            $sheet5->cell('A' . $j, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center');
                                                $cell->setFontWeight('bold');
                                                $cell->setBackground('#D9E1F2');
                                                $cell->setFontColor('#ffffff');
                                                $cell->setFontSize('12');
                                            });

                                            $i = count($finalCodes5) + $topSpace + 3;
                                            foreach ($reservationCheckinArray as $key => $value) {
                                                $sheet5->cell('A' . $i . ':G' . $i, function ($cell) use ($color) {
                                                    $cell->setAlignment('center'); // Center horizontally
                                                    $cell->setValignment('center'); // Center vertically
                                                });
                                                $sheet5->setCellValue('A' . $i, $value['Rate ($)']);
                                                $sheet5->setCellValue('B' . $i, $value['Ticket Number']);
                                                $sheet5->setCellValue('C' . $i, $value['No of Tickets']);
                                                $sheet5->setCellValue('D' . $i, $value['Ticket Amount ($)']);
                                                $sheet5->setCellValue('E' . $i, $value['Duration (Hours)']);
                                                $sheet5->setCellValue('F' . $i, $value['Gross Amount ($)']);
                                                $sheet5->setCellValue('G' . $i, $value['Discount ($)']);
                                                $i++;
                                            }
                                            $i++;

                                            //reservation counter total start here
                                            $resrvation_colorCode = count($finalCodes5) + count($reservationTickets) + $topSpace + 2; // color for reser top

                                            $sheet5->row($resrvation_colorCode, array('Total ($)', '-', $totalTicketsNonValidated, '-', $totalDriveUpDuration, $TotalRevenueNonValidated, $totalDiscountAmountNonValidated));
                                            $sheet5->cell('A' . $resrvation_colorCode . ':G' . $resrvation_colorCode, function ($cell) use ($color) {
                                                $cell->setAlignment('center');
                                                $cell->setValignment('center');
                                                $cell->setFontWeight('bold');
                                                $cell->setBackground('#EC33FF');
                                                $cell->setFontColor('#272829');
                                                $cell->setFontSize('12');
                                            });

                                            //End reservation counter total start here
                                        }
                                        /* End Reservation Related Code */
                                        //event colom start here                        
                                        $eventSheetRowStart = count($finalCodes5)  + $topSpace + 2; // color for reser top
                                        if (count($reservationTickets) > 0) {
                                            $eventSheetRowStart += count($reservationTickets) + 2; // color for reser top

                                        }

                                        $sheet5->mergeCells('A' . $eventSheetRowStart . ':J' . $eventSheetRowStart);
                                        $sheet5->setCellValue('A' . $eventSheetRowStart, 'Event Tickets');
                                        $sheet5->cell('A' . $eventSheetRowStart, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D9E1F2');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });

                                        $rowNumber1 = $eventSheetRowStart + 1;
                                        $sheet5->cell('A' . $rowNumber1 . ':J' . $rowNumber1, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                        });
                                        $sheet5->cell('A' . $rowNumber1 . ':J' . $rowNumber1, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                        });
                                        $sheet5->setCellValue('A' . $rowNumber1, 'Event Name');
                                        $sheet5->setCellValue('B' . $rowNumber1, 'Event Rate');
                                        $sheet5->setCellValue('C' . $rowNumber1, 'No of Tickets');
                                        $sheet5->setCellValue('D' . $rowNumber1, 'Ticket Number');
                                        $sheet5->setCellValue('E' . $rowNumber1, 'Tax ($)');
                                        $sheet5->setCellValue('F' . $rowNumber1, 'Total Revenue ($)');
                                        $eventRow = $rowNumber1 + 1;
                                        $totalNetAmount = 0;
                                        $event_totalGrossAmount = 0;
                                        $totalEventTicket = 0;
                                        $event_base_rate = 0;
                                        $event_tax_amount = 0;
                                        foreach ($eventTickets as $key => $eval) {
                                            $sheet5->cell('A' . $eventRow . ':J' . $eventRow, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center');
                                            });
                                            // dd();
                                            // echo "<br>RATE " . $eval['EventRate'];
                                            if (is_numeric($eval['EventRate'])) {
                                                $event_base_rate += $eval['EventRate'] != '-' ? $eval['EventRate'] : '-';
                                            }
                                            $sheet5->setCellValue('A' . $eventRow, $eval['EventName']);
                                            $sheet5->setCellValue('B' . $eventRow, $eval['EventRate']);
                                            $sheet5->setCellValue('C' . $eventRow, $eval['NoofTickets']);
                                            $sheet5->setCellValue('D' . $eventRow, $eval['TicketNumber']);
                                            $sheet5->setCellValue('E' . $eventRow, $eval['TaxAmount']);
                                            $sheet5->setCellValue('F' . $eventRow, $eval['GrossAmount']);
                                            if ($eval['TicketNumber'] == '') {
                                                $totalEventTicket  += $eval['NoofTickets'];
                                                $event_totalGrossAmount       += floatval($eval['GrossAmount']);
                                                $event_tax_amount  += $eval['TaxAmount'];
                                            }
                                            $eventRow++;
                                        }
                                        $eventTotal = $eventRow;
                                        //total value
                                        $sheet5->cell('A' . $eventTotal . ':J' . $eventTotal, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                        });
                                        $sheet5->cell('A' . $eventTotal . ':J' . $eventTotal, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                        });
                                        $sheet5->setCellValue('A' . $eventTotal, 'Total');
                                        $sheet5->setCellValue('B' . $eventTotal, '');
                                        $sheet5->setCellValue('C' . $eventTotal, $totalEventTicket);
                                        $sheet5->setCellValue('D' . $eventTotal, '');
                                        $sheet5->setCellValue('E' . $eventTotal, $event_tax_amount);
                                        $sheet5->setCellValue('F' . $eventTotal, $event_totalGrossAmount);


                                        // !!!!!!!! Permit Start here :
                                        $permitSheetRowStart = $eventTotal + 2;
                                        $sheet5->getStyle('E' . $permitSheetRowStart)->getNumberFormat()->setFormatCode('0');

                                        $sheet5->mergeCells('A' . $permitSheetRowStart . ':E' . $permitSheetRowStart);
                                        $sheet5->setCellValue('A' . $permitSheetRowStart, 'Tickets Cashiered Permit');
                                        $sheet5->cell('A' . $permitSheetRowStart, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D9E1F2');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });

                                        $rowNumber1 = $permitSheetRowStart + 1;
                                        $sheet5->cell('A' . $rowNumber1 . ':J' . $rowNumber1, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                        });
                                        $sheet5->cell('A' . $rowNumber1 . ':J' . $rowNumber1, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                        });

                                        $sheet5->setCellValue('A' . $rowNumber1, 'Rate');
                                        $sheet5->setCellValue('B' . $rowNumber1, 'Permit Count');
                                        $sheet5->setCellValue('C' . $rowNumber1, 'Permit Number');
                                        $sheet5->setCellValue('D' . $rowNumber1, 'Session Count');
                                        $sheet5->setCellValue('E' . $rowNumber1, 'Total Revenue ($)');
                                        // $sheet5->setCellValue('F' . $rowNumber1, 'Ticket Number');

                                        $permitRow = $rowNumber1 + 1;
                                        $totalNetAmount = 0;
                                        $permitGrossAmount = 0;
                                        $totalPermitTicket = 0;
                                        foreach ($permitTickets as $key => $eval) {
                                            $sheet5->getStyle('D' . $permitRow)->getNumberFormat()->setFormatCode('0');
                                            $sheet5->cell('A' . $permitRow . ':E' . $permitRow, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center');
                                            });

                                            // $event_base_rate += $eval['EventRate'];
                                            $sheet5->setCellValue('A' . $permitRow, $eval['Rate']);
                                            $sheet5->setCellValue('B' . $permitRow, $eval['PermitCount']);
                                            $sheet5->setCellValue('C' . $permitRow, $eval['PermitNumber']);
                                            $sheet5->setCellValue('D' . $permitRow, $eval['NoofTickets']);
                                            $sheet5->setCellValue('E' . $permitRow, $eval['GrossAmount']);
                                            // $sheet5->setCellValue('F' . $permitRow, $eval['TicketNumber']);

                                            if (is_numeric($eval['PermitNumber'])) {
                                                $totalPermitTicket += $eval['NoofTickets'];
                                                $permitGrossAmount += floatval($eval['GrossAmount']);
                                            }
                                            // if (!empty($eval['PermitNumber'])) {
                                            // $totalPermitTicket += $eval['NoofTickets'];
                                            // $permitGrossAmount += floatval($eval['GrossAmount']);
                                            // }
                                            $permitRow++;
                                        }

                                        $sheet5->getStyle('D' . $permitRow)->getNumberFormat()->setFormatCode('0');
                                        $totalRowForPermit = $permitRow;
                                        $sheet5->setCellValue('A' . $totalRowForPermit, 'Total ($)');
                                        $sheet5->setCellValue('B' . $totalRowForPermit, $permitCount);
                                        $sheet5->setCellValue('C' . $totalRowForPermit, '');
                                        $sheet5->setCellValue('D' . $totalRowForPermit, $totalPermitTicket);
                                        $sheet5->setCellValue('E' . $totalRowForPermit, $permitGrossAmount);
                                        // $sheet5->setCellValue('F' . $totalRowForPermit, '');

                                        $sheet5->cell('A' . $totalRowForPermit . ':J' . $totalRowForPermit, function ($cell) use ($color) {
                                            $cell->setAlignment('center');
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('12');
                                        });
                                        // !!!! Permit close here .


                                        /** Non Revenue Section Start Here : eValidation */
                                        // $revenuSheetRowStart = count($finalCodes5) + count($reservationCheckinArray) + count($permitTickets) + $topSpace + 5;
                                        // $rowNumber = count($finalCodes5) + count($reservationCheckinArray) + count($permitTickets) + $topSpace + 6;
                                        $jCell = $i = $revenuSheetRowStart = $totalRowForPermit;
                                        $validationPaidAmountTotal = 0;
                                        if (count($finalCodes4) > 0) {
                                            $revenuSheetRowStart = $totalRowForPermit + 2;
                                            $rowNumber = $revenuSheetRowStart + 1;
                                            $sheet5->mergeCells('A' . $revenuSheetRowStart . ':J' . $revenuSheetRowStart);
                                            // $sheet5->mergeCells('E' .$revenuSheetRowStart .':I' .$revenuSheetRowStart);
                                            $sheet5->setCellValue('A' . $revenuSheetRowStart, 'Non Revenue Tickets');
                                            $sheet5->cell('A' . $revenuSheetRowStart, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center');
                                                $cell->setFontWeight('bold');
                                                $cell->setBackground('#D9E1F2');
                                                $cell->setFontColor('#272829');
                                                $cell->setFontSize('12');
                                            });
                                            $sheet5->cell('A' . $rowNumber . ':J' . $rowNumber, function ($cell) use ($color) {
                                                $cell->setAlignment('center');
                                                $cell->setFontWeight('bold');
                                                $cell->setBackground($color);
                                                $cell->setFontColor('#ffffff');
                                                $cell->setFontSize('12');
                                            });

                                            $sheet5->setCellValue('A' . $rowNumber, 'Business Name');
                                            $sheet5->setCellValue('B' . $rowNumber, 'Policy Name');
                                            $sheet5->setCellValue('C' . $rowNumber, 'No of Tickets');
                                            $sheet5->setCellValue('D' . $rowNumber, 'Gross Amount ($)');
                                            $sheet5->setCellValue('E' . $rowNumber, 'Processing Fee ($)');
                                            $sheet5->setCellValue('F' . $rowNumber, 'Net Revenue ($)');
                                            $sheet5->setCellValue('G' . $rowNumber, 'Validated Amount ($)');
                                            $sheet5->setCellValue('H' . $rowNumber, 'Paid Amount ($)');
                                            $sheet5->setCellValue('I' . $rowNumber, 'Total Revenue ($)');
                                            $i = $rowNumber + 1;
                                            foreach ($finalCodes4 as $key => $value) {
                                                $sheet5->cell('A' . $i . ':I' . $i, function ($cell) use ($color) {
                                                    $cell->setAlignment('center'); // Center horizontally
                                                    $cell->setValignment('center'); // Center vertically
                                                });
                                                $sheet5->setCellValue('A' . $i, $value['Business Name']);
                                                $sheet5->setCellValue('B' . $i, $value['Policy Name']);
                                                $sheet5->setCellValue('C' . $i, isset($value['No of Tickets']) ? $value['No of Tickets'] : $value['No of Ticket']);
                                                $sheet5->setCellValue('D' . $i, isset($value['Gross Amount ($)']) ? $value['Gross Amount ($)'] : $value['Gross Total ($)']);
                                                $sheet5->setCellValue('E' . $i, isset($value['Processing Fee ($)']) ? $value['Processing Fee ($)'] : $value['Processing Fee ($)']);
                                                $sheet5->setCellValue('F' . $i, $value['Net Revenue ($)']);
                                                $sheet5->setCellValue('G' . $i, isset($value['Validated Amount ($)']) ? $value['Validated Amount ($)'] : $value['Validation Amount ($)']);
                                                $sheet5->setCellValue('H' . $i, $value['Paid Amount ($)']);
                                                $sheet5->setCellValue('I' . $i, $value['Total Revenue ($)']);
                                                $i++;
                                            }
                                            $sheet5->cell('A' . $i . ':J' . $i, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center'); // Center vertically
                                            });

                                            $sheet5->setCellValue('A' . $i, 'Total ($)');
                                            $sheet5->setCellValue('B' . $i, '');
                                            $sheet5->setCellValue('C' . $i, $validationTicketTotal);
                                            $sheet5->setCellValue('D' . $i, $totalGrossAmount);
                                            $sheet5->setCellValue('E' . $i, $totalServiceAmount);
                                            $sheet5->setCellValue('F' . $i, $totalNetAmount);
                                            $sheet5->setCellValue('G' . $i, $validationAmountTotal);
                                            $sheet5->setCellValue('H' . $i, $validationPaidAmountTotal);
                                            $sheet5->setCellValue('I' . $i, $validatedGTotal);

                                            /** for color of non revenue total row */
                                            $nonRevenueTotal = count($finalCodes4) + $rowNumber + 1;
                                            $row_name = 'A' . $nonRevenueTotal . ':J' . $nonRevenueTotal;
                                            $sheet5->cell($row_name, function ($row) use ($color) {
                                                $row->setBackground($color);
                                                $row->setFontColor('#ffffff');
                                                $row->setFont(array(
                                                    'family' => 'Calibri',
                                                    'size' => '12',
                                                    'bold' => true
                                                ));
                                            });
                                        }

                                        // $totalAmountForNonValidated = $validationPaidAmountTotal + $TotalRevenueNonValidated + $permitGrossTotalAmount + $event_totalGrossAmount;
                                        // $sheet5->mergeCells('I3:J3');
                                        $totalPayment =  $TotalRevenueNonValidated + $event_totalGrossAmount + $permitGrossTotalAmount + $validationPaidAmountTotal;

                                        $sheet5->setCellValue('D5',  $totalEventTicket);

                                        $sheet5->setCellValue('I5', floatval($totalPayment));


                                        /** payment breakdown Section Started. **/
                                        $jCell = $i + 2;
                                        // dd($i, $jCell);
                                        $sheet5->mergeCells('A' . $jCell . ':J' . $jCell);
                                        //$sheet5->mergeCells('E' . $jCell . ':I' . $jCell);

                                        $sheet5->setCellValue('A' . $jCell, 'Payment Breakdown');

                                        $sheet5->cell('A' . $jCell, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D9E1F2');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });



                                        // // Total Payment Received
                                        // dd($TotalPaymentReceived, $TotalCc, $validationPaidAmountTotal, $permitGrossTotalAmount, $TotalRevenueNonValidated);
                                        // $totalPayment = $TotalPaymentReceived + $TotalCc + $validationPaidAmountTotal + $permitGrossTotalAmount + $TotalRevenueNonValidated;
                                        // $totalPayment =  $TotalRevenueNonValidated + $event_totalGrossAmount+ $permitGrossTotalAmount + $validationPaidAmountTotal;

                                        // dd($permitTicketCount,$totalEventTicket,$totalTicketsNonValidated);
                                        $this->totalTickets = $totalTicketsNonValidated + $totalEventTicket + $permitTicketCount;
                                        $this->totalTax = $tranisent_tax_total + $event_tax_amount;
                                        // dd($this->totalTax);
                                        $this->totalAmounts = $totalPayment;
                                        // $totalPayment = $grossAmountNonValidated;
                                        $newCell = $jCell + 1;
                                        $sheet5->getStyle('B' . $newCell)->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->setCellValue('A' . $newCell, 'Total Payment Received ($)');
                                        $sheet5->setCellValue('B' . $newCell, $totalPayment);
                                        // total validated Amount
                                        $ValidatedCell = $jCell + 2;
                                        $sheet5->getStyle('B' . $ValidatedCell)->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->setCellValue('A' . $ValidatedCell, 'Tax ($)');
                                        $sheet5->setCellValue('B' . $ValidatedCell, floatval($tranisent_tax_total + $event_tax_amount));

                                        $sheet5->setCellValue('E' . $newCell, 'Total Revenue ($)');
                                        $sheet5->setCellValue('F' . $newCell, ($totalPayment));

                                        $sheet5->setCellValue('E' . $ValidatedCell, 'Net Revenue ($)');
                                        $sheet5->setCellValue('F' . $ValidatedCell, floatval($totalPayment + $validationAmountTotal - ($processingFeeNonValidated + $tranisent_tax_total + $event_tax_amount)));



                                        $nonCashCell = $ValidatedCell + 2;
                                        $sheet5->cell('A' . $nonCashCell . ':F' . $nonCashCell, function ($row) use ($color) {
                                            $row->setFontColor('#ffffff');
                                            $row->setBackground($color);
                                            $row->setFont(array(
                                                'family' => 'Calibri',
                                                'size' => '12',
                                                'bold' => true
                                            ));

                                            $row->setFontWeight('bold');
                                            $row->setValignment('center');
                                        });

                                        $sheet5->row($nonCashCell, array('Non-Cash Receipts'));
                                        $sheet5->setCellValue('D' . $nonCashCell, 'Cash Receipts');
                                        $cardTupeCell = $nonCashCell + 1;
                                        $nonCashCell + 2;
                                        $sheet5->cell($cardTupeCell, function ($cell) use ($color) {
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#EDEDED');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });

                                        $sheet5->cell('B' . $cardTupeCell, function ($cell) use ($color) {
                                            $cell->setAlignment('right'); // Center horizontally
                                            $cell->setValignment('right'); // Center vertically
                                        });
                                        $sheet5->cell('E' . $cardTupeCell, function ($cell) use ($color) {
                                            $cell->setAlignment('right'); // Center horizontally
                                            $cell->setValignment('right'); // Center vertically
                                        });
                                        $sheet5->cell('F' . $cardTupeCell, function ($cell) use ($color) {
                                            $cell->setAlignment('right'); // Center horizontally
                                            $cell->setValignment('right'); // Center vertically
                                        });
                                        $sheet5->setCellValue('A' . $cardTupeCell, 'Card Type');
                                        $sheet5->setCellValue('B' . $cardTupeCell, 'Total ($)');

                                        $sheet5->setCellValue('D' . $cardTupeCell, 'Payment Type');
                                        $sheet5->setCellValue('E' . $cardTupeCell, 'Total ($)');
                                        $sheet5->setCellValue('F' . $cardTupeCell, 'Discount ($)');
                                        $i = $cardTupeCell + 1;
                                        $j = $cardTupeCell + 1;
                                        foreach ($finalCodes3 as $key => $value) {
                                            if ($value['total'] > 0) {
                                                $sheet5->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                                                $sheet5->setCellValue('A' . $i, $value['no_cash_receipts']);
                                                $sheet5->setCellValue('B' . $i, $value['total']);
                                                $i++;
                                            }
                                        }
                                        foreach ($finalCodes2 as $key => $val) {
                                            if ($val['total'] > 0) {
                                                $sheet5->setCellValue('D' . $j, $val['payment_type']);
                                                $sheet5->setCellValue('E' . $j, $val['total']);
                                                $sheet5->setCellValue('F' . $j, $val['discount']);
                                                $j++;
                                            }
                                        }
                                        $cellColor = 'A' . $i . ':B' . $i;
                                        $sheet5->cell($cellColor, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                            $row->setFont(array(
                                                'family' => 'Calibri',
                                                'size' => '12',
                                                'bold' => true
                                            ));
                                        });

                                        $cellColor = 'D' . $j . ':F' . $j;
                                        $sheet5->cell($cellColor, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                            $row->setFont(array(
                                                'family' => 'Calibri',
                                                'size' => '12',
                                                'bold' => true
                                            ));
                                        });
                                        $sheet5->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                                        $sheet5->row($i, array('Total ($)', $TotalCc));
                                        $sheet5->setCellValue('D' . $j, 'Total ($)');
                                        $sheet5->setCellValue('E' . $j, $TotalPaymentReceived);
                                        $sheet5->setCellValue('F' . $j, ($TotalDiscountAmount));
                                        // //end payment breakdown issue
                                        //merge cell for formatting last data
                                        if ($i < $j) {
                                            $i = $i + 1;
                                        }
                                        $k = $i + 1;
                                        $rowVerticalFormate = 'K1:K' . $k;
                                        $sheet5->mergeCells($rowVerticalFormate);
                                        //horigental cell
                                        $rowHorizentalFormate = 'A' . $k . ':K' . $k;
                                        $sheet5->mergeCells($rowHorizentalFormate);
                                    });
                                }
                                // else {
                                //     $this->log->info("No Record Found ");
                                // }
                                // }
                            }
                        )->store('xls');

                        $this->log->info('City Parking Daily Revenue Save Successfully');
                        $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                        $data['totalTickets']   = ($this->totalTickets);
                        $data['netValue']       = sprintf('%.2f', ($this->totalAmounts - $this->totalTax));
                        $data['overAllTotal']   = sprintf('%.2f', $this->totalAmounts);
                        $data['report_name']    = 'daily_revenue_report';
                        $data['location_name']  = $locationName;
                        $data['themeColor']     = $color;
                        $data['mail_body']      = "PFB summary of today’s Revenue Report. A detailed report is also attached.";
                        $this->log->info('City Parking Print Data ' . json_encode($data));
                        // dd($data);
                        Mail::send('yenkee.city-parking', $data, function ($message) use ($excelSheetName) {
                            $message->to(['<EMAIL>']);
                            // $message->to(config('parkengage.Yankees.revenue_report_emails'));
                            $message->subject(config('parkengage.Yankees.revenue_report_subject'));
                            $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                            $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                            $this->log->info("City Park Mail Sent success with failname :  {$path_to_file}");
                            if (file_exists($path_to_file)) {
                                // $this->log->info("Path Of file and name 333 {$path_to_file}");
                                $message->attach($path_to_file);
                            }
                        });
                        // dd(Mail::failures());
                    } else {
                        $this->log->info("No Facility Found ");
                        // no Facility Found 
                    }
                }
            } else {
                // City Parking Daily Revenue Scheduled CRON
                $this->log->info("Facility Not Match");
            }
        } catch (\Throwable $th) {
            // throw $th;
            $this->log->error('CityParking Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine());
            $errorMessage = 'CityParking Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine();
            $data['partner_key'] = "Yankee";
            $data['exception'] = "Exception in CityParking Daily Revenue Report " . $errorMessage;
            Mail::send('email-exception', $data, function ($message) {
                $message->to(config('email_exceptions'));
                // $message->to('<EMAIL>');
                $message->subject("Email Exception :- CityParking Daily Revenue Report");
                $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
            });
        }
    }


    public function addLogoInExcelHeader($sheet)
    {
        /* Code for logo*/
        $drawing = new PHPExcel_Worksheet_Drawing();

        // For Local
        if (env('ISLOCAL')) {
            $drawing->setPath(public_path('assets/media/images/breeze.png'));
        } else {
            // For Dynamic Live
            $drawing->setPath(storage_path('app/brand-settings/' . $this->brandSetting->logo));
        }
        $drawing->setCoordinates('A1');

        // Adjust the dimensions of the image if needed
        $drawing->setWidth(150);
        $drawing->setHeight(50);
        $drawing->setOffsetX(25);
        $drawing->setOffsetY(10);
        // Add image to worksheet
        $sheet->getDrawingCollection()->append($drawing);
        /* End Code for logo*/


        // !! Text Section 
        $color = $this->color;
        $sheet->mergeCells('A1:J1');
        $sheet->getRowDimension(1)->setRowHeight(60);
        $sheet->setCellValue('A1', 'Daily Revenue Report');
        $sheet->cell('A1:J1', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            // $cell->setBackground($color);
            // $cell->setFontColor('#ffffff');
            $cell->setFontSize('30');
        });
    }
}
