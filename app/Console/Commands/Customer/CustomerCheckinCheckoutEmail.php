<?php

namespace App\Console\Commands\Customer;


use App\Exceptions\ApiGenericException;
use App\Services\LoggerFactory;
use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Ticket;
use Mail;
use Exception;
use Illuminate\Support\Collection;

class CustomerCheckinCheckoutEmail extends Command
{
   

  
    
    protected $signature = 'customer-checkin-checkout {ticket_number} {type}';
    protected $description = 'Send Customer checkin checkout Email.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/customer-checkin-checkout')->createLogger('customer-checkin-checkout');
    }

    public function handle()
    {

        $type = $this->argument('type');
        $ticket_number = $this->argument('ticket_number');

        try {
           
            
            if($type == "checkin"){
                
              $ticket = Ticket::with('user','transaction','facility','reservation')->where(['ticket_number'=> $ticket_number,'is_checkin'=>1])->whereNotNull('check_in_datetime')->first();                        
            }elseif($type == "checkout"){
                $ticket = Ticket::with('user','transaction','facility','reservation')->where(['ticket_number'=> $ticket_number,'is_checkout'=>1])->whereNotNull('checkout_datetime','!=',NULL)->first();                        
              
            }
                
            

            

                 $this->log->info("Start checkin  checkout email to send".$ticket->user->email);

                Mail::send(
                "checkin-checkout.customer-checkin-checkout", ['data' => $ticket], function ($message) use($ticket) {
                    $message->to([$ticket->user->email])->subject('Customer checkin checkout');
                    $message->from(config('parkengage.default_sender_email'));
                }
            );

            //  $this->log->info("Customer checkin checkout email about to send ".$ticket->user->email);
            $this->log->info("Success ");
        } catch (Exception $e) {
            throw $e;
        }
    }
}
