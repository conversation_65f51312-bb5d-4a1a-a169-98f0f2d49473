<?php

namespace App\Console\Commands\Customer;
use Mail;
use Exception;
use File;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\Valet\ValetParkingRequestNotification;
use App\Classes\PushNotification;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\Customer\SendPushNotifyCheckout;
use App\Jobs\Customer\SendPushCustomNotification;
use App\Services\LoggerFactory;


class CustomerTestPushNotification extends Command
{
    use DispatchesJobs;

    protected $signature = 'customer:customtest-pushnotification {user_id}';
    const QUEUE_NAME = 'pe-pushnotify-custom';

    protected $description = 'Send Push notification to customer.';

    public function handle(LoggerFactory $logFactory)
    {
       $user_id = $this->argument('user_id');
       //$ticket_number = $this->argument('ticket_number');
       $this->dispatch((new SendPushCustomNotification($user_id, $logFactory))->onQueue(self::QUEUE_NAME)); 
           
    }
     /**
     * send Push notification
     *
     * @return \Illuminate\Http\Response
     */

    
}

