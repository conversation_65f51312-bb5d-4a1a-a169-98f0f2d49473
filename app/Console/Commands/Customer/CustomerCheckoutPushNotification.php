<?php

namespace App\Console\Commands\Customer;

use Mail;
use Exception;
use File;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\Valet\ValetParkingRequestNotification;
use App\Classes\PushNotification;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\Customer\SendPushNotifyCheckout;

class CustomerCheckoutPushNotification extends Command
{
   use DispatchesJobs;

   protected $signature = 'customer:checkout-pushnotification {user_id} {ticket_number} {notification_type?} {msg?}';
   const QUEUE_NAME = 'pe-pushnotify-checkout';

   protected $description = 'Send Push notification to customer after checkout retrieve request.';

   public function handle()
   {
      $user_id = $this->argument('user_id');
      $ticket_number = $this->argument('ticket_number');
      $notification_type = $this->argument('notification_type');
      $msg = $this->argument('msg');
      $this->dispatch((new SendPushNotifyCheckout($user_id, $ticket_number, $notification_type, $msg))->onQueue(self::QUEUE_NAME));
   }
   /**
    * send Push notification
    *
    * @return \Illuminate\Http\Response
    */
}
