<?php

namespace App\Console\Commands\Customer;

use Mail;
use Exception;
use File;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Models\Valet\ValetParkingRequestNotification;
use App\Classes\PushNotification;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\Customer\SendPushNotifyAcceptPickDrop;

class CustomerCheckinPushNotification extends Command
{
    use DispatchesJobs;

    protected $signature = 'customer:checkin-pushnotification {user_id} {request_id} {type} {facility_name} {notification_type?} {app_name?}';
    const QUEUE_NAME = 'pe-pushnotify-accept-pickdrop';

    protected $description = 'Send Push notification to customer after checkin retrieve request.';

    public function handle()
    {
        $user_id = $this->argument('user_id');
        $request_id = $this->argument('request_id');
        $type = $this->argument('type');
        $facility_name = $this->argument('facility_name');
        $notification_type = $this->argument('notification_type');
        $app_name = $this->argument('app_name');
        $this->dispatch((new SendPushNotifyAcceptPickDrop($user_id, $request_id, $type, $facility_name, $notification_type, $app_name))->onQueue(self::QUEUE_NAME));
    }
    /**
     * send Push notification
     *
     * @return \Illuminate\Http\Response
     */
    public function sendPushNotification($msg_payload, $deviceType, $deviceToken)
    {
        return $response = PushNotification::sendPushNotification($deviceType, $deviceToken, $msg_payload);
    }
}
