<?php
namespace App\Console\Commands\Manlo;

use Carbon\Carbon;
use Storage;
use Illuminate\Console\Command;
//use App\Models\ParkEngage\PermitEmailRateMapping;
use Illuminate\Support\Facades\DB;
//use phpseclib\Net\SFTP;
use League\Csv\Reader;
use App\Models\User;
use Illuminate\Support\Facades\File;
use App\Models\ParkEngage\TicketCitation;
use Maatwebsite\Excel\Facades\Excel;
use PHPExcel_Worksheet_Drawing;
use App\Models\ParkEngage\MstMake;
use App\Models\ParkEngage\MstColor;
use App\Models\ParkEngage\MstStyle;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\Infraction;

class ExportCitation extends Command
{
    protected $signature = 'export:citation {partner_id}';
    protected $description = 'export citatio for SFT CSV file';

    protected $log;
    const PENALTY_DAYS = 7;
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/manlo/')->createLogger('citation-export');
    }

    public function handle()
    {
        $success = [];$move = [];$sftp='';
        try {
            $this->log->info("Queue started");
            $partner_id = $this->argument('partner_id');
            //$userDetails = User::find($partner_id);
            //$partner = $userDetails->name;
            
            $remoteDir = $directory = '/mnt/log-backup/.sftp-menlo/Menlo/Citation Data/';
            $destinationDir = $directory;
            
            $citations = TicketCitation::with(['permit','voidCodes','facility','ticketCitationInfraction'])->where("partner_id", $partner_id)->where("is_closed", '0')->orderBy("id", "DESC")->get();
            $excelSheetName = ucwords(str_replace(' ', '', date("Ymd").'-Unpaid Citation'));
            $finalCodes1 = [];
            $increment1 = 1;
            $this->log->info("count citation : ".count($citations));
            if(count($citations) > 0){
                foreach($citations as $key=>$citation){
                    $this->log->info("citation : ".$citation->citation_number);
                    
                    $startDate = Carbon::parse($citation->checkin_time);
                    $endDate = Carbon::parse(date("Y-m-d H:i:s"));
                    $diff = $startDate->diffInDays($endDate);
                    if($diff <= self::PENALTY_DAYS){
                        continue;
                    }
                    $penaltyFee = $citation->penalty_fee;
                    if(isset($citation->ticketCitationInfraction->infraction_id)){
                        $infraction = Infraction::find($citation->ticketCitationInfraction->infraction_id);
                        if(isset($infraction->id) && $infraction->extend_penalty_fee > 0){
                            $penaltyFee = $infraction->extend_penalty_fee;
                        }
                    }
                    $make = MstMake::select("code")->where("name", $citation->make)->first();
                    $color = MstColor::select("code")->where("name", $citation->color)->first();
                    $style = MstStyle::select("code")->where("name", $citation->style)->first();
                    $vin = '-';
                    if($citation->vin != ''){
                        $vin = $citation->vin;
                    }else{
                        $vin = substr($citation->license_plate, -4);
                    }
                    $finalCodes1[] = [
                        'Citation #' => $citation->citation_number,
                        'Issue Date' => date("m/d/Y", strtotime($citation->checkin_time)),
                        'Issue Time' => date("Hi", strtotime($citation->checkin_time)),
                        'Chalk Time' => '',
                        'Officer Badge' => isset($citation->badge) ? substr($citation->badge,0,10) : '',
                        'Permit #' => isset($citation->permit->account_number) ? $citation->permit->account_number : '',
                        'VIN' => $vin,
                        'License' => substr($citation->license_plate,0,8),
                        'State' => isset($citation->state) ? $citation->state : '',
                        'Expiration Date' => date("m/y", strtotime($citation->estimated_checkout)),
                        'Color' => isset($color->code) ? $color->code : '',
                        'Make' => isset($make->code) ?  $make->code : '',
                        'Body Style' => isset($style->code) ? $style->code : '',
                        'Drive Away' => isset($citation->drive_away) ? '' : '',
                        'Warning' => isset($citation->drive_away) ? '' : '',
                        'Void Code' => isset($citation->voidCodes->id) ? $citation->voidCodes->code : '',
                        'Location' => isset($citation->facility->between_streets) ? substr($citation->facility->between_streets,0,36) : '',
                        'Meter' => isset($citation->meter) ? $citation->meter : '',
                        'Voilation Code' => isset($citation->statutory_code) ? $citation->statutory_code : '',
                        'Fine Amount' => $penaltyFee,
                        'Comments 1' => isset($citation->description2) ? substr(strip_tags($citation->description2),0,100) : '',
                        'Comments 2' => isset($citation->description1) ? substr(strip_tags($citation->description1),0,100) : '',
                        'Private Comment' => ''
    
                    ];
                    $increment1++;
                }
                //'Voilation Code' => isset($citation->statutory_code) ? (int) filter_var($citation->statutory_code, FILTER_SANITIZE_NUMBER_INT) : '',
                $this->log->info("loop done: ");
                $excel = Excel::create(
                    $excelSheetName,
                    function ($excel) use ($finalCodes1, $excelSheetName) {
        
                        // Set the spreadsheet title, creator, and description
                        $excel->setTitle($excelSheetName);
                        $excel->setCreator('UnpaidCitation')->setCompany('ParkEngage');
                        $excel->setDescription('List Of Unpaid Citation');
                        // Build the spreadsheet, passing in the payments array
                        if (empty($finalCodes1)) {
                            
                        } else {
                            if (isset($finalCodes1) && !empty($finalCodes1)) {
                                $excel->sheet(
                                    'Unpaid Citation List',
                                    function ($sheet) use ($finalCodes1) {
                                        $sheet->fromArray($finalCodes1, null, 'A1', false, true);
                                    }
                                );
                                
                            }
                        }
                    }
                )->store('csv');
                $csv = file_get_contents(storage_path("exports/".$excelSheetName.".csv"));
                // $tsv = str_replace(',', "\t", $csv);
                $pattern = '/,(?=(?:[^"]*"[^"]*")*[^"]*$)/';

                // Replace the matched commas with a TAB (\t)
                $tsv = preg_replace($pattern, "\t", $csv);
                //remove "" from csv
                $tsv = str_replace('"', "", $tsv);
                // Save the modified content as a TSV file
                file_put_contents(storage_path("exports/".$excelSheetName.".csv"), $tsv);
                //Storage::put($excelSheetName, $excel);
                //Storage::disk('public')->put('menlo/' . $excelSheetName, $excel);
                $moveResponse = $this->moveFile($destinationDir,$excelSheetName.".csv",storage_path("exports/".$excelSheetName.".csv"));    
                

                $this->log->info("excel created : ");    
            }


            
        }catch (\Exception $e) {
            $this->log->error("error : ".$e);
        }
    }

    public function moveFile($destinationDir,$file,$sourcePath){
        $destinationPath = $destinationDir . $file;

        if (!File::exists($destinationDir)) {
            File::makeDirectory($destinationDir, 0755, true);
        }
        if (File::move($sourcePath, $destinationPath)) {
            echo "Moved: $file to $destinationPath\n";
            return $destinationPath;
        } else {
            $this->info("File failed to move to processed folder.");
            echo "Failed to move: $file\n";
            return 'FAIL';
        }
    }
}
