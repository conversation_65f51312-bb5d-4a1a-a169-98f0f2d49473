<?php
namespace App\Console\Commands\Manlo;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Models\PermitRate;
use App\Models\ParkEngage\PermitEmailRateMapping;
use Illuminate\Support\Facades\DB;
use phpseclib\Net\SFTP;
use League\Csv\Reader;
use App\Models\User;
use Illuminate\Support\Facades\File;

class ImportPermitTypeSft extends Command
{
    protected $signature = 'import:permit-type-sft';
    protected $description = 'Import permit data from SFT CSV file';


    const PARTNER_ID = '169163';
	const FACILITY_ID = '275'; 

    public function handle()
    {
        $success = [];$move = [];$sftp='';
        try {


            if(!self::PARTNER_ID == config('parkengage.PARTNER_MANLO')){
                die("no partner");
            }

            $userDetails = User::find(self::PARTNER_ID);
            $partner = $userDetails->name;
            $remoteDir = $directory = '/mnt/log-backup/.sftp-menlo/Menlo/Permit Mapping';
          //  $remoteDir = $directory = '/opt/uploads';
            $destinationDir = '/mnt/log-backup/.sftp-menlo/Menlo/Permit Mapping/moved-files/';


            if (File::exists($directory)) {
                // Get all files (non-recursive) from the directory.
                $files = File::files($directory);
                if(count($files) > 0){
                    foreach ($files as $file) {
                        
                    }
                }else{
                    echo "no files exist.";
                    die;
                }
            } else {
                //echo "Directory does not exist.";
                $error['auth'][] = $this->errorMessage("Directory does not exist."); 
              //  $this->sendMail($error,$partner,$success,$move);
                die;
            }
			//dd('Hi',$files);
            $error = [];
            $adminUser = User::where('email', '<EMAIL>')->first(); // Replace with your logic

            if (!$adminUser) {
                $error['auth'][] = $this->errorMessage('Admin user not found.'); 
             //   $this->sendMail($error,$partner,$success,$move);
                die;
            }
            /*

            $host      = config('parkengage.SFTP_CONNECTION.MENLO.host');
            $port      = config('parkengage.SFTP_CONNECTION.MENLO.port');
            $username  = config('parkengage.SFTP_CONNECTION.MENLO.username');
            $password   = config('parkengage.SFTP_CONNECTION.MENLO.password');
            $remoteDir  = config('parkengage.SFTP_CONNECTION.MENLO.remoteDir');
            $destinationDir = config('parkengage.SFTP_CONNECTION.MENLO.destinationDir');
            $sftp = new SFTP($host, $port);
            if (!$sftp->login($username, $password)) {
                die("Authentication failed");
            }
           */
           // $files = $sftp->nlist($remoteDir);
            if ($files === false) {
                die("Failed to list files in $remoteDir");
            }

            foreach ($files as $file) {
                
                if ($file === '.' || $file === '..') {
                    continue; // Skip current & parent directory entries
                }

                // Validate if it's a CSV file
                if (pathinfo($file, PATHINFO_EXTENSION) !== 'csv') {
                    echo "Skipping non-CSV file: $file\n";
                    continue;
                }

                echo $sourcePath  = $file; echo "\n";
				$filename =  basename($file);
            //    dd($filename);
			//	dd($sourcePath,$remoteDir,$file);
                /*
                // Read CSV File Content
                $csvContent = $sftp->get($sourcePath);
				*/
				$csvContent = File::get($file);
                if ($csvContent === false) {
                    echo "Failed to read file: $sourcePath\n";
                    continue;
                }
                
				
            //    $rows = explode("\n", trim($csvContent));
                $rows = explode("\n", trim($csvContent));
				
			//	dd($rows);
			//    dd(count($rows));
                if (count($rows) < 2) { // At least one data row needed
                    $this->error("CSV file is empty or invalid1");
                    return false;
                }
				//dd($rows[0]);
                // Validate header row
                $header = str_getcsv($rows[0]);
              //  dd($header);
                $expectedHeaders = ['last name', 'email', 'first name', 'type of permit'];
                $headerError = [];
                foreach ($header as $head) {
                    $trimmedHead = strtolower(trim($head));
                    $trimmedHead = preg_replace('/\x{FEFF}/u', '', $head);                                        
                    if (!in_array(strtolower($trimmedHead), $expectedHeaders, true)) {
                        $headerError[] = $trimmedHead;
                    }
                }
                
                if(count($headerError) > 0){
                    $this->error("Invalid CSV headers. Expected: " . implode(", ", $headerError));
                    unset($headerError);
                    continue;
                }

                // Process each row
                foreach (array_slice($rows, 1) as $row) {
					//dd($row);
                    $record = str_getcsv($row);
                    if (!$record || count($record) < 3) continue;

                    $firstName  = trim($record[0] ?? ''); 
                    $lastName   = trim($record[1] ?? '');
                    $email      = trim($record[2] ?? '');
                    $permitType = trim($record[3] ?? '');

                    if (!$email || !$permitType) {
                        $this->error("Permit type '{$permitType}' or '{$email}' not found.");
                        continue;
                    }

                    // Get permit_rate_id
                    $permitRate = PermitRate::where('name', $permitType)->first();
                    if (!$permitRate) {
                        $this->error("Permit type '{$permitType}' not found.");
                        continue;
                    }

                    $permitRateId = $permitRate->id;

                    // Check if email with same permit_rate_id exists
                    $exists = PermitEmailRateMapping::where('email', $email)
                        ->where('permit_rate_id', $permitRateId)
						->where('first_name', $firstName)
                        ->exists();
					//dd($firstName,$lastName,$email,$permitRateId,self::PARTNER_ID);
                    if (!$exists) {
                        PermitEmailRateMapping::create([
                            'first_name' => $firstName,
                            'last_name' => $lastName,
                            'email' => $email,
                            'permit_rate_id' => $permitRateId,
							'partner_id' => self::PARTNER_ID,
                        ]);
                        $this->info("Inserted: {$email} with permit rate ID {$permitRateId}");
                    } else {
                        $this->info("Skipped: {$email} already exists with permit rate ID {$permitRateId}");
                    }
                }
				//dd('Here');
             //   $destinationPath = $destinationDir . date('YmdHis')."-".$file;
                $moveResponse = $this->moveFile($destinationDir,$filename,$sourcePath);
                if($moveResponse!='FAIL'){
                    $move[$file] = $moveResponse;
				//	$this->info("file Move Response": json_encode($moveResponse));
                }
                // Move file to the destination folder
                /*
                if ($sftp->rename($sourcePath, $destinationPath)) {
                    echo "Moved: $file to $destinationPath\n";
                } else {
                    $this->info("File failed to move to processed folder.");
                    echo "Failed to move: $file\n";
                }
                */    
            }
        }catch (\Exception $e) {
            dd('An error occurred: ' . $e->getMessage());
        }
    }

    public function moveFile($destinationDir,$file,$sourcePath){
        $destinationPath = $destinationDir . date('YmdHis')."-".$file;

        if (!File::exists($destinationDir)) {
            File::makeDirectory($destinationDir, 0755, true);
        }

        if (File::move($sourcePath, $destinationPath)) {
            echo "Moved: $file to $destinationPath\n";
            return $destinationPath;
        } else {
            $this->info("File failed to move to processed folder.");
            echo "Failed to move: $file\n";
            return 'FAIL';
        }
    }

}
