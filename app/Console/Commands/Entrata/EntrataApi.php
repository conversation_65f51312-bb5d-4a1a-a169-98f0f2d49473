<?php

namespace App\Console\Commands\Entrata;

use Illuminate\Console\Command;
use App\Jobs\Entrata\EntrataThridPartyData;
use App\Services\LoggerFactory;

class EntrataApi extends Command
{
    // Artisan command name
    protected $signature = 'entrata-getdata';

    protected $description = 'Fetch entrata Permit data through API';

    protected $log;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/thridparty/entrata')->createLogger('entrata-api');

        dispatch(new EntrataThridPartyData());

        $this->log->info('Entrata API job dispatched.');
    }
}
