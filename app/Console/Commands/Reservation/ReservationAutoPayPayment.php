<?php

namespace App\Console\Commands\Reservation;

use App\Classes\DatacapPaymentGateway;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\DatacapTransaction;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\TicketExtend;
use App\Models\Reservation;
use Illuminate\Http\Request;
use App\Http\Helpers\QueryBuilder;

/**
 * Emails reservation stub to user
 */
class ReservationAutoPayPayment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'roc:reservation-heartland {--ticketech_code= : If want to run for specific reservation.}';
    // php71 artisan roc:reservation-heartland --ticketech_code='PO612025'


    /**
     * The console command description.
     *
     * @var string
     */

    const PARTNER_ID = '1553';
    const FACILITY_ID = '157';
    const IS_GATED_FACILITY = '0';

    public $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/roc/paymentManaul')->createLogger('heartland');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $facility = Facility::with(['FacilityPaymentDetails', 'facilityConfiguration'])
            ->where([
                'active' => '1',
                'id' => self::FACILITY_ID,
                'owner_id' => self::PARTNER_ID
            ])
            ->first();

        if (!$facility) {
            $this->log->info("Facility Data Not Found");
            return;
        }

        if (!$this->hasValidPostChargeFacility($facility)) {
            $this->log->info("Facility not avaiable for post charge");
            return;
        }

        $this->setCustomTimezone($facility);
        $current_time = date('Y-m-d H:i:s');
        $this->log->info("Current Time Capture Data: " . $current_time);

        $reservaitons = $this->getPendingReservations($facility->id, $current_time);
        // dd($reservaitons->count());
        if (!($reservaitons->count() > 0)) {
            $this->log->info("No reservation found for charge");
            return;
        }

        $this->log->info("Number reservation found 2 : " . json_encode($reservaitons->count()));
        // dd('count');
        foreach ($reservaitons as $key => $reservaiton) {
            $this->log->info("Reservation charge Start : " . $reservaiton->ticketech_code . " and Reservation id: " . $reservaiton->id . " Run count: " . ++$key);
            $grandTotal = $this->calculateGrandTotal($reservaiton);
            // dd($grandTotal);
            if ($grandTotal > 0) {
                sleep(5);
                if ($this->hasValidPaymentSettings($facility)) {
                    $this->processPayment($facility, $reservaiton, $grandTotal, $current_time);
                } else {
                    $this->log->info("Payment Settings not Found for this Facility");
                }
            } else {
                $this->handleUnpaidReservation($reservaiton, $reservaiton->total, $current_time);
            }
            $this->log->info("Reservation charge End");
        }
    }

    private function hasValidPostChargeFacility($facility)
    {
        if (isset($facility->facilityConfiguration) && $facility->facilityConfiguration->pre_post_charge_reservation) {
            return true;
        }
        return false;
    }

    private function getPendingReservations($facilityId, $current_time)
    {
        $query = Reservation::where('facility_id', $facilityId)
            ->whereNull('anet_transaction_id')
            ->whereNull('cancelled_at')
            ->where(function ($query) use ($current_time) {
                $query->where('end_timestamp', '<', $current_time);
            })
            ->whereIn('is_hub_zeag', [2, 3])
            ->where('is_charged', '0');

        // Check for the optional ticketech_code argument
        if ($this->option('ticketech_code')) {
            $query->where('ticketech_code', $this->option('ticketech_code'));
            // $query->whereIn('ticketech_code', ['PO406927', 'PO836164', 'PO378934', 'PO766360', 'PO315410', 'PO248108', 'PO514978', 'PO826894', 'PO742215', 'PO730648', 'PO779305', 'PO120523', 'PO629351']);
            // $query->whereIn('ticketech_code', ['PO248108','PO754018','PO980306','PO953758','PO569974']);
            // $query->whereIn('ticketech_code', ['PO610744', 'PO840867', 'PO512134', 'PO419883', 'PO629351', 'PO120553', 'PO283306', 'PO899165', 'PO987379', 'PO703806', 'PO120523', 'PO754018', 'PO980306',  'PO532423',  'PO953758', 'PO609338', 'PO961784', 'PO569974', 'PO248108',  'PO742215',  'PO596746', 'PO514978', 'PO176239', 'PO366812', 'PO378934']);
            #, 'PO836164','PO730648','PO195053','PO533016','PO903839',


            // $query->whereIn('ticketech_code', ['PO228310', 'PO881226', 'PO495946', 'PO509709', 'PO460234', 'PO410959', 'PO387182', 'PO840166', 'PO690254', 'PO244156', 'PO303738', 'PO779305', 'PO459761', 'PO826894', 'PO653150', 'PO261615', 'PO315410', 'PO766360', 'PO788637', 'PO294620', 'PO406927', 'PO655005', 'PO395698']);
            // Charged : 10-12-2024
            // $query->whereIn('ticketech_code', ['PO616976', 'PO738165', 'PO311982', 'PO827152', 'PO192038', 'PO369905', 'PO390891', 'PO717084', 'PO505833', 'PO147499', 'PO754300', 'PO341586', 'PO107642', 'PO191884','PO729802']);
            // 13-12-2024
            // $query->whereIn('ticketech_code', ['PO460234', 'PO387182', 'PO509709', 'PO410959', 'PO495946', 'PO840166', 'PO878315', 'PO689675', 'PO963042', 'PO589374']);
            // $query->whereIn('ticketech_code', ['PO495946', 'PO509709', 'PO460234', 'PO410959', 'PO387182', 'PO840166']);
        }

        // Fetch reservations with the specified conditions
        $reservation = $query->orderBy('id', 'desc')
            ->limit(100)
            ->get();

        return $reservation;
    }


    private function calculateGrandTotal($reservaiton)
    {
        $grandTotal = 0;
        if ($reservaiton->total == 0) {
            $this->log->info("Amount for charge: " . $grandTotal);
            return $grandTotal;
        }
        $grandTotal = $reservaiton->total - $reservaiton->charge_amount;
        $paymentEnv = isset($facility->FacilityPaymentDetails->heartland_payment_env) ? $facility->FacilityPaymentDetails->heartland_payment_env : config('parkengage.HEARTLAND_PAYMENT_ENV');
        $grandTotal = ($paymentEnv == 'test') ? '3.00' : $grandTotal;
        $this->log->info("Amount for charge: " . $grandTotal);
        return $grandTotal;
    }

    private function hasValidPaymentSettings($facility)
    {
        if (!isset($facility->FacilityPaymentDetails) || empty($facility->FacilityPaymentDetails)) {
            return false;
        }
        $this->log->info("Payment Gateway: " . json_encode($facility->FacilityPaymentDetails->facility_payment_type_id));
        return 4;
        return $facility->FacilityPaymentDetails->facility_payment_type_id;
    }


    private function processPayment($facility, $reservaiton, $grandTotal, $current_time)
    {
        $cardCheck = $this->getCardTransaction($reservaiton->id);
        // dd($cardCheck);
        $this->log->info("Card details: " . json_encode($cardCheck));
        if ($cardCheck) {
            $finalAmount = $this->calculateFinalAmount($grandTotal, $reservaiton->charged_amount);
            // dd($finalAmount);
            if ($finalAmount > 0) {
                $this->attemptCardPayment($facility, $reservaiton, $cardCheck, $finalAmount, $current_time);
                $this->log->info("Reservation Charged: " . $reservaiton->ticketech_code);
            } else {
                $this->markReservationAsClosed($reservaiton, $current_time);
                $this->markCardTransactionComplete($cardCheck);
            }
        } else {
            $this->markReservationAsClosed($reservaiton, $current_time);
        }
    }

    private function getCardTransaction($reservaitonId)
    {
        return DatacapTransaction::whereNull('deleted_at')
            ->where('reservation_id', $reservaitonId)
            ->where('is_payment_complete', '0')
            ->where('transaction_retry', '<', '3')
            ->first();
    }

    private function calculateFinalAmount($grandTotal, $chargedAmount)
    {
        return number_format($grandTotal - ($chargedAmount ?? 0), 2);
    }

    private function attemptCardPayment($facility, $reservaiton, $cardCheck, $amount, $current_time)
    {
        $paymentRequest = $this->createPaymentRequest($cardCheck, $amount);
        $this->log->info("Payment Request: " . json_encode($paymentRequest));

        $paymentResponse = $this->handleDeviceSpecificPayment($reservaiton, $facility, (object)$paymentRequest, $cardCheck);

        // $paymentResponse = json_decode($paymentResponse, true);    // On For Datacap
        // $this->log->info("Payment Request 2: " . json_encode($paymentResponse));

        // if (is_array($paymentResponse) && $this->hasValidPaymentSettings($facility) && $this->hasValidPaymentSettings($facility) == '2') {
        //     $paymentResponse['responseMessage'] = $paymentResponse['Message'];;
        //     $paymentResponse = (object)$paymentResponse;
        // }
        if (is_array($paymentResponse) && $this->hasValidPaymentSettings($facility) && $this->hasValidPaymentSettings($facility) == '4') {
            $paymentResponse['responseMessage'] = $paymentResponse['Message'];;
            $paymentResponse = (object)$paymentResponse;
        }


        $this->log->info("Payment Response: " . json_encode($paymentResponse));
        // return true;

        $message = "";
        if (isset($paymentResponse->responseMessage)) {
            $message = $paymentResponse->responseMessage;
        } else if (isset($paymentResponse->Message)) {
            $message = $paymentResponse->Message;
        }

        // dd($paymentResponse && isset($message) && in_array($message, ['Success', 'APPROVAL', 'APPROVED']), $message);
        if ($paymentResponse && isset($message) && in_array($message, ['Success', 'APPROVAL', 'APPROVED', 'COMPLETED'])) {
            $this->finalizePaymentTransaction($reservaiton, $cardCheck, $paymentResponse, $current_time, $amount, $facility);
            $this->log->info("finalizePaymentTransaction :");
        } else {
            $this->incrementRetryCount($cardCheck);
            $this->log->info("Retry Cound Increment");
        }
    }

    private function createPaymentRequest($cardCheck, $amount)
    {
        return [
            'transactionId' => $cardCheck->trans_id,
            'token' => $cardCheck->token,
            'Amount' => $amount
        ];
    }

    private function handleDeviceSpecificPayment($reservaiton, $facility, $paymentRequest, $cardCheck)
    {
        $this->log->info("handleDeviceSpecificPayment : ");
        $this->log->info("Device Type       : " . json_encode($reservaiton->device_type));
        // $this->log->info("Facility          : " . json_encode($facility));
        $this->log->info("paymentRequest    : " . json_encode($paymentRequest));
        $this->log->info("cardCheck         : " . json_encode($cardCheck));
        // return true;
        if ($reservaiton->device_type === "web") {
            try {
                // $paymentResponse = HeartlandPaymentGateway::chargePreauthCardPayment($paymentRequest, $facility);
                $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByTokenIM30($paymentRequest, $facility);
                $this->log->info("paymentResponse         : " . json_encode($paymentResponse));
                return $paymentResponse;
            } catch (\Throwable $th) {
                $this->log->error("Error         : {$th->getMessage()} File : {$th->getFile()} - Line : {$th->getLine()}  ");
            }
            // return true;
            // return HeartlandPaymentGateway::chargePreauthCardPayment($paymentRequest, $facility);

            // if ($facility->FacilityPaymentDetails->facility_payment_type_id == '4') {
            //     return HeartlandPaymentGateway::chargePreauthCardPayment($paymentRequest, $facility);
            // }

            /*   if ($facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                $data['Amount'] =  $paymentRequest->Amount;
                $data['Token'] = $paymentRequest->token;
                $data["CardHolderID"] = "Allow_V2";
                $data["ref_id"] = $cardCheck->ref_id;
                // return DatacapPaymentGateway::makePaymentDataCapCharge($data, $facility);
                return $this->makePaymentDataCapCharge($data, $facility);
            } */
        }
        return null;
    }

    private function finalizePaymentTransaction($reservaiton, $cardCheck, $paymentResponse, $current_time, $amount, $facility)
    {
        $transaction = $this->saveTransactionData($cardCheck, $paymentResponse, $reservaiton->user_id, $amount, $facility);
        if (is_array($transaction)) {
            $transaction = (object)$transaction;
        }
        $this->log->info("Save Transaction: " . json_encode($transaction));
        $this->log->info("Payment Success Reservation ID : {$reservaiton->user_id} Reservation PO : {$reservaiton->ticketech_code} : Amount : {$amount} Payment Status {$paymentResponse->responseCode} : AuthCode {$paymentResponse->transactionReference->authCode} ");
        $reservaiton->anet_transaction_id = $transaction->id;

        if ($this->hasValidPaymentSettings($facility) == '2')
            $reservaiton->payment_token = $paymentResponse->Token;

        $reservaiton->is_charged = '1';
        $reservaiton->charged_at = $current_time;
        $reservaiton->charged_amount = $amount;
        $reservaiton->save();

        $cardCheck->is_payment_complete = '1';
        $cardCheck->transaction_retry++;
        $cardCheck->save();


        //add ref key 
        // QueryBuilder::setReferenceKey($transaction->id, $reservaiton->ticketech_code);
        // QueryBuilder::setAllTransactions(json_encode($paymentResponse), 'Heartland', $reservaiton->ticketech_code);
        //add ref key 
    }

    private function saveTransactionData($cardCheck, $paymentResponse, $userId, $amount, $facility)
    {
        $trans_id = $cardCheck->trans_id;
        if ($this->hasValidPaymentSettings($facility) == '2') {
            $trans_id = $cardCheck->ref_id;
        }

        try {
            $request = [
                'total' => $amount,
                'card_last_four' => $cardCheck->card_last_four,
                'card_type' => $cardCheck->card_type,
                'expiration' => $cardCheck->expiry,
                'expiration_date' => $cardCheck->expiry,
                'pre_auth_transctions' => $trans_id,
                'name_on_card' => $cardCheck->name,
            ];

            $this->log->info("Request: " . json_encode($request) . "Payment details: " . $paymentResponse  . "User id: " . $userId);
            // dd($request, $paymentResponse, $userId);
            if ($this->hasValidPaymentSettings($facility) == '2') {
                return DatacapPaymentGateway::saveDatacapTransaction((object)$request, (array)$paymentResponse, $userId, false);
            } else if ($this->hasValidPaymentSettings($facility) == '4') {
                return HeartlandPaymentGateway::saveTransaction((object)$request, $paymentResponse, $userId);
            }
        } catch (\Throwable $th) {
            $this->log->info("Failed on Save Transtion: " . $th->getMessage());
        }
    }

    private function incrementRetryCount($cardCheck)
    {
        $cardCheck->transaction_retry++;
        $cardCheck->save();
    }

    private function markReservationAsClosed($reservaiton, $current_time)
    {
        $reservaiton->is_charged = 1;
        $reservaiton->charged_at = $current_time;
        $reservaiton->save();
    }

    private function markCardTransactionComplete($cardCheck)
    {
        $cardCheck->is_payment_complete = '1';
        $cardCheck->transaction_retry++;
        $cardCheck->save();
    }

    private function handleUnpaidReservation($reservaiton, $grandTotal, $current_time)
    {
        $finalAmount = $this->calculateFinalAmount($grandTotal, $reservaiton->charged_amount);
        if (isset($reservaiton->promocode) && !empty($reservaiton->promocode) && $reservaiton->total == "0.00") {
            $this->markReservationAsClosed($reservaiton, $current_time);
        }
        if ($finalAmount == "0.00") {
            $this->markReservationAsClosed($reservaiton, $current_time);
        }
    }

    private function setCustomTimezone($facility)
    {
        if ($facility && !empty($facility->timezone)) {
            date_default_timezone_set($facility->timezone);
            return;
        }

        $partnerTimezone = OauthClient::where('partner_id', $facility->owner_id)
            ->with('userPaymentGatewayDetail')
            ->first()
            ->userPaymentGatewayDetail ?? null;

        if ($partnerTimezone && !empty($partnerTimezone->timezone)) {
            date_default_timezone_set($partnerTimezone->timezone);
        }
    }

    private function makePaymentDataCapCharge($data, $facility)
    {
        $mid = $facility->FacilityPaymentDetails->datacap_ecommerce_mid;
        $ref_id = $data['ref_id'];
        $vars = json_encode($data);
        $url = $facility->FacilityPaymentDetails->datacap_preauth_url;

        // dd($mid, $ref_id, $vars);
        $this->log->info("DataCap : Payment Request Data --" . $vars);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        // $this->log->info("Payment Request Header --" . json_encode($headers));
        // $this->log->info("Payment Request URL --" . $url . $ref_id);
        $url = "https://pay.dcap.com/v1/credit/sale/";
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, 1);

        // curl_setopt($curl, CURLOPT_URL, $url . $ref_id);
        // curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);

        curl_close($curl);
        $this->log->info("DataCap : Payment Data --" . json_encode($response));

        return $response;
    }
}
