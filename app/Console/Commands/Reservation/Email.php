<?php

namespace App\Console\Commands\Reservation;

use Mail;
use Storage;
use File;
use Illuminate\Console\Command;
use App\Models\Reservation;
use App\Exceptions\NotFoundException;
use App\Http\Helpers\QueryBuilder;
use Intervention\Image\Facades\Image as Image;
use View;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\BrandSetting;
use App\Models\UserPass;
use DB;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\Cruise;
use App\Models\ParkEngage\CruiseSchedule;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\Facility;
use App\Models\User;
use Carbon\Carbon;
use App\Models\ParkEngage\FacilityConfiguration;

/**
 * Emails reservation stub to user
 */
class Email extends Command
{


  protected $successLog;
  protected $errorLog;
  protected $request;
  protected $log;
  protected $options;


  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'reservation:email
                            {reservationId}
                            {--email= : Email address to send to. Will be sent to reservation user if not provided.}
                            {--client_secret= : Client secret for partner.}
                            {--body= : optional body content for reminder}
                            {--flag= : optional identify content for 24 hours reminder}
                            {--subject= : optional dynamic subject}
                            {--request= : optional request data for email}';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Email reservation confirmation to user.';


  public  function __construct()
  {
    parent::__construct();
    $logFactory = new LoggerFactory();
    $this->log = $logFactory->setPath('logs/roc/email')->createLogger('email_status');
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
    try {

      $this->log->info('Reservation Mail Start');
      $request = $this->option('request') ?? '';
      $reservation = Reservation::find($this->argument('reservationId'));
      if (!$reservation) {
        throw new NotFoundException('No reservation with that ID.');
      }

      $this->log->info('======== Reachecd 44 ===========');

      $cruise = Cruise::select('id', 'cruise_name')->where('partner_id', $reservation->partner_id)->where('id', $reservation->cruise_id)->first();
      if ($cruise) {
        $reservation->cruise_name = isset($cruise->cruise_name) ? $cruise->cruise_name : '';
        //CruiseSchedule
        $cruiseSchedule = CruiseSchedule::where('cruise_id', $cruise->id)->where('is_active', 1)->whereNull('deleted_at')->first();
        $reservation->cruiseSchedule = isset($cruiseSchedule) ? $cruiseSchedule : '';
      }

      $this->log->info('======== Reachecd 55 ===========');

      $brand_setting = FacilityBrandSetting::where('facility_id', $reservation->facility_id)->first();
      if ($brand_setting) {
        $reservation->facility_logo_id = $brand_setting->id;
      } else {
        $settings = BrandSetting::where('user_id', $reservation->partner_id)->first();
        $reservation->logo_id = $settings->id;
      }

      $this->log->info('======== Reachecd 66 ===========');


      $client_secret = $this->option('client_secret') != '' ? $this->option('client_secret') : '';

      $image = $reservation->generateStubJpg($client_secret);

      $this->log->info('======== Reachecd 77 ===========');

      $imageBarcode = $reservation->generateBarcodeJpgNew($client_secret);

      $reservationVal = $reservation->getReservationVal();

      $this->log->info('======== Reachecd 11 ===========');

      $reservationFileName = str_random(10) . '_reservation.jpg';
      Storage::put($reservationFileName, $image);

      $this->log->info('======== Reachecd 22 ===========');

      $imageBarcodeFileName = str_random(10) . '_barcode.png';
      Storage::put($imageBarcodeFileName, $imageBarcode);

      $this->log->info('======== Reachecd 33 ===========');

      $facilityimage = '';
      $noImage = (object)[];
      if (isset($reservationVal['photo']->image_name)) {
        $facilityimagePath = storage_path('app/' . $reservationVal['photo']->image_name);
        $destinationPath = public_path('/assets/thumbnail/');

        $current = File::get(storage_path('app/' . $reservationVal['photo']->image_name));

        $facilityimage = str_random(10) . '_facilityimg.jpg';

        $img = Image::make($facilityimagePath);
        $img->resize(300, 162, function ($constraint) {
          // $constraint->aspectRatio();
        })->save(storage_path('app/' . $facilityimage));
        Storage::put('testi_91_testtttttttt.jpg', $current);
      } else {
        $this->log->info('======== NO Image Found ===========');
        $noImage->image_name = "no-image-found.jpg";
        $noImage->url = env('APP_URL') . "/facility-logo/no-image-found.jpg";
      }

      $this->log->info('Reservation Mail Sent Reached' . json_encode($noImage));


      $email = $this->option('email') ?: $reservation->user->email;

      $this->log->info('Reservation Mail Sent to ' . $email);
      $warning_on_reservation = $reservation->warning_on_reservation;
      $warning_on_reservation_msg = '';

      if ($warning_on_reservation) {
        $warning_on_reservation_msg = $reservation->warning_on_reservation_msg;
      }
      $is_end_time_updated = isset($reservation->is_end_time_updated) ? $reservation->is_end_time_updated : 0;


      $userpass = [];
      $reservation1 = DB::select("Select id,user_pass_id from reservations where id=" . $reservation->id);
      if ($reservation->user_pass_id != '') {
        $userpass = UserPass::find($reservation->user_pass_id);
      }

      $facility =  Facility::select('id', 'owner_id', 'is_service_update')->with(['facilityConfiguration'])->where('id', $reservation->facility_id)->first();

      $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
      if (in_array($checkPaymentUrl->user_id, [config('parkengage.PARTNER_PCI'), config('parkengage.PARTNER_MAPCO')])) {
        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
          $join->on('user_facilities.user_id', '=', 'users.id');
          $join->where('user_facilities.facility_id', "=", $facility->id);
        })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
        $slug = ($getRM->slug) ? $getRM->slug : '';
      } else {
        $slug = ($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : 'pay';
      }

      $facilityConfig = $facility->facilityConfiguration;
      $url = "";
      if (isset($facilityConfig->base_url)) {
        $url = $facilityConfig->base_url;
        $this->log->info('Url found in FacilityConfigruation ' . $url);
      } else {
        $url = env('CANCEL_RESERVATION');
        $this->log->info('Url found in ENV ' . $url);
      }

      if (isset($facilityConfig)) {
        if ($facility->is_service_update) {
          $this->log->info('Reached is_service_update');
          // Applied Tax 
          // $reservation->tax_fee = $reservation->getAppliedTaxFees($reservation->id);

          $this->log->info('Reservation Tax_fee ' . $reservation->tax_fee);

          $edit_url = "$url/$slug/reservation/" . base64_encode($reservation->id);
          $reservation->edit_url = $edit_url;

          $cancel_url = "$url/$slug/cancel/" . base64_encode($reservation->id);
          $reservation->cancel_url = $cancel_url;

          // Mobile number format
          $phone = QueryBuilder::formatPhoneNumber(substr($reservationVal['user']->phone, -10), false);
          $reservationVal['user']->phone = $phone;

          if (isset($reservationVal['facility']->phone_number)) {
            $phone = QueryBuilder::formatPhoneNumber(substr($reservationVal['facility']->phone_number, -10), false);
            $reservationVal['facility']->phone_number = $phone;
          }
        } else if ($facilityConfig->is_reservation_edit == '1') {
          $edit_url = "$url/$slug/reservation/" . base64_encode($reservation->id);
          $reservation->edit_url = $edit_url;
        }
      }

      $updateReservation =  false;
      if ($reservation->facility_id == config('parkengage.ROC_FACILITY')) {
        if (isset($request['is_edit']) && !empty($request['is_edit'])) {
          $updateReservation = true;
        }
      }

      $this->log->info('Update Resservation status: ' . json_encode($updateReservation));

      if ($reservation->cruise_id != '' && $reservation->schedule_id != '') {
        $this->log->info('======== Reachecd cruise_id found ===========');
        $cancel_url = "$url/$slug/cancel/" . base64_encode($reservation->id);
        $reservation->cancel_url = $cancel_url;
      }


      //added exit time for cruise schedule
      if ($reservation->cruise_id != '' && $reservation->schedule_id != '') {
        $end_time = Carbon::parse($reservation->start_timestamp)->addHours($reservation->length);
        if (intval($reservation->length) != $reservation->length) {
          $timarr = explode('.', $reservation->length);
          $end_time->addMinutes($timarr[1]);
        }
        $end_time = $end_time->format('Y-m-d H:i:s');
        $reservation->cruise_end_time = $end_time;
      }

      //code by Alka
      $this->log->info('=== Notification Reached 1 ===');
      $body = $this->option('body') ?: '';
      $this->log->info('=== Notification Reached 2 ===' . json_encode($body));

      $this->log->info('=== Notification flag 1 ===');
      $flag = $this->option('flag') ?: 0;
      $this->log->info('=== Notification flag 2 ===' . json_encode($flag));

      $subject = "Your Parking Reservation Confirmation";
      if (isset($body) && $body != '') {
        $subject = $this->option('subject') ?: '';
        $this->log->info('=== Subject for CheckIn/Out ===' . $subject);
      }
      //End

      $brand_setting = BrandSetting::where('user_id', $reservation->user->created_by)->first();
      //sending email different format
      if ($client_secret != '') {
        $view = 'reservation.email-atlanta';
        $view_text = $view . '-plain-text';
      } else {
        $view = 'reservation.email';
        $view_text = $view . '-plain-text';
      }

      if (!View::exists($view_text)) {
        $view_text = $view;
      }

      // $this->log->info('Reservation Data : ' . json_encode($reservation));
      $this->log->info('Reservation Mail reached at View level ' . $view_text);

      $pathofImages = $reservationVal['photo'] ?? $noImage;

      Mail::send(
        ['text' => $view_text, 'html' => $view],
        [
          'image_path' => $reservationFileName,
          'warning_on_reservation_msg' => $warning_on_reservation_msg,
          'bar_image_path' => $imageBarcodeFileName,
          'facility_image_path' => $facilityimage,
          'photo' => $pathofImages,
          'reservation' => $reservation,
          'addressLink' => $reservation->facility->generateAddressLink(),
          'reservationVal' => $reservationVal,
          'is_end_time_updated' => $is_end_time_updated,
          'client_secret' => $client_secret,
          'brand_setting' => $brand_setting,
          'user_pass' => $userpass,
          'reservation1' => $reservation1,
          'facility' => $facility,
          'body' => $body,
          'flag' => $flag,
          'is_edit' => $updateReservation,
          'email_footer' => isset($facilityConfig->facility_footer_email) ? $facilityConfig->facility_footer_email : ""
        ],
        function ($message) use ($email, $image, $subject) {
          $message->to($email)->subject($subject);
          $message->from(config('inventory.email'));
          $message->attachData($image, "reservation.jpg");
        }
      );

      $this->log->info('Reservation Mail Sent');
      Storage::delete($reservationFileName);
      Storage::delete($facilityimage);
      //Storage::delete($imageBarcodeFileName);
    } catch (\Exception $e) {
      $errorMessage = array();
      $errorMessage['message'] = $e->getMessage();
      $errorMessage['line number'] = $e->getLine();
      $errorMessage['file'] = $e->getFile();
      $this->log->info('Issue in email sending' . json_encode($errorMessage));
    }
  }
}
