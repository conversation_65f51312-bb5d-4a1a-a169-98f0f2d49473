<?php

namespace App\Console\Commands\Reservation;

use App\Classes\DatacapPaymentGateway;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\DatacapTransaction;
use App\Classes\HeartlandPaymentGateway;
use App\Models\Reservation;
use App\Http\Helpers\QueryBuilder;

/**
 * Emails reservation stub to user
 */
class ReservationAutoPayPaymentHeartland extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'roc:reservation-autopay-heartland {--ticketech_code= : If want to run for specific reservation.}';


    /**
     * The console command description.
     *
     * @var string
     */

    const IS_GATED_FACILITY = '0';

    public $log;
    protected $partnerId;
    protected $facilityId;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/roc/payment')->createLogger('autopay-heartland');
        $this->partnerId = config('parkengage.PARTNER_MAPCO');
        $this->facilityId = config('parkengage.ROC_FACILITY');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->log->info("Payment charge start for Facility id: {$this->facilityId} and Partner id: {$this->partnerId}");
        $facility = Facility::with(['FacilityPaymentDetails', 'facilityConfiguration'])
            ->where([
                'active' => '1',
                'id' => $this->facilityId,
                'owner_id' => $this->partnerId,
            ])
            ->first();

        if (!$facility) {
            $this->log->info("Facility Data Not Found");
            return;
        }

        if (!$this->hasValidPostChargeFacility($facility)) {
            $this->log->info("Facility not avaiable for post charge");
            return;
        }

        $this->setCustomTimezone($facility);
        $current_time = date('Y-m-d H:i:s');
        $this->log->info("Current Time Capture Data: " . $current_time);

        $reservations = $this->getPendingReservations($facility->id, $current_time);
        // dd($reservations->count());
        if (!($reservations->count() > 0)) {
            $this->log->info("No reservation found for charge");
            return;
        }

        $this->log->info("Number reservation found: " . json_encode($reservations->count()));
        foreach ($reservations as $key => $reservation) {
            $this->log->info("Reservation charge Start : " . $reservation->ticketech_code . " and Reservation id: " . $reservation->id . " Run count: " . ++$key);
            $grandTotal = $this->calculateGrandTotal($reservation);
            if ($grandTotal > 0) {
                if ($this->hasValidPaymentSettings($facility)) {
                    $this->processPayment($facility, $reservation, $grandTotal, $current_time);
                } else {
                    $this->log->info("Payment Settings not Found for this Facility");
                }
            } else {
                $this->handleUnpaidReservation($reservation, $reservation->total, $current_time);
            }
            $this->log->info("Reservation charge End");
        }
    }

    private function hasValidPostChargeFacility($facility)
    {
        if (isset($facility->facilityConfiguration) && $facility->facilityConfiguration->pre_post_charge_reservation) {
            return true;
        }
        return false;
    }

    private function getPendingReservations($facilityId, $current_time)
    {
        $query = Reservation::where('facility_id', $facilityId)
            ->whereNull('anet_transaction_id')
            ->whereNull('cancelled_at')
            ->whereIn('is_hub_zeag', [1, 2, 3])
            //  ->whereDate('created_at', '>=', '2025-01-08')
            ->where('is_charged', '0');

        // Add condition for `end_timestamp` if `ticketech_code` is not set
        if (!$this->option('ticketech_code')) {
            $query->where('end_timestamp', '<', $current_time);
        }

        // Add condition for `ticketech_code` if it is set
        if ($this->option('ticketech_code')) {
            $query->where('ticketech_code', $this->option('ticketech_code'));
        }

        // Fetch reservations with the specified conditions
        return $query->orderBy('id', 'desc')->get();
    }



    private function calculateGrandTotal($reservation)
    {
        $grandTotal = 0;
        if ($reservation->total == 0) {
            $this->log->info("Amount for charge: " . $grandTotal);
            return $grandTotal;
        }
        $grandTotal = $reservation->total - $reservation->charge_amount;
        $paymentEnv = isset($facility->FacilityPaymentDetails->heartland_payment_env) ? $facility->FacilityPaymentDetails->heartland_payment_env : config('parkengage.HEARTLAND_PAYMENT_ENV');
        $grandTotal = ($paymentEnv == 'test') ? '3.00' : $grandTotal;
        $this->log->info("Amount for charge: " . $grandTotal);
        return $grandTotal;
    }

    private function hasValidPaymentSettings($facility)
    {
        if (!isset($facility->FacilityPaymentDetails) || empty($facility->FacilityPaymentDetails)) {
            return false;
        }
        $this->log->info("Payment Gateway: " . json_encode($facility->FacilityPaymentDetails->facility_payment_type_id));
        return $facility->FacilityPaymentDetails->facility_payment_type_id;
    }


    private function processPayment($facility, $reservation, $grandTotal, $current_time)
    {
        $cardCheck = $this->getCardTransaction($reservation->id);
        // dd($cardCheck);
        $this->log->info("Card details: " . json_encode($cardCheck));
        if ($cardCheck) {
            $finalAmount = $this->calculateFinalAmount($grandTotal, $reservation->charged_amount);
            // dd($finalAmount);
            if ($finalAmount > 0) {
                $this->attemptCardPayment($facility, $reservation, $cardCheck, $finalAmount, $current_time);
                $this->log->info("Reservation Charged: " . $reservation->ticketech_code);
            } else {
                $this->markReservationAsClosed($reservation, $current_time);
                $this->markCardTransactionComplete($cardCheck);
            }
        } else {
            // $this->markReservationAsClosed($reservation, $current_time);
            $this->handleUnpaidReservation($reservation, $reservation->total, $current_time);
        }
    }

    private function getCardTransaction($reservationId)
    {
        return DatacapTransaction::whereNull('deleted_at')
            ->where('reservation_id', $reservationId)
            ->where('is_payment_complete', '0')
            ->where('transaction_retry', '<', '3')
            ->first();
    }

    private function calculateFinalAmount($grandTotal, $chargedAmount)
    {
        return number_format($grandTotal - ($chargedAmount ?? 0), 2);
    }

    private function attemptCardPayment($facility, $reservation, $cardCheck, $amount, $current_time)
    {
        $paymentRequest = $this->createPaymentRequest($cardCheck, $amount);
        $this->log->info("Payment Request: " . json_encode($paymentRequest));

        $paymentResponse = $this->handleDeviceSpecificPayment($reservation, $facility, (object)$paymentRequest, $cardCheck);

        $paymentResponse = json_decode($paymentResponse, true);

        if (is_array($paymentResponse) && $this->hasValidPaymentSettings($facility) && $this->hasValidPaymentSettings($facility) == '2') {
            $paymentResponse['responseMessage'] = $paymentResponse['Message'];;
            $paymentResponse = (object)$paymentResponse;
        }


        $this->log->info("Payment Response: " . json_encode($paymentResponse));


        $message = "";
        if (isset($paymentResponse->responseMessage)) {
            $message = $paymentResponse->responseMessage;
        } else if (isset($paymentResponse->Message)) {
            $message = $paymentResponse->Message;
        }

        // dd($paymentResponse && isset($message) && in_array($message, ['Success', 'APPROVAL', 'APPROVED']), $message);
        if ($paymentResponse && isset($message) && in_array($message, ['Success', 'APPROVAL', 'APPROVED', 'COMPLETED'])) {
            $this->finalizePaymentTransaction($reservation, $cardCheck, $paymentResponse, $current_time, $amount, $facility);
            $this->log->info("finalizePaymentTransaction :");
        } else {
            $this->incrementRetryCount($cardCheck);
            $this->log->info("Retry Cound Increment");
        }
    }

    private function createPaymentRequest($cardCheck, $amount)
    {
        return [
            'transactionId' => $cardCheck->trans_id,
            'token' => $cardCheck->token,
            'Amount' => $amount
        ];
    }

    private function handleDeviceSpecificPayment($reservation, $facility, $paymentRequest, $cardCheck)
    {
        $this->log->info("Device Type: " . json_encode($reservation->device_type));
        if ($reservation->device_type === "web") {
            if ($facility->FacilityPaymentDetails->facility_payment_type_id == '4') {
                return HeartlandPaymentGateway::chargePreauthCardPayment($paymentRequest, $facility);
            }

            if ($facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                $data['Amount'] =  $paymentRequest->Amount;
                $data['Token'] = $paymentRequest->token;
                $data["CardHolderID"] = "Allow_V2";
                $data["ref_id"] = $cardCheck->ref_id;
                $token_expire = false;
                if ($reservation->created_at->addDays(7)->isPast()) {
                    $token_expire = true;
                    $this->log->info("Charged by Sale");
                }
                // dd($token_expire);
                return DatacapPaymentGateway::makePaymentDataCapCharge($data, $facility, $token_expire);
            }
        }
        return null;
    }

    private function finalizePaymentTransaction($reservation, $cardCheck, $paymentResponse, $current_time, $amount, $facility)
    {
        $transaction = $this->saveTransactionData($cardCheck, $paymentResponse, $reservation->user_id, $amount, $facility);
        if (is_array($transaction)) {
            $transaction = (object)$transaction;
        }
        $this->log->info("Save Transaction: " . json_encode($transaction));
        $reservation->anet_transaction_id = $transaction->id;

        if ($this->hasValidPaymentSettings($facility) == '2')
            $reservation->payment_token = $paymentResponse->Token;

        $reservation->is_charged = '1';
        $reservation->charged_at = $current_time;
        $reservation->charged_amount = $amount;
        $reservation->save();

        $cardCheck->is_payment_complete = '1';
        $cardCheck->transaction_retry++;
        $cardCheck->save();


        //add ref key 
        // QueryBuilder::setReferenceKey($transaction->id,$reservation->ticketech_code);
        // QueryBuilder::setAllTransactions(json_encode($paymentResponse),'Heartland',$reservation->ticketech_code);
        //add ref key 
    }

    private function saveTransactionData($cardCheck, $paymentResponse, $userId, $amount, $facility)
    {
        $trans_id = $cardCheck->trans_id;
        if ($this->hasValidPaymentSettings($facility) == '2') {
            $trans_id = $cardCheck->ref_id;
        }
        try {
            $request = [
                'total' => $amount,
                'card_last_four' => $cardCheck->card_last_four,
                'card_type' => $cardCheck->card_type,
                'expiration' => $cardCheck->expiry,
                'expiration_date' => $cardCheck->expiry,
                'pre_auth_transctions' => $trans_id,
                'name_on_card' => $cardCheck->name,
            ];

            // dd($request, $paymentResponse, $userId);
            $this->log->info("Request: " . json_encode($request) . " User id: " . $userId);
            if ($this->hasValidPaymentSettings($facility) == '2') {
                return DatacapPaymentGateway::saveDatacapTransaction((object)$request, (array)$paymentResponse, $userId, false);
            } else if ($this->hasValidPaymentSettings($facility) == '4') {
                return HeartlandPaymentGateway::saveTransaction((object)$request, $paymentResponse, $userId);
            }
        } catch (\Throwable $th) {
            $this->log->info("Failed on Save Transtion: " . $th->getMessage());
        }
    }

    private function incrementRetryCount($cardCheck)
    {
        $cardCheck->transaction_retry++;
        $cardCheck->save();
    }

    private function markReservationAsClosed($reservation, $current_time)
    {
        $reservation->is_charged = 1;
        $reservation->charged_at = $current_time;
        $reservation->save();
    }

    private function markCardTransactionComplete($cardCheck)
    {
        $cardCheck->is_payment_complete = '1';
        $cardCheck->transaction_retry++;
        $cardCheck->save();
    }

    private function markReservationAsFailed($reservation, $current_time)
    {
        $reservation->is_charged = 2;
        $reservation->charged_at = $current_time;
        $reservation->save();
    }

    private function handleUnpaidReservation($reservation, $grandTotal, $current_time)
    {
        $finalAmount = $this->calculateFinalAmount($grandTotal, $reservation->charged_amount);
        if (isset($reservation->promocode) && !empty($reservation->promocode) && $reservation->total == "0.00") {
            $this->markReservationAsClosed($reservation, $current_time);
        }
        if ($finalAmount == "0.00") {
            $this->markReservationAsClosed($reservation, $current_time);
        }

        if ($finalAmount > 0) {
            $this->markReservationAsFailed($reservation, $current_time);
        }
    }

    private function setCustomTimezone($facility)
    {
        if ($facility && !empty($facility->timezone)) {
            date_default_timezone_set($facility->timezone);
            return;
        }

        $partnerTimezone = OauthClient::where('partner_id', $facility->owner_id)
            ->with('userPaymentGatewayDetail')
            ->first()
            ->userPaymentGatewayDetail ?? null;

        if ($partnerTimezone && !empty($partnerTimezone->timezone)) {
            date_default_timezone_set($partnerTimezone->timezone);
        }
    }
}
