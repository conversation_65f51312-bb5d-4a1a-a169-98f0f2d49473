<?php

namespace App\Console\Commands\Reservation;

use Mail;

use Illuminate\Console\Command;

use App\Models\Reservation;

use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\BrandSetting;
use App\Models\UserPass;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\OauthClient;

/**
 * Emails reservation stub to user
 */
class EmailCancellation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reservation:email-cancellation
                            {reservationId}
                            {--email= : Email address to send to. Will be sent to reservation user if not provided.}
                            {--client_secret= : Client secret for partner.}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email reservation cancelation confirmation to user.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/zooatlanta-cancel-email')->createLogger('zooatlanta-cancel-email');
    }
    public function handle()
    {
        $reservation = Reservation::find($this->argument('reservationId'));
		$userpass=[];
		if($reservation->user_pass_id !='')
		{
			$userpass = UserPass::find($reservation->user_pass_id); 
		}
        $client_secret = $this->option('client_secret')!= '' ?$this->option('client_secret') : '';
        if (!$reservation) {
            throw new NotFoundException('No reservation with that ID.');
        }
        
        
        if ($reservation->status !== 'Cancelled') {
            throw new ApiGenericException('Reservation is not canceled, cannot send cancelation confirmation.');
        }
        if(!$client_secret){
			$clientSecret = OauthClient::where("partner_id", $reservation->partner_id)->first();
			$client_secret = $clientSecret->secret;
		}
		$reservation->diff_in_days = $reservation->length/24;
        
        $email = $this->option('email') ?: $reservation->user->email;
        
        $brand_setting = FacilityBrandSetting::where('facility_id', $reservation->facility->id)->first();
        if($brand_setting){
            $reservation->facility_logo_id = ($brand_setting->id) ? $brand_setting->id : '';
        }else{
            $brand_setting = BrandSetting::where('user_id', $reservation->user->created_by)->first();
            $reservation->logo_id = ($brand_setting->id) ? $brand_setting->id : '';
        }
                
        if($client_secret != ''){
            $view='reservation.atlanta-cancellation-email';
          }else{
            $view='reservation.cancellation-email';
          }
        

        $view_text=$view.'-plain-text';
        if(!View::exists($view_text))
        {
            $view_text=$view;
        } 
         try{ 
             Mail::send(
                ['text'=>$view_text,'html'=>$view],
                [
                    'reservation' => $reservation,
                    'brand_setting' => $brand_setting,
					'user_pass' => $userpass
                ],
                function ($message) use ($email) {
                    $message->to($email)->subject("Reservation Canceled");
                    $message->from(config('inventory.email'));
                }
             );
        }catch (\Exception $e)
        {
            $errorMessage=array();
            $errorMessage['message']=$e->getMessage();
            $this->log = (new LoggerFactory)->setPath('logs/email-sending-issues-logs')->createLogger('email-sending-issue');
            $this->log->info('Issue in email sending:',$errorMessage);
        }
    }
}
