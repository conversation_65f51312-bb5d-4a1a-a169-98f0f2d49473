<?php

namespace App\Console\Commands\Reservation;

use Illuminate\Console\Command;
use App\Models\Reservation;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\PlatformNotification;
use Carbon\Carbon;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\SendSmsReminder;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\BrandSetting;
use App\Services\LoggerFactory;
use Exception;
use App\Models\ParkEngage\NotificationLog;
use App\Models\ParkEngage\TicketExtend;
use App\Models\Ticket;
use App\Models\User;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SmsReminder extends Command
{
   use DispatchesJobs;
   protected $log;
   protected $globalData = null;

   /**
    * The name and signature of the console command.
    *
    * @var string
    */
   protected $signature = 'reservation:sms-reminder';

   /**
    * The console command description.
    *
    * @var string
    */
   protected $description = 'Mapco Reservation sms notification reminder';

   /**
    * Create a new command instance.
    *
    * @return void
    */
   public function __construct(LoggerFactory $logFactory)
   {
      parent::__construct();
      $this->log = $logFactory->setPath('logs/mapcosms')->createLogger('sms');
   }

   /**
    * Execute the console command.
    *
    * @return mixed
    */
   public function handle()
   {
      try {
         $partnerId = '1553';
         $this->log->info("partner id:" . $partnerId);

         $rmId = '27768';
         $this->log->info("rm id:" . $rmId);

         $clientSecret = OauthClient::where('partner_id', $partnerId)->first();

         $facilities = DB::table('user_facilities')->where('user_id', $rmId)->whereNull('deleted_at')->pluck('facility_id');
        
         $reservations = Reservation::where('partner_id', '=', $partnerId)
            ->where('is_ticket', '!=', '2')
            ->whereIn('facility_id', $facilities)
            ->whereNull('deleted_at')
            ->whereNull('cancelled_at')
            ->orderBy('id', 'desc')
            ->get();
            
         $this->log->info('reservations');
         
         if ($reservations->isEmpty()) {
            $this->log->info('No Reservation Found');
            throw new NotFoundException('No reservation found.');
         }
         
         $smsContents = PlatformNotification::select('pn.partner_id', 'pn.status', 'pn.scheduled_flag', 'pn.time_in_minutes', 'nt.type as notify_type', 'platform_notifications.*')
            ->leftjoin('partner_notifications as pn', 'pn.platform_notification_id', '=', 'platform_notifications.id')
            ->leftjoin('notification_type as nt', 'pn.notification_type_id', '=', 'nt.id')
            ->where('pn.partner_id', '=', $partnerId)
            ->get();
         
         
         if (empty($smsContents)) {
            $this->log->info('Template data empty.');
            throw new NotFoundException('No partner notification found.');
         }
         
         foreach ($reservations as $reservation) {
            $user = User::where(['id' => $reservation->user_id])->first();
            $ticket = Ticket::where(['reservation_id' => $reservation->id])->first();
            
            if (!$user) {
               // $this->log->info("Continue for the user ID  : {$reservation->user_id}  ");
               continue;
            }
            $this->log->info("=======>>>>>>>> reservation id: {$reservation->id} AND  user ID : {$reservation->user_id} AND ticketecode : {$reservation->ticketech_code}");

            // Get User 
            $this->log->info("is user : Email : {$user->email} Phone {$user->phone} ");

            foreach ($smsContents as $smsContent) {
               //current date and time
               $currentDateTime = Carbon::now();
               $this->log->info("Current Time : {$currentDateTime}");
               
               //reservation start time
               $startTimestamp = Carbon::parse($reservation['start_timestamp']);
               $reservationStartDate = $startTimestamp->toDateString(); // Get only the date part
               
               $this->log->info("reservation start date : " . $reservation['start_timestamp']);
               
               // Calculate the start reservation time difference in hours and convert in minutes
               $convertHoursInMinutes = $this->hourDifferenceInMinutes($startTimestamp, $currentDateTime);
               $this->log->info(" convertHoursInMinutes : $convertHoursInMinutes ");

               // Calculate the checkin in difference in minutes
               $checkinInMinutes = $startTimestamp->diffInRealMinutes($currentDateTime);
               $this->log->info(" checkin in minutes : $checkinInMinutes");
               
               $this->log->info("start time check:$startTimestamp");
               //reservation checkout time
               $checkoutDateTime = '';
               $checkoutInMinutes = 0;
               if (!empty($ticket) && isset($ticket->estimated_checkout) && !empty($ticket->estimated_checkout)) {
                  $estimatedCheckout = $ticket->estimated_checkout;
                  $this->log->info("reservation estimated checkout time : $estimatedCheckout");

                  // Convert length to minutes
                  $length_minutes = (float)$ticket->length * 60;
                  // Subtract length from estimated checkout time
                  $checkoutDateTime = Carbon::parse($estimatedCheckout)->subMinutes($length_minutes);
                  $this->log->info("subtracted checkout date time : $checkoutDateTime");

                  if ($ticket->is_extended == 1) {
                     $ticketExtends = TicketExtend::select('length')->where('ticket_number', $ticket->ticket_number)->get();

                     foreach ($ticketExtends as $ticketExtend) {
                        $extendInMinutes = (float)$ticketExtend->length * 60;
                        $checkoutDateTime->addMinutes($extendInMinutes);
                        // $checkoutDateTime = $checkoutDateTime + $extendInMinutes;
                     }
                  }

                  // Checkout time difference in minutes
                  $checkoutInMinutes = $checkoutDateTime->diffInRealMinutes($currentDateTime);
                  $this->log->info("checkout in minutes : $checkoutInMinutes");
               }

               //current date only
               $currentDateOnly = $currentDateTime->toDateString();

               $data = [
                  'platform_notification_id' => $smsContent->id,
                  'reservation_id' => $reservation->id,
                  'status' => 1,
                  'start_timestamp' => $reservation->start_timestamp,
                  'error' => null,
                  'slug_name' => $smsContent->slug_name,
                  'user_prefrences' => $user['user_prefrences'],
               ];
               $this->globalData = $data;

               $logDatas = NotificationLog::where('reservation_id', '=', $reservation->id)
                  ->where('status', '=', '1')
                  ->whereNull('error')
                  ->pluck('platform_notification_id', 'user_prefrences')
                  ->toArray();

               $logReservationStartTime = NotificationLog::select('start_timestamp')
                  ->where('reservation_id', '=', $reservation->id)
                  ->where('status', '=', '1')
                  ->whereNull('error')
                  ->get();

               $logSlug = NotificationLog::where('reservation_id', '=', $reservation->id)
                  ->where('status', '=', '1')
                  ->whereNull('error')
                  ->pluck('slug_name')
                  ->toArray();

               $reservationData = [
                  "phone" => $reservation->user->phone,
                  "reservationId" => $reservation->id,
                  "email" => $reservation->user['email']
               ];

               $options = ['reservationId' => $reservation->id];
               if ($clientSecret) {
                  $options['--client_secret'] = $clientSecret['secret'];
               }
               $body = $smsContent->body;
               $options['--body'] = "";
               $options['--flag'] = 0;
               $options['--subject'] = "";

               //24 hours code
                 if ($logReservationStartTime->isEmpty()) {
                  $this->log->info("Under empty: " . $smsContent['time_in_minutes'] . "+++".$convertHoursInMinutes.")))))".strtotime($reservationStartDate) . '>='. strtotime($currentDateOnly) ."Enddddddddd");

                  if (($smsContent['time_in_minutes'] == $convertHoursInMinutes) && (strtotime($reservationStartDate) >= strtotime($currentDateOnly))) {
                     $this->log->info("hours reservation id:" . $reservation->id);
                     
                     $this->log->info("24 hours slug check for user prefrences 1: " . json_encode($logSlug). "++++".$user['user_prefrences']);

                     if ($user['user_prefrences'] == '1' && (!in_array("24-hours-reminder", $logSlug))) {
                        $this->dispatch((new SendSmsReminder($smsContent, $reservationData, new LoggerFactory)));

                        $this->globalData = $data;
                        $this->addDataInLog($data);
                     }

                     $this->log->info("24 hours slug check for user prefrences 2: " . json_encode($logSlug). "++++".$user['user_prefrences']);

                     if ($user['user_prefrences'] == "2" && (!in_array("24-hours-reminder", $logSlug))) {
                        $options['--flag'] = 1;
                        $options['--body'] = str_replace('by', '', $body);
                        $options['--subject'] = "Your have an upcoming Reservation " . $reservation->ticketech_code;

                        Artisan::queue('reservation:email', $options);
                        //$this->sendEmail($smsContent, $reservationData, $reservation, $user, $data);
                        $this->log->info("check-out-reminder Email reservation id:" . $reservation->id);
                        $this->globalData = $data;
                        $this->addDataInLog($data);
                     }

                     $this->log->info("24 hours slug check for user prefrences o and 3: " . json_encode($logSlug). "++++".$user['user_prefrences']);

                     if (($user['user_prefrences'] == '3' || $user['user_prefrences'] == '0') && (!in_array("24-hours-reminder", $logSlug))) {
                        //Sms
                        $this->log->info("24 hours sms/email reservation id:" . $reservation->id);

                        $this->dispatch((new SendSmsReminder($smsContent, $reservationData, new LoggerFactory)));
                        $this->globalData = $data;
                        $this->addDataInLog($data);

                        //Email
                        $this->log->info("24 hours email reservation id:" . $reservation->id);
                        $options['--flag'] = 1;
                        $options['--body'] = str_replace('by', '', $body);
                        $options['--subject'] = "Your have an upcoming Reservation " . $reservation->ticketech_code;

                        Artisan::queue('reservation:email', $options);
                        //$this->sendEmail($smsContent, $reservationData, $reservation, $user,$data);
                        $this->log->info("check-out-reminder user-preference Email reservation id:" . $reservation->id);
                        $this->globalData = $data;
                        $this->addDataInLog($data);
                     }
                  }
               } else {
                  $this->log->info("Updated Case if user update our reservation agian ");
                  foreach ($logReservationStartTime as $value) {

                     $this->log->info("Under foreach loop for update Case.");

                     $logStartTimestamp = Carbon::parse($value['start_timestamp']);
                     $this->log->info("Log reservation start date {$logStartTimestamp}");

                     // Calculate the start reservation time difference in hours and convert in minutes
                     $logResStartTimeHoursInMinutes = $this->hourDifferenceInMinutes($logStartTimestamp, $currentDateTime);
                     $this->log->info("logResStartTimeHoursInMinutes data {$logResStartTimeHoursInMinutes}");

                     $this->log->info($logResStartTimeHoursInMinutes. "!======". $convertHoursInMinutes);
                     if ($logResStartTimeHoursInMinutes != $convertHoursInMinutes) {

                        NotificationLog::where('reservation_id', $reservation->id)
                           ->where('slug_name', '24-hours-reminder')
                           ->where('status', '1')
                           ->delete();
                     }

                     $this->log->info("After Delete log for edit resdervation. ");

                     if (!in_array($smsContent['id'], $logDatas) && ($smsContent['time_in_minutes'] == $checkinInMinutes) && ($reservationStartDate >= $currentDateOnly) && ($logResStartTimeHoursInMinutes != $convertHoursInMinutes)) {

                     $this->log->info("Under 24 hours update if condition.");

                        if ($user['user_prefrences'] == '1' && (!in_array("24-hours-reminder", $logSlug))) {
                           $this->dispatch((new SendSmsReminder($smsContent, $reservationData, new LoggerFactory)));

                           $this->log->info("24 hours edit sms reservation id:" . $reservation->id);
                           $this->globalData = $data;
                           $this->addDataInLog($data);
                        }

                        if ($user['user_prefrences'] == "2"  && (!in_array("24-hours-reminder", $logSlug))) {
                           $this->log->info("24 hours edit reservation id:" . $reservation->id);
                           $options['--flag'] = 1;
                           $options['--body'] = str_replace('by', '', $body);
                           $options['--subject'] = "Your have an upcoming Reservation " . $reservation->ticketech_code;
                           Artisan::queue('reservation:email', $options);
                           //$this->sendEmail($smsContent, $reservationData, $reservation, $user, $data);
                           $this->log->info("24 hours edit email reservation id:" . $reservation->id);
                           $this->globalData = $data;
                           $this->addDataInLog($data);
                        }

                        if (($user['user_prefrences'] == '3' || $user['user_prefrences'] == '0')  && (!in_array("24-hours-reminder", $logSlug))) {
                           //Sms
                           $this->log->info("24 hours reminder sms/email reservation id:" . $reservation->id);

                           $this->dispatch((new SendSmsReminder($smsContent, $reservationData, new LoggerFactory)));
                           $this->log->info("24 hours sms reminder reservation id:" . $reservation->id);
                           $this->globalData = $data;
                           $this->addDataInLog($data);

                           //Email
                           $this->log->info("24 hours email reminder reservation id:" . $reservation->id);

                           $options['--flag'] = 1;
                           $options['--body'] = str_replace('by', '', $body);
                           $options['--subject'] = "Your have an upcoming Reservation " . $reservation->ticketech_code;
                           Artisan::queue('reservation:email', $options);

                           $this->log->info("24 hours user-preference Email reservation id:" . $reservation->id);
                           $this->globalData = $data;
                           $this->addDataInLog($data);
                        }
                     }
                  }
               }

               // checkin code
               if (($reservation['is_ticket'] === "0")  && ($smsContent['slug_name'] === "check-in-reminder") && ($smsContent['time_in_minutes'] == $checkinInMinutes) && strtotime($currentDateTime) > strtotime($startTimestamp)) {
                  try {
                     $this->log->info("check-in condition reservation id:" . $reservation->id);

                     if ((!in_array("check-in-reminder", $logSlug)) && $user['user_prefrences'] == '1') {
                        $this->log->info("check-in user_preference 1 reservation id:" . $reservation->id);

                        $this->dispatch((new SendSmsReminder($smsContent, $reservationData, new LoggerFactory)));
                        $this->log->info("check-in-reminder reservation id:" . $reservation->id);
                        $this->globalData = $data;
                        $this->addDataInLog($data);
                     }
                     if ((!in_array("check-in-reminder", $logSlug)) && $user['user_prefrences'] == "2") {
                        $this->log->info("check-in user_preference 2 reservation id:" . $reservation->id);

                        $options['--body'] = str_replace('by visiting the following link -', '', $body) . "button to modify reservation.";
                        $this->log->info("check-in-reminder24 body:" . $options['--body']);

                        $options['--subject'] = "Your Reservation is already started " . $reservation->ticketech_code;
                        $this->log->info("check-in-reminder24 subject:" . $options['--subject']);

                        Artisan::queue('reservation:email', $options);
                        $this->log->info("check-in-reminder hii Email reservation id:" . $reservation->id);
                        $this->globalData = $data;
                        $this->addDataInLog($data);
                     }

                     if ((!in_array("check-in-reminder", $logSlug)) && ($user['user_prefrences'] == '3' || $user['user_prefrences'] == '0')) {
                        $this->log->info("check-in user_preference 3 reservation id:" . $reservation->id);

                        //SMS
                        $this->dispatch((new SendSmsReminder($smsContent, $reservationData, new LoggerFactory)));
                        $this->log->info("check-in-reminder sms reservation id:" . $reservation->id);
                        $this->globalData = $data;
                        $this->addDataInLog($data);

                        //For Email
                        //$this->sendEmail($smsContent, $reservationData, $reservation, $user, $data);
                        $options['--body'] = str_replace('by visiting the following link -', '', $body) . "button to modify reservation.";
                        $this->log->info("check-in-reminder24 log for Email body:" . $options['--body']);

                        $options['--subject'] = "Your Reservation is already started " . $reservation->ticketech_code;
                        $this->log->info("check-in-reminder24 log for Email subject:" . $options['--subject']);

                        Artisan::queue('reservation:email', $options);
                        $this->log->info("check-in-reminder log for Email reservation id:" . $reservation->id);
                        $this->globalData = $data;
                        $this->addDataInLog($data);
                     }
                  } catch (\Throwable $th) {
                     $this->log->error("ERROR check-in reservation id:" . $th->getMessage());
                  }
               } else {
                  $this->log->info("not checked -in :" . $reservation->id);
                  $this->log->info("not checked -in :" . $smsContent['id']);
                  $this->log->info("not checked -in :" . json_encode($logDatas));
                  // $this->log->info("not checked -in :" . $smsContent['time_in_minutes'] . "  ---  " . $checkinInMinutes . ' Reminder -'  . $smsContent['slug_name']);
               }

               $this->log->info("time in minutes and checkout time:" .  $smsContent['time_in_minutes'] . '==' . $checkoutInMinutes);

               // checkout code
               if (($reservation['is_ticket'] === "1") && ($smsContent['time_in_minutes'] == $checkoutInMinutes) && ($smsContent['slug_name'] === "check-out-reminder") && strtotime($currentDateTime) > strtotime($checkoutDateTime)) {
                  $this->log->info("check-out condition reservation id:" . $reservation->id);
                  $this->log->info("check-out condition user_prefrences:" . $user['user_prefrences']);

                  if ((!in_array("check-out-reminder", $logSlug)) && $user['user_prefrences'] == '1') {
                     $this->log->info("check-out user_preference 1 reservation id:" . $reservation->id);

                     $this->log->info("check-out reservation id:" . $reservation->id);
                     $this->dispatch((new SendSmsReminder($smsContent, $reservationData, new LoggerFactory)));
                     $this->log->info("check-out-reminder reservation id:" . $reservation->id);

                     $this->globalData = $data;
                     $this->addDataInLog($data);
                  }

                  if ((!in_array("check-out-reminder", $logSlug)) && $user['user_prefrences'] == "2") {
                     $this->log->info("check-out user_preference 2 reservation id:" . $reservation->id);

                     $this->log->info("check-out Email reservation id:" . $reservation->id);
                     $options['--body'] = str_replace('by visiting the following link -', '', $body) . "button to modify reservation.";
                     $this->log->info("check-out-reminder Email body:" . $options['--body']);

                     $options['--subject'] = "Your scheduled end time of your reservation " . $reservation->ticketech_code;
                     $this->log->info("check-out-reminder Email subject:" . $options['--subject']);

                     Artisan::queue('reservation:email', $options);
                     $this->log->info("check-out-reminder Email reservation id:" . $reservation->id);
                     $this->globalData = $data;
                     $this->addDataInLog($data);
                  }

                  if ((!in_array("check-out-reminder", $logSlug)) && ($user['user_prefrences'] == '3' || $user['user_prefrences'] == '0')) {
                     $this->log->info("check-out user_preference 3 reservation id:" . $reservation->id);

                     //Sms
                     $this->log->info("check-out for Email reservation id:" . $reservation->id);

                     $this->dispatch((new SendSmsReminder($smsContent, $reservationData, new LoggerFactory)));
                     $this->log->info("check-out-reminder reservation id:" . $reservation->id);
                     $this->globalData = $data;
                     $this->addDataInLog($data);

                     //Email
                     $this->log->info("check-out user-preference Email reservation id:" . $reservation->id);

                     $options['--body'] = str_replace('by visiting the following link -', '', $body) . "button to modify reservation.";
                     $this->log->info("check-out-reminder user-preference Email body:" . $options['--body']);

                     $options['--subject'] = "Your scheduled end time of your reservation " . $reservation->ticketech_code;
                     $this->log->info("check-out-reminder user-preference Email subject:" . $options['--subject']);

                     Artisan::queue('reservation:email', $options);
                     $this->log->info("check-out-reminder user-preference Email reservation id:" . $reservation->id);

                     $this->globalData = $data;
                     $this->addDataInLog($data);
                  }
               }
            }
            $this->log->info("=======>>>>>>>>  CLOSE LOOP");
         }
      } catch (Exception $e) {
         $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
         $this->log->error($msg);
         $error = [
            'platform_notification_id' => $this->globalData['platform_notification_id'],
            'reservation_id' => $this->globalData['reservation_id'],
            'status' => 0,
            'start_timestamp' => $reservation->start_timestamp,
            'error' => $msg,
            'slug_name' => $smsContent->slug_name,
            'user_prefrences' => $user->user_prefrences
         ];
         $this->addDataInLog($error);
         $this->log->info("Cron ended");
      }
   }

   /***
    * Create data in notification_logs table.
    */
   public function addDataInLog($data)
   {
      NotificationLog::create($data);
      return true;
   }

   /***
    * Create data in notification_logs table.
    */
   public function hourDifferenceInMinutes($startTimestamp, $currentDateTime)
   {
      $hourDifference = $startTimestamp->diffInRealHours($currentDateTime);
      $convertHoursInMinutes = ($hourDifference * 60);
      // $this->log->info($convertHoursInMinutes);

      return $convertHoursInMinutes;
   }

   /***
    * Email body content
    */
   public function sendEmail($smsContent, $reservationData, $reservation, $user, $data)
   {
      try {
         $this->log->info("Hiiii email");

         $brand_setting = BrandSetting::where('user_id', $user->created_by)->first();
         $facility = Facility::where('id', $reservation['facility_id'])->first();

         $resUrlCode = base64_encode($reservationData['reservationId']);
         $content = [
            'body' => $smsContent->body . ' ' . $smsContent->url . $resUrlCode,
            'reservation' => $reservation,
            'user' => $user,
            'brand_setting' => $brand_setting,
            'facility' => $facility,
         ];
         //dd($dd['facility']->full_name);
         // Send SMS
         $this->log->info("Send Email to user:" . $reservationData['email']);
         $this->log->info("Sender Email:" . config('inventory.email'));
         Mail::send(
            'reservation.email-notify',
            $content,
            function ($message) use ($reservationData) {
               $message->to($reservationData['email']);
               $message->from(config('inventory.email'));
               $message->subject('Reservation Reminder');
            }
         );
         $this->log->info("Email Send Successfully");
         $this->globalData = $data;
         $this->addDataInLog($data);
      } catch (\Throwable $e) {
         $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
         $this->log->error($msg);
         $error = [
            'platform_notification_id' => $this->globalData['platform_notification_id'],
            'reservation_id' => $this->globalData['reservation_id'],
            'status' => 0,
            'start_timestamp' => $reservation->start_timestamp,
            'error' => $msg,
            'slug_name' => $smsContent->slug_name,
            'user_prefrences' => $user['user_prefrences'],
         ];
         $this->addDataInLog($error);
      }
   }
}
