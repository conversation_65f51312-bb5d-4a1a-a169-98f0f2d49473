<?php

namespace App\Console\Commands\ParkChrip;

use Illuminate\Console\Command;
use App\Jobs\ParkchripThridPartyData;
use App\Services\LoggerFactory;
use Carbon\Carbon; // Import Carbon

class GetData extends Command
{
    // Artisan command name
    protected $signature = 'parkchrip-getdata';

    protected $description = 'Fetch Parkchrip data through API';

    protected $log;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/thridparty/parkchrip')->createLogger('parkchrip');

        // Define the filename (modify this as needed)
        $filename = 'parkchrip_data_' . Carbon::now()->format('Ymd_His') . '.csv';

        dispatch(new ParkchripThridPartyData($filename));

        $this->log->info('Parkchrip API job dispatched.', ['filename' => $filename]);
    }
}
