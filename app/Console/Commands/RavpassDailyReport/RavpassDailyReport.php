<?php
/*
 * -----------------------------------------------------------------------------
 * Copyright (c) 2024 ParkEngage Inc. All rights reserved.
 * -----------------------------------------------------------------------------
 *
 * This software is the confidential and proprietary information of
 * ParkEngage Inc. ("Confidential Information"). You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms
 * of the license agreement you entered into with ParkEngage Inc.
 *
 * This source code is a valuable trade secret of ParkEngage Inc.
 *
 * DISCLAIMER:
 * THIS SOFTWARE IS PROVIDED BY PARKENGAGE INC. "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL PARKENGAGE INC. OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are
 * those of the authors and should not be interpreted as representing official
 * policies, either expressed or implied, of ParkEngage Inc.
 * -----------------------------------------------------------------------------
 */

namespace App\Console\Commands\RavpassDailyReport;

use Mail;
use Exception;
use Illuminate\Console\Command;
use DB;
use Carbon\Carbon;
use App\Http\Helpers\ReportBuilder;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\ParkEngage\BrandSetting;
use App\Http\Controllers\ParkEngage\ReportParkEngage;
use App\Services\LoggerFactory;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Contracts\Auth\Authenticatabl;
use Illuminate\Support\Facades\Log;

class RavpassDailyReport extends Command
{

    // protected $signature = 'KittredgeStreet:daily-report';
    protected $signature = "ravpass:daily-report";


    protected $description = 'get daily report data';

    protected $log;

    const PARTNER_ID = 215900;
    protected $request;
    protected $loggerFactory;
    protected $authNet;
    protected $cim;
    protected $reportBuilder;
    private $startOfMonth;
    private $endOfMonth;
    protected $partnerId;
    protected $facilityId;


    function __construct(Request $request, LoggerFactory $LoggerFactory, ReportBuilder $reportBuilder)
    {
        Log::info('ravpass Cron Start here1111  --------  ' . $request->facility_id);
        parent::__construct();
        $this->startOfMonth = Carbon::yesterday()->format('Y-m-d') . ' 00:00:00';
        $this->endOfMonth = Carbon::yesterday()->format('Y-m-d') . ' 23:59:59';
        // $this->startOfMonth =  '2025-06-07 00:00:00';
        // $this->endOfMonth = '2025-06-10 23:59:59';
        $request->replace([]);
        $this->loggerFactory = $LoggerFactory;
        $this->request = $request;
        $this->reportBuilder = $reportBuilder;


        Log::info('ravpass Cron Start here1111  --------  ' . $request->facility_id);
    }

    public function handle(Request $request)
    {
        set_time_limit(0);
        $facilityIds = DB::table('facility_email_configurations')
            ->whereNotNull('functioncall')  // Exact match, not array
            ->whereNotNull('email')      // Replace 'email' with your actual column
            ->where('is_active', 1)
            ->get();
        //$emails  = array_column($facilityIds, 'email'); // extract emails from records
        foreach ($facilityIds as $facilityId) {
            //dd($facilityId->user_id);
            $emails = $facilityId->email;
            if ($facilityId->date_range_daily == '0') {
                $this->startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d') . ' 00:00:00';
                $this->endOfMonth   = Carbon::now()->endOfMonth()->format('Y-m-d') . ' 23:59:59';
            }
            set_time_limit(0);

            $user = User::find($facilityId->user_id);
            Auth::login($user);
            $request->merge([
                'partner_id' => $facilityId->partner_id,
                'fromDate' => $this->startOfMonth,
                'toDate' => $this->endOfMonth,
                'facility_id' => $facilityId->facility_id,
                'email_send' => 1,
                'filename' => 1,
                'internal_external_use' => 0,
            ]);

            Log::info('Start processing for facility ' . $facilityId->facility_id);
            $getfunction = $facilityId->functioncall;

            $controller = new ReportParkEngage($request, $this->loggerFactory, $this->reportBuilder);
            $getDetails = $controller->$getfunction($this->request);

            if (isset($getDetails[0]) && !empty($getDetails[0])) {
                $excelName = $getDetails[1];
                $excelpath = $getDetails[0];
                $revenueData = $getDetails[2];

                $data = [
                    'totalTickets'   => $getDetails[3],
                    'netValue'       => round($getDetails[4], 2),
                    'overAllTotal'   => round($getDetails[5], 2),
                    'report_name'    => 'daily_revenue_report',
                    'location_name'  => $getDetails[6],
                    'subject'  => $facilityId->email_subject,
                    'mail_body'      => "PFB summary of today’s Revenue Report. A detailed report is also attached.",
                ];
                $facilityId = $facilityId->facility_id;
                Mail::send('daily-report-email.daily-email-format', $data, function ($message) use ($excelName, $excelpath, $revenueData, $facilityId, $emails, $data) {
                    //dd($data['report_name']);
                    $message->to($emails);
                    $subject = $data['subject'] . $data['location_name'] . ' - ' . date("d F, Y");
                    $message->subject($subject);
                    $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));

                    if (file_exists($excelpath)) {
                        $message->attach($excelpath);
                        Log::info("Mail sent successfully with attachment for facility $facilityId");
                    } else {
                        Log::warning("Attachment not found for facility $facilityId at path $excelpath");
                    }
                });
            }

            Auth::logout(); // safely log out after each facility
        }
    }
}
