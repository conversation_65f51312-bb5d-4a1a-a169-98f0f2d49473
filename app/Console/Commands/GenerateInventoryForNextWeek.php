<?php
namespace App\Console\Commands;
use App\Models\Facility;
use App\Models\FacilityAvailability;
use App\Models\FacilityInventory;
use App\Models\FacilityPartnerAvailabilityCron;
use App\Models\HoursOfOperation;
use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Classes\Inventory;

class GenerateInventoryForNextWeek extends Command
{
    const DAY_START_HOUR              = 0;
    const DAY_END_HOUR                = 23;
    const NUMBER_OF_SPOTS             = 50;
    const PARTNER_SPOTS_AVAILABLE     = 1;
    const PARTNER_SPOTS_NOT_AVAILABLE = 0;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:inventory-for-next-months';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command should be scheduled to run on weekend to copy the current inventory into next for each facility';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {
       //to create or update inventory and avialability from current week
        Carbon::setWeekStartsAt(Carbon::SUNDAY);
        $dates = new \DatePeriod(
            new \DateTime(Carbon::now()->startOfWeek()), new \DateInterval('P1D'),
            new \DateTime(Carbon::now()->startOfWeek()->addWeeks(14)));

        Facility::where('active', 1)->with(['availabilities'=>function($query){
            return $query->where('date', '>=', Carbon::now()->startOfWeek()->subDays(7)->format('Y-m-d'));

        }, 'inventories'=>function($query){
            return $query->where('date', '>=', Carbon::now()->startOfWeek()->subDays(7)->format('Y-m-d'));

        }])->chunk(
            5, function ($facilities) use ($dates) {
            foreach ($facilities as $facility) {
                
                $facilityInventories        = [];
                $facilityAvailabilities     = [];
                foreach ($facility->inventories as $facility_inventory) {
                    $facilityInventories[date('Y-m-d', strtotime($facility_inventory->date))] =
                    $facility_inventory->availability;
                }
                foreach ($facility->availabilities as $facility_availability) {
                    $facilityAvailabilities[date('Y-m-d', strtotime($facility_availability->date))] =
                    $facility_availability->availability;
                }

                Carbon::setWeekStartsAt(Carbon::SUNDAY);
                $current_week = new \DatePeriod(
                new \DateTime(Carbon::now()->startOfWeek()), new \DateInterval('P1D'),
                new \DateTime(Carbon::now()->startOfWeek()->addWeeks(1)));
                
                foreach($current_week as $k=>$current_week_date){
                    
                    $lastWeekToday = Carbon::createFromDate($current_week_date->format('Y'), $current_week_date->format('m'), $current_week_date->format('d'))->subDays(7)->format('Y-m-d');
                                       
                    
                    
                    if(isset($facilityInventories[$current_week_date->format('Y-m-d')])){
                        $facilityInventories[$lastWeekToday] = $facilityInventories[$current_week_date->format('Y-m-d')];
                        $facilityAvailabilities[$lastWeekToday] = $facilityAvailabilities[$current_week_date->format('Y-m-d')];
                    }
                }

              
                

                //fetch operational hours of this facility
                $hours = HoursOfOperation::where('facility_id', $facility->id)->orderBy('day_of_week', 'ASC')->get();
                
                $inventory = new Inventory();
                $getOperationalHours = $inventory->getOperationalHours($facility->id, $hours);
                $operationalHours    = $getOperationalHours['operationalHours'];
                $remainder           = $getOperationalHours['remainder'];
                $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
                
                //iterate over dates for the next 90 days starting from the most recent last sunday
                foreach ($dates as $key => $date) {
                    $inventories    = [];
                    $availabilities = [];
                    $partneravailabilities = [];
                    $today = $date->format('l');

                    //date of last week
                    $lastWeekToday = Carbon::createFromDate($date->format('Y'), $date->format('m'), $date->format('d'))->subDays(7)->format('Y-m-d');

                    $day_of_week = date('w', strtotime($date->format('Y-m-d')));
                    $operationalHoursOpenTime=$operationalHours[$today]['open_time'];
                    
                    if (count($facilityInventories) && isset($facilityInventories[$lastWeekToday])) {
                        $inventoriesTemp = json_decode($facilityInventories[$lastWeekToday], true);
                     
                        //multiple HOO
                        foreach($operationalHoursArray[$today] as $operationalHour){
                            
                                $inventories[$operationalHour] =
                            (isset($inventoriesTemp[$operationalHour]) && $inventoriesTemp[$operationalHour]!==0)?$inventoriesTemp[$operationalHour]:self::NUMBER_OF_SPOTS;
                           
                        }
                    } else {
                        //multiple HOO
                        foreach($operationalHoursArray[$today] as $operationalHour){
                            
                                $inventories[$operationalHour] = self::NUMBER_OF_SPOTS;
                            
                        }
                    }

                    /**
                     * SIMILAR ARRAY AS ABOVE FOR AVAILABILITIES
                     */
                    
                    if (count($facilityInventories) && isset($facilityInventories[$lastWeekToday])) 
                    {                        
                        $curr_inventory = [];
                        $curr_availability= [];
                        if(isset($facilityInventories[$date->format('Y-m-d')]) && isset($facilityAvailabilities[$date->format('Y-m-d')])){
                            $curr_inventory = json_decode($facilityInventories[$date->format('Y-m-d')], true);
                            $curr_availability = json_decode($facilityAvailabilities[$date->format('Y-m-d')], true);
                        }

                        $availabilitiesTemp    = json_decode($facilityInventories[$lastWeekToday], true);
                        
                        foreach($operationalHoursArray[$today] as $operationalHour){
                            $spots = 0;
                            if(isset($curr_inventory[$operationalHour]) && isset($curr_availability[$operationalHour])){
                                    $spots = $curr_inventory[$operationalHour] - $curr_availability[$operationalHour];
                            }

                            
                                $availabilities[$operationalHour] =
                                ((isset($availabilitiesTemp[$operationalHour]) && !($availabilitiesTemp[$operationalHour]==0 && $spots==0))?$availabilitiesTemp[$operationalHour]:self::NUMBER_OF_SPOTS) - $spots;
                                $partneravailabilities[$operationalHour] =  ((isset($availabilitiesTemp[$operationalHour]) && !($availabilitiesTemp[$operationalHour]==0 && $spots==0))?$availabilitiesTemp[$operationalHour]:self::NUMBER_OF_SPOTS) - $spots;
                           
                        }
                    } else {
                        //else create a static availability
                        foreach($operationalHoursArray[$today] as $operationalHour){
                            
                            
                                $availabilities[$operationalHour] = self::NUMBER_OF_SPOTS;
                                $partneravailabilities[$operationalHour] = self::NUMBER_OF_SPOTS;
                            
                        }
                    }

                      
                        
                        $newInventory = FacilityInventory::firstOrNew(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
                        if (!$newInventory->exists) {
                            $newInventory->facility_id  = $facility->id;
                            $newInventory->availability = json_encode($inventories, JSON_FORCE_OBJECT);
                            $newInventory->date         = $date->format('Y-m-d');
                            $newInventory->save();
                        }

                        $newAvailability = FacilityAvailability::firstOrNew(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
                        if (!$newAvailability->exists) {
                            $newAvailability->facility_id  = $facility->id;
                            $newAvailability->availability = json_encode($availabilities, JSON_FORCE_OBJECT);
                            $newAvailability->date         = $date->format('Y-m-d');
                            $newAvailability->save();
                        }
                    
                        //update partners data too
                        $partnerData = FacilityPartnerAvailabilityCron::firstOrNew(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
                        if (!$partnerData->exists) {
                            $partnerData->availability = json_encode($partneravailabilities, JSON_FORCE_OBJECT);
                            $partnerData->save();
                        }
                   
                }
            }
        });
    }
}
