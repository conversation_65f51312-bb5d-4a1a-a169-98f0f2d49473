<?php
namespace App\Console\Commands;
use App\Models\Facility;
use App\Models\FacilityAvailability;
use App\Models\FacilityInventory;
use App\Models\FacilityPartnerAvailabilityCron;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateExistingInventory extends Command
{
    const DAY_START_HOUR              = 0;
    const DAY_END_HOUR                = 23;
    const NUMBER_OF_SPOTS             = 50;
    const PARTNER_SPOTS_AVAILABLE     = 1;
    const PARTNER_SPOTS_NOT_AVAILABLE = 0;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:existing-inventory';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command to solved string availability issue';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {
        Facility::where('active', 1)->with('availabilities', 'inventories')->chunk(
            5, function ($facilities)  {

            foreach ($facilities as $facility) {

                //fetch last weeks inventory of this facility
              
               
                foreach ($facility->availabilities as $facility_availability) {
                    
                    $facilityAvailabityData = json_decode($facility_availability->availability);
                     $facilityAvailabilities =[];
                      if(isset($facilityAvailabityData))
                       {
                          foreach($facilityAvailabityData  as $key => $value)
                          {
                              $facilityAvailabilities[$key] = (int)$value;
                          }
                       }  
                      $facility_availability->availability = json_encode($facilityAvailabilities, JSON_FORCE_OBJECT);
                      $facility_availability->save();
                   }
                   
                   //inventories
                 foreach ($facility->inventories as $facility_inventories) {
                    
                    $facilityInventoryData = json_decode($facility_inventories->availability);
                     $facilityInventories     = [];
                      if(isset($facilityInventoryData))
                       {
                          foreach($facilityInventoryData  as $key1 => $value1)
                          {
                              $facilityInventories[$key1] = (int)$value1;
                          }
                       }  
                      $facility_inventories->availability = json_encode($facilityInventories, JSON_FORCE_OBJECT);
                      $facility_inventories->save();
                   }

            }

        });
    }


}
