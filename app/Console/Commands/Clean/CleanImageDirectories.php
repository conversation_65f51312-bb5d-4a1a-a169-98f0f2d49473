<?php

namespace App\Console\Commands\Clean;

use Illuminate\Console\Command;

use App\Models\Photo;
use File;
use Storage;
use Exception;

class CleanImageDirectories extends Command
{

    protected $signature = 'clean:imageDirectories';
    protected $description = 'put images in the respective directory';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $images = Photo::all();

        foreach ($images as $image) {
            // getting the model of the imageable_type
            if ($image->imageable_type == 'App\Models\Affiliate') {
                //If images are old models delete.
                $image->delete();
                continue;
            }

            $image_name = $image->getOriginal('image_name');

            if (!File::isDirectory(storage_path('app/' .constant("$image->imageable_type::IMAGE_FOLDER")))) {
                Storage::makeDirectory(constant("$image->imageable_type::IMAGE_FOLDER"));
            }

            // if the file exist in the models folder leave it there.
            if (File::exists(storage_path('app/' .constant("$image->imageable_type::IMAGE_FOLDER"). '/' . $image_name))) {
                continue;
            } else {
                // if the file exist in uploads we need to move it to the correct folder of the model
                if (File::exists(storage_path('app/uploads/' . $image_name))) {
                    File:: move(storage_path('app/uploads/' . $image_name), storage_path('app/' .constant("$image->imageable_type::IMAGE_FOLDER"). '/' . $image_name));
                } else {
                    //If the file does not exist we will delete it.
                    $image->delete();
                }
            }
        }
    }
}
