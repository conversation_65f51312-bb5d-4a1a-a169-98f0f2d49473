<?php

namespace App\Console\Commands\Clean;

use Illuminate\Console\Command;

use App\Models\WebMeta as Meta;

class WebMeta extends Command
{

    protected $signature = 'clean:web-meta';
    protected $description = 'Updates web meta table to new format';

    public function handle()
    {
        print_r("Cleaning web meta...\n");

        Meta::where('slug', 'like', '^%')->get()->each(
            function ($meta) {
                $newSlug = preg_replace('/[^A-Za-z0-9\-]/', '', $meta->slug);
                $meta->slug = "/$newSlug";
                $meta->save();
            }
        );

        if ($default = Meta::where('slug', 'DEFAULT')->first()) {
            $default->slug = "/";
            $default->save();
        }

        print_r("Web meta cleaned.\n");
    }
}
