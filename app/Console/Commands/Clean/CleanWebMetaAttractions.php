<?php

namespace App\Console\Commands\Clean;

use Illuminate\Console\Command;

use App\Models\WebMeta as Meta;

class CleanWebMetaAttractions extends Command
{

    protected $signature = 'clean:web-meta-attractions';
    protected $description = 'Removes the word attraction from web urls';

    public function handle()
    {
        print_r("Cleaning web meta...\n");

        $webMeta = Meta::all();

        foreach ($webMeta as $meta) {
            if (str_contains($meta->slug, '/attractions/')) {
                $meta->slug = preg_replace('/attractions\//', '', $meta->slug, 1);
                $meta->save();
            }
        }

        print_r("Web meta cleaned.\n");
    }
}
