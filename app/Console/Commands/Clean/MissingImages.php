<?php

namespace App\Console\Commands\Clean;

use Storage;

use App\Models\Photo;

use Illuminate\Console\Command;

class MissingImages extends Command
{

    protected $signature = 'clean:missing-images';

    protected $description = 'Finds and prints IDs and paths to missing images.';

    public function handle()
    {
        $directories = collect(Storage::allDirectories());

        $missing = Photo::all()->filter(
            function ($photo) use ($directories) {
                // Search in all subdirs
                $exists = $directories->search(
                    function ($directory) use ($photo) {
                        return Storage::exists("{$directory}/{$photo->image_name}");
                    }
                );
                // Search in base dir
                if (!$exists) {
                    $exists = Storage::exists($photo->image_name);
                }

                return !$exists;
            }
        )->map(
            function ($photo) {
                    $typeArray = explode('\\', $photo->imageable_type);
                    $type = array_pop($typeArray);
                    return [
                    'photo' => $photo,
                    'message' => "Photo ID {$photo->ID} with name {$photo->image_name} is missing on disk, related to {$type} ID {$photo->imageable_id}\n",
                    'id' => $photo->imageable_id,
                    'type' => $type
                    ];
            }
        );

        print_r("Found {$missing->count()} missing images.\n");
        $missing->each(
            function ($missing) {
                print_r($missing['message']);
            }
        );
    }
}
