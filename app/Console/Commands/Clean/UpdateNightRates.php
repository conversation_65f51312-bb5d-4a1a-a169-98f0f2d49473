<?php

namespace App\Console\Commands\Clean;

use Carbon\Carbon;

use Illuminate\Console\Command;

use App\Models\Rate;

/**
 * Night rates used to use a system where if the exit time was before the enter time, the exit
 * time was assumed to be next day. We have switched to a system where next day times will be expressed
 * by hours over 24, e.g. 6 a.m. next day would be 30:00:00. This script updates night rates to be compatible with the
 * new logic.
 */
class UpdateNightRates extends Command
{

    protected $signature = 'clean:night-rates';
    protected $description = 'Update night rates to be compatible with new over-24-hour system.';

    public function handle()
    {
        if (!$this->confirm('You are about to update night rates to a new format. Have you made a backup?')) {
            return;
        }

        $rates = Rate::whereColumn('entry_time_end', '<', 'entry_time_begin')
                    ->orWhereColumn('exit_time_end', '<', 'exit_time_begin')
                    ->withTrashed()
                    ->get();

        if (!$rates->count()) {
            return print_r("No rates with old format present.\n");
        }

        $bar = $this->output->createProgressBar($rates->count());

        $rates->each(
            function ($rate) use ($bar) {
                if ($rate->entry_time_end < $rate->entry_time_begin) {
                    $rate->entry_time_end = $this->add24Hours($rate->entry_time_end);
                }

                if ($rate->exit_time_end < $rate->exit_time_begin) {
                    $rate->exit_time_end = $this->add24Hours($rate->exit_time_end);
                }

                $rate->save();
                $bar->advance();
            }
        );

        $bar->finish();
    }

    // $time will be of format HH:mm:ss
    protected function add24Hours($time)
    {
        $time = explode(':', $time);
        $time[0] = $time[0] + 24;
        return implode(':', $time);
    }
}
