<?php

namespace App\Console\Commands\Clean;

use Illuminate\Console\Command;
use App\Models\CMSPage;

class ReplaceCmsUrls extends Command
{
    protected $signature = 'clean:ReplaceCmsUrls {oldName} {newName}';
    protected $description = 'This will replace the domain name of the CMS content with a new name';

    public function handle()
    {
        $replace = $this->argument('newName');
        $search = $this->argument('oldName');

        $entries = CMSPage::all();

        foreach ($entries as $entry) {
            $entry->content = str_replace($search, $replace, $entry->content);
            $entry->save();
            print_r("Changed `$entry->slug` \n");
        }
    }
}
