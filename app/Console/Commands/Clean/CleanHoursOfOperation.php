<?php

namespace App\Console\Commands\Clean;

use Illuminate\Console\Command;

use App\Models\Facility;
use App\Models\HoursOfOperation;

class CleanHoursOfOperation extends Command
{

    protected $signature = 'clean:hoursofoperation';
    protected $description = 'Remove hours of operation if a facilty is open 24/7.';

    public function handle()
    {
        // Remove hours of operation if a facility is open 24/7
        Facility::all()->each(
            function ($facility) {
                if ($facility->open_247 && $facility->hoursOfOperation->count()) {
                    $facility->hoursOfOperation()->delete();
                }
            }
        );

        // Clean up hours of operation that have invalid facility IDs
        HoursOfOperation::all()->each(
            function ($hours) {
                if (!$hours->facility) {
                    $hours->delete();
                }
            }
        );

        print_r("Hours of operation table cleaned.");
    }
}
