<?php

namespace App\Console\Commands\Clean;

use DB;
use Illuminate\Console\Command;

class UpdateFacilityDisclaimers extends Command
{

    protected $signature = 'clean:facility-disclaimers';

    protected $description = 'Removes requirement to print coupons from facility default disclaimer.';

    public function handle()
    {
        $toReplace = "Coupon must be printed in advance and surrendered to cashier at time of payment to receive discount rate. ";

        DB::update("update facilities set disclaimer = replace(disclaimer, :toReplace, '')", ['toReplace' => $toReplace]);

        print_r("Facility disclaimers updated.\n");
    }
}
