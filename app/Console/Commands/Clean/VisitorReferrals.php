<?php

namespace App\Console\Commands\Clean;

use Carbon\Carbon;

use Illuminate\Console\Command;

use App\Models\Visitor\VisitorCode;
use App\Models\Visitor\VisitorCouponPrint;
use App\Models\Visitor\VisitorCouponView;
use App\Models\Visitor\VisitorCouponEmail;
use App\Models\Visitor\VisitorFacilityMessage;

class VisitorReferrals extends Command
{

    protected $signature = 'clean:visitor-referrals';

    protected $description = 'Retroactively assign referral IDs to visitor tracking events.';

    protected $found = 0;
    protected $notFound = 0;

    public function handle()
    {
        collect(
            [
            VisitorCouponEmail::class,
            VisitorCouponPrint::class,
            VisitorCouponView::class,
            VisitorFacilityMessage::class
            ]
        )->each(
            function ($eventClass) {
                    $this->processEventType($eventClass);
            }
        );

        print_r("\nReferral assignment finished.\n");
        print_r("{$this->found} events were assigned referrals.\n");
        print_r("{$this->notFound} events could not be assigned referrals.\n");
    }

    public function processEventType($eventClass)
    {
        print_r("\nProcessing $eventClass events:\n");

        $query = $eventClass::query()
                    ->where('created_at', '>=', '2016-10-04 00:00:00')
                    ->whereNull('visitor_referral_id');

        $bar = $this->output->createProgressBar($query->count());

        $query->get()->each(
            function ($event) use ($bar) {
                $referral = $event->referralByTimestamp();
                if (!$referral) {
                    $bar->advance();
                    $this->notFound++;
                    return;
                }

                $event->referral()->associate($referral)->save();
                $this->found++;
                $bar->advance();
            }
        );

        $bar->finish();
    }
}
