<?php

namespace App\Console\Commands\Inventory;

use Exception;
use Artisan;
use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Console\Commands\Deploy\Transfer\TransferBase;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Exceptions\ApiGenericException;

use App\Models\Reservation;
use App\Models\HoursOfOperation;
use App\Models\FacilityAvailability;
use App\Jobs\UpdateFacilityInventory;
use App\Classes\Inventory;

class UpdateAvailability extends Command
{
    use DispatchesJobs;

    protected $signature = 'cron:update-availability-by-reservation';

    const DEFAULT_CONFIRM_VAL = 1;
    const DEFAULT_CANCEL_VAL = 2;    
    const END_TIME_EXTRA_MINUTES = 30;
    const ADD_EXTRA_DAY_COUNT = 1;
    const DEFAULT_HOURS = 0;
    const TWENTY_FOUR_HOURS = 23;
    const RESERVATION_TYPE = 'PARKENGAGE';
    

    protected $description = 'Updating availability by reservation- for those did not got update at that time';

    protected $log;



    /**
     * Execute the console command.
     *
     * @return mixed
     */
   
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
    
        $this->log = $logFactory->setPath('logs/availibility_update_by_reservation_cron')->createLogger('update_availability_by_reservations');
    }

    public function handle()
    {  
         $reservationsData = Reservation::where('is_avaiability_updated','0')->whereNull('cancelled_at')->get();
         if(count($reservationsData)>0)
         {
            foreach ($reservationsData as $reservationData)
            {
               $this->log->info("Reservation id-- $reservationData->id");
                try{
                    $this->updateReservationAvailability($reservationData->id);
                }catch (Exception $ex) {
                    
                }
            }
         }else{
			 $this->log->info("No reservation data");
		 }
    }
    
    public function updateReservationAvailability($reservation_id)
    {
       $reservation = Reservation::where('id',$reservation_id)->first();
       $type = self::DEFAULT_CONFIRM_VAL;   
       $facility_id= $reservation->facility_id;
       $date_time_in= date('Y-m-d H:i:s',strtotime($reservation->start_timestamp));
       
       $reservation_length= $reservation->length;
       $length= round($reservation->length,0);
        
       $date_time_out =  date('Y-m-d H:i:s',strtotime(Carbon::parse($date_time_in)->addHours($length)));
       
        $reservation_minutes = 0;
        $reservation_hours = explode(".",$reservation->length);
        if(isset($reservation_hours[1]) && ($reservation_hours[1])>0)
        {
                $reservation_minutes = 30;
        }
        $reservation_date_time_out =  date('Y-m-d H:i:s',strtotime(Carbon::parse($date_time_in)->addHours($reservation_length)->addMinutes($reservation_minutes)));

        
       $inventoryRepository = new Inventory();
        
      //check how many slots does entry and exit time occupies
       $difference = date_diff(date_create(date('Y-m-d',strtotime($date_time_in))), date_create(date('Y-m-d',strtotime(($date_time_out)))));
       
     //check if someone is parking for more than a day
     try{ 
      if ($difference->d > 0) {
         
        $dates   = $inventoryRepository->generateArrayOfDates(
          ($difference->d + self::ADD_EXTRA_DAY_COUNT), date('Y-m-d H:i:s', strtotime($date_time_in)));
      
      $dayDifference = $difference->d;
      
      foreach ($dates as $key => $date) {

        $dayIn = date('w', strtotime($date->format('Y-m-d')));
       
        $hours = HoursOfOperation::where(['facility_id' => $facility_id, 'day_of_week' => $dayIn])->first();
       
        $startingHour = self::DEFAULT_HOURS;
        $endingHour   = self::TWENTY_FOUR_HOURS;

        if ($hours) {
          $startingHour = date('G', strtotime($hours->open_time));
          $endingHour   = date('G', strtotime($hours->close_time));
        }

        $facilityAvailability = FacilityAvailability::where(
            ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')])->first();
        
        if ($facilityAvailability) {
          $inventory = json_decode($facilityAvailability->availability);
          if ($key == 0) {
            /**
             * because this is the first day in the dates provided
             * we should remove 1 from each time_slot starting
             * from the hour provided in the api call
             */
            $i = date('G', strtotime($date_time_in));
            if ($startingHour > $i) {
              $i = $startingHour;
            }

//            $loopEnd = ($endingHour < self::TWENTY_FOUR_HOURS) ? $endingHour : self::TWENTY_FOUR_HOURS;
              $loopEnd  =  self::TWENTY_FOUR_HOURS;
            while ($i <= $loopEnd) {
              if(isset($inventory->{$i}))
	     {				  
                    if($type == self::DEFAULT_CANCEL_VAL)
                   {    
                       $inventory->{$i} = $inventory->{$i} + 1;
                    }else if($type == self::DEFAULT_CONFIRM_VAL)
                   {    
                     $inventory->{$i} = $inventory->{$i} - 1;
                   }
	       }
                 $i++;
              }
            } elseif ($key == $dayDifference) {
            $i = date('G', strtotime($date_time_out));
            $minutes = date('i', strtotime($reservation_date_time_out));
	    $starting_minutes = date('i', strtotime($date_time_in));
            if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
                $i++;
            }
            /**
             * because this is the last day in the dates provided
             * we should remove 1 from each time_slot starting
             * till the hour provided in the api call
             */
          
            $j  = 0;
            while ($j < $i) {
                if(isset($inventory->{$j}))
               {
                    if($type == self::DEFAULT_CANCEL_VAL)
                  {    
                    $inventory->{$j} = $inventory->{$j} + 1;
                  }else if($type == self::DEFAULT_CONFIRM_VAL)
                  {
                      $inventory->{$j} = $inventory->{$j} - 1;
                  }
               }
               $j++;
            }
          } else {
            /**
             * because this could be any day except first and last in
             * the dates provided we should remove 1 from whole day
             */
            
            $k = 0;
            while ($k <= self::TWENTY_FOUR_HOURS) {
               if(isset($inventory->{$k}))
               {
                if($type == self::DEFAULT_CANCEL_VAL)
                {    
                  $inventory->{$k} = $inventory->{$k} + 1;
                }else   if($type == self::DEFAULT_CONFIRM_VAL)
                {
                    $inventory->{$k} = $inventory->{$k} - 1;
                }
               }
                $k++;
            }
          }

        $facilityAvailability->availability = json_encode($inventory, JSON_FORCE_OBJECT);
        $facilityAvailability->save();
        }
      }
      if($type == self::DEFAULT_CONFIRM_VAL)
        {
           $reservation->is_avaiability_updated = self::DEFAULT_CONFIRM_VAL;
        }else  if($type == self::DEFAULT_CANCEL_VAL)
        {
            $reservation->is_avaiability_updated = self::DEFAULT_CANCEL_VAL;
        }
        $reservation->save();
     } else {
         
      $startingHour = date('G', strtotime($date_time_in));
      $endingHour   = date('G', strtotime($date_time_out));
      $facilityAvailability     = FacilityAvailability::where(
          ['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))])->first();
      
      if ($facilityAvailability) {
        $availability = json_decode($facilityAvailability->availability);
        $minutes = date('i', strtotime($reservation_date_time_out));
	$starting_minutes = date('i', strtotime($date_time_in));
        if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
            $endingHour++;
        }
        
        while ($startingHour < $endingHour) {
             if(isset($availability->{$startingHour}))
		   {
               if($type == self::DEFAULT_CANCEL_VAL)
               {   
                 $availability->{$startingHour} = $availability->{$startingHour} + 1;
             
               }else if($type == self::DEFAULT_CONFIRM_VAL)
              {  
                  $availability->{$startingHour} = $availability->{$startingHour} - 1;
               }
		   }
          $startingHour++;
        }
     
        $facilityAvailability->availability = json_encode($availability, JSON_FORCE_OBJECT);
        if($type == self::DEFAULT_CONFIRM_VAL)
        {
           $reservation->is_avaiability_updated = self::DEFAULT_CONFIRM_VAL;
        }else  if($type == self::DEFAULT_CANCEL_VAL)
        {
            $reservation->is_avaiability_updated = self::DEFAULT_CANCEL_VAL;
        }
        
        $reservation->save();        
        $facilityAvailability->save();
      }
    }
	 }catch(\Exception $e)
	 {
		 $this->log->info("$e");
	 }
    
        //initialise the queue to update partner ineventory db
		$this->log->info("Job register for Reservation id-- $reservation_id type self::RESERVATION_TYPE");
        $updateJobParams = ['reservationId' => $reservation_id, 'type' =>self::RESERVATION_TYPE];
        Artisan::queue('cron:update-inventory', $updateJobParams);
        // Return reservation and charge details to caller
        return true;
    }


}
