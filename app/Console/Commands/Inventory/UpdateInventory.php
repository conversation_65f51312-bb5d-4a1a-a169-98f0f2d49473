<?php

namespace App\Console\Commands\Inventory;

use Exception;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use App\Console\Commands\Deploy\Transfer\TransferBase;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Exceptions\ApiGenericException;

use App\Models\Reservation;
use App\Jobs\UpdateFacilityInventory;

class UpdateInventory extends Command
{
    use DispatchesJobs;

    protected $signature = 'cron:update-inventory
                             {reservationId}{type}';

    const QUEUE_NAME = 'iqp-inventory';


    protected $description = 'Updating inventory for partner database';

    protected $log;



    /**
     * Execute the console command.
     *
     * @return mixed
     */
   
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
    
        $this->log = $logFactory->setPath('logs/inventory')->createLogger('update_inventory');
    }



    public function handle()
    {  
         $reservation_id = $this->argument('reservationId');
         $type = $this->argument('type');  
         $this->log->info("Job called for -- $reservation_id type $type");
         $this->dispatch((new UpdateFacilityInventory($reservation_id, $type))->onQueue(self::QUEUE_NAME)); 
          
    }


}
