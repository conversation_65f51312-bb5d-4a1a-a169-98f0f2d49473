<?php

namespace App\Console\Commands\UserPass;

use Mail;
use File;
use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Http\Helpers\MailHelper;
use View;
use App\Services\LoggerFactory;
use App\Models\UserPass;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\User;
use App\Services\Pdf;
use Illuminate\Support\Facades\Storage;

/**
 * Emails reservation stub to user
 */
class Email extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'pass:email {passId} {common?} {email?}';
  protected $log = '';

  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Email success to pass user.';


  public function __construct(LoggerFactory $logFactory)
  {
    parent::__construct();
    $this->log = (new LoggerFactory)->setPath('logs/userpass')->createLogger('userpass');
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
    $id = $this->argument('passId');
    $common = $this->argument('common');
    //add email code to send admin email when email does not have
    $email = $this->argument('email');
    $pass = UserPass::with(['user', 'rate', 'mapcoQrCode.event', 'mapcoQrCode.eventCategory.eventCategoryEvent.event'])->where('id', $id)->first();
    if (!$pass) {
      throw new NotFoundException('No pass with that ID.');
    }

    $imageBarcode = $pass->generateBarcodeJpgNew();

    $imageBarcodeFileName = str_random(10) . '_barcode.png';
    Storage::put($imageBarcodeFileName, $imageBarcode);

    $startDate = $pass->start_date;
    $endDate = $pass->end_date;

    $pass['start_date'] = date("M d, Y", strtotime($pass->start_date));
    $pass['end_date'] = date("M d, Y", strtotime($pass->end_date));
    //$pass['start_date'] = $this->getDaySufixFormat($pass->start_date);
    //$pass['end_date'] = $this->getDaySufixFormat($pass->end_date);
    $brand_setting = BrandSetting::where('user_id', $pass->partner_id)->first();
    $facility = Facility::where('owner_id', $pass->partner_id)->first();
    $data['data'] = $pass;
    $data['brand_setting'] = $brand_setting;
    $pdf = (new UserPass())->generatePdf($data, Pdf::class, '1');
    $pdfName = '';
    if (isset($data->rate->description)) {
      $pdfName = $pass->pass_code;
    } else {
      $pdfName = $pass->pass_code;
    }

    $view = 'userpass.email';
    if ($common == '1') {
      //dynamic created for USM
      $view = 'userpass.email-pass';
    }

    if (isset($email) && $email != '') {
      $pass['email'] = $email;
    }

    $view_text = $view . '-plain-text';
    if (!View::exists($view_text)) {
      $view_text = $view;
    }
    try {
      $this->log->info("user pass user email sending: $pass->email");
      // Change Request PIMS-12502 : Vijay - 30-01-2025 
      $subject = "Your pass has been confirmed";
      $filedata['type']       = 'runtime';
      $filedata['content']    = $pdf;
      $filedata['filename']   = $pdfName . ".pdf";
      $filedata['format']     = 'pdf';
      MailHelper::sendEmail($pass->email, ['text' => $view_text, 'html' => $view], ['subject' => $subject, 'data' => $pass, 'brand_setting' => $brand_setting, 'bar_image_path' => $imageBarcodeFileName, 'facility' => $facility, 'filedata' => $filedata], $pass->partner_id);
      $this->log->info("Mail sent to success : " . $pass->email);
      // Change Close : PIMS-12502
      //  Mail::send(
      //     ['text'=>$view_text,'html'=>$view],
      //     ['data' => $pass, 'brand_setting' => $brand_setting,'bar_image_path' => $imageBarcodeFileName,'facility' => $facility],
      //     function ($message) use ($pass, $pdf, $pdfName) {
      //         $message->to($pass->email)->subject("Your pass has been confirmed");
      //         $message->from(config('inventory.email'));
      //         $message->attachData($pdf,$pdfName.".pdf");
      //     }
      //  );             
      // $this->log->info("email sent: $pass->email");

      if (isset($email) && $email != '') {
        return true;
      }

      $accountSid = env('TWILIO_ACCOUNT_SID');
      $authToken  = env('TWILIO_AUTH_TOKEN');
      $client = new Client($accountSid, $authToken);


      $desiredStartDate = date("d F, Y", strtotime($startDate));
      $desiredEndDate = date("d F, Y", strtotime($endDate));
      $total = $pass->total;
      $msgBody = '';

      $user = User::find($pass->partner_id);
      $companyName = '';
      if ($user) {
        $companyName = " for " . $user->company_name;
      }

      if ($pass->partner_id == "45" || $pass->partner_id == "358642") {
        $msgBody = "Thank you for purchasing a parking pass for " . $facility->full_name . ". \nPass # : $pass->pass_code \nStart Date : $desiredStartDate \nEnd Date : $desiredEndDate \nAmount Charged : $$total";
      } else {
        $msgBody = "Thank you for purchasing a parking pass" . $companyName . ". \nPass # : $pass->pass_code \nStart Date : $desiredStartDate \nEnd Date : $desiredEndDate \nAmount Charged : $$total";
      }

      try {
        // Use the client to do fun stuff like send text messages!
        $client->messages->create(
          // the number you'd like to send the message to
          $pass->user->phone,
          array(
            // A Twilio phone number you purchased at twilio.com/console
            'from' => env('TWILIO_PHONE'),
            // the body of the text message you'd like to send
            //'body' => "Fine"
            'body' => $msgBody
          )
        );
      } catch (RestException $e) {
        //throw new ApiGenericException($e->getMessage());
        //return "success";
      }
    } catch (\Exception $e) {
      $errorMessage = array();
      $errorMessage['message'] = $e;
      $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
      $this->log->error('Issue in email sending:' . $msg);
      // dd($msg);
    }
  }


  public function getDaySufixFormat($date)
  {
    $day = date('d', strtotime($date));
    $monthYear = date('F, Y', strtotime($date));
    $number = (string) $day;
    $last_digit = substr($number, -1);
    $second_last_digit = substr($number, -2, 1);
    $suffix = 'th';
    if ($second_last_digit != '1') {
      switch ($last_digit) {
        case '1':
          $suffix = 'st';
          break;
        case '2':
          $suffix = 'nd';
          break;
        case '3':
          $suffix = 'rd';
          break;
        default:
          break;
      }
    }
    if ((string) $number === '1') $suffix = 'st';
    return $number . $suffix . ' ' . $monthYear;
  }
}
