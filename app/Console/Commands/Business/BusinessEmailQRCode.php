<?php

namespace App\Console\Commands\Business;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use Twilio\Rest\Client;
use <PERSON>wi<PERSON>\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use Illuminate\Support\Collection;
use Storage;
use DB;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\ParkEngage\BrandSetting;
use App\Models\BusinessQrCode;
use App\Models\ParkEngage\FacilityBrandSetting;

/**
 * Emails reservation stub to user
 */
class BusinessEmailQRCode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'businessQrcode:email {id} {email}';

 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sent Business QRcode Email.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/businessemail')->createLogger('businessemail');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
		$qrcode_id = $this->argument('id');   
        $email_id =  $this->argument('email');
		
        $data = BusinessQrCode::with('Business')->where('id',$qrcode_id)->first();
        if (!$data) {
             $this->log->info("QR code details not found.");
          
        }
        
        $brand_setting = '';
        
        $brand_setting = BrandSetting::where('user_id', $data->Business->partner_id)->first();
        if($brand_setting){
            $data->brandsetting_id = $brand_setting->id;
            
        }else{
            $facility_brand_setting = FacilityBrandSetting::where('facility_id', $data->facility_id)->first();
            if($facility_brand_setting){
                $data->facility_brand_id = $facility_brand_setting->id;
            }
        }
         // $url = env('QRCODE_AUTOVALIDATION');
        //$qrcode_path = "$url/townsend/" . base64_encode($data->business_qrcode);
        //$data->qrcode = $qrcode_path;
        
        $imageBarcodeFileName = str_random(10) . '_businessqrcode.png';
		Storage::put($imageBarcodeFileName, $this->generateBarcodeJpgNew($data->qrcode));
		$data->updated_qrcode = $imageBarcodeFileName;
        $data->email_id = $email_id;
        $view='businessclerk.send-qrcode';
        $view_text=$view.'-plain-text';
        if(!View::exists($view_text))
        {
            $view_text=$view;
        } 
         try{ 
            $this->log->info("Business user email sending: $email_id");
             Mail::send(
                ['text'=>$view_text,'html'=>$view],
                ['data' => $data,'brand_setting' => $brand_setting],
                function ($message) use ($data) {
                    $message->to($data->email_id)->subject("Sent QR Code");
                    $message->from(config('parkengage.default_sender_email'));
                }
             );             
            $this->log->info("email sent:" .$data->Business->email);
        }catch (\Exception $e)
        {
            $errorMessage=array();
            $errorMessage['message']=$e;
            $this->log->error('Issue in email sending:',$errorMessage);
        }
    }

    public function generateBarcodeJpgNew($qrcode)
	{
	  $html = $this->generateBarcodeHtml($qrcode);
  
	  $image = app()->make(Image::class);
	  $image->setOption('width', '420');
	  return $image->getOutputFromHtmlString($html);
	}
  
	public function generateBarcodeHtml($qrcode)
	{
		
	  return view('businessclerk.Qrcodegenerate', ['qrcode' => $qrcode]);
	}




}
