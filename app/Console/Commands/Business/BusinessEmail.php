<?php

namespace App\Console\Commands\Business;

use Illuminate\Support\Facades\Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Http\Helpers\MailHelper;
use View;
use App\Services\LoggerFactory;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use Illuminate\Support\Collection;
use Storage;
use Illuminate\Support\Facades\DB;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\ParkEngage\BrandSetting;
use App\Models\PasswordReset;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use Carbon\Carbon;
use App\Http\Helpers\QueryBuilder;

/**
 * Emails reservation stub to user
 */
class BusinessEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'businessemail:email {id}/{password}';
    protected $log;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Invitation Email to Business Membership Email.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/businessemail')->createLogger('businessemail');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //const USERTYPE = 8; ->where('user_type', self::USERTYPE)
        $user_id = $this->argument('id');

        $userDetails = User::whereNull('deleted_at')->where('id', $user_id)->first();
        if (!$userDetails) {
            $this->log->info("No user with that ID.");
            //  throw new NotFoundException('No user with that ID.');
        }
        $brand_setting = '';
        if ($userDetails) {
            $brand_setting = BrandSetting::where('user_id', $userDetails->created_by)->first();
        }
        //$userData = [];
        //$userData['userDetails'] = $userDetails;
        $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("WEB_URL", $userDetails->created_by);

        if ($dynamicReceiptUrl) {
            $url = $dynamicReceiptUrl->value;
            $userDetails->loginurl = $url;
        } else {
            $userDetails->loginurl = env('WEB_ADMIN_URL');
        }
        $secret = OauthClient::where('partner_id', $userDetails->created_by)->first();
        // Password Change
        // Generate password reset token
        $reset = PasswordReset::firstOrNew(['email' => $userDetails->email]);
        $reset->generatePasswordResetToken();
        $reset->created_at = Carbon::now()->toDateTimeString();
        $reset->save();
        if ($dynamicReceiptUrl) {
            $url = $dynamicReceiptUrl->value;
            $reset_password_url = $url . "/generate-new-password/";
        } else {
            $reset_password_url = env('WEB_URL') . "/generate-new-password/";
        }

        $userDetails->password = $reset_password_url . $reset->token . "?is_create=1";
        //$userDetails->password = $this->argument('password');

        $view = 'businessclerk.account';
        $view_text = $view . '-plain-text';
        if (!View::exists($view_text)) {
            $view_text = $view;
        }
        try {
            $this->log->info("Business user email sending: $userDetails->email");
            // Change Request PIMS-12502 : Vijay - 30-01-2025 
            // PIMS-12502 : Vijay - 30-01-2025
            if (isset($userDetails->PartnerName->company_name)) {
                $subject = "Welcome to " . ucfirst($userDetails->PartnerName->company_name);
            } else {
                $subject = "Welcome to ParkEngage";
            }
            MailHelper::sendEmail($userDetails->email, ['text' => $view_text, 'html' => $view], ['subject' => $subject, 'data' => $userDetails, 'brand_setting' => $brand_setting], $userDetails->created_by);
            $this->log->info("email sent Business Success : $userDetails->email");
            // Change Close : PIMS-12502

            /* Mail::send(
                ['text'=>$view_text,'html'=>$view],
                ['data' => $userDetails,'brand_setting' => $brand_setting],
                function ($message) use ($userDetails) {
                    $message->to($userDetails->email)->subject("Welcome to ParkEngage");
                    $message->from(config('parkengage.default_sender_email'));
                }
            ); */
        } catch (\Exception $e) {
            $errorMessage = array();
            $errorMessage['message'] = $e;
            $this->log->error('Issue in email sending:', $errorMessage);
        }
    }
}
