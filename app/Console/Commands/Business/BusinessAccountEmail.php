<?php

namespace App\Console\Commands\Business;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;


/**
 * Emails reservation stub to user
 */
class BusinessAccountEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'business-clerk-account-create {id}/{password}';

 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email cancele to permit user.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/business/')->createLogger('business');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $id = $this->argument('id');
        $pass = $this->argument('password');
        $user= User::where('id',$id)->first();
        //dd($user);
        if (!$user) {
            throw new NotFoundException('No user with that ID.');
        }

       // $brand_setting = BrandSetting::where('user_id', $user->partner_id)->first();


		//$view='businessclerk.accountcreate';
		$view='businessclerk.email';
		//dd($view);
		//'brand_setting' => $brand_setting,
        $view_text=$view.'-plain-text';
        if(!View::exists($view_text))
        {
            $view_text=$view;
        } 
        try{ 
            $this->log->info("Business Addcount Creation email sending: $user->email");
            
			//dd([$user->email,$pass,$view,$id]);
			Mail::send(
                ['text'=>'','html'=>$view],
                ['data' => $user,'pass'=>$pass],
                function ($message) use ($user) {
                    $message->to($user->email)->subject("Your account booking has been created");
                    $message->from(config('parkengage.default_sender_email'));
                }
             );             
            $this->log->info("email sent: $user->email");
        }catch (\Exception $e)
        {
            $errorMessage=array();
            $errorMessage['message']=$e;
            $this->log->error('Issue in email sending:',$errorMessage);
        }       
    }
}
