<?php

namespace App\Console\Commands\DailyRevenueReport;

use Mail;
use Exception;
use Illuminate\Console\Command;
use DB;
use Carbon\Carbon;
use App\Http\Helpers\ReportBuilder;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\ParkEngage\BrandSetting;
use App\Http\Controllers\ParkEngage\ReportParkEngage;
use App\Services\LoggerFactory;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Contracts\Auth\Authenticatabl;
use Illuminate\Support\Facades\Log;

class KittredgeStreet extends Command
{

    // protected $signature = 'KittredgeStreet:daily-report';
    protected $signature = "kstreet:daily-report";


    protected $description = 'get one month data';

    protected $log;

    const PARTNER_ID = 2980;
    protected $request;
    protected $loggerFactory;
    protected $authNet;
    protected $cim;
    protected $reportBuilder;
    private $startOfMonth;
    private $endOfMonth;
    protected $partnerId;
    protected $facilityId;


    function __construct(Request $request, LoggerFactory $LoggerFactory, ReportBuilder $reportBuilder)
    {

        parent::__construct();
        $this->startOfMonth = Carbon::yesterday()->format('Y-m-d') . ' 00:00:00';
        $this->endOfMonth = Carbon::yesterday()->format('Y-m-d') . ' 23:59:59';
        // $this->startOfMonth =  '2025-01-01 00:00:00';
        // $this->endOfMonth = '2025-01-10 23:59:59';
        $request->replace([]);
        $this->loggerFactory = $LoggerFactory;
        $this->request = $request;
        $this->reportBuilder = $reportBuilder;


        Log::info('kstreet Cron Start here1111  --------  ' . $request->facility_id);
    }

    public function handle(Request $request)
    {
        try {
            $user = User::find(96052);  // Replace 1 with the ID of the user you want to authenticate
            Auth::login($user);  // Log the user in
            Auth::user();  // 
            $request->merge([
                'partner_id' => self::PARTNER_ID,
                'fromDate' => $this->startOfMonth,
                'toDate' => $this->endOfMonth,
                'facility_id' => 156,
                'email_send' => 1,
                'filename' => 1,
            ]);
            Log::info('Email Cron Start here  ' . $request->facility_id);
            //Log::info('Checkin Inital Data :  ' . json_encode($request->all()));

            $controller = new ReportParkEngage($request,  $this->loggerFactory, $this->reportBuilder);
            $getDetails =   $controller->breezeExcelReport($this->request);
            if (isset($getDetails[0]) && !empty($getDetails[0])) {
                $excelName = $getDetails[1];
                $excelpath = $getDetails[0];
                $revenueData = $getDetails[2];
                $data['totalTickets']   = $revenueData['totalTickets'];
                $data['netValue']       = $revenueData['totalRevenue'];
                $data['overAllTotal']   = $revenueData['totalRevenue'];
                $data['report_name']    = 'daily_revenue_report';
                $data['location_name']  = "2020 Kittredge Street";
                $data['mail_body']      = "PFB summary of today’s Revenue Report. A detailed report is also attached.";
                Log::info('Mail Star for Kstreet :  ');
                Mail::send('daily-report-email.daily-email-format', $data, function ($message)  use ($excelName, $excelpath, $revenueData) {
                    // $message->to(config('parkengage.townsend.kstreet_report_emails'));
                    $message->to(['<EMAIL>', '<EMAIL>']);
                    $dateRange =  date("d F, Y", strtotime($this->startOfMonth)) . '-' . date("d F, Y", strtotime($this->endOfMonth));
                    $subject = ' Daily Revenue Report - ' . $dateRange;
                    $message->subject($subject);
                    $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                    $path_to_file = $excelpath;
                    if (file_exists($path_to_file)) {
                        $message->attach($path_to_file);
                    }
                    Log::info('Mail sent success for Kstreet :  ' . $path_to_file);
                });
            }else{
                $data['totalTickets']   = 0;
                $data['netValue']       = 0;
                $data['overAllTotal']   = 0;
                $data['report_name']    = 'daily_revenue_report';
                $data['location_name']  = "2020 Kittredge Street";
                $data['mail_body']      = "No Data Found";
                Mail::send('daily-report-email.daily-email-format', $data, function ($message) {
                    $message->to(['<EMAIL>', '<EMAIL>']);
                    $dateRange =  date("d F, Y", strtotime($this->startOfMonth)) . '-' . date("d F, Y", strtotime($this->endOfMonth));
                    $message->subject('Daily Revenue Report - ' . $dateRange);
                    $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));

                    Log::info('No transaction report email sent for Metro.');
                
                });
            }
            Auth::logout();
        } catch (\Throwable $th) {
            $msg = "Error Message: " . $th->getMessage() . ", File: " . $th->getFile() . ", Line Number: " . $th->getLine();
            Log::info($msg);
        }
    }
}
