<?php

namespace App\Console\Commands\Yenkee;

use App\Exceptions\NotFoundException;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\PermitRequest;
use App\Models\User;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class NotifyRenewPermit extends Command
{
    protected $log;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permit:renew-reminder-customer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send notification 5 days prior to customer for renew permit.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/Yenkee/')->createLogger('renew_permit');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->log->info("Called Handle function.");

            $facilityId = [155];
            $partnerId = [26380];

            $getFacilites = Facility::whereIn('owner_id',$partnerId)->get();
            
            foreach ($getFacilites as $facilities) {
                
                // Set time zone
                QueryBuilder::setCustomTimezone($facilities->id);

                $configuredDays = FacilityConfiguration::where('facility_id',$facilities->id)->whereIn('partner_id',$partnerId)->first();

                $permits = PermitRequest::where('permit_rate','!=',0)
                        ->where('user_consent','=',1)
                        ->where('status',1)
                        ->whereNull('deleted_at')
                        ->whereNull('cancelled_at')
                        ->whereIn('partner_id', $partnerId)
                        ->where('facility_id', $facilities->id)
                        ->get();
                
                if (!$permits) {
                    $this->log->info("Permits Not Found.");
                }
                $this->log->info("Permit Details: ".json_encode($permits));

                // currentDate 
                $currentDate = Carbon::now();
                $this->log->info("Current Time: ".$currentDate);

                // Get the start of the next month
                $startOfNextMonth = $currentDate->copy()->startOfMonth()->addMonth();
                $this->log->info("Future Month: ".$startOfNextMonth);

                
                $formattedNextMonthDate = $startOfNextMonth->toDateString();

                // 5th previous day
                $previous5thDate = $currentDate->subDays($configuredDays['permit_reminder_days']);
                
                $this->log->info("Previous 5th day of the month: ".$previous5thDate);
                
                // Format the date (YYYY-MM-DD)
                $previoudDay = $previous5thDate->toDateString();
                $this->log->info("formated(Y-m-d) 5th day of the month: ".$previoudDay);

                
                $this->log->info("compare current date ".$currentDate->toDateString() .'==="compare previous date"'. $previoudDay);
                
                if($currentDate->toDateString() === $previoudDay)
                {
                    $this->log->info("Under if condition");
                    foreach ($permits as $value) 
                    {
                        $this->log->info("Under if foreach condition");
                        $partnerEmail = User::whereIn('id',$partnerId)->first();

                        $facility = Facility::where('id', $facilities->id)->first();
                        $this->log->info("facility details:" .json_encode($facility));
        
                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $facilities->id)->first();
        
                        if($facility_brand_setting){
                            $value->facility_logo_id = $facility_brand_setting->id;
                            $this->log->info("facility_logo_id:" .$value->facility_logo_id);
                            
                        }else{
                            $brand_setting = BrandSetting::whereIn('user_id', $partnerId)->first(); 
                            $value->logo_id  = $brand_setting->id;
                            $this->log->info("facility_logo_id:" .$value->log_id);
        
                        }
                        $brand_setting = BrandSetting::whereIn('user_id', $partnerId)->first();
                        $this->log->info("Brand setting details:" .json_encode($brand_setting));

                        // get Partner Slug
                        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facilities) {
                            $join->on('user_facilities.user_id', '=', 'users.id');
                            $join->where('user_facilities.facility_id', "=", $facilities->id);
                          })->whereIn('created_by', $partnerId)->whereNotNull('slug')->where('user_type', 12)->first();
                          
                          if($getRM){
                            $partnerSlug = $getRM->slug;
                            
                          }else{
                            $checkPaymentUrl = UserPaymentGatewayDetail::whereIn('user_id', $partnerId)->first();
                            $partnerSlug  =  $checkPaymentUrl->touchless_payment_url;
                          }

                        $url = env('WEB_URL_CUSTOMERPORTAL')."/".$partnerSlug;
                        //
                        $data['report_name']    = 'permit_renew_notify';
                        
                        Mail::send(
                            "yenkee.permit-renew-notify", ['facility'=> $facility,'data' => $value, 'date'=>$formattedNextMonthDate, 'brand_setting' => $brand_setting,'details' => $data, 'partner_details' => $partnerEmail,'url' => $url, 'facility_config' => $configuredDays], function ($message) use($value, $formattedNextMonthDate) {
                                $message->to($value->email);
                                // $message->to('<EMAIL>');
                                $message->subject("Your Permit #". $value->account_number ."  will be Renew on " .$formattedNextMonthDate);
                                $message->from(config('parkengage.default_sender_email'));
                            }
                        );
                        $this->log->info("Mail sent to ". $value->email); 
                    }
                }
            }
        } catch (\Throwable $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended"); 
        }
    }
}
