<?php

namespace App\Console\Commands\Yenkee;

use App\Exceptions\NotFoundException;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\PermitRequest;
use App\Models\User;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;

class PaymentDueExcelsheet extends Command
{
    protected $log;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permit:renew-reminder-partner';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send permit renewal payment list of users to admin';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/Yenkee/')->createLogger('payment_due');

    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->log->info("Called Handle function.");
            
            $facilityId = [155];
            $partnerId = [26380];

            $facilites = Facility::whereIn('owner_id',$partnerId)->get();
            foreach ($facilites as $facility) {
                // Set time zone
                QueryBuilder::setCustomTimezone($facility->id);

                $configuredDays = FacilityConfiguration::where('facility_id',$facility->id)->whereIn('partner_id',$partnerId)->first();

                $facility_brand_setting = FacilityBrandSetting::where('facility_id', $facility->id)->first();
                
                if($facility_brand_setting){
                    $facility->facility_logo_id = $facility_brand_setting->id;
                    $this->log->info("facility_logo_id:" .$facility->facility_logo_id);
                    
                }else{
                    $brand_setting = BrandSetting::whereIn('user_id', $partnerId)->first(); 
                    $facility->logo_id  = $brand_setting->id;
                    $this->log->info("Partner logo id:" .$facility->log_id);
                }
            
                $partnerEmail = User::select('email')->whereIn('id',$partnerId)->first();

                // currentDate 
                $currentDate = Carbon::now();
                $this->log->info("Current Time: ".$currentDate);
                
                // Get the last date of the current month
                $lastDateOfMonth = $currentDate->copy()->endOfMonth();
                $this->log->info("Last date of the month: ".$lastDateOfMonth);
                
                // Format the date (YYYY-MM-DD)
                $formattedLastDate = $lastDateOfMonth->toDateString();
                $this->log->info("Formatted Last date of the month: ".$formattedLastDate);

                // Get the start of the next month
                $startOfNextMonth = $currentDate->copy()->startOfMonth()->addMonth();
                $this->log->info("Future Month: ".$startOfNextMonth);

                $formattedNextMonthDate = $startOfNextMonth->toDateString();

                // 5th previous day
                $previous5thDate = $startOfNextMonth->subDays($configuredDays['permit_partner_reminder_d']);
                $this->log->info("Previous 5th day of the month: ".$previous5thDate);

                // Format the date (YYYY-MM-DD)
                $previoudDay = $previous5thDate->toDateString();
                $this->log->info("formated(Y-m-d) 5th day of the month: ".$previoudDay);
                // 

                $permitsLists = PermitRequest::where('permit_rate','!=',0)
                        ->where('user_consent','=',1)   
                        ->where('status',1)
                        ->whereNull('deleted_at')
                        ->whereNull('cancelled_at')
                        ->whereIn('partner_id', $partnerId)
                        ->where('desired_end_date', $formattedLastDate)
                        ->get();

                if (!$permitsLists) {
                    $this->log->info("Permits Not Found.");
                }
                $this->log->info("Permit Details: ".json_encode($permitsLists));
            
                $this->log->info("Permit list count: ".count($permitsLists).'===========current date '.$currentDate->toDateString().'++++++++previous day '.$previoudDay);

                if($currentDate->toDateString() === $previoudDay){

                    $this->log->info('Under if condition');
                    $color = $brand_setting->color;
                    $this->log->info('brand Setting color: ' . $color);

                    $excelSheetName = 'Permit Payment Renewal List-' . date('Y-m-d');
                    $this->log->info('Excel sheet name: ' . $excelSheetName);

                    $data = [];

                    Excel::create(
                        $excelSheetName,
                        function ($excel) use (
                            $color,
                            $permitsLists,
                            $facility,
                            $startOfNextMonth
                        ) {
                            $excel->sheet('Payment Renewal List', function ($sheet) use (
                                $color,
                                $permitsLists,
                                $facility,
                                $startOfNextMonth
                            ) {
                                $sheet->setWidth(array(
                                    'A'     => 15,
                                    'B'     =>  35,
                                    'C'     =>  35,
                                    'D'     =>  35,
                                    'E'     =>  35,
                                    'F'     =>  35,
                                    'G'     =>  25
                                ));

                                $this->log->info('cityparking 99 .');

                                $sheet->getColumnDimension('A')->setWidth(15);
                                $sheet->getColumnDimension('B')->setWidth(36);
                                $sheet->getColumnDimension('C')->setWidth(36);
                                $sheet->getColumnDimension('D')->setWidth(30);
                                $sheet->getColumnDimension('E')->setWidth(30);
                                $sheet->getColumnDimension('F')->setWidth(30);
                                $sheet->getColumnDimension('G')->setWidth(25);

                                //end width for colom here
                                //set header for excel work
                                $sheet->mergeCells('A1:G1');
                                //$sheet->mergeCells('F1:J1');
                                $sheet->getRowDimension(1)->setRowHeight(60);
                                $sheet->setCellValue('A1', $facility->full_name.' Permit Payment Renewal User List');
                                $sheet->cell('A1:G1', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('20');
                                });

                                $cellValue = "Print Date - \n" .  date('Y-m-d h:i');
                                $sheet->mergeCells('A2:B2');
                                $sheet->setCellValue('A2', $cellValue);
                                $sheet->getStyle('A2:B2')->getAlignment()->setWrapText(true);
                                // Set the height of cell H2 (adjust as needed)
                                $sheet->getRowDimension(2)->setRowHeight(80);
                                $sheet->getRowDimension(3)->setRowHeight(50);

                                $sheet->cell('A2:B2', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#D6DCE4');
                                    $cell->setFontColor('#000000');
                                    $cell->setFontSize('16');
                                });

                                $location = "Location Name \r" . $facility->full_name;
                                $sheet->mergeCells('C2:D2');
                                $sheet->setCellValue('C2', $location);
                                $sheet->getStyle('C2:D2')->getAlignment()->setWrapText(true);

                                $sheet->cell('C2:D2', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#ffffff');
                                    $cell->setFontColor('#000000');
                                    $cell->setFontSize('18');
                                });

                                $sheet->mergeCells('E2:G2');
                                $locationId = "Location ID \n" . $facility->garage_code;
                                $sheet->setCellValue('E2', "$locationId");
                                $sheet->getStyle('E2:F2')->getAlignment()->setWrapText(true);

                                $sheet->cell('E2:G2', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#D6DCE4');
                                    $cell->setFontColor('#040D12');
                                    $cell->setFontSize('18');
                                });

                                $sheet->cell('A3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('B3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('C3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('D3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('E3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('F3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $sheet->cell('G3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('18');
                                });
                                $this->log->info('cityparking 111 .');

                                $i = 1;
                                $sheet->cell('A3:I3', function ($cell) use ($color) {
                                    $cell->setAlignment('left'); // Center horizontally
                                    $cell->setValignment('left');
                                });
                                $index = 4;
                                $this->log->info('permit details:'.$permitsLists);

                                foreach ($permitsLists as $key => $permitsList) {

                                    $data['Sr No.'] = $i;
                                    $data['Name'] = $permitsList->name;
                                    $data['Email'] = $permitsList->email;
                                    $data['Phone'] = $permitsList->phone;
                                    $data['Due On'] = $startOfNextMonth;
                                    $data['End Date'] = $permitsList->desired_end_date;
                                    $data['Permit Number'] = $permitsList->account_number;
                                    $i++;

                                    $dataArr[] = $data;
                                    $row = $key + $index;
                                    $sheet->cell('A' . $row . ':F' . $row, function ($cell) use ($color) {
                                        $cell->setAlignment('left'); // Center horizontally
                                        $cell->setValignment('left');
                                    });
                                }
                                $sheet->fromArray($dataArr, [], 'A3', true, true);

                            });
                        }
                    )->store('xls');
                }

                $data['themeColor']     = $color;
                $data['report_name']    = 'permit_payment_dues';
                
                storage_path('exports/' . $excelSheetName . '.xls');
                $this->log->info("outside Excel sheet name : {{$excelSheetName}}");

                Mail::send('yenkee.permit-renew-notify', ['data' => $facility, 'brand_setting' => $brand_setting, 'details' => $data], function ($message)  use ($excelSheetName,$partnerEmail) {
                    // $message->to('<EMAIL>'); // test email
                    // $message->to($partnerEmail['email']); // Send email to partner
                    $message->to(config('parkengage.Yankees.PERMIT_PAYMENT_DUE_EMAILS'));
                    $message->subject('Permit Renewal Due User List');
                    $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                    $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                    $this->log->info("Mail Sent success with failname :  {$path_to_file}");
                    if (file_exists($path_to_file)) {
                        // $this->log->info("Path Of file and name 333 {$path_to_file}");
                        $message->attach($path_to_file);
                    }
                });
            }
        } catch (\Throwable $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended"); 
        }
    }
}
