<?php

namespace App\Console\Commands\cashier;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\FacilityBrandSetting;
/**
 * Emails reservation stub to user
 */
class CashierAccountEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cashier-account-create {id}/{password}/{slug}';

 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email cancele to permit user.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/cashier/')->createLogger('cashier');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $id = $this->argument('id');
        $pass = $this->argument('password');
		$slug = $this->argument('slug');
        $user = User::where('id',$id)->first();
        
        if (!$user) {
            throw new NotFoundException('No user with that ID.');
        }
        $user->pass = $pass;
        $facility = \DB::table('user_facilities')->where('user_id',$user->user_parent_id)->whereNull('deleted_at')->pluck('facility_id');
        $facility_brand_setting = FacilityBrandSetting::whereIn('facility_id',$facility)->first();
        if($facility_brand_setting){
            $user->facility_logo_id = $facility_brand_setting->id;
            $rgb_color = json_decode($facility_brand_setting->rgb_color, true);
            $user->background_color = 'rgba(' . $rgb_color['r'] . ',' . $rgb_color['g'] . ',' . $rgb_color['b'] . ',' . $rgb_color['a'] . ')';
        }else{
           $brand_setting = BrandSetting::where('user_id', $user->created_by)->first();
            $user->logo_id = $brand_setting->id;
            $rgb_color = json_decode($brand_setting->rgb_color, true);
            $user->background_color = 'rgba(' . $rgb_color['r'] . ',' . $rgb_color['g'] . ',' . $rgb_color['b'] . ',' . $rgb_color['a'] . ')';
        }
        $user->loginurl = env('WEB_URL_CASHIER').'/'.$slug;  
		
		$view='cashier.email';
		$view_text=$view.'-plain-text';
        if(!View::exists($view_text))
        {
            $view_text=$view;
        } 
        try{ 
            $this->log->info("Cashier Account Creation email sending: $user->email");
            
			Mail::send(
                ['text'=>$view_text,'html'=>$view],
                ['data' => $user,'pass'=>$pass],
                function ($message) use ($user) {
                    $message->to($user->email)->subject("Welcome to ParkEngage");
                    $message->from(config('parkengage.default_sender_email'));
                }
             );             
            $this->log->info("email sent: $user->email");
        }catch (\Exception $e)
        {
            $errorMessage=array();
            $errorMessage['message']=$e;
            $this->log->error('Issue in email sending:',$errorMessage);
        }       
    }
}
