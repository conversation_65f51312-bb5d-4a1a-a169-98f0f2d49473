<?php

namespace App\Console\Commands\Woodman;

use App\Exceptions\NotFoundException;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\ParkEngage\WoodmanLicensePlate;
use App\Models\Ticket;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\PermitVehicle;

class Notifications extends Command
{
    protected $log;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:email-reminder {partnerId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'If the customer does not open the parking session within that time and for extending parking session if his session has only 15 minutes left then notify to customer by email.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/Woodland')->createLogger('email_notify');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->log->info("start cron");

            $partnerId = $this->argument('partnerId');
            $this->log->info("partner id: " . $partnerId);

            $facilityId = [153];
            $location = Facility::where('owner_id', $partnerId)->whereIn('id', $facilityId)->first();
            $garageCode = $location['garage_code'];

            QueryBuilder::setCustomTimezone($location['id']);

            $currentDate = Carbon::now()->toDateString();
            $this->log->info("current date: " . $currentDate);

            $currentTime = Carbon::now()->toTimeString();
            $this->log->info("current time: " . $currentTime);

            $configTime = FacilityConfiguration::select('n_before_checkin')->whereIn('facility_id', $facilityId)
                ->where('partner_id', $partnerId)->first();

            $currentDateTime = \Carbon\Carbon::now();
            $nowDateTime = Carbon::parse('now')->format('Y-m-d ') . '00:00:00';
            $this->log->info("current date time: " . $currentDateTime);
            $this->log->info("nowDateTime : " . $nowDateTime);

            // \DB::enableQueryLog(); // Enable query log

            $licensePlateData = WoodmanLicensePlate::where('partner_id', $partnerId)
                ->whereNull("deleted_at")
                ->whereNotNull("entry_time")
                ->where("license_plate", "!=", '')
                ->whereIN("facility_id", $facilityId)
                ->where('gate_type', '=', 'entry')
                ->where(\DB::raw('CHAR_LENGTH(license_plate)'), '>', '3')
                ->where('entry_time', '>=', $nowDateTime)
                //->whereRaw("TIMESTAMPDIFF(MINUTE, entry_time, NOW()) >= 15")
                ->whereRaw("TIMESTAMPDIFF(MINUTE, entry_time, ?) >= ?", [$currentDateTime, $configTime['n_before_checkin']])
                ->orderBy('id', 'desc')
                ->get()
                ->toArray();

            // dd('Query ', \DB::getQueryLog()); // Show results of log
            if (count($licensePlateData) == 0) {
                $this->log->info('No licence plate data found.');
                throw new NotFoundException('No licence plate data found.');
            }
            $this->log->info('licence plate data available: ' . json_encode($licensePlateData));

            $this->log->info('Count licence plate data: ' . count($licensePlateData));

            if (count($licensePlateData) > 0) {
                $this->log->info('Woodman Excel sheet start.');

                $brandSetting = BrandSetting::where('user_id', $partnerId)->first();
                $color = $brandSetting->color;
                $this->log->info('brand Setting color: ' . $color);

                $excelSheetName = 'LPR Scanned list-' . date('m-d-Y H:i:s');
                $data = [];
                Excel::create(
                    $excelSheetName,
                    function ($excel) use (
                        $color,
                        $licensePlateData,
                        $location,
                        $garageCode,
                        $currentDateTime,
                        $configTime
                    ) {
                        $excel->sheet('License', function ($sheet) use (
                            $color,
                            $licensePlateData,
                            $location,
                            $garageCode,
                            $currentDateTime,
                            $configTime
                        ) {
                            $sheet->setWidth(array(
                                'A'     => 35,
                                'B'     =>  35,
                                'C'     =>  35
                            ));

                            $this->log->info('WLA 99 .');

                            $sheet->getColumnDimension('A')->setWidth(36);
                            $sheet->getColumnDimension('B')->setWidth(36);
                            $sheet->getColumnDimension('C')->setWidth(36);

                            //end width for colom here
                            //set header for excel work
                            $sheet->mergeCells('A1:C1');
                            //$sheet->mergeCells('F1:J1');
                            $sheet->getRowDimension(1)->setRowHeight(60);
                            $sheet->setCellValue('A1', 'LPR Scanned License Plate List');
                            $sheet->cell('A1:C1', function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center'); // Center vertically
                                $cell->setFontWeight('bold');
                                $cell->setBackground($color);
                                $cell->setFontColor('#ffffff');
                                $cell->setFontSize('20');
                            });

                            $cellValue = "Print Date - \n" .  date('m-d-Y h:i');
                            $sheet->setCellValue('A2', "$cellValue");
                            $sheet->getStyle('A2')->getAlignment()->setWrapText(true);
                            // Set the height of cell H2 (adjust as needed)
                            $sheet->getRowDimension(2)->setRowHeight(80);
                            $sheet->getRowDimension(3)->setRowHeight(50);

                            $sheet->cell('A2', function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center'); // Center vertically
                                $cell->setFontWeight('bold');
                                $cell->setBackground('#D6DCE4');
                                $cell->setFontColor('#000000');
                                $cell->setFontSize('16');
                            });

                            $location = "Location Name \r" . $location['full_name'];
                            $sheet->mergeCells('B2:B2');
                            $sheet->setCellValue('B2', $location);
                            $sheet->getStyle('B2')->getAlignment()->setWrapText(true);

                            $sheet->cell('B2:B2', function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center'); // Center vertically
                                $cell->setFontWeight('bold');
                                $cell->setBackground('#ffffff');
                                $cell->setFontColor('#000000');
                                $cell->setFontSize('18');
                            });

                            $sheet->mergeCells('C2:C2');
                            $locationId = "Location ID \n" . $garageCode;
                            $sheet->setCellValue('C2', "$locationId");
                            $sheet->getStyle('C2')->getAlignment()->setWrapText(true);

                            $sheet->cell('C2', function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center'); // Center vertically
                                $cell->setFontWeight('bold');
                                $cell->setBackground('#D6DCE4');
                                $cell->setFontColor('#040D12');
                                $cell->setFontSize('18');
                            });

                            $sheet->cell('A3', function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center'); // Center vertically
                                $cell->setFontWeight('bold');
                                $cell->setBackground($color);
                                $cell->setFontColor('#ffffff');
                                $cell->setFontSize('18');
                            });
                            $sheet->cell('B3', function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center'); // Center vertically
                                $cell->setFontWeight('bold');
                                $cell->setBackground($color);
                                $cell->setFontColor('#ffffff');
                                $cell->setFontSize('18');
                            });
                            $sheet->cell('C3', function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center'); // Center vertically
                                $cell->setFontWeight('bold');
                                $cell->setBackground($color);
                                $cell->setFontColor('#ffffff');
                                $cell->setFontSize('18');
                            });
                            $this->log->info('WLA 111 .');

                            $i = 1;
                            $sheet->cell('A3:I3', function ($cell) use ($color) {
                                $cell->setAlignment('left'); // Center horizontally
                                $cell->setValignment('left');
                            });
                            $index = 4;
                            foreach ($licensePlateData as $key => $licensePlateEntries) {
                                // Format the datetime in mm-dd-yy format

                                $permitVehicle = PermitVehicle::with("permitVehicleMapping.permitRequest")
                                    ->where("license_plate_number", $licensePlateEntries['license_plate'])
                                    ->where("partner_id", $licensePlateEntries['partner_id'])->first();
                                if (isset($permitVehicle->permitVehicleMapping->permitRequest->id)) {
                                    $permitRequest = $permitVehicle->permitVehicleMapping->permitRequest;
                                    $licensePlate['permit_number'] = $permitRequest->account_number;
                                    if (strtotime($permitRequest->desired_end_date) < strtotime(date("Y-m-d"))) {
                                        
                                    } else {
                                        continue;
                                    }
                                }
                                $scanTime = Carbon::now()->subMinutes($configTime['n_before_checkin']);
                                $ticket = Ticket::where("license_plate", $licensePlateEntries['license_plate'])
                                        ->where("partner_id", $licensePlateEntries['partner_id'])
                                        ->where("checkin_time", ">=",date("Y-m-d H:i:s", strtotime($scanTime)))
                                        //->where("checkin_time", "<=",date("Y-m-d H:i:s", strtotime($currentDateTime)))
                                        ->orderBy("id", "DESC")->first();
                                if($ticket){
                                    continue;
                                }

                                
                                $time = \Carbon\Carbon::parse($licensePlateEntries['entry_time']);
                                $entryTime = $time->format('m-d-Y H:i:s');

                                $data['Sr No.'] = $i;
                                $data['License Plate'] = $licensePlateEntries['license_plate'];
                                $data['Entry Time'] = $entryTime;
                                $i++;

                                $dataArr[] = $data;
                                $row = $key + $index;
                                $sheet->cell('A' . $row . ':I' . $row, function ($cell) use ($color) {
                                    $cell->setAlignment('left'); // Center horizontally
                                    $cell->setValignment('left');
                                });
                            }
                            $sheet->fromArray($dataArr, [], 'A3', true, true);

                            /*  
                            foreach ($licensePlateData as $key =>  $licensePlateEntries) {
                                $sheet->cell('A' . $key + $index, function ($cell) use ($color) {
                                    $cell->setAlignment('left'); // Center horizontally
                                    $cell->setValignment('left');
                                });
                            } */
                        });
                    }
                )->store('xls');
            }
            //$data['report_name']    = 'lpr_open_ticket_report';
            $data['themeColor']     = $color;
            $data['mail_body']      = "Enclosed is the list of all the vehicles that entered the garage but did not open a ticket even after 15 minutes of stay. Please take the appropriate action.";

            storage_path('exports/' . $excelSheetName . '.xls');

            Mail::send('woodman.license-plate-report', $data, function ($message)  use ($excelSheetName) {
                //$message->bcc(['<EMAIL>']);
                //$message->to('<EMAIL>');
                $message->to(config('parkengage.townsend.woodman_lpr_open_ticket_emails'));
                $message->subject('LPR Scanned list');
                $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                $this->log->info("Mail Sent success with failname :  {$path_to_file}");
                if (file_exists($path_to_file)) {
                    // $this->log->info("Path Of file and name 333 {$path_to_file}");
                    $message->attach($path_to_file);
                }
            });
            // foreach ($licensePlateData as $value) {
            //     $id[] = $value['id'];
            // }
            /*   WoodmanLicensePlate::where('partner_id',$partnerId)
                                ->whereIn('id',$id)
                                ->update(['notification_status' => 1]); */
        } catch (Exception $e) {
            $this->log->info("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
        }
    }
}
