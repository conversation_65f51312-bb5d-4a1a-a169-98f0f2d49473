<?php

namespace App\Console\Commands\Woodman;

use App\Exceptions\NotFoundException;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\ParkEngage\LicensePlate;
use App\Models\ParkEngage\WoodmanLicensePlate;
use App\Models\Ticket;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\PermitVehicle;

class UngatedLPRNotification extends Command
{
    protected $log;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:ungated-lpr-notification-email {partnerId} {facilityId} {licensePlate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a notification email with an Excel sheet for a specific license plate.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/Woodland')->createLogger('single_email_notify');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->log->info("Start processing single license plate notification");

            // Get arguments
            $partnerId = $this->argument('partnerId');
            $licensePlate = $this->argument('licensePlate');
            $facilityId = [$this->argument('facilityId')];

            $this->log->info("Processing - Partner ID: $partnerId, License Plate: $licensePlate,Facility ID: " . json_encode($facilityId));

            $facility = Facility::find($this->argument('facilityId'));

            $this->log->info("Processing - Partner ID: $partnerId, License Plate: $licensePlate, Facility ID: " . json_encode($facilityId));

            // Fetch license plate from the model if available
            $licensePlate = $this->getLicensePlateFromModel($facility, $licensePlate);

            $this->log->info("License Plate after model lookup: $licensePlate");

            // Check permit status
            $isPermitActive = $this->isPermitActive($partnerId, $licensePlate);
            $this->log->info("Permit Status: $isPermitActive");

            // Get facility info
            $location = Facility::where('owner_id', $partnerId)->whereIn('id', $facilityId)->firstOrFail();
            QueryBuilder::setCustomTimezone($location->id);
            $garageCode = $location->garage_code;

            // Get config time
            $configTime = FacilityConfiguration::whereIn('facility_id', $facilityId)
                ->where('partner_id', $partnerId)
                ->firstOrFail();

            // Fetch license plate entry
            $licensePlateData = $this->getLicensePlateData($partnerId, $licensePlate, $facilityId, $configTime['n_before_checkin']);

            $this->log->info("n_before_checkin: " . $configTime['n_before_checkin']);
            $this->log->info("License Plate Data: $licensePlateData");

            if (!$licensePlateData) {
                throw new NotFoundException("No license plate data found for: $licensePlate");
            }

            // Get brand settings
            $brandSetting = BrandSetting::where('user_id', $partnerId)->first();
            $color = $brandSetting->color ?? '#000000';

            // Generate and send report
            $excelFilePath = $this->generateExcelReport($licensePlate, $licensePlateData, $location, $garageCode, $color);

            if ($isPermitActive) {
                $this->log->info("Mail not sent as permit is active");
            } else {
                $this->sendEmailReport($licensePlate, $excelFilePath, $color);
            }
        } catch (Exception $e) {
            $this->log->error("Error: " . $e->getMessage());
        }
    }

    /**
     * Get license plate from facility-specific model
     */
    private function getLicensePlateFromModel($facility, $licensePlate)
    {
        if (!empty($facility->license_plate_model)) {
            $model = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
            return $model::where("license_plate", $licensePlate)->latest('id')->value('license_plate');
        } else {
            return LicensePlate::where("license_plate", $licensePlate)->latest('id')->value('license_plate');
        }
        return null;
    }

    /**
     * Check if the permit is active for a given license plate and partner
     */
    private function isPermitActive($partnerId, $licensePlate)
    {
        $permitVehicle = PermitVehicle::with('permitVehicleMapping.permitRequest')
            ->where('license_plate_number', $licensePlate)
            ->where('partner_id', $partnerId)
            ->first();

        if (
            $permitVehicle &&
            $permitVehicle->permitVehicleMapping &&
            $permitVehicle->permitVehicleMapping->permitRequest &&
            $permitVehicle->permitVehicleMapping->permitRequest->desired_end_date
        ) {

            return strtotime($permitVehicle->permitVehicleMapping->permitRequest->desired_end_date) >= strtotime(date('Y-m-d'));
        }

        return false;
    }


    /**
     * Get license plate data
     */
    private function getLicensePlateData($partnerId, $licensePlate, $facilityId, $nBeforeCheckin)
    {
        return WoodmanLicensePlate::where('partner_id', $partnerId)
            ->whereNull("deleted_at")
            ->whereNotNull("entry_time")
            ->where("license_plate", $licensePlate)
            ->whereIn("facility_id", $facilityId)
            ->where('gate_type', 'entry')
            // ->whereRaw("TIMESTAMPDIFF(MINUTE, entry_time, ?) >= ?", [Carbon::now(), $nBeforeCheckin])
            ->latest('id')
            ->first();
    }

    /**
     * Generate and store the Excel report
     */
    private function generateExcelReport($licensePlate, $licensePlateData, $location, $garageCode, $color)
    {
        $excelSheetName = 'LPR_Scanned_' . $licensePlate . '_' . Carbon::now()->format('m-d-Y_H_i_s');

        Excel::create($excelSheetName, function ($excel) use ($color, $licensePlateData, $location, $garageCode) {
            $excel->sheet('License', function ($sheet) use ($color, $licensePlateData, $location, $garageCode) {
                // Set column widths
                $sheet->setWidth(['A' => 35, 'B' => 35, 'C' => 35]);

                // Header row (Row 1)
                $sheet->mergeCells('A1:C1');
                $sheet->getRowDimension(1)->setRowHeight(60);
                $sheet->setCellValue('A1', 'LPR Scanned License Plate List');
                $sheet->cell('A1:C1', function ($cell) use ($color) {
                    $cell->setAlignment('center')
                        ->setValignment('center')
                        ->setFontWeight('bold')
                        ->setBackground($color)
                        ->setFontColor('#ffffff')
                        ->setFontSize(20);
                });

                // Row 2 formatting
                $sheet->getRowDimension(2)->setRowHeight(80);
                $sheet->getStyle('A2:C2')->getAlignment()->setWrapText(true);

                // Print Date (A2)
                $sheet->setCellValue('A2', "Print Date - \n" . Carbon::now()->format('m-d-Y h:i'));
                $sheet->cell('A2', function ($cell) {
                    $cell->setAlignment('center')
                        ->setValignment('center')
                        ->setFontWeight('bold')
                        ->setBackground('#D6DCE4')
                        ->setFontColor('#000000')
                        ->setFontSize(16);
                });

                // Location Name (B2)
                $sheet->setCellValue('B2', "Location Name \r" . $location->full_name);
                $sheet->cell('B2', function ($cell) {
                    $cell->setAlignment('center')
                        ->setValignment('center')
                        ->setFontWeight('bold')
                        ->setBackground('#ffffff')
                        ->setFontColor('#000000')
                        ->setFontSize(18);
                });

                // Location ID (C2)
                $sheet->setCellValue('C2', "Location ID \n" . $garageCode);
                $sheet->cell('C2', function ($cell) {
                    $cell->setAlignment('center')
                        ->setValignment('center')
                        ->setFontWeight('bold')
                        ->setBackground('#D6DCE4')
                        ->setFontColor('#040D12')
                        ->setFontSize(18);
                });

                // Header row (Row 3)
                $sheet->getRowDimension(3)->setRowHeight(50);
                $sheet->setCellValue('A3', 'Sr No.')
                    ->setCellValue('B3', 'License Plate')
                    ->setCellValue('C3', 'Entry Time');

                $sheet->cells('A3:C3', function ($cell) use ($color) {
                    $cell->setAlignment('Left')
                        ->setValignment('bottom')
                        ->setFontWeight('bold')
                        ->setBackground($color)
                        ->setFontColor('#ffffff')
                        ->setFontSize(18);
                });

                // Data row (Row 4) - Modified section
                $sheet->getRowDimension(4)->setRowHeight(30);

                $sheet->setCellValue('A4', 1)
                    ->setCellValue('B4', $licensePlateData->license_plate)
                    ->setCellValue('C4', Carbon::parse($licensePlateData->entry_time)->format('m-d-Y H:i:s'));

                $sheet->cells('A4:C4', function ($cell) {
                    $cell->setAlignment('left')
                        ->setValignment('center');
                });
            });
        })->store('xls');

        return storage_path('exports/' . $excelSheetName . '.xls');
    }

    /**
     * Send the Excel report via email
     */
    private function sendEmailReport($licensePlate, $filePath, $color)
    {
        $data['themeColor'] = $color;
        $data['mail_body'] = "Enclosed is the list of the vehicle that entered the garage but did not have permit. Please take the appropriate action.";


        Mail::send('woodman.license-plate-report', $data, function ($message) use ($filePath) {
            $message->to(config('parkengage.townsend.woodman_lpr_open_ticket_emails'))
                ->subject('LPR Scanned List')
                ->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));

            if (file_exists($filePath)) {
                $message->attach($filePath);
            }
        });

        $this->log->info("Mail sent successfully for license plate: $licensePlate");
    }
}
