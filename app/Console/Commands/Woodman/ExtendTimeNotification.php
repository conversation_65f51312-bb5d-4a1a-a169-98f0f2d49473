<?php

namespace App\Console\Commands\Woodman;

use App\Exceptions\NotFoundException;
use App\Http\Helpers\QueryBuilder;
use App\Jobs\SendSmsReminder;
use App\Models\Facility;
use App\Models\ParkEngage\NotificationLog;
use App\Models\ParkEngage\PlatformNotification;
use App\Models\Ticket;
use App\Models\User;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Illuminate\Foundation\Bus\DispatchesJobs;

class ExtendTimeNotification extends Command
{
    use DispatchesJobs;
    protected $log;
    protected $globalData = null;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:extent-time-reminder {partnerId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'notification to customer for extending parking session if his session has only 15 minutes left';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/Woodland')->createLogger('sms_notify');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->log->info("start cron");

            $partnerId = $this->argument('partnerId');
            $this->log->info("partner id: " . $partnerId);

            if ($partnerId == '215900') // Evolution Partner for preprod
            {
                $facilityId = [297]; //Evolution Partner gardge id 
                $name = 'Evolution Parking';
            } else if ($partnerId == '54682') // Demo Setup New Theme partner for preprod
            {
                $facilityId = [199]; //Demo Setup New Theme gardge id
                $name = 'Demo Setup';
            } else if ($partnerId == '169163') // Menlo College partner for preprod
            {
                $facilityId = [275, 290, 291, 292, 293, 294, 295, 296]; //Menlo College gardge id
                $name = 'Menlo College';
            } else if ($partnerId == '91860') // Preferred Parking partner for preprod
            {
                $facilityId = [299, 300, 301, 302, 303, 304, 232]; //Preferred Parking gardge id
                $name = 'Preferred Parking';
            } else if ($partnerId == '7395') // Preferred Parking partner for preprod
            {
                $facilityId = [133, 342, 357, 373, 407, 416, 417, 419, 420, 421, 422, 423, 425, 426, 427]; //United Parking gardge id
                $name = 'United Parking';
            } else {
                $facilityId = [153]; // already exist in this file
                $name = 'Woodman';
            }
            $this->log->info("Facility ids: " . json_encode($facilityId));
            // Fetch facilities based on partner ID
            if (in_array($partnerId, config('parkengage.PARTNER_GROUP_NOTIFICATION'))) {
                // if ($partnerId == 40867 || $partnerId == 19349) {
                $facilities = Facility::where('owner_id', $partnerId)->where('active', '1')->get();
                $this->log->info("Facility Count: " . count($facilities));
            } else {
                $facilities = Facility::where('owner_id', $partnerId)
                    ->where('active', '1')
                    ->whereIn('id', $facilityId)
                    ->get();
                $this->log->info(".$name. facility details: " . json_encode($facilities));
            }
            // Check if facilities are not empty
            if ($facilities->isEmpty()) {
                $this->log->info('No facilities found.');
                // throw new NotFoundException('No facilities found.');
            }
            $facilityIds = [];
            foreach ($facilities as $facility) {
                //set custom time zone
                QueryBuilder::setCustomTimezone($facility['id']);
                $facilityIds[] = $facility['id'];
            }

            $currentDateTime = Carbon::now();
            $currentDate = $currentDateTime->toDateString();
            $this->log->info("current date: " . $currentDate);

            $currentTime = $currentDateTime->format('Y-m-d H:i:s');
            $this->log->info("current time: " . $currentTime);


            $contents = PlatformNotification::select('pn.partner_id', 'pn.status', 'pn.scheduled_flag', 'pn.time_in_minutes', 'nt.type as notify_type', 'platform_notifications.*')
                ->leftjoin('partner_notifications as pn', 'pn.platform_notification_id', '=', 'platform_notifications.id')
                ->leftjoin('notification_type as nt', 'pn.notification_type_id', '=', 'nt.id')
                ->where('pn.partner_id', '=', $partnerId)
                ->where('platform_notifications.type', '=', 'lpr')
                ->first();
            if (count($contents) == 0) {
                $this->log->info('No contents found for sms.');
                // throw new NotFoundException('No contents found for sms.');
            }
            $this->log->info('Contents details: ' . json_encode($contents));

            //configured time
            $timeInMinutes = $contents['time_in_minutes'];
            $this->log->info("configured time in minutes: " . $timeInMinutes);

            // Fetch tickets
            $this->log->info("Facility Ids Test: " . json_encode($facilityIds));
            $tickets = Ticket::whereIn('facility_id', $facilityIds)
                ->where("is_closed", 0)
                ->where("partner_id", $partnerId)
                ->where('estimated_checkout', '>', $currentTime)
                ->whereNull("deleted_at")
                ->whereNotNull("user_id")
                ->whereNull("stop_parking_time")
                ->get();
            $this->log->info("Ticket Count 222222: " . count($tickets));

            if (count($tickets) == 0) {
                $data = [
                    'platform_notification_id' => $contents['id'],
                    'reservation_id' => 0,
                    'status' => 0,
                    'start_timestamp' => null,
                    'error' => "No tickets found.",
                    'slug_name' => $contents['slug_name'],
                    'user_prefrences' => 0,
                ];
                $this->log->info("global data before foreach: " . json_encode($data));
                $this->globalData = $data;

                $this->log->info('No tickets found.');
                //   throw new NotFoundException('No tickets found.');
            }
            // $this->log->info('Tickets detail available: '. json_encode($tickets));

            foreach ($tickets as $key => $ticket) {
                $this->log->info('foreach loop start with ' . ++$key . ' for ticket: ' . json_encode($ticket->ticket_number));

                if ($ticket['user_id'] != null) {
                    $user = User::find($ticket['user_id']);
                    if ($user) {
                        $this->log->info('User details: ' . json_encode($user->id));
                    }
                }

                $this->log->info("estimated check out time: " . $ticket['estimated_checkout']);
                $checkoutTime = Carbon::parse($ticket['estimated_checkout']);
                $this->log->info("current Date Time : " . $currentDateTime);
                $this->log->info("check out time: " . $checkoutTime);

                // Calculate the check out time difference in minutes
                $checkoutTimeDiff = $checkoutTime->diffInRealMinutes($currentTime);
                $this->log->info("check out time difference in minutes: " . $checkoutTimeDiff);

                $data = [
                    'platform_notification_id' => $contents['id'],
                    'reservation_id' => $ticket['id'],
                    'status' => 1,
                    'start_timestamp' => $ticket->checkin_time,
                    'error' => null,
                    'slug_name' => $contents['slug_name'],
                    'user_prefrences' => $user['user_prefrences'],
                ];
                $this->log->info("global data 1111: " . json_encode($data));

                $this->globalData = $data;

                if (empty($user['phone'])) {
                    $this->log->info("User phone number not found: ");
                    continue;
                }

                $userData = [
                    "phone" => $user['phone'],
                    "reservationId" => $ticket['id'],
                    "ticketNumber" => $ticket['ticket_number'],
                    "email" => $user['email']
                ];
                $this->log->info("user details data value: " . json_encode($userData));

                $logs = NotificationLog::where('reservation_id', $ticket['id'])
                    ->where('status', 1)
                    ->pluck('reservation_id')
                    ->toArray();

                $this->log->info("from logs reservation_id: " . json_encode($logs));
                $this->log->info("compare time difference: " . $checkoutTimeDiff . '==' . $timeInMinutes);
                if (!in_array((string)$ticket['id'], $logs) && ($checkoutTimeDiff == $timeInMinutes)) {
                    $this->log->info("condition pass and ticket number is: " . $ticket['ticket_number']);

                    $this->log->info("sms contents:" . json_encode($contents));
                    //PIMIS-14876
                    $facilityById = Facility::find($ticket['facility_id']);
                    $contents->body = "Thank you for visiting ".$facilityById->full_name.".".$contents->body;
                    //PIMIS-14876
                    $this->dispatch((new SendSmsReminder($contents, $userData, new LoggerFactory, $partnerId)));

                    $this->log->info("Extend the time notify ticket id: " . $ticket['id']);
                    $this->globalData = $data;
                    $this->addDataInLog($data);
                }
                $this->log->info("condition not pass for Extend time notify ticket id: " . $ticket['id']);
            }
        } catch (\Throwable $e) {
            $this->log->info("Catch error");
            $msg = $this->log->info("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
            $error = [
                'platform_notification_id' => $this->globalData['platform_notification_id'],
                'reservation_id' => $this->globalData['reservation_id'],
                'status' => 0,
                'start_timestamp' => $this->globalData['start_timestamp'],
                'error' => $msg,
                'slug_name' => $this->globalData['slug_name'],
                'user_prefrences' => $this->globalData['user_prefrences'],
            ];
            $this->addDataInLog($error);
            $this->log->info("Cron ended");
        }
    }

    /***
     * Create data in notification_logs table.
     */
    public function addDataInLog($data)
    {
        NotificationLog::create($data);
        return true;
    }
}
