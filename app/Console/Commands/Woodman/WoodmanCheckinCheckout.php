<?php

namespace App\Console\Commands\Woodman;

use Mail;
use Exception;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Models\Ticket;

class WoodmanCheckinCheckout extends Command
{

    protected $signature = 'woodman:checkin-checkout';

    protected $description = 'close all the ungated open ticket which not closed by enforcement or LPR';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/parkengage/woodman')->createLogger('woodman-checkin-checkout');
    }

    public function handle()
    {
        $facility_id = 153;
        $this->log->info("checkout start");
        try {
            $today = date("Y-m-d");
            $ticketData = Ticket::whereDate('scan_date', '<', $today)->where(['facility_id' => $facility_id, 'is_closed' => '0'])->whereNotNull("anet_transaction_id")->get();
            if (count($ticketData) > 0) {
                $this->log->info("Before checkout email about to send");
                //user mail

                foreach ($ticketData as $val) {

                    if ($val->checkout_datetime != '') {
                        $endDate = date("Y-m-d", strtotime($val->checkout_datetime));
                        if (strtotime($endDate) < strtotime($today)) {
                            $status = Ticket::where(['ticket_number' => $val->ticket_number, 'facility_id' => $facility_id])->update(['is_closed' => '1', "remark" => "closed by backend team", "closed_date" => date("Y-m-d H:i:s")]);
                            $this->log->info("Checkout update for Ticket No " . $val->ticket_number);
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            Mail::send(
                "mapco.alert-error-checkout",
                ['data' => $msg],
                function ($message) use ($msg) {
                    $message->to(['<EMAIL>'])->subject("Error : Woodman Cron Alert Checkout");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Queue ended");
        }
    }
}
