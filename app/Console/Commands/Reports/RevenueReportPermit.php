<?php

namespace App\Console\Commands\Reports;

use App\Services\LoggerFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use App\Models\PermitRequest;
use App\Models\PermitRequestRenewHistory;
use App\Models\PermitRevenueReport;
use App\Models\AuthorizeNetTransaction;
use Illuminate\Support\Facades\DB;
use App\Http\Helpers\QueryBuilder;

class RevenueReportPermit extends Command
{
    protected $partnerId;
    protected $facilityId;
    protected $fromTime;
    protected $toTime;
    protected $log;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "permit:revenue-report
                            { --partnerId= : partner ID }
                            { --facilityId= : facility ID }
                            { --startDate= : optional start date}
                            { --endDate= : optional end date}";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'this command is use to populate data in flat table to make diamond report for BI.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/crons/permits')->createLogger('permits');
    }

    // setting up start and end date parameters
    public function setUp()
    {
        $this->log->info('Executing cron for Permit push in flat table.');

        $startDateCurrentMonth = new Carbon();
        // $startDate = $startDateCurrentMonth->subDay();
        // $startDate = $startDateCurrentMonth->now();
        $startDate = $startDateCurrentMonth->now()->startOfMonth();

        // $endDateNextMonth = new Carbon('first day of next month');
        // $endDate = $endDateNextMonth->subDay();
        $endDate = $startDateCurrentMonth->subMonth();
        $lastDayOfMonth = Carbon::now()->lastOfMonth();

        $this->partnerId = $this->option('partnerId') ?: 0;
        $this->facilityId = $this->option('facilityId') ?: 0;
        // $this->partnerId = config('parkengage.townsend.partner_id');
        $this->fromTime = $this->option('startDate') ?: $startDate->format('Y-m-d ') . '00:00:00';
        $this->toTime = $this->option('endDate') ?: $lastDayOfMonth->format('Y-m-d ') . '23:59:59';
        if ($this->partnerId <= 0) {
            throw new Exception("Please Enter Partner ID");
        }
        if ($this->facilityId <= 0) {
            throw new Exception("Please Enter Facility");
        }
        // $this->brandSettings
        $this->log->info("Permit Data move Cron Run for Partner ID : {$this->partnerId} AND Facility ID : {$this->facilityId}");
        $this->log->info('Executing cron with start date : ' . $this->fromTime . ' End Date : ' . $this->toTime);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //
        try {
            $this->setUp();
            $currentDate = Carbon::now();
            $previousMonth = $currentDate->subMonth();  // Get previous month dynamically
            $firstDay = Carbon::now()->format('d');     // Output: 01

            // Get Month wise permit account Number and insert into new report table

            ### Section of Daily new purchase Permits Start

            // check last entry from Permit Flat table.
            $getLasteRecord = PermitRevenueReport::where(['facility_id' => $this->facilityId, 'action_flag' => 'new'])->orderBy('id', 'DESC')->first();

            // Get New Purchages
            $permits = PermitRequest::where(['facility_id' => $this->facilityId])->where('id', '>', $getLasteRecord->permit_request_id)->get();
            $this->log->info("New Permit count to insert : " . $permits->count());
            // dd('Check Total Count before insert : ', $permits->count());

            $permitcount = 0;
            if ($permits->count() > 0) {
                $this->log->info("Start New populating : " . $permits->count());
                foreach ($permits as $key => $permit) {
                    $permitcount++;
                    $this->log->info("Start insert for permit account number :  {$permit->account_number} ");
                    // To save New permit records
                    $this->saveNewPermitPurchase($permit);
                }
                $this->log->info("Saved New : {$permitcount}");
            }
            ###  !!! Close here 

            // DB::enableQueryLog();

            ## This Section start for Update. 
            // Get all the New and Renuew of the month to check for any change 
            $PermitRevenueReport = PermitRevenueReport::where(['facility_id' => $this->facilityId])
                ->where('desired_start_date', '>=', $this->fromTime)
                ->where('desired_start_date', '<=', $this->toTime)
                ->orderBy('id', 'ASC')->get();

            // dd('count', $PermitRevenueReport->count());

            if ($PermitRevenueReport->count() > 0) {
                $this->log->info("To start update for count : " . $PermitRevenueReport->count());
                // dd('checkout count', $PermitRevenueReport->count());
                foreach ($PermitRevenueReport as $key => $permit) {
                    // $this->log->info("check for permit {$permit->id} and acount Number : {{$permit->account_number}} ");
                    $updateFlag = false;
                    $this->log->info("permirt record already exists key { $key } for Request id {$permit->id} ");
                    $permitRevenueReport = PermitRevenueReport::find($permit->id);
                    // $this->log->info("permirt record already exists key {$permitRevenueReport->id} ");
                    $updatePermit = PermitRequest::find($permit->permit_request_id);

                    if ($updatePermit->user_consent == '0' && $permitRevenueReport->user_consent == '1') {
                        $this->log->info("Permit Update for Consent ");
                        $permitRevenueReport->user_consent          = $updatePermit->user_consent;
                        $updateFlag = true;
                    }

                    if (!empty($updatePermit->cancelled_at) && empty($permitRevenueReport->cancelled_at)) {
                        $this->log->info("Permit Update for Cancelled ");
                        $permitRevenueReport->cancelled_at          = $updatePermit->cancelled_at;
                        $updateFlag = true;
                    }
                    if (!empty($updatePermit->refund_amount) && empty($permitRevenueReport->refund_amount)) {
                        $this->log->info("Permit Update for Refund ");
                        $permitRevenueReport->refund_type           = $updatePermit->refund_type;
                        $permitRevenueReport->refund_amount         = $updatePermit->refund_amount;
                        $permitRevenueReport->refund_date           = $updatePermit->refund_date;
                        $permitRevenueReport->refund_by             = $updatePermit->refund_by;
                        $updateFlag = true;
                    }
                    if ($updateFlag) {
                        $this->log->info("+++++Request Created for update case : " . $permitRevenueReport->account_number);
                        $permitRevenueReport->save();
                    } else {
                        $this->log->info("#####Request Created for update else case no update : ");
                    }
                }
            }
            ## This Section start for Update Close Here.... 
            return true;


            // this section to get renew permits again run on (4,6 of each month) 
            /*   $permits = PermitRequest::where(['facility_id' => $this->facilityId])
                ->where('desired_start_date', '>=', $this->fromTime)
                ->where('desired_start_date', '<=', $this->toTime)
                ->orderBy('id', 'ASC')->get();

            if ($permits->count() > $PermitRevenueReport->count()) {
                // Save New renew permits  
                $newRecords = QueryBuilder::getDiffByColumn($permits, $PermitRevenueReport, 'account_number');
                dd('if', $permits->count(), $PermitRevenueReport->count(), $newRecords);
            }
            dd('ELSE', $permits->count(), $PermitRevenueReport->count());
            // dd(DB::getQueryLog()); */




            // For Current Month New
            // $permits = PermitRequest::where('partner_id', $this->partnerId)
            //     ->where('approved_on', '>=', $this->fromTime)
            //     ->where('approved_on', '<=', $this->toTime)
            //     // ->where('desired_start_date', '>=', $this->fromTime)
            //     // ->where('desired_start_date', '<=', $this->toTime)
            //     // ->where('id', '>', 8034)
            //     ->get();



            //  to Specific Permits
            // $permits = PermitRequest::whereIn('id', [277, 304, 9193, 9215, 9216, 9418])->get();
            // $permits = PermitRequest::whereIn('id', [234, 240, 297, 323, 3430, 3558, 3560, 5137, 9243])->get();
            // $permits = PermitRequest::whereIn('id', [1653, 4313, 5108, 8189, 8441, 8761, 9079, 9100, 9130, 9149, 9170, 9455, 9492, 9543])->get();
            // : 07-01-2025
            // $permits = PermitRequest::whereIn('id', [9707])->get();
            // : 19-01-2025
            // $permits = PermitRequest::whereIn('id', [596, 597, 601, 3437, 7901, 8427, 8428, 8429, 8430, 8431, 8432, 8433, 8434, 8436, 8855, 8901, 9289, 9314, 9564, 9649])->get();
            // $permits = PermitRequest::whereIn('id', [2105, 6488, 7588, 9094, 9151, 9170, 9426, 9465, 9492, 9646, 9664, 9679, 9727, 9814, 9888, 9937, 9946])->get();
            // dd('New entry for daily : ', $permits->count());

            $permits = PermitRequest::whereIn('id', [0])->get();
            // For Current Month Renew
            if ($firstDay == '01') {
                // dd($previousMonth, $previousMonth->year, $previousMonth->month, $currentDate->startOfMonth(), $currentDate->endOfMonth());
                $permits = PermitRequest::where(function ($query) use ($previousMonth) {
                    $query->whereYear('approved_on', '<', $previousMonth->year)
                        ->orWhere(function ($subQuery) use ($previousMonth) {
                            $subQuery->whereYear('approved_on', '=', $previousMonth->year)
                                ->whereMonth('approved_on', '=', $previousMonth->month);
                        });
                })
                    ->whereDate('desired_start_date', '>=', Carbon::now()->startOfMonth()->toDateString())
                    ->whereDate('desired_end_date', '<=', Carbon::now()->endOfMonth()->toDateString())
                    ->whereIn('facility_id', [$this->facilityId])
                    ->whereNull('deleted_at')
                    ->get();

                // dd('First Day Count : ', $permits->count());
            } else if ($firstDay == '04' || $firstDay == '06') {
            } else {
                // Custom Run for Specific Permits
                // $permits = PermitRequest::whereIn('id', [2105, 6488, 7588, 9094, 9151, 9170, 9426, 9465, 9492, 9646, 9664, 9679, 9727, 9814, 9888, 9937, 9946])->get();
            }
            dd('Final Count : ', $permits->count());

            $permitcount = 0;
            if ($permits->count() > 0) {

                $this->log->info("Start New populating : " . $permits->count());
                print_r("\n\r Total Count for Run " . $permits->count());
                foreach ($permits as $key => $permit) {
                    $permitcount++;
                    $this->log->info("Start New populating : " . $key);
                    print_r("\n\r Start insert for permit account number :  " . $permit->account_number);
                    // dd(AuthorizeNetTransaction::find($permit->anet_transaction_id)->total);
                    // To save New permit records
                    // $this->saveNewPermitPurchase($permit);

                    // For Renew Permit data 
                    // $this->savePermitRenewHistory($permit);
                    // dd('ok success ' . $permit->id);
                }
                print_r("\n\r Final Permit Count Executed : " . $permitcount);
                print_r("\n\r");
            }




            // Get Renew History
            // $RenewHistory = PermitRequestRenewHistory::where('partner_id', $this->partnerId)
            //     ->where('desired_start_date', '>=', Carbon::parse($this->fromTime)->subMonth()->format('Y-m-d H:i:s'))
            //     ->where('desired_start_date', '<=', Carbon::parse($this->fromTime)->subMonth()->endOfMonth()->format('Y-m-d H:i:s'))
            //     ->get();

            //    $this->log->info("Close new data populating ");
            // $this->log->info("Start Renew population ");
            /*   $RenewHistory = PermitRequestRenewHistory::where('partner_id', $this->partnerId)
                ->where('desired_start_date', '>=', $this->fromTime)
                ->where('desired_start_date', '<=', $this->toTime)
                // ->whereMonth('approved_on', '<=', Carbon::parse($this->fromTime)->subMonth()->format('m'))
                // ->whereMonth('approved_on', '>=', Carbon::parse($this->fromTime)->format('m'))
                ->whereMonth('approved_on', '=', '9')
                ->where('permit_request_id', '>', '8034')
                ->get();

            // dd('Renew : ', $RenewHistory->count(), Carbon::parse($this->fromTime)->format('m'));

            if ($RenewHistory->count() > 0) {
                $this->log->info("Start RenNewed population : " . $RenewHistory->count());
                foreach ($RenewHistory as $key => $permit) {
                    // dd(AuthorizeNetTransaction::find($permit->anet_transaction_id)->total);
                    $this->savePermitRenewHistory($permit);
                    // dd('ok success ' . $permit->id);
                }
            }
            $this->log->info("Close Renew population "); */

            // $endDateNextMonth = new Carbon('last day of current month');
            // dd(Carbon::parse($this->fromTime)->subMonth()->format('Y-m-d H:i:s'), Carbon::parse($this->toTime)->subMonth()->format('Y-m-d H:i:s'));
            // dd($this->partnerId, $this->facilityId, $this->fromTime, $this->toTime);
        } catch (\Throwable $th) {
            //throw $th;
            //throw $th;
            $this->log->error('WLA Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine());
            print_r('WLA Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine());
            // $errorMessage = 'WLA Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine();
        }
    }


    protected function saveNewPermitPurchase($permit)
    {
        // get purchase data from history Table 
        $this->log->info("Start saveNewPermitPurchase for permirt Request id {$permit->id} and account number : {$permit->account_number} ");
        /* if ($this->isPermitExists($permit)) {
            $updateFlag = false;
            $this->log->info("permirt record already exists for Request id {$permit->id} ");
            $permitRevenueReport = PermitRevenueReport::find($permit->id);

            if ($permit->user_consent == '0') {
                $this->log->info("Permit Update for Consent ");
                $permitRevenueReport->user_consent          = $permit->user_consent;
                $updateFlag = true;
            }

            if (!empty($permit->cancelled_at)) {
                $this->log->info("Permit Update for Cancelled ");
                $permitRevenueReport->cancelled_at          = $permit->cancelled_at;
                $updateFlag = true;
            }
            if (!empty($permit->refund_amount)) {
                $this->log->info("Permit Update for Refund ");
                $permitRevenueReport->refund_type           = $permit->refund_type;
                $permitRevenueReport->refund_amount         = $permit->refund_amount;
                $permitRevenueReport->refund_date           = $permit->refund_date;
                $permitRevenueReport->refund_by             = $permit->refund_by;
                $updateFlag = true;
            }

            if ($updateFlag) {
                $this->log->info("Request Created for update case : " . $permitRevenueReport->account_number);
                $permitRevenueReport->save();
            } else {
                $this->log->info("Request Created for update else case no update : ");
            }

            return true;
        } */

        // Save New Records

        $purchaseDataFromHistory = PermitRequestRenewHistory::where('permit_request_id', $permit->id)->orderBy('id', 'ASC')->first();
        $permitAntAmount = isset($purchaseDataFromHistory->id) ? AuthorizeNetTransaction::find($purchaseDataFromHistory->anet_transaction_id)->total : AuthorizeNetTransaction::find($permit->anet_transaction_id)->total;

        $permitRevenueReport = new PermitRevenueReport();
        $permitRevenueReport->permit_request_id     = $permit->id;
        $permitRevenueReport->account_number        = $permit->account_number;
        $permitRevenueReport->permit_rate           = isset($purchaseDataFromHistory->id) ? $purchaseDataFromHistory->permit_rate           : $permit->permit_rate;
        $permitRevenueReport->permit_final_price    = isset($purchaseDataFromHistory->id) ? $purchaseDataFromHistory->permit_final_amount   : $permit->permit_final_amount;
        $permitRevenueReport->permit_create_date    = $permit->created_at;
        $permitRevenueReport->desired_start_date    = date('Y-m-d', strtotime($permit->created_at));
        $permitRevenueReport->desired_end_date      = isset($purchaseDataFromHistory->id) ? date('Y-m-d', strtotime($purchaseDataFromHistory->desired_end_date))      : date('Y-m-d', strtotime($permit->desired_end_date));
        $permitRevenueReport->approved_on           = isset($purchaseDataFromHistory->id) ? $purchaseDataFromHistory->approved_on           : $permit->approved_on;
        $permitRevenueReport->permit_type           = isset($purchaseDataFromHistory->id) ? $purchaseDataFromHistory->permit_type_name      : $permit->permit_type_name;
        $permitRevenueReport->anet_transaction_id   = isset($purchaseDataFromHistory->id) ? $purchaseDataFromHistory->anet_transaction_id   : $permit->anet_transaction_id;
        $permitRevenueReport->permit_amount         = $permitAntAmount;
        $permitRevenueReport->action_date           = $permit->created_at;
        $permitRevenueReport->cancelled_at          = $permit->cancelled_at;
        $permitRevenueReport->refund_type           = $permit->refund_type;
        $permitRevenueReport->refund_amount         = $permit->refund_amount;
        $permitRevenueReport->processing_fee        = $permit->processing_fee;
        $permitRevenueReport->tax_fee               = '0.00';
        $permitRevenueReport->refund_date           = $permit->refund_date;
        $permitRevenueReport->refund_by             = $permit->refund_by;
        $permitRevenueReport->user_consent          = $permit->user_consent;
        $permitRevenueReport->permit_rate_id        = $permit->permit_rate_id;
        $permitRevenueReport->partner_id            = $permit->partner_id;
        $permitRevenueReport->facility_id           = $permit->facility_id;
        $permitRevenueReport->user_id               = $permit->user_id;
        $permitRevenueReport->name                  = $permit->name;
        $permitRevenueReport->email                 = $permit->email;
        $permitRevenueReport->phone                 = $permit->phone;
        $permitRevenueReport->discount_amount       = $permit->discount_amount;
        $permitRevenueReport->promocode             = $permit->promocode;
        $permitRevenueReport->action_flag           = 'new';
        $permitRevenueReport->admin_user_id             = $permit->admin_user_id;
        $permitRevenueReport->service_prorate_amount    = $permit->service_prorate;
        $permitRevenueReport->permit_prorate_amount     = $permit->permit_prorate;
        $permitRevenueReport->save();

        $this->log->info("Save successfully for permirt Request id {$permit->id} and Account number : {$permit->account_number} ");
    }

    protected function savePermitRenewHistory($permit)
    {
        // get purchase data from history Table 
        // dd('hist ', $permit->id, $permit->account_number);
        $this->log->info("Start savePermitRenewHistory for permirt Request id {$permit->id} and account number : {$permit->account_number} ");
        // if ($this->isPermitExists($permit, 'renew')) {
        //     // dd('duplicate',$permit->id);
        //     $this->log->info("permirt renew record already exists for Request id {$permit->id} ");
        //     return true;
        // }
        // dd('111');
        $purchaseDataFromHistory =  [];
        // $purchaseDataFromHistory = PermitRequestRenewHistory::where('permit_request_id', $permit->id)->orderBy('id', 'DESC')->first();
        // $purchaseDataFromHistory = PermitRequestRenewHistory::where('permit_request_id', $permit->id)->orderBy('id', 'ASC')->first();
        // $permitAntAmount = isset($purchaseDataFromHistory->id) ? AuthorizeNetTransaction::find($purchaseDataFromHistory->anet_transaction_id)->total : AuthorizeNetTransaction::find($permit->anet_transaction_id)->total;
        // dd($permit->id, $permit->account_number,$purchaseDataFromHistory->anet_transaction_id);

        $permitRevenueReport = new PermitRevenueReport();
        // $permitRevenueReport->permit_request_id             = $purchaseDataFromHistory->permit_request_id;
        // $permitRevenueReport->permit_renew_history_id       = $permit->id;

        // only for current month 
        $permitRevenueReport->permit_request_id           = $permit->id;
        $permitRevenueReport->permit_renew_history_id     = 0;

        $permitRevenueReport->account_number        = $permit->account_number;
        $permitRevenueReport->permit_rate           = $permit->permit_rate;
        $permitRevenueReport->permit_final_price    = $permit->permit_final_amount;
        $permitRevenueReport->permit_create_date    = $permit->created_at;
        $permitRevenueReport->desired_start_date    = $permit->desired_start_date;
        $permitRevenueReport->desired_end_date      = $permit->desired_end_date;
        $permitRevenueReport->approved_on           = $permit->approved_on;
        $permitRevenueReport->permit_type           = $permit->permit_type_name;
        $permitRevenueReport->anet_transaction_id   = $permit->anet_transaction_id;
        $permitRevenueReport->permit_amount         = $permit->permit_final_amount;
        $permitRevenueReport->action_date           = $permit->desired_start_date;
        $permitRevenueReport->cancelled_at          = $permit->cancelled_at;
        $permitRevenueReport->refund_type           = $permit->refund_type;
        $permitRevenueReport->refund_amount         = $permit->refund_amount;
        $permitRevenueReport->processing_fee        = $permit->processing_fee;
        $permitRevenueReport->tax_fee               = '0.00';
        $permitRevenueReport->refund_date           = $permit->refund_date;
        $permitRevenueReport->refund_by             = $permit->refund_by;
        $permitRevenueReport->user_consent          = $permit->user_consent;
        $permitRevenueReport->permit_rate_id        = $permit->permit_rate_id;
        $permitRevenueReport->partner_id            = $permit->partner_id;
        $permitRevenueReport->facility_id           = $permit->facility_id;
        $permitRevenueReport->user_id               = $permit->user_id;
        $permitRevenueReport->name                  = $permit->name;
        $permitRevenueReport->email                 = $permit->email;
        $permitRevenueReport->phone                 = $permit->phone;
        $permitRevenueReport->discount_amount       = $permit->discount_amount;
        $permitRevenueReport->promocode             = $permit->promocode;
        $permitRevenueReport->action_flag           = 'renewed';
        $permitRevenueReport->admin_user_id             = $permit->admin_user_id;
        $permitRevenueReport->service_prorate_amount    = $permit->service_prorate;
        $permitRevenueReport->permit_prorate_amount     = $permit->permit_prorate;
        $permitRevenueReport->renew_type                = $permit->renew_type;          // to get permit renew from pay now if is 1 else cron for 0
        $permitRevenueReport->save();

        $this->log->info("Save successfully for permirt Request id {$permit->id} and Account number : {$permit->account_number} ");
        print_r("\n\rSave successfully for permirt Request id {$permit->id} and Account number : {$permit->account_number} ");
    }

    private function isPermitExists($permit, $flag = null)
    {
        if ($flag == 'renew') {
            $permit = PermitRevenueReport::where(['permit_request_id' => $permit->permit_request_id, 'permit_renew_history_id' => $permit->id, 'action_flag' => 'renewed'])->first();
        } else {
            $permit = PermitRevenueReport::where(['permit_request_id' => $permit->id, 'permit_renew_history_id' => '0', 'action_flag' => 'new'])->first();
        }
        if ($permit)
            return true;
        else
            return false;
    }
}
