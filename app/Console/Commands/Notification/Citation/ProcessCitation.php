<?php 

namespace App\Console\Commands\Notification\Citation;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use App\Jobs\ProcessCitationFile;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Services\LoggerFactory;

class ProcessCitation extends Command
{
    use DispatchesJobs;

    // Artisan command name with dynamic folder argument
    protected $signature = 'notification-citation-users {folder}';

    protected $description = 'Process citation notification files and send emails';
    
    protected $log;

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $folder = $this->argument('folder');

        // Initialize the logger with the dynamic folder name
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/citation/' . $folder)->createLogger('citation-notification');

        // Attempt to get the folder path from the config
        $configPath = config('parkengage.USM_PATH_FOR_EXCELPATH');
        $folderPath = $configPath ? public_path($configPath) : public_path('usm');

        // Check if the folder exists
        if (!File::exists($folderPath)) {
            $this->log->error('Folder not found: ' . $folderPath);
            $this->error('Folder not found: ' . $folderPath);
            return;
        }


        $files = File::allFiles($folderPath);

        foreach ($files as $file) {
            $this->log->info('Found file: ' . $file->getPathname());
    
            $filename = trim($file->getFilename());
            $this->log->info('Extracted filename: ' . $filename);
            $this->log->info('today date: ' .  date('m-d-Y') );

            // Regex for valid filename pattern with today's date, current month, and year
            if (preg_match('/^Notifications_' . date('m-d-Y') . '\.xlsx$/i', $filename)) {
            // Regex for valid filename pattern
                $this->log->info('Processing file: ' . $filename);
                
                // Dispatch job to queue
                dispatch(new ProcessCitationFile($filename));

            } else {
                $this->log->info('Invalid file detected: ' . $filename);
            }
        }
    
        $this->log->info('All eligible files have been dispatched to the queue.');
    }
}
