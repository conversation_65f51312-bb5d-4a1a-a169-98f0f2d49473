<?php

namespace App\Console\Commands\Notification;

use App\Exceptions\NotFoundException;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\User;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class NotifyEmail extends Command
{
    protected $log;
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify:email
                            {facilityId}
                            {vehicleNo}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email Notifications';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/notify/email')->createLogger('notify_email');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->log->info('Under handle function');

            $vehicleNumber = $this->argument('vehicleNo');
            $this->log->info($vehicleNumber);

            $facility = Facility::find($this->argument('facilityId'));
            
            if (!$facility) {
                throw new NotFoundException('No facility with that ID.');
            }
            if(isset($facility->owner_id) && !empty($facility->owner_id)){
                $partnerEmail = User::select('email')->where('id',$facility->owner_id)->first();
                $this->log->info('Partner Email '. $partnerEmail );
            }
            //set custom time zone
            QueryBuilder::setCustomTimezone($facility->id);

            $this->log->info('facility details '. json_encode($facility));
            
            $currentDateTimes = Carbon::now();
            $formatDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $currentDateTimes);

            // Format the date to mm-dd-yy
            $currentDateTime = $formatDateTime->format('m-d-Y H:i:s');

            $this->log->info('Current Date Time '. $currentDateTime );

            $brandSetting = FacilityBrandSetting::where('facility_id', $facility->id)->first();
    
            if ($brandSetting) {
                $facility->facility_logo_id = $brandSetting->id;
            } else {
                $settings = BrandSetting::where('user_id', $facility->owner_id)->first();
                $facility->logo_id = $settings->id;
            }
            $this->log->info('Logo id '. $facility->logo_id);
            $this->log->info("======== Partner email ".json_encode(config('parkengage.bolo.email_notify')));
            
            Mail::send(
                "notify.email-notify", ['facility'=> $facility, 'vehicle' => $vehicleNumber, 'currentDateTime'=>$currentDateTime], function ($message) use($vehicleNumber) {
                    // $message->to(config('parkengage.bolo.email_notify'));
                    $message->to(config('parkengage.bolo.email_notify'));
                    $message->subject("Session created for a Vehicle (License Plate # -$vehicleNumber) listed in BOLO List");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Mail sent to ".json_encode(config('parkengage.bolo.email'))); 

        } catch (\Throwable $e) {
            $errorMessage = array();
            $errorMessage['message'] = $e->getMessage();
            $errorMessage['line number'] = $e->getLine();
            $errorMessage['file'] = $e->getFile();
            $this->log->info('Issue in notify email sending' . json_encode($errorMessage));
            $this->log->info("Queue ended"); 
        }
        
    }
}
