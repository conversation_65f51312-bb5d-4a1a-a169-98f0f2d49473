<?php

namespace App\Console\Commands\Alerts;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\AdminAlerts;
use App\Models\Ticket;
use App\Models\Facility;
use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;
use Exception;

class TransientPaymentAlert extends Command
{
    protected $signature = 'alert:transient-payment-fail';
    // Command php artisan alert:transient-payment-fail

    protected $description = 'Notify if ticket not charged after checkout date time passed also checkout 3 retry.';

    protected $log;
    protected $totalTickets;
    protected $partnerName;


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/AlertsLogs')->createLogger('ticket-alert');
        $this->totalTickets = 0;
        $this->partnerName = '';
    }

    public function handle()
    {
        try {
            $this->log->info("Cron started");
            $partners = AdminAlerts::where(['active' => '1', 'alert_type' => 'ticket-not-charged'])->get();

            if ($partners->count() <= 0) {
                $this->log->info("No partner IDs found.");
                return;
            }

            $ticketsArray = [];
            foreach ($partners as $partner) {
                $this->partnerName = User::where('id', $partner->partner_id)->value('name');

                $facilities = $partner->facility_ids;

                foreach ($facilities as $facilityID) {
                    $facility = Facility::find($facilityID);
                    $facilityName = $facility->full_name ?? "Unknown Facility";

                    $timezone = QueryBuilder::setCustomTimezone($facility->id);
                    $thresholdTime = Carbon::parse('now')->format('Y-m-d H:i:s');

                    $this->log->info("Processing For Facility: $facilityName : TimeZone : {$timezone} For Run time : $thresholdTime");

                    $failedSubquery = "SELECT t1.reference_key, t1.response_code,  t1.response_message, t1.created_at as last_failed_time
                        FROM all_failed_transactions t1
                        INNER JOIN (
                            SELECT reference_key, MAX(id) AS max_id
                            FROM all_failed_transactions
                            GROUP BY reference_key
                        ) t2 ON t1.reference_key = t2.reference_key AND t1.id = t2.max_id";

                    $tickets = Ticket::select(
                        'tickets.ticket_number',
                        'tickets.created_at',
                        'tickets.updated_at',
                        'tickets.checkin_time',
                        'tickets.estimated_checkout',
                        'tickets.is_checkout',
                        'tickets.promocode',
                        'tickets.paid_by',
                        'dc.transaction_retry',
                        'f.full_name as facility_name',
                        'f.timezone as timezone',
                        DB::raw("CONVERT_TZ(NOW(), 'UTC', f.timezone) as `CurrentTime` "),
                        'aft.response_code as response_code',
                        'aft.response_message as last_response_message',
                        'aft.last_failed_time'
                    )
                        ->leftJoin('facilities as f', 'tickets.facility_id', '=', 'f.id')
                        ->leftJoin('datacap_transactions as dc', 'tickets.id', '=', 'dc.ticket_id')
                        ->leftJoin(DB::raw("($failedSubquery) AS aft"), 'aft.reference_key', '=', 'tickets.ticket_number')
                        ->whereNull('tickets.anet_transaction_id')
                        ->where('tickets.facility_id', '=', $facility->id)
                        ->where('dc.is_payment_complete', '=', '0')
                        ->where('tickets.is_checkout', '=', '0')
                        ->where('dc.transaction_retry', '=', '3')
                        ->where('tickets.estimated_checkout', '<=', $thresholdTime)
                        ->orderBy('tickets.estimated_checkout', 'desc')
                        ->get();

                    if ($tickets->isEmpty()) {
                        $this->log->info("No tickets found for Facility: $facilityName ");
                        continue;
                    }
                    $this->log->info('Tickets for facility : ' . $facilityID);
                    // $this->log->info(json_encode($tickets));
                    $this->totalTickets += count($tickets);

                    $ticketsArray[] = $tickets;
                }
                $this->log->info('Final Tickets Count ' . count($ticketsArray));

                if (count($ticketsArray) > 0) {
                    $this->notifyDevTeam($ticketsArray);
                    $this->log->info("Mail Send Successfuly ");
                } else {
                    $this->log->info("No Ticket found ");
                    $errorMessage = 'No Ticket Found : ';
                    $data['partner_key'] = $this->partnerName;
                    $data['exception'] = "No Tickets found ";
                    Mail::send('email-exception', $data, function ($message) {
                        $message->to(config('email_exceptions'));
                        // $message->to('<EMAIL>');
                        $message->subject("No Ticket Found :- Transient Payment Alert");
                        $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                    });
                }
            }

            $this->log->info("Cron ended successfully.");
        } catch (Exception $e) {
            $errorMessage = sprintf(
                "Error: %s in file %s on line %d",
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
            );

            $this->log->error('Open tickt cron Exception : ' . $errorMessage);
            $errorMessage = 'Open tickt cron Exception : ' . $errorMessage;
            $data['partner_key'] = $this->partnerName;
            $data['exception'] = "Exception in CityParking Daily Revenue Report " . $errorMessage;
            Mail::send('email-exception', $data, function ($message) {
                $message->to(config('email_exceptions'));
                // $message->to('<EMAIL>');
                $message->subject("Email Exception :- Transient Payment Alert");
                $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
            });
        }
    }

    protected function notifyDevTeam($ticketData)
    {
        $data = [
            'count' => $this->totalTickets,
            'partner_name' => $this->partnerName,
        ];

        $recipients = config('parkengage.notify.recipient_notify');

        try {
            // sleep(30);
            Mail::send(
                'admin_alerts.transient-payment-alert',
                ['data' => $data, 'ticketGroups' => $ticketData],
                function ($message) use ($recipients) {
                    $message->to($recipients)
                        ->subject("Alert : Transinet Payment Fail");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );

            $this->log->info("Notifications sent to recipients: " . implode(", ", $recipients));
        } catch (Exception $e) {
            $this->log->error("Failed to send notification emails: {$e->getMessage()}");
        }
    }
}
