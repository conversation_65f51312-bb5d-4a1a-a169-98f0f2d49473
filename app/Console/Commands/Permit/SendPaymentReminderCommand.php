<?php

namespace App\Console\Commands\Permit;

use Illuminate\Console\Command;
use App\Jobs\SendPaymentReminder;

class SendPaymentReminderCommand extends Command
{
    protected $signature = 'reminder:send';
    protected $description = 'Send payment reminder emails';

    public function handle()
    {
        dispatch(new SendPaymentReminder('19349', '167'));
        $this->info("Payment reminder job dispatched.");
    }
}
