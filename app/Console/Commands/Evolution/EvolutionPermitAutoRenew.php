<?php

namespace App\Console\Commands\Evolution;

use Illuminate\Support\Facades\Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Classes\DatacapPaymentGateway;
use App\Models\PermitRequestRenewHistory;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;
use App\Exceptions\ApiGenericException;
use Carbon\Carbon;
use App\Models\ParkEngage\permitTenureMapping;
use App\Models\FacilityFee;
use DateTime;
use App\Http\Helpers\MailHelper;


/**
 * Emails reservation stub to user
 */
class EvolutionPermitAutoRenew extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'evolution:permit-renew';
    protected $log;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly permit renew.';

    const PARTNER_ID = '215900';
    const FACILITY_ID = '317';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/Evolution/PermitRenew')->createLogger('autorenew');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {

            $permit_end_date = date('Y-m-d', strtotime('last day of previous month'));
            $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));

            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F", $time);
            $PermitRenewYear = date("Y", $time);
            $PermitRenewDay = date("d", $time);

            $monthlyRequest = PermitRequest::with(['facility.FacilityPaymentDetails', 'user', 'PermitVehicle'])->where('partner_id', self::PARTNER_ID)->whereDate('desired_end_date', '<=', $permit_end_date)->whereDate('desired_start_date', '>=', $permit_start_date)->whereNull('cancelled_at')->whereNull('deleted_at')->where('user_consent', 1)->whereNotNull('anet_transaction_id')->whereNull('business_id')->orderBy('id', 'desc')->limit(1)->get();
            //dd(count($monthlyRequest), $permit_start_date,$permit_end_date,self::PARTNER_ID,$monthlyRequest[0]->id,$monthlyRequest[0]->user_id,$monthlyRequest[0]->permit_rate,$monthlyRequest[0]->account_number); 
            if (!$monthlyRequest) {
                $this->log->info("Monthly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($monthlyRequest));
            // Create Permit Request History
            $count = 0;
            foreach ($monthlyRequest as $key => $val) {
                $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("CUSTOMERPORTAL_URL", $val->partner_id);

                $permitRate = PermitRate::where('id', $val->permit_rate_id)->first();

                $this->log->info("Permit Rate Found" . json_encode($permitRate));
                if ($permitRate->rate == '0.00') {
                    //dd('stop');
                    QuerBuilder::createPermitRequestHistoryNew($val);
                    $maxDays = date('t');
                    $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate);
                    $val->save();
                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                    if ($facility_brand_setting) {
                        $val->facility_logo_id = $facility_brand_setting->id;
                    } else {
                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                        $val->logo_id  = $brand_setting->id;
                    }
                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                    $view = "usm.permit-renew-free";

                    MailHelper::sendEmail($val->user->email, $view, ['subject' => "Your Permit #" . $val->account_number . " has been Renewed Successfully", 'data' => $val, 'brand_setting' => $brand_setting], $val->partner_id);


                    // Mail::send(
                    //     $view,
                    //     ['data' => $val, 'brand_setting' => $brand_setting],
                    //     function ($message) use ($val) {
                    //         $message->to($val->user->email)->subject("Your Permit #" . $val->account_number . " has been Renewed Successfully");
                    //         $message->from(config('parkengage.default_sender_email'));
                    //     }
                    // );
                    $this->log->info("Mail sent to " . $val->user->email);
                } else if ($permitRate->rate > "0.00") {

                    if ($permitRate) {
                        $final_amount = $permitRate->rate;
                        $rateValidate = QueryBuilder::permitRenewCalculation((object) [], $val, $final_amount, 1);
                        $final_amount = $rateValidate['total_amount'];
                        if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                            dd('stop');
                            $paymentProfile = PlanetPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            $amount = ($val->facility->FacilityPaymentDetails->planet_payment_env == 'test') ? '3.00' : $final_amount;
                            if ($paymentProfile) {
                                $val->session_id = $paymentProfile->token;
                                $paymentByToken = PlanetPaymentGateway::planetPaymentByToken($val, $amount, '');
                                $this->log->info("planet permit Payment Response :" . json_encode($paymentByToken));
                                if (!in_array($paymentByToken["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                    $this->log->info("planet Payment Failed Response");
                                    // Failure mail send to user
                                    #DD PIMS-11368	                 
                                    if (!isset($paymentByToken)) {
                                        $paymentByToken = NULL;
                                    }
                                    QueryBuilder::setAllFailedTransactions(json_encode($paymentByToken), "planet", $val->account_number, 'PermitAutoRenewEvolution', $val->user_id);

                                    $val->transaction_retry   = $val->transaction_retry + 1;
                                    $val->save();
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = $partnerDetails->company_name;
                                    if ($val->transaction_retry > '1') {
                                        $view = 'usm.permit-cancel-reminder';
                                    }
                                    $view = "usm.permit-renew-fail";
                                    MailHelper::sendEmail($val->user->email, $view, ['subject' => "Important: Action Required for Your Permit Payment", 'data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name], $val->partner_id);

                                    // Mail::send(
                                    //     $view,
                                    //     ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                    //     function ($message) use ($val) {
                                    //         //  $message->to([$val->user->email])->subject("Payment Failed against your Permit #". $val->account_number);
                                    //         $message->to([$val->user->email])->subject("Important: Action Required for Your Permit Payment");
                                    //         $message->from(config('parkengage.default_sender_email'));
                                    //     }
                                    // );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                } else {
                                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($paymentByToken, $val->user_id);
                                    $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($planetTransaction));
                                    $processing_fee = $val->facility->permit_processing_fee;
                                    #DD PIMS-11368	
                                    if (isset($planetTransaction->id)) {
                                        QueryBuilder::setReferenceKey($planetTransaction->id, $val->account_number);
                                    }

                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $maxDays = date('t');
                                    $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate,$rateValidate, $val->transaction_retry + 1,$planetTransaction->id,$amount);
                                    $val->save();
                                    //user mail
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = $partnerDetails->company_name;
                                    $view = "usm.permit-renew";
                                    MailHelper::sendEmail($val->user->email, $view, ['subject' => "Autopayment Confirmation for Permit #" . $val->account_number, 'data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name], $val->partner_id);
                                    // Mail::send(
                                    //     $view,
                                    //     ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                    //     function ($message) use ($val) {
                                    //         $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #" . $val->account_number);
                                    //         $message->from(config('parkengage.default_sender_email'));
                                    //     }
                                    // );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                            //dd('stop');
                            $amount = ($val->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $final_amount;
                            $ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $val->facility->FacilityPaymentDetails->datacap_script_url;

                            $paymentProfile = DatacapPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            /* $processing_fee = $val->facility->permit_processing_fee;
                            $permit_rate = $permitRate->rate;
                            $final_amount = $processing_fee + $permit_rate;
                            $rateValidate = $this->partnerProRateCalculation($val);
                            $final_amount = $rateValidate['total_amount'];
                            $amount = $final_amount; */
                            //dd($amount,$processing_fee,$ecommerce_mid,$url);
                            if ($paymentProfile) {
                                $data['Token'] = $paymentProfile->token;
                                if ($amount > 0) {
                                    //$amount = number_format($amount, 2);
                                    $data['Amount'] = $amount;
                                    $data['Token'] = $paymentProfile->token;
                                    $data['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                    $data["CardHolderID"] = "Allow_V2";
                                    $this->log->info("Payment Request Data --" . json_encode($data) . "--" . json_encode($ecommerce_mid) . "--" . json_encode($url));
                                    //dd($data, $ecommerce_mid, $url);
                                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                    if ($paymentResponse["Status"] == "Error") {
                                        $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                        #DD PIMS-11368	                 
                                        if (!isset($paymentResponse)) {
                                            $paymentResponse = NULL;
                                        }
                                        QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "datacap", $val->account_number, 'PermitAutoRenewEvolution', $val->user_id);
                                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card .");
                                        } else {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card.");
                                        }
                                    } else if ($paymentResponse["Status"] == "Declined") {
                                        #DD PIMS-11368	                 
                                        if (!isset($paymentResponse)) {
                                            $paymentResponse = NULL;
                                        }
                                        QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "datacap", $val->account_number, 'PermitAutoRenewEvolution', $val->user_id);
                                    }
                                    if ($paymentResponse['Status'] == 'Approved') {
                                        QuerBuilder::createPermitRequestHistoryNew($val);
                                        $this->log->info("Payment Transaction Data Datacap Payment -- " . json_encode($paymentResponse));
                                        $request = new Request([
                                            'total'   => $amount,
                                            'card_last_four' => $paymentProfile->card_last_four,
                                            'expiration' => $paymentProfile->expiry
                                        ]);
                                        $this->log->info("Save Transaction Data Request --" . json_encode($request) . "--" . json_encode($val->user_id));
                                        $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $val->user_id, '');
                                        $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($authorized_anet_transaction));
                                        #DD PIMS-11368	
                                        if (isset($authorized_anet_transaction->id)) {
                                            QueryBuilder::setReferenceKey($authorized_anet_transaction->id, $val->account_number);
                                        }
                                        $maxDays = date('t');
                                        $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate,$rateValidate, $val->transaction_retry + 1,$authorized_anet_transaction->id,$final_amount);
                                        $val->save();
                                        //user mail
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $paymentProfile->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $paymentProfile->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $partnerDetails = User::where('id', $val->partner_id)->first();
                                        $partner_name = $partnerDetails->company_name;

                                        $view = "usm.permit-renew";
                                        MailHelper::sendEmail($val->user->email, $view, ['subject' => "Autopayment Confirmation for Permit #" . $val->account_number, 'data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name], $val->partner_id);
                                        // Mail::send(
                                        //     $view,
                                        //     ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                        //     function ($message) use ($val) {
                                        //         $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #" . $val->account_number);
                                        //         $message->from(config('parkengage.default_sender_email'));
                                        //     }
                                        // );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if (isset($val->facility->FacilityPaymentDetails) && ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {

                            $paymentProfile = HeartlandPaymentProfile::where('user_id', $val->user_id)->first();
                            /* $rateValidate = $this->partnerProRateCalculation($val);
                            $final_amount = $rateValidate['total_amount']; */
                            $this->log->info("Payment Profile Data --" . $val->account_number . "--" . json_encode($paymentProfile));
                           /*  $processing_fee = $val->facility->permit_processing_fee;
                            $permit_rate = $permitRate->rate; */
                            //   $final_amount = $processing_fee + $permit_rate;



                            $amount = ($val->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $final_amount;

                            if ($paymentProfile) {
                                $request = new Request([
                                    'Amount'   => $amount,
                                    'total'   => $amount,
                                    'token' => $paymentProfile->token,
                                    'zipcode' => $paymentProfile->zipcode,
                                    'card_last_four' => $paymentProfile->card_last_four,
                                    'expiration_date' => $paymentProfile->expiry,
                                    'original_total' => $permitRate->permit_final_amount
                                ]);


                                #end add parking time in email
                                $permitRateDescHour = array();
                                $permitRateDetails    = PermitRate::find($val->permit_rate_id);
                                if ($permitRateDetails) {
                                    $permitRateDescHour = PermitRateDescription::find($permitRateDetails->permit_rate_description_id);
                                }
                                $paymentResponse = '---';
                                try {
                                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $val->facility);
                                } catch (Exception $e) {
                                    $this->log->info("Payment Charge Error Response Permit Id #:" . $val->account_number . "--" . json_encode($e->getMessage()));
                                    #DD PIMS-11368	                                    
                                    QueryBuilder::setAllFailedTransactions(json_encode($e->getMessage()), "heartland", $val->account_number, 'PermitAutoRenewEvolution', $val->user_id);
                                }

                                $this->log->info("Heartland Payment Response to Permit Id #:" .  $val->account_number . "--" . json_encode($paymentResponse));

                                if (isset($paymentResponse->responseMessage) && $paymentResponse->responseMessage == 'APPROVAL') {
                                    QuerBuilder::createPermitRequestHistoryNew($val);
                                    $user_id = $val->user_id;
                                    $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $user_id);
                                    $this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($authorized_anet_transaction));
                                    $maxDays = date('t');
                                    $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate,$rateValidate, $val->transaction_retry + 1,$authorized_anet_transaction->id,$final_amount);
                                    $val->save();

                                    #DD PIMS-11368	
                                    if (isset($authorized_anet_transaction->id)) {
                                        QueryBuilder::setReferenceKey($authorized_anet_transaction->id, $val->account_number);
                                    }
                                    //user mail

                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $paymentProfile->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $paymentProfile->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = $partnerDetails->company_name;
                                    $view = "usm.permit-renew";
                                    MailHelper::sendEmail($val->user->email, $view, ['subject' => "Autopayment Confirmation for Permit #" . $val->account_number, 'data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'permitRateDescHour' => $permitRateDescHour, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name], $val->partner_id);
                                    // Mail::send(
                                    //     $view,
                                    //     ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'permitRateDescHour' => $permitRateDescHour, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                    //     function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear) {
                                    //         $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #" . $val->account_number);
                                    //         $message->from(config('parkengage.default_sender_email'));
                                    //     }
                                    // );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                } else {
                                    #DD PIMS-11368	                 
                                    if (!isset($paymentResponse)) {
                                        $paymentResponse = NULL;
                                    }
                                    QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "heartland", $val->account_number, 'PermitAutoRenewEvolution', $val->user_id);
                                    $val->transaction_retry   = $val->transaction_retry + 1;
                                    $val->save();
                                    // Failure mail send to user
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $paymentProfile->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $paymentProfile->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $day_of_month = date('d');
                                    if ($day_of_month == '05') {
                                        $view = 'usm.permit-cancel-reminder';
                                        //call skiData Api to cancel resuable ticket
                                        $subject = "Important Notice: Permit Cancellation";

                                        $val->status = '0';
                                        $val->user_consent = '0';
                                        $val->cancelled_at = $permitRenewDate;
                                        $val->save();
                                    } else {
                                        $view = "usm.permit-renew-fail";
                                        $subject = "Important: Action Required for Your Permit Payment";
                                    }

                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = $partnerDetails->company_name;
                                    $val->final_amount = $final_amount;
                                    MailHelper::sendEmail($val->user->email, $view, ['subject' =>  $subject, 'data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'permitRateDescHour' => $permitRateDescHour, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name, 'dynamicReceiptUrl' => $dynamicReceiptUrl], $val->partner_id);
                                    // Mail::send(
                                    //     $view,
                                    //     ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'permitRateDescHour' => $permitRateDescHour, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name, 'dynamicReceiptUrl' => $dynamicReceiptUrl],
                                    //     function ($message) use ($val, $subject) {
                                    //         $message->to([$val->user->email])->subject($subject);
                                    //         $message->from(config('parkengage.default_sender_email'));
                                    //     }
                                    // );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                            }
                        }
                    }
                } else {
                    $this->log->info("Permit Rate Not Found");
                }
                $count++;
            }
            $msg = "Total Count of Permit Renew: " . $count;
            $this->log->info($msg);
            return $msg;
        } catch (Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();

            $this->log->error($msg);
            $this->log->info("Queue ended");
        }
    }

    public function partnerProRateCalculation($request)
    {
        $facility = Facility::with(['facilityConfiguration'])->select('id', 'short_name', 'license_format', 'license_min_lenght', 'license_max_lenght', 'fix_length', 'permit_processing_fee', 'additonal_fee', 'additonal_fee_type', 'surcharge_fee', 'tax_rate')->where('id', $request->facility_id)->first();

        $permit_service_amount = 0;

        if ($request->permit_service != '') {
            foreach ($request->permit_service as $permit_services) {
                $permit_service_amount += $permit_services['permit_service_rate'];
                $individualPermitAmount[] = [
                    'permit_service_rate' => $permit_services['permit_service_rate'],
                    'id' => $permit_services['id']
                ];
            }
        } elseif ($request->permitService != '') {
            foreach (json_decode($request->permitService) as $permit_services) {
                $permit_service_amount += $permit_services->permit_service_rate;
                // $individualPermitAmount[] = $permit_services->permit_service_rate;
                $individualPermitAmount[] = [
                    'permit_service_rate' => $permit_services->permit_service_rate,
                    'id' => $permit_services->id
                ];
            }
        }

        $is_resident_user = '';
        $result = [];

        if ($request->driving_license != '') {

            $mystring = $request->driving_license;
            $mystring = trim($mystring); //removes any spaces from beginning of the string
            $number = substr($mystring, 1);

            if (strlen($mystring) < 7) {
                throw new ApiGenericException('Invalid Driving License');
            } else if (strlen($mystring) > 13) {
                throw new ApiGenericException('Invalid Driving License');
            } else if ($mystring[0] == 'H' && is_numeric($number) && strlen($mystring) == 9) {
                $is_resident_user = 'Pass';
            } else {
                $is_resident_user = 'Fail';
            }
        } else {
            $is_resident_user = 'Fail';
        }

        $month = date("m");
        $maxDays = date('t');
        //$maxDays = cal_days_in_month(CAL_GREGORIAN, 12, 2022);
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        $today = date("Y-m-d");
        //  $today = "2026-06-26";
        $start_date = date("m-d-Y");
        $end_date = date("m-t-Y", $midDateString);
        $diff = abs(strtotime($to_date) - strtotime($today));

        $years = floor($diff / (365 * 60 * 60 * 24));
        $months = floor(($diff - $years * 365 * 60 * 60 * 24) / (30 * 60 * 60 * 24));
        $days = floor(($diff) / (60 * 60 * 24));

        $result['start_date'] = isset($request->desired_start_date) ? date("m-d-Y", strtotime($request->desired_start_date)) : $start_date;
        $result['end_date'] = $end_date;

        if ($is_resident_user == 'Pass') {

            $rate = PermitRate::with(['facility' => function ($query) use ($request) {
                $query->where('id', $request->facility_id);
            }])->where('id', $request->permit_rate_id)
                ->where('facility_id', $request->facility_id)
                ->first();

            $new_pro_rate = 0;
            $data = [];
            if (count($rate) > 0) {
                $permit_tenure = QueryBuilder::getPermitTenure($rate->id);

                // change for USM Dynamic Rate PIMS-10379 -ujjwal				
                $permit_tenure = QueryBuilder::getPermitTenure($rate->id);
                if ($permit_tenure) {
                    $rate->rate = $permit_tenure->rate;
                    $rate->permit_tenure = $permit_tenure;
                }
                #dushyant 21/06/2024 hardcore date for usm
                if (in_array($request->partner_id, config('parkengage.PARTNER_GROUP_SERVICES'))) {
                    $permitRateDescHour = PermitRateDescription::find($rate['permit_rate_description_id']);
                    if (isset($permitRateDescHour->permit_start_date) && ($permitRateDescHour->permit_start_date > $today)) {
                        $result['start_date'] = date("m-d-Y", strtotime($permitRateDescHour->permit_start_date));
                    }

                    if (isset($permitRateDescHour->permit_end_date) && !empty($permitRateDescHour->permit_end_date)) {
                        $result['end_date'] = date("m-d-Y", strtotime($permitRateDescHour->permit_end_date));
                    }

                    if (isset($permitRateDescHour) && (strtolower($permitRateDescHour->hours_description) == "yearly")) {
                        $maxDays = date("L") ? 366 : 365; // Check if it's a leap year
                        $today = Carbon::today();
                        $futureDate = Carbon::create(2026, 6, 30);
                        $days = $today->diffInDays($futureDate);
                    }
                    if (isset($permit_tenure)) {
                        $today = Carbon::today();
                        $permit_end_day = $permit_tenure->permit_end_day;
                        $permit_end_month = $permit_tenure->permit_end_month;
                        $permit_end_year = $permit_tenure->permit_end_year;

                        $permit_start_day = $permit_tenure->permit_start_day;
                        $permit_start_month = $permit_tenure->permit_start_month;
                        $permit_start_year = $permit_tenure->permit_start_year;

                        $permitStartDate = Carbon::create($permit_start_year, $permit_start_month, $permit_start_day);
                        $futureDate = Carbon::create($permit_end_year, $permit_end_month, $permit_end_day);
                        $days = $today->diffInDays($futureDate);
                        $maxDays = $permitStartDate->diffInDays($futureDate);
                        $result['end_date'] = date("m-d-Y", strtotime($futureDate));
                    }
                    // Alka, Weekly Permit
                    if (isset($permitRateDescHour) && ($permitRateDescHour->hours_description) == "Weekly") {
                        // Get the current date and subtract 7 days
                        $dateSevenDaysAgo = Carbon::now()->subDays(7);

                        // Get the start date (7 days ago) and the end date (1 day before today)
                        $startDate = $dateSevenDaysAgo->copy()->startOfDay(); // Start of the day 7 days ago
                        $endDate = Carbon::now()->subDay()->endOfDay();       // End of the day 1 day ago (yesterday)

                        $permit_start_date = $startDate->toDateString();
                        $permit_end_date = $endDate->toDateString();

                        $endDate = new DateTime($permit_end_date);

                        // Add one day to the end date
                        $desiredStartDate = $endDate->modify('+1 day');
                        $result['start_date'] = $desiredStartDate->format('m-d-Y');

                        $desiredEndDate = $desiredStartDate->modify('+6 day');
                        $result['end_date'] = $desiredEndDate->format('m-d-Y');
                    }
                }
                #end dushyant 21/06/2024 hardcore date for usm

                $current_monthly_rate = $rate['rate'];
                $permit_final_amount = $current_monthly_rate + $permit_service_amount;
                $facility_fee_result = FacilityFee::select('name', 'val')->where('facility_id', $request->facility_id)->where('name', 'permit_processing_fee')->where('active', 1)->first();

                if ($permit_final_amount == '0.00') {
                    $permit_processing_fee = "0.00";
                }


                $per_day_rate = $permit_final_amount / $maxDays;
                //$per_day_rate = number_format($per_day_rate, 2);
                if ($days >= 1) {
                    $days++;
                } else if ($days == 0) {
                    $days = '1';
                }
                $effective_pro_rate = $days * $per_day_rate;
                //$effective_pro_rate = round($days * $per_day_rate);

                $rate['price']         = $effective_pro_rate;
                $tax_rate              = $facility->getTaxRate($rate);
                $additionalFee         = $facility->getAdditionalFee($rate);
                $surchargeFee          = $facility->getSurchargeFee($rate);

                $permit_processing_fee = isset($facility->permit_processing_fee) ? $facility->permit_processing_fee : '0.00';


                if (in_array($request->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_DESCRIPTION_ID'))) {
                    $total_amount = $rate['rate'] + $permit_processing_fee + $surchargeFee + $additionalFee + $tax_rate;
                    $effective_pro_rate = number_format($current_monthly_rate, 2);
                } else {
                    $total_amount = $permit_processing_fee + $effective_pro_rate;
                    $effective_pro_rate = number_format($effective_pro_rate, 2, ".", "");
                }
                $result['prorate_disable'] = $rate['prorate_disable'];
                $result['base_rate'] = number_format($current_monthly_rate, 2);
                $result['per_day_rate'] = number_format($per_day_rate, 2);
                $result['effective_days'] = number_format($days, 2);
                $result['processing_fee'] = number_format($permit_processing_fee, 2);
                $result['effective_pro_rate'] = number_format($effective_pro_rate, 2);

                $result['surcharge_fee'] = number_format($surchargeFee, 2);
                $result['additional_fee'] = number_format($additionalFee, 2);
                $result['tax_fee'] = number_format($tax_rate, 2);

                if ($facility->facilityConfiguration) {
                    if ($facility->facilityConfiguration->is_prorate_apply == '0') {
                        $result['total_amount'] = sprintf("%.2f", $current_monthly_rate + $permit_service_amount);
                        $result['effective_pro_rate'] = sprintf("%.2f", $current_monthly_rate);
                        $effective_pro_rate = sprintf("%.2f", $current_monthly_rate);
                        $total_amount = $permit_processing_fee + $current_monthly_rate + $permit_service_amount + $surchargeFee + $additionalFee + $tax_rate;
                    }
                }

                $result['effective_pro_rate'] = sprintf("%.2f", $effective_pro_rate);
                $result['total_amount'] = sprintf("%.2f", $total_amount);
                $result['permit_final_amount'] = sprintf("%.2f", $total_amount);
                return $result;
            } else {
                throw new ApiGenericException("Sorry! Permit Rate Not Found.");
            }
        } else {
            $rate = PermitRate::with(['facility' => function ($query) use ($request) {
                $query->where('id', $request->facility_id);
            }])->where('id', $request->permit_rate_id)
                ->where('facility_id', $request->facility_id)
                ->first();

            if (count($rate) > 0) {
                // change for USM Dynamic Rate PIMS-10379 -ujjwal				
                $permit_tenure = QueryBuilder::getPermitTenure($rate->id);

                if ($permit_tenure) {
                    $rate->rate = $permit_tenure->rate;
                    $rate->permit_tenure = $permit_tenure;
                }
                #dushyant 21/06/2024 hardcore date for usm
                if (in_array($request->partner_id, config('parkengage.PARTNER_GROUP_SERVICES'))) {
                    $permitRateDescHour = PermitRateDescription::find($rate['permit_rate_description_id']);

                    if (isset($permitRateDescHour->permit_start_date) && ($permitRateDescHour->permit_start_date > $today)) {
                        $result['start_date'] = date("m-d-Y", strtotime($permitRateDescHour->permit_start_date));
                    }

                    if (isset($permitRateDescHour->permit_end_date) && !empty($permitRateDescHour->permit_end_date)) {
                        $result['end_date'] = date("m-d-Y", strtotime($permitRateDescHour->permit_end_date));
                    }

                    if (isset($permitRateDescHour) && (strtolower($permitRateDescHour->hours_description) == "yearly")) {
                        $maxDays = date("L") ? 366 : 365; // Check if it's a leap year	
                        $today = Carbon::today();
                        $futureDate = Carbon::create(2026, 6, 30);
                        $days = $today->diffInDays($futureDate);
                    }
                    if (isset($permit_tenure)) {

                        $today = Carbon::today();
                        $permit_end_day = $permit_tenure->permit_end_day;
                        $permit_end_month = $permit_tenure->permit_end_month;
                        $permit_end_year = $permit_tenure->permit_end_year;

                        $permit_start_day = $permit_tenure->permit_start_day;
                        $permit_start_month = $permit_tenure->permit_start_month;
                        $permit_start_year = $permit_tenure->permit_start_year;

                        $permitStartDate = Carbon::create($permit_start_year, $permit_start_month, $permit_start_day);
                        $futureDate = Carbon::create($permit_end_year, $permit_end_month, $permit_end_day);
                        $days = $today->diffInDays($futureDate);
                        $maxDays = $permitStartDate->diffInDays($futureDate);

                        $result['end_date'] = date("m-d-Y", strtotime($futureDate));
                    }
                    // Alka, Weekly Permit
                    if (isset($permitRateDescHour) && ($permitRateDescHour->hours_description) == "Weekly") {
                        // Get the current date and subtract 7 days
                        $dateSevenDaysAgo = Carbon::now()->subDays(7);

                        // Get the start date (7 days ago) and the end date (1 day before today)
                        $startDate = $dateSevenDaysAgo->copy()->startOfDay(); // Start of the day 7 days ago
                        $endDate = Carbon::now()->subDay()->endOfDay();       // End of the day 1 day ago (yesterday)

                        $permit_start_date = $startDate->toDateString();
                        $permit_end_date = $endDate->toDateString();

                        $endDate = new DateTime($permit_end_date);

                        // Add one day to the end date
                        $desiredStartDate = $endDate->modify('+1 day');
                        $result['start_date'] = $desiredStartDate->format('m-d-Y');

                        $desiredEndDate = $desiredStartDate->modify('+6 day');
                        $result['end_date'] = $desiredEndDate->format('m-d-Y');
                    }
                }

                #end dushyant 21/06/2024 hardcore date for usm

                $current_monthly_rate = $rate['rate'];
                $facility_fee_result = FacilityFee::select('name', 'val')->where('facility_id', $request->facility_id)->where('name', 'permit_processing_fee')->where('active', 1)->first();

                $permit_final_amount = $current_monthly_rate + $permit_service_amount;

                if ($permit_final_amount == '0.00') {
                    $permit_processing_fee = "0.00";
                }

                $per_day_rate = $permit_final_amount / $maxDays;
                //$per_day_rate = number_format($per_day_rate, 2);


                if ($days >= 1) {
                    $days++;
                } else if ($days == 0) {
                    $days = '1';
                }
                $result['permit_rate_prorate'] =  '';
                $result['individual_permit_services'] = [];
                #pims-13210 dd
                $permitProRate = $current_monthly_rate / $maxDays;
                $permitEffectiveProRate = $days * $permitProRate;
                $rate['price']         = $permitEffectiveProRate;

                $tax_rate              = $facility->getTaxRate($rate);
                $additionalFee         = $facility->getAdditionalFee($rate);
                $surchargeFee          = $facility->getSurchargeFee($rate);

                $permit_processing_fee = isset($facility->permit_processing_fee) ? $facility->permit_processing_fee : '0.00';

                //dd($rate['price'],$rate['rate'],$tax_rate,$additionalFee,$surchargeFee,$permit_processing_fee);


                if (in_array($request->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_DESCRIPTION_ID'))) {
                    $permit_effective_prorate = number_format($permitEffectiveProRate, 2);
                } else {
                    $permit_effective_prorate = number_format($permitEffectiveProRate, 2, ".", "");
                }
                $result['permit_rate_prorate'] =  $permit_effective_prorate;
                // Individual pro rate calculation
                if (isset($individualPermitAmount) && !empty($individualPermitAmount)) {
                    foreach ($individualPermitAmount as $permitAmountData) {

                        $permit_final_amount = $permitAmountData['permit_service_rate'];
                        $individualPerDayRate = $permit_final_amount / $maxDays;

                        $individualEffectiveProRate = $days * $individualPerDayRate;

                        if (in_array($request->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_DESCRIPTION_ID'))) {
                            $individual_effective_pro_rate = number_format($individualEffectiveProRate, 2);
                        } else {
                            $individual_effective_pro_rate = number_format($individualEffectiveProRate, 2, ".", "");
                        }

                        $result['individual_permit_services'][] = [
                            'service_id' => $permitAmountData['id'],
                            'individual_per_day_rate' => number_format($individualPerDayRate, 2),
                            'individual_effective_pro_rate' => $individual_effective_pro_rate
                        ];
                    }
                }

                $effective_pro_rate = $days * $per_day_rate;
                //$effective_pro_rate = round($days * $per_day_rate);

                if (in_array($request->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_DESCRIPTION_ID'))) {
                    $total_amount = $rate['rate'] + $permit_processing_fee + $surchargeFee + $additionalFee + $tax_rate;
                    $effective_pro_rate = number_format($current_monthly_rate, 2);
                } else {
                    $total_amount = $permit_processing_fee + $effective_pro_rate  + $surchargeFee + $additionalFee + $tax_rate;
                    $effective_pro_rate = number_format($effective_pro_rate, 2, ".", "");
                }

                #DD multitax
                $result['prorate_disable'] = $rate['prorate_disable'];
                $result['base_rate'] = number_format($current_monthly_rate, 2);
                $result['per_day_rate'] = number_format($per_day_rate, 2);
                $result['effective_days'] = number_format($days, 2);
                $result['effective_pro_rate'] = number_format($effective_pro_rate, 2);
                $result['processing_fee'] = number_format($permit_processing_fee, 2);
                $result['total_amount'] = number_format($total_amount, 2);
                $result['surcharge_fee'] = number_format($surchargeFee, 2);
                $result['additional_fee'] = number_format($additionalFee, 2);
                $result['tax_fee'] = number_format($tax_rate, 2);

                if ($facility->facilityConfiguration) {
                    if ($facility->facilityConfiguration->is_prorate_apply == '0') {
                        $result['total_amount'] = sprintf("%.2f", $current_monthly_rate + $permit_service_amount);
                        $result['effective_pro_rate'] = sprintf("%.2f", $current_monthly_rate);
                        $effective_pro_rate = sprintf("%.2f", $current_monthly_rate);
                        $total_amount = $permit_processing_fee + $current_monthly_rate + $permit_service_amount  + $surchargeFee + $additionalFee + $tax_rate;
                    }
                }
                $result['effective_pro_rate'] = sprintf("%.2f", $effective_pro_rate);
                $result['total_amount'] = sprintf("%.2f", $total_amount);
                $result['permit_final_amount'] = sprintf("%.2f", $total_amount);

                return $result;
            } else {
                throw new ApiGenericException("Sorry! Permit Rate Not Found.");
            }
        }
    }
}
