<?php

namespace App\Console\Commands\Evolution;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Http\Helpers\QueryBuilder;

use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\Evolution\EvolutionPermitCharge;

use App\Models\ParkEngage\HeartlandPaymentProfile;

/**
 * Emails reservation stub to user
 */
class EvolutionPermitAutoRenewMq extends Command
{
    use DispatchesJobs;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'evolution:permit-renewal {partner_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly permit renew.';
    protected $log;

	const PARTNER_ID = '215900';
	const FACILITY_ID = '317'; 
    const QUEUE_NAME = 'evolution-permit-renew-list';
	
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/Evolution/permitRenewal')->createLogger('autorenew');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {   //dd('I am here now');
        try{ 

            $partnerIdArg = $this->argument('partner_id');
           
            if($partnerIdArg){
                $partnerId = $this->argument('partner_id');
                $facilityId = self::FACILITY_ID;
            }else{
                $facilityId = self::FACILITY_ID;
                $partnerId = self::PARTNER_ID;
            }
            
            QueryBuilder::setCustomTimezone($facilityId);
            
            $permit_end_date = date('Y-m-d', strtotime('last day of previous month'));
            $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));
            
            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F",$time);
            $PermitRenewYear = date("Y",$time);
            
            $monthlyRequest = PermitRequest::select('id','facility_id','user_id','partner_id','account_number','permit_rate_id','permit_rate','permit_final_amount')
                                            ->where('partner_id',$partnerId)
                                            ->where('facility_id',$facilityId)
                                            ->whereDate('desired_end_date', '<=', $permit_end_date)
                                            ->whereDate('desired_start_date', '>=', $permit_start_date)
                                            ->whereNull('cancelled_at')->whereNull('deleted_at')
                                            ->where('user_consent',1)
                                            ->whereNotNull('anet_transaction_id')
                                            ->get();

            
            //dd(count($monthlyRequest));
            if (!$monthlyRequest) {
                $this->log->info("Monthly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($monthlyRequest));
            $count = 0;
            foreach($monthlyRequest as $key=>$val){
                $payment_profile = HeartlandPaymentProfile::select('token', DB::raw('count(token) as token_count'))
                ->where('token', $val->token)
                ->groupBy('token')
                ->havingRaw('count(token) > 1')
                ->first();
                if($payment_profile && ($payment_profile->token_count > 1)) {
                    $job = (new EvolutionPermitCharge($val))->onQueue(self::QUEUE_NAME)->delay(30); 
                } else {
                    $job = (new EvolutionPermitCharge($val))->onQueue(self::QUEUE_NAME)->delay(3); 
                }
                 
                $this->dispatch($job);                
            			    
                $response['status'] = true;
			    $response['message'] = 'Permit Data added in Queue Succesfully';
                $this->log->info("Permit Data Pushed to queue" . json_encode($response));
                $count++;	
            }
            $msg = "Total Count of Permit Renew: ".$count;
            $this->log->info($msg);
            return $msg;
        }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
        
    }

}
