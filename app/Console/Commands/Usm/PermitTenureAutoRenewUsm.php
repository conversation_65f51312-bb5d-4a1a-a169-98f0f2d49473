<?php

namespace App\Console\Commands\Usm;

use App\Classes\DatacapPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Classes\PlanetPaymentGateway;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Models\PermitRequest;
use App\Models\PermitRequestRenewHistory;
use App\Models\PermitServices;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use DateTime;
use App\Models\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Http\Helpers\QueryBuilder;
use App\Models\ParkEngage\permitTenureMapping;

class PermitTenureAutoRenewUsm extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'usm:tenure-permit-renew';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'tenure permit renew season changed.';

    // const PARTNER_ID = '19349';
    // const FACILITY_ID = '167';

    public $log;
    protected $partnerId;
    protected $facilityId;


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/usm/')->createLogger('tenure_autorenew');
        $this->partnerId = config('parkengage.PARTNER_USM');
        $this->facilityId = config('parkengage.USM_FACILITY');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $dateSevenDaysAgo = Carbon::now()->subDays(7);

            // Get the start date (7 days ago) and the end date (1 day before today)
            $startDate = $dateSevenDaysAgo->copy()->startOfDay(); // Start of the day 7 days ago
            $endDate = Carbon::now()->subDay()->endOfDay();       // End of the day 1 day ago (yesterday)

            $permit_start_date = $startDate->toDateString();
            $permit_end_date = $endDate->toDateString();

            $endDate = new DateTime($permit_end_date);

            // Add one day to the end date
            $desiredStartDate = $endDate->modify('+1 day');
            $desired_start_date = $desiredStartDate->format('Y-m-d');

            $desiredEndDate = $desiredStartDate->modify('+6 day');
            $desired_end_date = $desiredEndDate->format('Y-m-d');


            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F", $time);
            $PermitRenewYear = date("Y", $time);
            $PermitRenewDay = date("d", $time);

            //    $permitRateIds = permitTenureMapping::where('partner_id', $this->partnerId)->where('facility_id', $this->facilityId)->distinct()->pluck('permit_rate_id');
            $permitRateIds = config('parkengage.USM_YEARLY_PERMIT_RATE_ID');
            if ($permitRateIds) {
                $weeklyRequest = PermitRequest::with(['facility.facilityPaymentDetails', 'user', 'permitVehicle', 'PermitRate.PermitTenureMapping'])
                    ->where('partner_id', $this->partnerId)
                    ->whereDate('desired_end_date', '>=', $permit_start_date)
                    ->whereDate('desired_end_date', '<=', $permit_end_date)
                    ->whereNull('cancelled_at')
                    ->whereNull('deleted_at')
                    ->where('user_consent', 1)
                    ->whereIn('permit_rate_id', $permitRateIds)
                    ->whereNotNull('anet_transaction_id')
                    ->whereNotIn('permit_rate_id', [341, 342])
                    //    ->whereNotIn('account_number', ['********'])
                    ->whereHas('PermitRate', function ($query) use ($permitRateIds) {
                        $query->whereIn('id', $permitRateIds);
                    })
                    ->orderBy('id', 'desc')
                    ->limit(10)
                    ->get();
            }
            // dd(count($weeklyRequest));
            //dd($weeklyRequest[0]->id,$weeklyRequest[1]->id);
            if (!$weeklyRequest) {
                //throw new NotFoundException('Weekly Request Not Found.');
                $this->log->info("Weekly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($weeklyRequest));

            $count = 0;
            $partner_name = "USM Parking Services";
            foreach ($weeklyRequest as $key => $val) {

                $this->log->info("Under Foreach loop " . $key);
                #PIMS-11336  DD  
                #wait for 31 second as samecard details for multi permit in business acc for subordinate case              
                if (!is_null($val->business_id) && $val->business_id > 0) {
                    sleep(31);
                }
                #End DD

                $maxDays = date('t');
                $permit_tenure = PermitRate::getPermitTenure($val->permit_rate_id);
                if (!isset($permit_tenure)) {
                    continue;
                }

                $today = Carbon::today();
                $permit_end_day     = $permit_tenure->permit_end_day;
                $permit_end_month   = $permit_tenure->permit_end_month;
                $permit_end_year    = $permit_tenure->permit_end_year;

                $permit_start_day   = $permit_tenure->permit_start_day;
                $permit_start_month = $permit_tenure->permit_start_month;
                $permit_start_year  = $permit_tenure->permit_start_year;
                $permit_frequency  = $permit_tenure->permit_frequency;

                $permitStartDate = Carbon::create($permit_start_year, $permit_start_month, $permit_start_day);
                $desired_start_date = $permitStartDate;
                $futureDate = Carbon::create($permit_end_year, $permit_end_month, $permit_end_day);
                $days = $today->diffInDays($futureDate);
                $maxDays = $permitStartDate->diffInDays($futureDate);
                $end_date = date("Y-m-d", strtotime($futureDate));
                $desired_start_date = date("Y-m-d", strtotime($desired_start_date));
                $desired_end_date = $end_date;

                $desired_start_date = date('Y-m-01');
                $desired_end_date = config('parkengage.USM_YEARLY_END_DATE');

                $this->log->info("Permit Tenure Rate Found" . json_encode($permit_tenure));

                $permit_services = PermitRequestServiceMapping::where('permit_request_id', $val->id)
                    ->select('permit_service_id', 'permit_service_rate')
                    ->get();
                $permit_service_ids = $permit_services->pluck('permit_service_id');
                $services  = PermitServices::with([
                    'permit_service_criteria_mappings' => function ($query) use ($permit_service_ids) {
                        $query->whereIn('permit_service_id', $permit_service_ids)
                            ->with('criteria');
                    }
                ])
                    ->select('permit_services.*')
                    ->whereIn('id', $permit_service_ids)
                    ->orderBy('permit_services.id', 'asc')
                    ->get();
                $total_service_rate = 0;
                $total_service_rate = $permit_services->sum('permit_service_rate');

                if (count($services) > 0) {
                    $services = $this->formatPermitServiceCriteria($services);
                }

                $processing_fee = $val->facility->permit_processing_fee;
                $permit_rate = $permit_tenure->rate + $total_service_rate;
                //$permit_rate = $permit_tenure->rate;

                #end add parking time in email
                $permit_validity = '';
                $permitRateDescHour = array();
                $permitRateDescHour = PermitRateDescription::find($permit_tenure->permit_rate_description_id);
                if ($permit_frequency) {
                    if (($permit_frequency == "1")) {
                        $permit_validity = '1 Month';
                    } else if (($permit_frequency == "2")) {
                        $permit_validity = '2 Month';
                    } else if (($permit_frequency == "3")) {
                        $permit_validity = 'Quarterly';
                    } else if (($permit_frequency == "4")) {
                        $permit_validity = '4 Month';
                    } else if (($permit_frequency == "5")) {
                        $permit_validity = '5 Month';
                    } else if (($permit_frequency == "6")) {
                        $permit_validity = 'Half Yearly';
                    } else if (($permit_frequency == "7")) {
                        $permit_validity = '7 Month';
                    } else if (($permit_frequency == "8")) {
                        $permit_validity = '8 Month';
                    } else if (($permit_frequency == "9")) {
                        $permit_validity = '9 Month';
                    } else if (($permit_frequency == "10")) {
                        $permit_validity = '10 Month';
                    } else if (($permit_frequency == "11")) {
                        $permit_validity = '11 Month';
                    } else if (($permit_frequency == "12")) {
                        $permit_validity = '1 Year';
                    } else if (($permit_frequency == "13")) {
                        $permit_validity = 'Weekly';
                    } else {
                        $permit_validity = '1 Month';
                    }
                }

                if ($permit_rate == '0.00') {
                    //dd('stop');
                    $this->createPermitRequestWeeklyHistoryNew($val);
                    $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate);
                    $val->save();

                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                    if ($facility_brand_setting) {
                        $val->facility_logo_id = $facility_brand_setting->id;
                    } else {
                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                        $val->logo_id  = $brand_setting->id;
                    }
                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                    $view = "usm.permit-renew-free";
                    $partnerDetails = User::where('id', $val->partner_id)->first();
                    $partner_name = "USM Parking Services";

                    Mail::send(
                        $view,
                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permit_tenure, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'partner_name' => $partner_name],
                        function ($message) use ($val) {
                            $message->to($val->user->email)->subject("Your Permit #" . $val->account_number . " has been Renewed Successfully");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Mail sent to " . $val->user->email);
                } else if ($permit_rate > "0.00") {
                    if ($permit_tenure) {

                        if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                            dd('stop');
                            $paymentProfile = PlanetPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            $final_amount = $processing_fee + $permit_rate;
                            $amount = ($val->facility->FacilityPaymentDetails->planet_payment_env == 'test') ? '3.00' : $final_amount;
                            if ($paymentProfile) {
                                $val->session_id = $paymentProfile->token;
                                $paymentByToken = PlanetPaymentGateway::planetPaymentByToken($val, $amount, '');
                                $this->log->info("planet permit Payment Response :" . json_encode($paymentByToken));
                                if (!in_array($paymentByToken["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                    $this->log->info("planet Payment Failed Response :" . json_encode($paymentByToken));
                                    // Failure mail send to user
                                    #DD PIMS-11368	                 
                                    if (!isset($paymentByToken)) {
                                        $paymentByToken = NULL;
                                    }
                                    QueryBuilder::setAllFailedTransactions(json_encode($paymentByToken), "planet", $val->account_number, 'PermitTenureAutoRenewUsm', $val->user_id);
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();

                                    $view = "usm.permit-renew-fail";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permit_tenure, 'partner_name' => $partner_name],
                                        function ($message) use ($val) {
                                            $message->to([$val->user->email])->subject("Payment Failed against your Permit #" . $val->account_number);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                } else {
                                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($paymentByToken, $val->user_id);
                                    $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($planetTransaction));
                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $maxDays = date('t');
                                    $val->no_of_days          = $maxDays;
                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $val->permit_rate         = $permit_tenure->rate;
                                    $val->desired_start_date  = $desired_start_date;
                                    $val->desired_end_date    = $desired_end_date;
                                    $val->grace_end_date        = QueryBuilder::fetchPermitGraceEndDate($val); #pims-14610
                                    $val->save();
                                    #DD PIMS-11368	
                                    if (isset($planetTransaction->id)) {
                                        QueryBuilder::setReferenceKey($planetTransaction->id, $val->account_number);
                                    }
                                    // QueryBuilder::setAllTransactions(json_encode($paymentByToken), "planet", $val->account_number, 'PermitTenureAutoRenewUsm', $val->user_id);
                                    //user mail

                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);

                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();

                                    $view = "usm.permit-renew";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permit_tenure],
                                        function ($message) use ($val) {
                                            $message->to($val->user->email)->subject("Payment made successfully against Permit #" . $val->account_number);
                                            //$message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                            dd('stop');
                            $final_amount = $processing_fee + $permit_rate;
                            $amount = ($val->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $final_amount;
                            $ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $val->facility->FacilityPaymentDetails->datacap_script_url;

                            $paymentProfile = DatacapPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));

                            if ($paymentProfile) {
                                $data['Token'] = $paymentProfile->token;
                                if ($amount > 0) {
                                    //  $amount = number_format($amount, 2);
                                    $data['Amount'] = $amount;
                                    $data['Token'] = $paymentProfile->token;
                                    $data['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                    $data["CardHolderID"] = "Allow_V2";
                                    $this->log->info("Payment Request Data --" . json_encode($data) . "--" . json_encode($ecommerce_mid) . "--" . json_encode($url));
                                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                    if ($paymentResponse["Status"] == "Error") {
                                        #DD PIMS-11368	                 
                                        if (!isset($paymentResponse)) {
                                            $paymentResponse = NULL;
                                        }
                                        QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "datacap", $val->account_number, 'PermitTenureAutoRenewUsm', $val->user_id);
                                        $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card .");
                                        } else {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card.");
                                        }
                                    } else if ($paymentResponse["Status"] == "Declined") {
                                        // Failure mail send to user
                                        #DD PIMS-11368	                 
                                        if (!isset($paymentResponse)) {
                                            $paymentResponse = NULL;
                                        }
                                        QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "datacap", $val->account_number, 'PermitTenureAutoRenewUsm', $val->user_id);
                                        $val->card_last_four = $paymentProfile->card_last_four;
                                        $val->card_name = $paymentProfile->card_name;
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $val->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $val->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $val->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $view = "usm.permit-renew-fail";
                                        Mail::send(
                                            $view,
                                            ['data' => $val, 'brand_setting' => $brand_setting],
                                            function ($message) use ($val) {
                                                $message->to([$val->user->email])->subject("Your Permit #" . $val->account_number . "  could not be renewed due to Payment Failure");
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                    if ($paymentResponse['Status'] == 'Approved') {
                                        $this->log->info("Payment Transaction Data Datacap Payment -- " . json_encode($paymentResponse));
                                        $request = new Request([
                                            'total'   => $amount,
                                            'card_last_four' => $paymentProfile->card_last_four,
                                            'expiration' => $paymentProfile->expiry
                                        ]);
                                        $this->log->info("Save Transaction Data Request --" . json_encode($request) . "--" . json_encode($val->user_id));
                                        $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $val->user_id, '');
                                        $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($authorized_anet_transaction));
                                        $maxDays = date('t');
                                        $val->no_of_days          = $maxDays;
                                        $val->anet_transaction_id = $authorized_anet_transaction->id;
                                        $val->permit_rate         = $permit_tenure->rate;
                                        $val->desired_start_date  = $desired_start_date;
                                        $val->desired_end_date    = $desired_end_date;
                                        $val->grace_end_date        = QueryBuilder::fetchPermitGraceEndDate($val); #pims-14610
                                        $val->save();
                                        #DD PIMS-11368	
                                        if (isset($authorized_anet_transaction->id)) {
                                            QueryBuilder::setReferenceKey($authorized_anet_transaction->id, $val->account_number);
                                        }
                                        // QueryBuilder::setAllTransactions(json_encode($paymentResponse), "datacap", $val->account_number, 'PermitTenureAutoRenewUsm', $val->user_id);
                                        //user mail

                                        $val->card_last_four = $paymentProfile->card_last_four;
                                        $val->card_name = $paymentProfile->card_name;
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $val->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $val->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $val->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $view = "usm.permit-renew";
                                        Mail::send(
                                            $view,
                                            ['data' => $val, 'brand_setting' => $brand_setting],
                                            function ($message) use ($val) {
                                                $message->to($val->user->email)->subject("Your Permit #" . $val->account_number . " has been Renewed Successfully");
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if (isset($val->facility->FacilityPaymentDetails) && ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {

                            $paymentProfile = HeartlandPaymentProfile::where('user_id', $val->user_id)->first();

                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));

                            $final_amount = $processing_fee + $permit_rate;
                            $amount = ($val->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $final_amount;
                            //  $amount = number_format($amount, 2);

                            if ($paymentProfile) {
                                $request = new Request([
                                    'Amount'   => $amount,
                                    'total'   => $amount,
                                    'token' => $paymentProfile->token,
                                    'zipcode' => $paymentProfile->zipcode,
                                    'card_last_four' => $paymentProfile->card_last_four,
                                    'expiration_date' => $paymentProfile->expiry,
                                    'original_total' => $final_amount
                                    // 'original_total' => 22.0
                                ]);
                                // dd($request->all(),$val->id);
                                try {
                                    $paymentToken = HeartlandPaymentProfile::where('user_id', $val->user_id)->first();
                                    $request->request->add(['token' => $paymentToken->token]);

                                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $val->facility);
                                    $this->log->info("PTenureRenewC: Heartland Payment Response to Permit Id #:" .  $val->account_number . "--" . json_encode($paymentResponse));
                                } catch (Exception $e) {
                                    $this->log->info("Payment Charge Error Response Permit Id #:" . $val->account_number . "--" . json_encode($e->getMessage()));
                                    #DD PIMS-11368	                                    
                                    QueryBuilder::setAllFailedTransactions(json_encode($e->getMessage()), "heartland", $val->account_number, 'PermitTenureAutoRenewUsm', $val->user_id);
                                    if (str_contains($e->getMessage(), 'duplicate')) {
                                    } else {
                                        //$this->log->info("Error in Heartland Payment with payment profile id --" . json_encode($e->getMessage()));
                                        // Failure mail send to user
                                        //$val->card_last_four = $paymentProfile->card_last_four;
                                        //$val->card_name = $paymentProfile->card_name;
                                        /*
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $paymentProfile->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $paymentProfile->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $view = "usm.permit-renew-fail";
                                        
                                        $subject = "Important: Action Required for Your Permit Payment";
                                        $partnerDetails = User::where('id', $val->partner_id)->first();
                                        Mail::send(
                                            $view,
                                            ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permit_tenure, 'paymentProfile' => $paymentProfile, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name, 'display_services' => 1],
                                            function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear, $subject) {
                                                $message->to([$val->user->email])->subject($subject);
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                        */
                                    }
                                }


                                if (isset($paymentResponse) && $paymentResponse->responseMessage == 'APPROVAL') {
                                    $this->createPermitRequestWeeklyHistoryNew($val);
                                    $user_id = $val->user_id;
                                    $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $user_id);
                                    $this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($authorized_anet_transaction));
                                    $maxDays = date('t');
                                    $val->no_of_days          = $maxDays;
                                    $val->anet_transaction_id = $authorized_anet_transaction->id;
                                    $val->permit_rate         = $permit_tenure->rate;
                                    $val->desired_start_date  = $desired_start_date;
                                    $val->desired_end_date    = $desired_end_date;
                                    $val->permit_final_amount = $final_amount;
                                    $val->processing_fee      = $processing_fee;
                                    $val->grace_end_date        = QueryBuilder::fetchPermitGraceEndDate($val); #pims-14610
                                    $val->save();
                                    #DD PIMS-11368	
                                    if (isset($authorized_anet_transaction->id)) {
                                        QueryBuilder::setReferenceKey($authorized_anet_transaction->id, $val->account_number);
                                    }
                                    //QueryBuilder::setAllTransactions(json_encode($paymentResponse), "heartland", $val->account_number,'PermitTenureAutoRenewUsm',$val->user_id);

                                    //user mail
                                    //dd($paymentProfile);
                                    //$val->card_last_four = $paymentProfile->card_last_four;
                                    //$val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        //dd($expiry,$expiry_data);
                                        $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $paymentProfile->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $paymentProfile->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();

                                    $view = "usm.permit-renew";
                                    /* Mail::send(
										$view, 
                                        ['data' => $val, 'brand_setting' => $brand_setting,'permitRate' => $permit_tenure,'paymentProfile'=>$paymentProfile,'services' => $services,'permitRateDescHour' => $permitRateDescHour,'permit_validity'=>$permit_validity,'partner_name'=>$partner_name], 
                                        function ($message) use($val,$desired_end_date) {
											$message->to($val->user->email)->subject("Payment Successful against Permit #". $val->account_number." for the week, ending on <".$desired_end_date.">");
										  //  $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
											$message->from(config('parkengage.default_sender_email'));
                                        }
									); */
                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permit_tenure, 'paymentProfile' => $paymentProfile, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name, 'display_services' => 1],
                                        function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear) {
                                            $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #" . $val->account_number);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                } else {
                                    #DD PIMS-11368	                 
                                    if (!isset($paymentResponse)) {
                                        $paymentResponse = NULL;
                                    }
                                    QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "heartland", $val->account_number, 'PermitTenureAutoRenewUsm', $val->user_id);
                                }
                            }
                        }
                    }
                } else {
                    $this->log->info("Permit Rate Not Found");
                }
                $count++;
            }
            $msg = "Total Count of Permit Renew: " . $count;
            $this->log->info($msg);
            return $msg;
        } catch (Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();

            $this->log->error($msg);
            $this->log->info("Queue ended");
        }
    }

    public function createPermitRequestHistory($monthlyRequest)
    {
        if ($monthlyRequest) {
            $permitRenewDate = date('Y-m-d h:i:s');
            foreach ($monthlyRequest as $k => $v) {
                $permitData = PermitRequestRenewHistory::firstOrNew(['partner_id' => $this->partnerId, 'anet_transaction_id' => $v->anet_transaction_id]);

                $permitData->permit_request_id = $v->id;
                $permitData->user_id = $v->user_id;
                $permitData->facility_id = $v->facility_id;
                $permitData->anet_transaction_id = $v->anet_transaction_id;
                $permitData->tracking_code = $v->tracking_code;
                $permitData->email = $v->email;
                $permitData->name = $v->name;
                $permitData->phone = $v->phone;
                $permitData->permit_rate = $v->permit_rate;
                $permitData->permit_rate_id = $v->permit_rate_id;
                $permitData->approved_on = $v->approved_on;
                $permitData->account_number = $v->account_number;
                $permitData->monthly_duration_value = $v->monthly_duration_value;
                $permitData->no_of_days = $v->no_of_days;
                $permitData->partner_id = $v->partner_id;
                $permitData->license_number = $v->license_number;
                $permitData->mer_reference = $v->mer_reference;
                $permitData->image_front = $v->image_front;
                $permitData->image_back = $v->image_back;
                $permitData->user_consent = $v->user_consent;
                $permitData->vehicle_id = $v->vehicle_id;
                $permitData->is_admin = $v->is_admin;
                $permitData->ex_month = $v->ex_month;
                $permitData->ex_year = $v->ex_year;
                $permitData->payment_gateway = $v->payment_gateway;
                $permitData->permit_type = $v->permit_type;
                $permitData->is_payment_authrize = $v->is_payment_authrize;
                $permitData->session_id = $v->session_id;
                $permitData->permit_type_name = $v->permit_type_name;
                $permitData->skidata_id = $v->skidata_id;
                $permitData->skidata_value = $v->skidata_value;
                $permitData->acknowledge = $v->acknowledge;
                $permitData->facility_zone_id = $v->facility_zone_id;
                $permitData->desired_start_date = $v->desired_start_date;
                $permitData->desired_end_date = $v->desired_end_date;
                $permitData->cancelled_at = $v->cancelled_at;
                $permitData->created_at = $permitRenewDate;
                $permitData->updated_at = $permitRenewDate;
                $permitData->deleted_at = $v->deleted_at;

                $permitData->hid_card_number = $v->hid_card_number;
                $permitData->account_name = $v->account_name;
                $permitData->permit_final_amount = $v->permit_final_amount;
                $permitData->user_remark = $v->user_remark;
                $permitData->user_type_id = $v->user_type_id;
                $permitData->is_antipass_enabled = $v->is_antipass_enabled;
                $permitData->admin_user_id = $v->admin_user_id;
                $permitData->discount_amount = $v->discount_amount;
                $permitData->promocode = $v->promocode;
                $permitData->negotiated_amount = $v->negotiated_amount;
                $permitData->promocode              = $v->promocode;
                $permitData->processing_fee         = $v->processing_fee;
                $permitData->refund_amount          = $v->refund_amount;
                $permitData->refund_type            = $v->refund_type;
                $permitData->refund_remarks         = $v->refund_remarks;
                $permitData->refund_date            = $v->refund_date;
                $permitData->refund_by              = $v->refund_by;
                $permitData->refund_transaction_id  = $v->refund_transaction_id;
                $permitData->refund_status          = $v->refund_status;

                $permitData->save();
            }
        }
        return true;
    }

    private function formatPermitServiceCriteria($services)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);

        $i = 0;
        foreach ($services as $service) {
            if (count($service->permit_service_criteria_mappings) > 0) {
                $formatted = [];
                foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
                    $item = $permit_service_criteria_mapping->criteria;
                    $days = explode(',', $item->days);
                    sort($days); // Sort days to match the sequence of $allDays
                    if ($days == $allDays) {
                        $dayNamesStr = 'All Days';
                    } else {
                        $dayNames = array_map(function ($day) use ($daysMap) {
                            return $daysMap[$day];
                        }, $days);
                        $dayNamesStr = implode(',', $dayNames);
                    }

                    $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
                    $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
                    $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

                    // Adjust exit time if it's greater than 24 hours
                    $exit_time = explode(":", $item->exit_time_end);
                    if ($exit_time[0] > 23) {
                        $next_hr = $exit_time[0] - 24;
                        $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                        $exit_time_overflow = ' (next day)';
                    } else {
                        $exit_time_overflow = '';
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                    }


                    $formatted[] = [
                        'days' => $dayNamesStr,
                        'entry_time_begin' => $entry_time_begin,
                        'entry_time_end' => $entry_time_end,
                        'exit_time_begin' => $exit_time_begin,
                        'exit_time_end' => $exit_time_end . $exit_time_overflow,
                    ];
                }
                $services[$i]->criteria = $formatted;
            }
            $i++;
        }

        return $services;
    }

    #add parking time in email end

    public function createPermitRequestWeeklyHistoryNew($weeklyRequest)
    {
        if ($weeklyRequest) {
            $permitRenewDate = date('Y-m-d h:i:s');
            $permitData = PermitRequestRenewHistory::firstOrNew(['partner_id' => $this->partnerId, 'anet_transaction_id' => $weeklyRequest->anet_transaction_id]);

            $permitData->permit_request_id = $weeklyRequest->id;
            $permitData->user_id = $weeklyRequest->user_id;
            $permitData->facility_id = $weeklyRequest->facility_id;
            $permitData->anet_transaction_id = $weeklyRequest->anet_transaction_id;
            $permitData->tracking_code = $weeklyRequest->tracking_code;
            $permitData->email = $weeklyRequest->email;
            $permitData->name = $weeklyRequest->name;
            $permitData->phone = $weeklyRequest->phone;
            $permitData->permit_rate = $weeklyRequest->permit_rate;
            $permitData->permit_rate_id = $weeklyRequest->permit_rate_id;
            $permitData->approved_on = $weeklyRequest->approved_on;
            $permitData->account_number = $weeklyRequest->account_number;
            $permitData->monthly_duration_value = $weeklyRequest->monthly_duration_value;
            $permitData->no_of_days = $weeklyRequest->no_of_days;
            $permitData->partner_id = $weeklyRequest->partner_id;
            $permitData->license_number = $weeklyRequest->license_number;
            $permitData->mer_reference = $weeklyRequest->mer_reference;
            $permitData->image_front = $weeklyRequest->image_front;
            $permitData->image_back = $weeklyRequest->image_back;
            $permitData->user_consent = $weeklyRequest->user_consent;
            $permitData->vehicle_id = $weeklyRequest->vehicle_id;
            $permitData->is_admin = $weeklyRequest->is_admin;
            $permitData->ex_month = $weeklyRequest->ex_month;
            $permitData->ex_year = $weeklyRequest->ex_year;
            $permitData->payment_gateway = $weeklyRequest->payment_gateway;
            $permitData->permit_type = $weeklyRequest->permit_type;
            $permitData->is_payment_authrize = $weeklyRequest->is_payment_authrize;
            $permitData->session_id = $weeklyRequest->session_id;
            $permitData->permit_type_name = $weeklyRequest->permit_type_name;
            $permitData->skidata_id = $weeklyRequest->skidata_id;
            $permitData->skidata_value = $weeklyRequest->skidata_value;
            $permitData->acknowledge = $weeklyRequest->acknowledge;
            $permitData->facility_zone_id = $weeklyRequest->facility_zone_id;
            $permitData->desired_start_date = $weeklyRequest->desired_start_date;
            $permitData->desired_end_date = $weeklyRequest->desired_end_date;
            $permitData->cancelled_at = $weeklyRequest->cancelled_at;
            $permitData->created_at = $permitRenewDate;
            $permitData->updated_at = $permitRenewDate;
            $permitData->deleted_at = $weeklyRequest->deleted_at;

            $permitData->hid_card_number = $weeklyRequest->hid_card_number;
            $permitData->account_name = $weeklyRequest->account_name;
            $permitData->permit_final_amount = $weeklyRequest->permit_final_amount;
            $permitData->user_remark = $weeklyRequest->user_remark;
            $permitData->user_type_id = $weeklyRequest->user_type_id;
            $permitData->is_antipass_enabled = $weeklyRequest->is_antipass_enabled;
            $permitData->admin_user_id = $weeklyRequest->admin_user_id;
            $permitData->discount_amount = $weeklyRequest->discount_amount;
            $permitData->promocode = $weeklyRequest->promocode;
            $permitData->negotiated_amount = $weeklyRequest->negotiated_amount;
            $permitData->processing_fee         = $weeklyRequest->processing_fee;
            $permitData->refund_amount          = $weeklyRequest->refund_amount;
            $permitData->refund_type            = $weeklyRequest->refund_type;
            $permitData->refund_remarks         = $weeklyRequest->refund_remarks;
            $permitData->refund_date            = $weeklyRequest->refund_date;
            $permitData->refund_by              = $weeklyRequest->refund_by;
            $permitData->refund_transaction_id  = $weeklyRequest->refund_transaction_id;
            $permitData->refund_status          = $weeklyRequest->refund_status;
            $permitData->business_id            = $weeklyRequest->business_id;
            $permitData->renew_type             = $weeklyRequest->renew_type;
            $permitData->service_prorate        = $weeklyRequest->service_prorate;
            $permitData->permit_prorate         = $weeklyRequest->permit_prorate;
            $permitData->grace_end_date         = $weeklyRequest->grace_end_date; #pims-14610
            $permitData->net_parking_amount     = $weeklyRequest->net_parking_amount; #pims-14610
            $permitData->additional_fee         = $monthlyRequest->additional_fee;
            $permitData->surcharge_fee          = $monthlyRequest->surcharge_fee;
            $permitData->tax_fee                = $monthlyRequest->tax_fee;

            $permitData->save();
        }
        return true;
    }
}
