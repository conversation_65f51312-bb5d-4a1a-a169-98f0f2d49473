<?php

namespace App\Console\Commands\Usm;

use Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\PermitRateCriteriaMapping;
use App\Models\PermitRateCriteria;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Classes\DatacapPaymentGateway;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\HeartlandPaymentProfile;

/**
 * Emails reservation stub to user
 */
class PermitInviteMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'usm:permit-invite {partnerId?} {emailID?} {userName?} {permitType?}';
 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'permit invite email.';

	const PARTNER_ID = '363361';
	const FACILITY_ID = '407'; 
	
    protected  $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/usm/')->createLogger('permit-invite');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try{ 
           
            $partnerIdArg = $this->argument('partnerId');
            $emailId = $this->argument('emailID');
            $userName = $this->argument('userName');
            $permitType = $this->argument('permitType');
            if($partnerIdArg){
                $partnerId = $this->argument('partnerId');
            }else{
                $partnerId = self::PARTNER_ID;
            }
			       
		    $count = 0;

            $view = 'usm.permit-invite';
            $subject = "Important Notice: Permit invitation";     
            $partner_name = "<EMAIL>";  
            $brand_setting = BrandSetting::where('user_id', $partnerId)->first();
            $partnerDetails = User::where('id',$partnerId)->first();        
              
            $view_text=$view.'-plain-text';

            if(!View::exists($view_text))
            {
                $view_text=$view;
            }
                    try{ 
                        $this->log->info("Permit Invite email sending: $emailId");
                        Mail::send(
                            ['text'=>$view_text,'html'=>$view],
                            ['brand_setting' => $brand_setting,'partner_details' => $partnerDetails,'partner_name'=>$partner_name,'userName'=>$userName,'permitType'=>$permitType],
                            function ($message) use ($emailId,$subject) {
                                $message->to($emailId)->subject($subject);
                                $message->from(config('parkengage.default_sender_email'));
                            }
                        );             
                        $this->log->info("email sent: $emailId");
                    }catch (\Exception $e)
                    {
                        $errorMessage=array();
                        $errorMessage['message']=$e;
                        $this->log->error('Issue in email sending:',$errorMessage);
                    }
			        $count++;

		  
			
        }catch(Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
			
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
        
    }

    public function getDaySufixFormat($date){
        $day = date('d', strtotime($date));
        $monthYear = date('M, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
          $second_last_digit = substr($number, -2, 1);
          $suffix = 'th';
          if ($second_last_digit != '1')
          {
            switch ($last_digit)
            {
              case '1':
                $suffix = 'st';
                break;
              case '2':
                $suffix = 'nd';
                break;
              case '3':
                $suffix = 'rd';
                break;
              default:
                break;
            }
          }
          if ((string) $number === '1') $suffix = 'st';
         // return $number.$suffix . ' '.$monthYear;
          return $number. ' '.$monthYear;
    }

    private function formatPermitServiceCriteria($services)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        
		$i=0;
		foreach ($services as $service) {
			if(count($service->permit_service_criteria_mappings) > 0){
				$formatted = [];
				foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
					$item = $permit_service_criteria_mapping->criteria;
					$days = explode(',', $item->days);
					sort($days); // Sort days to match the sequence of $allDays
					if ($days == $allDays) {
						$dayNamesStr = 'All Days';
					} else {
						$dayNames = array_map(function($day) use ($daysMap) {
							return $daysMap[$day];
						}, $days);
						$dayNamesStr = implode(',', $dayNames);
					}
	
					$entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
					$entry_time_end = date('h:iA', strtotime($item->entry_time_end));
					$exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));
			
					// Adjust exit time if it's greater than 24 hours
					$exit_time = explode(":",$item->exit_time_end);
					if($exit_time[0]>23){
						$next_hr = $exit_time[0] - 24;
						$item->exit_time_end = $next_hr.":".$exit_time[1].":".$exit_time[2];
						$exit_time_end = date('h:iA', strtotime($item->exit_time_end));
						$exit_time_overflow = ' (next day)';
					}else{
						$exit_time_overflow = '';
						$exit_time_end = date('h:iA', strtotime($item->exit_time_end));
					}
	
			
					$formatted[] = [
						'days' => $dayNamesStr,
						'entry_time_begin' => $entry_time_begin,
						'entry_time_end' => $entry_time_end,
						'exit_time_begin' => $exit_time_begin,
						'exit_time_end' => $exit_time_end . $exit_time_overflow,
					];
				}
				$services[$i]->criteria=$formatted;
			}
			$i++;
		}

        return $services;
    }

    #add parking time in email
    private function formatPermitRateCriteria($criteria)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        $formatted = [];

        foreach ($criteria as $item) {
            $days = explode(',', $item->days);
            sort($days); // Sort days to match the sequence of $allDays
            if ($days == $allDays) {
                $dayNamesStr = 'All Days';
            } else {
                $dayNames = array_map(function($day) use ($daysMap) {
                    return $daysMap[$day];
                }, $days);
                $dayNamesStr = implode(',', $dayNames);
            }

            $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
            $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
            $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));
    
            // Adjust exit time if it's greater than 24 hours
            $exit_time = explode(":",$item->exit_time_end);
            if($exit_time[0]>23){
                $next_hr = $exit_time[0] - 24;
                $item->exit_time_end = $next_hr.":".$exit_time[1].":".$exit_time[2];
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                $exit_time_overflow = ' (next day)';
            }else{
                $exit_time_overflow = '';
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
            }

    
            $formatted[] = [
                'days' => $dayNamesStr,
                'entry_time_begin' => $entry_time_begin,
                'entry_time_end' => $entry_time_end,
                'exit_time_begin' => $exit_time_begin,
                'exit_time_end' => $exit_time_end . $exit_time_overflow,
            ];
        }

        return $formatted;
    }
    #add parking time in email end

    public function cancelSkiData($permit_id)
	{

		try {

			$this->log->info("Get Permit Request id for cancel SkiData " . json_encode($permit_id));
			$permit = PermitRequest::with(['facility'])->where("id", $permit_id)->first();
			//$guarage_code = $permit->facility->garage_code;
			$guarage_code = config('parkengage.SKIDATA_GUARAGE');
			$skidata_id =  $permit->skidata_id;
			$skiDataAuth = config('parkengage.SKIDATA_USERNAME') . ":" . config('parkengage.SKIDATA_PASSWORD');
			$dataAuthEncrption = base64_encode($skiDataAuth);
			$headers = [
				'Authorization: Basic ' . $dataAuthEncrption,
				'Content-Type: application/json',
			];
			$curl = curl_init();
			$url = config('parkengage.SKIDATA_CREATE_URL') . $guarage_code . '/' . $skidata_id;
			curl_setopt($curl, CURLOPT_URL, $url);
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
			curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
			$response = curl_exec($curl);
			curl_close($curl);
			$response = json_decode($response);
			$this->log->info("SkIData success completed.");
			return $response;
		} catch (\Exception $e) {
			$msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
			$this->log->error($msg);
		}
	}
}
