<?php

namespace App\Console\Commands\Usm;

use Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\PermitRateCriteriaMapping;
use App\Models\PermitRateCriteria;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Classes\DatacapPaymentGateway;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\UserPass;

/**
 * Emails reservation stub to user
 */
class MassEmailPermit extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'usm:permit-mail-send {partnerId?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'mass send permit email.';

    // const PARTNER_ID = '19349';
    // const FACILITY_ID = '167';

    public $log;
    protected $partnerId;
    protected $facilityId;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/usm/')->createLogger('mass-email');
        $this->partnerId = config('parkengage.PARTNER_USM');
        // $this->facilityId = config('parkengage.USM_FACILITY');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {

            $partnerIdArg = $this->argument('partnerId');
            if ($partnerIdArg) {
                $partnerId = $this->argument('partnerId');
            } else {
                $partnerId = $this->partnerId;
            }
            // ->where('user_consent',1)
            //dd($permit_start_date,$permit_end_date,$partnerId); 
            $permit_end_date = '2025-01-23';

            $monthlyRequest = PermitRequest::select(['email'])->where('partner_id', $partnerId)->whereDate('desired_end_date', '>=', $permit_end_date)->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')
                ->whereNull('business_id')->groupBy('email')
                ->offset(5600)->limit(1000)->get();

            /*
			 $monthlyRequest = UserPass::select(['email'])->where('partner_id',$partnerId)->whereDate('end_date', '>=', $permit_end_date)->whereNull('cancelled_at')
			 //->whereNotNull('anet_transaction_id')
			->groupBy('email')
            ->offset(0)->limit(1000)
			->get();
		    */
            // dd($permit_end_date,count($monthlyRequest),$monthlyRequest[0]->email,$partnerId); 
            if (!$monthlyRequest) {
                $this->log->info("Monthly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($monthlyRequest));

            $count = 0;
            foreach ($monthlyRequest as $key => $val) {

                $brand_setting = BrandSetting::where('user_id', $partnerId)->first();
                $partnerDetails = User::where('id', $partnerId)->first();
                $view = 'usm.permit-mass-email';
                $subject =  "Temporary Closure of Lot G8";
                $partner_name = "USM Parking Services";

                $view_text = $view . '-plain-text';

                if (!View::exists($view_text)) {
                    $view_text = $view;
                }
                try {
                    $this->log->info("Permit Renew Reminder email sending: $val->email");
                    Mail::send(
                        ['text' => $view_text, 'html' => $view],
                        ['data' => $val, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                        function ($message) use ($val, $subject) {
                            $message->to($val->email)->subject($subject);
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("email sent: $val->email");
                } catch (\Exception $e) {
                    $errorMessage = array();
                    $errorMessage['message'] = $e;
                    $this->log->error('Issue in email sending:', $errorMessage);
                }
                $count++;
            }
            $msg = "Total Count of Permit Renew Reminder: " . $count;
            $this->log->info($msg);
            return $msg;
        } catch (Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();

            $this->log->error($msg);
            $this->log->info("Queue ended");
        }
    }
}
