<?php

namespace App\Console\Commands\Usm;

use Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\PermitRateCriteriaMapping;
use App\Models\PermitRateCriteria;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Classes\DatacapPaymentGateway;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Http\Helpers\MailHelper;


/**
 * Emails reservation stub to user
 */
class PermitCancelMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'usm:permit-cancel {partnerId?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly permit renew reminder email.';

    // const PARTNER_ID = '19349';
    // const FACILITY_ID = '167';

    public $log;
    protected $partnerId;
    protected $facilityId;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/usm/')->createLogger('cancel-reminder');
        $this->partnerId = config('parkengage.PARTNER_USM');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {

            $permit_end_date = date('Y-m-d', strtotime('last day of previous month'));
            $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));

            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F", $time);
            $PermitRenewYear = date("Y", $time);
            $PermitRenewDay = date("d", $time);

            $partnerIdArg = $this->argument('partnerId');
            if ($partnerIdArg) {
                $partnerId = $this->argument('partnerId');
            } else {
                $partnerId = $this->partnerId;
            }

            /*
			$permit_end_date = '2024-08-31';
            $permit_start_date = '2024-08-01';
			
			$permit_end_date = '2024-07-31';
            $permit_start_date = '2024-07-01';
			*/

            $permit_end_date = '2025-01-31';
            $permit_start_date = '2025-01-01';


            // ->where('user_consent',1)
            //dd($permit_start_date,$permit_end_date,$partnerId);
            //->whereIn('account_number', [********, ********, ********, ********, ********])->limit(5)
            $monthlyRequest = PermitRequest::with(['user', 'PermitVehicle'])->where('partner_id', $partnerId)->whereDate('desired_end_date', '<=', $permit_end_date)->whereDate('desired_start_date', '>=', $permit_start_date)->whereNull('cancelled_at')->whereNull('deleted_at')->whereNotNull('anet_transaction_id')
                ->whereNull('business_id')
                ->whereNotIn('permit_rate_id', [341, 342])
                ->whereIn('account_number', [********, ********, ********])
                ->orderBy('id', 'desc')
                ->limit(50)
                ->get();
            //dd($monthlyRequest[0]->account_number,$monthlyRequest[1]->account_number,$monthlyRequest[2]->account_number,count($monthlyRequest));	
            // dd($partnerId,$permit_end_date,$permit_start_date,count($monthlyRequest),$monthlyRequest[0]->account_number,$monthlyRequest[1]->account_number); 
            if (!$monthlyRequest) {
                $this->log->info("Monthly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($monthlyRequest));

            $count = 0;
            foreach ($monthlyRequest as $key => $val) {
                $permitRate = PermitRate::where('id', $val->permit_rate_id)->first();
                //dd($permitRate->rate);				
                $this->log->info("Permit Rate Found" . json_encode($permitRate));

                if ($permitRate) {
                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                    $partnerDetails = User::where('id', $partnerId)->first();
                    #PIMS-10032 dushyant phase2
                    if (is_null($val->business_id) || empty($val->business_id)) {
                    } else {
                        $new_permit_amt = ($val->negotiated_amount ?? $val->permit_final_amount);
                        $val->permit_rate = $new_permit_amt;
                    }
                    //   $val->desired_start_date = $this->getDaySufixFormat($val->desired_start_date);
                    //  $val->desired_end_date = $this->getDaySufixFormat($val->desired_end_date);

                    $permit_services = PermitRequestServiceMapping::where('permit_request_id', $val->id)->pluck('permit_service_id');

                    $services  = PermitServices::with([
                        'permit_service_criteria_mappings' => function ($query) use ($permit_services) {
                            $query->whereIn('permit_service_id', $permit_services)
                                ->with('criteria');
                        }
                    ])
                        ->select('permit_services.*')
                        ->whereIn('id', $permit_services)
                        ->orderBy('permit_services.id', 'asc')
                        ->get();

                    if (count($services) > 0) {
                        $services = $this->formatPermitServiceCriteria($services);
                    }
                } else {
                    $this->log->info("Permit Rate Not Found");
                }
                $view = 'usm.permit-cancel-reminder';
                if ($val->skidata_id != '') {
                    if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                        $this->cancelSkiData($val->id);
                    }
                }

                $val->cancelled_at = date("Y-m-d H:i:s");
                $val->status = 0;
                $val->user_consent = 0;
                $val->save();

                if ($val->partner_id == config('parkengage.PARTNER_UNITED')) {
                    // $view='united.due-reminder';
                    $subject = "Important Notice: Permit Cancellation";
                    $partner_name = "United Parking at ************";
                } else if ($val->partner_id == config('parkengage.PARTNER_USM')) {
                    //    $view='usm.due-reminder';
                    $subject = "Important Notice: Permit Cancellation";
                    $partner_name = "USM Parking Services";
                } else if ($val->partner_id == config('parkengage.PARTNER_MANLO')) {     #pims-12258 dd
                    $subject = "Important Notice: Permit Cancellation";
                    $partner_name = "MENLO Parking Services";
                } else if ($val->partner_id == config('parkengage.PARTNER_YANKEE')) {
                    //    $view='yenkee.due-reminder';
                    $subject = "Important Notice: Permit Cancellation";
                    $partner_name = $partnerDetails->company_name;
                } else if ($val->facility_id == config('parkengage.WAILUKU_FACILITY')) {
                    //   $view='are.due-reminder';
                    $subject = "Important Notice: Permit Cancellation";
                    $partner_name = "Diamond Parking at (************* or email <NAME_EMAIL>";
                } else if ($val->facility_id != config('parkengage.WAILUKU_FACILITY') && $val->partner_id == config('parkengage.PARTNER_DIAMOND')) {
                    //  $view='are-diamond.due-reminder';  
                    $subject = "Important Notice: Permit Cancellation";
                    $partner_name = "Diamond Parking at (************* or email <NAME_EMAIL>";
                } else {
                    //  $view='yenkee.due-reminder';
                    $subject = "Important Notice: Permit Cancellation";
                    $partner_name = $partnerDetails->company_name;
                }

                if ($val->partner_id == config('parkengage.PARTNER_INTRAPARK')) {

                    //  $view='intrapark.due-reminder';
                    $subject = "Important Notice: Permit Cancellation";
                    $partner_name = "Intrapark Parking";
                }
                if ($val->partner_id == config('parkengage.PARTNER_PAVE')) {
                    //  $view='pave-permit.due-reminder';
                    $subject = "Important Notice: Permit Cancellation";
                    $partner_name = "Parking Payments";
                }

                if ($val->partner_id == config('parkengage.PARTNER_PCI')) {
                    //  $view='kstreet.due-reminder';    
                    $subject = "Urgent: Permit Payment Past Due";
                    $partner_name = "Parking Concepts";
                }

                $view_text = $view . '-plain-text';

                if (!View::exists($view_text)) {
                    $view_text = $view;
                }
                try {
                    $this->log->info("Permit Renew Reminder email sending: $val->email");
                    MailHelper::sendEmail($val->email, $view_text, ['subject' => $subject, 'data' => $val, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'services' => $services, 'partner_name' => $partner_name], $val->partner_id);

                    // Mail::send(
                    //     ['text' => $view_text, 'html' => $view],
                    //     ['data' => $val, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'services' => $services, 'partner_name' => $partner_name],
                    //     function ($message) use ($val, $subject) {
                    //         $message->to($val->email)->subject($subject);
                    //         $message->from(config('parkengage.default_sender_email'));
                    //     }
                    // );
                    $this->log->info("email sent: $val->email");
                } catch (\Exception $e) {
                    $errorMessage = array();
                    $errorMessage['message'] = $e;
                    $this->log->error('Issue in email sending:', $errorMessage);
                }
                $count++;
            }
            $msg = "Total Count of Permit Renew Reminder: " . $count;
            $this->log->info($msg);
            return $msg;
        } catch (Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();

            $this->log->error($msg);
            $this->log->info("Queue ended");
        }
    }

    public function getDaySufixFormat($date)
    {
        $day = date('d', strtotime($date));
        $monthYear = date('M, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
        $second_last_digit = substr($number, -2, 1);
        $suffix = 'th';
        if ($second_last_digit != '1') {
            switch ($last_digit) {
                case '1':
                    $suffix = 'st';
                    break;
                case '2':
                    $suffix = 'nd';
                    break;
                case '3':
                    $suffix = 'rd';
                    break;
                default:
                    break;
            }
        }
        if ((string) $number === '1') $suffix = 'st';
        // return $number.$suffix . ' '.$monthYear;
        return $number . ' ' . $monthYear;
    }

    private function formatPermitServiceCriteria($services)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);

        $i = 0;
        foreach ($services as $service) {
            if (count($service->permit_service_criteria_mappings) > 0) {
                $formatted = [];
                foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
                    $item = $permit_service_criteria_mapping->criteria;
                    $days = explode(',', $item->days);
                    sort($days); // Sort days to match the sequence of $allDays
                    if ($days == $allDays) {
                        $dayNamesStr = 'All Days';
                    } else {
                        $dayNames = array_map(function ($day) use ($daysMap) {
                            return $daysMap[$day];
                        }, $days);
                        $dayNamesStr = implode(',', $dayNames);
                    }

                    $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
                    $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
                    $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

                    // Adjust exit time if it's greater than 24 hours
                    $exit_time = explode(":", $item->exit_time_end);
                    if ($exit_time[0] > 23) {
                        $next_hr = $exit_time[0] - 24;
                        $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                        $exit_time_overflow = ' (next day)';
                    } else {
                        $exit_time_overflow = '';
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                    }


                    $formatted[] = [
                        'days' => $dayNamesStr,
                        'entry_time_begin' => $entry_time_begin,
                        'entry_time_end' => $entry_time_end,
                        'exit_time_begin' => $exit_time_begin,
                        'exit_time_end' => $exit_time_end . $exit_time_overflow,
                    ];
                }
                $services[$i]->criteria = $formatted;
            }
            $i++;
        }

        return $services;
    }

    #add parking time in email
    private function formatPermitRateCriteria($criteria)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        $formatted = [];

        foreach ($criteria as $item) {
            $days = explode(',', $item->days);
            sort($days); // Sort days to match the sequence of $allDays
            if ($days == $allDays) {
                $dayNamesStr = 'All Days';
            } else {
                $dayNames = array_map(function ($day) use ($daysMap) {
                    return $daysMap[$day];
                }, $days);
                $dayNamesStr = implode(',', $dayNames);
            }

            $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
            $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
            $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

            // Adjust exit time if it's greater than 24 hours
            $exit_time = explode(":", $item->exit_time_end);
            if ($exit_time[0] > 23) {
                $next_hr = $exit_time[0] - 24;
                $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                $exit_time_overflow = ' (next day)';
            } else {
                $exit_time_overflow = '';
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
            }


            $formatted[] = [
                'days' => $dayNamesStr,
                'entry_time_begin' => $entry_time_begin,
                'entry_time_end' => $entry_time_end,
                'exit_time_begin' => $exit_time_begin,
                'exit_time_end' => $exit_time_end . $exit_time_overflow,
            ];
        }

        return $formatted;
    }
    #add parking time in email end

    public function cancelSkiData($permit_id)
    {

        try {

            $this->log->info("Get Permit Request id for cancel SkiData " . json_encode($permit_id));
            $permit = PermitRequest::with(['facility'])->where("id", $permit_id)->first();
            //$guarage_code = $permit->facility->garage_code;
            $guarage_code = config('parkengage.SKIDATA_GUARAGE');
            $skidata_id =  $permit->skidata_id;
            $skiDataAuth = config('parkengage.SKIDATA_USERNAME') . ":" . config('parkengage.SKIDATA_PASSWORD');
            $dataAuthEncrption = base64_encode($skiDataAuth);
            $headers = [
                'Authorization: Basic ' . $dataAuthEncrption,
                'Content-Type: application/json',
            ];
            $curl = curl_init();
            $url = config('parkengage.SKIDATA_CREATE_URL') . $guarage_code . '/' . $skidata_id;
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            $response = curl_exec($curl);
            curl_close($curl);
            $response = json_decode($response);
            $this->log->info("SkIData success completed.");
            return $response;
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
        }
    }
}
