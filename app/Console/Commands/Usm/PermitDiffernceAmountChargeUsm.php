<?php

namespace App\Console\Commands\Usm;

use Illuminate\Support\Facades\Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Classes\DatacapPaymentGateway;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\HeartlandPaymentProfile;

/**
 * Emails reservation stub to user
 */
class PermitDiffernceAmountChargeUsm extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'usm:permit-diff-charge';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'permit difference charge.';

    public $log;
    protected $partnerId;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/usm/')->createLogger('permitDiffCharge');
        $this->partnerId = config('parkengage.PARTNER_USM');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $day_of_month = date('d');
            //	dd($day_of_month);		
            $permit_start_date = date('Y-m-d', strtotime('first day of this month'));
            $permit_end_date = date('Y-m-d', strtotime('2026-06-30'));

            //dd($permit_start_date,$permit_end_date);
            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F", $time);
            $PermitRenewYear = date("Y", $time);
            $PermitRenewDay = date("d", $time);


            $permitRequest = PermitRequest::with(['facility.FacilityPaymentDetails', 'user', 'transaction'])
                ->where('partner_id', $this->partnerId)
                ->whereDate('desired_end_date', '<=', $permit_end_date)
                ->whereDate('desired_start_date', '>=', $permit_start_date)
                ->whereNull('cancelled_at')->whereNull('deleted_at')
                ->where('user_consent', 1)
                ->whereColumn('permit_final_amount', '<', 'permit_rate')
                ->where('discount_amount', '0')
                ->whereDate('created_at', '=', $permit_start_date)
                ->whereNotNull('anet_transaction_id')
                ->orderBy('id', 'desc')
                ->limit(1)->get();
            //dd(count($permitRequest));
            //dd($permit_start_date, $permit_end_date, $permitRequest[0]->account_number);

            if (!$permitRequest) {
                $this->log->info("Monthly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($permitRequest));
            // Create Permit Request History
            $count = 0;
            foreach ($permitRequest as $key => $val) {

                $permitRate = PermitRate::where('id', $val->permit_rate_id)->first();
                $this->log->info("Permit Rate Found" . json_encode($permitRate));

                if ($permitRate) {
                    $processing_fee = $val->facility->permit_processing_fee;
                    $permit_rate = $permitRate->rate;
                    $final_amount = $processing_fee + $permit_rate;


                    $permit_services = PermitRequestServiceMapping::where('permit_request_id', $val->id)->pluck('permit_service_id');

                    $services  = PermitServices::with([
                        'permit_service_criteria_mappings' => function ($query) use ($permit_services) {
                            $query->whereIn('permit_service_id', $permit_services)
                                ->with('criteria');
                        }
                    ])
                        ->select('permit_services.*')
                        ->whereIn('id', $permit_services)
                        ->orderBy('permit_services.id', 'asc')
                        ->get();

                    if ($services) {

                        foreach ($services as $permitService) {
                            $final_amount += (float) $permitService->permit_service_rate;
                        }
                    }

                    if (count($services) > 0) {
                        $services = $this->formatPermitServiceCriteria($services);
                    }

                    $charged_amount = $val->transaction->total;
                    $diffAmount = $final_amount - $charged_amount;
                    //dd($final_amount,$charged_amount,$diffAmount);
                    $this->log->info("Payment Total Amount  --" . $final_amount);
                    $this->log->info("Payment Charged Amount --" . $charged_amount);
                    $this->log->info("Payment Amount For charged --" . $diffAmount);

                    $amount = ($val->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $diffAmount;

                    if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                        dd('stop Planet');
                        $paymentProfile = PlanetPaymentProfile::where('user_id', $val->user_id)->first();
                        $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                        $amount = ($val->facility->FacilityPaymentDetails->planet_payment_env == 'test') ? '3.00' : $permitRate->rate;
                        if ($paymentProfile) {
                            $val->session_id = $paymentProfile->token;
                            $paymentByToken = PlanetPaymentGateway::planetPaymentByToken($val, $amount, '');
                            $this->log->info("planet permit Payment Response :" . json_encode($paymentByToken));
                            if (!in_array($paymentByToken["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                $this->log->info("planet Payment Failed Response :" . json_encode($paymentByToken));

                                $val->transaction_retry   = $val->transaction_retry + 1;
                                $val->save();
                                $expiry    = $paymentProfile->expiry;
                                if ($expiry) {
                                    $expiry_data = str_split($expiry, 2);
                                    $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                } else {
                                    $val->card_expiry = "-";
                                }
                                $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                if ($facility_brand_setting) {
                                    $val->facility_logo_id = $facility_brand_setting->id;
                                } else {
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $val->logo_id  = $brand_setting->id;
                                }
                                $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                if ($val->transaction_retry > '1') {
                                    $view = 'usm.permit-cancel-reminder';
                                }
                                $view = "usm.permit-renew-fail";
                                $partnerDetails = User::where('id', $val->partner_id)->first();
                                $partner_name = "USM Parking Services";

                                Mail::send(
                                    $view,
                                    ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                    function ($message) use ($val) {
                                        $message->to([$val->user->email])->subject("Important: Action Required for Your Permit Payment");
                                        $message->from(config('parkengage.default_sender_email'));
                                    }
                                );
                                $this->log->info("Mail sent to " . $val->user->email);
                            } else {
                                $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($paymentByToken, $val->user_id);
                                $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($planetTransaction));
                                $val->anet_transaction_id = $planetTransaction->id;
                                $maxDays = date('t');
                                $val->no_of_days          = $maxDays;
                                $val->anet_transaction_id = $planetTransaction->id;
                                $val->permit_rate         = $permitRate->rate;
                                $val->desired_start_date  = $desired_start_date;
                                $val->desired_end_date    = $desired_end_date;
                                $val->transaction_retry   = $val->transaction_retry + 1;
                                $val->save();
                                //user mail
                                $val->card_last_four = $paymentProfile->card_last_four;
                                $val->card_name = $paymentProfile->card_name;
                                $expiry    = $paymentProfile->expiry;
                                if ($expiry) {
                                    $expiry_data = str_split($expiry, 2);
                                    $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                } else {
                                    $val->card_expiry = "-";
                                }
                                $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                if ($facility_brand_setting) {
                                    $val->facility_logo_id = $facility_brand_setting->id;
                                } else {
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $val->logo_id  = $brand_setting->id;
                                }
                                $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                $partnerDetails = User::where('id', $val->partner_id)->first();
                                $partner_name = "USM Parking Services";
                                $view = "usm.permit-renew";
                                Mail::send(
                                    $view,
                                    ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                    function ($message) use ($val) {
                                        $message->to($val->user->email)->subject("Payment made successfully against Permit #" . $val->account_number);
                                        $message->from(config('parkengage.default_sender_email'));
                                    }
                                );
                                $this->log->info("Mail sent to " . $val->user->email);
                            }
                        } else {
                            $this->log->info("Payment Profile not found");
                        }
                    } else if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                        dd('stop Datacap');
                        $amount = ($val->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $permitRate->rate;
                        $ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $val->facility->FacilityPaymentDetails->datacap_script_url;

                        $paymentProfile = DatacapPaymentProfile::where('user_id', $val->user_id)->first();
                        $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                        //dd($amount,$ecommerce_mid,$url,$paymentProfile);
                        if ($paymentProfile) {
                            $data['Token'] = $paymentProfile->token;
                            if ($amount > 0) {
                                // $amount = number_format($amount, 2);
                                $data['Amount'] = $amount;
                                $data['Token'] = $paymentProfile->token;
                                $data['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                $data["CardHolderID"] = "Allow_V2";
                                $this->log->info("Payment Request Data --" . json_encode($data) . "--" . json_encode($ecommerce_mid) . "--" . json_encode($url));
                                $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                if ($paymentResponse["Status"] == "Error") {
                                    $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                    if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                        $this->log->info("Invalid payment information. Please verify and try again or use another card ." . json_encode($paymentResponse));
                                    } else {
                                        $this->log->info("Invalid payment information. Please verify and try again or use another card." . json_encode($paymentResponse));
                                    }
                                } else if ($paymentResponse["Status"] == "Declined") {
                                    // Failure mail send to user
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $view = "usm.permit-renew-fail";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting],
                                        function ($message) use ($val) {
                                            $message->to([$val->user->email])->subject("Your Permit #" . $val->account_number . "  could not be renewed due to Payment Failure");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                                if ($paymentResponse['Status'] == 'Approved') {
                                    $this->log->info("Payment Transaction Data Datacap Payment -- " . json_encode($paymentResponse));

                                    $request = new Request([
                                        'total'   => $amount,
                                        'card_last_four' => $paymentProfile->card_last_four,
                                        'expiration' => $paymentProfile->expiry
                                    ]);
                                    $this->log->info("Save Transaction Data Request --" . json_encode($request) . "--" . json_encode($val->user_id));
                                    $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $val->user_id, '');
                                    $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($authorized_anet_transaction));
                                    $maxDays = date('t');
                                    $val->no_of_days          = $maxDays;
                                    $val->anet_transaction_id = $authorized_anet_transaction->id;
                                    $val->permit_rate         = $permitRate->rate;
                                    $val->desired_start_date  = $desired_start_date;
                                    $val->desired_end_date    = $desired_end_date;
                                    $val->save();
                                    //user mail
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $view = "usm.permit-renew";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting],
                                        function ($message) use ($val) {
                                            $message->to($val->user->email)->subject("Your Permit #" . $val->account_number . " has been Renewed Successfully");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                            }
                        } else {
                            $this->log->info("Payment Profile not found");
                        }
                    } else if (isset($val->facility->FacilityPaymentDetails) && ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {

                        $paymentProfile = HeartlandPaymentProfile::where('user_id', $val->user_id)->first();
                        //   dd($val->user_id,$val->id,$val->email,$paymentProfile->token);
                        $this->log->info("Payment Profile Data --" . $val->account_number . "--" . json_encode($paymentProfile));
                        $this->log->info("Permit final Amount and charge Amount Data --" . $val->account_number . "--final Payment--" . $final_amount . "--Charge Amount" . $amount);

                        if ($paymentProfile) {
                            $request = new Request([
                                'Amount'   => $amount,
                                'total'   => $amount,
                                'token' => $paymentProfile->token,
                                'zipcode' => $paymentProfile->zipcode,
                                'card_last_four' => $paymentProfile->card_last_four,
                                'expiration_date' => $paymentProfile->expiry,
                                'original_total' => $final_amount
                            ]);


                            #end add parking time in email
                            $permit_validity = '';

                            if ($permitRate) {
                                $permitRateDescHour = PermitRateDescription::find($permitRate->permit_rate_description_id);
                                if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "1")) {
                                    $permit_validity = '1 Month';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "2")) {
                                    $permit_validity = '2 Month';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "3")) {
                                    $permit_validity = 'Quarterly';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "4")) {
                                    $permit_validity = '4 Month';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "5")) {
                                    $permit_validity = '5 Month';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "6")) {
                                    $permit_validity = 'Half Yearly';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "7")) {
                                    $permit_validity = '7 Month';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "8")) {
                                    $permit_validity = '8 Month';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "9")) {
                                    $permit_validity = '9 Month';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "10")) {
                                    $permit_validity = '10 Month';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "11")) {
                                    $permit_validity = '11 Month';
                                } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "12")) {
                                    $permit_validity = '1 Year';
                                } else {
                                    $permit_validity = '1 Month';
                                }
                            }
                            $paymentResponse = '---';
                            try {
                                $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $val->facility);
                            } catch (Exception $e) {
                                $this->log->info("Payment Charge Error Response Permit Id #:" . $val->account_number . "--" . json_encode($e->getMessage()));
                                if (str_contains($e->getMessage(), 'duplicate')) {
                                }
                            }

                            $this->log->info("Heartland Payment Response to Permit Id #:" .  $val->account_number . "--" . json_encode($paymentResponse));

                            if (isset($paymentResponse->responseMessage) && $paymentResponse->responseMessage == 'APPROVAL') {
                                $this->log->info("Heartland Payment Approve Response to Permit Id #:" .  $val->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode . "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId . "-- HL Gateway Response" . json_encode($paymentResponse));


                                $user_id = $val->user_id;
                                $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $user_id);
                                $this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($authorized_anet_transaction));

                                $val->anet_transaction_id = $authorized_anet_transaction->id;
                                $val->permit_final_amount = $final_amount;
                                $val->transaction_retry   = $val->transaction_retry + 1;
                                $val->save();
                                //user mail

                                $expiry    = $paymentProfile->expiry;
                                if ($expiry) {
                                    $expiry_data = str_split($expiry, 2);
                                    $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                } else {
                                    $paymentProfile->card_expiry = "-";
                                }
                                $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                if ($facility_brand_setting) {
                                    $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                } else {
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $paymentProfile->logo_id  = $brand_setting->id;
                                }
                                $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                $partnerDetails = User::where('id', $val->partner_id)->first();
                                $partner_name = "USM Parking Services";
                                $view = "usm.permit-additional-charge";
                                $val->diffAmount = $diffAmount;
                                $val->charged_amount = $charged_amount;

                                Mail::send(
                                    $view,
                                    ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                    function ($message) use ($val) {
                                        $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #" . $val->account_number);
                                        $message->from(config('parkengage.default_sender_email'));
                                    }
                                );
                                $this->log->info("Mail sent to " . $val->user->email);
                            } else {
                                $this->log->info("Heartland Payment Failed Response to Permit Id #:" .  $val->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode . "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId . "-- HL Gateway Response" . json_encode($paymentResponse));

                                /*
                                $val->transaction_retry   = $val->transaction_retry + 1;
                                $val->save();
                                // Failure mail send to user
                                $expiry    = $paymentProfile->expiry;
                                if ($expiry) {
                                    $expiry_data = str_split($expiry, 2);
                                    $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                } else {
                                    $paymentProfile->card_expiry = "-";
                                }
                                $day_of_month = date('d');
                                if ($day_of_month == '06') {

                                    $view = 'usm.permit-cancel-reminder';
                                    $subject = "Important Notice: Permit Cancellation";

                                    $val->status = '0';
                                    $val->user_consent = '0';
                                    $val->cancelled_at =  $permitRenewDate;
                                    $val->save();
                                } else {
                                    $view = "usm.permit-renew-fail";
                                    $subject = "Important: Action Required for Your Permit Payment";
                                }

                                $partnerDetails = User::where('id', $val->partner_id)->first();
                                $partner_name = "USM Parking Services";

                                $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                if ($facility_brand_setting) {
                                    $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                } else {
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $paymentProfile->logo_id  = $brand_setting->id;
                                }
                                $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                $val->final_amount = $final_amount;

                                Mail::send(
                                    $view,
                                    ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                    function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear, $subject) {
                                        $message->to([$val->user->email])->subject($subject);
                                        $message->from(config('parkengage.default_sender_email'));
                                    }
                                );
                                $this->log->info("Mail sent to " . $val->user->email);
                                */
                            }
                        }
                    }
                } else {
                    $this->log->info("Permit Rate Not Found");
                    dd('stop');
                }
                $count++;
            }
            $msg = "Total Count of Permit: " . $count;
            $this->log->info($msg);
            return $msg;
        } catch (Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();

            $this->log->error($msg);
            $this->log->info("Queue ended");
        }
    }

    private function formatPermitServiceCriteria($services)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);

        $i = 0;
        foreach ($services as $service) {
            if (count($service->permit_service_criteria_mappings) > 0) {
                $formatted = [];
                foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
                    $item = $permit_service_criteria_mapping->criteria;
                    $days = explode(',', $item->days);
                    sort($days); // Sort days to match the sequence of $allDays
                    if ($days == $allDays) {
                        $dayNamesStr = 'All Days';
                    } else {
                        $dayNames = array_map(function ($day) use ($daysMap) {
                            return $daysMap[$day];
                        }, $days);
                        $dayNamesStr = implode(',', $dayNames);
                    }

                    $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
                    $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
                    $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

                    // Adjust exit time if it's greater than 24 hours
                    $exit_time = explode(":", $item->exit_time_end);
                    if ($exit_time[0] > 23) {
                        $next_hr = $exit_time[0] - 24;
                        $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                        $exit_time_overflow = ' (next day)';
                    } else {
                        $exit_time_overflow = '';
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                    }


                    $formatted[] = [
                        'days' => $dayNamesStr,
                        'entry_time_begin' => $entry_time_begin,
                        'entry_time_end' => $entry_time_end,
                        'exit_time_begin' => $exit_time_begin,
                        'exit_time_end' => $exit_time_end . $exit_time_overflow,
                    ];
                }
                $services[$i]->criteria = $formatted;
            }
            $i++;
        }

        return $services;
    }

    #add parking time in email
    private function formatPermitRateCriteria($criteria)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        $formatted = [];

        foreach ($criteria as $item) {
            $days = explode(',', $item->days);
            sort($days); // Sort days to match the sequence of $allDays
            if ($days == $allDays) {
                $dayNamesStr = 'All Days';
            } else {
                $dayNames = array_map(function ($day) use ($daysMap) {
                    return $daysMap[$day];
                }, $days);
                $dayNamesStr = implode(',', $dayNames);
            }

            $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
            $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
            $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

            // Adjust exit time if it's greater than 24 hours
            $exit_time = explode(":", $item->exit_time_end);
            if ($exit_time[0] > 23) {
                $next_hr = $exit_time[0] - 24;
                $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                $exit_time_overflow = ' (next day)';
            } else {
                $exit_time_overflow = '';
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
            }


            $formatted[] = [
                'days' => $dayNamesStr,
                'entry_time_begin' => $entry_time_begin,
                'entry_time_end' => $entry_time_end,
                'exit_time_begin' => $exit_time_begin,
                'exit_time_end' => $exit_time_end . $exit_time_overflow,
            ];
        }

        return $formatted;
    }
}
