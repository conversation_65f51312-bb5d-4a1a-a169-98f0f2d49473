<?php

namespace App\Console\Commands\Usm;

use Mail;
use Exception;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Models\Ticket;

class AutoCheckout extends Command
{

    protected $signature = 'usm:auto-checkout';

    protected $description = 'close all the open ticket which not closed after event permit reservation pass';

    protected $log;
    protected $partnerId;

    // const PARTNER_ID = 19349;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/parkengage/usm')->createLogger('auto-checkout');
        $this->partnerId = config('parkengage.PARTNER_USM');
    }

    public function handle()
    {
        $this->log->info("checkout start");
        try {
            $today = date("Y-m-d");
            $ticketData = Ticket::where("partner_id", $this->partnerId)
                ->where("is_checkout", "0")
                ->where("is_closed", "0");
            $ticketData = $ticketData->where(function ($query) {
                $query->orWhere("event_id", ">", 0)
                    ->orWhere("permit_request_id", ">", 0)
                    ->orWhere("user_pass_id", ">", 0)
                    ->orWhere("reservation_id", ">", 0);
            });
            $ticketData = $ticketData->get();
            if (count($ticketData) > 0) {
                $this->log->info("Before checkout email about to send");
                //user mail
                $currentDate = date("Y-m-d");
                $currentDatetime = date("Y-m-d H:i:s");
                foreach ($ticketData as $val) {
                    if ($val->event_id > 0) {
                        if (strtotime($val->estimated_checkout) <= strtotime($currentDatetime)) {
                            $status = Ticket::where(['ticket_number' => $val->ticket_number])->update(['is_checkout' => '1', 'is_closed' => '1', "checkout_remark" => "Pending checkouts; closing by Backend Process", "checkout_time" => $currentDatetime, "closed_date" => $currentDatetime]);
                            $this->log->info("Event Checkout update for Ticket No " . $val->ticket_number);
                        }
                    }
                    if ($val->reservation_id > 0) {
                        if (strtotime($val->estimated_checkout) <= strtotime($currentDatetime)) {
                            $status = Ticket::where(['ticket_number' => $val->ticket_number])->update(['is_checkout' => '1', 'is_closed' => '1', "checkout_remark" => "Pending checkouts; closing by Backend Process", "checkout_time" => $currentDatetime, "closed_date" => $currentDatetime]);
                            $this->log->info("Reservation Checkout update for Ticket No " . $val->ticket_number);
                        }
                    }
                    if ($val->user_pass_id > 0) {
                        if (strtotime($val->checkin_time) <= strtotime($currentDatetime)) {
                            $status = Ticket::where(['ticket_number' => $val->ticket_number])->update(['is_checkout' => '1', 'is_closed' => '1', "checkout_remark" => "Pending checkouts; closing by Backend Process", "checkout_time" => $currentDatetime, "closed_date" => $currentDatetime]);
                            $this->log->info("Pass Checkout update for Ticket No " . $val->ticket_number);
                        }
                    }
                    if ($val->permit_request_id > 0) {
                        if (strtotime($val->checkin_time) <= strtotime($currentDatetime)) {
                            $status = Ticket::where(['ticket_number' => $val->ticket_number])->update(['is_checkout' => '1', 'is_closed' => '1', "checkout_remark" => "Pending checkouts; closing by Backend Process", "checkout_time" => $currentDatetime, "closed_date" => $currentDatetime]);
                            $this->log->info("Permit Checkout update for Ticket No " . $val->ticket_number);
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            Mail::send(
                "mapco.alert-error-checkout",
                ['data' => $msg],
                function ($message) use ($msg) {
                    $message->to(['<EMAIL>', '<EMAIL>'])->subject("Error : USM Cron Alert Checkout");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Queue ended");
        }
    }
}
