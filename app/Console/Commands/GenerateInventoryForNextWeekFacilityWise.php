<?php
namespace App\Console\Commands;
use App\Models\Facility;
use App\Models\FacilityAvailability;
use App\Models\FacilityInventory;
use App\Models\FacilityPartnerAvailabilityCron;
use App\Models\HoursOfOperation;
use Carbon\Carbon;
use Illuminate\Console\Command;

class GenerateInventoryForNextWeekFacilityWise extends Command
{
    const DAY_START_HOUR              = 0;
    const DAY_END_HOUR                = 23;
    const NUMBER_OF_SPOTS             = 50;
    const PARTNER_SPOTS_AVAILABLE     = 1;
    const PARTNER_SPOTS_NOT_AVAILABLE = 0;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:inventory-for-next-months-ficility-wise {facility}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command should be scheduled to run on weekend to copy the current inventory into next for each facility';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {
		$facility_id = $this->argument('facility');
        Carbon::setWeekStartsAt(Carbon::SUNDAY);

        $dates = new \DatePeriod(
            new \DateTime(Carbon::now()->startOfWeek()), new \DateInterval('P1D'),
            new \DateTime(Carbon::now()->startOfWeek()->addWeeks(14)));

        Facility::where('id',$facility_id)->with('availabilities', 'inventories')->chunk(
            1, function ($facilities) use ($dates) {

            foreach ($facilities as $facility) {

                //fetch last weeks inventory of this facility
                $facilityAvailabilities     = [];
                $facilityInventories        = [];
                $facilityAvailabilitiesDate = [];
                $facilityInventoriesDate    = [];
                foreach ($facility->availabilities as $facility_availability) {
                    $facilityAvailabilities[date('l', strtotime($facility_availability->date))]         =
                        $facility_availability->availability;
                    $facilityAvailabilitiesDate[date('Y-m-d', strtotime($facility_availability->date))] =
                        $facility_availability->availability;
                }
                foreach ($facility->inventories as $facility_inventory) {
                    $facilityInventories[date('l', strtotime($facility_inventory->date))] =
                        $facility_inventory->availability;
                    $facilityInventoriesDate[date('Y-m-d', strtotime($facility_inventory->date))] =
                        $facility_inventory->availability;
                }

                //fetch operational hours of this facility
                $hours = HoursOfOperation::where('facility_id', $facility->id)->orderBy('day_of_week', 'ASC')->get();

                $getOperationalHours = $this->getOperationalHours($facility->id, $hours);
                $operationalHours    = $getOperationalHours['operationalHours'];
                $remainder           = $getOperationalHours['remainder'];


                //store all the rows of facility_availabilites table for this facility into a var
                $availabilities = $facility->availabilities;
                //store all the rows of facility_inventories table for this facility into a var
                $inventories = $facility->inventories;

                $inventoriesArray    = [];
                $availabilitiesArray = [];

                //create associative array of inventories data having date as index
                foreach ($inventories as $inventory) {
                    $inventoriesArray[$inventory->date] = $inventory;
                }

                //create associative array of availabilities data having date as index
                foreach ($availabilities as $availability) {
                    $availabilitiesArray[$availability->date] = $availability;
                }

                //iterate over dates for the next 90 days starting from the most recent last sunday
                foreach ($dates as $key => $date) {
                    $inventoriesArrayNew    = [];
                    $availabilitiesArrayNew = [];
                    $partnerAvailabilitiesArrayNew = [];
                    $partnersTableArray     = [];

                    $operationalHoursForAvailability = $operationalHours;
                    $operationalHoursForInventory    = $operationalHours;
                    $today                           = $date->format('l');

                    //if there is no row for this date in the inventoriesArray then compose an array
                    //if (!isset($inventoriesArray[$date->format('Y-m-d')])) {
                    //date of last week
                    $lastWeekToday = Carbon::createFromDate($date->format('Y'), $date->format('m'), $date->format('d'))->subDays(7)->format('Y-m-d');
                    //if we have data of this day in last week then copy it
                    if (count($facilityInventoriesDate) && isset($facilityInventoriesDate[$lastWeekToday])) {
                        //convert the data of last week same day into an array
                        $lastWeekDayRecords = json_decode($facilityInventoriesDate[$lastWeekToday], true);

                        foreach ($lastWeekDayRecords as $partnerKey => $lastWeekDayRecord) {
                            if ($lastWeekDayRecords[$partnerKey] > 0) {
                                $partnersTableArray[$partnerKey] = self::PARTNER_SPOTS_AVAILABLE;
                            } else {
                                $partnersTableArray[$partnerKey] = self::PARTNER_SPOTS_NOT_AVAILABLE;
                            }
                        }

                        $inventoriesArrayNew = json_decode($facilityInventoriesDate[$lastWeekToday], true);
                    } else {
                        //else create a static availability
                        while ($operationalHoursForInventory[$today]['open_time'] <= $operationalHoursForInventory[$today]['close_time']) {
                            $inventoriesArrayNew[$operationalHoursForInventory[$today]['open_time']] =
                                self::NUMBER_OF_SPOTS;
                            //create an array for partners table
                            $partnersTableArray[$operationalHoursForInventory[$today]['open_time']] =
                                self::PARTNER_SPOTS_AVAILABLE;
                            $operationalHoursForInventory[$today]['open_time']++;
                        }

                        if (isset($remainder[$date->format('l')]) && count($remainder[$date->format('l')])) {
                            foreach ($remainder[$date->format('l')] as $j => $remaining) {
                                $inventoriesArrayNew[$j] = self::NUMBER_OF_SPOTS;

                                $partnersTableArray[$j] = self::PARTNER_SPOTS_AVAILABLE;

                            }
                        }
                    }

                    /**
                     * SIMILAR ARRAY AS ABOVE FOR AVAILABILITIES
                     */

                    if (count($facilityInventoriesDate) && isset($facilityInventoriesDate[$lastWeekToday])) {
                        $availabilitiesArray    = json_decode($facilityInventoriesDate[$lastWeekToday], true);
                        $availabilitiesArrayNew = $availabilitiesArray;
                        // for first time
                       // $partnerAvailabilitiesArrayNew   = json_decode($facilityAvailabilitiesDate[$date->format('Y-m-d')], true);
                        // for later
                        $partnerAvailabilitiesArrayNew   = json_decode($facilityAvailabilitiesDate[$lastWeekToday], true);
                    } else {
                        //else create a static availability
                        while ($operationalHoursForAvailability[$today]['open_time'] <= $operationalHoursForAvailability[$today]['close_time']) {
                            $availabilitiesArrayNew[$operationalHoursForAvailability[$today]['open_time']] =
                                self::NUMBER_OF_SPOTS;
                            $partnerAvailabilitiesArrayNew[$operationalHoursForAvailability[$today]['open_time']] =     self::NUMBER_OF_SPOTS;
                            $operationalHoursForAvailability[$today]['open_time']++;
                        }

                        if (isset($remainder[$date->format('l')]) && count($remainder[$date->format('l')])) {
                            foreach ($remainder[$date->format('l')] as $j => $remaining) {
                                $availabilitiesArrayNew[$j] = self::NUMBER_OF_SPOTS;
                                $partnerAvailabilitiesArrayNew[$j] = self::NUMBER_OF_SPOTS;
                            }
                        }
                    }
                    
                 

                    /* $newInventory               = new FacilityInventory();
                    $newInventory->facility_id  = $facility->id;
                    $newInventory->availability = json_encode($inventoriesArrayNew, JSON_FORCE_OBJECT);
                    $newInventory->date         = $date->format('Y-m-d');
                    $newInventory->save();

                    $newAvailability               = new FacilityAvailability();
                      $newAvailability->facility_id  = $facility->id;
                      $newAvailability->availability = json_encode($availabilitiesArrayNew, JSON_FORCE_OBJECT);
                      $newAvailability->date         = $date->format('Y-m-d');
                      $newAvailability->save();*/

                    $newInventory = FacilityInventory::firstOrNew(
                        ['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
                    if (!$newInventory->exists) {
                        $newInventory->facility_id  = $facility->id;
                        $newInventory->availability = json_encode($inventoriesArrayNew, JSON_FORCE_OBJECT);
                        $newInventory->date         = $date->format('Y-m-d');
                        $newInventory->save();
                    }

                    $newAvailability = FacilityAvailability::firstOrNew(
                        ['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
                    if (!$newAvailability->exists) {
                        $newAvailability->facility_id  = $facility->id;
                        $newAvailability->availability = json_encode($availabilitiesArrayNew, JSON_FORCE_OBJECT);
                        $newAvailability->date         = $date->format('Y-m-d');
                        $newAvailability->save();
                    }

                    $partnerData = FacilityPartnerAvailabilityCron::firstOrNew(
                        ['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
                    if (!$partnerData->exists) {
                     //change on 2-7-19   $partnerData->availability = json_encode($partnersTableArray, JSON_FORCE_OBJECT);
                        $partnerData->availability = json_encode($partnerAvailabilitiesArrayNew, JSON_FORCE_OBJECT);
                        $partnerData->save();
                    }

                    //}
                }
            }

        });
    }


    /**
     * @param $facilityId
     * @param array $hours
     * @return array
     */
    protected function getOperationalHours($facilityId, $hours = []): array
    {
        /**
         * function will receive hours as eloquent collection if not
         * then fetch operational hours of the facility
         */

        if (!$hours) {
            //fetch operational hours of this facility
            $hours = HoursOfOperation::where('facility_id', $facilityId)->orderBy('day_of_week', 'ASC')->get();
        }

        $operationalHours = [];

        $week = [0 => 'Sunday', 1 => 'Monday', 2 => 'Tuesday', 3 => 'Wednesday', 4 => 'Thursday', 5 => 'Friday',
                 6 => 'Saturday', 7 => 'Sunday'];

        /**
         * create an associative array of hours of operation having day name as index
         * in case this facility has no matching row in the hours_of_operation table
         * still create an array with default opening and closing hours
         */

        $remainder = [];
        $data      = [];


        //iterate over facility working hours
        foreach ($hours as $hour) {
            if ($hour->close_time > '23:59:59') {
                /**
                 * if closing time of this facility is more than 23:59:59 then subtract 24 out of the closing time
                 * and set close time as 23:59:59, because anything more than 24 hours should be added to the
                 * next day of this facility
                 */
                $remainder[$week[$hour->day_of_week + 1]] = explode(':', $hour->close_time)[0] - 24;
                $hour->close_time                         = '23:59:59';
            }

            //if close time is less than 23:59:59 then subtract 1 hour from facility closing time
            if ($hour->close_time < '23:59:59') {
                $hour->close_time = date('H:i:s', strtotime('-1 hour', strtotime($hour->close_time)));
            }
            //set the operational hours of this week having day as index
            $operationalHours[$week[$hour->day_of_week]] = ['open_time'  => date('G', strtotime($hour->open_time)),
                                                            'close_time' => date('G', strtotime($hour->close_time))];
        }

        /**
         * iterate over an array of 0 to 6 and check if each day of week is set already, if not
         * then set that day and set the opening and closing hour as 0 and 23 respectively
         */
        foreach (range(0, 6) as $zeroToSix) {
            if (!isset($operationalHours[$week[$zeroToSix]])) {
                $operationalHours[$week[$zeroToSix]] =
                    ['open_time' => self::DAY_START_HOUR, 'close_time' => self::DAY_END_HOUR];
            }
        }

        /**
         * if we got any remainder while subtracting then create an
         * associative array for that having day name as index
         */
        if (count($remainder)) {
            foreach ($remainder as $key => $remaining) {
                for ($i = 0; $i < $remaining; $i++) {
                    $data[$key][] = true;
                }
            }
        }

        return ['operationalHours' => $operationalHours, 'remainder' => $data];
    }
}
