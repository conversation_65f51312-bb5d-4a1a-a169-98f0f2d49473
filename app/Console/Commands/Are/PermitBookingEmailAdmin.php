<?php

namespace App\Console\Commands\Are;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\Reservation;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\AreQrcode;
use Illuminate\Support\Collection;
use Storage;
use DB;
use Carbon\Carbon;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\PermitRateCriteriaMapping;
use App\Models\PermitRateCriteria;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Models\PasswordReset;
use App\Models\OauthClient;
use App\Models\ParkEngage\FacilityPaymentDetail;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use DateTime;
use App\Http\Helpers\MailHelper;


/**
 * Emails reservation stub to user
 */
class PermitBookingEmailAdmin extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'diamondpermitadmin:email {permit_request_id}/{password}/{is_admin}
                          {--customer_pay= : optional check customer payment}
                          {--payment_mode= : optional check for customer or admin mode}
                          {--resendemail= : optional check for resend email}
                          {--currentDateTime= : optional check for resend email}
                          {--is_admin_dynamic= : optional check for approve permit}';


  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Permit Request';


  public function __construct(LoggerFactory $logFactory)
  {
    parent::__construct();
    $this->log = (new LoggerFactory)->setPath('logs/are')->createLogger('permitbook');
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
    $monthly_request_id = $this->argument('permit_request_id');
    $is_admin = $this->argument('is_admin');
    $paymentMode = $this->option('payment_mode');
    $customerPay = $this->option('customer_pay');
    $resendemail = $this->option('resendemail');
    $currentDateTime = $this->option('currentDateTime');
    $isAdmin = $this->option('is_admin_dynamic');

    //$type_id = $this->argument('type_id');
    $monthlyRequest = PermitRequest::with(['facility.facilityConfiguration', 'user', 'transaction'])->where('id', $monthly_request_id)->first();
    //PMIS-14836 
    //0-Admin,1-Customer,2-Both 
    if (isset($monthlyRequest->facility->facilityConfiguration) && ($monthlyRequest->facility->facilityConfiguration->permit_email == 0)) {
      return true;
    } elseif(isset($monthlyRequest->facility->facilityConfiguration) && ($monthlyRequest->facility->facilityConfiguration->permit_email == 1) && ($monthlyRequest->facility->facilityConfiguration->cust_email_admin_email == 0) && $monthlyRequest->is_admin == 0 ) {
      return true;
    } elseif(isset($monthlyRequest->facility->facilityConfiguration) && ($monthlyRequest->facility->facilityConfiguration->permit_email == 1) && ($monthlyRequest->facility->facilityConfiguration->cust_email_admin_email == 1) && $monthlyRequest->is_admin == 1 ){
      return true;
    }
    //PMIS-14836 
    // get Partner Slug
    $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($monthlyRequest) {
      $join->on('user_facilities.user_id', '=', 'users.id');
      $join->where('user_facilities.facility_id', "=", $monthlyRequest->facility_id);
    })->where('created_by', $monthlyRequest->partner_id)->whereNotNull('slug')->where('user_type', 12)->first();

    if ($getRM) {
      $partnerSlug = $getRM->slug;
    } else {
      $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $monthlyRequest->partner_id)->first();
      $partnerSlug  =  $checkPaymentUrl->touchless_payment_url;
    }

    if ($monthlyRequest->facility_id == config('parkengage.WAILUKU_FACILITY')) {
      $monthlyRequest['loginurl'] = env('WEB_URL_WAILUKU');
    } else if ($monthlyRequest->partner_id == config('parkengage.PARTNER_PCI')) {
      $monthlyRequest['loginurl'] = env('WEB_URL_CUSTOMERPORTAL_PCI') . $partnerSlug;
    } else {
      $monthlyRequest['loginurl'] = env('WEB_URL_CUSTOMERPORTAL') . "/" . $partnerSlug;
    }

    if (!$monthlyRequest) {
      throw new NotFoundException('No user with that ID.');
    }


    //skiData QR code make image
    if ($monthlyRequest->skidata_value != '') {
      $permit_skidata = config('parkengage.SKIDATA_PREDEFINE') . $monthlyRequest->skidata_value;
      $this->generateBarcodeJpgNew($permit_skidata);
      $imageBarcodeFileName = str_random(12) . '_skiDataqrcode.png';
      Storage::put($imageBarcodeFileName, $this->generateBarcodeJpgNew($permit_skidata));
      $monthlyRequest['skidata_qrcode'] = $imageBarcodeFileName;
      $monthlyRequest->skidata_predefine = config('parkengage.SKIDATA_PREDEFINE');
    }

    if (isset($monthlyRequest->facility->facilityConfiguration) && $monthlyRequest->facility->facilityConfiguration->is_pemit_qrcode == '1') {
      $permit_qrcode = $monthlyRequest->account_number;
      $this->generateBarcodeJpgNew($permit_qrcode);
      $imageBarcodeFileName = str_random(12) . '_permitQrcode.png';
      Storage::put($imageBarcodeFileName, $this->generateBarcodeJpgNew($permit_qrcode));
      $monthlyRequest['permit_qrcode'] = $imageBarcodeFileName;
    }

    $parsed_start_date = Carbon::createFromFormat('Y-m-d', $monthlyRequest->desired_start_date);
    $monthlyRequest['desired_start_date'] = Carbon::parse($parsed_start_date)->format('m/d/Y');

    $parsed_end_date = Carbon::createFromFormat('Y-m-d', $monthlyRequest->desired_end_date);
    $monthlyRequest['desired_end_date'] = Carbon::parse($parsed_end_date)->format('m/d/Y');

    /* $monthlyRequest['desired_start_date'] = $this->getDaySufixFormat($monthlyRequest->desired_start_date);
        $monthlyRequest['desired_end_date'] = $this->getDaySufixFormat($monthlyRequest->desired_end_date); */
    $permit_vehicle = PermitVehicleMapping::where('permit_request_id', $monthly_request_id)->pluck('permit_vehicle_id');
    $monthlyRequest['vehicle'] = PermitVehicle::wherein('id', $permit_vehicle)->get();
    //$monthlyRequest['vehicle'] = PermitVehicle::where('permit_request_id',$monthly_request_id)->get();
    $monthlyRequest['is_admin'] = $this->argument('is_admin');
    // $monthlyRequest['password'] = $this->argument('password');

    #dushyant 06-06-2024
    #add parking time in email
    $permit_rate_criteria_id = PermitRateCriteriaMapping::where('permit_rate_id', $monthlyRequest->permit_rate_id)->pluck('permit_rate_criteria_id');
    if (count($permit_rate_criteria_id) > 0) {
      $monthlyRequest['permit_rate_criteria'] = PermitRateCriteria::wherein('id', $permit_rate_criteria_id)->get();
      $monthlyRequest['permit_rate_criteria'] = $this->formatPermitRateCriteria($monthlyRequest['permit_rate_criteria']);
    } else {
      $formated_time = [];
      $formated_time[] = [
        'days' => "All Days",
        'entry_time_begin' => "12:00 AM",
        'entry_time_end' => "11:59 PM",
        'exit_time_begin' => "12:00 AM",
        'exit_time_end' => "11:59 PM",
      ];
      $monthlyRequest['permit_rate_criteria'] = $formated_time;
    }
    #end add parking time in email
    $permit_validity = '';
    $permitRateDescHour = array();
    $permitRateDetails  = PermitRate::find($monthlyRequest->permit_rate_id);
    if ($permitRateDetails) {
      $permitRateDescHour = PermitRateDescription::find($permitRateDetails->permit_rate_description_id);
      // if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "1")) {
      //   $permit_validity = '1 Month';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "2")) {
      //   $permit_validity = '2 Month';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "3")) {
      //   $permit_validity = 'Quarterly';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "4")) {
      //   $permit_validity = '4 Month';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "5")) {
      //   $permit_validity = '5 Month';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "6")) {
      //   $permit_validity = 'Half Yearly';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "7")) {
      //   $permit_validity = '7 Month';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "8")) {
      //   $permit_validity = '8 Month';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "9")) {
      //   $permit_validity = '9 Month';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "10")) {
      //   $permit_validity = '10 Month';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "11")) {
      //   $permit_validity = '11 Month';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "12")) {
      //   $permit_validity = '1 Year';
      // } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "13")) {
      //   $permit_validity = 'Weekly';
      // } else {
      //   $permit_validity = '1 Month';
      // }
      /*
          if($permitRateDescHour && (strtolower($permitRateDescHour->hours_description)== "yearly")){
            $permit_validity = '1 Year';
          }else if($permitRateDescHour && (strtolower($permitRateDescHour->hours_description)== "mtm")){
            $permit_validity = '1 Month';
          }
          else if($permitRateDescHour && (strtolower($permitRateDescHour->hours_description)== "monthly")){
            $permit_validity = '1 Month';
          }
          */


      $permit_validity = $permitRateDescHour->permit_tenure;
      if ($permitRateDescHour && $permitRateDescHour->is_hide_validity == "1") {
        $permit_validity = '';
      }
    }

    $brand_setting = BrandSetting::where('user_id', $monthlyRequest->partner_id)->first();
    $partnerDetails = User::where('id', $monthlyRequest->partner_id)->first();
    $permit_services = PermitRequestServiceMapping::where('permit_request_id', $monthlyRequest->id)->pluck('permit_service_id');
    //$services        = PermitServices::whereIn('id',$permit_services)->get();

    $services  = PermitServices::with([
      'permit_service_criteria_mappings' => function ($query) use ($permit_services) {
        $query->whereIn('permit_service_id', $permit_services)
          ->with('criteria');
      }
    ])
      ->select('permit_services.*')
      ->whereIn('id', $permit_services)
      ->orderBy('permit_services.id', 'asc')
      ->get();

    if (count($services) > 0) {
      $services = $this->formatPermitServiceCriteria($services);
    }

    $secret = OauthClient::where('partner_id', $monthlyRequest->partner_id)->first();
    // Password Change
    // Generate password reset token
    $reset = PasswordReset::firstOrNew(['email' => $monthlyRequest->email]);
    $reset->generatePasswordResetToken();
    $reset->created_at = Carbon::now()->toDateTimeString();
    $reset->save();
    if (isset($secret) && !empty($secret)) {
      $payment_gateway = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
      if (isset($payment_gateway) && $payment_gateway->reset_password_url != '') {
        if ($brand_setting->user_id == config('parkengage.PARTNER_DIAMOND')) {
          $reset_password_url = env('WEB_URL_WAILUKU') . "/reset-password/";
        } else {
          if ($payment_gateway->user_id == config('parkengage.PARTNER_PCI')) {
            // if($request->facility_id){
            if ($monthlyRequest->facility_id) {
              $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($monthlyRequest, $payment_gateway) {
                $join->on('user_facilities.user_id', '=', 'users.id');
                $join->where('user_facilities.facility_id', "=", $monthlyRequest->facility_id);
              })->where('created_by', $payment_gateway->user_id)->whereNotNull('slug')->where('user_type', 12)->first();
              $reset_password_url = env('WEB_URL_CUSTOMERPORTAL_PCI') . "/" . $getRM->slug . "/reset-password/";
            } else {
              $reset_password_url = env('WEB_URL_CUSTOMERPORTAL_PCI') . "/" . $payment_gateway->reset_password_url . "/reset-password/";
            }
          } else {
            $reset_password_url = env('WEB_URL_CUSTOMERPORTAL') . "/" . $payment_gateway->reset_password_url . "/reset-password/";
          }
        }
      } else {
        $reset_password_url = config('parkengage.password_reset_url');
      }
    } else {
      $reset_password_url = config('parkengage.password_reset_url');
    }

    if ($monthlyRequest->is_admin == '0') {
      $monthlyRequest['password'] = '';
    } else {
      $monthlyRequest['password'] = $reset_password_url . $reset->token . "?is_create=1";
    }

    // Alka
    if (isset($isAdmin)) {
      $monthlyRequest['is_admin_dynamic']  = $isAdmin;
    }
    // end
    if (isset($monthlyRequest->user->user_type) && ($monthlyRequest->user->user_type != '5')) {
      $monthlyRequest['is_admin_dynamic']  = '1';
    }
    // Weekly permit date
    if (isset($permitRateDescHour) && $permitRateDescHour->permit_frequency == "13" && ($permitRateDescHour->id) == config('parkengage.WEEKLY.PERMIT_DESCRIPTION_ID')) {

      // Get the current date and subtract 7 days
      $dateSevenDaysAgo = Carbon::now()->subDays(7);

      // Get the start date (7 days ago) and the end date (1 day before today)
      $startDate = $dateSevenDaysAgo->copy()->startOfDay(); // Start of the day 7 days ago
      $endDate = Carbon::now()->subDay()->endOfDay();       // End of the day 1 day ago (yesterday)

      $permit_start_date = $startDate->toDateString();
      $permit_end_date = $endDate->toDateString();

      $endDate = new DateTime($permit_end_date);

      // Add one day to the end date
      $desiredStartDate = $endDate->modify('+1 day');
      $monthlyRequest['desired_start_date'] = $desiredStartDate->format('m/d/Y');

      $desiredEndDate = $desiredStartDate->modify('+6 day');
      $monthlyRequest['desired_end_date'] = $desiredEndDate->format('m/d/Y');
    }
    // End

    // Add Card details
    $facilityPaymentDetails = FacilityPaymentDetail::where('facility_id', $monthlyRequest->facility_id)->first();
    $paymentProfile = '';
    if (($facilityPaymentDetails) && $facilityPaymentDetails->facility_payment_type_id == '1') {
      $paymentProfile = PlanetPaymentProfile::where('user_id', $monthlyRequest->user_id)->first();
    } else if (($facilityPaymentDetails) && $facilityPaymentDetails->facility_payment_type_id == '2') {
      $paymentProfile = DatacapPaymentProfile::where('user_id', $monthlyRequest->user_id)->first();
    } else if (($facilityPaymentDetails) && $facilityPaymentDetails->facility_payment_type_id == '4') {
      $paymentProfile = HeartlandPaymentProfile::where('user_id', $monthlyRequest->user_id)->first();
    }

    #PIMS-10032 dushyant phase2
    if (is_null($monthlyRequest->business_id) || empty($monthlyRequest->business_id)) {
    } else {
      #DD PIMS-11544
      if (!$monthlyRequest->user->payment_mode) {
        $new_permit_amt = ($monthlyRequest->negotiated_amount ?? $monthlyRequest->permit_final_amount);
        $monthlyRequest->permit_rate = $new_permit_amt;
      }
    }

    if ($monthlyRequest->facility_id == config('parkengage.WAILUKU_FACILITY')) {
      $view = 'are.are-permit-booking';
    } else if ($monthlyRequest->partner_id == config('parkengage.PARTNER_UNITED')) {
      $view = 'united.permit-booking';
    } else if (in_array($monthlyRequest->facility_id, [config('parkengage.153rd_Street_Parking_FACILITY')])) {
      $view = 'yenkee.permit-booking';
    } else if ($monthlyRequest->facility_id != config('parkengage.WAILUKU_FACILITY') && $monthlyRequest->partner_id == config('parkengage.PARTNER_DIAMOND')) {
      $view = 'are-diamond.are-permit-booking';
    } else {
      $view = 'yenkee.permit-booking';
    }

    $partner_name = "Parking Services"; #pims-12258 dd
    if ($monthlyRequest->partner_id == config('parkengage.PARTNER_USM')) {
      $view = 'usm.permit-booking';
      $partner_name = "USM Parking";
    }
    if ($monthlyRequest->partner_id == config('parkengage.PARTNER_MANLO')) {
      $view = 'usm.permit-booking';
      $partner_name = "MENLO Parking";
    }
    if ($monthlyRequest->partner_id == config('parkengage.PARTNER_PCI')) {
      $view = 'kstreet.permit-booking';
    }
    if ($monthlyRequest->partner_id == config('parkengage.PARTNER_INTRAPARK')) {
      $view = 'intrapark.permit-booking';
    }
    if ($monthlyRequest->partner_id == config('parkengage.PARTNER_PAVE')) {
      $view = 'pave-permit.permit-booking';
    }

    $view_text = $view . '-plain-text';
    if (!View::exists($view_text)) {
      $view_text = $view;
    }

    try {
      $this->log->info("Permit user email sending: $monthlyRequest->email");
      $this->log->info("Permit user id: $monthlyRequest->user_id");
      $this->log->info("Card Data: " . json_encode($paymentProfile));

      if ($paymentMode != 0 || $paymentMode == null || $customerPay == 1) {
        if (isset($resendemail) && !empty($resendemail)) {
          $email = $resendemail;
        } else {
          $email = $monthlyRequest->email;
        }

        MailHelper::sendEmail($email, $view_text, ['subject' => "Your Parking Permit has been Confirmed", 'data' => $monthlyRequest, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'paymentProfile' => $paymentProfile, 'partner_name' => $partner_name], $monthlyRequest->partner_id);
        // Mail::send(
        //   ['text' => $view_text, 'html' => $view],
        //   ['data' => $monthlyRequest, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'paymentProfile' => $paymentProfile, 'partner_name' => $partner_name],
        //   function ($message) use ($monthlyRequest, $email) {
        //     $message->to($email)->subject("Your Parking Permit has been Confirmed");
        //     $message->from(config('parkengage.default_sender_email'));
        //   }
        // );
      }
      //Alka :: Yenkee
      if (isset($paymentMode) && $paymentMode == 0) {
        $this->log->info("Permit payment link send to email sending: $monthlyRequest->email");

        $monthlyRequest['paymentUrl'] = env('WEB_URL_CUSTOMERPORTAL') . "/" . $partnerSlug;
        $monthlyRequest['currentDateTime'] = !empty($currentDateTime) ? $currentDateTime : '';

        MailHelper::sendEmail($monthlyRequest->email, 'yenkee.permit-payment-link', ['subject' => 'Payment Link for your permit request ' . $monthlyRequest->account_number, 'data' => $monthlyRequest, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'permitRateDescHour' => $permitRateDescHour, 'services' => $services, 'permit_validity' => $permit_validity, 'partner_name' => $partner_name], $monthlyRequest->partner_id);

        // Mail::send(
        //   'yenkee.permit-payment-link',
        //   ['data' => $monthlyRequest, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'permitRateDescHour' => $permitRateDescHour, 'services' => $services, 'permit_validity' => $permit_validity, 'partner_name' => $partner_name],
        //   function ($message) use ($monthlyRequest) {
        //     $message->to($monthlyRequest->email)->subject('Payment Link for your permit request ' . $monthlyRequest->account_number);
        //     $message->from(config('parkengage.default_sender_email'));
        //   }
        // );
      }
      //  End           
      $this->log->info("email sent: $monthlyRequest->email");
      if ($monthlyRequest->facility_id == config('parkengage.DEPASQUALE_FACILITY')) {
        Mail::send(
          ['text' => $view_text, 'html' => $view],
          ['data' => $monthlyRequest, 'brand_setting' => $brand_setting],
          function ($message) use ($monthlyRequest) {
            $message->bcc(config('parkengage.WAILUKU.USER_4'))->subject("Your Parking Permit has been Confirmed");
            $message->from(config('parkengage.default_sender_email'));
          }
        );
        $view = "united.alert-user-permit-request";
        Mail::send(
          $view,
          ['data' => $monthlyRequest, 'brand_setting' => $brand_setting],
          function ($message) {
            $message->to([config('parkengage.WAILUKU.USER_1'), config('parkengage.WAILUKU.USER_4')])->subject("A new permit has been purchased");
            $message->from(config('parkengage.default_sender_email'));
          }
        );
      }
      if ($monthlyRequest->facility_id == config('parkengage.PARTNER_INTRAPARK')) {
        $view = "intrapark.alert-user-permit-request";
        Mail::send(
          $view,
          ['data' => $monthlyRequest, 'brand_setting' => $brand_setting],
          function ($message) {
            $message->to([config('parkengage.WAILUKU.USER_1'), config('parkengage.WAILUKU.USER_5')])->subject("A new permit has been purchased");
            $message->from(config('parkengage.default_sender_email'));
          }
        );
      }
    } catch (\Exception $e) {
      $errorMessage = array();
      $errorMessage['message'] = $e;
      $this->log->error('Issue in email sending:', $errorMessage);
    }
  }

  private function formatPermitServiceCriteria($services)
  {
    $daysMap = [
      1 => 'Sun',
      2 => 'Mon',
      3 => 'Tue',
      4 => 'Wed',
      5 => 'Thu',
      6 => 'Fri',
      7 => 'Sat'
    ];
    $allDays = range(1, 7);

    $i = 0;
    foreach ($services as $service) {
      if (count($service->permit_service_criteria_mappings) > 0) {
        $formatted = [];
        foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
          $item = $permit_service_criteria_mapping->criteria;
          $days = explode(',', $item->days);
          sort($days); // Sort days to match the sequence of $allDays
          if ($days == $allDays) {
            $dayNamesStr = 'All Days';
          } else {
            $dayNames = array_map(function ($day) use ($daysMap) {
              return $daysMap[$day];
            }, $days);
            $dayNamesStr = implode(',', $dayNames);
          }

          $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
          $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
          $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

          // Adjust exit time if it's greater than 24 hours
          $exit_time = explode(":", $item->exit_time_end);
          if ($exit_time[0] > 23) {
            $next_hr = $exit_time[0] - 24;
            $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
            $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
            $exit_time_overflow = ' (next day)';
          } else {
            $exit_time_overflow = '';
            $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
          }


          $formatted[] = [
            'days' => $dayNamesStr,
            'entry_time_begin' => $entry_time_begin,
            'entry_time_end' => $entry_time_end,
            'exit_time_begin' => $exit_time_begin,
            'exit_time_end' => $exit_time_end . $exit_time_overflow,
          ];
        }
        $services[$i]->criteria = $formatted;
      }
      $i++;
    }

    return $services;
  }

  #dushyant 06-06-2024
  #add parking time in email
  private function formatPermitRateCriteria($criteria)
  {
    $daysMap = [
      1 => 'Sun',
      2 => 'Mon',
      3 => 'Tue',
      4 => 'Wed',
      5 => 'Thu',
      6 => 'Fri',
      7 => 'Sat'
    ];
    $allDays = range(1, 7);
    $formatted = [];

    foreach ($criteria as $item) {
      $days = explode(',', $item->days);
      sort($days); // Sort days to match the sequence of $allDays
      if ($days == $allDays) {
        $dayNamesStr = 'All Days';
      } else {
        $dayNames = array_map(function ($day) use ($daysMap) {
          return $daysMap[$day];
        }, $days);
        $dayNamesStr = implode(',', $dayNames);
      }

      $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
      $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
      $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

      // Adjust exit time if it's greater than 24 hours
      $exit_time = explode(":", $item->exit_time_end);
      if ($exit_time[0] > 23) {
        $next_hr = $exit_time[0] - 24;
        $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
        $exit_time_overflow = ' (next day)';
      } else {
        $exit_time_overflow = '';
        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
      }


      $formatted[] = [
        'days' => $dayNamesStr,
        'entry_time_begin' => $entry_time_begin,
        'entry_time_end' => $entry_time_end,
        'exit_time_begin' => $exit_time_begin,
        'exit_time_end' => $exit_time_end . $exit_time_overflow,
      ];
    }

    return $formatted;
  }
  #add parking time in email end

  public function getDaySufixFormat($date)
  {
    $day = date('d', strtotime($date));
    $monthYear = date('M, Y', strtotime($date));
    $number = (string) $day;
    $last_digit = substr($number, -1);
    $second_last_digit = substr($number, -2, 1);
    $suffix = 'th';
    if ($second_last_digit != '1') {
      switch ($last_digit) {
        case '1':
          $suffix = 'st';
          break;
        case '2':
          $suffix = 'nd';
          break;
        case '3':
          $suffix = 'rd';
          break;
        default:
          break;
      }
    }
    if ((string) $number === '1') $suffix = 'st';
    // return $number.$suffix . ' '.$monthYear;
    return $number . ' ' . $monthYear;
  }



  public function generateBarcodeJpgNew($qrcode)
  {
    $html = $this->generateBarcodeHtml($qrcode);

    $image = app()->make(Image::class);
    $image->setOption('width', '420');
    return $image->getOutputFromHtmlString($html);
  }

  public function generateBarcodeHtml($qrcode)
  {
    return view('mapco.Qrcodegenerate', ['barcode' => $qrcode]);
  }
}
