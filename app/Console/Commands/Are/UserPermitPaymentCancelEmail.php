<?php

namespace App\Console\Commands\Are;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Http\Helpers\MailHelper;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Symfony\Component\Console\Input\InputOption;

/**
 * Emails reservation stub to user
 */
class UserPermitPaymentCancelEmail extends Command
{
    protected $log;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notification:permit-cancel-data  { --partner_id= : partner ID }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send current date data';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/permit-failled-email')->createLogger('email_notify');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
  public function handle()
  {
    try {
    $permitFaillData = DB::table('permit_requests')
    ->join('all_failed_transactions', 'permit_requests.account_number', '=', 'all_failed_transactions.reference_key')
    ->whereNull('permit_requests.anet_transaction_id')
    ->whereNull('permit_requests.cancelled_at')
    ->whereNotNull('permit_requests.deleted_at')
    ->whereRaw("DATE(permit_requests.created_at) = ?", [Carbon::today()->toDateString()])
    ->select('permit_requests.id as permit_req_id','permit_requests.account_number','permit_requests.user_id','permit_requests.facility_id','permit_requests.created_at','permit_requests.email','permit_requests.name','permit_requests.phone','permit_requests.deleted_at','permit_requests.cancelled_at','permit_requests.vehicle_id', 'all_failed_transactions.*'); 
    //->get();
    if (!empty($partner_id)) {
      $permitFaillData->where('permit_requests.partner_id', $partner_id);
    }
    $permitFaillData = $permitFaillData->get();
    $permitCanceldata = DB::table('permit_requests')
    ->join('all_failed_transactions', 'permit_requests.account_number', '=', 'all_failed_transactions.reference_key')
    ->whereNull('permit_requests.anet_transaction_id')
    ->whereNotNull('permit_requests.cancelled_at')
    ->whereRaw("DATE(permit_requests.created_at) = ?", [Carbon::today()->toDateString()])
    ->select('permit_requests.id as permit_req_id','permit_requests.account_number','permit_requests.user_id','permit_requests.facility_id','permit_requests.created_at','permit_requests.email','permit_requests.name','permit_requests.phone','permit_requests.deleted_at','permit_requests.cancelled_at','permit_requests.vehicle_id', 'all_failed_transactions.*'); 
    //->get();
    if (!empty($partner_id)) {
      $permitCanceldata->where('permit_requests.partner_id', $partner_id);
    }
    $permitCanceldata = $permitCanceldata->get();
    $transient  = DB::table('datacap_transactions')
    ->join('tickets', 'datacap_transactions.ticket_id', '=', 'tickets.id')
    ->join('all_failed_transactions', 'tickets.ticket_number', '=', 'all_failed_transactions.reference_key')
    ->where('datacap_transactions.transaction_retry','=',3)
    ->whereNull('tickets.anet_transaction_id')
    ->select('tickets.id as ticket_id','tickets.ticket_number','tickets.user_id','tickets.facility_id','tickets.created_at','tickets.license_plate as ticket_lp', 'all_failed_transactions.*') 

    ->whereRaw("DATE(datacap_transactions.created_at) = ?", [Carbon::today()->toDateString()]);
    //->get();
    if (!empty($partner_id)) {
      $transient->where('datacap_transactions.partner_id', $partner_id);
    }
    $transient = $transient->get();
    if(empty($permitFaillData) && empty($permitCanceldata) && empty($transient)){
      return true;
    }

      $excelSheetName = 'All_Permit_Failed_List' . date('m-d-Y H:i:s');
      $data = [];
      $color = "#0C4A7A";
      $location = "ALL";
      $currentDateTime = \Carbon\Carbon::now();
      Excel::create(
        $excelSheetName,
        function ($excel) use (
          $color,
          $permitFaillData,
          $permitCanceldata,
          $transient,
          $location,
          $currentDateTime
        ) {
          if(!empty($permitFaillData)){
            $excel->sheet('Failed Permits', function ($sheet) use (
              $color,
              $permitFaillData,
              $location,
              $currentDateTime
            ) 
            {
            
              $this->buildSheet($color,$sheet);
              $i = 1;
              foreach ($permitFaillData as $value) {
                
                  $data['Sr No.'] = $i;
                  $data['Permit Number'] = $value->account_number;
                  $data['Reason'] = $value->response_message;
                  $license_plates = json_decode($value->license_plate, true);
                  $plates = [];
                  foreach ($license_plates as $vehicle) {
                      if (isset($vehicle['license_plate'])) {
                        $plates[] = $vehicle['license_plate'];
                      }
                  }
                  $data['License Plate'] = implode(',', $plates);
                  $user = User::find($value->user_id);
                  $data['Name'] =  $user->name;
                  $data['Email'] = $user->email;
                  $data['Phone'] = $user->phone;
                  //$permitData = PermitRequest::with(['PermitVehicle.Vehicle'])
                  //->where('user_id',$value->user_id)
                  $i++;
                  $dataArr[] = $data;
                  $row = $key + $index;
                  $sheet->cell('A' . $row . ':I' . $row, function ($cell) use ($color) {
                    $cell->setAlignment('left'); // Center horizontally
                    $cell->setValignment('left');
                  });
              
              }
              $sheet->fromArray($dataArr, [], 'A3', true, true);
              
              // dd($dataArr);
              /*  
              foreach ($licensePlateData as $key =>  $value) {
                $sheet->cell('A' . $key + $index, function ($cell) use ($color) {
                  $cell->setAlignment('left'); // Center horizontally
                  $cell->setValignment('left');
                });
              } */
            });
          }
          if (!empty($permitCanceldata)) {
            $excel->sheet('Cancel Permits', function ($sheet) use (
              $color,
              $permitFaillData,
              $location,
              $currentDateTime
            ) {
            
              $this->buildSheet($color,$sheet);
              $i = 1;
              foreach ($permitCanceldata as $value) {
                 $data['Sr No.'] = $i;
                  $data['Permit Number'] = $value->account_number;
                  $data['Reason'] = $value->response_message;
                  $license_plates = json_decode($value->license_plate, true);
                  $plates = [];
                  foreach ($license_plates as $vehicle) {
                      if (isset($vehicle['license_plate'])) {
                        $plates[] = $vehicle['license_plate'];
                      }
                  }
                  $data['License Plate'] = implode(',', $plates);
                  $user = User::find($value->user_id);
                  $data['Name'] =  $user->name;
                  $data['Email'] = $user->email;
                  $data['Phone'] = $user->phone;
                  //$permitData = PermitRequest::with(['PermitVehicle.Vehicle'])
                  //->where('user_id',$value->user_id)
                  $i++;
                  $dataArr[] = $data;
                  $row = $key + $index;
                  $sheet->cell('A' . $row . ':I' . $row, function ($cell) use ($color) {
                    $cell->setAlignment('left'); // Center horizontally
                    $cell->setValignment('left');
                  });
              
              }
              $sheet->fromArray($dataArr, [], 'A3', true, true);
              
              // dd($dataArr);
              /*  
              foreach ($licensePlateData as $key =>  $value) {
                $sheet->cell('A' . $key + $index, function ($cell) use ($color) {
                  $cell->setAlignment('left'); // Center horizontally
                  $cell->setValignment('left');
                });
              } */
            });
          }
          if (!empty($transient)) {
            $excel->sheet('Transient', function ($sheet) use ($color,$transient,$location,$currentDateTime) {
              $this->buildSheet($color,$sheet);
              $i = 1;
              foreach ($transient as $key => $value) {
                  $this->log->info('in loop');
                 $data['Sr No.'] = $i;
                  $data['Ticket Number'] = $value->ticket_number;
                  $data['Reason'] = $value->response_message;
                  // $license_plates = json_decode($value->license_plate, true);
                  // $plates = [];
                  // foreach ($license_plates as $vehicle) {
                  //     if (isset($vehicle['license_plate'])) {
                  //       $plates[] = $vehicle['license_plate'];
                  //     }
                  // }
                  $data['License Plate'] = $value->ticket_lp;
                  $user = User::find($value->user_id);
                  $data['Name'] =  $user->name;
                  $data['Email'] = $user->email;
                  $data['Phone'] = $user->phone;
                  //$permitData = PermitRequest::with(['PermitVehicle.Vehicle'])
                  //->where('user_id',$value->user_id)
                   $this->log->info('Ticket Number'.$value->ticket_number);
                  $i++;
                  $dataArr[] = $data;
                  $row = $key + $index;
                  $sheet->cell('A' . $row . ':I' . $row, function ($cell) use ($color) {
                    $cell->setAlignment('left'); // Center horizontally
                    $cell->setValignment('left');
                  });
              
              }
              $sheet->fromArray($dataArr, [], 'A3', true, true);
                 $this->log->info('end'. json_encode($dataArr));
              /*  
              foreach ($licensePlateData as $key =>  $value) {
                $sheet->cell('A' . $key + $index, function ($cell) use ($color) {
                  $cell->setAlignment('left'); // Center horizontally
                  $cell->setValignment('left');
                });
              } */
            });
          }
        }
      )->store('xls');
      $data['themeColor']     = $color;
      $data['mail_body']      = "Permit and Trasient payment failed list";
      storage_path('exports/' . $excelSheetName . '.xls');
      Mail::send('woodman.license-plate-report', $data, function ($message)  use ($excelSheetName) {
          //$message->bcc(['<EMAIL>']);
          //$message->to('<EMAIL>');
          $message->to(config('parkengage.notify.failed_permit'));
          $message->subject('Permit and Trasient payment failed list');
          $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
          $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
          $this->log->info("Mail Sent success with failname :  {$path_to_file}");
          if (file_exists($path_to_file)) {
              // $this->log->info("Path Of file and name 333 {$path_to_file}");
              $message->attach($path_to_file);
          }
      });
    } catch (Exception $e) {
        $this->log->info("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
    }
  }

  public function buildSheet($color,$sheet) {
    $sheet->setWidth(array(
                'A'     => 35,
                'B'     =>  35,
                'C'     =>  35,
                'D'     =>  35,
                'E'     =>  35,
                'F'     =>  35,
                'G'     =>  35,
              ));

    $this->log->info('WLA 99 .');

    $sheet->getColumnDimension('A')->setWidth(25);
    $sheet->getColumnDimension('B')->setWidth(25);
    $sheet->getColumnDimension('C')->setWidth(30);
    $sheet->getColumnDimension('D')->setWidth(36);
    $sheet->getColumnDimension('E')->setWidth(36);
    $sheet->getColumnDimension('F')->setWidth(36);
    $sheet->getColumnDimension('G')->setWidth(36);
    //end width for colom here
    //set header for excel work
    $sheet->mergeCells('A1:G1');
    //$sheet->mergeCells('F1:J1');
    $sheet->getRowDimension(1)->setRowHeight(60);
    $sheet->setCellValue('A1', 'Failed Permit List');
    $sheet->cell('A1:G1', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground($color);
      $cell->setFontColor('#ffffff');
      $cell->setFontSize('20');
    });

    $cellValue = "Print Date - \n" .  date('m-d-Y h:i');
    $sheet->setCellValue('A2', "$cellValue");
    $sheet->getStyle('A2')->getAlignment()->setWrapText(true);
    // Set the height of cell H2 (adjust as needed)
    $sheet->getRowDimension(2)->setRowHeight(80);
    $sheet->getRowDimension(3)->setRowHeight(50);

    $sheet->cell('A2', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground('#D6DCE4');
      $cell->setFontColor('#000000');
      $cell->setFontSize('16');
    });

    $location = "All";
    $sheet->mergeCells('B2:B2');
    $sheet->setCellValue('B2', $location);
    $sheet->getStyle('B2')->getAlignment()->setWrapText(true);

    $sheet->cell('B2:B2', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground('#ffffff');
      $cell->setFontColor('#000000');
      $cell->setFontSize('18');
    });

    $sheet->cell('A3', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground($color);
      $cell->setFontColor('#ffffff');
      $cell->setFontSize('18');
    });
    $sheet->cell('B3', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground($color);
      $cell->setFontColor('#ffffff');
      $cell->setFontSize('18');
    });
    $sheet->cell('C3', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground($color);
      $cell->setFontColor('#ffffff');
      $cell->setFontSize('18');
    });
    $sheet->cell('D3', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground($color);
      $cell->setFontColor('#ffffff');
      $cell->setFontSize('18');
    });
    $sheet->cell('E3', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground($color);
      $cell->setFontColor('#ffffff');
      $cell->setFontSize('18');
    });
    $sheet->cell('F3', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground($color);
      $cell->setFontColor('#ffffff');
      $cell->setFontSize('18');
    });
    $sheet->cell('G3', function ($cell) use ($color) {
      $cell->setAlignment('center'); // Center horizontally
      $cell->setValignment('center'); // Center vertically
      $cell->setFontWeight('bold');
      $cell->setBackground($color);
      $cell->setFontColor('#ffffff');
      $cell->setFontSize('18');
    });
    $this->log->info('WLA 111 .');

    $i = 1;
    $sheet->cell('A3:I3', function ($cell) use ($color) {
      $cell->setAlignment('left'); // Center horizontally
      $cell->setValignment('left');
    });
    $index = 4;
  }
}

