<?php

namespace App\Console\Commands\Are;

use App\Services\LoggerFactory;
use App\Services\Pdf;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Mail;
use App\Models\ParkEngage\UserPermitTypeMapping;
use App\Models\PermitRequest;
use App\Models\User;
use App\Http\Helpers\QueryBuilder;

class PermitAutoExpireBusinessRole extends Command
{
    protected $partnerId;
    protected $partnerRmId;
    protected $facilityId;
    protected $checkinTime;
    protected $checkoutTime;
    // protected $brandSettings;
    protected $log;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "are:permit-auto-expire-business-role
                            { --partnerId= : partner ID }
                            { --facilityId= : facility ID }";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command is used to update capacity for business account subordinate in case of those permit expire after grace period ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/businessinvoice')->createLogger('businessinvoice');
    }
    // setting up start and end date parameters
    public function setUp()
    {
        $this->log->info('Execuing cron Business Account SubOrdinate Update capacity.');

        $this->partnerId = $this->option('partnerId') ?: 0;
        if ($this->partnerId <= 0) {
            throw new Exception("Please Enter Partner ID");
        }
        $this->facilityId = $this->option('facilityId') ?: 0;
        if ($this->facilityId <= 0) {
            throw new Exception("Please Enter Facility");
        }
      
        $this->log->info('Execuing cron for Business Account SubOrdinate Update capacity for permit expired after grace for partner : ' . $this->partnerId );
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            $this->setUp();
            $finalCodes5 = [];
            $checkinArray = [];

            $this->log->info('PAEBR 11.');
           
            QueryBuilder::setCustomTimezone($this->facilityId);
            
            $desirdEndDate = Carbon::now()->subDays(5)->format('Y-m-d');

            $monthlyRequest = PermitRequest::whereDate('desired_end_date', '=', $desirdEndDate)
            ->whereNull('cancelled_at')
            ->whereNull('deleted_at')
            ->whereNotNull('business_id')
            ->where('business_id', '>', 0)
            ->whereNotIn('account_number', [********,********,********,********,********,********,********,********,********,********,********])
            ->where('partner_id',$this->partnerId)
            ->get();
		    if($monthlyRequest && count($monthlyRequest) > 0){
                foreach($monthlyRequest as $val){
                    echo $permitId = $val->id." : ". $val->desired_end_date." : ".$val->business_id." : ".$val->permit_rate_id." : ";
                    if(isset($val->business_id) && !is_null($val->business_id) && $val->business_id > 0){
                        $user_permit_remaning = UserPermitTypeMapping::where('user_id', $val->business_id)->where('permit_type_id',$val->permit_rate_id)->first();
                        if (isset($user_permit_remaning->remaining_capacity)) {
                            echo $user_permit_remaning->id." : ".$user_permit_remaning->remaining_capacity;
                            $user_permit_remaning->remaining_capacity = $user_permit_remaning->remaining_capacity + 1;
                            $user_permit_remaning->save();
                        }
                    }
                    echo PHP_EOL;
                }
            }else{
                echo "No data found";
            }

        } catch (\Throwable $th) {
            //throw $th;
            //echo "catch".$th->getMessage();die;
            $this->log->error('WLA Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine());
            $errorMessage = 'WLA Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine();
            $data['partner_key'] = "DIAMOND";
            $data['exception'] = "Exception in Business Account SubOrdinate Update capacity for permit expired after grace " . $errorMessage;
            Mail::send('email-exception', $data, function ($message) {
                $message->to(config('email_exceptions'));
                $message->subject("Email Exception :- Business Account Subordinate Update capacity");
                $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
            });
        }
    }

    
}
