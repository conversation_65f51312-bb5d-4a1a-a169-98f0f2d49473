<?php

namespace App\Console\Commands\Are;

use DB;
use Exception;
use Illuminate\Console\Command;

use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Http\Helpers\QueryBuilder;

use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\Diamond\PermitRenewal;
use App\Models\ParkEngage\HeartlandPaymentProfile;

/**
 * Emails reservation stub to user
 */
class DiamondPermitRenew extends Command
{
    use DispatchesJobs;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'diamond:permit-renewal {partner_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly permit renew.';
    protected $log;

	const PARTNER_ID = '3156';
	const FACILITY_ID = '126'; 
    const QUEUE_NAME = 'diamond-permit-renew-list';
	
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/are/permitRenewal')->createLogger('autorenew');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {   //dd('I am here now');
        try{ 

            $partnerIdArg = $this->argument('partner_id');
            
            if($partnerIdArg){
                $partnerId = $this->argument('partner_id');
                $facilityId = self::FACILITY_ID;
            }else{
                $facilityId = self::FACILITY_ID;
                $partnerId = self::PARTNER_ID;
            }
            
            QueryBuilder::setCustomTimezone($facilityId);
            
            $permit_end_date = date('Y-m-d', strtotime('last day of previous month'));
            $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));
            
            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F",$time);
            $PermitRenewYear = date("Y",$time);
            // ->where('id','4977')
            //dd($partnerId,$facilityId,$permit_end_date,$permit_start_date,'null','null',1,'not null');
            $monthlyRequest = PermitRequest::from('permit_requests')
                                            ->leftJoin('heartland_payment_profile as h', 'h.user_id', '=', 'permit_requests.user_id')
                                            ->select('permit_requests.id','permit_requests.facility_id','permit_requests.user_id','permit_requests.partner_id','permit_requests.account_number','permit_requests.permit_rate_id',
                                            'permit_requests.permit_rate','permit_requests.permit_final_amount','h.token','h.is_default')
                                            ->where('permit_requests.partner_id',$partnerId)
                                            ->where('permit_requests.facility_id',$facilityId)
                                            ->whereDate('permit_requests.desired_end_date', '<=', $permit_end_date)
                                            ->whereDate('permit_requests.desired_start_date', '>=', $permit_start_date)
                                            ->whereNull('permit_requests.cancelled_at')
                                            ->where('permit_requests.user_consent','1')
                                            ->whereNotNull('permit_requests.anet_transaction_id')
                                            ->where('h.is_default','1')
                                            ->get();
            // dd($monthlyRequest,$partnerId,$facilityId,$permit_end_date,$permit_start_date,'null','null',1,'not null');
            if (!$monthlyRequest) {
                $this->log->info("Monthly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($monthlyRequest));
            $count = 0;
            foreach($monthlyRequest as $key=>$val){
                $payment_profile = HeartlandPaymentProfile::select('token', DB::raw('count(token) as token_count'))
                ->where('token', $val->token)
                ->groupBy('token')
                ->havingRaw('count(token) > 1')
                ->first();
              
                if($payment_profile && ($payment_profile->token_count > 1)){
                    $job = (new PermitRenewal($val))->onQueue(self::QUEUE_NAME)->delay(30); 
                }else{
                    $job = (new PermitRenewal($val))->onQueue(self::QUEUE_NAME)->delay(3); 
                }
                $this->dispatch($job);   
                		    
                $response['status'] = true;
			    $response['message'] = 'Permit Data added in Queue Succesfully';
                $this->log->info("Permit Data Pushed to queue" . json_encode($response));
                $count++;	
            }
            $msg = "Total Count of Permit Renew: ".$count;
            $this->log->info($msg);
            return $msg;
        }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
        
    }

}
