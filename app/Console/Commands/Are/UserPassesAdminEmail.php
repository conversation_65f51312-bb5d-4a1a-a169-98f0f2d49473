<?php

namespace App\Console\Commands\Are;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\Reservation;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\AreQrcode;
use Illuminate\Support\Collection;
use Storage;
use DB;
use App\Services\Image;
use App\Services\Pdf;
use App\Models\UserPass;


/**
 * Emails reservation stub to user
 */
class UserPassesAdminEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'diamonduserpassesadmin:email {permit_request_id}/{request}/{is_admin}/{pwd}/{userexist}';

 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email success to users on creating pass by admin.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/are')->createLogger('areuserpassadmin');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $request_id = $this->argument('permit_request_id'); 
        $is_admin = $this->argument('is_admin');   
        $request = $this->argument('request');
        $userexist = $this->argument('userexist');
        $pwd = $this->argument('pwd');
        $passRequest = UserPass::with(['facility','user'])->where('id',$request_id)->first();
        if (!$passRequest) {
            throw new NotFoundException('No user with that ID.');
        }

        $passRequest['pwd'] = $pwd;
        $this->generateBarcodeJpgNew($passRequest->pass_code);
        $imageBarcodeFileName = str_random(10) . '_userpassqrcode.png';
        Storage::put($imageBarcodeFileName, $this->generateBarcodeJpgNew($passRequest->pass_code));                   
        $passRequest['qrcodeimg'] = $imageBarcodeFileName;
        $passRequest['qrcode'] = $passRequest->pass_code;
        $passRequest['loginurl'] = env('WEB_URL_ARE');  
        $passRequest['userexist'] = $userexist; 

        


        // $brand_setting = BrandSetting::where('user_id', $monthlyRequest->partner_id)->first();

      //  $view='diamond.permit-email';
        if($is_admin==1)
        {
          $view='are.are-userpasscreatedadmin';        
        }
        else{
          $view='are.are-userpasscreated';
        }
     
        $view_text=$view.'-plain-text';
        if(!View::exists($view_text))
        {
            $view_text=$view;
        } 
         try{ 
            $this->log->info("Pass creation user email sending: $passRequest->email");
             Mail::send(
                ['text'=>$view_text,'html'=>$view],
                ['data' => $passRequest],
                function ($message) use ($passRequest) {
                    $message->to($passRequest->email)->subject("Your pass has been created");
                    $message->from(config('parkengage.default_sender_email'));
                    //$message->attachData($pdf,$pdfName.".pdf");      

                }
             );             
            $this->log->info("email sent: $passRequest->email");
        }catch (\Exception $e)
        {
            $errorMessage=array();
            $errorMessage['message']=$e;
            $this->log->error('Issue in email sending:',$errorMessage);
        }       

    }
    
    public function getDaySufixFormat($date){
        $day = date('d', strtotime($date));
        $monthYear = date('M, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
          $second_last_digit = substr($number, -2, 1);
          $suffix = 'th';
          if ($second_last_digit != '1')
          {
            switch ($last_digit)
            {
              case '1':
                $suffix = 'st';
                break;
              case '2':
                $suffix = 'nd';
                break;
              case '3':
                $suffix = 'rd';
                break;
              default:
                break;
            }
          }
          if ((string) $number === '1') $suffix = 'st';
         // return $number.$suffix . ' '.$monthYear;
          return $number. ' '.$monthYear;
    }



    public function generateBarcodeJpgNew($qrcode)
    {   
       $html = $this->generateBarcodeHtml($qrcode);

        $image = app()->make(Image::class);
        $image->setOption('width', '420');
        return $image->getOutputFromHtmlString($html);
    }

    public function generateBarcodeHtml($qrcode)
    {
        return view('mapco.Qrcodegenerate', ['barcode' => $qrcode]);
        
    }

}
