<?php

namespace App\Console\Commands\Are;

use Mail;
use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\Ticket;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\SendEmail;
use Illuminate\Support\Collection;

class CheckinEmailSend extends Command
{

  use DispatchesJobs;
  
    protected $signature = 'are:chkin-are-email-send {id}/{email}';
    protected $description = 'Check in/out Email Send to user.';
    const QUEUE_NAME = 'send-email';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/are/checkinout')->createLogger('checkinoutemailsend');
    }

    public function handle()
    {
      $id = $this->argument('id');
      $email = $this->argument('email'); 
      $subject = 'Check in/out Details'; 

      $dataRequest = Ticket::with(['transaction','facility','user'])->where('id',$id)->first();
      
      $this->log->info("Queue start  $email");
      $this->dispatch((new SendEmail($dataRequest,'are.chkinchekoutdetailsemail',$email,$subject,$this->log))->onQueue(self::QUEUE_NAME));
      $this->log->info("Queue end  $email");
    }    
    
}
