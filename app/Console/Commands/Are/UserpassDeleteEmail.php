<?php

namespace App\Console\Commands\Are;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\UserPass;



/**
 * Emails reservation stub to user
 */
class UserpassDeleteEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'are:delete-userpass-email {id}';

 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email to user for User Pass Deletion.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/are/')->createLogger('cancel');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $id = $this->argument('id');
        
        //$passRequest = UserPass::with(['facility','user'])->where('id',$id)->first();
        $passRequest = UserPass::with(['user','facility'])->withTrashed()->where('id',$id)->first();

        if (!$passRequest) {
            throw new NotFoundException('No user with that ID.');
        }
        //return $passRequest;

        $view='are.delete-userpass-email';
        $view_text=$view.'-plain-text';
        if(!View::exists($view_text))
        {
            $view_text=$view;
        } 
         try{ 
            $this->log->info("Userpass Cancel email sending: $passRequest->email");
             Mail::send(
                ['text'=>$view_text,'html'=>$view],
                ['data' => $passRequest],
                function ($message) use ($passRequest) {
                    $message->to($passRequest->email)->subject("Your User Pass has been canceled");
                    $message->from(config('parkengage.default_sender_email'));
                }
             );             
            $this->log->info("email sent: $passRequest->email");
        }catch (\Exception $e)
        {
            $errorMessage=array();
            $errorMessage['message']=$e;
            $this->log->error('Issue in email sending:',$errorMessage);
        }       

    }
    
    public function getDaySufixFormat($date){
        $day = date('d', strtotime($date));
        $monthYear = date('M, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
          $second_last_digit = substr($number, -2, 1);
          $suffix = 'th';
          if ($second_last_digit != '1')
          {
            switch ($last_digit)
            {
              case '1':
                $suffix = 'st';
                break;
              case '2':
                $suffix = 'nd';
                break;
              case '3':
                $suffix = 'rd';
                break;
              default:
                break;
            }
          }
          if ((string) $number === '1') $suffix = 'st';
         // return $number.$suffix . ' '.$monthYear;
          return $number. ' '.$monthYear;
    }
}
