<?php

namespace App\Console\Commands\Are;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\Reservation;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\AreQrcode;
use Illuminate\Support\Collection;
use Storage;
use DB;
use App\Services\Image;
use App\Services\Pdf;


/**
 * Emails reservation stub to user
 */
class VehicleDetailsEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vehicledetailscheckin:email {vehicletype}/{request}/{vehicleinfo}';

 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email success to monthly permit user.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/are')->createLogger('vehicleblackbolo');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $vehicletype = $this->argument('vehicletype'); 
        $request = $this->argument('request'); 
        //print_r($request);
        $lnum = $request['license_number'];


        
        //$monthly_request_id = '';
        $monthlyRequest = (object) array("name"=>"Hello", "email"=>"<EMAIL>", "msg"=>"This Vehicle is ".$vehicletype. ' belong to this License Number: '.$lnum);


        //$monthlyRequest = PermitRequest::with(['facility','user'])->where('id',$monthly_request_id)->first();

        //$monthlyRequest->email='<EMAIL>';
        //print_r($monthlyRequest);
       // echo $monthlyRequest['email'];

        // echo $monthlyRequest->email;
        //die;

    

      //  $view='diamond.permit-email';
      $view='are.vehicle-details-admin-email';
        $view_text=$view.'-plain-text';
        if(!View::exists($view_text))
        {
            $view_text=$view;
        } 
         try{ 
            $this->log->info("Check Vehicle email sending: $monthlyRequest->email");
             Mail::send(
                ['text'=>$view_text,'html'=>$view],
                ['data' => $monthlyRequest],
                function ($message) use ($monthlyRequest) {
                    $message->to($monthlyRequest->email)->subject("Vehicle Checked Info");
                    $message->from(config('parkengage.default_sender_email'));
                    //$message->attachData($pdf,$pdfName.".pdf");      

                }
             );             
            $this->log->info("email sent: $monthlyRequest->email");
        }catch (\Exception $e)
        {
            $errorMessage=array();
            $errorMessage['message']=$e;
            $this->log->error('Issue in email sending:',$errorMessage);
        }       

    }
  


   

}
