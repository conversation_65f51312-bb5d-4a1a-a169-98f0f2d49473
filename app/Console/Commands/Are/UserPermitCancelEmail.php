<?php

namespace App\Console\Commands\Are;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\UserPermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Http\Helpers\MailHelper;


/**
 * Emails reservation stub to user
 */
class UserPermitCancelEmail extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'user-permit-cancel-email {permit_request_id}';


  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Email cancele to permit user.';


  public function __construct(LoggerFactory $logFactory)
  {
    parent::__construct();
    $this->log = (new LoggerFactory)->setPath('logs/userPermit/')->createLogger('cancel');
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
    $monthly_request_id = $this->argument('permit_request_id');
    //$type_id = $this->argument('type_id');
    $monthlyRequest = UserPermitRequest::with(['facility', 'user'])->where('id', $monthly_request_id)->first();

    if (!$monthlyRequest) {
      throw new NotFoundException('No user with that ID.');
    }

    $monthlyRequest['desired_start_date'] = $this->getDaySufixFormat($monthlyRequest->desired_start_date);
    $monthlyRequest['desired_end_date'] = $this->getDaySufixFormat($monthlyRequest->desired_end_date);
    $monthlyRequest['vehicle'] = PermitVehicle::where('permit_request_id', $monthly_request_id)->get();
    $brand_setting = BrandSetting::where('user_id', $monthlyRequest->partner_id)->first();
    $partnerDetails = User::where('id', $monthlyRequest->partner_id)->first();


    //  $view='diamond.permit-email';
    if ($monthlyRequest->facility_id == config('parkengage.WAILUKU_FACILITY')) {
      $view = 'are.user-permit-cancel';
    } else {
      $view = 'are.user_permit_cancel_common';
    }

    $view_text = $view . '-plain-text';
    if (!View::exists($view_text)) {
      $view_text = $view;
    }
    try {
      $this->log->info("Permit cancel email sending: $monthlyRequest->email");

      MailHelper::sendEmail($monthlyRequest->email, $view_text, ['subject' => "Your permit booking has been canceled", 'data' => $monthlyRequest, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails], $monthlyRequest->partner_id);

      //  Mail::send(
      //     ['text'=>$view_text,'html'=>$view],
      //     ['data' => $monthlyRequest, 'brand_setting' => $brand_setting,'partner_details' => $partnerDetails],
      //     function ($message) use ($monthlyRequest) {
      //         $message->to($monthlyRequest->email)->subject("Your permit booking has been canceled");
      //         $message->from(config('parkengage.default_sender_email'));
      //     }
      //  );             
      $this->log->info("email sent: $monthlyRequest->email");
    } catch (\Exception $e) {
      $errorMessage = array();
      $errorMessage['message'] = $e;
      $this->log->error('Issue in email sending:', $errorMessage);
    }
  }

  public function getDaySufixFormat($date)
  {
    $day = date('d', strtotime($date));
    $monthYear = date('M, Y', strtotime($date));
    $number = (string) $day;
    $last_digit = substr($number, -1);
    $second_last_digit = substr($number, -2, 1);
    $suffix = 'th';
    if ($second_last_digit != '1') {
      switch ($last_digit) {
        case '1':
          $suffix = 'st';
          break;
        case '2':
          $suffix = 'nd';
          break;
        case '3':
          $suffix = 'rd';
          break;
        default:
          break;
      }
    }
    if ((string) $number === '1') $suffix = 'st';
    // return $number.$suffix . ' '.$monthYear;
    return $number . ' ' . $monthYear;
  }
}
