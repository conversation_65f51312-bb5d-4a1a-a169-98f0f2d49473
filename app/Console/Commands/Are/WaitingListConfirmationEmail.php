<?php

namespace App\Console\Commands\Are;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\WaitingList;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\Facility;
use App\Models\ParkEngage\UserPaymentGatewayDetail;

/**
 * Emails reservation stub to user
 */
class WaitingListConfirmationEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'arewaitinglist:email {waiting_request_id}';

 
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Email success to waiting list user.';


    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/are/waitinglistuser')->createLogger('are');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $request_id = $this->argument('waiting_request_id');
        $waitlistRequest = WaitingList::with(['facility','user'])->where('id',$request_id)->first();
        if (!$waitlistRequest) {
           // throw new NotFoundException('No user with that ID.');
            $this->log->info("No user with that ID.");
            return 1;
        }
		$facility = Facility::where('id',$waitlistRequest->facility_id)->first();
        
        // get Partner Slug
        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($waitlistRequest) {
            $join->on('user_facilities.user_id', '=', 'users.id');
            $join->where('user_facilities.facility_id', "=", $waitlistRequest->facility_id);
        })->where('created_by', $waitlistRequest->partner_id)->whereNotNull('slug')->where('user_type', 12)->first();
          
        if($getRM){
            $partnerSlug = $getRM->slug;
        }else{
            $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $waitlistRequest->partner_id)->first();
            $partnerSlug  =  $checkPaymentUrl->touchless_payment_url;
        }

        if($waitlistRequest->facility_id == config('parkengage.WAILUKU_FACILITY')){

        }else{
          $brand_setting = BrandSetting::where('user_id', $waitlistRequest->partner_id)->first();
        }
        $partnerDetails = User::where('id',$waitlistRequest->partner_id)->first();
        if($waitlistRequest->partner_id == config('parkengage.PARTNER_INTRAPARK')){	
            $view='intrapark.are-waiting';
        }else if($waitlistRequest->partner_id == config('parkengage.PARTNER_UNITED')){
            $view='united.are-waiting';
        }elseif(in_array($waitlistRequest->partner_id, config('parkengage.PARTNER_GROUP_SERVICES'))){ #pims-12258 dd	
            $view='usm.are-waiting';
        }else{
            $view='yenkee.are-waiting';
        }

        if($waitlistRequest->partner_id == config('parkengage.PARTNER_PAVE')){
            $view='pave-permit.are-waiting';
        }

        if($waitlistRequest->facility_id != config('parkengage.WAILUKU_FACILITY') && $waitlistRequest->partner_id == config('parkengage.PARTNER_DIAMOND')){
            $view='diamond.diamond-waiting-list';
        }
        
        $view_text=$view.'-plain-text';
        if(!View::exists($view_text))
        {
            $view_text=$view;
        } 
        
		try{ 
            $this->log->info("Waiting List User Email Sending: $waitlistRequest->email");
             Mail::send(
                ['text'=>$view_text,'html'=>$view],
                ['data' => $waitlistRequest, 'brand_setting' => $brand_setting,'facility' => $facility,'partner_details' => $partnerDetails],
                function ($message) use ($waitlistRequest) {
                    $message->to($waitlistRequest->email)->subject("Permit Request: You are added in the waiting list.");
                    $message->from(config('parkengage.default_sender_email'));
                }
             );             
            $this->log->info("email sent: $waitlistRequest->email");
        }catch (\Exception $e)
        {
            $errorMessage=array();
            $errorMessage['message']=$e;
            $this->log->error('Issue in email sending:',$errorMessage);
        }
    }    
}
