<?php

namespace App\Console\Commands\Are;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Http\Helpers\MailHelper;

/**
 * Emails reservation stub to user
 */
class PermitCancelEmail extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'are:cancel-permit-email {permit_request_id}
                            {partner_id : optional partner id}';


  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Email cancele to monthly permit user.';


  public function __construct(LoggerFactory $logFactory)
  {
    parent::__construct();
    $this->log = (new LoggerFactory)->setPath('logs/are/')->createLogger('cancel');
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
    $monthly_request_id = $this->argument('permit_request_id');
    $partnerId = $this->argument('partner_id');

    //$type_id = $this->argument('type_id');
    $monthlyRequest = PermitRequest::with(['facility', 'user', 'PermitVehicle', 'penalityTransaction'])->where('id', $monthly_request_id)->first();
    $partner_id = isset($partnerId) ? $partnerId : $monthlyRequest->partner_id;

    if (!$monthlyRequest) {
      throw new NotFoundException('No user with that ID.');
    }

    $monthlyRequest['desired_start_date'] = $this->getDaySufixFormat($monthlyRequest->desired_start_date);
    $monthlyRequest['desired_end_date'] = $this->getDaySufixFormat($monthlyRequest->desired_end_date);
    $monthlyRequest['vehicle'] = PermitVehicle::where('permit_request_id', $monthly_request_id)->get();
    $brand_setting = '';

    $brand_setting = BrandSetting::where('user_id', $monthlyRequest->partner_id)->first();
    $partnerDetails = User::where('id', $partner_id)->first();

    #PIMS-10032 dushyant phase2
    if (is_null($monthlyRequest->business_id) || empty($monthlyRequest->business_id)) {
    } else {
      $new_permit_amt = ($monthlyRequest->negotiated_amount ?? $monthlyRequest->permit_final_amount);
      $monthlyRequest->permit_rate = $new_permit_amt;
    }

    $permit_services = PermitRequestServiceMapping::where('permit_request_id', $monthlyRequest->id)->pluck('permit_service_id');

    $services  = PermitServices::with([
      'permit_service_criteria_mappings' => function ($query) use ($permit_services) {
        $query->whereIn('permit_service_id', $permit_services)
          ->with('criteria');
      }
    ])
      ->select('permit_services.*')
      ->whereIn('id', $permit_services)
      ->orderBy('permit_services.id', 'asc')
      ->get();

    if (count($services) > 0) {
      $services = $this->formatPermitServiceCriteria($services);
    }

    #PIMS-12258 DD
    if ($monthlyRequest->partner_id == config('parkengage.PARTNER_USM')) {
      $partner_name = "USM Parking";
    } else if ($monthlyRequest->partner_id == config('parkengage.PARTNER_MANLO')) {
      $partner_name = "MENLO Parking";
    } else {
      $partner_name = "Parking Services";
    }

    if ($monthlyRequest->facility_id == config('parkengage.DEPASQUALE_FACILITY')) {
      $view = 'united.are-cancel';
    } else if ($monthlyRequest->facility_id == config('parkengage.USM_FACILITY')) {
      $view = 'usm.are-cancel';
    } else if ($monthlyRequest->facility_id == config('parkengage.153rd_Street_Parking_FACILITY')) {
      $view = 'yenkee.are-cancel';
    } else if ($monthlyRequest->facility_id == config('parkengage.WAILUKU_FACILITY')) {
      $view = 'are.are-cancel';
    } else if ($monthlyRequest->facility_id != config('parkengage.WAILUKU_FACILITY') && $monthlyRequest->partner_id == config('parkengage.PARTNER_DIAMOND')) {
      $view = 'are-diamond.are-cancel';
    } else {
      $view = 'yenkee.are-cancel';
    }

    if ($monthlyRequest->partner_id == config('parkengage.PARTNER_INTRAPARK')) {
      $view = 'intrapark.are-cancel';
    }
    if ($monthlyRequest->partner_id == config('parkengage.PARTNER_PAVE')) {
      $view = 'pave-permit.are-cancel';
    }

    if ($monthlyRequest->partner_id == config('parkengage.PARTNER_PCI')) {
      $view = 'kstreet.are-cancel';
    }

    $view_text = $view . '-plain-text';
    if (!View::exists($view_text)) {
      $view_text = $view;
    }
    try {
      $this->log->info("Permit cancel email sending: $monthlyRequest->email");
      MailHelper::sendEmail($monthlyRequest->email, $view_text, ['subject' => "Your permit booking has been canceled", 'data' => $monthlyRequest, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'services' => $services,  'partner_name' => $partner_name], $monthlyRequest->partner_id);
      //  Mail::send(
      //     ['text'=>$view_text,'html'=>$view],
      //     ['data' => $monthlyRequest, 'brand_setting' => $brand_setting,'partner_details' => $partnerDetails,'services' => $services,'partner_name'=>$partner_name],
      //     function ($message) use ($monthlyRequest) {
      //         $message->to($monthlyRequest->email)->subject("Your permit booking has been canceled");
      //         $message->from(config('parkengage.default_sender_email'));
      //     }
      //  );             
      $this->log->info("email sent: $monthlyRequest->email");
    } catch (\Exception $e) {
      $errorMessage = array();
      $errorMessage['message'] = $e;
      $this->log->error('Issue in email sending:', $errorMessage);
    }
  }

  public function getDaySufixFormat($date)
  {
    $day = date('d', strtotime($date));
    $monthYear = date('M, Y', strtotime($date));
    $number = (string) $day;
    $last_digit = substr($number, -1);
    $second_last_digit = substr($number, -2, 1);
    $suffix = 'th';
    if ($second_last_digit != '1') {
      switch ($last_digit) {
        case '1':
          $suffix = 'st';
          break;
        case '2':
          $suffix = 'nd';
          break;
        case '3':
          $suffix = 'rd';
          break;
        default:
          break;
      }
    }
    if ((string) $number === '1') $suffix = 'st';
    // return $number.$suffix . ' '.$monthYear;
    return $number . ' ' . $monthYear;
  }

  private function formatPermitServiceCriteria($services)
  {
    $daysMap = [
      1 => 'Sun',
      2 => 'Mon',
      3 => 'Tue',
      4 => 'Wed',
      5 => 'Thu',
      6 => 'Fri',
      7 => 'Sat'
    ];
    $allDays = range(1, 7);

    $i = 0;
    foreach ($services as $service) {
      if (count($service->permit_service_criteria_mappings) > 0) {
        $formatted = [];
        foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
          $item = $permit_service_criteria_mapping->criteria;
          $days = explode(',', $item->days);
          sort($days); // Sort days to match the sequence of $allDays
          if ($days == $allDays) {
            $dayNamesStr = 'All Days';
          } else {
            $dayNames = array_map(function ($day) use ($daysMap) {
              return $daysMap[$day];
            }, $days);
            $dayNamesStr = implode(',', $dayNames);
          }

          $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
          $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
          $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

          // Adjust exit time if it's greater than 24 hours
          $exit_time = explode(":", $item->exit_time_end);
          if ($exit_time[0] > 23) {
            $next_hr = $exit_time[0] - 24;
            $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
            $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
            $exit_time_overflow = ' (next day)';
          } else {
            $exit_time_overflow = '';
            $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
          }


          $formatted[] = [
            'days' => $dayNamesStr,
            'entry_time_begin' => $entry_time_begin,
            'entry_time_end' => $entry_time_end,
            'exit_time_begin' => $exit_time_begin,
            'exit_time_end' => $exit_time_end . $exit_time_overflow,
          ];
        }
        $services[$i]->criteria = $formatted;
      }
      $i++;
    }
    return $services;
  }
}
