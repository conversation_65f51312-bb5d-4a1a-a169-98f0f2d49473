<?php

namespace App\Console\Commands\Evalutions;

use Mail;
use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Http\Helpers\ReportBuilder;
use Illuminate\Http\Request;

use App\Http\Controllers\ParkEngage\ReportParkEngage;
use App\Services\LoggerFactory;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class BankDeposit extends Command
{

    protected $signature = "evalution:bank-deposit-report";


    protected $description = 'Bank Deposit Report';

    protected $log;

    const PARTNER_ID = 215900;
    protected $request;
    protected $loggerFactory;
    protected $authNet;
    protected $cim;
    protected $reportBuilder;
    private $startOfMonth;
    private $endOfMonth;
    protected $partnerId;
    protected $facilityId;


    function __construct(Request $request, LoggerFactory $LoggerFactory, ReportBuilder $reportBuilder)
    {
        parent::__construct();
        $this->startOfMonth = Carbon::yesterday()->format('Y-m-d') . ' 00:00:00';
        $this->endOfMonth = Carbon::yesterday()->format('Y-m-d') . ' 23:59:59';
        $request->replace([]);
        $this->loggerFactory = $LoggerFactory;
        $this->request = $request;
        $this->reportBuilder = $reportBuilder;
    }

    public function handle(Request $request)
    {
        try {

            $user = User::find(272564);  // Replace  with the ID of the user you want to authenticate
            Auth::login($user);  // Log the user in
            Auth::user();  // 

            $request->merge([
                'partner_id' => self::PARTNER_ID,
                'fromDate' => $this->startOfMonth,
                'toDate' => $this->endOfMonth,
                'facility_id' => '300',
                'email_send' => 1,
                'filename' => 1,
            ]);
            Log::info('cron start  for ravpass :  ' . $request->facility_id);
            $controller = new ReportParkEngage($request,  $this->loggerFactory, $this->reportBuilder);
            $getDetails =   $controller->bankDepositReconciliation($request);
            dd($getDetails);
            if (isset($getDetails) && !empty($getDetails)) {
                $excelName = $getDetails[1];
                $excelpath = $getDetails[0];
                $revenueData = $getDetails[2];
                $data['totalTickets']   = $revenueData['totalTickets'];
                $data['netValue']       = $revenueData['totalRevenue'];
                $data['overAllTotal']   = $revenueData['totalRevenue'];
                $data['report_name']    = 'daily_revenue_report';
                $data['location_name']  = "Metro @ Showplace Square Garage";
                $data['mail_body']      = "PFB summary of today’s Revenue Report. A detailed report is also attached.";
                Mail::send('daily-report-email.daily-email-format', $data, function ($message)  use ($excelName, $excelpath, $revenueData) {
                    //$message->to(['<EMAIL>']);
                    //$message->to(config('parkengage.townsend.metro_report_emails'));
                    $message->to(['<EMAIL>', '<EMAIL>', '<EMAIL>',]);
                    $dateRange =  date("d F, Y", strtotime($this->startOfMonth)) . '-' . date("d F, Y", strtotime($this->endOfMonth));
                    $subject = ' Daily Revenue Report - ' . $dateRange;
                    $message->subject($subject);
                    $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                    $path_to_file = $excelpath;
                    if (file_exists($path_to_file)) {
                        $message->attach($path_to_file);
                    }
                    Log::info('Mail sent success for Metro :  ' . $path_to_file);
                });
            } else {
                $data['totalTickets']   = 0;
                $data['netValue']       = 0;
                $data['overAllTotal']   = 0;
                $data['report_name']    = 'daily_revenue_report';
                $data['location_name']  = "Metro @ Showplace Square Garage";
                $data['mail_body']      = "As no tickets were checked-out, the revenue report could not be generated.";
                Mail::send('daily-report-email.daily-email-format', $data, function ($message) {
                    $message->to(['<EMAIL>', '<EMAIL>', '<EMAIL>']);
                    $dateRange =  date("d F, Y", strtotime($this->startOfMonth)) . '-' . date("d F, Y", strtotime($this->endOfMonth));
                    $message->subject('Daily Revenue Report - ' . $dateRange);
                    $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));

                    Log::info('No transaction report email sent for Metro.');
                });
            }
            Auth::logout();
        } catch (\Throwable $th) {
            $msg = "Error Message: " . $th->getMessage() . ", File: " . $th->getFile() . ", Line Number: " . $th->getLine();
            Log::info($msg);
        }
    }
}
