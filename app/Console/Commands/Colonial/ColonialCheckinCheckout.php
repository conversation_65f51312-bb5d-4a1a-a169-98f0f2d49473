<?php

namespace App\Console\Commands\Colonial;

use Mail;
use Exception;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use App\Models\Ticket;
use App\Classes\DatacapPaymentGateway;
use App\Models\Rate;
use Carbon\Carbon;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;

class ColonialCheckinCheckout extends Command
{

    protected $signature = 'colonial:checkin-checkout';

    protected $description = 'close all the open ticket which not closed after event';

    protected $log;

    const POLICY_ID = '75';
	const PAID_TYPE = '0';
	const AFFILIATE_BUSINESS_ID = '70';

    protected $currentTime;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/parkengage/cron-checkin-checkout')->createLogger('colonial-checkin-checkout');
    }

    public function handle()
    {
        $facility_id = 158;
        $this->log->info("checkout start");
        try {
            $today = date("Y-m-d");
            $days = config('parkengage.COLONIAL_TICKET_CHECKOUT_DAYS');
            $backDate = date("Y-m-d", strtotime("$today -".$days." days"));
            $ticketData = Ticket::with(['facility.FacilityPaymentDetails', 'user'])->whereDate('checkin_time', '<', $today)->where(['facility_id' => $facility_id, 'is_checkout' => '0'])->get();
            //->whereNotNull("anet_transaction_id")->WhereNotNull("reservation_id")
            if (count($ticketData) > 0) {
                $this->log->info("Before checkout email about to send");
                //user mail
                foreach ($ticketData as $val) {
                    $checkoutTime = date("Y-m-d H:i:s");
                    if ($val->anet_transaction_id != '' || $val->reservation_id != '' || $val->event_id != '') {
                        $status = Ticket::where(['ticket_number' => $val->ticket_number, 'facility_id' => $facility_id])->update(['is_checkout' => '1', "checkout_remark" => "Pending checkouts; closing by Backend Process", "checkout_time" => date("Y-m-d H:i:s")]);
                        $this->log->info("Checkout update for Ticket No " . $val->ticket_number);
                    }else{
                        $this->log->info("Ticket No " . $val->ticket_number);
                        $checkinTime = date("Y-m-d", strtotime($val->checkin_time));
                        if($val->session_id != '' && $val->payment_token != '' && (strtotime($checkinTime) >= strtotime($backDate))){
                            $ticket = $val;
                            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $val->checkin_time);
                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                            $diff_in_hours = $val->getCheckinCheckOutDifference($checkoutTime);
                            //$this->log->error($arrival_time.'--'. $diff_in_hours.'--'. $ticket->checkin_time. '--'.$checkoutTime);
                            $isMember = 0;
                            if ($val->facility->is_hourly_rate) {
                                $this->log->error("worldport case");
                                $rate = $val->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
                            } else {
                                $this->log->error("townsnend case");
                                $rate = $val->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                            }

                            //check daily max price
                            $dailyMaxRate = Rate::where("category_id", config('parkengage.dailyMaxCategoryid'))->where("facility_id", $val->facility->id)->where("active", '1')->first();
                            if(isset($dailyMaxRate->id)){
                                if($dailyMaxRate->price < $rate['price']){
                                    $rate['id'] = $dailyMaxRate->id;
                                    $rate['description'] = $dailyMaxRate->description;
                                    $rate['price'] = $dailyMaxRate->price;
                                }
                            }
                            $priceBreakUp = $val->priceBreakUp($rate);
                            $this->log->info("priceBreakUp " . json_encode($priceBreakUp));
                            $ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $val->facility->FacilityPaymentDetails->datacap_script_url;
                            $datacap['Amount'] = $priceBreakUp['payable_amount'];
                            $datacap['Token'] = $val->payment_token;
                            $datacap['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);

                            $this->log->info("datacap Payment Response --" . json_encode($paymentResponse));

                            if (in_array($paymentResponse['Status'], ['Approved', 'Approval', 'Success'])) {
                                
                                $user_id = $val->user_id;
                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($val, $paymentResponse, $user_id, '');

                                $planetTransaction->payment_last_four = $val->card_last_four;
                                $planetTransaction->expiration = $val->expiry;
                                $planetTransaction->total = $priceBreakUp['payable_amount'];
                                $planetTransaction->save();
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $ticket->anet_transaction_id = $planetTransaction->id;
                                

                                $ticket->processing_fee  = $priceBreakUp['processing_fee'];
                                $ticket->tax_fee        = $priceBreakUp['tax_rate'];
                                $ticket->parking_amount  = $priceBreakUp['parking_amount'];
                                $ticket->paid_amount     = $priceBreakUp['paid_amount'];
                                $ticket->discount_amount = $priceBreakUp['discount_amount'];
                                $ticket->grand_total     = $priceBreakUp['payable_amount'];
                                $ticket->total     = $priceBreakUp['total'];
                                $ticket->length     = $diff_in_hours;

                                $ticket->is_checkout = '1';
                                $ticket->checkout_time = $checkoutTime;
                                $ticket->checkout_datetime = $checkoutTime;
                                $ticket->estimated_checkout = $checkoutTime;
                                $ticket->checkout_session_id = $ticket->session_id;
                                $ticket->checkout_card_last_four = $ticket->card_last_four;
                                $ticket->checkout_expiry = $ticket->expiry;
                                $ticket->checkout_card_type = $ticket->card_type;
                                $ticket->checkout_mode = '5';
                                $ticket->is_cloud_payment = '1';
                                $ticket->is_transaction_status = '0';
                                $ticket->rate_id = isset($rate['id']) ? $rate['id'] : "" ;
                                $ticket->rate_description = isset($rate['description']) ? $rate['description'] : "" ;
                                $ticket->checkout_remark = "This ticket is closed by BE process with a capping of Daily Max price $".$priceBreakUp['payable_amount'];
                                $this->log->info(json_encode($ticket));
                                $val->save();

                            }else{
                                QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "Datacap", $ticket->ticket_number, 'ColonialAutoCheckout', $ticket->user_id, null, null);

                                $calculatedPayableAmount = $this->calculatePayablePrice($val);
                                $payable_amount = $calculatedPayableAmount['amount'];
                                $val->paid_by               =  $val->partner_id;
                                $val->total                 =  $payable_amount;
                                $val->paid_amount           = $payable_amount;
                                $val->paid_type             = self::PAID_TYPE;
                                $val->paid_remark           = 'Ticket closed by scheduled process';
                                $val->paid_date             =  $checkoutTime;
                                $val->policy_id             = self::POLICY_ID;
                                $val->affiliate_business_id = self::AFFILIATE_BUSINESS_ID;
                                $val->is_checkout           = '1';
                                $val->checkout_remark       = 'Pending checkouts; closing by Backend Process';
                                $val->checkout_time          = $checkoutTime;
                                $val->save();
                                //return true;
                            }
                        }elseif($val->device_type == 'SMS'){

                            $calculatedPayableAmount = $this->calculatePayablePrice($val);
                            $payable_amount = $calculatedPayableAmount['amount'];
                            $val->paid_by               =  $val->partner_id;
                            $val->total                 =  $payable_amount;
                            $val->paid_amount           = $payable_amount;
                            $val->paid_type             = self::PAID_TYPE;
                            $val->paid_remark           = 'Ticket closed by scheduled process';
                            $val->paid_date             =  $checkoutTime;
                            $val->policy_id             = self::POLICY_ID;
                            $val->affiliate_business_id = self::AFFILIATE_BUSINESS_ID;
                            $val->is_checkout           = '1';
                            $val->checkout_remark       = 'Pending checkouts; closing by Backend Process';
                            $val->checkout_time          = $checkoutTime;
                            $val->save();
                            // $status = Ticket::where(['ticket_number' => $val->ticket_number, 'facility_id' => $facility_id])
                            //         ->update(['is_checkout' => '1', "checkout_remark" => "Pending checkouts; closing by Backend Process", "checkout_time" => date("Y-m-d H:i:s")]);
                            $this->log->info("SMS Checkout update for Ticket No " . $val->ticket_number);
                        }else{

                            $calculatedPayableAmount = $this->calculatePayablePrice($val);
                            $payable_amount = $calculatedPayableAmount['amount'];
                            $val->paid_by               =  $val->partner_id;
                            $val->total                 =  $payable_amount;
                            $val->paid_amount           = $payable_amount;
                            $val->paid_type             = self::PAID_TYPE;
                            $val->paid_remark           = 'Ticket closed by scheduled process';
                            $val->paid_date             =  $checkoutTime;
                            $val->policy_id             = self::POLICY_ID;
                            $val->affiliate_business_id = self::AFFILIATE_BUSINESS_ID;
                            $val->is_checkout           = '1';
                            $val->checkout_remark       = 'Pending checkouts; closing by Backend Process';
                            $val->checkout_time          = $checkoutTime;
                            $val->save();
                            //$status = Ticket::where(['ticket_number' => $val->ticket_number, 'facility_id' => $facility_id])->update(['is_checkout' => '1', "checkout_remark" => "Pending checkouts; closing by Backend Process", "checkout_time" => date("Y-m-d H:i:s")]);
                            $this->log->info("Old Checkout update for Ticket No " . $val->ticket_number);
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            Mail::send(
                "mapco.alert-error-checkout",
                ['data' => $msg],
                function ($message) use ($msg) {
                    $message->to(['<EMAIL>'])->subject("Error : Doc 79 Cron Alert Checkout");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Queue ended");
        }
    }


    protected function calculatePayablePrice($val)
    {
		$isMember = 0;
		$tickeAmount = [];
        $tickeAmount['amount'] = $tickeAmount['length'] = 0;
        
        $this->setCustomTimezone($val->facility_id);
        $ticketObj = Ticket::find($val->id);
        
		if (!empty($ticketObj->payment_date)) {
            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->payment_date);
            $rateDiffInHour = $val->getCheckOutCurrentTime(true, $ticketObj->payment_date);
        } else {
            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->checkin_time);
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $rateDiffInHour = $ticketObj->getCheckOutCurrentTime(true);
        }


        if ($ticketObj->facility->rate_duration_in_hours > 0 && $ticketObj->facility->rate_per_hour > 0 && $ticketObj->facility->rate_free_minutes > 0 && $ticketObj->facility->rate_daily_max_amount > 0) {
            if (!empty($ticketObj->payment_date)) {
                $rate = $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, true, null, false, false, '0', $isMember);
            } else {
                $rate = $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
            }
            if ($ticketObj->is_checkin == '1' && $ticketObj->is_checkout == '0' && $ticketObj->checkout_time != '') {
                //zeag ticket
                $rate = [];
                $rate['price'] = $ticketObj->parking_amount;
            }
        } else {
            if (!empty($ticketObj->payment_date)) {
                $rate = $ticketObj->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, true,  false, true, false, 0, $isMember);
            } else {
                $rate = $ticketObj->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, $isMember);
            }
        }
        $taxFee = $ticketObj->facility->getTaxRate($rate);
        if (!empty($ticketObj->payment_date)) {
            $processingFee = '0.00';
        } else {
            $processingFee = $ticketObj->facility->getProcessingFee(0);
        }
        $parkingAmount = $rate['price'];
        $tickeAmount['amount'] = $parkingAmount > 0 ? $parkingAmount + $taxFee + $processingFee : '0.00';
        $tickeAmount['length'] = $rateDiffInHour;
        // dd($tickeAmount);
        return $tickeAmount;
    }


    public function setCustomTimezone($facility_id)
	{
	  	$facility = Facility::find($facility_id);
	  	date_default_timezone_set($facility->timezone);
        $this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
	  
	}

}
