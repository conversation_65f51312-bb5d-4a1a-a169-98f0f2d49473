<?php

namespace App\Console\Commands\United;

use Illuminate\Support\Facades\Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Classes\DatacapPaymentGateway;
use App\Models\PermitRequestRenewHistory;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Http\Helpers\QueryBuilder;

/**
 * Emails reservation stub to user
 */
class PermitAutoRenewUnitedHL extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'united:permit-renew-hl';
    protected $log;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly permit renew.';

    const PARTNER_ID = '7395';
    const FACILITY_ID = '133';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/united/')->createLogger('autorenew');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {

            $permit_end_date = date('Y-m-d', strtotime('last day of previous month'));
            $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));

            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F", $time);
            $PermitRenewYear = date("Y", $time);
            $PermitRenewDay = date("d", $time);

            $monthlyRequest = PermitRequest::with(['facility.FacilityPaymentDetails', 'user', 'PermitVehicle'])->where('partner_id', self::PARTNER_ID)->whereDate('desired_end_date', '<=', $permit_end_date)->whereDate('desired_start_date', '>=', $permit_start_date)->whereNull('cancelled_at')->whereNull('deleted_at')->where('user_consent', 1)->whereNotNull('anet_transaction_id')->whereNull('business_id')->orderBy('id', 'desc')->limit(1)->get();
            //  dd(count($monthlyRequest), $permit_start_date,$permit_end_date,self::PARTNER_ID,$monthlyRequest[0]->id,$monthlyRequest[0]->user_id,$monthlyRequest[0]->permit_rate); 
            if (!$monthlyRequest) {
                $this->log->info("Monthly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($monthlyRequest));
            // Create Permit Request History
            $count = 0;
            foreach ($monthlyRequest as $key => $val) {
                $permitRate = PermitRate::where('id', $val->permit_rate_id)->first();

                $this->log->info("Permit Rate Found" . json_encode($permitRate));
                if ($permitRate->rate == '0.00') {
                    dd('stop');
                    QueryBuilder::createPermitRequestHistoryNew($val);
                    $maxDays = date('t');
                    $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate);
                    $val->save();
                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                    if ($facility_brand_setting) {
                        $val->facility_logo_id = $facility_brand_setting->id;
                    } else {
                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                        $val->logo_id  = $brand_setting->id;
                    }
                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                    $view = "usm.permit-renew-free";
                    Mail::send(
                        $view,
                        ['data' => $val, 'brand_setting' => $brand_setting],
                        function ($message) use ($val) {
                            $message->to($val->user->email)->subject("Your Permit #" . $val->account_number . " has been Renewed Successfully");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Mail sent to " . $val->user->email);
                } else if ($permitRate->rate > "0.00") {

                    if ($permitRate) {
                        $final_amount = $permitRate->rate;
                        $permit_services = PermitRequestServiceMapping::where('permit_request_id',$val->id)->pluck('permit_service_id');                    
                        $services  = PermitServices::with([
                        'permit_service_criteria_mappings' => function($query) use ($permit_services) {
                            $query->whereIn('permit_service_id',$permit_services)
                            ->with('criteria');
                        }
                        ])
                        ->select('permit_services.*')
                        ->whereIn('id',$permit_services)
                        ->orderBy('permit_services.id', 'asc') 
                        ->get();
                        if ($services) {                            
                            foreach ($services as $permitService) {
                                $final_amount += (float) $permitService->permit_service_rate;
                            }
                        }                    
                        if(count($services) > 0){
                            $services = QueryBuilder::formatPermitServiceCriteria($services);
                        }
                        $rateValidate = QueryBuilder::permitRenewCalculation((object) [], $val, $final_amount, 1);
                        $final_amount = $rateValidate['total_amount'];
                        if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                            dd('stop');
                            $paymentProfile = PlanetPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            $amount = ($val->facility->FacilityPaymentDetails->planet_payment_env == 'test') ? '3.00' : $final_amount;
                            if ($paymentProfile) {
                                $val->session_id = $paymentProfile->token;
                                $paymentByToken = PlanetPaymentGateway::planetPaymentByToken($val, $amount, '');
                                $this->log->info("planet permit Payment Response :" . json_encode($paymentByToken));
                                if (!in_array($paymentByToken["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                    $this->log->info("planet Payment Failed Response");
                                    // Failure mail send to user
                                    #DD PIMS-11368	                 
                                    if (!isset($paymentByToken)) {
                                        $paymentByToken = NULL;
                                    }
                                    QueryBuilder::setAllFailedTransactions(json_encode($paymentByToken), "planet", $val->account_number, 'PermitAutoRenewUsm', $val->user_id);

                                    $val->transaction_retry   = $val->transaction_retry + 1;
                                    $val->save();
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = "City Parking Inc";
                                    if ($val->transaction_retry > '1') {
                                        $view = 'usm.permit-cancel-reminder';
                                    }
                                    $view = "usm.permit-renew-fail";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                        function ($message) use ($val) {
                                            //  $message->to([$val->user->email])->subject("Payment Failed against your Permit #". $val->account_number);
                                            $message->to([$val->user->email])->subject("Important: Action Required for Your Permit Payment");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                } else {
                                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($paymentByToken, $val->user_id);
                                    $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($planetTransaction));
                                    #DD PIMS-11368	
                                    if (isset($planetTransaction->id)) {
                                        QueryBuilder::setReferenceKey($planetTransaction->id, $val->account_number);
                                    }
                                    // QueryBuilder::setAllTransactions(json_encode($paymentByToken), "planet", $val->account_number,'PermitAutoRenewUsm',$val->user_id);

                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $maxDays = date('t');
                                    $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate,$rateValidate, $val->transaction_retry + 1,$authorized_anet_transaction->id,$amount);
                                    $val->save();
                                    //user mail
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = "City Parking Inc";
                                    $view = "usm.permit-renew";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                        function ($message) use ($val) {
                                            $message->to($val->user->email)->subject("Payment made successfully against Permit #" . $val->account_number);
                                            //$message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                            dd('stop');
                            $amount = ($val->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $final_amount;
                            $ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $val->facility->FacilityPaymentDetails->datacap_script_url;

                            $paymentProfile = DatacapPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                           /*  $processing_fee = $val->facility->permit_processing_fee;
                            $permit_rate = $permitRate->rate;
                            $final_amount = $processing_fee + $permit_rate;
                            $amount = $final_amount; */
                            //dd($amount,$processing_fee,$ecommerce_mid,$url);
                            if ($paymentProfile) {
                                $data['Token'] = $paymentProfile->token;
                                if ($amount > 0) {
                                    //$amount = number_format($amount, 2);
                                    $data['Amount'] = $amount;
                                    $data['Token'] = $paymentProfile->token;
                                    $data['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                    $data["CardHolderID"] = "Allow_V2";
                                    $this->log->info("Payment Request Data --" . json_encode($data) . "--" . json_encode($ecommerce_mid) . "--" . json_encode($url));
                                    //dd($data, $ecommerce_mid, $url);
                                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                    if ($paymentResponse["Status"] == "Error") {
                                        $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                        #DD PIMS-11368	                 
                                        if (!isset($paymentResponse)) {
                                            $paymentResponse = NULL;
                                        }
                                        QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "datacap", $val->account_number, 'PermitAutoRenewUsm', $val->user_id);
                                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card .");
                                        } else {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card.");
                                        }
                                    } else if ($paymentResponse["Status"] == "Declined") {
                                        #DD PIMS-11368	                 
                                        if (!isset($paymentResponse)) {
                                            $paymentResponse = NULL;
                                        }
                                        QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "datacap", $val->account_number, 'PermitAutoRenewUsm', $val->user_id);
                                        // Failure mail send to user
                                        //$val->card_last_four = $paymentProfile->card_last_four;
                                        //$val->card_name = $paymentProfile->card_name;
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $paymentProfile->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $paymentProfile->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $partnerDetails = User::where('id', $val->partner_id)->first();
                                        $partner_name = "City Parking Inc Services";
                                        $subject = "Important: Action Required for Your Permit Payment";
                                        $view = "usm.permit-renew-fail";
                                        /*
										Mail::send(
											$view, 
                                            ['data' => $val, 'brand_setting' => $brand_setting,'permitRate' => $permitRate,'paymentProfile'=>$paymentProfile,'partner_details' => $partnerDetails,'partner_name'=>$partner_name], 
                                            function ($message) use($val,$subject) {
                                                $message->to($val->user->email)->subject($subject);
                                            	$message->from(config('parkengage.default_sender_email'));
											}
										);
										*/
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                    if ($paymentResponse['Status'] == 'Approved') {
                                        QueryBuilder::createPermitRequestHistoryNew($val);
                                        $this->log->info("Payment Transaction Data Datacap Payment -- " . json_encode($paymentResponse));
                                        $request = new Request([
                                            'total'   => $amount,
                                            'card_last_four' => $paymentProfile->card_last_four,
                                            'expiration' => $paymentProfile->expiry
                                        ]);
                                        $this->log->info("Save Transaction Data Request --" . json_encode($request) . "--" . json_encode($val->user_id));
                                        $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $val->user_id, '');
                                        $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($authorized_anet_transaction));
                                        #DD PIMS-11368	
                                        if (isset($authorized_anet_transaction->id)) {
                                            QueryBuilder::setReferenceKey($authorized_anet_transaction->id, $val->account_number);
                                        }
                                        // QueryBuilder::setAllTransactions(json_encode($paymentResponse), "datacap", $val->account_number,'PermitAutoRenewUsm',$val->user_id);
                                        $maxDays = date('t');
                                        $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate,$rateValidate, $val->transaction_retry + 1,$authorized_anet_transaction->id,$amount);
                                        $val->save();
                                        //user mail
                                        //$val->card_last_four = $paymentProfile->card_last_four;
                                        //$val->card_name = $paymentProfile->card_name;
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $paymentProfile->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $paymentProfile->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $partnerDetails = User::where('id', $val->partner_id)->first();
                                        $partner_name = "City Parking Inc Services";

                                        $view = "usm.permit-renew";
                                        Mail::send(
                                            $view,
                                            ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                            function ($message) use ($val) {
                                                $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #" . $val->account_number);
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if (isset($val->facility->FacilityPaymentDetails) && ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                            $paymentProfile = HeartlandPaymentProfile::where('user_id', $val->user_id)->first();

                            $this->log->info("Payment Profile Data --" . $val->account_number . "--" . json_encode($paymentProfile));
                            /* $processing_fee = $val->facility->permit_processing_fee;
                            $permit_rate = $permitRate->rate;
                            $final_amount = $processing_fee + $permit_rate; */

                            $amount = ($val->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $final_amount;

                            if ($paymentProfile) {
                                $request = new Request([
                                    'Amount'   => $amount,
                                    'total'   => $amount,
                                    'token' => $paymentProfile->token,
                                    'zipcode' => $paymentProfile->zipcode,
                                    'card_last_four' => $paymentProfile->card_last_four,
                                    'expiration_date' => $paymentProfile->expiry,
                                    'original_total' => $permitRate->permit_final_amount
                                ]);


                                #end add parking time in email
                                $permit_validity = '';
                                $permitRateDescHour = array();
                                $permitRateDetails    = PermitRate::find($val->permit_rate_id);
                                if ($permitRateDetails) {
                                    $permitRateDescHour = PermitRateDescription::find($permitRateDetails->permit_rate_description_id);
                                    if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "1")) {
                                        $permit_validity = '1 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "2")) {
                                        $permit_validity = '2 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "3")) {
                                        $permit_validity = 'Quarterly';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "4")) {
                                        $permit_validity = '4 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "5")) {
                                        $permit_validity = '5 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "6")) {
                                        $permit_validity = 'Half Yearly';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "7")) {
                                        $permit_validity = '7 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "8")) {
                                        $permit_validity = '8 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "9")) {
                                        $permit_validity = '9 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "10")) {
                                        $permit_validity = '10 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "11")) {
                                        $permit_validity = '11 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "12")) {
                                        $permit_validity = '1 Year';
                                    } else {
                                        $permit_validity = '1 Month';
                                    }
                                }
                                $paymentResponse = '---';
                                try {
                                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $val->facility);
                                } catch (Exception $e) {
                                    $this->log->info("Payment Charge Error Response Permit Id #:" . $val->account_number . "--" . json_encode($e->getMessage()));
                                    #DD PIMS-11368	                                    
                                    QueryBuilder::setAllFailedTransactions(json_encode($e->getMessage()), "heartland", $val->account_number, 'PermitAutoRenewUsm', $val->user_id);
                                    if (str_contains($e->getMessage(), 'duplicate')) {
                                    } else {
                                        $val->transaction_retry   = $val->transaction_retry + 1;
                                        $val->save();
                                        // Failure mail send to user
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $paymentProfile->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $paymentProfile->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $day_of_month = date('d');
                                        if ($day_of_month == '05') {
                                            $view = 'usm.permit-cancel-reminder';
                                            //call skiData Api to cancel resuable ticket
                                            $subject = "Important Notice: Permit Cancellation";

                                            $val->status = '0';
                                            $val->user_consent = '0';
                                            $val->cancelled_at = $permitRenewDate;
                                            $val->save();
                                        } else {
                                            $view = "usm.permit-renew-fail";
                                            $subject = "Important: Action Required for Your Permit Payment";
                                        }
                                        $partnerDetails = User::where('id', $val->partner_id)->first();
                                        $partner_name = "City Parking Inc";
                                        $val->final_amount = $final_amount;
                                        Mail::send(
                                            $view,
                                            ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                            function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear, $subject) {
                                                //$message->to([$val->user->email])->subject("Your Permit #". $val->account_number ."  could not be renewed due to Payment Failure");
                                                $message->to([$val->user->email])->subject($subject);
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                }

                                $this->log->info("Heartland Payment Response to Permit Id #:" .  $val->account_number . "--" . json_encode($paymentResponse));

                                if (isset($paymentResponse->responseMessage) && $paymentResponse->responseMessage == 'APPROVAL') {
                                    QueryBuilder::createPermitRequestHistoryNew($val);
                                    $user_id = $val->user_id;
                                    $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $user_id);
                                    $this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($authorized_anet_transaction));
                                    $maxDays = date('t');
                                    $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate,$rateValidate, $val->transaction_retry + 1,$authorized_anet_transaction->id,$amount);
                                    $val->save();

                                    #DD PIMS-11368	
                                    if (isset($authorized_anet_transaction->id)) {
                                        QueryBuilder::setReferenceKey($authorized_anet_transaction->id, $val->account_number);
                                    }
                                    // QueryBuilder::setAllTransactions(json_encode($paymentResponse), "heartland", $val->account_number,'PermitAutoRenewUsm',$val->user_id);
                                    //user mail

                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $paymentProfile->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $paymentProfile->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = "City Parking Inc";
                                    $view = "usm.permit-renew";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                        function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear) {
                                            //$message->to($val->user->email)->subject("Payment Successful against Permit #". $val->account_number." for the month of <".$PermitRenewMonth.", ".$PermitRenewYear.">");
                                            //  $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                            $message->to($val->user->email)->subject("Autopayment Confirmation for Permit #" . $val->account_number);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                } else {
                                    #DD PIMS-11368	                 
                                    if (!isset($paymentResponse)) {
                                        $paymentResponse = NULL;
                                    }
                                    QueryBuilder::setAllFailedTransactions(json_encode($paymentResponse), "heartland", $val->account_number, 'PermitAutoRenewUsm', $val->user_id);
                                    $val->transaction_retry   = $val->transaction_retry + 1;
                                    $val->save();
                                    // Failure mail send to user
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $paymentProfile->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $paymentProfile->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                    $day_of_month = date('d');
                                    if ($day_of_month == '05') {
                                        $view = 'usm.permit-cancel-reminder';
                                        //call skiData Api to cancel resuable ticket
                                        $subject = "Important Notice: Permit Cancellation";

                                        $val->status = '0';
                                        $val->user_consent = '0';
                                        $val->cancelled_at = $permitRenewDate;
                                        $val->save();
                                    } else {
                                        $view = "usm.permit-renew-fail";
                                        $subject = "Important: Action Required for Your Permit Payment";
                                    }

                                    $partnerDetails = User::where('id', $val->partner_id)->first();
                                    $partner_name = "City Parking Inc";
                                    $val->final_amount = $final_amount;

                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name],
                                        function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear, $subject) {
                                            //$message->to([$val->user->email])->subject("Your Permit #". $val->account_number ."  could not be renewed due to Payment Failure");
                                            $message->to([$val->user->email])->subject($subject);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                            }
                        }
                    }
                } else {
                    $this->log->info("Permit Rate Not Found");
                }
                $count++;
            }
            $msg = "Total Count of Permit Renew: " . $count;
            $this->log->info($msg);
            return $msg;
        } catch (Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();

            $this->log->error($msg);
            $this->log->info("Queue ended");
        }
    }
}
