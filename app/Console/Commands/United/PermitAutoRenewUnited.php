<?php

namespace App\Console\Commands\United;

use Illuminate\Support\Facades\Mail;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\PermitRequest;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\PermitRate;
use App\Classes\DatacapPaymentGateway;
use App\Models\PermitRequestRenewHistory;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Http\Helpers\QueryBuilder; #pims-14610

/**
 * Emails reservation stub to user
 */
class PermitAutoRenewUnited extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'united:permit-renew';
    protected $log;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'monthly permit renew.';

    const PARTNER_ID = '7395';
    const FACILITY_ID = '133';

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/united/')->createLogger('autorenew');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try{ 
            $permit_end_date = date('Y-m-d', strtotime('last day of previous month'));
            $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));

            $desired_end_date = date("Y-m-t");
            $desired_start_date = date('Y-m-01');
		
            $monthlyRequest = PermitRequest::with(['facility.FacilityPaymentDetails','user','PermitVehicle'])->where('partner_id',self::PARTNER_ID)->where('facility_id',self::FACILITY_ID)->whereDate('desired_end_date', '<=', $permit_end_date)->whereDate('desired_start_date', '>=', $permit_start_date)->whereNull('cancelled_at')->whereNull('deleted_at')->where('user_consent',1)->whereNotNull('anet_transaction_id')->get();
		    //dd($permit_start_date,$permit_end_date,$monthlyRequest,self::PARTNER_ID,self::FACILITY_ID);
            if (!$monthlyRequest) {
                //throw new NotFoundException('Monthly Request Not Found.');
                $this->log->info("Monthly Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($monthlyRequest));
            // Create Permit Request History
            //$this->createPermitRequestHistory($monthlyRequest);                     
		    $count = 0;
		    foreach($monthlyRequest as $key=>$val){
                //dd($val->permit_rate_id);
                $permitRate = PermitRate::where('id',$val->permit_rate_id)->first();    
                //dd($permitRate);
                $this->log->info("Permit Rate Found" . json_encode($permitRate));
                if($permitRate->rate=='0.00'){
                    QueryBuilder::createPermitRequestHistoryNew($val);
                    $maxDays=date('t');
                    $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate);
                    $val->save();
                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
					if($facility_brand_setting){
						$val->facility_logo_id = $facility_brand_setting->id;
					}else{
						$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
						$val->logo_id  = $brand_setting->id;
					}
					$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
					Mail::send(
                        "united.permit-renew", ['data' => $val, 'brand_setting' => $brand_setting], function ($message) use($val) {
                            $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Mail sent to ". $val->user->email);                   
                }else if($permitRate->rate >"0.00"){
                    if($permitRate){
                        $final_amount = $permitRate->rate;
                        $rateValidate = QueryBuilder::permitRenewCalculation((object) [], $val, $final_amount, 1);
                        $final_amount = $rateValidate['total_amount'];
                        //$amount = ($val->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.66' : $permitRate->rate;
						$amount = $final_amount;
                        $ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $val->facility->FacilityPaymentDetails->datacap_script_url;
                        
                        $paymentProfile = DatacapPaymentProfile::where('user_id',$val->user_id)->first();
                        $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                        //dd($amount,$ecommerce_mid,$url,$paymentProfile);
                        if($paymentProfile){
                            $data['Token'] = $paymentProfile->token;
                            if ($amount > 0) {
                              //  $amount = number_format($amount, 2);
                                $data['Amount'] = $amount;
                                $data['Token'] = $paymentProfile->token;
                                $data['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                $data["CardHolderID"] = "Allow_V2";
                                $this->log->info("Payment Request Data --" . json_encode($data)."--". json_encode($ecommerce_mid). "--" . json_encode($url));
                                $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                if($paymentResponse["Status"] =="Error"){
                                    $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                    if($paymentResponse["Message"] =="Open Testing Account Number Not Found for this Merchant ID"){
                                        $this->log->info("Invalid payment information. Please verify and try again or use another card ." . json_encode($paymentResponse));
                                    }else{
                                        $this->log->info("Invalid payment information. Please verify and try again or use another card." . json_encode($paymentResponse));
                                    }
                                }else if($paymentResponse["Status"] == "Declined"){
                                    // Failure mail send to user
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if($expiry){
                                        $expiry_data = str_split($expiry, 2);
                                        //dd($expiry,$expiry_data);
                                        $val->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                    }else{
                                        $val->card_expiry = "-";
                                    }
									$facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
									if($facility_brand_setting){
										$val->facility_logo_id = $facility_brand_setting->id;
									}else{
										$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
										$val->logo_id  = $brand_setting->id;
									}
									$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                
                                    Mail::send(
                                        "united.permit-renew-fail", ['data' => $val, 'brand_setting' => $brand_setting], function ($message) use($val) {
                                            $message->to([$val->user->email,config('parkengage.WAILUKU.USER_1')])->subject("Your Permit #". $val->account_number ."  could not be renewed due to Payment Failure");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to ". $val->user->email);   
                                }
                                if ($paymentResponse['Status'] == 'Approved') {
                                    QueryBuilder::createPermitRequestHistoryNew($val);
                                    $this->log->info("Payment Transaction Data Datacap Payment -- " . json_encode($paymentResponse));
                                    $request = new Request([
                                        'total'   => $amount,
                                        'card_last_four' => $paymentProfile->card_last_four,
                                        'expiration' => $paymentProfile->expiry
                                    ]);
                                    $this->log->info("Save Transaction Data Request --" . json_encode($request)."--".json_encode($val->user_id));
                                    $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request,$paymentResponse, $val->user_id,'');
                                    $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($authorized_anet_transaction));  
                                    $maxDays=date('t');
                                    $val = QueryBuilder::populatePermitValuesOnRenewal($val,$desired_end_date,$maxDays,$permitRate->rate,$rateValidate, $val->transaction_retry + 1,$authorized_anet_transaction->id,$amount);
                                    $val->save();
                                    //user mail
                                        //dd($paymentProfile);
                                    $val->card_last_four    = $paymentProfile->card_last_four;
                                    $val->card_name         = $paymentProfile->card_name;
                                    $expiry                 = $paymentProfile->expiry;
                                    if($expiry){
                                        $expiry_data = str_split($expiry, 2);
                                        //dd($expiry,$expiry_data);
                                        $val->card_expiry = $expiry_data[0]."/".$expiry_data[1];
                                    }else{
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
									if($facility_brand_setting){
										$val->facility_logo_id = $facility_brand_setting->id;
									}else{
										$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first(); 
										$val->logo_id  = $brand_setting->id;
									}
									$brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                
                                    Mail::send(
                                        "united.permit-renew", ['data' => $val, 'brand_setting' => $brand_setting], function ($message) use($val) {
                                            $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to ". $val->user->email);                       
                            
                                }
                            }
                        }
                    }
                } else{
                    $this->log->info("Permit Rate Not Found");
                }
			    $count++;				
		    }
			$msg = "Total Count of Permit Renew: ".$count;
			$this->log->info($msg);
			return $msg;
        }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
    }
}
