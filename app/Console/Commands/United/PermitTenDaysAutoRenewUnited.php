<?php

namespace App\Console\Commands\United;

use App\Classes\DatacapPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use App\Classes\PlanetPaymentGateway;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Models\PermitRequest;
use App\Models\PermitRequestRenewHistory;
use App\Models\PermitServices;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Mail;
use App\Http\Helpers\QueryBuilder;

class PermitTenDaysAutoRenewUnited extends Command
{
    protected $log;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'united:ten-days-permit-renew';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'United ten days permit renew.';

    const PARTNER_ID = '7395';
    const FACILITY_ID = '342';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = (new LoggerFactory)->setPath('logs/united/')->createLogger('ten_days_autorenew');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            // Get the current date and subtract 10 days
            $dateTenDaysAgo = Carbon::now()->subDays(10);
            // dd($dateTenDaysAgo);

            // Get the start date (10 days ago) and the end date (1 day before today)
            $startDate = $dateTenDaysAgo->copy()->startOfDay(); // Start of the day 10 days ago
            $endDate = Carbon::now()->subDay()->endOfDay();       // End of the day 1 day ago (yesterday)

            $permit_start_date = $startDate->toDateString();
            $permit_end_date = $endDate->toDateString();
            // dd($permit_start_date, $permit_end_date);

            $endDate = new DateTime($permit_end_date);

            // Add one day to the end date
            $desiredStartDate = $endDate->modify('+1 day');
            $desired_start_date = $desiredStartDate->format('Y-m-d');

            $desiredEndDate = $desiredStartDate->modify('+9 day');
            $desired_end_date = $desiredEndDate->format('Y-m-d');

            $permitRenewDate = date('Y-m-d h:i:s');
            $time = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F", $time);
            $PermitRenewYear = date("Y", $time);

            $tenDaysRequest = PermitRequest::with(['facility.facilityPaymentDetails', 'user', 'permitVehicle', 'PermitRate'])
                ->where('partner_id', self::PARTNER_ID)
                ->whereDate('desired_start_date', '>=', $permit_start_date)
                ->whereDate('desired_end_date', '<=', $permit_end_date)
                ->whereNull('cancelled_at')
                ->whereNull('deleted_at')
                ->where('user_consent', 1)
                ->whereNotNull('anet_transaction_id')
                //->whereNull('business_id')
                ->whereHas('PermitRate.rateDescription', function ($query) {
                    $query->where('hours_description', '10 Days');
                })
                ->orderBy('id', 'desc')
                ->limit(1)
                ->get();

            if (!$tenDaysRequest) {
                $this->log->info("Ten Days Request Not Found.");
            }
            $this->log->info("Permit Found" . json_encode($tenDaysRequest));

            $count = 0;
            $partner_name = "United Parking Services";
            foreach ($tenDaysRequest as $key => $val) {

                $this->log->info("Under Foreach loop " . $key);

                $permitRate = PermitRate::where('id', $val->permit_rate_id)->first();

                $this->log->info("Permit Rate Found" . json_encode($permitRate));

                if ($permitRate->rate == '0.00') {
                    $this->createPermitRequestTenDaysHistoryNew($val);
                    $maxDays = date('t');
                    $val->no_of_days          = $maxDays;
                    $val->permit_rate         = $permitRate->rate;
                    $val->desired_start_date  = $desired_start_date;
                    $val->desired_end_date    = $desired_end_date;
                    $val->save();
                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                    if ($facility_brand_setting) {
                        $val->facility_logo_id = $facility_brand_setting->id;
                    } else {
                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                        $val->logo_id  = $brand_setting->id;
                    }
                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                    $view = "united.permit-renew";
                    Mail::send(
                        $view,
                        ['data' => $val, 'brand_setting' => $brand_setting, 'partner_name' => $partner_name],
                        function ($message) use ($val) {
                            $message->to($val->user->email)->subject("Your Permit #" . $val->account_number . " has been Renewed Successfully");
                            $message->from(config('parkengage.default_sender_email'));
                        }
                    );
                    $this->log->info("Mail sent to " . $val->user->email);
                } else if ($permitRate->rate > "0.00") {
                    if ($permitRate) {

                        if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                            $paymentProfile = PlanetPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            $amount = ($val->facility->FacilityPaymentDetails->planet_payment_env == 'test') ? '3.00' : $permitRate->rate;
                            if ($paymentProfile) {
                                $val->session_id = $paymentProfile->token;
                                $paymentByToken = PlanetPaymentGateway::planetPaymentByToken($val, $amount, '');
                                $this->log->info("planet permit Payment Response :" . json_encode($paymentByToken));
                                if (!in_array($paymentByToken["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                    $this->log->info("planet Payment Failed Response :" . json_encode($paymentByToken));
                                    // Failure mail send to user
                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();

                                    $view = "united.permit-renew-fail";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'partner_name' => $partner_name],
                                        function ($message) use ($val) {
                                            $message->to([$val->user->email])->subject("Payment Failed against your Permit #" . $val->account_number);
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                } else {
                                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($paymentByToken, $val->user_id);
                                    $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($planetTransaction));
                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $maxDays = date('t');
                                    $val->no_of_days          = $maxDays;
                                    $val->anet_transaction_id = $planetTransaction->id;
                                    $val->permit_rate         = $permitRate->rate;
                                    $val->desired_start_date  = $desired_start_date;
                                    $val->desired_end_date    = $desired_end_date;
                                    $val->save();
                                    //user mail

                                    $val->card_last_four = $paymentProfile->card_last_four;
                                    $val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);

                                        $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $val->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $val->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $val->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();

                                    $view = "united.permit-renew";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'partner_name' => $partner_name],
                                        function ($message) use ($val) {
                                            $message->to($val->user->email)->subject("Payment made successfully against Permit #" . $val->account_number);
                                            //$message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                            $amount = ($val->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $permitRate->rate;
                            $ecommerce_mid = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $val->facility->FacilityPaymentDetails->datacap_script_url;

                            $paymentProfile = DatacapPaymentProfile::where('user_id', $val->user_id)->first();
                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));

                            if ($paymentProfile) {
                                $data['Token'] = $paymentProfile->token;
                                if ($amount > 0) {
                                   // $amount = number_format($amount, 2);
                                    $data['Amount'] = $amount;
                                    $data['Token'] = $paymentProfile->token;
                                    $data['ecommerce_mid'] = $val->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                    $data["CardHolderID"] = "Allow_V2";
                                    $this->log->info("Payment Request Data --" . json_encode($data) . "--" . json_encode($ecommerce_mid) . "--" . json_encode($url));
                                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                    if ($paymentResponse["Status"] == "Error") {
                                        $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card .");
                                        } else {
                                            $this->log->info("Invalid payment information. Please verify and try again or use another card.");
                                        }
                                    } else if ($paymentResponse["Status"] == "Declined") {
                                        // Failure mail send to user
                                        $val->card_last_four = $paymentProfile->card_last_four;
                                        $val->card_name = $paymentProfile->card_name;
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $val->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $val->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $val->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $view = "united.permit-renew-fail";
                                        Mail::send(
                                            $view,
                                            ['data' => $val, 'brand_setting' => $brand_setting],
                                            function ($message) use ($val) {
                                                $message->to([$val->user->email])->subject("Your Permit #" . $val->account_number . "  could not be renewed due to Payment Failure");
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                    if ($paymentResponse['Status'] == 'Approved') {
                                        $this->log->info("Payment Transaction Data Datacap Payment -- " . json_encode($paymentResponse));
                                        $request = new Request([
                                            'total'   => $amount,
                                            'card_last_four' => $paymentProfile->card_last_four,
                                            'expiration' => $paymentProfile->expiry
                                        ]);
                                        $this->log->info("Save Transaction Data Request --" . json_encode($request) . "--" . json_encode($val->user_id));
                                        $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $val->user_id, '');
                                        $this->log->info("Payment Transaction Data Authorized Table -- " . json_encode($authorized_anet_transaction));
                                        $maxDays = date('t');
                                        $val->no_of_days          = $maxDays;
                                        $val->anet_transaction_id = $authorized_anet_transaction->id;
                                        $val->permit_rate         = $permitRate->rate;
                                        $val->desired_start_date  = $desired_start_date;
                                        $val->desired_end_date    = $desired_end_date;
                                        $val->save();
                                        //user mail

                                        $val->card_last_four = $paymentProfile->card_last_four;
                                        $val->card_name = $paymentProfile->card_name;
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $val->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $val->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $val->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $val->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $view = "united.permit-renew";
                                        Mail::send(
                                            $view,
                                            ['data' => $val, 'brand_setting' => $brand_setting],
                                            function ($message) use ($val) {
                                                $message->to($val->user->email)->subject("Your Permit #" . $val->account_number . " has been Renewed Successfully");
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                }
                            } else {
                                $this->log->info("Payment Profile not found");
                            }
                        } else if (isset($val->facility->FacilityPaymentDetails) && ($val->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {

                            $paymentProfile = HeartlandPaymentProfile::where('user_id', $val->user_id)->first();

                            $this->log->info("Payment Profile Data --" . json_encode($paymentProfile));
                            $processing_fee = $val->facility->permit_processing_fee;
                            $permit_rate = $permitRate->rate;
                            $final_amount = $processing_fee + $permit_rate;

                            $amount = ($val->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $final_amount;
                          //  $amount = number_format($amount, 2);

                            if ($paymentProfile) {
                                $request = new Request([
                                    'Amount'   => $amount,
                                    'total'   => $amount,
                                    'token' => $paymentProfile->token,
                                    'zipcode' => $paymentProfile->zipcode,
                                    'card_last_four' => $paymentProfile->card_last_four,
                                    'expiration_date' => $paymentProfile->expiry,
                                    'original_total' => $val->permit_final_amount
                                    // 'original_total' => 22.0
                                ]);

                                $permit_services = PermitRequestServiceMapping::where('permit_request_id', $val->id)->pluck('permit_service_id');

                                $services  = PermitServices::with([
                                    'permit_service_criteria_mappings' => function ($query) use ($permit_services) {
                                        $query->whereIn('permit_service_id', $permit_services)
                                            ->with('criteria');
                                    }
                                ])
                                    ->select('permit_services.*')
                                    ->whereIn('id', $permit_services)
                                    ->orderBy('permit_services.id', 'asc')
                                    ->get();

                                if (count($services) > 0) {
                                    $services = $this->formatPermitServiceCriteria($services);
                                }
                                #end add parking time in email
                                $permit_validity = '';
                                $permitRateDescHour = array();
                                $permitRateDetails    = PermitRate::find($val->permit_rate_id);
                                if ($permitRateDetails) {
                                    $permitRateDescHour = PermitRateDescription::find($permitRateDetails->permit_rate_description_id);
                                    if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "1")) {
                                        $permit_validity = '1 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "2")) {
                                        $permit_validity = '2 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "3")) {
                                        $permit_validity = 'Quarterly';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "4")) {
                                        $permit_validity = '4 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "5")) {
                                        $permit_validity = '5 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "6")) {
                                        $permit_validity = 'Half Yearly';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "7")) {
                                        $permit_validity = '7 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "8")) {
                                        $permit_validity = '8 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "9")) {
                                        $permit_validity = '9 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "10")) {
                                        $permit_validity = '10 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "11")) {
                                        $permit_validity = '11 Month';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "12")) {
                                        $permit_validity = '1 Year';
                                    } else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "13")) {
                                        $permit_validity = 'Weekly';
                                    }
                                    else if (isset($permitRateDescHour->permit_frequency) && ($permitRateDescHour->permit_frequency == "14")) {
                                        $permit_validity = '10 Days';
                                    } else {
                                        $permit_validity = '1 Month';
                                    }
                                }

                                try {
                                    $paymentToken = HeartlandPaymentProfile::where('user_id', $val->user_id)->first();
                                    $request->request->add(['token' => $paymentToken->token]);

                                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $val->facility);
                                } catch (Exception $e) {
                                    $this->log->info("Payment Charge Error Response Permit Id #:" . $val->account_number . "--" . json_encode($e->getMessage()));
                                    if (str_contains($e->getMessage(), 'duplicate')) {
                                    } else {
                                        //$this->log->info("Error in Heartland Payment with payment profile id --" . json_encode($e->getMessage()));
                                        // Failure mail send to user
                                        //$val->card_last_four = $paymentProfile->card_last_four;
                                        //$val->card_name = $paymentProfile->card_name;
                                        $expiry    = $paymentProfile->expiry;
                                        if ($expiry) {
                                            $expiry_data = str_split($expiry, 2);
                                            $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                        } else {
                                            $paymentProfile->card_expiry = "-";
                                        }
                                        $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                        if ($facility_brand_setting) {
                                            $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                        } else {
                                            $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                            $paymentProfile->logo_id  = $brand_setting->id;
                                        }
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $view = "united.permit-renew-fail";
                                        Mail::send(
                                            $view,
                                            ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity, 'partner_name' => $partner_name],
                                            function ($message) use ($val, $PermitRenewMonth, $PermitRenewYear) {
                                                //$message->to([$val->user->email])->subject("Your Permit #". $val->account_number ."  could not be renewed due to Payment Failure");
                                                $message->to([$val->user->email])->subject("Payment Failed against Permit #" . $val->account_number . " for the month of <" . $PermitRenewMonth . ", " . $PermitRenewYear . ">");
                                                $message->from(config('parkengage.default_sender_email'));
                                            }
                                        );
                                        $this->log->info("Mail sent to " . $val->user->email);
                                    }
                                }
                                $this->log->info("Heartland Payment Response to Permit Id #:" .  $val->account_number . "--" . json_encode($paymentResponse));

                                if (isset($paymentResponse) && $paymentResponse->responseMessage == 'APPROVAL') {
                                    $this->createPermitRequestTenDaysHistoryNew($val);
                                    $user_id = $val->user_id;
                                    $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $user_id);
                                    $this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($authorized_anet_transaction));
                                    $maxDays = date('t');
                                    $val->no_of_days          = $maxDays;
                                    $val->anet_transaction_id = $authorized_anet_transaction->id;
                                    $val->permit_rate         = $permitRate->rate;
                                    $val->desired_start_date  = $desired_start_date;
                                    $val->desired_end_date    = $desired_end_date;
                                    $val->permit_final_amount = $amount;
                                    $val->processing_fee      = $processing_fee;
                                    $val->save();
                                    //user mail
                                    //dd($paymentProfile);
                                    //$val->card_last_four = $paymentProfile->card_last_four;
                                    //$val->card_name = $paymentProfile->card_name;
                                    $expiry    = $paymentProfile->expiry;
                                    if ($expiry) {
                                        $expiry_data = str_split($expiry, 2);
                                        //dd($expiry,$expiry_data);
                                        $paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
                                    } else {
                                        $paymentProfile->card_expiry = "-";
                                    }
                                    $facility_brand_setting = FacilityBrandSetting::where('facility_id', $val->facility_id)->first();
                                    if ($facility_brand_setting) {
                                        $paymentProfile->facility_logo_id = $facility_brand_setting->id;
                                    } else {
                                        $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();
                                        $paymentProfile->logo_id  = $brand_setting->id;
                                    }
                                    $brand_setting = BrandSetting::where('user_id', $val->partner_id)->first();

                                    $view = "united.permit-renew";
                                    Mail::send(
                                        $view,
                                        ['data' => $val, 'brand_setting' => $brand_setting, 'permitRate' => $permitRate, 'paymentProfile' => $paymentProfile, 'services' => $services, 'permitRateDescHour' => $permitRateDescHour, 'permit_validity' => $permit_validity],
                                        function ($message) use ($val, $desired_end_date) {
                                            $message->to($val->user->email)->subject("Payment Successful against Permit #" . $val->account_number . " for the week, ending on <" . $desired_end_date . ">");
                                            //  $message->to($val->user->email)->subject("Your Permit #". $val->account_number." has been Renewed Successfully");
                                            $message->from(config('parkengage.default_sender_email'));
                                        }
                                    );
                                    $this->log->info("Mail sent to " . $val->user->email);
                                }
                            }
                        }
                    }
                } else {
                    $this->log->info("Permit Rate Not Found");
                }
                $count++;
            }
            $msg = "Total Count of Permit Renew: " . $count;
            $this->log->info($msg);
            return $msg;
        } catch (Exception $e) {
            echo $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();

            $this->log->error($msg);
            $this->log->info("Queue ended");
        }
    }

    public function createPermitRequestHistory($monthlyRequest)
    {
        if ($monthlyRequest) {
            $permitRenewDate = date('Y-m-d h:i:s');
            foreach ($monthlyRequest as $k => $v) {
                $permitData = PermitRequestRenewHistory::firstOrNew(['partner_id' => self::PARTNER_ID, 'anet_transaction_id' => $v->anet_transaction_id]);

                $permitData->permit_request_id = $v->id;
                $permitData->user_id = $v->user_id;
                $permitData->facility_id = $v->facility_id;
                $permitData->anet_transaction_id = $v->anet_transaction_id;
                $permitData->tracking_code = $v->tracking_code;
                $permitData->email = $v->email;
                $permitData->name = $v->name;
                $permitData->phone = $v->phone;
                $permitData->permit_rate = $v->permit_rate;
                $permitData->permit_rate_id = $v->permit_rate_id;
                $permitData->approved_on = $v->approved_on;
                $permitData->account_number = $v->account_number;
                $permitData->monthly_duration_value = $v->monthly_duration_value;
                $permitData->no_of_days = $v->no_of_days;
                $permitData->partner_id = $v->partner_id;
                $permitData->license_number = $v->license_number;
                $permitData->mer_reference = $v->mer_reference;
                $permitData->image_front = $v->image_front;
                $permitData->image_back = $v->image_back;
                $permitData->user_consent = $v->user_consent;
                $permitData->vehicle_id = $v->vehicle_id;
                $permitData->is_admin = $v->is_admin;
                $permitData->ex_month = $v->ex_month;
                $permitData->ex_year = $v->ex_year;
                $permitData->payment_gateway = $v->payment_gateway;
                $permitData->permit_type = $v->permit_type;
                $permitData->is_payment_authrize = $v->is_payment_authrize;
                $permitData->session_id = $v->session_id;
                $permitData->permit_type_name = $v->permit_type_name;
                $permitData->skidata_id = $v->skidata_id;
                $permitData->skidata_value = $v->skidata_value;
                $permitData->acknowledge = $v->acknowledge;
                $permitData->facility_zone_id = $v->facility_zone_id;
                $permitData->desired_start_date = $v->desired_start_date;
                $permitData->desired_end_date = $v->desired_end_date;
                $permitData->cancelled_at = $v->cancelled_at;
                $permitData->created_at = $permitRenewDate;
                $permitData->updated_at = $permitRenewDate;
                $permitData->deleted_at = $v->deleted_at;

                $permitData->hid_card_number = $v->hid_card_number;
                $permitData->account_name = $v->account_name;
                $permitData->permit_final_amount = $v->permit_final_amount;
                $permitData->user_remark = $v->user_remark;
                $permitData->user_type_id = $v->user_type_id;
                $permitData->is_antipass_enabled = $v->is_antipass_enabled;
                $permitData->admin_user_id = $v->admin_user_id;
                $permitData->discount_amount = $v->discount_amount;
                $permitData->promocode = $v->promocode;
                $permitData->negotiated_amount = $v->negotiated_amount;
                $permitData->promocode              = $v->promocode;
                $permitData->processing_fee         = $v->processing_fee;
                $permitData->refund_amount          = $v->refund_amount;
                $permitData->refund_type            = $v->refund_type;
                $permitData->refund_remarks         = $v->refund_remarks;
                $permitData->refund_date            = $v->refund_date;
                $permitData->refund_by              = $v->refund_by;
                $permitData->refund_transaction_id  = $v->refund_transaction_id;
                $permitData->refund_status          = $v->refund_status;

                $permitData->save();
            }
        }
        return true;
    }

    private function formatPermitServiceCriteria($services)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);

        $i = 0;
        foreach ($services as $service) {
            if (count($service->permit_service_criteria_mappings) > 0) {
                $formatted = [];
                foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
                    $item = $permit_service_criteria_mapping->criteria;
                    $days = explode(',', $item->days);
                    sort($days); // Sort days to match the sequence of $allDays
                    if ($days == $allDays) {
                        $dayNamesStr = 'All Days';
                    } else {
                        $dayNames = array_map(function ($day) use ($daysMap) {
                            return $daysMap[$day];
                        }, $days);
                        $dayNamesStr = implode(',', $dayNames);
                    }

                    $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
                    $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
                    $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));

                    // Adjust exit time if it's greater than 24 hours
                    $exit_time = explode(":", $item->exit_time_end);
                    if ($exit_time[0] > 23) {
                        $next_hr = $exit_time[0] - 24;
                        $item->exit_time_end = $next_hr . ":" . $exit_time[1] . ":" . $exit_time[2];
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                        $exit_time_overflow = ' (next day)';
                    } else {
                        $exit_time_overflow = '';
                        $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                    }


                    $formatted[] = [
                        'days' => $dayNamesStr,
                        'entry_time_begin' => $entry_time_begin,
                        'entry_time_end' => $entry_time_end,
                        'exit_time_begin' => $exit_time_begin,
                        'exit_time_end' => $exit_time_end . $exit_time_overflow,
                    ];
                }
                $services[$i]->criteria = $formatted;
            }
            $i++;
        }

        return $services;
    }

    #add parking time in email end

    public function createPermitRequestTenDaysHistoryNew($tenDaysRequest)
    {
        if ($tenDaysRequest) {
            $permitRenewDate = date('Y-m-d h:i:s');
            $permitData = PermitRequestRenewHistory::firstOrNew(['partner_id' => self::PARTNER_ID, 'anet_transaction_id' => $tenDaysRequest->anet_transaction_id]);

            $permitData->permit_request_id = $tenDaysRequest->id;
            $permitData->user_id = $tenDaysRequest->user_id;
            $permitData->facility_id = $tenDaysRequest->facility_id;
            $permitData->anet_transaction_id = $tenDaysRequest->anet_transaction_id;
            $permitData->tracking_code = $tenDaysRequest->tracking_code;
            $permitData->email = $tenDaysRequest->email;
            $permitData->name = $tenDaysRequest->name;
            $permitData->phone = $tenDaysRequest->phone;
            $permitData->permit_rate = $tenDaysRequest->permit_rate;
            $permitData->permit_rate_id = $tenDaysRequest->permit_rate_id;
            $permitData->approved_on = $tenDaysRequest->approved_on;
            $permitData->account_number = $tenDaysRequest->account_number;
            $permitData->monthly_duration_value = $tenDaysRequest->monthly_duration_value;
            $permitData->no_of_days = $tenDaysRequest->no_of_days;
            $permitData->partner_id = $tenDaysRequest->partner_id;
            $permitData->license_number = $tenDaysRequest->license_number;
            $permitData->mer_reference = $tenDaysRequest->mer_reference;
            $permitData->image_front = $tenDaysRequest->image_front;
            $permitData->image_back = $tenDaysRequest->image_back;
            $permitData->user_consent = $tenDaysRequest->user_consent;
            $permitData->vehicle_id = $tenDaysRequest->vehicle_id;
            $permitData->is_admin = $tenDaysRequest->is_admin;
            $permitData->ex_month = $tenDaysRequest->ex_month;
            $permitData->ex_year = $tenDaysRequest->ex_year;
            $permitData->payment_gateway = $tenDaysRequest->payment_gateway;
            $permitData->permit_type = $tenDaysRequest->permit_type;
            $permitData->is_payment_authrize = $tenDaysRequest->is_payment_authrize;
            $permitData->session_id = $tenDaysRequest->session_id;
            $permitData->permit_type_name = $tenDaysRequest->permit_type_name;
            $permitData->skidata_id = $tenDaysRequest->skidata_id;
            $permitData->skidata_value = $tenDaysRequest->skidata_value;
            $permitData->acknowledge = $tenDaysRequest->acknowledge;
            $permitData->facility_zone_id = $tenDaysRequest->facility_zone_id;
            $permitData->desired_start_date = $tenDaysRequest->desired_start_date;
            $permitData->desired_end_date = $tenDaysRequest->desired_end_date;
            $permitData->cancelled_at = $tenDaysRequest->cancelled_at;
            $permitData->created_at = $permitRenewDate;
            $permitData->updated_at = $permitRenewDate;
            $permitData->deleted_at = $tenDaysRequest->deleted_at;

            $permitData->hid_card_number = $tenDaysRequest->hid_card_number;
            $permitData->account_name = $tenDaysRequest->account_name;
            $permitData->permit_final_amount = $tenDaysRequest->permit_final_amount;
            $permitData->user_remark = $tenDaysRequest->user_remark;
            $permitData->user_type_id = $tenDaysRequest->user_type_id;
            $permitData->is_antipass_enabled = $tenDaysRequest->is_antipass_enabled;
            $permitData->admin_user_id = $tenDaysRequest->admin_user_id;
            $permitData->discount_amount = $tenDaysRequest->discount_amount;
            $permitData->promocode = $tenDaysRequest->promocode;
            $permitData->negotiated_amount = $tenDaysRequest->negotiated_amount;
            $permitData->processing_fee         = $tenDaysRequest->processing_fee;
            $permitData->refund_amount          = $tenDaysRequest->refund_amount;
            $permitData->refund_type            = $tenDaysRequest->refund_type;
            $permitData->refund_remarks         = $tenDaysRequest->refund_remarks;
            $permitData->refund_date            = $tenDaysRequest->refund_date;
            $permitData->refund_by              = $tenDaysRequest->refund_by;
            $permitData->refund_transaction_id  = $tenDaysRequest->refund_transaction_id;
            $permitData->refund_status          = $tenDaysRequest->refund_status;

            $permitData->save();
        }
        return true;
    }
}
