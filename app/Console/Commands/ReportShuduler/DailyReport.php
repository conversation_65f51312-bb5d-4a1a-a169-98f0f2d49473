<?php

namespace App\Console\Commands\ReportShuduler;

use Mail;
use Exception;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use Carbon\Carbon;
use App\Http\Helpers\ReportBuilder;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\ParkEngage\BrandSetting;

class DailyReport extends Command
{

    protected $signature = 'usm:daily-report';

    protected $description = 'get one month data';

    protected $log;

    const PARTNER_ID = 19349;
    protected $request;
    protected $reportBuilder;
    private $partner_id = self::PARTNER_ID;
    private $facility_id = '167';
    private $startOfMonth = '2024-10-01 00:00:00';
    private $endOfMonth = '2024-10-31 23:59:59';

    function __construct(Request $request, LoggerFactory $logFactory, ReportBuilder $reportBuilder)
    {

        parent::__construct();
        // $this->startOfMonth = Carbon::now()->startOfMonth()->toDateString() . ' 00:00:00';
        // $this->endOfMonth = Carbon::now()->endOfMonth()->toDateString() . ' 23:59:59';
        $this->startOfMonth = Carbon::now()->subDay()->toDateString() . ' 00:00:00';
        $this->endOfMonth = Carbon::now()->subDay()->toDateString() . ' 23:59:59';

        $request->merge([
            'partner_id' => self::PARTNER_ID,
            'fromDate' => $this->startOfMonth,
            'toDate' => $this->endOfMonth,
            'facility_id' => '167'
        ]);

        $this->request = $request;
        $this->reportBuilder = new ReportBuilder($request);
    }

    public function handle()
    {
        //dd($startOfMonth,$endOfMonth);
        //ReportBuilder::getGatedDriveup();
        // $driveupList = ReportBuilder::convertArrayToDriveUpGated(ReportBuilder::getGatedDriveup());
        $excelSheetName = ucwords(str_replace(' ', '', 'Daily Revenue Report'));
        try {

            $excelCreate = Excel::create(
                $excelSheetName,
                function ($excel) use (
                    $excelSheetName
                ) {
                    $TotalCc = 0;
                    $partner_id = $this->partner_id;
                    $imploadFaclitiesData =  $this->facility_id;
                    $checkInTime = $this->startOfMonth;
                    $checkOutTime = $this->endOfMonth;
                    $validationAmountTotal = 0;
                    $validationPaidAmountTotal = 0;
                    $totalGrossAmount = 0;
                    $validationTicketTotal = 0;
                    $validatedGTotal = 0;
                    $totalNetAmount = 0;
                    $totalServiceAmount = 0;
                    $validationPaidAmountTotal = $totalNetAmount = $totalServiceAmount = $totalGrossAmount = $validationAmountTotal = $validationPaidAmountTotal = 0;
                    $DriveUpnonCashPayment = "SELECT CASE
                    when ant.card_type IN ('MC', 'MASTERCARD','M/C','DEBIT MASTERCARD') THEN 'MASTERCARD'
                    when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT','VISA CREDIT') THEN 'VISA'
                    when ant.card_type IN ('DCVR','Discover') THEN 'DCVR'
                    WHEN ant.card_type IN ('AMEX','AMEX CREDIT','AMERICAN EXPRESS','AMERICAN','AMEX DEBIT') THEN 'AMEX' 
                    WHEN ant.card_type IN ('Disc') THEN 'DISC'
                    when ant.card_type is null THEN 'OTHERS'
                    ELSE 'ant.card_type'
                    END AS combined_card_type, SUM(ant.total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                    (SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) as overstayGrandTotal,
                    (SELECT sum(ticket_extends.discount_amount) FROM ticket_extends where ticket_id IN (t.id)) as overstayDiscount,
                    group_concat(t.id) as ticket_ids
                    FROM tickets as t
                    left join anet_transactions as ant on ant.id = t.anet_transaction_id
                    WHERE t.partner_id IN ('$partner_id') AND t.checkout_time >='$checkInTime' AND t.checkout_time <='$checkOutTime' AND t.is_offline_payment IN ('0','2','3') and t.deleted_at is null  and t.is_checkout ='1' and t.facility_id in($imploadFaclitiesData) GROUP BY combined_card_type,t.id"; #td.rate_description  
                    $driveUpCCReport = DB::select($DriveUpnonCashPayment);
                    //Total Discount for Card type Payment
                    $TotalValidatedAmount = (array_sum(array_column($driveUpCCReport, 'discountAmount')) ?? 0) + (array_sum(array_column($driveUpCCReport, 'overstayDiscount')) ?? 0);


                    // Non Cash or card breakdown
                    $checkinDriveUpnonCashPayment = "SELECT CASE
                        when ant.card_type IN ('MC', 'MASTERCARD','M/C','DEBIT MASTERCARD') THEN 'MASTERCARD'
                        when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT','VISA CREDIT') THEN 'VISA'
                        when ant.card_type IN ('DCVR','Discover') THEN 'DCVR'
                            WHEN ant.card_type IN ('AMEX','AMEX CREDIT','AMERICAN EXPRESS','AMERICAN','AMEX DEBIT') THEN 'AMEX' 
                            WHEN ant.card_type IN ('Disc') THEN 'DISC'
                        when ant.card_type is null THEN 'OTHERS'
                        ELSE ant.card_type
                        END AS combined_card_type, 
                        SUM(ant.total) as total_amount,count(r.id) as ticketCount,SUM(r.discount) as discountAmount,SUM(r.processing_fee) as processingFee
                        FROM  reservations as r
                        left join anet_transactions as ant on ant.id = r.anet_transaction_id
                        WHERE r.partner_id IN ('$partner_id') AND r.start_timestamp >='$checkInTime' AND r.start_timestamp <='$checkOutTime'  and r.deleted_at is null  and r.deleted_at is null and r.deleted_at is null   and r.facility_id in($imploadFaclitiesData) GROUP BY combined_card_type";
                    //  dd('ds');
                    $checkinDriveUpCCReport = DB::select($checkinDriveUpnonCashPayment);
                    // dd($checkinDriveUpCCReport);
                    $sql_query4 = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                        sum(t.total) as total,                
                        sum(t.grand_total) as paidAmount,                
                        sum(t.parking_amount) as parking_amount,
                        sum(t.paid_amount) as validated_amount,
                        sum(t.discount_amount) as discount_amount,
                        t.affiliate_business_id, ab.business_name as BusinessName
                        FROM tickets as t        
                        inner join affiliate_business as ab on ab.id = t.affiliate_business_id 
                        WHERE t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime' and t.partner_id IN ('$partner_id')  and t.affiliate_business_id is not null and paid_by > 0 and t.deleted_at is null and t.is_checkout ='1' and t.facility_id in ($imploadFaclitiesData) GROUP BY t.affiliate_business_id order by t.affiliate_business_id";
                    //  dd('sa');
                    $validationReport = DB::select($sql_query4);

                    $totalCashDiscount = 0;
                    $driveUpCashReport = [];
                    foreach ($driveUpCashReport as $key => $value) {
                        if ($value->is_offline_payment == 1) {
                            $finalCodes2[0]['payment_type'] = 'Cash';
                            $finalCodes2[0]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                            $finalCodes2[0]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                        }
                        //  else if ($value->is_offline_payment == 2 || $value->is_offline_payment == 3) {
                        //     $finalCodes2[1]['payment_type'] = 'Card';
                        //     $finalCodes2[1]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                        //     $finalCodes2[1]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                        // }
                        // $totalCashServiceAmount += $value->processingFee;
                        // $cashticketCount = $cashticketCount + $value->ticketCount;
                        // $TotalPaymentReceived +=  $value->sum_offline_amount + $value->overstayGrandTotal;
                        $totalCashDiscount += $value->discountAmount + $value->overstayDiscount;
                        $TotalValidatedAmount +=  $value->discountAmount + $value->overstayDiscount;
                    }

                    // dd($driveUpCashReport, $finalCodes2, $TotalPaymentReceived);

                    //Credit Card Payment Section 
                    $permitCardPayment = "SELECT CASE
                        when ant.card_type IN ('MC', 'MASTERCARD','M/C','DEBIT MASTERCARD') THEN 'MASTERCARD'
                        when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT','VISA CREDIT') THEN 'VISA'
                        when ant.card_type IN ('DCVR','Discover') THEN 'DCVR'
                        WHEN ant.card_type IN ('AMEX','AMEX CREDIT','AMERICAN EXPRESS','AMERICAN','AMEX DEBIT') THEN 'AMEX' 
                        WHEN ant.card_type IN ('Disc') THEN 'DISC'
                        when ant.card_type is null THEN 'OTHERS'
                        ELSE ant.card_type
                        END AS combined_card_type, count(pr.id) as ticketCount, t.ticket_number, #pr.ticketech_code,
                        SUM(ant.total) as total_amount,
                        (SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) as overstayGrandTotal,
                        (SELECT sum(ticket_extends.discount_amount) FROM ticket_extends where ticket_id IN (t.id)) as overstayDiscount
                        FROM permit_revenue_flat_table as pr
                        inner join anet_transactions as ant on ant.id = pr.anet_transaction_id
                        left join tickets as t on t.permit_request_id = pr.id 
                        WHERE pr.partner_id IN ('$partner_id') AND pr.action_date >='$checkInTime' AND pr.action_date <='$checkOutTime' AND pr.deleted_at is null AND pr.facility_id in($imploadFaclitiesData)  GROUP BY combined_card_type,t.is_overstay,t.ticket_number"; #td.rate_description    
                    //  dd('fs') ;

                    // dd('das');
                    $permitCCReportPayment = DB::select($permitCardPayment);

                    // dd($permitCCReportPayment);

                    // dd($permitCCReportPayment);
                    $totalCardServiceAmount = $ccticketCount = $totalDiscountAmount = 0;
                    $cards = ['AMEX', 'DCVR', 'MASTERCARD', 'VISA', 'DISC', 'OTHERS'];
                    // $this->log->info("cashierShiftReport " . json_encode($driveUpCCReport));
                    // $this->log->info("cashierShiftReport  CASRD" . json_encode($cards));
                    foreach ($cards as $cardkey => $card) {
                        $totalcardPay  = $processingFees  = $discountAmount = $ticketCount = 0;
                        //$this->log->info("cashierShiftReport OUTER LOOP  " . $card);

                        if (isset($driveUpCCReport) && count($driveUpCCReport) > 0) {
                            foreach ($driveUpCCReport as $key => $value) {
                                // if ($card == $value->combined_card_type) {
                                if (strcasecmp($card, $value->combined_card_type) == 0) {
                                    if ($value->total_amount <= 0) continue;
                                    // $this->log->info("cashierShiftReport INNER LOOP " . json_encode($value->combined_card_type));
                                    $ticketCount += intval($value->ticketCount);
                                    $discountAmount += floatval($value->discountAmount + $value->overstayDiscount);
                                    $processingFees += floatval($value->processingFee);
                                    // $totalcardPay += floatval($value->total_amount+$value->overstayGrandTotal);
                                    $totalcardPay += floatval($value->total_amount);
                                    $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                    $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                    $TotalValidatedAmount += $discountAmount;
                                }
                            }
                        }
                        //  dd($finalCodes3);
                        //  dd($checkinDriveUpCCReport);
                        if (isset($checkinDriveUpCCReport) && count($checkinDriveUpCCReport) > 0) {
                            foreach ($checkinDriveUpCCReport as $key => $value) {
                                //dd($value);
                                if ($card == $value->combined_card_type) {
                                    // $this->log->info("cashierShiftReport INNER LOOP " . json_encode($value->combined_card_type));
                                    $totalcardPay += floatval($value->total_amount);
                                    //+ $value->overstayGrandTota
                                    // $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                    $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                }
                            }
                        }
                        // Permit card Payment
                        //  dd($permitCCReportPayment);
                        // $finalCodes3=[];
                        //  dd( $permitCCReportPayment);
                        if (isset($permitCCReportPayment) && count($permitCCReportPayment) > 0) {
                            foreach ($permitCCReportPayment as $key => $value) {
                                //  $this->log->info("cashierShiftReport INNER LOOP {$card}=={ $value->combined_card_type}");
                                // dd($card, $value->combined_card_type);
                                if ($card == $value->combined_card_type) {
                                    //  $this->log->info("cashierShiftReport INNER LOOP " . json_encode($value->combined_card_type));
                                    $totalcardPay += ($value->total_amount > 0) ? floatval($value->total_amount) : 0;

                                    $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                    $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                }
                            }
                        }
                        //dd($finalCodes3);
                        // $totalDiscountAmount +=$discountAmount;
                        $TotalCc += $totalcardPay;
                        $ccticketCount += $ticketCount;
                        $totalCardServiceAmount += $processingFees;
                        // $TotalValidatedAmount += $discountAmount;
                    }
                    $i = 0;
                    // dd($validationReport);
                    foreach ($validationReport as $key => $value) {
                        $finalCodes4[$i]['Business Name'] = $value->BusinessName;
                        $finalCodes4[$i]['Policy Name'] = '-';
                        $finalCodes4[$i]['No of Tickets'] = floatval($value->ticket_count);
                        $finalCodes4[$i]['Gross Amount ($)'] = floatval($value->total);
                        $finalCodes4[$i]['Processing Fee ($)'] = floatval($value->processingFee);
                        $finalCodes4[$i]['Net Amount ($)'] = floatval($value->total - $value->processingFee - $value->discount_amount);
                        $finalCodes4[$i]['Validated Amount ($)'] = floatval($value->validated_amount);
                        $finalCodes4[$i]['Paid Amount ($)'] = floatval($value->paidAmount);
                        $finalCodes4[$i]['Total Revenue ($)'] =  floatval($value->paidAmount + $value->validated_amount);

                        // $gTotal = $value->paidAmount + $value->validated_amount;
                        // $grossTotal = ($value->parking_amount + $value->processingFee) * $value->ticket_count;
                        // $serviceFee = floatval($value->processingFee * $value->ticket_count);

                        $validationTicketTotal  += $value->ticket_count;
                        $totalGrossAmount       += floatval($value->total);
                        $totalServiceAmount     += floatval($value->processingFee);
                        $totalNetAmount         += floatval($value->total - $value->processingFee - $value->discount_amount);
                        $validationAmountTotal  += floatval($value->validated_amount);
                        $validationPaidAmountTotal += floatval($value->paidAmount);
                        $validatedGTotal += floatval($value->paidAmount + $value->validated_amount);;

                        // $totalGrossAmount += $grossTotal;
                        // policy query according to business
                        $policy_query = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                                        sum(t.total) as total,
                                        sum(t.grand_total) as paidAmount,
                                        sum(t.parking_amount) as parking_amount,
                                        sum(t.discount_amount) as discount_amount,
                                        sum(t.paid_amount) as validated_amount,t.affiliate_business_id,t.policy_id,
                                        p.policy_name as policyName,
                                        ab.business_name as BusinessName
                                        FROM tickets as t
                                        inner join affiliate_business as ab on ab.id = t.affiliate_business_id
                                        inner join business_policy as p on p.id = t.policy_id 
                                        WHERE t.deleted_at is null and t.user_id > 0  and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime' and t.partner_id IN ('$partner_id')  and t.facility_id in ($imploadFaclitiesData) and t.affiliate_business_id =$value->affiliate_business_id and t.paid_by > 0 GROUP BY t.affiliate_business_id,t.policy_id order by t.affiliate_business_id";

                        $policyReport = DB::select($policy_query);

                        if (isset($policyReport) && !empty($policyReport)) {
                            $i++;
                            foreach ($policyReport as $k => $policy) {
                                // $gTotal = $policy->paidAmount + $policy->validated_amount;
                                // $grossTotal = ($policy->parking_amount + $policy->processingFee) * $policy->ticket_count;
                                // $serviceFee = floatval($policy->processingFee * $policy->ticket_count);
                                $finalCodes4[$i]['Business Name'] = '';
                                $finalCodes4[$i]['Policy Name'] = $policy->policyName;
                                $finalCodes4[$i]['No of Ticket'] = floatval($policy->ticket_count);
                                $finalCodes4[$i]['Gross Total ($)'] = floatval($policy->total);
                                $finalCodes4[$i]['Processing Fee ($)'] = floatval($policy->processingFee);
                                $finalCodes4[$i]['Net Amount ($)'] = floatval($policy->total - $policy->processingFee - $value->discount_amount);
                                $finalCodes4[$i]['Validation Amount ($)'] = floatval($policy->validated_amount);
                                $finalCodes4[$i]['Paid Amount ($)'] = floatval($policy->paidAmount);
                                $finalCodes4[$i]['Total Revenue ($)'] =  floatval($policy->paidAmount + $policy->validated_amount);
                                $i++;
                            }
                        }
                        $i++;
                    }

                    // *********************New reservation data issue  ******************************//
                    $reservations_sql = "SELECT *, COUNT(total) AS trx_count,sum(total) as tottalAmount, sum(discount) as discountAmount,SUM(CEIL(COALESCE(reservations.length, 0))) AS lengthInMints, total FROM reservations WHERE reservations.partner_id IN ($partner_id) AND reservations.start_timestamp >= '$checkInTime' AND reservations.start_timestamp <= '$checkOutTime' AND reservations.deleted_at IS NULL and event_id is null  GROUP BY reservations.total;";
                    $reservationData = DB::select($reservations_sql);
                    $reservationAmount = $rTticketCount = $rTicketAmount = $rGrossAmount = $rProcessingFees = $rNetAmonut = $rDiscountAmount = 0;
                    $categoryCount = 0;
                    if (isset($reservationData) && count($reservationData) > 0) {
                        //foreach ($rates as $key => $rate) {
                        $rrRR =  $rExcelrowCount = $rTicketAmount = $rGrossAmount = $rProcessingFees = $rNetAmonut = $rDiscountAmount = $rTticketCount = $totalDuration = 0;
                        foreach ($reservationData as $key => $value) {
                            $totalDuration += $value->lengthInMints;
                            // $rrRR               = $value->rate_description;
                            $rTicketAmount      += floatval($value->tottalAmount);
                            $rTticketCount++;
                            $rGrossAmount       += floatval($value->total);
                            $rProcessingFees    += floatval($value->processing_fee);
                            $rNetAmonut         += floatval($value->total - $value->processing_fee);
                            $rDiscountAmount    += floatval($value->discountAmount);
                            //&& $value->is_overstay == '1'
                            //if ($rate->id == $value->rate_id ) {
                            $rExcelrowCount++;
                            $categoryCount += $value->trx_count;
                            $reservationTickets[$key]['Rate']                   = $value->total;
                            $reservationTickets[$key]['No of Tickets']          = $value->trx_count;
                            $reservationTickets[$key]['Ticket Amount ($)']      = floatval($value->tottalAmount);
                            $reservationTickets[$key]['Gross Amount ($)']       = floatval($rGrossAmount);
                            $reservationTickets[$key]['Processing Fees ($)']    = floatval($rProcessingFees);
                            $reservationTickets[$key]['Net Amount ($)']         = floatval($rNetAmonut);
                            $reservationTickets[$key]['Discount Amount ($)']    = $value->discountAmount > 0 ? floatval($value->discountAmount) : floatval(0);
                            $reservationTickets[$key]['promocode']    = $value->promocode;
                            $reservationTickets[$key]['length']         = floatval($value->length);
                        }
                        $reservationAmount += $rGrossAmount;
                    }

                    //event start here
                    $event_sql = "SELECT   r.*,e.*,e.id as eid, COUNT(r.total) AS trx_count,sum(r.parking_amount) as parkingAmount,sum(r.total) as totalRevenue,sum(r.discount) as totalDiscount FROM reservations r JOIN events e ON  r.event_id = e.id
                        WHERE 
                        r.partner_id IN ($partner_id) 
                            AND r.start_timestamp >= '$checkInTime'
                        AND r.start_timestamp <= '$checkOutTime'
                            AND r.deleted_at IS NULL
                            AND r.event_id IS NOT NULL
                            AND e.deleted_at is null
                        AND r.deleted_at is null
                            GROUP BY 
                        e.title";
                    $eventData = DB::select($event_sql);
                    $eventAmount = $eTticketCount = $eTicketAmount = $eGrossAmount = $eProcessingFees = $eNetAmonut = $eDiscountAmount = $eventRateAmount = 0;
                    $categoryCount = 0;

                    if (isset($eventData) && count($eventData) > 0) {
                        //  dd($rates);
                        //foreach ($rates as $key => $rate) {
                        $eeRR =  $eExcelrowCount = $eTicketAmount = $eGrossAmount = $eProcessingFees = $eNetAmonut = $eDiscountAmount = $eTticketCount = 0;
                        foreach ($eventData as $keye => $valuee) {
                            // $eventRateAmount+= $valuee->$parking_amount;
                            // $rrRR               = $value->rate_description;
                            $eTicketAmount      += floatval($valuee->total);
                            $eTticketCount++;
                            $eGrossAmount       += floatval($valuee->total);
                            $eProcessingFees    += floatval($valuee->processing_fee);
                            $eNetAmonut         += floatval($valuee->total - $value->processing_fee);
                            $eDiscountAmount    += floatval($valuee->discount);
                            //&& $value->is_overstay == '1'
                            //if ($rate->id == $value->rate_id ) {
                            $eExcelrowCount++;
                            $categoryCount += $valuee->trx_count;
                            // dd($categoryCount);
                            $eventTicket[$keye]['Event Name']                   = $valuee->title;
                            $eventTicket[$keye]['Event Count']          = $valuee->trx_count;
                            $eventTicket[$keye]['Event Rate']      = floatval($valuee->parkingAmount);
                            $eventTicket[$keye]['Total Revenue ($)']       = floatval($valuee->totalRevenue);
                            $eventTicket[$keye]['Discount ($)']    = floatval($rProcessingFees);
                            $eventTicket[$keye]['Transcation amount']         = floatval($rNetAmonut);
                            $eventTicket[$keye]['Discount Amount ($)']    = $valuee->totalDiscount > 0 ? floatval($valuee->totalDiscount) : floatval(0);
                            $eventTicket[$keye]['promocode']    = $valuee->promocode;
                            $eventTicket[$keye]['eventid']    = $valuee->eid;
                            //  $reservationTickets[$keye]['Cancel Date']    = $value->promocode;
                            // }
                        }
                        $eventAmount += $eGrossAmount;
                    }

                    //all event count for cancle
                    $totalRenew = $totalCancel = 0;
                    $permit_sql_cancel = "SELECT COUNT(id) AS cancelCount FROM permit_requests where  partner_id IN ($partner_id) and cancelled_at>='$checkInTime' and cancelled_at<='$checkOutTime'  ";
                    $permitResultsCancel = DB::select($permit_sql_cancel);
                    $totalCancel = $permitResultsCancel[0]->cancelCount;
                    $permit_sql_renew = "SELECT COUNT(id) AS renewCount FROM permit_revenue_flat_table where   partner_id IN ($partner_id) and desired_start_date>='$checkInTime' and desired_start_date<='$checkOutTime' and action_flag ='renewed' ";
                    $renewResults = DB::select($permit_sql_renew);
                    $totalRenew = $renewResults[0]->renewCount;
                    /********************************new reservation data end *************************/
                    $excel->sheet($excelSheetName, function ($sheet) use (
                        $finalCodes3,
                        $finalCodes4,
                        $validationPaidAmountTotal,
                        $totalNetAmount,
                        $totalServiceAmount,
                        $totalGrossAmount,
                        $finalCodes2,
                        $reservationTickets,
                        $eventTicket,
                        $TotalCc
                    ) {
                        $brandSetting = BrandSetting::where('user_id', $this->partner_id)->first();
                        $color = $brandSetting->color;
                        $sheet->cell('A3:K3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });


                        ReportBuilder::setUpperSectionExcel($this->partner_id, $this->facility_id, $this->startOfMonth, $this->endOfMonth, $sheet, $lastColumn = "K", $excelSheetName = "Daily Revenue Report");

                        $sheet->mergeCells('A5:K5');
                        $sheet->setCellValue('A5', 'Tickets Cashiered DriveUp');
                        $sheet->cell('A5', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#D9E1F2');
                            $cell->setFontColor('#272829');
                            $cell->setFontSize('12');
                        });

                        // Color Row For Heading 
                        $sheet->cell('A6:K6', function ($row) use ($color) {
                            $row->setBackground($color);
                            $row->setFontColor('#ffffff');
                        });
                        //************************Driveup data******************************** */       
                        $getdriveupData = ReportBuilder::getUngatedDriveup($this->partner_id, $this->facility_id, $this->startOfMonth, $this->endOfMonth);

                        $sheet->fromArray($getdriveupData[0], [], 'A6', false, true);
                        $non_colorCode = count($getdriveupData[0]) + 7;
                        $row_name = 'A' . $non_colorCode . ':K' . $non_colorCode;
                        $sheet->cell($row_name, function ($row) use ($color) {
                            $row->setBackground($color);
                            $row->setFontColor('#ffffff');
                            $row->setAlignment('center');
                            $row->setValignment('center');
                            $row->setFont(array(
                                'family' => 'Calibri',
                                'size' => '12',
                                'bold' => true
                            ));
                        });

                        $sheet->row($non_colorCode, array('Total ($)', $getdriveupData[1][0], '-',  '-', '-', $getdriveupData[1][4],  $getdriveupData[1][5],   $getdriveupData[1][6],  $getdriveupData[1][7],   $getdriveupData[1][8],  $getdriveupData[1][9]));
                        //****************************End driveup Data Here */  
                        //**********************************Permit Data Started Here */
                        $getpermitData = ReportBuilder::getPermitListArray($this->partner_id, $this->facility_id, $this->startOfMonth, $this->endOfMonth);
                        $permitSheetRowStart = $non_colorCode + 1;

                        $sheet->getStyle('E' . $permitSheetRowStart)->getNumberFormat()->setFormatCode('0');

                        $sheet->mergeCells('A' . $permitSheetRowStart . ':K' . $permitSheetRowStart);
                        $sheet->setCellValue('A' . $permitSheetRowStart, 'Tickets Cashiered Permit');
                        $sheet->cell('A' . $permitSheetRowStart, function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#D9E1F2');
                            $cell->setFontColor('#272829');
                            $cell->setFontSize('12');
                        });

                        $rowNumber1 = $permitSheetRowStart + 1;
                        $sheet->cell('A' . $rowNumber1 . ':K' . $rowNumber1, function ($row) use ($color) {
                            $row->setBackground($color);
                            $row->setFontColor('#ffffff');
                        });
                        $sheet->cell('A' . $rowNumber1 . ':K' . $rowNumber1, function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center');
                        });

                        $sheet->setCellValue('A' . $rowNumber1, 'Permit Type');
                        $sheet->setCellValue('B' . $rowNumber1, 'Permit Count');
                        $sheet->setCellValue('C' . $rowNumber1, 'Permit Number');
                        $sheet->setCellValue('D' . $rowNumber1, 'Service Name');
                        $sheet->setCellValue('E' . $rowNumber1, 'Permit Rate');
                        $sheet->setCellValue('F' . $rowNumber1, 'Processing Fee');
                        $sheet->setCellValue('G' . $rowNumber1, 'Total Revenue ($)');
                        $sheet->setCellValue('H' . $rowNumber1, 'Total Discount ($)');
                        $sheet->setCellValue('I' . $rowNumber1, 'Total Refund ($)');
                        $sheet->setCellValue('J' . $rowNumber1, 'Canceled Date');

                        // $sheet5->setCellValue('F' . $rowNumber1, 'Ticket Number');

                        $kevent = $rowNumber1 + 1;
                        $event_processing = 0;
                        $permitGrossAmount = 0;
                        $totalPermitTicket = $permitCount = $processingfee = $permitFinalAmount = $refundamount = $permitDiscountAmount = 0;
                        //dd('22',$getpermitData);
                        foreach ($getpermitData as $key => $eval) {
                            $sheet->getStyle('D' . $kevent)->getNumberFormat()->setFormatCode('0');
                            $sheet->getStyle('F' . $kevent)->getNumberFormat()->setFormatCode('0.0');

                            $sheet->cell('A' . $kevent . ':I' . $kevent, function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center');
                            });
                            if (!empty($eval['permit_type']) && $eval['permit_type'] != '-') {
                                $permitCount += $eval['PermitCount'];
                                $permitFinalAmount += $eval['permit_amount'];
                                $processingfee += $eval['processingfee'];
                                $refundamount += $eval['refund_amount'];
                                $permitDiscountAmount += $eval['discount_amount'];
                            }

                            // $event_base_rate += $eval['EventRate'];
                            $sheet->setCellValue('A' . $kevent, $eval['permit_type']);
                            $sheet->setCellValue('B' . $kevent, $eval['PermitCount']);
                            $sheet->setCellValue('C' . $kevent, $eval['account_number']);
                            $sheet->setCellValue('D' . $kevent, '-');
                            $sheet->setCellValue('E' . $kevent, $eval['permit_rate']);
                            $sheet->setCellValue('F' . $kevent, $eval['processing_fee']);
                            $sheet->setCellValue('G' . $kevent, $eval['permit_amount']);
                            $sheet->setCellValue('H' . $kevent, $eval['discount_amount']);
                            $sheet->setCellValue('I' . $kevent, $eval['refund_amount']);
                            $sheet->setCellValue('J' . $kevent, '-');

                            // $sheet5->setCellValue('F' . $kevent, $eval['TicketNumber']);

                            if (is_numeric($eval['PermitNumber'])) {
                                $totalPermitTicket += $eval['NoofTickets'];
                                $permitGrossAmount += floatval($eval['GrossAmount']);
                            }
                            // if (!empty($eval['PermitNumber'])) {
                            // $totalPermitTicket += $eval['NoofTickets'];
                            // $permitGrossAmount += floatval($eval['GrossAmount']);
                            // }
                            $kevent++;
                        }

                        $sheet->getStyle('D' . $kevent)->getNumberFormat()->setFormatCode('0');
                        $totalRowForPermit = $kevent;
                        $sheet->setCellValue('A' . $totalRowForPermit, 'Total ($)');
                        $sheet->setCellValue('B' . $totalRowForPermit, $permitCount);
                        $sheet->setCellValue('C' . $totalRowForPermit, '');
                        $sheet->setCellValue('D' . $totalRowForPermit, '');
                        $sheet->setCellValue('E' . $totalRowForPermit, '');
                        $sheet->setCellValue('F' . $totalRowForPermit, $processingfee);
                        $sheet->setCellValue('G' . $totalRowForPermit, $permitFinalAmount);
                        // $sheet5->setCellValue('F' . $totalRowForPermit, $permitDiscountAmount);$permitFinalAmount
                        $sheet->setCellValue('H' . $totalRowForPermit, $permitDiscountAmount);
                        $sheet->setCellValue('I' . $totalRowForPermit, $refundamount);

                        //$sheet->setCellValue('G' . $totalRowForPermit, '');

                        $sheet->cell('A' . $totalRowForPermit . ':K' . $totalRowForPermit, function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });

                        // ************************************Remove after some time due to urgent changes
                        // dd($finalCodes4);
                        if (count($finalCodes4) > 0) {
                            $revenuSheetRowStart = $totalRowForPermit + 2;
                            $rowNumber = $revenuSheetRowStart + 1;
                            $sheet->mergeCells('A' . $revenuSheetRowStart . ':K' . $revenuSheetRowStart);
                            // $sheet5->mergeCells('E' .$revenuSheetRowStart .':I' .$revenuSheetRowStart);
                            $sheet->setCellValue('A' . $revenuSheetRowStart, 'Non Revenue Tickets');
                            $sheet->cell('A' . $revenuSheetRowStart, function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center');
                                $cell->setFontWeight('bold');
                                $cell->setBackground('#D9E1F2');
                                $cell->setFontColor('#272829');
                                $cell->setFontSize('12');
                            });
                            $sheet->cell('A' . $rowNumber . ':K' . $rowNumber, function ($cell) use ($color) {
                                $cell->setAlignment('center');
                                $cell->setFontWeight('bold');
                                $cell->setBackground($color);
                                $cell->setFontColor('#ffffff');
                                $cell->setFontSize('12');
                            });
                            // dd($count);

                            $sheet->setCellValue('A' . $rowNumber, 'Business Name');
                            $sheet->setCellValue('B' . $rowNumber, 'Policy Name');
                            $sheet->setCellValue('C' . $rowNumber, 'No of Tickets');
                            $sheet->setCellValue('D' . $rowNumber, 'Net Amount ($)');
                            $sheet->setCellValue('E' . $rowNumber, 'Processing Fee ($)');
                            $sheet->setCellValue('F' . $rowNumber, 'Gross Amount ($)');
                            $sheet->setCellValue('G' . $rowNumber, 'Validated Amount ($)');
                            $sheet->setCellValue('H' . $rowNumber, 'Paid Amount ($)');
                            // $sheet5->setCellValue('I' . $rowNumber, 'Total Revenue ($)');

                            $i = $rowNumber + 1;
                            // dd($i);
                            $sheet->getStyle('F')->getNumberFormat()->setFormatCode('0.00');
                            foreach ($finalCodes4 as $key => $value) {
                                $sheet->cell('A' . $i . ':K' . $i, function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                });

                                $sheet->setCellValue('A' . $i, $value['Business Name']);
                                $sheet->setCellValue('B' . $i, $value['Policy Name']);
                                $sheet->setCellValue('C' . $i, isset($value['No of Tickets']) ? $value['No of Tickets'] : $value['No of Ticket']);
                                $sheet->setCellValue('D' . $i, $value['Net Amount ($)']);
                                $sheet->setCellValue('E' . $i, isset($value['Processing Fee ($)']) ? $value['Processing Fee ($)'] : $value['Processing Fee ($)']);
                                $sheet->setCellValue('F' . $i, isset($value['Gross Amount ($)']) ? $value['Gross Amount ($)'] : $value['Gross Total ($)']);
                                $sheet->setCellValue('G' . $i, isset($value['Validated Amount ($)']) ? $value['Validated Amount ($)'] : $value['Validation Amount ($)']);
                                $sheet->setCellValue('H' . $i, $value['Paid Amount ($)']);
                                // $sheet5->setCellValue('I' . $i, $value['Total Revenue ($)']);
                                $i++;
                            }
                            $sheet->cell('A' . $i . ':K' . $i, function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center'); // Center vertically
                            });
                            $validationTicketTotal = $validationAmountTotal = 0;
                            $sheet->setCellValue('A' . $i, 'Total ($)');
                            $sheet->setCellValue('B' . $i, '');
                            $sheet->setCellValue('C' . $i, $validationTicketTotal);
                            $sheet->setCellValue('D' . $i, $totalNetAmount);
                            $sheet->setCellValue('E' . $i, $totalServiceAmount);
                            $sheet->setCellValue('F' . $i, $totalGrossAmount);
                            $sheet->setCellValue('G' . $i, $validationAmountTotal);
                            $sheet->setCellValue('H' . $i, $validationPaidAmountTotal);
                            // $sheet5->setCellValue('I' . $i, $validatedGTotal);
                            /** for color of non revenue total row */
                            $nonRevenueTotal =  $i + 1;
                            $row_name = 'A' . $nonRevenueTotal . ':K' . $nonRevenueTotal;
                            $sheet->cell($row_name, function ($row) use ($color) {
                                $row->setBackground($color);
                                $row->setFontColor('#ffffff');
                                $row->setFont(array(
                                    'family' => 'Calibri',
                                    'size' => '12',
                                    'bold' => true
                                ));
                            });
                        } else {
                            $sheet->getStyle('F')->getNumberFormat()->setFormatCode('0.00');
                            $i =  $revenuSheetRowStart = $totalRowForPermit;
                        }
                        $TotalRevenueNonValidated = $permitGrossTotalAmount = 0;
                        $totalAmountForNonValidated = $validationPaidAmountTotal + $TotalRevenueNonValidated + $permitGrossTotalAmount;

                        /** new code after reservation */
                        /* Reservation Related Code */
                        // dd(count($permitTickets), $permitTickets);
                        if (count($reservationTickets) > 0) {
                            $j = $i + 2;
                            //dd($j);
                            // dd($j);
                            $sheet->mergeCells('A' . $j . ':I' . $j);
                            $sheet->setCellValue('A' . $j, 'Tickets Cashiered Reservation');
                            $sheet->cell('A' . $j, function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center');
                                $cell->setFontWeight('bold');
                                $cell->setBackground($color);
                                $cell->setFontColor('#ffffff');
                                $cell->setFontSize('12');
                            });

                            $i = $j + 3;
                            // dd($reservationTickets);
                            $sheet->setCellValue('A' . $i, 'Rate');
                            $sheet->setCellValue('B' . $i, 'Booking Id');
                            $sheet->setCellValue('C' . $i, 'No of Tickets');
                            $sheet->setCellValue('D' . $i, 'Duration (Hours)');
                            $sheet->setCellValue('E' . $i, 'Total Collected ($)');
                            //    $sheet5->setCellValue('E' . $i, 'Gross Amount ($)');
                            //    $sheet5->setCellValue('F' . $i, 'Processing Fees ($)');
                            // $sheet5->setCellValue('G' . $i, 'Net Amount ($)');
                            $sheet->setCellValue('F' . $i, 'Discount Amount ($)');
                            $sheet->setCellValue('G' . $i, 'Promocode');
                            $sheet->setCellValue('H' . $i, 'Transaction Date');
                            $sheet->setCellValue('I' . $i, 'Cancel Date');

                            $cel = 'A' . $i . 'I' . $i;
                            //  dd( $cel);
                            $reservations_sql_fortechcoderesult = 0;
                            $sheet->cell('A' . $i . ':I' . $i, function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center');
                                $cell->setFontWeight('bold');
                                $cell->setBackground($color);
                                $cell->setFontColor('#ffffff');
                                $cell->setFontSize('12');
                            });
                            $i = $i + 1;
                            $totalReservationCount = $totalAmountforReservation = $totalDiscountAmount = $reservationProcessingFee = 0;
                            // dd($reservationTickets);
                            $sheet->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
                            $resrvation_colorCode = 0;
                            foreach ($reservationTickets as $key => $value) {
                                $sheet->cell('A' . $i . ':G' . $i, function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                });
                                // dd($value);
                                $totalReservationCount += $value['No of Tickets'];
                                $totalAmountforReservation += $value['Ticket Amount ($)'];
                                $totalDiscountAmount += $value['Discount Amount ($)'];
                                $sheet->setCellValue('A' . $i, $value['Rate']);
                                $sheet->setCellValue('B' . $i, '-');
                                $sheet->setCellValue('C' . $i, $value['No of Tickets']);
                                $sheet->setCellValue('D' . $i, $value['length']);
                                $sheet->setCellValue('E' . $i, $value['Ticket Amount ($)']);
                                // $sheet5->setCellValue('E' . $i, $value['Gross Amount ($)']);
                                // $sheet5->setCellValue('F' . $i, $value['Processing Fees ($)']);
                                //  $sheet5->setCellValue('G' . $i, $value['Net Amount ($)']);
                                $sheet->setCellValue('F' . $i, $value['Discount Amount ($)']);
                                $sheet->setCellValue('G' . $i, $value['promocode']);
                                $sheet->setCellValue('H' . $i, '-');
                                $sheet->setCellValue('I' . $i, '-');
                                $reservations_sql_fortechcode = "SELECT * FROM reservations WHERE reservations.partner_id IN ($this->partner_id) AND reservations.start_timestamp >= '$this->startOfMonth' AND reservations.start_timestamp <= '$this->endOfMonth' AND reservations.deleted_at IS NULL  and reservations.event_id is null and reservations.total={$value['Rate']}";
                                $reservations_sql_fortechcoderesult = DB::select($reservations_sql_fortechcode);
                                //dd( count($reservations_sql_fortechcode));
                                foreach ($reservations_sql_fortechcoderesult as $resultkey => $resultValue) {
                                    $i++;
                                    $reservationProcessingFee += $resultValue->processing_fee;

                                    $sheet->cell('A' . $i . ':G' . $i, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center'); // Center vertically
                                    });
                                    $sheet->setCellValue('A' . $i, '-');
                                    $sheet->setCellValue('B' . $i, $resultValue->ticketech_code);
                                    $sheet->setCellValue('C' . $i, '-');
                                    $sheet->setCellValue('D' . $i, $resultValue->length);
                                    $sheet->setCellValue('E' . $i, $resultValue->total);
                                    // $sheet5->setCellValue('E' . $i, $resultValue->total);
                                    // $sheet5->setCellValue('F' . $i, $resultValue->processing_fee);
                                    //  $sheet5->setCellValue('G' . $i, $resultValue->total);created_atcancelled_at
                                    $sheet->setCellValue('F' . $i, $resultValue->discount);
                                    $sheet->setCellValue('G' . $i, $resultValue->promocode);
                                    $sheet->setCellValue('H' . $i, $resultValue->created_at);
                                    $sheet->setCellValue('I' . $i, $resultValue->cancelled_at);
                                }

                                $i++;
                            }
                            $i++;

                            //reservation counter total start here
                            // $resrvation_colorCode = count($finalCodes5) + count($reservationTickets) + count($reservations_sql_fortechcoderesult)+ $topSpace + 3; // color for reser top
                            $resrvation_colorCode = $i + 1;
                            $sheet->row($resrvation_colorCode, array('Total ($)', '', $totalReservationCount, '', $totalAmountforReservation, $totalDiscountAmount));
                            //dd($totalDiscountAmount);
                            // dd('Total ($)', '', $totalReservationCount, $totalAmountforReservation,'', $TotalRevenueNonValidated, $totalDiscountAmount);


                            $sheet->cell('A' . $resrvation_colorCode . ':I' . $resrvation_colorCode, function ($cell) use ($color) {
                                $cell->setAlignment('center');
                                $cell->setValignment('center');
                                $cell->setFontWeight('bold');
                                $cell->setBackground($color);
                                $cell->setFontColor('#ffffff');
                                $cell->setFontSize('12');
                            });

                            //End reservation counter total start here
                        }
                        // dd($reservationProcessingFee);
                        /* End Reservation Related Code */
                        //event  grid start here
                        if (!isset($resrvation_colorCode)) {
                            $resrvation_colorCode = $non_colorCode;
                        }
                        $eventGridStart =  $resrvation_colorCode + 2;
                        // dd( $eventGridStart);
                        $sheet->mergeCells('A' . $eventGridStart . ':I' . $eventGridStart);
                        $sheet->setCellValue('A' . $eventGridStart, 'Tickets Cashiered Events');
                        $sheet->cell('A' . $eventGridStart, function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });
                        $i = $eventGridStart + 1;
                        $sheet->setCellValue('A' . $i, 'Event Name');
                        $sheet->setCellValue('B' . $i, 'Event Count');
                        $sheet->setCellValue('C' . $i, 'Event Rate');
                        $sheet->setCellValue('D' . $i, 'Total Revenue');
                        //  $sheet5->setCellValue('E' . $i, 'Payment Mode');

                        //  $sheet5->setCellValue('G' . $i, 'Net Amount ($)');
                        $sheet->setCellValue('E' . $i, 'Discount Amount ($)');
                        $sheet->setCellValue('F' . $i, 'Promocode');
                        $sheet->setCellValue('G' . $i, 'Transcation Date');
                        $sheet->setCellValue('H' . $i, 'Cancel Date');
                        $i = $i + 1;
                        // dd($eventTicket);
                        $totalevent = 0;
                        $totalEventRate = $totalRevenue = $totalDiscount = $eventProcessingFee = 0;
                        foreach ($eventTicket as $eventValue) {
                            $totalevent += $eventValue['Event Count'];
                            $totalEventRate += $eventValue['Event Rate'];
                            $totalRevenue += $eventValue['Total Revenue ($)'];
                            $totalDiscount = $eventValue['Discount Amount ($)'];
                            $sheet->cell('A' . $i . ':I' . $i, function ($cell) use ($color) {
                                $cell->setAlignment('center'); // Center horizontally
                                $cell->setValignment('center');
                                $cell->setFont([
                                    'bold' => true,
                                    'size' => 12,
                                    'color' => ['rgb' => $color]
                                ]);
                            });

                            // $event_base_rate += $eval['EventRate'];
                            $sheet->setCellValue('A' . $i, $eventValue['Event Name']);
                            $sheet->setCellValue('B' . $i, $eventValue['Event Count']);
                            $sheet->setCellValue('C' . $i, $eventValue['Event Rate']);
                            $sheet->setCellValue('D' . $i, $eventValue['Total Revenue ($)']);
                            // $sheet5->setCellValue('E' . $i, $eventValue['Transcation amount']);
                            $sheet->setCellValue('E' . $i, $eventValue['Discount Amount ($)']);
                            // $sheet5->setCellValue('G' . $i, $eventValue['refund_amount']);
                            $sheet->setCellValue('F' . $i, $eventValue['promocode']);
                            $sheet->setCellValue('G' . $i, '-');
                            $sheet->setCellValue('H' . $i, '-');
                            //get ticket list here for event
                            $event_sql_ticket = "SELECT   r.*,e.title,r.cancelled_at as cancelDate,r.created_at as createdAt FROM reservations r JOIN events e ON  r.event_id = e.id
                                            WHERE 
                                                r.partner_id IN ($this->partner_id) 
                                                AND r.start_timestamp >= '$this->startOfMonth'
                                                AND r.start_timestamp <= '$this->endOfMonth'
                                                AND r.deleted_at IS NULL
                                                AND r.event_id IS NOT NULL
                                                AND e.deleted_at is null
                                                AND r.deleted_at is null
                                                AND e.id = '{$eventValue['eventid']}' ";
                            $event_sql_ticket_result = DB::select($event_sql_ticket);
                            foreach ($event_sql_ticket_result as $resultevent => $resultEventValue) {
                                $eventProcessingFee += $resultEventValue->processing_fee;
                                $i++;
                                $sheet->setCellValue('A' . $i, '-');
                                $sheet->setCellValue('B' . $i, $resultEventValue->ticketech_code);
                                $sheet->setCellValue('C' . $i, $resultEventValue->parking_amount);
                                $sheet->setCellValue('D' . $i, $resultEventValue->total);
                                $sheet->setCellValue('E' . $i, $resultEventValue->discount);
                                // $sheet5->setCellValue('F' . $i, $resultEventValue->discount);
                                $sheet->setCellValue('F' . $i, $resultEventValue->promocode);
                                $sheet->setCellValue('G' . $i, $resultEventValue->createdAt);
                                $sheet->setCellValue('H' . $i, $resultEventValue->cancelDate);
                            }
                            //end ticket list here for event
                            $i++;
                        }

                        $i = $i + 1;
                        $sheet->row($i, array('Total ($)',  $totalevent,  $totalEventRate,  $totalRevenue,  $totalDiscount, '-'));
                        $sheet->cell('A' . $i . ':I' . $i, function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });
                        /** new code end reservation  */
                        /** payment breakdown Section Started. **/
                        $jCell = $i + 2;
                        // dd($i, $jCell);
                        $sheet->mergeCells('A' . $jCell . ':K' . $jCell);
                        //$sheet5->mergeCells('E' . $jCell . ':I' . $jCell);

                        $sheet->setCellValue('A' . $jCell, 'Payment Breakdown');

                        $sheet->cell('A' . $jCell, function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#D9E1F2');
                            $cell->setFontColor('#272829');
                            $cell->setFontSize('12');
                        });



                        // // Total Payment Received
                        // dd($TotalPaymentReceived, $TotalCc, $validationPaidAmountTotal, $permitGrossTotalAmount, $TotalRevenueNonValidated);
                        // $totalPayment = $TotalPaymentReceived + $TotalCc + $validationPaidAmountTotal + $permitGrossTotalAmount + $TotalRevenueNonValidated;
                        // $totalPayment =  $TotalRevenueNonValidated + $permitGrossTotalAmount + $validationPaidAmountTotal;
                        //dd( $TotalRevenueNonValidated , $permitGrossTotalAmount );
                        $validationAmountTotal = $totalDiscountAmountNonValidated = $totalDiscountAmount = $totalDiscount = $totalrefundAmount = $processingFeeNonValidated = $eventProcessingFee = $reservationProcessingFee = 0;
                        $totalPayment =  $TotalRevenueNonValidated + $permitGrossTotalAmount;
                        // $totalPayment = $grossAmountNonValidated;
                        $newCell = $jCell + 1;
                        $sheet->getStyle('B' . $newCell)->getNumberFormat()->setFormatCode('0.00');
                        $sheet->setCellValue('A' . $newCell, 'Total Payment Received ($)');
                        $sheet->setCellValue('B' . $newCell, $TotalCc);
                        // total validated Amount
                        $ValidatedCell = $jCell + 2;
                        $sheet->getStyle('B' . $ValidatedCell)->getNumberFormat()->setFormatCode('0.00');
                        $sheet->setCellValue('A' . $ValidatedCell, 'Total Validated Amt ($)');
                        $sheet->setCellValue('B' . $ValidatedCell, $validationAmountTotal);


                        $DiscountCell = $ValidatedCell + 1;
                        $refundAmountcell = $DiscountCell + 1;
                        // dd( $processingfee , ($processingFeeNonValidated),$eventProcessingFee);
                        $sheet->getStyle('B' . $DiscountCell)->getNumberFormat()->setFormatCode('0.00');
                        $sheet->setCellValue('A' . $DiscountCell, 'Total Discount Amt ($)');
                        $sheet->setCellValue('B' . $DiscountCell, $permitDiscountAmount + $totalDiscountAmountNonValidated + $totalDiscountAmount + $totalDiscount);
                        $sheet->setCellValue('A' . $refundAmountcell, 'Refund Amt ($)');
                        $sheet->setCellValue('B' . $refundAmountcell, $refundamount + $totalrefundAmount);
                        $sheet->setCellValue('E' . $newCell, 'Total Revenue ($)');
                        $sheet->setCellValue('F' . $newCell, ($TotalCc));

                        $sheet->setCellValue('E' . $ValidatedCell, 'Net Revenue ($)');
                        $sheet->setCellValue('F' . $ValidatedCell, floatval($TotalCc - $processingfee - $processingFeeNonValidated - $eventProcessingFee - $reservationProcessingFee));
                        // $sheet5->setCellValue('E' . $ValidatedCell, 'Net Revenue ($)');
                        // $sheet5->setCellValue('F' . $ValidatedCell, floatval($totalPayment + $validationAmountTotal - ($processingFeeNonValidated)));
                        // $sheet5->setCellValue('F' . $ValidatedCell, floatval(($totalPayment + $validationAmountTotal) - ($totalServiceAmount + $processingFeeNonValidated)));




                        $nonCashCell = $DiscountCell + 2;
                        $sheet->cell('A' . $nonCashCell . ':K' . $nonCashCell, function ($row) use ($color) {
                            $row->setFontColor('#ffffff');
                            $row->setBackground($color);
                            $row->setFont(array(
                                'family' => 'Calibri',
                                'size' => '12',
                                'bold' => true
                            ));

                            $row->setFontWeight('bold');
                            $row->setValignment('center');
                        });

                        $sheet->row($nonCashCell, array('Non-Cash Receipts'));
                        $sheet->setCellValue('D' . $nonCashCell, 'Cash Receipts');
                        $cardTupeCell = $nonCashCell + 1;
                        $totalCell = $nonCashCell + 2;
                        $sheet->cell($cardTupeCell, function ($cell) use ($color) {
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#EDEDED');
                            $cell->setFontColor('#272829');
                            $cell->setFontSize('12');
                        });

                        $sheet->cell('B' . $cardTupeCell, function ($cell) use ($color) {
                            $cell->setAlignment('right'); // Center horizontally
                            $cell->setValignment('right'); // Center vertically
                        });
                        $sheet->cell('E' . $cardTupeCell, function ($cell) use ($color) {
                            $cell->setAlignment('right'); // Center horizontally
                            $cell->setValignment('right'); // Center vertically
                        });
                        $sheet->cell('F' . $cardTupeCell, function ($cell) use ($color) {
                            $cell->setAlignment('right'); // Center horizontally
                            $cell->setValignment('right'); // Center vertically
                        });
                        $sheet->setCellValue('A' . $cardTupeCell, 'Card Type');
                        $sheet->setCellValue('B' . $cardTupeCell, 'Total ($)');

                        $sheet->setCellValue('D' . $cardTupeCell, 'Payment Type');
                        $sheet->setCellValue('E' . $cardTupeCell, 'Total ($)');
                        $sheet->setCellValue('F' . $cardTupeCell, 'Discount ($)');
                        $i = $cardTupeCell + 1;
                        $j = $cardTupeCell + 1;
                        // dd($finalCodes3);
                        foreach ($finalCodes3 as $key => $value) {
                            if ($value['total'] > 0) {
                                $sheet->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                                $sheet->setCellValue('A' . $i, $value['no_cash_receipts']);
                                $sheet->setCellValue('B' . $i, $value['total']);
                                $i++;
                            }
                        }
                        if (count($finalCodes2) > 0) {
                            foreach ($finalCodes2 as $key => $val) {
                                $sheet->setCellValue('D' . $j, 'Total ($)');
                                $sheet->setCellValue('E' . $j, $val['total']);
                                $sheet->setCellValue('F' . $j, $val['discount']);
                                $j++;
                            }
                        } else {
                            $sheet->setCellValue('D' . $j, 'Total ($)');
                            $sheet->setCellValue('E' . $j, 0);
                            $sheet->setCellValue('F' . $j, 0);
                        }

                        $cellColor = 'A' . $i . ':B' . $i;
                        $sheet->cell($cellColor, function ($row) use ($color) {
                            $row->setBackground($color);
                            $row->setFontColor('#ffffff');
                            $row->setFont(array(
                                'family' => 'Calibri',
                                'size' => '12',
                                'bold' => true
                            ));
                        });

                        $cellColor = 'D' . $j . ':F' . $j;
                        $sheet->cell($cellColor, function ($row) use ($color) {
                            $row->setBackground($color);
                            $row->setFontColor('#ffffff');
                            $row->setFont(array(
                                'family' => 'Calibri',
                                'size' => '12',
                                'bold' => true
                            ));
                        });
                        $sheet->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                        // echo$i;die;

                        $sheet->row($i, array('Total ($)', $TotalCc));
                        // //end payment breakdown issue
                        //merge cell for formatting last data
                        if ($i < $j) {
                            $i = $i + 1;
                        }
                        $k = $i + 1;
                        // $rowVerticalFormate = 'J1:J' . $k;
                        // $sheet5->mergeCells($rowVerticalFormate);
                        $sheet->getStyle("D4")->getNumberFormat()->setFormatCode('0');
                        $sheet->getStyle("D3")->getNumberFormat()->setFormatCode('0');

                        //horigental cell
                        $rowHorizentalFormate = 'A' . $k . ':K' . $k;
                        $sheet->mergeCells($rowHorizentalFormate);

                        //grid for Above
                        //  $totalRevenueAmountgrid= $totalAmountForNonValidated+$totalAmountforReservation;
                        // $totalAmountforReservation
                        //$sheet->mergeCells('A4:K4');
                        $sheet->cell('A4:K4', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });

                        $totalRenew = $totalCancel = 0;
                        $permit_sql_cancel = "SELECT COUNT(id) AS cancelCount FROM permit_requests where  partner_id IN ($this->partner_id) and cancelled_at>='$this->startOfMonth' and cancelled_at<='$this->endOfMonth'  ";
                        $permitResultsCancel = DB::select($permit_sql_cancel);
                        $totalCancel = $permitResultsCancel[0]->cancelCount;
                        $permit_sql_renew = "SELECT COUNT(id) AS renewCount FROM permit_revenue_flat_table where   partner_id IN ($this->partner_id) and desired_start_date>='$this->startOfMonth' and desired_start_date<='$this->endOfMonth' and action_flag ='renewed' ";
                        $renewResults = DB::select($permit_sql_renew);
                        $totalRenew = $renewResults[0]->renewCount;


                        // $totalevent = $totalRenew = $totalCancel = 0;
                        $sheet->setCellValue('A3', "Total Tickets");
                        $sheet->setCellValue('B3', $getdriveupData[1][0]);
                        $sheet->setCellValue('H3', 'Total Revenue');

                        $sheet->setCellValue('I3', $TotalCc);
                        $sheet->setCellValue('C3', "Event Count");
                        $sheet->setCellValue('D3', $totalevent);
                        // $sheet5->setCellValue('I3', '$' . $totalRevenueAmountgrid );
                        // dd($totalAmountForNonValidated+$totalAmountforReservation);
                        $sheet->setCellValue('E3', "Reservation Count");
                        $sheet->setCellValue('F3', isset($totalReservationCount) ? $totalReservationCount : 0.0);
                        $sheet->setCellValue('A4', "Purchased Count");
                        $sheet->setCellValue('B4', $permitCount - $totalRenew);
                        $sheet->setCellValue('C4', "Renewed Count");
                        $sheet->setCellValue('D4',  $totalRenew);
                        $sheet->setCellValue('E4', "Canceled Count");
                        $sheet->setCellValue('F4', $totalCancel);
                        //end***********************************************


                        //dd($getdriveupData,'dssd');
                        //**********************************end Permit Data Here */
                    });
                }
            )->store('xls');

            $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
            $this->sendEmailExcelForPArtner($excelSheetName, $path_to_file, "Multiple Location", $this->startOfMonth, $this->endOfMonth, "<EMAIL>");
            $response['status'] = true;
            $response['message'] = 'Email send successfully.';
            return response()->json($response, 200);
        } catch (\Throwable $e) {
            dd('Error ' . $e->getMessage() . ' File ' . $e->getFile() . ' Line no :- ' . $e->getLine());
        }
    }

    public function sendEmailExcelForPArtner($excelSheetName, $path_to_file, $facilityName, $fromDate, $todate, $email)
    {
        $emailArray = explode(',', $email);
        Mail::send([], [], function ($message)  use ($excelSheetName, $path_to_file, $facilityName, $fromDate, $todate, $emailArray) {
            // $message->bcc(['<EMAIL>', '<EMAIL>']);
            // $message->to(config('parkengage.townsend.open_ticket_emails'));
            $message->to(['<EMAIL>', '<EMAIL>', '<EMAIL>']);
            // $message->to(['<EMAIL>', '<EMAIL>']);
            // $message->to($emailArray);
            $dateRange =  date("d F, Y", strtotime($fromDate)) . '-' . date("d F, Y", strtotime($todate));
            $subject = $facilityName  . ' Report - ' . $dateRange;
            $message->subject($subject);
            $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
            $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
            // $this->successLog->info("Mail Sent success with failname :  {$path_to_file}");
            if (file_exists($path_to_file)) {
                $message->attach($path_to_file);
            }
        });
    }
}
