<?php

namespace App\Console\Commands\ReportShuduler;

use Mail;
use Exception;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use DB;
use Carbon\Carbon;
use App\Http\Helpers\ReportBuilder;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\ParkEngage\BrandSetting;

class Promocode extends Command
{

    protected $signature = 'usm:promo-report';

    protected $description = 'get one month data';

    protected $log;

    const PARTNER_ID = 19349;
    protected $request;
    protected $reportBuilder;
    private $partner_id = self::PARTNER_ID;
    private $facility_id = '167';
    private $startOfMonth = '2024-10-01 00:00:00';
    private $endOfMonth = '2024-10-31 23:59:59';
    public function __construct(Request $request, LoggerFactory $logFactory, ReportBuilder $reportBuilder)
    {

        parent::__construct();
        // $this->startOfMonth = Carbon::now()->startOfMonth()->toDateString() . ' 00:00:00';
        // $this->endOfMonth = Carbon::now()->endOfMonth()->toDateString() . ' 23:59:59';
        $this->startOfMonth = Carbon::now()->subDay()->toDateString() . ' 00:00:00';
        $this->endOfMonth = Carbon::now()->subDay()->toDateString() . ' 23:59:59';

        $request->merge([
            'partner_id' => self::PARTNER_ID,
            'fromDate' => $this->startOfMonth,
            'toDate' => $this->endOfMonth,
            'facility_id' => '167'
        ]);

        $this->request = $request;
        $this->reportBuilder = new ReportBuilder($request);
    }

    public function handle()
    {
        set_time_limit(300);
        $facilityIDS = $facilityIDS = [];
        // dd($this->partnerId);

        try {



            $facility_id =  $this->facility_id;

            //dd('dsds---1');
            $fromDateForPdf = $this->startOfMonth;
            $toDateForPdf = $this->endOfMonth;
            $partner_id = self::PARTNER_ID;
            $promocodequery = $facilityquery = $location = "";
            $countfacility = 0;
            if (!empty($facility_id) && $facility_id != "all") {
                $arrayfacility = explode(",", $facility_id);
                $countfacility = count($arrayfacility);
                $facilityquery = "and f.id in ($facility_id)";
            }
            if ($countfacility > 1 || $facility_id == "all") {
                $location = "Multiple Location";
            }
            if (!empty($promocode) &&  $promocode != "all") {
                $array = explode(",", $promocode);
                $quoted_string = "'" . implode("','", $array) . "'";
                $promocodequery = "and  pu.promocode in ($quoted_string)";
            }

            // dd(  $facility_id_array);
            $sql = "SELECT COALESCE(t.facility_id, r.facility_id, p.facility_id) AS final_facility_id, pu.email,pu.promocode,
                    pu.reservation_id,account_number,t.ticket_number,pu.created_at,pr.description, pr.valid_from,pr.valid_to ,f.full_name,f.garage_code,pu.discount_amount
                   FROM promo_usages AS pu LEFT JOIN tickets AS t ON t.id = pu.ticket_id LEFT JOIN reservations AS r ON r.ticketech_code = pu.reservation_id
                     LEFT JOIN permit_requests AS p ON p.id = pu.permit_request_id
                   INNER JOIN promo_codes AS pc ON pc.promocode = pu.promocode
                   INNER JOIN promotions AS pr ON pr.id = pc.promotion_id
                    INNER JOIN facilities AS f ON f.id = COALESCE(t.facility_id, r.facility_id, p.facility_id)
                    
                   WHERE  ( pu.partner_id IN ($partner_id) OR t.partner_id IN ($partner_id)   OR r.partner_id IN ($partner_id)  OR p.partner_id IN ($partner_id) )
                    and pu.created_at >='$fromDateForPdf' and pu.created_at <='$toDateForPdf'  $promocodequery $facilityquery";
            //   // dd(';');

            $promoReport = DB::select($sql);
            $excelSheetName = ucwords(str_replace(' ', '', 'Promo Report')) . date('m-d-Y');
            if (!empty($promoReport)) {
                $excelCreate =   Excel::create(
                    $excelSheetName,
                    function ($excel) use ($excelSheetName, $fromDateForPdf, $toDateForPdf, $promoReport, $location) {
                        if (isset($excelSheetName) && !empty($excelSheetName)) {
                            $excel->sheet($excelSheetName, function ($sheet) use ($fromDateForPdf, $toDateForPdf, $promoReport, $location) {
                                //for header setting 
                                $sheet->setWidth(array(
                                    'A'     => 17,
                                    'B'     => 17,
                                    'C'     =>  22,
                                    'D'     =>  23.87,
                                    'E'     =>  30,
                                    'F'     =>  23.75,
                                    'G'     =>  15.57,
                                    'H'    =>   14.20,
                                    'I'    =>   12.20,
                                    'J'    =>   21.29
                                ));
                                $color = "#1D1160";

                                $sheet->mergeCells('A1:J1');
                                $sheet->getRowDimension(1)->setRowHeight(40);
                                $sheet->setCellValue('A1', 'Promo Code Usage Report');
                                $sheet->cell('A1:M1', function ($cell) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#281875');
                                    $cell->setFontfamily('Arial Black');
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('28');
                                });
                                // {{env('APP_URL').'/brand-settings-logo/356320_logo.png'}}
                                // {{env('APP_URL').'/brand-settings-logo/'.$brand_setting->id}}
                                $sheet->mergeCells('A2:C2');
                                $cellValue = "Date Range - " .  date('m-d-Y', strtotime($fromDateForPdf)) .  ' - ' . date('m-d-Y', strtotime($toDateForPdf));
                                $cellValue .= "\nPrint Date - " .   date('m-d-Y', strtotime('now'));
                                $sheet->setCellValue('A2', "$cellValue");
                                $sheet->getStyle('A2')->getAlignment()->setWrapText(true);
                                // Set the height of cell H2 (adjust as needed)
                                $sheet->getRowDimension(2)->setRowHeight(80);
                                $sheet->getRowDimension(3)->setRowHeight(50);


                                // $sheet->getRowDimension(3)->setRowHeight(50);
                                // $sheet->mergeCells('A3:J3');
                                $sheet->cell('A2:C2', function ($cell) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setBackground('#D6DCE4');
                                    $cell->setFontWeight('bold');
                                    $cell->setFontColor('#000000');
                                    $cell->setFontSize('18');
                                });
                                if (!empty($location)) {
                                    $locationName = $location;
                                    $garageCode = '-';
                                } else {
                                    $locationName = $promoReport[0]->full_name;
                                    $garageCode = $promoReport[0]->garage_code;
                                }

                                //print_r($locationName[0]['Garage Name']);die;
                                $location = "Location Name \r " . $locationName;
                                $sheet->mergeCells('D2:G2');
                                $sheet->setCellValue('D2', "Multiple Location");
                                $sheet->getStyle('D2')->getAlignment()->setWrapText(true);

                                $sheet->cell('D2:G2', function ($cell) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#ffffff');
                                    $cell->setFontColor('#000000');
                                    $cell->setFontSize('18');
                                });
                                $sheet->mergeCells('H2:M2');

                                $locationId = "Location ID \n" . $garageCode;
                                $sheet->setCellValue('H2', "Multiple Garage");
                                $sheet->getStyle('H2')->getAlignment()->setWrapText(true);
                                $sheet->cell('H2:M2', function ($cell) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center'); // Center vertically
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#D6DCE4');
                                    $cell->setFontColor('#040D12');
                                    $cell->setFontSize('18');
                                });




                                // Ticket Count and Revenue Row Start !!!!
                                $sheet->mergeCells('A3:B3');
                                $sheet->getRowDimension(3)->setRowHeight(50);


                                $sheet->cell('A3:B3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center');
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('12');
                                });
                                $count = 4;
                                $sheet->mergeCells('C3:M3');
                                //$sheet->setCellValue('C3', $count);
                                $sheet->cell('C3:M3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center');
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground($color);
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('12');
                                });




                                // Ticket Count and Revenue Row Close Here  !!!!
                                $sheet->mergeCells('A4:M4');

                                $sheet->setCellValue('A5', 'Ticket Number');
                                $sheet->setCellValue('B5', 'Promo Code');
                                $sheet->setCellValue('C5', 'Description');
                                $sheet->setCellValue('D5', 'Date Used');
                                $sheet->setCellValue('E5', 'Account');
                                $sheet->setCellValue('F5', 'Facility Name');
                                $sheet->setCellValue('G5', 'Zone ID');
                                $sheet->setCellValue('H5', 'Start Time');
                                $sheet->setCellValue('I5', 'End Time');
                                $sheet->setCellValue('J5', 'Amount');

                                $sheet->cell('A5:M5', function ($cell) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center');
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#1D1160');
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('9');
                                });

                                // dd($finalCodes1);

                                //  print_r($groupedData);
                                //  die;
                                $i = 6;
                                // dd($promoReport);
                                $totalDiscount = 0;
                                foreach ($promoReport as $promocodeNameKey => $promocodeNameValue) {
                                    $totalDiscount += $promocodeNameValue->discount_amount;
                                    $sheet->cell('A' . $i . ':M' . $i, function ($cell) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        // $cell->setFontWeight('bold');
                                        // $cell->setBackground('#1D1160');
                                        // $cell->setFontColor('#ffffff');
                                        // $cell->setFontSize('9');
                                    });
                                    if (!empty($promocodeNameValue->ticket_number)) {
                                        $ticketnumber = $promocodeNameValue->ticket_number;
                                    } elseif (!empty($promocodeNameValue->reservation_id)) {
                                        $ticketnumber = $promocodeNameValue->reservation_id;
                                    } elseif (!empty($promocodeNameValue->account_number)) {
                                        $ticketnumber = $promocodeNameValue->account_number;
                                    }
                                    $sheet->setCellValue('A' . $i, $ticketnumber);
                                    $sheet->setCellValue('B' . $i, $promocodeNameValue->promocode);
                                    $sheet->setCellValue('C' . $i, $promocodeNameValue->description);
                                    $sheet->setCellValue('D' . $i, $promocodeNameValue->created_at);
                                    $sheet->setCellValue('E' . $i, $promocodeNameValue->email);
                                    $sheet->setCellValue('F' . $i, $promocodeNameValue->full_name);
                                    $sheet->setCellValue('G' . $i, $promocodeNameValue->garage_code);
                                    $sheet->setCellValue('H' . $i, $promocodeNameValue->valid_from);
                                    $sheet->setCellValue('I' . $i, $promocodeNameValue->valid_to);
                                    $sheet->setCellValue('J' . $i, $promocodeNameValue->discount_amount);



                                    $i++;
                                }
                                $i = $i + 1;
                                $sheet->row($i, array('Total', '', '', '', '', '', '', '', '', $totalDiscount));
                                $j = 0;
                                $j = $i;

                                // dd($j);
                                $sheet->cell('A' . $j . ':M' . $j, function ($cell) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center');
                                    // $cell->setFontWeight('bold');
                                    $cell->setBackground('#1D1160');
                                    $cell->setFontColor('#ffffff');
                                    $cell->setFontSize('12');
                                });
                                $sheet->mergeCells('N1:N' . $i);
                                $k = $j + 1;
                                $sheet->mergeCells('A' . $k . ':M' . $k);
                            });
                        } else {
                            $this->log->info("No Record Found ");
                        }
                    }
                )->store('xls');
                $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                $this->sendEmailExcelForPArtner($excelSheetName, $path_to_file, "Multiple Location", $this->startOfMonth, $this->endOfMonth, "<EMAIL>");
                $response['status'] = true;
                $response['message'] = 'Email send successfully.';
                return response()->json($response, 200);
            } else {
                return response()->json([
                    'status' => 500,
                    'data' => null,
                    'errors' => [
                        'message' => 'There is no data for this date'
                    ]
                ], 500);
            }
        } catch (\Throwable $e) {
            dd('Error ' . $e->getMessage() . ' File ' . $e->getFile() . ' Line no :- ' . $e->getLine());
        }
    }

    public function sendEmailExcelForPArtner($excelSheetName, $path_to_file, $facilityName, $fromDate, $todate, $email)
    {
        $emailArray = explode(',', $email);
        Mail::send([], [], function ($message)  use ($excelSheetName, $path_to_file, $facilityName, $fromDate, $todate, $emailArray) {
            // $message->bcc(['<EMAIL>']);
            // $message->to(config('parkengage.townsend.open_ticket_emails'));
            $message->to(['<EMAIL>', '<EMAIL>', '<EMAIL>']);
            // $message->to($emailArray);
            $dateRange =  date("d F, Y", strtotime($fromDate)) . '-' . date("d F, Y", strtotime($todate));
            $subject = $facilityName  . ' Report - ' . $dateRange;
            $message->subject($subject);
            $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
            $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
            // $this->successLog->info("Mail Sent success with failname :  {$path_to_file}");
            if (file_exists($path_to_file)) {
                $message->attach($path_to_file);
            }
        });
    }
}
