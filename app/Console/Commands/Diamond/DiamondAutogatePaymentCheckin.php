<?php

namespace App\Console\Commands\Diamond;

use Mail;
use Exception;
use File;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use <PERSON><PERSON><PERSON>\Rest\Client;
use <PERSON><PERSON>lio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\User;
use App\Models\ParkEngage\TicketCitation;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;


class DiamondAutogatePaymentCheckin extends Command
{

    protected $signature = 'email:diamond-parking-autogate-checkin-payment {id} {type}';

    protected $description = 'Send checkin payment email.';

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/diamond-checkin-checkout')->createLogger('checkin-checkout');
    }

    public function handle()
    {
        try{
       $id = $this->argument('id');
       $type = $this->argument('type');
       if($type == 'normal'){
            $this->log->info("Mail start");
            $data = Ticket::with('facility')->where('id', $id)->first();
            //user mail
            try{

            $ticket_number = base64_encode($data->ticket_number);
            $facilityName =  ucwords($data->facility->full_name);
            $accountSid = env('TWILIO_ACCOUNT_SID');
            $authToken  = env('TWILIO_AUTH_TOKEN');
            $url = env('TOUCHLESS_APP_URL');
            $client = new Client($accountSid, $authToken);
            /*$encrypt = Crypt::encrypt(json_encode(['gate'=>'1', 'facility_id'=> $result->facility_id]));*/
            try
            {
                // Use the client to do fun stuff like send text messages!

                 if(isset($data->old_anet_transaction_id) && $data->old_anet_transaction_id !='')
                  {
                      $msg = "Thank you for extended your parking with $facilityName.Your ticket number is $data->ticket_number. Use the link to extend your stay. $url/autogate-extend-payment-details/$ticket_number";  
                  } else {
                    $msg = "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number. Use the link to extend your stay. $url/autogate-extend-payment-details/$ticket_number";
                  }      

                $client->messages->create(
                // the number you'd like to send the message to
                    $data->user->phone,
               array(
                     // A Twilio phone number you purchased at twilio.com/console
                     'from' => env('TWILIO_PHONE'),
                     // the body of the text message you'd like to send
                     //'body' => "Fine"
                     /*'body' => "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number. Use the following link to Check-Out. $url/touchless-parking-atlanta-prepaid-checkout-details/$ticket_number"*/
                    

                  


                     //'body' => "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number. Use the link to extend your stay. $url/autogate-extend-payment-details/$ticket_number"
                    'body' => $msg

                 )
             );
                $this->log->info("SMS sent to ".$data->user->phone);
            }catch (RestException $e)
            {
                
            }
                
            Mail::send(
                "diamond-checkin-checkout.touchless-parking-email-payment", ['data' => $data], function ($message) use($data) {
                    $message->to($data->user->email)->subject("Transient Booking Details - Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Mail sent to ".$data->user->email);
            }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
       }elseif($type == 'citation'){
            $this->log->info("Citation Email Queue started id: ".$id);            
            $data = TicketCitation::with('facility','transaction')->where('id', $id)->first();
            $user = User::find($data->user_id);

            $data->grand_total = $data->transaction->total;#DD PIMS-12423

            $facilityBrandSetting = FacilityBrandSetting::where("facility_id", $data->facility_id)->first();
            $facilityBrandSettingFlag = 1;
            if(!$facilityBrandSetting){
                $facilityBrandSettingFlag = 0;
                $facilityBrandSetting = BrandSetting::where("user_id", $data->partner_id)->first();
            }
            $facilityName =  ucwords($data->facility->full_name);
            $accountSid = env('TWILIO_ACCOUNT_SID');
            $authToken  = env('TWILIO_AUTH_TOKEN');
            $url = env('TOUCHLESS_APP_URL');
            $client = new Client($accountSid, $authToken);
            /*$encrypt = Crypt::encrypt(json_encode(['gate'=>'1', 'facility_id'=> $result->facility_id]));*/
            try
            {
                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                // the number you'd like to send the message to
                    $user->phone,
               array(
                     // A Twilio phone number you purchased at twilio.com/console
                     'from' => env('TWILIO_PHONE'),
                     // the body of the text message you'd like to send
                     //'body' => "Fine"
                     /*'body' => "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number. Use the following link to Check-Out. $url/touchless-parking-atlanta-prepaid-checkout-details/$ticket_number"*/
                     'body' => "Thank you for the payment against citation #$data->citation_number."
                 )
             );
            $this->log->info("SMS sent to ".$user->phone);
            }catch (RestException $e)
            {
                
            }

            //user mail
            if($user->email != ''){
                Mail::send(
                    "autogate-checkin-checkout.citation-email-payment", ['data' => $data, 'brand_setting'=> $facilityBrandSetting, 'facilityBrandSettingFlag'=> $facilityBrandSettingFlag ,'user' => $user], function ($message) use($data, $user) {
                        $message->to($user->email)->subject("Citation Payment Details #". $data->citation_number);
                        $message->from(config('parkengage.default_sender_email'));
                    }
                );
                $this->log->info("Citation Payment Mail sent : ".$user->email);
            }
       }else{
        $this->log->info("Autopay overstay Email Queue started overstay id: ".$id);            
            $data = OverstayTicket::with('facility')->where('id', $id)->first();
            $ticket = Ticket::where('ticket_number', $data->ticket_number)->first();


            $facilityName =  ucwords($ticket->facility->full_name);
            $accountSid = env('TWILIO_ACCOUNT_SID');
            $authToken  = env('TWILIO_AUTH_TOKEN');
            $url = env('TOUCHLESS_APP_URL');
            $client = new Client($accountSid, $authToken);
            /*$encrypt = Crypt::encrypt(json_encode(['gate'=>'1', 'facility_id'=> $result->facility_id]));*/
            try
            {
                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                // the number you'd like to send the message to
                    $ticket->user->phone,
               array(
                     // A Twilio phone number you purchased at twilio.com/console
                     'from' => env('TWILIO_PHONE'),
                     // the body of the text message you'd like to send
                     //'body' => "Fine"
                     /*'body' => "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number. Use the following link to Check-Out. $url/touchless-parking-atlanta-prepaid-checkout-details/$ticket_number"*/
                     'body' => "Thank you for the payment against overstay booking #$ticket->ticket_number."
                 )
             );
            $this->log->info("SMS sent to ".$ticket->user->phone);
            }catch (RestException $e)
            {
                
            }

            //user mail
            Mail::send(
                "autogate-checkin-checkout.autopay-overstay-email-payment", ['data' => $data,'ticket' => $ticket], function ($message) use($data) {
                    $message->to($data->user->email)->subject("Autopay Overstay Details Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Overstay Payment Mail done : ".$data->user->email);
       }

       
       }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
       
       
    }
    
}

