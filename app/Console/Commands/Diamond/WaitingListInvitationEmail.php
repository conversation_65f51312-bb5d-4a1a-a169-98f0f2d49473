<?php

namespace App\Console\Commands\Diamond;

use Mail;

use Illuminate\Console\Command;
use App\Models\User;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use View;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\WaitingList;
use App\Models\ParkEngage\BrandSetting;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitVehicle;
use App\Models\Facility;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Http\Helpers\MailHelper;


/**
 * Emails reservation stub to user
 */
class WaitingListInvitationEmail extends Command
{
  /**
   * The name and signature of the console command.
   *
   * @var string
   */
  protected $signature = 'diamondwaitinglistinvitation:email {waiting_request_id}';


  /**
   * The console command description.
   *
   * @var string
   */
  protected $description = 'Email success to waiting list user.';


  public function __construct(LoggerFactory $logFactory)
  {
    parent::__construct();
    $this->log = (new LoggerFactory)->setPath('logs/diamond/waitinglistuser')->createLogger('diamond');
  }

  /**
   * Execute the console command.
   *
   * @return mixed
   */
  public function handle()
  {
    $request_id = $this->argument('waiting_request_id');
    //$type_id = $this->argument('type_id');
    $waitlistRequest = WaitingList::with(['facility', 'user'])->where('id', $request_id)->first();
    $facility = Facility::where('id', $waitlistRequest->facility_id)->first();
    if (!$waitlistRequest) {
      // throw new NotFoundException('No user with that ID.');
      $this->log->info("No user with that ID.");
      return 1;
    }

    // get Partner Slug
    $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($waitlistRequest) {
      $join->on('user_facilities.user_id', '=', 'users.id');
      $join->where('user_facilities.facility_id', "=", $waitlistRequest->facility_id);
    })->where('created_by', $waitlistRequest->partner_id)->whereNotNull('slug')->where('user_type', 12)->first();

    if ($getRM) {
      $partnerSlug = $getRM->slug;
    } else {
      $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $waitlistRequest->partner_id)->first();
      $partnerSlug  =  $checkPaymentUrl->touchless_payment_url;
    }

    if ($waitlistRequest->facility_id == config('parkengage.WAILUKU_FACILITY')) {
    } else {
      $brand_setting = BrandSetting::where('user_id', $waitlistRequest->partner_id)->first();
    }
    $partner_name = "Parking Services"; #pims-12258 dd
    $partnerDetails = User::where('id', $waitlistRequest->partner_id)->first();
    if ($waitlistRequest->partner_id == config('parkengage.PARTNER_INTRAPARK')) {
      $view = 'intrapark.are-waiting-list';
      $url = env('WEB_URL_CUSTOMERPORTAL') . $partnerSlug;
    } else if ($waitlistRequest->partner_id == config('parkengage.PARTNER_UNITED')) {
      $view = 'united.are-waiting-list';
      $url = env('WEB_URL_CUSTOMERPORTAL') . $partnerSlug;
    } else if ($waitlistRequest->facility_id == config('parkengage.WAILUKU_FACILITY')) {
      $view = 'are.are-waiting-list';
      $url = env('WEB_URL_WAILUKU');
    } else if ($waitlistRequest->partner_id == config('parkengage.PARTNER_USM')) {
      $view = 'usm.are-waiting-list';
      $url = env('WEB_URL_CUSTOMERPORTAL') . $partnerSlug;
      $partner_name = "USM Parking";
    } else if ($waitlistRequest->partner_id == config('parkengage.PARTNER_MANLO')) {   #pims-12258 dd
      $view = 'usm.are-waiting-list';
      $url = env('WEB_URL_CUSTOMERPORTAL') . $partnerSlug;
      $partner_name = "MENLO Parking";
    } else if ($waitlistRequest->partner_id == config('parkengage.PARTNER_PCI')) {
      $view = 'yenkee.are-waiting-list';
      $url = env('WEB_URL_CUSTOMERPORTAL_PCI') . $partnerSlug;
    } else {
      $view = 'yenkee.are-waiting-list';
      $url = env('WEB_URL_CUSTOMERPORTAL') . $partnerSlug;
    }

    if ($waitlistRequest->partner_id == config('parkengage.PARTNER_PAVE')) {
      $view = 'pave-permit.are-waiting-list';
      $url = env('WEB_URL_CUSTOMERPORTAL') . $partnerSlug;
    }

    $view_text = $view . '-plain-text';
    if (!View::exists($view_text)) {
      $view_text = $view;
    }

    try {
      $this->log->info("Waiting List User Email Sending: $waitlistRequest->email");
      MailHelper::sendEmail($waitlistRequest->email, $view_text, ['subject' => "Your Waiting List has been confirmed", 'data' => $waitlistRequest, 'brand_setting' => $brand_setting, 'partner_details' => $partnerDetails, 'partner_name' => $partner_name], $waitlistRequest->partner_id);
      //  Mail::send(
      //     ['text'=>$view_text,'html'=>$view],
      //     ['data' => $waitlistRequest, 'brand_setting' => $brand_setting,'facility' => $facility,'url' => $url,'partner_details' => $partnerDetails,'partner_name'=>$partner_name],
      //     function ($message) use ($waitlistRequest) {
      //         $message->to($waitlistRequest->email)->subject("Your Waiting List has been confirmed");
      //         $message->from(config('parkengage.default_sender_email'));
      //     }
      //  );             
      $this->log->info("email sent: $waitlistRequest->email");
    } catch (\Exception $e) {
      $errorMessage = array();
      $errorMessage['message'] = $e;
      $this->log->error('Issue in email sending:', $errorMessage);
    }
  }
}
