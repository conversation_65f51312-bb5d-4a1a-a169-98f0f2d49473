<?php

namespace App\Console\Commands\Diamond;

use Mail;
use Exception;
use File;
use Storage;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use Illuminate\Support\Collection;
use Illuminate\Console\Command;
use App\Services\LoggerFactory;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\User;
use App\Models\ParkEngage\TicketCitation;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\BrandSetting;
use App\Services\Image;

class MarkCitation extends Command
{

    protected $signature = 'email:mark-citation {id} {type}';

    protected $description = 'Send mark citation email.';

    protected $log;
        
	public function __construct(LoggerFactory $logFactory)
    {
        parent::__construct();
        $this->log = $logFactory->setPath('logs/mark-citation')->createLogger('mark-citation');
    }

    public function handle()
    {
        try{
       $id = $this->argument('id');
       $type = $this->argument('type');
       //$this->log->info("start".$type);
       $this->log->info("start");

       if($type == 'citation'){
            $this->log->info("Mail start");
            $data = TicketCitation::with(['facility','appeal','user'])->where('id', $id)->first();
            $brand_setting = BrandSetting::where('user_id', $data->partner_id)->first();

            //user mail
            try{

            $citation_number = $data->citation_number;
            $facilityName =  ucwords($data->facility->full_name);
            $accountSid = env('TWILIO_ACCOUNT_SID');
            $authToken  = env('TWILIO_AUTH_TOKEN');
            $url = config('parkengage.TOUCHLESS_APP_CITATION_URL');
            $urlAdmin = env('WEB_ADMIN_URL');
            $client = new Client($accountSid, $authToken);

            /*$encrypt = Crypt::encrypt(json_encode(['gate'=>'1', 'facility_id'=> $result->facility_id]));*/
            try
            {
                // Use the client to do fun stuff like send text messages!
				$partnerDetails = UserPaymentGatewayDetail::where('user_id', $data->partner_id)->first();
                $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'extend';
                $data->url = $url.$name.'/autogate-citation-payment-details/'.$citation_number;

                if(isset($data->user->phone)){
                    $msg = "You have received a Parking Citation: ".$data->citation_number." on ".date("m/d/Y", strtotime($data->checkin_time))." at ".date("g:i A", strtotime($data->checkin_time)). " for vehicle ". $data->license_plate." at ".$facilityName.". Please visit ".$data->url." for details and payment options.";
                    $client->messages->create(
                        // the number you'd like to send the message to
                        $data->user->phone,
                        array(
                        // A Twilio phone number you purchased at twilio.com/console
                        'from' => env('TWILIO_PHONE'),
                        'body' => $msg
                        )
                    );
                    $this->log->info("SMS sent to ".$data->user->phone);	    
                }
                
            }catch (RestException $e)
            {
                
            }
            if(isset($data->user->email)){
                Mail::send(
                    "diamond.mark-citation", ['data' => $data, 'brand_setting' => $brand_setting], function ($message) use($data) {
                        $message->to($data->user->email)->subject("Citation created successfully : ". $data->citation_number);
                        $message->from(config('parkengage.default_sender_email'));
                    }
                );
                $this->log->info("Mail sent to ".$data->user->email);
            }    
            
            }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
       }else{
        $this->log->info("Autopay overstay Email Queue started overstay id: ".$id);            
            $data = OverstayTicket::with('facility')->where('id', $id)->first();
            $ticket = Ticket::where('ticket_number', $data->ticket_number)->first();


            $facilityName =  ucwords($ticket->facility->full_name);
            $accountSid = env('TWILIO_ACCOUNT_SID');
            $authToken  = env('TWILIO_AUTH_TOKEN');
            $url = env('TOUCHLESS_APP_URL');
            $client = new Client($accountSid, $authToken);
            /*$encrypt = Crypt::encrypt(json_encode(['gate'=>'1', 'facility_id'=> $result->facility_id]));*/
            try
            {
                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                // the number you'd like to send the message to
                    $ticket->user->phone,
               array(
                     // A Twilio phone number you purchased at twilio.com/console
                     'from' => env('TWILIO_PHONE'),
                     // the body of the text message you'd like to send
                     //'body' => "Fine"
                     /*'body' => "Thank you for Check-In with $facilityName. Your ticket number is $data->ticket_number. Use the following link to Check-Out. $url/touchless-parking-atlanta-prepaid-checkout-details/$ticket_number"*/
                     'body' => "Thank you for the payment against overstay booking #$ticket->ticket_number."
                 )
             );
            $this->log->info("SMS sent to ".$ticket->user->phone);
            }catch (RestException $e)
            {
                
            }

            //user mail
            Mail::send(
                "pave.autopay-overstay-email-payment", ['data' => $data,'ticket' => $ticket], function ($message) use($data) {
                    $message->to($data->user->email)->subject("Autopay Overstay Details Ticket : ". $data->ticket_number);
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            $this->log->info("Overstay Payment Mail done : ".$data->user->email);
       }

       
       }catch(Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error($msg);
            $this->log->info("Queue ended");            
        }
       
       
    }
}

