<?php

namespace App\Jobs\Entrata;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use App\Classes\CommonFunctions;
use App\Services\LoggerFactory;
use App\Models\PermitThirdParkchip;
use App\Models\Facility;
use App\Models\PermitVehicle;
use App\Models\PermitRequest;
use App\Models\PermitVehicleMapping;
use App\Models\User;
use App\Models\PermitRate;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Http\Helpers\QueryBuilder;
use App\Models\ThirdPartyIntegrationsMapping;

class EntrataThridPartyData implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $log;

    public function __construct()
    {
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/thridparty/entrata')->createLogger('entrata-api');
    }

    public function handle()
    {
        try {
            // Step 1: Get Authorization Token
            $authUrl = config('parkchrip.entrata_url') . "/" . config('parkchrip.entrata_subdomain');
            $authHeaders = ['Content-Type: application/json', 'accept: application/json', 'X-Api-Key:' . config('parkchrip.entrata_api_key')];
            $authUrlStatus = $authUrl . config('parkchrip.entrata_status_endpoint');
            $authUrlLease = $authUrl . config('parkchrip.entrata_lease_endpoint');
            $getCustUrl   = $authUrl . config('parkchrip.entrata_get_customers');
            $requestBody  = [];
            $startTime = Carbon::now()->startOfMonth()->format('m/d/Y');
            $endTime = Carbon::now()->format('m/d/Y'); // Current date in UTC format
            $requestBody['auth']['type'] = "apikey";
            $requestBody['requestId'] = '15';
            $requestBody['method']['name'] = "getLeases";
            $requestBody['method']['version'] = "r2";
            $requestBody['method']['params']['propertyId'] = config('parkchrip.entrata_property_id');

            $requestBody['method']['params']['moveInDateFrom'] = $startTime;
            $requestBody['method']['params']['moveInDateTo'] = $endTime;
            $authResponse = CommonFunctions::makeRequest($authUrlLease, 'POST', $authHeaders, $requestBody);
            //dd($authUrlStatus, 'POST', $authHeaders, $requestBody, $authResponse);
            // Check if the token exists in the response
            if (!isset($authResponse)) {
                $this->log->error('Failed to retrieve response.', ['response' => $authResponse]);
                throw new \Exception("Failed to retrieve entrata Data.");
            }

            try {
                //dd(json_encode($authResponse['response']['result']['leases'], true));
                foreach ($authResponse['response']['result']['leases']['lease'] as $key => $data) {
                    //dd($data);
                    $d = json_decode(json_encode($data)); // Convert array to JSON and back to object
                    //dd($d, $d->leaseIntervals->leaseInterval[0]->startDate, $d->leaseIntervals->leaseInterval[0]->endDate, $d->leaseIntervals->leaseInterval[0]->applicationCompletedOn);

                    // Ensure required keys exist before accessing
                    if (!isset($d->propertyId)) {
                        $this->log->error("Missing essential subscription data. Skipping...");
                        continue;
                    }
                    $requestBodyCust['auth']['type'] = "apikey";
                    $requestBodyCust['requestId'] = '15';
                    $requestBodyCust['method']['name'] = "getCustomers";
                    $requestBodyCust['method']['params']['propertyId'] = $d->propertyId;
                    $requestBodyCust['method']['params']['customerIds'] = $d->customers->customer[0]->id;
                    //$requestBodyCust['method']['params']['customerIds'] = 18304490;

                    $authResponseCust = CommonFunctions::makeRequest($getCustUrl, 'POST', $authHeaders, $requestBodyCust);
                    //dd($requestBodyCust, $authResponseCust,$d);
                    $vechileData = json_decode(json_encode($authResponseCust));
                    $vehicles = [];
                    //dd($vechileData->response->result->Customers->Customer[0]->Vehicles,$vechileData);
                    //dd('stop');
                    if ($vechileData->response->result->Customers->Customer[0]->Vehicles) {
                        $vehicles = $vechileData->response->result->Customers->Customer[0]->Vehicles->Vehicle;
                    }
                    //dd($d);
                    $this->log->info('start1');
                    $facilityId = $d->propertyId;
                    $parkerId = $d->customers->customer[0]->id;
                    $parkerFullName =  strtolower($d->customers->customer[0]->firstName . "-" . $d->customers->customer[0]->lastName);
                    $parkerPhone =  $d->customers->customer[0]->phones->phone[0]->phoneNumber;
                    // $this->log->info('start');

                    // Check if record already exists
                    $existingRecord = PermitThirdParkchip::where('facilityId', $facilityId)
                        ->where('parkerId', $parkerId)
                        ->first();
                    //dd($existingRecord);
                    // $this->log->info("existing record :". $existingRecord);
                    $email = $d->customers->customer[0] && $d->customers->customer[0]->emailAddress
                        ? $d->customers->customer[0]->emailAddress
                        : strtolower(str_replace(' ', '-', $parkerFullName)) . '<EMAIL>';

                    $startDate  = date("Y-m-d", strtotime($d->leaseIntervals->leaseInterval[0]->startDate));
                    $endDate    = date("Y-m-d", strtotime($d->leaseIntervals->leaseInterval[0]->endDate));
                    $createDate = date("Y-m-d H:i:s", strtotime($d->leaseIntervals->leaseInterval[0]->applicationCompletedOn));
                    $leaseID    = $d->leaseIntervals->leaseInterval[0]->id;
                    // dd($facilityId, $parkerId, $parkerFullName, $email, $startDate,$endDate,$createDate);
                    // If exists, update instead of skipping
                    $this->log->info('Facility ID:' . $facilityId);
                    $this->log->info('Parker ID:' . $parkerId);
                    $this->log->info('Parker First Name:' . $d->customers->customer[0]->firstName);

                    if ($existingRecord) {

                        if ($vehicles != '') {
                            $oldVehicles = json_decode($existingRecord->vehicles, true) ?: [];
                            $newVehicles = isset($vehicles) ? json_decode(json_encode($vehicles), true) : [];

                            //dd($oldVehicles,$newVehicles);	
                            $mergedVehicles = array_merge($oldVehicles, $newVehicles);
                            $uniqueVehicles = array_values(array_unique($mergedVehicles, SORT_REGULAR));
                            //dd($mergedVehicles,$uniqueVehicles,json_encode($uniqueVehicles));
                            $existingRecord->vehicles = json_encode($uniqueVehicles);
                        }
                        // $this->log->info("Updating existing record for FacilityID: $facilityId, AccountID: $accountId, SubscriptionID: $subscriptionId");

                        //dd($oldVehicles, $newVehicles);

                        //dd($existingRecord->vehicles);	
                        //$existingRecord->sublotId = $d->sublotId ?? null;
                        $existingRecord->parkerId = $parkerId ?? null;
                        $existingRecord->parkerFirstName = $d->customers->customer[0]->firstName ?? null;
                        $existingRecord->parkerLastName = $d->customers->customer[0]->lastName ?? null;
                        $existingRecord->parkerFullName = ($d->customers->customer[0]->firstName ?? '') . ' ' . ($d->customers->customer[0]->lastName ?? '');
                        $existingRecord->parkerEmail =  $email;
                        $existingRecord->parkerPhone =  $parkerPhone ?? null;
                        $existingRecord->createdAt = $createDate ?? now();
                        $existingRecord->startDate = $startDate ?? null;
                        $existingRecord->endDate = $endDate ?? null;
                        $existingRecord->partner_id = config('parkengage.PARTNER_CITYPARKING');
                        $existingRecord->save(); // Update record
                        $this->log->info("vechiles :" . $existingRecord->vehicles);
                        // Insert new vehicles
                        if (!empty($existingRecord->vehicles)) {
                            $vehicles = json_decode($existingRecord->vehicles);
                            foreach ($vehicles as $v) {
                                $licensePlate = $v->LicensePlateNumber ?? null;
                                $vehicleType = null;
                                $vehiclemake = $v->Make ?? null;
                                $vehiclemodel = $v->Model ?? null;
                                $deletedAt    = null;

                                if ($licensePlate) {
                                    // Check if this vehicle already exists related to the permit record
                                    $exists = $existingRecord->vehicleList()
                                        ->where('vehicleNumber', $licensePlate)
                                        ->exists();

                                    if (!$exists) {
                                        // Create only if not exists
                                        $existingRecord->vehicleList()->create([
                                            'vehicleNumber' => $licensePlate,
                                            'state' => $vehicleType,
                                            'make' => $vehiclemake,
                                            'model' => $vehiclemodel,
                                            'deleted_at' => $deletedAt,
                                            'permit_third_parkchip_id' => $existingRecord->id,
                                        ]);

                                        $this->log->info("Created vehicle with licensePlateNumber: $licensePlate");
                                    } else {
                                        $this->log->info("Vehicle with licensePlateNumber: $licensePlate already exists, skipping.");
                                    }
                                }
                            }
                        }
                    } else {
                        //dd($vehicles);
                        // Insert new record
                        $permit = PermitThirdParkchip::create([
                            'facilityId' => $facilityId,
                            'parkerId' => $parkerId ?? null,
                            'parkerFirstName' => $d->customers->customer[0]->firstName ?? null,
                            'parkerLastName' => $d->customers->customer[0]->lastName ?? null,
                            'parkerFullName' => ($d->customers->customer[0]->firstName ?? '') . ' ' . ($d->customers->customer[0]->lastName ?? ''),
                            'parkerEmail' => $email,
                            'parkerPhone' => $parkerPhone ?? null,
                            'createdAt' => $createDate ?? now(),
                            'startDate' => $startDate ?? null,
                            'endDate' => $endDate ?? null,
                            'accountId' => $leaseID,
                            'vehicles' => json_encode($vehicles ?? []),
                            'partner_id' => config('parkengage.PARTNER_CITYPARKING'),
                        ]);

                        if (!empty($vehicles)) {
                            $vehicles = ($vehicles); // Convert JSON string to array of objects

                            foreach ($vehicles as $v) {
                                $licensePlate = $v->LicensePlateNumber ?? null;
                                $vehicleType = null;
                                $vehiclemake = $v->Make ?? null;
                                $vehiclemodel = $v->Model ?? null;
                                $deletedAt    = null;

                                $permit->vehicleList()->create([
                                    'vehicleNumber' => $licensePlate,
                                    'state' => $vehicleType,
                                    'make' => $vehiclemake,
                                    'model' => $vehiclemodel,
                                    'deleted_at' => $deletedAt,
                                    'permit_third_parkchip_id' => $permit->id,
                                ]);
                            }
                        }
                    }
                    if ($key == 2) {
                        break;
                    }
                }
                //  DB::commit();  // Commit the transaction if all inserts are successful
                //dd('stop1');
                DB::beginTransaction();
                // $this->log->info('start4');
                $data = PermitThirdParkchip::with('vehicleList')->where('partner_id', config('parkengage.PARTNER_CITYPARKING'))->get();
                $countryCode = QueryBuilder::appendCountryCode();
                // $this->log->info('countrycode'.$countryCode);
                //dd(count($data));
                try {
                    foreach ($data as $k => $d) {
                        $checkfacility = ThirdPartyIntegrationsMapping::where('third_party_id', $d->facilityId)->first();
                        //dd($checkfacility);
                        if (!empty($checkfacility)) {
                            $facility     = Facility::where('id', $checkfacility->facility_id)->first();
                        }
                        //dd($facility);	
                        if (empty($facility)) {
                            $this->log->info('Facility is not exist.');
                        }
                        // Check if vehicles are provided
                        if (empty($d->vehicles)) {
                            $d->failed_type = "Empty";
                            $d->failed_description = "Vehicle Object is Empty";
                            $d->save();
                            // Log the situation when no vehicles are provided
                            // $this->log->info("No vehicles provided, skipping PermitRequest creation: " . json_encode($d));
                            continue; // Skip this iteration if no vehicles
                        }
                        //dd(count($d->vehicles));
                        $vehicles = ($d->vehicles);

                        $vechilesdata = [];
                        //dd(count(json_decode($vehicles)));
                        // $this->log->info('vehicles: '.json_encode($vehicles));

                        $vehicles = json_decode($vehicles, true);
                        //dd($vehicles,count($vehicles));
                        if (count($vehicles) > 0) {
                            foreach ($vehicles as $vech) { //dd($vech['LicensePlateNumber'], $vech['Make'], $vech['Model']);
                                if (empty($vech['LicensePlateNumber'])) {
                                    $d->failed_type = "Empty";
                                    $d->failed_description = "Vehicle License Plate is Empty";
                                    $d->save();
                                    continue;
                                }
                                $existingVehicle = PermitVehicle::where('license_plate_number', $vech['LicensePlateNumber'])
                                    ->whereNULL('deleted_at')
                                    ->first();
                                if ($existingVehicle) {
                                    $existingMapping = PermitVehicleMapping::where('permit_vehicle_id', $existingVehicle->permit_vehicle_id)
                                        ->first();
                                    if ($existingMapping) {
                                        $existdata = PermitRequest::where('partner_id', $facility->owner_id)->where('partner_id', $existingMapping->permit_request_id)
                                            ->wherenull('deleted_at')->wherenull('cancelled_at')->where('status', '1')->first();
                                        if ($existdata) {
                                            $d->failed_type = "Duplicate";
                                            $d->failed_description = "Vehicle License Plate is Already Exists & Mapped with Permit";
                                            $d->save();
                                            continue;
                                        }
                                    }
                                }
                                // $this->log->info('vehiclesinfo: '.json_encode($veh));
                                $vehicleFilter = PermitRequest::licensePlateFilter($vech['LicensePlateNumber'], $vech['Make'], $vech['Model']);
                                // $this->log->info('vehicleFilter: '.$vevehicleFilter);
                                //dd($vech);
                                if (!$vehicleFilter) {
                                    $vechilesdata[] = $vech;
                                }
                                //dd($vechilesdata,$vech);
                            }
                        } else {
                            //dd('i m here');
                        }

                        //dd($vechilesdata);
                        // $this->log->info('vechilesdata: '.json_encode($vechilesdata));

                        $email = $d->parkerEmail ? $d->parkerEmail : strtolower(str_replace(' ', '-', $d->parkerFullName)) . '<EMAIL>';
                        //dd($email);
                        if (!isset($facility->id)) {
                            continue;
                        }

                        //$existUser = User::where('email', $email)->where('thirdparty_userid', $d->parkerId)->first();

                        $existUser = User::where('email', $email)->where('created_by', $facility->owner_id)->whereNull('deleted_at')->first();
                        //dd($email);
                        // $this->log->info('User exist: '.$existUser);
                        if (isset($existUser->id)) {
                            $email = $existUser->email;
                        }
                        $facilityid = $facility->id;

                        $existdata = null;
                        $existdata = PermitRequest::where('partner_id', $facility->owner_id)->where('email', $email)
                            ->wherenull('deleted_at')
                            ->first();
                        //dd($existdata);

                        $ratedata   = PermitRate::where('facility_id', $facilityid)->where('is_thirdparty', '1')->first();
                        //dd($facilityid,$ratedata);	
                        //  $this->log->info($d->parkerId);
                        $postdata = [
                            'first_name' => $d->parkerFirstName,
                            'last_name' => $d->parkerLastName,
                            'email' => $email,
                            'phone' => $d->parkerPhone,
                            'facility_id' => $facility->id,
                            'vehicleList' => $vechilesdata, // Start with the current vehicle
                            'address1' => '',
                            'address2' => '',
                            'city' => '',
                            'state' => '',
                            'zipcode' => '',
                            'permit_rate_id' => isset($ratedata->id) ? $ratedata->id : null,
                            "permit_rate" => isset($ratedata->rate) ? $ratedata->rate : 0,
                            "permit_type_name" => isset($ratedata->name) ? $ratedata->name : null,
                            "type_id" => isset($type_id) ? $type_id : null,
                            'desired_start_date' =>  date('Y-m-d', strtotime($d->startDate)),
                            'desired_end_date' =>  date('Y-m-d', strtotime($d->endDate)),
                            'permit_final_amount' => isset($ratedata->rate) ? $ratedata->rate : 0,
                            'partner_id' => $facility->owner_id,
                            'name' => $d->parkerFullName,
                            "third_party_type" => 1,
                            'thirdparty_userid' => $d->parkerId,
                            'third_party_subscription_id' => $d->subscriptionId,
                            'third_party_account_id' => $d->accountId,
                            'account_number' => rand(100, 999) . rand(100, 999) . rand(10, 99),
                        ];
                        //dd($postdata);
                        if (empty($postdata['vehicleList'])) {
                            $d->failed_type = "Empty";
                            $d->failed_description = "Vehicle License Plate is Empty";
                            $d->save();
                            continue;
                        }

                        $postdata['vehicleList'] = json_encode($postdata['vehicleList']);
                        $partner_id = $facility->owner_id;
                        //dd($postdata);
                        // $this->log->info("post data: " . json_encode($postdata));
                        if (!isset($existUser->id)) {
                            $existUser = '';
                            $postdata['confirm_password'] = $d->parkerFullName . date('md');
                        }
                        $request = (object) $postdata;


                        $user = User::fetchOrCreateUserThirdParty($request, $partner_id, $d->parkerFullName, false, $existUser, $countryCode);
                        // $this->log->info("User Details:" . json_encode($user));
                        if (isset($user->id)) {
                            $userId = $user->id;
                        } else {
                            // $this->log->info('User not exist. Please check again for email: '.$d->parkerEmail); 
                            continue;
                        }

                        $status = 0;


                        $licenseMaxCheck = PermitRequest::licenseMaxCheck($facility, $user, $request, $partner_id);
                        //dd($licenseMaxCheck);
                        if (!$licenseMaxCheck) {
                            //dd($user->id);
                            //dd($facility, $user, $request, $partner_id, $status, $existdata);
                            $permitRes = PermitRequest::savePermitRequestThirdParty($facility, $user, $request, $partner_id, $status, $existdata);
                            // $this->log->info('permitcreatd '.$permitRes);
                            //dd($permitRes, $user, $request, $partner_id,$existdata);
                            $vechilemap = PermitRequest::saveOrUpdatePermitVehicle($permitRes, $user, $request, $partner_id);
                            //dd($vechilemap);
                            // $this->log->info('vechilemap '.$vechilemap);
                        } else {
                            // $this->log->info($licenseMaxCheck); 
                            continue;
                        }
                    }

                    DB::commit(); // Commit the transaction
                    return response()->json([
                        'status' => 'success',
                        'message' => 'Transaction data saved successfully.'
                    ], 200); // Success response

                } catch (\Exception $e) {
                    //dd($e->getMessage());
                    $this->log->error("Error saving transaction data: " . $e->getMessage() . 'line no ' . $e->getLine());
                    DB::rollBack(); // Rollback the transaction if any error occurs
                    // Return an error response to the API client
                    return response()->json([
                        'status' => 'error',
                        'message' => 'An error occurred while saving transaction data.',
                        'error' => $e->getMessage()
                    ], 500); // Internal server error response
                }
            } catch (\Exception $e) {
                // DB::rollBack();  // Rollback the transaction if any error occurs
                $this->log->error('Error saving transaction data1: ' . $e->getMessage() . 'line no ' . $e->getLine());
                return 'error';  // Return error message
            }
        } catch (\Exception $e) {
            $this->log->error('Error in ParkchirpThirdPartyData Job.', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),  // Full stack trace for deeper insight
            ]);
        }
    }
    /*
    //user create data
    private function usercreate($id, $fullname, $email)
    {
        // Check if user with the same parker_id exists
        $user = User::where('thirdparty_userid', $id)->first();

        if (!$user) {
            if (!empty($email)) {
                // Check if another user exists with the same first & last name but a different parker_id
                $user = User::create([
                    'thirdparty_userid'  => $id,
                    'name' => $fullname,
                    'email'      => $email,
                ]);
            } else {
                // Check if another user exists with the same first & last name but a different parker_id
                $existingUser = User::where('name', $fullname)->first();

                if ($existingUser) {
                    // Generate a unique email based on first_name, last_name, and a random string
                    $email = strtolower($fullname) . rand(1000, 9999) . '@yopmail.com';
                } else {
                    // Use provided email or generate a default one
                    $email = strtolower($fullname) . '@yopmail.com';
                }

                // Create a new user
                $user = User::create([
                    'thirdparty_userid'  => $id,
                    'name' => $fullname,
                    'email'      => $email,
                ]);
            }

            return $user;
        }

        return $user;
    }
    */
}
