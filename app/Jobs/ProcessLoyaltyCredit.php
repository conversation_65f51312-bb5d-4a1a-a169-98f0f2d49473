<?php

namespace App\Jobs;

use Exception;
use App\Exceptions\ApiGenericException;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Storage;
use App\Models\LoyaltyUserAccounts;
use App\Models\Reservation;
use App\Classes\LoyaltyProgram;

/**
 * Checks if an account is active and then credit loyalty points.
 * Steps:
 *
 * @package App\Jobs
 */
class ProcessLoyaltyCredit extends CronJob implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    const MAIL_QUEUE = 'iqp-loyalty';
    const LOG_DIRECTORY = 'logs/loyalty';

    protected $reservationId;
    protected $userId;
    protected $amount;
    protected $account;
   

    protected $successLog;
    protected $errorLog;

    public function __construct($userId, $reservationId, $amount)
    {  
        $this->userId = $userId;
        $this->reservationId = $reservationId;
        $this->amount = $amount;
      
        $this->account = LoyaltyUserAccounts::where('user_id', $userId)->orderBy('id', 'DESC')->first();
          
  
    }

    public function handle()
    {
        try { 
            $this->createLoggers();
            $this->successLog->info("{ $this->reservationId} Job started..");
            // loyalty account validations
            if(!$this->account) {
           
              throw new NotFoundException('No account found associated with this user.');
            } 
           $this->computePoints();
       
            $this->successLog->info("Reached data");
       } catch (Exception $e) {
           $this->errorLog->error($e->getMessage());
           throw $e;
        
        }
    }

    public function computePoints() 
    {   
        $this->successLog->info("{ $this->reservationId} computing points..");
        $dataSet = array(
            "sva" => $this->account["account_no"],
            "amount" => $this->amount*100,
            "tax" => 0,
            "tip" => 0,
            "lineItem" => array(
                array(
                    "orderLineNumber" => "1",
                    "amount" => $this->amount*100,
                    "currency" => "USD",
                    "sku" => "ExpectedPoints",
                    "description" => "Amount credited for reservation :".$this->reservationId,
                    "quantity" => "1"
                )
            ),
            "payment" => array(
                array(
                    "type" => "CREDIT",
                    "orderLineNumber" => "1",
                    "amount" => $this->amount*100,
                    "currency" => "USD"
                )
            )
        );
        

        $transactionData = array(
            'account_id' => $this->account["id"],
            'reservation_id' => $this->reservationId
        );
        $this->successLog->info("Loyalty computing api call for account no:  {$this->account["account_no"]} and reservation: {$this->reservationId}");

        $response = LoyaltyProgram::computePoints($dataSet, $this->userId, $transactionData);
        $reservation = Reservation::find($this->reservationId);

        if($response['success']) {

            $reservation->loyalty_program = LoyaltyProgram::LP_AWAILED;
            $reservation->save();
            
            $this->successLog->info("Successful loyalty points addedd for:  {$response['request_no']}");  
            return $response['data']['status.description'];
           
        } else {

            $reservation->loyalty_program = LoyaltyProgram::LP_REJECTED;
            $reservation->save();
            $msg = isset($response['data']['status.description']) ? $response['data']['status.description'] : 'Could not compute points';
            $rspMessage = isset($response['error']) ? $response['error'] : 'Error from api';
            $this->errorLog->error("{$this->reservationId} : ".$rspMessage);
            $this->errorLog->error("Error during compute api {$response['request_no']}");   
            $this->errorLog->error("{$this->reservationId} : ".$msg);
               
        }
    }

    protected function createLoggers()
    {
        $this->errorLog = $this->createLogger('loyalty-credits-error');
        $this->successLog = $this->createLogger('loyalty-credits-success');
    }
}
