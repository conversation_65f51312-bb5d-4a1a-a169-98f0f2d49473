<?php

namespace App\Jobs;


use Storage;
use Exception;

use App\Services\LoggerFactory;
use Monolog\Logger;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;

use App\Classes\LoyaltyProgram;
use App\Models\ApplePass;
use App\Models\User;
use App\Models\LoyaltyUserAccounts;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Classes\PushNotification;

class ProcessAppleWalletUpdate extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;
    const LOG_DIRECTORY = 'logs/applewalletJob';
    const PASS_IDENTITY = "pass.com.iconGO.developmentPass";
   
    protected $user;
    protected $serial_number;
    protected $loyalty_points;
    protected $userloyaltyPoints;
    protected $applePassObject;
   
    // Responses from authorize.net
    protected $responses = [];

    // Exceptions caught in the process of running transactions
    protected $exceptions = [];


    protected $successLog;
    protected $errorLog;
    
    public function __construct(ApplePass $appleWalletObj)
    {   
        $logFactory = new LoggerFactory();
        $this->loyalty_points = 0;
        $this->applePassObject = $appleWalletObj;
        $this->errorLog = $logFactory->setPath('logs/applewalletJob')->createLogger('applewalletJob_error');
        $this->successLog = $logFactory->setPath('logs/applewalletJob')->createLogger('applewalletJob_success');   
    }

    public function handle()
    {
        try {
            $account = LoyaltyUserAccounts::where('account_no', $this->applePassObject->serial_number)
					->orderBy('id', 'DESC')
					->first(); 
             if($account)
              {
                $user_id = $account->user_id;
                $this->user  = User::find($user_id); 
                $this->userloyaltyPoints = isset($account->user_total_points)?$account->user_total_points:0;
               
                $accountData = LoyaltyProgram::getUserAccounts($this->user->email, $this->user->id);
                if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) 
                {
                     $this->loyalty_points = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
                }  
                //update only if user points has been changed 
                if((double)($this->loyalty_points) != (double)($this->userloyaltyPoints))
                {
                
                    //update the users  points from katana api                    
                    $account->user_total_points = $this->loyalty_points;
                    $account->save();
                    
                    $this->applePassObject->updated_date = date('Y-m-d H:i:s');
                    $this->applePassObject->save();
                    $this->successLog->info('Record  will be process'.$this->applePassObject->serial_number);               
                    if($this->applePassObject)  
                     {
                          $this->serial_number= isset($this->applePassObject->serial_number)?$this->applePassObject->serial_number:'';
                          $this->sendPushNotificationsForApplePass(); 

                     }
                     
                }else{
                     $this->successLog->info('Record  will be skipping.'.$this->applePassObject->serial_number);      
                }
                
              }
            
        } catch (Exception $e) {
            $this->errorLog->error($e->getMessage());
            throw $e;
        }
    }
    
    public function sendPushNotificationsForApplePass()
    {
         $passData = ApplePass::where('serial_number',$this->serial_number)->first();
            if($passData)
            {
                $passesAllDevices = $passData->passesAllDevices;
                if(count($passesAllDevices)>0)
                {
                    foreach($passesAllDevices as $devices)
                    {
                            $deviceToken=isset($devices->push_token)?$devices->push_token:'';
                            $passIdentity = self::PASS_IDENTITY;
                            PushNotification::sendIphonePushAppleWallet($deviceToken, $passIdentity, $badge = 0, $check= 0);

                    }
                }
            }
    }


}