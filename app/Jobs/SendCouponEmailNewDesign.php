<?php

namespace App\Jobs;

use App\Jobs\Job;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Artisan;

class SendCouponEmailNewDesign extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    private $rateId;
    private $email;
    private $visitorCode;

    public function __construct($email, $rateId, $visitorCode)
    {
        $this->email = $email;
        $this->rateId = $rateId;
        $this->visitorCode = $visitorCode;
    }

    public function handle()
    {
        Artisan::call('coupons-gtm-new-design:email', ['email' => $this->email, 'rateId' => $this->rateId, 'visitorCode' => $this->visitorCode]);   
        // Artisan::call('coupons:email', ['email' => $this->email, 'rateId' => $this->rateId, 'visitorCode' => $this->visitorCode]);
    }
}
