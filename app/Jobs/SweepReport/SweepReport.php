<?php

namespace App\Jobs\SweepReport;

use App\Jobs\Job;
use Carbon\Carbon;
use App\Models\OauthClient;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Mail;
use App\Services\LoggerFactory;
use App\Classes\CommonFunctions;
use App\Models\Facility;
use App\Models\ParkEngage\FacilityPaymentDetail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class SweepReport extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    protected $log;
    protected $requestData;
    protected $carbon;


    // protected MAPCO_BASE_URL;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($requestData)
    {
        // parent::__construct();
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/sweep-report')->createLogger('sweep-report');
        $this->carbon = new Carbon();
        $this->requestData =  $requestData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $current_time = date('Y-m-d H:i:s');
        $this->log->info("Sweep report start at from Job" . $current_time);

        // validate data
        // if (!$this->requestData = $this->validateRequest()) {
        //     $this->log->error("Invalid Request");
        //     return false;
        // }

        // Get Accounts:
        $accounts = $this->getAccountNames();
        $this->log->info("Accounts Details: " . json_encode($accounts));

        // Get Baches
        $batchesData = $this->getBatches($accounts);
        $this->log->info("Baches Data: " . json_encode($accounts));


        // Generate Excel and Send Mail
        $filePath = $this->generateExcel($batchesData);
        $this->log->info("File Path: " . json_encode($filePath));


        $this->log->info("Sweep report from Job end");
    }

    // private function validateRequest()
    // {
    //     $requestDataRaw = $this->option('requestData');

    //     // Try to decode the JSON
    //     $requestData = json_decode($requestDataRaw, true);

    //     // Check for JSON decoding errors
    //     if (json_last_error() !== JSON_ERROR_NONE) {
    //         $this->log->warning("Invalid JSON passed to --requestData: " . json_last_error_msg());
    //         return false;
    //     }

    //     // Optional debug output:
    //     // dd($requestDataRaw, $requestData);

    //     // Ensure it's an array
    //     if (!is_array($requestData) || empty($requestData)) {
    //         $this->log->warning("Decoded requestData is empty or not an array.");
    //         return false;
    //     }

    //     // Everything is good — return the decoded array
    //     return $requestData;
    // }

    private function getAccountNames()
    {
        $this->setCustomTimezone();
        return FacilityPaymentDetail::select('heartland_website_id')
            ->whereIn('facility_id', $this->requestData['facility_ids'])->get();
    }

    private function getBatches($accounts)
    {
        $this->log->info("getBatches Start " . json_encode($accounts));

        $batchesData = [];

        $beginDate         = $this->requestData['beginDate'];
        $endDate           = $this->requestData['endDate'];
        $beginRecordOffset = $this->requestData['beginRecordOffset'];
        $limit             = $this->requestData['limit'];

        foreach ($accounts as $key => $account) {
            $url = "https://reportsapi.propay.com/v1/accounts/{$account->heartland_website_id}/transfers"
                . "?beginDate={$beginDate}&endDate={$endDate}&beginRecordOffset={$beginRecordOffset}&limit={$limit}";

            $method = "GET";
            $headers = [
                'Authorization: Basic MDZjZmZlZDg1Zjc0ZTQwOGE3YjM4NDIwZWZkMjY5OmU0MDhhN2Iz',
                'Content-Type: application/json'
            ];


            $response = CommonFunctions::makeRequest($url, $method, $headers);


            $this->log->info("Response data: " . json_encode($response) . "getBatches for " . ++$key);


            // Check if the response is valid and an array before merging
            if (is_array($response)) {
                $bachIdData = $this->getBachIdData($response, $account);
                $batchesData = array_merge($batchesData, $bachIdData);
            } else {
                $this->log->warning('No record found');
            }

            // Optional: If you want to send mail per account
            // $this->sendMail($response);
        }

        // Optional: Send combined mail
        // $this->sendMail($batchesData);
        $this->log->info("getBatches End");
        return $batchesData;
    }

    private function getBachIdData($batchesData, $account)
    {
        $this->log->info("getBachIdData Start for batchIdData: " . json_encode($batchesData) . " and Account for: " . $account->heartland_website_id);

        $tranctionsData = [];

        // dd($batchesData['transferResults']);

        $beginDate         = $this->requestData['beginDate'];
        $endDate           = $this->requestData['endDate'];
        $beginRecordOffset = $this->requestData['beginRecordOffset'];
        $limit             = $this->requestData['limit'];

        foreach ($batchesData['transferResults'] as $transfer) {

            $url = "https://reportsapi.propay.com/v1/accounts/$account->heartland_website_id/transfers/" . $transfer['batchId'] . "?beginRecordOffset=0&limit=5000";

            $method = "GET";
            $headers = [
                'Authorization: Basic MDZjZmZlZDg1Zjc0ZTQwOGE3YjM4NDIwZWZkMjY5OmU0MDhhN2Iz',
                'Content-Type: application/json'
            ];

            // $this->log->info("Batch data Url: " . $url);


            $response = CommonFunctions::makeRequest($url, $method, $headers);
            // $this->log->info("Batch data response: " . json_encode($response));


            // Check if the response is valid and an array before merging
            // dd(is_array($response));
            if (is_array($response)) {
                $tranctionsData = array_merge($tranctionsData, $response['transferDetailResults']);
            }
        }
        $this->log->info("Final Batch data response: " . json_encode($tranctionsData));

        $this->log->info("getBachIdData End");

        return $tranctionsData;
    }

    private function generateExcel($getBachIdData)
    {
        // Generate file name and path
        $fileName = 'sweep_report_' . $this->carbon->now()->format('m-d-Y');

        $grossAmount = $netAmount = 0;

        // Create Excel file and store it temporarily
        $excel = Excel::create(
            $fileName,
            function ($excel) use ($getBachIdData, &$grossAmount, &$netAmount) {
                $excel->sheet('Sweep report patner wise', function ($sheet) use ($getBachIdData, &$grossAmount, &$netAmount) {
                    $this->log->info('Excel Data :' . json_encode($getBachIdData));

                    foreach ($getBachIdData as $key => $valList) {
                        $data['Sr No.']                     = ++$key;
                        $data['gatewayTransactionId']       = $valList['gatewayTransactionId'] ?? '-';
                        $data['transactionDate']            = isset($valList['transactionDate']) ? $this->utcToNewYork($valList['transactionDate']) : '-';
                        $data['fundDate']                   = isset($valList['fundDate']) ? $this->utcToNewYork($valList['fundDate']) : '-';
                        $data['transNum']                   = $valList['transNum']  ?? '-';
                        $data['transType']                  = $valList['transType']  ?? '-';
                        $data['invNum']                     = $valList['invNum']  ?? '-';
                        $data['payerName']                  = $valList['payerName']  ?? '-';
                        $data['authorizationAmount']        = $valList['authorizationAmount']  ?? '-';
                        $data['authorizationCurrency']      = $valList['authorizationCurrency']  ?? '-';
                        $data['grossAmount']                = $valList['grossAmount']  ?? '-';
                        $data['discFee']                    = $valList['discFee']  ?? '-';
                        $data['perTransFee']                = $valList['perTransFee']  ?? '-';
                        $data['credit']                     = $valList['credit']  ?? '-';
                        $data['totalFee']                   = $valList['totalFee']  ?? '-';
                        $data['netAmount']                  = $valList['netAmount']  ?? '-';
                        $data['settlementCurrency']         = $valList['settlementCurrency']  ?? '-';
                        $data['comment1']                   = $valList['comment1']  ?? '-';
                        $data['comment2']                   = $valList['comment2']  ?? '-';

                        if (!is_null($valList['invNum'])) {
                            $grossAmount = $valList['grossAmount'];
                            $netAmount = $valList['netAmount'];
                        }

                        $dataArr[] = $data;
                    }
                    // dd($dataArr);
                    $sheet->fromArray($dataArr, [], 'A1', true, true);
                });
            }
        );

        $excel->store('xls', storage_path('exports'));

        // Build the full path
        $filePath = storage_path("exports/{$fileName}.xls");

        $this->sendMail($getBachIdData, $fileName, $filePath, $grossAmount, $netAmount);

        return "Mail Sent Successfully";
    }

    public function utcToNewYork($dateTime)
    {
        if (empty($dateTime)) {
            return null;
        }

        // Ensure the input is clean and validate the format
        $dateTime = trim($dateTime);
        return date('Y-m-d H:i:s', strtotime($dateTime));
    }

    private function sendMail($data, $fileName, $filePath, $grossAmount, $netAmount)
    {
        $this->log->info("Send mail start");

        $excelData = [
            'totalRecords' => count($data),
            'grossAmount' => $grossAmount,
            'netAmount' => $netAmount,
            'timestamp' => $this->carbon->now()->toDateTimeString()
        ];

        $recipients = config("parkengage.notify.profac");

        // dd($fileName, $filePath, $recipients, count($excelData), config('parkengage.default_sender_email'));

        $subject = "Sweep Report " . $this->carbon->parse($this->requestData->beginDate)->format('jS F, Y');

        try {
            Mail::send('sweep-report.sweep-report', $excelData, function ($message) use ($recipients, $filePath, $subject) {
                // $message->to("<EMAIL>")
                $message->to($recipients)
                    ->subject($subject)
                    ->from(config('parkengage.default_sender_email'))
                    ->attach($filePath);
            });

            $this->log->info("Notifications sent with Excel attachment: $fileName");
        } catch (\Exception $e) {
            $this->log->error("Failed to send email with Excel: {$e->getMessage()}");
        }
    }


    private function setCustomTimezone()
    {
        $facilities = Facility::whereIn('id', $this->requestData['facility_ids'])->get();
        if (count($facilities) > 0) {
            foreach ($facilities as $facility) {

                // $facility = Facility::whereIn('id', [);
                if ($facility && !empty($facility->timezone)) {
                    date_default_timezone_set($facility->timezone);
                    return;
                }

                $partnerTimezone = OauthClient::where('partner_id', $facility->owner_id)
                    ->with('userPaymentGatewayDetail')
                    ->first()
                    ->userPaymentGatewayDetail ?? null;

                if ($partnerTimezone && !empty($partnerTimezone->timezone)) {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }

    public function getBankReportData($request)
    {
        if (!in_array($request->partner_id, ['215900', '169163'])) {
            return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "No data found. Please select another partner."]], 500);
        }

        // Super Admin
        if (!isset($request->facility_id)) {
            return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "Please select Facility."]], 500);
        } elseif (isset($request->facility_id) && empty($request->facility_id)) {
            return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "Please select Facility."]], 500);
        }

        $partner_id = isset($request->partner_id) ? $request->partner_id : 215900;
        $facility_id = (isset($request->facility_id) && !empty($request->facility_id)) ? $request->facility_id : 300;


        $loggedInUserId = Auth::user()->id;
        $fromdate = isset($request->fromDate) ? date('Y-m-d', strtotime($request->fromDate))  : date('Y-m-d');
        $toDate = isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate))  : date('Y-m-d');

        $checkInTime  = date('Y-m-d', strtotime($fromdate));
        $checkOutTime    = date('Y-m-d', strtotime($toDate));


        if (!$partner_id) {
            return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "Partner detail not found."]], 500);
        }

        $usertypedata = Auth::user()->user_type;

        if ($usertypedata == 3 || $usertypedata == 1) {
            $UserFacilitiesquery = "SELECT id from facilities where facilities.owner_id = $partner_id AND facilities.id=$request->facility_id ";
            $UserFacilities = DB::select($UserFacilitiesquery);
            // print_r(count($UserFacilities));die;
            $getAllFacility = array_column($UserFacilities, 'id');
            $imploadFaclitiesData = implode(",", $getAllFacility);
        } else {
            $UserFacilitiesquery = "SELECT facility_id as id from user_facilities where user_facilities.user_id=$loggedInUserId  AND user_facilities.deleted_at is null  and user_facilities.facility_id=$request->facility_id";
            $UserFacilities = DB::select($UserFacilitiesquery);
            // print_r(count($UserFacilities));die;
            $getAllFacility = array_unique(array_column($UserFacilities, 'id'));
            $imploadFaclitiesData = implode(",", $getAllFacility);
        }

        $facilityName = "SELECT garage_code,full_name from facilities where facilities.owner_id = $partner_id AND facilities.id in ('$request->facility_id') ";
        $facilityNamersult = DB::select($facilityName);

        $brandSetting = BrandSetting::select('id', 'color')->where('user_id', $partner_id)->first();
        $color = $brandSetting->color;
        $locationId = $facilityNamersult[0]->garage_code;
        $locationName = $facilityNamersult[0]->full_name;

        $finalCodes5 = [];
        $rowKey = 0;

        if (isset($request->facility_id) && !empty($request->facility_id)) {
            $driveupDataTickets = "select *  from (
                                SELECT 
                                    -- Ticket info
                                    t.id AS ticketCount,
                                    t.discount_amount,
                                    t.ticket_number,
                                    t.length,
                                    t.total,
                                    t.grand_total,
                                    t.processing_fee,
                                    t.rate_description,
                                    t.checkin_time,
                                    t.checkout_time,
                                    t.estimated_checkout,
                                    t.tax_fee,
                                    t.promocode,
                                    t.parking_amount,
                                    t.additional_fee,
                                    t.surcharge_fee,
                                    t.paid_date,
                                    t.anet_transaction_id,
                                    u.name,

                                    -- Anet data
                                    ant.total AS totalAntAmount,
                                    ant.anet_trans_id,
                                    DATE_FORMAT(ant.created_at, '%m-%d-%Y') AS transaction_date,

                                    -- Card label
                                    CASE 
                                        WHEN t.card_type IN ('MC', 'MASTERCARD', 'M/C') THEN 'MASTERCARD'  
                                        WHEN t.card_type IN ('VS', 'VISA', 'VISA CREDIT', 'VISA DEBIT') THEN 'VISA'
                                        WHEN t.card_type = 'Disc' THEN 'DISC'
                                        WHEN t.card_type = 'DCVR' THEN 'DCVR'
                                        ELSE t.card_type
                                    END AS card_type_label,

                                    -- Net Amounts
                                    COALESCE(IF(t.grand_total > 0, t.grand_total - t.processing_fee - t.tax_fee - t.additional_fee, 0), 0) AS net_amount,
                                    COALESCE(t.grand_total, 0) AS Pay_grand_total_new,

                                    -- Overstay info
                                    COUNT(te.id) AS no_of_extensions,
                                    SUM(te.grand_total) AS overstayGrandTotal,
                                    SUM(te.discount_amount) AS overstayDiscount,
                                    SUM(te.surcharge_fee) AS surcharge_fee_extend,
                                    SUM(te.tax_fee) AS tax_fee_extend,
                                    SUM(te.additional_fee) AS additional_fee_extend,

                                    -- Final SUM
                                    CASE 
                                        WHEN t.is_extended = '1' THEN IFNULL((SELECT SUM(te3.grand_total) FROM ticket_extends te3 WHERE te3.ticket_id = t.id), 0) + t.grand_total
                                        ELSE t.grand_total
                                    END AS final_sum,

                                    -- Preauth and payment time
                                    t.checkin_time AS preauth_time,
                                    ant.created_at AS payment_time,

                                    -- Final reconciliation report date logic
                                    CASE
                                        WHEN DATE(
                                            GREATEST(
                                                CASE
                                                    WHEN TIME_FORMAT(CASE WHEN t.is_extended = '1' THEN MAX(te.created_at) ELSE t.checkin_time END, '%H:%i') < '22:00'
                                                    THEN DATE_ADD(CASE WHEN t.is_extended = '1' THEN MAX(te.created_at) ELSE t.checkin_time END, INTERVAL 3 DAY)
                                                    ELSE DATE_ADD(CASE WHEN t.is_extended = '1' THEN MAX(te.created_at) ELSE t.checkin_time END, INTERVAL 4 DAY)
                                                END,
                                                IFNULL(ant.created_at, t.checkin_time)
                                            )
                                        ) = DATE(ant.created_at)
                                        THEN DATE_ADD(
                                            GREATEST(
                                                CASE
                                                    WHEN TIME_FORMAT(CASE WHEN t.is_extended = '1' THEN MAX(te.created_at) ELSE t.checkin_time END, '%H:%i') < '22:00'
                                                    THEN DATE_ADD(CASE WHEN t.is_extended = '1' THEN MAX(te.created_at) ELSE t.checkin_time END, INTERVAL 3 DAY)
                                                    ELSE DATE_ADD(CASE WHEN t.is_extended = '1' THEN MAX(te.created_at) ELSE t.checkin_time END, INTERVAL 4 DAY)
                                                END,
                                                IFNULL(ant.created_at, t.checkin_time)
                                            ), INTERVAL 1 DAY
                                        )
                                        ELSE GREATEST(
                                            CASE
                                                WHEN TIME_FORMAT(CASE WHEN t.is_extended = '1' THEN MAX(te.created_at) ELSE t.checkin_time END, '%H:%i') < '22:00'
                                                THEN DATE_ADD(CASE WHEN t.is_extended = '1' THEN MAX(te.created_at) ELSE t.checkin_time END, INTERVAL 3 DAY)
                                                ELSE DATE_ADD(CASE WHEN t.is_extended = '1' THEN MAX(te.created_at) ELSE t.checkin_time END, INTERVAL 4 DAY)
                                            END,
                                            IFNULL(ant.created_at, t.checkin_time)
                                        )
                                    END AS final_reconciliation_report_date

                                FROM tickets t
                                LEFT JOIN anet_transactions ant ON ant.id = t.anet_transaction_id
                                LEFT JOIN ticket_extends te ON te.ticket_id = t.id
                                LEFT JOIN users u ON u.id = t.user_id

                                WHERE 
                                    t.facility_id IN (300)  
                                    AND t.event_id IS NULL 
                                    AND t.deleted_at IS NULL 
                                    AND t.is_checkout = '1'  
                                    AND t.reservation_id IS NULL

                                GROUP BY t.id
                                ) as test where date(final_reconciliation_report_date)=('$checkInTime')

                                
                                ;";
            //GROUP BY t.ticket_number
            //ORDER BY t.created_at DESC
            $driveupDataTicketResults = DB::select($driveupDataTickets);
            //  dd($driveupDataTicketResults);

            if (empty($driveupDataTicketResults)) {
                return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "Data not found11."]], 500);

                //throw new ApiGenericException('Data not found.', 500);
            }

            $finalCodes5['total'] = count($driveupDataTicketResults);
            $finalCodes5['total_revenue']              = 0;
            $finalCodes5['ticket_amount']              = 0;
            $finalCodes5['parking_amount']             = 0;
            $finalCodes5['pe_processing_base']         = 0;
            $finalCodes5['pe_processing_variable']     = 0;
            $finalCodes5['EPGS_processing_variable']   = 0;
            $finalCodes5['PE_CC_Fee']                  = 0;
            $finalCodes5['Tax']                        = 0;
            $finalCodes5['city_surcharge']             = 0;
            $finalCodes5['discount_amount']            = 0;
            $finalCodes5['total_collected']            = 0;
            $finalCodes5['net_revenue']                = 0;
            $finalCodes5['details_list'] = [];
            $totalCalculatedAmount = $ticketAmount = $footerTotalCalculated = $footerTicketAmount = $footerparking = $finalNetAmountfooter = 0;
            $pebase = $peVariable = $EPGSbase = $EPGSvariable = $PECCbase = $PECCvariable = $footerTax = $footercitysurcharge = $footerdiscount = 0;

            foreach ($driveupDataTicketResults as $tkey => $ticket) {

                $facilitybreakdowndquery = "SELECT 
                                    ROUND(
                                        (CASE 
                                            WHEN t.is_extended = '1' THEN (IFNULL((SELECT SUM(te2.total) FROM ticket_extends te2 WHERE te2.ticket_id = t.id), 0) + t.parking_amount)
                                            ELSE t.parking_amount 
                                        END) * pev.rate_value / 100, 2
                                    ) AS 'parkengage_variable',

                                    ROUND(
                                        (CASE 
                                            WHEN t.is_extended = '1' THEN (IFNULL((SELECT SUM(te2.total) FROM ticket_extends te2 WHERE te2.ticket_id = t.id), 0) + t.parking_amount)
                                            ELSE t.parking_amount 
                                        END) * rev.rate_value / 100, 2
                                    ) AS 'revpass_variable',

                                    ROUND(
                                        (CASE 
                                            WHEN t.is_extended = '1' THEN (IFNULL((SELECT SUM(te2.total) FROM ticket_extends te2 WHERE te2.ticket_id = t.id), 0) + t.parking_amount)
                                            ELSE t.parking_amount 
                                        END) * ccv.rate_value / 100, 2
                                    ) AS 'credit_card_variable',

                                    f1.rate_value AS 'credit_card_base',
                                    f2.rate_value AS 'parkengage_base',
                                    f3.rate_value AS 'revpass_base'
                             

                                FROM tickets AS t
                                LEFT JOIN ticket_extends AS te ON te.ticket_id = t.id

                                
                                LEFT JOIN (
                                    SELECT * FROM Facility_FeeBreakdown
                                    WHERE effective_from_time <= '$ticket->checkin_time'
                                    UNION ALL
                                    SELECT * FROM Facility_FeeBreakdown_history
                                    WHERE effective_from_time < '$ticket->checkin_time'
                                ) AS combined_fee ON 1 = 1

                             
                                LEFT JOIN (
                                    SELECT * FROM (
                                        SELECT * FROM Facility_FeeBreakdown
                                        WHERE effective_from_time <= '$ticket->checkin_time'
                                        UNION ALL
                                        SELECT * FROM Facility_FeeBreakdown_history
                                        WHERE effective_from_time < '$ticket->checkin_time'
                                    ) AS all_fees
                                    WHERE display_name = 'parkengage variable' AND rate_type = '1' AND is_active = '1'
                                ) AS pev ON 1 = 1

                                LEFT JOIN (
                                    SELECT * FROM (
                                        SELECT * FROM Facility_FeeBreakdown
                                        WHERE effective_from_time <= '$ticket->checkin_time'
                                        UNION ALL
                                        SELECT * FROM Facility_FeeBreakdown_history
                                        WHERE effective_from_time < '$ticket->checkin_time'
                                    ) AS all_fees
                                    WHERE display_name = 'revpass variable' AND rate_type = '1' AND is_active = '1'
                                ) AS rev ON 1 = 1

                                LEFT JOIN (
                                    SELECT * FROM (
                                        SELECT * FROM Facility_FeeBreakdown
                                        WHERE effective_from_time <= '$ticket->checkin_time'
                                        UNION ALL
                                        SELECT * FROM Facility_FeeBreakdown_history
                                        WHERE effective_from_time < '$ticket->checkin_time'
                                    ) AS all_fees
                                    WHERE display_name = 'credit card variable' AND rate_type = '1' AND is_active = '1'
                                ) AS ccv ON 1 = 1

                               
                                LEFT JOIN (
                                    SELECT * FROM (
                                        SELECT * FROM Facility_FeeBreakdown
                                        WHERE effective_from_time <= '$ticket->checkin_time'
                                        UNION ALL
                                        SELECT * FROM Facility_FeeBreakdown_history
                                        WHERE effective_from_time < '$ticket->checkin_time'
                                    ) AS fixed_fees
                                    WHERE rate_type = '0' AND is_active = '1'
                                    ORDER BY rate_value ASC
                                    LIMIT 1
                                ) AS f1 ON 1 = 1

                                LEFT JOIN (
                                    SELECT * FROM (
                                        SELECT * FROM Facility_FeeBreakdown
                                        WHERE effective_from_time <= '$ticket->checkin_time'
                                        UNION ALL
                                        SELECT * FROM Facility_FeeBreakdown_history
                                        WHERE effective_from_time < '$ticket->checkin_time'
                                    ) AS fixed_fees
                                    WHERE rate_type = '0' AND is_active = '1'
                                    ORDER BY rate_value ASC
                                    LIMIT 1 OFFSET 1
                                ) AS f2 ON 1 = 1

                                LEFT JOIN (
                                    SELECT * FROM (
                                        SELECT * FROM Facility_FeeBreakdown
                                        WHERE effective_from_time <= '$ticket->checkin_time'
                                        UNION ALL
                                        SELECT * FROM Facility_FeeBreakdown_history
                                        WHERE effective_from_time < '$ticket->checkin_time'
                                    ) AS fixed_fees
                                    WHERE rate_type = '0' AND is_active = '1'
                                    ORDER BY rate_value DESC
                                    LIMIT 1
                                ) AS f3 ON 1 = 1
                                WHERE t.facility_id IN ($imploadFaclitiesData) and t.ticket_number='$ticket->ticket_number'";


                $ticketbreakdownhistory = DB::select($facilitybreakdowndquery);
                //  dd($ticketbreakdownhistory[0]->parkengage_variable);

                $finalParkingAmount = $finalNetAmount = $ticket->parking_amount;
                if (!empty($ticket->parking_amount)) {

                    $grossparkingamount = $ticket->parking_amount - ($ticket->discount_amount + $ticket->overstayDiscount);
                    $ticketAmount = $grossparkingamount + $ticket->tax_fee + $ticket->tax_fee_extend + $ticket->surcharge_fee  + $ticket->surcharge_fee_extend + $ticketbreakdownhistory[0]->credit_card_base + $ticketbreakdownhistory[0]->credit_card_variable + $ticketbreakdownhistory[0]->parkengage_base + $ticketbreakdownhistory[0]->parkengage_variable + $ticketbreakdownhistory[0]->revpass_base + $ticketbreakdownhistory[0]->revpass_variable;
                    $totalCalculatedAmount = $ticketAmount;
                    $finalNetAmount = $totalCalculatedAmount - ($ticketbreakdownhistory[0]->credit_card_base + $ticketbreakdownhistory[0]->credit_card_variable + $ticketbreakdownhistory[0]->parkengage_base + $ticketbreakdownhistory[0]->parkengage_variable);
                }
                //end deduction
                $ticketData = [
                    'Ticket Number' => $ticket->ticket_number,
                    'Start Date' => date('m/d/y h:i A', strtotime($ticket->checkin_time)),
                    'End Date' => date('m/d/y h:i A', strtotime($ticket->estimated_checkout)),
                    'Transaction ID' => isset($ticket->anet_trans_id) ? $ticket->anet_trans_id : '-',
                    'Parking Amount ($)' => $ticket->parking_amount,
                    'Discount Amount ($)' =>  floatval($ticket->discount_amount + $ticket->overstayDiscount),
                    'Gross Parking Revenue ($)' => floatval($grossparkingamount),
                    'Tax Amount ($)' => $ticket->tax_fee + $ticket->tax_fee_extend,
                    'City Surcharge ($)' =>  floatval($ticket->surcharge_fee  + $ticket->surcharge_fee_extend),
                    'Credit Card Fees ($)' => [
                        'Base($)' => floatval($ticketbreakdownhistory[0]->credit_card_base),
                        'Variable(%)' => floatval($ticketbreakdownhistory[0]->credit_card_variable),
                    ],
                    'PE Transaction Fee ($)' => [
                        'Base($)' =>  floatval($ticketbreakdownhistory[0]->parkengage_base),
                        'Variable(%)' =>  floatval($ticketbreakdownhistory[0]->parkengage_variable),
                    ],
                    'Revpass Share ($)' => [
                        'Base($)' =>  floatval($ticketbreakdownhistory[0]->revpass_base),
                        'Variable(%)' => floatval($ticketbreakdownhistory[0]->revpass_variable),
                    ],
                    //'Processing Fee ($)' => $ticket->processing_fee,
                    'Ticket Amount ($)' =>  $ticketAmount,
                    'Payment Method' => isset($ticket->card_type_label) ? $ticket->card_type_label : '-',
                    'Amount Paid ($)' =>  floatval($totalCalculatedAmount),
                    '(-) Credit Card Fee ($)' => [
                        'Base($)' => floatval($ticketbreakdownhistory[0]->credit_card_base),
                        'Variable(%)' => floatval($ticketbreakdownhistory[0]->credit_card_variable),
                    ],

                    '(-) ParkEngage Fee ($)' => [
                        'Base($)' =>  floatval($ticketbreakdownhistory[0]->parkengage_base),
                        'Variable(%)' =>  floatval($ticketbreakdownhistory[0]->parkengage_variable),
                    ],

                    'Net Deposit ($)' => floatval($finalNetAmount),
                ];

                $finalCodes5['details_list'][] = $ticketData;
            }


            $finalCodes5['total_revenue']              = 0;
            $finalCodes5['ticket_amount']              = collect($finalCodes5['details_list'])->sum('Ticket Amount ($)');
            $finalCodes5['parking_amount']             = collect($finalCodes5['details_list'])->sum('Parking Amount ($)');
            $finalCodes5['pe_processing_base']         = collect($finalCodes5['details_list'])->sum(function ($item) {
                return floatval($item['PE Transaction Fee ($)']['Base($)'] ?? 0);
            });

            $finalCodes5['pe_processing_variable']     = collect($finalCodes5['details_list'])->sum(function ($item) {
                return floatval($item['PE Transaction Fee ($)']['Variable(%)'] ?? 0);
            });
            $finalCodes5['EPGS_processing_base']       = collect($finalCodes5['details_list'])->sum(function ($item) {
                return floatval($item['Revpass Share ($)']['Base($)'] ?? 0);
            });
            $finalCodes5['EPGS_processing_variable']   = collect($finalCodes5['details_list'])->sum(function ($item) {
                return floatval($item['Revpass Share ($)']['Variable(%)'] ?? 0);
            });
            $finalCodes5['PE_CC_Fee_base']             = collect($finalCodes5['details_list'])->sum(function ($item) {
                return floatval($item['Credit Card Fees ($)']['Base($)'] ?? 0);
            });
            $finalCodes5['PE_CC_Fee_variable']         = collect($finalCodes5['details_list'])->sum(function ($item) {
                return floatval($item['Credit Card Fees ($)']['Variable(%)'] ?? 0);
            });
            $finalCodes5['Tax']                        = collect($finalCodes5['details_list'])->sum('Tax Amount ($)');
            $finalCodes5['city_surcharge']             = collect($finalCodes5['details_list'])->sum('City Surcharge ($)');
            $finalCodes5['discount_amount']            = collect($finalCodes5['details_list'])->sum('Discount Amount ($)');
            $finalCodes5['total_collected']            = collect($finalCodes5['details_list'])->sum('Amount Paid ($)');
            $finalCodes5['net_revenue']                = collect($finalCodes5['details_list'])->sum('Net Deposit ($)');
            $finalCodes5['color']                      = $color;
            $finalCodes5['location_id']                = $locationId;
            $finalCodes5['locationName']                = $locationName;
            $finalCodes5['processingfee']                = 0;
            $finalCodes5['gross_parking']                = collect($finalCodes5['details_list'])->sum('Discount Amount ($)');
            $finalCodes5['print_date']                = "Report Date Range -" .  date('m-d-Y', strtotime($checkInTime)) .  ' to ' . date('m-d-Y', strtotime($checkOutTime));;
            dd($finalCodes5['details_list']);
            $excelSheetName = "Bank Deposit Reconciliation";
            //$finalCodes5,$fromdate,$toDate,$excelSheetName5
            Excel::create(
                $excelSheetName,
                function ($excel) use (
                    $finalCodes5,
                    $fromdate,
                    $toDate,
                    $excelSheetName
                ) {
                    $excel->sheet($excelSheetName, function ($sheet5) use ($finalCodes5, $fromdate, $toDate, $excelSheetName) {
                        $topSpace = 7;
                        $sheet5->setWidth(array(
                            'A'     => 21,
                            'B'     =>  24,
                            'C'     =>  18,
                            'D'     =>  17.57,
                            'E'     =>  17.34,
                            'F'     =>  24,
                            'G'    =>   21,
                            'H'    =>   16.86,
                            'I'    =>   18,
                            'J'    =>   26.78,
                            'K'    =>   18,
                            'N'    =>   17,
                            'O'    =>   18,
                            'P'    =>   10,
                            'Q'    =>   18,

                        ));
                        $color = $finalCodes5['color'];
                        $garageCode = $finalCodes5['location_id'];
                        $locationName = $finalCodes5['locationName'];

                        $sheet5->getColumnDimension('D')->setWidth(20);
                        $sheet5->getColumnDimension('E')->setWidth(22.34);

                        $sheet5->getColumnDimension('F')->setWidth(24);
                        $sheet5->getColumnDimension('G')->setWidth(24);
                        $sheet5->getColumnDimension('H')->setWidth(16.86);
                        $sheet5->getColumnDimension('M')->setWidth(16.86);
                        $sheet5->getColumnDimension('P')->setWidth(16.86);
                        $sheet5->getColumnDimension('R')->setWidth(22);
                        $sheet5->getColumnDimension('S')->setWidth(36);
                        $sheet5->getColumnDimension('M')->setWidth(36);
                        $sheet5->getColumnDimension('O')->setWidth(36);
                        $sheet5->getColumnDimension('T')->setWidth(20);
                        $sheet5->getColumnDimension('V')->setWidth(20);
                        $sheet5->getColumnDimension('L')->setWidth(20);
                        $sheet5->getColumnDimension('N')->setWidth(20);
                        $sheet5->getColumnDimension('R')->setWidth(20);
                        $sheet5->getColumnDimension('T')->setWidth(20);
                        $sheet5->getColumnDimension('J')->setWidth(20);
                        $sheet5->getColumnDimension('V')->setWidth(20);
                        $sheet5->getColumnDimension('U')->setWidth(36);
                        $sheet5->getColumnDimension('K')->setWidth(36);
                        $sheet5->getColumnDimension('W')->setWidth(20);



                        // $sheet5->getStyle("D")->getNumberFormat()->setFormatCode('0.000');
                        $sheet5->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("H")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("K")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("L")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("N")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("O")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("I")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("J")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("C3")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("M")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("P")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("S")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("Q")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("R")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("U")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("T")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("V")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->getStyle("W")->getNumberFormat()->setFormatCode('0.00');

                        $colorCode = count($finalCodes5['details_list']) + 8;
                        $row_name = 'A' . $colorCode . ':W' . $colorCode;
                        $sheet5->cell($row_name, function ($row) use ($color) {
                            $row->setBackground($color);
                            $row->setFontColor('#ffffff');
                            $row->setFont(array(
                                'family'     => 'Calibri',
                                'size'       => '12',
                                'bold'       =>  true
                            ));
                        });

                        $sheet5->mergeCells('A1:V1');
                        $sheet5->getRowDimension(1)->setRowHeight(60);
                        // $this->addLogoInExcelHeader($sheet5);
                        $sheet5->setCellValue('A1', "Bank Deposit Reconciliation");

                        $sheet5->mergeCells('A2:D2');
                        $cellValue = "Report Date Range -" .  date('m-d-Y', strtotime($fromdate)) .  ' to ' . date('m-d-Y', strtotime($toDate));
                        $cellValue .= "\nPrint Date - " . date('m-d-Y');
                        $sheet5->setCellValue('A2', "$cellValue");
                        $sheet5->getStyle('A2')->getAlignment()->setWrapText(true);

                        // Set the height of cell H2 (adjust as needed)
                        $sheet5->getRowDimension(2)->setRowHeight(80);
                        $sheet5->getRowDimension(3)->setRowHeight(50);
                        $sheet5->cell('A2:D2', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#D6DCE4');
                            $cell->setFontColor('#000000');
                            $cell->setFontSize('18');
                        });
                        $location = "Location Name \r" . $locationName;

                        $sheet5->mergeCells('E2:G2');
                        $sheet5->setCellValue('E2', "$location");
                        $sheet5->getStyle('E2')->getAlignment()->setWrapText(true);
                        $sheet5->cell('E2:G2', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center'); // Center vertically
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#D6DCE4');
                            $cell->setFontColor('#000000');
                            $cell->setFontSize('18');
                        });
                        $sheet5->mergeCells('H2:W2');
                        $locationId = "Location ID \n" . $garageCode;
                        $sheet5->setCellValue('H2', "$locationId");
                        $sheet5->getStyle('H2')->getAlignment()->setWrapText(true);

                        $sheet5->cell('H2:W2', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#D6DCE4');
                            $cell->setFontColor('#040D12');
                            $cell->setFontSize('18');
                        });


                        // Ticket Count and Revenue Row Start !!!!
                        $sheet5->mergeCells('A3:B3');
                        $sheet5->setCellValue('A3', "Total Tickets");
                        $sheet5->cell('A3:B3', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });

                        $sheet5->mergeCells('C3:C3');

                        $sheet5->setCellValue('C3', count($finalCodes5['details_list']));

                        $sheet5->cell('C3:C3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });

                        // !! Event or Permit Counter Start
                        // $sheet5->mergeCells('D3:E3');
                        $sheet5->setCellValue('D3', "Total Revenue ($)");

                        $sheet5->cell('D3:E3', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });

                        $sheet5->getStyle("F3")->getNumberFormat()->setFormatCode('0.00');
                        $sheet5->mergeCells('F3:F3');

                        $sheet5->cell('F3:F3', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });

                        $sheet5->mergeCells('G3:P3');
                        $sheet5->cell('G3:P3', function ($cell) use ($color) {
                            $cell->setAlignment('center'); // Center horizontally
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });
                        $sheet5->cell('I3:W3', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground($color);
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });
                        $sheet5->getRowDimension(4)->setRowHeight(30);
                        $sheet5->mergeCells('A4:D4');
                        $sheet5->cell('A4:C4', function ($cell) use ($color) {
                            $cell->setBackground('#808080');
                        });

                        $sheet5->mergeCells('E4:G4');
                        $sheet5->setCellValue('E4', 'Parking Amount ($)');
                        $sheet5->cell('E4:G4', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#BFBFBF');
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });

                        $sheet5->mergeCells('H4:I4');
                        $sheet5->setCellValue('H4', '(+) Taxes');
                        $sheet5->cell('H4:I4', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground("#808080");
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });
                        $sheet5->mergeCells('J4:O4');
                        $sheet5->setCellValue('J4', '(+) Processing Fees');
                        $sheet5->cell('J4:O4', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#BFBFBF');
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });
                        $sheet5->mergeCells('P4:R4');
                        $sheet5->setCellValue('P4', '(=) Ticket Amount = Payment Amount');
                        $sheet5->cell('P4:R4', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground("#808080");
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });
                        $sheet5->mergeCells('S4:V4');
                        $sheet5->setCellValue('S4', '(-) Deductions from Payment');
                        $sheet5->cell('S4:V4', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#BFBFBF');
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });
                        $sheet5->setCellValue('W4', '(=) Deposit');
                        $sheet5->cell('W4', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground("#808080");
                            $cell->setFontColor('#ffffff');
                            $cell->setFontSize('12');
                        });
                        $sheet5->mergeCells('A4:D4');

                        // Drive Up Section Start Here.
                        $sheet5->mergeCells('A5:W5');
                        $sheet5->setCellValue('A5', 'Tickets Cashiered DriveUp');
                        $sheet5->cell('A5', function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                            $cell->setFontWeight('bold');
                            $cell->setBackground('#D9E1F2');
                            $cell->setFontColor('#272829');
                            $cell->setFontSize('12');
                        });

                        // Color Row For Heading 
                        $sheet5->cell('A6:W6', function ($row) use ($color) {
                            $row->setBackground($color);
                            $row->setFontColor('#ffffff');
                            $row->setAlignment('center');
                            $row->setValignment('center');
                        });

                        //for colom cell value
                        $K = 6;
                        $sheet5->mergeCells('J6:K6');
                        $sheet5->mergeCells('L6:M6');
                        $sheet5->mergeCells('N6:O6');

                        $sheet5->mergeCells('S6:T6');
                        $sheet5->mergeCells('U6:V6');
                        $sheet5->setCellValue('A' . $K, 'Ticket Number');
                        $sheet5->setCellValue('B' . $K, 'Start');
                        $sheet5->setCellValue('C' . $K, 'End');
                        $sheet5->setCellValue('D' . $K, 'Transaction ID');
                        $sheet5->setCellValue('E' . $K, 'Parking Amount ($)');
                        $sheet5->setCellValue('F' . $K, '(-)Discount Amount ($)');
                        $sheet5->setCellValue('G' . $K, '(=)Gross Parking Revenue ($)');
                        $sheet5->setCellValue('H' . $K, '(+)City Surcharge ($)');
                        $sheet5->setCellValue('I' . $K, '(+)Tax Amount ($)');


                        $sheet5->setCellValue('J' . $K, '(+)Credit Card Fees ($)');
                        $sheet5->setCellValue('L' . $K,  '(+)PE Transaction Fee ($)');
                        $sheet5->setCellValue('N' . $K, '(+)Revpass Share ($)');
                        // $sheet5->setCellValue('H' . $K, 'Processing Fee ($)');
                        $sheet5->setCellValue('P' . $K, '(=)Ticket Amount ($)');
                        $sheet5->setCellValue('Q' . $K, 'Payment Method');
                        $sheet5->setCellValue('R' . $K, 'Amount Paid ($)');
                        $sheet5->setCellValue('S' . $K, '(-) Credit Card Fee ($)');
                        $sheet5->setCellValue('U' . $K, '(-) ParkEngage Fee ($)');
                        $sheet5->setCellValue('W' . $K, 'Net Deposit ($)');


                        $K++;
                        $sheet5->cell('A7:W7', function ($row) use ($color) {
                            $row->setBackground($color);
                            $row->setFontColor('#ffffff');
                            $row->setAlignment('center');
                            $row->setValignment('center');
                        });

                        $sheet5->cell('I7:J7', function ($row) use ($color) {
                            $row->setAlignment('center');
                            $row->setValignment('center');
                        });
                        $sheet5->setCellValue('J' . $K, 'Base($)');
                        $sheet5->setCellValue('K' . $K, 'Variable(%) ');
                        $sheet5->setCellValue('L' . $K, 'Base($)');
                        $sheet5->setCellValue('M' . $K, 'Variable(%)');
                        $sheet5->setCellValue('N' . $K, 'Base($)');
                        $sheet5->setCellValue('O' . $K, 'Variable(%) ');

                        $sheet5->setCellValue('S' . $K, 'Variable($)');
                        $sheet5->setCellValue('T' . $K, 'Fixed(%)');
                        $sheet5->setCellValue('U' . $K, 'Variable($)');
                        $sheet5->setCellValue('V' . $K, 'Fixed(%)');

                        //all data 

                        foreach ($finalCodes5['details_list'] as $ticketList) {

                            $K++;
                            $sheet5->cell('A' . $K . ':W' . $K, function ($cell) use ($color) {
                                $cell->setAlignment('center');
                                $cell->setValignment('center');
                            });

                            $sheet5->setCellValue('A' . $K, $ticketList['Ticket Number']);
                            $sheet5->setCellValue('B' . $K, $ticketList['Start Date']);
                            $sheet5->setCellValue('C' . $K, $ticketList['End Date']);
                            $sheet5->setCellValue('D' . $K, $ticketList['Transaction ID']);
                            $sheet5->setCellValue('E' . $K, $ticketList['Parking Amount ($)']);
                            $sheet5->setCellValue('F' . $K, $ticketList['Discount Amount ($)']);
                            $sheet5->setCellValue('G' . $K, $ticketList['Gross Parking Revenue ($)']);
                            $sheet5->setCellValue('H' . $K, $ticketList['City Surcharge ($)']);
                            $sheet5->setCellValue('I' . $K, $ticketList['Tax Amount ($)']);
                            $sheet5->setCellValue('J' . $K,  array_values($ticketList['Credit Card Fees ($)'])[0]);
                            $sheet5->setCellValue('K' . $K,  array_values($ticketList['Credit Card Fees ($)'])[1]);
                            $sheet5->setCellValue('L' . $K,  array_values($ticketList['PE Transaction Fee ($)'])[0]);
                            $sheet5->setCellValue('M' . $K,  array_values($ticketList['PE Transaction Fee ($)'])[1]);
                            $sheet5->setCellValue('N' . $K,  array_values($ticketList['Revpass Share ($)'])[0]);
                            $sheet5->setCellValue('O' . $K,  array_values($ticketList['Revpass Share ($)'])[1]);
                            $sheet5->setCellValue('P' . $K, $ticketList['Ticket Amount ($)']);
                            $sheet5->setCellValue('Q' . $K, $ticketList['Payment Method']);
                            $sheet5->setCellValue('R' . $K, $ticketList['Amount Paid ($)']);
                            $sheet5->setCellValue('S' . $K,  array_values($ticketList['Credit Card Fees ($)'])[1]);
                            $sheet5->setCellValue('T' . $K,  array_values($ticketList['Credit Card Fees ($)'])[0]);
                            $sheet5->setCellValue('U' . $K,  array_values($ticketList['PE Transaction Fee ($)'])[1]);
                            $sheet5->setCellValue('V' . $K,  array_values($ticketList['PE Transaction Fee ($)'])[0]);
                            $sheet5->setCellValue('W' . $K, $ticketList['Net Deposit ($)']);
                        }

                        $K++;
                        $sheet5->cell('A' . $K . ':W' . $K, function ($cell) use ($color) {
                            $cell->setAlignment('center');
                            $cell->setValignment('center');
                        });
                        $sheet5->setCellValue('A' . $K, 'Total');
                        $sheet5->setCellValue('B' . $K, '-');
                        $sheet5->setCellValue('C' . $K, '-');
                        $sheet5->setCellValue('D' . $K, '-');
                        $sheet5->setCellValue('E' . $K, $finalCodes5['parking_amount']);
                        $sheet5->setCellValue('F' . $K, $finalCodes5['discount_amount']);
                        $sheet5->setCellValue('G' . $K, $finalCodes5['gross_parking']);
                        $sheet5->setCellValue('H' . $K, $finalCodes5['city_surcharge']);
                        $sheet5->setCellValue('I' . $K, $finalCodes5['Tax']);


                        $sheet5->setCellValue('J' . $K, $finalCodes5['PE_CC_Fee_base']);
                        $sheet5->setCellValue('K' . $K, $finalCodes5['PE_CC_Fee_variable']);
                        $sheet5->setCellValue('L' . $K, $finalCodes5['pe_processing_base']);
                        $sheet5->setCellValue('M' . $K, $finalCodes5['pe_processing_variable']);
                        $sheet5->setCellValue('N' . $K, $finalCodes5['EPGS_processing_base']);
                        $sheet5->setCellValue('O' . $K, $finalCodes5['EPGS_processing_variable']);
                        $sheet5->setCellValue('P' . $K, $finalCodes5['ticket_amount']);
                        $sheet5->setCellValue('Q' . $K, '-');
                        $sheet5->setCellValue('R' . $K, $finalCodes5['total_collected']);
                        $sheet5->setCellValue('S' . $K, $finalCodes5['PE_CC_Fee_variable']);
                        $sheet5->setCellValue('T' . $K, $finalCodes5['PE_CC_Fee_base']);
                        $sheet5->setCellValue('U' . $K, $finalCodes5['pe_processing_variable']);
                        $sheet5->setCellValue('V' . $K, $finalCodes5['pe_processing_base']);
                        $sheet5->setCellValue('W' . $K, $finalCodes5['net_revenue']);


                        //$sheet5->setCellValue('O' . $K, $finalCodes5['total_collected']);
                        // $sheet5->setCellValue('P' . $K, $finalCodes5['net_revenue']);
                        $sheet5->getStyle("C3")->getNumberFormat()->setFormatCode('0');
                        $sheet5->getStyle("W")->getNumberFormat()->setFormatCode('0');
                        $sheet5->setCellValue('F3', $finalCodes5['total_collected']);
                    });
                }
            )->store('xls')->download('xls');
        }
    }
}
