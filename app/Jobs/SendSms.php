<?php

namespace App\Jobs;

use App\Jobs\Job;
use App\Services\LoggerFactory;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use <PERSON><PERSON><PERSON>\Rest\Client;
use <PERSON>wi<PERSON>\Jwt\ClientToken;
use Twilio\Exceptions\RestException;


class SendSms extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    //protected $successLog;
    protected $phone;
    protected $msg;
    
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($msg, $phone)
    {
        //$logFactory = new LoggerFactory();
        //$this->successLog = $logFactory->setPath('logs/parkengage')->createLogger('sms');
        $this->phone = $phone;
        $this->msg = $msg;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //$this->successLog->info("sms about to send. {$this->msg} sent to $this->phone");

        if($this->phone == '' || $this->msg == ''){
          //  $this->successLog->info("blank data");
            return true;
        }
        try {
            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
            $client = new Client($accountSid, $authToken);
            try {

                
                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                  // the number you'd like to send the message to
                  $this->phone,
                  array(
                    // A Twilio phone number you purchased at twilio.com/console
                    'from' => config('parkengage.TWILIO_PHONE'),
                    // the body of the text message you'd like to send
                    //'body' => "Fine"
                    'body' => "$this->msg",
                    //'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)
        
                  )
                );
                //$this->successLog->info("Message : {$this->msg} sent to $this->phone");
              } catch (RestException $e) {
                //$this->successLog->error($e->getMessage());
              }

        } catch (RestException $th) {
            //$this->successLog->error('Error ' . $th->getMessage() . ' File Name :  ' . $th->getFile() . ' Line ' . $th->getLine());
        }
    }

    
}
