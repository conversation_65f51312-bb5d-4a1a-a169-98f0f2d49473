<?php

namespace App\Jobs;

use Exception;
use Monolog\Logger;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;



use App\Exceptions\ApiGenericException;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Storage;
use App\Models\MonthlyParkingUser;
use App\Models\User;
use App\Classes\LoyaltyProgram;
use DB;

/**
 * Checks if a user has accepted terms and condition and .
 * Steps:
 *
 * @package App\Jobs
 */
class ProcessUserWithoutLoyaltyAccount extends CronJob implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    const MAIL_QUEUE = 'iqp-loyalty-account';
    const LOG_DIRECTORY = 'logs/mpuser';

    protected $user;

    protected $successLog;
    protected $errorLog;

    public function __construct(User $account)
    {  
        $this->user = $user;
    }

    public function handle()
    { 
        try { 
            $this->createLoggers();
            // creating user account for loyalty program
            $this->successLog->info("User with user id {$user->id} and email {$user->email}  account has started");
            if($user->is_monthly_ir){
               $this->successLog->info("User with user id {$user->id} and email {$user->email} is a monthly user for which account creation has started."); 
            }
            $accountResponse = LoyaltyProgram::createAccount($user->email);
            if (isset($accountResponse['data']['id'])) {

                $accountResponse = $accountResponse['data'];
                $loyaltyAccount = new LoyaltyUserAccounts;
                $loyaltyAccount->user_id = $user->id;
                $loyaltyAccount->account_id = $accountResponse['id'];
                $loyaltyAccount->account_no = $accountResponse['number'];
                $loyaltyAccount->account_pin = $accountResponse['pin'];
                $loyaltyAccount->reward_no = mt_rand(**********, **********);
                $loyaltyAccount->chain_name = $accountResponse['chainName'];
                $loyaltyAccount->status = $accountResponse['status'];
                $loyaltyAccount->track = $accountResponse['track2'];

                $loyaltyAccount->save();
                
                $responseAddAlias = LoyaltyProgram::addAccountAlias($user->email, $user->id, $loyaltyAccount->account_id, array(
                    'id' => $loyaltyAccount->reward_no
                ));

                
                if(isset($responseAddAlias['success']) && $responseAddAlias['success']) {
                   
                   $this->successLog->info("User with user id {$user->id} and email {$user->email} enrollement in Icon Rewards Program completed successfully.");
                    
                } else {
                   $responseAddAlias = json_encode($responseAddAlias);
                   $this->errorLog->error("There was an error in add alias for user {$user->id} and email {$user->email} with loyalty program with error {$responseAddAlias}");
                }
            } else {
                $this->errorLog->error("There was an error in add alias for user {$user->id} and email {$user->email} with loyalty program with error {$responseAddAlias}");
            }
         } catch (Exception $e) {
           $this->errorLog->error($e->getMessage());
          // throw $e;
        
        }
    }

    

    protected function createLoggers()
    {
        $this->errorLog = $this->createLogger('enroll-monthly-parking-user-error');
        $this->successLog = $this->createLogger('enroll-monthly-parking-user-success');
    }
/*
    protected function createLogger($logger_name)
    {
        // setup the log files to have the monthly_user_id and account_number surrounded by brackets after the date /time
        // this should allow for easier parsing in the event something goes wrong
        $format = "[%datetime%][%level_name%][%monthly_user_id%][%account_number%] %message% %context% %extra%\n";
        $path = $this->loggerDirectoryPath() . "/" . $logger_name . ".log";

        $handler = new RotatingFileHandler($path);
        $handler->setFormatter(new LineFormatter($format, null, false, true));
        $logger = new Logger('transaction_status');
        $logger->pushHandler($handler);
        $logger->pushProcessor(
            function ($record) {
                $record['account_number'] = $this->account->account_number;
                $record['monthly_user_id'] = $this->account->id;
                return $record;
            }
        );
        return $logger;
    }

    protected function loggerDirectoryPath()
    {
        $dir = storage_path(self::LOG_DIRECTORY);

        // Verify that directory exists
        if (!Storage::exists($dir)) {
            Storage::makeDirectory($dir);
        }

        return $dir;
    }

*/



}
