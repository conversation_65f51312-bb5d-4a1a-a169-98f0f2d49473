<?php

namespace App\Jobs;

use Mail;

use App\Exceptions\AuthorizeNetException;

use App\Models\AuthorizeNetTransaction;
use App\Models\AuthNetStatus;

use App\Classes\AuthorizeNet\Transactions;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class UpdateTransactionStatus extends CronJob implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    protected $logger;
    protected $transaction;
    protected $status;
    protected $template = null;
    protected $returnedReasons = null;

    const LOG_DIRECTORY = 'logs/transaction-status';

    public function __construct($transactionId)
    {
        $this->transaction = AuthorizeNetTransaction::find($transactionId);
    }

    public function handle()
    {
        $this->createLoggers();

        // Make sure that this transaction was not voided at capture. If it was, end the job and do not send any emails
        if ($this->transaction->is_capture_transaction && $this->transaction->is_refunded_or_voided) {
            $this->successLog->info("Transaction {$this->transaction->id} was already refunded or voided, not sending email.");
            return;
        }

        try {
            $response = (new Transactions())->setTransaction($this->transaction)->get();
        } catch (AuthorizeNetException $e) {
            $this->errorLog->error("Could not updated status for transaction {$this->transaction->id}, Authorize.Net returned response {$e->getMessage()}.");
            return;
        }

        $this->status = AuthNetStatus::where('status', $response['status'])->first();

        // Make sure that we don't have any returned items
        // NOTE: If an ACH transaction gets returned, Auth.Net will return a status of 'settledSuccessfully' and an additional field,
        // returned items, that contains status codes and reasons for the failure. We need to set our status manually in this case,
        // and also send the correct errors to the front end
        if ($response['returned_items']) {
            $this->returnedReasons = $response['returned_items']->map(
                function ($return) {
                    return $return['description'];
                }
            );

            // Manually get the returned item status
            $this->status = AuthNetStatus::where('status', Transactions::STATUS_RETURNED)->first();
        }

        if (!$this->status) {
            $message = "Unrecognized status {$response['status']} returned from Authorize.Net for transaction {$this->transaction->id}.";
            $this->errorLog->error($message);
            return;
        }

        $this->transaction->anet_status_id = $this->status->id;
        $this->transaction->save();

        $this->successLog->info("Updated status for transaction {$this->transaction->id} to {$this->status->status}");

        $this->sendUpdatedStatusEmail();
    }

    protected function sendUpdatedStatusEmail()
    {
        // No notification email needed
        if ($this->status->category === AuthNetStatus::SUCCESS) {
            return;
        }

        $facility = $this->transaction->capture_facility;

        $data = [
            'merchant' => $facility ? $facility->full_name : false,
            'payment_date_time' => $this->transaction->created_at,
            'total' => $this->transaction->total,
            'payment' => $this->transaction->payment_details,
            'transaction_id' => $this->transaction->anet_trans_id,
            'auth_code' => $this->transaction->auth_code,
            'account_number' => $this->transaction->monthlyParkingPayment
                ? $this->transaction->monthlyParkingPayment->monthlyParkingUser->account_number
                : false,
            'garage_code' => $facility ? $facility->garage_code : false,
            'error_message' => $this->transaction->response_message ?: ''
        ];

        switch ($this->status->category) {
        case AuthNetStatus::FAILED:
            if ($payment = $this->transaction->monthlyParkingPayment) {
                $this->template = $payment->is_autopay ? 'payments.autopay-failure' : 'payments.one-time-failure';
                break;
            }

            $this->template = 'payments.reservation-failure';
            break;
        case AuthNetStatus::RETURNED:
            $data['status'] = 'returned'; // Intentional fallthrough
            $data['error_message'] = "Return reason provided: {$this->returnedReasons[0]}";
            $this->template = 'payments.void-or-returned';
            break;
        case AuthNetStatus::VOID:
            $data['status'] = 'voided';
            $data['error_message'] = 'The transaction was marked void by the merchant before settlement.'; // Intentional fallthrough
            $this->template = 'payments.void-or-returned';
            break;
        case AuthNetStatus::REFUNDED:
            $data['status'] = 'refunded';
            $this->template = 'payments.void-or-returned';
            break;
        }

        if (!$this->template) {
            $message = "Unable to find template for category {$this->transaction->authNetStatus->category} for transaction {$this->transaction->id}.";
            $this->errorLog->error($message);
            return;
        }

        Mail::send(
            $this->template, $data, function ($message) {
                $message->subject('Your payment status has been updated');
                $message->to($this->transaction->user->email);
                $message->from(config('icon.email'));
            }
        );
    }

    protected function createLoggers()
    {
        $this->errorLog = $this->createLogger('transaction_status_error');
        $this->successLog = $this->createLogger('transaction_status_success');
    }
}
