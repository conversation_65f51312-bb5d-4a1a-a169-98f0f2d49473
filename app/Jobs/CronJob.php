<?php

namespace App\Jobs;

use Monolog\Logger;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;

use Closure;

/**
 * Base class to provide common functionality to Cron jobs
 *
 * @package App\Jobs
 */
abstract class CronJob extends Job
{

    const LOG_DIRECTORY = "logs";

    protected function createLogger($logger_name, $format = null, Closure $processor = null)
    {
        $format = $format ?: "[%datetime%][%level_name%] %message% %context% %extra%\n";
        $path = $this->loggerDirectoryPath() . "/" . $logger_name . ".log";

        $handler = new RotatingFileHandler($path);
        $handler->setFormatter(new LineFormatter($format, null, false, true));

        $logger = new Logger($logger_name);
        $logger->pushHandler($handler);

        if ($processor) {
            $logger->pushProcessor($processor);
        }
        return $logger;
    }

    protected function loggerDirectoryPath()
    {
        // Important that you use static and not self here so that different cron jobs
        // can put their logs in different directories
        // See http://php.net/manual/en/language.oop5.late-static-bindings.php
        return storage_path(static::LOG_DIRECTORY);
    }
}
