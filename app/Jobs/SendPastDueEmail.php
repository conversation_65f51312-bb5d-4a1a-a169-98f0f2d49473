<?php

namespace App\Jobs;

use Exception;
use App\Exceptions\ApiGenericException;

use App\Classes\ArApi;
use App\Services\Mailers\UserMailer;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Storage;

/**
 * Checks if an account is past due and sends reminder emails.
 * Steps:
 *
 * @package App\Jobs
     */
class SendPastDueEmail extends CronJob implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    const MAIL_QUEUE = 'iqp-pastdue-mail';
    const LOG_DIRECTORY = 'logs/pastdue';

    protected $accountNumber;
    protected $tenant;
    protected $email;
    protected $tenantSpot;
    protected $facility;
    protected $currentBalance;
    protected $threshold;
    protected $subject;
    protected $template;
    protected $userDescription;

    //Amount user owes before auto pay.
    protected $currentMonthBalance;

    protected $successLog;
    protected $errorLog;
    protected $allEmailsLog;

    public function __construct($account, $threshold, $farPast = false)
    {
        $this->accountNumber = $account->Account_Code;
        $this->tenant = title_case($account->Full_Name);
        $this->email = strtolower($account->Email);
        $this->tenantSpot = $account->Tenant_Spot;
        $this->facility = $account->Name;
        $this->template = $farPast ? 'pastdue.farpastdue' : 'pastdue.pastdue';
        $this->subject = $farPast ? "Overdue Payment for Account #$this->tenantSpot-$this->accountNumber ($this->facility)" : "Payment Reminder for Account #$this->tenantSpot-$this->accountNumber ($this->facility) ";
        $this->threshold = $threshold;
        $this->userDescription = "monthly user {$this->tenant} account number [{$this->accountNumber}]";
    }

    public function handle()
    {
        try {
            $this->createLoggers();

            //check for payment reminder flag
            $responsePaymentReminder = ArApi::checkPaymentReminderFlag($this->accountNumber);
            
            if (!$responsePaymentReminder['success']) {
                throw new ApiGenericException("Error retrieving account information");
            }            
            $dataPaymentReminder = (object)$responsePaymentReminder['data'];
            if($dataPaymentReminder->skip_payment_reminder_flag)
            {
                $this->successLog->info("Payment Reminder is disable, Account ".$this->accountNumber." has been Cancelled for no email sent.");
                return;
            }
            
            $response = ArApi::getBalances($this->accountNumber);
            if (!$response['success']) {
                throw new ApiGenericException("Error retrieving account balances for [$this->userDescription].", 500, $response['data']);
            }
            
            $data = (object)$response['data'];
            $this->currentBalance = $data->CurrmonthBal;

            if ($this->currentBalance <= 0 || $this->currentBalance < $this->threshold) {
                $this->successLog->info("No overdue balance for $this->userDescription no email sent.");
                
                //call function for complition mail if its last queued up
                $this->SendPastDueCompletionEmail();
                return;
            }
            $this->sendPastDueEmail();
            $this->successLog->info("Overdue email sent for $this->userDescription.");
            
            //call function for complition mail if its last queued up
            $this->SendPastDueCompletionEmail();
            return;
        } catch (Exception $e) {
            $this->errorLog->error($e->getMessage());
            throw $e;
        }
    }


    protected function sendPastDueEmail()
    {
        // to create log for send past due emails
        $this->allEmailsLog->info("sendPastDueEmail() method is called for:- $this->email.");
        $emailData = [
            'tenant' => $this->tenant,
            'balance' => $this->currentBalance
        ];

        (new UserMailer())->queueMailTo($this->email, $this->subject, $emailData, $this->template);
        //log All mails which has been sent in pastdue-email
        $this->allEmailsLog->info("Mail has been sent for $this->userDescription");
    }

    // Function to check last job for sending complition mail
    protected function SendPastDueCompletionEmail()
    {
        //json file to store total count of accounts also the job counter number
        $fileName = 'lastQueuedChecker.json';
        
        //file location:- storage/app/temp/
        //provide full permission to this location as it will be updated for all jobs
        $json = Storage::disk('local')->get('/temp/'.$fileName);
        $json = (object)json_decode($json, true);
        $this->allEmailsLog->info("Queue completed function is called for job counter- ".$json->counter." ");
       
       //counter is started by 0 , so total count must be counter+1       
        if((int)$json->totalqueued == ((int)$json->counter+1) ){

            //sleep for 4 minuts to insure complition mail will be send at last
            sleep(240);

            //loging completion mail too
            $this->allEmailsLog->info("Queue completed its task now (Its a completion mail sent to : ".config('icon.technical_emails').")");
            
            //Complation mail 
            (new UserMailer())->queueMailTo(
            [config('icon.technical_emails')],
            "Overdue emails have been sent successfully -- Completion successful",
            ['body' => "Overdue emails have been sent successfully to all queued accounts"],
            'email.generic'
        );

        }
        else{

            //if job is not last, incriment counter by 1. on same file
            $data = json_encode(["totalqueued"=>(int)$json->totalqueued,"counter"=>((int)$json->counter+1)]);
            Storage::disk('local')->put('/temp/'.$fileName, $data);
        }
    }

    protected function createLoggers()
    {
        $this->errorLog = $this->createLogger('pastdue-error');
        $this->successLog = $this->createLogger('pastdue-success');
        $this->allEmailsLog = $this->createLogger('pastdue-email');
    }
}
