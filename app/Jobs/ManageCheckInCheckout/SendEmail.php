<?php

namespace App\Jobs\ManageCheckInCheckout;

use App\Exceptions\ApiGenericException;
use App\Http\Helpers\MailHelper;
use App\Http\Helpers\QueryBuilder;
use App\Jobs\Job;
use App\Models\Ticket;
use App\Services\LoggerFactory;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\BusinessPolicy;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Models\OverstayTicket;
use App\Models\ParkEngage\TicketExtend;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use PHPExcel_Worksheet_Drawing;

class SendEmail extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    protected $successLog;
    protected $errorLog;
    protected $request;
    protected $sqlWhereClause;
    protected $toEmails;
    protected $dateTimeRange;
    protected $isOffline;
    protected $revpassPartnerId;

    const TYPE_ALL = 0;
    // const CHECKIN2                  = 'check-in-against-booking';
    const CHECKIN                   = 'check-in-against-booking';
    const CHECKOUT = 2;
    const DRIVEUP                   = 'transient-checkins';
    // const DRIVEUP2                  = 'transient-checkins';
    const OFFPAYMENT = 'checkout-from-admin';
    const THIRDPARTY = 'third-party-tickets';
    const CHECKINAGAINSTPERMIT = 'check-in-against-permit';
    const CHECKIN_AGAINST_PASSES    = 'check-in-against-passes';


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $logFactory = new LoggerFactory();
        $this->successLog = $logFactory->setPath('logs/managecheckinchekout_job')->createLogger('sendmail_success');
        $this->errorLog = $logFactory->setPath('logs/managecheckinchekout_job')->createLogger('sendmail_error');
        $this->request = (object) $request[0];
        $this->sqlWhereClause = '';
        $this->dateTimeRange = '';
        $this->isOffline = false;
        $this->getToEmails();
        $this->revpassPartnerId = config('parkengage.PARTNER_RevPass');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->successLog->info('okay from job ' . json_encode($this->request));
        try {
            //code...

            // $excelSheetName = ucwords(str_replace(' ', '', 'CheckinCheckoutExcel-')) . $this->request->slug . '-' . date('Y-m-d');
            // $excelSheetName = ucwords(str_replace(' ', '', 'Checkin Checkout ')) . date('m-d-Y');
            $excelSheetName =  "Checkin-Checkout-Summary-Transients-" . date("jS M, Y", strtotime($this->request->from_date)) . ' - ' . date("jS M, Y", strtotime($this->request->to_date));
            $finalCodes1 = [];
            $finalCodes2 = [];
            $finalCodes3 = [];
            $finalCodes4 = [];
            $finalCodes5 = [];
            $mailSummary = [];
            $permitFinalResult = [];
            $finalRowIncrement = 1;
            $increment1 = 1;
            $increment2 = 1;
            $increment3 = 1;
            $increment4 = 1;
            $increment5 = 1;

            $total_amount = 0;
            $total_validate_amount = 0;
            $total_parking_amount = 0;
            $total_amount_paid = 0;
            $total_processfee = 0;
            $total_net_amount = 0;
            $total_payable = 0;
            $total_tax_fee = 0;
            $total_cc_fee = 0;
            $total_city_surcharge = 0;
            $total_discount_amount = 0;
            $totaldiscoumtvalidateamount = 0;
            $TotalticketAmount = 0;

            $locationName = 'All';
            $garageCode = 'All';
            $brandSetting = null;
            $color = "#0C4A7A";
            $checkincheckout = [];
            $driveupTicets = [];
            $ticketOffPayment = [];
            $checkinAgaintPassTickets = [];
            $openTicketListArray = [];
            $driveupTicetsArray = [];
            $ticketOffPayment = [];
            $openTicketList = [];
            $finalCodes1 = [];
            $finalCodes2 = [];
            $finalCodes3 = [];
            $finalCodes4 = [];
            $finalCodes5 = [];
            $permitFinalResult = [];
            $passFinalResult = [];
            $passesFinalResult = [];

            if (isset($this->request->facility_id) && !empty($this->request->facility_id)) {
                $facility = Facility::find($this->request->facility_id);
                $locationName = $facility->full_name;
                $garageCode = $facility->garage_code;
            }

            $this->successLog->info('ticketdriveup Start SLUG  : ');
            $this->successLog->info(' SLUG  : ' . $this->request->slug);
            $this->successLog->info('ticketdriveup Start SLUG 11 : ');
            $brandSetting = BrandSetting::where('user_id', $this->request->partner_id)->first();
            if (!empty($brandSetting)) {
                $color = $brandSetting->color;
            }
            $this->dateTimeRange = date("jS M, Y", strtotime($this->request->from_date)) . ' - ' . date("jS M, Y", strtotime($this->request->to_date));
            $from_date = date("Y-m-d", strtotime($this->request->from_date));
            $to_date = date("Y-m-d", strtotime($this->request->to_date));
            if ($this->request->slug == self::CHECKIN) {
                $this->successLog->info('checkin test');
                $checkincheckout = $this->getSqlQuery();
                $mailSummary['Check-in against Booking'] = count($checkincheckout);
                // $this->successLog->info('ticketdriveup Start SLUG 11 : ' . json_encode($checkincheckout));
                foreach ($checkincheckout as $val) {
                    $this->successLog->info('checkincheckout ');
                    if (isset($val->userPass)) {
                        $pass_code = $val->userPass->pass_code;
                    } else {
                        $pass_code = $val->pass_code;
                    }

                    $finalCodes1[] = [
                        'No.' => $increment1,
                        'Ticket' => isset($val->ticket_number) ? $val->ticket_number : '-',
                        'License Plate' => isset($val->license_plate) ? $val->license_plate : '-',
                        'Facility Name' => isset($val->full_name) ? $val->full_name : '-',
                        'Booking Id' => (isset($val->ticketech_code) && !empty($val->ticketech_code)) ? $val->ticketech_code : '-',
                        'Check-In At' => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
                        'Check-Out At' => isset($val->checkout_time) ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '-',
                        'Booking Against Pass' => isset($val->pass_code) ? 'Yes' : 'NA',
                        'Pass ID' => isset($pass_code) ? $pass_code : '-',
                        'Pass Rate ($)' => isset($val->pass_total) ? $val->pass_total : '-',
                        'Pass Days' => isset($val->total_days) ? $val->total_days : '-',
                        'Price ($)' => isset($val->total) ? floatval($val->total) : '0.00',
                        'Overstay' => ($val->is_overstay == '1') ? 'Yes' : 'No',
                        'Email' => (isset($val->email) && !empty($val->email)) ? $val->email : '-',
                        'Phone' => (isset($val->phone) && !empty($val->phone)) ? $val->phone : '-',
                    ];

                    $increment1++;
                }
            } else if ($this->request->slug == self::DRIVEUP) {

                $this->successLog->info('ticketdriveup Start SLUG 22 : ');
                $driveupTicetsArray =  $this->getSqlQuery();
                $driveupTicets = (object) $driveupTicetsArray;
                $mailSummary['Transient Checkins'] = count($driveupTicetsArray);
                $this->successLog->info('ticketdriveup Result test : ' . json_encode($driveupTicets));
                $total_discount_amount = 0;
                $total_cc_fee = 0;
                $total_city_surcharge = 0;
                $total_tax = 0;
                $total_discount_amount = 0;
                $total_cc_fee = 0;
                $total_city_surcharge = 0;
                $total_surcharge = 0;
                $passFinalResult = [];
                // echo "OK";
                // foreach ($driveupTicets as $ticket) {
                //     $this->successLog->info('ticketdriveup Start SLUG 22 : ');
                //     // ************** @ vijay sir staging code **************
                //     // $net_amount = $tickeAmount  = 0;
                //     // if ($ticket->is_checkout == 0) {
                //     //     $ticketObj = Ticket::find($ticket->id);
                //     //     $arrival_time = $ticket->checkin_time;
                //     //     $diff_in_hour = $ticketObj->getCheckOutCurrentTime(true);

                //     //     if ($ticketObj->facility->rate_duration_in_hours > 0 && $ticketObj->facility->rate_per_hour > 0 && $ticketObj->facility->rate_free_minutes > 0 && $ticketObj->facility->rate_daily_max_amount > 0) {
                //     //         $rate = $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hour, false, false, null, false, false, '0', '0');
                //     //     } else {
                //     //         $rate = $ticketObj->facility->rateForReservationOnMarker($arrival_time, $diff_in_hour, false, false, false, true, false, 0, '0');
                //     //     }
                //     //     // $rate['price']  = 0;
                //     //     $parkingAmount = $rate['price'];
                //     //     $taxFee = $ticketObj->facility->getTaxRate($rate);
                //     //     $processingFee = $ticketObj->facility->getProcessingFee($rate);
                //     //     $tickeAmount = $parkingAmount + $taxFee + $processingFee;
                //     // }
                //     //  *************** @ vijay sir preprod code
                //     $this->successLog->info('ticketdriveup Start SLUG 22 : ');
                //     $net_amount = $tickeAmount  = 0;
                //     if ($ticket->is_checkout == 0) {
                //         $tickeAmount = $this->calculatePayablePrice($ticket);
                //     }
                //     $overstayPrice = 0;
                //     if ($ticket->is_overstay == '1') {
                //         $overstayPrice = DB::table('overstay_tickets')->where('ticket_id', $ticket->id)->sum('grand_total');

                //         $overstayProcessingTax = DB::table('overstay_tickets')
                //             ->where('ticket_id', $ticket->id)
                //             ->selectRaw('SUM(processing_fee) as processing_fee, SUM(tax_fee) as tax_fee')
                //             ->first();
                //     } elseif ($ticket->is_extended == '1') {
                //         $overstayPrice = DB::table('ticket_extends')->where('ticket_id', $ticket->id)->sum('grand_total');

                //         $overstayProcessingTax = DB::table('ticket_extends')
                //             ->where('ticket_id', $ticket->id)
                //             ->selectRaw('SUM(processing_fee) as processing_fee, SUM(tax_fee) as tax_fee, SUM(additional_fee) as total_additional_fee, SUM(surcharge_fee) as total_surcharge_fee')
                //             ->first();

                //         $this->successLog->info('overstayPrice Ticket Number ' . $ticket->ticket_number . ' : ' . json_encode($overstayPrice));
                //     }
                //     $ticket->grand_total = $ticket->grand_total + $overstayPrice;
                //     $ticket->total = $ticket->total + $overstayPrice;
                //     $ticket->total_tax_fee = $ticket->tax_fee;
                //     $ticket->total_additional_fee = $ticket->additional_fee;
                //     $ticket->total_surcharge_fee = $ticket->surcharge_fee;

                //     $netAmount = ($ticket->total) - ($ticket->processing_fee + $ticket->tax_fee);
                //     if (isset($overstayProcessingTax) && !empty($overstayProcessingTax)) {
                //         $netAmount = ($netAmount + $overstayPrice) - ($overstayProcessingTax->processing_fee + $overstayProcessingTax->tax_fee);
                //     }

                //     $finalCodes3[] = [
                //         'No.' => $increment3,
                //         'Ticket' => isset($ticket->ticket_number) ? $ticket->ticket_number : '-',
                //         'License Plate' => isset($ticket->license_plate) ? $ticket->license_plate : '-',
                //         'Facility Name' => isset($ticket->full_name) ? $ticket->full_name : '-',
                //         'Check-In At' => date("m/d/Y, h:i A", strtotime($ticket->checkin_time)),
                //         'Check-Out At' => isset($ticket->checkout_time) ? date("m/d/Y, h:i A", strtotime($ticket->checkout_time)) : '-',
                //         'Card No' => (isset($ticket->card_last_four) && !empty($ticket->card_last_four)) ? $ticket->card_last_four : '-',
                //         'Card Type' => isset($ticket->card_type) ? $ticket->card_type : '-',
                //         'Card Expiry' => (isset($ticket->expiry) && !empty($ticket->expiry)) ? $ticket->expiry : '-',
                //         'Total Amount ($)' => isset($ticket->total) ? floatval($ticket->total) : 0.00,
                //         'Parking Amount ($)' => isset($ticket->parking_amount) ? floatval($ticket->parking_amount) : 0.00,
                //         'Validated Amount ($)' => isset($ticket->paid_amount) ? floatval($ticket->paid_amount) : 0.00,
                //         'Payable Amount ($)' => isset($tickeAmount) ? floatval($tickeAmount) : 0.00,
                //         'Amount Paid ($)' => isset($ticket->grand_total) ? floatval($ticket->grand_total) : 0.00,
                //         'Processing Fee ($)' => isset($ticket->processing_fee) ? floatval($ticket->processing_fee) : 0.00,
                //         'Net Amount ($)' => isset($netAmount) ? floatval($netAmount) : $net_amount,
                //         'Policy Name' => (isset($ticket->policy_name) && !empty($ticket->policy_name)) ? $ticket->policy_name : '-',
                //         'Validation Remarks' => (isset($ticket->paid_remark) && !empty($ticket->paid_remark)) ? $ticket->paid_remark : '-',
                //         'Tax ($)' => isset($ticket->tax_fee) ? $ticket->tax_fee + @$ticket->total_tax_fee : '-',
                //         'Promocode' => isset($ticket->promocode) ? $ticket->promocode : '-',
                //         'Discount Amount ($)' =>  isset($ticket->discount_amount) ? $ticket->discount_amount : '-'
                //     ];

                //       // Conditionally add CC Fee and City Surcharge
                //       if ($this->request->partner_id == $this->revpassPartnerId) {
                //         $finalCodes3[count($finalCodes3) - 1]['CC Fee ($)'] = isset($ticket->additional_fee) ? $ticket->additional_fee : '-';
                //         $finalCodes3[count($finalCodes3) - 1]['City Surcharge ($)'] = isset($ticket->surcharge_fee) ? $ticket->surcharge_fee : '-';
                //     }

                //     $total_amount =  $total_amount + $ticket->total;
                //     $total_parking_amount = $total_parking_amount + $ticket->parking_amount;
                //     $total_validate_amount = $total_validate_amount + $ticket->paid_amount;
                //     // $total_payable += $tickeAmount;
                //     $total_amount_paid = $total_amount_paid + $ticket->grand_total;
                //     $total_processfee = $total_processfee + $ticket->processing_fee;
                //     $total_net_amount = $total_net_amount + ($ticket->grand_total) - ($ticket->processing_fee - $ticket->tax_fee);

                //     $total_tax_fee = $total_tax_fee + $ticket->tax_fee + $ticket->total_tax_fee;
                //     $total_discount_amount = $total_discount_amount + $ticket->discount_amount;

                //     $total_cc_fee = $total_cc_fee + $ticket->additional_fee + @$ticket->total_additional_fee;
                //     $total_city_surcharge = $total_city_surcharge + $ticket->surcharge_fee + @$ticket->total_surcharge_fee;

                //     $increment3++;
                // }
                $ticketNumbers = collect($driveupTicetsArray)->pluck('ticket_number');

                // Fetch related overstay and extend data once for all ticket numbers
                $overstayData = OverstayTicket::whereIn('ticket_number', $ticketNumbers)
                    ->selectRaw('ticket_number, SUM(grand_total) as grand_total, SUM(total) as total')
                    ->groupBy('ticket_number')
                    ->get()
                    ->keyBy('ticket_number');

                $extendData = TicketExtend::whereIn('ticket_number', $ticketNumbers)
                    ->selectRaw('ticket_number, SUM(grand_total) as grand_total, SUM(total) as total, SUM(tax_fee) as total_tax_fee, 
                                    SUM(discount_amount) as total_discount_amount, SUM(additional_fee) as total_additional_fee, SUM(processing_fee) as total_processing_fee,  
                                    SUM(surcharge_fee) as total_surcharge_fee')
                    ->groupBy('ticket_number')
                    ->get()
                    ->keyBy('ticket_number');
                $ticketExtendSums = TicketExtend::whereIn('ticket_number', $ticketNumbers)
                    ->selectRaw('ticket_number, SUM(total) as extend_total')
                    ->groupBy('ticket_number')
                    ->pluck('extend_total', 'ticket_number')
                    ->toArray(); // Converts to key-value array for fast lookup

                $tickettimezonedata = $this->preloadTimezonesForTickets(collect($driveupTicetsArray));

                foreach ($driveupTicets as $val) {
                    set_time_limit(0);
                    // if ($val->is_checkout == '0' && $val->checkout_time == '') {
                    //     $tickeAmount = $this->calculatePayablePrice($val);;
                    // } else {
                    //     $tickeAmount = '0.0';
                    // }
                    // Calulate Amount Due or Payable !! Start here : 16-01-2025
                    $ticketObj = Ticket::with([
                        'transaction' => function ($query) {
                            $query->select('id', 'method', 'payment_last_four', 'expiration', 'anet_trans_id');
                        },
                        'preAuthData' => function ($query) {
                            $query->select('id', 'ticket_id', 'trans_id', 'ref_id'); // include ticket_id for relation
                        },
                        'facility' => function ($query) {
                            $query->select('id', 'full_name', 'rate_duration_in_hours', 'rate_per_hour', 'rate_free_minutes', 'rate_daily_max_amount')
                                ->with(['FacilityPaymentDetails' => function ($q) {
                                    $q->select('id', 'facility_id', 'facility_payment_type_id');
                                }]);
                        }
                    ])->where('id', $val->id)->first();
                    $tickeAmount = '0.00';
                    // $payablePrice =   $this->calculatePayablePrice($val);
                    $payablePrice =   0;
                    $payableset = 0;
                    $this->successLog->info("DRIVE UP  : Ticket Number : {$val->ticket_number} , Payable : {$payablePrice}");
                    if ($ticketObj->facility->is_gated_facility == '1') { // gated 
                        if ($val->is_checkout == '0' && $val->checkout_time == '') {
                            $tickeAmount = $payablePrice;
                            $payableset = 1;
                        }
                    } else {   // ungated 
                        if ($ticketObj->is_checkout == '0') {
                            if (!is_null($ticketObj->event_user_id)) {
                                $tickeAmount = '0.00';
                            } else {
                                $payablePrice       =   $this->transientcalculatePayablePrice($val, $tickettimezonedata);
                                $tickeAmount        =  $payablePrice;
                                $payableset = 1;
                                $val->grand_total   =   '0.00';
                            }

                            if ($ticketObj->is_closed === '1' || $ticketObj->is_checkout == '1') {
                                $tickeAmount = '0.00';
                                if ($ticketObj->discount_amount > 0 || $ticketObj->is_extended == '1' || $ticketObj->paid_amount > 0) {
                                    $this->successLog->info("discountprice : {$val->ticket_number} , Payable : {$ticketObj->discount_amount}");
                                    $val->grand_total = $this->transientcalculatePayablePrice($val, $tickettimezonedata);
                                    $this->successLog->info("discountprice : {$val->grand_total}");
                                }
                            }
                        } elseif (!empty($ticketObj->payment_date) || $ticketObj->is_checkout == '1') {
                            $tickeAmount = '0.00';
                            if ($ticketObj->discount_amount > 0 || $ticketObj->is_extended == '1' || $ticketObj->paid_amount > 0) {
                                $this->successLog->info("discountprice : {$val->ticket_number} , Payable : {$ticketObj->discount_amount}");
                                $val->grand_total = $this->transientcalculatePayablePrice($val, $tickettimezonedata);
                                $this->successLog->info("discountprice : {$val->grand_total}");
                            }
                        }
                    }
                    // !! Close Here !!!

                    if (isset($ticketObj->card_last_four) && $ticketObj->card_last_four != '') {
                        $card_no = $ticketObj->card_last_four;
                    } elseif (isset($ticketObj->transaction['payment_last_four']) && $ticketObj->transaction['payment_last_four'] != '') {
                        $card_no = $ticketObj->transaction['payment_last_four'];
                    } else {
                        $card_no = '';
                    }

                    if (isset($ticketObj->card_type) && $ticketObj->card_type != '') {
                        $card_type = $ticketObj->card_type;
                    } elseif (isset($ticketObj->transaction['card_type']) && $ticketObj->transaction['card_type'] != '') {
                        $card_type = $ticketObj->transaction['card_type'];
                    } else {
                        $card_type = '';
                    }

                    if (isset($ticketObj->expiry) && $ticketObj->expiry != '') {
                        $output = str_split($ticketObj->expiry, 2);

                        if (isset($output[1])) {
                            $card_expiry = $output[0] . "/" . $output[1];
                        }
                    } elseif (isset($val->transaction['expiration']) && $val->transaction['expiration'] != '') {
                        $output = str_split($val->transaction['expiration'], 2);
                        if (isset($output[1])) {
                            $card_expiry = $output[0] . "/" . $output[1];
                        }
                    } else {
                        $card_expiry = '';
                    }

                    $overstayTotal = $overstayGrandTotal = $extendGrandTotal = $extendTotal = $extendparkingAmount = $extendProcessingFee = $extendtaxFee = 0;
                    $val->total_tax_fee = $ticketObj->total_tax_fee;
                    $val->total_discount_amount = $ticketObj->total_discount_amount;
                    $val->total_additional_fee = $ticketObj->total_additional_fee;
                    $val->total_surcharge_fee = $ticketObj->total_surcharge_fee;
                    $val->total_processing_fee = 0;

                    $overstaySums = $overstayData[$val->ticket_number] ?? null;
                    if ($ticketObj->is_overstay == '1' && $overstaySums) {
                        $val->total += $overstaySums->total ?? 0;
                        $val->grand_total += $overstaySums->grand_total ?? 0;
                    } elseif ($ticketObj->is_extended == '1') {

                        $extendSums = $extendData[$val->ticket_number] ?? null;
                        if ($ticketObj->is_extended == '1' && $extendSums) {
                            // $val->grand_total += $extendSums->grand_total ?? 0;
                            // $val->total += $extendSums->total ?? 0;
                            $val->total_tax_fee += $extendSums->total_tax_fee ?? 0;
                            $val->total_discount_amount += $extendSums->total_discount_amount ?? 0;
                            $val->total_processing_fee = $extendSums->total_processing_fee ?? 0;
                            $val->total_additional_fee += $extendSums->total_additional_fee ?? 0;
                            $val->total_surcharge_fee += $extendSums->total_surcharge_fee ?? 0;
                        }

                        $extendTotal = $ticketExtendSums[$val->ticket_number] ?? 0;
                        $val->total += $extendTotal > 0 ? $extendTotal : 0;

                        $extendparkingAmount = ($extendTotal - ($val->total_processing_fee + $val->total_additional_fee + $val->total_surcharge_fee + $val->total_tax_fee));
                    }

                    $val->ticketadditionalinfo = $ticketObj->ticketadditionalinfo() ?? null;

                    // net amount
                    // $net_amount = ($val->grand_total > 0) ? max(0.00, round($val->grand_total - $val->processing_fee - $val->tax_fee, 2)) : 0.00;

                    $processingFee = round(
                        floatval($val->processing_fee ?? 0) +
                            floatval($val->total_processing_fee ?? 0) +
                            floatval($val->total_additional_fee ?? 0) +
                            floatval($val->additional_fee ?? 0),
                        2
                    );

                    $taxFee = round(
                        floatval($val->tax_fee ?? 0) +
                            floatval($val->total_tax_fee ?? 0),
                        2
                    );
                    $citysurcharge = round(
                        floatval($val->surcharge_fee  ?? 0) +
                            floatval($val->total_surcharge_fee ?? 0),
                        2
                    );

                    if (isset(($val->ticketadditionalinfo)->new_discount_amount)) {
                        $discountFee = round(
                            (
                                isset(($val->ticketadditionalinfo)->new_discount_amount)
                                ? floatval(($val->ticketadditionalinfo)->new_discount_amount)
                                : (isset($val->discount_amount)
                                    ? floatval($val->discount_amount)
                                    : 0)
                            ),
                            2
                        );

                        $net_amount = $val->ticketadditionalinfo->new_parking_amount;
                    } else {
                        $discountFee = round(floatval($val->discount_amount ?? 0) + floatval($val->total_discount_amount ?? 0), 2);
                        $net_amount = ($val->grand_total > 0) ? max(0.00, round($val->grand_total - $processingFee - $taxFee - $citysurcharge, 2)) : 0.00;
                    }

                    $totalAmount = round(floatval($val->total ?? 0), 2);

                    if (isset($val->promocode) && $val->promocode != '') {
                        $isPromoApplied = false;
                    } else {
                        $isPromoApplied = false;
                    }

                    $newdataparkingAmount = isset($val->parking_amount)
                        ? round(floatval($val->parking_amount + $extendparkingAmount), 2)
                        : 0.00;

                    $newdatadiscountAmount = isset($val->paid_amount)
                        ? round(floatval($val->paid_amount), 2)
                        : (
                            ($tickeAmount == 0 && $isPromoApplied)
                            ? round($discountFee - ($processingFee + $taxFee + $citysurcharge), 2)
                            : round($discountFee, 2)
                        );

                    $newdataprocessingFee = ($tickeAmount == 0 && $isPromoApplied)
                        ? 0.00
                        : round(
                            (
                                isset(($val->ticketadditionalinfo)->new_processing_fee)
                                ? floatval(($val->ticketadditionalinfo)->new_processing_fee)
                                : (isset($val->processing_fee)
                                    ? floatval($val->processing_fee)
                                    : 0)
                            ) +
                                floatval($val->total_processing_fee ?? 0) +
                                floatval($val->total_additional_fee ?? 0) +
                                floatval($val->additional_fee ?? 0),
                            2
                        );

                    if (isset($val->ticketadditionalinfo->new_tax_amount)) {
                        $newdatatax = ($tickeAmount == 0 && $isPromoApplied)
                            ? 0.00
                            : round(
                                floatval($val->ticketadditionalinfo->new_tax_amount ?? $val->tax_fee ?? 0),
                                2
                            );
                    } else {
                        $newdatatax = ($tickeAmount == 0 && $isPromoApplied)
                            ? 0.00
                            : round(
                                floatval($val->tax_fee ?? 0) + floatval($val->total_tax_fee ?? 0),
                                2
                            );
                    }


                    $olddataprocessingFee = ($tickeAmount == 0 && $isPromoApplied)
                        ? 0.00
                        : round(
                            (isset($val->processing_fee)
                                ? floatval($val->processing_fee)
                                : 0)
                                +
                                floatval($val->total_processing_fee ?? 0) +
                                floatval($val->total_additional_fee ?? 0) +
                                floatval($val->additional_fee ?? 0),
                            2
                        );

                    $olddatatax = ($tickeAmount == 0 && $isPromoApplied)
                        ? 0.00
                        : round(
                            floatval($val->tax_fee ?? 0) +
                                floatval($val->total_tax_fee ?? 0),
                            2
                        );

                    // Total
                    $newdatatotalAmount = round($newdataparkingAmount  + $newdataprocessingFee + $newdatatax, 2);

                    if (isset($val->ticketadditionalinfo->new_tax_amount)) {
                        if ($payableset) {
                            $tickeAmount  = $val->ticketadditionalinfo->new_parking_amount + $val->ticketadditionalinfo->new_processing_fee + $val->ticketadditionalinfo->new_tax_amount;
                            $net_amount = $val->ticketadditionalinfo->new_parking_amount;
                        } else {
                            $val->grand_total = $val->ticketadditionalinfo->new_parking_amount + $val->ticketadditionalinfo->new_processing_fee + $val->ticketadditionalinfo->new_tax_amount;
                        }
                    } else {
                        if ($payableset) {
                            $net_amount = ($tickeAmount > 0) ? max(0.00, round($tickeAmount - $processingFee - $taxFee - $citysurcharge, 2)) : 0.00;
                        }
                    }

                    if (($newdatatotalAmount == $newdatadiscountAmount) &&  empty($val->ticketadditionalinfo)) {
                        $newdatatotalAmount = $newdataparkingAmount;
                        $newdatadiscountAmount = $newdataparkingAmount;
                        $newdataprocessingFee = "0.00";
                    }

                    $entry = [
                        'No.' => $increment3,
                        'Ticket' => isset($val->ticket_number) ? $val->ticket_number : '-',
                        'Transaction ID' => $ticketObj->is_checkout == '0'
                            ? (
                                isset($ticketObj->facility->FacilityPaymentDetails) && $ticketObj->facility->FacilityPaymentDetails->facility_payment_type_id == 2
                                // ? (isset($val->preAuthData->ref_id) ? $val->preAuthData->ref_id : '-')
                                ? '-'
                                : (
                                    isset($ticketObj->facility->FacilityPaymentDetails) && $ticketObj->facility->FacilityPaymentDetails->facility_payment_type_id == 4
                                    // ? (isset($val->preAuthData->trans_id) ? $val->preAuthData->trans_id : '-')
                                    ? '-'
                                    : '-'
                                )
                            )
                            : (isset($ticketObj->transaction) && isset($ticketObj->transaction->anet_trans_id) ? $ticketObj->transaction->anet_trans_id : '-'),
                        'License Plate' => isset($val->license_plate) ? $val->license_plate : '-',
                        'Facility Name' => isset($val->full_name) ? $val->full_name : '-',
                        'Check-In At' => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
                        #KT | PIMS-13894
                        // 'Check-Out At' => isset($val->estimated_checkout) ? date("m/d/Y, h:i A", strtotime($val->estimated_checkout)) : '-',
                        'Check-Out At' => isset($val->facility->is_gated_facility) && $val->facility->is_gated_facility == 0
                            ? (isset($val->estimated_checkout) ? date("m/d/Y, h:i A", strtotime($val->estimated_checkout)) : '-')
                            : (isset($val->checkout_time) ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '-'),
                        'Check-In Mode' => isset($val->device_type) ? $val->device_type : '-',
                        'Card No' => (isset($card_no) && !empty($card_no)) ? $card_no : '-',
                        'Card Type' => isset($val->card_type) ? $val->card_type : '-',
                        'Card Expiry' => (isset($card_expiry) && !empty($card_no)) ? $card_expiry : '-',
                        // 'Total Amount ($)' => ($tickeAmount == 0 && $isPromoApplied)
                        //     ? round($totalAmount - ($processingFee + $taxFee + $citysurcharge), 2)
                        //     : $totalAmount,
                        // 'Parking Amount ($)' => isset($val->parking_amount)
                        //     ? round(floatval($val->parking_amount + $extendparkingAmount), 2)
                        //     : 0.00,

                        // 'Processing Fee ($)' => ($tickeAmount == 0 && $isPromoApplied)
                        //     ? 0.00
                        //     : round(
                        //         floatval($val->processing_fee ?? 0) +
                        //             floatval($val->total_processing_fee ?? 0) +
                        //             floatval($val->total_additional_fee ?? 0) +
                        //             floatval($val->additional_fee ?? 0),
                        //         2
                        //     ),

                        // 'Tax ($)' => ($tickeAmount == 0 && $isPromoApplied)
                        //     ? 0.00
                        //     : round(
                        //         floatval($val->tax_fee ?? 0) +
                        //             floatval($val->total_tax_fee ?? 0),
                        //         2
                        //     ),
                        'Amount Paid ($)' => isset($val->grand_total) ? round(floatval($val->grand_total), 2) : round(floatval(0.00), 2),
                        // 'Total Amount ($)' => ($tickeAmount == 0 && $isPromoApplied)
                        //     ? round($totalAmount - ($processingFee + $taxFee + $citysurcharge), 2)
                        //     : $totalAmount,
                        'Payable Amount ($)' => isset($tickeAmount) ? round(floatval($tickeAmount), 2) : round(floatval(0.00), 2),
                        // 'Parking Amount ($)' => isset($val->parking_amount)
                        //     ? round(floatval($val->parking_amount + $extendparkingAmount), 2)
                        //     : 0.00,
                        'Parking Amount ($)'  => $newdataparkingAmount,
                        'Discount Amount ($)' => $newdatadiscountAmount,
                        'Processing Fee ($)' =>  $newdataprocessingFee,
                        'Tax ($)' => $newdatatax,
                        // 'Processing Fee ($)' => round(floatval($val->processing_fee ?? 0) + floatval($val->total_processing_fee ?? 0) + floatval($val->total_additional_fee ?? 0) + floatval($val->additional_fee ?? 0), 2),
                        // 'Tax ($)' => isset($val->tax_fee) ? round(floatval($val->tax_fee) + @$val->total_tax_fee, 2) : round(floatval(0.00), 2),

                    ];

                    // Add 'City Surcharge ($)' only if partner ID matches
                    if ($this->request->partner_id == $this->revpassPartnerId) {
                        // $entry['City Surcharge ($)'] = isset($ticketObj->surcharge_fee)
                        //     ? round(floatval($citysurcharge), 2)
                        //     : floatval(0.00);

                        $entry['City Surcharge ($)'] = ($tickeAmount == 0 && $isPromoApplied)
                            ? 0.00
                            : round(
                                floatval($val->surcharge_fee ?? 0) + floatval($val->total_surcharge_fee ?? 0),
                                2
                            );

                        $total_surcharge += ($tickeAmount == 0 && $isPromoApplied)
                            ? 0.00
                            : round(
                                floatval($val->surcharge_fee ?? 0) + floatval($val->total_surcharge_fee ?? 0),
                                2
                            );
                    }

                    // Add remaining columns
                    $entry += [
                        // 'Validated Amount ($)' => isset($val->paid_amount) ? round(floatval($val->paid_amount), 2) : round(floatval(0.00), 2),
                        // 'Payable Amount ($)' => isset($tickeAmount) ? round(floatval($tickeAmount), 2) : round(floatval(0.00), 2),
                        // 'Amount Paid ($)' => isset($val->grand_total) ? round(floatval($val->grand_total), 2) : round(floatval(0.00), 2),
                        // 'Net Amount ($)' => isset($net_amount) ? round(floatval($net_amount), 2) : round(floatval(0.00), 2),
                        // 'Promocode' => isset($ticketObj->promocode) ? $ticketObj->promocode : '-',
                        // 'Policy Name' => (isset($val->policy_name) && !empty($val->policy_name)) ? $val->policy_name : '-',
                        // 'Validation Remarks' => (isset($val->paid_remark) && !empty($val->paid_remark)) ? $val->paid_remark : '-',
                        // 'Discount Amount ($)' => ($tickeAmount == 0 && $isPromoApplied)
                        //     ? round($discountFee - ($processingFee + $taxFee + $citysurcharge), 2)
                        //     : $discountFee,
                        'Ticket Amount' => $newdatatotalAmount,
                        'Net Amount ($)' => isset($net_amount) ? round(floatval($net_amount), 2) : round(floatval(0.00), 2),
                        'Promocode' => isset($val->promocode) ? $val->promocode : '-',
                        'Policy Name' => (isset($val->policy_name) && !empty($val->policy_name)) ? $val->policy_name : '-',
                        'Validation Remarks' => (isset($val->paid_remark) && !empty($val->paid_remark)) ? $val->paid_remark : '-',
                    ];

                    // Add the complete entry to $finalCodes3
                    $finalCodes3[] = $entry;

                    // Conditionally add CC Fee and City Surcharge
                    // if ($this->request->partner_id == $this->revpassPartnerId) {
                    //     $finalCodes3[count($finalCodes3) - 1]['CC Fee ($)'] = isset($val->additional_fee) ? $val->additional_fee + @$val->total_additional_fee : '-';
                    //     $finalCodes3[count($finalCodes3) - 1]['City Surcharge ($)'] = isset($val->surcharge_fee) ? $val->surcharge_fee + @$val->total_surcharge_fee : '-';
                    // }


                    // $totaldiscoumtvalidateamount += $discountAmount = isset($val->paid_amount)
                    // ? round(floatval($val->paid_amount), 2)
                    // : (
                    //     ($tickeAmount == 0 && $isPromoApplied)
                    //         ? round($discountFee - ($processingFee + $taxFee + $citysurcharge), 2)
                    //         : round($discountFee, 2)
                    // );
                    $totaldiscoumtvalidateamount += $newdatadiscountAmount;

                    // Calculate Ticket Amount first
                    $TotalticketAmount += $newdatatotalAmount;

                    $total_amount += ($tickeAmount == 0 && $isPromoApplied)
                        ? round($totalAmount - ($processingFee + $taxFee + $citysurcharge), 2)
                        : $totalAmount;

                    $total_parking_amount += isset($val->parking_amount) ? round(floatval($val->parking_amount + $extendparkingAmount), 2) : round(floatval(0.00), 2);
                    $total_validate_amount += isset($val->paid_amount) ? round(floatval($val->paid_amount), 2) : round(floatval(0.00), 2);
                    $total_payable += isset($tickeAmount) ? round(floatval($tickeAmount), 2) : round(floatval(0.00), 2);
                    $total_amount_paid += isset($val->grand_total) ? round(floatval($val->grand_total), 2) : round(floatval(0.00), 2);
                    // $total_processfee +=
                    //     ($tickeAmount == 0 && $isPromoApplied)
                    //     ? 0.00
                    //     : round(
                    //         floatval($val->processing_fee ?? 0) +
                    //             floatval($val->total_processing_fee ?? 0) +
                    //             floatval($val->total_additional_fee ?? 0) +
                    //             floatval($val->additional_fee ?? 0),
                    //         2
                    //     );

                    // $total_processfee +=
                    // ($tickeAmount == 0 && $isPromoApplied)

                    // ? 0.00

                    // : round(

                    //     (

                    //         isset(($val->ticketadditionalinfo)->new_processing_fee) 

                    //             ? floatval(($val->ticketadditionalinfo)->new_processing_fee)

                    //             : (isset($val->processing_fee) 

                    //                 ? floatval($val->processing_fee) 

                    //                 : 0)

                    //     )  +
                    //             floatval($val->total_processing_fee ?? 0) +
                    //             floatval($val->total_additional_fee ?? 0) +
                    //             floatval($val->additional_fee ?? 0),
                    //         2
                    //     );
                    $total_processfee += $newdataprocessingFee;
                    $total_net_amount +=  isset($net_amount) ? floatval($net_amount) : floatval(0.00);
                    $total_tax += $newdatatax;
                    // $total_discount_amount += ($tickeAmount == 0 && $isPromoApplied)
                    //     ? round($discountFee - ($processingFee + $taxFee + $citysurcharge), 2)
                    //     : $discountFee;
                    // $total_tax += ($tickeAmount == 0 && $isPromoApplied)
                    //     ? 0.00
                    //     : round(
                    //         floatval($val->tax_fee ?? 0) +
                    //             floatval($val->total_tax_fee ?? 0),
                    //         2
                    //     );
                    // $total_discount_amount += ($tickeAmount == 0 && $isPromoApplied)
                    //     ? round($discountFee - ($processingFee + $taxFee + $citysurcharge), 2)
                    //     : $discountFee;
                    if ($this->request->partner_id == $this->revpassPartnerId) {
                        $total_cc_fee = $total_cc_fee + $val->additional_fee + @$val->total_additional_fee;
                        $total_city_surcharge = $total_city_surcharge + $val->surcharge_fee + @$val->total_surcharge_fee;
                    }

                    $increment3++;
                }
                // dd($finalCodes3);

                // show total payable for row created
                $totalentry = [
                    'No.' => '',
                    'Ticket' => '',
                    'Transaction ID' => '',
                    'License Plate' => '',
                    'Facility Name' => '',
                    'Check-In At' => '',
                    'Check-Out At' => '',
                    'Check-In Mode' => '',
                    'Card No' => '',
                    'Card Type' => '',
                    'Card Expiry' => '',
                    'Amount Paid' => '',
                    'Payable Amount' => '',
                    // 'Total Amount ($)' => '',
                    'Parking Amount ($)' => '',
                    'Discount Amount ($)' => '',
                    'Processing Fee ($)' => '',
                    'Tax ($)' => '',
                    // 'Total Amount ($)' => '',
                    // 'Parking Amount ($)' => '',
                    // 'Processing Fee ($)' => '',
                    // 'Tax ($)' => '',
                ];

                if ($this->request->partner_id == $this->revpassPartnerId) {
                    $totalentry['City Surcharge ($)'] = '';
                }

                $totalentry += [
                    'Ticket Amount' => '',
                    // 'Validated Amount' => '',
                    // 'Payable Amount' => '',
                    // 'Amount Paid' => '',
                    'Net Amount ($)' => '',
                    'Promocode' => '',
                    'Policy Name' => '',
                    'Validation Remarks' => '',
                ];

                $finalCodes3[] = $totalentry;

                $totalentryplus = [
                    'No.' => '',
                    'Ticket' => '',
                    'Transaction ID' => '',
                    'License Plate' => '',
                    'Facility Name' => '',
                    'Check-In At' => '',
                    'Check-Out At' => '',
                    'Check-In Mode' => '',
                    'Card No' => '',
                    'Card Type' => '',
                    'Card Expiry' => 'Total',
                    // 'Total Amount' => (float) number_format($total_amount, 2, '.', ''),
                    'Amount Paid' => (float) number_format($total_amount_paid, 2, '.', ''),
                    'Payable Amount' => (float) number_format($total_payable, 2, '.', ''),
                    'Parking Amount' => (float) number_format($total_parking_amount, 2, '.', ''),
                    'Discount Amount ($)' => (float) number_format($totaldiscoumtvalidateamount, 2, '.', ''),
                    'Processing Fee ($)' => (float) number_format($total_processfee, 2, '.', ''),
                    'Tax ($)' => (float) number_format($total_tax, 2, '.', ''),
                    // 'Total Amount' => (float) number_format($total_amount, 2, '.', ''),
                    // 'Parking Amount' => (float) number_format($total_parking_amount, 2, '.', ''),
                    // 'Processing Fee ($)' => (float) number_format($total_processfee, 2, '.', ''),
                    // 'Tax ($)' => (float) number_format($total_tax, 2, '.', ''),
                ];

                if ($this->request->partner_id == $this->revpassPartnerId) {
                    $totalentryplus['City Surcharge ($)'] = (float) number_format($total_city_surcharge, 2, '.', '');
                }

                $totalentryplus += [
                    'Ticket Amount' => (float) number_format($TotalticketAmount, 2, '.', ''),
                    // 'Validated Amount' => (float) number_format($total_validate_amount, 2, '.', ''),
                    // 'Payable Amount' => (float) number_format($total_payable, 2, '.', ''),
                    // 'Amount Paid' => (float) number_format($total_amount_paid, 2, '.', ''),
                    'Net Amount ($)' => (float) number_format($total_net_amount, 2, '.', ''),
                    'Promocode' => '',
                    'Policy Name' => '',
                    'Validation Remarks' => '',
                ];

                $finalCodes3[] = $totalentryplus;


                // $this->successLog->info('hit finalcodes3' );
                // // echo "OK";
                // $finalCodes3[] = [
                //     'No.' => '',
                //     'Ticket' => '',
                //     'License Plate' => '',
                //     'Facility Name' => '',
                //     'Check-In At' => '',
                //     'Check-Out At' => '',
                //     'Card No' => '',
                //     'Card Type' => '',
                //     'Card Expiry' => '',
                //     'Total Amount' => '',
                //     'Parking Amount' => '',
                //     'Validated Amount' => '',
                //     //'Payable Amount' => '', 
                //     'Amount Paid' => '',
                //     'Processing Fee' => '',
                //     'Net Amount' => '',
                //     'Policy Name' => '',
                //     'Validation Remarks' => '',
                //     'Tax' => '',
                //     'Promocode' => '',
                //     'Discount Amount' => '',


                // ];

                // $this->successLog->info('partnerid hit' );
                // if ($this->request->partner_id == $this->revpassPartnerId) {
                //     $finalCodes3[count($finalCodes3) - 1]['CC Fee ($)'] = '-';
                //     $finalCodes3[count($finalCodes3) - 1]['City Surcharge ($)'] = '-';
                // }

                // $this->successLog->info('finalcodes323' );

                // $finalCodes3[] = [
                //     'No.' => '',
                //     'Ticket' => '',
                //     'License Plate' => '',
                //     'Facility Name' => '',
                //     'Check-In At' => '',
                //     'Check-Out At' => '',
                //     'Card No' => '',
                //     'Card Type' => '',
                //     'Card Expiry' => 'Total',
                //     'Total Amount' => $total_amount,
                //     'Parking Amount' => $total_parking_amount,
                //     'Validated Amount' => $total_validate_amount,
                //     'Payable Amount' => $total_payable,
                //     'Amount Paid' => $total_amount_paid,
                //     'Processing Fee' => $total_processfee,
                //     'Net Amount' => $total_net_amount,
                //     'Policy Name' => '',
                //     'Validation Remarks' => '',
                //     'Tax' => $total_tax_fee,
                //     'Promocode' => '',
                //     'Discount Amount' => $total_discount_amount,

                // ];
                // $this->successLog->info('revepasspern' );
                // if ($this->request->partner_id == $this->revpassPartnerId) {
                //     $finalCodes3[count($finalCodes3) - 1]['CC Fee ($)'] = @$total_cc_fee;
                //     $finalCodes3[count($finalCodes3) - 1]['City Surcharge ($)'] = @$total_city_surcharge;
                // }
                // $this->successLog->info('revasdasepasspern' );
                // $this->successLog->info('Final Array  : ' . json_encode($finalCodes3));
            } else if ($this->request->slug == self::OFFPAYMENT) {
                $ticketOffPayment = $this->getSqlQuery();
                $mailSummary['Checkout from Admin'] = count($ticketOffPayment);
                // $this->successLog->info('ticketdriveup Start SLUG 11 : ' . json_encode($ticketOffPayment));

                foreach ($ticketOffPayment as $ticket) {
                    $this->successLog->info('ticketdriveup Start SLUG 11 : ticketOffPayment ');
                    //return $rec;
                    $finalCodes4[] = [
                        'No.' => $increment4,
                        'Facility Name' => isset($ticket->full_name) ? $ticket->full_name : '-',
                        'Ticket' => isset($ticket->ticket_number) ? $ticket->ticket_number : '-',
                        'License Plate' => $ticket->license_plate != '' ? $ticket->license_plate : '-',
                        'Check-In At' => date("m/d/Y, h:i A", strtotime($ticket->checkin_time)),
                        //'Check-Out At' => date("m/d/Y, h:i A", strtotime($val->checkout_time)),
                        'Check-Out At' => isset($ticket->checkout_time) ? date("m/d/Y, h:i A", strtotime($ticket->checkout_time)) : '-',
                        'Card No' => isset($ticket->card_last_four) && !empty($ticket->card_last_four)  ? $ticket->card_last_four : (!empty($ticket->payment_last_four) ? $ticket->payment_last_four : '-'),
                        'Card Type' => isset($ticket->card_type) && !empty($ticket->card_type) ? $ticket->card_type : (!empty($ticket->anet_card_type) ? $ticket->anet_card_type : '-'),
                        'Card Expiry' => isset($ticket->expiry) && !empty($ticket->expiry) ? substr($ticket->expiry, 0, 2) . '/' . substr($ticket->expiry, -2) : (!empty($ticket->expiration) ? substr($ticket->expiration, 0, 2) . '/' . substr($ticket->expiration, -2) : '-'),
                        'Payable Amount' => sprintf("%.2f", '0.00'),
                        'Validated Amount' => isset($ticket->paid_amount) ? sprintf("%.2f", $ticket->paid_amount) : sprintf("%.2f", '0.00'),
                        'Attendant Name' => isset($ticket->attendent_name) ? $ticket->attendent_name : '-',
                        'Comments' => isset($ticket->checkout_remark) ? $ticket->checkout_remark : '-',
                        'Overstay' => ($ticket->is_overstay == '1') ? 'Yes' : 'No',
                        'Reader Used' => (isset($ticket->reader_used) && !empty($ticket->reader_used)) ? $ticket->reader_used : '-',
                        //'Terminal Id' => isset($ticket->terminal_id']) ? $ticket->terminal_id']:'-',
                    ];
                    $increment4++;
                }
            } else if ($this->request->slug == self::THIRDPARTY) {
                $openTicketListArray = $this->getSqlQuery();
                $openTicketList = (object) $openTicketListArray;
                $mailSummary['Third Party Tickets'] = count($openTicketListArray);
                foreach ($openTicketList as $val) {

                    //rate find
                    $tickeAmount = $payable_amt = 0;
                    $isMember = 0;
                    if ($val->facility_id != '') {
                        if (($val->checkout_time == NULL && $val->is_checkout == '0') || ($val->is_checkin == '1' && $val->is_checkout == '0')) {
                            $this->setCustomTimezone($val->facility_id);

                            $arrival_time = $val->checkin_time;
                            $this->successLog->error("Ticket number:" . $val->ticket_number);
                            //$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                            $ticketObj = Ticket::find($val->id);
                            $rateDiffInHour = $ticketObj->getCheckOutCurrentTime(true);
                            $facilityObj = Facility::find($val->facility_id);
                            // $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
                            if ($facilityObj->rate_duration_in_hours > 0 && $facilityObj->rate_per_hour > 0 && $facilityObj->rate_free_minutes > 0 && $facilityObj->rate_daily_max_amount > 0) {
                                $rate = $facilityObj->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
                                if ($val->is_checkin == '0' && $val->is_checkout == '0' && $val->checkout_time != '') {
                                    //zeag ticket
                                    $rate = [];
                                    $rate['price'] = $val->parking_amount;
                                }
                            } else {

                                $rate = $facilityObj->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, $isMember);
                            }

                            if ($rate == false) {
                                throw new ApiGenericException('Garage is currently closed.');
                            }
                            $taxFee = $facilityObj->getTaxRate($rate);
                            $processingFee = $facilityObj->getProcessingFee($rate);
                            $parkingAmount = $rate['price'];

                            $tickeAmount = $parkingAmount > 0 ? $parkingAmount + $taxFee + $processingFee : '0.00';
                        }
                    }

                    // net amount
                    if (isset($val->grand_total) && $val->grand_total > 0) {
                        $net_amount = ($val->grand_total) - ($val->processing_fee);
                    } else {
                        $net_amount = 0.00;
                    }

                    //return $rec;
                    $finalCodes5[] = [
                        'No.' => $increment5,
                        'Ticket' => isset($val->ticket_number) ? $val->ticket_number : '-',
                        'Facility Name' => isset($val->full_name) ? $val->full_name : '-',
                        'Check-In At' => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
                        'Check-Out At' => isset($val->checkout_time) ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '-',
                        'Card No' => isset($val->checkout_card_last_four) ? $val->checkout_card_last_four : '-',
                        'Card Type' => isset($val->checkout_card_type) ? $val->checkout_card_type : '-',
                        'Card Expiry' => isset($val->checkout_expiry) ? $val->checkout_expiry : '-',
                        'Total Amount ($)' => isset($val->total) ? floatval($val->total) : 0.00,
                        'Parking Amount ($)' => isset($val->parking_amount) ? floatval($val->parking_amount) : 0.00,
                        'Validated Amount' => isset($val->paid_amount) ? floatval($val->paid_amount) : '0.00',
                        'Payable Amount' => isset($tickeAmount) ? floatval($tickeAmount) : 0.00,
                        'Amount Paid ($)' => isset($val->grand_total) ? floatval($val->grand_total) : 0.00,
                        'Processing Fee ($)' => isset($val->processing_fee) ? floatval($val->processing_fee) : 0.00,
                        'Net Amount ($)' => isset($net_amount) ? floatval($net_amount) : 0.00,
                        'Policy Name' => (isset($policy_name) && !empty($policy_name)) ? $policy_name : '-',
                        'Validation Remarks' => (isset($val->paid_remark) && !empty($val->paid_remark)) ? $val->paid_remark : '-'
                    ];
                    $increment5++;
                }
            } else if ($this->request->slug == self::CHECKINAGAINSTPERMIT) {
                // dd($this->request->slug);die;
                $permitticket = Ticket::where('tickets.partner_id', $this->request->partner_id)->whereNotNull("tickets.permit_request_id")
                    // ->whereDate('tickets.checkout_time', '>=', $from_date)
                    // ->whereDate('tickets.checkout_time', '<=', $to_date)
                    ->join('permit_requests', 'permit_requests.id', '=', 'tickets.permit_request_id', 'left')
                    ->join('permit_rates', 'permit_rates.id', '=', 'permit_requests.permit_rate_id', 'left')
                    ->select('tickets.*', 'permit_requests.permit_type_name', 'permit_rates.rate');
                if ($this->request->facility_id != '') {
                    $permitticket = $permitticket->where('tickets.facility_id', $this->request->facility_id);
                }
                if (isset($from_date) && $from_date != '') {
                    $permitticket = $permitticket->whereDate('tickets.checkin_time', '>=', $from_date)->whereDate('.tickets.checkin_time', '<=', $to_date);
                }
                $permitticket = $permitticket->orderBy("tickets.id", "DESC");
                $permitticket = $permitticket->get();
                //permit data prepare
                if (isset($permitticket) && count($permitticket) > 0) {
                    foreach ($permitticket as $val) {
                        $val->payable_amount = "0.00";
                        if (isset($val->facility)) {
                            $full_name = $val->facility->full_name;
                        } else {
                            $full_name = $val->full_name;
                        }
                        if (isset($val->userPass)) {
                            $pass_code = $val->userPass->pass_code;
                            $pass_total = $val->userPass->pass_total;
                            $total_days = $val->userPass->total_days;
                        } else {
                            $pass_code = $val->pass_code;
                            $pass_total = $val->pass_total;
                            $total_days = $val->total_days;
                        }
                        if (isset($val->reservation)) {
                            $ticketech_code = $val->reservation->ticketech_code;
                        } else {
                            $ticketech_code = $val->ticketech_code;
                        }
                        if (isset($val->user)) {
                            $email = $val->user->email;
                            $phone = $val->user->phone;
                        } else {
                            $email = $val->email;
                            $phone = $val->phone;
                        }

                        $permitFinalResult[] = [
                            'No.' => $finalRowIncrement,
                            'Ticket' => isset($val->ticket_number) ? $val->ticket_number : '-',
                            'License Plate' => isset($val->license_plate) ? $val->license_plate : '-',
                            'Facility Name' => isset($full_name) ? $full_name : '-',
                            'Booking Id' => (isset($ticketech_code) && !empty($ticketech_code)) ? $ticketech_code : '-',
                            'Check-In At' => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
                            'Check-Out At' => isset($val->checkout_time) ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '-',
                            'Permit Type Name' => isset($val->permit_type_name) ? $val->permit_type_name : 'N.A.',
                            'Permit Rate' => isset($val->rate) ? $val->rate : 'N.A.',
                            'Booking Against Pass' => isset($pass_code) ? 'Yes' : 'NA',
                            'Pass ID' => isset($pass_code) ? $pass_code : '-',
                            'Pass Rate ($)' => isset($pass_total) ? $pass_total : '-',
                            'Pass Days' => isset($total_days) ? $total_days : '-',
                            'Price ($)' => isset($val->total) ? $val->total : '0.00',
                            'Payable Amount ($)' => $val->payable_amount,
                            'Overstay' => ($val->is_overstay == '1') ? 'Yes' : 'No',
                            'Email' => (isset($email) && !empty($email)) ? $email : '-',
                            'Phone' => (isset($phone) && !empty($phone)) ? $phone : '-',
                        ];

                        $finalRowIncrement++;
                    }
                    //print_r( $permitFinalResult);
                }

                //
            } else if ($this->request->slug == self::CHECKIN_AGAINST_PASSES) {


                $checkinAgaintPassTickets = Ticket::where('tickets.partner_id', $this->request->partner_id)->whereNotNull("tickets.user_pass_id")
                    ->join('user_passes', 'user_passes.id', '=', 'tickets.permit_request_id', 'left');

                if ($this->request->facility_id != '') {
                    $checkinAgaintPassTickets = $checkinAgaintPassTickets->where('tickets.facility_id', $this->request->facility_id);
                }
                if (isset($from_date) && $from_date != '') {
                    $checkinAgaintPassTickets = $checkinAgaintPassTickets->whereDate('tickets.checkin_time', '>=', $from_date)->whereDate('.tickets.checkin_time', '<=', $to_date);
                }
                $checkinAgaintPassTickets = $checkinAgaintPassTickets->orderBy("tickets.id", "DESC");
                $checkinAgaintPassTickets = $checkinAgaintPassTickets->get();

                //permit data prepare

                if (isset($checkinAgaintPassTickets) && count($checkinAgaintPassTickets) > 0) {
                    $mailSummary['Check-In against Passes'] = count($checkinAgaintPassTickets);
                    foreach ($checkinAgaintPassTickets as $val) {
                        $val->payable_amount = "0.00";
                        if (isset($val->facility)) {
                            $full_name = $val->facility->full_name;
                        } else {
                            $full_name = $val->full_name;
                        }
                        if (isset($val->userPass)) {
                            $pass_code = $val->userPass->pass_code;
                            $pass_total = $val->userPass->pass_total;
                            $total_days = $val->userPass->total_days;
                        } else {
                            $pass_code = $val->pass_code;
                            $pass_total = $val->pass_total;
                            $total_days = $val->total_days;
                        }
                        if (isset($val->reservation)) {
                            $ticketech_code = $val->reservation->ticketech_code;
                        } else {
                            $ticketech_code = $val->ticketech_code;
                        }
                        if (isset($val->user)) {
                            $email = $val->user->email;
                            $phone = $val->user->phone;
                        } else {
                            $email = $val->email;
                            $phone = $val->phone;
                        }

                        $passesFinalResult[] = [
                            'No.'                   => $finalRowIncrement,
                            'Ticket'                => isset($val->ticket_number) ? $val->ticket_number : '-',
                            'License Plate'         => isset($val->license_plate) ? $val->license_plate : '-',
                            'Facility Name'         => isset($full_name) ? $full_name : '-',
                            // 'Booking Id'            => (isset($ticketech_code) && !empty($ticketech_code)) ? $ticketech_code : '-',
                            'Check-In At'           => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
                            'Check-Out At'          => isset($val->checkout_time) ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '-',
                            // 'CheckIn Against Pass'  => isset($pass_code) ? 'Yes' : 'NA',
                            'Pass ID'               => isset($pass_code) ? $pass_code : '-',
                            'Pass Rate ($)'         => isset($pass_total) ? $pass_total : '-',
                            'Pass Days'             => isset($total_days) ? $total_days : '-',
                            'Price ($)'             => isset($val->total) ? $val->total : '0.00',
                            'Email'                 => (isset($email) && !empty($email)) ? $email : '-',
                            'Phone'                 => (isset($phone) && !empty($phone)) ? $phone : '-',
                        ];

                        $finalRowIncrement++;
                    }
                    //print_r( $permitFinalResult);
                }

                //
            } else {
                // all tab ticket data downlaod
                $facilities = '';
                if (in_array($this->request->user_type, [3, 4, 12])) {
                    $facilities = DB::table('user_facilities')->where('user_id', $this->request->user_id)->whereNull('deleted_at')->pluck('facility_id');
                } else {
                    $facilities = DB::table('facilities')->where('owner_id', $this->request->user_id)->whereNull('deleted_at')->pluck('id');
                }

                $facilitynew = $facilities;
                $facilityID = '';
                // $facility_id = isset($this->request->facility_id) ? $this->request->facility_id : 0;
                if (isset($this->request->facility_id) && $this->request->facility_id > 0) {
                    $facilityID = " AND t.facility_id IN ('" . $this->request->facility_id . "') ";
                }

                if (!isset($this->request->facility_id)) {
                    $facilitynew = implode(",", $facilitynew);
                    $facilityID .= " AND t.facility_id IN ($facilitynew)";
                }

                $this->request->slug = '';
                $checkincheckout = $this->getSqlQuery(self::CHECKIN);
                if (count($checkincheckout) > 0) {
                    foreach ($checkincheckout as $val) {
                        $this->successLog->info('checkincheckout ');
                        if (isset($val->userPass)) {
                            $pass_code = $val->userPass->pass_code;
                        } else {
                            $pass_code = $val->pass_code;
                        }

                        $finalCodes1[] = [
                            'No.' => $increment1,
                            'Ticket' => isset($val->ticket_number) ? $val->ticket_number : '-',
                            'License Plate' => isset($val->license_plate) ? $val->license_plate : '-',
                            'Facility Name' => isset($val->full_name) ? $val->full_name : '-',
                            'Booking Id' => (isset($val->ticketech_code) && !empty($val->ticketech_code)) ? $val->ticketech_code : '-',
                            'Check-In At' => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
                            'Check-Out At' => isset($val->checkout_time) ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '-',
                            'Booking Against Pass' => isset($val->pass_code) ? 'Yes' : 'NA',
                            'Pass ID' => isset($pass_code) ? $pass_code : '-',
                            'Pass Rate ($)' => isset($val->pass_total) ? $val->pass_total : '-',
                            'Pass Days' => isset($val->total_days) ? $val->total_days : '-',
                            'Price ($)' => isset($val->total) ? floatval($val->total) : '0.00',
                            'Overstay' => ($val->is_overstay == '1') ? 'Yes' : 'No',
                            'Email' => (isset($val->email) && !empty($val->email)) ? $val->email : '-',
                            'Phone' => (isset($val->phone) && !empty($val->phone)) ? $val->phone : '-',
                        ];

                        $increment1++;
                    }
                }

                // Drive up data
                // $this->request->request->add(['slug' => self::DRIVEUP]);
                $driveupTicets = $this->getSqlQuery(self::DRIVEUP);

                if (count($driveupTicets) > 0) {
                    $ticketNumbers = collect($driveupTicets)->pluck('ticket_number');

                    $policyData = BusinessPolicy::whereIn('id', collect($driveupTicets)->pluck('policy_id'))->get()->keyBy('id');

                    // Fetch related overstay and extend data once for all ticket numbers
                    $overstayData = OverstayTicket::whereIn('ticket_number', $ticketNumbers)
                        ->selectRaw('ticket_number, SUM(grand_total) as grand_total, SUM(total) as total')
                        ->groupBy('ticket_number')
                        ->get()
                        ->keyBy('ticket_number');

                    $extendData = TicketExtend::whereIn('ticket_number', $ticketNumbers)
                        ->selectRaw('ticket_number, SUM(grand_total) as grand_total, SUM(total) as total, SUM(tax_fee) as total_tax_fee, 
                                    SUM(discount_amount) as total_discount_amount, SUM(additional_fee) as total_additional_fee, 
                                    SUM(surcharge_fee) as total_surcharge_fee')
                        ->groupBy('ticket_number')
                        ->get()
                        ->keyBy('ticket_number');
                    $ticketExtendSums = TicketExtend::whereIn('ticket_number', $ticketNumbers)
                        ->selectRaw('ticket_number, SUM(total) as extend_total')
                        ->groupBy('ticket_number')
                        ->pluck('extend_total', 'ticket_number')
                        ->toArray(); // Converts to key-value array for fast lookup

                    $tickettimezonedata = $this->preloadTimezonesForTickets(collect($driveupTicets));

                    foreach ($driveupTicets as $val) {
                        set_time_limit(0);
                        // if ($val->is_checkout == '0' && $val->checkout_time == '') {
                        //     $tickeAmount = $this->calculatePayablePrice($val);;
                        // } else {
                        //     $tickeAmount = '0.0';
                        // }
                        // Calulate Amount Due or Payable !! Start here : 16-01-2025
                        $ticketObj = Ticket::find($val->id);
                        $ticketObj->ticketadditionalinfo = $ticketObj->ticketadditionalinfo() ?? null;
                        $tickeAmount = '0.00';
                        // $payablePrice =   $this->calculatePayablePrice($val);
                        $payablePrice =   0;
                        $payableset = 0;
                        $this->successLog->info("DRIVE UP  : Ticket Number : {$val->ticket_number} , Payable : {$payablePrice}");
                        if ($ticketObj->facility->is_gated_facility == '1') { // gated 
                            if ($val->is_checkout == '0' && $val->checkout_time == '') {
                                $tickeAmount = $payablePrice;
                                $payableset = 1;
                            }
                        } else {   // ungated 
                            if ($ticketObj->is_checkout == '0') {
                                if (!is_null($ticketObj->event_user_id)) {
                                    $tickeAmount = '0.00';
                                } else {
                                    $payablePrice       =   $this->calculatePayablePrice($ticketObj, $tickettimezonedata);
                                    $tickeAmount        =  $payablePrice;
                                    $payableset = 1;
                                    $val->grand_total   =   '0.00';
                                }

                                if ($ticketObj->is_closed === '1' || $ticketObj->is_checkout == '1') {
                                    $tickeAmount = '0.00';
                                    if ($ticketObj->discount_amount > 0 || $ticketObj->is_extended == '1' || $ticketObj->paid_amount > 0) {
                                        $val->grand_total = $this->calculatePayablePrice($ticketObj, $tickettimezonedata);
                                    }
                                }
                            } elseif (!empty($ticketObj->payment_date) || $ticketObj->is_checkout == '1') {
                                $tickeAmount = '0.00';
                                if ($ticketObj->discount_amount > 0 || $ticketObj->is_extended == '1' || $ticketObj->paid_amount > 0) {
                                    $val->grand_total = $this->calculatePayablePrice($ticketObj, $tickettimezonedata);
                                }
                            }
                        }
                        // !! Close Here !!!

                        $policy_name = isset($policyData[$ticketObj->policy_id]) ? $policyData[$ticketObj->policy_id]->policy_name : '';
                        if (isset($ticketObj->card_last_four) && $ticketObj->card_last_four != '') {
                            $card_no = $ticketObj->card_last_four;
                        } elseif (isset($ticketObj->transaction['payment_last_four']) && $ticketObj->transaction['payment_last_four'] != '') {
                            $card_no = $ticketObj->transaction['payment_last_four'];
                        } else {
                            $card_no = '';
                        }

                        if (isset($ticketObj->card_type) && $ticketObj->card_type != '') {
                            $card_type = $ticketObj->card_type;
                        } elseif (isset($ticketObj->transaction['card_type']) && $ticketObj->transaction['card_type'] != '') {
                            $card_type = $ticketObj->transaction['card_type'];
                        } else {
                            $card_type = '';
                        }

                        if (isset($ticketObj->expiry) && $ticketObj->expiry != '') {
                            $output = str_split($ticketObj->expiry, 2);

                            if (isset($output[1])) {
                                $card_expiry = $output[0] . "/" . $output[1];
                            }
                        } elseif (isset($val->transaction['expiration']) && $val->transaction['expiration'] != '') {
                            $output = str_split($val->transaction['expiration'], 2);
                            if (isset($output[1])) {
                                $card_expiry = $output[0] . "/" . $output[1];
                            }
                        } else {
                            $card_expiry = '';
                        }

                        $overstayTotal = $overstayGrandTotal = $extendGrandTotal = $extendTotal = $extendparkingAmount = $extendProcessingFee = $extendtaxFee = 0;
                        $val->total_tax_fee = $ticketObj->total_tax_fee;
                        $val->total_discount_amount = $ticketObj->total_discount_amount;
                        $val->total_additional_fee = $ticketObj->total_additional_fee;
                        $val->total_surcharge_fee = $ticketObj->total_surcharge_fee;

                        $overstaySums = $overstayData[$val->ticket_number] ?? null;
                        if ($ticketObj->is_overstay == '1' && $overstaySums) {
                            $val->total += $overstaySums->total ?? 0;
                            $val->grand_total += $overstaySums->grand_total ?? 0;
                        } elseif ($ticketObj->is_extended == '1') {

                            $extendSums = $extendData[$val->ticket_number] ?? null;
                            if ($ticketObj->is_extended == '1' && $extendSums) {
                                $val->grand_total += $extendSums->grand_total ?? 0;
                                $val->total += $extendSums->total ?? 0;
                                $val->total_tax_fee += $extendSums->total_tax_fee ?? 0;
                                $val->total_discount_amount += $extendSums->total_discount_amount ?? 0;
                                $val->total_additional_fee += $extendSums->total_additional_fee ?? 0;
                                $val->total_surcharge_fee += $extendSums->total_surcharge_fee ?? 0;
                            }

                            $extendTotal = $ticketExtendSums[$val->ticket_number] ?? 0;
                            $val->total += $extendTotal > 0 ? $extendTotal : 0;

                            $extendparkingAmount = ($extendTotal - ($extendProcessingFee + $extendtaxFee));
                        }



                        // net amount
                        // $net_amount = ($val->grand_total > 0) ? max(0.00, round($val->grand_total - $val->processing_fee - $val->tax_fee, 2)) : 0.00;
                        $processingFee = round(
                            floatval($val->processing_fee ?? 0) +
                                floatval($val->total_processing_fee ?? 0) +
                                floatval($val->total_additional_fee ?? 0) +
                                floatval($val->additional_fee ?? 0),
                            2
                        );

                        $taxFee = round(
                            floatval($val->tax_fee ?? 0) +
                                floatval($val->total_tax_fee ?? 0),
                            2
                        );
                        $citysurcharge = round(
                            floatval($val->surcharge_fee  ?? 0) +
                                floatval($val->total_surcharge_fee ?? 0),
                            2
                        );

                        if (isset(($val->ticketadditionalinfo)->new_discount_amount)) {
                            $discountFee = round(
                                (
                                    isset(($val->ticketadditionalinfo)->new_discount_amount)
                                    ? floatval(($val->ticketadditionalinfo)->new_discount_amount)
                                    : (isset($val->discount_amount)
                                        ? floatval($val->discount_amount)
                                        : 0)
                                ),
                                2
                            );

                            $net_amount = $val->ticketadditionalinfo->new_parking_amount;
                        } else {
                            $discountFee = round(floatval($val->discount_amount ?? 0) + floatval($val->total_discount_amount ?? 0), 2);
                            $net_amount = ($val->grand_total > 0) ? max(0.00, round($val->grand_total - $processingFee - $taxFee - $citysurcharge, 2)) : 0.00;
                        }

                        $totalAmount = round(floatval($val->total ?? 0), 2);

                        if (isset($val->promocode) && $val->promocode != '') {
                            $isPromoApplied = false;
                        } else {
                            $isPromoApplied = false;
                        }

                        $newdataparkingAmount = isset($val->parking_amount)
                            ? round(floatval($val->parking_amount + $extendparkingAmount), 2)
                            : 0.00;

                        $newdatadiscountAmount = isset($val->paid_amount)
                            ? round(floatval($val->paid_amount), 2)
                            : (
                                ($tickeAmount == 0 && $isPromoApplied)
                                ? round($discountFee - ($processingFee + $taxFee + $citysurcharge), 2)
                                : round($discountFee, 2)
                            );

                        $newdataprocessingFee = ($tickeAmount == 0 && $isPromoApplied)
                            ? 0.00
                            : round(
                                (
                                    isset(($val->ticketadditionalinfo)->new_processing_fee)
                                    ? floatval(($val->ticketadditionalinfo)->new_processing_fee)
                                    : (isset($val->processing_fee)
                                        ? floatval($val->processing_fee)
                                        : 0)
                                ) +
                                    floatval($val->total_processing_fee ?? 0) +
                                    floatval($val->total_additional_fee ?? 0) +
                                    floatval($val->additional_fee ?? 0),
                                2
                            );

                        if (isset($val->ticketadditionalinfo->new_tax_amount)) {
                            $newdatatax = ($tickeAmount == 0 && $isPromoApplied)
                                ? 0.00
                                : round(
                                    floatval($val->ticketadditionalinfo->new_tax_amount ?? $val->tax_fee ?? 0),
                                    2
                                );
                        } else {
                            $newdatatax = ($tickeAmount == 0 && $isPromoApplied)
                                ? 0.00
                                : round(
                                    floatval($val->tax_fee ?? 0) + floatval($val->total_tax_fee ?? 0),
                                    2
                                );
                        }


                        $olddataprocessingFee = ($tickeAmount == 0 && $isPromoApplied)
                            ? 0.00
                            : round(
                                (isset($val->processing_fee)
                                    ? floatval($val->processing_fee)
                                    : 0)
                                    +
                                    floatval($val->total_processing_fee ?? 0) +
                                    floatval($val->total_additional_fee ?? 0) +
                                    floatval($val->additional_fee ?? 0),
                                2
                            );

                        $olddatatax = ($tickeAmount == 0 && $isPromoApplied)
                            ? 0.00
                            : round(
                                floatval($val->tax_fee ?? 0) +
                                    floatval($val->total_tax_fee ?? 0),
                                2
                            );

                        // Total
                        $newdatatotalAmount = round($newdataparkingAmount  + $newdataprocessingFee + $newdatatax, 2);

                        if (isset($val->ticketadditionalinfo->new_tax_amount)) {
                            if ($payableset) {
                                $tickeAmount  = $val->ticketadditionalinfo->new_parking_amount + $val->ticketadditionalinfo->new_processing_fee + $val->ticketadditionalinfo->new_tax_amount;
                                $net_amount = $val->ticketadditionalinfo->new_parking_amount;
                            } else {
                                $val->grand_total = $val->ticketadditionalinfo->new_parking_amount + $val->ticketadditionalinfo->new_processing_fee + $val->ticketadditionalinfo->new_tax_amount;
                            }
                        } else {
                            if ($payableset) {
                                $net_amount = ($tickeAmount > 0) ? max(0.00, round($tickeAmount - $processingFee - $taxFee - $citysurcharge, 2)) : 0.00;
                            }
                        }

                        $entry = [
                            'No.' => $increment3,
                            'Ticket' => isset($val->ticket_number) ? $val->ticket_number : '-',
                            'License Plate' => isset($val->license_plate) ? $val->license_plate : '-',
                            'Facility Name' => isset($val->full_name) ? $val->full_name : '-',
                            'Check-In At' => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
                            // 'Check-Out At' => isset($val->checkout_time) ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '-',
                            'Check-Out At' => isset($val->facility->is_gated_facility) && $val->facility->is_gated_facility == 0
                                ? (isset($val->estimated_checkout) ? date("m/d/Y, h:i A", strtotime($val->estimated_checkout)) : '-')
                                : (isset($val->checkout_datetime) ? date("m/d/Y, h:i A", strtotime($val->checkout_datetime)) : '-'),
                            'Check-In Mode' => isset($val->device_type) ? $val->device_type : '-',
                            'Card No' => (isset($card_no) && !empty($card_no)) ? $card_no : '-',
                            'Card Type' => isset($val->card_type) ? $val->card_type : '-',
                            'Card Expiry' => (isset($card_expiry) && !empty($card_no)) ? $card_expiry : '-',
                            'Amount Paid ($)' => isset($val->grand_total) ? round(floatval($val->grand_total), 2) : round(floatval(0.00), 2),
                            // 'Total Amount ($)' => ($tickeAmount == 0 && $isPromoApplied)
                            //     ? round($totalAmount - ($processingFee + $taxFee + $citysurcharge), 2)
                            //     : $totalAmount,
                            'Payable Amount ($)' => isset($tickeAmount) ? round(floatval($tickeAmount), 2) : round(floatval(0.00), 2),
                            // 'Parking Amount ($)' => isset($val->parking_amount)
                            //     ? round(floatval($val->parking_amount + $extendparkingAmount), 2)
                            //     : 0.00,
                            'Parking Amount ($)'  => $newdataparkingAmount,
                            'Discount Amount ($)' => $newdatadiscountAmount,
                            'Processing Fee ($)' =>  $newdataprocessingFee,
                            'Tax ($)' => $newdatatax,
                            // 'Amount Paid ($)' => isset($val->grand_total) ? round(floatval($val->grand_total), 2) : round(floatval(0.00), 2),
                            // 'Payable Amount ($)' => isset($tickeAmount) ? round(floatval($tickeAmount), 2) : round(floatval(0.00), 2),
                            // // 'Total Amount ($)' => isset($val->total) ? round(floatval($val->total), 2) : round(floatval(0.00), 2),
                            // // 'Parking Amount ($)' => isset($val->parking_amount) ? round(floatval($val->parking_amount + $extendparkingAmount), 2) : round(floatval(0.00), 2),
                            // 'Parking Amount ($)' => isset($val->parking_amount)
                            //     ? round(floatval($val->parking_amount + $extendparkingAmount), 2)
                            //     : 0.00,
                            // 'Discount Amount ($)' => '$' . (

                            //                             isset($val->paid_amount)

                            //                                 ? round(floatval($val->paid_amount), 2)

                            //                                 : (

                            //                                     ($tickeAmount == 0 && $isPromoApplied)

                            //                                         ? round($discountFee - ($processingFee + $taxFee + $citysurcharge), 2)

                            //                                         : round($discountFee, 2)

                            //                                 )

                            //                         ),
                            // 'Processing Fee ($)' => ($tickeAmount == 0 && $isPromoApplied)

                            //         ? 0.00

                            //         : round(

                            //             (

                            //                 isset(($val->ticketadditionalinfo)->new_processing_fee) 

                            //                     ? floatval(($val->ticketadditionalinfo)->new_processing_fee)

                            //                     : (isset($val->processing_fee) 

                            //                         ? floatval($val->processing_fee) 

                            //                         : 0)

                            //             )  +
                            //             floatval($val->total_processing_fee ?? 0) +
                            //             floatval($val->total_additional_fee ?? 0) +
                            //             floatval($val->additional_fee ?? 0),
                            //         2
                            //     ),

                            // 'Tax ($)' => ($tickeAmount == 0 && $isPromoApplied)
                            //     ? 0.00
                            //     : round(
                            //         (
                            //             isset(($val->ticketadditionalinfo)->new_tax_amount) 
                            //                 ? floatval(($val->ticketadditionalinfo)->new_tax_amount)
                            //                 : floatval($val->tax_fee ?? 0)
                            //         ) +
                            //         floatval($val->total_tax_fee ?? 0),
                            //         2
                            //     ),
                            // 'Processing Fee ($)' => isset($val->processing_fee) ? round(floatval($val->processing_fee + $extendProcessingFee), 2) : round(floatval(0.00), 2),
                            // 'Tax ($)' => isset($val->tax_fee) ? round(floatval($val->tax_fee) + @$val->total_tax_fee, 2) : round(floatval(0.00), 2),

                        ];

                        // Add 'City Surcharge ($)' only if partner ID matches
                        if ($this->request->partner_id == $this->revpassPartnerId) {
                            $entry['City Surcharge ($)'] = isset($ticketObj->surcharge_fee)
                                ? round(floatval($ticketObj->surcharge_fee), 2)
                                : floatval(0.00);

                            $total_surcharge += isset($ticketObj->surcharge_fee)
                                ? round(floatval($ticketObj->surcharge_fee), 2)
                                : floatval(0.00);
                        }

                        // Add remaining columns
                        $entry += [
                            // 'Validated Amount ($)' => isset($val->paid_amount) ? round(floatval($val->paid_amount), 2) : round(floatval(0.00), 2),
                            // 'Payable Amount ($)' => isset($tickeAmount) ? round(floatval($tickeAmount), 2) : round(floatval(0.00), 2),
                            // 'Amount Paid ($)' => isset($val->grand_total) ? round(floatval($val->grand_total), 2) : round(floatval(0.00), 2),
                            // 'Ticket Amount' => ($tickeAmount == 0 && $isPromoApplied)
                            // ? 0.00
                            // : round(
                            //     floatval($val->parking_amount ?? 0) +
                            //     floatval($extendparkingAmount ?? 0) +
                            //     floatval($val->paid_amount ?? 0) +
                            //     floatval($discountFee ?? 0) +
                            //     floatval($val->processing_fee ?? 0) +
                            //     floatval($val->total_processing_fee ?? 0) +
                            //     floatval($val->additional_fee ?? 0) +
                            //     floatval($val->total_additional_fee ?? 0) +
                            //     floatval($val->tax_fee ?? 0) +
                            //     floatval($val->total_tax_fee ?? 0) +
                            //     floatval($citysurcharge ?? 0),
                            //     2
                            // ),
                            'Ticket Amount' => $newdatatotalAmount,
                            'Net Amount ($)' => isset($net_amount) ? round(floatval($net_amount), 2) : round(floatval(0.00), 2),
                            'Promocode' => isset($val->promocode) ? $val->promocode : '-',
                            'Policy Name' => (isset($policy_name) && !empty($policy_name)) ? $policy_name : '-',
                            'Validation Remarks' => (isset($val->paid_remark) && !empty($val->paid_remark)) ? $val->paid_remark : '-',
                            // 'Discount Amount ($)' => isset($val->discount_amount) ? round(floatval($val->discount_amount + @$val->total_discount_amount), 2) : round(floatval(0.00), 2),
                        ];

                        // Add the complete entry to $finalCodes3
                        $finalCodes3[] = $entry;

                        // Conditionally add CC Fee and City Surcharge
                        // if ($this->request->partner_id == $this->revpassPartnerId) {
                        //     $finalCodes3[count($finalCodes3) - 1]['CC Fee ($)'] = isset($val->additional_fee) ? $val->additional_fee + @$val->total_additional_fee : '-';
                        //     $finalCodes3[count($finalCodes3) - 1]['City Surcharge ($)'] = isset($val->surcharge_fee) ? $val->surcharge_fee + @$val->total_surcharge_fee : '-';
                        // }

                        $totaldiscoumtvalidateamount += $discountAmount = isset($val->paid_amount)
                            ? round(floatval($val->paid_amount), 2)
                            : (
                                ($tickeAmount == 0 && $isPromoApplied)
                                ? round($discountFee - ($processingFee + $taxFee + $citysurcharge), 2)
                                : round($discountFee, 2)
                            );

                        // Calculate Ticket Amount first
                        $TotalticketAmount += $newdatatotalAmount;


                        // $total_amount += isset($val->total) ? round(floatval($val->total), 2) : round(floatval(0.00), 2);
                        $total_amount += ($tickeAmount == 0 && $isPromoApplied)
                            ? round($totalAmount - ($processingFee + $taxFee + $citysurcharge), 2)
                            : $totalAmount;
                        $total_parking_amount += isset($val->parking_amount) ? round(floatval($val->parking_amount + $extendparkingAmount), 2) : round(floatval(0.00), 2);
                        $total_validate_amount += isset($val->paid_amount) ? round(floatval($val->paid_amount), 2) : round(floatval(0.00), 2);
                        $total_payable += isset($tickeAmount) ? round(floatval($tickeAmount), 2) : round(floatval(0.00), 2);
                        $total_amount_paid += isset($val->grand_total) ? round(floatval($val->grand_total), 2) : round(floatval(0.00), 2);
                        // $total_processfee += isset($val->processing_fee) ? round(floatval($val->processing_fee + $extendProcessingFee), 2) : round(floatval(0.00), 2);
                        $total_net_amount +=  isset($net_amount) ? floatval($net_amount) : floatval(0.00);
                        // $total_tax += isset($val->tax_fee) ? round(floatval($val->tax_fee) + @$val->total_tax_fee, 2) : round(floatval(0.00), 2);
                        $total_processfee +=
                            ($tickeAmount == 0 && $isPromoApplied)

                            ? 0.00

                            : round(

                                (

                                    isset(($val->ticketadditionalinfo)->new_processing_fee)

                                    ? floatval(($val->ticketadditionalinfo)->new_processing_fee)

                                    : (isset($val->processing_fee)

                                        ? floatval($val->processing_fee)

                                        : 0)

                                )  +
                                    floatval($val->total_processing_fee ?? 0) +
                                    floatval($val->total_additional_fee ?? 0) +
                                    floatval($val->additional_fee ?? 0),
                                2
                            );
                        $total_tax += $newdatatax;
                        $total_discount_amount += ($tickeAmount == 0 && $isPromoApplied)
                            ? round($discountFee - ($processingFee + $taxFee + $citysurcharge), 2)
                            : $discountFee;
                        if ($this->request->partner_id == $this->revpassPartnerId) {
                            $total_cc_fee = $total_cc_fee + $val->additional_fee + @$val->total_additional_fee;
                            $total_city_surcharge = $total_city_surcharge + $val->surcharge_fee + @$val->total_surcharge_fee;
                        }

                        $increment3++;
                    }
                    // dd($finalCodes3);

                    // show total payable for row created
                    $totalentry = [
                        'No.' => '',
                        'Ticket' => '',
                        'License Plate' => '',
                        'Facility Name' => '',
                        'Check-In At' => '',
                        'Check-Out At' => '',
                        'Check-In Mode' => '',
                        'Card No' => '',
                        'Card Type' => '',
                        'Card Expiry' => '',
                        'Amount Paid' => '',
                        'Payable Amount' => '',
                        'Parking Amount ($)' => '',
                        'Discount Amount ($)' => '',
                        'Processing Fee ($)' => '',
                        'Tax ($)' => '',
                        // 'Total Amount ($)' => '',
                        // 'Parking Amount ($)' => '',
                        // 'Processing Fee ($)' => '',
                        // 'Tax ($)' => '',
                    ];

                    if ($this->request->partner_id == $this->revpassPartnerId) {
                        $totalentry['City Surcharge ($)'] = '';
                    }

                    $totalentry += [
                        'Ticket Amount' => '',
                        // 'Validated Amount' => '',
                        // 'Payable Amount' => '',
                        // 'Amount Paid' => '',
                        'Net Amount ($)' => '',
                        'Promocode' => '',
                        'Policy Name' => '',
                        'Validation Remarks' => '',
                        // 'Discount Amount ($)' => '',
                    ];

                    $finalCodes3[] = $totalentry;

                    $totalentryplus = [
                        'No.' => '',
                        'Ticket' => '',
                        'License Plate' => '',
                        'Facility Name' => '',
                        'Check-In At' => '',
                        'Check-Out At' => '',
                        'Check-In Mode' => '',
                        'Card No' => '',
                        'Card Type' => '',
                        'Card Expiry' => 'Total',
                        // 'Total Amount' => (float) number_format($total_amount, 2, '.', ''),
                        'Amount Paid' => (float) number_format($total_amount_paid, 2, '.', ''),
                        'Payable Amount' => (float) number_format($total_payable, 2, '.', ''),
                        'Parking Amount' => (float) number_format($total_parking_amount, 2, '.', ''),
                        'Discount Amount ($)' => (float) number_format($totaldiscoumtvalidateamount, 2, '.', ''),
                        'Processing Fee ($)' => (float) number_format($total_processfee, 2, '.', ''),
                        'Tax ($)' => (float) number_format($total_tax, 2, '.', ''),
                    ];

                    if ($this->request->partner_id == $this->revpassPartnerId) {
                        $totalentryplus['City Surcharge ($)'] = (float) number_format($total_city_surcharge, 2, '.', '');
                    }

                    $totalentryplus += [
                        'Ticket Amount' => (float) number_format($TotalticketAmount, 2, '.', ''),
                        // 'Validated Amount' => (float) number_format($total_validate_amount, 2, '.', ''),
                        // 'Payable Amount' => (float) number_format($total_payable, 2, '.', ''),
                        // 'Amount Paid' => (float) number_format($total_amount_paid, 2, '.', ''),
                        'Net Amount ($)' => (float) number_format($total_net_amount, 2, '.', ''),
                        'Promocode' => '',
                        'Policy Name' => '',
                        'Validation Remarks' => '',
                        // 'Discount Amount ($)' => (float) number_format($total_discount_amount, 2, '.', ''),
                    ];

                    $finalCodes3[] = $totalentryplus;
                    // foreach ($driveupTicets as $ticket) {
                    //     $this->successLog->info('ticketdriveup Start SLUG 22 : ');
                    //     $net_amount = $tickeAmount  = 0;
                    //     if ($ticket->is_checkout == 0) {
                    //         $tickeAmount = $this->calculatePayablePrice($ticket);
                    //     }

                    //     $finalCodes3[] = [
                    //         'No.' => $increment3,
                    //         'Ticket' => isset($ticket->ticket_number) ? $ticket->ticket_number : '-',
                    //         'License Plate' => isset($ticket->license_plate) ? $ticket->license_plate : '-',
                    //         'Facility Name' => isset($ticket->full_name) ? $ticket->full_name : '-',
                    //         'Check-In At' => date("m/d/Y, h:i A", strtotime($ticket->checkin_time)),
                    //         'Check-Out At' => isset($ticket->checkout_time) ? date("m/d/Y, h:i A", strtotime($ticket->checkout_time)) : '-',
                    //         'Card No' => (isset($ticket->card_last_four) && !empty($ticket->card_last_four)) ? $ticket->card_last_four : '-',
                    //         'Card Type' => isset($ticket->card_type) ? $ticket->card_type : '-',
                    //         'Card Expiry' => (isset($ticket->expiry) && !empty($ticket->expiry)) ? $ticket->expiry : '-',
                    //         'Total Amount ($)' => isset($ticket->total) ? floatval($ticket->total) : 0.00,
                    //         'Parking Amount ($)' => isset($ticket->parking_amount) ? floatval($ticket->parking_amount) : 0.00,
                    //         'Validated Amount ($)' => isset($ticket->paid_amount) ? floatval($ticket->paid_amount) : 0.00,
                    //         'Payable Amount ($)' => isset($tickeAmount) ? floatval($tickeAmount) : 0.00,
                    //         'Amount Paid ($)' => isset($ticket->grand_total) ? floatval($ticket->grand_total) : 0.00,
                    //         'Processing Fee ($)' => isset($ticket->processing_fee) ? floatval($ticket->processing_fee) : 0.00,
                    //         'Net Amount ($)' => isset($net_amount) ? floatval($net_amount) : 0.00,
                    //         'Policy Name' => (isset($ticket->policy_name) && !empty($ticket->policy_name)) ? $ticket->policy_name : '-',
                    //         'Validation Remarks' => (isset($ticket->paid_remark) && !empty($ticket->paid_remark)) ? $ticket->paid_remark : '-'
                    //     ];
                    //     $total_amount =  $total_amount + $ticket->total;
                    //     $total_parking_amount = $total_parking_amount + $ticket->parking_amount;
                    //     $total_validate_amount = $total_validate_amount + $ticket->paid_amount;
                    //     // $total_payable += $tickeAmount;
                    //     $total_amount_paid = $total_amount_paid + $ticket->grand_total;
                    //     $total_processfee = $total_processfee + $ticket->processing_fee;
                    //     $total_net_amount = $total_net_amount + ($ticket->grand_total) - ($ticket->processing_fee - $ticket->tax_fee);

                    //     $increment3++;
                    // }

                    // $finalCodes3[] = [
                    //     'No.' => '',
                    //     'Ticket' => '',
                    //     'License Plate' => '',
                    //     'Facility Name' => '',
                    //     'Check-In At' => '',
                    //     'Check-Out At' => '',
                    //     'Card No' => '',
                    //     'Card Type' => '',
                    //     'Card Expiry' => '',
                    //     'Total Amount' => '',
                    //     'Parking Amount' => '',
                    //     'Validated Amount' => '',
                    //     //'Payable Amount' => '', 
                    //     'Amount Paid' => '',
                    //     'Processing Fee' => '',
                    //     'Net Amount' => '',
                    //     'Policy Name' => '',
                    //     'Validation Remarks' => ''


                    // ];


                    // $finalCodes3[] = [
                    //     'No.' => '',
                    //     'Ticket' => '',
                    //     'License Plate' => '',
                    //     'Facility Name' => '',
                    //     'Check-In At' => '',
                    //     'Check-Out At' => '',
                    //     'Card No' => '',
                    //     'Card Type' => '',
                    //     'Card Expiry' => 'Total',
                    //     'Total Amount' => $total_amount,
                    //     'Parking Amount' => $total_parking_amount,
                    //     'Validated Amount' => $total_validate_amount,
                    //     'Payable Amount' => $total_payable,
                    //     'Amount Paid' => $total_amount_paid,
                    //     'Processing Fee' => $total_processfee,
                    //     'Net Amount' => $total_net_amount,
                    //     'Policy Name' => '',
                    //     'Validation Remarks' => ''


                    // ];
                }

                // offline 
                // $this->request->request->add(['slug' => self::OFFPAYMENT]);
                $ticketOffPayment = $this->getSqlQuery(self::OFFPAYMENT);


                if (count($ticketOffPayment) > 0) {
                    foreach ($ticketOffPayment as $ticket) {
                        $this->successLog->info('ticketdriveup Start SLUG 11 : ticketOffPayment ');
                        //return $rec;
                        $finalCodes4[] = [
                            'No.' => $increment4,
                            'Facility Name' => isset($ticket->full_name) ? $ticket->full_name : '-',
                            'Ticket' => isset($ticket->ticket_number) ? $ticket->ticket_number : '-',
                            'License Plate' => $ticket->license_plate != '' ? $ticket->license_plate : '-',
                            'Check-In At' => date("m/d/Y, h:i A", strtotime($ticket->checkin_time)),
                            //'Check-Out At' => date("m/d/Y, h:i A", strtotime($val->checkout_time)),
                            'Check-Out At' => isset($ticket->checkout_time) ? date("m/d/Y, h:i A", strtotime($ticket->checkout_time)) : '-',
                            'Card No' => isset($ticket->card_last_four) && !empty($ticket->card_last_four)  ? $ticket->card_last_four : (!empty($ticket->payment_last_four) ? $ticket->payment_last_four : '-'),
                            'Card Type' => isset($ticket->card_type) && !empty($ticket->card_type) ? $ticket->card_type : (!empty($ticket->anet_card_type) ? $ticket->anet_card_type : '-'),
                            'Card Expiry' => isset($ticket->expiry) && !empty($ticket->expiry) ? substr($ticket->expiry, 0, 2) . '/' . substr($ticket->expiry, -2) : (!empty($ticket->expiration) ? substr($ticket->expiration, 0, 2) . '/' . substr($ticket->expiration, -2) : '-'),
                            'Payable Amount' => isset($ticket->total) ? sprintf("%.2f", $ticket->total) : sprintf("%.2f", '0.00'),
                            'Validated Amount' => isset($ticket->paid_amount) ? sprintf("%.2f", $ticket->paid_amount) : sprintf("%.2f", '0.00'),
                            'Attendant Name' => isset($ticket->attendent_name) ? $ticket->attendent_name : '-',
                            'Comments' => isset($ticket->checkout_remark) ? $ticket->checkout_remark : '-',
                            'Overstay' => ($ticket->is_overstay == '1') ? 'Yes' : 'No',
                            'Reader Used' => (isset($ticket->reader_used) && !empty($ticket->reader_used)) ? $ticket->reader_used : '-',
                            //'Terminal Id' => isset($ticket->terminal_id']) ? $ticket->terminal_id']:'-',
                        ];
                        $increment4++;
                    }
                }

                // Third party tickest 
                // $this->request->request->add(['slug' => self::THIRDPARTY]);
                $openTicketList = $this->getSqlQuery(self::THIRDPARTY);
                if (count($openTicketList) > 0) {
                    foreach ($openTicketList as $val) {

                        //rate find
                        $tickeAmount = $payable_amt = 0;
                        $isMember = 0;
                        if ($val->facility_id != '') {
                            if (($val->checkout_time == NULL && $val->is_checkout == '0') || ($val->is_checkin == '1' && $val->is_checkout == '0')) {
                                $this->setCustomTimezone($val->facility_id);

                                $arrival_time = $val->checkin_time;
                                $this->successLog->error("openTicketList Ticket number: " . $val->ticket_number);
                                //$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                $ticketObj = Ticket::find($val->id);
                                $rateDiffInHour = $ticketObj->getCheckOutCurrentTime(true);
                                $facilityObj = Facility::find($val->facility_id);
                                if ($facilityObj->rate_duration_in_hours > 0 && $facilityObj->rate_per_hour > 0 && $facilityObj->rate_free_minutes > 0 && $facilityObj->rate_daily_max_amount > 0) {
                                    $rate = $facilityObj->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
                                    if ($val->is_checkin == '0' && $val->is_checkout == '0' && $val->checkout_time != '') {
                                        //zeag ticket
                                        $rate = [];
                                        $rate['price'] = $val->parking_amount;
                                    }
                                } else {

                                    $rate = $facilityObj->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, $isMember);
                                }

                                if ($rate == false) {
                                    throw new ApiGenericException('Garage is currently closed.');
                                }
                                $taxFee = $facilityObj->getTaxRate($rate);
                                $processingFee = $facilityObj->getProcessingFee($rate);
                                $parkingAmount = $rate['price'];

                                // $tickeAmount = $parkingAmount > 0 ? $rate['price'] + $taxFee + $processingFee : '0.00';
                                $tickeAmount = $parkingAmount > 0 ? $parkingAmount + $taxFee + $processingFee : '0.00';
                            }
                        }

                        // net amount
                        if (isset($val->grand_total) && $val->grand_total > 0) {
                            $net_amount = ($val->grand_total) - ($val->processing_fee);
                        } else {
                            $net_amount = 0.00;
                        }

                        //return $rec;
                        $finalCodes5[] = [
                            'No.' => $increment5,
                            'Ticket' => isset($val->ticket_number) ? $val->ticket_number : '-',
                            'Facility Name' => isset($val->full_name) ? $val->full_name : '-',
                            'Check-In At' => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
                            'Check-Out At' => isset($val->checkout_time) ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '-',
                            'Card No' => isset($val->checkout_card_last_four) ? $val->checkout_card_last_four : '-',
                            'Card Type' => isset($val->checkout_card_type) ? $val->checkout_card_type : '-',
                            'Card Expiry' => isset($val->checkout_expiry) ? $val->checkout_expiry : '-',
                            'Total Amount ($)' => isset($val->total) ? floatval($val->total) : 0.00,
                            'Parking Amount ($)' => isset($val->parking_amount) ? floatval($val->parking_amount) : 0.00,
                            'Validated Amount' => isset($val->paid_amount) ? floatval($val->paid_amount) : '0.00',
                            'Payable Amount' => isset($tickeAmount) ? floatval($tickeAmount) : 0.00,
                            'Amount Paid ($)' => isset($val->grand_total) ? floatval($val->grand_total) : 0.00,
                            'Processing Fee ($)' => isset($val->processing_fee) ? floatval($val->processing_fee) : 0.00,
                            'Net Amount ($)' => isset($net_amount) ? floatval($net_amount) : 0.00,
                            'Policy Name' => (isset($policy_name) && !empty($policy_name)) ? $policy_name : '-',
                            'Validation Remarks' => (isset($val->paid_remark) && !empty($val->paid_remark)) ? $val->paid_remark : '-'
                        ];
                        $increment5++;
                    }
                }

                $mailSummary['Check-In against Booking'] = count($checkincheckout);
                $mailSummary['Transient Checkins'] = count($driveupTicets);
                $mailSummary['Checkout from Admin'] = count($ticketOffPayment);
                $mailSummary['Third Party Tickets'] = count($openTicketList);
            }

            $this->successLog->info('data checkincheckout : ' . count($checkincheckout));
            $this->successLog->info('data driveupTicets : ' . count($driveupTicets));
            $this->successLog->info('data ticketOffPayment : ' . count($ticketOffPayment));
            $this->successLog->info('data openTicketList : ' . count($openTicketList));
            $this->successLog->info('data checkinAgaintPassTickets : ' . count($checkinAgaintPassTickets));
            $this->successLog->info('data openTicketListArray : ' . count($openTicketListArray));
            $this->successLog->info('data driveupTicetsArray : ' . count($driveupTicetsArray));

            if ((count($checkincheckout) == 0) &&
                (count($driveupTicets) == 0) &&
                (count($ticketOffPayment) == 0) &&
                (count($openTicketList) == 0) &&
                (count($checkinAgaintPassTickets) == 0) &&
                (count($openTicketListArray) == 0) &&
                (count($driveupTicetsArray) == 0) &&
                (count($ticketOffPayment) == 0)
            ) {
                return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "There is no data for this date."]], 500);
            }

            $this->successLog->info(' Summary   : ' . json_encode($mailSummary));

            $this->successLog->info(' mails   : ' . json_encode($this->toEmails));
            $lastrow = isset($increment3) ? $increment3 + 2 : 0;

            $this->successLog->info('data finalCodes1 : ' . count($finalCodes1));
            $this->successLog->info('data finalCodes2 : ' . count($finalCodes2));
            $this->successLog->info('data finalCodes3 : ' . count($finalCodes3));
            $this->successLog->info('data finalCodes4 : ' . count($finalCodes4));
            $this->successLog->info('data finalCodes5 : ' . count($finalCodes5));
            $this->successLog->info('data permitFinalResult : ' . count($permitFinalResult));
            $this->successLog->info('data passesFinalResult : ' . count($passesFinalResult));

            if ((count($finalCodes1) == 0) && (count($finalCodes2) == 0) && (count($finalCodes3) == 0) && (count($finalCodes4) == 0) && (count($finalCodes5) == 0) && (count($permitFinalResult) == 0) && (count($passesFinalResult) == 0)) {
                $this->successLog->info(' data not found');
                return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "There is no data for this date."]], 500);
            }

            Excel::create(
                $excelSheetName,
                function ($excel) use ($finalCodes1, $permitFinalResult, $passesFinalResult, $finalCodes2, $finalCodes3, $finalCodes4, $finalCodes5, $excelSheetName, $lastrow, $color, $brandSetting, $from_date, $to_date, $locationName, $garageCode) {

                    // Set the spreadsheet title, creator, and description
                    $excel->setTitle($excelSheetName);
                    $excel->setCreator('RevenueDetails')->setCompany('ParkEngage');
                    $excel->setDescription('List Of Checkin');

                    // Build the spreadsheet, passing in the payments array
                    if (empty($finalCodes1) && empty($finalCodes2) && empty($finalCodes3) && empty($finalCodes4) && empty($finalCodes5) && empty($permitFinalResult) && empty($passesFinalResult)) {
                        $this->successLog->info('Sorry! No Data Found.');
                        throw new ApiGenericException('Sorry! No Data Found.');
                    } else {
                        if (isset($finalCodes1) && !empty($finalCodes1)) {
                            $getSummary = 'Check-In Against Booking';
                            $excel->sheet(
                                'Check-In Against Booking',
                                /*  function ($sheet) use ($finalCodes1) {
                                    $sheet->fromArray($finalCodes1, null, 'A1', false, true);
                                } */
                                function ($sheet) use ($finalCodes1, $brandSetting, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary) {
                                    $this->addLogoInExcelHeader($sheet, $getSummary, $brandSetting, $color);
                                    $headerSpace = 3;
                                    $this->getBrandHeaderSection($sheet, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary);

                                    // Color Row For Heading 
                                    $sheet->cell("A$headerSpace:X$headerSpace", function ($row) use ($color) {

                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                    });

                                    $sheet->fromArray($finalCodes1, null, 'A' . "$headerSpace", false, true);
                                }
                            );
                        }


                        if (isset($finalCodes2) && !empty($finalCodes2)) {
                            $getSummary = 'Only Check-Out';
                            $excel->sheet(
                                'Only Check-Out',
                                /*  function ($sheet) use ($finalCodes2) {
                                    $sheet->fromArray($finalCodes2, null, 'A1', false, true);
                                } */
                                function ($sheet) use ($finalCodes2, $brandSetting, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary) {
                                    $this->addLogoInExcelHeader($sheet, $getSummary, $brandSetting, $color);
                                    $headerSpace = 3;
                                    $this->getBrandHeaderSection($sheet, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary);

                                    // Color Row For Heading 
                                    $sheet->cell("A$headerSpace:R$headerSpace", function ($row) use ($color) {

                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                    });

                                    $sheet->fromArray($finalCodes2, null, 'A' . "$headerSpace", false, true);
                                }
                            );
                        }

                        if (isset($finalCodes3) && !empty($finalCodes3)) {
                            $getSummary = 'Transient Checkins';
                            $excel->sheet(
                                'Transient-Checkins',
                                /* function ($sheet) use ($finalCodes3, $lastrow) {
                                    $colorCode = $lastrow;
                                    $row_name = 'G' . $colorCode . ':O' . $colorCode;
                                    $sheet->cell($row_name, function ($row) {

                                        $row->setFont(array(
                                            'bold'       =>  true
                                        ));
                                    });
                                    $sheet->fromArray($finalCodes3, null, 'A1', false, true);
                                } */

                                function ($sheet) use ($finalCodes3, $lastrow, $brandSetting, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary) {

                                    $columnIndex = count($finalCodes3[0]);
                                    $columnLetter = '';

                                    while ($columnIndex > 0) {
                                        $mod = ($columnIndex - 1) % 26;
                                        $columnLetter = chr(65 + $mod) . $columnLetter;
                                        $columnIndex = (int)(($columnIndex - $mod) / 26);
                                    }

                                    // Merge cells from A1 to the calculated column
                                    $sheet->mergeCells("A1:{$columnLetter}1");
                                    $this->addLogoInExcelHeader($sheet, $getSummary, $brandSetting, $color);
                                    $headerSpace = 3;
                                    $this->getBrandHeaderSection($sheet, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary);

                                    // Color Row For Heading 
                                    $colorCode = $lastrow + 2;
                                    $row_name = 'G' . $colorCode . ':' . $columnLetter . $colorCode;

                                    // Get the ASCII value of the starting column "J" and the dynamic ending column
                                    $startColumn = ord('J');
                                    $endColumn = ord($columnLetter);

                                    // Apply two-decimal format to the range from "J" to the dynamic end column
                                    for ($col = $startColumn; $col <= $endColumn; $col++) {
                                        $sheet->getStyle(chr($col))->getNumberFormat()->setFormatCode('0.00');
                                    }

                                    // Apply styling to the row range
                                    $sheet->cell($row_name, function ($row) {
                                        $row->setFont(['bold' => true]);
                                        $row->setBackground('#D6DCE4');
                                        $row->setAlignment('center');
                                    });

                                    // Center-align all data
                                    $sheet->cells('A3:' . $columnLetter . (count($finalCodes3) + 2), function ($cells) {
                                        $cells->setAlignment('center');
                                    });

                                    // Fill data from array
                                    $sheet->fromArray($finalCodes3, [], 'A3', false, true);
                                }
                            );
                        }

                        if (isset($finalCodes4) && !empty($finalCodes4)) {
                            $getSummary = 'Checkout from Admin';
                            $excel->sheet(
                                'Checkout from Admin',
                                /* function ($sheet) use ($finalCodes4) {
                                    $sheet->fromArray($finalCodes4, null, 'A1', false, true);
                                } */
                                function ($sheet) use ($finalCodes4, $brandSetting, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary) {
                                    $this->addLogoInExcelHeader($sheet, $getSummary, $brandSetting, $color);
                                    $headerSpace = 3;
                                    $this->getBrandHeaderSection($sheet, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary);

                                    // Color Row For Heading 
                                    $sheet->cell("A$headerSpace:T$headerSpace", function ($row) use ($color) {

                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                    });

                                    $sheet->fromArray($finalCodes4, null, 'A' . "$headerSpace", false, true);
                                }
                            );
                        }


                        if (isset($finalCodes5) && !empty($finalCodes5)) {
                            $getSummary = 'Third-Party Tickets';
                            $excel->sheet(
                                'Third-Party Tickets',
                                /*  function ($sheet) use ($finalCodes5) {
                                    $sheet->fromArray($finalCodes5, null, 'A1', false, true);
                                } */
                                function ($sheet) use ($finalCodes5, $brandSetting, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary) {
                                    $this->addLogoInExcelHeader($sheet, $getSummary, $brandSetting, $color);
                                    $headerSpace = 3;
                                    $this->getBrandHeaderSection($sheet, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary);

                                    // Color Row For Heading 
                                    $sheet->cell("A$headerSpace:T$headerSpace", function ($row) use ($color) {

                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                    });

                                    $sheet->fromArray($finalCodes5, null, 'A' . "$headerSpace", false, true);
                                }
                            );
                        }
                        //permit changes
                        //permit 
                        if (isset($permitFinalResult) && !empty($permitFinalResult)) {
                            $getSummary = 'Check-In Against Permit';
                            $excel->sheet(
                                'Check-In Against Permit',
                                function ($sheet) use ($permitFinalResult, $brandSetting, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary) {
                                    $this->addLogoInExcelHeader($sheet, $getSummary, $brandSetting, $color);
                                    $headerSpace = 3;
                                    $this->getBrandHeaderSection($sheet, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary);

                                    // Color Row For Heading 
                                    $sheet->cell("A$headerSpace:T$headerSpace", function ($row) use ($color) {

                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                    });

                                    $sheet->fromArray($permitFinalResult, null, 'A' . "$headerSpace", false, true);
                                }
                            );
                        }
                        // && empty($passesFinalResult)
                        if (isset($passesFinalResult) && !empty($passesFinalResult)) {
                            $getSummary = 'Check-In Against Passes';
                            $excel->sheet(
                                'Check-In Against Passes',
                                function ($sheet) use ($passesFinalResult, $brandSetting, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary) {
                                    $this->addLogoInExcelHeader($sheet, $getSummary, $brandSetting, $color);
                                    $headerSpace = 3;
                                    $this->getBrandHeaderSection($sheet, $color, $from_date, $to_date, $locationName, $garageCode, $getSummary);

                                    // Color Row For Heading 
                                    $sheet->cell("A$headerSpace:T$headerSpace", function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                    });

                                    $sheet->fromArray($passesFinalResult, null, 'A' . "$headerSpace", false, true);
                                }
                            );
                        }
                    }
                }
            )->store('xls');
            $this->successLog->info('File name   : ' . $excelSheetName);
            $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
            $this->successLog->info('File Saved on   : ' . $path_to_file);

            $data['totalTickets']   = 1;
            // $data['netValue']       = $TotalRevenueNonValidated + $TotalCheckinRevenueNonValidated;
            // $data['overAllTotal']   = $TotalRevenueNonValidated + $TotalCheckinRevenueNonValidated;
            $data['report_name']    = 'checkin_chekout';
            $data['facilityWiseTicketsCount']    = $mailSummary;
            // $data['location_name']  = 'TEST';
            $data['themeColor']     = $brandSetting->color;
            $data['mail_body']      = "PFB summary of checkin Checkout Ticket Report. A detailed report is also attached.";
            $data['team_regards']      = $this->request->partner_id == config('parkengage.PARTNER_RevPass') ? " RevPass" : " Parkengage";

            $this->successLog->error('Check To emails before sent emails ' . json_encode($this->toEmails));

            // \Log::info('Print data in Send Email ');
            // \Log::info($data);

            $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');

            // Change Request PIMS-12502 : Vijay - 30-01-2025 
            $subject = config('parkengage.townsend.checkin_checkout_subject') . $this->dateTimeRange;
            $data['subject']        = $subject;

            $filedata['type']       = 'saved';
            $filedata['content']    = $path_to_file;
            $filedata['filename']   = $path_to_file;
            $filedata['format']     = 'xls';

            $data['filedata'] = $filedata;

            MailHelper::sendEmail($this->toEmails, 'townsend.cashier-shift-report', $data, $this->request->partner_id);
            $this->successLog->info("Mail sent to success : " . json_encode($this->toEmails));

            /* Mail::send('townsend.cashier-shift-report', $data, function ($message)  use ($excelSheetName) {
                // $message->bcc(['<EMAIL>']);
                // $message->to(config('parkengage.townsend.open_ticket_emails'));
                $message->to($this->toEmails);
                $message->subject(config('parkengage.townsend.checkin_checkout_subject') . $this->dateTimeRange);
                $message->from(config('parkengage.default_sender_email'), env('MAIL_USERNAME'));
                $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                $this->successLog->info("Mail Sent success with failname :  {$path_to_file}");
                if (file_exists($path_to_file)) {
                    // $this->successLog->info("Path Of file and name 333 {$path_to_file}");
                    $message->attach($path_to_file);
                }
            }); */
        } catch (\Throwable $th) {
            $this->errorLog->info('Error ' . $th->getMessage() . ' File Name :  ' . $th->getFile() . ' Line ' . $th->getLine());
            $errorMessage = 'WLA Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine();
            $data['partner_key'] = "PCI";
            $data['exception'] = "Exception in Check-in checkout Mail Request : " . $errorMessage;
            // Mail::send('email-exception', $data, function ($message) {
            //     $message->to(config('parkengage.email_exceptions'));
            //     $message->subject("Email Exception :- PCI - Check-in Checkout Email Request.");
            //     $message->from(env('INFO_MAIL_PARKENGAGE_FROM'), env('MAIL_USERNAME'));
            // });
        }
    }

    private function getSqlQuery($slug = null)
    {
        $this->successLog->info("Query requested for slug : {$slug} AND  'this->request->slug : ' {$this->request->slug} ");
        $this->isOffline = false;
        if ($this->request->slug == self::OFFPAYMENT || $slug == self::OFFPAYMENT) {
            $this->isOffline = true;
        }
        $setSqlParams = $this->setSqlParams();

        $advanceFilter = $this->getAdvanceFilter();
        $this->successLog->info('SQL QUERY1 ===>  : ' . $advanceFilter);


        if (($this->request->slug == self::CHECKIN) || $slug == self::CHECKIN) {
            $sqlQuery = "SELECT t.id,t.estimated_checkout, t.is_overstay,t.is_extended,t.anet_transaction_id, t.is_checkout,is_checkin, t.facility_id, t.ticket_number, t.license_plate, t.checkin_time, t.checkout_time, t.card_type, t.card_last_four, t.expiry, t.checkout_card_type, t.checkout_card_last_four, t.checkout_expiry, t.parking_amount, t.paid_amount, t.total, t.grand_total, t.processing_fee,t.tax_fee, t.paid_remark,f.full_name, bp.policy_name, up.pass_code, up.total as pass_total, up.total_days, r.ticketech_code, u.email,u.phone, ant.card_type as anet_card_type, ant.payment_last_four, ant.expiration,t.user_pass_id,t.reservation_id FROM tickets as t INNER JOIN facilities as f on f.id = t.facility_id LEFT JOIN users as u ON u.id = t.user_id  LEFT JOIN business_policy as bp on bp.id = t.policy_id LEFT JOIN user_passes as up on up.id = t.user_pass_id LEFT JOIN reservations as r ON r.id = t.reservation_id LEFT JOIN anet_transactions as ant on ant.id = t.anet_transaction_id where t.vp_device_checkin ='0' and t.checkout_without_checkin ='0' AND t.is_checkin !='0' and (t.user_pass_id is not null or t.reservation_id is not null) AND t.deleted_at is null $setSqlParams $advanceFilter ORDER BY id DESC";
        } else if (($this->request->slug == self::DRIVEUP) || $slug == self::DRIVEUP) {
            $sqlQuery = "SELECT t.id,t.estimated_checkout, t.checkout_datetime, t.is_overstay,t.device_type,t.promocode,t.is_extended,t.anet_transaction_id,  t.is_checkout,is_checkin, t.facility_id, t.event_id,t.event_user_id,t.ticket_number, t.license_plate, t.checkin_time, t.checkout_time, t.card_type, t.card_last_four, t.expiry, t.checkout_card_type, t.checkout_card_last_four, t.checkout_expiry, t.parking_amount, t.paid_amount, t.total, t.grand_total, t.payment_date,t.is_overstay,t.processing_fee,t.tax_fee, t.paid_remark,t.additional_fee,t.surcharge_fee,t.discount_amount,f.full_name, f.facility_type_id, f.is_gated_facility, bp.policy_name  FROM tickets as t INNER JOIN facilities as f on f.id = t.facility_id LEFT JOIN business_policy as bp on bp.id = t.policy_id LEFT JOIN users as u ON u.id = t.user_id where t.user_pass_id is null and t.reservation_id is null  and t.permit_request_id is null AND t.deleted_at is null and  ((t.is_checkin='1' and t.is_checkout= '0') or (t.is_checkin='0' and t.is_checkin= '1') or (t.is_checkin='1' and t.is_checkout='1')) $setSqlParams $advanceFilter ORDER BY id DESC";
        } else if ($this->request->slug == self::OFFPAYMENT || $slug == self::OFFPAYMENT) {
            $sqlQuery = "SELECT t.id,t.estimated_checkout, t.is_overstay,t.anet_transaction_id,t.is_extended, t.is_checkout,is_checkin, t.facility_id, t.ticket_number, t.license_plate, t.checkin_time, t.checkout_time, t.card_type, t.card_last_four, t.expiry, t.checkout_card_type, t.checkout_card_last_four, t.checkout_expiry, t.parking_amount, t.paid_amount, t.total, t.grand_total, t.processing_fee,t.tax_fee, t.paid_remark,f.full_name, bp.policy_name, ant.card_type as anet_card_type, ant.payment_last_four, ant.expiration,ant.reader_used  FROM tickets as t INNER JOIN facilities as f on f.id = t.facility_id LEFT JOIN anet_transactions as ant on ant.id = t.anet_transaction_id LEFT JOIN business_policy as bp on bp.id = t.policy_id LEFT JOIN users as u ON u.id = t.user_id where t.checkout_time is not null and t.checkout_by is not null AND t.deleted_at is null $setSqlParams $advanceFilter ORDER BY id DESC";
        } else if ($this->request->slug == self::THIRDPARTY || $slug == self::THIRDPARTY) {
            $sqlQuery = "SELECT t.id,t.estimated_checkout, t.is_overstay,t.anet_transaction_id,t.is_extended,t.is_checkout,is_checkin, t.facility_id, t.ticket_number, t.license_plate, t.checkin_time, t.checkout_time, t.card_type, t.card_last_four, t.expiry, t.checkout_card_type, t.checkout_card_last_four, t.checkout_expiry, t.parking_amount, t.paid_amount, t.total, t.grand_total, t.processing_fee,t.tax_fee, t.paid_remark,f.full_name, bp.policy_name  FROM tickets as t INNER JOIN facilities as f on f.id = t.facility_id LEFT JOIN business_policy as bp on bp.id = t.policy_id LEFT JOIN users as u ON u.id = t.user_id
                where t.reservation_id is null and t.user_pass_id is null and t.checkout_by is null  AND t.is_checkin ='0' AND t.deleted_at is null $setSqlParams $advanceFilter ORDER BY id DESC";
            #and t.vp_device_checkin ='1'
        } else {
            // this for all tab data ticket downlaod 
        }


        $this->successLog->info('SQL QUERY ===>  : ' . json_encode($sqlQuery));
        // $result = DB::select($sqlQuery);
        try {
            // $result = [];
            $result = DB::select($sqlQuery);
            return $result;
        } catch (QueryException $e) {
            //throw $th;
            $this->successLog->error('Error In Query :  ' . $e->getMessage());
        }


        // $this->successLog->info('SQL QUERY  RESULT  : ' . json_encode($result));

    }

    private function setSqlParams()
    {
        $this->successLog->info('ticketdriveup Start SLUG 55 : ');
        $setParams = '';
        $fromDate = date('Y-m-d ', strtotime($this->request->from_date)) . '00:00:00';
        $toDate = date('Y-m-d ', strtotime($this->request->to_date)) . '23:59:59';
        $this->successLog->info("Check Date time Start Date  : {$this->request->from_date} Todate : {$this->request->to_date}   ");
        $this->successLog->info("Check Date time Start Date 2 : {$fromDate} Todate : {$toDate}   ");

        if (isset($this->request->partner_id) && !empty($this->request->partner_id)) {
            $this->successLog->info('ticketdriveup Start SLUG 66 :  11 ' . $this->isOffline);
            if (!empty($this->request->from_date) && !empty($this->request->to_date)) {
                $this->successLog->info('ticketdriveup Start SLUG 66 : 22 check offline ' . $this->isOffline);
                $setParams = " AND t.checkin_time >='$fromDate' AND t.checkin_time <='$toDate' AND t.partner_id = {$this->request->partner_id}";
                // if (($this->request->slug == self::DRIVEUP || $this->request->slug == self::DRIVEUP2)) {
                if (($this->request->slug == self::DRIVEUP)) {
                    // $setParams = " AND t.checkin_time >='$fromDate' AND t.checkin_time <='$toDate' AND t.partner_id = {$this->request->partner_id}";
                    $setParams = " AND ((checkin_time >='$fromDate' and checkin_time <='$toDate') OR (checkout_time >='$fromDate' and checkout_time <='$toDate')) AND t.partner_id = {$this->request->partner_id}";
                }

                $this->successLog->info('ticketdriveup Start SLUG 66 : 22 check offline  ' . json_encode($this->isOffline));
                if ($this->request->slug == self::OFFPAYMENT || $this->isOffline == true) {
                    $this->successLog->info(' offline 1 ' . $this->isOffline);
                    $setParams = " AND t.checkout_time >='$fromDate' AND t.checkout_time <='$toDate' AND t.partner_id = {$this->request->partner_id}";
                }

                if (isset($this->request->is_checkin_checkout) && $this->request->is_checkin_checkout == '1') {
                    $setParams = " AND t.checkout_time >='$fromDate' AND t.checkout_time <='$toDate' AND t.partner_id = {$this->request->partner_id}";
                }
            } else {
                $this->successLog->info('ticketdriveup Start SLUG 66 : 33 ');
                $setParams .= " AND t.partner_id = {$this->request->partner_id}";
            }
            $this->successLog->info('ticketdriveup Start SLUG 66 : Close ');
        }



        if (isset($this->request->facility_id) && !empty($this->request->facility_id)) {
            $setParams .= ' AND t.facility_id = ' . $this->request->facility_id;
            $this->successLog->info('ticketdriveup Start SLUG 77 : ');
        } else {
            if (in_array($this->request->user_type, [4, 12])) {
                $setParams .= ' AND t.facility_id IN (SELECT facility_id FROM user_facilities WHERE user_id IN (' . $this->request->user_id . ') and deleted_at is null) ';
            } else {
                $setParams .= ' AND t.facility_id IN (SELECT id FROM facilities WHERE owner_id IN (' . $this->request->partner_id . ') and deleted_at is null) ';
            }
        }

        if (isset($this->request->is_checkin_checkout)  && $this->request->is_checkin_checkout == '0') {
            $setParams .= " AND t.is_checkout ='0' ";
        }
        // filter data only checkout
        if (isset($this->request->is_checkin_checkout)  && $this->request->is_checkin_checkout == '1') {
            $setParams .= " AND t.is_checkout ='1' ";
        }
        // filter for closed ticket
        if (isset($this->request->is_checkin_checkout)  && $this->request->is_checkin_checkout == '2') {
            // $ticket = $ticket->whereNotNull('checkout_time');
            $setParams .= " AND checkout_time is not null ";
        }

        if (isset($this->request->device_type)  && $this->request->device_type != '') {
            // $ticket = $ticket->whereNotNull('checkout_time');
            $setParams .= " AND device_type = '" . $this->request->device_type . "' ";
        }

        if (isset($this->request->is_admin_checkout) && $this->request->is_admin_checkout == '1') {
            $setParams .= " AND t.checkout_by is not null ";
        }
        if (isset($this->request->is_admin_checkout) && $this->request->is_admin_checkout == '0') {
            $setParams .= " AND t.checkout_by is null ";
        }

        // $ticket = $ticket->whereNotNull('checkout_time');
        if (isset($this->request->search)  && $this->request->search != '') {   // Normal Search Above From Table search
            $search = QueryBuilder::removeSpecialChar($this->request->search);
            if ($this->request->slug == self::CHECKIN || $this->request->slug == self::CHECKIN2) {
                $setParams .= " AND (t.ticket_number LIKE  '%" . $search . "%' ";
                $setParams .= " OR t.card_type LIKE '%" . $search . "%' ";
                $setParams .= " OR t.card_last_four LIKE '%" . $search . "%' ";
                $setParams .= " OR t.license_plate LIKE '%" . $search . "%' ";
                $setParams .= " OR u.phone LIKE '%" . $search . "%' ";
                $setParams .= " OR u.email LIKE '%" . $search . "%' ";
                $setParams .= " OR r.ticketech_code LIKE '%" . $search . "%' ) ";
            } else if ($this->request->slug == self::DRIVEUP || $this->request->slug == self::DRIVEUP2) {
                $setParams .= " AND (t.ticket_number LIKE  '%" . $search . "%' ";
                $setParams .= " OR t.card_type LIKE '%" . $search . "%' ";
                $setParams .= " OR t.card_last_four LIKE '%" . $search . "%' ";
                $setParams .= " OR t.license_plate LIKE '%" . $search . "%' ";
                $setParams .= " OR u.phone LIKE '%" . $search . "%' ";
                $setParams .= " OR u.email LIKE '%" . $search . "%' )";
            } else if ($this->request->slug == self::OFFPAYMENT) {
                $setParams .= " AND (t.ticket_number LIKE  '%" . $search . "%' ";
                $setParams .= " OR t.card_type LIKE '%" . $search . "%' ";
                $setParams .= " OR t.card_last_four LIKE '%" . $search . "%' ";
                $setParams .= " OR t.license_plate LIKE '%" . $search . "%' ";
                $setParams .= " OR u.phone LIKE '%" . $search . "%' ";
                $setParams .= " OR u.email LIKE '%" . $search . "%' )";
            } else if ($this->request->slug == self::THIRDPARTY) {
                $setParams .= " AND (t.ticket_number LIKE  '%" . $search . "%' ";
                $setParams .= " OR t.card_type LIKE '%" . $search . "%' ";
                $setParams .= " OR t.card_last_four LIKE '%" . $search . "%' ";
                $setParams .= " OR t.license_plate LIKE '%" . $search . "%' ";
                $setParams .= " OR u.phone LIKE '%" . $search . "%' ";
                $setParams .= " OR u.email LIKE '%" . $search . "%' )";
            }
        }

        $this->successLog->info('ticketdriveup Start SLUG 77 : ');
        $this->successLog->info('SET SQL PARAM  : ' . $setParams);
        return $setParams;
    }

    // Advance Search Filter : 
    private function getAdvanceFilter()
    {
        // Advance filter ;
        $advanceFilter = '';
        if (isset($this->request->search_filter)  && $this->request->search_filter == '1') {

            if (isset($this->request->ticket_number) && $this->request->ticket_number != '') {
                $advanceFilter .= " AND t.ticket_number LIKE  '%{$this->request->ticket_number}%' ";
            }
            if (isset($this->request->license_plate) && $this->request->license_plate != '') {
                $advanceFilter .= " AND t.license_plate LIKE  '%{$this->request->license_plate}%' ";
            }

            if (isset($this->request->phone_number) && $this->request->phone_number != '') {
                $phone = QueryBuilder::removeSpecialChar($this->request->phone_number);
                $advanceFilter .= " AND u.phone LIKE '%" . $phone . "%' ";
            }
            if (isset($this->request->customer_name) && $this->request->customer_name != '') {
                $advanceFilter .= " AND u.name LIKE '%" . $this->request->customer_name . "%' ";
            }

            if (isset($this->request->is_validated) && $this->request->is_validated == '1') {
                $advanceFilter .= " AND t.paid_by > 0 ";
                if (isset($this->request->validated_by) && $this->request->validated_by > 0) {
                    $validate_user_id = $this->request->validated_by;
                    $advanceFilter .= " AND t.paid_by IN ($validate_user_id)";
                }
            }

            if (isset($this->request->payment_status) && strtolower($this->request->payment_status) == 'processed') {
                $advanceFilter .= " AND t.anet_transaction_id > 0 ";
            }
            if (isset($this->request->payment_status) && strtolower($this->request->payment_status) == 'authorized') {
                $advanceFilter .= " AND t.anet_transaction_id =''  AND t.card_last_four !='' ";
            }

            if ($this->request->slug == self::CHECKIN || $this->request->slug == self::CHECKIN2) {
                //ant.card_type as anet_card_type, ant.payment_last_four, ant.expiration
                if (isset($this->request->payment_last_four) && $this->request->payment_last_four != '') {
                    $advanceFilter .= " AND ant.payment_last_four ='" . $this->request->payment_last_four . "' ";
                }

                if (isset($this->request->card_type) && $this->request->card_type != '') {
                    $advanceFilter .= " AND ant.card_type ='" . $this->request->card_type . "' ";
                }

                if (isset($this->request->expiration) && $this->request->expiration != '') {
                    $advanceFilter .= " AND ant.expiration ='" . $this->request->expiration . "' ";
                }
            } else {
                if (isset($this->request->payment_last_four) && $this->request->payment_last_four != '') {
                    $advanceFilter .= " AND t.card_last_four ='" . $this->request->payment_last_four . "' ";
                }

                if (isset($this->request->card_type) && $this->request->card_type != '') {
                    $advanceFilter .= " AND t.card_type ='" . $this->request->card_type . "' ";
                }

                if (isset($this->request->expiration) && $this->request->expiration != '') {
                    $advanceFilter .= " AND t.expiry ='" . $this->request->expiration . "' ";
                }
            }

            if (isset($this->request->checkin_between) && $this->request->checkin_between != '') {
                $checkinTime = explode("-", $this->request->checkin_between);
                $start_time =  date("H:i:s", strtotime($checkinTime[0]));
                $end_time = date("H:i:s", strtotime($checkinTime[1]));

                $advanceFilter .= " AND time(t.checkin_time) >='$start_time' AND time(t.checkin_time) <= '$end_time'";
            }
        }
        $this->successLog->info('advanceFilter advanceFilter ');
        $this->successLog->info('Advance Filter Query 2 : ' . $advanceFilter);
        return $advanceFilter;
    }

    private function getToEmails()
    {
        $this->toEmails = $this->request->emails;
        $this->successLog->error('requested To emails : ' . json_encode($this->toEmails));
    }

    private function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if (($facility) && ($facility->timezone != '')) {
            date_default_timezone_set($facility->timezone);
            // $this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
        } else {
            if ($partnerTimezone) {
                if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                    // $this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
                }
            }
        }
    }

    protected function oldcalculatePayablePrice($val)
    {

        $this->setCustomTimezone($val->facility_id);
        $ticketObj = Ticket::find($val->id);

        if (!empty($ticketObj->payment_date)) {
            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->payment_date);
            $rateDiffInHour = $ticketObj->getCheckOutCurrentTime(true, $ticketObj->payment_date);
        } else {
            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->checkin_time);
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $rateDiffInHour = $ticketObj->getCheckOutCurrentTime(true);
        }

        $tickeAmount = $isMember = 0;
        if ($ticketObj->facility->rate_duration_in_hours > 0 && $ticketObj->facility->rate_per_hour > 0 && $ticketObj->facility->rate_free_minutes > 0 && $ticketObj->facility->rate_daily_max_amount > 0) {
            if (!empty($ticketObj->payment_date)) {
                $rate = $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, true, null, false, false, '0', $isMember);
            } else {
                $rate = $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
            }
            if ($ticketObj->is_checkin == '1' && $ticketObj->is_checkout == '0' && $ticketObj->checkout_time != '') {
                //zeag ticket
                $rate = [];
                $rate['price'] = $ticketObj->parking_amount;
            }
        } else {
            if (!empty($ticketObj->payment_date)) {
                $rate = $ticketObj->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, true,  false, true, false, 0, $isMember);
            } else {
                $rate = $ticketObj->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, $isMember);
            }
        }
        $taxFee = $ticketObj->facility->getTaxRate($rate);
        if (!empty($ticketObj->payment_date)) {
            $processingFee = '0.00';
        } else {
            $processingFee = $ticketObj->facility->getProcessingFee(0);
        }
        $parkingAmount = $rate['price'];
        $tickeAmount = $parkingAmount > 0 ? $parkingAmount + $taxFee + $processingFee : '0.00';
        return $tickeAmount;
    }

    protected function calculatePayablePrice($val, $tickettimezonedata)
    {
        $tickeAmount = $isMember = 0;

        static $lastTimezone = null; // Static variable to retain value across calls

        if (isset($tickettimezonedata[$val->id])) {
            $currentTimezone = $tickettimezonedata[$val->id];

            // Ensure timezone is set only when changed
            if ($lastTimezone !== $currentTimezone) {
                date_default_timezone_set($currentTimezone);
                $lastTimezone = $currentTimezone;
            }
        }

        $ticketObj = Ticket::find($val->id);
        if ($this->checkEventCaseBeforePayableCalculate($ticketObj) && $ticketObj->event_id > 0) {
            return $tickeAmount;
        }


        // From here Code Moved on line : 6661 because it usefull for gated case only

        // for Event App Changes
        if ($ticketObj->event_user_id != NULL) {
            return '0.00';
        }

        // gated
        if (!empty($ticketObj->facility) && $ticketObj->facility->is_gated_facility == '1') {
            // Jump Here : from Line :  
            if (!empty($ticketObj->payment_date)) {
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->payment_date);
                $rateDiffInHour = $val->getCheckOutCurrentTime(true, $ticketObj->payment_date);
            } else {
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->checkin_time);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                $rateDiffInHour = $ticketObj->getCheckOutCurrentTime(true);
            }

            if ($ticketObj->facility->rate_duration_in_hours > 0 && $ticketObj->facility->rate_per_hour > 0 && $ticketObj->facility->rate_free_minutes > 0 && $ticketObj->facility->rate_daily_max_amount > 0) {
                if (!empty($ticketObj->payment_date)) {
                    $rate = $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, true, null, false, false, '0', $isMember);
                } else {
                    $rate = $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
                }
                if ($ticketObj->is_checkin == '1' && $ticketObj->is_checkout == '0' && $ticketObj->checkout_time != '') {
                    //zeag ticket
                    $rate = [];
                    $rate['price'] = $ticketObj->parking_amount;
                }
            } else {
                if (!empty($ticketObj->payment_date)) {
                    $rate = $ticketObj->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, true,  false, true, false, 0, $isMember);
                } else {
                    $rate = $ticketObj->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, $isMember);
                }
            }
        } else {
            // ungated
            // Check Promotion and Validation 
            $rate['price']  = $this->checkPromotionsAndValidations($ticketObj);
            $tickeAmount = $rate['price'];
            return $tickeAmount;
        }
        $taxFee = $ticketObj->facility->getTaxRate($rate);
        if (!empty($ticketObj->payment_date)) {
            $processingFee = '0.00';
        } else {
            $processingFee = $ticketObj->facility->getProcessingFee(0);
        }
        $parkingAmount = $rate['price'];
        $tickeAmount = $parkingAmount > 0 ? $parkingAmount + $taxFee + $processingFee : '0.00';
        return $tickeAmount;
    }

    #PIMS-8246 dushyant autostart

    public function setPartnerCustomTimezone($parnter_id)
    {
        $secret = OauthClient::where('partner_id', $parnter_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($partnerTimezone->timezone != '') {
                date_default_timezone_set($partnerTimezone->timezone);
                $this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
            }
        }
    }


    public function addLogoInExcelHeader($excel, $title = "Checkin Checkout Summary", $brandSetting, $color = '#191D88')
    {
        /* Code for logo*/
        if (isset($brandSetting) && !empty($brandSetting)) {
            $drawing = new PHPExcel_Worksheet_Drawing();
            if (env('ISLOCAL')) {
                $drawing->setPath(public_path('assets/media/images/breeze.png'));
            } else {
                // For Dynamic
                if (isset($brandSetting) && !empty($brandSetting)) {
                    $drawing->setPath(storage_path('app/brand-settings/' . $brandSetting->logo));
                } else {
                    $drawing->setPath(public_path('assets/media/images/breeze.png'));
                }
            }
            $drawing->setCoordinates('A1');
            // Adjust the dimensions of the image if needed
            $drawing->setWidth(150);
            $drawing->setHeight(50);
            $drawing->setOffsetX(25);
            $drawing->setOffsetY(10);
            // Add image to worksheet
            $excel->getDrawingCollection()->append($drawing);
            /* End Code for logo*/
        }

        // !! Text Section 
        // $color = $this->color;
        $excel->mergeCells('A1:I1');
        $excel->getRowDimension(1)->setRowHeight(60);
        $excel->setCellValue('A1', $title);
        $excel->cell('A1:I1', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            // $cell->setBackground($color);
            // $cell->setFontColor('#ffffff');
            $cell->setFontSize('30');
        });
    }

    public function getBrandHeaderSection($excel, $color, $fromdate, $todate, $locationName, $garageCode, $getSummary)
    {
        $excel->mergeCells('A2:G2');
        $cellValue = "Checkin Checkout Summary-" . $getSummary . ' ' .  date('m-d-Y', strtotime($fromdate)) .  ' - ' . date('m-d-Y', strtotime($todate));
        $cellValue .= "\nPrint Date - " .  date('m-d-Y', strtotime('now'));
        $excel->setCellValue('A2', "$cellValue");
        $excel->getStyle('A2')->getAlignment()->setWrapText(true);
        $excel->getRowDimension(2)->setRowHeight(80);
        $excel->getRowDimension(1)->setRowHeight(60);

        $excel->cell('A2:G2', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            $cell->setBackground($color);
            $cell->setFontColor('#ffffff');
            $cell->setBackground('#D6DCE4');
            $cell->setFontColor('#040D12');
            $cell->setFontSize('18');
        });
        //location name changes

        $location = "Location Name \r" . $locationName;
        $excel->mergeCells('H2:P2');
        $excel->setCellValue('H2', "$location");
        $excel->getStyle('H2')->getAlignment()->setWrapText(true);

        $excel->cell('H2:P2', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            $cell->setBackground('#D6DCE4');
            $cell->setFontColor('#040D12');

            $cell->setFontSize('18');
        });
        //end location
        $excel->mergeCells('Q2:X2');
        $locationId = "Location ID \n" . $garageCode;
        $excel->setCellValue('Q2', "$locationId");
        $excel->getStyle('Q2')->getAlignment()->setWrapText(true);

        $excel->cell('Q2:X2', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            $cell->setBackground($color);
            $cell->setFontColor('#ffffff');
            $cell->setBackground('#D6DCE4');
            $cell->setFontColor('#040D12');
            $cell->setFontSize('18');
        });
        //Heading 
        $excel->getStyle('A1')->getAlignment()->setWrapText(true);

        $excel->cell('A1:X1', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            $cell->setBackground($color);
            $cell->setFontColor('#ffffff');
            $cell->setFontSize('30');
        });
    }

    public function preloadTimezonesForTickets($tickets)
    {
        // Unique Facility & Partner IDs
        $facilityIds = $tickets->pluck('facility_id')->filter()->unique();
        $partnerIds = $tickets->pluck('partner_id')->filter()->unique();

        // Fetch all timezones in one query
        $facilities = Facility::whereIn('id', $facilityIds)->pluck('timezone', 'id');
        $partners = OauthClient::whereIn('partner_id', $partnerIds)->pluck('partner_id', 'partner_id');
        $partnerTimezones = UserPaymentGatewayDetail::whereIn('user_id', $partners->values())->pluck('timezone', 'user_id');

        // Create an indexed timezone array for tickets
        $ticketTimezones = [];
        foreach ($tickets as $ticket) {
            $timezone = $facilities[$ticket->facility_id] ?? null;

            // If facility timezone is not set, use partner timezone
            if (!$timezone && isset($partners[$ticket->partner_id])) {
                $timezone = $partnerTimezones[$partners[$ticket->partner_id]] ?? null;
            }

            // Store timezone by ticket ID
            $ticketTimezones[$ticket->id] = $timezone ?? config('app.timezone');
        }

        return $ticketTimezones;
    }

    protected function transientcalculatePayablePrice($val, $tickettimezonedata)
    {
        $tickeAmount = 0;
        $isMember = 0;

        // Set the correct timezone based on facility or partner ID
        $ticketObj = Ticket::find($val->id);
        static $lastTimezone = null; // Static variable to retain value across calls

        if (isset($tickettimezonedata[$val->id])) {
            $currentTimezone = $tickettimezonedata[$val->id];

            // Ensure timezone is set only when changed
            if ($lastTimezone !== $currentTimezone) {
                date_default_timezone_set($currentTimezone);
                $lastTimezone = $currentTimezone;
            }
        }



        // If event case applies, return early
        if ($this->checkEventCaseBeforePayableCalculate($ticketObj) && $ticketObj->event_id > 0) {
            return $tickeAmount;
        }

        // Directly return 0 if the ticket has an event user
        if (!is_null($ticketObj->event_user_id)) {
            return '0.00';
        }

        // Determine arrival time and rate duration
        $arrival_time = !empty($ticketObj->payment_date) ?
            Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->payment_date) :
            Carbon::createFromFormat('Y-m-d H:i:s', $ticketObj->checkin_time);

        $rateDiffInHour = !empty($ticketObj->payment_date) ?
            $ticketObj->getCheckOutCurrentTime(true, $ticketObj->payment_date) :
            $ticketObj->getCheckOutCurrentTime(true);

        // Gated facility pricing logic
        if (!empty($ticketObj->facility) && $ticketObj->facility->is_gated_facility == '1') {

            $rate = (!empty($ticketObj->payment_date)) ?
                $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, true, null, false, false, '0', $isMember) :
                $ticketObj->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
            // Special case for check-in without checkout
            if ($ticketObj->is_checkin == '1' && $ticketObj->is_checkout == '0' && !empty($ticketObj->checkout_time)) {
                $rate = ['price' => $ticketObj->parking_amount];
            }
        } else {
            // Ungated facility - check promotions & validations
            $tickeAmount = $this->newcheckPromotionsAndValidations($ticketObj);
            return $tickeAmount;
        }

        // Tax and processing fee calculations
        $taxFee = $ticketObj->facility->getTaxRate($rate);
        $processingFee = empty($ticketObj->payment_date) ? $ticketObj->facility->getProcessingFee(0) : '0.00';

        // Final ticket amount calculation
        $parkingAmount = $rate['price'] ?? 0;
        return ($parkingAmount > 0) ? $parkingAmount + $taxFee + $processingFee : '0.00';
    }

    protected function checkEventCaseBeforePayableCalculate($ticket)
    {
        // Case 1 : estimated checkout time > current time then user in in event
        // Case 2 : estimated checkout time < current time event close but user not exist or checked out (Still in Garage)
        // White listed uses but no event is is present + case 1 
        // White listed uses but no event is is present + case 2 

        $latestEstimatedCheckout =  !empty($ticket->estimated_checkout) ? $ticket->estimated_checkout : $ticket->payment_date;
        $overstay = $ticket->overstay;
        if ($overstay->count() > 0) {
            foreach ($overstay as $key => $value) {
                $latestEstimatedCheckout = !empty($value->estimated_checkout) ? $value->estimated_checkout : $value->payment_date;
            }
        }
        $currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
        $eventCheckoutimeStatus = Carbon::parse($latestEstimatedCheckout)->gt($currentTime);

        if (!empty($ticket->event_id) && $eventCheckoutimeStatus) {
            return true;
        } else if (!empty($ticket->event_id) && $eventCheckoutimeStatus == false) {
            return false;
        } else if (empty($ticket->event_id) && $eventCheckoutimeStatus) {
            return true;
        } else if (empty($ticket->event_id) && $eventCheckoutimeStatus == false) {
            return false;
        }
    }

    protected function checkPromotionsAndValidations($ticketObj)
    {
        $payable = $ticketObj->grand_total;
        $extendDetails = QueryBuilder::ticketExtendDetails($ticketObj->id);
        if ($ticketObj->is_extended) {
            $payable += $extendDetails['extend_total'];
        }
        if (!empty($ticketObj->facility) && $ticketObj->facility->is_gated_facility == '1') {    // gated 
            // if required to do for gated not for now. 
        } else {  // ungated
            // Check Discount if any applied 
            if ($ticketObj->promocode && $ticketObj->discount_amount > 0) {
                $payable  =  (($ticketObj->grand_total + $extendDetails['extend_total']) - ($ticketObj->discount_amount + $extendDetails['extend_discoountAmount']));
            }
            // check Validation if any applied 
            if ($ticketObj->paid_by > 0 && $ticketObj->paid_amount > 0) {
                $payable  =  (($ticketObj->grand_total + $extendDetails['extend_total']) - ($ticketObj->paid_amount));
            }
        }
        return $payable;
    }

    protected function newcheckPromotionsAndValidations($ticketObj)
    {
        $payable = $ticketObj->grand_total;
        $extendDetails = QueryBuilder::newticketExtendDetails($ticketObj);
        if ($ticketObj->is_extended) {
            $payable += $extendDetails['extend_total'];
        }
        if (!empty($ticketObj->facility) && $ticketObj->facility->is_gated_facility == '1') {    // gated 
            // if required to do for gated not for now. 
        } else {  // ungated
            // Check Discount if any applied 
            if ($ticketObj->promocode && $ticketObj->discount_amount > 0) {
                $payable  =  (($ticketObj->grand_total + $extendDetails['extend_total']) - ($ticketObj->discount_amount + $extendDetails['extend_discoountAmount']));
            }
            // check Validation if any applied 
            if ($ticketObj->paid_by > 0 && $ticketObj->paid_amount > 0) {
                $payable  =  (($ticketObj->grand_total + $extendDetails['extend_total']) - ($ticketObj->paid_amount));
            }
        }
        return $payable;
    }
}
