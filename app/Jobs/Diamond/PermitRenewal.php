<?php

namespace App\Jobs\Diamond;

use Exception;
use App\Jobs\Job;

use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;

use App\Services\LoggerFactory;
use App\Http\Helpers\QueryBuilder;

use App\Classes\DatacapPaymentGateway;
use App\Classes\PlanetPaymentGateway;
use App\Classes\HeartlandPaymentGateway;

use App\Models\User;
use App\Models\PermitRequest;
use App\Models\PermitRate;
use App\Models\PermitRequestRenewHistory;

use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;

use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\HeartlandPaymentProfile;

class PermitRenewal extends Job implements ShouldQueue
{
	use InteractsWithQueue, SerializesModels;

    protected $permit;
    protected $log;
	protected $user;
	protected $permitRate;
	protected $partnerDetails;
	protected $partner_name;
	protected $facility_brand_setting;
	protected $brand_setting;
	protected $paymentProfile;
	protected $business_user_id;
	/**
     * Create a new job instance.
     *
     * @return void
     */

	public function __construct($permit, $log)
    {
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/are/permitRenewal')->createLogger('permitrenewjob');    

        $this->permit = $permit;    

		$this->user 					= User::where('id', $this->permit->user_id)->first();
		$this->permitRate 				= PermitRate::where('id', $this->permit->permit_rate_id)->first();

		// Partner Data
		$this->partnerDetails 			= User::where('id', $this->permit->partner_id)->first();

		// Brand Settings
		$this->facility_brand_setting 	= FacilityBrandSetting::where('facility_id', $this->permit->facility_id)->first();
		$this->brand_setting 			= BrandSetting::where('user_id', $this->permit->partner_id)->first();
		
		#DD PIMS-11336 SUBORIDNATE
		if(!is_null($this->permit->business_id) && $this->permit->business_id > 0){
			$businessUser = User::where('id', $this->permit->business_id)->first();
		}		
		if(isset($businessUser) && $businessUser->payment_mode=='0'){
			$this->paymentProfile 			= HeartlandPaymentProfile::where('user_id', $this->permit->business_id)->where('is_default',1)->orderBy('id', 'DESC')->first();
			$this->business_user_id 		= $this->permit->business_id;
		}else{
			$this->paymentProfile 			= HeartlandPaymentProfile::where('user_id', $this->permit->user_id)->where('is_default',1)->orderBy('id', 'DESC')->first();
			$this->business_user_id 		= $this->permit->user_id;
		}
		#DD PIMS-11336 END

		if($this->facility_brand_setting){
			$this->paymentProfile->facility_logo_id = $this->facility_brand_setting->id;
		} else {
			$this->paymentProfile->logo_id  = $this->brand_setting->id;
		}
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
	{
		try {
			QueryBuilder::setCustomTimezone($this->permit->facility_id);

			$desired_end_date = date("Y-m-t");
			$desired_start_date = date('Y-m-01');

			$permitRenewDate = date('Y-m-d h:i:s');

			$this->log->info('Under Permit Renewal handle function start.');
			$this->log->info("Request For Permit Renewal :" . $this->permit);

			$permitData = PermitRequest::with(['facility','user','PermitVehicle'])->where('id',$this->permit->id)->first();

			$this->log->info("Permit Rate Found " . $permitData->account_number . "--"   . json_encode($this->permitRate));


			if ($this->permitRate->rate == '0.00') {
				QueryBuilder::createPermitRequestHistoryNew($this->permit);   
				$maxDays=date('t');
				
				$permitData = QueryBuilder::populatePermitValuesOnRenewal($permitData,$desired_end_date,$maxDays,$this->permitRate->rate,NULL,++$permitData->transaction_retry);
				$permitData->save();

				if ($permitData->facility_id == config('parkengage.WAILUKU_FACILITY')) {
					$view = 'are.permit-renew-free';
				} else {
					$view = 'are-diamond.permit-renew';
				}

				$this->log->info("Free Permit Renew Permit Id #:" . $permitData->account_number);
				$subject = "Your Permit #". $permitData->account_number." has been Renewed Successfully";

				// Brand Setting Details
				if ($this->facility_brand_setting) {
					$permitData->facility_logo_id = $this->facility_brand_setting->id;
				}else{
					$permitData->logo_id  = $this->brand_setting->id;
				}

				// Send Mail
				$this->sendMail($permitData, $view, $subject);
			} else if ($this->permitRate->rate > "0.00") {
				$final_amount = $this->permitRate->rate;
				$rateValidate = QueryBuilder::permitRenewCalculation((object) [], $permitData, $final_amount, 1);
				$final_amount = $rateValidate['total_amount'];
				if (isset($permitData->facility->FacilityPaymentDetails) && ($permitData->facility->FacilityPaymentDetails->facility_payment_type_id == '1')) { 
					dd('stop planet');
				}else if (isset($permitData->facility->FacilityPaymentDetails) && ($permitData->facility->FacilityPaymentDetails->facility_payment_type_id == '2')) {
					dd('stop datacap');
				}else if (isset($permitData->facility->FacilityPaymentDetails) && ($permitData->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
					

					$this->log->info("Payment Profile Data --" . $permitData->account_number . "--"  . json_encode($this->paymentProfile));
					$amount = ($permitData->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $final_amount;

					if ($this->paymentProfile) {
						$request = new Request([
							'Amount'   => $amount,
							'total'   => $amount,
							'token' 			=> $this->paymentProfile->token,
							'zipcode' 			=> $this->paymentProfile->zipcode,
							'card_last_four' 		=> $this->paymentProfile->card_last_four,
							'expiration_date' 		=> $this->paymentProfile->expiry,
						]);
						$this->log->info("Payment Request to HL Data --" . $permitData->account_number . "--" . json_encode($request->all()));
						try {
							$paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $permitData->facility);
							if (isset($paymentResponse) && $paymentResponse->responseMessage == 'APPROVAL') {
								$this->log->info("Heartland Payment Success Response to Permit Id #:" .  $permitData->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode . "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId);
								QueryBuilder::createPermitRequestHistoryNew($permitData);
								$user_id = $permitData->user_id;
								$authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request,$paymentResponse, $this->business_user_id);  #PIMS-11336
								$this->log->info("Payment Transaction Data make Heartland Payment -- " .  $permitData->account_number. "--" . json_encode($authorized_anet_transaction));
								$maxDays=date('t');
								$permitData = QueryBuilder::populatePermitValuesOnRenewal($permitData,$desired_end_date,$maxDays,$this->permitRate->rate,$rateValidate, $permitData->transaction_retry + 1,$authorized_anet_transaction->id,$amount);
								$permitData->save();

								$expiry    = $this->paymentProfile->expiry;
								if($expiry){
									$expiry_data = str_split($expiry, 2);
									$this->paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
								} else {
									$this->paymentProfile->card_expiry = "-";
								}

								if ($this->facility_brand_setting) {
									$this->paymentProfile->facility_logo_id = $this->facility_brand_setting->id;
								} else {
									$this->paymentProfile->logo_id  = $this->brand_setting->id;
								}

								if ($permitData->facility_id == config('parkengage.WAILUKU_FACILITY')) {
									$view = 'are.permit-renew';
								}else{
									$view = 'are-diamond.permit-renew';
								}
								
								$subject = "Autopayment Confirmation for Permit # " . $permitData->account_number;

								// Send Mail
								$this->sendMail($permitData, $view, $subject);
							} else {
								$this->log->info("Heartland Payment Failure Response to Permit Id #:" .  $permitData->account_number . "-- Response Message: " . $paymentResponse->responseMessage . "-- Auth Code" . $paymentResponse->transactionReference->authCode . "-- Card Brand ID" . $paymentResponse->cardBrandTransactionId . "-- Transaction ID" . $paymentResponse->transactionReference->transactionId);
								$permitData->transaction_retry   = $permitData->transaction_retry + 1;
								$permitData->save();

								if ($this->facility_brand_setting) {
									$permitData->facility_logo_id = $this->facility_brand_setting->id;
								}else{
									$permitData->logo_id  = $this->brand_setting->id;
								}

								$day_of_month = date('d');
								if($day_of_month =='03'){
									$view='usm.permit-cancel-reminder';  
									//call skiData Api to cancel resuable ticket
									$subject = "Important Notice: Permit Cancellation";
									if ($permitData->skidata_id != '') {
										if ($permitData->facility_id == config('parkengage.WAILUKU_FACILITY')) {
											$this->cancelSkiData($permitData->id);
										}
										$permitData->cancelled_at = $permitRenewDate;
										$permitData->save();	
									}
								}else{
									if($permitData->facility_id==config('parkengage.WAILUKU_FACILITY')){
										$view='are.permit-renew-fail';  
									}else{
										$view='are-diamond.permit-renew-fail';  
									}	
									$subject = "Important: Action Required for Your Permit Payment";									
								}		
								$this->log->info("Mail subject " . $subject);
								
								// Send Mail
								$this->sendMail($permitData, $view, $subject);
							}
							$this->log->info("Heartland Payment Response to Permit Id #:" .  $permitData->account_number . "-- HL Gateway Response" . json_encode($paymentResponse));
						} catch (Exception $e) {
							$this->log->info("Heartland Payment Charge Error Response Permit Id #:" . $permitData->account_number . "--" . json_encode($e->getMessage()));
							if (str_contains($e->getMessage(), 'duplicate')) {

							// Send Mail
								$this->sendMail($permitData, $view, $subject);
							}
						} catch (Exception $e) {
							$this->log->info("Payment Charge Error Response Permit Id #:" . $permitData->account_number . "--" . json_encode($e->getMessage()));
							if (str_contains($e->getMessage(), 'duplicate')) {
						}else{
							$permitData->transaction_retry   = $permitData->transaction_retry + 1;
							$permitData->save();
								$expiry    = $this->paymentProfile->expiry;
							
								if ($expiry) {
									$expiry_data = str_split($expiry, 2);
									$this->paymentProfile->card_expiry = $expiry_data[0] . "/" . $expiry_data[1];
							} else {
									$this->paymentProfile->card_expiry = "-";
							}
							
							$day_of_month = date('d');
							if($day_of_month =='03'){
								$view='usm.permit-cancel-reminder';  
								//call skiData Api to cancel resuable ticket
								$subject = "Important Notice: Permit Cancellation";
								if ($permitData->skidata_id != '') {
									if ($permitData->facility_id == config('parkengage.WAILUKU_FACILITY')) {
										$this->cancelSkiData($permitData->id);
									}
									$permitData->cancelled_at = $permitRenewDate;
									$permitData->save();	
								}
							}else{
								if($permitData->facility_id==config('parkengage.WAILUKU_FACILITY')){
									$view='are.permit-renew-fail';  
								}else{
									$view='are-diamond.permit-renew-fail';  
								}	
								$subject = "Important: Action Required for Your Permit Payment";								
							}							
							
								$this->log->info("Mail subject " . $subject);
							// Send Mail
								$this->sendMail($permitData, $view, $subject);
							}
						}
					} else {
						$this->log->info("Payment Profile Not Found" . $permitData->account_number);
					}
				}
			}
            $this->log->info('Under handle function end.');    
		}catch (\Exception $th) {
            $this->log->info('Error ' . $th->getMessage() . ' File Name :  ' . $th->getFile() . ' Line ' . $th->getLine());
        }
	}


	public function cancelSkiData($permit_id)
	{
		try {
			$this->log->info("Get Permit Request id for cancel SkiData Autorenew" . json_encode($permit_id));
			$permit = PermitRequest::with(['facility'])->where("id", $permit_id)->first();
			//$guarage_code = $permit->facility->garage_code;
			$guarage_code = config('parkengage.SKIDATA_GUARAGE');
			$skidata_id =  $permit->skidata_id;
			$skiDataAuth = config('parkengage.SKIDATA_USERNAME') . ":" . config('parkengage.SKIDATA_PASSWORD');
			$dataAuthEncrption = base64_encode($skiDataAuth);
			$headers = [
				'Authorization: Basic ' . $dataAuthEncrption,
				'Content-Type: application/json',
			];
			$curl = curl_init();
			$url = config('parkengage.SKIDATA_CREATE_URL') . $guarage_code . '/' . $skidata_id;
			curl_setopt($curl, CURLOPT_URL, $url);
			curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
			curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
			$response = curl_exec($curl);
			curl_close($curl);
			$response = json_decode($response);
			$this->log->info("SkIData success completed.");
			return $response;
		} catch (\Exception $e) {
			$msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
			$this->log->error($msg);
		}
	}

	public function sendMail($permitData, $view, $subject)
    {

		try{ 
			$brand_setting  = $this->brand_setting;
			$partner_name   =  "Diamond Parking at (808) 451-2792 or email <NAME_EMAIL>";
			$paymentProfile =  isset($this->paymentProfile) ? $this->paymentProfile : "";
			#PIMS-11336 DD
			if($this->business_user_id == $permitData->business_id){
				$userEmail = $permitData->business->email;
			}else{
				$userEmail = $permitData->user->email;
			}
			$this->log->info("Permit Email sending:". $userEmail);
            Mail::send(
				$view, ['data' => $permitData, 'brand_setting' => $brand_setting,'paymentProfile'=>$paymentProfile,'partner_name'=>$partner_name], 
				function ($message) use($permitData,$subject,$userEmail) {
					$message->to($userEmail)->subject($subject);
					$message->from(config('parkengage.default_sender_email'));
				}
			);            
            $this->log->info("email sent:". $userEmail);
		}catch (\Exception $e)
        {
			$this->log->info('Issue in email sending ' . $e->getMessage() . ' File Name :  ' . $e->getFile() . ' Line ' . $e->getLine());
		}
		return true;
	}
}
