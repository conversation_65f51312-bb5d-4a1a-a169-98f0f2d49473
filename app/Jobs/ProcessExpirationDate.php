<?php

namespace App\Jobs;

use Exception;

use App\Services\Mailers\UserMailer;

use App\Classes\AuthorizeNet\Cim;

use App\Models\ExpirationDate;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

use App\Classes\ArApi;

/**
 * Sends expiration date email for monthly parking users who have payment methods that have expired
 */
class ProcessExpirationDate extends CronJob implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    const LOG_DIRECTORY = "logs/expiration-date";

    protected $expirationDate;
    protected $mpUser;

    protected $successLog;
    protected $errorLog;

    // True if we are processing expirations that expire next month
    protected $next;

    public function __construct(ExpirationDate $expirationDate, $next = false)
    {
        $this->next = $next;
        $this->expirationDate = $expirationDate;
        $this->mpUser = $expirationDate->paymentProfile->cim->monthlyParkingUser;
    }

    public function handle()
    {
        try {
            $this->successLog = $this->createLogger('success');
            $this->errorLog = $this->createLogger('error');

            // Check the account is active in the AR API
            $this->mpUser->updateAddressFromArAPI();

            if (!$this->mpUser->active) {
                $this->successLog->info("MP user {$this->mpUser->account_number} no longer active, not sending expiration date email.");
                return;
            }

            $this->send();

            $this->successLog->info($this->getLogMessage());
        } catch (Exception $e) {
            $this->errorLog->error($e->getMessage());
            throw $e;
        }
    }

    protected function send()
    {
        $paymentLastFour = (new Cim())
                                ->setMonthlyParkingUser($this->mpUser)
                                ->setPaymentProfile($this->expirationDate->paymentProfile->payment_profile)
                                ->getPaymentLastFour();
        $data = [
            'account' => $this->expirationDate->paymentProfile->cim->monthlyParkingUser,
            'payment_last_four' => $paymentLastFour,
            'expiration' => $this->expirationDate->formatted,
            'next' => $this->next,
            'is_autopay' => false
        ];

        (new UserMailer($this->mpUser->user))->sendMail('Payment method expiring soon', $data, 'autopay.expired');
    }

    protected function getLogMessage()
    {
        $monthlyParkingUserId = $this->expirationDate->paymentProfile->cim->monthlyParkingUser->id;

        return "Expiration date {$this->expirationDate->id} expiring on {$this->expirationDate->formatted} for monthly parking user $monthlyParkingUserId.";
    }
}
