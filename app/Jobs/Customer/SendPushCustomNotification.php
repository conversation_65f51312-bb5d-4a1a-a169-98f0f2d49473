<?php

namespace App\Jobs\Customer;

use Exception;
use App\Exceptions\ApiGenericException;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
//use App\Models\Valet\ValetParkingRequest;
use App\Classes\PushNotification;
use App\Models\Devicetoken;
use App\Jobs\Job;
use App\Models\User;
use App\Models\Ticket;
use App\Models\ParkEngage\Notification;
use App\Services\LoggerFactory;
use Log;





class SendPushCustomNotification extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    //public $ticket_number;
    //public $type;
    public $user_id;
    protected $log;

    

    public function __construct($user_id, $logFactory)
    {
        $this->user_id = $user_id;
        $this->log = $logFactory->setPath('logs/notification/push-notification')->createLogger('push-notification');
        //$this->ticket_number;
    }

    public function handle()
    {
        try 
        {         
            $customerDetails = false;
            $customerDetails = User::with(['userDeviceToken'])->where('id',$this->user_id)->orderBy('id', 'desc')->first();
            $this->log->info("user info: ".json_encode($customerDetails));
            
            if($customerDetails && $customerDetails->is_notification_enabled)
            {
                //send Push notification
                $deviceTokens = Devicetoken::where('user_id',$customerDetails->id)->orderBy('id', 'desc')->get();
                
                //$checkout = Ticket::select('user_id','ticket_number as order_number','checkout_datetime as check_out_time')->where(['user_id'=>$this->user_id])->whereNotNull('check_in_datetime')->where('ticket_number',$this->ticket_number)->first();   

                //dd($activeCheckIn);               
                if(count($deviceTokens)>0)
                {   
                        $message = "Thank you"." ".ucfirst($customerDetails->name).","." "."Please visit again."; 
                        $title = "Checkout Successfully";                       

                        $msg_payload = array (
                                        'message' => $message,               
                                        'title' => $title,               
                                        // 'set_payload'=> array('check-in-details' =>$checkout, 'title' =>$title,
                                        // 'notification_type'=>"CHECKOUT")  // add new payload 
                                        'set_payload'=> array('title' =>$title,
                                        'notification_type'=>"Custom")  // add new payload 
                        );
                        
                        foreach($deviceTokens as $deviceToken)
                        {
                            $deviceType = isset($deviceToken->device_type)?$deviceToken->device_type:'';
                            $deviceToken = isset($deviceToken->device_token)?$deviceToken->device_token:'';
                            $this->sendPushNotification($msg_payload, $deviceType, $deviceToken); 
                            
                            $notificationData = new Notification();
                            $notificationData->user_id = $this->user_id;
                            $notificationData->title = $title;
                            $notificationData->message = $message;
                            $notificationData->save();
                            $this->log->info("Send Notification Message: '{$message}' and title: '{$title}' for user_id '{$this->user_id}'");                            

                        }

                }
            }
            else{
                $this->log->error("Notification not enable for this user_id {$this->user_id}");
                //return 'Notification not enable for this user'.$this->user_id;
                //$this->errorLog->error("Notification not enable for this user_id {$this->user_id}");

            }
        } catch (Exception $e) {
            throw $e;
        }
    }
     
    public function sendPushNotification($msg_payload, $deviceType, $deviceToken )
    {	
        return $response = PushNotification::sendPushNotification($deviceType, $deviceToken, $msg_payload);        
    }

    


}
