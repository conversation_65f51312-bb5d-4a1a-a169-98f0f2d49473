<?php

namespace App\Jobs\Customer;

use Exception;
use App\Exceptions\ApiGenericException;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
//use App\Models\Valet\ValetParkingRequest;
use App\Classes\PushNotification;
use App\Models\Devicetoken;
use App\Jobs\Job;
use App\Models\User;
use App\Models\Ticket;
use App\Models\ParkEngage\MobileNotificationConfiguration;
use App\Classes\PushNotificationNew;

class SendPushNotifyCheckout extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    // const MAIL_QUEUE = 'iqp-pastdue-mail';
    public $ticket_number;
    public $type;
    public $user_id;
    public $notification_type;
    public $msg;


    public function __construct($user_id, $ticket_number, $notification_type, $msg)
    {
        $this->user_id = $user_id;
        $this->ticket_number = $ticket_number;
        $this->notification_type = $notification_type;
        $this->msg = $msg;
    }

    public function handle()
    {
        try {
            $customerDetails = false;
            $customerDetails = User::where('id', $this->user_id)->first();

            if ($customerDetails) {
                //send Push notification
                $deviceTokens = Devicetoken::where('user_id', $customerDetails->id)->orderBy('id', 'desc')->get();
                if (isset($this->msg)) {
                    $checkout = Ticket::select('facility_id', 'user_id', 'ticket_number', 'checkout_datetime as check_out_time')->where('ticket_number', $this->ticket_number)->first();
                    if (count($deviceTokens) > 0) {
                        //$message = "Vehicle [" . $this->msg['license_plate'] ."] with Ticket ".$this->msg['ticket_number']." is at the checkout gate."; 
                        //$title = "Vehicle [".$this->msg['license_plate']."] Arrived at Checkout";
                        $message = $this->msg['message'];
                        $title = $this->msg['title'];

                        $config = MobileNotificationConfiguration::where("facility_id", $checkout->facility_id)->first();
                        $type = "CHECKOUT";
                        if (isset($this->notification_type) && $this->notification_type != '') {
                            $type = $this->notification_type;
                        }
                        $msg_payload = array(
                            'message' => $message,
                            'title' => $title,
                            'set_payload' => array(
                                'check-in-details' => $checkout,
                                'title' => $title,
                                'notification_type' => $type,
                                'ticket_number' => $checkout->ticket_number,
                                'license_plate' => $this->msg['license_plate'],
                                'gate_number' => $this->msg['gate_number'],
                                'gate_type' => $this->msg['gate_type'],
                                'gate_name' => $this->msg['gate_name']
                            )  // add new payload 
                        );
                        foreach ($deviceTokens as $deviceToken) {
                            $deviceType = isset($deviceToken->device_type) ? $deviceToken->device_type : '';
                            $deviceToken = isset($deviceToken->device_token) ? $deviceToken->device_token : '';
                            $this->sendPushNotificationNew($msg_payload, $deviceType, $deviceToken, $customerDetails->id, $config);
                        }
                    }
                } else {
                    $checkout = Ticket::select('facility_id', 'user_id', 'ticket_number', 'checkout_datetime as check_out_time')->where(['user_id' => $this->user_id])->whereNotNull('check_in_datetime')->where('ticket_number', $this->ticket_number)->first();
                    if (count($deviceTokens) > 0) {
                        $message = "Thank you" . " " . ucfirst($customerDetails->name) . "," . " " . "Please visit again.";
                        $title = "Checkout Successfully";

                        $config = MobileNotificationConfiguration::where("facility_id", $checkout->facility_id)->first();
                        $type = "CHECKOUT";
                        if (isset($this->notification_type) && $this->notification_type != '') {
                            $type = $this->notification_type;
                        }
                        $msg_payload = array(
                            'message' => $message,
                            'title' => $title,
                            'set_payload' => array(
                                'check-in-details' => $checkout,
                                'title' => $title,
                                'notification_type' => $type,
                                'ticket_number' => $checkout->ticket_number
                            )  // add new payload 
                        );

                        foreach ($deviceTokens as $deviceToken) {
                            $deviceType = isset($deviceToken->device_type) ? $deviceToken->device_type : '';
                            $deviceToken = isset($deviceToken->device_token) ? $deviceToken->device_token : '';

                            $this->sendPushNotification($msg_payload, $deviceType, $deviceToken, $customerDetails->id, $config);
                        }
                    }
                }
            }
        } catch (Exception $e) {
            throw $e;
        }
    }

    public function sendPushNotification($msg_payload, $deviceType, $deviceToken, $userid, $config = null)
    {
        return $response = PushNotification::sendPushNotification($deviceType, $deviceToken, $msg_payload, $userid, $config);
    }

    public function sendPushNotificationNew($msg_payload, $deviceType, $deviceToken, $userid, $config = null)
    {
        return $response = PushNotificationNew::sendPushNotification($deviceType, $deviceToken, $msg_payload, $userid, $config);
    }
}
