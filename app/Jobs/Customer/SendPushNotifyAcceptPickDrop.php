<?php

namespace App\Jobs\Customer;

use Exception;
use App\Exceptions\ApiGenericException;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
//use App\Models\Valet\ValetParkingRequest;
use App\Classes\PushNotification;
use App\Models\Devicetoken;
use App\Jobs\Job;
use App\Models\User;
use App\Models\Ticket;
use App\Models\ParkEngage\Notification;
use App\Models\ParkEngage\MobileNotificationConfiguration;
use App\Classes\PushNotificationNew;


class SendPushNotifyAcceptPickDrop extends Job implements ShouldQueue
{
  use InteractsWithQueue, SerializesModels;

  // const MAIL_QUEUE = 'iqp-pastdue-mail';
  public $request_id;
  public $type;
  public $user_id;
  public $facility_name;
  public $notification_type;
  public $app_name;
  const WELCOME_CHECK_IN_MSG = "Welcome ";
  const NOTIFICATION_CHECK_IN = "CHECK_IN";
  const WELCOME_CHECK_IN_MSG1 = " You have successfully started your parking session at ";
  public function __construct($user_id, $request_id, $type, $facility_name, $notification_type, $app_name)
  {
    $this->request_id = $request_id;
    $this->user_id = $user_id;
    $this->type = $type;
    $this->facility_name = $facility_name;
    $this->notification_type = $notification_type;
    $this->app_name = $app_name;
    
  }

  public function handle()
  {
    try {


      $customerDetails = false;
      $customerDetails = User::where('id', $this->user_id)->first();

          if($customerDetails && $customerDetails->is_notification_enabled)
          {
             //send Push notification
             $deviceTokens = Devicetoken::where('user_id',$customerDetails->id)->orderBy('id', 'desc')->get();  
             $activeCheckIn = Ticket::select('vehicle_id','user_id','ticket_number as order_number','check_in_datetime as check_in_time','reservation_id')->where(['user_id'=>$this->user_id])->whereNotNull('check_in_datetime')->whereNULL('checkout_datetime')->first();

             if(count($deviceTokens)>0)
             {   
                     if($this->type  ==0)
                     {
                        $message = self::WELCOME_CHECK_IN_MSG."".ucfirst($customerDetails->name).",".self::WELCOME_CHECK_IN_MSG1." ".$this->facility_name; 
                        $title = "Checkin Successfully";
                      }else{
                        $message = self::WELCOME_CHECK_IN_MSG."".ucfirst($customerDetails->name).",".self::WELCOME_CHECK_IN_MSG1." ".$this->facility_name; 
                        $title = "Checkin Successfully"; 
                      }

                      $ticket = Ticket::where('user_id', $customerDetails->id)->orderBy('id', 'desc')->first();

                      $type = self::NOTIFICATION_CHECK_IN;
                      
                      if(isset($this->app_name) && $this->app_name != ''){
                        $config = MobileNotificationConfiguration::where("partner_id",$ticket->partner_id)->where("app_name",$this->app_name)->first();
                      }else{
                        $config = MobileNotificationConfiguration::where("facility_id",$ticket->facility_id)->first();
                      }
                      

                      if(isset($this->notification_type) && $this->notification_type != ''){
                        $type = $this->notification_type;
                      }
                      $msg_payload = array (
                                     'message' => $message,               
                                     'title' => $title,               
                                     'set_payload'=> array('check-in-details' =>$activeCheckIn, 'title' =>$title,
                                     'notification_type'=>$type,
                                     'ticket_number'=>$ticket->ticket_number)  // add new payload 
                      );

                     $notificationData = Notification::where('user_id',$customerDetails->id)->where('ticket_number',$ticket->ticket_number)->where('event_type',self::NOTIFICATION_CHECK_IN)->orderBy('id', 'desc')->first();

                     if(!$notificationData) {
                     foreach($deviceTokens as $deviceToken)
                        {
                            $deviceType = isset($deviceToken->device_type)?$deviceToken->device_type:'';
                            $deviceToken = isset($deviceToken->device_token)?$deviceToken->device_token:'';
                             $this->sendPushNotificationNew($msg_payload, $deviceType, $deviceToken,$customerDetails->id, $config);
                        }
                        $notificationData = new Notification();
                        $notificationData->user_id = $customerDetails->id;
                        $notificationData->title = $title;
                        $notificationData->message = $message;
                        $notificationData->ticket_number = $ticket->ticket_number;
                        $notificationData->device_type = $deviceType;
                        $notificationData->event_type = self::NOTIFICATION_CHECK_IN;   
                        $notificationData->save();
                     }


         

                   }
             }
        } catch (Exception $e) {
          throw $e;
        }
    }

  public function sendPushNotification($msg_payload, $deviceType, $deviceToken, $userid, $config = null)
  {
    return $response = PushNotification::sendPushNotification($deviceType, $deviceToken, $msg_payload, $userid, $config);
  }

  public function sendPushNotificationNew($msg_payload, $deviceType, $deviceToken, $userid, $config = null)
    {
        return $response = PushNotificationNew::sendPushNotification($deviceType, $deviceToken, $msg_payload, $userid, $config);
    }
}
