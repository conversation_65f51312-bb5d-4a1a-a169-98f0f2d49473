<?php

namespace App\Jobs\Customer;

use Exception;
use App\Exceptions\ApiGenericException;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
//use App\Models\Valet\ValetParkingRequest;
use App\Classes\PushNotification;
use App\Models\Devicetoken;
use App\Jobs\Job;
use App\Models\User;
use App\Models\Ticket;

class SendPushNotifyAcceptPayment extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

   // const MAIL_QUEUE = 'iqp-pastdue-mail';
    public $request_id;
    public $type;
    public $user_id;
    const WELCOME_CHECK_IN_MSG = "Welcome ";
    const NOTIFICATION_CHECK_IN = "PAYMENT";
    const WELCOME_CHECK_IN_MSG1 = " You have successfully  make the transaction.";
    public function __construct($user_id, $request_id, $type)
    {
        $this->request_id = $request_id;
        $this->user_id = $user_id;
        $this->type = $type;
    }

    public function handle()
    {
        try {
           
            
            $customerDetails = false;
            $customerDetails = User::where('id',$this->user_id)->first();
            
            if($customerDetails)
          {
             //send Push notification
             $deviceTokens = Devicetoken::where('user_id',$customerDetails->id)->orderBy('id', 'desc')->get();  

             
             $activeCheckIn = Ticket::select('vehicle_id','user_id','ticket_number as order_number','check_in_datetime as check_in_time')->where(['user_id'=>$this->user_id])->whereNotNull('check_in_datetime')->whereNULL('checkout_datetime')->first();   

             //dd($activeCheckIn);               
             if(count($deviceTokens)>0)
             {   
                     if($this->type  ==0)
                     {
                        $message = self::WELCOME_CHECK_IN_MSG."".ucfirst($customerDetails->name).",".self::WELCOME_CHECK_IN_MSG1; 
                        $title = "Checkin Request Accepted";
                      }else{
                        $message = self::WELCOME_CHECK_IN_MSG."".ucfirst($customerDetails->name).",".self::WELCOME_CHECK_IN_MSG1; 
                        $title = "Checkin Request Accepted"; 
                      }

                      $msg_payload = array (
                                     'message' => $message,               
                                     'title' => $title,               
                                     'set_payload'=> array('check-in-details' =>$activeCheckIn, 'title' =>$title,
                                     'notification_type'=>self::NOTIFICATION_CHECK_IN)  // add new payload 
                      );

                     foreach($deviceTokens as $deviceToken)
                     {
                         $deviceType = isset($deviceToken->device_type)?$deviceToken->device_type:'';
                         $deviceToken = isset($deviceToken->device_token)?$deviceToken->device_token:'';

                          $this->sendPushNotification($msg_payload, $deviceType, $deviceToken);
                          
                     }

                   }
             }
        } catch (Exception $e) {
            throw $e;
        }
    }
     
    public function sendPushNotification($msg_payload, $deviceType, $deviceToken )
    {	
        return $response = PushNotification::sendPushNotification($deviceType, $deviceToken, $msg_payload);
        
    }
    


}
