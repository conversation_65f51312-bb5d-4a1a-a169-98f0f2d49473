<?php

namespace App\Jobs;


use Storage;
use Exception;

use Carbon\Carbon;
use App\Services\LoggerFactory;
use Monolog\Logger;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;

use App\Classes\LoyaltyProgram;
use App\Models\User;
use App\Models\GoogleWallet\LoyaltyPass;
use App\Models\LoyaltyUserAccounts;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

/**
 * Processes credit loyalty points to google wallet  for a single loyalty pass user
 * Steps:
 * 1. Get loyalty points for user
 * 2. Update loyalty points for user at google wallet loyalty card
 * @package App\Jobs
 */
class ProcessGooglePayLoyalty extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;
    const MAIL_QUEUE = 'iqp-loyalty-wallet';
    const LOG_DIRECTORY = 'logs/google-pay';

    protected $loyaltyobject;
    protected $user;
   
    // Responses from authorize.net
    protected $responses = [];

    // Exceptions caught in the process of running transactions
    protected $exceptions = [];


    protected $successLog;
    protected $errorLog;

    public function __construct(LoyaltyPass $loyaltyobject)
    {   
        $logFactory = new LoggerFactory();
        $this->loyaltyobject = $loyaltyobject;
        $this->user = User::where('id', $this->loyaltyobject->user_id)->first();
        $this->errorLog = $logFactory->setPath('logs/google-pay')->createLogger('loyalty-pass-error');
        $this->successLog = $logFactory->setPath('logs/google-pay')->createLogger('loyalty-pass-success');   
    }

    public function handle()
    {
        try {
            include_once("/var/www/sites/iqp-api/googlewallet/LoyaltyObject.php"); 
            $loyalty = new \LoyaltyObject();
            $service = $loyalty->getWalletLoyaltyObject();
            $loyaltyPoints = 0;
            $account = LoyaltyUserAccounts::where('user_id', $this->user->id)
					->orderBy('id', 'DESC')
					->first();
            $accountData = LoyaltyProgram::getUserAccounts($this->user->email, $this->user->id);
            if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
                $loyaltyPoints = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
            } 
            $profileResponse = LoyaltyProgram::getUserProfile($this->user->email);
            $firstName = isset($profileResponse['data']['firstName']) ? $profileResponse['data']['firstName'] : '';
            $lastName =  isset($profileResponse['data']['lastName']) ? $profileResponse['data']['lastName'] : '' ;

            $dataSet = ['loyaltyPoints'=> $loyaltyPoints, 'loyaltyObjectId'=> $this->loyaltyobject->loyalty_object_id,'member_since' => $account->created_at,'reward_no'=> $account->reward_no,
                        'phone' => config('icon.webphone'),'location'=>$this->loyaltyobject->location,'name'=>$firstName.' '.$lastName];
            try {
               $response = $service->loyaltyobject->update($this->loyaltyobject->loyalty_object_id, \Loyalty::updateLoyaltyObjectPoints(config('loyalty.issuer_id'),config('loyalty.loyalty_class_id'), $dataSet));
            } catch(Exception $e){
               $this->errorLog->info("Loyalty Pass error for loyalty object {$this->loyaltyobject->loyalty_object_id}. loyalty pass {$e->getMessage()}");
            }

            $this->successLog->info("Loyalty Pass processed for loyalty object {$this->loyaltyobject->loyalty_object_id} successfully.");
        } catch (Exception $e) {
            $this->errorLog->error($e->getMessage());
            throw $e;
        }
    }


}

?>
