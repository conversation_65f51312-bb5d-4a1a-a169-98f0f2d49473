<?php

namespace App\Jobs;

use App\Classes\AuthorizeNet;
use App\Exceptions\AuthorizeNetException;
use Mail;
use Storage;
use Exception;
use Carbon\Carbon;
use Monolog\Logger;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;

Use App\Models\ParkEngage\UserMembership;
Use App\Models\ParkEngage\MembershipPayment;
use App\Models\ParkEngage\AutopayMethod;
use App\Models\AuthorizeNetTransaction;

use App\Services\Mailers\UserMailer;

use App\Classes\AuthorizeNet\Cim;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

/**
 * Processes an autopay transaction for a single monthly parking user
 * Steps:
 * 1. Verify that the payment methods expiration dates are valid. If not, send user an email and deactivate their autopay.
 * 2. Update the account's current balance from the AR API. This is handled automatically via laravel attributes on the MonthlyParkingUser model.
 * 3. Run transaction.
 * 4. Send email notification of successful/failed payment.
 *
 * @package App\Jobs
 */
class ProcessAutopayment extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    const MAIL_QUEUE = 'parkengage-autopay-mail';
    const LOG_DIRECTORY = 'logs/autorenew_membership';

    protected $membership_account;
    protected $methods;
    protected $userDescription;

    // Whether or not this is a dry run job
    protected $dry;

    // Amount user owes before auto pay
    protected $currentMonthBalance;
    protected $membership_plan_details;

    // Updated as payment methods are processed
    protected $updatedBalance;

    // Responses from authorize.net
    protected $responses = [];

    // Exceptions caught in the process of running transactions
    protected $exceptions = [];

    // Instances of AuthorizeNetTransaction for payments processed on this job
    protected $transactions = [];

    // Instances of Membership Payment that are processed for this job
    protected $payments = [];

    protected $successLog;
    protected $errorLog;

    const MONTHLY_PLAN_DAYS = 30;
    const ANNUAL_PLAN_DAYS = 365;
        
    public function __construct($account, $dry = false)
    {
        $this->membership_account = $account;
        $this->methods = AutopayMethod::where('user_membership_id', $this->membership_account->id)->orderBy('order', 'asc')->get();
        $this->userDescription = "Membership Account {$this->membership_account->id} User_id {$this->membership_account->user_id}";
        $this->dry = $dry;
    }

    public function handle()
    {
        try {

           $this->createLoggers();

            if (!$this->areAutopayMethodsValid()) {
                return;
            }

            // Record our AutoPay attempt
            $this->membership_account->last_autorenew = Carbon::now()->toDateTimeString();
            $this->membership_account->save();

            
            //get Plan Value as as the type set by user/ Monthly or Yearly Plan
            $this->membership_plan_details = isset($this->membership_account->membershipPlan)?$this->membership_account->membershipPlan:'';
           
            if(isset($this->membership_plan_details) && $this->membership_plan_details)
            {
            
               //for monthly and trial
               if($this->membership_account->plan_type == 1 || $this->membership_account->plan_type == 0 )
               {   
                  // user amount as per plan
                  $this->currentMonthBalance = $this->membership_plan_details->amount;
                  $this->updatedBalance = $this->membership_plan_details->amount;

                        
               }else  if($this->membership_account->plan_type == 2)
               {
                        // Update our balance from the plan
                        $this->currentMonthBalance = $this->membership_plan_details->annual_amount;
                        $this->updatedBalance = $this->membership_plan_details->annual_amount;
               }
               else{
                 // Update our balance from the AR API
                $this->currentMonthBalance = 0;
                $this->updatedBalance = 0;
               
             }
                
            }else{
                 // Update our balance from the AR API
                $this->currentMonthBalance = 0;
                $this->updatedBalance = 0;
               
            }
            
           
            
            // If there is no balance, log and end
            if ($this->currentMonthBalance <= 0) {
                $this->successLog->info("No Plan values Added for {$this->userDescription}, no automatic payments made.");
                return;
            }

            if ($this->dry) {
                $this->dryRunTransactions();
            } else {
                $this->runTransactions();
            }

            $this->chooseEmailSendFunction();

            // Improve this
            $this->successLog->info("Membership Auto Renew payment processed for {$this->userDescription}.");
        } catch (Exception $e) {
            $this->errorLog->error($e->getMessage());
            throw $e;
        }
    }

    protected function areAutopayMethodsValid()
    {
        // Check that we have autopay methods set up
        if (!$this->methods->count()) {
            $this->errorLog->error("No AutoPay methods set up for {$this->userDescription}.");
            return false;
        }

        // Check that we have not already processed auto pay for this user
        if (!$this->dry) {
            if (in_array($this->membership_account->autopay_processed, [UserMembership::AUTOPAY_COMPLETE, UserMembership::AUTOPAY_PARTIAL])) {
                $this->errorLog->error("AutoRenew Membership Payment has already been processed for {$this->userDescription}.");
                return false;
            }
        }

        // Check if any of our methods are expired
        if ($expired = $this->expiredMethods()) {
            $this->membership_account->last_autorenew = Carbon::now()->toDateTimeString();
            $this->membership_account->save();

            $message = "Expired payment method with ID {$expired->id} for {$this->userDescription}.";
            $this->errorLog->error($message);
            $this->sendMethodExpiredEmail($expired);
            return false;
        }

        return true;
    }

    /**
     * Check our payment methods to see if any have expired. If an expiration is not stored,
     * assume that the payment method is not expired.
     */
    protected function expiredMethods()
    {
        return $this->methods->first(
            function ($key, $method) {
                $expiration = $method->paymentProfile->expirationDate;
                if (!$expiration) {
                    return false;
                }

                return $expiration->is_expired;
            }
        );
    }

    /**
     * Run our transactions.
     * Note that in the case of multiple transactions, if the first transaction fails the total amount
     * should fall over to the second payment method.
     */
    protected function runTransactions()
    {
        $this->methods->each(
            function ($method) {
                try {
                    //check expiration for each autopay methods
                    $expiration = $method->paymentProfile->expirationDate;
                    if (!$expiration) {
                        //there is no record in anet_cc_expirations table. Assuming it is not expired
                    }
                    else if($expiration->is_expired){
                        $message = "Expired current payment method ( autopay method id = {$method->id} ) for {$this->userDescription}.";
                        $this->errorLog->error($message);
                        return;
                    }
                    //check if cim id is deleted for any autopay method
                    if(!$method->paymentProfile->cim){
                        return;
                    }
                    // Set up our Authorize.Net transaction
                    $authorizeNet = (new Cim())
                    ->setUser($this->membership_account->users)
                    ->isMembership()
                    ->setPaymentProfile($method->paymentProfile->payment_profile);

                    $paymentLastFour = $authorizeNet->getPaymentLastFour();
                    $description = $this->membership_account->user_id.' '.$this->membership_account->membership_plan_id .' -type-'.$this->membership_account->plan_type. ' ' . Cim::PAYMENT_AUTO . " Parkengage Web";

                    $invoice = $this->membership_account->id . ',' . $paymentLastFour;

                    $amount = $this->getAmountForMethod($method);

                    if ($amount === false) {
                        $this->successLog->info("No charge on payment method {$method->id} because remaining balance was less than or equal to 0.");
                        return; // Nothing to charge
                    } else {
                        $this->successLog->info("Going to charge $amount on method {$method->id}.");
                    }

                    // Make the payment
                    try {
                        $authorizeNet->createTransaction($amount, $description, $invoice)->executeTransaction();

                        // Update our remaining balance
                        $this->updatedBalance = $this->updatedBalance - $amount;

                        $exception = false;
                    } catch (AuthorizeNetException $e) {
                        $exception = $this->handleAuthorizeNetException($e, $method);
                    }

                    $transaction = $authorizeNet->getTransaction();
                    $this->responses[$method->order] = $authorizeNet->getChargeDetails();
                    $this->transactions[$method->order] = $transaction;

                    //update Membership Plan End Date                    
                    $start_time = Carbon::now();
                    $days = 0;
                    if($this->membership_account->plan_type == 1 || $this->membership_account->plan_type == 0){
                            $days = self::MONTHLY_PLAN_DAYS;
                    }elseif($this->membership_account->plan_type == 2){
                            $days = self::ANNUAL_PLAN_DAYS;
                    }else{
                            $days = 0;
                    }

                    $end_time = $start_time->copy()->addDays($days);
                    
                    $this->membership_account->end_date = $end_time;
                    $this->membership_account->save();
                    
                    $this->successLog->info("Membership Payment {$this->userDescription} end Date Updated with".$end_time);
                    
                    // save into membership payment 
                    $this->payments[$method->order] = MembershipPayment::create(
                        [
                        'user_membership_id' => $this->membership_account->id,
                        'anet_transaction_id' => $transaction->id,
                        'payment_profile_id' => $transaction->payment_profile_id,
                        'service_id' => $this->membership_account->membershipPlan->service_id,
                        'membership_plan_id' => $this->membership_account->membershipPlan->id,
                        'user_id' => $this->membership_account->user_id,
                        'total' => $amount,
                        'payment_date' => date('Y-m-d'),
                        'is_auto_renew' => 1,
                        'is_success' => 1
                        ]
                    );

                    if (!$exception) {
                        $this->successLog->info("Membership Payment SUCCESS for {$this->userDescription}, transaction {$transaction->id} for amount {$transaction->total}.");
                    }
                } catch (Exception $e) {
                    $this->handleAuthorizeNetException($e, $method);
                }
            }
        );
    }

    /**
     * Simulate running transactions for testing purposes
     */
    protected function dryRunTransactions()
    {
        $this->methods->each(
            function ($method) {
                try {
                    $paymentLastFour = 'XXXX1111';
                    $description = $this->membership_account->user_id.' '.$this->membership_account->membership_plan_id .' -type-'.$this->membership_account->plan_type. ' ' . Cim::PAYMENT_AUTO . " Parkengage Web";

                    $amount = $this->getAmountForMethod($method);

                    if ($amount === false) {
                        $this->successLog->info("No charge on payment method {$method->id}.");
                        return; // Nothing to charge
                    } else {
                        $this->successLog->info("Going to charge $amount on method {$method->id}.");
                    }

                    // Fail one out of every 100 transactions
                    $fail = mt_rand(1, 100) === 100;

                    $transaction = new AuthorizeNetTransaction();
                    $transaction->fill(
                        [
                        'sent' => 1,
                        'anonymous' => 0,
                        'user_id' => $this->membership_account->user_id,
                        'total' => $amount,
                        'description' => $description,
                        'response_code' => $fail ? 3 : 1,
                        'anet_trans_id' => mt_rand(1000000, 9999999),
                        'anet_trans_hash' => str_random(7),
                        'anet_type_id' => 1,
                        'payment_last_four' => $paymentLastFour,
                        'expiration' => '09/20',
                        'method' => mt_rand(0, 1) ? 'card' : 'bank',
                        'payment_profile_id' => $method->payment_profile_id
                        ]
                    );

                    // Make the payment
                    try {
                        if ($fail) {
                            throw new AuthorizeNetException("Charge declined.");
                        }

                        // Update our remaining balance
                    $this->updatedBalance = $this->updatedBalance - $amount;

                        $exception = false;
                    } catch (AuthorizeNetException $e) {
                        $exception = $this->handleAuthorizeNetException($e, $method);
                    }

                    $transaction->save();

                    $this->transactions[$method->order] = $transaction;

                    // here we need to save payment in membership payment Save the payment
                    // Membership payment entry
                    // save into membership payment 
                    $this->payments[$method->order] = MembershipPayment::create(
                        [
                        'user_membership_id' => $this->membership_account->id,
                        'anet_transaction_id' => $transaction->id,
                        'payment_profile_id' => $transaction->payment_profile_id,
                        'service_id' => $this->membership_account->membershipPlan->service_id,
                        'membership_plan_id' => $this->membership_account->membershipPlan->id,
                        'user_id' => $this->membership_account->user_id,
                        'total' => $amount,
                        'payment_date' => date('Y-m-d'),
                        'is_auto_renew' => 1,
                        'is_success' => 1
                        ]
                    );

                    if (!$exception) {
                        $this->successLog->info("Payment SUCCESS for {$this->userDescription}, transaction {$transaction->id} for amount {$transaction->total}.");
                    }
                } catch (Exception $e) {
                    $this->handleAuthorizeNetException($e, $method);
                }
            }
        );
    }

    // Get the amount to charge on this method. Returns false if no amount should be paid.
    protected function getAmountForMethod(AutopayMethod $method)
    {
        if (!$this->updatedBalance || $this->updatedBalance <= 0) {
            return false;
        }

        // Default to returning amount or updated balance, whichever is less
        return $this->updatedBalance;
    }

    protected function sendMail($template, $subject, array $data = [])
    {
       // here we can get service name and plan names
        $emailData = array_merge(
            [
            'service_name' => $this->membership_account->membershipPlan->service->full_name,
            'amount' => $this->currentMonthBalance,
            'payments' => $this->payments
            ], $data
        );
        if($this->membership_account->user){
            (new UserMailer())->queueMailTo($this->membership_account->user->email, $subject, $emailData, $template);
        }
    }

    protected function chooseEmailSendFunction()
    {
        if (count($this->exceptions)) {
            return $this->sendPaymentFailedEmail();
        }

        return $this->sendSuccessEmail();
    }

    protected function sendSuccessEmail()
    {
        return $this->sendMail('autopay.success', "Your Payment for Membership Renew has been processed for your {$this->membership_account->membershipPlan->service->full_name} Memerbship Plan ");
    }

    protected function sendPaymentFailedEmail()
    {
       
        return $this->sendMail(
            'autopay.failed',
            "We were unable to process your payment for {$this->membership_account->membershipPlan->service->full_name} Membership Plan Renew",
            ['errors' => $this->exceptions]
        );
    }


    protected function sendMethodExpiredEmail($method)
    {
        if (!$this->membership_account->active) {
            return; // account is not active, don't need to send an email
        }

        if (!$this->dry) {
            $payment = (new Cim())
                                ->setUser($this->membership_account->user)
                                ->getPaymentProfile($method->paymentProfile->payment_profile);
            $paymentLastFour = $payment['card'] ? $payment['card']['card_number'] : $payment['bank']['bank_account'];
        } else {
            $paymentLastFour = 'XXXX1111';
        }

        return $this->sendMail(
            'autopay.expired',
            "Your payment method has expired for service  Members",
            [
                'service_name' => $this->membership_account->membershipPlan->service->full_name,
                'expiration' => $method->paymentProfile->expirationDate->formatted,
                'payment_last_four' => $paymentLastFour,
                'is_autopay' => true,
                'next' => false
            ]
        );
    }

    protected function createLoggers()
    {
        $this->errorLog = $this->createLogger('autorenew-error');
        $this->successLog = $this->createLogger('autorenew-success');
    }

    private function createLogger($logger_name)
    {
        // setup the log files to have the monthly_user_id and account_number surrounded by brackets after the date /time
        // this should allow for easier parsing in the event something goes wrong
        $format = "[%datetime%][%level_name%][%membership_user_id%][%mebership_id%] %message% %context% %extra%\n";
        $path = $this->loggerDirectoryPath() . "/" . $logger_name . ".log";

        $handler = new RotatingFileHandler($path);
        $handler->setFormatter(new LineFormatter($format, null, false, true));
        $logger = new Logger('transaction_status');
        $logger->pushHandler($handler);
        $logger->pushProcessor(
            function ($record) {
                $record['account_number'] = $this->membership_account->user_id;
                $record['monthly_user_id'] = $this->membership_account->id;
                return $record;
            }
        );
        return $logger;
    }

    private function loggerDirectoryPath()
    {
        $dir = storage_path(self::LOG_DIRECTORY);

        // Verify that directory exists
        if (!Storage::exists($dir)) {
            Storage::makeDirectory($dir);
        }

        return $dir;
    }

    protected function handleAuthorizeNetException($e, $method)
    {
        $this->exceptions[$method->order] = $e->getMessage();

        $message = "Unable to process autopay payment for {$this->userDescription}, received error {$e->getMessage()}";

        if (method_exists($e, 'getErrors')) {
            $detail = $e->getErrors();
            $message .= print_r($detail, true);
        }

        $this->errorLog->error($message);
        return $e;
    }
}
