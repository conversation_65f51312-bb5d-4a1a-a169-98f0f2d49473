<?php

namespace App\Jobs;

use App\Models\PermitRequest;
use App\Models\PermitVehicle;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
// use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Services\LoggerFactory;


class SendPaymentReminder implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    const PARTNER_ID = 19349; // fallback partner id
    const FACILIY_ID = 301; // fallback partner id
    protected $partnerId;
    protected $log;

    public function __construct($partnerId = null, $facilityId = null, $log = null)
    {
        $this->partnerId = $partnerId ?: self::PARTNER_ID;
        $this->facilityId = $facilityId ?: self::FACILIY_ID;
        if(is_null($log)){
            $logFactory = new LoggerFactory();
            $this->log = $logFactory->setPath('logs/permit/notification')->createLogger('paymentReminder');
        } else {
            $this->log = $log;
        }
        // dd($this->partnerId, $this->facilityId, $partnerId, $facilityId);
    }


    public function handle()
    {
        // Calculate date ranges (previous month permits)
        $permit_end_date   = date('Y-m-d', strtotime('last day of previous month'));
        $permit_start_date = date('Y-m-d', strtotime('first day of previous month'));

        $desired_end_date   = date("Y-m-t");
        $desired_start_date = date('Y-m-01');

        $permitRenewDate = date('Y-m-d H:i:s');
        $time            = strtotime($permitRenewDate);
        $PermitRenewMonth = date("F", $time);
        $PermitRenewYear  = date("Y", $time);
        $PermitRenewDay   = date("d", $time);

        // Fetch permit requests instead of ParkingSubscription
        $monthlyRequest = PermitRequest::with(['user', 'PermitVehicle'])
            ->where('partner_id', $this->partnerId);
            if(!is_null($this->facilityId)){
                $monthlyRequest = $monthlyRequest->where('facility_id', $this->facilityId);
            }

        $monthlyRequest = $monthlyRequest->whereDate('desired_end_date', '<=', $permit_end_date)
            ->whereDate('desired_start_date', '>=', $permit_start_date)
            ->whereNotNull('anet_transaction_id')
            ->whereNull('cancelled_at')
            ->whereNull('deleted_at')
            ->whereNull('business_id')
            // ->where('user_consent', 1)
            ->where('permit_rate_id', '>' , 0)
            ->whereIn('account_number', ["********"])
            ->orderBy('id', 'desc')
            ->get();

            dd($monthlyRequest->count(), $this->partnerId, $this->facilityId, $permit_end_date, $permit_start_date, $monthlyRequest[0]);

        if ($monthlyRequest->isEmpty()) {
            $this->log->info("Monthly Request Not Found.");
            return;
        }

        $this->log->info("Permit Found: " . json_encode($monthlyRequest->count()));

        // Loop through permits and send reminders
        foreach ($monthlyRequest as $permit) {

            $dueDate = Carbon::parse($permit->desired_end_date);
            $today   = Carbon::today();
            // $today   = Carbon::parse('2025-07-30 00:00:00');

            // Reminder: 10 days before due date
            if ($today->eq($dueDate->copy()->subDays(10))) {
                $subject = "Reminder: Upcoming Payment – {$permit->account_number} on {$permit->desired_end_date}";
                $this->sendReminder($permit, '10_days', $subject);
            }

            // Reminder: 24 hours before due date
            if ($today->eq($dueDate->copy()->subDay())) {
                $subject = "URGENT: Your Permit Payment is Due Tomorrow";  
                $this->sendReminder($permit, '24_hours', $subject);
            }
        }
    }

    private function sendReminder($permit, $type, $subject)
    {
        $this->log->info("sms reminder for : " . $permit->account_number . " and type: " . $type);

        $user = $permit->user;

        if (!$user || !$user->email) {
            $this->log->warning("Permit {$permit->id} has no valid user/email.");
            return;
        }

        // Payment link with permit ID
        $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $permit->partner_id)->first();
        $partnerSlug  =  $checkPaymentUrl->touchless_payment_url;
        $paymentLink = config("parkengage.WEB_URL_CUSTOMERPORTAL") ."/" .$partnerSlug;

        $permitData['permit']           = $permit;
        $permitData['title']            = $subject;
        $permitData['type']             = $type;
        $permitData['url']              = $paymentLink;
        $permitData['lisense_plate']    = implode(',',(PermitVehicle::whereIn("id", $permit->PermitVehicle->pluck('permit_vehicle_id')->toArray())->pluck('license_plate_number')->toArray()));


        $recipients = config("parkengage.notify.reservation") ?? '<EMAIL>';

        // Send reminder email
        Mail::send('notify.payment_reminder', $permitData, function ($message) use ($recipients, $subject) {
            $message->to("<EMAIL>")
            // $message->to($recipients)
                ->subject($subject)
                ->from(config('parkengage.default_sender_email'));
        });
    }
}
