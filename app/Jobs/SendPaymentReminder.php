<?php

namespace App\Jobs;

use App\Models\PermitRequest;
use App\Models\PermitVehicle;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
// use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Services\LoggerFactory;


class SendPaymentReminder implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    const PARTNER_ID = 19349; // fallback partner id
    const FACILITY_ID = 301; // fallback facility id
    protected $partnerId;
    protected $facilityId;
    protected $log;

    public function __construct($partnerId = null, $facilityId = null, $log = null)
    {
        $this->partnerId = $partnerId ?: self::PARTNER_ID;
        $this->facilityId = $facilityId ?: self::FACILITY_ID;
        if(is_null($log)){
            $logFactory = new LoggerFactory();
            $this->log = $logFactory->setPath('logs/permit/notification')->createLogger('paymentReminder');
        } else {
            $this->log = $log;
        }

        $this->log->info("SendPaymentReminder initialized", [
            'partnerId' => $this->partnerId,
            'facilityId' => $this->facilityId
        ]);
    }


    public function handle()
    {
        try {
            $this->log->info("Starting payment reminder job");

            // Calculate date ranges (previous month permits)
            $permit_start_date = date('Y-m-d', strtotime('first day of this month'));
            $permit_end_date   = date('Y-m-d', strtotime('last day of this month'));

            $desired_end_date   = date("Y-m-t");
            $desired_start_date = date('Y-m-01');

            $permitRenewDate = date('Y-m-d H:i:s');
            $time            = strtotime($permitRenewDate);
            $PermitRenewMonth = date("F", $time);
            $PermitRenewYear  = date("Y", $time);
            $PermitRenewDay   = date("d", $time);

            $this->log->info("Date ranges calculated", [
                'permit_end_date' => $permit_end_date,
                'permit_start_date' => $permit_start_date,
                'current_month' => $PermitRenewMonth . ' ' . $PermitRenewYear
            ]);

        // Fetch permit requests instead of ParkingSubscription
        $monthlyRequest = PermitRequest::with(['user', 'PermitVehicle'])
            ->where('partner_id', $this->partnerId);

        if(!is_null($this->facilityId)){
            $monthlyRequest = $monthlyRequest->where('facility_id', $this->facilityId);
        }

        $monthlyRequest = $monthlyRequest->whereDate('desired_end_date', '<=', $permit_end_date)
            ->whereDate('desired_start_date', '>=', $permit_start_date)
            ->whereNotNull('anet_transaction_id')
            ->whereNull('cancelled_at')
            ->whereNull('deleted_at')
            ->whereNull('business_id')
            // ->where('user_consent', 1)
            ->where('permit_rate_id', '>' , 0)
            // ->whereIn('account_number', ["********"])
            ->orderBy('id', 'desc')
            ->get();

        $this->log->info("Payment reminder query executed", [
            'count' => $monthlyRequest->count(),
            'partnerId' => $this->partnerId,
            'facilityId' => $this->facilityId,
            'permit_end_date' => $permit_end_date,
            'permit_start_date' => $permit_start_date
        ]);
        // dd($monthlyRequest[0]->account_number, count($monthlyRequest));

        if ($monthlyRequest->isEmpty()) {
            $this->log->info("Monthly Request Not Found.");
            return;
        }

        $this->log->info("Permit Found: " . json_encode($monthlyRequest->count()));

        // Loop through permits and send reminders
        foreach ($monthlyRequest as $permit) {

            $dueDate = Carbon::parse($permit->desired_end_date);
            $today   = Carbon::today();
            // $today   = Carbon::parse('2025-07-21 00:00:00');

            // Reminder: 10 days before due date
            if ($today->eq($dueDate->copy()->subDays(10))) {
                $subject = "Reminder: Upcoming Payment – {$permit->account_number} on {$permit->desired_end_date}";
                $this->sendReminder($permit, '10_days', $subject);
            }

            // Reminder: 24 hours before due date
            if ($today->eq($dueDate->copy()->subDay())) {
                $subject = "URGENT: Your Permit Payment is Due Tomorrow";
                $this->sendReminder($permit, '24_hours', $subject);
            }

            $this->log->info("Payment reminder 24 hours: ". json_encode($today->eq($dueDate->copy()->subDay())) . " Days: " . json_encode(($today->eq($dueDate->copy()->subDays(10)))));

        }

        $this->log->info("Payment reminder job completed successfully");

        } catch (\Exception $e) {
            $this->log->error("Payment reminder job failed", [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            throw $e;
        }
    }

    private function sendReminder($permit, $type, $subject)
    {
        try {
            $this->log->info("Sending reminder", [
                'account_number' => $permit->account_number,
                'type' => $type,
                'permit_id' => $permit->id
            ]);

            $user = $permit->user;

            if (!$user || !$user->email) {
                $this->log->warning("Permit {$permit->id} has no valid user/email.");
                return;
            }

            // Payment link with permit ID
            $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $permit->partner_id)->first();

            if (!$checkPaymentUrl || !$checkPaymentUrl->touchless_payment_url) {
                $this->log->error("No payment gateway details found for partner: " . $permit->partner_id);
                return;
            }

            $partnerSlug = $checkPaymentUrl->touchless_payment_url;
            $paymentLink = config("parkengage.WEB_URL_CUSTOMERPORTAL") . "/" . $partnerSlug;

            // Get license plates safely
            $licenseePlates = '';
            if ($permit->PermitVehicle && $permit->PermitVehicle->count() > 0) {
                $vehicleIds = $permit->PermitVehicle->pluck('permit_vehicle_id')->toArray();
                if (!empty($vehicleIds)) {
                    $licenseePlates = implode(',', PermitVehicle::whereIn("id", $vehicleIds)->pluck('license_plate_number')->toArray());
                }
            }

            $permitData = [
                'permit' => $permit,
                'title' => $subject,
                'type' => $type,
                'url' => $paymentLink,
                'license_plate' => $licenseePlates
            ];

            $recipients = config("parkengage.notify.reservation") ?? '<EMAIL>';

            // Send reminder email
            Mail::send('notify.payment_reminder', $permitData, function ($message) use ($user, $subject) {
                $message->to($user->email)
                    ->subject($subject)
                    ->from(config('parkengage.default_sender_email'));
            });

            $this->log->info("Reminder email sent successfully", [
                'permit_id' => $permit->id,
                'email' => $user->email,
                'type' => $type
            ]);

        } catch (\Exception $e) {
            $this->log->error("Failed to send reminder", [
                'permit_id' => $permit->id ?? 'unknown',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }
}
