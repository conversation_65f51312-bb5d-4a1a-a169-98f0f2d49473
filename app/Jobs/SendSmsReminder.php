<?php

namespace App\Jobs;

use App\Http\Helpers\QueryBuilder;
use App\Jobs\Job;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use <PERSON><PERSON><PERSON>\Rest\Client;

class SendSmsReminder extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    private $reservationData;
    private $smsContent;
    private $log;
    private $partnerid; // Declare the partnerid property

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($smsContent, $reservationData, $logFactory, $partnerid = null)
    {
        // parent::__construct();
        $this->reservationData = $reservationData;
        $this->smsContent = $smsContent;
        $this->partnerid = $partnerid; // Initialize partnerid with the provided value (or null)

        $this->log = $logFactory->setPath('logs/mapcosms')->createLogger('sms');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $phone = env('TWILIO_PHONE');
        $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
        $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
        $this->log->info("Reservation SMS JOB LOG START ");
        $client = new Client($accountSid, $authToken);

        try {
            $this->log->info("Chekc SMS SLUG :  {$this->smsContent->slug_name} , BODY : {$this->smsContent->body}");

            $resUrlCode = base64_encode($this->reservationData['reservationId']);
            if ($this->partnerid == '215900') {
                $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("TRANSIENT_URL", $this->partnerid);
                if ($dynamicReceiptUrl) {
                    $configurl = $dynamicReceiptUrl->value;
                    $url = $configurl . $this->smsContent->url . $this->reservationData['ticketNumber'];
                } else {
                    $this->log->error("Config url not get " . $this->partnerid);
                }
            } else if ($this->smsContent->slug_name === "extend-time-reminder") {
                $url = config('parkengage.Woodman.url') . $this->smsContent->url . $this->reservationData['ticketNumber'];
            } else if ($this->smsContent->slug_name === "extend-time-reminders" || $this->smsContent->slug_name === "extend-session-reminder") {
                $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("TRANSIENT_URL", $this->partnerid);
                if ($dynamicReceiptUrl) {
                    $configurl = $dynamicReceiptUrl->value;
                    $url = $configurl . $this->smsContent->url . $this->reservationData['ticketNumber'];
                } else {
                    $url = config('parkengage.Woodman.usm_url') . $this->smsContent->url . $this->reservationData['ticketNumber'];
                }
            } else {
                $url = config('parkengage.Woodman.roc_url') . $this->smsContent->url . $resUrlCode;
            }
            // Send SMS
            $client->messages->create(
                $this->reservationData['phone'],
                ['from' => $phone, 'body' => $this->smsContent->body . ' ' . $url]
            );
            $this->log->info("Reservation SMS JOB LOG START CLOSE ");
        } catch (\Throwable $e) {
            //$this->log->error("Reservation SMS JOB LOG ERROR EXCEPTION  " . $e->getMessage());
            $this->log->error("Reservation SMS JOB LOG ERROR EXCEPTION  " . $e->getMessage() . 'File ' . $e->getFile() . ' Line ' . $e->getLine());
        }
    }
}
