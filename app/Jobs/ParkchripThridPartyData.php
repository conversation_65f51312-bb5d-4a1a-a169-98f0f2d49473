<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Log;
use App\Classes\CommonFunctions; // Ensure you have the correct namespace for CommonFunctions
use App\Services\LoggerFactory;
use App\Models\PermitThirdParkchip;
use App\Models\Facility;
use App\Models\PermitVehicle;
use App\Models\PermitRequest;
use App\Models\PermitVehicleMapping;
use App\Models\User;
use App\Models\PermitRate;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Http\Helpers\QueryBuilder;
use App\Models\ThirdPartyIntegrationsMapping;

class ParkchripThridPartyData implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $filename;
    protected $log;

    public function __construct($filename)
    {
        $this->filename = $filename;
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/thridparty/parkchrip')->createLogger('parkchrip');
    }

    public function handle()
    {
        try {
            // Step 1: Get Authorization Token
            $authUrl = config('parkchrip.auth_url');
            $authHeaders = ['Content-Type: application/json'];
            $authData = [
                "email" => config('parkchrip.email'),
                "password" => config('parkchrip.password')
            ];

            // $this->log->info('Auth API request initiated.', ['url' => $authUrl, 'method' => 'POST', 'headers' => $authHeaders, 'data' => $authData]);
            //dd($authUrl, 'POST', $authHeaders, $authData);
            $authResponse = CommonFunctions::makeRequest($authUrl, 'POST', $authHeaders, $authData);

            // Check if the token exists in the response
            if (!isset($authResponse['idToken'])) {
                $this->log->error('Failed to retrieve auth token.', ['response' => $authResponse]);
                throw new \Exception("Failed to retrieve auth token.");
            }

            $authToken = $authResponse['idToken'];

            // $this->log->info('Auth token received successfully.');

            // Step 2: Call Facilities API with Authorization Token
            //    $startTime = Carbon::now()->subDays(17)->format('Y-m-d\TH:i:s\Z'); // 30 days ago in UTC format
            $startTime = Carbon::now()->startOfMonth()->format('Y-m-d\TH:i:s\Z');
            $endTime = Carbon::now()->format('Y-m-d\TH:i:s\Z'); // Current date in UTC format
            //dd($firstDayOfMonth,$startTime);
            $facilitiesUrl = config('parkchrip.facilities_url') .
                '?facilityId=' . config('parkchrip.facility_id') .
                '&startTime=' . $startTime .
                '&endTime=' . $endTime;

            $accessUrl = config('parkchrip.access_devices_url') .
                '?facilityId=' . config('parkchrip.facility_id');

            $facilityHeaders = [
                'Authorization: Bearer ' . $authToken
            ];

            // $this->log->info('Facilities API request initiated.', ['url' => $facilitiesUrl]);
            //dd($facilitiesUrl, 'GET', $facilityHeaders);
            $response = CommonFunctions::makeRequest($facilitiesUrl, 'GET', $facilityHeaders);

            $accessResponse = CommonFunctions::makeRequest($accessUrl, 'GET', $facilityHeaders);
            //dd($response);

            // DB::beginTransaction();  // Start the transaction
            try {

                foreach ($response['transactions'] as $key => $data) {

                    $d = json_decode(json_encode($data)); // Convert array to JSON and back to object

                    // Ensure required keys exist before accessing
                    if (!isset($d->facilityId, $d->subscription->id)) {
                        $this->log->error("Missing essential subscription data. Skipping...");
                        continue;
                    }
                    // $this->log->info('start1');
                    $facilityId = $d->facilityId;
                    $subscriptionId = $d->subscription->id;
                    $accountId = $d->subscription->accountId;
                    $parkerId = $d->parkerId;
                    $parkerFullName =  strtolower($d->parker->firstName . "-" . $d->parker->lastName);
                    $prox_card_number = NULL;
                    if ($accessResponse) {
                        foreach ($accessResponse as $k => $v) {
                            $v1 = json_decode(json_encode($v));
                            if ($v1->parker->id == $parkerId) {
                                if ($v1->accessDevices) {
                                    foreach ($v1->accessDevices as $k1 => $v2) {
                                        if (($v2->deviceType == 'Proximity Card') && ($v2->deviceType != 'Inactive')) {
                                            $prox_card_number = $v2->externalDeviceId;
                                        } else {
                                            continue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // $this->log->info('start');

                    // Check if record already exists
                    $existingRecord = PermitThirdParkchip::where('facilityId', $facilityId)
                        ->where('parkerId', $parkerId)
                        ->first();
                    //dd($existingRecord);
                    // $this->log->info("existing record :". $existingRecord);
                    $email = $d->parker && $d->parker->email
                        ? $d->parker->email
                        : strtolower(str_replace(' ', '-', $parkerFullName)) . '<EMAIL>';
                    // If exists, update instead of skipping
                    if ($existingRecord) {
                        // $this->log->info("Updating existing record for FacilityID: $facilityId, AccountID: $accountId, SubscriptionID: $subscriptionId");
                        $oldVehicles = json_decode($existingRecord->vehicles, true) ?: [];
                        $newVehicles = isset($d->vehicles) ? $d->vehicles : [];
                        //dd($oldVehicles, $newVehicles);	
                        $mergedVehicles = array_merge($oldVehicles, $newVehicles);
                        $uniqueVehicles = array_values(array_unique($mergedVehicles, SORT_REGULAR));
                        //dd($mergedVehicles,$uniqueVehicles,json_encode($uniqueVehicles));
                        $existingRecord->vehicles = json_encode($uniqueVehicles);
                        //dd($existingRecord->vehicles);	
                        $existingRecord->sublotId = $d->sublotId ?? null;
                        $existingRecord->parkerId = $d->parkerId ?? null;
                        $existingRecord->parkerFirstName = $d->parker->firstName ?? null;
                        $existingRecord->parkerLastName = $d->parker->lastName ?? null;
                        $existingRecord->parkerFullName = ($d->parker->firstName ?? '') . ' ' . ($d->parker->lastName ?? '');
                        $existingRecord->parkerEmail =  $email;
                        $existingRecord->parkerPhone =  $d->parker->phone ?? null;
                        $existingRecord->createdAt = $d->createdAt ?? now();
                        $existingRecord->startDate = $d->startDate ?? null;
                        $existingRecord->endDate = $d->endDate ?? null;
                        $existingRecord->subscriptionName = $d->subscription->subscriptionName ?? null;
                        $existingRecord->prox_card_number = $prox_card_number ?? null;
                        $existingRecord->partner_id = config('parkengage.PARTNER_PCI');
                        // $existingRecord->vehicles = json_encode($d->vehicles ?? []); 

                        $existingRecord->save(); // Update record
                        //dd($existingRecord->vehicles);
                        $this->log->info("vechiles :" . $existingRecord->vehicles);
                        // Insert new vehicles
                        if (!empty($existingRecord->vehicles)) {
                            $vehicles = json_decode($existingRecord->vehicles);

                            foreach ($vehicles as $v) {
                                $licensePlate = $v->licensePlateNumber ?? null;
                                $vehicleType = $v->licensePlateState ?? null;
                                $vehiclemake = $v->make ?? null;
                                $vehiclemodel = $v->model ?? null;
                                $deletedAt    = $v->deletedAt ?? null;

                                if ($licensePlate) {
                                    // Check if this vehicle already exists related to the permit record
                                    $exists = $existingRecord->vehicleList()
                                        ->where('vehicleNumber', $licensePlate)
                                        ->exists();

                                    if (!$exists) {
                                        // Create only if not exists
                                        $existingRecord->vehicleList()->create([
                                            'vehicleNumber' => $licensePlate,
                                            'state' => $vehicleType,
                                            'make' => $vehiclemake,
                                            'model' => $vehiclemodel,
                                            'deleted_at' => $deletedAt,
                                            'permit_third_parkchip_id' => $existingRecord->id,
                                        ]);

                                        $this->log->info("Created vehicle with licensePlateNumber: $licensePlate");
                                    } else {
                                        $this->log->info("Vehicle with licensePlateNumber: $licensePlate already exists, skipping.");
                                    }
                                }
                            }
                        }
                    } else {
                        // Insert new record
                        $permit = PermitThirdParkchip::create([
                            'facilityId' => $facilityId,
                            'sublotId' => $d->sublotId ?? null,
                            'parkerId' => $d->parkerId ?? null,
                            'parkerFirstName' => $d->parker->firstName ?? null,
                            'parkerLastName' => $d->parker->lastName ?? null,
                            'parkerFullName' => ($d->parker->firstName ?? '') . ' ' . ($d->parker->lastName ?? ''),
                            'parkerEmail' => $email,
                            'parkerPhone' => $d->parker->phone ?? null,
                            'createdAt' => $d->createdAt ?? now(),
                            'startDate' => $d->startDate ?? null,
                            'endDate' => $d->endDate ?? null,
                            'subscriptionId' => $subscriptionId,
                            'subscriptionName' => $d->subscription->subscriptionName ?? null,
                            'accountId' => $accountId,
                            'prox_card_number' => $prox_card_number ?? null,
                            'vehicles' => json_encode($d->vehicles ?? []),
                            'partner_id' => config('parkengage.PARTNER_PCI'),
                        ]);

                        if (!empty($d->vehicles)) {
                            $vehicles = ($d->vehicles); // Convert JSON string to array of objects

                            foreach ($vehicles as $v) {
                                $licensePlate = $v->licensePlateNumber ?? null;
                                $vehicleType = $v->licensePlateState ?? null;
                                $vehiclemake = $v->make ?? null;
                                $vehiclemodel = $v->model ?? null;
                                $deletedAt    = $v->deletedAt ?? null;

                                $permit->vehicleList()->create([
                                    'vehicleNumber' => $licensePlate,
                                    'state' => $vehicleType,
                                    'make' => $vehiclemake,
                                    'model' => $vehiclemodel,
                                    'deleted_at' => $deletedAt,
                                    'permit_third_parkchip_id' => $permit->id,
                                ]);
                            }
                        }
                    }
                }
                //  DB::commit();  // Commit the transaction if all inserts are successful

                DB::beginTransaction();
                // $this->log->info('start4');
                $data = PermitThirdParkchip::with('vehicleList')->where('partner_id', config('parkengage.PARTNER_PCI'))->get();
                $countryCode = QueryBuilder::appendCountryCode();
                // $this->log->info('countrycode'.$countryCode);
                //dd(count($data),$facilitiesUrl, 'GET', $facilityHeaders);
                try {
                    foreach ($data as $k => $d) {
                        if ($d->prox_card_number) {
                            $proxcardlength = strlen($d->prox_card_number);
                            if ($proxcardlength < config('parkengage.PROX_CARD_LENGTH')) {
                                $diff = 5 - $proxcardlength;
                                $prox_card_number = str_repeat("0", $diff) . $d->prox_card_number;
                                $d->prox_card_number = $prox_card_number;
                            }
                        }
                        $checkfacility = ThirdPartyIntegrationsMapping::where('third_party_id', $d->facilityId)->first();
                        if (!empty($checkfacility)) {
                            $facility     = Facility::where('id', $checkfacility->facility_id)->first();
                        }

                        if (empty($facility)) {
                            $this->log->info('Facility is not exist.');
                        }
                        //dd($d);
                        //dd($d->vehicleList);
                        //dd($d->vehicles);
                        // Check if vehicles are provided
                        if (empty($d->vehicles) && empty($d->prox_card_number)) {
                            $d->failed_type = "Empty";
                            $d->failed_description = "Vehicle Object is Empty";
                            $d->save();
                            // Log the situation when no vehicles are provided
                            // $this->log->info("No vehicles provided, skipping PermitRequest creation: " . json_encode($d));
                            continue; // Skip this iteration if no vehicles
                        }

                        $vehicles = json_decode($d->vehicles);

                        $vechilesdata = [];

                        // $this->log->info('vehicles: '.json_encode($vehicles));
                        foreach ($vehicles as $vech) {
                            if (!empty($vech->deletedAt)) {
                                $d->failed_type = "Deleted";
                                $d->failed_description = "Vehicle License Plate is Deleted";
                                $d->save();
                                continue;
                            }
                            if (empty($vech->licensePlateNumber)) {
                                $d->failed_type = "Empty";
                                $d->failed_description = "Vehicle License Plate is Empty";
                                $d->save();
                                continue;
                            }
                            $existingVehicle = PermitVehicle::where('license_plate_number', $vech->licensePlateNumber)
                                ->whereNULL('deleted_at')
                                ->first();
                            if ($existingVehicle) {
                                $existingMapping = PermitVehicleMapping::where('permit_vehicle_id', $existingVehicle->permit_vehicle_id)
                                    ->first();
                                if ($existingMapping) {
                                    $existdata = PermitRequest::where('partner_id', $facility->owner_id)->where('partner_id', $existingMapping->permit_request_id)
                                        ->wherenull('deleted_at')->wherenull('cancelled_at')->where('status', '1')->first();
                                    if ($existdata) {
                                        $d->failed_type = "Duplicate";
                                        $d->failed_description = "Vehicle License Plate is Already Exists & Mapped with Permit";
                                        $d->save();
                                        continue;
                                    }
                                }
                            }
                            // $this->log->info('vehiclesinfo: '.json_encode($veh));
                            $vehicleFilter = PermitRequest::licensePlateFilter($vech->licensePlateNumber, $vech->make, $vech->model);
                            // $this->log->info('vehicleFilter: '.$vevehicleFilter);
                            if (!$vehicleFilter) {
                                $vechilesdata[] = $vech;
                            }
                        }
                        //dd($vechilesdata);
                        // $this->log->info('vechilesdata: '.json_encode($vechilesdata));

                        $email = $d->parkerEmail ? $d->parkerEmail : strtolower(str_replace(' ', '-', $d->parkerFullName)) . '<EMAIL>';
                        //dd($email);
                        if (!isset($facility->id)) {
                            continue;
                        }

                        //$existUser = User::where('email', $email)->where('thirdparty_userid', $d->parkerId)->first();

                        $existUser = User::where('email', $email)->where('created_by', $facility->owner_id)->whereNull('deleted_at')->first();
                        //dd($email);
                        // $this->log->info('User exist: '.$existUser);
                        if (isset($existUser->id)) {
                            $email = $existUser->email;
                        }
                        $facilityid = $facility->id;

                        $existdata = null;
                        $existdata = PermitRequest::where('partner_id', $facility->owner_id)->where('email', $email)
                            ->wherenull('deleted_at')
                            ->first();
                        //dd($existdata);

                        $ratedata   = PermitRate::where('facility_id', $facilityid)->where('is_thirdparty', '1')->first();
                        //dd($facilityid,$ratedata);	

                        //  $this->log->info($d->parkerId);
                        $postdata = [
                            'first_name' => $d->parkerFirstName,
                            'last_name' => $d->parkerLastName,
                            'email' => $email,
                            'phone' => $d->parkerPhone,
                            'facility_id' => $facility->id,
                            'vehicleList' => $vechilesdata, // Start with the current vehicle
                            'address1' => '',
                            'address2' => '',
                            'city' => '',
                            'state' => '',
                            'zipcode' => '',
                            'permit_rate_id' => isset($ratedata->id) ? $ratedata->id : null,
                            "permit_rate" => isset($ratedata->rate) ? $ratedata->rate : 0,
                            "permit_type_name" => isset($ratedata->name) ? $ratedata->name : null,
                            "type_id" => isset($type_id) ? $type_id : null,
                            'desired_start_date' =>  date('Y-m-d', strtotime($d->startDate)),
                            'desired_end_date' =>  date('Y-m-d', strtotime($d->endDate)),
                            'permit_final_amount' => isset($ratedata->rate) ? $ratedata->rate : 0,
                            'partner_id' => $facility->owner_id,
                            'name' => $d->parkerFullName,
                            "third_party_type" => 1,
                            'thirdparty_userid' => $d->parkerId,
                            'third_party_subscription_id' => $d->subscriptionId,
                            'third_party_account_id' => $d->accountId,
                            'hid_card_number' =>   $d->prox_card_number ?? null,
                            'account_number' => rand(100, 999) . rand(100, 999) . rand(10, 99),
                        ];
                        if (empty($postdata['vehicleList']) && empty($postdata['hid_card_number'])) {
                            $d->failed_type = "Empty";
                            $d->failed_description = "Vehicle License Plate is Empty";
                            $d->save();
                            continue;
                        }

                        $postdata['vehicleList'] = json_encode($postdata['vehicleList']);
                        $partner_id = $facility->owner_id;

                        // $this->log->info("post data: " . json_encode($postdata));
                        if (!isset($existUser->id)) {
                            $existUser = '';
                            $postdata['confirm_password'] = $d->parkerFullName . date('md');
                        }
                        $request = (object) $postdata;


                        $user = User::fetchOrCreateUserThirdParty($request, $partner_id, $d->parkerFullName, false, $existUser, $countryCode);
                        // $this->log->info("User Details:" . json_encode($user));
                        if (isset($user->id)) {
                            $userId = $user->id;
                        } else {
                            // $this->log->info('User not exist. Please check again for email: '.$d->parkerEmail); 
                            continue;
                        }

                        $status = 0;


                        $licenseMaxCheck = PermitRequest::licenseMaxCheck($facility, $user, $request, $partner_id);
                        //dd($licenseMaxCheck);
                        if (!$licenseMaxCheck) {
                            //dd($user->id);
                            //dd($facility, $user, $request, $partner_id, $status, $existdata);
                            $permitRes = PermitRequest::savePermitRequestThirdParty($facility, $user, $request, $partner_id, $status, $existdata);
                            // $this->log->info('permitcreatd '.$permitRes);
                            //dd($permitRes, $user, $request, $partner_id,$existdata);
                            $vechilemap = PermitRequest::saveOrUpdatePermitVehicle($permitRes, $user, $request, $partner_id);
                            //dd($vechilemap);
                            // $this->log->info('vechilemap '.$vechilemap);
                        } else {
                            // $this->log->info($licenseMaxCheck); 
                            continue;
                        }
                    }

                    DB::commit(); // Commit the transaction
                    return response()->json([
                        'status' => 'success',
                        'message' => 'Transaction data saved successfully.'
                    ], 200); // Success response

                } catch (\Exception $e) {
                    //dd($e->getMessage());
                    $this->log->error("Error saving transaction data: " . $e->getMessage() . 'line no ' . $e->getLine());
                    DB::rollBack(); // Rollback the transaction if any error occurs
                    // Return an error response to the API client
                    return response()->json([
                        'status' => 'error',
                        'message' => 'An error occurred while saving transaction data.',
                        'error' => $e->getMessage()
                    ], 500); // Internal server error response
                }
            } catch (\Exception $e) {
                // DB::rollBack();  // Rollback the transaction if any error occurs
                $this->log->error('Error saving transaction data1: ' . $e->getMessage() . 'line no ' . $e->getLine());
                return 'error';  // Return error message
            }
        } catch (\Exception $e) {
            $this->log->error('Error in ParkchirpThirdPartyData Job.', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),  // Full stack trace for deeper insight
            ]);
        }
    }

    //user create data
    private function usercreate($id, $fullname, $email)
    {
        // Check if user with the same parker_id exists
        $user = User::where('thirdparty_userid', $id)->first();

        if (!$user) {
            if (!empty($email)) {
                // Check if another user exists with the same first & last name but a different parker_id
                $user = User::create([
                    'thirdparty_userid'  => $id,
                    'name' => $fullname,
                    'email'      => $email,
                ]);
            } else {
                // Check if another user exists with the same first & last name but a different parker_id
                $existingUser = User::where('name', $fullname)->first();

                if ($existingUser) {
                    // Generate a unique email based on first_name, last_name, and a random string
                    $email = strtolower($fullname) . rand(1000, 9999) . '@yopmail.com';
                } else {
                    // Use provided email or generate a default one
                    $email = strtolower($fullname) . '@yopmail.com';
                }

                // Create a new user
                $user = User::create([
                    'thirdparty_userid'  => $id,
                    'name' => $fullname,
                    'email'      => $email,
                ]);
            }

            return $user;
        }

        return $user;
    }

    /*
    // Separated the permit request function due to varying data formats
    private function savePermitRequest($d, $userdata)
    {
        try {

            $facility = Facility::where('spothero_facility_id', $d->facilityId)->first();

            $ratedata = PermitRate::where('facility_id', $facility->id)->first();

            if (!empty($facility)) {
                $PermitRequest = new PermitRequest();
                $PermitRequest->facility_id = $facility->id;
                $PermitRequest->user_id = $userdata->id;
                $PermitRequest->desired_start_date = date('Y-m-d', strtotime($d->startDate));
                $PermitRequest->desired_end_date = date('Y-m-d', strtotime($d->endDate));
                $PermitRequest->email = $userdata->email;
                $PermitRequest->name = $d->parkerFullName;
                $PermitRequest->account_number = rand(100, 999) . rand(100, 999) . rand(10, 99);
                $PermitRequest->approved_on = date('Y-m-d H:i:s');  // Current timestamp
                $PermitRequest->license_number = '';
                $PermitRequest->partner_id = $facility->owner_id;
                $PermitRequest->third_party_type = 1;  //new param
                $PermitRequest->third_party_subscription_id = $d->subscriptionId; // new param
                $PermitRequest->third_party_user_id = $d->parkerId; // new param
                $PermitRequest->third_party_account_id = $d->accountId; // new param
                $PermitRequest->permit_rate = $ratedata->rate ?? 0.00;
                $PermitRequest->permit_type_name = $ratedata->name ?? null;
                $PermitRequest->permit_rate_id = $ratedata->id ?? null;


                $PermitRequest->save(); // Save the PermitRequest
                return $PermitRequest;
            }

            return null;
        } catch (\Exception $e) {
            // Log error if PermitRequest fails to save
            // $this->log->info("Error saving PermitRequest: " . json_encode($e->getMessage()).'line no'.$e->getLine());
            // You can still return a default object or handle this as necessary
            return null;
        }
    }
    */
    /*
    // Separated the vehiclesAndMappings function due to varying data formats
    private function saveVehiclesAndMappings($vehicles, $permitRequestId, $permitdata)
    {
        try {
            $vehicles = json_decode($vehicles);

            // $this->log->info("vehicles data: " . json_encode($vehicles));

            if (!is_array($vehicles)) {
                $this->log->error("Invalid vehicles data. Skipping...");
                return;
            }

            foreach ($vehicles as $vech) {
                if (!isset($vech->licensePlateNumber, $vech->licensePlateState)) {
                    $this->log->error("Missing vehicle details. Skipping...");
                    continue;
                }

                // Check if the vehicle already exists based on license plate and state name
                $existingVehicle = PermitVehicle::where('license_plate_number', $vech->licensePlateNumber)
                    ->where('state_name', $vech->licensePlateState)
                    ->first();

                if ($existingVehicle) {
                    $vehicle_id = $existingVehicle->id;
                    // $this->log->info("Vehicle already exists with ID: " . $vehicle_id);
                } else {
                    // Create a new vehicle record if not exists
                    $res = new PermitVehicle();
                    $res->license_plate_number = $vech->licensePlateNumber;
                    $res->make = $vech->make ?? null;
                    $res->model = $vech->model ?? null;
                    $res->color = $vech->color ?? null;
                    $res->is_default = 0;
                    $res->state_name = $vech->licensePlateState;
                    $res->user_id = $permitdata->user_id;
                    $res->partner_id = $permitdata->partner_id;
                    $res->save();

                    $vehicle_id = $res->id;
                    // $this->log->info("New vehicle saved with ID: " . $vehicle_id);
                }

                // Check if the mapping already exists before creating a new one
                $existingMapping = PermitVehicleMapping::where('permit_request_id', $permitRequestId)
                    ->where('permit_vehicle_id', $vehicle_id)
                    ->first();

                if (!$existingMapping) {
                    $mapping = new PermitVehicleMapping();
                    $mapping->permit_request_id = $permitRequestId;
                    $mapping->permit_vehicle_id = $vehicle_id;
                    $mapping->save();
                    // $this->log->info("New vehicle mapping created.");
                } else {
                    // $this->log->info("Mapping already exists for vehicle ID: " . $vehicle_id);
                }
            }
        } catch (\Exception $e) {
            $this->log->error("Error saving vehicles or mappings: " . $e->getMessage() . " at line " . $e->getLine());
        }
    }
    */
}
