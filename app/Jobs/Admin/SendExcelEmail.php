<?php

namespace App\Jobs\Admin;

use App\Exceptions\ApiGenericException;
use App\Jobs\Job;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\PermitRequest;
use App\Models\PermitRateDescription;
use App\Models\UserPermitRequest;
use App\Services\LoggerFactory;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use net\authorize\util\LogFactory;
use Excel;
use PHPExcel_Worksheet_Drawing;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class SendExcelEmail extends Job implements ShouldQueue
{
	use InteractsWithQueue, SerializesModels;

	protected $request;
	protected $log;
	protected $toEmails;

	/**
	 * Create a new job instance.
	 *
	 * @return void
	 */
	public function __construct($request)
	{
		$logFactory = new LoggerFactory();
		$this->log = $logFactory->setPath('logs/admin/permit')->createLogger('managepermitlist');
		$this->request = (object) $request;
		$this->getToEmails();
	}

	/**
	 * Execute the job.
	 *
	 * @return void
	 */
	public function handle()
	{
		try{
		$this->log->info('Under handle function.');

		$facilityIds = empty($this->request->facility_id)
			? Facility::where('owner_id', $this->request->partner_id)->pluck('id')
			: collect([$this->request->facility_id]);

		$fromDate = $this->request->from_date ? date("Y-m-d", strtotime($this->request->from_date)) : null;
		$toDate = $this->request->to_date ? date("Y-m-d", strtotime($this->request->to_date)) : null;
		$currentDate = date('Y-m-d');
		$locationName = 'All';
		$garageCode = 'All';
		$brandSetting = null;

		if (isset($this->request->facility_id) && !empty($this->request->facility_id)) {
			$facility = Facility::find($this->request->facility_id);
			$locationName = $facility->full_name;
			$garageCode = $facility->garage_code;
		}

		// Initial Permit Query
		if (isset($this->request->list_api) && $this->request->list_api == 'diamond-user-permit-request-list') {
			$permits = $this->permitRequestSql($fromDate, $toDate, $facilityIds);
			// Filter by permit type if provided
			if (!empty($this->request->permit_type)) {
				#DD pims-12258 replace permit type search with id not name
				$permitRateDescription = PermitRateDescription::find($this->request->permit_type);
				if(isset($permitRateDescription->name)){
					$permits->where('permit_type_name', $permitRateDescription->name);
				}				
			}
		} else {
			$this->log->info($this->request->list_api);
			if($this->request->list_api == 'are-permit-list')
			{
				$this->log->info('get in this');

				$results = $this->getPermitList($this->request);
				

			}
			else
			{
			
				$permits = $this->permitListSql($fromDate, $toDate, $facilityIds);
				// Filter by permit type if provided
				if (!empty($this->request->permit_type)) {
					#DD pims-12258 replace permit type search with id not name
					$permitRateDescription = PermitRateDescription::find($this->request->permit_type);
					if(isset($permitRateDescription->name)){
						$permits->where('permit_type_name', $permitRateDescription->name);
					}	
				}
			}
		}

		if($this->request->list_api != 'are-permit-list')
		{
			$results = $permits->get();
		}

		if ($this->request->list_api != 'are-permit-list' && $results->isEmpty()) {
			throw new ApiGenericException("No Record Found");
		}

		// Prepare Data for Excel
		$finalCodes = [];
		$increment = 1;

		foreach ($results as $permit) {
			$serviceRate = 0;
			$serviceNames = [];

			$method = isset($this->request->list_api) && $this->request->list_api == 'diamond-user-permit-request-list'
				? 'permitRequestExcelData'
				: 'permitListExcelData';

			$finalCodes[] = $this->$method($permit, $serviceRate, $serviceNames, $currentDate, $increment);
			$increment++;
		}

		$color = "#0C4A7A";
		if (isset($partner_id) && !empty($partner_id)) {
			$brandSetting = BrandSetting::where('user_id', $partner_id)->first();
			if (isset($brandSetting) && !empty($brandSetting)) {
				$getLogoId = $brandSetting->id;
				$color = $brandSetting->color;
			}
		}
		if (isset($this->request->list_api) && $this->request->list_api == 'diamond-user-permit-request-list') {
			$excelSheetName = 'PermitRequestExcelSheet' . $currentDate;
		} else {
			$excelSheetName = 'PermitListExcelSheet' . $currentDate;
		}

		// Excel Creation Logic
		Excel::create(
			$excelSheetName,
			function ($excel) use ($finalCodes, $excelSheetName, $brandSetting, $color, $fromDate, $toDate, $locationName, $garageCode) {
				// Set the spreadsheet title, creator, and description
				$excel->setTitle($excelSheetName);
				$excel->setCreator('UserPermitList')->setCompany('ParkEngage');
				// Build the spreadsheet, passing in the payments array
				if (empty($finalCodes)) {
					throw new ApiGenericException('Sorry! No Data Found.');
				} else {
					if (isset($finalCodes) && !empty($finalCodes)) {
						$getDateSummary = 'User Permit List';
						$excel->sheet(
							'Permit List',
							function ($sheet) use ($finalCodes, $brandSetting, $color, $fromDate, $toDate, $locationName, $garageCode, $getDateSummary) {
								$this->addLogoInExcelHeader($sheet, $getDateSummary, $brandSetting, $color);
								$headerSpace = 3;
								$this->getBrandHeaderSection($sheet, $color, $fromDate, $toDate, $locationName, $garageCode);

								// Color Row For Heading 
								$sheet->cell("A$headerSpace:X$headerSpace", function ($row) use ($color) {

									$row->setBackground($color);
									$row->setFontColor('#ffffff');
								});

								$sheet->fromArray($finalCodes, null, 'A' . "$headerSpace", false, true);
							}
						);
					}
				}
			}
		)->store('xls');

		$path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
		$this->log->info('File saved on: ' . $path_to_file);
		$data['report_name']    = 'manage_permit';
		$data['totalPermits']    = count($results);
		$data['themeColor']     = $color;

		if (isset($this->request->list_api) && $this->request->list_api == 'diamond-user-permit-request-list') {
			$data['mail_body'] = "Summary of permit request list Report. A detailed report is also attached.";
			$subject = 'Permit Request List Report - ';
		} else {
			$data['mail_body'] = "Summary of permit list Report. A detailed report is also attached.";
			$subject = 'Permit List Report - ';
		}
		// Email Sending Logic
		Mail::send('admin.manage-permit', $data, function ($message) use ($excelSheetName, $subject) {
			$path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
			$message->to($this->toEmails)
				->subject($subject . date("jS F, Y"))
				->from(config('parkengage.default_sender_email'));
			if (file_exists($path_to_file)) {
				$message->attach($path_to_file);
			}
		});
	}
	catch(Exception $e)
	{
		throw new ApiGenericException($e->getMessage().'line no'.$e->getLine());
	}
	}

	private function getToEmails()
	{
		$this->toEmails = $this->request->emails;
		$this->log->info('requested To emails : ' . json_encode($this->toEmails));
	}

	public function addLogoInExcelHeader($excel, $title = "Permit List", $brandSetting, $color = '#191D88')
	{
		/* Code for logo*/
		if (isset($brandSetting) && !empty($brandSetting)) {
			$drawing = new PHPExcel_Worksheet_Drawing();
			if (env('ISLOCAL')) {
				$drawing->setPath(public_path('assets/media/images/breeze.png'));
			} else {
				// For Dynamic
				if (isset($brandSetting) && !empty($brandSetting)) {
					$drawing->setPath(storage_path('app/brand-settings/' . $brandSetting->logo));
				} else {
					$drawing->setPath(public_path('assets/media/images/breeze.png'));
				}
			}
			$drawing->setCoordinates('A1');
			// Adjust the dimensions of the image if needed
			$drawing->setWidth(150);
			$drawing->setHeight(50);
			$drawing->setOffsetX(25);
			$drawing->setOffsetY(10);
			// Add image to worksheet
			$excel->getDrawingCollection()->append($drawing);
			/* End Code for logo*/
		}

		// !! Text Section 
		// $color = $this->color;
		$excel->mergeCells('A1:I1');
		$excel->getRowDimension(1)->setRowHeight(60);
		$excel->setCellValue('A1', $title);
		$excel->cell('A1:I1', function ($cell) use ($color) {
			$cell->setAlignment('center'); // Center horizontally
			$cell->setValignment('center'); // Center vertically
			$cell->setFontWeight('bold');
			// $cell->setBackground($color);
			// $cell->setFontColor('#ffffff');
			$cell->setFontSize('30');
		});
	}

	public function getBrandHeaderSection($excel, $color, $fromdate, $todate, $locationName, $garageCode)
	{
		$excel->mergeCells('A2:G2');
		$cellValue = "Permit List Date Range - " .  date('m-d-Y', strtotime($fromdate)) .  ' - ' . date('m-d-Y', strtotime($todate));
		$cellValue .= "\nPrint Date - " .  date('m-d-Y', strtotime('now'));
		$excel->setCellValue('A2', "$cellValue");
		$excel->getStyle('A2')->getAlignment()->setWrapText(true);
		$excel->getRowDimension(2)->setRowHeight(80);
		$excel->getRowDimension(1)->setRowHeight(60);

		$excel->cell('A2:G2', function ($cell) use ($color) {
			$cell->setAlignment('center'); // Center horizontally
			$cell->setValignment('center'); // Center vertically
			$cell->setFontWeight('bold');
			$cell->setBackground($color);
			$cell->setFontColor('#ffffff');
			$cell->setBackground('#D6DCE4');
			$cell->setFontColor('#040D12');
			$cell->setFontSize('18');
		});
		//location name changes

		$location = "Location Name \r" . $locationName;
		$excel->mergeCells('H2:P2');
		$excel->setCellValue('H2', "$location");
		$excel->getStyle('H2')->getAlignment()->setWrapText(true);

		$excel->cell('H2:P2', function ($cell) use ($color) {
			$cell->setAlignment('center'); // Center horizontally
			$cell->setValignment('center'); // Center vertically
			$cell->setFontWeight('bold');
			$cell->setBackground('#D6DCE4');
			$cell->setFontColor('#040D12');

			$cell->setFontSize('18');
		});
		//end location
		$excel->mergeCells('Q2:X2');
		$locationId = "Location ID \n" . $garageCode;
		$excel->setCellValue('Q2', "$locationId");
		$excel->getStyle('Q2')->getAlignment()->setWrapText(true);

		$excel->cell('Q2:X2', function ($cell) use ($color) {
			$cell->setAlignment('center'); // Center horizontally
			$cell->setValignment('center'); // Center vertically
			$cell->setFontWeight('bold');
			$cell->setBackground($color);
			$cell->setFontColor('#ffffff');
			$cell->setBackground('#D6DCE4');
			$cell->setFontColor('#040D12');
			$cell->setFontSize('18');
		});
		//Heading 
		$excel->getStyle('A1')->getAlignment()->setWrapText(true);

		$excel->cell('A1:X1', function ($cell) use ($color) {
			$cell->setAlignment('center'); // Center horizontally
			$cell->setValignment('center'); // Center vertically
			$cell->setFontWeight('bold');
			$cell->setBackground($color);
			$cell->setFontColor('#ffffff');
			$cell->setFontSize('30');
		});
	}

	public function permitListSql($fromDate, $toDate, $facilityIds)
	{
		$permit = PermitRequest::with([
			'PermitRate',
			'facility',
			'getPermitRequestRenewHistory' => function ($q) use ($fromDate, $toDate) {
				if ($fromDate && $toDate) {
					$q->join('anet_transactions', 'permit_requests_renew_history.anet_transaction_id', '=', 'anet_transactions.id')
						->whereBetween('desired_start_date', [$fromDate, $toDate]);
				}
			},
			'transaction',
			'PermitVehicle.Vehicle',
			'PermitRequestServiceMapping.permitServices'
		])
			->whereIn('facility_id', $facilityIds)
			->where('partner_id', $this->request->partner_id);

		if (isset($fromDate) && !empty($fromDate) && isset($toDate) && !empty($toDate)) {
			$permit->whereBetween('desired_start_date', [$fromDate, $toDate]);
		}

		return $permit;
	}

	public function previousmonthdata($request)
	{
		$user = Auth::user();
		$partner_id = $request->partner_id ?? null;

		if ($partner_id) {
			if ($user->user_type == '4' || $user->user_type == '12') {
				$admin_partner_id = $user->created_by;
				if ($user->user_type == '4' && $admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
					$partner_id = $request->partner_id ?: 'all';
				} else {
					$partner_id = $admin_partner_id;
					$facility = \DB::table('user_facilities')->where('user_id', $user->id)->whereNull('deleted_at')->pluck('facility_id');
				}
			}
		} else {
			if ($user->user_type == '1') {
				$result = [
					"total" => 0,
					"per_page" => 20,
					"current_page" => 1,
					"last_page" => 1,
					"next_page_url" => null,
					"prev_page_url" => null,
					"from" => null,
					"to" => null,
					"data" => []
				];
				$partner_id = 'all';
			} elseif ($user->user_type == '4' || $user->user_type == '12') {
				$admin_partner_id = $user->created_by;
				if ($user->user_type == '4' && $admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
					$partner_id = $request->partner_id ?: 'all';
				} else {
					$partner_id = $admin_partner_id;
					$facility = \DB::table('user_facilities')->where('user_id', $user->id)->whereNull('deleted_at')->pluck('facility_id');
				}
			} else {
				$partner_id = $user->id;
			}
		}


		if (!empty($request->from_date)) {
			$from_date = date('Y-m-d', strtotime($request->from_date));
			$to_date = date('Y-m-d', strtotime($request->to_date));
		} else {
			$month = date("m");
			$from_date  = date('1970-' . $month . '-01');
			$to_date  = date('Y-' . $month . '-t');
			$mid_date  = date('Y-' . $month . '-15');
			$midDateString = strtotime($mid_date);
			$lastdate = strtotime(date("Y-m-t", $midDateString));
			$to_date = date("Y-m-d", $lastdate);
		}
	

		$permit = PermitRequestRenewHistory::select([
			'id', 'user_id', 'facility_id', 'anet_transaction_id', 'updated_at',
			'created_at', 'desired_start_date', 'desired_end_date', 'email', 'name',
			'phone', 'permit_rate', 'permit_rate_id', 'approved_on', 'account_number',
			'user_consent', 'hid_card_number', 'permit_type_name', 'promocode',
			'discount_amount', 'refund_amount', 'refund_status',
			'refund_transaction_id', 'cancelled_at', 'permit_final_amount', 'business_id', 
			'partner_id', 'admin_user_id'
		])
		->with([
			'PermitRate',
			'facility' => function ($query) { 
				$query->select('id', 'short_name', 'full_name', 'garage_code'); 
			},
			'transaction',
			'PermitVehicle.Vehicle',
			'PermitRequestServiceMapping.permitServices',
		]);

			$permit->with([
				'latestRenewHistory',
    			'createdBy',
			]);
		
		if (!empty($from_date) && !empty($to_date)) 
		{
			$permit->whereBetween('desired_start_date', [$from_date, $to_date]);
		}
	
	
		// Laravel 5.2 me `softDeletes` ka default support nahi hota, isliye check karein
			$permit->whereNull('deleted_at');
		
		// Apply partner_id filter only if it's not 'all'
		if ($partner_id == 'all') {
			// $permit = $permit;
		} else if ((config('parkengage.PARTNER_SUPERADMIN') == Auth::user()->created_by) && (Auth::user()->user_type == '4')) {
			if ($request->partner_id != '') {
				$partner_id = $request->partner_id;
				$permit = $permit->where('permit_requests_renew_history.partner_id', $partner_id);
			}
		} else {
			$permit = $permit->where('permit_requests_renew_history.partner_id', $partner_id);
		}

		$allowedIds = [$partner_id];
		$partnerId = config('parkengage.PARTNER_GROUP_SERVICES');

		if ($partner_id == 'all' || ($partnerId && in_array($partnerId, $allowedIds, true)) ) 
		{
			$listsearch = false;
		} else {
			$listsearch = true;
		}
		

		// Add filters dynamically based on request inputs
		$filters = [
			'payment_last_four' => 'anet_transactions.payment_last_four',
			'card_type' => 'anet_transactions.card_type',
			'expiration' => 'anet_transactions.expiration'
		];
		
		foreach ($filters as $key => $column) {
			if (!empty($request->$key)) {
				$permit->whereHas('transaction', function ($query) use ($column, $request, $key) {
					$query->where($column, $request->$key);
				});
			}
		}
		
		
		$filterFields = [
			'facility_id' => 'permit_requests_renew_history.facility_id',
			'account_name' => 'account_name',
			'business_id' => 'business_id',
			'permit_type' => 'permit_type_name'
		];
		
		foreach ($filterFields as $key => $column) {
			if (!empty($request->$key)) {
				$permit->where($column, trim($request->$key));
			}
		}
		
		$current_date = date('Y-m-d H:i:s');
		
		if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12') {
			$permit->whereIn('facility_id', $facility);
		}
		
		if (Auth::user()->user_type == '4') {
			$user_permit_ids = UserPermitTypeMapping::where('user_id', Auth::user()->id)->pluck('permit_type_id');
			if (count($user_permit_ids) > 0 ) 
			{
				$permit->whereIn('permit_rate_id', $user_permit_ids);
			}
		}

		$now = Carbon::today();
		$fiveDaysBefore = Carbon::today()->addDays(5);
		$fiveDaysAfter = Carbon::today()->addDays(24);
		$permit_renew_date_visible = config('parkengage.PERMIT_RENEW_DATE');

		if ($permit_renew_date_visible) {
			$permit = $permit->selectRaw("
				permit_requests_renew_history.*, 
				IF(
					(business_id IS NULL OR business_id = 0) AND 
					((DATE_FORMAT(?, '%Y-%m') = DATE_FORMAT(desired_end_date, '%Y-%m') AND DAY(?) >= ?) OR 
					(DATE(?) > DATE(desired_end_date))),
					1, 
					0
				) AS permit_renew_pay", [$now, $now, $permit_renew_date_visible, $now]);
		} else {
			$permit = $permit->selectRaw(
				"*, IF((business_id IS NULL OR business_id = 0) AND desired_end_date <= ?, 1, 0) AS permit_renew_pay", 
				[$fiveDaysBefore]
			);
		}

		$permit = $permit->orderBy("permit_requests_renew_history.id", "DESC");
		$permit = $permit->whereBetween('desired_start_date', [$from_date, $to_date]);
		$permit = $permit->whereBetween('desired_end_date', [$from_date, $to_date]);
		
		$permitList = $permit->get();
		
		return $permitList;
	}

	public function getPermitList(\stdClass $request)
	{
		DB::enableQueryLog();
		$start = microtime(true);
		$user = Auth::user();
		$partner_id = $request->partner_id ?? null;

		if ($partner_id) {
			if ($user->user_type == '4' || $user->user_type == '12') {
				$admin_partner_id = $user->created_by;
				if ($user->user_type == '4' && $admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
					$partner_id = $request->partner_id ?: 'all';
					$facility = \DB::table('user_facilities')->whereNull('deleted_at')->pluck('facility_id');
				} else {
					$partner_id = $admin_partner_id;
					$facility = \DB::table('user_facilities')->where('user_id', $user->id)->whereNull('deleted_at')->pluck('facility_id');
				}
			}
		} else {
			if ($user->user_type == '1') {
				$result = [
					"total" => 0,
					"per_page" => 20,
					"current_page" => 1,
					"last_page" => 1,
					"next_page_url" => null,
					"prev_page_url" => null,
					"from" => null,
					"to" => null,
					"data" => []
				];
				$partner_id = 'all';
			} elseif ($user->user_type == '4' || $user->user_type == '12') {
				$admin_partner_id = $user->created_by;
				if ($user->user_type == '4' && $admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
					$partner_id = $request->partner_id ?: 'all';
					$facility = \DB::table('user_facilities')->whereNull('deleted_at')->pluck('facility_id');
				} else {
					$partner_id = $admin_partner_id;
					$facility = \DB::table('user_facilities')->where('user_id', $user->id)->whereNull('deleted_at')->pluck('facility_id');
				}
			} else {
				$partner_id = $user->id;
			}
		}


		if (!empty($request->from_date)) {
			$from_date = date('Y-m-d', strtotime($request->from_date));
			$to_date = date('Y-m-d', strtotime($request->to_date));
		} else {
			$month = date("m");
			$from_date  = date('1970-' . $month . '-01');
			$to_date  = date('Y-' . $month . '-t');
			$mid_date  = date('Y-' . $month . '-15');
			$midDateString = strtotime($mid_date);
			$lastdate = strtotime(date("Y-m-t", $midDateString));
			$to_date = date("Y-m-d", $lastdate);
		}
		
		$currentDate = date('Y-m-d');
		$currentMonth = date('Y-m'); // Current month in Y-m format
		$fromMonth = date('Y-m', strtotime($from_date)); // Month of from_date
		$toMonth = date('Y-m', strtotime($to_date)); // Month of to_date
		
		// Check if either of the dates falls in the current month
		$usePermitHistory = ($fromMonth !== $currentMonth && $toMonth !== $currentMonth);
		
		if($usePermitHistory) {
		
			// Use PermitRequestRenewHistory for both dates not being in current month
			return $this->previousmonthdata($request);

			
		} else {
			// Use PermitRequest if any date is from the current month
			$permit = PermitRequest::select([
				'id', 'user_id', 'facility_id', 'anet_transaction_id', 'updated_at',
				'created_at', 'desired_start_date', 'desired_end_date', 'email', 'name',
				'phone', 'permit_rate', 'permit_rate_id', 'approved_on', 'account_number',
				'user_consent', 'hid_card_number', 'permit_type_name', 'promocode',
				'discount_amount', 'status', 'refund_amount', 'refund_status',
				'refund_transaction_id', 'cancelled_at', 'permit_final_amount', 'business_id', 
				'partner_id', 'admin_user_id'
			])
			->with([
				'PermitRate',
				'facility' => function ($query) { 
					$query->select('id', 'short_name', 'full_name', 'garage_code'); 
				},
				'transaction',
				'PermitVehicle.Vehicle',
				'PermitRequestServiceMapping.permitServices',
				'getPermitRequestRenewHistory' => function ($query) use ($from_date, $to_date) {
					if (!empty($from_date) && !empty($to_date)) {
						$query->whereBetween('desired_start_date', [$from_date, $to_date]);
					}
				}
			]);
		}
		
			$permit->with([
				'latestRenewHistory',
    			'createdBy',
			]);
		
	
		// Laravel 5.2 me `softDeletes` ka default support nahi hota, isliye check karein
		$permit->whereNull('deleted_at');
		
		// Apply partner_id filter only if it's not 'all'
		if ($partner_id == 'all') {
			// $permit = $permit;
		} else if ((config('parkengage.PARTNER_SUPERADMIN') == Auth::user()->created_by) && (Auth::user()->user_type == '4')) {
			if ($request->partner_id != '') {
				$partner_id = $request->partner_id;
				$permit = $permit->where('permit_requests.partner_id', $partner_id);
			}
		} else {
			$permit = $permit->where('permit_requests.partner_id', $partner_id);
		}

		$allowedIds = [$partner_id];
		$partnerId = config('parkengage.PARTNER_GROUP_SERVICES');

		if ($partner_id == 'all' || ($partnerId && in_array($partnerId, $allowedIds, true)) ) 
		{
			$listsearch = false;
		} else {
			$listsearch = true;
		}
		

		// Add filters dynamically based on request inputs
		$filters = [
			'payment_last_four' => 'anet_transactions.payment_last_four',
			'card_type' => 'anet_transactions.card_type',
			'expiration' => 'anet_transactions.expiration'
		];
		
		foreach ($filters as $key => $column) {
			if (!empty($request->$key)) {
				$permit->whereHas('transaction', function ($query) use ($column, $request, $key) {
					$query->where($column, $request->$key);
				});
			}
		}
		
		$filterFields = [
			'facility_id' => 'permit_requests.facility_id',
			'account_name' => 'account_name',
			'business_id' => 'business_id',
			'permit_type' => 'permit_type_name'
		];
		
		foreach ($filterFields as $key => $column) 
		{

			if (!empty($request->$key)) {
				$permit->where($column, trim($request->$key));
			}
		}
		
		
		$current_date = date('Y-m-d H:i:s');
		
		if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12') {
			$permit->whereIn('facility_id', $facility);
		}
		
		if (Auth::user()->user_type == '4') {
			$user_permit_ids = UserPermitTypeMapping::where('user_id', Auth::user()->id)->pluck('permit_type_id')->filter(); // Removes null values;
			// dd(count($user_permit_ids));
			if (count($user_permit_ids) > 0 )  
			{
				$permit->whereIn('permit_rate_id', $user_permit_ids);
			}
		}

		$now = Carbon::today();
		$fiveDaysBefore = Carbon::today()->addDays(5);
		$fiveDaysAfter = Carbon::today()->addDays(24);
		$permit_renew_date_visible = config('parkengage.PERMIT_RENEW_DATE');

		if ($permit_renew_date_visible) {
			$permit = $permit->selectRaw("
				permit_requests.*, 
				IF(
					(business_id IS NULL OR business_id = 0) AND 
					((DATE_FORMAT(?, '%Y-%m') = DATE_FORMAT(desired_end_date, '%Y-%m') AND DAY(?) >= ?) OR 
					(DATE(?) > DATE(desired_end_date))),
					1, 
					0
				) AS permit_renew_pay", [$now, $now, $permit_renew_date_visible, $now]);
		} else {
			$permit = $permit->selectRaw(
				"*, IF((business_id IS NULL OR business_id = 0) AND desired_end_date <= ?, 1, 0) AS permit_renew_pay", 
				[$fiveDaysBefore]
			);
		}
		$permit = $permit->whereBetween('desired_start_date', [$from_date, $to_date]);
		$permit = $permit->whereBetween('desired_end_date', [$from_date, $to_date]);
		
		$permit = $permit->orderBy("permit_requests.id", "DESC");
		
		$permitList = $permit->get();
		
		return $permitList;
		
	}

	public function permitRequestSql($fromDate, $toDate, $facilityIds)
	{
		return UserPermitRequest::select('id', 'facility_id', 'name', 'email', 'phone', 'address', 'address2', 'city', 'state', 'country', 'pincode', 'permit_rate', 'permit_rate_id', 'tracking_code', 'monthly_duration_value', 'no_of_days', 'partner_id', 'desired_start_date', 'desired_end_date', 'user_consent', 'is_admin', 'created_at', 'cancelled_at', 'deleted_at', 'approved_on', 'approved_status', 'is_rejected', 'permit_type', 'is_payment_authrize', 'name_on_card', 'card_last_four', 'card_type', 'card_name', 'expiry', 'tx_state_text', 'tx_state', 'result_reason', 'currency_used', 'permit_type_name', 'acknowledge', 'hid_card_number', 'account_name', 'permit_final_amount', 'user_remark', 'is_negotiable', 'user_type_id', 'discount_amount', 'processing_fee', 'promocode', 'is_prorate_apply', 'business_id', 'ticket_count', 'total_usage', 'remaining_usage', 'penality_cancel_id', 'penality_status', 'reject_reason')
			->with(['PermitRequestServiceMapping', 'PermitVehicle.Vehicle', 'facility.facilityConfiguration', 'facility.FacilityPaymentDetails'])
			->join('users', 'users.email', '=', 'user_permit_requests.email')
			->join('heartland_payment_profile as hpp', 'hpp.user_id', '=', 'users.id')
			->select('user_permit_requests.*', 'users.id as userId', 'hpp.card_type as cardType', 'hpp.card_last_four as cardLastFour', 'hpp.card_name as cardName', 'hpp.expiry as Expiry')
			->where('approved_status', '0')
			->where('hpp.is_default', '1')
			->whereIn('user_permit_requests.facility_id', $facilityIds)
			->where('user_permit_requests.partner_id', $this->request->partner_id)
			->whereBetween('desired_start_date', [$fromDate, $toDate]);

		// ->whereDate('user_permit_requests.desired_start_date', '>=', $from_date)->whereDate('user_permit_requests.desired_start_date', '<=', $to_date);
	}

	public function permitListExcelData($permit, $serviceRate, $serviceNames, $currentDate, $increment)
	{
		foreach ($permit->PermitRequestServiceMapping as $mapping) {
			$serviceRate += $mapping->permit_service_rate;
			if (isset($mapping->permitServices['permit_service_name'])) {
				$serviceNames[] = $mapping->permitServices['permit_service_name'];
			}
		}

		$paymentDetails = $permit->transaction ?? null;
		$paymentLastFour = $paymentDetails->payment_last_four ?? '';
		$cardType = $paymentDetails->card_type ?? '';
		$paymentStatus = $paymentDetails->response_message ?? 'NA';
		$cardExpiry = isset($paymentDetails->expiration) ? implode("/", str_split($paymentDetails->expiration, 2)) : '';

		$permitStatus = $permit->cancelled_at ? 'Canceled'
			: ($permit->desired_end_date < $currentDate ? 'Expired' : 'Active');

		$vehicle = $permit->PermitVehicle->first();
		$licensePlate = isset($vehicle->Vehicle->license_plate_number) ? $vehicle->Vehicle->license_plate_number : '-';

		$permitFinalAmount = $permit->negotiated_amount ?? $permit->permit_final_amount;
		foreach ($permit->getPermitRequestRenewHistory as $history) {
			$permitFinalAmount += $history->negotiated_amount ?? $history->permit_final_amount;
		}

		// Get latest renewal date
		$latestRenewal = $permit->getPermitRequestRenewHistory()->latest('created_at')->first();
		$renewalDate = $latestRenewal ? date("m/d/Y", strtotime($latestRenewal->updated_at)) : '-';

		return [
			'No.' => $increment++,
			'Facility' => $permit->facility->full_name ?? '-',
			'Name' => $permit->name ?? '-',
			'Email' => $permit->email ?? '-',
			'Phone' => $permit->phone ?? '-',
			'License Plate' => $licensePlate,
			'Permit Number' => $permit->account_number ?? '-',
			'Permit Status' => $permitStatus,
			'Permit Type' => $permit->permit_type_name ?? '-',
			'Permit Rate' => '$' . number_format((float)$permit->permit_rate, 2),
			'Permit Service' => implode(', ', $serviceNames),
			'Service Amount' => '$' . number_format((float)$serviceRate, 2),
			'Amount Paid' => '$' . number_format((float)$permitFinalAmount, 2),
			'Processing Fee' => '$' . number_format((float)$permit->processing_fee, 2),
			'Discount' => '$' . number_format((float)$permit->discount_amount, 2),
			'Refund' => '$' . number_format((float)$permit->refund_amount, 2),
			'Card No' => $paymentLastFour,
			'Card Type' => $cardType,
			'Card Expiry' => $cardExpiry,
			'Payment Status' => $paymentStatus,
			'Auto-renewal Consent' => $permit->user_consent ? 'YES' : 'NO',
			'Purchased Date' => $permit->created_at ? date("m/d/Y", strtotime($permit->created_at)) : '-',
			'Last Renewal Date' => $renewalDate,
			'Canceled Date' => $permit->cancelled_at ? date("m/d/Y", strtotime($permit->cancelled_at)) : '',
		];
		// return $finalCodes;
	}

	public function permitRequestExcelData($permit, $serviceRate, $serviceNames, $currentDate, $increment)
	{
		$permit_status = '';
		$license_plate_number = '-';
		$make = '-';
		$model = '-';

		if ($permit->approved_status == '0' && $permit->is_rejected != '1') {
			$permit_status = 'Pending';
		} elseif ($permit->approved_status == '1') {
			$permit_status = 'Approved';
		} elseif ($permit->is_rejected == '1') {
			$permit_status = 'Rejected';
		}

		$vehicle = $permit->PermitVehicle->first();
		if ($vehicle) {
			$license_plate_number = $vehicle->Vehicle->license_plate_number ?? '-';
			$make = $vehicle->Vehicle->make ?? '-';
			$model = $vehicle->Vehicle->model ?? '-';
		}

		return [
			'No.' => $increment,
			'Facility' => $permit->facility->full_name ?? '-',
			'Permit Type' => $permit->permit_type_name ?? '-',
			'Reference Id' => $permit->tracking_code ?? '-',
			'Name' => $permit->name ?? '-',
			'Email' => $permit->email ?? '-',
			'Phone' => $permit->phone ?? '-',
			'Status' => $permit_status,
			'License Plate' => $license_plate_number,
			'Make' => $make,
			'Model' => $model,
		];
	}
}
