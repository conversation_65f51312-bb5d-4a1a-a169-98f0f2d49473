<?php

namespace App\Jobs;

use Exception;
use Monolog\Logger;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;



use App\Exceptions\ApiGenericException;

use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Storage;
use App\Models\MonthlyParkingUser;
use App\Models\User;
use App\Classes\LoyaltyProgram;

/**
 * Checks if an account is active and then credit loyalty points.
 * Steps:
 *
 * @package App\Jobs
 */
class ProcessMonthlyRewardEnrollment extends CronJob implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    const MAIL_QUEUE = 'iqp-loyalty-monthly';
    const LOG_DIRECTORY = 'logs/mpuser';

    protected $user;
    protected $userId;
    protected $account;

    protected $successLog;
    protected $errorLog;

    public function __construct(MonthlyParkingUser $account)
    {  
        $this->account = $account;
        $this->userId = $this->account->user_id;
      
    }

    public function handle()
    { 
        try { 
            $this->createLoggers();
            // loyalty account validations
                     
            $user = User::where('id', '=', $this->userId)->first();
            $this->successLog->info("User with user id {$user->id} enrollement in Icon Rewards Program Started");
           
           
            if($user->is_loyalty == 0 and $user->is_loyalty_active == 0)  {
                $res = LoyaltyProgram::registerMonthlyUserById($user->id);
                
                  if (!$res['success']) {
                        $res = json_encode($res);

                        $this->errorLog->error("There was an error registering the user {$user->id} with loyalty program {$res}");
                  } else {
                         $this->successLog->info("User with user id {$user->id}  enrollement in Icon Rewards Program completed successfully.");
                  }
            } else {
              //  $this->check = false; 
                  $this->errorLog->error("There was an error registering the user with loyalty program as the user {$user->id} is already active but not added card ");

            }
         } catch (Exception $e) {
           $this->errorLog->error($e->getMessage());
          // throw $e;
        
        }
    }

    

    protected function createLoggers()
    {
        $this->errorLog = $this->createLogger('enroll-monthly-parking-user-error');
        $this->successLog = $this->createLogger('enroll-monthly-parking-user-success');
    }
/*
    protected function createLogger($logger_name)
    {
        // setup the log files to have the monthly_user_id and account_number surrounded by brackets after the date /time
        // this should allow for easier parsing in the event something goes wrong
        $format = "[%datetime%][%level_name%][%monthly_user_id%][%account_number%] %message% %context% %extra%\n";
        $path = $this->loggerDirectoryPath() . "/" . $logger_name . ".log";

        $handler = new RotatingFileHandler($path);
        $handler->setFormatter(new LineFormatter($format, null, false, true));
        $logger = new Logger('transaction_status');
        $logger->pushHandler($handler);
        $logger->pushProcessor(
            function ($record) {
                $record['account_number'] = $this->account->account_number;
                $record['monthly_user_id'] = $this->account->id;
                return $record;
            }
        );
        return $logger;
    }

    protected function loggerDirectoryPath()
    {
        $dir = storage_path(self::LOG_DIRECTORY);

        // Verify that directory exists
        if (!Storage::exists($dir)) {
            Storage::makeDirectory($dir);
        }

        return $dir;
    }

*/



}
