<?php

namespace App\Jobs;

use App\Jobs\Job;
use Carbon\Carbon;
use App\Models\Reservation;
use App\Services\LoggerFactory;
use App\Classes\CommonFunctions;
use App\Models\ReservationHistroy;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class HubParking extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    protected $create;
    protected $update;
    protected $reservation_id;
    protected $method;
    protected $carbon;
    protected $external_update;
    
    // protected MAPCO_BASE_URL;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($reservation_id, $method, $external_update=false)
    {
        $logFactory = new LoggerFactory();
        $this->create = $logFactory->setPath('logs/roc/hubapi/create')->createLogger('create_api');
        $this->update = $logFactory->setPath('logs/roc/hubapi/update')->createLogger('update_api');
        $this->carbon = new Carbon();

        $this->reservation_id = $reservation_id;
        $this->method = $method;
        $this->external_update = $external_update;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $log = "";
        $headers = [];
        if ($this->method == "PUT") {
            $log = $this->update;
        } else {
            $log = $this->create;
        }

        $log->info("Reservation Job Start");

        // Set the public and private keys
        $public_key = config('parkengage.HUB_ZEAG.public_key');
        $private_key = config('parkengage.HUB_ZEAG.private_key');

        // Capture the current date/time for the request in UTC
        $messageDate = $this->carbon->now('Asia/Kolkata')->format('Y-m-d\TH:i:sP');
        $log->info("Header Date: $messageDate");

        // Create the message representation, composed of the Verb, resource, content hash (empty), and request date/time
        $representation = "$this->method|/api/reservation||" . $messageDate;

        // Create the hashed message representation using HMAC SHA1 and encode it in base64
        $hmacSHA1 = base64_encode(hash_hmac('sha1', $representation, $private_key, true));

        // Set the headers
        $headers = [
            'x-message-date: ' . $messageDate,
            'x-ctr-signature: CTR ' . $public_key . ':' . $hmacSHA1,
            'Content-Type: application/json'
        ];

        try {
            $reservation = Reservation::with(['facility', 'reservations_history', 'user'])->whereNull('cancelled_at')->where('id', $this->reservation_id)->first();
            if (!$reservation) {
                $log->info("No Reservation found with id " . $this->reservation_id . " on Date: $messageDate");
            } else {
                $reservation_count = ReservationHistroy::where('res_id', $this->reservation_id)->count();

                // Post Data
                $data['BookingId'] = $reservation->ticketech_code;
                $data['BookingVersion'] = $reservation_count;

                $utc_start_timestamp    = $reservation->utc_start_timestamp;
                $utc_end_timestamp      = $reservation->utc_end_timestamp;
                if ($this->external_update) {
                    $log->info("External update");
                    $utc_start_timestamp = Carbon::parse($reservation->start_timestamp)->setTimezone('UTC')->format('Y-m-d\TH:i:s\Z');
                    $utc_end_timestamp = Carbon::parse($reservation->end_timestamp)->setTimezone('UTC')->format('Y-m-d\TH:i:s\Z');
                } 
                // dd( $utc_start_timestamp, $utc_end_timestamp);
                $data['ScheduledArrivalDateTime'] = $utc_start_timestamp;
                $data['ScheduledDepartureDateTime'] = $utc_end_timestamp;

                $data['ArrivalGraceMinutes'] = $reservation->facility->reservation_start_time;
                $data['TotalGraceMinutes'] = $reservation->facility->reservation_grace_period_minute;

                $data['ParkhouseId'] = config('parkengage.HUB_ZEAG.parkhouse_id');
                $data['OverstayParkhouseId'] = config('parkengage.HUB_ZEAG.overstay_parkhouse_id');

                $data['AccessIdentifierType'] = "BarCode";
                $data['AccessIdentifier'] = $reservation->hub_unique_id;
                $data['AccessMediaReminder'] = $reservation->thirdparty_code;;

                $trimmedName = trim(preg_replace('/\s+/', ' ', $reservation->user->name));
                $name = explode(" ", $trimmedName);
                $log->info('User Name: ' . $reservation->user->name . " " . json_encode($name) . " User id: " . $reservation->user->id);

                $data['FirstName'] = $name[0];
                $data['LastName'] = $name[1] ?? $name[0];
                $data['VehiclePlate'] = $reservation->license_plate;
                $data['ParkingAmount'] = $reservation->total;
                $data['AddOnAmount'] = 0;

                $log->info('Local time: ' . $reservation->start_timestamp . ' UTC Time Zone: ' . $data['ScheduledArrivalDateTime']);
                $post_data = json_encode($data);

                $base_url = config('parkengage.HUB_ZEAG.base_url');
                $url = $base_url . '/api/reservation';

                $log->info('HubParking Api Request Data ' . $post_data . ', from Job Url: ' . $url . ' Headers: ' . json_encode($headers));

                $response = CommonFunctions::makeRequest($url, $this->method, $headers, $data);
                $log->info('HubParking Api Response Data ' . json_encode($response));

                $reservation = Reservation::find($this->reservation_id);
                $is_failed_email = false;

                switch ($this->method) {
                    case 'POST':
                        if (isset($response['http_code']) && in_array($response['http_code'], [201])) {
                            $log->info('Saved on Hub Zeag');
                            $reservation->is_hub_zeag = 2;
                        } else {
                            $log->info('Failed to Create on Hub Zeag');
                            $reservation->is_hub_zeag = 5;
                            $is_failed_email = true;
                        }
                        break;

                    case 'PUT':
                        if (isset($response['http_code']) && in_array($response['http_code'], [200, 204])) {
                            $log->info('Updated on Hub Zeag');
                            $reservation->is_hub_zeag = $response['http_code'] == 204 ? 1 : 3;
                            $log->info($response['http_code'] == 204 ? 'Update hit without creating' : 'Update Successfully');

                            if ($this->external_update && $response['http_code'] == 200) {
                                $reservation->utc_start_timestamp = $utc_start_timestamp;
                                $reservation->utc_end_timestamp = $utc_end_timestamp;
                            }

                            if ($response['http_code'] == 204) {
                                $is_failed_email = true;
                            }
                        } else {
                            $log->info('Failed to Update on Hub Zeag');
                            $reservation->is_hub_zeag = 6;
                            $is_failed_email = true;
                        }
                        break;

                    default:
                        $log->warning('Unknown operation type: ' . $this->method);
                        break;
                }

                $reservation->hub_zeag_response = json_encode($response);
                $reservation->save();

                if ($is_failed_email) {
                    Artisan::call('hubzeag:failed_notification', [
                        '--ticketech_code' => $reservation->ticketech_code,
                    ]);
                }

                return $response;
            }
            $log->info("Reservation Job End");
            
        } catch (\Throwable $th) {
            $log->info('Error ' . $th->getMessage() . ' File Name :  ' . $th->getFile() . ' Line ' . $th->getLine());
            $errorMessage = 'ROC Exception : ' . $th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine();
            $data['partner_key'] = "ROC";
            $data['exception'] = $errorMessage;
        }
    }
}
