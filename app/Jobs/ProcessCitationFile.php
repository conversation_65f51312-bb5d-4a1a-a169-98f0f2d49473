<?php 

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\LoggerFactory;
use App\Models\PermitVehicle;
use App\Models\User;
use Log;
use DB;

class ProcessCitationFile implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $filename;
    protected $log;

    public function __construct($filename)
    {
        $this->filename = $filename;
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/citation/usm')->createLogger('citation-notification');
    }

    public function handle()
    {
        $configPath = config('parkengage.USM_PATH_FOR_EXCELPATH');
        $folderPath = $configPath ? public_path($configPath) : public_path('usm');
        $path = $folderPath . '/' . $this->filename;
        $this->log->info("path: " . $path);
        $this->log->info("processed file name in job: ".json_encode( $this->filename));
        // Check if file exists before processing
        if (!file_exists($path)) {
            $this->log->info('File not found: ' . $path);
            return;
        }

        $this->log->info("Processing file in job: ".json_encode( $this->filename));

        try {
            // Using 'load' instead of 'toArray' for Laravel 5.2 compatibility
            $data = Excel::load($path, function($reader) {
                // Optionally manipulate the reader object here
            })->get()->toArray();

            if (empty($data)) {

                $this->log->info("No data found in file: ".json_encode( $this->filename));
                return;
            }
            $custominfo = DB::table('contact_info_usm_integration')->first();
            foreach ($data as $row) {
                $this->log->info("excel row data: ".json_encode( $row));
                $vechiledata = PermitVehicle::where('license_plate_number',$row['license_plate'])->select('user_id')->first();
                $this->log->info("vechile data: ".( $vechiledata));
                if(!empty($vechiledata))
                {
                    $userdata = User::where('id',$vechiledata->user_id)->where('anon','0')->first();
                    $this->log->info("user data: ".( $userdata));
                    $email = $userdata->email;
                    $this->log->info("email: ".( $email));
                    if ($userdata) {
                        Mail::send('email.citation_notification', ['data' => $row,'user'=>$userdata,'custominfo' => (array)$custominfo], function ($message) use ($userdata,$custominfo,$email, $row) {
                            $message->to($email);
                            $message->from(env('MAIL_DEFAULT_FROM'));
                            $message->subject('Enforcement Notice – ' . ($row['citation_number'] ?? 'No Ticket Number'));
                        });
                    }
                }
            }

            $this->log->info("File processed successfully: ".json_encode( $this->filename));
            

        } catch (\Exception $e) {
            $this->log->info('Error processing file: ' . $this->filename . ' - ' . $e->getMessage().'line no :'.$e->getLine());

        }
    }
}
