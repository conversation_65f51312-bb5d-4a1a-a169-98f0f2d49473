<?php
namespace App\Jobs;
use Exception;
use App\Exceptions\ApiGenericException;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Storage;
use App\Models\LoyaltyUserAccounts;
use App\Models\Reservation;
use App\Models\Facility;
use App\Classes\Inventory;
use App\Models\HoursOfOperation;
use App\Models\FacilityInventory;
use App\Models\FacilityAvailability;
use App\Models\FacilityPartnerAvailability;
use Carbon\Carbon;
use App\Models\PartnerReservation;
/**
 * Need to update facilityAvailability table in inventory partner database for 
 * 
 * @package App\Jobs
 */
class UpdateFacilityInventory extends CronJob implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    const MAIL_QUEUE = 'iqp-inventory';
    const LOG_DIRECTORY = 'logs/inventory';
    const ADD_EXTRA_DAY_COUNT = 1;
    const UPDATE_INVENTORY_TRUE = 1;
    const UPDATE_INVENTORY_FALSE = 0;
    const TWENTY_FOUR_HOURS = 23;
    const DEFAULT_HOURS = 0;
    const REALTIME_WINDOW = 2;
    const END_TIME_EXTRA_MINUTES = 30;
    const ICON_TYPE = 'PARKENGAGE';
    const PARTNER_TYPE = 'PARTNER';
    protected $reservationId;
    protected $reservation;
    protected $successLog;
    protected $errorLog;
    protected $reservationType;
            

    public function __construct($reservationId , $type = self::ICON_TYPE)
    {  
        $this->reservationId = $reservationId;
        $this->reservationType = $type;
        if($this->reservationType == self::ICON_TYPE)
       {
         $this->reservation = Reservation::where('id', $reservationId)->first();
       }else{
           $this->reservation = PartnerReservation::where('id', $reservationId)->first();
       }  
    }

    public function handle()
    {
       
        try { 
            $this->createLoggers();
            $this->successLog->info("{ $this->reservationId} Job started..");
            
            if(!$this->reservation) {
           
              $this->errorLog->info("{ $this->reservationId} issue {$this->reservationType}...");
              throw new NotFoundException('No reservation found for this id.');
            } 
            
            // call as per the reservation type
            if($this->reservationType == self::ICON_TYPE)
            {
                $this->successLog->info("{ $this->reservationId} {$this->reservationType}Job started for ICON..");
                $this->updateReservationAvailability($this->reservationId);
                
            }else if($this->reservationType == self::PARTNER_TYPE)
            {
                
                $this->successLog->info("{ $this->reservationId} Job started for PARTNER..");
                $this->updateReservationAvailabilityPartner($this->reservationId);
            }else{
                 $this->errorLog->info("Something is wrong with reservation type");
            }
       
            $this->successLog->info("job completed for".$this->reservationId);
        } catch (Exception $e) {
           $this->errorLog->error($e->getMessage());
           throw $e;
        }
    }

     public function updateReservationAvailability($reservation_id)
   {
       
      try{
       $reservation = Reservation::where('id',$reservation_id)->first();
       $facility_id= $reservation->facility_id;
       
       $date_time_in= date('Y-m-d H:i:s',strtotime($reservation->start_timestamp));        
       
       $length= round($reservation->length,0);
       
       $date_time_out =  date('Y-m-d H:i:s',strtotime(Carbon::parse($date_time_in)->addHours($length)));
       $reservation_length= $reservation->length;
       $reservation_minutes = 0;
       $reservation_hours = explode(".",$reservation->length);
        if(isset($reservation_hours[1]) && ($reservation_hours[1])>0)
        {
                $reservation_minutes = 30;
        }
       $reservation_date_time_out =  date('Y-m-d H:i:s',strtotime(Carbon::parse($date_time_in)->addHours($reservation_length)->addMinutes($reservation_minutes)));

       $inventoryRepository = new Inventory();
      
      //check how many slots does entry and exit time occupies
       $difference = date_diff(date_create(date('Y-m-d',strtotime($date_time_in))), date_create(date('Y-m-d',strtotime(($date_time_out)))));
       
     //check if someone is parking for more than a day
      
      if ($difference->d > 0) {
        $dates   = $inventoryRepository->generateArrayOfDates(
          ($difference->d + self::ADD_EXTRA_DAY_COUNT), date('Y-m-d H:i:s', strtotime($date_time_in)));
      
      $dayDifference = $difference->d;
      
      foreach ($dates as $key => $date) {

        $dayIn = date('w', strtotime($date->format('Y-m-d')));
       
        $hours = HoursOfOperation::where(['facility_id' => $facility_id, 'day_of_week' => $dayIn])->orderBy('open_time', 'ASC')->first();
        $hoursEnd = HoursOfOperation::where(['facility_id' => $facility_id, 'day_of_week' => $dayIn])->orderBy('close_time', 'DESC')->first();
       
        $startingHour = self::DEFAULT_HOURS;
        $endingHour   = self::TWENTY_FOUR_HOURS;

        if ($hours) {
          $startingHour = date('G', strtotime($hours->open_time));
          $endingHour   = date('G', strtotime($hoursEnd->close_time));
        }

        $facilityInventory = FacilityInventory::where(
            ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')])->first();

        $facilityAvailability = FacilityAvailability::where(
            ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')])->first();
        
        $facilityAvailabilityPartner = FacilityPartnerAvailability::where(
            ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')])->first();
        
        
        if ($facilityAvailability) {
          $inventory = json_decode($facilityAvailability->availability);
          $inventory_partner = json_decode($facilityAvailabilityPartner->availability);
          if ($key == 0) {
            /**
             * because this is the first day in the dates provided
             * we should remove 1 from each time_slot starting
             * from the hour provided in the api call
             */
            $i = date('G', strtotime($date_time_in));
            if ($startingHour > $i) {
              $i = $startingHour;
            }

            $loopEnd = ($endingHour < self::TWENTY_FOUR_HOURS) ? $endingHour : self::TWENTY_FOUR_HOURS;
            while ($i <= $loopEnd) {
             if(isset( $inventory->{$i}))
             {
              $inventoryCount = $inventory->{$i};
              $inventory_partner->{$i} = $inventoryCount;
             }
              //change on 2-7-2019
//              if($inventoryCount <=0)
//              {
//                  $inventory_partner->{$i} = self::UPDATE_INVENTORY_FALSE;
//              }else{
//                   $inventory_partner->{$i} = self::UPDATE_INVENTORY_TRUE;
//              }
              $i++;
            }
          } elseif ($key == $dayDifference) {
            $i = date('G', strtotime($date_time_out));
            $minutes = date('i', strtotime($reservation_date_time_out));
      $starting_minutes = date('i', strtotime($date_time_in));
            if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
                $i++;
            }
            
            /**
             * because this is the last day in the dates provided
             * we should remove 1 from each time_slot starting
             * till the hour provided in the api call
             */
          
            $j  = 0;
            while ($j < $i) {
                if(isset($inventory->{$j}))
               {
                    $inventoryCount = $inventory->{$j};   
                    $inventory_partner->{$j} = $inventoryCount;
                    
//                    if($inventoryCount <=0)
//                    {
//                        $inventory_partner->{$j} = self::UPDATE_INVENTORY_FALSE;
//                    }else{
//                         $inventory_partner->{$j} = self::UPDATE_INVENTORY_TRUE;
//                    }
                     
                  
               }
               $j++;
            }
          } else {
            /**
             * because this could be any day except first and last in
             * the dates provided we should remove 1 from whole day
             */
            
            $k = 0;
            while ($k <= self::TWENTY_FOUR_HOURS) {
               if(isset($inventory->{$k}))
               {
                    $inventoryCount = $inventory->{$k};   
                    $inventory_partner->{$k} = $inventoryCount;
//                    if($inventoryCount <=0)
//                    {
//                        $inventory_partner->{$k} = self::UPDATE_INVENTORY_FALSE;
//                    }else{
//                         $inventory_partner->{$k} = self::UPDATE_INVENTORY_TRUE;
//                    }
               }
                $k++;
            }
          }
        //update avaliablity when partner_shared_value has value by vikrant  
        $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($facilityInventory->availability, true), $inventory, $facility_id);
        if($newPartnerAvailability){
          
          $facilityAvailabilityPartner->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
          $facilityAvailabilityPartner->save();
        }


        }
      }
      
     } else {
      $startingHour = date('G', strtotime($date_time_in));
      $endingHour   = date('G', strtotime($date_time_out));

      $facilityInventory = FacilityInventory::where(['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))])->first();

      $facilityAvailability     = FacilityAvailability::where(
          ['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))])->first();
      $facilityAvailabilityPartner     = FacilityPartnerAvailability::where(
          ['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))])->first();
      if ($facilityAvailability) {
        $availability = json_decode($facilityAvailability->availability);
        $availability_partner = json_decode($facilityAvailabilityPartner->availability);
        $minutes = date('i', strtotime($reservation_date_time_out));
  $starting_minutes = date('i', strtotime($date_time_in));
        if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
            $endingHour++;
        }
       
        while ($startingHour < $endingHour) {
                $inventoryCount = $availability->{$startingHour}; 
                $availability_partner->{$startingHour} = $inventoryCount;
//                if($inventoryCount <=0)
//                {
//                    $availability_partner->{$startingHour} = self::UPDATE_INVENTORY_FALSE;
//                }else{
//                     $availability_partner->{$startingHour} = self::UPDATE_INVENTORY_TRUE;
//                }
              $startingHour++;
        }

        //update avaliablity when partner_shared_value has value by vikrant
        $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($facilityInventory->availability, true), $availability, $facility_id);
        if($newPartnerAvailability){

          $facilityAvailabilityPartner->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
          $facilityAvailabilityPartner->save();
        }
        
      }
    }
      } catch (\Exception $e)
      {
          $this->errorLog->info($e);
      }
   }
   
   
    public function updateReservationAvailabilityPartner($reservation_id)
   {
      try{
          
       $reservation = PartnerReservation::where('id',$reservation_id)->first();
       $facility_id= $reservation->location_id;
       
       $date_time_in= date('Y-m-d H:i:s',strtotime($reservation->date_time_in));        
       
       $date_time_out =  date('Y-m-d H:i:s',strtotime($reservation->date_time_out));     
       
       $inventoryRepository = new Inventory();
      
      //check how many slots does entry and exit time occupies
       $difference = date_diff(date_create(date('Y-m-d',strtotime($date_time_in))), date_create(date('Y-m-d',strtotime(($date_time_out)))));
       
     //check if someone is parking for more than a day
      
      if ($difference->d > 0) {
         
        $dates   = $inventoryRepository->generateArrayOfDates(
          ($difference->d + self::ADD_EXTRA_DAY_COUNT), date('Y-m-d H:i:s', strtotime($date_time_in)));
      
      $dayDifference = $difference->d;
      
      foreach ($dates as $key => $date) {

        $dayIn = date('w', strtotime($date->format('Y-m-d')));
       
        $hours = HoursOfOperation::where(['facility_id' => $facility_id, 'day_of_week' => $dayIn])->orderBy('open_time', 'ASC')->first();
        $hoursEnd = HoursOfOperation::where(['facility_id' => $facility_id, 'day_of_week' => $dayIn])->orderBy('close_time', 'DESC')->first();
       
        $startingHour = self::DEFAULT_HOURS;
        $endingHour   = self::TWENTY_FOUR_HOURS;

        if ($hours) {
          $startingHour = date('G', strtotime($hours->open_time));
          $endingHour   = date('G', strtotime($hoursEnd->close_time));
        }

        $facilityInventory = FacilityInventory::where(
            ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')])->first();

        $facilityAvailability = FacilityAvailability::where(
            ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')])->first();
        
        $facilityAvailabilityPartner = FacilityPartnerAvailability::where(
            ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')])->first();
        
        
        if ($facilityAvailability) {
          $inventory = json_decode($facilityAvailability->availability);
          $inventory_partner = json_decode($facilityAvailabilityPartner->availability);
          if ($key == 0) {
            /**
             * because this is the first day in the dates provided
             * we should remove 1 from each time_slot starting
             * from the hour provided in the api call
             */
            $i = date('G', strtotime($date_time_in));
            if ($startingHour > $i) {
              $i = $startingHour;
            }

            //$loopEnd = ($endingHour < self::TWENTY_FOUR_HOURS) ? $endingHour : self::TWENTY_FOUR_HOURS;
            $loopEnd = self::TWENTY_FOUR_HOURS;
            while ($i <= $loopEnd) {
              if(isset($inventory->{$i})){
                $inventoryCount = $inventory->{$i};
              $inventory_partner->{$i} = $inventoryCount;
              }
              
//              if($inventoryCount <=0)
//              {
//                  $inventory_partner->{$i} = self::UPDATE_INVENTORY_FALSE;
//              }else{
//                   $inventory_partner->{$i} = self::UPDATE_INVENTORY_TRUE;
//              }
              $i++;
            }
          } elseif ($key == $dayDifference) {
            $i = date('G', strtotime($date_time_out));
            $minutes = date('i', strtotime($date_time_out));
            if ($minutes >= self::END_TIME_EXTRA_MINUTES) {
                $i++;
            }
            /**
             * because this is the last day in the dates provided
             * we should remove 1 from each time_slot starting
             * till the hour provided in the api call
             */
          
            $j  = 0;
            while ($j < $i) {
                if(isset($inventory->{$j}))
               {
                    $inventoryCount = $inventory->{$j};  
                    $inventory_partner->{$j} = $inventoryCount;
//                    if($inventoryCount <=0)
//                    {
//                        $inventory_partner->{$j} = self::UPDATE_INVENTORY_FALSE;
//                    }else{
//                         $inventory_partner->{$j} = self::UPDATE_INVENTORY_TRUE;
//                    }
                     
                  
               }
               $j++;
            }
          } else {
            /**
             * because this could be any day except first and last in
             * the dates provided we should remove 1 from whole day
             */
            
            $k = 0;
            while ($k <= self::TWENTY_FOUR_HOURS) {
               if(isset($inventory->{$k}))
               {
                    $inventoryCount = $inventory->{$k};      
                    $inventory_partner->{$k} = $inventoryCount;
//                    if($inventoryCount <=0)
//                    {
//                        $inventory_partner->{$k} = self::UPDATE_INVENTORY_FALSE;
//                    }else{
//                         $inventory_partner->{$k} = self::UPDATE_INVENTORY_TRUE;
//                    }
               }
                $k++;
            }
          }

          //update avaliablity when partner_shared_value has value by vikrant
        $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($facilityInventory->availability, true), $inventory, $facility_id);
        if($newPartnerAvailability){
          $facilityAvailabilityPartner->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
          $facilityAvailabilityPartner->save();
        }
        
        }
      }
      
     } else {
       
      $startingHour = date('G', strtotime($date_time_in));
      $endingHour   = date('G', strtotime($date_time_out));

      $facilityInventory = FacilityInventory::where(
            ['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))])->first();      
      $facilityAvailability     = FacilityAvailability::where(
          ['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))])->first();
      
      $facilityAvailabilityPartner     = FacilityPartnerAvailability::where(
          ['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))])->first();
    
      if ($facilityAvailability) {
        $availability = json_decode($facilityAvailability->availability);
        $availability_partner = json_decode($facilityAvailabilityPartner->availability);
        $minutes = date('i', strtotime($date_time_out));
        if ($minutes >= self::END_TIME_EXTRA_MINUTES) {
            $endingHour++;
        }
        while ($startingHour < $endingHour) {
          if(isset($availability->{$startingHour})){

          
                $inventoryCount = $availability->{$startingHour};      
                 $availability_partner->{$startingHour} = $inventoryCount;
//                if($inventoryCount <=0)
//                {
//                    $availability_partner->{$startingHour} = self::UPDATE_INVENTORY_FALSE;
//                }else{
//                     $availability_partner->{$startingHour} = self::UPDATE_INVENTORY_TRUE;
//                }
          }
              $startingHour++;
        }
        
           //update avaliablity when partner_shared_value has value by vikrant
        $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($facilityInventory->availability, true), $availability, $facility_id);
        if($newPartnerAvailability){
          $facilityAvailabilityPartner->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
        $facilityAvailabilityPartner->save();
        }



      }
    }
      } catch (\Exception $e)
      {
          $this->errorLog->info($e);
      }
   }



    protected function createLoggers()
    {
        $this->errorLog = $this->createLogger('inventory-updateerror');
        $this->successLog = $this->createLogger('inventory-update-success');
    }
}
