<?php

namespace App\Jobs\ManageBookingHistory;

use App\Exceptions\ApiGenericException;
use App\Http\Helpers\MailHelper;
use App\Models\Reservation;
use App\Services\LoggerFactory;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use App\Jobs\Job;
use Illuminate\Support\Facades\Auth;

class SendBookingReportEmail extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;


    protected $successLog;
    protected $errorLog;
    protected $request;
    protected $requestData;
    protected $toEmails;
    protected $partner_id;
    protected $facility;

    /**
     * Create a new job instance.
     */
    public function __construct(Request $request, $partner_id, $facility)
    {
        $logFactory = new LoggerFactory();
        $this->successLog = $logFactory->setPath('logs/booking_report_job')->createLogger('sendmail_success');
        $this->errorLog = $logFactory->setPath('logs/booking_report_job')->createLogger('sendmail_error');

        // Extract only necessary, serializable data from the Request
        $this->requestData = [
            'from_date' => $request->from_date,
            'to_date' => $request->to_date,
            'search' => $request->search,
            'booking_type' => $request->booking_type,
            'facility_id' => $request->facility_id,
            'sort' => $request->sort,
            'sortBy' => $request->sortBy,
            'emails' => $request->emails ?? [$request->email]
        ];

        $this->partner_id = $partner_id;
        $this->facility = $facility;
        $this->getToEmails();
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $this->successLog->info('Starting booking report email job: ' . json_encode($this->requestData));

        try {
            $reservation = $this->getReservationData();

            $excelSheetName = "Booking-Details-Report-" .
                date("jS M, Y", strtotime($this->requestData['from_date'])) .
                ' - ' .
                date("jS M, Y", strtotime($this->requestData['to_date']));

            $finalCodes1 = $this->prepareExcelData($reservation);

            if (empty($finalCodes1)) {
                throw new ApiGenericException('Sorry! No Data Found.');
            }

            $path_to_file = $this->generateExcelFile($excelSheetName, $finalCodes1);

            $this->successLog->info('File Saved on: ' . $path_to_file);

            $this->sendEmail($excelSheetName, $path_to_file, count($finalCodes1));

            // Clean up the file
            if (file_exists($path_to_file)) {
                unlink($path_to_file);
            }
        } catch (\Throwable $th) {
            $this->errorLog->info('Error: ' . $th->getMessage() . ' File: ' . $th->getFile() . ' Line: ' . $th->getLine());

            $data = [
                'partner_key' => "PCI",
                'exception' => "Exception in Booking Report Email Request: " . $th->getMessage()
            ];

            Mail::send('email-exception', $data, function ($message) {
                $message->to('<EMAIL>')
                    ->subject("Email Exception: PCI - Booking Report Email Request")
                    ->from(env('INFO_MAIL_PARKENGAGE_FROM'), env('MAIL_USERNAME'));
            });
        }
    }

    /**
     * Prepare the data for Excel export
     */
    private function prepareExcelData($reservation)
    {
        $finalCodes1 = [];
        $increment1 = 1;

        foreach ($reservation as $val) {
            if ($this->partner_id == '358642') {
                $length = explode('.', $val->length);
                $endtime = Carbon::parse($val->start_timestamp)->addHours($length[0]);
                if (isset($length[1]) && $length[1] > 0) {
                    $endtime = $endtime->addMinutes($length[1]);
                }
                $finalCodes1[] = [
                    'No.' => $increment1,
                    'Booking Id' => $val->ticketech_code,
                    'License Plate' => $val->license_plate,
                    'Booking Amount ($)' => $val->total,
                    'Booking Date' => date("m/d/Y", strtotime($val->start_timestamp)),
                    'Booking Day' => date("m/d/Y h:i A", strtotime($val->created_at)),
                    'Start Date/Time' => date('m/d/Y h:i A', strtotime($val->start_timestamp)),
                    'End Date/Time' => date('m/d/Y h:i A', strtotime($endtime)),
                    'User' => isset($val->user->email) ? $val->user->email : '',
                    'Phone' => isset($val->user->phone) ? $val->user->phone : '',
                    'Status' => $val->cancelled_at != '' ? "Canceled" : '-'
                ];
            } else {
                $finalCodes1[] = [
                    'No.' => $increment1,
                    'Booking Id' => $val->ticketech_code,
                    'License Plate' => $val->license_plate,
                    'Booking Amount ($)' => $val->total,
                    'Booking Date' => date("m/d/Y", strtotime($val->start_timestamp)),
                    'Booking Day' => date("m/d/Y h:i A", strtotime($val->created_at)),
                    'Booking Against Pass' => $val->user_pass_id != '' ? 'Yes' : 'NA',
                    'Pass ID' => isset($val->userPass->pass_code) ? $val->userPass->pass_code : '-',
                    'Pass Rate' => isset($val->userPass->total) ? $val->userPass->total : '-',
                    'Pass Days' => isset($val->userPass->total_days) ? $val->userPass->total_days : '-',
                    'User' => isset($val->user->email) ? $val->user->email : '',
                    'Phone' => isset($val->user->phone) ? $val->user->phone : '',
                    'Status' => $val->cancelled_at != '' ? "Canceled" : '-'
                ];
            }
            $increment1++;
        }

        return $finalCodes1;
    }

    /**
     * Generate and store the Excel file
     */
    private function generateExcelFile($excelSheetName, $finalCodes1)
    {
        Excel::create($excelSheetName, function ($excel) use ($finalCodes1, $excelSheetName) {
            $excel->setTitle($excelSheetName);
            $excel->setCreator('BookingDetails')->setCompany('ParkEngage');
            $excel->setDescription('List Of Bookings');

            $excel->sheet('Booking Details', function ($sheet) use ($finalCodes1) {
                $color = "#2C4293"; // Dark blue for header and totals
                $sheet->setWidth([
                    'A' => 6,    // No.
                    'B' => 22,   // Booking Id
                    'C' => 22,   // License Plate
                    'D' => 20,   // Booking Amount ($)
                    'E' => 20,   // Booking Date
                    'F' => 25,   // Booking Day
                    'G' => 15,   // Booking Against Pass or Start Date/Time
                    'H' => 15,   // Pass ID or End Date/Time
                    'I' => 15,   // Pass Rate or User
                    'J' => 15,   // Pass Days or Phone
                    'K' => 15,   // User or Status
                    'L' => 15,   // Phone
                    'M' => 15    // Status
                ]);

                // Header Row 1: Title and Print Date
                $location = date('d M, Y', strtotime($this->requestData['from_date'])) . '-' . date('d M, Y', strtotime($this->requestData['to_date']));
                $sheet->mergeCells('A1:F1');
                $sheet->setCellValue('A1', "Booking Summary - " . $location);
                $sheet->getStyle('A1')->getAlignment()->setWrapText(true);
                $printDate = "Print Date - " . date('d M, Y');
                $sheet->mergeCells('G1:M1');
                $sheet->setCellValue('G1', $printDate);
                $sheet->getStyle('G1')->getAlignment()->setWrapText(true);
                $sheet->setHeight(1, 80);
                $sheet->cells('A1:M1', function ($cells) use ($color) {
                    $cells->setAlignment('center');
                    $cells->setValignment('center');
                    $cells->setFont(['bold' => true, 'size' => 22, 'color' => ['argb' => 'FFFFFFFF']]);
                    $cells->setBackground($color);
                });

                // Row 2: Spacer
                $sheet->mergeCells('A2:M2');

                // Row 3: Column Headers
                $sheet->fromArray($finalCodes1, null, 'A3', true); // true to include headers
                $sheet->cells('A3:M3', function ($cells) {
                    $cells->setAlignment('center');
                    $cells->setValignment('center');
                    $cells->setFont(['bold' => true]);
                    $cells->setBackground('#D9E1F2'); // Light blue
                    $cells->setFontColor('#000000');
                });

                // Data Rows
                $i = 3; // Start after headers
                $ticketCount = $totalAmount = 0;
                foreach ($finalCodes1 as $index => $value) {
                    $rowNumber = $i + $index + 1; // +1 because $i is already 3, and we start at row 4
                    $ticketCount++;
                    $totalAmount += is_numeric($value['Booking Amount ($)']) ? (float)$value['Booking Amount ($)'] : 0.00;
                    $sheet->cells("A$rowNumber:M$rowNumber", function ($cells) {
                        $cells->setAlignment('center');
                        $cells->setValignment('center');
                    });
                }

                // Totals Row
                $k = $i + count($finalCodes1) + 1; // Place totals right after last data row
                $sheet->cells("A$k:M$k", function ($cells) use ($color) {
                    $cells->setAlignment('center');
                    $cells->setValignment('center');
                    $cells->setFont(['bold' => true, 'size' => 12, 'color' => ['argb' => 'FFFFFFFF']]);
                    $cells->setBackground($color);
                });
                $sheet->setCellValue("A$k", 'Total');
                $sheet->setCellValue("B$k", '-');
                $sheet->setCellValue("C$k", '-');
                $sheet->setCellValue("D$k", number_format($totalAmount));
                $sheet->setCellValue("E$k", '-');
                $sheet->setCellValue("F$k", '-');
                $sheet->setCellValue("G$k", '-');
                $sheet->setCellValue("H$k", '-');
                $sheet->setCellValue("I$k", '-');
                $sheet->setCellValue("J$k", '-');
                $sheet->setCellValue("K$k", '-');
                $sheet->setCellValue("L$k", '-');
                $sheet->setCellValue("M$k", $ticketCount ?: 0);
            });
        })->store('xls');

        return storage_path('exports/' . $excelSheetName . '.xls');
    }

    /**
     * Send the email with the attached Excel file
     */
    private function sendEmail($excelSheetName, $path_to_file, $totalTickets)
    {
        $dateRange = date("jS M, Y", strtotime($this->requestData['from_date'])) .
            ' - ' .
            date("jS M, Y", strtotime($this->requestData['to_date']));

        $data = [
            'totalTickets' => $totalTickets,
            'report_name' => 'booking_report',
            'facilityWiseTicketsCount' => ['Bookings' => $totalTickets],
            'themeColor' => '#0C4A7A',
            'mail_body' => "Please find attached the booking report for the period $dateRange.",
            'team_regards' => 'Parkengage',
            'subject' => "Booking Report - $dateRange"
        ];

        $filedata = [
            'type' => 'saved',
            'content' => $path_to_file,
            'filename' => $path_to_file,
            'format' => 'xls'
        ];

        $data['filedata'] = $filedata;

        // Loop through each email and send individually
        foreach ($this->toEmails as $email) {
            try {
                MailHelper::sendEmail([$email], 'email.booking_report', $data, $this->partner_id);
                $this->successLog->info("Mail sent successfully to: " . $email);
            } catch (\Throwable $th) {
                $this->errorLog->info("Failed to send email to: $email. Error: " . $th->getMessage());
                // Continue to the next email without stopping the process
                continue;
            }
        }
    }

    /**
     * Get reservation data based on request parameters
     */
    private function getReservationData()
    {
        $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction']);


        if (!empty($this->requestData['from_date']) && !empty($this->requestData['to_date'])) {
            // Convert date format from mm/dd/yyyy to yyyy-mm-dd
            $fromDate = \Carbon\Carbon::createFromFormat('m/d/Y', $this->requestData['from_date'])->format('Y-m-d');
            $toDate = \Carbon\Carbon::createFromFormat('m/d/Y', $this->requestData['to_date'])->format('Y-m-d');

            $reservation = $reservation->whereDate('created_at', '>=', $fromDate)
                ->whereDate('created_at', '<=', $toDate);
        }

        if (!empty($this->partner_id)) {
            $reservation = $reservation->where('partner_id', $this->partner_id);
        }

        if (!empty($this->requestData['facility_id']) && $this->requestData['facility_id'] != '') {
            $reservation = $reservation->where('facility_id', $this->requestData['facility_id']);
        }

        if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12') {
            $admin_partner_id = Auth::user()->created_by;
            if ($admin_partner_id != config('parkengage.PARTNER_SUPERADMIN')) {
                $reservation = $reservation->whereIn('facility_id', $this->facility);
            }
        }
        $reservationData = $reservation->orderBy('id', 'DESC')->get();
        return $reservationData;
    }

    /**
     * Determine email recipients
     */
    private function getToEmails()
    {
        // Use 'emails' if provided, otherwise fallback to 'email'
        $this->toEmails = $this->requestData['emails'] ??  '<EMAIL>';

        // Ensure $toEmails is always an array and filter out invalid entries
        if (!is_array($this->toEmails)) {
            $this->toEmails = [$this->toEmails];
        }

        $this->toEmails = array_filter($this->toEmails, function ($email) {
            return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
        });

        $this->successLog->info('To emails: ' . json_encode($this->toEmails));
    }
}
