<?php

namespace App\Jobs\ManageBookingHistory;

use App\Exceptions\ApiGenericException;
use App\Http\Helpers\MailHelper;
use App\Models\Event;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\Reservation;
use App\Services\LoggerFactory;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;
use App\Jobs\Job;


class SendBookingExcelEmail extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    protected $successLog;
    protected $errorLog;
    protected $requestData;
    protected $partner_id;
    protected $facility_id;
    protected $toEmails;

    public function __construct(Request $request, $partner_id, $facility_id)
    {
        $logFactory = new LoggerFactory();
        $this->successLog = $logFactory->setPath('logs/booking_excel_email')->createLogger('sendmail_success');
        $this->errorLog = $logFactory->setPath('logs/booking_excel_email')->createLogger('sendmail_error');

        $this->requestData = $request->only([
            'from_date', 'to_date', 'booking_type', 'facility_id', 'emails'
        ]);

        $this->partner_id = $partner_id;
        $this->facility_id = $facility_id;
        $this->toEmails = $this->requestData['emails'] ?? [Auth::user()->email];
        if (!is_array($this->toEmails)) {
            $this->toEmails = [$this->toEmails];
        }
    }

    public function handle()
    {
        $this->successLog->info('Starting booking excel email job: ' . json_encode($this->requestData));

        try {
            $from_date = $this->requestData['from_date'] ?? date('Y-m-01');
            $to_date = $this->requestData['to_date'] ?? date('Y-m-t');
            $reservation = $this->fetchReservationData();
            $finalCodes1 = $this->prepareExcelData($reservation);

            if (empty($finalCodes1)) {
                throw new ApiGenericException('Sorry! No Data Found.');
            }

            $excelSheetName = "Booking-Details-Report-" . date("jS M, Y", strtotime($from_date)) . ' - ' . date("jS M, Y", strtotime($to_date));
            $path_to_file = $this->generateExcelFile($excelSheetName, $finalCodes1, $from_date, $to_date);
            $this->successLog->info('File Saved on: ' . $path_to_file);

            $this->sendEmail($excelSheetName, $path_to_file, count($finalCodes1));

            if (file_exists($path_to_file)) {
                unlink($path_to_file);
            }
        } catch (\Throwable $th) {
            $this->errorLog->info('Error: ' . $th->getMessage() . ' File: ' . $th->getFile() . ' Line: ' . $th->getLine());
            Mail::send('email-exception', [
                'partner_key' => "PCI",
                'exception' => "Exception in Booking Excel Email Request: " . $th->getMessage()
            ], function ($message) {
                $message->to('<EMAIL>')
                        ->subject("Email Exception: PCI - Booking Excel Email Request")
                        ->from(env('INFO_MAIL_PARKENGAGE_FROM'), env('MAIL_USERNAME'));
            });
            throw $th;
        }
    }

    private function fetchReservationData()
    {
        $from_date = date('Y-m-d', strtotime($this->requestData['from_date'])) ?? date('Y-m-01');
        $to_date = date('Y-m-d', strtotime($this->requestData['to_date'])) ?? date('Y-m-t');
        $todayQrCode = $this->getTodayQrCode();

        $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'facility', 'transaction']);

        if (
            isset($this->requestData['from_date']) && 
            $this->requestData['from_date'] != '' && 
            isset($this->requestData['to_date']) && 
            $this->requestData['to_date'] != ''
        ) {
            $reservation->whereDate('created_at', '>=', $from_date)
                        ->whereDate('created_at', '<=', $to_date);
        }

        if ($this->facility_id) {
            $reservation->where('facility_id', $this->facility_id);
        }

        $reservation->where('partner_id', $this->partner_id);
        $reservation->orderBy($this->requestData['sort'] ?? 'id', $this->requestData['sortBy'] ?? 'DESC');
        $reservation = $reservation->get();

        return $reservation;
    }

    private function getTodayQrCode()
    {
        $from_date = $this->requestData['from_date'] ?? date('Y-m-01');
        $to_date = $this->requestData['to_date'] ?? date('Y-m-t');
        $todayQrCode = [];
        if ($this->requestData['booking_type'] != '0') {
            $eventQuery = Event::where("is_active", '1');
            if ($this->requestData['booking_type'] == '') {
                $event = $eventQuery->whereDate('start_time', '=', $from_date)->get();
            } else {
                $event = $eventQuery->whereDate('start_time', '>=', $from_date)->whereDate('start_time', '<=', $to_date)->get();
            }
            if ($event->count() > 0) {
                foreach ($event as $v) {
                    $qrCode = MapcoQrcode::where("event_id", $v->id)->get();
                    foreach ($qrCode as $value) {
                        $todayQrCode[] = $value->reservation_id;
                    }
                }
            } elseif ($this->requestData['booking_type'] == '') {
                $todayQrCode[] = 0;
            }
        }
        return $todayQrCode;
    }

    private function prepareExcelData($reservation)
    {
        $finalCodes1 = [];
        $increment1 = 1;
        foreach ($reservation as $val) {
            $paymentStatus = $val->anet_transaction_id ? 'Processed' : ($val->total > 0 ? 'Authorized' : 'NA');
            $finalCodes1[] = [
                'No.' => $increment1++,
                'Facility Name' => $val->facility->full_name,
                'Booking Id' => $val->ticketech_code,
                'License Plate' => $val->license_plate,
                'Creation Date' => date("m/d/Y h:i A", strtotime($val->created_at)),
                'Reservation Start Date' => date("m/d/Y h:i A", strtotime($val->start_timestamp)),
                'Reservation End Date' => date("m/d/Y h:i A", strtotime($val->end_timestamp)),
                'Event Date' => !empty($val->event_id) ? date("m/d/Y", strtotime($val->start_timestamp)) : '-',
                'Booking Amount Due ($)' => $val->is_charged == '0' ? $val->total : '0.00',
                'Booking Amount Paid ($)' => $val->charged_amount,
                'Processing fee ($)' => $val->processing_fee,
                'Refund Amount ($)' => $val->refund_amount,
                'Discount' => $val->discount,
                'Promo Code' => !empty($val->promocode) ? $val->promocode : '-',
                'Payment Status' => $paymentStatus,
                'Reference Number' => isset($val->transaction->ref_id) ? $val->transaction->ref_id : '-',
                'Transaction Status' => $val->cancelled_at ? "Canceled" : '-',
                'Canceled At' => $val->cancelled_at ? date("m/d/Y", strtotime($val->cancelled_at)) : '-',
                'User Email' => isset($val->user->email) ? $val->user->email : '-',
            ];
        }
        return $finalCodes1;
    }

    private function generateExcelFile($excelSheetName, $finalCodes1, $from_date, $to_date)
    {
        Excel::create($excelSheetName, function ($excel) use ($finalCodes1, $excelSheetName, $from_date, $to_date) {
            $excel->setTitle($excelSheetName)
                ->setCreator('BookingDetails')
                ->setCompany('ParkEngage')
                ->setDescription('List Of Booking');

            $excel->sheet('Booking Details', function ($sheet) use ($finalCodes1, $from_date, $to_date) {
                $color = "#2C4293"; // Dark blue for header and totals
                $sheet->setWidth([
                    'A' => 6,    // No.
                    'B' => 45,   // Facility Name
                    'C' => 22,   // Booking Id
                    'D' => 22,   // License Plate
                    'E' => 17.34, // Creation Date
                    'F' => 20,   // Reservation Start Date
                    'G' => 21,   // Reservation End Date
                    'H' => 20,   // Event Date
                    'I' => 23,   // Booking Amount Due ($)
                    'J' => 15,   // Booking Amount Paid ($)
                    'K' => 18,   // Processing fee ($)
                    'L' => 15,   // Refund Amount ($)
                    'M' => 15,   // Discount
                    'N' => 15,   // Promo Code
                    'O' => 13,   // Payment Status
                    'P' => 20,   // Reference Number
                    'Q' => 21,   // Transaction Status
                    'R' => 15,   // Canceled At
                    'S' => 28    // User Email
                ]);

                // Header Row 1: Title and Print Date
                $location = date('d M, Y', strtotime($from_date)) . '-' . date('d M, Y', strtotime($to_date));
                $sheet->mergeCells('A1:E1');
                $sheet->setCellValue('A1', "Booking Summary - " . $location);
                $sheet->getStyle('A1')->getAlignment()->setWrapText(true);
                $printDate = "Print Date - " . date('d M, Y');
                $sheet->mergeCells('F1:S1');
                $sheet->setCellValue('F1', $printDate);
                $sheet->getStyle('F1')->getAlignment()->setWrapText(true);
                $sheet->setHeight(1, 80);
                $sheet->cells('A1:S1', function ($cells) use ($color) {
                    $cells->setAlignment('center');
                    $cells->setValignment('center');
                    $cells->setFont(['bold' => true, 'size' => 22, 'color' => ['argb' => 'FFFFFFFF']]);
                    $cells->setBackground($color);
                });

                // Row 2: Spacer
                $sheet->mergeCells('A2:S2');

                // Row 3: Column Headers
                $sheet->fromArray($finalCodes1, null, 'A3', true); // true to include headers
                $sheet->cells('A3:S3', function ($cells) {
                    $cells->setAlignment('center');
                    $cells->setValignment('center');
                    $cells->setFont(['bold' => true]);
                    $cells->setBackground('#D9E1F2'); // Light blue
                    $cells->setFontColor('#000000');
                });

                // Data Rows
                $i = 4; // Start at row 4 (after headers at row 3)
                $ticketCount = $dueAmount = $chargedAmount = $processingFee = $refundAmount = $discountAmount = 0;
                foreach ($finalCodes1 as $index => $value) {
                    $rowNumber = $i + $index; // Calculate exact row for each data entry
                    $ticketCount++;
                    $dueAmount += (float)($value['Booking Amount Due ($)'] ?? 0);
                    $chargedAmount += (float)($value['Booking Amount Paid ($)'] ?? 0);
                    $processingFee += (float)($value['Processing fee ($)'] ?? 0);
                    $refundAmount += (float)($value['Refund Amount ($)'] ?? 0);
                    $discountAmount += (float)($value['Discount'] ?? 0);
                    $sheet->cells("A$rowNumber:S$rowNumber", function ($cells) {
                        $cells->setAlignment('center');
                        $cells->setValignment('center');
                    });
                }

                // Totals Row
                $k = $i + count($finalCodes1); // Place totals right after last data row
                $sheet->cells("A$k:S$k", function ($cells) use ($color) {
                    $cells->setAlignment('center');
                    $cells->setValignment('center');
                    $cells->setFont(['bold' => true, 'size' => 12, 'color' => ['argb' => 'FFFFFFFF']]);
                    $cells->setBackground($color);
                });
                $sheet->setCellValue("A$k", 'Total');
                $sheet->setCellValue("B$k", '-');
                $sheet->setCellValue("C$k", $ticketCount ?: 0);
                $sheet->setCellValue("D$k", '-');
                $sheet->setCellValue("E$k", '-');
                $sheet->setCellValue("F$k", '-');
                $sheet->setCellValue("G$k", '-');
                $sheet->setCellValue("H$k", '-');
                $sheet->setCellValue("I$k", number_format($dueAmount));
                $sheet->setCellValue("J$k", number_format($chargedAmount));
                $sheet->setCellValue("K$k", number_format($processingFee));
                $sheet->setCellValue("L$k", number_format($refundAmount));
                $sheet->setCellValue("M$k", number_format($discountAmount));
                $sheet->setCellValue("N$k", '-');
                $sheet->setCellValue("O$k", '-');
                $sheet->setCellValue("P$k", '-');
                $sheet->setCellValue("Q$k", '-');
                $sheet->setCellValue("R$k", '-');
                $sheet->setCellValue("S$k", '-');
            });
        })->store('xls');

        return storage_path('exports/' . $excelSheetName . '.xls');
    }

    private function sendEmail($excelSheetName, $path_to_file, $totalTickets)
    {
        $dateRange = date("jS M, Y", strtotime($this->requestData['from_date'])) . ' - ' . date("jS M, Y", strtotime($this->requestData['to_date']));
        $data = [
            'totalTickets' => $totalTickets,
            'report_name' => 'booking_report',
            'facilityWiseTicketsCount' => ['Bookings' => $totalTickets],
            'themeColor' => '#2C4293',
            'mail_body' => "Please find attached the booking report for the period $dateRange.",
            'team_regards' => 'Parkengage',
            'subject' => "Booking Report - $dateRange",
            'filedata' => [
                'type' => 'saved',
                'content' => $path_to_file,
                'filename' => $path_to_file,
                'format' => 'xls'
            ]
        ];

        MailHelper::sendEmail($this->toEmails, 'email.booking_report', $data, $this->partner_id);
        $this->successLog->info("Mail sent successfully to: " . json_encode($this->toEmails));
    }
}