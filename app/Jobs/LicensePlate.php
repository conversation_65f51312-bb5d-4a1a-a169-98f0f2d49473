<?php

namespace App\Jobs;

use App\Jobs\Job;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Artisan;

class LicensePlate extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    private $license_plate;
    
    public function __construct($license_plate)
    {
        $this->license_plate = $license_plate;        
    }

    public function handle()
    {
        return $this->license_plate;
    }
}
