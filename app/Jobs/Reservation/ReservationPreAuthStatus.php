<?php

namespace App\Jobs\Reservation;

use App\Jobs\Job;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Services\LoggerFactory;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class ReservationPreAuthStatus extends Job implements ShouldQueue
{
    use InteractsWithQueue, SerializesModels;

    protected $log;
    protected $requestData;
    protected $carbon;


    // protected MAPCO_BASE_URL;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($requestData)
    {
        // parent::__construct();
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/roc/reservation/pre-auth-status')->createLogger('pre-auth-status');
        $this->carbon = new Carbon();
        $this->requestData =  $requestData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->log->info("Pre Auth Status Start");

        if (!$this->requestData = $this->validateRequest()) {
            $this->log->error("Invalid Request");
            return false;
        }

        // dd($this->requestData);
        $this->sendMail($this->requestData['ticketech_code'], $this->requestData['paymentReleaseRequest'], $this->requestData['paymentReleaseResponse'], $this->requestData['refund_for']);
    }

    private function validateRequest()
    {
        $requestDataRaw = $this->requestData;

        // Try to decode the JSON
        $requestData = json_decode($requestDataRaw, true);

        // Check for JSON decoding errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log->warning("Invalid JSON passed to --requestData: " . json_last_error_msg());
            return false;
        }

        // Optional debug output:
        // dd($requestDataRaw, $requestData);

        // Ensure it's an array
        if (!is_array($requestData) || empty($requestData)) {
            $this->log->warning("Decoded requestData is empty or not an array.");
            return false;
        }

        // Everything is good — return the decoded array
        return $requestData;
    }

    private function sendMail($ticketech_code, $paymentReleaseRequest, $paymentReleaseResponse, $refund_for)
    {
        // dd($ticketech_code, $paymentReleaseRequest, $paymentReleaseResponse);
        $this->log->info("Send mail start");

        $preAuthStatusData = [
            'ticketech_code' => $ticketech_code,
            'paymentReleaseRequest' => $paymentReleaseRequest,
            'paymentReleaseResponse' => $paymentReleaseResponse,
            'refund_for' => $refund_for,
            'timestamp' => $this->carbon->now()->toDateTimeString()
        ];

        $recipients = config("parkengage.notify.reservation");

        // dd($fileName, $filePath, $recipients, count($excelData), config('parkengage.default_sender_email'));

        $subject = "Pre auth release failed";

        try {
            Mail::send('reservation.reservation-pre-auth-status', $preAuthStatusData, function ($message) use ($recipients, $subject) {
                // $message->to("<EMAIL>")
                $message->to($recipients)
                    ->subject($subject)
                    ->from(config('parkengage.default_sender_email'));
            });

            $this->log->info("Notifications sent");
        } catch (\Exception $e) {
            $this->log->error("Failed to send email with Excel: {$e->getMessage()}");
        }
    }
    
}
