<?php

namespace App\CamraIntegrations\LPR;

use Illuminate\Support\Facades\Validator;

class TattileParser implements LprParserInterface
{
    public function parse(array $payload): array
    {
        Validator::make($payload, [
            'results.plate' => 'required|string',
            'results.timestamp' => 'required|date',
        ])->validate();

        return [
            'plate_number' => $payload['results']['plate'],
            'timestamp' => $payload['results']['timestamp'],
            'image_url' => $payload['results']['image_url'] ?? null,
        ];
    }
}
