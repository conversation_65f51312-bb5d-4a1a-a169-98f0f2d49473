<?php

namespace App\CamraIntegrations\LPR;

use Illuminate\Support\Str;
use InvalidArgumentException;

class LprParserFactory
{
    public static function make(string $vendor): LprParserInterface
    {
        $class = 'App\\Parsers\\LPR\\' . Str::studly($vendor) . 'Parser';

        if (!class_exists($class)) {
            throw new InvalidArgumentException("No parser found for vendor: $vendor");
        }

        return app()->make($class);
    }
}
