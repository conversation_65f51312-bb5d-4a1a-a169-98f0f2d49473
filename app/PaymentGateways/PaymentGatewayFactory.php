<?php

namespace App\PaymentGateways;

use App\Classes\FiservPaymentGateway;
use App\PaymentGateways\Contracts\PaymentGatewayInterface;
use App\PaymentGateways\Classes\DatacapPaymentGateway;
use App\PaymentGateways\Classes\HeartlandPaymentGateway;
// use App\PaymentGateways\Stripe\StripeGateway;
// use App\PaymentGateways\Razorpay\RazorpayGateway;
use App\Services\LoggerFactory;

class PaymentGatewayFactory
{
    /**
     * Create an instance of the specified payment gateway.
     *
     * @param string $gateway
     * @param array $config
     * @return PaymentGatewayInterface
     * @throws \Exception
     */
    public static function create($gateway, $config = [])
    {
        switch (strtolower($gateway)) {
            case '2':       # DataCap Payment Gatway
                return new DatacapPaymentGateway($config);
            case '3':       # Fiserv Payment Gatways
                return new FiservPaymentGateway($config);
            case '4':       # Heart Land Payment Gatways
                return new HeartlandPaymentGateway($config);
                // case 'razorpay':
                //     return new RazorpayGateway($config);
            default:
                throw new \Exception("Payment gateway [$gateway] is not supported.");
        }
    }
}
