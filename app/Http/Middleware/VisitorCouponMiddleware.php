<?php

namespace App\Http\Middleware;

use Closure;

use App\Models\Rate;
use App\Models\Visitor\Visitor;

class VisitorCouponMiddleware
{
    /**
     * Handles tracking visitor interactions with the coupon system
     * Attach to routes that handle coupon actions and pass in a param for the event type like so:
     * 'middleware' => 'coupon-logging:email' where 'email' is one of:
     * email, view, print, text
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle($request, Closure $next, $eventType = null)
    {
        $validTypes = ['email', 'print', 'view'];

        if (!$eventType || !in_array($eventType, $validTypes)) {
            return $next($request);
        }

        $visitorCode = $request->header(config('headers.visitor'));
        $couponId = isset($request->coupon) ? $request->coupon->id : $request->id;

        $visitor = Visitor::where('visitor_code', $visitorCode)->first();

        if (!$visitor || !$couponId) {
            // TODO: Create the visitor ID here?
            return $next($request);
        }
        // Construct our visitor tracking class
        $eventClass = 'App\Models\Visitor\VisitorCoupon' . ucfirst($eventType);

        // Save tracking event
        $event = new $eventClass();
        $event->visitor_code_id = $visitor->id;
        $event->coupon_id = $couponId;

        if ($eventType === 'email' && $request->email) {
            $event->to = $request->email;
        }

        $event->coupon();
        $event->save();

        return $next($request);
    }
}
