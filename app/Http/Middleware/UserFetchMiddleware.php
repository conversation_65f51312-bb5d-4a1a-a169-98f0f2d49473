<?php

namespace App\Http\Middleware;

use App\Exceptions\ApiGenericException;
use Closure;
use Response;

class UserFetchMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (\Authorizer::getResourceOwnerType() === 'user') {
            $id = \Authorizer::getResourceOwnerId();
            $user = \Auth::loginUsingId($id);
            // change for delete feature
            if (!\Auth::loginUsingId($id)) {
                throw new ApiGenericException("Unauthorized", 401);
            }
            if ($user) {
                if ($request->header('X-UserType') != '') {
                    if ($user->user_type != $request->header('X-UserType')) {
                        return response()->json([
                            "user_type" => $user->user_type
                        ], 402);
                    }
                }
            }
        }
        return $next($request);
    }
}
