<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

class UserMiddleware
{
    public $user;

    public function __construct(Request $request)
    {
        $this->user =  User::find($request->id) ?: new User;
        app()->instance(User::class, $this->user);
    }
    public function handle($request, Closure $next)
    {
        $keys = Schema::getColumnListing($this->user->getTable());
        $request_data = $request->only($keys);
        $this->user->fill($request_data);

        return $next($request);
    }
}
