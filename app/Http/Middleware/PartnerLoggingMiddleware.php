<?php

namespace App\Http\Middleware;

use Closure;
use Faker\Provider\Uuid;
use App\Classes\ChannelLog as Log;

class PartnerLoggingMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $UID = Uuid::uuid();
        $url = $request->url();
        \Log::useDailyFiles(storage_path('/logs/partner/requests.log'), 10);
        Log::write('request', "Id $UID - HEADER-- ".json_encode($request->headers->all())."--URL- $url" .json_encode($request->all()));

        $response = $next($request);
        $responseMsg = "";
        \Log::useDailyFiles(storage_path('/logs/partner/requests.log'), 10);
        try{
         if (isset($response->exception) && $response->exception) {
            if (get_parent_class($response->exception) == 'App\Exceptions\ApiException') {
                $responseMsg =$response->exception->getMessage()."--Code--".$response->exception->getStatusCode();
            }
            if (get_parent_class($response->exception) == 'League\OAuth2\Server\Exception\OAuthException') {
                $responseMsg = "Not Authorized-- code 401";
            }
        }else{
           
             $responseMsg = $response->status();
        }
        }catch(\Exception $e)
        {
            return $response;
        }
        Log::write('response', " UID $UID -Url- $url -Status code ".  $responseMsg);

        return $response;
    }
}
