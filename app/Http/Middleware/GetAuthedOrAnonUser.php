<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

use LucaDegas<PERSON><PERSON>\OAuth2Server\Authorizer;

use Illuminate\Http\Request;

use App\Models\User;

/**
 * Some routes may be called by authed or unauthed users. If the user is authed, inject
 * that user into the service container. Othewrwise, get an anonymous user based on the request email field
 */
class GetAuthedOrAnonUser
{

    protected $user;

    public function __construct(Request $request, Authorizer $auth)
    {
        $user = null;

        if (!$request->header('Authorization') && !$request->email) {
            return;
        }

        // This route is not authed so we need to manually get the user
        $authHeader = $request->header('Authorization');
        if ($authHeader && $authHeader !== 'Bearer null') {
            $auth->setRequest($request);
            $auth->validateAccessToken(true);
            if (\Authorizer::getResourceOwnerType() === 'user') {
                $id = \Authorizer::getResourceOwnerId();
                Auth::loginUsingId($id);
                $user = Auth::user();
            }
        }

        if (!$user || $user->isAdmin || $user->isMpSales) {
            $user = User::getAnonUser($request->email);
        }

        app()->instance(User::class, $user);
    }

    public function handle($request, Closure $next)
    {
        return $next($request);
    }
}
