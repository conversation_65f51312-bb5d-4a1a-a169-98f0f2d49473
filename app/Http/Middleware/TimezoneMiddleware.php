<?php

namespace App\Http\Middleware;

use Closure;
use DateTimeZone;
use Cookie;
use Auth;
use App\Models\OauthClient;
use App\Exceptions\UnauthorizedException;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\Facility;

class TimezoneMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        //set timezone for frontend user APIs
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new UnauthorizedException('Invalid partner.');
            }

            // Vijay : 27-02-2025
            // Store partnerId in the request object so it can be accessed in controllers
            $request->attributes->set('xpartnerId', $secret->partner_id);
            // !!!! Close Here.

            $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            if (($facility) && ($facility->timezone != '')) {
                $this->setTimezone($partnerTimezone->timezone);
                return $next($request);
            } else {
                if ($partnerTimezone) {
                    if ($partnerTimezone->timezone != '') {
                        $this->setTimezone($partnerTimezone->timezone);
                        return $next($request);
                    }
                }
            }
            /*
			if($partnerTimezone){
                if($partnerTimezone->timezone != ''){
                    $this->setTimezone($partnerTimezone->timezone);
                    return $next($request);
                }
            }
			*/
        } else {
            //set timezone for partner login user
            if (Auth::check()) {

                // Vijay : 27-02-2025
                // Store partnerId in the request object so it can be accessed in controllers
                $request->attributes->set('xpartnerId', Auth::user()->created_by);
                // !!!! Close Here.

                if (Auth::user()->user_type == '3') {
                    $partnerTimezone = UserPaymentGatewayDetail::where('user_id', Auth::user()->id)->first();
                    if ($partnerTimezone) {
                        if ($partnerTimezone->timezone != '') {
                            $this->setTimezone($partnerTimezone->timezone);
                            return $next($request);
                        }
                    } else {
                    }
                }
            }
        }
        return $next($request);
    }

    public function setTimezone($timezone)
    {
        if (empty($timezone)) {
            $timezone = config('app.timezone');
        }
        date_default_timezone_set($timezone);
    }
}
