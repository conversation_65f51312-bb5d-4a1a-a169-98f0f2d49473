<?php

namespace App\Http\Middleware;

use App\Services\LoggerFactory;

class TrackForeignRequest
{

    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/requests')->createLogger('foreign_requests');
    }

    public function handle($request, \Closure  $next)
    {
        if (!config('tracking.foreign_requests')) {
            return $next($request);
        }

        if (trim($request->headers->get('origin'), '/') != trim(config('app.web_url'), '/')) {
            $this->log->info(
                'app.requests', [
                'method' => $request->method(),
                'ip' => $request->ip(),
                'remote_address' => $request->server('REMOTE_ADDR'),
                'route' => $request->url(),
                'origin' => $request->headers->get('origin'),
                'headers' => $request->headers->all(),
                'request' => $request->all(),
                'request-json' => $request->getContent()
                ]
            );
        };

        return $next($request);
    }
}
