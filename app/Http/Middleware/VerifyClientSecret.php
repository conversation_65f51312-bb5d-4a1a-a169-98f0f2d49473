<?php

namespace App\Http\Middleware;

use App\Exceptions\ApiGenericException;
use Closure;
use App\Models\OauthClient;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Exceptions\InvalidClientSecretException;
use App\Exceptions\NotFoundException;
use App\Exceptions\UnauthorizedException;
use Illuminate\Support\Facades\Log;

class VerifyClientSecret
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Step 1: Check if header exists
        if (!$request->hasHeader('X-ClientSecret')) {
            throw new UnauthorizedException('Client secret header is missing');
        }

        $clientSecret = $request->header('X-ClientSecret');

        if (empty($clientSecret) || strlen($clientSecret) < 16) {
            throw new UnauthorizedException('Invalid client secret format');
        }

        $client = OauthClient::where('secret', $clientSecret)->first();

        if (!$client) {
            throw new UnauthorizedException('Invalid client credentials');
        }

        $request->merge(['partner_id' => $client->partner_id , 'client_id' => $client->id , 'client_secret' => $client->secret]);

        Log::info('Client secret validated', [
            'client_id' => $client->id,
            'partner_id' => $client->partner_id,
            'ip' => $request->ip()
        ]);

        return $next($request);
    }
}