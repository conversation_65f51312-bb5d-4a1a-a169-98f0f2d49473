<?php

namespace App\Http\Middleware;

use App\Models\Partner;
use App\Models\Rate;
use Closure;
use Illuminate\Http\Request;
use Schema;

class RateMiddleware
{
    public $rate;

    public function __construct(Request $request)
    {
        $this->rate =  Rate::with('rateType', 'facility', 'partner')->find($request->id) ?: new Rate;
        app()->instance(Rate::class, $this->rate);
    }

    public function handle($request, Closure $next)
    {
        $keys = Schema::getColumnListing($this->rate->getTable());
        $request_data = $request->only($keys);
        $this->rate->fill($request_data);
        return $next($request);
    }
}
