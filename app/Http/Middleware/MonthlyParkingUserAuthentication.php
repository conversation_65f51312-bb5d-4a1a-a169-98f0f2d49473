<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

use App\Models\MonthlyParkingUser;
use App\Models\Role;

use App\Exceptions\UserNotAuthorized;
use App\Exceptions\NotFoundException;

class MonthlyParkingUserAuthentication
{
    /**
     * Confirm that the currently authed user owns the monthly
     * parking account that they are trying to access
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $account = $request->route()->parameter('account'); // Note, this uses laravel implicit model binding too

        if (!$account) {
            return $next($request);
        }

        if (is_string($account)) {
            $account = self::getMonthlyParkingUser($account);
        }

        self::confirmAuthedUserOwnsAccount($account);

        return $next($request, $account);
    }

    /**
     * Get the account with the the given account number, or throw an exception
     *
     * @param  $accountId integer
     * @return App\Models\MonthlyParkingUser
     */
    protected static function getMonthlyParkingUser($accountId)
    {
        $account = MonthlyParkingUser::where('account_number', $accountId)->with('user')->first();

        if (!$account) {
            throw new NotFoundException('No account with that account number found.');
        }

        return $account;
    }

    /**
     * Confirm that the authed user owns the monthly parking account we are trying to edit
     *
     * @param  MonthlyParkingUser $account [description]
     * @return [type]                      [description]
     */
    protected static function confirmAuthedUserOwnsAccount(MonthlyParkingUser $account)
    {
        $user = Auth::user();

        if ($user->isAdmin || $user->hasRole(Role::CUSTOMER_SERVICE) || $user->hasRole(Role::ACCOUNTS_RECEIVABLE) || $user->hasRole(Role::PARTNER) || $user->hasRole(Role::SUBPARTNER) || $user->hasRole(Role::REGIONAL_MANAGER)) {
            return true;
        }

        if ($account->user->id !== $user->id) {
            throw new UserNotAuthorized('User does not own this monthly parking account.');
        }

        return true;
    }
}
