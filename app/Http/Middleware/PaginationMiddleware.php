<?php

namespace App\Http\Middleware;

use Illuminate\Pagination\Paginator;

use Closure;

class PaginationMiddleware
{
    /**
     * Allows us to use pagination with URLs like /resource/page/2, instead of /resource?page=2
     * To use, you need to register a route like /resource/page/{page} in routes.php and send it to a method that uses
     * the laravel paginate() method on a query result, eg. Resource::where('x', 'y')->paginate($itemsPerPage);
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $path = explode('/', $request->path());
        $index = array_search('page', $path);

        if ($index === false || count($path) <= $index + 1) {
            return $next($request);
        }

        Paginator::currentPageResolver(
            function () use ($path, $index) {
                return $path[$index + 1];
            }
        );

        return $next($request);
    }
}
