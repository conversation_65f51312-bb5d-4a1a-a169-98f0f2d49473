<?php

namespace App\Http\Middleware;

use Closure;

use App\Models\Visitor\Visitor;
use App\Models\Visitor\VisitorFacilityMessage;

// Terminable middleware to track facility messages
class VisitorFacilityMiddleware
{

    /**
     * Type of message to store
     *
     * @var [type]
     */
    protected $type;

    /**
     * Middleware params are passed only to the handle function, so we need to
     * store the type here
     *
     * @param  [type]  $request [description]
     * @param  Closure $next    [description]
     * @param  [type]  $type    [description]
     * @return [type]           [description]
     */
    public function handle($request, Closure $next, $type)
    {
        $this->type = $type;
        return $next($request);
    }

    /**
     * Track Facility messaging (text and email) and log to database
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Http\Response $response
     * @return mixed
     */
    public function terminate($request, $response)
    {
        $visitorCode = $request->header(config('headers.visitor'));

        if (!$visitorCode) {
            return;
        }

        $visitor = Visitor::where('visitor_code', $visitorCode)->first();

        if (!$visitor) {
            return;
        }

        $message = VisitorFacilityMessage::create(
            [
            'visitor_code_id' => $visitor->id,
            'facility_id' => $request->id,
            'to' => $request->to,
            'type' => $this->type
            ]
        );
    }
}
