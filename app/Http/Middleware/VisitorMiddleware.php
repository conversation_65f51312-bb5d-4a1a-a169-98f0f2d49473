<?php

namespace App\Http\Middleware;

use Closure;
use Auth;

use App\Models\Visitor\Visitor;

class VisitorMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $visitor_code = $request->header(config('headers.visitor'));
        $masquerade_token = $request->header(config('headers.masquerade'));
        $user = Auth::user();

        // Don't continue if we are masquerading, dont' have a visitor code or an authed user
        if (!$visitor_code || !$user || $masquerade_token) {
            return $next($request);
        }

        // Make sure the right user is set on our visitor code
        $visitor = Visitor::where('visitor_code', $visitor_code)->first();

        if (!$visitor) {
            // Create and save our visitor if this tracking code can't be found
            $visitor = Visitor::create(
                [
                'visitor_code' => $visitor_code,
                'ip' => $request->ip()
                ]
            );
        }

        // Continue if correct user already set
        if ($visitor->user_id === $user->id) {
            return $next($request);
        }

        // Save the authed user to this visitor code
        $visitor->user_id = $user->id;
        $visitor->save();

        return $next($request);
    }
}
