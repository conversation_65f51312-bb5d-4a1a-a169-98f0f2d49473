<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;

class Preflight
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */

    public function handle($request, Closure $next)
    {
        // temporary workaround for pre-flight cors requests
        $headers = [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods'=> 'POST, GET, OPTIONS, PUT, DELETE',
            'Access-Control-Allow-Headers'=> 'Content-Type, X-Auth-Token, Origin, Authorization, X-Big-Apple, X-Masquerade, X-UserType, X-ClientSecret, X-FacilityID, X-Masquerade-Parent-Id'
        ];
        foreach ($headers as $key => $value) {
            @header("$key: $value");
        }

        return  $next($request);
    }
}
