<?php

namespace App\Http\Middleware;

use Closure;
use Hash;

class LegacyAuthenticationMiddleware
{
    /**
     * Intercepts user login requests and updates legacy user login credentials to the new password schema.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $username = $request->input('username');
        $query = \DB::table('users')->where('email', $username);
        $user = $query->first();
        if (isset($user)) {
            $isLegacy = $user->legacy_authentication;
            if ($isLegacy) {
                $password = $request->input('password');
                $legacyPassword = $user->password;
                if (md5($password) == $legacyPassword) {
                    $modernValue = Hash::make($request->input('password'));
                    $query->update(['password' => $modernValue, 'legacy_authentication' => 0]);
                }
            }
        }
        return $next($request);
    }
}
