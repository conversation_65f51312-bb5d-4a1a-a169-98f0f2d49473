<?php

namespace App\Http\Middleware;

use Auth;
use Closure;
use App\Models\User;
use App\Models\Permission;
use App\Models\ParkEngage\SubPartnerPermission;
use Illuminate\Support\Facades\Route;
use App\Exceptions\UnauthorizedException;

class CheckPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = User::with('membershipPlans')->where('id', Auth::user()->id)->first();
        if($user->user_type == '1' || $user->user_type == '2'){
            //check for superadmin and admin
            return $next($request);
        }elseif($user->user_type == '3'){
            //check for partner
            $ids = [];
            foreach ($user->membershipPlans as $key => $value) {
                if($value->pivot->is_active == '1'){
                    $ids[] = $value->id;    
                }
                
            }
            if(count($ids) == 0){
                throw new UnauthorizedException('Your membership plan has been expired. Please upgrade your plan.');
            }
            
            $permission = Permission::whereIn('membership_plan_id', $ids)->where('name', \Route::getCurrentRoute()->getPath())->where('method', \Request::getMethod())->count(); 
            if($permission > 0){
                return $next($request);    
            }else{
                throw new UnauthorizedException('You do not have permission for access.');
            }            
        }elseif($user->user_type == '4'){
            //check for partner user
            $ids = $user->membershipPlans->pluck('id')->toArray();
            $permission = SubPartnerPermission::whereIn('membership_plan_id', $ids)->where('name', \Route::getCurrentRoute()->getPath())->where('method', \Request::getMethod())->count(); 
            //dd($permission,$ids,\Route::getCurrentRoute()->getPath(), \Request::getMethod());
            if($permission > 0){
                return $next($request);    
            }else{
                throw new UnauthorizedException('You do not have permission for access.');
            }            
        }
        
    }
}
