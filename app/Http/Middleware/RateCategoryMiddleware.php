<?php

namespace App\Http\Middleware;

use App\Models\RateCategory;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use Webpatser\Uuid\Uuid;

class RateCategoryMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public $rateCategory;

    public function __construct(Request $request)
    {
        $this->rateCategory = RateCategory::with('rates')->find($request->id) ?: new RateCategory();

        app()->instance(RateCategory::class, $this->rateCategory);
    }
    public function handle($request, Closure $next)
    {
        $keys = Schema::getColumnListing($this->rateCategory->getTable());
        $request_data = $request->only($keys);
        $this->rateCategory->fill($request_data);

        return $next($request);
    }
}
