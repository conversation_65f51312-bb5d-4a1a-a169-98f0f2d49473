<?php

// MONTHLY PARKING ROUTES FILE

// Non-api endpoints for monthly parking users (downloads PDFs, JPGs)
Route::group(
    [
    'middleware' => ['oauth', 'fetch-user', 'monthlyparkinguserauth'],
    'prefix' => 'monthly-parking/account/{account}'
    ], function () {
        Route::get('current-invoice/pdf', 'UserInvoiceController@getCurrentInvoicePdf');
        Route::get('past-invoice/pdf/{date}', 'UserInvoiceController@getPastInvoicePdf');
        Route::get('current-invoice/jpg', 'UserInvoiceController@getCurrentInvoiceJpg');
        Route::get('past-invoice/jpg/{date}', 'UserInvoiceController@getPastInvoiceJpg');
    }
);

Route::group(
    [
    'middleware' => ['apiresponse', 'oauth', 'fetch-user', 'visitor', 'monthlyparkinguserauth'],
    'prefix' => 'monthly-parking/account/{account}'
    ], function () {
        Route::delete('', 'MonthlyParkingUserController@removeAccount');
        Route::get('balance', 'MonthlyParkingUserController@getAccountBalances');
        Route::get('payment', 'MonthlyParkingUserPaymentController@getPayments'); // Get payment history
        Route::get('payment/{payment}', 'MonthlyParkingUserPaymentController@getSinglePayment'); // Get payment history
        Route::post('payment', 'MonthlyParkingUserPaymentController@makePayment'); // Make a single-time payment to the account
        Route::get('billing', 'MonthlyParkingUserController@getBillingAddress');
        Route::put('billing', 'MonthlyParkingUserController@updateBillingInformation'); // update billing address across all payment methods
        Route::get('autopay', 'AutopayMethodsController@getAutopaySettings'); // update autopay preferences
        Route::put('autopay', 'AutopayMethodsController@updateAutopay'); // update autopay preferences
        Route::post('cancel', 'MonthlyParkingUserController@cancelAccount'); // Send email to icon to cancel account
        Route::get('current-invoice', 'UserInvoiceController@getCurrentInvoice'); // Get current user invoices
        Route::get('past-invoice', 'UserInvoiceController@getPastInvoice'); // Get current user past invoices

        // Monthly parking payment methods
        Route::get('payment-method', 'MonthlyParkingUserController@getPaymentMethods');
        Route::post('payment-method', 'MonthlyParkingUserPaymentController@addPaymentMethod');
        Route::put('payment-method/{paymentProfileId}', 'MonthlyParkingUserController@updatePaymentMethod');
        Route::delete('payment-method/{paymentProfileId}', 'MonthlyParkingUserPaymentController@deletePaymentMethod');

        Route::get('current-past-invoice', 'UserInvoiceController@getCurrentPastInvoice'); // Get current user invoices
    }
);
Route::get('get-tenants-slots/{account}', 'CancellationRequestController@getAccountTenantsSlots')->middleware('apiresponse');

//cancellation request for account tenants
Route::group(
    [
    'middleware' => ['apiresponse', 'oauth', 'fetch-user'],
    ], function () {
        Route::post('cancel-tenants', 'CancellationRequestController@postCancelRequest');

});
