<?php

// Exisiting monthly user enrollment
use Illuminate\Support\Facades\Artisan;

if (version_compare(PHP_VERSION, '7.2.0', '>=')) {
    error_reporting(E_ALL ^ E_NOTICE ^ E_WARNING);
}

Route::get('/enroll-monthly-user/{account}', function ($account) {
    if ($account) {
        Artisan::call('cron:enroll-monthly-parking-user', ['--account' => $account]);
    } else {
        echo "No account provided";
    }
});

Route::get('monthly-campaign-neighborhood', 'NeighborhoodController@getMonthlyCampaignNeighborhood');

Route::get('notifications', 'NotificationController@index');
Route::get('mob-notifications', 'NotificationController@mobNotifications');
Route::get('web-notifications', 'NotificationController@webNotifications');

Route::get('validate-promo-user/{actvationCode}', 'PromotionController@validateUserPromo');
Route::get('download-file', 'PublicEventhController@downloadfile');
Route::get('save-events-to-db', 'PublicEventhController@saveEventToDataBase');

// PUBLIC ROUTES FILE
Route::get('release/full/{site}', 'ReleaseController@full');

Route::get('release/pending/{site?}', 'ReleaseController@pending');
Route::get('release/view/{site}/{number}', 'ReleaseController@release');

Route::get('photo/{photo}', 'PhotoController@show');
Route::get('download-pkpass/{file}', 'PhotoController@downloadPkPass');

// Display coupon with details on get coupon


// Get reservation stub HTML/images
// Note that this is a member-only route that must be declared here, because it conflicts with a
// route declaration for reservation/{reservationCode}/{ticketechCode}` below
Route::get(
    'reservation/{reservation}/jpg',
    ['uses' => 'ReservationController@getReservationStubJpgById', 'middleware' => ['oauth', 'fetch-user']]
);
Route::get('reservation/{reservationCode}/{ticketechCode}/jpg', 'ReservationController@getReservationStubJpgByCode');

Route::get(
    'reservation/{reservation}/jpg/barcode',
    ['uses' => 'ReservationController@getReservationBarcodeJpgById', 'middleware' => ['oauth', 'fetch-user']]
);
Route::get(
    'reservation/{reservationCode}/{ticketechCode}/jpg/barcode',
    'ReservationController@getReservationBarcodeJpgByCode'
);

// Test route for seeing how your view looks in pdf form.
Route::get('pdf-test', ['uses' => 'PdfController@index']);

// Route to download the CSV for facility or all rates
Route::get('rates/csv/facility/{facility}', ['uses' => 'RateController@downloadAllFacilityRates']);
Route::get('rates/csv', ['uses' => 'RateController@downloadAllRates']);

// Displays coupon for printing
Route::get(
    'coupon/jpg/{coupon}/{visitor_code?}',
    ['middleware' => 'coupon-logging:view', 'uses' => 'CouponController@showJpg']
);
Route::get('coupon/jpg/{coupon}/barcode', 'CouponController@showBarcodeJpg');

Route::get('globalcoupon/{slug}/jpg', 'GlobalCouponController@showJpg');

// Application documentation
Route::get('docs', 'DocumentationController@index');

Route::group(
    ['middleware' => ['apiresponse', 'timezone']],
    function () {
        Route::get('get-coupon/details/{coupon}', ['middleware' => 'coupon-logging:view', 'uses' => 'CouponController@showCouponDetails']);
        Route::get('evcharging', 'FacilityController@evcharging');
        //for  public events
        Route::post('search-public-events', 'PublicEventhController@searchPublicEvents');

        // Create company affilate
        Route::post('create-company-affilate', 'CompanyAffilateController@createCompanyUser');
        Route::get('get-coupon-mobile-code', 'VisitorController@getVisitorCouponMobileCode');

        // Login
        Route::post('oauth/access_token', ['middleware' => 'legacy-auth', 'uses' => 'AuthController@grantOauthToken']);

        Route::get('redirect', 'RedirectController@getByPath');

        // TODO: The below route does not exist
        Route::get('hoursofoperation/facility', ['uses' => 'HoursOfOperationController@index']);
        Route::get('hoursofoperation/facility/{hooId}', ['uses' => 'HoursOfOperationController@show']);

        // Facility
        Route::get('facility_llc', 'FacilityController@facilityLlc');
        Route::get('facility/{facility}', 'FacilityController@show');
        Route::get('facility-realtimeconf/{facility}', 'FacilityController@getRealTimeConfiguration');
        Route::get('facility/{facility}/rates', 'FacilityController@getRates');
        Route::get('facility/slug/{slug}', 'FacilityController@getFacilityBySlug');
        Route::post('facility/{facility}/active-events', 'FacilityController@getActiveFacilityEvents');
        Route::get('facility/{facility}/future-events', 'FacilityController@getFutureFacilityEvents');
        Route::post('facility/{id}/text', ['uses' => 'FacilityController@sendFacilityTextMessage', 'middleware' => 'facility-message-logging:text']);

        //Send url in mobile
        Route::post('facility/text', 'FacilityController@sendUrlTextMessage');

        Route::post('facility/{id}/email', ['uses' => 'FacilityController@sendFacilityEmail', 'middleware' => 'facility-message-logging:email']);
        Route::get('facility/{facility}/hours', 'FacilityController@getHoursOfOperation');
        Route::get('facility/{facility}/hours-formatted', 'FacilityController@getHoursOfOperationFormatted');
        Route::get('facilities/neighborhood', 'FacilityController@indexByNeighborhoods');

        Route::get('facility-types', 'FacilityTypeController@get');

        // Rate categories and types
        Route::resource('ratetype/{facility_id?}', 'RateTypeController', ['only' => ['index']]);
        Route::get('ratecategory', ['uses' => 'RateCategoryController@index', 'middleware' => 'ratecategory']);
        Route::get('ratecategory/{id}', ['uses' => 'RateCategoryController@show', 'middleware' => 'ratecategory']);

        Route::get('partner-ratecategory/{facility_id?}', ['uses' => 'RateCategoryController@partnerRateCategory', 'middleware' => 'ratecategory']);
        Route::get('ratecategorybyid/{id}', ['uses' => 'RateCategoryController@rateCategoryDetails', 'middleware' => 'ratecategory']);

        // Rate engine requests
        Route::get('rate', 'RateController@index');
        Route::get('ratetypes', ['uses' => 'RateController@indexRateTypes', 'middleware' => 'rate']);
        Route::get('rate/{rate}', 'RateController@show');
        Route::get('rate/facility/{facility}', ['uses' => 'RateController@getFacilityRate']);
        Route::get('rate-facility-on-side-map', ['uses' => 'RateController@getFacilityRateOnSideMap']);
        Route::get('rate/facility-on-marker/{facility}', ['uses' => 'RateController@getFacilityRateOnMapMarker']);
        Route::get('rates/facility/category/{facility}', ['uses' => 'RateController@getFacilityRatesByCategory']);
        Route::get('rates/facility/type/{facilityId}', ['uses' => 'RateController@getFacilityRatesByType']);
        Route::get('rates_mob/facility/type/{facilityId}', ['uses' => 'RateController@getFacilityRatesByTypeMobile']);

        // Get Rate for Reservation : Lokesh
        Route::get('rate/get-reservation-rate/{facility}', ['uses' => 'RateController@getRateForReservation']);
        // End
        // Get coupon raw information (no html/jpg rendering)
        Route::get('coupon/{coupon}', 'CouponController@show');

        // Email signup
        Route::post('email-signup', 'EmailSignupController@store');

        // Coupon promo and offer getters
        Route::get('couponpromo', 'CouponPromoController@index');
        Route::get('couponpromo/{promo}', 'CouponPromoController@show')->where(['promo' => '[0-9]+']);
        Route::get('couponoffer', 'CouponOfferController@index');
        Route::get('couponoffer/{offer}', 'CouponOfferController@show')->where(['offer' => '[0-9]+']);

        // Monthly Parking Promos
        Route::get('monthlyparkingpromo', 'MonthlyParkingPromoController@index');
        Route::get('monthlyparkingpromo/{promo}', 'MonthlyParkingPromoController@show')->where(['promo' => '[0-9]+']);
        // Attractions
        Route::resource('attractiontype', 'AttractionTypeController', ['only' => ['index']]);
        Route::get('attraction/all', 'AttractionController@fetchAll');
        Route::get('attraction/{attraction}', 'AttractionController@show')->where(['attraction' => '[0-9]+']);
        Route::get('attraction/{type}/{slug}', 'AttractionController@getAttractionBySlug');

        Route::get('partner/{slug}', 'PartnerController@partnerBySlug');
        Route::get('event/{slug}', 'EventController@eventBySlug');
        
        Route::post('search/facilities-by-location', ['uses' => 'SearchController@getFacilitiesByLocation', 'middleware' => ['oauth', 'fetch-user']]);
        Route::get('search/bounding-boxes', ['uses' => 'SearchController@getAllBoundingBoxes']);
        Route::get('search/allfacilites/page/{page}', ['uses' => 'SearchController@getAllFacilities', 'middleware' => 'pagination']);
        Route::post('search/facilities', ['uses' => 'SearchController@facilities']);
        Route::post('search/rates', ['uses' => 'SearchController@rates']);
        Route::post('search/facilities-rates', ['uses' => 'SearchController@facilitiesRates']);
        Route::post('search/partner-rates', ['uses' => 'SearchController@partnerRates']);
        Route::post('search/event-rates', ['uses' => 'SearchController@eventRates']);
        Route::get('neighborhood', ['uses' => 'NeighborhoodController@index']);
        Route::get('neighborhood/facilities', 'NeighborhoodController@facilitiesByNeighborhood');

        Route::get('cms', ['uses' => 'CMSController@index']);
        Route::get('cms/{page}', ['uses' => 'CMSController@show'])->where(['page' => '[0-9]+']);
        Route::get('cms/slug/{slug}', ['uses' => 'CMSController@showBySlug']);

        Route::post('correspondence', 'CorrespondenceController@store');

        // Monthly parking requests
        Route::post('monthly-request/{facility}', ['uses' => 'MonthlyRequestController@create', 'middleware' => 'auth-or-anon']);

        // Basic monthly parking requests
        Route::post('basicmonthlyrequest', 'BasicMonthlyRequestController@store');

        Route::post('simple-monthly-request', 'SimpleMonthlyRequestController@store');

        Route::get(
            'elimiwait-toggle',
            function () {
                return ['toggle' => config('elimiwait.iframe-toggle')];
            }
        );

        // Coupons
        Route::get('coupon/html/{id}', 'CouponController@showHtml'); // Returns straight HTML of coupon
        Route::post('coupon/email', ['uses' => 'CouponController@sendEmail', 'middleware' => 'coupon-logging:email']);
        Route::post('coupon/landing/email', ['uses' => 'CouponController@sendLandingPageEmail', 'middleware' => 'coupon-logging:email']);
        Route::post('coupon/global/email', ['uses' => 'GlobalCouponController@sendGlobalPageEmail', 'middleware' => 'coupon-logging:email']);
        Route::post('coupon/global-image/email', ['uses' => 'GlobalCouponController@sendGlobalImagePageEmail']);

        Route::post('coupon/new_design/email', ['uses' => 'CouponController@sendEmailNewDesign', 'middleware' => 'coupon-logging:email']);

        Route::post('coupon/landing/new_design/email', ['uses' => 'CouponController@sendLandingPageEmailNewDesign', 'middleware' => 'coupon-logging:email']);

        // Anonymous user reservations
        Route::post('reservation', 'ReservationController@makeAnonReservation');
        Route::post('reservation-apple-pay', 'ReservationController@makeAnonReservationApplePay');
        Route::get('reservation/{reservationCode}/{ticketechCode}', 'ReservationController@getByCode')->where(['reservationCode' => '^(?!.*page).*$']);
        Route::post('reservation/cancel/{reservationCode}/{ticketechCode}', 'ReservationController@cancelReservationViaCode');

        Route::get('user/maintenance', 'UserController@accountMaintenance');
        Route::post('user', 'UserController@create');
        Route::post('user/{userId}/email/{confirmationCode}', 'UserController@confirmEmail');
        Route::post('user/{userId}/resend-email', 'UserController@resendConfirmationEmail');

        Route::post('user/social', 'UserController@createSocialUser');
        Route::post('check-social-user-email', 'UserController@checkSocialUserEmail');


        // Visitor tracking URLs
        Route::post('visitor', 'VisitorController@createVisitor');
        Route::get('visitor/{visitor}', 'VisitorController@getVisitor');
        Route::post('visitor/referral', 'VisitorController@createVisitorReferral');
        Route::post('visitor/coupon/{coupon}', 'VisitorController@createVisitorCouponEvent');

        // Password reset links. These use laravel default password reset functionality. See Illuminate\Foundation\Auth/ResetsPasswords for details.
        //By Alka
        Route::post('user/reset-password/email', ['uses' => 'PasswordController@sendPasswordResetEmail']);
        Route::post('user/reset-password', 'PasswordController@resetPassword');

        // Error logging for the web side
        Route::post('error-logging/slack', 'ErrorController@logErrorToSlack');

        // Intentionally throw an API error for testing purposes
        Route::post('error-logging/throw', 'ErrorController@throwApiError');

        // Site map generation
        Route::get('site-map', 'SiteMapController@generateJson');
        Route::get('site-map-generate', 'SiteMapController@generateXml');

        // Web meta tags
        Route::get('web-meta', 'WebMetaController@index');
        Route::get('web-meta/slug', 'WebMetaController@getBySlug');

        // Callback route for Elimiwait request;
        Route::post('request/confirm', 'ElimiwaitRequestController@handleElimiwaitUpdate'); // link a new account
        Route::put('request/confirm', 'ElimiwaitRequestController@handleElimiwaitUpdate'); // link a new account

        Route::post('loyalty-club', 'LoyaltyClubUserController@store'); // Join loyalty club


        // NEW ROUTS FOR REFERRAL CODE FUNCTIONALITY

        // returns wallet configurations [DONE]
        Route::get('park/walletconfig', 'WalletController@getWalletConfig');
        Route::get('icongo/walletconfig', 'WalletController@getWalletConfig');
        Route::get('/reservation-anon/{reservation}', 'ReservationController@getByIdAnon');

        Route::get('guest-reservation-details/{reservationCode}/{ticketech_code}', 'ReservationController@getByIdGuest');

        // returns user favourite facilities
        Route::post('user/favourites', 'UserController@getUserFavourites'); // Receive - userId

        // update user favourite facilities [DONE]
        Route::post('user/setfavourites', 'UserController@setUserFavourites'); // Receive - userId, FacilityId, bool

        // returns user wallet and transaction details [DONE]
        Route::post('user/mywallet', 'WalletController@getUserWalletWithTransactions'); // Receive - userId

        // returns referred users of a particular user [DONE]
        Route::post('user/myreferred', 'UserController@getUserReferred'); // Receive - userId

        // update user device token [DONE]
        Route::post('updatedevice', 'UserController@updateDeviceToken'); // Receive - userId, deviceToken, deviceKey

        // update user device token [DONE]
        Route::post('user/getreferralcode', 'UserController@createReferralCode'); // Receive - userId

        // MODIFICATION IN EXISTING ROUTES FOR REFERRAL CODE FUNCTIONALITY

        // 1. Signup :
        // Create Referral Code, [DONE]
        // SetDeviceToken, [DONE]
        // Create Credit Transaction, [DONE]
        // UpdateWallet, [DONE]
        // Send Push Notification, [DONE]
        // Send Email [DONE]

        // 2. Reservation :
        // Redeem Wallet (IF Any) [DONE],
        // Credit Referrer (IF Any) [DONE],
        // Send Push Notification [DONE],
        // Send Email [DONE]

        // 3. Facility Search :
        // 1. Return User Favourites Included in Existing API [DONE]
        // 2. Check For the Search Radius To Cut Off Excess Response Data

        Route::resource('/promotypes', 'PromoTypeController');
        Route::resource('/channelpartners', 'ChannelPartnerController');
        Route::resource('/channelpartnertypes', 'ChannelPartnerTypeController');
        //Route::resource('/promotions', 'PromotionController');
        Route::get('/downloadexcel/{promotionId}', 'LatestPromotionController@downloadExcel');
        Route::post('checkpromo', 'LatestPromotionController@checkPromo');
        Route::post('validate_promocode', 'LatestPromotionController@checkPromoThirdParty');
        Route::post('update_promocode_usage', 'LatestPromotionController@updatePromocodeUsage');

        Route::post('mapco_validate_promocode', 'MapcoPromotionController@checkPromoThirdParty');

        Route::post('adduserpromocode', 'UserController@addUserPromoCode');
        Route::post('userPromoUsage', 'UserController@getUserPromoUsage');
        Route::post('addUserEvent', 'UserEventsLogController@addUserEvent');

        // Promocode Tracking API's
        //Route::get('/getallpromotions', 'PromotionController@getAll');
        // Route::get('/getByIDPromotions/{promotionId}', 'LatestPromotionController@show');
        Route::post('/updatePromotionsByID', 'LatestPromotionController@update');
        //Send promotion email
        Route::post('promotionsemail', 'UserController@promotionsemail');
        //delete user added promocode
        Route::post('deleteUserPromoCode', 'UserController@deleteUserPromoCode');
        //get wordpress post
        Route::get('getwordpressPost/{wpslug}', 'wordpressPostController@getwordpressPost');
        Route::post('updateFacilityBeacon/', 'FacilityController@updateBeacon');
        // Route::post('mp/register', 'MonthlyParkingUserController@registerAccount'); // link a new account

        //fetch user trsnsaction details and promotion details (28-11-2017)
        Route::get('/getUsers', 'UserController@getUser');
        Route::get('getUserSearch/search/{search}', 'UserController@userSearch');
        Route::get('getUserDetails/details/{search}', 'UserController@userDetails');
        //end

        //anuj writing for user feedback on 9th feb 2018    
        Route::get('user-feedback-listing', 'UserFeedbackController@getUserFeedbackListing');

        Route::get('externalcheckincheckout', 'DocumentationController@externalcheckincheckout');

        //Route::post('search/facilities-rates', ['uses' => 'SearchController@facilitiesRates']);
        Route::post('locations', 'ClientsController@facilitiesRates');
        Route::post('company/{CompanyId}/location/{LocationId}/CheckInUser', 'ClientsController@checkinuser');
        Route::post('company/{CompanyId}/location/{LocationId}/GetStayAmount', 'ClientsController@getStayAmount');
        Route::post('company/{CompanyId}/location/{LocationId}/CheckOutUser', 'ClientsController@CheckOutUser');
        //end
        Route::post('feedback', 'FeedbackController@addfeedback');
        // routes for GTM Tracking
        Route::post('gtm-tracking', 'GtmTrackingController@store');

        // Routes for loyality programs
        Route::get('loyalty-account/{emailId}', 'LoyaltyController@getUserProfile');
        Route::get('wallet/configs', 'WalletController@configs');
        Route::post('loyalty-user', 'UserController@createLoyaltyUser');
        Route::get('loyalty/cron', 'LoyaltyController@cron');
        // decrypt url parameters
        Route::post('decrypt/user', 'UserController@decryptUser');

        // check user email ID
        Route::post('validate-user', 'UserController@validateExistingUser');
        //to run shell script for invoice import database sync
        Route::post('db-sh', 'PublicEventhController@dbSh');

        /** inventory update availability **/
        Route::post('update-reservation-availability', 'ReservationController@updateReservationAvailability');

        Route::get('coupon/details/{rateId}', 'SavedCouponController@getCouponDetails');

        Route::post('newsletter', 'NewsletterController@store');

        Route::get('/get-autoapply-promocode', 'PromotionController@getAutoApplyPromocode');

        // Mobile Force Update : Vijay -26-06-2023
        Route::get('mobile-device-version/{device}/{appname?}', 'MobileDeviceVersionController@index');
        Route::put('update-device-version/{device}', 'MobileDeviceVersionController@updateVersion');
        // Townsend Report
        // Route::get('cashier-shift-report', 'ParkEngage\TownsendApiController@cashierShiftReport');

        Route::get('validation-report-townsend', 'ParkEngage\TownsendApiController@validationReportClerkWise');
        Route::get('reservation-report-excel', 'ParkEngage\TownsendApiController@reservationExcel');

        //16/10/2023 get base url
        Route::post('get_base_url', 'MobileDeviceVersionController@getBaseUrl');

        // PIMS - 13904
        Route::post('transient-inventory-check', ['uses' => 'RateController@geInventoryCheck']);
    }
);

// this url is call form google app added by santosh kumar 
Route::group(['middleware' => ['prevent-back-history', 'web']], function () {

    Route::get('/user-delete/{slug}', 'ParkEngage\GoogleUserDeleteController@userDelete');
    Route::post('/user-delete', 'ParkEngage\GoogleUserDeleteController@userDeleteAccount')->name('user-deletes');
});
