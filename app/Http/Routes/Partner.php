<?php
/**
 * Created by PhpStorm.
 * User: Anuj
 * Date: 18/6/18
 * Time: 10:49 AM
 */

Route::group(
    ['middleware' => ['apiresponse', 'partner-logging']], function () {

    Route::post('partner/authenticate', 'PartnerController@authenticate');
});

Route::group(
    ['middleware' => ['apiresponse', 'partner-logging','oauth']], function () {
    //Route::group(['middleware' => ['apiresponse', 'oauth', 'fetch-user', 'visitor']], function () {

    Route::post('facilities/pricing', 'PartnerController@facilityPricing');

    Route::get('icon-facilities/pricing', 'PartnerController@iconFacilityPricing');

    Route::post('partner-reservation/confirmation', 'PartnerController@reservationConfirmation');

    Route::post('partner-reservation/cancellation', 'PartnerController@reservationCancellation');

    //admin specific routes

});


Route::group(
    ['middleware' => ['apiresponse', 'oauth', 'fetch-user']], function () {

    Route::get('realtime/facility', 'FacilityController@realtimeWindow');

    Route::get('availability-matrix', 'PartnerController@availabilityMatrix');

    Route::get('generate-availability/{facilityId}', 'FacilityController@generateAvailability');

    Route::get('coupons/get-uptick', 'FacilityController@getUptick');

    Route::post('coupons/add-uptick', 'FacilityController@addUptick');

    Route::get('inventory-matrix', 'PartnerController@getInventory');

    Route::get('inventory-matrix-special-dates', 'PartnerController@getInventoryForSpecialDates');

    Route::get('inventory-matrix-special-dates-mob', 'PartnerController@getInventoryForSpecialDatesMob');

    Route::put('inventory/edit', 'PartnerController@bulkUpdateInventory');

    Route::post('inventory/generate-all', 'PartnerController@bulkUpdateInventory');

    Route::put('facility/update/{spots}/{facilityId}', 'PartnerController@updateInventory');
	
	//coupon threshold
	Route::get('coupon-threshold/{facility_id}', 'CouponThresholdController@getThreshold');
	Route::post('coupon-threshold', 'CouponThresholdController@addThreshold');
	Route::delete('coupon-threshold', 'CouponThresholdController@deleteThreshold');
	// end counpon threshold 

    //coupon threshold test
Route::get('coupon-threshold/{facility_id}', 'CouponThresholdController@getThreshold');
Route::post('coupon-threshold', 'CouponThresholdController@addThreshold');
Route::delete('coupon-threshold', 'CouponThresholdController@deleteThreshold');


// end counpon threshold test

});
