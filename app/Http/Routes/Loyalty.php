<?php
// LOYALTY ROUTE 


Route::group(['middleware' => ['apiresponse', 'oauth', 'fetch-user', 'visitor']], function () {
        // loyalty accounts:
        Route::get('loyalty', 'LoyaltyController@index');
        Route::put('loyalty/register', 'LoyaltyController@register');
        Route::put('loyalty/update-profile', 'LoyaltyController@updateUserProfile');
        Route::get('loyalty/accounts', 'LoyaltyController@accounts');
        Route::post('loyalty/card', 'LoyaltyController@addCard');
        Route::get('loyalty/cards', 'LoyaltyController@getCards');
        Route::get('loyalty/card/{id}', 'LoyaltyController@viewCard');
        Route::delete('loyalty/card/{id}', 'LoyaltyController@deleteCard');
        Route::put('loyalty/card/{id}', 'LoyaltyController@updateCard');
        Route::get('loyalty/history', 'LoyaltyController@getHistory');
        Route::get('loyalty/digital-card', 'LoyaltyController@getDigitalCard');
        Route::post('loyalty/add-loyalty-card', 'GoogleWallet\LoyaltyObjectController@addLoyaltyCard');
        Route::post('loyalty/getLoyaltyObject', 'GoogleWallet\LoyaltyObjectController@getLoyaltyObject');

        Route::get('user/wallets', 'WalletController@getUserWallets');
		
    }
);

