<?php
/*
 * -----------------------------------------------------------------------------
 * Copyright (c) 2024 ParkEngage Inc. All rights reserved.
 * -----------------------------------------------------------------------------
 *
 * This software is the confidential and proprietary information of
 * ParkEngage Inc. ("Confidential Information"). You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms
 * of the license agreement you entered into with ParkEngage Inc.
 *
 * This source code is a valuable trade secret of ParkEngage Inc.
 *
 * DISCLAIMER:
 * THIS SOFTWARE IS PROVIDED BY PARKENGAGE INC. "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL PARKENGAGE INC. OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are
 * those of the authors and should not be interpreted as representing official
 * policies, either expressed or implied, of ParkEngage Inc.
 * -----------------------------------------------------------------------------
 */

use Illuminate\Http\Request;

Route::post('import-event-list', 'ParkEngage\TestController@importEventExcelFile');

Route::get('cron-membership-expired-with-email', function () {
    Artisan::queue('membership-expired-with-email');
});

Route::get('cron-email-membership-reminder', function () {
    Artisan::queue('email:membership-reminder');
});

Route::get('cron-auto-renew-partner-membership', function () {
    Artisan::queue('cron:auto-renew-partner-membership', ['--full' => 1]);
});

Route::get('cron-promotion-expired', function () {
    Artisan::queue('promotion-expired');
});

Route::get('generate-inventory-for-next-months', function () {
    Artisan::queue('generate:inventory-for-next-months');
    return "done";
});

Route::get('partner-revenue-balance', function () {
    Artisan::queue('partner-revenue-balance');
    return "done";
});

Route::get('worldpay-payment-add', function () {
    Artisan::queue('worldpay-payment-account', array('id' => '1114', 'transaction_id' => ''));
    return "done";
});

Route::get('mapco-email-payment', function () {
    //  Artisan::queue('mapco:reservation-email', array('id' => '781'));
    Artisan::queue('mapco:reservation-email', array('id' => '8272'));
    return "done";
});
//Pave Autopay cron
Route::get('pave-auto-renew', function () {
    Artisan::queue('pave:ticket-autopay');
    return "done";
});

Route::get('townsend-auto-pay', function () {
    Artisan::queue('townsend:ticket-autopay');
    return "done";
});

Route::get('subordinate-email', function () {

    Artisan::queue('subordinate-account-create:email', array('id' => '3754', 'password' => 'Welcome@1234'));
    return "done";
});
Route::get('test-getdown-email', function () {

    //Artisan::queue('gate:down-check');

    return "done";
});

//wailuku permit auto renew
Route::get('wailuku-permit-renew', function () {
    Artisan::queue('diamond:permit-renew');
    return "done";
});

Route::get('united-permit-renew', function () {
    Artisan::queue('united:ticket-autopay');
    return "done";
});

Route::get('woodman-auto-pay', function () {
    Artisan::queue('woodman:ticket-autopay-heartland');
    return "done";
});

Route::get('united-auto-pay', function () {
    Artisan::queue('united:ticket-autopay');
    return "done";
});

Route::get('kstreet-permit-renew', function () {
    Artisan::queue('kstreet:permit-renew');
    return "done";
});

Route::post('hl-transaction-list', 'ParkEngage\SubordinateController@getHLTransactionList');
//sagar
Route::get('genarate-apple-wallet-pass', 'ParkEngage\WalletPassController@generateApplePass');
Route::get('genarate-google-wallet-pass', 'ParkEngage\WalletPassController@generateGooglePass');
Route::get('genarate-permit-apple-wallet-pass', 'ParkEngage\WalletPassController@generatePermitApplePass');
Route::get('genarate-permit-google-wallet-pass', 'ParkEngage\WalletPassController@generatePermitGooglePass');
Route::get('genarate-reservation-apple-wallet-pass', 'ParkEngage\WalletPassController@genrateReservationApplePass');
Route::get('genarate-reservation-google-wallet-pass', 'ParkEngage\WalletPassController@generateReservationGooglePass');
Route::get('genarate-pass-apple-wallet-pass', 'ParkEngage\WalletPassController@genratePassApplePass');
Route::get('genarate-pass-google-wallet-pass', 'ParkEngage\WalletPassController@generatePassGooglePass');


// lokesh
Route::get('test-roc-email/{reservation_id}', function ($id, Request $request) {
    $options = ['reservationId' => $id];
    $client_secret = $request->header('X-ClientSecret');
    if ($client_secret) {
        $options['--client_secret'] = $client_secret;
    }
    Artisan::queue('reservation:email', $options);
    return "done";
});

Route::get('permit-email', function () {
    Artisan::queue('diamondpermitadmin:email', array('permit_request_id' => '638', 'password' => 'U@jsr123', 'is_admin' => '1'));
    return "done";
});

// intrapark permit auto renew
Route::get('intrapark-auto-renew', function () {
    Artisan::queue('intrapark:permit-renew');
    return "done";
});

Route::get('pci-auto-pay', function () {
    //dd('here');
    Artisan::queue('pci:ticket-autopay');
    return "done";
});
Route::get('permit-cancel-email', function () {
    Artisan::queue('are:cancel-permit-email', array('permit_request_id' => '100'));
    return "done";
});

//for testing 
Route::get('create-skidata/{permit_id}', 'ParkEngage\AreRegisterController@createSkiDataTicket');
Route::get('cancel-skidata/{permit_id}', 'ParkEngage\AreRegisterController@cancelSkiData');

Route::post('permit-invite', 'ParkEngage\AreRegisterController@permitInvite');

Route::get('/permit-image-front/{logo}', 'ParkEngage\AreRegisterController@getFrontUrl');
Route::get('/permit-image-back/{logo}', 'ParkEngage\AreRegisterController@getBackUrl');

Route::get('/user-permit-image-front/{logo}', 'ParkEngage\AreRegisterController@getUserFrontUrl');
Route::get('/user-permit-image-back/{logo}', 'ParkEngage\AreRegisterController@getUserBackUrl');

// New Image Change Diamond:

Route::get('/permit-id-proof-front/{logo}', 'ParkEngage\AreRegisterController@getFrontImageUrl');
Route::get('/permit-id-proof-back/{logo}', 'ParkEngage\AreRegisterController@getBackImageUrl');
Route::get('/permit-id-utility-bill/{logo}', 'ParkEngage\AreRegisterController@getUtilityBillUrl');
Route::get('/permit-id-tax-return/{logo}', 'ParkEngage\AreRegisterController@getTaxReturnUrl');
Route::get('/permit-id-drivers-license/{logo}', 'ParkEngage\AreRegisterController@getDriversLicense');
Route::get('/permit-id-pay-stub/{logo}', 'ParkEngage\AreRegisterController@getPayStub');
Route::get('/permit-id-proof-of-low-income/{logo}', 'ParkEngage\AreRegisterController@getProofOfLowIncome');
Route::get('/permit-id-any/{logo}', 'ParkEngage\AreRegisterController@getAny');
Route::get('/permit-id-proof-of-low-income-excel/{logo}', 'ParkEngage\AreRegisterController@getProofOfLowIncomeExcel');
Route::get('/state-id-drivers-license/{logo}', 'ParkEngage\AreRegisterController@getStateIdDriversLicense');
#DD PIMS-11836 
Route::get('/permit-university-id/{logo}', 'ParkEngage\AreRegisterController@getPermitUniversityId');


//Yankeens :: Author Alka
Route::get('/view-signature/{docid}', 'ParkEngage\AreRegisterController@viewSignature');

Route::get('/user-permit-id-proof-front/{logo}', 'ParkEngage\AreRegisterController@getUserFrontImageUrl');
Route::get('/user-permit-id-proof-back/{logo}', 'ParkEngage\AreRegisterController@getUserBackImageUrl');
Route::get('/user-permit-id-utility-bill/{logo}', 'ParkEngage\AreRegisterController@getUserUtilityBillUrl');
Route::get('/user-permit-id-tax-return/{logo}', 'ParkEngage\AreRegisterController@getUserTaxReturnUrl');
Route::get('/user-permit-id-drivers-license/{logo}', 'ParkEngage\AreRegisterController@getUserDriversLicense');
Route::get('/user-permit-id-pay-stub/{logo}', 'ParkEngage\AreRegisterController@getUserPayStub');
Route::get('/user-permit-id-proof-of-low-income/{logo}', 'ParkEngage\AreRegisterController@getUserProofOfLowIncome');
Route::get('/user-permit-id-any/{logo}', 'ParkEngage\AreRegisterController@getUserAny');
Route::get('/user-permit-id-proof-of-low-income-excel/{logo}', 'ParkEngage\AreRegisterController@getUserProofOfLowIncomeExcel');


Route::get('parkingpayments-download-receipt/{ticket_number}', 'ParkEngage\PaveWebController@downloadReceipt');
Route::get('/brand-settings-applogo/{logo}', 'ParkEngage\PartnerController@getAppLogoUrl');


Route::get('/signature/{id}', 'ParkEngage\DiamondGateApiController@getSignatureUrl');
Route::get('/license-plate-image/{id}', 'ParkEngage\DiamondGateApiController@getLicensePlateImageUrl');
Route::get('/warning-license-plate-image/{id}', 'ParkEngage\DiamondGateApiController@getWarningLicensePlateImageUrl');

Route::get('/warning-image/{id}', 'ParkEngage\DiamondGateApiController@getWarningImageUrl');

Route::get('townsend-download-receipt/{ticket_number}', 'ParkEngage\TownsendWebController@downloadReceipt');

Route::get('mapco-failed-record',  'ParkEngage\AutogateApiController@getMapcoFailedRecord');

Route::post('touchless-facility-qrcode',  'FacilityController@generateFacilityQrCode');

Route::get('download-pass-qrcode/{id}/{facility_id?}',  'UserPassesController@downloadPassQrCode');

Route::get('atlanta-download-receipt/{ticket_number}', 'ParkEngage\AutoGateController@downloadReceipt');
Route::get('mapco-download-receipt/{ticket_number}', 'ParkEngage\MapcoWebController@downloadReceipt');
Route::get('qr-image/{image}', 'ParkEngage\CheckinCheckoutController@qrImage');

Route::get('/brand-settings-logo/{logo}', 'ParkEngage\PartnerController@getLogoUrl');
Route::get('/brand-settings-banner/{banner}', 'ParkEngage\PartnerController@getBannerUrl');
Route::get('/brand-settings-favicon/{favicon}', 'ParkEngage\PartnerController@getFaviconUrl');
Route::get('/customer-banner/{banner}', 'ParkEngage\PartnerController@getCustomerBannerUrl');
Route::get('/reservation-banner/{banner}', 'ParkEngage\PartnerController@getReservationBannerUrl');
// Facility Wise Brand Settings
Route::get('/facility-brand-settings-logo/{logo}', 'ParkEngage\PartnerController@getLogoFacilityUrl');
Route::get('/facility-brand-settings-banner/{banner}', 'ParkEngage\PartnerController@getFacilityBannerUrl');
Route::get('/facility-settings-favicon/{favicon}', 'ParkEngage\PartnerController@getFacilityFaviconUrl');
Route::get('/facility-customer-banner/{banner}', 'ParkEngage\PartnerController@getFacilityCustomerBannerUrl');
Route::get('/facility-reservation-banner/{banner}', 'ParkEngage\PartnerController@getFacilityReservationBannerUrl');

// Business Wise Brand Settings
Route::get('/business-brand-settings-logo/{logo}', 'ParkEngage\AffiliateBussinessController@getLogoUrl');
Route::get('/business-brand-settings-banner/{banner}', 'ParkEngage\AffiliateBussinessController@getBannerUrl');
Route::get('/business-brand-settings-favicon/{favicon}', 'ParkEngage\AffiliateBussinessController@getFaviconUrl');

Route::get('/services-image/{image}', 'ParkEngage\ServiceController@getServiceUrl');
Route::get('/services-details-image/{image}', 'ParkEngage\ServiceController@getServiceDetailsUrl');

Route::get('booking-qrcode/{photo}', 'ReservationController@showQrCodeImage');
Route::get('pass-qrcode/{photo}', 'ReservationController@showQrCodeImage');

//mapco qr code
Route::get('mapco-download-qrcode/{reservation}', 'ParkEngage\MapcoController@downloadQrCode');
Route::get('mapco-qrcode-email/{qrcode}', 'ParkEngage\MapcoController@showQrCodeImage');
Route::get('mapco-download-checkin-qrcode/{reservation}', 'ParkEngage\MapcoController@downloadQrCodeCheckin');

//AAA Reservation
Route::get('aaa-download-booking-pdf/{reservation}', 'ParkEngage\AtlantaReservationController@downloadBookingPdf');
Route::get('aaa-download-booking-pdf-new/{reservation}', 'ParkEngage\AtlantaReservationController@downloadBookingPdfNew');

// Vijay - 10-11-2022
Route::get('mapco-download-report', 'ParkEngage\MapcoController@downloadReport');
// Vijay - 30-05-2023
Route::get('diamond-citation-report', 'ParkEngage\AreRegisterController@diamondCitationReport');
Route::get('diamond-citation-report-new', 'ParkEngage\AreRegisterController@diamondCitationReport_new');

Route::get('check-lpr-accuracy-license-plate', 'ParkEngage\TestController@checkLPRTownsendTicket');

//download dynamic passes vikrant
Route::get('download-passes/{id}', 'UserPassesController@downloadPass');

Route::post('hl-paymentdetails', 'ParkEngage\HeartlandProfileController@getHLTransactionList');

//Route::group(['middleware' => ['']], function () {
Route::group(['middleware' => ['prevent-back-history', 'web']], function () {

    Route::get('touchless-promotion-qrcode/{id}',  'LatestPromotionController@generatePromotionQrCode');

    //townsend web
    Route::get('townsend-pay/{ticket_number}', 'ParkEngage\TownsendWebController@getCheckinPaymentDetails');
    Route::get('townsend-error-checkin', 'ParkEngage\TownsendWebController@errorCheckin');
    Route::get('townsend-thankyou-payment/{ticket_number}', 'ParkEngage\TownsendWebController@getCheckinDetailsPaymentThankyou');
    Route::get('townsend-payment/{ticket_number}', 'ParkEngage\TownsendWebController@getAfterCheckinPaymentDetails');
    Route::get('townsend-error-facility', 'ParkEngage\TownsendWebController@errorFacility');
    Route::post('townsend-make-ticket-payment', 'ParkEngage\TownsendWebController@makePayment');
    Route::get('townsend-thankyou-payment/{ticket_number}', 'ParkEngage\TownsendWebController@getCheckinDetailsPaymentThankyou');
    Route::get('townsend-scan-screen/{ticket_number}', 'ParkEngage\TownsendWebController@getCheckinDetails');
    Route::post('townsend-confirm-checkout', 'ParkEngage\TownsendWebController@confirmCheckout');
    Route::get('townsend-thankyou-checkout/{ticket_number}', 'ParkEngage\TownsendWebController@thankyouCheckout');
    Route::get('townsend-already-checkout', 'ParkEngage\TownsendWebController@alreadyCheckout');




    Route::get('scan-screen/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinDetails');
    Route::post('confirm-checkin', 'ParkEngage\NewTwilioController@confirmCheckin');
    Route::get('thankyou-checkin', 'ParkEngage\NewTwilioController@thankyouCheckin');
    Route::get('thankyou-checkout/{ticket_number}', 'ParkEngage\NewTwilioController@thankyouCheckout');
    Route::get('already-checkout', 'ParkEngage\NewTwilioController@alreadyCheckout');
    Route::get('error-checkin', 'ParkEngage\NewTwilioController@errorCheckin');
    Route::get('error-checkout', 'ParkEngage\NewTwilioController@errorCheckout');
    Route::get('test-scan-screen/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinDetailsTest');
    Route::get('demo-scan-screen/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinDetailsDemo');
    Route::get('demo2-scan-screen/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinDetailsDemoTwo');
    Route::get('demo3-scan-screen/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinDetailsDemoThree');
    Route::get('pay/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinPaymentDetails');

    Route::get('payment/{ticket_number}', 'ParkEngage\NewTwilioController@getAfterCheckinPaymentDetails');

    Route::get('overstay-pay/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinPaymentDetailsForOverstay');

    Route::get('pay-design/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinPaymentDetailsDesign');

    Route::get('custom-decrypt', 'ParkEngage\NewTwilioController@customDecrypt');

    Route::post('make-ticket-payment', 'ParkEngage\NewTwilioController@makePayment');

    Route::post('make-overstay-ticket-payment', 'ParkEngage\NewTwilioController@makeOverstayPayment');

    Route::get('tcp-connection', 'ParkEngage\TCPController@index');
    Route::get('tcp-read', 'ParkEngage\TCPController@tcpRead');

    Route::get('thankyou-payment/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinDetailsPaymentThankyou');
    Route::get('prepaid-checkin-screen/{ticket_number}', 'ParkEngage\NewTwilioController@getPrepaidCheckinDetails');
    Route::get('prepaid-confirm-checkin-screen/{ticket_number}', 'ParkEngage\NewTwilioController@getPrepaidConfirmCheckinDetails');
    Route::get('prepaid-checkout-screen/{ticket_number}', 'ParkEngage\NewTwilioController@getPrepaidCheckoutDetails');
    Route::get('prepaid-checkin-success/{ticket_number}', 'ParkEngage\NewTwilioController@getPrepaidConfirmCheckinSuccess');
    Route::get('prepaid-checkout-details/{ticket_number}', 'ParkEngage\NewTwilioController@getPrepaidConfirmCheckinThankyou');
    Route::post('prepaid-confirm-checkin', 'ParkEngage\NewTwilioController@confirmPrepaidCheckin');
    Route::post('prepaid-confirm-checkout', 'ParkEngage\NewTwilioController@confirmPrepaidCheckout');
    Route::get('prepaid-overstay-pay/{ticket_number}', 'ParkEngage\NewTwilioController@getPrepaidCheckinPaymentDetailsForOverstay');
    Route::post('make-prepaid-overstay-ticket-payment', 'ParkEngage\NewTwilioController@makePrepaidOverstayPayment');
    Route::get('error-prepaid-checkin', 'ParkEngage\NewTwilioController@errorPrepaidCheckin');
    Route::get('prepaid-checkin-checkout-details/{ticket_number}', 'ParkEngage\NewTwilioController@getPrepaidCheckinCheckoutDetails');
    Route::get('thankyou-prepaid-checkout/{ticket_number}', 'ParkEngage\NewTwilioController@thankyouPrepaidCheckout');
    Route::get('error-facility', 'ParkEngage\NewTwilioController@errorFacility');

    Route::get('touchless-parking/{key}', 'ParkEngage\NewTwilioController@getTouchlessParkingUser');

    Route::post('touchless-parking-confirm-checkin', 'ParkEngage\NewTwilioController@confirmTouchlessCheckin');

    Route::get('touchless-parking-checkin-details/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchlessParkingCheckinDetails');

    Route::get('touchless-parking-checkout-details/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchlessParkingCheckoutDetails');

    Route::get('touchless-parking-payment-success/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchlessParkingPaymentThankyou');

    Route::post('make-touchless-parking-payment', 'ParkEngage\NewTwilioController@makeTouchlessParkingPayment');

    Route::get('touchless-parking-checkout/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchlessParkingCheckout');

    Route::post('touchless-parking-confirm-checkout', 'ParkEngage\NewTwilioController@confirmTouchlessParkingCheckout');

    Route::get('touchless-parking-thankyou-checkout/{ticket_number}', 'ParkEngage\NewTwilioController@thankyouTouchlessParkingCheckout');

    Route::get('touchless-parking-overstay-pay/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchlessParkingDetailsForOverstay');

    Route::post('touchless-parking-make-overstay-payment', 'ParkEngage\NewTwilioController@makeTouchlessParkingOverstayPayment');

    Route::get('touchless-parking-prepaid/{ticket_number}/{encrypt?}', 'ParkEngage\NewTwilioController@getTouchlessParkingPrepaidCheckinDetails');

    Route::get('touchless-parking-prepaid-checkin/{ticket_number}/{encrypt?}', 'ParkEngage\NewTwilioController@getTouchelessParkingPrepaidCheckin');

    Route::post('touchless-parking-prepaid-confirm-checkin', 'ParkEngage\NewTwilioController@confirmTouchelessParkingPrepaidCheckin');

    Route::get('touchless-parking-prepaid-checkin-success/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchelessParkingPrepaidConfirmCheckinSuccess');

    Route::get('touchless-parking-prepaid-checkout-details/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchelessParkingPrepaidCheckoutDetails');

    Route::get('touchless-parking-prepaid-checkout/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchelessParkingPrepaidCheckout');

    Route::get('touchless-parking-prepaid-checkout-screen/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchelessParkingPrepaidCheckoutScreen');


    Route::post('touchless-parking-prepaid-confirm-checkout', 'ParkEngage\NewTwilioController@confirmTouchelessParkingPrepaidCheckout');

    Route::get('touchless-parking-prepaid-checkout-success/{ticket_number}', 'ParkEngage\NewTwilioController@touchlessParkingPrepaidCheckoutSuccess');

    Route::get('touchless-parking-prepaid-overstay-pay/{ticket_number}', 'ParkEngage\NewTwilioController@getTouchlessParkingPrepaidCheckinPaymentDetailsForOverstay');

    Route::post('touchless-parking-make-prepaid-overstay-ticket-payment', 'ParkEngage\NewTwilioController@makeTouchlessParkingPrepaidOverstayPayment');
    //new checkout flow
    Route::get('touchless-parking-verify-checkout/{key}', 'ParkEngage\NewTwilioController@getTouchlessParkingVerifyCheckout');

    Route::post('touchless-parking-confirm-direct-checkout', 'ParkEngage\NewTwilioController@confirmTouchlessParkingDirectCheckout');


    //atlanta new flow Route

    Route::get('touchless-parking-atlanta-direct/{key}', 'ParkEngage\AtlantaDirectController@touchlessParkingDirectCheckin');

    Route::get('touchless-parking-atlanta-direct-checkin/{ticket_number}', 'ParkEngage\AtlantaDirectController@touchlessParkingDirectCheckinDetails');

    Route::get('touchless-parking-atlanta-direct-verify-checkin/{ticket_number}', 'ParkEngage\AtlantaDirectController@touchlessParkingDirectCheckinVerify');

    Route::post('touchless-parking-atlanta-direct-confirm-user-checkin', 'ParkEngage\AtlantaDirectController@touchlessParkingDirectConfirmUserCheckin');

    Route::get('touchless-parking-atlanta-direct-checkin-details/{ticket_number}', 'ParkEngage\AtlantaDirectController@getTouchlessParkingDirectCheckinDetails');

    Route::get('touchless-parking-atlanta-direct-prepaid/{ticket_number}/{encrypt?}', 'ParkEngage\AtlantaDirectController@getTouchlessParkingDirectPrepaidCheckinDetails');

    Route::get('touchless-parking-atlanta-direct-prepaid-checkin-details/{ticket_number}', 'ParkEngage\AtlantaDirectController@getTouchlessParkingDirectPrepaidCheckinDetails');

    Route::get('error-gate', 'ParkEngage\AtlantaDirectController@errorGate');

    //sms flow rout

    Route::get('get-prepaid-user-checkin/{ticket_number}', 'ParkEngage\NewTwilioController@getPrepaidUserCheckin');

    Route::post('prepaid-user-checkin-confirm', 'ParkEngage\NewTwilioController@prepaidUserCheckinConfirm');

    Route::get('checkin-pay/{ticket_number}', 'ParkEngage\NewTwilioController@getCheckinPayment');

    Route::get('get-user-checkout/{key}', 'ParkEngage\NewTwilioController@getPrepaidUserCheckout');

    Route::post('prepaid-user-checkout-confirm', 'ParkEngage\NewTwilioController@prepaidUserCheckoutConfirm');

    Route::get('permit-checkout/{ticket_number}', 'ParkEngage\PermitController@getPermitCheckinDetails');

    Route::get('permit-error-checkin', 'ParkEngage\PermitController@errorPermitCheckin');

    Route::get('permit-checkout-screen/{ticket_number}', 'ParkEngage\PermitController@getPermitCheckoutDetails');

    Route::post('permit-confirm-checkout', 'ParkEngage\PermitController@confirmPermitCheckout');

    Route::get('thankyou-permit-checkout/{ticket_number}', 'ParkEngage\PermitController@thankyouPermitCheckout');

    Route::get('demo-touchless-parking/{key}', 'ParkEngage\NewTwilioController@getTouchlessParkingDemoUser');

    //atlanta routes

    Route::get('atlanta-pay/{ticket_number}', 'ParkEngage\AtlantaController@getCheckinPaymentDetails');

    Route::get('atlanta-checkin-pay/{ticket_number}', 'ParkEngage\AtlantaController@getCheckinPayment');

    Route::get('atlanta-payment/{ticket_number}', 'ParkEngage\AtlantaController@getAfterCheckinPaymentDetails');

    Route::post('atlanta-make-ticket-payment', 'ParkEngage\AtlantaController@makePayment');

    Route::get('atlanta-thankyou-payment/{ticket_number}', 'ParkEngage\AtlantaController@getCheckinDetailsPaymentThankyou');

    Route::get('atlanta-scan-screen/{ticket_number}', 'ParkEngage\AtlantaController@getCheckinDetailsOnCheckout');

    Route::post('atlanta-confirm-checkin', 'ParkEngage\AtlantaController@confirmCheckin');

    Route::get('atlanta-thankyou-checkout/{ticket_number}', 'ParkEngage\AtlantaController@thankyouCheckout');


    Route::get('atlanta-get-user-checkout/{key}', 'ParkEngage\AtlantaController@getPrepaidUserCheckout');

    Route::post('atlanta-prepaid-user-checkout-confirm', 'ParkEngage\AtlantaController@prepaidUserCheckoutConfirm');

    Route::get('atlanta-prepaid-checkout-details/{ticket_number}', 'ParkEngage\AtlantaController@getPrepaidConfirmCheckinThankyou');


    Route::get('touchless-parking-atlanta/{key}', 'ParkEngage\AtlantaController@getTouchlessParkingUser');

    Route::post('touchless-parking-atlanta-confirm-checkin', 'ParkEngage\AtlantaController@confirmTouchlessCheckin');

    Route::get('touchless-parking-atlanta-checkin-details/{ticket_number}', 'ParkEngage\AtlantaController@getTouchlessParkingCheckinDetails');

    Route::get('touchless-parking-atlanta-checkout-details/{ticket_number}', 'ParkEngage\AtlantaController@getTouchlessParkingCheckoutDetails');

    Route::get('touchless-parking-atlanta-payment-success/{ticket_number}', 'ParkEngage\AtlantaController@getTouchlessParkingPaymentThankyou');

    Route::post('make-touchless-parking-atlanta-payment', 'ParkEngage\AtlantaController@makeTouchlessParkingPayment');

    Route::get('touchless-parking-atlanta-checkout/{ticket_number}', 'ParkEngage\AtlantaController@getTouchlessParkingCheckout');

    Route::post('touchless-parking-atlanta-confirm-checkout', 'ParkEngage\AtlantaController@confirmTouchlessParkingCheckout');

    Route::get('touchless-parking-atlanta-thankyou-checkout/{ticket_number}', 'ParkEngage\AtlantaController@thankyouTouchlessParkingCheckout');

    Route::get('touchless-parking-atlanta-prepaid/{ticket_number}/{encrypt?}', 'ParkEngage\AtlantaController@getTouchlessParkingPrepaidCheckinDetails');

    Route::get('touchless-parking-atlanta-prepaid-checkin/{ticket_number}/{encrypt?}', 'ParkEngage\AtlantaController@getTouchelessParkingPrepaidCheckin');

    Route::get('touchless-parking-atlanta-prepaid-checkin-success/{ticket_number}', 'ParkEngage\AtlantaController@getTouchelessParkingPrepaidConfirmCheckinSuccess');

    Route::post('touchless-parking-atlanta-prepaid-confirm-checkin', 'ParkEngage\AtlantaController@confirmTouchelessParkingPrepaidCheckin');

    Route::get('touchless-parking-atlanta-prepaid-checkout-details/{ticket_number}', 'ParkEngage\AtlantaController@getTouchelessParkingPrepaidCheckoutDetails');

    Route::get('touchless-parking-atlanta-prepaid-checkout/{ticket_number}', 'ParkEngage\AtlantaController@getTouchelessParkingPrepaidCheckout');

    Route::get('touchless-parking-atlanta-prepaid-checkout-success/{ticket_number}', 'ParkEngage\AtlantaController@touchlessParkingPrepaidCheckoutSuccess');

    Route::get('touchless-parking-atlanta-prepaid-checkout-screen/{ticket_number}', 'ParkEngage\AtlantaController@getTouchelessParkingPrepaidCheckoutScreen');

    Route::post('touchless-parking-atlanta-prepaid-confirm-checkout', 'ParkEngage\AtlantaController@confirmTouchelessParkingPrepaidCheckout');

    Route::get('touchless-parking-atlanta-verify-checkout/{key}', 'ParkEngage\AtlantaController@getTouchlessParkingVerifyCheckout');

    Route::post('touchless-parking-atlanta-confirm-direct-checkout', 'ParkEngage\AtlantaController@confirmTouchlessParkingDirectCheckout');

    Route::get('touchless-parking-atlanta-overstay-pay/{ticket_number}', 'ParkEngage\AtlantaController@getTouchlessParkingDetailsForOverstay');

    Route::post('touchless-parking-atlanta-make-overstay-payment', 'ParkEngage\AtlantaController@makeTouchlessParkingOverstayPayment');

    Route::get('touchless-parking-atlanta-prepaid-overstay-pay/{ticket_number}', 'ParkEngage\AtlantaController@getTouchlessParkingPrepaidCheckinPaymentDetailsForOverstay');

    Route::post('touchless-parking-atlanta-make-prepaid-overstay-ticket-payment', 'ParkEngage\AtlantaController@makeTouchlessParkingPrepaidOverstayPayment');


    Route::get('atlanta-error-checkout', 'ParkEngage\AtlantaController@errorCheckout');
    Route::get('atlanta-error-checkin', 'ParkEngage\AtlantaController@errorCheckin');

    Route::get('atlanta-checkin-checkout-receipt/{ticket_number}', 'ParkEngage\AutoGateController@getCheckinCheckoutReceipt');

    //Spelman direct touchless routes

    Route::get('touchless-parking-permit/{key}', 'ParkEngage\PermitController@getTouchlessParkingUser');

    Route::post('touchless-parking-permit-confirm-checkin', 'ParkEngage\PermitController@confirmTouchlessCheckin');

    Route::get('touchless-parking-permit-checkin-details/{ticket_number}', 'ParkEngage\PermitController@getTouchlessParkingCheckinDetails');

    Route::get('touchless-parking-permit-checkout-details/{ticket_number}', 'ParkEngage\PermitController@getTouchlessParkingCheckoutDetails');

    Route::get('touchless-parking-permit-payment-success/{ticket_number}', 'ParkEngage\PermitController@getTouchlessParkingPaymentThankyou');

    Route::post('make-touchless-parking-permit-payment', 'ParkEngage\PermitController@makeTouchlessParkingPayment');

    Route::get('touchless-parking-permit-checkout/{ticket_number}', 'ParkEngage\PermitController@getTouchlessParkingCheckout');

    Route::post('touchless-parking-permit-confirm-checkout', 'ParkEngage\PermitController@confirmTouchlessParkingCheckout');

    Route::get('touchless-parking-permit-thankyou-checkout/{ticket_number}', 'ParkEngage\PermitController@thankyouTouchlessParkingCheckout');

    Route::get('touchless-parking-permit-prepaid/{ticket_number}/{encrypt?}', 'ParkEngage\PermitController@getTouchlessParkingPrepaidCheckinDetails');

    Route::get('touchless-parking-permit-prepaid-checkin/{ticket_number}/{encrypt?}', 'ParkEngage\PermitController@getTouchelessParkingPrepaidCheckin');

    Route::get('touchless-parking-permit-prepaid-checkin-success/{ticket_number}', 'ParkEngage\PermitController@getTouchelessParkingPrepaidConfirmCheckinSuccess');

    Route::post('touchless-parking-permit-prepaid-confirm-checkin', 'ParkEngage\PermitController@confirmTouchelessParkingPrepaidCheckin');

    Route::get('touchless-parking-permit-prepaid-checkout-details/{ticket_number}', 'ParkEngage\PermitController@getTouchelessParkingPrepaidCheckoutDetails');

    Route::get('touchless-parking-permit-prepaid-checkout/{ticket_number}', 'ParkEngage\PermitController@getTouchelessParkingPrepaidCheckout');

    Route::get('touchless-parking-permit-prepaid-checkout-success/{ticket_number}', 'ParkEngage\PermitController@touchlessParkingPrepaidCheckoutSuccess');

    Route::get('touchless-parking-permit-prepaid-checkout-screen/{ticket_number}', 'ParkEngage\PermitController@getTouchelessParkingPrepaidCheckoutScreen');

    Route::post('touchless-parking-permit-prepaid-confirm-checkout', 'ParkEngage\PermitController@confirmTouchelessParkingPrepaidCheckout');

    Route::get('touchless-parking-permit-verify-checkout/{key}', 'ParkEngage\PermitController@getTouchlessParkingVerifyCheckout');

    Route::post('touchless-parking-permit-confirm-direct-checkout', 'ParkEngage\PermitController@confirmTouchlessParkingDirectCheckout');

    //autogate web routes
    Route::get('touchless-parking-autogate/{key}', 'ParkEngage\AutoGateController@getTouchlessParkingUser');

    Route::post('touchless-parking-autogate-confirm-checkin', 'ParkEngage\AutoGateController@confirmTouchlessCheckin');


    Route::get('autogate-payment/{encrypt}', 'ParkEngage\AutoGateController@getBeforeCheckinPaymentDetails');

    Route::post('autogate-make-ticket-payment', 'ParkEngage\AutoGateController@makePayment');

    Route::get('autogate-thankyou-payment/{ticket_number}', 'ParkEngage\AutoGateController@getCheckinDetailsPaymentThankyou');

    Route::get('autogate-thankyou-checkout/{ticket_number}', 'ParkEngage\AutoGateController@thankyouCheckout');

    Route::get('autogate-error-facility', 'ParkEngage\AutoGateController@errorFacility');

    Route::get('autogate-citation-payment-details/{citation}', 'ParkEngage\AutoGateController@getOverstayPaymentDetails');

    Route::post('autogate-make-citation-payment', 'ParkEngage\AutoGateController@makeCitationPayment');

    Route::get('autogate-citation-thankyou-checkout/{citation}', 'ParkEngage\AutoGateController@thankyouCitationCheckout');

    Route::get('autogate-extend-payment-details/{ticket_number}', 'ParkEngage\AutoGateController@getExtendCheckinPaymentDetails');

    Route::post('autogate-make-extend-payment', 'ParkEngage\AutoGateController@makeExtendPayment');

    Route::get('autogate-error-extend', 'ParkEngage\AutoGateController@errorExtend');

    Route::get('autogate-worldpay-response', 'ParkEngage\AutoGateController@worldpayResponse');

    Route::get('autogate-worldpay-extend-response/{days}', 'ParkEngage\AutoGateController@worldpayExtendResponse');

    Route::get('autogate-worldpay-citation-response', 'ParkEngage\AutoGateController@worldpayCitationResponse');

    Route::get('vertical', 'ParkEngage\AutoGateController@vertical');

    //mapco web route

    Route::get('mapco-checkin-checkout-receipt/{ticket_number}', 'ParkEngage\MapcoWebController@getCheckinCheckoutReceipt');

    //townsend
    //Route::get('townsend-checkin-checkout-receipt', 'ParkEngage\TownsendWebController@getTicketReceipt');
    Route::get('townsend', 'ParkEngage\TownsendWebController@getTicketReceipt');
    Route::post('townsend-get-checkin-checkout-receipt', 'ParkEngage\TownsendWebController@checkinCheckoutReceipt');

    Route::get('parkingpayments-checkin-checkout-receipt/{ticket_number}', 'ParkEngage\PaveWebController@getCheckinCheckoutReceipt');
    Route::get('parkingpayments', 'ParkEngage\PaveWebController@getTicketReceipt');
    Route::post('parkingpayments-get-checkin-checkout-receipt', 'ParkEngage\PaveWebController@checkinCheckoutReceipt');
});

Route::post('download-ticket-details', ['uses' => 'ParkEngage\PartnerController@downloadTicketDetails']);

// Profac
Route::post('propay/propay/webhooks', 'ProPayController@getAccountKeysByWebhook');
Route::group(
    ['middleware' => ['apiresponse', 'oauth', 'timezone']],
    function () {
        // Account Management
        Route::get('propay/merchant/account-detail/{merchant_id}', 'ProPayController@getMerchantAccountDetail');
        Route::get('propay/merchants/page/{page}', ['uses' => 'ProPayController@getMerchantAccounts', 'middleware' => 'pagination']);
        Route::get('propay/merchant/{merchant_id}', 'ProPayController@getMerchantAccountById');
        Route::delete('propay/merchant/delete/{merchant_id}', 'ProPayController@deleteMerchantAccount');
        Route::put('propay/merchant/create', 'ProPayController@createMerchantAccount');
        Route::put('propay/merchant/edit/bank-account/{merchant_id}', 'ProPayController@editMerchantBankAccount');
        Route::put('propay/merchant/edit/contact-info/{merchant_id}', 'ProPayController@editMerchantContactInfo');
        Route::put('propay/merchant/edit/address/{merchant_id}', 'ProPayController@editMerchantAddress');
        Route::put('propay/merchant/edit/business-info/{merchant_id}', 'ProPayController@editMerchantBusinessInfo');
        Route::post('propay/charge-back-upload', 'ProPayController@uploadChargebackDocument');
        Route::post('propay/document-upload', 'ProPayController@uploadDocument');
        Route::post('propay/transaction-details', 'ProPayController@getTransactionDetails');
        Route::post('propay/sso-token', 'ProPayController@getSSOAuthToken');
    }
);

Route::post('lpr-webhook', 'ParkEngage\LPRController@handleLPRFeed');

Route::group(
    ['middleware' => ['apiresponse', 'timezone']],
    function () {
        Route::post('apple-pay-session', 'ParkEngage\HeartlandProfileController@applePaySession');
        Route::post('parkengage-ungated-tattile-feed', 'ParkEngage\WoodmanCheckinCheckoutController@ungatedTattileFeed');
        Route::post('parkengage-tattile-feed', 'ParkEngage\TattileCheckinCheckoutController@parkengageTattileFeed');
        Route::post('parkengage-townsend-tattile-feed', 'ParkEngage\TattileCheckinCheckoutController@townsendTattileFeed');

        Route::get('check-vehicle-enabled-test/{facility_id}/{gate}', 'ParkEngage\WorldportCheckinCheckoutController@checkVehicleEnabled');

        Route::get('diamond-document-type/{id}', 'ParkEngage\AreRegisterController@getDiamondDocumentType');
        Route::get('permit-type/{id}', 'ParkEngage\AreRegisterController@getPermitType');
        Route::get('facility-permit-type/{partner_id}/{facility_id?}', 'FacilityController@getFacilityPermitType');
        // Common Api Section Added Vijay For Both Platform : mobile and Responsive;
        Route::post('pave-price-band', 'ParkEngage\PaveCheckinCheckoutController@getPavePriceBand');

        Route::post('vehicle-details-checkin', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@VehicleDetailsOnCheckin']);

        Route::get('mapco-events_date', 'ParkEngage\MapcoController@mapcoEvntsDates');


        Route::get('services/{is_offline?}', 'ParkEngage\RequestDemoController@getServices');

        Route::get('frontend-services', 'ParkEngage\ServiceController@getFrontendServices');

        Route::get('countries', 'ParkEngage\RequestDemoController@getCountries');

        Route::post('save-request-demo', 'ParkEngage\RequestDemoController@store');

        Route::post('save-contact-us', 'ParkEngage\ContactUsController@store');

        Route::get('contact-us', 'ParkEngage\ContactUsController@index');
        Route::get('contact-us/page/{page}', ['uses' => 'ParkEngage\ContactUsController@index', 'middleware' => 'pagination']);
        Route::get('contact-us/{id}', 'ParkEngage\ContactUsController@getUserDetails');

        Route::post('/trial-registration', 'ParkEngage\PartnerController@store');

        Route::post('/reset-temp-password', 'ParkEngage\PartnerController@resetTempPassword');

        Route::get('configurations', 'ParkEngage\ConfigurationController@configurationDetails');

        Route::post('/multiple-trial-registration', 'ParkEngage\PartnerController@storeMultipleTrial');


        Route::get('partner_mob', 'ParkEngage\PartnerController@partnersList');

        Route::get('send-sms', 'ParkEngage\NewTwilioController@sendSms');
        Route::post('receive-sms', 'ParkEngage\NewTwilioController@receiveSms');
        Route::post('retailer-receive-sms', 'ParkEngage\SubordinateController@receiveRetailerSms');

        Route::get('service-by-slug/{slug}', 'ParkEngage\ServiceController@getServicesBySlug');

        Route::get('/partners-key', ['uses' => 'ParkEngage\PartnerController@getAllPartnersWithKey']);

        Route::get('/partners-key-by-secret/{secret}', ['uses' => 'ParkEngage\PartnerController@getAllPartnersWithKeyBySecret']);

        Route::get('about-us', ['uses' => 'ParkEngage\AboutUsController@getPartnerAboutUs']);

        Route::get('rate-pass/{facility_id}', ['uses' => 'RateController@getFacilityRateWithPass']);

        Route::post('verify-user', 'ParkEngage\PartnerController@verifyEmployeeUser');

        Route::post('send-reservation-sms', 'ReservationController@sendSmsAfterReservation');

        Route::post('check-user-permit', 'ReservationController@checkUserPermit');

        Route::post('all-user-permit', ['uses' => 'ReservationController@allPermitData', 'middleware' => 'pagination']);
        Route::post('all-user-permit/page/{page}', ['uses' => 'ReservationController@allPermitData', 'middleware' => 'pagination']);

        Route::get('partner-monthly-rates/{facility_id}', 'ParkEngage\PartnerController@partnerMonthlyRates');

        Route::post('buy-permit', 'PermitController@buyPermit');

        Route::get('expire-permit-by-license/{license_number}', 'PermitController@expirePermit');

        Route::post('add-commuter', 'PermitController@addCommuter');

        Route::get('partner-permit-rates/{facility_id}', 'ParkEngage\PartnerController@partnerPermitRates');

        Route::post('verify-member-user', 'ParkEngage\PartnerController@verifyMemberUser');

        Route::get('user-passes', ['uses' => 'UserPassesController@index']);


        Route::post('ticket-checkin-checkout', 'ParkEngage\CheckinCheckoutController@ticketCheckinCheckout');

        Route::post('get-parking-gates', 'ParkEngage\CheckinCheckoutController@getAllFacilityGates');

        Route::get('google-wallet', 'ParkEngage\GoogleWalletController@generateEvent');

        Route::post('ticket-session-checkin-checkout', 'ParkEngage\CheckinCheckoutController@ticketSessionCheckinCheckout');

        Route::post('get-session-ticket-payment-details', 'ParkEngage\CheckinCheckoutController@getSessionTicketPaymentDetails');

        Route::post('autogate-days-amount', 'ParkEngage\AutogateApiController@getNoOfDaysRate');

        Route::post('demo-ticket-checkin-checkout', 'ParkEngage\CheckinCheckoutController@demoTicketCheckinCheckout');
        Route::post('demo-ticket-session-checkin-checkout', 'ParkEngage\CheckinCheckoutController@demoTicketSessionCheckinCheckout');

        Route::post('upload-license-plate', 'ParkEngage\AutogateApiController@uploadLicensePlate');

        Route::post('get-device-config', 'ParkEngage\CheckinCheckoutController@getDeviceConfig');

        /*//autogate apis routes
        Route::post('upload-license-plate', 'ParkEngage\AutogateApiController@uploadLicensePlate');

        Route::get('validate-license-plate-checkin/{license_plate}', 'ParkEngage\AutogateApiController@validateLicensePlateCheckin');

        Route::post('mark-citation', 'ParkEngage\AutogateApiController@markCitation');

        Route::get('autogate-citation-list', 'ParkEngage\AutogateApiController@citationList');
        Route::get('autogate-dashboard', 'ParkEngage\AutogateApiController@dashboard');

        Route::post('autogate-autopay-overstay-payment', 'ParkEngage\AutogateApiController@autopayOverstayPayment');
        
        Route::get('autogate-checkout-list', 'ParkEngage\AutogateApiController@checkoutList');

        Route::post('autogate-days-amount', 'ParkEngage\AutogateApiController@getNoOfDaysRate');

        Route::post('autogate-citation-update', 'ParkEngage\AutogateApiController@citationUpdate');
		
		Route::get('autogate-unavailable-vehicle-list', 'ParkEngage\AutogateApiController@unavailableVehicleList');

		Route::post('autogate-unavailable-vehicle-update', 'ParkEngage\AutogateApiController@unavailableVehicleUpdate');  

		Route::post('autogate-reopen-status-update', 'ParkEngage\AutogateApiController@reopenStatusUpdate');  

		Route::get('partner-facility', 'ParkEngage\AutogateApiController@getPartnerFacility');      

		Route::post('partner-validate-device', 'ParkEngage\AutogateApiController@validatePartnerDevice');      */


        Route::get('worldpay-payment-account-response/{user_id}', 'ParkEngage\WorldpayController@paymentAccountResponse');

        Route::get('worldpay-payment-account-autopay-response/{id}', 'ParkEngage\WorldpayController@paymentAccountAutopayResponse');

        Route::get('partner-autopay-response/{id}/{from_date}/{to_date}', 'ParkEngage\WorldpayController@partnerAutopayResponse');

        Route::get('partner-delete-payment-account-response/{id}', 'ParkEngage\WorldpayController@partnerDeletePaymentAccountResponse');

        Route::get('planet-payment', 'ParkEngage\AutogateApiController@planetpaymenttest');

        Route::get('planet-payment-response', 'ParkEngage\AutogateApiController@planetPaymentResponse');
        Route::get('planet-payment-session', 'ParkEngage\AutogateApiController@planetPaymentSession');
        //Api for Frontend configuration 
        Route::post('get-adam-config', 'ParkEngage\AdamController@GetAdamConfig');


        //Mapco Routes 
        Route::post('get-mapco-event-list', 'ParkEngage\MapcoController@index');
        Route::post('get-mapco-event-bundle-pass', 'ParkEngage\MapcoController@getBundlePassEvent');
        Route::post('planet-payment-session', 'ParkEngage\MapcoController@planetPaymentSession');
        Route::post('mapco-payment-success', 'ParkEngage\MapcoController@makePaymentSuccess');
        Route::post('get-mapco-passes', 'ParkEngage\MapcoController@getAllPass');
        Route::get('mapco-send-user-email/{id}', 'ParkEngage\MapcoController@sendUserEmail');
        Route::post('mapco-ticket-checkin-checkout', 'ParkEngage\MapcoController@mapcoTicketCheckinCheckout');
        Route::post('mapco-session-checkin-checkout', 'ParkEngage\MapcoController@mapcoSessionCheckinCheckout');
        Route::post('mapco-driveup-checkin-checkout', 'ParkEngage\MapcoController@mapcoDriveupCheckinCheckout');
        Route::post('save-declined-transactions', 'ParkEngage\MapcoController@saveDeclinedTransaction');

        Route::post('mapco-booking-before-payment', 'ParkEngage\MapcoController@makeBookingBeforePayment');
        Route::post('mapco-booking-after-payment', 'ParkEngage\MapcoController@paymentSuccess');
        Route::post('mapco-update-payment-status', 'ParkEngage\MapcoController@updatePaymentStatus');
        Route::post('planet-payment-session-pci', 'ParkEngage\TownsendApiController@planetPaymentSessionPci');

        Route::post('get-event-date-list', 'ParkEngage\MapcoController@getEventDateList');


        //demo
        Route::post('demo-mapco-ticket-checkin-checkout', 'ParkEngage\MapcoController@demoMapcoTicketCheckinCheckout');
        Route::post('demo-mapco-session-checkin-checkout', 'ParkEngage\MapcoController@demoMapcoSessionCheckinCheckout');
        Route::post('demo-mapco-driveup-checkin-checkout', 'ParkEngage\MapcoController@demoMapcoDriveupCheckinCheckout');

        Route::post('post-success', 'ParkEngage\MapcoController@postSuccess');
        Route::post('post-fail', 'ParkEngage\MapcoController@postFail');

        Route::post('send-otp', 'ParkEngage\MapcoController@sendOtp');
        Route::post('confirm-otp', 'ParkEngage\MapcoController@confirmOtp');

        //atlanta planet new booking flow
        Route::post('atlanta-planet-payment-session', 'ParkEngage\AtlantaReservationController@planetPaymentSession');
        Route::post('atlanta-reservation', 'ParkEngage\AtlantaReservationController@makeAnonReservation');
        Route::post('check-booking-before-payment', 'ParkEngage\AtlantaReservationController@checkBookingBeforePayment');
        Route::get('atlanta-cancel-ticket/{id}', 'ParkEngage\AtlantaReservationController@cancelTicket');

        // AAA Planet Payment call  back API 
        Route::post('aaa-booking-after-payment', 'ParkEngage\AtlantaReservationController@paymentSuccess');
        Route::post('aaa-post-success', 'ParkEngage\AtlantaReservationController@postSuccess');
        Route::post('aaa-post-fail', 'ParkEngage\AtlantaReservationController@postFail');
        // Payment Using Save Card
        Route::post('aaa-payment-by-token', 'ParkEngage\AtlantaReservationController@payment');

        Route::post('customer-portal-planet-payment-session', 'ParkEngage\AtlantaReservationController@customerPoratalPlanetPaymentSession');


        //atlanta new checkin flow

        Route::post('zoo-ticket-checkin-checkout', 'ParkEngage\CheckinCheckoutController@zooTicketCheckinCheckout');
        Route::post('zoo-driveup-checkin-checkout', 'ParkEngage\CheckinCheckoutController@zooTicketDriveupCheckinCheckout');
        Route::post('zoo-ticket-session-checkin-checkout', 'ParkEngage\CheckinCheckoutController@zooTicketSessionCheckinCheckout');
        Route::post('zoo-save-declined-transactions', 'ParkEngage\CheckinCheckoutController@saveDeclinedTransaction');

        Route::post('demo-zoo-ticket-checkin-checkout', 'ParkEngage\CheckinCheckoutController@demoZooTicketCheckinCheckout');
        Route::post('demo-zoo-driveup-checkin-checkout', 'ParkEngage\CheckinCheckoutController@demoZooTicketDriveupCheckinCheckout');
        Route::post('demo-zoo-session-checkin-checkout', 'ParkEngage\CheckinCheckoutController@demoZooTicketSessionCheckinCheckout');

        // ARE Routes
        Route::get('are-partner-permit-rates/{slug?}', 'ParkEngage\AreRegisterController@partnerPermitRates');
        Route::post('are-partner-pro-rate', 'ParkEngage\AreRegisterController@partnerProRateCalculation');
        #DD pims-11714 script prorate test
        Route::post('are-partner-pro-rate-script-test', 'ParkEngage\AreRegisterController@partnerProRateCalculationScript');
        Route::post('pro-rate-script', 'ParkEngage\AreRegisterController@prorateNoServiceScript');
        Route::post('pro-rate-service-script', 'ParkEngage\AreRegisterController@prorateServiceScript');
        Route::post('pro-rate-service-request-script', 'ParkEngage\AreRegisterController@prorateUserRequestServiceScript');

        Route::post('book-permit-are', 'ParkEngage\AreRegisterController@store');
        Route::post('book-pass-are', 'UserPassesController@store');
        Route::get('get-user-facility-pass/{facility_id}', 'RateController@getFacilityUserPass');
        Route::get('gethotelpasses/{facility_id?}', 'RateController@gethotelpasses');

        Route::post('are-send-user-email-checkin', 'ParkEngage\AreRegisterController@sendUserEmailCheckin');

        //Diamond User Permit Request
        Route::post('diamond-user-permit-request', 'ParkEngage\AreRegisterController@createPermitByUser');


        // ARE Planet Payment Routes
        Route::post('are-planet-payment-session', 'ParkEngage\AreRegisterController@planetPaymentSession');
        Route::post('are-booking-before-payment', 'ParkEngage\AreRegisterController@makeBookingBeforePayment');
        Route::post('are-post-success', 'ParkEngage\AreRegisterController@postSuccess');
        Route::post('are-post-fail', 'ParkEngage\AreRegisterController@postFail');
        Route::post('are-booking-after-payment', 'ParkEngage\AreRegisterController@paymentSuccess');
        // Payment by Datacap & planet Both
        // Route::post('permit-booking', 'ParkEngage\AreRegisterController@makePermitBooking');

        Route::post('permit-booking', ['uses' => 'ParkEngage\AreRegisterController@makePermitBooking', 'middleware' => 'verify.client']);

        // Payment by Data trans & planet Both permit renew manual payment
        Route::post('make-permit-renew-payment', 'ParkEngage\AreRegisterController@makeManualPermitRenew');
        // Payment Using Save Card
        Route::post('are-payment', 'ParkEngage\AreRegisterController@payment');
        Route::post('diamond-planet-payment-session', 'ParkEngage\AreRegisterController@diamondPlanetPaymentSession');

        // for user pass with planet payment        
        Route::post('userpass-before-payment', 'UserPassesController@createUserPassBeforePayment');
        Route::post('userpass-planet-payment-session', 'UserPassesController@planetPaymentSession');
        Route::post('userpass-post-success', 'UserPassesController@postSuccess');
        Route::post('userpass-post-fail', 'UserPassesController@postFail');
        Route::post('userpass-after-payment', 'UserPassesController@paymentSuccess');
        Route::post('userpass-payment-failed', 'UserPassesController@paymentFailure');
        // Payment Using Save Card
        Route::post('userpass-payment', 'UserPassesController@payment');

        Route::post('get-facility-event-rate', 'ParkEngage\MapcoController@getFacilityEventStartRate');

        Route::post('device-keep-alive', 'ParkEngage\PartnerController@deviceKeepAlive');

        Route::post('search-ticket-number', ['uses' => 'ParkEngage\PartnerController@searchTicketNumber', 'middleware' => 'pagination']);
        Route::post('send-sms-email', ['uses' => 'ParkEngage\PartnerController@sendSmsEmail']);

        Route::post('wait-list-user-update', 'ParkEngage\WaitingListController@waitListedusers');

        Route::post('townsend-ticket-checkin-checkout', 'ParkEngage\TownsendCheckinCheckoutController@ticketCheckinCheckout');
        Route::post('townsend-driveup-checkin-checkout', 'ParkEngage\TownsendCheckinCheckoutController@ticketDriveupCheckinCheckout');
        Route::post('townsend-ticket-session-checkin-checkout', 'ParkEngage\TownsendCheckinCheckoutController@ticketSessionCheckinCheckout');
        Route::post('townsend-save-declined-transactions', 'ParkEngage\TownsendCheckinCheckoutController@saveDeclinedTransaction');
        Route::post('townsend-datacap-ticket-checkin-checkout', 'ParkEngage\TownsendCheckinCheckoutController@datacapTicketCheckinCheckout');
        Route::post('townsend-datacap-driveup-checkin-checkout', 'ParkEngage\TownsendCheckinCheckoutController@datacapTicketDriveupCheckinCheckout');
        Route::post('townsend-datacap-ticket-session-checkin-checkout', 'ParkEngage\TownsendCheckinCheckoutController@datacapTicketSessionCheckinCheckout');
        Route::post('townsend-lpr-checkin-checkout', 'ParkEngage\TownsendCheckinCheckoutController@lprCheckinCheckout');
        Route::post('townsend-licenseplate-checkin-checkout', 'ParkEngage\TownsendCheckinCheckoutController@parkengageLicensePlateCheckinCheckout');

        Route::post('sign-up', 'ParkEngage\DiamondTransientApiController@signUP');

        Route::post('transient-confirm-checkin', 'ParkEngage\DiamondTransientApiController@TransientConfirmCheckin');
        Route::get('get-facility/{id}', 'ParkEngage\DiamondTransientApiController@getFacility');
        Route::post('transient-make-payment', 'ParkEngage\DiamondTransientApiController@makePayment');
        Route::get('diamond-permit-partner-facility/{slug?}/{business_id?}/{permit_type_id?}', 'ParkEngage\DiamondGateApiController@getPartnerPermitFacility');
        Route::post('citation-make-payment', 'ParkEngage\DiamondTransientApiController@makeCitationPayment');
        Route::post('citation-rate', 'ParkEngage\DiamondTransientApiController@citationRate');

        Route::get('transient-extend-payment-details/{ticket_number}', 'ParkEngage\DiamondTransientApiController@getExtendCheckinPaymentDetails');
        Route::post('transient-make-extend-payment', 'ParkEngage\DiamondTransientApiController@makeExtendPayment');

        //Route::post('parkengage-ticket-checkin-checkout', 'ParkEngage\CustomerCheckinCheckoutController@CustomerTicketCheckinCheckout');

        Route::post('diamond-citation-details', 'ParkEngage\DiamondTransientApiController@citationRate');
        Route::post('demo-diamond-citation-details', 'ParkEngage\DemoDiamondTransientApiController@citationRate');


        // Diamond Planet Payment call  back API 
        Route::post('diamond-citation-after-payment', 'ParkEngage\DiamondTransientApiController@paymentSuccess');
        Route::post('diamond-citation-post-success', 'ParkEngage\DiamondTransientApiController@postSuccess');
        Route::post('diamond-citation-post-fail', 'ParkEngage\DiamondTransientApiController@postFail');
        Route::post('diamond-citation-before-payment', 'ParkEngage\DiamondTransientApiController@updateCitationBeforePayment');
        Route::post('demo-diamond-citation-before-payment', 'ParkEngage\DemoDiamondTransientApiController@updateCitationBeforePayment');

        //for ticket details and download pdf
        Route::post('get-receipt-ticket-details', 'ParkEngage\PartnerController@getReceiptTicketDetails');
        Route::post('get-license-plates', 'ParkEngage\PartnerController@getTicketByLicensePlate');

        Route::post('get-facility-rate', 'ParkEngage\PaveCheckinCheckoutController@getFacilityRate');

        Route::get('pave-extend-payment-details/{ticket_number}', 'ParkEngage\PaveCheckinCheckoutController@getPaveExtendCheckinPaymentDetails');
        Route::post('pave-days-amount', 'ParkEngage\PaveCheckinCheckoutController@getPaveNoOfDaysRate');

        Route::post('pave-make-extend-payment', 'ParkEngage\PaveCheckinCheckoutController@makePaveExtendPayment');
        Route::post('pave-ticket-checkin-checkout', 'ParkEngage\PaveCheckinCheckoutController@paveTicketCheckinCheckout');
        Route::post('pave-driveup-checkin-checkout', 'ParkEngage\PaveCheckinCheckoutController@paveTicketDriveupCheckinCheckout');
        Route::post('pave-ticket-session-checkin-checkout', 'ParkEngage\PaveCheckinCheckoutController@paveTicketSessionCheckinCheckout');
        Route::post('pave-save-declined-transactions', 'ParkEngage\PaveCheckinCheckoutController@saveDeclinedTransaction');
        Route::post('pave-ungated-ticket-session-checkin-checkout', 'ParkEngage\PaveCheckinCheckoutController@paveUngatedCheckinCheckout');

        Route::post('pave-web-get-facility-rate', 'ParkEngage\PaveCheckinCheckoutController@getFacilityWebRate');
        Route::post('pave-make-web-ungated-checkin-payment', 'ParkEngage\PaveCheckinCheckoutController@makeUngatedWebCheckinPayment');

        //Route::post('usm-ticket-checkin-checkout', 'ParkEngage\PaveCheckinCheckoutController@ticketCheckinCheckout');
        Route::post('usm-driveup-checkin-checkout', 'ParkEngage\UsmCheckinCheckoutController@ticketDriveupCheckinCheckout');
        Route::post('usm-ticket-session-checkin-checkout', 'ParkEngage\UsmCheckinCheckoutController@ticketSessionCheckinCheckout');
        Route::post('usm-ungated-ticket-session-checkin-checkout', 'ParkEngage\UsmCheckinCheckoutController@ungatedCheckinCheckout');
        Route::post('usm-ungated-get-current-session',  'ParkEngage\UsmCheckinCheckoutController@getTicketDetailsForExtend');
        Route::post('usm-get-extend-rate-checkin-details', 'ParkEngage\UsmCheckinCheckoutController@getExtendRateCheckinDetails');
        Route::post('usm-make-ungated-extend-payment', 'ParkEngage\UsmCheckinCheckoutController@getUngatedExtendPayment');
        Route::get('usm-get-ticket-extend-list/{ticket_number}', 'ParkEngage\UsmCheckinCheckoutController@getTicketExtendList');
        Route::post('usm-ticket-checkin-checkout', 'ParkEngage\UsmCheckinCheckoutController@ticketCheckinCheckout');

        Route::get('ticket-details/{ticket_number}/{user_id?}/{partner_slug?}', 'ParkEngage\TownsendApiController@getCheckinTicketDetails');
        Route::get('ticket-payment-details/{ticket_number}/{user_id?}/{partner_slug?}', 'ParkEngage\TownsendApiController@AfterCheckinPaymentDetails');
        Route::post('ticket-payment', 'ParkEngage\TownsendApiController@MakeTicketPayment');
        Route::post('scaned-ticket-details', 'ParkEngage\TownsendApiController@ScanedTicketDetails');
        // promocode- gated flow
        Route::post('apply-promocode-gated-ticket', 'ParkEngage\TownsendApiController@updatePromoCodeOnTicket');
        Route::get('pave-end-session/{ticket_number}', 'ParkEngage\PaveCheckinCheckoutController@paveEndSession');
        Route::get('pave-faq-term-condition/{field_name}', 'ParkEngage\PaveCheckinCheckoutController@paveFaq');

        Route::get('get-server-currenttime/{facility_id?}', 'ParkEngage\PaveCheckinCheckoutController@getCurrentTime');
        Route::get('get-states', 'ParkEngage\PaveCheckinCheckoutController@getAllState');
        Route::get('get-ticket-extend-list/{ticket_number}', 'ParkEngage\PaveCheckinCheckoutController@getTicketExtendList');

        Route::get('timezones', 'FacilityController@getTimezones');

        Route::post('apply-promocode-on-ticket', 'ParkEngage\PaveCheckinCheckoutController@updatePromoCodeOnTicket');

        Route::get('get-ticket-details/{ticket_number}', 'ParkEngage\PaveCheckinCheckoutController@getTicketDetails');

        Route::put('update-phone-number-by-ticket', 'ParkEngage\TownsendApiController@updatePhoneNumber');

        Route::get('partners-key-by-id/{id}', ['uses' => 'ParkEngage\PartnerController@getAllPartnersWithKeyBySecret']);
        Route::get('/partner-details-by-slug/{slug}/{facilityslug?}', ['uses' => 'ParkEngage\PartnerController@getAllPartnersDetailsBySlug']);

        Route::post('classic-ticket-checkin-checkout', 'ParkEngage\ClassicCheckinCheckoutController@ticketCheckinCheckout');
        Route::post('classic-driveup-checkin-checkout', 'ParkEngage\ClassicCheckinCheckoutController@ticketDriveupCheckinCheckout');
        Route::post('classic-ticket-session-checkin-checkout', 'ParkEngage\ClassicCheckinCheckoutController@ticketSessionCheckinCheckout');
        Route::post('classic-save-declined-transactions', 'ParkEngage\ClassicCheckinCheckoutController@saveDeclinedTransaction');
        Route::post('classic-datacap-ticket-checkin-checkout', 'ParkEngage\ClassicCheckinCheckoutController@datacapTicketCheckinCheckout');
        Route::post('classic-datacap-driveup-checkin-checkout', 'ParkEngage\ClassicCheckinCheckoutController@datacapTicketDriveupCheckinCheckout');
        Route::post('classic-datacap-ticket-session-checkin-checkout', 'ParkEngage\ClassicCheckinCheckoutController@datacapTicketSessionCheckinCheckout');
        Route::post('classic-lpr-checkin-checkout', 'ParkEngage\ClassicCheckinCheckoutController@lprCheckinCheckout');

        Route::post('clear-license-plate', 'ParkEngage\AutogateApiController@clearLicensePlate');

        //common sms send api
        Route::post('common-send-sms', 'ParkEngage\SmsController@commonSendSms');
        //world port
        Route::post('worldport-ticket-checkin-checkout', 'ParkEngage\WorldportCheckinCheckoutController@ticketCheckinCheckout');
        Route::post('worldport-driveup-checkin-checkout', 'ParkEngage\WorldportCheckinCheckoutController@ticketDriveupCheckinCheckout');
        Route::post('worldport-ticket-session-checkin-checkout', 'ParkEngage\WorldportCheckinCheckoutController@ticketSessionCheckinCheckout');
        Route::post('worldport-save-declined-transactions', 'ParkEngage\WorldportCheckinCheckoutController@saveDeclinedTransaction');
        Route::post('worldport-datacap-ticket-checkin-checkout', 'ParkEngage\WorldportCheckinCheckoutController@datacapTicketCheckinCheckout');
        Route::post('worldport-datacap-driveup-checkin-checkout', 'ParkEngage\WorldportCheckinCheckoutController@datacapTicketDriveupCheckinCheckout');
        Route::post('worldport-datacap-ticket-session-checkin-checkout', 'ParkEngage\WorldportCheckinCheckoutController@datacapTicketSessionCheckinCheckout');
        Route::post('worldport-lpr-checkin-checkout', 'ParkEngage\WorldportCheckinCheckoutController@lprCheckinCheckout');
        Route::post('worldport-licenseplate-open-gate', 'ParkEngage\WorldportCheckinCheckoutController@openGateCheckinCheckout');
        Route::post('help-notification', 'ParkEngage\WorldportCheckinCheckoutController@helpNotification');
        Route::post('clear-ticket-transaction-status', 'ParkEngage\PartnerController@clearTicketTransactionStatus');
        Route::get('attendant-user-type/{partner_id}', 'ParkEngage\PartnerController@getAttendantType');


        //colonial
        Route::post('colonial-ticket-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@ticketCheckinCheckout');
        Route::post('colonial-driveup-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@ticketDriveupCheckinCheckout');
        Route::post('colonial-ticket-session-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@ticketSessionCheckinCheckout');
        Route::post('colonial-save-declined-transactions', 'ParkEngage\ColonialCheckinCheckoutController@saveDeclinedTransaction');
        Route::post('colonial-datacap-ticket-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@datacapTicketCheckinCheckout');
        Route::post('colonial-datacap-driveup-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@datacapTicketDriveupCheckinCheckout');
        Route::post('colonial-datacap-ticket-session-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@datacapTicketSessionCheckinCheckout');
        Route::post('colonial-lpr-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@lprCheckinCheckout');
        Route::post('colonial-licenseplate-open-gate', 'ParkEngage\ColonialCheckinCheckoutController@openGateCheckinCheckout');

        Route::post('pe-ticket-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@ticketCheckinCheckout');
        Route::post('pe-driveup-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@ticketDriveupCheckinCheckout');
        Route::post('pe-ticket-session-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@ticketSessionCheckinCheckout');
        Route::post('pe-save-declined-transactions', 'ParkEngage\ColonialCheckinCheckoutController@saveDeclinedTransaction');
        Route::post('pe-datacap-ticket-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@datacapTicketCheckinCheckout');
        Route::post('pe-datacap-driveup-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@datacapTicketDriveupCheckinCheckout');
        Route::post('pe-datacap-ticket-session-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@datacapTicketSessionCheckinCheckout');
        Route::post('pe-lpr-checkin-checkout', 'ParkEngage\ColonialCheckinCheckoutController@lprCheckinCheckout');
        Route::post('pe-licenseplate-open-gate', 'ParkEngage\ColonialCheckinCheckoutController@openGateCheckinCheckout');

        Route::post('validate-reservation',  'ParkEngage\PaveCheckinCheckoutController@validateReservation');

        Route::post('save-lpr-data', 'ParkEngage\WoodmanCheckinCheckoutController@uploadLicensePlate');

        // Alka :: Enforcement App
        Route::post('enforcement-ungated-checkin-payment',  'ParkEngage\PaveCheckinCheckoutController@makeUngatedWebCheckinPayment');

        //breeze tablet APIs
        Route::post('facility-all-gates', 'ParkEngage\GateController@getFacilityGates');

        Route::get('get-ticket-amount-due/{ticket_number}', 'ParkEngage\PartnerController@getTicketAmountDue');
        // Alka, PIMS-10649
        Route::post('permit-payment', 'ParkEngage\AreRegisterController@makeAdminPaymntOnBehalfCustomer');

        //vikrant
        Route::post('get-offline-ticket-details', 'ParkEngage\OfflineTicketController@getTicketDetails');

        // Vijay : [G-PAY, A-PAY]
        Route::post('payment-verify-before-payment', 'ParkEngage\UserController@paymentVerifyBeforePayment');

        //
        Route::get('get-user-ticket-details/{user_id}', 'ParkEngage\PaveCheckinCheckoutController@getUserTicketDetails');

        Route::post('send-pass-sms-email', ['uses' => 'UserPassesController@sendEmailSms']);
        //Ram UPBL-87,
        Route::post('parkengage-survision-feed', 'ParkEngage\SurvisionCheckinCheckoutController@parkengageSurvisionFeed');

        //PIMS-14211
        Route::post('get-license-start-time', 'ParkEngage\LPRController@getLicenseTimes');
    }
);

Route::group(
    ['middleware' => ['apiresponse', 'oauth', 'fetch-user', 'checkpermission', 'timezone']],
    function () {

        //    ['middleware' => ['apiresponse','checkpermission']], function () {
        Route::get('membership-plan', ['uses' => 'ParkEngage\MembershipPlanController@index', 'middleware' => 'pagination']);
        Route::get('membership-plan/page/{page}', ['uses' => 'ParkEngage\MembershipPlanController@index', 'middleware' => 'pagination']);
        Route::post('save-membership-plan', 'ParkEngage\MembershipPlanController@store');
        Route::post('update-membership-plan', 'ParkEngage\MembershipPlanController@update');
        Route::get('membership-plan-details/{id}', 'ParkEngage\MembershipPlanController@membershipPlanDetails');

        Route::delete('delete-membership-plan/{id}', 'ParkEngage\MembershipPlanController@delete');

        Route::get('request-demo', 'ParkEngage\RequestDemoController@getRequestDemoDetails');
        Route::get('request-demo/page/{page}', ['uses' => 'ParkEngage\RequestDemoController@getRequestDemoDetails', 'middleware' => 'pagination']);
        Route::get('get-request-demo/{id}', 'ParkEngage\RequestDemoController@getRequestDemoDetailsById');

        Route::get('permission', 'ParkEngage\PermissionController@index');
        Route::post('save-permission', 'ParkEngage\PermissionController@store');
        Route::post('update-permission', 'ParkEngage\PermissionController@update');

        Route::get('/partners', ['uses' => 'ParkEngage\PartnerController@getAllPartners']);


        Route::get('service', 'ParkEngage\ServiceController@index');
        Route::get('/service/page/{page}', ['uses' => 'ParkEngage\ServiceController@index', 'middleware' => 'pagination']);
        Route::post('save-service', 'ParkEngage\ServiceController@store');
        Route::post('update-service', 'ParkEngage\ServiceController@update');
        Route::get('service-details/{id}', 'ParkEngage\ServiceController@serviceDetails');
        Route::delete('delete-service/{id}', 'ParkEngage\ServiceController@delete');
        Route::delete('delete-service-image/{id}', 'ParkEngage\ServiceController@destroyServiceImage');
        Route::delete('delete-service-details-image/{id}', 'ParkEngage\ServiceController@destroyServiceDetailsImage');

        Route::get('newsletters', 'NewsletterController@index');
        Route::get('newsletters/page/{page}', ['uses' => 'NewsletterController@index', 'middleware' => 'pagination']);
    }
);
// Townsend pdf Download 
Route::group(
    ['middleware' => ['oauth', 'fetch-user', 'timezone']],
    function () {


        // New API : Vijay : 08-02-2024 
        Route::post('manage-checkin-checkout-download-pdf', 'ParkEngage\ManageCheckinCheckoutController@checkinCheckoutDownloadPdf');
        Route::post('manage-checkin-checkout-download-excel', 'ParkEngage\ManageCheckinCheckoutController@checkinCheckoutDownloadExcel');
        Route::get('are-permit-downloadpdf/page/{page}', 'ParkEngage\AreRegisterController@getPermitList');
        // Route::get('daily-report-pdf', 'ParkEngage\TownsendApiController@getDailyReportPdfFormat');
        // Route::get('cashier-shift-report', 'ParkEngage\TownsendApiController@cashierShiftReport');
        // New Routes
        Route::get('daily-report-pdf', 'ParkEngage\ReportParkEngage@getDailyReportPdfFormat');
        Route::get('cashier-shift-report', 'ParkEngage\ReportParkEngage@cashierShiftReport');
        Route::get('revenue-report-summary', 'ParkEngage\ReportParkEngage@ravpassSummaryExcel');
        Route::get('overnight-vehicle-report', 'ParkEngage\ReportParkEngage@RavPassovernightExcelReport');

        Route::get('test-cashier-shift-report', 'ParkEngage\TestTownsendApiController@cashierShiftReport');
        Route::get('open-report-excel/page/{page}', ['uses' => 'ParkEngage\TownsendApiController@openExcel', 'middleware' => 'pagination']);

        Route::get('revenue-report-summary', 'ParkEngage\ReportParkEngage@ravpassSummaryExcel');
        Route::get('overnight-vehicle-report', 'ParkEngage\ReportParkEngage@RavPassovernightExcelReport');
        Route::get('revenue-report-accrual', 'ParkEngage\ReportParkEngage@ravpass_accrual_basis_excel');
        Route::get('revenue-report-accrual-api', 'ParkEngage\ReportParkEngage@ravpass_accrual_api');
        Route::get('promocode-usage-report', 'ParkEngage\ReportParkEngage@promocodeDownloadReport');
        Route::get('bank-deposit-reconciliation', 'ParkEngage\ReportParkEngage@bankDepositReconciliation');
        Route::get('bank-deposit-reconciliation-excel', 'ParkEngage\ReportParkEngage@bankDepositReconciliationExcel');
        Route::get('vehicle-report-permit', 'ParkEngage\ReportParkEngage@usmVehicalReport');
        Route::get('carpool-holder-report', 'ParkEngage\ReportParkEngage@CarpoolHoldersReport');

        // Attendent Shift Report : 27-05-2024
        Route::get('attendant-shift-report-excel', 'ParkEngage\ReportParkEngage@attendentShiftReportExcel');
        Route::get('attendant-shift-report-pdf', 'ParkEngage\ReportParkEngage@attendentShiftReportPdf');

        Route::get('shift-report', 'ParkEngage\ReportParkEngage@attendentShiftReportExcel');
        // Route::get('week-wise-transient', 'ParkEngage\ReportParkEngage@weekWiseTransient');
        Route::get('week-wise-transient-report', 'ParkEngage\ReportParkEngage@weekWiseTransient');

        //    ['middleware' => ['apiresponse','checkpermission']], function () {
        // Route::post('atlanta-checkin-checkout-download-pdf', 'ParkEngage\PartnerController@atlantaCheckinCheckoutDownloadPdf');
        // Route::post('atlanta-checkin-checkout-download-excel', 'ParkEngage\PartnerController@atlantaCheckinCheckoutDownloadExcel');
        Route::post('atlanta-checkin-checkout-download-pdf', 'ParkEngage\PartnerController@checkinCheckoutDownloadPdf');
        Route::post('atlanta-checkin-checkout-download-excel', 'ParkEngage\PartnerController@checkinCheckoutDownloadExcel');

        // Deployed : 03-04-204 : Vijay : Scanned Vehicles for Wailuku
        Route::get('scanned-vehicle-report-execel', ['uses' => 'ParkEngage\ReportParkEngage@scannedVehiclesExcelReport', 'middleware' => 'apiresponse']);


        Route::post('permit-checkin-checkout-download-pdf', 'ParkEngage\PartnerController@diamondCheckinCheckoutDownloadPdf');
        Route::post('permit-checkin-checkout-download-excel', 'ParkEngage\PartnerController@diamondCheckinCheckoutDownloadExcel');

        Route::post('checkin-checkout-download-pdf', 'ParkEngage\MapcoController@checkinCheckoutDownloadPdf');
        Route::post('checkin-checkout-download-excel', 'ParkEngage\MapcoController@checkinCheckoutDownloadExcel');
        Route::get('partner-invoice/pdf/{id}', 'ParkEngage\MembershipPaymentController@getInvoicePdf');

        Route::get('partner-invoice/jpg/{id}', 'ParkEngage\MembershipPaymentController@getInvoiceJpg');

        Route::get('get-billing-invoice', 'ParkEngage\PartnerCheckinCheckoutController@getInvoicePdf');

        Route::get('get-autogate-billing-invoice', 'ParkEngage\PartnerCheckinCheckoutController@getAutogateInvoicePdf');

        Route::get('mapco-booking-list-download', 'ParkEngage\MapcoController@getBookingListDownload');

        Route::post('partner-booking-list-download-excel', 'ParkEngage\PartnerController@getBookingListDownloadExcel');
        Route::get('partner-booking-list-download-pdf', 'ParkEngage\PartnerController@getBookingListDownload');

        Route::get('get-mapco-billing-invoice', 'ParkEngage\MapcoController@getInvoicePdf');
    }
);

#KT: 22-11-24
Route::group(
    ['middleware' => ['oauth', 'fetch-user', 'timezone']],
    function () {
        Route::get('partner-user-passes/download', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@downloadUserPassPdf', 'middleware' => 'oauth']);

        #KT: 20-12-24
        #To download all vehicles (Whitelisted,Blacklisted,BOLO)
        Route::get('vehicles/download', ['uses' => 'ParkEngage\DiamondBlacklistController@downloadVehiclePdf', 'middleware' => 'oauth']);
    }
);

Route::group(
    ['middleware' => ['oauth', 'fetch-user', 'timezone']],
    function () {
        Route::get('get-tickets-list-download-pdf', 'ParkEngage\PaveCheckinCheckoutController@getTicketListDownload');
    }
);

Route::group(
    ['middleware' => ['apiresponse', 'oauth', 'fetch-user', 'timezone']],
    function () {
        Route::get('get-total-suboridnate-list', 'ParkEngage\PartnerController@getsubordinateListData');
        // DD :11-march-2025 run city word permit cron
        Route::get('/third-party-permit-upload', function (Request $request) {
            $exitCode = Artisan::call('import:permit-bulk-upload', [
                'partner_id' => $request->input('partner_id')
            ]);

            return response()->json([
                'message' => 'Command executed successfully!',
                'output' => Artisan::output(),
                'exit_code' => $exitCode
            ]);
        });

        Route::post('promo-list',   'ParkEngage\ManageCheckinCheckoutController@getUsePromoCodeListing');
        // Loeksh :05-AUg-2024 => to put in middleware
        Route::post('mapco-booking-list-download-excel', 'ParkEngage\MapcoController@getBookingListDownloadExcel');

        Route::post('get-permit-type-services', 'ParkEngage\AreRegisterController@getPermitlistServicesforFilter');
        Route::post('get-permit-services-admin', 'ParkEngage\AreRegisterController@getPermitServicesAdmin');
        Route::post('get-permit-rate-by-email-admin', 'ParkEngage\AreRegisterController@getPermitRateByEmailAdmin');
        Route::get('get-permit-categories-admin', 'ParkEngage\AreRegisterController@getPermitCategoriesAdmin');

        // Vijay : To Manage All Checkin checkout Request from same controller along with pdf and excel features 
        Route::get('manage-checkin-checkout', 'ParkEngage\ManageCheckinCheckoutController@getCheckinCheckoutList');
        Route::get('manage-checkin-checkout/page/{page}', ['uses' => 'ParkEngage\ManageCheckinCheckoutController@getCheckinCheckoutList', 'middleware' => 'pagination']);

        Route::get('get-vehicle', 'ParkEngage\VehicleController@getVehicle');
        Route::post('add-vehicles', 'ParkEngage\VehicleController@addVehicle');
        Route::post('edit-vehicle', 'ParkEngage\VehicleController@editVehicle');
        Route::post('delete-vehicle', 'ParkEngage\VehicleController@deleteVehicle');
        Route::get('get-primary-vehicle', 'ParkEngage\VehicleController@getDefaultVehicle');
        // Alka, Date::18 Sept 2024
        Route::put('update-vehicle-details', 'ParkEngage\DiamondUserProfileController@updateVehicleDetails');

        // Vehicle Add for All: Lokesh : 03/Oct/2024
        Route::post('add-vehicle', 'ParkEngage\VehicleController@addVehicleROC');


        Route::get('user-data', 'ParkEngage\UserController@getUserProfileData');
        Route::post('add-user-profile', 'ParkEngage\UserController@updateUserProfile');
        Route::get('get-user-profile', 'ParkEngage\UserController@getUserProfileData');
        Route::get('get-profile-dashboard', 'ParkEngage\UserController@getProfileDashboard');
        Route::post('update-device', 'ParkEngage\UserController@updateDevice');
        Route::post('change-password', 'ParkEngage\UserController@changePassword');

        Route::post('customer-checkin', 'ParkEngage\CustomerCheckinCheckoutController@customerCheckin');
        Route::post('customer-checkout', 'ParkEngage\CustomerCheckinCheckoutController@customerCheckout');
        Route::get('get-payments-details-on-checkout', 'ParkEngage\CustomerCheckinCheckoutController@customerPaymentsDetailsOnCheckout');
        Route::get('list-payment-methods', 'ParkEngage\MembershipPaymentController@listPaymentMethods');
        Route::post('checkout-pay', 'ParkEngage\CustomerCheckinCheckoutController@makePayment');
        Route::get('booking-history', 'ParkEngage\CustomerCheckinCheckoutController@customerBookingHistory');
        Route::get('test-checkin', 'ParkEngage\CustomerCheckinCheckoutController@testCheckin');
        Route::post('parkengage-ticket-checkin-checkout', 'ParkEngage\CustomerCheckinCheckoutController@CustomerTicketCheckinCheckout');

        Route::post('/create-user', 'ParkEngage\PartnerController@registerUser');
        Route::get('/user/page/{page}', ['uses' => 'UserController@index', 'middleware' => 'pagination']);
        Route::get('/user/{user_id}', 'UserController@show');
        Route::post('update-partner-membership', 'ParkEngage\PartnerController@updatePartnerMembership');
        Route::delete('/delete-user/{id}', 'ParkEngage\PartnerController@delete');
        Route::post('/update-user', 'ParkEngage\PartnerController@updateUser');
        Route::get('/user-membership-details', 'ParkEngage\PartnerController@userMembershipDetails');
        Route::get('/sub-partner-details/{id}', 'ParkEngage\PartnerController@subPartnerDetails');
        // Remove this api end point as hemant confirm this is not in use at FE - 19-11-2024
        //    Route::get('facility-list',  ['uses' => 'FacilityController@indexFacilityList', 'middleware' => 'pagination']);
        //    Route::get('facility-list/page/{page}', ['uses' => 'FacilityController@indexFacilityList', 'middleware' => 'pagination']);

        Route::get('dashboard', 'ParkEngage\DashboardController@index');

        Route::post('/membership-payment', 'ParkEngage\PartnerController@makeMembershipPlan');

        //Route::get('membership-plan-details/{id}', 'ParkEngage\MembershipPlanController@membershipPlanDetails');
        Route::get('get-menu-tab/{id}/{flag?}', 'ParkEngage\MembershipPlanController@getTabMenu');
        Route::get('user-membership-plan-details/{id}/{payment_id?}', 'ParkEngage\PartnerController@userMembershipDetailByPlan');

        Route::get('partner-payment-history', 'ParkEngage\MembershipPaymentController@index');
        Route::get('partner-payment-history/page/{page}', ['uses' => 'ParkEngage\MembershipPaymentController@index', 'middleware' => 'pagination']);

        Route::get('get-total-revenue', 'ParkEngage\PartnerCheckinCheckoutController@getTotalRevenue');
        Route::get('get-total-revenue/page/{page}', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@getTotalRevenue', 'middleware' => 'pagination']);
        Route::get('/partner-user/page/{page}', ['uses' => 'ParkEngage\PartnerController@getPartnerUsers', 'middleware' => 'pagination']);

        //Route::get('partner-invoice/pdf/{id}', 'ParkEngage\MembershipPaymentController@getInvoicePdf');

        Route::post('/update-profile', 'UserController@updateProfile');
        Route::post('/update-password', 'ParkEngage\PartnerController@changePassword');
        Route::put('/update-profile-password', 'UserController@updateProfilePassword');

        Route::post('/update-user-password', 'ParkEngage\PartnerController@updatePassword');

        Route::post('membership-payment-method', 'ParkEngage\MembershipPaymentController@addPaymentMethod');
        Route::delete('delete-membership-payment-method/{payment_profile_id}', 'ParkEngage\MembershipPaymentController@deletePaymentMethod');
        Route::get('list-payment-methods', 'ParkEngage\MembershipPaymentController@listPaymentMethods');

        Route::get('/get-user-profile', 'ParkEngage\PartnerController@getUserProfile');

        Route::post('/cancel-membership', 'ParkEngage\PartnerController@cancelMembership');

        Route::post('/brand-settings', 'ParkEngage\PartnerController@brandSettings');

        Route::get('check-partner-facility-count',  'FacilityController@checkPartnerFacilityCount');


        Route::get('partner-facility-entry-gates/{facility_id}', 'ParkEngage\PartnerCheckinCheckoutController@getFacilityEntryGates');

        Route::post('partner-confirm-checkin', 'ParkEngage\PartnerCheckinCheckoutController@confirmCheckin');

        Route::get('partner-checkin-checkout-list', 'ParkEngage\PartnerCheckinCheckoutController@getCheckinCheckoutList');

        Route::get('partner-checkin-checkout-list/page/{page}', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@getCheckinCheckoutList', 'middleware' => 'pagination']);

        Route::get('partner-checkin-checkout-details/{id}', 'ParkEngage\PartnerCheckinCheckoutController@getCheckinCheckoutDetails');

        Route::get('partner-permit-checkin-checkout-details/{id}', 'ParkEngage\PartnerCheckinCheckoutController@getPermitCheckinCheckoutDetails');


        Route::get('partner-checkin-payment-details/{id}', 'ParkEngage\PartnerCheckinCheckoutController@getCheckinPaymentDetails');

        Route::post('partner-make-payment', 'ParkEngage\PartnerCheckinCheckoutController@makePayment');

        Route::post('partner-confirm-checkout', 'ParkEngage\PartnerCheckinCheckoutController@confirmCheckout');

        Route::get('partner-facility-exit-gates/{facility_id}', 'ParkEngage\PartnerCheckinCheckoutController@getFacilityExitGates');

        Route::get('partner-overstay-details/{id}', 'ParkEngage\PartnerCheckinCheckoutController@getoverstayDetails');

        Route::post('partner-make-overstay-payment', 'ParkEngage\PartnerCheckinCheckoutController@makeOverstayPayment');

        Route::get('partner-revenue-details',  'ParkEngage\PartnerCheckinCheckoutController@getRevenueCount');

        Route::get('partner-user-passes',  'ParkEngage\PartnerCheckinCheckoutController@getUserPasses');

        Route::get('partner-user-passes/page/{page}', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@getUserPasses', 'middleware' => 'pagination']);

        Route::post('partner-user-passes/email',  'UserPassesController@sendExcelEmail');

        Route::get('partner-downloadrevenueexcel', 'ParkEngage\PartnerCheckinCheckoutController@downloadRevenueExcel');

        Route::post('revenue-payment-history', 'ParkEngage\PartnerCheckinCheckoutController@saveRevenuePaymentHistory');

        Route::get('get-billing-history', 'ParkEngage\PartnerCheckinCheckoutController@getBillingHistory');

        Route::get('get-booking-list', 'ReservationController@getBookingList');
        Route::get('get-booking-list/page/{page}', ['uses' => 'ReservationController@getBookingList', 'middleware' => 'pagination']);

        Route::post('admin-confirm-checkin', 'ParkEngage\PartnerCheckinCheckoutController@confirmAdminCheckin');

        Route::get('partner-driveup-checkin-checkout-list', 'ParkEngage\PartnerCheckinCheckoutController@getDriveUpCheckinCheckoutList');

        Route::get('partner-driveup-checkin-checkout-list/page/{page}', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@getDriveUpCheckinCheckoutList', 'middleware' => 'pagination']);

        Route::get('get-partner-permit-checkin-checkout-list', 'ParkEngage\PartnerCheckinCheckoutController@getPermitCheckinCheckoutList');

        Route::get('get-partner-permit-checkin-checkout-list/page/{page}', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@getPermitCheckinCheckoutList', 'middleware' => 'pagination']);

        Route::get('partner-citation-list', 'ParkEngage\PartnerCheckinCheckoutController@getcitationList');

        Route::get('partner-citation-list/page/{page}', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@getcitationList', 'middleware' => 'pagination']);


        Route::get('autogate-get-total-revenue', 'ParkEngage\PartnerCheckinCheckoutController@getAutogateTotalRevenue');
        Route::get('autogate-get-total-revenue/page/{page}', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@getAutogateTotalRevenue', 'middleware' => 'pagination']);

        Route::get('partner-autogate-revenue-details',  'ParkEngage\PartnerCheckinCheckoutController@getAutogateRevenueCount');


        Route::get('partner-subordinate-list', 'ParkEngage\PartnerController@getsubordinateList');
        Route::get('partner-subordinate-list/page/{page}', ['uses' => 'ParkEngage\PartnerController@getsubordinateList', 'middleware' => 'pagination']);
        Route::post('email-permitlist-excel', 'ParkEngage\AreRegisterController@sendPermitListExcelInEmail');
        #07-08-2024 dushyant PIMS-10032
        Route::get('partner-subordinate-all-list', ['uses' => 'ParkEngage\PartnerController@getsubordinateAllList', 'middleware' => 'pagination']);


        Route::get('partner-rm-list', 'ParkEngage\PartnerController@getRmList');


        Route::get('partner-rm-list/page/{page}', ['uses' => 'ParkEngage\PartnerController@getRmList', 'middleware' => 'pagination']);

        //autogate apis routes

        Route::post('autogate-autopay-overstay-payment', 'ParkEngage\AutogateApiController@autopayOverstayPayment');

        /*Route::post('upload-license-plate', 'ParkEngage\AutogateApiController@uploadLicensePlate');*/

        Route::get('validate-license-plate-checkin/{license_plate}', 'ParkEngage\AutogateApiController@validateLicensePlateCheckin');

        Route::post('mark-citation', 'ParkEngage\AutogateApiController@markCitation');

        Route::get('autogate-citation-list', 'ParkEngage\AutogateApiController@citationList');
        Route::get('autogate-dashboard', 'ParkEngage\AutogateApiController@dashboard');


        Route::get('autogate-checkout-list', 'ParkEngage\AutogateApiController@checkoutList');

        /*Route::post('autogate-days-amount', 'ParkEngage\AutogateApiController@getNoOfDaysRate');*/

        Route::post('autogate-citation-update', 'ParkEngage\AutogateApiController@citationUpdate');

        Route::get('autogate-unavailable-vehicle-list', 'ParkEngage\AutogateApiController@unavailableVehicleList');

        Route::post('autogate-unavailable-vehicle-update', 'ParkEngage\AutogateApiController@unavailableVehicleUpdate');

        Route::post('autogate-reopen-status-update', 'ParkEngage\AutogateApiController@reopenStatusUpdate');

        Route::get('partner-facility', 'ParkEngage\AutogateApiController@getPartnerFacility');

        Route::post('partner-validate-device', 'ParkEngage\AutogateApiController@validatePartnerDevice');

        Route::get('autogate-today-closed-vehicle-list', 'ParkEngage\AutogateApiController@todayClosedVehicleList');

        Route::get('autogate-autopay-payment-status/{id}', 'ParkEngage\AutogateApiController@getAutopayPaymentStatus');


        Route::get('partner-add-payment-account', 'ParkEngage\PartnerController@getAddPaymentAccount');
        Route::get('partner-response-payment-account', 'ParkEngage\PartnerController@responsePaymentAccount');

        Route::get('partner-payment-account-list', 'ParkEngage\PartnerController@paymentAccountList');

        Route::get('partner-delete-payment-account/{id}', 'ParkEngage\PartnerController@deletePaymentAccount');

        Route::get('partner-payment-account/{id}', 'ParkEngage\PartnerController@getpaymentAccount');

        Route::get('partner-facility-list/{id?}', 'ParkEngage\PartnerController@getPartnerFacility');

        // parking Device
        Route::get('pos', 'ParkEngage\PosController@index');
        Route::post('pos', 'ParkEngage\PosController@store');
        Route::get('pos/page/{page}', ['uses' => 'ParkEngage\PosController@index', 'middleware' => 'pagination']);
        Route::put('pos', 'ParkEngage\PosController@update');
        Route::get('pos/{id}', 'ParkEngage\PosController@show');
        Route::delete('pos/{id}', 'ParkEngage\PosController@destroy');


        Route::get('pos-gate-name/{id}', 'ParkEngage\PosController@getFacilityEntryGates');
        Route::get('get-facility-gates', 'ParkEngage\PosController@getFacilityGate');

        // Gate Controller     
        Route::get('gate', 'ParkEngage\GateController@index');
        Route::post('gate', 'ParkEngage\GateController@store');
        Route::get('gate/page/{page}', ['uses' => 'ParkEngage\GateController@index', 'middleware' => 'pagination']);
        Route::put('gate', 'ParkEngage\GateController@update');
        Route::get('gate/{id}', 'ParkEngage\GateController@show');
        Route::delete('gate/{id}', 'ParkEngage\GateController@destroy');

        // Cruise Controller     
        Route::get('cruiseList/{facility_id}', 'ParkEngage\CruiseController@cruiseListAdmin');
        Route::get('cruise-list', 'ParkEngage\CruiseController@index');
        Route::post('cruise', 'ParkEngage\CruiseController@store');
        Route::get('cruise-list/page/{page}', ['uses' => 'ParkEngage\CruiseController@index', 'middleware' => 'pagination']);
        Route::put('cruise', 'ParkEngage\CruiseController@update');
        Route::get('show-cruise/{id}', 'ParkEngage\CruiseController@show');
        Route::delete('delete-cruise/{id}', 'ParkEngage\CruiseController@destroy');
        Route::get('get-sample-cruise-excel', 'ParkEngage\CruiseController@downloadSampleCruiseExcelFile');
        Route::post('import-cruise-excel', 'ParkEngage\CruiseController@importCruiseExcelFile');

        Route::post('service-permissions', 'ParkEngage\MembershipPlanController@getServicePermissions');
        Route::get('customer-permissions', 'ParkEngage\MembershipPlanController@getCustomerPermissions');
        Route::get('adam', 'ParkEngage\AdamController@index');
        Route::post('adam', 'ParkEngage\AdamController@store');
        Route::put('adam', 'ParkEngage\AdamController@update');
        Route::get('adam/{id}', 'ParkEngage\AdamController@show');
        Route::delete('adam/{id}', 'ParkEngage\AdamController@destroy');
        Route::get('adam/page/{page}', ['uses' => 'ParkEngage\AdamController@index', 'middleware' => 'pagination']);

        Route::get('parking-device-list', 'ParkEngage\PosController@getParkingDeviceType');

        //mapco routes
        Route::get('mapco-cancel-booking/{id}', 'ParkEngage\MapcoController@cancelBooking');
        Route::get('refund-amount/{id}', 'ParkEngage\MapcoController@refundAmount');

        Route::get('mapco-get-total-revenue', 'ParkEngage\MapcoController@getTotalRevenue');
        Route::get('mapco-get-total-revenue/page/{page}', ['uses' => 'ParkEngage\MapcoController@getTotalRevenue', 'middleware' => 'pagination']);
        Route::get('mapco-revenue-details',  'ParkEngage\MapcoController@getRevenueCount');
        Route::get('mapco-downloadrevenueexcel', 'ParkEngage\MapcoController@downloadRevenueExcel');

        Route::get('mapco-booking-usage-details/{id}', 'ParkEngage\MapcoController@getBookingUsageDetails');

        Route::get('event-booking-details/{id}', 'ParkEngage\MapcoController@getBookingUsageDetails');

        Route::get('partner-config-details/{id}', 'ParkEngage\PartnerController@getPartnerConfigDetails');


        Route::get('vangaurd-revpass-report', 'ParkEngage\VanguardController@getRportUrl');

        // Event Cancel API
        Route::get('reservation-cancel/{id}', 'ParkEngage\PartnerController@cancelBooking');
        Route::get('reservation-cancel-hl/{id}', 'ParkEngage\PartnerController@cancelBookingHL');
        Route::post('reservation-refund', 'ParkEngage\PartnerController@refundBooking');
        Route::post('refund-transient-payment', 'ParkEngage\PaveCheckinCheckoutController@refundTransientBooking');

        // Diamond Routes

        Route::get('get-wait-list-users', 'ParkEngage\WaitingListController@getWaitListedUsers');
        Route::get('get-wait-list-users/page/{page}', ['uses' => 'ParkEngage\WaitingListController@getWaitListedUsers', 'middleware' => 'pagination']);
        Route::post('delete-wait-listed-user', 'ParkEngage\WaitingListController@removeUser');


        Route::post('pos-gate-list', 'ParkEngage\PosController@getFacilitiesEntryGates');

        Route::post('pos-gate-list-test', 'ParkEngage\PosController@getFacilitiEntryGate');

        Route::get('get-user-all-permits', 'ParkEngage\AreRegisterController@getUserPermitList');
        Route::get('cancel-permit-by-user/{id}', 'ParkEngage\AreRegisterController@cancelPermitByUser');
        Route::get('cancel-permit-by-user-new/{id}', 'ParkEngage\AreRegisterController@cancelPermitByUserNew');

        #08-08-2024 dushyant PIMS-10032
        Route::get('get-subordinate-permit-allocations/{business_id}', 'ParkEngage\AreRegisterController@getSubordinatePermitAllocation');
        Route::post('subordinate-bundle-payment', 'ParkEngage\AreRegisterController@subordinate_bundle_payment');

        #26-08-2024 dushyant PIMS-10391
        Route::post('add-user-permit-service', 'ParkEngage\AreRegisterController@addUserPermitServices');


        // Diamond User Profile
        Route::get('diamond-get-user-by-id', 'ParkEngage\DiamondUserProfileController@getUserProfileById');
        Route::get('get-user-booking', 'ParkEngage\DiamondUserProfileController@getUserBooking');
        Route::get('get-user-booking/page/{page}', ['uses' => 'ParkEngage\DiamondUserProfileController@getUserBooking', 'middleware' => 'pagination']);
        Route::post('diamond-update-profile', 'ParkEngage\DiamondUserProfileController@updateUserProfile');
        // Route::post('add-vehicle', 'ParkEngage\DiamondUserProfileController@addVehicle');
        Route::post('update-vehicle', 'ParkEngage\DiamondUserProfileController@updateVehicle');
        Route::get('get-vehicle-list/page/{page}', 'ParkEngage\DiamondUserProfileController@getVehicleList');
        Route::get('get-vehicle-Byid/{id}', 'ParkEngage\DiamondUserProfileController@getVehicleByid');
        Route::delete('delete-vehicle/{id}', 'ParkEngage\DiamondUserProfileController@deleteVehicle');

        Route::get('get-partner-user-passes', ['uses' => 'UserPassesController@getUserPassByUserid', 'middleware' => 'pagination']);

        Route::get('staging-partner-permit-rates-admin', 'ParkEngage\AreRegisterController@partnerPermitRatesAdmin');
        Route::get('partner-permit-rates-new', 'ParkEngage\AreRegisterController@partnerPermitRate');
        Route::post('staging-partner-pro-rate-admin', 'ParkEngage\AreRegisterController@partnerProRateCalculationAdmin');
        Route::post('book-permit-admin', 'ParkEngage\AreRegisterController@createPermitByAdmin');
        #pims-13916 #DD
        Route::post('update-permit-admin', 'ParkEngage\AreRegisterController@updatePermitByAdmin');
        Route::post('bulk-book-permit-admin', 'ParkEngage\AreRegisterController@getpermitImportData');
        Route::post('blacklist-vehicle-data-import', 'ParkEngage\DiamondBlacklistController@getBlacklistVehicleImportData');
        Route::get('get_users_list', 'UserController@getUserList');
        Route::get('get_facility_pass/{facility_id}', 'RateController@getFacilityPass');
        Route::post('add-partner-user-passes', 'UserPassesController@createUserPassByAdmin');
        Route::post('add-user-passes-by-clerk', 'UserPassesController@createHotelPass');
        Route::post('edit-user-passes-by-clerk', 'UserPassesController@editHotelPass');
        Route::post('extend-pass', 'UserPassesController@extendPass');
        Route::get('get-partner-user-passes-Byid/{id}', 'UserPassesController@getUserPassByid');
        Route::delete('delete-partner-user-passes/{id}', 'UserPassesController@deleteUserPass');

        Route::post('bulk-pass-import-admin', 'UserPassesController@userpassesBulkImportData');
        Route::get('/get-userpasses', 'UserPassesController@getpasses');
        Route::get('get-partner-user-passes', ['uses' => 'UserPassesController@getUserPassByUserid', 'middleware' => 'pagination']);

        # Blacklisted vehicle list | Kuldeep:- Please don't remove without asking me
        Route::post('add-blacklisted-vehicle', 'ParkEngage\DiamondBlacklistController@addVehicle');
        Route::post('update-blacklisted-vehicle', 'ParkEngage\DiamondBlacklistController@updateVehicle');
        // Route::get('get-blacklisted-vehicle-list/page/{page}', ['uses' => 'ParkEngage\DiamondBlacklistController@getVehicleList', 'middleware' => 'pagination']);
        Route::get('get-blacklisted-vehicle-list/page/{page}', ['uses' => 'ParkEngage\DiamondBlacklistController@getVehicleList', 'middleware' => 'pagination']);
        Route::get('get-blacklisted-vehicle-list/', ['uses' => 'ParkEngage\DiamondBlacklistController@getVehicleList']);
        Route::get('get-blacklisted-vehicle-Byid/{id}', 'ParkEngage\DiamondBlacklistController@getVehicleByid');
        Route::delete('delete-blacklisted-vehicle/{id}', 'ParkEngage\DiamondBlacklistController@deleteVehicle');


        //Are admin routes
        Route::get('are-permit-list/page/{page}', ['uses' => 'ParkEngage\AreRegisterController@getPermitList', 'middleware' => 'pagination']);
        Route::get('are-permit-details/{id}', ['uses' => 'ParkEngage\AreRegisterController@getPermitDetails', 'middleware' => 'pagination']);
        Route::post('are-permit-cancel',  'ParkEngage\AreRegisterController@permitCancel');
        Route::get('are-permit-refund/{id}',  'ParkEngage\AreRegisterController@permitRefund');
        Route::get('user-pass-refund/{id}',  'UserPassesController@passRefund');

        Route::get('get-user-active-permiits', 'ParkEngage\InvoiceController@getUserActivePermitList');
        //PIMS-14196 || Dev:Sagar
        Route::post('are-permit-allow',  'ParkEngage\AreRegisterController@updatePemitAllowWhenNonOperational');




        Route::post('/send-user-email-checkin', 'ParkEngage\MapcoController@sendUserEmailCheckin');
        Route::get('send-user-details-email/{id}', 'ParkEngage\AtlantaReservationController@sendUserDetailsEmail');

        //member plan id wise rate
        Route::get('member/plan/{facilityId}/{memberPlanId}', ['uses' => 'ParkEngage\MembershipPlanController@getMemberPlanByPlanId']);

        //Facility wise Tab for superadmin
        Route::get('member/facility/tab/{facilityId}', ['uses' => 'ParkEngage\MembershipPlanController@getTabByFacilityId']);

        Route::post('ticket-search', 'ParkEngage\SubordinateController@ticketSearch');
        Route::post('retailer-rate', 'ParkEngage\SubordinateController@getRate');
        Route::post('retialer-make-checkout', 'ParkEngage\SubordinateController@makeCheckout');
        Route::post('retialer-validate-ticket', 'ParkEngage\SubordinateController@validateTicket');
        Route::post('apply-passes-ticket', 'ParkEngage\SubordinateController@applyPass');
        //Afflicate Business API
        //Route::get('get-business-type', 'ParkEngage\AffiliateBussinessController@getBusinessType');
        Route::post('add-business', 'ParkEngage\AffiliateBussinessController@store');
        Route::post('update-business', 'ParkEngage\AffiliateBussinessController@update');
        Route::post('update-business-profile', 'ParkEngage\AffiliateBussinessController@profileupdate');
        Route::delete('delete-business/{id}', 'ParkEngage\AffiliateBussinessController@destroy');
        Route::get('get-business', 'ParkEngage\AffiliateBussinessController@index');
        Route::get('get-business/page/{id}', ['uses' => 'ParkEngage\AffiliateBussinessController@index', 'middleware' => 'pagination']);
        Route::get('get-business-by-id/{id}', 'ParkEngage\AffiliateBussinessController@show');


        Route::post('get-business-policy-data', 'ParkEngage\AffiliateBussinessController@getBusinessDetails');
        Route::post('get-business-policy-data-for-multiple-tickets', 'ParkEngage\AffiliateBussinessController@getBusinessDetailsForMultipleTickets');

        //Business Clerk API
        Route::post('add-business-clerk', 'ParkEngage\BusinessClerkController@store');
        Route::post('update-business-clerk', 'ParkEngage\BusinessClerkController@update');
        Route::delete('delete-business-clerk/{id}', 'ParkEngage\BusinessClerkController@destroy');
        Route::get('get-business-clerk', 'ParkEngage\BusinessClerkController@index');
        Route::get('get-business-clerk/page/{id}', ['uses' => 'ParkEngage\BusinessClerkController@index', 'middleware' => 'pagination']);
        Route::get('get-business-clerk-by-id/{id}', 'ParkEngage\BusinessClerkController@show');
        Route::get('get-policy-business-by-id/{id}/{facility_id}', 'ParkEngage\BusinessClerkController@getPolicyByBusinessId');

        //Business Policy API
        Route::post('add-business-policy', 'ParkEngage\BusinessPolicyController@store');
        Route::post('update-business-policy', 'ParkEngage\BusinessPolicyController@update');
        Route::delete('delete-business-policy/{id}', 'ParkEngage\BusinessPolicyController@destroy');
        Route::get('get-business-policy', 'ParkEngage\BusinessPolicyController@index');
        Route::get('get-business-policy/page/{id}', ['uses' => 'ParkEngage\BusinessPolicyController@index', 'middleware' => 'pagination']);
        Route::get('get-business-policy-by-id/{id}', 'ParkEngage\BusinessPolicyController@show');



        //Infraction routes

        Route::get('get-infraction-list/page/{page}', ['uses' => 'ParkEngage\PartnerController@getInfractionList', 'middleware' => 'pagination']);
        Route::post('infraction-create', 'ParkEngage\PartnerController@infrationCreate');
        Route::post('infraction-update', 'ParkEngage\PartnerController@infractionUpdate');
        Route::get('infraction-edit/{id}', 'ParkEngage\PartnerController@infrationEdit');
        Route::delete('infraction-delete/{id}', 'ParkEngage\PartnerController@infrationDelete');

        Route::get('get-facility-infractions/{facility_id}', 'ParkEngage\DiamondGateApiController@getFacilityInfractions');


        Route::get('diamond-dashboard', 'ParkEngage\DiamondGateApiController@dashboard');
        Route::get('diamond-citation-list', 'ParkEngage\DiamondGateApiController@citationList');
        Route::post('diamond-autopay-overstay-payment', 'ParkEngage\DiamondGateApiController@autopayOverstayPayment');
        Route::get('diamond-checkout-list', 'ParkEngage\DiamondGateApiController@checkoutList');
        Route::post('diamond-citation-update', 'ParkEngage\DiamondGateApiController@citationUpdate');
        Route::post('diamond-partner-validate-device', 'ParkEngage\DiamondGateApiController@validatePartnerDevice');
        Route::get('diamond-unavailable-vehicle-list', 'ParkEngage\DiamondGateApiController@unavailableVehicleList');
        Route::post('diamond-unavailable-vehicle-update', 'ParkEngage\DiamondGateApiController@unavailableVehicleUpdate');
        Route::get('diamond-partner-facility', 'ParkEngage\DiamondGateApiController@getPartnerFacility');
        Route::get('diamond-citation-list/page/{page}', ['uses' => 'ParkEngage\DiamondGateApiController@citationList', 'middleware' => 'pagination']);
        Route::get('diamond-checkout-list/page/{page}', ['uses' => 'ParkEngage\DiamondGateApiController@checkoutList', 'middleware' => 'pagination']);
        Route::get('diamond-unavailable-vehicle-list/page/{page}', ['uses' => 'ParkEngage\DiamondGateApiController@unavailableVehicleList', 'middleware' => 'pagination']);
        Route::post('diamond-unavailable-vehicle-search', 'ParkEngage\DiamondGateApiController@unavailableVehicleSearch');
        Route::post('diamond-reopen-status-update', 'ParkEngage\DiamondGateApiController@reopenStatusUpdate');
        Route::get('diamond-today-closed-vehicle-list', 'ParkEngage\DiamondGateApiController@todayClosedVehicleList');
        Route::get('diamond-today-closed-vehicle-list/page/{page}', ['uses' => 'ParkEngage\DiamondGateApiController@todayClosedVehicleList', 'middleware' => 'pagination']);
        Route::get('diamond-unavailable-vehicle-count', 'ParkEngage\DiamondGateApiController@unavailableVehicleCount');

        Route::post('diamond-get-scaned-vehicles', 'ParkEngage\DiamondGateApiController@getScanedVehicles');
        Route::post('diamond-partner-get-scaned-vehicles', 'ParkEngage\DiamondGateApiController@getPartnerScanedVehicles');

        Route::post('diamond-permit-details', 'ParkEngage\DiamondGateApiController@getPermitDetails');
        Route::post('diamond-reservation-details', 'ParkEngage\DiamondGateApiController@getReservationDetails');

        Route::get('diamond-partner-warning-details/{warning_id}', 'ParkEngage\DiamondGateApiController@getWarningDetails');
        Route::get('diamond-partner-citation-details/{citation_id}', 'ParkEngage\DiamondGateApiController@getCitationDetails');

        Route::post('diamond-mark-citation', 'ParkEngage\DiamondGateApiController@markCitation');
        Route::post('demo-diamond-mark-citation', 'ParkEngage\DemoDiamondGateApiController@markCitation');

        Route::get('diamond-validate-license-plate-checkin/{license_plate}', 'ParkEngage\DiamondGateApiController@validateLicensePlateCheckin');

        Route::get('diamond-validate-license-plate-checkin-test/{license_plate}', 'ParkEngage\DiamondGateApiController@validateLicensePlateCheckinTest');

        Route::post('diamond-validate-license-plate-checkin-new', 'ParkEngage\DiamondGateApiController@validateLicensePlateCheckinNew');
        Route::post('diamond-validate-license-plate-checkin-demo', 'ParkEngage\DemoDiamondGateApiController@validateLicensePlateCheckinNew');

        Route::post('diamond-mark-warning', 'ParkEngage\DiamondGateApiController@markWarning');
        Route::get('diamond-warning-list', 'ParkEngage\DiamondGateApiController@warningList');
        Route::post('diamond-warning-citation-details', 'ParkEngage\DiamondGateApiController@warningCitationDetails');
        Route::get('diamond-warning-list/page/{page}', ['uses' => 'ParkEngage\DiamondGateApiController@warningList', 'middleware' => 'pagination']);

        Route::get('partner-citation-list', 'ParkEngage\DiamondGateApiController@getCitationList');
        Route::get('partner-citation-list/page/{page}', ['uses' => 'ParkEngage\DiamondGateApiController@getCitationList', 'middleware' => 'pagination']);

        Route::get('partner-warning-list', 'ParkEngage\DiamondGateApiController@getWarningList');
        Route::get('partner-warning-list/page/{page}', ['uses' => 'ParkEngage\DiamondGateApiController@getWarningList', 'middleware' => 'pagination']);

        Route::post('upload-signature', 'ParkEngage\DiamondGateApiController@uploadSignature');
        // Facility List   
        Route::get('facility-list',  ['uses' => 'FacilityController@facilityList']);
        //Route::get('diamond-warning-list', 'ParkEngage\DiamondGateApiController@warningList');

        //Diamond User Permit Request
        Route::get('diamond-user-permit-request-list',  ['uses' => 'ParkEngage\AreRegisterController@getUserPermitRequestList']);
        Route::get('diamond-user-permit-request-list/page/{page}', ['uses' => 'ParkEngage\AreRegisterController@getUserPermitRequestList', 'middleware' => 'pagination']);

        Route::get('get-diamond-user-permit-by-id/{id}', 'ParkEngage\AreRegisterController@show');
        Route::post('approved-user-permit-request', 'ParkEngage\AreRegisterController@approvePermitRequest');
        //dushyant 28/05/2024 Antipass
        Route::post('update-permit-antipass', 'ParkEngage\AreRegisterController@updatePermitAntiPass');
        Route::post('update-passback-status', 'ParkEngage\AreRegisterController@updatePassbackStatus'); #pims-13373 antipass

        //dushyant 28/05/2024 Services and Category api for backend usm
        Route::get('get-services-list/{facility_id}', 'ParkEngage\AreRegisterController@getServicesList');
        Route::get('get-category-list/{partner_id}', 'ParkEngage\AreRegisterController@getCategoryList');

        Route::get('get-service-list', 'ParkEngage\AreRegisterController@getPartnerWiseServiceList');

        Route::post('reject-user-permit-request', 'ParkEngage\AreRegisterController@rejectPermitRequest');

        // Planet Payment Profile API
        Route::get('get-planet-profile', 'ParkEngage\PlanetProfileController@index');
        Route::get('get-planet-profile/page/{id}', ['uses' => 'ParkEngage\PlanetProfileController@index', 'middleware' => 'pagination']);

        // Payment Profile API
        Route::get('get-card-profile/{facility_id?}', 'ParkEngage\PartnerController@getCardProfile');
        Route::post('add-card-profile', 'ParkEngage\PartnerController@addCardProfile');
        Route::post('update-card-profile', 'ParkEngage\PartnerController@updateCardProfile');
        Route::post('update-default-card', 'ParkEngage\PartnerController@makeCardDefault');
        Route::post('delete-card-profile', 'ParkEngage\PartnerController@destroyCardProfile');

        Route::post('add-planet-profile', 'ParkEngage\PlanetProfileController@store');
        Route::delete('delete-planet-profile/{id}', 'ParkEngage\PlanetProfileController@destroy');
        Route::get('list-payment-methods-planet', 'ParkEngage\PlanetProfileController@getPlanetPaymentProfile');

        Route::post('/reset-are-password', 'UserController@changePassword');

        //waitlisted user add by admin	
        Route::post('wait-list-user-update-admin', 'ParkEngage\WaitingListController@waitListedusersAdmin');

        Route::get('facility-list-by-business/{id}',  ['uses' => 'FacilityController@FacilityListByBusiness']);

        Route::post('pave-user-reservation', 'ParkEngage\PaveMobilityReservationController@makeUserReservation');
        Route::post('pave-reservation/{reservation}/cancel', 'ParkEngage\PaveMobilityReservationController@cancelReservationById');

        Route::post('create-partner-user', 'ParkEngage\PartnerController@createPartnerUser');
        Route::get('partner-user-list', 'ParkEngage\PartnerController@partnerUsersList');
        Route::get('partner-user-list/page/{page}', ['uses' => 'ParkEngage\PartnerController@partnerUsersList', 'middleware' => 'pagination']);

        Route::post('update-partner-user', 'ParkEngage\PartnerController@updatePartnerUser');
        Route::get('get-partner-user-by-id/{id}', 'ParkEngage\PartnerController@getpartnerUserById');


        Route::post('gated-confirm-checkin', 'ParkEngage\PaveCheckinCheckoutController@gatedCheckin');
        Route::post('customerapp-facility-details', 'ParkEngage\PaveCheckinCheckoutController@getFacilityDetails');
        Route::post('make-gated-checkin-payment',  'ParkEngage\PaveCheckinCheckoutController@makeGatedCheckinPayment');
        Route::get('facility-list-by-business/{id}',  ['uses' => 'FacilityController@FacilityListByBusiness']);

        Route::post('ungated-confirm-checkin', 'ParkEngage\PaveCheckinCheckoutController@ungatedCheckin');
        Route::post('make-ungated-checkin-payment',  'ParkEngage\PaveCheckinCheckoutController@makeUngatedCheckinPayment');
        Route::get('get-extend-checkin-details/{ticket_number}', 'ParkEngage\PaveCheckinCheckoutController@getExtendCheckinDetails');
        Route::post('get-extend-rate-checkin-details', 'ParkEngage\PaveCheckinCheckoutController@getExtendRateCheckinDetails');
        Route::post('make-ungated-extend-payment', 'ParkEngage\PaveCheckinCheckoutController@getUngatedExtendPayment');

        Route::get('user-permit-request-list', 'ParkEngage\AreRegisterController@getPermitRequestListByUser');
        Route::get('user-permit-request-list/page/{page}', ['uses' => 'ParkEngage\AreRegisterController@getPermitRequestListByUser', 'middleware' => 'pagination']);

        Route::post('add-datacap-profile', 'ParkEngage\DatacapController@store');
        Route::delete('delete-datacap-profile/{id}', 'ParkEngage\DatacapController@destroy');
        Route::get('list-payment-methods-datacap', 'ParkEngage\DatacapController@getDatacapProfile');

        //19
        Route::post('/app-make-payment', 'PayMobilityReservationController@makePayment');
        //19 

        Route::delete('delete-user-accounts', 'ParkEngage\PaveCheckinCheckoutController@deleteUserAccount');
        Route::put('pave-user-notification', 'ParkEngage\PaveCheckinCheckoutController@paveUserNotification');
        Route::get('get-user-notifications', 'ParkEngage\PaveCheckinCheckoutController@getUserNotification');
        Route::get('get-user-notifications/page/{page}', ['uses' => 'ParkEngage\PaveCheckinCheckoutController@getUserNotification', 'middleware' => 'pagination']);
        Route::delete('delete-notification/{id}', 'ParkEngage\PaveCheckinCheckoutController@deletetUserNotification');
        Route::get('get-user-checkin-history', 'ParkEngage\PaveCheckinCheckoutController@getUserCheckinHistory');
        Route::get('get-user-checkin-history/page/{page}', ['uses' => 'ParkEngage\PaveCheckinCheckoutController@getUserCheckinHistory', 'middleware' => 'pagination']);


        Route::get('user-permit-request-list', 'ParkEngage\AreRegisterController@getPermitRequestListByUser');
        Route::get('user-permit-request-list/page/{page}', ['uses' => 'ParkEngage\AreRegisterController@getPermitRequestListByUser', 'middleware' => 'pagination']);

        Route::post('update-notification-badge', 'ParkEngage\PaveCheckinCheckoutController@updateNotificationBadge');

        Route::get('get-tickets-list', 'ParkEngage\PaveCheckinCheckoutController@getTicketList');
        Route::get('get-tickets-list-for-validation/page/{page}', ['uses' => 'ParkEngage\PaveCheckinCheckoutController@getTicketListForValidation', 'middleware' => 'pagination']);
        Route::get('get-tickets-list/page/{page}', ['uses' => 'ParkEngage\PaveCheckinCheckoutController@getTicketList', 'middleware' => 'pagination']);

        Route::get('get-validated-tickets-list', 'ParkEngage/SubordinateController@getValidatedTicketDetails');
        Route::get('get-validated-tickets-list/page/{page}', ['uses' => 'ParkEngage\SubordinateController@getValidatedTicketDetails', 'middleware' => 'pagination']);

        Route::post('get-tickets-details', 'ParkEngage/SubordinateController@getScanTicketDetails');

        Route::get('get-tickets-list-by-id', 'ParkEngage\PaveCheckinCheckoutController@getTicketData');


        Route::get('user-reservation-details/{ticketech_code}', 'ParkEngage\PaveMobilityReservationController@getReservationDetailsByUserId');

        // V-26-05-2023 : Checkin Against Reservation
        Route::post('checkin-against-reservation', 'ParkEngage\PaveCheckinCheckoutController@checkInAgainstReservation');
        Route::post('get-rate-for-qr_checkin', 'ParkEngage\PaveCheckinCheckoutController@getRateQrCheckin');
        Route::post('make-gated-checkin-qr-payment',  'ParkEngage\PaveCheckinCheckoutController@gatedQrPayment');
        Route::post('checkout-qr-ticket',  'ParkEngage\PaveCheckinCheckoutController@qrTicketCheckout');
        //activate deactivate partner
        Route::get('activate-deactivate-partner/{partner_id}/{status}', 'ParkEngage\PartnerController@activateDeactivatePartner');

        Route::get('delete-permit-vehicle/{license_plate_number}', 'PermitController@deletePermitVehicle');

        Route::get('partner-driveup-offlinepayment-list/page/{page}', ['uses' => 'ParkEngage\PartnerCheckinCheckoutController@getDriveOffPaymenttList', 'middleware' => 'pagination']);
        Route::get('get-partner-permit-checkin-checkout-list', 'ParkEngage\PartnerCheckinCheckoutController@getPermitCheckinCheckoutList');
        Route::get('payment-type-list', 'PartnerController@paymentTypeList');
        Route::get('get-partner-details-by-id/{id}', 'ParkEngage\PartnerController@getPartnersDetailsBy');

        Route::get('get-user-validated-list', 'ParkEngage\PartnerCheckinCheckoutController@getUserValidated');
        //RM api
        Route::get('get-rm-list', 'ParkEngage\PartnerController@getRMList');
        Route::get('get-partner-customer-permission-list', 'ParkEngage\PartnerController@getPartnerCustomerPermissionList');
        Route::get('get-partner-customer-permission-list/page/{page}', ['uses' => 'ParkEngage\PartnerController@getPartnerCustomerPermissionList', 'middleware' => 'pagination']);
        Route::post('save-customer-permission', 'ParkEngage\MembershipPlanController@saveCustomerPermission');
        Route::post('update-customer-permission', 'ParkEngage\MembershipPlanController@updateCustomerPermission');
        Route::get('customer-permission-details/{id}', 'ParkEngage\MembershipPlanController@customerPermissionDetails');
        Route::delete('delete-customer-permission-details/{id}', 'ParkEngage\MembershipPlanController@destroyCustomerPermissionDetails');

        //Generate Business QR code
        // Route::post('business-qr-code',  'ParkEngage\AffiliateBussinessController@createBusinessQRCode');
        Route::post('business-qr-code',  'ParkEngage\AffiliateBussinessController@createBusinessQRCodeNew');
        Route::post('business-email-QRCode',  'ParkEngage\AffiliateBussinessController@businessEmailQRCode');
        Route::post('business-qr-logo-delete',  'ParkEngage\AffiliateBussinessController@businessQRCodeLogoDelete');
        Route::get('validate-qr-code/{ticketech_code}', 'ParkEngage\TownsendApiController@validateEventQrCode');
        Route::get('get-map-events/{id}', 'EventController@getMapEvents');
        Route::get('get-event-details/{event_id}/{facility_id}', 'EventController@getEventDetails');
        Route::post('event-parking-booking', 'ParkEngage\TownsendApiController@makeEventBooking');
        Route::get('get-ongoing-events/{id}', 'EventController@getOnGoingEvents');
        Route::get('get-event-scan-history', 'ReservationController@getScanHistory');
        Route::get('get-event-scan-history-new', 'ReservationController@getScanHistoryNew');
        Route::get('get-event-scan-history-by-id/{id}', 'ReservationController@getScanHistoryByEvent');
        Route::get('get-event-scan-history/page/{page}', ['uses' => 'ReservationController@getScanHistory', 'middleware' => 'pagination']);
        Route::get('get-event-user-facility/{id}', 'FacilityController@getFacilities');
        Route::post('event-driveup-checkin', 'ParkEngage\TownsendApiController@eventDriveupCheckin');
        Route::post('event-reservation-driveup-checkin', 'ParkEngage\TownsendApiController@reservationDriveupCheckin');
        Route::get('event-driveup-checkin-listing', 'ParkEngage\TownsendApiController@getDriveupCheckin');
        Route::get('event-driveup-checkin-listing/page/{page}', ['uses' => 'ParkEngage\TownsendApiController@getDriveupCheckin', 'middleware' => 'pagination']);

        Route::get('payment-gateway-list', ['uses' => 'ParkEngage\PartnerController@paymentGatewayList']);
        Route::get('/getByIDPromotions/{promotionId}', 'LatestPromotionController@show');

        //API for Data Trans Mobile
        Route::post('data-trans-payment-token',  'ParkEngage\DataTransController@getDataTransPaymentToken');

        //created gate vend api by vikrant

        Route::post('partner-gate-open', 'ParkEngage\GateController@openGate');
        Route::get('gated-facility-list',  ['uses' => 'ParkEngage\GateController@gatedFacilityList', 'middleware' => 'pagination']);
        Route::get('gated-facility-list/page/{page}', ['uses' => 'ParkEngage\GateController@gatedFacilityList', 'middleware' => 'pagination']);

        //common api for warning update and citation    
        Route::post('partner-warning-citation-update', 'ParkEngage\DiamondGateApiController@updateWarningCitation');
        Route::post('permit-license-plate-update', 'ParkEngage\AreRegisterController@permitLicensePLateUpdate');

        // License Plate listing
        Route::get('lpr-license-plate-listing', 'ParkEngage\PartnerController@licensePlateListing');
        Route::get('lpr-license-plate-listing/page/{page}', ['uses' => 'ParkEngage\PartnerController@licensePlateListing', 'middleware' => 'pagination']);

        // WhiteList Api

        Route::get('whitelist-users', 'ParkEngage\WhitelistUserController@index')->name('whitelist-users.index');
        Route::get('whitelist-users/page/{page}', ['uses' => 'ParkEngage\WhitelistUserController@index', 'middleware' => 'pagination']);
        Route::post('whitelist-users', 'ParkEngage\WhitelistUserController@store')->name('whitelist-users.store');
        Route::get('whitelist-users/{whitelist}', 'ParkEngage\WhitelistUserController@show')->name('whitelist-users.show');
        Route::put('whitelist-users/{whitelist}', 'ParkEngage\WhitelistUserController@update')->name('whitelist-users.update');
        Route::delete('whitelist-users/{whitelist}', 'ParkEngage\WhitelistUserController@destroy')->name('whitelist-users.delete');
        Route::get('whitelist-users-events', 'ParkEngage\WhitelistUserController@getEventDetails')->name('whitelist-users.event_details');

        //by Alka resend payment URL : Yankee 
        Route::post('resend-payment-url/{permit_id}', 'ParkEngage\AreRegisterController@resendPaymentUrl');

        // Alka:: Resend Payment URL After Expiration Without Payment 
        Route::post('resend-afterexpire-paymenturl', 'ParkEngage\AreRegisterController@resendPaymentUrlAfterExpiration');

        //port attendant confirm payment checkout
        Route::post('attendant-confirm-checkout', 'ParkEngage\PartnerCheckinCheckoutController@attendantConfirmCheckout');
        Route::post('attendant-confirm-checkin', 'ParkEngage\PartnerCheckinCheckoutController@attendantConfirmCheckin');
        Route::get('ticket-user-card-list/{ticket_number}', 'ParkEngage\PartnerCheckinCheckoutController@getTicketUserCardList');


        Route::post('resend-email', 'ParkEngage\AreRegisterController@resendEmail');
        // Userwise Event Listing
        Route::get('get-events-list', 'EventController@getEventsList');
        // Attendant Listing
        Route::get('get-attendant-list', 'ParkEngage\PartnerController@getAttendantList');

        Route::post('save-attendant-gates-mapping', 'ParkEngage\PartnerCheckinCheckoutController@saveAttendantGateWithDeviceToken');

        Route::get('get-ticket-overstay-list/{ticket_number}', 'ParkEngage\PartnerCheckinCheckoutController@getTicketOverstayList');

        // Lokesh: For Appeal 
        Route::put('appeal-file/{update_id}', 'ParkEngage\DemoDiamondGateApiController@fileAppealAgainstCitation');
        Route::post('citation-appeal-details-email', 'ParkEngage\DemoDiamondGateApiController@forwardCitationAppealDetailEmail');
        Route::get('get-appeals', 'ParkEngage\DemoDiamondGateApiController@getAppealAgainstCitation');
        Route::get('get-appeals/page/{page}', ['uses' => 'ParkEngage\DemoDiamondGateApiController@getAppealAgainstCitation', 'middleware' => 'pagination']);
        Route::get('get-promo-monitor-excel', 'LatestPromotionController@getPromocodeUsers');

        // Alka, Date::18 Sept 2024
        Route::put('update-vehicle-details', 'ParkEngage\DiamondUserProfileController@updateVehicleDetails');
        // Alka Date: get user card details
        // Route::get('get-card-details', 'ParkEngage\PartnerController@getCardProfile');
        Route::get('get-card-details', 'ParkEngage\PartnerController@getCardProfileDetails');

        Route::get('admin-diamond-permit-partner-facility/{slug?}/{business_id?}/{permit_type_id?}', 'ParkEngage\DiamondGateApiController@getAdminPartnerPermitFacility');

        Route::post('sync-offline-tickets', 'ParkEngage\OfflineTicketController@syncOfflineTickets');

        Route::get('get-citation-void-codes/{partner_id?}', 'ParkEngage\DiamondGateApiController@getCitationVoidCodes');

        //PIMS-13879 - endpoint start
        Route::post('qrcode-import', 'ParkEngage\QrCodeImportController@importQrCodes');

        Route::post('facility-qrcode-mapping/store', 'ParkEngage\QrCodeImportController@qrcodeFaciltyMapping');

        // Update QR code-facility mapping
        Route::put('facility-qrcode-mapping/{id}', 'ParkEngage\QrCodeImportController@updateQrcodeFacilityMapping');

        // Delete a single QR code-facility mapping
        Route::delete('facility-qrcode-mapping/{id}', 'ParkEngage\QrCodeImportController@destroyQrcodeFacilityMapping');

        Route::get('facility-qrcode-mapping/page/{page}', 'ParkEngage\QrCodeImportController@listQrcodeFacilityMappings');

        // Show details of a single QR code-facility mapping (with mapped short code)
        Route::get('facility-qrcode-mapping/detail/{id}', 'ParkEngage\QrCodeImportController@showQrcodeFacilityMapping');

        Route::get('facility-qrcode-mapping/edit/{id}', 'ParkEngage\QrCodeImportController@editQrcodeFacilityMapping');

        Route::post('facility-qrcode-mapping/qrcode_mapping_csv', 'ParkEngage\QrCodeImportController@qrcodeFacilityMappingCsv');
        //PIMS-13879 - endpoint end
    }

);

//PIMS-13879 - endpoint start
Route::get('facility-qrcode-mapping/qrcode/list/{id}', 'ParkEngage\QrCodeImportController@getUnMappedQrCodeSeries');

Route::get('download-garage-checkin-qrcode/{id}', 'ParkEngage\QrCodeImportController@genrateQrCode');

Route::get('facility-qrcode-mapping/qrcodes_download', 'ParkEngage\QrCodeImportController@downloadQrCodes');


Route::get('get-sample-qrcode-partner-upload-file', 'ParkEngage\QrCodeImportController@downloadSampleQrCodePartnerAndFacilityMappingFile');

Route::get('get-sample-qrcode-upload-file', 'ParkEngage\QrCodeImportController@downloadSampleQrCodeMappingFile');


Route::get('get-sample-qrcode-upload-file', 'ParkEngage\QrCodeImportController@downloadSampleQrCodeMappingFile');
//PIMS-13879 - endpoint end

Route::get('get-sample-bulkpermit/{partner_id?}', 'ParkEngage\AreRegisterController@downloadSampleBulkPermitFile');
Route::get('get-sample-file', 'ParkEngage\DiamondBlacklistController@downloadSampleBlacklistedFile');
Route::get('get-sample-bulkpasses', 'UserPassesController@downloadSampleBulkUserPassesFile');


Route::get('permitbooking-qrcode-email/{qrcode}', 'ParkEngage\AreRegisterController@showQrCodeImage');

Route::get('deleted_user-access', 'ParkEngage\AreRegisterController@deletedUser');

// for pdf download receipt
//Route::get('download-receipt-pdfticket/{ticket_number}', 'ParkEngage\PartnerController@downloadPdfReceipt');

Route::get('download-receipt-pdfticket/{ticket_number}/{partner_id}', 'ParkEngage\PartnerController@downloadPdfReceipt');


Route::get('update-checkin-time', 'ParkEngage\BusinessClerkController@updateCheckinTime');
Route::get('/citations-images/{id}', 'ParkEngage\DiamondGateApiController@getCitationImageUrl');
Route::get("/spothero-registration-list", 'ReservationController@spotheroReservationCron');
Route::group(
    ['middleware' => ['apiresponse', 'timezone']],
    function () {

        // Auto start USM : 01-08-2024 : PRRD OFF as discussed : 11-09-2024
        // Route::post('transient-autostart/{secret}', 'ParkEngage\LPRCheckinCheckoutIntegrationController@thirdPartyCheckinCheckout');

        // ALka:: Date::27 June 2024, Validate email
        Route::post('get-validated-user', 'ParkEngage\AreRegisterController@userDetailsByEmailId');

        // Lokesh: Demo Promotions   
        Route::get('getallpromotions_demo', 'ParkEngage\DemoPromotionController@getAll');
        Route::get('getallpromotions_demo/page/{page}',  ['uses' => 'ParkEngage\DemoPromotionController@getAll', 'middleware' => 'pagination']);
        Route::post('submit-usage-promotion-demo', 'ParkEngage\DemoPromotionController@usagePromocodeDemo');

        // Lokesh: For Appeal 
        Route::post('appeal-file', 'ParkEngage\DemoDiamondGateApiController@fileAppealAgainstCitation');


        // Lokesh: Hub Zeag
        Route::get('create-update-reservation-hub-zeag/{reservation_id}/{method}/{is_external?}', 'HubZeag\HubZeagController@dispatchJobForParkingHub'); //create and update on hubzeag
        Route::delete('cancel-reservation-hub-zeag/{reservation_id}', 'HubZeag\HubZeagController@cancelReseravtionForParkingHub'); //cancelled on hubzeag
        Route::get('checkin-out-hub-zeag-by-date', 'HubZeag\HubZeagController@checkInDataForParkingHubByDate');
        Route::get('checkin-out-hub-zeag-by-booking-id', 'HubZeag\HubZeagController@checkInDataForParkingHubByBookingID');
        Route::get('checkin-synced-hub-zeag-by-booking-id', 'HubZeag\HubZeagController@checkInSyncedDataForParkingHubByBookingID');
        Route::get('reservation-hub-zeag-by-date', 'HubZeag\HubZeagController@reservationDataForParkingHubByDate');
        Route::get('reservation-hub-zeag-by-booking-id', 'HubZeag\HubZeagController@reservationDataForParkingHubByBookingID');

        Route::post('/pave-user-reservation-guest', 'ParkEngage\PaveMobilityReservationController@userReservationCreate');
        Route::put('/pave-user-reservation-update', 'ParkEngage\PaveMobilityReservationController@userReservationUpdate');
        Route::post('/import-reservation', 'ParkEngage\PaveMobilityReservationController@importReservation');

        Route::get('get-total-rm-list', 'ParkEngage\PartnerController@getTotalRmList');
        Route::get('get-qrcode-validate-details/{qrcode}',  'ParkEngage\AffiliateBussinessController@getQrcodeValidateDetails');
        Route::post('get-ticket-search',  'ParkEngage\AffiliateBussinessController@getTicketSearch');
        Route::post('get-policy-data',  'ParkEngage\AffiliateBussinessController@getPolicyData');
        Route::post('validate-qrbased-ticket', 'ParkEngage\AffiliateBussinessController@validateQrbasedTicket');
        Route::post('get-events', 'EventController@getEvents');
        Route::post('event-booking-web', 'ParkEngage\TownsendApiController@makeEventBookingWeb');
        Route::get('facility-entry-gates/{facility_id}', 'ParkEngage\PartnerCheckinCheckoutController@getFacilityEntryGates');
        Route::post('test-heartland-payment', 'ParkEngage\HeartlandProfileController@testHeartland');
        Route::post('charge-by-token', 'ParkEngage\HeartlandProfileController@chargePaymentByToken');
        Route::post('charge-by-apple-token', 'ParkEngage\HeartlandProfileController@chargePaymentByAppleToken');
        Route::post('charge-by-token-pay-google', 'ParkEngage\HeartlandProfileController@chargePaymentByTokenPay');
        Route::get('heartland-customer-profile', 'ParkEngage\HeartlandProfileController@getHeartlandCustomers');
        Route::post('heartland-payment-by-card', 'ParkEngage\HeartlandProfileController@chargePaymentByCard');
        Route::post('heartland-auth-card-payment', 'ParkEngage\HeartlandProfileController@authCardPayment');
        Route::post('heartland-charge-auth-card-payment', 'ParkEngage\HeartlandProfileController@chargePreauthCardPayment');
        Route::post('heartland-surcharge-payment', 'ParkEngage\HeartlandProfileController@surChargeHeartlandPayment');
        Route::post('heartland-card-payment', 'ParkEngage\HeartlandProfileController@cardPayment');
        Route::post('heartland-save-card', 'ParkEngage\HeartlandProfileController@saveHeartlandCard');
        Route::post('heartland-refund-payment', 'ParkEngage\HeartlandProfileController@refundHeartlandPayment');
        Route::post('heartland-return-payment', 'ParkEngage\HeartlandProfileController@returnHeartlandPayment');
        Route::post('heartland-verify-card', 'ParkEngage\HeartlandProfileController@verifyCardHeartlandPayment');
        Route::get('cruise/{facility_id}', 'ParkEngage\CruiseController@cruiseList');
        Route::get('cruise-schedule/{id}', 'ParkEngage\CruiseController@getCruiseSchedule');
        // Event Cancel API
        Route::get('cancel-reservation-by-user/{id}', 'ParkEngage\PartnerController@cancelBookingFront');
        Route::get('reservation-booking-details/{id}', 'ParkEngage\MapcoController@getBookingUsageDetails');
        Route::get('get-all-make-models/{country_id?}', 'ParkEngage\DiamondGateApiController@getAllMakeModelsData');
        Route::get('get-account-name/{partnet_id}', 'ParkEngage\PartnerController@getAccountName');
        //Alka:: 23-Feb-2024 MAPCO ROC - HUB Zeag Integration to get Check-in/Out data and save in the database
        // Route::post('roc-checkin',  'Integration\IntegrationController@saveMapcoRocCheckinData');
        // Route::put('roc-checkout',  'Integration\IntegrationController@updateMapcoRocCheckinData');

        // Alka :: 03 May 2024:: Yenkee
        Route::get('payment-permit-request/{permit_id}', 'ParkEngage\AreRegisterController@getAdminPermitDetails');
        // DD :: PIMS-12870 
        Route::post('user-ticket-consent', 'ParkEngage\PaveCheckinCheckoutController@updateUserTicketConsent');
        Route::post('customer-permit-payment', 'ParkEngage\AreRegisterController@customerPayment');
        // Vijay Deployed : 15-05-2024
        Route::get('rate-details/{facility_id}', 'RateController@getRateDetails');
        // Alka::08 May 2024 (for warning details transient)
        Route::get('get-warnings/{warning_number}', 'ParkEngage\PaveCheckinCheckoutController@getWarningDetails');

        //Dushyant::13 May 2024 
        //get permit services/categories 
        Route::post('get-permit-services', 'ParkEngage\AreRegisterController@getPermitServices');
        Route::get('get-permit-categories', 'ParkEngage\AreRegisterController@getPermitCategories');
        Route::get('get-partner-details', 'ParkEngage\PartnerController@getPartnerDetails');
        //Vikrant facility payment details
        Route::get('facility-payment-details/{facility_id}', 'ParkEngage\ConfigurationController@facilityPaymentDetails');

        /** PIMS-14556 middleware optimization */
        Route::post('get-permit-rate-by-email', ['uses' => 'ParkEngage\AreRegisterController@getPermitRateByEmail', 'middleware' => 'verify.client']);
        /** PIMS-14556 end middleware optimization */

        #KT: 21/03/2025 | Clerk Role Update
        Route::post('user-role-update/{user_id}', 'ParkEngage\BusinessClerkController@updateUserRole');

        //Vikrant colinial whitelist user save with card
        Route::post('validate-whitelist-users', 'ParkEngage\ColonialCheckinCheckoutController@validateWhitelistUsers');
        Route::post('validate-whitelist-users-cards', 'ParkEngage\ColonialCheckinCheckoutController@validateWhitelistUsersCardExist');
        //UPBL-87
        Route::get('get-facility-slot-count/{facility_id}', 'ParkEngage\FacilitySlotController@facilitySlotCount');
    }
);
Route::get('aaa-download-booking-pdf-guest/{reservation}', 'ReservationController@downloadBookingPdf');
Route::get('/facility-logo/{logo}', 'ParkEngage\PartnerController@getFacilityLogoUrl');
Route::get('/business-logo/{logo}', 'ParkEngage\AffiliateBussinessController@getBusinessLogoUrl');
Route::get('business-pdf-QRCode/{id}/{enabled_ticket_creation_flow?}',  'ParkEngage\AffiliateBussinessController@businessPdfQRCode');
Route::get('permit-pdf-QRCode/{id}',  'ParkEngage\AreRegisterController@permitPdfQRCode');
Route::get('download-user-permit', 'ParkEngage\InvoiceController@getInvoicePdf');

Route::post('customer-massgrading', ['uses' => 'ParkEngage\CustomerMassgrading@customerMass', 'middleware' => 'auth-or-anon']);
Route::post('send-ticket-to-guest', 'ParkEngage\PartnerController@sendtickettoguest'); #PIMS-12580
Route::post('/roc-preauth-release', 'ParkEngage\PartnerController@preAuthReleaseRoc');
Route::get('/run-cronjob-parkchrip', 'ParkEngage\AreRegisterController@runCronjob');
Route::get('/run-cronjob-entrata', 'ParkEngage\AreRegisterController@runCronjobEntrata');
Route::get('/citation-cronjob-parkchrip', 'ParkEngage\AreRegisterController@citationrunCronjob');
Route::post('/update-hl-card-expiry', 'ParkEngage\AreRegisterController@updateCardExpiry');
