<?php

// ADMIN ROUTES FILE
use App\Models\Role;

// Customer service routes
// Has access to users tab (excluding creating new users, changing access levels and deleting users) and the reservations tab
Route::group(
    ['middleware' => ['apiresponse', 'oauth', 'fetch-user', 'timezone', 'role:' . Role::ADMIN . '|' . Role::CUSTOMER_SERVICE . '|' . Role::PARTNER . '|' . Role::SUBPARTNER . '|' . Role::REGIONAL_MANAGER]],
    function () {
        // Masquerade as a normal user access
        Route::post(
            'oauth/access_token/masquerade',
            function () {
                $_SESSION['client_id'] = 'inventory-demo';
                $_SESSION['client_secret'] = 'Wqhqdz9PMKMHWUG9pZ0Oowvrz';
                return Authorizer::issueAccessToken();
            }
        );

        // Reservation management
        Route::get('reservation', 'ReservationController@get');
        Route::get('reservation/page/{page}', ['uses' => 'ReservationController@get', 'middleware' => 'pagination']);
        Route::post('reservation/{reservation}/resend', 'ReservationController@resendEmail');
    }
);

// Accounts receivable and customer service
// Should only have access to the user's tab, excluding the ability to add new users, change access levels, delete users, or masquerade as users
Route::group(
    ['middleware' => ['apiresponse', 'oauth', 'fetch-user', 'timezone', 'role:' . Role::ADMIN . '|' . Role::CUSTOMER_SERVICE . '|' . Role::ACCOUNTS_RECEIVABLE . '|' . Role::PARTNER . '|' . Role::SUBPARTNER . '|' . Role::REGIONAL_MANAGER]],
    function () {
        // User admin
        Route::get('user', ['uses' => 'UserController@index', 'middleware' => 'pagination']);
        // Route::get('user/page/{page}', ['uses' => 'UserController@index', 'middleware' => 'pagination']);
        Route::get('user/{user}/details', 'UserController@getUserForAdmin');
        Route::get('userCredits/page/{page}', ['uses' => 'UserController@userCredits', 'middleware' => 'pagination']);
        Route::get('userTransactions/{userId}/page/{page}',  ['uses' => 'UserController@userTransactions', 'middleware' => 'pagination']);
        Route::get('searchUserStr/{searchValue}',  'UserController@searchUserStr');
        Route::post('admin/user/{user}/reset-password', 'PasswordController@resetPasswordAdmin');
        Route::post('addUserTransaction', 'UserController@addUserTransaction');
        Route::get('deleteUserTransactions/{tid}',  'UserController@deleteUserTransactions');
    }
);

// Monthly parking sales routes
Route::group(
    ['middleware' => ['apiresponse', 'oauth', 'timezone', 'fetch-user', 'role:' . Role::MONTHLY_SALES . '|' . Role::ADMIN . '|' . Role::PARTNER . '|' . Role::SUBPARTNER . '|' . Role::REGIONAL_MANAGER]],
    function () {
        // Manage monthly requests
        Route::get('monthly-request', 'MonthlyRequestController@index');
        Route::get('monthly-request/page/{page}', ['uses' => 'MonthlyRequestController@index', 'middleware' => 'pagination']);
        Route::get('monthly-request/{monthlyRequest}', 'MonthlyRequestController@getById');
        Route::put('monthly-request/{monthlyRequest}', 'MonthlyRequestController@update');
        Route::post('monthly-request/{monthlyRequest}/charge', 'MonthlyRequestController@charge');
        Route::delete('monthly-request/{monthlyRequest}', 'MonthlyRequestController@delete');
    }
);

// Administrator routes
Route::group(
    ['middleware' => ['apiresponse', 'oauth', 'fetch-user', 'timezone', 'role:' . Role::ADMIN . '|' . Role::PARTNER . '|' . Role::SUBPARTNER . '|' . Role::REGIONAL_MANAGER . '|' . Role::BUSINESSUSER . '|' . Role::BUSINESSCLERK]],
    function () {

        Route::get('neighborhood_with_facilitytypes', ['uses' => 'NeighborhoodController@neighborhoodWithFacilityTypes']);

        Route::get('facility',  ['uses' => 'FacilityController@index']);
        Route::get('facility_details/{facility}', 'FacilityController@showDetailsAdmin');
        Route::get('facility_details_mob/{facility}', 'FacilityController@showDetailsAdminMob');
        Route::get('facility/page/{page}', ['uses' => 'FacilityController@index']);
        Route::get('facility_new',  ['uses' => 'FacilityController@indexNew', 'middleware' => 'pagination']);
        Route::get('facility_new/page/{page}', ['uses' => 'FacilityController@indexNew', 'middleware' => 'pagination']);
        Route::post('facility-list-mobile',  ['uses' => 'FacilityController@indexFacilityListMobile', 'middleware' => 'pagination']);
        /*Route::post('facility-list-mobile/page/{page}',  ['uses' => 'FacilityController@indexFacilityListMobile', 'middleware' => 'pagination']);*/
        Route::put('facility/{facility}', 'FacilityController@update');
        Route::put('facility-payment-details/{facility}', 'FacilityController@updatePaymentdetails');
        Route::put('facility-general/{facility}', 'FacilityController@updateGeneral');
        Route::put('facility-geolocation/{facility}', 'FacilityController@updateGeolocation');
        Route::put('facility-rate/{facility}', 'FacilityController@updateRate');
        Route::delete('facility/{facility}', 'FacilityController@destroy');
        Route::post('facility', 'FacilityController@store');
        Route::get('facility/integrations/{facility}', 'FacilityController@showIntegrations');
        Route::post('facility/{facility}/photo', 'FacilityController@addPhoto');
        
        Route::post('facility/{facility}/hours', 'FacilityController@addHoursOfOperation');
        Route::post('hours/{hours}', 'HoursOfOperationController@destroy');
       //UPBL-87
        Route::get('get-threshold-vehicle-list/{facility}', ['uses' => 'FacilityController@getVehicleList', 'middleware' => 'pagination']);
        Route::put('update-threshold-vehicle/{lpr_feed_id}','FacilityController@updateVehicle');
        Route::get('get-threshold-vehicle/{lpr_feed_id}','FacilityController@getVehicle');
        Route::post('add-threshold-vehicle','FacilityController@addVehicle');
        Route::post('delete-threshold-vehicle', 'FacilityController@deleteVehicle');
        Route::post('get-permit-ticket-By-Lp/{license_plate}','FacilityController@getPermitTicketByLp');

        
       // Route::get('get-vechile-list/{facility}','FacilityController@getVechileList');
        Route::put('update-facility-threshold/{facility_id}','FacilityController@updateThreshold');

        Route::post('facility/{facility}/hours', 'FacilityController@addHoursOfOperation');
        Route::post('hours/{hours}', 'HoursOfOperationController@destroy');

        // New change For facility Update in inventory
        Route::put('inventory-updateflag/{facility}', 'FacilityController@updateInventoyFlags');
        //add  new route for inventory
        Route::delete('hours/{hours}', 'HoursOfOperationController@destroy');

        Route::put('ratecategory/{id}', ['uses' => 'RateCategoryController@update', 'middleware' => 'ratecategory']);
        Route::delete('ratecategory/{id}', ['uses' => 'RateCategoryController@destroy', 'middleware' => 'ratecategory']);
        Route::post('ratecategory', ['uses' => 'RateCategoryController@store', 'middleware' => 'ratecategory']);
        Route::get('ratecategorylist', ['uses' => 'RateCategoryController@getRateCategoryList', 'middleware' => 'ratecategory']);
        Route::get('passusaselist/page/{page}', ['uses' => 'RateCategoryController@getPassUsaseList', 'middleware' => 'pagination']);
        Route::get('ratecategoryedit/{id}', ['uses' => 'RateCategoryController@edit']);


        Route::put('rate/{id}', 'RateController@update');
        Route::get('rate/coupon-code/validation', 'RateController@validateCouponCode');
        Route::delete('rate/{id}', 'RateController@destroy');
        Route::post('rate', 'RateController@store');
        Route::post('rates/csv', 'RateController@updateAllRatesCsv');

        Route::post('rate-details-list', 'RateController@getRateDetailsListInAdmin');
        Route::get('rate-category-list', 'RateController@getRateCategoryListInAdmin');

        Route::put('couponpromo/{promo}', 'CouponPromoController@update')->where(['promo' => '[0-9]+']);
        Route::post('couponpromo/{promo}/photo', 'CouponPromoController@addPhoto')->where(['promo' => '[0-9]+']);
        Route::delete('couponpromo/{promo}', 'CouponPromoController@destroy')->where(['promo' => '[0-9]+']);
        Route::post('couponpromo', 'CouponPromoController@store');
        Route::put('couponoffer/{offer}', 'CouponOfferController@update')->where(['offer' => '[0-9]+']);
        Route::delete('couponoffer/{offer}', 'CouponOfferController@destroy')->where(['offer' => '[0-9]+']);
        Route::post('couponoffer', 'CouponOfferController@store');

        Route::put('monthlyparkingpromo/{promo}', 'MonthlyParkingPromoController@update')->where(['promo' => '[0-9]+']);
        Route::post('monthlyparkingpromo/{promo}/photo', 'MonthlyParkingPromoController@addPhoto')->where(['promo' => '[0-9]+']);
        Route::delete('monthlyparkingpromo/{promo}', 'MonthlyParkingPromoController@destroy')->where(['promo' => '[0-9]+']);
        Route::post('monthlyparkingpromo', 'MonthlyParkingPromoController@store');

        Route::get('attraction', 'AttractionController@index');
        Route::post('attraction', 'AttractionController@store');
        Route::put('attraction/{attraction}', 'AttractionController@update')->where(['attraction' => '[0-9]+']);
        Route::delete('attraction/{attraction}', 'AttractionController@destroy')->where(['attraction' => '[0-9]+']);

        Route::get('partner', 'PartnerController@index');
        Route::get('partner/page/{page}', ['uses' => 'PartnerController@index', 'middleware' => 'pagination']);
        Route::get('partner/edit/{partner}', 'PartnerController@show');
        Route::post('partner', 'PartnerController@store');
        Route::put('partner/{partner}', 'PartnerController@update');
        Route::post('partner/{partner}/photo', 'PartnerController@addPhoto');
        Route::delete('partner/{partner}', 'PartnerController@destroy');

        // Event admin routes
        Route::get('event', 'EventController@index');
        Route::get('event/edit/{event}', 'EventController@show');
        Route::get('event/page/{page}', ['uses' => 'EventController@index', 'middleware' => 'pagination']);
        Route::put('event/{event}', 'EventController@update');
        Route::post('event', 'EventController@store');
        Route::delete('event/{event}', 'EventController@destroy');

        Route::post('event-image-update', 'EventController@eventImageUpdate');
        Route::post('image-upload', 'EventController@imageUpload');
        Route::put('image-delete', 'EventController@imageDelete');

        Route::put('event-activate-inactive', 'EventController@eventActiveInactive');

        // Event category routes
        Route::get('event-category-list/page/{page}', ['uses' => 'EventCategoryController@index', 'middleware' => 'pagination']);
        Route::post('add-event-category', 'EventCategoryController@addEventCategory');
        Route::delete('delete-event-category/{id}', 'EventCategoryController@deleteEventCategory');
        Route::put('update-event-category', 'EventCategoryController@updateEventCategory');
        Route::get('edit-event-category/{id}', 'EventCategoryController@editEventCategory');

        //partner event
        Route::get('event-list', 'EventController@getEventList');
        Route::get('partner-event-category', 'EventCategoryController@getEventCategoryList');

        // Update user roles
        Route::put('user/change-role/{user}', 'UserController@changeRole');
        Route::delete('user/remove-roles/{user}', 'UserController@removeRoles');

        // Delete user
        Route::delete('user/{user}', 'UserController@delete');

        // Edit CMS pages
        Route::put('cms/{page}', 'CMSController@update');
        Route::post('cms', 'CMSController@store');
        Route::delete('cms/{page}', 'CMSController@destroy');
        Route::post('cms/{page}/photo', ['uses' => 'CMSController@addPhoto']);

        Route::post('photo', 'PhotoController@store');

        // View contact us submissions - no UI for this currently
        Route::get('correspondence', 'CorrespondenceController@index');
        Route::get('correspondence/{correspondence}', 'CorrespondenceController@show')
            ->where(['correspondence' => '[0-9]+']);

        // Basic monthly parking requests
        Route::get('simple-monthly-request', ['uses' => 'SimpleMonthlyRequestController@index']);
        Route::get('simple-monthly-request/page/{page}', ['uses' => 'SimpleMonthlyRequestController@index', 'middleware' => 'pagination']);
        Route::get('simple-monthly-request/{simpleMonthlyRequest}', 'SimpleMonthlyRequestController@show');
        Route::delete('simple-monthly-request/{simpleMonthlyRequest}', 'SimpleMonthlyRequestController@delete');
        Route::put('simple-monthly-request/{simpleMonthlyRequest}', 'SimpleMonthlyRequestController@update');

        Route::post('basicmonthlyrequest/{id}/toggle', 'BasicMonthlyRequestController@toggleComplete');

        // Accounts receivable
        Route::get('ar/accounts', 'ArApiController@getActiveAccounts');
        Route::get('ar/accounts/{accountId}', 'ArApiController@getAccount');
        Route::get('ar/accounts/{accountId}/balance', 'ArApiController@getAccountBalances');
        Route::get('ar/ping', 'ArApiController@ping');

        // Authorize net helpers
        Route::get('authnet/paymentprofiles/{profileId}', 'PaymentProfileController@get');
        Route::get('authnet/transaction/{transaction}', 'AuthorizeNetController@getTransaction');

        Route::get('error/{error}', 'ErrorController@getError');

        // Web meta
        Route::get('web-meta/admin', ['uses' => 'WebMetaController@index', 'middleware' => 'pagination']);
        Route::get('web-meta/page/{page}', ['uses' => 'WebMetaController@index', 'middleware' => 'pagination']);
        Route::post('web-meta', 'WebMetaController@store');
        Route::get('web-meta/{webMeta}', 'WebMetaController@show');
        Route::put('web-meta/{webMeta}', 'WebMetaController@update');
        Route::delete('web-meta/{webMeta}', 'WebMetaController@destroy');

        Route::get('basicmonthlyrequest', ['uses' => 'BasicMonthlyRequestController@index']);
        Route::get('basicmonthlyrequest/page/{page}', ['uses' => 'BasicMonthlyRequestController@index', 'middleware' => 'pagination']);
        Route::get('basicmonthlyrequest/{basicMonthlyRequest}', 'BasicMonthlyRequestController@getById');

        // Kicking off user invoice
        Route::post('user-invoice/import', 'UserInvoiceController@import');
        Route::post('uploadInvoice', 'UploadInvoiceController@uploadInvoice');

        //create Notification
        Route::post('notification/create', 'NotificationController@createNotification');
        //active deactive notification
        Route::post('notification/change-activation', 'NotificationController@activedeactive');
        //update notification
        Route::post('notification/update', 'NotificationController@updateNotification');
        Route::put('rate-description/{id}', 'RateDescriptionController@update');
        //Route::delete('rate-description/{id}', 'RateDescriptionController@destroy');
        Route::post('rate-description', 'RateDescriptionController@store');
        Route::get('rate-description/{id}', 'RateDescriptionController@getRateDescription');
        Route::get('rate-description', 'RateDescriptionController@index');
        Route::get('rate-description-list/{partner_id}', 'RateDescriptionController@rateDescriptionList');
        //get monthl rates Admin API
        Route::get('facility/monthly-rates/{facility}', 'FacilityController@getMonthlyRates');
        Route::post('facility/monthly-rates/{facility}', 'FacilityController@addFacilityRates');
        Route::post('facility/delete-month-rate/{facilityrate}', 'FacilityController@destroyFacilityRates');

        // to get all loyalty code
        Route::get('loyalty-code', 'LoyaltyCodeController@index');
        Route::get('loyalty-code/page/{page}', ['uses' => 'LoyaltyCodeController@index', 'middleware' => 'pagination']);
        // to get loyalty code by id
        Route::get('loyalty-code/{loyaltycode}', 'LoyaltyCodeController@show');
        // to get loyalty code by code
        Route::get('loyalty-code/code/{code}', 'LoyaltyCodeController@getByCode');
        // add loyalty code
        Route::post('loyalty-code/create', 'LoyaltyCodeController@store');
        // add loyalty code
        Route::put('loyalty-code/{loyaltycode}', 'LoyaltyCodeController@update');
        // add loyalty code
        Route::delete('loyalty-code/{loyaltycode}', 'LoyaltyCodeController@destroy');
        // On signup completion, mapping loyalty code with loyalty user account 
        Route::put('loyalty-code/{loyaltyCodeId}/{loyaltyUserAccountId}', 'LoyaltyCodeController@signupComplete');
        //get unique loyalty code
        Route::get('loyalty-unique-code', 'LoyaltyCodeController@generateLoyaltyCode');
        // Credit POS points
        Route::post('credit-points-transient-user', 'LoyaltyController@creditPointsPOS');

        //monthly campaign APIs
        Route::post('monthly-campaign/{facility}', 'FacilityController@updateMonthlyCampaignPrice');

        //update partner inventory threshold from admin
        Route::post('facility/edit-inventory-threshold', 'FacilityController@updateInventoryThreshold');
        Route::post('facility/edit-realtime-window', 'FacilityController@updateRealtimeWindow');

        Route::resource('/promotions', 'LatestPromotionController');
        Route::get('/getallpromotions', 'LatestPromotionController@getAll');
        Route::get('/getallpromotions/page/{page}',  ['uses' => 'LatestPromotionController@getAll', 'middleware' => 'pagination']);

        Route::get('/getallpromocodes', 'LatestPromotionController@getAllPromocodes');
        Route::get('/getallpromocodes/page/{page}',  ['uses' => 'LatestPromotionController@getAllPromocodes', 'middleware' => 'pagination']);
        Route::get('/getpromocodebyname/{name}', 'LatestPromotionController@getPromocodeByName');
        Route::delete('/deleteByIDPromotion/{id}', 'LatestPromotionController@destroy');

        Route::get('/getPromocodeByPromotion/{promotionId}', 'LatestPromotionController@getPromocodeByPromotion');
        Route::get('/getPromocodeUsers', 'LatestPromotionController@getPromocodeUsers');
        Route::get('/getPromocodeUsers/page/{page}',  ['uses' => 'LatestPromotionController@getPromocodeUsers', 'middleware' => 'pagination']);

        Route::delete('facility-photo/{facility}', 'FacilityController@destroyFacilityPhoto');

        // Deploy : 06-01-2025
        Route::post('/create-promo-board-rate-category', 'LatestPromotionController@storePromoBoardRateCategory');

        //permit rate routs

        Route::put('permit-rate-description/{id}', 'PermitRateDescriptionController@update');
        //Route::delete('rate-description/{id}', 'RateDescriptionController@destroy');
        Route::post('permit-rate-description', 'PermitRateDescriptionController@store');
        Route::get('permit-rate-description/{id}', 'PermitRateDescriptionController@getRateDescription');
        Route::get('permit-rate-description', 'PermitRateDescriptionController@index');
        Route::get('permit-rate-description-list/{partner_id}', 'PermitRateDescriptionController@rateDescriptionList');
        //get monthl rates Admin API
        Route::get('facility/permit-rates/{facility}', 'FacilityController@getPermitRates');
        Route::post('facility/permit-rates/{facility}', 'FacilityController@addPermitRates');
        Route::post('facility/delete-permit-rate/{facilityrate}', 'FacilityController@destroyPermitRates');

        //date 29-04-2024 Permit Rate Day Wise
        Route::post('facility/permit-rates-daywise/{facility}', 'FacilityController@addPermitRatesDayWise');
        Route::get('facility/permit-rates-daywise/{facility}', 'FacilityController@getPermitRatesDayWise');
        Route::post('facility/delete-permit-rate-daywise/{facilityrate}', 'FacilityController@destroyPermitRatesDayWise');
        //date 07-05-2024
        Route::get('permit-criteria-daywise', 'FacilityController@getPermitCriteriaDayWise');
    }
);

// routes for cancellation requests
Route::group(
    [
        'middleware' => ['apiresponse', 'oauth', 'fetch-user', 'timezone', 'role:' . Role::ADMIN . '|' . Role::CUSTOMER_SERVICE . '|' . Role::PARTNER . '|' . Role::SUBPARTNER . '|' . Role::REGIONAL_MANAGER],
        'prefix' => 'cancellation-request'
    ],
    function () {
        Route::put('{id}/cancel', 'CancellationRequestController@updateCancellationRequestStatus');
        Route::put('{id}/update', 'CancellationRequestController@updateCancellationRequestDate');
        Route::get('list', 'CancellationRequestController@getCancellationRequestList');
        Route::get('list/page/{page}', ['uses' => 'CancellationRequestController@getCancellationRequestList', 'middleware' => 'pagination']);
        Route::get('{id}/view', 'CancellationRequestController@getCancellationRequest');
    }
);


//superset dadashboard routes || DATA-28 || Dev:Sagar
Route::group(
    ['middleware' => ['apiresponse', 'oauth', 'timezone', 'fetch-user', 'role:' . Role::MONTHLY_SALES . '|' . Role::ADMIN . '|' . Role::PARTNER . '|' . Role::SUBPARTNER . '|' . Role::REGIONAL_MANAGER. '|' . Role::BUSINESSUSER . '|' . Role::BUSINESSCLERK]],
    function () {
        Route::get('da_dashboards', 'SuperSet\DADashboardController@index');
        Route::post('da_dashboards', 'SuperSet\DADashboardController@store');
        Route::get('da_dashboards/{id}', 'SuperSet\DADashboardController@show');
        Route::put('da_dashboards/{id}', 'SuperSet\DADashboardController@update');
        Route::delete('da_dashboards/{id}', 'SuperSet\DADashboardController@destroy');

        Route::post('/superset/guest-token', 'SuperSet\SupersetController@getGuestToken');

        Route::post('super-set/assign-dashboards', 'SuperSet\DADashboardController@assignDashboardsToPartner');

        Route::post('/partner/dashboards', 'SuperSet\SupersetController@getDashboardsByPartner');
    }
);
