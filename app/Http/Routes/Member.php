<?php
// MEMBER ROUTES FILE
Route::group(
    ['prefix' => 'apple-wallet'
    ], function () {
    //Route::group(['middleware' => ['apiresponse', 'oauth', 'fetch-user', 'visitor']], function () {


    Route::post('generate', ['uses' => 'AppleWalletController@generateWalletPass', 'middleware' =>['apiresponse', 'oauth', 'fetch-user','visitor']]);


    Route::post('{version}/devices/{deviceLibraryIdentifier}/registrations/{passTypeIdentifier}/{serialNumber}', ['uses' => 'AppleWalletController@registerADevice', 'middleware' =>['apiresponse']]);
    Route::delete('{version}/devices/{deviceLibraryIdentifier}/registrations/{passTypeIdentifier}/{serialNumber}', ['uses' => 'AppleWalletController@unRegisterADevice', 'middleware' => ['apiresponse']]);
    Route::get('{version}/devices/{deviceLibraryIdentifier}/registrations/{passTypeIdentifier}', 'AppleWalletController@getSerialNumbersByDevice');
   
    Route::get('{version}/passes/{passTypeIdentifier}/{serialNumber}', 'AppleWalletController@getUpdatedPass');
    Route::post('send_push_notifiction', 'AppleWalletController@sendPushNotification');
    Route::post('{version}/log', 'AppleWalletController@logAppleWalletError');
    //admin specific routes
});


Route::group(['middleware' => ['apiresponse', 'oauth', 'fetch-user', 'visitor']], function () {
    
        // Verify authentication
        Route::get('api', 'UserController@self');

        Route::get('loggedin-user-reservation-details/{reservationCode}/{ticketech_code}', 'ReservationController@getReservationDetailsByUser');





        // Verify different roles
        Route::get('user/admin', ['uses' => 'UserController@admin']);
        Route::get('user/cms-access', ['uses' => 'UserController@cmsAccess']);
        Route::get('user/mp-sales', ['uses' => 'UserController@mpSales']);

        // Reservations
        Route::get('user/{userId}/reservation', 'ReservationController@getUserReservations');
        Route::get('user/{userId}/reservation/page/{page}', ['uses' => 'ReservationController@getUserReservations', 'middleware' => 'pagination']);
        //checkin checkout
        Route::get('user/{userId}/checkincheckout/page/{page}', ['uses' => 'TicketController@getUserCheckinCheckout', 'middleware' => 'pagination']);
        
        Route::post('user/reservation', 'ReservationController@makeUserReservation');

        Route::post('user/reservation-apple-pay', 'ReservationController@makeUserReservationApplePay');

        Route::post('reservation/{reservation}/cancel', 'ReservationController@cancelReservationById');
        Route::get('/reservation/{reservation}', 'ReservationController@getById');
        Route::post('reservation/{reservationId}', 'ReservationController@getReservationById');

        // Reservation cims
        Route::get('user/authorizenet/cim', 'AuthorizeNetController@getUserCim');
        Route::post('user/{user}/payment', 'AuthorizeNetController@saveUserPayment');
        Route::delete('user/authorizenet/payment', 'AuthorizeNetController@deleteUserPayment');

        // Save coupon to account
        Route::post('user/{userId}/coupon', 'SavedCouponController@saveCoupon');
        Route::get('user/{userId}/coupon', 'SavedCouponController@getSavedCoupons');

        Route::post('user/{userId}/couponMob', 'SavedCouponController@saveCouponMob');
        Route::get('user/{userId}/couponMob', 'SavedCouponController@getSavedCouponsMob');

        Route::get('user/{userId}/coupon/page/{page}', ['uses' => 'SavedCouponController@getSavedCoupons', 'middleware' => 'pagination']);
        Route::delete('user/coupon/{coupon}', 'SavedCouponController@deleteSavedCoupon');

        // Account management
        Route::post('user/{user}', 'UserController@edit');

        // Reset user password
        Route::post('user/{user}/reset-password', 'PasswordController@resetPasswordFromCurrent'); // reset password via sending in current password
        Route::post('user/{user}/reset-email', 'UserController@emailReset'); // reset email

        // Monthly parking
        Route::post('monthly-parking/register', 'MonthlyParkingUserController@registerAccount'); // link a new account
        Route::post('mp/register', 'MonthlyParkingUserController@registerAccount');
        Route::get('monthly-parking/account', 'MonthlyParkingUserController@getUserAccounts'); // get currently linked accounts
        Route::get('monthly-parking/account-numbers', 'MonthlyParkingUserController@getUserAccountIds'); // get currently linked account numbers
        Route::get('monthly-parking/account/{account}', 'MonthlyParkingUserController@getAccount');

        //to check if user is having any monthly account or not
        Route::post('monthly-parking/verify-user-accounts', 'MonthlyParkingUserController@checkUserMonthlyAccounts');
        
        Route::get('elimiwait', 'ElimiwaitRequestController@index');
        Route::get('elimiwait/request/{elimiwaitAccount}', 'ElimiwaitRequestController@updateRequests');
        Route::post('elimiwait/request/{elimiwaitAccount}', 'ElimiwaitRequestController@request');
        Route::post('elimiwait/{monthlyParkingUser}', 'ElimiwaitRequestController@show');

        Route::post('elimiwait-account', 'ElimiwaitAccountController@store');
        Route::put('elimiwait-account/{elimiwaitAccount}', 'ElimiwaitAccountController@update');
        Route::delete('elimiwait-account/{elimiwaitAccount}', 'ElimiwaitAccountController@delete');
        Route::get('elimiwait-account/accounts/{user}', 'ElimiwaitAccountController@getUserAccounts');
        Route::get('elimiwait-account/requests/{user}', 'ElimiwaitAccountController@getActiveRequests');
        Route::get('elimiwait-account/{elimiwaitAccount}', 'ElimiwaitAccountController@show');
        
        Route::put('update-profile', 'UserController@updateProfile');

        // loyalty accounts:
        /*Route::get('loyalty', 'LoyaltyController@index');
        Route::put('loyalty/register', 'LoyaltyController@register');
        Route::put('loyalty/update-profile', 'LoyaltyController@updateUserProfile');
        Route::get('loyalty/accounts', 'LoyaltyController@accounts');
        Route::post('loyalty/card', 'LoyaltyController@addCard');
        Route::get('loyalty/cards', 'LoyaltyController@getCards');
        Route::get('loyalty/card/{id}', 'LoyaltyController@viewCard');
        Route::delete('loyalty/card/{id}', 'LoyaltyController@deleteCard');
        Route::put('loyalty/card/{id}', 'LoyaltyController@updateCard');
        Route::get('loyalty/history', 'LoyaltyController@getHistory');
        Route::get('loyalty/digital-card', 'LoyaltyController@getDigitalCard');
        Route::post('loyalty/add-loyalty-card', 'GoogleWallet\LoyaltyObjectController@addLoyaltyCard');
        Route::post('loyalty/getLoyaltyObject', 'GoogleWallet\LoyaltyObjectController@getLoyaltyObject');
*/
        Route::get('user/wallets', 'WalletController@getUserWallets');

		/*  
		Combined API for Saved Card List, Icon Go Credits, Saved Promo Code and Status Of Promo Code Subscription
		*/
	 Route::get('/user/get-user-details', 'AuthorizeNetController@getUserDetails');
		
		
    }
);

