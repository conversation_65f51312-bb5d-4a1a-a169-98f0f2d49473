<?php

Route::group(['middleware' => ['apiresponse', 'timezone']], function () {

    Route::post('authenticate-partner/{secret}', 'Swagger<PERSON>ontroller@authenticatePartner');
    Route::post('getTicketDetails/{secret}', 'Swagger<PERSON>ontroller@getTicketDetails');
    Route::post('getActiveTicketDetails/{secret}', 'Swagger<PERSON>ontroller@getActiveTicketDetails');
    Route::post('getPermitDetails/{secret}', 'SwaggerController@getPermitDetailsV1');
    // Route::post('V1/getPermitDetails/{secret}', 'SwaggerController@getPermitDetailsV1');
    Route::post('v2/getPermitDetails/{secret}', 'SwaggerController@getPermitDetailsV2');
    Route::post('getPermitAccountDetails/{secret}', 'Swagger<PERSON>ontroller@getPermitAccountDetails');
    Route::post('getPermitActivityDetails/{secret}', '<PERSON>wa<PERSON><PERSON><PERSON>roller@getPermitActivityDetails');
    Route::post('getPassDetails/{secret}', 'SwaggerController@getPassDetails');
    Route::post('getReservationDetails/{secret}', 'SwaggerController@getReservationDetails');

    // Deployed Vijay : 28-02-2024
    Route::get('getScannedVehiclesUserList', 'SwaggerController@getScannedVehiclesUserList');


    // testing API 
    Route::post('event-logs/{event_name}', 'UserEventsLogController@getUserEventLogs');
    Route::get('get-user-details/{email}', 'UserEventsLogController@getUserDetaiils');

    #KT : 23-12-2024
    Route::post('getWhitelistedVehicle/{secret}', 'SwaggerController@getWhitelistedVehicle');

    #KT : 13-01-2025
    Route::get('create-update-reservation-hub-zeag/{reservation_id}/{method}', 'HubZeag\HubZeagController@dispatchJobForParkingHub'); //create and update on hubzeag
    Route::delete('cancel-reservation-hub-zeag/{reservation_id}', 'HubZeag\HubZeagController@cancelReseravtionForParkingHub'); //cancelled on hubzeag
    Route::get('checkin-out-hub-zeag-by-date', 'HubZeag\HubZeagController@checkInDataForParkingHubByDate');
    Route::get('checkin-out-hub-zeag-by-booking-id', 'HubZeag\HubZeagController@checkInDataForParkingHubByBookingID');
    Route::get('reservation-hub-zeag-by-date', 'HubZeag\HubZeagController@reservationDataForParkingHubByDate');
    Route::get('reservation-hub-zeag-by-booking-id', 'HubZeag\HubZeagController@reservationDataForParkingHubByBookingID');
});
