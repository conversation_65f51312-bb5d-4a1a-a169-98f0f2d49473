<?php

namespace App\Http\Helpers;

use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Models\Facility;
use Illuminate\Support\Facades\DB;

use App\Models\ParkEngage\FacilityOvernightDuration;
use App\Models\ParkEngage\OverstayTicket;
use App\Models\ParkEngage\TicketExtend;
use App\Models\ParkEngage\BrandSetting;

class ReportBuilder
{
    protected $partnerId;
    protected $facilityId;
    protected $checkinTime;
    protected $checkoutTime;
    protected static $driveString;
    protected static $driveString2;
    protected static $reservationString;
    protected static $eventString;
    protected static $eventStringPayment;
    protected static $eventStringPaymentWithoutBooking;
    protected static $permitString;
    protected $logFactory;


    public function __construct(Request $request)
    {
        $this->partnerId = $request->partner_id;
        $this->facilityId = $request->facility_id;


        $fromdate = isset($request->fromDate) ? date('Y-m-d', strtotime($request->fromDate))  : date('Y-m-d');
        $toDate = isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate))  : date('Y-m-d');

        $this->checkinTime      = date('Y-m-d', strtotime($fromdate)) . ' 00:00:00';
        $this->checkoutTime     = date('Y-m-d', strtotime($toDate)) . ' 23:59:59';

        if ($this->facilityId == '158') {
            //self::$driveString = "t.partner_id IN ({$this->partnerId}) AND ((t.checkout_time IS NULL AND t.grand_total IS NOT NULL AND t.checkin_time BETWEEN '" . $this->checkinTime . "' AND '" . $this->checkoutTime . "') OR (t.checkout_time BETWEEN '" . $this->checkinTime . "' AND '" . $this->checkoutTime . "')) AND  t.facility_id IN (" . $this->facilityId . ")  AND t.reservation_id is null AND t.deleted_at is null AND t.permit_request_id is null ";
            self::$driveString = "t.partner_id IN ({$this->partnerId}) AND t.checkout_time >= '" . $this->checkinTime . "' AND t.checkout_time <= '" . $this->checkoutTime . "' AND  t.facility_id IN (" . $this->facilityId . ") AND t.is_checkout ='1' AND t.reservation_id is null AND t.deleted_at is null AND t.permit_request_id is null AND t.event_id is null";


            self::$driveString2 = "t.partner_id IN ({$this->partnerId}) AND t.checkin_time >= '" . $this->checkinTime . "' AND t.checkin_time <= '" . $this->checkoutTime . "' AND  t.facility_id IN (" . $this->facilityId . ") AND t.is_checkout ='1' AND t.reservation_id is null AND t.deleted_at is null AND t.permit_request_id is null";
        } else {
            self::$driveString = "t.partner_id IN ({$this->partnerId}) AND t.checkout_time >= '" . $this->checkinTime . "' AND t.checkout_time <= '" . $this->checkoutTime . "' AND  t.facility_id IN (" . $this->facilityId . ") AND t.is_checkout ='1' AND t.reservation_id is null AND t.deleted_at is null AND t.permit_request_id is null ";
        }


        self::$reservationString = "r.partner_id IN ({$this->partnerId}) AND r.start_timestamp >= '" . $this->checkinTime . "' AND r.start_timestamp <= '" . $this->checkoutTime . "' AND  r.facility_id IN (" . $this->facilityId . ") AND r.deleted_at is null AND r.cancelled_at is null";

        self::$eventStringPayment = "e.partner_id IN ({$this->partnerId}) AND (e.start_time >= '" . $this->checkinTime . "' AND e.start_time <= '" . $this->checkoutTime . "') AND e.deleted_at is null AND r.cancelled_at is null AND  r.facility_id IN (" . $this->facilityId . ")";

        self::$eventStringPaymentWithoutBooking = "e.partner_id IN ({$this->partnerId}) AND (e.start_time >= '" . $this->checkinTime . "' AND e.start_time <= '" . $this->checkoutTime . "' AND t.checkin_time >= '" . $this->checkinTime . "' AND t.checkin_time <= '" . $this->checkoutTime . "') AND e.deleted_at is null AND t.facility_id IN (" . $this->facilityId . ") AND t.event_id is not null AND t.deleted_at is null";

        self::$eventString = "e.partner_id IN ({$this->partnerId}) AND (e.start_time >= '" . $this->checkinTime . "' AND e.start_time <= '" . $this->checkoutTime . "' AND t.checkin_time >= '" . $this->checkinTime . "' AND t.checkin_time <= '" . $this->checkoutTime . "') AND e.deleted_at is null AND  ef.facility_id IN (" . $this->facilityId . ")";
        // e.partner_id IN ('$partner_id') AND r.deleted_at is null  and e.start_time >='$checkInTime' and e.start_time <='$checkInTime1' $RfacilityID

        self::$permitString = "pr.partner_id IN ({$this->partnerId}) AND pr.created_at >= '" . $this->checkinTime . "' AND pr.created_at <= '" . $this->checkoutTime . "' AND  pr.facility_id IN (" . $this->facilityId . ") AND  pr.status ='1' and pr.cancelled_at is null AND pr.deleted_at is null ";
        //pr.deleted_at IS NULL AND pr.partner_id IN ('$partner_id')  $permitFacilityId  AND pr.created_at >='$checkInTime' and pr.created_at <='$checkInTime1'
        //pr.partner_id IN ('$partner_id') AND pr.created_at >='$checkInTime' AND pr.created_at <='$checkInTime1' AND pr.deleted_at is null AND pr.facility_id = '$facility_id' and ant.card_type is not null and pr.status ='1' and pr.cancelled_at is null
    }

    public static function currencyFormat($value)
    {
        return sprintf("%.2f", $value);
    }

    public static function getGatedDriveup()
    {
        // Non Validated
        $allDriveupData = [];
        $sqlQuery = "SELECT COUNT(distinct(t.id)) AS ticketCount,	
                t.total AS total, 
                t.grand_total AS grand_total, 
                t.parking_amount AS parking_amount, 
                IF(t.grand_total >0,t.parking_amount,t.grand_total) AS NetAmount, 
                t.discount_amount AS discount_amount, 
                t.processing_fee AS processing_fee, 
                t.tax_fee AS tax_fee, 
                t.paid_amount AS validated_amount, 
                
                SUM(ot.total) as overstayTotal, 
                SUM(ot.tax_fee) as overstayTaxFee, 
                SUM(ot.processing_fee) as overstayProcessingFee, 
                SUM(ot.grand_total) as overstayGrandTotal, 
                SUM(ot.discount_amount) as overstayDiscount,
                t.ticket_number            
                FROM 
                tickets AS t 
                left join overstay_tickets as ot on ot.ticket_id = t.id           
                where  " . self::$driveString . " AND t.paid_type='9' group by t.ticket_number order by t.parking_amount ASC";
        $results = DB::select($sqlQuery);
        return $results;
    }

    public static function GatedValidatedTickets()
    {
        // Validated tickets Query
        $sqlQuery = "SELECT COUNT(t.ticket_number) AS ticketCount, 
            t.paid_by,
            sum(t.processing_fee) as processing_fee,
            sum(t.tax_fee) as tax_fee,
            sum(t.total) as total,                
            sum(t.grand_total) as grand_total,                
            sum(t.parking_amount) as parking_amount,
            sum(t.paid_amount) as validated_amount,
            sum(t.discount_amount) as discount_amount,
            t.affiliate_business_id,
            t.ticket_number,
            ab.business_name AS BusinessName ,
            p.policy_name as policyName
            FROM tickets as t        
            inner join affiliate_business as ab on ab.id = t.affiliate_business_id 
            inner join business_policy as p on p.id = t.policy_id
            WHERE " . self::$driveString . "  and t.affiliate_business_id is not null and paid_by > 0 GROUP BY t.affiliate_business_id,t.ticket_number order by t.affiliate_business_id";
        $results = DB::select($sqlQuery);
        return $results;
    }

    public static function getPermitTickets()
    {
        $sqlQuery = "SELECT pr.permit_rate, count(DISTINCT pr.id) as permitCount, count(DISTINCT t.id) as ticketCount, pr.id as permit_id, pr.account_number, sum(ant.total) as total
           FROM permit_requests as pr
           inner join anet_transactions as ant on ant.id = pr.anet_transaction_id  
           left join tickets as t on t.permit_request_id = pr.id        
           where " . self::$permitString . " group by pr.permit_rate,pr.account_number order by pr.permit_rate asc";
        // dd($permitData);
        $results = DB::select($sqlQuery);
        return $results;
    }

    public static function getEventsTickets()
    {
        #inner join mapco_qrcodes as mqr on mqr.event_id= e.id
        #left join tickets as t on t.reservation_id = r.id   ;; Removed : 13-06-2024 based on discussion 
        $sqlQuery = "SELECT count(distinct r.id) as bookingCount, count(distinct t.id) as ticketCount, e.id as event_id, e.title, e.event_rate,
        r.total as event_total, 
        t.grand_total,
        SUM(ot.grand_total) as overstayGrandtotal, 
        r.ticketech_code as bookingID, 
        t.ticket_number, 
        r.total,
        r.is_cash_payment
        FROM events as e
        inner join event_facility as ef on ef.event_id= e.id        
        left join reservations as r on r.event_id= e.id
        left join tickets as t on t.event_id = e.id
        left join overstay_tickets as ot on t.id = ot.ticket_id 
        WHERE " . self::$eventString . " AND t.deleted_at is null group by e.id,t.ticket_number order by e.title asc ";
        // dd('11');
        $results =  DB::select($sqlQuery);
        return $results;
        // dd('11'); ##left join tickets as t on t.reservation_id = r.id
    }

    public static function getEventGatedDriveup()
    {
        // Non Validated
        $allDriveupData = [];
        $sqlQuery = "SELECT COUNT(distinct(t.id)) AS ticketCount,	
                t.total AS total, 
                t.grand_total AS grand_total, 
                t.parking_amount AS parking_amount, 
                IF(t.grand_total >0,t.parking_amount,t.grand_total) AS NetAmount, 
                t.discount_amount AS discount_amount, 
                t.processing_fee AS processing_fee, 
                t.tax_fee AS tax_fee, 
                t.paid_amount AS validated_amount, 
                
                SUM(ot.total) as overstayTotal, 
                SUM(ot.tax_fee) as overstayTaxFee, 
                SUM(ot.processing_fee) as overstayProcessingFee, 
                SUM(ot.grand_total) as overstayGrandTotal, 
                SUM(ot.discount_amount) as overstayDiscount,
                t.ticket_number            
                FROM 
                tickets AS t 
                left join overstay_tickets as ot on ot.ticket_id = t.id           
                where  " . self::$driveString . " AND t.paid_type='9' group by t.ticket_number order by t.parking_amount ASC";
        $results = DB::select($sqlQuery);
        return $results;
    }

    public static function getUngatedDriveup($partner_id, $facility_id, $checkInTime, $checkOutTime)
    {
        // Non Validated
        //$allDriveupData = [];
        $rowKey = 0;
        $outerLoopIndex = 0;
        $totalExtendedAmount = 0;
        // $validationAmountTotal = 0;

        // $validationPaidAmountTotal = 0;
        // $totalGrossAmount = 0;
        // $validationTicketTotal = 0;
        // $validatedGTotal = 0;
        // $totalNetAmount = 0;
        // $totalServiceAmount = 0;
        // $finalCodes3 = [];
        // $TotalCc = 0;
        // $rTticketCount = $rTicketAmount = $rGrossAmount = $rProcessingFees = $rNetAmonut = $rDiscountAmount = 0;
        $TotalTicketAmount = 0;
        $totalExtentionCount = 0;
        $TotalRevenueNonValidated = 0;

        $totalTicketsNonValidated = 0;
        $netValueNonValidated = 0;
        $validatedAmountNonValidated = 0;
        $processingFeeNonValidated = 0;
        $totalExtendedAmount = 0;
        $TotalTicketAmount = 0;
        $totalDriveUpDuration = $totalReservationDurtion = 0;
        $totalDiscountAmountNonValidated = $validatedCheckinAmountNonValidated = $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = 0;
        $finalCodes5 = [];

        $driveupSql = "SELECT COUNT(distinct(t.id)) AS ticketCount,
                                    GROUP_CONCAT(distinct(t.id)) as  t_ids,
                                    COALESCE(t.grand_total, 0) AS grand_total,
                                    COALESCE(t.grand_total, 0) AS payAmount,
                                    COALESCE(t.processing_fee, 0) AS processingFeePerTicket,
                                    COALESCE(t.parking_amount, 0) AS parkingAmount,
                                    SUM(COALESCE(t.tax_fee, 0)) AS tax_fee,
                                    (t.processing_fee* COUNT(distinct(t.id))) AS processing_fee,
                                    SUM(COALESCE(t.grand_total, 0)) AS Pay_grand_total,
                                    SUM(COALESCE(IF(t.grand_total > 0,t.parking_amount,t.grand_total), 0)) AS net_amount,
                                    SUM(COALESCE(t.discount_amount, 0)) AS discount_amount,
                                    SUM(COALESCE(t.paid_amount, 0)) AS validated_amount,group_concat(t.id) as t_id,
                                    t.length,
                                    t.total,
                                    t.is_extended,
                                    t.promocode,
                                    t.refund_amount,
                                     SUM(COALESCE(t.refund_amount, 0)) AS refundamount,
                                    case when  t.is_extended = '1' then
                                        ((SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) + t.grand_total )
                                    ELSE
                                        t.grand_total
                                    END
                                    AS final_sum,
                                    count(te.id) as no_of_extentions,
                                    sum(te.grand_total)  as overstayGrandTotal, 
                                    sum(te.discount_amount) as overstayDiscount,
                                    CASE
                                        WHEN t.is_extended = '1' THEN sum(CEIL(COALESCE(t.length, 0)  ))
                                        ELSE SUM(CEIL(COALESCE(t.length, 0)))
                                    END AS lenghtInMints             
                                    from tickets as t
                                    left join ticket_extends as te on te.ticket_id = t.id 
                                    where t.partner_id IN ({$partner_id}) AND t.checkout_time >= '" . $checkInTime . "' AND t.checkout_time <= '" . $checkOutTime . "'  AND t.is_checkout ='1' AND t.reservation_id is null AND t.deleted_at is null AND t.permit_request_id is null and t.event_id is null and  t.paid_type='9'  group by parkingAmount  order by parkingAmount ASC";
        $driveupResult = DB::select($driveupSql);
        // return $driveupResult;
        $totalNewRevenueAmount = 0;
        foreach ($driveupResult as $key => $value) {
            // $finalCodes5[$rowKey]['Duration (Hours)']    = intval($value->lenghtInMints);
            $finalCodes5[$rowKey]['Rate ($)']               = floatval($value->parkingAmount);
            $finalCodes5[$rowKey]['No of Tickets']          = intval($value->ticketCount);
            $finalCodes5[$rowKey]['Ticket Number']          = '';
            $finalCodes5[$rowKey]['Transaction Date']    = '-';
            $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($value->parkingAmount) ? floatval($value->parkingAmount) : '0.00';
            $finalCodes5[$rowKey]['No of Extensions']        = floatval($value->no_of_extentions);
            $finalCodes5[$rowKey]['Extension Amount ($)']   = floatval($value->overstayGrandTotal);
            $finalCodes5[$rowKey]['Net Amount ($)']         = floatval($value->net_amount + ($value->overstayGrandTotal ?? 0));
            $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($value->processing_fee);
            $finalCodes5[$rowKey]['Total Collected ($)']    = floatval($value->Pay_grand_total + ($value->overstayGrandTotal ?? 0));
            $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($value->discount_amount  + ($value->overstayDiscount ?? 0));
            $totalNewRevenueAmount += floatval($value->net_amount + ($value->overstayGrandTotal ?? 0));

            $driveupDataTickets = "SELECT t.id AS ticketCount,
                                      t.discount_amount AS discount_amount,
                                      t.ticket_number,
                                      t.length,
                                      t.total,
                                      t.grand_total as grand_total,
                                      t.processing_fee,
                                      t.parking_amount, DATE_FORMAT(ant.created_at,'%m-%d-%Y') as transaction_date,
                                      case when  t.is_extended = '1' then
                                          ((SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) + t.grand_total )
                                      ELSE
                                          t.grand_total
                                      END
                                      AS final_sum,
                                      count(te.id) as no_of_extentions,
                                      sum(te.grand_total)  as overstayGrandTotal, 
                                      sum(te.discount_amount) as overstayDiscount
                                      from tickets as t
                                      left join anet_transactions as ant on ant.id = t.anet_transaction_id
                                      left join ticket_extends as te on te.ticket_id = t.id
                                      where t.partner_id IN ({$partner_id}) AND t.checkout_time >= '" . $checkInTime . "' AND t.checkout_time <= '" . $checkOutTime . "'  AND t.is_checkout ='1' AND t.reservation_id is null AND t.deleted_at is null AND t.permit_request_id is null and t.event_id is null and  t.paid_type='9'
                                      and t.parking_amount = $value->parkingAmount group by t.ticket_number";
            $driveupDataTicketResults = DB::select($driveupDataTickets);
            $a[] = $driveupDataTicketResults;
            $processingFeein = $extedGrandTotal = $extedDiscount = $extedNetTotal = 0;
            $outerLoopIndex = $rowKey;
            $rowKey++;
            // dd($driveupDataTicketResults);
            if (count($driveupDataTicketResults) > 0) {

                foreach ($driveupDataTicketResults as $tkey => $ticket) {
                    //  $netRev=0.00;
                    // if($ticket->grand_total + ($ticket->overstayGrandTotal)<=0){
                    //  $netRev=0.00;
                    // }
                    // dd($driveupDataTicketResults);
                    $finalCodes5[$rowKey]['Rate ($)']               = '';
                    $finalCodes5[$rowKey]['No of Tickets']          = '-';
                    $finalCodes5[$rowKey]['Ticket Number']          = $ticket->ticket_number;
                    $finalCodes5[$rowKey]['Transaction Date']    = $ticket->transaction_date;
                    $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($ticket->parking_amount) ? floatval($ticket->parking_amount) : '0.00';
                    $finalCodes5[$rowKey]['No of Extensions']       = floatval($ticket->no_of_extentions);
                    $finalCodes5[$rowKey]['Extention Amount ($)']   = floatval($ticket->overstayGrandTotal);
                    $finalCodes5[$rowKey]['Net Amount ($)']         = (($ticket->grand_total + $ticket->overstayGrandTotal) > 0) ? floatval($ticket->parking_amount + $ticket->overstayGrandTotal) : 0;
                    $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($ticket->processing_fee);
                    $finalCodes5[$rowKey]['Total Collected ($)']    = floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
                    $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                    $rowKey++;
                    // $processingFeein += floatval($ticket->parking_amount) ;
                    $extedNetTotal += (($ticket->grand_total + $ticket->overstayGrandTotal) > 0) ? floatval($ticket->parking_amount + $ticket->overstayGrandTotal) : 0;
                    $extedGrandTotal += floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
                    $extedDiscount += floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                }
            }
            $totalExtendedAmount           += floatval($value->overstayGrandTotal);
            // $finalCodes5[$outerLoopIndex]['Processing Fees ($)']         = floatval($processingFeein);
            $finalCodes5[$outerLoopIndex]['Net Amount ($)']         = floatval($extedNetTotal);
            $finalCodes5[$outerLoopIndex]['Total Collected ($)']    = floatval(($extedGrandTotal));
            $finalCodes5[$outerLoopIndex]['Discount Amount ($)']    = floatval(($extedDiscount));
            $TotalTicketAmount              += '0.00';
            $netValueNonValidated           += '0.00';
            $totalDriveUpDuration           += $value->lenghtInMints;
            $totalExtentionCount            += $value->no_of_extentions;
            $totalTicketsNonValidated       += $value->ticketCount;
            $TotalRevenueNonValidated       += floatval(($extedGrandTotal));;
            $processingFeeNonValidated      += floatval($value->processing_fee);
            $validatedAmountNonValidated    += floatval($value->validated_amount);
            $totalDiscountAmountNonValidated += floatval(($extedDiscount));
        }

        $totalSummary = [$totalTicketsNonValidated, '-',  '-', '-', $totalExtentionCount, $totalExtendedAmount,  $totalNewRevenueAmount, $processingFeeNonValidated,  $TotalRevenueNonValidated, $totalDiscountAmountNonValidated];
        return [$finalCodes5, $totalSummary];
    }

    public function backup_getUngatedDriveup()
    {
        // Non Validated
        $allDriveupData = [];
        $driveupSql = "SELECT COUNT(distinct(t.id)) AS ticketCount,
            COALESCE(t.grand_total, 0) AS grand_total,
            COALESCE(t.grand_total, 0) AS payAmount,
            SUM(COALESCE(t.tax_fee, 0)) AS tax_fee,
            SUM(COALESCE(t.processing_fee, 0)) AS processing_fee,
            SUM(COALESCE(t.grand_total, 0)) AS Pay_grand_total,
            SUM(COALESCE(t.parking_amount, 0)) AS net_amount,
            SUM(COALESCE(t.discount_amount, 0)) AS discount_amount,
            SUM(COALESCE(t.paid_amount, 0)) AS validated_amount,             
            GROUP_CONCAT(t.id) as tickets,
            SUM(tex.grand_total) as overstayGrandTotal, 
            SUM(tex.discount_amount) as overstayDiscount
            from tickets  t
            left join ticket_extends as tex on t.id = tex.ticket_id            
            where  " . $this->driveString . " group by t.ticket_number order by t.grand_total";
        $driveupResult = DB::select($driveupSql);
        return $driveupResult;

        $rowKey = 0;
        $outerLoopIndex = 0;
        foreach ($driveupResult as $key => $value) {
            $allDriveupData[$rowKey]['Rate ($)']               = floatval($value->payAmount);
            $allDriveupData[$rowKey]['Ticket Number']          = '';
            $allDriveupData[$rowKey]['No of Tickets']          = intval($value->ticketCount);
            $allDriveupData[$rowKey]['Ticket Amount ($)']      = isset($value->total) ? floatval($value->total) : '0.00';
            $allDriveupData[$rowKey]['Gross Amount ($)']       = floatval($value->Pay_grand_total + ($value->overstayGrandTotal ?? 0));
            $allDriveupData[$rowKey]['Processing Fees ($)']    = floatval($value->processing_fee);
            $allDriveupData[$rowKey]['Net Amount ($)']         = floatval($value->net_amount);
            $allDriveupData[$rowKey]['Discount Amount ($)']    = floatval($value->discount_amount  + ($value->overstayDiscount ?? 0));

            $ticketsSql = "SELECT t.id AS ticketCount,
                                    t.discount_amount AS discount_amount,
                                    t.ticket_number,
                                    t.length,
                                    t.total,
                                    t.grand_total as grand_total,
                                    t.processing_fee,
                                    t.parking_amount,
                                    SUM(tex.grand_total) as overstayGrandTotal, 
                                    SUM(tex.discount_amount) as overstayDiscount
                                    from tickets  t
                                    left join ticket_extends as tex on t.id = tex.ticket_id
                                    where  " . $this->driveString . " AND t.grand_total = " . $value->payAmount . " group by t.ticket_number order by t.grand_total";
            $ticketsResults = DB::select($ticketsSql);
            return $ticketsResults;
            // $a[] = $ticketsResults;
            // $extedGrandTotal = $extedDiscount = 0;
            // $outerLoopIndex = $rowKey;
            // if (count($ticketsResults) > 0) {

            //     $rowKey++;
            //     foreach ($ticketsResults as $tkey => $ticket) {
            //         $allDriveupData[$rowKey]['Rate ($)']              = '';
            //         $allDriveupData[$rowKey]['Ticket Number']          = $ticket->ticket_number;
            //         $allDriveupData[$rowKey]['No of Tickets']          = 1;
            //         $allDriveupData[$rowKey]['Ticket Amount ($)']      = isset($ticket->total) ? floatval($ticket->total) : '0.00';
            //         // $allDriveupData[$rowKey]['Duration (Hours)']       = intval($ticket->lenghtInMints);
            //         $allDriveupData[$rowKey]['Gross Amount ($)']       = floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
            //         $allDriveupData[$rowKey]['Processing Fees ($)']    = floatval($ticket->processing_fee);
            //         $allDriveupData[$rowKey]['Net Amount ($)']         = floatval($ticket->parking_amount);
            //         $allDriveupData[$rowKey]['Discount Amount ($)']    = floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
            //         $rowKey++;
            //         $extedGrandTotal += floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
            //         $extedDiscount += floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
            //     }
            // }

            // $finalCodes5[$outerLoopIndex]['Gross Amount ($)']       = floatval(($extedGrandTotal));
            // $finalCodes5[$outerLoopIndex]['Discount Amount ($)']    = floatval(($extedDiscount));

            // $rowKey++;


            // $TotalTicketAmount              += '0.00';
            // $netValueNonValidated           += '0.00';
            // $totalDriveUpDuration           += $value->lenghtInMints;
            // $totalTicketsNonValidated       += $value->ticketCount;
            // $TotalRevenueNonValidated       += floatval(($extedGrandTotal));;
            // $processingFeeNonValidated      += floatval($value->processing_fee);
            // $validatedAmountNonValidated    += floatval($value->validated_amount);
            // $totalDiscountAmountNonValidated += floatval(($extedDiscount));
        }
    }

    // Specific to Drive Up
    public static function convertArrayToDriveUpGated($products, $type = 'driveup')
    {
        // Sample array of products with prices
        /*  $products = [
            ['id' => 1, 'name' => 'Laptop', 'category' => 'Electronics', 'price' => 1000],
            ['id' => 2, 'name' => 'Smartphone', 'category' => 'Electronics', 'price' => 800],
            ['id' => 3, 'name' => 'T-shirt', 'category' => 'Clothing', 'price' => 20],
            ['id' => 4, 'name' => 'Jeans', 'category' => 'Clothing', 'price' => 50],
            ['id' => 5, 'name' => 'Headphones', 'category' => 'Electronics', 'price' => 100],
        ]; */
        // Initialize an empty result array
        $result = [];
        // $result['overall'] = [];
        $result['overall']['alltaxFee']          = 0;
        $result['overall']['allTicketAmount']    = 0;
        $result['overall']['allprocessingFee']   = 0;
        $result['overall']['allgrandTotal']      = 0;
        $result['overall']['allticketsCount']    = 0;
        $result['overall']['allparkingAmount']   = 0;
        $result['overall']['alldiscountAmount']  = 0;
        $result['overall']['allpaidAmount']      = 0;
        $result['overall']['allfinalAmount']     = 0;

        // Group elements by the 'category' key and calculate the sum of prices
        foreach ($products as $product) {
            if ($type == 'driveup')
                $category = $product->parking_amount;

            if ($type == 'validated')
                $category = $product->affiliate_business_id;


            // If the category key doesn't exist in the result array, create it
            if (!isset($result[$category])) {
                $result[$category] = [
                    'tickets' => [],
                    'taxFee' => 0,
                    'processingFee' => 0,
                    'TicketAmount' => 0,
                    'grandTotal' => 0,
                    'ticketsCount' => 0,
                    'parkingAmount' => 0,
                    'discountAmount' => 0,
                    'paidAmount' => 0,
                    'finalAmount' => 0,
                    'affiliate_business_id' => 0,
                    'businessName' => '',
                ];
            }

            // Append the current product to the category
            $result[$category]['tickets'][] = $product;

            // Add the price of the current product to the total price of the category
            $result[$category]['taxFee']            += $product->tax_fee + ($product->overstayTaxFee ?? 0);
            $result[$category]['TicketAmount']      += $product->total + ($product->overstayTotal ?? 0);
            $result[$category]['processingFee']     += $product->processing_fee + ($product->overstayProcessingFee ?? 0);
            $result[$category]['grandTotal']        += $product->grand_total + ($product->overstayGrandTotal ?? 0);
            $result[$category]['ticketsCount']      += $product->ticketCount;
            $result[$category]['parkingAmount']     += $product->grand_total > 0 ? $product->parking_amount : 0;
            $result[$category]['discountAmount']    += $product->discount_amount + ($product->overstayDiscount ?? 0);
            $result[$category]['paidAmount']        += $product->validated_amount;
            $result[$category]['finalAmount']       += ($product->grand_total + ($product->overstayGrandTotal ?? 0)) + ($product->discount_amount + ($product->overstayDiscount ?? 0));

            // Valiation Related 
            $result[$category]['businessName']              = isset($product->BusinessName) ? $product->BusinessName : '';
            $result[$category]['affiliate_business_id']     = isset($product->affiliate_business_id) ? $product->affiliate_business_id : 0;

            $result['overall']['alltaxFee']            += $product->tax_fee + ($product->overstayTaxFee ?? 0);
            $result['overall']['allTicketAmount']      += $product->total + ($product->overstayTotal ?? 0);
            $result['overall']['allprocessingFee']     += $product->processing_fee + ($product->overstayProcessingFee ?? 0);
            $result['overall']['allgrandTotal']        += $product->grand_total + ($product->overstayGrandTotal ?? 0);
            $result['overall']['allticketsCount']      += $product->ticketCount;
            $result['overall']['allparkingAmount']     += $product->grand_total > 0 ? $product->parking_amount : 0;
            $result['overall']['alldiscountAmount']    += $product->discount_amount + ($product->overstayDiscount ?? 0);
            $result['overall']['allpaidAmount']        += $product->validated_amount;
            $result['overall']['allfinalAmount']       += ($product->grand_total + ($product->overstayGrandTotal ?? 0)) + ($product->discount_amount + ($product->overstayDiscount ?? 0));
        }

        // Output the grouped result
        // print_r($result);
        return $result;
    }

    // Specific to Drive Up
    public static function convertArrayToEvents($products, $type = 'event')
    {

        // Initialize an empty result array
        $result = [];
        // $result['overall'] = [];
        // $result['overall']['alltaxFee']          = 0;
        // $result['overall']['allTicketAmount']    = 0;
        // $result['overall']['allprocessingFee']   = 0;
        $result['overall']['allgrandTotal']      = 0;
        $result['overall']['alleventsCount']    = 0;
        $result['overall']['allticketsCount']    = 0;
        $result['overall']['allbookingCount']    = 0;
        // $result['overall']['allparkingAmount']   = 0;
        // $result['overall']['alldiscountAmount']  = 0;
        // $result['overall']['allpaidAmount']      = 0;
        // $result['overall']['allfinalAmount']     = 0;

        // Group elements by the 'category' key and calculate the sum of prices

        foreach ($products as $key =>  $product) {
            if ($type == 'event')
                $category = $product->event_id;

            // if ($type == 'validated')
            //     $category = $product->affiliate_business_id;



            // If the category key doesn't exist in the result array, create it
            if (!isset($result[$category])) {
                $result[$category] = [
                    'tickets' => [],
                    /* 'taxFee' => 0,
                    'processingFee' => 0,
                    'TicketAmount' => 0, */
                    'grandTotal' => 0,
                    'ticketsCount' => 0,
                    'bookingCount' => 0,
                    /* 'parkingAmount' => 0,
                    'discountAmount' => 0,
                    'paidAmount' => 0, */
                    'eventName' => 0,
                    'eventRate' => 0,
                    'finalAmount' => 0/* ,
                    'affiliate_business_id' => 0,
                    'businessName' => '', */
                ];
            }

            // Append the current product to the category
            $result[$category]['tickets'][] = $product;

            // Add the price of the current product to the total price of the category
            // $result[$category]['taxFee']            += $product->tax_fee + ($product->overstayTaxFee ?? 0);
            // $result[$category]['TicketAmount']      += $product->total + ($product->overstayTotal ?? 0);
            // $result[$category]['processingFee']     += $product->processing_fee + ($product->overstayProcessingFee ?? 0);
            $result[$category]['eventRate']            = $product->event_rate;
            $result[$category]['grandTotal']            += $product->grand_total + $product->overstayGrandtotal ?? 0;
            $result[$category]['ticketsCount']          += $product->ticketCount;
            $result[$category]['bookingCount']          += $product->bookingCount;
            // $result[$category]['parkingAmount']     += $product->parking_amount;
            // $result[$category]['discountAmount']    += $product->discount_amount + ($product->overstayDiscount ?? 0);
            // $result[$category]['paidAmount']        += $product->validated_amount;
            // $result[$category]['finalAmount']       += ($product->grand_total + ($product->overstayGrandTotal ?? 0)) + ($product->discount_amount + ($product->overstayDiscount ?? 0));

            // Valiation Related 
            /*  $result[$category]['businessName']              = isset($product->BusinessName) ? $product->BusinessName : '';
            $result[$category]['affiliate_business_id']     = isset($product->affiliate_business_id) ? $product->affiliate_business_id : 0; */
            $result[$category]['eventName']                 = isset($product->title) ? $product->title : '';

            // $result['overall']['alltaxFee']              += $product->tax_fee + ($product->overstayTaxFee ?? 0);
            // $result['overall']['allTicketAmount']        += $product->total + ($product->overstayTotal ?? 0);
            // $result['overall']['allprocessingFee']       += $product->processing_fee + ($product->overstayProcessingFee ?? 0);
            $result['overall']['allgrandTotal']             += $product->grand_total + $product->overstayGrandtotal ?? 0;
            // $result['overall']['alleventsCount']         += count($result[$category]);
            $result['overall']['allticketsCount']           += $product->ticketCount;
            $result['overall']['allbookingCount']           += $product->bookingCount;
            // $result['overall']['allparkingAmount']       += $product->parking_amount;
            // $result['overall']['alldiscountAmount']      += $product->discount_amount + ($product->overstayDiscount ?? 0);
            // $result['overall']['allpaidAmount']          += $product->validated_amount;
            // $result['overall']['allfinalAmount']         += ($product->grand_total + ($product->overstayGrandTotal ?? 0)) + ($product->discount_amount + ($product->overstayDiscount ?? 0));
        }
        $result['overall']['alleventsCount']      += count($result) - 1;

        // Output the grouped result
        // print_r($result);
        return $result;
    }

    public static function convertToPermits($products)
    {
        $result = [];
        // Double group elements by the 'group' key and then by the 'category' key
        foreach ($products as $item) {
            $groupKey = $item->permit_rate;
            $categoryKey = $item->account_number;

            // If the group key doesn't exist in the result array, create it
            if (!isset($result[$groupKey])) {
                $result[$groupKey] = [
                    'permitcount' => 0
                ];
            }

            // If the category key doesn't exist in the group, create it
            if (!isset($result[$groupKey][$categoryKey])) {
                $result[$groupKey][$categoryKey] = [];
            }

            // Append the current item to the double group
            $result[$groupKey][$categoryKey][] = $item;
        }
        return $result;
    }

    // Permit Specific
    public static function convertArrayToPermits($products)
    {

        // Initialize an empty result array
        $result = [];
        // $result['overall'] = [];
        // $result['overall']['alltaxFee']          = 0;
        // $result['overall']['allTicketAmount']    = 0;
        // $result['overall']['allprocessingFee']   = 0;
        $result['overall']['allgrandTotal']      = 0;
        $result['overall']['allpermitCount']    = 0;
        $result['overall']['allticketsCount']    = 0;
        // $result['overall']['allparkingAmount']   = 0;
        // $result['overall']['alldiscountAmount']  = 0;
        // $result['overall']['allpaidAmount']      = 0;
        // $result['overall']['allfinalAmount']     = 0;

        // Group elements by the 'category' key and calculate the sum of prices

        foreach ($products as $key =>  $product) {

            $category = $product->permit_rate;


            // If the category key doesn't exist in the result array, create it
            if (!isset($result[$category])) {
                $result[$category] = [
                    'tickets' => [],
                    /* 'taxFee' => 0,
                    'processingFee' => 0,
                    'TicketAmount' => 0, */
                    'grandTotal' => 0,
                    'ticketsCount' => 0,
                    'permitCount' => 0,
                    'permitRate' => 0,
                    /* 'parkingAmount' => 0,
                    'discountAmount' => 0,
                    'paidAmount' => 0, */
                    'finalAmount' => 0/* ,
                    'affiliate_business_id' => 0,
                    'businessName' => '', */
                ];
            }

            // Append the current product to the category
            $result[$category]['tickets'][] = $product;

            // Add the price of the current product to the total price of the category
            // $result[$category]['taxFee']            += $product->tax_fee + ($product->overstayTaxFee ?? 0);
            // $result[$category]['TicketAmount']      += $product->total + ($product->overstayTotal ?? 0);
            // $result[$category]['processingFee']     += $product->processing_fee + ($product->overstayProcessingFee ?? 0);
            $result[$category]['permitRate']            = $product->permit_rate;
            $result[$category]['grandTotal']            += $product->total;
            $result[$category]['ticketsCount']          += $product->ticketCount;
            $result[$category]['permitCount']          += $product->permitCount;
            // $result[$category]['parkingAmount']     += $product->parking_amount;
            // $result[$category]['discountAmount']    += $product->discount_amount + ($product->overstayDiscount ?? 0);
            // $result[$category]['paidAmount']        += $product->validated_amount;
            // $result[$category]['finalAmount']       += ($product->grand_total + ($product->overstayGrandTotal ?? 0)) + ($product->discount_amount + ($product->overstayDiscount ?? 0));

            // Valiation Related 
            /*  $result[$category]['businessName']              = isset($product->BusinessName) ? $product->BusinessName : '';
            $result[$category]['affiliate_business_id']     = isset($product->affiliate_business_id) ? $product->affiliate_business_id : 0; */
            // $result[$category]['eventName']              = isset($product->title) ? $product->title : '';

            // $result['overall']['alltaxFee']            += $product->tax_fee + ($product->overstayTaxFee ?? 0);
            // $result['overall']['allTicketAmount']      += $product->total + ($product->overstayTotal ?? 0);
            // $result['overall']['allprocessingFee']     += $product->processing_fee + ($product->overstayProcessingFee ?? 0);
            $result['overall']['allgrandTotal']          += $product->total;
            $result['overall']['allpermitCount']       += $product->permitCount;
            $result['overall']['allticketsCount']      += $product->ticketCount;
            // $result['overall']['allparkingAmount']     += $product->parking_amount;
            // $result['overall']['alldiscountAmount']    += $product->discount_amount + ($product->overstayDiscount ?? 0);
            // $result['overall']['allpaidAmount']        += $product->validated_amount;
            // $result['overall']['allfinalAmount']       += ($product->grand_total + ($product->overstayGrandTotal ?? 0)) + ($product->discount_amount + ($product->overstayDiscount ?? 0));
        }
        // $result['overall']['allpermitCount']      += count($result) - 1;

        // Output the grouped result
        // print_r($result);
        return $result;
    }


    // Payments Functions
    // driveup Cash with discount if any 
    public static  function driveUpCashPayments()
    {
        $sqlQuery = "SELECT count(distinct t.id) as ticketCount,
            t.grand_total AS sum_offline_amount,
            t.discount_amount as discountAmount,
            t.is_offline_payment,
            t.partner_id ,            
            
            SUM(ot.grand_total) as overstayGrandTotal, 
            SUM(ot.discount_amount) as overstayDiscount,
            t.ticket_number
        FROM tickets AS t 
        left join overstay_tickets as ot on ot.ticket_id = t.id      
        WHERE " . self::$driveString . " AND t.is_offline_payment IN ('1') GROUP BY t.ticket_number";
        // dd('11');
        $results = DB::select($sqlQuery);
        return $results;
    }

    public static  function driveUpCardPayments()
    {
        $sqlQuery = "SELECT count(distinct t.id) as ticketCount,t.grand_total as GT_total ,ant.total as anet_total, t.anet_transaction_id , ant.id as anet_id,
            CASE                             
                when ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT') THEN 'VISA'
                when ant.card_type IN ('DCVR') THEN 'DCVR'
                WHEN ant.card_type IN ('AMEX') THEN 'AMEX'
                WHEN ant.card_type IN ('Disc') THEN 'DISC' 
            ELSE ant.card_type
            END AS combined_card_type, 
        t.grand_total as total_amount,        
        t.discount_amount as discountAmount,
        t.processing_fee as processingFee,
        
        SUM(ot.grand_total) as overstayGrandTotal, 
        SUM(ot.discount_amount) as overstayDiscount
        FROM tickets as t
        left join overstay_tickets as ot on ot.ticket_id = t.id
        left join anet_transactions as ant ON ant.id = t.anet_transaction_id
        WHERE " . self::$driveString . " AND (t.is_offline_payment IN ('0','2','3') ) GROUP BY combined_card_type,t.ticket_number";
        $results = DB::select($sqlQuery);
        // dd('11');
        return $results;
    }

    public static  function reservationPayments()
    {
        #r.ticketech_code,
        $sqlQuery = "SELECT 
                COUNT(r.id) AS ticketCount,
                t.ticket_number,
                CASE
                    WHEN ant.card_type IN ('MC', 'MASTERCARD', 'M/C') THEN 'MASTERCARD'
                    WHEN ant.card_type IN ('VS', 'VISA', 'Visa Credit', 'VISA DEBIT') THEN 'VISA'
                    WHEN ant.card_type IN ('DCVR') THEN 'DCVR'
                    WHEN ant.card_type IN ('AMEX') THEN 'AMEX'
                    WHEN ant.card_type IN ('Disc') THEN 'DISC' 	
                    ELSE ant.card_type
                END AS combined_card_type,
                SUM(r.total) AS total_amount,
                SUM(r.discount) AS discountAmount, 
                SUM(r.processing_fee) AS processingFee,
                SUM(r.tax_fee) AS tax_fee,
                SUM(ot.grand_total) AS overstayGrandTotal, 
                SUM(ot.discount_amount) AS overstayDiscount
            FROM reservations AS r
            LEFT JOIN anet_transactions AS ant ON ant.id = r.anet_transaction_id
            LEFT JOIN tickets AS t ON t.reservation_id = r.id
            LEFT JOIN overstay_tickets AS ot ON ot.ticket_id = t.id
            LEFT JOIN anet_transactions AS ant_tickets ON ant_tickets.id = ot.anet_transaction_id 
            WHERE " . self::$reservationString . " GROUP BY combined_card_type,t.ticket_number";
        $results = DB::select($sqlQuery);
        return $results;
    }

    public static  function eventsPayment()
    {
        $sqlQuery = "SELECT 
                CASE
                    WHEN ant.card_type IN ('VS', 'VISA', 'Visa Credit', 'VISA DEBIT') THEN 'VISA'
                    WHEN ant.card_type IN ('MC', 'MASTERCARD', 'M/C') THEN 'MASTERCARD'
                    WHEN ant.card_type IN ('DCVR') THEN 'DCVR'
                    WHEN ant.card_type IN ('AMEX') THEN 'AMEX'  
                    WHEN ant.card_type IN ('Disc') THEN 'DISC' 
                    ELSE ant.card_type
                END AS combined_card_type,
                SUM(r.total) AS total_amount,
                SUM(r.discount) AS discountAmount,
                r.is_cash_payment 
            FROM events AS e                
            INNER JOIN reservations AS r ON r.event_id = e.id
            LEFT JOIN anet_transactions AS ant ON ant.id = r.anet_transaction_id   
            WHERE " . self::$eventStringPayment . " GROUP BY combined_card_type,r.is_cash_payment";
        $results = DB::select($sqlQuery);
        return $results;
        #inner join mapco_qrcodes as mqr on mqr.event_id= e.id
    }
    public static  function eventPaymentWithoutBooking()
    {
        $sqlQuery = "SELECT count(distinct t.id) as ticketCount,t.grand_total as GT_total ,ant.total as anet_total, t.anet_transaction_id , ant.id as anet_id,
            CASE                             
                when ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
                when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT') THEN 'VISA'
                when ant.card_type IN ('DCVR') THEN 'DCVR'
                WHEN ant.card_type IN ('AMEX') THEN 'AMEX'
                WHEN ant.card_type IN ('Disc') THEN 'DISC' 
            ELSE ant.card_type
            END AS combined_card_type, 
            t.grand_total as total_amount,        
            t.discount_amount as discountAmount,
            t.processing_fee as processingFee,
            r.is_cash_payment,
            
            SUM(ot.grand_total) as overstayGrandTotal, 
            SUM(ot.discount_amount) as overstayDiscount
            FROM tickets as t
            left join events as e on t.event_id = e.id
            left join reservations AS r ON r.id = t.reservation_id
            left join overstay_tickets as ot on ot.ticket_id = t.id
            left join anet_transactions as ant ON ant.id = t.anet_transaction_id
        WHERE " . self::$eventStringPaymentWithoutBooking . " AND (t.is_offline_payment IN ('0','2','3') ) GROUP BY combined_card_type,t.ticket_number";
        $results = DB::select($sqlQuery);
        return $results;
        #inner join mapco_qrcodes as mqr on mqr.event_id= e.id
    }

    public static  function permitPayments()
    {
        $sqlQuery = "SELECT CASE
            when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT') THEN 'VISA'
            WHEN ant.card_type IN ('MC', 'MASTERCARD','M/C') THEN 'MASTERCARD'
            when ant.card_type IN ('DCVR') THEN 'DCVR'
            WHEN ant.card_type IN ('AMEX') THEN 'AMEX'  
            WHEN ant.card_type IN ('Disc') THEN 'DISC'  
            ELSE ant.card_type
        END AS combined_card_type, count(pr.id) as ticketCount, #pr.ticketech_code,
        SUM(ant.total) as total_amount,
        '0' as discountAmount
        FROM permit_requests as pr
        inner join anet_transactions as ant on ant.id = pr.anet_transaction_id   
        left join tickets as t on t.permit_request_id = pr.id
        WHERE " . self::$permitString . " AND ant.card_type is not null GROUP BY combined_card_type";
        $results = DB::select($sqlQuery);
        return $results;
        #inner join mapco_qrcodes as mqr on mqr.event_id= e.id
    }

    public static function weekWiseDriveup()
    {
        // Non Validated
        $allDriveupData = [];
        $sqlQuery = "SELECT COUNT(distinct(t.id)) AS ticketCount, 
                date(t.checkout_time) as transactionDate, 
                DATE_FORMAT(t.checkout_time,'%W') as days,
                DATE_FORMAT(t.checkout_time,'%w') as dayname,                 
                t.processing_fee AS processing_fee, 
                t.total AS total, 
                t.grand_total AS grand_total, 
                t.parking_amount AS parking_amount, 
                t.discount_amount AS discount_amount,
                t.tax_fee AS tax_fee, 
                t.paid_amount AS validated_amount,
                 
                count(ot.id) as extentionCount, 
                SUM(ot.total) as overstayTotal, 
                SUM(ot.total) as overstayParkingAmount,
                SUM(ot.tax_fee) as overstayTaxFee, 
                SUM(ot.processing_fee) as overstayProcessingFee, 
                SUM(ot.grand_total) as overstayGrandTotal, 
                SUM(ot.discount_amount) as overstayDiscount, t.ticket_number,
                TIMESTAMPDIFF(MINUTE, t.checkin_time, t.checkout_time) AS time_difference_minutes       
                FROM tickets AS t 
                left join ticket_extends as ot on ot.ticket_id = t.id           
                where  " . self::$driveString . " AND t.paid_type='9' group by date(t.checkout_time),t.id order by t.checkout_time ASC";
        $results = DB::select($sqlQuery);

        return $results;
    }

    public static function weekWiseConvertToPermits($products)
    {
        $result = [];
        // Double group elements by the 'group' key and then by the 'category' key
        foreach ($products as $item) {
            $groupKey = $item->permit_rate;
            $categoryKey = $item->account_number;

            // If the group key doesn't exist in the result array, create it
            if (!isset($result[$groupKey])) {
                $result[$groupKey] = [
                    'permitcount' => 0
                ];
            }

            // If the category key doesn't exist in the group, create it
            if (!isset($result[$groupKey][$categoryKey])) {
                $result[$groupKey][$categoryKey] = [];
            }

            // Append the current item to the double group
            $result[$groupKey][$categoryKey][] = $item;
        }
        return $result;
    }


    public static function weekWiseDriveUpData($products, $type = 'driveup')
    {

        // Initialize an empty result array
        $result = [];
        // $result['overall'] = [];
        $result['overall']['allticketsCount']    = 0;
        $result['overall']['allnetAmount']       = 0;
        $result['overall']['allparkingAmount']   = 0;
        $result['overall']['allgrandTotal']      = 0;
        $result['overall']['alldiscountAmount']  = 0;
        $result['overall']['allTicketAmount']    = 0;
        $result['overall']['allprocessingFee']   = 0;
        $result['overall']['alltaxFee']          = 0;
        $result['overall']['allpaidAmount']      = 0;
        $result['overall']['allfinalAmount']     = 0;
        $result['overall']['allextentionCount']     = 0;
        $result['overall']['allextentionAmount']     = 0;
        $result['overall']['minuteAvgDiff']  = 0;

        // Group elements by the 'category' key and calculate the sum of prices
        foreach ($products as $product) {
            $groupKey = $product->transactionDate;
            $categoryKey = $product->days;

            // If the category key doesn't exist in the group, create it
            if (!isset($result[$groupKey][$categoryKey])) {
                $result[$groupKey][$categoryKey] = [
                    'tickets' => [],
                    'ticketsCount' => 0,
                    'netAmount' => 0,
                    'parkingAmount' => 0,
                    'grandTotal' => 0,
                    'discountAmount' => 0,
                    'TicketAmount' => 0,
                    'processingFee' => 0,
                    'taxFee' => 0,
                    'date' => '',
                    'days' => '',
                    'paidAmount' => 0,
                    'finalAmount' => 0,
                    'extentionsCount' => 0,
                    'extentionAmount' => 0,
                    'minuteAvgDiff' => 0
                ];
            }
            // dd($product);


            // Append the current item to the double group
            // $result[$groupKey][$categoryKey][] = $product;

            // Append the current product to the category
            $result[$groupKey][$categoryKey]['tickets'][] = $product;

            // Add the price of the current product to the total price of the category
            $result[$groupKey][$categoryKey]['date']              = $product->transactionDate;
            $result[$groupKey][$categoryKey]['days']              = $product->days;
            $result[$groupKey][$categoryKey]['taxFee']            += $product->tax_fee + ($product->overstayTaxFee ?? 0);
            $result[$groupKey][$categoryKey]['TicketAmount']      = $product->total + ($product->overstayTotal ?? 0);
            // $result[$groupKey][$categoryKey]['TicketAmount']      = $product->parking_amount;
            $result[$groupKey][$categoryKey]['processingFee']     += $product->processing_fee + ($product->overstayProcessingFee ?? 0);
            $result[$groupKey][$categoryKey]['grandTotal']        += $product->grand_total + ($product->overstayGrandTotal ?? 0);
            $result[$groupKey][$categoryKey]['ticketsCount']      += $product->ticketCount;
            $result[$groupKey][$categoryKey]['parkingAmount']     += $product->parking_amount + $product->overstayParkingAmount;
            $result[$groupKey][$categoryKey]['netAmount']         += $product->grand_total + $product->overstayParkingAmount - $product->processing_fee;
            $result[$groupKey][$categoryKey]['discountAmount']    += $product->discount_amount + ($product->overstayDiscount ?? 0);
            $result[$groupKey][$categoryKey]['paidAmount']        += $product->validated_amount;
            $result[$groupKey][$categoryKey]['finalAmount']       += ($product->grand_total + ($product->overstayGrandTotal ?? 0)) + ($product->discount_amount + ($product->overstayDiscount ?? 0));
            $result[$groupKey][$categoryKey]['extentionsCount']       += $product->extentionCount;
            $result[$groupKey][$categoryKey]['extentionAmount']       += $product->overstayTotal;
            $result[$groupKey][$categoryKey]['minuteAvgDiff']       += $product->time_difference_minutes;


            // Valiation Related 
            // $result[$category]['businessName']              = isset($product->BusinessName) ? $product->BusinessName : '';
            // $result[$category]['affiliate_business_id']     = isset($product->affiliate_business_id) ? $product->affiliate_business_id : 0;

            $result['overall']['allticketsCount']      += $product->ticketCount;
            $result['overall']['allnetAmount']         += $product->parking_amount + $product->overstayParkingAmount;
            $result['overall']['allparkingAmount']     += $product->grand_total + $product->overstayParkingAmount - $product->processing_fee;
            $result['overall']['allgrandTotal']        += $product->grand_total + ($product->overstayGrandTotal ?? 0);
            $result['overall']['alldiscountAmount']    += $product->discount_amount + ($product->overstayDiscount ?? 0);
            $result['overall']['allTicketAmount']      =  $product->total + ($product->overstayTotal ?? 0);
            $result['overall']['allprocessingFee']     += $product->processing_fee + ($product->overstayProcessingFee ?? 0);
            $result['overall']['alltaxFee']            += $product->tax_fee + ($product->overstayTaxFee ?? 0);
            $result['overall']['allpaidAmount']        += $product->validated_amount;
            $result['overall']['allfinalAmount']       += ($product->grand_total + ($product->overstayGrandTotal ?? 0)) + ($product->discount_amount + ($product->overstayDiscount ?? 0));
            $result['overall']['allextentionCount']    += $product->extentionCount;
            $result['overall']['allextentionAmount']   += $product->overstayTotal;
            $result['overall']['minuteAvgDiff']       += $product->time_difference_minutes;
        }
        // Output the grouped result
        return $result;
    }

    public static function setUpperSectionExcel($partner_id, $facility_id, $start, $end, $sheet, $lastColumn, $excelSheetName)
    {
        //for multipleLocation fscility id is All 
        $brandSetting = BrandSetting::where('user_id', $partner_id)->first();
        $color = $brandSetting->color;
        $range = $start . '-' . $end;
        $facilityIDS = [];
        if (count($facility_id) > 0) {
            $facility = Facility::where(['id' => $facility_id])->first();
        } else {
            $facility = Facility::where(['owner_id' => $partner_id])->first();
            $facilities = Facility::select('id')->where(['owner_id' => $partner_id])->get();
            foreach ($facilities as $key => $value) {
                array_push($facilityIDS, $value->id);
            }
        }
        $imploadFaclities = implode(",", $facilityIDS);
        $locationName = $facility->full_name;
        $garageCode = $facility->garage_code;
        $locationName = "Multiple Location";
        $garageCode = "Multiple Garage Code";
        //************* set height and width***************************
        $sheet->setWidth(array(
            'A'     => 21,
            'B'     =>  24,
            'C'     =>  18,
            'D'     =>  17.57,
            'E'     =>  17.34,
            'F'     =>  15.57,
            'G'    =>   21,
            'H'    =>   16.86,
            'I'    =>   18,
            'J'    =>   18,
            'K'    =>   18

        ));

        $sheet->getColumnDimension('D')->setWidth(17.57);
        $sheet->getColumnDimension('E')->setWidth(17.34);
        $sheet->getColumnDimension('F')->setWidth(15.57);
        $sheet->getColumnDimension('G')->setWidth(21);
        $sheet->getColumnDimension('H')->setWidth(16.86);
        $sheet->getStyle("D")->getNumberFormat()->setFormatCode('0.00');
        $sheet->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
        $sheet->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
        $sheet->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
        $sheet->getStyle("H")->getNumberFormat()->setFormatCode('0.00');
        $sheet->getStyle("I")->getNumberFormat()->setFormatCode('0.00');
        $sheet->getStyle("J")->getNumberFormat()->setFormatCode('0.00');
        $sheet->getStyle("K")->getNumberFormat()->setFormatCode('0.00');
        $sheet->getStyle("C3")->getNumberFormat()->setFormatCode('0');

        //*********************************end height and width  */
        $sheet->mergeCells('A1:' . $lastColumn . '1');
        $sheet->setCellValue('A1', $excelSheetName);
        $sheet->cell('A1:' . $lastColumn . '1', function ($cell) use ($color) {
            $cell->setAlignment('center');
            $cell->setValignment('center');
            $cell->setFontWeight('bold');
            $cell->setFontSize('30');
        });

        $sheet->mergeCells('A2:D2');
        $cellValue = "Report Date Range - " .  date('m-d-Y', strtotime($start)) .  ' to ' . date('m-d-Y', strtotime($end));
        $cellValue .= "\nPrint Date - " . date('m-d-Y');
        $sheet->setCellValue('A2', "$cellValue");
        $sheet->getStyle('A2')->getAlignment()->setWrapText(true);

        // Set the height of cell H2 (adjust as needed)
        $sheet->getRowDimension(2)->setRowHeight(80);
        $sheet->getRowDimension(3)->setRowHeight(50);
        $sheet->getRowDimension(4)->setRowHeight(50);
        $sheet->cell('A2:D2', function ($cell) use ($color) {
            $cell->setAlignment('center');
            $cell->setValignment('center');
            $cell->setFontWeight('bold');
            $cell->setBackground('#D6DCE4');
            $cell->setFontColor('#000000');
            $cell->setFontSize('16');
        });

        // !! Set Localtion Name:
        $location = "Location Name \r" . $locationName;
        $sheet->mergeCells('E2:G2');
        $sheet->setCellValue('E2', "$location");
        $sheet->getStyle('E2')->getAlignment()->setWrapText(true);
        $sheet->cell('E2:G2', function ($cell) use ($color) {
            $cell->setAlignment('center');
            $cell->setValignment('center');
            $cell->setFontWeight('bold');
            $cell->setBackground('#D6DCE4');
            $cell->setFontColor('#000000');
            $cell->setFontSize('16');
        });

        // !! Set Garage Code 
        $sheet->mergeCells('H2:' . $lastColumn . '2');
        $locationId = "Location ID \n" . $garageCode;
        $sheet->setCellValue('H2', "$locationId");
        $sheet->getStyle('H2')->getAlignment()->setWrapText(true);

        $sheet->cell('H2:' . $lastColumn . '2', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            $cell->setBackground('#D6DCE4');
            $cell->setFontColor('#040D12');
            $cell->setFontSize('18');
        });
        // $sheet->mergeCells('A3:'.$lastColumn.'3');
    }

    public static function getPermitListArray($partner_id, $facility_id, $checkInTime, $checkOutTime)
    {

        //dd($partner_id,$facility_id,$checkInTime,$checkOutTime);
        $permitData =  "select COUNT(Pf.account_number) AS permitCount,Pf.deleted_at ,Pf.discount_amount,sum(discount_amount) as discountpf,Pf.permit_rate,Pf.approved_on, Pf.permit_type ,action_date,account_number,permit_amount,action_flag,cancelled_at,sum(Pf.processing_fee) as processingfee ,sum(permit_amount) as permitFinalAmount,refund_amount,sum(refund_amount) as refundAmount, permit_rate_id,Pf.deleted_at,ant.card_type, f.full_name from permit_revenue_flat_table  AS Pf inner join facilities as f   On  Pf.facility_id =f.id inner join anet_transactions as ant On Pf.anet_transaction_id = ant.id where partner_id IN ('$partner_id')  and desired_start_date>='$checkInTime' and desired_start_date<='$checkOutTime' group by permit_type";
        $permitResults = DB::select($permitData);
        // dd($permitResults,'sd');
        // dd($permitResults);and facility_id= '$facility_id'

        $permitGrossTotalAmount = $permitTicketCount = $permitDiscountTotalAmount = $permitRefundTotalAmount = $permitRowIndex = $permitCount = 0;
        $permitTickets = [];
        $permitTicketCountTotal = 0;
        foreach ($permitResults as $key => $value) {
            $permitCount += $value->permitCount;
            $permitTickets[$permitRowIndex]['permit_type'] = $value->permit_type;
            $permitTickets[$permitRowIndex]['PermitCount'] = $value->permitCount;
            $permitTickets[$permitRowIndex]['account_number'] = '-';
            $permitTickets[$permitRowIndex]['permit_rate'] = $value->permit_rate;
            $permitTickets[$permitRowIndex]['processing_fee'] = $value->processingfee;
            $permitTickets[$permitRowIndex]['permit_amount'] = $value->permitFinalAmount;
            $permitTickets[$permitRowIndex]['discount_amount']      = $value->discountpf;
            $permitTickets[$permitRowIndex]['refund_amount']      = $value->refundAmount;
            $permitTickets[$permitRowIndex]['service_name'] = '-';

            $permitTickets[$permitRowIndex]['action_date'] = '-';
            $permitTickets[$permitRowIndex]['cancelled_at'] = '-';
            $permitTickets[$permitRowIndex]['NoofTickets'] = floatval(0);
            $permitTickets[$permitRowIndex]['PermitNumber'] = '-';
            $permitTickets[$permitRowIndex]['permit_rate_id'] = $value->permit_rate_id;
            $permitTickets[$permitRowIndex]['processingfee'] = $value->processingfee;
            $permitTickets[$permitRowIndex]['permitFinalAmount'] = $value->permitFinalAmount;

            $permitRowIndex++;
            $permitAcountData = "SELECT Pf.deleted_at, Pf.discount_amount, Pf.permit_rate, Pf.approved_on,Pf.permit_type, action_date, account_number, permit_amount, action_flag,permit_final_price, cancelled_at, refund_amount,Pf.processing_fee, Pf.deleted_at, ant.card_type, f.full_name,permit_request_id FROM permit_revenue_flat_table AS Pf INNER JOIN facilities AS f ON Pf.facility_id = f.id 
          INNER JOIN anet_transactions AS ant ON Pf.anet_transaction_id = ant.id 
           WHERE partner_id IN ('{$partner_id}')  and facility_id= '$facility_id'
           AND permit_rate_id = '{$value->permit_rate_id}' and desired_start_date>='$checkInTime' and desired_start_date<='$checkOutTime'";
            // dd('1');
            $permitAcountResult = DB::select($permitAcountData);
            if (!empty($permitAcountResult) && count($permitAcountResult) > 0) {

                foreach ($permitAcountResult as $acoountkey => $acoountVal) {
                    //   $kevent++;
                    $permitRowIndex++;
                    $serviceName = "SELECT permit_service_name from permit_request_service_mapping ps inner join permit_services s on s.id=ps.permit_service_id where permit_request_id={$acoountVal->permit_request_id}";
                    $serviceNameResult = DB::select($serviceName);
                    $serviceNames = [];
                    if (!empty($serviceNameResult)) {
                        $serviceNames = array_column($serviceNameResult, 'permit_service_name');
                    }
                    $permitTickets[$permitRowIndex]['permit_type'] = '-';
                    $permitTickets[$permitRowIndex]['PermitCount'] = '-';
                    $permitTickets[$permitRowIndex]['account_number'] = $acoountVal->account_number;
                    $permitTickets[$permitRowIndex]['service_name'] = isset($serviceNames[0]) ? $serviceNames[0] : '-';
                    $permitTickets[$permitRowIndex]['permit_rate'] = $acoountVal->permit_rate;
                    $permitTickets[$permitRowIndex]['processing_fee'] = $acoountVal->processing_fee;
                    $permitTickets[$permitRowIndex]['permit_amount'] = $acoountVal->permit_amount;
                    $permitTickets[$permitRowIndex]['discount_amount'] = $acoountVal->discount_amount;
                    $permitTickets[$permitRowIndex]['refund_amount'] = ($acoountVal->refund_amount) ? $acoountVal->refund_amount : '0.00';
                    $permitTickets[$permitRowIndex]['action_date'] = $acoountVal->action_date;
                    $permitTickets[$permitRowIndex]['cancelled_at'] = $acoountVal->cancelled_at;
                }
            }
        }
        //dd($permitTickets);
        return $permitTickets;
    }

    public static function arrayNormalise($rawCardTypes)
    {
        foreach ($rawCardTypes as $item) {
            $type = strtoupper(trim($item->combined_card_type ?? ''));
            $normalized[] = $type;
        }
        $finalList = array_values(array_unique($normalized));
        return $finalList;
    }
}
