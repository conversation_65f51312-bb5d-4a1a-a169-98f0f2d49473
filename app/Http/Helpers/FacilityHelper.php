<?php
namespace App\Http\Helpers; 

use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Services\LoggerFactory;

class FacilityHelper
{
    
	protected  $log;
	protected  $errorLog;
	protected  $log_upload;


    /**
     * Constructor to set up logging.
     *
     * @param LoggerFactory $logFactory The LoggerFactory object to
     *     use for creating loggers.
     */
	public function __construct(LoggerFactory $logFactory)
	{
		$this->log = $logFactory->setPath('logs/are/buy-monthlypermit')->createLogger('buy-monthly');
		$this->errorLog = $logFactory->setPath('logs/are/buy-monthlypermit')->createLogger('buy-monthly-error');

		$this->log_upload = $logFactory->setPath('logs/are/permitupload')->createLogger('uploadpermit');

	}
    /**
     * Validates the given facility for booking eligibility.
     *
     * Checks various conditions to determine if the facility is available
     * and suitable for bookings. Logs information and throws exceptions
     * if any validation fails.
     *
     * @param object $facility The facility object to validate.
     * 
     * @throws NotFoundException If the facility is not found.
     * @throws ApiGenericException If the facility is inactive, unavailable,
     *                             has an unsupported booking type, or does
     *                             not accept credit cards.
     *
     * @return bool True if the facility passes all validation checks.
     */

    function validateFacility($facility)
    {
        if (!$facility) {
            $this->log->info("PBooking: No garage found with this partner.");
            throw new NotFoundException('No garage found with this partner.');
        }
        else if ($facility->active != '1') {
            $this->log->info("PBooking: active Garage is not available");
            throw new ApiGenericException('Garage is not available for any booking');
        }
        else if ($facility->is_available != '1') {
            $this->log->info("PBooking: IA Garage is not available");
            throw new ApiGenericException('Garage is not available for any booking');
        }
        else if ($facility->facility_booking_type == '1') {
            $this->log->info("PBooking: FBT Garage is not available");
            throw new ApiGenericException('Garage is not available for any booking');
        }
        else if (!$facility->accept_cc) {
            $this->log->info("PBooking: Garage does not accept CC");
            throw new ApiGenericException("Garage does not accept credit cards");
        }
        else {
            // if all validation passed
            return true;
        }
    }
}