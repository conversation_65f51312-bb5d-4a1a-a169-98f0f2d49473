<?php

namespace App\Http\Helpers;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class MailHelper
{
    public static function sendEmail($to, $view, $data, $partnerId, $fileType = null)
    {
        Log::info('Check Data');
        Log::info('Partner ID : ' . $partnerId);
        $subject = $data['subject'];
        Log::info('Subject Line : ' . $subject);


        // Send the email
        // check for attachement
        $attachementArray = [];
        if (isset($data['filedata']['type']) && !empty($data['filedata']['type'])) {
            Log::info('check file content');
            Log::info($data['filedata']['type']);
            $attachementArray['type']           = $data['filedata']['type'];
            $attachementArray['content']        = $data['filedata']['content'];
            $attachementArray['filename']       = $data['filedata']['filename'];
            $attachementArray['format']         = $data['filedata']['format'];
        }
        Log::info('Email sent to user : ' . json_encode($to));
        Log::info('Print data before sent to mail');
        // Log::info($data);

        Mail::send($view, $data, function ($message) use ($to, $subject, $attachementArray, $partnerId) {
            Log::info('check attachementArray');

            $message->to($to);
            $message->subject($subject);

            // if ($partnerId == '215900') {
            //     Log::info('Check Partner ID  : ' . $partnerId);
            //     $message->from('<EMAIL>', 'RevPASS');
            // } else {
            //     Log::info('Check Partner ID  2 : ' . $partnerId);
            //     $message->from('<EMAIL>', 'ParkEngage');
            // }

            switch ($partnerId) {
                case config('parkengage.PARTNER_RevPass'):
                    Log::info('PARTNER_RevPass Check Partner ID : ' . $partnerId);
                    $message->from('<EMAIL>', 'RevPASS');
                    break;
            
                case config('parkengage.PARTNER_PARK2VISIT'):
                    Log::info('PARTNER_PARK2VISIT Check Partner ID 3 : ' . $partnerId);
                    $message->from('<EMAIL>', 'Park2Visit');
                    break;
            
                default:
                    Log::info('Parkengage Check Partner ID 2 : ' . $partnerId);
                    $message->from('<EMAIL>', 'ParkEngage');
                    break;
            }

            if (isset($attachementArray['type']) && $attachementArray['type'] == 'runtime') {
                Log::info('File Format : ' . $attachementArray['format']);
                if ($attachementArray['format'] == 'pdf') {
                    $pdf        = $attachementArray['content'];
                    $fileName   = $attachementArray['filename'];
                    Log::info('File Name :  ' . $fileName);
                    $message->attachData($pdf, $fileName);
                }
            } else if (isset($attachementArray['type']) && $attachementArray['type'] == 'saved') {
                Log::info('check attachementArray ELSE CASE');
                $path_to_file = $attachementArray['filename'];
                if (file_exists($path_to_file)) {
                    Log::info("check attachementArray with saved data : {$path_to_file}");
                    $message->attach($path_to_file);
                }
            }
        });
    }
}
