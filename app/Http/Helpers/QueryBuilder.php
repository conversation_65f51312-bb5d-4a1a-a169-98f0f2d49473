<?php

namespace App\Http\Helpers;

use App\Models\Facility;
use App\Models\OauthClient;
use Carbon\Carbon;
use App\Models\ParkEngage\FacilityOvernightDuration;
use App\Models\OverstayTicket;
use App\Models\User;
use App\Models\ParkEngage\PartnerConfiguration;
use App\Models\ParkEngage\TicketExtend;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\Ticket;
use App\Models\ParkEngage\TicketTemporaryDetail;
use App\Models\PermitVehicle;
use App\Models\PermitRequest;
use App\Models\Reservation;
use App\Models\AuthorizeNetTransaction;
use App\Models\AllTransactions;
use App\Models\AllFailedTransactions;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\State;
use App\Models\PromotionDay;
use Illuminate\Support\Facades\Log;
use App\Exceptions\ApiGenericException;
use App\Models\CustomText;
use App\Models\ParkEngage\CustomerPortalPermission;
use DateTime;
use Illuminate\Support\Facades\DB;
//UPBL-87
use App\Classes\ParkengageGateApi;
use App\Models\ParkEngage\FacilitySlot;
//pims-14518
use App\Classes\LatestPromoCodeLib;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\UserPermitTypeMapping;
use App\Models\PermitServices;
use App\Models\PermitRateDescription;
use App\Models\PermitRate;
use App\Models\FacilityFee;
use App\Models\PermitVehicleMapping;
use App\Models\ParkEngage\permitTenureMapping;
use App\Models\ParkEngage\UserAccess;
use Illuminate\Support\Facades\Auth;
use App\Models\PermitRequestRenewHistory; #pims-14610 
use App\Services\PermitRateDescriptionService;

class QueryBuilder
{
    const RESERVATION_THRESHOLD_TYPE = 2;
    const FULL_DAY_HOUR_VAL = 24;
    /**
     * Append search query terms to an existing query
     *
     * @param  QueryBuilder $query  Existing laravel eloquent query, e.g. User::query();
     * @param  string       $term   Term to search for
     * @param  string       $fields Database columns to search on
     * @return [type]         [description]
     */
    public static function buildSearchQuery($query, $term, $fields)
    {
        foreach ($fields as $index => $field) {
            if (!$index) {
                $query->where($field, 'like', "%$term%");
                continue;
            }

            $query->orWhere($field, 'like', "%$term%");
        }

        return $query;
    }

    public static function buildSearchQueryForPartner($query, $term, $fields)
    {
        $query->where(function ($myQuery) use ($fields, $term) {
            foreach ($fields as $index => $field) {
                if (!$index) {
                    $myQuery->where($field, 'like', "%$term%");
                    continue;
                }

                $myQuery->orWhere($field, 'like', "%$term%");
            }
        });

        return $query;
    }

    public static function currencyFormat($value)
    {
        return sprintf("%.2f", $value);
    }

    public static function safeMax($value)
    {
        return max(0, $value);
    }

    public static function checkEqualValue($value1, $value2, $precision = 2)
    {
        return round((float) $value1, $precision) == round((float) $value2, $precision);
    }

    public static function checkValidMobileLength($string)
    {
        if (!empty($string)) {
            if (strlen(substr($string, -10)) == 10) {
                return true;
            }
        }
        return false;
    }

    public static function appendCountryCode($phone = '')
    {
        try {
            $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
            if ($geoLocation['geoplugin_countryCode'] == 'IN') {
                $countryCode = "+91";
            } elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
                $countryCode = "+1";
            } else {
                $countryCode = "+1";
            }
            if (!empty($phone)) {
                return $countryCode . $phone;
            }
            return $countryCode;
        } catch (\Exception $e) {
            $countryCode = "+1";
            return $countryCode;
        }
    }

    public static function formatPhoneNumber($phoneNumber, $is_countryCode = true)
    {

        // Remove any non-digit characters
        $cleaned = preg_replace('/\D/', '', $phoneNumber);

        // Check if the number has 10 digits
        if (strlen($cleaned) === 10) {
            $formattedNumber = sprintf(
                '(%s) %s-%s',
                substr($cleaned, 0, 3),
                substr($cleaned, 3, 3),
                substr($cleaned, 6)
            );

            // If country code is required, prepend it
            if ($is_countryCode) {
                $formattedNumber = self::appendCountryCode() . ' ' . $formattedNumber;
            }

            return $formattedNumber;
        }

        // If the number doesn't have 10 digits and a country code is required, prepend the country code
        if ($is_countryCode) {
            $phoneNumber = self::appendCountryCode() . ' ' . $phoneNumber;
        }

        // Return the original number if it doesn't have 10 digits
        return $phoneNumber;
    }


    public static function getConvertedLength($length)
    {
        $explode = explode(".", $length);
        $diffInHours = $explode[0];
        $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;

        if ($diffInMinutes > 0) {
            $diffInMints =  ($diffInMinutes * 100) / 60;
            $diffInHours = $diffInHours . '.' . $diffInMints;
        } else {
            $diffInHours = $length;
        }

        return $diffInHours;
    }

    public static function checkMints(string $mints)
    {
        if (strlen($mints) == 2) {
            $calulatedMints = (int) $mints;
        } else if (in_array($mints, ['1', '2', '3', '4', '5'])) {
            $calulatedMints = $mints * 10;
        } else {
            $calulatedMints = $mints;
        }
        return $calulatedMints;
    }

    public static function getLengthInMints(float $length)
    {
        if ($length <= 0) {
            return $length;
        }
        $explode = explode(".", $length);
        $diffInHours = $explode[0];
        $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;
        if ($diffInMinutes > 0) {
            if ($diffInHours > 0) {
                $diffInMints =  ($diffInHours * 60) + self::checkMints($diffInMinutes);
            } else {
                $diffInMints = $length * 100;
            }
        } else {
            $diffInMints = $diffInHours * 60;
        }
        return $diffInMints;
    }

    public static function getLengthInHours($length)
    {
        $diffInHours = 0;
        if ($length >= 60) {
            $lengthInhours = ($length / 60);
            $explode = explode(".", $lengthInhours);
            $diffInHours = isset($explode[0]) ? $explode[0] : intval($lengthInhours);
            $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;

            if ($diffInMinutes > 0) {
                $getRemaingmints = $length - ($diffInHours * 60);
                $mints = $getRemaingmints / 100;
                $diffInHours = $diffInHours + $mints;
            }
            return floatval($diffInHours);
        } else {
            if ($length < 10) {
                $diffInHours =  '0.0' . $length;
            } else {
                $diffInHours =  '0.' . $length;
            }
        }
        return floatval($diffInHours);
    }

    // created to formate the reservation length in day,hours,min 13-09-2023 by
    public static function getFormatedDurationByLength($length)
    {
        // $length = $reservation->length . "";
        $length = explode('.', $length);
        $hours = $length[0];
        $minute = 0;

        if (isset($length[1])) {
            $minute = $length[1];
        }
        $days = round($hours / 24);
        $duration = '';
        $s = ($hours > 1) ? 's ' : '';
        if ($hours > 0) {
            $duration = $hours . ' Hr' . $s;
        }
        if ($hours > 24) {
            $days = floor($hours / 24);
            $hours = $hours % 24;
            if ($days == 1) {
                $duration = $days . " Day ";
            } else {
                $duration = $days . " Days ";
            }
            if ($hours > 0) {
                $duration = $duration . "" . $hours . ' Hr' . $s;
            }
        }
        if ($minute > 0) {
            $duration = $duration . $minute . " Min";
        }
        return $duration;
    }

    public static function getDeviceTypeID($deviceType)
    {
        if ($deviceType == 'android') {
            $deviceType = '6';
        } elseif ($deviceType == 'IOS') {
            $deviceType = '7';
        } else {
            $deviceType = '0';
        }
        return $deviceType;
    }

    public static function getFormatedLengthForPrice($length)
    {
        $explode = explode(".", $length);
        $diffInHours = $explode[0];
        $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;


        if ($diffInMinutes < 60 && $diffInHours == '0') {
            // if ($diffInMinutes < 10) {
            //     $diffInHours =  '0.' . $diffInMinutes;
            // } else {
            // }
            $diffInHours =  '0.' . $diffInMinutes;
            // $diffInHours = $diffInMinutes / 100;
        } else if ($diffInHours > 0) {
            if ($diffInMinutes < 10) {
                $diffInMinutesInNew =  '0.0' . $diffInMinutes;
            } else {
                $diffInMinutesInNew =  '0.' . $diffInMinutes;
            }
            // $diffInMinutesInNew = $diffInMinutes / 100;
            $diffInHours = $diffInHours + $diffInMinutesInNew;
        }

        return $diffInHours;
    }

    public static function addMintsInCurrentDatetime($lengthInMints)
    {
        // Carbon::now()->addMinutes($lengthInMints)->format
        return  Carbon::parse('now')->addMinutes($lengthInMints)->format('Y-m-d H:i:s');
    }

    public static function addMintsInDatetime(Carbon $time, $lengthInMints, $type = 'mints')
    {
        if ($type == 'mints') {
            $checkoutTime = Carbon::parse($time)->addMinutes($lengthInMints)->format('Y-m-d H:i:s');
        } else {
            // Hours
            $lengthInMints = $lengthInMints * 60;
            $checkoutTime = Carbon::parse($time)->addMinutes($lengthInMints)->format('Y-m-d H:i:s');
        }

        return $checkoutTime;
    }

    public static function getNowTime()
    {
        return Carbon::parse('now')->format('Y-m-d H:i:s');
    }

    public static function getArrivalTime()
    {
        return Carbon::parse(self::getNowTime())->format('Y-m-d H:i:s');
    }

    public function getUngatedDinffInHours($lengthInMints = false, $overstayCheckingTime = false)
    {
        if ($lengthInMints) {
            if ($overstayCheckingTime == false) {
                $checkinTime = Carbon::parse($this->checkin_time);
            } else {
                $checkinTime = Carbon::parse($overstayCheckingTime);
            }
            $nowTime = Carbon::parse('now');
            $diffInHours = $checkinTime->diffInRealHours($nowTime);
            $diffInMinutes = $checkinTime->diffInRealMinutes($nowTime);
            $diffInSeconds = $checkinTime->diffInSeconds($nowTime);
            // dd($checkinTime, $nowTime, $diffInHours);

            // dd($diffInHours, $diffInMinutes, $diffInSeconds);
            if ($diffInMinutes < 60) {
                $diffInHours = $diffInMinutes / 100;
            }
            // dd($diffInHours);
            if ($diffInMinutes > 59) {
                if ($diffInHours > 0) {
                    $diffInMints = $diffInMinutes - ($diffInHours * 60);
                    $NewdiffInMints = $this->addZero($diffInMints);
                    $diffInHours = $diffInHours + $NewdiffInMints;
                } else {
                    $diffInHours = $this->addZero($diffInMinutes);
                }
                // dd($diffInMints, $diffInHours);
            }
            if ($diffInSeconds < 60) {
                $diffInHours = .01;
            }
            return $diffInHours;
            // return $this->getSubscriptionEndDate($this->checkin_time);
        }
        return Carbon::parse('now')->format('Y-m-d H:i:s');
    }

    public static function get_mimetype($filepath)
    {
        if (!preg_match('/\.[^\/\\\\]+$/', $filepath)) {
            return finfo_file(finfo_open(FILEINFO_MIME_TYPE), $filepath);
        }
        switch (strtolower(preg_replace('/^.*\./', '', $filepath))) {
            // START MS Office 2007 Docs
            case 'docx':
                return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            case 'docm':
                return 'application/vnd.ms-word.document.macroEnabled.12';
            case 'dotx':
                return 'application/vnd.openxmlformats-officedocument.wordprocessingml.template';
            case 'dotm':
                return 'application/vnd.ms-word.template.macroEnabled.12';
            case 'xlsx':
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            case 'xlsm':
                return 'application/vnd.ms-excel.sheet.macroEnabled.12';
            case 'xltx':
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.template';
            case 'xls':
                return 'application/vnd.ms-excel';
            case 'xltm':
                return 'application/vnd.ms-excel.template.macroEnabled.12';
            case 'xlsb':
                return 'application/vnd.ms-excel.sheet.binary.macroEnabled.12';
            case 'xlam':
                return 'application/vnd.ms-excel.addin.macroEnabled.12';
            case 'pptx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
            case 'pptm':
                return 'application/vnd.ms-powerpoint.presentation.macroEnabled.12';
            case 'ppsx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.slideshow';
            case 'ppsm':
                return 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12';
            case 'potx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.template';
            case 'potm':
                return 'application/vnd.ms-powerpoint.template.macroEnabled.12';
            case 'ppam':
                return 'application/vnd.ms-powerpoint.addin.macroEnabled.12';
            case 'sldx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.slide';
            case 'sldm':
                return 'application/vnd.ms-powerpoint.slide.macroEnabled.12';
            case 'one':
                return 'application/msonenote';
            case 'onetoc2':
                return 'application/msonenote';
            case 'onetmp':
                return 'application/msonenote';
            case 'onepkg':
                return 'application/msonenote';
            case 'thmx':
                return 'application/vnd.ms-officetheme';
                //END MS Office 2007 Docs
        }

        return finfo_file(finfo_open(FILEINFO_MIME_TYPE), $filepath);
    }

    public static function slugify($text, string $divider = '-')
    {
        // replace non letter or digits by divider
        $text = preg_replace('~[^\pL\d]+~u', $divider, $text);

        // transliterate
        $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

        // remove unwanted characters
        $text = preg_replace('~[^-\w]+~', '', $text);

        // trim
        $text = trim($text, $divider);

        // remove duplicate divider
        $text = preg_replace('~-+~', $divider, $text);

        // lowercase
        $text = strtolower($text);

        if (empty($text)) {
            return 'n-a';
        }

        return $text;
    }

    // Vijay : 27-02-2024
    public static function clean($string)
    {


        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }
    public static function removeSpecialChar($string)
    {
        $string = str_replace('-', '', $string); // Replaces all spaces with hyphens.
        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }

    // Vijay : 31-01-2024
    public static function getRate($facility, $arrivalTime, $diffInHours)
    {
        $isMember = 0;
        if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
            \Log::info("getRate from Helper 111 ");
            $rate = $facility->rateForReservationByPassRateEngine($arrivalTime, $diffInHours, false, false, null, false, false, '0', $isMember);
        } else {
            \Log::info("getRate from Helper 222 ");
            $rate = $facility->rateForReservationOnMarker($arrivalTime, $diffInHours, false, false, false, true, false, 0, $isMember);
        }
        return $rate;
    }

    /* 
      @ Facility Type is gated or ungated 
      @ 1 => Gated , 0 => ungated  
    */
    public static function isOvernightOrNormal($ticket, $facility_type, $arrival_time, $length_of_stay)
    {
        // $exitTime = Carbon::parse('now');
        // $exitTime = Carbon::parse('2023-11-22 06:04:01');
        $overnightPaymentStatus = [];
        // $overnightPaymentStatus['status'] = false;
        $overnightFallingTime = FacilityOvernightDuration::where(['rate_category_id' => '43', 'active' => '1', 'partner_id' => $ticket->facility->owner_id])->first();
        if ($overnightFallingTime) {
            // Gated 
            if ($facility_type == '1') {
                $exitTime = Carbon::parse('now');
                $OverStratTime = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnigth_start_time);
                $OverEndTime = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnight_end_time);
                $exitBetweenOvernight =  $exitTime->between($OverStratTime, $OverEndTime);
                $ticketPaymentDate = Carbon::parse($ticket->payment_date);
                $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);

                // check Overstay and payment for normal and overstay
                $extendOverstay = OverstayTicket::where(['ticket_id' => $ticket->id])->orderBy('id', 'DESC')->first();
            } else { // UnGated

                $exitTime = Carbon::parse(QueryBuilder::addMintsInDatetime(Carbon::parse($arrival_time), QueryBuilder::getLengthInMints($length_of_stay)));
                $OverStratTime = self::timestampToCarbon($exitTime, $overnightFallingTime->overnigth_start_time);
                $OverEndTime = self::timestampToCarbon($exitTime, $overnightFallingTime->overnight_end_time);

                // Vijay : 28-05-2024 : USM Overnight 
                $isSameDay = $OverStratTime->isSameDay($OverEndTime);       // to check overnight is start day before and end in Next Day.  // expected false
                // $entryNextDay = $arrival_time->isSameDay($rateEndEntryTime);    // to check entry of user in next but overnight is started Day Before. // expected true

                // Vijay : 28-09-2024-To handle Usm Case but approch is genric.
                // user at next day then i will substract 1 Day to check today valid till.
                if ($isSameDay == false && $carbonNow->lte(self::timestampToCarbon(Carbon::parse('now')->subDay(), $overnightFallingTime->overnight_end_time))) {
                    $OverStratTime = self::timestampToCarbon(Carbon::parse('now')->subDay(), $overnightFallingTime->overnigth_start_time);       // Rate Exit Start time 
                    $OverEndTime = self::timestampToCarbon(Carbon::parse('now')->subDay(), $overnightFallingTime->overnight_end_time);             // Rate Exit End time                    
                    // dd($isSameDay, Carbon::parse('now'), $carbonNow, $OverStratTime, $OverEndTime, $carbonNow->lte(self::timestampToCarbon(Carbon::parse('now')->subDay(), $overnightFallingTime->overnight_end_time)));
                }

                /*if ($overnightFallingTime->rate_category_id == 362) {
                    $OverStratTime = self::timestampToCarbon(Carbon::parse($arrival_time), $overnightFallingTime->overnigth_start_time);
                    if (strtotime($overnightFallingTime->overnigth_start_time) > strtotime($overnightFallingTime->overnight_end_time)) {
                        $OverStratTime = self::timestampToCarbon(Carbon::parse($arrival_time), $overnightFallingTime->overnigth_start_time)->subDays(1);
                    }
                    $OverEndTime = self::timestampToCarbon(Carbon::parse($arrival_time), $overnightFallingTime->overnight_end_time);
                }*/

                $exitBetweenOvernight =  $exitTime->between($OverStratTime, $OverEndTime);

                // PIMS-11890 start date changes after discussion by vijay sir
                // $ticketPaymentDate = Carbon::parse($ticket->checkout_time);
                // $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
                if (!empty($ticket->payment_date)) {
                    $ticketPaymentDate = Carbon::parse($ticket->payment_date);
                    $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
                } else {
                    $paymentStatus = false;
                }


                // check Overstay and payment for normal and overstay
                $extendOverstay = TicketExtend::where(['ticket_id' => $ticket->id])->orderBy('id', 'DESC')->first();
                if ($extendOverstay) {
                    $ticketPaymentDate = Carbon::parse($extendOverstay->checkout_time);
                    $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
                }
            }
            // dd($ticketPaymentDate, $exitBetweenOvernight, $paymentStatus);
            \Log::info("exitIslowerToEndTime exitIslowerToEndTime {$exitBetweenOvernight}");
            if ($extendOverstay) {
                //  either Reservation or Normal Drive up.
                $ticketPaymentDate = Carbon::parse($extendOverstay->checkout_time);
                $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
                if ($exitBetweenOvernight && $paymentStatus) {
                    $overnightPaymentStatus['isOvernightExit'] = true;
                    $overnightPaymentStatus['isOvernightPaid'] = true;
                } else if ($exitBetweenOvernight) {
                    $overnightPaymentStatus['isOvernightExit'] = true;
                    $overnightPaymentStatus['isOvernightPaid'] = false;
                } else if ($paymentStatus) {
                    $overnightPaymentStatus['isOvernightExit'] = false;
                    $overnightPaymentStatus['isOvernightPaid'] = true;
                } else {
                    return false;
                }
            } else {
                // check in against Reservation 
                if ($ticket->reservation_id != '') {
                    if ($ticket->reservation->thirdparty_integration_id) { // third party pay first pay to come in overstay
                        // because fisrt need to pay
                        return false;
                    }
                }
                // checkin against reservation 
                if ($exitBetweenOvernight && $paymentStatus) {
                    $overnightPaymentStatus['isOvernightExit'] = true;
                    $overnightPaymentStatus['isOvernightPaid'] = true;
                } else if ($exitBetweenOvernight) {
                    $overnightPaymentStatus['isOvernightExit'] = true;
                    $overnightPaymentStatus['isOvernightPaid'] = false;
                } else if ($paymentStatus) {
                    $overnightPaymentStatus['isOvernightExit'] = false;
                    $overnightPaymentStatus['isOvernightPaid'] = true;
                } else {
                    return false;
                }
            }
        }
        return $overnightPaymentStatus;
        return false;
    }

    public static function timestampToCarbon(Carbon $initial, $time)
    {
        $times = explode(':', $time);

        $hour = $times[0] ?? 0;
        $minute = $times[1] ?? 0;
        $second = $times[2] ?? 0;

        return $initial->copy()->hour($hour)->minute($minute)->second($second);
    }

    /***
     *  Method to check time zone
     *  Alka::date::29 march 2024
     */
    public static function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if (($facility) && ($facility->timezone != '')) {
            date_default_timezone_set($facility->timezone);
        } else {
            if ($partnerTimezone) {
                if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }

    public static function getTableNameByNamespace($modelNamespace)
    {
        // Resolve the model class name from the given namespace
        $modelName = class_basename($modelNamespace);

        // Attempt to instantiate the model
        $model = new $modelNamespace();
        $table_name = $model->getTable();

        // Check if the model has a table name defined
        if (isset($table_name)) {
            return $table_name;
        }

        // If the table name is not defined, get it from the database schema
        // return $model->getConnection()->getTablePrefix() . $model->getConnection()->getTable($model->getTable());
    }

    public static function getFomatedEndTime($rate, $arrival_time, $type = 'exit')
    {
        if ($type == 'exit') {
            $rateExitTime = self::timestampToCarbon($arrival_time, $rate->exit_time_end);          // Rate Exit End time 
        } else if ($type == 'entry') {
            $rateExitTime = self::timestampToCarbon($arrival_time, $rate->entry_time_begin);          // Rate Exit End time 
        }
        return Carbon::parse($rateExitTime)->format('g:i A');
    }

    public static function convertNumberToDays($number)
    {
        //  $number = "1,2,5";
        $days = '';
        if ($number != '') {
            $number_arr = explode(',', $number);
            if (in_array('1', $number_arr)) {
                $days .= 'Sunday';
            }
            if (in_array('2', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Monday';
            }
            if (in_array('3', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Tuesday';
            }

            if (in_array('4', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Wednesday';
            }
            if (in_array('5', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Thursday';
            }
            if (in_array('6', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Friday';
            }
            if (in_array('7', $number_arr)) {
                if ($days != '') {
                    $days .= ',';
                }
                $days .= 'Saturday';
            }
        }
        // dd($number_arr,$days);
        return $days;
    }
    public static function convertToUTC($facility_id, $datetime)
    {
        $timezone = Facility::where(['id' => $facility_id])->value('timezone');
        if ($timezone != '') {
            $date = Carbon::createFromFormat('Y-m-d H:i:s', $datetime, $timezone);
            // Convert the datetime to UTC
            $dateInUTC = $date->setTimezone('UTC');

            // Output the datetime in UTC
            $n_date = $dateInUTC->toDateTimeString();

            return Carbon::parse($n_date)->format('m/d/Y H:i:s');
        } else {
            return Carbon::parse($datetime)->format('m/d/Y H:i:s');
        }
    }

    public static function ticketExtendDetails($ticketId)
    {
        $ticketObj = Ticket::find($ticketId);
        $baseLengthInMints = $lengthInMints = $discountAmount = $totalAmount =  $toatlAmountPaid = $totalParkingAmount = $processingFee = $taxFee  = 0;
        if ($ticketObj->count() > 0) {
            $isExtended = $ticketObj->ticketExtend;
            foreach ($isExtended as $key => $value) {
                $totalAmount            += $value->total;
                $toatlAmountPaid        += $value->grand_total;
                $discountAmount         += $value->discount_amount;
                $totalParkingAmount     += $value->grand_total - ($value->processing_fee + $value->tax_fee + $value->additional_fee + $value->surcharge_fee);
                $processingFee          += $value->processing_fee;
                $taxFee                 += $value->tax_fee;
                $lengthInMints          += self::getLengthInMints($value->length);
                $baseLengthInMints      += self::getLengthInMints($value->base_length != null ? $value->base_length : 0);
            }
        }

        $extendDetails['extend_total']              = $totalAmount;
        $extendDetails['extend_grand_total']        = $toatlAmountPaid;
        $extendDetails['extend_parking_amouont']    = $totalParkingAmount;
        $extendDetails['extend_discoountAmount']    = $discountAmount;
        $extendDetails['extend_processin_fee']      = $processingFee;
        $extendDetails['extend_tax_fee']            = $taxFee;
        $extendDetails['extend_length']             = $lengthInMints;
        $extendDetails['extend_base_length']        = $baseLengthInMints;
        return $extendDetails;
    }

    public static function newticketExtendDetails($ticketObj)
    {
        $extendDetails = [
            'extend_total'           => 0,
            'extend_grand_total'     => 0,
            'extend_discoountAmount' => 0,
            'extend_processin_fee'   => 0,
            'extend_tax_fee'         => 0,
            'extend_parking_amouont' => 0,
            'extend_length'          => 0,
            'extend_base_length'     => 0,
        ];

        if ($ticketObj && count($ticketObj->ticketExtend) > 0) {
            $extendDetails['extend_total']           = $ticketObj->ticketExtend->sum('total');
            $extendDetails['extend_grand_total']     = $ticketObj->ticketExtend->sum('grand_total');
            $extendDetails['extend_discoountAmount'] = $ticketObj->ticketExtend->sum('discount_amount');
            $extendDetails['extend_processin_fee']   = $ticketObj->ticketExtend->sum('processing_fee');
            $extendDetails['extend_tax_fee']         = $ticketObj->ticketExtend->sum('tax_fee');

            // Calculating values using map() since Laravel 5.2 does not support arrow functions
            $extendDetails['extend_parking_amouont'] = $ticketObj->ticketExtend->map(function ($value) {
                return $value->grand_total - ($value->processing_fee + $value->tax_fee);
            })->sum();

            $extendDetails['extend_length'] = $ticketObj->ticketExtend->map(function ($value) {
                return self::getLengthInMints($value->length);
            })->sum();

            $extendDetails['extend_base_length'] = $ticketObj->ticketExtend->map(function ($value) {
                return self::getLengthInMints(!is_null($value->base_length) ? $value->base_length : 0);
            })->sum();
        }

        return $extendDetails;
    }

    public static function ticketTemporaryDetails($ticket, $priceBreakUp, $length = null)
    {
        $existTempTicket = TicketTemporaryDetail::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->delete();
        $tempTicket                     = new TicketTemporaryDetail();
        $tempTicket->ticket_id          = $ticket->id;
        $tempTicket->ticket_number      = $ticket->ticket_number;
        $tempTicket->processing_fee     = $priceBreakUp['processing_fee'];
        $tempTicket->tax_fee            = $priceBreakUp['tax_rate'];
        $tempTicket->parking_amount     = $priceBreakUp['parking_amount'];
        $tempTicket->paid_amount        = $priceBreakUp['paid_amount'];
        $tempTicket->discount_amount    = $priceBreakUp['discount_amount'];
        $tempTicket->grand_total        = $priceBreakUp['payable_amount'];
        $tempTicket->total              = $priceBreakUp['total'];
        $tempTicket->length             = $length;
        $tempTicket->save();
    }

    public static function getTicketTemporaryDetails($ticket)
    {
        return TicketTemporaryDetail::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();
    }

    public static function getPartnerConfig($key, $partner_id)
    {
        return PartnerConfiguration::where("key", $key)->where("partner_id", $partner_id)->first();
    }



    public static function ticketOverstayDetails($ticketId)
    {
        $ticketObj = Ticket::find($ticketId);
        $discountAmount = $totalAmount =  $toatlAmountPaid = $totalParkingAmount = $processingFee = $taxFee  = 0;
        if ($ticketObj->count() > 0) {
            $isOverstay = $ticketObj->overstay;
            foreach ($isOverstay as $key => $value) {
                $totalAmount            += $value->total;
                $toatlAmountPaid        += $value->grand_total;
                $discountAmount         += $value->discount_amount;
                $totalParkingAmount     += $value->grand_total - ($value->processing_fee + $value->tax_fee);
                $processingFee          += $value->processing_fee;
                $taxFee                 += $value->tax_fee;
            }
        }
        $overstayDetails['overstay_total']              = $totalAmount;
        $overstayDetails['overstay_grand_total']        = $toatlAmountPaid;
        $overstayDetails['overstay_parking_amount']    = $totalParkingAmount;
        $overstayDetails['overstay_discount_mount']    = $discountAmount;
        $overstayDetails['overstay_processing_fee']      = $processingFee;
        $overstayDetails['overstay_tax_fee']            = $taxFee;
        return $overstayDetails;
    }

    public static function setPrintReceipt($ticket, $is_checkin)
    {
        $data =  [];
        $key = 0;
        $data[$key]["key"] = "Check-In At:";
        $data[$key]["value"] = date("m/d/Y g:i A", strtotime($ticket->checkin_time));
        $key = $key + 1;
        if ($is_checkin == '1') {
            if ($ticket->permit_request_id > 0) {
                $permit = PermitRequest::find($ticket->permit_request_id);
                $data[$key]["key"] = "Permit Number:";
                $data[$key]["value"] = $permit->account_number;
                $key = $key + 1;
            }
            if ($ticket->reservation_id > 0) {
                $reservation = Reservation::find($ticket->reservation_id);
                $data[$key]["key"] = "Booking ID:";
                $data[$key]["value"] = $reservation->ticketech_code;
                $key = $key + 1;
            }
            $data[$key]["key"] = "Ticket ID:";
            $data[$key]["value"] = $ticket->ticket_number;
            $key = $key + 1;
            if ($ticket->license_plate != '') {
                $data[$key]["key"] = "LPN:";
                $data[$key]["value"] = $ticket->license_plate;
                $key = $key + 1;
            }
            if ($ticket->card_last_four != '') {
                $data[$key]["key"] = "Credit Card Used:";
                $data[$key]["value"] = $ticket->card_last_four;
                $key = $key + 1;
            }
        } else {
            $data[$key]["key"] = "Check-Out At:";
            $data[$key]["value"] = date("m/d/Y g:i A", strtotime($ticket->checkout_time));
            $key = $key + 1;

            if ($ticket->permit_request_id > 0) {
                $permit = PermitRequest::find($ticket->permit_request_id);
                $data[$key]["key"] = "Permit Number:";
                $data[$key]["value"] = $permit->account_number;
                $key = $key + 1;
            }
            if ($ticket->reservation_id > 0) {
                $reservation = Reservation::find($ticket->reservation_id);
                $data[$key]["key"] = "Booking ID:";
                $data[$key]["value"] = $reservation->ticketech_code;
                $key = $key + 1;
            }

            $data[$key]["key"] = "Ticket ID:";
            $data[$key]["value"] = $ticket->ticket_number;
            $key = $key + 1;

            if ($ticket->checkout_license_plate != '') {
                $data[$key]["key"] = "LPN:";
                $data[$key]["value"] = $ticket->license_plate;
                $key = $key + 1;
            }
            if ($ticket->card_last_four != '') {
                $data[$key]["key"] = "Credit Card Used:";
                $data[$key]["value"] = $ticket->card_last_four;
                $key = $key + 1;
            }
            if ($ticket->grand_total > 0) {
                $data[$key]["key"] = "Line";
                $data[$key]["value"] = "-----------------------------------------";
                $key = $key + 1;
            }

            $totalParkingAmount = $ticket->parking_amount;
            $totalPaidAmount = $ticket->grand_total;
            $totalProcessingFee = $ticket->processing_fee;
            $totalTaxFee = $ticket->tax_fee;
            $totalValidatedAmount = $ticket->paid_amount;
            $ticketOverstayDetails = QueryBuilder::ticketOverstayDetails($ticket->id);
            if ($ticketOverstayDetails) {
                $totalPaidAmount = $totalPaidAmount + $ticketOverstayDetails['overstay_grand_total'];
                $totalParkingAmount = $totalParkingAmount + $ticketOverstayDetails['overstay_parking_amount'];
                $totalProcessingFee = $ticket->processing_fee + $ticketOverstayDetails['overstay_processing_fee'];
                $totalTaxFee = $ticket->tax_fee + $ticketOverstayDetails['overstay_tax_fee'];
            }
            if ($totalParkingAmount > 0) {
                $data[$key]["key"] = "Parking Amount:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalParkingAmount);
                $key = $key + 1;
            }
            if ($totalProcessingFee > 0) {
                $data[$key]["key"] = "Processing Fee:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalProcessingFee);
                $key = $key + 1;
            }
            if ($totalTaxFee > 0) {
                $data[$key]["key"] = "Tax:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalTaxFee);
                $key = $key + 1;
            }
            if ($totalValidatedAmount > 0) {
                $data[$key]["key"] = "Validated Amount:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalValidatedAmount);
                $key = $key + 1;
            }
            if ($totalPaidAmount > 0) {
                $data[$key]["key"] = "Amount Paid:";
                $data[$key]["value"] = "$" . sprintf("%.2f", $totalPaidAmount);
            }
        }
        return $data;
    }

    /* this function is use to check users license plate already exists
     */
    public static function isUserVehicleExists($licensePlate, $user_id, $partner_id)
    {
        $PermitVehicle = PermitVehicle::where(['license_plate_number' => $licensePlate, 'user_id' => $user_id, 'partner_id' => $partner_id])->first();
        if ($PermitVehicle) {
            return $PermitVehicle;
        } else {
            return false;
        }
    }

    public static function getFormatedHooTime($arrival_time, $time)
    {
        $rateExitTime = self::timestampToCarbon($arrival_time, $time);          // Rate Exit End time 

        return Carbon::parse($rateExitTime)->format('g:i A');
    }

    public static function setReferenceKey($anetId, $ticketRefrenceNumber)
    {
        $paymentRefrenceId = AuthorizeNetTransaction::find($anetId);
        if ($paymentRefrenceId) {
            $paymentRefrenceId->reference_key = $ticketRefrenceNumber;
            $paymentRefrenceId->save();
        }
    }

    #PIMS-11368 DD
    public static function setAllTransactions($jsonString, $gatewaytype = NULL, $reference_key = NULL, $action = NULL, $user_id = NULL)
    {
        $data = json_decode($jsonString, true);
        Log::info("Save Fail Transacrion");
        Log::info($data);
        // Log::info('Payment Status : ' . $data['Status']);
        $user = new AllTransactions();
        $user->total =  isset($data['total']) ? $data['total'] : '';

        if (isset($data['total'])) {
            $user->total = $data['total'];
        } else if (isset($data['balanceAmount'])) {
            $user->total = $data['balanceAmount'];
        } else if (isset($data['Response']['Params']['AmountUsed'])) {
            $user->total = $data['Response']['Params']['AmountUsed'];
        } else {
            $user->total = '';
        }
        if (isset($data['user_id'])) {
            $user->user_id = $data['user_id'];
        } else if (isset($user_id)) {
            $user->user_id = $user_id;
        } else {
            $user->user_id = '';
        }
        if (isset($data['description'])) {
            $user->description = $data['description'];
        } else if (isset($data['token'])) {
            $user->description = $data['token'];
        } else if (isset($data['Response']['Params']['TxID'])) {
            $user->description = $data['Response']['Params']['TxID'];
        } else {
            $user->description = '';
        }
        if (isset($data['response_code'])) {
            $user->response_code = $data['response_code'];
        } else if (isset($data['responseCode'])) {
            $user->response_code = $data['responseCode'];
        } else {
            $user->response_code = '';
        }
        if (isset($data['response_message'])) {
            $user->response_message = $data['response_message'];
        } else if (isset($data['responseMessage'])) {
            $user->response_message = $data['responseMessage'];
        } else if (isset($data['Response']['Params']['ResultReason'])) {
            $user->response_message = $data['Response']['Params']['ResultReason'];
        } else {
            $user->response_message = '';
        }
        if (isset($data['transactionReference']['authCode'])) {
            $user->auth_code = $data['transactionReference']['authCode'];
        } else if (isset($data['Response']['Params']['BankAuthCode'])) {
            $user->response_message = $data['Response']['Params']['BankAuthCode'];
        } else if (isset($data['auth_code'])) {
            $user->auth_code = $data['auth_code'];
        } else {
            $user->auth_code = '';
        }
        if (isset($data['ref_id'])) {
            $user->ref_id = $data['ref_id'];
        } else if (isset($data['referenceNumber'])) {
            $user->ref_id = $data['referenceNumber'];
        } else if (isset($data['Response']['Params']['RequesterTransRefNum'])) {
            $user->ref_id = $data['Response']['Params']['RequesterTransRefNum'];
        } else {
            $user->ref_id = '';
        }
        if (isset($data['status_message'])) {
            $user->status_message = $data['status_message'];
        } else if (isset($data['cvnResponseMessage'])) {
            $user->status_message = $data['cvnResponseMessage'];
        } else if (isset($data['Response']['Params']['TxStateText'])) {
            $user->status_message = $data['Response']['Params']['TxStateText'];
        } else {
            $user->status_message = '';
        }
        if (is_null($reference_key) && isset($data['reference_key'])) {
            $user->reference_key =  $data['reference_key'];
        } else {
            $user->reference_key =  $reference_key;
        }
        if (isset($data['created_at'])) {
            $user->created_at = $data['created_at'];
        }
        //$user->created_at = isset($data['created_at']) ? $data['created_at'] : '';
        $user->status_code = isset($data['status_code']) ? $data['status_code'] : '';
        $user->status_type = isset($data['status_type']) ? $data['status_type'] : '';

        $user->failed_transactionscol = isset($data['failed_transactionscol']) ? $data['failed_transactionscol'] : '';
        $user->response_json = $jsonString;
        $user->action = isset($action) ? $action : '';
        $user->gateway = isset($gatewaytype) ? $gatewaytype : '';

        // Update Details for Failed Case
        if ($gatewaytype == 'datacap' && $action == 'Failed') {
            if ($data['Status'] == 'Error') {
                $user->description      = isset($data['ResponseOrigin']) ? $data['ResponseOrigin'] : '';
                $user->response_code    = isset($data['ReturnCode']) ? $data['ReturnCode'] : '';
                $user->response_message = isset($data['Message']) ? $data['Message'] : '';
                $user->status_code      = isset($data['ReturnCode']) ? $data['ReturnCode'] : '';
                $user->status_type      = isset($data['Status']) ? $data['Status'] : '';
                $user->ref_id           = isset($data['RefNo']) ? $data['RefNo'] : '';
                $user->save();
            }
        }

        $user->save();
    }

    public static function setAllFailedTransactions($jsonString, $gatewaytype = NULL, $reference_key = NULL, $action = NULL, $user_id = NULL, $payment_request = NULL, $request = NULL)
    {
        $data = json_decode($jsonString, true);

        $user = new AllFailedTransactions();
        if (isset($data['total'])) {
            $user->total = $data['total'];
        } else if (isset($data['balanceAmount'])) {
            $user->total = $data['balanceAmount'];
        } else if (isset($data['Response']['Params']['AmountUsed'])) {
            $user->total = $data['Response']['Params']['AmountUsed'];
        } else if (isset($payment_request->Amount)) {
            $user->total = $payment_request->Amount;
        } else {
            $user->total = '';
        }

        if (isset($data['user_id'])) {
            $user->user_id = $data['user_id'];
        } else if (isset($user_id)) {
            $user->user_id = $user_id;
        } else {
            $user->user_id = '';
        }
        if (isset($data['description'])) {
            $user->description = $data['description'];
        } else if (isset($data['token'])) {
            $user->description = $data['token'];
        } else if (isset($data['Response']['Params']['TxID'])) {
            $user->description = $data['Response']['Params']['TxID'];
        } else {
            $user->description = '';
        }
        if (isset($data['response_code'])) {
            $user->response_code = $data['response_code'];
        } else if (isset($data['responseCode'])) {
            $user->response_code = $data['responseCode'];
        } else {
            $user->response_code = '';
        }
        if (isset($data['response_message'])) {
            $user->response_message = $data['response_message'];
        } else if (isset($data['responseMessage'])) {
            $user->response_message = $data['responseMessage'];
        } else if (isset($data['Response']['Params']['ResultReason'])) {
            $user->response_message = $data['Response']['Params']['ResultReason'];
        } else {
            $user->response_message = '';
        }
        if (isset($data['transactionReference']['authCode'])) {
            $user->auth_code = $data['transactionReference']['authCode'];
        } else if (isset($data['Response']['Params']['BankAuthCode'])) {
            $user->response_message = $data['Response']['Params']['BankAuthCode'];
        } else if (isset($data['auth_code'])) {
            $user->auth_code = $data['auth_code'];
        } else {
            $user->auth_code = '';
        }
        if (isset($data['ref_id'])) {
            $user->ref_id = $data['ref_id'];
        } else if (isset($data['referenceNumber'])) {
            $user->ref_id = $data['referenceNumber'];
        } else if (isset($data['Response']['Params']['RequesterTransRefNum'])) {
            $user->ref_id = $data['Response']['Params']['RequesterTransRefNum'];
        } else {
            $user->ref_id = '';
        }
        if (isset($data['status_message'])) {
            $user->status_message = $data['status_message'];
        } else if (isset($data['cvnResponseMessage'])) {
            $user->status_message = $data['cvnResponseMessage'];
        } else if (isset($data['Response']['Params']['TxStateText'])) {
            $user->status_message = $data['Response']['Params']['TxStateText'];
        } else {
            $user->status_message = '';
        }
        if (is_null($reference_key) && isset($data['reference_key'])) {
            $user->reference_key =  $data['reference_key'];
        } else {
            $user->reference_key =  $reference_key;
        }
        if (isset($data['created_at'])) {
            $user->created_at = $data['created_at'];
        }
        if (isset($payment_request) && !empty($payment_request)) {
            $user->payment_request_json = $payment_request;
        }
        //$user->created_at = isset($data['created_at']) ? $data['created_at'] : '';
        $user->status_code = isset($data['status_code']) ? $data['status_code'] : '';
        $user->status_type = isset($data['status_type']) ? $data['status_type'] : '';

        $user->failed_transactionscol = isset($data['failed_transactionscol']) ? $data['failed_transactionscol'] : '';
        $user->response_json = $jsonString;
        $user->action = isset($action) ? $action : '';
        $user->gateway = isset($gatewaytype) ? $gatewaytype : '';
        $user->license_plate = isset($request->vehicleList) ? $request->vehicleList : '';

        $expiry = "";
        $card_last_four = "";

        if (isset($request->card_last_four)) {
            $card_last_four = $request->card_last_four;
        } elseif (isset($request->payment_last_four)) {
            $card_last_four = $request->payment_last_four;
        }

        if (isset($request->expiration_date)) {
            $expiry = $request->expiration_date;
        } elseif (isset($request->expiry)) {
            $expiry = $request->expiry;
        } elseif (isset($request->expiration)) {
            $expiry = $request->expiration;
        }

        $user->card_last_four    = isset($card_last_four) ? $card_last_four : '0';
        $user->expiry               = isset($expiry) ? $expiry : '0';
        if ((strtolower($gatewaytype) == 'heartland') && ($action == 'Failed')) {
            $user->total                    = isset($paymentRequest->Amount) ? $paymentRequest->Amount : '';
            $user->description              = isset($data['responseMessage']) ? $data['responseMessage'] : '';
            $user->response_code            = isset($data['responseCode']) ? $data['responseCode'] : '';
            $user->response_message         = isset($data['responseMessage']) ? $data['responseMessage'] : '';
            $user->status_code              = isset($data['responseCode']) ? $data['responseCode'] : '';
            $user->ref_id                   = isset($data['transactionReference']['transactionId']) ? $data['transactionReference']['transactionId'] : '';
            if (json_last_error() === JSON_ERROR_NONE && $data !== null) {
            } else {
                $user->response_message = isset($jsonString) ? $jsonString : '';
            }
            $user->save();
        }
        $user->save();
    }

    public static function getTimeDifferenceInHHMM($startDateTime, $endDateTime)
    {
        // // Parse the date-time strings into Carbon instances // with seconds
        // $start = Carbon::parse($startDateTime);
        // $end = Carbon::parse($endDateTime);
        // Parse the date-time strings into Carbon instances
        $start = Carbon::parse($startDateTime)->startOfMinute(); // Ignore seconds
        $end = Carbon::parse($endDateTime)->startOfMinute();     // Ignore seconds

        // Calculate the difference in total minutes
        $totalMinutes = $end->diffInMinutes($start);

        // Convert minutes to hours and remaining minutes
        $hours = intdiv($totalMinutes, 60); // Get the number of hours
        $minutes = $totalMinutes % 60;     // Get the remaining minutes

        // Format the output as HH:MM
        return sprintf('%02d.%02d', $hours, $minutes);
    }

    public static function getDiffByColumn(Collection $mainCollection, Collection $compareCollection, $column)
    {
        // Extract the column values from the compare collection
        $compareValues = $compareCollection->pluck($column)->toArray();

        // Filter the main collection to exclude records with matching column values
        return $mainCollection->filter(function ($item) use ($compareValues, $column) {
            return !in_array($item[$column], $compareValues);
        });
    }

    public static function getRegisterUserProfileId($gatewayType, $cardLastFour, $cardExpiry, $userId, $partnerId)
    {
        switch ($gatewayType) {
            case '1':
                $cardDetails = PlanetPaymentProfile::where(['user_id' => $userId, 'card_last_four' => $cardLastFour, 'partner_id' => $partnerId])->whereNull('deleted_at')->latest()->first();
                break;
            case '2':
                $cardDetails = DatacapPaymentProfile::where(['user_id' => $userId, 'card_last_four' => $cardLastFour, 'partner_id' => $partnerId])->whereNull('deleted_at')->latest()->first();
                break;
            case '4':
                $cardDetails = HeartlandPaymentProfile::where(['user_id' => $userId, 'card_last_four' => $cardLastFour, 'partner_id' => $partnerId])->whereNull('deleted_at')->latest()->first();
                break;
            default:
                Log::error("No Card Found for GateWayType : {$gatewayType} User ID : {$userId} and Last Four : {$cardLastFour} : Exppiry : {$cardExpiry} ");
                return false;
                break;
        }
        return $cardDetails;
    }

    public static function calculateProratePriceOnPromotion($rate, $noOfDays)
    {

        $parkingAmount      = $rate['price'];
        $rateHours          = $rate['max_stay'];

        $pricePerHours      = $parkingAmount / $rateHours;
        $payableAmount      = $noOfDays * $pricePerHours;

        // dd('q2', $parkingAmount, $rateHours, $pricePerHours, $payableAmount);
        return sprintf("%.2f", $payableAmount);
    }

    public static function getPayableAndDiscountHours($request, $rate)
    {

        // calulate Payable Hours for Price
        $tillNowLengthInMints   = self::getLengthInMints($request->tillNowLength);
        $oneDayLengthInMInts    = self::getLengthInMints(self::FULL_DAY_HOUR_VAL);
        $promoUsedHours         = (int) $request->promoUsedHours;
        $requestedLengthDays    = $rate['max_stay'] >= self::FULL_DAY_HOUR_VAL ? ceil($rate['max_stay'] / self::FULL_DAY_HOUR_VAL) : 0;

        $remaningPromoHoursInaDays = 0;   // Remaing Promo Hours in 24 Hours (1 Day) 
        if ($request->perDayFreeHours > $request->promoUsedHours) {
            $remaningPromoHoursInaDays = (int) ($request->perDayFreeHours - $request->promoUsedHours);   // Remaing Promo Hours in 24 Hours (1 Day) 
        }

        // dd(ceil($rate['max_stay'] / self::FULL_DAY_HOUR_VAL));
        // dd($request->all(), $promoUsedHours, $remaningPromoHoursInaDays, $tillNowLengthInMints, $oneDayLengthInMInts, $requestedLengthDays, $rate['max_stay']);
        // if ($rate['max_stay'] > self::FULL_DAY_HOUR_VAL) {
        // dd($tillNowLengthInMints, $oneDayLengthInMInts, $tillNowLengthInMints > $oneDayLengthInMInts);
        if ($tillNowLengthInMints > $oneDayLengthInMInts) {
            if ($promoUsedHours >= $request->perDayFreeHours) {

                if ($request->length  <= $request->perDayFreeHours) {
                    $payableHours       =   0;
                    $dayHourInMints     =   $request->length;
                } else {
                    // dd($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL, $requestedLengthDays, $requestedLengthDays * $request->perDayFreeHours);
                    if ($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL) {  // More that 24 hour case 
                        $payableHours       =   self::getLengthInHours(self::getLengthInMints($rate['max_stay']) - self::getLengthInMints($requestedLengthDays * $request->perDayFreeHours));
                        $dayHourInMints     =   $requestedLengthDays * $request->perDayFreeHours;
                    } else {
                        // Request in Hours less than 24 hours
                        $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->length) - self::getLengthInMints($request->perDayFreeHours));
                        $dayHourInMints     =   $request->perDayFreeHours;
                    }
                }
            } else {
                if ($request->length  <= $request->perDayFreeHours) {
                    $payableHours       =   0;
                    $dayHourInMints     =   $request->length;
                } else {
                    // dd($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL, $requestedLengthDays, $requestedLengthDays * $request->perDayFreeHours);
                    if ($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL) {  // More that 24 hour case 
                        $payableHours       =   self::getLengthInHours(self::getLengthInMints($rate['max_stay']) - self::getLengthInMints($requestedLengthDays * $request->perDayFreeHours));
                        $dayHourInMints     =   $requestedLengthDays * $request->perDayFreeHours;
                    } else {
                        // Request in Hours less than 24 hours
                        $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->length) - self::getLengthInMints($request->perDayFreeHours));
                        $dayHourInMints     =   $request->perDayFreeHours;
                    }
                }
            }
            $result['payableHours']  = $payableHours;
            $result['discountHours'] =  $dayHourInMints;
            return $result;
        } else {
            if ($request->promoUsedHours < $request->perDayFreeHours && $request->length <= $remaningPromoHoursInaDays) {
                $payableHours       =   0;
                $dayHourInMints     =   $request->length;
            } else if ($request->length  <= $request->perDayFreeHours && $request->length > $remaningPromoHoursInaDays) {
                // $payableHours       =   $request->length - $remaningPromoHoursInaDays;
                $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->length) - self::getLengthInMints($remaningPromoHoursInaDays));
                $dayHourInMints     =   $remaningPromoHoursInaDays;
            } else {
                // dd($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL, $requestedLengthDays, $requestedLengthDays * $request->perDayFreeHours);
                if ($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL) {  // More that 24 hour case 
                    $payableHours       =   self::getLengthInHours(self::getLengthInMints($rate['max_stay']) - self::getLengthInMints($requestedLengthDays * $request->perDayFreeHours));
                    $dayHourInMints     =   $requestedLengthDays * $request->perDayFreeHours;
                } else {
                    // Request in Hours less than 24 hours
                    $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->length) - self::getLengthInMints($request->perDayFreeHours));
                    $dayHourInMints     =   $request->perDayFreeHours;
                }
            }
            $result['payableHours']  = $payableHours;
            $result['discountHours'] =  $dayHourInMints;
            return $result;
            $payableHours       =   self::getLengthInHours(self::getLengthInMints($request->tillNowLength) - self::getLengthInMints(self::FULL_DAY_HOUR_VAL));
            $dayHourInMints     =   self::getLengthInMints(self::FULL_DAY_HOUR_VAL);
        }
        // cacluate applied discunt Hours
        // dd($request->all(), self::getLengthInMints($request->totalLength) + self::getLengthInMints($rate['max_stay']));
        $totalLength        = (self::getLengthInMints($request->totalLength) + self::getLengthInMints($rate['max_stay']));

        // dd($totalLength > $dayHourInMints);
        if ($totalLength > $dayHourInMints) {
            $nextdayHours       =   self::getLengthInHours($totalLength - $dayHourInMints);
        } else {
            $nextdayHours       = 0;
        }

        $result['payableHours']  = $payableHours;
        $result['discountHours'] =  $nextdayHours;

        return $result;
    }

    // PIMS - 
    public static function calculateHourlyPromotions($request, $appliedPromotion, $rate, $facility, $serviceType)
    {
        if ($serviceType == 'transient') {
            $diffInHours   = $request->length;
            $payableAmount = self::calculateProratePriceOnPromotion($rate, $diffInHours);
            return $payableAmount;
        }
    }
    public static function getDayWisePromocodeUsed($lengthInhours)
    {
        $lengthInhours = self::getLengthInMints($lengthInhours);
        if ($lengthInhours > 1440) {
            $noOfDayUsed =  ceil($lengthInhours / 1440);
            return $noOfDayUsed;
        } else if ($lengthInhours > 0) {
            return 1;
        }
        return false;
    }


    /*
    * Dev: Sagar
    * PIMS - 11744
    * checking promocode for daywise with time range
    */
    public static function checkDayWiseValidationForPromoCode($promotion)
    {
        $checkApplicableDays = PromotionDay::where('promotion_id', $promotion->id)->first();
        $applicableDays = $checkApplicableDays != null
            ? explode(',', $checkApplicableDays->days)
            : ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        $today = date('l');
        if (!in_array($today, $applicableDays)) {
            throw new ApiGenericException('The promocode is not valid for today.');
        }

        $currentTime = date('H:i:s');
        if (!is_null($checkApplicableDays) && !empty($checkApplicableDays)) {
            if ($currentTime < $checkApplicableDays->start_time || $currentTime > $checkApplicableDays->end_time) {
                throw new ApiGenericException('The promocode is not valid at this time.');
            }
        }
        return true;
    }

    public static function checkDayWiseUsageLimit($promotion, $usage_type, $totalUesedByUser, $userId, $promocode, $license_plate, $ticket_number)
    {
        $today = date('l');
        if (is_null($userId)) {
            $getUserDetail = Ticket::where('license_plate', $license_plate)->first();
            $userId = $getUserDetail->user_id;
        }

        if ($usage_type == 4) {
            // Fetch applicable days
            $checkApplicableDays = PromotionDay::where('promotion_id', $promotion->id)->first();
            $applicableDays = $checkApplicableDays
                ? explode(',', $checkApplicableDays->days)
                : ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

            // Check if today is within the applicable days
            if (!in_array($today, $applicableDays)) {
                throw new ApiGenericException('The promocode is not valid for today.');
            }

            // Fetch tickets for today
            $todayDate = Carbon::now()->toDateString();
            // $todayDate = '2024-12-30';

            $todayUsageCount = Ticket::where('user_id', $userId)
                ->where('promocode', $promocode)
                ->whereRaw("DATE(check_in_datetime) = ?", [$todayDate])
                ->count();

            // Check if the promo code has been used today
            if ($todayUsageCount >= $promotion->usage) {
                //discuss with vijay sir then extra check in case of extend ticket
                if (isset($ticket_number) && !empty($ticket_number)) {
                    return true;
                }
                throw new ApiGenericException('Maximum usage of this promocode has already been exceeded.');
            }

            // Allow usage
            return true;
        } else if ($usage_type == 3) {
            // Allow usage for unlimited
            return true;
        }

        return false;
    }


    public static function getPayableAndDiscountHoursFromLength($request, $promoResponse)
    {
        $dayCount = $lengthLeft = $fullDays = 0;
        if ($request->tillNowLength >= self::FULL_DAY_HOUR_VAL) {
            $dayCount       = ceil($request->tillNowLength / self::FULL_DAY_HOUR_VAL);

            if ($request->tillNowLength % self::FULL_DAY_HOUR_VAL === 0) {
                $lengthLeft     =  0;
                $fullDays       =  $dayCount;
            } else {
                $lengthLeft     =  $request->tillNowLength - (($dayCount - 1) * self::FULL_DAY_HOUR_VAL);
            }

            if ($lengthLeft > 0) {
                $fullDays = $dayCount - 1;
            }
            $payableHours = $fullDays * self::FULL_DAY_HOUR_VAL - ($fullDays * $promoResponse->discount_in_hours);
            $nextdayHours = ($fullDays * $promoResponse->discount_in_hours);
        } else {
        }

        $result['fullDays']         =  $fullDays;
        $result['lengthLeft']       =  $lengthLeft;
        $result['payableHours']     =  $payableHours;
        $result['discountHours']    =  $nextdayHours;

        return $result;
    }

    #pims-12580 05-02-2025

    public static function ungatedSmsCheckInCheckOut($facility)
    {
        // only send SMS for Reseponsive Form URL.
        // responsive url for Pave and Townsend
        $appUrl = env('CUSTOMERAPP_TOUCHLESS_APP_URL');
        $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("TRANSIENT_URL", $facility->owner_id);
        if ($dynamicReceiptUrl) {
            $appUrl = $dynamicReceiptUrl->value;
        }
        $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        $user = User::find($checkPaymentUrl->user_id);
        $innerSlug = '';
        if ($user->company_name != '') {
            $companyNameArray = explode(" ", $user->company_name);
            $companyName = strtolower($companyNameArray[0]);
            $innerSlug = $companyName;
        } else {
            $innerSlug = $slug;
        }
        $slug = isset($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : '';
        $resUrl = $appUrl . '/' . $slug . '/touchless-parking-' . $innerSlug;
        return $resUrl;
    }

    public static function sanitizePriceBreakUp($priceBreakUp)
    {
        // Ensure every value is non-negative
        foreach ($priceBreakUp as $key => $value) {
            if (is_numeric($value) && $value < 0) {
                $priceBreakUp[$key] = 0;
            }
        }

        return [
            'length'              => $priceBreakUp['length'] ?? 0,
            'total'               => $priceBreakUp['total'] ?? 0,
            'processing_fee'      => $priceBreakUp['processing_fee'] ?? 0,
            'tax_rate'            => $priceBreakUp['tax_rate'] ?? 0,
            'tax_fee'             => $priceBreakUp['tax_rate'] ?? 0, // Double-check if you meant tax_rate or tax_fee
            'parking_amount'      => $priceBreakUp['parking_amount'] ?? 0,
            'extend_amount'       => $priceBreakUp['extend_amount'] ?? 0,
            'paid_amount'         => $priceBreakUp['paid_amount'] ?? 0,
            'amount_paid'         => $priceBreakUp['amount_paid'] ?? 0,
            'payable_amount'      => $priceBreakUp['payable_amount'] ?? 0,
            'discount_amount'     => $priceBreakUp['discount_amount'] ?? 0,
            'is_price_applicable' => $priceBreakUp['is_price_applicable'] ?? 0,
            'additional_fee'      => $priceBreakUp['additional_fee'] ?? 0,
            'surcharge_fee'       => $priceBreakUp['surcharge_fee'] ?? 0
        ];
    }

    protected static function validDayOfTheWeek($rate)
    {
        $startDay = strtolower(date('l', strtotime(Carbon::parse('now'))));
        //$endDay = strtolower(date('l', strtotime($this->arrival_time) + $this->length_of_stay * 3600));
        return $rate->$startDay; //$rate->$endDay;

    }

    protected static function timestampToCarbonString(Carbon $initial, $time)
    {

        $times = explode(':', $time);

        $hour = (int) ($times[0] ?? 0);
        $minute = (int) ($times[1] ?? 0);

        // Clone initial time and set the given hour & minute
        $carbonTime = $initial->copy()->hour($hour)->minute($minute);

        // Check if time shifts to the next day
        $isNextDay = $carbonTime->day > $initial->day;

        // Format time as 12-hour AM/PM
        $formattedTime = $carbonTime->format('h:i A');

        // Append "Next Day" if applicable
        return $isNextDay ? "{$formattedTime} (Next Day)" : $formattedTime;
    }

    public static function formatRateOperationTime($rates)
    {
        $carbonNowTime = Carbon::parse('now');
        if ($rates->count() > 0) {

            $string = '';
            foreach ($rates as $key => $rate) {
                if (!self::validDayOfTheWeek($rate)) {    // to check Current day is applicale or not
                    continue; // Use return null instead of continue
                }
                $entryTime = self::timestampToCarbonString($carbonNowTime, $rate->entry_time_begin) . '-' . self::timestampToCarbonString($carbonNowTime, $rate->entry_time_end);
                $exitTime = self::timestampToCarbonString($carbonNowTime, $rate->exit_time_begin) . '-' . self::timestampToCarbonString($carbonNowTime, $rate->exit_time_end);
                $string .= "{$rate['description']} : \n\r Entry Time {$entryTime}, Exit Time {$exitTime} \n\r";
                // $string .= "Garage Entry Time {$entryTime}, Exit Time {$exitTime} \n";
            }
            return $string;
        }
        return false;
    }

    #DD pims-9183 bulk upload
    public static function validateAndFormatDate($dateString)
    {
        //$formats = ['F j, Y','F j,Y','F d, Y','F d,Y'];
        $formats = [
            'Y-m-d',
            'Y-m-d H:i:s',
            'Y-m-d H:i',
            'Y-m-d-H:i',
            'Y-m-d-H:i:s',
            'Y-m-dP',
            'Y-m-d\TH:iP',
        ];
        foreach ($formats as $format) {
            $d = DateTime::createFromFormat($format, $dateString);
            if ($d) {
                return $d->format('Y-m-d');
            }
        }

        return false;
    }


    public static function storeMasqueradeTrailingData($trailingData)
    {
        try {
            DB::table('masqurade_trailing_data')->insert([
                'masqurade_parent_id' => $trailingData['masqurade_parent_id'] ?? null,
                'event_type' => $trailingData['event_type'] ?? null,
                'event_id' => $trailingData['event_id'] ?? null,
                'event_action' => $trailingData['event_action'] ?? 'create',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
            return true;
        } catch (\Exception $e) {
            Log::error('Error storing Masquerade Trailing Data: ' . $e->getMessage());
            return false;
        }
    }

    // PIMS - 13805    
    public static function  partnerWiseCustomerPermissions($partnerId)
    {
        $customerPermissions =  CustomerPortalPermission::where(["partner_id" => $partnerId, 'type' => '3'])->orderBy('id', 'DESC')->get();
        if ($customerPermissions->count() > 0) {
            return $customerPermissions;
        }
        return false;
    }

    public static function getCustomMessage($slug, $facility_id, $partnerId = null)
    {
        $message = false;
        if (!empty($facility_id) && !empty($partnerId)) {
            $CustomText =  CustomText::where(["partner_id" => $partnerId, 'facility_id' => $facility_id, 'slug' => $slug])->first();
        } else if (!empty($facility_id)) {
            $CustomText =  CustomText::where(['facility_id' => $facility_id, 'slug' => $slug])->first();
        }

        if (!$CustomText) {
            $CustomText =  CustomText::where(["partner_id" => $partnerId, 'slug' => $slug])->first();
        }

        if (isset($CustomText->message)) {
            $message =  $CustomText->message;
        }
        return $message;
    }

    public static function getPartnerNameBySecret($secret)
    {
        if (!empty($secret)) {
            $oauthClient = OauthClient::where('secret', $secret)->first();
            if ($oauthClient) {
                $partner = User::find($oauthClient->partner_id);
                if ($partner) {
                    return self::slugify($partner->name);
                }
            }
        }
        return false;
    }
    //UPBL-87
     public static function updateFacilitySlot($ticket, $type, $deviceType)
    {
        $facilitySlot = FacilitySlot::where("facility_id", $ticket['facility']->id)->orderBy("id", "DESC")->first();
        $facility = Facility::with("facilityConfiguration")->find($ticket['facility']->id);
        $slot = new FacilitySlot();
        $lotFull = 0;
        if (!$facilitySlot) {
            \Log::info("test-----------------1" .  $ticket['facility']->id);
            $slot->facility_id = $ticket['facility']->id;
            $slot->partner_id = $ticket['gate']->partner_id;
            $slot->license_plate = $ticket['license_plate']['license_plate'];
            $slot->entry_type = $ticket['gate']->gate_type;
            $slot->entry_time = $ticket['license_plate']['event_created_at'];
            // $slot->device_type = $ticket->device_type;
            $slot->slot = 1;
            $slot->save();
        } else {
            \Log::info("test-----------------2" .  $ticket['facility']->id);
            $slot->facility_id =  $ticket['facility']->id;
            $slot->partner_id = $ticket['gate']->partner_id;
            $slot->license_plate = $ticket['license_plate']['license_plate'];
            $slot->entry_type = $ticket['gate']->gate_type;

            if ($type == "entry") {
                $slot->entry_time = $ticket['license_plate']['event_created_at'];
                //$slot->device_type = $ticket->device_type;
                $slot->slot = $facilitySlot->slot + 1;
            } else {
                \Log::info("test-----------------3" . $ticket['facility']->id);
                $slot->entry_time = $ticket['license_plate']['event_created_at'];
                // $slot->device_type = $deviceType;
                $slot->slot = $slot->slot = ($facilitySlot->slot == 0) ? 0 : $facilitySlot->slot - 1;
            }
            $slot->save();
        }

        if ($slot) {
            $availSlot = $slot->slot;
            if ($availSlot >= $facility->facilityConfiguration->threshold_onlevel) {
                //UPBL-87
                if($facility->facilityConfiguration->threshold_type == "0" || $facility->facilityConfiguration->threshold_type == 0){
                    $facility->facilityConfiguration->is_lot_full = '1';
                    $facility->facilityConfiguration->save();
                    $params = ['facility_id' => $ticket['facility']->id, 'is_full' => 'true'];
                    \Log::info("Request Parkengage SetLotStatus Service : SetLotStatus command about to run facility {$ticket['facility']->id} params" . json_encode($params));
                    $command_response = ParkengageGateApi::SetLotStatus($params, $facility->adam_host);
                    \Log::info("Response Parkengage SetLotStatus Service : SetLotStatus command complete facility {$ticket['facility']->id} response" . json_encode($command_response));
                }
                //UPBL-87
            } elseif(($availSlot < $facility->facilityConfiguration->threshold_onlevel) && ($availSlot > $facility->facilityConfiguration->threshold_offlevel)) {
                return true;
            }elseif ($availSlot < $facility->facilityConfiguration->threshold_offlevel) {
                //UPBL-87
                if($facility->facilityConfiguration->threshold_type == "0" || $facility->facilityConfiguration->threshold_type == 0){
                    $facility->facilityConfiguration->is_lot_full = '0';
                    $facility->facilityConfiguration->save();
                    $params = ['facility_id' => $ticket['facility']->id, 'is_full' => 'false'];
                    \Log::info("Request Parkengage SetLotStatus Service : SetLotStatus command about to run facility {$ticket['facility']->id} params" . json_encode($params));
                    $command_response = ParkengageGateApi::SetLotStatus($params, $facility->adam_host);
                    \Log::info("Response Parkengage SetLotStatus Service : SetLotStatus command complete facility {$ticket['facility']->id} response" . json_encode($command_response));
                }
                //UPBL-87
            }
        }
    }

    //UPBL-87

    public static function removeAtPrefixes($array, $is_header)
    {
        if ($is_header == 0) {
            foreach ($array as $key => $value) {
                $newKey = ltrim($key, '@');
                if (is_array($value)) {
                    $value = self::removeAtPrefixes($value, 0);
                }
                if ($newKey !== $key) {
                    unset($array[$key]);
                }
                $array[$newKey] = $value;
            }

            return $array;
        } else {
            $cleaned = [];
            foreach ($array as $key => $value) {
                $first = $value[0];
                $decoded = json_decode($first, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $cleaned[$key] = $decoded[0];
                } else {
                    $cleaned[$key] = $first;
                }
            }
            return $cleaned;
        }
    }

    public static function getSlotStatus($facility_id, $capcity, $threshold_onlevel, $threshold_offlevel)
    {
        $facilitySlot = FacilitySlot::where("facility_id", $facility_id)->orderBy("id", "DESC")->first();
       // $facility = Facility::with("facilityConfiguration")->find($facility_id);
        $slot = new FacilitySlot();
        $availSlot =  $facilitySlot->slot;
        if ($availSlot >= $threshold_onlevel) {
            return '1';
        } elseif ($availSlot < $threshold_offlevel) {
            return  '0';
        } else {
            return '2';
        }
    }

    //PIMS-14518
    public static function promoCodeCalculation($request, $secret, $result, $ticketPrice, $parkingAmount, $rate, $surchargeFee, $additionalFee, $tax_rate, $processingFee, $diff_in_hours, $partnerId, $facility, $is_add_discount = 0)
    {
        $logFactory = app(LoggerFactory::class); // or resolve it manually
        $log = $logFactory->setPath('logs/are/buy-monthlypermit')->createLogger('buy-monthly');
        $rate['max_stay'] =  $base_diff_in_hours = $diff_in_hours;
        $request->request->add(['length' => $diff_in_hours]);
        $request->request->add(['rate_band_id' => '']);
        $base_total = $total_amount = $ticketPrice;
        $effective_pro_rate = $parkingAmount;
        $base_tax_rate = $tax_rate;
        $discountAmount = $payableAmount = $oversize_fee = 0;
        $arrival_time     = date("Y-m-d 00:00:00", strtotime($result['start_date']));
        $permit_processing_fee =  $processingFee;
        $result['org_surcharge_fee'] = $result['surcharge_fee'] = number_format($surchargeFee, 2);
        $result['org_additional_fee'] = $result['additional_fee'] = number_format($additionalFee, 2);
        $result['tax_fee'] = number_format($tax_rate, 2);

        if (isset($request->promocode) && !empty($request->promocode)) {
            $promoTaxFee = $promoProcessingFee = 0;
            $request->request->add(['tax_amount'        => $tax_rate]);
            $request->request->add(['processing_fee'    => ($processingFee + $additionalFee + $surchargeFee)]);
            $request->request->add(['ticketLength'      => $diff_in_hours]);
            $request->request->add(['reference_key'     => '']);
            $request->request->add(['arrivalTime'       => $arrival_time]);
            $request->request->add(['rateHours'         => $diff_in_hours]);
            $request->request->add(['facility_id'       => $request->facility_id]);
            $request->request->add(['promocode'         => $request->promocode]);
            $request->request->add(['partner_id'        => $secret->partner_id]);
            $request->request->add(['amount'            => $rate['price']]);
            // $request->request->add(['client_id'         => $request->header('X-ClientSecret')]);
            //$this->validate($request, Promotion::$checkPromoValidationRulesThirdParty);
            //die;
            $response = LatestPromoCodeLib::validatePromoCodeThirdParty($request);
            //print_r($response->getData());
            $log->info('QueryBuilder->promoCodeCalculation : Rate : ' . json_encode($response->getData()));

            if (isset($response->getData()->is_promocode_valid) == '1') {
                // Hourly Promocode Implementation

                if ($response->getData()->discount_in_hours > 0) {
                    $log->info(' hours Promocode applied  ');
                    $appliedDiscountHours = 0;

                    if ($diff_in_hours > $response->getData()->discount_in_hours) {

                        if (isset($diff_in_hours) && !empty($diff_in_hours) && empty($request->rate_band_id)) {
                            $log->error("endparking:  hours Promocode applied if");
                            $resultdata = $response->getData();
                            $newrate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, '0', false, false, false, false);
                            if (isset($newrate['id']) && !empty($newrate['id'])) {
                                $request->request->add(['rate_id_hourly' => $newrate['id']]);
                            } else {
                                $request->request->add(['baseprice' => $newrate['price']]);
                            }
                            $request->request->add(['length_of_stay' => $diff_in_hours]);
                            $request->request->add(['arrival_time' => $arrival_time]);
                            $request->request->add(['facility_id' => $facility->id]);
                            self::getUpdatedPriceforHourlyPromoCode($response, $resultdata, $request);
                        } else {
                            if ($rate['max_stay'] >= self::FULL_DAY_HOUR_VAL) {
                                $log->error("endparking:  hours Promocode applied full day val");
                                //dd($rate['no_of_days'],$response->getData()->discount_in_hours);
                                if ($response->getData()->no_of_times == '1') {
                                    // 1=>for one times,0 =>for multiple times
                                    $no_of_days = 1;
                                } else {
                                    $no_of_days = $rate['max_stay'] / self::FULL_DAY_HOUR_VAL;
                                }
                                $request->request->add(['length' => ($diff_in_hours - $no_of_days * $response->getData()->discount_in_hours)]);
                            } else {
                                $log->error("endparking:  hours Promocode applied non full day");
                                $request->request->add(['length' => $diff_in_hours - ($response->getData()->discount_in_hours)]);
                                $newlenth = $request->length;
                                if ($facility->is_hourly_rate != '1' || $facility->is_hourly_rate != 1) {
                                    $isMember = 0; //added isMember = 0 due to error fix
                                    $newrate = $facility->rateForReservationOnMarker($arrival_time, $newlenth, false, false, false, true, false, '0', $isMember, false, false, $data['promo_category_id']);
                                    $rateforHours = $newrate;
                                }
                            }
                            $hourlyPromotionsPrice = self::calculateHourlyPromotions($request, $response->getData(), $rateforHours, $facility,  $serviceType = 'transient');
                        }

                        if (isset($diff_in_hours) && !empty($diff_in_hours) && empty($request->rate_band_id)) {
                            $payableAmount = $resultdata->payable_amount;
                            $discountAmount = $resultdata->discount_in_dollar;
                        } else {
                            $payableAmount = $hourlyPromotionsPrice;
                            $discountAmount = $request->amount - $hourlyPromotionsPrice;
                        }
                        $appliedDiscountHours = isset($no_of_days) && $no_of_days > 0 ?  $no_of_days * $response->getData()->discount_in_hours : $response->getData()->discount_in_hours;
                    } else {
                        $log->error("endparking:  hours Promocode applied zero price");
                        // Zero Price Applicable if length is more less than or equal 
                        $payableAmount = '0.00';
                        $discountAmount = $request->amount;
                        if ($diff_in_hours < $response->getData()->discount_in_hours) {
                            $appliedDiscountHours = $diff_in_hours;
                        } else {
                            $appliedDiscountHours = $response->getData()->discount_in_hours;
                        }
                    }

                    // Dev : Vijay : 03-01-2025
                    // New Implementaion on Tax Calculation again based on payable 
                    $discountRate['price']      = $payableAmount;
                    if (isset($request->baseprice) && !empty($request->baseprice)) {
                        $promoTaxFee        =   $tax_rate;
                        $promoProcessingFee =   $processingFee;
                    } else {
                        $promoTaxFee                = $payableAmount > 0 ? $facility->getTaxRate($discountRate) : $tax_rate;
                        $promoProcessingFee         = $payableAmount > 0 ? $facility->PromoProcesingFee('0', $request, $rate, $payableAmount, $response->getData()) : $processingFee;
                    }

                    // Dev : Vijay : 03-01-2025 Close
                    // dd($request['length'], $payableAmount, $promoTaxFee, $promoProcessingFee, $discountAmount);
                    if (isset($request->length) && !empty($request->length) && empty($request->rate_band_id)) {

                        if ($response->getData()->is_tax_applicable == '1') {
                            if (in_array($facility->owner_id, config('parkengage.PARTNER_GROUP_DISCOUNT')) && (!isset($request->is_mobile_origin) || $request->is_mobile_origin !== 'mobile')) {
                                if (!empty($response) &&  !empty($response->getData()) && $response->getData()->is_tax_applicable == '1') {
                                    if ($response->getData()->promoin != '1') {
                                        if ($parkingAmount > $discountAmount) {
                                            $rate['price'] = number_format(($parkingAmount - $discountAmount), 3);
                                            $data['new_price']  = number_format($rate['price'], 2);
                                            $rate['price'] = $rate['price'] + $surchargeFee;
                                            $newtax_rate = $facility->getTaxRate($rate, '0');
                                            $rate['price'] = $rate['price'] - $surchargeFee;
                                            $data['new_tax']  = number_format($newtax_rate, 2);

                                            $data['new_processingfee']  = number_format($processingFee, 2);
                                            if ($ticketPrice > $rate["price"]) {
                                                $ticketPrice = $rate["price"] + $processingFee + $additionalFee + $newtax_rate + $surchargeFee;
                                                if ($is_add_discount) {
                                                    $ticketPrice += $discountAmount;
                                                }
                                            } else if ($ticketPrice < $rate["price"]) {
                                                $ticketPrice = "0.00";
                                            }
                                        } else {
                                            $data['new_price']  = "0.00";
                                            $data['new_tax']  = "0.00";
                                            $data['new_processingfee']  = "0.00";
                                            $ticketPrice = "0.00";
                                        }
                                    } else {
                                        if ($parkingAmount > $response->getData()->maxvalue) {
                                            $rate['price'] = number_format(($parkingAmount - $discountAmount), 3);
                                            $data['new_price']  = $rate['price'];

                                            $rate['price'] = $rate['price'] + $surchargeFee;
                                            $newtax_rate = $facility->getTaxRate($rate, '0');
                                            $rate['price'] = $rate['price'] - $surchargeFee;
                                            $data['new_tax']  = $newtax_rate;
                                        } else {
                                            $rate['price'] = round((float) $parkingAmount - ($response->getData()->promovalue / 100) * (float) $parkingAmount, 3);
                                            $data['new_price'] = number_format($rate['price'], 2);

                                            $rate['price'] = $rate['price'] + $surchargeFee;
                                            $newtax_rate = $facility->getTaxRate($rate, '0');
                                            $rate['price'] = $rate['price'] - $surchargeFee;
                                            if ($rate['price'] > 0) {
                                                if ($facility->tax_rate_type != '0') {
                                                    $data['new_tax']  = $newtax_rate;
                                                } else {
                                                    $data['new_tax'] = number_format(
                                                        round((float) $tax_rate - ($response->getData()->promovalue / 100) * (float) $tax_rate, 3),
                                                        2
                                                    );
                                                }
                                            } else {
                                                $data['new_tax']  = "0.00";
                                            }
                                        }

                                        $data['new_processingfee'] = number_format(
                                            round((float) $processingFee - ($response->getData()->promovalue / 100) * (float) $processingFee, 3),
                                            2
                                        );

                                        if ($ticketPrice > $rate["price"]) {
                                            $ticketPrice = $data['new_price'] + $data['new_processingfee'] + $additionalFee + $data['new_tax'] + $surchargeFee;
                                            if ($is_add_discount) {
                                                $ticketPrice += $discountAmount;
                                            }
                                        } else if ($ticketPrice < $rate["price"]) {
                                            $ticketPrice = "0.00";
                                        }
                                    }
                                } else if (!empty($response) && !empty($response->getData())) {
                                    if ($response->getData()->promoin != '1') {
                                        $rate['price'] = number_format(($parkingAmount - $discountAmount), 3);
                                        $data['new_price']  = number_format($rate['price'], 2);

                                        $data['new_tax']  = number_format($tax_rate, 2);
                                        $data['new_processingfee']  = number_format($processingFee, 2);
                                        $ticketPrice = $data['new_price'] + $data['new_tax'] + $data['new_processingfee'];
                                        if ($is_add_discount) {
                                            $ticketPrice += $discountAmount;
                                        }
                                    } else {
                                        if ($parkingAmount > $response->getData()->maxvalue) {
                                            $data['new_price'] = number_format(($parkingAmount - $discountAmount), 3);
                                        } else {
                                            $data['new_price'] = number_format(($parkingAmount - ($response->getData()->promovalue / 100) * $parkingAmount), 3);
                                        }
                                        $data['new_tax'] = number_format($tax_rate, 2);
                                        $data['new_processingfee'] = number_format($processingFee, 2);

                                        $ticketPrice = $data['new_price'] + $data['new_tax'] + $data['new_processingfee'];
                                        if ($is_add_discount) {
                                            $ticketPrice += $discountAmount;
                                        }
                                    }
                                }
                            } else {
                                $discountAmount += ($tax_rate + $processingFee + $additionalFee + $surchargeFee);
                                $payableAmount  = $payableAmount;
                            }
                        } else {
                            if (in_array($facility->owner_id, config('parkengage.PARTNER_GROUP_DISCOUNT')) && (!isset($request->is_mobile_origin) || $request->is_mobile_origin !== 'mobile')) {
                                if (!empty($response) && !empty($response->getData())) {
                                    if ($response->getData()->promoin != '1') {
                                        //echo $parkingAmount." : ".$discountAmount." : ".$tax_rate." : ".$processingFee;echo "\n";
                                        $rate['price'] = number_format(($parkingAmount - $discountAmount), 3);
                                        $data['new_price']  = number_format($rate['price'], 2);

                                        $data['new_tax']  = number_format($tax_rate, 2);
                                        $data['new_processingfee']  = number_format($processingFee, 2);
                                        $ticketPrice = $data['new_price'] + $data['new_tax'] + $data['new_processingfee'];
                                        if ($is_add_discount) {
                                            $ticketPrice += $discountAmount;
                                        }
                                    } else {
                                        if ($parkingAmount > $response->getData()->maxvalue) {
                                            $data['new_price'] = number_format(($parkingAmount - $discountAmount), 3);
                                        } else {
                                            $data['new_price'] = number_format(($parkingAmount - ($response->getData()->promovalue / 100) * $parkingAmount), 3);
                                        }
                                        $data['new_tax'] = number_format($tax_rate, 2);
                                        $data['new_processingfee'] = number_format($processingFee, 2);

                                        $ticketPrice = $data['new_price'] + $data['new_tax'] + $data['new_processingfee'];
                                        if ($is_add_discount) {
                                            $ticketPrice += $discountAmount;
                                        }
                                    }
                                }
                            }
                            $discountAmount = $discountAmount;
                            $payableAmount  = $payableAmount + ($tax_rate + $processingFee + $additionalFee + $surchargeFee);
                        }
                    } else {
                        if ($response->getData()->is_tax_applicable == '1') {
                            $discountAmount = ($discountAmount + $promoTaxFee + $promoProcessingFee);
                            $payableAmount  = $payableAmount > 0 ? ($payableAmount + ($tax_rate - $promoTaxFee) + $promoProcessingFee) : $payableAmount;
                            if ((float)$payableAmount <= 0) {
                                $data['new_price'] = "0.00";
                                $data['new_tax'] = "0.00";
                                $data['new_processingfee'] = "0.00";
                            }
                        } else {
                            $payableAmount  += $tax_rate + $processingFee;
                            if ((float)$payableAmount <= 0) {
                                $data['new_price'] = "0.00";
                                $data['new_tax'] = "0.00";
                                $data['new_processingfee'] = "0.00";
                            }
                        }
                    }
                    //end hors changes
                    $data['payable_amount']         = sprintf("%.2f", $payableAmount);
                    // dd($payableAmount);
                    if (in_array($facility->owner_id, config('parkengage.PARTNER_GROUP_DISCOUNT')) && (!isset($request->is_mobile_origin) || $request->is_mobile_origin !== 'mobile')) {
                        if (!empty($response) && !empty($response->getData()) && !empty($data['new_price']) && !empty($data['new_tax']) && !empty($data['new_processingfee'])) {
                            $data['payable_amount']         = sprintf("%.2f", ($data['new_price'] + $data['new_tax'] + $data['new_processingfee']));
                        } else {
                            $data['payable_amount']         = sprintf("%.2f", $payableAmount);
                        }
                    } else {
                        $data['payable_amount']         = sprintf("%.2f", $payableAmount);
                    }

                    if (in_array($partnerId, config('parkengage.PARTNER_GROUP_DISCOUNT')) && (!isset($request->is_mobile_origin) || $request->is_mobile_origin !== 'mobile') && !empty($response) && !empty($response->getData())) {
                        if (!empty($discountAmount) && $discountAmount  == ($parkingAmount + $processingFee + $tax_rate)) {
                            $data['new_discount_amount'] = $parkingAmount;
                        } else if (!empty($data['new_price'])) {
                            $data['new_discount_amount'] = number_format(($parkingAmount - $data['new_price']), 2);;
                        }
                    }

                    $data['parking_amount']         = $parkingAmount;
                    $data['processing_fee']         = $processingFee;
                    $data['tax_rate']               = $tax_rate;
                    $data['CCFee']                  = $additionalFee;    // Set Dynamic Name 
                    $data['surcharge_fee']          = $surchargeFee;    // Set Dynamic Name
                    $data['facility']               = $facility;
                    $data['valid_till']             = date("Y-m-d H:i:s", strtotime($from));


                    $data['promocode_discount']             = $response->getData();
                    $data['promocode_discount_amount']      = sprintf("%.2f", $discountAmount);
                    $data['discount_amount']                = sprintf("%.2f", $discountAmount);

                    $data['day_wise_promocode_used']      = 0;
                    $promousedTimes = self::getDayWisePromocodeUsed($request->rateHours);
                    if ($promousedTimes) {
                        $data['day_wise_promocode_used']      = $promousedTimes;
                    }

                    if ($facility->tax_with_surcharge_enable == '1') {
                        if (in_array($partnerId, config('parkengage.PARTNER_GROUP_DISCOUNT')) && isset($data['new_price']) && (!empty($response) &&  !empty($response->getData()) && $response->getData()->is_tax_applicable == '1')) {
                            //dd('as');
                            $rate['price'] =  $data['new_price'];
                            $data['surcharge_fee'] =  $facility->getSurchargeFee($rate);
                            $data['CCFee'] = $facility->getAdditionalFee($rate);
                            $rate['price'] =  $data['new_price'] + $data['surcharge_fee'];
                            $data['tax_rate'] =  $facility->getTaxRate($rate, '0');
                            $data['payable_amount'] = $data['payable_amount'] - ($additionalFee + $surchargeFee);

                            $data['payable_amount'] = $data['CCFee'] + $data['tax_rate'] + $data['surcharge_fee'] + $data['new_processingfee'] + $data['new_price'];
                            $data['processing_fee']            = $data['new_processingfee'];
                            //removed for FE Requriement all new Key
                            $data['discount_amount']           = $data['new_discount_amount'];
                            unset($data['new_tax'], $data['new_processingfee'], $data['new_discount_amount'], $data['new_discount_amount']);
                        } else {
                            $rate['price'] =  $parkingAmount + $surchargeFee;
                            if (!empty($data['payable_amount']) && $data['payable_amount'] > 0) {
                                $data['payable_amount'] = $data['payable_amount'] - $tax_rate;
                                $data['tax_rate'] =  $facility->getTaxRate($rate, '0');
                                $data['payable_amount'] = $data['payable_amount'] + $data['tax_rate'];
                                if (isset($data['new_price'])) {
                                    $data['payable_amount'] = $data['new_price'] + $data['tax_rate'] + $data['surcharge_fee'] + $data['CCFee'] + $data['processing_fee'];
                                }
                                if (isset($data['new_discount_amount'])) {
                                    $data['discount_amount']     = $data['new_discount_amount'];
                                }
                                unset($data['new_tax'], $data['new_discount_amount']);
                            }
                        }
                        $data['is_price_applicable'] = 1;
                    }
                }
                // Percentage or Others promocode implementation 
                else {
                    if ($response->getData()->is_tax_applicable == '1') {

                        //$tax_rate = $facility->getTaxRate($rate, '0');
                        $rate['price'] = $rate['price'] + $surchargeFee;
                        $tax_rate = $facility->getTaxRate($rate, '0');
                        $rate['price'] = $rate['price'] - $surchargeFee;
                        //$processingFee = $facility->getProcessingFee($rate, '0');
                        $permit_processing_fee =  $processingFee = isset($facility->permit_processing_fee) ? $facility->permit_processing_fee : '0.00';
                        if ($ticketPrice > $rate["price"]) {
                            $ticketPrice = $rate["price"] + $processingFee + $additionalFee + $tax_rate + $surchargeFee;
                        } else if ($ticketPrice < $rate["price"]) {
                            $ticketPrice = "0.00";
                        }
                        $log->info('Percentage promocode is_tax_applicable price + pe + af + tr + sf: ' . $rate["price"] . '+' . $processingFee  . '+' . $additionalFee  . '+' . $tax_rate  . '+' . $surchargeFee);
                        //ask by sunil you can check and put new condition
                        if (($base_total - $ticketPrice) > 0) {

                            $data['discount_amount']            = number_format(($base_total - $ticketPrice), 2);
                        } else {
                            $data['discount_amount']            = number_format($response->getData()->discount_in_dollar, 2);
                        }
                        $log->info('Percentage promocode is_tax_applicable discount_amount: ' . $data['discount_amount']);
                    } else {
                        if ($ticketPrice > $rate["price"]) {
                            $ticketPrice = $rate["price"] + $processingFee + $additionalFee + $tax_rate + $surchargeFee;
                        } else if ($ticketPrice < $rate["price"]) {
                            $ticketPrice = "0.00";
                        }
                        $data['discount_amount']            = number_format(($parkingAmount - $rate["price"]), 2);
                        $log->info('Percentage promocode non_is_tax_applicable price + pe + af + tr + sf: ' . $rate["price"] . '+' . $processingFee  . '+' . $additionalFee  . '+' . $tax_rate  . '+' . $surchargeFee);
                        $log->info('Percentage promocode non_is_tax_applicable discount_amount: ' . $data['discount_amount']);
                    }

                    $log->info('endparking: Value Promocode applied ');
                    if ($parkingAmount < $response->getData()->discount_in_dollar) {
                        $dataResponse = $response->getData();
                        $data['promocode_discount'] = $dataResponse;
                        $data['promocode_discount_amount'] = number_format($response->getData()->discount_in_dollar, 2);
                        $updated_price = $parkingAmount;
                        $log->info('Percentage promocode parkingAmount + updated_price:if: ' . $parkingAmount  . '-' . $updated_price);
                    } else {
                        $new_pro_rate = ($parkingAmount) - ($response->getData()->discount_in_dollar);
                        $updated_price = $new_pro_rate > 0 ? $new_pro_rate : '0.00';
                        $dataResponse = $response->getData();
                        $data['promocode_discount'] = $dataResponse;
                        $data['promocode_discount_amount'] = number_format($response->getData()->discount_in_dollar, 2);
                        $log->info('Percentage promocode parkingAmount + updated_price:else: ' . $parkingAmount  . '-' . $updated_price);
                    }
                    if (!$is_add_discount) {
                        /* $data['promocode_discount_amount'] =$response->getData()->discount_in_dollar + $tax_rate + $processingFee+$additionalFee+$surchargeFee ;
                        $data['promocode_discount_amount'] =$response->getData()->discount_in_dollar + $tax_rate ;
                        $data['promocode_discount_amount'] = number_format($data['promocode_discount_amount'], 2); */
                        $data['promocode_discount_amount'] = number_format($response->getData()->discount_in_dollar, 2);
                    } else {
                        $data['promocode_discount_amount'] = number_format($response->getData()->discount_in_dollar, 2);
                    }

                    if (($base_total - $ticketPrice) > 0) {
                        $data['discount_amount']            = number_format(($parkingAmount - $updated_price), 2);
                    } else {
                        $data['discount_amount']            = $data['discount_amount'];
                    }
                    $log->info('Percentage promocode discount_amount: ' . $data['discount_amount']);
                }
            }
        }
        $discountAmount             = isset($data['promocode_discount_amount']) ? $data['promocode_discount_amount'] : '0';
        $data['parking_amount']     = $parkingAmount;
        $data['price']              = self::currencyFormat($parkingAmount + $oversize_fee);
        $data['processing_fee']     = $processingFee;
        $data['tax_rate']           = $tax_rate;
        $data['extend_amount']      = '0.00';
        $data['paid_amount']        = '0.00';
        $data['total']              = self::currencyFormat($base_total);
        $data['CCFee']              = $additionalFee;    // Set Dynamic Name 
        $data['surcharge_fee']      = $surchargeFee;    // Set Dynamic Name 
        //echo "Price: ".$rate['price']." :prorated_amount: ".$prorated_amount." :new_price: ".$data['new_price']." :parking_amount: ".$parkingAmount  ." :ticketPrice : ". $ticketPrice  ." :discountAmount : ". $discountAmount ." :promocode_discount_amount : ". $data['promocode_discount_amount']  ." :Oversize : ". $oversize_fee ." :ProcessingFee : ". $processingFee ." :AddFee: ". $additionalFee ." :TaxRate: ". $tax_rate ." :Surcharge: ". $surchargeFee;echo "\n";

        if (isset($request->promocode) && !empty($request->promocode)) {
            if (in_array($partnerId, config('parkengage.PARTNER_GROUP_DISCOUNT')) && (!isset($request->is_mobile_origin) || $request->is_mobile_origin !== 'mobile')) {
                if (!empty($response) &&  !empty($response->getData()) && $response->getData()->is_tax_applicable == '1') {
                    if ($response->getData()->promoin != '1') {
                        if ($parkingAmount > $discountAmount) {
                            $log->info('Log: ' . __LINE__ . ' check: ' . $parkingAmount . ' :: ' . $discountAmount);
                            $rate['price'] = number_format(($parkingAmount - $discountAmount), 3);
                            $data['new_price']  = number_format($rate['price'], 2);

                            $rate['price'] = $rate['price'] + $surchargeFee;
                            $newtax_rate = $facility->getTaxRate($rate, '0');
                            $rate['price'] = $rate['price'] - $surchargeFee;
                            $data['new_tax']  = number_format($newtax_rate, 2);
                            $additionalFee      = $facility->getAdditionalFee($rate);     // Addition Fee Introduce 
                            $surchargeFee       = $facility->getSurchargeFee($rate);

                            if ($data['new_price'] <= 0) {
                                $processingFee = "0.00";
                            }
                            $data['new_processingfee']  = number_format($processingFee, 2);
                            if ($ticketPrice > $rate["price"]) {
                                $ticketPrice = $rate["price"] + $processingFee + $additionalFee + $newtax_rate + $surchargeFee;
                                if ($is_add_discount) {
                                    $ticketPrice += $discountAmount;
                                }
                            } else if ($ticketPrice < $rate["price"]) {
                                $ticketPrice = "0.00";
                            }
                            $log->info('Log: ' . __LINE__ . 'Percentage promocode is_tax_applicable promoin price + pe + af + tr + sf: ' . $parkingAmount . '-' . $discountAmount . '+' . $processingFee . '+' . $additionalFee  . '+' . $newtax_rate  . '+' . $surchargeFee);
                        } else {
                            $data['new_price']  = "0.00";
                            $data['new_tax']  = "0.00";
                            $data['new_processingfee']  = "0.00";
                            $ticketPrice = "0.00";
                        }
                    } else {
                        if ($parkingAmount > $response->getData()->maxvalue) {
                            $rate['price'] = number_format(($parkingAmount - $discountAmount), 3);
                            $data['new_price']  = $rate['price'];
                            $rate['price'] = $rate['price'] + $surchargeFee;
                            $newtax_rate = $facility->getTaxRate($rate, '0');
                            $rate['price'] = $rate['price'] - $surchargeFee;
                            $data['new_tax']  = $newtax_rate;

                            // $data['new_processingfee']  = $processingFee;
                        } else {
                            $rate['price'] = round((float) $parkingAmount - ($response->getData()->promovalue / 100) * (float) $parkingAmount, 3);
                            $log->info('Percentage promocode is_tax_applicable promoin 1 parkingAmount + promovalue: ' . $rate['price'] . " : " . $parkingAmount);
                            $data['new_price'] = number_format($rate['price'], 2);

                            $rate['price'] = $rate['price'] + $surchargeFee;
                            $newtax_rate = $facility->getTaxRate($rate, '0');
                            $rate['price'] = $rate['price'] - $surchargeFee;
                            if ($rate['price'] > 0) {
                                if ($facility->tax_rate_type != '0') {
                                    $data['new_tax']  = $newtax_rate;
                                } else {
                                    $data['new_tax'] = number_format(
                                        round((float) $tax_rate - ($response->getData()->promovalue / 100) * (float) $tax_rate, 3),
                                        2
                                    );
                                }
                            } else {
                                $data['new_tax']  = "0.00";
                            }
                        }

                        if ($data['new_price'] > 0) {

                            $data['new_processingfee'] = number_format(
                                round((float) $processingFee - ($response->getData()->promovalue / 100) * (float) $processingFee, 3),
                                2
                            );
                        } else {
                            $data['new_processingfee'] = "0.00";
                        }
                        $log->info('Percentage promocode is_tax_applicable promoin 1 new_tax + new_processingfee + new_price + af + sf: ' . $data['new_tax'] . "+" . $data['new_processingfee'] . "+" . $data['new_price'] . "+" . $additionalFee . "+" . $surchargeFee);

                        if ($ticketPrice > $rate["price"]) {
                            $additionalFee      = $facility->getAdditionalFee($rate);     // Addition Fee Introduce 
                            $surchargeFee       = $facility->getSurchargeFee($rate);
                            $log->info('Percentage promocode is_tax_applicable promoin 1 new_tax + new_processingfee + new_price + new af + new sf: ' . $data['new_tax'] . "+" . $data['new_processingfee'] . "+" . $data['new_price'] . "+" . $additionalFee . "+" . $surchargeFee);

                            $ticketPrice = $data['new_price'] + $data['new_processingfee'] + $additionalFee + $data['new_tax'] + $surchargeFee;
                            if ($is_add_discount) {
                                $ticketPrice += $discountAmount;
                            }
                        } else if ($ticketPrice < $rate["price"]) {
                            $ticketPrice = "0.00";
                        }
                    }
                } else if (!empty($response) && !empty($response->getData())) {
                    if ($response->getData()->promoin != '1') {
                        $rate['price'] = number_format(($parkingAmount - $discountAmount), 3);
                        $data['new_price']  = number_format($rate['price'], 2);

                        $data['new_tax']  = number_format($tax_rate, 2);
                        $data['new_processingfee']  = number_format($processingFee, 2);
                        $ticketPrice = $data['new_price'] + $data['new_processingfee'] + $additionalFee + $data['new_tax'] + $surchargeFee;
                        if ($is_add_discount) {
                            $ticketPrice += $discountAmount;
                        }
                    } else {
                        if ($parkingAmount > $response->getData()->maxvalue) {
                            $data['new_price'] = number_format(($parkingAmount - $discountAmount), 3);
                        } else {
                            $data['new_price'] = number_format(($parkingAmount - ($response->getData()->promovalue / 100) * $parkingAmount), 3);
                        }
                        // $data['new_price'] = number_format(($parkingAmount - ($response->getData()->promovalue / 100) * $parkingAmount), 3);
                        $data['new_tax'] = number_format($tax_rate, 2);
                        $data['new_processingfee'] = number_format($processingFee, 2);
                        $ticketPrice = $data['new_price'] + $data['new_processingfee'] + $additionalFee + $data['new_tax'] + $surchargeFee;
                        if ($is_add_discount) {
                            $ticketPrice += $discountAmount;
                        }
                    }
                }
            }
            $result['promocode_discount'] = $response->getData();
            $result['promocode_discount_amount'] = sprintf("%.2f", $discountAmount);
        }

        if (isset($data['new_processingfee'])) {
            $result['processing_fee'] = $result['new_processing_fee'] = $data['new_processingfee'];
        }
        if (isset($data['new_tax'])) {
            $result['tax_fee'] = $result['new_tax_fee']  = $data['new_tax'];
        }
        $result['surcharge_fee'] = $result['new_surcharge_fee'] = number_format($surchargeFee, 2);
        $result['additional_fee'] = $result['new_additional_fee'] = number_format($additionalFee, 2);

        /*  
        if(isset($result['promocode_discount_amount'])){
            if(isset($data['new_processingfee']) && $data['new_processingfee']>0){
                $processing_fee = $result['processing_fee'] - $data['new_processingfee'];
                $processing_fee = ($processing_fee >= 0) ? $processing_fee : $data['new_processingfee'];
            }else{
                $processing_fee = $result['processing_fee'];
            }
            
            if(isset($data['new_tax']) && $data['new_tax']>0){
                $new_tax = $result['tax_fee'] - $data['new_tax'];
                $new_tax = ($new_tax >= 0) ? $new_tax : $data['new_tax'];
            }else{
                $new_tax = $result['tax_fee'];
            }
            if($additionalFee<=0){
                $additionalFee  = $result['org_additional_fee'];
            }else{
                $additionalFee = $result['org_additional_fee'] - $additionalFee;
                $additionalFee = ($additionalFee >= 0) ? $additionalFee : $result['org_additional_fee'];
            }
            if($surchargeFee<=0){
                $surchargeFee  = $result['org_surcharge_fee'];
            }else{
                $surchargeFee = $result['org_surcharge_fee'] - $surchargeFee;
                $surchargeFee = ($surchargeFee >= 0) ? $surchargeFee : $result['org_surcharge_fee'];
            }
            //echo $processing_fee . " : ".$new_tax . " : ".$additionalFee . " : ".$surchargeFee;
            $discount = $discountAmount + $processing_fee+$additionalFee+$surchargeFee+$new_tax;
            $result['promocode_discount_amount'] =number_format($discount, 2) ;
        }
        */

        if (isset($data['new_price'])) {
            $parkingAmount = $data['new_price'] - $surchargeFee;
        }
        if (isset($discountAmount)) {
            $parkingAmount = $effective_pro_rate - $discountAmount;
        }
        if ($parkingAmount <= 0) {
            $parkingAmount = $result['additional_fee'] = $result['surcharge_fee'] = 0;
        }
        return [
            'result' => $result,
            'ticketPrice' => $ticketPrice,
            'netParkingAmount' => number_format($parkingAmount, 2)
        ];
    }

    public static function partnerProRateCalculation($request, $flag = 0) //flag 1 means permit create via admin, flag 2 means renew 
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $partner_id = $secret->partner_id;
        } elseif ($flag == '1') {
            if (Auth::user()->user_type == '4') {
                $partner_id = Auth::user()->created_by;
            } else if (Auth::user()->user_type == '12') {
                $partner_id = Auth::user()->created_by;
            } else if (Auth::user()->user_type == '1') {
                $partner_id = $request->partner_id;
            } else if (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            } else {
                throw new ApiGenericException('Invalid Partner. Please contact Admin');
            }
            $secret = OauthClient::where('partner_id', $partner_id)->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $is_resident_user = '';
            $user_permit_remaning = '';
            if (Auth::user()->user_type == '4') {
                $user_permit_remaning = UserPermitTypeMapping::where('user_id', Auth::user()->id)->get();
            }
        } else if ($flag == '2') {
            $partner_id = $request->partner_id;
            $secret = OauthClient::where('partner_id', $partner_id)->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        } else {
            throw new NotFoundException('No partner found.');
        }
        $request->request->add(['client_id' => $secret->secret]);

        $facility = Facility::with(['facilityConfiguration'])->select('id', 'short_name', 'license_format', 'license_min_lenght', 'license_max_lenght', 'fix_length', 'permit_processing_fee', 'additonal_fee', 'additonal_fee_type', 'surcharge_fee', 'surcharge_fee_type', 'tax_rate_type', 'tax_rate')->where('id', $request->facility_id)->first();

        $permit_service_amount = 0;

        if ($request->permit_service != '') {
            foreach ($request->permit_service as $permit_services) {
                $permit_service_amount += $permit_services['permit_service_rate'];
                $individualPermitAmount[] = [
                    'permit_service_rate' => $permit_services['permit_service_rate'],
                    'id' => $permit_services['id'],
                    'permit_service_name' => $permit_services['permit_service_name'],
                    'permit_service_criteria_mappings' => $permit_services['permit_service_criteria_mappings']
                ];
            }
        } else if ($request->permit_services != '') {
            $permitServices     = PermitServices::whereIn('id', $request->permit_services)->get();
            foreach ($permitServices as $permit_services) {
                $permit_service_amount += $permit_services['permit_service_rate'];
                $individualPermitAmount[] = [
                    'permit_service_rate' => $permit_services['permit_service_rate'],
                    'id' => $permit_services['id'],
                    'permit_service_name' => $permit_services['permit_service_name'],
                    'permit_service_criteria_mappings' => $permit_services['permit_service_criteria_mappings']
                ];
            }
        } elseif ($request->permitService != '') {
            foreach (json_decode($request->permitService) as $permit_services) {
                $permit_service_amount += $permit_services->permit_service_rate;
                // $individualPermitAmount[] = $permit_services->permit_service_rate;
                $individualPermitAmount[] = [
                    'permit_service_rate' => $permit_services->permit_service_rate,
                    'id' => $permit_services->id,
                    'permit_service_name' => $permit_services->permit_service_name,
                    'permit_service_criteria_mappings' => $permit_services->permit_service_criteria_mappings
                ];
            }
        }

        $is_resident_user = '';
        $result = [];

        if (isset($request->business_id) && !empty($request->business_id)) {
            $user_id = $request->business_id;
            $user_permit_remaning = UserPermitTypeMapping::where('user_id', $request->business_id)->where('permit_type_id', $request->permit_rate_id)->get();
            if ($user_permit_remaning != null && count($user_permit_remaning) > 0) {
                $result['permit_negotiable_price'] = $user_permit_remaning[0]['permit_negotiable_price'];
            } else {
                $result['permit_negotiable_price'] = NULL;
            }
        }

        if ($request->driving_license != '') {

            $mystring = $request->driving_license;
            $mystring = trim($mystring); //removes any spaces from beginning of the string
            $number = substr($mystring, 1);
            if (strlen($mystring) < 7) {
                throw new ApiGenericException('Invalid Driving License');
            } else if (strlen($mystring) > 13) {
                throw new ApiGenericException('Invalid Driving License');
            } else if ($mystring[0] == 'H' && is_numeric($number) && strlen($mystring) == 9) {
                $is_resident_user = 'Pass';
            } else {
                $is_resident_user = 'Fail';
            }
        } else {
            $is_resident_user = 'Fail';
        }

        $month = date("m");
        $maxDays = date('t');
        $maxDays = isset($request->desired_start_date) ? date('t', strtotime($request->desired_start_date)) : $maxDays;     #pims-12512
        //$maxDays = cal_days_in_month(CAL_GREGORIAN, 12, 2022);
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);
        $to_date = isset($request->desired_start_date) ? date("Y-m-t", strtotime($request->desired_start_date)) : $to_date; #pims-12512

        $today = date("Y-m-d");
        $today = isset($request->desired_start_date) ? date("Y-m-d", strtotime($request->desired_start_date)) : $today; #pims-12512
        $start_date = date("m-d-Y");
        $end_date = date("m-t-Y", $midDateString);
        /* $diff = abs(strtotime($to_date) - strtotime($today));

        $years = floor($diff / (365 * 60 * 60 * 24));
        $months = floor(($diff - $years * 365 * 60 * 60 * 24) / (30 * 60 * 60 * 24));
        $days = floor(($diff) / (60 * 60 * 24)); */
        $today = new DateTime($today);
        $to_date = new DateTime($to_date);
        $interval = $today->diff($to_date);
        $days = $interval->days;

        $result['start_date'] = isset($request->desired_start_date) ? date("m-d-Y", strtotime($request->desired_start_date)) : $start_date;
        $result['end_date'] = $end_date;


        $tax_rate    = $additionalFee =    $surchargeFee = 0; #PIMS-14518 

        $rate = PermitRate::with(['facility' => function ($query) use ($request) {
            $query->where('id', $request->facility_id);
        }])->where('id', $request->permit_rate_id)
            ->where('facility_id', $request->facility_id)
            ->get();

        if (count($rate) > 0) {
            // change for USM Dynamic Rate PIMS-10379 -ujjwal				
            $permit_tenure = self::getPermitTenure($rate[0]->id);

            if ($permit_tenure) {
                $rate[0]->rate = $permit_tenure->rate;
                $rate[0]->permit_tenure = $permit_tenure;
            }
            $permitRateDescriptionService = app(PermitRateDescriptionService::class);
            list($desiredEndDate, $no_of_days) = $permitRateDescriptionService->getDesiredEndDate($rate[0]->rateDescription, Carbon::createFromFormat('m-d-Y', $result['start_date'])->format('Y-m-d'),'m-d-Y');
            $result['end_date'] = $desiredEndDate;
            
            #dushyant 21/06/2024 hardcore date for usm
            if (in_array($secret->partner_id, config('parkengage.PARTNER_GROUP_SERVICES'))) { #PIMS-12258 #DD
                $permitRateDescHour = PermitRateDescription::find($rate[0]['permit_rate_description_id']);

                if (isset($permitRateDescHour->permit_start_date) && ($permitRateDescHour->permit_start_date > $today->format('Y-m-d'))) {
                    $result['start_date'] = date("m-d-Y", strtotime($permitRateDescHour->permit_start_date));
                }

                if (isset($permitRateDescHour->permit_end_date) && !empty($permitRateDescHour->permit_end_date)) {
                    $result['end_date'] = date("m-d-Y", strtotime($permitRateDescHour->permit_end_date));
                }
                

                if (isset($permitRateDescHour) && (strtolower($permitRateDescHour->hours_description) == "yearly")) {
                    $maxDays = date("L") ? 366 : 365; // Check if it's a leap year	
                    $today = Carbon::today();
                    $today = Carbon::createFromFormat('m-d-Y', $result['start_date']);
                    // $futureDate = Carbon::create(2026, 6, 30);
                    $futureDate = Carbon::createFromFormat('m-d-Y', $desiredEndDate); //upbl-163
                    $days = $today->diffInDays($futureDate);
                }
                if (isset($permit_tenure)) {

                    $today = Carbon::today();
                    $permit_end_day = $permit_tenure->permit_end_day;
                    $permit_end_month = $permit_tenure->permit_end_month;
                    $permit_end_year = $permit_tenure->permit_end_year;

                    $permit_start_day = $permit_tenure->permit_start_day;
                    $permit_start_month = $permit_tenure->permit_start_month;
                    $permit_start_year = $permit_tenure->permit_start_year;

                    $permitStartDate = Carbon::create($permit_start_year, $permit_start_month, $permit_start_day);
                    $futureDate = Carbon::create($permit_end_year, $permit_end_month, $permit_end_day);
                    $days = $today->diffInDays($futureDate);
                    $maxDays = $permitStartDate->diffInDays($futureDate);

                    $result['end_date'] = date("m-d-Y", strtotime($futureDate));
                }
                // Alka, Weekly Permit
                if (isset($permitRateDescHour) && ($permitRateDescHour->hours_description) == "Weekly") {
                    if (isset($request->desired_start_date)) {
                        // Get the current date and subtract 7 days

                        $startDate = Carbon::parse($request->desired_start_date)->startOfDay();
                        $endDate = $startDate->copy()->addDays(6)->endOfDay();

                        $result['start_date'] = $startDate->format('m-d-Y'); // MM-DD-YYYY format
                        $result['end_date'] = $endDate->format('m-d-Y');     // MM-DD-YYYY format
                    } else {
                        // Get the current date and subtract 7 days
                        $dateSevenDaysAgo = Carbon::now()->subDays(7);

                        // Get the start date (7 days ago) and the end date (1 day before today)
                        $startDate = $dateSevenDaysAgo->copy()->startOfDay(); // Start of the day 7 days ago
                        $endDate = Carbon::now()->subDay()->endOfDay();       // End of the day 1 day ago (yesterday)

                        $permit_start_date = $startDate->toDateString();
                        $permit_end_date = $endDate->toDateString();

                        $endDate = new DateTime($permit_end_date);

                        // Add one day to the end date
                        $desiredStartDate = $endDate->modify('+1 day');
                        $result['start_date'] = $desiredStartDate->format('m-d-Y');

                        $desiredEndDate = $desiredStartDate->modify('+6 day');
                        $result['end_date'] = $desiredEndDate->format('m-d-Y');
                    }
                }
            }

            #end dushyant 21/06/2024 hardcore date for usm

            $current_monthly_rate = $rate[0]['rate'];
            $facility_fee_result = FacilityFee::select('name', 'val')->where('facility_id', $request->facility_id)->where('name', 'permit_processing_fee')->where('active', 1)->first();

            $permit_final_amount = $current_monthly_rate + $permit_service_amount;
            $permit_processing_fee = $processingFee = isset($facility->permit_processing_fee) ? $facility->permit_processing_fee : '0.00';
            if ($permit_final_amount == '0.00') {
                $permit_processing_fee = "0.00";
            }

            $per_day_rate = $permit_final_amount / $maxDays;
            //$per_day_rate = number_format($per_day_rate, 2);


            if ($days >= 1) {
                $days++;
            } else if ($days == 0) {
                $days = '1';
            }
            $result['permit_rate_prorate'] =  '';
            $result['individual_permit_services'] = [];
            #pims-13210 dd
            $permitProRate = $current_monthly_rate / $maxDays;
            $permitEffectiveProRate = $days * $permitProRate;
            if (in_array($request->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_DESCRIPTION_ID'))) {
                $permit_effective_prorate = number_format($permitEffectiveProRate, 2);
            } else {
                $permit_effective_prorate = number_format($permitEffectiveProRate, 2, ".", "");
            }
            $result['permit_rate_prorate'] =  $permit_effective_prorate;

            // Individual pro rate calculation
            if (isset($individualPermitAmount) && !empty($individualPermitAmount)) {
                foreach ($individualPermitAmount as $permitAmountData) {

                    $permit_final_amount = $permitAmountData['permit_service_rate'];
                    $individualPerDayRate = $permit_final_amount / $maxDays;

                    $individualEffectiveProRate = $days * $individualPerDayRate;

                    if (in_array($request->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_DESCRIPTION_ID'))) {
                        $individual_effective_pro_rate = number_format($individualEffectiveProRate, 2);
                    } else {
                        $individual_effective_pro_rate = number_format($individualEffectiveProRate, 2, ".", "");
                    }

                    if ($facility->facilityConfiguration->is_prorate_apply == '1') {
                        $result['individual_permit_services'][] = [
                            'service_id' => $permitAmountData['id'],
                            'permit_service_name' => $permitAmountData['permit_service_name'] ?? NULL,
                            'permit_service_criteria_mappings' => $permitAmountData['permit_service_criteria_mappings'] ?? NULL,
                            'individual_per_day_rate' => number_format($individualPerDayRate, 2),
                            'individual_effective_pro_rate' => $individual_effective_pro_rate
                        ];
                    } else {
                        $result['individual_permit_services'][] = [
                            'service_id' => $permitAmountData['id'],
                            'permit_service_name' => $permitAmountData['permit_service_name'] ?? NULL,
                            'permit_service_criteria_mappings' => $permitAmountData['permit_service_criteria_mappings'] ?? NULL,
                            'individual_per_day_rate' => number_format($individualPerDayRate, 2),
                            'individual_effective_pro_rate' => $permitAmountData['permit_service_rate']
                        ];
                    }
                }
            }

            $effective_pro_rate = $days * $per_day_rate;
            //$effective_pro_rate = round($days * $per_day_rate);

            if (in_array($request->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_DESCRIPTION_ID'))) {
                /*** add  permit taxes pims-14518  upbl-163 */
                $rate['price']         = $parkingAmount = $rate[0]['rate'];
                $tax_rate              = $facility->getTaxRate($rate, '0');
                $additionalFee         = $facility->getAdditionalFee($rate);
                $surchargeFee          = $facility->getSurchargeFee($rate);

                if ($facility->tax_with_surcharge_enable == '1') {
                    $rate['price']      = $rate['price'] + $surchargeFee;
                    $tax_rate           = $facility->getTaxRate($rate, '0');
                } else {
                    $tax_rate           = $facility->getTaxRate($rate, '0');
                }
                /*** add  permit taxes pims-14518  upbl-163 */
                $ticketPrice = $total_amount = $rate[0]['rate'] + $permit_processing_fee + $additionalFee + $tax_rate + $surchargeFee; //pims-14518  upbl-163
                $effective_pro_rate = number_format($current_monthly_rate, 2);
                $final_amount = $rate[0]['rate'];
            } else {
                /*** add  permit taxes pims-14518  upbl-163 */
                $rate['price']         = $parkingAmount = $effective_pro_rate;
                $tax_rate              = $facility->getTaxRate($rate, '0');
                $additionalFee         = $facility->getAdditionalFee($rate);
                $surchargeFee          = $facility->getSurchargeFee($rate);

                if ($facility->tax_with_surcharge_enable == '1') {
                    $rate['price']      = $rate['price'] + $surchargeFee;
                    $tax_rate           = $facility->getTaxRate($rate, '0');
                } else {
                    $tax_rate           = $facility->getTaxRate($rate, '0');
                }
                /*** add  permit taxes pims-14518  upbl-163 */
                $ticketPrice = $total_amount = $permit_processing_fee + $effective_pro_rate + $additionalFee + $tax_rate + $surchargeFee; //pims-14518  upbl-163
                $final_amount = $effective_pro_rate;
                $effective_pro_rate = number_format($effective_pro_rate, 2, ".", "");
            }
            if (isset($result['individual_permit_services'])) {
                $request->request->add(['individual_permit_services' => $result['individual_permit_services']]);
            }

            #DD multitax
            $result['prorate_disable'] = ($facility->facilityConfiguration->is_prorate_apply == '1') ? "0" : "1";
            $result['base_rate'] = number_format($current_monthly_rate, 2);
            $result['per_day_rate'] = number_format($per_day_rate, 2);
            $result['effective_days'] = number_format($days, 2);
            $result['effective_pro_rate'] = number_format($effective_pro_rate, 2);
            $result['processing_fee'] = number_format($permit_processing_fee, 2);
            $result['total_amount'] = number_format($total_amount, 2);
            /*** add  permit taxes pims-14518  upbl-163 */
            $partner_id     = $secret->partner_id;
            $diff_in_hours         = $days * 24;
            $is_add_discount    = 0;
            /*** add  permit taxes pims-14518  upbl-163 */

            if ($facility->facilityConfiguration) {
                if ($facility->facilityConfiguration->is_prorate_apply == '0') {
                    $result['total_amount'] = sprintf("%.2f", $current_monthly_rate + $permit_service_amount);
                    $result['effective_pro_rate'] = sprintf("%.2f", $current_monthly_rate);
                    $effective_pro_rate = sprintf("%.2f", $current_monthly_rate);
                    /*** add  permit taxes pims-14518  upbl-163 */
                    $rate['price']         = $parkingAmount = ($current_monthly_rate + $permit_service_amount);
                    $tax_rate              = $facility->getTaxRate($rate, '0');
                    $additionalFee         = $facility->getAdditionalFee($rate);
                    $surchargeFee          = $facility->getSurchargeFee($rate);

                    if ($facility->tax_with_surcharge_enable == '1') {
                        $rate['price']      = $rate['price'] + $surchargeFee;
                        $tax_rate           = $facility->getTaxRate($rate, '0');
                    } else {
                        $tax_rate           = $facility->getTaxRate($rate, '0');
                    }
                    /*** add  permit taxes pims-14518  upbl-163 */
                    $ticketPrice = $total_amount = $permit_processing_fee + $parkingAmount + $additionalFee + $tax_rate + $surchargeFee; //pims-14518  upbl-163
                    $final_amount = $current_monthly_rate + $permit_service_amount;
                }
            }

            $result['surcharge_fee'] = number_format($surchargeFee, 2);
            $result['additional_fee'] = number_format($additionalFee, 2);
            $result['tax_fee'] = number_format($tax_rate, 2);
            /* if (isset($request->promocode) && !empty($request->promocode)) {

                $total_amount = $total_amount - $permit_processing_fee;
                $request->request->add(['amount' => $total_amount]);
                $request->request->add(['processing_fee' => $permit_processing_fee]);
                $request->request->add(['tax_amount' => isset($request->tax_amount) ? $request->tax_amount : 0]);
                $request->request->add(['client_id' => $request->header('X-ClientSecret')]);
                $this->validate($request, Promotion::$checkPromoValidationRulesThirdParty);
                $response = LatestPromoCodeLib::validatePromoCodeThirdParty($request);
                //return $response;
                if (isset($response->getData()->is_promocode_valid) == '1') {
                    //return [$effective_pro_rate,$response->getData()->discount_in_dollar];
                    if ($total_amount < $response->getData()->discount_in_dollar) {
                        $data = $response->getData();
                        $result['promocode_discount'] = $data;
                        $total_amount = '0.00';
                        $result['promocode_discount_amount'] = sprintf("%.2f", ($permit_processing_fee + $effective_pro_rate));
                    } else {
                        $new_pro_rate = ($total_amount) - ($response->getData()->discount_in_dollar);
                        $totalAmount = $new_pro_rate + $permit_processing_fee;
                        $total_amount = $new_pro_rate > 0 ? $totalAmount : '0.00';
                        $data = $response->getData();
                        $result['promocode_discount'] = $data;
                        $result['promocode_discount_amount'] = sprintf("%.2f", $response->getData()->discount_in_dollar);
                    }
                }
            } */

            /*** add  permit taxes pims-14518  upbl-163 start ****/

            $result['org_surcharge_fee'] = $result['surcharge_fee'] = number_format($surchargeFee, 2);
            $result['org_additional_fee'] = $result['additional_fee'] = number_format($additionalFee, 2);
            $result['tax_fee'] = number_format($tax_rate, 2);
            
            if (isset($request->promocode) && !empty($request->promocode)) {
                $promoCalculateData = self::promoCodeCalculation($request, $secret, $result, $ticketPrice, $parkingAmount, $rate, $surchargeFee, $additionalFee, $tax_rate, $processingFee, $diff_in_hours, $partner_id, $facility, $is_add_discount);
                if (isset($promoCalculateData['result'])) {
                    $result  = $promoCalculateData['result'];
                }
                if (isset($promoCalculateData['ticketPrice'])) {
                    $ticketPrice  = $promoCalculateData['ticketPrice'];
                }
                if (isset($promoCalculateData['netParkingAmount'])) {
                    $result['net_parking_amount']  = $promoCalculateData['netParkingAmount'];
                }
            }

            $total_amount = $ticketPrice;
            /*** add  permit taxes pims-14518  upbl-163 end ****/

            $result['effective_pro_rate'] = sprintf("%.2f", $effective_pro_rate);
            /* if (isset($facility->facilityConfiguration->is_prorate_apply) && $facility->facilityConfiguration->is_prorate_apply == '0') {
                $result['effective_pro_rate'] = '0'; //comment for now but enable it in future after discuss with sk
            } */
            $result['total_amount'] = sprintf("%.2f", $total_amount);
            $result['permit_final_amount'] = sprintf("%.2f", $total_amount);
            /**************13899******** */
            $result = self::fetchPermitExistForFuture($request, $secret, $result);
            /************13899 end ********** */

            return $result;
        } else {
            throw new ApiGenericException("Sorry! Permit Rate Not Found.");
        }
    }

    public static function permitRenewCalculation($request, $permit, $permit_final_amount = 0, $flag=NULL)
    {
        $secret = OauthClient::where('partner_id', $permit->partner_id)->first();
        if (!$secret) {
            throw new NotFoundException('No partner found.');
        }
        $is_resident_user = '';
        $user_permit_remaning = '';
        $request->request->add(['client_id' => $secret->secret]);
        $request->request->add(['promocode' => $permit->promocode]);
        $request->request->add(['facility_id' => $permit->facility_id]);
        $facility = $permit->facility;

        $permit_service_amount = 0;

        $tax_rate    = $additionalFee =    $surchargeFee = 0; #PIMS-14518         

        $rate['price']         = $parkingAmount = $permit_final_amount;
        $additionalFee         = $facility->getAdditionalFee($rate);
        $surchargeFee          = $facility->getSurchargeFee($rate);

        $permit_processing_fee  = $processingFee = isset($facility->permit_processing_fee) ? $facility->permit_processing_fee : '0.00';
        if ($permit_final_amount == '0.00') {
            $permit_processing_fee = "0.00";
        }

        if ($facility->tax_with_surcharge_enable == '1') {
            $rate['price']      = $permit_final_amount + $surchargeFee;
            $tax_rate           = $facility->getTaxRate($rate, '0');
        } else {
            $tax_rate           = $facility->getTaxRate($rate, '0');
        }
        /*** add  permit taxes pims-14518  upbl-163 */
        $ticketPrice = $total_amount = $permit_final_amount + $permit_processing_fee + $additionalFee + $tax_rate + $surchargeFee; //pims-14518  upbl-163
        $effective_pro_rate = number_format($permit_final_amount, 2);
        //$final_amount = $rate[0]['rate'];

        #DD multitax
        $result['prorate_disable']      = ($facility->facilityConfiguration->is_prorate_apply == '1') ? "0" : "1";
        $result['base_rate']            = number_format($permit_final_amount, 2);
        $result['effective_pro_rate']   = number_format($permit_final_amount, 2);
        $result['processing_fee']       = number_format($permit_processing_fee, 2);
        $result['total_amount']         = number_format($total_amount, 2);


        $is_add_discount    = 0;

        $result['org_surcharge_fee']    = $result['surcharge_fee']  = number_format($surchargeFee, 2);
        $result['org_additional_fee']   = $result['additional_fee'] = number_format($additionalFee, 2);
        $result['tax_fee'] = number_format($tax_rate, 2);

        /* if (isset($request->promocode) && !empty($request->promocode)) {
            $diff_in_hours 		= $days * 24;
            $promoCalculateData = self::promoCodeCalculation($request, $secret, $result,$ticketPrice, $parkingAmount, $rate ,$surchargeFee, $additionalFee, $tax_rate, $processingFee, $diff_in_hours, $secret->partner_id, $facility, $is_add_discount);		
            if(isset($promoCalculateData['result'])){
                $result  = $promoCalculateData['result'];
            }
            if(isset($promoCalculateData['ticketPrice'])){
                $ticketPrice  = $promoCalculateData['ticketPrice'];
            }	
            if(isset($promoCalculateData['netParkingAmount'])){
                $result['net_parking_amount']  = $promoCalculateData['netParkingAmount'];
            }			
        }
        */
        $total_amount = $ticketPrice;
        /*** add  permit taxes pims-14518  upbl-163 end ****/

        $result['effective_pro_rate'] = sprintf("%.2f", $effective_pro_rate);
        $result['permit_final_amount'] = sprintf("%.2f", $total_amount);

        return $result;
    }

    /**************DD 13899******** */
    public static function fetchPermitExistForFuture($request, $secret, $result)
    {
        $logFactory = app(LoggerFactory::class); // or resolve it manually
        $log = $logFactory->setPath('logs/are/buy-monthlypermit')->createLogger('buy-monthly');
        $log->info('QueryBuilder->fetchPermitExistForFuture');
        $countryCode = self::appendCountryCode(); #PIMS-12292 DD
        $existPhone = 0;
        if (isset($request->user_id) && !empty($request->user_id)) {
            $existPhone = User::where('id', $request->user_id)->first();
            if ($existPhone) {
                $users = $existPhone;
            }
        } else {
            if ($request->phone) {
                $phone = $countryCode . $request->phone;
                $existPhone = User::where('phone', $phone)->where('created_by', $secret->partner_id)->first();
            }
            if (!$existPhone) {
                $existPhone = User::where('email', $request->email)->where('created_by', $secret->partner_id)->first();
            }
            if ($existPhone) {
                $users = $existPhone;
            }
        }
        if (isset($users)) {
            $permitExist = PermitRequest::where('user_id', $users->id)->where('facility_id', $request->facility_id)->where('permit_rate_id', $request->permit_rate_id);
            if ($request->license_number != '') {
                $permitExist = $permitExist->where('license_number', $request->license_number);
            }
            $permitExist = $permitExist->WhereNull('cancelled_at')->whereNull('deleted_at')->get();
            $userdata = [];
            $values = "";
            //print_r($permitExist);die;

            if (isset($permitExist) && count($permitExist) > 0) {
                if (isset($request->vehicleList) && !empty($request->vehicleList)) {
                    $permitTypeNameExists = false;
                    if (gettype($request->vehicleList) == 'string') {
                        $decode = json_decode($request->vehicleList, true);
                    } else {
                        $decode = $request->vehicleList;
                    }
                    foreach ($decode as $key => $value) {
                        $value = (object) $value;
                        $log->info("License Plate Number: $value->license_plate and Partner Id: $secret->partner_id");
                        $isExpire = PermitVehicle::where('license_plate_number', $value->license_plate)->where('partner_id', $secret->partner_id)->first();
                        if (isset($isExpire) && !empty($isExpire)) {
                            foreach ($permitExist as $permitCheck) {
                                $log->info("PermitId: $permitCheck->id and Vehicle Id: $isExpire->id");
                                $isVehicleMappingExist = PermitVehicleMapping::where('permit_request_id', $permitCheck->id)->where('permit_vehicle_id', $isExpire->id)->first();
                                if (isset($isVehicleMappingExist->id)) {
                                    $values != "" && $values .= ",";
                                    $values .= $isExpire->license_plate_number;
                                    $permitTypeNameExists = true;
                                    $permit_start_date = Carbon::createFromFormat('Y-m-d', $permitCheck->desired_start_date);
                                    $permit_end_date = Carbon::createFromFormat('Y-m-d', $permitCheck->grace_end_date);
                                    $end_date = Carbon::createFromFormat('m-d-Y', $result['end_date']);
                                    $start_date = Carbon::createFromFormat('m-d-Y', $result['start_date']);
                                    if ($permit_start_date->gt($end_date)) {
                                        //if permit already exist for future and try to craeate permit again for present month
                                        $result['hide_auto_consent'] = 1;
                                        $result['hide_auto_consent_msg'] = "You already have the same permit type set for " . date('F', strtotime($permitCheck->desired_start_date)) . " month";
                                        $log->info("fetchPermitExistForFuture: You already have the same permit type set for " . $permitCheck->desired_start_date . " : " . $end_date);
                                    } else if ($permit_end_date->gte($start_date)) {
                                        //if permit already exist for present month and try to create permit for future within exist permit period
                                        $result['error_permit_exist'] = 'A permit is already issued for the vehicle with the License Plate ' . $values . ' for this facility. Please use another vehicle for purchasing another permit.';
                                        $log->info("fetchPermitExistForFuture: A permit is already issued in permitrequest for LP " . $values);
                                    } else if ($permit_end_date->lt($start_date)) {
                                        //A user has a permit with current month and autorenewal is off , and purchases again for the next month
                                        if ($permitCheck->user_consent) {
                                            $result['error_permit_exist'] = 'A active permit is already issued for the same permit type #' . $permitCheck->account_number;
                                            $log->info("fetchPermitExistForFuture: A active permit is already issued for the same permit type #" . $permitCheck->account_number);
                                        } else {
                                            $result['show_auto_consent'] = 1;
                                            $result['show_auto_consent_msg'] = 'You already have same permit type which can be set for renewal for next month, Do you still want to continue ?';
                                            $log->info("fetchPermitExistForFuture: You already have same permit type which can be set for renewal for next month, Do you still want to continue ?");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return $result;
    }

    public static function getPermitTenure($Permit_id)
    {
        $date = strtotime(date('Y-m-d'));
        //$date = strtotime('2026-01-22');

        $futureDate = date('Y', strtotime('+1 year'));
        $pastYear = date('Y', strtotime('-1 year'));
        $presentYear = date('Y');

        $tenure_start_fall = strtotime($pastYear . "-" . config('parkengage.PERMIT_FALL_START'));
        $tenure_end_fall   = strtotime($presentYear . "-" . config('parkengage.PERMIT_FALL_END'));

        $tenure_start_spring = strtotime($presentYear . "-" . config('parkengage.PERMIT_SPRING_START'));
        $tenure_end_spring   = strtotime($presentYear . "-" . config('parkengage.PERMIT_SPRING_END'));

        $tenure_start_summer = strtotime($presentYear . "-" . config('parkengage.PERMIT_SUMMER_START'));
        $tenure_end_summer   = strtotime($presentYear . "-" . config('parkengage.PERMIT_SUMMER_END'));
        $permitTenure = '';

        if ($date >= $tenure_start_fall && $date <= $tenure_end_fall) {
            $permitTenure = 1;
        }

        if ($date >= $tenure_start_spring && $date <= $tenure_end_spring) {
            $permitTenure = 2;
        }

        if ($date >= $tenure_start_summer && $date <= $tenure_end_summer) {
            $permitTenure = 3;
        }
        $permit_tenure_mapping = permitTenureMapping::where('permit_rate_id', $Permit_id)
            ->where('permit_tenure_id', $permitTenure)
            ->first();
        return     $permit_tenure_mapping;
    }

    public function getUpdatedPriceforHourlyPromoCode($result, $data, $request)
    {
        if ($result->getData()->is_promocode_valid == true) {
            if (isset($result->getData()->promocode->discount_type) && $result->getData()->promocode->discount_type == 'hours') {
                if (isset($request->rate_id_hourly)) {
                    $rateId =  $request->rate_id_hourly;
                    $rate = Rate::where('id', $rateId)->first();
                }
                $payableAMountNewRate = 0;
                $request->request->add(['amount' => $request->amount]);
                if (isset($request->length_of_stay) && $request->length_of_stay > 0) {
                    if (isset($rate) && !empty($rate['price'])) {
                        //$perhours=$rate['price']/$request->length_of_stay;
                        //per hours price changes due to change in logic parkingAmount/lenthstay per hours
                        //if need pervious check is uncomment
                        //new logic for first 12 or discount hours logic change
                        //new condition
                        $perhours = $rate['price'] / $rate['max_stay'];
                        if (isset($result->getData()->first_hours_apply) && $result->getData()->first_hours_apply == '1' && $result->getData()->discount_in_hours <= self::FULL_DAY_HOUR_VAL) {
                            $fullday = self::FULL_DAY_HOUR_VAL;
                            $length = $result->getData()->discount_in_hours;
                            $length = 24;
                            $freeHoursRate =  self::getUpdatedRate($request->facility_id, $request->arrival_time, $length);
                            // dd($freeHoursRate['price'],$fullday);
                            $perhours = $freeHoursRate['price'] / $fullday;
                            //new changes for promocode
                            if ($request->length_of_stay > 0 && $request->length_of_stay < self::FULL_DAY_HOUR_VAL) {
                                $remaininglenth = $request->length_of_stay - $result->getData()->discount_in_hours;
                                $rate = self::getUpdatedRate($request->facility_id, $request->arrival_time, $remaininglenth);
                                $payableAMountNewRate = $rate['price'];
                                //check for discount hours and payable are not same
                                // $checkDiscountAMount=  $request->amount- $payableAMountNewRate;
                            }
                            //end new changes
                        } elseif (isset($result->getData()->first_hours_apply) && $result->getData()->first_hours_apply == '2') {
                            $length = $rate['max_stay'];
                            $freeHoursRate =  self::getUpdatedRate($request->facility_id, $request->arrival_time, $length);
                            $perhours = $freeHoursRate['price'] / $length;
                            if ($request->length_of_stay > 0 && $request->length_of_stay < self::FULL_DAY_HOUR_VAL) {
                                $remaininglenth = $request->length_of_stay - $result->getData()->discount_in_hours;
                                $rate = self::getUpdatedRate($request->facility_id, $request->arrival_time, $remaininglenth);
                                $payableAMountNewRate = $rate['price'];
                            }
                        }
                        //end

                        //$perhours=$rate['price']/$request->length_of_stay;
                        //end new logic
                    } else {
                        $perhours = $request->baseprice / $request->length_of_stay;
                    }
                    if ($result->getData()->no_of_times == '0') {
                        $dayCount = ceil($request->length_of_stay / self::FULL_DAY_HOUR_VAL);
                        $applyHours = $dayCount * $result->getData()->discount_in_hours;
                        $freeHours = $perhours * $applyHours;
                    } else {
                        $freeHours = $perhours * $result->getData()->discount_in_hours;
                    }



                    $payableAmount = $request->amount - $freeHours;
                    $discountAmount = $freeHours;
                    $data->discount_in_dollar = $discountAmount;
                    $data->payable_amount = $payableAmount;
                    //new payble amount Logic
                    if ($payableAMountNewRate > 0) {
                        $data->payable_amount = $payableAMountNewRate;
                        $data->discount_in_dollar =  $request->amount - $data->payable_amount;
                    }



                    $result->setData($data);
                }
            }
        }
    }

    public static function getUpdatedRate($facility_id, $arrival, $length_of_stay)
    {
        $facility = Facility::with(['FacilityPaymentDetails', 'facilityConfiguration'])->find($facility_id);
        return $rate = $facility->rateForReservationOnMarker($arrival, $length_of_stay, false, false, null, false, false, '0', false);
    }

    #pims-14610
    public static function fetchPermitGraceEndDate($PermitRequest)
    {
        if ($PermitRequest->no_of_days >= 28) {
            $partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $PermitRequest->partner_id)->first();
            if (isset($partnerPaymentDetails->permit_grace_days)) {
            return Carbon::parse($PermitRequest->desired_end_date)->addDays($partnerPaymentDetails->permit_grace_days)->format('Y-m-d');
            }
        }
        return $PermitRequest->desired_end_date;
    }
    #pims-14610
    public static function createPermitRequestHistoryNew($monthlyRequest)
    {
        try {
            QueryBuilder::setCustomTimezone($monthlyRequest->facility_id);
            if ($monthlyRequest) {
                $permitRenewDate = date('Y-m-d h:i:s');
                $permitData = new PermitRequestRenewHistory();

                $permitData->permit_request_id         = $monthlyRequest->id;
                $permitData->user_id                 = $monthlyRequest->user_id;
                $permitData->facility_id             = $monthlyRequest->facility_id;
                $permitData->anet_transaction_id     = $monthlyRequest->anet_transaction_id;
                $permitData->tracking_code             = $monthlyRequest->tracking_code;
                $permitData->email                     = $monthlyRequest->email;
                $permitData->name                     = $monthlyRequest->name;
                $permitData->phone                     = $monthlyRequest->phone;
                $permitData->permit_rate             = $monthlyRequest->permit_rate;
                $permitData->permit_rate_id         = $monthlyRequest->permit_rate_id;
                $permitData->approved_on             = $monthlyRequest->approved_on;
                $permitData->account_number         = $monthlyRequest->account_number;
                $permitData->monthly_duration_value = $monthlyRequest->monthly_duration_value;
                $permitData->no_of_days             = $monthlyRequest->no_of_days;
                $permitData->partner_id             = $monthlyRequest->partner_id;
                $permitData->license_number         = $monthlyRequest->license_number;
                $permitData->mer_reference             = $monthlyRequest->mer_reference;
                $permitData->image_front             = $monthlyRequest->image_front;
                $permitData->image_back             = $monthlyRequest->image_back;
                $permitData->user_consent             = $monthlyRequest->user_consent;
                $permitData->vehicle_id             = $monthlyRequest->vehicle_id;
                $permitData->is_admin                 = $monthlyRequest->is_admin;
                $permitData->ex_month                 = $monthlyRequest->ex_month;
                $permitData->ex_year                 = $monthlyRequest->ex_year;
                $permitData->payment_gateway         = $monthlyRequest->payment_gateway;
                $permitData->permit_type             = $monthlyRequest->permit_type;
                $permitData->is_payment_authrize     = $monthlyRequest->is_payment_authrize;
                $permitData->session_id             = $monthlyRequest->session_id;
                $permitData->permit_type_name         = $monthlyRequest->permit_type_name;
                $permitData->skidata_id             = $monthlyRequest->skidata_id;
                $permitData->skidata_value             = $monthlyRequest->skidata_value;
                $permitData->acknowledge             = $monthlyRequest->acknowledge;
                $permitData->facility_zone_id         = $monthlyRequest->facility_zone_id;
                $permitData->desired_start_date     = $monthlyRequest->desired_start_date;
                $permitData->desired_end_date         = $monthlyRequest->desired_end_date;
                $permitData->cancelled_at             = $monthlyRequest->cancelled_at;
                $permitData->created_at             = $permitRenewDate;
                $permitData->updated_at             = $permitRenewDate;
                $permitData->deleted_at             = $monthlyRequest->deleted_at;
                $permitData->hid_card_number         = $monthlyRequest->hid_card_number;
                $permitData->account_name             = $monthlyRequest->account_name;
                $permitData->permit_final_amount     = $monthlyRequest->permit_final_amount;
                $permitData->user_remark             = $monthlyRequest->user_remark;
                $permitData->user_type_id             = $monthlyRequest->user_type_id;
                $permitData->is_antipass_enabled     = $monthlyRequest->is_antipass_enabled;
                $permitData->admin_user_id             = $monthlyRequest->admin_user_id;
                $permitData->discount_amount         = $monthlyRequest->discount_amount;
                $permitData->promocode                 = $monthlyRequest->promocode;
                $permitData->negotiated_amount         = $monthlyRequest->negotiated_amount;
                $permitData->processing_fee         = $monthlyRequest->processing_fee;
                $permitData->refund_amount          = $monthlyRequest->refund_amount;
                $permitData->refund_type            = $monthlyRequest->refund_type;
                $permitData->refund_remarks         = $monthlyRequest->refund_remarks;
                $permitData->refund_date            = $monthlyRequest->refund_date;
                $permitData->refund_by              = $monthlyRequest->refund_by;
                $permitData->refund_transaction_id  = $monthlyRequest->refund_transaction_id;
                $permitData->refund_status          = $monthlyRequest->refund_status;
                $permitData->business_id              = $monthlyRequest->business_id;
                $permitData->permit_prorate         = $monthlyRequest->permit_prorate;
                $permitData->service_prorate        = $monthlyRequest->service_prorate;
                $permitData->renew_type                = $monthlyRequest->renew_type;
                $permitData->net_parking_amount     = $monthlyRequest->net_parking_amount;
                $permitData->grace_end_date            = $monthlyRequest->grace_end_date;
                $permitData->additional_fee         = $monthlyRequest->additional_fee;
                $permitData->surcharge_fee          = $monthlyRequest->surcharge_fee;
                $permitData->tax_fee                = $monthlyRequest->tax_fee;
                $permitData->save();
            }
        } catch (Exception $e) {
            $logFactory = app(LoggerFactory::class); // or resolve it manually
            $log = $logFactory->setPath('logs/are/buy-monthlypermit')->createLogger('buy-monthly');
            $log->info("QueryBuilder->createPermitRequestHistoryNew log for Approve Permit Sql Exception Case" . json_encode($e->getMessage()));
            throw new ApiGenericException('There is some issue occured while the permit Renew. Please Contact Admin');
        }
        return true;
    }
     public static function populatePermitValuesOnRenewal($val,$desiredEndDate,$noOfDays,$permitRate,$rateValidate=NULL,$transactionRetry=NULL,$anetTransactionId=NULL,$permitFinalAmount=NULL,$renewType=NULL){
        //$val->desired_start_date  = $desired_start_date;
       // $val->no_of_days          = $noOfDays;        
        $val->permit_rate         = $permitRate;
        $val->desired_end_date    = $desiredEndDate;
        $val->grace_end_date      = self::fetchPermitGraceEndDate($val);  //pims-14610
        if(!is_null($rateValidate) && isset($rateValidate['additional_fee'])){
            $val->additional_fee      = $rateValidate['additional_fee'];
            $val->surcharge_fee       = $rateValidate['surcharge_fee'];
            $val->tax_fee             = $rateValidate['tax_fee'];
            $val->processing_fee      = $rateValidate['processing_fee'];
        }        
        if(!is_null($anetTransactionId))
            $val->anet_transaction_id = $anetTransactionId;
        if(!is_null($transactionRetry))
            $val->transaction_retry   = $transactionRetry;
        if(!is_null($permitFinalAmount))
            $val->permit_final_amount   = $permitFinalAmount;
        if(!is_null($renewType))
            $val->renew_type   = $renewType;
        $val->promocode           = isset($rateValidate['promocode']) ? $rateValidate['promocode'] : NULL;
        $val->discount_amount     = isset($rateValidate['discount_amount']) ? $rateValidate['discount_amount'] : '0.00';
        return $val;
    }
    public static function formatPermitServiceCriteria($services)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        
		$i=0;
		foreach ($services as $service) {
			if(count($service->permit_service_criteria_mappings) > 0){
				$formatted = [];
				foreach ($service->permit_service_criteria_mappings as $permit_service_criteria_mapping) {
					$item = $permit_service_criteria_mapping->criteria;
					$days = explode(',', $item->days);
					sort($days); // Sort days to match the sequence of $allDays
					if ($days == $allDays) {
						$dayNamesStr = 'All Days';
					} else {
						$dayNames = array_map(function($day) use ($daysMap) {
							return $daysMap[$day];
						}, $days);
						$dayNamesStr = implode(',', $dayNames);
					}
	
					$entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
					$entry_time_end = date('h:iA', strtotime($item->entry_time_end));
					$exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));
			
					// Adjust exit time if it's greater than 24 hours
					$exit_time = explode(":",$item->exit_time_end);
					if($exit_time[0]>23){
						$next_hr = $exit_time[0] - 24;
						$item->exit_time_end = $next_hr.":".$exit_time[1].":".$exit_time[2];
						$exit_time_end = date('h:iA', strtotime($item->exit_time_end));
						$exit_time_overflow = ' (next day)';
					}else{
						$exit_time_overflow = '';
						$exit_time_end = date('h:iA', strtotime($item->exit_time_end));
					}
	
			
					$formatted[] = [
						'days' => $dayNamesStr,
						'entry_time_begin' => $entry_time_begin,
						'entry_time_end' => $entry_time_end,
						'exit_time_begin' => $exit_time_begin,
						'exit_time_end' => $exit_time_end . $exit_time_overflow,
					];
				}
				$services[$i]->criteria=$formatted;
			}
			$i++;
		}

        return $services;
    }

    #add parking time in email
    public static function formatPermitRateCriteria($criteria)
    {
        $daysMap = [
            1 => 'Sun',
            2 => 'Mon',
            3 => 'Tue',
            4 => 'Wed',
            5 => 'Thu',
            6 => 'Fri',
            7 => 'Sat'
        ];
        $allDays = range(1, 7);
        $formatted = [];

        foreach ($criteria as $item) {
            $days = explode(',', $item->days);
            sort($days); // Sort days to match the sequence of $allDays
            if ($days == $allDays) {
                $dayNamesStr = 'All Days';
            } else {
                $dayNames = array_map(function($day) use ($daysMap) {
                    return $daysMap[$day];
                }, $days);
                $dayNamesStr = implode(',', $dayNames);
            }

            $entry_time_begin = date('h:iA', strtotime($item->entry_time_begin));
            $entry_time_end = date('h:iA', strtotime($item->entry_time_end));
            $exit_time_begin = date('h:iA', strtotime($item->exit_time_begin));
    
            // Adjust exit time if it's greater than 24 hours
            $exit_time = explode(":",$item->exit_time_end);
            if($exit_time[0]>23){
                $next_hr = $exit_time[0] - 24;
                $item->exit_time_end = $next_hr.":".$exit_time[1].":".$exit_time[2];
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
                $exit_time_overflow = ' (next day)';
            }else{
                $exit_time_overflow = '';
                $exit_time_end = date('h:iA', strtotime($item->exit_time_end));
            }

    
            $formatted[] = [
                'days' => $dayNamesStr,
                'entry_time_begin' => $entry_time_begin,
                'entry_time_end' => $entry_time_end,
                'exit_time_begin' => $exit_time_begin,
                'exit_time_end' => $exit_time_end . $exit_time_overflow,
            ];
        }

        return $formatted;
    }

    #PIMS-14737 || Dev: Sagar || 29/07/2025
    public static function getPartnerPermissionByUserId($userId) {
        $getUserAccess = UserAccess::where('user_id', $userId)->where('is_active', 1)->first();
        if ($getUserAccess) {
            return true;
        }
        return false;
    
    }
    //PIMS-14941
    public static function createVehicleList($request){
        foreach ($request as $key => $value) {
            $result[] =  [
                "license_plate"    => $value->vehicle->license_plate_number,
                "make"             => $value->vehicle->make,
                "make_id"          => $value->vehicle->make_id,
                "model"            => $value->vehicle->model,
                "model_id"         => $value->vehicle->model_id,
                "color"            => $value->vehicle->color,
                "color_id"         => $value->vehicle->color_id,
                "style"            => $value->vehicle->style,
                "style_id"         => $value->vehicle->style_id,
                "state_id"         => $value->vehicle->state_id,
                "state_name"       => $value->vehicle->state_name,
                "country"          => $value->vehicle->country,
                "vehicle_type"     => $value->vehicle->vehicle_type,
                "vehicle_type_id"  => $value->vehicle->vehicle_type_id,
            ];
            return $result;
        }
    }
}
