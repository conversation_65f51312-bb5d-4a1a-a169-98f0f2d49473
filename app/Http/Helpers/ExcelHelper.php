<?php

namespace App\Http\Helpers;
use Illuminate\Support\Facades\Log;
use App\Exceptions\ApiGenericException;

use App\Services\LoggerFactory;
use Illuminate\Support\Facades\Auth;
use PHPExcel_Worksheet_Drawing;
class ExcelHelper
{

     public static function addLogoInExcelHeader($sheet,$color,$logo, $header = null, $lastColumn = 'I')
    {
        /* Code for logo*/
        $drawing = new PHPExcel_Worksheet_Drawing();
        // dd(public_path('assets/media/images/fb.png'));
        if (env('ISLOCAL')) {
            $drawing->setPath(public_path('assets/media/images/breeze.png'));
        } else {
            // For Dynamic
            $drawing->setPath(storage_path('app/brand-settings/' . $logo));
        }

        $drawing->setCoordinates('A1');

        // Adjust the dimensions of the image if needed
        $drawing->setWidth(150);
        $drawing->setHeight(50);
        $drawing->setOffsetX(25);
        $drawing->setOffsetY(10);
        // Add image to worksheet
        $sheet->getDrawingCollection()->append($drawing);
        /* End Code for logo*/


        // !! Text Section 
        // $color = $color;
        $sheet->mergeCells('A1:' . $lastColumn . '1');
        $sheet->getRowDimension(1)->setRowHeight(60);
        if ($header) {
            $sheet->setCellValue('A1', $header);
        } else {
            $sheet->setCellValue('A1', 'Daily Revenue Report');
        }
        $sheet->cell('A1:' . $lastColumn . '1', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            // $cell->setBackground($color);
            // $cell->setFontColor('#ffffff');
            $cell->setFontSize('30');
        });
    }



}
