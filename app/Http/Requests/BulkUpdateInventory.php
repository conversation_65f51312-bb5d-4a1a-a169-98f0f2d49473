<?php

namespace App\Http\Requests;

class BulkUpdateInventory extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return ['availability' => 'required', 'facility_id' => 'required|regex:/[0-9,]/',
                'macro'        => 'in:all-day,hr,dow,json', 'hr' => 'numeric|max:24|required_if:macro,hr',
                'day'          => 'required_if:macro,dow|in:sunday,monday,tuesday,wednesday,thursday,friday,saturday,Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday'];
    }
}
