<?php

namespace App\Http\Controllers;

use Response;

use App\Exceptions\ApiGenericException;
use Illuminate\Http\Request;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function loadRelationships($model, $relationships)
    {
        foreach ($relationships as $relationship) {
            $model->load($relationship);
        }
    }

    protected function buildFailedValidationResponse(Request $request, array $errors)
    {
        if (($request->ajax() && ! $request->pjax()) || $request->wantsJson()) {
            throw new ApiGenericException("Validation Errors", 422, $errors);
        }

        return redirect()->to($this->getRedirectUrl())
            ->withInput($request->input())
            ->withErrors($errors, $this->errorBag());
    }

    protected function respondWithPdf($pdf, string $fileName = 'file.pdf')
    {
        return Response::make($pdf, 200)->withHeaders(
            [
            'Content-Type'          => 'application/pdf',
            'Content-Disposition'   => 'filename="' . $fileName . '"'
            ]
        );
    }

    protected function respondWithJpg($image, string $fileName = 'image.jpg')
    {
        return Response::make($image, 200)->withHeaders(
            [
            'Content-Type'          => 'image/jpg',
            'Content-Disposition'   => 'filename="' . $fileName . '"'
            ]
        );
    }

     /**
     * Return substring after a specific keyword
     *
     * @return substring
     */
    protected function postString($key, $inthat)
    {
        if (!is_bool(strpos($inthat, $key)))
        return substr($inthat, strpos($inthat,$key)+strlen($key));
    }

    /**
     * Return substring before a specific keyword
     *
     * @return substring
     */
    protected function preString($key, $inthat)
    {
        $str = substr($inthat, 0, strpos($inthat, $key));
        return $str ? $str : $inthat;
    }
}
