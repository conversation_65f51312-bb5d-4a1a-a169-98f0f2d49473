<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
//use Auth;
use Hash;
use Exception;
use Artisan;

//use App\Classes\IparcApi;
use App\Exceptions\NotFoundException;
use App\Exceptions\InvalidResponseException;
use App\Exceptions\ApiGenericException;
use App\Models\PermitRequest;
//use App\Models\AutopayMethod;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Models\AuthorizeNetTransaction;
use App\Services\LoggerFactory;
use App\Models\Facility;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
//use App\Models\PermitRequestPromocode;
//use CreditCard;
//use App\Models\MonthlyPromoUsage;
//use App\Models\PromoCode;
//use App\Classes\PromoCodeLib;
use App\Models\User;
use Mail;
use config;
use App\Models\OauthClient;
use App\Models\PermitRateDescription;
use App\Models\PermitRate;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\PermitVehicle;
use App\Models\PermitRateDiscount;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\ParkEngage\SpelmanUser;
use App\Models\PermitVehicleMapping;
use App\Http\Helpers\QueryBuilder;

class PermitController extends Controller
{


    protected  $request;
    protected  $authNet;
    protected  $log;
    protected  $log_auth_trans;
    protected $errorLog;
    protected $user;
    protected  $facility;
    protected $cc_type;
    protected $authorizeNetTransaction;
    protected $countryCode;
    protected $partnerPaymentDetails;

    public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet)
    {
        $this->log = $logFactory->setPath('logs/buy-monthlypermit')->createLogger('buy-monthly');
        $this->log_auth_trans = $logFactory->setPath('logs/log_auth_trans')->createLogger('log_auth_trans');
        $this->errorLog = $logFactory->setPath('logs/buy-monthlypermit')->createLogger('buy-monthly-error');
        $this->authNet = $authNet;
        $this->request = $request;
        $this->authorizeNetTransaction = null;
    }

    public function expirePermit($license)
    {
        $permitDetails = PermitVehicle::where('license_plate_number', $license)->first();
        if ($permitDetails) {
            PermitRequest::where('id', $permitDetails->permit_request_id)->delete();
            PermitVehicle::where('permit_request_id', $permitDetails->permit_request_id)->delete();
            echo "Permit has been expired/deleted";
            exit;
        } else {
            echo "sorry no permit found for this license number";
            exit;
        }
    }

    //Buy Monthly Parking
    public function buyPermit(Request $request)
    {
        //$this->errorLog->info("Request payload: " . json_encode($request->all()));
        if ($request->header('X-ClientSecret') != '') {

            if ($this->request->phone != '') {
                $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('No partner found.');
                }
                // Get country Code
                $this->countryCode = QueryBuilder::appendCountryCode();
                $existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $secret->partner_id)->first();
                $spelman_user_id = '';
                if ($this->request->spelman_user_id != '') {
                    $spelmanUser = SpelmanUser::where('employee_id', $this->request->spelman_user_id)->first();

                    $existUser = User::where('spelman_user_id', $spelmanUser->id)->first();
                    if ($existUser) {
                        throw new ApiGenericException('This record already exist in our system.');
                    }
                    $spelman_user_id = $spelmanUser->id;
                }
                if ($existPhone) {
                    if ($existPhone->type_id != '') {
                        if ($existPhone->type_id != $this->request->type_id) {
                            throw new ApiGenericException('This record already exist in our system.');
                        }
                    }
                    $existEmail = User::where('email', $this->request->email)->where('id', '!=', $existPhone->id)->where('created_by', $secret->partner_id)->first();
                    if ($existEmail) {
                        //                   throw new ApiGenericException('Email already exist.');   
                    }
                    $existPhone->email = $this->request->email;
                    $existPhone->name = $this->request->name;
                    $existPhone->type_id = $this->request->type_id;
                    //$existPhone->spelman_user_id = $spelman_user_id;
                    $existPhone->save();
                    $this->user = $existPhone;
                } else {
                    $this->user = User::create(
                        [
                            'name' => $this->request->name,
                            'email' => $this->request->email,
                            'phone' => $this->countryCode . $this->request->phone,
                            'password' => Hash::make(str_random(60)),
                            'anon' => false,
                            'user_type' => '5',
                            'created_by' => $secret->partner_id,
                            'type_id' => $this->request->type_id
                            //'spelman_user_id' => $spelman_user_id
                        ]
                    );
                    /*$existEmail = User::where('email', $this->request->email)->where('created_by', $secret->partner_id)->first();
                    if($existEmail){
       //                 throw new ApiGenericException('Email already exist.');   
                    }else{
                    $this->user = User::create(
                        [
                        'name' => $this->request->name,
                        'email' => $this->request->email,
                        'phone' => $this->countryCode.$this->request->phone,
                        'password' => Hash::make(str_random(60)),
                        'anon' => false,
                        'user_type' => '5',
                        'created_by' => $secret->partner_id,
                        'type_id' => $this->request->type_id
                        //'spelman_user_id' => $spelman_user_id
                        ]
                    );
		          }*/
                }
            } else {
                throw new ApiGenericException("Invalid request.");
            }
        } else {
            throw new ApiGenericException("No partner found.");
        }

        if (strtotime(date("Y-m-d", strtotime($this->request->desired_start_date))) < strtotime(date("Y-m-d"))) {
            throw new ApiGenericException("Sorry! Permit date can not be in past date.");
        }

        $permitExist = PermitRequest::where('user_id', $this->user->id)->whereDate('desired_end_date', '>=', $this->request->desired_start_date)->first();

        if ($permitExist) {
            // PermitRequest::where('user_id', $this->user->id)->delete();         
            throw new ApiGenericException("Sorry! You already have a permit.");
        }

        if ($this->request->vehicleList) {
            foreach (json_decode($this->request->vehicleList) as $key => $value) {
                $isExpire = PermitVehicle::with('vehicles')->where('license_plate_number', $value->license_plate)->first();
                if ($isExpire) {
                    if (strtotime($isExpire->vehicles->desired_end_date) >= strtotime(date("Y-m-d"))) {
                        throw new ApiGenericException('License Plate ' . $value->license_plate . ' already in use.');
                    } else {
                        continue;
                    }
                }
            }
        }

        //$this->request = $request;
        $facility = Facility::where('id', $this->request->facility_id)->first();
        if (!$facility) {
            throw new ApiGenericException("Invalid garage.");
        }
        $this->facility = $facility;
        //decrypt the card
        if ($this->request->is_card_req == '1') {
            $this->setDecryptedCard();
        }

        //$this->cc_type = CreditCard::validCreditCard($this->request->card_number)['type'];
        if (!$facility->accept_cc) {
            $this->errorLog->error("Facility with id: {$facility->id} does not accept credit cards. (User ID: {$this->user->id})");
            throw new ApiGenericException("Facility does not accept credit cards");
        }

        if ($facility->active == 0) {
            throw new ApiGenericException('Something went wrong and we were not able to complete your transaction. Please return to the booking page and try again.');
        }

        //$this->validate($request, array_merge(PermitRequest::$validParams, AuthorizeNetTransaction::$cardValidParams), PermitRequest::$validParamsMessages);

        //$promocode = $this->validateMonthlyPromocodeRequest($request); 

        //verify if amount is valid
        //$totalAmount = $totalAmountCheck = $request->permit_rate;


        $is_partner = 0;
        if ($this->user->user_type == 3) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
            $is_partner = 1;
        } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
            if ($this->partnerPaymentDetails) {
                $is_partner = 1;
            } else {
                throw new NotFoundException('No payment gateway details found for this partner.');
            }
        } else {
            $is_partner = 0;
        }
        $totalAmount = $this->request->permit_rate;
        //need to check and subtract oversize, exotic and electric check to validate amount
        if ($this->request->is_card_req == '1') {
            $rate = "";

            $rateActiveCheck = 0;
            $rateAmountChangeCheck = 0;
            $PermitRateDescription = PermitRateDescription::where('type_id', $this->request->type_id)->where('partner_id', $this->user->created_by)->first();
            if (!$PermitRateDescription) {
                throw new ApiGenericException('1Something went wrong and we were not able to complete your transaction. Please return to the booking page and try again.');
            }
            $PermitRates = PermitRate::where('permit_rate_description_id', $PermitRateDescription->id)->get();

            if ($this->request->type_id == '3') {
                /*$PermitRateDiscount = PermitRateDiscount::where('permit_rate_description_id', $PermitRateDescription->id)->get();
                foreach($PermitRateDiscount as $PermitRate){
                
                if($PermitRate->rate_discount == $totalAmount){
                    
                    $rate=$PermitRate->rate;
                    $rateAmountChangeCheck = 1;
                    if($PermitRate->active == 1)
                    {
                        $rateActiveCheck = 1;
                    }
                    
                }
                }*/
                $rateAmountChangeCheck = 1;
                $rateActiveCheck = 1;
            } else {
                foreach ($PermitRates as $PermitRate) {
                    if ($PermitRate->rate == $totalAmount) {

                        $rate = $PermitRate->rate;
                        $rateAmountChangeCheck = 1;
                        if ($PermitRate->active == 1) {
                            $rateActiveCheck = 1;
                        }
                    }
                }
            }
            if ($rateAmountChangeCheck == 0) {
                throw new ApiGenericException('Something went wrong and we were not able to complete your transaction. Please return to the booking page and try again.');
            } else {
                if ($rateActiveCheck == 0) {
                    throw new ApiGenericException('Something went wrong and we were not able to complete your transaction. Please return to the booking page and try again.');
                } else {
                    if ($totalAmount > 0) {
                        try {
                            if ($is_partner == 1) {
                                $this->authNet
                                    ->setUser($this->user)
                                    ->setFacility($this->facility)
                                    ->isPartner($this->partnerPaymentDetails)
                                    ->setBillingAddress($this->getBillingArray());
                                $this->authNet
                                    ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
                            } else {
                                $this->authNet
                                    ->setUser($this->user)
                                    ->setFacility($this->facility)
                                    ->setBillingAddress($this->getBillingArray());
                                $this->authNet
                                    ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
                            }
                            // Validate their credit card manually
                            //$this->authNet->validateCardIparc("Authorization test for monthly request {$this->request->email}");
                        } catch (Exception $e) {
                            $this->errorLog->error("Could not validate the card from Auth.Net (User ID: {$this->user->id}): " . json_encode($e->getMessage()));
                            $error_message = $e->getMessage();
                            if ($error_message == "Error getting valid response from api. Check log file for error details") {
                                throw new ApiGenericException("The system is not currently available. Please try again later.");
                            }
                            if (
                                strpos(strtolower($error_message), 'card has expire') !== false
                                || strpos(strtolower($error_message), 'expiration date is invalid') !== false
                                || strpos(strtolower($error_message), 'card number is invalid') !== false
                            ) {
                                throw new ApiGenericException("We were unable to authorize the credit card you provided. please confirm the card info or use another card and try again.");
                            }
                            throw $e;
                        }
                    }
                }
            }
        }

        $accountNumber = rand(100, 999) . rand(100, 999) . rand(10, 99);
        $charge = array();

        // Now we will try to create account
        if ($accountNumber > 0) {
            if ($totalAmount > 0) {
                // Making AuthNet transaction for pending capture
                $reservationMode = (substr($request->description, 0, 6) === "Mobile") ? "Mobile Permit Booking" : "Permit Booking";
                try {

                    if ($is_partner == 1) {
                        $charge =  $this->authNet->createTransaction(
                            $totalAmount,
                            "{$reservationMode}",
                            $accountNumber
                        )->isPartner($this->partnerPaymentDetails)->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur    
                    } else {
                        $charge =  $this->authNet->createTransaction(
                            $totalAmount,
                            "{$reservationMode}",
                            $accountNumber
                        )->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
                    }

                    $this->authorizeNetTransaction = AuthorizeNetTransaction::where('anet_trans_id', $charge['anet_trans_id'])->first();

                    if (count($charge) === 0) {
                        $this->errorLog->error("Auth.Net API Failed to get valid response (User ID: {$this->user->id})");
                        throw new ApiGenericException("Error in authorize.net api.");
                    }
                } catch (\Exception $e) {
                    $this->errorLog->error("Transaction Failed with error response (User ID: {$this->user->id}): " . json_encode($e->getMessage()));
                    throw new ApiGenericException($e->getMessage());
                }
            }
            /*try {
    				$mp_user = $this->createMonthlyAccount($accountNumber);
    			}
    			catch(\Exception $e) {
                    $this->errorLog->error("Error while creating Monthly Account in Icon DB for the user in our database (User ID: {$this->user->id}): ". json_encode($e->getMessage()));
    				throw new ApiGenericException("Error in create mp users.");
    			}*/

            //save all values to monthly resuest table
            $this->user->spelman_user_id = $spelman_user_id;
            $this->user->save();
            $permitRes  = $this->savePermitRequestData($accountNumber, '', $this->authorizeNetTransaction);

            if ($permitRes) {
                if ($this->request->vehicleList) {
                    foreach (json_decode($this->request->vehicleList) as $key => $value) {
                        $vehicle['license_plate_number'] = $value->license_plate;
                        $vehicle['make_model'] = $value->make_model;
                        $vehicle['permit_request_id'] = $permitRes->id;
                        PermitVehicle::create($vehicle);
                    }
                }
                $this->errorLog->info("User saved: " . json_encode($request->all()));
            }
            //send email to customer
            Artisan::queue('monthlypermituser:email', ['permit_request_id' => $permitRes->id, 'type_id' => '']);

            /*$permitRes['desired_start_date'] = $this->getDaySufixFormat($permitRes->desired_start_date);
                $permitRes['desired_end_date'] = $this->getDaySufixFormat($permitRes->desired_end_date);*/

            //send sms to user
            /*$accountSid = env('TWILIO_ACCOUNT_SID');
                $authToken  = env('TWILIO_AUTH_TOKEN');
                $client = new Client($accountSid, $authToken);
                $desiredStartDate = date("d F, Y", strtotime($permitRes->desired_start_date));
                $desiredEndDate = date("d F, Y", strtotime($permitRes->desired_end_date));
                $permitRate = $permitRes->permit_rate;
                try
                {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                    // the number you'd like to send the message to
                        $this->countryCode.$this->request->phone,
                   array(
                         // A Twilio phone number you purchased at twilio.com/console
                         'from' => env('TWILIO_PHONE'),
                         // the body of the text message you'd like to send
                         //'body' => "Fine"
                         'body' => 
            "Thank you for booking monthly permit with Spelman College. Your permit has been confirmed.\nPermit # : $permitRes->account_number \nStart Date : $desiredStartDate \nEnd Date : $desiredEndDate \nAmount Charged : $$permitRate"
                     )
                 );
                }catch (RestException $e)
                {
                    //throw new ApiGenericException($e->getMessage());
                    return $permitRes;
                }*/
            return $permitRes;
        } else {
            // Implement more specific logging here for later. (Iparc returned error)
            $this->errorLog->error("Could not create monthly account (User ID: {$this->user->id})");
            throw new ApiGenericException('Something went wrong while creating the account.');
        }
    }

    public function getDaySufixFormat($date)
    {
        $day = date('d', strtotime($date));
        $monthYear = date('F, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
        $second_last_digit = substr($number, -2, 1);
        $suffix = 'th';
        if ($second_last_digit != '1') {
            switch ($last_digit) {
                case '1':
                    $suffix = 'st';
                    break;
                case '2':
                    $suffix = 'nd';
                    break;
                case '3':
                    $suffix = 'rd';
                    break;
                default:
                    break;
            }
        }
        if ((string) $number === '1') $suffix = 'st';
        return $number . $suffix . ' ' . $monthYear;
    }

    public function  savePermitRequestData($account_number, $payment_profile_id = null, $authTransaction = null)
    {
        // Create our monthly request object
        $PermitRequest = new PermitRequest();
        $PermitRequest->facility_id = $this->facility->id;
        $PermitRequest->user_id = $this->user->id;
        $PermitRequest->desired_start_date = $this->request->desired_start_date;
        $PermitRequest->email = $this->user->email;
        $PermitRequest->name = $this->user->name;
        $PermitRequest->phone = $this->user->phone;
        $PermitRequest->permit_rate_id = $this->request->permit_rate_id;
        $PermitRequest->approved_on = date('Y-m-d H:i:s');
        $PermitRequest->permit_rate = $this->request->permit_rate;
        $PermitRequest->account_number = $account_number;
        $PermitRequest->partner_id = $this->user->created_by;

        if ($authTransaction) {
            $PermitRequest->anet_transaction_id = isset($authTransaction->id) ? $authTransaction->id : '';
        }


        if ($this->request->file('image_front')) {
            $front = $this->request->file('image_front');
            $file_extension = $front->getClientOriginalExtension();
            $file_name = $account_number . '_front.' . $file_extension;
            $destination_path = storage_path("app/monthly-permit-user");
            if (!$front->move($destination_path, $file_name)) {
                throw new ApiGenericException("Something went wrong while uploading image");
            }
            $PermitRequest->image_front = $file_name;
        }
        if ($this->request->file('image_back')) {

            $back = $this->request->file('image_back');
            $file_extension = $back->getClientOriginalExtension();
            $file_name = $account_number . '_back.' . $file_extension;
            $destination_path = storage_path("app/monthly-permit-user");
            if (!$back->move($destination_path, $file_name)) {
                throw new ApiGenericException("Something went wrong while uploading image");
            }
            $PermitRequest->image_back = $file_name;
        }
        $semester = \DB::table('semesters')->whereDate('start_date', '<=', $this->request->desired_start_date)->whereDate('end_date', '>=', $this->request->desired_start_date)->first();

        if ($this->request->type_id == '2') {
            if ($this->request->week != '0') {
                $weekDays = 7 * $this->request->week;
                $endTimestamp = date('Y-m-d', strtotime($this->request->desired_start_date . ' + ' . $weekDays . ' days'));
                if (strtotime($semester->end_date) < strtotime($endTimestamp)) {
                    $PermitRequest->desired_end_date = $semester->end_date;
                } else {
                    $PermitRequest->desired_end_date = $endTimestamp;
                }
                $PermitRequest->no_of_days = $weekDays;
            }
            if ($this->request->month != '0') {
                $weekDays = date('t') * $this->request->month;
                $endTimestamp = date('Y-m-d', strtotime($this->request->desired_start_date . ' + ' . $weekDays . ' days'));
                if (strtotime($semester->end_date) < strtotime($endTimestamp)) {
                    $PermitRequest->desired_end_date = $semester->end_date;
                } else {
                    $PermitRequest->desired_end_date = $endTimestamp;
                }
                $PermitRequest->no_of_days = $weekDays;
            }
        } else {
            $PermitRequest->desired_end_date = $semester->end_date;
        }


        $PermitRequest->save();

        return $PermitRequest;
    }


    public function  saveCommuterPermitRequestData($account_number, $payment_profile_id = null, $authTransaction = null)
    {
        // Create our monthly request object
        $PermitRequest = new PermitRequest();
        $PermitRequest->facility_id = $this->facility->id;
        $PermitRequest->user_id = $this->user->id;
        $PermitRequest->email = $this->user->email;
        $PermitRequest->name = $this->user->name;
        $PermitRequest->phone = $this->user->phone;
        $PermitRequest->account_number = $account_number;
        $PermitRequest->partner_id = $this->user->created_by;

        if ($this->request->file('image_front')) {
            $front = $this->request->file('image_front');
            $file_extension = $front->getClientOriginalExtension();
            $file_name = $account_number . '_front.' . $file_extension;
            $destination_path = storage_path("app/monthly-permit-user");
            if (!$front->move($destination_path, $file_name)) {
                throw new ApiGenericException("Something went wrong while uploading image");
            }
            $PermitRequest->image_front = $file_name;
        }
        if ($this->request->file('image_back')) {

            $back = $this->request->file('image_back');
            $file_extension = $back->getClientOriginalExtension();
            $file_name = $account_number . '_back.' . $file_extension;
            $destination_path = storage_path("app/monthly-permit-user");
            if (!$back->move($destination_path, $file_name)) {
                throw new ApiGenericException("Something went wrong while uploading image");
            }
            $PermitRequest->image_back = $file_name;
        }

        $PermitRequest->save();

        return $PermitRequest;
    }


    private function createMonthlyAccount($account)
    {
        $mp_user = new MonthlyParkingUser();
        $mp_user->user_id = $this->user->id;
        $mp_user->name = $this->request->name;
        $mp_user->facility_id = $this->facility->id;
        $mp_user->garage_code = $this->facility->garage_code;
        $mp_user->account_number = $account;
        $mp_user->active = 1;
        $mp_user->type_id = $this->user->type_id;
        $mp_user->address_one = "";
        $mp_user->address_two = "";
        $mp_user->city =  "";
        $mp_user->state = "";
        $mp_user->zip = "";
        $mp_user->company_name = $this->user->company_name;
        $mp_user->address_type = "";
        $mp_user->country = "";
        $mp_user->phone_type_one = "";
        $mp_user->phone_type_two = "";
        $mp_user->phone_number_one = $this->user->phone;
        $mp_user->phone_number_two = "";
        $mp_user->phone_ext_one = "";
        $mp_user->phone_ext_two = "";
        $mp_user->phone_contact_one = "";
        $mp_user->phone_contact_two = "";
        $mp_user->billing_method = "";
        $mp_user->is_valid_ar_account = "0";
        $mp_user->partner_id = $this->user->created_by;

        if ($this->request->file('student_id_front')) {
            $front = $this->request->file('student_id_front');
            $file_extension = $front->getClientOriginalExtension();
            $file_name = $account . '_front.' . $file_extension;
            $destination_path = storage_path("app/monthly-permit-user");
            if (!$front->move($destination_path, $file_name)) {
                throw new ApiGenericException("Something went wrong while uploading image");
            }
            $mp_user->image_front = $file_name;
        }
        if ($this->request->file('student_id_back')) {

            $back = $this->request->file('student_id_back');
            $file_extension = $back->getClientOriginalExtension();
            $file_name = $account . '_back.' . $file_extension;
            $destination_path = storage_path("app/monthly-permit-user");
            if (!$back->move($destination_path, $file_name)) {
                throw new ApiGenericException("Something went wrong while uploading image");
            }
            $mp_user->image_back = $file_name;
        }

        if ($this->request->file('employee_id_front')) {
            $front = $this->request->file('employee_id_front');
            $file_extension = $front->getClientOriginalExtension();
            $file_name = $account . '_front.' . $file_extension;
            $destination_path = storage_path("app/monthly-permit-user");
            if (!$front->move($destination_path, $file_name)) {
                throw new ApiGenericException("Something went wrong while uploading image");
            }
            $mp_user->image_front = $file_name;
        }
        if ($this->request->file('employee_id_back')) {

            $back = $this->request->file('employee_id_back');
            $file_extension = $back->getClientOriginalExtension();
            $file_name = $account . '_back.' . $file_extension;
            $destination_path = storage_path("app/monthly-permit-user");
            if (!$back->move($destination_path, $file_name)) {
                throw new ApiGenericException("Something went wrong while uploading image");
            }
            $mp_user->image_back = $file_name;
        }

        if ($mp_user->save()) {
            return $mp_user;
        }
        return false;
    }


    public function setDecryptedCard()
    {
        if (isset($this->request->payment_profile_id) && $this->request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($this->request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = isset($cardData[4]) ? $cardData[4] : '';
        $this->request->request->add(
            [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code_on_card' => $zipCode
            ]
        );
    }

    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code_on_card ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }


    public function addCommuter(Request $request)
    {
        //$this->errorLog->info("Request payload: " . json_encode($request->all()));
        if ($request->header('X-ClientSecret') != '') {

            if ($this->request->phone != '') {
                $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('No partner found.');
                }
                // Get country Code
                $this->countryCode = QueryBuilder::appendCountryCode();
                //->where('created_by', $secret->partner_id)
                $partner_id = $secret->partner_id;
                $existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $secret->partner_id)->first();
                if ($existPhone) {

                    if ($existPhone->type_id != $this->request->type_id) {
                        throw new ApiGenericException('This record already exist in our system.');
                    }

                    $existEmail = User::where('email', $this->request->email)->where('id', '!=', $existPhone->id)->where('created_by', $secret->partner_id)->first();
                    if ($existEmail) {
                        throw new ApiGenericException('Email already exist.');
                    }
                    $existPhone->email = $this->request->email;
                    $existPhone->name = $this->request->name;
                    $existPhone->type_id = $this->request->type_id;
                    $existPhone->save();
                    $this->user = $existPhone;
                } else {
                    $existEmail = User::where('email', $this->request->email)->where('created_by', $secret->partner_id)->first();
                    if ($existEmail) {
                        throw new ApiGenericException('Email already exist.');
                    }
                    $this->user = User::create(
                        [
                            'name' => $this->request->name,
                            'email' => $this->request->email,
                            'phone' => $this->countryCode . $this->request->phone,
                            'password' => Hash::make(str_random(60)),
                            'anon' => false,
                            'user_type' => '5',
                            'created_by' => $secret->partner_id,
                            'type_id' => $this->request->type_id
                        ]
                    );
                }
            } else {
                throw new ApiGenericException("Invalid request.");
            }
        } else {
            throw new ApiGenericException("No partner found.");
        }

        $permitExist = PermitRequest::where('user_id', $this->user->id)->first();

        if ($permitExist) {
            throw new ApiGenericException("Sorry! You have already registerd with us.");
        }
        //$this->request = $request;
        $facility = Facility::where('id', $this->request->facility_id)->first();
        if (!$facility) {
            throw new ApiGenericException("Invalid garage.");
        }

        $this->facility = $facility;

        $accountNumber = rand(100, 999) . rand(100, 999) . rand(10, 99);
        // Now we will try to create account
        if ($accountNumber > 0) {
            /*try {
                $mp_user = $this->createMonthlyAccount($accountNumber);
            }
            catch(\Exception $e) {
                $this->errorLog->error("Error while creating Monthly Account in Icon DB for the user in our database (User ID: {$this->user->id}): ". json_encode($e->getMessage()));
                throw new ApiGenericException("Error in create mp users.");
            }*/

            //save all values to monthly resuest table
            $permitRes  = $this->saveCommuterPermitRequestData($accountNumber, '', '');
            //send email to customer
            if ($permitRes) {
                if ($this->request->vehicleList) {
                    foreach (json_decode($this->request->vehicleList) as $key => $value) {
                        $vehicle['license_plate_number'] = $value->license_plate;
                        $vehicle['make_model'] = $value->make_model;
                        $vehicle['permit_request_id'] = $permitRes->id;
                        PermitVehicle::create($vehicle);
                    }
                }
            }
            $this->errorLog->info("Commuter saved: " . json_encode($request->all()));
            Artisan::queue('monthlypermituser:email', ['permit_request_id' => $permitRes->id, 'type_id' => $this->request->type_id]);
            return $permitRes;
        } else {
            // Implement more specific logging here for later. (Iparc returned error)

            $this->errorLog->error("Could not create monthly account (User ID: {$this->user->id})");
            throw new ApiGenericException('Something went wrong while creating the account.');
        }
    }


    public function getPermitRequestData($tracking_code)
    {
        $monthly_request = PermitRequest::with(
            'facility.hoursOfOperation',
            'facility.photos',
            'facility.geolocations',
            'facility.hoursOfOperation',
            'paymentProfile.expirationDate'
        )->where('tracking_code', $tracking_code)
            ->orderBy('id', 'desc')
            ->first();

        if (!$monthly_request || !$monthly_request->facility) {
            throw new NotFoundException('Monthly request not found.');
        }

        $data['monthly_request'] = $monthly_request->toArray();

        $data['facility_data'] = [
            'id' => $monthly_request->facility->id,
            'short_name' => $monthly_request->facility->short_name,
            'full_name' => $monthly_request->facility->full_name,
            'facility_name_modified' => $monthly_request->facility->facility_name_modified,
            'garage_code' => $monthly_request->facility->garage_code,
            'between_streets' => $monthly_request->facility->between_streets,
            'entrance_location' => $monthly_request->facility->entrance_location,
            'phone_number' => $monthly_request->facility->phone_number,
            'is_247_open' => $monthly_request->facility->getOpen247Attribute() ? 1 : 0,
            'monthly_oversized_fee' => $monthly_request->facility->monthly_oversized_fee,
            'monthly_electric_surcharge' => $monthly_request->facility->monthly_electric_surcharge,
            'monthly_exotic_charge' => $monthly_request->facility->monthly_exotic_charge,
            'photo' => $monthly_request->facility->photos,
            'geolocation' => $monthly_request->facility->geolocations,
        ];

        if (!$data['facility_data']['is_247_open']) {
            $data['facility_data']['hours_of_operation_data'] = $monthly_request->facility->hoursOfOperation;
        }

        unset($data['monthly_request']['facility']);
        return $data;
    }

    public function deletePermitVehicle($license_plate)
    {


        $permitRequest = PermitRequest::where('license_number', $license_plate)->first();
        if ($permitRequest) {

            $permit_vehicle_map = PermitVehicleMapping::where('permit_request_id', $permitRequest->id)->get();
            foreach ($permit_vehicle_map as $val) {
                PermitVehicle::where('id', $val->permit_vehicle_id)->delete();
            }
            $permitRequest->delete();
            return ['Data delete sucess'];
        } else {
            throw new ApiGenericException('Sorry, No license plate found');
        }
    }
}
