<?php

namespace App\Http\Controllers;

use File;

use App\Models\CouponPromo;
use App\Models\Photo;
use Illuminate\Http\Request;

use App\Http\Requests;
use Intervention\Image\Facades\Image;
use Webpatser\Uuid\Uuid;

class CouponPromoController extends Controller
{
    public function index()
    {
        return CouponPromo::with('photo')->get();
    }

    public function show(CouponPromo $promo)
    {
        return $promo->load('photo');
    }

    public function store(Request $request)
    {
        $this->validate($request, CouponPromo::validParams());
        $promo = CouponPromo::create($request->only(['body', 'header', 'slug']));
        return $promo->load('photo');
    }

    public function destroy(CouponPromo $promo)
    {
        $promo->delete();
        return 'Promo deleted';
    }

    public function addPhoto(CouponPromo $promo, Request $request)
    {
        return $promo->uploadPhoto($request);
    }

    public function update(CouponPromo $promo, Request $request)
    {
        $this->validate($request, CouponPromo::validParams());

        $promo->update($request->all());

        return $promo->load('photo');
    }
}
