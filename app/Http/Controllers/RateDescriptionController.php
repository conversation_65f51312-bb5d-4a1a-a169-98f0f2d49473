<?php

namespace App\Http\Controllers;

use App\Models\RateDescription;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

class RateDescriptionController extends Controller
{
    public function index()
    {
        $rate_descriptions = RateDescription::all();
        return ['rate_descriptions' => $rate_descriptions];
    }

    public function rateDescriptionList($partner_id)
    {
        if($partner_id != ''){
            $rate_descriptions = RateDescription::where('partner_id', $partner_id)->get();
        }else{
            $rate_descriptions = RateDescription::all();
        }
        
        return ['rate_descriptions' => $rate_descriptions];
    }

    public function getRateDescription($id)
    {
        $rate_descriptions = RateDescription::where('id', $id)->get();
        return ['rate_descriptions' => $rate_descriptions];
    }
    public function store(Request $request)
    {
        $this->validate($request, RateDescription::$validParams);
        $rate_description = RateDescription::create($request->all());
        return $rate_description;
    }
    public function destroy(RateDescription $rate_description)
    {
        
    }
    public function update(Request $request, $id)
    {
        $this->validate($request, RateDescription::$validParams);
        $rate_description=RateDescription::find($id);
        $rate_description->name=$request->name;
        $rate_description->hours_description=$request->hours_description;
        $rate_description->description=$request->description;
        $rate_description->active_status=$request->active_status;
        
        if($rate_description->save()){
            return $rate_description;
        }
        else{
            return "Can't update now";
        }
    }
}
