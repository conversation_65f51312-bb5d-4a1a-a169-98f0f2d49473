<?php

namespace App\Http\Controllers;

use App\Models\LoyaltyCode;
use App\Models\LoyaltyUserAccounts;
use Illuminate\Http\Request;
use App\Exceptions\ApiGenericException;
use DB;
use App\Http\Helpers\QueryBuilder;

class LoyaltyCodeController extends Controller
{
    const RESULTS_PER_PAGE = 20;
    public function index(Request $request)
    {
        $LoyaltyCode = LoyaltyCode::query();

        if ($request->search) {
            $LoyaltyCode = QueryBuilder::buildSearchQuery($LoyaltyCode, $request->search, LoyaltyCode::$searchFields);
        }
        return $LoyaltyCode->orderBy('id','desc')->paginate(self::RESULTS_PER_PAGE);
    }

    public function show($id)
    {
        return LoyaltyCode::Id($id)->get();
    }

    public function getByCode($code)
    {
        return LoyaltyCode::Code($code)->get();
    }

    public function store(Request $request)
    {
        $this->validate($request, LoyaltyCode::$validParams);
        $response=LoyaltyCode::validateDates($request->start_date,$request->end_date, $request->no_end_date);
        $bonus_validation_response=LoyaltyCode::validateBonus($request->profile_completion_bonus,$request->sign_up_bonus);
        if($response || $bonus_validation_response) {
            throw new ApiGenericException(($response && $bonus_validation_response)?$response.", ".$bonus_validation_response:$response.$bonus_validation_response);
        }
           
        $LoyaltyCode = LoyaltyCode::create($request->all());

        if ($request->profile_completion_bonus == '') {
            $LoyaltyCode->profile_completion_bonus = NULL;
            $LoyaltyCode->save();
        }

        return $LoyaltyCode;
    }

    public function update(Request $request, $LoyaltyCode)
    {
        $this->validate($request, LoyaltyCode::$validParams);
        $response=LoyaltyCode::validateDatesForUpdate($request->start_date,$request->end_date, $request->no_end_date);
        $bonus_validation_response=LoyaltyCode::validateBonus($request->profile_completion_bonus,$request->sign_up_bonus);
        if($response || $bonus_validation_response) {
            throw new ApiGenericException(($response && $bonus_validation_response)?$response.", ".$bonus_validation_response:$response.$bonus_validation_response);
        }
        $LoyaltyCode=LoyaltyCode::find($LoyaltyCode);
        $LoyaltyCode->fill($request->all())->save();

        if ($request->profile_completion_bonus == '') {
            $LoyaltyCode->profile_completion_bonus = NULL;
            $LoyaltyCode->save();
        }
        
        return $LoyaltyCode;
    }

    public function destroy($id)
    {
        $LoyaltyCode=LoyaltyCode::find($id);
        if(!$LoyaltyCode){
            throw new ApiGenericException('ID does not exist');
        }
        return $LoyaltyCode->delete() ? $LoyaltyCode : null;
    }
    
    public function signupComplete(Request $request, $LoyaltyCodeId, $loyaltuUserAccountId)
    {
        $LoyaltyUserAccounts=LoyaltyUserAccounts::find($loyaltuUserAccountId);
        $LoyaltyUserAccounts->loyalty_code_id=$LoyaltyCodeId;
        $LoyaltyUserAccounts->save();
        return $LoyaltyUserAccounts;
    }

    public function generateLoyaltyCode()
    {
        return LoyaltyCode::generateLoyaltyCode();
    }
    

}
