<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Models\WebMeta;
use App\Http\Helpers\QueryBuilder;

use App\Exceptions\ApiGenericException;

class WebMetaController extends Controller
{
    public function index()
    {
        return WebMeta::all();
    }

    public function indexAdmin(Request $request)
    {
        $webMetas = WebMeta::query();

        if ($request->search) {
            $webMetas = QueryBuilder::buildSearchQuery($webMetas, $request->search, WebMeta::$searchFields);
        }

        return $webMetas->orderBy('title')->paginate(20);
    }

    public function getBySlug(Request $request)
    {
        if (!$request->slug) {
            throw new ApiGenericException("Must include slug with request to get web meta.");
        }

        $meta = WebMeta::where('slug', starts_with($request->slug, '/') ? $request->slug : "/{$request->slug}")->first();

        if (!$meta) {
            $meta = WebMeta::where('slug', '/')->first();
        }

        // Make sure our slug has a leading slash
        return [
            'meta' => $meta
        ];
    }

    public function store(Request $request)
    {
        $this->validate($request, WebMeta::$validParams);
        $webMeta = WebMeta::create($request->all());
        return $webMeta;
    }

    public function update(Request $request, WebMeta $webMeta)
    {
        $this->validate($request, WebMeta::$validParams);
        $webMeta->fill($request->all());
        $webMeta->save();
        return $webMeta;
    }

    public function show(WebMeta $webMeta)
    {
        return $webMeta;
    }

    public function destroy(WebMeta $webMeta)
    {
        $webMeta->delete();
        return $webMeta;
    }
}
