<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Artisan;
use DB;
use Response;
use File;
use App\Models\UserInvoice;
use App\Models\UserInvoicePast;
use App\Models\MonthlyParkingUser;

use App\Console\Commands\Import\UserInvoices as InvoiceImport;

use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;

use App\Services\Image;
use App\Services\Pdf;

class UserInvoiceController extends Controller
{
    /**
     * Get current invoice (itemized list charges for current billing period and past-due)
     */
    public function getCurrentInvoice(MonthlyParkingUser $account)
    {
        $invoices = UserInvoice::where('mp_user_id', $account->id)->get();
        return $invoices;
    }

    /**
     * Get all past user invoices, grouped by invoice date
     */
    public function getPastInvoice(MonthlyParkingUser $account)
    {
        $invoices = UserInvoicePast::where('mp_user_id', $account->id)
            ->select('id', 'invoice_date', DB::raw('bill_total as invoice_total'))
            ->groupBy('invoice_date')
            ->orderBy('invoice_date', 'desc')
            ->paginate(6);

        return $invoices;
    }

    /**
     * Get all past user invoices, grouped by invoice date
     */
    public function getCurrentPastInvoice(MonthlyParkingUser $account)
    {
        $result = array();

        $invoices_past = UserInvoicePast::where('mp_user_id', $account->id)
            ->select('id', 'invoice_date', DB::raw('bill_total as invoice_total'))
            ->groupBy('invoice_date')
            ->orderBy('invoice_date', 'desc')
            ->paginate(6);

        $invoices_current = UserInvoice::where('mp_user_id', $account->id)->get();    

        $result['invoice_past'] = $invoices_past;
        $result['invoice_current'] = $invoices_current;

        return $result;
    }
    

    /**
     * Get a past invoice in pdf format (itemized list charges for current billing period and past-due)
     */
    public function getPastInvoicePdf(MonthlyParkingUser $account, $date)
    {
        $pdf = $this->getPastInvoiceView($account, $date, Pdf::class);

        return $this->respondWithPdf($pdf);
    }

    public function getPastInvoiceJpg(MonthlyParkingUser $account, $date)
    {
        $jpg = $this->getPastInvoiceView($account, $date, Image::class);
        return $this->respondWithJpg($jpg);
    }

    protected function getPastInvoiceView(MonthlyParkingUser $account, $date, string $format = Image::class)
    {
        if (!isset($date) || $date == 'undefined') {
            throw new NotFoundException("No invoice for the provided date.");
        }

        return (new UserInvoice())->generatePreviousInvoice($account, $date, $format);
    }

    /**
     * Get current invoice in pdf format (itemized list charges for current billing period and past-due)
     */
    public function getCurrentInvoicePdf(MonthlyParkingUser $account)
    {
        if (!$account->userInvoices->all()) {
            return []; // TODO: This should throw a not found exception
        }

        $pdf = (new UserInvoice())->generateCurrentInvoice($account, Pdf::class);
        return $this->respondWithPdf($pdf);
    }

    public function getCurrentInvoiceJpg(MonthlyParkingUser $account)
    {
        if (!$account->userInvoices->all()) {
            return []; // TODO: This should throw a not found exception
        }

        $image = (new UserInvoice())->generateCurrentInvoice($account, Image::class);
        return $this->respondWithJpg($image);
    }

    public function import()
    {
        $importPath = config('icon.invoice_import_path') . InvoiceImport::FILE_NAME;

        if (!File::exists($importPath)) {
            throw new ApiGenericException("No import file exists at $importPath, could not import invoices.");
        }

        // rename file here so that they can't fire this twice
        rename($importPath, base_path(InvoiceImport::INVOICES_IMPORT_FILE_TO_PROCESS));

        Artisan::queueOn('import:user-invoices', InvoiceImport::QUEUE);

        return ['queued' => true];
    }
}
