<?php

namespace App\Http\Controllers;

use Auth;

use Illuminate\Http\Request;

use App\Http\Requests;

use App\Models\Rate;
use App\Models\Visitor\Visitor;
use App\Models\Visitor\VisitorCouponEmail;
use App\Models\Visitor\VisitorCouponPrint;
use App\Models\Visitor\VisitorCouponText;
use App\Models\Visitor\VisitorCouponView;
use App\Models\Visitor\VisitorReferral;
use App\Models\Visitor\ReferralUrlKey;
use App\Models\Visitor\ReferralUrlValue;

use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\VisitorMobileCode;

class VisitorController extends Controller
{
    /**
     * Visitor we are interacting with, if available
     */
    protected $visitor;

    /**
     * Get the visitor from the request
     */
    public function __construct(Request $request)
    {
        $visitorCode = $request->header(config('headers.visitor'));

        if ($visitorCode) {
            $this->visitor = Visitor::where('visitor_code', $visitorCode)->first();
        }
    }

    /**
     * Get information about an already existing visitor code,
     * along with the tracking events associated with that visitor
     *
     * @param  Request $request [description]
     * @return [type]           [description]
     */
    public function getVisitor(Request $request, Visitor $visitor)
    {
        return $visitor->load('couponEmails', 'couponPrints', 'couponTexts', 'couponViews', 'referrals');
    }

    /**
     * Create a new visitor code if the one provided is not valid
     *
     * @param  Request $request [description]
     * @return [type]           [description]
     */
    public function createVisitor(Request $request)
    {
        if ($this->visitor) {
            return $this->visitor;
        }

        $user = Auth::user();

        $visitor = new Visitor();
        $visitor->user_id = $user ? $user->id : null;
        $visitor->ip = $request->ip();
        $visitor->generateCode()->save();

        return $visitor;
    }

    /**
     * Create a visitor referral.
     * Referrals should be logged when visitors land on the site from adwords
     * or bing campaigns
     *
     * @param  Request $request [description]
     * @param  Visitor $visitor [description]
     * @return [type]           [description]
     */
    public function createVisitorReferral(Request $request)
    {
        $this->validate(
            $request, [
            'referrer' => 'required|url'
            ]
        );

        if (!$this->visitor) {
            throw new NotFoundException('No visitor found.');
        }

        $url = parse_url($request->referrer);

        // Save the referral
        $referral = new VisitorReferral();
        $referral->host = $url['host'];
        $referral->visitor_code_id = $this->visitor->id;
        $referral->save();

        if (isset($url['query']) && $url['query']) {
            $referral->saveQueryParams($url['query']);
        }

        return $referral->load('urlValues');
    }

    /**
     * Create a visitor coupon event
     *
     * @param  Request $request [description]
     * @param  Visitor $visitor [description]
     * @param  Coupon  $coupon  [description]
     * @return [type]           [description]
     */
    public function createVisitorCouponEvent(Request $request, Rate $coupon)
    {
        $typeMap = [
            'email' => VisitorCouponEmail::class,
            'print' => VisitorCouponPrint::class,
            'text' => VisitorCouponText::class,
            'view' => VisitorCouponView::class
        ];

        if (!isset($typeMap[$request->type])) {
            throw new ApiGenericException('Invalid event type, must be email, print, text, or view.');
        }

        $eventClass = $typeMap[$request->type];
        $this->validate($request, $eventClass::$validationRules);

        $visitorCode = $request->header(config('headers.visitor'));
        $visitor = Visitor::where('visitor_code', $visitorCode)->first();

        if (!$visitor) {
            throw new NotFoundException('No visitor found for that visitor code.');
        }
      
        $event = new $eventClass();
        $event->fill($request->all());
        $event->visitor_code_id = $visitor->id;
        $event->coupon_id = $coupon->id;
        if($request->type!='view' && $request->type!='email')
        { 
          $event->save();
        }
        return $event;
    }
    public function getVisitorCouponMobileCode(Request $request)
    {
         $visitorCode = $request->header(config('headers.visitor'));
         $visitor = Visitor::where('visitor_code', $visitorCode)->first();
         if (!$visitor) {
             throw new NotFoundException('No visitor found for that visitor code.');
         }
        //getting coupon code       
        $vistorMobileCouponDetails=VisitorMobileCode::where('visitor_id',$visitor->id)->first();
       if($vistorMobileCouponDetails!='')
       {
            return $vistorMobileCouponDetails->load('facility');
       }
       return $vistorMobileCouponDetails;
    }
}
