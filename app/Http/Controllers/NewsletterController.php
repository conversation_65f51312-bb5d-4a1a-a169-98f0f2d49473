<?php

namespace App\Http\Controllers;

use App\Models\CMSPage;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Exceptions\ApiGenericException;

use App\Http\Requests;
use App\Models\NewsletterSubscription;
use App\Http\Helpers\QueryBuilder;


class NewsletterController extends Controller
{

	public function index(Request $request) {

      if($request->sort != ''){
        $newsletter = NewsLetterSubscription::orderBy($request->sort,$request->sortBy);
      }else{
        $newsletter = NewsLetterSubscription::orderBy('id', 'Desc');
      }
      
      if ($request->search) {
        $newsletter = QueryBuilder::buildSearchQuery($newsletter, $request->search, NewsletterSubscription::$searchFields);
        }
      return $newsletter->paginate(20);
            
    }

    //
    public function store(Request $request)
    {
        $this->validate(
            $request, [
				'email' => 'required|email',
            ]
        );
		
		$newsletter = NewsLetterSubscription::where('email', $request->email)->first();
		if(!$newsletter) {
			$newsletter = new NewsLetterSubscription();
			$newsletter->email = $request->email;
			$newsletter->save();
		}
		
		return ["success" => true];
    }
}
