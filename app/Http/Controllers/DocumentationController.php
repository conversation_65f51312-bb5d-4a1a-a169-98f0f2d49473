<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;

class DocumentationController extends Controller
{

    public function index(Request $request)
    {
        // IP-limit access to the docs
        $whitelist = explode(',', env('DOCUMENTATION_IP_WHITELIST', '**************'));

        if (!in_array($request->ip(), $whitelist)) {
            throw new \Exception("You do not have access to this page.");
        }

        return response()->file(base_path('resources/assets/docs/index.html'));
    }
	
	 public function externalcheckincheckout(Request $request)
    {
		
        // IP-limit access to the docsd
        $whitelist = explode(',', env('DOCUMENTATION_IP_WHITELIST', '**************'));

        if (!in_array($request->ip(), $whitelist)) {
            throw new \Exception("You do not have access to this page.");
        }

        return response()->file(base_path('resources/assets/docs/checkincheckout.html'));
    }
}
