<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Rate;
use App\Models\SavedCoupon;
use App\Models\VisitorCode;

use App\Exceptions\NotFoundException;
use App\Exceptions\DuplicateEntryException;
use App\Models\VisitorMobileCode;
use App\Models\Visitor\Visitor;

class SavedCouponController extends Controller
{
    /**
     * checks to make sure coupon and user exist
     * checks to make sure there are no duplicates
     * saves the coupon and links it to a user
     *
     * @param Request $request
     */
    const EXPIRE_DAYS = 14;
    const DEFAULT_ZERO = 0;
    public function saveCoupon(Request $request, $userId)
    {
        $this->validate(
            $request, [
            'coupon_id' => 'required|integer',
            ]
        );

        $couponId = $request->input('coupon_id');

        //make sure user exists
        $userExist = User::find($userId);

        if (!$userExist) {
            throw new NotFoundException('No user found for this id.');
        }

        //make sure coupon exists
        $couponExist = Rate::find($couponId);

        if (!$couponExist) {
            throw new NotFoundException('No coupon found for this id.');
        }

        $fields = ['user_id' => $userId, 'coupon_id' => $couponId];
        $existing = SavedCoupon::where($fields)->first();

        if ($existing) {
            throw new DuplicateEntryException('You already have this coupon saved.');
        }

        $savedCoupon = new SavedCoupon();

        $savedCoupon->coupon_id = $couponId;
        $savedCoupon->user_id = $userId;

        $savedCoupon->save();

        return $savedCoupon;
    }


    /**
     * gets all saved coupons related to user
     *
     * @param  Request $request
     * @return mixed
     */
    public function getSavedCoupons(Request $request, $userId)
    {
        return SavedCoupon::with(
            [
            'rate' => function ($query) {
                $query->select('description', 'price', 'id', 'facility_id')
                    ->with(
                        ['facility' => function ($query) {
                            $query->select('id', 'short_name', 'full_name');
                        }]
                    );
            }
            ]
        )
            ->where('user_id', $userId)
            ->orderBy('id', 'desc')
            ->paginate(20);
    }


    /**
     * checks to make sure coupon and user exist
     * checks to make sure there are no duplicates
     * saves the coupon and links it to a user
     *
     * @param Request $request
     */
    public function saveCouponMob(Request $request, $userId)
    {
        $this->validate(
            $request, [
            'coupon_id' => 'required|integer'
            ]
        );

        $couponId = $request->input('coupon_id');
        $couponMobileCode = $request->input('coupon_mobile_code'); 

        //make sure user exists
        $userExist = User::find($userId);

        if (!$userExist) {
            throw new NotFoundException('No user found for this id.');
        }

        //make sure coupon exists
        $couponExist = Rate::find($couponId);

        if (!$couponExist) {
            throw new NotFoundException('No coupon found for this id.');
        }

        $fields = ['user_id' => $userId, 'coupon_id' => $couponId];
        $existing = SavedCoupon::where($fields)->first();

        if ($existing) {
            throw new DuplicateEntryException('You already have this coupon saved.');
        }

        if(empty($couponMobileCode))
        {
          $couponMobileCode = $couponExist->generateCouponCode();
        }

        $savedCoupon = new SavedCoupon();

        $savedCoupon->coupon_id = $couponId;
        $savedCoupon->user_id = $userId;
        $savedCoupon->coupon_mobile_code = $couponMobileCode;

        $savedCoupon->save();

        return $savedCoupon;
    }
    /**
     * gets all saved coupons related to user
     *
     * @param  Request $request
     * @return mixed
     */
    public function getSavedCouponsMob(Request $request, $userId)
    {   
        $result = array();
        $visitor = VisitorCode::firstOrNew(
            [
            'visitor_code' => $request->header(config('tracking.header'))
            ]
        );
        
        $results = SavedCoupon::with(
            [
            'rate' => function ($query) {
                $query->select('description', 'price', 'id', 'coupon_code', 'facility_id','details','coupon_restrictions','disclaimer','max_stay')
                    ->with(
                        ['facility' => function ($query) {
                            $query->with('photos')->with('HoursOfOperation');
                        }]
                    );
            }
            ]
        )
            ->where('user_id', $userId)
            ->orderBy('id', 'desc')
            ->get();

        foreach ($results as $result) {
             
            $expire_on = $result->created_at->addDays(self::EXPIRE_DAYS);
            $start_from = date("g:i A", strtotime("+30 minutes",strtotime($result->created_at))) . " on " . date('M d, Y',strtotime($result->created_at));
            $result->expire_on = Carbon::parse($expire_on)->format('M d,Y');
           
            if($result->rate)
            {
                $result->coupon_mob_code = empty($result->coupon_mobile_code) ? $result->rate->generateCouponCode() : $result->coupon_mobile_code;
                
                $result->expiry_text = "Coupon active and available for use after ".$start_from;          
                $result->coupon_bar_code = $visitor->barCodeString($result->coupon_code);
        
            }
            
        }    

        return $results;    
    }

    /**
     * gets all saved coupons related to user
     *
     * @param  Request $request
     * @return mixed
     */
    public function getCouponDetails($rateId,Rate $coupon,Request $request)
    {   
        $result = array();        
        $facility_id = self::DEFAULT_ZERO;
        $coupon_id = self::DEFAULT_ZERO;
        
        $visitorCode = $request->header(config('tracking.header'));
        
        if((!isset($visitorCode)) || ($visitorCode==''))
        {
            $visitorCode = str_random(65);
            $visitor = new Visitor();
            $visitor->ip = $request->ip(); 
            $visitor->visitor_code = $visitorCode; 
            $visitor->save();
        } 
        
        $visitor = VisitorCode::firstOrNew(
            [
            'visitor_code' => $visitorCode
            ]
        );
        
        $data = $coupon->select('min_stay','max_stay','created_at', 'description', 'price', 'id', 'coupon_code', 'facility_id','details','coupon_restrictions','disclaimer')->with(
                        ['facility' => function ($query) {
                            $query->with('photos')->with('HoursOfOperation');
                        }]
                    )->where('id', $rateId)->first();
        
  
        $result['coupon_bar_code'] = $visitor->barCodeString($data->coupon_code);
        $result['coupon_mob_code'] = $data->generateCouponCode();
        
        
        if(isset($data->facility_id))
        {
            $facility_id = $data->facility_id;
        }
        
        if(isset($data->id))
        {
            $coupon_id = $data->id;
        }
        
        if(isset($visitor->id))
        {
          //saving coupon mobile Code
           $visitorMobileCode=VisitorMobileCode::where('visitor_id',$visitor->id)->first(); 
           if(count($visitorMobileCode)<=0)
           {
             $visitorMobileCode=new VisitorMobileCode();
             $visitorMobileCode->visitor_id=$visitor->id;
           }
          $visitorMobileCode->code = isset($result['coupon_mob_code'])?$result['coupon_mob_code']:'0';
          $visitorMobileCode->barcode = isset($result['coupon_bar_code'])?$result['coupon_bar_code']:'0';         
          $visitorMobileCode->facility_id = $facility_id;
          $visitorMobileCode->coupon_id = $coupon_id;
          $visitorMobileCode->save();
        }
        if($data){
            
            $expire_on = Carbon::now()->addDays(self::EXPIRE_DAYS);
            $data->expire_on = Carbon::parse($expire_on)->format('M d,Y');
            $data->expiry_text = "Coupon active and available for use after ".$data->time_in;
            $result['coupon_details'] = $data;
        }
        else{
            $result['coupon_details'] = array();
        }

        return $result;
    }
    /**
     * deletes the saved coupon
     *
     * @param  Request     $request
     * @param  SavedCoupon $coupon
     * @return SavedCoupon
     * @throws \Exception
     */
    public function deleteSavedCoupon(Request $request, SavedCoupon $coupon)
    {
        $coupon->delete();
        return $coupon;
    }
}
