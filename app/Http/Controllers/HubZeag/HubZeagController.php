<?php

namespace App\Http\Controllers\HubZeag;

use App\Classes\CommonFunctions;
use App\Exceptions\ApiGenericException;
use App\Http\Controllers\Controller;
use App\Jobs\HubParking;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Artisan;

class HubZeagController extends Controller
{
    protected $log;
    protected $carbon;
    protected $checkHubTest;
    const QUEUE_NAME_HUB_PARKING = 'hub-zeag-reservation-create';

    public function __construct(LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/integration/mapco-roc-logs')->createLogger('mapco-roc-logs');
        $this->checkHubTest = $logFactory->setPath('logs/roc/hubapi/checkTest')->createLogger('checkin_test_api');
        $this->carbon = new Carbon();
    }

    // Get all Reservation data from HubZeag by date
    public function reservationDataForParkingHubByDate(Request $request){
        $this->checkHubTest->info("Hub CheckIn Data Request: ". json_encode($request->all()));

        // Set the public and private keys
        $public_key = config('parkengage.HUB_ZEAG.public_key');
        $private_key = config('parkengage.HUB_ZEAG.private_key');

        // Capture the current date/time for the request in UTC
        $messageDate = Carbon::now('Asia/Kolkata')->format('Y-m-d\TH:i:sP');

        // Create the message representation, composed of the Verb, resource, content hash (empty), and request date/time
        $representation = "GET|/api/reservation||" . $messageDate;

        // Create the hashed message representation using HMAC SHA1 and encode it in base64
        $hmacSHA1 = base64_encode(hash_hmac('sha1', $representation, $private_key, true));

        // Set the headers
        $headers = [
            'x-message-date: ' . $messageDate,
            'x-ctr-signature: CTR ' . $public_key . ':' . $hmacSHA1,
            'Content-Type: application/json'
        ];

        $fromDate = date('Y-m-d');
        if(isset($request->fromDate)){
            $fromDate = $request->fromDate;
        }

        if(isset($request->toDate)){
            $fromDate = $fromDate."&todate=".$request->toDate;
        }

        $base_url = config('parkengage.HUB_ZEAG.base_url');
        $url = $base_url . '/api/reservation?fromdate='.$fromDate;   

        // dd($url, $headers);
        $response = CommonFunctions::makeRequest($url, "GET", $headers);
        $this->checkHubTest->info("Hub CheckIn Data from Get API: ". json_encode($response));

        return $response;
    }

    // Get all Reservation data from HubZeag by Booking Id
    public function reservationDataForParkingHubByBookingID(Request $request){
        $this->checkHubTest->info("Hub CheckIn Data Request: ". json_encode($request->all()));

        // Set the public and private keys
        $public_key = config('parkengage.HUB_ZEAG.public_key');
        $private_key = config('parkengage.HUB_ZEAG.private_key');

        // Capture the current date/time for the request in UTC
        $messageDate = Carbon::now('Asia/Kolkata')->format('Y-m-d\TH:i:sP');

        // Create the message representation, composed of the Verb, resource, content hash (empty), and request date/time
        $representation = "GET|/api/reservation||" . $messageDate;

        // Create the hashed message representation using HMAC SHA1 and encode it in base64
        $hmacSHA1 = base64_encode(hash_hmac('sha1', $representation, $private_key, true));

        // Set the headers
        $headers = [
            'x-message-date: ' . $messageDate,
            'x-ctr-signature: CTR ' . $public_key . ':' . $hmacSHA1,
            'Content-Type: application/json'
        ];

        $bookingid = "";
        if(isset($request->bookingid)){
            $bookingid = $request->bookingid;
        }else{
            throw new ApiGenericException('Invalid Parameter.');
        }

        $base_url = config('parkengage.HUB_ZEAG.base_url');
        $url = $base_url . '/api/reservation?bookingid='.$bookingid;   

        // dd($url, $headers, $bookingid);
        $response = CommonFunctions::makeRequest($url, "GET", $headers);
        $this->checkHubTest->info("Hub CheckIn Data from Get API: ". json_encode($response));

        return $response;
    }

    // Get all Reservation data from HubZeag by Booking Id
    public function checkInSyncedDataForParkingHubByBookingID(Request $request){
        $this->checkHubTest->info("Hub CheckIn Data Synced Request: ". json_encode($request->all()));

        $bookingid = "";
        if(isset($request->bookingid)){
            $bookingid = $request->bookingid;
        }else{
            throw new ApiGenericException('Invalid Parameter.');
        }
        // dd($bookingid);
        Artisan::call("hubzeag:checkin_data", ['--ticketech_code' =>$bookingid]);     

        $this->checkHubTest->info("Hub CheckIn Data Synced");
        return 'CheckIn Data Synced successfully.';
    }

    // Get all checkin by Booiking Id
    public function checkInDataForParkingHubByBookingID(Request $request){
        $this->checkHubTest->info("Hub CheckIn Data Request: ". json_encode($request->all()));

        // Set the public and private keys
        $public_key = config('parkengage.HUB_ZEAG.public_key');
        $private_key = config('parkengage.HUB_ZEAG.private_key');

        // Capture the current date/time for the request in UTC
        $messageDate = Carbon::now('Asia/Kolkata')->format('Y-m-d\TH:i:sP');

        // Create the message representation, composed of the Verb, resource, content hash (empty), and request date/time
        $representation = "GET|/api/reservationstatushistory||" . $messageDate;

        // Create the hashed message representation using HMAC SHA1 and encode it in base64
        $hmacSHA1 = base64_encode(hash_hmac('sha1', $representation, $private_key, true));

        // Set the headers
        $headers = [
            'x-message-date: ' . $messageDate,
            'x-ctr-signature: CTR ' . $public_key . ':' . $hmacSHA1,
            'Content-Type: application/json'
        ];
        $bookingid = "";
        if(isset($request->bookingid)){
            $bookingid = $request->bookingid;
        }else{
            throw new ApiGenericException('Invalid Parameter.');
        }

        $base_url = config('parkengage.HUB_ZEAG.base_url');
        $url = $base_url . '/api/reservationstatushistory?bookingid='.$bookingid;   

        $response = CommonFunctions::makeRequest($url, "GET", $headers);
        $this->checkHubTest->info("Hub CheckIn Data from Get API with Booking ID: ". json_encode($response));

        return $response;
    }

    // Get all checkin by Date
    public function checkInDataForParkingHubByDate(Request $request){
        $this->checkHubTest->info("Hub CheckIn Data Request: ". json_encode($request->all()));

        // Set the public and private keys
        $public_key = config('parkengage.HUB_ZEAG.public_key');
        $private_key = config('parkengage.HUB_ZEAG.private_key');

        // Capture the current date/time for the request in UTC
        $messageDate = Carbon::now('Asia/Kolkata')->format('Y-m-d\TH:i:sP');

        // Create the message representation, composed of the Verb, resource, content hash (empty), and request date/time
        $representation = "GET|/api/reservationstatushistory||" . $messageDate;

        // Create the hashed message representation using HMAC SHA1 and encode it in base64
        $hmacSHA1 = base64_encode(hash_hmac('sha1', $representation, $private_key, true));

        // Set the headers
        $headers = [
            'x-message-date: ' . $messageDate,
            'x-ctr-signature: CTR ' . $public_key . ':' . $hmacSHA1,
            'Content-Type: application/json'
        ];
        
        $fromDate = date('Y-m-d');
        if ( isset($request->fromDate) && isset($request->maxRecords) ){
            $fromDate = $request->fromDate;
            $maxRecords = $request->maxRecords;
        } else {
            throw new ApiGenericException('fromDate and maxRecords are mandatory');
        }

        $base_url = config('parkengage.HUB_ZEAG.base_url');
        $url = "$base_url/api/reservationstatushistory?fromdate=$fromDate&maxrecords=$maxRecords";   
        // dd($url, $headers);

        $response = CommonFunctions::makeRequest($url, "GET", $headers);
        $this->checkHubTest->info("Hub CheckIn Data from Get API from: $fromDate, ". json_encode($response));

        return $response;
    }

    // Cancell Reservation
    public function cancelReseravtionForParkingHub($reservation_id){
        Artisan::call('hubzeag:reservation_delete', ['reservation_id' => $reservation_id, "method" => "DELETE"]);
        return 'Reservation is canceled successfully.';
    }

    // Create and Update Reservation
    public function dispatchJobForParkingHub($reservation_id, $method, $is_external=false)
    {
        try {
            $this->checkHubTest->info("Reservation Request for Hub Api from job Start: " . $reservation_id);
            dispatch((new HubParking($reservation_id, $method, $is_external))->onQueue(self::QUEUE_NAME_HUB_PARKING));
            return "Job runned Successfully";
        } catch (\Throwable $th) {
            $this->checkHubTest->info("Exception in Hub Api Job:  " . $th->getMessage() . ' File ' . $th->getFile() . ' Line ' . $th->getLine());
        }
    }
   
}
