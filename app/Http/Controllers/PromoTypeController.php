<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Models\PromoType;

use App\Exceptions\ApiGenericException;

class PromoTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $promoTypes = PromoType::where('status', 1)->get();
        return $promoTypes;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, PromoType::$storeValidationRules);

        $promotype = new PromoType();

        $promotype->fill($request->only(['name', 'description']));
        $promotype->status = 1;
        $result = $promotype->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Promo Type Could Not Be Saved');
        }

        return [
            'success' => true,
            'promoType' => $promotype
        ];
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        if (!$id) {
            throw new ApiGenericException('Please Provide Valid Promo Type Id');
        }

        $promotype = PromoType::where(['id' => $id, 'status' => 1])->first();

        if (!$promotype) {
            throw new ApiGenericException('Couldn\'t Find Any Promo Type With Given Id');
        }

        return [
            'success' => true,
            'promoType' => $promotype
        ];
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int                      $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $this->validate($request, PromoType::$updateValidationRules);

        $promotype = PromoType::where('id', $request->id)->first();

        if (!$promotype) {
            throw new ApiGenericException('Couldn\'t Find Any Promo Type With Given Id');
        }

        $promotype->fill($request->only(['name', 'description']));
        $result = $promotype->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Promo Type Could Not Be Updated');
        }

        return [
            'success' => true,
            'promoType' => $promotype
        ];
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {

        $promotype = PromoType::where('id', $id)->first();

        if (!$promotype) {
            throw new ApiGenericException('Couldn\'t Find Any Promo Type With Given Id');
        }

        $result = $promotype->delete();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Promo Type Could Not Be Deleted');
        }

        return [
            'success' => true,
        ];
    }
}
