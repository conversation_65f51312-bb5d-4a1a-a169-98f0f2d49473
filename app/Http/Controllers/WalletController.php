<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Models\User;
use App\Models\Wallet;
use App\Models\LoyaltyUserAccounts;
use App\Models\LoyaltyCode;
use App\Exceptions\ApiGenericException;

use Auth;
use DB;

use App\Classes\LoyaltyProgram;

class WalletController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int                      $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }


    /**
     * Retrieve Wallet Configurations
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function getWalletConfig()
    {
        $config = Wallet::getConfig();
        if (!$config) {
            throw new ApiGenericException('Some Error Occures, Couldn\'t Revrieve Wallet Configurations.');
        }
        return [
            'wallet_config' => $config
        ];
    }

    /**
     * Get User Wallet Details along with transactions
     *
     * @param  User $user_id [description]
     * @return [type]       [description]
     */
    public function getUserWalletWithTransactions(Request $request)
    {

        $this->validate($request, User::$validateUserId);
        $user_id = $request->user_id;
        $user = User::where('id', $user_id)->first();
        
        if (!$user) {
            throw new ApiGenericException('No User Found with This User Id.');
        }

        if (!$user->referral_code) {
            // if (!$user->referral_code) {
            // generate new referral code
            $referral_code = $this->generateReferCode($user->name);
            $user->referral_code = $referral_code;
            $user->save();
        // }
            // throw new ApiGenericException('User do not have a referral code.');
        }

        Wallet::calculateWallet($user_id);
        $config = Wallet::getConfig();
        $messages = DB::table('transaction_messages')->get();

        foreach ($messages as $message) {
            $msg[$message->mode] = $message->message;
        }

        $transactions = Wallet::getUserTransactions($user_id);

        if (!$transactions) {
            $transactions = [];
        }

        $wallet = Wallet::getUserWallet($user_id);

        if (!$wallet) {
            $wallet = ['balance' => 0];
        }

        return [
            'user_id' => $user->id,
            'referral_code' => $user->referral_code,
            'wallet' => $wallet,
            'transactions' => $transactions,
            'config' => $config,
            'messages' => $msg
        ];
    }

    public function getUserWallets() {

        $user = Auth::user();

        if (!$user->referral_code) {
            // generate new referral code
            $referral_code = $this->generateReferCode($user->name);
            $user->referral_code = $referral_code;
            $user->save();
        }

        Wallet::calculateWallet($user->id);
        $config = Wallet::getConfig();

        $wallet = Wallet::getUserWallet($user->id);

        if (!$wallet) {
            $wallet = ['balance' => 0];
        }

        $loyalty_points = 0;
        $loyalty_config = LoyaltyProgram::getConfig();

        if ($user->is_loyalty) {
            $accountData = LoyaltyProgram::getUserAccounts($user->email, $user->id);
            /*if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
                $loyalty_points = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
            }*/

            if($accountData['success'] && count($accountData['data']['accounts'])>1) {
                foreach($accountData['data']['accounts'] as $account){
                  
                    $loyalty_account = LoyaltyUserAccounts::where('user_id' , '=', $user->id)->where('account_id' , '=', $account['id'])->where('account_no' , '=', $account['number'])->first();
                    if($loyalty_account && isset($account['loyalty'][0]['amount'])){
                        $loyalty_points = $account['loyalty'][0]['amount'];
                    }
                }
            }
            else{
                if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount']))
                {
                    $loyalty_points = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
                }
            }

            $account = LoyaltyUserAccounts::where('user_id' , '=', $user->id)->orderBy('id', 'DESC')->first();
            if ($account && $account->loyalty_code_id) {
                $loyalty_config['is_invitation_code'] = true;
                if($account->profile_completion_bonus >= 0) {
                    $loyalty_config['POINTS_AT_REGISTRATION'] = $account->profile_completion_bonus;
                }
                $loyalty_config['SIGN_UP_BONUS'] = $account->sign_up_bonus;
            } else {
                $loyalty_config['is_invitation_code'] = false;
            }
        }


        return [
            'user_id' => $user->id,
            'referral_code' => $user->referral_code,
            'is_loyalty' => $user->is_loyalty,
            'loyalty_points' => $loyalty_points,
            'loyalty_config' => $loyalty_config,
            'wallet' => $wallet,
            'config' => $config
        ];
    }

    public function configs() {

        $config = Wallet::getConfig();
        $wallet = ['balance' => 0];

        $loyalty_points = 0;

        $loyalty_config = LoyaltyProgram::getConfig();

        return [
            'user_id' => 0,
            'referral_code' => '',
            'is_loyalty' => 0,
            'loyalty_points' => $loyalty_points,
            'loyalty_config' => $loyalty_config,
            'wallet' => $wallet,
            'config' => $config
        ];
    }

    public function generateReferCode($name)
    {
        $code = mt_rand(10000000, 99999999);
        $name = $this->cleanData($name);
        $name_slug = strtoupper(substr($name, 0, 4));
        if (strlen($name_slug) < 4) {
            $remaining = 4 - strlen($name_slug);
            for ($i = 0; $i < $remaining; $i++) {
                $name_slug .= 'X';
            }
        }
        return $name_slug . $code;
    }
}
