<?php

namespace App\Http\Controllers;

use App\Models\CMSPage;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

use App\Http\Requests;
use App\Models\Photo;
use Intervention\Image\Facades\Image;
use Webpatser\Uuid\Uuid;

class CMSController extends Controller
{

    //
    public function index()
    {
        return CMSPage::all();
    }

    public function show(CMSPage $page)
    {
        return $page;
    }

    public function showBySlug($slug)
    {
        return CMSPage::where('slug', $slug)->first();
    }

    public function store(Request $request)
    {
        $this->validate($request, CMSPage::validParams());
        $page = CMSPage::create($request->all());
        return $page;
    }

    public function update(CMSPage $page, Request $request)
    {
        $this->validate($request, CMSPage::validParams($page->id));
        $page->update($request->all());
        return $page;
    }

    public function addPhoto(CMSPage $page, Request $request)
    {
        if (!$request->hasFile('image')) {
            return false;
        }

        $image = $request->file('image');
        $filename = Uuid::generate(4) . '_' . $image->getClientOriginalName();
        Image::make($image->getRealPath())->save(storage_path("app/" .$page::IMAGE_FOLDER.'/'. $filename));

        $photo = new Photo();
        $photo->image_name = $filename;
        $page->photos()->save($photo);

        return $photo->id;
    }

    public function destroy(CMSPage $page)
    {
        $page->delete();
        return $page;
    }
}
