<?php

namespace App\Http\Controllers;

use Auth;
use App\Classes\Inventory;
use App\Exceptions\ApiGenericException;
use App\Models\PartnerSearch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use G<PERSON>zy\LaravelMysqlSpatial\Types\Point;
use App\Http\Requests;
use App\Models\RateSearch;
use App\Models\Favourite;
use App\Models\Rate;
use App\Models\Facility;
use App\Exceptions\NotFoundException;
use App\Models\CampaignRateSearch;
use App\Models\User;
use App\Models\Reservation;
use App\Models\Ticket;
use Carbon\Carbon;
use App\Models\ParkEngage\ParkingDevice;
use App\Services\LoggerFactory;
use App\Models\GeoLocation;
use App\Http\Helpers\QueryBuilder;
use App\Models\FacilityAvailability;
use App\Models\OauthClient;
use App\Models\ParkEngage\Cruise;
use App\Models\ParkEngage\CruiseFacility;
use App\Models\ParkEngage\FrontendFacility;

use function GuzzleHttp\json_encode;

class SearchController extends Controller
{
    public  $facilityDictionary = [];
    protected $is_rm_enabled;
    public $log;
    public $request;

    const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const IS_FACILITY_CLOSED  = 1;
    const IS_FACILITY_OPEN  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE = 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO = 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR = 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "Sold Out At This Time.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->request = $request;
        $this->is_rm_enabled = 0;
        $this->log = $logFactory->setPath('logs/rate')->createLogger('search');
    }

    public function getConvertedLength($length)
    {
        $explode = explode(".", $length);
        $diffInHours = $explode[0];
        $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;

        if ($diffInMinutes > 0) {
            $diffInMints =  ($diffInMinutes * 100) / 60;
            $diffInHours = $diffInHours . '.' . $diffInMints;
        } else {
            $diffInHours = $length;
        }

        return $diffInHours;
    }

    private function searchValidationErrors(Request $request)
    {
        if (!$request->longitude) {
            return "longitude is required";
        }
        if (!$request->latitude) {
            return "latitude is required";
        }
        if (!$request->radius) {
            return "radius is required";
        }
        if (!$request->arrival_time) {
            return "arrival_time is required";
        }
        if (!$request->length_of_stay) {
            return "length_of_stay is required";
        }

        return null;
    }

    public function facilities(Request $request)
    {

        // setup our radius searching against geolocation database
        $R = 3959;  // earth's mean radius, km
        $lon = $request->longitude;
        $lat = $request->latitude;
        $rad = $request->radius;

        // first-cut bounding box (in degrees)
        $maxLat = $lat + rad2deg($rad / $R);
        $minLat = $lat - rad2deg($rad / $R);

        // compensate for degrees longitude getting smaller with increasing latitude
        $maxLon = $lon + rad2deg($rad / $R / cos(deg2rad($lat)));
        $minLon = $lon - rad2deg($rad / $R / cos(deg2rad($lat)));

        $lat = deg2rad($lat);
        $lon = deg2rad($lon);

        $condition = " where facilities.id > 0";
        //        $request->request->add(['is_oversize'=>'1','is_24_hours_open'=>'1','is_indoor_parking' =>'1','is_outdoor_parking'=>'1','is_tesla_charging'=>'1','is_generic_ev_charging'=>'1','is_motorcycle_parking'=>'1']);

        if ((isset($request->is_indoor_parking)) && ($request->is_indoor_parking == 1)) {
            $condition .= " and facilities.is_indoor_parking='1'";
        }
        if ((isset($request->is_outdoor_parking)) && ($request->is_outdoor_parking == 1)) {
            $condition .= " and facilities.is_outdoor_parking='1'";
        }
        if ((isset($request->is_tesla_charging)) && ($request->is_tesla_charging == 1)) {
            $condition .= " and facilities.is_tesla_charging='1'";
        }
        if ((isset($request->is_generic_ev_charging)) && ($request->is_generic_ev_charging == 1)) {

            $condition .= " and facilities.is_generic_ev_charging='1'";
        }
        if ((isset($request->is_motorcycle_parking)) && ($request->is_motorcycle_parking == 1)) {
            $condition .= " and facilities.is_motorcycle_parking='1'";
        }
        if ((isset($request->is_oversize)) && ($request->is_oversize == 1)) {

            $condition .= " and facilities.oversize_fee>0";
        }
        if ((isset($request->is_monthly)) && ($request->is_monthly == 1)) {

            $condition .= " and facilities.monthly_rate>0";
        }
        if ((isset($request->monthly_campaign)) && ($request->monthly_campaign == 1)) {

            $condition .= " and facilities.monthly_campaign='1'";
        }
        // Vijay : 09-10-2023 : 

        if ($this->is_rm_enabled == '0' || $this->is_rm_enabled == 0) {
            if ((isset($request->user_id)) && ($request->user_id != '')) {
                $user = User::find($request->user_id);
                if ($user) {
                    $partner = User::find($user->created_by);
                    //if rm enabled or not for partner
                    $condition .= " and facilities.owner_id=" . $user->created_by;
                }
            }
        }

        // only get cruise facilities : Only for Mobile Specific.
        if (isset($request->cruise_flag) && $request->cruise_flag) {
            $condition .= " and facilities.id IN (" . implode(',', config('parkengage.townsend.cruise_facilities')) . ")";
        }

        // dd($this->is_rm_enabled, $request->user_id, $condition);

        //        // execute the query to return distances
        //        $query = " SELECT  locatable_id as facility_id,
        //                        acos(sin( :lat )*sin(radians(latitude)) + cos( :lat )*cos(radians(latitude))*cos(radians(longitude)- :lon )) * :r AS distance
        //                FROM (
        //                        SELECT locatable_id, latitude, longitude
        //                        FROM geolocations
        //                        WHERE latitude BETWEEN :minLat AND :maxLat
        //                          AND longitude BETWEEN :minLon AND :maxLon
        //                          AND locatable_type = 'App\\\Models\\\Facility'
        //                    ) AS FirstCut
        //                    inner join facilities on facilities.id=FirstCut.locatable_id
        //                   ".$condition."
        //                HAVING distance < :rad
        //                ORDER BY distance
        //                ";

        //        $binding = ['lat' => $lat, 'lon' => $lon, 'r' => $R, 'minLat' => $minLat, 'maxLat' => $maxLat, 'minLon' => $minLon, 'maxLon' => $maxLon, 'rad' => $rad];

        /** store procedure for getting facility data **/

        $facilities = \DB::select('call facility_search("' . $lat . '", "' . $lon . '", "' . $R . '", "' . $minLat . '", "' . $maxLat . '", "' . $minLon . '", "' . $maxLon . '", "' . $rad . '", "' . $condition . '")');
        return ['facilities' => $facilities];
    }


    public function getFacilitiesByLocation(Request $request)
    {
        // Validate that latitude and longitude are provided
        $this->validate($request, [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        // Get the latitude and longitude from the request
        $latitude = $request->input('latitude');
        $longitude = $request->input('longitude');
        $isLot = $request->input('isLot');

        $this->log->info("lat/lng : {$latitude}/{$longitude}");
        // Log::info("latitude:", $latitude, "longitude", $longitude);

        // Find the geolocation where the provided point is within the bounding box
        $matchingGeolocation = GeoLocation::whereRaw("ST_Contains(bounding_box, ST_GeomFromText('POINT($longitude $latitude)', 0))")->first();

        $owner_id = 0;
        $userData = User::where('id', Auth::user()->id)->first();
        $facilityData = '';
        if ($userData->user_type == '1' || $userData->user_type == '2') {
            $facilityData = Facility::find($matchingGeolocation->locatable_id)->where('is_lot', $isLot);
        } else {
            $facilityArr = [];
            if ($userData->user_type == '3') {
                $owner_id = $userData->id;
            } else if (($userData->user_type == '4') || ($userData->user_type == '12')) {
                $owner_id = $userData->created_by;
                $facilityArr = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
            }
            //daniel code which is commented
            /*if($matchingGeolocation){
                $facilityData = Facility::where('owner_id', $owner_id)->find($matchingGeolocation->locatable_id)->where('is_lot', $isLot)->where('active', 1);
                if (($userData->user_type == '4') || ($userData->user_type == '12')) {
					$facilityData = Facility::whereIn('id', $facilityArr)->where('is_lot', $isLot)->where('active', 1)->find($matchingGeolocation->locatable_id);
				}	
			} */
            //vikrant code which fixed
            if ($matchingGeolocation) {
                if (($userData->user_type == '4') || ($userData->user_type == '12')) {
                    //dd($facilityArr, $isLot, $matchingGeolocation->locatable_id);
                    $facilityData = Facility::where('is_lot', $isLot)->where('active', 1)->find($matchingGeolocation->locatable_id);
                } else {
                    $facilityData = Facility::where('owner_id', $owner_id)->where('is_lot', $isLot)->where('active', 1)->find($matchingGeolocation->locatable_id);
                }
            }
        }

        if ($facilityData) {
            $facilityDetails[] = [
                'id' => $facilityData['id'],
                'full_name' => $facilityData['full_name'],
                'entrance_location' => $facilityData['entrance_location'],
                'between_streets' => $facilityData['between_streets'],
                'citation_format' => $facilityData['citation_format'],
                'district' => $facilityData['geolocations']['district'],
                'logo' => $facilityData['facility_brand_settings']['logo'],
                'color' => $facilityData['facility_brand_settings']['color'],
                'brand_setting_id' => $facilityData['facility_brand_settings']['brand_setting_id'],
                'state' => $facilityData['geolocations']['state'],
            ];
            $this->log->info("geofence :" . json_encode($facilityDetails));

            return $facilityDetails;
        } else return response()->json([], 201);
    }

    public function getAllFacilities(Request $request)
    {
        $facilities = Facility::with(
            'user',
            'user.userMember',
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'neighborhood',
            'hoursOfOperation',
            'faciltyBrandSetting'
        )->where('active', $request->status)->paginate(20);

        return $facilities;
    }

    public function getAllBoundingBoxes(Request $request)
    {
        $clientSecret = $request->header('X-ClientSecret');
        if (!empty($clientSecret)) {
            $secret = OauthClient::where('secret', $clientSecret)->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $partnerId = $secret->partner_id;
        }
        $geolocations = Geolocation::whereNotNull('points')->get();
        if($geolocations)
        {
            $formattedData = $geolocations->map(function ($geo) {
                $points = json_decode($geo->points, true); 
                $this->log->info("points :". json_encode($points));
                if (!is_array($points) || empty($points[0])) {
                    return null; 
                }
                $facility = Facility::where("id", $geo->locatable_id)->first();
    
                $convertedPoints = array_map(function ($point) {
                    return [
                        "longitude" => $point[0],
                        "latitude" => $point[1]
                    ];
                }, $points[0]);
    
                return [
                    "id" => $geo->locatable_id, 
                    "name" => $facility->full_name, 
                    "bounding_box" => $convertedPoints,
                    "is_active"  => $facility->active,
                    "partner_id" => $facility->owner_id
                ];
            })->filter(function ($item) use ($partnerId) {
                return $item !== null && $item['is_active'] && $item['partner_id'] == $partnerId;
            })->values(); 
    
            $this->log->info("geolocations :". json_encode($formattedData));
            return $formattedData;
        } else return response()->json([], 200);
    }

    public function ratesValidationErrors(Request $request)
    {
        if (!is_array($request->facility_ids)) {
            return "facility_ids array is required";
        }
        if (!$request->arrival_time) {
            return "arrival_time is required";
        }
        if (!$request->length_of_stay) {
            return "length_of_stay is required";
        }

        return null;
    }

    public function getRates(Request $request)
    {
        // check for validation errors
        if ($error = $this->ratesValidationErrors($request)) {
            throw new \Exception($error);
        }

        // rate search
        \Log::info("Check facilitity ID getRates ");
        \Log::info(" ============== " . json_encode($request->facility_ids));
        $rateSearch = new RateSearch($request->facility_ids, $request->arrival_time, $request->length_of_stay, $request->verbose, false, $request->is_24_hours_open);
        return ['facilities' => $rateSearch->facilities];
    }

    public function getCampaignRates(Request $request)
    {
        // check for validation errors
        if ($error = $this->ratesValidationErrors($request)) {
            throw new \Exception($error);
        }

        // rate search

        $rateSearch = new CampaignRateSearch($request->facility_ids, $request->arrival_time, $request->length_of_stay, $request->verbose, false, $request->is_24_hours_open);
        return ['facilities' => $rateSearch->facilities];
    }


    // public function getPartnerRates(Request $request)
    // {
    //     // check for validation errors
    //     if ($error = $this->ratesValidationErrors($request)) {
    //         throw new \Exception($error);
    //     }
    //     // rate search
    //     $rateSearch = new PartnerSearch($request->facility_ids, $request->arrival_time, $request->length_of_stay, $request->partner_id);
    //     return ['facilities' => $rateSearch->facilities];
    // }

    public function getPartnerRates(Request $request)
    {
        // check for validation errors
        if ($error = $this->ratesValidationErrors($request)) {
            throw new \Exception($error);
        }
        // rate search
        $rateSearch = new PartnerSearch($request->facility_ids, $request->arrival_time, $request->length_of_stay, $request->partner_id);

        // return ['facilities' => $rateSearch->facilities];
        $data = $rateSearch->facilities;
        $finalData = array();
        $facilities = array();
        $facData = Facility::with('facilityType', 'geolocations', 'photos')->whereIn('id', $request->facility_ids)->get();
        foreach ($facData as $fac) {
            $facilities[$fac->id] = $fac;
        }
        foreach ($data as $facility) {
            $newFacDataWrapper = $facility;
            $newFacDataWrapper->geolocation = $facilities[$facility->facility_id]->geolocations;
            $newFacDataWrapper->photos = $facilities[$facility->facility_id]->photos;
            $newFacDataWrapper->entrance_location = $facilities[$facility->facility_id]->entrance_location;
            $newFacDataWrapper->full_name = $facilities[$facility->facility_id]->full_name;
            $newFacDataWrapper->between_streets = $facilities[$facility->facility_id]->between_streets;
            $newFacDataWrapper->phone_number = $facilities[$facility->facility_id]->phone_number;
            if (isset($facilities[$facility->facility_id]->photos->url) && ($facilities[$facility->facility_id]->photos->url)) {
                $newFacDataWrapper->photo_url = $facilities[$facility->facility_id]->photos->url;
            } else {
                $newFacDataWrapper->photo_url = null;
            }
            $finalData[] = $newFacDataWrapper;
        }

        return ['facilities' => $finalData];
    }


    public function getEventRates(Request $request)
    {
        // check for validation errors
        if ($error = $this->ratesValidationErrors($request)) {
            throw new \Exception($error);
        }
        // rate search
        $rateSearch = new RateSearch($request->facility_ids, $request->arrival_time, $request->length_of_stay);
        return ['facilities' => $rateSearch->facilities];
    }

    // Function to inject isfavourites in Facility Rates List
    public function injectIsFavourites($rates, Request $request)
    {
        $facilityIds = $request->facility_ids;
        $facilityBonusData = Facility::select('id', 'reservation_bonus_hours', 'reservation_bonus_rate')->whereIn('id', $request->facility_ids)->get();
        $filtered = [];
        foreach ($facilityBonusData as $data) {
            $filtered[$data->id] = [
                'reservation_bonus_hours' => $data->reservation_bonus_hours,
                'reservation_bonus_rate' => $data->reservation_bonus_rate
            ];
        }

        if (isset($request->user_id)) {
            $favourites = Favourite::where('user_id', $request->user_id)->first();

            if ($favourites) {
                $user_favourites = explode(',', $favourites->facility_ids);
            }
        }

        foreach ($rates['facilities'] as $key => $rate) {
            $rates['facilities'][$key]->reservation_bonus_hours = $filtered[$rates['facilities'][$key]->facility_id]['reservation_bonus_hours'];
            $rates['facilities'][$key]->reservation_bonus_rate = $filtered[$rates['facilities'][$key]->facility_id]['reservation_bonus_rate'];
            $rates['facilities'][$key]->is_favourite = false;

            if (isset($request->user_id) && $favourites) {
                if (in_array($rates['facilities'][$key]->facility_id, $user_favourites)) {
                    $rates['facilities'][$key]->is_favourite = true;
                }
            }
        }

        return $rates;
    }

    public function updateRateInformationWithAvailibilty($request, Facility $facility)
    {
        $returnResultArr = array();

        $returnResultArr['coupon_threshold_price'] = 0;
        $returnResultArr['is_coupon_threshold_price_percentage'] = 0;
        $returnResultArr['availability'] = 0;
        $returnResultArr['is_coupon_threshold_applied'] = 0;

        $inventory = new Inventory();
        $date_time_out = Carbon::parse($request->arrival_time)->addMinutes((number_format($request->length_of_stay, 2) * 60));

        $realtimeWindow = $facility->realtime_window;
        $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;

        $timeDifference = date_diff(date_create($request->arrival_time), Carbon::now());

        $isAvailable = true;

        $thresholdAvailability = self::DEFAULT_VALUE;

        if ($isAvailable == true) {
            //check how many slots does entry and exit time occupies
            //$difference = date_diff(date_create($request->arrival_time), date_create($date_time_out));
            $difference = date_diff(date_create(date('Y-m-d', strtotime($request->arrival_time))), date_create(date('Y-m-d', strtotime(($date_time_out)))));

            if ($difference->d > 0) {
                //                $dates   = $inventory->generateArrayOfDates(
                //                '', date($request->arrival_time), date($date_time_out));

                $dates   = $inventory->generateArrayOfDates(
                    ($difference->d + self::ADD_EXTRA_DAY_COUNT),
                    date('Y-m-d H:i:s', strtotime($request->arrival_time))
                );

                $dayDifference = $difference->d;

                foreach ($dates as $key => $date) {
                    $facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();

                    if ($facilityAvailability) {
                        $inventory = json_decode($facilityAvailability->availability);

                        if ($key == 0) {
                            /**
                             * because this is the first day in the dates provided
                             * we should check from each time_slot starting
                             * from the hour provided in the api call
                             */
                            $i = date('G', strtotime($request->arrival_time));
                            while ($i <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$i})) {
                                    if ($inventory->{$i} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$i}) {
                                            $thresholdAvailability = $inventory->{$i};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$i};
                                    }
                                }
                                $i++;
                            }
                        } elseif ($key == $dayDifference) {
                            $i = date('G', strtotime($date_time_out));
                            $minutes = date('i', strtotime($date_time_out));
                            if ($minutes >= 30) {
                                $i++;
                            }
                            /**
                             * because this is the last day in the dates provided
                             * we should check from each time_slot starting
                             * till the hour provided in the api call
                             */
                            $j = 0;
                            while ($j < $i) {
                                if (isset($inventory->{$j})) {
                                    if ($inventory->{$j} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$j}) {
                                            $thresholdAvailability = $inventory->{$j};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$j};
                                    }
                                }
                                $j++;
                            }
                        } else {
                            /**
                             * because this could be any day except first and last in
                             * the dates provided we should check from whole day
                             */
                            $k = 0;
                            while ($k <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$k})) {
                                    if ($inventory->{$k} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$k}) {
                                            $thresholdAvailability = $inventory->{$k};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$k};
                                    }
                                }
                                $k++;
                            }
                        }
                    }
                }
            } else {
                $startingHour = date('G', strtotime($request->arrival_time));
                $endingHour   = date('G', strtotime($date_time_out));
                $facilityAvailability     = FacilityAvailability::where(
                    ['facility_id' => $facility->id, 'date' => date('Y-m-d', strtotime($request->arrival_time))]
                )->first();

                if ($facilityAvailability) {
                    $availability = json_decode($facilityAvailability->availability, true);

                    while ($startingHour <= $endingHour) {
                        if (isset($availability[$startingHour])) {
                            if (($availability[$startingHour] < 1)) {
                                $isAvailable = false;
                            }
                            if ($thresholdAvailability > 0) {
                                if ($thresholdAvailability > $availability[$startingHour]) {
                                    $thresholdAvailability = $availability[$startingHour];
                                }
                            } else {
                                $thresholdAvailability = $availability[$startingHour];
                            }
                        }
                        $startingHour++;
                    }
                }
            }

            if ($thresholdAvailability < self::DEFAULT_VALUE) {
                $thresholdAvailability = self::DEFAULT_VALUE;
            }

            if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {

                $dateIn = date('Y-m-d', strtotime($request->arrival_time));
                $facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();

                if ($facilityAvailability) {
                    //$availabilities = json_decode($facilityAvailability->availability, true);

                    if ($thresholdAvailability >= 0) {

                        $couponThresholdsNew = $facility->facilityCouponThreshold;
                        $couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');

                        $thresholds       = array();
                        if ($couponThresholdsNew) {
                            $thresholdCounter = self::DEFAULT_VALUE;
                            foreach ($couponThresholdsNew as $couponThreshold) {

                                if ($couponThreshold->uptick_type !== 'deleted') {
                                    $thresholds[$thresholdCounter] =
                                        ['threshold' => $couponThreshold->threshold, 'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
                                    $thresholdCounter++;
                                }
                            }
                        }
                        $thresholdPrice = 0;
                        $currentAvailability = $thresholdAvailability;
                        foreach ($thresholds as $key => $threshold) {
                            if ($thresholdAvailability <= $threshold['threshold']) {
                                if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
                                    if ($threshold['uptick_type'] == 'price') {
                                        $thresholdPrice = $threshold['uptick'];

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    } else if ($threshold['uptick_type'] == 'percentage') {
                                        $thresholdPrice =  $threshold['uptick'];
                                        $returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG;

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    }
                                }
                            }
                        }
                        $returnResultArr['coupon_threshold_price'] = $thresholdPrice;

                        $returnResultArr['availability'] = $currentAvailability;
                    }
                }
            }
        }

        //check realtime availability
        /*if ($timeDifference->h <= $realtimeWindow) {

            if($facility->realtime_minimum_availability > $returnResultArr['availability']){
              $returnResultArr['coupon_threshold_price'] = 0;        
              $returnResultArr['is_coupon_threshold_price_percentage'] = 0;        
              $returnResultArr['availability'] = 0;
              $returnResultArr['is_coupon_threshold_applied'] = 0;
            }
        }*/
        return $returnResultArr;
    }

    public function facilitiesRates(Request $request)
    {
        ini_set('max_execution_time', 0);
        if (isset($request->user_id)) {
            $user = User::where('id', $request->user_id)->first();
            $this->log->info("Check User : {$request->user_id} ");
            if (!$user) {
                throw new ApiGenericException("Unauthorized", 401);
            }
        }
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $partner = User::find($user->created_by);
            if (!$partner) {
                $partner = User::find($secret->partner_id);
            }
            $this->is_rm_enabled  = $partner->is_rm_enabled;
            // \Log::info("IS RM ENABLED " . json_encode($partner));
            \Log::info("IS RM ENABLED : " . $this->is_rm_enabled);
        }
        // try {
        $this->prepareSearch($request);

        \Log::info("facilitiesRates facilityDictionary : " . json_encode($this->facilityDictionary));
        $request->request->add(['facility_ids' => array_keys($this->facilityDictionary)]);

        if ((isset($request->monthly_campaign)) && ($request->monthly_campaign == 1)) {
            $rates = $this->getCampaignRates($request);
        } else {
            $rates = $this->getRates($request);
            \Log::info("Check facilitity ID facilitiesRates ");
            // \Log::info("RATE  " . json_encode($rates));
            if ($rates['facilities']) {
                foreach ($rates['facilities'] as $key => $rate) {
                    // $facility = Facility::find($rate->facility_id);
                    $facility = Facility::with('FacilityPaymentDetails')->find($rate->facility_id);

                    $facilityrate = Rate::select('max_stay', 'price')->where('facility_id', $rate->facility_id)->where('active', 1)->get();
                    foreach ($facilityrate as $value) {
                        $rates['facility_rates'][] = '$' . $value->price . ' for ' . $value->max_stay . ' hours';
                    }

                    if ($facility) {

                        if (isset($facility->FacilityPaymentDetails) && !empty($facility->FacilityPaymentDetails)) {
                            $rate->payment_type = ($facility->FacilityPaymentDetails->facilityPaymentType->payment_type);
                        } else {
                            $rate->payment_type = '';
                        }

                        $rate->isFacilityClosed = self::IS_FACILITY_CLOSED;
                        if ($rate->price >= '0') {
                            $rate->isFacilityClosed = self::IS_FACILITY_OPEN;
                        }
                        if (isset($rate->todayHoursOfOperation) && $rate->todayHoursOfOperation == '0' && $rate->is_24hour_open == '0') {
                            $rate->isFacilityClosed = self::IS_FACILITY_CLOSED;
                        }
                        $rate->is_gated_facility = $facility->is_gated_facility;

                        $rate->garage_code = $facility->garage_code;
                        $rate->is_lot = $facility->is_lot;
                        $rate->is_user_qrcode_enabled = $facility->is_user_qrcode_enabled;
                        $rate->facility_booking_type = $facility->facility_booking_type;
                        $parkingDevice = ParkingDevice::where("facility_id", $facility->id)->first();
                        if (isset($request->cruise_flag) && $request->cruise_flag) {
                            $cruiseFacility = CruiseFacility::where("facility_id", $facility->id)->pluck('cruise_id');
                            $cruises = Cruise::whereIn('id', $cruiseFacility)->first();

                            $rate->cruise = $cruises;  // Mobile : Vijay 03-10-2024  
                        }


                        if ($parkingDevice) {
                            $rate->is_device_facility = '1';
                        } else {
                            $rate->is_device_facility = '0';
                        }
                        if ($request->user_id != '') {
                            $reservations = Reservation::where("user_id", $request->user_id)->where("facility_id", $rate->facility_id)->orderBy("id", "DESC")->get();

                            if (count($reservations) <= 0) {
                                $rate->is_reservation_found = '0';
                            } else {
                                $now = date("Y-m-d H:i:s");
                                foreach ($reservations as $reservation) {
                                    $entryTime = $reservation->start_timestamp;
                                    $exitTime = Carbon::parse($reservation->start_timestamp)->addMinutes(($reservation->length * 60));
                                    if (strtotime($entryTime) <= strtotime($now) && strtotime($exitTime) >= strtotime($now)) {
                                        $rate->is_reservation_found = '1';
                                        $rate->reservation_code = $reservation->ticketech_guid;
                                        $rate->iq_code = $reservation->ticketech_code;

                                        $ticket = Ticket::where("reservation_id", $reservation->id)->orderBy("id", "DESC")->first();
                                        if (!$ticket) {
                                            $rate->is_reservation_checkin_found = '0';
                                        } else {
                                            $rate->is_reservation_checkin_found = '1';
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    $rateData = $this->updateRateInformationWithAvailibilty($request, $facility);
                    if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
                        $rate->availability = $rateData['availability'];
                    }
                }
            }
        }
        // } catch (\Exception $e) {
        //     $this->log->error("Error message" . $e);
        //     throw new \Exception("There is something wrong! Please try again letter." . $e);
        //     //dd($e);
        // }
        if (isset($rates['facility_rates'])) {
            $rates['facility_rates'] = implode(', ', $rates['facility_rates']);
        }

        return $this->injectIsFavourites($this->prepareReturn($rates), $request);
    }

    public function partnerRates(Request $request)
    {
        if (!$request->partner_id) {
            throw new \Exception('Partner Id is required');
        }

        $this->customRates = Rate::where('partner_id', $request->partner_id)->get()->all();

        if (!$this->customRates) {
            throw new NotFoundException('No partner rates found.');
        }

        $this->prepareSearch($request);

        if ($this->customRates) {
            foreach ($this->customRates as $rate) {
                if (array_key_exists($rate->facility_id, $this->facilityDictionary)) {
                    $this->customFacilities[$rate->facility_id] = $this->facilityDictionary[$rate->facility_id];
                }
            }
        }
        $request->request->add(['facility_ids' => array_keys($this->customFacilities)]);

        $rates = $this->getPartnerRates($request);

        return $this->prepareReturn($rates);
    }

    public function eventRates(Request $request)
    {
        if (!$request->event_id) {
            throw new \Exception('Event Id is required');
        }

        $eventFacilities = Facility::whereHas(
            'events',
            function ($query) use ($request) {
                $query->where('id', '=', $request->event_id);
            }
        )->get();

        if (!$eventFacilities) {
            throw new \Exception('No event rates found.');
        }

        $this->prepareSearch($request);

        foreach ($eventFacilities as $facility) {
            if (array_key_exists($facility->id, $this->facilityDictionary)) {
                $this->customFacilities[$facility->id] = $this->facilityDictionary[$facility->id];
            }
        }

        $request->request->add(['facility_ids' => array_keys($this->customFacilities)]);

        $rates = $this->getEventRates($request);

        return $this->prepareReturn($rates);
    }

    public function prepareSearch($request)
    {
        // check for validation errors
        if ($error = $this->searchValidationErrors($request)) {
            throw new \Exception($error);
        }

        // search for facilities
        $facilities = $this->facilities($request);


        // if the result is a response that means there was a validation error
        if (is_a($facilities, "Illuminate\\Http\\Response")) {
            return $facilities;
        }

        // create a facility lookup object so we only have to loop the array one time
        foreach ($facilities['facilities'] as $key => $facility) {
            // filter data for active facility 
            $facilityData = Facility::find($facility->facility_id);
            if ($this->getFrontendFacility($facilityData)) {
                \Log::info("Check facilitity ID 3333333333333333333 ELSE ");
                $this->facilityDictionary[$facility->facility_id] = $facility;
            }
            $this->facilityDictionary[$facility->facility_id] = $facility;
        }
        \Log::info("Check facilitity ID CLOSE ");
    }

    public function prepareReturn($rates)
    {
        // if the result is a response that means there was a validation error
        if (is_a($rates, "Illuminate\\Http\\Response")) {
            return $rates;
        }

        // combine the facility search with the rate search for a unified response
        if (isset($rates['facilities'])) {
            foreach ($rates['facilities'] as $key => $rate) {
                if ($this->facilityDictionary[$rate->facility_id]) {
                    $rates['facilities'][$key]->distance = $this->facilityDictionary[$rate->facility_id]->distance;
                }
            }
        }

        // return our search result
        return $rates;
    }

    function getFrontendFacility($facility)
    {
        if ($facility) {
            $FrontendFacility = FrontendFacility::where(['active' => '1', 'facility_id' => $facility->id])->get();
            if ($FrontendFacility->count() > 0) {
                return true;
            } else {
                return true;
            }
        }
        return false;
    }

    function getCruiseDetails($facility)
    {
        if ($facility) {
            $FrontendFacility = FrontendFacility::where(['active' => '1', 'facility_id' => $facility->id])->get();
            if ($FrontendFacility->count() > 0) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }
}
