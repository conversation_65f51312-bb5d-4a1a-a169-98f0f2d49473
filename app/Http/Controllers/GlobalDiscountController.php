<?php
namespace App\Http\Controllers;
use App\Exceptions\NotFoundException;
use App\Models\GlobalDiscount;
use App\Http\Requests;
use Illuminate\Http\Request;
use Storage;
use App\Exceptions\ApiGenericException;
class GlobalDiscountController extends Controller
{

   /**
     * @param Request $request
     * @return string
     * @throws ApiGenericException
     */
    public function globalDiscounts(Request $request)
    {
        try {
            $result = GlobalDiscount::first();
            if (!$result) {
                GlobalDiscount::create($request->all());
            } else {
                $result->percentage_off_discounts = $request->percentage_off_discounts;
                $result->dollar_off_discounts     = $request->dollar_off_discounts;
                $result->save();
            }
            return 'success';
        } catch(\Exception $e) {
            throw  new ApiGenericException($e->getMessage(), 500);
        }
    }

    /**
     * @return mixed
     * @throws ApiGenericException
     */
    public function getGlobalDiscounts()
    {
        $result = GlobalDiscount::first();
        if($result){
            return $result;
        }

        throw new ApiGenericException('record not found', 404);
    }
    
}
