<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Exceptions\NotFoundException;

use Carbon\Carbon;

use App\Http\Requests;

use App\Classes\Elimiwait;

use App\Models\ElimiwaitRequest;
use App\Models\ElimiwaitAccount;
use App\Models\MonthlyParkingUser;

class ElimiwaitRequestController extends Controller
{
    public function request(Request $request, ElimiwaitAccount $elimiwaitAccount)
    {
        return $elimiwaitAccount->requestCar($request->pickUpTime);
    }

    public function updateRequests(Request $request, ElimiwaitAccount $elimiwaitAccount)
    {
        $elimiwait = new Elimiwait($elimiwaitAccount);

        return collect($elimiwait->getActiveRequests())->map(
            function ($request) use ($elimiwaitAccount) {
                $localRequest = Request::where('request_id', $request['RequestId'])->first();
                $localRequest->pick_up_time = $request['PickupRequestTime'];
                $localRequest->save();
                return $localRequest;
            }
        );
    }

    // This controller method gets pinged by Elimiwai<PERSON> when requests are updated
    // Called by Elimiwait when they confirm a request
    public function handleElimiwaitUpdate(Request $request)
    {
        $elimiwaitRequest = ElimiwaitRequest::where('request_id', $request->RequestId)->first();

        if (!$elimiwaitRequest) {
            throw new NotFoundException('No request found for request ID');
        }

        // Make sure time is in new york time zone.
        $updatedPickupRequestTime = Carbon::parse($request->PickupRequestTime)->tz(config('app.timezone'));

        // Make sure time matches our records, otherwise update it.
        if ($updatedPickupRequestTime->toDateTimeString() !== $elimiwaitRequest->pick_up_time->toDateTimeString()) {
            $elimiwaitRequest->pick_up_time = $updatedPickupRequestTime->toDateTimeString();
            $elimiwaitRequest->approved_on = null;
        }

        // Handle cancellation
        if ($request->Cancelled) {
            $elimiwaitRequest->cancelled_on = Carbon::now();

            $elimiwaitRequest->approved_on = $elimiwaitRequest->Confirmed
                ? Carbon::now()
                : null;
        }

        // Handle confirmation
        if ($request->Confirmed) {
            $elimiwaitRequest->approved_on = Carbon::now();
        }

        $elimiwaitRequest->save();

        return $elimiwaitRequest;
    }

    public function index()
    {
        return ElimiwaitRequest::all();
    }

    public function show(MonthlyParkingUser $monthlyParkingUser)
    {
        $elimiwaitRequest = ElimiwaitRequest::where('mp_user', $monthlyParkingUser->id);

        if (!$elimiwaitRequest) {
            throw new NotFoundException('No request found for monthly parking user');
        }

        return $elimiwaitRequest;
    }
}
