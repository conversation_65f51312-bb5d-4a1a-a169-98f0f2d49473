<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Artisan;
use DB;
use Response;
use File;


use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;

use Intervention\Image\Facades\Image;

use Illuminate\Support\Facades\Storage;


class UploadInvoiceController extends Controller
{
   
	
	public function uploadInvoice(Request $request){
		
		try {
				if (!$request->hasFile('TXT')) {
					return false;
				}

				$TXTFILE = $request->file('TXT');
				//$fileName=$TXTFILE->getClientOriginalName();
				$fileName = 'bills.txt';
				$TXTFILE->getRealPath();
				$image = @file_get_contents($TXTFILE->getRealPath());
				
				Storage::disk('localimport')->put($fileName, File::get($TXTFILE->getRealPath()));

				$data['filename'] = $TXTFILE->getClientOriginalName();
				$data['status'] = true;
				return $data;
				
		}
		catch (\Exception $e) {
			return $e->getMessage();
		}
        
		//echo'<br>test';exit;
		
		
	}
}
