<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RateType;
use App\Models\Facility;
use App\Http\Requests;

class RateTypeController extends Controller
{
    public function index($facility_id = '')
    {
    	if($facility_id != ''){
    	$facility = Facility::where('id', $facility_id)->first();
    	$partner = \DB::table('partner_rate_types')->where('partner_id', $facility->owner_id)->get();
    	$rateTypeId = [];
    	foreach ($partner as $key => $value) {
    		$rateTypeId[] = $value->rate_type_id;
    	}
    	//PartnerRateType::with('RateType')->where('partner_id', $facility->owner_id)->get();
    	return RateType::whereIN('id', $rateTypeId)->get();
    	}else{
    		return RateType::all();	
    	}

    }
}
