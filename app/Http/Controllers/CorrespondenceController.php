<?php

namespace App\Http\Controllers;

use App\Models\Correspondence;
use Illuminate\Http\Request;
use App\Services\Mailers\UserMailer;
use App\Models\User;

class CorrespondenceController extends Controller
{

    public function index()
    {
        return Correspondence::all();
    }

    public function show(Correspondence $correspondence)
    {
        return $correspondence;
    }

    public function store(Request $request)
    {
        $this->validate($request, Correspondence::validParams(), Correspondence::validMessages());
        $correspondence = Correspondence::create(
            $request->only(
                [
                'user_id',
                'name',
                'email',
                'phone',
                'city',
                'state',
                'comments'
                ]
            )
        );
        $this->sendStatusEmail($correspondence);
        return $correspondence;
    }

    private function sendStatusEmail($correspondence)
    {
        $mailer = new UserMailer();
        $timestamp = date('Y-m-d H:i:s', time());
        $account = User::where('id', $correspondence->user_id)->first();
        $mailData = [
            'timestamp' => $timestamp,
            'correspondence' => $correspondence,
            'account' => $account,
            'mp_accounts' => $account ? $account->monthlyParkingAccounts()->get() : null,
            'reservations' => $account ? $account->reservations()->get() : null
        ];

        //$mailer->sendMailTo(config('icon.customer_service_email'), 'Contact Us: User Correspondence', $mailData, 'email.correspondence');
		$mailer->sendMailTo(config('icon.customer_service_email'), 'Contact Us: User Correspondence', $mailData, 'email.correspondence');
		$mailer->sendMailTo($correspondence->email, 'Contact Form Received', $mailData, 'email.clientcorrespondence');
    }
}
