<?php

namespace App\Http\Controllers;

use App\Http\Requests;

use Illuminate\Http\Request;
use App\Exceptions\NotFoundException;
use App\Services\LoggerFactory;
use App\Exceptions\ApiGenericException;
use App\Models\ParkEngage\MobileDeviceVersion;
use App\Models\OauthClient;
use App\Models\ParkEngage\BrandSetting;
use Auth;

class MobileDeviceVersionController extends Controller
{
    protected $log;
    public function __construct()
    {
        $this->log = (new LoggerFactory)->setPath('logs/updatebuilVersion')->createLogger('builversion');
    }

    public function index(Request $request, $device, $appname = null)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        // $partner_id = $secret->partner_id;
        $result = [];
        $query = MobileDeviceVersion::query();
        if (strtolower($device) == 'android') {
            $query = $query->select('title', 'message', 'android_version', 'android_status', 'android_soft_status', 'app_name', 'app_theme_color', 'android_app_download_url', 'latitute', 'longitude', 'location', 'sub_title', 'left_latitude', 'left_longitude', 'right_latitude', 'right_longitude', 'google_pay_url', 'apple_pay_url');
        } elseif (strtolower($device) == 'ios') {
            $query = $query->select('title', 'message', 'ios_version', 'ios_status', 'ios_soft_status', 'app_name', 'app_theme_color', 'ios_app_download_url', 'latitute', 'longitude', 'location', 'sub_title', 'left_latitude', 'left_longitude', 'right_latitude', 'right_longitude', 'google_pay_url', 'apple_pay_url');
        } elseif (strtolower($device) == 'web') {
            $query = $query->select('title', 'message', 'web_version', 'web_status', 'latitute', 'longitude', 'location', 'sub_title', 'left_latitude', 'left_longitude', 'right_latitude', 'right_longitude', 'google_pay_url', 'apple_pay_url');
        } else {
            throw new NotFoundException('Something worng!');
        }
        if (isset($appname)) {
            $query = $query->where(['app_name' => $appname]);
        } else {
            $partner_id = $secret->partner_id;
            $settings = BrandSetting::where('user_id', $partner_id)->first();
            $query = $query->where('partner_id', $partner_id);
        }
        $data = $query->first();
        if ($data) {
            if (!isset($appname))
                $data->branding_color  = isset($settings->color) ? $settings->color : NULL;
        }
        return $result = ['status' => 'true', 'data' => $data];
    }

    public function updateVersion($device)
    {
        try {

            $deviceVersion = MobileDeviceVersion::where('id', '1')->first();
            // return $result = ['status' => 'true', 'data' =>$deviceVersion];
            if (strtolower($device) == 'android') {
                $last_version = $deviceVersion->android_version;
                $new_version = intval($last_version) + intval(1);
                $affectedRows = MobileDeviceVersion::where('id', '=', 1)->update(['android_version' => $new_version]);
            } elseif (strtolower($device) == 'ios') {
                $last_version = $deviceVersion->ios_version;
                $new_version = floatval($last_version) + floatval(0.1);
                $affectedRows = MobileDeviceVersion::where('id', '=', 1)->update(['ios_version' => $new_version]);
            } elseif (strtolower($device) == 'web') {
                $last_version = $deviceVersion->web_version;
                $new_version = floatval($last_version) + floatval(0.1);
                $affectedRows = MobileDeviceVersion::where('id', '=', 1)->update(['web_version' => $new_version]);
            } else {
                throw new NotFoundException('Something worng!');
            }
            $deviceVersion = MobileDeviceVersion::where('id', '1')->first();
            $this->log->error('Old build Version is ' . $last_version . ' New Version is ' . $new_version . ' For Device ' . $device);

            if ($affectedRows)
                return $result = ['status' => 'true', 'data' => $deviceVersion, 'msg' => 'Device Version Update Successfully'];
            else
                return $result = ['status' => 'false', 'msg' => 'Build Version Update fail, Check With BE Team'];

            //code...
        } catch (Exception $e) {
            $this->log->error('Error in update build Version ' . $e->getMessage());
            throw new ApiGenericException('The system is currently not available. Please try again later.');
        }
    }


    public function getBaseUrl(Request $request)
    {
        //return $request->app_key;

        if ($request->app_name) {
            $result = [];
            $data = MobileDeviceVersion::select('base_url', 'client_id', 'client_secret', 'is_android_notification', 'is_ios_notification')->where('app_name', $request->app_name)->first();

            if ($data) {
                return $data;
            }
            throw new ApiGenericException('no data found');
        } else {
            throw new ApiGenericException('no data found');
        }
    }
}
