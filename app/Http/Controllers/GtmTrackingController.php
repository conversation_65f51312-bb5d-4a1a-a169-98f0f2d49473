<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;

use App\Models\GtmTracking;
use App\Models\Visitor\Visitor;

/**
 * Handling all requests related to GTM tracking
 */
class GtmTrackingController extends Controller
{
    
    protected $visitor;
    public function __construct(Request $request)
    {
        // get visitor code form request header
        $visitorCode = $request->header(config('headers.visitor'));

        if ($visitorCode) {
            $this->visitor = Visitor::where('visitor_code', $visitorCode)->first();
        }
    }

    /**
     * store data in db for gtm tracking details
     * 
     * @param $request Request
     */
    public function store(Request $request)
    {
        $this->validate(
            $request, [
            'iq_code' => 'max:50',
            'coupon_code' => 'max:50',
            'bar_code' => 'max:50',
            // 'event_type' => 'in:0, 1', //Event type 0 For Coupon and 1 for Reservations, Default set 0.
            ]
        );

        $visitorId = null;

        if ($this->visitor) {
            $visitorId = $this->visitor->id;
        }

        try {

            // insert new record for gtm tracking
            $model = new GtmTracking;
            $model->visitor_id = $visitorId;
            $model->iq_code = $request->iq_code;
            $model->coupon_code = $request->coupon_code;
            $model->bar_code = $request->bar_code;
            $model->event_type = isset($request->event_type) ? $request->event_type : 0 ;

            return ['success' => $model->save()];
        }
        catch (Exception $e) {
            throw new ApiGenericException("Could not add request, " . $e->getMessage());
        }
    }  
}
