<?php

namespace App\Http\Controllers;

use App\Exceptions\NotFoundException;
use Illuminate\Http\Request;

use App\Http\Requests;

use App\Models\LegacyRedirect;

class RedirectController extends Controller
{

    public function getByPath(Request $request)
    {
        $redirect = LegacyRedirect::where('old_url', $request->old_url)->first();

        if (!$redirect) {
            throw new NotFoundException('No legacy redirect with that path.');
        }

        return ['new_path' => $redirect->new_url];
    }
}
