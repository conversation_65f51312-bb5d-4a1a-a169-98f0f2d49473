<?php

namespace App\Http\Controllers;

use Hash;
use Auth;
use DB;
use Phone;
use Authorizer;

use App\Classes\AuthorizeNet\Cim;


use App\Models\Facility;
use App\Models\RateSearch;
use App\Models\Rate;
use App\Models\Clients;

use App\Http\Helpers\QueryBuilder;

use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

use Illuminate\Http\Request;
use App\Http\Requests;

//namespace League\OAuth2\Server;

use League\Event\Emitter;
use League\OAuth2\Server\Storage\AccessTokenInterface;
use League\OAuth2\Server\Storage\AuthCodeInterface;
use League\OAuth2\Server\Storage\ClientInterface;
use League\OAuth2\Server\Storage\MacTokenInterface;
use League\OAuth2\Server\Storage\RefreshTokenInterface;
use League\OAuth2\Server\Storage\ScopeInterface;
use League\OAuth2\Server\Storage\SessionInterface;
use League\OAuth2\Server\TokenType\TokenTypeInterface;

use League\OAuth2\Server\Entity\AccessTokenEntity;
use League\OAuth2\Server\Entity\ClientEntity;
use League\OAuth2\Server\Entity\RefreshTokenEntity;
use League\OAuth2\Server\Entity\SessionEntity;
use League\OAuth2\Server\Event;
use League\OAuth2\Server\Exception;
use League\OAuth2\Server\Util\SecureKey;
//use Vendor\Symfony\Component\HttpFoundation\Request;

class ClientsController extends Controller
{
	 /**
     * Grant identifier
     *
     * @var string
     */
    protected $identifier = 'clients';

    /**
     * Response type
     *
     * @var string
     */
    protected $responseType = null;

    /**
     * AuthServer instance
     *
     * @var \League\OAuth2\Server\AuthorizationServer
     */
    protected $server = null;

     /**
     * {@inheritdoc}
     */
    public function getIdentifier()
    {
        return $this->identifier;
    }

  

   
   private $facilityDictionary = [];

    // Paginated list of all clients
    public function index(Request $request)
    {
       
    }
	
	private function searchValidationErrors(Request $request)
    {
        if (!$request->longitude) {
            return "longitude is required";
        }
        if (!$request->latitude) {
            return "latitude is required";
        }
        if (!$request->radius) {
            return "radius is required";
        }
        if (!$request->arrival_time) {
            return "arrival_time is required";
        }
        if (!$request->length_of_stay) {
            return "length_of_stay is required";
        }

        return null;
    }

	
	public function ratesValidationErrors(Request $request)
    {
        if (!is_array($request->facility_ids)) {
            return "facility_ids array is required";
        }
        if (!$request->arrival_time) {
            return "arrival_time is required";
        }
        if (!$request->length_of_stay) {
            return "length_of_stay is required";
        }

        return null;
    }
	
	//https://test-api.iconparkingsystems.com/{CompanyId}/search/facilities-rates
	 public function facilitiesRates(Request $request)
		{
			
			
			$CompanyRes = DB::table('oauth_clients')->where('id', $request->client_id)->first();
			if (!$CompanyRes) {
				throw new NotFoundException('Error Occurred, No Client Found With This company code.');
			}
			
			
			/*{
	  "latitude": 40.75901100000001,
	  "longitude": -73.98447220000003,
	  "radius": "2",
	  "length_of_stay": 2,
	  "arrival_time": "2018-01-1811:30:24",
	  "verbose": "true"
	}*/

	// Get the required params
		   $clientId = $request->request->get('client_id', $request->getUser());
		   if (is_null($clientId)) {
				throw new Exception\InvalidRequestException('client_id');
			}
			
			$clientSecret = $request->request->get('client_secret',
				$request->getPassword());
				
			if (is_null($clientSecret)) {
				throw new Exception\InvalidRequestException('client_secret');
			}
			
			
			$CheckClientQr = DB::table('oauth_clients')->where('id', $request->client_id)->where('secret', $clientSecret)->first();
			if (!$CheckClientQr) {
				throw new NotFoundException('Error Occurred, No Client Found With This company code.');
			}	

			$created_at=date('Y-m-d H:i:s');

			$SessionId = DB::table('oauth_sessions')->insertGetId(
						['client_id' => $clientId, 'owner_type' => 'client', 'owner_id' => $clientId, 'created_at' => $created_at]
				);		


			$tokid=SecureKey::generate();
			$expire_time=time()+3600;
			
		$resultInsertQuery = DB::insert('insert into oauth_access_tokens (id,session_id, expire_time,created_at) values (?, ?, ?, ?)', ["$tokid","$SessionId", "$expire_time", "$created_at"]);

		   $session = array('access_token'=>$tokid,'token_type'=>'Bearer','expires_in'=>'3600');
		   
		  // print_r($session);exit;
		   
		   $this->prepareSearch($request);
			
			$request->request->add(['facility_ids' => array_keys($this->facilityDictionary)]);
			$rates = $this->getRates($request);
			
			return array_merge($session, $this->injectIsFavourites($this->prepareReturn($rates), $request));
			
			//return 1;
		}
	 public function prepareReturn($rates)
		{
			
			// if the result is a response that means there was a validation error
			if (is_a($rates, "Illuminate\\Http\\Response")) {
				return $rates;
			}

			// combine the facility search with the rate search for a unified response
			if (isset($rates['facilities'])) {
				foreach ($rates['facilities'] as $key => $rate) {
					if ($this->facilityDictionary[$rate->facility_id]) {
						$rates['facilities'][$key]->distance = $this->facilityDictionary[$rate->facility_id]->distance;
					}
				}
			}

			// return our search result
			return $rates;
		}

	 public function getRates(Request $request)
    {
        // check for validation errors
        if ($error = $this->ratesValidationErrors($request)) {
            throw new \Exception($error);
        }
        // rate search
        $rateSearch = new RateSearch($request->facility_ids, $request->arrival_time, $request->length_of_stay, $request->verbose);
        return ['facilities' => $rateSearch->facilities];
    }
	
	public function facilities(Request $request)
    {
        // setup our radius searching against geolocation database
        $R = 3959;  // earth's mean radius, km
        $lon = $request->longitude;
        $lat = $request->latitude;
        $rad = $request->radius;

        // first-cut bounding box (in degrees)
        $maxLat = $lat + rad2deg($rad / $R);
        $minLat = $lat - rad2deg($rad / $R);

        // compensate for degrees longitude getting smaller with increasing latitude
        $maxLon = $lon + rad2deg($rad / $R / cos(deg2rad($lat)));
        $minLon = $lon - rad2deg($rad / $R / cos(deg2rad($lat)));

        $lat = deg2rad($lat);
        $lon = deg2rad($lon);

        // execute the query to return distances
        $query = "
                SELECT  locatable_id as facility_id,
                        acos(sin( :lat )*sin(radians(latitude)) + cos( :lat )*cos(radians(latitude))*cos(radians(longitude)- :lon )) * :r AS distance
                FROM (
                        SELECT locatable_id, latitude, longitude
                        FROM geolocations
                        WHERE latitude BETWEEN :minLat AND :maxLat
                          AND longitude BETWEEN :minLon AND :maxLon
                          AND locatable_type = 'App\\\Models\\\Facility'
                    ) AS FirstCut
                HAVING distance < :rad
                ORDER BY distance
                ";
        $binding = ['lat' => $lat, 'lon' => $lon, 'r' => $R, 'minLat' => $minLat, 'maxLat' => $maxLat, 'minLon' => $minLon, 'maxLon' => $maxLon, 'rad' => $rad];

        $facilities = \DB::select($query, $binding);

        return ['facilities' => $facilities];
    }
	
	 public function prepareSearch($request)
    {
		
        // check for validation errors
        if ($error = $this->searchValidationErrors($request)) {
            throw new \Exception($error);
        }

        // search for facilities
        $facilities = $this->facilities($request);


        // if the result is a response that means there was a validation error
        if (is_a($facilities, "Illuminate\\Http\\Response")) {
            return $facilities;
        }

        // create a facility lookup object so we only have to loop the array one time
        foreach ($facilities['facilities'] as $key => $facility) {
            $this->facilityDictionary[$facility->facility_id] = $facility;
        }
    }
	
	 // Function to inject isfavourites in Facility Rates List
    public function injectIsFavourites($rates, Request $request)
    {
        $facilityIds = $request->facility_ids;
        $facilityBonusData = Facility::select('id', 'reservation_bonus_hours', 'reservation_bonus_rate')->whereIn('id', $request->facility_ids)->get();
        $filtered = [];
        foreach ($facilityBonusData as $data) {
            $filtered[$data->id] = [
                'reservation_bonus_hours' => $data->reservation_bonus_hours,
                'reservation_bonus_rate' => $data->reservation_bonus_rate
            ];
        }

        if (isset($request->user_id)) {
            $favourites = Favourite::where('user_id', $request->user_id)->first();

            if ($favourites) {
                $user_favourites = explode(',', $favourites->facility_ids);
            }
        }

        foreach ($rates['facilities'] as $key => $rate) {
            $rates['facilities'][$key]->reservation_bonus_hours = $filtered[$rates['facilities'][$key]->facility_id]['reservation_bonus_hours'];
            $rates['facilities'][$key]->reservation_bonus_rate = $filtered[$rates['facilities'][$key]->facility_id]['reservation_bonus_rate'];
            $rates['facilities'][$key]->is_favourite = false;
            
            if (isset($request->user_id) && $favourites) {
                if (in_array($rates['facilities'][$key]->facility_id, $user_favourites)) {
                    $rates['facilities'][$key]->is_favourite = true;
                }
            }
        }

        return $rates;
    }
	   
	
	public function checkinuser(Request $request,$CompanyId,$LocationId)
    {
		if ($request->headers->has('Authorization') === false) {
            return;
        }

        $header = $request->headers->get('Authorization');

        if (substr($header, 0, 7) !== 'Bearer ') {
            return;
        }

        $accessToken= trim(substr($header, 7));//exit;*/Date(created_at) DB::raw("DATE(created_at) = '".date('Y-m-d')."'")
		
		$veriaccessToken = DB::table('oauth_access_tokens')->where('id', $accessToken)->whereDate('created_at', '=', date('Y-m-d'))->first();
        if (!$veriaccessToken) {
            throw new NotFoundException('Error Occurred, Please send valid access token.');
        }
		
		
		//session_id
		$veriSessidClietid= DB::table('oauth_sessions')->where('id', $veriaccessToken->session_id)->where('client_id', $CompanyId)->first();
		
		if (!$veriSessidClietid) {
            throw new NotFoundException('Error Occurred, Please send valid access token with client id.');
        }
		
		
		//print_r($veriSessidClietid);exit;
		
		if (!$LocationId) {
            return "location is required";
        }
		
		$CompanyRes = DB::table('oauth_clients')->where('id', $CompanyId)->first();
        if (!$CompanyRes) {
            throw new NotFoundException('Error Occurred, No Client Found With This company code.');
        }
		

$plate=$request->plate;
$color=$request->color;
$make=$request->make;
$oversize=$request->oversize;
$location=$LocationId;
$created_at=date('Y-m-d H:i:s');

if($oversize){$oversize=1;}else{$oversize=0;}

//$resultInsertVehicleQuery = DB::insert('insert into vender_vehicles (vehicle_license_plate, vehicle_color,vehicle_make,oversize,created_at) values (?, ?, ?, ?, ?)', ["$plate", "$color", "$make", "$oversize", "$created_at"]);

//print_r($resultInsertVehicleQuery);
$vehicleId = DB::table('vender_vehicles')->insertGetId(
            ['vehicle_license_plate' => $plate, 'vehicle_color' => $color, 'vehicle_make' => $make, 'oversize' => $oversize, 'created_at' => $created_at]
    );
	
        //get and set message;
      /*  $res = array();
        $res['data']['title'] = "";
        $res['data']['is_background'] = false;
        $res['data']['message'] = $msg_payload['message'];
        $res['data']['image'] = '';
        $res['data']['payload'] = $payload;
        $res['data']['timestamp'] = date('Y-m-d G:i:s');
		
 
        $fields = array(
            'to' => $deviceToken,
            'data' => $res,
        );*/
		
		
        // Set POST variables
        $url = 'http://ultracloud.azurewebsites.net/api/v1/MobilePayments/company/MLH/location/'.$location.'/CheckInUser?ApiKey=2B20488A-0280-462D-8AAE-8B2048FE5819&ApplicationName=iconGo&Plate='.$plate.'&Color='.$color.'&Make='.$make.'&Oversize='.$oversize.'&ReservationProviderID=22537aaa-5953-45fb-a875-5f48b3378444';
        //Set Server API KEY
     
         $headers = array(
            'Content-Type: application/json'
        );

       // Log::info("Reached Android");
        // Open connection
        $ch = curl_init();
 
        // Set the url, number of POST vars, POST data
        curl_setopt($ch, CURLOPT_URL, $url);
 
       // curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
 
        // Disabling SSL Certificate support temporarly
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
 
        //curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
 
        // Execute post
        $result = curl_exec($ch);

        curl_close($ch);
       // Log:info(json_encode($result));
	   
	   /*Response:  {"LocationID":"MLH350","Plate":"OUTH195","TicketNumber":"744697","CheckInDateTime":"2017-10-05T09:02:15.61","TicketSecurityCode":"1400","ResultCode":0,"ResultMessage":"Success"}*/
	   /* {
    "LocationID": "string",
    "Plate": "string",
    "TicketNumber": "string",
    "CheckInDateTime": "2018-01-22T05:43:41.200Z",
    "TicketSecurityCode": "string",
    "ResultCode": 0,
    "ResultMessage": "string"
  }
   */
  
  //$result='{"LocationID":"MLH350","Plate":"OUTH195","TicketNumber":"744704","CheckInDateTime":"2018-02-01T05:43:41.200Z","TicketSecurityCode":"1400","ResultCode":0,"ResultMessage":"Success"}';
  $resultarr=json_decode($result);
  
  $facilityBonusData = Facility::select('id', 'reservation_bonus_hours', 'reservation_bonus_rate')->where('ticketech_id', $resultarr->LocationID)->get();
  $facility_id=$facilityBonusData[0]->id;
  $TicketNumber=$resultarr->TicketNumber;
  $ticket_security_code=$resultarr->TicketSecurityCode;
  $CheckInDateTimear=explode('.',$resultarr->CheckInDateTime);
  $CheckInDateTime=str_replace('T',' ',$CheckInDateTimear[0]);
  $oversize=$request->oversize;
  $created_at=date('Y-m-d H:i:s');
  
  if($oversize){$oversize=1;}else{$oversize=0;}
  
  $chTicketNumber = DB::table('vender_tickets')->where('ticket_number', $TicketNumber)->first();
        if ($chTicketNumber) {
            throw new NotFoundException('Error Occurred, Ticket number is already exist.');
        }
 

        if ($result) {
			$resultInsertQuery = DB::insert('insert into vender_tickets (facility_id,vehicle_id, ticket_number,ticket_security_code,check_in_datetime,created_at,oversize) values (?, ?, ?, ?, ?, ?, ?)', ["$facility_id","$vehicleId", "$TicketNumber", "$ticket_security_code", "$CheckInDateTime", "$created_at", "$oversize"]);
			 return [
                'status' => 'Successfull',
                'response' => $result
            ];
        } else {
            return [
                'status' => 'Not Successfull',
                'response' => curl_error($ch)
            ];
        }
       
    }
	
	public function getStayAmount(Request $request,$CompanyId,$LocationId)
    {
		if ($request->headers->has('Authorization') === false) {
            return;
        }

        $header = $request->headers->get('Authorization');

        if (substr($header, 0, 7) !== 'Bearer ') {
            return;
        }

        $accessToken= trim(substr($header, 7));//exit;*/Date(created_at) DB::raw("DATE(created_at) = '".date('Y-m-d')."'")
		
		$veriaccessToken = DB::table('oauth_access_tokens')->where('id', $accessToken)->whereDate('created_at', '=', date('Y-m-d'))->first();
        if (!$veriaccessToken) {
            throw new NotFoundException('Error Occurred, Please send valid access token.');
        }
		
		
		//session_id
		$veriSessidClietid= DB::table('oauth_sessions')->where('id', $veriaccessToken->session_id)->where('client_id', $CompanyId)->first();
		
		if (!$veriSessidClietid) {
            throw new NotFoundException('Error Occurred, Please send valid access token with client id.');
        }
		
		//echo $LocationId;
		if (!$LocationId) {
            return "location is required";
        }
		
		$CompanyRes = DB::table('oauth_clients')->where('id', $CompanyId)->first();
        if (!$CompanyRes) {
            throw new NotFoundException('Error Occurred, No Client Found With This company code.');
        }
		
		$TicketNumber=$request->TicketNumber;
		$TicketSecurityCode=$request->TicketSecurityCode;
		
        // Set POST variables
       $url = 'http://ultracloud.azurewebsites.net/api/v1/MobilePayments/company/MLH/location/'.$LocationId.'/GetStayAmount?ApiKey=2B20488A-0280-462D-8AAE-8B2048FE5819&ApplicationName=iconGo&TicketNumber='.$TicketNumber.'&TicketSecurityCode='.$TicketSecurityCode;
	  //exit;
        //Set Server API KEY
      
         $headers = array(
            'Content-Type: application/json'
        );

       // Log::info("Reached Android");
        // Open connection
        $ch = curl_init();
 
        // Set the url, number of POST vars, POST data
        curl_setopt($ch, CURLOPT_URL, $url);
 
        //curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
 
        // Disabling SSL Certificate support temporarly
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
 
        //curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
 
        // Execute post
        $result = curl_exec($ch);

        curl_close($ch);
       // Log:info(json_encode($result));
	   
	   /*Response: {"LocationID":"MLH350","TicketNumber":"744697","ReservationCode":"IQ653422","StayAmount":0.0,"TaxAmount":0.0,"TaxPercent":18.375,"CalculationDateTime":"2017-10-05T09:35:24.22","ResultCode":0,"ResultMessage":"OK"}*/
	   
  
 // $result='{"LocationID":"MLH350","TicketNumber":"744703","StayAmount":0.0,"TaxAmount":0.0,"TaxPercent":18.375,"CalculationDateTime":"2018-02-01T09:35:24.22","ResultCode":0,"ResultMessage":"OK"}';
 

  $resultarr=json_decode($result);
  //print_r($resultarr);echo $resultarr->ResultMessage;
  // print_r($result);$resultarr['ResultCode'];
   //exit;
   
   if($resultarr->ResultMessage=='OK'){
   
  $LocationID = $resultarr->LocationID;
  //$TicketNumber=$resultarr->TicketNumber;***
  $resultarr->TicketNumber=$TicketNumber;
  /*************/
  $StayAmount=$resultarr->StayAmount;
  $TaxAmount=$resultarr->TaxAmount;
  $TaxPercent=$resultarr->TaxPercent;
  //$CalculationDateTime=$resultarr->CalculationDateTime;
  $CalculationDateTimear=explode('.',$resultarr->CalculationDateTime);
  $CalculationDateTime=str_replace('T',' ',$CalculationDateTimear[0]);
  $ResultCode=$resultarr->ResultCode;
  $ResultMessage=$resultarr->ResultMessage;
  $updated_at=date('Y-m-d H:i:s');
   }
  //echo "update vender_tickets set LocationID='".$LocationID."', StayAmount='".$StayAmount."', TaxAmount='".$TaxAmount."', TaxPercent='".$TaxPercent."', CalculationDateTime='".$CalculationDateTime."', ResultCode='".$ResultCode."', ResultMessage='".$ResultMessage."', updated_at='".$updated_at."' where ticket_number='".$TicketNumber."' ";
 

        if ($result) {
			if($resultarr->ResultMessage=='OK'){
			$resultInsertQuery = DB::update("update vender_tickets set LocationID='".$LocationID."', StayAmount='".$StayAmount."', TaxAmount='".$TaxAmount."', TaxPercent='".$TaxPercent."', CalculationDateTime='".$CalculationDateTime."', ResultCode='".$ResultCode."', ResultMessage='".$ResultMessage."', updated_at='".$updated_at."' where ticket_number='".$TicketNumber."' ");
			}
			
			 return [
                'status' => 'Successfull',
                'response' => $result
            ];
        } else {
            return [
                'status' => 'Not Successfull',
                'response' => curl_error($ch)
            ];
        }
       
    }
	
	public function CheckOutUser(Request $request,$CompanyId,$LocationId)
    {
		if ($request->headers->has('Authorization') === false) {
            return;
        }

        $header = $request->headers->get('Authorization');

        if (substr($header, 0, 7) !== 'Bearer ') {
            return;
        }

        $accessToken= trim(substr($header, 7));//exit;*/Date(created_at) DB::raw("DATE(created_at) = '".date('Y-m-d')."'")
		
		$veriaccessToken = DB::table('oauth_access_tokens')->where('id', $accessToken)->whereDate('created_at', '=', date('Y-m-d'))->first();
        if (!$veriaccessToken) {
            throw new NotFoundException('Error Occurred, Please send valid access token.');
        }
		
		
		//session_id
		$veriSessidClietid= DB::table('oauth_sessions')->where('id', $veriaccessToken->session_id)->where('client_id', $CompanyId)->first();
		
		if (!$veriSessidClietid) {
            throw new NotFoundException('Error Occurred, Please send valid access token with client id.');
        }
		
		//echo $LocationId;
		if (!$LocationId) {
            return "location is required";
        }
		
		$CompanyRes = DB::table('oauth_clients')->where('id', $CompanyId)->first();
        if (!$CompanyRes) {
            throw new NotFoundException('Error Occurred, No Client Found With This company code.');
        }
		
		$TicketNumber=$request->TicketNumber;
		$TicketSecurityCode=$request->TicketSecurityCode;
		$PaymentAmount=$request->PaymentAmount;
		$AuthorizationCode=$request->AuthorizationCode;
		  // Set POST variables
       $url = 'http://ultracloud.azurewebsites.net/api/v1/MobilePayments/company/MLH/location/MLH350/CheckOutUser?ApiKey=2B20488A-0280-462D-8AAE-8B2048FE5819&ApplicationName=iconGo&TicketNumber='.$TicketNumber.'&TicketSecurityCode='.$TicketSecurityCode.'&PaymentAmount=0.0&AuthorizationCode=042536';
        //Set Server API KEY
      
         $headers = array(
            'Content-Type: application/json'
        );

       // Log::info("Reached Android");
        // Open connection
        $ch = curl_init();
 
        // Set the url, number of POST vars, POST data
        curl_setopt($ch, CURLOPT_URL, $url);
 
        //curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
 
        // Disabling SSL Certificate support temporarly
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
 
        //curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
 
        // Execute post
        $result = curl_exec($ch);

        curl_close($ch);
       // Log:info(json_encode($result));
	   
	   /*Response: {"LocationID":"MLH350","TicketNumber":"744697","ReservationCode":"IQ653422","StayAmount":0.0,"TaxAmount":0.0,"TaxPercent":18.375,"CalculationDateTime":"2017-10-05T09:35:24.22","ResultCode":0,"ResultMessage":"OK"}*/
	   
  //print_r($result);exit;
  //$result='{"LocationID":"MLH350","TicketNumber":"744703","ReservationCode":null,"StayAmount":0.0,"PaymentDatetime":"2018-02-01T13:35:33.234375+00:00","ResultCode":0,"ResultMessage":"OK"}';
  $resultarr=json_decode($result);
  
  if($resultarr->ResultMessage=='OK'){
  $LocationID = $resultarr->LocationID;
  //$TicketNumber=$resultarr->TicketNumber;
  $resultarr->TicketNumber=$TicketNumber;
  $StayAmount=$resultarr->StayAmount;
  //$PaymentDatetime=$resultarr->PaymentDatetime;
  $PaymentDatetimear=explode('.',$resultarr->PaymentDatetime);
  $PaymentDatetime=str_replace('T',' ',$PaymentDatetimear[0]);
  $ResultCode=$resultarr->ResultCode;
  $ResultMessage=$resultarr->ResultMessage;
  $updated_at=date('Y-m-d H:i:s');
  $CheckOutDateTime=date('Y-m-d H:i:s');;
  }
 
 

        if ($result) {
			if($resultarr->ResultMessage=='OK'){
			$resultInsertQuery = DB::update("update vender_tickets set  StayAmount='".$StayAmount."', checkout_datetime='".$CheckOutDateTime."', PaymentDatetime='".$PaymentDatetime."', ResultCode='".$ResultCode."', ResultMessage='".$ResultMessage."', updated_at='".$updated_at."' where ticket_number='".$TicketNumber."' ");
			}
			
			 return [
                'status' => 'Successfull',
                'response' => $result
            ];
        } else {
            return [
                'status' => 'Not Successfull',
                'response' => curl_error($ch)
            ];
        }
       
    }
	
	
	
    // REMOVE SPECIAL CHARACTERS, SPACES FROM USER NAME IF THERE IS ANY
    public function cleanData($data)
    {
        $data = str_replace(' ', '', $data); // Remove All Spaces.
        $data = str_replace('-', '', $data); // Remove All Dashes.
        return preg_replace('/[^A-Za-z0-9\-]/', '', $data); // REMOVE ALL SPECIAL CHARACETRS
    }
 
}
