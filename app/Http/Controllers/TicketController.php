<?php

namespace App\Http\Controllers;

use App\Exceptions\NotFoundException;
use App\Models\AuthorizeNetTransaction;
use Auth;
use Exception;
use Response;
use DB;
use Illuminate\Support\Facades\Input;
use Illuminate\Http\Request;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\Ticketech;
use App\Classes\PromoCodeLib;


use App\Http\Helpers\QueryBuilder;

use App\Models\Rate;
use App\Models\User;
use App\Models\Reservation;
use App\Models\Facility;
use App\Models\Ticket;
use App\Models\Role;
use App\Models\PaymentProfile;
use App\Models\Wallet;
use App\Models\PromoUsage;
use App\Models\PromoCode;


use App\Exceptions\ApiGenericException;
use App\Exceptions\TicketechException;
use App\Exceptions\UserNotFound;
use App\Exceptions\UserNotAuthorized;
use App\Exceptions\UserWithEmailExistsException;

use App\Classes\MagicCrypt;
use App\Classes\LoyaltyProgram;
use Carbon\Carbon;

use Log;


class TicketController extends Controller
{

    protected $user;

    protected $facility;

    protected $request;

    protected $authNet;

    protected $cim;

    protected $paymentProfileId;

    protected $validationRules;

    public function __construct(Request $request, AuthorizeNet $authNet, Cim $cim, Ticketech $ticketech)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->cim = $cim;
        $this->ticketech = $ticketech;

        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        // Use these rules if a pre-existing payment profile is being used
        $this->profileValidation = [
            'payment_profile_id' => 'required'
        ];
    }

    /**
     * Get list of reservations for the given user ID
     *
     * @param  [type] $userId [description]
     * @return [type]         [description]
     */
    public function getUserCheckinCheckout($userId)
    {
    	$point_ratio = LoyaltyProgram::POINTS_RATIO;
    	$userTicketData=array();

		$first = DB::table('tickets')
	        ->Join('reservations', 'reservations.id', '=', 'tickets.reservation_id')
			->select(
				'reservations.id as id','reservations.user_id', 
				'reservations.facility_id','reservations.anet_transaction_id', 
				'reservations.created_at','reservations.total', 
				'tickets.check_in_datetime as start_timestamp', 
				DB::raw("(time_to_sec(timediff(tickets.checkout_datetime, tickets.check_in_datetime )) / 3600) AS length"), 
				'tickets.ticket_number as ticketech_code','reservations.cancelled_at','reservations.anet_overstay_transaction_id', 
				'reservations.ticketech_guid', 
				'reservations.bonus_rate', 'reservations.overstay_rate', 'reservations.overstay_hours',
				'reservations.discount','reservations.credit_used',
				'reservations.loyalty_program', 'reservations.loyalty_point_used', 'reservations.loyalty_amount_used','reservations.pay_by'
			)
			->where('reservations.user_id', $userId)
			->whereNotNull('tickets.checkout_datetime')
			->WhereNull('reservations.deleted_at');
		
		
		$getusedid=DB::table('reservations')
			->Join('tickets', 'reservations.id', '=', 'tickets.reservation_id')
			->select('reservations.id')
			->where('reservations.user_id', $userId)
			->whereNotNull('tickets.checkout_datetime')
			->get();
		
		$idarr=array();
		foreach($getusedid as $ky=>$val){
			if (!in_array($val->id, $idarr)) 
				$idarr[]=$val->id;
		}
			
		$second= DB::table('reservations')
	        ->select(
	        	'reservations.id as id','reservations.user_id', 
	        	'reservations.facility_id','reservations.anet_transaction_id', 
	        	'reservations.created_at','reservations.total','reservations.start_timestamp', 
	        	'reservations.length as length','ticketech_code', 
	        	'cancelled_at','anet_overstay_transaction_id', 
	        	'ticketech_guid','bonus_rate', 'overstay_rate', 
	        	'overstay_hours', 'discount','reservations.credit_used',
			'reservations.loyalty_program', 'reservations.loyalty_point_used', 'reservations.loyalty_amount_used','reservations.pay_by'
	        )
			->where('reservations.user_id', $userId)
			->whereNOTin('id',$idarr)
			->WhereNull('reservations.deleted_at');
			//time_to_sec(timediff('2010-09-01 03:00:00', '2010-09-01 00:10:00' )) / 3600 TIMESTAMPDIFF(HOUR,check_in_datetime,checkout_datetime)

		$third= DB::table('tickets')
	        ->leftJoin('anet_transactions', 'anet_transactions.id', '=', 'tickets.anet_transaction_id')
	        ->select('tickets.id as id','tickets.user_id', 
	        	'tickets.facility_id','tickets.anet_transaction_id', 
	        	'tickets.created_at', 'anet_transactions.total', 
	        	'tickets.check_in_datetime as start_timestamp', 
	        	DB::raw("(time_to_sec(timediff(checkout_datetime, check_in_datetime )) / 3600) AS length"), 
	        	'ticket_number as ticketech_code','tickets.updated_at as cancelled_at', 
	        	'tickets.updated_at as anet_overstay_transaction_id', 
	        	'tickets.updated_at as ticketech_guid', 
	        	'tickets.updated_at as bonus_rate', 'tickets.updated_at as overstay_rate', 
	        	'tickets.updated_at as overstay_hours', 'tickets.updated_at as discount','tickets.updated_at as credit_used',
				'tickets.updated_at as loyalty_program', 'tickets.updated_at as loyalty_point_used', 'tickets.updated_at as loyalty_amount_used','tickets.updated_at as pay_by'
	        )
			->where('tickets.user_id', $userId)
			->whereNotNull('tickets.checkout_datetime')
			->Where(function ($query) {
					$query->WhereNull('tickets.reservation_id')
					->orWhere('tickets.reservation_id',0);
           	});//reservation_id checkout_datetime

		$userTicketData = Reservation::with(
				[
					'facility' => function ($query) {
						$query->select('full_name', 'short_name', 'entrance_location', 'id')->with('photos');
					},
					'transaction' => function ($transaction) {
						$transaction->select('payment_last_four','name','id','expiration','card_type');
					},
					'loyalty_transactions' => function ($loyalty_transactions) use ($point_ratio) {
						$loyalty_transactions->select(DB::raw("id,reservation_id,IF(type = 'COMPUTE', points*$point_ratio,points) as points"),'type');
					}
				]
			)
			->select(
				'reservations.id as id','reservations.user_id', 
				'reservations.facility_id','reservations.anet_transaction_id', 
				'reservations.created_at','reservations.total', 
				'reservations.start_timestamp','reservations.length as length', 
				'ticketech_code','cancelled_at','anet_overstay_transaction_id', 
				'ticketech_guid','bonus_rate', 'overstay_rate', 
				'overstay_hours', 'discount', 'credit_used',
				'reservations.loyalty_program', 'reservations.loyalty_point_used', 'reservations.loyalty_amount_used','reservations.pay_by'
			)
			->union($first)
			->union($second)
			->union($third)
			->where('user_id', $userId)
			->whereNOTin('reservations.id',$idarr)
			->WhereNull('reservations.deleted_at')
			->orderBy('start_timestamp', 'desc')
			->simplepaginate(20);
		
        return $userTicketData;
    }

	public function getTicketDetails($id)
	{
		$ticket = Ticket::with(['facility', 'reservation', 'transaction'])->find($id);
		return $ticket;
	}

}
