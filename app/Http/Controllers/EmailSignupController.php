<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;

use App\Models\User;
use App\Models\EmailSignup;

use App\Exceptions\NotFoundException;

class EmailSignupController extends Controller
{

    /**
     * Create a new email signup
     *
     * @param  Request $request [description]
     * @return [type]           [description]
     */
    public function store(Request $request)
    {
        $this->validate(
            $request, [
            'email' => 'required|email|unique:email_signups,email',
            'user_id' => 'exists:users,id'
            ]
        );

        return EmailSignup::firstOrCreate(
            [
            'user_id' => $request->user_id,
            'email' => $request->email
            ]
        );
    }
}
