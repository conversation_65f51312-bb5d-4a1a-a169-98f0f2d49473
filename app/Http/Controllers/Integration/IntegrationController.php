<?php

namespace App\Http\Controllers\Integration;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use App\Models\Facility;
use App\Exceptions\ApiGenericException;
use App\Models\OauthClient;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\Reservation;
use App\Models\Ticket;
use App\Services\LoggerFactory;
use Carbon\Carbon;

class IntegrationController extends Controller
{
   protected $log;

   public function __construct(LoggerFactory $logFactory)
   {
      $this->log = $logFactory->setPath('logs/integration/mapco-roc-logs')->createLogger('mapco-roc-logs');
   }

   /*** 
    * Store MAPCO ROC - HUB Zeag Integration Check-in data
    * 
    * @return
    */
   public function saveMapcoRocCheckinData(Request $request)
   {
      $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
      if (!$secret) {
         throw new ApiGenericException('Please provide valid X-ClientSecret.');
      }
      $this->validate($request, Ticket::$hubRocCheckinValidation);

      if (strtotime($request->checkin_time) < strtotime(date('Y-m-d'))) {
         throw new ApiGenericException('Checkin time will not be less than the current date');
      }

      if (strtotime($request->estimated_checkout) < strtotime($request->checkin_time)) {
         throw new ApiGenericException('Estimated checkout time will not be less than checkin time');
      }

      try {
         if ($request->header('X-ClientSecret') != '') {
            $facility = Facility::where('garage_code', $request->garage_code)->first();
            $this->log->info("Available:" . $facility);
            $checkinBeforeTime = FacilityConfiguration::select('reservation_start_time', 'before_cancel_time')->where('facility_id', $facility['id'])->first();

            $reservationStartTime = Reservation::select('start_timestamp')->where('id', $request->reservation_id)->first();

            $startTimestamp = Carbon::parse($reservationStartTime['start_timestamp']);
            $reservationStartDate = $startTimestamp->toDateString();

            $currentDateTime = Carbon::now();
            $differenceInMinutes = $startTimestamp->diffInRealMinutes($currentDateTime);

            if ((string)$differenceInMinutes <= (string)$checkinBeforeTime['reservation_start_time']) {
               throw new ApiGenericException('The check in time is outside of the allowed window');
            }
            $input = [];
            if ($facility) {
               $this->log->info("Facility available");
               $input = $request->all();

               $input['user_id']       = isset($input['user_id']) ? $input['user_id'] : 0;
               $input['checkin_gate']  = isset($input['checkin_gate']) ? $input['checkin_gate'] : null;
               $input['terminal_id']   = isset($input['terminal_id']) ? $input['terminal_id'] : null;
               $input['card_type']     = isset($input['card_type']) ? $input['card_type'] : null;
               $input['expiry']        = isset($input['expiry']) ? $input['expiry'] : null;
               $input['card_last_four']     = isset($input['card_last_four']) ? $input['card_last_four'] : null;
               $input['discount_amount']     = isset($input['discount_amount']) ? $input['discount_amount'] : null;
               $input['promocode']     = isset($input['promocode']) ? $input['promocode'] : null;
               $input['cardholder_name']     = isset($input['cardholder_name']) ? $input['cardholder_name'] : null;
               $input['is_checkin']     = 1;
               $input['is_checkout']    = 0;
               $input['is_closed']     = 1;

               $ticket = Ticket::create($input);
               $this->log->info("Data save or not" . $ticket);

               if ($ticket) {
                  $this->log->info("Data created" . $ticket);
                  return 'Checkin data saved successfully.';
               }
            } else {
               $this->log->info('Facility Not Found');
               throw new ApiGenericException('Facility Not Found');
            }
         }
      } catch (\Throwable $e) {
         $this->log->error($e);
         throw new ApiGenericException($e->getMessage());
      }
   }

   /*** 
    *  To update Check-out data from MAPCO ROC - HUB Zeag
    * 
    * @return array
    */
   public function updateMapcoRocCheckinData(Request $request)
   {
      $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
      if (!$secret) {
         throw new ApiGenericException('Please provide valid X-ClientSecret.');
      }

      $this->validate($request, Ticket::$hubRocCheckoutValidation);

      if (strtotime($request->checkout_time) < strtotime($request->checkin_time)) {
         throw new ApiGenericException('checkout time will not be less than checkin time');
      }

      try {
         if ($request->header('X-ClientSecret') != '') {
            $facility = Facility::where('garage_code', $request->garage_code)->first();
            $ticketExist = Ticket::where('facility_id', $facility['id'])->where('ticket_number', $request['ticket_number'])->first();

            if ($ticketExist == null) {
               return "Data not exist.";
            }
            $estimatedCheckoutTime = Reservation::select('estimated_checkout_time')->where('id', $request->reservation_id)->first();

            $checkoutGraceTime = Carbon::parse($estimatedCheckoutTime['estimated_checkout_time']);

            $currentDateTime = Carbon::now();

            if (strtotime($currentDateTime) > strtotime($checkoutGraceTime)) {
               throw new ApiGenericException('The check out time is outside of the allowed window');
            }

            $updateTicket = Ticket::where('ticket_number', $request['ticket_number'])
               ->where('facility_id', $facility['id'])
               ->update([
                  "checkout_time" => $request['checkout_time'],
                  "checkout_remark" => isset($request['checkout_remark']) ? $request['checkout_remark'] : null,

                  "checkout_cardholder_name" => isset($request['checkout_cardholder_name']) ? $request['checkout_cardholder_name'] : null,

                  "checkout_card_last_four" => isset($request['checkout_card_last_four']) ? $request['checkout_card_last_four'] : null,

                  "checkout_expiry" => isset($request['checkout_expiry']) ? $request['checkout_expiry'] : null,
                  "checkout_card_type" => isset($request['checkout_card_type']) ? $request['checkout_card_type'] : null,

                  "is_checkout" => 1,
                  "is_closed" => 2
               ]);
            $this->log->info('Ticket update.' . ' ' . $updateTicket);

            if ($updateTicket) {
               $this->log->info('Checkout data saved successfully.');
               return 'Checkout data saved successfully.';
            }
         }
      } catch (\Throwable $e) {
         $this->log->error($e);
         throw new ApiGenericException($e->getMessage());
      }
   }
}
