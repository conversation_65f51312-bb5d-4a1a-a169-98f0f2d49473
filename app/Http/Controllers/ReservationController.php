<?php

namespace App\Http\Controllers;

use App\Exceptions\NotFoundException;
use App\Models\AuthorizeNetTransaction;
use Auth;
use Exception;
use Response;
use Hash;
use Illuminate\Http\Request;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\TransactionsApplePay as AuthorizeNetApplePay;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\Ticketech;
use App\Classes\PromoCodeLib;
use App\Classes\LoyaltyProgram;
use App\Http\Helpers\QueryBuilder;
use App\Models\Rate;
use App\Models\User;
use App\Models\Reservation;
use App\Models\Facility;
use App\Models\Role;
use App\Models\PaymentProfile;
use App\Models\Wallet;
use App\Models\PromoUsage;
use App\Models\PromoCode;
use App\Models\LoyaltyUserAccounts;
use App\Exceptions\ApiGenericException;
use App\Exceptions\TicketechException;
use App\Exceptions\UserNotFound;
use App\Exceptions\UserNotAuthorized;
use App\Exceptions\UserWithEmailExistsException;
use App\Classes\MagicCrypt;
use Carbon\Carbon;
use Log;
use App\Models\CompanyAffilate;
use App\Models\EmailSignup;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
//use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use App\Models\HoursOfOperation;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\UpdateFacilityInventory;
use Artisan;
use App\Models\FacilityPartnerAvailability;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\ParkEngage\LicensePlateNumber;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\MemberUser;
use App\Models\UserPass;
use App\Models\Ticket;
use App\Services\LoggerFactory;
use Illuminate\Support\Facades\Storage;
use App\Models\Event;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\BrandSetting;
use App\Services\Pdf;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Services\Image;
// use App\Models\ParkEngage\PlatformNotification;
use App\Models\ParkEngage\PartnerNotification;
use App\Models\ParkEngage\Gate;
use App\Models\ParkEngage\Cruise;
use App\Models\ParkEngage\CruiseSchedule;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\ReservationHistroy;
use DB;

class ReservationController extends Controller
{

    protected $log;

    protected $user;

    protected $partnerPaymentDetails;

    protected $countryCode;

    protected $facility;

    protected $request;

    protected $authNet;
    protected $authNetApple;

    protected $cim;

    protected $paymentProfileId;

    protected $validationRules;

    protected $sendAnet = false;
    protected $anonymousAnet = false;

    const DEFAULT_CONFIRM_VAL = 1;
    const APPLE_PAY_FLAG = 1;
    const ADD_EXTRA_DAY_COUNT = 1;

    const DEFAULT_CANCEL_VAL = 2;
    const DEFAULT_UPDATED_VAL = 3;

    const TWENTY_FOUR_HOURS = 23;
    const DEFAULT_HOURS = 0;

    const REALTIME_WINDOW = 2;
    const END_TIME_EXTRA_MINUTES = 30;
    const DEFAULT_VALUE = 0;
    const RESERVATION_THRESHOLD_TYPE = 2;
    const QUEUE_NAME = 'iqp-inventory';
    const UPDATE_INVENTORY_TRUE = 1;
    const UPDATE_INVENTORY_FALSE = 0;
    const RESERVATION_TYPE = 'PARKENGAGE';
    const AUTH_LOGIN_ID = '92YXdf5TFA';
    const AUTH_TRANSACTION_ID = '22z48X6XHDr8Znjg';

    const SUPERADMIN = 1;
    const SUBORDINATE = 4;
    const PARTNER = 3;
    const REGIONAL_MANAGER = 12;
    const CASHIER = 11;
    const ATTENDANT = 9;
    const BUSINESS_USERTYPE = 10;
	const BUSINESS_CLERK = 8;

    public function __construct(Request $request, AuthorizeNet $authNet, Cim $cim, Ticketech $ticketech, AuthorizeNetApplePay $authApple)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->authNetApple = $authApple;
        $this->cim = $cim;
        $this->ticketech = $ticketech;

        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        // Use these rules if a pre-existing payment profile is being used
        $this->profileValidation = [
            'payment_profile_id' => 'required'
        ];

        $this->log = (new LoggerFactory)->setPath('logs/parkengage')->createLogger('booking');
    }

    /**
     * Get a listing of reservations.
     *
     * @param  $request Request (injected by laravel)
     * @return [type] [description]
     */
    public function get(Request $request)
    {
        $reservations = Reservation::query();

        if ($request->search) {
            $reservations = QueryBuilder::buildSearchQuery($reservations, $request->search, Reservation::$searchFields)
                ->orWhereHas(
                    'user',
                    function ($query) use ($request) {
                        $query
                            ->where('name', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%");
                    }
                )
                ->orWhereHas(
                    'facility',
                    function ($query) use ($request) {
                        $query
                            ->where('full_name', 'like', "%{$request->search}%")
                            ->orWhere('short_name', 'like', "%{$request->search}%");
                    }
                );
        }

        // Syntax for selecting fields on relationships.
        // Note that you have to retrieve the foreign key field, or laravel will return
        // null since it can't find the relationship
        return $reservations->with(
            [
                'user' => function ($query) {
                    $query->select('name', 'email', 'id');
                },
                'facility' => function ($query) {
                    $query->select('full_name', 'short_name', 'id');
                }
            ]
        )
            ->orderBy('id', 'desc')
            ->paginate(20);
    }

    /**
     * Validates a anon reservation codes
     *
     * @param  $reservationCode
     * @param  $ticketechCode
     * @return mixed
     * @throws UserNotAuthorized
     */
    public function validateReservationCodes($reservationCode, $ticketechCode)
    {
        $reservation = Reservation::where('ticketech_code', $ticketechCode)->first();
        if (!$reservation) {
            throw new NotFoundException('No reservation found with that ticketech code');
        }
        if ($reservationCode != $reservation->reservation_code) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }
        return $reservation;
    }

    // Returns an anon reservation
    public function getByCode($reservationCode, $ticketechCode)
    {
        $reservation = $this->validateReservationCodes($reservationCode, $ticketechCode);
        return $reservation->withRelations();
    }

    /* public function getReservationStubJpgByCode($reservationCode, $ticketechCode)
    {
        $reservation = $this->validateReservationCodes($reservationCode, $ticketechCode);
        $image = $reservation->generateStubJpg();
        return Response::make($image, 200)->withHeaders(
            [
            'Content-Type'          => 'image/jpg',
            'Content-Disposition'   => 'filename="image.jpg"'
            ]
        );
    }
    */

    public function getReservationStubJpgByCode($reservationCode, $ticketechCode)
    {

        $status = 500;
        $reservation = Reservation::where('ticketech_code', $ticketechCode)->first();
        if (!$reservation) {
            $reservation = new Reservation();
            $image = $reservation->generateStubJpgException('No reservation found.');
        } else {

            $currentDateTime = Carbon::now();
            $reservationEndDateTime = Carbon::parse($reservation->formatted_end_date_time);
            if ($reservationCode != $reservation->reservation_code) {
                $image = $reservation->generateStubJpgException('You are not authorized to view this reservation.');
            } else if ($currentDateTime->gte($reservationEndDateTime)) {
                $image = $reservation->generateStubJpgException('Reservation duration has been passed.');
            } else if ($reservation->cancelled_at) {
                $image = $reservation->generateStubJpgException('Reservation has been cancelled.');
            } else {
                $status = 200;
                $image = $reservation->generateStubJpg();
            }
        }
        return Response::make($image, $status)->withHeaders(
            [
                'Content-Type'          => 'image/jpg',
                'Content-Disposition'   => 'filename="image.jpg"'
            ]
        );
    }

    public function getReservationBarcodeJpgByCode($reservationCode, $ticketechCode)
    {
        $reservation = $this->validateReservationCodes($reservationCode, $ticketechCode);

        return $this->respondWithJpg($reservation->generateBarcodeJpg());
    }

    /**
     * cancels a reservation for anon user
     *
     * @param  $reservationCode
     * @param  $ticketechCode
     * @return mixed
     * @throws UserNotAuthorized
     */
    public function cancelReservationViaCode(Request $request, $reservationCode, $ticketechCode)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $reservation = $this->validateReservationCodes($reservationCode, $ticketechCode);

        return $reservation->cancel();
    }

    /**
     * @param Reservation $reservation
     * @return $this
     * @throws UserNotAuthorized
     * @throws UserNotFound
     */
    public function getById(Reservation $reservation)
    {
        $user = $this->getUser();
        if ($user->id != $reservation->user_id && !$user->isAdmin && !$user->hasRole(Role::CUSTOMER_SERVICE) && !$user->hasRole(Role::PARTNER) && !$user->hasRole(Role::SUBPARTNER) && !$user->hasRole(Role::REGIONAL_MANAGER)) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }
        return $reservation->withRelations();
    }

    public function getReservationDetailsByUser($reservationCode, $ticketechCode)
    {
        $user = Auth::user();
        if (!$user) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }
        $reservation = $this->validateReservationCodes($reservationCode, $ticketechCode);

        if ($user->id != $reservation->user_id) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }

        $reservation->created_at_formatted = date("D, F d, Y h:i A", strtotime($reservation->created_at));
        return $reservation->withRelations();
    }

    /**     
     * @param Reservation $reservation     
     * @return $this     
     */
    public function getByIdAnon(Reservation $reservation)
    {
        return $reservation->withRelations();
    }


    public function getByIdGuest($reservationCode, $ticketechCode)
    {
        $reservation = $this->validateReservationCodes($reservationCode, $ticketechCode);
        if ($reservation->guest_read_flag == '1') {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }
        $reservation->guest_read_flag = '1';
        $reservation->save();
        $reservation->created_at_formatted = date("D, F d, Y h:i A", strtotime($reservation->created_at));
        //        $reservation->created_at_formatted = date("D, M jS Y g:i A", strtotime($reservation->created_at));
        return $reservation->withRelations();
    }


    /**
     * @param $reservationId
     * return Reservation with facility, geolocation and transaction details
     */

    public function getReservationById($reservationId)
    {
        $reservation =  Reservation::with('facility', 'facility.geolocations', 'facility.photos', 'transaction')->where('id', $reservationId)->get()->first();
        return  $reservation;
    }


    public function getReservationStubJpgById(Reservation $reservation)
    {
        $user = $this->getUser();
        if ($user->id != $reservation->user_id && !$user->isAdmin && !$user->hasRole(Role::PARTNER) && !$user->hasRole(Role::SUBPARTNER) && !$user->hasRole(Role::REGIONAL_MANAGER)) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }
        $image = $reservation->generateStubJpg();
        return Response::make($image, 200)->withHeaders(
            [
                'Content-Type'          => 'image/jpg',
                'Content-Disposition'   => 'filename="image.jpg"'
            ]
        );
    }

    public function getReservationBarcodeJpgById(Reservation $reservation)
    {
        $user = $this->getUser();
        if ($user->id != $reservation->user_id && !$user->isAdmin) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }

        return $this->respondWithJpg($reservation->generateStubJpg());
    }

    /**
     * @param Reservation $reservation
     * @return Reservation
     * @throws ApiGenericException
     */
    public function cancelReservationById(Request $request, Reservation $reservation)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $user = Auth::user();
        if ($user->id != $reservation->user_id && !$user->isAdmin && !$user->hasRole(Role::CUSTOMER_SERVICE) && !$user->hasRole(Role::PARTNER) && !$user->hasRole(Role::SUBPARTNER) && !$user->hasRole(Role::REGIONAL_MANAGER)) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }
        if ($request->header('X-ClientSecret') != '') {
            return $reservation->cancel($request->header('X-ClientSecret'));
        } else {
            return $reservation->cancel();
        }
    }

    public function resendEmail(Request $request, Reservation $reservation)
    {
        $reservation->emailReservationToUser($request->email);

        return ['sent' => true];
    }

    /**
     * Get reservation stub HTML
     *
     * @return [type] [description]
     */
    public function getReservationStubHtml(Reservation $reservation)
    {
        return $reservation->generateStubHtml();
    }

    /**
     * Get list of reservations for the given user ID
     *
     * @param  [type] $userId [description]
     * @return [type]         [description]
     */
    public function getUserReservations(Request $request, $userId)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }

        $reservations = Reservation::with(
            [
                'flightDetails',
                'facility.facilityConfiguration',
                'facility' => function ($query) {
                    $query->with('photos')->select('full_name', 'timezone', 'short_name', 'id', 'entrance_location', 'between_streets', 'is_gated_facility');
                },
                'transaction' => function ($transaction) {
                    $transaction->select('payment_last_four', 'name', 'id', 'card_type');
                },
                'vehicle',
                'facility.parkingDevices',
                'ticket'
            ]
        )
            ->where('user_id', $userId)
            ->orderBy('start_timestamp', 'desc')
            ->paginate(20);

        // added the checkin-facility details by Ashutosh 26-09-2023
        if (isset($reservations) && !empty($reservations)) {
            foreach ($reservations as $key => $val) {
                $val->checkin_facility = null;
                if (isset($val->ticket) && $val->facility_id != $val->ticket->facility_id) {
                    $val->checkin_facility = Facility::where('id', $val->ticket->facility_id)->first();
                }
            }
        }

        return $reservations;
    }

    /**
     * Make a reservation for an anonymous (not logged in) user
     *
     * @param  Request      $request Should include these fields: name, address_one, address_two, city, state, zip, country, email, name_on_card, card_number, expiration_date, security_code, description, facility_id, arrival, length, total
     *                               name, address_one, address_two, city, state, zip, country, email,
     *                               name_on_card, card_number, expiration_date, security_code,
     *                               description, facility_id, arrival, length, total
     * @param  AuthorizeNet $authNet [description]
     * @return [type]                [description]
     */

    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);

        $zipCode = (isset($cardData[4]) && $cardData[4] != 'undefined') ? $cardData[4] : '';
        $request->request->add(
            [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode
            ]
        );

        $this->request = $request;
    }


    public function makeAnonReservation(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $checkFacility = Facility::where('id', $request->facility_id)->where('owner_id', $secret->partner_id)->first();
            if (!$checkFacility) {
                throw new NotFoundException('No garage found with this partner.');
            }
            if ($checkFacility->active != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->is_available != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->facility_booking_type == '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if (!$checkFacility->accept_cc) {
                throw new ApiGenericException("Garage does not accept credit cards");
            }
        }
        if (isset($this->request->is_card_req) && $this->request->is_card_req == true) {
            $this->setDecryptedCard($request);
            $this->validate($this->request, $this->billingValidation);
        }

        if (isset($this->request->is_booking_also) && $this->request->is_booking_also == true) {
            $this->setDecryptedCard($request);
            $this->validate($this->request, $this->billingValidation);
        }

        if (!(isset($request->is_third_party_promo)) || !($request->is_third_party_promo)) {
            $promocode = $this->validatePromocodeRequest($request); // Check If Promocode Used
        }
        $partner_id = $secret->partner_id;
        //$member_user_id = '';
        $is_member = '0';
        if ($request->header('X-ClientSecret') != '') {
            if ($this->request->phone != '') {
                // Get country Code
                $this->countryCode = QueryBuilder::appendCountryCode();
                $existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $secret->partner_id)->first();
                if ($existPhone) {

                    if ($this->request->email != '') {
                        $existPhone->email = $this->request->email;
                    }
                    $existPhone->name = $this->request->name;
                    //$existPhone->type_id = $this->request->type_id;
                    $existPhone->save();
                    $this->user = $existPhone;
                } else {
                    /*$existEmail = User::where('email', $this->request->email)->where('created_by', $secret->partner_id)->first();
                    if($existEmail){
                        throw new ApiGenericException('Email already exist.');   
                    }*/
                    $this->user = User::create(
                        [
                            'name' => $this->request->name,
                            'email' => $this->request->email,
                            'phone' => $this->countryCode . $this->request->phone,
                            'password' => Hash::make(str_random(60)),
                            'anon' => false,
                            'user_type' => '5',
                            'created_by' => $secret->partner_id,
                            //'type_id' => $this->request->type_id
                        ]
                    );
                }
            } else {
                $this->user = User::getAnonUserByPartner($this->request->email, $secret->partner_id);
            }
            if ($this->request->member_id != '') {
                $is_member = '1';

                // Test if string contains the word 
                $search = '511';
                $mystring = $this->request->member_id;
                if (preg_match("/{$search}/i", $mystring)) {
                } else {
                    throw new ApiGenericException('Invalid member user.');
                }
                $member = MemberUser::where('member_id', $this->request->member_id)->first();
                if ($member) {
                    $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                    if ($memberUser) {
                        throw new ApiGenericException('Member Id already associate with other user.');
                    }
                }
                //$this->user->member_user_id = $memberUser->id;
                //$this->user->save();
            }

            if ($this->request->is_book_parking == '0' && $this->request->is_booking_also == '0' && $this->request->pass_id == '' && $this->request->is_pass_purchase == '1') {
            } else {
                $isResExist = Reservation::where("user_id", $this->user->id)->whereDate("start_timestamp", '=', date("Y-m-d", strtotime($this->request->arrival)))->whereNull("cancelled_at")->orderBy('id', 'DESC')->first();
                if ($isResExist) {

                    $tickets = Ticket::where('reservation_id', $isResExist->id)->first();
                    if (!$tickets) {

                        $day = date('d', strtotime($this->request->arrival));
                        $monthYear = date('F, Y', strtotime($this->request->arrival));
                        $number = (string) $day;
                        $last_digit = substr($number, -1);
                        $second_last_digit = substr($number, -2, 1);
                        $suffix = 'th';
                        if ($second_last_digit != '1') {
                            switch ($last_digit) {
                                case '1':
                                    $suffix = 'st';
                                    break;
                                case '2':
                                    $suffix = 'nd';
                                    break;
                                case '3':
                                    $suffix = 'rd';
                                    break;
                                default:
                                    break;
                            }
                        }
                        if ((string) $number === '1') $suffix = 'st';
                        throw new ApiGenericException('You already have a booking for ' . $number . $suffix . ' ' . $monthYear . '.');
                    } else {
                        if ($tickets->is_checkout == '0') {
                            throw new ApiGenericException('You already have check-in against today booking.');
                        }
                    }
                }
            }
        } else {
            if ($this->request->phone != '') {

                // Get country Code
                $this->countryCode = QueryBuilder::appendCountryCode();
                $userFacility = Facility::where('id', $request->facility_id)->first();
                if (!$userFacility) {
                    throw new NotFoundException('No garage found with this partner.');
                }
                $existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $userFacility->owner_id)->first();
                if ($existPhone) {
                    /*$existEmail = User::where('email', $this->request->email)->where('id', '!=', $existPhone->id)->where('created_by', $secret->partner_id)->first();
                    if($existEmail){
                        throw new ApiGenericException('Email already exist.');   
                    }*/
                    if ($this->request->email != '') {
                        $existPhone->email = $this->request->email;
                    }
                    $existPhone->name = $this->request->name;
                    //$existPhone->type_id = $this->request->type_id;
                    $existPhone->save();
                    $this->user = $existPhone;
                } else {
                    /*$existEmail = User::where('email', $this->request->email)->where('created_by', $secret->partner_id)->first();
                    if($existEmail){
                        throw new ApiGenericException('Email already exist.');   
                    }*/
                    $this->user = User::create(
                        [
                            'name' => $this->request->email,
                            'email' => $this->request->email,
                            'phone' => $this->countryCode . $this->request->phone,
                            'password' => Hash::make(str_random(60)),
                            'anon' => false,
                            'user_type' => '5',
                            'created_by' => $userFacility->owner_id,
                            //'type_id' => $this->request->type_id
                        ]
                    );
                }
            } else {
                $this->user = User::getAnonUser($this->request->email);
            }
        }

        if ($this->request->arrival != '') {
            if (strtotime(date("Y-m-d", strtotime($this->request->arrival))) < strtotime(date("Y-m-d"))) {
                throw new ApiGenericException('Parking Date can not be a past date');
            }
        }
        if ($this->request->is_pass_purchase == '1') {
            if ($this->request->pass_id != '') {
                $details = $this->getExistingPassDetails($this->request->pass_id);
            } else {
                $details = $this->makePassPayment();
            }

            $reservation = [];
            if ($this->request->is_book_parking == '1') {

                $reservation = $this->makePassReservation();

                //save zoo atlanta member user id 
                //$this->user->member_user_id = $member_user_id;
                $this->user->is_member = $is_member;
                $this->user->save();

                if ($this->request->member_id != '') {
                    $is_member = '1';

                    $search = '511';
                    $mystring = $this->request->member_id;
                    if (preg_match("/{$search}/i", $mystring)) {
                    } else {
                        throw new ApiGenericException('Invalid member user.');
                    }
                    $member = MemberUser::where('member_id', $this->request->member_id)->first();
                    if ($member) {
                        $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                        if ($memberUser) {
                            throw new ApiGenericException('Member Id already associate with other user.');
                        }
                    } else {
                        $mUser['member_id'] = $this->request->member_id;
                        $member = MemberUser::create($mUser);
                    }
                    $this->user->member_user_id = $member->id;
                    $this->user->save();
                }
                if ($reservation['reservation']) {
                    $res = Reservation::find($reservation['reservation']->id);
                    $res->user_pass_id = $details->id;
                    $res->save();

                    $reservation = $reservation['reservation']->toArray();
                }

                $details->consume_days = $details->consume_days + 1;
                $details->remaining_days = $details->remaining_days - 1;
                $details->save();
            } else {
                $this->user->is_member = $is_member;
                $this->user->save();

                if ($this->request->member_id != '') {
                    $is_member = '1';

                    $search = '511';
                    $mystring = $this->request->member_id;
                    if (preg_match("/{$search}/i", $mystring)) {
                    } else {
                        throw new ApiGenericException('Invalid member user.');
                    }
                    $member = MemberUser::where('member_id', $this->request->member_id)->first();
                    if ($member) {
                        $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                        if ($memberUser) {
                            throw new ApiGenericException('Member Id already associate with other user.');
                        }
                    } else {
                        $mUser['member_id'] = $this->request->member_id;
                        $member = MemberUser::create($mUser);
                    }
                    $this->user->member_user_id = $member->id;
                    $this->user->save();
                }
            }

            return [
                'pass' => $details->withRelations(),
                'reservation' => $reservation
            ];
        }/*elseif($this->request->is_pass_purchase == '1' && $this->request->is_book_parking == '1'){

             
        }*/ else {
            $this->log->info("Booking about to start");
            $details = $this->makeReservation();
            //save zoo atlanta member user id 
            $this->user->is_member = $is_member;
            $this->user->save();

            if ($this->request->member_id != '') {
                $is_member = '1';

                $search = '511';
                $mystring = $this->request->member_id;
                if (preg_match("/{$search}/i", $mystring)) {
                } else {
                    throw new ApiGenericException('Invalid member user.');
                }
                $member = MemberUser::where('member_id', $this->request->member_id)->first();
                if ($member) {
                    $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                    if ($memberUser) {
                        throw new ApiGenericException('Member Id already associate with other user.');
                    }
                } else {
                    $mUser['member_id'] = $this->request->member_id;
                    $member = MemberUser::create($mUser);
                }
                $this->user->member_user_id = $member->id;
                $this->user->save();
            }
            $reservation_id = $details['reservation']->ticketech_code;

            if (isset($promocode) && $promocode) {
                $this->updatePromocodeChanges($request, $promocode, $reservation_id, $this->user); // Update If Promocode Used
            }

            // Return reservation and charge details to caller
            return [
                'reservation' => $details['reservation']->toArray(),
                'ref_id' => $details['charge']['ref_id']
            ];
        }
    }

    public function getExistingPassDetails($pass_id)
    {

        $pass = UserPass::find($pass_id);
        if (!$pass) {
            throw new ApiGenericException('Invalid Pass Id.');
        }

        if (strtotime($pass->start_date) > strtotime($this->request->arrival)) {
            throw new ApiGenericException('Selected pass will be valid from ' . date('d\t\h F, Y', strtotime($pass->start_date)));
        } else if (strtotime($pass->end_date) < strtotime($this->request->arrival)) {

            throw new ApiGenericException('Selected pass already expired on ' . date('d\t\h F, Y', strtotime($pass->end_date)));
        } else if ($pass->remaining_days <= 0) {

            throw new ApiGenericException('You have already consumed you pass.');
        } else {

            return $pass;
        }
    }


    protected function makePassPayment()
    {

        // Validate the reservation details here
        $this->log->info("Pass about to purchase.");
        $this->setDecryptedCard($this->request);
        $this->validate($this->request, $this->billingValidation);

        $this->validate($this->request, UserPass::$validationRules, UserPass::$validationMessages);


        // Confirm that request rate matches database rate

        // if ($rate['price'] != $this->request->total) {
        //$sentRate = $this->request->pass_total;

        $passRate = Rate::with(['rateType', 'category'])->where('id', $this->request->pass_rate_id)->where('rate_type_id', '7')->where('active', '1')->first();
        if (!$passRate) {
            throw new ApiGenericException(
                'Sent rate does not match database rate, please reset pass rate options and try again.',
                422,
                ['database_rate' => $passRate->price]
            );
        }
        $pass_rate_id = $passRate->id;
        $pass_rate = $passRate->price;


        if (isset($this->request->facility_id)) {
            $this->facility = Facility::find($this->request->facility_id);

            //for processing fee
            if ($this->facility->processing_fee > 0) {

                $pass_rate = $pass_rate + (float)$this->facility->processing_fee;
            }
        }        //check if the total amount is 0

        if ($pass_rate > 0) {
            $secret = OauthClient::where('secret', $this->request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $is_partner = 0;
            if ($this->user->user_type == 3) {
                $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                $is_partner = 1;
            } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
                $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                if ($this->partnerPaymentDetails) {
                    $is_partner = 1;
                } else {
                    throw new NotFoundException('No payment gateway details found for this partner.');
                }
            } else {
                $is_partner = 0;
            }
            if ($is_partner == 1) {
                $this->authNet
                    ->setUser($this->user)
                    /*->isReservation()                
                ->setFacility($this->facility)*/
                    ->isPartner($this->partnerPaymentDetails)
                    ->setBillingAddress($this->getBillingArray());
            } else {
                $this->authNet
                    ->setUser($this->user)
                    /*->isReservation()
                ->setFacility($this->facility)*/
                    ->setBillingAddress($this->getBillingArray());
            }

            if (isset($this->paymentProfileId)) { // Use our logged in users profile
                $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
            } else {  // Otherwise set transaction details manually
                if ($is_partner == 1) {
                    $this->authNet
                        ->isPartner($this->partnerPaymentDetails)->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                } else {
                    $this->authNet->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            }
        }

        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $pass = $this->savePass($passRate);

        // Mobile reservation overstay text comes from mobile end.
        $passMode = "Pass Purchase";

        //check if the total amount is 0
        if ($pass_rate > 0) {

            try {

                // Use our database rate price to create the transaction
                $charge = $this->authNet->createTransaction(
                    // $rate['price'],
                    $pass_rate,
                    "{$passMode} {$pass->id}",
                    config('icon.reservations_account')
                )->isPartner($this->partnerPaymentDetails)->executeTransaction();
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                $this->log->error($e->getMessage());
                $transaction = $this->authNet->getTransaction();
                $pass->anet_transaction_id = $transaction->id;
                $pass->save();
                $pass->delete();
                throw $e;
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $pass->anet_transaction_id = $transaction->id;
            if ($this->request->header('X-ClientSecret') != '') {
                $pass->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
            }

            $pass->save();
        } else {


            // Charge successful, save transaction relationship to it
            $authorized_anet_transaction = new AuthorizeNetTransaction();

            $authorized_anet_transaction->sent = $this->sendAnet;
            $authorized_anet_transaction->anonymous = $this->anonymousAnet;
            $authorized_anet_transaction->user_id = $this->user->id;
            $authorized_anet_transaction->ip_address = \Request::ip();
            $authorized_anet_transaction->total = 0;
            $authorized_anet_transaction->name = $this->getBillingName();

            $authorized_anet_transaction->description = "Pass purchase {$pass->id}";

            $authorized_anet_transaction->response_message = "Zero amount transaction";
            $authorized_anet_transaction->save();

            $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

            $pass->anet_transaction_id = $authorized_anet_transaction->id;

            if ($this->request->header('X-ClientSecret') != '') {
                $pass->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
            }

            $pass->save();
        }


        $pass->emailPassToUser();
        $this->log->info("Pass created : " . $pass->id);
        return $pass;
        /*return [
            'charge' => $charge,
            'pass' => $pass->withRelations()
        ];*/
    }

    protected function checkPassCode()
    {
        $ticket = 'PA' . rand(100, 999) . rand(100, 999);
        $isExist = UserPass::where('pass_code', $ticket)->first();
        if ($isExist) {
            $this->checkPassCode();
        }
        return $ticket;
    }

    protected function savePass($rate)
    {

        $pass = new UserPass(
            [
                'user_id' => $this->user->id,
                'email' => $this->request->email,
                'phone' => $this->request->phone,
                'purchased_on' => date("Y-m-d H:i:s"),
                'start_date' => date("Y-m-d", strtotime($this->request->arrival)),
                'pass_code' => $this->checkPassCode(),
            ]
        );
        if ($rate->category->no_of_days > 0) {
            $endDate = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $rate->category->no_of_days . ' days'));
            $pass->end_date = $endDate;
        }
        $pass->total_days = $rate->max_stay / 24;
        $pass->remaining_days = $rate->max_stay / 24;
        $pass->consume_days = 0;
        $pass->rate_id = $rate->id;


        if (isset($this->facility->processing_fee) && $this->facility->processing_fee > 0) {

            $pass->processing_fee = $this->facility->processing_fee;
            $pass->total = $rate->price + (float)$this->facility->processing_fee;
        } else {
            $pass->total = $rate->price;
        }
        if (isset($this->facility->processing_fee) && $this->facility->processing_fee > 0) {

            $pass->processing_fee = $this->facility->processing_fee;
            $pass->total = $rate->price + (float)$this->facility->processing_fee;
        } else {
            $pass->total = $rate->price;
        }

        // If this is higher the apply bonus will get double applied


        $pass->save();

        $userDetails  = User::where('id', $this->user->id)->first();
        $userDetails->is_member = '1';
        $userDetails->save();


        return $pass;
    }

    public function makeAnonReservationApplePay(Request $request)
    {

        $promocode = $this->validatePromocodeRequest($request); // Check If Promocode Used

        $this->user = User::getAnonUser($this->request->email);

        $details = $this->makeReservationApplePay();

        $reservation_id = $details['reservation']->ticketech_code;

        if ($promocode) {
            $this->updatePromocodeChanges($request, $promocode, $reservation_id, $this->user); // Update If Promocode Used
        }

        // Return reservation and charge details to caller
        return [
            'reservation' => $details['reservation']->toArray(),
            'ref_id' => $details['charge']['ref_id']
        ];
    }

    /**
     * Make a apple pay reservation for a logged in user.
     *
     * @return [type] [description]
     */
    public function makeUserReservationApplePay(Request $request)
    {

        // 1. Set a global flag for first reservation for refer a friend reference for later use
        $isFirstReservation = false;

        // 2. Fetch Logged In User
        $user = User::find(Auth::user()->id);

        // 3. Check if it is user's first reservation
        if (!$user->has_reserved) {
            // echo 'CHECKING RESERVATIONS FOR : ' . $user->id;
            $reservation = Reservation::where('user_id', $user->id)->first();
            if (!$reservation) {
                // echo 'SETTING TRUE';
                $isFirstReservation = true;
            }
        }

        //==============================================================//
        // Direct Parameter Injection for Testing Purpose (REFER A FRIEND)
        // $request->request->add([
        //     'is_wallet_redeemed' => true,
        //     'redeemed_amount' => 5
        // ]);
        // $reservation_id = 123456;
        //==============================================================//

        //==============================================================//
        // Direct Parameter Injection for Testing Purpose (PROMO CODE)
        // $request->request->add([
        //     'is_promocode_redeemed' => true,
        //     'promocode' => 'X2XR98G8',
        //     'redeemed_amount' => 10,
        //     'user_id' => 101322
        // ]);
        //==============================================================//

        $promocode = $this->validatePromocodeRequest($request); // Check If Promocode Used

        $wallet = Wallet::getUserWallet($user->id);
        if ($request->is_wallet_redeemed && isset($request->redeemed_amount_credit)) {
            if ($wallet->balance < $request->redeemed_amount_credit) {
                throw new ApiGenericException('Invalid Transaction, Reservation Can\'t be Processed.');
            }
        }

        $this->user = $this->getUser();
        $this->facility = Facility::find($this->request->facility_id);


        $details = $this->makeReservationApplePay();

        $reservation_id = $details['reservation']->ticketech_code;

        // Reservation Ends Here


        // Wallet Implementation Starts From Here
        $walletConfig = Wallet::getConfig();

        if (isset($request->is_wallet_redeemed)) { // Check if user has redeemed his/her wallet
            if ($request->is_wallet_redeemed && $request->redeemed_amount_credit != "" && $request->redeemed_amount_credit > 0) {
                Wallet::makeTransaction($user->id, 'DEBIT', 'REDEEM', $request->redeemed_amount_credit, null, $reservation_id);
            }
        }

        if ($isFirstReservation) { // If First reservation flag is true
            if ($user->referred_by) {
                $referred_user = User::where('referral_code', $user->referred_by)->first();
                if ($referred_user) {
                    Wallet::makeTransaction($referred_user->id, 'CREDIT', 'REFERRAL', $walletConfig['REFERRAL_BONUS'], $user->id, $reservation_id);
                }
            }
            $user->has_reserved = 1;
            $user->save();
        }
        // Wallet Impleenration Ends Here ...

        if ($promocode) {
            $this->updatePromocodeChanges($request, $promocode, $reservation_id, $user);
        }

        // Return reservation and charge details to caller

        $returnData['reservation'] = $details['reservation']->toArray();
        $returnData['ref_id'] = $details['charge']['ref_id'];
        return $returnData;
    }

    /**
     * Make a reservation for a logged in user.
     *
     * @return [type] [description]
     */
    public function makeUserReservation(Request $request)
    {
        // for local system testing
        // error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            if (!Facility::where('id', $request->facility_id)->where('id', $secret->partner_id)->get()) {
                throw new NotFoundException('No garage found with this partner.');
            }
        }

        $validationRules = $this->billingValidation;
        unset($validationRules['email']);

        if ($this->request->input('payment_profile_id')) {
            $validationRules = $this->profileValidation;
        }

        if (!isset($this->request->is_card_req)) {
            $is_card_req = 1;
        } else {
            $is_card_req = $this->request->is_card_req;
        }

        if (!(isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed) && $is_card_req) {
            $this->setDecryptedCard($request);
        }

        // 1. Set a global flag for first reservation for refer a friend reference for later use
        $isFirstReservation = false;

        // 2. Fetch Logged In User
        $user = User::find(Auth::user()->id);

        // 3. Check if it is user's first reservation
        if (!$user->has_reserved) {
            // echo 'CHECKING RESERVATIONS FOR : ' . $user->id;
            $reservation = Reservation::where('user_id', $user->id)->first();
            if (!$reservation) {
                // echo 'SETTING TRUE';
                $isFirstReservation = true;
            }
        }

        //==============================================================//
        // Direct Parameter Injection for Testing Purpose (REFER A FRIEND)
        // $request->request->add([
        //     'is_wallet_redeemed' => true,
        //     'redeemed_amount' => 5
        // ]);
        // $reservation_id = 123456;
        //==============================================================//

        //==============================================================//
        // Direct Parameter Injection for Testing Purpose (PROMO CODE)
        // $request->request->add([
        //     'is_promocode_redeemed' => true,
        //     'promocode' => 'X2XR98G8',
        //     'redeemed_amount' => 10,
        //     'user_id' => 101322
        // ]);
        //==============================================================//

        if (!(isset($request->is_third_party_promo)) || !($request->is_third_party_promo)) {
            $promocode = $this->validatePromocodeRequest($request); // Check If Promocode Used
        }

        /*$wallet = Wallet::getUserWallet($user->id);
        if ($request->is_wallet_redeemed && isset($request->redeemed_amount_credit)) {
            if ($wallet->balance < $request->redeemed_amount_credit) {
                throw new ApiGenericException('Invalid Transaction, Reservation Can\'t be Processed.');
            }
        }*/

        // Reservation Starts From Here ...

        // Validate different depending on whether user is submitting new payment or using existing
        // remove validation in case of loyalty redeem request

        if (!isset($this->request->is_card_req)) {
            $is_card_req = 1;
        } else {
            $is_card_req = $this->request->is_card_req;
        }

        if (!(isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed) && $is_card_req) {
            $this->validate($this->request, $validationRules);
        }

        $this->user = $this->getUser();
        $this->facility = Facility::find($this->request->facility_id);

        // Try to get an existing profile
        $this->paymentProfileId = $this->request->input('payment_profile_id');

        if (!$this->paymentProfileId && $this->request->savePaymentMethod) {
            // Set up our cim class
            if ($this->request->header('X-ClientSecret') != '') {
                $secret = OauthClient::where('secret', $this->request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('No partner found.');
                }
                $is_partner = 0;
                if ($this->user->user_type == 3) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if ($this->partnerPaymentDetails) {
                        $is_partner = 1;
                    } else {
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                } else {
                    $is_partner = 0;
                }
            } else {
                $is_partner = 0;
            }
            if ($is_partner == 1) {
                $this->cim
                    ->setUser($this->user)
                    ->setFacility($this->facility)
                    ->isReservation()
                    ->isPartner($this->partnerPaymentDetails)
                    ->setBillingAddress($this->getBillingArray())
                    ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
            } else {
                $this->cim
                    ->setUser($this->user)
                    ->setFacility($this->facility)
                    ->isReservation()
                    ->setBillingAddress($this->getBillingArray())
                    ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
            }


            $customerProfile = $this->user->cim;

            if (!$customerProfile) {  // Create a cim for this user so they can store their details and reuse them
                $details = $this->cim->createCustomerProfile()->isPartner($this->partnerPaymentDetails)->executeCustomerProfileRequest();
            } else { // If the user has a cim already, just add a new payment profile
                $details = $this->cim->createPaymentProfile()->isPartner($this->partnerPaymentDetails)->executePaymentProfileRequest();
            }

            if (isset($details['payment_profile_id'])) {
                $this->paymentProfileId = $details['payment_profile_id'];
            } else {
                $returnData['account_card'] = array(
                    'success' => false,
                    'message' => 'Unable to add Account card  for future reservations.'
                );
            }
        }

        $details = $this->makeReservation('1');

        $reservation_id = $details['reservation']->ticketech_code;

        // Reservation Ends Here


        // Wallet Implementation Starts From Here
        $walletConfig = Wallet::getConfig();

        if (isset($request->is_wallet_redeemed)) { // Check if user has redeemed his/her wallet
            if ($request->is_wallet_redeemed && $request->redeemed_amount_credit != "" && $request->redeemed_amount_credit > 0) {
                Wallet::makeTransaction($user->id, 'DEBIT', 'REDEEM', $request->redeemed_amount_credit, null, $reservation_id);
            }
        }

        if ($isFirstReservation) { // If First reservation flag is true
            if ($user->referred_by) {
                $referred_user = User::where('referral_code', $user->referred_by)->first();
                if ($referred_user) {
                    Wallet::makeTransaction($referred_user->id, 'CREDIT', 'REFERRAL', $walletConfig['REFERRAL_BONUS'], $user->id, $reservation_id);
                }
            }
            $user->has_reserved = 1;
            $user->save();
        }
        // Wallet Impleenration Ends Here ...

        if (isset($promocode) && $promocode) {
            $this->updatePromocodeChanges($request, $promocode, $reservation_id, $user);
        }

        // Return reservation and charge details to caller

        $returnData['reservation'] = $details['reservation']->toArray();
        $returnData['ref_id'] = $details['charge']['ref_id'];
        return $returnData;
    }


    public function validatePromocodeRequest($request)
    {
        if (isset($request->is_promocode_redeemed) && isset($request->promocode) && isset($request->redeemed_amount)) {
            if ($request->is_promocode_redeemed) {
                if ($request->redeemed_amount <= 0) {
                    throw new ApiGenericException('Invalid Transaction, Discount Amount Can Not Be 0 or Null');
                }
                $request->request->add(['from_reservation' => '1']);
                $promoResult = PromoCodeLib::validatePromoCode($request);

                if (!$promoResult['is_promocode_valid']) {
                    throw new ApiGenericException($promoResult['message']);
                }

                //saving emaail in email signup if user has not opt out
                if (isset($request->is_opt_out)) {
                    if ($request->is_opt_out == 1) {
                        $this->storeEmailSignup($request);
                    } else if ($request->is_opt_out == 0) {
                        $this->removeEmailSignup($request);
                    }
                }

                return $promoResult['promocode'];
            }
        }
        return false;
    }

    public function storeEmailSignup($request)
    {
        EmailSignup::firstOrCreate(
            [
                'user_id' => $request->user_id,
                'email' => $request->email
            ]
        );
    }

    public function removeEmailSignup($request)
    {
        $emailSignup = EmailSignup::where('email', $request->email)->first();
        if ($emailSignup) {
            $emailSignup->flag = 0;
            $emailSignup->save();
        }
    }

    public function updatePromocodeChanges($request, $promocode, $reservation_id, $user = null)
    {
        $promoType = $promocode->promo_type_id;
        switch ($promoType) {
            case 1:
                try {
                    $promoUsage = new PromoUsage();
                    $promoUsage->user_id = $user->id;
                    $promoUsage->promocode = $promocode->promocode;
                    $promoUsage->reservation_id = $reservation_id;
                    $promoUsage->discount_amount = $request->redeemed_amount;
                    $promoUsage->save();
                } catch (Exception $e) {
                    throw $e;
                }
                break;
            case 2:
                try {
                    $actualPromoCode = PromoCode::where('promocode', $promocode->promocode)->first();
                    $actualPromoCode->is_expired = 1;
                    $actualPromoCode->status = 0;
                    $actualPromoCode->save();
                    if ($user) {
                        $promoUsage = new PromoUsage();
                        $promoUsage->user_id = $user->id;
                        $promoUsage->promocode = $promocode->promocode;
                        $promoUsage->reservation_id = $reservation_id;
                        $promoUsage->discount_amount = $request->redeemed_amount;
                        $promoUsage->save();
                    }
                } catch (Exception $e) {
                    throw $e;
                }
                break;

            case 3:
                // code...
                break;

            default:
                // code...
                break;
        }
    }

    /**
     * Given a user, make a reservation for that user.
     * TODO: This functionality should probably be drawn out into a ReservationService
     *
     * @param  User $user [description]
     * @return [type]       [description]
     */
    protected function makeReservation($car_req = '')
    {
        if ($car_req == '') {
            $this->setDecryptedCard($this->request);
            $this->validate($this->request, $this->billingValidation);
        }
        // Validate the reservation details here

        if (isset($this->request->use_overstay) && $this->request->use_overstay > 0) {

            return $this->updateReservationWithOverstay();
        }
        $this->validate($this->request, Reservation::$validationRules, Reservation::$validationMessages);

        $this->facility = Facility::find($this->request->facility_id);

        // Confirm that facilty has ticktech ID
        if (!$this->facility->has_ticketech && !$this->facility->parkonect_id) {
            throw new ApiGenericException('Online reservations cannot be made with this facility.');
        }


        // Users can choose to apply bonus time to their reservation for a small fee
        $useBonus = (bool) $this->request->use_bonus;


        $couponThresholdPrice = isset($this->request->coupon_threshold_price) ? ($this->request->coupon_threshold_price) : (self::DEFAULT_VALUE);

        // Returns Rate or array if the facility base rate is returned
        //last 2 parameters are for inventory, last paramter will have value 2 to indicate the call is from reservation so add the threshold directly instead
        //of checking threshold type if price or percentage
        //if($this->request->is_rate_dynamic == '1'){
        $isMember = $this->request->member_id != '' ? 1 : 0;
        $rate = $this->facility->rateForReservationOnMarker($this->request->arrival, $this->request->length, $useBonus, false, false, true, $couponThresholdPrice, self::RESERVATION_THRESHOLD_TYPE, $isMember);
        // return $rate;
        if (!$rate) {
            $this->log->info("No rate found in database for this reservation.");
            throw new ApiGenericException('No rate found in database for this reservation.', 422);
        }
        /*}else{
            $rate = [];
        }*/

        // Confirm that request rate matches database rate

        // if ($rate['price'] != $this->request->total) {
        $sentRate = $this->request->total;
        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $sentRate = ($this->request->total + $this->request->redeemed_amount);
        }

        //for credit user
        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $sentRate = ($sentRate + $this->request->redeemed_amount_credit);
        }

        //for Processing Fee
        if (isset($this->facility->processing_fee) && $this->facility->processing_fee > 0) {
            $sentRate = ($sentRate - $this->facility->processing_fee);
        }


        //add use bonus if used

        if ($useBonus) {
            $rate['price'] = $rate['price'] + $this->facility->reservation_bonus_rate;
        }



        // for loyalty points:
        if (
            isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed
            && $this->request->loyalty_points > 0
        ) {

            // check loyalty account is active
            if ($this->user->is_loyalty_active != LoyaltyProgram::CONST_ONE) {
                throw new ApiGenericException('Your loyalty account is inactive.');
            }

            // // get user total loyalty points check
            // $loyalty_points = 0;
            // $accountData = LoyaltyProgram::getUserAccounts($this->user->email, $this->user->id);
            // if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
            //     $loyalty_points = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
            // }

            // if ($loyalty_points < $this->request->loyalty_points) {
            //     throw new ApiGenericException(
            //         'You do not have enough loyalty points for this reservation, please reset reservation options and try again.',
            //         422,
            //         ['sent_points' => $this->request->loyalty_points, 'actual_points' => $loyalty_points]
            //     );
            // }
            // Reservation::$reservationType = 'IR';
            // $sentRate = floatval($sentRate + floatval($this->request->loyalty_points / LoyaltyProgram::POINTS_REDEEM_RATIO));
        }
        /*$pass_rate_id = '';
        if($this->request->member_id != ''){
            $passRate = Rate::where('price', $this->request->total)->where('rate_type_id', '7')->where('active', '1')->first();
            if(!$passRate){
                throw new ApiGenericException(
                    'Sent rate does not match database rate, please reset reservation options and try again.',
                    422,
                    ['sent_rate' => $this->request->total, 'database_rate' => $passRate->price]
                );
            }
            $pass_rate_id = $passRate->id;
        }else{*/
        if (filter_var($rate['price'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $sentRate) {
            throw new ApiGenericException(
                'Sent rate does not match database rate, please reset reservation options and try again.',
                422,
                ['sent_rate' => $this->request->total, 'database_rate' => $rate['price']]
            );
        }
        /*}*/

        //check if the total amount is 0
        if ($this->request->total > 0) {
            if ($this->request->header('X-ClientSecret') != '') {
                $secret = OauthClient::where('secret', $this->request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('No partner found.');
                }
                $is_partner = 0;
                if ($this->user->user_type == 3) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if ($this->partnerPaymentDetails) {
                        $is_partner = 1;
                    } else {
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                } else {
                    $is_partner = 0;
                }
            } else {
                $is_partner = 0;
            }
            if ($is_partner == 1) {
                $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->isPartner($this->partnerPaymentDetails)
                    ->setBillingAddress($this->getBillingArray());
            } else {
                $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());
            }
            /*$this->authNet
                ->setUser($this->user)
                ->isReservation()
                ->setFacility($this->facility)
                ->setBillingAddress($this->getBillingArray());
*/
            if (isset($this->paymentProfileId)) { // Use our logged in users profile
                $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
            } else {  // Otherwise set transaction details manually
                if ($is_partner == 1) {
                    $this->authNet
                        ->isPartner($this->partnerPaymentDetails)->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                } else {
                    $this->authNet->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            }
        }

        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $reservation = $this->saveReservation($rate, $useBonus);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

        // loyalty programs check for reservations
        //        removed second condtion discuced with nirbhay- if ($this->user->is_loyalty && $this->user->is_loyalty_active == LoyaltyProgram::CONST_ONE) {

        // if ($this->user->is_loyalty) {
        //     $reservation->loyalty_program = LoyaltyProgram::LP_ALLOW;
        // }

        //save warning message flag and values 

        if (isset($this->request->warning_on_reservation) && (($this->request->warning_on_reservation) > 0)) {
            $reservation->warning_on_reservation = $this->request->warning_on_reservation;
            $reservation->warning_on_reservation_msg = $this->request->warning_on_reservation_msg_email;
        }

        //check if the total amount is 0
        if ($this->request->total > 0) {

            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                try {
                    // $ticketech_guid = $this->ticketech->makeReservation($reservation, $this->request->name_on_card);
                    $ticketech_guid = rand(100000, 999999);
                } catch (TicketechException $e) {
                    // If ticketech returns an invalid response, void our transaction and pass the error on to the user
                    $reservation->delete();
                    throw $e;
                }
            }
            // Send reservation to parkonect
            else if ($this->facility->parkonect_id) {
                try {
                    // $ticketech_guid = $this->parkonect->makeReservation($reservation, $this->request->name_on_card); 
                    $ticketech_guid = rand(100000, 999999);
                } catch (ParkonectException $e) {
                    // If parkonect returns an invalid response, void our transaction and pass the error on to the user
                    $reservation->delete();
                    throw $e;
                }
            }

            try {

                // Use our database rate price to create the transaction
                if ($this->facility->parkonect_id) {
                    if ($is_partner == 1) {
                        $charge = $this->authNet->createTransaction(
                            // $rate['price'],
                            $this->request->total,
                            "{$reservationMode} {$reservation->id}, Parkonect Code {$reservation->ticketech_code}",
                            config('icon.reservations_account')
                        )->isPartner($this->partnerPaymentDetails)->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
                    } else {
                        $charge = $this->authNet->createTransaction(
                            // $rate['price'],
                            $this->request->total,
                            "{$reservationMode} {$reservation->id}, Parkonect Code {$reservation->ticketech_code}",
                            config('icon.reservations_account')
                        )->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
                    }
                } else {
                    if ($is_partner == 1) {
                        $charge = $this->authNet->createTransaction(
                            // $rate['price'],
                            $this->request->total,
                            "{$reservationMode} {$reservation->id}, Ticketech Code {$reservation->ticketech_code}",
                            config('icon.reservations_account')
                        )->isPartner($this->partnerPaymentDetails)->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
                    } else {
                        $charge = $this->authNet->createTransaction(
                            // $rate['price'],
                            $this->request->total,
                            "{$reservationMode} {$reservation->id}, Ticketech Code {$reservation->ticketech_code}",
                            config('icon.reservations_account')
                        )->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
                    }
                }
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                $this->log->error($e->getMessage());
                $transaction = $this->authNet->getTransaction();
                $reservation->anet_transaction_id = $transaction->id;
                $reservation->save();
                $reservation->delete();
                throw $e;
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $reservation->anet_transaction_id = $transaction->id;
            $reservation->ticketech_guid = rand(100000, 999999);
            if ($this->request->header('X-ClientSecret') != '') {
                $reservation->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
            } else {
                $reservation->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
            }

            if ($this->request->week != '') {
                $weekDays = 7 * $this->request->week;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }
            if ($this->request->month != '') {
                $weekDays = date('t') * $this->request->month;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }

            if ($this->request->member_id != '') {
                $reservation->is_daily = '1';
            }

            if ($this->request->no_of_visitor != '') {
                $reservation->no_of_visitor = $this->request->no_of_visitor;
            }

            /*if($pass_rate_id != ''){
                $reservation->rate_id = $pass_rate_id;
            }*/
            $reservation->payment_gateway = 'authnet';
            $reservation->save();
        } else {

            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                try {
                    // $ticketech_guid = $this->ticketech->makeReservation($reservation, $this->request->name_on_card);
                    $ticketech_guid = rand(100000, 999999);
                } catch (TicketechException $e) {
                    $reservation->delete();
                    throw $e;
                }
            }
            // Send reservation to parkonect
            else if ($this->facility->parkonect_id) {
                try {
                    //$ticketech_guid = $this->parkonect->makeReservation($reservation, $this->request->name_on_card);
                    $ticketech_guid = rand(100000, 999999);
                } catch (ParkonectException $e) {
                    $reservation->delete();
                    throw $e;
                }
            }

            // Charge successful, save transaction relationship to it
            $authorized_anet_transaction = new AuthorizeNetTransaction();

            $authorized_anet_transaction->sent = $this->sendAnet;
            $authorized_anet_transaction->anonymous = $this->anonymousAnet;
            $authorized_anet_transaction->user_id = $this->user->id;
            $authorized_anet_transaction->ip_address = \Request::ip();
            $authorized_anet_transaction->total = 0;
            $authorized_anet_transaction->name = $this->getBillingName();

            if ($this->facility->parkonect_id) {
                $authorized_anet_transaction->description = "Reservation {$reservation->id}, Parkonect Code {$reservation->ticketech_code}";
            } else {
                $authorized_anet_transaction->description = "Reservation {$reservation->id}, Ticketech Code {$reservation->ticketech_code}";
            }

            if (
                isset($this->request->is_loyalty_redeemed)
                && $this->request->is_loyalty_redeemed
            ) {
                $authorized_anet_transaction->response_message = "Zero amount loyalty transaction";
            } else {
                $authorized_anet_transaction->response_message = "Zero amount transaction";
            }
            $authorized_anet_transaction->save();

            $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

            $reservation->anet_transaction_id = $authorized_anet_transaction->id;
            $reservation->ticketech_guid = $ticketech_guid;

            if ($this->request->week != '') {
                $weekDays = 7 * $this->request->week;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }
            if ($this->request->month != '') {
                $weekDays = date('t') * $this->request->month;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }

            if ($this->request->member_id != '') {
                $reservation->is_daily = '1';
            }

            /*if($pass_rate_id != ''){
                $reservation->rate_id = $pass_rate_id;
            }*/

            if ($this->request->header('X-ClientSecret') != '') {
                $reservation->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
            }

            if ($this->request->no_of_visitor != '') {
                $reservation->no_of_visitor = $this->request->no_of_visitor;
            }


            /*if($this->request->license_plate_number != ''){
                $reservation->license_plate_number = $this->request->license_plate_number;
                $reservation->make_model = $this->request->make_model;
            }*/

            $reservation->save();
        }


        // Send email to user
        if ($this->request->header('X-ClientSecret') != '') {
            if ($this->request->phone != '') {
                $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));

                if ($reservation->is_daily == '0') {
                    $startLabel = "Enter After";
                    $endLabel = "Exit Before";
                } else {
                    $startLabel = "Start Date";
                    $endLabel = "End Date";
                }

                // facility Name used for sms
                $facilityName = ucwords($this->facility->full_name);
                //converted the length into hh:mm
                $length = $reservation->length . "";
                $length = str_replace('.', ':', $length);

                //send sms to user
                $accountSid = env('TWILIO_ACCOUNT_SID');
                $authToken  = env('TWILIO_AUTH_TOKEN');
                $client = new Client($accountSid, $authToken);
                try {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                        // the number you'd like to send the message to
                        $this->countryCode . $this->request->phone,
                        array(
                            // A Twilio phone number you purchased at twilio.com/console
                            'from' => env('TWILIO_PHONE'),
                            // the body of the text message you'd like to send
                            //'body' => "Fine"
                            'body' =>
                            "Thank you for booking your parking with $facilityName.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date_time \n$endLabel : $reservation->formatted_end_date_time \nAmount Charged : $$reservation->total \nDuration : $length"
                        )
                    );
                } catch (RestException $e) {
                    //throw new ApiGenericException($e->getMessage());
                    //return "success";
                }
            } else {
                $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));
            }
        } else {
            $reservation->emailReservationToUser();
        }
        $this->log->info("Booking successfull : " . $reservation->id);
        return [
            'charge' => $charge,
            'reservation' => $reservation->withRelations(),
            'ticketech_guid' => isset($ticketech_guid) ? $ticketech_guid : ''
        ];
    }

    protected function makePassReservation()
    {
        $this->log->info("Pass reservation about to start.");
        // Validate the reservation details here

        if (isset($this->request->use_overstay) && $this->request->use_overstay > 0) {

            return $this->updateReservationWithOverstay();
        }
        $this->validate($this->request, Reservation::$validationRules, Reservation::$validationMessages);

        $this->facility = Facility::find($this->request->facility_id);

        // Confirm that facilty has ticktech ID
        if (!$this->facility->has_ticketech && !$this->facility->parkonect_id) {
            throw new ApiGenericException('Online reservations cannot be made with this facility.');
        }


        // Users can choose to apply bonus time to their reservation for a small fee
        $useBonus = (bool) $this->request->use_bonus;


        $couponThresholdPrice = isset($this->request->coupon_threshold_price) ? ($this->request->coupon_threshold_price) : (self::DEFAULT_VALUE);

        // Returns Rate or array if the facility base rate is returned
        //last 2 parameters are for inventory, last paramter will have value 2 to indicate the call is from reservation so add the threshold directly instead
        //of checking threshold type if price or percentage
        //if($this->request->is_rate_dynamic == '1'){
        $isMember = $this->request->member_id != '' ? 1 : 0;
        $rate = $this->facility->rateForReservationOnMarker($this->request->arrival, $this->request->length, $useBonus, false, false, true, $couponThresholdPrice, self::RESERVATION_THRESHOLD_TYPE, $isMember);
        if (!$rate) {
            throw new ApiGenericException('No rate found in database for this reservation.', 422);
        }
        /*}else{
            $rate = [];
        }*/

        // Confirm that request rate matches database rate

        // if ($rate['price'] != $this->request->total) {
        $sentRate = $this->request->total;

        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $sentRate = ($this->request->total + $this->request->redeemed_amount);
        }

        //for credit user
        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $sentRate = ($sentRate + $this->request->redeemed_amount_credit);
        }

        //add use bonus if used

        if ($useBonus) {
            $rate['price'] = $rate['price'] + $this->facility->reservation_bonus_rate;
        }



        // for loyalty points:
        if (
            isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed
            && $this->request->loyalty_points > 0
        ) {

            // check loyalty account is active
            if ($this->user->is_loyalty_active != LoyaltyProgram::CONST_ONE) {
                throw new ApiGenericException('Your loyalty account is inactive.');
            }
        }


        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $reservation = $this->savePassReservation($rate, $useBonus);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

        // loyalty programs check for reservations
        //        removed second condtion discuced with nirbhay- if ($this->user->is_loyalty && $this->user->is_loyalty_active == LoyaltyProgram::CONST_ONE) {

        // if ($this->user->is_loyalty) {
        //     $reservation->loyalty_program = LoyaltyProgram::LP_ALLOW;
        // }

        //save warning message flag and values 

        if (isset($this->request->warning_on_reservation) && (($this->request->warning_on_reservation) > 0)) {
            $reservation->warning_on_reservation = $this->request->warning_on_reservation;
            $reservation->warning_on_reservation_msg = $this->request->warning_on_reservation_msg_email;
        }

        // Send reservation to ticketech
        if ($this->facility->ticketech_id) {
            try {
                // $ticketech_guid = $this->ticketech->makeReservation($reservation, $this->request->name_on_card);
                $ticketech_guid = rand(100000, 999999);
            } catch (TicketechException $e) {
                $reservation->delete();
                throw $e;
            }
        }
        // Send reservation to parkonect
        else if ($this->facility->parkonect_id) {
            try {
                //$ticketech_guid = $this->parkonect->makeReservation($reservation, $this->request->name_on_card);
                $ticketech_guid = rand(100000, 999999);
            } catch (ParkonectException $e) {
                $reservation->delete();
                throw $e;
            }
        }

        // Charge successful, save transaction relationship to it
        $authorized_anet_transaction = new AuthorizeNetTransaction();

        $authorized_anet_transaction->sent = $this->sendAnet;
        $authorized_anet_transaction->anonymous = $this->anonymousAnet;
        $authorized_anet_transaction->user_id = $this->user->id;
        $authorized_anet_transaction->ip_address = \Request::ip();
        $authorized_anet_transaction->total = 0;
        $authorized_anet_transaction->name = $this->getBillingName();

        if ($this->facility->parkonect_id) {
            $authorized_anet_transaction->description = "Reservation {$reservation->id}, Parkonect Code {$reservation->ticketech_code}";
        } else {
            $authorized_anet_transaction->description = "Reservation {$reservation->id}, Ticketech Code {$reservation->ticketech_code}";
        }

        if (
            isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed
        ) {
            $authorized_anet_transaction->response_message = "Zero amount loyalty transaction";
        } else {
            $authorized_anet_transaction->response_message = "Zero amount transaction";
        }
        $authorized_anet_transaction->save();

        $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

        $reservation->anet_transaction_id = $authorized_anet_transaction->id;
        $reservation->ticketech_guid = $ticketech_guid;

        if ($this->request->week != '') {
            $weekDays = 7 * $this->request->week;
            $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
            $reservation->end_timestamp = $endTimestamp;
            $reservation->length = $weekDays * 24;
            $reservation->days = $weekDays;
        }
        if ($this->request->month != '') {
            $weekDays = date('t') * $this->request->month;
            $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
            $reservation->end_timestamp = $endTimestamp;
            $reservation->length = $weekDays * 24;
            $reservation->days = $weekDays;
        }

        if ($this->request->member_id != '') {
            $reservation->is_daily = '1';
        }

        /*if($pass_rate_id != ''){
                $reservation->rate_id = $pass_rate_id;
            }*/

        if ($this->request->header('X-ClientSecret') != '') {
            $reservation->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
        }

        if ($this->request->no_of_visitor != '') {
            $reservation->no_of_visitor = $this->request->no_of_visitor;
        }


        /*if($this->request->license_plate_number != ''){
                $reservation->license_plate_number = $this->request->license_plate_number;
                $reservation->make_model = $this->request->make_model;
            }*/
        $reservation->payment_gateway = 'authnet';
        $reservation->save();

        // Send email to user
        if ($this->request->header('X-ClientSecret') != '') {

            if ($this->request->phone != '') {
                $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));

                if ($reservation->is_daily == '0') {
                    $startLabel = "Enter After";
                    $endLabel = "Exit Before";
                } else {
                    $startLabel = "Start Date";
                    $endLabel = "End Date";
                }

                // facility Name used for sms
                $facilityName = ucwords($this->facility->full_name);
                //converted the length into hh:mm
                $length = $reservation->length . "";
                $length = str_replace('.', ':', $length);

                //send sms to user
                $accountSid = env('TWILIO_ACCOUNT_SID');
                $authToken  = env('TWILIO_AUTH_TOKEN');
                $client = new Client($accountSid, $authToken);
                try {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                        // the number you'd like to send the message to
                        $this->countryCode . $this->request->phone,
                        array(
                            // A Twilio phone number you purchased at twilio.com/console
                            'from' => env('TWILIO_PHONE'),
                            // the body of the text message you'd like to send
                            //'body' => "Fine"
                            'body' =>
                            "Thank you for booking your parking with $facilityName.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date_time \n$endLabel : $reservation->formatted_end_date_time \nAmount Charged : $$reservation->total\nDuration : $length"
                        )
                    );
                } catch (RestException $e) {
                    //throw new ApiGenericException($e->getMessage());
                    //return "success";
                }
            } else {
                $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));
            }
        } else {
            $reservation->emailReservationToUser();
        }
        $this->log->info("Pass reservation done ." . $reservation->id);
        return [
            'charge' => $charge,
            'reservation' => $reservation->withRelations(),
            'ticketech_guid' => isset($ticketech_guid) ? $ticketech_guid : ''
        ];
    }

    /**
     * Given a user, make a reservation for that user, made reservation using apple pay.
     * TODO: This functionality should probably be drawn out into a ReservationService
     *
     * @param  User $user [description]
     * @return [type]       [description]
     */
    protected function makeReservationApplePay()
    {
        // Validate the reservation details here

        if (isset($this->request->use_overstay) && $this->request->use_overstay > 0) {

            return $this->updateReservationWithOverstayApplePay();
        }

        $this->validate($this->request, Reservation::$validationRules, Reservation::$validationMessages);

        $this->facility = Facility::find($this->request->facility_id);

        // Confirm that facilty has ticktech ID
        if (!$this->facility->has_ticketech) {
            throw new ApiGenericException('Online reservations cannot be made with this facility.');
        }

        // Users can choose to apply bonus time to their reservation for a small fee
        $useBonus = (bool) $this->request->use_bonus;


        $couponThresholdPrice = isset($this->request->coupon_threshold_price) ? ($this->request->coupon_threshold_price) : (self::DEFAULT_VALUE);

        // Returns Rate or array if the facility base rate is returned
        //last 2 parameters are for inventory, last paramter will have value 2 to indicate the call is from reservation so add the threshold directly instead
        //of checking threshold type if price or percentage
        $rate = $this->facility->rateForReservation($this->request->arrival, $this->request->length, $useBonus, false,  false, true, array(), $couponThresholdPrice, self::RESERVATION_THRESHOLD_TYPE);

        if (!$rate) {
            throw new ApiGenericException('No rate found in database for this reservation.', 422);
        }


        // Confirm that request rate matches database rate

        // if ($rate['price'] != $this->request->total) {
        $sentRate = $this->request->total;

        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $sentRate = ($this->request->total + $this->request->redeemed_amount);
        }

        //for credit user
        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $sentRate = ($sentRate + $this->request->redeemed_amount_credit);
        }

        //add use bonus if used

        if ($useBonus) {
            $rate['price'] = $rate['price'] + $this->facility->reservation_bonus_rate;
        }



        // for loyalty points:
        if (
            isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed
            && $this->request->loyalty_points > 0
        ) {

            // check loyalty account is active
            if ($this->user->is_loyalty_active != LoyaltyProgram::CONST_ONE) {
                throw new ApiGenericException('Your loyalty account is inactive.');
            }

            // get user total loyalty points check
            $loyalty_points = 0;
            $accountData = LoyaltyProgram::getUserAccounts($this->user->email, $this->user->id);
            if ($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
                $loyalty_points = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
            }

            if ($loyalty_points < $this->request->loyalty_points) {
                throw new ApiGenericException(
                    'You do not have enough loyalty points for this reservation, please reset reservation options and try again.',
                    422,
                    ['sent_points' => $this->request->loyalty_points, 'actual_points' => $loyalty_points]
                );
            }
            Reservation::$reservationType = 'IR';
            $sentRate = floatval($sentRate + floatval($this->request->loyalty_points / LoyaltyProgram::POINTS_REDEEM_RATIO));
        }

        if (filter_var($rate['price'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $sentRate) {
            throw new ApiGenericException(
                'Sent rate does not match database rate, please reset reservation options and try again.',
                422,
                ['sent_rate' => $this->request->total, 'database_rate' => $rate['price']]
            );
        }

        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $reservation = $this->saveReservation($rate, $useBonus);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

        // loyalty programs check for reservations
        if ($this->user->is_loyalty) {
            $reservation->loyalty_program = LoyaltyProgram::LP_ALLOW;
        }

        //save warning message flag and values         
        if (isset($this->request->warning_on_reservation) && (($this->request->warning_on_reservation) > 0)) {
            $reservation->warning_on_reservation = $this->request->warning_on_reservation;
            $reservation->warning_on_reservation_msg = $this->request->warning_on_reservation_msg_email;
        }

        //check if the total amount is 0
        if ($this->request->total > 0) {
            $charge = $this->makeApplePayTransaction($reservation, $this->request->total);
            $transaction_id = isset($charge['anet_trans_id']) ? $charge['anet_trans_id'] : '0';
            // Charge successful, save transaction relationship to it
            //$transaction = $this->authNet->getTransaction();
            $transaction = $this->authNetApple->getTransaction();
            $reservation->anet_transaction_id = $transaction->id;
            $reservation->pay_by = self::APPLE_PAY_FLAG;
            $reservation->save();

            // Send reservation to ticketech
            try {
                $ticketech_guid = $this->ticketech->makeReservation($reservation);
            } catch (TicketechException $e) {
                $transaction = AuthorizeNetTransaction::where('anet_trans_id', $transaction_id)->first();
                $this->authNetApple->setTransaction($transaction)->void();
                $reservation->delete();
                throw $e;
            }
        } else {
            // Charge successful, save transaction relationship to it
            $authorized_anet_transaction = new AuthorizeNetTransaction();

            $authorized_anet_transaction->sent = $this->sendAnet;
            $authorized_anet_transaction->anonymous = $this->anonymousAnet;
            $authorized_anet_transaction->user_id = $this->user->id;
            $authorized_anet_transaction->ip_address = \Request::ip();
            $authorized_anet_transaction->total = 0;
            $authorized_anet_transaction->name = $this->getBillingName();
            $authorized_anet_transaction->description = "Reservation {$reservation->id}, Ticketech Code {$reservation->ticketech_code}";

            if (
                isset($this->request->is_loyalty_redeemed)
                && $this->request->is_loyalty_redeemed
            ) {
                $authorized_anet_transaction->response_message = "Zero amount loyalty transaction-Applepay";
            } else {
                $authorized_anet_transaction->response_message = "Zero amount transaction-Applepay";
            }
            $authorized_anet_transaction->save();

            $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

            $reservation->anet_transaction_id = $authorized_anet_transaction->id;
            $reservation->save();

            // Send reservation to ticketech
            try {
                $ticketech_guid = $this->ticketech->makeReservation($reservation);
            } catch (TicketechException $e) {
                $reservation->delete();
                throw $e;
            }
        }

        // loyalty redeemed validations and process
        if (
            isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed
        ) {
            // loyalty validations
            if (!$this->user->is_loyalty) {
                $reservation->delete();
                throw new NotFoundException('User not registered with loyalty program.');
            }

            $account = LoyaltyUserAccounts::where('user_id', $this->user->id)
                ->orderBy('id', 'DESC')
                ->first();

            // loyalty account validations
            if (!$account) {
                $reservation->delete();
                throw new NotFoundException('No account found associated with this user.');
            }
            $requestData = array(
                "sva" => $account->account_no,
                "amount" => $this->request->loyalty_points
            );

            $transactionData = array(
                'account_id' => $account->id,
                'reservation_id' => $reservation->id
            );
            $redeemResponse = LoyaltyProgram::redeemPoints($requestData, $this->user->id, $transactionData);

            if ($redeemResponse['success']) {
                $reservation->loyalty_point_used = $this->request->loyalty_points;
                $reservation->loyalty_amount_used = $this->request->loyalty_points / LoyaltyProgram::POINTS_REDEEM_RATIO;
                $reservation->save();
            } else {
                $reservation->delete();
                $msg = isset($redeemResponse['data']['status.description']) ? $redeemResponse['data']['status.description'] : 'Could not get accounts';
                throw new ApiGenericException($msg);
            }
        }
        // Send email to user
        $reservation->emailReservationToUser();

        return [
            'charge' => $charge,
            'reservation' => $reservation,
            'ticketech_guid' => $ticketech_guid
        ];
    }

    protected function updateReservationWithOverstay()
    {

        $reservation_id = $this->request->reservation_id;

        $useOverstay = (bool) $this->request->use_overstay;


        $reservation = Reservation::where('reservations.id', $reservation_id)->first();
        $currentDateTime = Carbon::now();
        $reservationEndDateTime = Carbon::parse($reservation->formatted_end_date_time);
        if ($currentDateTime->gte($reservationEndDateTime)) {
            throw new ApiGenericException('Reservation time duration has already expired', 422);
        }




        // Returns Rate or array if the facility base rate is returned. It will beneficial for furture partner of Icon
        // $rate = $this->facility->rateForReservation($this->request->arrival, $this->request->length, $useBonus, $useOverstay, $reservation_id);


        if (!$reservation) {
            throw new ApiGenericException('No reservation found in database with the reservation id.', 422);
        }

        // // Confirm that request rate matches database rate

        // // if ($rate['price'] != $this->request->total) {
        // $sentRate = $this->request->total;


        // if ($rate['price'] != $sentRate) {
        //     throw new ApiGenericException(
        //         'Sent rate does not match database rate, please reset reservation options and try again.',
        //         422,
        //         ['sent_rate' => $this->request->total, 'database_rate' => $rate['price']]
        //     );
        // }
        // print_r(json_encode($reservation));
        // die("Got it");

        $this->authNet
            ->setUser($this->user)
            ->isReservation()
            ->setFacility($this->facility)
            ->setBillingAddress($this->getBillingArray());


        if (isset($this->paymentProfileId)) { // Use our logged in users profile
            $this->authNet->setPaymentProfile($this->paymentProfileId);
        } else {  // Otherwise set transaction details manually
            $this->authNet
                ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
        }

        // Save reservation before sending so we have a reservation ID to attach to the auth net response


        $reservation = $this->updateReservation($reservation, $useOverstay);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

        try {
            // Use our database rate price to create the transaction
            $charge = $this->authNet->createTransaction(
                $reservation->overstay_rate,
                "{$reservationMode} {$reservation->id}, Ticketech Code {$reservation->ticketech_code}",
                config('icon.reservations_account')
            )->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
        } catch (Exception $e) {
            $reservation->delete();
            throw $e;
        }

        // Charge successful, save transaction relationship to it
        $transaction = $this->authNet->getTransaction();
        $reservation->total = $reservation->total + $reservation->overstay_rate;
        $reservation->anet_overstay_transaction_id = $transaction->id;

        $reservation->save();

        // Send reservation to ticketech
        try {

            $ticketech_guid = $this->ticketech->makeReservation($reservation);
        } catch (TicketechException $e) {
            // If ticketech returns an invalid response, void our transaction and pass the error on to the user
            $transaction = AuthorizeNetTransaction::where('anet_trans_id', $charge['anet_trans_id'])->first();
            $this->authNet->setTransaction($transaction)->void();
            $reservation->delete();
            throw $e;
        }

        // Send email to user
        $reservation->emailReservationExtensionToUser();

        return [
            'charge' => $charge,
            'reservation' => $reservation,
            'ticketech_guid' => $ticketech_guid
        ];
    }


    protected function updateReservationWithOverstayApplePay()
    {

        $reservation_id = $this->request->reservation_id;

        $useOverstay = (bool) $this->request->use_overstay;

        $reservation = Reservation::where('reservations.id', $reservation_id)->first();
        $currentDateTime = Carbon::now();
        $reservationEndDateTime = Carbon::parse($reservation->formatted_end_date_time);

        if ($currentDateTime->gte($reservationEndDateTime)) {
            throw new ApiGenericException('Reservation time duration has already expired', 422);
        }

        // Returns Rate or array if the facility base rate is returned. It will beneficial for furture partner of Icon
        // $rate = $this->facility->rateForReservation($this->request->arrival, $this->request->length, $useBonus, $useOverstay, $reservation_id);


        if (!$reservation) {
            throw new ApiGenericException('No reservation found in database with the reservation id.', 422);
        }


        // Save reservation before sending so we have a reservation ID to attach to the auth net response


        $reservation = $this->updateReservation($reservation, $useOverstay);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

        $transaction_id = 0;
        $charge = $this->makeApplePayTransaction($reservation, $reservation->overstay_rate);

        // Charge successful, save transaction relationship to it
        $transaction = $this->authNetApple->getTransaction();
        $reservation->anet_overstay_transaction_id = $transaction->id;
        $reservation->total = $reservation->total + $reservation->overstay_rate;
        $reservation->save();

        // Send reservation to ticketech
        try {

            $ticketech_guid = $this->ticketech->makeReservation($reservation);
        } catch (TicketechException $e) {
            // If ticketech returns an invalid response, void our transaction and pass the error on to the user
            $transaction = AuthorizeNetTransaction::where('anet_trans_id', $transaction_id)->first();
            $this->authNetApple->setTransaction($transaction)->void();
            $reservation->delete();
            throw $e;
        }

        // Send email to user
        $reservation->emailReservationExtensionToUser();

        return [
            'charge' => $charge,
            'reservation' => $reservation,
            'ticketech_guid' => $ticketech_guid
        ];
    }

    /**
     *  makeApple Pay payment
     */
    protected function makeApplePayTransaction($reservation, $amount = 0)
    {

        $this->authNetApple
            ->setUser($this->user)
            ->setFacility($this->facility)
            ->isReservation()
            ->setBillingAddress($this->getBillingArray());
        try {
            $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

            // Use our database rate price to create the transaction
            $charge = $this->authNetApple->createTransaction(
                $this->request->nonce,
                $amount,
                "{$reservationMode} {$reservation->id}, Ticketech Code {$reservation->ticketech_code}",
                config('icon.reservations_account')
            )->executeTransaction();
        } catch (Exception $e) {
            $reservation->delete();
            throw $e;
        }


        return $charge;
    }


    /**
     * Get the currently authorized user, or return an anonymous user
     * if the user is not logged in.
     *
     * @return [type] [description]
     */
    protected function getUser()
    {
        // Probably an easier way to get the current user with middleware - please let me know if there is
        if (\Authorizer::getResourceOwnerType() !== 'user') {
            throw new UserNotFound('No user found for this auth token.');
        }

        $id = \Authorizer::getResourceOwnerId();
        $user = User::find($id);

        if (!$user) {
            throw new UserNotFound('No user found for this auth token.');
        }

        return $user;
    }

    /**
     * Save the processed reservation
     *
     * @return [type] [description]
     */
    protected function saveReservation($rate, bool $useBonus): Reservation
    {
        $discount = 0.00;
        $discount_credit_used = 0.00;

        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $discount = $this->request->redeemed_amount;
        }

        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $discount_credit_used = $this->request->redeemed_amount_credit;
        }

        //verifying the company tracking id
        $companyAffilateId = 0;
        if (isset($this->request->companyName) && $this->request->companyName != '') {
            $companyAffilateDetails = CompanyAffilate::where('slug', $this->request->companyName)->first();
            if ($companyAffilateDetails) {
                $companyAffilateId = $companyAffilateDetails->id;
            }
        }

        $reservation = new Reservation(
            [
                'user_id' => $this->user->id,
                'facility_id' => $this->facility->id,
                'start_timestamp' => $this->request->arrival,
                'length' => $this->request->length,
                'discount' => $discount,
                'credit_used' => $discount_credit_used,
                'company_affilate_id' => $companyAffilateId
            ]
        );


        //for processing fee
        if ($this->facility->processing_fee > 0) {

            $reservation->processing_fee = $this->facility->processing_fee;
        }

        if (is_a($rate, Rate::class)) {
            $reservation->rate_id = $rate->id;
        }

        if (is_array($rate) && isset($rate['id'])) {
            $reservation->rate_id = $rate['id'];
        }

        if ($useBonus) {
            $reservation->applyBonus();
        }

        // If this is higher the apply bonus will get double applied
        $reservation->total = $this->request->total;

        $reservation->save();

        return $reservation;
    }


    /**
    /**
     * Save the processed reservation
     *
     * @return [type] [description]
     */
    protected function savePassReservation($rate, bool $useBonus): Reservation
    {
        $discount = 0.00;
        $discount_credit_used = 0.00;

        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $discount = $this->request->redeemed_amount;
        }

        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $discount_credit_used = $this->request->redeemed_amount_credit;
        }

        //verifying the company tracking id
        $companyAffilateId = 0;
        if (isset($this->request->companyName) && $this->request->companyName != '') {
            $companyAffilateDetails = CompanyAffilate::where('slug', $this->request->companyName)->first();
            if ($companyAffilateDetails) {
                $companyAffilateId = $companyAffilateDetails->id;
            }
        }

        $reservation = new Reservation(
            [
                'user_id' => $this->user->id,
                'facility_id' => $this->facility->id,
                'start_timestamp' => $this->request->arrival,
                'length' => $this->request->length,
                'discount' => $discount,
                'credit_used' => $discount_credit_used,
                'company_affilate_id' => $companyAffilateId
            ]
        );



        if (is_a($rate, Rate::class)) {
            $reservation->rate_id = $rate->id;
        }

        if (is_array($rate) && isset($rate['id'])) {
            $reservation->rate_id = $rate['id'];
        }

        if ($useBonus) {
            $reservation->applyBonus();
        }

        // If this is higher the apply bonus will get double applied
        $reservation->total = $this->request->total;

        $reservation->save();

        return $reservation;
    }

    /**
     * Update the processed reservation
     *
     * @return [type] [description]
     */
    protected function updateReservation(Reservation $reservation, bool $useOverstay): Reservation
    {
        if ($useOverstay) {
            $reservation->applyOverstay();
        }

        $reservation->save();

        return $reservation;
    }

    /**
     * Get billing address information in auth net form from the current request
     * Note we are currently only sending the user name, not the complete billing address
     *
     * @return [type] [description]
     */
    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }
    protected function getBillingName()
    {
        $name = $this->request->name_on_card ?: $this->user->name;

        $nameArray = explode(' ', trim($name));

        return (reset($nameArray) . " " . end($nameArray));
    }

    private function getDatesFromRange($start_date, $end_date, $date_format = 'Y-m-d')
    {
        $dates_array = array();
        for ($x = strtotime($start_date); $x <= strtotime($end_date); $x += 86400) {
            array_push($dates_array, date($date_format, $x));
        }

        return $dates_array;
    }

    //update Reservation Availibility 
    public function updateReservationAvailability(Request $request)
    {
        $this->log->info("updateReservationAvailability: " . json_encode($request->all()));
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $reservation = Reservation::where('id', $request->reservation_id)->first();
        $type = $request->type;

        $this->log->info("is_avaiability_updated: " . $reservation->is_avaiability_updated);

        if ($type == self::DEFAULT_CONFIRM_VAL) {
            $this->log->info("Type from: " . json_encode($request->all()));
            //check if it is not getting updating twice.
            if ($reservation->is_avaiability_updated == '1') {
                throw new ApiGenericException('Sorry, Availability can not be created now', 422);
            }
        }

        if ($type == self::DEFAULT_CANCEL_VAL) {
            //check if it is not getting updating twice.
            if ($reservation->is_avaiability_updated == '2') {
                throw new ApiGenericException('Sorry, Availability can not be cancel now', 422);
            }
        }

        if ($type == self::DEFAULT_CONFIRM_VAL) {
            $reservation->is_avaiability_updated = self::DEFAULT_CONFIRM_VAL;
        } else  if ($type == self::DEFAULT_CANCEL_VAL) {
            $reservation->is_avaiability_updated = self::DEFAULT_CANCEL_VAL;
        } else  if ($type == self::DEFAULT_UPDATED_VAL) {
            $reservation->is_avaiability_updated = self::DEFAULT_UPDATED_VAL;
        }
        $reservation->save();

        $facility_id = $reservation->facility_id;

        $date_time_in = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
        $length = round($reservation->length, 0);

        $date_time_out =  date('Y-m-d H:i:s', strtotime(Carbon::parse($date_time_in)->addHours($length)));
        $reservation_length = $reservation->length;
        if ($type == self::DEFAULT_UPDATED_VAL) {
            $resHistroy = ReservationHistroy::where('res_id', $request->reservation_id)->orderBy('id', 'DESC')->first(['altered_length']);
            // dd($resHistroy->altered_length);
            $this->log->info("Extend Reservation Actual Start Time: " . $date_time_in);
            $date_time_in =  date('Y-m-d H:i:s', strtotime(Carbon::parse($date_time_out)->subHours($resHistroy->altered_length)));
            $this->log->info("Extend Reservation Updated Start Time: " . $date_time_in);
            $date_time_out =  date('Y-m-d H:i:s', strtotime(Carbon::parse($date_time_in)->addHours($resHistroy->altered_length)));
            $this->log->info("Extend Reservation Updated End Time: " . $date_time_out);
            $reservation_length = $resHistroy->altered_length;
        }
        // dd($reservation->start_timestamp, $length, $resHistroy->altered_length, $date_time_in, $date_time_out);

        $reservation_minutes = 0;
        $reservation_hours = explode(".", $reservation_length);

        if (isset($reservation_hours[1]) && ($reservation_hours[1]) > 0) {
            $reservation_minutes = 30;
        }
        $reservation_date_time_out =  date('Y-m-d H:i:s', strtotime(Carbon::parse($date_time_in)->addHours($reservation_length)->addMinutes($reservation_minutes)));
        $this->log->info("Reservation Length: " . $reservation_length . " Minutes: " . $reservation_minutes . " Out Time: " . $reservation_date_time_out);


        $inventoryRepository = new Inventory();

        //check how many slots does entry and exit time occupies
        $difference = date_diff(date_create(date('Y-m-d', strtotime($date_time_in))), date_create(date('Y-m-d', strtotime(($date_time_out)))));

        //check if someone is parking for more than a day
        $this->log->info("Day count: " . $difference->d . "--- Condition: " . json_encode($difference->d > 0));
        if ($difference->d > 0) {

            $dates   = $inventoryRepository->generateArrayOfDates(
                ($difference->d + self::ADD_EXTRA_DAY_COUNT),
                date('Y-m-d H:i:s', strtotime($date_time_in))
            );
            $this->log->info("Dates data: " . json_encode($dates));

            $dayDifference = $difference->d;

            foreach ($dates as $key => $date) {

                $dayIn = date('w', strtotime($date->format('Y-m-d')));

                $hours = HoursOfOperation::where(['facility_id' => $facility_id, 'day_of_week' => $dayIn])->first();

                $startingHour = self::DEFAULT_HOURS;
                $endingHour   = self::TWENTY_FOUR_HOURS;

                if ($hours) {
                    $startingHour = date('G', strtotime($hours->open_time));
                    $endingHour   = date('G', strtotime($hours->close_time));
                }

                $facilityAvailability = FacilityAvailability::where(
                    ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')]
                )->first();
                $this->log->info("Reservation Minutes 1111: " . $reservation_minutes);
                if ($facilityAvailability) {
                    $this->log->info("Key: {$key} and Day Difference: {$dayDifference} Before Update Availability: " . $facilityAvailability->availability);
                    $inventory = json_decode($facilityAvailability->availability);
                    if ($key == 0) {
                        $this->log->info("Case 1: Availability");
                        /**
                         * because this is the first day in the dates provided
                         * we should remove 1 from each time_slot starting
                         * from the hour provided in the api call
                         */
                        $i = date('G', strtotime($date_time_in));
                        if ($startingHour > $i) {
                            $i = $startingHour;
                        }

                        //$loopEnd = ($endingHour < self::TWENTY_FOUR_HOURS) ? $endingHour : self::TWENTY_FOUR_HOURS;
                        $loopEnd  = self::TWENTY_FOUR_HOURS;
                        while ($i <= $loopEnd) {
                            if (isset($inventory->{$i})) {
                                if ($type == self::DEFAULT_CANCEL_VAL) {
                                    $inventory->{$i} = $inventory->{$i} + 1;
                                } else if ($type == self::DEFAULT_CONFIRM_VAL) {
                                    $inventory->{$i} = $inventory->{$i} - 1;
                                } else if ($type == self::DEFAULT_UPDATED_VAL) {
                                    if (isset($request->reservation_mode)) {
                                        if ($request->reservation_mode == '2') {
                                            $inventory->{$i} = $inventory->{$i} - 1;
                                        } else if ($request->reservation_mode == '1') {
                                            $inventory->{$i} = $inventory->{$i} + 1;
                                        }
                                    }
                                }
                            }
                            $i++;
                        }
                    } elseif ($key == $dayDifference) {
                        $this->log->info("Case 2: Availability");
                        $i = date('G', strtotime($date_time_out));
                        $minutes = date('i', strtotime($reservation_date_time_out));
                        $starting_minutes = date('i', strtotime($date_time_in));
                        if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
                            $i++;
                        }
                        /**
                         * because this is the last day in the dates provided
                         * we should remove 1 from each time_slot starting
                         * till the hour provided in the api call
                         */

                        $j  = 0;
                        while ($j < $i) {
                            if (isset($inventory->{$j})) {
                                if ($type == self::DEFAULT_CANCEL_VAL) {
                                    $inventory->{$j} = $inventory->{$j} + 1;
                                } else if ($type == self::DEFAULT_CONFIRM_VAL) {
                                    $inventory->{$j} = $inventory->{$j} - 1;
                                } else if ($type == self::DEFAULT_UPDATED_VAL) {
                                    if (isset($request->reservation_mode)) {
                                        if ($request->reservation_mode == '2') {
                                            $inventory->{$i} = $inventory->{$i} - 1;
                                        } else if ($request->reservation_mode == '1') {
                                            $inventory->{$i} = $inventory->{$i} + 1;
                                        }
                                    }
                                }
                            }
                            $j++;
                        }
                    } else {
                        $this->log->info("Case 3: Availability");
                        /**
                         * because this could be any day except first and last in
                         * the dates provided we should remove 1 from whole day
                         */

                        $k = 0;
                        while ($k <= self::TWENTY_FOUR_HOURS) {
                            if (isset($inventory->{$k})) {
                                if ($type == self::DEFAULT_CANCEL_VAL) {
                                    $inventory->{$k} = $inventory->{$k} + 1;
                                } else   if ($type == self::DEFAULT_CONFIRM_VAL) {
                                    $inventory->{$k} = $inventory->{$k} - 1;
                                } else if ($type == self::DEFAULT_UPDATED_VAL) {
                                    if (isset($request->reservation_mode)) {
                                        if ($request->reservation_mode == '2') {
                                            $inventory->{$k} = $inventory->{$k} - 1;
                                        } else if ($request->reservation_mode == '1') {
                                            $inventory->{$k} = $inventory->{$k} + 1;
                                        }
                                    }
                                }
                            }
                            $k++;
                        }
                    }

                    $facilityAvailability->availability = json_encode($inventory, JSON_FORCE_OBJECT);
                    $facilityAvailability->save();
                }
            }
        } else {
            $this->log->info("Same day");
            $startingHour = date('G', strtotime($date_time_in));
            $endingHour   = date('G', strtotime($date_time_out));
            $facilityAvailability     = FacilityAvailability::where(
                ['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))]
            )->first();

            if ($facilityAvailability) {
                $availability = json_decode($facilityAvailability->availability);
                $this->log->info("Before Update Availability: " . json_encode($availability));
                $minutes = date('i', strtotime($reservation_date_time_out));
                $starting_minutes = date('i', strtotime($date_time_in));
                if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
                    $endingHour++;
                }
                while ($startingHour < $endingHour) {

                    if ($type == self::DEFAULT_CANCEL_VAL) {
                        $availability->{$startingHour} = $availability->{$startingHour} + 1;
                    } else if ($type == self::DEFAULT_CONFIRM_VAL) {
                        $availability->{$startingHour} = $availability->{$startingHour} - 1;
                    } else if ($type == self::DEFAULT_UPDATED_VAL) {
                        if (isset($request->reservation_mode)) {
                            if ($request->reservation_mode == '2') {
                                $availability->{$startingHour} = $availability->{$startingHour} - 1;
                            } else if ($request->reservation_mode == '1') {
                                $availability->{$startingHour} = $availability->{$startingHour} + 1;
                            }
                        }
                    }
                    $startingHour++;
                }
                $this->log->info("After Update Availability: " . json_encode($availability, JSON_FORCE_OBJECT));
                $facilityAvailability->availability = json_encode($availability, JSON_FORCE_OBJECT);

                $facilityAvailability->save();
            }
        }

        // Commented by Lokesh and dissucss with Vikrant => Third Party Inventroy update in Icon
        //initialise the queue to update partner ineventory db
        // $updateJobParams = ['reservationId' => $request->reservation_id, 'type' => self::RESERVATION_TYPE];
        // $this->log->info("Cron Request: ". json_encode($updateJobParams));
        // Artisan::queue('cron:update-inventory', $updateJobParams);
        // $this->updateReservationAvailabilityPartner($request->reservation_id);
        // Return reservation and charge details to caller
        return [
            'is_avalibility_update' => true,
        ];
    }

    public function sendSmsAfterReservation(Request $request)
    {
        $reservation = Reservation::with('user')->where('ticketech_code', $request->ticketech_code)->first();

        if (!$reservation) {
            throw new ApiGenericException('No reservation found.');
        }
        if ($reservation->is_daily == '0') {
            $startLabel = "Enter After";
            $endLabel = "Exit Before";
        } else {
            $startLabel = "Start Date";
            $endLabel = "End Date";
        }


        //send sms to user
        $accountSid = env('TWILIO_ACCOUNT_SID');
        $authToken  = env('TWILIO_AUTH_TOKEN');
        $client = new Client($accountSid, $authToken);

        try {
            // Use the client to do fun stuff like send text messages!
            $client->messages->create(
                // the number you'd like to send the message to
                $reservation->user->phone,
                array(
                    // A Twilio phone number you purchased at twilio.com/console
                    'from' => env('TWILIO_PHONE'),
                    // the body of the text message you'd like to send
                    //'body' => "Fine"
                    'body' =>
                    "Thank you for booking with Spelman College. Your parking has been confirmed.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date, $reservation->formatted_start_time \n$endLabel : $reservation->formatted_end_date, $reservation->formatted_end_time \nAmount Charged : $$reservation->total"
                )
            );
        } catch (RestException $e) {
            throw new ApiGenericException($e->getMessage());
        }

        return "true";
    }


    public function checkUserPermit(Request $request)
    {
        $permitVehicle = PermitVehicle::with([
            'vehicles' => function ($query) {
                $query->where('grace_end_date', '>=', date("Y-m-d"));
            }
        ])->where('license_plate_number', $request->license_plate_number)->orderBy('id', 'DESC')->first();

        if (!$permitVehicle) {
            throw new ApiGenericException('No permit found.');
        }
        if (isset($permitVehicle->vehicles->grace_end_date)) {

            $today = strtotime(date("Y-m-d H:i:s"));
            $endDate = strtotime($permitVehicle->vehicles->grace_end_date);
            $permitVehicle->vehicles['is_expired'] = 0;
            if ($today > $endDate) {
                $permitVehicle->vehicles['is_expired'] = 1;
            }
            $permitVehicle->vehicles['desired_start_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_start_date);
            $permitVehicle->vehicles['desired_end_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_end_date);
            return $permitVehicle;
        } else {

            $permitVehicle = PermitVehicle::with('vehicles')->where('license_plate_number', $request->license_plate_number)->orderBy('id', 'DESC')->first();
            if (isset($permitVehicle->vehicles->grace_end_date)) {

                $today = strtotime(date("Y-m-d H:i:s"));
                $endDate = strtotime($permitVehicle->vehicles->grace_end_date);
                $permitVehicle->vehicles['is_expired'] = 0;
                if ($today > $endDate) {
                    $permitVehicle->vehicles['is_expired'] = 1;
                }
                $permitVehicle->vehicles['desired_start_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_start_date);
                $permitVehicle->vehicles['desired_end_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_end_date);
                return $permitVehicle;
            } else {
                throw new ApiGenericException('This permit has been expired or not valid.');
            }
        }
    }
    public function allPermitData(Request $request)
    {
        $query = PermitVehicle::query();

        //for date
        if (isset($request->license_plate_number) && ($request->license_plate_number != '')) {
            $query->where('license_plate_number', 'like', "{$request->license_plate_number}%");
        }
        //expired
        $today = date('Y-m-d');
        if (isset($request->expired_flag)) {
            if ($request->expired_flag == '1') {
                $query->whereHas('vehicles', function ($q) use ($today) {
                    $q->where('grace_end_date', '>', $today);
                });
            }
            if ($request->expired_flag == '2') {
                $query->whereHas('vehicles', function ($q) use ($today) {
                    $q->where('grace_end_date', '<=', $today);
                });
            }
        }

        $query->whereHas('vehicles', function ($q) {
            $q->whereNull('deleted_at');
        });

        $query->with('vehicles');
        return $query->paginate(20);


        //           $result->vehicles['desired_start_date'] = $this->getDaySufixFormat($result->vehicles->desired_start_date);
        //         $result->vehicles['desired_end_date'] = $this->getDaySufixFormat($result->vehicles->desired_end_date);





    }

    public function getDaySufixFormat($date)
    {
        $day = date('d', strtotime($date));
        $monthYear = date('F, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
        $second_last_digit = substr($number, -2, 1);
        $suffix = 'th';
        if ($second_last_digit != '1') {
            switch ($last_digit) {
                case '1':
                    $suffix = 'st';
                    break;
                case '2':
                    $suffix = 'nd';
                    break;
                case '3':
                    $suffix = 'rd';
                    break;
                default:
                    break;
            }
        }
        if ((string) $number === '1') $suffix = 'st';
        return $number . $suffix . ' ' . $monthYear;
    }


    public function getBookingList(Request $request)
    {
        set_time_limit(0);
        if (!in_array(Auth::user()->user_type, [1, 3, 4, 12,8,10])) {
            throw new UserNotAuthorized("You dont have permission to log in. Please contact your Admin for further details.");
        }
        $facility = [];
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
                $result  = [];
                $result["total"] = 0;
                $result["per_page"] = 20;
                $result["current_page"] = 1;
                $result["last_page"] = 1;
                $result["next_page_url"] = Null;
                $result["prev_page_url"] = Null;
                $result["from"] = Null;
                $result["to"] = Null;
                $result["data"] = [];
                $partner_id = 'all';
                //return $result;
            } else {
                if (Auth::user()->user_type == '4') {

                    $admin_partner_id = Auth::user()->created_by;
                    if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                        if ($request->partner_id != '') {
                            $partner_id = $request->partner_id;
                        } else {
                            $partner_id = 'all';
                        }
                    } else {
                        $partner_id = Auth::user()->created_by;
                    }
                    $facility = \DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                } else if (Auth::user()->user_type == '12') {
                    $partner_id = Auth::user()->created_by;
                    $facility = \DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                } else if (Auth::user()->user_type == '10') {
                    $partner_id = Auth::user()->created_by;
                    $facility = DB::table('business_facility_policy')->whereNull('deleted_at')->where('business_id',Auth::user()->business_id)->pluck('facility_id');
                } else if (Auth::user()->user_type == '8') {
                    $partner_id = Auth::user()->created_by;
                    $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date  = date('Y-' . $month . '-01');
            $to_date  = date('Y-' . $month . '-t');
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }
        else {
            $from_date = date('1969-' . $month . '-01');
            $to_date = date('Y-' . $month . '-t');
        }

        if ($partner_id == 'all') {
            $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'facility', 'mapcoQrCode', 'facility.facilityConfiguration']);
            // ->whereNotNull('anet_transaction_id');
            if ($request->booking_type == '0') {
                $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
            } elseif ($request->booking_type == '1') {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            } elseif ($request->booking_type == '2') {
                $reservation = $reservation->whereDate('end_timestamp', '>=', $from_date)->whereDate('end_timestamp', '<=', $to_date);
            } else {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            }
            if ($request->facility_id != '') {
                $reservation = $reservation->where('facility_id', $request->facility_id);
            }
            // Alka, PIMS-11381
            if (isset($request->payment_method) && !empty($request->payment_method)) {
                $reservation = $reservation->where('is_cash_payment', $request->payment_method);
            }
            if (isset($request->payment_type) && in_array($request->payment_type, ['0', '1', '2', '3'])) {
                $reservation = $this->paymentTypeFilter($reservation, $request);
            }
            if (isset($request->device_type) && !empty($request->device_type)) {
                $reservation = $reservation->where('device_type', $request->device_type);
            }
            // 
            if (isset($request->search)) {

                $reservation = $reservation->where(function ($query) use ($request, $partner_id) {
                    $query->where('ticketech_code', "like", "%" . $request->search . "%");
                    // $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%");
                    $query->orWhere("license_plate", 'like', "%$request->search%");
                    // $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%");
                    // $query->Where("partner_id", $partner_id);
                });
                $reservation = $reservation
                    // ->whereNotNull('anet_transaction_id')
                    ->orWhereHas(
                        'user',
                        function ($query) use ($request, $partner_id) {
                            $query
                                ->where('phone', 'like', "%{$request->search}%")
                                ->orWhere('email', 'like', "%{$request->search}%")
                                ->where('created_by', $partner_id);
                        }
                    );
                $reservation = $reservation
                    // ->whereNotNull('anet_transaction_id')
                    ->orWhereHas(
                        'userPass',
                        function ($query) use ($request, $partner_id) {
                            $query
                                ->where('pass_code', 'like', "%{$request->search}%");
                        }
                    );
                $reservation = $reservation
                    // ->whereNotNull('anet_transaction_id')
                    ->orWhereHas(
                        'transaction',
                        function ($query) use ($request) {
                            $query
                                ->where('ref_id', 'like', "%{$request->search}%");
                        }
                    );
            }

            if ($request->sort != '') {
                if ($request->sort == 'formatted_start_date_time') {
                    $reservation = $reservation
                        // ->whereNotNull('anet_transaction_id')
                        ->orderBy('start_timestamp', $request->sortBy);
                } else if ($request->sort == 'formatted_end_date_time') {
                    $reservation = $reservation
                        // ->whereNotNull('anet_transaction_id')
                        ->orderBy('end_timestamp', $request->sortBy);
                } else {
                    $reservation = $reservation
                        // ->whereNotNull('anet_transaction_id')
                        ->orderBy($request->sort, $request->sortBy);
                }
            } else {
                $reservation = $reservation
                    // ->whereNotNull('anet_transaction_id')
                    ->orderBy('id', 'DESC');
            }
            if ($request->event_id) {
                $reservation = $reservation->where('event_id', $request->event_id);
            }
            $reservation = $reservation->paginate(20);
            return $reservation;
        }

        if ($partner_id ==  config('parkengage.PARTNER_AAA')) {  // 
            // if(in_array($partner_id, array(356808,358642,359890))){
            if ($request->facility_id != '') {
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'facility', 'facility.facilityConfiguration'])->where(function ($query) {
                    //$query->whereNotNull('thirdparty_integration_id');
                    // ->orWhereNotNull('anet_transaction_id');
                })->where("facility_id", $request->facility_id);
            } else {
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'facility', 'facility.facilityConfiguration'])->where(function ($query) {
                    // $query->whereNotNull('thirdparty_integration_id');
                    // ->orWhereNotNull('anet_transaction_id');
                });
            }
            // dd($reservation->get()->toArray());

            if (isset($request->search)) {
                $reservation = $reservation->where(function ($query) use ($request, $partner_id) {
                    $query->where('ticketech_code', "like", "%" . $request->search . "%");
                    // $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%");
                    $query->orWhere("license_plate", 'like', "%$request->search%");
                    // $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%");
                    $query->Where("partner_id", $partner_id);
                });
                $reservation = $reservation->orWhereHas(

                    'user',
                    function ($query) use ($request, $partner_id) {
                        $query
                            ->where('created_by', $partner_id)
                            ->where('phone', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%");
                    }
                )->where('facility_id', $request->facility_id);
                $reservation = $reservation->orWhereHas(
                    'userPass',
                    function ($query) use ($request, $partner_id) {
                        $query
                            ->where('pass_code', 'like', "%{$request->search}%")
                            ->where('partner_id', $partner_id);
                    }
                )->where('facility_id', $request->facility_id);
                $reservation = $reservation->orWhereHas(
                    'transaction',
                    function ($query) use ($request) {
                        $query->where('ref_id', 'like', "%{$request->search}%");
                    }
                );
            }
            if (isset($request->payment_method) && !empty($request->payment_method)) {
                $reservation = $reservation->where('is_cash_payment', $request->payment_method);
            }
            if (isset($request->payment_type) && in_array($request->payment_type, ['0', '1', '2', '3'])) {
                $reservation = $this->paymentTypeFilter($reservation, $request);
            }

            if ($request->facility_id != '') {
                $reservation = $reservation->where(function ($query) use ($request) {
                    $query->where('facility_id', $request->facility_id);
                });
            }


            if (Auth::user()->user_type == '4') {
                $admin_partner_id = Auth::user()->created_by;
                if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                    if ($request->facility_id != '') {
                        $reservation = $reservation->where('facility_id', $request->facility_id);
                    }
                } else {
                    $reservation = $reservation->where(function ($query) use ($facility) {
                        $query->whereIn('facility_id', $facility);
                    });
                }
            } else if (Auth::user()->user_type == '12') {
                $reservation = $reservation->where(function ($query) use ($facility) {
                    $query->whereIn('facility_id', $facility);
                });
            }

            $reservation = $reservation->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
            if ($request->booking_type == '0') {
                $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
            } elseif ($request->booking_type == '1') {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            } elseif ($request->booking_type == '2') {
                $reservation = $reservation->whereDate('end_timestamp', '>=', $from_date)->whereDate('end_timestamp', '<=', $to_date);
            } else {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            }
            if ($request->event_id) {
                $reservation = $reservation->where('event_id', $request->event_id);
            }
            if ($request->sort != '') {
                if ($request->sort == 'formatted_start_date_time') {
                    $reservation = $reservation
                        // ->whereNotNull('anet_transaction_id')
                        ->orderBy('start_timestamp', $request->sortBy);
                } else if ($request->sort == 'formatted_end_date_time') {
                    $reservation = $reservation
                        // ->whereNotNull('anet_transaction_id')
                        ->orderBy('end_timestamp', $request->sortBy);
                } else {
                    $reservation = $reservation
                        // ->whereNotNull('anet_transaction_id')
                        ->orderBy($request->sort, $request->sortBy);
                }
            } else {
                $reservation = $reservation->orderBy('id', 'DESC');
            }

            //   $reservation = $reservation->where('partner_id', $partner_id);
            $reservation = $reservation->paginate(20);
        } else if ($partner_id == config('parkengage.PARTNER_PCI') || $partner_id == config('parkengage.PARTNER_COLONIAL') || $partner_id == config('parkengage.PARTNER_UNITED') || $partner_id == config('parkengage.PARTNER_PE_DEMO')) {  // 
            if ($request->facility_id != '') {
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'facility', 'mapcoQrCode', 'facility.facilityConfiguration'])->where(function ($query) {
                    //   $query->orWhereNotNull('thirdparty_integration_id')->orWhereNotNull('anet_transaction_id');
                })->where("facility_id", $request->facility_id);
            } else {
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'facility', 'mapcoQrCode', 'facility.facilityConfiguration'])->where(function ($query) {
                    // $query->orWhereNotNull('thirdparty_integration_id')->orWhereNotNull('anet_transaction_id');
                });
            }
            if (isset($request->search)) {

                $reservation = $reservation->where(function ($query) use ($request, $partner_id) {
                    $query->where('ticketech_code', "like", "%" . $request->search . "%");
                    // $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%");
                    $query->orWhere("license_plate", 'like', "%$request->search%");
                    // $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%");
                    $query->Where("partner_id", $partner_id);
                });

                $reservation = $reservation->orWhereHas(
                    'user',
                    function ($query) use ($request, $partner_id) {
                        $query
                            ->where('phone', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%")
                            ->where('created_by', $partner_id);
                    }
                );
                $reservation = $reservation->orWhereHas(
                    'userPass',
                    function ($query) use ($request, $partner_id) {
                        $query
                            ->where('pass_code', 'like', "%{$request->search}%")
                            ->where('partner_id', $partner_id);
                    }
                );
                $reservation = $reservation->orWhereHas(
                    'transaction',
                    function ($query) use ($request) {
                        $query->where('ref_id', 'like', "%{$request->search}%");
                    }
                );
            }

            if (isset($request->payment_method) && !empty($request->payment_method)) {
                $reservation = $reservation->where('is_cash_payment', $request->payment_method);
            }

            if (isset($request->payment_type) && in_array($request->payment_type, ['0', '1', '2', '3'])) {
                $reservation = $this->paymentTypeFilter($reservation, $request);
            }
            if (isset($partner_id) && !empty($partner_id)) {
                $reservation = $reservation->where('partner_id', $partner_id);
            }
            if ($request->facility_id != '') {
                $reservation = $reservation->where('facility_id', $request->facility_id);
            }
            if (Auth::user()->user_type == '4') {
                $admin_partner_id = Auth::user()->created_by;
                if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                    if ($request->facility_id != '') {
                        $reservation = $reservation->where('facility_id', $request->facility_id);
                    }
                } else {
                    $reservation = $reservation->where(function ($query) use ($facility) {
                        $query->whereIn('facility_id', $facility);
                    });
                }
            } else if (Auth::user()->user_type == '12') {
                $reservation = $reservation->where(function ($query) use ($facility) {
                    $query->whereIn('facility_id', $facility);
                });
            }

            $reservation = $reservation->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
            if ($request->booking_type == '0') {
                $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
            } elseif ($request->booking_type == '1') {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            } elseif ($request->booking_type == '2') {
                $reservation = $reservation->whereDate('end_timestamp', '>=', $from_date)->whereDate('end_timestamp', '<=', $to_date);
            } else {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            }
            if ($request->event_id) {
                $reservation = $reservation->where('event_id', $request->event_id);
            }
            if ($request->sort != '') {
                if ($request->sort == 'formatted_start_date_time') {
                    $reservation = $reservation
                        // ->whereNotNull('anet_transaction_id')
                        ->orderBy('start_timestamp', $request->sortBy);
                } else if ($request->sort == 'formatted_end_date_time') {
                    $reservation = $reservation
                        // ->whereNotNull('anet_transaction_id')
                        ->orderBy('end_timestamp', $request->sortBy);
                } else {
                    $reservation = $reservation
                        // ->whereNotNull('anet_transaction_id')
                        ->orderBy($request->sort, $request->sortBy);
                }
            } else {
                $reservation = $reservation->orderBy('id', 'DESC');
            }
            //dd($reservation->tosql());
            $reservation = $reservation->paginate(20);
        } else if ($partner_id == config('parkengage.PARTNER_MAPCO')) {
            if ($request->facility_id != '') {
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'facility', 'facility.facilityConfiguration', 'mapcoQrCode']);
                // if ($request->facility_id == config('parkengage.MAPCO_CIVIC_CENTER_FACILITY')) {
                //     $reservation = $reservation->whereNotNull('anet_transaction_id');
                // }
                $reservation = $reservation->where("facility_id", $request->facility_id);
            } else {
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'facility', 'facility.facilityConfiguration', 'mapcoQrCode']);
                // ->whereNotNull('anet_transaction_id');
            }

            if (isset($request->search)) {

                $reservation = $reservation->where(function ($query) use ($request, $partner_id) {
                    $query->where('ticketech_code', "like", "%" . $request->search . "%");
                    // $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%");
                    $query->orWhere("license_plate", 'like', "%$request->search%");
                    // $query->orWhere("users.phone", 'like', "%$request->search%");
                    // $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%");
                    $query->Where("partner_id", $partner_id);
                });

                $reservation = $reservation
                    // ->whereNotNull('anet_transaction_id')
                    ->orWhereHas(
                        'user',
                        function ($query) use ($request, $partner_id) {
                            $query
                                ->where('phone', 'like', "%{$request->search}%")
                                ->orWhere('email', 'like', "%{$request->search}%")
                                ->where('created_by', $partner_id);
                        }
                    );
                $reservation = $reservation
                    // ->whereNotNull('anet_transaction_id')
                    ->orWhereHas(
                        'userPass',
                        function ($query) use ($request, $partner_id) {
                            $query
                                ->where('pass_code', 'like', "%{$request->search}%")
                                ->where('partner_id', $partner_id);
                        }
                    );
                $reservation = $reservation
                    // ->whereNotNull('anet_transaction_id')
                    ->orWhereHas(
                        'transaction',
                        function ($query) use ($request) {
                            $query
                                ->where('ref_id', 'like', "%{$request->search}%");
                        }
                    );
            }
            if (isset($request->payment_method) && !empty($request->payment_method)) {
                $reservation = $reservation->where('is_cash_payment', $request->payment_method);
            }

            if (isset($request->payment_type) && in_array($request->payment_type, ['0', '1', '2', '3'])) {
                $reservation = $this->paymentTypeFilter($reservation, $request);
            }
            if (isset($request->device_type) && !empty($request->device_type)) {
                $reservation = $reservation->where('device_type', $request->device_type);
            }

            if ($request->facility_id != '') {
                $reservation = $reservation->where('facility_id', $request->facility_id);
            }
            if (Auth::user()->user_type == '4') {
                $admin_partner_id = Auth::user()->created_by;
                if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                    if ($request->facility_id != '') {
                        $reservation = $reservation->where('facility_id', $request->facility_id);
                    }
                } else {
                    $reservation = $reservation->where(function ($query) use ($facility) {
                        $query->whereIn('facility_id', $facility);
                    });
                }
            } else if (Auth::user()->user_type == '12') {
                $reservation = $reservation->where(function ($query) use ($facility) {
                    $query->whereIn('facility_id', $facility);
                });
            }
            if ($request->booking_type == '0') {
                if(isset($request->from_date) && $request->from_date != '' && isset($request->to_date) && $request->to_date != '') {
                    $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
                }
            } elseif ($request->booking_type == '1') {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            } elseif ($request->booking_type == '2') {
                $reservation = $reservation->whereDate('end_timestamp', '>=', $from_date)->whereDate('end_timestamp', '<=', $to_date);
            } else {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            }

            $reservation = $reservation->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
            if ($request->event_id) {
                $reservation = $reservation->where('event_id', $request->event_id);
            }
            if ($request->sort != '') {
                if ($request->sort == 'formatted_start_date_time') {
                    $reservation = $reservation->orderBy('start_timestamp', $request->sortBy);
                } else if ($request->sort == 'formatted_end_date_time') {
                    $reservation = $reservation->orderBy('end_timestamp', $request->sortBy);
                } else if ($request->sort == 'reservation_start_timestamp') {
                    $reservation = $reservation->orderBy('start_timestamp', $request->sortBy);
                } else if ($request->sort == 'reservation_end_timestamp') {
                    $reservation = $reservation->orderBy('end_timestamp', $request->sortBy);
                } else {
                    $reservation = $reservation->orderBy($request->sort, $request->sortBy);
                }
            } else {
                $reservation = $reservation->orderBy('id', 'DESC');
            }

            if (Auth::user()->user_type == '4') {
                $admin_partner_id = Auth::user()->created_by;
                if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                    if ($request->facility_id != '') {
                        $reservation = $reservation->where('facility_id', $request->facility_id);
                    }
                } else {
                    $reservation = $reservation->where(function ($query) use ($facility) {
                        $query->whereIn('facility_id', $facility);
                    });
                }
            } else  if (Auth::user()->user_type == '12') {
                $reservation = $reservation->where(function ($query) use ($facility) {
                    $query->whereIn('facility_id', $facility);
                });
            }

            $reservation = $reservation->paginate(20);
        } else {
            set_time_limit(0);
            $today = $request->from_date;

            $todayQrCode = [];
            if ($request->booking_type == '0') {
            } else {
                if ($request->booking_type == '') {
                    $event = Event::whereDate('start_time', '=', $from_date)->where("is_active", '1')->get();
                } else {
                    $event = Event::whereDate('start_time', '>=', $from_date)->whereDate('start_time', '<=', $to_date)
                        ->where("is_active", '1')
                        ->get();
                }
            }
            //dd($todayQrCode);
            if ($request->facility_id != '') {
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'mapcoQrCode', 'facility', 'facility.facilityConfiguration'])
                    // ->whereNotNull('anet_transaction_id')
                    ->where("facility_id", $request->facility_id);
            } else {
                $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'mapcoQrCode', 'facility', 'facility.facilityConfiguration']);
                // ->whereNotNull('anet_transaction_id');
            }
            if (isset($partner_id) && !empty($partner_id)) {
                $reservation = $reservation->where('partner_id', $partner_id);
            }


            if (isset($request->search)) {
                // $reservation = $reservation->where('ticketech_code', 'like', "%$request->search%")->where('partner_id', $partner_id);
                $reservation = $reservation->where(function ($query) use ($request, $partner_id) {
                    $query->where('ticketech_code', "like", "%" . $request->search . "%");
                    // $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%");
                    $query->orWhere("license_plate", 'like', "%$request->search%");
                    // $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%");
                    $query->Where("partner_id", $partner_id);
                });
                $reservation = $reservation
                    // ->whereNotNull('anet_transaction_id')
                    ->orWhereHas(
                        'user',
                        function ($query) use ($request, $partner_id) {
                            $query
                                ->where('phone', 'like', "%{$request->search}%")
                                ->orWhere('email', 'like', "%{$request->search}%")
                                ->where('created_by', $partner_id);
                        }
                    );
                $reservation = $reservation
                    // ->whereNotNull('anet_transaction_id')
                    ->orWhereHas(
                        'transaction',
                        function ($query) use ($request) {
                            $query
                                ->where('ref_id', 'like', "%{$request->search}%");
                        }
                    );
            }

            // Alka, PIMS-11381
            if (isset($request->payment_method) && !empty($request->payment_method)) {
                $reservation = $reservation->where('is_cash_payment', $request->payment_method);
            }

            if (isset($request->payment_type) && in_array($request->payment_type, ['0', '1', '2', '3'])) {
                $reservation = $this->paymentTypeFilter($reservation, $request);
            }

            if (isset($request->device_type) && !empty($request->device_type)) {
                $reservation = $reservation->where('device_type', $request->device_type);
            }
            // End

            if ($request->booking_type == '0') {
                if(isset($request->from_date) && $request->from_date != '' && isset($request->to_date) && $request->to_date != '') {
                    $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
                }
            } else if ($request->booking_type == '1') {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            } else {
                $reservation = $reservation->whereDate('end_timestamp', '>=', $from_date)->whereDate('end_timestamp', '<=', $to_date);
            }

            //dd($todayQrCode);
            $reservation = $reservation->where(function ($query) use ($partner_id, $todayQrCode) {
                $query->where('partner_id', $partner_id);
                if (count($todayQrCode) > 0) {
                    $query->whereIn('id', $todayQrCode);
                }
            });

            if (isset($facility) && !empty($facility)) {
                $reservation = $reservation->where(function ($query) use ($facility) {
                    $query->whereIn('facility_id', $facility);
                });
            }
            if ($request->event_id) {
                $reservation = $reservation->where('event_id', $request->event_id);
            }
            if ($request->sort != '') {
                if ($request->sort == 'formatted_start_date_time') {
                    $reservation = $reservation->orderBy('start_timestamp', $request->sortBy);
                } else if ($request->sort == 'formatted_end_date_time') {
                    $reservation = $reservation->orderBy('end_timestamp', $request->sortBy);
                } else {
                    $reservation = $reservation->orderBy($request->sort, $request->sortBy);
                }
            } else {
                $reservation = $reservation->orderBy('id', 'DESC');
            }

            if (Auth::user()->user_type == '4') {
                $admin_partner_id = Auth::user()->created_by;
                if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                    if ($request->facility_id != '') {
                        $reservation = $reservation->where('facility_id', $request->facility_id);
                    }
                } else {
                    $reservation = $reservation->where(function ($query) use ($facility) {
                        $query->whereIn('facility_id', $facility);
                    });
                }
            } else if (Auth::user()->user_type == '12') {
                $reservation = $reservation->where(function ($query) use ($facility) {
                    $query->whereIn('facility_id', $facility);
                });
            }
            set_time_limit(0);
            $reservation = $reservation->paginate(20);

            if ($request->booking_type == '0') {
            } else {
                if (count($reservation) > 0) {
                    foreach ($reservation as $key => $value) {
                        if (isset($value->mapcoQrCode)) {
                            foreach ($value->mapcoQrCode as $k => $v) {
                                if ($v->event_id != '0') {
                                    //$event = Event::where("id",$v->event_id)->where("is_active", '1')->first();
                                    if (count($event) > 0) {
                                        foreach ($event as $eventKey => $eventValue) {
                                            if (strtotime(date("Y-m-d", strtotime($eventValue->start_time))) == strtotime($today)) {
                                                $reservation[$key]['start_timestamp'] = $eventValue->start_time;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($request->sort == 'phone') {
            if (count($reservation) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->phone > $reservation[$j]['user']->phone) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->phone < $reservation[$j]['user']->phone) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                }
            }
        }
        if ($request->sort == 'email') {
            if (count($reservation) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->email > $reservation[$j]['user']->email) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->email < $reservation[$j]['user']->email) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                }
            }
        }

        
        if ($partner_id == config('parkengage.PARTNER_MAPCO')) {
            if (count($reservation) > 0) {
                foreach($reservation as $key=>$value){
                    if($value->facility_id == config('parkengage.MAPCO_CIVIC_CENTER_FACILITY')){
                        if($value->anet_transaction_id == ""){
                            unset($reservation[$key]);
                        }
                        
                    }
                }
            }
        }
        
        return $reservation;
    }


    public function showQrCodeImage($photo)
    {
        if (!$photo) {
            throw new NotFoundException('No image with that name found.');
        }

        $file = Storage::disk('local')->get($photo) ?: null;
        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/' . $photo));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }

    public function downloadBookingPdf($id)
    {
        $this->log->info("Download PDF with Id: $id");
        $data  = Reservation::with(['transaction', 'facility', 'facility.facilityConfiguration', 'facility.photos', 'user', 'rate', 'mapcoQrCode.event', 'mapcoQrCode.eventCategory.eventCategoryEvent.event'])->where('id', $id)->first();
        if ($data) {
            $cruise = Cruise::select('id', 'cruise_name')->where('partner_id', $data->partner_id)->where('id', $data->cruise_id)->first();
            if ($cruise) {
                $data->cruise_name = isset($cruise->cruise_name) ? $cruise->cruise_name : '';
                //CruiseSchedule
                $cruiseSchedule = CruiseSchedule::where('cruise_id', $cruise->id)->where('is_active', 1)->whereNull('deleted_at')->first();
                $data->cruiseSchedule = isset($cruiseSchedule) ? $cruiseSchedule : '';
            }
        }
        $this->log->info("Reached 11");

        $brand_setting = BrandSetting::where('user_id', $data->partner_id)->first();
        $rgb_color = json_decode($brand_setting->rgb_color, true);
        $background_color = 'rgba(' . $rgb_color['r'] . ',' . $rgb_color['g'] . ',' . $rgb_color['b'] . ',' . $rgb_color['a'] . ')';
        $brand_setting->rgb_color = $background_color;
        if (!is_null($data) && count($data->facility) > 0) {
            $this->log->info("Reached 22");
            $facility_brand_setting = FacilityBrandSetting::where('facility_id', $data->facility->id)->first();
            if ($facility_brand_setting) {
                $this->log->info("Reached 33");
                $data->facility_logo_id = ($facility_brand_setting->id) ? $facility_brand_setting->id : '';
                $rgb_color = json_decode($facility_brand_setting->rgb_color, true);
                $background_color = 'rgba(' . $rgb_color['r'] . ',' . $rgb_color['g'] . ',' . $rgb_color['b'] . ',' . $rgb_color['a'] . ')';
                $brand_setting->rgb_color = $background_color;
                $data->brandSetting = $facility_brand_setting;
                $data->brandSetting->rgb_color = $background_color;
            } else {
                $this->log->info("Reached 44");

                $data->brandSetting = $brand_setting;
                $data->brandSetting->rgb_color = $background_color;
            }
        } else {
            $this->log->info("Reached 55");
            $data->brandSetting = $brand_setting;
            $data->brandSetting->rgb_color = $background_color;
        }
        $end_time = Carbon::parse($data->start_timestamp)->addHours($data->length);
        if (intval($data->length) != $data->length) {
            $this->log->info("Reached 66");
            $timarr = explode('.', $data->length);
            $end_time->addMinutes($timarr[1]);
        }
        $end_time = $end_time->subSecond()->format('Y-m-d H:i:s');
        $data->end_time = $end_time;
        $data->message = "Booking Confirmation";
        $data->addressLink = $data->facility->generateAddressLink();
        $data->hours = $data->facility->hoursOfOperation;
        $pdfName = '';
        // $template = PlatformNotification::with('notificationType')->where('slug_name', 'reservation')->first();
        // $data->template = $template;
        $imageBarcodeFileName = str_random(10) . '_common.png';

        $barcode = "";
        if (isset($data->thirdparty_code) && $data->thirdparty_code != '') {
            $this->log->info("Reached 77");
            $barcode = $data->thirdparty_code;
        } else {
            $this->log->info("Reached 88");
            $barcode = $data->ticketech_code;
        }

        if (isset($data->on_behalf) && !empty($data->on_behalf)) {
            $email = User::where('id', $data->on_behalf)->pluck('email')->first();
            $data->email_on_behalf = $email;
        }
        
        Storage::put($imageBarcodeFileName, $this->generateBarcodeJpgNew($barcode));
        $data->updated_qrcode = $imageBarcodeFileName;
        $this->log->info("Reached 99");

        // Cancel and Modifiy Buttons
        $url = env('CANCEL_RESERVATION');

        $facility = $data->facility;

        $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $data->facility->owner_id)->first();
        if (in_array($checkPaymentUrl->user_id, [config('parkengage.PARTNER_PCI'), config('parkengage.PARTNER_MAPCO')])) {
            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                $join->on('user_facilities.user_id', '=', 'users.id');
                $join->where('user_facilities.facility_id', "=", $facility->id);
            })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();

            if (isset($getRM->slug)) {
                $slug = ($getRM->slug) ? $getRM->slug : '';
            } else {
                $slug = ($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : 'pay';
            }
        } else {
            $slug = ($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : 'pay';
        }
        $this->log->info("Reached 109");

        $discount = $data->discount;
        if ($discount == 0) {
            $discount = $data->getDiscountedValue($data->id);
            $data->discount = $discount;
        }

        // Lokesh: 28-Jun-2024 View logic Implemented in Controller
        if ($facility->is_service_update) {
            $edit_url = "$url/$slug/reservation/" . base64_encode($data->id);
            $data->edit_url = $edit_url;

            $cancel_url = "$url/$slug/cancel/" . base64_encode($data->id);
            $data->cancel_url = $cancel_url;

            // $data->total =  $data->parking_amount;

        } else {
            if ($data->reservation_amount && !empty($data->reservation_amount) && $data->reservation_amount != '0.00') {
                $reservation_amount = $data->reservation_amount;
                if ($discount > 0) {
                    $reservation_amount -= $discount;
                    $data->total = $reservation_amount;
                } else {
                    $data->total = $reservation_amount;
                }
            } else if ($data->total) {
                $data->total = $data->total;
            }
        }
        // Mobile number format
        if (isset($data->user->phone)) {
            $phone = QueryBuilder::formatPhoneNumber(substr($data->user->phone, -10), false);
            $data->user->phone = $phone;
        }

        $this->log->info("Discount: $discount and Total: {$data->total}");
        $pdf = (new MapcoQrcode())->generateEmailPdf($data, Pdf::class);

        return $pdf;
    }

    public function generateBarcodeJpgNew($qrcode)
    {
        $html = $this->generateBarcodeHtml($qrcode);

        $image = app()->make(Image::class);
        $image->setOption('width', '420');
        return $image->getOutputFromHtmlString($html);
    }

    public function generateBarcodeHtml($qrcode)
    {
        return view('platform.email.Qrcodegenerate', ['barcode' => $qrcode]);
    }

    public function getScanHistory()
    {
        if (in_array(Auth::user()->user_type, [self::ATTENDANT, self::SUBORDINATE])) {
            $id = Auth::user()->id;
            // $facility_ids = \DB::table('user_facilities')->where('user_id', $id)->whereNull('deleted_at')->pluck('facility_id');
            // $eventsId = \DB::table('event_facility')->whereIn('facility_id', $facility_ids)->pluck('event_id');
            $eventsId = Reservation::where('event_user_id', $id)->orderBy('id', 'desc')->pluck('event_id');

            //->where('is_active',1)
            $events = Event::whereIn('id', $eventsId)->get();

            if (!$events) {
                throw new NotFoundException('Event Not Found');
            }
            foreach ($events as $key => $val) {

                //  $eventReservation = MapcoQrcode::whereIn('event_id', [$val['id']])->pluck('reservation_id');
                // ->whereIn('id', $eventReservation)
                $reservation = Reservation::with(['facility.facilityConfiguration', 'mapcoQrCode.event', 'user', 'transaction', 'ticket'])->where('event_user_id', $id)->limit(15)->orderBy('id', 'desc')->get();
                foreach ($reservation as $key => $value) {
                    # code...
                    $this->facility = Facility::with('facilityConfiguration')->where('id', $value->facility_id)->where('owner_id', $value->partner_id)->first();
                    $user_reciept_url = $this->facility->user_receipt_url;
                    if (isset($this->facility->facilityConfiguration) && $this->facility->facilityConfiguration->is_sbm_event_qrcode == '1') {
                        $value->print_reciept_url = $value->mapcoQrCode[0]->qrcode;
                    } else {
                        if ($value->ticket) {
                            $value->print_reciept_url = $user_reciept_url . $value->ticket->ticket_number;
                        } else {
                            $value->print_reciept_url = $value->mapcoQrCode[0]->qrcode;
                        }
                    }
                }

                if ($reservation) {
                    $val['reservations'] =  $reservation;
                }
            }
            return $events;
        } else {
            throw new UserNotAuthorized('User Not Authorize');
        }
    }

    public function getScanHistoryNew()
    {
        if (in_array(Auth::user()->user_type, [self::ATTENDANT, self::SUBORDINATE])) {
            $id = Auth::user()->id;
            // $facility_ids = \DB::table('user_facilities')->where('user_id', $id)->whereNull('deleted_at')->pluck('facility_id');
            // $eventsId = \DB::table('event_facility')->whereIn('facility_id', $facility_ids)->pluck('event_id');
            $eventsId = Ticket::where('event_user_id', $id)->whereNotNull('event_id')->orderBy('id', 'desc')->pluck('event_id');

            //->where('is_active',1)
            $events = Event::whereIn('id', $eventsId)->get();

            if (!$events) {
                throw new NotFoundException('Event Not Found');
            }
            /*
            foreach ($events as $key => $val) {

              //  $eventReservation = MapcoQrcode::whereIn('event_id', [$val['id']])->pluck('reservation_id');
				// ->whereIn('id', $eventReservation)
                $reservation = Reservation::with(['facility.facilityConfiguration', 'mapcoQrCode.event', 'user', 'transaction', 'ticket'])->where('event_user_id', $id)->limit(15)->orderBy('id', 'desc')->get();
                foreach ($reservation as $key => $value) {
					# code...
                    $this->facility = Facility::with('facilityConfiguration')->where('id', $value->facility_id)->where('owner_id', $value->partner_id)->first();
                    $user_reciept_url = $this->facility->user_receipt_url;
                    if(isset($this->facility->facilityConfiguration) && $this->facility->facilityConfiguration->is_sbm_event_qrcode=='1'){
                        $value->print_reciept_url = $value->mapcoQrCode[0]->qrcode;  
                    }else{
						if($value->ticket){
							$value->print_reciept_url = $user_reciept_url.$value->ticket->ticket_number;	
						}else{
							$value->print_reciept_url = $value->mapcoQrCode[0]->qrcode;
						}  
                    }
                }

                if ($reservation) {
                    $val['reservations'] =  $reservation;
                }
            }
            */
            return $events;
        } else {
            throw new UserNotAuthorized('User Not Authorize');
        }
    }

    public function getScanHistoryByEvent($event_id)
    {

        if (in_array(Auth::user()->user_type, [self::ATTENDANT, self::SUBORDINATE])) {
            $id = Auth::user()->id;
            $events = Event::where('id', $event_id)->first();
			
            if (!$events) {
                throw new NotFoundException('Event Not Found');
            }
			
			$ticket = Ticket::with(['facility.facilityConfiguration', 'user', 'transaction','reservation'])->where('event_user_id', $id)->where('event_id', $event_id)->orderBy('id', 'desc')->get();
            $new_details = []; 
			foreach ($ticket as $key => $value) {
			   //dd($value->facility,$value->facility->user_receipt_url);
			   
				# code...
				$user_reciept_url = $value->facility->user_receipt_url;
				if (isset($value->facility->facilityConfiguration) && ($value->facility->facilityConfiguration->is_sbm_event_qrcode == '1' || $value->facility->facilityConfiguration->is_sbm_event_qrcode == '2')) {
					$new_details[$key]['print_reciept_url'] = $value->ticket_security_code;
				} else {
					if ($value->ticket) {
						$new_details[$key]['print_reciept_url'] = $user_reciept_url . $value->ticket->ticket_number;
					} else {
						$new_details[$key]['print_reciept_url'] = $value->ticket_security_code;
					}
				}

				
				$new_details[$key]['id'] =   $value->id;
				$new_details[$key]['user_id'] =   $value->user_id;
				$new_details[$key]['facility_id'] =   $value->facility_id;
			
				$new_details[$key]['anet_transaction_id'] =   $value->anet_transaction_id;
				$new_details[$key]['ticket_security_code'] =   $value->ticket_security_code;
				$new_details[$key]['check_in_datetime'] =   $value->check_in_datetime;
				$new_details[$key]['estimated_checkout'] =   $value->estimated_checkout;
				$new_details[$key]['checkout_datetime'] =   $value->checkout_datetime;
				$new_details[$key]['is_checkin'] =   $value->is_checkin;
				$new_details[$key]['is_checkout'] =   $value->is_checkout;
				$new_details[$key]['total'] =   $value->total;
				$new_details[$key]['length'] =   $value->length;
				$new_details[$key]['grand_total'] =   $value->grand_total;
				$new_details[$key]['partner_id'] =   $value->partner_id;
				$new_details[$key]['checkin_time'] =   $value->checkin_time;
				$new_details[$key]['checkout_time'] =   $value->checkout_time;
				$new_details[$key]['parking_amount'] =   $value->parking_amount;
				$new_details[$key]['remark'] =   $value->remark;
				$new_details[$key]['processing_fee'] =   $value->processing_fee;
				$new_details[$key]['tax_fee'] =   $value->tax_fee;
				$new_details[$key]['additional_fee'] =   $value->additional_fee; //PIMS-14961 Dev:Sagar
				$new_details[$key]['surcharge_fee'] =   $value->surcharge_fee; //PIMS-14961 Dev:Sagar
				$new_details[$key]['payment_date'] =   $value->payment_date;
				$new_details[$key]['event_id'] =   $value->event_id;
                if($value->reservation_id!=''){
                    $new_details[$key]['ticketech_code'] =   $value->reservation->ticketech_code;
                }                
				$new_details[$key]['device_type'] =   $value->device_type;
				$new_details[$key]['base_length'] =   $value->base_length;
				$new_details[$key]['is_offline_payment'] =   $value->is_offline_payment;
                if($value->is_offline_payment =='1'){
                    $new_details[$key]['is_cash_payment'] =   $value->is_offline_payment;
                }else{
                    $new_details[$key]['is_cash_payment'] =   0;
                }
                
				$new_details[$key]['event_user_id'] =   $value->event_user_id;
				//$new_details['print_reciept_url'] =   $value->print_reciept_url;
				$new_details[$key]['comment'] =   $value->comment;
                $new_details[$key]['payment_comment'] =   $value->comment;
				$new_details[$key]['base_amount'] =   $value->base_amount;
				$new_details[$key]['validate_url'] =   $value->validate_url;
			
					
				$new_details[$key]['ticket']['ticket_number'] = $value->ticket_number;
				$new_details[$key]['facility'] = $value->facility;
				$new_details[$key]['transaction'] = $value->transaction;
				$new_details[$key]['end_time'] = Carbon::createFromFormat('Y-m-d H:i:s', $value->checkout_time);
				$new_details[$key]['formatted_start_date'] = Carbon::parse($value->checkin_time)->format('M d, Y');    
				$new_details[$key]['formatted_start_time'] = Carbon::parse($value->checkin_time)->format('h:i A');   
				$new_details[$key]['formatted_end_date'] = Carbon::parse($value->checkout_time)->format('M d, Y');
				$new_details[$key]['formatted_end_time'] = Carbon::parse($value->checkout_time)->format('h:i A');
				
				
			}
			if ($ticket) {    
				$events['reservations'] =  $new_details;
			//	$events['reservations']['ticket'] = $ticket; 
			}
			/*
            foreach ($events as $k => $v) {
              //  $reservation = Reservation::with(['facility.facilityConfiguration', 'mapcoQrCode.event', 'user', 'transaction', 'ticket'])->where('event_user_id', $id)->where('event_id', $event_id)->orderBy('id', 'desc')->get();
                $ticket = Ticket::with(['facility.facilityConfiguration', 'user', 'transaction'])->where('event_user_id', $id)->where('event_id', $event_id)->orderBy('id', 'desc')->get();
                
              //  foreach ($reservation as $key => $value) {
                foreach ($ticket as $key => $value) {
                    # code...
                  //  $this->facility = Facility::with('facilityConfiguration')->where('id', $value->facility_id)->where('owner_id', $value->partner_id)->first();
                    $user_reciept_url = $value->facility->user_receipt_url;
                    if (isset($value->facility->facilityConfiguration) && $value->facility->facilityConfiguration->is_sbm_event_qrcode == '1') {
                        $value->print_reciept_url = $value->mapcoQrCode[0]->qrcode;
                    } else {
                        if ($value->ticket) {
                            $value->print_reciept_url = $user_reciept_url . $value->ticket->ticket_number;
                        } else {
                            $value->print_reciept_url = $value->ticket_security_code;
                        }
                    }

                    $value->end_time = Carbon::createFromFormat('Y-m-d H:i:s', $value->checkout_time);
                    $value->formatted_start_date = Carbon::parse($value->checkin_time)->format('M d, Y');    
                    $value->formatted_start_time = Carbon::parse($value->checkin_time)->format('h:i A');   
                    $value->formatted_end_date = Carbon::parse($value->checkout_time)->format('M d, Y');
                    $value->formatted_end_time = Carbon::parse($value->checkout_time)->format('h:i A');
                }
            //    if ($reservation) {
                if ($ticket) {    
                    $v['reservations'] =  $ticket;
                    $v['reservations']['ticket'] = $ticket; 
                }
            }
			*/
            $data = [];
            $data[0] = $events;
            return $data;
        } else {
            throw new UserNotAuthorized('User Not Authorize');
        }
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if (($facility) && ($facility->timezone != '')) {
            date_default_timezone_set($facility->timezone);
        } else {
            if ($partnerTimezone) {
                if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }
}
