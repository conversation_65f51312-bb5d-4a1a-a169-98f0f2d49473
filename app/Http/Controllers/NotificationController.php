<?php

namespace App\Http\Controllers;

use App\Models\Notification;

use App\Services\Mailers\UserMailer;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

class NotificationController extends Controller
{
    public function index()
    {
        $notifications = Notification::where('active_status','1')->where('start_date','<=',Carbon::now())->where('end_date','>=',Carbon::now())->get();
        if($notifications->count()){

            return [ 
            'success' => true,            
            'data' => $notifications        
        ];
        }
        else{
            return [ 
            'success' => false,            
            'data' => $notifications        
        ];
        }
       // return ['notifications' => $notifications];
    }
    public function mobNotifications()
    {
        $notifications = Notification::where('active_status','=','1')->where('display_status','!=','3')->where('start_date','<=',Carbon::now())->where('end_date','>=',Carbon::now())->get();
        if($notifications->count()){

            return [ 
            'success' => true,            
            'data' => $notifications        
        ];
        }
        else{
            return [ 
            'success' => false,            
            'data' => $notifications        
        ];
        }
        //return ['notifications' => $notifications];
    }
    public function webNotifications()
    {
        $notifications = Notification::where('active_status','1')->where('display_status','!=','2')->where('start_date','<=',Carbon::now())->where('end_date','>=',Carbon::now())->get();
        if($notifications->count()){

            return [ 
            'success' => true,            
            'data' => $notifications        
        ];
        }
        else{
            return [ 
            'success' => false,            
            'data' => $notifications        
        ];
        }
      //  return ['notifications' => $notifications];
    }
    public function createNotification(Request $request)
    {
        $this->validate(
            $request, [
            'title' => 'required',
            'description' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',
            'active_status' => 'required',
            'display_status' => 'required'
            
            ]
        );
        $notification = Notification::create($request->all());
        if (!$notification) {            
            throw new ApiGenericException('Error Occured, Notification Could Not Be Created');      
        }
        return [ 
            'success' => true,            
              'data' => $notification        
        ];     
        //return ['notification' => $notification];
    }
    public function updateNotification(Request $request)
    {
        $this->validate(
            $request, [
            'title' => 'required',
            'description' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',
            'active_status' => 'required',
            'display_status' => 'required'
            ]
        );
        $notification = Notification::where('id', $request->id)->first();
        $notification->title=$request->title;
        $notification->description=$request->description;
        $notification->start_date=$request->start_date;
        $notification->end_date=$request->end_date;
        $notification->active_status=$request->active_status;
        $notification->display_status=$request->display_status;
        $result= $notification->save();
        if (!$result) {            
            throw new ApiGenericException('Error Occured, Notification Could Not Be Updated');      
        }     
        return [ 
            'success' => true,            
              'data' => $notification        
        ]; 
    }
    public function activedeactive(Request $request)
    {
        $this->validate(
            $request, [
                'id' => 'required',
                'active_status' => 'required',
            ]
        );
        $notification = Notification::where('id', $request->id)->first();                     
        $notification->active_status=$request->active_status;
        $result= $notification->save();
        
        if (!$result) {            
            throw new ApiGenericException('Error Occured, Notification Could Not Be Updated');      
        }   
        return [ 
            'success' => true,            
              'data' => $notification        
        ]; 
        
    }

}
