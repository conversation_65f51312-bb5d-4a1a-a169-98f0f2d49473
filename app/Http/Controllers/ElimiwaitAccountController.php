<?php

namespace App\Http\Controllers;

use App\Models\ElimiwaitAccount;
use App\Models\MonthlyParkingUser;
use App\Models\User;
use Illuminate\Http\Request;

use App\Http\Requests;
use Intervention\Image\Exception\NotFoundException;

class ElimiwaitAccountController extends Controller
{
    public function index()
    {
        return ElimiwaitAccount::all();
    }

    public function store(Request $request)
    {
        $this->validate($request, ElimiwaitAccount::$validParams, ElimiwaitAccount::$validationMessages);

        return ElimiwaitAccount::create($request->all());
    }

    public function show(ElimiwaitAccount $elimiwaitAccount)
    {
        return $elimiwaitAccount->load('requests', 'monthlyParkingUser');
    }

    public function update(Request $request, ElimiwaitAccount $elimiwaitAccount)
    {
        $elimiwaitAccount->fill($request->all());
        $elimiwaitAccount->save();

        return $elimiwaitAccount;
    }

    public function delete(ElimiwaitAccount $elimiwaitAccount)
    {
        $elimiwaitAccount->delete();

        return $elimiwaitAccount;
    }

    public function getUserAccounts(User $user)
    {
        return $user->getElimiwaitMpAccountNumbers();
    }

    public function getActiveRequests(User $user)
    {
        return $user->getActiveElimiwaitRequests();
    }
}
