<?php

namespace App\Http\Controllers;

use Hash;
use Auth;
use DB;
use Phone;
use Authorizer;
use Carbon\Carbon;
use App\Http\Controllers\LoyaltyController;
use App\Classes\MagicCrypt;
use App\Classes\PushNotification;
use App\Classes\PromoCodeLib;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\LoyaltyProgram;

use App\Http\Requests;
use Illuminate\Http\Request;
use App\Http\Helpers\QueryBuilder;

use App\Models\Promotion;
use App\Models\UserPromoCode;
use App\Models\PromoCode;
use App\Models\Role;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Devicetoken;
use App\Models\Facility;
use App\Models\Favourite;
use App\Models\RateSearch;
use App\Models\Rate;
use App\Models\LoyaltyUserAccounts;
use \App\Models\UserSocialLogin;
use App\Models\LoyaltyCode;
use App\Exceptions\UserNotFoundException;
use App\Exceptions\UserNotAuthorized;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserWithEmailExistsException;
use App\Exceptions\NotFoundException;
use App\Http\Helpers\MailHelper;
use App\Services\LoggerFactory;
use App\Models\MonthlyParkingUser;
use Mail;
use App\Services\Mailers\UserMailer;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\UserValidateMaster;


class UserController extends Controller
{
	protected $log;

	public function __construct(LoggerFactory $logFactory)
	{
		$this->log = $logFactory->setPath('logs/user')->createLogger('user_profile');
	}

	// Paginated list of all users
	public function index(Request $request)
	{
		$users = User::with(['userPaymentGatewayDetail', 'membershipPlans' => function ($query) {
			$query->select('membership_plans.id', 'name', 'is_trial', 'start_date', 'end_date', 'service_id');
			// $query->join('services', 'membership_plans.service_id', '=', 'services.id');
			$query->with('service', 'permission');
		}]);

		$authenticated_user = Auth::user();

		if ($request->search) {

			if ($authenticated_user->user_type == '3' || $authenticated_user->user_type == '4' || $authenticated_user->user_type == '12') {
				$users = QueryBuilder::buildSearchQueryForPartner($users, $request->search, User::$searchFields);
			} else {

				if ($request->search == 'NO') {
					if ($authenticated_user->user_type == '1' || $authenticated_user->user_type == '2') {
						$users->where(function ($query) use ($request) {
							$query->where('created_by', $request->owner_id);
						});
					}
				} else {
					if ($request->owner_id != '') {
						$users->where('created_by', $request->owner_id);
					}
					$users->where('anon', 0);
					$users->where(function ($query) use ($request) {
						$query->where('name', 'LIKE', '%' . $request->search . '%')->orWhere('email', 'LIKE', '%' . $request->search . '%')->orWhere('company_name', 'LIKE', '%' . $request->search . '%');
					});
				}
			}
		}

		//check if logged in user is partner or partner-user
		if ($authenticated_user->user_type == '4') {
			$admin_partner_id = Auth::user()->created_by;
			if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
				if ($request->owner_id != '') {
					$users->where('created_by', $request->owner_id);
				}
			} else {
				$users->where(function ($query) use ($authenticated_user) {
					$query->where('created_by', $authenticated_user->created_by);
				});
			}
		}
		if ($authenticated_user->user_type == '3') {
			$users->where(function ($query) use ($authenticated_user) {
				$query->where('created_by', $authenticated_user->id);
			});
		}

		if ($authenticated_user->user_type == '12') {
			$users->where(function ($query) use ($authenticated_user) {
				$query->where('created_by', $authenticated_user->created_by);
			});
		}

		if ($request->sort != '') {
			if ($authenticated_user->user_type == '1') {
				$users =  $users->where('user_type', 3)->orderBy('id', 'Desc')->paginate(20);
			}
			//$users =  $users->where('anon',0)->orderBy($request->sort,$request->sortBy)->paginate(20);
		} else {
			//$users =  $users->where('anon',0)->orderBy('id','Desc')->paginate(20);
			if ($authenticated_user->user_type == '3' || $authenticated_user->user_type == '12') {
				$users =  $users->where('anon', 1)->orderBy('id', 'Desc')->paginate(20);
			} elseif ($authenticated_user->user_type == '4') {
				if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
					$users =  $users->where('user_type', 3)->orderBy('id', 'Desc')->paginate(20);
				} else {
					$users =  $users->where('anon', 1)->orderBy('id', 'Desc')->paginate(20);
				}
			} elseif ($authenticated_user->user_type == '1') {
				$users =  $users->where('user_type', 3)->orderBy('id', 'Desc')->paginate(20);
			} else {
				$users =  $users->where('anon', 0)->orderBy('id', 'Desc')->paginate(20);
			}
		}
		return $users;
	}

	public function self(Request $request)
	{
		$userData = Auth::user();

		$userData->maintenanceMode = env('APP_MAINTENANCE_USER_ACCOUNT');
		$ipArray = explode(',', env('IP_ADDRESS_OVERRIDE'));

		foreach ($ipArray as $ip) {
			if ($request->ip() ==  $ip) {
				$userData->maintenanceMode = false;
			}
		}
		/**$account = LoyaltyUserAccounts::where('user_id' , '=', $userData->id)->orderBy('id', 'DESC')->first();
		if ($account) {
			$userData->is_loyalty_active = LoyaltyProgram::CONST_ONE;
			$userData->is_loyalty = LoyaltyProgram::CONST_ONE;
		}
		else{
            $userData->is_loyalty = LoyaltyProgram::CONST_ZERO;
	        $userData->is_loyalty_active = LoyaltyProgram::CONST_ZERO;
        }**/
		return $userData->load('photo', 'brandSetting');
	}

	// User information for display on admin detail page
	public function getUserForAdmin(User $user)
	{
		/*if($user){
			$mpuser=MonthlyParkingUser::where('user_id', '=', $user->id)->where('active','=', 1)->first();
			if(!$mpuser){
				$user->is_loyalty = 0;
				$user->is_loyalty_active = 0;
			}
		}*/
		$account = LoyaltyUserAccounts::where('user_id', '=', $user->id)->orderBy('id', 'DESC')->first();
		if ($account) {
			$user->is_loyalty = LoyaltyProgram::CONST_ONE;
			$user->is_loyalty_active = LoyaltyProgram::CONST_ONE;
		} else {
			$user->is_loyalty = LoyaltyProgram::CONST_ZERO;
			$user->is_loyalty_active = LoyaltyProgram::CONST_ZERO;
		}
		return $user->load('reservations', 'reservations.facility', 'monthlyParkingAccounts', 'monthlyParkingAccounts.facility', 'photo');
	}

	/**
	 * Create a new user without any side effects
	 *
	 * @param  User $user [description]
	 * @return [type]       [description]
	 */
	public function store(User $user)
	{
		$user->save();
		return $user;
	}

	// REMOVE SPECIAL CHARACTERS, SPACES FROM USER NAME IF THERE IS ANY
	public function cleanData($data)
	{
		$data = str_replace(' ', '', $data); // Remove All Spaces.
		$data = str_replace('-', '', $data); // Remove All Dashes.
		return preg_replace('/[^A-Za-z0-9\-]/', '', $data); // REMOVE ALL SPECIAL CHARACETRS
	}

	// CHECK IF THE REFERRER CODE EXISTS IN DATABASE
	public function checkReferrerUser($referred_by)
	{
		$referrer = DB::table('users')->where('referral_code', $referred_by)->first();
		if (!$referrer) {
			throw new NotFoundException('Please enter a valid referral code.');
		}
		return $referrer->id;
	}

	// GENERATE A NEW REFERREL CODE
	public function generateReferCode($name)
	{
		$code = mt_rand(********, ********);
		$name = $this->cleanData($name);
		$name_slug = strtoupper(substr($name, 0, 4));
		if (strlen($name_slug) < 4) {
			$remaining = 4 - strlen($name_slug);
			for ($i = 0; $i < $remaining; $i++) {
				$name_slug .= 'X';
			}
		}
		return $name_slug . $code;
	}

	/**
	 * Generate referral code for given user id if not already generated.
	 *
	 * @param  User $user_id [description]
	 * @return [type]       [description]
	 */
	public function createReferralCode(Request $request)
	{

		// 1. Check if user_id is provided
		$this->validate($request, User::$validateUserId);

		$user_id = $request->user_id;

		// 2. Check if user exists or not
		$user = User::find($user_id);
		if (!$user) {
			throw new NotFoundException('Error Occurred, No User Found with This User Id.');
		}

		// 3. check if user already have an referral code
		if (!$user->referral_code) {
			// generate new referral code
			$referral_code = $this->generateReferCode($user->name);
			$user->referral_code = $referral_code;
			$user->save();
		}

		// 4. return success
		return [
			'status' => true,
			'user_id' => $user_id,
			'referral_code' => $user->referral_code,
			'updated_at' => $user->updated_at
		];
	}

	/**
	 * Update Device Token for Logged In User
	 *
	 * @param  User $user_id, $device_token, $device_key [description]
	 * @return [type]       [description]
	 */
	public function updateDeviceToken(Request $request)
	{

		// 1. Check if user_id, device_token, device_key is provided
		$this->validate($request, User::$validateUserDevice);
		$user_id = $request->user_id;

		// 2. Check if user exists or not
		$user = User::find($user_id);
		if (!$user) {
			throw new NotFoundException('Error Occurred, No User Found with This User Id.');
		}

		// 3. check id device_key already exists in database
		$device_key = $request->device_key;
		$device = Devicetoken::findDeviceWithKey($device_key);

		// Create New Device Token Entry
		if (!$device) {
			$device = new Devicetoken();
		}

		$device->fill($request->only(['user_id', 'device_token', 'device_key', 'device_type']));
		$device->save();

		return [
			'status' => true,
			'device' => $device
		];
	}

	/**
	 * Get Referred Users of a Referral User
	 *
	 * @param  User $user_id [description]
	 * @return [type]       [description]
	 */
	public function getUserReferred(Request $request)
	{

		// 1. Check if user_id is provided
		$this->validate($request, User::$validateUserId);
		$user_id = $request->user_id;

		// 2. check if user exists or not
		$user = User::where('id', $user_id)->first();

		if (!$user) {
			throw new NotFoundException('Error Occured, No User Found with This User Id.');
		}

		// 3. Return Empty if User do not have a Referral Code
		if (!$user->referral_code) {
			return [
				'status' => true,
				'referral_code' => $user->referral_code,
				'referred_users' => [],
				'user_count' => 0
			];
		}

		$referredUsers = User::where('referred_by', $user->referral_code)->get();

		if (!$referredUsers) {
			$referredUsers = [];
		}

		return [
			'referral_code' => $user->referral_code,
			'referred_users' => $referredUsers,
			'user_count' => sizeof($referredUsers)
		];
	}

	/**
	 * Set User favourite facilities
	 *
	 * @param  User $user_id, $facility_id, $is_favourite [description]
	 * @return [type]       [description]
	 */
	public function setUserFavourites(Request $request)
	{

		// 1. Check if user_id is provided
		$this->validate($request, User::$validateUserFavourites);
		$user_id = $request->user_id;
		$facility_id = $request->facility_id;

		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}
			$fac = Facility::where('id', $facility_id)->where('owner_id', $secret->partner_id)->first();
			$user = User::where('id', $user_id)->where('created_by', $secret->partner_id)->first();
		} else {
			$fac = Facility::where('id', $facility_id)->first();
			$user = User::where('id', $user_id)->first();
		}

		if (!$fac) {
			throw new NotFoundException('Error Occurred, No Facility Found.');
		}

		// 2. check if user exists or not
		if (!$user) {
			throw new NotFoundException('Error Occurred, No User Found with This User Id.');
		}

		// 3. Fetch User Favourites
		$favourites = Favourite::getFavourites($user_id);

		// 4. Check if the request is to add to favourites or to remove from favourites
		if ($request->is_favourite) {
			// Add to favourites
			if (!$favourites) {
				// Create Entry If Setting Favourite First Time
				$favourites = new Favourite();
				$favourites->user_id = $request->user_id;
				$favourites->facility_ids = $request->facility_id;
			} else {
				$facilityIds = explode(',', $favourites->facility_ids);

				// check if facility already exists in facility_ids
				if (!in_array($facility_id, $facilityIds)) {
					array_push($facilityIds, $facility_id);
				}
				// asort($facilityIds);
				$favourites->facility_ids = implode(',', $facilityIds);
			}
		} else {
			// Remove from favourites
			if ($favourites) {
				$facilityIds = explode(',', $favourites->facility_ids);

				if (($key = array_search($facility_id, $facilityIds)) !== false) {
					unset($facilityIds[$key]);
				}
				$favourites->facility_ids = implode(',', $facilityIds);
			}
		}

		$facilityCount = 0;

		if ($favourites) {
			if (strlen($favourites->facility_ids) > 0) {
				$facilityCount = sizeof(explode(',', $favourites->facility_ids));
				$result = $favourites->save();
			} else {
				$result = $favourites->delete();
			}
			if (!$result) {
				throw new ApiGenericException('Error Occurred, Favourites Facility Could Not Be Saved.');
			}
		}

		return [
			'status' => true,
			'favourite_count' => $facilityCount
		];
	}

	public function getUserFavourites(Request $request)
	{
		$this->validate($request, User::$validateGetUserFavourites);
		$user_id = $request->user_id;
		$returnData = array();
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}
			$user = User::where('id', $user_id)->where('created_by', $secret->partner_id)->first();
		} else {
			$user = User::where('id', $user_id)->first();
		}

		if (!$user) {
			throw new NotFoundException('Error Occurred, No User Found with This User Id.');
		}

		$favourites = Favourite::getFavourites($user_id);

		if (!$favourites) {
			throw new NotFoundException('Error Occurred, Favourite Facilities Not Found For This User Id.');
		}

		$fav_facilities = explode(",", $favourites->facility_ids);

		$facilities = Facility::with('geolocations', 'photos')->whereIn('id', $fav_facilities)->get();

		$rateSearch = new RateSearch($fav_facilities, $request->arrival_time, $request->length_of_stay, true);

		$facRates = $allTimeFlag = $monthlyRateFlag = $facilityRateId = [];

		foreach ($rateSearch->facilities as $facilityRate) {

			$monthlyRate = reset($facilityRate->monthly_rates);
			$facRates[$facilityRate->facility_id] = $facilityRate->price;
			$allTimeFlag[$facilityRate->facility_id] = $facilityRate->is_24hour_open;
			$monthlyRateFlag[$facilityRate->facility_id] = ($monthlyRate['rate'] > 0 ? 1 : 0);
			$facilityRateId[$facilityRate->facility_id] = ($facilityRate->rate_id ? $facilityRate->rate_id : 0);
		}


		foreach ($facilities as $facility) {
			$returnData[] = [
				'facility_id' => $facility->id,
				'geolocation' => $facility->geolocations->toArray(),
				'photo_url' => $facility->photos ? $facility->photos->url : null,
				'full_name' => $facility->full_name,
				'entrance_location' => $facility->entrance_location,
				'between_streets' => $facility->between_streets,
				'phone_number' => $facility->phone_number,
				'ticketech_id' => $facility->ticketech_id,
				'reservation_bonus_hours' => $facility->reservation_bonus_hours,
				'reservation_bonus_rate' => $facility->reservation_bonus_rate,
				'price' => $facRates[$facility->id],
				'is_indoor_parking' => $facility->is_indoor_parking,
				'is_outdoor_parking' => $facility->is_outdoor_parking,
				'is_tesla_charging' => $facility->is_tesla_charging,
				'is_generic_ev_charging' => $facility->is_generic_ev_charging,
				'is_motorcycle_parking' => $facility->is_motorcycle_parking,
				'oversize_fee' => ($facility->oversize_fee > 0 ? 1 : 0),
				'is_oversize' => ($facility->oversize_fee > 0 ? 1 : 0),
				'is_24hour_open' => $allTimeFlag[$facility->id],
				'monthly_rate' => $monthlyRateFlag[$facility->id],
				'is_monthly' => $monthlyRateFlag[$facility->id],
				'rate_id' => $facilityRateId[$facility->id]
			];
		}

		if (!$facilities) {
			throw new NotFoundException('Error Occurred, Facilities Not Found For This User Id.');
		}

		return [
			'status' => true,
			'facilities' => $returnData
		];
	}
	/**
	 * Create a new user. Used for registering a new user account.
	 *
	 * @param  User $user [description]
	 * @return [type]       [description]
	 */
	public function create(Request $request)
	{
		// if the social user is already registered
		if ($request->social_id) {
			$this->validate($request, User::$validationSocialLogin);
			$request->request->add(['password' => User::DEFAULT_SOCIAL_PASSWORD]);
			$user = User::where('social_id', $request->social_id)->first();
			if ($user) {
				$request->request->add(['username' => $user->email]);
				$user->fill($request->only(['name', 'phone', 'photo_url']));
				$user->save();
				return  [
					'user_id' => $user->id,
					'user' => $user->load('photo'),
					'session' => $user->logIn($request)
				];
			}
		} else {
			$this->validate($request, User::$validationRules);
		}

		$user = null;
		$anonFlag = false;

		// Get country Code
		$countryCode = QueryBuilder::appendCountryCode();
		// Check that email does not belong to a user already
		if ($request->email) {

			$secret = OauthClient::where('secret', $request->client_secret)->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}

			$user = User::where('email', $request->email)->where('anon', 0)->where('created_by', $secret->partner_id)->first();
			// dd($countryCode . $request->phone, $user);
			if ($user && !Hash::check($request->password, $user->password)) {
				$errorMsg = 'User with email address already registered.';
				throw new UserWithEmailExistsException($errorMsg);
			} else if (!$user) {
				// Check if that email belongs to an anon user that we can assign it to

				$user = User::where('phone', $countryCode . $request->phone)->where('anon', 0)->where('created_by', $secret->partner_id)->first();

				if ($user) {
					if ($user->email == '') {
						$user->email = 	$request->email;
						$user->password = Hash::make($request->password);
						$user->anon = 0;
						$user->qr_code_number = $this->generateQrCode();
						$user->is_notification_enabled = $request->is_notification_enabled;
						$user->save();
					} else {
						$errorMsg = 'The phone number you\'ve entered is already registered. Kindly provide a different number';
						throw new UserWithEmailExistsException($errorMsg);
					}
				}

				/*$user = User::where('email', $request->email)->where('anon', 1)->where('created_by', $secret->partner_id)->first();
				if ($user) {
					$anonFlag = false;
				}*/
			} else if ($user && Hash::check($request->password, $user->password)) {
				$user->qr_code_number = $this->generateQrCode();
				$user->is_notification_enabled = $request->is_notification_enabled;
				$user->save();
			}
		}

		$newUserFlag = false;

		if (!$user) {
			$user = new User();
			$newUserFlag = true;
		}

		$ref_user_id = null;

		if ($request->referred_by != "") {
			$ref_user_id = $this->checkReferrerUser($request->referred_by);
		}
		$request->password = $request->password ? $request->password : User::DEFAULT_SOCIAL_PASSWORD;

		if ($newUserFlag || $anonFlag) {


			$user->fill($request->only(['name', 'email', 'phone', 'referred_by', 'social_id', 'social_type', 'photo_url', 'user_type', 'created_by', 'referral_code', 'qr_code_number']));
			$user->password = Hash::make($request->password);
			$user->anon = 0;
			$user->referral_code = $this->generateReferCode($request->name);
			$user->has_reserved = null;
			$user->created_by = $secret->partner_id;
			$user->user_type = '5';
			$user->phone = $request->phone == '' ? $request->phone : $countryCode . $request->phone;
			$user->referral_code = $request->referral_code;
			$user->qr_code_number = $this->generateQrCode();
			$user->is_notification_enabled = $request->is_notification_enabled;

			$user->save();
		}

		$is_device_updated = false;

		if (isset($request->device_token) && $request->device_token != '' && isset($request->device_key) && $request->device_key != '' && isset($request->device_type) && $request->device_type != '') {
			// check id device_key already exists in database
			$device_key = $request->device_key;
			$device = Devicetoken::findDeviceWithKey($device_key);
			$request->request->add(
				[
					'user_id' => $user->id
				]
			);

			// Create New Device Token Entry
			if (!$device) {
				$device = new Devicetoken();
			}

			$device->fill($request->only(['user_id', 'device_token', 'device_key', 'device_type']));
			$device->user_id = $user->id;
			$device->device_token = $request->device_token;
			$device->device_id = $request->device_key;
			$device->device_type = $request->device_type;
			$device->save();
			$is_device_updated = true;
		}

		// // Upload profile picture if provided
		if ($request->profile_image) {
			$user->uploadPhoto($request, 'profile_image');
		}
		$msg = '';
		$newLoyaltyUserFlag = false;



		$user = User::find($user->id);
		if (isset($request->address) && !empty($request->address))
			$user->address = $request->address;

		if (isset($request->address2) && !empty($request->address2))
			$user->address2 = $request->address2;

		if (isset($request->pincode) && !empty($request->pincode))
			$user->pincode = $request->pincode;

		if (isset($request->state) && !empty($request->state))
			$user->state = $request->state;

		if (isset($request->city) && !empty($request->city))
			$user->city = $request->city;

		if (isset($request->country) && !empty($request->country))
			$user->country = $request->country;

		$user->save();


		if (!$request->adminCreating) {
			$user->sendEmailConfirmation();
		}

		if (isset($secret->partner_id)) {
			$_SESSION['partner_id'] = $secret->partner_id;
		}

		$return = [
			'user_id' => $user->id,
			'user' => $user->load('photo'),
			'session' => $user->logIn($request),
			'is_device_updated' => $is_device_updated,
			'is_new_user' => $newUserFlag,
			'message' => $msg or ''
		];

		if ($is_device_updated) {
			$return['device'] = $device;
		}

		return $return;
	}

	public function generateQrCode()
	{
		$code = 'P' . rand(10, 99) . rand(100, 999) . rand(100, 999) . 'S';
		$isExist = User::where('qr_code_number', $code)->first();
		if ($isExist) {
			$this->generateQrCode();
		}
		return $code;
	}


	/**
	 * Create a new loyalty user. Used for registering a new user along with loyalty account.
	 *
	 * @param  User $user [description]
	 * @return [type]       [description]
	 */
	public function createLoyaltyUser(Request $request)
	{

		$this->validate($request, User::$validationRules);
		$user = null;

		// Check that email does not belong to a user already
		$user = User::where('email', $request->email)->where('anon', 0)->first();

		if ($user && !Hash::check($request->password, $user->password)) {

			$errorMsg = 'User with email address already registered';
			throw new UserWithEmailExistsException($errorMsg);
		} else if (!$user) {
			// Check if that email belongs to an anon user that we can assign it to
			$user = User::where('email', $request->email)->where('anon', 1)->first();
		}

		$newUserFlag = false;
		if (!$user) {
			$user = new User();
			$newUserFlag = true;
		}

		$ref_user_id = null;

		if ($request->referred_by != "") {
			$ref_user_id = $this->checkReferrerUser($request->referred_by);
		}
		$request->password = $request->input('password') ? $request->input('password') : User::DEFAULT_SOCIAL_PASSWORD;
		$user->fill($request->only(['name', 'email', 'phone', 'referred_by', 'social_id', 'social_type', 'photo_url']));
		$user->password = Hash::make($request->password);
		$user->anon = 0;
		$user->referral_code = $this->generateReferCode($request->name);
		$user->has_reserved = null;

		$user->save();

		$is_device_updated = false;

		if (isset($request->device_token) && $request->device_token != '' && isset($request->device_key) && $request->device_key != '' && isset($request->device_type) && $request->device_type != '') {
			// check id device_key already exists in database
			$device_key = $request->device_key;
			$device = Devicetoken::findDeviceWithKey($device_key);
			$request->request->add(
				[
					'user_id' => $user->id
				]
			);

			// Create New Device Token Entry
			if (!$device) {
				$device = new Devicetoken();
			}

			$device->fill($request->only(['user_id', 'device_token', 'device_key', 'device_type']));
			$device->save();
			$is_device_updated = true;
		}

		// // Upload profile picture if provided
		if ($request->profile_image) {
			$user->uploadPhoto($request, 'profile_image');
		}

		if (!$user->is_loyalty || $user->is_loyalty == LoyaltyProgram::CONST_ZERO) {
			$res = LoyaltyProgram::registerUserById($user->id);
		}

		if ($newUserFlag) {
			$user->sendEmailConfirmation();
		}

		$user = User::find($user->id);
		$return = [
			'user_id' => $user->id,
			'user' => $user->load('photo'),
			'session' => $user->logIn($request),
			'is_device_updated' => $is_device_updated,
			'is_new_user' => $newUserFlag
		];

		if ($is_device_updated) {
			$return['device'] = $device;
		}

		return $return;
	}


	/**
	 * Create a new user. Used for registration/login a  user using social account.
	 *
	 * @param  User $user [description]
	 * @return [type]       [description]
	 */
	public function createSocialUser(Request $request)
	{

		// check if social media id is provided
		$this->validate($request, User::$validationSocialLogin);
		if ($request->social_id) {
			$request->request->add(['password' => $request->social_id]);
			$userSocialDetails = UserSocialLogin::where('social_id', $request->social_id)->where('social_type', $request->social_type)->first();
			if ($userSocialDetails) {

				$user = User::where('id', $userSocialDetails->user_id)->first();
				if ($user) {
					$request->request->add(['username' => $user->email]);
					return  [
						'user_id' => $userSocialDetails->user_id,
						'user' => $user->load('photo'),
						'session' => $user->logIn($request)
					];
				} else {
					throw new ApiGenericException('Login Failed! Invalid user details.');
				}
			}

			// if user social details are not yet in database, Check if the similiar email user is already exist
			$user = null;
			if (isset($request->email) && ($request->email != '')) {
				$existingUser = User::where('email', $request->email)->where('anon', 0)->first();

				if ($existingUser) {

					//if user is already exist then insert the socila details in social login table
					$this->addUserSocialLoginDetails($request, $existingUser->id);

					//update user photo url if , photo has not update yet
					if ((isset($existingUser->photo_url) && ($existingUser->photo_url != '') && (isset($request->photo_url)))) {
						$existingUser->fill($request->only(['photo_url']));
						$existingUser->save();
					}
					$request->request->add(['username' => $existingUser->email]);

					return  [
						'user_id' => $existingUser->id,
						'user' => $existingUser->load('photo'),
						'session' => $existingUser->logIn($request)
					];
				}

				// Check if that email belongs to an anon user that we can assign it to
				$user = User::where('email', $request->email)->where('anon', 1)->first();
			} else {
				//return error if email is not there
				return  [
					'is_email_found' => '0',
					'email' => ''
				];
			}
			if (!$user) {
				$user = new User();
			}

			$ref_user_id = null;
			if ($request->referred_by != "") {
				$ref_user_id = $this->checkReferrerUser($request->referred_by);
			}

			// These will get used in grant verfy code
			$request->request->add(['username' => $request->email]);
			$user->fill($request->only(['name', 'email', 'phone', 'referred_by', 'photo_url']));
			$user->anon = 0;
			$user->referral_code = $this->generateReferCode($request->name);
			$user->has_reserved = null;
			if (isset($request->email) && ($request->email != '')) {
				$user->email_confirmed = 1;
			}
			$user->save();
			$is_device_updated = false;

			if (isset($request->device_token) && $request->device_token != '' && isset($request->device_key) && $request->device_key != '' && isset($request->device_type) && $request->device_type != '') {
				// check id device_key already exists in database
				$device_key = $request->device_key;
				$device = Devicetoken::findDeviceWithKey($device_key);
				$request->request->add(
					[
						'user_id' => $user->id
					]
				);

				// Create New Device Token Entry
				if (!$device) {
					$device = new Devicetoken();
				}

				$device->fill($request->only(['user_id', 'device_token', 'device_key', 'device_type']));
				$device->save();
				$is_device_updated = true;
			}

			//adding details o user social login table.
			$this->addUserSocialLoginDetails($request, $user->id);

			$user = User::find($user->id);
			$return = [
				'user_id' => $user->id,
				'user' => $user->load('photo'),
				'session' => $user->logIn($request),
				'is_device_updated' => $is_device_updated
			];

			if ($is_device_updated) {
				$return['device'] = $device;
			}
			return $return;
		} else {
			throw new ApiGenericException('Please provide required data \social_token\ missing.');
		}
	}


	public function checkSocialUserEmail(Request $request)
	{
		$this->validate($request, User::$validationSocialEmailCheck);
		if (($request->social_id)) {
			$userSocialDetails = UserSocialLogin::where('social_id', $request->social_id)->where('social_type', $request->social_type)->first();
			if ($userSocialDetails) {
				$user = User::where('id', $userSocialDetails->user_id)->first();
				if ((isset($user->email)) && ($user->email != '')) {
					return  [
						'is_email_found' => '1',
						'email' => $user->email
					];
				} else {
					return  [
						'is_email_found' => '0',
						'email' => ''
					];
				}
			}
			return  [
				'is_email_found' => '0',
				'email' => ''
			];
		}
		return  [
			'is_email_found' => '0',
			'email' => ''
		];
	}
	public function addUserSocialLoginDetails(Request $request, $user_id = '0')
	{
		$arrUserSocial = array();
		$arrUserSocial['user_id'] = $user_id;
		$arrUserSocial['social_id'] = $request->social_id;
		$arrUserSocial['social_type'] = $request->social_type;
		//inserting details in user socila login table
		return UserSocialLogin::create($arrUserSocial);
	}

	public function edit(Request $request, $userId)
	{
		if (!$user = User::find($userId)) {
			throw new NotFoundException("No user with that ID exists.");
		}

		if (Auth::user()->id !== $user->id) {
			throw new UserNotAuthorized("You are not authorized to edit this user.");
		}

		foreach (['name', 'phone'] as $key) {
			if ($request->$key) {
				$user->$key = $request->$key;
			}
		}

		if ($user->is_loyalty) {
			$dataSet = LoyaltyProgram::getUserProfile($user->email);
			if ($dataSet['success'] && $dataSet['data']) {
				$profile = $dataSet['data'];
				if ($request->name) {
					$splitName = LoyaltyProgram::splitName($request->name);
					$profile['firstName'] = $splitName['first_name'];
					$profile['lastName'] = $splitName['last_name'];
				}

				if ($request->phone) {
					$profile['voice'] = $profile['sms'] = $request->phone;
				}

				$responseProfile = LoyaltyProgram::updateUserProfile($user->email, $dataSet);
			}
		}

		$user->save();

		if ($request->profile_image) {
			$user->uploadPhoto($request, 'profile_image');
		}

		return $user->load('photo');
	}

	/**
	 * Confirm this user's email address is valid
	 *
	 * @param  [type]  $userId           [description]
	 * @param  [type]  $confirmationCode [description]
	 * @param  Request $request          [description]
	 * @return [type]                    [description]
	 */
	public function confirmEmail($userId, $confirmationCode)
	{
		$user = User::find($userId);

		if (!$user) {
			throw new NotFoundException();
		}

		if ($user->email_confirmed) {
			return ['email_confirmed' => true];
		}

		if ($user->confirmation_code !== $confirmationCode) {
			throw new ApiGenericException('Given confirmation code does not match confirmation code for this user.');
		}

		$user->email_confirmed = true;
		$user->save();

		return ['email_confirmed' => true];
	}

	/**$
	 * Resend email confirmation email. Generates a new confirmation token for this user before sending.
	 * @param  [type] $userId [description]
	 * @return [type]         [description]
	 */
	public function resendConfirmationEmail($userId)
	{
		$user = User::find($userId);

		if (!$user) {
			throw new NotFoundException('No user found with that ID.');
		}

		$user->generateEmailConfirmationCode()->save();
		$user->sendEmailConfirmation();

		return ['sent' => true];
	}

	public function delete(User $user)
	{
		// Below logic is for unit testing - you can't reach this endpoint without being authed anyway
		if (Auth::user() && (Auth::user()->id === $user->id)) {
			throw new ApiGenericException("You can not delete your own user.");
		}

		$user->delete();
		return $user;
	}

	public function admin()
	{
		if (\Auth::user()->isAdmin) {
			return 'Ok';
		} else {
			throw new UserNotAuthorized('Must be logged in as admin.');
		}
	}

	public function cmsAccess()
	{
		return 'Ok';
		/*if (Auth::user()->hasCmsAccess) {
			return 'Ok';
		} else {
			throw new UserNotAuthorized('User does not have cms access');
		}*/
	}
	public function mpSales()
	{
		if (Auth::user()->isMpSales) {
			return 'Ok';
		} else {
			throw new UserNotAuthorized('Must be logged in as admin.');
		}
	}

	public function accountMaintenance()
	{
		return ['maintenanceMode' => env('APP_MAINTENANCE_USER_ACCOUNT')];
	}

	public function changeRole(Request $request, User $user)
	{
		$role = Role::where('name', $request->role)->first();
		if (!$role) {
			throw new NotFoundException('Role does not exist.');
		}
		$user->roles()->detach();
		$user->attachRole($role);
		$user->save();
		return $this->getUserForAdmin($user);
	}

	public function removeRoles(User $user)
	{
		$user->roles()->detach();
		$user->save();
		return $this->getUserForAdmin($user);
	}

	public function updateEmail(Request $request, User $user)
	{
		// Check for existing email address
		if (User::where('email', $request->email)->first()) {
			throw new ApiGenericException('This email address is already in use.');
		}

		$oldEmail = $user->email;
		$newEmail = $request->email;

		try {
			// Update email in database
			$user->email = $request->email;
			$user->save();

			/*if ($user->is_loyalty == LoyaltyProgram::CONST_ONE) {
				// update loyalty user email
				$res = LoyaltyProgram::updateEmail(
					array('email' => $newEmail),
					$user->id,
					array('email' => $oldEmail)
				);
				
				if (!$res['success']) {
					//DB::rollback();
					// $msg = isset($res['data']['description']) ? $res['data']['description'] : 'loyalty email not updated.'
					throw new ApiGenericException("There was an error in email update at reward server");
				}
			}*/

			/*  if(count($user->monthlyParkingAccounts)>0)  
                      {
			// Update Authorize.Net profile email addresses for user and all monthly parking accounts
			(new Cim())->setUser($user)->isReservation()->updateCustomerEmail($request->email);
			foreach ($user->monthlyParkingAccounts as $mpUser) {
				(new Cim())->setMonthlyParkingUser($mpUser)->updateCustomerEmail($request->email);
			}*/

			// Update address for each account in the AR API.
			// Note: This calls appears to work (returns success true, status 200) but email address is not
			// returned from the AR API when you get an account, so we have no way to verify if this function is
			// actually updating email addresses
			/*foreach ($user->monthlyParkingAccounts as $mpAccount) {
				$mpAccount->updateEmailAddress($request->email);
			}
                      }*/
		} catch (\Exception $e) {
			//DB::rollback();
			$this->log->info("User: " . $user->id . "Unable to update email address " . $e->getMessage());
			throw new ApiGenericException("Unable to update email address, returned message {$e->getMessage()}");
		}

		//DB::commit();

		return true;
	}




	public function addUserPromoCode(Request $request)
	{

		// 1. Check if PromoCode is provided
		$this->validate($request, UserPromoCode::$checkUserPromoAddValidationRules);

		$user = User::where('id', $request->user_id)->first();

		if (!$user) {
			throw new ApiGenericException('Invalid User, No User Found With This Id.');
		}

		// 2. check if PromoCode is valid 
		$valid_promocode = PromoCodeLib::validatePromoCode($request);
		if (!$valid_promocode['is_promocode_valid']) {
			throw new ApiGenericException('Error Occured, Promocode Can Not Be Added');
		}
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}
			$isValidPromotion = Promotion::where('id', $valid_promocode['promocode']->promotion_id)->where('owner_id', $secret->partner_id)->first();
			if (!$isValidPromotion) {
				throw new ApiGenericException('Invalid User, No Promocode Found for this user.');
			}
		}


		$usedpromocode = UserPromoCode::where(['promocode' => $request->promocode, 'user_id' => $request->user_id])->first();


		if ($usedpromocode) {
			throw new ApiGenericException('Promocode Already Exists in your account');
		} else {

			$nUserPromoCode = new UserPromoCode();
			$nUserPromoCode->user_id = $user->id;
			$nUserPromoCode->promocode = $valid_promocode['promocode']->promocode;
			$nUserPromoCode->promo_code_id = $valid_promocode['promocode']->id;
			$result = $nUserPromoCode->save();

			if (!$result) {
				throw new ApiGenericException('Error Occured, User promo code Could Not Be Saved');
			}

			return [
				'is_promocode_added' => 1,
				'user_id' => $user->id,
				'promocode' => $valid_promocode['promocode']->promocode,
				'message' => 'Promocode is updated with user'
			];
		}
	}


	public function getUserPromoUsage(Request $request)
	{

		// 1. Check if user_id is provided
		$this->validate($request, User::$validateUserId);

		// 2. check if user exists or not
		$userId = $request->user_id;
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}
		}

		$user = User::where('id', $userId)->first();

		if (!$user) {
			throw new ApiGenericException('Invalid User, No User Found With This Id.');
		}
		$currentDate = date('Y-m-d');
		$newpromousage = null;
		/*     
		if($user) {
			$user_id=$user->id;
			$userPromocode = DB::table('users_promo_codes')
			->Join('promo_codes', 'users_promo_codes.promocode', '=', 'promo_codes.promocode')
			->Join('promotions', 'promotions.id', '=', 'promo_codes.promotion_id')
			->select('users_promo_codes.promocode', 'users_promo_codes.user_id', 'promo_codes.promocode', 'promo_codes.usage', 'promotions.valid_to', 'promo_codes.discount_type', 'promo_codes.discount_value')
			->where('users_promo_codes.user_id', $user_id)
			->where('promotions.valid_from', '<=', $currentDate)
			->where('promotions.valid_to', '>=', $currentDate)
			->get();
		}
		 */

		if ($user) {
			$user_id = $user->id;
			$userPromocode = DB::table('users_promo_codes')
				->Join('promo_codes', 'users_promo_codes.promocode', '=', 'promo_codes.promocode')
				->Join('promotions', 'promotions.id', '=', 'promo_codes.promotion_id')
				->select('users_promo_codes.promocode', 'users_promo_codes.user_id', 'promo_codes.promocode', 'promo_codes.usage', 'promotions.valid_to', 'promotions.description', 'promo_codes.discount_type', 'promo_codes.discount_value')
				->where('users_promo_codes.user_id', $user_id)
				->where('promotions.valid_from', '<=', $currentDate)
				->where('promotions.valid_to', '>=', $currentDate)
				->get();
		}
		if ($userPromocode) {

			foreach ($userPromocode as $upck => $upcv) {
				$upcvuser_id = $upcv->user_id;
				$upcvpromocode = $upcv->promocode;
				if ($upcv->usage == '') {
					$upcv->usage = 1;
				}
				$tolproass[$upcvuser_id][$upcvpromocode] = $upcv->usage;
				$tolprovalto[$upcvuser_id][$upcvpromocode] = $upcv->valid_to;
				if ($upcv->discount_type == 'value') {
					$tolprodiscount = '$' . $upcv->discount_value;
				} else {
					$tolprodiscount = $upcv->discount_value . '%';
				}

				/*f($upcv->usage==1)
					$booking=' Booking';
				else
					$booking=' Bookings';*/

				//        $tolpromessage[$upcvuser_id][$upcvpromocode]='New User Offer Get '.$tolprodiscount.' Discount on '.$upcv->usage.$booking;

				//$tolpromessage[$upcvuser_id][$upcvpromocode]='GET '.$tolprodiscount.' OFF YOUR FIRST PARKING RESERVATION';
				$tolpromessage[$upcvuser_id][$upcvpromocode] = $upcv->description;
				$promocodearr[] = $upcv->promocode;
			}

			$promocodestr = "'" . implode("','", $promocodearr) . "'";

			$usedpromocode = DB::table('promo_usages')->select(DB::raw('count(promocode) as tolusage ,promocode, user_id'))->where('user_id', $userId)->whereIn('promocode', $promocodearr)->groupBy('promocode')->get(); //UserPromoCode::



			if ($usedpromocode) {
				foreach ($usedpromocode as $upcuk => $upcuv) {
					$upcvuser_id = $upcuv->user_id;
					$upcvpromocode = $upcuv->promocode;
					$upcvusage = $upcuv->tolusage;
					$tolproused[$upcvuser_id][$upcvpromocode] = $upcvusage;
				}
			}

			if ($tolproass[$userId]) {
				foreach ($tolproass[$userId] as $tpak => $tpav) {
					if (isset($tolproused[$userId][$tpak])) {
						$newpromousageck = $tpav - $tolproused[$userId][$tpak];
					} else {
						$newpromousageck = $tpav - 0;
					}

					if ($newpromousageck) {
						$newpromousage[$userId][] = array('promocode' => $tpak, 'usages' => $newpromousageck, 'validto' => $tolprovalto[$userId][$tpak], 'message' => $tolpromessage[$userId][$tpak]);
					}
				}
			}
		}

		if (!$newpromousage[$userId]) {
			// throw new ApiGenericException('Error Occured, No record found with this user_id.');
			$message = 'No promo code added to this account';
			$newpromousage[$userId] = '';
		} else {

			$message = 'Remain usages of promocode.';
		}


		return [
			'is_promocode_assign' => 1,
			'user_id' => $userId,
			'promocodedetail' => $newpromousage[$userId],
			'message' => $message
		];
	}

	public function deleteUserPromoCode(Request $request)
	{

		// 1. Check if PromoCode is provided
		$this->validate($request, UserPromoCode::$checkUserPromoAddValidationRules);

		$user = User::where('id', $request->user_id)->first();

		if (!$user) {
			throw new ApiGenericException('Invalid User, No User Found With This Id.');
		}

		// 2. check if PromoCode is valid 
		$valid_promocode = PromoCodeLib::validatePromoCode($request);

		if (!$valid_promocode['is_promocode_valid']) {
			throw new ApiGenericException('Error Occured, Promocode Can Not Be Added');
		}

		$usedpromocode = UserPromoCode::where(['promocode' => $request->promocode, 'user_id' => $request->user_id])->first();


		if ($usedpromocode) {

			$result = UserPromoCode::where(['promocode' => $request->promocode, 'user_id' => $request->user_id])->delete();
			if (!$result) {
				throw new ApiGenericException('Error Occured, User promo code Could Not Be Deleted');
			}

			return [
				'is_promocode_delete' => 1,
				'user_id' => $user->id,
				'promocode' => $valid_promocode['promocode']->promocode,
				'message' => 'Promocode is deleted with user'
			];
		} else {

			throw new ApiGenericException('User promo code is not exist in record');
		}
	}


	public function promotionsemail(Request $request)
	{
		$this->validate(
			$request,
			[
				'nPromotionCodeEmail' => 'required|email'
			],
			['nPromotionCodeEmail.required' => 'Please input a valid email address.', 'nPromotionCodeEmail.email' => 'Please input a valid email address.']
		);
		$promotionsId = $request->npromotionsId;
		$promotion = Promotion::find($promotionsId);
		if (!$promotion) {
			throw new ApiGenericException('Error Occured, Could Not Find Promotion');
		}
		$PromotionCodeEmail = $request->nPromotionCodeEmail;
		$fileName = ucwords($promotion->name) . '.xls';
		$file = storage_path('exports/') . $fileName;
		$data = ['nPromotionCodeEmail' => $PromotionCodeEmail, 'npromotionsId' => $promotionsId, 'body' => "Dear User, please find attached promocode"];

		try {
			// Change Request PIMS-12502 : Vijay - 30-01-2025 
			if ($promotion->owner_id == config('parkengage.PARTNER_RevPass')) {
				$subject = "Validation Code";
				$data['tagline']       = 'Team RevPASS';
				$data['mailbody']      = "Please find attached Validation Code.";
			} else {
				$subject = "Promotion Code";
				$data['tagline']        = 'Team ParkEngage';
				$data['mailbody']       = "Please find attached promocode.";
			}
			$data['subject']        = $subject;

			$filedata['type']       = 'saved';
			$filedata['content']    = $file;
			$filedata['filename']   = $file;
			$filedata['format']     = 'xls';

			$data['filedata'] = $filedata;

			MailHelper::sendEmail($PromotionCodeEmail, 'email.promotion_generic', $data, $promotion->owner_id);
			$this->log->info("Mail sent to success : " . $PromotionCodeEmail);
			// Change Close : PIMS-12502

			// Mail::send(
			// 	"email.promotion_generic",
			// 	$data,
			// 	function ($message) use ($PromotionCodeEmail, $file, $fileName) {
			// 		$message->to($PromotionCodeEmail)->subject("Promotion Code");
			// 		$message->from(env('MAIL_DEFAULT_FROM'));
			// 		$message->attach($file, ['as' => $fileName]);
			// 	}
			// );
			return ['EmailSent' => "$PromotionCodeEmail,$promotionsId"];
		} catch (Exception $e) {
			throw new ApiGenericException("Could not add request, " . $e->getMessage());
		}
	}

	/*
*@Auther santosh 21-11-2017
*get all users
*/

	public function getUser()
	{
		$users = DB::table('users')
			->select('users.*')
			->limit(20)
			->orderBy('id', 'desc')
			->get();

		return $users;
	}

	public function updateProfile(Request $request)
	{

		$user = User::find(Auth::user()->id);

		if (!$user) {
			throw new NotFoundException('No User Found.');
		}

		// Get country Code
		$countryCode = QueryBuilder::appendCountryCode();

		//DB::beginTransaction();

		if (isset($request->email) && $request->email !== $user->email) {
			$this->validate($request, User::$validationUpdateProfile, User::$validationUpdateProfileMessage);
			$this->updateEmail($request, $user);
		}

		if (isset($request->phone)) {
			$usersData = User::where('created_by', Auth::user()->created_by)->where('phone', $countryCode . $request->phone)->where('email', $request->email)->first();
			if (!$usersData) {
				$chkusers = User::where('created_by', Auth::user()->created_by)->where('phone', $countryCode . $request->phone)->first();
				if ($chkusers) {
					throw new ApiGenericException('Phone number is associated with other user.');
				}
			}
		}


		try {

			$user->name = $request->name;
			$user->phone = $countryCode . $request->phone;

			if (isset($request->address) && !empty($request->address))
				$user->address = $request->address;

			if (isset($request->address2) && !empty($request->address2))
				$user->address2 = $request->address2;

			if (isset($request->pincode) && !empty($request->pincode))
				$user->pincode = $request->pincode;

			if (isset($request->state) && !empty($request->state))
				$user->state = $request->state;

			if (isset($request->city) && !empty($request->city))
				$user->city = $request->city;

			if (isset($request->country) && !empty($request->country))
				$user->country = $request->country;

			if (isset($request->dob) && !empty($request->dob))
				$user->dob = $request->dob;

			if (isset($request->company_name) && !empty($request->company_name))
				$user->company_name = $request->company_name;

			$user->save();
			return $user;
		} catch (Exception $e) {
			//DB::rollback();
			throw new ApiGenericException('Unable to update user details.');
		}
		//  DB::commit();
	}

	/*
	*@Auther santosh 21-11-2017
	*get search user by email or name
	*/
	public function userSearch($search)
	{

		$users = User::query();
		if ($search) {
			$users = QueryBuilder::buildSearchQuery($users, $search, User::$searchFields)
				->orWhereHas(
					'monthlyParkingAccounts',
					function ($query) use ($search) {
						$query->where('account_number', 'like', "%{$search}%");
					}
				);
		}
		return $users->paginate(20);
	}

	/*
*@Auther santosh 21-11-2017
*get  user  details by user id 
*/

	public function userDetails($userID)
	{
		// echo $userID;die;
		// 1. Check if user_id is provided
		//$this->validate($request, User::$validateUserId);

		// 2. check if user exists or not
		$userId = $userID;
		$user = User::where('id', $userId)->first();

		if (!$user) {
			throw new ApiGenericException('Invalid User, No User Found With This Id.');
		}

		$user_id = $user->id;
		$user_balance = DB::table('users')
			->leftJoin('mp_users', 'users.id', '=', 'mp_users.user_id')
			->leftJoin('icongo_wallet', 'users.id', '=', 'icongo_wallet.user_id')
			->select('users.id as user_id', 'users.name', 'users.email', 'icongo_wallet.balance', 'mp_users.account_number')
			->where('users.id', $user_id)
			->get();
		$user_credit_details = DB::table('transactions')
			->leftJoin('users', 'users.id', '=', 'transactions.user_id')
			->select('users.id as user_id', 'users.name', 'users.email', 'transactions.type', 'transactions.mode', 'transactions.amount', 'transactions.mode')
			->where('transactions.user_id', $user_id)
			->get();
		$currentDate = date('Y-m-d');
		$userPromocoden = null;

		if ($user) {
			$user_id = $user->id;
			$userPromocode = DB::table('users_promo_codes')
				->Join('promo_codes', 'users_promo_codes.promocode', '=', 'promo_codes.promocode')
				->Join('promotions', 'promotions.id', '=', 'promo_codes.promotion_id')
				->select('users_promo_codes.promocode', 'users_promo_codes.user_id', 'promotions.name', 'promotions.promo_type_id', 'promo_codes.usage')
				->where('users_promo_codes.user_id', $user_id)
				->get();
		}

		$usedpromocodebyuser = DB::table('promo_usages')
			->Join('promo_codes', 'promo_usages.promocode', '=', 'promo_codes.promocode')
			->Join('promotions', 'promotions.id', '=', 'promo_codes.promotion_id')
			->select('promo_usages.promocode', 'promo_usages.user_id', 'promotions.name', 'promotions.promo_type_id', 'promo_codes.usage')
			->where('promo_usages.user_id', $userId)
			->get();

		$fianlPromo = array_merge($userPromocode, $usedpromocodebyuser);
		$fianlPromo = array_map("unserialize", array_unique(array_map("serialize", $fianlPromo)));

		if (!empty($fianlPromo)) {

			foreach ($fianlPromo as $upck => $upcv) {

				$upcvuser_id = $upcv->user_id;
				$upcvpromocode = $upcv->promocode;
				$usedpromocode = DB::table('promo_usages')
					->select(DB::raw('count(promocode) as consume'))
					->where('user_id', $userId)
					->where('promocode', $upcvpromocode)
					->get();
				//check for single use promo type expire or not
				$is_consume = $usedpromocode[0]->consume;
				if ($upcv->promo_type_id == 2 && $is_consume == 0) {
					$usedpromocode = DB::table('promo_codes')
						->select(DB::raw('count(promocode) as consume'))
						->where('promocode', $upcvpromocode)
						->where('is_expired', 1)
						->get();

					$check_consume = $usedpromocode[0]->consume;
					if ($check_consume > 0) {
						$usedpromocode[0]->consume = "Consumed by other user";
					} else {
						$usedpromocode[0]->consume = 0;
					}
				}


				$upcv->consume = $usedpromocode[0]->consume;
				$userPromocoden[] = $upcv;
			}
		}

		return [
			'is_promocode_assign' => 1,
			'user_id' => $userId,
			'user_details' => $user_balance,
			'transaction_details' => $user_credit_details,
			'promocodedetail' => $userPromocoden
		];
	}


	public function userCredits(Request $request)
	{


		$users = User::query();

		if ($request->search) {

			$users = QueryBuilder::buildSearchQuery($users, $request->search, User::$searchFields)
				->orWhereHas(
					'monthlyParkingAccounts',
					function ($query) use ($request) {
						$query->where('account_number', '=', "$request->search");
						//                                $query->where('account_number', 'like', "%{$request->search}%"); //change this query by above as multiple account was getting fired.
					}
				);
		}

		$userrec = $users->paginate(20);
		$useridarr = array();

		foreach ($userrec as $uskey => $usval) {

			$useridarr[] = $usval->id;
		}

		$user_balance = DB::table('icongo_wallet')
			->select('user_id', 'balance')
			->whereIn('user_id', $useridarr)->get();

		//print_r($user_balance[);
		$userbalance = array();
		$userid = array();
		foreach ($user_balance as $ubkey => $ubval) {

			$userbalance[$ubval->user_id] = $ubval->balance;
			$userid[] = $ubval->user_id;
		}

		foreach ($userrec as $uskey => $usval) {

			if (in_array($usval->id, $userid)) {

				if ($userbalance[$usval->id] == 0 || $userbalance[$usval->id] == "0" || $userbalance[$usval->id] == "")
					$userbalance[$usval->id] = '0.00';

				$usval->balance = $userbalance[$usval->id];
			} else {
				$usval->balance = '0.00';
			}
		}

		//print_r($userrec);

		return $userrec;

		exit;
	}

	public function userTransactions(Request $request)
	{

		$utransactions = DB::table('transactions')
			->select('transactions.id')
			->where('transactions.user_id', $request->userId)
			->whereNull('transactions.deleted_at')->first();
		if (!$utransactions) {
			$user_balance = DB::table('users')
				->select('users.id as user_id', 'users.name', 'users.email')
				->where('users.id', $request->userId)
				->whereNull('users.deleted_at');
		} else {

			$user_balance = DB::table('transactions')
				->Join('users', 'users.id', '=', 'transactions.user_id')
				->select('users.id as user_id', 'users.name', 'users.email', 'transactions.type', 'transactions.reservation_id', 'transactions.mode', 'transactions.amount', 'transactions.id', 'transactions.admin_reason')
				->where('users.id', $request->userId)
				->orderBy('transactions.id', 'desc')
				->whereNull('transactions.deleted_at');
		}

		// echo$request->search;exit;

		if ($request->search) {
			$user_balance->where('users.name', 'like', "%" . $request->search . "%");

			$user_balance->orWhere('users.email', 'like', "%" . $request->search . "%")->where('icongo_wallet.balance', '!=', "0");

			$user_balance->orWhere('mp_users.account_number', '=', "%" . $request->search . "%");
		}


		return $user_balance->paginate(20);
		//print_r($user_balance);exit;die;


	}



	public function deleteUserTransactions(Request $request)
	{

		$userData = Auth::user();
		//print_r($userData->name);exit;
		$result = DB::table('transactions')->select('user_id', 'amount')->where('id', $request->tid)->first();


		$delete_result = DB::table('transactions')
			->where('id', $request->tid)
			->update(array('deleted_by' => $userData->id, 'deleted_at' => date('Y-m-d H:i:s')));



		$wallet = DB::table('icongo_wallet')->select('balance')->where('user_id', $result->user_id)->first();

		if ($wallet->balance > 0)
			$updated_amount = ($wallet->balance) - ($result->amount);

		$resultn = DB::table('icongo_wallet')
			->where('user_id', $result->user_id)
			->update(array('balance' => $updated_amount, 'updated_at' => date('Y-m-d H:i:s')));


		return $resultn;
	}

	public function addUserTransaction(Request $request)
	{
		$userData = Auth::user();
		$ref_user_id = null;
		$reservation_id = null;
		$walletConfig = Wallet::getConfig();
		// if($ref_user_id) {
		$userData = Auth::user();


		$transactionEntry = array(
			'user_id' => $request->ncredutUserId,
			'type' => 'CREDIT',
			'mode' => $userData->name,
			'amount' => $request->ncreditAmount,
			'ref_user_id' => $ref_user_id,
			'reservation_id' => $reservation_id,
			'admin_reason' => $request->nCreditReason,
			'created_at' => date('Y-m-d H:i:s')
		);

		$result = DB::table('transactions')->insert($transactionEntry);
		$user_id = $request->ncredutUserId;

		if (!$result) {
			return false;
		} else {
			$wallet = DB::table('icongo_wallet')->select('balance')->where('user_id', $user_id)->first();
			$updatedBalance = '';
			if (is_object($wallet)) {
				$updatedBalance = $wallet->balance;
			}
			$updatedBalance = (int) $updatedBalance + (int) $request->ncreditAmount;

			if (!is_object($wallet)) {
				$walletEntry = [
					'user_id' => $user_id,
					'status' => 1,
					'balance' => $updatedBalance,
					'created_at' => date('Y-m-d H:i:s'),
					'updated_at' => date('Y-m-d H:i:s')
				];
				$result = DB::table('icongo_wallet')->insert($walletEntry);
			} else {
				$result = DB::table('icongo_wallet')
					->where('user_id', $user_id)
					->update(array('balance' => $updatedBalance, 'updated_at' => date('Y-m-d H:i:s')));
			}
		}



		if (!$result) {
			throw new UserWithEmailExistsException('Some Error Occured, Discount Could Not Be Added To Wallet');
		}

		return $request->ncreditAmount;
	}

	public function searchUserStr(Request $request)
	{
		if ($request->searchValue == '') return false;
		//$users = DB::table('users')->skip(10)->take(5)->get();

		$user_balance = DB::table('users')
			->select('users.id as val', 'users.name')
			->where('users.name', 'like', $request->searchValue . '%')
			->whereNull('users.deleted_at')
			->take(20)->get();
		//print_r($user_balance);exit;die;
		return $user_balance;
	}

	// decrypt user information used for app and web communications
	public function decryptUser(Request $request)
	{

		$this->validate($request, array('keyword' => 'required'));

		try {
			$key = env('PCI_ENCRYPTION_KEY');
			$mc = new MagicCrypt($key, 256);
			$decryptedNonce = $mc->decrypt($request->keyword);
			parse_str($decryptedNonce, $returnData);
			return $returnData;
		} catch (Exceptions $e) {
			throw new ApiGenericException("Invalid data " . $e->getMessage());
		}
	}

	public function validateExistingUser(Request $request)
	{
		$existingUser = User::where('email', $request->email)->where('anon', 0)->first();

		if ($existingUser) {

			if ($request->is_loyalty) {
				$errorMsg = '<div>User with email address already registered.</div><div class="toaster-msg mar-top-xs pad-left-xs">Please log on with your existing account and select ENROLL IN ICON REWARDS from the MY ACCOUNT page.</div>';
			} else {
				$errorMsg = 'User with email address already registered.';
			}

			throw new UserWithEmailExistsException($errorMsg);
		}

		return [
			'message' => 'Email ID available'
		];
	}

	public function show($id)
	{
		$users = User::with(['userPaymentGatewayDetail', 'membershipPlans' => function ($query) {
			$query->select('membership_plans.id', 'name', 'is_trial', 'start_date', 'end_date', 'service_id');
			$query->with('service', 'permission');
		}]);

		$authenticated_user = Auth::user();
		//check if logged in user is partner or partner-user
		if ($authenticated_user->user_type == '3' || $authenticated_user->user_type == '4' || $authenticated_user->user_type == '12') {
			$users->where(function ($query) use ($authenticated_user) {
				$query->where('created_by', $authenticated_user->id);
			});
		}
		$users =  $users->where('id', $id)->first();

		return $users;
	}

	public function getUserList()
	{
		$users = DB::table('users')
			->Join('permit_vehicles', 'permit_vehicles.user_id', '=', 'users.id')
			->select('users.id', 'users.email', 'permit_vehicles.license_plate_number')
			->where('created_by', Auth::user()->id)
			->orderBy('id', 'desc')
			->get();
		return $users;
	}

	public function changePassword(Request $request)
	{
		$this->validate($request, User::$validateUserPassword);

		$user = User::where('id', Auth::user()->id)->first();
		if ($user) {

			if (!Hash::check($request->old_password, $user->password)) {
				throw new ApiGenericException('Old password does not match.');
			}

			$user->password = Hash::make($request->password);
			$user->save();
		}

		return $user;
	}



	public function getCurrentTime(Request $request)
	{
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new ApiGenericException('Invalid partner.');
			}
			$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
			if ($partnerTimezone) {
				if ($partnerTimezone->timezone != '') {
					date_default_timezone_set($partnerTimezone->timezone);
					$date = date('Y-m-d H:i:s');
					return $date;
				} else {
					date_default_timezone_set(config('app.timezone'));
					$date = date('Y-m-d H:i:s');
					return $date;
				}
			} else {
			}
		}
	}

	public function updateProfilePassword(Request $request) 
    {
		$user = User::select('id','name','password','email','phone','pincode','city','state','address','address2','country','status','business_id','user_parent_id','attendent_type','user_type')->find(Auth::user()->id);
		if (!$user) {
			throw new NotFoundException('No User Found.');
		}
		// Get country Code
		$countryCode = QueryBuilder::appendCountryCode();
		// $this->validate($request, User::$validatePassword);
		if(isset($request->phone)) {
		    $usersData = User::where('created_by', Auth::user()->created_by)->where('phone', $countryCode.$request->phone)->where('email', $request->email)->first();
			if(!$usersData) {
				$chkusers = User::where('created_by', Auth::user()->created_by)->where('id','!=', Auth::user()->id)->where('phone', $countryCode.$request->phone)->first();
				if($chkusers) {
					throw new ApiGenericException('Phone number is associated with other user.');
				}
			}
		}
		try {
			$user->name = $request->name;
			$user->phone = $countryCode.$request->phone;
			if (isset($request->address) && !empty($request->address))
				$user->address = $request->address;
			if (isset($request->address2) && !empty($request->address2))
				$user->address2 = $request->address2;
			if (isset($request->pincode) && !empty($request->pincode))
				$user->pincode = $request->pincode;
			if (isset($request->state) && !empty($request->state))
				$user->state = $request->state;
			if (isset($request->city) && !empty($request->city))
				$user->city = $request->city;
			if (isset($request->country) && !empty($request->country))
				$user->country = $request->country;
			if (isset($request->dob) && !empty($request->dob))
				$user->dob = $request->dob;
			if (isset($request->company_name) && !empty($request->company_name))
				$user->company_name = $request->company_name;
			if (isset($request->password) && !empty($request->password)) {
				if (!Hash::check($request->old_password, $user->password)) {
					throw new ApiGenericException('Old password does not match.');
				}
				$user->password = Hash::make($request->password);
			}
			$user->save();
			return $user;
		} catch (Exception $e) {
			//DB::rollback();
			throw new ApiGenericException('Unable to update user details.');
		}
		//  DB::commit();
	}
}
