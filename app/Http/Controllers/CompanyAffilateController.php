<?php

namespace App\Http\Controllers;

use Auth;
use Config;
use App\Models\CompanyAffilate;
use Illuminate\Http\Request;
use App\Http\Requests;

class CompanyAffilateController extends Controller
{

    /**
     * Create company affilate user
     */
    public function createCompanyUser(Request $request)
    {
        $this->validate(
            $request,
            [
                'name' => 'required'
            ]
        );
        try {
            $slug = str_slug($request->name) . '-' . rand(100, 9999);
            $request->request->add(['slug' => $slug]);

            $companyAffilate = new CompanyAffilate();
            $companyAffilate->fill($request->only(['name', 'slug']));
            $companyAffilate->save();
        } catch (\Exception $e) {
            throw new ApiGenericException('Please provide required data \social_token\ missing.');
        }

        return $return = [
            'name' => $companyAffilate->name,
            'slug' => $companyAffilate->slug
        ];
    }
}
