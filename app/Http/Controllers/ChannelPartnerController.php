<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Models\ChannelPartner;

use App\Exceptions\ApiGenericException;

class ChannelPartnerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $channelPartners = ChannelPartner::where('status', 1)->get();
        return $channelPartners;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, ChannelPartner::$storeValidationRules);

        $channelPartner = new ChannelPartner();

        $channelPartner->fill($request->only(['name', 'description', 'status']));
        $result = $channelPartner->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Channel Partner Could Not Be Saved');
        }

        return [
            'success' => true,
            'channelPartner' => $channelPartner
        ];
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        if (!$id) {
            throw new ApiGenericException('Please Provide Valid Promo Type Id');
        }

        $channelPartner = ChannelPartner::where(['id' => $id, 'status' => 1])->first();

        if (!$channelPartner) {
            throw new ApiGenericException('Couldn\'t Find Any Channel Partner With Given Id');
        }

        return [
            'success' => true,
            'channelPartner' => $channelPartner
        ];
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int                      $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $this->validate($request, ChannelPartner::$updateValidationRules);

        $channelPartner = ChannelPartner::where('id', $request->id)->first();

        if (!$channelPartner) {
            throw new ApiGenericException('Couldn\'t Find Any Channel Partner With Given Id');
        }

        $channelPartner->fill($request->only(['name', 'description', 'status']));
        $result = $channelPartner->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Channel Partner Could Not Be Updated');
        }

        return [
            'success' => true,
            'channelPartner' => $channelPartner
        ];
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {

        $channelPartner = ChannelPartner::where('id', $request->id)->first();

        if (!$channelPartner) {
            throw new ApiGenericException('Couldn\'t Find Any Channel Partner With Given Id');
        }

        $result = $channelPartner->delete();
        
        if (!$result) {
            throw new ApiGenericException('Error Occured, Channel Partner Could Not Be Deleted');
        }

        return [
            'success' => true,
        ];
    }
}
