<?php

namespace App\Http\Controllers;

use App\Http\Helpers\QueryBuilder;
use App\Models\SimpleMonthlyRequest;

use Illuminate\Http\Request;

use App\Http\Requests;

class SimpleMonthlyRequestController extends Controller
{
    public function index(Request $request)
    {
        $simpleMonthlyRequest = SimpleMonthlyRequest::query();

        if ($request->search) {
            $simpleMonthlyRequest = QueryBuilder::buildSearchQuery($simpleMonthlyRequest, $request->search, SimpleMonthlyRequest::$searchFields);
        }

        return $simpleMonthlyRequest->orderBy('name')->paginate(50);
    }
    
    public function show(SimpleMonthlyRequest $simpleMonthlyRequest)
    {
        return $simpleMonthlyRequest;
    }

    public function delete(SimpleMonthlyRequest $simpleMonthlyRequest)
    {
        $simpleMonthlyRequest->delete();

        return $simpleMonthlyRequest;
    }

    public function store(Request $request)
    {
        $this->validate($request, SimpleMonthlyRequest::$validParams);
        $simpleMonthlyRequest = new SimpleMonthlyRequest();
        $simpleMonthlyRequest->fill($request->all());
        $simpleMonthlyRequest->save();
        $simpleMonthlyRequest->email();
        return $simpleMonthlyRequest;
    }

    public function update(Request $request, SimpleMonthlyRequest $simpleMonthlyRequest)
    {
        $simpleMonthlyRequest->fill($request->all());
        $simpleMonthlyRequest->save();

        return $simpleMonthlyRequest;
    }
}
