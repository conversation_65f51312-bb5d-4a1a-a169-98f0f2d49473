<?php

namespace App\Http\Controllers;

use App\Models\CouponOffer;
use Illuminate\Http\Request;

class CouponOfferController extends Controller
{
    public function index()
    {
        return CouponOffer::all();
    }

    public function show(CouponOffer $offer)
    {
        return $offer;
    }

    public function store(Request $request)
    {
        $this->validate($request, CouponOffer::validParams());

        return CouponOffer::create($request->all());
    }

    public function destroy(CouponOffer $offer)
    {
        $offer->delete();

        return 'Offer deleted';
    }

    public function update(CouponOffer $offer, Request $request)
    {
        $this->validate($request, CouponOffer::validParams());

        $offer->update($request->all());

        return $offer;
    }
}
