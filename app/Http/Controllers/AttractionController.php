<?php

namespace App\Http\Controllers;

use App\Models\Attraction;
use App\Models\AttractionType;
use App\Models\GeoLocation;
use Illuminate\Http\Request;

use App\Http\Helpers\QueryBuilder;

class AttractionController extends Controller
{

    public function index(Request $request)
    {
        $attractions = Attraction::query();

        if ($request->search) {
            $attractions = QueryBuilder::buildSearchQuery($attractions, $request->search, Attraction::$searchFields);
        }

        return $attractions->with('attractionType', 'geolocations')->orderBy('name')->paginate(50);
    }

    public function fetchAll(Request $request)
    {
        $attractions = Attraction::with('attractionType')->orderBy('attraction_type_id')->get();
        $attractionList = $attractions->groupBy('attraction_type_id')->keyBy(
            function ($item) {
                return $item[0]->attractionType->name;
            }
        )->toArray();
        return $attractionList;
    }

    public function show(Attraction $attraction)
    {
        return $attraction->load('attractionType', 'geolocations');
    }

    public function store(Request $request)
    {
        $this->validate($request, Attraction::$validParams);
        $attraction = Attraction::create($request->all());
        $geolocation = new GeoLocation();
        $geolocation->fill($request->geolocations);
        $attraction->geolocations()->save($geolocation);
        $attraction->save();
        return $attraction->load('attractionType', 'geolocations');
    }

    public function destroy(Attraction $attraction)
    {
        $attraction->geolocations()->delete();
        $attraction->delete();
        return $attraction;
    }

    public function update(Attraction $attraction, Request $request)
    {
        $this->validate($request, Attraction::$validParams);
        $attraction->fill($request->all());
        if (!$attraction->geolocations) {
            $geolocation = new GeoLocation();
            $geolocation->fill($request->geolocations);
            $attraction->geolocations()->save($geolocation);
        } else {
            $attraction->geolocations->fill($request->geolocations)->save();
            $attraction->fill($request->all())->save();
        }
        return $attraction->load('geolocations', 'attractionType');
    }

    public function getAttractionBySlug($type, $slug)
    {
        $attraction = Attraction::where('slug', $slug)->whereHas(
            'attractionType', function ($query) use ($type) {
                $query->where('slug', $type);
            }
        );

        return $attraction->with('attractionType', 'geolocations')->first();
    }
}
