<?php

namespace App\Http\Controllers;

use Auth;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use App\Exceptions\NotFoundException;
use App\Models\Facility;
use App\Models\ParkEngage\EventCategory;
use App\Models\ParkEngage\EventCategoryEvent;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserNotAuthorized;
class EventCategoryController extends Controller
{
    const SUPERADMIN = 1;
    const SUBORDINATE = 4;
    const PARTNER = 3;
    const REGIONAL_MANAGER = 12;
    const BUSINESS_USERTYPE = 10;
	const BUSINESS_CLERK = 8;
    /**
     * Get a paginated list of all users
     *
     * @return [type] [description]
     */
    public function index(Request $request)
    {
        /*
        if($request->partner_id)
        {
            $partner_id = $request->partner_id;
        }
        else{
            $partner_id = Auth::user()->id;
        }
        */
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
        } else if (Auth::user()->user_type == self::BUSINESS_USERTYPE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else {
            throw new UserNotAuthorized("Invalid User!");
        }

        $eventCategories = EventCategory::with('eventCategoryEvent.event');
        if ($request->search) {
            //$eventCategories = QueryBuilder::buildSearchQuery($eventCategories, $request->search, EventCategory::$searchFields);
            /* $eventCategories = EventCategory::with('eventCategoryEvent.event')->where('partner_id', $partner_id)
            ->where('name', 'like', '%' . $request->search_value . '%')
            ->orderby('id','desc')->paginate(10);*/
            $eventCategories = $eventCategories->where('name', 'like', '%' . $request->search . '%');
        }

        $eventCategories = $eventCategories->orderby('id', 'desc');
        if ($partner_id) {
            $eventCategories = $eventCategories->where('partner_id', $partner_id);
        }
        if ($rm_id) {
            //      $eventCategories = $eventCategories->where('created_by', $rm_id);
        }
        $eventCategories = $eventCategories->paginate(10);
        $data = [];
        foreach ($eventCategories as $eventCategory) {
            if ($eventCategory->eventCategoryEvent) {
                $event_name = '';
                foreach ($eventCategory->eventCategoryEvent as $value) {
                    if ($value->event) {
                        $event_name .= $value->event->title . ',   ';
                    }
                }
                $eventCategory['event_name'] = rtrim($event_name, ',   ');
            }
        }
        return $eventCategories;
    }
    public function addEventCategory(Request $request)
    {
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_USERTYPE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else {
            throw new ApiGenericException("Invalid User!");
        }
        //validation check
        if ($partner_id != '' && $rm_id != '') {
            $validationExist = EventCategory::where('name', $request->name)->where('partner_id', $partner_id)->where('rm_id', $rm_id)->first();
        } else {
            $validationExist = EventCategory::where('name', $request->name)->where('partner_id', $partner_id)->first();
        }
        if ($validationExist) {
            throw new ApiGenericException("Category name already exist.");
        }
        $eventCategory = EventCategory::Create([
            'name' => $request->name,
            'partner_id' => $partner_id,
            'rm_id' => $rm_id,
            'created_by' => $created_by,
        ]);

        if (count($request->events) > 0) {
            foreach ($request->events as $event) {
                $data['event_id'] = $event;
                $data['event_category_id'] = $eventCategory->id;
                EventCategoryEvent::create($data);
            }
        }

        $eventname = \DB::table('events')
            ->join('event_category_events', 'event_category_events.event_id', '=', 'events.id')
            ->select('title as event_name')
            ->where('event_category_events.event_category_id', $eventCategory->id)
            ->get();


        $event_name = '';
        foreach ($eventname as $val) {
            if ($val->event_name != '') {
                $event_name .= $val->event_name . ',';
            }
        }

        $eventCategory['event_name'] = rtrim($event_name, ',');

        return  $eventCategory;
    }

    public function getEventCategoryList(Request $request)
    {
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
        } else if (Auth::user()->user_type == self::BUSINESS_USERTYPE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else {
            throw new UserNotAuthorized("Invalid User!");
        }
        $events = \DB::table('event_categories')
            ->select('id as id', 'id as val', 'name as name');
        if ($partner_id) {
            $events = $events->where('partner_id', $partner_id);
        }
        if ($rm_id) {
            //    $events =$events->where('created_by', $rm_id);
        }
        $events = $events->orderby('id', 'desc')->get();
        if (!$events) {
            throw new ApiGenericException("Event Category Not Found.");
        }
        return $events;
    }
    public function deleteEventCategory($id)
    {

        $status = EventCategory::where('id', $id)->first();

        if ($status) {
            $eventcategory = EventCategory::where('id', $id)->delete();
            $events = EventCategoryEvent::where('event_category_id', $id)->delete();
            return $id;
        } else {
            throw new NotFoundException('No Data found.');
        }
    }
    // for update 
    public function updateEventCategory(Request $request)
    {
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_USERTYPE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else {
            throw new UserNotAuthorized("Invalid User!");
        }

        $eventCategory = EventCategory::find($request->id);
        if (!$eventCategory) {
            throw new NotFoundException('No Data found.');
        }

        //validation check at update time
        $isvalidationExist = EventCategory::where('id', $request->id)->first();
        if ($isvalidationExist->name != $request->name) {
            if ($request->partner_id != '' && $request->rm_id != '') {

                $validationExist = EventCategory::where('partner_id', $request->partner_id)->where('rm_id', $request->rm_id)->where('created_by', $created_by)->first();
                if ($validationExist->name == $request->name) {

                    throw new ApiGenericException("Category name already exist.");
                }
            } else {

                $validationExist = EventCategory::where('partner_id', $request->partner_id)->where('created_by', $created_by)->first();
                if ($validationExist->name == $request->name) {
                    throw new ApiGenericException("Category name already exist.");
                }
            }
        }
        $eventCategory->name = $request->name;
        $eventCategory->save();

        $status = EventCategoryEvent::where('event_category_id', $request->id)->delete();

        if (count($request->events) > 0) {
            foreach ($request->events as $event) {
                $data['event_id'] = $event;
                $data['event_category_id'] = $request->id;
                EventCategoryEvent::create($data);
            }
        }

        return  $eventCategory;
    }


    // for edit category event
    public function editEventCategory($id)
    {

        if (isset($id) && $id != '') {
            $eventCategories = EventCategory::with('eventCategoryEvent.event')->where('id', $id)->first();
            return $eventCategories;
        } else {
            throw new NotFoundException('No Event Category found.');
        }
    }
}
