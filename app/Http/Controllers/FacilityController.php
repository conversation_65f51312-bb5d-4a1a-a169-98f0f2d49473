<?php

namespace App\Http\Controllers;

use Log;
use App\Classes\Inventory;
use App\Classes\MessageRateLimiter;
use App\Classes\TextMessaging;
use Exception;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Jobs\SendFacilityText;
use App\Models\Facility;
use App\Models\FacilityRate;
use App\Models\FacilityType;
use App\Models\Feature;
use App\Models\GeoLocation;
use App\Models\FacilityFee;
use App\Models\HoursOfOperation;
use App\Models\Neighborhood;
use App\Models\Photo;
use App\Models\PermitRateCriteria;
use App\Models\PermitServices;
use App\Models\PermitRequest; #pims-13373
use App\Models\PermitRateCriteriaMapping;
use App\Models\RateDescription;
use App\Services\Mailers\UserMailer;
use Artisan;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Intervention\Image\Facades\Image;
use Webpatser\Uuid\Uuid;
use App\Models\MonthlyCampaign;

//for inventory update on hours of operation
use App\Models\FacilityAvailability;
use App\Models\FacilityInventory;
use App\Models\FacilityPartnerAvailabilityCron;
use App\Models\Reservation;
use App\Models\FacilityInventorySpecificDate;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Http\Helpers\QueryBuilder;
use App\Models\OauthClient;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use App\Models\PermitRateDiscount;
use App\Models\ParkEngage\FacilityQrcode;
use App\Services\Pdf;
use Response;
use App\Models\ParkEngage\Gate;
use App\Models\ParkEngage\BrandSetting;
use Illuminate\Support\Facades\DB;
use App\Models\ParkEngage\FacilityPaymentDetail;
use App\Models\ParkEngage\UserFacility;
use App\Models\ParkEngage\PermitTypeFacilityMapping;
use App\Models\ParkEngage\PermitTypeMaster;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\ParkEngage\MasterFacilityShortCode;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\PermitServicesFacilityMapping;
use App\Models\ParkEngage\PartnerPaymentGateway;
use App\Models\ParkEngage\ShortCodeFacilityMapping;
use Grimzy\LaravelMysqlSpatial\Types\Polygon;
use Grimzy\LaravelMysqlSpatial\Types\LineString;
use Grimzy\LaravelMysqlSpatial\Types\Point;
//UPBL-87
use App\Classes\ParkengageGateApi;
use App\Models\ParkEngage\LPRFeed;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\Ticket;
use App\Models\ParkEngage\FacilitySlot;

class FacilityController extends Controller
{
    protected $mail;
    protected  $log;
    protected  $request;
    const TIMEOUT_VAL = 2;

    // for inventory update on hours of operation
    const DAY_START_HOUR              = 0;
    const DAY_END_HOUR                = 23;
    const NUMBER_OF_SPOTS             = 50;
    const PARTNER_SPOTS_AVAILABLE     = 1;
    const PARTNER_SPOTS_NOT_AVAILABLE = 0;
    const ENTRY_GATE = 11;
    const Exit_GATE = 22;

    // How to setup for sending mail
    public function __construct(UserMailer $mail, Request $request, LoggerFactory $logFactory)
    {
        $this->mail = $mail;
        $this->log = $logFactory->setPath('logs/facility')->createLogger('facility-log');
        $this->request = $request;
    }

    public function evcharging()
    {
        $facilities = Facility::with('geolocations', 'neighborhood')->where('active', '1')->where(function ($query) {
            $query->where('is_generic_ev_charging', '1')->orWhere(
                'is_tesla_charging',
                '1'
            );
        })->get();
        return ['facilities' => $facilities];
    }

    public function index(Request $request)
    {
        $owner_id = 0;
        $userData = User::where('id', Auth::user()->id)->first();
        if ($userData->user_type == '1' || $userData->user_type == '2') {
            $owner_id = $request->partner_id;
            $rm_id = $request->rm_id;
            $facility = DB::table('user_facilities')->where('user_id', $rm_id)->whereNull('deleted_at')->pluck('facility_id');
            $facilities = Facility::with(
                'user',
                'facilityType',
                'geolocations',
                'photos',
                'features',
                'neighborhood',
                'hoursOfOperation',
                'faciltyBrandSetting'
            );
            $facilities = $facilities->where(function ($query) use ($request) {
                $query->where('is_lot', '0');
                if (isset($request->is_lot) && $request->is_lot == '1') {
                    $query->orWhere('is_lot', '1');
                }
            });
            if ($owner_id) {
                $facilities = $facilities->where('owner_id', $owner_id);
            }
            if (($userData->user_type == '1') || ($userData->user_type == '2')) {
                if ($facility) {
                    $facilities = $facilities->where(function ($query) use ($facility) {
                        $query->whereIn('id', $facility);
                    });
                }
            }
            $facilities = $facilities->orderBy('id', 'Desc')->get();
        } else {
            $facility = '';
            if ($userData->user_type == '3') {
                $owner_id = $userData->id;
                $rm_id = $request->rm_id;
                $facility = DB::table('user_facilities')->where('user_id', $rm_id)->whereNull('deleted_at')->pluck('facility_id');
            } else if ($userData->user_type == '4') {
                $owner_id = $userData->created_by;
                $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                /*
                $partnerSubUser = User::where('id', Auth::user()->id)->first();
                if ($partnerSubUser) {
                    $owner_id = $partnerSubUser->id;
                }
                */
            } else if ($userData->user_type == '12') {
                $owner_id = $userData->created_by;
                $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');

                /*
                $partnerSubUser = User::where('id', Auth::user()->id)->where('user_type','12')->first();
                if ($partnerSubUser) {
                    $owner_id = $partnerSubUser->id;
                }
                */
            }
            $facilities = Facility::with(
                'user',
                'facilityType',
                'geolocations',
                'photos',
                'features',
                'neighborhood',
                'hoursOfOperation',
                'faciltyBrandSetting'
            );
            $facilities = $facilities->where(function ($query) use ($request) {
                $query->where('is_lot', '0');
                if (isset($request->is_lot) && $request->is_lot == '1') {
                    $query->orWhere('is_lot', '1');
                }
            });
            if ($owner_id) {
                $facilities = $facilities->where('owner_id', $owner_id);
            }
            if (($userData->user_type == '4') || ($userData->user_type == '12')) {
                $facilities = $facilities->where(function ($query) use ($facility) {
                    $query->whereIn('id', $facility);
                });
            }
            if (($userData->user_type == '3') && ($facility)) {
                $facilities = $facilities->where(function ($query) use ($facility) {
                    $query->whereIn('id', $facility);
                });
            }
            $facilities = $facilities->orderBy('full_name', 'ASC')->get();
        }
        return ['facilities' => $facilities];
    }


    public function indexFacilityList()
    {
        $owner_id = 0;
        $userData = User::where('id', Auth::user()->id)->where('status', 1)->first();

        if ($userData->user_type == '1' || $userData->user_type == '2') {
            if (isset($request->partner_id)) {
                $facilities = Facility::with('facilityType', 'photos')->select('id', 'short_name', 'full_name', 'facility_type_id', 'active')->whereNull('deleted_at')->where('active', 1);
                if ($request->is_event == '1') {
                    $facility_ids = Facility::whereNull('deleted_at')->where('active', 1)->pluck('id');
                    $event_facility = DB::table('event_facility')->whereIn('facility_id', $facility_ids)->pluck('facility_id');
                    $facilities = $facilities->where(function ($query) use ($event_facility) {
                        $query->whereIn('id', $event_facility);
                    });
                }
                if (isset($request->rm_id)) {
                    $facility = DB::table('user_facilities')->where('user_id', $request->rm_id)->whereNull('deleted_at')->pluck('facility_id');
                    $facilities = $facilities->where(function ($query) use ($facility) {
                        $query->whereIn('id', $facility);
                    });
                }
                if (isset($request->is_lot) && $request->is_lot == '1') {
                    $facilities = $facilities->where('is_lot', '0');
                }
            } else {
                $facilities = Facility::with('facilityType', 'photos')->select('id', 'short_name', 'full_name', 'facility_type_id', 'active')->whereNull('deleted_at')->where('active', 1);
                if ($request->is_event == '1') {
                    $facility_ids = Facility::whereNull('deleted_at')->where('active', 1)->pluck('id');
                    $event_facility = DB::table('event_facility')->whereIn('facility_id', $facility_ids)->pluck('facility_id');
                    $facilities = $facilities->where(function ($query) use ($event_facility) {
                        $query->whereIn('id', $event_facility);
                    });
                }
                if (isset($request->is_lot) && $request->is_lot == '1') {
                    $facilities = $facilities->where('is_lot', '0');
                }
                $facilities = $facilities->orderBy('full_name', 'ASC')->get();
            }
        } else {
            $facility = [];
            if ($userData->user_type == '3') {
                $owner_id = $userData->id;
            } else if ($userData->user_type == '4') {
                $admin_partner_id = Auth::user()->created_by;
                if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                    if (isset($request->partner_id)) {
                        $facilities = Facility::with('facilityType', 'photos')->select('id', 'short_name', 'full_name', 'facility_type_id', 'active')->whereNull('deleted_at')->where('active', 1)->where('owner_id', $request->partner_id);
                        if ($request->is_event == '1') {
                            $facility_ids = Facility::whereNull('deleted_at')->where('active', 1)->pluck('id');
                            $event_facility = DB::table('event_facility')->whereIn('facility_id', $facility_ids)->pluck('facility_id');
                            $facilities = $facilities->where(function ($query) use ($event_facility) {
                                $query->whereIn('id', $event_facility);
                            });
                        }
                        if (isset($request->rm_id)) {
                            $facility = DB::table('user_facilities')->where('user_id', $request->rm_id)->whereNull('deleted_at')->pluck('facility_id');
                            $facilities = $facilities->where(function ($query) use ($facility) {
                                $query->whereIn('id', $facility);
                            });
                        }
                        if (isset($request->is_lot) && $request->is_lot == '1') {
                            $facilities = $facilities->where('is_lot', '0');
                        }
                    }
                } else {
                    $owner_id = $userData->created_by;
                    $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                }
            } else if ($userData->user_type == '12') {
                $owner_id = $userData->created_by;
                $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
            }
            $facilities = Facility::with('facilityType', 'photos')->select('id', 'short_name', 'full_name', 'facility_type_id', 'active')->whereNull('deleted_at')->where('active', 1)->where('owner_id', $owner_id);
            if (isset($facility) && !empty($facility)) {
                $facilities = $facilities->where(function ($query) use ($facility) {
                    $query->whereIn('id', $facility);
                });
            }
            if ($request->is_event == '1') {
                $facility_ids = Facility::whereNull('deleted_at')->where('active', 1)->pluck('id');
                $event_facility = DB::table('event_facility')->whereIn('facility_id', $facility_ids)->pluck('facility_id');
                $facilities = $facilities->where(function ($query) use ($event_facility) {
                    $query->whereIn('id', $event_facility);
                });
            }

            if (isset($request->is_lot) && $request->is_lot == '1') {
                $facilities = $facilities->where('is_lot', '0');
            }
            $facilities = $facilities->orderBy('id', 'Desc')->get();
        }
        return ['facilities' => $facilities];
    }


    public function indexFacilityListMobile(Request $request)
    {
        $owner_id = 0;
        $userData = User::where('id', Auth::user()->id)->first();
        if ($userData->user_type == '1' || $userData->user_type == '2') {
            // $facilities = DB::table('facilities')->join('facility_types', 'facility_types.id', '=', 'facilities.facility_type_id')->join('users', 'users.id', '=', 'facilities.owner_id')->select('facilities.id', 'facilities.short_name', 'facilities.full_name', 'facilities.facility_type_id', 'facilities.active', 'facility_types.facility_type', 'users.id as user_id', 'users.name as user_name')->whereNull('facilities.deleted_at');
            $facilities = DB::table('facilities')->join('facility_types', 'facility_types.id', '=', 'facilities.facility_type_id')->join('users', 'users.id', '=', 'facilities.owner_id')->select('facilities.*', 'facility_types.facility_type', 'users.id as user_id', 'users.name as user_name')->whereNull('facilities.deleted_at');

            if ($request->is_indoor_parking == 1) {
                $facilities->where('is_indoor_parking', '1');
            }
            if ($request->is_outdoor_parking == 1) {
                $facilities->where('is_outdoor_parking', '1');
            }
            if ($request->is_tesla_charging == 1) {
                $facilities->where('is_tesla_charging', '1');
            }
            if ($request->is_generic_ev_charging == 1) {
                $facilities->where('is_generic_ev_charging', '1');
            }
            if ($request->is_motorcycle_parking == 1) {
                $facilities->where('is_motorcycle_parking', '1');
            }
            if ($request->is_motorcycle_parking == 1) {
                $facilities->where('is_motorcycle_parking', '1');
            }
            if ($request->status) {
                if ($request->status == 1) {
                    $facilities->where('active', '1');
                }
                if ($request->status == 2) {
                    $facilities->where('active', '0');
                }
            }
            $result = $facilities->orderBy('id', 'Desc')->get();
        } else {
            $facility = [];
            if ($userData->user_type == '3') {
                $owner_id = $userData->id;
                $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
            } else if ($userData->user_type == '4') {
                $partnerSubUser = User::where('owner_id', Auth::user()->id)->first();
                $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                if ($partnerSubUser) {
                    $owner_id = $partnerSubUser->id;
                }
            }
            /*$facilities = Facility::with('facilityType')->select('id','short_name','full_name','facility_type_id','active')->where('owner_id',$owner_id)->get();*/


            $facilities = DB::table('facilities')->join('facility_types', 'facility_types.id', '=', 'facilities.facility_type_id')->join('users', 'users.id', '=', 'facilities.owner_id')->select('facilities.id', 'facilities.short_name', 'facilities.full_name', 'facilities.facility_type_id', 'facilities.active', 'facility_types.facility_type', 'users.id as user_id', 'users.name as user_name')->where('owner_id', $owner_id)->whereNull('facilities.deleted_at');

            if ($request->is_indoor_parking == 1) {
                $facilities->where('is_indoor_parking', '1');
            }
            if ($request->is_outdoor_parking == 1) {
                $facilities->where('is_outdoor_parking', '1');
            }
            if ($request->is_tesla_charging == 1) {
                $facilities->where('is_tesla_charging', '1');
            }
            if ($request->is_generic_ev_charging == 1) {
                $facilities->where('is_generic_ev_charging', '1');
            }
            if ($request->is_motorcycle_parking == 1) {
                $facilities->where('is_motorcycle_parking', '1');
            }
            if ($request->status) {
                if ($request->status == 1) {
                    $facilities->where('active', '1');
                }
                if ($request->status == 2) {
                    $facilities->where('active', '0');
                }
            }
            if (isset($facility) && !empty($facility)) {
                $facilities = $facilities->where(function ($query) use ($facility) {
                    $query->whereIn('id', $facility);
                });
            }
            $result = $facilities->orderBy('id', 'Desc')->get();
        }
        if (count($result > 0)) {
            foreach ($result as $key => $value) {
                $photos = Photo::where('imageable_id', $value->id)->first();
                if (count($photos) > 0) {
                    $value->image_name = Facility::IMAGE_FOLDER . '/' . $photos->image_name;
                    $value->url = config('app.url') . '/photo/' . $photos->id;
                } else {
                    $value->image_name = '';
                    $value->url = '';
                }
            }
        }
        return ['facilities' => $result];
    }

    public function indexNew(Request $request)
    {
        $owner_id = 0;
        $userData = User::where('id', Auth::user()->id)->first();
        if ($userData->user_type == '1' || $userData->user_type == '2') {
            $facilities = Facility::with(
                'user',
                'user.userMember',
                'facilityType',
                'geolocations',
                'photos',
                'features',
                'neighborhood',
                'hoursOfOperation',
                'faciltyBrandSetting'
            );

            // Alka: PIMS::PIMS-10664
            if (isset($request->status) && ($request->status != '')) {
                $facilities = $facilities->where(function ($query) use ($request) {
                    $query->where('active', $request->status);
                });
            }
            // End
            if ($request->sort != '') {
                if ($request->sort == 'facility_type') {
                    $facilities = $facilities->paginate(20);
                } else {
                    $facilities = $facilities->orderBy($request->sort, $request->sortBy)->paginate(20);
                }
            } else {
                $facilities = $facilities->orderBy('is_lot', 'ASC')->paginate(20);
            }
            if ($request->search) {
                if ($request->search == 'NO') {
                    $facilities = Facility::with(
                        'user',
                        'user.userMember',
                        'facilityType',
                        'geolocations',
                        'photos',
                        'features',
                        'neighborhood',
                        'hoursOfOperation',
                        'faciltyBrandSetting'
                    )->where('owner_id', $request->owner_id);
                    // Alka: PIMS::PIMS-10664
                    if (isset($request->status) && ($request->status != '')) {
                        $facilities = $facilities->where(function ($query) use ($request) {
                            $query->where('active', $request->status);
                        });
                    }
                    // End
                    if ($request->sort != '') {
                        if ($request->sort == 'facility_type') {
                            $facilities = $facilities->paginate(20);
                        } else {
                            $facilities = $facilities->orderBy($request->sort, $request->sortBy)->paginate(20);
                        }
                    } else {
                        $facilities = $facilities->orderBy('is_lot', 'ASC')->paginate(20);
                    }
                    return $facilities;
                } else {

                    $facilities = Facility::with(
                        'user',
                        'user.userMember',
                        'facilityType',
                        'geolocations',
                        'photos',
                        'features',
                        'neighborhood',
                        'hoursOfOperation',
                        'faciltyBrandSetting'
                    );


                    //$facilities = QueryBuilder::buildSearchQuery($facilities, $request->search, Facility::$searchFields);
                    $facilities = $facilities->where(function ($query) use ($request) {
                        $query->where('full_name', 'like', "%" . $request->search . "%")
                            ->orWhere('short_name', 'like', "%" . $request->search . "%")
                            ->orWhere('garage_code', 'like', "%" . $request->search . "%");
                    });


                    if ($request->owner_id != '') {
                        $facilities->where('owner_id', $request->owner_id);
                    }
                    // Alka: PIMS::PIMS-10664
                    if (isset($request->status) && ($request->status != '')) {
                        $facilities = $facilities->where(function ($query) use ($request) {
                            $query->where('active', $request->status);
                        });
                    }
                    // End

                    if ($request->sort != '') {
                        if ($request->sort == 'facility_type') {
                            $facilities = $facilities->paginate(20);
                        } else {
                            $facilities = $facilities->orderBy($request->sort, $request->sortBy)->paginate(20);
                        }
                    } else {
                        $facilities = $facilities->orderBy('is_lot', 'ASC')->paginate(20);
                    }
                    return $facilities;
                }
            }
        } else {
            $facilityArr = [];

            if ($userData->user_type == '3') {
                $owner_id = $userData->id;
            } else if (($userData->user_type == '4') || ($userData->user_type == '12')) {
                $owner_id = $userData->created_by;
                $facilityArr = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
            }

            if ($request->search) {
                $facilities = Facility::with(
                    'user',
                    'user.userMember',
                    'facilityType',
                    'geolocations',
                    'photos',
                    'features',
                    'neighborhood',
                    'hoursOfOperation',
                    'faciltyBrandSetting'
                )->where('owner_id', $owner_id)
                    ->where(function ($query) use ($request, $facilityArr, $userData) {
                        $query->where(function ($subQuery) use ($request) {
                            $subQuery->where('full_name', 'like', "%" . $request->search . "%")
                                ->orWhere('short_name', 'like', "%" . $request->search . "%")
                                ->orWhere('garage_code', 'like', "%" . $request->search . "%");
                        });
                        if (($userData->user_type == '4') || ($userData->user_type == '12')) {
                            $query->whereIn('id', $facilityArr);
                        }
                    });
                // Alka: PIMS::PIMS-10664
                if (isset($request->status) && ($request->status != '')) {
                    $facilities = $facilities->where(function ($query) use ($request) {
                        $query->where('active', $request->status);
                    });
                }
                // End
                //$facilities = QueryBuilder::buildSearchQuery($facilities, $request->search, Facility::$searchFields);
                if ($request->sort != '') {
                    if ($request->sort == 'facility_type') {
                        $facilities = $facilities->paginate(20);
                    } else {
                        $facilities = $facilities->orderBy($request->sort, $request->sortBy)->paginate(20);
                    }
                } else {
                    $facilities = $facilities->orderBy('is_lot', 'ASC')->paginate(20);
                }

                return $facilities;
            }
            $facilities = Facility::with(
                'user',
                'user.userMember',
                'facilityType',
                'geolocations',
                'photos',
                'features',
                'neighborhood',
                'hoursOfOperation',
                'faciltyBrandSetting'
            )->where('owner_id', $owner_id);

            $facilities = $facilities->where(function ($query) use ($owner_id) {
                $query->where('owner_id', $owner_id);
            });

            if (($userData->user_type == '4') || ($userData->user_type == '12')) {
                $facilities = $facilities->where(function ($query) use ($facilityArr) {
                    $query->whereIn('id', $facilityArr);
                });
            }

            // Alka: PIMS::PIMS-10664
            if (isset($request->status) && ($request->status != '')) {
                $facilities = $facilities->where(function ($query) use ($request) {
                    $query->where('active', $request->status);
                });
            }
            // End

            if ($request->sort != '') {
                if ($request->sort == 'facility_type') {
                    $facilities = $facilities->paginate(20);
                } else {
                    $facilities = $facilities->orderBy($request->sort, $request->sortBy)->paginate(20);
                }
            } else {
                $facilities = $facilities->orderBy('is_lot', 'ASC')->paginate(20);
            }
            return $facilities;
        }
        if ($request->sort == 'facility_type') {
            if (count($facilities) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($facilities); $i++) {
                        for ($j = $i + 1; $j < count($facilities); $j++) {
                            if ($facilities[$i]['facilityType']->facility_type > $facilities[$j]['facilityType']->facility_type) {
                                $temp = $facilities[$i];
                                $facilities[$i] = $facilities[$j];
                                $facilities[$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($facilities); $i++) {
                        for ($j = $i + 1; $j < count($facilities); $j++) {
                            if ($facilities[$i]['facilityType']->facility_type < $facilities[$j]['facilityType']->facility_type) {
                                $temp = $facilities[$i];
                                $facilities[$i] = $facilities[$j];
                                $facilities[$j] = $temp;
                            }
                        }
                    }
                }
            }
        }
        return $facilities;
    }


    public function facilityLlc(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $facilities = Facility::select('id', 'short_name', 'full_name', 'active', 'garage_code', 'is_lot')->where('active', 1)->with(array('geolocations' => function ($query) {
                $query->select('locatable_id', 'locatable_type', 'longitude', 'latitude');
            }));

            $rmUser = User::with("userFacility")->where("user_type", '12')->where("default_rm", '1')->where('created_by', $secret->partner_id)->first();
            $rmFacility = [];
            if (isset($rmUser->userFacility) && count($rmUser->userFacility) > 0) {
                foreach ($rmUser->userFacility as $userfacility) {
                    $rmFacility[] = $userfacility->facility_id;
                }
                $facilities = $facilities->whereIn("id", $rmFacility);
            }

            $facilities = $facilities->where('owner_id', $secret->partner_id)->get();
        } else {
            $facilities = Facility::select('id', 'short_name', 'full_name', 'active', 'garage_code', 'is_lot')->where('active', 1)->with(array('geolocations' => function ($query) {

                $query->select('locatable_id', 'locatable_type', 'longitude', 'latitude');
            }))->get();
        }

        $facilities->makeHidden(['slug', 'has_ticketech', 'has_monthly', 'is_elimiwait_active', 'display_name']);

        return ['facilities' => $facilities];
    }

    public function getRealTimeConfiguration(Facility $facility)
    {
        if ($facility) {
            return ['realtime_window' => $facility->realtime_window, 'realtime_minimum_availability' => $facility->realtime_minimum_availability, 'inquiry_key_lifetime' => $facility->inquiry_key_lifetime];
        } else {
            throw new ApiGenericException("Facility does not exist.");
        }
    }

    public function show(Facility $facility)
    {
        //return $facility->load('photos', 'facilityType', 'geolocations', 'facilityRate.rateDescription');
        $result       = $facility->load(
            ['facilityfee', 'photos', 'facilityType', 'geolocations', 'faciltyBrandSetting', 'facilityConfiguration', 'service_data', 'facilityRate' => function ($query) {
                $query = $query->with('rateDescription')->where('active', 1)->where('rate', '>', 0);
            }, 'FacilityPaymentDetails']
        );

        $user_ids = DB::table('user_facilities')->where('facility_id', $result->id)->whereNull('deleted_at')->pluck('user_id');
        $rm_user = User::whereIn('id', $user_ids)->where('user_type', '12')->whereNull('deleted_at')->get();

        $result->setRelation('rm_user', $rm_user);

        $monthly_rates = array();
        $monthly_rate = 0;
        $i            = 0;
        $monthlyTaxPrice = 0;
        $monthlyNetPrice = 0;

        foreach ($result->facilityRate as $facilityRate) {

            //calulation net price
            if (isset($facility->tax_rate) && ($facility->tax_rate > 0)) {
                $monthlyNetPrice = (float)(($facilityRate->rate) / (1 + $facility->tax_rate));
                //calulation tax rate price
                $monthlyTaxPrice = (float)(($monthlyNetPrice) * ($facility->tax_rate));
            } else {
                $monthlyNetPrice = $facilityRate->rate;
            }

            $monthly_rates[$i]['rate_type'] = $facilityRate->rateDescription->name;
            $monthly_rates[$i]['rate'] = $facilityRate->rate;
            $monthly_rates[$i]['net_rate'] = number_format($monthlyNetPrice, 2, '.', '');
            $monthly_rates[$i]['tax_rate'] = number_format($monthlyTaxPrice, 2, '.', '');
            $monthly_rates[$i]['active'] = $facilityRate->active;
            $monthly_rates[$i]['facility_id'] = $facility->id;
            $monthly_rates[$i]['id'] = $facilityRate->id;
            $monthly_rates[$i]['rate_description'] = $facilityRate->rateDescription;

            if (((float)$monthlyNetPrice < $monthly_rate || $monthly_rate === 0) && $facilityRate->active == 1 && $monthlyNetPrice > 0) {
                $monthly_rate = round($monthlyNetPrice, 2);
            }
            $i++;
        }
        if ($result) {
            $result->monthly_rate = $monthly_rate;
            $result->facilityMonthlyTaxRates = $monthly_rates;
            if (isset($result->FacilityPaymentDetails) && !empty($result->FacilityPaymentDetails)) {

                $result->facility_payment_type_id = $result->FacilityPaymentDetails->facility_payment_type_id;
            } else {
                $result->facility_payment_type_id = null;
            }
        }

        return $result;
    }

    public function showDetailsAdmin(Facility $facility)
    {
        $userData = User::where('id', Auth::user()->id)->first();
        if ($userData->user_type == '3') {
            if ($userData->id != $facility->owner_id) {
                throw new ApiGenericException("Sorry you do not have permission to access this data.");
            }
        }

        if ($userData->user_type == '4') {
            $partnerSubUser = User::where('owner_id', Auth::user()->id)->first();

            if ($partnerSubUser) {
                if ($partnerSubUser->id != $facility->owner_id) {
                    throw new ApiGenericException("Sorry you do not have permission to access this data.");
                }
            }
        }

        if ($userData->user_type == '12') {
            $partnerSubUser = User::where('owner_id', Auth::user()->id)->first();

            if ($partnerSubUser) {
                if ($partnerSubUser->id != $facility->owner_id) {
                    throw new ApiGenericException("Sorry you do not have permission to access this data.");
                }
            }
        }

        //return $facility->load('photos', 'facilityType', 'geolocations', 'facilityRate.rateDescription');
        $result       = $facility->load(
            ['photos', 'facilityType', 'geolocations', 'faciltyBrandSetting', 'facilityRate' => function ($query) {
                $query = $query->with('rateDescription')->where('active', 1)->where('rate', '>', 0);
            }]
        );

        $monthly_rates = array();
        $monthly_rate = 0;
        $i            = 0;
        $monthlyTaxPrice = 0;
        $monthlyNetPrice = 0;

        foreach ($result->facilityRate as $facilityRate) {

            //calulation net price
            if (isset($facility->tax_rate) && ($facility->tax_rate > 0)) {
                $monthlyNetPrice = (float)(($facilityRate->rate) / (1 + $facility->tax_rate));
                //calulation tax rate price
                $monthlyTaxPrice = (float)(($monthlyNetPrice) * ($facility->tax_rate));
            } else {
                $monthlyNetPrice = $facilityRate->rate;
            }

            $monthly_rates[$i]['rate_type'] = $facilityRate->rateDescription->name;
            $monthly_rates[$i]['rate'] = $facilityRate->rate;
            $monthly_rates[$i]['net_rate'] = number_format($monthlyNetPrice, 2, '.', '');
            $monthly_rates[$i]['tax_rate'] = number_format($monthlyTaxPrice, 2, '.', '');
            $monthly_rates[$i]['active'] = $facilityRate->active;
            $monthly_rates[$i]['facility_id'] = $facility->id;
            $monthly_rates[$i]['id'] = $facilityRate->id;
            $monthly_rates[$i]['rate_description'] = $facilityRate->rateDescription;

            if (((float)$monthlyNetPrice < $monthly_rate || $monthly_rate === 0) && $facilityRate->active == 1 && $monthlyNetPrice > 0) {
                $monthly_rate = round($monthlyNetPrice, 2);
            }
            $i++;
        }
        if ($result) {
            $result->monthly_rate = $monthly_rate;
            $result->facilityMonthlyTaxRates = $monthly_rates;
        }

        return $result;
    }


    public function showDetailsAdminMob(Facility $facility)
    {
        $userData = User::where('id', Auth::user()->id)->first();
        if ($userData->user_type == '3') {
            if ($userData->id != $facility->owner_id) {
                throw new ApiGenericException("Sorry you do not have permission to access this data.");
            }
        }

        if ($userData->user_type == '4') {
            $partnerSubUser = User::where('owner_id', Auth::user()->id)->first();

            if ($partnerSubUser) {
                if ($partnerSubUser->id != $facility->owner_id) {
                    throw new ApiGenericException("Sorry you do not have permission to access this data.");
                }
            }
        }

        if ($userData->user_type == '12') {
            $partnerSubUser = User::where('owner_id', Auth::user()->id)->first();

            if ($partnerSubUser) {
                if ($partnerSubUser->id != $facility->owner_id) {
                    throw new ApiGenericException("Sorry you do not have permission to access this data.");
                }
            }
        }

        //return $facility->load('photos', 'facilityType', 'geolocations', 'facilityRate.rateDescription');
        $result       = $facility->load(
            ['photos', 'facilityType', 'geolocations', 'hoursOfOperation', 'facilityOnBoardRate', 'faciltyBrandSetting', 'facilityRate' => function ($query) {
                $query = $query->with('rateDescription')->where('active', 1)->where('rate', '>', 0);
            }]
        );

        $monthly_rates = array();
        $monthly_rate = 0;
        $i            = 0;
        $monthlyTaxPrice = 0;
        $monthlyNetPrice = 0;

        foreach ($result->facilityRate as $facilityRate) {

            //calulation net price
            if (isset($facility->tax_rate) && ($facility->tax_rate > 0)) {
                $monthlyNetPrice = (float)(($facilityRate->rate) / (1 + $facility->tax_rate));
                //calulation tax rate price
                $monthlyTaxPrice = (float)(($monthlyNetPrice) * ($facility->tax_rate));
            } else {
                $monthlyNetPrice = $facilityRate->rate;
            }

            $monthly_rates[$i]['rate_type'] = $facilityRate->rateDescription->name;
            $monthly_rates[$i]['rate'] = $facilityRate->rate;
            $monthly_rates[$i]['net_rate'] = number_format($monthlyNetPrice, 2, '.', '');
            $monthly_rates[$i]['tax_rate'] = number_format($monthlyTaxPrice, 2, '.', '');
            $monthly_rates[$i]['active'] = $facilityRate->active;
            $monthly_rates[$i]['facility_id'] = $facility->id;
            $monthly_rates[$i]['id'] = $facilityRate->id;
            $monthly_rates[$i]['rate_description'] = $facilityRate->rateDescription;

            if (((float)$monthlyNetPrice < $monthly_rate || $monthly_rate === 0) && $facilityRate->active == 1 && $monthlyNetPrice > 0) {
                $monthly_rate = round($monthlyNetPrice, 2);
            }
            $i++;
        }
        if ($result) {
            $result->monthly_rate = $monthly_rate;
            $result->facilityMonthlyTaxRates = $monthly_rates;
        }

        return $result;
    }


    public function getRates(Facility $facility)
    {
        return $facility->rates;
    }


    /**
     * @param Facility $facility
     * @return mixed
     */
    public function getHoursOfOperation(Facility $facility)
    {
        return $facility->hoursOfOperation()->orderBy('day_of_week')->get();
    }

    public function getHoursOfOperationFormatted(Facility $facility)
    {
        $result            = [];
        $i                 = 0;
        $hoursOfOperations = $facility->hoursOfOperation()->orderBy('day_of_week')->get();
        foreach ($hoursOfOperations as $hoursOfOperation) {

            if (!$i) {
                $result[$i] =
                    [
                        'facility_id'          => $hoursOfOperation->facility_id,
                        'open_time'            => $hoursOfOperation->open_time,
                        'close_time'           => $hoursOfOperation->close_time,
                        'day'                  => substr($hoursOfOperation->day, 0, 3),
                        'formatted_open_time'  => $hoursOfOperation->formatted_open_time,
                        'formatted_close_time' => $hoursOfOperation->formatted_close_time
                    ];
                $i++;
            } else {
                if (($result[$i - 1]['open_time'] === $hoursOfOperation->open_time) && ($result[$i - 1]['close_time'] === $hoursOfOperation->close_time)) {
                    $result[$i - 1]['day'] =
                        substr($result[$i - 1]['day'], 0, 3) . " - " . substr($hoursOfOperation->day, 0, 3);
                } else {
                    $result[$i] =
                        [
                            'facility_id'          => $hoursOfOperation->facility_id,
                            'open_time'            => $hoursOfOperation->open_time,
                            'close_time'           => $hoursOfOperation->close_time,
                            'day'                  => substr($hoursOfOperation->day, 0, 3),
                            'formatted_open_time'  => $hoursOfOperation->formatted_open_time,
                            'formatted_close_time' => $hoursOfOperation->formatted_close_time,
                        ];
                    $i++;
                }
            }
        }
        // we can use collection to return empty array with status 200
        //return collect($result);
        return $result;
    }

    public function addHoursOfOperation(Request $request, Facility $facility)
    {
        $this->log->info("Log Request 1 :  " . json_encode($request->all()));
        $this->log->info("Log Request Facility :  " . json_encode($facility));

        $this->validate($request, HoursOfOperation::$validParams);

        //Hours of operation conflict validation
        $hoursOfOperations = HoursOfOperation::where('facility_id', $facility->id)->where('day_of_week', $request->day_of_week)->get();
        if (count($hoursOfOperations)) {
            foreach ($hoursOfOperations as $hourOfOperations) {
                if (($hourOfOperations->open_time <= $request->open_time && $hourOfOperations->close_time >= $request->open_time) || ($hourOfOperations->open_time <= $request->close_time && $hourOfOperations->close_time >= $request->close_time) || ($hourOfOperations->open_time > $request->open_time && $hourOfOperations->close_time < $request->close_time)) {
                    throw new ApiGenericException("There are already assigned garage hours for the same day that conflict with the proposed garage hours.", 422);
                }
            }
        }

        // If Hours of operations is for more than one day
        $forTwoDay = false;
        $nextDayOfWeek = ($request->day_of_week < 6) ? $request->day_of_week + 1 : 0;

        if ($request->close_time > '23:59:59') {
            $forTwoDay = true;
            $request_open_time = '00:00:00';
            $request_close_time = "0" . ($request->close_time - '24:00:00') . ":00:00";
            $request_day_of_week = ($request->day_of_week < 6) ? $request->day_of_week + 1 : 0;

            $hoursOfOperations = HoursOfOperation::where('facility_id', $facility->id)->where('day_of_week', $request_day_of_week)->get();
            if (count($hoursOfOperations)) {
                foreach ($hoursOfOperations as $hourOfOperations) {
                    if (($hourOfOperations->open_time <= $request_open_time && $hourOfOperations->close_time >= $request_open_time) || ($hourOfOperations->open_time <= $request_close_time && $hourOfOperations->close_time >= $request_close_time) || ($hourOfOperations->open_time > $request_open_time && $hourOfOperations->close_time < $request_close_time)) {
                        throw new ApiGenericException("There are already assigned garage hours for the next day that conflict with the proposed garage hours.", 422);
                    }
                }
            }
        }
        $request_day_of_week = ($request->day_of_week > 0) ? $request->day_of_week - 1 : 6;

        $hoursOfOperations = HoursOfOperation::where('facility_id', $facility->id)->where('day_of_week', $request_day_of_week)->get();

        if (count($hoursOfOperations)) {
            foreach ($hoursOfOperations as $hourOfOperations) {
                if ($hourOfOperations->close_time > '23:59:59') {
                    $hourOfOperations->open_time = '00:00:00';
                    $hourOfOperations->close_time = "0" . ($hourOfOperations->close_time - '24:00:00') . ":00:00";
                    if (($hourOfOperations->open_time <= $request->open_time && $hourOfOperations->close_time >= $request->open_time) || ($hourOfOperations->open_time <= $request->close_time && $hourOfOperations->close_time >= $request->close_time) || ($hourOfOperations->open_time > $request->open_time && $hourOfOperations->close_time < $request->close_time)) {
                        throw new ApiGenericException("There are already assigned garage hours for the same day that conflict with the proposed garage hours.", 422);
                    }
                }
            }
        }



        //check if any reservation confliction

        $reservation_validation = isset($request->reservation_validation) ? $request->reservation_validation : true;

        $reservations = Reservation::where('facility_id', $facility->id)->whereNull('cancelled_at')->whereRaw("'" . Carbon::now() . "' <= DATE_ADD(start_timestamp, INTERVAL length HOUR)")->get();
        $hoursOfOperations = HoursOfOperation::where('facility_id', $facility->id)->get();

        if (count($reservations) > 0 && $reservation_validation) {
            $operationalHoursArray = [];
            $week = [0 => 'Sunday', 1 => 'Monday', 2 => 'Tuesday', 3 => 'Wednesday', 4 => 'Thursday', 5 => 'Friday', 6 => 'Saturday', 7 => 'Sunday'];

            foreach (range(0, 6) as $zeroToSix) {
                $operationalHoursArray[$week[$zeroToSix]] = [];
            }
            if (count($hoursOfOperations)) {
                $operationalHoursArray = (new Inventory)->getOperationalHours($facility->id, $hoursOfOperations)['operationalHoursArray'];
            }

            if ($request->close_time > '23:59:59') {
                for ($i = 0; $i < explode(':', $request->close_time)[0] - 24; $i++) {
                    $operationalHoursArray[$week[$request->day_of_week + 1]][] = (string)$i;
                }
                for ($i = (int)date('G', strtotime($request->open_time)); $i <= (int)date('G', strtotime('23:59:59')); $i++) {
                    $operationalHoursArray[$week[$request->day_of_week]][] = (string)$i;
                }
            } else {
                for ($i = (int)date('G', strtotime($request->open_time)); $i <= (int)date('G', strtotime($request->close_time)); $i++) {
                    $operationalHoursArray[$week[$request->day_of_week]][] = (string)$i;
                }
            }

            foreach ($reservations as $reservation) {
                $date_time_in = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
                $reservation_length = $reservation->length;
                $length = round($reservation->length, 0);
                $reservation_minutes = 0;
                $reservation_hours = explode(".", $reservation->length);
                if (isset($reservation_hours[1]) && ($reservation_hours[1]) > 0) {
                    $reservation_minutes = 30;
                }
                $reservation_date_time_out =  date('Y-m-d H:i:s', strtotime(Carbon::parse($date_time_in)->addHours($reservation_length)->addMinutes($reservation_minutes)));
                if (Carbon::parse($reservation_date_time_out)->gte(Carbon::now())) {
                    $dayIn = date('w', strtotime(Carbon::parse($date_time_in)->format('Y-m-d')));
                    $dayOut = date('w', strtotime(Carbon::parse($reservation_date_time_out)->format('Y-m-d')));
                    $hourIn = date('G', strtotime(Carbon::parse($date_time_in)->format('H:i:s')));
                    $hourOut = date('G', strtotime(Carbon::parse($reservation_date_time_out)->format('H:i:s')));

                    if (!in_array($hourIn, $operationalHoursArray[$week[$dayIn]]) || !in_array($hourOut, $operationalHoursArray[$week[$dayOut]])) {
                        //throw new ApiGenericException("There are future reservations that conflict with the proposed garage hours.", 422);
                        return ['warning' =>  true, 'message' => "There are future reservations that conflict with the proposed garage hours.", 'hours' => $facility->hoursOfOperation()->orderBy('day_of_week')->get()];
                    }
                }
            }
        }


        $deleteInventory = false;

        $hoursOfOperationsChk = HoursOfOperation::where('facility_id', $facility->id)->orderBy('day_of_week', 'ASC')->get();
        if (count($hoursOfOperationsChk) <= 0) {
            $deleteInventory = true;
            // FacilityInventorySpecificDate::where('facility_id',$facility->id)->delete();
        }

        // update hours of operation
        $hours = new HoursOfOperation();
        $hours->fill($request->all());
        $facility->hoursOfOperation()->save($hours);

        //to create or update inventory and avialability from current week
        Carbon::setWeekStartsAt(Carbon::SUNDAY);
        $dates = new \DatePeriod(
            new \DateTime(Carbon::now()->startOfWeek()),
            new \DateInterval('P1D'),
            new \DateTime(Carbon::now()->startOfWeek()->addWeeks(14))
        );

        //to get special dates    

        $inventory = new Inventory;
        $SpectialDates = $inventory->getSpectialDates($facility->id);


        Facility::where('id', $facility->id)->with(['availabilities' => function ($query) {
            return $query->where('date', '>=', Carbon::now()->startOfWeek()->subDays(7)->format('Y-m-d'));
        }, 'inventories' => function ($query) {
            return $query->where('date', '>=', Carbon::now()->startOfWeek()->subDays(7)->format('Y-m-d'));
        }])->chunk(
            1,
            function ($facilities) use ($dates, $request, $forTwoDay, $nextDayOfWeek, $SpectialDates) {
                foreach ($facilities as $facility) {

                    $facilityInventories        = [];
                    $facilityAvailabilities     = [];
                    foreach ($facility->inventories as $facility_inventory) {
                        $facilityInventories[date('Y-m-d', strtotime($facility_inventory->date))] =
                            $facility_inventory->availability;
                    }
                    foreach ($facility->availabilities as $facility_availability) {
                        $facilityAvailabilities[date('Y-m-d', strtotime($facility_availability->date))] =
                            $facility_availability->availability;
                    }

                    Carbon::setWeekStartsAt(Carbon::SUNDAY);
                    $current_week = new \DatePeriod(
                        new \DateTime(Carbon::now()->startOfWeek()),
                        new \DateInterval('P1D'),
                        new \DateTime(Carbon::now()->startOfWeek()->addWeeks(1))
                    );
                    $flatValue = true;
                    $tempValue = 0;
                    $sameDayInventory = 0;
                    $sameDayFlag = true;
                    $sameDayhours = HoursOfOperation::where('facility_id', $facility->id)->where('day_of_week', $request->day_of_week)->get();
                    foreach ($current_week as $k => $current_week_date) {


                        $current_inventory = [];
                        $current_availability = [];
                        $lastWeekToday = Carbon::createFromDate($current_week_date->format('Y'), $current_week_date->format('m'), $current_week_date->format('d'))->subDays(7)->format('Y-m-d');

                        if (isset($facilityInventories[$current_week_date->format('Y-m-d')])) {
                            $startDate = $current_week_date->format('Y-m-d');

                            while (in_array($startDate, $SpectialDates)) {

                                $startDate = Carbon::parse($startDate)->addDays(7)->format('Y-m-d');
                            }
                            $current_inventory = json_decode($facilityInventories[$startDate], true);

                            $current_availability = json_decode($facilityAvailabilities[$startDate], true);
                        }



                        foreach ($current_inventory as $value) {

                            if ($tempValue === 0) {
                                $tempValue = $value;
                            } else if ($tempValue == $value) {
                                // flat value consistent
                            } else {
                                $flatValue = false;
                            }

                            // same day inventory check
                            if (date('w', strtotime(Carbon::parse($current_week_date->format('Y-m-d')))) == $request->day_of_week && count($sameDayhours) > 1) {

                                if ($sameDayInventory === 0) {
                                    $sameDayInventory = $value;
                                } else if ($sameDayInventory == $value) {
                                    // flat value consistent
                                } else {
                                    $sameDayFlag = false;
                                }
                            }
                        }
                        if (isset($facilityInventories[$current_week_date->format('Y-m-d')])) {

                            $startDate = $current_week_date->format('Y-m-d');

                            while (in_array($startDate, $SpectialDates)) {

                                $startDate = Carbon::parse($startDate)->addDays(7)->format('Y-m-d');
                            }

                            $facilityInventories[$lastWeekToday] = $facilityInventories[$startDate];
                            $facilityAvailabilities[$lastWeekToday] = $facilityAvailabilities[$startDate];
                        }
                    }

                    if ($tempValue === 0) {
                        $tempValue = self::NUMBER_OF_SPOTS;
                    }
                    if ($sameDayFlag && $sameDayInventory !== 0) {
                        $tempValue = $sameDayInventory;
                        $flatValue = true;
                    }

                    //fetch operational hours of this facility
                    $hours = HoursOfOperation::where('facility_id', $facility->id)->orderBy('day_of_week', 'ASC')->get();
                    $oneDayValidation = 0;
                    $tempDayOfWeek = 7;
                    foreach ($hours as $oneHour) {
                        if ($tempDayOfWeek == 7) {
                            $tempDayOfWeek = $oneHour->day_of_week;
                            $oneDayValidation++;
                        }
                        if ($tempDayOfWeek != $oneHour->day_of_week) {
                            $oneDayValidation++;
                        }
                    }
                    $inventory = new Inventory;
                    $getOperationalHours = $inventory->getOperationalHours($facility->id, $hours);
                    $operationalHours    = $getOperationalHours['operationalHours'];
                    $remainder           = $getOperationalHours['remainder'];
                    $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];

                    //iterate over dates for the next 90 days starting from the most recent last sunday
                    foreach ($dates as $key => $date) {
                        $inventories    = [];
                        $availabilities = [];
                        $partneravailabilities = [];
                        $today = $date->format('l');

                        //date of last week
                        $lastWeekToday = Carbon::createFromDate($date->format('Y'), $date->format('m'), $date->format('d'))->subDays(7)->format('Y-m-d');

                        $day_of_week = date('w', strtotime($date->format('Y-m-d')));
                        $operationalHoursOpenTime = $operationalHours[$today]['open_time'];


                        //multiple HOO
                        $specialDayCheck = 0;
                        $specialDayCheckAvail = 0;
                        $specialDayCheckValue = 0;
                        $specialDayCheckAvailValue = 0;

                        if (count($facilityInventories) && isset($facilityInventories[$lastWeekToday])) {
                            $inventoriesTemp = json_decode($facilityInventories[$lastWeekToday], true);

                            $inventoriesTempSpecialDay = [];
                            if (in_array($date->format('Y-m-d'), $SpectialDates)) {
                                if (isset($facilityInventories[$date->format('Y-m-d')])) {

                                    $inventoriesTempSpecialDay = json_decode($facilityInventories[$date->format('Y-m-d')], true);
                                }
                            }


                            foreach ($operationalHoursArray[$today] as $operationalHour) {
                                if (in_array($date->format('Y-m-d'), $SpectialDates)) {

                                    if ($specialDayCheck == 0) {
                                        foreach ($inventoriesTempSpecialDay as $key => $inventoryTmpSpecialVal) {
                                            if (isset($inventoriesTempSpecialDay[$key]) && ($inventoriesTempSpecialDay[$key] > 0)) {
                                                $specialDayCheckValue = $inventoriesTempSpecialDay[$key];
                                                $specialDayCheck++;
                                                break;
                                            }
                                        }
                                    }
                                    $inventories[$operationalHour] = $specialDayCheckValue;
                                } else {

                                    if (($day_of_week == $request->day_of_week && $flatValue) || ($forTwoDay && $nextDayOfWeek == $day_of_week && $flatValue)) {

                                        $inventories[$operationalHour] = $tempValue;
                                    } else {
                                        $inventories[$operationalHour] =
                                            isset($inventoriesTemp[$operationalHour]) ? $inventoriesTemp[$operationalHour] : self::NUMBER_OF_SPOTS;
                                    }
                                }
                            }
                        } else {
                            //multiple HOO
                            foreach ($operationalHoursArray[$today] as $operationalHour) {

                                if (in_array($date->format('Y-m-d'), $SpectialDates)) {

                                    if ($specialDayCheck == 0) {
                                        foreach ($inventoriesTempSpecialDay as $key => $inventoryTmpSpecialVal) {
                                            if (isset($inventoriesTempSpecialDay[$key]) && ($inventoriesTempSpecialDay[$key] > 0)) {
                                                $specialDayCheckValue = $inventoriesTempSpecialDay[$key];
                                                $specialDayCheck++;
                                                break;
                                            }
                                        }
                                    }
                                    $inventories[$operationalHour] = $specialDayCheckValue;
                                    //  $inventories[$operationalHour] = $inventoriesTempSpecialDay[$operationalHour];

                                } else {
                                    if (($day_of_week == $request->day_of_week && $flatValue) || ($forTwoDay && $nextDayOfWeek == $day_of_week && $flatValue)) {
                                        $inventories[$operationalHour] = $tempValue;
                                    } else {
                                        $inventories[$operationalHour] = self::NUMBER_OF_SPOTS;
                                    }
                                }
                            }
                        }

                        /**
                         * SIMILAR ARRAY AS ABOVE FOR AVAILABILITIES
                         */


                        if (count($facilityInventories) && isset($facilityInventories[$lastWeekToday])) {
                            $curr_inventory = [];
                            $curr_availability = [];
                            if (isset($facilityInventories[$date->format('Y-m-d')]) && isset($facilityAvailabilities[$date->format('Y-m-d')])) {
                                $curr_inventory = json_decode($facilityInventories[$date->format('Y-m-d')], true);
                                $curr_availability = json_decode($facilityAvailabilities[$date->format('Y-m-d')], true);
                            }

                            $availabilitiesTemp    = json_decode($facilityInventories[$lastWeekToday], true);

                            $inventoriesTempSpecialDay = [];
                            if (in_array($date->format('Y-m-d'), $SpectialDates)) {
                                if (isset($facilityInventories[$date->format('Y-m-d')])) {

                                    $inventoriesTempSpecialDay = json_decode($facilityInventories[$date->format('Y-m-d')], true);
                                }
                            }
                            /**                        if(isset($facilityInventories[$lastWeekToday])){
                            $inventoriesTempSpecialDay = json_decode($facilityInventories[$date->format('Y-m-d')], true);
                        }
                             **/
                            foreach ($operationalHoursArray[$today] as $operationalHour) {
                                $spots = 0;
                                if (isset($curr_inventory[$operationalHour]) && isset($curr_availability[$operationalHour])) {
                                    $spots = $curr_inventory[$operationalHour] - $curr_availability[$operationalHour];
                                }

                                if (in_array($date->format('Y-m-d'), $SpectialDates) && isset($inventoriesTempSpecialDay[$operationalHour])) {

                                    if ($specialDayCheckAvail == 0) {
                                        foreach ($inventoriesTempSpecialDay as $key => $inventoryTmpSpecialVal) {
                                            if (isset($inventoriesTempSpecialDay[$key]) && ($inventoriesTempSpecialDay[$key] > 0)) {
                                                $specialDayCheckAvailValue = $inventoriesTempSpecialDay[$key];
                                                $specialDayCheckAvail++;
                                                break;
                                            }
                                        }
                                    }

                                    $availabilities[$operationalHour] = $specialDayCheckAvailValue - $spots;
                                    $partneravailabilities[$operationalHour] = $specialDayCheckAvailValue - $spots;
                                    //$availabilities[$operationalHour] = $inventoriesTempSpecialDay[$operationalHour]-$spots;
                                    //$partneravailabilities[$operationalHour] = $inventoriesTempSpecialDay[$operationalHour] - $spots;

                                } else {


                                    if (($day_of_week == $request->day_of_week && $flatValue) || ($forTwoDay && $nextDayOfWeek == $day_of_week && $flatValue)) {
                                        $availabilities[$operationalHour] = $tempValue - $spots;
                                        $partneravailabilities[$operationalHour] = $tempValue - $spots;
                                    } else {
                                        $availabilities[$operationalHour] =
                                            (isset($availabilitiesTemp[$operationalHour]) ? $availabilitiesTemp[$operationalHour] : self::NUMBER_OF_SPOTS) - $spots;
                                        $partneravailabilities[$operationalHour] =     (isset($availabilitiesTemp[$operationalHour]) ? $availabilitiesTemp[$operationalHour] : self::NUMBER_OF_SPOTS) - $spots;
                                    }
                                }
                            }
                        } else {
                            //else create a static availability
                            foreach ($operationalHoursArray[$today] as $operationalHour) {
                                if (in_array($date->format('Y-m-d'), $SpectialDates)) {

                                    if ($specialDayCheckAvail == 0) {
                                        foreach ($inventoriesTempSpecialDay as $key => $inventoryTmpSpecialVal) {
                                            if (isset($inventoriesTempSpecialDay[$key]) && ($inventoriesTempSpecialDay[$key] > 0)) {
                                                $specialDayCheckAvailValue = $inventoriesTempSpecialDay[$key];
                                                $specialDayCheckAvail++;
                                                break;
                                            }
                                        }
                                    }


                                    $availabilities[$operationalHour] = $specialDayCheckAvailValue;
                                    $partneravailabilities[$operationalHour] = $specialDayCheckAvailValue;

                                    //    $availabilities[$operationalHour] = $inventoriesTempSpecialDay[$operationalHour];
                                    //  $partneravailabilities[$operationalHour] = $inventoriesTempSpecialDay[$operationalHour];


                                } else {


                                    if (($day_of_week == $request->day_of_week && $flatValue) || ($forTwoDay && $nextDayOfWeek == $day_of_week && $flatValue)) {
                                        $availabilities[$operationalHour] = $tempValue;
                                        $partneravailabilities[$operationalHour] = $tempValue;
                                    } else {
                                        $availabilities[$operationalHour] = self::NUMBER_OF_SPOTS;
                                        $partneravailabilities[$operationalHour] = self::NUMBER_OF_SPOTS;
                                    }
                                }
                            }
                        }

                        if (($oneDayValidation == 1 || $day_of_week == $request->day_of_week || ($forTwoDay && $nextDayOfWeek == $day_of_week))) {

                            $newInventory = FacilityInventory::firstOrNew(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
                            $newInventory->facility_id  = $facility->id;
                            $newInventory->availability = json_encode($inventories, JSON_FORCE_OBJECT);
                            $newInventory->date         = $date->format('Y-m-d');
                            $newInventory->save();

                            $newAvailability = FacilityAvailability::firstOrNew(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
                            $newAvailability->facility_id  = $facility->id;
                            $newAvailability->availability = json_encode($availabilities, JSON_FORCE_OBJECT);
                            $newAvailability->date         = $date->format('Y-m-d');
                            $newAvailability->save();

                            //update partners data too
                            $partnerData = FacilityPartnerAvailabilityCron::firstOrNew(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);

                            //update avaliablity when inventory_threshold has value by vikrant
                            $partnerData->availability = json_encode($availabilities, JSON_FORCE_OBJECT);
                            $partnerData->save();
                        }
                        if (in_array($date->format('Y-m-d'), $SpectialDates)) {


                            $special_facilities = Facility::where('id', $facility->id)->with(['availabilities' => function ($query) use ($date) {
                                return $query->where('date', $date->format('Y-m-d'));
                            }, 'inventories' => function ($query) use ($date) {
                                return $query->where('date', $date->format('Y-m-d'));
                            }])->get();
                            $specilal_inventory_to_update = [];
                            foreach ($special_facilities as $special_facility) {
                                foreach ($special_facility->inventories as $special_inventory) {
                                    $specilal_inventory_to_update[date('Y-m-d', strtotime($special_inventory->date))] =
                                        $special_inventory->availability;
                                }
                            }
                            $current_inventory = json_decode($specilal_inventory_to_update[$date->format('Y-m-d')], true);
                            $final_inventory = [];
                            $final_special_value = 50;
                            foreach ($current_inventory as $value) {
                                $final_special_value = $value;
                            }
                            $today = $date->format('l');
                            foreach ($operationalHoursArray[$today] as $operationalHour) {
                                $final_inventory[$operationalHour] =
                                    isset($current_inventory[$operationalHour]) ? $current_inventory[$operationalHour] : $final_special_value;
                            }
                            $newInventory = FacilityInventory::firstOrNew(
                                ['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]
                            );
                            if ($newInventory->exists) {
                                $newInventory->availability = json_encode($final_inventory, JSON_FORCE_OBJECT);
                                $newInventory->save();
                            }

                            $newAvailability = FacilityAvailability::firstOrNew(
                                ['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]
                            );
                            if ($newAvailability->exists) {

                                $newAvailability->availability = json_encode($final_inventory, JSON_FORCE_OBJECT);
                                $newAvailability->save();
                            }

                            //update partners data too
                            $partnerData = FacilityPartnerAvailabilityCron::firstOrNew(
                                ['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]
                            );
                            if ($partnerData->exists) {

                                //update avaliablity when inventory_threshold has value by vikrant
                                if (!$final_inventory) {
                                    $partnerData->availability = json_encode($final_inventory, JSON_FORCE_OBJECT);
                                    $partnerData->save();
                                } else {
                                    $newPartnerAvailability = Facility::calculatePartnerAvailability($inventories, $final_inventory, $facility->id);
                                    if ($newPartnerAvailability) {
                                        $partnerData->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                                        $partnerData->save();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        );
        if ($deleteInventory) {

            foreach ($SpectialDates as $SpectialDate) {
                if ($request->day_of_week != Carbon::parse($SpectialDate)->format('w')) {
                    FacilityInventorySpecificDate::where('facility_id', $facility->id)->where('date', $SpectialDate)->delete();
                }
            }
        }
        return $facility->hoursOfOperation()->orderBy('day_of_week')->get();
    }

    /**
     * Send a text message with facility information to the given phone number
     *
     * @param  Facility $facility [description]
     * @return [type]             [description]
     */
    public function sendFacilityTextMessage(Request $request, $id)
    {
        $this->validate(
            $request,
            ['to' => 'required']
        );

        if (!TextMessaging::validatePhoneNumber($request->to)) {
            throw new ApiGenericException('Please submit a valid phone number.', 422);
        }

        // Check we are not over rate limit before throwing into queue
        $rateLimiter = new MessageRateLimiter();
        if ($rateLimiter->isOverRateLimit($request->to, $request->ip())) {
            throw new ApiGenericException(
                'You have sent too many text messages to this number, please wait before sending.'
            );
        }

        // Queue up our email
        Artisan::queue('icon:facility-text', ['facility' => $id, 'to' => $request->to, 'ip' => $request->ip()]);

        return ['queued' => true];
    }

    public function sendUrlTextMessage(Request $request)
    {
        $this->validate(
            $request,
            ['to' => 'required']
        );

        if (!TextMessaging::validatePhoneNumber($request->to)) {
            throw new ApiGenericException('Please submit a valid phone number.', 422);
        }


        // Queue up our email
        Artisan::queue(
            'icon:mobileapplink-text',
            ['mobileapplink' => $request->mobileapplink, 'to' => $request->to, 'ip' => $request->ip()]
        );

        return ['queued' => true];
    }

    public function getFacilityBySlug($short_name)
    {
        return Facility::where('short_name', 'like', '%' . $short_name . '%')->with(
            'photos',
            'facilityType',
            'geolocations',
            'facilityOnBoardRate',
            'faciltyBrandSetting'
        )->first();
    }

    public function store(Request $request)
    {
        $this->log->info("Add Facility Log Request :  " . json_encode($request->all()));
        $this->log->info("Update Facility Request By:  " . Auth::user()->id);
        $this->validate($request, Facility::$validParams, Facility::$validMessages);

        if (Auth::user()->user_type == '1' || Auth::user()->user_type == '2') {
            $partner = User::with('membershipPlans')->where('id', $request->partner_id)->first();
            $totalPlanCount = 0;
            if ($partner->membershipPlans) {
                foreach ($partner->membershipPlans as $key => $value) {
                    $totalPlanCount += $value->no_of_facility;
                }
            }
            $facilityCount = Facility::where('owner_id', $partner->id)->count();
            if ($facilityCount >= $totalPlanCount) {
                throw new ApiGenericException('Partner has exceed no of garage creation.');
            }
        }

        //check no of facility create by partner
        if (Auth::user()->user_type == '3') {
            $totalPlanCount = 0;
            if (Auth::user()->membershipPlans) {
                foreach (Auth::user()->membershipPlans as $key => $value) {
                    $totalPlanCount += $value->no_of_facility;
                }
            }
            $facilityCount = Facility::where('owner_id', Auth::user()->id)->count();
            if ($facilityCount >= $totalPlanCount) {
                throw new ApiGenericException('You have exceed your no of garage creation. Please contact to admin.');
            }
        }

        $facility    = Facility::create($request->all());
        if ($facility) {
            FacilityConfiguration::create([
                "facility_id" => $facility->id,
                "partner_id" => $request->partner_id
            ]);
        }
        $owner_id = Auth::user()->id;
        $created_by = Auth::user()->id;
        if (Auth::user()->user_type == '1' || Auth::user()->user_type == '2') {
            $owner_id = $request->partner_id;
        }
        if (Auth::user()->user_type == '4') {
            $owner_id = Auth::user()->created_by;
        }
        if (Auth::user()->user_type == '12') {
            $owner_id = Auth::user()->created_by;
        }
        $facility->owner_id = $owner_id;
        $facility->created_by = $created_by;
        $geolocation = new GeoLocation();
        $facility->geolocations()->save($geolocation);
        $facility->save();

        if (((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) && ($facility)) {

            $data['facility_id'] = $facility->id;
            $data['user_id'] = Auth::user()->id;
            UserFacility::create($data);
        }

        // Store facility details in MasterFacilityShortCode table
        $masterShortCode = new MasterFacilityShortCode();
        $masterShortCode->short_code = $request->garage_code;
        $masterShortCode->type = 0;
        $masterShortCode->status = $facility->active ?? 1;
        $masterShortCode->save();

        // Map short code to facility in ShortCodeFacilityMapping table
        $shortCodeMapping = new ShortCodeFacilityMapping();
        $shortCodeMapping->short_code_id = $masterShortCode->id; // use correct ID here
        $shortCodeMapping->facility_id = $facility->id;
        $shortCodeMapping->partner_id = $request->partner_id;
        $shortCodeMapping->save();

        //$this->destroysOldGeolocation($facility);
        Artisan::call('generate:inventory-for-next-months-ficility-wise', ['facility' => $facility->id]);

        return $facility->load('facilityfee', 'facilityType', 'geolocations', 'photos', 'features', 'hoursOfOperation', 'facilityConfiguration');
    }

    // public function destroy(Facility $facility)
    // {
    //     $this->log->info("Delete Facility Log Request :  " . json_encode($facility));
    //     $this->log->info("Delete Facility Request By:  " . Auth::user()->id);

    //     foreach ($facility->features as $feature) {
    //         $facility->features()->detach($feature->id);
    //     }

    //     $this->destroysOldGeolocation($facility);
    //     return $facility->delete() ? $facility : null;
    // }

    public function destroy(Facility $facility)
    {
        $this->log->info("Delete Facility Log Request: " . json_encode($facility));
        $this->log->info("Delete Facility Request By: " . Auth::user()->id);

        // Detach all features
        foreach ($facility->features as $feature) {
            $facility->features()->detach($feature->id);
        }

        // Custom cleanup
        $this->destroysOldGeolocation($facility);

        // Delete facility and related mappings in a DB transaction
        DB::transaction(function () use ($facility) {
            // Get and delete related shortcode mappings
            $facilityMappings = ShortCodeFacilityMapping::where('facility_id', $facility->id)
                ->with('masterFacilityShortCode')
                ->get();

            foreach ($facilityMappings as $mapping) {
                $mapping->delete(); // soft delete mapping

                // Soft delete related MasterFacilityShortCode
                if ($mapping->masterFacilityShortCode) {
                    $mapping->masterFacilityShortCode->delete();
                }
            }

            // Soft delete the facility itself
            $facility->delete();
        });

        return $facility;
    }

    public function update(Request $request, Facility $facility)
    {
        $this->log->info("Update Facility Log Request :  " . json_encode($request->all()));
        $this->log->info("Update Facility Request By:  " . Auth::user()->id);
        $this->log->info("Log Request Facility :  " . json_encode($facility));

        //check for duplicate before update
        $duplicateCount = Facility::where('garage_code', $request->garage_code)
            ->where('id', '!=', $facility->id)
            ->whereNull('deleted_at')
            ->count();
        if ($duplicateCount > 0) {
            throw new ApiGenericException('Duplicate garage code found in active facilities.');
        }

        //$this->validate($request, Facility::$validParams);
        // associate the new facility type with Facility.
        if (Auth::user()->user_type == '3') {
            //   $request->request->remove('active');
        }
        $this->associateFacilityType($facility, $request);

        // Gets and sets the relationship with the upto date features
        $this->setFeatures($facility, $request);
        if ($request->geolocations) {
            $geolocationData = $request->geolocations;
            // Check if the request contains bounding_box data
            if (isset($geolocationData['points'])) {
                $trimmedBoundingBox = substr($geolocationData['points'], 1, -1);
                $boundingBoxCoordinates = json_decode($trimmedBoundingBox, true);
                // Create Point objects for each coordinate pair in the bounding box
                $points = [];
                foreach ($boundingBoxCoordinates as $coordinate) {
                    $points[] = new Point($coordinate[1], $coordinate[0]); // (latitude, longitude)
                }
                // Create a LineString from the points and create a Polygon
                $boundingBoxPolygon = new Polygon([new LineString($points)]);
                // Save the bounding box polygon to the geolocation's bounding_box column
                DB::table('geolocations')
                    ->where('locatable_id', $facility->id)
                    ->update([
                        'bounding_box' => DB::raw("ST_GeomFromText('" . $boundingBoxPolygon->toWKT() . "')"),
                        'points' => $geolocationData['points'],
                        'address_1' => $geolocationData['address_1'],
                        'address_2' => $geolocationData['address_2'],
                        'city' => $geolocationData['city'],
                        'district' => $geolocationData['district'],
                        'latitude' => $geolocationData['latitude'],
                        'longitude' => $geolocationData['longitude'],
                        'state' => $geolocationData['state'],
                        'zip_code' => $geolocationData['zip_code'],
                        'updated_at' => Carbon::now(),
                    ]);
            } else {
                // Update other geolocation fields if bounding_box is not present
                $facility->geolocations->fill($geolocationData)->save();
            }
        }

        if ($request->facility_configuration) {
            $existConfig = FacilityConfiguration::where('facility_id', $facility->id)->first();

            if ($existConfig) {
                /**
                 * PIMS-13373 
                 * DD
                 * ANTIPASSBACK FOR BULK if setting changed then update permit only
                 */

                /* if (isset($request->facility_configuration['is_antipass_enabled']) && $request->facility_configuration['is_antipass_enabled'] != $existConfig->is_antipass_enabled) {
                    PermitRequest::where(['facility_id' => $facility->id])
                        ->update(['is_antipass_enabled' => $request->facility_configuration['is_antipass_enabled']]);
                } */
                //UPBL-87
                // if((isset($request->facility_configuration['threshold_type']) && ($request->facility_configuration['threshold_type'] == 0 || $request->facility_configuration['threshold_type'] == '0'))
                //  && (isset($request->facility_configuration['threshold_onlevel']) && ($request->facility_configuration['threshold_onlevel'] > 0 || $request->facility_configuration['threshold_onlevel'] > '0')) 
                // && (isset($request->facility_configuration['threshold_offlevel']) && ($request->facility_configuration['threshold_offlevel'] > 0 || $request->facility_configuration['threshold_offlevel'] > '0'))
                // ) {

                //     $slotStatus = QueryBuilder::getSlotStatus($request->facility_configuration['facility_id'],$request->capacity,$request->facility_configuration['threshold_onlevel'],$request->facility_configuration['threshold_offlevel']);
                //     $request->merge([
                //         'facility_configuration' => array_merge(
                //             $request->facility_configuration,
                //             ['is_lot_full' => $slotStatus]
                //         )
                //     ]);
                // }

                FacilityConfiguration::where('facility_id', $request->facility_configuration['facility_id'])
                    ->where('partner_id', $request->facility_configuration['partner_id'])
                    ->update([
                        'screen_saver_time' => $request->facility_configuration['screen_saver_time'],
                        'is_web_scanner_disabled' => $request->facility_configuration['is_web_scanner_disabled'],
                        'is_prorate_apply' => isset($request->facility_configuration['is_prorate_apply']) ? $request->facility_configuration['is_prorate_apply'] : 0,
                        'zero_amount_event_booking_enable' => isset($request->facility_configuration['zero_amount_event_booking_enable']) ? $request->facility_configuration['zero_amount_event_booking_enable'] : 0,
                        'is_manual_permit_amount_enabled' => isset($request->facility_configuration['is_manual_permit_amount_enabled']) ? $request->facility_configuration['is_manual_permit_amount_enabled'] : 0,
                        'hid_card_enabled' => isset($request->facility_configuration['hid_card_enabled']) ? $request->facility_configuration['hid_card_enabled'] : 0,
                        'terms_condition_content' => isset($request->facility_configuration['terms_condition_content']) ? $request->facility_configuration['terms_condition_content'] : '',
                        'account_name_enabled' => isset($request->facility_configuration['account_name_enabled']) ? $request->facility_configuration['account_name_enabled'] : '',
                        'is_want_configure_services_enabled' => isset($request->facility_configuration['is_want_configure_services_enabled']) ? $request->facility_configuration['is_want_configure_services_enabled'] : 0,
                        'is_negotiated_price' => isset($request->facility_configuration['is_negotiated_price']) ? $request->facility_configuration['is_negotiated_price'] : $existConfig->is_negotiated_price,
                        'is_auto_print' => isset($request->facility_configuration['is_auto_print']) ? $request->facility_configuration['is_auto_print'] : '',
                        'is_booking_cancel_enable' => isset($request->facility_configuration['is_booking_cancel_enable']) ? $request->facility_configuration['is_booking_cancel_enable'] : '0',
                        'is_permit_payment_enable' => isset($request->facility_configuration['is_permit_payment_enable']) ? $request->facility_configuration['is_permit_payment_enable'] : '0',
                        'is_display_validate_button' => isset($request->facility_configuration['is_display_validate_button']) ? $request->facility_configuration['is_display_validate_button'] : '0',
                        'is_oversize_enabled' => isset($request->facility_configuration['is_oversize_enabled']) ? $request->facility_configuration['is_oversize_enabled'] : '0',
                        'is_oversize_allowed_extend' => isset($request->facility_configuration['is_oversize_allowed_extend']) ? $request->facility_configuration['is_oversize_allowed_extend'] : '0',
                        'cancel_type' => isset($request->facility_configuration['cancel_type']) ? $request->facility_configuration['cancel_type'] : '0',
                        'is_antipass_enabled' => isset($request->facility_configuration['is_antipass_enabled']) ? $request->facility_configuration['is_antipass_enabled'] : '0',

                        'allow_permit_when_facility_non_operational' => $request->facility_configuration['allow_permit_when_facility_non_operational'],

                        'is_rate_duration_allowed' => isset($request->facility_configuration['is_rate_duration_allowed']) ? $request->facility_configuration['is_rate_duration_allowed'] : '0',
                        //UPBL-87
                        // 'threshold_type' => isset($request->facility_configuration['threshold_type']) ? $request->facility_configuration['threshold_type'] : '0',

                        // 'threshold_onlevel' => isset($request->facility_configuration['threshold_onlevel']) ? $request->facility_configuration['threshold_onlevel'] : '0',

                        // 'threshold_offlevel' => isset($request->facility_configuration['threshold_offlevel']) ? $request->facility_configuration['threshold_offlevel'] : '0',

                        // 'is_lot_full' => isset($request->facility_configuration['is_lot_full']) ? $request->facility_configuration['is_lot_full'] : '0',

                        //PIMS-14211 || Dev:Sagar || Date:23/07/2025
                        'is_true_start' => isset($request->facility_configuration['is_true_start']) ? $request->facility_configuration['is_true_start'] : '0',
                        'oversize_fee_times' => isset($request->facility_configuration['oversize_fee_times']) ? $request->facility_configuration['oversize_fee_times'] : NULL,
                        //PIMS-12376 || Dev: Sagar || 04/08/2025
                        'is_pemit_qrcode' => isset($request->facility_configuration['is_pemit_qrcode']) ? $request->facility_configuration['is_pemit_qrcode'] : '0',
                        'oversize_fee_amount_type' => isset($request->facility_configuration['oversize_fee_amount_type']) ? $request->facility_configuration['oversize_fee_amount_type'] : '0',
                        //PMIS-14836 
                        'permit_email' => isset($request->facility_configuration['permit_email']) ? $request->facility_configuration['permit_email'] : '1',
                        'cust_email_admin_email' => isset($request->facility_configuration['cust_email_admin_email']) ? $request->facility_configuration['cust_email_admin_email'] : '2',
                        //PMIS-14836 

                    ]);
            } else {
                FacilityConfiguration::Insert([
                    'facility_id' => $request->facility_configuration['facility_id'],
                    'partner_id' => $request->facility_configuration['partner_id'],
                    'screen_saver_time' => $request->facility_configuration['screen_saver_time'],
                    'is_web_scanner_disabled' => $request->facility_configuration['is_web_scanner_disabled'],
                    'is_prorate_apply' => isset($request->facility_configuration['is_prorate_apply']) ? $request->facility_configuration['is_prorate_apply'] : 0,
                    'zero_amount_event_booking_enable' => isset($request->facility_configuration['zero_amount_event_booking_enable']) ? $request->facility_configuration['zero_amount_event_booking_enable'] : 0,
                    'is_manual_permit_amount_enabled' => isset($request->facility_configuration['is_manual_permit_amount_enabled']) ? $request->facility_configuration['is_manual_permit_amount_enabled'] : 0,
                    'hid_card_enabled' => isset($request->facility_configuration['hid_card_enabled']) ? $request->facility_configuration['hid_card_enabled'] : 0,
                    'terms_condition_content' => isset($request->facility_configuration['terms_condition_content']) ? $request->facility_configuration['terms_condition_content'] : '',
                    'account_name_enabled' => isset($request->facility_configuration['account_name_enabled']) ? $request->facility_configuration['account_name_enabled'] : '',
                    'is_want_configure_services_enabled' => isset($request->facility_configuration['is_want_configure_services_enabled']) ? $request->facility_configuration['is_want_configure_services_enabled'] : 0, #dushyant 29/05/2024 USM
                    'is_negotiated_price' => isset($request->facility_configuration['is_negotiated_price']) ? $request->facility_configuration['is_negotiated_price'] : '',
                    'is_auto_print' => isset($request->facility_configuration['is_auto_print']) ? $request->facility_configuration['is_auto_print'] : '',
                    'is_booking_cancel_enable' => isset($request->facility_configuration['is_booking_cancel_enable']) ? $request->facility_configuration['is_booking_cancel_enable'] : '0',
                    'is_permit_payment_enable' => isset($request->facility_configuration['is_permit_payment_enable']) ? $request->facility_configuration['is_permit_payment_enable'] : '0',
                    'is_display_validate_button' => isset($request->facility_configuration['is_display_validate_button']) ? $request->facility_configuration['is_display_validate_button'] : '0',
                    'is_oversize_enabled' => isset($request->facility_configuration['is_oversize_enabled']) ? $request->facility_configuration['is_oversize_enabled'] : '0',
                    'is_oversize_allowed_extend' => isset($request->facility_configuration['is_oversize_allowed_extend']) ? $request->facility_configuration['is_oversize_allowed_extend'] : '0',
                    'cancel_type' => isset($request->facility_configuration['cancel_type']) ? $request->facility_configuration['cancel_type'] : '0',
                    'is_antipass_enabled' => isset($request->facility_configuration['is_antipass_enabled']) ? $request->facility_configuration['is_antipass_enabled'] : '0',
                    'allow_permit_when_facility_non_operational' => $request->facility_configuration['allow_permit_when_facility_non_operational'],
                    //UPBL-87
                    // 'threshold_type' => isset($request->facility_configuration['threshold_type']) ? $request->facility_configuration['threshold_type'] : '0',

                    // 'threshold_onlevel' => isset($request->facility_configuration['threshold_onlevel']) ? $request->facility_configuration['threshold_onlevel'] : '0',

                    // 'threshold_offlevel' => isset($request->facility_configuration['threshold_offlevel']) ? $request->facility_configuration['threshold_offlevel'] : '0',

                    // 'is_lot_full' => isset($request->facility_configuration['is_lot_full']) ? $request->facility_configuration['is_lot_full'] : '0',

                    //PIMS-14211 || Dev:Sagar || Date:23/07/2025
                    'is_true_start' => isset($request->facility_configuration['is_true_start']) ? $request->facility_configuration['is_true_start'] : '0',
                    'oversize_fee_times' => isset($request->facility_configuration['oversize_fee_times']) ? $request->facility_configuration['oversize_fee_times'] : NULL,
                    'is_pemit_qrcode' => isset($request->facility_configuration['is_pemit_qrcode']) ? $request->facility_configuration['is_pemit_qrcode'] : '0',
                    'oversize_fee_amount_type' => isset($request->facility_configuration['oversize_fee_amount_type']) ? $request->facility_configuration['oversize_fee_amount_type'] : '0',
                    //PMIS-14836 
                    'permit_email' => isset($request->facility_configuration['permit_email']) ? $request->facility_configuration['permit_email'] : '1',
                    'cust_email_admin_email' => isset($request->facility_configuration['cust_email_admin_email']) ? $request->facility_configuration['cust_email_admin_email'] : '2',
                    //PMIS-14836 
                ]);
                /**
                 * PIMS-13373 
                 * DD
                 * ANTIPASSBACK FOR BULK 
                 */


                /* if (isset($request->facility_configuration['is_antipass_enabled'])) {
                    PermitRequest::where(['facility_id' => $facility->id])
                        ->update(['is_antipass_enabled' => $request->facility_configuration['is_antipass_enabled']]);
                } */
            }
            //UPBL-87
            // if((isset($request->facility_configuration['threshold_type']) && ($request->facility_configuration['threshold_type'] == 0 || $request->facility_configuration['threshold_type'] == '0'))
            // && (isset($request->facility_configuration['threshold_onlevel']) && ($request->facility_configuration['threshold_onlevel'] > 0 || $request->facility_configuration['threshold_onlevel'] > '0')) 
            // && (isset($request->facility_configuration['threshold_offlevel']) && ($request->facility_configuration['threshold_offlevel'] > 0 || $request->facility_configuration['threshold_offlevel'] > '0'))
            // ) {
            //     if(isset($request->facility_configuration['is_lot_full']) && ($request->facility_configuration['is_lot_full'] == 1 || $request->facility_configuration['is_lot_full'] == '1')) {
            //         $params = ['facility_id' => $request->facility_configuration['facility_id'],'is_full'=>'true'];
            //         $this->log->info("Request Parkengage SetLotStatus Service : SetLotStatus command about to run facility {$request->facility_configuration['facility_id']} params" . json_encode($params));
            //         $command_response = ParkengageGateApi::SetLotStatus($params, $facility->adam_host);
            //         $this->log->info("Response Parkengage SetLotStatus Service : SetLotStatus command complete facility {$request->facility_configuration['facility_id']} response" . json_encode($command_response));
            //     } else {
            //         $params = ['facility_id' => $request->facility_configuration['facility_id'],'is_full'=>'false'];
            //         $this->log->info("Request Parkengage SetLotStatus Service : SetLotStatus command about to run facility {$request->facility_configuration['facility_id']} params" . json_encode($params));
            //         $command_response = ParkengageGateApi::SetLotStatus($params, $facility->adam_host);
            //         $this->log->info("Response Parkengage SetLotStatus Service : SetLotStatus command complete facility {$request->facility_configuration['facility_id']} response" . json_encode($command_response));
            //     }
            // }
            //UPBL-87
        }

        if ($request->facilityfee) {
            $arr = [];
            foreach ($request->facilityfee as $key => $val) {
                $arr[$key]['facility_id'] = $val['facility_id'];
                $arr[$key]['partner_id'] = $val['partner_id'];
                $arr[$key]['name'] = $val['name'];
                $arr[$key]['val'] = $val['val'];
                $arr[$key]['active'] = $val['active'];
            }
            FacilityFee::where('facility_id', $request->id)->delete();
            //return $arr;
            FacilityFee::insert($arr);
        } else {
            FacilityFee::where('facility_id', $request->id)->delete();
        }

        #dushyant 29/05/2024 USM
        if ($request->serviceData) {
            foreach ($request->serviceData as $permit_services) {
                $data = [];
                $data['is_status'] = $permit_services['is_status'];
                $data['is_purchase'] = $permit_services['is_purchase'];
                $data['is_negotiable'] = $permit_services['is_negotiable'];
                //PermitServices::where('id', $permit_services['id'])->update($data);
                $mapping = PermitServicesFacilityMapping::where('permit_service_id', $permit_services['id'])
                    ->where('facility_id', $request->id)
                    ->first();

                if ($mapping) {
                    // If the record exists, update it with $data
                    $mapping->update($data);
                } else {
                    // If no record exists, create a new one with $data
                    PermitServicesFacilityMapping::create([
                        'permit_service_id' => $permit_services['id'],
                        'facility_id' => $request->id,
                        'is_purchase' => $permit_services['is_purchase'],
                        'is_negotiable' => $permit_services['is_negotiable'],
                        'is_status' => $permit_services['is_status']
                    ]);
                }
            }
        }
        #end dushyant 29/05/2024 USM 

        if ($request->rate_duration_in_hours <= 0 && $request->rate_per_hour <= 0 && $request->rate_free_minutes <= 0 && $request->rate_daily_max_amount <= 0) {
            $request->request->add(['is_hourly_rate' => '0']);
        }
        if ($request->rate_duration_in_hours > 0 && $request->rate_per_hour > 0 && $request->rate_free_minutes > 0 && $request->rate_daily_max_amount > 0) {
            $request->request->add(['is_hourly_rate' => '1']);
        }

        $additionalFee = $request->additional_fee;
        if (isset($request->additional_fee) && !empty($request->additional_fee)) {
            $request->merge([
                'additonal_fee' => $additionalFee,
            ]);
        }
        //fee audit trail function calling here
        $this->storeFeeAuditTrail($facility, $request);

        $facility->fill($request->all())->save();

        $this->setNeighborhood($facility, $request);
        $facility->fill($request->input());
        $facility->save();

        //update or save facility payment details
        // This code is move to new api for update facility intergration tab data

        // Reload facility to get any fields that are modified by database types

        $facilityData = Facility::with(
            'facilityfee',
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            'service_data',
            'FacilityPaymentDetails',
            'faciltyBrandSetting',
            'facilityConfiguration'
        )->find($facility->id);

        $getMasterShortCodeMapping = ShortCodeFacilityMapping::where('facility_id', $facilityData->id)->first();

        $garageCode = $facilityData->garage_code;
        $status = isset($facilityData->active) ? $facilityData->active : 1;
        $partnerId = isset($facilityData->owner_id) ? $facilityData->owner_id : null;

        if ($getMasterShortCodeMapping) {
            $shortCodeId = $getMasterShortCodeMapping->short_code_id;
            $updateMasterShortCode = MasterFacilityShortCode::find($shortCodeId);

            if ($updateMasterShortCode) {
                // Update status
                $updateMasterShortCode->status = $status;

                // Check and update short_code if different
                if ($updateMasterShortCode->short_code !== $garageCode) {
                    $existingShortCode = MasterFacilityShortCode::where('short_code', $garageCode)
                        ->where('master_facility_short_code.id', '!=', $updateMasterShortCode->id)
                        ->join('short_code_facility_mapping', 'master_facility_short_code.id', '=', 'short_code_facility_mapping.short_code_id')
                        ->where('short_code_facility_mapping.facility_id', '!=', $facilityData->id)
                        ->exists();

                    if ($existingShortCode) {
                        throw new \Exception("Garage code '{$garageCode}' is already in use by another facility.");
                    }

                    $updateMasterShortCode->short_code = $garageCode;
                }

                $updateMasterShortCode->save();
            } else {
                // If short code is missing but mapping exists, create new short code and update mapping
                $newShortCode = new MasterFacilityShortCode();
                $newShortCode->short_code = $garageCode;
                $newShortCode->type = 0;
                $newShortCode->status = $status;
                $newShortCode->save();

                $getMasterShortCodeMapping->short_code_id = $newShortCode->id;
                $getMasterShortCodeMapping->partner_id = $partnerId;
                $getMasterShortCodeMapping->save();
            }
        } else {
            // No mapping, create new short code and mapping
            $newShortCode = new MasterFacilityShortCode();
            $newShortCode->short_code = $garageCode;
            $newShortCode->type = 0;
            $newShortCode->status = $status;
            $newShortCode->save();

            $newMapping = new ShortCodeFacilityMapping();
            $newMapping->short_code_id = $newShortCode->id;
            $newMapping->facility_id = $facilityData->id;
            $newMapping->partner_id = $partnerId;
            $newMapping->save();
        }

        return $facilityData;
    }

    public function updateGeneral(Request $request, Facility $facility)
    {
        $this->log->info("Update General Facility Log Request :  " . json_encode($request->all()));
        $this->log->info("Update General Facility Request By:  " . Auth::user()->id);
        $this->log->info("General Log Request Facility :  " . json_encode($facility));
        $facility->fill($request->all())->save();

        $facility->fill($request->input());
        $facility->save();

        // Reload facility to get any fields that are modified by database types

        return Facility::with(
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            'faciltyBrandSetting'
        )->find($facility->id);
    }

    public function updateGeolocation(Request $request, Facility $facility)
    {
        $this->log->info("Update Facility Geolocation Log Request :  " . json_encode($request->all()));
        $this->log->info("Update Facility Geolocation Request By:  " . Auth::user()->id);
        $this->log->info("Log Request Geolocation Facility :  " . json_encode($facility));

        if ($request->geolocations) {
            $facility->geolocations->fill($request->geolocations)->save();
        }

        $facility->fill($request->all())->save();

        $facility->fill($request->input());
        $facility->save();

        // Reload facility to get any fields that are modified by database types

        return Facility::with(
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            'faciltyBrandSetting'
        )->find($facility->id);
    }

    public function updateRate(Request $request, Facility $facility)
    {
        $this->log->info("Update Facility Rate Log Request :  " . json_encode($request->all()));
        $this->log->info("Update Facility Rate Request By:  " . Auth::user()->id);
        $this->log->info("Log Request Rete Facility :  " . json_encode($facility));

        $facility->fill($request->all())->save();

        $facility->fill($request->input());
        $facility->save();

        // Reload facility to get any fields that are modified by database types

        return Facility::with(
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            'faciltyBrandSetting'
        )->find($facility->id);
    }

    public function showIntegrations(Facility $facility)
    {
        return $facility->getIntegrations();
    }

    /**
     * Send an email with facility information to the given email address
     *
     * @param  Facility $facility [description]
     * @return [type]             [description]
     */
    public function sendFacilityEmail(Request $request, $id)
    {
        $this->validate(
            $request,
            ['to' => 'required|email']
        );

        // Check we are not over rate limit before throwing into queue
        $rateLimiter = new MessageRateLimiter();
        if ($rateLimiter->isOverRateLimit($request->to, $request->ip())) {
            throw new ApiGenericException('You have sent too many emails to this address, please wait before sending.');
        }

        // Queue up our email
        Artisan::queue('icon:facility-email', ['facility' => $id, 'to' => $request->to, 'ip' => $request->ip()]);

        return ['queued' => true];
    }

    public function getActiveFacilityEvents(Request $request, Facility $facility)
    {
        $eventsArray = [];
        if (!$facility->events) {
            throw new NotFoundException('No active events found.');
        }

        $arrival = Carbon::createFromTimestamp(strtotime($request->arrival_time));
        $exit    = Carbon::createFromTimestamp(strtotime($arrival))->addHours($request->length_of_stay);

        foreach ($facility->events as $event) {
            if ($event->isActiveEventTime($arrival) || $event->isActiveEventTime($exit)) {
                $eventsArray[] = $event;
            }
        }
        return $eventsArray;
    }

    public function getFutureFacilityEvents(Facility $facility)
    {
        $events = $facility->events()->future()->get();

        foreach ($events as $event) {
            $event->full_event_price = $event->getFacilityFullEventPrice($facility);
        }
        return $events;
    }

    protected function mapRequestDataToFill($model, $request)
    {
        $keys         = Schema::getColumnListing($model->getTable());
        $request_data = array_filter($request->only($keys));
        return $model->fill($request_data);
    }

    public function addPhoto(Facility $facility, Request $request)
    {
        $this->validate($request, ['image' => 'required|mimes:jpeg,png,jpg'], ['image.required' => 'Garage image must be a file of type : .jpeg, .png, .jpg', 'image.mimes' => 'Garage image must be a file of type : .jpeg, .png, .jpg']);

        $facility->photos()->delete(); //Delete all copies of the previous logos

        $image    = $request->file('image');
        $filename = Uuid::generate(4) . '.' . $image->getClientOriginalExtension();

        $photo             = new Photo();
        $photo->image_name = $filename;
        $facility->photos()->save($photo);

        Image::make($image->getRealPath())->save(storage_path("app/" . $facility::IMAGE_FOLDER . '/' . $filename));

        return $photo;
    }

    protected function mapNeighborhoodInputToArray($request)
    {
        return [
            'title'       => $request->input('neighborhood.title'),
            'longitude'   => $request->input('neighborhood.longitude'),
            'latitude'    => $request->input('neighborhood.latitude'),
            'description' => $request->input('neighborhood.description')
        ];
    }

    protected function associateFacilityType($facility, $request)
    {
        $facility_type = $request->input('facility_type.facility_type');
        if ($facility_type) {
            $model                      = FacilityType::firstOrCreate(['facility_type' => $facility_type]);
            $facility->facility_type_id = $model->id;
            $facility->FacilityType()->associate($model);
        }
    }

    public function addFacilityRates(Request $request, Facility $facility)
    {
        $this->log->info("Add Facility Rate Log Request :  " . json_encode($request->all()));
        $this->log->info("Add Facility Rate Request By:  " . Auth::user()->id);
        $this->log->info("Add Log Request Rate Facility :  " . json_encode($facility));

        $this->validate(
            $request,
            ['active' => 'required', 'id' => 'required', 'rate' => 'required',]
        );
        if ($request->input('rate') < 0) {

            throw new ApiGenericException('Invalid Rate. It must be numeric.');
        }
        if ($request->input('active') < 0 || $request->input('active') > 1) {

            throw new ApiGenericException('Invalid active parameter. It must be 0 or 1');
        }
        if (!RateDescription::where('id', $request->input('id'))->get()->count()) {
            throw new ApiGenericException('Category ID is invalid');
        }
        if ($request->input('rate_id') != '') {
            $monthly_rates = FacilityRate::where('id', $request->input('rate_id'))->first();
            $monthly_rates->active = $request->input('active');
            $monthly_rates->rate  = $request->input('rate');
            $monthly_rates->name   = $request->input('name');
            $monthly_rates->save();
        } else {
            $monthly_rates['active'] = $request->input('active');
            $monthly_rates['rate']   = $request->input('rate');
            $monthly_rates['name']   = $request->input('name');
            $monthly_rates['rate_description_id']   = $request->input('id');
            $monthly_rates['facility_id']   = $facility->id;
            FacilityRate::create($monthly_rates);
        }
        /*$monthly_rates[0]['active'] = $request->input('active');
        $monthly_rates[0]['id']     = $request->input('id');
        $monthly_rates[0]['rate']   = $request->input('rate');
        $monthly_rates[0]['name']   = $request->input('name');
        //$monthly_rates = $request->input('monthly_rates.*');
        if (count($monthly_rates)) {
            foreach ($monthly_rates as $monthly_rate) {
                $facility_rate = FacilityRate::firstOrCreate(
                    ['id' => $monthly_rate['id'], 'facility_id' => $facility->id]);
                FacilityRate::where('id', $facility_rate->id)->update(
                    ['rate' => $monthly_rate['rate'], 'active' => $monthly_rate['active'], 'name' => $monthly_rate['name']]);
            }
        }*/
        return Facility::with(
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            'facilityRate.rateDescription',
            'faciltyBrandSetting'
        )->find($facility->id);
    }


    public function destroyFacilityRates(Request $request, $id)
    {
        $facilityRate = FacilityRate::find($id);
        if (!$facilityRate) {
            throw new ApiGenericException('The id does not exist');
        }
        if ($facilityRate->delete()) {
            return Facility::with(
                'facilityType',
                'geolocations',
                'photos',
                'features',
                'hoursOfOperation',
                'neighborhood',
                'rates',
                'facilityRate.rateDescription',
                'faciltyBrandSetting'
            )->find(
                $facilityRate->facility_id
            );
        } else {
            throw new ApiGenericException('Unable to delete Data.');
        }
    }

    public function getMonthlyRates(Facility $facility)
    {
        return Facility::with(
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            'facilityRate.rateDescription',
            'faciltyBrandSetting'
        )->find($facility->id);
    }

    //permit rates

    public function addPermitRates(Request $request, Facility $facility)
    {
        $this->log->info("Add Permit Rate Facility Log Request :  " . json_encode($request->all()));
        $this->log->info("Add Permit Rate Facility Request By:  " . Auth::user()->id);
        $this->log->info("Add Permit Rate Log Request Facility :  " . json_encode($facility));

        $this->validate(
            $request,
            ['active' => 'required', 'id' => 'required', 'rate' => 'required',]
        );
        if ($request->input('rate') < 0) {

            throw new ApiGenericException('Invalid Rate. It must be numeric.');
        }
        if ($request->input('active') < 0 || $request->input('active') > 1) {

            throw new ApiGenericException('Invalid active parameter. It must be 0 or 1');
        }
        if (!PermitRateDescription::where('id', $request->input('id'))->get()->count()) {
            throw new ApiGenericException('Category ID is invalid');
        }
        if ($request->input('rate_id') != '') {
            $remaining_usage_new = 0;
            $monthly_rates = PermitRate::where('id', $request->input('rate_id'))->first();

            $new_val = $request->input('total_usage');
            $total_usage = $monthly_rates->total_usage;
            $remaining_usage = $monthly_rates->remaining_usage;

            if ($new_val > $total_usage) {
                $remaining_total = ($new_val - $total_usage);
                $remaining_usage_new = $remaining_total + $remaining_usage;
            } else if (($new_val < $total_usage)) {
                $remaining_total = ($total_usage - $new_val);
                //return $remaining_total;
                if ($remaining_total > $remaining_usage) {
                    throw new ApiGenericException('The Remaining Value not Accepted');
                } else if ($remaining_total < $remaining_usage) {
                    $remaining_usage_new = $remaining_usage - $remaining_total;
                } else {
                    $remaining_usage_new = $remaining_total - $remaining_usage;
                }
            } else if ($new_val == $total_usage) {
                $remaining_usage_new = $monthly_rates->remaining_usage;
            } else {
                throw new ApiGenericException('Invalid Data');
            }

            $monthly_rates->active = $request->input('active');
            $monthly_rates->permit_rate_description_id = $request->input('id');
            $monthly_rates->rate  = $request->input('rate');
            $monthly_rates->name   = $request->input('name');
            $monthly_rates->rate_type   = $request->input('rate_type');
            $monthly_rates->total_count   = $request->input('total_count');
            $monthly_rates->total_usage   = $request->input('total_usage');
            $monthly_rates->remaining_usage   = $remaining_usage_new;
            $monthly_rates->facility_id   = $facility->id;
            $monthly_rates->sign_up_timeframe   = $request->input('sign_up_timeframe');
            //$monthly_rates->is_resident   = (($request->input('id') == '7') || ($request->input('id') == '9')) ? '1' : '0';
            $is_resident = $request->input('is_resident');
            if ($is_resident == '2') {
                $is_resident = "0";
            }
            $monthly_rates->is_resident   = $is_resident;

            //permit rate additional code
            $monthly_rates->monday = $request->input('monday') == '1' ? $request->input('monday') : '0';
            $monthly_rates->tuesday = $request->input('tuesday') == '1' ? $request->input('tuesday') : '0';
            $monthly_rates->wednesday = $request->input('wednesday') == '1' ? $request->input('wednesday') : '0';
            $monthly_rates->thursday = $request->input('thursday') == '1' ? $request->input('thursday') : '0';
            $monthly_rates->friday = $request->input('friday') == '1' ? $request->input('friday') : '0';
            $monthly_rates->saturday = $request->input('saturday') == '1' ? $request->input('saturday') : '0';
            $monthly_rates->sunday = $request->input('sunday') == '1' ? $request->input('sunday') : '0';
            $monthly_rates->entry_time_begin = $request->input('entry_time_begin');
            $monthly_rates->entry_time_end = $request->input('entry_time_end');
            $monthly_rates->exit_time_begin = $request->input('exit_time_begin');
            $monthly_rates->exit_time_end = $request->input('exit_time_end');

            $monthly_rates->save();

            if ($request->permit_rate_discount) {
                PermitRateDiscount::where('permit_rate_description_id', $request->input('id'))->delete();
                foreach ($request->permit_rate_discount as $key => $value) {
                    $discount['rate'] = $request->input('rate');
                    $discount['rate_discount'] = $value['rate_discount'];
                    $discount['discount_name'] = $value['discount_name'];
                    $discount['permit_rate_description_id']   = $request->input('id');
                    PermitRateDiscount::create($discount);
                }
            }
        } else {
            $monthly_rates['active'] = $request->input('active');
            $monthly_rates['rate']   = $request->input('rate');
            $monthly_rates['name']   = $request->input('name');
            $monthly_rates['permit_rate_description_id']   = $request->input('id');
            $monthly_rates['rate_type']   = $request->input('rate_type');
            $monthly_rates['total_count']   = $request->input('total_count');
            $monthly_rates['total_usage']   = $request->input('total_usage');
            $monthly_rates['remaining_usage']   = $request->input('total_usage');
            $monthly_rates['facility_id']   = $facility->id;
            $monthly_rates['sign_up_timeframe']   = $request->input('sign_up_timeframe');
            //$monthly_rates['is_resident']   = (($request->input('id') == '7') || ($request->input('id') == '9')) ? '1' : '0';
            $is_resident = $request->input('is_resident');
            if ($is_resident == '2') {
                $is_resident = "0";
            }
            $monthly_rates['is_resident']   = $is_resident;

            //permit rate additional code
            $monthly_rates['monday'] = $request->input('monday') == '1' ? $request->input('monday') : '0';
            $monthly_rates['tuesday'] = $request->input('tuesday') == '1' ? $request->input('tuesday') : '0';
            $monthly_rates['wednesday'] = $request->input('wednesday') == '1' ? $request->input('wednesday') : '0';
            $monthly_rates['thursday'] = $request->input('thursday') == '1' ? $request->input('thursday') : '0';
            $monthly_rates['friday'] = $request->input('friday') == '1' ? $request->input('friday') : '0';
            $monthly_rates['saturday'] = $request->input('saturday') == '1' ? $request->input('saturday') : '0';
            $monthly_rates['sunday'] = $request->input('sunday') == '1' ? $request->input('sunday') : '0';
            $monthly_rates['entry_time_begin'] = $request->input('entry_time_begin');
            $monthly_rates['entry_time_end'] = $request->input('entry_time_end');
            $monthly_rates['exit_time_begin'] = $request->input('exit_time_begin');
            $monthly_rates['exit_time_end'] = $request->input('exit_time_end');



            PermitRate::create($monthly_rates);

            if ($request->permit_rate_discount) {
                foreach ($request->permit_rate_discount as $key => $value) {
                    $discount['rate'] = $request->input('rate');
                    $discount['rate_discount'] = $value['rate_discount'];
                    $discount['discount_name'] = $value['discount_name'];
                    $discount['permit_rate_description_id']   = $request->input('id');
                    PermitRateDiscount::create($discount);
                }
            }
        }
        /*$monthly_rates[0]['active'] = $request->input('active');
        $monthly_rates[0]['id']     = $request->input('id');
        $monthly_rates[0]['rate']   = $request->input('rate');
        $monthly_rates[0]['name']   = $request->input('name');
        //$monthly_rates = $request->input('monthly_rates.*');
        if (count($monthly_rates)) {
            foreach ($monthly_rates as $monthly_rate) {
                $facility_rate = FacilityRate::firstOrCreate(
                    ['id' => $monthly_rate['id'], 'facility_id' => $facility->id]);
                FacilityRate::where('id', $facility_rate->id)->update(
                    ['rate' => $monthly_rate['rate'], 'active' => $monthly_rate['active'], 'name' => $monthly_rate['name']]);
            }
        }*/
        return Facility::with(
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            'permitRate.rateDescription.PermitRateDiscount',
            'faciltyBrandSetting'
        )->find($facility->id);
    }

    public function destroyPermitRates(Request $request, $id)
    {
        $facilityRate = PermitRate::find($id);
        if (!$facilityRate) {
            throw new ApiGenericException('The id does not exist');
        }
        if ($facilityRate->delete()) {
            return Facility::with(
                'facilityType',
                'geolocations',
                'photos',
                'features',
                'hoursOfOperation',
                'neighborhood',
                'rates',
                'permitRate.rateDescription.PermitRateDiscount',
                'faciltyBrandSetting'
            )->find(
                $facilityRate->facility_id
            );
        } else {
            throw new ApiGenericException('Unable to delete Data.');
        }
    }

    public function getPermitRates(Facility $facility)
    {
        return Facility::with(
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            //'facilityRate.rateDescription',
            'permitRate.rateDescription.PermitRateDiscount',
            'faciltyBrandSetting'
        )->find($facility->id);
    }

    /**
     *  started 29-04-2024 
     *  update add permit rate function 
     *  add time for each day rather fix time for all previously
     *  START
     **/

    public function addPermitRatesDayWise(Request $request, Facility $facility)
    {
        $this->log->info("Add Permit Rate Daywise Facility Log Request :  " . json_encode($request->all()));
        $this->log->info("Add Permit Rate Daywise Facility Request By:  " . Auth::user()->id);
        $this->log->info("Add Permit Rate Daywise Log Request Facility :  " . json_encode($facility));

        $this->validate(
            $request,
            ['active' => 'required', 'id' => 'required', 'rate' => 'required', 'partner_id' => 'required',]
        );
        if ($request->input('rate') < 0) {

            throw new ApiGenericException('Invalid Rate. It must be numeric.');
        }
        if ($request->input('active') < 0 || $request->input('active') > 1) {

            throw new ApiGenericException('Invalid active parameter. It must be 0 or 1');
        }
        if (!PermitRateDescription::where('id', $request->input('id'))->get()->count()) {
            throw new ApiGenericException('Category ID is invalid');
        }
        $permitRateDescription = PermitRateDescription::where('id', $request->input('id'))->first();
        $is_promotion = $request->input('is_promotion');
        if ($permitRateDescription) {
            $permitRateDescription->is_promotion = isset($is_promotion) ? $is_promotion : '0';
            $permitRateDescription->save();
        }
        if ($request->input('rate_id') != '') { //edit permit rate
            $remaining_usage_new = 0;
            $monthly_rates = PermitRate::where('id', $request->input('rate_id'))->first();
            if (!$monthly_rates) {
                throw new ApiGenericException('Permit Rate not exist');
            }
            $new_val = $request->input('total_usage');
            if ($new_val) {
                $total_usage = $monthly_rates->total_usage;
                $remaining_usage = $monthly_rates->remaining_usage;

                if ($new_val > $total_usage) {
                    $remaining_total = ($new_val - $total_usage);
                    $remaining_usage_new = $remaining_total + $remaining_usage;
                } else if (($new_val < $total_usage)) {
                    $remaining_total = ($total_usage - $new_val);
                    //return $remaining_total;
                    if ($remaining_total > $remaining_usage) {
                        throw new ApiGenericException('The Remaining Value not Accepted');
                    } else if ($remaining_total < $remaining_usage) {
                        $remaining_usage_new = $remaining_usage - $remaining_total;
                    } else {
                        $remaining_usage_new = $remaining_total - $remaining_usage;
                    }
                } else if ($new_val == $total_usage) {
                    $remaining_usage_new = $monthly_rates->remaining_usage;
                } else {
                    throw new ApiGenericException('Invalid Data');
                }
                $monthly_rates->total_usage   = $request->input('total_usage');
                $monthly_rates->remaining_usage   = $remaining_usage_new;
            } else {
                $monthly_rates->total_usage       = NULL;
                $monthly_rates->remaining_usage   = NULL;
            }
            $sign_up_timeframe = $request->input('sign_up_timeframe');
            if ($sign_up_timeframe != '') {
                $monthly_rates->sign_up_timeframe   = $request->input('sign_up_timeframe');
            } else {
                $monthly_rates->sign_up_timeframe   = NULL;
            }
            $monthly_rates->active = $request->input('active');
            $monthly_rates->permit_rate_description_id = $request->input('id');
            $monthly_rates->rate  = $request->input('rate');
            $monthly_rates->name   = $request->input('name');
            $monthly_rates->rate_type   = $request->input('rate_type');
            $monthly_rates->total_count   = $request->input('total_count');
            $monthly_rates->facility_id   = $facility->id;

            //$monthly_rates->is_resident   = (($request->input('id') == '7') || ($request->input('id') == '9')) ? '1' : '0';
            $is_resident = $request->input('is_resident');
            if ($is_resident == '2') {
                $is_resident = "0";
            }
            if ($is_resident == '') {
                $is_resident = "0";
            }

            $monthly_rates['is_promotion']   = isset($is_promotion) ? $is_promotion : '0';
            $monthly_rates->is_resident   = $is_resident;
            $monthly_rates->entry_time_begin = '00-00-00';
            $monthly_rates->entry_time_end = '00-00-00';
            $monthly_rates->exit_time_begin = '00-00-00';
            $monthly_rates->exit_time_end = '00-00-00';
            $monthly_rates->save();
            Log::info('Permit Rate Criteria Update Auth: ' . Auth::user()->id);
            Log::info("Permit Rate Criteria Update json" . json_encode($request->permit_rate_criteria));
            if (isset($request->permit_rate_criteria)) {

                $criteriaMonDay = $criteriaTueDay = $criteriaWedDay = $criteriaThuDay = $criteriaFriDay = $criteriaSatDay = $criteriaSunDay = '0';
                foreach ($request->permit_rate_criteria as $key => $criteria) {

                    /********** 1. new table permit_rate_criteria **********/

                    $days = $criteria['days'];

                    $data = [
                        'label' => $criteria['label'],
                        'days' => $days,
                        'entry_time_begin' => isset($criteria['entry_time_begin']) ? $criteria['entry_time_begin'] : '00-00-00',
                        'entry_time_end' => isset($criteria['entry_time_end']) ? $criteria['entry_time_end'] : '00-00-00',
                        'exit_time_begin' => isset($criteria['exit_time_begin']) ? $criteria['exit_time_begin'] : '00-00-00',
                        'exit_time_end' => isset($criteria['exit_time_end']) ? $criteria['exit_time_end'] : '00-00-00',
                    ];

                    $conditions = [
                        'partner_id' => $request->input('partner_id'),
                        'label' => $criteria['label'],
                    ];

                    $existingRecord = PermitRateCriteria::where($conditions)->first();

                    if ($existingRecord) {
                        //$existingRecord->update($data);
                        $conditionsMapping = [
                            'permit_rate_id' => $monthly_rates->id,
                            'permit_rate_criteria_id' => $existingRecord->id,
                        ];
                        $existingPermitMappingRecord = PermitRateCriteriaMapping::where($conditionsMapping)->first();
                        if (!$existingPermitMappingRecord) {
                            $data_mapping = [
                                'facility_id'   => $facility->id,
                                'permit_rate_criteria_id' => $existingRecord->id,
                                'permit_rate_id'    => $monthly_rates->id
                            ];
                            PermitRateCriteriaMapping::create($data_mapping);
                        }
                    } else {
                        $permit_rate_criteria =  PermitRateCriteria::create(array_merge($conditions, $data));
                        $data_mapping = [
                            'facility_id'   => $facility->id,
                            'permit_rate_criteria_id' => $permit_rate_criteria->id,
                            'permit_rate_id'    => $monthly_rates->id
                        ];
                        PermitRateCriteriaMapping::create($data_mapping);
                    }

                    /********** end new table permit_rate_criteria **********/
                } //endforach

                // Delete records that were not found in the response
                /* PermitRateCriteria::where('partner_id', $request->input('partner_id'))
                ->whereNotIn('label', array_column($request->permit_rate_criteria, 'label'))
                ->delete(); */
                $permit_rate_criteria_ids_to_delete = PermitRateCriteria::where('partner_id', $request->partner_id)
                    ->whereNotIn('label', array_column($request->permit_rate_criteria, 'label'))
                    ->pluck('id');
                //PermitRateCriteria::whereIn('id', $permit_rate_criteria_ids_to_delete)->delete();
                PermitRateCriteriaMapping::where('permit_rate_id', $request->input('rate_id'))->whereIn('permit_rate_criteria_id', $permit_rate_criteria_ids_to_delete)->delete();

                /*  PermitRateCriteriaMapping::where('permit_rate_criteria_id', $request->input('partner_id'))
                ->delete(); */
            } else {
                //else no permit rate days passed
            }


            if ($request->permit_rate_discount) {
                PermitRateDiscount::where('permit_rate_description_id', $request->input('id'))->delete();
                foreach ($request->permit_rate_discount as $key => $value) {
                    $discount['rate'] = $request->input('rate');
                    $discount['rate_discount'] = $value['rate_discount'];
                    $discount['discount_name'] = $value['discount_name'];
                    $discount['permit_rate_description_id']   = $request->input('id');
                    PermitRateDiscount::create($discount);
                }
            }
        } else { //add permit rates
            $sign_up_timeframe = $request->input('sign_up_timeframe');
            if ($sign_up_timeframe != '') {
                $monthly_rates['sign_up_timeframe']   = $request->input('sign_up_timeframe');
            } else {
                $monthly_rates['sign_up_timeframe']   = NULL;
            }
            $permit_usage = $request->input('total_usage');
            $monthly_rates['active'] = $request->input('active');
            $monthly_rates['rate']   = $request->input('rate');
            $monthly_rates['name']   = $request->input('name');
            $monthly_rates['permit_rate_description_id']   = $request->input('id');
            $monthly_rates['rate_type']     = $request->input('rate_type');
            $monthly_rates['total_count']   = $request->input('total_count');
            $monthly_rates['total_usage']   = isset($permit_usage) ? $permit_usage : NULL;
            $monthly_rates['remaining_usage'] = isset($permit_usage) ? $permit_usage : NULL;
            $monthly_rates['facility_id']   = $facility->id;

            //$monthly_rates['is_resident']   = (($request->input('id') == '7') || ($request->input('id') == '9')) ? '1' : '0';
            $is_resident = $request->input('is_resident');
            if ($is_resident == '2') {
                $is_resident = "0";
            }
            if ($is_resident == '') {
                $is_resident = "0";
            }

            $monthly_rates['is_promotion']   = isset($is_promotion) ? $is_promotion : '0';
            $monthly_rates['is_resident']   = $is_resident;
            $monthly_rates['entry_time_begin'] = '00-00-00';
            $monthly_rates['entry_time_end'] = '00-00-00';
            $monthly_rates['exit_time_begin'] = '00-00-00';
            $monthly_rates['exit_time_end'] = '00-00-00';
            $permitRate = PermitRate::create($monthly_rates);
            Log::info('Permit Rate Criteria Add Auth: ' . Auth::user()->id);
            Log::info("Permit Rate Criteria Add json" . json_encode($request->permit_rate_criteria));
            if (isset($request->permit_rate_criteria)) {
                $criteriaMonDay = $criteriaTueDay = $criteriaWedDay = $criteriaThuDay = $criteriaFriDay = $criteriaSatDay = $criteriaSunDay = '0';
                foreach ($request->permit_rate_criteria as $key => $criteria) {

                    /********** 1. new table permit_rate_criteria **********/


                    $days = $criteria['days'];

                    $data = [
                        'label' => $criteria['label'],
                        'days' => $days,
                        'entry_time_begin' => isset($criteria['entry_time_begin']) ? $criteria['entry_time_begin'] : '00-00-00',
                        'entry_time_end' => isset($criteria['entry_time_end']) ? $criteria['entry_time_end'] : '00-00-00',
                        'exit_time_begin' => isset($criteria['exit_time_begin']) ? $criteria['exit_time_begin'] : '00-00-00',
                        'exit_time_end' => isset($criteria['exit_time_end']) ? $criteria['exit_time_end'] : '00-00-00',
                    ];

                    $conditions = [
                        'partner_id' => $request->input('partner_id'),
                        'label' => $criteria['label'],
                    ];

                    $existingRecord = PermitRateCriteria::where($conditions)->first();

                    if ($existingRecord) {
                        $conditionsMapping = [
                            'permit_rate_id' => $permitRate->id,
                            'permit_rate_criteria_id' => $existingRecord->id,
                        ];
                        $existingPermitMappingRecord = PermitRateCriteriaMapping::where($conditionsMapping)->first();
                        if (!$existingPermitMappingRecord) {
                            $data_mapping = [
                                'facility_id'   => $facility->id,
                                'permit_rate_criteria_id' => $existingRecord->id,
                                'permit_rate_id'    => $permitRate->id
                            ];
                            PermitRateCriteriaMapping::create($data_mapping);
                        }
                    } else {
                        $permit_rate_criteria =  PermitRateCriteria::create(array_merge($conditions, $data));
                        $data_mapping = [
                            'facility_id'   => $facility->id,
                            'permit_rate_criteria_id' => $permit_rate_criteria->id,
                            'permit_rate_id'    => $permitRate->id
                        ];
                        PermitRateCriteriaMapping::create($data_mapping);
                    }
                    /********** end new table permit_rate_criteria **********/
                }
            } else {
                //permit rate day not passed                
            }

            if ($request->permit_rate_discount) {
                foreach ($request->permit_rate_discount as $key => $value) {
                    $discount['rate'] = $request->input('rate');
                    $discount['rate_discount'] = $value['rate_discount'];
                    $discount['discount_name'] = $value['discount_name'];
                    $discount['permit_rate_description_id']   = $request->input('id');
                    PermitRateDiscount::create($discount);
                }
            }
        }

        return Facility::with(
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            'permitRate.permitRateCriteriaMapping.permitRateCriteria',
            'permitRate.rateDescription.PermitRateDiscount',
            'faciltyBrandSetting'
        )->find($facility->id);
    }


    public function destroyPermitRatesDayWise(Request $request, $id)
    {
        $facilityRate = PermitRate::find($id);
        if (!$facilityRate) {
            throw new ApiGenericException('The id does not exist');
        }
        if ($facilityRate->delete()) {
            //dushyant 29/05/2024 delete mapping for deleted permit rate with criteria
            $permitRateCriteriaMapping = PermitRateCriteriaMapping::where('permit_rate_id', $id)->first();
            if ($permitRateCriteriaMapping) {
                PermitRateCriteriaMapping::where('permit_rate_id', $id)->delete();
            }


            return Facility::with(
                'facilityType',
                'geolocations',
                'photos',
                'features',
                'hoursOfOperation',
                'neighborhood',
                'rates',
                'permitRate.permitRateCriteriaMapping.permitRateCriteria',
                'permitRate.rateDescription.PermitRateDiscount',
                'faciltyBrandSetting'
            )->find(
                $facilityRate->facility_id
            );
        } else {
            throw new ApiGenericException('Unable to delete Data.');
        }
    }

    public function getPermitRatesDayWise(Facility $facility)
    {
        $query = Facility::with(
            'facilityType',
            'geolocations',
            'photos',
            'features',
            'hoursOfOperation',
            'neighborhood',
            'rates',
            'permitRate.permitRateCriteriaMapping.permitRateCriteria',
            'permitRate.rateDescription.permitRateDiscount',
            'faciltyBrandSetting'
        )->find($facility->id);

        if (isset($request->promo_category_id) && !empty($request->promo_category_id)) {
            $promoCategoryId = $request->promo_category_id;

            // Filter the rates
            $filteredRates = $query->rates->filter(function ($rate) use ($promoCategoryId) {
                return $rate->promo_category_id == $promoCategoryId;
            });

            // Unset the original rates and assign the filtered rates
            unset($query->rates); // Clear previous rates
            $query->rates = $filteredRates->values(); // Set filtered rates
        }

        return $query;
    }

    public function getPermitCriteriaDayWise(Request $request)
    {
        $validator = Validator::make($request->all(), ['partner_id' => 'required|integer']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }
        $permitResponse =  PermitRateCriteria::where(['partner_id' => $request->partner_id])->get();
        return $permitResponse;
    }

    /**
     *  started 29-04-2024 
     *  update add permit rate function 
     *  add time for each day rather fix time for all previously
     *  END
     **/

    protected function destroysOldGeolocation($facility)
    {
        if ($facility->geolocations) {
            GeoLocation::destroy($facility->geolocations->id);
        }
    }

    protected function setFeatures($facility, $request)
    {
        $features    = $request->input('features.*');
        $featuresKey = [];
        if ($features) {
            foreach ($features as $feature) {
                $model = Feature::firstOrCreate(['name' => $feature['name']]);
                array_push($featuresKey, $model->id);
            }

            // Need to save because sync needs to have a an id.
            // Checks to see if facility is being updated or is new.
            if (!$facility->id) {
                $facility->save();
            }
            $facility->features()->sync($featuresKey);
        }
    }

    protected function setNeighborhood($facility, $request)
    {
        $neighborhood = $request->input('neighborhood.*');
        if ($neighborhood) {
            $neighborhood_data = array_filter($this->mapNeighborhoodInputToArray($request));
            $model             = Neighborhood::firstOrCreate($neighborhood_data);
            if ($model->id) {
                $facility->neighborhood_id = $model->id;
            }
        }
    }

    public function updateBeacon(Request $request)
    {
        $beaconFacility = Facility::where('id', $request->id)->first();

        if (!$beaconFacility) {
            throw new ApiGenericException('Couldn\'t Find Any Facility With Given Id');
        }

        if ($beaconFacility->beacon_status == Facility::BEACON_ACTIVE) {
            $beacon_status = Facility::BEACON_INACTIVE;
        } else {
            $beacon_status = Facility::BEACON_ACTIVE;
        }

        $beaconFacility->fill($request->only(['beacon_status']));

        $result = $beaconFacility->save();
        if (!$result) {
            throw new ApiGenericException('Error Occured, Facility Could Not Be Updated');
        }
        return ['success' => true, 'data' => $beaconFacility];
    }

    /**
     * @param Request $request
     * @return string
     * @throws ApiGenericException
     */
    public function getUptick(Request $request)
    {
        $validator = Validator::make($request->all(), ['facility_id' => 'required|integer']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        preg_match('/,/', $request->facility_id, $matches);
        if (count($matches)) {
            throw new ApiGenericException('Please provide 1 facility id at a time', 422);
        }

        $facility = Facility::find($request->facility_id);

        if (!$facility) {
            throw new ApiGenericException("Facility not found", 422);
        }

        if (is_null(
            $facility->coupon_threshold
        ) || $facility->coupon_threshold == '' || $facility->coupon_threshold == 'null') {
            $facility->coupon_threshold = json_encode($this->generateThreshold(), JSON_FORCE_OBJECT);
            $facility->save();
        }

        return json_decode($facility->coupon_threshold, true);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiGenericException
     */
    public function addUptick(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'facility_id'           => 'required|numeric|min:1',
                'threshold' => 'required|array',
                'threshold.*.threshold' => 'sometimes|required|numeric',
                'threshold.*.uptick'    => 'sometimes|numeric',
            ],
            $messages =
                [
                    'threshold.*.threshold.numeric'  => 'Minimum threshold accepts only numbers',
                    'threshold.*.threshold.required' => 'Minimum threshold is a required field',
                    'threshold.*.threshold.min'      => 'Minimum Threshold cannot be less than 0',
                    'threshold.*.uptick.numeric'     => 'Values field should contain only numbers',
                    'threshold.*.uptick.min'         => 'Values field cannot be zero',
                    'threshold.*.uptick.required'    => 'Values is a required field',
                ]
        );

        $counter = 0;
        foreach ($request->threshold as $threshold) {
            if ($threshold['uptick_type'] == '') {
                $counter++;
            } else if (!in_array($threshold['uptick_type'], ['price', 'percentage', 'deleted'])) {
                throw new ApiGenericException('Please choose either price/percentage for $/% field', 422);
            }
            if ($threshold['threshold'] > 0 && $threshold['uptick'] > 0 && $threshold['uptick_type'] == '') {
                throw new ApiGenericException('Please choose either price/percentage for $/% field', 422);
            }
            if ($counter == 6) {
                throw new ApiGenericException('Please choose either price/percentage for atleast one $/% field', 422);
            }
        }
        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        $facility = Facility::find($request->facility_id);

        $thresholdArray = [];
        $check          = [];
        $deletedCheck = false;
        foreach ($request->threshold as $threshold) {
            //if (($threshold['threshold'] > 0)) {
            if (!in_array($threshold['threshold'], $check) && $threshold['uptick_type'] != 'deleted') {
                $thresholdArray[] = $threshold;
            }
            if ($threshold['uptick_type'] === 'deleted') {
                $deletedCheck = true;
            }

            $check[] = $threshold['threshold'];
            //}
        }

        if ($facility->active == 1) {
            $facility->coupon_threshold = count($thresholdArray) ? json_encode($thresholdArray) : null;
            $facility->save();
        } else {
            throw new ApiGenericException('Cannot update threshold of inactive facility', 422);
        }
        if ($deletedCheck) {
            return response()->json('deleted', 200);
        }

        return response()->json('success', 200);
    }


    /**
     * @return array
     */
    public function generateThreshold()
    {
        $i        = 0;
        $response = [];
        while ($i < 6) {
            $response[] = ['threshold' => 0, 'uptick' => 0, 'uptick_type' => ''];
            $i++;
        }
        return $response;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiGenericException
     */
    public function realtimeWindow(Request $request)
    {
        $validator = Validator::make($request->all(), ['facility_id' => 'required|regex:/[0-9]/']);
        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        preg_match('/,/', $request->facility_id, $matches);
        if (count($matches)) {
            throw new ApiGenericException('Please provide 1 facility id at a time', 422);
        }

        $facility      = Facility::findOrFail($request->facility_id);
        $timeout_val   = isset($facility->realtime_window) ? $facility->realtime_window : self::TIMEOUT_VAL;
        $response      = new \stdClass();
        $realTime      = \GuzzleHttp\json_decode($timeout_val);
        $response->hrs = isset($realTime[0]) ? $realTime[0] : 2;
        return response()->json($response, 200);
    }

    /**
     * @param $facilityIds
     * @throws \Exception
     */
    public function generateAvailability($facilityIds)
    {
        $facilityIds = explode(',', $facilityIds);
        $inventory   = new Inventory();
        foreach ($facilityIds as $facilityId) {
            $inventory->generateAvailabilityForWeek($facilityId, 20, true, 30);
        }
    }

    /**
     * to update monthly campain spacial price and availability
     * @param $facilityId
     *
     */

    public function updateMonthlyCampaignPrice(Request $request, Facility $facility)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'monthly_campaign' => 'required|in:0,1',
                'monthly_campaign_price' => 'required|numeric|regex:"^[0-9]+"',
                'total_availability' => 'required|integer|regex:"^[0-9]+"'
            ]
        );

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }
            throw new ApiGenericException($errors, 422);
        }

        $monthlyCampaign = MonthlyCampaign::where('facility_id', $facility->id)->first();
        if ($monthlyCampaign && $request->total_availability < $monthlyCampaign->used_availability) {
            throw new ApiGenericException("$monthlyCampaign->used_availability Monthly passes already purchased. Total availability must be greater then or equal to $monthlyCampaign->used_availability", 422);
        }

        $facility->monthly_campaign = $request->monthly_campaign;
        $facility->monthly_campaign_price = $request->monthly_campaign_price;
        $facility->save();
        //$monthlyCampaign = MonthlyCampaign::where('facility_id',$facility->id)->first();
        if ($monthlyCampaign) {
            $monthlyCampaign->total_availability = $request->total_availability;
            $monthlyCampaign->save();
        } else {
            $monthlyCampaign = new MonthlyCampaign();
            $monthlyCampaign->facility_id = $facility->id;
            $monthlyCampaign->total_availability = $request->total_availability;
            $monthlyCampaign->save();
        }
        return $facility;
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     Vikrant
     */
    public function updateRealtimeWindow(Request $request)
    {
        $validator = Validator::make($request->all(), ['facility_id' => 'required|integer', 'realtime_window' => 'numeric', 'realtime_minimum_availability' => 'numeric', 'inquiry_key_lifetime' => 'numeric']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        $facilityDeatils = Facility::where('id', $request->facility_id)->first();
        if (!$facilityDeatils) {
            throw new ApiGenericException('Please send valid faclity id', 422);
        }

        $facilityDeatils->realtime_window = (int) $request->realtime_window;
        $facilityDeatils->realtime_minimum_availability = (int) $request->realtime_minimum_availability;
        $facilityDeatils->inquiry_key_lifetime = (int) $request->inquiry_key_lifetime;
        $facilityDeatils->save();
        return $facilityDeatils;
    }


    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     Vikrant
     */
    public function updateInventoryThreshold(Request $request)
    {
        $validator = Validator::make($request->all(), ['facility_id' => 'required|integer', 'inventory_threshold' => 'required|numeric']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        preg_match('/,/', $request->facility_id, $matches);
        if (count($matches)) {
            throw new ApiGenericException('Please provide 1 facility id at a time', 422);
        }

        $facilityDeatils = Facility::where('id', $request->facility_id)->first();
        if (!$facilityDeatils) {
            throw new ApiGenericException('Please send valid faclity id', 422);
        }
        $facilityDeatils->inventory_threshold = (int) $request->inventory_threshold;
        $facilityDeatils->save();

        //update partner availabilities details
        $facilitiesAvailabilities = FacilityAvailability::where('facility_id', $request->facility_id)->whereDate('date', '>=', Carbon::now()->format('Y-m-d'))->get();
        if ($facilitiesAvailabilities) {
            FacilityPartnerAvailabilityCron::where('facility_id', $request->facility_id)->whereDate('date', '>=', Carbon::now()->format('Y-m-d'))->delete();

            foreach ($facilitiesAvailabilities as $key => $value) {
                if (isset($value->availability)) {
                    $partnerData = FacilityPartnerAvailabilityCron::firstOrNew(['facility_id' => $request->facility_id, 'date' => Carbon::parse($value->date)->format('Y-m-d')]);
                    if (!$partnerData->exists) {

                        //update avaliablity when inventory_threshold has value by vikrant
                        $facilitiesInventories = FacilityInventory::where('facility_id', $request->facility_id)->where('date', '=', $value->date)->first();
                        $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($facilitiesInventories->availability, true), json_decode($value->availability), $request->facility_id);
                        if ($newPartnerAvailability) {
                            $partnerData->facility_id  = $request->facility_id;
                            $partnerData->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                            $partnerData->date         = $value->date;
                            $partnerData->save();
                        } else {
                            $partnerData->facility_id  = $request->facility_id;
                            $partnerData->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                            $partnerData->date         = $value->date;
                            $partnerData->save();
                        }
                    }
                }
            }
        }

        return $facilityDeatils;
    }


    public function checkPartnerFacilityCount()
    {
        if (Auth::user()->user_type == '1' || Auth::user()->user_type == '2') {
            /*$partner = User::with('membershipPlans')->where('id', $request->partner_id)->first();
            $totalPlanCount = 0;
            if($partner->membershipPlans){
                foreach ($partner->membershipPlans as $key => $value) {
                    $totalPlanCount += $value->no_of_facility;
                }
            }
            $facilityCount = Facility::where('owner_id', $partner->id)->count();
            if($facilityCount >= $totalPlanCount){
                throw new ApiGenericException('Partner has exceed no of facilities creation.');
            }*/
        }
        //check no of facility create by partner
        if (Auth::user()->user_type == '3') {
            $totalPlanCount = 0;
            if (Auth::user()->membershipPlans) {
                foreach (Auth::user()->membershipPlans as $key => $value) {
                    $totalPlanCount += $value->no_of_facility;
                }
            }
            $facilityCount = Facility::where('owner_id', Auth::user()->id)->count();
            if ($facilityCount >= $totalPlanCount) {
                throw new ApiGenericException('You have exceed your no of garage creation. Please contact to admin.');
            }
            return ['is_accessible' => true];
        }

        if (Auth::user()->user_type == '4') {
            $totalPlanCount = 0;
            $owner_id = Auth::user()->created_by;
            $user = User::where('id', $owner_id)->first();

            if ($user->membershipPlans) {
                foreach ($user->membershipPlans as $key => $value) {
                    $totalPlanCount += $value->no_of_facility;
                }
            }
            $facilityCount = Facility::where('owner_id', $owner_id)->count();
            if ($facilityCount >= $totalPlanCount) {
                throw new ApiGenericException('You have exceed your no of garage creation. Please contact to admin.');
            }

            return ['is_accessible' => true];
        }

        if (Auth::user()->user_type == '12') {
            $totalPlanCount = 0;
            $owner_id = Auth::user()->created_by;
            $user = User::where('id', $owner_id)->first();

            if ($user->membershipPlans) {
                foreach ($user->membershipPlans as $key => $value) {
                    $totalPlanCount += $value->no_of_facility;
                }
            }
            $facilityCount = Facility::where('owner_id', $owner_id)->count();
            if ($facilityCount >= $totalPlanCount) {
                throw new ApiGenericException('You have exceed your no of garage creation. Please contact to admin.');
            }

            return ['is_accessible' => true];
        }
    }

    public function destroyFacilityPhoto(Facility $facility)
    {
        $photo = Photo::where('imageable_id', $facility->id)->first();
        if (!$photo) {
            throw new ApiGenericException('Garage not found.');
        }
        unlink(storage_path("app/" . $photo->image_name));
        return $photo->delete() ? $facility : null;
    }

    public function getFacilityDetails(Request $request)
    {

        $qrcode = basename($request->qrcode);
        if ($qrcode) {
            $decrypt = json_decode(base64_decode($qrcode));
            if (isset($decrypt)) {

                $facility = Facility::where('id', $decrypt->facility_id)->where('active', '1')->first();
                if (!$facility) {
                    throw new ApiGenericException('Facility not Found.');
                }

                $gate = Gate::where('gate', $decrypt->gate)->where('is_active', '1')->first();
                if (!$gate) {
                    throw new ApiGenericException('Gate not Found.');
                }

                $facility['gate_details'] = $gate;

                return $facility;
            } else {
                throw new ApiGenericException('QR Code is not valid');
            }
        } else {
            throw new ApiGenericException('QR code not Found.');
        }
    }

    public function generateFacilityQrCode(Request $request)
    {
        //$url = env('TOUCHLESS_APP_FACILITY_URL');
        $url = env('CUSTOMERAPP_TOUCHLESS_APP_URL');
        $data = [];

        if ($request->is_gated == '1') {
            foreach ($request->qrcode as $key => $val) {
                $ticket_number = base64_encode(json_encode($val));

                $gates = Gate::with('facility')
                    ->where('facility_id', $val['facility_id'])
                    ->where('deleted_at', Null)
                    ->where('is_active', 1)
                    ->orderBy('facility_id', 'desc')
                    ->get();

                if (count($gates) == '0') {
                    throw new ApiGenericException('Please add the gate first and then try to download the QR code.');
                }

                foreach ($gates as $gk => $gateData) {
                    $settings = BrandSetting::where('user_id', $gateData->partner_id)->first();
                    $data[$key][$gk]['gate_name'] = isset($gateData->gate_name) ? $gateData->gate_name . '(' . $gateData->gate_type . ')' : '-';
                    $data[$key][$gk]['facility_type'] = isset($request->is_gated) ? $request->is_gated : '-';
                    $data[$key][$gk]['facility_name'] = isset($gateData->facility->full_name) ? $gateData->facility->full_name : '-';
                    $data[$key][$gk]['qrcode'] = $url . "/" . $request->partner_name . "/" . $request->url_slug . "/" . $ticket_number;
                    $data[$key][$gk]['brand_setting'] = $settings;
                }
            }
            $pdf = (new FacilityQrcode())->generatePdf($data, Pdf::class);
            return $pdf;
        } else if ($request->is_gated == '0') {
            $arr = [];
            $rec = $request->qrcode;
            //  dd($rec,count($rec));
            /*
            if(count($rec) > 1){

            } else {

            }
            */
            foreach ($rec as $key => $val) {
                $qrNumber = $this->checkFacilityQRNumber();
                $data[$key][0] = $val;
                //	$data[$key][1] = $val;
                $data[$key][0]['gate'] = SELF::ENTRY_GATE;
                //	$data[$key][1]['gate'] = SELF::Exit_GATE;
                //   $data[$key][0]['qr_number'] = $qrNumber;
                $data[$key][0]['gate_type'] = '0';
            }
            $result_data = [];
            foreach ($data as $k => $v) {
                foreach ($v as $k1 => $v1) {
                    //$result_data[$k] = $v1;	
                    $ticket_number = base64_encode(json_encode($v1));
                    $facility = Facility::where('id', $v1['facility_id'])
                        ->where('deleted_at', Null)
                        ->where('active', 1)
                        ->first();

                    $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("TRANSIENT_URL", $facility['owner_id']);
                    if ($dynamicReceiptUrl) {
                        $url = $dynamicReceiptUrl->value;
                    }

                    $settings = BrandSetting::where('user_id', $facility['owner_id'])->first();
                    $result_data[$k][$k1]['facility_name'] = isset($facility->full_name) ? $facility->full_name : '-';
                    $result_data[$k][$k1]['facility_type'] = isset($request->is_gated) ? $request->is_gated : '-';
                    $result_data[$k][$k1]['qrcode'] = $url . "/" . $request->partner_name . "/" . $request->url_slug . "/" . $ticket_number;
                    $result_data[$k][$k1]['gate_name'] = ($v1['gate'] == SELF::ENTRY_GATE) ? 'Entry Gate' : 'Exit Gate';
                    //    $result_data[$k][$k1]['qr_number'] = ($v1['qr_number']) ? $v1['qr_number'] : 'NULL';
                    $result_data[$k][$k1]['brand_setting'] = $settings;
                }
            }
            //return $result_data;
            $pdf = (new FacilityQrcode())->generatePdfUngated($result_data, Pdf::class);
            return $pdf;
        } else if ($request->is_gated == '2') {
            $arr = [];
            $rec = $request->qrcode;
            //  dd($rec,count($rec));
            /*
            if(count($rec) > 1){

            } else {

            }
            */
            foreach ($rec as $key => $val) {
                $qrNumber = $this->checkFacilityQRNumber();
                $data[$key][0] = $val;
                //	$data[$key][1] = $val;
                $data[$key][0]['gate'] = SELF::ENTRY_GATE;
                //	$data[$key][1]['gate'] = SELF::Exit_GATE;
                // $data[$key][0]['qr_number'] = $qrNumber;
                $data[$key][0]['gate_type'] = '2';
            }
            $result_data = [];
            foreach ($data as $k => $v) {
                foreach ($v as $k1 => $v1) {
                    //$result_data[$k] = $v1;	
                    $ticket_number = base64_encode(json_encode($v1));
                    $facility = Facility::where('id', $v1['facility_id'])
                        ->where('deleted_at', Null)
                        ->where('active', 1)
                        ->first();

                    $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("TRANSIENT_URL", $facility['owner_id']);
                    if ($dynamicReceiptUrl) {
                        $url = $dynamicReceiptUrl->value;
                    }

                    $settings = BrandSetting::where('user_id', $facility['owner_id'])->first();
                    $result_data[$k][$k1]['facility_name'] = isset($facility->full_name) ? $facility->full_name : '-';
                    $result_data[$k][$k1]['facility_type'] = isset($request->is_gated) ? $request->is_gated : '-';
                    $result_data[$k][$k1]['qrcode'] = $url . "/" . $request->partner_name . "/" . $request->url_slug . "/" . $ticket_number;
                    $result_data[$k][$k1]['gate_name'] = ($v1['gate'] == SELF::ENTRY_GATE) ? 'Entry Gate' : 'Exit Gate';
                    //  $result_data[$k][$k1]['qr_number'] = ($v1['qr_number']) ? $v1['qr_number'] : 'NULL';
                    $result_data[$k][$k1]['brand_setting'] = $settings;
                }
            }
            //return $result_data;
            $pdf = (new FacilityQrcode())->generatePdfUngated($result_data, Pdf::class);
            return $pdf;
        }
    }

    public function generateFacilityQrCodes(Request $request)
    {
        //$url = env('TOUCHLESS_APP_FACILITY_URL');
        $url = env('CUSTOMERAPP_TOUCHLESS_APP_URL');
        $data = [];

        if ($request->is_gated == '1') {
            foreach ($request->qrcode as $key => $val) {
                $ticket_number = base64_encode(json_encode($val));


                $gates = Gate::with('facility')
                    ->where('facility_id', $val['facility_id'])
                    ->where('deleted_at', Null)
                    ->where('is_active', 1)
                    ->orderBy('facility_id', 'desc')
                    ->get();

                if (!$gates) {
                    throw new ApiGenericException('Please add the gate first and then try to download the QR code.');
                }

                foreach ($gates as $gk => $gateData) {
                    $settings = BrandSetting::where('user_id', $gateData->partner_id)->first();
                    $data[$key][$gk]['gate_name'] = isset($gateData->gate_name) ? $gateData->gate_name . '(' . $gateData->gate_type . ')' : '-';
                    $data[$key][$gk]['facility_name'] = isset($gateData->facility->full_name) ? $gateData->facility->full_name : '-';
                    $data[$key][$gk]['qrcode'] = $url . "/" . $request->partner_name . "/" . $request->url_slug . "/" . $ticket_number;
                    $data[$key][$gk]['brand_setting'] = $settings;
                }
            }
            $pdf = (new FacilityQrcode())->generatePdf($data, Pdf::class);
            return $pdf;
        } else if ($request->is_gated == '0') {
            $arr = [];
            $rec = $request->qrcode;

            foreach ($rec as $key => $val) {
                $data[$key][0] = $val;
                //	$data[$key][1] = $val;
                $data[$key][0]['gate'] = SELF::ENTRY_GATE;
                //	$data[$key][1]['gate'] = SELF::Exit_GATE;
            }
            $result_data = [];
            foreach ($data as $k => $v) {
                foreach ($v as $k1 => $v1) {
                    //$result_data[$k] = $v1;	
                    $ticket_number = base64_encode(json_encode($v1));
                    $facility = Facility::where('id', $v1['facility_id'])
                        ->where('deleted_at', Null)
                        ->where('active', 1)
                        ->first();
                    $settings = BrandSetting::where('user_id', $facility['owner_id'])->first();
                    $result_data[$k][$k1]['facility_name'] = isset($facility->full_name) ? $facility->full_name : '-';
                    $result_data[$k][$k1]['qrcode'] = $url . "/" . $request->partner_name . "/" . $request->url_slug . "/" . $ticket_number;
                    $result_data[$k][$k1]['gate_name'] = ($v1['gate'] == SELF::ENTRY_GATE) ? 'Entry Gate' : 'Exit Gate';
                    $result_data[$k][$k1]['brand_setting'] = $settings;
                }
            }
            //return $result_data;
            $pdf = (new FacilityQrcode())->generatePdfUngated($result_data, Pdf::class);
            return $pdf;
        }
    }

    public function facilityList(Request $request)
    {
        $owner_id = 0;
        $userData = User::where('id', Auth::user()->id)->first();
        #07-08-2024 dushyant PIMS-10032
        if (isset($request->business_id) && !empty($request->business_id)) {
            $userData = User::where('id', $request->business_id)->first();
        }
        if ($userData->user_type == '1' || $userData->user_type == '2') {
            if (isset($request->partner_id)) {
                //$facilities = DB::table('facilities')->select('id', 'full_name', 'citation_format','is_gated_facility')->where('owner_id', $request->partner_id)->whereNull('deleted_at')->where('active', 1);
                //$facilities = Facility::with('facilityConfiguration')->select('id', 'short_name', 'full_name', 'citation_format', 'is_gated_facility', 'facility_type_id', 'active')->whereNull('deleted_at')->where('owner_id', $request->partner_id)->where('is_lot', '0');
                $facilities = Facility::with('facilityConfiguration')->select('id', 'short_name', 'full_name', 'citation_format', 'is_gated_facility', 'facility_type_id', 'active')->where('owner_id', $request->partner_id)->whereNull('deleted_at')->where('active', '1');

                $facilities = $facilities->where(function ($query) use ($request) {
                    $query->where('is_lot', '0');
                    if (isset($request->is_lot) && $request->is_lot == '1') {
                        $query->orWhere('is_lot', '1');
                    }
                });

                if (isset($request->rm_id)) {
                    $facility = DB::table('user_facilities')->where('user_id', $request->rm_id)->whereNull('deleted_at')->pluck('facility_id');
                    $facilities = $facilities->where(function ($query) use ($facility) {
                        $query->whereIn('id', $facility);
                    });
                }
                if ($request->is_event == '1') {
                    $facility_ids = Facility::whereNull('deleted_at')->where('active', 1)->pluck('id');
                    $event_facility = DB::table('event_facility')->whereIn('facility_id', $facility_ids)->pluck('facility_id');
                    $facilities = $facilities->where(function ($query) use ($event_facility) {
                        $query->whereIn('id', $event_facility);
                    });
                }

                $facilities = $facilities->orderBy('is_lot', 'ASC')->get();
            } else {
                //  $facilities = DB::table('facilities')->select('id', 'full_name', 'citation_format','is_gated_facility')->whereNull('deleted_at')->where('active', 1)->orderBy('full_name', 'ASC')->get();
                $facilities = Facility::with('facilityConfiguration')->select('id', 'short_name', 'full_name', 'citation_format', 'is_gated_facility', 'facility_type_id', 'active')->whereNull('deleted_at')->where('active', 1)->where('is_lot', '0')->orderBy('facilities.full_name', 'ASC')->get();
            }
        } else {

            $facility = [];
            if ($userData->user_type == '3') {
                $owner_id = $userData->id;
                $rm_id = $request->rm_id;
            } else if ($userData->user_type == '4') {
                $owner_id = $userData->created_by;
                $admin_partner_id = Auth::user()->created_by;
                if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                    $owner_id = $request->partner_id;
                } else {
                    $owner_id = $userData->created_by;
                }
                if (isset($request->business_id) && !empty($request->business_id)) {
                    $facility = DB::table('user_facilities')->where('user_id', $userData->id)->whereNull('deleted_at')->pluck('facility_id');
                } else {
                    $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                }
            } else if ($userData->user_type == '12') {
                $owner_id = $userData->created_by;
                if (isset($request->business_id) && !empty($request->business_id)) {
                    $facility = DB::table('user_facilities')->where('user_id', $userData->id)->whereNull('deleted_at')->pluck('facility_id');
                } else {
                    $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                }
            } else if ($userData->user_type == '10') {
                $owner_id = $userData->created_by;
                if (isset($request->business_id) && !empty($request->business_id)) {
                    $facility = DB::table('business_facility_policy')->where('business_id', $userData->business_id)->pluck('facility_id');
                } else {
                    $facility = DB::table('business_facility_policy')->where('business_id', $userData->business_id)->pluck('facility_id');
                }
            } else if ($userData->user_type == '8') {
                $owner_id = $userData->created_by;
                if (isset($request->business_id) && !empty($request->business_id)) {
                    //   $facility = DB::table('business_facility_policy')->where('business_id', $userData->business_id)->pluck('facility_id');
                    $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                } else {
                    $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                    //    $facility = DB::table('business_facility_policy')->where('business_id', $userData->business_id)->pluck('facility_id');
                }
            }
            //$facilities = DB::table('facilities')->where('owner_id', $owner_id)->whereNull('deleted_at')->select('id', 'full_name', 'citation_format',"is_gated_facility")->where('active', 1);
            $facilities = Facility::with('facilityConfiguration')->select('id', 'short_name', 'full_name', 'citation_format', 'is_gated_facility', 'facility_type_id', 'active')->where('owner_id', $owner_id)->whereNull('deleted_at')->where('active', '1');

            $facilities = $facilities->where(function ($query) use ($request) {
                $query->where('is_lot', '0');
                if (isset($request->is_lot) && $request->is_lot == '1') {
                    $query->orWhere('is_lot', '1');
                }
            });

            if ($userData->user_type == '4') {
                if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                } else {
                    $facilities = $facilities->where(function ($query) use ($facility) {
                        $query->whereIn('id', $facility);
                    });
                }
            }
            if ($userData->user_type == '12') {

                $facilities = $facilities->where(function ($query) use ($facility) {
                    $query->whereIn('id', $facility);
                });
            }

            if ($userData->user_type == '10') {

                $facilities = $facilities->where(function ($query) use ($facility) {
                    $query->whereIn('id', $facility);
                });
            }

            if ($userData->user_type == '8') {

                $facilities = $facilities->where(function ($query) use ($facility) {
                    $query->whereIn('id', $facility);
                });
            }

            if ($request->is_event == '1') {
                $facility_ids = Facility::whereNull('deleted_at')->where('active', 1)->pluck('id');
                $event_facility = DB::table('event_facility')->whereIn('facility_id', $facility_ids)->pluck('facility_id');
                $facilities = $facilities->where(function ($query) use ($event_facility) {
                    $query->whereIn('id', $event_facility);
                });
            }
            if ($userData->user_type == '3' && (isset($request->rm_id))) {
                $facility = DB::table('user_facilities')->where('user_id', $request->rm_id)->whereNull('deleted_at')->pluck('facility_id');
                $facilities = $facilities->where(function ($query) use ($facility) {
                    $query->whereIn('id', $facility);
                });
            }

            $facilities = $facilities->orderBy('is_lot', 'ASC')->get();
        }
        return ['facilities' => $facilities];
    }


    public function FacilityListByBusiness($id)
    {
        //PIMS-12614 06-02-2025 jattin
        if (Auth::user()->user_type == '8') {
            $clerk_facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->pluck('facility_id');
            if (!$clerk_facility) {
                throw new ApiGenericException('Facility not Found.');
            }
            $facilities = Facility::select('id', 'short_name', 'full_name', 'facility_type_id', 'active')->whereIn('id', $clerk_facility)->groupBy('id')->where('active', '1')->orderBy('id', 'Desc')->get();
        } else {
            $business_facility = DB::table('business_facility_policy')->where('business_id', $id)->pluck('facility_id');
            if (!$business_facility) {
                throw new ApiGenericException('Facility not Found.');
            }
            $facilities = Facility::select('id', 'short_name', 'full_name', 'facility_type_id', 'active')->whereIn('id', $business_facility)->groupBy('id')->where('active', '1')->orderBy('id', 'Desc')->get();
        }
        return ['facilities' => $facilities];
    }





    public function getTimezones()
    {
        return DB::table('timezones')->orderBy('timezone', 'ASC')->get();
    }

    public function getFacilityPermitType($partner_id, $facility)
    {
        $permitFacilityMapping = PermitTypeFacilityMapping::where('facility_id', $facility)->pluck('permit_type_id');
        if ($permitFacilityMapping->count() > 0) {
            $permitType = PermitTypeMaster::select('id', 'permit_type_name')->where('partner_id', $partner_id)->where('is_status', 1)->wherein('id', $permitFacilityMapping)->get();
        } else {
            $permitType = PermitTypeMaster::select('id', 'permit_type_name')->where('partner_id', $partner_id)->where('is_status', 1)->get();
        }
        return $permitType;
    }

    public function getFacilities($id)
    {
        $facility_ids = UserFacility::where('user_id', $id)->whereNull('deleted_at')->pluck('facility_id');
        if ($facility_ids) {
            $facilities = Facility::select('id', 'short_name', 'full_name', 'facility_type_id', 'active', 'user_receipt_url', 'is_receipt_enabled', 'user_receipt_url', 'generic_receipt_url', 'entrance_location', 'grace_period_minute')->with(['FacilityPaymentDetails', 'geolocations', 'facilityConfiguration'])->whereIn('id', $facility_ids)->where('active', 1)->orderBy('id', 'Desc')->get();
        } else {
            throw new ApiGenericException('Facility Not Found');
        }

        if (!$facilities) {
            throw new ApiGenericException('Facility Not Found');
        }

        foreach ($facilities as $key => $val) {
            //if ($val->facilityConfiguration->is_sbm_event_qrcode == '1') {
            $val->parkingFacilityIdentifier = $val->facilityConfiguration->product_identifier_id;
            $val->productIdentifier  = $val->facilityConfiguration->product_identifier;
            //}
        }

        return $facilities;
    }

    // update facility payment details
    public function updatePaymentdetails(Request $request, Facility $facility)
    {
        $this->log->info("Update Facility Payment Log Request :  " . json_encode($request->all()));
        $this->log->info("Update Facility Request By: " . Auth::user()->id);
        //update or save facility payment details
        if (isset($request->facility_payment_type_id) && !empty($request->facility_payment_type_id)) {
            $checkpaymentDetails = FacilityPaymentDetail::where('facility_id', $facility->id)->first();
            $paymentDetails['facility_id'] = $facility->id;
            $paymentDetails['facility_payment_type_id'] = $request->facility_payment_type_id;

            // Vijay :10-10-2024 : Update Partner Payment Gateway
            // $this->updatePartnerGatewayDetils($request, $facility->id);

            if ($request->facility_payment_type_id == config('parkengage.PLANET')) {
                // planet
                if ($request->planet_merchant_id) {
                    $paymentDetails['planet_merchant_id'] = isset($request->planet_merchant_id) ? $request->planet_merchant_id : '';
                    $request->request->remove('planet_merchant_id');
                }
                if ($request->planet_validation_code) {
                    $paymentDetails['planet_validation_code'] = isset($request->planet_validation_code) ? $request->planet_validation_code : '';
                    $request->request->remove('planet_validation_code');
                }
                if ($request->planet_payment_url) {
                    $paymentDetails['planet_payment_url'] = isset($request->planet_payment_url) ? $request->planet_payment_url : '';
                    $request->request->remove('planet_payment_url');
                }
                if ($request->planet_refund_url) {
                    $paymentDetails['planet_refund_url'] = isset($request->planet_refund_url) ? $request->planet_refund_url : '';
                    $request->request->remove('planet_refund_url');
                }

                $paymentDetails['planet_payment_env'] = isset($request->planet_payment_env) ? $request->planet_payment_env : config('parkengage.PLANET_PAYMENT_ENV');
                $request->request->remove('planet_payment_env');



                // datatrans
                if ($request->data_trans_url) {
                    $paymentDetails['data_trans_url'] = isset($request->data_trans_url) ? $request->data_trans_url : '';
                    $request->request->remove('data_trans_url');
                }
                if ($request->data_trans_username) {
                    $paymentDetails['data_trans_username'] = isset($request->data_trans_username) ? $request->data_trans_username : '';
                    $request->request->remove('data_trans_username');
                }
                if ($request->data_trans_password) {
                    $paymentDetails['data_trans_password'] = isset($request->data_trans_password) ? $request->data_trans_password : '';
                    $request->request->remove('data_trans_password');
                }

                $paymentDetails['data_trans_payment_env'] = isset($request->data_trans_payment_env) ? $request->data_trans_payment_env : config('parkengage.PLANET_PAYMENT_ENV');
                $request->request->remove('data_trans_payment_env');

                if ($request->data_trans_username_web) {
                    $paymentDetails['data_trans_username_web'] = isset($request->data_trans_username_web) ? $request->data_trans_username_web : '';
                    $request->request->remove('data_trans_username_web');
                }

                if ($request->data_trans_password_web) {
                    $paymentDetails['data_trans_password_web'] = isset($request->data_trans_password_web) ? $request->data_trans_password_web : '';
                    $request->request->remove('data_trans_password_web');
                }

                if ($request->dataTrans_success_url) {
                    $paymentDetails['dataTrans_success_url'] = isset($request->dataTrans_success_url) ? $request->dataTrans_success_url : '';
                    $request->request->remove('dataTrans_success_url');
                }

                if ($request->dataTrans_cancel_url) {
                    $paymentDetails['dataTrans_cancel_url'] = isset($request->dataTrans_cancel_url) ? $request->dataTrans_cancel_url : '';
                    $request->request->remove('dataTrans_cancel_url');
                }

                if ($request->dataTrans_error_url) {
                    $paymentDetails['dataTrans_error_url'] = isset($request->dataTrans_error_url) ? $request->dataTrans_error_url : '';
                    $request->request->remove('dataTrans_error_url');
                }
            }

            if ($request->facility_payment_type_id == config('parkengage.DATACAP')) {
                // datacap
                if ($request->datacap_mid) {
                    $paymentDetails['datacap_mid'] = isset($request->datacap_mid) ? $request->datacap_mid : '';
                    $request->request->remove('datacap_mid');
                }

                if ($request->datacap_ecommerce_mid) {
                    $paymentDetails['datacap_ecommerce_mid'] = isset($request->datacap_ecommerce_mid) ? $request->datacap_ecommerce_mid : '';
                    $request->request->remove('datacap_ecommerce_mid');
                }

                if ($request->datacap_token) {
                    $paymentDetails['datacap_token'] = isset($request->datacap_token) ? $request->datacap_token : '';
                    $request->request->remove('datacap_token');
                }

                if ($request->datacap_ecommerce_token) {
                    $paymentDetails['datacap_ecommerce_token'] = isset($request->datacap_ecommerce_token) ? $request->datacap_ecommerce_token : '';
                    $request->request->remove('datacap_ecommerce_token');
                }

                if ($request->datacap_authonly_url) {
                    $paymentDetails['datacap_authonly_url'] = isset($request->datacap_authonly_url) ? $request->datacap_authonly_url : '';
                    $request->request->remove('datacap_authonly_url');
                }

                if ($request->datacap_preauth_url) {
                    $paymentDetails['datacap_preauth_url'] = isset($request->datacap_preauth_url) ? $request->datacap_preauth_url : '';
                    $request->request->remove('datacap_preauth_url');
                }

                if ($request->datacap_otu_url) {
                    $paymentDetails['datacap_otu_url'] = isset($request->datacap_otu_url) ? $request->datacap_otu_url : '';
                    $request->request->remove('datacap_otu_url');
                }

                if ($request->datacap_script_url) {
                    $paymentDetails['datacap_script_url'] = isset($request->datacap_script_url) ? $request->datacap_script_url : '';
                    $request->request->remove('datacap_script_url');
                }

                if ($request->datacap_refund_url) {
                    $paymentDetails['datacap_refund_url'] = isset($request->datacap_refund_url) ? $request->datacap_refund_url : '';
                    $request->request->remove('datacap_refund_url');
                }

                if ($request->datacap_ios_mid) {
                    $paymentDetails['datacap_ios_mid'] = isset($request->datacap_ios_mid) ? $request->datacap_ios_mid : '';
                    $request->request->remove('datacap_ios_mid');
                }

                if ($request->wallet_datacap_mid) {
                    $paymentDetails['wallet_datacap_mid'] = isset($request->wallet_datacap_mid) ? $request->wallet_datacap_mid : '';
                    $request->request->remove('wallet_datacap_mid');
                }

                if ($request->wallet_datacap_token) {
                    $paymentDetails['wallet_datacap_token'] = isset($request->wallet_datacap_token) ? $request->wallet_datacap_token : '';
                    $request->request->remove('wallet_datacap_token');
                }

                if ($request->wallet_datacap_ios_merchant) {
                    $paymentDetails['wallet_datacap_ios_merchant'] = isset($request->wallet_datacap_ios_merchant) ? $request->wallet_datacap_ios_merchant : '';
                    $request->request->remove('wallet_datacap_ios_merchant');
                }

                if ($request->wallet_datacap_gpay_merchant) {
                    $paymentDetails['wallet_datacap_gpay_merchant'] = isset($request->wallet_datacap_gpay_merchant) ? $request->wallet_datacap_gpay_merchant : '';
                    $request->request->remove('wallet_datacap_gpay_merchant');
                }

                $paymentDetails['datacap_payment_env'] = isset($request->datacap_payment_env) ? $request->datacap_payment_env : config('parkengage.DATACAP_PAYMENT_ENV');
                $request->request->remove('datacap_payment_env');
            }

            if ($request->facility_payment_type_id == config('parkengage.HEARTLAND')) {
                // datacap
                if ($request->heartland_mid) {
                    $paymentDetails['heartland_mid'] = isset($request->heartland_mid) ? $request->heartland_mid : '';
                    $request->request->remove('heartland_mid');
                }

                if ($request->heartland_service_url) {
                    $paymentDetails['heartland_service_url'] = isset($request->heartland_service_url) ? $request->heartland_service_url : '';
                    $request->request->remove('heartland_service_url');
                }

                if ($request->heartland_secret_api_key) {
                    $paymentDetails['heartland_secret_api_key'] = isset($request->heartland_secret_api_key) ? $request->heartland_secret_api_key : '';
                    $request->request->remove('heartland_secret_api_key');
                }

                if ($request->heartland_developer_id) {
                    $paymentDetails['heartland_developer_id'] = isset($request->heartland_developer_id) ? $request->heartland_developer_id : '';
                    $request->request->remove('heartland_developer_id');
                }

                if ($request->heartland_version_number) {
                    $paymentDetails['heartland_version_number'] = isset($request->heartland_version_number) ? $request->heartland_version_number : '';
                    $request->request->remove('heartland_version_number');
                }

                if ($request->heartland_public_api_key) {
                    $paymentDetails['heartland_public_api_key'] = isset($request->heartland_public_api_key) ? $request->heartland_public_api_key : '';
                    $request->request->remove('heartland_public_api_key');
                }

                if ($request->heartland_single_token_slug) {
                    $paymentDetails['heartland_single_token_slug'] = isset($request->heartland_single_token_slug) ? $request->heartland_single_token_slug : '';
                    $request->request->remove('heartland_single_token_slug');
                }

                if ($request->heartland_merchant_id) {
                    $paymentDetails['heartland_merchant_id'] = isset($request->heartland_merchant_id) ? $request->heartland_merchant_id : '';
                    $request->request->remove('heartland_merchant_id');
                }

                if ($request->heartland_merchant_name) {
                    $paymentDetails['heartland_merchant_name'] = isset($request->heartland_merchant_name) ? $request->heartland_merchant_name : '';
                    $request->request->remove('heartland_merchant_name');
                }

                if ($request->heartland_applepay_identifier) {
                    $paymentDetails['heartland_applepay_identifier'] = isset($request->heartland_applepay_identifier) ? $request->heartland_applepay_identifier : '';
                    $request->request->remove('heartland_applepay_identifier');
                }

                if ($request->heartland_gpay_merchant) {
                    $paymentDetails['heartland_gpay_merchant'] = isset($request->heartland_gpay_merchant) ? $request->heartland_gpay_merchant : '';
                    $request->request->remove('heartland_gpay_merchant');
                }
                //    
                if ($request->exists('heartland_device_id')) {
                    $paymentDetails['heartland_device_id'] = !empty($request->heartland_device_id) ? $request->heartland_device_id : '';
                    $request->request->remove('heartland_device_id');
                }

                if ($request->exists('heartland_site_id')) {
                    $paymentDetails['heartland_site_id'] = !empty($request->heartland_site_id) ? $request->heartland_site_id : '';
                    $request->request->remove('heartland_site_id');
                }

                if ($request->exists('heartland_website_id')) {
                    $paymentDetails['heartland_website_id'] = !empty($request->heartland_website_id) ? $request->heartland_website_id : '';
                    $request->request->remove('heartland_website_id');
                }

                $paymentDetails['heartland_payment_env'] = isset($request->heartland_payment_env) ? $request->heartland_payment_env : config('parkengage.HEARTLAND_PAYMENT_ENV');
                $request->request->remove('heartland_payment_env');
            }

            if ($request->facility_payment_type_id == config('parkengage.FISERV')) {
                // fiserv
                if ($request->fiserv_mid) {
                    $paymentDetails['fiserv_mid'] = isset($request->fiserv_mid) ? $request->fiserv_mid : '';
                    $request->request->remove('fiserv_mid');
                }
                if ($request->fiserv_server_url) {
                    $paymentDetails['fiserv_server_url'] = isset($request->fiserv_server_url) ? $request->fiserv_server_url : '';
                    $request->request->remove('fiserv_server_url');
                }
                if ($request->fiserv_iframe_url) {
                    $paymentDetails['fiserv_iframe_url'] = isset($request->fiserv_iframe_url) ? $request->fiserv_iframe_url : '';
                    $request->request->remove('fiserv_iframe_url');
                }
                if ($request->fiserv_auth_token) {
                    $paymentDetails['fiserv_auth_token'] = isset($request->fiserv_auth_token) ? $request->fiserv_auth_token : '';
                    $request->request->remove('fiserv_auth_token');
                }
            }

            if (isset($checkpaymentDetails) && !empty($checkpaymentDetails)) {
                $facilityPaymentDetails = FacilityPaymentDetail::find($checkpaymentDetails->id)->update($paymentDetails);
            } else {
                $facilityPaymentDetails = FacilityPaymentDetail::create($paymentDetails);
            }

            // Vijay :10-10-2024 : Update Partner Payment Gateway
            $this->updatePartnerGatewayDetils($request, $facility->id);

            return Facility::with(
                'facilityfee',
                'facilityType',
                'geolocations',
                'photos',
                'features',
                'hoursOfOperation',
                'neighborhood',
                'rates',
                'FacilityPaymentDetails',
                'faciltyBrandSetting',
                'facilityConfiguration'
            )->find($facility->id);
        } else {
            throw new ApiGenericException('Facility Not Found');
        }
    }

    protected function updatePartnerGatewayDetils(Request $request, $facilityId)
    {
        $FacilityPaymentDetail = FacilityPaymentDetail::where("facility_id", $facilityId)->first();
        $Facility = Facility::find($facilityId);
        $merchantMID = '';
        $PartnerPaymentGateway = '';
        // Update partner gateway info
        if ($FacilityPaymentDetail) {
            if ($request->facility_payment_type_id == config('parkengage.PLANET')) {            // Planet 
                $PartnerPaymentGateway = PartnerPaymentGateway::where(['payment_mid' => $FacilityPaymentDetail->planet_merchant_id, 'partner_id' => $Facility->owner_id])->first();
                $merchantMID = $FacilityPaymentDetail->planet_merchant_id;
            } else if ($request->facility_payment_type_id == config('parkengage.DATACAP')) {   // Datacap     
                $PartnerPaymentGateway = PartnerPaymentGateway::where(['payment_mid' => $FacilityPaymentDetail->datacap_ecommerce_mid, 'partner_id' => $Facility->owner_id])->first();
                $merchantMID = $FacilityPaymentDetail->datacap_ecommerce_mid;
            } else if ($request->facility_payment_type_id == config('parkengage.HEARTLAND')) {   // Heartland
                $PartnerPaymentGateway = PartnerPaymentGateway::where(['payment_mid' => $FacilityPaymentDetail->heartland_mid, 'partner_id' => $Facility->owner_id])->first();
                $merchantMID = $FacilityPaymentDetail->heartland_mid;
            } else if ($request->facility_payment_type_id == config('parkengage.FISERV')) {   // Fiserv
                $PartnerPaymentGateway = PartnerPaymentGateway::where(['payment_mid' => $FacilityPaymentDetail->fiserv_mid, 'partner_id' => $Facility->owner_id])->first();
                $merchantMID = $FacilityPaymentDetail->fiserv_mid;
            }

            $facilities = [];

            if ($PartnerPaymentGateway) {
                $facilities = $PartnerPaymentGateway->facility_ids;

                if (!$this->isKeyExists($facilities, $facilityId)) {
                    array_push($facilities, $facilityId);
                }

                // $PartnerPaymentGateway->partner_id               = $Facility->owner_id;
                $PartnerPaymentGateway->payment_mid                 = $merchantMID;
                $PartnerPaymentGateway->facility_ids                = $facilities;
                $PartnerPaymentGateway->facility_payment_type_id    = $request->facility_payment_type_id;
                $PartnerPaymentGateway->save();
            } else {
                array_push($facilities, $facilityId);
                $PaymentGateway  =  new PartnerPaymentGateway();
                $PaymentGateway->partner_id                  = $Facility->owner_id;
                $PaymentGateway->payment_mid                 = $merchantMID;
                $PaymentGateway->facility_ids                = $facilities;
                $PaymentGateway->facility_payment_type_id    = $request->facility_payment_type_id;
                $PaymentGateway->save();
            }

            // Remove facility ID from other payment gateways if any  
            $this->removeFacilityFromOtherGateways($request, $Facility);
        } else {
            $this->log->info("Update Integration Tab But Facility Payment Details not found  " . Auth::user()->id);
        }
    }

    public function isKeyExists($array, $key)
    {
        if (in_array($key, $array))
            return true;
        else
            return false;
    }
    protected function removeFacilityFromOtherGateways(Request $request, $Facility)
    {
        $PartnerPaymentGateway = PartnerPaymentGateway::where(['partner_id' => $Facility->owner_id])->first();

        if ($request->facility_payment_type_id == config('parkengage.PLANET')) {            // Planet 
            $PartnerPaymentGateways = PartnerPaymentGateway::where(['partner_id' => $Facility->owner_id])->whereIn('facility_payment_type_id', [2, 3, 4])->get();
        } else if ($request->facility_payment_type_id == config('parkengage.DATACAP')) {   // Datacap
            $PartnerPaymentGateways = PartnerPaymentGateway::where(['partner_id' => $Facility->owner_id])->whereIn('facility_payment_type_id', [1, 3, 4])->get();
        } else if ($request->facility_payment_type_id == config('parkengage.HEARTLAND')) {   // Heartland
            $PartnerPaymentGateways = PartnerPaymentGateway::where(['partner_id' => $Facility->owner_id])->whereIn('facility_payment_type_id', [1, 2, 3])->get();
        } else if ($request->facility_payment_type_id == config('parkengage.FISERV')) {   // Fiserv
            $PartnerPaymentGateways = PartnerPaymentGateway::where(['partner_id' => $Facility->owner_id])->whereIn('facility_payment_type_id', [1, 2, 4])->get();
        }

        if ($PartnerPaymentGateways) {
            foreach ($PartnerPaymentGateways as $key => $paymentGateway) {
                $PartnerPaymentGateway = PartnerPaymentGateway::find($paymentGateway->id);
                $facilities = $PartnerPaymentGateway->facility_ids;
                if ($this->isKeyExists($facilities, $Facility->id)) {
                    $index = array_search($Facility->id, $facilities);       // find index                    
                    unset($facilities[$index]);                             // delete value from index
                    $facilities = array_values($facilities);                // reindexes the array so that keys are numeric and sequential 
                    $PartnerPaymentGateway->facility_ids = $facilities;    // update value 
                    $PartnerPaymentGateway->save();                         // save value                     
                }
            }
        }
    }

    protected function checkFacilityQRNumber()
    {
        $qrNumber = rand(1000, 9999) . rand(1000, 9999);
        $isExist = FacilityQrcode::where('qrcode_number', $qrNumber)->first();
        if ($isExist) {

            $qrNumber = $this->checkFacilityQRNumber();
            $isExist = FacilityQrcode::where('ticket_number', $qrNumber)->first();
            if ($isExist) {
                $qrNumber = $this->checkFacilityQRNumber();
            }
        }
        return $qrNumber;
    }

    //Fee audit trail function start here
    private function storeFeeAuditTrail(Facility $facility, Request $request)
    {
        $additionalFee = $request->additonal_fee; // Keep the typo as requested
        if (isset($request->additonal_fee) && !empty($request->additonal_fee)) {
            $request->merge([
                'additional_fee' => $additionalFee
            ]);
        }

        $feeFields = [
            'tax_rate' => ['new_key' => 'tax_fee', 'old_key' => 'old_tax_fee', 'type_key' => 'tax_type', 'type_field' => 'tax_rate_type'],
            'citation_processing_fee' => ['new_key' => 'citation_processing_fee', 'old_key' => 'old_citation_processing_fee'],
            'drive_up_processing_fee' => ['new_key' => 'drive_up_processing_fee', 'old_key' => 'old_drive_up_processing_fee'],
            'permit_processing_fee' => ['new_key' => 'permit_processing_fee', 'old_key' => 'old_permit_processing_fee'],
            'pass_processing_fee' => ['new_key' => 'pass_processing_fee', 'old_key' => 'old_pass_processing_fee'],
            'additional_fee' => ['new_key' => 'additional_fee', 'old_key' => 'old_additional_fee'],
            'surcharge_fee' => ['new_key' => 'surcharge_fee', 'old_key' => 'old_surcharge_fee'],
            'oversize_fee' => ['new_key' => 'oversize_fee', 'old_key' => 'old_oversize_fee'],
        ];

        $auditData = [
            'facility_id' => $facility->id, // Add facility_id
            'modified_by' => Auth::user()->id ?? 0,
            'reservation_processing_fee' => 0.00,
            'old_reservation_processing_fee' => 0.00,
            'tax_fee' => 0.00,
            'old_tax_fee' => 0.00,
            'tax_type' => 0,
            'old_tax_type' => 0,
            'citation_processing_fee' => 0.00,
            'old_citation_processing_fee' => 0.00,
            'drive_up_processing_fee' => 0.00,
            'old_drive_up_processing_fee' => 0.00,
            'permit_processing_fee' => 0.00,
            'old_permit_processing_fee' => 0.00,
            'additional_fee' => null,
            'old_additional_fee' => null,
            'surcharge_fee' => null,
            'old_surcharge_fee' => null,
            'oversize_fee' => null,
            'old_oversize_fee' => null,
        ];

        $hasChanges = false;

        foreach ($feeFields as $field => $keys) {
            // Handle misspelled field in Facility model
            if ($field === 'additional_fee') {
                $newValue = $request->has($field) ? $request->input($field) : ($facility->additonal_fee ?? null);
                $oldValue = $facility->additonal_fee ?? null;
            } else if ($field === 'surcharge_fee') {
                $newValue = $request->has($field) ? $request->input($field) : ($facility->surcharge_fee ?? null);
                $oldValue = $facility->surcharge_fee ?? null;
            } else {
                $newValue = $request->has($field) ? $request->input($field) : ($facility->$field ?? null);
                $oldValue = $facility->$field ?? null;
            }

            // Assign values to audit data
            $auditData[$keys['new_key']] = $newValue ?? ($keys['new_key'] === 'tax_fee' ? 0.00 : null);
            $auditData[$keys['old_key']] = $oldValue ?? ($keys['old_key'] === 'old_tax_fee' ? 0.00 : null);

            // Compare values, handling null explicitly
            if ($newValue != $oldValue) {
                $hasChanges = true;
            }

            if (isset($keys['type_key'])) {
                $newType = $request->has($keys['type_field']) ? $request->input($keys['type_field']) : ($facility->tax_rate_type ?? 0);
                $oldType = $facility->tax_rate_type ?? 0;

                $auditData[$keys['type_key']] = $newType ?? 0;
                $auditData['old_' . $keys['type_key']] = $oldType ?? 0;

                if ($newType != $oldType) {
                    $hasChanges = true;
                }
            }
        }

        if ($hasChanges) {
            DB::table('fee_audit_trail')->insert($auditData);
        }
    }

    public function updateInventoyFlags(Request $request, Facility $facility)
    {
        $this->log->info("Update Facility Log Request :  " . json_encode($request->all()));
        $this->log->info("Update Facility Request By:  " . Auth::user()->id);
        $this->log->info("Log Request Facility :  " . json_encode($facility));

        $this->validate($request, [
            'facility_id'   => 'required|numeric|min:1',
            'partner_id'    => 'required|numeric|min:1'
        ]);
        if ($request->facility_configuration) {
            $facilityConfiguration = FacilityConfiguration::where('facility_id', $facility->id)->first();

            if ($facilityConfiguration) {
                FacilityConfiguration::where('facility_id', $facility->id)
                    ->update([
                        'is_inventory_check' => isset($request->facility_configuration['is_inventory_check']) ? $request->facility_configuration['is_inventory_check'] : $facilityConfiguration->is_inventory_check,
                    ]);
            }
        }

        $facilityData = Facility::with(
            'facilityConfiguration'
        )->find($facility->id);


        return $facilityData;
    }

    public function updateThreshold(Request $request, $facility_id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'threshold_type' => 'required|integer',
                'threshold_onlevel' => 'required|integer',
                'threshold_offlevel' => 'required|integer',
                'is_lot_full' => 'required|integer'
            ]);
            if ($validator->fails()) {
                foreach ($validator->errors()->getMessages() as $key => $error) {
                    $errors = $error[0];
                }
                throw new ApiGenericException($errors, 422);
            } else {
                $facility = Facility::with("facilityConfiguration")->find($facility_id);
                //if((isset($request->threshold_type) && ($request->threshold_type == 0 || $request->threshold_type == '0'))){
                //$slotStatus = QueryBuilder::getSlotStatus($facility_id,$facility->capacity,$request->threshold_onlevel,$request->threshold_offlevel);
                //$request->merge(['is_lot_full' => $slotStatus]);
                //}

                $thresHoldData = FacilityConfiguration::updateOrCreate(
                    ['facility_id' => $facility_id],
                    ['threshold_type' => $request->threshold_type, 'threshold_onlevel' => $request->threshold_onlevel, 'threshold_offlevel' => $request->threshold_offlevel, 'is_lot_full' => $request->is_lot_full]
                );

                if (!empty($thresHoldData)) {
                    $slot = FacilitySlot::select("slot")->where("facility_id", $facility_id)->orderBy("id", "DESC")->first();
                    $availSlot = $slot->slot;
                    if ((int)$availSlot >= (int)$thresHoldData->threshold_onlevel) {
                        //UPBL-87

                        if ($thresHoldData->threshold_type == "0" || $thresHoldData->threshold_type == 0) {
                            $slotStatus = QueryBuilder::getSlotStatus($facility_id, $facility->capacity, $request->threshold_onlevel, $request->threshold_offlevel);
                            if (($slotStatus != 2) && $slotStatus != '2') {
                                $thresHoldData->update(['is_lot_full' => $slotStatus]);
                            }
                            $updateSlot = FacilityConfiguration::where('facility_id', $facility_id)->update(['is_lot_full' => 1]);
                            $params = ['facility_id' => $facility_id, 'is_full' => 'true'];
                            \Log::info("Request Parkengage SetLotStatus Service : SetLotStatus command about to run facility { $facility_id} params" . json_encode($params));
                            $command_response = ParkengageGateApi::SetLotStatus($params, $facility->adam_host);
                            \Log::info("Response Parkengage SetLotStatus Service : SetLotStatus command complete facility { $facility_id} response" . json_encode($command_response));
                        }
                        //UPBL-87
                    } elseif ($thresHoldData->threshold_type == 0 && ((int)$availSlot <  (int)$thresHoldData->threshold_onlevel) && ((int)$availSlot >  (int)$thresHoldData->threshold_offlevel)) {
                        return $thresHoldData;
                    } elseif ((int)$availSlot < (int)$thresHoldData->threshold_offlevel) {
                        //UPBL-87
                        if ($thresHoldData->threshold_type == "0" || $thresHoldData->threshold_type == 0) {
                            if (($slotStatus != 2) && $slotStatus != '2') {
                                $thresHoldData->update(['is_lot_full' => $slotStatus]);
                            }
                            $updateSlot = FacilityConfiguration::where('facility_id', $facility_id)->update(['is_lot_full' => 0]);
                            $params = ['facility_id' =>  $facility_id, 'is_full' => 'false'];
                            \Log::info("Request Parkengage SetLotStatus Service : SetLotStatus command about to run facility { $facility_id} params" . json_encode($params));
                            $command_response = ParkengageGateApi::SetLotStatus($params, $facility->adam_host);
                            \Log::info("Response Parkengage SetLotStatus Service : SetLotStatus command complete facility { $facility_id} response" . json_encode($command_response));
                        }
                        //UPBL-87
                    }
                    if ($thresHoldData->threshold_type == "1" || $thresHoldData->threshold_type == 1) {
                        $updateSlot = FacilityConfiguration::where('facility_id', $facility_id)->update(['is_lot_full' => $request->is_lot_full]);
                        if ($request->is_lot_full == "0" || $request->is_lot_full == 0) {
                            $is_full = 'false';
                        } else {
                            $is_full = 'true';
                        }

                        $params = ['facility_id' => $facility_id, 'is_full' => $is_full];
                        \Log::info("Request Parkengage SetLotStatus Service : SetLotStatus command about to run facility { $facility_id} params" . json_encode($params));
                        $command_response = ParkengageGateApi::SetLotStatus($params, $facility->adam_host);
                        \Log::info("Response Parkengage SetLotStatus Service : SetLotStatus command complete facility { $facility_id} response" . json_encode($command_response));
                    }

                    return $thresHoldData;
                } else {
                    throw new ApiGenericException('Something went wrong');
                }
            }
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            return $msg;
        }
    }


    public function getVehicleList(Request $request, $facility_id)
    {
        try {
            //$vechiles = LPRFeed::leftJoin('tickets', 'lpr_feeds.license_plate', '=', 'tickets.license_plate')
            //->where('lpr_feeds.facility_id',$facility_id)->where('lpr_feeds.event_type',"entry")->whereIn('lpr_feeds.ticket_status',['0','1'])->orderBy('lpr_feeds.id', 'desc')->paginate(10);
            // $vechiles = DB::table('lpr_feeds')
            // ->leftJoin('tickets', 'lpr_feeds.license_plate', '=', 'tickets.license_plate')
            // ->where('lpr_feeds.event_type', 'entry')
            // ->where('lpr_feeds.facility_id', $facility_id)
            // ->whereIn('lpr_feeds.ticket_status', ['0', '1'])
            // ->whereRaw('lpr_feeds.event_created_at = (
            //     SELECT MIN(l2.event_created_at)
            //     FROM lpr_feeds l2
            //     WHERE l2.license_plate = lpr_feeds.license_plate
            //     AND l2.event_type = "entry"
            // )')
            // ->groupBy('lpr_feeds.license_plate')
            // ->orderBy('lpr_feeds.event_created_at', 'asc')
            // ->select(
            //     'lpr_feeds.*',
            //     'tickets.id as ticket_id',
            //     'tickets.ticket_number as ticket_number'
            // )
            // ->paginate(10);
            $vehicles = DB::table('lpr_feeds as l1')
                ->where('l1.event_type', 'entry')
                ->where('l1.facility_id', $facility_id)
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('lpr_feeds as l2')
                        ->whereColumn('l2.license_plate', 'l1.license_plate')
                        ->where('l2.event_type', 'exit')
                        ->whereColumn('l2.event_created_at', '>', 'l1.event_created_at');
                })
                ->orderBy('l1.event_created_at', 'desc')
                ->paginate(10);
            $vechilelprData = array();
            if (count($vehicles) > 0) {
                foreach ($vehicles as $value) {
                    $tickets = DB::table('tickets')
                        ->where('license_plate', $value->license_plate)
                        ->where("partner_id", $value->partner_id)
                        //->where('checkin_time', '<=', $value->event_created_at)
                        ->where('checkout_time', '>=', $value->event_created_at)
                        ->where('is_checkout', '=', 0)
                        ->where('is_checkin', '=', 1)
                        ->orderBy('id', 'desc')
                        ->first();
                    if ($tickets) {
                        $vechilelprData[] = [
                            'lpr_feed_id'     => $value->id,
                            'license_plate'   => $value->license_plate,
                            'account_number'  => $this->getPermitNumber($value->license_plate),
                            'ticket_number'   => $tickets->ticket_number,
                            'entry_time'   =>   $value->event_created_at,

                        ];
                    } else {
                        $vechilelprData[] = [
                            'lpr_feed_id'     => $value->id,
                            'license_plate'   => $value->license_plate,
                            'account_number'  => $this->getPermitNumber($value->license_plate),
                            'ticket_number'   => null,
                            'entry_time'   =>   $value->event_created_at,

                        ];
                    }
                }
                return response()->json([
                    'vehicles'         => $vechilelprData,
                    'current_page' => $vehicles->currentPage(),
                    'last_page'    => $vehicles->lastPage(),
                    'per_page'     => $vehicles->perPage(),
                    'total'        => $vehicles->total(),
                ]);
                ///return $vechilelprData;
            } else {
                throw new ApiGenericException('No data found against this facility.');
            }
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            return $msg;
        }
    }

    public function getPermitNumber($license_plate)
    {
        $vehicles = PermitVehicleMapping::join('permit_vehicles', 'permit_vehicle_mapping.permit_vehicle_id', '=', 'permit_vehicles.id')
            ->join('permit_requests', function ($join) {
                $join->on('permit_vehicle_mapping.permit_request_id', '=', 'permit_requests.id')
                    ->on('permit_requests.user_id', '=', 'permit_vehicles.user_id');
            })
            ->where('permit_vehicles.license_plate_number', $license_plate)
            ->whereDate('permit_requests.desired_start_date', '<=', date("Y-m-d"))
            ->whereDate('permit_requests.desired_end_date', '>=', date("Y-m-d"))
            ->select(
                'permit_vehicle_mapping.*',
                'permit_vehicles.license_plate_number',
                'permit_requests.status as request_status',
                'permit_requests.user_id',
                'permit_requests.account_number'
            )
            ->first();

        // $vehicles = DB::table("lpr_feeds as lp")->where('lp.license_plate',$license_plate)
        // 	->leftJoin("permit_vehicles as pv", function ($join) use ($request) {
        // 		$join->on("lp.license_plate", "=", "pv.license_plate_number");
        // 	})
        // 	->leftJoin("permit_vehicle_mapping as pvm", "pv.id", "=", "pvm.permit_vehicle_id")
        // 	->leftJoin("permit_requests as pr", function ($join) use ($request) {
        // 		$join->on("pvm.permit_request_id", "=", "pr.id");
        //     });
        //     $vehicles = $vehicles->first();
        if (!empty($vehicles)) {
            return $vehicles->account_number;
        } else {
            return null;
        }
    }

    public function addVehicle(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'facility_id' => 'required|integer',
                'partner_id' => 'required|integer',
                'license_plate' => 'required|string',
                // 'permit_number' => 'required|string',
                'check_in_at' => 'required|date',
                //'survision_serial_number' => 'required'

            ]);
            if ($validator->fails()) {
                return response()->json([
                    'status' => 500,
                    'error' => $validator->errors()->first(),
                ], 500);
            } else {
                $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("facility_id", $request->facility_id)->first();
                //PIMIS-14859
                $addVechile = LPRFeed::create(
                    ['event_id' => $gate->lane_id, 'facility_id' => $request->facility_id, 'partner_id' => $request->partner_id, 'event_type' => $gate->gate_type, "event_created_at" => $request->check_in_at, 'event_timestamp' => $request->check_in_at, "license_plate" => $request->license_plate, "created_by" => $request->user()->id]
                );
                //$gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("facility_id", $request->facility_id)->first();
                if ($addVechile) {
                    $facility = Facility::with("facilityConfiguration")->find($request->facility_id);
                    $slotStatus = QueryBuilder::getSlotStatus($request->facility_id, $facility->capacity, $facility->facilityConfiguration->threshold_onlevel, $facility->facilityConfiguration->threshold_offlevel);
                    $ticket = array('facility' => $facility, 'license_plate' => array('event_created_at' => $request->check_in_at, 'license_plate' => $request->license_plate), 'gate' => $gate);
                    QueryBuilder::updateFacilitySlot($ticket, "entry", "LPR");

                    // $thresHoldData = FacilityConfiguration::updateOrCreate(
                    //     ['facility_id' => $request->facility_id], 
                    //     ['threshold_type' => $facility->facilityConfiguration->threshold_type, 'threshold_onlevel' => $facility->facilityConfiguration->threshold_onlevel,'threshold_offlevel' => $facility->facilityConfiguration->threshold_offlevel,'is_lot_full' => $slotStatus] 
                    // );

                }
                return $addVechile;
            }
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            return $msg;
        }
    }

    public function updateVehicle(Request $request, $lpr_feed_id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'facility_id' => 'required|integer',
                'partner_id' => 'required|integer',
                'license_plate' => 'required|string',
                // 'permit_number' => 'required|string',
                'check_in_at' => 'required|date',
                //'survision_serial_number' => 'required'
                //'lpr_feed_id' => 'required'
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'status' => 500,
                    'error' => $validator->errors()->first(),
                ], 500);
            } else {
                //PIMIS-14859
                $updateVechile = LPRFeed::where('id', $request->lpr_feed_id)
                    ->update([
                        'facility_id' => $request->facility_id,
                        'partner_id' => $request->partner_id,
                        'event_type' => "entry",
                        "event_created_at" => $request->check_in_at,
                        'event_timestamp' => $request->check_in_at,
                        "license_plate" => $request->license_plate,
                        "created_by" => $request->user()->id
                    ]);

                $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("facility_id", $request->facility_id)->first();
                if ($updateVechile) {
                    return "Data update Successfully";
                    //$facility = Facility::with("facilityConfiguration")->find($request->facility_id);
                    //$slotStatus = QueryBuilder::getSlotStatus($request->facility_id,$facility->capacity,$facility->facilityConfiguration->threshold_onlevel,$facility->facilityConfiguration->threshold_offlevel);

                    //$ticket=array('facility'=>$facility, 'license_plate' => array('event_created_at'=>$request->check_in_at,'license_plate'=>$request->license_plate),'gate'=>$gate);
                    //QueryBuilder::updateFacilitySlot($ticket, "entry", "LPR");

                } else {
                    return response()->json([
                        'status' => 500,
                        'error' => "Data not update please contact with admin."
                    ], 500);
                }
            }
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            return $msg;
        }
    }

    public function getVehicle(Request $request, $lpr_feed_id)
    {
        $getvechile = LPRFeed::find($lpr_feed_id);
        if ($getvechile) {
            $vehicles = DB::table('lpr_feeds')->find($lpr_feed_id);
            if (!empty($vehicles)) {
                $getTickets = Ticket::where('license_plate', $getvechile->license_plate)
                    ->where('is_checkin', '1')->where('is_checkout', '0')
                    //->where('checkin_time', '<=', $vehicles->event_created_at)
                    ->where('checkout_time', '>=', $vehicles->event_created_at)
                    ->first();
                $vechilelprData = [
                    'lpr_feed_id'     => $getvechile->id,
                    'license_plate'   => $getvechile->license_plate,
                    'account_number'  => $this->getPermitNumber($getvechile->license_plate),
                    'ticket_number'   => !empty($getTickets) ? $getTickets->ticket_number : null,
                    'entry_time'   =>   $getvechile->event_created_at
                ];
            }
        } else {
            return "Data not found";
        }
        return $vechilelprData;
    }

    public function deleteVehicle(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'facility_id' => 'required',
                'lpr_feed_id' => 'required|array',
                'lpr_feed_id' => 'required',
                'partner_id' => 'required',
                // 'license_plate' => 'required|array',
                // 'license_plate.*' => 'required',
                'check_in_at' => 'required'
            ]);
            if ($validator->fails()) {
                return response()->json([
                    'status' => 500,
                    'error' => $validator->errors()->first(),
                ], 500);
            } else {
                $getVehicle = LPRFeed::whereIn('id', $request->lpr_feed_id)
                    ->where('facility_id', $request->facility_id)->where('partner_id', $request->partner_id)->get();
                $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("facility_id", $request->facility_id)->first();
                $facility = Facility::with("facilityConfiguration")->find($request->facility_id);
                if (count($getVehicle) > 0) {
                    foreach ($getVehicle as $vehicle) {
                        $newVehicle = $vehicle->replicate();
                        $newVehicle->event_type = "exit";
                        $newVehicle->event_created_at = $request->check_in_at;
                        $newVehicle->event_timestamp = $request->check_in_at; //PIMS-14859
                        $newVehicle->created_by = $request->user()->id;
                        $newVehicle->event_id = $gate->lane_id;
                        $newVehicle->save();

                        $ticket = array('facility' => $facility, 'license_plate' => array('event_created_at' => $request->check_in_at, 'license_plate' => $vehicle->license_plate), 'gate' => $gate);
                        QueryBuilder::updateFacilitySlot($ticket, "exit", "LPR");
                    }
                } else {
                    return "No data found.";
                }
                return "Data deleted sucessfully.";
            }
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            return $msg;
        }
    }

    public function getPermitTicketByLp(Request $request, $license_plate)
    {
        $validator = Validator::make($request->all(), [
            'facility_id' => 'required',
            'license_plate' => 'required',
            'check_in_at' => 'required|date'
        ]);
        if ($validator->fails()) {
            return response()->json([
                'status' => 500,
                'error' => $validator->errors()->first(),
            ], 500);
        } else {
            $tickets = DB::table('tickets')
                ->where('license_plate', $request->license_plate)
                ->where("facility_id", $request->facility_id)
                // ->where('checkin_time', '<=', $request->check_in_at)
                ->where('checkout_time', '>=', $request->check_in_at)
                ->where('is_checkout', '=', 0)
                ->where('is_checkin', '=', 1)
                ->orderBy('id', 'desc')
                ->first();
            $data = array('tickets' => !empty($tickets) ? $tickets->ticket_number : null, "permit" => $this->getPermitNumber($request->license_plate));
            return $data;
        }
    }
}
