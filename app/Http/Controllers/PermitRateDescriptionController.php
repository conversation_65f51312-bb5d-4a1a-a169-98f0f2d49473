<?php

namespace App\Http\Controllers;

use App\Models\PermitRateDescription;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

class PermitRateDescriptionController extends Controller
{
    public function index()
    {
        $rate_descriptions = PermitRateDescription::all();
        return ['rate_descriptions' => $rate_descriptions];
    }

    public function rateDescriptionList($partner_id)
    {
        if($partner_id != ''){
            $rate_descriptions = PermitRateDescription::where('partner_id', $partner_id)->whereNull('deleted_at')->get();
        }else{
            $rate_descriptions = PermitRateDescription::whereNull('deleted_at')->all();
        }
        
        return ['rate_descriptions' => $rate_descriptions];
    }

    public function getRateDescription($id)
    {
        $rate_descriptions = PermitRateDescription::where('id', $id)->get();
        return ['rate_descriptions' => $rate_descriptions];
    }
    public function store(Request $request)
    {
        $this->validate($request, PermitRateDescription::$validParams);
        $rate_description = PermitRateDescription::create($request->all());
        return $rate_description;
    }
    public function destroy(RateDescription $rate_description)
    {
        
    }
    public function update(Request $request, $id)
    {
        $this->validate($request, PermitRateDescription::$validParams);
        $rate_description=PermitRateDescription::find($id);
        $rate_description->name=$request->name;
        $rate_description->hours_description=$request->hours_description;
        $rate_description->description=$request->description;
        $rate_description->active_status=$request->active_status;
        
        if($rate_description->save()){
            return $rate_description;
        }
        else{
            return "Can't update now";
        }
    }
}
