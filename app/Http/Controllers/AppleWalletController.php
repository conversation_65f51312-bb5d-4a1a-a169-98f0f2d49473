<?php

namespace App\Http\Controllers;

use Auth;
use Config;
use App\Models\ApplePass;
use App\Models\ApplePassDevices;
use App\Models\ApplePassRegister;
use Illuminate\Http\Request;
use App\Http\Requests;
use Thenextweb\PassGenerator;
use App\Classes\LoyaltyProgram;
use Storage;
use App\Models\LoyaltyUserAccounts;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Classes\PushNotification;
use App\Services\LoggerFactory;
use App\Models\User;
use App\Exceptions\UnauthorizedException;

class AppleWalletController extends Controller
{

    const PASS_TYPE_IDENTIFIER = "pass.com.iconGO.developmentPass";
    const PASS_DESCRIPTION = "Icon Rewards Card";
    const ORGANIZATION_NAME = "Icon parking System, LLC";
    const TEAM_ID = "CWLU787QMG";
    protected $log;
    /**
     * Create company affilate user
     */
    public function __construct(LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/apple_wallet_pass')->createLogger('apple_waalet_pass');
    }
    public function generateWalletPass(Request $request)
    {        
       if (!isset($request->account_number)) {
                    throw new ApiGenericException('Please provide required parameter.');
        }
        
       $serial_number = $request->account_number;
       $pk_file_url ='';
       $authToken = '';
       $pass_identifier = '';
       //check if pass is alreay created
       $passData = ApplePass::where('serial_number',$serial_number)->first();
       
      if($passData)
      {
          $pass_identifier = $passData->pass_id;
          $authToken = $passData->authorization_token;
          
          $file_exist = storage_path('app/passgenerator/'.$pass_identifier.'.pkpass');
          if(file_exists($file_exist))
          {
            unlink($file_exist);
          }
      }else{
          $authToken = $this->generateAuthToken();
          $pass_identifier = time(); 
      }
        // This, if set, it would allow for retrieval later on of the created Pass
        $pass = new PassGenerator($pass_identifier);        
        $loyalty_points = 0;
        $reward_number = '';
        $user = Auth::user();       
        $barcode = self::PASS_DESCRIPTION;         
       try{ 
        if ($user->is_loyalty) {
            $accountData = LoyaltyProgram::getUserAccounts($user->email, $user->id);
            if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) 
            {
                $loyalty_points = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
            }        
        $account = LoyaltyUserAccounts::where('user_id' , '=', $user->id)->orderBy('id', 'DESC')->first();

        if ($account) {
                $reward_number = $account->reward_no;
        }
      }
     }catch(\Exception $e)
     {
         throw new ApiGenericException('It seems to be some issue with your loyalty account.');
     }
       if($reward_number!='')
       {
          $barcode = $reward_number;
       }
        $pass_definition = [
            "description"       => self::PASS_DESCRIPTION,
            "formatVersion"     => 1,
            "organizationName"  => self::ORGANIZATION_NAME,
            "passTypeIdentifier"=> self::PASS_TYPE_IDENTIFIER,
            "serialNumber"      => $serial_number,
            "authenticationToken" => $authToken,
            //"webServiceURL" =>   "",
            "webServiceURL" =>   url('/apple-wallet'),
            "teamIdentifier"    => self::TEAM_ID,
            "foregroundColor"   => "rgb(255,255,255)",
            "backgroundColor"   => "rgb(51,51,51)",
            "labelColor" => "rgb(237,129,33)",           
            "barcode" => [
                "message"   => $barcode,
                "format"    => "PKBarcodeFormatQR",
                "altText"   => self::PASS_DESCRIPTION,
                "messageEncoding"=> "utf-8",
            ],
            "storeCard" => [
                "headerFields" => [
                    [
                        "key" => "points",
                        "label" => "Points",
                        "value" => number_format($loyalty_points)
                        ]
                ],              
                
                "secondaryFields" => [
                    [
                        "key" => "user",
                        "label" => "Member",
                        "value" => isset($user->name)?$user->name:'-'
                    ],
                    [
                        "key" => "bookingref",
                        "label" => "Rewards #",
                        "textAlignment" => "PKTextAlignmentRight",
                        "value" => $reward_number
                    ]
                ],
              
                "backFields" => [
                    [
                        "key" => "terms",
                        "label" => "Terms & Conditions",
                        "value" => "Terms and Conditions"
                    ], [
                        "key" => "Privacy",
                        "label" => "Privacy & Policy",
                        "value" => "Privacy and Policy"
                    ]
                ],
                "locations" => [
                    [
                        "latitude" => 0,
                        "longitude" => 0,
                        "relevantText" => ""
                    ]
                ],
                "transitType" => "PKTransitTypeTrain",
                 "relevantDate" => "2014-12-05T09:00-08:00"
            ],
        ];

        $pass->setPassDefinition($pass_definition);
        $pass->addAsset(base_path('public/assets/wallet/storecard/strip.png'));
        $pass->addAsset(base_path('public/assets/wallet/storecard/icon.png'));
        $pass->addAsset(base_path('public/assets/wallet/storecard/logo.png'));
        $pass->create();
         
        
        $pk_file_url = url('download-pkpass/'.$pass_identifier);
       
       if(!$passData)
      {  
         $passArray = array('serial_number'=>$serial_number,"user_id" =>$user->id,"pass_id" =>$pass_identifier, "data"=> json_encode($pass_definition),"authorization_token"=>$authToken,"pass_type_identifier"=>self::PASS_TYPE_IDENTIFIER);
        
         ApplePass::create(($passArray));
      }
       //adding apple walet to db       
        return ['file_url'=>$pk_file_url, 'pass_data'=>$pass_definition];
    }
    
    function generateAuthToken($length = 16) {
        return substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, $length);
    }
    
    
    function registerADevice(Request $request, $version, $identifier, $passTypeIdentifier, $serialNumber) 
    {
         //check if the same serialNumber pass is available.
        $applePass = ApplePass::where('serial_number',$serialNumber)->where('pass_type_identifier',$passTypeIdentifier)->first();
        $push_token = $request->pushToken;
        
        if(!$applePass)
        {
            throw new NotFoundException(NotFoundException::NOT_FOUND);
        }
         
        //check for authorization token
        $authorizationHeader = $request->header('authorization');
        $authorizedToken = explode(" ",$authorizationHeader);
        if(count($authorizedToken)>1)
        {
            $authorisedText = isset($authorizedToken[0])?$authorizedToken[0]:'';
            $authorisedValue = isset($authorizedToken[1])?$authorizedToken[1]:'';
            if(($authorisedText!="ApplePass") || ($authorisedValue != $applePass->authorization_token))
            {
                throw new UnauthorizedException("Sorry you are not authorised to use this service.");
            }
            
        }else{
            throw new UnauthorizedException("Sorry you are not authorised to use this service.");
        }
       
        
        //add a device 
        $deviceInfo = ApplePassDevices::firstOrNew(['identifier'=>$identifier]);
        $deviceInfo->push_token = $push_token;
        $deviceInfo->save();
       
        //register pass the devices
        ApplePassRegister::firstOrCreate(['apple_pass_id'=>$applePass->id,'apple_pass_device_id'=>$deviceInfo->id]);
        
        return "Success";
    }
    function unRegisterADevice(Request $request, $version, $identifier, $passTypeIdentifier, $serialNumber) 
    {
        //check if the same serialNumber pass is available.
        $applePass = ApplePass::where('serial_number',$serialNumber)->where('pass_type_identifier',$passTypeIdentifier)->first();
        $push_token = $request->pushToken;
        
        if(!$applePass)
        {
            throw new NotFoundException(NotFoundException::NOT_FOUND);
        }
        
        //check for authorization token
        $authorizationHeader = $request->header('authorization');
        $authorizedToken = explode(" ",$authorizationHeader);
        if(count($authorizedToken)>1)
        {
            $authorisedText = isset($authorizedToken[0])?$authorizedToken[0]:'';
            $authorisedValue = isset($authorizedToken[1])?$authorizedToken[1]:'';
            if(($authorisedText!="ApplePass") || ($authorisedValue != $applePass->authorization_token))
            {
                throw new UnauthorizedException("Sorry you are not authorised to use this service.");
            }
            
        }else{
            throw new UnauthorizedException("Sorry you are not authorised to use this service.");
        }
        
        //remove a device
        $deviceInfo = ApplePassDevices::firstOrNew(['identifier'=>$identifier]);
        $deviceInfo->delete();
        
        //register pass the devices
        $passDevices = ApplePassRegister::where(['apple_pass_id'=>$applePass->id,'apple_pass_device_id'=>$deviceInfo->id])->first();        
        $passDevices->delete();
        return "Success";
        
    }
    
    function getSerialNumbersByDevice(Request $request, $version, $identifier, $passTypeIdentifier) 
    {
       
        $returnData = array();
        $serialNumbers = array();
        //add a device 
        $passesUpdatedSince = isset($request->passesUpdatedSince)?$request->passesUpdatedSince:'';
      
        $this->log->info("Time - $passesUpdatedSince Device ID $identifier, passType id - $passTypeIdentifier, version - $version");

        $deviceInfo = ApplePassDevices::where('identifier',$identifier)->first();
        if(!$deviceInfo)
        {
            throw new NotFoundException(NotFoundException::NOT_FOUND);
        }
        
        //register pass the devices
        $applePass = $deviceInfo->passes;
        $applePass = $applePass->reject(function ($pass) use ($passesUpdatedSince) {
         if($passesUpdatedSince!='' && $passesUpdatedSince!=null)
            {
                if((strtotime($pass->updated_at))>($passesUpdatedSince))
                    return false;
                else
                    return true;
            }
            return false;      
        })->values();
        
        $lastUpdatedTag = $passesUpdatedSince;
        if(count($applePass)>0)
        {
            foreach($applePass as $pass)
            {
               $serialNumbers[] = $pass->serial_number;
               if($lastUpdatedTag != null)
               {
                 if($lastUpdatedTag < strtotime($pass->updated_at))
                 {
                    $lastUpdatedTag = strtotime($pass->updated_at);
                 }
               }else{
                   $lastUpdatedTag = strtotime($pass->updated_at);
               }
                
            }
        }
            
        $returnData["serialNumbers"]=$serialNumbers;
        $returnData["lastUpdated"]=(string)$lastUpdatedTag;
       
        return $returnData;
    }
    
    
    
    function getUpdatedPass(Request $request, $version, $passTypeIdentifier, $serial_number) 
    {
        if (!isset($serial_number)) {
                    throw new ApiGenericException('Please provide required parameter.');
        }
       $this->log->info("Updated pass Device ID $passTypeIdentifier, version - $request");
       $pk_file_url ='';
       //check if pass is alreay created
       $passData = ApplePass::where('serial_number',$serial_number)->first();
     
      if(!$passData)
      {
           throw new ApiGenericException('Sorry there is no pass available for provided data.');
      }
      
        $pass_identifier = $passData->pass_id;
        $file_exist = storage_path('app/passgenerator/'.$pass_identifier.'.pkpass');
        if(file_exists($file_exist))
        {
          unlink($file_exist);
        }
        // This, if set, it would allow for retrieval later on of the created Pass
        $pass = new PassGenerator($pass_identifier);        
        $loyalty_points = 0;
        $reward_number = '';     
        $barcode = self::PASS_DESCRIPTION;
        
        $account = LoyaltyUserAccounts::where('account_no', $serial_number)
					->orderBy('id', 'DESC')
					->first(); 
        $user_id = $account->user_id;
        $user = User::find($user_id); 
        try
        {
            if ($user->is_loyalty) {
               $accountData = LoyaltyProgram::getUserAccounts($user->email, $user->id);
               if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) 
               {
                    $loyalty_points = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
               }        
            $account = LoyaltyUserAccounts::where('user_id' , '=', $user->id)->orderBy('id', 'DESC')->first();

            if ($account) {
                $reward_number = $account->reward_no;
               }
            }
        }catch(\Exception $e)
        {
         throw new ApiGenericException('It seems to be some issue with your loyalty account.');
        }
        
       if($reward_number!='')
       {
          $barcode = $reward_number;
       }
       $pass_definition = [
            "description"       => self::PASS_DESCRIPTION,
            "formatVersion"     => 2,
            "organizationName"  => self::ORGANIZATION_NAME,
            "passTypeIdentifier"=> self::PASS_TYPE_IDENTIFIER,
            "serialNumber"      => $passData->serial_number,
            "authenticationToken" => $passData->authorization_token,
            //"webServiceURL" =>   "",
            "webServiceURL" =>   url('/apple-wallet'),
            "teamIdentifier"    => self::TEAM_ID,
            "foregroundColor"   => "rgb(255,255,255)",
            "backgroundColor"   => "rgb(51,51,51)",
            "labelColor" => "rgb(237,129,33)",
            
            "barcode" => [
                "message"   => $barcode,
                "format"    => "PKBarcodeFormatQR",
                "altText"   => self::PASS_DESCRIPTION,
                "messageEncoding"=> "utf-8",
            ],
            "storeCard" => [
                "headerFields" => [
                    [
                        "key" => "points",
                        "label" => "Points",
                        "value" => number_format($loyalty_points),
                        "changeMessage"=> "Your have %@ Rewards points now.",
                        ]
                ],              
                
                "secondaryFields" => [
                    [
                        "key" => "user",
                        "label" => "Member",
                        "value" => isset($user->name)?$user->name:'-'
                    ],
                    [
                        "key" => "bookingref",
                        "label" => "Rewards #",
                        "textAlignment" => "PKTextAlignmentRight",
                        "value" => $reward_number
                    ]
                ],
              
                "backFields" => [
                    [
                        "key" => "terms",
                        "label" => "Terms & Conditions",
                        "value" => "Terms and Conditions"
                    ], [
                        "key" => "Privacy",
                        "label" => "Privacy & Policy",
                        "value" => "Privacy and Policy"
                    ]
                ],
                "locations" => [
                    [
                        "latitude" => 0,
                        "longitude" => 0,
                        "relevantText" => ""
                    ]
                ],
                "transitType" => "PKTransitTypeTrain",
                 "relevantDate" => "2014-12-05T09:00-08:00"
            ],
        ];

        $pass->setPassDefinition($pass_definition);
        $pass->addAsset(base_path('public/assets/wallet/storecard/strip.png'));
        $pass->addAsset(base_path('public/assets/wallet/storecard/icon.png'));
        $pass->addAsset(base_path('public/assets/wallet/storecard/logo.png'));
        $pass_data = $pass->create();
        return $pass_data;

        
    }
    
    function sendPushNotification() 
    {
        $deviceToken="83eaadf28636564647631fb8a11abde6df058d0882862964e2610a4240ef162f";
        $passIdentity = "pass.com.iconGO.developmentPass";
        $response = PushNotification::sendIphonePushAppleWallet($deviceToken, $passIdentity, $badge = 0, $check= 0);
        return $response;
    }
    function logAppleWalletError(Request $request,$version) 
    {
        $passLogs = isset($request->logs)?$request->logs:'';
        $this->log->info("Logs Error $passLogs");
        
    }

}
