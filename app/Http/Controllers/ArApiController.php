<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;

use App\Classes\ArApi;
use App\Exceptions\NotFoundException;
use App\Exceptions\InvalidResponseException;

class ArApiController extends Controller
{

    /**
     * Gets all active accounts receivable? Not sure at this point.
     */
    public function getActiveAccounts()
    {
        $response = ArApi::activeAccounts();

        return $response;
    }

    /**
     * Get account receivable information from the AR API
     */
    public function getAccount($accountId)
    {
        $response = ArApi::lookupAccount($accountId);

        if ($response['status'] === 404 ||  $response['status'] === 400) {
            throw new NotFoundException('No account found with that ID.');
        }

        if ($response['status'] !== 200) {
            throw new InvalidResponseException('Invalid response returned from account API', ['api_response' => $response]);
        }

        $data = $response['data'];

        return $data;
    }

    /**
     * Get account receivable information from the AR API
     */
    public function getAccountBalances($accountId)
    {
        $response = ArApi::getBalances($accountId);

        if ($response['status'] === 404 ||  $response['status'] === 400) {
            throw new NotFoundException('No account found with that ID.');
        }

        if ($response['status'] !== 200) {
            throw new InvalidResponseException('Invalid response returned from account API', ['api_response' => $response]);
        }

        return $response['data'];
    }

    /**
     * See if the AR API is up
     */
    public function ping()
    {
        $response = ArApi::ping();

        return $response;
    }
}
