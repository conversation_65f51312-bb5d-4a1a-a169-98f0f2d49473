<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\LoyaltyClubUser;
use App\Models\MonthlyParkingUser;
use App\Exceptions\DuplicateEntryException;

use App\Services\Mailers\UserMailer;

class LoyaltyClubUserController extends Controller
{

    public function store(Request $request)
    {
        $this->validate($request, LoyaltyClubUser::$validParams);

        $loyaltyClubUser = new LoyaltyClubUser();

        $loyaltyClubUser->fill($request->all());
        $loyaltyClubUser->save();
        $this->sendStatusEmail($loyaltyClubUser);
        return $loyaltyClubUser;
    }

    private function sendStatusEmail($loyaltyClubUser)
    {
        $mailer = new UserMailer();
        $timestamp = date('Y-m-d H:i:s', time());
        $mailData = [
            'timestamp' => $timestamp,
            'user' => $loyaltyClubUser
        ];

        $mailer->sendMailTo(config('icon.loyalty_club_email'), 'Loyalty Club: New Application', $mailData, 'email.loyalty-club');
    }
}
