<?php

namespace App\Http\Controllers;

use App\Models\Photo;
use Illuminate\Http\Request;
use Auth;
use App\Http\Requests;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use Webpatser\Uuid\Uuid;
use Response;
use App\Exceptions\NotFoundException;
use Thenextweb\PassGenerator;
use App\Models\ApplePass;
use App\Classes\LoyaltyProgram;
use App\Models\LoyaltyUserAccounts;

class PhotoController extends Controller
{
    public function show(Photo $photo)
    {
        if (!$photo) {
            throw new NotFoundException('No image with that name found.');
        }
        
        $file = Storage::disk('local')->get($photo->image_name) ?: null;
        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/'.$photo->image_name));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }
    
    public function downloadPkPass($pass_identifier)
    {
        $headers = array(
              'Content-Type: application/pkpass',
         );        
        $file_name_download = $pass_identifier.".pkpass";
        $file_name =storage_path('app/passgenerator/'.$pass_identifier.'.pkpass');    
        return Response::download($file_name, $file_name_download, $headers);
//        return new Response($pkpass, 200, [
//            'Content-Transfer-Encoding' => 'binary',
//            'Content-Description' => 'File Transfer',
//            'Content-Disposition' => 'attachment; filename="pass.pkpass"',
//            'Content-length' => strlen($pkpass),
//            'Content-Type' => PassGenerator::getPassMimeType(),
//            'Pragma' => 'no-cache',
//        ]);
       
    }

    function generateAuthToken($length = 16) {
        return substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, $length);
    }

    public function couponImage($photo)
    {
        $file = Storage::disk('local')->get("coupons/". $photo) ?: null;
        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path("app/coupons/" . $photo));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }

    public function store(Request $request)
    {
        if (!$request->hasFile('image')) {
            return null;
        }

        $image = $request->file('image');
        $filename = Uuid::generate(4) . '_' . $image->getClientOriginalName();
        Image::make($image->getRealPath())->save(storage_path('app/uploads/' . $filename));

        return $filename;
    }
}
