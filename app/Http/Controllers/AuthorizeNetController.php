<?php

namespace App\Http\Controllers;

use Auth;

use App\Models\User;
use App\Models\PaymentProfile;
use App\Models\AuthorizeNetTransaction;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Models\Wallet;
use App\Models\Promotion;
use DB;
use Exception;

use App\Classes\AuthorizeNet\Cim;
use App\Classes\AuthorizeNet\Transactions;

use App\Exceptions\NotFoundException;
use App\Exceptions\UserNotAuthorized;

use Illuminate\Http\Request;

use App\Classes\MagicCrypt;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\DatacapPaymentProfile;


class AuthorizeNetController extends Controller
{

    protected $cim;
    protected $transactions;
    protected $request;
    protected $authNet;
    const CARD_VERIFICATION_AMOUNT = 0.1;
    const CARD_VERIFICATION_MESSAGE = "Save Payment Card Transation- Verifcation";

    public function __construct(Cim $cim, Transactions $transactions,AuthorizeNet $authNet)
    {
        $this->cim = $cim;
        $this->transactions = $transactions;
        $this->authNet = $authNet;
    }

    /**
     * Get cim for the currently authed user
     *
     * @return [type] [description]
     */
    public function getUserCim()
    {
        $user = Auth::user();
        $profile = $this->cim->isReservation()->setUser($user)->getCustomerProfile();

        if (!$profile) {
            throw new NotFoundException('No profile found for this user.');
        }

        return $profile;
    }

    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $name = trim($cardData[0]);
        $name = explode(' ', $name);
        $firstName = $name[0];
        $lastName = " ";
        $zipCode = isset($cardData[4])?$cardData[4]:'';
        if (isset($name[1])) {
            $lastName = $name[1];
        }
        $request->request->add(
            [
            'first_name' => $firstName,
            'last_name' => $lastName,
            'card_number' => $cardData[1],
            'expiration_date' => $cardData[2],
            'security_code' => $cardData[3],
            'zip_code' =>$zipCode
            ]
        );
    }

    public function saveUserPayment(Request $request, User $user)
    {
        $this->setDecryptedCard($request);
        
        $this->validate(
            $request, [
            'first_name' => 'required|string|max:255',
            // 'last_name' => 'required|string|max:255',
            'card_number' => 'required|numeric|digits_between:13,16',
            'expiration_date' => 'required|numeric|digits:4',
            'security_code' => 'required|numeric|digits_between:3,4'
            ]
        );

        if (Auth::user()->id !== $user->id) {
            throw new UserNotAuthorized("You are not authorized to add payments to this account.");
        }
        
        $this->authNet
                ->setUser($user)
                ->isReservation()
                ->setBillingAddress(
                [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'zip'  =>  $request->zip_code,   
                ]
            )->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
        
        //first check zip code by making a 0.01 transation         
            try {
                // Use our database rate price to create the transaction
                $charge = $this->authNet->createTransaction(
                    // $rate['price'],
                    self::CARD_VERIFICATION_AMOUNT,
                    self::CARD_VERIFICATION_MESSAGE,
                    config('icon.reservations_account')
                )->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
            } catch (Exception $e) {               
                throw $e;
            }
             // Void the transaction
            $transaction = AuthorizeNetTransaction::where('anet_trans_id', $charge['anet_trans_id'])->first();
            $this->authNet->setTransaction($transaction)->void();
            // Send reservation to ticketech
            
            
            $this->cim
            ->setUser($user)
            ->isReservation()
            ->setBillingAddress(
                [
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'zip'  =>  $request->zip_code,   
                ]
            )
            ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code);
        
            
            // Create payment or customer profile depending on whether we have an existing profile already
            return $user->cim
                ? $this->cim->createPaymentProfile()->executePaymentProfileRequestAddNewMethod()
                : $this->cim->createCustomerProfile()->executeCustomerProfileRequestAddNewMethod();
    }

    /**
     * Delete the payment profile given in $request->paymentProfile
     *
     * @param  Request $request [description]
     * @return [type]           [description]
     */
    public function deleteUserPayment(Request $request)
    {
        $paymentProfileId = $request->input('payment_profile_id');

        $cim = new Cim();
        if(Auth::user()->user_type == 3){
                $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', Auth::user()->id)->first();
                $is_partner = 1;
            }elseif(Auth::user()->user_type == 4 || Auth::user()->user_type == 5){
                $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', Auth::user()->created_by)->first();
                if($this->partnerPaymentDetails){
                    $is_partner = 1;
                }else{
                    throw new NotFoundException('No payment gateway details found for this partner.');
                }
            }else{
                $is_partner = 0;
            }
            if($is_partner == 1){
                $valid = $cim
                ->setUser(Auth::user())
                ->isReservation()
                ->deleteCustomerPaymentProfile($paymentProfileId, $this->partnerPaymentDetails);
            }else{
                $valid = $cim
                ->setUser(Auth::user())
                ->isReservation()
                ->deleteCustomerPaymentProfile($paymentProfileId);
            }

        return ['status' => $valid];
    }

    /**
     * Get information about a transaction from authorize.net
     *
     * @param  AuthorizeNetTransaction $transaction
     * @return array
     */
    public function getTransaction(AuthorizeNetTransaction $transaction)
    {
        return $this->transactions->setTransaction($transaction)->get();
    }
	
    public function getUserDetails(Request $request)
    {
            // Response variable
            $data = array();

            /* Saved Card List */
                    $user = Auth::user();
                    
                    $is_partner = 0;
                    if($user->user_type == 3){
                        $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $user->id)->first();
                        $is_partner = 1;
                    }elseif($user->user_type == 4 || $user->user_type == 5){
                        $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $user->created_by)->first();
                        if($this->partnerPaymentDetails){
                            $is_partner = 1;
                        }else{
                            throw new NotFoundException('No payment gateway details found for this partner.');
                        }
                    }else{
                        $is_partner = 0;
                    }

                    try {
                        $profile = $this->cim->isReservation()->setUser($user)->isPartner($this->partnerPaymentDetails)->getCustomerProfile();
                    }
                    catch(Exception $e) {
                            $data['saved_cards_list'] = ["payments" => ''];
                    }

                    if(empty($profile)) {
                            $data['saved_cards_list'] = ["payments" => []];
                    }
                    else {
                            $data['saved_cards_list'] = $profile;
                    }

                    $result = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user->id)->get();
                    if($result)
                    {
                        $data['planet_saved_cards_list'] = ["payments" => $result];
                    }
					
					$resultDataCap = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $user->id)->get();
                    if($resultDataCap)
                    {
                        $data['datacap_saved_cards_list'] = ["payments" => $resultDataCap];
                    }

            /* Icon Go Credits */
                    $this->validate($request, User::$validateUserId);
                    $user_id = $request->user_id;
                    $user = User::where('id', $user_id)->first();

                    if (!$user) {
                            throw new ApiGenericException('No User Found with This User Id.');
                    }

                    $msg = array();

                    if (!$user->referral_code) {
                            // if (!$user->referral_code) {
                            // generate new referral code
                            $referral_code = $this->generateReferCode($user->name);
                            $user->referral_code = $referral_code;
                            $user->save();
                    // }
                            // throw new ApiGenericException('User do not have a referral code.');
                    }

                    Wallet::calculateWallet($user_id);
                    $config = Wallet::getConfig();
                    $messages = DB::table('transaction_messages')->get();

                    foreach ($messages as $message) {
                            $msg[$message->mode] = $message->message;
                    }

                    $transactions = Wallet::getUserTransactions($user_id);

                    if (!$transactions) {
                            $transactions = [];
                    }

                    $wallet = Wallet::getUserWallet($user_id);

                    if (!$wallet) {
                            $wallet = ['balance' => 0];
                    }

                    $data['icon_go_credits'] = [
                            'user_id' => $user->id,
                            'referral_code' => $user->referral_code,
                            'wallet' => $wallet,
                            'config' => $config,
                            'messages' => $msg
                    ];

            /* Saved Promo Code */
                    // 1. Check if user_id is provided
                    $this->validate($request, User::$validateUserId);

                    // 2. check if user exists or not
                    $userId=$request->user_id;
                    $user = User::where('id', $userId)->first();

                    $currentDate = date('Y-m-d');
                    $newpromousage=null;

                            if($user) {
                            $user_id=$user->id;
                            $userPromocode = DB::table('users_promo_codes')
                            ->Join('promo_codes', 'users_promo_codes.promocode', '=', 'promo_codes.promocode')
                            ->Join('promotions', 'promotions.id', '=', 'promo_codes.promotion_id')
                            ->select('users_promo_codes.promocode', 'users_promo_codes.user_id', 'promo_codes.promocode', 'promo_codes.usage', 'promotions.valid_to', 'promotions.description', 'promo_codes.discount_type', 'promo_codes.discount_value')
                            ->where('users_promo_codes.user_id', $user_id)
                            ->where('promotions.valid_from', '<=', $currentDate)
                            ->where('promotions.valid_to', '>=', $currentDate)
                            ->get();
                    }       
                    if($userPromocode) {

                            foreach($userPromocode as $upck=>$upcv){
                                    $upcvuser_id=$upcv->user_id;
                                    $upcvpromocode=$upcv->promocode;
                                    if($upcv->usage=='') { $upcv->usage=1;
                                    }
                                    $tolproass[$upcvuser_id][$upcvpromocode]=$upcv->usage;
                                    $tolprovalto[$upcvuser_id][$upcvpromocode]=$upcv->valid_to;
                                    if($upcv->discount_type=='value') {
                                            $tolprodiscount='$'.$upcv->discount_value;
                                    } else {
                                            $tolprodiscount=$upcv->discount_value.'%';
                                    }

                                    $tolpromessage[$upcvuser_id][$upcvpromocode]=$upcv->description;      
                                    $promocodearr[]= $upcv->promocode;
                            }

                            $promocodestr="'".implode("','", $promocodearr)."'";

                            $usedpromocode = DB::table('promo_usages')->select(DB::raw('count(promocode) as tolusage ,promocode, user_id'))->where('user_id', $userId)->whereIn('promocode', $promocodearr)->groupBy('promocode')->get();//UserPromoCode::



                            if($usedpromocode) {
                                    foreach($usedpromocode as $upcuk=>$upcuv){
                                            $upcvuser_id=$upcuv->user_id;
                                            $upcvpromocode=$upcuv->promocode;
                                            $upcvusage=$upcuv->tolusage;
                                            $tolproused[$upcvuser_id][$upcvpromocode]=$upcvusage;
                                    }
                            }

                            if($tolproass[$userId]) {
                                    foreach($tolproass[$userId] as $tpak=>$tpav){
                                            if(isset($tolproused[$userId][$tpak])) {
                                                    $newpromousageck=$tpav - $tolproused[$userId][$tpak];
                                            } else {
                                                    $newpromousageck=$tpav-0;
                                            }

                                            if($newpromousageck) {
                                                    $newpromousage[$userId][]=array('promocode'=>$tpak,'usages'=>$newpromousageck,'validto'=>$tolprovalto[$userId][$tpak],'message'=>$tolpromessage[$userId][$tpak]);
                                            }
                                    }
                            }
                    }

                    if(!$newpromousage[$userId]) {
                       // throw new ApiGenericException('Error Occured, No record found with this user_id.');
                       $message='No promo code added to this account';
                                       $newpromousage[$userId]='';
                    }else{

                            $message='Remain usages of promocode.';
                    }


                    $data['saved_promo_code'] = [
                            'is_promocode_assign' => 1,
                            'user_id' => $userId,
                            'promocodedetail' => $newpromousage[$userId],
                            'message' => $message
                    ];

            /* Status Of Promo Code Subscription */
                    if($user->is_email_promotions)
                            $data['is_email_promotions'] = 1;
                    else
                            $data['is_email_promotions'] = 0;

            /* Return response to API */
                    return $data;

    }

    public function generateReferCode($name)
    {
        $code = mt_rand(********, ********);
        $name = $this->cleanData($name);
        $name_slug = strtoupper(substr($name, 0, 4));
        if (strlen($name_slug) < 4) {
            $remaining = 4 - strlen($name_slug);
            for ($i = 0; $i < $remaining; $i++) {
                $name_slug .= 'X';
            }
        }
        return $name_slug . $code;
    }

    public function cleanData($data)
    {
        $data = str_replace(' ', '', $data); // Remove All Spaces.
        $data = str_replace('-', '', $data); // Remove All Dashes.
        return preg_replace('/[^A-Za-z0-9\-]/', '', $data); // REMOVE ALL SPECIAL CHARACETRS
    }
	
}
