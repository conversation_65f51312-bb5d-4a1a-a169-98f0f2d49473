<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\WebError;
use Carbon\Carbon;

use App\Services\SlackErrorHandler;

// Used for logging errors sent in from the web application
class ErrorController extends Controller
{

    /**
     * Throw an API error for purposes of testing the slack logging integration
     *
     * @return [type] [description]
     */
    public function throwApiError(Request $request)
    {
        throw new \Exception("Test exception endpoint triggered by user visiting from {$request->ip()}.");
    }

    public function getError(WebError $error)
    {
        return $error;
    }

    /**
     * Log an error from the web application to slack
     *
     * @param  Request $request Should include params url, message, and state (json object representing current redux state)
     * @return [type]           [description]
     */
    public function logErrorToSlack(Request $request)
    {
        $this->validate(
            $request, [
                        'url' => 'string',
                        'message' => 'string',
                        'browser' => 'string'
                    ]
        );

        $error = WebError::firstOrNew($request->only((new WebError())->getFillable()));
        $error->occurences++;

        // set time as now
        $time = Carbon::now()->format('Y-m-d H:i:s');
        $time_seconds = strtotime($time);

        // slack seconds
        $in_slack_seconds = $error->in_slack ? strtotime($error->in_slack) : 0;

        // set time interval to false
        $slack_time_interval = 0;

        // if time interval for notifications has passed or is null set slack time and allow slack message to send
        if (! $error->in_slack || $time_seconds > ( $in_slack_seconds + env('SLACK_NOTIFY_TIME_INTERVAL', 0) )) {
            $error->in_slack = $time;
            $slack_time_interval = 1;
        }

        $error->save();

        if ($slack_time_interval && ($error->occurences <= env('SLACK_NOTIFY_OCCURRENCES_INTERVAL', 1000000) || ! strpos(($error->occurences / env('SLACK_NOTIFY_OCCURRENCES_INTERVAL', 1)), '.'))) {
            return (new SlackErrorHandler())->sendWebException($error);
        } else {
            return true;
        }
    }
}
