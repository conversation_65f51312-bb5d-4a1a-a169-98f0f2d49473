<?php

namespace App\Http\Controllers;

use Hash;
use Auth;
use DB;




use App\Models\UserEventsLog;
use App\Models\User;


use App\Http\Helpers\QueryBuilder;


use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

use Illuminate\Http\Request;
use App\Http\Requests;

class UserEventsLogController extends Controller
{



    public function addUserEvent(Request $request)
    {


        // 1. Check if PromoCode is provided
        /*$this->validate($request, UserEventsLog::$checkUserPromoAddValidationRules);
    
        $user = User::where('id', $request->user_id)->first();
     
        if(!$user) {
            throw new ApiGenericException('Invalid User, No User Found With This Id.');
        }*/

        $nUserEventsLog = new UserEventsLog();
        $nUserEventsLog->user_id = isset($request->user_id) ? $request->user_id : '0';
        $nUserEventsLog->event_name = $request->event_name;
        $nUserEventsLog->latitude = $request->latitude;
        $nUserEventsLog->longitude = $request->longitude;
        $nUserEventsLog->facility_id = $request->facility_id;
        $nUserEventsLog->app_version = $request->app_version;
        $nUserEventsLog->error_message = $request->error_message;
        $nUserEventsLog->device = $request->device;
        $nUserEventsLog->os_version = isset($request->os_version) ? $request->os_version : 'NA';
        $nUserEventsLog->device_detail = isset($request->device_detail) ? $request->device_detail : 'NA';
        $nUserEventsLog->zip_code = $request->zipcode;
        $nUserEventsLog->facility_name = $request->facility_name;
        $nUserEventsLog->ip = $request->ip();

        $result = $nUserEventsLog->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, User promo code Could Not Be Saved');
        }

        return [
            'is_UserEventsLog_added' => 1,
            'user_id' => $request->user_id,
            'event_name' => $request->event_name,
            'message' => 'UserEventsLog is updated with user'
        ];
    }
    
}
