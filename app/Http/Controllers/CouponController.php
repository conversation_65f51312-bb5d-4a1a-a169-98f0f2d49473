<?php

namespace App\Http\Controllers;

use Closure;

use App\Exceptions\NotFoundException;

use App\Models\Rate;
use App\Models\VisitorCode;

use Illuminate\Http\Request;
use App\Models\Visitor\Visitor;

class CouponController extends Controller
{

    public function sendEmail(Request $request)
    {
        $visitorCode = $request->header('X-Big-Apple');
        if((!isset($visitorCode)) || ($visitorCode==''))
        {
            $visitorCode = str_random(65);
            $visitor = new Visitor();
            $visitor->ip = $request->ip(); 
            $visitor->visitor_code = $visitorCode; 
            $visitor->save();
        }
        $this->validate($request, ['email' => 'required|email']);
        $coupon = Rate::find($request->id);
        if (!$coupon || !$coupon->coupon_code) {
            throw new NotFoundException(NotFoundException::NOT_FOUND);
        }
        $coupon->sendEmailGTM($request->email, $visitorCode);
        return "Success";
    }
    
    public function sendLandingPageEmail(Request $request)
    {
        $visitorCode = $request->header('X-Big-Apple');
        $this->validate($request, ['email' => 'required|email']);
        $coupon = Rate::find($request->id);
        if (!$coupon || !$coupon->coupon_code) {
            throw new NotFoundException(NotFoundException::NOT_FOUND);
        }
        $coupon->sendEmailLandingPage($request->email, $visitorCode);
        return "Success";
    }

    public function sendEmailNewDesign(Request $request)
    {
        $visitorCode = $request->header('X-Big-Apple');
        if((!isset($visitorCode)) || ($visitorCode==''))
        {
            $visitorCode = str_random(65);
            $visitor = new Visitor();
            $visitor->ip = $request->ip(); 
            $visitor->visitor_code = $visitorCode; 
            $visitor->save();
        }
        $this->validate($request, ['email' => 'required|email']);
        $coupon = Rate::find($request->id);
        if (!$coupon || !$coupon->coupon_code) {
            throw new NotFoundException(NotFoundException::NOT_FOUND);
        }
        $coupon->sendEmailGTMNewDesign($request->email, $visitorCode);
        return "Success";
    }
    
    public function sendLandingPageEmailNewDesign(Request $request)
    {
        $visitorCode = $request->header('X-Big-Apple');
        $this->validate($request, ['email' => 'required|email']);
        $coupon = Rate::find($request->id);
        if (!$coupon || !$coupon->coupon_code) {
            throw new NotFoundException(NotFoundException::NOT_FOUND);
        }
        $coupon->sendEmailLandingPageNewDesign($request->email, $visitorCode);
        return "Success";
    }
   

    public function show(Request $request, Rate $coupon)
    {
        $this->checkIsCoupon($coupon);

        $visitor = VisitorCode::firstOrNew(
            [
            'visitor_code' => $request->header(config('tracking.header'))
            ]
        );

        return array_merge(
            $coupon->toArray(), [
            'barcode_string' => $visitor->barCodeString($coupon->coupon_code)
            ]
        );
    }

    public function showHtml(Request $request, Rate $coupon)
    {
        $this->checkIsCoupon($coupon);

        $visitorCode = $request->header(config('tracking.header'));
        return $coupon->generateHtml($visitorCode);
    }

    public function showJpg(Request $request, Rate $coupon,$visitor_code='')
    {
        if((!isset($visitor_code)) || ($visitor_code==''))
        {
            $visitor_code = str_random(65);
            $visitor = new Visitor();
            $visitor->ip = $request->ip(); 
            $visitor->visitor_code = $visitor_code; 
            $visitor->save();
        }
        return $this->respondWithCouponImageMain(
            $request, $coupon, $visitor_code, function (Rate $coupon, $visitorCode) {
                return $coupon->generateJpg($visitorCode);
            }
        );
    }

    public function showBarcodeJpg(Request $request, Rate $coupon)
    {
        return $this->respondWithCouponImage(
            $request, $coupon, function (Rate $coupon, $visitorCode) {
                return $coupon->generateBarcodeJpg($visitorCode);
            }
        );
    }

    protected function respondWithCouponImage(Request $request, Rate $coupon, Closure $renderFunction)
    {
        $this->checkIsCoupon($coupon);

        $visitorCode = $request->header(config('tracking.header'));
        
        $image = $renderFunction($coupon, $visitorCode);

        return $this->respondWithJpg($image);
    }
    protected function respondWithCouponImageMain(Request $request, Rate $coupon, $visitor_code='', Closure $renderFunction)
    {
        $this->checkIsCoupon($coupon);

        $visitorCode = $request->header(config('tracking.header'));
        if((!isset($visitorCode)) || ($visitorCode==''))
        {
            $visitorCode=$visitor_code;
        }
        $image = $renderFunction($coupon, $visitorCode);

        return $this->respondWithJpg($image);
    }

    protected function checkIsCoupon(Rate $coupon)
    {
        if (!($coupon->is_coupon || $coupon->is_partner_coupon) || !$coupon->coupon_code) {
            throw new NotFoundException(NotFoundException::NOT_FOUND);
        }
    }
	
    public function showCouponDetails(Request $request, Rate $coupon)
    {
        $visitor_code = $request->header(config('headers.visitor'));
        $data = $coupon->couponDetails($visitor_code);
	return $data;
    }
}
