<?php

namespace App\Http\Controllers;
use App\Models\Merchant;

use Illuminate\Http\Request;
use App\Classes\ProPayService;
use Illuminate\Support\Facades\Log;

class ProPayController extends Controller
{
    protected $propayService;

    // Inject the ProPayService
    public function __construct(ProPayService $propayService)
    {
        $this->propayService = $propayService;
    }

    public function createMerchantAccount(Request $request)
    {
        // Validate the request data
        $this->validate($request, [
            'personalFirstName' => 'required|string',
            'personalLastName' => 'required|string',
            'personalDateOfBirth' => 'required|date_format:m-d-Y',
            'dayPhone' => 'required|string',
            'currencyCode' => 'required|string',
            'tier' => 'required|string',
            'businessLegalName' => 'required|string',
            'ein' => 'required|string',
            'personalAddress1' => 'required|string',
            'personalCity' => 'required|string',
            'personalState' => 'required|string',
            'personalCountry' => 'required|string',
            'personalZip' => 'required|string',
            'bankAccountNumber' => 'required|string',
            'routingNumber' => 'required|string',
            'partnerId' => 'required|string',
        ]);

        // Prepare the data for the ProPay API request
        $payload = $this->propayService->preparePayload($request->all());
        $errorMessages = config('propay_error_messages');
        Log::info('ProPay API request:', $payload);
        try {
            // Call the service to create a merchant account
            $response = $this->propayService->createMerchantAccount($payload);
            Log::info('ProPay API response:', $response);

            // Check if the account creation was successful
            if (isset($response['Status']) && $response['Status'] == '00') {
                // Store the account details in the database
                $this->storeMerchantData($request->all(), $response);

                // Return success response
                return response()->json([
                    'status' => 'success',
                    'data' => $response
                ], 200);
            } else {
                // Get the error message based on the status code
                Log::info("response".json_encode($response));
                $statusCode = $response['Status'];
                $errorMessage = $errorMessages[$statusCode] ?? 'Unknown error';

                // Return failure response with specific error message
                return response()->json([
                    'status' => $statusCode,
                    'message' => $errorMessage
                ], 200);
            }

        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store merchant data in the database.
     *
     * @param array $data
     * @param array $response
     */
    protected function storeMerchantData(array $data, array $response)
    {
        Merchant::create([
            'partner_id' => $data['partnerId'],
            'merchant_id' => $response['AccountNumber'],
            'first_name' => $data['personalFirstName'],
            'last_name' => $data['personalLastName'],
            'social_security_number' => $data['socialSecurityNumber'] ?? null,
            'source_email' => $data['sourceEmail'],
            'day_phone' => $data['dayPhone'],
            'evening_phone' => $data['eveningPhone'] ?? null,
            'terms_acceptance_ip' => $data['termsAcceptanceIP'],
            'terms_version' => $data['termsVersion'],
            'notification_email' => $data['notificationEmail'],
            'time_zone' => $data['timeZone'],

            'currency_code' => $data['currencyCode'],
            'tier' => $data['tier'],

            'business_legal_name' => $data['businessLegalName'],
            'doing_business_as' => $data['doingBusinessAs'],
            'ein' => $data['ein'],
            'merchant_category_code' => $data['merchantCategoryCode'],
            'business_type' => $data['businessType'],
            'website_url' => $data['websiteURL'],
            'business_description' => $data['businessDescription'],
            'monthly_bank_card_volume' => $data['monthlyBankCardVolume'],
            'average_ticket' => $data['averageTicket'],
            'highest_ticket' => $data['highestTicket'],
            'business_phone_number' => $data['businessPhoneNumber'],

            'address_address1' => $data['personalAddress1'],
            'address_address2' => $data['personalAddress2'] ?? null,
            'address_city' => $data['personalCity'],
            'address_state' => $data['personalState'],
            'address_country' => $data['personalCountry'],
            'address_zip' => $data['personalZip'],

            'mail_address1' => $data['mailAddress1'],
            'mail_address2' => $data['mailAddress2'] ?? null,
            'mail_city' => $data['mailCity'],
            'mail_state' => $data['mailState'],
            'mail_country' => $data['mailCountry'],
            'mail_zip' => $data['mailZip'],

            'business_address_address1' => $data['businessAddress1'],
            'business_address_address2' => $data['businessAddress2'] ?? null,
            'business_address_city' => $data['businessCity'],
            'business_address_state' => $data['businessState'],
            'business_address_country' => $data['businessCountry'],
            'business_address_zip' => $data['businessZip'],

            'bank_account_country_code' => $data['accountCountryCode'],
            'bank_account_number' => $data['bankAccountNumber'],
            'routing_number' => $data['routingNumber'],
            'account_ownership_type' => $data['accountOwnershipType'],
            'bank_name' => $data['bankName'],
            'account_type' => $data['accountType'],
            'account_name' => $data['accountName'],
            'description' => $data['businessDescription'],

            'device_name' => $data['deviceName'],
            'device_quantity' => $data['deviceQuantity'],

            'owner_count' => $data['ownerCount'] ?? null,
            'owner_first_name' => $data['ownerFirstName'] ?? null,
            'owner_last_name' => $data['ownerLastName'] ?? null,
            'owner_ssn' => $data['ownerSSN'] ?? null,
            'owner_email' => $data['ownerEmail'] ?? null,
            'owner_address' => $data['ownerAddress'] ?? null,
            'owner_city' => $data['ownerCity'] ?? null,
            'owner_state' => $data['ownerState'] ?? null,
            'owner_zip' => $data['ownerZip'] ?? null,
            'owner_country' => $data['ownerCountry'] ?? null,
            'owner_title' => $data['ownerTitle'] ?? null,
            'owner_percentage' => $data['ownerPercentage'] ?? null
        ]);
    }

    public function editMerchantBankAccount(Request $request, $merchant_id)
    {
        // Log the request data
        Log::info('Edit Merchant Bank Account Request:', $request->all());

        // Validate the request data
        $this->validate($request, [
            'IsSecondaryBankAccount' => 'boolean',
            'BankAccount.AccountCountryCode' => 'string|max:3|in:USA,CAN',
            'BankAccount.BankAccountNumber' => 'numeric|digits_between:1,20',
            'BankAccount.RoutingNumber' => 'numeric|digits_between:1,20',
            'BankAccount.AccountOwnershipType' => 'string|in:Personal,Business',
            'BankAccount.BankName' => 'string|max:50',
            'BankAccount.AccountType' => 'string|in:Checking,Savings,GeneralLedger',
        ]);

        // Prepare the payload
        $payload = $this->propayService->prepareEditBankAccountPayload($request->all(), $merchant_id);

        try {
            // Call the service to edit the merchant bank account
            $response = $this->propayService->editMerchantBankAccount($payload, $merchant_id);
            Log::info('Edit Merchant Bank Account response:', $response);

            // Check if the response indicates success (assuming `Status` is '00' for success)
            if (isset($response['Status']) && $response['Status'] == '00') {
                // Update the merchant data in the database
                $merchant = Merchant::where('merchant_id', $merchant_id)->first();

                if ($merchant) {
                    $merchant->update([
                        'bank_account_country_code' => $request->input('BankAccount.AccountCountryCode'),
                        'bank_account_number' => $request->input('BankAccount.BankAccountNumber'),
                        'routing_number' => $request->input('BankAccount.RoutingNumber'),
                        'account_ownership_type' => $request->input('BankAccount.AccountOwnershipType'),
                        'bank_name' => $request->input('BankAccount.BankName'),
                        'account_type' => $request->input('BankAccount.AccountType'),
                    ]);

                    Log::info('Merchant bank account updated in database for account ID: ' . $merchant_id);
                } else {
                    Log::warning('Merchant not found in database for account ID: ' . $merchant_id);
                }

                // Return success response
                return response()->json($response, 200);
            } else {
                // Return failure response from ProPay
                return response()->json([
                    'status' => $response['Status'],
                    'message' => $response['Message'] ?? 'Failed to update bank account',
                ], 400);
            }
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function editMerchantContactInfo(Request $request, $accountId)
    {
        // Validate the request data
        $this->validate($request, [
            'PersonalData.FirstName' => 'required|string|max:255',
            'PersonalData.MiddleInitial' => 'string|max:1',  // Optional by default
            'PersonalData.LastName' => 'required|string|max:255',
            'PersonalData.SourceEmail' => 'required|email',
            'PersonalData.PhoneInformation.DayPhone' => 'required|numeric|digits:10',
            'PersonalData.PhoneInformation.EveningPhone' => 'required|numeric|digits:10',
            'ExternalId' => 'required|string|max:255',
        ]);

        // Prepare the payload
        $payload = [
            'AccountNumber' => $accountId,
            'PersonalData' => [
                'FirstName' => $request->input('PersonalData.FirstName'),
                'MiddleInitial' => $request->input('PersonalData.MiddleInitial'),
                'LastName' => $request->input('PersonalData.LastName'),
                'SourceEmail' => $request->input('PersonalData.SourceEmail'),
                'PhoneInformation' => [
                    'DayPhone' => $request->input('PersonalData.PhoneInformation.DayPhone'),
                    'EveningPhone' => $request->input('PersonalData.PhoneInformation.EveningPhone')
                ]
            ],
            'ExternalId' => $request->input('ExternalId')
        ];

        try {
            // Call the service to update merchant contact information
            $response = $this->propayService->editMerchantContactInfo($payload, $accountId);
            Log::info('Contact info response:', $response);

            // Check if the API response indicates success
            if (isset($response['data']['Status']) && $response['data']['Status'] == '00') {
                // Update the merchant contact information in the database
                $merchant = Merchant::where('merchant_id', $accountId)->first();
                $data = $request->all();
                if ($merchant) {
                    $merchant->update([
                        'first_name' => $request['PersonalData']['FirstName'],
                        'middle_initial' => $data['PersonalData']['MiddleInitial'],
                        'last_name' => $data['PersonalData']['LastName'],
                        'source_email' => $data['PersonalData']['SourceEmail'],
                        'day_phone' => $data['PersonalData']['PhoneInformation']['DayPhone'],
                        'evening_phone' => $data['PersonalData']['PhoneInformation']['EveningPhone'],
                        'external_id' => $data['ExternalId'],
                    ]);

                    Log::info('Merchant contact information updated in database for Account Number: ' . $accountId);
                } else {
                    Log::warning('Merchant not found in database for Account Number: ' . $accountId);
                }

                // Return success response
                return response()->json($response, 200);
            } else {
                // Return failure response if ProPay returned an error status
                return response()->json([
                    'status' => $response['Status'],
                    'message' => $response['Message'] ?? 'Failed to update contact information',
                ], 400);
            }
        } catch (\Exception $e) {
            // Log the error and return an error response
            Log::error('Edit Merchant Contact Info Failed: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }


    public function editMerchantAddress(Request $request, $merchant_id)
    {
        Log::info('Payload:', ['data' => json_encode($request)]);

        // Validate the request data
        $this->validate($request, [
            'Address.ApartmentNumber' => 'string',
            'Address.Address1' => 'required|string|max:255',
            'Address.Address2' => 'string|max:255',
            'Address.City' => 'required|string|max:100',
            'Address.State' => 'required|string|max:100',
            'Address.Country' => 'required|string|max:3',
            'Address.Zip' => 'required|string|max:20',
            'MailAddress.ApartmentNumber' => 'string',
            'MailAddress.Address1' => 'string|max:255',
            'MailAddress.Address2' => 'string|max:255',
            'MailAddress.City' => 'required|string|max:100',
            'MailAddress.State' => 'required|string|max:100',
            'MailAddress.Country' => 'required|string|max:3',
            'MailAddress.Zip' => 'required|string|max:20',
        ]);

        // Prepare the payload
        $payload = [
            'AccountNumber' => $merchant_id,
            'Address' => [
                'ApartmentNumber' => $request->input('Address.ApartmentNumber'),
                'Address1' => $request->input('Address.Address1'),
                'Address2' => $request->input('Address.Address2'),
                'City' => $request->input('Address.City'),
                'State' => $request->input('Address.State'),
                'Country' => $request->input('Address.Country'),
                'Zip' => $request->input('Address.Zip'),
            ],
            'MailAddress' => [
                'ApartmentNumber' => $request->input('MailAddress.ApartmentNumber'),
                'Address1' => $request->input('MailAddress.Address1'),
                'Address2' => $request->input('MailAddress.Address2'),
                'City' => $request->input('MailAddress.City'),
                'State' => $request->input('MailAddress.State'),
                'Country' => $request->input('MailAddress.Country'),
                'Zip' => $request->input('MailAddress.Zip'),
            ]
        ];

        try {
            // Call the service to update merchant address information
            $response = $this->propayService->editMerchantAddress($payload, $merchant_id);

            // Check if the API response indicates success
            if (isset($response['data']['Status']) && $response['data']['Status'] == '00') {
                // Update the merchant address information in the database
                $merchant = Merchant::where('merchant_id', $merchant_id)->first();

                if ($merchant) {
                    $merchant->update([
                        'address_apartment_number' => $payload['Address']['ApartmentNumber'],
                        'address_address1' => $payload['Address']['Address1'],
                        'address_address2' => $payload['Address']['Address2'],
                        'address_city' => $payload['Address']['City'],
                        'address_state' => $payload['Address']['State'],
                        'address_country' => $payload['Address']['Country'],
                        'address_zip' => $payload['Address']['Zip'],
                        'mail_address_apartment_number' => $payload['MailAddress']['ApartmentNumber'],
                        'mail_address_address1' => $payload['MailAddress']['Address1'],
                        'mail_address_address2' => $payload['MailAddress']['Address2'],
                        'mail_address_city' => $payload['MailAddress']['City'],
                        'mail_address_state' => $payload['MailAddress']['State'],
                        'mail_address_country' => $payload['MailAddress']['Country'],
                        'mail_address_zip' => $payload['MailAddress']['Zip'],
                    ]);

                    Log::info('Merchant address information updated in database for Account Number: ' . $merchant_id);
                } else {
                    Log::warning('Merchant not found in database for Account Number: ' . $merchant_id);
                }

                // Return success response
                return response()->json($response, 200);
            } else {
                // Return failure response if ProPay returned an error status
                return response()->json([
                    'status' => $response['Status'] ?? 'error',
                    'message' => $response['Message'] ?? 'Failed to update address information',
                ], 400);
            }
        } catch (\Exception $e) {
            // Log the error and return an error response
            Log::error('Edit Merchant Address Failed: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }


    public function editMerchantBusinessInfo(Request $request, $merchant_id)
    {
        // Validate the request data
        $this->validate($request, [
            'AuthorizedSignerFirstName' => 'string|max:255',
            'AuthorizedSignerLastName' => 'string|max:255',
            'AuthorizedSignerTitle' => 'string|max:255',
            'BusinessData.BusinessLegalName' => 'string|max:255',
            'BusinessData.DoingBusinessAs' => 'string|max:255',
            'BusinessData.EIN' => 'string|max:9',
            'BusinessData.WebsiteURL' => 'string|max:255', // No need for 'nullable'
            'BusinessData.BusinessDescription' => 'string|max:1000', // No need for 'nullable'
            'BusinessData.MonthlyBankCardVolume' => 'integer', // No need for 'nullable'
            'BusinessData.HighestTicket' => 'integer', // No need for 'nullable'
            'BusinessAddress.ApartmentNumber' => 'string|max:10', // No need for 'nullable'
            'BusinessAddress.Address1' => 'string|max:255',
            'BusinessAddress.Address2' => 'string|max:255', // No need for 'nullable'
            'BusinessAddress.City' => 'string|max:100',
            'BusinessAddress.State' => 'string|max:100',
            'BusinessAddress.Country' => 'string|max:3', // No need for 'nullable'
            'BusinessAddress.Zip' => 'string|max:20',
        ]);

        // Prepare the payload
        $payload = [
            'AccountNumber' => $merchant_id,
            'AuthorizedSignerFirstName' => $request->input('AuthorizedSignerFirstName'),
            'AuthorizedSignerLastName' => $request->input('AuthorizedSignerLastName'),
            'AuthorizedSignerTitle' => $request->input('AuthorizedSignerTitle'),
            'BusinessData' => [
                'BusinessLegalName' => $request->input('BusinessData.BusinessLegalName'),
                'DoingBusinessAs' => $request->input('BusinessData.DoingBusinessAs'),
                'EIN' => $request->input('BusinessData.EIN'),
                'WebsiteURL' => $request->input('BusinessData.WebsiteURL'),
                'BusinessDescription' => $request->input('BusinessData.BusinessDescription'),
                'MonthlyBankCardVolume' => $request->input('BusinessData.MonthlyBankCardVolume'),
                'HighestTicket' => $request->input('BusinessData.HighestTicket')
            ],
            'BusinessAddress' => [
                'ApartmentNumber' => $request->input('BusinessAddress.ApartmentNumber'),
                'Address1' => $request->input('BusinessAddress.Address1'),
                'Address2' => $request->input('BusinessAddress.Address2'),
                'City' => $request->input('BusinessAddress.City'),
                'State' => $request->input('BusinessAddress.State'),
                'Country' => $request->input('BusinessAddress.Country'),
                'Zip' => $request->input('BusinessAddress.Zip')
            ]
        ];

        try {
            // Call the service to update merchant business information
            $response = $this->propayService->editMerchantBusinessInfo($payload, $merchant_id);

            // Check if the API response indicates success
            if (isset($response['data']['Status']) && $response['data']['Status'] == '00') {
                // Update the merchant business information in the database
                $merchant = Merchant::where('merchant_id', $merchant_id)->first();

                if ($merchant) {
                    $merchant->update([
                        'authorized_signer_first_name' => $payload['AuthorizedSignerFirstName'],
                        'authorized_signer_last_name' => $payload['AuthorizedSignerLastName'],
                        'authorized_signer_title' => $payload['AuthorizedSignerTitle'],
                        'business_legal_name' => $payload['BusinessData']['BusinessLegalName'],
                        'doing_business_as' => $payload['BusinessData']['DoingBusinessAs'],
                        'ein' => $payload['BusinessData']['EIN'],
                        'website_url' => $payload['BusinessData']['WebsiteURL'],
                        'business_description' => $payload['BusinessData']['BusinessDescription'],
                        'monthly_bank_card_volume' => $payload['BusinessData']['MonthlyBankCardVolume'],
                        'highest_ticket' => $payload['BusinessData']['HighestTicket'],
                        'business_address_apartment_number' => $payload['BusinessAddress']['ApartmentNumber'],
                        'business_address_address1' => $payload['BusinessAddress']['Address1'],
                        'business_address_address2' => $payload['BusinessAddress']['Address2'],
                        'business_address_city' => $payload['BusinessAddress']['City'],
                        'business_address_state' => $payload['BusinessAddress']['State'],
                        'business_address_country' => $payload['BusinessAddress']['Country'],
                        'business_address_zip' => $payload['BusinessAddress']['Zip']
                    ]);

                    Log::info('Merchant business information updated in database for Account Number: ' . $merchant_id);
                } else {
                    Log::warning('Merchant not found in database for Account Number: ' . $merchant_id);
                }

                // Return success response
                return response()->json($response, 200);
            } else {
                // Return failure response if ProPay returned an error status
                return response()->json([
                    'status' => $response['Status'] ?? 'error',
                    'message' => $response['Message'] ?? 'Failed to update business information',
                ], 400);
            }
        } catch (\Exception $e) {
            // Log the error and return an error response
            Log::error('Edit Merchant Business Info Failed: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }


    public function getMerchantAccountDetail($merchant_id)
    {
        try {
            // Call the service to update merchant business information
            $response = $this->propayService->getMerchantAccountDetail($merchant_id);

            // Return success response
            return response()->json($response, 200);

        } catch (\Exception $e) {
            Log::error('ProPay Account Details Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error occurred while getting account details: ' . $e->getMessage()
            ], 500);
        }
    }

    public function uploadChargebackDocument(Request $request)
    {
        // Validate the form-data request
        $this->validate($request, [
            'AccountNum' => 'required|integer',
            'DocumentName' => 'required|string|max:100',
            'TransactionReference' => 'required|string|max:40',
            'DocType' => 'required|string|in:tif,tiff,bmp,jpg,jpeg,gif,png,pdf',
            'Document' => 'required|file|max:10240', // 10 MB max file size
        ]);

        // Retrieve text data from the form-data request
        $accountNum = $request->input('AccountNum');
        $documentName = $request->input('DocumentName');
        $transactionReference = $request->input('TransactionReference');
        $docType = $request->input('DocType');

        // Retrieve the uploaded file
        $file = $request->file('Document');

        // Convert the file to Base64
        $documentBase64 = base64_encode(file_get_contents($file->getRealPath()));

        // Prepare the payload for the service
        $payload = [
            'accountNum' => $accountNum,
            'DocumentName' => $documentName,
            'TransactionReference' => $transactionReference,
            'DocType' => $docType,
            'Document' => $documentBase64,
            'Identification' => 'Null'  // Required field as per ProPay's API
        ];

        try {
            // Call the ProPayService to upload the document
            $response = $this->propayService->uploadChargebackDocument($payload);
            if (isset($response['Status']) && $response['Status'] == '00') {

                // Return success response
                return response()->json([
                    'status' => 'success',
                    'message' => $response
                ], 200);
            } else {
                $errorMessages = config('propay_error_messages');
                $statusCode = $response['Status'];
                // Return a success response
                return response()->json([
                    'status' => $statusCode,
                    'message' => $errorMessages[$statusCode]
                ], 200);
            }
        } catch (\Exception $e) {
            // Log the error and return an error response
            Log::error('Upload Chargeback Document Failed: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getTransactionDetails(Request $request)
    {
        // Validate the converted array data
        $this->validate($request, [
            'accountNum' => 'required|integer',
            'payerName' => 'string|max:100',
            'amount' => 'integer',
            'ccNumLastFour' => 'integer|digits:4',
            'comment1' => 'string|max:128',
            'invNum' => 'string|max:50',
            'invoiceExternalRefNum' => 'string|max:50',
            'transNum' => 'integer'
        ]);

        $payload = [
            'accountNum' => $request->input('accountNum'),
            'payerName' => $request->input('payerName'),
            'amount' => $request->input('amount'),
            'ccNumLastFour' => $request->input('ccNumLastFour'),
            'comment1' => $request->input('comment1'),
            'invNum' => $request->input('invNum'),
            'invoiceExternalRefNum' => $request->input('invoiceExternalRefNum'),
            'transNum' => $request->input('transNum'),
            'transType' => $request->input('transType')
        ];


        // Proceed with the service call using $data
        try {
            $response = $this->propayService->getTransactionDetails($payload);

            // Return a successful JSON response
            return response()->json($response, 200);

        } catch (\Exception $e) {
            \Log::error('Transaction Details Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error occurred while getting transaction details: ' . $e->getMessage()
            ], 500);
        }
    }

    public function uploadDocument(Request $request)
    {
        // Validate the converted array data
        $this->validate($request, [
            'AccountNum' => 'required|integer',
            'DocumentName' => 'required|string|max:100',
            'DocCategory' => 'required|string|max:40',
            'DocType' => 'required|string|max:4',
            'Document' => 'required|file|max:10240' // 10 MB limit for file size
        ]);

        $file = $request->file('Document');
        $base64File = base64_encode(file_get_contents($file));

        $payload = [
            'AccountNum' => $request->input('AccountNum'),
            'DocumentName' => $request->input('DocumentName'),
            'DocCategory' => $request->input('DocCategory'),
            'DocType' => $request->input('DocType'),
            'Document' => $base64File,
        ];

        try {
            // Call the service to upload the document
            $response = $this->propayService->uploadDocument($payload);
            if (isset($response['XMLTrans']['status']) && $response['XMLTrans']['status'] == '00') {

                // Return success response
                return response()->json([
                    'status' => 'success',
                    'message' => $response
                ], 200);
            } else {
                $errorMessages = config('propay_error_messages');
                $statusCode = $response['XMLTrans']['status'];
                // Return a success response
                return response()->json([
                    'status' => $statusCode,
                    'message' => $errorMessages[$statusCode]
                ], 200);
            }

        } catch (\Exception $e) {
            Log::error('ProPay Upload Document Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error occurred while uploading document: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getSSOAuthToken(Request $request)
    {
        // Validate the converted array data
        $this->validate($request, [
            'AccountNum' => 'required|integer',
            'ReferrerUrl' => 'required|string|max:100',
            'IpSubnetMask' => 'required|string|max:40',
            'IpAddress' => 'required|string|max:40',
        ]);

        $payload = [
            'AccountNum' => $request->input('AccountNum'),
            'ReferrerUrl' => $request->input('ReferrerUrl'),
            'IpSubnetMask' => $request->input('IpSubnetMask'),
            'IpAddress' => $request->input('IpAddress'),
        ];

        try {
            // Call the service to upload the document
            $response = $this->propayService->getSSOAuthToken($payload);
            if (isset($response['XMLTrans']['status']) && $response['XMLTrans']['status'] == '00') {

                // Return success response
                return response()->json([
                    'status' => 'success',
                    'message' => $response
                ], 200);
            } else {
                $errorMessages = config('propay_error_messages');
                $statusCode = $response['XMLTrans']['status'];
                // Return a success response
                return response()->json([
                    'status' => $statusCode,
                    'message' => $errorMessages[$statusCode]
                ], 200);
            }

        } catch (\Exception $e) {
            Log::error('ProPay Upload Document Error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error occurred while uploading document: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getMerchantAccounts(Request $request)
    {
        // Start with a base query builder for merchants
        $merchantsQuery = Merchant::with(['FacilityDetails']);
        Log::info('Edit Merchant Bank Account Request:' . $request->search);

        // Apply search filter if the search query is provided
        if ($request->search != '') {

            $merchantsQuery->where(function ($query) use ($request) {
                $query->where('merchant_id', 'like', "%{$request->search}%")
                    ->orWhere('source_email', 'like', "%{$request->search}%")
                    ->orWhere('bank_name', 'like', "%{$request->search}%")
                    ->orWhere('business_legal_name', 'like', "%{$request->search}%")
                    ->orWhere('first_name', 'like', "%{$request->search}%");
            });

        }

        // Apply sorting if sort parameters are provided
        if ($request->sort != '') {
            $merchantsQuery->orderBy($request->sort, $request->sortBy);
        } else {
            $merchantsQuery->orderBy('merchant_id', 'DESC');
        }

        $merchantsQuery->where('partner_id', $request->partnerId);

        $merchants = $merchantsQuery->paginate(20);

        // Return data as JSON response
        return response()->json($merchants);
    }

    public function getMerchantAccountById($accountId)
    {
        // Find a merchant by ID
        $merchant = Merchant::where("merchant_id", $accountId)->first();

        // Check if the merchant exists
        if (!$merchant) {
            return response()->json(['error' => 'Merchant not found'], 404);
        }

        // Return merchant data as JSON response
        return response()->json($merchant);
    }
    public function deleteMerchantAccount($merchant_id)
    {
        try {
            // Find the merchant by ID
            $merchant = Merchant::where('merchant_id', $merchant_id)->first();

            // Check if the merchant exists
            if (!$merchant) {
                return response()->json(['error' => 'Merchant not found'], 404);
            }

            // Delete the merchant
            $merchant->delete();

            // Return success response
            return response()->json(['status' => 'success', 'message' => 'Merchant deleted successfully'], 200);
        } catch (\Exception $e) {
            // Log the error and return an error response
            \Log::error('Error deleting merchant: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => 'Failed to delete merchant'], 500);
        }
    }

    public function getAccountKeysByWebhook(Request $request)
    {
        // Log the incoming request for debugging purposes
        Log::info("Propay account received", [$request->all()]);

        // Extract credentials from the request
        $credentials = $request->input('credentials');

        // Check if the necessary data exists in the request
        if (!isset($credentials['merchantId'])) {
            return response()->json(['error' => 'merchantId is required'], 400);
        }

        // Find the merchant by merchantId from the request
        $merchant = Merchant::where("merchant_id", $credentials['merchantId'])->first();

        // Check if the merchant exists
        if (!$merchant) {
            return response()->json(['error' => 'Merchant not found'], 404);
        }

        // Update the merchant with the credentials from the request
        $merchant->deviceId = $credentials['deviceId'] ?? null;
        $merchant->siteId = $credentials['siteId'] ?? null;
        $merchant->publicApiKey = $credentials['publicApiKey'] ?? null;
        $merchant->secretApiKey = $credentials['secretApiKey'] ?? null;
        $merchant->website = $credentials['website'] ?? null;

        // Save the updated merchant data to the database
        $merchant->save();

        // Return the updated merchant data as JSON response
        return response()->json($merchant);
    }

}
