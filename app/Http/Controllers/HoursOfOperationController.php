<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;

use App\Models\HoursOfOperation;
use App\Models\FacilityInventorySpecificDate;

//for inventory update on hours of operation
use App\Models\FacilityAvailability;
use App\Models\FacilityInventory;
use App\Models\FacilityPartnerAvailabilityCron;
use App\Exceptions\ApiGenericException;
use Carbon\Carbon;
use App\Models\Reservation;
use App\Models\Facility;
use App\Classes\Inventory;

class HoursOfOperationController extends Controller
{
	// for inventory update on hours of operation
    const DAY_START_HOUR              = 0;
    const DAY_END_HOUR                = 23;
    const NUMBER_OF_SPOTS             = 50;
    const PARTNER_SPOTS_AVAILABLE     = 1;
    const PARTNER_SPOTS_NOT_AVAILABLE = 0;

    public function destroy(Request $request, HoursOfOperation $hours)
    {
    	$reservation_validation = isset($request->reservation_validation)?$request->reservation_validation:true;
    	//check if any reservation confliction
        $respons = $hours;
        if($hours)
        {
        	$hoursOfOperations = HoursOfOperation::where('facility_id', $hours->facility_id)->get();
	        $reservationExist = false;
	        $reservations = Reservation::where('facility_id', $hours->facility_id)->whereNull('cancelled_at')->whereRaw("'".Carbon::now()."' <= DATE_ADD(start_timestamp, INTERVAL length HOUR)")->get();
	        if(count($reservations)>0 && count($hoursOfOperations)>1 && $reservation_validation)
	        {	        	
		        $week = [0 => 'Sunday', 1 => 'Monday', 2 => 'Tuesday', 3 => 'Wednesday', 4 => 'Thursday', 5 => 'Friday', 6 => 'Saturday', 7 => 'Sunday'];

		        $operationalHoursArray = [];
		        foreach (range(0, 6) as $zeroToSix) {
            		$operationalHoursArray[$week[$zeroToSix]]=[];
        		}
		    	        
		        if ($hours->close_time > '23:59:59') {
		            for($i=0;$i<explode(':', $hours->close_time)[0] - 24;$i++){
		                $operationalHoursArray[$week[$hours->day_of_week + 1]][]=(string)$i;
		            }
		            for($i=(int)date('G', strtotime($hours->open_time));$i<=(int)date('G', strtotime('23:59:59'));$i++){
		                $operationalHoursArray[$week[$hours->day_of_week]][]=(string)$i;
		            }

		        }
		        else{
		            for($i=(int)date('G', strtotime($hours->open_time));$i<=(int)date('G', strtotime($hours->close_time));$i++){
		                $operationalHoursArray[$week[$hours->day_of_week]][]=(string)$i;
		            }
		        }
	            foreach ($reservations as $reservation)
	            {
	                $date_time_in= date('Y-m-d H:i:s',strtotime($reservation->start_timestamp));
	                $reservation_length= $reservation->length;
	                $length= round($reservation->length,0);
	                $reservation_minutes = 0;
	                $reservation_hours = explode(".",$reservation->length);
	                if(isset($reservation_hours[1]) && ($reservation_hours[1])>0)
	                {
	                    $reservation_minutes = 30;
	                }
	                $reservation_date_time_out =  date('Y-m-d H:i:s',strtotime(Carbon::parse($date_time_in)->addHours($reservation_length)->addMinutes($reservation_minutes)));
	                if(Carbon::parse($reservation_date_time_out)->gte(Carbon::now())){
	                    $dayIn = date('w', strtotime(Carbon::parse($date_time_in)->format('Y-m-d')));
	                    $dayOut = date('w', strtotime(Carbon::parse($reservation_date_time_out)->format('Y-m-d')));
	                    $hourIn = date('G', strtotime(Carbon::parse($date_time_in)->format('H:i:s')));
                    	$hourOut = date('G', strtotime(Carbon::parse($reservation_date_time_out)->format('H:i:s')));
                        if(in_array($hourIn,$operationalHoursArray[$week[$dayIn]]) || in_array($hourOut,$operationalHoursArray[$week[$dayOut]])){
                        	//throw new ApiGenericException("There are future reservations that conflict with the proposed garage hours.", 422);
                        	return ['deleted' =>  false, 'message' => "There are future reservations that conflict with the proposed garage hours."];
                    	}
                    }
	            }
	        }
	        
	    	$respons = $hours->delete();

	        //to create or update inventory and avialability from current weel
	        Carbon::setWeekStartsAt(Carbon::SUNDAY);
	        $dates = new \DatePeriod(
	            new \DateTime(Carbon::now()->startOfWeek()), new \DateInterval('P1D'),
	            new \DateTime(Carbon::now()->startOfWeek()->addWeeks(14)));
			//to get special dates    
			$facilitySpecify = FacilityInventorySpecificDate::where('facility_id', $hours->facility_id)->where('date', '>=', Carbon::now()->startOfWeek()->format('Y-m-d'))->select('date')->get()->toArray();
			$SpectialDates = [];
			foreach($facilitySpecify as $SpectialDate){
				$SpectialDates[] = $SpectialDate['date'];
			}
	        Facility::where('id', $hours->facility_id)->with(['availabilities'=>function($query){
            	return $query->where('date', '>=', Carbon::now()->startOfWeek()->subDays(7)->format('Y-m-d'));

        	}, 'inventories'=>function($query){
            	return $query->where('date', '>=', Carbon::now()->startOfWeek()->subDays(7)->format('Y-m-d'));

        	}])->chunk(
	            1, function ($facilities) use ($dates, $hours, $SpectialDates) {
	            foreach ($facilities as $facility) {
	                
	                $facilityInventories        = [];
	                $facilityAvailabilities     = [];
	                                
	                foreach ($facility->inventories as $facility_inventory) {
	                   	$facilityInventories[date('Y-m-d', strtotime($facility_inventory->date))] = 
	                	$facility_inventory->availability;
	                }
	                foreach ($facility->availabilities as $facility_availability) {
                        $facilityAvailabilities[date('Y-m-d', strtotime($facility_availability->date))] =
                        $facility_availability->availability;
                    }

                	Carbon::setWeekStartsAt(Carbon::SUNDAY);
	                $current_week = new \DatePeriod(
	                new \DateTime(Carbon::now()->startOfWeek()), new \DateInterval('P1D'),
	                new \DateTime(Carbon::now()->startOfWeek()->addWeeks(1)));
	                $flatValue = true;
	                $tempValue = 0;
	                foreach($current_week as $k=>$current_week_date){
	                    
	                    $lastWeekToday = Carbon::createFromDate($current_week_date->format('Y'), $current_week_date->format('m'), $current_week_date->format('d'))->subDays(7)->format('Y-m-d');
	                    $current_inventory = json_decode($facilityInventories[$current_week_date->format('Y-m-d')], true);

						$current_availability = json_decode($facilityAvailabilities[$current_week_date->format('Y-m-d')], true);
						

						if(isset($facilityInventories[$current_week_date->format('Y-m-d')])){
							$startDate = $current_week_date->format('Y-m-d');
								
							while(in_array($startDate, $SpectialDates)){
	
								$startDate = Carbon::parse($startDate)->addDays(7)->format('Y-m-d');
							}
							$current_inventory = json_decode($facilityInventories[$startDate], true);
	
							$current_availability = json_decode($facilityAvailabilities[$startDate], true);
						}

	                    foreach($current_inventory as $value){
	                        if($tempValue === 0){
	                            $tempValue = $value;
	                        }
	                        else if($tempValue == $value){
	                            // flat value consistent
	                        }
	                        else{
	                            $flatValue = false;
	                        }
						}
						
						if(isset($facilityInventories[$current_week_date->format('Y-m-d')])){

							$startDate = $current_week_date->format('Y-m-d');
								
							while(in_array($startDate, $SpectialDates)){
	
								$startDate = Carbon::parse($startDate)->addDays(7)->format('Y-m-d');
							}
	
							$facilityInventories[$lastWeekToday] = $facilityInventories[$startDate];
							$facilityAvailabilities[$lastWeekToday] = $facilityAvailabilities[$startDate];
	
						}
	                   /* $facilityInventories[$lastWeekToday] = $facilityInventories[$current_week_date->format('Y-m-d')];
						$facilityAvailabilities[$lastWeekToday] = $facilityAvailabilities[$current_week_date->format('Y-m-d')];
						*/
	                }
	                if($tempValue === 0){
                    	$tempValue = self::NUMBER_OF_SPOTS;
                	}

	                //fetch operational hours of this facility
	                $hoursOfOperations = HoursOfOperation::where('facility_id', $facility->id)->orderBy('day_of_week', 'ASC')->get();
	                $inventory = new Inventory;
	                $getOperationalHours = $inventory->getOperationalHours($facility->id, $hoursOfOperations);
	                $operationalHours    = $getOperationalHours['operationalHours'];
	                $remainder           = $getOperationalHours['remainder'];
	                $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
                
                    //iterate over dates for the next 90 days starting from the most recent last sunday
	                foreach ($dates as $key => $date) {
	                    $inventories    = [];
	                    $availabilities = [];
	                    $partneravailabilities = [];
	                    $today = $date->format('l');

	                    //date of last week
	                    $lastWeekToday = Carbon::createFromDate($date->format('Y'), $date->format('m'), $date->format('d'))->subDays(7)->format('Y-m-d');

	                    $day_of_week = date('w', strtotime($date->format('Y-m-d')));
	                    $operationalHoursOpenTime=$operationalHours[$today]['open_time'];
	                    
	                    if (count($facilityInventories) && isset($facilityInventories[$lastWeekToday])) {
	                        if($day_of_week==$hours->day_of_week && count($hoursOfOperations) && !count($operationalHoursArray[$today])){
	                        	$inventories    = [];
	                        }
	                        else{
	                        	
                                $inventoriesTemp = json_decode($facilityInventories[$lastWeekToday], true);
	                           	foreach($operationalHoursArray[$today] as $operationalHour){
		                            if($flatValue){
                                 		$inventories[$operationalHour] = $tempValue;
                            		}
                            		else{
	                            		$inventories[$operationalHour] =
	                                		isset($inventoriesTemp[$operationalHour])?$inventoriesTemp[$operationalHour]:self::NUMBER_OF_SPOTS;
	                            	}
		                        }
			                }  
	                    } else {
	                        foreach($operationalHoursArray[$today] as $operationalHour){
		                        if($flatValue){
	                                 $inventories[$operationalHour] = $tempValue;
	                            }
	                            else{
		                            $inventories[$operationalHour] =
		                                self::NUMBER_OF_SPOTS;
		                        }
		                    }
	                    }

	                    /**
	                     * SIMILAR ARRAY AS ABOVE FOR AVAILABILITIES
	                     */
	                    
	                    if (count($facilityInventories) && isset($facilityInventories[$lastWeekToday])) {
	                        if($day_of_week==$hours->day_of_week && count($hoursOfOperations) && !count($operationalHoursArray[$today])){
	                        	$availabilities    = [];
	                        	$partneravailabilities     = [];
	                        }
	                        else{
	                        	$curr_inventory = [];
		                        $curr_availability= [];

		                        if(isset($facilityInventories[$date->format('Y-m-d')]) && isset($facilityAvailabilities[$date->format('Y-m-d')])){
		                                
		                                $curr_inventory = json_decode($facilityInventories[$date->format('Y-m-d')], true);
		                                $curr_availability = json_decode($facilityAvailabilities[$date->format('Y-m-d')], true);
		                        }
	                            $availabilitiesArrayTemp    = json_decode($facilityInventories[$lastWeekToday], true);
	                        	foreach($operationalHoursArray[$today] as $operationalHour){
	                        		$spots = 0;
		                            if(isset($curr_inventory[$operationalHour]) && isset($curr_availability[$operationalHour])){
		                                    $spots = $curr_inventory[$operationalHour] - $curr_availability[$operationalHour];
		                            }

		                            if($flatValue){
		                                 $availabilities[$operationalHour] = $tempValue - $spots;
		                                 $partneravailabilities[$operationalHour] = $tempValue - $spots;
                            		}
		                            else{
			                            $availabilities[$operationalHour] =
			                                (isset($availabilitiesArrayTemp[$operationalHour])?$availabilitiesArrayTemp[$operationalHour]:self::NUMBER_OF_SPOTS) - $spots;
			                            $partneravailabilities[$operationalHour] =     (isset($availabilitiesArrayTemp[$operationalHour])?$availabilitiesArrayTemp[$operationalHour]:self::NUMBER_OF_SPOTS) - $spots;
			                        }                    
		                    	}
							}
	                    } else {
	                        foreach($operationalHoursArray[$today] as $operationalHour){
	                        	if($flatValue){
	                                $availabilities[$operationalHour] = $tempValue;
	                                $partneravailabilities[$operationalHour] = $tempValue;
                            	}
	                            else{
		                            $availabilities[$operationalHour] = self::NUMBER_OF_SPOTS;
		                            $partneravailabilities[$operationalHour] = self::NUMBER_OF_SPOTS;
		                        }                
		                    }
			            }
	                 
	                    $nextDayOfWeek = ($hours->day_of_week < 6)?$hours->day_of_week+1:0;
	                    if(!in_array($date->format('Y-m-d'), $SpectialDates)){
	                    $newInventory = FacilityInventory::firstOrNew(
	                        ['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
	                    if (!$newInventory->exists || $hours->day_of_week == date('w', strtotime($date->format('Y-m-d'))) || count($hoursOfOperations)===0 || ($hours->close_time > '23:59:59' && $nextDayOfWeek == date('w', strtotime($date->format('Y-m-d'))))) {
	                        $newInventory->facility_id  = $facility->id;
	                        $newInventory->availability = json_encode($inventories, JSON_FORCE_OBJECT);
	                        $newInventory->date         = $date->format('Y-m-d');
	                        $newInventory->save();
	                    }

	                    $newAvailability = FacilityAvailability::firstOrNew(
	                        ['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
	                    if (!$newAvailability->exists || $hours->day_of_week == date('w', strtotime($date->format('Y-m-d'))) || count($hoursOfOperations)===0 || ($hours->close_time > '23:59:59' && $nextDayOfWeek == date('w', strtotime($date->format('Y-m-d'))))) {
	                        $newAvailability->facility_id  = $facility->id;
	                        $newAvailability->availability = json_encode($availabilities, JSON_FORCE_OBJECT);
	                        $newAvailability->date         = $date->format('Y-m-d');
	                        $newAvailability->save();
	                    }

	                    //update partners data too
	                    $partnerData = FacilityPartnerAvailabilityCron::firstOrNew(
	                        ['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
	                    if (!$partnerData->exists || $hours->day_of_week == date('w', strtotime($date->format('Y-m-d'))) || count($hoursOfOperations)===0 || ($hours->close_time > '23:59:59' && $nextDayOfWeek == date('w', strtotime($date->format('Y-m-d'))))) {
	                     	
		                     	$partnerData->availability = json_encode($availabilities, JSON_FORCE_OBJECT);
		                        $partnerData->save();
						   }
						}

                            if ($hours->day_of_week == date('w', strtotime($date->format('Y-m-d'))) && in_array($date->format('Y-m-d'), $SpectialDates) && count($hoursOfOperations)!==0) {
                                
                                $hoursOfOperationsAllDay =   HoursOfOperation::where('facility_id', $facility->id)->orderBy('day_of_week', 'ASC')->get(); 
                               if(count($hoursOfOperationsAllDay)>0)
                               {
                                 $hoursOfOperations = HoursOfOperation::where('facility_id', $facility->id)->where('day_of_week',$day_of_week)->orderBy('day_of_week', 'ASC')->get(); 
                                    if(count($hoursOfOperations)<=0)
                                    {

                                         FacilityInventorySpecificDate::where('date',$date->format('Y-m-d'))->delete();
                                    }/*else{
										$special_facilities = Facility::where('id', $facility->id)->with(['availabilities'=>function($query) use ($date){
											return $query->where('date', $date->format('Y-m-d'));
							
										}, 'inventories'=>function($query) use ($date){
											return $query->where('date', $date->format('Y-m-d'));
							
										}])->get();
										$specilal_inventory_to_update = [];
										foreach($special_facilities as $special_facility){
											foreach ($special_facility->inventories as $special_inventory) {
												$specilal_inventory_to_update[date('Y-m-d', strtotime($special_inventory->date))] = 
											 	$special_inventory->availability;
										 	}
										}
										$current_inventory = json_decode($specilal_inventory_to_update[$date->format('Y-m-d')], true);
										$final_inventory = [];
										$final_special_value = 50;
										foreach($current_inventory as $value){
											$final_special_value = $value;
										}
										$today = $date->format('l');
										foreach($operationalHoursArray[$today] as $operationalHour){
											$final_inventory[$operationalHour] =
													isset($current_inventory[$operationalHour])?$current_inventory[$operationalHour]:$final_special_value;
										}
										$newInventory = FacilityInventory::firstOrNew(
											['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
										if ($newInventory->exists) {
											$newInventory->availability = json_encode($final_inventory, JSON_FORCE_OBJECT);
											$newInventory->save();
										}

										$newAvailability = FacilityAvailability::firstOrNew(
											['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
										if ($newAvailability->exists) {
											
											$newAvailability->availability = json_encode($final_inventory, JSON_FORCE_OBJECT);
											$newAvailability->save();
										}
				
										//update partners data too
										$partnerData = FacilityPartnerAvailabilityCron::firstOrNew(
											['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
										if ($partnerData->exists ) {
										 
											$partnerData->availability = json_encode($final_inventory, JSON_FORCE_OBJECT);
											$partnerData->save();
										   }
										
									}*/
                               }
							}
							

							if (in_array($date->format('Y-m-d'), $SpectialDates)) {
								$special_facilities = Facility::where('id', $facility->id)->with(['availabilities'=>function($query) use ($date){
									return $query->where('date', $date->format('Y-m-d'));
					
								}, 'inventories'=>function($query) use ($date){
									return $query->where('date', $date->format('Y-m-d'));
					
								}])->get();
								$specilal_inventory_to_update = [];
								foreach($special_facilities as $special_facility){
									foreach ($special_facility->inventories as $special_inventory) {
										$specilal_inventory_to_update[date('Y-m-d', strtotime($special_inventory->date))] = 
										 $special_inventory->availability;
									 }
								}
								$current_inventory = json_decode($specilal_inventory_to_update[$date->format('Y-m-d')], true);
								$final_inventory = [];
								$final_special_value = 50;
								foreach($current_inventory as $value){
									$final_special_value = $value;
								}
								$today = $date->format('l');
								foreach($operationalHoursArray[$today] as $operationalHour){
									$final_inventory[$operationalHour] =
											isset($current_inventory[$operationalHour])?$current_inventory[$operationalHour]:$final_special_value;
								}
								$newInventory = FacilityInventory::firstOrNew(
									['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
								if ($newInventory->exists) {
									$newInventory->availability = json_encode($final_inventory, JSON_FORCE_OBJECT);
									$newInventory->save();
								}

								$newAvailability = FacilityAvailability::firstOrNew(
									['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
								if ($newAvailability->exists) {
									
									$newAvailability->availability = json_encode($final_inventory, JSON_FORCE_OBJECT);
									$newAvailability->save();
								}
		
								//update partners data too
								$partnerData = FacilityPartnerAvailabilityCron::firstOrNew(
									['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')]);
								if ($partnerData->exists ) {
								 	
								 	$partnerData->availability = json_encode($final_inventory, JSON_FORCE_OBJECT);
										$partnerData->save();								 	
								}
								
							}

					
	                }
	            }

	        });
	    }
        return ['deleted' =>  $respons];
    }
    
}