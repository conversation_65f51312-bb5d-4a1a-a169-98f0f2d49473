<?php

namespace App\Http\Controllers;

use Response;
use App\Models\PartnerSearch;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Exceptions\NotFoundException;
use App\Models\PublicEventSearch;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;
class PublicEventhController extends Controller
{
    
    public function searchPublicEvents(Request $request)
    {
         $this->validate($request, PublicEventSearch::$validateSearch);
         $searchKeyword = $request->search_keyword;
         //$dataSearch = PublicEventSearch::search($searchKeyword)->get();      
         $data = [];
         return Response::json($data, 200);   
    }

    //to run shell script for invoice import database sync
    public function dbSh(Request $request){
      
      ini_set('max_execution_time', 0); //0=NOLIMIT
//      $process = new Process('sh db.sh');
        $process = new Process("echo '211E38st!' | sudo -S su -c './db.sh' -s /bin/sh outworx");
      $process->setTimeout(3600);
      $process->run();
      // executes after the command finishes
      if (!$process->isSuccessful()) {
        throw new ProcessFailedException($process);
      }
      $data['true']=$process->getOutput();
      return $data;
    }
    
    public function downloadfile()
    {
       
            $url = "http://app.ticketmaster.com/dc/feeds/v1/events.json?countryCode=US&apikey=********************************";
            $basePath= realpath(dirname(__FILE__).'/../../..');
            $zip_file = $basePath."/public/events_".date('Y-m-d').".json.gz";
//
            $zip_resource = fopen($zip_file, "w");

            $ch_start = curl_init();
            curl_setopt($ch_start, CURLOPT_URL, $url);
            curl_setopt($ch_start, CURLOPT_FAILONERROR, true);
            curl_setopt($ch_start, CURLOPT_HEADER, 0);
            curl_setopt($ch_start, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch_start, CURLOPT_AUTOREFERER, true);
            curl_setopt($ch_start, CURLOPT_BINARYTRANSFER,true);
            curl_setopt($ch_start, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch_start, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch_start, CURLOPT_SSL_VERIFYPEER, 0); 
            curl_setopt($ch_start, CURLOPT_FILE, $zip_resource);
            $page = curl_exec($ch_start);
            if(!$page)
            {
             echo "Error :- ".curl_error($ch_start);
            }
            curl_close($ch_start);
            $zip_file_txt=$basePath."/public/events_".date('Y-m-d').".txt";
            // open the gzip file
            $gz = gzopen($zip_file, 'rb');
            if (!$gz) {
                throw new \UnexpectedValueException(
                    'Could not open gzip file'
                );
            }

            $dest = fopen($zip_file_txt, 'wb');
            if (!$dest) {
                gzclose($gz);
                throw new \UnexpectedValueException(
                    'Could not open destination file'
                );
            }
            
            // transfer ...
            stream_copy_to_stream($gz, $dest);
            
            gzclose($gz);
            fclose($dest);

    }
    
    public function saveEventToDataBase()
    {
      $expiredEvents=PublicEventSearch::whereDate('end_local_date_api','<',date('Y-m-d'))->orWhere('end_local_date_api','0000-00-00')->orWhere('end_local_date_api',null)->get();
      //removeAlltheseRecords
     
      if(count($expiredEvents)>0)
      {
          foreach($expiredEvents as $event_expire)
          {
            
              $event_expire->delete();
          }
      }
   
      $check=0;
      for($j=0;$j<31;$j++)
      {      
          $date=date('Y-m-d H:i:s', strtotime(date("Y-m-d H:i:s") . ' +'.$j.' day'));
          $date_end=date('Y-m-d H:i:s', strtotime($date . ' +'.($j+1).' day'));   
          $end_date=date("Y-m-d\TH:i:s\Z", strtotime($date_end));
          $data_content='';
            for($i=0;$i<5;$i++)
            {
                $check++;
                   $url = "https://app.ticketmaster.com/discovery/v2/events.json?apikey=********************************&stateCode=NY&countryCode=US&endDateTime=".$end_date."&sort=date,asc&size=200&page=".$i;
                    $options = array(
                    CURLOPT_RETURNTRANSFER => true,   // return web page
                    CURLOPT_HEADER         => false,  // don't return headers
                    CURLOPT_FOLLOWLOCATION => true,   // follow redirects
                    CURLOPT_MAXREDIRS      => 10,     // stop after 10 redirects
                    CURLOPT_ENCODING       => "",     // handle compressed
                    CURLOPT_USERAGENT      => "test", // name of client
                    CURLOPT_AUTOREFERER    => true,   // set referrer on redirect
                    CURLOPT_CONNECTTIMEOUT => 120,    // time-out on connect
                    CURLOPT_TIMEOUT        => 120,    // time-out on response
                ); 

                $ch = curl_init($url);
                curl_setopt_array($ch, $options);

                $content  = curl_exec($ch);

                curl_close($ch);
                
                $data_content=json_decode($content,true);
               
                if((isset($data_content['_embedded']['events'])) && (count($data_content['_embedded']['events'])>0))
                {
                 
                  
                   foreach($data_content['_embedded']['events'] as $events)
                   { 

                      $eventId=isset($events['id'])?$events['id']:'0';
//                      $publicEvent=PublicEventSearch::where("event_id",event_id)->first();
                        $dataEvent = PublicEventSearch::firstOrNew(array('event_id' => $eventId,'is_deleted' =>0));
                        
                        $dataEvent->start_local_date = isset($events['dates']['start']['localDate'])?$events['dates']['start']['localDate']:'';
                        $dataEvent->start_local_date_api = isset($events['dates']['start']['localDate'])?$events['dates']['start']['localDate']:'';
                        $dataEvent->event_name = isset($events['name'])?$events['name']:'';                       
                        $dataEvent->event_id = $eventId;                     
                        $dataEvent->event_url = isset($events['url'])?$events['url']:'';
                       $dataEvent->start_local_date = isset($events['dates']['start']['localDate'])?$events['dates']['start']['localDate']:'';
                       $start_date_local=$dataEvent->start_local_date_api = isset($events['dates']['start']['localDate'])?$events['dates']['start']['localDate']:'';
                       if(($dataEvent->start_local_date_api)< date('Y-m-d'))
                       {
                            $dataEvent->start_local_date = date('Y-m-d');
                            $dataEvent->start_local_date_api = date('Y-m-d');
                       }
                       $isDuplicateEvent = PublicEventSearch::where(array('event_name'=>$dataEvent->event_name,'start_local_date_api' =>$start_date_local,'start_local_time' =>''))->first();
                       if(count($isDuplicateEvent)>0)
                       {
                           continue;
                       }
                       
                       $dataEvent->start_local_time = isset($events['dates']['start']['localTime'])?$events['dates']['start']['localTime']:'';
                        
                       $dataEvent->star_date_time = isset($events['dates']['start']['dateTime'])?$events['dates']['start']['dateTime']:'';
                       if((!isset($events['dates']['end']['localDate'])) || ($events['dates']['end']['localDate']==''))
                       {
                         if(((isset($dataEvent->start_local_date)) && ($dataEvent->start_local_date!='') && (isset($dataEvent->start_local_time)) && ($dataEvent->start_local_time!='') ))
                         {
                           $dataEvent->end_local_date = $dataEvent->start_local_date;
                           $dataEvent->end_local_date_api =  $dataEvent->start_local_date ;
                         }
                       }else{
                          $dataEvent->end_local_date_api = isset($events['dates']['end']['localDate'])?$events['dates']['end']['localDate']:'';
                          $dataEvent->end_local_date =   isset($events['dates']['end']['localDate'])?$events['dates']['end']['localDate']:'';
                       }
                       if(($dataEvent->end_local_date_api)< date('Y-m-d'))
                       {
                            $dataEvent->end_local_date_api = date('Y-m-d');
                            $dataEvent->end_local_date_api = date('Y-m-d');
                       }
                       $dataEvent->end_local_time = isset($events['dates']['end']['localTime'])?$events['dates']['end']['localTime']:'';
                       $dataEvent->end_date_time = isset($events['dates']['end']['dateTime'])?$events['dates']['end']['dateTime']:'';
                      
                       $dataEvent->venue_name = isset($events['_embedded']['venues'][0]['name'])?$events['_embedded']['venues'][0]['name']:'';
                       $dataEvent->longitude = isset($events['_embedded']['venues'][0]['location']['longitude'])?$events['_embedded']['venues'][0]['location']['longitude']:'';
                       $dataEvent->latitude = isset($events['_embedded']['venues'][0]['location']['latitude'])?$events['_embedded']['venues'][0]['location']['latitude']:'';
                       $dataEvent->save();
                     
                  }
                 
                }
            }
      }
      
     
     }
    
 }
