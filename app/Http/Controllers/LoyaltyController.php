<?php
namespace App\Http\Controllers;

use Uuid;
use Auth;
use Artisan;
use Exception;
use Carbon\Carbon;

use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Services\Mailers\UserMailer;

use App\Classes\MagicCrypt;
use App\Classes\LoyaltyProgram;
use App\Classes\AuthorizeNet\Cim;

use App\Models\LoyaltyUserProfile;
use App\Models\LoyaltyUserAccounts;
use App\Models\LoyaltyUserCC;
use App\Models\LoyaltyTransactions;
use App\Models\User;
use App\Models\Reservation;
use App\Models\LoyaltyCode;
use App\Models\GoogleWallet\LoyaltyPass;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Exceptions\DuplicateEntryException;
use App\Exceptions\UserNotAuthorized;
use Illuminate\Database\Eloquent\SoftDeletes;
/**
* Loyalty Controller handling all requests for loyalty programs
*/
class LoyaltyController extends Controller
{

	/**
	 * Return a loggedin user loyalty profile
	 *
	 * @return loyalty check and user profile
	 */
        
        use SoftDeletes;
	protected $cim;

	protected $log;

	const DATE_PHASE2 = '2018-10-10';
        
        const CUSTOM_FIELD_MONTHLY_STATUS = 'IsMonthlyIR'; // Existing monthly User enrollment
        const CONST_ONE = 1;

	public function __construct(LoggerFactory $logFactory)
        {
              $this->log = $logFactory->setPath('logs/loyalty')->createLogger('api_calls');
        }
        
	public function index() {

		$user = Auth::user();
		$profile = '';
		$memberSince = '';
		$rewardNo = '';
		if($user){
			$account = LoyaltyUserAccounts::where('user_id' , '=', $user->id)->orderBy('id', 'DESC')->first();
            if ($account) {
                $user->is_loyalty = LoyaltyProgram::CONST_ONE;
                $user->is_loyalty_active = LoyaltyProgram::CONST_ONE;
            }
            else{
                $user->is_loyalty = LoyaltyProgram::CONST_ZERO;
                $user->is_loyalty_active = LoyaltyProgram::CONST_ZERO;
            }
		}
		if($user->is_loyalty) {
			$dataSet = LoyaltyProgram::getUserProfile($user->email);
			if($dataSet['success']) {
				$profile = $dataSet['data'];
			}
			$account = LoyaltyUserAccounts::where('user_id' , '=', $user->id)->orderBy('id', 'DESC')->first();

			if ($account) {
				$rewardNo = $account->reward_no;
				$memberSince = $account->created_at;

				/*if ($memberSince >= self::DATE_PHASE2) {
					$card = LoyaltyUserCC::where('user_id' , '=', $user->id)
						->withTrashed()
						->first();
					if ($card) {
						$memberSince = $card->created_at;
					}
				}*/
			}
			
		}
		return [
			'is_loyalty' => $user->is_loyalty,
			'is_loyalty_active' => $user->is_loyalty_active,
			'is_loyalty_profile_completed' => $user->is_loyalty_profile_completed,
			'reward_no' => $rewardNo,
			'profile' => $profile,
			'member_since' =>  $memberSince,
			'security_questions' => config('loyalty.security_questions')
		];
	}

	/**
	 * Return a user loyalty profile by admin credentials
	 *
	 * @return array loyalty profile
	 */
	public function getUserProfile($email) {
		$dataSet = LoyaltyProgram::getUserProfile($email);
		return $dataSet['data'];
	}

	 /**
	 * Register a loggedIn user to loyalty profile by admin credentials
	 *
	 * @return loyalty profile
	 */
	public function register(Request $request) {
	   
		$user = Auth::user();
                $message = '';
		$this->log->info("[$user->id]RegisterRequestData: ". json_encode($request->all()));
		
		if ($request->invitation_code) {
			$this->log->info("[$user->id]Register invitation code $request->invitation_code");
			$loyaltyCodeData = LoyaltyCode::where('code', '=', $request->invitation_code)->first();
			if (!$loyaltyCodeData) {
				throw new ApiGenericException('This invitation code is not valid.');
			}
			else if ($loyaltyCodeData->start_date > Carbon::today()->toDateString()) {
				throw new ApiGenericException('This invitation code is not active yet.');
			}
			else if (!$loyaltyCodeData->no_end_date && $loyaltyCodeData->end_date < Carbon::today()->toDateString()) {
				throw new ApiGenericException('This invitation code has expired.');
			}
			$this->log->info("[$user->id]Register valid invitation found");
			$request->request->add(
				[
					'loyalty_code_id' => $loyaltyCodeData->id,
					'profile_completion_bonus' => $loyaltyCodeData->profile_completion_bonus,
					'sign_up_bonus' => $loyaltyCodeData->sign_up_bonus
				]
			);
		} 
		$returnData = array(
			'success' => true,
			'message' => ''
		);
		$newEnrollment = false;
               
		if (!$user->is_loyalty) {
			$this->log->info("[$user->id]Register new enrollment");
			$response = LoyaltyProgram::registerUserById($user->id, $request->loyalty_code_id);
			if (!$response['success']) {
				$this->log->info("[$user->id]RegisterError Enable to enroll ".$response['data']['description']);
				throw new ApiGenericException($response['data']['description']);
			} 
                        
			$newEnrollment = true;
                        if($request->invitation_code != ''){                   
                       
			  $returnData['invitation_code'] = array(
				'success' => $newEnrollment,
				'message' => $message
		        	);
                       }
		}

		$returnData['message'] .= 'User registered to Loyalty program';

		$account = LoyaltyUserAccounts::where('user_id', $user->id)
				->orderBy('id', 'DESC')
				->first();

		// loyalty account validations
		if(!$account) {
			$returnData['success'] = false;
			$returnData['message'] = 'No account found associated with this user.';
			$this->log->info("[$user->id]RegisterError Enable to enroll ".$returnData['message']);
			return $returnData;
		}
		// load signup bonus
		if ($newEnrollment && $request->loyalty_code_id) {
			$this->log->info("[$user->id]Register new user enrollment");
			$loyaltyCode = LoyaltyCode::withTrashed()->find($account->loyalty_code_id);
			$loyaltyPoints = $account->sign_up_bonus;
			$this->log->info('['.$user->id.']Invitation code: '.$loyaltyCode->code.' Bonus: '.$loyaltyPoints);
	    	if ($loyaltyPoints > 0) {
				$response = self::loadPoints($loyaltyPoints);
				if ($response['success']) {
					$this->log->info('['.$user->id.']Signup bonus points added.');	
					// setting up email parameters for user
					$subject_user = "Icon Rewards: ".number_format($loyaltyPoints) ." Points Loaded";
					$totalLoyaltyPoints = 0;
			        $accountData = LoyaltyProgram::getUserAccounts($user->email, $user->id);
			        if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
			            $totalLoyaltyPoints = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
			        }
					$data = [
							'user' => $user,
							'points' => number_format($loyaltyPoints),
							'loyalty_points' => number_format($totalLoyaltyPoints)
						];
					$mailer = new UserMailer();
					$mailer->sendRewardMailTo($user->email, $subject_user, $data, 'email.loyalty.signup-load-points');
					$this->log->info('['.$user->id.']FnRegister load');	

					$returnData['invitation_code'] = array(
					'success' => true,
					'message' => 'Invitation Code successfully added'
				);
				}
			}
		}

		if (!$newEnrollment && $request->loyalty_code_id) {
			$this->log->info("[$user->id]Register old user enrollment");
			if ($account->loyalty_code_id) {
				$returnData['invitation_code']['success'] = false;
				$returnData['invitation_code']['message'] = 'Invitation code already associated with your account';
			}
			else {
				$account->loyalty_code_id = $request->loyalty_code_id;
				$account->sign_up_bonus = $request->sign_up_bonus;

				if (is_null($request->profile_completion_bonus)) {

					$account->profile_completion_bonus = LoyaltyProgram::POINTS_AT_REGISTRATION;
				}
				else {

					$account->profile_completion_bonus = $request->profile_completion_bonus;
				}

				$account->save();

				$returnData['invitation_code'] = array(
					'success' => true,
					'message' => 'Invitation Code successfully added'
				);
				//load signup bonus
				$loyaltyCode = LoyaltyCode::withTrashed()->find($account->loyalty_code_id);
				$loyaltyPoints = $account->sign_up_bonus;
				$this->log->info('['.$user->id.']Invitation code: '.$loyaltyCode->code.' Bonus: '.$loyaltyPoints);
	    		if ($loyaltyPoints > 0) {
					$response = self::loadPoints($loyaltyPoints);
					if ($response['success']) {
						$this->log->info('['.$user->id.']Signup bonus points added.');	
						// setting up email parameters for user
						$subject_user = "Icon Rewards: ".number_format($loyaltyPoints) ." Points Loaded";

						$totalLoyaltyPoints = 0;
				        $accountData = LoyaltyProgram::getUserAccounts($user->email, $user->id);
				        if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
				            $totalLoyaltyPoints = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
				        }
						$data = [
								'user' => $user,
								'points' => number_format($loyaltyPoints),
								'loyalty_points' => number_format($totalLoyaltyPoints)
							];

						$mailer = new UserMailer();
						
						$mailer->sendRewardMailTo($user->email, $subject_user, $data, 'email.loyalty.signup-load-points');
						$this->log->info('['.$user->id.']FnRegister load');	
					}
				}
			}
		}
	
		// loyalty card
		if ($request->card) {
			$cardData = self::decryptCardData($request->card);

			if ($cardData && count($cardData)) {
			
				$cardResponse = $this->addCreditCard($cardData, $user, $account);

				if (!$cardResponse['success']) {

					if (isset($cardResponse['error_type']) && $cardResponse['error_type'] == 'Duplicate') {
						$msg = 'Duplicate Card';

						$returnData['loyalty_card'] = array(
							'success' => false,
							'message' => $cardResponse['message']
						);
						$this->log->info("[$user->id]RegisterError ".$msg);
					}
					else {
						$this->log->info("[$user->id]RegisterError ".$cardResponse['message']);
					//	throw new ApiGenericException($cardResponse['message']);
					}
				}
				else {

					$returnData['loyalty_card'] = array(
						'success' => true,
						'message' => $cardResponse['message'],
						'isFirstCard' => $cardResponse['isFirstCard'],
						'isCardPointLoaded' =>  $cardResponse['isCardPointLoaded']
					);

					if (isset($request->iq_code) && $request->iq_code) {
						LoyaltyProgram::addReservationToLoyalty($user->id, $request->iq_code);
					}
				}
			}
			else {
				$this->log->info("[$user->id]RegisterError, We were unable to add your credit card in Icon Rewards, please try again later");
				throw new ApiGenericException('We were unable to add your credit card in Icon Rewards, please try again later');
			}
		}

		// if user registerd without user info
		if (
			 
			 isset($request->userInfo) 
			&& $request->userInfo
		) {

			// birthday date validations
			if ($request->userInfo['birth_date'] && $request->userInfo['birth_date'] > Carbon::now()) {
				$returnData['success'] = false;
				$returnData['message'] .= 'Invalid birth date, Profile not updated';
				return $returnData;
			}
			if ($request->userInfo['birth_date']) {
				$dob = date('Y-m-01', strtotime($request->userInfo['birth_date']));
			}
			else {
				$dob = '';
			}

			$fullname = $user->name;
			$dataSet = array(
				"sms" =>  $request->userInfo['phone'],
				"voice" => $request->userInfo['phone'],
				"gender" => $request->userInfo['gender'],
				"firstName" => $this->preString(' ', $fullname),
				"lastName" => $this->postString(' ', $fullname),
				"birthDate" => $dob,
				"address" => array(
					"zip" => $request->userInfo['zip']
				),
			   "customFields" => $request->userInfo['custom_fields']
			);
			
			$profileResponse = $this->updateProfile($dataSet);

			if ($profileResponse['success']) {
				$returnData['message'] .= (isset($profileResponse['message']) && $profileResponse['message']) ? ', '.$profileResponse['message'] : ', Loyalty profile  updated';
			}
			else {
				$returnData['success'] = false;
				$returnData['message'] .= (isset($profileResponse['message']) && $profileResponse['message']) ? ', '.$profileResponse['message'] : ', Loyalty profile not updated';
			}
		}

		$user = User::find($user->id);

		$returnData['is_loyalty'] = $user->is_loyalty;
		$returnData['is_loyalty_active'] = $user->is_loyalty_active;
		$returnData['is_loyalty_profile_completed'] = $user->is_loyalty_profile_completed;
                
                if (isset($request->iq_code) && $request->iq_code) {
				LoyaltyProgram::addReservationToLoyalty($user->id, $request->iq_code);
		}

		// add card for future reservations
		try {
			if ($request->add_to_account && $request->card && $cardData) {
				$this->log->info('['.$user->id.']User registration save card for future');
				if ( isset($cardData[7]) && $cardData[7] &&  $cardData[7] != '' ) {
					$name =  $cardData[7];
				}
				else {
					$name = $user->name;
				}
				
				$number = isset($cardData[8]) ? $cardData[8] : '';
				$security_code = isset($cardData[9]) ? $cardData[9] : '';
				$expiration = isset($cardData[5]) ? $cardData[5] : '';

				$resp = $this->addAccountCreditCard($number, $expiration, $security_code, $name);

				if(!$resp) {
					$returnData['account_card'] = array(
						'success' => false,
						'message' => 'We are unable to save this card for future reservations.'
					);
				} else {
					$returnData['account_card'] = array(
						'success' => true,
						'message' => 'Card successfully saved for future reservations.'
					);
				}
				$this->log->info('['.$user->id.']Register save card for future: '.$returnData['account_card']['message']);
			}

			$this->log->info('['.$user->id.']Register success');
			return $returnData;

		} catch (Exception $e) {
			// error handling for account card
			$returnData['account_card'] = array(
				'success' => false,
				'message' => $e->getMessage()
			);
			return $returnData;
		}
		$this->log->info('['.$user->id.']Register success');
		return $returnData;
	}

	/**
	 * Update loyalty user profile
	 *
	 * @return loyalty profile
	 */
	public function updateUserProfile(Request $request) {

		$user = User::find(Auth::user()->id);
		$this->log->info('['.$user->id.']UpdateProfileRequestData '.json_encode($request->all()));
		if (!$user->is_loyalty) {
			throw new NotFoundException("User doesn't have loyalty profile.");
		}
		
		if ($request->birth_date && $request->birth_date > Carbon::now()) {
			throw new NotFoundException("Invalid birth date.");
		}

		$fullname = $user->name;
		if($request->birth_date) {
			$dob = date('Y-m-01', strtotime($request->birth_date));
		}
		else {
			$dob = '';
		}
                  
		$dataSet = array(
			"sms" =>  $request->phone,
			"voice" => $request->phone,
			"firstName" => $this->preString(' ', $fullname),
			"lastName" => $this->postString(' ', $fullname),
			"birthDate" => $dob,
			"gender" => $request->gender,
			"address" => array(
				"zip" => $request->zip
			),
		   "customFields" => $request->custom_fields
		);
	
		$response = $this->updateProfile($dataSet);

		if (!$response['success']) {
			$this->log->info('['.$user->id.']RegisterProfileError Could not update');
			throw new ApiGenericException("Could not update loyalty profile.");
		}
		
		$this->log->info('['.$user->id.']RegisterProfile success');
		return $response;
	}

	/**
	 * Return a user loyalty accounts
	 *
	 * @return array accounts
	 */
	public function accounts() {

		$user = Auth::user();
		if($user->is_loyalty) {
			$dataSet = LoyaltyProgram::getUserAccounts($user->email, $user->id);
			if($dataSet['success']) {
				return $dataSet['data'];
			}
			else {
				$msg = isset($dataSet['data']['description']) && $dataSet['data']['description'] ? $dataSet['data']['description'] : 'Could not get accounts';
				throw new ApiGenericException($msg, $dataSet['status']);			
			}
		} else {
			throw new NotFoundException('User not registered with loyalty program.');
		}
	}

	public function addCard(Request $request) {

		$this->validate(
			$request, [
				'card' => 'required'
			]
		);
		$user = User::find(Auth::user()->id);
		$this->log->info('['.$user->id.']AddCardRequestData '.json_encode($request->all()));

		if ($request->invitation_code) {
			$this->log->info("[$user->id]AddCard invitation code $request->invitation_code");
			$loyaltyCodeData = LoyaltyCode::where('code', '=', $request->invitation_code)->first();
			if (!$loyaltyCodeData) {
				throw new ApiGenericException('This invitation code is not valid.');
			}
			else if ($loyaltyCodeData->start_date > Carbon::today()->toDateString()) {
				throw new ApiGenericException('This invitation code is not active yet.');
			}
			else if (!$loyaltyCodeData->no_end_date && $loyaltyCodeData->end_date < Carbon::today()->toDateString()) {
				throw new ApiGenericException('This invitation code has expired.');
			}
			$this->log->info("[$user->id]AddCard valid invitation found");
			$request->request->add(
				[
					'loyalty_code_id' => $loyaltyCodeData->id,
					'profile_completion_bonus' => $loyaltyCodeData->profile_completion_bonus,
					'sign_up_bonus' => $loyaltyCodeData->sign_up_bonus
				]
			);
		}
		// loyalty validations
		if(!$user->is_loyalty) {
			throw new NotFoundException('User not registered with loyalty program');
		}

		$account = LoyaltyUserAccounts::where('user_id', $user->id)
					->orderBy('id', 'DESC')
					->first();

		// loyalty account validations
		if(!$account) {
			throw new NotFoundException('No account found associated with this user');
		}

		if (!$user->is_loyalty_active && $request->loyalty_code_id) {
			$this->log->info("[$user->id]AddCard invitation code");
			if ($account->loyalty_code_id) {
				$invitationResponse['success'] = false;
				$invitationResponse['message'] = 'Invitation code already associated with your account';
			}
			else {
				$account->loyalty_code_id = $request->loyalty_code_id;
				$account->sign_up_bonus = $request->sign_up_bonus;

				if (is_null($request->profile_completion_bonus)) {

					$account->profile_completion_bonus = LoyaltyProgram::POINTS_AT_REGISTRATION;
				}
				else {

					$account->profile_completion_bonus = $request->profile_completion_bonus;
				}

				$account->save();

				$invitationResponse = array(
					'success' => true,
					'message' => 'Invitation Code successfully added'
				);
			}
		}

		$cardData = self::decryptCardData($request->card);

		if (!($cardData && count($cardData))) {
			throw new NotFoundException('Invalid card details');
		}
		$cardResponse = $this->addCreditCard($cardData, $user, $account, $request->loyalty_code_id?1:0);

		if (!$cardResponse['success']) {
			if (isset($cardResponse['error_type']) && $cardResponse['error_type'] == 'Duplicate') {
				$msg = 'Duplicate Card';
				$this->log->info('['.$user->id.']AddCardError Duplicate card');
			}
			else {
				$this->log->info('['.$user->id.']AddCardError: '.$cardResponse['message']);
				throw new ApiGenericException($cardResponse['message']);
			}
		} else {

			if (isset($request->iq_code) && $request->iq_code) {
				LoyaltyProgram::addReservationToLoyalty($user->id, $request->iq_code);
			}
		}

		$user = User::find($user->id);
		
		$cardResponse['is_loyalty'] = $user->is_loyalty;
		$cardResponse['is_loyalty_active'] = $user->is_loyalty_active;
		$cardResponse['is_loyalty_profile_completed'] = $user->is_loyalty_profile_completed;

		if (isset($invitationResponse)) {
			$cardResponse['invitation_code'] = $invitationResponse;
		}

		try {
			if ($request->add_to_account) {
				$this->log->info('['.$user->id.']AddCard for future reservations');
				if ( isset($cardData[7]) && $cardData[7] &&  $cardData[7] != '' ) {
					$name =  $cardData[7];
				}
				else {
					$name = $user->name;
				}

				$number = isset($cardData[8]) ? $cardData[8] : '';
				$security_code = isset($cardData[9]) ? $cardData[9] : '';
				$expiration = isset($cardData[5]) ? $cardData[5] : '';

				$resp = $this->addAccountCreditCard($number, $expiration, $security_code, $name);

				if(!$resp) {
					$cardResponse['account_card'] = array(
						'success' => false,
						'message' => 'We are unable to save this card for future reservations.'
					);
				} else {
					$cardResponse['account_card'] = array(
						'success' => true,
						'message' => 'Card successfully saved for future reservations.'
					);
				}
				$this->log->info('['.$user->id.']AddCard for future reservations: '.$cardResponse['account_card']['message']);
			}

			return $cardResponse;

		} catch (Exception $e) {
			$cardResponse['account_card'] = array(
				'success' => false,
				'message' => $e->getMessage()
			);
			return $cardResponse;
		}
	}

	protected function addAccountCreditCard($card_number, $expiration, $security_code, $name)
    {

        $cim = new Cim();
       	$user = Auth::user();
        $cim->setUser($user)
            ->isReservation()
            ->setBillingAddress($this->getName($name))
            ->setCreditCard($card_number, $expiration, $security_code);

        // Create payment or customer profile depending on whether we have an existing profile already
        return $user->cim
                ? $cim->createPaymentProfile()->executePaymentProfileRequest()
                : $cim->createCustomerProfile()->executeCustomerProfileRequest();

    }


	// get user cards
	public function getCards() {
		$user = Auth::user();
		return LoyaltyUserCC::where('user_id', $user->id)->orderBy('id', 'DESC')->get();
	}

	// get user card
	public function viewCard($id) {
		$user = Auth::user();
		return LoyaltyUserCC::where('user_id', $user->id)
				->where('id', $id)
				->first();
	}

	// get user card
	public function deleteCard($id) {
		$user = Auth::user();
		$card =  LoyaltyUserCC::where('user_id', $user->id)
				->where('id', $id)
				->firstOrFail();

		$requestData = array(
			"accountId" => $card->account->account_id,
			"emailId" => $user->email,
			"alias" => $card->cc_id
		);

		$response = LoyaltyProgram::deleteCard($user->id, $requestData);

		if ($response['success']) {
			$card->delete();
			return $response;

		} else {
			$msg = isset($response['data']['description']) ? $response['data']['description'] : 'Could not remove card';
			throw new ApiGenericException($msg, $response['status']);	
		}
	}

	// get user accounts history
	public function getHistory(Request $request) {
		$user = Auth::user();
		$account = LoyaltyUserAccounts::where('user_id' , '=', $user->id)->orderBy('id', 'DESC')->first();
            if ($account) {
                $user->is_loyalty = LoyaltyProgram::CONST_ONE;
                $user->is_loyalty_active = LoyaltyProgram::CONST_ONE;
            }
            else{
                $user->is_loyalty = LoyaltyProgram::CONST_ZERO;
                $user->is_loyalty_active = LoyaltyProgram::CONST_ZERO;
            }
		// loyalty validations
		if(!$user->is_loyalty) {
			throw new NotFoundException('User not registered with loyalty program.');
		}

		//$account = LoyaltyUserAccounts::where('user_id', $user->id)
		//			->orderBy('id', 'DESC')
		//			->first();

		// loyalty account validations
		if(!$account) {
			throw new NotFoundException('No account found associated with this user.');
		}



		$requestData = array(
			"accountId" => $account->account_id,
			"emailId" => trim($user->email),
		// 	"limit" => $request->limit,
		// 	"page" => $request->page
		);

		$response = LoyaltyProgram::getAccountHistory($user->id, $requestData);
		$profileCompletionDate = null;
		$profilePoint = $signUpPoint = 0;
		if ($response['success'] && isset($response['data']['history'])) {

			$historyData = array();

			if ($user->is_loyalty_active) {
				if ($account->sign_up_bonus) {

					if (!$account->sign_up_completion_date) {
						$this->log->info('['.$user->id.']History setting signup date for old users ');
						$signUpCompletionDate = $account->created_at;
						$account->sign_up_completion_date = $account->created_at;
						$account->save();
					}

					$signUpHistory = array(
						'date' => date('Y-m-d h:i:s', strtotime($account->sign_up_completion_date)),
						'type' => 'Load',
						'amount' => $account->sign_up_bonus,
						'message' => 'Signup bonus points awarded'
					);
					$signUpPoint = $account->sign_up_bonus;
					array_push($historyData, $signUpHistory);
				}
			}
			if ($user->is_loyalty_profile_completed==LoyaltyProgram::PROFILE_COMPLETED) {	
				$profileBonus = $account->loyalty_code_id ? $account->profile_completion_bonus :  LoyaltyProgram::POINTS_AT_REGISTRATION ;

				$profilePoint = $profileBonus;
				if ($profileBonus) {

					$keyIndex = array_push($historyData, array(
						'date' => date('Y-m-d h:i:s', strtotime($account->profile_completion_date)),
						'type' => 'Load',
						'amount' => $profileBonus,
						'message' => 'Profile completion bonus points awarded'
					));
					$profileCompletionDate = &$historyData[$keyIndex - 1]['date'];
				}
			}
			foreach ($response['data']['history'] as $data) {

				if(is_null($profileCompletionDate) && $user->is_loyalty_profile_completed && $data['type'] === 'Load') {
					$this->log->info('['.$user->id.']History setting profile completion date for old users ');
						
					$profileCompletionDate = $data['date'];
					$account->profile_completion_date = $data['date'];
					$account->save();
				}

				$points =  isset($data['transactionAmount']['amount']) ? $data['transactionAmount']['amount'] : 0;

				if ($points == 0)
					continue;

				if ($data['type'] == "Load" && $points == $profilePoint) {
					$profilePoint = 0;
					continue;
				}

				if ($data['type'] == "Load" && $points == $signUpPoint) {
					$signUpPoint = 0;
					continue;
				}

				$record = array();
				$record['type'] = $data['type'];
				$record['amount'] = $points;
				$record['date'] = $data['date'];
				$msg = $data['store'];
				
				$record['message'] = $msg;
				array_push($historyData, $record);
			};

			return ['history' => $historyData];

		} else {
			$msg = isset($response['data']['description']) ? $response['data']['description'] : 'Could not find account history';
			throw new ApiGenericException($msg, $response['status']);	
		}
	}

   	// get user loyalty digital card details
        // get user loyalty digital card details
	public function getDigitalCard() {
		$user = Auth::user();
 		
 		$profile = '';
		$memberSince = '';
		$rewardNo = '';
		$loyaltyPoints = '';
		$cardExists = false;
		// loyalty validations
		if(!$user->is_loyalty) {
			throw new NotFoundException('User not registered with loyalty program.');
		}

		$dataSet = LoyaltyProgram::getUserProfile($user->email);
		if($dataSet['success']) {
			$profile = $dataSet['data'];
		}
		
		$account = LoyaltyUserAccounts::where('user_id', $user->id)
					->orderBy('id', 'DESC')
					->first();

		$accountData = LoyaltyProgram::getUserAccounts($user->email, $user->id);
            if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
                $loyaltyPoints = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
            }


		// loyalty account validations
		if(!$account) {
			throw new NotFoundException('No account found associated with this user.');
		} 
              

    
    $loyalty = new \LoyaltyObject();
    $service = $loyalty->getWalletLoyaltyObject();

 

    $loyaltypass = LoyaltyPass::where('user_id',$user->id)->first();
    $response = ''; 
   // var_dump($loyaltypass['loyalty_object_id']); die("hello");
    if(isset($loyaltypass)){
    try {
       $response = $service->loyaltyobject->get($loyaltypass["loyalty_object_id"]);
    } catch(Exception $e){
    //  var_dump('['.$user->id.'] There was error updating'.$loyaltyPoints.' points to loyalty object for //user'.$e->getMessage().$e->getFile().$e->getLine());//die("hello");
       throw new ApiGenericException('There was some error adding points to loyalty card',  422);
    }

    if ($response['hasUsers']) {
        $cardExists = true;
    } else {
       $loyaltypass->delete();

    } }


		return  array(
			"account_id" => $account->account_id,
			"account_no" => $account->account_no,
			"reward_no" => $account->reward_no,
			"issuer_name" => "Icon Rewards",
			"program_name" => "Icon GO Rewards",
                        "loyalty_class" => "01_Loyalty",
                        "phone"=>  config('icon.webphone'),
			"profile" => $profile,
			"member_since" => $account->created_at,
			"loyalty_points" => $loyaltyPoints,
			"cardExists"=> $cardExists
		);
	}



	public function updateCard(Request $request, $id) {

		$response = $this->addCard($request);

		if($response) {
			$this->deleteCard($id);
		}
		return $response;
	}

	// update profile for loyalty account
	protected function updateProfile($dataSet = array()) {

		$user = User::find(Auth::user()->id);
		$profileFlag = $user->is_loyalty_profile_completed;

		$user->is_loyalty_profile_completed = LoyaltyProgram::PROFILE_REQUESTED;
		$user->save();

		$this->log->info('['.$user->id.']FnUpdateProfile data: '.json_encode($dataSet));

		$configCustomFields = config('loyalty.security_question_alias');

		$customFields = array(
			array(
				'name' => LoyaltyProgram::CUSTOM_FIELD_STATUS,
				'value' => $user->is_loyalty_active
			)
		);
                
                if($user->is_monthly_ir == '1')
                {
                    array_push($customFields, array(
						"name" => self::CUSTOM_FIELD_MONTHLY_STATUS,
						"value" => self::CONST_ONE)
		    );
                }
		if (isset($dataSet['customFields']) && is_array($dataSet['customFields'])) {
			foreach ($dataSet['customFields'] as $key => $field) {
				if (in_array($field['name'], $configCustomFields)) {
					array_push($customFields, array(
						"name" => $field['name'],
						"value" => $field['value'] )
					);
				}
			}
		}

		$dataSet['customFields'] = $customFields;

		$this->log->info('['.$user->id.']FnUpdateProfile generatedData: '.json_encode($dataSet));
		$responseProfile = LoyaltyProgram::updateUserProfile($user->email, $dataSet);

		$responseProfile['pointLoaded'] = 0;
		$user->is_loyalty_profile_completed = $profileFlag;

		$user->save();

		if (!$responseProfile['success']) {
			$responseProfile['message'] = 'Could not update user profile';
			return $responseProfile;
		}

		// profile completed check
		if ($user->is_loyalty_profile_completed ==  LoyaltyProgram::PROFILE_PENDING) {

			if (isset($dataSet['birthDate']) && $dataSet['birthDate'] 
				&& isset($dataSet['gender']) && $dataSet['gender'] 
				&& isset($dataSet['address']['zip']) && $dataSet['address']['zip']
				&& (isset($dataSet['customFields'][1]['value']) && $dataSet['customFields'][1]['value'] !== ''
					&& isset($dataSet['customFields'][2]['value']) && $dataSet['customFields'][2]['value'] !== ''
				)

			) {
				$user->is_loyalty_profile_completed = LoyaltyProgram::PROFILE_REQUESTED;
				$user->save();
				$account = LoyaltyUserAccounts::where('user_id' , '=', $user->id)->orderBy('id', 'DESC')->first();
				
				if ($account->loyalty_code_id) {
					$this->log->info('['.$user->id.']Loyalty Invitation Code found');
					$loyaltyCode = LoyaltyCode::withTrashed()->find($account->loyalty_code_id);
					$loyaltyPoints = $account->profile_completion_bonus;

    				$this->log->info('['.$user->id.']Invitation code: '.$loyaltyCode->code.' Bonus: '.$loyaltyPoints);
				} else {
					$loyaltyPoints = LoyaltyProgram::POINTS_AT_REGISTRATION;
					
				}
				if ($loyaltyPoints > 0) {
					$response = self::loadPoints($loyaltyPoints);

					if ($response['success']) {
						$this->log->info('['.$user->id.']Profile completion points added.');
						$user->is_loyalty_profile_completed = LoyaltyProgram::PROFILE_COMPLETED;
						$user->save();

						$account->profile_completion_date = Carbon::now();
						$account->save();

						$responseProfile['pointLoaded'] = 1;

						// setting up email parameters for user
						$subject_user = "Icon Rewards: ".number_format($loyaltyPoints) ." Points Loaded";

						$totalLoyaltyPoints = 0;
			            $accountData = LoyaltyProgram::getUserAccounts($user->email, $user->id);
			            if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
			                $totalLoyaltyPoints = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
			            }
						$data = [
							'user' => $user,
							'points' => number_format($loyaltyPoints),
							'loyalty_points' => number_format($totalLoyaltyPoints)
						];

						$mailer = new UserMailer();
					
						$mailer->sendRewardMailTo($user->email, $subject_user, $data, 'email.loyalty.load-points');
						$this->log->info('['.$user->id.']FnUpdateProfile load');
					} else {
						$this->log->info('['.$user->id.']FnUpdateProfileError unable to load loyalty points');
						$responseProfile['message'] = 'Profile udpated, unable to load loyalty points';	
					}
				}
			}
		}
		
		return $responseProfile;
	}

	/**
	 * Load points in users loyalty account TEST ONLY
	 *
	 * @return array 
	 */
	public static function loadPoints($amount = 0, $userId = 0) {
		
		if ($userId) {
			$user = User::find($userId);
		} else {
			$user = User::find(Auth::user()->id);
		}
		
		// loyalty validations
		if(!$user->is_loyalty) {
			throw new NotFoundException('User not registered with loyalty program');
		}

		$account = LoyaltyUserAccounts::where('user_id', $user->id)
					->orderBy('id', 'DESC')
					->first();

		// loyalty account validations
		if(!$account) {
			throw new NotFoundException('No account found associated with this user.');
		}
		$dataSet = array(
			"sva" => $account->account_no,
			"amount" => $amount
		);
		
		return LoyaltyProgram::loadPoints($dataSet, $user->id);
	}

	/**
	 * add credit card details for loyalty account
	 *
	 * @return array 
	 *$loyaltyInvitation is to check if loyalty invitation code is posted in request 
	 */
	protected function addCreditCard($cardData, $user, $account, $loyaltyInvitation = 0) {
		$token = isset($cardData[0]) ? $cardData[0] : '';
		$type = isset($cardData[1]) ? $cardData[1] : '';
		$length = isset($cardData[2]) ? $cardData[2] : '';
		$bin = isset($cardData[3]) ? $cardData[3] : '';
		$lastFour = isset($cardData[4]) ? $cardData[4] : '';
		$expiration = isset($cardData[5]) ? $cardData[5] : '';
		$zip = isset($cardData[6]) ? $cardData[6] : '';
		
		$dataSet = array(
			"token" => $token,
			"bin" => $bin,
			"lastFour" => $lastFour,
			"length" => $length,
			"expiration" => $expiration,
			"billing" => array (
				"cardholder" => 'NA',
				"address" => array (
					"zip" => $zip
				)
			)
		);

		$requestData = array(
			"accountId" => $account->account_id,
			"emailId" => $user->email
		);

		$response = LoyaltyProgram::addCard($dataSet, $user->id, $requestData);

		if (!$response['success']) {

			if (isset($response['timeout']) && $response['timeout']) {
				$response['message'] = 'But we are unable to add a credit card at the moment – Please add your credit card details again in order to take full advantage of the  benefits of Icon Rewards';
				$response['error_type'] = 'Timeout';
			}
			else if (isset($response['data']['name']) && $response['data']['name'] == 'CardAuthorizationFailed') {
				$response['message'] = 'Can’t add credit card – We can not seem to validate this credit card. Please check your details and try again';
				$response['error_type'] = 'Failed';
			}
			else if (isset($response['data']['name']) && $response['data']['name'] == 'ApiError') {
				$response['message'] =  'We are unable to register this card in Icon Rewards, please try another card';
				$response['error_type'] = 'Duplicate';
			}
			else {
				$response['message'] = 'We are unable to register this card in Icon Rewards, please try another card';
				$response['error_type'] = 'Failed';
			}
    		$this->log->info('['.$user->id.']FnAddCardError: '.$response['message'].' Card Details: '. json_encode($dataSet));
			return $response;
		}
    	$this->log->info('['.$user->id.']FnAddCard success.');

		$isFirstCard = 0;
		$isCardPointLoaded = 0;
       
		$userCCData = LoyaltyUserCC::where('user_id', '=', $user->id)->withTrashed()->first(); 

		$userCC = new LoyaltyUserCC;
    	$userCC->user_id = $user->id;
    	$userCC->loyalty_account_id = $account->id;
    	$userCC->type = $type;
    	$userCC->length = $length;
    	$userCC->bin = $bin;
    	$userCC->last_four = $lastFour;
    	$userCC->expiration = $expiration;
    	$userCC->zip = $zip;
    	$userCC->save();
    	$message = 'Your credit card details are added successfully';
    	$this->log->info('['.$user->id.']Credit card added successfully in loyalty user credit cards.');
      
		if (!$userCCData){
			$this->log->info('['.$user->id.']Adding first credit card.');
			$isFirstCard = 1;
			$user->is_loyalty_active = LoyaltyProgram::CONST_ONE;
			$user->save();
			$message = 'Your credit card details are added successfully and now you can earn points even when you park without reservation';
			// update loyalty status:
			$profileRsp = LoyaltyProgram::getUserProfile($user->email);


			if ($profileRsp['success'] && $profileRsp['data']) {
				$profile = $profileRsp['data'];
			
				$dataSet = array(
					"sms" =>  isset($profile['sms']) ? $profile['sms'] : ''  ,
					"voice" => isset($profile['voice']) ? $profile['voice'] : ''  ,
					"gender" => isset($profile['gender']) ? $profile['gender'] : ''  ,
					"firstName" => isset($profile['firstName']) ? $profile['firstName'] : '',
					"lastName" => isset($profile['lastName']) ? $profile['lastName'] : '' ,
					"birthDate" => isset($profile['birthDate']) ? $profile['birthDate'] : '',
					"address" => isset($profile['address']) ? $profile['address'] : '',
				   "customFields" => isset($profile['customFields']) ? $profile['customFields'] : ''
				);
				$this->log->info('['.$user->id.']FnAddCard FirstCard '.json_encode($dataSet));

				foreach ($dataSet['customFields'] as $key => $field) {
					
					if ($field['name'] == LoyaltyProgram::CUSTOM_FIELD_STATUS) {
						$dataSet['customFields'][$key]['value'] = LoyaltyProgram::CONST_ONE;

						LoyaltyProgram::updateUserProfile($user->email, $dataSet);
						break;
					}
				}
			}
		} 
    	$getCardsResponse = LoyaltyProgram::getCards($user->id, $requestData);
        
    	if ($getCardsResponse['success'] && isset($getCardsResponse['data']['aliases'])) {
    		foreach ($getCardsResponse['data']['aliases'] as $alias) {

    			if(!isset($alias['bankCard'])) continue;

    			$card = LoyaltyUserCC::where('cc_id', '=', $alias['id'])->first();
    			if ($card) continue;
    			if (substr($alias['bankCard'], -4) == $userCC->last_four) {
    				$userCC->cc_id = $alias['id'];
    				$userCC->save();
    				break;
    			}

    		}
    	}

    	if ($isFirstCard && $account->loyalty_code_id && $account->sign_up_bonus && $loyaltyInvitation) {
    		$this->log->info('['.$user->id.']Signup bonus initiated');
    		$loyaltyCodeData = LoyaltyCode::withTrashed()->find($account->loyalty_code_id);
    		
    		$loyaltyPoints = $account->sign_up_bonus;

			$this->log->info('['.$user->id.']Invitation code: '.$loyaltyCodeData->code.' Bonus: '.$loyaltyPoints);
			$res = self::loadPoints($loyaltyPoints, $user->id);
			if (!$res['success']) {
				$response['loyalty_code'] = array (
					'success' => false,
					'bonus' => 0,
					'message' => 'There was a problem adding signup bonus to your Icon Rewards account.'
				);
				$this->log->info('['.$user->id.']There was a problem adding signup bonus to your Icon Rewards account.');
			}
			else {

				$account->sign_up_completion_date = Carbon::now();
				$account->save();
				$response['loyalty_code'] = array (
					'success' => true,
					'bonus' => $loyaltyPoints,
					'message' => 'Signup bonus successfully added to your Icon Rewards account.'
				);
				$this->log->info('['.$user->id.']Bonus Loaded');

				// setting up email parameters for user
				$subject_user = "Icon Rewards: ".number_format($loyaltyPoints) ." Points Loaded";

				$totalLoyaltyPoints = 0;
	            $accountData = LoyaltyProgram::getUserAccounts($user->email, $user->id);
	            if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
	                $totalLoyaltyPoints = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
	            }
				$data = [
					'user' => $user,
					'points' => number_format($loyaltyPoints),
					'loyalty_points' => number_format($totalLoyaltyPoints)
				];

				$mailer = new UserMailer();
			
				$mailer->sendRewardMailTo($user->email, $subject_user, $data, 'email.loyalty.signup-load-points');
				
			}
    	}

    	$response['isFirstCard'] = $isFirstCard;
    	$response['isCardPointLoaded'] = $isCardPointLoaded;
    	$response['message'] = $message;
    	return $response;
	}

	// for test call loyalty cron by url
	public function cron() {
		Artisan::call('cron:credit-loyalty-points');
		die('success');
	}

	public function test() {
		// code for custom profile fields updates
		// $users = User::where('is_loyalty', '=', 1)
		// 				->where('is_loyalty_active', '=', 1)
		// 				->get();

		// foreach ($users as $key => $user) {

		// 	$profileRsp = LoyaltyProgram::getUserProfile($user->email);
		// 	if ($profileRsp['success'] && $profileRsp['data']) {
		// 		$profile = $profileRsp['data'];
		// 		foreach ($profile['customFields'] as $key => $field) {
		// 			if ($field['name'] == LoyaltyProgram::CUSTOM_FIELD_STATUS) {

		// 				if($field['value'] != LoyaltyProgram::CONST_ONE) {
		// 					$profile['customFields'][$key]['value'] = LoyaltyProgram::CONST_ONE;
		// 					LoyaltyProgram::updateUserProfile($user->email, $profile);
		// 				}
		// 			}
		// 		}
		// 	}
		// }
		// die('success');
	}

	// decrypt card data
	public static function decryptCardData($requestData) {
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($requestData);
    	return explode(':', $decryptedNonce);
	}

	public function getName($name)
    {
        $splitName = explode(' ', $name);

        return [
            'first_name' => array_shift($splitName),
            'last_name' => count($splitName) ? implode(' ', $splitName) : ''
        ];
    }
      public function updateLoyaltyObject(Request $request){
	   $loyalty = new \LoyaltyObject();
           $service = $loyalty->getWalletLoyaltyObject();
//`   var_dump($request); die("helo");
   //$data = $service->loyaltyobject->get('icon22298012');
  // $response = $service->loyaltyobject->get('3323399842768045437.icon7107');
   //$response = json_decode($data, true);
          $dataSet = ['loyaltyPoints'=> '200', 'loyaltyObjectId'=> $request->loyalty_object_id];
           try {
          $response = $service->loyaltyobject->update($request->loyalty_object_id, \Loyalty::updateLoyaltyObjectPoints('3323399842768045437','icon22298012', $dataSet));
          } catch(Exception $e){
             var_dump($e->getMessage()); die("hello");
          
           }
          if(!is_object($response)){
 	    
           //  return "hello wordl";
          } else {
            return [
               'success' => true];
             
          }
      }
}
