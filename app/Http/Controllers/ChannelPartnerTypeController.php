<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Models\ChannelPartnerType;

use App\Exceptions\ApiGenericException;

class ChannelPartnerTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $channelPartnerTypes = ChannelPartnerType::where('status', 1)->get();
        return $channelPartnerTypes;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->validate($request, ChannelPartnerType::$storeValidationRules);

        $channelPartnerType = new ChannelPartnerType();

        $channelPartnerType->fill($request->only(['name', 'description', 'status']));
        $result = $channelPartnerType->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Channel Partner Type Could Not Be Saved');
        }

        return [
            'success' => true,
            'channelPartnerType' => $channelPartnerType
        ];
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        if (!$id) {
            throw new ApiGenericException('Please Provide Valid Channel Partner Type Id');
        }

        $channelPartnerType = ChannelPartnerType::where(['id' => $id, 'status' => 1])->first();

        if (!$channelPartnerType) {
            throw new ApiGenericException('Couldn\'t Find Any Channel Partner Type With Given Id');
        }

        return [
            'success' => true,
            'channelPartnerType' => $channelPartnerType
        ];
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int                      $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $this->validate($request, ChannelPartnerType::$updateValidationRules);

        $channelPartnerType = ChannelPartnerType::where('id', $request->id)->first();

        if (!$channelPartnerType) {
            throw new ApiGenericException('Couldn\'t Find Any Channel Partner Type With Given Id');
        }

        $channelPartnerType->fill($request->only(['name', 'description', 'status']));
        $result = $channelPartnerType->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Channel Partner Could Not Be Updated');
        }

        return [
            'success' => true,
            'channelPartnerType' => $channelPartnerType
        ];
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id, Request $request)
    {
        if (!$request->id) {
            throw new ApiGenericException('Please Provide Valid Channel Partner Id');
        }

        $channelPartnerType = ChannelPartnerType::where('id', $request->id)->first();

        if (!$channelPartnerType) {
            throw new ApiGenericException('Couldn\'t Find Any Channel Partner With Given Id');
        }

        if ($request->forcedelete) {
            $result = $channelPartnerType->forceDelete();
        } else {
            $result = $channelPartnerType->delete();
        }
        
        if (!$result) {
            throw new ApiGenericException('Error Occured, Channel Partner Type Could Not Be Deleted');
        }

        return [
            'success' => true,
        ];
    }
}
