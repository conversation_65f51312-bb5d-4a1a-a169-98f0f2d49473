<?php

namespace App\Http\Controllers;

use Hash;
use Auth;
use DB;


use App\Http\Helpers\QueryBuilder;


use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

use Illuminate\Http\Request;
use App\Http\Requests;

class wordpressPostController extends Controller
{
    

    
   
    public function getwordpressPost(Request $request)
    {  
        if (!$request->wpslug) {
            throw new ApiGenericException('Validate Error,Slug is erquired.');
        }
        $post_html = \DB::connection('iconnews')->table('wp_posts')
             ->selectRaw('post_content, post_title')->where('post_name', $request->wpslug)->first();
             
        if (!$post_html) {
            throw new ApiGenericException('Error Occured, No record found');
        }
            
            return [
            'is_wp_html' => 1,
            'post_html' => $post_html->post_content,
            'post_title' => $post_html->post_title
            ];
    }
    
}
