<?php

namespace App\Http\Controllers;

use App\Models\MonthlyParkingPromo;
use Illuminate\Http\Request;

use Illuminate\Support\Facades\Input;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use Webpatser\Uuid\Uuid;

class MonthlyParkingPromoController extends Controller
{
    public function index()
    {
        return MonthlyParkingPromo::with('photo')->get();
    }

    public function show(MonthlyParkingPromo $promo)
    {
        return $promo->load('photo');
    }

    public function store(Request $request)
    {
        $this->validate($request, MonthlyParkingPromo::$validParams);

        return MonthlyParkingPromo::create($request->all())->load('photo');
    }

    public function destroy(MonthlyParkingPromo $promo)
    {
        $promo->delete();

        return $promo;
    }

    public function addPhoto(MonthlyParkingPromo $promo, Request $request)
    {
        return $promo->uploadPhoto($request);
    }

    public function update(MonthlyParkingPromo $promo, Request $request)
    {
        $this->validate($request, MonthlyParkingPromo::$validParams);

        $promo->update($request->all());

        return $promo->load('photo');
    }
}
