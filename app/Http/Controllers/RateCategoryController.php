<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Facility;
use App\Models\RateCategory;
use App\Models\Rate;
use App\Http\Requests;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use Auth;

class RateCategoryController extends Controller
{
    public function index()
    {
        $rateCategories = RateCategory::all();

        return $rateCategories;
    }

    public function partnerRateCategory($facility_id = '')
    {
        if ($facility_id != '') {
            $facility = Facility::where('id', $facility_id)->first();
            $rateCategories = RateCategory::where('partner_id', $facility->owner_id)->orWhereNull('partner_id')->orderBy('id', 'DESC')->get();
        } else {
            $rateCategories = RateCategory::select('id', 'category', 'created_at', 'updated_at', 'partner_id', 'no_of_days', 'total_usage', 'is_event_enabled')->orderby('id', 'desc')->get();
        }
        return $rateCategories;
    }

    public function show(RateCategory $rateCategory)
    {

        return $rateCategory;
    }

    public function edit($id)
    {


        $rateCategory = RateCategory::find($id);
        if (!$rateCategory) {
            throw new NotFoundException('Record Not Found');
        }
        return $rateCategory;
    }

    public function store(Request $request)
    {  
     	 $this->validate($request, [
           'category' => 'required|max:255',
           'total_usage' => 'required_if:is_usage_finite,0|required_without:is_usage_finite',
        ]);
        if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12') {
            $partner_id = Auth::user()->created_by;
        }  
        $rateCategory= new  RateCategory; 
	    $rateCategory->partner_id = isset($partner_id)?$partner_id : Auth::user()->id;
        $rateCategory->category = $request->category;
        if(!empty($request->is_usage_finite))
        {
            $rateCategory->is_usage_finite = 1;    
        }
        $rateCategory->total_usage = $request->total_usage;
        $rateCategory->is_event_enabled = '1';
        $rateCategory->save();
        return $rateCategory;
    }


    public function destroy($id)
    {
        //return $rateCategory->id;
        $rateCategory = RateCategory::find($id);
        if (!$rateCategory) {
            throw new NotFoundException('No Found Pass id.');
        }

        $ratestatus = Rate::where('category_id', $rateCategory->id)->first();
        if ($ratestatus == '') {

            $status = $rateCategory->delete() ? $rateCategory : null;
            if ($status) {
                return "Deleted Successfully";
            }
        } else {
            throw new ApiGenericException('Sorry usage category linked to pass can not be deleted');
        }
    }

    public function update(Request $request)
    {

        $rateCategory = RateCategory::find($request->id);
        if (!$rateCategory) {
            throw new ApiGenericException("Rate Category not found.");
        }
        if(!empty($request->is_usage_finite))
        {
            $rateCategory->is_usage_finite = 1;    
        }
        else
        {
            $rateCategory->is_usage_finite = 0;
        }
        $rateCategory->category = $request->category;
        $rateCategory->total_usage = $request->total_usage;
        $rateCategory->save();
        return $rateCategory;
    }

    public function getRateCategoryList()
    {
        $owner_id = Auth::user()->id;
        $category = \DB::table('rate_categories')
            ->select('id as id', 'category')
            ->where('partner_id', $owner_id)
            ->get();
        return $category;
    }

    public function getPassUsaseList(Request $request)
    {

        if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12') {
            $owner_id = Auth::user()->created_by;
        } else {
            $owner_id = Auth::user()->id;
        }

        $category = RateCategory::where('partner_id', $owner_id);
        if ($request->search) {
            $category = $category->Where('category', 'like', '%' . trim($request->search) . '%')->orWhere('total_usage', 'like', '%' . trim($request->search) . '%');
        }
        if ($request->sort != '') {
            $category = $category->orderBy($request->sort, $request->sortBy);
        } else {
            $category = $category->orderBy('id', 'DESC');
        }
        $category = $category->paginate(10);
        return $category;
    }



    public function rateCategoryDetails($id)
    {

        return RateCategory::find($id);
    }


    // update image 
    public function imageUpdate(Request $request)
    {
        $rate = Rate::where('id', $request->id)->first();
        if (!$rate) {
            throw new ApiGenericException('Rate Category  Record Not Found');
        }

        /*
                if($rate->image!=''){
                    $file_path = 'assets/media/images/mapco/'.$request->images;
                	unlink($file_path);	
                }

                */
        Rate::where('id', $request->id)->update(['image' => $request->images]);
        $imagepath = env('APP_URL') . '/assets/media/images/mapco/' . $request->images;
        $data = [
            'id' => $request->id,
            'image_name' => $request->images,
            'image_path' => $imagepath
        ];

        return $data;
    }
}
