<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\MonthlyParkingUserPayment;
use App\Models\MonthlyParkingUser;
use App\Models\PaymentProfile;
use App\Models\AuthorizeNetTransaction;

use App\Exceptions\UserNotAuthorized;
use App\Exceptions\ApiGenericException;

use App\Classes\AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;

use App\Classes\MagicCrypt;

class MonthlyParkingUserPaymentController extends Controller
{

    protected $request;

    // Instance of AuthorizeNet\Cim
    protected $charge;

    // Result of the transaction request to Authorize.Net
    protected $transaction;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function getSinglePayment(MonthlyParkingUser $account, MonthlyParkingUserPayment $payment)
    {
        // Verify account owns this paymenta
        if (!$account->payments()->where('id', $payment->id)->first()) {
            throw new UserNotAuthorized('Monthly parking account did not make this payment.');
        }

        return $payment->load('transaction', 'paymentProfile');
    }

    public function getPayments(MonthlyParkingUser $account)
    {
        return $account->payments()
            ->with(
                ['transaction' => function ($query) {
                        $query->select('id', 'anet_status_id', 'total', 'method', 'payment_last_four', 'expiration', 'response_code', 'created_at');
                }]
            )
                ->with('paymentProfile')
                ->orderBy('created_at', 'desc')
                ->get();
    }

    public function addPaymentMethod(Request $request, MonthlyParkingUser $account)
    {
        $this->setDecryptedCard($request);

        $paymentType = $request->input('payment_type');

        if (!$paymentType || ($paymentType !== 'credit_card' && $paymentType !== 'bank_account')) {
            throw new ApiGenericException('Must include payment_type with this request, and must be either credit_card or bank_account.');
        }

        if ($paymentType === 'credit_card') {
            $validationRules = [
                'name_on_card' => 'string|required',
                'card_number' => 'required|digits_between:13,16',
                'expiration_date' => 'required|digits:4',
                'security_code' => 'required|digits_between:3,4'
            ];
        } else { // bank account
            $validationRules = [
                'name' => 'string|required',
                'account_type' => 'string|required|in:checking,savings,businessChecking',
                'routing' => 'required|digits:9',
                'account_number' => 'required|digits_between:5,17'
            ];
        }

        $this->validate($request, $validationRules);

        $input = $request->all();
        return $paymentType === 'credit_card'
            ? $account->addCreditCard($input['card_number'], $input['expiration_date'], $input['security_code'], $input['name_on_card'])
            : $account->addBankAccount($input['account_type'], $input['routing'], $input['account_number'], $input['name']);
    }

    public function deletePaymentMethod(MonthlyParkingUser $account, $paymentProfileId)
    {
        // Make sure monthly parking account owns the payment profile
        $owns = $account->ownsPaymentProfile($paymentProfileId);
        if (!$owns) {
            throw new UserNotAuthorized('Monthly parking account does not own this payment method.');
        }

        // Delete the profile - handles deleting in auth net and locally
        $cim = new Cim();
        $cim->setMonthlyParkingUser($account)->deleteCustomerPaymentProfile($paymentProfileId);

        return $paymentProfileId;
    }


    public function decryptNonce($nonce)
    {
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        return $mc->decrypt($nonce);
    }

    public function setDecryptedCard(Request $request)
    {
        if ($request->card) {
            $nonce = $request->card['nonce'];
            $decryptedNonce = $this->decryptNonce($nonce);

            $cardData = explode(':', $decryptedNonce);
            $card = array(
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3]
            );
            $request->merge(array('card' => $card));
            $this->request = $request;
        }

        if (isset($request->payment_type) && $request->payment_type == "credit_card") {
            $nonce = $request->nonce;
            $decryptedNonce = $this->decryptNonce($nonce);
            $cardData = explode(':', $decryptedNonce);
            $request->request->add(
                [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3]
                ]
            );
            $this->request = $request;
        }
    }

    /**
     * Make a one time payemnt against a monthly parking account
     * Can use a saved payment profile or one time payment method
     * If a one time method is entered and the user wants to save it, you should first use the
     * save payment method endpoint, then send through a request to make a payment using the payment
     * method that was just saved
     */

    public function makePayment(Request $request, MonthlyParkingUser $account)
    {
        $this->setDecryptedCard($request);

        $this->validate(
            $request, [
            'amount' => 'required|numeric|min:0'
            ]
        );

        if (!$request->card && !$request->bank && !$request->payment_profile_id) {
            throw new ApiGenericException("You must provide a new payment method or choose an existing one.");
        }

        if ($request->card || $request->bank) {
            $validationRules = $request->card
                                ?   [
                                        'card.name_on_card' => 'string|required',
                                        'card.card_number' => 'required|digits_between:13,16',
                                        'card.expiration_date' => 'required|digits:4',
                                        'card.security_code' => 'required|digits_between:3,4'
                                    ]
                                :   [
                                        'bank.name' => 'string|required',
                                        'bank.account_type' => 'string|required|in:checking,savings,businessChecking',
                                        'bank.routing' => 'required|digits:9',
                                        'bank.account_number' => 'required|digits_between:5,17'
                                    ];

            $this->validate($request, $validationRules);
        }

        $this->charge = (new Cim())->setMonthlyParkingUser($account);

        if ($request->payment_profile_id) {
            $this->setupChargeViaPaymentProfile($account);

            $payment = (new Cim())->setMonthlyParkingUser($account)->getPaymentProfile($request->payment_profile_id);
            $paymentLastFour = isset($payment['card']) ? $payment['card']['card_number'] : $payment['bank']['bank_account'];
        } else {
            $request->card ? $this->setupChargeViaCard() : $this->setupChargeViaBank();

            $paymentLastFour = $request->card ? substr($request->card['card_number'], -4) : substr($request->bank['account_number'], 4);
        }

        // Description and invoice format are specific and based on legacy code.
        // They are used for internal processes at Icon
        $description = $account->account_number . ' ' . Cim::PAYMENT_ONE_TIME . " ICON Web";
        $invoice = $account->account_number . ',' . $paymentLastFour;

        // Create and send transaction
        $this->transaction = $this->charge->createTransaction($request->amount, $description, $invoice)->executeTransaction();

        $payment = $this->savePayment($account);
        $payment->sendConfirmationEmail();

        return $payment->load('transaction');
    }

    // Saved payment profile flow
    protected function setupChargeViaPaymentProfile(MonthlyParkingUser $account)
    {
        // Get payment profile, making sure it is owned by this user
        $paymentProfileId = $this->request->payment_profile_id;
        $paymentProfile = $account->paymentProfiles()->where('payment_profile', $paymentProfileId)->first();

        if (!$paymentProfile) {
            throw new UserNotAuthorized('User does not own this payment profile.');
        }

        // Set payment profile on transaction
        $this->charge->setPaymentProfile($paymentProfileId);
    }

    // One time charge via credit card, not saved
    protected function setupChargeViaCard()
    {
        $card = $this->request->card;
        $this->charge
            ->setBillingAddress(AuthorizeNet::splitName($card['name_on_card']))
            ->setCreditCard($card['card_number'], $card['expiration_date'], $card['security_code']);
    }

    // One time charge via echeck, not saved
    protected function setupChargeViaBank()
    {
        $bank = $this->request->bank;
        $this->charge
            ->setBillingAddress(AuthorizeNet::splitName($bank['name']))
            ->setBankAccount(
                $bank['account_type'],
                $bank['routing'],
                $bank['account_number'],
                $bank['name']
            );
    }

    protected function savePayment(MonthlyParkingUser $account)
    {
        $transaction = AuthorizeNetTransaction::where('anet_trans_id', $this->transaction['anet_trans_id'])->first();
        $paymentProfile = $this->request->payment_profile_id ? PaymentProfile::where('payment_profile', $this->request->payment_profile_id)->first() : false;

        return MonthlyParkingUserPayment::create(
            [
            'mp_user_id' => $account->id,
            'payment_profile_id' => $paymentProfile ? $paymentProfile->id : null,
            'authnet_transaction_id' => $transaction->id
            ]
        );
    }
}
