<?php
namespace App\Http\Controllers;

use Auth;
use Exception;
use Artisan;

use Illuminate\Http\Request;
use Carbon\Carbon;

use App\Classes\ArApi;

use App\Services\Mailers\UserMailer;

use App\Models\MonthlyParkingUser;
use App\Models\AutopayMethod;
use App\Models\CancellationReasons;
use App\Models\CancellationRequest;
use App\Models\CancellationRequestReasons;
use App\Models\CancellationRequestTenants;

use App\Exceptions\InvalidResponseException;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Exceptions\DuplicateEntryException;
use App\Exceptions\UserNotAuthorized;


class CancellationRequestController extends Controller
{

	const NOTES_ID = 23;
	const DATE_FIRST = 1;
        
	/**
	* get All Tenants slots for specific account
	*/	
	public function getAccountTenantsSlots($account_code = '')
	{

		$response = ArApi::accountTenantsSlots($account_code);
		
		if ($response['status'] === 404 ||  $response['status'] === 400) {
			throw new NotFoundException('No tenant numbers associated with this monthly account.');
		} 

		if ($response['status'] !== 200) {
			throw new InvalidResponseException('Invalid response returned from tenants accounts API', ['api_response' => $response]);
		}

		$data = $response['data'];

		if ($data) {
			foreach ($data as $key => $value) {

				$requestStatus = "";				
				$requestedSlots = CancellationRequestTenants::select('cancellation_request_tenants.tenant_id', 'cancellation_requests.status')
								->join('cancellation_requests','cancellation_requests.id','=','cancellation_request_tenants.cancellation_request_id')
								->where('cancellation_requests.account_no', '=', $value['Account_Code'])
								->where('cancellation_request_tenants.tenant_id', '=', $value['Tenat_ID'])
								->where(function ($query) {
									$query->where('cancellation_requests.status', CancellationRequest::STATUS_COMPLETE)
										->orWhere('cancellation_requests.status', CancellationRequest::STATUS_PENDING);
								})->first();

				if ($requestedSlots) {
					$requestStatus = $requestedSlots->status;
				}

				$data[$key]['Status'] = $requestStatus;
			}
		}

		return $data;        
	}

	/**
	 * cancel a parking account tenant
	 * This method add all cancel request to database
	 *
	 * @param $request Request
	 */
	public function postCancelRequest(Request $request)
	{
		$this->validate(
			$request, [
				'account_no' => 'required|integer',
				'termination_date' => 'required|date|date_format:"Y-m-d"|after:'.date('Y-m-d', strtotime("yesterday")),
				'reasons' => 'required',
				'tenants' => 'required',
				'comments' => 'string'            
			]
		);

		$user = Auth::user();
		$accountNo = $request->input('account_no');
		$termination_date = $request->input('termination_date');
                $termination_date = Carbon::parse($termination_date)->format('Y-m-d');
		$reasons = $request->input('reasons');
		$tenants = $request->input('tenants');
		$comment = $request->input('comments');

		// validating account number is associated with user			
		$mpUser = MonthlyParkingUser::where('account_number', $accountNo)
				->where('user_id', $user->id)
				->where('active', 1)
				->first();
                
		if (!$mpUser) {
			throw new NotFoundException('No monthly parking account with that account number.');
		}
		foreach ($tenants as $tenant) {
                    
			// validation for duplicate request
			$pendingRequest = CancellationRequestTenants::join('cancellation_requests', 'cancellation_requests.id', '=', 'cancellation_request_tenants.cancellation_request_id')
						->where('cancellation_requests.account_no', '=', $accountNo)
						->whereIn('cancellation_requests.status',[CancellationRequest::STATUS_PENDING, CancellationRequest::STATUS_COMPLETE])
						->where('cancellation_request_tenants.tenant_id', '=', $tenant['Tenant_ID'])
						->where('cancellation_requests.user_id', '=', $user->id)
						->first();
			if ($pendingRequest) {
				throw new DuplicateEntryException("A cancellation request has been already registered for monthly parking account $accountNo with parking slot ". $tenant['Tenant_Spot']. ".");
			}

		}

		try {

			// creating reason text and reason emails array for T-Out Notes and emails
			$reasonArray = array();
			$reasonMailsArray = array();
			foreach ($reasons as $reasonId) {
				// fetching cancellation reason and email ID
				$reason = CancellationReasons::find($reasonId);

				if (!$reason) {
					continue;
				}

				if ($reason->reason == CancellationReasons::REASON_SERVICE) {
					$garageEmail = $this->getGarageEmails($mpUser->garage_code);

					if ($garageEmail) {
						$reasonMailsArray[$reason->id] = $garageEmail;
					}
				}
				else {
					$reasonMailsArray[$reason->id] = $reason->email;
				}

				$reasonArray[$reason->id] = $reason->reason;

			}

			// creating T-Out notes
			$tOutNotes = array();
			foreach ($tenants as $tenant) {
				$tOutNotes[] = array(
					'account_note_flag' => null,
					'account_id' => $tenant['Account_ID'],
					'tenant_id' => $tenant['Tenant_ID'],
					'notes_id' => CancellationRequestController::NOTES_ID,
					'subject_details' => Carbon::createFromFormat('Y-m-d', $request->input('termination_date'))->format('m-d-Y'),
					'subject_text' => implode(',', $reasonArray) . ", $comment",
					'created_by' => $user->id,
					'created_on' => date('Y-m-d H:i:s'),
					'last_modified_by' => null,
					'last_modified_on' => null,
					'note_tenant_no' => $tenant['Tenant_Spot'],
				);

			}
			// send T-Out notes
			$this->createToutNotes($tOutNotes);
						
			// new cancellation request
			$cancelRequest = new CancellationRequest;
			$cancelRequest->user_id = $user->id;
			$cancelRequest->account_no = $accountNo;
			$cancelRequest->termination_date = $termination_date;
			$cancelRequest->comment = $comment;
			$cancelRequest->status = CancellationRequest::STATUS_PENDING;   
			
			$cancelRequest->save();

			// associated tenants for cancel request
			$tenantSlotArray = array();
			foreach ($tenants as $tenant) {
				$cancelTenanet = new CancellationRequestTenants;
				$cancelTenanet->cancellation_request_id = $cancelRequest->id;
				$cancelTenanet->tenant_id = $tenant['Tenant_ID'];
				$cancelTenanet->slot_no = $tenant['Tenant_Spot'];
				$cancelTenanet->save();
				$tenantSlotArray[] = $tenant['Tenant_Spot'];
			}

			// all associated reasons for cancel request
			foreach ($reasonArray as $reasonId => $reason) {
				$cancellationReason = new CancellationRequestReasons;
				$cancellationReason->cancellation_request_id = $cancelRequest->id;
				$cancellationReason->reason_id = $reasonId;
				$cancellationReason->save();   
			}

			// setting up email parameters for user
			$subject_user = "Cancellation Request: $accountNo";

			$data = [
				'mp_id' => $accountNo,
				'accountId' => $accountNo,
				'termination_date' => Carbon::createFromFormat('Y-m-d', $request->input('termination_date'))->toFormattedDateString(),
				'tenant_number' => implode(',', $tenantSlotArray),
				'comments' => $request->input('comments')
			];

			$mailer = new UserMailer();
		
			$mailer->queueMailTo($user->email, $subject_user, $data, 'email.monthly-parking-cancellation-user');

			// send emails according to cancellation reasons
                        
			$subject_admin = "Cancellation request for monthly parking account $accountNo";
                        $facility_name= isset($mpUser->facility->full_name)?$mpUser->facility->full_name:'';
			foreach ($reasonMailsArray as $reasonId => $email) {
				$data['reason'] = $reasonArray[$reasonId];
				$data['facility_name'] = $facility_name;
				$mailer->queueMailTo($email, $subject_admin, $data, 'email.monthly-parking-cancellation-admin');
			}

			// flag validation for account payment notifications
			$flagDeactivation = false;

			$firstDay = new Carbon('first day of next month'); 
			if ($termination_date <=  $firstDay->format('Y-m-d')) {
				$flagDeactivation = true;
			}

			$countActiveSlots = $this->getActiveTenantsSlots($accountNo, $cancelRequest->id);

			// Account flag updates
			if ((date('d') != CancellationRequestController::DATE_FIRST) && ($flagDeactivation) 
				&& ($countActiveSlots == 0) 
				&& ($this->updateAccountFlags($accountNo))) {

                                /**getting method id value from AR database to saved in our db**/
				$billing_method_id= $this->getAndUpdateBillingMethodId($accountNo);
                                $mpUser->billing_method_id = $billing_method_id;
                                $mpUser->save();
				/** end **/
                                
                                /** update supress flag status for requested tenants **/
                                $requestedSlots = CancellationRequestTenants::select('cancellation_request_tenants.tenant_id')
                                            ->join('cancellation_requests','cancellation_requests.id','=','cancellation_request_tenants.cancellation_request_id')
                                            ->where('cancellation_requests.account_no', '=', $accountNo)
                                            ->where('cancellation_requests.status', '=', '1')
                                            ->orWhere('cancellation_requests.id', '=',$cancelRequest->id)
                                            ->get();
                                
                               $tOutNotesSupress=array(); 
                               if(count($requestedSlots)>0)
                               {
                                    foreach($requestedSlots as $key => $value) {
                                        
                                        $tOutNotesSupress[] = array(
                                                                'tenant_id' => isset($value['tenant_id'])?$value['tenant_id']:'0'
                                        );
                                    }
                                    
                                    if(count($tOutNotesSupress)>0)
                                    {
				         $this->enableSupressFlag($tOutNotesSupress);
                                    }
                               }
                               /** end supress flag ***/
                                
                               /** delete Autopay Method **/
				$mpAutoPayMethods = $mpUser->autopayMethods; 
				if($mpAutoPayMethods && $mpAutoPayMethods->count()) {
					foreach ($mpAutoPayMethods as $payment) {
						$payment->delete();
					}
                                        $this->disableAccountAchFlags($accountNo);
					// cancel request status marked as complete if account flags updated
					$mpUser->is_cancel_autopay_updated = CancellationRequest::UPDATE_FLAG;
					$mpUser->save();
                                        
                                        // send autopayment disabled email
					$this->sendAutopaymentStatusEmail($mpUser->id);
				}
			}

			// update request status for current month
			if ((date('d') != CancellationRequestController::DATE_FIRST) && ($flagDeactivation) ) {
				
				$cancelRequest->status = CancellationRequest::STATUS_COMPLETE;
				$cancelRequest->save();
				
			}

		} catch (Exception $e) {
			throw new ApiGenericException("Could not add request, " . $e->getMessage());
		}

		return $cancelRequest;
	}

	/**
	* update cancel account request status
	* #param $request id and $status
	*/
	public function updateCancellationRequestStatus($id = 0) {

		// fetching cancellation request
		$cancellationRequest = CancellationRequest::find($id);

		if (!$cancellationRequest) {
			throw new NotFoundException('No Cancellation request found for selected record.');
		}

		$mpUser = MonthlyParkingUser::where('account_number', $cancellationRequest->account_no)->first();

		if (!$mpUser) {
			throw new NotFoundException('No monthly parking account with that account number.');
		}

		$user = Auth::user();

		try {
                    
                        //reset flags
			$this->enableAccountFlags($cancellationRequest->account_no, $mpUser->billing_method_id);
                        $mpUser->billing_method_id = CancellationRequest::RESET_FLAG;
                        $mpUser->save();
                        
                        /** update supress flag status for requested tenants **/
                        $requestedSlots = CancellationRequestTenants::select('cancellation_request_tenants.tenant_id')
                           ->join('cancellation_requests','cancellation_requests.id','=','cancellation_request_tenants.cancellation_request_id')
                           ->where('cancellation_requests.account_no', '=', $cancellationRequest->account_no)
                           ->where('cancellation_requests.status', '=', '1')     
                           ->get();

                        $tOutNotes=array();
                        if(count($requestedSlots)>0)
                        {
                            foreach($requestedSlots as $key => $value) {

                                $tOutNotes[] = array(
                                        'tenant_id' => isset($value['tenant_id'])?$value['tenant_id']:'0'
                                );
                             }
                            if(count($tOutNotes)>0)
                            {
                                $this->disableSupressFlag($tOutNotes);  
                            }
                        }
                        
			if ($mpUser->is_cancel_autopay_updated) {
				
				$activeAccounts = $this->getActiveTenantsSlots($cancellationRequest->account_no);
                                
				if (!$activeAccounts) {
					throw new ApiGenericException('This request has been already completed');
				}
				else {

					$this->enableAccountAchFlags($cancellationRequest->account_no);
                                        
					$autoPayMethods = AutopayMethod::onlyTrashed()->where('mp_user_id', $mpUser->id)->get();
					
					//restore Autopay Method
					//getting mp_user all payment methods	
					if(count($autoPayMethods)>0) {
						foreach ($autoPayMethods as $payment) {
							$payment->restore();
						}
                                             // send autopayment disabled email
                                            $this->sendAutopaymentStatusEmail($mpUser->id);        
					}
                                        
					$mpUser->is_cancel_autopay_updated = CancellationRequest::RESET_FLAG;
					$mpUser->save();
                                        
				}

			}
                        
			
			$cancellationRequest->status = CancellationRequest::STATUS_CANCEL;
			$cancellationRequest->updated_by = $user->id;

			$cancellationRequest->save();

			// get account ID from account numner
			$accountModel = $this->getAccountPaymentFlag($cancellationRequest->account_no);
			$accountID = null;
			if ($accountModel) {
				$accountID = $accountModel['account_id'];
			}
			
			// creating T-Out notes for request delete
			$tOutNotes = array();
			foreach ($cancellationRequest->tenants as $tenant) {
				$tOutNotes[] = array(
					'account_note_flag' => null,
					'account_id' => $accountID,
					'tenant_id' => $tenant->tenant_id,
					'notes_id' => CancellationRequestController::NOTES_ID,
					'subject_details' => "Cancellation request delete",
					'subject_text' => "The cancellation request effective $cancellationRequest->termination_date was deleted.",
					'created_by' => $user->id,
					'created_on' => date('Y-m-d H:i:s'),
					'last_modified_by' => null,
					'last_modified_on' => null,
					'note_tenant_no' => $tenant->slot_no,
				);

			}
                        
			// send T-Out notes
			$this->createDeleteToutNotes($tOutNotes);

		} catch (Exception $e) {
			throw new ApiGenericException("Could not update request, " . $e->getMessage());
		}

		return $cancellationRequest;
	}
 
 	// This functionality has been removed :: client request
 	// Updated on 29-Jun-18 -Ashish Kumar
	/**
	* update cancellation request
	* This method update termination date for  cancellation
	*
	* #param $request Request, $requestId Cancellation request ID
	*/
	// public function updateCancellationRequestDate(Request $request, $id = 0) {

	// 	$this->validate(
	// 		$request, [
	// 		'termination_date' => 'required|date|date_format:"Y-m-d"|after:' . date('Y-m-d', strtotime("yesterday"))
	// 		]
	// 	);

	// 	try {
	// 		// fetching cancellation request
	// 		$cancellationRequest = CancellationRequest::find($id);

	// 		if (!$cancellationRequest) {
	// 			throw new NotFoundException('No cancellation request found.');
	// 		}

	// 		$user = Auth::user();
	// 		$cancellationRequest->termination_date = $request->termination_date;
	// 		$cancellationRequest->updated_by = $user->id;
	// 		$cancellationRequest->save();
	// 		return $cancellationRequest;
			
	// 	} catch (Exception $e) {
	// 		throw new ApiGenericException("Could not update request, " . $e->getMessage());
	// 	}
	// }

	/**
	* This method returns the list of cancellation requests
	*
	* #param $request Request
	*/
	public function getCancellationRequestList(Request $request) {

		$keyword = $request->keyword;

		$cancellationRequests = CancellationRequest::select('cancellation_requests.*','users.name','users.email')
						->join('users','users.id','=','cancellation_requests.user_id')
						->with('tenants')
						->with('reasons')
						->with('reasons.cancellationReason')
						->orderBy('cancellation_requests.id', 'DESC');

		if ($keyword) {
			$cancellationRequests = $cancellationRequests->where(function ($query) use ($keyword) {
				$query->where('users.email', 'LIKE', "%$keyword%")
					->orWhere('users.name', 'LIKE', "%$keyword%")
					->orWhere('cancellation_requests.account_no', 'LIKE', $keyword);
				}
			);
		}

		return $cancellationRequests->paginate(20); ;
	}

	/**
	* This method returns the details for selected cancellation request
	*
	* #param $id cancellation request ID
	*/
	public function getCancellationRequest($id = 0) {

		$cancellationRequest = CancellationRequest::select('cancellation_requests.*','users.name','users.email','users.phone')
								->with('reasons')
								->with('reasons.cancellationReason')
								->with('tenants')
								->join('users','users.id','=','cancellation_requests.user_id')
								->find($id);

		if (!$cancellationRequest) {
			throw new NotFoundException('No cancellation request found.');
		}
		return $cancellationRequest;
	}

	/**
	* This method call AR API to create TOUT Notes
	*
	* #param $notes multi-records
	*/
	public function createToutNotes($notes = array()) {

		$response = ArApi::addTenantNotes($notes);
		
		if ($response['status'] !== 200) {
			throw new InvalidResponseException('Invalid response returned from API', ['api_response' => $response]);
		}
		return true;
	}
        
        /**
	* This method call AR API to update supress rent flag to 1
	*
	* #param $notes multi-records
	*/
	public function enableSupressFlag($notes = array()) {

		$response = ArApi::enableSupressFlag($notes);
		
		if ($response['status'] !== 200) {
			throw new InvalidResponseException('Invalid response returned from API', ['api_response' => $response]);
		}
		return true;
	}
        
        
        /**
	* This method call AR API to update supress rent flag to 0
	*
	* #param $notes multi-records
	*/
	public function disableSupressFlag($notes = array()) {

		$response = ArApi::disableSupressFlag($notes);
		
		if ($response['status'] !== 200) {
			throw new InvalidResponseException('Invalid response returned from API', ['api_response' => $response]);
		}
		return true;
	}
        
	/**
	* This method call AR API to create TOUT Notes
	*
	* #param $notes multi-records
	*/
	public function createDeleteToutNotes($notes = array()) {

		$response = ArApi::addDeleteTenantNotes($notes);		
		if ($response['status'] !== 200) {
			throw new InvalidResponseException('Invalid response returned from API', ['api_response' => $response]);
		}
		return true;
	}

	/**
	* This method call AR API to update account flags
	*
	* #param $account_no
	*/
	public function updateAccountFlags($account_no = 0) {

		$response = ArApi::updateNotificationPaymentAutopayFlags($account_no);
		if ($response['status'] !== 200) {
			return false;
		}
		return true;
	}
        
        public function getAndUpdateBillingMethodId($account_no = 0) {

		$response = ArApi::getAndUpdateBillingMethodId($account_no);

		if ($response['status'] !== 200) {
			return 0;
		}
              if(isset($response['data']['bliing_method_id']))
              {
		return $response['data']['bliing_method_id'];
              }
              return 0;
	}

        
	/**
	* This method call AR API to revoke account flags
	*
	* #param $account_no
	*/
	public function enableAccountFlags($account_no = 0, $billing_method_id = 0) {

		$response = ArApi::enableNotificationPaymentAutopayFlags($account_no, $billing_method_id);

		if ($response['status'] !== 200) {
			return false;
		}
		return true;
	}
        
	/**
	* This method call AR API to revoke account flags
	*
	* #param $account_no
	*/
	public function enableAccountAchFlags($account_no = 0) {

		$response = ArApi::enableAccountAchFlags($account_no);

		if ($response['status'] !== 200) {
			return false;
		}
		return true;
	}
        
	/**
	* This method call AR API update ACH  account flags
	*
	* #param $account_no
	*/
	public function disableAccountAchFlags($account_no = 0) {

		$response = ArApi::disableAccountAchFlags($account_no);

		if ($response['status'] !== 200) {
			return false;
		}
		return true;
	}

	/**
	* This method call AR API to get garage emails
	*
	* #param $garage_code
	*/
	public function getGarageEmails($garage_code = '') {
		$response = ArApi::getGarageEmails($garage_code);
		if ($response['status'] !== 200) {
			return false;
		}

		if (isset($response['data']['Garage_Supervisor_Email']) && ($response['data']['Garage_Supervisor_Email'] !== "")) {
			return $response['data']['Garage_Supervisor_Email'];
		}
		else if (isset($response['data']['Garage_Email']) && ($response['data']['Garage_Email'] !== "")) {
			return $response['data']['Garage_Email'];
			
		} 
		else {
			return false;
		}
	}

	/**
	* This method call AR API to get all tenants slots
	* Returns counts for active slots
	* #param $account_no, $request_id
	*/
	public function getActiveTenantsSlots($account_no = '', $requestId = 0) {
		$response = ArApi::accountTenantsSlots($account_no);
		if ($response['status'] !== 200) {
			return false;
		}
		$data = $response['data'];
		$tenantsData = array();
		foreach ($data as $key => $value) {
			array_push($tenantsData, $value['Tenat_ID']);
		}

		if ($requestId) {
			$requestedSlots = CancellationRequestTenants::select('cancellation_request_tenants.tenant_id')
								->join('cancellation_requests','cancellation_requests.id','=','cancellation_request_tenants.cancellation_request_id')
								->where('cancellation_requests.account_no', '=', $account_no)
								->where(function ($query) use ($requestId) {
									$query->where('cancellation_requests.status', CancellationRequest::STATUS_COMPLETE)
										->orWhere('cancellation_requests.id', $requestId);
								})->get();

			foreach ($requestedSlots as $key => $value) {

				$tenantKey = array_search($value['tenant_id'],$tenantsData);
				if ($tenantKey > -1) {
					unset($tenantsData[$tenantKey]);
				}
			}
		}
		return count($tenantsData);
	}

	/**
	* This method call AR API to get account ID an payment reminder flag
	*
	* #param $account_no
	*/ 
	public function getAccountPaymentFlag($account_no = '')
	{
		$response = ArApi::checkPaymentReminderFlag($account_no);
		if ($response['status'] !== 200) {
			return false;
		}
		return $response['data'];
	}

	/**
	* This method call Artisan command to send autopayment status emails
	*
	* #param $accountId
	*/ 

	public function sendAutopaymentStatusEmail($accountId = 0) {
		Artisan::queue('icon:autopay-email', ['monthlyParkingUserId' => $accountId]);
	}
	
}