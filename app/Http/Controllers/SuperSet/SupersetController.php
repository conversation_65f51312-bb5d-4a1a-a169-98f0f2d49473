<?php

/* 
  *  Copyright (c) 2024 ParkEngage Inc. All rights reserved.
  *  
  *  This software is the confidential and proprietary information of
  *  ParkEngage Inc. ("Confidential Information"). You shall not
  *  disclose such Confidential Information and shall use it only in
  *  accordance with the terms of the license agreement you entered into
  *  with ParkEngage Inc.
  *  
  *  This source code is a valuable trade secret of ParkEngage Inc.
  *  
  *  THIS SOFTWARE IS PROVIDED BY ParkEngage Inc. ``AS IS'' AND ANY
  *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  *  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  *  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL [YOUR COMPANY NAME] OR
  *  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  *  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  *  PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  *  OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *  
  *  The views and conclusions contained in the software and documentation
  *  are those of the authors and should not be interpreted as representing
  *  *official policies, either expressed or implied, of ParkEngage Inc.
*/

namespace App\Http\Controllers\SuperSet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\SuperSet\DADashboard;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\Facility;
use App\Models\ParkEngage\UserFacility;
use App\Services\LoggerFactory;
use App\Models\SuperSet\PartnerMappedDashboardList;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

/**
 * Controller for integrating with Apache Superset to generate guest tokens for embedding dashboards.
 */
class SupersetController extends Controller
{
    /**
     * @var string Superset server URL from environment configuration.
     */
    protected $supersetUrl;

    /**
     * @var array Admin credentials for Superset authentication.
     */
    protected $adminCredentials;

    /**
     * @var \Monolog\Logger Logger instance for logging token generation activities.
     */
    protected $log;

    /**
     * Constructor to initialize Superset configuration and logger.
     *
     * @param LoggerFactory $logFactory Factory for creating logger instances.
     */
    public function __construct(LoggerFactory $logFactory)
    {
        // Initialize logger for token generation
        $this->log = $logFactory->setPath('logs/superset')->createLogger('token_genration_log');

        // Load Superset URL and credentials from parkengage config file
        $this->supersetUrl = config('parkengage.SUPER_SET.SUPERSET_URL');
        $this->adminCredentials = [
            'username' => config('parkengage.SUPER_SET.SUPERSET_ADMIN_USERNAME'),
            'password' => config('parkengage.SUPER_SET.SUPERSET_ADMIN_PASSWORD'),
            'provider' => config('parkengage.SUPER_SET.SUPERSET_PROVIDER'),
        ];

        // Log configuration details for debugging
        $this->log->info('Initialized SupersetController', [
            'superset_url' => $this->supersetUrl,
            'admin_credentials' => array_merge($this->adminCredentials, ['password' => '****'])
        ]);
    }

    /**
     * Executes a cURL request with common configurations and logging.
     *
     * @param resource $ch cURL handle with initialized URL.
     * @param array $headers HTTP headers for the request.
     * @param string|null $postData JSON-encoded POST data, if applicable.
     * @param bool $includeHeaders Whether to include response headers.
     * @return array Response data, HTTP code, redirect URL, and verbose log.
     * @throws ApiGenericException If the request fails.
     */
    protected function executeCurlRequest($ch, array $headers, $postData = null, $includeHeaders = false)
    {
        // Set common cURL options
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 15);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);
        // Disable SSL verification for debugging (enable in production)
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        // Set POST options if data is provided
        if ($postData !== null) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        }

        // Include headers in response if requested
        if ($includeHeaders) {
            curl_setopt($ch, CURLOPT_HEADER, true);
        }

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);

        // Check for cURL errors or non-200 status
        if ($response === false || $httpCode !== 200) {
            $error = curl_error($ch);
            curl_close($ch);
            $this->log->error('cURL request failed', [
                'error' => $error,
                'http_code' => $httpCode,
                'redirect_url' => $redirectUrl,
                'verbose' => $verboseLog
            ]);
            throw new ApiGenericException("Request failed: $error", 500);
        }

        return [
            'response' => $response,
            'http_code' => $httpCode,
            'redirect_url' => $redirectUrl,
            'verbose_log' => $verboseLog,
            'curl_handle' => $ch
        ];
    }

    /**
     * Authenticates with Superset to obtain a JWT access token.
     *
     * @return string Access token.
     * @throws ApiGenericException If authentication fails or no token is returned.
     */
    protected function authenticateSuperset()
    {
        $this->log->info('Starting authentication with Superset');
        $ch = curl_init($this->supersetUrl . '/api/v1/security/login');
        $headers = ['Content-Type: application/json'];
        $postData = json_encode($this->adminCredentials);

        try {
            $result = $this->executeCurlRequest($ch, $headers, $postData);
            $responseData = json_decode($result['response'], true);

            if (!isset($responseData['access_token'])) {
                curl_close($result['curl_handle']);
                $this->log->error('No access token in response', ['response' => $responseData]);
                throw new ApiGenericException('No access token in response', 500);
            }

            $accessToken = $responseData['access_token'];
            curl_close($result['curl_handle']);
            $this->log->info('Access token obtained', ['access_token' => substr($accessToken, 0, 10) . '...']);
            return $accessToken;
        } catch (ApiGenericException $e) {
            $this->log->error('Authentication failed', ['error' => $e->getMessage()]);
            throw new ApiGenericException($e->getMessage());
        }
    }

    /**
     * Fetches the CSRF token and session cookie from Superset.
     *
     * @param string $accessToken JWT access token for authentication.
     * @return array CSRF token and session cookie.
     * @throws ApiGenericException If CSRF token fetch fails or no token is returned.
     */
    protected function fetchCsrfToken($accessToken)
    {
        $this->log->info('Fetching CSRF token');
        $ch = curl_init($this->supersetUrl . '/api/v1/security/csrf_token/');
        $headers = [
            'Authorization: Bearer ' . $accessToken,
            'Content-Type: application/json'
        ];

        try {
            $result = $this->executeCurlRequest($ch, $headers, null, true);
            $responseParts = explode("\r\n\r\n", $result['response'], 2);
            $headers = $responseParts[0];
            $body = isset($responseParts[1]) ? $responseParts[1] : '';
            $responseData = json_decode($body, true);

            if (!isset($responseData['result'])) {
                curl_close($result['curl_handle']);
                $this->log->error('No CSRF token in response', ['response' => $responseData]);
                throw new ApiGenericException('No CSRF token in response', 500);
            }

            $csrfToken = $responseData['result'];
            preg_match('/set-cookie: ([^\n]+)/i', $headers, $cookieMatch);
            $sessionCookie = isset($cookieMatch[1]) ? $cookieMatch[1] : '';

            curl_close($result['curl_handle']);
            $this->log->info('CSRF token obtained', ['csrf_token' => substr($csrfToken, 0, 10) . '...']);
            return [
                'csrf_token' => $csrfToken,
                'session_cookie' => $sessionCookie
            ];
        } catch (ApiGenericException $e) {
            $this->log->error('CSRF token fetch failed', ['error' => $e->getMessage()]);
            throw new ApiGenericException($e->getMessage());
        }
    }

    /**
     * Requests a guest token from Superset with row-level security.
     *
     * @param string $accessToken JWT access token for authentication.
     * @param string $csrfToken CSRF token for the request.
     * @param string $sessionCookie Session cookie for the request.
     * @return array Guest token response.
     * @throws ApiGenericException If guest token fetch fails or no token is returned.
     */
    protected function requestGuestToken($accessToken, $csrfToken, $sessionCookie, $dashboardId)
    {
        $assignedDashboards = PartnerMappedDashboardList::where('id', $dashboardId)->first();
        if (!$assignedDashboards) {
            $this->log->error('PartnerMappedDashboardList entry not found', ['dashboardId' => $dashboardId]);
            throw new ApiGenericException('Dashboard mapping not found', 404);
        }

        $getDashboardData = DADashboard::where('id', $assignedDashboards->dashboard_id)->first();

        $getUser = User::where('id', $assignedDashboards->partner_id)->first();
        if ($getUser->user_type == 3) {
            $getFacilityIds = Facility::where('owner_id', $getUser->id)->pluck('id')->toArray();
        } else {
            $getFacilityIds = UserFacility::where('user_id', $assignedDashboards->partner_id)->pluck('facility_id')->toArray();
        }

        if (!$getDashboardData) {
            $this->log->error('DADashboard entry not found', ['dashboard_id' => $assignedDashboards->dashboard_id]);
            throw new ApiGenericException('Dashboard data not found', 404);
        }

        $clause = '';

        if (!empty($getDashboardData->rls)) {
            $this->log->debug('Raw RLS string', ['rls' => $getDashboardData->rls]);

            // Handle potential double-escaping
            $decodedRls = $this->safeJsonDecode($getDashboardData->rls);

            $this->log->debug('Preprocessed and decoded RLS', [
                'decoded_rls' => $decodedRls,
                'json_error' => json_last_error_msg()
            ]);

            if (json_last_error() === JSON_ERROR_NONE && is_array($decodedRls)) {
                $clauses = [];

                $placeholders = [
                    '{{partner_id}}',
                    '{{id}}',
                    '{{owner_id}}',
                    '{{facility_id}}'
                ];

                $replacements = [
                    $assignedDashboards->partner_id,
                    $assignedDashboards->id,
                    $assignedDashboards->owner_id,
                    $assignedDashboards->facility_id ?? ''
                ];

                foreach ($decodedRls as $key => $rawValue) {
                    // Check if the RLS value contains facility_id placeholder
                    if (strpos($rawValue, '{{facility_id}}') !== false && !empty($getFacilityIds)) {
                        // Format facility_ids for WHERE IN condition
                        $formattedFacilityIds = implode("','", array_map('intval', $getFacilityIds));
                        $value = str_replace('{{facility_id}}', $formattedFacilityIds, $rawValue);
                        $clauses[] = "$key IN ('$value')";
                    } else {
                        // Replace other placeholders normally
                        $value = str_replace($placeholders, $replacements, $rawValue);
                        $clauses[] = "$key = '$value'";
                    }
                }

                if (!empty($clauses)) {
                    $clause = implode(' AND ', $clauses);
                } else {
                    $this->log->warn('No valid RLS keys found, defaulting to partner_id');
                    $clause = "partner_id = '{$assignedDashboards->partner_id}'";
                }
            } else {
                $this->log->error('Failed to decode RLS JSON', [
                    'rls' => $getDashboardData->rls,
                    'error' => json_last_error_msg()
                ]);
                $clause = "partner_id = '{$assignedDashboards->partner_id}'";
            }
        } else {
            $this->log->info('RLS is empty, using default clause based on partner_id');
            $clause = "partner_id = '{$assignedDashboards->partner_id}'";
        }

        $this->log->info('Requesting guest token with RLS', ['clause' => $clause]);
        // dd(['clause' => $clause]);
        $ch = curl_init($this->supersetUrl . '/api/v1/security/guest_token/');
        $payload = [
            'user' => [
                'username' => 'guest-' . uniqid(),
                'first_name' => 'Guest',
                'last_name' => 'User'
            ],
            'rls' => [
                ['clause' => $clause]
            ],
            'resources' => [
                ['type' => 'dashboard', 'id' => $getDashboardData->embedded_id]
            ]
        ];

        $headers = [
            'Authorization: Bearer ' . $accessToken,
            'X-CSRFToken: ' . $csrfToken,
            'Cookie: ' . $sessionCookie,
            'Content-Type: application/json'
        ];

        $postData = json_encode($payload);

        try {
            $result = $this->executeCurlRequest($ch, $headers, $postData);
            $responseData = json_decode($result['response'], true);

            if (!isset($responseData['token'])) {
                curl_close($result['curl_handle']);
                $this->log->error('No guest token in Superset response', ['response' => $responseData]);
                throw new ApiGenericException('No guest token in response', 500);
            }

            curl_close($result['curl_handle']);
            $this->log->info('Guest token generated successfully');
            return $responseData;
        } catch (ApiGenericException $e) {
            $this->log->error('Guest token fetch failed', ['error' => $e->getMessage()]);
            throw new ApiGenericException($e->getMessage());
        }
    }


    protected function safeJsonDecode($json, $maxDepth = 3)
    {
        $depth = 0;
        while (is_string($json) && $depth < $maxDepth) {
            $decoded = json_decode($json, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $json = $decoded;
            } else {
                break;
            }
            $depth++;
        }
        return is_array($json) ? $json : null;
    }

    /**
     * Generates a guest token for embedding a Superset dashboard with row-level security.
     *
     * @return \Illuminate\Http\JsonResponse Guest token or error response.
     */
    public function getGuestToken(Request $request)
    {
        try {

            $dashboardId = $request->dashboard_id;
            $loggedInUserId = Auth::user()->id;

            $arrData = ['mapped_id' => $dashboardId, 'login_id' => $loggedInUserId];
            if (Auth::user()->user_type != '1') {
                $isDashboardAccessAvailable = $this->checkDashboardAccess($arrData);
                if (!$isDashboardAccessAvailable) {
                    throw new ApiGenericException('Dashboard access is not available');
                }
            }

            // Authenticate with Superset to get access token
            $accessToken = $this->authenticateSuperset();

            // Fetch CSRF token and session cookie
            $csrfData = $this->fetchCsrfToken($accessToken);

            // Request guest token with RLS
            $result = $this->requestGuestToken(
                $accessToken,
                $csrfData['csrf_token'],
                $csrfData['session_cookie'],
                $dashboardId
            );

            return response()->json($result);
        } catch (ApiGenericException $e) {
            throw new ApiGenericException($e->getMessage());
        } catch (\Exception $e) {
            throw new ApiGenericException($e->getMessage());
        }
    }


    /**
     * Check if the logged-in user has access to the given Superset dashboard.
     *
     * @param array $arrData
     *   - 'mapped_id': The ID from the partner_mapped_dashboard_list table that maps a user to a dashboard.
     *   - 'login_id': The ID of the currently logged-in user.
     *
     * @return bool
     *   Returns true if the user has access to the specified dashboard; false otherwise.
     */
    public function checkDashboardAccess($arrData)
    {
        $assignedDashboards = PartnerMappedDashboardList::where('partner_mapped_dashboard_list.id', $arrData['mapped_id'])->first();

        if (!$assignedDashboards) {
            return false;
        }
        $dashboardId = $assignedDashboards->dashboard_id;
        $assignedDashboards = PartnerMappedDashboardList::where('partner_mapped_dashboard_list.dashboard_id', $dashboardId)->where('partner_mapped_dashboard_list.partner_id', $arrData['login_id'])->first();

        $isExist = $assignedDashboards ? true : false;
        return $isExist;
    }



    /**
     * Retrieve all Superset dashboards assigned to a specific partner.
     *
     * This function fetches dashboards mapped to a partner, appends the partner_id
     * to the RLS (Row Level Security) rules, and returns the complete list.
     *
     * @param Request $request
     *   - Requires 'partner_id' (ID of the partner/user).
     *
     * @return array
     *   - 'partner_id': The ID of the partner.
     *   - 'assigned_dashboards': A list of dashboard records with RLS data included.
     */
    // public function getDashboardsByPartner(Request $request)
    // {
    //     $this->validate($request, [
    //         'partner_id' => 'required|exists:users,id'
    //     ]);

    //     $partnerId = $request->input('partner_id');

    //     $assignedDashboards = PartnerMappedDashboardList::where('partner_id', $partnerId)
    //         ->join('da_dashboards', 'partner_mapped_dashboard_list.dashboard_id', '=', 'da_dashboards.id')
    //         ->select('da_dashboards.*', 'partner_mapped_dashboard_list.id as mapped_id')
    //         ->where('da_dashboards.deleted_at', null)
    //         ->get()
    //         ->map(function ($dashboard) use ($partnerId) {
    //             // Initialize rls as an array
    //             $rls = [];

    //             // Try to decode the rls JSON string if it exists
    //             if (!empty($dashboard->rls)) {
    //                 $decodedRls = json_decode($dashboard->rls, true);
    //                 // Check if decoding was successful and resulted in an array
    //                 if (json_last_error() === JSON_ERROR_NONE && is_array($decodedRls)) {
    //                     $rls = $decodedRls;
    //                 }
    //             }

    //             // Set partner_id in the rls array
    //             $rls['partner_id'] = $partnerId;

    //             // Encode back to JSON string
    //             $dashboard->rls = json_encode($rls);

    //             return $dashboard;
    //         });

    //     $response = [
    //         'partner_id' => $partnerId,
    //         'assigned_dashboards' => $assignedDashboards
    //     ];

    //     return $response;
    // }

    public function getDashboardsByPartner(Request $request)
    {
        $this->validate($request, [
            'login_user_id' => 'required|exists:users,id'
        ]);

        $loggedInUserId = $request->input('login_user_id');
        if (isset($request->partner_id) && !empty($request->partner_id)) {
            $partnerId = $request->input('partner_id');
        } else {
            $partnerId = $loggedInUserId;
        }

        $assignedDashboards = PartnerMappedDashboardList::where('partner_id', $loggedInUserId)
            ->join('da_dashboards', 'partner_mapped_dashboard_list.dashboard_id', '=', 'da_dashboards.id')
            ->select('da_dashboards.*', 'partner_mapped_dashboard_list.id as mapped_id')
            ->where('da_dashboards.deleted_at', null)
            ->get()
            ->map(function ($dashboard) use ($partnerId) {
                // Initialize rls as an array
                $rls = [];

                // Try to decode the rls JSON string if it exists
                if (!empty($dashboard->rls)) {
                    $decodedRls = json_decode($dashboard->rls, true);
                    // Check if decoding was successful and resulted in an array
                    if (json_last_error() === JSON_ERROR_NONE && is_array($decodedRls)) {
                        $rls = $decodedRls;
                    }
                }

                // Set partner_id in the rls array
                $rls['partner_id'] = $partnerId;

                // Encode back to JSON string
                $dashboard->rls = json_encode($rls);

                return $dashboard;
            });

        $response = [
            'partner_id' => $partnerId,
            'assigned_dashboards' => $assignedDashboards
        ];

        return $response;
    }
}
