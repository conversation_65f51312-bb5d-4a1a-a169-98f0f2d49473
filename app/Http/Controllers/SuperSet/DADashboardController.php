<?php

/* 
  *  Copyright (c) 2024 ParkEngage Inc. All rights reserved.
  *  
  *  This software is the confidential and proprietary information of
  *  ParkEngage Inc. ("Confidential Information"). You shall not
  *  disclose such Confidential Information and shall use it only in
  *  accordance with the terms of the license agreement you entered into
  *  with ParkEngage Inc.
  *  
  *  This source code is a valuable trade secret of ParkEngage Inc.
  *  
  *  THIS SOFTWARE IS PROVIDED BY ParkEngage Inc. ``AS IS'' AND ANY
  *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  *  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  *  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL [YOUR COMPANY NAME] OR
  *  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  *  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  *  PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  *  OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *  
  *  The views and conclusions contained in the software and documentation
  *  are those of the authors and should not be interpreted as representing
  *  *official policies, either expressed or implied, of ParkEngage Inc.
*/

namespace App\Http\Controllers\SuperSet;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\SuperSet\DADashboard;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\SuperSet\PartnerMappedDashboardList;

class DADashboardController extends Controller
{
    /**
     * Get validation rules for dashboards.
     *
     * @return array
     */
    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'embedded_id' => 'required|string|max:255',
            'rls' => 'required|json',
            'ui_config' => 'json' // optional
        ];
    }

    /**
     * Display a paginated list of dashboards with optional filters.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function index(Request $request)
    {
        $query = DADashboard::query();

        if ($request->has('search_text') && $request->get('search_text') !== '') {
            $search = $request->get('search_text');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                    ->orWhere('embedded_id', 'like', '%' . $search . '%');
            });
        }

        return $query->paginate($request->get('per_page', 10));
    }



    /**
     * Store a newly created dashboard in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Models\SuperSet\DADashboard
     * @throws \App\Exceptions\ApiGenericException
     */
    public function store(Request $request)
    {
        $this->validateDashboard($request);

        DB::beginTransaction();
        try {
            // Check for duplicate embedded_id or name
            $exists = DADashboard::where('embedded_id', $request->get('embedded_id'))
                ->orWhere('name', $request->get('name'))
                ->first();

            if ($exists) {
                throw new ApiGenericException('A dashboard with the same name or embedded ID already exists.');
            }

            $dashboard = DADashboard::create($request->only(array_keys($this->rules())));
            DB::commit();

            return $dashboard;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApiGenericException('Failed to create dashboard: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified dashboard.
     *
     * @param  int  $id
     * @return \App\Models\SuperSet\DADashboard
     * @throws \App\Exceptions\NotFoundException
     */
    public function show($id)
    {
        return $this->findDashboardOrFail($id);
    }

    /**
     * Update the specified dashboard in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \App\Models\SuperSet\DADashboard
     * @throws \App\Exceptions\ApiGenericException
     * @throws \App\Exceptions\NotFoundException
     */
    public function update(Request $request, $id)
    {
        $this->validateDashboard($request);
        $dashboard = $this->findDashboardOrFail($id);

        DB::beginTransaction();
        try {
            // Check for duplicate name or embedded_id in other records
            $exists = DADashboard::where(function ($query) use ($request) {
                $query->where('embedded_id', $request->get('embedded_id'))
                    ->orWhere('name', $request->get('name'));
            })
                ->where('id', '<>', $id)
                ->first();

            if ($exists) {
                throw new ApiGenericException('Another dashboard with the same name or embedded ID already exists.');
            }

            $dashboard->update($request->only(array_keys($this->rules())));
            DB::commit();

            return $dashboard;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApiGenericException('Failed to update dashboard: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified dashboard from storage.
     *
     * @param  int  $id
     * @return string
     * @throws \App\Exceptions\NotFoundException
     */
    public function destroy($id)
    {
        $dashboard = $this->findDashboardOrFail($id);

        DB::beginTransaction();
        try {
            $dashboard->delete();
            DB::commit();
            return 'Dashboard deleted successfully';
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApiGenericException('Failed to delete dashboard: ' . $e->getMessage());
        }
    }

    /**
     * Validate dashboard data.
     *
     * @param  \Illuminate\Http\Request  $request
     * @throws \App\Exceptions\ApiGenericException
     */
    protected function validateDashboard(Request $request)
    {
        $validator = Validator::make($request->all(), $this->rules());

        if ($validator->fails()) {
            throw new ApiGenericException($validator->errors());
        }
    }

    /**
     * Retrieve a dashboard by ID or fail.
     *
     * @param  int  $id
     * @return \App\Models\SuperSet\DADashboard
     * @throws \App\Exceptions\NotFoundException
     */
    protected function findDashboardOrFail($id)
    {
        $dashboard = DADashboard::find($id);

        if (!$dashboard) {
            throw new NotFoundException('Dashboard not found');
        }

        return $dashboard;
    }


    public function assignDashboardsToPartner(Request $request)
    {
        $this->validate($request, [
            'partner_id' => 'required|exists:users,id',
            'dashboard_ids' => 'required|array',
            'dashboard_ids.*' => 'distinct|exists:da_dashboards,id'
        ]);

        $partnerId = $request->input('partner_id');
        $dashboardIds = $request->input('dashboard_ids');

        //Pre-check if all dashboard mappings already exist
        $existing = PartnerMappedDashboardList::where('partner_id', $partnerId)
            ->whereIn('dashboard_id', $dashboardIds)
            ->pluck('dashboard_id')
            ->toArray();

        $newDashboardIds = array_diff($dashboardIds, $existing);

        if (empty($newDashboardIds)) {
            return response()->json([
                'message' => 'All provided dashboards are already assigned to this partner.'
            ], 409); // 409 Conflict
        }

        //Insert only new mappings
        DB::beginTransaction();
        try {
            foreach ($newDashboardIds as $dashboardId) {
                PartnerMappedDashboardList::create([
                    'partner_id' => $partnerId,
                    'dashboard_id' => $dashboardId
                ]);
            }

            DB::commit();
            return response()->json(['message' => 'Dashboards assigned successfully.']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApiGenericException('Failed to assign dashboards: ' . $e->getMessage());
        }
    }

    public function getDashboardsByPartner(Request $request)
    {
        $this->validate($request, [
            'partner_id' => 'required|exists:users,id'
        ]);

        $partnerId = $request->input('partner_id');

        $assignedDashboards = PartnerMappedDashboardList::where('partner_id', $partnerId)
            ->join('da_dashboards', 'partner_mapped_dashboard_list.dashboard_id', '=', 'da_dashboards.id')
            ->select('da_dashboards.*')
            ->get();

        $response = [
            'partner_id' => $partnerId,
            'assigned_dashboards' => $assignedDashboards
        ];
        return $response;
    }
}
