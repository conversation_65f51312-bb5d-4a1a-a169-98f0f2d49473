<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;

use App\Classes\ArApi;
use App\Classes\AuthorizeNet\Cim;
use App\Models\MonthlyParkingUser;
use App\Models\AutopayMethod;
use App\Models\PaymentProfile;

use App\Exceptions\UserNotAuthorized;

/**
 * Man<PERSON> requests to update monthly parking user's autopay preferences
 */
class AutopayMethodsController extends Controller
{

    protected $request;
    protected $account;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Get currently configured autopay settings for this monthly parking user
     *
     * @param  Request            $request       [description]
     * @param  MonthlyParkingUser $this->account [description]
     * @return [type]                      [description]
     */
    public function getAutopaySettings(MonthlyParkingUser $account)
    {
        $authorizeNet = new Cim();
        $autoPayStatus = $account->autopay;
        $autopaymethodWithCard =$autopayMethods = $account->autopayMethods->load('paymentProfile');
       
        if(count($autopayMethods)>0)
        {
            $i=0;
            $payment_profile_id = false;
            foreach($autopayMethods as $autopaymethod)
            {
                
                $payment_profile_id = ($autopaymethod->paymentProfile->payment_profile)?$autopaymethod->paymentProfile->payment_profile:false;
                if($payment_profile_id)
                {
                    $autopayMethods[$i]['card_details'] = $authorizeNet->setMonthlyParkingUser($account)->getPaymentProfile($payment_profile_id);
                }else{
                    $autopayMethods[$i]['card_details'] = false;
                }
                 $i++;
            }
        }
        return [
            'autopay' => $account->autopay,
            'autopay_methods' =>$autopayMethods
        ];
    }

    /**
     * Update the auto pay preferences for this account. A user can have multiple autopay
     * payments. Request should include an array of payment methods, in order, with the corresponding amounts
     * that can be charged to each payment method
     *
     * @return array something
     */
    public function updateAutopay(MonthlyParkingUser $account)
    {
        $this->account = $account;

        // Note: The .*. syntax is used to validate array data members
        $this->validate(
            $this->request, [
            'autopay' => 'required|boolean',
            'payments.*.payment_profile_id' => 'exists:anet_payment_profiles,payment_profile',
            'payments.*.amount' => 'numeric',
            'payment.*.pay_remainder' => 'boolean'
            ]
        );

        if (!$this->request->autopay) {
            return $this->deactivateAutopay($this->request, $this->account);
        }

        if ($this->account->autopay) {
            $this->saveAutopayMethods($this->request, $this->account);
            return $this->account->load('facility', 'autopayMethods');
        }
        return $this->activateAutopay($this->request, $this->account);
    }

    protected function saveAutopayMethods()
    {
        // Check that account owns each submitted payment profile
        foreach ($this->request->payments as $payment) {
            if (!$this->account->ownsPaymentProfile($payment['payment_profile_id'])) {
                throw new UserNotAuthorized('User does not own this payment method.', ['invalid_method' => $payment]);
            }
        }

        $this->account->deleteAutopayMethods();

        // Create the new payment methods
        $autopayArray = array_map(
            function ($payment, $index) {
                $paymentProfile = PaymentProfile::where('payment_profile', $payment['payment_profile_id'])->first();
                $amount = count($this->request->payments) > 1 ? $payment['amount'] : 0;
                return AutopayMethod::create(
                    [
                    'mp_user_id' => $this->account->id,
                    'payment_profile_id' => $paymentProfile->id,
                    'amount' => $amount,
                    'order' => $index,
                    'pay_remainder' => $amount == 0 ? 1 : 0
                    ]
                );
            }, $this->request->payments, array_keys($this->request->payments)
        );

        $paymentProfile =$this->request->payments[0]['payment_profile_id'];
        $paymentProfile = (new Cim())->setMonthlyParkingUser($this->account)->getPaymentProfile($paymentProfile);

        $type = $paymentProfile['card'] ? ArApi::PAYMENT_METHOD_CC : ArApi::PAYMENT_METHOD_ACH;
        ArApi::autopayStatus($this->account->account_number, $type);
        return $autopayArray;
    }

    protected function activateAutopay()
    {
        $this->saveAutopayMethods($this->request, $this->account);
        $this->account->sendAutopayStatusEmail();
        return $this->account->load('facility', 'autopayMethods');
    }

    protected function deactivateAutopay()
    {
        $this->account->deleteAutopayMethods()->sendAutopayStatusEmail();
        ArApi::autopayStatus($this->account->account_number, ArApi::PAYMENT_METHOD_DISABLED);
        return $this->account->load('facility');
    }
}
