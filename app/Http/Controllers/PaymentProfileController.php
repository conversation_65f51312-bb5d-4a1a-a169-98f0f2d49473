<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;

use App\Classes\AuthorizeNet\Cim;

use App\Models\PaymentProfile;

class PaymentProfileController extends Controller
{

    public function __construct()
    {
        $this->cim = new Cim();
    }


    /**
     * Get a payment profile, including information from authorize.net
     *
     * @return array
     */
    public function get($paymentProfileId)
    {
        $payment = PaymentProfile::where('payment_profile', $paymentProfileId)->first();
        $user = $payment->cim->user;

        $profile = $this->cim->setUser($user)->getPaymentProfile($paymentProfileId);

        return $profile;
    }
}
