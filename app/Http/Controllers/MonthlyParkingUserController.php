<?php

namespace App\Http\Controllers;

use Auth;
use Exception;

use Illuminate\Http\Request;
use Carbon\Carbon;

use App\Classes\ArApi;
use App\Classes\AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;

use App\Classes\LoyaltyProgram;

use App\Services\Mailers\UserMailer;

use App\Models\MonthlyParkingUser;
use App\Models\Facility;
use App\Models\MonthlyParkingUserPayment;
use App\Models\AuthorizeNetTransaction;
use App\Models\ExpirationDate;

use App\Exceptions\InvalidResponseException;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Exceptions\DuplicateEntryException;
use App\Exceptions\UserNotAuthorized;

/**
 * Controls monthly parking user/account actions
 * TODO: Extract payment methods on this controller into a payment methods controller
 */
class MonthlyParkingUserController extends Controller
{

    protected function getAccountsForUser($user)
    {
        return MonthlyParkingUser::with(
            ['facility' => function ($query) {
                $query->withTrashed();
            }]
        )->where('user_id', $user->id)->get();
    }

    /**
     * Get monthly parking account IDs for the currently authed user.
     * This is useful because getting full account information can be an expensive operation,
     * as reading each account from the AR API can take up to 3-5 seconds
     *
     * @param  Request $request [description]
     * @return [type]           [description]
     */
    public function getUserAccountIds(Request $request)
    {
        $accounts = $this->getAccountsForUser(Auth::user());

        return $accounts->map(
            function ($account) {
                return $account->account_number;
            }
        );
    }

    /**
     * Get all monthly parking accounts that belong to the currently
     * authed user
     */
    public function getUserAccounts(Request $request)
    {
        return $this->getAccountsForUser(Auth::user());
    }
    
    /**
     * check if user has monthly account or not, return yes or no accordingly
     * authed user
     */
    public function checkUserMonthlyAccounts(Request $request)
    {
        
        $user = Auth::user();
        $userAcounts= $this->getAccountsForUser($user);
       
        return [
            'isMonthlyAccountUser'=>((isset($userAcounts) && (count($userAcounts)>0))?'1':'0'),
            'is_loyalty' => $user->is_loyalty
        ];
        
    }

    /**
     * Get account by account number if currently authed user owns that account.
     * Also allow retrieval is user is admin
     *
     * @param  Request $request [description]
     * @param  [type]  $account [description]
     * @return [type]           [description]
     */
    public function getAccount(Request $request, $account)
    {
        $user = Auth::user();

        $mpUser = MonthlyParkingUser::where('account_number', $account)->with(
            ['facility' => function ($query) {
                $query->withTrashed()->with('photos');
            }]
        )->first();

        if (!$mpUser) {
            throw new NotFoundException('No monthly parking account with that account number.');
        }

        if ($mpUser->user_id !== $user->id && !$user->isAdmin) {
            return $mpUser;
        }

        throw new UserNotAuthorized('You do not have access to this monthly parking account.');
    }

    /**
     * Get the locally stred billing address for this account
     *
     * @param  Request $request   [description]
     * @param  integer $accountId Account to get address for
     * @return [type]             [description]
     */
    public function getBillingAddress(Request $request, MonthlyParkingUser $account)
    {
        return array_only(
            $account->toArray(), [
            'name',
            'company_name',
            'address_one',
            'address_two',
            'city',
            'state',
            'zip',
            'country',
            'phone_number_one',
            'phone_number_two',
            'phone_ext_one',
            'phone_ext_two',
            'phone_type_one',
            'phone_type_two',
            'phone_contact_one',
            'phone_contact_two',
            'address_type',
            'billing_method'
            ]
        );
    }

    /**
     * Register an already existing monthly parking account.
     * This method calls to the ArApi to see if the account exists.
     * If it exists, add to our database and return a positive response.
     *
     * @param $request Request
     */
    public function registerAccount(Request $request)
    {
        $this->validate(
            $request, [
            'account_id' => 'required|integer',
            'zip' => 'required|size:5'
            ]
        );

        $accountId = $request->input('account_id');
        $zip = $request->input('zip');

        // Make sure we don't already have an account saved and assigned to a user
        $existing = MonthlyParkingUser::where('account_number', $accountId)->whereNotNull('user_id')->first();


        if ($existing) {
            throw new DuplicateEntryException('Account with this ID already exists. Please log in to edit your account.');
        }

        $user = Auth::user();

        // Will throw if the account does not exist, no need to check here
        $arAccount = self::getArAccount($accountId);

        // Transform AR API fields to local database fields
        $transformed = MonthlyParkingUser::transformFromArFields($arAccount);

        // Check we don't have this account trashed
        $account = MonthlyParkingUser::onlyTrashed()->where('account_number', $accountId)->where('zip', $zip)->first();

        if ($account) { // Restore and return the deleted account
            $account->restore();
        } else {
            // Create a new account or grab the current one if it has a null user id
            $account = MonthlyParkingUser::firstOrNew(['account_number' => $accountId, 'user_id' => null]);
        }

        $account->fill($transformed);

        // Make sure that the account data matches up with the zip sent through
        if ($account->zip !== $zip) {
            throw new NotFoundException('Cannot find an account with that ID and zip');
        }

        // Set user and facility here
        $facility = Facility::where('garage_code', $account->garage_code)->first();

        if (!$user) {
            throw new NotFoundException('No corresponding user found for this account.');
        }

        if (!$facility) {
            throw new NotFoundException('No corresponding facility found for this account.');
        }

        $account->facility_id = $facility->id;
        $account->user_id = $user->id;

        try {
            $account->save();
        } catch (Exception $e) {
            throw new ApiGenericException("Could not add account - account number may already exist.");
        }
        

        
        // Update accounts email address in the AR API system
        ArApi::updateEmailAddress($account->account_number, $user->email);
        $account->getBalances();

        if ($user->is_loyalty == LoyaltyProgram::CONST_ZERO && $user->is_loyalty_active == LoyaltyProgram::CONST_ZERO) {
            self::getArActiveAccount($accountId);            
            $res = LoyaltyProgram::registerMonthlyUserById($user->id);
                     
            if (!$res['success']) {
                 throw new ApiGenericException("There was an error registering the user with loyalty program.");
            } 

        }
        
        return $account->load('facility');
    }

    public function removeAccount(MonthlyParkingUser $account)
    {
        if ($account->cim) {
            $account->cim->delete();
        }

        if ($account->paymentProfiles) {
            $cim = $cim = (new Cim())->setMonthlyParkingUser($account);

            $account->paymentProfiles->each(
                function ($profile) use ($cim) {
                    $cim->deleteCustomerPaymentProfile($profile->payment_profile); // Deletes from Authorize.Net and local database
                }
            );
        }

        // Deactivate autopay for this account
        $account->deleteAutopayMethods();
        $account->delete();

        return $account;
    }

    /**
     * Send an email to icon parking systems to cancel this monthly parking account
     *
     * @param  $request  [description]
     * @param  [type]                 $acountId [description]
     * @return [type]            [description]
     */
    public function cancelAccount(Request $request, MonthlyParkingUser $account)
    {
        $this->validate(
            $request, [
            'termination_date' => 'required|date|after:yesterday',
            'tenant_number' => 'string',
            'comments' => 'string'
            ]
        );

        $subject_admin = "Cancellation request for monthly parking account $account->account_number";
        $subject_user = "Cancellation Request: $account->account_number";

        $data = [
            //'mp_id' => $account->id,
            'mp_id' => $account->account_number,
            'accountId' => $account->account_number,
            'termination_date' => Carbon::createFromFormat('Y-m-d', $request->input('termination_date'))->toFormattedDateString(),
            'tenant_number' => $request->input('tenant_number'),
            'comments' => $request->input('comments'),
        ];

        $mailer = new UserMailer();
        $mailer->queueMailTo(config('icon.customer_service_email'), $subject_admin, $data, 'email.monthly-parking-cancellation-admin');
        $mailer->queueMailTo($account->user->email, $subject_user, $data, 'email.monthly-parking-cancellation-user');

        return ['sent' => true];
    }

    /**
     * Get balances for the given account.
     * This needs to be cached for a set period of time (e.g. 15 minutes) because slow.
     *
     * @param  $accountId The AR account ID to get billing information for
     * @return array billing information returned from the ar api
     */
    public function getAccountBalances(MonthlyParkingUser $account)
    {
        // This method handles calling the API/caching
        $account->getBalances();

        return [
            'balance' => $account->balance,
            'curr_month_balance' => $account->curr_month_balance
        ];
    }

    /**
     * Get payment methods that for this monthly parking user account
     * TODO: Does this need to me moved onto the model?
     *
     * @param  $request
     * @param  integer $accountId ID of this account
     * @return [type]             [description]
     */
    public function getPaymentMethods(Request $request, MonthlyParkingUser $account)
    {
        $authorizeNet = new Cim();
        $response = $authorizeNet->setMonthlyParkingUser($account)->getCustomerProfile();

        if (!$response['profile_id']) {
           // throw new NotFoundException('No customer profile exists for this monthly parking user.');
            throw new NotFoundException('There are currently no saved payment methods on this account.');
        }

        // Add our stored expiration dates if available
        $response['payments'] = array_map(
            function ($payment) {
                if (!$payment['card']) {
                    return $payment;
                }

                $expirationDate = ExpirationDate::whereHas(
                    'paymentProfile', function ($query) use ($payment) {
                        $query->where('payment_profile', $payment['payment_profile_id']);
                    }
                )->first();
                if (!$expirationDate) {
                    return $payment;
                }

                $payment['expiration_month'] = $expirationDate->expiration_month;
                $payment['expiration_year'] = $expirationDate->expiration_year;
                return $payment;
            }, $response['payments']
        );

        return $response;
    }

    /**
     * Update the billing information for this accont locally and in the AR API
     * Since we don't have access to a development version of the AR API,
     * if this is the dev or staging site we are only mocking calls to the AR API.
     * Hopefully someone will figure out a way to test this API. Please. Help.
     *
     * @param  $accountId integer AR API account number
     * @param  $request Request HTTP request
     * @return array Updated monthly parking user
     */
    public function updateBillingInformation(Request $request, MonthlyParkingUser $account)
    {
        $this->validate(
            $request, [
            'company_name' => 'string|max:150',
            'address_one' => 'string|max:100',
            'city' => 'string|max:50',
            'state' => 'string|max:50',
            'zip' => 'string|max:15',
            'country' => 'string|max:50',
            'phone_ext_one' => 'string|max:7',
            'phone_ext_two' => 'string|max:7',
            'phone_type_one' => 'string|max:50',
            'phone_number_one' => 'string|digits:10',
            'phone_number_two' => 'string|digits:10',
            'phone_type_two' => 'string|max:50',
            'phone_contact_one' => 'string|max:50+',
            'phone_contact_two' => 'string|max:50',
            'address_type' => 'string',
            'billing_method' => 'string'
            ]
        );

        $address = $request->only(
            [
            'name', 'company_name', 'address_one', 'address_two','city', 'state',
            'zip', 'country', 'phone_number_one', 'phone_number_two', 'phone_ext_one',
            'phone_ext_two', 'phone_type_one', 'phone_type_two', 'phone_contact_one',
            'phone_contact_two', 'address_type', 'billing_method'
            ]
        );

        // Update in the AR API if we are not in production
        // Handles updating our cims and the AR API, as well as local
        $account->updateBillingAddress($address);

        return $account;
    }

    /**
     * Update payment method given in the path.
     * Must include a 'type' param in the request body, set to 'card' or 'bank'
     *
     * @param  Request $request          [description]
     * @param  [type]  $paymentProfileId [description]
     * @return [type]                    [description]
     */
    public function updatePaymentMethod(Request $request, MonthlyParkingUser $account, $paymentProfileId)
    {
        $type = $request->input('type');

        if (!$type || !in_array($type, ['card', 'bank'])) {
            throw new ApiGenericException('Type must be included and must be bank or card.');
        }

        // Make sure monthly parking account owns the payment profile
        // TODO: This is duplicated from the above method, might want to pull out
        $owns = $account->ownsPaymentProfile($paymentProfileId);
        if (!$owns) {
            throw new UserNotAuthorized('Monthly parking account does not own this payment method.');
        }

        if ($type === 'card') {
            return $this->updateCreditCard($request, $account, $paymentProfileId);
        }

        return $this->updateBankAccount($request, $account, $paymentProfileId);
    }

    /**
     * Update payment methods for this account
     * Updates payment profiles in Authorize.Net using the CIM for this account
     * Handles updating either check or
     *
     * @return array something
     */
    protected function updateCreditCard(Request $request, MonthlyParkingUser $account, $paymentProfileId)
    {
        $validationRules = [
            'name' => 'string|required',
            'card_number' => 'integer|required|digits_between:13,16',
            'expiration' => 'integer|required|digits:4',
            'security_code' => 'integer|required|digits_between:3,4'
        ];

        $this->validate($request, $validationRules);

        // TODO: Update payment profile methods need to be moved onto the payment profile model
        $update = $account->updateCreditCard(
            $paymentProfileId,
            $request->input('card_number'),
            $request->input('expiration'),
            $request->input('security_code')
        );

        return ['payment_profile_id' => $paymentProfileId];
    }

    /**
     * Update the bank account data stored in this accounts authorize.net payment profile
     *
     * @return array something
     */
    protected function updateBankAccount(Request $request, MonthlyParkingUser $account, $paymentProfileId)
    {
        $validationRules = [
            'name' => 'string|required',
            'account_type' => 'string|required|in:checking,savings,businessChecking',
            'routing' => 'integer|required|digits:9',
            'account_number' => 'integer|required|digits_between:5,17'
        ];

        $this->validate($request, $validationRules);

        $update = $account->updateBankAccount(
            $paymentProfileId,
            $request->input('account_type'),
            $request->input('routing'),
            $request->input('account_number'),
            $request->input('name')
        );

        return ['payment_profile_id' => $paymentProfileId];
    }

    /**
     * Get account for this account ID from the AR API
     *
     * @param $accountId integer Accounts receivable account ID
     */
    protected static function getArAccount($accountId)
    {

        $response = ArApi::lookupAccount($accountId);

        if ($response['status'] === 404 ||  $response['status'] === 400) {
            throw new NotFoundException('No account found with that ID.');
        }

        if ($response['status'] !== 200) {
            throw new InvalidResponseException('Invalid response returned from account API', ['api_response' => $response]);
        }

        return $response['data'];
    }

    /**
     * Get account for this account ID from the AR API
     *
     * @param $accountId integer Accounts receivable account ID
     */
    protected static function getArActiveAccount($accountId)
    {
        $response = ArApi::activeAccounts($accountId);

        if ($response['status'] === 404 ||  $response['status'] === 400) {
            throw new ApiGenericException("Your Monthly Account is added successfully but the monthly account is not currently active.",200);
        }

        if ($response['status'] !== 200) {
            throw new InvalidResponseException('Invalid response returned from account API', ['api_response' => $response]);
        }

        return $response['data'];
    }

    
}
