<?php

namespace App\Http\Controllers;

use Validator;
use Illuminate\Http\Request;
use App\Models\BasicMonthlyRequest;

use App\Http\Helpers\QueryBuilder;

use App\Http\Requests;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class BasicMonthlyRequestController extends Controller
{
    /**
     * Return a listing of all basic monthly requests
     *
     * @return [type] [description]
     */
    public function index(Request $request)
    {
        $monthlyRequests = BasicMonthlyRequest::query()->orderby('created_at', 'desc');

        if ($request->search) {
            $monthlyRequests = QueryBuilder::buildSearchQuery($monthlyRequests, $request->search, BasicMonthlyRequest::$searchFields);
        }

        return $monthlyRequests->paginate(20);
    }

    /**
     * Get a single monthly request by ID
     *
     * @param  BasicMonthlyRequest $monthlyRequest [description]
     * @return [type]                              [description]
     */
    public function getById(BasicMonthlyRequest $basicMonthlyRequest)
    {
        return $basicMonthlyRequest;
    }

    /**
     * Save a new basic monthly request to the database
     *
     * @param  Request $request [description]
     * @return [type]           [description]
     */
    public function store(Request $request)
    {
        $this->validate($request, BasicMonthlyRequest::$validParams);

        $basic = new BasicMonthlyRequest();
        $basic->fill(
            $request->only(
                [
                'user_id',
                'full_name',
                'email',
                'phone',
                'preferred_communication',
                'desired_area',
                'vehicle_model',
                'vehicle_make',
                'current_customer',
                'completed'
                ]
            )
        );

        // Throws a SQL constraint error if not present
        if (!$basic->user_id) {
            $basic->user_id = 0;
        }
        if (!$basic->completed) {
            $basic->completed = 0;
        }

        $basic->save();
        $basic->email();

        
        return $basic;
    }

    /**
     * Toggle completed status on the basic monthly request with the given ID
     *
     * @param  [type] $id [description]
     * @return [type]     [description]
     */
    public function toggleComplete($id)
    {
        $monthlyRequest = BasicMonthlyRequest::find($id);

        $monthlyRequest->completed = !$monthlyRequest->completed;
        $monthlyRequest->save();

        return $monthlyRequest;
    }
}
