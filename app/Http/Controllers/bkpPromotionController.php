<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Models\Promotion;
use App\Models\PromoCode;
use App\Models\PromoUsage;
use App\Models\User;
use App\Models\PromotionUser;
use App\Classes\PromoCodeLib;
use Validator;

use App\Exceptions\ApiGenericException;
use Illuminate\Support\Facades\DB;
use Auth;
use Excel;
use App\Models\PromotionVerification;
use App\Http\Helpers\QueryBuilder;
use App\Services\LoggerFactory;
use Carbon\Carbon;


class PromotionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $promotions = Promotion::where('status', 1)->get();
        return ['promotions' => $promotions];
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $deletedPromocode = PromoCode::withTrashed()->where('promocode', $request->couponscode)->whereNotNull('deleted_at')->get();
        if (count($deletedPromocode) > 0) {
            throw new ApiGenericException('Promocode deleted by admin. Please contact to admin for activate.');
        }
        $this->validate($request, Promotion::$ValidationRules, Promotion::$ValidationMessage);

        if ($request->valid_from > $request->valid_to) {
            throw new ApiGenericException('Error Occured, Invalid Validity Date Provided');
        }

        if($request->specific_user_type == '1'){
            if ($request->percentage_off_discount > $request->max_lifetime_discount || $request->discount_value > $request->max_lifetime_discount) {
                throw new ApiGenericException('Max Lifetime value should be greater discount amount or discount value');
            }

            if(!$request->PromotionCodeEmail && !$request->PromotionCodeEmailSheet){
                throw new ApiGenericException('Users email or excel file is required.');
            }

            if($request->PromotionCodeEmailSheet){
                
                $destination_path = public_path().'/assets/promotion-emails-spreadsheet/';
                $file = $request->file('PromotionCodeEmailSheet');
                $file_extension = $file->getClientOriginalExtension();
                
                /* File validation: */
                $validation = Validator::make(
                    ['file' => $file, 'extension' => $file_extension],
                    ['file' => 'required', 'extension' => 'required|in:xlsx,xls']
                );
                if($validation->fails()) {
                    throw new ApiGenericException("Please upload valid spreadsheet");
                }                
                
            }
        }

        if($request->couponscode!='') {
            $request->couponscode=strtoupper($request->couponscode);
            $dacouponscode = PromoCode::where('promocode', $request->couponscode)->first();

            if ($dacouponscode) {
                throw new ApiGenericException('This promocode already exist in record.');
            }            
        }

        $promo_type_id = $request->promo_type_id;
        $promotion = new Promotion();
        $promotion->fill($request->only(['promo_type_id','channel_partner_id','name','description','total_coupons','valid_from','valid_to','user_type','percentage_off_discount','dollar_off_discount','owner_id','max_lifetime_discount','status','is_tax_applicable']));
        $promotion->status = $request->status;
        $promotion->created_by = Auth::user()->id;
        if(Auth::user()->user_type == 1 || Auth::user()->user_type == 2){
            $promotion->owner_id = $request->partner_id;
        }
        if(Auth::user()->user_type == 3){
            $promotion->owner_id = Auth::user()->id;
        }

        if($request->valid_from == '1000-01-01' || $request->valid_from == ''){
            $promotion->valid_from = date("Y-m-d");
        }
        if($request->valid_to == '1000-01-01' || $request->valid_to == ''){
            $promotion->valid_to = '';
        }
        
        switch ($promo_type_id) {
        case 1:
            // Validate Against Static Type Promo Codes
            $this->validate($request, Promotion::$staticValidationRules);
            $promotion->fill($request->only(['usage','discount_type','discount_value']));
            break;
        case 2:
            // Validate Against Single Use Promo Codes
            $this->validate($request, Promotion::$singleValidationRules);
            $promotion->fill($request->only(['usage','discount_type','discount_value']));
            break;
        case 3:
            // Validate Against Promo Type Promo Codes
            $this->validate($request, Promotion::$promoValidationRules);
            $promotion->fill($request->only(['base_price','promo_price']));
            break;
        default:
            // code...
            break;
        }

        $promotion->save();

        if($request->specific_user_type == '1'){
            $promotion->specific_user_type = 1;
            $promotion->save();

            if($request->PromotionCodeEmail){
                $emails = explode(",", $request->PromotionCodeEmail);
                $emails = array_unique($emails);
                $promotionEmails = [];
                foreach ($emails as $key => $value) {
                    if(trim($value) != ''){
                        $promotionEmails[$key]['email'] = trim($value);
                        $promotionEmails[$key]['promotion_id'] = $promotion->id;    
                    }                    
                }
                PromotionUser::insert($promotionEmails);
            }

            if($request->PromotionCodeEmailSheet){
                
                $destination_path = public_path().'/assets/promotion-emails-spreadsheet/';
                $file = $request->file('PromotionCodeEmailSheet');
                $file_extension = $file->getClientOriginalExtension();
                
                /* File validation: */
                $validation = Validator::make(
                    ['file' => $file, 'extension' => $file_extension],
                    ['file' => 'required', 'extension' => 'required|in:xlsx,xls']
                );
                if($validation->fails()) {
                    throw new ApiGenericException("Please upload valid spreadsheet");
                }
                
                $file_name = 'promotion-emails.' . $file_extension;
                if(!$file->move($destination_path, $file_name)) {
                    throw new ApiGenericException("Something went wrong while importing users sheet");
                }
                
                $uploaded_file = $destination_path . $file_name;
                
                $result = Excel::load($uploaded_file, 
                function($reader) {})->get()->toArray();

                if(empty($result)) {
                    throw new ApiGenericException("No data found in the spreadsheet.");
                }
                
                $promotionEmails = [];
                $uniqueEmails = [];
                foreach($result as $row) {
                    foreach($row as $excel_email) {
                        $excel_email = trim($excel_email);
                        if($excel_email) {
                            $uniqueEmails[] = $excel_email;
                        }
                    }
                }
                $uniqueEmails = array_unique($uniqueEmails);
                foreach ($uniqueEmails as $key => $value) {
                    $promotionEmails[] = ['email' => $value, 'promotion_id' => $promotion->id];
                }
                
                PromotionUser::insert($promotionEmails);
            }

        }
        
        //Add custom coupon code
        if($request->couponscode!='') {
            $request->couponscode=strtoupper($request->couponscode);
            $dacouponscode = PromoCode::where('promocode', $request->couponscode)->first();

            if ($dacouponscode) {
                throw new ApiGenericException('This promocode already exist in record.');
            }
            $promotion->couponscode=$request->couponscode;
        }
        else {
            $promotion->couponscode="";
        }

        return PromoCodeLib::generatePromoCodes($promotion);
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function showOld($id)
    {
        if (!$id) {
            throw new ApiGenericException('Please Provide Valid Promo Type Id');
        }

        $promotype = Promotion::where(['id' => $id, 'status' => 1])->first();

        if (!$promotype) {
            throw new ApiGenericException('Couldn\'t Find Any Promo Type With Given Id');
        }

        return [
            'success' => true,
            'promoType' => $promotype
        ];
    }

    public function show($id)
    {
        if (!$id) {
            throw new ApiGenericException('Please Provide Valid Promotion Id');
        }

        $promotion = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers')->where(['id' => $id])->first();
        if($promotion->total_coupons <=1){
            $promotion->is_custom = 1;
        }else{
            $promotion->is_custom = 0;
        }
        $total_usage = 0;    
        $total_redeem_amount = 0;    
        if($promotion->promoCode){
            foreach ($promotion->promoCode as $key => $value) {
                $promocode = PromoUsage::where('promocode', $value->promocode)->get();
                if($promocode){
                    $value->usage = count($promocode);
                $total_usage += count($promocode);
                foreach ($promocode as $k => $v) {
                        $total_redeem_amount += $v->discount_amount;
                        $promotion->promocode_usage_users = $promocode;
                }    
                }
                
            }
            
        }
        $promotion->total_usage = $total_usage;
        $promotion->total_redeem_amount = $total_redeem_amount;

        if (!$promotion) {
            throw new ApiGenericException('Couldn\'t Find Any Promotion With Given Id');
        }

        return [
            'success' => true,
            'Promotion' => $promotion
        ];
    }
    
    /**
     * 
     * @param type validateUserPromo
     * @ success redirect on website or fail
     * @throws ApiGenericException
     */
    public function validateUserPromo($activaionCode=NULL)
    {
        $message = "";
        if (!$activaionCode) {
            $message = "Link is not valid or has been expired.";
            return view('email-promocode-activated', array("is_error"=>'1','message'=>$message));  
        }

        $promotionData = PromotionVerification::where(['activation_code' => $activaionCode,'status' => '0'])->first();

        if (!$promotionData) {
            $message = "Link is not valid or has been expired.";
            return view('email-promocode-activated', array("is_error"=>'1','message'=>$message)); 
      
        }
        
        $message = "Your email has been validated to use promocode: ".$promotionData->promocode;
        $promotionData->status = 1;
        $promotionData->save();
        return view('email-promocode-activated', array("is_error"=>'0','message'=>$message));      
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int                      $id
     * @return \Illuminate\Http\Response
     */
    public function updateOld(Request $request, $id)
    {
        $this->validate($request, Promotion::$updateValidationRules);

        $promotype = Promotion::where('id', $request->id)->first();

        if (!$promotype) {
            throw new ApiGenericException('Couldn\'t Find Any Promo Type With Given Id');
        }

        $promotype->fill($request->only(['name', 'description']));
        $result = $promotype->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Promo Type Could Not Be Updated');
        }

        return [
            'success' => true,
            'promoType' => $promotype
        ];
    }

    public function update(Request $request)
    {
        
        $this->validate($request, Promotion::$excelValidationRules);
        
        if ($request->valid_from > $request->valid_to) {
            throw new ApiGenericException('Error Occured, Invalid Validity Date Provided');
        }
        
        if($request->specific_user_type == '1'){
            if ($request->percentage_off_discount > $request->max_lifetime_discount || $request->discount_value > $request->max_lifetime_discount) {
                throw new ApiGenericException('Max Lifetime value should be greater discount amount or discount value');
            }

            if(!$request->PromotionCodeEmail && !$request->PromotionCodeEmailSheet){
                throw new ApiGenericException('Users email or excel file is required.');
            }

            if($request->PromotionCodeEmailSheet){
                
                $destination_path = public_path().'/assets/promotion-emails-spreadsheet/';
                $file = $request->file('PromotionCodeEmailSheet');
                $file_extension = $file->getClientOriginalExtension();
                
                /* File validation: */
                $validation = Validator::make(
                    ['file' => $file, 'extension' => $file_extension],
                    ['file' => 'required', 'extension' => 'required|in:xlsx,xls']
                );
                if($validation->fails()) {
                    throw new ApiGenericException("Please upload valid spreadsheet");
                }                
                
            }
        }
        
        $promotion = Promotion::where('id', $request->id)->first();   
      
        if (!$promotion) {
            throw new ApiGenericException('Couldn\'t Find Any Promotion With Given Id');
        }

        $promotion->fill($request->only(['status', 'valid_from','valid_to','description','discount_type','discount_value','usage', 'percentage_off_discount', 'dollar_off_discount','max_lifetime_discount','is_tax_applicable']));
        $result= $promotion->save();
    
        try {

            if($request->specific_user_type == '1'){
            $promotion->specific_user_type = 1;
            $promotion->save();
            //PromotionUser::where('promotion_id', $promotion->id)->delete();
            if($request->PromotionCodeEmail){
                $emails = explode(",", $request->PromotionCodeEmail);
                $emails = array_unique($emails);
                $promotionEmails = [];
                foreach ($emails as $key => $value) {
                    if(trim($value) != ''){
                        $alreadyExist = PromotionUser::where('promotion_id', $promotion->id)->where('email', trim($value))->first();
                        if(!$alreadyExist){
                            $promotionEmails[$key]['email'] = trim($value);
                            $promotionEmails[$key]['promotion_id'] = $promotion->id;
                        }
                    }                    
                }
                if(count($promotionEmails) > 0){
                    PromotionUser::insert($promotionEmails);
                }
            }

            if($request->PromotionCodeEmailSheet){
                $destination_path = public_path().'/assets/promotion-emails-spreadsheet/';
                $file = $request->file('PromotionCodeEmailSheet');
                $file_extension = $file->getClientOriginalExtension();
                
                /* File validation: */
                $validation = Validator::make(
                    ['file' => $file, 'extension' => $file_extension],
                    ['file' => 'required', 'extension' => 'required|in:xlsx,xls']
                );
                if($validation->fails()) {
                    throw new ApiGenericException("Please upload valid spreadsheet");
                }
                
                $file_name = 'promotion-emails.' . $file_extension;
                if(!$file->move($destination_path, $file_name)) {
                    throw new ApiGenericException("Something went wrong while importing users sheet");
                }
                
                $uploaded_file = $destination_path . $file_name;
                
                $result = Excel::load($uploaded_file, 
                function($reader) {})->get()->toArray();

                if(empty($result)) {
                    throw new ApiGenericException("No data found in the spreadsheet.");
                }
                
                $promotionEmails = [];
                $uniqueEmails = [];
                foreach($result as $row) {
                    foreach($row as $excel_email) {
                        $excel_email = trim($excel_email);
                        if($excel_email) {
                            $alreadyExist = PromotionUser::where('promotion_id', $promotion->id)->where('email', $excel_email)->first();
                            if(!$alreadyExist){
                                $uniqueEmails[] = $excel_email;
                            }
                        }
                    }
                }
                $uniqueEmails = array_unique($uniqueEmails);
                foreach ($uniqueEmails as $key => $value) {
                    $promotionEmails[] = ['email' => $value, 'promotion_id' => $promotion->id];
                }
                
                PromotionUser::insert($promotionEmails);
            }

        }else{
            $promotion->specific_user_type = 0;
            $promotion->save();
            PromotionUser::where('promotion_id', $promotion->id)->delete();
        }

            if((!isset($request->discount_value)) || ($request->discount_value ==''))
            {
                    $request->discount_value = 0;
            }
            DB::table('promo_codes')->where('promotion_id', $request->id)
               ->update(['usage' => $request->usage,'valid_from' => $request->valid_from,'valid_to' => $request->valid_to,'discount_type' => $request->discount_type,'discount_value' => $request->discount_value,'usage' => $request->usage,'max_lifetime_discount' => $request->max_lifetime_discount]);
         
        } catch (Exception $e) {
          
        }
       
        //die();
        if (!$result) {
            throw new ApiGenericException('Error Occured, Promotion Could Not Be Updated');
        }

        return [
            'success' => true,
            'Promotion' => $promotion
        ];
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id, Request $request)
    {
        if (!$request->id) {
            throw new ApiGenericException('Please Provide Valid Promo Type Id');
        }

        $promotype = Promotion::where('id', $request->id)->first();

        if (!$promotype) {
            throw new ApiGenericException('Couldn\'t Find Any Promo Type With Given Id');
        }

        $result = $promotype->delete();
        PromoCode::where('promotion_id', $request->id)->delete();
        PromotionUser::where('promotion_id', $request->id)->delete();   
        /*if ($request->forcedelete) {
            $result = $promotype->forceDelete();
        } else {
            $result = $promotype->delete();
            PromoCode::where('promotion_id', $request->id)->delete();
            PromotionUser::where('promotion_id', $request->id)->delete();
        }*/
        

        if (!$result) {
            throw new ApiGenericException('Error Occured, Promo Type Could Not Be Deleted');
        }

        return [
            'success' => true,
        ];
    }

    public function downloadExcel($promotionId)
    {
        // $this->validate($request, Promotion::$excelValidationRules);

        $promotion = Promotion::with('promotionUsers')->where('id', $promotionId)->first();

        if (!$promotion) {
            throw new ApiGenericException('Error Occured, Could Not Find Promo Codes For This Promotion');
        }

        $promotionEmails = '';    
        if(count($promotion->promotionUsers) > 0){
            foreach ($promotion->promotionUsers as $key => $value) {
                $promotionEmails .= $value->email.', ';   
            }
            $promotionEmails = rtrim($promotionEmails, ", ");
        }

        $promoCodes = PromoCode::select(
            'promocode',
            'discount_type',
            'discount_value',
            'valid_from',
            'valid_to',
            'created_at'
        )->where('promotion_id', $promotionId)->get();

        $finalCodes = [];
        $increment = 1;
        foreach ($promoCodes as $code) {
            $finalCodes[] = [
                'No.' => $increment,
                'Promotion' => $promotion->name,
                'PromoCode' => $code->promocode,
                'Promo Type' => ucfirst($code->discount_type) . ' Based',
                'Promo Value' => $code->discount_value,
                'Valid From' => $code->valid_from,
                'Valid To' => $code->valid_to,
                'Max Percentage off discount' => ($promotion->percentage_off_discount>0)?$promotion->percentage_off_discount:'-',
                'Minimum Transaction Amount($)' => ($promotion->dollar_off_discount>0)?$promotion->dollar_off_discount:'-',
                'Generated On' => $code->created_at,
                'Max Life Discount' => $promotion->max_lifetime_discount,
                'Specific Users' => $promotionEmails
            ];
            $increment++;
        }

        $excelSheetName = ucwords($promotion->name);

        Excel::create(
            $excelSheetName, function ($excel) use ($finalCodes, $excelSheetName) {

                // Set the spreadsheet title, creator, and description
                $excel->setTitle($excelSheetName);
                $excel->setCreator('IconGo')->setCompany('Outworx Solutions Pvt Ltd');
                $excel->setDescription('List Of Promo Codes');

                // Freeze first row
                // $excel->freezeFirstRow();

                // Build the spreadsheet, passing in the payments array
                $excel->sheet(
                    'Promo Codes', function ($sheet) use ($finalCodes) {
                        $sheet->fromArray($finalCodes, null, 'A1', false, true);
                    }
                );
            }
        )->store('xls')->download('xls');
    }

    public function checkPromo(Request $request)
    {
        $this->validate($request, Promotion::$checkPromoValidationRules);
        return PromoCodeLib::validatePromoCode($request);
    }

    public function checkPromoThirdParty(Request $request)
    {
        $this->validate($request, Promotion::$checkPromoValidationRulesThirdParty);
        return PromoCodeLib::validatePromoCodeThirdParty($request);
    }
    
    
    public function updatePromocodeUsage(Request $request)
    {
        $db_logs_data['request'] = json_encode($request->all());
        $db_logs_data['api_type'] = 'usage';
        $db_logs_data['created_at'] = Carbon::now();
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/partner_promocode_api_logs')->createLogger('usage_api_logs');
        
        try {
            $log->info("Request: " . $db_logs_data['request']);
            $this->validate($request, Promotion::$checkPromoUsageValidationRulesThirdParty);
            $promocodeData = PromoCodeLib::validatePromoCodeUsageThirdParty($request);
            try {
                $promoUsage = new PromoUsage();
                $promoUsage->user_id = $promocodeData['partner_id'];
                $promoUsage->promocode = $request->promocode;
                $promoUsage->email = $request->email;
                $promoUsage->reservation_id = $request->transaction_id;
                $promoUsage->discount_amount = $promocodeData['applicable_discount'];
                $promoUsage->save();
            } catch (Exception $e) {
                throw $e;
            }
            
            $response = [
                'success' => true,
                'message' => $promocodeData['message'],
                'amount' => $request->amount,
                'applicable_discount' => $promocodeData['applicable_discount']
            ];

            $db_logs_data['response'] = json_encode($response);
            $log->info("Response: " . $db_logs_data['response']);
            DB::table('partner_promocode_api_logs')->insert($db_logs_data);

            if($promocodeData['applicable_discount'] < $request->amount) {
                return response()->json($response, 202);
            }
            return response()->json($response, 200);
        }
        catch(Exception $e) {
            $log->error("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
            $db_logs_data['is_error'] = 1;
            $db_logs_data['error_message'] = $e->getMessage();
            DB::table('partner_promocode_api_logs')->insert($db_logs_data);
            throw $e;
        }
        
    }
    

     public function prepareArrayData(Array $data) {
        $tempData = [];
        for ($i = 0; $i < count($data); $i++) { 
            $particular = (array) $data[$i];
            $tempData[$particular['id']] = $particular;
        }

        return $tempData;
    }

    public function getTotalUsage($promotions, $request = ''){
        foreach ($promotions as $key => $promotion) {
            if($promotion->total_coupons <=1){
                $promotion->is_custom = 1;
            }else{
                $promotion->is_custom = 0;
            }
            if($promotion->is_custom == 1){
                $total_usage = 0;
                $total_redeem_amount = 0;
                    if($promotion->promoCode){
                        foreach ($promotion->promoCode as $key => $value) {
                            $promocode = PromoUsage::where('promocode', $value->promocode)->get();
                            $value->usage = count($promocode);
                            $total_usage += count($promocode);
                            foreach ($promocode as $k => $v) {
                                $total_redeem_amount += $v->discount_amount;
                            }
                            $promotion->promocode_usage_users = $promocode;
                        }
                    }
                $promotion->total_usage = $total_usage;
                $promotion->total_redeem_amount = $total_redeem_amount;
            }else{
                $total_usage = 0;
                $total_used_coupon_count = 0;
                $total_redeem_amount = 0;
                $total_promocode = 0;
                    if($promotion->promoCode){
                           
                        foreach ($promotion->promoCode as $key => $value) {
                            $promocode = PromoUsage::where('promocode', $value->promocode)->get();
                            if($promocode){
                                $value->usage = count($promocode);
                                $total_usage += count($promocode);
                                foreach ($promocode as $k => $v) {
                                    if($k == 0){
                                        $total_used_coupon_count += 1;
                                    }
                                    $total_redeem_amount += $v->discount_amount;
                                }
                                $promotion->promocode_usage_users = $promocode;
                            }
                        }
                    $total_promocode = count($promotion->promoCode);    
                    }
                $promotion->total_used_coupon_count = $total_used_coupon_count;
                $promotion->total_usage = $total_usage;
                $promotion->total_redeem_amount = $total_redeem_amount;
                $promotion->total_promocode = $total_promocode;
            }
        }
        if($request != ''){
            if($request->sortBy == 'asc' || $request->sortBy == 'Asc'){
              for ($i = 0; $i < count($promotions); $i++) {
              for ($j = $i + 1; $j < count($promotions); $j++) {
                  if ($promotions[$i]->total_usage > $promotions[$j]->total_usage) {
                      $temp = $promotions[$i];
                      $promotions[$i] = $promotions[$j];
                      $promotions[$j] = $temp;
                  }
              }
            }
            }else{
              for ($i = 0; $i < count($promotions); $i++) {
              for ($j = $i + 1; $j < count($promotions); $j++) {
                  if ($promotions[$i]->total_usage < $promotions[$j]->total_usage) {
                      $temp = $promotions[$i];
                      $promotions[$i] = $promotions[$j];
                      $promotions[$j] = $temp;
                  }
              }
            }
            }
            
        }
        return $promotions;
    }

    public function getTotalPromocodeUsage($promotions){
        if($promotions){
            foreach ($promotions as $key => $value) {
                $total_usage = 0;
                $total_redeem_amount = 0;
                $promocode = PromoUsage::where('promocode', $value->promocode)->get();
                $total_usage += count($promocode);
                foreach ($promocode as $k => $v) {
                    $total_redeem_amount += $v->discount_amount;
                }
                $value->promocode_usage_users = $promocode;
                $value->total_usage = $total_usage;
                $value->total_redeem_amount = $total_redeem_amount;
            }

            return $promotions;        
        }        
    }

    public function getAll(Request $request)
    {  
        $owner_id = 0;
        $userData = User::where('id',Auth::user()->id)->first();
        if($userData->user_type =='1' || $userData->user_type=='2')
        {
            $promotions = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers');     
            if ($request->search) {
                 if($request->search == 'NO'){
                        $promotions =  Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers')->where('owner_id', $request->owner_id);
                        if($request->sort != ''){
                            $promotions = $promotions->orderBy($request->sort,$request->sortBy)->paginate(20);
                        }else{
                            $promotions = $promotions->orderBy('id','DESC')->paginate(20);
                        }
                        return $this->getTotalUsage($promotions);
                 }else{
                    $promotions = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers');     
                    if ($request->owner_id !='') {
                             $promotions->where('owner_id', $request->owner_id);    
                    }
                    $promotions->where('name', 'LIKE', '%' . $request->search . '%');
                    if($request->sort != ''){
                        $promotions = $promotions->orderBy($request->sort,$request->sortBy)->paginate(20);
                    }else{
                        $promotions = $promotions->orderBy('id','DESC')->paginate(20);
                    }
                    return $this->getTotalUsage($promotions);
                 }
                
          }
          if($request->sort != ''){
                $promotions = $promotions->orderBy($request->sort,$request->sortBy)->paginate(20);
            }else{
                $promotions = $promotions->orderBy('id','DESC')->paginate(20);
            }
          return $this->getTotalUsage($promotions);

        }else{
            if($userData->user_type =='3')
            {
                $owner_id = $userData->id;
                
            }else if($userData->user_type =='4')
            {
                $owner_id = $userData->created_by;
            }
            $promotions = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers')->where('owner_id', $owner_id);     
        
        
        if ($request->search) {
                
             $promotions = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers');            
             $promotions = QueryBuilder::buildSearchQueryForPartner($promotions, $request->search, Promotion::$searchFields);
                $promotions->where(function($query) use($owner_id) {
                    $query->where('owner_id', $owner_id);
                });
            if($request->sort != ''){
                $promotions = $promotions->orderBy($request->sort,$request->sortBy)->paginate(20);
            }else{
                $promotions = $promotions->orderBy('id','DESC')->paginate(20);
            }
            return $this->getTotalUsage($promotions);
            
            }
        }
        if($request->sort != ''){
            $promotions = $promotions->orderBy($request->sort,$request->sortBy)->paginate(20);
        }else{
            $promotions = $promotions->orderBy('id','DESC')->paginate(20);
        }
        return $this->getTotalUsage($promotions);
    }

    public function getAllPromocodes(Request $request)
    {  
        $owner_id = 0;
        $userData = User::where('id',Auth::user()->id)->first();
        if($userData->user_type =='1' || $userData->user_type=='2')
        {
            $promotions = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers')->where('status', $request->status);     
            if ($request->search) {
                 if($request->search == 'NO'){
                        $promotions =  Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers')->where('status', $request->status);
                        if($request->owner_id != ''){
                            $promotions->where('owner_id', $request->owner_id);
                        }
                    $promotions = $promotions->orderBy('id','DESC')->paginate(20);
                    return $this->getTotalUsage($promotions, $request);
                 }else{
                    $promotions = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers');     
                    if ($request->owner_id !='') {
                        $promotions->where('owner_id', $request->owner_id);    
                    }
                    $promotions->where('name', 'LIKE', '%' . $request->search . '%')->where('status', $request->status);                    
                    $promotions = $promotions->orderBy('id','DESC')->paginate(20);                
                    return $this->getTotalUsage($promotions, $request);
                 }
                
          }
            $promotions = $promotions->orderBy('id','DESC')->paginate(20);
          return $this->getTotalUsage($promotions, $request);

        }else{
            if($userData->user_type =='3')
            {
                $owner_id = $userData->id;
                
            }else if($userData->user_type =='4')
            {
                $owner_id = $userData->created_by;
            }
            $promotions = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers')->where('owner_id', $owner_id)->where('status', $request->status);     
        
        
        if ($request->search) {
            
            if($request->search == 'NO'){
                        $promotions = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers')->where('owner_id', $owner_id)->where('status', $request->status);
                        $promotions = $promotions->orderBy('id','DESC')->paginate(20);
                        return $this->getTotalUsage($promotions, $request);
            }else{
                $promotions = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers');
                $promotions = QueryBuilder::buildSearchQueryForPartner($promotions, $request->search, Promotion::$searchFields);
                $promotions->where(function($query) use($owner_id, $request) {
                    $query->where('owner_id', $owner_id);
                    $query->where('status', $request->status);
                });
                $promotions = $promotions->orderBy('id','DESC')->paginate(20);
                return $this->getTotalUsage($promotions, $request);
            }
            
            }
        }
        $promotions = $promotions->orderBy('id','DESC')->paginate(20);
        return $this->getTotalUsage($promotions, $request);
    }


    public function getPromocodeByName($name)
    {
        if (!$name) {
            throw new ApiGenericException('Please Provide Valid Promocode');
        }

        $promotion = PromoCode::with('promotype','channelpartner','promotion')->where(['promocode' => $name])->first();
        
        if($promotion){
            //foreach ($promotion->promoCode as $key => $value) {
            $total_usage = 0;    
            $total_redeem_amount = 0;    
            $promocode = PromoUsage::where('promocode', $promotion->promocode)->get();
            $total_usage += count($promocode);
            foreach ($promocode as $k => $v) {
                    $total_redeem_amount += $v->discount_amount;
            }
            //}
            $promotion->promocode_usage_users = $promocode;
            $promotion->total_usage = $total_usage;
            $promotion->total_redeem_amount = $total_redeem_amount;
        }        

        if (!$promotion) {
            throw new ApiGenericException('Couldn\'t Find Any Promotion With Given Id');
        }

        return [
            'success' => true,
            'Promocode' => $promotion
        ];
    }

    public function getPromocodeByPromotion($id, Request $request)
    {
        if (!$id) {
            throw new ApiGenericException('Please Provide Valid Promotion Id');
        }

        $promotion = Promotion::with('promotype','channelpartner','users','promoCode','promotionUsers')->where(['id' => $id])->first();
        if($promotion->total_coupons <=1){
            $promotion->is_custom = 1;
        }else{
            $promotion->is_custom = 0;
        }
        $total_usage = 0;    
        $total_redeem_amount = 0;
        if($promotion->promoCode){
            $all_promocode_usage = [];
            $allPromocodes = [];
            foreach ($promotion->promoCode as $key => $value) {
                $allPromocodes[] = $value->promocode;
                $promocode = PromoUsage::where('promocode', $value->promocode)->get();        
                if($promocode){
                    array_push($all_promocode_usage, $promocode);
                    $value->usage = count($promocode);
                $total_usage += count($promocode);
                foreach ($promocode as $k => $v) {
                        $total_redeem_amount += $v->discount_amount;
                        $promotion->promocode_usage_users = $promocode;
                }    
                }
                
            }
            $response = PromoUsage::whereIn('promocode', $allPromocodes)->get();
            $promotion->promocode_usage_users = $response;        
        }
        $promotion->total_usage = $total_usage;
        $promotion->total_redeem_amount = $total_redeem_amount;

        if (!$promotion) {
            throw new ApiGenericException('Couldn\'t Find Any Promotion With Given Id');
        }

        return [
            'success' => true,
            'Promotion' => $promotion
        ];
    }


    public function getPromocodeUsers(Request $request)
    {
        $promocodes = PromoCode::where('promotion_id', $request->promotion_id)->get();

        $allPromocodes = [];
        foreach ($promocodes as $key => $value) {
            $allPromocodes[] = $value->promocode;
        }

        if($request->search !='NO'){            
            $response = PromoUsage::whereIn('promocode', $allPromocodes)->where('email', 'like', '%'.$request->search.'%')->paginate(20);
        }else{
            $response = PromoUsage::whereIn('promocode', $allPromocodes)->paginate(20);;
        }

        return $response;

    }

    
    /*public function getAllPromocodes(Request $request)
    {  
        $owner_id = 0;
        $userData = User::where('id',Auth::user()->id)->first();
        if($userData->user_type =='1' || $userData->user_type=='2')
        {
            $promotions = PromoCode::with('promotype','channelpartner','promotion');     
            if ($request->search) {
                 if($request->search == 'NO'){
                    $promotions = PromoCode::with(['promotype','channelpartner','promotion' => function($query) use($request){
                        $query->select('promotions.*');
                        if($request->owner_id != ''){
                        $query->where('promotions.owner_id', $request->owner_id);
                        }
                    }])->where('status', $request->status)->orderBy('id','DESC')->paginate(20);     
                        return $this->getTotalPromocodeUsage($promotions);
                 }else{
                    $promotions = PromoCode::with(['promotype','channelpartner','promotion' => function($query) use($request){
                        $query->select('promotions.*');
                        if($request->owner_id != ''){
                        $query->where('promotions.owner_id', $request->owner_id);
                        }
                    }])->where('status', $request->status);     
                    
                        $promotions = QueryBuilder::buildSearchQuery($promotions, $request->search, PromoCode::$searchFields)->orWhereHas(
                    'promotion', function ($query) use ($request) {
                        $query
                            ->where('owner_id', '=', "{$request->owner_id}");
                    }
                );
                   $promotions = $promotions->orderBy('id','DESC')->paginate(20);    
                    return $this->getTotalPromocodeUsage($promotions);
                 }
                
          }
          $promotions  = $promotions->orderBy('id','DESC')->paginate(20);          
          return $this->getTotalPromocodeUsage($promotions);

        }else{
            if($userData->user_type =='3')
            {
                $owner_id = $userData->id;
                
            }else if($userData->user_type =='4')
            {
                $owner_id = $userData->created_by;
            }
            $partnerPromo = Promotion::select('id')->where('owner_id', $owner_id)->get();
            $promotions = PromoCode::with('promotype','channelpartner','promotion')->whereIn('promotion_id', $partnerPromo);     

            if ($request->search) {
                
             $promotions = PromoCode::with('promotype','channelpartner','promotion')->whereIn('promotion_id', $partnerPromo);     
             $promotions = QueryBuilder::buildSearchQuery($promotions, $request->search, PromoCode::$searchFields);
            $promotions = $promotions->orderBy('id','DESC')->paginate(20);
            return $this->getTotalPromocodeUsage($promotions);
            
            }
        }
        $promotions = $promotions->orderBy('id','DESC')->paginate(20);
        return $this->getTotalPromocodeUsage($promotions);
    }*/

    /*public function getAll()
    {  
            $promotions =DB::select(
                   "select id,name,total_coupons,status,valid_from,valid_to,promo_type_id,percentage_off_discount, dollar_off_discount,
                (CASE 
                WHEN promo_type_id = 2  
                THEN (select count(distinct(PC.promocode)) as promo_exposed from promo_codes 
                as PC where PC.is_expired = 1 and PC.promotion_id = promotions.id) 
                WHEN promo_type_id = 1 
                THEN (select count(distinct(PU.user_id)) as promo_exposed from promo_codes 
                as PC, promo_usages as PU where PC.promocode = PU.promocode and PC.promotion_id = promotions.id)              
                ELSE 0 
                END)as consume_promo,
               (CASE 
                WHEN promo_type_id = 2  
                THEN 0 
                WHEN promo_type_id = 1 
                THEN (select promocode as promocode from promo_codes 
                as PC where PC.promotion_id = promotions.id)              
                ELSE 0 
                END)as promo_code

                 from promotions"
               );

             $newPromotions = $this->prepareArrayData($promotions);
             $savepromotions =DB::select(
                   "select count(*) as 'Saved_Promocodes', p.name as 'Promotion' , p.id as 'id' from 
                    users_promo_codes as upc, promo_codes as pc, 
                    promotions as p  where 
                    upc.promo_code_id = pc.id and 
                    pc.promotion_id = p.id
                    group by p.id"
               );
        $newSavePromotions = $this->prepareArrayData($savepromotions);
         
        foreach ($newPromotions as $id => $value) {
            if (isset($newSavePromotions[$id])) {
                $newPromotions[$id]['Saved_Promocodes'] = $newSavePromotions[$id]['Saved_Promocodes'];
            } else {
                $newPromotions[$id]['Saved_Promocodes'] = 0;
            }
        }
    

          return ['promotions' => $newPromotions];
    }*/
}