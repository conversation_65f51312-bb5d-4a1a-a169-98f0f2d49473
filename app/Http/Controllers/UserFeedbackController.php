<?php
namespace App\Http\Controllers;
use App\Models\UserFeedback;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests;
use App\Exceptions\UserNotFoundException;
use App\Exceptions\UserNotAuthorized;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserWithEmailExistsException;
use App\Exceptions\NotFoundException;
use DB;
class UserFeedbackController extends Controller
{

    // Returning all the user feedback list here
    public function getUserFeedbackListing()
    {
        
        return DB::table('user_feedback')
       ->select('name as user_name', 'full_name as facility_name','short_name','email as user_email','user_feedback.*')         
        ->join('users', 'user_feedback.user_id', '=', 'users.id')
        ->join('facilities', 'user_feedback.facility_id', '=', 'facilities.id')
        ->orderBy('user_feedback.id', 'desc')
        ->get();
       
    }
   

}
