<?php

namespace App\Http\Controllers;

class ReleaseController extends Controller
{
    public function full($site)
    {
        \Artisan::call('releases:full', ['site' => $site]);
        return "<pre>" . \Artisan::output();
    }

    public function pending($site = null)
    {
        $output = "<pre>";
        if ($site == null) {
            $output .= $this->call("web");
            $output .= $this->call("api");
        } else {
            $output .= $this->call($site);
        }
        return $output;
    }

    public function release($site, $releaseNumber)
    {
        \Artisan::call('releases:generate', ['site' => $site, 'releaseNumber' => $releaseNumber]);
        return "<pre>" . \Artisan::output();
    }

    private function call($site)
    {
        \Artisan::call('releases:generate', ['site' => $site]);
        return \Artisan::output();
    }
}
