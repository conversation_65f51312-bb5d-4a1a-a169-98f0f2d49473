<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use App\Exceptions\NotFoundException;
use App\Models\Event;
use App\Models\GeoLocation;
use App\Models\Facility;
use App\Models\ParkEngage\EventCategoryEvent;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserNotAuthorized;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\MapcoQrcode;
use File;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\User;
use App\Models\EventUser;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserFacility;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\Reservation;

class EventController extends Controller
{

    protected $log;
    protected $request;

    const SUPERADMIN = 1;
    const SUBORDINATE = 4;
    const PARTNER = 3;
    const REGIONAL_MANAGER = 12;
    const CASHIER = 11;
    const ATTENDANT = 9;
    const BUSINESS_USERTYPE = 10;
    const BUSINESS_CLERK = 8;

    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->request = $request;
        $this->log = $logFactory->setPath('logs/parkengage/event')->createLogger('event');
    }

    /**
     * Get a paginated list of all users
     *
     * @return [type] [description]
     */
    public function index(Request $request)
    {
        if (!Auth::user()) {
            throw new UserNotAuthorized("Invalid User!");
        }
        if (Auth::user()->status == '0') {
            throw new UserNotAuthorized("User is inactive. Please contact to admin.");
        }
        $facility = [];
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
            $events = Event::with('eventCategoryEvent.eventCategory', 'eventFacility')->whereNull('deleted_at');
            if ($partner_id) {
                $events = $events->where('partner_id', $partner_id);
            }
            if ($rm_id) {
                $events = $events->where('rm_id', $rm_id);
            }
            $facility[] = $request->facility_id;
            if (isset($facility) && !empty($facility)) {
                $events = $events->WhereHas(
                    'eventFacility',
                    function ($query) use ($facility) {
                        $query->whereIN("facility_id", $facility);
                    }
                );
            }
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
            // Alka
            $facility = [];
            $userFacilities = UserFacility::where('user_id', Auth::user()->id)->get();

            if (!empty($userFacilities)) {
                foreach ($userFacilities as $userFacility) {
                    $facility[] = $userFacility->facility_id;
                }
            }
            // End
            $events = Event::with('eventCategoryEvent.eventCategory', 'eventFacility')->where('partner_id', $partner_id)->whereNull('deleted_at');
            if ($rm_id) {
                $events = $events->where('rm_id', $rm_id);
            }

            // ALka
            if (isset($facility) && !empty($facility)) {
                $events = $events->WhereHas(
                    'eventFacility',
                    function ($query) use ($facility) {
                        $query->whereIN("facility_id", $facility);
                    }
                );
            }
            // end
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
            $events = Event::with('eventCategoryEvent.eventCategory', 'eventFacility')->where('partner_id', $partner_id)->whereNull('deleted_at');
            if ($rm_id) {
                if ($partner_id == config('parkengage.PARTNER_USM')) {
                } else {
                    $events = $events->where('rm_id', $rm_id);
                }
            }
            $facility[] = $request->facility_id;
            if (isset($facility) && !empty($facility)) {
                $events = $events->WhereHas(
                    'eventFacility',
                    function ($query) use ($facility) {
                        $query->whereIN("facility_id", $facility);
                    }
                );
            }
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
            $events = Event::with('eventCategoryEvent.eventCategory', 'eventFacility')->where('partner_id', $partner_id)->whereNull('deleted_at');
            if ($rm_id) {
                $events = $events->where('rm_id', $rm_id);
            }
            $facility[] = $request->facility_id;
            if (isset($facility) && !empty($facility)) {
                $events = $events->WhereHas(
                    'eventFacility',
                    function ($query) use ($facility) {
                        $query->whereIN("facility_id", $facility);
                    }
                );
            }
        } else if (Auth::user()->user_type == self::BUSINESS_USERTYPE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $events = Event::with('eventCategoryEvent.eventCategory', 'eventFacility')->where('partner_id', $partner_id)->whereNull('deleted_at');
            if ($rm_id) {
                $events = $events->where('rm_id', $rm_id);
            }
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $events = Event::with('eventCategoryEvent.eventCategory', 'eventFacility')->where('partner_id', $partner_id)->whereNull('deleted_at');
            if ($rm_id) {
                $events = $events->where('rm_id', $rm_id);
            }
        } else {
            throw new UserNotAuthorized("Invalid User!");
        }

        if ($request->search != '') {

            $events = $events->where('title', 'like', "%{$request->search}%")->orwhere('slug', 'like', "%{$request->search}%");

            if ($partner_id) {
                $events = $events->where('partner_id', $partner_id)->orWhereHas(
                    'eventCategoryEvent.eventCategory',
                    function ($query) use ($request) {
                        $query->where('name', 'like', "%{$request->search}%");
                        $query->orWhere('title', 'like', "%{$request->search}%");
                    }
                );
            } else {
                $events = $events->orWhereHas(
                    'eventCategoryEvent.eventCategory',
                    function ($query) use ($request) {
                        $query->where('name', 'like', "%{$request->search}%");
                        $query->orWhere('title', 'like', "%{$request->search}%");
                    }
                );
            }
        }

        if ($request->status != null) {
            $events = $events->where('is_active', $request->status);
        }

        if ($request->event_category_id) {
            $events = $events->WhereHas(
                'eventCategoryEvent',
                function ($query) use ($request) {
                    $query->where('event_category_id', $request->event_category_id);
                }
            );
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date('Y-m-d', strtotime($request->from_date));
            $to_date = date('Y-m-d', strtotime($request->to_date));

            $events = $events->whereDate('start_time', '>=', $from_date)
                ->whereDate('end_time', '<=', $to_date);
        }


        /* $events = $events->whereDate('start_time', '>=', $from_date)
                        ->whereDate('end_time', '<=', $to_date); */
        //return $events->toSql();

        if ($request->sort != '' && $request->sort == "name") {
            $events = $events->WhereHas(
                'eventCategoryEvent.eventCategory',
                function ($query) use ($request) {
                    $query->orderBy("name", $request->sortBy);
                }
            );
            return $events->paginate(10);
        } else if ($request->sort != '') {
            return $events->orderBy($request->sort, $request->sortBy)->paginate(10);;
        } else {
            return $events->orderBy('created_at', 'desc')->paginate(10);;
        }
        return $events;
    }

    public function store(Request $request)
    {
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_USERTYPE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else {
            throw new UserNotAuthorized("Invalid User!");
        }

        $this->log->info("Add event request received : " . json_encode($request->all()) . " Loggedin user id : " . $created_by);
        $this->validate($request, Event::$validParams);
        $request->request->add(['partner_id' => $partner_id]);
        $request->request->add(['rm_id' => $rm_id]);
        $request->request->add(['created_by' => $created_by]);

        $exist = Event::where('slug', $request->slug)->first();
        if ($exist) {
            $request->request->add(['slug' => $request->slug . "-" . rand(100000, 99999)]);
        }
        $event = Event::create($request->all());

        if ($request->event_category_id != '') {
            $data['event_id'] = $event->id;
            $data['event_category_id'] = $request->event_category_id;
            EventCategoryEvent::create($data);
        }

        $geolocation = new GeoLocation();
        $geolocation->fill($request->geolocations);
        $event->geolocations()->save($geolocation);

        $facilitiesArray = [];
        foreach ($request->facilities as $facility) {
            array_push($facilitiesArray, $facility['id']);
        }
        $event->facilities()->sync($facilitiesArray);
        $event->save();
        $this->log->info("Event stored : " . json_encode($event) . " Loggedin user id : " . $partner_id);
        return $event->load('geolocations', 'facilities');
    }

    public function update(Request $request, Event $event)
    {
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_USERTYPE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else {
            throw new UserNotAuthorized("Invalid User!");
        }

        $this->validate($request, Event::$validParams);

        $this->log->info("Update event request received : " . json_encode($request->all()) . " Loggedin user id : " . $created_by);
        $event->fill($request->all());


        if (!$event->geolocations) {
            $geolocation = new GeoLocation();
            $geolocation->fill($request->geolocations);
            $event->geolocations()->save($geolocation);
        } else {
            $event->geolocations->fill($request->geolocations)->save();
        }
        $event->fill($request->all())->save();
        /*$exist = Event::where('id', '!=' ,$event->id)->where('title', $request->title)->first();
        if($exist){
            throw new ApiGenericException('Event already exist.');
        }*/
        $eventCategory = EventCategoryEvent::where('event_id', $event->id)->first();
        if ($eventCategory) {
            $eventCategory->event_category_id = $request->event_category_id;
            $eventCategory->save();
        } else {
            if ($request->event_category_id != '') {
                $eventCategory = new EventCategoryEvent();
                $eventCategory->event_id = $event->id;
                $eventCategory->event_category_id = $request->event_category_id;
                $eventCategory->save();
            }
        }
        $facilitiesArray = [];
        foreach ($request->facilities as $facility) {
            array_push($facilitiesArray, $facility['id']);
        }
        $event->facilities()->sync($facilitiesArray);
        $this->log->info("Update event successfully : " . json_encode($event) . " Loggedin user id : " . Auth::user()->id);
        return $event->load('geolocations', 'facilities');
    }

    public function show(Event $event)
    {
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }
        if (Auth::user()->user_type == '1') {
            $event = $event->load('geolocations', 'facilities', 'eventCategoryEvent.eventCategory');
        } else {
            $event = $event->load('geolocations', 'facilities', 'eventCategoryEvent.eventCategory');
        }
        //->load('geolocations', 'facilities', 'eventCategoryEvent.eventCategory')

        if (isset($event->eventCategoryEvent) && !empty($event->eventCategoryEvent)) {
            $event['event_category_id'] = $event->eventCategoryEvent->event_category_id;
        } else {
            $event['event_category_id'] = '';
        }
        $event['is_event_booked'] = false;
        $eventBooked = MapcoQrcode::where("event_id", $event->id)->first();
        if ($eventBooked) {
            $event['is_event_booked'] = true;
        }
        return $event;
    }

    public function eventBySlug($slug)
    {
        $event = Event::where('slug', $slug)->first();
        if (!$event) {
            throw new NotFoundException('Event not found for slug.');
        }
        return $event->load('geolocations', 'facilities');
    }

    public function destroy(Event $event)
    {
        $eventDate = date("Y-m-d", strtotime($event->start_time));
        $today = date("Y-m-d");
        // if(strtotime($eventDate) >= strtotime($today)){
        //     $eventBooked = MapcoQrcode::where("event_id", $event->id)->first();
        //     if($eventBooked){
        //         throw new NotFoundException('Event already booked. You can not delete this event.');
        //     }
        // }
        $eventBooked = MapcoQrcode::where("event_id", $event->id)->first();
        if ($eventBooked) {
            throw new NotFoundException('Event already booked. You can not delete this event.');
        }
        $this->log->info("Delete event request received : " . json_encode($event) . " Loggedin user id : " . Auth::user()->id);
        $eventCategoryEvent = EventCategoryEvent::where("event_id", $event->id)->delete();
        $event->facilities()->detach();
        $event->geolocations()->delete();
        $event->delete();
        $this->log->info("Delete event successfully by Loggedin user id : " . Auth::user()->id);
        return $event;
    }

    public function getEventList(Request $request)
    {
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $admin_partner_id = Auth::user()->created_by;
            if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                $partner_id = isset($request->partner_id) ? $request->partner_id : '';
                $rm_id = Auth::user()->user_parent_id;
            } else {
                $partner_id = Auth::user()->created_by;
                $rm_id = Auth::user()->user_parent_id;
            }
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
        } else if (Auth::user()->user_type == self::BUSINESS_USERTYPE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = $request->rm_id;
            $created_by = Auth::user()->id;
        } else {
            throw new UserNotAuthorized("Invalid User!");
        }
        $events = DB::table('events')->select('id as id', 'title as name', 'slug', 'is_active');
        if ($partner_id) {
            $events = $events->where('partner_id', $partner_id);
        }
        if ($rm_id) {
            $events = $events->where('rm_id', $rm_id);
        }
        if ($request->facility_id) {
            $eventIDs = DB::table('event_facility')->where('facility_id', $request->facility_id)->pluck('event_id');
            $events = $events->whereIn('id',  $eventIDs);
        }
        $events = $events->whereNull("deleted_at")->get();
        if (empty($events)) {
            throw new ApiGenericException('Event  Record Not Found');
        }
        return $events;
    }
    // image upload
    public function imageUpload(Request $request)
    {

        //$this->validate($request, Event::$validImage);
        $this->validate($request, ['images' => 'mimes:png|dimensions:max_width=250,max_height=250'], ['images.mimes' => 'Image  must be a file of type : .png', 'images.dimensions' => 'Image logo should not be greater than 250 x 250 pixels.']);
        if ($request->file('images')) {
            $files = $request->file('images');

            $file_extension = $files->getClientOriginalExtension();
            // $imageName = $files->getClientOriginalName();

            $imageName = str_random(5) . "." . $file_extension;
            //  $imageName = $name .".". $file_extension;
            $destinationPath = 'assets/media/images/mapco/';
            $request->file('images')->move($destinationPath, $imageName);
            $request->request->add(['image' => $imageName]);
            $imagepath = env('APP_URL') . '/assets/media/images/mapco/' . $imageName;
            $data = [
                'image_name' => $imageName,
                'image_path' => $imagepath
            ];
        }
        return $data;
    }

    // update image 
    public function eventImageUpdate(Request $request)
    {


        $events = Event::where('id', $request->id)->first();
        if (!$events) {
            throw new ApiGenericException('Event  Record Not Found');
        }
        /*
                    if($events->image!=''){
                        $file_path = 'assets/media/images/mapco/'.$request->images;
                        unlink($file_path);	
                    }
                        */
        Event::where('id', $request->id)->update(['image' => $request->images]);
        $imagepath = env('APP_URL') . '/assets/media/images/mapco/' . $request->images;
        $data = [
            'id' => $request->id,
            'image_name' => $request->images,
            'image_path' => $imagepath
        ];

        return $data;
    }


    // active inactive events with checkbox
    public function eventActiveInactive(Request $request)
    {
        $this->log->info("Activate Deactivate event request received : " . json_encode($request->all()));
        $msg = '';
        $today = date("Y-m-d");
        $flag = 0;
        if (isset($request->events) && count($request->events) > 0) {
            $result = [];
            foreach ($request->events as $key => $row) {
                $is_active = $row['is_active'];

                $events = Event::where('id', $row['id'])->first();
                $eventDate = date("Y-m-d", strtotime($events->end_time));
                if (strtotime($eventDate) >= strtotime($today)) {
                    $events->is_active = $is_active;
                    $events->save();
                    $result[$key] = $events;
                } else {
                    $flag++;
                    $result[$key] = $events;
                }
            }
            if ($flag > '0') {
                $result['suceess_msg'] = "Sorry,We can not update past date event";
            } else {
                $result['suceess_msg'] = "Event is updated successfully";
            }

            return $result;
        } else {
            throw new ApiGenericException('Request data not found');
        }
    }

    public function getEvents(Request $request)
    {

        if (isset($request->facility_id) && count($request->facility_id) > 0) {
            $result = [];
            $this->setCustomTimezone($request->facility_id[0]);
            $events = DB::table('event_facility')->whereIn('facility_id', $request->facility_id)->get();

            foreach ($events as $key => $value) {
                $event = Event::where('id', $value->event_id)->where('is_active', '1')->whereDate("end_time", ">=", date('Y-m-d'))->first();
                if (isset($event)) {
                    $result[] = ['title' => $event->title, 'event_id' => $event->id, 'facility_id' => $value->facility_id];
                }
            }
            return $result;
        } else {
            throw new ApiGenericException('Input paramaters are missing.');
        }
    }

    public function getMapEvents($id)
    {
        /*
        $result = [];
        $ongoing_events = [];
        $userDetails = User::where('status', 1)->find($id);
        $facility_ids = \DB::table('user_facilities')->where('user_id', $id)->whereNull('deleted_at')->get();

        if ($facility_ids) {
            foreach ($facility_ids as $key => $val) {
                $facility = Facility::with(['gates', 'FacilityPaymentDetails'])->where('id', $val->facility_id)->first();
                $event_processing_fee =  $facility->getProcessingFee(1);
                $event_tax = '0.00';
                $this->setCustomTimezone($val->facility_id);
                $eventsId = DB::table('event_facility')->where('facility_id', $val->facility_id)->pluck('event_id');
                if ($eventsId) {
                    $events = Event::whereIn('id', $eventsId)->where('is_active', '1')->orderBy('start_time', 'ASC')->get();
                    foreach ($events as $event) {
                        $today_date = date('Y-m-d');
                        $event_start_date = date("Y-m-d", strtotime($event->start_time));
                        $event_end_date = date("Y-m-d", strtotime($event->end_time));
                        $total = number_format(($event->event_rate + $event_processing_fee + $event_tax), 2);
                        if ($event !== null && $event_start_date > $today_date) {
                            $result[] = ['title' => $event->title, 'event_id' => $event->id, 'start_time' => $event->start_time, 'end_time' => $event->end_time, 'event_rate' => $event->event_rate, 'parking_start_time' => $event->parking_start_time, 'parking_end_time' => $event->parking_end_time, 'active' => $event->is_active, 'image' => $event->image, 'image_url' => $event->image_url, 'event_details' => $event, 'event_processing_fee' => $event_processing_fee, 'event_tax' => $event_tax, 'event_total' => $total, 'parking_amount' => $event->event_rate, 'facility' => $facility];
                        } else if ($event !== null && $event_start_date <= $today_date && $event_end_date >= $today_date) {
                            $ongoing_events[] = ['title' => $event->title, 'event_id' => $event->id, 'start_time' => $event->start_time, 'end_time' => $event->end_time, 'event_rate' => $event->event_rate, 'parking_start_time' => $event->parking_start_time, 'parking_end_time' => $event->parking_end_time, 'active' => $event->is_active, 'image' => $event->image, 'image_url' => $event->image_url, 'event_details' => $event, 'event_processing_fee' => $event_processing_fee, 'event_tax' => $event_tax, 'event_total' => $total, 'parking_amount' => $event->event_rate, 'facility' => $facility];
                        }
                    }
                } else {
                    //throw new ApiGenericException('Event Not Found');     
                }
            }
            $userDetails->upcoming_events = $result;
            $userDetails->ongoing_events = $ongoing_events;
            return $userDetails;
        } else {
            throw new ApiGenericException('Facility Not Found');
        }
		*/
        $result = [];
        $ongoing_events = [];
        $userDetails = User::where('status', 1)->find($id);
        $facility_ids = DB::table('user_facilities')->where('user_id', $id)->whereNull('deleted_at')->pluck('facility_id');

        if ($facility_ids) {
            $eventsId = DB::table('event_facility')->whereIn('facility_id', $facility_ids)->pluck('event_id');
            if ($eventsId) {
                $events = Event::with(['eventFacility'])->whereIn('id', $eventsId)->where('is_active', '1')->orderBy('start_time', 'ASC')->get();
                foreach ($events as $event) {
                    $facility = Facility::with(['gates', 'FacilityPaymentDetails'])->where('id', $event->eventFacility[0]->facility_id)->first();
                    $event_processing_fee =  $facility->getProcessingFee(1);
                    $event_tax = '0.00';
                    $this->setCustomTimezone($facility->id);

                    $today_date = date('Y-m-d');
                    $event_start_date = date("Y-m-d", strtotime($event->start_time));
                    $event_end_date = date("Y-m-d", strtotime($event->end_time));
                    $total = number_format(($event->event_rate + $event_processing_fee + $event_tax), 2);
                    if ($event !== null && $event_start_date > $today_date) {
                        $result[] = ['title' => $event->title, 'event_id' => $event->id, 'start_time' => $event->start_time, 'end_time' => $event->end_time, 'event_rate' => $event->event_rate, 'parking_start_time' => $event->parking_start_time, 'parking_end_time' => $event->parking_end_time, 'active' => $event->is_active, 'image' => $event->image, 'image_url' => $event->image_url, 'event_details' => $event, 'event_processing_fee' => $event_processing_fee, 'event_tax' => $event_tax, 'event_total' => $total, 'parking_amount' => $event->event_rate, 'facility' => $facility];
                    } else if ($event !== null && $event_start_date <= $today_date && $event_end_date >= $today_date) {
                        $ongoing_events[] = ['title' => $event->title, 'event_id' => $event->id, 'start_time' => $event->start_time, 'end_time' => $event->end_time, 'event_rate' => $event->event_rate, 'parking_start_time' => $event->parking_start_time, 'parking_end_time' => $event->parking_end_time, 'active' => $event->is_active, 'image' => $event->image, 'image_url' => $event->image_url, 'event_details' => $event, 'event_processing_fee' => $event_processing_fee, 'event_tax' => $event_tax, 'event_total' => $total, 'parking_amount' => $event->event_rate, 'facility' => $facility];
                    }
                }
            }
            $userDetails->upcoming_events = $result;
            $userDetails->ongoing_events = $ongoing_events;
            return $userDetails;
        } else {
            throw new ApiGenericException('Facility Not Found');
        }
    }

    public function getEventDetails($event_id, $facility_id)
    {
        try {
            $facility = Facility::with(['gates'])->select(['id', 'full_name', 'short_name', 'base_event_rate', 'processing_fee', 'user_receipt_url', 'is_receipt_enabled', 'user_receipt_url', 'generic_receipt_url', 'entrance_location', 'tax_rate', 'tax_rate_type', 'additonal_fee', 'surcharge_fee'])->with(['geolocations', 'facilityConfiguration'])->where('id', $facility_id)->where('active', 1)->first();
            if ($facility) {
                $facility->base_event_rate_total = $facility->base_event_rate + $facility->processing_fee;
                if ($facility) {
                    $this->setCustomTimezone($facility->id);
                }
                $today_date = date('Y-m-d');

                $event = Event::where('id', $event_id)->wheredate('end_time', '>=', $today_date)->where('is_active', 1)->first();
                if (!$event) {
                    throw new ApiGenericException('Event Details Not Found.');
                }
                $event->setRelation('facility', $facility);
                $event_processing_fee =  $facility->getProcessingFee(1);
                //PIMS-14961 Dev:Sagar
                $rate['price'] = $event->event_rate;
                $taxDetails = $facility->getTaxRateForEvent($rate, $facility); 
                $event_tax = $taxDetails['tax_rate'];
                $cc_tax = $taxDetails['cc_fee'];
                $surcharge_tax = $taxDetails['surcharge_fee'];
                $event_tax = $facility->getTaxRate($rate); 
                $cc_tax = $facility->getAdditionalFee($rate);
                $surcharge_tax = $facility->getSurchargeFee($rate);
                // dd($event_tax);
                $total = number_format(($event->event_rate + $event_processing_fee + $event_tax + $cc_tax + $surcharge_tax), 2);
                $event->event_processing_fee = $event_processing_fee;
                $event->event_tax = $event_tax;
                $event->ccFee = $cc_tax; //PIMS-14961 Dev:Sagar
                $event->surchargeFee = $surcharge_tax; //PIMS-14961 Dev:Sagar
                $event->event_total = $total;
                if ($event->event_cash_rate == '0.00') {
                    $event->event_cash_rate = $facility->base_event_rate;
                } else if (($event->event_cash_rate == '0.00') && ($event->event_rate == '0.00')) {
                    $event->event_cash_rate = $total;
                }
                $event->parking_amount = $event->event_rate;

                // if($facility->facilityConfiguration->is_sbm_event_qrcode == '1'){
                //     $event->parkingFacilityIdentifier = config('parkengage.Facility_ID');
                //     $event->productIdentifier  = config('parkengage.PRODUCT_IDENTIFIER');
                // }
                $event->parkingFacilityIdentifier = $facility->facilityConfiguration->product_identifier_id;
                $event->productIdentifier  = $facility->facilityConfiguration->product_identifier;
                if ($event->partner_id == config('parkengage.PARTNER_PCI') && $facility->id == config('parkengage.PCC_FACILITY')) {
                    $thirtyDaysLater = Carbon::now()->addDays(30);
                    $endTime = Carbon::parse($event->end_time);

                    if ($thirtyDaysLater->lessThan($endTime)) {
                        $event->monthly_event_endate = $thirtyDaysLater->format('Y-m-d H:i:s');
                    } else {
                        $event->monthly_event_endate = $endTime->format('Y-m-d H:i:s');
                    }
                } else {
                    $event->monthly_event_endate = $event->end_time;
                }
                return $event;
            } else {
                throw new ApiGenericException('Facility Not Found');
            }
        } catch (\Exception $e) {
            // Handle the exception (e.g., log the error and set a default value)
            throw new ApiGenericException($e->getMessage());
        }
    }


    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($facility->timezone != '') {
                date_default_timezone_set($facility->timezone);
            } else if ($partnerTimezone->timezone != '') {
                date_default_timezone_set($partnerTimezone->timezone);
            }
        }
    }

    public function getOnGoingEvents($id)
    {
        $ongoing_events = [];
        $userDetails = User::where('status', 1)->find($id);
        $facility_ids = DB::table('user_facilities')->where('user_id', $id)->whereNull('deleted_at')->get();

        if ($facility_ids) {
            foreach ($facility_ids as $key => $val) {
                $facility = Facility::with(['gates', 'FacilityPaymentDetails'])->where('id', $val->facility_id)->first();
                $event_processing_fee =  $facility->getProcessingFee(1);
                $event_tax = '0.00';
                $this->setCustomTimezone($val->facility_id);
                $eventsId = DB::table('event_facility')->where('facility_id', $val->facility_id)->pluck('event_id');
                if ($eventsId) {
                    $events = Event::whereIn('id', $eventsId)->where('is_active', '1')->get();
                    foreach ($events as $event) {
                        $today_date = date('Y-m-d');
                        $event_start_date = date("Y-m-d", strtotime($event->start_time));
                        $event_end_date = date("Y-m-d", strtotime($event->end_time));
                        $total = number_format(($event->event_rate + $event_processing_fee + $event_tax), 2);
                        if ($event !== null && $event_start_date <= $today_date && $event_end_date > $today_date) {
                            $ongoing_events[] = ['title' => $event->title, 'event_id' => $event->id, 'start_time' => $event->start_time, 'end_time' => $event->end_time, 'event_rate' => $event->event_rate, 'parking_start_time' => $event->parking_start_time, 'parking_end_time' => $event->parking_end_time, 'active' => $event->is_active, 'image' => $event->image, 'image_url' => $event->image_url, 'event_details' => $event, 'event_processing_fee' => $event_processing_fee, 'event_tax' => $event_tax, 'event_total' => $total, 'parking_amount' => $event->event_rate, 'facility' => $facility];
                        }
                    }
                } else {
                    throw new ApiGenericException('Event Not Found');
                }
            }
        } else {
            throw new ApiGenericException('Facility Not Found');
        }
        $userDetails->ongoing_events = $ongoing_events;
        return $userDetails;
    }
    //Alka
    /****
     * Method to delete event image (logo)
     * * */
    public function imageDelete(request $request)
    {
        $events = Event::where('id', $request->id)->first();

        if (!$events) {
            throw new ApiGenericException('Event  Record Not Found');
        }
        $deleteImage = Event::where('id', $request->id)
            ->where(['image' => $request->images])
            ->update(['image' => null]);

        $imagepath = env('APP_URL') . '/assets/media/images/mapco/' . $request->images;
        if (file_exists($imagepath)) {
            unlink($imagepath);
        }
        if ($deleteImage) {
            $eventData = Event::where('id', $request->id)->first();
            return $eventData;
        }
        return $events;
    }
    //End

    public function getEventsList(Request $request)
    {

        if (!Auth::user()) {
            throw new UserNotAuthorized("Invalid User!");
        }
        if (Auth::user()->status == '0') {
            throw new UserNotAuthorized("User is inactive. Please contact to admin.");
        }
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id      = $request->rm_id;
            $facilities = DB::table('facilities')->where('owner_id', $partner_id)->where('active', 1)->pluck('id');
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
            $facilities = DB::table('user_facilities')->where('user_id', $rm_id)->pluck('facility_id');
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
            $facilities = DB::table('facilities')->where('owner_id', $partner_id)->where('active', 1)->pluck('id');
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
            $facilities = DB::table('user_facilities')->where('user_id', $rm_id)->pluck('facility_id');
        } else {
            throw new UserNotAuthorized("Invalid User!");
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);
        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date("Y-m-d", strtotime($request->from_date));
            $to_date = date("Y-m-d", strtotime($request->to_date));
        }

        if (isset($request->attendant_id) && !empty($request->attendant_id) && $request->attendant_id != 'all') {
            $user_facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', $request->attendant_id)->pluck('facility_id');
            $events = DB::table('event_facility')->whereIn('facility_id', $user_facilities)->pluck('event_id');
            $event  = DB::table('events')->select('id', 'title')->where('partner_id', $partner_id)->whereDate("start_time", ">=",  $from_date)->whereDate("end_time", "<=",  $to_date)->whereIn('id', $events)->get();

            if (!count($event)) {
                return response()->json([], 200);
            }
            return $event;
        }

        if (isset($request->facility_id) && !empty($request->facility_id) && $request->facility_id != 'all') {
            $result = [];
            $this->setCustomTimezone($request->facility_id);
            $events = DB::table('event_facility')->where('facility_id', $request->facility_id)->pluck('event_id');
            $event = DB::table('events')->select('id', 'title')->where('partner_id', $partner_id)->whereDate("start_time", ">=",  $from_date)->whereDate("end_time", "<=",  $to_date)->whereIn('id', $events)->get();

            if (!count($event)) {
                return response()->json([], 200);
            }
            return $event;
        } else if ($facilities) {
            $result = [];
            $this->setCustomTimezone($facilities[0]);
            $events = DB::table('event_facility')->whereIn('facility_id', $facilities)->pluck('event_id');

            $event = DB::table('events')->select('id', 'title')->where('partner_id', $partner_id)->whereDate("start_time", ">=",  $from_date)->whereDate("end_time", "<=",  $to_date)->whereIn('id', $events)->get();
            if (!count($event)) {
                return response()->json([], 200);
            }
            return $event;
        } else {
            throw new ApiGenericException('Facility Not Found.');
        }
    }
}
