<?php

namespace App\Http\Controllers\ParkEngage;

use App\Classes\DatacapPaymentGateway;
use App\Classes\HeartlandPaymentGateway;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Carbon\Carbon;
use App\Models\Rate;
use App\Models\OauthClient;
use App\Models\PermitVehicle;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserNotAuthorized;
use Illuminate\Support\Facades\DB;
use Mail;
use App\Services\Pdf;
use App\Models\User;
use App\Models\Facility;
use Illuminate\Support\Facades\Storage;
use Response;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\Gate;
use App\Models\Ticket;
use App\Models\TicketAdditionalInfo;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use Excel;
use App\Models\Otp;
use App\Services\Image;
use App\Models\OverstayTicket;
use App\Models\UserPass;
use App\Models\BusinessPolicy;
use App\Models\BusinessQrCode;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\ParkEngage\UserValidateMaster;
use App\Models\ParkEngage\UserValidateUsages;
use App\Models\PromoUsage;
use App\Models\ParkEngage\TicketExtend;
use App\Models\RefundTransaction;
use DateTime;

class SubordinateController extends Controller
{
	protected $log;
	protected $errorLog;
	protected $request;
	protected $user;
	protected $logPreAuthAmount;
	protected $countryCode;
	protected $facility;
	protected $facilityFee;
	protected $passData;
	protected $partnerPaymentDetails;

	const RESERVATION_THRESHOLD_TYPE = 2;
	const BUSINESS_CLERK = 8;
	const BUSINESS_USER = 10;
	const SUBORDINATE = 4;
	const PARTNER = 3;
	const SUPERADMIN = 1;
	const REGIONAL_MANAGER = 12;
	const URL_PATH = '/admin/tickets';

	public function __construct(Request $request, LoggerFactory $logFactory)
	{
		$this->request = $request;
		$this->log = $logFactory->setPath('logs/parkengage/retail')->createLogger('retail');
		$this->logPreAuthAmount = $logFactory->setPath('logs/preauth_log/pre-auth-amount-release-logs')->createLogger('pre-auth-amount-release-logs');
	}

	public function ticketSearch(Request $request)
	{
		$this->log->info('ticketSearch Request : ' . json_encode($request->all()));
		$facilities = [];
		$clerkLimit = NULL;
		// $partner_id == config('parkengage.PARTNER_COLONIAL')
		if (Auth::check()) {
			if (!Auth::user()) {
				throw new UserNotAuthorized("Invalid User!");
			}
			$this->log->info('Login User Details : ' . json_encode(Auth::user()->id));
			if (Auth::user()->status == '0') {
				throw new UserNotAuthorized("You dont have permission to log in. Please contact your Admin for further details.");
			}
			if (Auth::user()->user_type == self::BUSINESS_CLERK) {
				$partner_id = Auth::user()->created_by;
				$clerkLimit = Auth::user()->clerkLimit;
				$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
			}
			if (Auth::user()->user_type == self::BUSINESS_USER) {
				$partner_id = Auth::user()->created_by;
				$facilities = DB::table('business_facility_policy')->where('business_id', Auth::user()->business_id)->pluck('facility_id');
			}
			if (Auth::user()->user_type == self::SUBORDINATE) {
				$partner_id = Auth::user()->created_by;
				$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
			}
			if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
				$partner_id = Auth::user()->created_by;
				$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
			}
			if (Auth::user()->user_type == self::PARTNER) {
				$partner_id = Auth::user()->id;
				$facilities = DB::table('facilities')->whereNull('deleted_at')->where('owner_id', Auth::user()->id)->pluck('id');
			}
			if (Auth::user()->user_type == self::SUPERADMIN) {
				$partner_id = $request->partner_id;
				$facilities = DB::table('facilities')->whereNull('deleted_at')->where('owner_id', $request->partner_id)->pluck('id');
			}
			if ($facilities) {
				$ticket = Ticket::with(['user', 'transaction', 'facility'])->whereIn('facility_id', $facilities)->whereNull('permit_request_id');
				if ($partner_id != config('parkengage.PARTNER_COLONIAL')) {
					$ticket = $ticket->whereNull('reservation_id')->whereNull('user_pass_id')->whereNull('event_id')->whereNull('event_user_id');
				}
			} else {
				$ticket = Ticket::with(['user', 'transaction', 'facility'])->whereIn('facility_id', $facilities)->whereNull('permit_request_id');
				if ($partner_id != config('parkengage.PARTNER_COLONIAL')) {
					$ticket = $ticket->whereNull('reservation_id')->whereNull('user_pass_id')->whereNull('event_id')->whereNull('event_user_id');
				}
			}
			$facility = Facility::whereIn('id', $facilities)->first();
		} else {
			$partner_id = $request->partner_id;
			//for guest user
			$facility = Facility::where('id', $request->facility_id)->first();
			if (!$facility) {
				throw new ApiGenericException("Facility id not fund.");
			}
			$ticket = Ticket::with(['user', 'transaction', 'facility'])->where('facility_id', $facility->id);
		}
		if (($request->ticket != '') && ($request->license_plate != '') && ($request->phone != '') && ($request->cardLast4 != '') && ($request->cardExpiry != '')) {
			$ticket = $ticket->Where('ticket_number', $request->ticket)->Where('license_plate', 'like', "%{$request->license_plate}%");
			if (isset($facility) && ($facility->is_gated_facility == '1')) {
				//	$ticket = $ticket->where('anet_transaction_id', NULL);
			}
			$ticket = $ticket->WhereHas(
				'user',
				function ($query) use ($request) {
					$query->where('phone', 'like', "%{$request->phone}%");
				}
			);
			$ticket = $ticket->Where('card_last_four', $request->cardLast4);
			$ticket = $ticket->Where('expiry', $request->cardExpiry);
		} else if (($request->ticket != '') || ($request->license_plate != '') || ($request->phone != '') || ($request->cardLast4 != '') || ($request->cardExpiry != '')) {
			if (isset($facility) && ($facility->is_gated_facility == '1')) {
				//	$ticket = $ticket->where('anet_transaction_id', NULL);
			}
			if ($request->ticket != '') {
				$ticket = $ticket->Where('ticket_number', 'like', "%{$request->ticket}%");
			}
			if ($request->license_plate != '') {
				$ticket = $ticket->Where('license_plate', 'like', "%{$request->license_plate}%");
			}
			if ($request->phone != '') {
				$ticket = $ticket->WhereHas(
					'user',
					function ($query) use ($request) {
						$query->where('phone', 'like', "%{$request->phone}%");
					}
				);
			}
			if (($request->cardLast4 != '') && ($request->cardExpiry != '')) {
				$ticket = $ticket->Where('card_last_four', $request->cardLast4);
				$ticket = $ticket->Where('expiry', $request->cardExpiry);
			}
			if (($request->cardLast4 != '') && ($request->cardExpiry == '')) {
				$ticket = $ticket->Where('card_last_four', $request->cardLast4);
			}
			if (($request->cardLast4 == '') && ($request->cardExpiry != '')) {
				$ticket = $ticket->Where('expiry', $request->cardExpiry);
			}
		} else {
			throw new ApiGenericException("Invalid Search Parameter.");
		}
		$ticket = $ticket->orderBy("id", "desc")->first();
		if (!$ticket) {
			throw new NotFoundException("There is no active check-in for this search criteria.");
		}
		if ($request->display_ticket == '1') {
			$overstay = OverstayTicket::where('ticket_number', $ticket->ticket_number)->first();
			if ($overstay) {
				$ticket['overstay_amount'] = $overstay->total;
			} else {
				$ticket['overstay_amount'] = '0';
			}
			$diff_in_hours = $ticket->getCheckOutCurrentTime(true);
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$isMember = 0;
			$rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
			if ($rate == false) {
				throw new ApiGenericException('Garage is currently closed.');
			}
			$processingFee = $ticket->getProcessingFee();   // to get prcessing free channel wise need to
			$taxRate = $ticket->getTaxRate($rate);
			$priceBreakUp = $ticket->priceBreakUp($rate);
			$this->log->info("getCheckinCheckoutDetails priceBreakUp " . json_encode($priceBreakUp));
			$rate['facility'] = $ticket->facility;
			$ticket['rate'] = $rate;
			$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
			$ticket['diff_in_days'] = $startDate->diffInDays($endDate);
			$ticket['diff_in_hours'] = $startDate->copy()->addDays($ticket['diff_in_days'])->diffInRealHours($endDate);
			$ticket['diff_in_minutes'] = $startDate->copy()->addDays($ticket['diff_in_days'])->addHours($ticket['diff_in_hours'])->diffInRealMinutes($endDate);
			$ticket['total']  = $priceBreakUp['total'];
			$ticket['payable_amount']  = $priceBreakUp['payable_amount'];
			$ticket['processing_fee']  = $priceBreakUp['processing_fee'];
			$ticket['tax_rate']        = $priceBreakUp['tax_rate'];
			$ticket['parking_amount']  = $priceBreakUp['parking_amount'];
			$ticket['overstay_amount'] = $priceBreakUp['overstay_amount'];
			$ticket['paid_amount']     = $priceBreakUp['paid_amount'];
			$ticket['discount_amount'] = $priceBreakUp['discount_amount'];
			$ticket['amount_paid']     = $ticket->grand_total > 0 ? $ticket->grand_total : '0.00';
			if ($ticket->paid_type == '0') {
				$ticket['payment'] = 1;
			} else {
				if ($ticket['payable_amount'] > 0) {
					$ticket['payment'] = 0;
				} else {
					$ticket['payment'] = 1;
				}
			}
			$gates = Gate::where('facility_id', $ticket->facility_id)->get();
			if ($gates) {
				foreach ($gates as $key => $value) {
					if ($value->gate == $ticket->checkin_gate) {
						$ticket['checkin_gate_name'] = $value->gate_name;
					}
					if ($value->gate == $ticket->checkout_gate) {
						$ticket['checkout_gate_name'] = $value->gate_name;
					}
				}
			} else {
				$ticket['checkin_gate_name'] = '';
				$ticket['checkout_gate_name'] = '';
			}
			$ticket['paid_by'] = isset($user->name) ? $user->name : '';
			if ($ticket->is_offline_payment == '1') {
				$ticket->payment_response_message = 'Offline';
			} else if ($ticket->is_offline_payment == '2' || $ticket->is_offline_payment == '3') {
				$ticket->payment_response_message = 'Offline/Processed';
			} else if ($ticket->anet_transaction_id != '') {
				$ticket->payment_response_message = 'Processed';
			} else if ($ticket->anet_transaction_id == '') {
				if ($ticket->card_last_four != '') {
					$ticket->payment_response_message = 'Authorized';
				} else {
					$ticket->payment_response_message = '';
				}
			} else if (strtolower($ticket->transaction->response_message) == 'approval' || strtolower($ticket->transaction->response_message) == 'approved') {
				$ticket->payment_response_message = 'Processed';
			}
			return $ticket;
		}
		if (isset($facility) && ($facility->is_gated_facility == '0')) {
			if ($ticket->is_checkout == '1' || $ticket->is_checkout == 1) {
				//		throw new ApiGenericException("No active check-in against this ticket.");      
			}
		}
		if ($ticket->facility->is_gated_facility == '1') {
			if ($ticket->is_checkout == '1' && ($partner_id != config('parkengage.PARTNER_COLONIAL'))) {
				throw new ApiGenericException('Ticket has been already checked out.');
			}
		}
		if ($partner_id != config('parkengage.PARTNER_COLONIAL')) {
			if (in_array($ticket->is_offline_payment, [1, 2, 3]) || ($ticket->anet_transaction_id != '')) {
				$ticket->error_msg = "Ticket can not be validated as it is already paid by the user";
			}
		}
		/*
		if (in_array($ticket->is_offline_payment, [1, 2, 3]) || ($ticket->anet_transaction_id != '')) {
			$ticket->error_msg = "Ticket can not be validated as it is already paid by the user";
		}
		*/
		$ticket->clerk_limit = $clerkLimit;
		return $ticket;
	}

	public function validateTicket(Request $request)
	{
		// Check and normalize ticket_number input
		$ticketNumbers = $request->ticket_number;

		if (strpos($ticketNumbers, '[') !== false) $ticketNumbers = array_map('trim', explode(',', trim($ticketNumbers, "[]")));
		else $ticketNumbers = [$ticketNumbers];

		$results = [];
		foreach ($ticketNumbers as $ticketNumber) {
			try {
				$request->merge(['ticket_number' => $ticketNumber]); // Update the request with the current ticket number
				$business_policy = BusinessPolicy::where('id', $request->policy_id)->first();

				if (!$business_policy) {
					throw new ApiGenericException("Invalid Policy ID: {$request->policy_id}");
				}

				$validateTicket = null;

				if ($business_policy->discount_type === '0') {
					$validateTicket = $this->makeCheckout($request);
				} elseif ($business_policy->discount_type === '1' && $business_policy->duration_type == 'hours') {
					$request->merge(['hours' => $business_policy->discount_value]);
					$validateTicket = $this->makeCheckout($request);
				} elseif ($business_policy->discount_type === '2') {
					$request->merge(['amount' => $business_policy->discount_value]);
					$validateTicket = $this->makeCheckout($request);
				} elseif ($business_policy->discount_type === '3') {
					$request->merge(['percent' => $business_policy->discount_value]);
					$validateTicket = $this->makeCheckout($request);
				} else {
					throw new UserNotAuthorized("Invalid Policy!");
				}

				$this->log->info("Print validateTicket for Ticket {$ticketNumber} :  " . json_encode($validateTicket));

				if ($validateTicket) {
					$results[$ticketNumber] = $validateTicket;
					$results['clerk_limit'] = $validateTicket->clerk_limit;
				} else {
					throw new ApiGenericException("No active check-in for ticket: {$ticketNumber}");
				}
			} catch (\Exception $err) {
				$this->log->error("Error validating ticket {$ticketNumber}: " . $err->getMessage());
				throw new ApiGenericException($err->getMessage());
			}
		}
		// If only one ticket, return the result directly
		if (Auth::check()) {
			if (Auth::user()->user_type == self::BUSINESS_CLERK) {
				$cleckRemaining =  UserValidateMaster::select('*')->where('user_id', Auth::user()->id)->first();
				if ($cleckRemaining) {
					$results['clerk_limit'] = $cleckRemaining;
				}
			}
		}
		return count($results) === 1 ? reset($results) : $results;
	}

	public function makeCheckout(Request $request)
	{
		$this->log->info('makeCheckout Request : ' . json_encode($request->all()));
		if (Auth::check()) {
			if (!Auth::user()) {
				throw new UserNotAuthorized("Invalid User!");
			}
			if (Auth::user()->status == '0') {
				throw new UserNotAuthorized("You dont have permission to log in. Please contact your Admin for further details.");
			}
			if (Auth::user()->user_type == self::BUSINESS_CLERK) {
				$partner_id = Auth::user()->created_by;
			}
			if (Auth::user()->user_type == self::BUSINESS_USER) {
				$partner_id = Auth::user()->created_by;
			}
			if (Auth::user()->user_type == self::SUBORDINATE) {
				$partner_id = Auth::user()->created_by;
			}
			if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
				$partner_id = Auth::user()->created_by;
			}
			if (Auth::user()->user_type == self::PARTNER) {
				$partner_id = Auth::user()->id;
			}
			if (Auth::user()->user_type == self::SUPERADMIN) {
				$partner_id = $request->partner_id;
			}
		} else {
			$partner_id = $request->partner_id;
		}
		$ticket = Ticket::with(['user', 'facility.FacilityPaymentDetails', 'transaction'])->where('ticket_number', $request->ticket_number)->whereNull('permit_request_id');

		if ($partner_id != config('parkengage.PARTNER_COLONIAL')) {
			$ticket = $ticket->whereNull('reservation_id')->whereNull('user_pass_id')->whereNull('event_id')->whereNull('event_user_id');
		}
		$ticket = $ticket->first();
		//In Ungated facility if checkout time is cross then ticket should be validated.
		if (!$ticket) {
			throw new ApiGenericException("There is no active check-in for this search criteria.");
		}
		$new_updated_price = '';
		if ($ticket->paid_by) {
			throw new ApiGenericException("The Ticket is already validated.");
		}
		$ticketLength = $ticket->length;
		$ticketextendDatas = TicketExtend::with(['ticket', 'facility'])->where('ticket_number', $ticket->ticket_number)->orderBy('id', 'desc')->get();
		$extendCCfee = $extendSurchargeFee = $ExtentTicketTotal = $TotalParkingAmount = $ExtentTicketTax = $ExtentTicketProcessingFee = $extendTax = 0;
		$TotalParkingAmount = $ticket->parking_amount;
		if (count($ticketextendDatas) > 0) {
			$count = 0;

			foreach ($ticketextendDatas as $ticketextendData) {
				if ($ticketextendData->facility->is_gated_facility == '0') {
					if (date('Y-m-d H:i:s') > date('Y-m-d H:i:s', strtotime($ticketextendData->checkout_time))) {
						throw new ApiGenericException('Ticket has been already checked out.');
					}
				}
				$ExtentTicketTotal 		      += $ticketextendData->total;
				$TotalParkingAmount 		  += $ticketextendData->new_parking_amount ?? 0.00;
				$ExtentTicketTax 		      += $ticketextendData->tax_fee;
				$ExtentTicketProcessingFee    += $ticketextendData->processing_fee;
				$extendCCfee                  += $ticketextendData->additional_fee;
				$extendSurchargeFee           += $ticketextendData->surcharge_fee;
				$ticketLength                 += $ticketextendData->length;
				$count++;
			}
		} else {
			if ($ticket->facility->is_gated_facility == '0') {
				if (date('Y-m-d H:i:s') > date('Y-m-d H:i:s', strtotime($ticket->checkout_time)) && !isset($request->enabled_ticket_creation_flow) && ($request->enabled_ticket_creation_flow == '0')) {
					throw new ApiGenericException('Ticket has been already checked out.');
				}
			}
		}
		//validate clerk remaining usages.
		if ($ticket) {

			 if (!empty($request->enabled_ticket_creation_flow) && $request->enabled_ticket_creation_flow == '1') {
				#PIMS-14577 || Dev: Sagar || 11/07/2025
				$length     = 0.00;
				$policyName = 'Full Validate'; // default

				$qRCode = BusinessQrCode::where('business_qrcode', base64_decode($request->business_qrcode))->first();

				if ($qRCode) {
					$businessPolicy = BusinessPolicy::find($qRCode->policy_id);
					if ($businessPolicy && $businessPolicy->discount_type == '1') {
						$length     = round($businessPolicy->discount_value, 2);
						$policyName = $businessPolicy->policy_name ?? $policyName;
					}
				}

				$remark = "Ticket validated by scanning Restaurant QR code";

				$ticket->is_checkout       = '0';
				$ticket->is_closed         = '0';
				$ticket->checkout_remark   = $remark;
				$ticket->paid_date         = date('Y-m-d H:i:s');
				$ticket->payment_date      = null;
				$ticket->paid_type         = '0';
				$ticket->paid_remark       = "$remark: $policyName";
				$ticket->length            = $length;

				// Assign paid_by safely
				$user = User::select('id')->where('business_id', $ticket->business_id)->first();
				$ticket->paid_by = $user ? $user->id : null;

				// Estimate checkout
				$checkIn  = Carbon::parse($ticket->check_in_datetime);
				$checkout = $checkIn->copy()->addHours((int) $request->hours);

				$ticket->estimated_checkout = $checkout->toDateTimeString();
				$ticket->checkout_datetime  = $checkout->toDateTimeString();
				$ticket->checkout_time      = $checkout->toDateTimeString();

				$ticket->save();

				return $ticket;
			}

			//vikrant add condition Self Check-in Policy for colonial specific if user paid $20 already then we can not validate user
			if ($partner_id == config('parkengage.PARTNER_COLONIAL')) {
				if ($request->policy_id == 65 && $ticket->anet_transaction_id != '') {
					if ($ticket->parking_amount <= $ticket->facility->base_rate) {
						throw new ApiGenericException('Ticket can not be validated as validate amount is greater than paid amount.');
					}
				}
			}
			$promocodeUsage = PromoUsage::where('ticket_id', $ticket->id)->first();
			if ($promocodeUsage) {
				$ticket->promocodeUsage = $promocodeUsage;
			}
			$this->setCustomTimezone($ticket->facility_id);

			// PIMS-11195 
			// This Apple Payment Case : 
			// We make refund for Apple PAY in case validation to skip the anet check.   
			if ($partner_id != config('parkengage.PARTNER_COLONIAL')) {
				if ($ticket->facility->is_gated_facility == '1') {
					if ($ticket->is_checkout == '1') {
						throw new ApiGenericException('Ticket has been already checked out.');
					}
				}
				if (in_array($ticket->is_offline_payment, [1, 2, 3]) || ($ticket->anet_transaction_id != '') && isset($ticket->transaction->method) && !in_array($ticket->transaction->method, [config('parkengage.APPLE_PAY'), config('parkengage.GOOGLE_PAY_HL')])) {
					throw new ApiGenericException('Ticket can not be validated as it is already paid by the user');
				}
			}
			// PIMS-11195 : Close Here...
			// if (in_array($ticket->is_offline_payment, [1, 2, 3]) || ($ticket->anet_transaction_id != '')) {
			// 	throw new ApiGenericException('Ticket can not be validated as it is already paid by the user');
			// }
			if (($ticket->discount_amount > '0.00')) {
				throw new ApiGenericException('The Ticket is already validated.');
			}
			$userValidation = '';

			if (Auth::check()) {
				if (Auth::user()->user_type == self::BUSINESS_CLERK) {
					$userValidation = UserValidateMaster::where('user_id', Auth::user()->id)->first();
				}
			}
			$this->user = User::where('id', $ticket->user_id)->first();
			$arrival_time = $ticket->checkin_time;
			$diff_in_hours = $ticket->length;
			if (!$diff_in_hours) {
				$diff_in_hours = $ticket->getCheckOutCurrentTime(true);
			}
			$this->log->info(' makeCheckout get diff_in_hours : ' . $diff_in_hours);
			$isMember = 0;
			// This function is changed for calculate rate based also on hourly  
			if (isset($ticket->facility->is_gated_facility) && $ticket->facility->is_gated_facility == '1') {
				if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {
					$rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
				} else {
					$rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE, $isMember);
				}
			} else {
				$rate['price'] = $ticket->parking_amount;
			}

			// new implementation
			$CCFee = $surcharge_fee = 0;
			if ($ticket->facility->is_gated_facility == '1') {
				if ($rate['price'] > 0) {
					$processing_fee   = $ticket->facility->getProcessingFee('0');
					$tax             = $ticket->facility->getTaxRate($rate);
				} else {
					$processing_fee  = "0.00";
					$tax             = "0.00";
				}
			} else {
				$processing_fee  = $ticket->processing_fee;
				$tax             = $ticket->tax_fee;
				// $CCFee           = $ticket->additional_fee 	+ $extendCCfee;
				$CCFee           = $ticket->additional_fee;
				// $surcharge_fee   = $ticket->surcharge_fee 	+ $extendSurchargeFee;
				$surcharge_fee   = $ticket->surcharge_fee;
			}

			/* Note: No use of below code  $rate['price'] already managed above
			if ($ticket->facility->is_gated_facility == '1') {
				$final_rate = $rate['price'] + $processing_fee + $tax + $ExtentTicketTotal + $CCFee + $surcharge_fee;
			} else {
				$final_rate = $rate['price'] + $processing_fee + $tax + $ExtentTicketTotal + $CCFee + $surcharge_fee;
			}
			*/
			$policy = BusinessPolicy::find($request->policy_id);
			if (!$policy) {
				throw new ApiGenericException('Policy id not found');
			}
			if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
				if ($policy->is_tax_applicable == '1') {
					$final_rate = $rate['price'] + $processing_fee + $tax + $ExtentTicketTotal + $CCFee + $surcharge_fee;
				} else {
					$final_rate = $rate['price'] + $ExtentTicketTotal + $CCFee + $surcharge_fee;
				}
			} else {
				$final_rate = $rate['price'] + $processing_fee + $tax + $ExtentTicketTotal + $CCFee + $surcharge_fee;
			}


			$length = isset($request->length) ? $request->length : 2;
			$request->request->add(['length' => $length]);
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
			$next_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time)->addHours($request->length);
			$carbon_date = Carbon::parse($arrival_time);
			$checkout_time = $carbon_date->addHours($request->length);
			if (Auth::check()) {
				$ticket->paid_by = Auth::user()->id;
			} else {
				if (isset($this->user->id)) {
					$ticket->paid_by = $this->user->id;
				}
			}
			$ticket->paid_date = date('Y-m-d H:i:s');

			$new_tax_amount = "0.00";
			$new_processing_fee = "0.00";
			$new_updated_price = "0.00";
			$new_discount_amount  = "0.00";

			if ($request->hours != '') {
				$ticket->policy_id = $request->policy_id;
				if (isset($userValidation) && !empty($userValidation) && $userValidation->max_remaining_hour != '') {
					if ($userValidation->max_remaining_hour == '0.00' || $userValidation->max_remaining_hour == '0') {
						throw new ApiGenericException("You can't validate the ticket as your monthly capping is exhausted.");
					}
					// if($ticket->facility->is_gated_facility !='1'){
					if ($ticket->length > $userValidation->max_remaining_hour) {
						throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket duration is greater than the remaining monthly capacity.');
					}
					// }   
					$paid_value = $request->hours;
					// for ungated facility city parking iNC when ticket lenght is less than policy hours for 
					// && ($ticket->facility->is_gated_facility != '1')
					if (($ticket->length < $this->request->hours)) {
						$paid_value = $ticket->length;
						$request->request->add(['hours' => $paid_value]);
					}
					$ticket->paid_type = 1;
					$ticket->paid_remark = Auth::user()->name . ' validated ' . $request->hours  . 'hour(s) from the total stay.';
					$ticket->paid_hour = $this->request->hours;
					if (isset($ticket->length) && $ticket->length != '') {
						// if ($this->request->hours > $ticket->length) {
						// 	throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket duration is less than hours.');
						// }
						//	$duration = $ticket->length - $this->request->hours;
						$duration = $this->request->hours;
						if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
							if ($policy->is_tax_applicable == '1') {
								$total_amount = $rate['price'] + $ExtentTicketTotal;
							} else {
								$total_amount = $final_rate;
							}
						} else {
							$total_amount = $final_rate;
						}

						$ticket_length_in_minutes = QueryBuilder::getLengthInMints($ticket->length);
						$validate_length_in_minutes = QueryBuilder::getLengthInMints($duration);
						$paid_amount = (($total_amount) / $ticket_length_in_minutes) * $validate_length_in_minutes;
						if ($duration == 0) {
							$ticket->paid_amount = $ticket->grand_total;
							$new_tax_amount = "0.00";
							$new_processing_fee = "0.00";
							$new_updated_price = "0.00";
							$new_discount_amount  = "0.00";
							//$ticket->grand_total = '0.00';
						} else {
							if ($ticket->facility->is_gated_facility != '1') {

								if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
									if ($policy->is_tax_applicable == '1') {
										if ($duration == $ticketLength) {
											$new_updated_price   = "0.00";
											$new_tax_amount      = "0.00";
											$new_processing_fee  = "0.00";
											$new_discount_amount = "0.00";
											$ticket->paid_amount  = $paid_amount;
										} else {

											$new_updated_price = $total_amount - $paid_amount;
											if ($new_updated_price > 0) {
												$rate['price'] = $new_updated_price;
											}
											$new_tax_amount       = $ticket->facility->getTaxRate($rate);
											$new_processing_fee   = $processing_fee;
											$ticket->paid_amount  = $paid_amount + $new_tax_amount + $new_processing_fee;
											$new_discount_amount  = $rate['price'];
										}
									} else {
										$new_tax_amount       = $ticket->facility->getTaxRate($rate);
										$new_processing_fee   = $processing_fee;
										$ticket->paid_amount  = $rate['price'];
										$new_discount_amount  = $paid_amount;
									}
								} else {
									$new_updated_price    = $total_amount - $paid_amount;
									if ($new_updated_price <= 0) {
										$new_updated_price = "0.00";
									}
									$ticket->paid_amount  = $paid_amount;
									$new_processing_fee   = $processing_fee;
									$new_tax_amount       = $tax;
									$new_discount_amount  = $paid_amount;
								}
							}
						}
					}
					if ($ticket->facility->is_gated_facility != '1') {
						$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();
						if ($usage) {
							$usage->max_remaining_hour = $usage->max_remaining_hour - $paid_value;
							$usage->save();
						}
					}
				} else {
					$paid_value = $request->hours;
					$ticket->paid_type = 1;
					$ticket->policy_id = $request->policy_id;

					if (($ticketLength < $this->request->hours)) {
						$paid_value = $ticketLength;
						$request->request->add(['hours' => $paid_value]);
					}

					if (Auth::check()) {
						$ticket->paid_remark = Auth::user()->name . ' validated ' . $request->hours . '  hour(s) from the total stay.';
					} else {
						$ticket->paid_remark = $this->user->name . ' validated ' . $request->hours . '  hour(s) from the total stay.';
					}
					// this code is comment not remove this code.
					// if($ticket->length =='' || $ticket->length ==NULL){
					// 	if($this->request->hours > $diff_in_hours){
					// 		throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket duration is less than hours.');
					// 	}
					// }
					$ticket->paid_hour = $this->request->hours;
					if (isset($ticket->length) && $ticket->length != '') {
						if ($this->request->hours > $ticketLength) {
							throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket duration is less than hours.');
						}
						// $duration = $ticket->length - $this->request->hours;
						$duration = $this->request->hours;
						//$total_amount = $final_rate;
						if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
							if ($policy->is_tax_applicable == '1') {
								$total_amount = $rate['price'];
							} else {
								$total_amount = $final_rate;
							}
						} else {
							$total_amount = $final_rate;
						}

						$ticket_length_in_minutes = QueryBuilder::getLengthInMints($ticketLength);
						$validate_length_in_minutes = QueryBuilder::getLengthInMints($duration);
						$paid_amount = (($total_amount) / $ticket_length_in_minutes) * $validate_length_in_minutes;
						//dd($paid_amount,$total_amount,$duration,$ticketLength,$ticket_length_in_minutes,$validate_length_in_minutes);
						if ($duration == 0) {
							$ticket->paid_amount = $ticket->grand_total;
							$new_tax_amount = "0.00";
							$new_processing_fee = "0.00";
							$new_updated_price = "0.00";
							$new_discount_amount  = "0.00";
						} else {
							if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
								$new_updated_price = $total_amount - $paid_amount;
								if ($policy->is_tax_applicable == '1') {
									if ($duration == $ticketLength) {
										$new_updated_price   = "0.00";
										$new_tax_amount      = "0.00";
										$new_processing_fee  = "0.00";
										$new_discount_amount = $paid_amount;
										$ticket->paid_amount  = $paid_amount;
									} else {
										if ($new_updated_price > 0) {
											$rate['price'] = $new_updated_price;
										}
										$new_tax_amount       = $ticket->facility->getTaxRate($rate);
										$new_processing_fee   = $processing_fee;
										$ticket->paid_amount  = $paid_amount;
										$new_discount_amount  = $paid_amount;
									}
									//dd($new_updated_price,$new_tax_amount,$new_processing_fee,$paid_amount,$new_discount_amount);
								} else {
									$new_tax_amount       = $tax;
									$new_processing_fee   = $processing_fee;
									$ticket->paid_amount  = $paid_amount;
									$new_discount_amount  = $paid_amount;
								}
							} else {
								$new_updated_price    = $total_amount - $paid_amount;
								if ($new_updated_price <= 0) {
									$new_updated_price = "0.00";
								}
								$ticket->paid_amount  = $paid_amount;
								$new_processing_fee   = $processing_fee;
								$new_tax_amount       = $tax;
								$new_discount_amount  = $paid_amount;
							}
						}
					}
				}
			} else if ($request->amount != '') {
				$ticket->policy_id = $request->policy_id;
				$ticket->paid_type = 2;
				if ($partner_id == config('parkengage.PARTNER_COLONIAL')) {
					$ticket->paid_amount = $request->amount;
				} else {
					if ($final_rate < $request->amount) {
						$new_tax_amount = $tax;
						$new_updated_price = $final_rate;
						$new_processing_fee   = $processing_fee;
						$ticket->paid_amount  = $final_rate;
					} else {
						$ticket->paid_amount = $request->amount;
						$new_updated_price = $final_rate;
						if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
							if ($policy->is_tax_applicable == '1') {
								$new_updated_price = $final_rate - ($processing_fee + $tax + $ExtentTicketTax + $ExtentTicketProcessingFee) - $request->amount;
								//dd($new_updated_price,$final_rate,$processing_fee,$tax,$ExtentTicketTax,$ExtentTicketProcessingFee,$request->amount,$rate['price']);
								if ($new_updated_price > 0) {
									$rate['price'] = $new_updated_price;
								}
								//$new_updated_price    = $request->amount;	
								if ($new_updated_price <= '0') {
									$new_updated_price = '0.00';
								}
								$rate['price'] = $final_rate - ($processing_fee + $tax + $ExtentTicketTax + $ExtentTicketProcessingFee);
								$new_tax_amount       = $ticket->facility->getTaxRate($rate);
								$new_processing_fee   = $processing_fee;
								$ticket->paid_amount  = $request->amount;
								$new_discount_amount  = $request->amount;
								//	dd($rate['price'],$final_rate,$processing_fee,$new_tax_amount,$new_updated_price);
							} else {
								$new_updated_price    = $new_updated_price - $request->amount;
								if ($new_updated_price <= '0') {
									$new_updated_price = '0.00';
								}
								$new_tax_amount       = $ticket->facility->getTaxRate($rate);
								$new_processing_fee   = $processing_fee;
								if ($new_updated_price > 0) {
									$rate['price'] = $new_updated_price;
								}
								$new_discount_amount  = $request->amount;
							}
						} else {
							$new_updated_price    = $new_updated_price - $request->amount;
							if ($new_updated_price <= '0') {
								$new_updated_price = '0.00';
							}
							$new_tax_amount       = $ticket->facility->getTaxRate($rate);
							$new_processing_fee   = $processing_fee;
							$new_discount_amount  = $request->amount;
						}
					}
				}
				$paid_value = $request->amount;
				if (Auth::check()) {
					$ticket->paid_remark = Auth::user()->name . ' validated $' . $request->amount . '  of the total amount. ';
				} else {
					$ticket->paid_remark = $this->user->name . ' validated $' . $request->amount . '  of the total amount. ';
				}
				if ($ticket->facility->is_gated_facility != '1') {
					if ($request->amount > $final_rate) {
						throw new ApiGenericException('Sorry, Your total amount should be greater than discount amount.');
					}
				}
				if (isset($userValidation) && !empty($userValidation) && $userValidation->max_remaining_dollar != '') {
					if ($userValidation->max_remaining_dollar == '0.00' || $userValidation->max_remaining_dollar == '0') {
						throw new ApiGenericException("You can't validate the ticket as your monthly capping is exhausted.");
					}
					// if($ticket->facility->is_gated_facility !='1'){
					if ($userValidation->max_remaining_dollar < $request->amount) {
						throw new ApiGenericException('Sorry, Your remaining amount is less than validation value of amount');
					}
					// }
					// here capping  only deduct for ungated case, in case of gated capping will be deduct payment time. 
					if ($ticket->facility->is_gated_facility != '1') {
						$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();
						if ($usage) {
							$usage->max_remaining_dollar = $usage->max_remaining_dollar - $paid_value;
							$usage->save();
						}
					}
				}
			} else if ($request->percent != '') {
				$ticket->max_validated_amount = $policy->discount_max;
				$ticket->policy_id = $request->policy_id;

				$total_amount = $final_rate;
				if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
					if ($policy->is_tax_applicable == '1') {
						$total_amount = $TotalParkingAmount;
					} else {
						$total_amount = $final_rate;
					}
				}

				$percentage_amount = ($total_amount * $request->percent) / 100;
				$paid_value = $percentage_amount;
				$ticket->paid_percentage = $request->percent;

				//only for Gated facility
				if ($ticket->facility->is_gated_facility != '1') {
					$ticket->paid_amount = $percentage_amount;
					if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
						if ($policy->is_tax_applicable == '1') {

							$new_updated_price = $total_amount - $percentage_amount;
							if ($new_updated_price <= 0) {
								$new_updated_price = "0.00";
							}
							if ($new_updated_price == "0.00") {
								$new_tax_amount       = "0.00";
								$new_processing_fee   = "0.00";
								$ticket->paid_amount = $percentage_amount;
								$new_discount_amount  = $paid_value;
								if ($new_discount_amount <= '0') {
									$new_discount_amount  = "0.00";
								}
								$ticket->total = $percentage_amount;
								$ticket->grand_total = $percentage_amount;
							} else {
								if ($new_updated_price > 0) {
									$rate['price'] = $total_amount - $percentage_amount;
								}
								$new_tax_amount       = $ticket->facility->getTaxRate($rate);
								$new_processing_fee   = number_format((($processing_fee * $request->percent) / 100), 2);
								$new_processing_fee   = number_format(($processing_fee - $new_processing_fee), 2);
								//dd($total_amount,$paid_value,$rate['price'],$new_tax_amount ,$new_processing_fee);
								$ticket->paid_amount = $percentage_amount + $new_tax_amount + $new_processing_fee;
								$new_discount_amount  = $paid_value;
								if ($new_discount_amount <= '0') {
									$new_discount_amount  = "0.00";
								}
							}
						} else {
							$new_updated_price = $rate['price'] - $percentage_amount;
							if ($new_updated_price <= 0) {
								$new_updated_price = "0.00";
							}
							if ($new_updated_price > 0) {
								$rate['price'] = $rate['price'];
							}
							$new_tax_amount       = $ticket->facility->getTaxRate($rate);
							$new_processing_fee   = $processing_fee;
							//dd($total_amount,$paid_value,$rate['price'],$new_tax_amount ,$new_processing_fee);

							$ticket->paid_amount = $percentage_amount;
							$new_discount_amount  = $paid_value;
							if ($new_discount_amount <= '0') {
								$new_discount_amount  = "0.00";
							}
						}
					} else {
						$new_updated_price = $rate['price'] - $percentage_amount;
						if ($new_updated_price <= 0) {
							$new_updated_price = "0.00";
						}
						if ($new_updated_price > 0) {
							$rate['price'] = $rate['price'];
						}
						$new_tax_amount       = $ticket->facility->getTaxRate($rate);
						$new_processing_fee   = $processing_fee;
						//dd($total_amount,$paid_value,$rate['price'],$new_tax_amount ,$new_processing_fee);

						$ticket->paid_amount = $percentage_amount;
						$new_discount_amount  = $paid_value;
						if ($new_discount_amount <= '0') {
							$new_discount_amount  = "0.00";
						}
					}
				}
				if (isset($userValidation) &&  !empty($userValidation) &&  $userValidation->max_remaining_dollar != '') {
					if ($userValidation->max_remaining_dollar == '0.00' || $userValidation->max_remaining_dollar == '0') {
						throw new ApiGenericException("You can't validate the ticket as your monthly capping is exhausted.");
					}
					// if($ticket->facility->is_gated_facility !='1'){
					if ($userValidation->max_remaining_dollar < $percentage_amount) {
						throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket price is greater than the remaining monthly capacity.');
					}
					// }
					$ticket->paid_type = 3;
					$percentage_amount = number_format($percentage_amount, 2);
					$ticket->paid_remark = Auth::user()->name . ' validated ' . $request->percent . '% of the total amount. ';
					if ($ticket->facility->is_gated_facility != '1') {
						$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();
						if ($usage) {
							$usage->max_remaining_dollar = $usage->max_remaining_dollar - $percentage_amount;
							$usage->save();
						}
					}
				} else {
					$ticket->paid_type = 3;
					$ticket->paid_percentage = $request->percent;
					if (Auth::check()) {
						$ticket->paid_remark = Auth::user()->name . ' validated ' . $request->percent . '% of the total amount. ';
					} else if (isset($request->enabled_ticket_creation_flow) && $request->enabled_ticket_creation_flow == '1') {
						$ticket->paid_remark = 'Auto-Validated';
					} else {
						$ticket->paid_remark = $this->user->name . ' validated ' . $request->percent . '% of the total amount. ';
					}
				}
			} else if ($request->days != '') {
				$ticket->policy_id = $request->policy_id;
				$hrs = ($request->days) * 24;
				if (isset($userValidation) && $userValidation->max_remaining_hour != '') {
					if ($userValidation->max_remaining_hour == '0.00' || $userValidation->max_remaining_hour == '0') {
						throw new ApiGenericException("You can't validate the ticket as your monthly capping is exhausted.");
					}
					if ($hrs > $userValidation->max_remaining_hour) {
						throw new ApiGenericException('Sorry, Your remaining amount should not be less than days.');
					}
					$paid_value = $request->days;
					$ticket->paid_type = 4;
					$ticket->paid_remark = Auth::user()->name . ' validated for ' . $request->days . ' days';
					$ticket->paid_days = $this->request->days;
					if (isset($ticket->length) && $ticket->length != '') {
						$ticket_days = ($ticket->length) / 24;
					} else {
						$ticket_days = ($diff_in_hours) / 24;
					}
					if ($ticket_days >= $this->request->days) {
						//	$duration = $ticket_days - $this->request->days;
						$duration = $this->request->days;
						$total_amount =  $final_rate;
						if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
							if ($policy->is_tax_applicable == '1') {
								$total_amount = $rate['price'];
							}
							$new_discount_amount  = $rate['price'];
						}

						$paid_amount = (($total_amount) / $ticket_days) * $duration;
						$ticket->paid_amount = $paid_amount;
						$grand_total = ($ticket->grand_total) - ($paid_amount);
						$new_updated_price = $total_amount - $paid_amount;
						if ($new_updated_price > 0) {
							$rate['price'] = $new_updated_price;
						}
						$new_tax_amount       = $ticket->facility->getTaxRate($rate);
						$new_processing_fee   = $processing_fee;
						if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
							if ($policy->is_tax_applicable == '1') {
								$ticket->paid_amount = $paid_amount + $new_tax_amount + $new_processing_fee;
							}
						}
					}
					if ($ticket->facility->is_gated_facility != '1') {
						$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();
						if ($usage) {
							$usage->max_remaining_dollar = $usage->max_remaining_dollar - $hrs;
							$usage->save();
						}
					}
				} else {
					$paid_value = $request->days;
					$ticket->paid_type = 4;
					$ticket->paid_remark = Auth::user()->name . ' validated for ' . $request->days . ' days';
					$ticket->paid_days = $this->request->days;
					if (isset($ticket->length) && $ticket->length != '') {
						$ticket_days = ($ticket->length) / 24;
					} else {
						$ticket_days = ($diff_in_hours) / 24;
					}
					if ($ticket_days >= $this->request->days) {
						//	$duration = $ticket_days - $this->request->days;
						$duration = $this->request->days;
						$total_amount = $final_rate;
						if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
							if ($policy->is_tax_applicable == '1') {
								$total_amount = $rate['price'];
							}
						}

						$paid_amount = (($total_amount) / $ticket_days) * $duration;
						$ticket->paid_amount = $paid_amount;

						$new_updated_price = $total_amount - $paid_amount;
						if ($new_updated_price > 0) {
							$rate['price'] = $new_updated_price;
						}
						$new_tax_amount       = $ticket->facility->getTaxRate($rate);
						$new_processing_fee   = $processing_fee;
						if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
							if ($policy->is_tax_applicable == '1') {
								$ticket->paid_amount = $paid_amount;
							}
							$new_discount_amount  = $rate['price'];
						}
					}
				}
			} else {
				$paid_value = '';
				$total_amount = '';
				$ticket->policy_id = $request->policy_id;
				if ($ticket->facility->is_gated_facility == '1') {
					if ($ticket->total != '') {
						$total_amount         = $final_rate;
						$new_tax_amount       = $tax;
						$new_processing_fee   = $processing_fee;
						$new_updated_price    = $ticket->parking_amount;
						$new_discount_amount  = $final_rate;
					} else {
						//$total_amount = isset($rate['price']) ? $rate['price'] : '0.00';
						//$total_amount = $total_amount + $processing_fee + $tax + $CCFee + $surcharge_fee;
						$total_amount        = $final_rate;
						$new_tax_amount      = $tax;
						$new_processing_fee  = $processing_fee;
						$new_updated_price   = $total_amount;
						$new_discount_amount = $final_rate;
					}
				} else {
					//$total_amount = $ticket->parking_amount;
					//$total_amount = $total_amount + $processing_fee + $tax + $CCFee + $surcharge_fee;

					if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
						if ($policy->is_tax_applicable == '1') {
							$total_amount         = $final_rate - ($tax + $processing_fee);
							$new_tax_amount       = "0.00";
							$new_processing_fee   = "0.00";
							$new_updated_price    = "0.00";
							$new_discount_amount  = $total_amount;
							$ticket->total 		  = $total_amount;
							$ticket->grand_total  = $total_amount;
						} else {
							$total_amount         = $final_rate;
							$new_tax_amount       = $tax;
							$new_processing_fee   = $processing_fee;
							$new_updated_price    = "0.00";
							$new_discount_amount  = $total_amount;
							$ticket->total 		  = $total_amount + $processing_fee + $tax;
							$ticket->grand_total  = $total_amount + $processing_fee + $tax;
						}
					} else {
						$total_amount         = $final_rate;
						$new_tax_amount       = $tax;
						$new_processing_fee   = $processing_fee;
						$new_updated_price    = "0.00";
						$new_discount_amount  = $total_amount;
					}
				}

				$ticket->paid_type = 0;
				if (Auth::check()) {
					$ticket->paid_remark = Auth::user()->name . ' validated  for total amount';
				} else {
					if (isset($this->user->name)) {
						$ticket->paid_remark = $this->user->name . ' validated  for total amount';
					}
				}
				if ($ticket->facility->is_gated_facility != '1') {
					$ticket->paid_amount = $total_amount;
					if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
						if ($policy->is_tax_applicable == '1') {
							$ticket->paid_amount = $total_amount;
						}
					}
				}
				if (isset($userValidation) && !empty($userValidation) &&  $userValidation->full_amount_remaining != '') {
					if ($userValidation->full_amount_remaining == '0.00' || $userValidation->full_amount_remaining == '0') {
						throw new ApiGenericException("You can't validate the ticket as your monthly capping is exhausted.");
					}
					// if($ticket->facility->is_gated_facility !='1'){
					if ($total_amount > $userValidation->full_amount_remaining) {
						throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket price is greater than the remaining monthly capacity.');
					}
					// }
					//$ticket->grand_total = '0.00';
					if ($ticket->facility->is_gated_facility != '1') {
						$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();
						if ($usage) {
							$usage->full_amount_remaining = $usage->full_amount_remaining - $total_amount;
							$usage->save();
						}
					}
				} else {
					//$ticket->grand_total = '0.00';
				}
			}
			if (isset($new_tax_amount) && !empty($new_tax_amount)) {
				$ticket->tax_fee = $new_tax_amount;
			}
			if ($new_processing_fee) {
				$ticket->processing_fee = $new_processing_fee;
			}
			$ticket->affiliate_business_id = $request->business_id;
			$ticket->save();
			$policy->is_policy_used = 1;
			$policy->save();

			if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
				if (!empty($new_tax_amount)) {
					$ticketdetail = new TicketAdditionalInfo();
					$ticketdetail->ticket_id = $ticket->id;
					$ticketdetail->ticket_type = 1;
					$ticketdetail->new_parking_amount = $new_updated_price;
					$ticketdetail->new_tax_amount = $new_tax_amount;
					$ticketdetail->new_processing_fee = $new_processing_fee;
					$ticketdetail->new_discount_amount = $new_discount_amount;
					$ticketdetail->save();
				}
			}

			$validateUsage = [];
			if (Auth::check()) {
				if (Auth::user()->user_type == self::BUSINESS_CLERK) {
					$validateUsage['user_id'] 			= Auth::user()->id;
					$validateUsage['ticket_id'] 		= $ticket->id;
					$validateUsage['validate_type'] 	= $ticket->paid_type;
					$validateUsage['validate_value'] 	= isset($paid_value) ? $paid_value : $ticket->paid_amount;
					$validateUsage['validate_amount'] 	= $ticket->paid_amount;
					$validateUsage['validate_remarks'] 	= $ticket->paid_remark;
					//dd($validateUsage);
					UserValidateUsages::create($validateUsage);
					// $usage = UserValidateMaster::where('user_id',Auth::user()->id)->where('month',date("m"))->where('year',date("Y"))->first();
					// if($usage){
					// 	$usage->monthly_remaining_amount = $usage->monthly_remaining_amount - $paid_value;
					// 	$usage->save();
					// }
					// if($userValidation){
					// 	$remainingAmount = $userValidation->monthly_remaining_amount - $ticket->paid_amount;
					// 	if($remainingAmount>0){
					// 		$userValidation->monthly_remaining_amount = $remainingAmount;	
					// 	}else{
					// 		$userValidation->monthly_remaining_amount = 0;
					// 	}
					// 	$userValidation->save();
					// }
				}
			}
			$this->log->info("Business Partner done payment ticket id {$ticket->id} ticket number {$ticket->ticket_number}  with browser {$_SERVER['HTTP_USER_AGENT']}");
			if ($ticket) {
				if (isset($this->user->phone) && $this->user->phone != '' && QueryBuilder::checkValidMobileLength($this->user->phone)) {  // checked condition of user exist for spothero by Ashutosh 28-08-2023
					if (Auth::check()) {
						$msg = "Thank you for visiting us. Your e-ticket has been validated.";
					} else {
						$msg = "Thank you for visiting us. Your e-ticket has been validated.";
					}
					try {
						$this->customeReplySms($msg, $this->user->phone);
					} catch (\Exception $e) {
						//echo "Error: " . $e->getMessage();
						$this->log->error($e->getMessage());
						return "success";
					}
				} elseif (isset($this->user->email) && $this->user->email != '') {
					Artisan::queue('send-validation-email', array('id' => $ticket->id));
				}

				if ($partner_id == config('parkengage.PARTNER_COLONIAL')  && $ticket->anet_transaction_id != '') {
					if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2')) {
						$ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
						$url = $ticket->facility->FacilityPaymentDetails->datacap_refund_url;
						$grandTotal = $ticket->paid_amount;
						//$amount = (config('parkengage.DATACAP_PAYMENT_ENV') == 'test') ? '3.00' : $grandTotal;
						$amount = $grandTotal;
						$ticketAmount = $final_rate;

						if (($ticketAmount > $amount) && ($amount > 0)) {
							$refundstatus = DatacapPaymentGateway::datacapPaymentTicketRefund($amount, $ticket, $ecommerce_mid, $url);
							//dd($refundstatus,$amount, $ecommerce_mid, $url);
							if ($refundstatus == null) {
								throw new ApiGenericException('Refund Can not be Initiated Now.');
							}
							if (Auth::check()) {
								$refund_by = Auth::user()->id;
							} else {
								if (isset($this->user->id)) {
									$refund_by = $this->user->id;
								} else {
									$refund_by = NULL;
								}
							}

							$this->log->info("Payment refund Log Datacap #:" . json_encode($refundstatus));
							$refund_remarks = 'Validated Amount $' . $request->amount . ' is Refunded of the total amount.';
							$refund_type = '2';
							if ($refundstatus['Status']  == 'Approved') {
								$ticket->refund_transaction_id = $refundstatus['RefNo'];
								$ticket->refund_status = "Refunded";
								$ticket->refund_amount = $grandTotal;
								$ticket->refund_type = $refund_type;
								$ticket->refund_date = date("Y-m-d H:i:s");
								$ticket->refund_remarks = $refund_remarks;
								$ticket->refund_by = $refund_by;
								//update ticket checkout after initiat refund or cancel
								$ticket->save();
							} else {
								$ticket->refund_status = "DECLINED";
								$ticket->refund_amount = $grandTotal;
								$ticket->refund_type = $refund_type;
								$ticket->refund_date = date("Y-m-d H:i:s");
								$ticket->refund_remarks = $refund_remarks;
								$ticket->refund_by = $refund_by;
								$ticket->save();
								$this->log->info("Payment refund fail to Booking Id #:" . $ticket->ticket_number . "--" . json_encode($refundstatus));
								throw new ApiGenericException('Refund can not be initiated.');
							}
						} else {
							$this->log->info("Payment refund not match the condition for to Booking Id #:" . $ticket->ticket_number . "--Ticket Amount--" . json_encode($ticketAmount) . "--Refund Amount--" . json_encode($amount));
						}
					}
				}

				$clerkLimit = NULL;
				if (Auth::check()) {
					if (Auth::user()->user_type == self::BUSINESS_CLERK) {
						$clerkLimit = Auth::user()->clerkLimit;
					}
					$ticket->clerk_limit = $clerkLimit;
				}
				if ($ticket->facility->is_gated_facility == '1') {
					$priceBreakup = $ticket->priceBreakUp($rate);
				} else {
					$priceBreakup =  $ticket->unGatedPriceBreakUp($rate, $ticket->facility, 'get');
				}
				if (isset($priceBreakup['payable_amount'])) {
					$ticket->payable_amount  = $priceBreakup['payable_amount'];
				}
				if (isset($priceBreakup['amount_paid'])) {
					$ticket->amount_paid  = $priceBreakup['amount_paid'];
				}
				//code change by vikrant for nitesh validation app 08-04-2025
				if ($ticket->facility->is_gated_facility == '1' && (isset($priceBreakup['paid_amount']) || $priceBreakup['paid_amount'] > 0)) {
					$ticket->paid_amount  = $priceBreakup['paid_amount'];
				}
				if ($partner_id != config('parkengage.PARTNER_COLONIAL') && (!isset($request->enabled_ticket_creation_flow) || $request->enabled_ticket_creation_flow == '0')) {
					//relsease pre-auth amount after validate
					if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1')) {
						# planet has no implementation for preauth amount release as it charging at the same time
					} else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2')) {
						$this->releasePreAuthAmountDatacap($ticket);
					} else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
						$preAuthresult  = $this->releasePreAuthAmountHeartland($ticket);
						if ($preAuthresult) {
							$preAuthresult->ticket_id = $ticket->id;
							$preAuthresult->save();
						}
					}
				}

				return $ticket;
			}
			//$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
		} else {
			throw new ApiGenericException('Ticket has been already validated.');
		}
	}


	//preauth release functions start here

	public function releasePreAuthAmountDatacap($ticket)
	{
		try {
			$this->logPreAuthAmount->info("Processing Ticket: " . json_encode($ticket));

			// Validate Ticket and Facility Payment Details
			if (!$ticket || !$ticket->facility || !$ticket->facility->FacilityPaymentDetails) {
				$this->logPreAuthAmount->error("Invalid ticket or missing facility payment details.");
				throw new ApiGenericException('Amount Validation Failed.');
			}

			// Fetch Datacap transaction record
			$cardCheck = DatacapTransaction::whereNull('deleted_at')
				->where('ticket_id', $ticket->id)
				->where('is_payment_complete', '0')
				->first();

			if (!$cardCheck) {
				$this->logPreAuthAmount->error("No valid Datacap transaction found for Ticket ID: {$ticket->id}");
				throw new ApiGenericException('Amount Validation Failed!');
			}

			// Extract Facility Payment Details
			$paymentDetails = $ticket->facility->FacilityPaymentDetails;
			$mid = $paymentDetails->datacap_ecommerce_mid ?? null;
			$url = $paymentDetails->datacap_preauth_url ?? null;

			if (empty($mid) || empty($url)) {
				$this->logPreAuthAmount->error("Missing MID or URL for the facility.");
				throw new ApiGenericException('Amount Validation Failed. MID missing!');
			}

			$rate['price'] = $ticket->parking_amount;
			$priceBreakup = $ticket->unGatedPriceBreakUp($rate, $ticket->facility, 'get');
			$payAmount = $priceBreakup['amount_paid'];

			$total = round((float) $priceBreakup['total'], 2);
			$paid = round((float) $priceBreakup['paid_amount'], 2);

			$partialReleaseAmount = ($total === $paid)
				? $priceBreakup['paid_amount']
				: $total - $paid;
			$finalAmount = $payAmount;


			$this->logPreAuthAmount->info(json_encode([
				"Final Amount" => $finalAmount,
				"Partial Release Amount" => $partialReleaseAmount
			]));

			// Prepare Data for Payment
			$tranId = $cardCheck->ref_id ?? null;
			$token = $cardCheck->token ?? null;

			if (!$tranId || !$token) {
				$this->logPreAuthAmount->error("Missing transaction ID or token.");
				throw new ApiGenericException('Amount validation failed!');
			}

			// Perform new pre-authorization if required
			if ($finalAmount > 0) {
				$preAuthData = [
					'Amount' => $finalAmount,
					'Token' => $token,
					'CardHolderID' => 'Allow_V2'
				];

				$preAuthResponse = $this->makePreAuthPaymentDataCap($preAuthData, $mid, $url);
				$this->logPreAuthAmount->info("New PreAuth Response: " . $preAuthResponse);

				$paymentResponse = json_decode($preAuthResponse, true);
				if (isset($paymentResponse['Status']) && $paymentResponse['Status'] === 'Approved') {
					// Prepare data for new transaction
					$data['Amount'] = $finalAmount;
					$paymentResponse['total'] = $finalAmount + ($this->request->total ?? 0);
					$this->request->name_on_card = $cardCheck->name ?? $cardCheck->card_name;
					$this->request->card_number = $cardCheck->card_last_four;
					$this->request->cardExpiry = $cardCheck->expiry;

					// Save New Record in Datacap Transaction table for New PreAuth
					$res = $this->makeDatacapTransaction($paymentResponse, $ticket);
					$res->ticket_id = $ticket->id;
					$res->save();
					$this->logPreAuthAmount->info("New PreAuth Saved Response: " . json_encode($res));

					// Mark the previous transaction as deleted
					$cardCheck->update([
						'deleted_at' => date('Y-m-d H:i:s')
					]);
				} else {
					$this->logPreAuthAmount->error("New PreAuth failed: " . $preAuthResponse);
					throw new ApiGenericException('Amount validation failed!');
				}
			}

			// Release the previous pre-authorized amount
			if ($partialReleaseAmount > 0) {
				$releaseData = ['Amount' => $partialReleaseAmount, 'Token' => $token];
				$paymentReleaseResponse = DatacapPaymentGateway::makeVoidPaymentChargeDataCap($releaseData, $mid, $tranId, $url);

				$this->logPreAuthAmount->info("PreAuth Payment Partial Release Response: " . json_encode($paymentReleaseResponse));

				$paymentReleaseData = json_decode($paymentReleaseResponse, true);
				if (!isset($paymentReleaseData['Status']) || $paymentReleaseData['Status'] !== 'Approved') {
					$this->logPreAuthAmount->error("PreAuth Release failed: " . $paymentReleaseResponse);
				}
				// Save transaction and log response
				$this->saveVoidTransactions($paymentReleaseResponse, $ticket);
			}
		} catch (\Exception $e) {
			$this->logPreAuthAmount->error("Error in releasePreAuthAmountDatacap: " . $e->getMessage());
		}
	}


	public function makePreAuthPaymentDataCap($data, $mid, $url)
	{
		$this->logPreAuthAmount->info("call datacap auth only curl");
		if ($mid == '') {
			$this->logPreAuthAmount->error("Payment Details Not Set for this facility.");
		}
		$vars = json_encode($data);
		$headers = [
			'Authorization: ' . $mid,
			'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
			'Content-Type: application/json',
			'Accept: application/json',
			'Accept-Language: en-US,en;q=0.5',
			'Cache-Control: no-cache'
		];

		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

		$response = curl_exec($curl);
		curl_close($curl);

		$this->logPreAuthAmount->info("datacap auth only curl response --- " . json_encode($response));
		return $response;
	}


	public function releasePreAuthAmountHeartland($pvticket, $requestedToatl = 0)
	{
		try {
			$ticket = Ticket::where('id', $pvticket->id)->first();
			if (!$this->isValidTicket($ticket)) {
				$this->log->error("Invalid ticket or missing facility payment details.");
				throw new ApiGenericException('Amount validation failed!');
			}

			$cardCheck = $this->getDatacapTransaction($ticket->id);
			if (!$cardCheck) {
				$this->log->error("No valid Datacap transaction found for Ticket ID: {$ticket->id}");
				throw new ApiGenericException('Amount validation failed!');
			}

			if (!$cardCheck->trans_id || !$cardCheck->token) {
				$this->log->error("Missing transaction ID or token.");
				throw new ApiGenericException('Amount validation failed!');
			}

			$rate['price'] = $ticket->parking_amount;
			$priceBreakup = $ticket->unGatedPriceBreakUp($rate, $ticket->facility, 'get');
			$payAmount = $priceBreakup['amount_paid'];

			$total = round((float) $priceBreakup['total'], 2);
			$paid = round((float) $priceBreakup['paid_amount'], 2);

			$partialReleaseAmount = ($total === $paid)
				? $priceBreakup['paid_amount']
				: $total - $paid;
			$finalAmount = $payAmount;

			$this->log->info("Final Amount Paid: {$finalAmount}");

			// Process new pre-auth if needed
			if ($finalAmount > 0) {
				$preAuthResponse = $this->processNewPreAuth($cardCheck, $ticket, $requestedToatl);

				if ($preAuthResponse && isset($preAuthResponse->responseMessage) && $preAuthResponse->responseMessage === 'APPROVAL') {
					// Mark the previous transaction as deleted
					$cardCheck->update([
						'deleted_at' => date('Y-m-d H:i:s')
					]);
				} else {
					$this->log->error("New PreAuth failed: " . json_encode($preAuthResponse));
					return $preAuthResponse;
				}
			}

			// Process partial void to release old transaction data
			$this->processPartialVoid($cardCheck, $ticket);

			return $preAuthResponse ?? true;
		} catch (\Throwable $e) {
			$this->log->error("Error in releasePreAuthAmountHeartland: " . $e->getMessage());
			return;
		}
	}

	private function isValidTicket($ticket)
	{
		return $ticket && $ticket->facility && $ticket->facility->FacilityPaymentDetails;
	}

	private function getDatacapTransaction($ticketId)
	{
		return DatacapTransaction::whereNull('deleted_at')
			->where('ticket_id', $ticketId)
			->where('is_payment_complete', '0')
			->whereNotNull('ticket_id')
			->first();
	}

	private function processPartialVoid($cardCheck, $ticket)
	{
		try {

			$paymentReleaseResponse = HeartlandPaymentGateway::makeVoidPaymentChargeHeartLand($cardCheck->trans_id, $ticket->facility);
			$this->logPreAuthAmount->info("PreAuth Payment Partial Release Response: " . json_encode($paymentReleaseResponse));

			if ($paymentReleaseResponse->responseCode == '00' && $paymentReleaseResponse->responseMessage == 'Success') {
				HeartlandPaymentGateway::saveVoidTransactions(json_encode($paymentReleaseResponse), $ticket, $cardCheck->trans_id);
				$cardCheck->delete(); // Delete Old Record and create new                
				return $paymentReleaseResponse;
			} else {
				$this->logPreAuthAmount->error("Error in voiding payment: " . $e->getMessage());
				throw new ApiGenericException("Payment Fail, something went wrong.", 500);
			}
		} catch (\Exception $e) {
			$this->logPreAuthAmount->error("Error in voiding payment: " . $e->getMessage());
		}
	}

	private function processNewPreAuth($cardCheck, $pvticket, $requestedToatl = 0)
	{
		$ticket = Ticket::where('id', $pvticket->id)->first();

		$rate['price'] = $ticket->parking_amount;
		$priceBreakup =  $ticket->unGatedPriceBreakUp($rate, $ticket->facility, 'get');

		$payAmount = $priceBreakup['amount_paid'] + $requestedToatl;

		$finalAmount = $payAmount;

		$request = new Request([
			'token'             => $cardCheck->token,
			'Amount'            => $finalAmount,
			'total'             => $finalAmount,
			'card_name'         => $cardCheck->card_name,
			'card_last_four'    => $cardCheck->card_last_four,
			'expiration'        => $cardCheck->expiry,
			'PreToken'          => $cardCheck->token,
			'partner_id'        => $cardCheck->partner_id,
			'ticket_id'         => $cardCheck->ticket_id,
		]);
		$this->log->info("PreAuth Request:", $request->toArray());
		$paymentResponse = HeartlandPaymentGateway::makePreAuthPaymentHeartland($request, $ticket->facility);

		$this->log->info("Re PreAuth response:" . json_encode($paymentResponse));

		if ($paymentResponse->responseMessage == 'APPROVAL') {
			$this->log->info("Processing approved transaction.");
			// Save new PreAuth Data for HL 
			$user_id = $cardCheck->user_id;
			$transactionStatus = HeartlandPaymentGateway::makeHeartlandPaymentTransaction($request, $paymentResponse, $user_id);
			return $transactionStatus;
		}
		return $paymentResponse;
	}


	public function saveVoidTransactions($paymentReleaseResponse, $ticket_details)
	{
		$paymentReleaseData = json_decode($paymentReleaseResponse);

		$user_id = $ticket_details->user_id;
		$total   = floatval($paymentReleaseData->Amount);
		$brand = str_replace('/', '', $paymentReleaseData->Brand);
		$card_last_four = substr($paymentReleaseData->Account, -4);

		$refundTransaction = new  RefundTransaction();
		if ($paymentReleaseData->Status == 'Approved') {
			$refundTransaction->sent = '1';
			$refundTransaction->user_id = $user_id;
			$refundTransaction->ip_address = \Request::ip();
			$refundTransaction->total = $total;

			$refundTransaction->description = "Datacap Void Sale Done User : " .  $user_id;
			$refundTransaction->card_type = $brand;
			$refundTransaction->ref_id = $paymentReleaseData->RefNo;
			$refundTransaction->method = "card";
			$refundTransaction->payment_last_four = isset($card_last_four) ? $card_last_four : '0';
			$refundTransaction->auth_code = $paymentReleaseData->AuthCode;
			$refundTransaction->response_code = $paymentReleaseData->ReturnCode;
			$refundTransaction->response_message = $paymentReleaseData->TranCode;
			$refundTransaction->invoice_number = $paymentReleaseData->InvoiceNo;
			$refundTransaction->anet_trans_id = $paymentReleaseData->InvoiceNo;
			$refundTransaction->status_message = $paymentReleaseData->Status;
			$refundTransaction->reference_key = $ticket_details->ticket_number;
			$refundTransaction->created_at = date("Y-m-d H:i:s");
			$refundTransaction->save();
			$this->logPreAuthAmount->info("PreAuth Payment Release Response Save -- " . json_encode($refundTransaction));
			return $refundTransaction;
		} else {
			$this->logPreAuthAmount->info("PreAuth Payment Release Response Failed");
			return true;
		}
	}

	//preauth release function end here
	/*
	public function Backup_11_01_2025makeCheckout(Request $request)
	{
		$this->log->info('makeCheckout Request : ' . json_encode($request->all()));
		if (Auth::check()) {
			if (!Auth::user()) {
				throw new UserNotAuthorized("Invalid User!");
			}
			if (Auth::user()->status == '0') {
				throw new UserNotAuthorized("You dont have permission to log in. Please contact your Admin for further details.");
			}
		}
		$ticket = Ticket::with(['user', 'facility'])->where('ticket_number', $request->ticket_number)->whereNull('paid_by')->whereNull('reservation_id')->whereNull('user_pass_id')->whereNull('event_id')->whereNull('event_user_id')->whereNull('permit_request_id')->first();
		//In Ungated facility if checkout time is cross then ticket should be validated.
		if (!$ticket) {
			throw new ApiGenericException("There is no active check-in for this search criteria.");
		}


		//validate clerk remaining usages.
		if ($ticket) {
			$this->setCustomTimezone($ticket->facility_id);

			if ($ticket->facility->is_gated_facility == '1') {
				if ($ticket->is_checkout == '1') {
					throw new ApiGenericException('Ticket has been already checked out.');
				}
			}
			
			if (in_array($ticket->is_offline_payment, [1, 2, 3]) || ($ticket->anet_transaction_id != '')) {
				throw new ApiGenericException('Ticket can not be validated as it is already paid by the user');
			}

			if (($ticket->discount_amount > '0.00')) {
				throw new ApiGenericException('The Ticket is already validated.');
			}

			$userValidation = '';
			
			if (Auth::check()) {
				if (Auth::user()->user_type == self::BUSINESS_CLERK) {
					$userValidation = UserValidateMaster::where('user_id', Auth::user()->id)->first();
				}
			}

			$this->user = User::where('id', $ticket->user_id)->first();
			$arrival_time = $ticket->checkin_time;
			$diff_in_hours = $ticket->length;

			if (!$diff_in_hours) {
				$diff_in_hours = $ticket->getCheckOutCurrentTime(true);
			}
			$this->log->info(' makeCheckout get diff_in_hours : ' . $diff_in_hours);
			$isMember = 0;
			$rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
			$facilityFee = Facility::find($ticket->facility_id);
			$processing_fee = $facilityFee->processing_fee;
			//tax rate type 0=>amount and 1=>percentage
			$tax = 0;
			if ($facilityFee->tax_rate_type == 0) {
				$tax = $facilityFee->tax_rate;
			} else {
				$tax = number_format((($rate['price'] * $ticket->facility->tax_rate) / 100), 2);;
			}
			$final_rate = $rate['price'] + $processing_fee + $tax;

			$request->length = isset($request->length) ? $request->length : 2;
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
			$next_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time)->addHours($request->length);
			$carbon_date = Carbon::parse($arrival_time);
			$checkout_time = $carbon_date->addHours($request->length);
			if (Auth::check()) {
				$ticket->paid_by = Auth::user()->id;
			} else {
				$ticket->paid_by = $this->user->id;
			}
			$ticket->paid_date = date('Y-m-d H:i:s');

			$policy = BusinessPolicy::find($request->policy_id);
			if (!$policy) {
				throw new ApiGenericException('Policy id not found');
			}

			if ($request->hours != '') {
				//dd($userValidation);
				$ticket->policy_id = $request->policy_id;
				if (isset($userValidation) && !empty($userValidation) && $userValidation->max_remaining_hour != '') {

					if ($userValidation->max_remaining_hour == '0.00' || $userValidation->max_remaining_hour == '0') {
						throw new ApiGenericException('This ticket can not be validated as your monthly capping is exhausted.');
					}
					if ($ticket->facility->is_gated_facility != '1') {
						if ($userValidation->max_remaining_hour < $this->request->hours) {
							throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket duration is greater than the remaining monthly capacity.');
						}
					}
					$paid_value = $request->hours;

					$ticket->paid_type = 1;
					$ticket->paid_remark = Auth::user()->name . ' validated ' . $request->hours  . 'hour(s) from the total stay.';
					$ticket->paid_hour = $this->request->hours;
					if (isset($ticket->length) && $ticket->length != '') {
						if ($this->request->hours > $ticket->length) {
							throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket duration is less than hours.');
						}

						$duration = $ticket->length - $this->request->hours;
						//$total_amount = isset($rate['price']) ? $rate['price']:'0.00';
						$total_amount = $final_rate;
						$paid_amount = (($total_amount) / $ticket->length) * $duration;

						if ($duration == 0) {
							$ticket->paid_amount = $ticket->grand_total;
							//$ticket->grand_total = '0.00';
						} else {
							if ($ticket->facility->is_gated_facility != '1') {
								$ticket->paid_amount = $paid_amount;
							}
						}
					}

					$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();

					if ($usage) {

						$usage->max_remaining_hour = $usage->max_remaining_hour - $paid_value;
						$usage->save();
					}
				} else {
					$paid_value = $request->hours;
					$ticket->paid_type = 1;
					$ticket->policy_id = $request->policy_id;
					if (Auth::check()) {
						$ticket->paid_remark = Auth::user()->name . ' validated ' . $request->hours . '  hour(s) from the total stay.';
					} else {
						$ticket->paid_remark = $this->user->name . ' validated ' . $request->hours . '  hour(s) from the total stay.';
					}
					// this code is comment not remove this code.
					// if($ticket->length =='' || $ticket->length ==NULL){

					// 	if($this->request->hours > $diff_in_hours){
					// 		throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket duration is less than hours.');
					// 	}
					// }

					$ticket->paid_hour = $this->request->hours;
					if (isset($ticket->length) && $ticket->length != '') {
						if ($this->request->hours > $ticket->length) {
							throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket duration is less than hours.');
						}
						$duration = $ticket->length - $this->request->hours;
						$total_amount = $final_rate;
						$paid_amount = (($total_amount) / $ticket->length) * $duration;

						if ($duration == 0) {
							$ticket->paid_amount = $ticket->grand_total;
							//$ticket->grand_total = '0.00';
						} else {
							$ticket->paid_amount = $paid_amount;
						}
					}
				}
			} else if ($request->amount != '') {
				$ticket->policy_id = $request->policy_id;
				$ticket->paid_type = 2;
				$ticket->paid_amount = $request->amount;
				$paid_value = $request->amount;
				if (Auth::check()) {
					$ticket->paid_remark = Auth::user()->name . ' validated $' . $request->amount . '  of the total amount. ';
				} else {
					$ticket->paid_remark = $this->user->name . ' validated $' . $request->amount . '  of the total amount. ';
				}
				if ($ticket->facility->is_gated_facility != '1') {
					if ($request->amount > $final_rate) {
						throw new ApiGenericException('Sorry, Your total amount should be greater than discount amount.');
					}
				}
				if (isset($userValidation) && !empty($userValidation) && $userValidation->max_remaining_dollar != '') {

					if ($userValidation->max_remaining_dollar == '0.00' || $userValidation->max_remaining_dollar == '0') {
						throw new ApiGenericException('This ticket can not be validated as your monthly capping is exhausted.');
					}
					if ($ticket->facility->is_gated_facility != '1') {
						if ($userValidation->max_remaining_dollar < $request->amount) {
							throw new ApiGenericException('Sorry, Your remaining amount is less than validation value of amount');
						}
					}
					//  if($final_rate >= $request->amount)
					//  {
					// 	$ticket->paid_remark = Auth::user()->name .' validated $'.$request->amount.'  of the total amount. ';	
					// }else{
					// 	throw new ApiGenericException('Sorry, Your total amount should be greater than discount amount.');
					//  }

					$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();

					if ($usage) {
						$usage->max_remaining_dollar = $usage->max_remaining_dollar - $paid_value;
						$usage->save();
					}
				}
			} else if ($request->percent != '') {

				$ticket->max_validated_amount = $policy->discount_max;
				$ticket->policy_id = $request->policy_id;
				if ($ticket->total != '') {
					$total_amount = $ticket->total;
				} else {
					$total_amount = $final_rate;
				}
				$percentage_amount = ($total_amount * $request->percent) / 100;
				$paid_value = $percentage_amount;
				$ticket->paid_percentage = $request->percent;
				//only for Gated facility
				if ($ticket->facility->is_gated_facility != '1') {
					$ticket->paid_amount = $percentage_amount;
				}
				if (isset($userValidation) &&  !empty($userValidation) &&  $userValidation->max_remaining_dollar != '') {
					if ($userValidation->max_remaining_dollar == '0.00' || $userValidation->max_remaining_dollar == '0') {
						throw new ApiGenericException('This ticket can not be validated as your monthly capping is exhausted.');
					}

					if ($ticket->facility->is_gated_facility != '1') {
						if ($userValidation->max_remaining_dollar < $percentage_amount) {
							throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket price is greater than the remaining monthly capacity.');
						}
					}

					$ticket->paid_type = 3;
					$percentage_amount = number_format($percentage_amount, 2);
					$ticket->paid_remark = Auth::user()->name . ' validated ' . $request->percent . '% of the total amount. ';
					$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();
					if ($usage) {
						$usage->max_remaining_dollar = $usage->max_remaining_dollar - $percentage_amount;
						$usage->save();
					}
				} else {
					$ticket->paid_type = 3;
					$ticket->paid_percentage = $request->percent;
					if (Auth::check()) {
						$ticket->paid_remark = Auth::user()->name . ' validated ' . $request->percent . '% of the total amount. ';
					} else {
						$ticket->paid_remark = $this->user->name . ' validated ' . $request->percent . '% of the total amount. ';
					}
				}
			} else if ($request->days != '') {
				$ticket->policy_id = $request->policy_id;
				$hrs = ($request->days) * 24;
				if (isset($userValidation) && $userValidation->max_remaining_hour != '') {
					if ($userValidation->max_remaining_hour == '0.00' || $userValidation->max_remaining_hour == '0') {
						throw new ApiGenericException('This ticket can not be validated as your monthly capping is exhausted.');
					}
					if ($hrs > $userValidation->max_remaining_hour) {
						throw new ApiGenericException('Sorry, Your remaining amount should not be less than days.');
					}
					$paid_value = $request->days;
					$ticket->paid_type = 4;
					$ticket->paid_remark = Auth::user()->name . ' validated for ' . $request->days . ' days';
					$ticket->paid_days = $this->request->days;
					if (isset($ticket->length) && $ticket->length != '') {
						$ticket_days = ($ticket->length) / 24;
					} else {
						$ticket_days = ($diff_in_hours) / 24;
					}

					if ($ticket_days >= $this->request->days) {
						$duration = $ticket_days - $this->request->days;
						$total_amount =  $final_rate;
						$paid_amount = (($total_amount) / $ticket_days) * $duration;
						$ticket->paid_amount = $paid_amount;

						$grand_total = ($ticket->grand_total) - ($paid_amount);
						if ($grand_total <= 0) {
							//	$ticket->grand_total = '0.00';
						} else {
							//	$ticket->grand_total = $grand_total;
						}
					}
					$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();
					if ($usage) {
						$usage->max_remaining_dollar = $usage->max_remaining_dollar - $hrs;
						$usage->save();
					}
				} else {
					$paid_value = $request->days;
					$ticket->paid_type = 4;
					$ticket->paid_remark = Auth::user()->name . ' validated for ' . $request->days . ' days';
					$ticket->paid_days = $this->request->days;

					if (isset($ticket->length) && $ticket->length != '') {
						$ticket_days = ($ticket->length) / 24;
					} else {
						$ticket_days = ($diff_in_hours) / 24;
					}
					if ($ticket_days >= $this->request->days) {
						$duration = $ticket_days - $this->request->days;
						$total_amount = $final_rate;
						$paid_amount = (($total_amount) / $ticket_days) * $duration;
						$ticket->paid_amount = $paid_amount;

						$grand_total = ($ticket->grand_total) - ($paid_amount);
						if ($grand_total <= 0) {
							//	$ticket->grand_total = '0.00';
						} else {
							//	$ticket->grand_total = $grand_total;
						}
					}
				}
			} else {
				$paid_value = '';
				$total_amount = '';
				$ticket->policy_id = $request->policy_id;
				if ($ticket->total != '') {
					$total_amount = $ticket->total;
				} else {
					$total_amount = isset($rate['price']) ? $rate['price'] : '0.00';
					$total_amount = $total_amount + $processing_fee + $tax;
				}
				$ticket->paid_type = 0;
				if (Auth::check()) {
					$ticket->paid_remark = Auth::user()->name . ' validated  for total amount';
				} else {
					$ticket->paid_remark = $this->user->name . ' validated  for total amount';
				}

				if ($ticket->facility->is_gated_facility != '1') {
					$ticket->paid_amount = $total_amount;
				}
				if (isset($userValidation) && !empty($userValidation) &&  $userValidation->full_amount_remaining != '') {

					if ($userValidation->full_amount_remaining == '0.00' || $userValidation->full_amount_remaining == '0') {
						throw new ApiGenericException('This ticket can not be validated as your monthly capping is exhausted.');
					}
					if ($ticket->facility->is_gated_facility != '1') {
						if ($total_amount > $userValidation->full_amount_remaining) {
							throw new ApiGenericException('Sorry, This ticket can not be validated as the ticket price is greater than the remaining monthly capacity.');
						}
					}
					//$ticket->grand_total = '0.00';
					$usage = UserValidateMaster::where('user_id', Auth::user()->id)->first();
					if ($usage) {
						$usage->full_amount_remaining = $usage->full_amount_remaining - $total_amount;
						$usage->save();
					}
				} else {
					//$ticket->grand_total = '0.00';
				}
			}
			$ticket->affiliate_business_id = $request->business_id;
			$ticket->save();
			$policy->is_policy_used = 1;
			$policy->save();
			$validateUsage = [];
			if (Auth::check()) {
				if (Auth::user()->user_type == self::BUSINESS_CLERK) {
					$validateUsage['user_id'] = Auth::user()->id;
					$validateUsage['ticket_id'] = $ticket->id;
					$validateUsage['validate_type'] = $ticket->paid_type;
					$validateUsage['validate_value'] = isset($paid_value) ? $paid_value : $ticket->paid_amount;
					$validateUsage['validate_amount'] = $ticket->paid_amount;
					$validateUsage['validate_remarks'] = $ticket->paid_remark;
					//dd($validateUsage);
					UserValidateUsages::create($validateUsage);

					// $usage = UserValidateMaster::where('user_id',Auth::user()->id)->where('month',date("m"))->where('year',date("Y"))->first();

					// if($usage){
					// 	$usage->monthly_remaining_amount = $usage->monthly_remaining_amount - $paid_value;
					// 	$usage->save();
					// }

					// if($userValidation){
					// 	$remainingAmount = $userValidation->monthly_remaining_amount - $ticket->paid_amount;
					// 	if($remainingAmount>0){
					// 		$userValidation->monthly_remaining_amount = $remainingAmount;	
					// 	}else{
					// 		$userValidation->monthly_remaining_amount = 0;
					// 	}
					// 	$userValidation->save();
					// }
				}
			}
			$this->log->info("Business Partner done payment ticket id {$ticket->id} ticket number {$ticket->ticket_number}  with browser {$_SERVER['HTTP_USER_AGENT']}");
			if ($ticket) {
				if (isset($this->user->phone) && $this->user->phone != '') {  // checked condition of user exist for spothero by Ashutosh 28-08-2023
					if (Auth::check()) {
						$msg = "Thank you for visiting us. Your e-ticket has been validated.";
					} else {
						$msg = "Thank you for visiting us. Your e-ticket has been validated.";
					}
					try {
						$this->customeReplySms($msg, $this->user->phone);
					} catch (\Exception $e) {
						//echo "Error: " . $e->getMessage();
						$this->log->error($e->getMessage());
						return "success";
					}
				}
				if ($ticket->facility->is_gated == '0') {
					Artisan::queue('email:touchless-parking-autogate-checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));
				}

				$clerkLimit = NULL;
				if (Auth::check()) {
					if (Auth::user()->user_type == self::BUSINESS_CLERK) {
						$clerkLimit = Auth::user()->clerkLimit;
					}
					$ticket->clerk_limit = $clerkLimit;
				}
				return $ticket;
			}

			//$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";



		} else {
			throw new ApiGenericException('Ticket has been already validated.');
		}
	}
	*/
	public function customeReplySms($msg, $phone, $imageURL = '')
	{
		if ($phone == '') {
			return "success";
		}
		$accountSid = env('TWILIO_ACCOUNT_SID');
		$authToken  = env('TWILIO_AUTH_TOKEN');
		$client = new Client($accountSid, $authToken);
		try {
			/*$imageBarcode = $this->generateBarcodeJpgNew("111");
              $imageBarcodeFileName = str_random(10) . '_qrcode.png';
            Storage::put($imageBarcodeFileName, $imageBarcode);
              $imageBarcodeFileName = str_random(10) . '_qrcode.png';
              Storage::put($imageBarcodeFileName, $imageBarcode);
              dd($imageBarcodeFileName, $imageBarcode);
              $data['bar_image_path'] = $imageBarcodeFileName;*/
			// Use the client to do fun stuff like send text messages!
			$client->messages->create(
				// the number you'd like to send the message to
				$phone,
				array(
					// A Twilio phone number you purchased at twilio.com/console
					'from' => env('TWILIO_PHONE'),
					// the body of the text message you'd like to send
					//'body' => "Fine"
					'body' => "$msg",
					//'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)
				)
			);
			$this->log->info("Message : {$msg} sent to $phone");
			return "success";
		} catch (RestException $e) {
			//echo "Error: " . $e->getMessage();
			$this->log->error($e->getMessage());
			return "success";
		}
	}

	public function setCustomTimezone($facility_id)
	{
		$facility = Facility::find($facility_id);
		$secret = OauthClient::where('partner_id', $facility->owner_id)->first();
		$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
		if (($facility) && ($facility->timezone != '')) {
			date_default_timezone_set($facility->timezone);
		} else {
			if ($partnerTimezone) {
				if ($partnerTimezone->timezone != '') {
					date_default_timezone_set($partnerTimezone->timezone);
				}
			}
		}
	}

	public function getValidatedTicketDetails(Request $request)
	{
		$this->log->info("get Ticket List" . json_encode($request->all()));
		$partner_id = Auth::User()->created_by;
		$user_id = 	Auth::User()->id;
		if (isset($request->id) && !empty($request->id)) {
			$facility = DB::table('user_facilities')->where('user_id', $request->id)->whereNull('deleted_at')->pluck('facility_id');
		}
		$ticket = Ticket::select('id', 'user_pass_id', 'user_id', 'facility_id', 'anet_transaction_id', 'ticket_number', 'rate_id', 'rate_description', 'is_checkin', 'is_checkout', 'total', 'grand_total', 'length', 'partner_id', 'checkin_time', 'checkout_time', 'parking_amount', 'processing_fee', 'tax_fee', 'additional_fee', 'license_plate', 'car_model', 'scan_date', 'is_closed', 'scan_date', 'payment_date', 'vehicle_id', 'paid_by', 'paid_amount', 'paid_remark', 'paid_date', 'paid_type', 'paid_hour', 'paid_days', 'paid_percentage', 'card_type', 'expiry', 'card_last_four', 'stop_parking_time', 'promocode', 'discount_hours', 'policy_id')
			->with(['facilityData', 'userData', 'transaction'])->where('paid_by', $user_id);
		if (($request->ticket_number != '') && ($request->license_plate != '') && ($request->phone != '') && ($request->card_number != '') && ($request->card_expiry != '') && ($request->checkin_date != '')) {
			$ticket = $ticket->Where('ticket_number', $request->ticket_number)->Where('license_plate', $request->license_plate)->WhereDate('checkin_time', '=', date('Y-m-d', strtotime($request->checkin_date)));
			$ticket = $ticket->WhereHas(
				'user',
				function ($query) use ($request) {
					$query->where('phone', 'like', "%{$request->phone}");
				}
			);
			$ticket = $ticket->Where('card_last_four', $request->card_number);
			$ticket = $ticket->Orwhere('expiry', $request->card_expiry);
		} elseif ($request->card_expiry != '' && $request->card_number != '' && $request->entry_time != '' && $request->exit_time != '') {
			$ticket = $ticket->Where('card_last_four', $request->card_number)
				->Where('expiry', $request->card_expiry);
			if (isset($user_type) &&  !empty($user_type)) {
				$ticket = $ticket->WhereHas(
					'user',
					function ($query) use ($request) {
						$query->where('user_type', '12');
					}
				);
			}
		} else if (($request->ticket_number != '') || ($request->license_plate != '') || ($request->phone != '') || ($request->card_number != '') || ($request->card_expiry != '') || ($request->checkin_date != '') || ($request->entry_time != '') || ($request->exit_time != '') || ($request->session_id != '') || ($request->user_id != '')) {
			if ($request->facility_id != '') {
				$ticket = $ticket->Where('facility_id', $request->facility_id);
			}
			if ($request->ticket_number != '') {
				$ticket = $ticket->Where('ticket_number', 'like', "%{$request->ticket_number}%");
			}
			if ($request->checkin_date != '') {
				$ticket = $ticket->WhereDate('checkin_time', '=', date('Y-m-d', strtotime($request->checkin_date)));
			}
			if ($request->license_plate != '') {
				$ticket = $ticket->Where('license_plate', 'like', "%{$request->license_plate}%");
			}
			if ($request->phone != '') {
				$ticket = $ticket->WhereHas(
					'user',
					function ($query) use ($request) {
						$query->where('phone', 'like', "%{$request->phone}%");
					}
				);
			}
			if (($request->card_number != '')) {
				$ticket = $ticket->Where('card_last_four', $request->card_number);
			}
			if (($request->card_expiry != '')) {
				$ticket = $ticket->Where('expiry', $request->card_expiry);
			}
			if ($request->session_id != '') {
				$ticket = $ticket->Where('session_id', $request->session_id);
				$ticket = $ticket->orWhere('checkout_session_id', $request->session_id);
			}
			if ($request->user_id != '') {
				$ticket = $ticket->where('user_id', $request->user_id);
			}
		}
		if (isset($facility) && !empty($facility)) {
			$ticket->whereIn('facility_id', $facility);
		}
		if ($request->entry_time != ''  && $request->exit_time != '') {
			$ticket = $ticket->where(function ($query) use ($request) {
				$query->WhereDate('checkin_time', '>=', date('Y-m-d', strtotime($request->entry_time)))->WhereDate('checkout_time', '<=', date('Y-m-d', strtotime($request->exit_time)));
			});
		}
		if ($request->facility_id != '') {
			$ticket = $ticket->where(function ($query) use ($request) {
				$query->where('facility_id', $request->facility_id);
			});
		}
		$ticket = $ticket->where(function ($query) use ($partner_id) {
			$query->where('partner_id', $partner_id);
		});
		if (isset($request->from_date) && $request->from_date != '') {
			$from_date = date('Y-m-d', strtotime($request->from_date));
			$to_date = date('Y-m-d', strtotime($request->to_date));
			$ticket = $ticket->whereDate('checkin_time', '>=', $from_date)->whereDate('checkin_time', '<=', $to_date);
		}
		$ticket = $ticket->orderBy("checkin_time", "desc")->paginate(20);
		if (!$ticket) {
			throw new ApiGenericException("There is no active check-in for this Ticket or Mobile No.");
		}
		// adding the price brakup from centralized approche by ashutosh 08-09-2023
		$ticketArray = $ticket->toArray();
		foreach ($ticketArray['data'] as $key => $val) {
			$ticketObje = Ticket::with(['reservation', 'facility', 'user', 'transaction'])->where('id', $val['id'])->first();
			$this->setCustomTimezone($ticketObje->facility_id);
			if ($val['facility_data']['is_gated_facility'] == '1') {
				$is_overstay = 0;
				if ($ticketObje->estimated_checkout != '') {
					$overstayExist = OverstayTicket::where("ticket_id", $ticketObje->id)->orderBy("id", "DESC")->first();
					if ($overstayExist) {
						if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
							$ticketArray['data'][$key]['payable_amount'] = "0.00";
							$ticketArray['data'][$key]['grand_total'] = $overstayExist->grand_total + $ticketObje->grand_total;
							return $ticketArray;
						} else {
							$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->payment_date);
							$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
							$is_overstay = 1;
							$ticketArray['data'][$key]['grand_total'] = $overstayExist->grand_total + $ticketObje->grand_total;
						}
					} else {
						if (strtotime($ticketObje->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
							// Sport hero if not in overstay then goes here
							$ticketArray['data'][$key]['payable_amount'] = "0.00";
							$ticketArray['data'][$key]['grand_total'] = $ticketObje->grand_total;
							return $ticketArray;
						} else {
							$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObje->estimated_checkout);
							$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
							$is_overstay = 1;
						}
					}
				}
				if ($is_overstay == 1) {
					$paymentDate = ($ticketObje->payment_date != '' ? $ticketObje->payment_date : $ticketObje->estimated_checkout);
					$diff_in_hours = $ticketObje->getCheckOutCurrentTime(true, $paymentDate);
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $paymentDate);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
					$rate = [];
					$isMember = 0;
					if ($ticketObje->facility->is_hourly_rate == '1' || $ticketObje->facility->is_hourly_rate == 1) {
						$this->log->info("getCheckinCheckoutDetails Get Price 111");
						$rate = $ticketObje->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
					} else {
						$this->log->info("confirmCheckout Get Price 222");
						$rate = $ticketObje->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
					}
					$this->log->error("search ticket OverStay : rate for hours  {$diff_in_hours} ");
					$overstayDetails = $this->getOverstayTicketDetails($ticketObje, $rate, $ticketObje->facility);
					$ticketArray['data'][$key]['overstay_amount'] = sprintf("%.2f", $overstayDetails->overstay_amount);
					$ticketArray['data'][$key]['payable_amount'] = sprintf("%.2f", $overstayDetails->payable_amount);
					$ticketArray['data'][$key]['overstay_tax_fee'] = sprintf("%.2f", $overstayDetails->overstay_tax_fee);
					$ticketArray['data'][$key]['overstay_processing_fee'] = sprintf("%.2f", $overstayDetails->overstay_processing_fee);
					$ticketArray['data'][$key]['is_overstay'] = '1';
					return $ticketArray;
				}
				$diff_in_hours = $ticketObje->getCheckOutCurrentTime(true);
				$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticketObje->checkin_time);
				$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
				$isMember = 0;
				if ($ticketObje->facility->is_hourly_rate == '1' || $ticketObje->facility->is_hourly_rate == 1) {
					$this->log->info("getCheckinCheckoutDetails Get Price 111");
					$rate = $ticketObje->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
				} else {
					$this->log->info("confirmCheckout Get Price 222");
					$rate = $ticketObje->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
				}
				if ($rate != false) {
					$pricebrackup = $ticketObje->priceBreakUp($rate);
				}
			} else {
				$rate['price'] = $ticketObje->parking_amount;
				$pricebrackup = $ticketObje->unGatedPriceBreakUp($rate, $ticketObje->facility, 'get');
			}
			if (isset($pricebrackup) && !empty($pricebrackup)) {
				$ticketArray['data'][$key]['amount_paid'] = $pricebrackup['amount_paid'];
				$ticketArray['data'][$key]['paid_amount'] = $pricebrackup['paid_amount'];
				$ticketArray['data'][$key]['payable_amount'] = sprintf("%.2f", $pricebrackup['payable_amount']);
				$ticketArray['data'][$key]['parking_amount'] = sprintf("%.2f", $pricebrackup['parking_amount']);
				$ticketArray['data'][$key]['processing_fee'] = $pricebrackup['processing_fee'];
				$ticketArray['data'][$key]['discount_amount'] = $pricebrackup['discount_amount'];
				// $ticketArray['data'][$key]['overstay'] = $pricebrackup['overstay_amount'];
				$ticketArray['data'][$key]['tax_rate'] = $pricebrackup['tax_rate'];
				$ticketArray['data'][$key]['tax_fee'] = $pricebrackup['tax_rate'];
			}

			if (!empty($val['user_pass_id'])) {
				$ticketArray['data'][$key]['passdata'] = DB::table('user_passes')->select('email', 'start_time', 'end_time', 'pass_code')->where('id', $val['user_pass_id'])->first();
			}
		}
		return $ticketArray;
	}

	public function getScanTicketDetails(Request $request)
	{
		$this->log->info('getTicketDetails ' . json_encode($request->all()));
		if (!Auth::user()) {
			throw new UserNotAuthorized("Invalid User!");
		}
		$this->log->info('Login User Details : ' . json_encode(Auth::user()->id));
		if (Auth::user()->status == '0') {
			throw new UserNotAuthorized("You dont have permission to log in. Please contact your Admin for further details.");
		}
		if (Auth::user()->user_type == self::BUSINESS_CLERK) {
			$clerkLimit = Auth::user()->clerkLimit;
			$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
		}
		if (Auth::user()->user_type == self::BUSINESS_USER) {
			$facilities = DB::table('business_facility_policy')->where('business_id', Auth::user()->business_id)->pluck('facility_id');
		}
		if (Auth::user()->user_type == self::SUBORDINATE) {
			$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
		}
		if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
			$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
		}
		if (Auth::user()->user_type == self::PARTNER) {
			$facilities = DB::table('facilities')->whereNull('deleted_at')->where('owner_id', Auth::user()->id)->pluck('id');
		}
		if (Auth::user()->user_type == self::SUPERADMIN) {
			$facilities = DB::table('facilities')->whereNull('deleted_at')->where('owner_id', $request->partner_id)->pluck('id');
		}
		if ($request->url_data) {
			$url = $request->url_data;
			parse_str(parse_url($url)['query'], $params);
			$url_path = parse_url($url, PHP_URL_PATH);
			if ($url_path != self::URL_PATH) {
				throw new ApiGenericException("Invalid Request Parameter.");
			}
			//dd('stop');
			//dd($parseURL);
			if (isset($params['ticket_number']) && !empty($params['ticket_number'])) {
				$ticket_number = $params['ticket_number'];
			} else {
				throw new ApiGenericException("Invalid Request Parameter.");
			}
			if ($facilities) {
				$ticket = Ticket::with(['userData', 'transaction', 'facilityData'])->whereIn('facility_id', $facilities)->whereNull('reservation_id')->whereNull('user_pass_id')->whereNull('event_user_id')->whereNull('permit_request_id')->where('is_checkin', '1');
			} else {
				$ticket = Ticket::with(['userData', 'transaction', 'facilityData'])->whereIn('facility_id', $facilities)->whereNull('reservation_id')->whereNull('user_pass_id')->whereNull('permit_request_id')->where('is_checkin', '1');
			}
			if ($ticket_number != '') {
				$ticket = $ticket->Where('ticket_number', $ticket_number);
			}
			$ticket = $ticket->orderBy("id", "desc")->first();
			if (!$ticket) {
				throw new ApiGenericException("There is no active check-in for this search criteria.");
			}
			if ($ticket->paid_type != '9') {
				throw new ApiGenericException("Ticket Already Validated.");
			}
			if ($ticket->is_checkout == '1' || $ticket->is_checkout == 1) {
				throw new ApiGenericException("No active check-in against this ticket.");
			}
			if ($ticket->paid_type != '9') {
				throw new ApiGenericException("Ticket Already Validated.");
			}
			return $ticket;
		} else {
			throw new ApiGenericException("Invalid Request Parameter.");
		}
	}

	public function getOverstayTicketDetails($ticket, $rate, $facility)
	{
		// vikrant : 02-07-2024
		$priceBreakUp = $ticket->priceBreakUp($rate);
		$this->log->info("search ticket number priceBreakUp " . json_encode($priceBreakUp));
		if ($ticket->reservation_id != '') {
			$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
			// $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
			// Vijay : 24-09-2023 : To Handel Tentative CHeckout time in case of  Reservation we always showing current datetime.
			if (!empty($ticket->reservation_id)) {
				$ticket->estimated_checkout = date("Y-m-d H:i:s");
				$ticket->checkout_datetime = date("Y-m-d H:i:s");
				$ticket->checkout_time = date("Y-m-d H:i:s");
				$endDate = date("Y-m-d H:i:s");
			} else {
				$endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
			}
			$ticket['amount_paid'] = $ticket->grand_total;
			$this->log->info("reservation get Diff in Hours : ");
			$ticket->payable_amount = $priceBreakUp['payable_amount'];
			$ticket->overstay_amount = $priceBreakUp['overstay_amount'];
			$ticket->overstay_tax_fee  = $priceBreakUp['tax_rate'];
			$ticket->overstay_processing_fee  = $priceBreakUp['processing_fee'];
			$ticket->tax_rate = $priceBreakUp['tax_rate'];
			$ticket['is_overstay'] = '1';
			$ticket['rate'] = [];
			return $ticket;
		}
		$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
		$endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
		$ticket['checkout_time'] = date('Y-m-d H:i:s', strtotime($endDate));
		$ticket['diff_in_days'] = $startDate->diffInDays($endDate);
		$ticket['diff_in_hours'] = $startDate->copy()->addDays($ticket['diff_in_days'])->diffInHours($endDate);
		$ticket['diff_in_minutes'] = $startDate->copy()->addDays($ticket['diff_in_days'])->addHours($ticket['diff_in_hours'])->diffInMinutes($endDate);
		$ticket['amount_paid'] = $ticket->grand_total;
		if ($ticket->payment_date) {
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date);
			$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->payment_date);
			$this->log->info("overstay get Diff in Hours : {$diff_in_hours}");
		}
		// $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, 0);
		// Vijay : 02-11-2023 To handle Overnight Overstay
		if ($priceBreakUp['payable_amount'] == 0) {
			$ticket->payable_amount = "0.00";
			$ticket->overstay_amount = "0.00";
			$ticket['is_overstay'] = '0';
			$ticket['rate'] = $rate;
			return $ticket;
		}
		$payableAmount = $priceBreakUp['payable_amount'];
		$overstayAmount = $priceBreakUp['overstay_amount'];
		$ticket->payable_amount = number_format($payableAmount, 2);
		$ticket->overstay_amount = number_format($overstayAmount, 2);
		$ticket->overstay_tax_fee  = $priceBreakUp['tax_rate'];
		$ticket->overstay_processing_fee  = $priceBreakUp['processing_fee'];
		$ticket->tax_rate = $priceBreakUp['tax_rate'];
		$ticket['is_overstay'] = '1';
		$ticket['rate'] = $rate;
		return $ticket;
	}


	public function applyPass(Request $request)
	{
		try {
			$this->user = Auth::user();
			if ($request->header('X-ClientSecret') != '') {
				$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
				if (!$secret) {
					throw new NotFoundException('No partner found.');
				}
			}

			// Validate facility
			$facility = Facility::where('id', $request->facility_id)->first();
			if (!$facility) {
				return response()->json([
					'status' => 'error',
					'message' => 'Invalid garage.',
				], 400);
			}
			$this->facility = $facility;

			$passes = $request->passes;
			$responsePasses = [];
			$ticket = '';

			foreach ($passes as $pass) {

				//PIMS-12614 06-02-2025
				$startdate = new DateTime(); // Current date
				$enddate = new DateTime(date('Y-m-d', strtotime($pass['end_time']))); // Example end date
				$interval = $startdate->diff($enddate); // Calculate the difference between the two dates

				// Get the number of days from the interval
				$days = $interval->days;

				$ticket = Ticket::with(['user'])->where('ticket_number', $pass['ticket_number'])->first();
				if (!$ticket) throw new ApiGenericException("There is no ticket for {$pass['ticket_number']}");
				$this->facilityFee = $facility->processing_fee;

				$request->request->add(['start_date' => date('Y-m-d')]);
				$request->request->add(['end_date' => $pass['end_time']]);
				$request->request->add(['start_time' => date('H:i:s')]);
				$request->request->add(['end_time' => $pass['end_time']]);

				if (strtotime(date("Y-m-d", strtotime($request->start_date))) < strtotime(date("Y-m-d"))) {
					throw new ApiGenericException("Sorry! User Pass date can not be in past date.");
				}
				$passCheck = '';
				$passData = Rate::where('facility_id', $facility->id)->where('rate_type_id', '7')->first();

				if (!$passData) {
					throw new ApiGenericException("Invalid Pass Rate.");
				}
				$this->passData = $passData;

				$isVehicle = PermitVehicle::where('license_plate_number', $ticket->license_plate)->first();

				if (isset($isVehicle) && !empty($isVehicle)) {
					if (!empty($ticket->user) && !empty($facility) && !empty($isVehicle)) {
						$passCheck = UserPass::where('facility_id', $facility->id)->where('phone', $ticket->user->phone)->where('vehicle_id', $isVehicle->id)->where('end_date', '>=', $request->start_date)->first();
					}
					if (isset($passCheck) && !empty($passCheck)) {
						if ($passCheck->remaining_days == '0') {
						} else {
							$userdata = [
								'error_msg' => 'A user pass is already issued for the for this user.'
							];
							return $userdata;
						}
					}
				}

				// Get country Code
				$this->countryCode = QueryBuilder::appendCountryCode();
				if (!empty($pass['phone'])) {
					$phoneWithCountryCode = $this->countryCode . $pass['phone'];
					$existPhone = User::where('phone', $phoneWithCountryCode)
						->where('created_by', Auth::user()->id)
						->first();

					$password = rand(100000, 999999);

					if ($existPhone) {
						if (empty($existPhone->password)) {
							$existPhone->password = Hash::make($password);
							$existPhone->save();
						}
						$userdata = $existPhone;
					} else {
						$userdata = User::create([
							'name' => 'Guest User',  // Default name to avoid empty field
							'email' => null,         // Set null instead of an empty string if optional
							'phone' => $phoneWithCountryCode,
							'password' => Hash::make($password),
							'anon' => true,
							'user_type' => 5,
							'created_by' => Auth::user()->id,
						]);
					}
				}
				if (!empty($userdata)) {
					$request->merge([
						'mobile' => $userdata->phone,
						'email' => $userdata->email ?? '',
					]);
				} else if (!empty($ticket->user)) {
					$request->merge([
						'mobile' => $ticket->user->phone,
						'email' => $ticket->user->email ?? '',
					]);
				}


				if (!isset($facility) || $facility->active == 0) {
					throw new ApiGenericException(
						'Something went wrong and we were not able to complete your transaction. Please return to the booking page and try again.'
					);
				}


				$this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();

				if (isset($pass['end_time']) && !empty($pass['end_time'])) {
					$totalAmount = 0;
					$processing_fee = 0;
					$processing_fee = isset($this->facilityFee->val) ? $this->facilityFee->val : 0;
					$totalAmount = $this->passData->price + $processing_fee;
					$currentDateTime = Carbon::now();
					// Create our monthly request object
					$PassRequest = new UserPass();
					$PassRequest->facility_id = $this->facility->id;
					$PassRequest->user_id = isset($userdata) && isset($userdata->id) ? $userdata->id : ($ticket->user_id ?? 0);
					$PassRequest->email = $this->request->email;
					$PassRequest->phone = $this->request->mobile;
					$PassRequest->total = null;
					$PassRequest->rate_id = $pass['rate_id'];
					$PassRequest->purchased_on = $currentDateTime;
					$PassRequest->start_date = date('Y-m-d');
					$PassRequest->end_date = date('Y-m-d', strtotime($pass['end_time']));
					$PassRequest->start_time = date('Y-m-d H:i:s');
					$PassRequest->end_time = $pass['end_time'];
					$PassRequest->processing_fee = ($this->request->is_admin == '0') ? $processing_fee : '0.00';
					$PassRequest->total_days = $days;
					$PassRequest->consume_days = 0;
					//$PassRequest->remaining_days = $this->passData->total_usage; comment by vikrant
					// changes by vikrant
					$PassRequest->remaining_days = $days;
					$PassRequest->partner_id = $this->user['user_type'] === '3' ? $this->user->id : $this->facility->owner_id;
					$PassRequest->is_admin = isset($this->request->is_admin) ? $this->request->is_admin : 1;
					$PassRequest->pass_code = $this->checkQrCode();
					$PassRequest->license_number = $ticket->license_number;
					$PassRequest->mer_reference = isset($this->request->reference) ? $this->request->reference : '';
					$PassRequest->user_consent = isset($this->request->user_consent) ? $this->request->user_consent : 0;
					$PassRequest->ex_month = isset($this->request->ex_month) ? $this->request->ex_month : '';
					$PassRequest->ex_year = isset($this->request->ex_year) ? $this->request->ex_year : '';
					$PassRequest->created_by = $this->user->id;
					$PassRequest->name = isset($userdata->name) && !empty($userdata->name)
						? $userdata->name
						: (isset($ticket->user) && isset($ticket->user->name) ? $ticket->user->name : 'Guest');
					$PassRequest->email = isset($pass['email']) ? $pass['email'] : '';
					$PassRequest->pass_type = isset($pass['pass_type']) ? $pass['pass_type'] : 0;
					$PassRequest->license_plate = $ticket->license_plate;
					$PassRequest->save();
					//$ticket->user_pass_id = $this->user->id;
					//code added by vikrant because wrong id attach with ticket
					$ticket->user_pass_id = $PassRequest->id;
					$ticket->save();



					if (isset($pass['email'])) {
						Artisan::queue('pass:email', ['passId' => $PassRequest->id, 'common' => '1', 'email' => $pass['email']]);
					}

					$responsePasses[] = [
						'ticket_number' => $ticket->ticket_number,
						'passApplied' => true,
						'pass_code' => $PassRequest->pass_code,
					];
				} else {
					$responsePasses[] = [
						'ticket_number' => $ticket->ticket_number,
						'passApplied' => false,
					];
				}
			}

			// Return the response
			return response()->json([
				'user_Id' => $this->user->id,
				'facility_id' => $this->facility->id,
				'passes' => $responsePasses,
			], 200);
		} catch (\Exception $e) {
			throw new ApiGenericException($e->getMessage() . ' line no-' . $e->getLine());
		}
	}



	protected function checkQrCode()
	{
		$code = 'PA' . rand(10, 99) . rand(100, 999) . rand(100, 999);
		$isExist = UserPass::where('pass_code', $code)->first();
		if ($isExist) {
			$this->checkQrCode();
		}
		return $code;
	}

	public function getHLTransactionList(Request $request)
	{
		$facilityId = $request->facility_id; //442
		$totalDays = $request->total_days; //10
		//$response = HeartlandPaymentGateway::getPaymentList($facilityId, $totalDays);
		$response = HeartlandPaymentGateway::getPaymentList($request);
		return response()->json($response);
	}

	public function makeDatacapTransaction($response, $ticket)
	{

		// $this->log->info("Request Data Datacap Save: " . json_encode($this->request->all()));
		$this->log->info("Resposen Data 1 " . json_encode($response));

		$this->log->info("Resposen Data 2--- " . $response['total']);
		//$response = json_decode($response, true);
		try {
			// dd($response, $response['AuthCode'],$user_id,$partner_id);
			$datacapTransaction = new DatacapTransaction();
			$datacapTransaction->user_id            = $ticket->user_id;
			$datacapTransaction->partner_id         = $ticket->partner_id;
			$datacapTransaction->ip_address         = \Request::ip();
			$datacapTransaction->total              = $response['total'];
			$datacapTransaction->response_message   = $response['Message'];
			$datacapTransaction->ref_id             = $response['RefNo'];
			$datacapTransaction->invoice_no         = $response['InvoiceNo'];
			$datacapTransaction->trans_id           = $response['AuthCode'];
			$datacapTransaction->name               = isset($this->request->datacap_token['card_holder_name']) ? $this->request->datacap_token['card_holder_name'] : $this->request->name_on_card;
			$datacapTransaction->card_last_four     = isset($this->request->datacap_token['Last4']) ? $this->request->datacap_token['Last4'] : substr($this->request->card_number, -4);
			$datacapTransaction->card_type          = isset($response['Brand']) ? $response['Brand'] : $this->request->datacap_token['Brand'];
			$datacapTransaction->card_name          = isset($this->request->datacap_token['Brand']) ? $this->request->datacap_token['Brand'] : $this->request->name_on_card;
			$datacapTransaction->expiry             = isset($this->request->cardExpiry) ? $this->request->cardExpiry : $this->request->expiration_month . $this->request->expiration_year;
			$datacapTransaction->card_type          = $response['Brand'];
			$datacapTransaction->session_id         = $response['CardHolderID'];
			$datacapTransaction->token              = $response['Token'];
			$datacapTransaction->result_reason      = $response['Message'];
			$datacapTransaction->currency_used      = "USD";
			$result = $datacapTransaction->save();
			$this->log->error("Record Saved Success ");
			if (!$result) {
				$this->log->error("Record Saved Fail ");
				// throw new ApiGenericException("Record Not Added");
			}
			return $datacapTransaction;
		} catch (\Throwable $th) {
			$this->log->error("Error : Message => {$th->getMessage()}  : File => {$th->getFile()}  : Line => {$th->getLine()}");
		}
	}
}
