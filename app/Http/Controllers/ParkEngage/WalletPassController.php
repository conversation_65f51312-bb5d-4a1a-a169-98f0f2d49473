<?php

namespace App\Http\Controllers\ParkEngage;

use App\Exceptions\ApiGenericException;
use Illuminate\Http\Request;
use Thenextweb\PassGenerator;
use App\Services\LoggerFactory;
use ZipArchive;
use App\Http\Requests;
use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\EventFacility;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\PermitRequest;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\Reservation;
use App\Models\Ticket;
use App\Models\UserPass;
use Carbon\Carbon;
use DateTime;
use PKPass\PKPass;
use Exception;
use Intervention\Image\ImageManagerStatic as Image;
use PKPass\PKConfiguration;
use GuzzleHttp\Client;
use Firebase\JWT\JWT;


class WalletPassController extends Controller
{
    const PASS_TYPE_IDENTIFIER = "pass.com.genericpass";
    const PASS_DESCRIPTION = "Parkengage Pass";
    const ORGANIZATION_NAME = "ParkEngage";
    const TEAM_ID = "CWA7VA7V37";
    const CERT_PWD = "Welcome@Buzz25";
    const ISSUER_ID = "3388000000022882909";
    protected $log;
    public function __construct(LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/apple_wallet_pass')->createLogger('apple_waalet_pass');
    }
    public function generateApplePass(Request $request)
    {
        $ticketNumber = $request->ticket_id;
        $getTicketDetails = Ticket::where('ticket_number', $ticketNumber)->first();
        if (!$getTicketDetails) {
            // throw new ApiGenericException("Ticket does not exist.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Ticket does not exist."
                ]
            ], 500);
        }
        $getFacilityDetails = Facility::find($getTicketDetails->facility_id);
        $facilityName = $getFacilityDetails->full_name;
        $facilityType = $getFacilityDetails->is_gated_facility;

        $checkinTime = $getTicketDetails->check_in_datetime;
        $checkOutTime = $getTicketDetails->estimated_checkout;
        $facilityId = $getTicketDetails->facility_id;
        $partnerId = $getTicketDetails->partner_id;
        $licensePlate = $getTicketDetails->license_plate;
        $totalAmount = '$' . number_format($getTicketDetails->grand_total, 2);


        $rate['price'] = $getTicketDetails->parking_amount;
        $priceBreakup =  $getTicketDetails->unGatedPriceBreakUp($rate, $getTicketDetails->facility, 'get');
        $payAmount = $priceBreakup['amount_paid'];
        $totalAmount = '$' . number_format($payAmount, 2);
        // Get brand settings
        $getBrandSetting = BrandSetting::where('user_id', $partnerId)->first();
        if (!$getBrandSetting) {
            // throw new ApiGenericException("Brand Setting not available.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Brand Setting not available."
                ]
            ], 500);
        }
        $backgroundColor = $getBrandSetting->color;

        $longUrl = env('WEB_ADMIN_URL') . '/admin/tickets?ticket_number=' . $ticketNumber;
        $shortUrl = file_get_contents("https://tinyurl.com/api-create.php?url=" . urlencode($longUrl));
        // Initialize PKPass
        $pass = new PKPass(
            storage_path('certificates/apple_pass.p12'),
            self::CERT_PWD
        );

        $validityDate = new DateTime($facilityType == '0' ? $checkOutTime : $checkinTime);
        if ($facilityType != '0') {
            $validityDate->modify('+24 hours');
        }
        // Pass data (unchanged)
        $data = [
            'description' => self::PASS_DESCRIPTION,
            'formatVersion' => 1,
            'organizationName' => self::ORGANIZATION_NAME,
            'passTypeIdentifier' => self::PASS_TYPE_IDENTIFIER,
            'serialNumber' => (string) $ticketNumber,
            'teamIdentifier' => self::TEAM_ID,
            'expirationDate' => $facilityType == '0' ? date('c', strtotime($checkOutTime)) : date('c', strtotime($checkinTime . ' +24 hours')),
            'generic' => [
                'transitType' => 'PKTransitTypeGeneric',
                'headerFields' => [],
                'primaryFields' => [
                    [
                        'key' => 'location',
                        'label' => '',
                        'value' => $facilityName,
                    ],
                ],
                'secondaryFields' => [
                    [
                        'key' => 'ticketId',
                        'label' => 'TICKET ID',
                        'value' => (string) $ticketNumber,
                    ],
                    [
                        'key' => 'licensePlate',
                        'label' => 'LICENSE PLATE',
                        'value' => !empty($licensePlate) ? (string) $licensePlate : '-',
                    ],
                    [
                        'key' => 'amount',
                        'label' => 'AMOUNT',
                        'value' => (string) $totalAmount,
                    ],
                ],
                'auxiliaryFields' => [
                    [
                        'key' => 'start',
                        'label' => 'START',
                        'value' => date('d M Y h:i A', strtotime($checkinTime)),
                    ],
                    [
                        'key' => 'end',
                        'label' => 'END',
                        'value' => $facilityType == 0 ? date('d M Y h:i A', strtotime($checkOutTime)) : '-',
                    ],
                ],
                'backFields' => [
                    [
                        'key' => 'validity',
                        'label' => 'VALID UNTIL',
                        'value' => $facilityType == '0' ? date('d M Y h:i A', strtotime($checkOutTime)) : date('d M Y h:i A', strtotime($checkinTime . ' +24 hours')),
                    ],
                ],
            ],
            'barcode' => [
                'format' => 'PKBarcodeFormatQR',
                'message' => $shortUrl ?? $ticketNumber,
                'messageEncoding' => 'iso-8859-1',
            ],
            'backgroundColor' => $backgroundColor,
            'foregroundColor' => '#ffffff',
            'labelColor' => '#ffffff',
            'relevantDate' => $facilityType == '0' ? date('c', strtotime($checkOutTime)) : date('c', strtotime($checkinTime . ' +24 hours')),
        ];
        $pass->setData($data);
        // Dynamic logo fetching and resizing
        $logourl = config('app.url');
        $applogourl = $logourl . '/brand-settings-logo/' . $getBrandSetting->id;
        $client = new Client();
        $tempLogoPath = storage_path('app/temp/logo.png'); // Temporary file path
        // dd($tempLogoPath);
        try {
            $logoResponse = $client->get($applogourl);
            if ($logoResponse->getStatusCode() == 200) {
                // Save the image to a temporary file
                file_put_contents($tempLogoPath, $logoResponse->getBody()->getContents());
                // Resize the logo to a standard height (e.g., 180px for @2x) while maintaining aspect ratio
                $image = Image::make($tempLogoPath)
                    ->resize(null, 40, function ($constraint) {
                        $constraint->aspectRatio(); // Maintain aspect ratio
                        $constraint->upsize(); // Prevent upscaling if original is smaller
                    })
                    ->save($tempLogoPath); // Overwrite temp file with resized image
                $pass->addFile($tempLogoPath); // Add resized logo
            } else {
                $pass->addFile(public_path('facility-logo/logo.png')); // Fallback
            }
        } catch (\Exception $e) {
            $pass->addFile(public_path('facility-logo/logo.png')); // Fallback on error
        }
        // Add static files
        $pass->addFile(public_path('facility-logo/icon.png'));
        // Create the pass
        $passFile = $pass->create();
        // Clean up temporary file
        if (file_exists($tempLogoPath)) {
            unlink($tempLogoPath); // Remove temp file after pass creation
        }
        if ($passFile) {
            return response($passFile, 200)
                ->header('Content-Type', 'application/vnd.apple.pkpass')
                ->header('Content-Disposition', "attachment; filename=\"pass-{$ticketNumber}.pkpass\"");
        } else {
            return response('Error generating pass: ' . $pass->getError(), 500);
        }
    }

    public function generateGooglePass(Request $request)
    {
        $ticketNumber = $request->ticket_id;
        $getTicketDetails = Ticket::where('ticket_number', $ticketNumber)->first();
        if (!$getTicketDetails) {
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Ticket does not exist."
                ]
            ], 500);
        }

        $getFacilityDetails = Facility::find($getTicketDetails->facility_id);
        $facilityName = $getFacilityDetails->full_name;
        $facilityType = $getFacilityDetails->is_gated_facility;
        $checkinTime = $getTicketDetails->check_in_datetime;
        $checkOutTime = $getTicketDetails->estimated_checkout;
        $facilityId = $getTicketDetails->facility_id;
        $partnerId = $getTicketDetails->partner_id;
        $licensePlate = $getTicketDetails->license_plate;
        $totalAmount = '$' . number_format($getTicketDetails->grand_total, 2);
        $rate['price'] = $getTicketDetails->parking_amount;
        $priceBreakup = $getTicketDetails->unGatedPriceBreakUp($rate, $getTicketDetails->facility, 'get');
        $payAmount = $priceBreakup['amount_paid'];
        $totalAmount = '$' . number_format($payAmount, 2);

        $getBrandSetting = BrandSetting::where('user_id', $partnerId)->first();
        if (!$getBrandSetting) {
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Brand Setting not available."
                ]
            ], 500);
        }

        $backgroundColor = $getBrandSetting->color;
        $logourl = config('app.url');
        $applogourl = $logourl . '/brand-settings-logo/' . $getBrandSetting->id. '.png';

        $imagePath = public_path('brand-settings-logo/' . $getBrandSetting->id . '.png');
        if (file_exists($imagePath)) {
            $image = Image::make($imagePath);
            $image->resize(10, 10, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            })->save();
        }


        $longUrl = env('WEB_ADMIN_URL') . '/admin/tickets?ticket_number=' . $ticketNumber;
        $shortUrl = file_get_contents("https://tinyurl.com/api-create.php?url=" . urlencode($longUrl));

        $serviceAccountPath = storage_path('certificates/service-account.json');
        $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);

        $issuerId = self::ISSUER_ID;
        $classSuffix = 'ParkengagePass_' . uniqid();
        $objectSuffix = 'ticket_object_' . uniqid();

        $genericClass = [
            'id' => "{$issuerId}.{$classSuffix}",
            'genericType' => 'GENERIC_TYPE_UNSPECIFIED',
            'hexBackgroundColor' => $backgroundColor,
            'heroImage' => [
                'sourceUri' => [
                    'uri' => $applogourl.'.png'
                ],
                'contentDescription' => [
                    'defaultValue' => [
                        'language' => 'en-US',
                        'value' => 'ParkEngage Hero Image'
                    ]
                ]
            ],
            'classTemplateInfo' => [
                'cardTemplateOverride' => [
                    'cardTitle' => [
                        'defaultValue' => [
                            'language' => 'en-US',
                            'value' => 'ParkEngage Pass (Test Mode)'
                        ]
                    ],
                    'cardRowTemplateInfos' => [
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['ticketId']"]
                                        ]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['licensePlate']"]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['amount']"]
                                        ]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'value' => '' // Placeholder to maintain structure
                                    ]
                                ]
                            ]
                        ],
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['startTime']"]
                                        ]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['endTime']"]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $genericObject = [
            'id' => "{$issuerId}.{$objectSuffix}",
            'classId' => "{$issuerId}.{$classSuffix}",
            'state' => 'ACTIVE',
            'cardTitle' => [
                'defaultValue' => [
                    'language' => 'en-US',
                    'value' => "TEST MODE - $facilityName"
                ]
            ],
            'header' => [
                'defaultValue' => [
                    'language' => 'en-US',
                    'value' => $facilityName
                ]
            ],
            'textModulesData' => [
                [
                    'header' => 'TICKET ID',
                    'body' => $ticketNumber,
                    'id' => 'ticketId'
                ],
                [
                    'header' => 'LICENSE PLATE',
                    'body' => !empty($licensePlate) ? (string) $licensePlate : '-',
                    'id' => 'licensePlate'
                ],
                [
                    'header' => 'AMOUNT',
                    'body' => $totalAmount,
                    'id' => 'amount'
                ],
                [
                    'header' => 'START',
                    'body' => date('d M Y h:i A', strtotime($checkinTime)),
                    'id' => 'startTime'
                ],
                [
                    'header' => 'END',
                    'body' => $facilityType == 0 ? date('d M Y h:i A', strtotime($checkOutTime)) : '-',
                    'id' => 'endTime'
                ]
            ],
            'barcode' => [
                'type' => 'QR_CODE',
                'value' => $shortUrl ?? $ticketNumber,
                'alternateText' => ''
            ],
            'hexBackgroundColor' => $backgroundColor,
            'logo' => [
                'sourceUri' => ['uri' => $applogourl],
                'contentDescription' => [
                    'defaultValue' => [
                        'language' => 'en-US',
                        'value' => 'ParkEngage Logo'
                    ]
                ]
            ]
        ];

        $claims = [
            'iss' => $serviceAccount['client_email'],
            'aud' => 'google',
            'origins' => ['https://parkengage.com'],
            'typ' => 'savetowallet',
            'payload' => [
                'genericClasses' => [$genericClass],
                'genericObjects' => [$genericObject]
            ]
        ];

        $privateKey = $serviceAccount['private_key'];
        $jwt = JWT::encode($claims, $privateKey, 'RS256');
        $walletUrl = "https://pay.google.com/gp/v/save/{$jwt}";

        // Debugging: Log the full payload and URL
        \Log::info("Google Wallet Payload: " . json_encode($claims['payload'], JSON_PRETTY_PRINT));
        \Log::info("Generated Google Wallet URL: $walletUrl");
        return $walletUrl;
    }
 

    public function generatePermitApplePass(Request $request)
    {
        $accountNumber = $request->account_number;
        $getTicketDetails = PermitRequest::where('account_number', $accountNumber)->first();
        $getLiecenseNumber = PermitVehicleMapping::where('permit_request_id', $getTicketDetails->id)
            ->first();
        $getPermitVechile = PermitVehicle::where('id', $getLiecenseNumber->permit_vehicle_id)->first();
        $permitLicenseNumber = $getPermitVechile->license_plate_number;

        if (!$getTicketDetails) {
            // throw new ApiGenericException("Permit does not exist.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Permit does not exist."
                ]
            ], 500);
        }
        $getFacilityDetails = Facility::find($getTicketDetails->facility_id);
        $facilityName = $getFacilityDetails->full_name;
        $facilityType = $getFacilityDetails->is_gated_facility;

        $checkinTime = $getTicketDetails->desired_start_date;
        $checkOutTime = $getTicketDetails->desired_end_date;
        $facilityId = $getTicketDetails->facility_id;
        $partnerId = $getTicketDetails->partner_id;
        $licensePlate = $getTicketDetails->license_number;
        $totalAmount = '$' . number_format($getTicketDetails->permit_final_amount, 2);
        // Get brand settings
        $getBrandSetting = BrandSetting::where('user_id', $partnerId)->first();
        if (!$getBrandSetting) {
            // throw new ApiGenericException("Brand Setting not available.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Brand Setting not available."
                ]
            ], 500);
        }
        $backgroundColor = $getBrandSetting->color;
        // Initialize PKPass
        $pass = new PKPass(
            storage_path('certificates/apple_pass.p12'),
            self::CERT_PWD
        );
        // Pass data (unchanged)
        $data = [
            'description' => self::PASS_DESCRIPTION,
            'formatVersion' => 1,
            'organizationName' => self::ORGANIZATION_NAME,
            'passTypeIdentifier' => self::PASS_TYPE_IDENTIFIER,
            'serialNumber' => (string) $accountNumber,
            'teamIdentifier' => self::TEAM_ID,
            'expirationDate' => date('c', strtotime($checkOutTime)),
            'generic' => [
                'transitType' => 'PKTransitTypeGeneric',
                'headerFields' => [],
                'primaryFields' => [
                    [
                        'key' => 'location',
                        'label' => '',
                        'value' => $facilityName,
                    ],
                ],
                'secondaryFields' => [
                    [
                        'key' => 'permitNo',
                        'label' => 'PERMIT NO.',
                        'value' => (string) $accountNumber,
                    ],
                    [
                        'key' => 'licensePlate',
                        'label' => 'LICENSE PLATE',
                        'value' => (string) $permitLicenseNumber,
                    ],
                    [
                        'key' => 'amount',
                        'label' => 'AMOUNT',
                        'value' => (string) $totalAmount,
                    ],
                ],
                'auxiliaryFields' => [
                    [
                        'key' => 'start',
                        'label' => 'START AT',
                        'value' => date('d M Y h:i A', strtotime($checkinTime)),
                    ],
                    [
                        'key' => 'end',
                        'label' => 'END AT',
                        'value' => date('d M Y h:i A', strtotime($checkOutTime)),
                    ],
                ],
                'backFields' => [
                    [
                        'key' => 'validity',
                        'label' => 'VALID UNTIL',
                        'value' => date('d M Y h:i A', strtotime($checkOutTime)),
                    ],
                ],
            ],
            'barcode' => [
                'format' => 'PKBarcodeFormatQR',
                'message' => $accountNumber,
                'messageEncoding' => 'iso-8859-1',
            ],
            'backgroundColor' => $backgroundColor,
            'foregroundColor' => '#ffffff',
            'labelColor' => '#ffffff',
            'relevantDate' => date('c', strtotime($checkOutTime)),
        ];
        $pass->setData($data);
        // Dynamic logo fetching and resizing
        $logourl = config('app.url');
        $applogourl = $logourl . '/brand-settings-logo/' . $getBrandSetting->id;
        $client = new Client();
        $tempLogoPath = storage_path('app/temp/logo.png'); // Temporary file path
        // dd($tempLogoPath);
        try {
            $logoResponse = $client->get($applogourl);
            if ($logoResponse->getStatusCode() == 200) {
                // Save the image to a temporary file
                file_put_contents($tempLogoPath, $logoResponse->getBody()->getContents());
                // Resize the logo to a standard height (e.g., 180px for @2x) while maintaining aspect ratio
                $image = Image::make($tempLogoPath)
                    ->resize(null, 40, function ($constraint) {
                        $constraint->aspectRatio(); // Maintain aspect ratio
                        $constraint->upsize(); // Prevent upscaling if original is smaller
                    })
                    ->save($tempLogoPath); // Overwrite temp file with resized image
                $pass->addFile($tempLogoPath); // Add resized logo
            } else {
                $pass->addFile(public_path('facility-logo/logo.png')); // Fallback
            }
        } catch (\Exception $e) {
            $pass->addFile(public_path('facility-logo/logo.png')); // Fallback on error
        }
        // Add static files
        $pass->addFile(public_path('facility-logo/icon.png'));
        // Create the pass
        $passFile = $pass->create();
        // Clean up temporary file
        if (file_exists($tempLogoPath)) {
            unlink($tempLogoPath); // Remove temp file after pass creation
        }
        if ($passFile) {
            return response($passFile, 200)
                ->header('Content-Type', 'application/vnd.apple.pkpass')
                ->header('Content-Disposition', "attachment; filename=\"pass-{$accountNumber}.pkpass\"");
        } else {
            return response('Error generating pass: ' . $pass->getError(), 500);
        }
    }
    public function generatePermitGooglePass(Request $request)
    {
        $accountNumber = $request->account_number;
        $getTicketDetails = PermitRequest::where('account_number', $accountNumber)->first();
        $getTicketDetails = PermitRequest::where('account_number', $accountNumber)->first();
        $getLiecenseNumber = PermitVehicleMapping::where('permit_request_id', $getTicketDetails->id)
            ->first();
        $getPermitVechile = PermitVehicle::where('id', $getLiecenseNumber->permit_vehicle_id)->first();

        $permitLicenseNumber = $getPermitVechile->license_plate_number;


        if (!$getTicketDetails) {
            // throw new ApiGenericException("Permit does not exist.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Permit does not exist."
                ]
            ], 500);
        }
        $getFacilityDetails = Facility::find($getTicketDetails->facility_id);
        $facilityName = $getFacilityDetails->full_name;
        $facilityType = $getFacilityDetails->is_gated_facility;

        $checkinTime = $getTicketDetails->desired_start_date;
        $checkOutTime = $getTicketDetails->desired_end_date;
        $facilityId = $getTicketDetails->facility_id;
        $partnerId = $getTicketDetails->partner_id;
        $licensePlate = $getTicketDetails->license_number;
        $totalAmount = '$' . number_format($getTicketDetails->permit_final_amount, 2);
        // Get brand settings (assuming ParkEngage branding)
        $getBrandSetting = BrandSetting::where('user_id', $partnerId)->first();
        if (!$getBrandSetting) {
            // throw new ApiGenericException("Brand Setting not available.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Brand Setting not available."
                ]
            ], 500);
        }
        $backgroundColor = $getBrandSetting->color;
        $logourl = config('app.url');
        $applogourl = $logourl . '/brand-settings-logo/' . $getBrandSetting->id. '.png';

        $imagePath = public_path('brand-settings-logo/' . $getBrandSetting->id . '.png');
        if (file_exists($imagePath)) {
            $image = Image::make($imagePath);
            $image->resize(40, 40, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            })->save();
        }
        // Load service account credentials
        $serviceAccountPath = storage_path('certificates/service-account.json');
        $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
        // Define issuer ID and class/object IDs
        $issuerId = self::ISSUER_ID;
        $classSuffix = 'ParkengagePass_' . uniqid();
        $objectSuffix = 'ticket_object_' . uniqid();
        // Define the Generic Pass Class (template) - Customize for parking pass design
        $genericClass = [
            'id' => "{$issuerId}.{$classSuffix}",
            'genericType' => 'GENERIC_TYPE_UNSPECIFIED',
            'hexBackgroundColor' => $backgroundColor,
            'heroImage' => [
                'sourceUri' => [
                    'uri' => $applogourl.'.png'
                ],
                'contentDescription' => [
                    'defaultValue' => [
                        'language' => 'en-US',
                        'value' => 'ParkEngage Hero Image'
                    ]
                ]
            ],
            'classTemplateInfo' => [
                'cardTemplateOverride' => [
                    'cardTitle' => [
                        'defaultValue' => [
                            'language' => 'en-US',
                            'value' => 'Parkengage Parking Permit'
                        ]
                    ],
                    'cardRowTemplateInfos' => [
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['ticketId']"]
                                        ]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['licensePlate']"]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['amount']"]
                                        ]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'value' => '' // Placeholder to maintain structure
                                    ]
                                ]
                            ]
                        ],
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['startTime']"]
                                        ]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['endTime']"]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        // Define the Generic Pass Object (instance) - Populate with ticket data from the image
        $genericObject = [
            'id' => "{$issuerId}.{$objectSuffix}",
            'classId' => "{$issuerId}.{$classSuffix}",
            'state' => 'ACTIVE',
            'cardTitle' => [
                'defaultValue' => [
                    'language' => 'en-US',
                    'value' => $facilityName
                ]
            ],
            'header' => [
                'defaultValue' => [
                    'language' => 'en-US',
                    'value' => $facilityName
                ]
            ],
            'textModulesData' => [
                [
                    'header' => 'Permit No.',
                    'body' => $accountNumber,
                    'id' => 'ticketId'
                ],
                [
                    'header' => 'License Plate',
                    'body' => $permitLicenseNumber,
                    'id' => 'licensePlate'
                ],
                [
                    'header' => 'Amount',
                    'body' => $totalAmount,
                    'id' => 'amount'
                ],
                [
                    'header' => 'Start',
                    'body' => date('d M Y h:i A', strtotime($checkinTime)),
                    'id' => 'startTime'
                ],
                [
                    'header' => 'End',
                    'body' => date('d M Y h:i A', strtotime($checkOutTime)),
                    'id' => 'endTime'
                ]
            ],
            'barcode' => [
                'type' => 'QR_CODE',
                'value' => $accountNumber, // Use the Ticket ID or generate a unique QR code value
                'alternateText' => ''
            ],
            'hexBackgroundColor' => $backgroundColor,
            'logo' => [
                'sourceUri' => ['uri' => $applogourl],
                'contentDescription' => [
                    'defaultValue' => [
                        'language' => 'en-US',
                        'value' => 'ParkEngage Logo'
                    ]
                ]
            ]
        ];
        $claims = [
            'card_title' => 'ParkEngage Permit',
            'iss' => $serviceAccount['client_email'],
            'aud' => 'google',
            'origins' => ['https://parkengage.com'],
            'typ' => 'savetowallet',
            'payload' => [
                'genericClasses' => [$genericClass],
                'genericObjects' => [$genericObject]
            ]
        ];
        // Sign the JWT with the private key
        $privateKey = $serviceAccount['private_key'];
        $jwt = JWT::encode($claims, $privateKey, 'RS256');
        // Generate the "Add to Google Wallet" link
        $walletUrl = "https://pay.google.com/gp/v/save/{$jwt}";
        return $walletUrl;
    }

    public function genrateReservationApplePass(Request $request)
    {
        $ticketNumber = $request->account_number;
        $getTicketDetails = Reservation::where('ticketech_code', $ticketNumber)->first();
        $thirdPartyCode = $getTicketDetails->thirdparty_code;
        if (!$getTicketDetails) {
            // throw new ApiGenericException("Reservation does not exist.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Reservation does not exist."
                ]
            ], 500);
        }

        $getFacilityDetails = Facility::find($getTicketDetails->facility_id);
        $facilityName = $getFacilityDetails->full_name;
        $facilityType = $getFacilityDetails->is_gated_facility;

        $checkinTime = $getTicketDetails->start_timestamp;
        $checkOutTime = $getTicketDetails->end_timestamp;
        $facilityId = $getTicketDetails->facility_id;
        $partnerId = $getTicketDetails->partner_id;
        $licensePlate = $getTicketDetails->license_plate;
        $totalAmount = '$' . number_format($getTicketDetails->total, 2);

        // Get brand settings
        $getBrandSetting = BrandSetting::where('user_id', $partnerId)->first();
        if (!$getBrandSetting) {
            // throw new ApiGenericException("Brand Setting not available.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Brand Setting not available."
                ]
            ], 500);
        }

        $backgroundColor = $getBrandSetting->color;

        // Initialize PKPass
        $pass = new PKPass(
            storage_path('certificates/apple_pass.p12'),
            self::CERT_PWD
        );

        // Pass data (unchanged)
        $data = [
            'description' => self::PASS_DESCRIPTION,
            'formatVersion' => 1,
            'organizationName' => self::ORGANIZATION_NAME,
            'passTypeIdentifier' => self::PASS_TYPE_IDENTIFIER,
            'serialNumber' => (string) $ticketNumber,
            'teamIdentifier' => self::TEAM_ID,
            'expirationDate' => date('c', strtotime($checkOutTime)),
            'generic' => [
                'transitType' => 'PKTransitTypeGeneric',
                'headerFields' => [],
                'primaryFields' => [
                    [
                        'key' => 'location',
                        'label' => '',
                        'value' => $facilityName,
                    ],
                ],
                'secondaryFields' => [
                    [
                        'key' => 'ticketId',
                        'label' => 'RESERVATION ID',
                        'value' => (string) $ticketNumber,
                    ],
                    [
                        'key' => 'licensePlate',
                        'label' => 'LICENSE PLATE',
                        'value' => (string) $licensePlate,
                    ],
                    [
                        'key' => 'amount',
                        'label' => 'AMOUNT',
                        'value' => (string) $totalAmount,
                    ],
                ],
                'auxiliaryFields' => [
                    [
                        'key' => 'start',
                        'label' => 'START',
                        'value' => date('d M Y h:i A', strtotime($checkinTime)),
                    ],
                    [
                        'key' => 'end',
                        'label' => 'END',
                        'value' => date('d M Y h:i A', strtotime($checkOutTime)),
                    ],
                ],
                'backFields' => [
                    [
                        'key' => 'validity',
                        'label' => 'VALID UNTIL',
                        'value' => date('d M Y h:i A', strtotime($checkOutTime)),
                    ],
                ],
            ],
            'barcode' => [
                'format' => 'PKBarcodeFormatQR',
                'message' => !empty($thirdPartyCode) ? $thirdPartyCode : $ticketNumber,
                'messageEncoding' => 'iso-8859-1',
            ],
            'backgroundColor' => $backgroundColor,
            'foregroundColor' => '#ffffff',
            'labelColor' => '#ffffff',
            'relevantDate' => date('c', strtotime($checkOutTime)),
        ];

        $pass->setData($data);

        // Dynamic logo fetching and resizing
        $logourl = config('app.url');
        $applogourl = $logourl . '/brand-settings-logo/' . $getBrandSetting->id;
        $client = new Client();
        $tempLogoPath = storage_path('app/temp/logo.png'); // Temporary file path
        // dd($tempLogoPath);
        try {
            $logoResponse = $client->get($applogourl);
            if ($logoResponse->getStatusCode() == 200) {
                // Save the image to a temporary file
                file_put_contents($tempLogoPath, $logoResponse->getBody()->getContents());

                // Resize the logo to a standard height (e.g., 180px for @2x) while maintaining aspect ratio
                $image = Image::make($tempLogoPath)
                    ->resize(null, 40, function ($constraint) {
                        $constraint->aspectRatio(); // Maintain aspect ratio
                        $constraint->upsize(); // Prevent upscaling if original is smaller
                    })
                    ->save($tempLogoPath); // Overwrite temp file with resized image

                $pass->addFile($tempLogoPath); // Add resized logo
            } else {
                $pass->addFile(public_path('facility-logo/logo.png')); // Fallback
            }
        } catch (\Exception $e) {
            $pass->addFile(public_path('facility-logo/logo.png')); // Fallback on error
        }

        // Add static files
        $pass->addFile(public_path('facility-logo/icon.png'));

        // Create the pass
        $passFile = $pass->create();

        // Clean up temporary file
        if (file_exists($tempLogoPath)) {
            unlink($tempLogoPath); // Remove temp file after pass creation
        }

        if ($passFile) {
            return response($passFile, 200)
                ->header('Content-Type', 'application/vnd.apple.pkpass')
                ->header('Content-Disposition', "attachment; filename=\"pass-{$ticketNumber}.pkpass\"");
        } else {
            return response('Error generating pass: ' . $pass->getError(), 500);
        }
    }

    public function generateReservationGooglePass(Request $request)
    {
        $ticketNumber = $request->account_number;

        // Fetch reservation details
        $getTicketDetails = Reservation::where('ticketech_code', $ticketNumber)->first();
        if (!$getTicketDetails) {
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => ['message' => "Reservation does not exist."]
            ], 500);
        }

        $thirdPartyCode = $getTicketDetails->thirdparty_code;
        $facilityId = $getTicketDetails->facility_id;
        $partnerId = $getTicketDetails->partner_id;
        $licensePlate = $getTicketDetails->license_plate;
        $totalAmount = '$' . number_format($getTicketDetails->total, 2);
        $checkinTime = $getTicketDetails->start_timestamp;
        $checkOutTime = $getTicketDetails->end_timestamp;

        // Fetch facility details
        $getFacilityDetails = Facility::find($facilityId);
        $facilityName = $getFacilityDetails->full_name;

        // Fetch brand settings
        $getBrandSetting = BrandSetting::where('user_id', $partnerId)->first();
        if (!$getBrandSetting) {
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => ['message' => "Brand Setting not available."]
            ], 500);
        }

        $backgroundColor = $getBrandSetting->color;
        $logourl = config('app.url');
        $applogourl = $logourl . '/brand-settings-logo/' . $getBrandSetting->id. '.png';

        $imagePath = public_path('brand-settings-logo/' . $getBrandSetting->id . '.png');
        if (file_exists($imagePath)) {
            $image = Image::make($imagePath);
            $image->resize(40, 40, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            })->save();
          
        }

        // Load service account credentials
        $serviceAccountPath = storage_path('certificates/service-account.json');
        $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);

        $issuerId = self::ISSUER_ID;

        // Use deterministic IDs instead of unique ones
        $classSuffix = 'ParkengagePass_' . uniqid(); // Reuse class for same facility
        $objectSuffix = 'ticket_object_'.uniqid();

        $classId = "{$issuerId}.{$classSuffix}";
        $objectId = "{$issuerId}.{$objectSuffix}";

        // Define the generic class (template) - this only needs to be created once
        $genericClass = [
            'id' => $classId,
            'genericType' => 'GENERIC_TYPE_UNSPECIFIED',
            'hexBackgroundColor' => $backgroundColor,
            'heroImage' => [
                'sourceUri' => [
                    'uri' => $applogourl.'.png'
                ],
                'contentDescription' => [
                    'defaultValue' => [
                        'language' => 'en-US',
                        'value' => 'ParkEngage Hero Image'
                    ]
                ]
            ],
            'classTemplateInfo' => [
                'cardTemplateOverride' => [
                    'cardRowTemplateInfos' => [
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [['fieldPath' => "object.textModulesData['ticketId']"]]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'fields' => [['fieldPath' => "object.textModulesData['licensePlate']"]]
                                    ]
                                ]
                            ]
                        ],
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [['fieldPath' => "object.textModulesData['amount']"]]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => ['value' => '']
                                ]
                            ]
                        ],
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [['fieldPath' => "object.textModulesData['startTime']"]]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'fields' => [['fieldPath' => "object.textModulesData['endTime']"]]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Define the generic object (instance) - this will be updated
        $genericObject = [
            'id' => $objectId,
            'classId' => $classId,
            'state' => 'ACTIVE',
            'cardTitle' => [
                'defaultValue' => [
                    'language' => 'en-US',
                    'value' => $facilityName
                ]
            ],
            'header' => [
                'defaultValue' => [
                    'language' => 'en-US',
                    'value' => $facilityName
                ]
            ],
            'textModulesData' => [
                ['header' => 'RESERVATION ID', 'body' => $ticketNumber, 'id' => 'ticketId'],
                ['header' => 'LICENSE PLATE', 'body' => $licensePlate, 'id' => 'licensePlate'],
                ['header' => 'AMOUNT', 'body' => $totalAmount, 'id' => 'amount'],
                ['header' => 'START', 'body' => date('d M Y h:i A', strtotime($checkinTime)), 'id' => 'startTime'],
                ['header' => 'END', 'body' => date('d M Y h:i A', strtotime($checkOutTime)), 'id' => 'endTime']
            ],
            'barcode' => [
                'type' => 'QR_CODE',
                'value' => !empty($thirdPartyCode) ? $thirdPartyCode : $ticketNumber,
                'alternateText' => ''
            ],
            'hexBackgroundColor' => $backgroundColor,
            'logo' => [
                'sourceUri' => ['uri' => $applogourl],
                'contentDescription' => [
                    'defaultValue' => [
                        'language' => 'en-US',
                        'value' => 'ParkEngage Logo'
                    ]
                ]
            ]
        ];
        

        // JWT claims for Google Wallet
        $claims = [
            'card_title' => 'ParkEngage Pass',
            'iss' => $serviceAccount['client_email'],
            'aud' => 'google',
            'origins' => ['https://parkengage.com'],
            'typ' => 'savetowallet',
            'payload' => [
                'genericClasses' => [$genericClass], // Include class only if it doesn't exist yet
                'genericObjects' => [$genericObject]
            ]
        ];

        $privateKey = $serviceAccount['private_key'];
        $jwt = JWT::encode($claims, $privateKey, 'RS256');
        $walletUrl = "https://pay.google.com/gp/v/save/{$jwt}";

        return $walletUrl;
    }


    public function genratePassApplePass(Request $request)
    {
        $passCode = $request->account_number;
        $getDetails = UserPass::where('pass_code', $passCode)->first();
        if (!$getDetails) {
            // throw new ApiGenericException("Pass does not exist.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Pass does not exist."
                ]
            ], 500);
        }

        $getFacilityDetails = Facility::find($getDetails->facility_id);
        $facilityName = $getFacilityDetails->full_name;
        $facilityType = $getFacilityDetails->is_gated_facility;

        $checkinTime = $getDetails->start_time;
        $checkOutTime = $getDetails->end_time;
        $partnerId = $getDetails->partner_id;
        $licensePlate = $getDetails->license_plate;
        $totalAmount = '$' . number_format($getDetails->pass_amount, 2);

        // Get brand settings
        $getBrandSetting = BrandSetting::where('user_id', $partnerId)->first();
        if (!$getBrandSetting) {
            // throw new ApiGenericException("Brand Setting not available.");
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Brand Setting not available."
                ]
            ], 500);
        }

        $backgroundColor = $getBrandSetting->color;

        // Initialize PKPass
        $pass = new PKPass(
            storage_path('certificates/apple_pass.p12'),
            self::CERT_PWD
        );

        // Pass data (unchanged)
        $data = [
            'description' => self::PASS_DESCRIPTION,
            'formatVersion' => 1,
            'organizationName' => self::ORGANIZATION_NAME,
            'passTypeIdentifier' => self::PASS_TYPE_IDENTIFIER,
            'serialNumber' => (string) $passCode,
            'teamIdentifier' => self::TEAM_ID,
            'expirationDate' => date('c', strtotime($checkOutTime)),
            'generic' => [
                'transitType' => 'PKTransitTypeGeneric',
                'headerFields' => [],
                'primaryFields' => [
                    [
                        'key' => 'location',
                        'label' => '',
                        'value' => $facilityName,
                    ],
                ],
                'secondaryFields' => [
                    [
                        'key' => 'ticketId',
                        'label' => 'PASS ID',
                        'value' => (string) $passCode,
                    ],
                    [
                        'key' => 'licensePlate',
                        'label' => 'LICENSE PLATE',
                        'value' => (string) $licensePlate,
                    ],
                    [
                        'key' => 'amount',
                        'label' => 'AMOUNT',
                        'value' => (string) $totalAmount,
                    ],
                ],
                'auxiliaryFields' => [
                    [
                        'key' => 'start',
                        'label' => 'START',
                        'value' => date('d M Y h:i A', strtotime($checkinTime)),
                    ],
                    [
                        'key' => 'end',
                        'label' => 'END',
                        'value' => date('d M Y h:i A', strtotime($checkOutTime)),
                    ],
                ],
                'backFields' => [
                    [
                        'key' => 'validity',
                        'label' => 'VALID UNTIL',
                        'value' => date('d M Y h:i A', strtotime($checkOutTime)),
                    ],
                ],
            ],
            'barcode' => [
                'format' => 'PKBarcodeFormatQR',
                'message' => $passCode,
                'messageEncoding' => 'iso-8859-1',
            ],
            'backgroundColor' => $backgroundColor,
            'foregroundColor' => '#ffffff',
            'labelColor' => '#ffffff',
            'relevantDate' => date('c', strtotime($checkOutTime)),
        ];

        $pass->setData($data);

        // Dynamic logo fetching and resizing
        $logourl = config('app.url');
        $applogourl = $logourl . '/brand-settings-logo/' . $getBrandSetting->id;
        $client = new Client();
        $tempLogoPath = storage_path('app/temp/logo.png'); // Temporary file path
        // dd($tempLogoPath);
        try {
            $logoResponse = $client->get($applogourl);
            if ($logoResponse->getStatusCode() == 200) {
                // Save the image to a temporary file
                file_put_contents($tempLogoPath, $logoResponse->getBody()->getContents());

                // Resize the logo to a standard height (e.g., 180px for @2x) while maintaining aspect ratio
                $image = Image::make($tempLogoPath)
                    ->resize(null, 40, function ($constraint) {
                        $constraint->aspectRatio(); // Maintain aspect ratio
                        $constraint->upsize(); // Prevent upscaling if original is smaller
                    })
                    ->save($tempLogoPath); // Overwrite temp file with resized image

                $pass->addFile($tempLogoPath); // Add resized logo
            } else {
                $pass->addFile(public_path('facility-logo/logo.png')); // Fallback
            }
        } catch (\Exception $e) {
            $pass->addFile(public_path('facility-logo/logo.png')); // Fallback on error
        }

        // Add static files
        $pass->addFile(public_path('facility-logo/icon.png'));

        // Create the pass
        $passFile = $pass->create();

        // Clean up temporary file
        if (file_exists($tempLogoPath)) {
            unlink($tempLogoPath); // Remove temp file after pass creation
        }

        if ($passFile) {
            return response($passFile, 200)
                ->header('Content-Type', 'application/vnd.apple.pkpass')
                ->header('Content-Disposition', "attachment; filename=\"pass-{$passCode}.pkpass\"");
        } else {
            return response('Error generating pass: ' . $pass->getError(), 500);
        }
    }

    public function generatePassGooglePass(Request $request)
    {
        $passCode = $request->account_number;
        $getDetails = UserPass::where('pass_code', $passCode)->first();
        if (!$getDetails) {
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Pass does not exist."
                ]
            ], 500);
        }
        $getFacilityDetails = Facility::find($getDetails->facility_id);
        $facilityName = $getFacilityDetails->full_name;
        $facilityType = $getFacilityDetails->is_gated_facility;

        $checkinTime = $getDetails->start_time;
        $checkOutTime = $getDetails->end_time;
        $partnerId = $getDetails->partner_id;
        $licensePlate = $getDetails->license_plate;
        $totalAmount = '$' . number_format($getDetails->pass_amount, 2);

        $getBrandSetting = BrandSetting::where('user_id', $partnerId)->first();
        if (!$getBrandSetting) {
            return response()->json([
                'status' => 500,
                'data' => null,
                'errors' => [
                    'message' => "Brand Setting not available."
                ]
            ], 500);
        }

        $backgroundColor = $getBrandSetting->color;
        $logourl = config('app.url');
        $applogourl = $logourl . '/brand-settings-logo/' . $getBrandSetting->id. '.png';

        $imagePath = public_path('brand-settings-logo/' . $getBrandSetting->id . '.png');
        if (file_exists($imagePath)) {
            $image = Image::make($imagePath);
            $image->resize(40, 40, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            })->save();
        }

        $serviceAccountPath = storage_path('certificates/service-account.json');
        $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);

        $issuerId = self::ISSUER_ID;
        $classSuffix = 'ParkengagePass_' . uniqid();
        $objectSuffix = 'ticket_object_' . uniqid();

        $genericClass = [
            'id' => "{$issuerId}.{$classSuffix}",
            'genericType' => 'GENERIC_TYPE_UNSPECIFIED',
            'hexBackgroundColor' => $backgroundColor,
            'heroImage' => [
                'sourceUri' => [
                    'uri' => $applogourl.'.png'
                ],
                'contentDescription' => [
                    'defaultValue' => [
                        'language' => 'en-US',
                        'value' => 'ParkEngage Hero Image'
                    ]
                ]
            ],
            'classTemplateInfo' => [
                'cardTemplateOverride' => [
                    'cardTitle' => [
                        'defaultValue' => [
                            'language' => 'en-US',
                            'value' => ''
                        ]
                    ],
                    'header' => [
                        'defaultValue' => [
                            'language' => 'en-US',
                            'value' => $facilityName
                        ]
                    ],
                    'barcode' => [
                        'type' => 'QR_CODE',
                        'alternateText' => 'Scan for Entry'
                    ],
                    'cardRowTemplateInfos' => [
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['ticketId']"]
                                        ]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['licensePlate']"]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['amount']"]
                                        ]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'value' => '' // Placeholder to maintain structure
                                    ]
                                ]
                            ]
                        ],
                        [
                            'twoItems' => [
                                'startItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['startTime']"]
                                        ]
                                    ]
                                ],
                                'endItem' => [
                                    'firstValue' => [
                                        'fields' => [
                                            ['fieldPath' => "object.textModulesData['endTime']"]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'imageModulesData' => [
                        [
                            'mainImage' => [
                                'sourceUri' => [
                                    'uri' => $applogourl
                                ]
                            ]
                        ]
                    ],
                    'style' => [
                        'backgroundColor' => $backgroundColor // Set to yellow
                    ]
                ]
            ]
        ];

        $genericObject = [
            'id' => "{$issuerId}.{$objectSuffix}",
            'classId' => "{$issuerId}.{$classSuffix}",
            'state' => 'ACTIVE',
            'cardTitle' => [
                'defaultValue' => [
                    'language' => 'en-US',
                    'value' => $facilityName
                ]
            ],
            'header' => [
                'defaultValue' => [
                    'language' => 'en-US',
                    'value' => $facilityName
                ]
            ],
            'textModulesData' => [
                [
                    'header' => 'PASS ID',
                    'body' => $passCode,
                    'id' => 'ticketId'
                ],
                [
                    'header' => 'LICENSE PLATE',
                    'body' => $licensePlate,
                    'id' => 'licensePlate'
                ],
                [
                    'header' => 'AMOUNT',
                    'body' => $totalAmount,
                    'id' => 'amount'
                ],
                [
                    'header' => 'START',
                    'body' => date('d M Y h:i A', strtotime($checkinTime)),
                    'id' => 'startTime'
                ],
                [
                    'header' => 'END',
                    'body' => date('d M Y h:i A', strtotime($checkOutTime)),
                    'id' => 'endTime'
                ]
            ],
            'barcode' => [
                'type' => 'QR_CODE',
                'value' => $passCode,
                'alternateText' => ''
            ],
            'hexBackgroundColor' => $backgroundColor,
            'logo' => [
                'sourceUri' => ['uri' => $applogourl],
                'contentDescription' => [
                    'defaultValue' => [
                        'language' => 'en-US',
                        'value' => 'ParkEngage Logo'
                    ]
                ]
            ]
        ];

        $claims = [
            'card_title' => 'ParkEngage Pass',
            'iss' => $serviceAccount['client_email'],
            'aud' => 'google',
            'origins' => ['https://parkengage.com'],
            'typ' => 'savetowallet',
            'payload' => [
                'genericClasses' => [$genericClass],
                'genericObjects' => [$genericObject]
            ]
        ];

        $privateKey = $serviceAccount['private_key'];
        $jwt = JWT::encode($claims, $privateKey, 'RS256');
        $walletUrl = "https://pay.google.com/gp/v/save/{$jwt}";

        return $walletUrl;
    }
}
