<?php

namespace App\Http\Controllers\ParkEngage;

use App\Http\Controllers\Controller;
use App\Exceptions\NotFoundException;
use App\Models\AuthorizeNetTransaction;
use Auth;
use Exception;
use Response;
use Hash;
use Illuminate\Http\Request;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\TransactionsApplePay as AuthorizeNetApplePay;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\CommonFunctions;
use App\Classes\Ticketech;
use App\Classes\PromoCodeLib;
use App\Classes\LoyaltyProgram;
use App\Http\Helpers\QueryBuilder;
use App\Models\Rate;
use App\Models\User;
use App\Models\Reservation;
use App\Models\ReservationHistroy;
use App\Models\FlightDetails;
use App\Models\Facility;
use App\Models\Role;
use App\Models\PaymentProfile;
use App\Models\Wallet;
use App\Models\PromoUsage;
use App\Models\PromoCode;
use App\Models\LoyaltyUserAccounts;
use App\Exceptions\ApiGenericException;
use App\Exceptions\TicketechException;
use App\Exceptions\UserNotFound;
use App\Exceptions\UserNotAuthorized;
use App\Exceptions\UserWithEmailExistsException;
use App\Classes\MagicCrypt;
use Carbon\Carbon;
use Log;
use App\Models\CompanyAffilate;
use App\Models\EmailSignup;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
//use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use App\Models\HoursOfOperation;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\UpdateFacilityInventory;
// use Artisan;
use App\Models\FacilityPartnerAvailability;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\ParkEngage\LicensePlateNumber;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\MemberUser;
use App\Models\UserPass;
use App\Models\Ticket;
use App\Services\LoggerFactory;
use Illuminate\Support\Facades\Storage;
use App\Models\Event;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\Notification;
use App\Models\ParkEngage\UserSession;
use App\Classes\PlanetPaymentGateway;
use App\Models\ParkEngage\Gate;
use App\Models\ParkEngage\Cruise;
use App\Classes\DatacapPaymentGateway;
use App\Models\ParkEngage\CruiseSchedule;
use App\Jobs\HubParking;
use App\Models\ParkEngage\MstMake;
use App\Models\ParkEngage\MstModel;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Classes\HeartlandPaymentGateway;
use App\Classes\LatestPromoCodeLib;
use App\Models\ParkEngage\NotificationLog;
use App\Models\ParkEngage\PartnerPaymentGateway;
use App\Models\ParkEngage\TicketExtend;
use Illuminate\Support\Facades\Artisan;
use App\Http\Helpers\GatewayHelper;
use App\Models\ParkEngage\DatacapTransaction;
use Illuminate\Support\Facades\DB;

class PaveMobilityReservationController extends Controller
{

    protected $log;
    protected $checkHubTest;
    protected $reservationlog;
    protected $importReservationLog;

    protected $user;

    protected $partnerPaymentDetails;

    protected $countryCode;

    protected $facility;

    protected $request;
    protected $ticketech;
    protected $billingValidation;
    protected $profileValidation;

    protected $authNet;
    protected $authNetApple;

    protected $cim;

    protected $paymentProfileId;

    protected $validationRules;

    protected $sendAnet = false;
    protected $anonymousAnet = false;

    const DEFAULT_CONFIRM_VAL = 1;
    const APPLE_PAY_FLAG = 1;
    const ADD_EXTRA_DAY_COUNT = 1;

    const DEFAULT_CANCEL_VAL = 2;

    const TWENTY_FOUR_HOURS = 23;
    const DEFAULT_HOURS = 0;

    const REALTIME_WINDOW = 2;
    const END_TIME_EXTRA_MINUTES = 30;
    const DEFAULT_VALUE = 0;
    const RESERVATION_THRESHOLD_TYPE = 2;
    const QUEUE_NAME = 'iqp-inventory';
    const QUEUE_NAME_HUB_PARKING = 'hub-zeag-reservation-create';
    const UPDATE_INVENTORY_TRUE = 1;
    const UPDATE_INVENTORY_FALSE = 0;
    const RESERVATION_TYPE = 'PARKENGAGE';
    const AUTH_LOGIN_ID = '92YXdf5TFA';
    const AUTH_TRANSACTION_ID = '22z48X6XHDr8Znjg';

    const PARTNER_ID = 3307;

    const CREDIT_POINTS = 10;

    public function __construct(Request $request, Ticketech $ticketech)
    {
        $this->request = $request;
        $this->ticketech = $ticketech;
        $this->billingValidation = PaymentProfile::$creditCardValidation;
        $this->profileValidation = [
            'payment_profile_id' => 'required'
        ];
        $this->log = (new LoggerFactory)->setPath('logs/parkengage')->createLogger('booking');
        $this->reservationlog = (new LoggerFactory)->setPath('logs/roc/reservation/create')->createLogger('booking');
        $this->importReservationLog = (new LoggerFactory)->setPath('logs/roc/reservation/import')->createLogger('import');
        $this->checkHubTest = (new LoggerFactory)->setPath('logs/roc/hubapi/checkTest')->createLogger('checkin_test_api');
    }

    /**
     * Get a listing of reservations.
     *
     * @param  $request Request (injected by laravel)
     * @return [type] [description]
     */
    public function get(Request $request)
    {
        $reservations = Reservation::query();

        if ($request->search) {
            $reservations = QueryBuilder::buildSearchQuery($reservations, $request->search, Reservation::$searchFields)
                ->orWhereHas(
                    'user',
                    function ($query) use ($request) {
                        $query
                            ->where('name', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%");
                    }
                )
                ->orWhereHas(
                    'facility',
                    function ($query) use ($request) {
                        $query
                            ->where('full_name', 'like', "%{$request->search}%")
                            ->orWhere('short_name', 'like', "%{$request->search}%");
                    }
                );
        }

        return $reservations->with(
            [
                'user' => function ($query) {
                    $query->select('name', 'email', 'id');
                },
                'facility' => function ($query) {
                    $query->select('full_name', 'short_name', 'id');
                }
            ]
        )
            ->orderBy('id', 'desc')
            ->paginate(20);
    }

    /**
     * Validates a anon reservation codes
     *
     * @param  $reservationCode
     * @param  $ticketechCode
     * @return mixed
     * @throws UserNotAuthorized
     */
    public function validateReservationCodes($reservationCode, $ticketechCode)
    {
        $reservation = Reservation::where('ticketech_code', $ticketechCode)->first();
        if (!$reservation) {
            throw new NotFoundException('No reservation found with that ticketech code');
        }
        if ($reservationCode != $reservation->reservation_code) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }
        return $reservation;
    }


    public function cancelReservationViaCode(Request $request, $reservationCode, $ticketechCode)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $reservation = $this->validateReservationCodes($reservationCode, $ticketechCode);

        return $reservation->cancel();
    }

    public function getReservationById($reservationId)
    {
        $reservation =  Reservation::with('facility', 'facility.geolocations', 'facility.photos', 'transaction')->where('id', $reservationId)->get()->first();
        return  $reservation;
    }

    public function getReservationBarcodeJpgById(Reservation $reservation)
    {
        $user = $this->getUser();
        if ($user->id != $reservation->user_id && !$user->isAdmin) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }

        return $this->respondWithJpg($reservation->generateStubJpg());
    }

    /**
     * @param Reservation $reservation
     * @return Reservation
     * @throws ApiGenericException
     */
    public function cancelReservationById(Request $request, $reservation_id)
    {
        $this->log->info('cancelReservationById ' . json_encode($request));
        $this->log->info('cancelReservationById reservation_id ' . $reservation_id);
        $reservation = Reservation::with(['ticket', 'facility.FacilityPaymentDetails'])->where("ticketech_code", $reservation_id)->whereNull("cancelled_at")->first();

        if ($reservation) {

            if ($reservation->is_ticket != '0' || $reservation->is_ticket != 0) {
                throw new ApiGenericException('Sorry! Booking already consumed.');
            }

            if ($reservation->partner_id == self::PARTNER_ID) {

                $facility = Facility::where('id', $reservation->facility_id)->where('active', '1')->first();
                $this->facility = $facility;

                $refund = $this->makePaymentRefundDataCap($reservation_id);
                if ($refund) {
                    $reservation->cancelled_at = date("Y-m-d H:i:s");
                    $reservation->save();
                } else {
                    throw new ApiGenericException('Refund Payment Gateway Issue');
                }
                // this need to un comment I comment this because getting error : 12-09-2023 : Vijay
                // Artisan::queue('reservation:email-cancellation', ['reservationId' => $reservation->id]);
                //return 'User booking successfully canceled.';  
                return $reservation;
            }

            if ($reservation->total == 0.00 || $reservation->total == 0 || $reservation->total == NULL) {
                //return $reservation->total;
                $reservation->cancelled_at = date("Y-m-d H:i:s");
                $reservation->save();
                $this->log->info('cancelReservationById check total value if section ' . $reservation_id);
                // this need to un comment I comment this because getting error : 12-09-2023 : vijay 
                // Artisan::queue('mapco:cancel-reservation-email', array('id' => $reservation_id));  
                //return 'User booking successfully canceled.';  
                return $reservation;
            } else {
                // return $reservation->total;
                //  Removed the Extra artisan call of email-cancellation
                //    Artisan::queue('reservation:email-cancellation', ['reservationId' =>$reservation->id]);
                if ($reservation->total > 0) {  // check for if paid or zero amount booking

                    // call common refund function 15-09-2023 Ashutosh
                    if (isset($reservation->facility->FacilityPaymentDetails) && !empty($reservation->facility->FacilityPaymentDetails)) {
                        $refundstatus = PlanetPaymentGateway::planetPaymentRefund($reservation->id);
                        $this->log->info("Payment refund Log Planet #:" . json_encode($refundstatus));
                        if ($refundstatus == false) {
                            throw new ApiGenericException('No Transaction Details Found.');
                        }
                    } else {
                        $refundstatus = $this->refundAmount($reservation_id);
                    }

                    //return $refundstatus;
                    if ($refundstatus) {
                        foreach ($refundstatus as $val) {

                            if ($val['Params']['TxState'] == 'AR' || $val['Params']['TxState'] == 'CQ') {
                                $reservation->refund_transaction_id = $val['Params']['TxID'];
                                $reservation->refund_status = $val['Params']['ResultReason'];
                                $reservation->cancelled_at = date("Y-m-d H:i:s");
                                $reservation->save();
                                Artisan::queue('reservation:email-cancellation', ['reservationId' => $reservation->id]);
                                //  Artisan::queue('checkin-email-send', array('signup' => '', 'slug' => 'reservation-cancel', 'id' => $reservation->ticketech_code, 'reset' => ''));
                                $this->log->info("Payment refund  to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($val['Params']));
                                //return 'User booking successfully canceled.';  
                                return $reservation;
                            } else {

                                $reservation->refund_transaction_id = $val['Params']['TxID'];
                                $reservation->refund_status = $val['Params']['ResultReason'];

                                $reservation->save();
                                $this->log->info("Payment refund fail to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($val['Params']));
                                throw new ApiGenericException('Refund Fail due to' . " " . $val['Params']['ResultReason']);
                            }
                        }
                    } else {
                        throw new ApiGenericException('Refund Payment Gateway Issue');
                    }
                }
            }
        } else {
            throw new ApiGenericException('Reservation id not found');
        }
    }

    public function refundAmount($reservation_ticketcode)
    {
        $reservation = Reservation::with('transaction')->where("ticketech_code", $reservation_ticketcode)->first();
        $tran_id = $reservation->transaction->anet_trans_id;
        $amount = - ($reservation->total * 100);

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => config('parkengage.MAPCO_PLANET_REFUND_URL') . $tran_id,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
          "Request": {
              "Type": "payrequestnocardreadbytxid",
              "Version": "W2MXG520",
              "Credentials": {
                  "ValidationID": "ParkEngageTest",
                  "ValidationCode": "ParkEngageTest1!",
                  "ValidationCodeHash": null
              },
              "Params": {
                  "RequesterTransRefNum": "NAU TEST PAYMENT 001",
                   "Amount": "' . $amount . '",
                  "Currency": "USD"
                  }
          }
      }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        //return gettype($response);
        return $refundstatus = json_decode($response, TRUE);
    }

    public function resendEmail(Request $request, Reservation $reservation)
    {
        $reservation->emailReservationToUser($request->email);
        return ['sent' => true];
    }

    /**
     * Get reservation stub HTML
     *
     * @return [type] [description]
     */
    public function getReservationStubHtml(Reservation $reservation)
    {
        return $reservation->generateStubHtml();
    }

    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = (isset($cardData[4]) && $cardData[4] != 'undefined') ? $cardData[4] : '';
        $card_last_four = substr($cardData[1], -4);
        $request->merge(
            [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode,
                'card_last_four' => $card_last_four
            ]
        );
        $this->request = $request;
    }

    public function makeAnonReservation(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $checkFacility = Facility::where('id', $request->facility_id)->where('owner_id', $secret->partner_id)->first();
            if (!$checkFacility) {
                throw new NotFoundException('No garage found with this partner.');
            }
            if ($checkFacility->active != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->is_available != '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if ($checkFacility->facility_booking_type == '1') {
                throw new ApiGenericException('Garage is not available for any booking');
            }
            if (!$checkFacility->accept_cc) {
                throw new ApiGenericException("Garage does not accept credit cards");
            }
        }
        if (isset($this->request->is_card_req) && $this->request->is_card_req == true) {
            $this->setDecryptedCard($request);
            $this->validate($this->request, $this->billingValidation);
        }

        if (isset($this->request->is_booking_also) && $this->request->is_booking_also == true) {
            $this->setDecryptedCard($request);
            $this->validate($this->request, $this->billingValidation);
        }

        if (!(isset($request->is_third_party_promo)) || !($request->is_third_party_promo)) {
            $promocode = $this->validatePromocodeRequest($request); // Check If Promocode Used
        }

        //$member_user_id = '';
        $is_member = '0';
        if ($request->header('X-ClientSecret') != '') {
            if ($this->request->phone != '') {

                $this->countryCode = QueryBuilder::appendCountryCode();
                //$this->countryCode = "+1";
                $existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $secret->partner_id)->first();
                //$existPhone = User::where('email',$request->email)->where('created_by', $secret->partner_id)->first();

                if ($existPhone) {

                    if ($this->request->email != '') {
                        $existPhone->email = $this->request->email;
                    }
                    $existPhone->name = $this->request->name;
                    //$existPhone->type_id = $this->request->type_id;
                    $existPhone->save();
                    $this->user = $existPhone;
                } else {

                    $this->user = User::create(
                        [
                            'name' => $this->request->name,
                            'email' => $this->request->email,
                            'phone' => $this->countryCode . $this->request->phone,
                            //   'password' => Hash::make(str_random(60)),
                            'anon' => false,
                            'user_type' => '5',
                            'created_by' => $secret->partner_id,
                            //'type_id' => $this->request->type_id
                        ]
                    );
                }
            } else {
                $this->user = User::getAnonUserByPartner($this->request->email, $secret->partner_id);
            }
            if ($this->request->member_id != '') {
                $is_member = '1';

                // Test if string contains the word 
                $search = '511';
                $mystring = $this->request->member_id;
                if (preg_match("/{$search}/i", $mystring)) {
                } else {
                    throw new ApiGenericException('Invalid member user.');
                }
                $member = MemberUser::where('member_id', $this->request->member_id)->first();
                if ($member) {
                    $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                    if ($memberUser) {
                        throw new ApiGenericException('Member Id already associate with other user.');
                    }
                }
                //$this->user->member_user_id = $memberUser->id;
                //$this->user->save();
            }

            if ($this->request->is_book_parking == '0' && $this->request->is_booking_also == '0' && $this->request->pass_id == '' && $this->request->is_pass_purchase == '1') {
            } else {
                $isResExist = Reservation::where("user_id", $this->user->id)->whereDate("start_timestamp", '=', date("Y-m-d", strtotime($this->request->arrival)))->whereNull("cancelled_at")->orderBy('id', 'DESC')->first();
                if ($isResExist) {

                    $tickets = Ticket::where('reservation_id', $isResExist->id)->first();
                    if (!$tickets) {

                        $day = date('d', strtotime($this->request->arrival));
                        $monthYear = date('F, Y', strtotime($this->request->arrival));
                        $number = (string) $day;
                        $last_digit = substr($number, -1);
                        $second_last_digit = substr($number, -2, 1);
                        $suffix = 'th';
                        if ($second_last_digit != '1') {
                            switch ($last_digit) {
                                case '1':
                                    $suffix = 'st';
                                    break;
                                case '2':
                                    $suffix = 'nd';
                                    break;
                                case '3':
                                    $suffix = 'rd';
                                    break;
                                default:
                                    break;
                            }
                        }
                        if ((string) $number === '1') $suffix = 'st';
                        throw new ApiGenericException('You already have a booking for ' . $number . $suffix . ' ' . $monthYear . '.');
                    } else {
                        if ($tickets->is_checkout == '0') {
                            throw new ApiGenericException('You already have check-in against today booking.');
                        }
                    }
                }
            }
        } else {
            if ($this->request->phone != '') {

                $this->countryCode = QueryBuilder::appendCountryCode();
                //$this->countryCode = "+1";
                $userFacility = Facility::where('id', $request->facility_id)->first();
                if (!$userFacility) {
                    throw new NotFoundException('No garage found with this partner.');
                }
                $existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $userFacility->owner_id)->first();
                if ($existPhone) {

                    if ($this->request->email != '') {
                        $existPhone->email = $this->request->email;
                    }
                    $existPhone->name = $this->request->name;
                    //$existPhone->type_id = $this->request->type_id;
                    $existPhone->save();
                    $this->user = $existPhone;
                } else {

                    $this->user = User::create(
                        [
                            'name' => $this->request->email,
                            'email' => $this->request->email,
                            'phone' => $this->countryCode . $this->request->phone,
                            //   'password' => Hash::make(str_random(60)),
                            'anon' => false,
                            'user_type' => '5',
                            'created_by' => $userFacility->owner_id,
                            //'type_id' => $this->request->type_id
                        ]
                    );
                }
            } else {
                $this->user = User::getAnonUser($this->request->email);
            }
        }

        if ($this->request->arrival != '') {
            if (strtotime(date("Y-m-d", strtotime($this->request->arrival))) < strtotime(date("Y-m-d"))) {
                throw new ApiGenericException('Parking Date can not be a past date');
            }
        }
        if ($this->request->is_pass_purchase == '1') {
            if ($this->request->pass_id != '') {
                $details = $this->getExistingPassDetails($this->request->pass_id);
            } else {
                $details = $this->makePassPayment();
            }

            $reservation = [];
            if ($this->request->is_book_parking == '1') {

                $reservation = $this->makePassReservation();

                //save zoo atlanta member user id 
                //$this->user->member_user_id = $member_user_id;
                $this->user->is_member = $is_member;
                $this->user->save();

                if ($this->request->member_id != '') {
                    $is_member = '1';

                    $search = '511';
                    $mystring = $this->request->member_id;
                    if (preg_match("/{$search}/i", $mystring)) {
                    } else {
                        throw new ApiGenericException('Invalid member user.');
                    }
                    $member = MemberUser::where('member_id', $this->request->member_id)->first();
                    if ($member) {
                        $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                        if ($memberUser) {
                            throw new ApiGenericException('Member Id already associate with other user.');
                        }
                    } else {
                        $mUser['member_id'] = $this->request->member_id;
                        $member = MemberUser::create($mUser);
                    }
                    $this->user->member_user_id = $member->id;
                    $this->user->save();
                }
                if ($reservation['reservation']) {
                    $res = Reservation::find($reservation['reservation']->id);
                    $res->user_pass_id = $details->id;
                    $res->save();

                    $reservation = $reservation['reservation']->toArray();
                }

                $details->consume_days = $details->consume_days + 1;
                $details->remaining_days = $details->remaining_days - 1;
                $details->save();
            } else {
                $this->user->is_member = $is_member;
                $this->user->save();

                if ($this->request->member_id != '') {
                    $is_member = '1';

                    $search = '511';
                    $mystring = $this->request->member_id;
                    if (preg_match("/{$search}/i", $mystring)) {
                    } else {
                        throw new ApiGenericException('Invalid member user.');
                    }
                    $member = MemberUser::where('member_id', $this->request->member_id)->first();
                    if ($member) {
                        $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                        if ($memberUser) {
                            throw new ApiGenericException('Member Id already associate with other user.');
                        }
                    } else {
                        $mUser['member_id'] = $this->request->member_id;
                        $member = MemberUser::create($mUser);
                    }
                    $this->user->member_user_id = $member->id;
                    $this->user->save();
                }
            }

            return [
                'pass' => $details->withRelations(),
                'reservation' => $reservation
            ];
        } else {
            $this->log->info("Booking about to start");
            $details = $this->makeReservation();
            //save zoo atlanta member user id 
            $this->user->is_member = $is_member;
            $this->user->save();

            if ($this->request->member_id != '') {
                $is_member = '1';

                $search = '511';
                $mystring = $this->request->member_id;
                if (preg_match("/{$search}/i", $mystring)) {
                } else {
                    throw new ApiGenericException('Invalid member user.');
                }
                $member = MemberUser::where('member_id', $this->request->member_id)->first();
                if ($member) {
                    $memberUser = User::where('member_user_id', $member->id)->where('id', '!=', $this->user->id)->first();
                    if ($memberUser) {
                        throw new ApiGenericException('Member Id already associate with other user.');
                    }
                } else {
                    $mUser['member_id'] = $this->request->member_id;
                    $member = MemberUser::create($mUser);
                }
                $this->user->member_user_id = $member->id;
                $this->user->save();
            }
            $reservation_id = $details['reservation']->ticketech_code;
            $request->request->add(['partner_id' => $details['reservation']->partner_id]);
            if (isset($promocode) && $promocode) {
                $this->updatePromocodeChanges($request, $promocode, $reservation_id, $this->user); // Update If Promocode Used
            }

            // Return reservation and charge details to caller
            return [
                'reservation' => $details['reservation']->toArray(),
                'ref_id' => $details['charge']['ref_id']
            ];
        }
    }

    public function getExistingPassDetails($pass_id)
    {

        $pass = UserPass::find($pass_id);
        if (!$pass) {
            throw new ApiGenericException('Invalid Pass Id.');
        }

        if (strtotime($pass->start_date) > strtotime($this->request->arrival)) {
            throw new ApiGenericException('Selected pass will be valid from ' . date('d\t\h F, Y', strtotime($pass->start_date)));
        } else if (strtotime($pass->end_date) < strtotime($this->request->arrival)) {

            throw new ApiGenericException('Selected pass already expired on ' . date('d\t\h F, Y', strtotime($pass->end_date)));
        } else if ($pass->remaining_days <= 0) {

            throw new ApiGenericException('You have already consumed you pass.');
        } else {

            return $pass;
        }
    }

    protected function makePassPayment()
    {
        // Validate the reservation details here
        $this->log->info("Pass about to purchase.");
        $this->setDecryptedCard($this->request);
        $this->validate($this->request, $this->billingValidation);

        $this->validate($this->request, UserPass::$validationRules, UserPass::$validationMessages);


        // Confirm that request rate matches database rate

        // if ($rate['price'] != $this->request->total) {
        //$sentRate = $this->request->pass_total;

        $passRate = Rate::with(['rateType', 'category'])->where('id', $this->request->pass_rate_id)->where('rate_type_id', '7')->where('active', '1')->first();
        if (!$passRate) {
            throw new ApiGenericException(
                'Sent rate does not match database rate, please reset pass rate options and try again.',
                422,
                ['database_rate' => $passRate->price]
            );
        }
        $pass_rate_id = $passRate->id;
        $pass_rate = $passRate->price;

        if (isset($this->request->facility_id)) {
            $this->facility = Facility::find($this->request->facility_id);

            //for processing fee
            if ($this->facility->processing_fee > 0) {

                $pass_rate = $pass_rate + (float)$this->facility->processing_fee;
            }
        }        //check if the total amount is 0

        if ($pass_rate > 0) {
            $secret = OauthClient::where('secret', $this->request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $is_partner = 0;
            if ($this->user->user_type == 3) {
                $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                $is_partner = 1;
            } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
                $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                if ($this->partnerPaymentDetails) {
                    $is_partner = 1;
                } else {
                    throw new NotFoundException('No payment gateway details found for this partner.');
                }
            } else {
                $is_partner = 0;
            }
            if ($is_partner == 1) {
                $this->authNet
                    ->setUser($this->user)
                    /*->isReservation()                
                ->setFacility($this->facility)*/
                    ->isPartner($this->partnerPaymentDetails)
                    ->setBillingAddress($this->getBillingArray());
            } else {
                $this->authNet
                    ->setUser($this->user)
                    /*->isReservation()
                ->setFacility($this->facility)*/
                    ->setBillingAddress($this->getBillingArray());
            }

            if (isset($this->paymentProfileId)) { // Use our logged in users profile
                $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
            } else {  // Otherwise set transaction details manually
                if ($is_partner == 1) {
                    $this->authNet
                        ->isPartner($this->partnerPaymentDetails)->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                } else {
                    $this->authNet->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            }
        }

        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $pass = $this->savePass($passRate);

        // Mobile reservation overstay text comes from mobile end.
        $passMode = "Pass Purchase";

        //check if the total amount is 0
        if ($pass_rate > 0) {

            try {

                // Use our database rate price to create the transaction
                $charge = $this->authNet->createTransaction(
                    // $rate['price'],
                    $pass_rate,
                    "{$passMode} {$pass->id}",
                    config('icon.reservations_account')
                )->isPartner($this->partnerPaymentDetails)->executeTransaction();
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                $this->log->error($e->getMessage());
                $transaction = $this->authNet->getTransaction();
                $pass->anet_transaction_id = $transaction->id;
                $pass->save();
                $pass->delete();
                throw $e;
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $pass->anet_transaction_id = $transaction->id;
            if ($this->request->header('X-ClientSecret') != '') {
                $pass->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
            }

            $pass->save();
        } else {

            // Charge successful, save transaction relationship to it
            $authorized_anet_transaction = new AuthorizeNetTransaction();

            $authorized_anet_transaction->sent = $this->sendAnet;
            $authorized_anet_transaction->anonymous = $this->anonymousAnet;
            $authorized_anet_transaction->user_id = $this->user->id;
            $authorized_anet_transaction->ip_address = \Request::ip();
            $authorized_anet_transaction->total = 0;
            $authorized_anet_transaction->name = $this->getBillingName();
            $authorized_anet_transaction->description = "Pass purchase {$pass->id}";
            $authorized_anet_transaction->response_message = "Zero amount transaction";
            $authorized_anet_transaction->save();

            $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

            $pass->anet_transaction_id = $authorized_anet_transaction->id;

            if ($this->request->header('X-ClientSecret') != '') {
                $pass->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
            }

            $pass->save();
        }


        $pass->emailPassToUser();
        $this->log->info("Pass created : " . $pass->id);
        return $pass;
        /*return [
            'charge' => $charge,
            'pass' => $pass->withRelations()
        ];*/
    }

    protected function checkPassCode()
    {
        $ticket = 'PA' . rand(100, 999) . rand(100, 999);
        $isExist = UserPass::where('pass_code', $ticket)->first();
        if ($isExist) {
            $this->checkPassCode();
        }
        return $ticket;
    }

    protected function savePass($rate)
    {
        $pass = new UserPass(
            [
                'user_id' => $this->user->id,
                'email' => $this->request->email,
                'phone' => $this->request->phone,
                'purchased_on' => date("Y-m-d H:i:s"),
                'start_date' => date("Y-m-d", strtotime($this->request->arrival)),
                'pass_code' => $this->checkPassCode(),
            ]
        );
        if ($rate->category->no_of_days > 0) {
            $endDate = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $rate->category->no_of_days . ' days'));
            $pass->end_date = $endDate;
        }
        $pass->total_days = $rate->max_stay / 24;
        $pass->remaining_days = $rate->max_stay / 24;
        $pass->consume_days = 0;
        $pass->rate_id = $rate->id;

        if (isset($this->facility->processing_fee) && $this->facility->processing_fee > 0) {
            $pass->processing_fee = $this->facility->processing_fee;
            $pass->total = $rate->price + (float)$this->facility->processing_fee;
        } else {
            $pass->total = $rate->price;
        }
        if (isset($this->facility->processing_fee) && $this->facility->processing_fee > 0) {

            $pass->processing_fee = $this->facility->processing_fee;
            $pass->total = $rate->price + (float)$this->facility->processing_fee;
        } else {
            $pass->total = $rate->price;
        }

        // If this is higher the apply bonus will get double applied

        $pass->save();

        $userDetails  = User::where('id', $this->user->id)->first();
        $userDetails->is_member = '1';
        $userDetails->save();


        return $pass;
    }

    public function makeAnonReservationApplePay(Request $request)
    {

        $promocode = $this->validatePromocodeRequest($request); // Check If Promocode Used

        $this->user = User::getAnonUser($this->request->email);

        $details = $this->makeReservationApplePay();

        $reservation_id = $details['reservation']->ticketech_code;
        $request->request->add(['partner_id' => $details['reservation']->partner_id]);
        if ($promocode) {
            $this->updatePromocodeChanges($request, $promocode, $reservation_id, $this->user); // Update If Promocode Used
        }

        // Return reservation and charge details to caller
        return [
            'reservation' => $details['reservation']->toArray(),
            'ref_id' => $details['charge']['ref_id']
        ];
    }

    /**
     * Make a apple pay reservation for a logged in user.
     *
     * @return [type] [description]
     */
    public function makeUserReservationApplePay(Request $request)
    {

        // 1. Set a global flag for first reservation for refer a friend reference for later use
        $isFirstReservation = false;

        // 2. Fetch Logged In User
        $user = User::find(Auth::user()->id);

        // 3. Check if it is user's first reservation
        if (!$user->has_reserved) {
            // echo 'CHECKING RESERVATIONS FOR : ' . $user->id;
            $reservation = Reservation::where('user_id', $user->id)->first();
            if (!$reservation) {
                // echo 'SETTING TRUE';
                $isFirstReservation = true;
            }
        }

        //==============================================================//
        // Direct Parameter Injection for Testing Purpose (REFER A FRIEND)
        // $request->request->add([
        //     'is_wallet_redeemed' => true,
        //     'redeemed_amount' => 5
        // ]);
        // $reservation_id = 123456;
        //==============================================================//

        //==============================================================//
        // Direct Parameter Injection for Testing Purpose (PROMO CODE)
        // $request->request->add([
        //     'is_promocode_redeemed' => true,
        //     'promocode' => 'X2XR98G8',
        //     'redeemed_amount' => 10,
        //     'user_id' => 101322
        // ]);
        //==============================================================//

        $promocode = $this->validatePromocodeRequest($request); // Check If Promocode Used

        $wallet = Wallet::getUserWallet($user->id);
        if ($request->is_wallet_redeemed && isset($request->redeemed_amount_credit)) {
            if ($wallet->balance < $request->redeemed_amount_credit) {
                throw new ApiGenericException('Invalid Transaction, Reservation Can\'t be Processed.');
            }
        }

        $this->user = $this->getUser();
        $this->facility = Facility::find($this->request->facility_id);


        $details = $this->makeReservationApplePay();

        $reservation_id = $details['reservation']->ticketech_code;

        // Reservation Ends Here


        // Wallet Implementation Starts From Here
        $walletConfig = Wallet::getConfig();

        if (isset($request->is_wallet_redeemed)) { // Check if user has redeemed his/her wallet
            if ($request->is_wallet_redeemed && $request->redeemed_amount_credit != "" && $request->redeemed_amount_credit > 0) {
                Wallet::makeTransaction($user->id, 'DEBIT', 'REDEEM', $request->redeemed_amount_credit, null, $reservation_id);
            }
        }

        if ($isFirstReservation) { // If First reservation flag is true
            if ($user->referred_by) {
                $referred_user = User::where('referral_code', $user->referred_by)->first();
                if ($referred_user) {
                    Wallet::makeTransaction($referred_user->id, 'CREDIT', 'REFERRAL', $walletConfig['REFERRAL_BONUS'], $user->id, $reservation_id);
                }
            }
            $user->has_reserved = 1;
            $user->save();
        }
        // Wallet Impleenration Ends Here ...
        $request->request->add(['partner_id' => $details['reservation']->partner_id]);
        if ($promocode) {
            $this->updatePromocodeChanges($request, $promocode, $reservation_id, $user);
        }

        // Return reservation and charge details to caller

        $returnData['reservation'] = $details['reservation']->toArray();
        $returnData['ref_id'] = $details['charge']['ref_id'];
        return $returnData;
    }

    /**
     * Make a reservation for a logged in user.
     *
     * @return [type] [description]
     */
    public function makeUserReservation(Request $request)
    {
        $this->reservationlog->info("mobile Booking Request :" . json_encode($request->all()));

        $reference = rand(1000, 9999) . rand(1000, 9999) . rand(1000, 9999);
        $request->request->add(['ref_id' => $reference]);

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            if (!Facility::where('id', $request->facility_id)->where('id', $secret->partner_id)->get()) {
                throw new NotFoundException('No garage found with this partner.');
            }
        }
        $request->request->add(['client_id' => $request->header('X-ClientSecret')]);
        //convert length in 100 format to get price 
        $length = QueryBuilder::getConvertedLength($request->length);
        $request->request->add(['length_of_stay' => $length]);


        // 2. Fetch Logged In User
        $user = User::find(Auth::user()->id);

        // check reservation exist for same time slot or between same time slot
        $time = Carbon::parse($request->arrival)->addHours($request->length);
        if (intval($request->length) != $request->length) {
            $timarr = explode('.', $request->length);
            // $minute = ('.' . $timarr[1]) * 60;
            $time->addMinutes($timarr[1]);
        }

        // $time = Carbon::parse($request->arrival)->addHours($request->length_of_stay);

        // if (intval($request->length_of_stay) != $request->length_of_stay) {
        //     $timarr = explode('.', $request->length_of_stay);
        //     // $minute = ('.' . $timarr[1]) * 60;
        //     $time->addMinutes($timarr[1]);
        // }

        $endDateTime = $time->subSecond()->format('Y-m-d H:i:s');
        $checkReservationExist = Reservation::where('user_id', $user->id)->whereDate('start_timestamp', '=', date('Y-m-d', strtotime($request->arrival)))->whereNull('cancelled_at')->where('is_ticket', '0')->get();
        $this->reservationlog->info("Reservation Exists Data :" . json_encode($checkReservationExist));
        // if (isset($checkReservationExist) && !empty($checkReservationExist)) {
        if (count($checkReservationExist) > 0) {
            foreach ($checkReservationExist as $key => $val) {
                $endtime = Carbon::parse($val->start_timestamp)->addHours($val->length);

                if (intval($val->length) != $val->length) {
                    $timarr = explode('.', $val->length);
                    // $minute = ('.' . $timarr[1]) * 60;
                    $endtime->addMinutes($timarr[1]);
                }
                $endtimedb = $endtime->subSecond()->format('Y-m-d H:i:s');

                $val->start_timestamp = Carbon::parse($val->start_timestamp);
                $start_timestamp = $val->start_timestamp->format('Y-m-d H:i:s');

                $arrival = Carbon::parse($request->arrival);
                $arrival = $arrival->format('Y-m-d H:i:s');
                if ($arrival >= $start_timestamp && $arrival <= $endtimedb) {
                    throw new ApiGenericException('Reservation already exist between same time slot.');
                } else if ($endDateTime >= $start_timestamp && $endDateTime <= $endtimedb) {
                    throw new ApiGenericException('Reservation already exist between same time slot.');
                } else if ($arrival <= $start_timestamp && $endDateTime >= $endtimedb) {
                    throw new ApiGenericException('Reservation already exist between same time slot.');
                }
            }
        }
        if (!(isset($request->is_third_party_promo)) || !($request->is_third_party_promo)) {
            $promocode = $this->validatePromocodeRequest($request); // Check If Promocode Used
        }

        // check user has current date reservation
        //  $s_date = date('Y-m-d').' 00:00:00' ;
        //  $e_date = date('Y-m-d').' 23:59:59' ;
        //  $is_reservation = Reservation::where('user_id',$user->id)->where('partner_id',$user->created_by)->whereDate('created_at', '>=',$s_date )->whereDate('created_at', '<=', $e_date)->first();   
        // if($is_reservation){
        //     throw new ApiGenericException("Reservation is already in current date");
        // }

        if (!isset($this->request->is_card_req)) {
            $is_card_req = 1;
        } else {
            $is_card_req = $this->request->is_card_req;
        }

        $this->user = $this->getUser();
        $this->facility = Facility::find($this->request->facility_id);
        $this->setCustomTimezone($this->facility->id);

        $details = $this->makeReservation('1');

        $reservation_id = $details['reservation']->ticketech_code;
        $request->request->add(['partner_id' => $details['reservation']->partner_id]);
        if (isset($promocode) && $promocode) {
            $this->updatePromocodeChanges($request, $promocode, $reservation_id, $user);
        }

        // Wallet Implementation Starts From Here
        $walletConfig = Wallet::getConfig();

        if (isset($request->is_wallet_redeemed)) { // Check if user has redeemed his/her wallet
            if ($request->is_wallet_redeemed && $request->redeemed_amount_credit != "" && $request->redeemed_amount_credit > 0) {
                $points = floor($request->redeemed_amount_credit * self::CREDIT_POINTS);
                Wallet::makeTransaction($this->user->id, 'DEBIT', 'REDEEM', $request->redeemed_amount_credit, null, $reservation_id, $points);
            }
        }

        /*$creditPoints = floor(($this->request->total - $this->facility->processing_fee) - $this->request->tax_fee);
        $pointsAmount = number_format($creditPoints/self::CREDIT_POINTS,2);
        Wallet::makeTransaction($this->user->id, 'CREDIT', 'LOYALTY-POINTS', $pointsAmount, null, $reservation_id, $creditPoints);*/

        // Return reservation and charge details to caller
        // dd($details['reservation']);
        $returnData['reservation'] = $details['reservation']->toArray();
        $returnData['ref_id'] = $details['reservation']['mer_reference'];

        $updateJobParams = ['user_id' => $user->id, 'user_email' => $user->email, 'facility' => $details['reservation']->facility->full_name];
        Artisan::queue('pave:push-notification-make-reservation', $updateJobParams);
        return $returnData;
    }

    public function validatePromocodeRequest($request)
    {
        if (isset($request->is_promocode_redeemed) && isset($request->promocode) && isset($request->redeemed_amount)) {
            if ($request->is_promocode_redeemed) {
                if ($request->redeemed_amount <= 0) {
                    throw new ApiGenericException('Invalid Transaction, Discount Amount Can Not Be 0 or Null');
                }
                $request->request->add(['from_reservation' => '1']);
                if (!empty($request->redeemed_amount)) {
                    $totalForCoupon = $request->total + $request->redeemed_amount;
                } else {
                    $totalForCoupon = $request->total;
                }
                $request->request->add(['amount' => $totalForCoupon]);
                // $promoResult2 = PromoCodeLib::validatePromoCode($request);
                $promoResult = LatestPromoCodeLib::validatePromoCodeThirdParty($request);
                //dd( $promoResult->getData()->is_tax_applicable);
                // if (!$promoResult['is_promocode_valid']) {
                if (!$promoResult->getData()->is_promocode_valid) {
                    throw new ApiGenericException($promoResult['message']);
                }

                //saving emaail in email signup if user has not opt out
                if (isset($request->is_opt_out)) {
                    if ($request->is_opt_out == 1) {
                        $this->storeEmailSignup($request);
                    } else if ($request->is_opt_out == 0) {
                        $this->removeEmailSignup($request);
                    }
                }
                //changes due to promo code
                $data = $promoResult->getData();
                if (isset($data->promocode) && is_object($data->promocode)) {
                    $data->promocode->is_tax_applicable = $data->is_tax_applicable;
                    $promoResult->setData($data);
                }

                //end new 
                return $promoResult->getData()->promocode;
                return $promoResult['promocode'];
            }
        }
        return false;
    }

    public function storeEmailSignup($request)
    {
        EmailSignup::firstOrCreate(
            [
                'user_id' => $request->user_id,
                'email' => $request->email
            ]
        );
    }

    public function removeEmailSignup($request)
    {
        $emailSignup = EmailSignup::where('email', $request->email)->first();
        if ($emailSignup) {
            $emailSignup->flag = 0;
            $emailSignup->save();
        }
    }

    public function updatePromocodeChanges($request, $promocode, $reservation_id, $user = null)
    {
        $reservation = Reservation::where('ticketech_code', $reservation_id)->first();
        if (!empty($user->id)) {
            // PIMS - 10480 :Mobile Specific : vijay
            // $userid = $request->partner_id;   
            $userid = (isset($request->user_id) && $request->user_id > 0) ? $request->user_id : $request->partner_id;
            // !! close
        }
        $promoType = $promocode->promo_type_id;
        // dd($userid, $request->partner_id, $promocode->promocode, $reservation_id, $request->redeemed_amount, $request->email);
        switch ($promoType) {
            case 1:
                try {
                    $promoUsage = new PromoUsage();
                    $promoUsage->user_id =  $userid;
                    $promoUsage->partner_id =  $request->partner_id;
                    $promoUsage->promocode = $promocode->promocode;
                    $promoUsage->reservation_id = $reservation_id;
                    $promoUsage->discount_amount = $request->redeemed_amount;
                    //add email to save promo usages @ aaded by sunil due to promo_usages
                    $promoUsage->email = $request->email;
                    $promoUsage->save();
                } catch (\Exception $e) {
                    $reservation->delete();
                    $this->reservationlog->info('Error ' . $e->getMessage() . ' File ' . $e->getFile() . ' Line no :- ' . $e->getLine());
                    throw $e->getMessage();
                }
                break;
            case 2:
                try {
                    $actualPromoCode = PromoCode::where('promocode', $promocode->promocode)->first();
                    $actualPromoCode->is_expired = 1;
                    $actualPromoCode->status = 0;
                    $actualPromoCode->save();
                    if ($user) {
                        $promoUsage = new PromoUsage();
                        $promoUsage->user_id =  $userid;
                        $promoUsage->partner_id =  $request->partner_id;
                        $promoUsage->promocode = $promocode->promocode;
                        $promoUsage->reservation_id = $reservation_id;
                        $promoUsage->discount_amount = $request->redeemed_amount;
                        //add email to save promo usages @ aaded by sunil due to promo_usages
                        $promoUsage->email = $request->email;
                        $promoUsage->save();
                    }
                } catch (\Exception $e) {
                    $reservation->delete();
                    $this->reservationlog->info('Error ' . $e->getMessage() . ' File ' . $e->getFile() . ' Line no :- ' . $e->getLine());
                    throw $e->getMessage();
                }
                break;

            case 3:
                // code...
                break;

            default:
                // code...
                break;
        }
    }

    protected function flightDetails(Reservation $reservation)
    {
        // Flight Details Added
        $flight_details = new  FlightDetails();
        if (isset($this->request->flight_details) && !isset($this->request->is_edit)) {
            $flight_details->create(array_merge($this->request->flight_details, ['reservation_id' => $reservation->id]));
        } else if (isset($this->request->flight_details)) {
            $result = $flight_details->where('reservation_id', $reservation->id)->update($this->request->flight_details);
        }
    }

    public function makeHeartlandPaymentAuth($request, $facility)
    {
        if (Auth::check()) {
            $user_id = Auth::user()->id;
            $partner_id = Auth::user()->created_by;
        } else {
            $user_id = isset($this->user->id) ? $this->user->id : $request->user_id;
            $partner_id = isset($this->user->created_by) ? $this->user->created_by : $request->partner_id;
        }
        if ($request->payment_profile_id != '') {

            // Vijay : 05-12-2024 : New Add Card flow Payment change : PIMS- 11063
            if (isset($request->payment_profile_id) && !empty($request->payment_profile_id) && isset($request->user_id) && $request->user_id > 0) {
                try {
                    $payment_type_id    = $this->facility->FacilityPaymentDetails->facility_payment_type_id;
                    $cardCheck = GatewayHelper::getRegisterUserProfileId($payment_type_id, $request->card_last_four, $request->expiry, $request->user_id, $this->facility);
                    $request->request->add(['payment_profile_id' => $cardCheck->token]);
                } catch (\Throwable $th) {
                    throw new ApiGenericException("Payment Failed!");
                    throw new ApiGenericException($th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine());
                }
            }
            // dd($cardCheck);
            // Close here !!!
            $this->reservationlog->info("Request Data Heartland with payment profile: " . json_encode($request->all()));
            $result = HeartlandPaymentProfile::whereNull('deleted_at')->where('card_holder_id', $request->payment_profile_id)->where('user_id', $user_id)->first();

            if ($result) {
                $card_month = substr($result->expiry, 0, 2);
                $card_year = substr($result->expiry, -2);
                $amount = number_format(($request->total - $request->charged_amount), 2);
                $request->merge([
                    'expiration_month' => $card_month,
                    'expiration_year' => $card_year,
                    'card_name' => $result->name,
                    'Amount' => $amount,
                    'total' => $amount,
                    'token' => $result['token'],
                    'card_last_four' => $result->card_last_four,
                    'user_consent' => 0,
                    'zip_code' => $result->zipcode
                ]);
                $this->reservationlog->info("Updated Request Data Heartland : " . json_encode($request->all()));
                try {
                    $paymentResponse = HeartlandPaymentGateway::makePreAuthPaymentHeartland($request, $facility);
                } catch (Exception $e) {
                    $this->reservationlog->info($e->getMessage());
                    if (!isset($this->request->is_edit))
                        $request->reservation->delete();
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card");
                }

                $this->reservationlog->info("Response Data Heartland : " . json_encode($paymentResponse));

                if (($paymentResponse == NULL) || ($paymentResponse == FALSE)) {
                    if (!isset($this->request->is_edit))
                        $request->reservation->delete();
                    throw new ApiGenericException('Payment failed. Please correct your card details or try again with a different card');
                }

                if ($paymentResponse->responseMessage == 'APPROVAL') {
                    $request->request->add(['multi_token' => isset($paymentResponse->token) ? $paymentResponse->token : '']);
                    $request->request->add(['expiration' => $result->expiry]);
                    $request->request->add(['partner_id' => $partner_id]);
                    $transactionStatus = HeartlandPaymentGateway::makeHeartlandPaymentTransaction($request, $paymentResponse, $user_id);

                    if (!$transactionStatus) {
                        if (!isset($this->request->is_edit))
                            $request->reservation->delete();
                        throw new ApiGenericException("Record Not Added");
                    }
                    return $transactionStatus;
                } else {
                    if (!isset($this->request->is_edit))
                        $request->reservation->delete();
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card");
                }
            } else {
                $this->reservationlog->info("Payment failed. Please correct your card details or try again with a different card");
            }
        } else if ($request->nonce) {
            $this->reservationlog->info("Request Data Heartland: " . json_encode($request->all()));
            $this->setDecryptedCard($request);
            $card_month = substr($request->expiration_date, 0, 2);
            $card_year = substr($request->expiration_date, -2);
            $request->request->add(['expiration_month' => $card_month]);
            $request->request->add(['expiration_year' => $card_year]);
            //heartland otu token
            // otuPaymentTokenHeartland
            $otu_token = HeartlandPaymentGateway::otuPaymentTokenHeartland($request, $facility);
            $this->reservationlog->info("Heartland OTU Token: " . json_encode($otu_token));
            //dd($otu_token);
            if ($otu_token) {
                try {
                    if (isset($otu_token["error"]["code"]) == "2" || isset($otu_token["error"]["code"]) == 2) {
                        if (!isset($this->request->is_edit))
                            $request->reservation->delete();
                        throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card");
                    }
                    $amount = $request->total;
                    $request->request->add(['Amount' => $amount]);
                    $request->request->add(['token' => $otu_token['token_value']]);
                    $request->request->add(['name' => $request->name_on_card]);
                    $card_last_four = substr($request->card_number, -4);
                    $request->request->add(['card_last_four' => $card_last_four]);
                    $request->request->add(['user_consent' => 1]);
                    $paymentResponse = HeartlandPaymentGateway::makePreAuthPaymentHeartland($request, $facility);
                } catch (Exception $e) {
                    $this->reservationlog->info("Error in Heartland Payment with card --" . json_encode($e->getMessage()));
                    if (!isset($this->request->is_edit))
                        $request->reservation->delete();
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card");
                }
                $this->log->info("Response Data Heartland : " . json_encode($paymentResponse));

                if (($paymentResponse == NULL) || ($paymentResponse == FALSE)) {
                    if (!isset($this->request->is_edit))
                        $request->reservation->delete();
                    throw new ApiGenericException('Payment failed. Please correct your card details or try again with a different card');
                }

                if ($paymentResponse->responseMessage == 'APPROVAL') {
                    $request->request->add(['multi_token' => isset($paymentResponse->token) ? $paymentResponse->token : '']);
                    $request->request->add(['expiration' => $request->expiration_date]);
                    $request->request->add(['partner_id' => $partner_id]);
                    $transactionStatus = HeartlandPaymentGateway::makeHeartlandPaymentTransaction($request, $paymentResponse, $user_id);
                    if ($request->user_consent == 1) {
                        $cardCheck = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $request->user_id)->where('card_last_four', $card_last_four)->first();

                        if ($request->user_consent == 1 && !$cardCheck) {
                            if (!empty($paymentResponse->token)) {
                                $this->reservationlog->info("Card Saved:");
                                $response = HeartlandPaymentGateway::saveHeartlandCard($paymentResponse, $request->user_id, $request);
                            } else {
                                $this->reservationlog->info("Card Not Saved:");
                                $request->request->add(['is_card_added' => "0"]);
                            }
                        }
                    }
                    return $transactionStatus;
                } else {
                    if (!isset($this->request->is_edit))
                        $request->reservation->delete();
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card");
                }
            } else {
                if (!isset($this->request->is_edit))
                    $request->reservation->delete();

                $this->reservationlog->info("Heartland gateway is down");
                throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card");
            }
        } else {
            if (!isset($this->request->is_edit))
                $request->reservation->delete();
            throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
        }
    }

    public function makeDatacapPaymentPreAuth($request, $facility)
    {
        $this->reservationlog->info("Pre Auth Start " . $request->reservation->ticketech_code);
        try {
            if (Auth::check()) {
                $user_id = Auth::user()->id;
                $partner_id = Auth::user()->created_by;
            } else {
                $user_id = isset($this->user->id) ? $this->user->id : $request->user_id;
                $partner_id = isset($this->user->created_by) ? $this->user->created_by : $request->partner_id;
            }

            $amount = number_format(($request->total - $request->charged_amount), 2);
            if ($request->is_edit) {
                $amount = number_format((($request->reservation->total + $request->total) - $request->reservation->charged_amount), 2);
            }
            $paymentEnv = $facility->FacilityPaymentDetails->datacap_payment_env ?? config('parkengage.DATACAP_PAYMENT_ENV');
            $amount = ($paymentEnv == 'test') ? $facility->FacilityPaymentDetails->test_amount : $amount;

            if (!empty($request->payment_profile_id)) {
                $this->reservationlog->info("Payment by Saved Card");
                if (!empty($request->creater_id) && !empty($request->payment_profile_id)) {
                    $user_id = $request->user_id;
                }
                try {
                    $payment_type_id = $facility->FacilityPaymentDetails->facility_payment_type_id;
                    $this->reservationlog->info("Find Card: facility_payment_type_id: {$payment_type_id}, Last Four: {$request->card_last_four}, Expiry: {$request->expiry}, User id: {$request->user_id}, Facility id: {$facility->id}");
                    $cardCheck = GatewayHelper::getRegisterUserProfileId($payment_type_id, $request->card_last_four, $request->expiry, $request->user_id, $facility);
                    if(!$cardCheck){
                        $this->reservationlog->info("Card not Found");
                        $cardCheck = DatacapTransaction::select( '*', DB::raw('card_last_four as payment_last_four') )->where('reservation_id', $request->reservation->id)->first();
                    }
                    $this->reservationlog->info("Card details: " . json_encode($cardCheck));
                    $request->request->add(['payment_profile_id' => $cardCheck->token]);
                } catch (\Throwable $th) {
                    $this->reservationlog->info("Card details: " . json_encode($cardCheck));
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
                }

                $data = [
                    'Amount' => $amount,
                    'Token' => $cardCheck->token,
                    'CardHolderID' => "Allow_V2"
                ];

                $this->reservationlog->info("Actual Request for Datacap : " . json_encode($data));

                try {
                    $paymentResponse = (object)DatacapPaymentGateway::makePreAuthPaymentDataCap($data, $facility);
                    $this->reservationlog->info("Response from Datacap: " . json_encode($paymentResponse));
                } catch (Exception $e) {
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
                }

                if (!$paymentResponse || !in_array($paymentResponse->Message, ['APPROVED', 'APPROVAL'])) {
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
                }

                $request->request->add(['multi_token' => $paymentResponse->token ?? '', 'expiration' => $cardCheck->expiry, 'partner_id' => $partner_id, 'Amount' => $data['Amount']]);
                return DatacapPaymentGateway::makeDatacapPaymentTransaction($request, $paymentResponse, $user_id);
            } elseif (!empty($request->nonce)) {
                $this->reservationlog->info("Request Data for Datacap: " . json_encode($request->all()));
                $this->setDecryptedCard($request);
                $card_month = substr($request->expiration_date, 0, 2);
                $card_year = substr($request->expiration_date, -2);
                $request->request->add(['expiration_month' => $card_month]);
                $request->request->add(['expiration_year' => $card_year]);

                $otu_token = DatacapPaymentGateway::otuPaymentTokenDataCap($this->request, $facility);
                $this->reservationlog->info("Datacap OTU Token: " . json_encode($otu_token));
                if (!isset($otu_token["Token"])) {
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
                }

                try {
                    $card_last_four = substr($request->card_number, -4);
                    $request->request->add(['Amount' => $amount, 'token' => $otu_token['Token'], 'Token' => $otu_token['Token'], 'card_last_four' => $card_last_four]);
                    $paymentResponse = (object)DatacapPaymentGateway::makePreAuthPaymentDataCap(['Amount' => $amount, 'Token' => $otu_token['Token'], 'CardHolderID' => "Allow_V2"], $facility);
                    $this->reservationlog->info("Response from Datacap: " . json_encode($paymentResponse));
                } catch (Exception $e) {
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
                }

                if (!$paymentResponse || !in_array($paymentResponse->Message, ['APPROVED', 'APPROVAL'])) {
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
                }

                // Card Saved Logic
                if ($request->user_consent == 1) {
                    $cardCheck = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $request->user_id)->where('card_last_four', $card_last_four)->first();
                    if ($request->user_consent == 1 && !$cardCheck) {
                        if (!empty($paymentResponse->Token)) {
                            $this->reservationlog->info("Card Saved:");
                            $request->request->add(['is_card_added' => "1"]);
                            $response = DatacapPaymentGateway::saveDatacapCard($paymentResponse, $request->user_id, $request);
                        } else {
                            $this->reservationlog->info("Card Not Saved:");
                            $request->request->add(['is_card_added' => "0"]);
                        }
                    }
                }

                return DatacapPaymentGateway::makeDatacapPaymentTransaction($request, $paymentResponse, $user_id);
            } else {
                throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
            }
            $this->reservationlog->info("Pre Auth End");
        } catch (ApiGenericException $e) {
            $this->reservationlog->info("Payment Error: " . $e->getMessage() . ' File ' . $e->getFile() . ' Line no :- ' . $e->getLine());
            if (!isset($this->request->is_edit)) {
                $request->reservation->delete();
            }
            throw $e;
        } catch (Exception $e) {
            $this->reservationlog->info("Unexpected Error: " . $e->getMessage());
            throw new ApiGenericException("An unexpected error occurred. Please try again later.");
        }
    }

    protected function configPaymentGateway($payment_type_id, $mid)
    {
        $paymentGateways = PartnerPaymentGateway::where(['partner_id' => $this->facility->owner_id])->where(['facility_payment_type_id' => $payment_type_id])->where(['payment_mid' => $mid])->first();
        $this->request->merge(['facility_payment_type_id' => $payment_type_id, 'partner_payment_gateway_id'     => isset($paymentGateways) ? $paymentGateways->id : NULL]);
        $this->reservationlog->info("facility_payment_type_id: " . $this->request->partner_payment_gateway_id . " , partner_payment_gateway_id: " . $this->request->partner_payment_gateway_id);
    }

    /**
     * Given a user, make a reservation for that user.
     * TODO: This functionality should probably be drawn out into a ReservationService
     *
     * @param  User $user [description]
     * @return [type]       [description]
     */
    protected function makeReservation()
    {
        // Validate the reservation details here
        if (isset($this->request->use_overstay) && $this->request->use_overstay > 0) {
            return $this->updateReservationWithOverstay();
        }

        $secret = CommonFunctions::partnerCheck($this->request);
        $this->request->request->add(['client_id' => $secret->secret]);
        if (!(isset($request->is_third_party_promo)) || !($request->is_third_party_promo)) {
            $promocode = $this->validatePromocodeRequest($this->request); // Check If Promocode Used
            //if tax is applicablle
            if (isset($promocode->is_tax_applicable) && $promocode->is_tax_applicable === "1") {
                $this->request->request->add(['is_taxable_promocode' => '1']);
            }
        }

        $this->request->request->add(['is_card_added' => "1"]);

        //$this->validate($this->request, Reservation::$validationRules, Reservation::$validationMessages);
        $this->facility = Facility::with(['FacilityPaymentDetails', 'facilityConfiguration'])->find($this->request->facility_id);

        //Dev: Sagar | PIMS-11888 | prepay 
        $isPrepayEnabled = $this->facility->facilityConfiguration->is_prepay_enabled == 1 ? true : false;

        // Users can choose to apply bonus time to their reservation for a small fee
        $useBonus = (bool) $this->request->use_bonus;

        $couponThresholdPrice = isset($this->request->coupon_threshold_price) ? ($this->request->coupon_threshold_price) : (self::DEFAULT_VALUE);

        $isMember = $this->request->member_id != '' ? 1 : 0;

        $this->reservationlog->info("Rate Calculation Against Reservation 11 ");
        // rate functionality change by ashutosh 13-09-2023 
        if ($this->facility->rate_duration_in_hours > 0 && $this->facility->rate_per_hour > 0 && $this->facility->rate_free_minutes > 0 && $this->facility->rate_daily_max_amount > 0) {
            $rate = $this->facility->rateForReservationByPassRateEngine($this->request->arrival, $this->request->length_of_stay, $useBonus, false, null, false, $couponThresholdPrice, '0', $isMember);
        } else {
            //Dev: Sagar | PIMS-11888 | prepay condition added
            $rate = $this->facility->rateForReservationOnMarker($this->request->arrival, $this->request->length_of_stay, $useBonus, false, null, false, $couponThresholdPrice, '0', $isMember, false, false, false, $isPrepayEnabled);
        }

        $this->reservationlog->info("Rate Calculated Against Reservation");

        if (!$rate) {
            $this->reservationlog->info("No rate found in database for this reservation.");
            throw new ApiGenericException('No rate found in database for this reservation.', 422);
        }
        $tax_rate = $this->facility->getTaxRate($rate, '1');
        $processingFee = $this->facility->getProcessingFee('1');

        $sentRate = $this->request->total;
        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $sentRate = ($sentRate + $this->request->redeemed_amount);
        }


        if (isset($this->request->is_edit)) {
            $this->reservationlog->info("Reservation in Edit/ Extend Mode: " . $this->request->reservation_mode);
            $res_mode = $this->request->reservation_mode;
            $reservation = Reservation::find($this->request->reservation_id);
            $sentRate += $reservation->total;
            if ($this->facility->tax_rate_type == 0)
                $tax_rate += $reservation->tax_fee;
            $processingFee = $reservation->processing_fee;
        }
        // dd($rate['price'], $sentRate, $reservation->total, $tax_rate, $processingFee, $this->request->length_of_stay, $this->request->total);

        //for credit user
        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $sentRate = ($sentRate + $this->request->redeemed_amount_credit);
        }

        //for Processing Fee
        if ($processingFee > 0) {
            $this->reservationlog->info("Processing Fee Applicable");
            if (isset($processingFee) && $processingFee > 0) {
                $sentRate = ($sentRate - $processingFee);
            }
        }

        // Lokesh this is for Multi Tax
        if ($sentRate > 0) {
            if (isset($tax_rate) && $tax_rate > 0) {
                $sentRate = ($sentRate - $tax_rate);
            }
        }

        //add use bonus if used
        if ($useBonus) {
            $rate['price'] = $rate['price'] + $this->facility->reservation_bonus_rate;
        }

        // for loyalty points:
        if (isset($this->request->is_loyalty_redeemed) && $this->request->is_loyalty_redeemed  && $this->request->loyalty_points > 0) {
            // check loyalty account is active
            if ($this->user->is_loyalty_active != LoyaltyProgram::CONST_ONE) {
                throw new ApiGenericException('Your loyalty account is inactive.');
            }
        }

        $ratesData['database_rate']         = $rate['price']; // Rate get from rate api
        $ratesData['sent_rate']             = $sentRate;    // rate sent from request => total
        $ratesData['tax_rate']              = $tax_rate;
        $ratesData['processing_fee']        = $processingFee;
        $ratesData['discount']              = $this->request->discount;
        $ratesData['redeemed_amount']       = $this->request->redeemed_amount;

        $this->reservationlog->info("Rate from API: database_rate " .  json_encode($ratesData));

        // if (filter_var($sentRate, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $rate['price']) { : Lokesh 
        if (bccomp($sentRate, $rate['price'], 2) !== 0) { // Lokesh: Change for Reservation Price comparison coming wrong on above conditions discuss with vijay
            throw new ApiGenericException(
                'Your parking amount is revised. please refresh your screen.',
                422,
                ['sent_rate' => $sentRate, 'database_rate' => $rate['price'], 'isratemismatch' => 1]
            );
        }

        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        if (!isset($this->request->is_edit)) {

            $this->reservationlog->info("Reservation Save Start on Create");

            $reservation = $this->saveReservation($rate, $useBonus);
            $reservation->user->saveUserHistroy($this->request, $reservation->id, 'reservation');

            $this->reservationlog->info("Flight Operation in Reservation Create ");

            $reservation->license_plate = $this->request->license_plate;
            if (isset($this->request->device_type) && !empty($this->request->device_type)) {
                $reservation->device_type = $this->request->device_type;
            }
            //on behalf changes to save email and phone
            if (isset($this->request->email) && !empty($this->request->email)) {
                $reservation->email = $this->request->email;
            }
            if (isset($this->request->phone) && !empty($this->request->phone)) {
                $reservation->phone = $this->request->phone;
            }
            if (isset($this->request->onBehalf) && !empty($this->request->onBehalf) && $this->request->onBehalf == '1') {
                $reservation->on_behalf = $this->request->creater_id;
            }
            //end
            $reservation->save();

            // Flight Details
            $this->flightDetails($reservation);
            $res_mode = 0;

            $this->reservationlog->info("Reservation Save End on Create");
        }

        $reservation->mer_reference = isset($this->request->ref_id) ? $this->request->ref_id : '';
        $this->request->request->add(['reservation' =>  $reservation]);
        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";
        if (isset($this->request->warning_on_reservation) && (($this->request->warning_on_reservation) > 0)) {
            $reservation->warning_on_reservation = $this->request->warning_on_reservation;
            $reservation->warning_on_reservation_msg = $this->request->warning_on_reservation_msg_email;
        }

        //check if the total amount is 0
        if ($this->request->total > 0) {
            $paymentStatus = "";
            $this->reservationlog->info("======== Charge Amount is > 0 Select Payment Gateway Start ======");
            if (isset($this->facility->FacilityPaymentDetails) && !empty($this->facility->FacilityPaymentDetails) && ($this->facility->FacilityPaymentDetails->facility_payment_type_id) == '1') {

                // Multiple Gateway Config:
                $payment_type_id = $this->facility->FacilityPaymentDetails->facility_payment_type_id;
                $mid             = $this->facility->FacilityPaymentDetails->planet_merchant_id;
                $this->configPaymentGateway($payment_type_id, $mid);

                if (($this->request->transaction_id) && in_array($this->request->paymentMethods, [config('parkengage.APPLE_PAY'), config('parkengage.GOOGLE_PAY')])) {
                    $this->reservationlog->info("Reservation Data :" . json_encode($reservation));
                    $transactionSettle = DataTransGateway::dataTransSettleTransaction($reservation->id, $this->request);
                    $this->reservationlog->info("Transaction Settle Datatrans Payment :" . json_encode($transactionSettle));
                    //dd($transactionSettle);
                    if ($transactionSettle["error"]["message"]) {
                        if (!isset($this->request->is_edit))
                            $reservation->delete();
                        throw new ApiGenericException($transactionSettle["error"]["message"]);
                    }
                    $paymentStatus = DataTransGateway::saveDataTransTransaction($this->request, $this->request->user_id);
                    $this->reservationlog->info("Transaction Save in Authrize net table Datatrans Payment :" . json_encode($paymentStatus));

                    $reservation->payment_gateway = 'dataTrans';
                    $reservation->payment_method = $this->request->paymentMethods;
                } else {
                    // make planet payment
                    $paymentStatus = $this->makePlanetPayment($this->request);
                    $this->reservationlog->info("== Payment Status by Planet Card Start == : " . json_encode($paymentStatus));
                    if ($paymentStatus['Status'] == 'Error') {
                        if (!isset($this->request->is_edit))
                            $reservation->delete();
                        throw new ApiGenericException("Payment Failed. Try Again !!");
                    }
                    if ($paymentStatus == 'Payment Failed.') {
                        if (!isset($this->request->is_edit))
                            $reservation->delete();
                        throw new ApiGenericException("Payment Failed. Try Again !!");
                    }
                    $reservation->session_id =  isset($paymentStatus['planet_token']) ? $paymentStatus['planet_token'] : NULL;
                    $reservation->anet_transaction_id = $paymentStatus->id;
                    $reservation->payment_gateway = 'planet';
                    //add ref key 
                    QueryBuilder::setReferenceKey($paymentStatus->id, $reservation->ticketech_code);
                    $this->reservationlog->info(" == Payment Status by Planet Card End == ");
                }
            } else if (isset($this->facility->FacilityPaymentDetails) && !empty($this->facility->FacilityPaymentDetails) && ($this->facility->FacilityPaymentDetails->facility_payment_type_id) == '2') {

                // Multiple Gateway Config:
                $payment_type_id = $this->facility->FacilityPaymentDetails->facility_payment_type_id;
                $mid             = $this->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                $this->configPaymentGateway($payment_type_id, $mid);
                // dd($this->request->partner_payment_gateway_id);

                $this->reservationlog->info("== Payment Status by Datacap Card Start");
                // dd($this->facility->facilityConfiguration->pre_post_charge_reservation);
                if (isset($this->facility->facilityConfiguration) && $this->facility->facilityConfiguration->pre_post_charge_reservation) {
                    if (isset($this->request->creater_id) && !empty($this->request->creater_id) && isset($this->request->payment_profile_id)) {
                        $this->request->request->add(['user_id' => $this->request->creater_id]);
                    }

                    $this->reservationlog->info("Post Charge:");
                    $paid_amount = isset($this->request->is_edit) ? 0 : $reservation->total;
                    $this->request->merge(['reservation_id' => $reservation->id, 'paid_amount' => $paid_amount, 'charge_amount' => $reservation->charge_amount, 'ticket_code' => $reservation->ticketech_code]);
                    $paymentStatus = $this->makeDatacapPaymentPreAuth($this->request, $this->facility);
                    $reservation->is_charged = '0';
                } else {
                    //dd('d111');
                    $this->reservationlog->info("Pre Charge:");
                    $paymentStatus = $this->makeDataCapPayment($this->request);
                    $this->reservationlog->info("== Response ==: " . json_encode($paymentStatus));
                    if ($paymentStatus == 'Payment Failed.') {
                        if (!isset($this->request->is_edit))
                            $reservation->delete();
                        throw new ApiGenericException("Payment Failed. Try Again !!");
                    }
                    $reservation->anet_transaction_id = $paymentStatus->id;
                    // These are required on payment charaged or refund 
                    $reservation->session_id =  isset($this->request->token) ? $this->request->token : NULL;
                    $reservation->payment_token =  isset($this->request->token) ? $this->request->token : NULL;
                    //add ref key 
                    QueryBuilder::setReferenceKey($paymentStatus->id, $reservation->ticketech_code);
                    //add ref key 
                }

                $reservation->payment_gateway = 'datacap';
                $this->reservationlog->info(" == Payment Datacap Card End == ");
            } else if (isset($this->facility->FacilityPaymentDetails) && !empty($this->facility->FacilityPaymentDetails) && ($this->facility->FacilityPaymentDetails->facility_payment_type_id) == '4') {

                // Multiple Gateway Config:
                $payment_type_id = $this->facility->FacilityPaymentDetails->facility_payment_type_id;
                $mid             = $this->facility->FacilityPaymentDetails->heartland_mid;
                $this->configPaymentGateway($payment_type_id, $mid);

                // make hartland payment
                $this->reservationlog->info("Payment by Heartland Card Start");
                if (isset($this->facility->facilityConfiguration) && $this->facility->facilityConfiguration->pre_post_charge_reservation) {
                    $this->reservationlog->info("Post Charge:");
                    $paid_amount = isset($this->request->is_edit) ? 0 : $reservation->total;
                    $this->request->merge(['reservation_id' => $reservation->id, 'paid_amount' => $paid_amount, 'charge_amount' => $reservation->charge_amount]);
                    $paymentStatus = $this->makeHeartlandPaymentAuth($this->request, $this->facility);
                    $reservation->is_charged = '0';
                } else {
                    $this->reservationlog->info("Pre Charge:");
                    $paymentStatus = $this->makeHeartlandPayment($this->request, $reservation);
                    $reservation->anet_transaction_id = $paymentStatus->id;
                    $reservation->is_charged = '1';
                    //add ref key 
                    QueryBuilder::setReferenceKey($paymentStatus->id, $reservation->ticketech_code);
                    //add ref key 
                }
                $this->reservationlog->info("Payment Status: " . json_encode($paymentStatus));

                if ($paymentStatus == null) {
                    if (!isset($this->request->is_edit))
                        $reservation->delete();
                }
                $reservation->session_id =  isset($this->request->token) ? $this->request->token : NULL;
                $reservation->payment_token =  isset($this->request->token) ? $this->request->token : NULL;
                $reservation->payment_gateway = 'heartland';
                $this->reservationlog->info(" == Payment Status by Heartland Card End == ");
            } else {
                if (!isset($this->request->is_edit))
                    $reservation->delete();
                throw new ApiGenericException("Error in Payment Method.");
            }
            // dd($paymentStatus);
            if (isset($paymentStatus['Status']) && $paymentStatus['Status'] == 'Error') {
                if (!isset($this->request->is_edit))
                    $reservation->delete();
                throw new ApiGenericException("Card details are invalid");
            }
            $this->reservationlog->info("======== Select Payment Gateway End ======");

            // $transaction = $this->authNet->getTransaction();
            $reservation->ticketech_guid = rand(100000, 999999);
            $reservation->partner_id =  $this->user->created_by;

            if ($this->request->week != '') {
                $weekDays = 7 * $this->request->week;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }
            if ($this->request->month != '') {
                $weekDays = date('t') * $this->request->month;
                $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
                $reservation->end_timestamp = $endTimestamp;
                $reservation->length = $weekDays * 24;
                $reservation->days = $weekDays;
            }

            if ($this->request->member_id != '') {
                $reservation->is_daily = '1';
            }

            if ($this->request->no_of_visitor != '') {
                $reservation->no_of_visitor = $this->request->no_of_visitor;
            }

            if (isset($this->request->paymentMethods) && ($this->request->paymentMethods == "APL" || $this->request->paymentMethods == "GPAY")) {
                $reservation->payment_method = $this->request->paymentMethods;
            }


            // $reservation->vehicle_id = isset($this->request->vehicle_id) ? $this->request->vehicle_id : '';
            $reservation->tax_fee = $this->request->tax_fee;
            $reservation->license_plate = isset($vehicle->license_plate_number) ? $vehicle->license_plate_number : '';

            // added new fields for reservation booking source
            if (isset($this->request->booking_source) && !empty($this->request->booking_source)) {
                $reservation->booking_source = $this->request->booking_source;
            }
            // save licence plate from resquest
            if (isset($this->request->license_plate) && !empty($this->request->license_plate)) {
                $reservation->license_plate = $this->request->license_plate;
            }
            $reservation->cruise_id = isset($this->request->cruise_id) ? $this->request->cruise_id : '';
            $reservation->schedule_id = isset($this->request->schedule_id) ? $this->request->schedule_id : '';
            $reservation->save();

            $this->reservationlog->info("======== Charge Amount is > 0  End ======");
        } else {
            if (isset($this->facility->facilityConfiguration) && !$this->facility->facilityConfiguration->pre_post_charge_reservation) {
                $this->reservationlog->info("Zero Amount Pre Payment .");

                $ticketech_guid = rand(100000, 999999);
                $reservation->partner_id =  $this->user->created_by;
                // Charge successful, save transaction relationship to it
                $authorized_anet_transaction = new AuthorizeNetTransaction();

                $authorized_anet_transaction->sent = $this->sendAnet;
                $authorized_anet_transaction->anonymous = $this->anonymousAnet;
                $authorized_anet_transaction->user_id = $this->user->id;
                $authorized_anet_transaction->ip_address = \Request::ip();
                $authorized_anet_transaction->total = 0;
                $authorized_anet_transaction->name = $this->getBillingName();

                $authorized_anet_transaction->description = "Reservation for zero amount {$reservation->id}, Ticketech Code {$reservation->ticketech_code} ";

                if (
                    isset($this->request->is_loyalty_redeemed)
                    && $this->request->is_loyalty_redeemed
                ) {
                    $authorized_anet_transaction->response_message = "Zero amount loyalty transaction";
                } else {
                    $authorized_anet_transaction->response_message = "Zero amount transaction";
                }
                $authorized_anet_transaction->save();

                $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

                $reservation->anet_transaction_id = $authorized_anet_transaction->id;
                $reservation->ticketech_guid = $ticketech_guid;

                if ($this->request->length) {

                    // Vijay : change as per length of stay login changed 
                    $lengthInMints = QueryBuilder::getLengthInMints($this->request->length);
                    $now = Carbon::parse('now')->format('Y-m-d H:i:s');
                    $arrival_time = Carbon::parse($this->request->arrival)->format('Y-m-d H:i:s');
                    $endTimestamp = Carbon::parse($arrival_time)->addMinutes($lengthInMints)->format('Y-m-d H:i:s');

                    $reservation->end_timestamp = $endTimestamp;
                    $reservation->length = $this->request->length;
                    $reservation->days = $this->request->length;
                    $this->reservationlog->info("Converted Length In Mints : {$lengthInMints} ");
                    $this->reservationlog->info("reservation end time Arrival : {$this->request->arrival} and length {$this->request->length} End time : {$endTimestamp} ");
                }


                if ($this->request->member_id != '') {
                    $reservation->is_daily = '1';
                }


                if ($this->request->header('X-ClientSecret') != '') {
                    $reservation->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
                }

                if ($this->request->no_of_visitor != '') {
                    $reservation->no_of_visitor = $this->request->no_of_visitor;
                }
                $reservation->tax_fee = $this->request->tax_fee;
                $reservation->license_plate = isset($vehicle->license_plate_number) ? $vehicle->license_plate_number : '';
                $this->reservationlog->info("Check License Plate : {$reservation->license_plate} ");
                // added new fields for reservation booking source
                if (isset($this->request->booking_source) && !empty($this->request->booking_source)) {
                    $reservation->booking_source = $this->request->booking_source;
                    $this->reservationlog->info("Check Booking source : {$reservation->license_plate} ");
                }
                // save licence plate from resquest
                if (isset($this->request->license_plate) && !empty($this->request->license_plate)) {
                    $reservation->license_plate = $this->request->license_plate;
                    $this->reservationlog->info("Check RServation plate  : {$reservation->license_plate} ");
                }

                $reservation->cruise_id = isset($this->request->cruise_id) ? $this->request->cruise_id : '';
                $reservation->schedule_id = isset($this->request->schedule_id) ? $this->request->schedule_id : '';
                $reservation->save();
            } else {
                $this->reservationlog->info("Zero Amount Post Payment.");
            }
        }

        // 
        if (!isset($this->request->is_edit)) {
            // Vehicle Details
            $vehicle = PermitVehicle::handleVehicleDetails($this->request, $this->user, $this->reservationlog);
            $this->reservationlog->info("Final Vehicle Id : " . $vehicle->id . " Make id: " . $vehicle->make_id . " Model id: " . $vehicle->model_id);
            $reservation->vehicle_id = $vehicle->id;
            $reservation->save();
            $reservation['is_card_added'] = (isset($request->is_card_added) && $request->is_card_added == 1) ? "1" : "0";
        }

        // Promocode Logic for both normal and extend;
        $this->request->merge([
            'partner_id' => $reservation->partner_id,
            'email' => $reservation->user->email,
            'user_id' => $reservation->user->id
        ]);

        if (isset($promocode) && $promocode) {
            $this->updatePromocodeChanges($this->request, $promocode, $reservation->ticketech_code, $reservation->user);
        }
        // Promocode End

        // Send email to user
        if (!isset($this->request->is_edit)) {
            if ($this->request->header('X-ClientSecret') != '') {
                if ($this->request->phone != '') {
                    if (in_array($this->user->user_prefrences, [0, 2, 3])) {
                        $this->reservationlog->info("User Prefrences  for Email: " . $this->user->user_prefrences);
                        $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));
                    }

                    //send sms to user
                    if (in_array($this->user->user_prefrences, [0, 1, 3])) {
                        $this->reservationlog->info("User Prefrences  for SMS: Start");
                        if ($reservation->is_daily == '0') {
                            $startLabel = "Enter After";
                            $endLabel = "Exit Before";
                        } else {
                            $startLabel = "Start Date";
                            $endLabel = "End Date";
                        }

                        $facilityName = ucwords($this->facility->full_name);

                        $links = $url = "";

                        $url = $this->facility->facilityConfiguration->base_url;

                        $n_facility = $this->facility;
                        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($n_facility) {
                            $join->on('user_facilities.user_id', '=', 'users.id');
                            $join->where('user_facilities.facility_id', "=", $n_facility->id);
                        })->where('created_by', $n_facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();

                        if ($getRM) {
                            $partnerSlug = $getRM->slug;
                        } else {
                            $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                            $partnerSlug  =  $checkPaymentUrl->touchless_payment_url;
                        }

                        $resUrl = $url . '/' . $partnerSlug . '/sms/' . base64_encode($reservation->id);

                        if ($this->facility->facilityConfiguration->is_send_url) {
                            $links = "Url: $resUrl";
                        }

                        // convert length into days hours min 13-09-2023 by Ashutosh
                        $duration = QueryBuilder::getFormatedDurationByLength($reservation->length);
                        $msg = "Thank you for your booking with $facilityName.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date_time \n$endLabel : $reservation->formatted_end_date_time \nAmount Charged: $" . $reservation->total . " \nDuration: $duration \n$links";

                        CommonFunctions::sendSmsAfterReservation($reservation->id, $msg, $this->reservationlog);
                        $this->reservationlog->info("User Prefrences  for SMS: End");
                    }
                } else {
                    if (in_array($this->user->user_prefrences, [0, 2, 3])) {
                        $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));
                        $this->reservationlog->info("User Prefrences  for Email: " . $this->user->user_prefrences);
                    }
                }
            } else {
                if (in_array($this->user->user_prefrences, [0, 2, 3])) {
                    $reservation->emailReservationToUser(); //uncommented the code for guest user
                    $this->reservationlog->info("User Prefrences  for Email: " . $this->user->user_prefrences);
                }
            }
        }

        if (!isset($this->request->is_edit)) {
            $this->reservationlog->info("Reservation History Created: res_id " . $reservation->id);
            $this->reservationHistory($reservation->id, $this->request->total, $res_mode);

            // Hub Api Integration
            if (config('parkengage.ROC_FACILITY') == $reservation->facility_id && config('parkengage.HUB_ZEAG.status')) {
                // Artisan::call('hubzeag:reservation', ['reservation_id' => $reservation->id, "method" => "POST"]);
                $this->dispatchJobForParkingHub($reservation->id, "POST");
                $this->reservationlog->info("Reservation Created On HubZeag Ticket:" . $reservation->ticketech_code);
            }
        } else {
            $this->reservationlog->info("Reservation History Update: res_id " . $reservation->id);
        }
        $this->reservationlog->info("End makeReservation");
        return [
            'charge' => $reservation->total,
            'reservation' => $reservation->withRelations(),
            'ticketech_guid' => isset($ticketech_guid) ? $ticketech_guid : '',
            'rate' => $rate
        ];
    }

    protected function makePassReservation()
    {
        $this->log->info("Pass reservation about to start.");
        // Validate the reservation details here

        if (isset($this->request->use_overstay) && $this->request->use_overstay > 0) {

            return $this->updateReservationWithOverstay();
        }
        $this->validate($this->request, Reservation::$validationRules, Reservation::$validationMessages);

        $this->facility = Facility::find($this->request->facility_id);

        // Confirm that facilty has ticktech ID
        if (!$this->facility->has_ticketech && !$this->facility->parkonect_id) {
            throw new ApiGenericException('Online reservations cannot be made with this facility.');
        }


        // Users can choose to apply bonus time to their reservation for a small fee
        $useBonus = (bool) $this->request->use_bonus;


        $couponThresholdPrice = isset($this->request->coupon_threshold_price) ? ($this->request->coupon_threshold_price) : (self::DEFAULT_VALUE);

        // Returns Rate or array if the facility base rate is returned
        //last 2 parameters are for inventory, last paramter will have value 2 to indicate the call is from reservation so add the threshold directly instead
        //of checking threshold type if price or percentage
        //if($this->request->is_rate_dynamic == '1'){
        $isMember = $this->request->member_id != '' ? 1 : 0;
        $rate = $this->facility->rateForReservationOnMarker($this->request->arrival, $this->request->length, $useBonus, false, false, true, $couponThresholdPrice, self::RESERVATION_THRESHOLD_TYPE, $isMember);
        if (!$rate) {
            throw new ApiGenericException('No rate found in database for this reservation.', 422);
        }

        // Confirm that request rate matches database rate
        $sentRate = $this->request->total;

        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $sentRate = ($this->request->total + $this->request->redeemed_amount);
        }

        //for credit user
        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $sentRate = ($sentRate + $this->request->redeemed_amount_credit);
        }

        //add use bonus if used
        if ($useBonus) {
            $rate['price'] = $rate['price'] + $this->facility->reservation_bonus_rate;
        }

        // for loyalty points:
        if (isset($this->request->is_loyalty_redeemed) && $this->request->is_loyalty_redeemed && $this->request->loyalty_points > 0) {
            // check loyalty account is active
            if ($this->user->is_loyalty_active != LoyaltyProgram::CONST_ONE) {
                throw new ApiGenericException('Your loyalty account is inactive.');
            }
        }

        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $reservation = $this->savePassReservation($rate, $useBonus);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

        // loyalty programs check for reservations
        //        removed second condtion discuced with nirbhay- if ($this->user->is_loyalty && $this->user->is_loyalty_active == LoyaltyProgram::CONST_ONE) {

        // if ($this->user->is_loyalty) {
        //     $reservation->loyalty_program = LoyaltyProgram::LP_ALLOW;
        // }

        //save warning message flag and values 

        if (isset($this->request->warning_on_reservation) && (($this->request->warning_on_reservation) > 0)) {
            $reservation->warning_on_reservation = $this->request->warning_on_reservation;
            $reservation->warning_on_reservation_msg = $this->request->warning_on_reservation_msg_email;
        }

        // Send reservation to ticketech
        if ($this->facility->ticketech_id) {
            try {
                // $ticketech_guid = $this->ticketech->makeReservation($reservation, $this->request->name_on_card);
                $ticketech_guid = rand(100000, 999999);
            } catch (TicketechException $e) {
                $reservation->delete();
                throw $e;
            }
        }
        // Send reservation to parkonect
        else if ($this->facility->parkonect_id) {
            try {
                //$ticketech_guid = $this->parkonect->makeReservation($reservation, $this->request->name_on_card);
                $ticketech_guid = rand(100000, 999999);
            } catch (Exception $e) {
                $reservation->delete();
                throw $e;
            }
        }

        // Charge successful, save transaction relationship to it
        $authorized_anet_transaction = new AuthorizeNetTransaction();

        $authorized_anet_transaction->sent = $this->sendAnet;
        $authorized_anet_transaction->anonymous = $this->anonymousAnet;
        $authorized_anet_transaction->user_id = $this->user->id;
        $authorized_anet_transaction->ip_address = \Request::ip();
        $authorized_anet_transaction->total = 0;
        $authorized_anet_transaction->name = $this->getBillingName();

        if ($this->facility->parkonect_id) {
            $authorized_anet_transaction->description = "Reservation {$reservation->id}, Parkonect Code {$reservation->ticketech_code}";
        } else {
            $authorized_anet_transaction->description = "Reservation {$reservation->id}, Ticketech Code {$reservation->ticketech_code}";
        }

        if (
            isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed
        ) {
            $authorized_anet_transaction->response_message = "Zero amount loyalty transaction";
        } else {
            $authorized_anet_transaction->response_message = "Zero amount transaction";
        }
        $authorized_anet_transaction->save();

        $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

        $reservation->anet_transaction_id = $authorized_anet_transaction->id;
        $reservation->ticketech_guid = $ticketech_guid;

        if ($this->request->week != '') {
            $weekDays = 7 * $this->request->week;
            $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
            $reservation->end_timestamp = $endTimestamp;
            $reservation->length = $weekDays * 24;
            $reservation->days = $weekDays;
        }
        if ($this->request->month != '') {
            $weekDays = date('t') * $this->request->month;
            $endTimestamp = date('Y-m-d', strtotime($this->request->arrival . ' + ' . $weekDays . ' days'));
            $reservation->end_timestamp = $endTimestamp;
            $reservation->length = $weekDays * 24;
            $reservation->days = $weekDays;
        }

        if ($this->request->member_id != '') {
            $reservation->is_daily = '1';
        }

        /*if($pass_rate_id != ''){
                $reservation->rate_id = $pass_rate_id;
            }*/

        if ($this->request->header('X-ClientSecret') != '') {
            $reservation->partner_id = $this->user->user_type == '3' ? $this->user->id : $this->user->created_by;
        }

        if ($this->request->no_of_visitor != '') {
            $reservation->no_of_visitor = $this->request->no_of_visitor;
        }


        /*if($this->request->license_plate_number != ''){
                $reservation->license_plate_number = $this->request->license_plate_number;
                $reservation->make_model = $this->request->make_model;
            }*/
        $reservation->payment_gateway = 'authnet';
        $reservation->save();

        // Send email to user
        if ($this->request->header('X-ClientSecret') != '') {

            if ($this->request->phone != '') {
                $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));

                if ($reservation->is_daily == '0') {
                    $startLabel = "Enter After";
                    $endLabel = "Exit Before";
                } else {
                    $startLabel = "Start Date";
                    $endLabel = "End Date";
                }

                // facility Name used for sms
                $facilityName = ucwords($this->facility->full_name);
                // convert length into days hours min 13-09-2023 by Ashutosh
                $duration = QueryBuilder::getFormatedDurationByLength($reservation->length);
                //send sms to user
                $accountSid = env('TWILIO_ACCOUNT_SID');
                $authToken  = env('TWILIO_AUTH_TOKEN');
                $client = new Client($accountSid, $authToken);
                try {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                        // the number you'd like to send the message to
                        $this->countryCode . $this->request->phone,
                        array(
                            // A Twilio phone number you purchased at twilio.com/console
                            'from' => env('TWILIO_PHONE'),
                            // the body of the text message you'd like to send
                            //'body' => "Fine"
                            'body' =>
                            "Thank you for your booking with $facilityName.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date_time \n$endLabel : $reservation->formatted_end_date_time \nAmount Charged : $reservation->total\nDuration : $duration"
                        )
                    );
                } catch (RestException $e) {
                    //throw new ApiGenericException($e->getMessage());
                    //return "success";
                }
            } else {
                $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'));
            }
        } else {
            $reservation->emailReservationToUser();
        }
        $this->log->info("Pass reservation done ." . $reservation->id);
        return [
            'charge' => $charge,
            'reservation' => $reservation->withRelations(),
            'ticketech_guid' => isset($ticketech_guid) ? $ticketech_guid : ''
        ];
    }

    /**
     * Given a user, make a reservation for that user, made reservation using apple pay.
     * TODO: This functionality should probably be drawn out into a ReservationService
     *
     * @param  User $user [description]
     * @return [type]       [description]
     */
    protected function makeReservationApplePay()
    {
        // Validate the reservation details here

        if (isset($this->request->use_overstay) && $this->request->use_overstay > 0) {

            return $this->updateReservationWithOverstayApplePay();
        }

        $this->validate($this->request, Reservation::$validationRules, Reservation::$validationMessages);

        $this->facility = Facility::find($this->request->facility_id);

        // Confirm that facilty has ticktech ID
        if (!$this->facility->has_ticketech) {
            throw new ApiGenericException('Online reservations cannot be made with this facility.');
        }

        // Users can choose to apply bonus time to their reservation for a small fee
        $useBonus = (bool) $this->request->use_bonus;


        $couponThresholdPrice = isset($this->request->coupon_threshold_price) ? ($this->request->coupon_threshold_price) : (self::DEFAULT_VALUE);

        // Returns Rate or array if the facility base rate is returned
        //last 2 parameters are for inventory, last paramter will have value 2 to indicate the call is from reservation so add the threshold directly instead
        //of checking threshold type if price or percentage
        $rate = $this->facility->rateForReservation($this->request->arrival, $this->request->length, $useBonus, false,  false, true, array(), $couponThresholdPrice, self::RESERVATION_THRESHOLD_TYPE);

        if (!$rate) {
            throw new ApiGenericException('No rate found in database for this reservation.', 422);
        }


        // Confirm that request rate matches database rate

        // if ($rate['price'] != $this->request->total) {
        $sentRate = $this->request->total;

        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $sentRate = ($this->request->total + $this->request->redeemed_amount);
        }

        //for credit user
        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $sentRate = ($sentRate + $this->request->redeemed_amount_credit);
        }

        //add use bonus if used

        if ($useBonus) {
            $rate['price'] = $rate['price'] + $this->facility->reservation_bonus_rate;
        }



        // for loyalty points:
        if (
            isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed
            && $this->request->loyalty_points > 0
        ) {

            // check loyalty account is active
            if ($this->user->is_loyalty_active != LoyaltyProgram::CONST_ONE) {
                throw new ApiGenericException('Your loyalty account is inactive.');
            }

            // get user total loyalty points check
            $loyalty_points = 0;
            $accountData = LoyaltyProgram::getUserAccounts($this->user->email, $this->user->id);
            if ($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
                $loyalty_points = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
            }

            if ($loyalty_points < $this->request->loyalty_points) {
                throw new ApiGenericException(
                    'You do not have enough loyalty points for this reservation, please reset reservation options and try again.',
                    422,
                    ['sent_points' => $this->request->loyalty_points, 'actual_points' => $loyalty_points]
                );
            }
            Reservation::$reservationType = 'IR';
            $sentRate = floatval($sentRate + floatval($this->request->loyalty_points / LoyaltyProgram::POINTS_REDEEM_RATIO));
        }

        if (filter_var($rate['price'], FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $sentRate) {
            throw new ApiGenericException(
                'Sent rate does not match database rate, please reset reservation options and try again.',
                422,
                ['sent_rate' => $this->request->total, 'database_rate' => $rate['price']]
            );
        }

        // Save reservation before sending so we have a reservation ID to attach to the auth net response
        $reservation = $this->saveReservation($rate, $useBonus);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

        // loyalty programs check for reservations
        if ($this->user->is_loyalty) {
            $reservation->loyalty_program = LoyaltyProgram::LP_ALLOW;
        }

        //save warning message flag and values         
        if (isset($this->request->warning_on_reservation) && (($this->request->warning_on_reservation) > 0)) {
            $reservation->warning_on_reservation = $this->request->warning_on_reservation;
            $reservation->warning_on_reservation_msg = $this->request->warning_on_reservation_msg_email;
        }

        //check if the total amount is 0
        if ($this->request->total > 0) {
            $charge = $this->makeApplePayTransaction($reservation, $this->request->total);
            $transaction_id = isset($charge['anet_trans_id']) ? $charge['anet_trans_id'] : '0';
            // Charge successful, save transaction relationship to it
            //$transaction = $this->authNet->getTransaction();
            $transaction = $this->authNetApple->getTransaction();
            $reservation->anet_transaction_id = $transaction->id;
            $reservation->pay_by = self::APPLE_PAY_FLAG;
            $reservation->save();

            // Send reservation to ticketech
            try {
                $ticketech_guid = $this->ticketech->makeReservation($reservation);
            } catch (TicketechException $e) {
                $transaction = AuthorizeNetTransaction::where('anet_trans_id', $transaction_id)->first();
                $this->authNetApple->setTransaction($transaction)->void();
                $reservation->delete();
                throw $e;
            }
        } else {
            // Charge successful, save transaction relationship to it
            $authorized_anet_transaction = new AuthorizeNetTransaction();

            $authorized_anet_transaction->sent = $this->sendAnet;
            $authorized_anet_transaction->anonymous = $this->anonymousAnet;
            $authorized_anet_transaction->user_id = $this->user->id;
            $authorized_anet_transaction->ip_address = \Request::ip();
            $authorized_anet_transaction->total = 0;
            $authorized_anet_transaction->name = $this->getBillingName();
            $authorized_anet_transaction->description = "Reservation {$reservation->id}, Ticketech Code {$reservation->ticketech_code}";

            if (
                isset($this->request->is_loyalty_redeemed)
                && $this->request->is_loyalty_redeemed
            ) {
                $authorized_anet_transaction->response_message = "Zero amount loyalty transaction-Applepay";
            } else {
                $authorized_anet_transaction->response_message = "Zero amount transaction-Applepay";
            }
            $authorized_anet_transaction->save();

            $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

            $reservation->anet_transaction_id = $authorized_anet_transaction->id;
            $reservation->save();

            // Send reservation to ticketech
            try {
                $ticketech_guid = $this->ticketech->makeReservation($reservation);
            } catch (TicketechException $e) {
                $reservation->delete();
                throw $e;
            }
        }

        // loyalty redeemed validations and process
        if (
            isset($this->request->is_loyalty_redeemed)
            && $this->request->is_loyalty_redeemed
        ) {
            // loyalty validations
            if (!$this->user->is_loyalty) {
                $reservation->delete();
                throw new NotFoundException('User not registered with loyalty program.');
            }

            $account = LoyaltyUserAccounts::where('user_id', $this->user->id)
                ->orderBy('id', 'DESC')
                ->first();

            // loyalty account validations
            if (!$account) {
                $reservation->delete();
                throw new NotFoundException('No account found associated with this user.');
            }
            $requestData = array(
                "sva" => $account->account_no,
                "amount" => $this->request->loyalty_points
            );

            $transactionData = array(
                'account_id' => $account->id,
                'reservation_id' => $reservation->id
            );
            $redeemResponse = LoyaltyProgram::redeemPoints($requestData, $this->user->id, $transactionData);

            if ($redeemResponse['success']) {
                $reservation->loyalty_point_used = $this->request->loyalty_points;
                $reservation->loyalty_amount_used = $this->request->loyalty_points / LoyaltyProgram::POINTS_REDEEM_RATIO;
                $reservation->save();
            } else {
                $reservation->delete();
                $msg = isset($redeemResponse['data']['status.description']) ? $redeemResponse['data']['status.description'] : 'Could not get accounts';
                throw new ApiGenericException($msg);
            }
        }
        // Send email to user
        $reservation->emailReservationToUser();

        return [
            'charge' => $charge,
            'reservation' => $reservation,
            'ticketech_guid' => $ticketech_guid
        ];
    }

    protected function updateReservationWithOverstay()
    {

        $reservation_id = $this->request->reservation_id;

        $useOverstay = (bool) $this->request->use_overstay;


        $reservation = Reservation::where('reservations.id', $reservation_id)->first();
        $currentDateTime = Carbon::now();
        $reservationEndDateTime = Carbon::parse($reservation->formatted_end_date_time);
        if ($currentDateTime->gte($reservationEndDateTime)) {
            throw new ApiGenericException('Reservation time duration has already expired', 422);
        }

        // Returns Rate or array if the facility base rate is returned. It will beneficial for furture partner of Icon
        // $rate = $this->facility->rateForReservation($this->request->arrival, $this->request->length, $useBonus, $useOverstay, $reservation_id);


        if (!$reservation) {
            throw new ApiGenericException('No reservation found in database with the reservation id.', 422);
        }

        // // Confirm that request rate matches database rate

        // // if ($rate['price'] != $this->request->total) {
        // $sentRate = $this->request->total;


        // if ($rate['price'] != $sentRate) {
        //     throw new ApiGenericException(
        //         'Sent rate does not match database rate, please reset reservation options and try again.',
        //         422,
        //         ['sent_rate' => $this->request->total, 'database_rate' => $rate['price']]
        //     );
        // }
        // print_r(json_encode($reservation));
        // die("Got it");

        $this->authNet
            ->setUser($this->user)
            ->isReservation()
            ->setFacility($this->facility)
            ->setBillingAddress($this->getBillingArray());


        if (isset($this->paymentProfileId)) { // Use our logged in users profile
            $this->authNet->setPaymentProfile($this->paymentProfileId);
        } else {  // Otherwise set transaction details manually
            $this->authNet
                ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
        }

        // Save reservation before sending so we have a reservation ID to attach to the auth net response


        $reservation = $this->updateReservation($reservation, $useOverstay);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

        try {
            // Use our database rate price to create the transaction
            $charge = $this->authNet->createTransaction(
                $reservation->overstay_rate,
                "{$reservationMode} {$reservation->id}, Ticketech Code {$reservation->ticketech_code}",
                config('icon.reservations_account')
            )->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
        } catch (Exception $e) {
            $reservation->delete();
            throw $e;
        }

        // Charge successful, save transaction relationship to it
        $transaction = $this->authNet->getTransaction();
        $reservation->total = $reservation->total + $reservation->overstay_rate;
        $reservation->anet_overstay_transaction_id = $transaction->id;

        $reservation->save();

        // Send reservation to ticketech
        try {

            $ticketech_guid = $this->ticketech->makeReservation($reservation);
        } catch (TicketechException $e) {
            // If ticketech returns an invalid response, void our transaction and pass the error on to the user
            $transaction = AuthorizeNetTransaction::where('anet_trans_id', $charge['anet_trans_id'])->first();
            $this->authNet->setTransaction($transaction)->void();
            $reservation->delete();
            throw $e;
        }

        // Send email to user
        $reservation->emailReservationExtensionToUser();

        return [
            'charge' => $charge,
            'reservation' => $reservation,
            'ticketech_guid' => $ticketech_guid
        ];
    }


    protected function updateReservationWithOverstayApplePay()
    {

        $reservation_id = $this->request->reservation_id;

        $useOverstay = (bool) $this->request->use_overstay;

        $reservation = Reservation::where('reservations.id', $reservation_id)->first();
        $currentDateTime = Carbon::now();
        $reservationEndDateTime = Carbon::parse($reservation->formatted_end_date_time);

        if ($currentDateTime->gte($reservationEndDateTime)) {
            throw new ApiGenericException('Reservation time duration has already expired', 422);
        }

        // Returns Rate or array if the facility base rate is returned. It will beneficial for furture partner of Icon
        // $rate = $this->facility->rateForReservation($this->request->arrival, $this->request->length, $useBonus, $useOverstay, $reservation_id);


        if (!$reservation) {
            throw new ApiGenericException('No reservation found in database with the reservation id.', 422);
        }


        // Save reservation before sending so we have a reservation ID to attach to the auth net response


        $reservation = $this->updateReservation($reservation, $useOverstay);


        // Mobile reservation overstay text comes from mobile end.
        $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

        $transaction_id = 0;
        $charge = $this->makeApplePayTransaction($reservation, $reservation->overstay_rate);

        // Charge successful, save transaction relationship to it
        $transaction = $this->authNetApple->getTransaction();
        $reservation->anet_overstay_transaction_id = $transaction->id;
        $reservation->total = $reservation->total + $reservation->overstay_rate;
        $reservation->save();

        // Send reservation to ticketech
        try {

            $ticketech_guid = $this->ticketech->makeReservation($reservation);
        } catch (TicketechException $e) {
            // If ticketech returns an invalid response, void our transaction and pass the error on to the user
            $transaction = AuthorizeNetTransaction::where('anet_trans_id', $transaction_id)->first();
            $this->authNetApple->setTransaction($transaction)->void();
            $reservation->delete();
            throw $e;
        }

        // Send email to user
        $reservation->emailReservationExtensionToUser();

        return [
            'charge' => $charge,
            'reservation' => $reservation,
            'ticketech_guid' => $ticketech_guid
        ];
    }

    /**
     *  makeApple Pay payment
     */
    protected function makeApplePayTransaction($reservation, $amount = 0)
    {

        $this->authNetApple
            ->setUser($this->user)
            ->setFacility($this->facility)
            ->isReservation()
            ->setBillingAddress($this->getBillingArray());
        try {
            $reservationMode = (substr($this->request->description, 0, 6) === "Mobile") ? "Mobile Reservation" : "Reservation";

            // Use our database rate price to create the transaction
            $charge = $this->authNetApple->createTransaction(
                $this->request->nonce,
                $amount,
                "{$reservationMode} {$reservation->id}, Ticketech Code {$reservation->ticketech_code}",
                config('icon.reservations_account')
            )->executeTransaction();
        } catch (Exception $e) {
            $reservation->delete();
            throw $e;
        }
        return $charge;
    }


    /**
     * Get the currently authorized user, or return an anonymous user
     * if the user is not logged in.
     *
     * @return [type] [description]
     */
    protected function getUser()
    {
        // Probably an easier way to get the current user with middleware - please let me know if there is
        if (\Authorizer::getResourceOwnerType() !== 'user') {
            throw new UserNotFound('No user found for this auth token.');
        }

        $id = \Authorizer::getResourceOwnerId();
        $user = User::find($id);

        if (!$user) {
            throw new UserNotFound('No user found for this auth token.');
        }

        return $user;
    }

    /**
     * Save the processed reservation
     *
     * @return [type] [description]
     */
    protected function saveReservation($rate, bool $useBonus): Reservation
    {
        $this->reservationlog->info("== Reached 11 in saveReseration ===============");
        $discount = 0.00;
        $discount_credit_used = 0.00;
        $parkingAmount = $rate['price'];  //Rate from Api and database rate or parking amount is same
        $processingFee = $this->facility->getProcessingFee(1); // 1 is for reservation processing fee
        $taxRate = $this->facility->getTaxRate($rate, 1);

        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $discount = $this->request->redeemed_amount;
        }

        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $discount_credit_used = $this->request->redeemed_amount_credit;
        }
        $this->reservationlog->info("== Reached 22 in saveReseration ProcessingFee: $processingFee ===============");

        //verifying the company tracking id
        $companyAffilateId = 0;


        $user_consent = 0;
        if (isset($this->request->user_consent) && $this->request->user_consent == 1) {
            $user_consent = 1;
        }
        $this->reservationlog->info("== Reached 44 in saveReseration ===============");

        $reservation = new Reservation(
            [
                'user_id' => $this->user->id,
                'facility_id' => $this->facility->id,
                'start_timestamp' => $this->request->arrival,
                'length' => $this->request->length,
                'discount' => $discount,
                'credit_used' => $discount_credit_used,
                'company_affilate_id' => $companyAffilateId,
                'user_consent' => $user_consent,
                'device_type' => isset($this->request->device_type) ? $this->request->device_type : 'web',
                //  'mer_reference' => $this->request->ref_id
            ]
        );

        $this->reservationlog->info("== Reservation instant 33 in saveReseration ===============");

        // if we get promocode in request
        if (isset($this->request->promocode) && !empty($this->request->promocode)) {
            $reservation->promocode = $this->request->promocode;
            $this->reservationlog->info("== Promocode: {$this->request->promocode} in saveReseration ===============");
        }

        //for processing fee
        if ($parkingAmount > 0 && $processingFee > 0)
            $reservation->processing_fee = $processingFee;

        if ($parkingAmount > 0 && $taxRate > 0)
            $reservation->tax_fee = $taxRate;


        if (is_a($rate, Rate::class)) {
            $reservation->rate_id = $rate->id;
        }

        if (is_array($rate) && isset($rate['id'])) {
            $reservation->rate_id = $rate['id'];
        }

        if ($useBonus) {
            $reservation->applyBonus();
        }

        $reservation->reservation_amount = $discount + $reservation->total + $this->request->total;

        if (isset($rate['price']) && !empty($rate['price'])) {
            $reservation->parking_amount = $rate['price'];
        }

        $this->reservationlog->info("======== Reservation 44 in saveReseration ===============");

        // If this is higher the apply bonus will get double applied
        $reservation->total += $this->request->total;

        if (isset($this->request->is_acknowledge))
            $reservation->is_acknowledge = $this->request->is_acknowledge;

        $lengthInMints = QueryBuilder::getLengthInMints($this->request->length);
        $arrival_time = Carbon::parse($this->request->arrival)->format('Y-m-d H:i:s');
        $end_timestamp = Carbon::parse($arrival_time)->addMinutes($lengthInMints)->format('Y-m-d H:i:s');

        $graceMinutes = QueryBuilder::getLengthInMints($this->facility->reservation_grace_period_minute);
        $estimated_checkout_time = Carbon::parse($arrival_time)->addMinutes(($lengthInMints + $graceMinutes))->format('Y-m-d H:i:s');

        $this->reservationlog->info("Estimated checkouttime: $estimated_checkout_time and Endtime is:  $end_timestamp in saveReseration ===============");
        $reservation->estimated_checkout_time = $estimated_checkout_time;
        $reservation->end_timestamp = $end_timestamp;
        $reservation->partner_id =  $this->user->created_by;
        $reservation->save();

        if (in_array($this->request->facility_id, [config('parkengage.ROC_FACILITY')])) {
            $barcode = $reservation->generateBarCode();
            $unique_id = substr($barcode, -8);
            $utc_arrival_time = Carbon::parse($reservation->start_timestamp)->setTimezone('UTC')->format('Y-m-d\TH:i:s\Z');
            $utc_end_time = Carbon::parse($reservation->end_timestamp)->setTimezone('UTC')->format('Y-m-d\TH:i:s\Z');

            $reservation->thirdparty_code = $barcode;
            $reservation->utc_start_timestamp = $utc_arrival_time;
            $reservation->utc_end_timestamp = $utc_end_time;
            $reservation->hub_unique_id = $unique_id;
            $reservation->is_hub_zeag = 1;

            $this->reservationlog->info("Reservation: Barcode: $barcode, Utc Start Time: $utc_arrival_time, Utc End Time: $utc_end_time");

            $reservation->save();
            $this->reservationlog->info("======== Reservation 55 in saveReseration ===============");
        }

        $ratesData['database_rate'] = $rate['price']; // Rate get from rate api
        $ratesData['sent_rate'] = $this->request->total;    // rate sent from request => total
        $ratesData['tax_rate'] = $taxRate;
        $ratesData['processing_fee'] = $processingFee;
        $ratesData['discount'] = $discount;

        $this->reservationlog->info("Rates from saveFunction: " .  json_encode($ratesData));

        return $reservation;
    }

    /**
     * Save the processed reservation
     *
     * @return [type] [description]
     */
    protected function savePassReservation($rate, bool $useBonus): Reservation
    {
        $discount = 0.00;
        $discount_credit_used = 0.00;

        if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
            $discount = $this->request->redeemed_amount;
        }

        if (isset($this->request->redeemed_amount_credit) && $this->request->redeemed_amount_credit > 0) {
            $discount_credit_used = $this->request->redeemed_amount_credit;
        }

        //verifying the company tracking id
        $companyAffilateId = 0;  //Lokesh No Schema found
        // if (isset($this->request->companyName) && $this->request->companyName != '') {
        //     $companyAffilateDetails = CompanyAffilate::where('slug', $this->request->companyName)->first();
        //     if ($companyAffilateDetails) {
        //         $companyAffilateId = $companyAffilateDetails->id;
        //     }
        // }

        $reservation = new Reservation(
            [
                'user_id' => $this->user->id,
                'facility_id' => $this->facility->id,
                'start_timestamp' => $this->request->arrival,
                'length' => $this->request->length,
                'discount' => $discount,
                'credit_used' => $discount_credit_used,
                'company_affilate_id' => $companyAffilateId
            ]
        );



        if (is_a($rate, Rate::class)) {
            $reservation->rate_id = $rate->id;
        }

        if (is_array($rate) && isset($rate['id'])) {
            $reservation->rate_id = $rate['id'];
        }

        if ($useBonus) {
            $reservation->applyBonus();
        }

        // If this is higher the apply bonus will get double applied
        $reservation->total = $this->request->total;
        // $reservation->discount = $this->request->total;

        $reservation->save();

        return $reservation;
    }

    /**
     * Update the processed reservation
     *
     * @return [type] [description]
     */
    protected function updateReservation(Reservation $reservation, bool $useOverstay): Reservation
    {
        if ($useOverstay) {
            $reservation->applyOverstay();
        }

        $reservation->save();

        return $reservation;
    }

    /**
     * Get billing address information in auth net form from the current request
     * Note we are currently only sending the user name, not the complete billing address
     *
     * @return [type] [description]
     */
    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }

    protected function getBillingName()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $nameArray = explode(' ', trim($name));
        return (reset($nameArray) . " " . end($nameArray));
    }

    /*
    private function getDatesFromRange($start_date, $end_date, $date_format = 'Y-m-d')
    {
        $dates_array = array();
        for ($x = strtotime($start_date); $x <= strtotime($end_date); $x += 86400) {
            array_push($dates_array, date($date_format, $x));
        }

        return $dates_array;
    }
    */

    //update Reservation Availibility 
    public function updateReservationAvailability(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $reservation = Reservation::where('id', $request->reservation_id)->first();
        $type = $request->type;

        if ($type == self::DEFAULT_CONFIRM_VAL) {

            //check if it is not getting updating twice.
            if ($reservation->is_avaiability_updated != '0') {

                throw new ApiGenericException('Sorry, Availability can not be updated now', 422);
            }
        }
        if ($type == self::DEFAULT_CANCEL_VAL) {
            //check if it is not getting updating twice.
            if ($reservation->is_avaiability_updated != '1') {
                throw new ApiGenericException('Sorry, Availability can not be updated now', 422);
            }
        }

        if ($type == self::DEFAULT_CONFIRM_VAL) {
            $reservation->is_avaiability_updated = self::DEFAULT_CONFIRM_VAL;
        } else  if ($type == self::DEFAULT_CANCEL_VAL) {
            $reservation->is_avaiability_updated = self::DEFAULT_CANCEL_VAL;
        }
        $reservation->save();


        $facility_id = $reservation->facility_id;
        $date_time_in = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
        $length = round($reservation->length, 0);

        $date_time_out =  date('Y-m-d H:i:s', strtotime(Carbon::parse($date_time_in)->addHours($length)));

        $reservation_length = $reservation->length;
        $reservation_minutes = 0;
        $reservation_hours = explode(".", $reservation->length);
        if (isset($reservation_hours[1]) && ($reservation_hours[1]) > 0) {
            $reservation_minutes = 30;
        }
        $reservation_date_time_out =  date('Y-m-d H:i:s', strtotime(Carbon::parse($date_time_in)->addHours($reservation_length)->addMinutes($reservation_minutes)));


        $inventoryRepository = new Inventory();

        //check how many slots does entry and exit time occupies
        $difference = date_diff(date_create(date('Y-m-d', strtotime($date_time_in))), date_create(date('Y-m-d', strtotime(($date_time_out)))));

        //check if someone is parking for more than a day

        if ($difference->d > 0) {

            $dates   = $inventoryRepository->generateArrayOfDates(
                ($difference->d + self::ADD_EXTRA_DAY_COUNT),
                date('Y-m-d H:i:s', strtotime($date_time_in))
            );

            $dayDifference = $difference->d;

            foreach ($dates as $key => $date) {

                $dayIn = date('w', strtotime($date->format('Y-m-d')));

                $hours = HoursOfOperation::where(['facility_id' => $facility_id, 'day_of_week' => $dayIn])->first();

                $startingHour = self::DEFAULT_HOURS;
                $endingHour   = self::TWENTY_FOUR_HOURS;

                if ($hours) {
                    $startingHour = date('G', strtotime($hours->open_time));
                    $endingHour   = date('G', strtotime($hours->close_time));
                }

                $facilityAvailability = FacilityAvailability::where(
                    ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')]
                )->first();
                if ($facilityAvailability) {
                    $inventory = json_decode($facilityAvailability->availability);
                    if ($key == 0) {
                        /**
                         * because this is the first day in the dates provided
                         * we should remove 1 from each time_slot starting
                         * from the hour provided in the api call
                         */
                        $i = date('G', strtotime($date_time_in));
                        if ($startingHour > $i) {
                            $i = $startingHour;
                        }

                        //$loopEnd = ($endingHour < self::TWENTY_FOUR_HOURS) ? $endingHour : self::TWENTY_FOUR_HOURS;
                        $loopEnd  = self::TWENTY_FOUR_HOURS;
                        while ($i <= $loopEnd) {
                            if (isset($inventory->{$i})) {
                                if ($type == self::DEFAULT_CANCEL_VAL) {
                                    $inventory->{$i} = $inventory->{$i} + 1;
                                } else if ($type == self::DEFAULT_CONFIRM_VAL) {
                                    $inventory->{$i} = $inventory->{$i} - 1;
                                }
                            }
                            $i++;
                        }
                    } elseif ($key == $dayDifference) {
                        $i = date('G', strtotime($date_time_out));
                        $minutes = date('i', strtotime($reservation_date_time_out));
                        $starting_minutes = date('i', strtotime($date_time_in));
                        if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
                            $i++;
                        }
                        /**
                         * because this is the last day in the dates provided
                         * we should remove 1 from each time_slot starting
                         * till the hour provided in the api call
                         */

                        $j  = 0;
                        while ($j < $i) {
                            if (isset($inventory->{$j})) {
                                if ($type == self::DEFAULT_CANCEL_VAL) {
                                    $inventory->{$j} = $inventory->{$j} + 1;
                                } else if ($type == self::DEFAULT_CONFIRM_VAL) {
                                    $inventory->{$j} = $inventory->{$j} - 1;
                                }
                            }
                            $j++;
                        }
                    } else {
                        /**
                         * because this could be any day except first and last in
                         * the dates provided we should remove 1 from whole day
                         */

                        $k = 0;
                        while ($k <= self::TWENTY_FOUR_HOURS) {
                            if (isset($inventory->{$k})) {
                                if ($type == self::DEFAULT_CANCEL_VAL) {
                                    $inventory->{$k} = $inventory->{$k} + 1;
                                } else   if ($type == self::DEFAULT_CONFIRM_VAL) {
                                    $inventory->{$k} = $inventory->{$k} - 1;
                                }
                            }
                            $k++;
                        }
                    }

                    $facilityAvailability->availability = json_encode($inventory, JSON_FORCE_OBJECT);
                    $facilityAvailability->save();
                }
            }
        } else {

            $startingHour = date('G', strtotime($date_time_in));
            $endingHour   = date('G', strtotime($date_time_out));
            $facilityAvailability     = FacilityAvailability::where(
                ['facility_id' => $facility_id, 'date' => date('Y-m-d', strtotime($date_time_in))]
            )->first();

            if ($facilityAvailability) {
                $availability = json_decode($facilityAvailability->availability);

                $minutes = date('i', strtotime($reservation_date_time_out));
                $starting_minutes = date('i', strtotime($date_time_in));
                if (($minutes >= self::END_TIME_EXTRA_MINUTES) && ($starting_minutes >= self::END_TIME_EXTRA_MINUTES)) {
                    $endingHour++;
                }
                while ($startingHour < $endingHour) {

                    if ($type == self::DEFAULT_CANCEL_VAL) {
                        $availability->{$startingHour} = $availability->{$startingHour} + 1;
                    } else if ($type == self::DEFAULT_CONFIRM_VAL) {
                        $availability->{$startingHour} = $availability->{$startingHour} - 1;
                    }
                    $startingHour++;
                }

                $facilityAvailability->availability = json_encode($availability, JSON_FORCE_OBJECT);

                $facilityAvailability->save();
            }
        }

        //initialise the queue to update partner ineventory db
        $updateJobParams = ['reservationId' => $request->reservation_id, 'type' => self::RESERVATION_TYPE];
        Artisan::queue('cron:update-inventory', $updateJobParams);
        //       $this->updateReservationAvailabilityPartner($request->reservation_id);

        // Return reservation and charge details to caller
        return [
            'is_avalibility_update' => true,
        ];
    }

    public function sendSmsAfterReservation(Request $request)
    {
        $reservation = Reservation::with('user')->where('ticketech_code', $request->ticketech_code)->first();

        if (!$reservation) {
            throw new ApiGenericException('No reservation found.');
        }
        if ($reservation->is_daily == '0') {
            $startLabel = "Enter After";
            $endLabel = "Exit Before";
        } else {
            $startLabel = "Start Date";
            $endLabel = "End Date";
        }


        //send sms to user
        $accountSid = env('TWILIO_ACCOUNT_SID');
        $authToken  = env('TWILIO_AUTH_TOKEN');
        $client = new Client($accountSid, $authToken);

        try {
            // Use the client to do fun stuff like send text messages!
            $client->messages->create(
                // the number you'd like to send the message to
                $reservation->user->phone,
                array(
                    // A Twilio phone number you purchased at twilio.com/console
                    'from' => env('TWILIO_PHONE'),
                    // the body of the text message you'd like to send
                    //'body' => "Fine"
                    'body' =>
                    "Thank you for booking with Spelman College. Your parking has been confirmed.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date, $reservation->formatted_start_time \n$endLabel : $reservation->formatted_end_date, $reservation->formatted_end_time \nAmount Charged : $reservation->total"
                )
            );
        } catch (RestException $e) {
            throw new ApiGenericException($e->getMessage());
        }

        return "true";
    }

    /*
    public function checkUserPermit(Request $request)
    {
        $permitVehicle = PermitVehicle::with([
            'vehicles' => function ($query) {
                $query->where('desired_end_date', '>=', date("Y-m-d"));
            }
        ])->where('license_plate_number', $request->license_plate_number)->orderBy('id', 'DESC')->first();

        if (!$permitVehicle) {
            throw new ApiGenericException('No permit found.');
        }
        if (isset($permitVehicle->vehicles->desired_end_date)) {

            $today = strtotime(date("Y-m-d H:i:s"));
            $endDate = strtotime($permitVehicle->vehicles->desired_end_date);
            $permitVehicle->vehicles['is_expired'] = 0;
            if ($today > $endDate) {
                $permitVehicle->vehicles['is_expired'] = 1;
            }
            $permitVehicle->vehicles['desired_start_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_start_date);
            $permitVehicle->vehicles['desired_end_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_end_date);
            return $permitVehicle;
        } else {

            $permitVehicle = PermitVehicle::with('vehicles')->where('license_plate_number', $request->license_plate_number)->orderBy('id', 'DESC')->first();
            if (isset($permitVehicle->vehicles->desired_end_date)) {

                $today = strtotime(date("Y-m-d H:i:s"));
                $endDate = strtotime($permitVehicle->vehicles->desired_end_date);
                $permitVehicle->vehicles['is_expired'] = 0;
                if ($today > $endDate) {
                    $permitVehicle->vehicles['is_expired'] = 1;
                }
                $permitVehicle->vehicles['desired_start_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_start_date);
                $permitVehicle->vehicles['desired_end_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_end_date);
                return $permitVehicle;
            } else {
                throw new ApiGenericException('This permit has been expired or not valid.');
            }
        }
    }
    */
    /*
    public function allPermitData(Request $request)
    {
        $query = PermitVehicle::query();

        //for date
        if (isset($request->license_plate_number) && ($request->license_plate_number != '')) {
            $query->where('license_plate_number', 'like', "{$request->license_plate_number}%");
        }
        //expired
        $today = date('Y-m-d');
        if (isset($request->expired_flag)) {
            if ($request->expired_flag == '1') {
                $query->whereHas('vehicles', function ($q) use ($today) {
                    $q->where('desired_end_date', '>', $today);
                });
            }
            if ($request->expired_flag == '2') {
                $query->whereHas('vehicles', function ($q) use ($today) {
                    $q->where('desired_end_date', '<=', $today);
                });
            }
        }

        $query->whereHas('vehicles', function ($q) {
            $q->whereNull('deleted_at');
        });

        $query->with('vehicles');
        return $query->paginate(20);


        //           $result->vehicles['desired_start_date'] = $this->getDaySufixFormat($result->vehicles->desired_start_date);
        //         $result->vehicles['desired_end_date'] = $this->getDaySufixFormat($result->vehicles->desired_end_date);


    }
    */

    public function getDaySufixFormat($date)
    {
        $day = date('d', strtotime($date));
        $monthYear = date('F, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
        $second_last_digit = substr($number, -2, 1);
        $suffix = 'th';
        if ($second_last_digit != '1') {
            switch ($last_digit) {
                case '1':
                    $suffix = 'st';
                    break;
                case '2':
                    $suffix = 'nd';
                    break;
                case '3':
                    $suffix = 'rd';
                    break;
                default:
                    break;
            }
        }
        if ((string) $number === '1') $suffix = 'st';
        return $number . $suffix . ' ' . $monthYear;
    }

    public function getBookingList(Request $request)
    {

        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
                $result  = [];
                $result["total"] = 0;
                $result["per_page"] = 20;
                $result["current_page"] = 1;
                $result["last_page"] = 1;
                $result["next_page_url"] = Null;
                $result["prev_page_url"] = Null;
                $result["from"] = Null;
                $result["to"] = Null;
                $result["data"] = [];
                return $result;
            } else {
                $partner_id = Auth::user()->id;
            }
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }
        if ($partner_id == "356808") {
            $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->whereNotNull('anet_transaction_id');

            if (isset($request->search)) {
                $reservation = $reservation->where('ticketech_code', 'like', "%$request->search%")->where('partner_id', $partner_id);
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orWhereHas(

                    'user',
                    function ($query) use ($request, $partner_id) {
                        $query
                            ->where('phone', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%")
                            ->where('created_by', $partner_id);
                    }
                );
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orWhereHas(
                    'userPass',
                    function ($query) use ($request, $partner_id) {
                        $query
                            ->where('pass_code', 'like', "%{$request->search}%")
                            ->where('partner_id', $partner_id);
                    }
                );
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orWhereHas(
                    'transaction',
                    function ($query) use ($request) {
                        $query
                            ->where('ref_id', 'like', "%{$request->search}%");
                    }
                );
            }
            if ($request->booking_type == '0') {
                $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
            } elseif ($request->booking_type == '1') {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            } else {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            }
            $reservation = $reservation->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
            $reservation = $reservation->where(function ($query) {
                //$query->whereNotNull('anet_transaction_id');

            });
            //$reservation = $reservation->whereNotNull('anet_transaction_id');  
            if ($request->sort != '') {
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orderBy($request->sort, $request->sortBy);
            } else {
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orderBy('id', 'DESC');
            }

            /*$reservation = QueryBuilder::buildSearchQueryForPartner($reservation, $request->search, Reservation::$searchFields)
            ->orWhereHas(
                    'user', function ($query) use ($request) {
                        $query
                            ->where('phone', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%");
                    }
            );*/

            // return $reservation->toSql();
            $reservation = $reservation->paginate(20);
        } else if ($partner_id == "356560") {
            $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->whereNotNull('anet_transaction_id');

            if (isset($request->search)) {
                $reservation = $reservation->where('ticketech_code', 'like', "%$request->search%")->where('partner_id', $partner_id);
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orWhereHas(
                    'user',
                    function ($query) use ($request, $partner_id) {
                        $query
                            ->where('phone', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%")
                            ->where('created_by', $partner_id);
                    }
                );
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orWhereHas(
                    'userPass',
                    function ($query) use ($request, $partner_id) {
                        $query
                            ->where('pass_code', 'like', "%{$request->search}%")
                            ->where('partner_id', $partner_id);
                    }
                );
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orWhereHas(
                    'transaction',
                    function ($query) use ($request) {
                        $query
                            ->where('ref_id', 'like', "%{$request->search}%");
                    }
                );
            }
            if ($request->booking_type == '0') {
                $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
            } elseif ($request->booking_type == '1') {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            } else {
                $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
            }
            $reservation = $reservation->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
            if ($request->sort != '') {
                $reservation = $reservation->orderBy($request->sort, $request->sortBy);
            } else {
                $reservation = $reservation->orderBy('id', 'DESC');
            }

            /*$reservation = QueryBuilder::buildSearchQueryForPartner($reservation, $request->search, Reservation::$searchFields)
            ->orWhereHas(
                    'user', function ($query) use ($request) {
                        $query
                            ->where('phone', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%");
                    }
            );*/


            $reservation = $reservation->paginate(20);
        } else {


            $today = $request->from_date;
            $todayQrCode = [];
            if ($request->booking_type == '0') {
            } else {
                if ($request->booking_type == '') {
                    $event = Event::whereDate('start_time', '=', $from_date)->where("is_active", '1')->get();
                } else {
                    $event = Event::whereDate('start_time', '>=', $from_date)->whereDate('start_time', '<=', $to_date)->where("is_active", '1')->get();
                }

                $todayQrCode = [];
                if (count($event) > 0) {
                    foreach ($event as $k => $v) {
                        $qrCode = MapcoQrcode::where("event_id", $v->id)->get();
                        if (count($qrCode) > 0) {
                            foreach ($qrCode as $key => $value) {
                                $todayQrCode[] = $value->reservation_id;
                            }
                        }
                    }
                } else {
                    if ($request->booking_type == '') {
                        $todayQrCode[] = 0;
                    }
                }
            }
            $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction', 'mapcoQrCode'])->whereNotNull('anet_transaction_id');

            if (isset($request->search)) {
                $reservation = $reservation->where('ticketech_code', 'like', "%$request->search%")->where('partner_id', $partner_id);
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orWhereHas(
                    'user',
                    function ($query) use ($request, $partner_id) {
                        $query
                            ->where('phone', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%")
                            ->where('created_by', $partner_id);
                    }
                );
                $reservation = $reservation->whereNotNull('anet_transaction_id')->orWhereHas(
                    'transaction',
                    function ($query) use ($request) {
                        $query
                            ->where('ref_id', 'like', "%{$request->search}%");
                    }
                );
            }
            if ($request->booking_type == '0') {
                $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
            }
            /*if($request->booking_type == '0'){    
            $reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);      
          }elseif($request->booking_type == '1'){
            $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);      
            }else{
            $reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);      
            }
          */
            $reservation = $reservation->where(function ($query) use ($partner_id, $todayQrCode) {
                $query->where('partner_id', $partner_id);
                if (count($todayQrCode) > 0) {
                    $query->whereIn('id', $todayQrCode);
                }
            });
            if ($request->sort != '') {
                $reservation = $reservation->orderBy($request->sort, $request->sortBy);
            } else {
                $reservation = $reservation->orderBy('id', 'DESC');
            }

            /*$reservation = QueryBuilder::buildSearchQueryForPartner($reservation, $request->search, Reservation::$searchFields)
            ->orWhereHas(
                    'user', function ($query) use ($request) {
                        $query
                            ->where('phone', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%");
                    }
            );*/


            $reservation = $reservation->paginate(20);

            if ($request->booking_type == '0') {
            } else {
                if (count($reservation) > 0) {
                    foreach ($reservation as $key => $value) {
                        if (isset($value->mapcoQrCode)) {
                            foreach ($value->mapcoQrCode as $k => $v) {
                                if ($v->event_id != '0') {
                                    //$event = Event::where("id",$v->event_id)->where("is_active", '1')->first();
                                    if (count($event) > 0) {
                                        foreach ($event as $eventKey => $eventValue) {
                                            if (strtotime(date("Y-m-d", strtotime($eventValue->start_time))) == strtotime($today)) {
                                                $reservation[$key]['start_timestamp'] = $eventValue->start_time;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        if ($request->sort == 'phone') {
            if (count($reservation) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->phone > $reservation[$j]['user']->phone) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->phone < $reservation[$j]['user']->phone) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                }
            }
        }
        if ($request->sort == 'email') {
            if (count($reservation) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->email > $reservation[$j]['user']->email) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($reservation); $i++) {
                        for ($j = $i + 1; $j < count($reservation); $j++) {
                            if ($reservation[$i]['user']->email < $reservation[$j]['user']->email) {
                                $temp = $reservation[$i];
                                $reservation[$i] = $reservation[$j];
                                $reservation[$j] = $temp;
                            }
                        }
                    }
                }
            }
        }

        return $reservation;
    }


    public function showQrCodeImage($photo)
    {
        if (!$photo) {
            throw new NotFoundException('No image with that name found.');
        }

        $file = Storage::disk('local')->get($photo) ?: null;
        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/' . $photo));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }

    public function makePayment(Request $request)
    {

        $total = ($request->total) * 100;
        $user_id = Auth::user()->id;
        $partner_id = Auth::user()->created_by;
        if ($request->payment_profile_id != '') // saved cards
        {
            $tokenCheck = PlanetPaymentProfile::where('user_id', $user_id)->where('partner_id', $partner_id)->where('token', $request->payment_profile_id)->first();

            if (!$tokenCheck) {
                throw new ApiGenericException("Payment Profile Not Found.");
            }

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => config('parkengage.PLANET_PROFILE_SAVE_URL'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                  "Request": {
                    "Type": "payrequestnocardread",
                    "Version": "W2MXG520",
                      "Credentials": {
                        "ValidationID": "ParkEngageTest",
                        "ValidationCode": "ParkEngageTest1!",
                        "ValidationCodeHash": null
                      },
                      "Params": {
                        "PaymentOkUrl": "",
                        "CardNumber": "' . $request->payment_profile_id . '",
                        "CardExpiryDateMMYY": "",
                        "CardStartDateMMYY": "",
                        "CardIssueNumber": "",
                        "CardCvv2": "",
                        "CardholderStreetAddress1": "",
                        "CardholderCity": "",
                        "CardholderState": "",
                        "CardholderZipCode": "",
                        "CardholderNameFirst": "",
                        "CardholderNameLast": "",
                        "Amount": "' . $total . '",
                        "Currency": "USD",
                        "RequesterTransRefNum": "TEST MIT PAYMENT 001",
                        "UserData1": "",
                        "UserData2": "",
                        "UserData3": "",
                        "UserData4": "",
                        "UserData5": "",
                        "OptionFlags": "P"

                        }
                }
            }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
                ),
            ));

            $response = curl_exec($curl);
            curl_close($curl);
            $refundstatus = json_decode($response, TRUE);
            //$this->log->info("Response Data Planet (Saved Cards): " . $refundstatus);	


        } else // not saved cards
        {
            if ($request->nonce) {
                $this->setDecryptedCard($request);
            }

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => config('parkengage.PLANET_PROFILE_SAVE_URL'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                  "Request": {
                    "Type": "EftAuthorization",
                    "Version": "W2MXG520",
                      "Credentials": {
                        "ValidationID": "ParkEngageTest",
                        "ValidationCode": "ParkEngageTest1!",
                        "ValidationCodeHash": null
                      },
                      "Params": {
                          "PaymentOkUrl": "",
                          "CardNumber": "' . $request->card_number . '",
                          "CardExpiryDateMMYY": "' . $request->expiration_date . '",
                          "CardStartDateMMYY": "",
                          "CardIssueNumber": "",
                          "CardCvv2": "' . $request->security_code . '",
                          "CardholderStreetAddress1": "",
                          "CardholderCity": "",
                          "CardholderState": "",
                          "CardholderZipCode": "",
                          "CardholderNameFirst": "",
                          "CardholderNameLast": "",
                          "Amount": "' . $total . '",
                          "Currency": "USD",
                          "RequesterTransRefNum": "TEST AUTH 001",
                          "UserData1": "",
                          "UserData2": "",
                          "UserData3": "",
                          "UserData4": "",
                          "UserData5": "",
                          "OptionFlags": "G"
                        }
                }
            }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            //return json_encode($response);
            //$this->log->info("Response Data Planet (Non Saved Cards): " . json_encode($response));	

            $refundstatus = json_decode($response, TRUE);
            //$this->log->info("Response Data Planet (Non Saved Cards): " .$refundstatus);
            //return $refundstatus;


            $cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $partner_id)->where('card_last_four', $refundstatus["Response"]["Params"]["CardNumberLast4"])->first();

            // if($cardCheck){
            //     throw new ApiGenericException("Card Details Already Added");
            // }

            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                //. $refundstatus["Response"]["Params"]["ResultReason"] 
                // throw new ApiGenericException("Card details are invalid");	
                return ['error' => 'payment failed.'];
            }


            if ($request->user_concent == 1 && !$cardCheck) {
                $data['user_id'] = $user_id;
                $data['partner_id'] = $partner_id;
                $data['name'] = isset($request->name_on_card) ? $request->name_on_card : '';
                $data['card_last_four'] = $refundstatus["Response"]["Params"]["CardNumberLast4"];
                $data['card_type'] = $refundstatus["Response"]["Params"]["CardSchemeId"];
                $data['card_name'] = $refundstatus["Response"]["Params"]["CardSchemeName"];
                $data['expiry'] = $request->expiration_date;
                $data['token'] = $refundstatus["Response"]["Params"]["Token"];
                $data['tx_state_text'] = $refundstatus["Response"]["Params"]["TxStateText"];
                $data['tx_state'] = $refundstatus["Response"]["Params"]["TxState"];
                $data['result_reason'] = $refundstatus["Response"]["Params"]["ResultReason"];
                $data['currency_used'] = $refundstatus["Response"]["Params"]["CurrencyUsed"];

                $result = PlanetPaymentProfile::create($data);
                // if(!$result){
                // throw new ApiGenericException("Record Not Added");
                // }
                // return $result;        
            }
        }

        $authorized_anet_transaction = new AuthorizeNetTransaction();
        $authorized_anet_transaction->sent = '1';
        $authorized_anet_transaction->user_id = $user_id;
        $authorized_anet_transaction->ip_address = \Request::ip();
        $authorized_anet_transaction->total = $this->request->total;
        $authorized_anet_transaction->description = "Pay Mobility payment: " . $user_id;
        $authorized_anet_transaction->card_type = $refundstatus["Response"]["Params"]["CardSchemeId"];
        $authorized_anet_transaction->ref_id = $this->request->ref_id;
        $authorized_anet_transaction->anet_trans_id = $refundstatus["Response"]["Params"]["TxID"];
        $authorized_anet_transaction->method = "card";
        $authorized_anet_transaction->payment_last_four = isset($refundstatus["Response"]["Params"]["CardNumberLast4"]) ? $refundstatus["Response"]["Params"]["CardNumberLast4"] : '0';
        $authorized_anet_transaction->expiration = isset($refundstatus["Response"]["Params"]["CardExpiryDateMMYY"]) ? $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"] : '0';

        $authorized_anet_transaction->save();

        return $authorized_anet_transaction;
    }

    public function makeDataCapPayment(Request $request)
    {
        $paymentEnv = isset($this->facility->FacilityPaymentDetails->datacap_payment_env) ? $this->facility->FacilityPaymentDetails->datacap_payment_env : config('parkengage.DATACAP_PAYMENT_ENV');

        $amount = ($paymentEnv == 'test') ? '3.00' : $request->total;

        if ($request->payment_profile_id != '') {        // saved cards

            // Vijay : 05-12-2024 : New Add Card flow Payment change : PIMS- 11063
            if (isset($request->payment_profile_id) && !empty($request->payment_profile_id) && isset($request->user_id) && $request->user_id > 0) {
                try {
                    $payment_type_id    = $this->facility->FacilityPaymentDetails->facility_payment_type_id;
                    $cardCheck = GatewayHelper::getRegisterUserProfileId($payment_type_id, $request->card_last_four, $request->expiry, $request->user_id, $this->facility);
                    $request->request->add(['payment_profile_id' => $cardCheck->token]);
                } catch (\Throwable $th) {
                    throw new ApiGenericException("Payment Failed!");
                }
            }
            // Close here !!!

            $cardCheck = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $request->user_id)->where('token', $request->payment_profile_id)->first();
            if (!$cardCheck) {
                $this->request->reservation->delete();
                throw new ApiGenericException("Payment Profile Not Found.");
            }
            $this->reservationlog->info("Payment Profile Data --" . $cardCheck);

            $data['Token'] = $cardCheck->token;
            $request->request->add(['Token' => $cardCheck->token]);
            if ($amount > 0) {
                $amount = number_format($amount, 2);
                $data['Amount'] = $amount;
                $data['Token'] = $cardCheck->token;
                $paymentResponse = $this->makePaymentDataCap($data);
                if ($paymentResponse['Status'] == 'Approved') {
                    $brand = str_replace('/', '', $paymentResponse['Brand']);
                    $authorized_anet_transaction = new AuthorizeNetTransaction();
                    $authorized_anet_transaction->sent = '1';
                    $authorized_anet_transaction->user_id = $request->user_id;
                    $authorized_anet_transaction->ip_address = $cardCheck->ip_address;
                    $authorized_anet_transaction->total = $request->total;
                    $authorized_anet_transaction->description = "Reservation Payment Done User : " . $request->user_id;
                    $authorized_anet_transaction->card_type = $brand;
                    $authorized_anet_transaction->ref_id = $paymentResponse["RefNo"];
                    $authorized_anet_transaction->method = "card";
                    $authorized_anet_transaction->payment_last_four = isset($cardCheck->card_last_four) ? $cardCheck->card_last_four : '0';
                    $authorized_anet_transaction->auth_code = $paymentResponse["AuthCode"];
                    $authorized_anet_transaction->response_code = $paymentResponse["ReturnCode"];
                    $authorized_anet_transaction->invoice_number = $paymentResponse["InvoiceNo"];
                    $authorized_anet_transaction->anet_trans_id = $paymentResponse["InvoiceNo"];
                    $authorized_anet_transaction->status_message = $paymentResponse["Status"];
                    $authorized_anet_transaction->expiration = $cardCheck->expiry;

                    $authorized_anet_transaction->save();
                    $this->reservationlog->info("Payment Transaction Data  --" . $authorized_anet_transaction);
                    $authorized_anet_transaction->token = $cardCheck->token;
                    return $authorized_anet_transaction;
                } else {
                    return $paymentResponse;
                }
            }
        } else if ($request->nonce) {
            $this->reservationlog->info("Request Data Datacap: " . json_encode($request->all()));
            $this->setDecryptedCard($request);
            $card_month = substr($request->expiration_date, 0, 2);
            $card_year = substr($request->expiration_date, -2);
            $request->request->add(['expiration_month' => $card_month]);
            $request->request->add(['expiration_year' => $card_year]);
            //datacap otu token
            $datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($request, $this->facility);
            $this->reservationlog->info("datacapPaymentToken Datacap: " . json_encode($datacapPaymentToken));
            if (isset($datacapPaymentToken['Errors']) && !empty($datacapPaymentToken['Errors'])) {
                $this->request->reservation->delete();
                throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
            }
            if ($datacapPaymentToken) {
                $request->request->add(['token' => $datacapPaymentToken["Token"]]);
            } else {
                $this->request->reservation->delete();
                throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
            }
            $paymentStatus = $this->makeZeroAuthPaymentDataCap();
            $paymentRecord = json_decode($paymentStatus, TRUE);
            if (isset($paymentRecord['Status']) && $paymentRecord['Status'] == 'Approved') {
                $cardCheck = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $request->user_id)->where('card_holder_id', $paymentRecord['CardHolderID'])->first();
                if (!$cardCheck) {
                }
            } else {
                $this->request->reservation->delete();
                throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card.");
            }
            $data['Amount'] = $amount;
            $data['Token'] = $paymentRecord['Token'];
            $this->request->request->add(['CardHolderID' => $paymentRecord["CardHolderID"]]);
            $this->request->request->add(['Token' => $paymentRecord["Token"]]);
            $paymentResponse = $this->makePaymentDataCap($data);
            if ($request->user_concent == 1 && !$cardCheck) {
                $dataProfile['user_id'] = $request->user_id;
                $dataProfile['name'] = isset($request->name_on_card) ? $request->name_on_card : '';
                $dataProfile['partner_id'] = $request->partner_id;
                $dataProfile['card_last_four'] = isset($request->card_number) ? substr($request->card_number, -4) : '0';
                $dataProfile['card_type'] = $request->brand;
                $dataProfile['card_name'] = $request->brand;
                $dataProfile['expiry'] = $request->expiration_date;
                $dataProfile['token'] = $paymentRecord['Token'];
                $dataProfile['card_holder_id'] = $paymentRecord['CardHolderID'];
                $dataProfile['result_reason'] = $paymentRecord['Message'];
                $dataProfile['currency_used'] = "USD";
                $dataProfile['RefNo'] = $paymentRecord['RefNo'];
                $dataProfile['InvoiceNo'] = $paymentRecord['InvoiceNo'];
                $dataProfile['AuthCode'] = $paymentRecord['AuthCode'];

                $result = DatacapPaymentProfile::create($dataProfile);
                if (!$result) {
                    throw new ApiGenericException("Record Not Added");
                }
            }

            if ($paymentResponse['Status'] == 'Approved') {
                $authorized_anet_transaction = new AuthorizeNetTransaction();
                $authorized_anet_transaction->sent = '1';
                $authorized_anet_transaction->user_id = $request->user_id;
                $authorized_anet_transaction->ip_address = \Request::ip();
                $authorized_anet_transaction->total = $request->total;
                $authorized_anet_transaction->description = "Reservation Payment Done User : " . $request->user_id;
                $authorized_anet_transaction->card_type = $request->brand;
                $authorized_anet_transaction->ref_id = $paymentResponse["RefNo"];
                $authorized_anet_transaction->method = "card";
                $authorized_anet_transaction->payment_last_four = isset($request->card_number) ? substr($request->card_number, -4) : '0';
                $authorized_anet_transaction->auth_code = $paymentResponse["AuthCode"];
                $authorized_anet_transaction->response_code = $paymentResponse["ReturnCode"];
                $authorized_anet_transaction->response_message = "Processed";
                $authorized_anet_transaction->invoice_number = $paymentResponse["InvoiceNo"];

                $authorized_anet_transaction->status_message = $paymentResponse["Status"];
                $authorized_anet_transaction->expiration = $request->expiration_date;

                $authorized_anet_transaction->save();
                $this->reservationlog->info("Payment Transaction Data  --" . $authorized_anet_transaction);
                $authorized_anet_transaction->token  = $paymentResponse['Token'];
                return $authorized_anet_transaction;
            } else {
                return $data['error'] = "Payment Failed.";
                //return $data;
            }
        }
        //return $authorized_anet_transaction;
    }

    // zero Auth Payment
    public function makeZeroAuthPaymentDataCap()
    {
        $this->log->info("call datacap auth only curl");
        $data['Token'] = $this->request->token;
        $data['Amount'] = 0.00;
        $data["CardHolderID"] = "Allow_V2";
        $mid = isset($this->facility->FacilityPaymentDetails->datacap_ecommerce_mid) ? $this->facility->FacilityPaymentDetails->datacap_ecommerce_mid : $this->facility->ecommerce_mid;
        if ($mid == '') {
            $this->request->reservation->delete();
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $vars = json_encode($data);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, isset($this->facility->FacilityPaymentDetails->datacap_authonly_url) ? $this->facility->FacilityPaymentDetails->datacap_authonly_url : config('parkengage.DATACAP_AUTHONLY_URL'));
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);
        curl_close($curl);
        //dd($response);
        $this->log->info("datacap auth only curl response " . json_encode($response));
        return $response;
    }

    //  Pre Auth Transaction
    public function makePreAuthPaymentDataCap($data)
    {
        $this->log->info("call datacap auth only curl");
        //$mid = $this->facility->ecommerce_mid;
        $mid = isset($this->facility->FacilityPaymentDetails->datacap_ecommerce_mid) ? $this->facility->FacilityPaymentDetails->datacap_ecommerce_mid : $this->facility->ecommerce_mid;
        if ($mid == '') {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $vars = json_encode($data);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, isset($this->facility->FacilityPaymentDetails->datacap_preauth_url) ? $this->facility->FacilityPaymentDetails->datacap_preauth_url : config('parkengage.DATACAP_PAYMENT_PRE_AUTH_URL'));
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);
        curl_close($curl);
        //dd($response);
        $this->log->info("datacap auth only curl response " . json_encode($response));
        return $response;
    }

    public function makePaymentDataCap($data)
    {

        $vars = json_encode($data);
        $mid = isset($this->facility->FacilityPaymentDetails->datacap_ecommerce_mid) ? $this->facility->FacilityPaymentDetails->datacap_ecommerce_mid : $this->facility->ecommerce_mid;
        if ($mid == '') {
            $this->request->reservation->delete();
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, isset($this->facility->FacilityPaymentDetails->datacap_script_url) ? $this->facility->FacilityPaymentDetails->datacap_script_url : config('parkengage.DATACAP_PAYMENT_URL'));
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);

        curl_close($curl);
        $this->log->info("Payment Data --" . json_encode($response));
        //dd($response);
        $paymentData = json_decode($response, TRUE);
        return $paymentData;
    }

    public function makePaymentRefundDataCap($reservation_ticketcode)
    {
        $reservation = Reservation::with('transaction')->where("ticketech_code", $reservation_ticketcode)->first();
        $card_last_four = $reservation->transaction->payment_last_four;
        //$amount = $reservation->total;
        $paymentEnv = isset($this->facility->FacilityPaymentDetails->planet_payment_env) ? $this->facility->FacilityPaymentDetails->planet_payment_env : config('parkengage.DATACAP_PAYMENT_ENV');

        $amount = ($paymentEnv == 'test') ? '3.00' : $reservation->total;

        $cardCheck = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $reservation->user_id)->where('card_last_four', $card_last_four)->first();

        $data['Amount'] = $amount;
        $data['Token']    = $cardCheck->token;


        $vars = json_encode($data);
        $mid = isset($this->facility->FacilityPaymentDetails->datacap_ecommerce_mid) ? $this->facility->FacilityPaymentDetails->datacap_ecommerce_mid : $this->facility->ecommerce_mid;

        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, isset($this->facility->FacilityPaymentDetails->planet_refund_url) ? $this->facility->FacilityPaymentDetails->planet_refund_url : config('parkengage.DATACAP_PAYMENT_REFUND_URL'));
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);

        curl_close($curl);
        $this->log->info("Payment Data --" . json_encode($response));
        //dd($response);
        $paymentData = json_decode($response, TRUE);
        return $paymentData;
    }

    public function getReservationDetailsByUserId($ticketechCode)
    {
        $user = Auth::user();
        if (!$user) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }

        $reservation = Reservation::where('ticketech_code', $ticketechCode)->first();
        if (!$reservation) {
            throw new NotFoundException('No reservation found with that ticketech code');
        }

        if ($user->id != $reservation->user_id) {
            throw new UserNotAuthorized('You are not authorized to view this reservation.');
        }

        Notification::whereNull('deleted_at')->where('reservation_code', $ticketechCode)->update(["is_read" => '1']);

        $reservation->created_at_formatted = date("D, F d, Y h:i A", strtotime($reservation->created_at));
        // cross facility check Ashutosh 27-09-2023
        $reservation = $reservation->withRelations();
        if (isset($reservation) && !empty($reservation)) {
            $reservation->checkin_facility = null;
            if (isset($reservation->ticket) && $reservation->ticket->facility_id != $reservation->facility_id) {
                $reservation->checkin_facility = Facility::where('id', $reservation->ticket->facility_id)->first();
            }
        }
        return $reservation;
    }

    // Create ReservationHistroy
    public function reservationHistory($id, $total, $mode = 0)
    {
        $this->reservationlog->info("=======ReservationHistroy Start Mode: {$mode} and Id: {$id} and Payable: {$total}===========");
        try {
            $reservation_new = Reservation::find($id);
            $reservation_history = ReservationHistroy::create(array_merge($reservation_new->toArray(), ['reservation_mode' => $mode, 'res_id' => $id, 'payable_amount' => $total]));
            $this->reservationlog->info("=======ReservationHistroy END { $reservation_new->ticketech_code } ===========");
        } catch (\Throwable $th) {
            $this->reservationlog->info("Exception " . $th->getMessage() . ' File ' . $th->getFile() . ' Line ' . $th->getLine());
        }

        return $reservation_history;
    }

    // Update the Reservation Mapco ROC
    public function userReservationUpdate(Request $request)
    {
        // Update
        $this->reservationlog->info("===================Reservation Update Start================");
        $this->reservationlog->info("Booking Request userReservationUpdate : " . json_encode($this->request->all()));
        $reservation = Reservation::with('facility')->where('id', $this->request->reservation_id)->whereNull('cancelled_at')->first();
        $this->reservationlog->info("=Reservation Id: " . $this->request->reservation_id . " ================");
        // dd($reservation);
        if (!$reservation) {
            $this->reservationlog->info("=================== Reservation Delete due to Payment Failed ================");
            throw new ApiGenericException("The reservation has been cancelled and cannot be modified.", 422);
        }
        $this->reservationlog->info("===== Reservation Booking Id: " . $reservation->ticketech_code . " ================");
        if ($reservation->user_id != $request->user_id) {
            throw new ApiGenericException('User is not authorized to update this reservation.', 422);
        }

        $this->countryCode = ((isset($this->countryCode)) && $this->countryCode == '') ?: QueryBuilder::appendCountryCode();
        // dd($this->countryCode);

        $facility = Facility::with('facilityConfiguration')->find($this->request->facility_id);

        $this->user = User::find($request->user_id);
        // Get the current time
        $currentTime = Carbon::now();
        $request->request->add(['is_card_added' => "1"]);

        // From DB => needed for edit conditions
        $start_timestamp_db = Carbon::parse($reservation->start_timestamp);
        $end_timestamp_db = Carbon::parse($reservation->end_timestamp);

        // From Request for save
        $arrival_time = Carbon::parse($this->request->arrival)->format('Y-m-d H:i:s');
        $endTimestamp = Carbon::parse($arrival_time)->addMinutes(QueryBuilder::getLengthInMints($this->request->length))->format('Y-m-d H:i:s');
        $this->reservationlog->info("Reservation: Start Time: $arrival_time, End Time: $endTimestamp");

        // From Request UTC Timezone for save
        $utc_arrival_time = Carbon::parse($this->request->arrival)->setTimezone('UTC')->format('Y-m-d\TH:i:s\Z');
        $utc_end_time = Carbon::parse($utc_arrival_time)->addMinutes(QueryBuilder::getLengthInMints($this->request->length))->setTimezone('UTC')->format('Y-m-d\TH:i:s\Z');
        $this->reservationlog->info("Reservation: Utc Start Time: $utc_arrival_time, Utc End Time: $utc_end_time");

        $getLengthInMinutes = $facility->reservation_grace_period_minute;
        $this->reservationlog->info("=======Estimated Minutes: $getLengthInMinutes================");

        $estimatedTimestamp = Carbon::parse($this->request->arrival)->addHours($this->request->length)->addMinutes($getLengthInMinutes)->format('Y-m-d H:i:s');
        $this->reservationlog->info("=======Estimated Timestamp: $estimatedTimestamp ================");

        // dd($arrival_time, $utc_arrival_time, $endTimestamp, $utc_end_time, $estimatedTimestamp, $getLengthInMinutes);
        $reservationMode = $this->request->reservation_mode;

        $user = User::find($this->request->user_id);

        // Decision for Editing in makeReservation Function
        $this->request->request->add(['is_edit' => true]);

        // Variables:
        $amountPaid = $parkingAmount = $processingFee = $taxRate = $payableAmount = $reservationAmount  = $altered_length = $length_type = $refundAmount = $totalAmount = 0;

        // Check if the difference is greater than 1 hour
        if ($start_timestamp_db->diffInRealMinutes($currentTime) > $facility->facilityConfiguration->before_cancel_time) {
            $this->reservationlog->info("=================== Case: 1 ================");
            $this->reservationlog->info("Time difference is > 1hr  Minutes: " . $facility->facilityConfiguration->before_cancel_time);
            if ($this->request->length != $reservation->length || $this->request->length > $reservation->length || $this->request->length < $reservation->length) {
                // Price based on Length Manupulation
                if ($this->request->length > $reservation->length) {
                    $altered_length = QueryBuilder::getLengthInMints($this->request->length - $reservation->length);
                    $length_type = '1';
                    $reservationMode = 2;
                    $this->reservationlog->info("Extended MInutes: " . $altered_length . " and Length Type: " . $length_type);

                    $this->request->request->add(['length_of_stay' => $this->request->length]);

                    $discount = 0;
                    if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
                        $discount = $this->request->redeemed_amount;
                        $reservation->discount = $discount;
                        $this->reservationlog->info("Promocode: {$reservation->promocode} Applied on Extend, Discount: $discount");
                    } else {
                        // $discount = $reservation->getDiscountedValue($reservation->id);
                        $this->reservationlog->info("No discount found: $discount");
                    }

                    $this->reservationlog->info("Before makeReservation Function");
                    if ($this->request->total > 0) {
                        $details = $this->makeReservation();

                        $parkingAmount = $details['rate']['price'];
                        $processingFee = $this->facility->getProcessingFee(1); // 1 is for reservation processing fee
                        $taxRate = $this->facility->getTaxRate($details['rate'], 1);
                        // $taxRate = $this->request->tax_fee;


                        if ($parkingAmount > 0 && $processingFee > 0)
                            $reservation->processing_fee = $processingFee;

                        if ($parkingAmount > 0 && $this->request->tax_fee > 0)
                            $reservation->tax_fee =  $this->request->tax_fee;

                        $payableAmount = $this->request->total;
                        $amountPaid = $reservation->total;
                        $reservationAmount = $parkingAmount + $processingFee + $taxRate;
                        $totalAmount = ($payableAmount + $amountPaid);

                        $this->reservationlog->info("Price on Update Resevation => Parking Amount: $parkingAmount Reservation Amount: $reservationAmount ProcessingFee: $processingFee Tax Fee: $taxRate Discount: $discount");

                        $reservation->parking_amount = $parkingAmount;                             // cost defined for partner
                        $reservation->reservation_amount = $reservationAmount;                    // tax + processing + parking_amount
                        $reservation->discount = $discount;

                        $reservation->total = $totalAmount;                     // tax + processing + parking_amount - discount

                    } else {
                        $this->reservationlog->info("Amount is zero");
                        $isMember = 0;
                        if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0) {
                            $rate = $facility->rateForReservationByPassRateEngine($this->request->arrival, $this->request->length, '0', false, null, false, '0', '0', $isMember);
                        } else {
                            $rate = $facility->rateForReservationOnMarker($this->request->arrival, $this->request->length, '0', false, null, false, '0', '0', $isMember);
                        }
                        $parkingAmount = $rate['price'];
                        $taxRate = $facility->getTaxRate($rate, 1);
                        $processingFee = $facility->getProcessingFee(1); // 1 is for reservation processing fee
                        $reservationAmount = $parkingAmount + $taxRate + $processingFee;
                        $totalAmount = $reservationAmount - $discount;

                        $sentRate = $this->request->total + $reservation->total + $discount;
                        if ($processingFee > 0)
                            $sentRate -= $processingFee;

                        if ($taxRate > 0)
                            $sentRate -= $taxRate;

                        $this->reservationlog->info("Price on Update: Parking Amount: $parkingAmount Reservation Amount: $reservationAmount ProcessingFee: $processingFee Tax Fee: $taxRate Discount: $discount");

                        if (bccomp($sentRate, $rate['price'], 2) !== 0) { // Lokesh: Change for Reservation Price comparison coming wrong on above conditions discuss with vijay
                            throw new ApiGenericException(
                                'Your parking amount is revised. please refresh your screen.',
                                422,
                                ['sent_rate' => $sentRate, 'database_rate' => $rate['price'], 'isratemismatch' => 1]
                            );
                        }

                        $reservation->reservation_amount = $reservationAmount;
                        $reservation->parking_amount = $rate['price'];
                        $reservation->tax_fee =  $taxRate;
                        $reservation->processing_fee =  $processingFee;
                        $reservation->discount = $discount;
                        $reservation->total = $totalAmount;
                    }
                    $this->reservationlog->info("After makeReservation Function");

                    // dd($totalAmount, $parkingAmount, $reservationAmount, $processingFee,  $taxRate, $discount);

                    $reservation->start_timestamp = $arrival_time;
                    $reservation->utc_start_timestamp = $utc_arrival_time;
                    $reservation->end_timestamp = $endTimestamp;
                    $reservation->utc_end_timestamp = $utc_end_time;

                    $reservation->length = $this->request->length;
                    $reservation->estimated_checkout_time = $estimatedTimestamp;
                    $reservation->length_type = $length_type;
                    $reservation->altered_length = $altered_length;
                    // Lokesh: 23-Aug
                    // save promo code in reservation
                    if (isset($request->promocode) && !empty($request->promocode)) {
                        $reservation->promocode = $request->promocode;
                    }
                    $reservation->save();
                } else if ($this->request->length < $reservation->length) {
                    $this->reservationlog->info("Reduce or Refund Case!");
                    $altered_length = QueryBuilder::getLengthInMints($reservation->length - $this->request->length);
                    $length_type = '2';
                    $reservationMode = 1;
                    $this->reservationlog->info("Reduced Minutes: " . $altered_length . " and Length Type: " . $length_type);

                    // $this->request->request->add(['length_of_stay' => QueryBuilder::getLengthInHours(QueryBuilder::getLengthInMints($reservation->length) - QueryBuilder::getLengthInMints($this->request->length))]);
                    $this->reservationlog->info("Length of stay in Hours: " . $this->request->length_of_stay);

                    $isMember = 0;
                    if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0) {
                        $rate = $facility->rateForReservationByPassRateEngine($this->request->arrival, $this->request->length, '0', false, null, false, '0', '0', $isMember);
                    } else {
                        $rate = $facility->rateForReservationOnMarker($this->request->arrival, $this->request->length, '0', false, null, false, '0', '0', $isMember);
                    }

                    // return $rate;
                    if (!$rate) {
                        $this->reservationlog->info("No rate found in database for this reservation.");
                        throw new ApiGenericException('No rate found in database for this reservation.', 422);
                    }
                    $priceUpdate = true;

                    $parkingAmount = $rate['price'];
                    $tax_rate = $facility->getTaxRate($rate, 1);
                    $processingFee = $facility->getProcessingFee(1);
                    $reservationAmount = $parkingAmount > 0 ? ($parkingAmount + $tax_rate + $processingFee) : '0';

                    // Actual paid 
                    $amountPaid = $reservation->total;
                    $discount = $reservation->discount;

                    // Payment remaining after refund
                    $totalAmount = $reservationAmount - $discount;

                    // Actual refund
                    $refundAmount = ($amountPaid - $totalAmount);

                    $sentRate = $this->request->refund_amount;

                    $ratesData['database_rate'] = $rate['price']; // Rate get from rate api
                    $ratesData['sent_rate'] = $sentRate;    // rate sent from request => total
                    $ratesData['tax_rate'] = $tax_rate;
                    $ratesData['processing_fee'] = $processingFee;
                    $ratesData['discount'] = $this->request->discount;
                    $ratesData['redeemed_amount'] = $this->request->redeemed_amount;
                    $ratesData['refund_amount'] = $refundAmount;
                    $ratesData['amount_paid'] = $amountPaid;
                    $ratesData['discount'] = $discount;

                    $this->reservationlog->info("Rate from API Update: " .  json_encode($ratesData));

                    // if (filter_var($sentRate, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $refundAmount) {
                    if (bccomp($sentRate, $refundAmount, 2) !== 0) {
                        throw new ApiGenericException(
                            'Your parking amount is revised. please refresh your screen.',
                            422,
                            ['sent_rate' => $sentRate, 'database_rate' => $refundAmount]
                        );
                    }

                    if (filter_var($sentRate, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) == $refundAmount) {
                        $priceUpdate = false;
                        if ($amountPaid > $reservationAmount) {
                            $priceUpdate = true;
                        }
                    }

                    $reservation->start_timestamp = $arrival_time;
                    $reservation->end_timestamp = $endTimestamp;
                    $reservation->utc_start_timestamp = $utc_arrival_time;
                    $reservation->utc_end_timestamp = $utc_end_time;
                    $reservation->length = $this->request->length;
                    $reservation->length_type = $length_type;
                    $reservation->altered_length = $altered_length;
                    $reservation->estimated_checkout_time = $estimatedTimestamp;

                    if ($priceUpdate) {
                        $reservation->reservation_amount = $reservationAmount;
                        $reservation->parking_amount = $parkingAmount;
                        $reservation->tax_fee = $tax_rate;
                        $reservation->processing_fee  = $processingFee;
                        $reservation->total = $totalAmount;
                    }

                    // save promo code in reservation
                    if (isset($request->promocode) && !empty($request->promocode)) {
                        $reservation->promocode = $request->promocode;
                        $this->reservationlog->info("Promocode: {$request->promocode} amount is: $refundAmount, And Sent Rate is: $sentRate");
                    }
                    // Refund Function call
                    $this->reservationlog->info("Refund amount is: $refundAmount, And Sent Rate is: $sentRate");
                    if ($sentRate > 0 && !is_null($reservation->anet_transaction_id)) {
                        if (isset($reservation->facility->FacilityPaymentDetails) && ($reservation->facility->FacilityPaymentDetails->facility_payment_type_id == '1')) {
                            $this->reservationlog->info("=== Refund Initiated with Planet Start ===");
                            // dd($reservation->total, $reservationAmount, $refundAmount, $priceUpdate, $sentRate);
                            $refundstatus = PlanetPaymentGateway::planetPaymentRefund($reservation->id);
                            // dd($refundstatus);
                            if ($refundstatus) {
                                $reservation->payment_gateway = 'planet';
                                foreach ($refundstatus as $val) {
                                    if ($val['Params']['TxState'] == 'AR' || $val['Params']['TxState'] == 'CQ') {
                                        $reservation->refund_transaction_id = $val['Params']['TxID'];
                                        $reservation->refund_status = $val['Params']['ResultReason'];
                                        $reservation->refund_amount = $refundAmount;
                                        $reservation->save();
                                        $this->reservationlog->info("Payment refund  to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($val['Params']));
                                    } else {
                                        $reservation->refund_transaction_id = $val['Params']['TxID'];
                                        $reservation->refund_status = $val['Params']['ResultReason'];
                                        $reservation->refund_amount = $refundAmount;
                                        $reservation->save();
                                        $this->reservationlog->info("Payment refund fail to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($val['Params']));
                                        throw new ApiGenericException('Refund Fail due to' . " " . $val['Params']['ResultReason']);
                                    }
                                }
                            } else {
                                throw new ApiGenericException('Refund Payment Gateway Issue');
                            }
                            $this->reservationlog->info("=== Refund Initiated with Planet End ===");
                        } else if (isset($reservation->facility->FacilityPaymentDetails) && ($reservation->facility->FacilityPaymentDetails->facility_payment_type_id == '2')) {
                            $this->reservationlog->info("=== Refund Initiated with DataCap Start ===");
                            $ecommerce_mid = $reservation->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $reservation->facility->FacilityPaymentDetails->datacap_refund_url;
                            $refund_type = 'reservation';
                            if ($reservation->payment_token == '') {
                                throw new ApiGenericException('Datacap token not found.');
                            }
                            $refundstatus = DatacapPaymentGateway::makePaymentRefundDataCap($reservation->id, $ecommerce_mid, $url, $refund_type, $refundAmount);
                            $reservation->refund_status = $refundstatus['Message'];
                            $reservation->refund_amount = $refundAmount;
                            $reservation->payment_gateway = 'datacap';
                            $this->reservationlog->info("Payment refund Log Datacap #:" . json_encode($refundstatus));
                            $this->reservationlog->info("=== Refund Initiated with DataCap End ===");
                        } else if (isset($reservation->facility->FacilityPaymentDetails) && ($reservation->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                            $this->reservationlog->info("=== Refund Initiated with Heartland Start ===");
                            try {
                                $reservation->payment_gateway = 'heartland';
                                $paymentResponse = HeartlandPaymentGateway::heartlandPaymentRefund($reservation->id, $reservation->facility, $refundAmount);
                                if (($paymentResponse == NULL) || ($paymentResponse == FALSE)) {
                                    throw new ApiGenericException('Please contact to Admin');
                                }
                                //if ($paymentResponse->responseMessage == 'Success') {
                                if (in_array($paymentResponse->responseMessage, ["Success", "APPROVAL"])) {
                                    $reservation->refund_transaction_id = $paymentResponse->transactionReference->transactionId;
                                    $reservation->refund_status = $paymentResponse->responseMessage;
                                    $reservation->refund_amount = $refundAmount;
                                    $reservation->save();
                                    $this->reservationlog->info("Heartland Payment refund  to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($paymentResponse));
                                } else {
                                    $reservation->refund_transaction_id = $paymentResponse->transactionReference->transactionId;
                                    $reservation->refund_status = $paymentResponse->responseMessage;
                                    $reservation->refund_amount = $refundAmount;
                                    $reservation->save();
                                    $this->reservationlog->info("Heartland Payment refund fail to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($paymentResponse));
                                    throw new ApiGenericException('Please contact to Admin');
                                }
                            } catch (Exception $e) {
                                if ($e->getMessage() == "Unexpected Gateway Response: 6 - Transaction rejected because amount to be returned exceeds the original settlement amount or the return amount is zero.. ") {
                                    throw new ApiGenericException('Transaction Already Canceled.');
                                }
                            }
                            $this->reservationlog->info("=== Refund Initiated with Heartland End ===");
                        }
                    } else {
                        // Pre Auth Release and new charge
                        $this->request->request->add(['reservation' => $reservation, 'ticket_code' => $reservation->ticketech_code]);
                        if (isset($reservation->facility->FacilityPaymentDetails) && ($reservation->facility->FacilityPaymentDetails->facility_payment_type_id == '2') && $sentRate > 0) {
                            $paymentStatus = $this->makeDatacapPaymentPreAuth($this->request, $reservation->facility);
                        }
                        $reservation->save();
                    }
                }
                $this->reservationlog->info("Length Altered: ");
            } else {
                $this->reservationlog->info("Length Not Altered, Simple Data Update");
                $altered_length = $this->request->length - $reservation->length;
                $length_type = '3'; // for No Change

                $reservation->start_timestamp = $arrival_time;
                $reservation->utc_start_timestamp = $utc_arrival_time;
                $reservation->end_timestamp = $endTimestamp;
                $reservation->utc_end_timestamp = $utc_end_time;
                $reservation->length = $this->request->length;
                $reservation->estimated_checkout_time = $estimatedTimestamp;
                // save promo code in reservation
                if (isset($request->promocode) && !empty($request->promocode)) {
                    $reservation->promocode = $request->promocode;
                }
                $reservation->save();
                $reservationMode = 3; // for Edit
            }

            $this->reservationlog->info("User Update before > 1hr or Minutes: " . $facility->facilityConfiguration->before_cancel_time);

            if (isset($this->request->name) && !empty($this->request->name) && $user->name != $this->request->name) {
                $user->name = $name;
            }

            if (($user->email == "") || ($user->email == NULL)) {
                $user->email = $request->email;
            }

            if (($user->phone == "") || ($user->phone == NULL)) {
                $user->phone =  $this->countryCode . $request->phone;
            }

            if (isset($this->request->companyName) && !empty($this->request->companyName) && empty($user->company_name)) {
                $user->company_name = $this->request->companyName;
            }
            if (isset($this->request->address) && !empty($this->request->address) && empty($user->address)) {
                $user->address = $this->request->address;
            }
            if (isset($this->request->address2) && !empty($this->request->address2) && empty($user->address2)) {
                $user->address2 = $this->request->address2;
            }
            if (isset($this->request->city) && !empty($this->request->city) && empty($user->city)) {
                $user->city = $this->request->city;
            }
            if (isset($this->request->country) && !empty($this->request->country) && empty($user->country)) {
                $user->country = $this->request->country;
            }
            if (isset($this->request->state) && !empty($this->request->state) && empty($user->state)) {
                $user->state = $this->request->state;
            }
            if (isset($this->request->pincode) && !empty($this->request->pincode) && empty($user->pincode)) {
                $user->pincode = $this->request->pincode;
            }

            $user->save();

            // if start time is less than one hour.
        } else if ($start_timestamp_db->diffInRealMinutes($currentTime) < $facility->facilityConfiguration->before_cancel_time) {
            $this->reservationlog->info("=================== Case: 2 ================");
            $this->reservationlog->info("Time difference is < 1hr or Minutes: " . $facility->facilityConfiguration->before_cancel_time);
            // End Time can be pushed but not reduced
            if ($endTimestamp < $end_timestamp_db && !$request->is_admin) {
                $this->reservationlog->info("The end time can only be pushed out, it can not be shortened. 1 " . json_encode($endTimestamp < $end_timestamp_db));
                throw new ApiGenericException("The end time can only be pushed out, it can not be shortened.");
            }

            // End time Exceed
            if ($currentTime > $start_timestamp_db && $currentTime > $end_timestamp_db) {
                $this->reservationlog->info("Modification time expire");
                throw new ApiGenericException("Modification time expire");
            }

            if ($arrival_time > $start_timestamp_db && !$request->is_admin) {
                $this->reservationlog->info("Modification time expire");
                throw new ApiGenericException("Start Time Can't be modified.");
            }

            if ($this->request->length > $reservation->length) {

                $this->reservationlog->info("Extend Case!");
                $altered_length = QueryBuilder::getLengthInMints($this->request->length - $reservation->length);
                $length_type = '1';
                $reservationMode = 2;
                $this->reservationlog->info("Extended MInutes: " . $altered_length . " and Length Type: " . $length_type);

                $this->request->request->add(['length_of_stay' => $this->request->length]);

                $this->request->request->add(['is_edit' => true]);

                $discount = 0;
                if (isset($this->request->redeemed_amount) && $this->request->redeemed_amount > 0) {
                    $this->reservationlog->info("Promocode Applied on Extend!");
                    $discount = $this->request->redeemed_amount;
                } else {
                    // $discount = $reservation->getDiscountedValue($reservation->id);
                    $this->reservationlog->info("No discount found: $discount");
                }

                $this->reservationlog->info("Before makeReservation Function");
                if ($this->request->total > 0) {
                    $details = $this->makeReservation();

                    $parkingAmount = $details['rate']['price'];
                    // $taxRate = $this->facility->getTaxRate($details['rate'], 1);
                    $processingFee = $this->facility->getProcessingFee(1); // 1 is for reservation processing fee

                    if ($parkingAmount > 0 && $processingFee > 0)
                        $reservation->processing_fee = $processingFee;

                    if ($parkingAmount > 0 && $this->request->tax_fee > 0) {
                        $reservation->tax_fee =  $this->request->tax_fee;
                        $taxRate = $this->request->tax_fee;
                    }

                    $reservationAmount = $parkingAmount + $reservation->processing_fee + $taxRate;
                    $payableAmount = $this->request->total;
                    $amountPaid = $reservation->total;
                    $totalAmount = ($payableAmount + $amountPaid);

                    $reservation->parking_amount = $parkingAmount;                             // cost defined for partner
                    $reservation->reservation_amount = $reservationAmount;                    // tax + processing + parking_amount
                    $reservation->discount = $discount;
                    $reservation->total = $totalAmount;
                } else {
                    $this->reservationlog->info("Amount is zero");
                    $isMember = 0;
                    if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0) {
                        $rate = $facility->rateForReservationByPassRateEngine($this->request->arrival, $this->request->length, '0', false, null, false, '0', '0', $isMember);
                    } else {
                        $rate = $facility->rateForReservationOnMarker($this->request->arrival, $this->request->length, '0', false, null, false, '0', '0', $isMember);
                    }
                    $parkingAmount = $rate['price'];
                    $taxRate = $facility->getTaxRate($rate, 1);
                    $processingFee = $facility->getProcessingFee(1); // 1 is for reservation processing fee
                    $reservationAmount = $parkingAmount + $taxRate + $processingFee;
                    $totalAmount = $reservationAmount - $discount;

                    $sentRate = $this->request->total + $reservation->total + $discount;
                    if ($processingFee > 0)
                        $sentRate -= $processingFee;

                    if ($taxRate > 0)
                        $sentRate -= $taxRate;

                    $this->reservationlog->info("Price on Update: Parking Amount: $parkingAmount Reservation Amount: $reservationAmount ProcessingFee: $processingFee Tax Fee: $taxRate Discount: $discount");

                    if (bccomp($sentRate, $rate['price'], 2) !== 0) { // Lokesh: Change for Reservation Price comparison coming wrong on above conditions discuss with vijay
                        throw new ApiGenericException(
                            'Your parking amount is revised. please refresh your screen.',
                            422,
                            ['sent_rate' => $sentRate, 'database_rate' => $rate['price'], 'isratemismatch' => 1]
                        );
                    }

                    $reservation->reservation_amount = $reservationAmount;
                    $reservation->parking_amount = $rate['price'];
                    $reservation->tax_fee =  $taxRate;
                    $reservation->processing_fee =  $processingFee;
                    $reservation->discount = $discount;
                    $reservation->total = $totalAmount;
                }
                $this->reservationlog->info("After makeReservation Function");
                $this->reservationlog->info("Price on Update Resevation => Parking Amount: $parkingAmount Reservation Amount: $reservationAmount ProcessingFee: $processingFee Tax Fee: $taxRate Discount: $discount Total:  $reservation->total");
                // dd($reservationAmount, $parkingAmount, $taxRate, $processingFee);

                // save promo code in reservation
                if (isset($request->promocode) && !empty($request->promocode)) {
                    $reservation->promocode = $request->promocode;
                }
                $reservation->start_timestamp = $arrival_time;
                $reservation->utc_start_timestamp = $utc_arrival_time;
                $reservation->end_timestamp = $endTimestamp;
                $reservation->utc_end_timestamp = $utc_end_time;
                $reservation->length = $this->request->length;
                $reservation->length_type = $length_type;
                $reservation->altered_length = $altered_length;
                $reservation->estimated_checkout_time = $estimatedTimestamp;
                $reservation->save();
            } else if ($this->request->length < $reservation->length && !$request->is_admin) {
                $this->reservationlog->info("Time difference is < 1hr or Minutes: " . $facility->facilityConfiguration->before_cancel_time);
                throw new ApiGenericException("The end time can only be pushed out, it can not be shortened.");
            } else {
                if ($this->request->length < $reservation->length) {
                    $this->reservationlog->info("Reduce or Refund Case!");
                    $altered_length = QueryBuilder::getLengthInMints($reservation->length - $this->request->length);
                    $length_type = '2';
                    $reservationMode = 1;
                    $this->reservationlog->info("Reduced Minutes: " . $altered_length . " and Length Type: " . $length_type); // converted into minutes on 21 March

                    // $this->request->request->add(['length_of_stay' => QueryBuilder::getLengthInHours(QueryBuilder::getLengthInMints($reservation->length) - QueryBuilder::getLengthInMints($this->request->length))]);
                    $this->reservationlog->info("Length of stay in Hours: " . $this->request->length_of_stay);

                    $isMember = 0;
                    if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0) {
                        $rate = $facility->rateForReservationByPassRateEngine($this->request->arrival, $this->request->length, '0', false, null, false, '0', '0', $isMember);
                    } else {
                        $rate = $facility->rateForReservationOnMarker($this->request->arrival, $this->request->length, '0', false, null, false, '0', '0', $isMember);
                    }

                    // return $rate;
                    if (!$rate) {
                        $this->reservationlog->info("No rate found in database for this reservation.");
                        throw new ApiGenericException('No rate found in database for this reservation.', 422);
                    }
                    $priceUpdate = true;

                    $parkingAmount = $rate['price'];
                    $tax_rate = $facility->getTaxRate($rate, 1);
                    $processingFee = $facility->getProcessingFee(1);
                    $reservationAmount = $parkingAmount > 0 ? ($parkingAmount + $tax_rate + $processingFee) : '0';

                    // Actual paid 
                    $amountPaid = $reservation->total;
                    $discount = $reservation->discount;

                    // Payment remaining after refund
                    $totalAmount = $reservationAmount - $discount;

                    // Actual refund
                    $refundAmount = ($amountPaid - $totalAmount);

                    $sentRate = $this->request->refund_amount;

                    $ratesData['database_rate'] = $rate['price']; // Rate get from rate api
                    $ratesData['sent_rate'] = $sentRate;    // rate sent from request => total
                    $ratesData['tax_rate'] = $tax_rate;
                    $ratesData['processing_fee'] = $processingFee;
                    $ratesData['discount'] = $this->request->discount;
                    $ratesData['redeemed_amount'] = $this->request->redeemed_amount;
                    $ratesData['refund_amount'] = $refundAmount;
                    $ratesData['amount_paid'] = $amountPaid;
                    $ratesData['discount'] = $discount;

                    $this->reservationlog->info("Rate from API Update: " .  json_encode($ratesData));

                    // if (filter_var($sentRate, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $refundAmount) {
                    if (bccomp($sentRate, $refundAmount, 2) !== 0) {
                        throw new ApiGenericException(
                            'Your parking amount is revised. please refresh your screen.',
                            422,
                            ['sent_rate' => $sentRate, 'database_rate' => $refundAmount]
                        );
                    }

                    if (filter_var($sentRate, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) == $refundAmount) {
                        $priceUpdate = false;
                        if ($amountPaid > $reservationAmount) {
                            $priceUpdate = true;
                        }
                    }

                    $reservation->start_timestamp = $arrival_time;
                    $reservation->end_timestamp = $endTimestamp;
                    $reservation->utc_start_timestamp = $utc_arrival_time;
                    $reservation->utc_end_timestamp = $utc_end_time;
                    $reservation->length = $this->request->length;
                    $reservation->length_type = $length_type;
                    $reservation->altered_length = $altered_length;
                    $reservation->estimated_checkout_time = $estimatedTimestamp;

                    if ($priceUpdate) {
                        $reservation->reservation_amount = $reservationAmount;
                        $reservation->parking_amount = $parkingAmount;
                        $reservation->tax_fee = $tax_rate;
                        $reservation->processing_fee  = $processingFee;
                        $reservation->total = $totalAmount;
                    }

                    // save promo code in reservation
                    if (isset($request->promocode) && !empty($request->promocode)) {
                        $reservation->promocode = $request->promocode;
                        $this->reservationlog->info("Promocode: {$request->promocode} amount is: $refundAmount, And Sent Rate is: $sentRate");
                    }
                    // Refund Function call
                    $this->reservationlog->info("Refund amount is: $refundAmount, And Sent Rate is: $sentRate");
                    if ($sentRate > 0 && !is_null($reservation->anet_transaction_id)) {
                        if (isset($reservation->facility->FacilityPaymentDetails) && ($reservation->facility->FacilityPaymentDetails->facility_payment_type_id == '1')) {
                            $this->reservationlog->info("=== Refund Initiated with Planet Start ===");
                            // dd($reservation->total, $reservationAmount, $refundAmount, $priceUpdate, $sentRate);
                            $refundstatus = PlanetPaymentGateway::planetPaymentRefund($reservation->id);
                            // dd($refundstatus);
                            if ($refundstatus) {
                                $reservation->payment_gateway = 'planet';
                                foreach ($refundstatus as $val) {
                                    if ($val['Params']['TxState'] == 'AR' || $val['Params']['TxState'] == 'CQ') {
                                        $reservation->refund_transaction_id = $val['Params']['TxID'];
                                        $reservation->refund_status = $val['Params']['ResultReason'];
                                        $reservation->refund_amount = $refundAmount;
                                        $reservation->save();
                                        $this->reservationlog->info("Payment refund  to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($val['Params']));
                                    } else {
                                        $reservation->refund_transaction_id = $val['Params']['TxID'];
                                        $reservation->refund_status = $val['Params']['ResultReason'];
                                        $reservation->refund_amount = $refundAmount;
                                        $reservation->save();
                                        $this->reservationlog->info("Payment refund fail to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($val['Params']));
                                        throw new ApiGenericException('Refund Fail due to' . " " . $val['Params']['ResultReason']);
                                    }
                                }
                            } else {
                                throw new ApiGenericException('Refund Payment Gateway Issue');
                            }
                            $this->reservationlog->info("=== Refund Initiated with Planet End ===");
                        } else if (isset($reservation->facility->FacilityPaymentDetails) && ($reservation->facility->FacilityPaymentDetails->facility_payment_type_id == '2')) {
                            $this->reservationlog->info("=== Refund Initiated with DataCap Start ===");
                            $ecommerce_mid = $reservation->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $reservation->facility->FacilityPaymentDetails->datacap_refund_url;
                            $refund_type = 'reservation';
                            if ($reservation->payment_token == '') {
                                throw new ApiGenericException('Datacap token not found.');
                            }
                            $refundstatus = DatacapPaymentGateway::makePaymentRefundDataCap($reservation->id, $ecommerce_mid, $url, $refund_type, $refundAmount);
                            $reservation->refund_status = $refundstatus['Message'];
                            $reservation->refund_amount = $refundAmount;
                            $reservation->payment_gateway = 'datacap';
                            $this->reservationlog->info("Payment refund Log Datacap #:" . json_encode($refundstatus));
                            $this->reservationlog->info("=== Refund Initiated with DataCap End ===");
                        } else if (isset($reservation->facility->FacilityPaymentDetails) && ($reservation->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                            $this->reservationlog->info("=== Refund Initiated with Heartland Start ===");
                            try {
                                $reservation->payment_gateway = 'heartland';
                                $paymentResponse = HeartlandPaymentGateway::heartlandPaymentRefund($reservation->id, $reservation->facility, $refundAmount);
                                if (($paymentResponse == NULL) || ($paymentResponse == FALSE)) {
                                    throw new ApiGenericException('Please contact to Admin');
                                }
                                //if ($paymentResponse->responseMessage == 'Success') {
                                if (in_array($paymentResponse->responseMessage, ["Success", "APPROVAL"])) {
                                    $reservation->refund_transaction_id = $paymentResponse->transactionReference->transactionId;
                                    $reservation->refund_status = $paymentResponse->responseMessage;
                                    $reservation->refund_amount = $refundAmount;
                                    $reservation->save();
                                    $this->reservationlog->info("Heartland Payment refund  to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($paymentResponse));
                                } else {
                                    $reservation->refund_transaction_id = $paymentResponse->transactionReference->transactionId;
                                    $reservation->refund_status = $paymentResponse->responseMessage;
                                    $reservation->refund_amount = $refundAmount;
                                    $reservation->save();
                                    $this->reservationlog->info("Heartland Payment refund fail to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($paymentResponse));
                                    throw new ApiGenericException('Please contact to Admin');
                                }
                            } catch (Exception $e) {
                                if ($e->getMessage() == "Unexpected Gateway Response: 6 - Transaction rejected because amount to be returned exceeds the original settlement amount or the return amount is zero.. ") {
                                    throw new ApiGenericException('Transaction Already Canceled.');
                                }
                            }
                            $this->reservationlog->info("=== Refund Initiated with Heartland End ===");
                        }
                    } else {
                        // Pre Auth Release and new charge
                        $this->request->request->add(['reservation' => $reservation, 'ticket_code' => $reservation->ticketech_code]);
                        if (isset($reservation->facility->FacilityPaymentDetails) && ($reservation->facility->FacilityPaymentDetails->facility_payment_type_id == '2') && $sentRate > 0) {
                            $paymentStatus = $this->makeDatacapPaymentPreAuth($this->request, $reservation->facility);
                        }
                        $reservation->save();
                    }
                }
            }
        }

        // Vehicle Detials
        $vehicle = PermitVehicle::handleVehicleDetails($this->request, $this->user, $this->reservationlog);
        $this->reservationlog->info("Final Vehicle Id : " . $vehicle->id . " Make id: " . $vehicle->make_id . " Model id: " . $vehicle->model_id);
        $reservation->vehicle_id = $vehicle->id;
        $reservation->license_plate = $this->request->license_plate;
        // Promocode on update
        if (isset($request->promocode) && !empty($request->promocode)) {
            $reservation->promocode = $request->promocode;
        }
        $reservation->is_update = '1';
        $reservation->save();

        // Flight Details
        $this->flightDetails($reservation);

        // User Histroy Created
        $this->user->saveUserHistroy($this->request, $reservation->id, 'reservation');

        $this->reservationlog->info("=========Before Email and SMS Notification on Update===========");
        if ($this->request->header('X-ClientSecret') != '') {
            $this->reservationlog->info("Email and SMS Start When Header in Request");
            if (in_array($this->user->user_prefrences, [0, 2, 3])) {
                $this->reservationlog->info("User Prefrences for Email On Update : " . $this->user->user_prefrences);
                $reservation->emailReservationToPartnerUser($this->request->header('X-ClientSecret'), $this->request);
            }
            //send sms to user
            if (in_array($this->user->user_prefrences, [0, 1, 3])) {
                if ($reservation->is_daily == '0') {
                    $startLabel = "Enter After";
                    $endLabel = "Exit Before";
                } else {
                    $startLabel = "Start Date";
                    $endLabel = "End Date";
                }

                $facilityName = ucwords($facility->full_name);

                $links = $url = "";

                $url = $facility->facilityConfiguration->base_url;

                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                    $join->on('user_facilities.user_id', '=', 'users.id');
                    $join->where('user_facilities.facility_id', "=", $facility->id);
                })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();

                $resUrl = $url . '/' . $getRM->slug . '/sms/' . base64_encode($reservation->id);

                if ($facility->facilityConfiguration->is_send_url) {
                    $links = "Url: $resUrl";
                }

                // convert length into days hours min 13-09-2023 by Ashutosh
                $duration = QueryBuilder::getFormatedDurationByLength($reservation->length);

                $msg = "Thank you updating for your booking with $facilityName.\nBooking # : $reservation->ticketech_code \n$startLabel : $reservation->formatted_start_date_time \n$endLabel : $reservation->formatted_end_date_time \nAmount Charged: $" . $reservation->total . " \nDuration : $duration \n$links";

                CommonFunctions::sendSmsAfterReservation($reservation->id, $msg, $this->reservationlog);
            }
        } else {
            $this->reservationlog->info("Email and SMS Start without Header");
            if (in_array($this->user->user_prefrences, [0, 2, 3])) {
                $reservation->emailReservationToUser(); //uncommented the code for guest user
                $this->reservationlog->info("User Prefrences for Email on Update: " . $this->user->user_prefrences);
            }
        }
        $this->reservationlog->info("========After Email and SMS Notification on Update { $reservation->ticketech_code } =========");

        // Hub Api Integration
        if (config('parkengage.ROC_FACILITY') == $reservation->facility_id && config('parkengage.HUB_ZEAG.status')) {
            // Artisan::call('hubzeag:reservation', ['reservation_id' => $this->request->reservation_id, "method" => "PUT"]);
            $this->dispatchJobForParkingHub($reservation->id, "PUT");
            $this->reservationlog->info("Reservation Update On HubZeag Ticket:" . $reservation->ticketech_code);
        }

        try {
            $this->reservationHistory($reservation->id, $payableAmount, $reservationMode);
            $this->reservationlog->info("============= Reservation Update End with: res_id " . $reservation->id . "========== Vehicle Id: " . $reservation->vehicle_id);
        } catch (\Throwable $th) {
            $this->reservationlog->info("Exception reservation History Call  :  " . $th->getMessage() . ' File ' . $th->getFile() . ' Line ' . $th->getLine());
        }


        $reservation->is_card_added = (isset($request->is_card_added) && $request->is_card_added == 1) ? "1" : "0";
        $this->reservationlog->info("=======Reservation Update END ===========");
        return $reservation;
    }

    // Create job HubParking
    public function dispatchJobForParkingHub($reservation_id, $method)
    {
        try {
            $this->reservationlog->info("Reservation Request for Hub Api from job Start: " . $reservation_id);
            dispatch((new HubParking($reservation_id, $method))->onQueue(self::QUEUE_NAME_HUB_PARKING));
            return "Job runned Successfully";
        } catch (\Throwable $th) {
            $this->reservationlog->info("Exception in Hub Api Job:  " . $th->getMessage() . ' File ' . $th->getFile() . ' Line ' . $th->getLine());
        }
        $this->reservationlog->info("Reservation Hub Api from job End");
    }

    // Validate of LicensePlate
    public function validateLicensePlate($licensePlate)
    {
        $minLength = 2;
        $maxLength = 10;

        if (strlen($licensePlate) < $minLength || strlen($licensePlate) > $maxLength) {
            return false;
        }

        return true;
    }

    public function userReservationCreate(Request $request)
    {
        $this->reservationlog->info("==================Reservation Start Creating=======================");
        $this->reservationlog->info("Booking Request :" . json_encode($request->all()));
        $this->validate($request, ['email' => 'required|email', 'phone' => 'required']);

        if ($request->header('X-ClientSecret') == '') {
            throw new ApiGenericException('Something went wrong.');
        }


        $request->request->add(['client_id' => $request->header('X-ClientSecret')]);
        $reference = rand(1000, 9999) . rand(1000, 9999) . rand(1000, 9999);
        $request->request->add(['ref_id' => $reference]);

        $this->facility = Facility::with('FacilityPaymentDetails')->find($request->facility_id);
        $partner_id = $this->facility->owner_id;
        $request->request->add(['partner_id' => $partner_id]);

        $this->reservationlog->info("Partner Id Against Reservation :" . $partner_id);

        // Fetch Logged In User
        // 1. Prepare phone number
        $this->countryCode = QueryBuilder::appendCountryCode();
        $countryCode = $this->countryCode;
        $phone = $countryCode . substr($request->phone, -10);

        // 2. Try to find user by email and phone first
        $user = User::where('created_by', $this->facility->owner_id)
            ->where('status', '1')
            ->where(function ($query) use ($request, $phone) {
                $query->where('email', $request->email)
                    ->where('phone', $phone);
            })->first();

        // 3. If not found, try by phone only
        if (!$user) {
            $user = User::where('created_by', $this->facility->owner_id)
                ->where('status', '1')
                ->where('phone', $phone)
                ->first();
        }

        // 4. Setup default values
        $name = $request->name ?? null;
        $anon = (isset($request->anon) && $request->anon == '0') ? 0 : 1;
        $userType = $anon ? 'Guest' : 'Registered';

        // 5. Create new user if not found
        if (!$user) {
            $user = User::create([
                'name'          => $name,
                'email'         => $this->request->email,
                'phone'         => $phone,
                'password'      => Hash::make(str_random(60)),
                'anon'          => $anon,
                'user_type'     => '5',
                'created_by'    => $this->facility->owner_id,
                'company_name'  => $this->request->companyName ?? null,
                'address'       => $this->request->address ?? null,
                'address2'      => $this->request->address2 ?? null,
                'city'          => $this->request->city ?? null,
                'country'       => $this->request->country ?? null,
                'state'         => $this->request->state ?? null,
                'pincode'       => $this->request->pincode ?? null,
            ]);

            $this->reservationlog->info("$userType User Created: " . json_encode([
                $user->id,
                $user->email,
                $user->phone,
                $user->anon
            ]));

            // 6. Else update user if needed
        } else {
            $fieldsToUpdate = [
                'name'         => $name,
                'email'        => $request->email,
                'phone'        => $phone,
                'company_name' => $this->request->companyName ?? null,
                'address'      => $this->request->address ?? null,
                'address2'     => $this->request->address2 ?? null,
                'city'         => $this->request->city ?? null,
                'country'      => $this->request->country ?? null,
                'state'        => $this->request->state ?? null,
                'pincode'      => $this->request->pincode ?? null,
            ];

            $hasChanges = false;
            foreach ($fieldsToUpdate as $key => $value) {
                if (!empty($value) && empty($user->$key)) {
                    $user->$key = $value;
                    $hasChanges = true;
                }
            }

            if ($hasChanges) {
                $user->save();
                $this->reservationlog->info("$userType User Updated: " . json_encode([
                    $user->id,
                    $user->email,
                    $user->phone,
                    $user->anon
                ]));
            }
        }

        $this->user = $user;
        $this->reservationlog->info("User Details:" . json_encode($user));

        // Check for License Plate
        if (isset($request->vehicle_id) && !empty($request->vehicle_id)) {
            $vehicle = PermitVehicle::find($request->vehicle_id);
            $license_plate = $vehicle->license_plate_number;
            if ($license_plate) {
                $request->merge(['license_plate' => $license_plate, 'license_plate_number' => $license_plate]);
            } else {
                throw new ApiGenericException('License Plate is Required');
            }
        } else if (isset($request->license_plate) && empty($request->license_plate)) {
            throw new ApiGenericException('License Plate is Required');
        } else {
            if (!$this->validateLicensePlate($request->license_plate)) {
                throw new ApiGenericException("The license plate must be between 2 and 10 characters.");
            }
        }

        // Check reservation existence for the same time slot
        #pims-10727 DD 19-09-2024 for grouping facility in usm
        if ($this->facility->owner_id == config('parkengage.PARTNER_USM')) {
            Reservation::checkExistingUsmReservation($this->request, $user->id, $this->reservationlog);
        } else {
            Reservation::checkExistingReservation($this->request, $user->id, $this->reservationlog);
        }


        $request->request->add(['user_id' => $user->id]);

        // Apple Pay, Google Pay Check for Prepay Condition to verify before making payment.
        if (isset($request->verify_prepay) && $request->verify_prepay == 1) {
            return $request->all();
        }

        $this->reservationlog->info("Before Reservation Function");
        $details = $this->makeReservation();
        $this->reservationlog->info("After Reservation Function");



        // Wallet Implementation Starts From Here
        $walletConfig = Wallet::getConfig();

        if (isset($request->is_wallet_redeemed)) { // Check if user has redeemed his/her wallet
            if ($request->is_wallet_redeemed && $request->redeemed_amount_credit != "" && $request->redeemed_amount_credit > 0) {
                $points = floor($request->redeemed_amount_credit * self::CREDIT_POINTS);
                Wallet::makeTransaction($this->user->id, 'DEBIT', 'REDEEM', $request->redeemed_amount_credit, null, $reservation_id, $points);
            }
        }

        /*$creditPoints = floor(($this->request->total - $this->facility->processing_fee) - $this->request->tax_fee);
        $pointsAmount = number_format($creditPoints/self::CREDIT_POINTS,2);
        Wallet::makeTransaction($this->user->id, 'CREDIT', 'LOYALTY-POINTS', $pointsAmount, null, $reservation_id, $creditPoints);*/

        // Return reservation and charge details to caller

        $returnData['reservation'] = $details['reservation']->toArray();
        $cruise = Cruise::select('cruise_name', 'id')->where('partner_id', $this->facility->owner_id)->where('id', $this->request->cruise_id)->first();
        $returnData['reservation']['cruise_name'] = isset($cruise->cruise_name) ? $cruise->cruise_name : '';

        //CruiseSchedule
        $cruiseSchedule = CruiseSchedule::where('is_active', 1)->where('cruise_id', $this->request->cruise_id)->whereNull('deleted_at')->first();
        $returnData['reservation']['cruise_schedule'] = isset($cruiseSchedule) ? $cruiseSchedule : '';
        $returnData['reservation']['is_card_added'] = (isset($request->is_card_added) && $request->is_card_added == 1) ? "1" : "0";
        // dd($request->is_card_added, $returnData['reservation']['is_card_added']);
        $returnData['ref_id'] = $details['reservation']['mer_reference'];

        $this->reservationlog->info("Before Reservation Push Notification");
        $updateJobParams = ['user_id' => $user->id, 'user_email' => $user->email, 'facility' => $details['reservation']->facility->full_name];
        Artisan::queue('pave:push-notification-make-reservation', $updateJobParams);
        $this->reservationlog->info("After Reservation Push Notification");

        $this->reservationlog->info("====================Reservation Created END=================================");

        return $returnData;
    }

    public function makePlanetPayment(Request $request)
    {
        $this->reservationlog->info("=========== Make Planet Payment Function Start ===================");

        $user_id = $this->user->id;
        $partner_id = $this->user->created_by;
        $validationID = '"' . $this->facility->FacilityPaymentDetails->planet_merchant_id . '"';
        $paymentURl = $this->facility->FacilityPaymentDetails->planet_payment_url;
        $validationCode = '"' . $this->facility->FacilityPaymentDetails->planet_validation_code . '"';
        $reference = rand(1000, 9999) . rand(1000, 9999) . rand(1000, 9999);
        $request->request->add(['ref_id' => $reference]);
        $total = $request->total;

        if ((!$validationID) || (!$paymentURl) || (!$validationCode)) {
            throw new ApiGenericException("Payment Settings Not Found");
        }
        if ($request->payment_profile_id != '') { // saved cards

            // Vijay : 05-12-2024 : New Add Card flow Payment change : PIMS- 11063
            if (isset($request->payment_profile_id) && !empty($request->payment_profile_id) && isset($request->user_id) && $request->user_id > 0) {
                try {
                    $payment_type_id    = $this->facility->FacilityPaymentDetails->facility_payment_type_id;
                    $cardCheck = GatewayHelper::getRegisterUserProfileId($payment_type_id, $request->card_last_four, $request->expiry, $request->user_id, $this->facility);
                    $request->request->add(['payment_profile_id' => $cardCheck->token]);
                } catch (\Throwable $th) {
                    throw new ApiGenericException("Payment Failed!");
                }
            }
            // Close here !!!

            $cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $partner_id)->where('token', $request->payment_profile_id)->first();
            if (!$cardCheck) {
                $this->request->reservation->delete();
                throw new ApiGenericException("Card details are invalid");
            }

            $total = (config('parkengage.PLANET_PAYMENT_ENV') == 'test') ? '3.00' : $total;
            $response = PlanetPaymentGateway::planetPaymentReservationByToken($this->facility, $total, $request);
            $this->reservationlog->info("Response Data Planet (Saved Cards): " . json_encode($response));

            $refundstatus = $response;
            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                $this->request->reservation->delete();
                return ['Status' => 'Error'];
            }

            if (isset($refundstatus["Response"]["Params"]["Token"])) {
                $userSessionExist = UserSession::where("user_id", $user_id)->where("session_id", $refundstatus["Response"]["Params"]["Token"])->first();
                if (!$userSessionExist) {
                    $userSession = new UserSession();
                    $userSession->user_id = $user_id;
                    $userSession->partner_id = $partner_id;
                    $userSession->session_id = $refundstatus["Response"]["Params"]["Token"];
                    $userSession->save();
                }
            }
        } else {
            if ($request->nonce) {
                $this->setDecryptedCard($request);
            }
            $total = (config('parkengage.PLANET_PAYMENT_ENV') == 'test') ? '3.00' : $total;
            $response = PlanetPaymentGateway::planetPaymentReservationByCrediCard($this->facility, $total, $request);
            $this->reservationlog->info("Response Data Planet (Non Saved Cards): " . json_encode($response));

            $refundstatus = $response;

            $savePlanetProfile = "";
            if ($request->user_consent == '1') {
                $cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('card_last_four', $this->request->card_pan_last4digits)->first();
                if ($cardCheck) {
                    $this->reservationlog->info("Card Already Added for this user");
                } else {
                    $savePlanetProfile = PlanetPaymentGateway::savePlanetCard($refundstatus, $user_id, $partner_id, $request);
                }
            }
            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                if ($cardAdded = PlanetPaymentProfile::where(['user_id' => $user_id, 'partner_id' => $partner_id])->first()) {
                    $this->reservationlog->info("Card Deleted because Payment Failed");
                    $cardAdded->delete();
                }

                $this->reservationlog->info("Before Reservation Deleted with" . json_encode($request));

                if (!isset($request->is_edit)) {
                    $this->reservationlog->info("Reservation Deleted");
                    $this->request->reservation->delete();
                }

                return ['Status' => 'Error'];
            }
            $this->reservationlog->info("=========== Make Planet Payment Function End ===================");
        }

        $authorized_anet_transaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $user_id);

        if (isset($refundstatus["Response"]["Params"]["Token"])) {
            $userSessionExist = UserSession::where("user_id", $user_id)->where("session_id", $refundstatus["Response"]["Params"]["Token"])->first();
            if (!$userSessionExist) {
                $userSession = new UserSession();
                $userSession->user_id = $user_id;
                $userSession->partner_id = $partner_id;
                $userSession->session_id = $refundstatus["Response"]["Params"]["Token"];
                $userSession->save();
            }
        }
        $authorized_anet_transaction->planet_token = isset($refundstatus["Response"]["Params"]["Token"]) ? $refundstatus["Response"]["Params"]["Token"] : '';
        return $authorized_anet_transaction;
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);

        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($partnerTimezone->timezone != '') {
                if ($facility->timezone != '') {
                    date_default_timezone_set($facility->timezone);
                } else if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }

    // Heartland Payment 

    public function makeHeartlandPayment($request, $reservation)
    {
        // dd($request->all());
        $paymentEnv = isset($this->facility->FacilityPaymentDetails->heartland_payment_env) ? $this->facility->FacilityPaymentDetails->heartland_payment_env : config('parkengage.HEARTLAND_PAYMENT_ENV');

        $amount = ($paymentEnv == 'test') ? '3.00' : $request->total;

        if ($request->payment_profile_id != '') { // saved cards
            // Vijay : 05-12-2024 : New Add Card flow Payment change : PIMS- 11063
            if (isset($request->payment_profile_id) && !empty($request->payment_profile_id) && isset($request->user_id) && $request->user_id > 0) {
                try {
                    $payment_type_id    = $this->facility->FacilityPaymentDetails->facility_payment_type_id;
                    $cardCheck = GatewayHelper::getRegisterUserProfileId($payment_type_id, $request->card_last_four, $request->expiry, $request->user_id, $this->facility);
                    $request->request->add(['payment_profile_id' => $cardCheck->token]);
                } catch (\Throwable $th) {
                    throw new ApiGenericException("Payment Failed!");
                    throw new ApiGenericException($th->getMessage() . ' File  ' . $th->getFile() . ' Line ' . $th->getLine());
                }
            }
            // dd($cardCheck);
            // Close here !!!

            $cardCheck = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $request->user_id)->where('token', $request->payment_profile_id)->first();
            if (!$cardCheck) {
                if (!isset($this->request->is_edit))
                    $reservation->delete();
                $this->reservationlog->info("Payment Failed, Please Try Again");
                throw new ApiGenericException("Payment Failed, Please Try Again.");
            }
            $request->request->add(['is_card_added' => "1"]);
            $request->request->add(['expiration' => $cardCheck->expiry]);
            $request->request->add(['expiration_date' => $cardCheck->expiry]);
            $request->request->add(['card_last_four' => $cardCheck->card_last_four]);
            $request->request->add(['zipcode' => $request->pincode]);
            $this->reservationlog->info("Payment Profile Data --" . $cardCheck);

            if ($amount > 0) {
                $request->request->add(['Amount' => $amount]);
                $request->request->add(['token' => $cardCheck->token]);
                $this->reservationlog->info("Request Before Payment: " . json_encode($request->all()));
                try {
                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $this->facility);
                    $this->reservationlog->info("Response Data Heartland (Saved Cards): " . json_encode($paymentResponse));
                } catch (Exception $e) {
                    $this->reservationlog->info("Error in Heartland Payment with payment profile id --" . json_encode($e->getMessage()));
                    if (!isset($this->request->is_edit))
                        $reservation->delete();
                    throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
                }
            }
        } else if ($request->nonce) {
            $this->reservationlog->info("Request Data Heartland: " . json_encode($request->all()));
            $this->setDecryptedCard($request);
            // otuPaymentTokenHeartland
            $otu_token = HeartlandPaymentGateway::otuPaymentTokenHeartland($this->request, $this->facility);
            //dd($otu_token);
            $this->reservationlog->info("Heartland OTU Token: " . json_encode($otu_token));

            if ($otu_token) {

                if (isset($otu_token["error"]["code"]) == "2" || isset($otu_token["error"]["code"]) == 2) {
                    if (!isset($this->request->is_edit))
                        $request->reservation->delete();
                    throw new ApiGenericException("Payment failed. Please correct your card details or try again with a different card");
                }

                $amount = ($paymentEnv == 'test') ? '3.00' : $request->total;
                $request->request->add(['Amount' => $amount]);
                $request->request->add(['token' => $otu_token['token_value']]);

                try {
                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($this->request, $this->facility);
                    $this->reservationlog->info("Response Data Heartland : " . json_encode($paymentResponse));
                } catch (Exception $e) {
                    $this->reservationlog->info("Error in Heartland Payment with card --" . json_encode($e->getMessage()));
                    if (!isset($this->request->is_edit))
                        $reservation->delete();
                    throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
                }
            } else {
                $this->reservationlog->info("Heartland gateway is down");
                if (!isset($this->request->is_edit))
                    $reservation->delete();
                throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
            }

            $card_month = substr($request->expiration_date, 0, 2);
            $card_year = substr($request->expiration_date, -2);
            $request->request->add(['expiration_month' => $card_month]);
            $request->request->add(['expiration_year' => $card_year]);
            $card_last_four = substr($request->card_number, -4);
            $request->request->add(['card_last_four' => $card_last_four]);
            $request->request->add(['name_on_card' => $request->name_on_card]);

            // $paymentRecord = json_decode($paymentResponse, TRUE);
            if (isset($paymentResponse) &&  $paymentResponse->responseMessage == 'APPROVAL') {
                $request->request->add(['expiration' => $request->expiration_date]);
                $request->request->add(['expiration_date' => $request->expiration_date]);
                $request->request->add(['multi_token' => isset($paymentResponse->token) ? $paymentResponse->token : '']);
                if ($request->user_consent == 1) {
                    $cardCheck = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $request->user_id)->where('card_last_four', $card_last_four)->first();

                    if ($request->user_consent == 1 && !$cardCheck) {
                        if (!empty($paymentResponse->token)) {
                            $this->reservationlog->info("Card Saved:");
                            $response = HeartlandPaymentGateway::saveHeartlandCard($paymentResponse, $request->user_id, $request);
                        } else {
                            $this->reservationlog->info("Card Not Saved:");
                            $request->request->add(['is_card_added' => "0"]);
                        }
                    }
                }
            } else {
                if (!isset($this->request->is_edit))
                    $reservation->delete();
                throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
            }
        }
        $this->reservationlog->info("Payment Response: " . $paymentResponse->responseMessage);
        if ($paymentResponse->responseMessage == 'APPROVAL') {
            $user_id = $this->user->id;
            $paymentStatus = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $user_id);
            $this->reservationlog->info("Payment Transaction Data make Heartland Payment -- " . json_encode($paymentStatus));
            return $paymentStatus;
        } else {
            if (!isset($this->request->is_edit))
                $reservation->delete();
            throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
        }
        //
    }

    public function updateExtendTicket($reservation, $ticket, $data)
    {
        $this->reservationlog->info("updateExtendTicket function call");
        $extendTicket = TicketExtend::where('ticket_id', $ticket->id)->orderBy('id', 'desc')->first();
        $this->reservationlog->info("Get ticket extend table Data: " . json_encode($extendTicket));
        $ticketData = Ticket::select('length')->where('ticket_number', $ticket->ticket_number)->orderBy('id', 'desc')
            ->first();
        $this->reservationlog->info("Get ticket Data: " . json_encode($ticketData));

        if (!empty($extendTicket)) {
            $this->reservationlog->info("Extend data found for perticular ticket id");

            NotificationLog::where('reservation_id', $reservation->id)->where('slug_name', 'check-out-reminder')->delete();
            $this->reservationlog->info("Notification log deleted for existing entry");

            $ticketextend = new TicketExtend();
            $ticketextend->ticket_id        = $ticket->id;
            $ticketextend->length           = $data['length'];
            $ticketextend->ticket_number    = $ticket->ticket_number;
            $ticketextend->facility_id      = $ticket->facility->id;
            $ticketextend->partner_id       = $ticket->partner_id;
            $ticketextend->total            = $data['total'];
            $ticketextend->grand_total      = $data['grand_total'];
            $ticketextend->checkin_time     = $extendTicket->checkout_time;
            $ticketextend->checkout_time    = $data['estimatedTime'];
            $ticketextend->base_length      = $data['length'];
            $ticketextend->tax_fee          = $data['tax'];
            $ticketextend->discount_amount          = $data['discount_amount'];

            $ticketextend->save();
        } else {
            $this->reservationlog->info("else condition if Extend first time");

            NotificationLog::where('reservation_id', $reservation->id)->where('slug_name', 'check-out-reminder')->delete();
            $this->reservationlog->info("Notification log deleted for new entry");

            $extendLength = $data['length'] - $ticketData->length;
            $this->reservationlog->info("Extend Length: " . $extendLength);

            $ticketextend = new TicketExtend();
            $ticketextend->ticket_id        = $ticket->id;
            $ticketextend->length           = $extendLength;
            $ticketextend->ticket_number    = $ticket->ticket_number;
            $ticketextend->facility_id      = $ticket->facility->id;
            $ticketextend->partner_id       = $ticket->partner_id;
            $ticketextend->total            = $data['total'];
            $ticketextend->grand_total      = $data['grand_total'];
            $ticketextend->checkin_time     = $ticket->checkout_time;
            $ticketextend->checkout_time    = $data['estimatedTime'];
            $ticketextend->base_length      = $data['length'];
            $ticketextend->tax_fee          = $data['tax'];
            $ticketextend->discount_amount          = $data['discount_amount'];
            $ticketextend->save();
        }
        // update ticket is extended
        $ticketUpdate = Ticket::where('ticket_number', $ticket->ticket_number)->orderBy('id', 'desc')->first();
        $ticketUpdate->is_extended = 1;
        $ticketUpdate->save();
    }

    // Mobile nonce decrypt code
    public function setDecryptedCardMobile(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }

        $nonce = json_decode($request->nonce);
        $encryptedData = base64_decode($nonce->ct);
        $iv = base64_decode($nonce->iv);

        // Key (32 bytes for AES-256)
        $key = config('parkengage.MOBILE_AES_ENCRYPTION'); // Adjust as needed // PARKKENGAE CONFIG

        $decryptedData = $this->decryptAes256NoPadding($encryptedData, $key, $iv);

        $cardData = explode(':', $decryptedData);
        $zipCode = (isset($cardData[4]) && $cardData[4] != 'undefined') ? $cardData[4] : '';
        $request->request->add(
            [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode
            ]
        );
        $this->reservationlog->info("Nonce Card: " . json_encode($cardData));
        $this->request = $request;
    }

    public function decryptAes256NoPadding($data, $key, $iv)
    {
        $cipher = 'aes-256-cbc';
        $options = OPENSSL_RAW_DATA;

        // Decrypt the data
        $decryptedData = openssl_decrypt($data, $cipher, $key, $options, $iv);
        return $decryptedData;
    }

    public function importReservation(Request $request)
    {
        $this->importReservationLog->info("Import Started");
        // Get the uploaded file
        $file = $request->file('file');

        // Read the file's contents into an array
        $csvData = file($file->getRealPath());
        $csvData = array_map('str_getcsv', $csvData); // Convert each line to an array

        // Extract the header (first row)
        $header = array_shift($csvData);

        // Check if we got a valid header row
        if (!$header || count($header) < 3) {
            return response()->json(['error' => 'Invalid CSV file format'], 400);
        }

        // Loop through each row and insert into the database
        foreach ($csvData as $i => $row) {
            // Map row to headers
            $row = array_combine($header, $row);

            // From Request
            $arrival_time = Carbon::parse($row['reservaiton_arrival'])->format('Y-m-d H:i:s');
            $end_time = Carbon::parse($row['reservaiton_arrival'])->addMinutes(QueryBuilder::getLengthInMints($row['length_of_stay']))->format('Y-m-d H:i:s');

            // UTC Timezone
            $utc_arrival_time = Carbon::parse($row['reservaiton_arrival'])->setTimezone('UTC')->format('Y-m-d\TH:i:s\Z');
            $utc_end_time = Carbon::parse($row['reservaiton_arrival'])->addMinutes(QueryBuilder::getLengthInMints($row['length_of_stay']))->setTimezone('UTC')->format('Y-m-d\TH:i:s\Z');


            // Facility Object:
            $this->facility = Facility::where('id', 398)->first();


            $reservation = new Reservation($row);
            $row['arrival'] = $row['reservaiton_arrival'];

            $request->replace($row);
            $this->request = $request;

            // 2. Fetch Logged In User
            $this->countryCode = QueryBuilder::appendCountryCode();
            $countryCode = $this->countryCode;
            $user = User::where('created_by', $this->facility->owner_id)->where('status', '1');
            if (isset($row['anon']) && $row['anon'] == true) {
                $user = $user->where('anon', $row['anon']);
            }
            $user = $user->where(function ($query) use ($row, $countryCode) {
                $query->where('email', $row['email'])->orWhere('phone', $countryCode . $row['phone']);
            })->first();

            $name = ' ';
            if (isset($row['name']) && !empty($row['name'])) {
                $name = $row['name'];
            }
            $partner_id = $this->facility->owner_id;
            $request->request->add(['partner_id' => $partner_id]);

            if (!isset($user) || empty($user)) {
                $user = User::create([
                    'name' => $name,
                    'email' => $row['email'],
                    'phone' => $this->countryCode . $row['phone'],
                    'password' => Hash::make(str_random(60)),
                    'anon' => true,
                    'user_type' => '5',
                    'created_by' => $this->facility->owner_id,
                    'company_name' => $row['companyName'] ?? NULL,
                    'address' => $row['address'] ?? NULL,
                    'address2' => $row['address2'] ?? NULL,
                    'city' => $row['city'] ?? NULL,
                    'country' => $row['country'] ?? NULL,
                    'state' => $row['state'] ?? NULL,
                    'pincode' => $row['pincode'] ?? NULL,
                ]);
                $this->importReservationLog->info("Guest User Created with :" . json_encode([$user->id, $user->email, $user->phone, $user->anon]));
            } else {
                if (isset($row['name']) && !empty($row['name']) && $user->name != $row['name']) {
                    $user->name = $name;
                }
                if (($user->email == "") || ($user->email == NULL)) {
                    $user->email = $request->email;
                }

                if (($user->phone == "") || ($user->phone == NULL)) {
                    $user->phone =  $this->countryCode . $request->phone;
                }

                if (isset($row['companyName']) && !empty($row['companyName']) && $user->company_name != $row['companyName']) {
                    $user->company_name = $row['companyName'];
                }
                if (isset($row['address']) && !empty($row['address']) && $user->address != $row['address']) {
                    $user->address = $row['address'];
                }
                if (isset($row['address2']) && !empty($row['address2']) && $user->address2 != $row['address2']) {
                    $user->address2 = $row['address2'];
                }
                if (isset($row['city']) && !empty($row['city']) && $user->city != $row['city']) {
                    $user->city = $row['city'];
                }
                if (isset($row['country']) && !empty($row['country']) && $user->country != $row['country']) {
                    $user->country = $row['country'];
                }
                if (isset($row['state']) && !empty($row['state']) && $user->state != $row['state']) {
                    $user->state = $row['state'];
                }
                if (isset($row['pincode']) && !empty($row['pincode']) && $user->pincode != $row['pincode']) {
                    $user->pincode = $row['pincode'];
                }

                $user->save();

                $this->importReservationLog->info("User Updated with :" . json_encode([$user->id, $user->email, $user->phone, $user->anon]));

                // check reservation exist for same time slot or between same time slot
                $time = Carbon::parse($row['reservaiton_arrival'])->addHours($row['length_of_stay']);

                if (intval($row['length_of_stay']) != $row['length_of_stay']) {
                    $timarr = explode('.', $row['length_of_stay']);
                    // $minute = ('.' . $timarr[1]) * 60;
                    $time->addMinutes($timarr['1']);
                }

                $endDateTime = $time->subSecond()->format('Y-m-d H:i:s');

                if (isset($row['license_plate'])) {
                    $checkReservationExist = Reservation::where(['user_id' => $user->id, 'facility_id' => $this->facility->id, 'license_plate' => $row['license_plate']])->whereDate('start_timestamp', '=', date('Y-m-d', strtotime($row['reservaiton_arrival'])))->whereNull('cancelled_at')->where('is_ticket', '0')->get();

                    if (count($checkReservationExist) > 0) {
                        foreach ($checkReservationExist as $key => $val) {
                            $this->importReservationLog->info("Reservaion Already Exists having Id :" . $val->id);
                            $endtime = Carbon::parse($val->start_timestamp)->addHours($val->length);

                            if (intval($val->length) != $val->length) {
                                $timarr = explode('.', $val->length);
                                // $minute = ('.' . $timarr[1]) * 60;
                                $endtime->addMinutes($timarr[1]);
                            }
                            $endtimedb = $endtime->subSecond()->format('Y-m-d H:i:s');

                            $val->start_timestamp = Carbon::parse($val->start_timestamp);
                            $start_timestamp = $val->start_timestamp->format('Y-m-d H:i:s');

                            $arrival = Carbon::parse($row['reservaiton_arrival']);
                            $arrival = $arrival->format('Y-m-d H:i:s');
                            if ($arrival >= $start_timestamp && $arrival <= $endtimedb) {
                                $this->importReservationLog->info("Reservaion Already Exists 1 " . $val->ticketech_code . " Sr No.: " . $row['sr_no']);
                                continue;
                            } else if ($endDateTime >= $start_timestamp && $endDateTime <= $endtimedb) {
                                $this->importReservationLog->info("Reservaion Already Exists 2 " . $val->ticketech_code . " Sr No.: " . $row['sr_no']);
                                continue;
                            } else if ($arrival <= $start_timestamp && $endDateTime >= $endtimedb) {
                                $this->importReservationLog->info("Reservaion Already Exists 3 " . $val->ticketech_code . " Sr No.: " . $row['sr_no']);
                                continue;
                            }
                        }
                    }
                } else {
                    $this->importReservationLog->info("License Plate is Required and Sr No.: " . $row['sr_no']);
                    continue;
                }
            }

            $this->user = $user;

            $this->countryCode = QueryBuilder::appendCountryCode();
            $countryCode = $this->countryCode;
            $partner_id = $this->facility->owner_id;
            $facility_id = $this->facility->id;
            $name = $this->request->name ?? ' ';
            $request->request->add(['partner_id' => $partner_id]);
            $request->request->add(['facility_id' => $facility_id]);

            // Fetch or Create User
            // $user = User::fetchOrCreateUser($this->request, $partner_id, $countryCode, $name);
            $this->reservationlog->info("User Details:" . json_encode($user));
            $this->user = $user;

            // Check reservation existence for the same time slot
            if (isset($this->request->license_plate) && !empty($this->request->license_plate)) {
                $check = Reservation::checkExistingReservation($this->request, $user->id, $this->importReservationLog, true);
                if ($check) {
                    $this->importReservationLog->info("Sheet Sr. No.: " . $row['sr_no']);
                    continue;
                }
            } else {
                $this->importReservationLog->info("License Plate is Required and Sr No.: " . $row['sr_no']);
                continue;
            }

            $this->importReservationLog->info("User id: " . $this->user->id . " User Email: " . $this->user->email);

            // Facily Id:
            $reservation->facility_id = '398';
            $reservation->partner_id = '357982';
            $reservation->user_id = $this->user->id;

            // Zub Zeag data
            $barcode = $reservation->generateBarCode();
            $unique_id = substr($barcode, -8);
            $reservation->thirdparty_code = $barcode;
            $reservation->hub_unique_id = $unique_id;
            $reservation->is_hub_zeag = 1;
            $reservation->is_ticket = 0;
            $reservation->total = $row['amount_paid'];
            $reservation->device_type = 'Hub-Zeag';

            $this->importReservationLog->info("Barcode: " . $barcode . " Key: " . $i);

            // Normal Time
            $reservation->start_timestamp = $arrival_time;
            $reservation->end_timestamp = $end_time;
            $lengthInMints = QueryBuilder::getLengthInMints($row['length_of_stay']);
            $graceMinutes = QueryBuilder::getLengthInMints($this->facility->reservation_grace_period_minute);
            $estimated_checkout_time = Carbon::parse($arrival_time)->addMinutes(($lengthInMints + $graceMinutes))->format('Y-m-d H:i:s');
            $reservation->estimated_checkout_time = $estimated_checkout_time;

            // UTC format Time
            $reservation->utc_start_timestamp = $utc_arrival_time;
            $reservation->utc_end_timestamp = $utc_end_time;

            // General Data
            $reservation->license_plate = $row['license_plate'];
            $reservation->length = $row['length_of_stay'];

            // dd($reservation, $this->user);
            $saved = $reservation->save();
            $lastInsertId = $reservation->id;

            if ($saved) {
                $authorized_anet_transaction = new AuthorizeNetTransaction();

                $authorized_anet_transaction->sent = $this->sendAnet;
                $authorized_anet_transaction->anonymous = $this->anonymousAnet;
                $authorized_anet_transaction->user_id = $this->user->id;
                $authorized_anet_transaction->ip_address = \Request::ip();
                $authorized_anet_transaction->total = 0;
                $authorized_anet_transaction->name = $this->user->name;

                $authorized_anet_transaction->description = "Hub Zeag Import Reseration {$lastInsertId}";

                $authorized_anet_transaction->response_message = "Zero amount transaction";
                $authorized_anet_transaction->save();

                $reservation->anet_transaction_id = $authorized_anet_transaction->id;
                $reservation->save();
            }

            // Vehicle details
            $vehicle = PermitVehicle::handleVehicleDetails($this->request, $this->user, $this->reservationlog);
            $this->reservationlog->info("Final Vehicle Id : " . $vehicle->id . " Make id: " . $vehicle->make_id . " Model id: " . $vehicle->model_id);
            $reservation->vehicle_id = $vehicle->id;
            $reservation->license_plate = $this->request->license_plate;
            $reservation->save();
            // Insert into the database

            // Send Email:
            $secret = OauthClient::where('partner_id', $partner_id)->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $options = ['reservationId' => $lastInsertId];
            $options['--client_secret'] = $secret->secret;

            $this->dispatchJobForParkingHub($reservation->id, "POST");
            $this->reservationlog->info("Reservation Created On HubZeag Ticket:" . $reservation->ticketech_code);

            Artisan::queue('reservation:email', $options);

            $this->importReservationLog->info("Booking Id: " . $reservation->ticketech_code . " Key: " . $i);
        }

        return response()->json(['message' => 'Reservations imported successfully!'], 200);
    }
}
