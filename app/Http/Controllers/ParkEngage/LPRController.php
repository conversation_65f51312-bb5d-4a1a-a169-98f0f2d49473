<?php

/* 
  *  Copyright (c) 2024 ParkEngage Inc. All rights reserved.
  *  
  *  This software is the confidential and proprietary information of
  *  ParkEngage Inc. ("Confidential Information"). You shall not
  *  disclose such Confidential Information and shall use it only in
  *  accordance with the terms of the license agreement you entered into
  *  with ParkEngage Inc.
  *  
  *  This source code is a valuable trade secret of ParkEngage Inc.
  *  
  *  THIS SOFTWARE IS PROVIDED BY ParkEngage Inc. ``AS IS'' AND ANY
  *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  *  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  *  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL [YOUR COMPANY NAME] OR
  *  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  *  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  *  PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  *  OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *  
  *  The views and conclusions contained in the software and documentation
  *  are those of the authors and should not be interpreted as representing
  *  *official policies, either expressed or implied, of ParkEngage Inc.
*/

namespace App\Http\Controllers\ParkEngage;

use App\Classes\CommonFunctions;
use App\Exceptions\ApiGenericException;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Http\Controllers\Controller;
use App\Models\ParkEngage\LPRCitation;
use App\Models\ParkEngage\LPRFeed;
use App\Models\ParkEngage\LPRSession;
use App\Models\Ticket;
use Exception;
use Illuminate\Support\Facades\Auth;
use GuzzleHttp\Client;
use Carbon\Carbon;
use App\Models\Facility;
use App\Models\ParkEngage\Gate;
// VP : PIMS - 14662
// use App\Models\LprCamera;

use App\Classes\HeartlandPaymentGateway;
use App\Http\Helpers\QueryBuilder;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\LprTicketMapping;
use App\Services\LPR\LprTransientService;
use App\Services\Transient\TicketHelper;

class LPRController extends Controller
{
    protected $vanGarurd;
    protected $lprService;
    protected $ticketHelper;

    private const EVENT_HANDLERS = [
        'vehicle.entered'   => [LPRFeed::class, 'fromApiPayload'],
        'vehicle.exited'    => [LPRFeed::class, 'fromApiPayload'],
        'session.closed'    => [LPRSession::class, 'fromApiPayload'],
        'ticket.created'    => [LPRCitation::class, 'fromApiPayload'],
        'ticket.canceled'   => [LPRCitation::class, 'fromApiPayload'],
        'ticket.paid'       => [LPRCitation::class, 'fromApiPayload'],
    ];

    public function __construct(LoggerFactory $logFactory)
    {
        $this->vanGarurd = $logFactory->setPath('logs/vanGarurd')->createLogger('vanGarurd');
        // Pass identifier (e.g., vangaurd) to service
        $this->lprService = new LprTransientService('tattile'); // VP:PIMS-14662
        $this->ticketHelper = new TicketHelper();
    }

    public function getRportUrl()
    {
        $user = Auth::user();
        $vanguard_username = $user->vanguard_username;
        $vanguard_password = $user->vanguard_password;

        if (!empty($vanguard_username) && !empty($vanguard_password)) {
            $client = new Client();

            try {
                $response = $client->post('https://api.vparking.co/auth/login', [
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ],
                    'json' => [
                        'email' => $vanguard_username,
                        'password' => $vanguard_password,
                    ],
                ]);

                $body = $response->getBody();
                $data = json_decode($body, true);

                // Check if access_token is present in response
                if (isset($data['access_token'])) {
                    $reportUrl = "https://app.payrevpass.com/dashboard?token=" . $data['access_token'];

                    return $reportUrl;
                }
                throw new ApiGenericException("No report found for this user, Please contact site administrator!");
            } catch (\Exception $e) {
                throw new ApiGenericException($e->getMessage());
            }
        }
        throw new ApiGenericException("No report found for this user, Please contact site administrator!");
    }

    public function handleVanGaurdianTicket($payload)
    {
        try {
            $this->vanGarurd->info("Handling VanGuardian ticket", ['payload' => $payload]);

            $licensePlate = $payload['vehicle']['plate']['lpn'] ?? null;
            if (!$licensePlate) {
                $this->vanGarurd->warning("License plate not found in payload", ['payload' => $payload]);
                return null;
            }

            $partnerId = config('parkengage.PARTNER_RevPass');
            $facilityId = config('parkengage.REVPASS_FACILITY');

            // Check for existing active ticket
            $existingTicket = Ticket::where([
                'license_plate' => $licensePlate,
                'facility_id'   => $facilityId,
                'partner_id'    => $partnerId,
                'is_checkout'   => 0
            ])->first();

            if ($existingTicket) {
                $this->vanGarurd->info("Duplicate ticket found", ['ticket_number' => $existingTicket->ticket_number]);
                return $existingTicket;
            }

            // Create new ticket
            $ticket = new Ticket();
            $ticket->license_plate = $licensePlate;
            $ticket->ticket_number = CommonFunctions::checkTicketNumber();
            $ticket->facility_id = $facilityId;
            $ticket->partner_id = $partnerId;
            $ticket->save();

            $this->vanGarurd->info("New ticket created", ['ticket_number' => $ticket->ticket_number]);

            return $ticket;
        } catch (\Exception $e) {
            $this->vanGarurd->error("Error handling VanGuardian ticket", ['error' => $e->getMessage()]);
            return null;
        }
    }

    public function handleLPRFeed(Request $request)
    {
        $this->vanGarurd->info("handleLPRFeed request: " . json_encode($request->all()));
        $eventType = $request->event_type;
        $this->vanGarurd->info("Event Type: " . json_encode($eventType));

        if (isset(self::EVENT_HANDLERS[$eventType])) {
            [$modelClass, $method] = self::EVENT_HANDLERS[$eventType];
            $this->vanGarurd->info("Model: $modelClass and function: $method");

            try {
                // This call save the LPR Feed only. 
                $response = call_user_func([$modelClass, $method], $request->all());

                // Start VP:PIMS-14662
                $facility_id    = $response->facility_id;
                $gateType       = strtolower($response->camera_direction);
                //  get Gate information 
                $gate = Gate::where(['facility_id' => $facility_id, 'gate_type' => $gateType])->first();


                if ($gateType == 'entry') {
                    $this->vanGarurd->info("Checkin Done");
                } elseif ($gateType == 'exit') {
                    $facility = Facility::find($facility_id);
                    // Set TimeZone
                    $this->lprService->setCustomTimezone($facility_id);

                    // Here are the business logic for transient
                    $this->vanGarurd->info("Response . " . json_encode($response));
                    $request->merge([
                        'camera_type'   => $response->camera_type,
                        'license_plate' => $response->license_plate,
                        'facility_id'   => $facility_id,
                        'partner_id'    => $gate->facility->owner_id,
                    ]);


                    // this in store in UTC in database and now convert here based on garage timezone.  // This is LPN exit time. 
                    $nycLprReadTime = Carbon::parse($response->event_timestamp, 'UTC')->setTimezone($gate->facility->timezone)->format('Y-m-d H:i:s');
                    $request->request->add(['nycLprReadTime'    => $nycLprReadTime]);

                    $this->vanGarurd->info("Before to check entry");
                    // get first entry for The license late.
                    $trueStart = $this->getLicenseTimes($request);
                    $this->vanGarurd->info("After to check entry " . json_encode($trueStart));

                    if (isset($trueStart['lpr_id']) && $trueStart['lpr_id'] != null) {
                        $this->vanGarurd->info("Before Duration calculation");
                        $stayOfLength  = $this->ticketHelper->getLenghtOfStay($trueStart['event_timestamp'], $nycLprReadTime);
                        $this->vanGarurd->info("After Duration calculation stayOfLength : {$stayOfLength}");

                        $request->merge([
                            'lpr_id'                => $trueStart['lpr_id'],
                            'lprStartTime'          => $trueStart['event_timestamp'],
                            'stayOfLength'          => $stayOfLength
                        ]);

                        // Check FastTrack Enabled or not 
                        // dd($request->all(), $facility);
                        $fastTrack = $this->ticketHelper->isFastTrackExists($request, $facility);
                        if ($fastTrack) {
                            $this->vanGarurd->info("Get Fast track data " . json_encode($fastTrack));
                            $request->merge(
                                [
                                    'user_id' => $fastTrack->id,
                                    'checkin_time' => $this->ticketHelper->getUTCToFacilityTimezone($request->event_timestamp, $facility),
                                    'estimated_checkout' => $this->ticketHelper->getUTCToFacilityTimezone(date('Y-m-d H:i:s'), $facility)
                                ]
                            );
                            $this->vanGarurd->info("Going to get rate  ");
                            // Calculate price again 
                            $amountDue = $this->ticketHelper->getAmountDue($request, $facility);
                            $rate['price']         = $amountDue['price'];

                            // Now calculate Tax and Fees 
                            $parkingAmount      = $rate['price'];
                            $processingFee      = $facility->getProcessingFee('0');             // pass 1 to get the processing fee // pass 1 to get the processing fee
                            $surchargeFee       = $facility->getSurchargeFee($rate);
                            $newRate['price']   = ($rate['price'] + $surchargeFee);
                            $tax_rate           = $facility->getTaxRate($newRate, '0');         // fetch tax from getTicketRate dinamicaly
                            $additionalFee      = $facility->getAdditionalFee($rate);           // Addition Fee Introduce
                            $payableAmount      = ($parkingAmount + $processingFee + $surchargeFee + $tax_rate + $additionalFee);

                            $this->vanGarurd->info("Payment Amount --" . $payableAmount);
                            $request->merge(
                                [
                                    'token' => $fastTrack->token,
                                    'Amount' => '3'
                                ]
                            );
                            // if Fast Tract Enabled than going to make payment 
                            $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByTokenIM30($request, $facility);
                            $this->vanGarurd->info("Payment Response --" . json_encode($paymentResponse));
                            if (($paymentResponse) && (in_array($paymentResponse->responseMessage, ['Success', 'APPROVAL']))) {

                                // Add this in Request
                                $request->merge(
                                    [
                                        'total'                 => $payableAmount,
                                        'grand_total'           => $payableAmount,
                                        'parking_amount'        => $parkingAmount,
                                        'tax_fee'               => $tax_rate,
                                        'processing_fee'        => $processingFee,
                                        'additional_fee'        => $additionalFee,
                                        'surcharge_fee'         => $surchargeFee,
                                        'rate_id'               => isset($amountDue['rate_id']) ? $amountDue['rate_id'] : 0,
                                        'rate_description'      => isset($amountDue['rate_description']) ? $amountDue['rate_description'] : 'base_price'
                                    ]
                                );

                                // Save Ticket
                                $ticket = $this->ticketHelper->createTicket($request, $facility);

                                $request->merge(
                                    [
                                        'card_last_four' => $fastTrack->card_last_four,
                                        'expiration' => $fastTrack->expiry,
                                        'expiration_date' => $fastTrack->expiry
                                    ]
                                );
                                $authorizedAnet = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $ticket->user_id);
                                $this->vanGarurd->info("Payment Transaction Data  --" . json_encode($authorizedAnet));
                                // $paymentTransaction = AuthorizeNetTransaction::where('id', $$authorizedAnet->id)->first();
                                // $paymentTransaction->card_type = $fastTrack->card_type;
                                // $paymentTransaction->save();

                                //************* update ANt tble Refrence key */ 
                                QueryBuilder::setReferenceKey($$authorizedAnet->id, $ticket->ticket_number);
                                //*********************Set All Response Message */
                                // QueryBuilder::setAllTransactions(json_encode($paymentResponse), "Heartland", $details->ticket_number);
                                //end ********************************End Response Message
                                //************* End update ANt tble Refrence key */ 


                                $ticket->anet_transaction_id = $authorizedAnet->id;
                                $ticket->payment_date = date('Y-m-d H:i:s');
                                $ticket->is_closed = 1;
                                $ticket->is_checkout = 1;
                                $ticket->closed_date = date('Y-m-d H:i:s');
                                $ticket->save();


                                $response_type = '1';
                                $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                                $msg['is_phone_linked'] = '0';
                                $msg['phone_linked_msg'] = '';
                                if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                    $msg['is_phone_linked'] = '1';
                                    $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                                }
                                $msg['booking_type'] = 'driveup';
                                $msg['is_check_in_ontime'] = '1';
                                $msg['is_our_ticket'] = '1';
                                // $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                                $queue_name = '';
                                $gate_type = '';
                                if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
                                    $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
                                    $gate_type  = $gate->gate_type;
                                }
                                $this->lprService->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            } else {
                                $this->vanGarurd->info("Payment Fail --" . json_encode($paymentResponse));
                            }
                        }
                    }

                    $this->lprService->saveCheckout($request, $gate);   // VP:PIMS-14662
                    $this->vanGarurd->info("Checkout Done");
                } else {
                    $this->vanGarurd->info("Invalid Gate");
                }
                // Close : VP:PIMS-14662

                return  response()->json(['status' => 200, 'data' => $response]);
            } catch (\Exception $e) {
                $this->vanGarurd->error('Error ' . $e->getMessage() . ' File ' . $e->getFile() . ' Line no :- ' . $e->getLine());
                return  response()->json(['status' => 500, 'msg' => 'Something went wrong processing the event.', 'data' => null]);
            }
        } else {
            $this->vanGarurd->error("Unknown event type");
            return response()->json(['status' => 400, 'message' => 'Unknown event type']);
        }
    }


    public function getLicenseTimes(Request $request)
    {
        $plate = $request->license_plate;
        $facilityId = $request->facility_id;
        $partnerId = $request->partner_id;
        $qrScanTime = date('Y-m-d H:i:s');
        // $entryAfterThatTime = Carbon::parse('now')->subHours(config('parkengage.revpass.n_time_before_in_hours'))->format('Y-m-d H:i:s');
        $entryAfterThatTime = Carbon::parse('now')->subHours(config('parkengage.revpass.n_time_before_in_hours'))->startOfDay()->format('Y-m-d H:i:s');
        // $startOfYesterdayNY = Carbon::parse('now')->subDays(config('parkengage.revpass.n_time_before_in_hours'))->startOfDay()->format('Y-m-d H:i:s');
        // dd($qrScanTime, $entryAfterThatTime, $startOfYesterdayNY);

        if (!$plate || !$facilityId || !$partnerId || !$qrScanTime) {
            throw new ApiGenericException('Missing required parameters');
        }

        $qrTime = Carbon::parse($qrScanTime);
        $facility = Facility::find($request->facility_id);

        // Fetch the last checkout time from tickets table
        $lastCheckout = Ticket::where('license_plate', $plate)
            ->where('facility_id', $facilityId)
            ->where('partner_id', $partnerId)
            ->whereNotNull('estimated_checkout')
            ->orderBy('estimated_checkout', 'desc')
            ->first();


        // Fetch the first LPR entry event after the last checkout time and before QR scan
        $query = LPRFeed::where('license_plate', $plate)
            ->where('facility_id', $facilityId)
            ->where('partner_id', $partnerId)
            ->whereIn('event_type', ['vehicle.entered', 'entry']); //    //PIMS-14859

        $lastCheckoutTime = $lastCheckout ? $lastCheckout->estimated_checkout : null;

        if (isset($lastCheckout->is_checkout) && $lastCheckout->is_checkout == '0') {     // if ticket is open
            $lprEntryTicket = LprTicketMapping::where(['ticket_id' => $lastCheckout->id, 'status' => '0'])->first();
            if ($lprEntryTicket) {
                $query->where('id', $lprEntryTicket->lpr_feed_id);
            } else {
                $query->where('event_timestamp', '<=', $qrTime);
            }

            // else no checkin found for LPR
        } else if (isset($lastCheckout->is_checkout) && $lastCheckout->is_checkout == '1') {  // if ticket is Closed

            /*  if (isset($lastCheckoutTime) && $lastCheckoutTime != null) {
                $nycCurrentTime = Carbon::parse($lastCheckoutTime, $facility->timezone)->setTimezone('UTC')->format('Y-m-d H:i:s');;
                $query->where('event_timestamp', '>', $nycCurrentTime);
            } */
            $lprEntry = $query->orderBy('event_timestamp', 'desc')->first();
        } else {
            // No Ticket Found Case no need to handle any think.
            $nycCurrentTime = Carbon::parse($entryAfterThatTime, $facility->timezone)->setTimezone('UTC')->format('Y-m-d H:i:s');;
            $query->where('event_timestamp', '>=', $nycCurrentTime);
        }

        $lprEntry = $query->orderBy('event_timestamp', 'asc')->first();

        $trueStart = $lprEntry ? $lprEntry->event_timestamp : null;
        // dd($lastCheckoutTime, Carbon::parse($qrTime)->format('Y-m-d H:i:s'), $lprEntry, $trueStart);

        // if (!empty($trueStart) || !empty($lastCheckoutTime)) {
        if (!empty($trueStart)) {
            $nycLprReadTime = Carbon::parse($trueStart, 'UTC')->setTimezone($facility->timezone)->format('Y-m-d H:i:s');;
            return [
                'lpr_id'                => $lprEntry->id,
                'event_timestamp'       => $nycLprReadTime,
                'utc_event_timestamp'   => $trueStart,
                'last_checkout_time'    => $lastCheckoutTime
            ];
        }

        return [
            'lpr_id' => null,
            'event_timestamp' => null,
            'utc_event_timestamp' => null,
            'last_checkout_time' => null
        ];
    }
}
