<?php

/* 
  *  Copyright (c) 2024 ParkEngage Inc. All rights reserved.
  *  
  *  This software is the confidential and proprietary information of
  *  ParkEngage Inc. ("Confidential Information"). You shall not
  *  disclose such Confidential Information and shall use it only in
  *  accordance with the terms of the license agreement you entered into
  *  with ParkEngage Inc.
  *  
  *  This source code is a valuable trade secret of ParkEngage Inc.
  *  
  *  THIS SOFTWARE IS PROVIDED BY ParkEngage Inc. ``AS IS'' AND ANY
  *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  *  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  *  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL [YOUR COMPANY NAME] OR
  *  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  *  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  *  PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  *  OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *  
  *  The views and conclusions contained in the software and documentation
  *  are those of the authors and should not be interpreted as representing
  *  *official policies, either expressed or implied, of ParkEngage Inc.
*/

namespace App\Http\Controllers\ParkEngage;

use App\Classes\CommonFunctions;
use App\Exceptions\ApiGenericException;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Http\Controllers\Controller;
use App\Models\ParkEngage\LPRCitation;
use App\Models\ParkEngage\LPRFeed;
use App\Models\ParkEngage\LPRSession;
use App\Models\Ticket;
use Exception;
use Illuminate\Support\Facades\Auth;
// use GuzzleHttp\Client;
use Carbon\Carbon;
use App\Models\Facility;
use App\Models\ParkEngage\Gate;
// VP : PIMS - 14662
// use App\Models\LprCamera;

use App\Classes\HeartlandPaymentGateway;
use App\Http\Helpers\QueryBuilder;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\LprTicketMapping;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\User;
use App\Services\LPR\LprTransientService;
use App\Services\Transient\TicketHelper;
use Twilio\Exceptions\RestException;
use Twilio\Rest\Client;

class LPRController extends Controller
{
    protected $vanGarurd;
    protected $lprService;
    protected $ticketHelper;
    const WINSCONSIN_AVE_CAMPUS = 5;

    private const EVENT_HANDLERS = [
        'vehicle.entered'   => [LPRFeed::class, 'fromApiPayload'],
        'vehicle.exited'    => [LPRFeed::class, 'fromApiPayload'],
        'session.closed'    => [LPRSession::class, 'fromApiPayload'],
        'ticket.created'    => [LPRCitation::class, 'fromApiPayload'],
        'ticket.canceled'   => [LPRCitation::class, 'fromApiPayload'],
        'ticket.paid'       => [LPRCitation::class, 'fromApiPayload'],
    ];

    public function __construct(LoggerFactory $logFactory)
    {
        $this->vanGarurd = $logFactory->setPath('logs/vanGarurd')->createLogger('vanGarurd');
        // Pass identifier (e.g., vangaurd) to service
        $this->lprService = new LprTransientService('tattile'); // VP:PIMS-14662
        $this->ticketHelper = new TicketHelper();
    }

    public function customeReplySms($msg, $phone, $imageURL = '')
    {
        try {
            if ($phone == '') {
                return "success";
            }
            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
            $client = new Client($accountSid, $authToken);
            try {
                if (QueryBuilder::checkValidMobileLength($phone)) {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                        // the number you'd like to send the message to
                        $phone,
                        array(
                            // A Twilio phone number you purchased at twilio.com/console
                            'from' => env('TWILIO_PHONE'),
                            // the body of the text message you'd like to send
                            //'body' => "Fine"
                            'body' => "$msg",
                            //'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)
                        )
                    );
                    $this->vanGarurd->info("Message : {$msg} sent to $phone");
                }
                return "success";
            } catch (RestException $e) {
                //echo "Error: " . $e->getMessage();
                $this->vanGarurd->error($e->getMessage());
                return "success";
            }
        } catch (RestException $e) {
            //echo "Error: " . $e->getMessage();
            $this->vanGarurd->error($e->getMessage());
            return "success";
        }
    }

    public function getRportUrl()
    {
        $user = Auth::user();
        $vanguard_username = $user->vanguard_username;
        $vanguard_password = $user->vanguard_password;

        if (!empty($vanguard_username) && !empty($vanguard_password)) {
            $client = new Client();

            try {
                $response = $client->post('https://api.vparking.co/auth/login', [
                    'headers' => [
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ],
                    'json' => [
                        'email' => $vanguard_username,
                        'password' => $vanguard_password,
                    ],
                ]);

                $body = $response->getBody();
                $data = json_decode($body, true);

                // Check if access_token is present in response
                if (isset($data['access_token'])) {
                    $reportUrl = "https://app.payrevpass.com/dashboard?token=" . $data['access_token'];

                    return $reportUrl;
                }
                throw new ApiGenericException("No report found for this user, Please contact site administrator!");
            } catch (\Exception $e) {
                throw new ApiGenericException($e->getMessage());
            }
        }
        throw new ApiGenericException("No report found for this user, Please contact site administrator!");
    }

    public function handleVanGaurdianTicket($payload)
    {
        try {
            $this->vanGarurd->info("Handling VanGuardian ticket", ['payload' => $payload]);

            $licensePlate = $payload['vehicle']['plate']['lpn'] ?? null;
            if (!$licensePlate) {
                $this->vanGarurd->warning("License plate not found in payload", ['payload' => $payload]);
                return null;
            }

            $partnerId = config('parkengage.PARTNER_RevPass');
            $facilityId = config('parkengage.REVPASS_FACILITY');

            // Check for existing active ticket
            $existingTicket = Ticket::where([
                'license_plate' => $licensePlate,
                'facility_id'   => $facilityId,
                'partner_id'    => $partnerId,
                'is_checkout'   => 0
            ])->first();

            if ($existingTicket) {
                $this->vanGarurd->info("Duplicate ticket found", ['ticket_number' => $existingTicket->ticket_number]);
                return $existingTicket;
            }

            // Create new ticket
            $ticket = new Ticket();
            $ticket->license_plate = $licensePlate;
            $ticket->ticket_number = CommonFunctions::checkTicketNumber();
            $ticket->facility_id = $facilityId;
            $ticket->partner_id = $partnerId;
            $ticket->save();

            $this->vanGarurd->info("New ticket created", ['ticket_number' => $ticket->ticket_number]);

            return $ticket;
        } catch (\Exception $e) {
            $this->vanGarurd->error("Error handling VanGuardian ticket", ['error' => $e->getMessage()]);
            return null;
        }
    }

    public function handleLPRFeed(Request $request)
    {
        $this->vanGarurd->info("handleLPRFeed request: " . json_encode($request->all()));
        $eventType = $request->event_type;
        $this->vanGarurd->info("Event Type: " . json_encode($eventType));

        if (isset(self::EVENT_HANDLERS[$eventType])) {
            [$modelClass, $method] = self::EVENT_HANDLERS[$eventType];
            $this->vanGarurd->info("Model: $modelClass and function: $method");

            try {
                // This call save the LPR Feed only. 
                $this->vanGarurd->info("Event Type: 1 ");
                $response = call_user_func([$modelClass, $method], $request->all());
                $this->vanGarurd->info("Event Type: 1.2 ");

                if (in_array($eventType, ['vehicle.entered', 'vehicle.exited'])) {
                // Start VP:PIMS-14662
                $facility_id    = $response->facility_id;
                $gateType       = strtolower($response->camera_direction);
                $facility       = Facility::find($facility_id);

                //  get Gate information 
                    //$gate = Gate::where(['facility_id' => $facility_id, 'gate_type' => $gateType])->first();

                    $request->merge([
                        'camera_type'   => $response->camera_type,
                        'license_plate' => $response->license_plate,
                        'facility_id'   => $facility->id,
                        'partner_id'    => $facility->owner_id,
                    ]);
                    $facilityName  = $facility->full_name;
                    $this->vanGarurd->info("Event Type: 11 ");

                if ($gateType == 'entry') {
                        $this->vanGarurd->info("Checkin Start");
                        $fastTrack = $this->ticketHelper->isFastTrackExists($request, $facility);
                        $this->vanGarurd->info("get fasttrack " . json_encode($fastTrack));
                        if ($fastTrack) {
                            $user = User::find($fastTrack->user_id);
                            $this->vanGarurd->info("get user " . json_encode($user->id));
                            if ($user) {
                                $msg = "Thank you for Check-In with Fast Track at facility {$facility->full_name} ";
                                $this->customeReplySms($msg, $user->phone);
                                $this->vanGarurd->info("SMS SEND SUCCESSFULLY ");
                            }
                        }
                    $this->vanGarurd->info("Checkin Done");
                } elseif ($gateType == 'exit') {

                        if ($facility->is_gated_facility == '2') {
                    $facility = Facility::find($facility_id);
                            //  get Gate information 
                            $gate = Gate::where(['facility_id' => $facility_id, 'gate_type' => $gateType])->first();
                    // Set TimeZone
                    $this->lprService->setCustomTimezone($facility_id);

                    // Here are the business logic for transient
                    $this->vanGarurd->info("Response . " . json_encode($response));
                    $request->merge([
                        'camera_type'   => $response->camera_type,
                        'license_plate' => $response->license_plate,
                        'facility_id'   => $facility_id,
                        'partner_id'    => $gate->facility->owner_id,
                    ]);


                    // this in store in UTC in database and now convert here based on garage timezone.  // This is LPN exit time. 
                    $nycLprReadTime = Carbon::parse($response->event_timestamp, 'UTC')->setTimezone($gate->facility->timezone)->format('Y-m-d H:i:s');
                    $request->request->add(['nycLprReadTime'    => $nycLprReadTime]);

                    $this->vanGarurd->info("Before to check entry");
                    // get first entry for The license late.
                    $trueStart = $this->getLicenseTimes($request);
                    $this->vanGarurd->info("After to check entry " . json_encode($trueStart));

                    if (isset($trueStart['lpr_id']) && $trueStart['lpr_id'] != null) {
                        $this->vanGarurd->info("Before Duration calculation");
                        $stayOfLength  = $this->ticketHelper->getLenghtOfStay($trueStart['event_timestamp'], $nycLprReadTime);
                        $this->vanGarurd->info("After Duration calculation stayOfLength : {$stayOfLength}");

                        $request->merge([
                            'lpr_id'                => $trueStart['lpr_id'],
                            'lprStartTime'          => $trueStart['event_timestamp'],
                            'stayOfLength'          => $stayOfLength
                        ]);

                        // Check FastTrack Enabled or not 
                        $fastTrack = $this->ticketHelper->isFastTrackExists($request, $facility);
                                $this->vanGarurd->info("Check Request " . json_encode($request->all()));

                        if ($fastTrack) {
                            $this->vanGarurd->info("Get Fast track data " . json_encode($fastTrack));
                            $request->merge(
                                [
                                    'user_id' => $fastTrack->id,
                                    'checkin_time' => $this->ticketHelper->getUTCToFacilityTimezone($request->event_timestamp, $facility),
                                    'estimated_checkout' => $this->ticketHelper->getUTCToFacilityTimezone(date('Y-m-d H:i:s'), $facility)
                                ]
                            );
                            $this->vanGarurd->info("Going to get rate  ");
                            // Calculate price again 
                            $amountDue = $this->ticketHelper->getAmountDue($request, $facility);
                            $rate['price']         = $amountDue['price'];

                            // Now calculate Tax and Fees 
                            $parkingAmount      = $rate['price'];
                            $processingFee      = $facility->getProcessingFee('0');             // pass 1 to get the processing fee // pass 1 to get the processing fee
                            $surchargeFee       = $facility->getSurchargeFee($rate);
                            $newRate['price']   = ($rate['price'] + $surchargeFee);
                            $tax_rate           = $facility->getTaxRate($newRate, '0');         // fetch tax from getTicketRate dinamicaly
                            $additionalFee      = $facility->getAdditionalFee($rate);           // Addition Fee Introduce
                            $payableAmount      = ($parkingAmount + $processingFee + $surchargeFee + $tax_rate + $additionalFee);

                            $this->vanGarurd->info("Payment Amount --" . $payableAmount);
                            $request->merge(
                                [
                                    'token' => $fastTrack->token,
                                            'Amount' => $payableAmount > 0 ? $payableAmount : 1
                                ]
                            );
                            // if Fast Tract Enabled than going to make payment 
                            $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByTokenIM30($request, $facility);
                            $this->vanGarurd->info("Payment Response --" . json_encode($paymentResponse));
                            if (($paymentResponse) && (in_array($paymentResponse->responseMessage, ['Success', 'APPROVAL']))) {

                                // Add this in Request
                                $request->merge(
                                    [
                                        'total'                 => $payableAmount,
                                        'grand_total'           => $payableAmount,
                                        'parking_amount'        => $parkingAmount,
                                        'tax_fee'               => $tax_rate,
                                        'processing_fee'        => $processingFee,
                                        'additional_fee'        => $additionalFee,
                                        'surcharge_fee'         => $surchargeFee,
                                        'rate_id'               => isset($amountDue['rate_id']) ? $amountDue['rate_id'] : 0,
                                        'rate_description'      => isset($amountDue['rate_description']) ? $amountDue['rate_description'] : 'base_price'
                                    ]
                                );

                                // Save Ticket
                                $ticket = $this->ticketHelper->createTicket($request, $facility);

                                $request->merge(
                                    [
                                        'card_last_four' => $fastTrack->card_last_four,
                                        'expiration' => $fastTrack->expiry,
                                        'expiration_date' => $fastTrack->expiry
                                    ]
                                );
                                $authorizedAnet = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $ticket->user_id);
                                $this->vanGarurd->info("Payment Transaction Data  --" . json_encode($authorizedAnet));
                                // $paymentTransaction = AuthorizeNetTransaction::where('id', $$authorizedAnet->id)->first();
                                // $paymentTransaction->card_type = $fastTrack->card_type;
                                // $paymentTransaction->save();

                                //************* update ANt tble Refrence key */ 
                                QueryBuilder::setReferenceKey($$authorizedAnet->id, $ticket->ticket_number);
                                //*********************Set All Response Message */
                                // QueryBuilder::setAllTransactions(json_encode($paymentResponse), "Heartland", $details->ticket_number);
                                //end ********************************End Response Message
                                //************* End update ANt tble Refrence key */ 


                                $ticket->anet_transaction_id = $authorizedAnet->id;
                                $ticket->payment_date = date('Y-m-d H:i:s');
                                $ticket->is_closed = 1;
                                $ticket->is_checkout = 1;
                                $ticket->closed_date = date('Y-m-d H:i:s');
                                $ticket->save();


                                $response_type = '1';
                                $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                                $msg['is_phone_linked'] = '0';
                                $msg['phone_linked_msg'] = '';
                                if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                    $msg['is_phone_linked'] = '1';
                                    $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                                }
                                $msg['booking_type'] = 'driveup';
                                $msg['is_check_in_ontime'] = '1';
                                $msg['is_our_ticket'] = '1';
                                // $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                                $queue_name = '';
                                $gate_type = '';
                                if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
                                    $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
                                    $gate_type  = $gate->gate_type;
                                }
                                $this->lprService->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            } else {
                                $this->vanGarurd->info("Payment Fail --" . json_encode($paymentResponse));
                            }
                        }
                    }

                    $this->lprService->saveCheckout($request, $gate);   // VP:PIMS-14662
                        } else {
                            $this->vanGarurd->info("Event Type: 22 ");
                            // Check FastTrack Enabled or not 
                            // Check FastTrack Enabled or not 
                            $fastTrack = $this->ticketHelper->isFastTrackExists($request, $facility);
                            $this->vanGarurd->info("Event Type: 33 ");
                            if ($fastTrack) {
                                $trueStart = $this->getLicenseTimes($request);
                                // dd($trueStart);
                                $this->vanGarurd->info("After to check entry TRUE START VALUE . " . json_encode($trueStart));

                                if (isset($trueStart['lpr_id']) && $trueStart['lpr_id'] != null) {
                                    $this->vanGarurd->info("Before Duration calculation");
                                    $nycLprReadTime = Carbon::parse($response->event_created_at, 'UTC')->setTimezone($facility->timezone)->format('Y-m-d H:i:s');
                                    // this in store in UTC in database and now convert here based on garage timezone.
                                    $request->request->add(['nycLprReadTime'    => $nycLprReadTime]);

                                    $diffInRealhours = Carbon::parse($nycLprReadTime)->diffInRealhours(Carbon::parse($trueStart['event_timestamp']));
                                    $diffInRealMints = Carbon::parse($nycLprReadTime)->diffInRealMinutes(Carbon::parse($trueStart['event_timestamp']));
                                    $stayOfLength = $diffInRealhours;
                                    if ($diffInRealhours > 0) {
                                        $remainingMinutes = $diffInRealMints % 60;
                                        $stayOfLength = $diffInRealhours . '.' . $remainingMinutes;
                                    } else {
                                        $stayOfLength =  '0.' . $diffInRealMints;
                                    }
                                    $this->vanGarurd->info("After Duration calculation HR  : {$stayOfLength} MINTS : {$diffInRealMints}");
                                    $request->merge([
                                        'lpr_id'                => $trueStart['lpr_id'],
                                        'lprStartTime'          => $trueStart['event_timestamp'],
                                        'diffInHours'           => $diffInRealhours,
                                        'diffInRealMints'       => $diffInRealMints,
                                        'stayOfLength'          => $stayOfLength
                                    ]);

                                    if ($fastTrack) {
                                        $this->vanGarurd->info("Get Fast track data " . json_encode($fastTrack));
                                        $request->merge(
                                            [
                                                'user_id' => $fastTrack->user_id,
                                                'checkin_time' => $trueStart['event_timestamp'],
                                                'estimated_checkout' => $request->nycLprReadTime
                                            ]
                                        );
                                        $this->vanGarurd->info("Going to get rate Request : " . json_encode($request->all()));
                                        // Calculate price again 
                                        $amountDue = $this->ticketHelper->getAmountDue($request, $facility);
                                        $rate['price']         = $amountDue['price'];

                                        // Now calculate Tax and Fees 
                                        $parkingAmount      = $rate['price'];
                                        $processingFee      = $facility->getProcessingFee('0');             // pass 1 to get the processing fee // pass 1 to get the processing fee
                                        $surchargeFee       = $facility->getSurchargeFee($rate);
                                        $newRate['price']   = ($rate['price'] + $surchargeFee);
                                        $tax_rate           = $facility->getTaxRate($newRate, '0');         // fetch tax from getTicketRate dinamicaly
                                        $additionalFee      = $facility->getAdditionalFee($rate);           // Addition Fee Introduce
                                        $payableAmount      = ($parkingAmount + $processingFee + $surchargeFee + $tax_rate + $additionalFee);

                                        $this->vanGarurd->info("Payment Amount --" . $payableAmount . " price ");
                                        $this->vanGarurd->info("Total Request  " . json_encode($request->all()));
                                        $request->merge(
                                            [
                                                'token' => $fastTrack->token,
                                                'Amount' => $payableAmount > 0 ? $payableAmount : 1
                                            ]
                                        );
                                        // return true;
                                        // if Fast Tract Enabled than going to make payment 
                                        $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByTokenIM30($request, $facility);
                                        $this->vanGarurd->info("Payment  Response --" . json_encode($paymentResponse));
                                        if (($paymentResponse) && (in_array($paymentResponse->responseMessage, ['Success', 'APPROVAL']))) {

                                            // Add this in Request
                                            $request->merge(
                                                [
                                                    'total'                 => $payableAmount,
                                                    'grand_total'           => $payableAmount,
                                                    'parking_amount'        => $parkingAmount,
                                                    'tax_fee'               => $tax_rate,
                                                    'processing_fee'        => $processingFee,
                                                    'additional_fee'        => $additionalFee,
                                                    'surcharge_fee'         => $surchargeFee,
                                                    'rate_id'               => isset($amountDue['rate_id']) ? $amountDue['rate_id'] : 0,
                                                    'rate_description'      => isset($amountDue['rate_description']) ? $amountDue['rate_description'] : 'base_price'
                                                ]
                                            );

                                            // Save Ticket
                                            $ticket = $this->ticketHelper->createTicket($request, $facility);
                                            $this->vanGarurd->info("Checkout point ticket done   --");


                                            $request->merge(
                                                [
                                                    'card_last_four' => $fastTrack->card_last_four,
                                                    'expiration' => $fastTrack->expiry,
                                                    'expiration_date' => $fastTrack->expiry
                                                ]
                                            );
                                            $this->vanGarurd->info("Save Transactions");
                                            $authorizedAnet = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $ticket->user_id);
                                            $this->vanGarurd->info("Payment Transaction Data  --" . json_encode($authorizedAnet));
                                            // $paymentTransaction = AuthorizeNetTransaction::where('id', $$authorizedAnet->id)->first();
                                            // $paymentTransaction->card_type = $fastTrack->card_type;
                                            // $paymentTransaction->save();

                                            //************* update ANt tble Refrence key */ 
                                            // QueryBuilder::setReferenceKey($$authorizedAnet->id, $ticket->ticket_number);
                                            //*********************Set All Response Message */
                                            // QueryBuilder::setAllTransactions(json_encode($paymentResponse), "Heartland", $details->ticket_number);
                                            //end ********************************End Response Message
                                            //************* End update ANt tble Refrence key */ 


                                            $ticket->anet_transaction_id = $authorizedAnet->id;
                                            $ticket->payment_date = date('Y-m-d H:i:s');
                                            $ticket->is_closed = 1;
                                            $ticket->is_checkout = 1;
                                            $ticket->closed_date = date('Y-m-d H:i:s');
                                            $ticket->save();
                                            $this->vanGarurd->info("Start sending SMS ");
                                            $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->partner_id)->first();
                                            $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                            $url = env('RECEIPT_URL');
                                            $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $ticket->partner_id);
                                            if ($dynamicReceiptUrl) {
                                                $url = $dynamicReceiptUrl->value;
                                            }
                                            $this->vanGarurd->info("PAYment Done at exit and start sending sms");
                                            $msg = "Thank you for visiting " . $facilityName . ". Your ticket number is " . $ticket->ticket_number . ". Please use the following link to download E-Ticket, extend your stay & Validate $url/$name/ticket/" . $ticket->ticket_number;
                                            $this->customeReplySms($msg, $ticket->user->phone);
                                            $this->vanGarurd->info("DONE sending SMS ");
                                            $this->vanGarurd->info("Checkout done Thanks Msg   --" . $msg);

                                            // $response_type = '1';
                                            // $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                                            // $msg['is_phone_linked'] = '0';
                                            // $msg['phone_linked_msg'] = '';
                                            // if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                            //     $msg['is_phone_linked'] = '1';
                                            //     $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                                            // }
                                            // $msg['booking_type'] = 'driveup';
                                            // $msg['is_check_in_ontime'] = '1';
                                            // $msg['is_our_ticket'] = '1';
                                            // // $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                                            // $queue_name = '';
                                            // $gate_type = '';
                                            // if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
                                            //     $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
                                            //     $gate_type  = $gate->gate_type;
                                            // }
                                            // $this->lprService->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                        } else {
                                            $this->vanGarurd->info("Payment Fail --" . json_encode($paymentResponse));
                                        }
                                    }
                                }
                            } else {
                                $this->vanGarurd->info("No Fast Track Found");
                            }
                        }

                    $this->vanGarurd->info("Checkout Done");
                } else {
                    $this->vanGarurd->info("Invalid Gate");
                }
                // Close : VP:PIMS-14662
                }


                return  response()->json(['status' => 200, 'data' => $response]);
            } catch (\Exception $e) {
                $this->vanGarurd->error('Error Vangard : ' . $e->getMessage() . ' File ' . $e->getFile() . ' Line no :- ' . $e->getLine());
                return  response()->json(['status' => 500, 'msg' => 'Something went wrong processing the event.', 'data' => null]);
            }
        } else {
            $this->vanGarurd->error("Unknown event type");
            return response()->json(['status' => 400, 'message' => 'Unknown event type']);
        }
    }


    public function getLicenseTimes(Request $request)
    {
        $plate = $request->license_plate;
        $facilityId = $request->facility_id;
        $partnerId = $request->partner_id;
        $qrScanTime = date('Y-m-d H:i:s');
        // $entryAfterThatTime = Carbon::parse('now')->subHours(config('parkengage.revpass.n_time_before_in_hours'))->format('Y-m-d H:i:s');
        $entryAfterThatTime = Carbon::parse('now')->subHours(config('parkengage.revpass.n_time_before_in_hours'))->startOfDay()->format('Y-m-d H:i:s');
        // $startOfYesterdayNY = Carbon::parse('now')->subDays(config('parkengage.revpass.n_time_before_in_hours'))->startOfDay()->format('Y-m-d H:i:s');
        // dd($qrScanTime, $entryAfterThatTime, $startOfYesterdayNY);

        if (!$plate || !$facilityId || !$partnerId || !$qrScanTime) {
            throw new ApiGenericException('Missing required parameters');
        }

        $facility = Facility::find($facilityId);
        // Neighborhood logic – adjust facility_id before queries
        $neighborhoodData = Facility::select('neighborhood_id')->find($facilityId);
        if ($neighborhoodData && $neighborhoodData->neighborhood_id == self::WINSCONSIN_AVE_CAMPUS) {
            $latestLpr = LPRFeed::where('license_plate', $plate)
                ->where('partner_id', $partnerId)
                ->orderBy('id', 'desc')
                ->first();

            if ($latestLpr) {
                $facilityId = $latestLpr->facility_id;
                $facility   = Facility::find($facilityId); //refresh facility object
            }
        }

        $qrTime = Carbon::parse($qrScanTime);
        $facility = Facility::find($request->facility_id);

        // Fetch the last checkout time from tickets table
        $lastCheckout = Ticket::where('license_plate', $plate)
            ->where('facility_id', $facilityId)
            ->where('partner_id', $partnerId)
            ->whereNotNull('estimated_checkout')
            ->orderBy('estimated_checkout', 'desc')
            ->first();


        // Fetch the first LPR entry event after the last checkout time and before QR scan
        $query = LPRFeed::where('license_plate', $plate)
            ->where('facility_id', $facilityId)
            ->where('partner_id', $partnerId)
            ->whereIn('event_type', ['vehicle.entered', 'entry']); //    //PIMS-14859

        $lastCheckoutTime = $lastCheckout ? $lastCheckout->estimated_checkout : null;

        if (isset($lastCheckout->is_checkout) && $lastCheckout->is_checkout == '0') {     // if ticket is open
            $lprEntryTicket = LprTicketMapping::where(['ticket_id' => $lastCheckout->id, 'status' => '0'])->first();
            if ($lprEntryTicket) {
                $query->where('id', $lprEntryTicket->lpr_feed_id);
            } else {
                $query->where('event_timestamp', '<=', $qrTime);
            }
            $lprEntry = $query->orderBy('event_timestamp', 'asc')
                ->first();
            // else no checkin found for LPR
        } else if (isset($lastCheckout->is_checkout) && $lastCheckout->is_checkout == '1') {  // if ticket is Closed
            $this->vanGarurd->info("Checkout ticket NYC TIME {$lastCheckoutTime} : ");
            // if (isset($lastCheckoutTime) && $lastCheckoutTime != null) {
            //     $nycCurrentTime = Carbon::parse('now', $facility->timezone)->setTimezone('UTC')->format('Y-m-d H:i:s');;
            //     $this->vanGarurd->info("Checkout ticket NYC TIME {$lastCheckoutTime} : UTC TIME : {$nycCurrentTime} ");
            //     $query->where('event_timestamp', '>', $nycCurrentTime);
            // }
            $lprEntry = $query->orderBy('event_timestamp', 'desc')->first();
        } else {
            // No Ticket Found Case no need to handle any think.
            $nycCurrentTime = Carbon::parse($entryAfterThatTime, $facility->timezone)->setTimezone('UTC')->format('Y-m-d H:i:s');
            $query->where('event_timestamp', '>=', $nycCurrentTime);
            $lprEntry = $query->orderBy('event_timestamp', 'asc')
                ->first();
        }




        $trueStart = $lprEntry ? $lprEntry->event_timestamp : null;
        // dd($lastCheckoutTime, Carbon::parse($qrTime)->format('Y-m-d H:i:s'), $lprEntry, $trueStart);

        // if (!empty($trueStart) || !empty($lastCheckoutTime)) {
        if (!empty($trueStart)) {
            $nycLprReadTime = Carbon::parse($trueStart, 'UTC')->setTimezone($facility->timezone)->format('Y-m-d H:i:s');;
            return [
                'lpr_id'                => $lprEntry->id,
                'event_timestamp'       => $nycLprReadTime,
                'utc_event_timestamp'   => $trueStart,
                'last_checkout_time'    => $lastCheckoutTime
            ];
        }

        return [
            'lpr_id' => null,
            'event_timestamp' => null,
            'utc_event_timestamp' => null,
            'last_checkout_time' => null
        ];
    }
}
