<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Crypt;
use Hash;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redirect;
use App\Models\ParkEngage\Gate;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Configuration;
use App\Models\PermitTicket;
use App\Models\PermitRequest;
use App\Classes\AbacusWebService;
use Twilio\Exceptions\RestException;
use App\Models\UserPass;
use App\Models\AuthorizeNetTransaction;
use App\Models\UserEventsLog;
use App\Models\OauthClient;
use App\Models\ParkEngage\TicketCitation;
use App\Services\Pdf;
use App\Classes\InlineViewCss;
use App\Services\Image;
use App\Exceptions\ApiGenericException;
use App\Classes\ParkengageGateApi;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\TransactionData;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\ParkEngage\UserSession;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\LicensePlate;
use App\Models\AffiliateBusiness;
use App\Models\Rate;
use Auth;
use App\Http\Helpers\QueryBuilder;

class TestTownsendApiController extends Controller
{

    protected $log;
    protected $logPayment;
    protected $user;
    protected $partnerPaymentDetails;
    protected $authNet;
    protected $facility;
    protected $sendAnet = false;
    protected $anonymousAnet = false;
    protected $paymentProfileId;
    protected $cim;
    protected $request;
    protected $rate;

    const RESERVATION_THRESHOLD_TYPE = 2;
    const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE = 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO = 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR = 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "Sold Out At This Time.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    const  GRACE_PERIOD = 1;
    const  FACILITY_ID = 33;
    const  PARTNET_ID = 2980;

    public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->cim = $cim;
        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        $this->log = $logFactory->setPath('logs/parkengage/townsend-api')->createLogger('townsend-api');

        //$this->logPayment = $logFactory->setPath('logs/parkengage/townsend-web-payment-logs')->createLogger('townsend-web-payment-logs');
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($partnerTimezone->timezone != '') {
                if ($facility->timezone != '') {
                    date_default_timezone_set($facility->timezone);
                } else if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }

    //First Screen only details of ticket
    public function getCheckinTicketDetails(Request $request, $ticket_number, $user_id = '')
    {
        $this->log->info("getCheckinTicketDetails " . json_encode($request->all()));
        $this->log->info("Ticket Number : " . base64_decode($ticket_number));
        $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        if (!$secret) {
            throw new ApiGenericException('Invalid partner.');
        }

        $checkinData = Ticket::with(['user', 'facility.photos'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();

        if (count($checkinData) == 0) {
            $this->log->error("error on checkin screen");
            //return Redirect::to('townsend-error-checkin');
            throw new ApiGenericException("No recent ticket is found for this user.");
        }
        // if (($user_id != '') && ($checkinData->user_id != $user_id)) {
        //     throw new ApiGenericException("No recent ticket is found for this user..");
        // }
        if ($this->checkFacilityAvailable($checkinData->facility_id) == false) {
            //Redirect::to('error-facility');
            throw new ApiGenericException("Facility not available.");
        }

        $this->setCustomTimezone($checkinData->facility_id);

        if ($checkinData->anet_transaction_id != '') {
            $this->log->info(" anet_transaction_id is not null ");
            //return redirect('townsend-thankyou-payment/'.base64_encode($checkinData->ticket_number));
            // return $this->getCheckinDetailsPaymentThankyou($request, base64_encode($checkinData->ticket_number));
        }
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkin_time);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

        $diff_in_hours = $checkinData->getCheckOutCurrentTime(true);
        $this->log->info("get Diff in Hours : {$diff_in_hours}");
        $checkinData['length'] = $diff_in_hours;

        $isMember = 0;
        $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
        if ($rate == false) {
            //return Redirect::to('townsend-error-facility');
            throw new ApiGenericException("Rate not found.");
        }
        $this->log->info("HOURS {$diff_in_hours} AND RATE {$rate['price']} ");
        //return current availabilit
        if ($rate['price'] == 'N/A') {
            $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            //$rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
        } else {
            //$rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
        }

        $this->log->info("Agian HOURS {$diff_in_hours} AND RATE {$rate['price']} ");
        //returning  message as per availibility 
        $checkinData['rate'] = $rate;
        $this->log->info("user is on checkin screen on sms click with ticket number {$checkinData->ticket_number}");
        // return view('townsend.payment-screen', ['data'=>$checkinData]);
        $updatedPaidAmount = "0";

        $checkinData['DATACAP_SCRIPT_URL'] = config('parkengage.DATACAP_SCRIPT_URL');
        $checkinData['DATACAP_TOKEN'] = $checkinData->facility->ecommerce_token;
        $checkinData['DATACAP_OTU_URL'] = config('parkengage.DATACAP_OTU_URL');

        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();
        if ($overstay) {
            $checkinData['overstay_amount'] = $overstay->total;
        } else {
            $checkinData['overstay_amount'] = "0.00";
        }


        if ($rate['price'] == "0.00" || $rate['price'] == 0) {
            $rate['price'] = 0.00;
            $this->log->info("getCheckinTicketDetails Validated paid_type IF section  {$checkinData->paid_type}");
            $checkinData['payable_amount']  = '0';
            $checkinData['processing_fee']  = '0';
            $checkinData['tax_rate']        = '0';
            $checkinData['parking_amount']  = '0';
            $checkinData['overstay_amount'] = '0';
            $checkinData['paid_amount']     = '0';
            $checkinData['discount_amount'] = '0';
            $checkinData['amount_paid']     = '0';
            $checkinData['payment'] = 0;    // need to call payment api 
            $this->log->info("getCheckinTicketDetails Return ");
            $this->log->info(json_encode($checkinData));
            return $checkinData;
        } else {
            $this->log->info("getCheckinTicketDetails Validated paid_type ELSE section  {$checkinData->paid_type}");

            $priceBreakUp = $checkinData->priceBreakUp($rate);
            $this->log->info("return priceBreakUp " . json_encode($priceBreakUp));
            $checkinData['payable_amount']  = $priceBreakUp['payable_amount'];
            $checkinData['processing_fee']  = $priceBreakUp['processing_fee'];
            $checkinData['tax_rate']        = $priceBreakUp['tax_rate'];
            $checkinData['parking_amount']  = $priceBreakUp['parking_amount'];
            $checkinData['overstay_amount'] = $priceBreakUp['overstay_amount'];
            $checkinData['paid_amount']     = $priceBreakUp['paid_amount'];
            $checkinData['discount_amount'] = $priceBreakUp['discount_amount'];
            if ($checkinData->is_overstay == '1') {
                $checkinData['amount_paid']     = isset($priceBreakUp['amount_paid']) ? $priceBreakUp['amount_paid'] : $checkinData->grand_total;
            } else {
                $checkinData['amount_paid']     = $checkinData->grand_total > 0 ? $checkinData->grand_total : 0;
            }

            if ($checkinData->paid_type == '0') {
                $checkinData['payment'] = 1;  // paymnt done no need to make call payment api
            } else {
                $checkinData['payment'] = 0;    // need to call payment api 
            }
            $this->log->info("getCheckinTicketDetails Return ");
            $this->log->info(json_encode($checkinData));
            return $checkinData;
        }
    }

    // 2nd where payable amount and other ticket details display
    public function AfterCheckinPaymentDetails(Request $request, $ticket_number, $user_id = '')
    {
        $this->log->info("AfterCheckinPaymentDetails " . json_encode($request->all()));
        $this->log->info("AfterCheckinPaymentDetails TICKET NUMBER {$ticket_number}");

        $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        if (!$secret) {
            throw new ApiGenericException('Invalid partner.');
        }
        $checkinData = Ticket::with('user')->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();

        if (count($checkinData) == 0) {
            throw new ApiGenericException("No recent ticket is found for this user.");
        }
        if (($user_id != '') && ($checkinData->user_id != $user_id)) {
            throw new ApiGenericException("No recent ticket is found for this user.");
        }
        if ($checkinData->anet_transaction_id != '') {
            $checkinData['payment'] = 1;
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));


        $diff_in_hours = $checkinData->getCheckOutCurrentTime(true);
        $this->log->info("get Diff in Hours : {$diff_in_hours}");

        $checkinData['length'] = $diff_in_hours;
        $facility = Facility::find($checkinData->facility_id);

        $rate = [];
        $isMember = 0;
        $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
        if ($rate == false) {
            throw new ApiGenericException("Rate not found.");
        }
        $taxRate = $checkinData->getTaxRate($rate);          // to get tax price
        $processingFee = $checkinData->getProcessingFee($checkinData); // to get prcessing free channel wise need to
        $ticketPrice = $rate['price'] + $processingFee + $taxRate;
        $this->log->info("PRINT HOURS {$diff_in_hours} AND RATE {$rate['price']} AND TICKET PRICE {$ticketPrice}");
        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();
        if ($overstay) {
            $checkinData['overstay_amount'] = $overstay->total;
        } else {
            $checkinData['overstay_amount'] = '0';
        }

        $checkinData['DATACAP_SCRIPT_URL'] = config('parkengage.DATACAP_SCRIPT_URL');
        $checkinData['DATACAP_TOKEN'] = $checkinData->facility->ecommerce_token;
        $checkinData['DATACAP_OTU_URL'] = config('parkengage.DATACAP_OTU_URL');



        if ($rate['price'] == 'N/A') {
            $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            if ($rate['price'] == "0.00" || $rate['price'] == 0) {
                $rate['price'] = 0.00;
            } else {
                $priceBreakUp = $checkinData->priceBreakUp($rate);
                if ($checkinData->paid_by != '') {
                    // Full  Amount Validate
                    if ($checkinData->paid_type == '0' || $checkinData->paid_type == 0) {
                        $checkinData['payment'] = 1;
                        if ($rate['price'] > "0") {
                            $updatedPaidAmount = $rate['price'];
                        }
                        $rate['price'] = "0.00";
                        $rate['facility'] = $checkinData->facility;
                        $checkinData['rate'] = $rate;
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);

                        if ($updatedPaidAmount <= "0") {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                            $checkinData['overstay_amount'] = "0.00";
                            $checkinData['paid_amount'] = $updatedPaidAmount;
                        } else {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = number_format($updatedPaidAmount, 2);
                            $checkinData['paid_amount'] = $updatedPaidAmount + $checkinData->facility->processing_fee;
                        }
                        return $checkinData;
                    }

                    // Hour validate
                    if ($checkinData->paid_type == '1' || $checkinData->paid_type == 1) {
                        if ($checkinData->paid_hour != '') {
                            $checkinData['payment'] = 0;
                            $duration_in_hou = $diff_in_hours - $checkinData->paid_hour;
                            $duration = $this->getEndTime($duration_in_hou);
                            $duration_in_min1 = $this->getEndTime($diff_in_hours);
                            $total_amount =  $rate['price'];
                            $paid_amount = "0.00";
                            if ($duration == 0) {
                                $checkinData['payment'] = 1;
                                $rate['price'] = "0";
                                $updatedPaidAmount = "0";
                                $checkinData->paid_amount = $rate['price'];
                            } else {
                                $paid_amount = ((($total_amount) / $duration_in_min1) * $duration);
                                $paid_amount = round($paid_amount);
                                $checkinData->paid_amount = $paid_amount;
                            }

                            if ($rate['price'] <= $paid_amount) {
                                $checkinData['payment'] = 1;
                                $rate['price'] = "0";
                                $rate['facility'] = $checkinData->facility;
                                $checkinData['rate'] = $rate;
                                $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                $checkinData['payable_amount'] = "0.00";
                                $checkinData['processing_fee'] = "0.00";
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = "0.00";
                            } else {
                                $updatedPaidAmount = $rate['price'];
                                $payable_amount = $updatedPaidAmount - $paid_amount;
                                if ($checkinData->grand_total > "0") {
                                    $payable_amount = $payable_amount - $checkinData->grand_total;
                                }
                                if ($payable_amount > "0") {
                                    $payable_amount = $payable_amount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                } else {
                                    $payable_amount = "0.00";
                                    $processing_fee = "0.00";
                                    $checkinData['payment'] = 1;
                                }

                                $checkinData['rate'] = $rate;
                                $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                $checkinData['processing_fee'] = $processing_fee;
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = $updatedPaidAmount;
                            }
                            return $checkinData;
                        }
                    }

                    //paid amount
                    if ($checkinData->paid_type == '2' || $checkinData->paid_type == 2) {

                        $checkinData['payment'] = 0;
                        $rate['price'] = $rate['price'];
                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                            $checkinData['payment'] = 1;
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                        } else {
                            if ($checkinData->paid_amount != '') {
                                $updatedPaidAmount = $rate['price'];
                                $payable_amount = $updatedPaidAmount - $checkinData->paid_amount;
                                if ($checkinData->grand_total > "0") {
                                    $payable_amount = $payable_amount - $checkinData->grand_total;
                                }
                                if ($payable_amount >= "0") {
                                    $payable_amount = $payable_amount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                } else if ($payable_amount <= $checkinData->paid_amount) {
                                    $checkinData->paid_amount = $updatedPaidAmount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                    $payable_amount = "0.00";
                                    $checkinData['payment'] = 1;
                                } else {
                                    $payable_amount = "0.00";
                                    $processing_fee = "0.00";
                                    $checkinData['payment'] = 1;
                                }
                                $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                $checkinData['processing_fee'] = $processing_fee;
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = $updatedPaidAmount;
                            }
                            $checkinData['rate'] = $rate;
                        }
                        return $checkinData;
                    }

                    // paid percentage
                    if ($checkinData->paid_type == '3' || $checkinData->paid_type == 3) {
                        $checkinData['payment'] = 0;

                        $total_amount =  $rate['price'];
                        if ($total_amount > "0.00") {
                            $percentageAmount = (($total_amount * $checkinData->paid_percentage) / 100);
                        } else {
                            $total_amount = "0.00";
                            $percentageAmount = "0.00";
                        }

                        $rate['price'] = number_format($rate['price'], 2);

                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {

                            $checkinData['payment'] = 1;
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                        } else {
                            if ($percentageAmount > '0.00') {
                                $updatedPaidAmount = $rate['price'];
                                $payable_amount = $updatedPaidAmount - $percentageAmount;
                                if ($checkinData->grand_total > "0") {
                                    $payable_amount = $payable_amount - $checkinData->grand_total;
                                }
                                if ($payable_amount > "0") {
                                    $payable_amount = $payable_amount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                } else {
                                    $payable_amount = "0.00";
                                    $processing_fee = "0.00";
                                    $checkinData['payment'] = 1;
                                }
                                $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                $checkinData['processing_fee'] = $processing_fee;
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = $updatedPaidAmount;
                            }
                            $checkinData['rate'] = $rate;
                        }
                        return $checkinData;
                    }
                }
                $rate['price'] =  $rate['price'];
            }
        } else {
            if ($rate['price'] == "0.00" || $rate['price'] == 0) {
                $rate['price'] = 0.00;
            } else {
                $priceBreakUp = $checkinData->priceBreakUp($rate);
                $this->log->info("priceBreakUp " . json_encode($priceBreakUp));
                $checkinData['payable_amount']  = $priceBreakUp['payable_amount'];
                $checkinData['processing_fee']  = $priceBreakUp['processing_fee'];
                $checkinData['tax_rate']        = $priceBreakUp['tax_rate'];
                $checkinData['parking_amount']  = $priceBreakUp['parking_amount'];
                $checkinData['overstay_amount'] = $priceBreakUp['overstay_amount'];
                $checkinData['paid_amount']     = $priceBreakUp['paid_amount'];
                $checkinData['discount_amount'] = $priceBreakUp['discount_amount'];
                $checkinData['amount_paid']     = $checkinData->grand_total;
                if ($checkinData->paid_type == '0') {
                    $checkinData['payment'] = 1;
                } else {
                    if ($checkinData['payable_amount'] > 0) {
                        $checkinData['payment'] = 0;
                    } else {
                        $checkinData['payment'] = 1;
                    }
                }
                $this->log->info("AfterCheckinPaymentDetails Return ");
                $this->log->info(json_encode($checkinData));
                return $checkinData;
            }
        }
        //  Below code not in use Vijay 05-08-2023
        $checkinData['rate'] = $rate;
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
        $this->log->info("user is on payment section click on checkout button on checkin screen with ticket number {$checkinData->ticket_number}");

        // $tax_rate = $checkinData->facility->tax_rate;
        // if ($tax_rate > 0) {
        //     $tax_rate = number_format((($rate['price'] * $checkinData->facility->tax_rate) / 100), 2);
        // }

        if ($rate['price'] <= "0") {
            $checkinData['payable_amount'] = "0.00";
            $checkinData['processing_fee'] = "0.00";
            $checkinData['tax_rate'] = "0.00";
            $checkinData['parking_amount'] = "0.00";
            $checkinData['payment'] = 1;
        } else {
            $checkinData['payable_amount'] = number_format($rate['price'] + $processingFee + $taxRate - $checkinData->grand_total - $checkinData->paid_amount, 2);
            $checkinData['processing_fee'] = $processingFee;
            $checkinData['tax_rate'] = $taxRate;
            $checkinData['parking_amount'] = $rate['price'];
            if ($rate['price'] <= "0") {
                $checkinData['payable_amount'] = "0.00";
                $checkinData['processing_fee'] = "0.00";
                $checkinData['tax_rate'] = "0.00";
                $checkinData['payment'] = 1;
            }
        }
        if ($checkinData->is_offline_payment != 0 || $checkinData->is_offline_payment != '0') {
            $checkinData['payable_amount'] = "0.00";
            $checkinData['processing_fee'] = "0.00";
            $checkinData['tax_rate'] = "0.00";
            $checkinData['parking_amount'] = "0.00";
            $checkinData['payment'] = 1;
        }
        $this->log->info("AfterCheckinPaymentDetails Before Response " . json_encode($checkinData));
        return $checkinData;
    }

    // 3rd to make payment
    public function MakeTicketPayment(Request $request)
    {
        $this->log->info("Received request for payment : " . json_encode($request->all()));
        $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        if (!$secret) {
            throw new ApiGenericException('Invalid partner.');
        }
        $this->request = $request;
        $this->secret = $secret;
        $this->checkFacilityAvailable($this->request->facility_id);
        //  $this->setDecryptedCard($request);

        //$this->validate($this->request, $this->billingValidation);

        $this->setCustomTimezone($this->request->facility_id);

        $checkinData = Ticket::with(['facility', 'user'])->where('ticket_number', $this->request->ticket_number)->where('is_checkin', '1')->where('is_checkout', '0')->first();

        if (!$checkinData) {
            throw new ApiGenericException("Invalid ticket detials.");
        }

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkin_time);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

        $diff_in_hours = $checkinData->getCheckOutCurrentTime(true);

        $facility = Facility::find($checkinData->facility_id);
        $this->facility = $facility;
        $this->user = $checkinData->user;

        $rate = [];
        $isMember = 0;
        $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
        if ($rate == false) {
            throw new ApiGenericException("Rate not found.");
        }
        $this->rate =  $rate;

        if ($rate['price'] == 'N/A') {
            $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            if ($rate['price'] == "0.00" || $rate['price'] == 0) {
                $rate['price'] = 0.00;
            } else {

                if ($checkinData->paid_by != '') {
                    // Full  Amount Validate
                    if ($checkinData->paid_type == '0' || $checkinData->paid_type == 0) {
                        $checkinData['payment'] = 1;
                        if ($rate['price'] > "0") {
                            $updatedPaidAmount = $rate['price'];
                        }
                        $rate['price'] = "0.00";
                        $rate['facility'] = $checkinData->facility;
                        $checkinData['rate'] = $rate;
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);


                        if ($updatedPaidAmount <= "0") {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                            $checkinData['overstay_amount'] = "0.00";
                            $checkinData['paid_amount'] = $updatedPaidAmount;
                            $checkinData['payment'] = 1;
                        } else {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = number_format($updatedPaidAmount, 2);
                            $checkinData['paid_amount'] = $updatedPaidAmount + $checkinData->facility->processing_fee;
                        }
                        return $checkinData;
                    }

                    if ($checkinData->paid_type == '1' || $checkinData->paid_type == 1) {
                        if ($checkinData->paid_hour != '') {
                            //$diff_in_hours = $diff_in_hours - $checkinData->paid_hour;
                            if ($rate['price'] <= $checkinData->paid_amount) {
                                $checkinData['payment'] = 1;
                                $rate['price'] = "0";
                                $rate['facility'] = $checkinData->facility;
                                $checkinData['rate'] = $rate;
                                $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                return $checkinData;
                            }
                            $rate['price'] = $rate['price'];
                        }
                    }


                    if ($checkinData->paid_type == '2' || $checkinData->paid_type == 2) {
                        $checkinData['payment'] = 1;
                        $rate['price'] = $rate['price'];
                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            return $checkinData;
                        }
                    }
                    if ($checkinData->paid_type == '3' || $checkinData->paid_type == 3) {
                        $checkinData['payment'] = 1;

                        $percentageAmount = (($rate['price'] * $checkinData->paid_percentage) / 100);
                        if ($percentageAmount <= $checkinData->paid_amount) {
                            $rate['price'] = number_format($rate['price'], 2);
                        } else {
                            $rate['price'] = number_format($rate['price'], 2);
                        }

                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            return $checkinData;
                        }
                    }
                }

                $rate['price'] =  $rate['price'];
            }
        } else {
            if ($rate['price'] == "0.00" || $rate['price'] == 0) {
                $rate['price'] = 0.00;
            } else {
                $this->log->info("Received request for payment : ");
                $this->log->info("Received request for paid_type : {$checkinData->paid_type}");
                $priceBreakUp = $checkinData->priceBreakUp($rate);
                $this->log->info("MakeTicketPayment priceBreakUp " . json_encode($priceBreakUp));
                $checkinData['payable_amount']  = $priceBreakUp['payable_amount'];
                $checkinData['processing_fee']  = $priceBreakUp['processing_fee'];
                $checkinData['tax_rate']        = $priceBreakUp['tax_rate'];
                $checkinData['parking_amount']  = $priceBreakUp['parking_amount'];
                $checkinData['overstay_amount'] = $priceBreakUp['overstay_amount'];
                $checkinData['paid_amount']     = $priceBreakUp['paid_amount'];
                $checkinData['discount_amount'] = $priceBreakUp['discount_amount'];
                $checkinData['amount_paid']     = $checkinData->grand_total;

                if ($checkinData['payable_amount'] <= 0) {
                    if ($checkinData->paid_type == '0') {
                        $checkinData['payment'] = 1;  // payment done no need to make call payment api
                    } else {
                        $checkinData['payment'] = 0;    // need to call payment api 
                    }
                    $this->log->info("getCheckinTicketDetails Return ");
                    $this->log->info(json_encode($checkinData));
                    return $checkinData;
                }
                // $rate['price'] =  $rate['price'];
                $this->log->info("Here have some payable amount so print this line and go for payment ");
            }
        }

        // I think is the case or overstay because payment is done and again total have some value 
        if (($checkinData->anet_transaction_id != '') && ($request->total > 0)) {
            $paymentStatus = $this->makePlanetPayment($this->request, $checkinData);
            $this->log->info("Received request for payment : Going to overstay payment");
            //$paymentStatus = $this->makeDataCapPayment($this->request, $checkinData);

            if (isset($paymentStatus['error']) && $paymentStatus['error'] != '') {
                throw new ApiGenericException("Payment failed! Please enter valid card details.");
            }
            /*
            if (isset($paymentStatus) && $paymentStatus == "Payment Failed.") {
                throw new ApiGenericException("Payment failed! Please enter valid card details.");
            }
			*/
            // set overStay Flag 
            $request->request->add(['is_overstay' => '1']);
            $this->paymentStatus = $paymentStatus;
            $ticket = $this->saveOverstayTicket();
            $transaction = $this->saveTransactionData();
            $transaction->overstay_ticket_id = $ticket->id;
            $transaction->save();
            Artisan::queue('email:townsend-checkin-payment', array('id' => $ticket->id, 'type' => 'overstay'));
            return $ticket;
        }
        $this->log->info("Check sent rate match DB {$priceBreakUp['payable_amount']} SENT rate {$request->total} ");
        // dd($rate['price'], $request->rate_price);
        if ((float) $priceBreakUp['payable_amount'] != $request->total) {
            throw new ApiGenericException(
                'Sent rate does not match database rate, please reset parking options and try again.',
                500,
                ['sent_rate' => $request->total, 'database_rate' => $priceBreakUp['payable_amount']]
            );
        }

        $total = $rate['price'];
        //$this->facility = Facility::find($this->request->facility_id);

        if ($this->user) {

            $this->user->name = isset($this->request->name_on_card) ? $this->request->name_on_card : '';
            //$this->user->email = $this->request->email;
            $this->user->save();
        } else {

            throw new ApiGenericException("Payment failed! Invalid user.");
        }
        if ($total > 0) {
            try {
                // this to be handel database unkonw column error. 
                unset($checkinData['payable_amount']);
                unset($checkinData['tax_rate']);
                unset($checkinData['overstay_amount']);
                unset($checkinData['amount_paid']);
                //$paymentStatus = $this->makeDataCapPayment($this->request, $checkinData);
                $paymentStatus = $this->makePlanetPayment($this->request, $checkinData);
                //dd($paymentStatus,$this->request->all());

                if (isset($paymentStatus['error']) && $paymentStatus['error'] != '') {
                    throw new ApiGenericException("Payment failed! Please enter valid card details.");
                }
                /*
                if (isset($paymentStatus) && $paymentStatus == "Payment Failed.") {
                    throw new ApiGenericException("Payment failed! Please enter valid card details.");
                }
				*/

                $ticket = $this->saveTicket();

                $ticket->anet_transaction_id = $paymentStatus->id;
                $ticket->payment_date = date("Y-m-d H:i:s");
                if (isset($paymentStatus->id)) {
                    $ticket->card_type = $paymentStatus->card_type;
                    $ticket->card_last_four = $paymentStatus->payment_last_four;
                    $ticket->expiry = $paymentStatus->expiration;
                }
                $ticket->save();
                $transaction = $this->saveTransactionData();
                if ($this->request->email != '') {
                    Artisan::queue('email:townsend-checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));
                }
                return $ticket;
            } catch (Exception $e) {
                $this->log->error($e);
                throw new ApiGenericException("Payment error!");
                //$this->log->error("System exception -". $e->getMessage());
            }
        }
    }

    // 4th to scan QR code to checkout from facility
    public function ScanedTicketDetails(Request $request)
    {
        $this->log->info("Scaned Ticket Received request for payment : " . json_encode($request->all()));
        $this->request = $request;
        $this->request->request->add(['ticket_number' => $request->order_number]);

        $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        if (!$secret) {
            throw new ApiGenericException('Invalid partner.');
        }
        try {

            if ($request->encrypt) {
                $explode = explode(':', $request->encrypt);
                if (count($explode) < 5) {
                    if (isset($explode[3])) {
                        $exp = explode(".", $explode[3]);
                        $decrypt = base64_decode(trim($exp[0]));
                    } else {
                        return back()->with('danger', "Please scan the right QR code.");
                    }
                } else {
                    $decrypt = base64_decode($explode[4]);
                }

                if ($decrypt) {
                    $decrytArray = json_decode($decrypt);
                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();


                    $this->setCustomTimezone($decrytArray->facility_id);
                    $licensePlate = LicensePlate::where('facility_id', $decrytArray->facility_id)->where('gate', $decrytArray->gate)->orderBy("id", "DESC")->first();
                    $checkinData = [];
                    if ($gate && isset($gate->gate_type)) {
                        if ($gate->gate_type == 'entry') {
                            throw new ApiGenericException('Please scan the right QR code.');
                        }
                        $is_checkout = Ticket::where('ticket_number', $request->order_number)->where('is_checkout', '1')->first();
                        if ($is_checkout) {
                            throw new ApiGenericException('You have already checked-Out.');
                        }
                        $this->log->info("Scaned Ticket Received GATE OK ");
                        //$this->checkFacilityAvailable($decrytArray->facility_id);

                        $checkinData = Ticket::with(['user', 'facility'])->where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                        if ($checkinData) {
                            $this->log->info("Scaned Ticket Received TICKET OK ");
                            if ($checkinData->facility->check_vehicle_enabled == '1') {
                                $gateStatus = $this->isParkEngageGateAvailable($decrytArray->facility_id, $decrytArray->gate, $request['From']);
                                if ($gateStatus === false) {
                                    return true;
                                }
                            }

                            if ($checkinData->is_offline_payment != 0 || $checkinData->is_offline_payment != '0') {
                                $this->log->info("Scaned Ticket Received Check Offline Flags ");
                                $ticketData = Ticket::where('id', $checkinData->id)->first();
                                $ticketData->is_checkout = '1';
                                $ticketData->checkout_gate = $decrytArray->gate;
                                $ticketData->checkout_time = date("Y-m-d H:i:s");
                                $ticketData->checkout_datetime = date("Y-m-d H:i:s");
                                $ticketData->save();

                                if ($licensePlate) {
                                    $ticketData->checkout_license_plate = $licensePlate->license_plate;
                                    $ticketData->save();
                                    $licensePlate->delete();
                                }

                                $ticketData['amount_paid'] = $ticketData->grand_total;

                                if ($checkinData->facility->open_gate_enabled == '1') {
                                    $gateStatus = $this->isParkEngageGateOpen($decrytArray->facility_id, $decrytArray->gate, '');
                                    if ($gateStatus == "true") {
                                    } else {
                                        throw new ApiGenericException($gateStatus);
                                    }
                                }

                                if (isset($checkinData->user->phone) && $checkinData->user->phone != '') {
                                    $partnerDetails = UserPaymentGatewayDetail::where('user_id', $checkinData->partner_id)->first();
                                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                    $facilityName = isset($checkinData->facility->full_name) ? ucwords($checkinData->facility->full_name) : '';
                                    $url = env('RECEIPT_URL');
                                    $msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download e-receipt $url/$name/ticket/" . $checkinData->ticket_number;

                                    $this->customeReplySms($msg, $checkinData->user->phone);
                                }
                                return $ticketData;
                            }

                            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                            $diff_in_hours = $checkinData->getCheckOutCurrentTime(true);
                            $isMember = 0;
                            $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                            $this->rate = $rate;
                            if ($rate == false) {
                                throw new ApiGenericException('Garage is currently closed.');
                            }
                            $parkingAmount = $rate['price'];
                            $this->log->info("Scaned Ticket Received Get Rate  {$parkingAmount}");
                            $processingFee = $checkinData->getProcessingFee();   // to get prcessing free channel wise need to
                            $taxRate = $checkinData->getTaxRate($rate);
                            $ticketPrice = $rate['price'] + $processingFee + $taxRate;

                            $priceBreakUp = $checkinData->priceBreakUp($rate);
                            $this->log->info("priceBreakUp " . json_encode($priceBreakUp));

                            if ($checkinData->anet_transaction_id == '' && $checkinData->paid_by == '') {
                                $this->log->info("Scaned Ticket anet_transaction_id {$parkingAmount}");
                                $tax_rate = $taxRate;
                                $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();

                                if ($rate['price'] == 'N/A') {
                                    $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
                                    if ($rate['price'] == 0.00 || $rate['price'] == "0.00") {
                                    } else {
                                        if ($tax_rate > 0) {
                                            $tax_rate = number_format((($rate['price'] * $checkinData->facility->tax_rate) / 100), 2);
                                        }
                                        if ($overstay) {
                                            $checkinData['overstay_amount'] = $overstay->total;
                                        } else {
                                            $checkinData['overstay_amount'] = '0';
                                        }
                                        if ($rate['price'] <= "0") {
                                            $checkinData['payable_amount'] = "0.00";
                                            $checkinData['processing_fee'] = "0.00";
                                            $checkinData['tax_rate'] = "0.00";
                                        } else {
                                            $checkinData['payable_amount'] = number_format($rate['price'] + $checkinData->facility->processing_fee + $tax_rate - $checkinData->grand_total - $checkinData->paid_amount, 2);
                                            $checkinData['processing_fee'] = $checkinData['processing_fee'];
                                            $checkinData['tax_rate'] = $tax_rate;
                                            if ($rate['price'] <= "0") {
                                                $checkinData['payable_amount'] = "0.00";
                                                $checkinData['processing_fee'] = "0.00";
                                                $checkinData['tax_rate'] = "0.00";
                                            }
                                        }
                                        $checkinData['parking_amount'] = ($checkinData['total'] + $checkinData['paid_amount']) - $checkinData['processing_fee'] - $checkinData['tax_fee'];

                                        $checkinData['payment'] = 0;
                                        $checkinData['rate'] = $rate;
                                        $checkinData['amount_paid'] = $checkinData->grand_total;
                                        return $checkinData;
                                    }
                                } else {
                                    if ($rate['price'] > 0.00) {
                                        if ($overstay) {
                                            $checkinData['overstay_amount'] = $overstay->total;
                                        } else {
                                            $checkinData['overstay_amount'] = '0';
                                        }
                                        if ($rate['price'] <= "0") {
                                            $checkinData['payable_amount'] = "0.00";
                                            $checkinData['processing_fee'] = "0.00";
                                            $checkinData['tax_rate'] = "0.00";
                                        } else {
                                            $checkinData['payable_amount'] = number_format($rate['price'] + $checkinData->facility->processing_fee + $tax_rate - $checkinData->grand_total - $checkinData->paid_amount, 2);
                                            $checkinData['processing_fee'] = $checkinData['processing_fee'];
                                            $checkinData['tax_rate'] = $tax_rate;
                                            if ($rate['price'] <= "0") {
                                                $checkinData['payable_amount'] = "0.00";
                                                $checkinData['processing_fee'] = "0.00";
                                                $checkinData['tax_rate'] = "0.00";
                                            }
                                        }
                                        $checkinData['parking_amount'] = ($checkinData['total'] + $checkinData['paid_amount']) - $checkinData['processing_fee'] - $checkinData['tax_fee'];

                                        $checkinData['payment'] = 0;
                                        $checkinData['rate'] = $rate;
                                        $checkinData['amount_paid'] = $checkinData->grand_total;
                                        return $checkinData;
                                    } else {
                                        $ticketData = Ticket::where('id', $checkinData->id)->first();
                                        $ticketData->paid_amount = $priceBreakUp['paid_amount'];
                                        $ticketData->processing_fee = $priceBreakUp['processing_fee'];
                                        // Vijay Added : 27-07-2023
                                        $ticketData->total = $priceBreakUp['total'];
                                        $ticketData->length = $diff_in_hours;
                                        $ticketData->grand_total = $priceBreakUp['grand_total'];
                                        $ticketData->parking_amount = $priceBreakUp['parking_amount'];
                                        // Vijay Added : 27-07-2023
                                        $ticketData->save();
                                        $this->saveTransactionData();
                                    }
                                }
                            } else if ($checkinData->paid_by != '') {

                                // Full  Amount Validate
                                if ($checkinData->paid_type == '0' || $checkinData->paid_type == 0) {
                                    $checkinData['payment'] = 1;
                                    if ($rate['price'] > "0") {
                                        $updatedPaidAmount = $rate['price'];
                                    } else {
                                        $updatedPaidAmount = "0.00";
                                    }
                                    $rate['price'] = "0.00";
                                    $rate['facility'] = $checkinData->facility;
                                    $checkinData['rate'] = $rate;
                                    $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                    $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                    $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                    $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                    $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);


                                    $ticketData = Ticket::where('id', $checkinData->id)->first();
                                    $ticketData->paid_amount = $priceBreakUp['paid_amount'];
                                    $ticketData->processing_fee = $priceBreakUp['processing_fee'];
                                    // Vijay Added : 27-07-2023
                                    $ticketData->total = $priceBreakUp['total'];
                                    $ticketData->length = $diff_in_hours;
                                    $ticketData->grand_total = $priceBreakUp['grand_total'];
                                    $ticketData->parking_amount = $priceBreakUp['parking_amount'];
                                    // Vijay Added : 27-07-2023
                                    $ticketData->save();
                                    $this->saveTransactionData();
                                    // if ($updatedPaidAmount > "0") {
                                    //     $ticketData->paid_amount = $updatedPaidAmount + $processingFee;
                                    //     $ticketData->processing_fee = $parkingAmount > 0 ? $processingFee : '0.00';
                                    //     // Vijay Added : 27-07-2023
                                    //     $ticketData->total = isset($validationData) ? $validationData['total'] : $ticketPrice;
                                    //     $ticketData->length = $diff_in_hours;
                                    //     $ticketData->grand_total = '0.00';
                                    //     $ticketData->parking_amount = $parkingAmount;
                                    //     // Vijay Added : 27-07-2023
                                    //     $ticketData->save();
                                    // } else {
                                    //     $ticketData->paid_amount = '0.00';
                                    //     $ticketData->processing_fee = '0.00';
                                    //     // Vijay Added : 27-07-2023
                                    //     $ticketData->total = '0.00';
                                    //     $ticketData->length = $diff_in_hours;
                                    //     $ticketData->grand_total = '0.00';
                                    //     $ticketData->parking_amount = '0.00';
                                    //     // Vijay Added : 27-07-2023
                                    //     $ticketData->save();
                                    // }
                                }

                                // Hour validate
                                if ($checkinData->paid_type == '1' || $checkinData->paid_type == 1) {
                                    if ($checkinData->paid_hour != '') {
                                        $checkinData['payment'] = 0;
                                        $duration_in_hou = $diff_in_hours - $checkinData->paid_hour;
                                        $duration = $this->getEndTime($duration_in_hou);
                                        $duration_in_min1 = $this->getEndTime($diff_in_hours);
                                        $total_amount =  $rate['price'];
                                        $paid_amount = "0.00";
                                        if ($duration == 0) {
                                            $rate['price'] = "0";
                                            $updatedPaidAmount = "0";
                                            $checkinData->paid_amount = $rate['price'];
                                        } else {
                                            $paid_amount = ((($total_amount) / $duration_in_min1) * $duration);
                                            $paid_amount = round($paid_amount);
                                            $checkinData->paid_amount = $paid_amount;
                                        }

                                        if ($rate['price'] <= $paid_amount) {
                                            $checkinData['payment'] = 1;
                                            $rate['price'] = "0";
                                            $rate['facility'] = $checkinData->facility;
                                            $checkinData['rate'] = $rate;
                                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                            $checkinData['payable_amount'] = "0.00";
                                            $checkinData['processing_fee'] = "0.00";
                                            $checkinData['tax_rate'] = "0.00";
                                            $checkinData['parking_amount'] = "0.00";
                                        } else {
                                            $updatedPaidAmount = $rate['price'];
                                            $payable_amount = $updatedPaidAmount - $paid_amount;
                                            if ($checkinData->grand_total > "0") {
                                                $payable_amount = $payable_amount - $checkinData->grand_total;
                                            }
                                            if ($payable_amount > "0") {
                                                $payable_amount = $payable_amount + $processingFee;
                                                $processing_fee = $processingFee;
                                            } else {
                                                $payable_amount = "0.00";
                                                $processing_fee = "0.00";
                                                $checkinData['payment'] = 1;
                                            }
                                            if ($updatedPaidAmount > "0") {
                                                $ticketData = Ticket::where('id', $checkinData->id)->first();
                                                $ticketData->paid_amount =  $priceBreakUp['paid_amount'];;
                                                $ticketData->processing_fee = $processingFee;
                                                $ticketData->save();
                                            }
                                            $checkinData['rate'] = $rate;
                                            $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                            $checkinData['processing_fee'] = $processing_fee;
                                            $checkinData['tax_rate'] = "0.00";
                                            $checkinData['parking_amount'] = $updatedPaidAmount;
                                        }
                                        //return $checkinData;
                                    }
                                }

                                //paid amount
                                if ($checkinData->paid_type == '2' || $checkinData->paid_type == 2) {

                                    $checkinData['payment'] = 0;
                                    $rate['price'] = $rate['price'];
                                    if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                                        $checkinData['payment'] = 1;
                                        $rate['price'] = "0";
                                        $rate['facility'] = $checkinData->facility;
                                        $checkinData['rate'] = $rate;
                                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                        $checkinData['payable_amount'] = "0.00";
                                        $checkinData['processing_fee'] = "0.00";
                                        $checkinData['tax_rate'] = "0.00";
                                        $checkinData['parking_amount'] = "0.00";
                                    } else {
                                        if ($checkinData->paid_amount != '') {
                                            $updatedPaidAmount = $rate['price'];
                                            $payable_amount = $updatedPaidAmount - $checkinData->paid_amount;
                                            if ($checkinData->grand_total > "0") {
                                                $payable_amount = $payable_amount - $checkinData->grand_total;
                                            }
                                            if ($payable_amount >= "0") {
                                                $payable_amount = $payable_amount + $processingFee;
                                                $processing_fee = $processingFee;
                                            } else if ($payable_amount <= $checkinData->paid_amount) {
                                                $checkinData->paid_amount = $updatedPaidAmount + $processingFee;
                                                $processing_fee = $processingFee;
                                                $payable_amount = "0.00";
                                                $checkinData['payment'] = 1;
                                            } else {
                                                $payable_amount = "0.00";
                                                $processing_fee = "0.00";
                                                $checkinData['payment'] = 1;
                                            }
                                            $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                            $checkinData['processing_fee'] = $processingFee;
                                            $checkinData['tax_rate'] = $taxRate;
                                            $checkinData['parking_amount'] = $updatedPaidAmount;
                                            if ($updatedPaidAmount > "0") {
                                                $ticketData = Ticket::where('id', $checkinData->id)->first();
                                                // $ticketData->paid_amount = $updatedPaidAmount + $processingFee; // not clear why we update this again
                                                $ticketData->processing_fee = $processingFee;
                                                // Vijay Added : 27-07-2023
                                                $ticketData->parking_amount = $parkingAmount;
                                                $ticketData->total = $ticketPrice;
                                                $ticketData->length = $diff_in_hours;
                                                $ticketData->grand_total = isset($priceBreakUp) ? $priceBreakUp['grand_total'] : number_format($payable_amount, 2);
                                                $ticketData->paid_amount = $priceBreakUp['paid_amount'];
                                                // Vijay Added : 27-07-2023
                                                $ticketData->save();
                                            }
                                        }
                                        $checkinData['rate'] = $rate;
                                    }
                                    //return $checkinData;
                                }

                                // paid percentage
                                if ($checkinData->paid_type == '3' || $checkinData->paid_type == 3) {
                                    $checkinData['payment'] = 0;

                                    $total_amount =  $rate['price'];
                                    if ($total_amount > "0.00") {
                                        $percentageAmount = (($total_amount * $checkinData->paid_percentage) / 100);
                                    } else {
                                        $total_amount = "0.00";
                                        $percentageAmount = "0.00";
                                    }

                                    $rate['price'] = number_format($rate['price'], 2);

                                    if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {

                                        $checkinData['payment'] = 1;
                                        $rate['price'] = "0";
                                        $rate['facility'] = $checkinData->facility;
                                        $checkinData['rate'] = $rate;
                                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                        $checkinData['payable_amount'] = "0.00";
                                        $checkinData['processing_fee'] = "0.00";
                                        $checkinData['tax_rate'] = "0.00";
                                        $checkinData['parking_amount'] = "0.00";
                                    } else {
                                        if ($percentageAmount > '0.00') {
                                            $updatedPaidAmount = $rate['price'];
                                            $payable_amount = $updatedPaidAmount - $percentageAmount;
                                            if ($checkinData->grand_total > "0") {
                                                $payable_amount = $payable_amount - $checkinData->grand_total;
                                            }
                                            if ($payable_amount > "0") {
                                                $payable_amount = $payable_amount + $processingFee;
                                                $processing_fee = $processingFee;
                                            } else {
                                                $payable_amount = "0.00";
                                                $processing_fee = "0.00";
                                                $checkinData['payment'] = 1;
                                            }
                                            $checkinData['payable_amount'] = number_format($priceBreakUp['payable_amount'], 2);
                                            $checkinData['processing_fee'] = $priceBreakUp['processing_fee'];
                                            $checkinData['tax_rate'] = $priceBreakUp['tax_rate'];
                                            $checkinData['parking_amount'] = $priceBreakUp['parking_amount'];
                                            if ($updatedPaidAmount > "0") {
                                                $ticketData = Ticket::where('id', $checkinData->id)->first();
                                                // $ticketData->paid_amount = $updatedPaidAmount + $processingFee; // not clear why its update
                                                $ticketData->paid_amount = $priceBreakUp['paid_amount'];
                                                $ticketData->processing_fee = $priceBreakUp['processing_fee'];
                                                // Vijay Added : 27-07-2023
                                                $ticketData->total = $priceBreakUp['total'];
                                                $ticketData->length = $diff_in_hours;
                                                $ticketData->grand_total = $priceBreakUp['grand_total'];
                                                $ticketData->parking_amount = $priceBreakUp['parking_amount'];
                                                // Vijay Added : 27-07-2023
                                                if ($ticketData->grand_total <= 0) {
                                                    $this->saveTransactionData();
                                                }
                                                $ticketData->save();
                                            }
                                        }
                                        $checkinData['rate'] = $rate;
                                    }
                                    // return $checkinData;
                                }
                            }

                            if ($checkinData->facility->open_gate_enabled == '1') {
                                $gateStatus = $this->isParkEngageGateOpen($decrytArray->facility_id, $decrytArray->gate, '');
                                if ($gateStatus == "true") {
                                } else {
                                    throw new ApiGenericException($gateStatus);
                                }
                            }

                            //$rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
                            $ticketData = Ticket::where('id', $checkinData->id)->first();
                            $ticketData->is_checkout = '1';
                            $ticketData->checkout_gate = $decrytArray->gate;
                            $ticketData->checkout_time = date("Y-m-d H:i:s");
                            $ticketData->checkout_datetime = date("Y-m-d H:i:s");
                            $ticketData->save();

                            if ($licensePlate) {
                                $ticketData->checkout_license_plate = $licensePlate->license_plate;
                                $ticketData->save();
                                $licensePlate->delete();
                            }

                            if ($checkinData) {
                                $checkinData = Ticket::with(['user', 'facility'])->where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->first();

                                $this->log->info("user checkout with ticket number {$checkinData->ticket_number}");
                                // return Redirect::to('townsend-thankyou-checkout/'.base64_encode($checkinData->ticket_number));
                                //return $this->thankyouCheckout(base64_encode($checkinData->ticket_number));
                                if (isset($checkinData->user->phone) && $checkinData->user->phone != '') {
                                    $partnerDetails = UserPaymentGatewayDetail::where('user_id', $checkinData->partner_id)->first();
                                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                    $facilityName = isset($checkinData->facility->full_name) ? ucwords($checkinData->facility->full_name) : '';
                                    $url = env('RECEIPT_URL');
                                    $msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download e-receipt $url/$name/ticket/" . $checkinData->ticket_number;

                                    $this->customeReplySms($msg, $checkinData->user->phone);
                                }

                                if ($checkinData->is_overstay == '1') {
                                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->estimated_checkout);
                                    $diff_in_hours = $checkinData->getCheckOutCurrentTime(true, $checkinData->estimated_checkout);
                                } else {
                                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                    $diff_in_hours = $checkinData->getCheckOutCurrentTime(true);
                                }
                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));


                                $isMember = 0;
                                $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                                if ($rate == false) {
                                    throw new ApiGenericException('Garage is currently closed.');
                                }

                                $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();

                                $priceBreakUp = $checkinData->priceBreakUp($rate);
                                $this->log->info("overstay priceBreakUp " . json_encode($priceBreakUp));
                                $checkinData['payable_amount']  = $priceBreakUp['payable_amount'];
                                $checkinData['processing_fee']  = $priceBreakUp['processing_fee'];
                                $checkinData['tax_rate']        = $priceBreakUp['tax_rate'];
                                $checkinData['parking_amount']  = $priceBreakUp['parking_amount'];
                                $checkinData['paid_amount']     = $priceBreakUp['paid_amount'];
                                $checkinData['discount_amount'] = $priceBreakUp['discount_amount'];

                                if ($checkinData->is_overstay == '1') {
                                    $checkinData['overstay_amount'] = $priceBreakUp['overstay_amount'];
                                    $checkinData['amount_paid']     = $priceBreakUp['amount_paid'];
                                } else {
                                    $checkinData['overstay_amount'] = $priceBreakUp['overstay_amount'];
                                    $checkinData['amount_paid']     = $checkinData->grand_total;
                                }
                                return $checkinData;
                                if ($overstay) {
                                    $checkinData['overstay_amount'] = $overstay->total;
                                } else {
                                    $checkinData['overstay_amount'] = '0';
                                }
                                if ($rate['price'] <= "0") {
                                    $checkinData['payable_amount'] = "0.00";
                                    $checkinData['processing_fee'] = "0.00";
                                    $checkinData['tax_rate'] = "0.00";
                                } else {
                                    $checkinData['payable_amount'] = number_format($rate['price'] + $checkinData->facility->processing_fee + $tax_rate - $checkinData->grand_total - $checkinData->paid_amount, 2);
                                    $checkinData['processing_fee'] = $checkinData['processing_fee'];
                                    $checkinData['tax_rate'] = $tax_rate;
                                    if ($rate['price'] <= "0") {
                                        $checkinData['payable_amount'] = "0.00";
                                        $checkinData['processing_fee'] = "0.00";
                                        $checkinData['tax_rate'] = "0.00";
                                    }
                                }
                                // $checkinData['parking_amount'] = ($checkinData['grand_total']) - $checkinData['processing_fee'] - $tax_rate;
                                $checkinData['parking_amount'] = $ticketData->parking_amount;
                                if ($checkinData['parking_amount'] <= "0") {
                                    $checkinData['parking_amount'] = "0.00";
                                }

                                $checkinData['amount_paid'] = $checkinData->grand_total;
                                return $checkinData;
                            } else {
                                //return back()->with('danger', 'Please scan valid scan QR code.');
                                throw new ApiGenericException('Please scan valid scan QR code.');
                            }
                        }
                        $this->log->info("Scaned Ticket Received GATE NOT OK ");
                        /*$checkinData = Ticket::where('ticket_number', $request->order_number)->where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();*/
                    } else {
                        // return Redirect::to('error-checkin');
                        throw new ApiGenericException('Gate Error.');
                    }
                    $checkinData = Ticket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->first();

                    $this->log->info("Scaned Ticket Received GATE NT OK OK ");

                    if ($checkinData) {
                        //return Redirect::to('thankyou-checkin');
                        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();
                        if ($overstay) {
                            $checkinData['overstay_amount'] = $overstay->total;
                        } else {
                            $checkinData['overstay_amount'] = '0';
                        }
                        $checkinData['payable_amount'] = "0.00";
                        $checkinData['parking_amount'] = ($checkinData['grand_total']) - $checkinData['processing_fee'] - $checkinData['tax_fee'];
                        if ($checkinData['parking_amount'] <= "0") {
                            $checkinData['parking_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                        }
                        $checkinData['processing_fee'] = "0.00";
                        $checkinData['tax_rate'] = "0.00";

                        return $checkinData;
                    } else {
                        throw new ApiGenericException('No recent ticket is found for this user.');
                    }
                }
            } else {
                //return back()->with('danger', 'Please scan valid scan QR code.');
                throw new ApiGenericException('Please scan valid scan QR code.');
            }
        } catch (Exception $e) {
            // return back()->with('danger', 'Please scan valid scan QR code.');
            throw new ApiGenericException('Please scan valid scan QR code.');
        }
    }

    //First Screen only details of ticket
    public function getCheckinTicketDetails_backup(Request $request, $ticket_number, $user_id = '')
    {
        $this->log->info("getCheckinTicketDetails " . json_encode($request->all()));
        $this->log->info(" {$ticket_number}");
        $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        if (!$secret) {
            throw new ApiGenericException('Invalid partner.');
        }

        $checkinData = Ticket::with(['user', 'facility.photos'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();

        if (count($checkinData) == 0) {
            $this->log->error("error on checkin screen");
            //return Redirect::to('townsend-error-checkin');
            throw new ApiGenericException("No recent ticket is found for this user.");
        }
        if (($user_id != '') && ($checkinData->user_id != $user_id)) {
            throw new ApiGenericException("No recent ticket is found for this user.");
        }
        if ($this->checkFacilityAvailable($checkinData->facility_id) == false) {
            //Redirect::to('error-facility');
            throw new ApiGenericException("Facility not available.");
        }

        $this->setCustomTimezone($checkinData->facility_id);

        if ($checkinData->anet_transaction_id != '') {
            //return redirect('townsend-thankyou-payment/'.base64_encode($checkinData->ticket_number));
            return $this->getCheckinDetailsPaymentThankyou($request, base64_encode($checkinData->ticket_number));
        }
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

        // $diff_in_hours = $arrival_time->diffInHours($from);
        // $diff_in_mins = $arrival_time->diffInMinutes($from);
        // $diff_in_secs = $arrival_time->diffInSeconds($from);

        // if ($diff_in_mins < 60) {
        //     $diff_in_hours = $diff_in_mins / 100;
        // }
        // if ($diff_in_mins > 59) {
        //     $diff_in_hours = number_format($diff_in_mins / 60, 2);
        // }
        // if ($diff_in_secs < 60) {
        //     $diff_in_hours = .01;
        // }
        $diff_in_hours = $checkinData->getCheckOutCurrentTime(true);

        $checkinData['length'] = $diff_in_hours;

        $isMember = 0;
        $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
        if ($rate == false) {
            //return Redirect::to('townsend-error-facility');
            throw new ApiGenericException("Rate not found.");
        }

        //return current availabilit
        if ($rate['price'] == 'N/A') {
            $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            //$rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
        } else {
            //$rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
        }

        //returning  message as per availibility 
        $checkinData['rate'] = $rate;
        $this->log->info("user is on checkin screen on sms click with ticket number {$checkinData->ticket_number}");
        // return view('townsend.payment-screen', ['data'=>$checkinData]);
        $updatedPaidAmount = "0";

        $checkinData['DATACAP_SCRIPT_URL'] = config('parkengage.DATACAP_SCRIPT_URL');
        $checkinData['DATACAP_TOKEN'] = $checkinData->facility->ecommerce_token;
        $checkinData['DATACAP_OTU_URL'] = config('parkengage.DATACAP_OTU_URL');

        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();
        if ($overstay) {
            $checkinData['overstay_amount'] = $overstay->total;
        } else {
            $checkinData['overstay_amount'] = "0.00";
        }
        $taxRate = $checkinData->getTaxRate($rate);          // to get tax price
        $processingFee = $checkinData->getProcessingFee();   // to get prcessing free channel wise need to
        if ($rate['price'] == "0.00" || $rate['price'] == 0) {
            $rate['price'] = 0.00;
        } else {
            $this->log->info("getCheckinTicketDetails Validated ");
            if ($checkinData->paid_by != '') {
                $this->log->info("getCheckinTicketDetails Validated paid_type {$checkinData->paid_type}");
                if ($checkinData->paid_type != 9) {
                    $validationData = $this->populateValidationFields($checkinData, $rate, $checkinData->facility);
                    $this->log->info("populateValidationFields " . json_encode($validationData));
                }
                // Full  Amount Validate
                if ($checkinData->paid_type == '0' || $checkinData->paid_type == 0) {
                    $checkinData['payment'] = 1;
                    if ($rate['price'] > "0") {
                        $updatedPaidAmount = $rate['price'];
                    }
                    $rate['price'] = "0.00";
                    $rate['facility'] = $checkinData->facility;
                    $checkinData['rate'] = $rate;
                    $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                    $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                    $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                    $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                    $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);


                    if ($updatedPaidAmount <= "0") {
                        $checkinData['payable_amount'] = "0.00";
                        $checkinData['processing_fee'] = "0.00";
                        $checkinData['tax_rate'] = "0.00";
                        $checkinData['parking_amount'] = "0.00";
                        $checkinData['overstay_amount'] = "0.00";
                        $checkinData['paid_amount'] = $updatedPaidAmount;
                    } else {
                        $checkinData['payable_amount'] = "0.00";
                        $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
                        $checkinData['tax_rate'] = "0.00";
                        $checkinData['parking_amount'] = number_format($updatedPaidAmount, 2);
                        $checkinData['paid_amount'] = $updatedPaidAmount + $checkinData->facility->processing_fee;
                    }
                    return $checkinData;
                }

                // Hour validate
                if ($checkinData->paid_type == '1' || $checkinData->paid_type == 1) {
                    if ($checkinData->paid_hour != '') {
                        $checkinData['payment'] = 0;
                        $isMember = 0;
                        // change in hours 
                        // $duration_in_hou = $diff_in_hours - $checkinData->paid_hour;
                        // $duration = $this->getEndTime($duration_in_hou);
                        // $duration_in_min1 = $this->getEndTime($diff_in_hours);
                        // $total_amount =  $rate['price'];
                        // $paid_amount = "0.00";
                        // if ($duration == 0) {
                        //     $checkinData['payment'] = 1;
                        //     $rate['price'] = "0";
                        //     $updatedPaidAmount = "0";
                        //     $checkinData->paid_amount = $rate['price'];
                        // } else {
                        //     $paid_amount = ((($total_amount) / $duration_in_min1) * $duration);
                        //     $paid_amount = round($paid_amount);
                        //     $checkinData->paid_amount = $paid_amount;
                        // }

                        $diff_in_hours = $diff_in_hours - $checkinData->paid_hour;
                        $discountRate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                        $total_amount =  $discountRate['price'];
                        $paid_amount = "0.00";
                        if ($rate['price'] <= $paid_amount) {
                            $checkinData['payment'] = 1;
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                            $checkinData['payment'] = 1;
                        } else {
                            // $updatedPaidAmount = $rate['price'];
                            // $payable_amount = $updatedPaidAmount - $paid_amount;
                            $updatedPaidAmount = $rate['price'] - $discountRate['price'];
                            $payable_amount = $discountRate['price'];

                            if ($checkinData->grand_total > "0") {
                                $payable_amount = $payable_amount - $checkinData->grand_total;
                            }
                            if ($payable_amount >= "0") {
                                $payable_amount = $payable_amount + $processingFee;
                                $processing_fee = $processingFee;
                            } else {
                                $payable_amount = "0.00";
                                $processing_fee = $processingFee;
                                $checkinData['payment'] = 1;
                            }

                            $checkinData['rate'] = $rate;
                            $checkinData['paid_amount'] = $validationData['paid_amount'];
                            $checkinData['total'] = $validationData['total'];
                            $checkinData['amount_paid'] = $checkinData->grand_total;
                            $checkinData['payable_amount'] = number_format($payable_amount, 2);
                            $checkinData['processing_fee'] = $processing_fee;
                            $checkinData['tax_rate'] = $taxRate;
                            $checkinData['parking_amount'] = number_format($rate['price'], 2);
                        }
                        return $checkinData;
                    }
                }

                //paid amount
                if ($checkinData->paid_type == '2' || $checkinData->paid_type == 2) {

                    $checkinData['payment'] = 0;
                    $rate['price'] = $rate['price'];
                    if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                        $checkinData['payment'] = 1;
                        $rate['price'] = "0";
                        $rate['facility'] = $checkinData->facility;
                        $checkinData['rate'] = $rate;
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                        $checkinData['payable_amount'] = "0.00";
                        $checkinData['processing_fee'] = "0.00";
                        $checkinData['tax_rate'] = "0.00";
                        $checkinData['parking_amount'] = "0.00";
                    } else {
                        if ($checkinData->paid_amount != '') {
                            $updatedPaidAmount = $rate['price'];
                            $payable_amount = $updatedPaidAmount - $checkinData->paid_amount;
                            if ($checkinData->grand_total > "0") {
                                $payable_amount = $payable_amount - $checkinData->grand_total;
                            }
                            if ($payable_amount >= "0") {
                                $payable_amount = $payable_amount + $checkinData->facility->processing_fee;
                                $processing_fee = $checkinData->facility->processing_fee;
                            } else if ($payable_amount <= $checkinData->paid_amount) {
                                $checkinData->paid_amount = $updatedPaidAmount + $checkinData->facility->processing_fee;
                                $processing_fee = $checkinData->facility->processing_fee;
                                $payable_amount = "0.00";
                                $checkinData['payment'] = 1;
                            } else {
                                $checkinData['payment'] = 1;
                                $payable_amount = "0.00";
                                $processing_fee = "0.00";
                            }
                            $checkinData['paid_amount'] = $validationData['paid_amount'];
                            $checkinData['amount_paid'] = $checkinData->grand_total;
                            $checkinData['total'] = $validationData['total'];
                            $checkinData['payable_amount'] = number_format($payable_amount, 2);
                            $checkinData['processing_fee'] = $processing_fee;
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = number_format($updatedPaidAmount, 2);
                        }
                        $checkinData['rate'] = $rate;
                    }
                    return $checkinData;
                }

                // paid percentage
                if ($checkinData->paid_type == '3' || $checkinData->paid_type == 3) {
                    $checkinData['payment'] = 0;

                    $processingFee = $checkinData->getProcessingFee($checkinData); // to get prcessing free channel wise need to
                    $total_amount =  $rate['price'] + $processingFee + $taxRate;
                    if ($total_amount > "0.00") {
                        $percentageAmount = (($total_amount * $checkinData->paid_percentage) / 100);

                        if (($checkinData->max_validated_amount > 0) && $checkinData->max_validated_amount != '') {
                            if ($percentageAmount <= $checkinData->max_validated_amount) {
                                $payableAmount = number_format($total_amount - $percentageAmount, 2);
                                $updatedPaidAmount = $percentageAmount;
                            } else {
                                $payableAmount = number_format($total_amount - $checkinData->max_validated_amount, 2);
                                $updatedPaidAmount = $checkinData->max_validated_amount;
                            }
                        } else {
                            $payableAmount = number_format($total_amount - $percentageAmount, 2);
                            $updatedPaidAmount = $percentageAmount;
                        }
                    } else {
                        $total_amount = "0.00";
                        $percentageAmount = "0.00";
                    }

                    $rate['price'] = number_format($rate['price'], 2);
                    // dd($payableAmount);
                    if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {

                        $checkinData['payment'] = 1;
                        $rate['price'] = "0";
                        $rate['facility'] = $checkinData->facility;
                        $checkinData['rate'] = $rate;
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                        $checkinData['payable_amount'] = "0.00";
                        $checkinData['processing_fee'] = "0.00";
                        $checkinData['tax_rate'] = "0.00";
                        $checkinData['parking_amount'] = "0.00";
                    } else {
                        if ($percentageAmount > '0.00') {
                            $updatedPaidAmount = $rate['price'];
                            // $payable_amount = $updatedPaidAmount - $percentageAmount;
                            $payable_amount = $payableAmount;
                            if ($payable_amount > "0") {
                                $payable_amount = $payable_amount;
                                // $processing_fee = $processingFee;
                            } else {
                                $payable_amount = "0.00";
                                // $processing_fee = "0.00";
                                $checkinData['payment'] = 1;
                            }
                            $checkinData['paid_amount'] = $validationData['paid_amount'];
                            $checkinData['total'] = $validationData['total'];
                            $checkinData['amount_paid'] = $checkinData->grand_total;
                            $checkinData['payable_amount'] = number_format($payable_amount, 2);
                            $checkinData['processing_fee'] = $processingFee;
                            $checkinData['tax_rate'] = $taxRate;
                            $checkinData['parking_amount'] = $updatedPaidAmount;
                        }
                        $checkinData['rate'] = $rate;
                    }
                    // dd($rate['price'], $checkinData['payable_amount'], $checkinData['processing_fee'], $checkinData['parking_amount'], $checkinData['paid_amount']);
                    return $checkinData;
                }
            }
        }

        $tax_rate = $checkinData->facility->tax_rate;
        if ($tax_rate > 0) {
            $tax_rate = number_format((($rate['price'] * $checkinData->facility->tax_rate) / 100), 2);
        }
        if ($rate['price'] <= "0") {
            $checkinData['payable_amount'] = "0.00";
            $checkinData['processing_fee'] = "0.00";
            $checkinData['tax_rate'] = "0.00";
            $checkinData['parking_amount'] = "0.00";
            $checkinData['payment'] = 1;
        } else {
            $checkinData['payable_amount'] = number_format($rate['price'] + $checkinData->facility->processing_fee + $tax_rate - $checkinData->grand_total - $checkinData->paid_amount, 2);
            $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
            $checkinData['tax_rate'] = $tax_rate;
            $checkinData['parking_amount'] = number_format($rate['price'], 2);
            if ($checkinData['payable_amount'] <= "0") {
                $checkinData['payable_amount'] = "0.00";
                $checkinData['processing_fee'] = "0.00";
                $checkinData['tax_rate'] = "0.00";
                $checkinData['payment'] = 1;
            }
            $this->log->info("Print checkin data before return");
            $this->log->info(json_encode($checkinData));
        }
        return $checkinData;
    }


    // 2nd where payable amount and other ticket details display
    public function AfterCheckinPaymentDetails_backup(Request $request, $ticket_number, $user_id = '')
    {
        $this->log->info("AfterCheckinPaymentDetails " . json_encode($request->all()));
        $this->log->info("AfterCheckinPaymentDetails TICKET NUMBER {$ticket_number}");

        $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        if (!$secret) {
            throw new ApiGenericException('Invalid partner.');
        }
        $checkinData = Ticket::with('user')->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();

        if (count($checkinData) == 0) {
            throw new ApiGenericException("No recent ticket is found for this user.");
        }
        if (($user_id != '') && ($checkinData->user_id != $user_id)) {
            throw new ApiGenericException("No recent ticket is found for this user.");
        }
        if ($checkinData->anet_transaction_id != '') {
            $checkinData['payment'] = 1;
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));


        $diff_in_hours = $arrival_time->diffInHours($from);
        $diff_in_mins = $arrival_time->diffInMinutes($from);
        $diff_in_secs = $arrival_time->diffInSeconds($from);

        if ($diff_in_mins < 60) {
            $diff_in_hours = $diff_in_mins / 100;
        }
        if ($diff_in_mins > 59) {
            if ($diff_in_hours > 0) {
                $diffInMints = $diff_in_mins - ($diff_in_hours * 60);
                $diff_in_hours = $diff_in_hours . '.' . $diffInMints;
            } else {
                $diff_in_hours = number_format($diff_in_mins / 60, 2);
            }
        }
        if ($diff_in_secs < 60) {
            $diff_in_hours = .01;
        }

        $checkinData['length'] = $diff_in_hours;
        $facility = Facility::find($checkinData->facility_id);

        $rate = [];
        $isMember = 0;
        $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
        if ($rate == false) {
            throw new ApiGenericException("Rate not found.");
        }
        $taxRate = $checkinData->getTaxRate($rate);          // to get tax price
        $processingFee = $checkinData->getProcessingFee($checkinData); // to get prcessing free channel wise need to
        $ticketPrice = $rate['price'] + $processingFee + $taxRate;
        $this->log->info("PRINT HOURS {$diff_in_hours} AND RATE {$rate['price']} AND TICKET PRICE {$ticketPrice}");
        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();
        if ($overstay) {
            $checkinData['overstay_amount'] = $overstay->total;
        } else {
            $checkinData['overstay_amount'] = '0';
        }

        $checkinData['DATACAP_SCRIPT_URL'] = config('parkengage.DATACAP_SCRIPT_URL');
        $checkinData['DATACAP_TOKEN'] = $checkinData->facility->ecommerce_token;
        $checkinData['DATACAP_OTU_URL'] = config('parkengage.DATACAP_OTU_URL');



        if ($rate['price'] == 'N/A') {
            $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            if ($rate['price'] == "0.00" || $rate['price'] == 0) {
                $rate['price'] = 0.00;
            } else {
                if ($checkinData->paid_by != '') {
                    // Full  Amount Validate
                    if ($checkinData->paid_type == '0' || $checkinData->paid_type == 0) {
                        $checkinData['payment'] = 1;
                        if ($rate['price'] > "0") {
                            $updatedPaidAmount = $rate['price'];
                        }
                        $rate['price'] = "0.00";
                        $rate['facility'] = $checkinData->facility;
                        $checkinData['rate'] = $rate;
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);

                        if ($updatedPaidAmount <= "0") {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                            $checkinData['overstay_amount'] = "0.00";
                            $checkinData['paid_amount'] = $updatedPaidAmount;
                        } else {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = number_format($updatedPaidAmount, 2);
                            $checkinData['paid_amount'] = $updatedPaidAmount + $checkinData->facility->processing_fee;
                        }
                        return $checkinData;
                    }

                    // Hour validate
                    if ($checkinData->paid_type == '1' || $checkinData->paid_type == 1) {
                        if ($checkinData->paid_hour != '') {
                            $checkinData['payment'] = 0;
                            $duration_in_hou = $diff_in_hours - $checkinData->paid_hour;
                            $duration = $this->getEndTime($duration_in_hou);
                            $duration_in_min1 = $this->getEndTime($diff_in_hours);
                            $total_amount =  $rate['price'];
                            $paid_amount = "0.00";
                            if ($duration == 0) {
                                $checkinData['payment'] = 1;
                                $rate['price'] = "0";
                                $updatedPaidAmount = "0";
                                $checkinData->paid_amount = $rate['price'];
                            } else {
                                $paid_amount = ((($total_amount) / $duration_in_min1) * $duration);
                                $paid_amount = round($paid_amount);
                                $checkinData->paid_amount = $paid_amount;
                            }

                            if ($rate['price'] <= $paid_amount) {
                                $checkinData['payment'] = 1;
                                $rate['price'] = "0";
                                $rate['facility'] = $checkinData->facility;
                                $checkinData['rate'] = $rate;
                                $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                $checkinData['payable_amount'] = "0.00";
                                $checkinData['processing_fee'] = "0.00";
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = "0.00";
                            } else {
                                $updatedPaidAmount = $rate['price'];
                                $payable_amount = $updatedPaidAmount - $paid_amount;
                                if ($checkinData->grand_total > "0") {
                                    $payable_amount = $payable_amount - $checkinData->grand_total;
                                }
                                if ($payable_amount > "0") {
                                    $payable_amount = $payable_amount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                } else {
                                    $payable_amount = "0.00";
                                    $processing_fee = "0.00";
                                    $checkinData['payment'] = 1;
                                }

                                $checkinData['rate'] = $rate;
                                $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                $checkinData['processing_fee'] = $processing_fee;
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = $updatedPaidAmount;
                            }
                            return $checkinData;
                        }
                    }

                    //paid amount
                    if ($checkinData->paid_type == '2' || $checkinData->paid_type == 2) {

                        $checkinData['payment'] = 0;
                        $rate['price'] = $rate['price'];
                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                            $checkinData['payment'] = 1;
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                        } else {
                            if ($checkinData->paid_amount != '') {
                                $updatedPaidAmount = $rate['price'];
                                $payable_amount = $updatedPaidAmount - $checkinData->paid_amount;
                                if ($checkinData->grand_total > "0") {
                                    $payable_amount = $payable_amount - $checkinData->grand_total;
                                }
                                if ($payable_amount >= "0") {
                                    $payable_amount = $payable_amount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                } else if ($payable_amount <= $checkinData->paid_amount) {
                                    $checkinData->paid_amount = $updatedPaidAmount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                    $payable_amount = "0.00";
                                    $checkinData['payment'] = 1;
                                } else {
                                    $payable_amount = "0.00";
                                    $processing_fee = "0.00";
                                    $checkinData['payment'] = 1;
                                }
                                $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                $checkinData['processing_fee'] = $processing_fee;
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = $updatedPaidAmount;
                            }
                            $checkinData['rate'] = $rate;
                        }
                        return $checkinData;
                    }

                    // paid percentage
                    if ($checkinData->paid_type == '3' || $checkinData->paid_type == 3) {
                        $checkinData['payment'] = 0;

                        $total_amount =  $rate['price'];
                        if ($total_amount > "0.00") {
                            $percentageAmount = (($total_amount * $checkinData->paid_percentage) / 100);
                        } else {
                            $total_amount = "0.00";
                            $percentageAmount = "0.00";
                        }

                        $rate['price'] = number_format($rate['price'], 2);

                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {

                            $checkinData['payment'] = 1;
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                        } else {
                            if ($percentageAmount > '0.00') {
                                $updatedPaidAmount = $rate['price'];
                                $payable_amount = $updatedPaidAmount - $percentageAmount;
                                if ($checkinData->grand_total > "0") {
                                    $payable_amount = $payable_amount - $checkinData->grand_total;
                                }
                                if ($payable_amount > "0") {
                                    $payable_amount = $payable_amount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                } else {
                                    $payable_amount = "0.00";
                                    $processing_fee = "0.00";
                                    $checkinData['payment'] = 1;
                                }
                                $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                $checkinData['processing_fee'] = $processing_fee;
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = $updatedPaidAmount;
                            }
                            $checkinData['rate'] = $rate;
                        }
                        return $checkinData;
                    }
                }
                $rate['price'] =  $rate['price'];
            }
        } else {
            if ($rate['price'] == "0.00" || $rate['price'] == 0) {
                $rate['price'] = 0.00;
            } else {

                if ($checkinData->paid_by != '') {
                    if ($checkinData->paid_type != 9) {
                        $validationData = $this->populateValidationFields($checkinData, $rate, $checkinData->facility);
                        $this->log->info("populateValidationFields " . json_encode($validationData));
                    }

                    // Full  Amount Validate
                    if ($checkinData->paid_type == '0' || $checkinData->paid_type == 0) {
                        $checkinData['payment'] = 1;
                        if ($rate['price'] > "0") {
                            $updatedPaidAmount = $rate['price'];
                        }
                        $rate['price'] = "0.00";
                        $rate['facility'] = $checkinData->facility;
                        $checkinData['rate'] = $rate;
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);


                        if ($updatedPaidAmount <= "0") {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                            $checkinData['overstay_amount'] = "0.00";
                            $checkinData['paid_amount'] = $updatedPaidAmount;
                        } else {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = number_format($updatedPaidAmount, 2);
                            $checkinData['paid_amount'] = $updatedPaidAmount + $checkinData->facility->processing_fee;
                        }
                        return $checkinData;
                    }

                    // Hour validate
                    if ($checkinData->paid_type == '1' || $checkinData->paid_type == 1) {
                        if ($checkinData->paid_hour != '') {
                            $checkinData['payment'] = 0;
                            $isMember = 0;
                            // change in hours 
                            // $duration_in_hou = $diff_in_hours - $checkinData->paid_hour;
                            // $duration = $this->getEndTime($duration_in_hou);
                            // $duration_in_min1 = $this->getEndTime($diff_in_hours);
                            // $total_amount =  $rate['price'];
                            // $paid_amount = "0.00";
                            // if ($duration == 0) {
                            //     $checkinData['payment'] = 1;
                            //     $rate['price'] = "0";
                            //     $updatedPaidAmount = "0";
                            //     $checkinData->paid_amount = $rate['price'];
                            // } else {
                            //     $paid_amount = ((($total_amount) / $duration_in_min1) * $duration);
                            //     $paid_amount = round($paid_amount);
                            //     $checkinData->paid_amount = $paid_amount;
                            // }

                            $diff_in_hours = $diff_in_hours - $checkinData->paid_hour;
                            $discountRate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                            $total_amount =  $discountRate['price'];
                            $paid_amount = "0.00";
                            if ($rate['price'] <= $paid_amount) {
                                $checkinData['payment'] = 1;
                                $rate['price'] = "0";
                                $rate['facility'] = $checkinData->facility;
                                $checkinData['rate'] = $rate;
                                $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                $checkinData['payable_amount'] = "0.00";
                                $checkinData['processing_fee'] = "0.00";
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = "0.00";
                                $checkinData['payment'] = 1;
                            } else {
                                // $updatedPaidAmount = $rate['price'];
                                // $payable_amount = $updatedPaidAmount - $paid_amount;
                                $updatedPaidAmount = $rate['price'] - $discountRate['price'];
                                $payable_amount = $discountRate['price'];

                                if ($checkinData->grand_total > "0") {
                                    $payable_amount = $payable_amount - $checkinData->grand_total;
                                }
                                if ($payable_amount >= "0") {
                                    $payable_amount = $payable_amount + $processingFee;
                                    $processing_fee = $processingFee;
                                } else {
                                    $payable_amount = "0.00";
                                    $processing_fee = $processingFee;
                                    $checkinData['payment'] = 1;
                                }

                                $checkinData['rate'] = $rate;
                                $checkinData['paid_amount'] = $validationData['paid_amount'];
                                $checkinData['total'] = $validationData['total'];
                                $checkinData['amount_paid'] = $checkinData->grand_total;
                                $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                $checkinData['processing_fee'] = $processing_fee;
                                $checkinData['tax_rate'] = $taxRate;
                                $checkinData['parking_amount'] = number_format($rate['price'], 2);
                            }
                            return $checkinData;
                        }
                    }

                    //paid amount
                    if ($checkinData->paid_type == '2' || $checkinData->paid_type == 2) {

                        $checkinData['payment'] = 0;
                        $rate['price'] = $rate['price'];
                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                            $checkinData['payment'] = 1;
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                        } else {
                            if ($checkinData->paid_amount != '') {
                                $updatedPaidAmount = $rate['price'];
                                $payable_amount = $updatedPaidAmount - $checkinData->paid_amount;
                                if ($checkinData->grand_total > "0") {
                                    $payable_amount = $payable_amount - $checkinData->grand_total;
                                }
                                if ($payable_amount >= "0") {
                                    $payable_amount = $payable_amount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                } else if ($payable_amount <= $checkinData->paid_amount) {
                                    $checkinData->paid_amount = $updatedPaidAmount + $checkinData->facility->processing_fee;
                                    $processing_fee = $checkinData->facility->processing_fee;
                                    $payable_amount = "0.00";
                                    $checkinData['payment'] = 1;
                                } else {
                                    $checkinData['payment'] = 1;
                                    $payable_amount = "0.00";
                                    $processing_fee = "0.00";
                                }
                                $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                $checkinData['processing_fee'] = $processing_fee;
                                $checkinData['tax_rate'] = "0.00";
                                $checkinData['parking_amount'] = $updatedPaidAmount;
                            }
                            $checkinData['rate'] = $rate;
                        }
                        return $checkinData;
                    }

                    // paid percentage
                    if ($checkinData->paid_type == '3' || $checkinData->paid_type == 3) {
                        $checkinData['payment'] = 0;
                        $total_amount =  $rate['price'] + $processingFee + $taxRate;

                        if ($total_amount > "0.00") {
                            $percentageAmount = (($total_amount * $checkinData->paid_percentage) / 100);

                            if (($checkinData->max_validated_amount > 0) && $checkinData->max_validated_amount != '') {
                                if ($percentageAmount <= $checkinData->max_validated_amount) {
                                    $payableAmount = number_format($total_amount - $percentageAmount, 2);
                                    $updatedPaidAmount = $percentageAmount;
                                } else {
                                    $payableAmount = number_format($total_amount - $checkinData->max_validated_amount, 2);
                                    $updatedPaidAmount = $checkinData->max_validated_amount;
                                }
                            } else {
                                $payableAmount = number_format($total_amount - $percentageAmount, 2);
                                $updatedPaidAmount = $percentageAmount;
                            }
                        } else {
                            $total_amount = "0.00";
                            $percentageAmount = "0.00";
                        }
                        // dd($total_amount, $checkinData->paid_percentage, $percentageAmount);
                        // dd($rate['price'], $total_amount, $percentageAmount, $payableAmount, $updatedPaidAmount);
                        $rate['price'] = number_format($rate['price'], 2);

                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {

                            $checkinData['payment'] = 1;
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                        } else {
                            if ($percentageAmount > '0.00') {
                                $updatedPaidAmount = $rate['price'];
                                $parkingAmount = $rate['price'];
                                // $payable_amount = $updatedPaidAmount - $percentageAmount;
                                if ($checkinData->grand_total) {
                                }
                                $payable_amount = $checkinData->grand_total > 0 ? $payableAmount - $checkinData->grand_total : $payableAmount;
                                if ($payable_amount > "0") {
                                    $payable_amount = $payable_amount;
                                    $processing_fee = $processingFee;
                                } else {
                                    $payable_amount = "0.00";
                                    $processing_fee = "0.00";
                                    $checkinData['payment'] = 1;
                                }
                                $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                $checkinData['processing_fee'] = $processing_fee;
                                $checkinData['tax_rate'] = $taxRate;
                                $checkinData['parking_amount'] = $parkingAmount;
                            }
                            $checkinData['rate'] = $rate;
                        }
                        // dd("LQ", $checkinData['payable_amount'], $checkinData['parking_amount']);
                        return $checkinData;
                    }
                }
                $rate['price'] =  $rate['price'];
            }
        }
        $checkinData['rate'] = $rate;
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
        $this->log->info("user is on payment section click on checkout button on checkin screen with ticket number {$checkinData->ticket_number}");

        $tax_rate = $checkinData->facility->tax_rate;
        if ($tax_rate > 0) {
            $tax_rate = number_format((($rate['price'] * $checkinData->facility->tax_rate) / 100), 2);
        }
        if ($rate['price'] <= "0") {
            $checkinData['payable_amount'] = "0.00";
            $checkinData['processing_fee'] = "0.00";
            $checkinData['tax_rate'] = "0.00";
            $checkinData['parking_amount'] = "0.00";
            $checkinData['payment'] = 1;
        } else {
            $checkinData['payable_amount'] = number_format($rate['price'] + $checkinData->facility->processing_fee + $tax_rate - $checkinData->grand_total - $checkinData->paid_amount, 2);
            $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
            $checkinData['tax_rate'] = $tax_rate;
            $checkinData['parking_amount'] = $rate['price'];
            if ($rate['price'] <= "0") {
                $checkinData['payable_amount'] = "0.00";
                $checkinData['processing_fee'] = "0.00";
                $checkinData['tax_rate'] = "0.00";
                $checkinData['payment'] = 1;
            }
        }
        $this->log->info("Before Response " . json_encode($checkinData));
        return $checkinData;
    }


    public function MakeTicketPayment_backup(Request $request)  // 3rd to make payment
    {
        $this->log->info("Received request for payment : " . json_encode($request->all()));
        $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        if (!$secret) {
            throw new ApiGenericException('Invalid partner.');
        }
        $this->secret = $secret;
        $this->checkFacilityAvailable($this->request->facility_id);
        //  $this->setDecryptedCard($request);

        //$this->validate($this->request, $this->billingValidation);

        $this->setCustomTimezone($this->request->facility_id);

        $checkinData = Ticket::with(['facility', 'user'])->where('ticket_number', $this->request->ticket_number)->where('is_checkin', '1')->where('is_checkout', '0')->first();

        if (!$checkinData) {
            throw new ApiGenericException("Invalid ticket detials.");
        }

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));


        $diff_in_hours = $arrival_time->diffInHours($from);
        $diff_in_mins = $arrival_time->diffInMinutes($from);
        $diff_in_secs = $arrival_time->diffInSeconds($from);

        //dd($diff_in_hours,$diff_in_mins,$diff_in_secs);
        if ($diff_in_mins < 60) {
            $diff_in_hours = $diff_in_mins / 100;
        }
        if ($diff_in_mins > 59) {
            $diff_in_hours = number_format($diff_in_mins / 60, 2);
        }
        if ($diff_in_secs < 60) {
            $diff_in_hours = .01;
        }

        $facility = Facility::find($checkinData->facility_id);
        $this->facility = $facility;

        $rate = [];
        $isMember = 0;
        $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
        if ($rate == false) {
            throw new ApiGenericException("Rate not found.");
        }
        $this->rate =  $rate;
        if ($rate['price'] == 'N/A') {
            $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            if ($rate['price'] == "0.00" || $rate['price'] == 0) {
                $rate['price'] = 0.00;
            } else {

                if ($checkinData->paid_by != '') {
                    // Full  Amount Validate
                    if ($checkinData->paid_type == '0' || $checkinData->paid_type == 0) {
                        $checkinData['payment'] = 1;
                        if ($rate['price'] > "0") {
                            $updatedPaidAmount = $rate['price'];
                        }
                        $rate['price'] = "0.00";
                        $rate['facility'] = $checkinData->facility;
                        $checkinData['rate'] = $rate;
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);


                        if ($updatedPaidAmount <= "0") {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                            $checkinData['overstay_amount'] = "0.00";
                            $checkinData['paid_amount'] = $updatedPaidAmount;
                            $checkinData['payment'] = 1;
                        } else {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = number_format($updatedPaidAmount, 2);
                            $checkinData['paid_amount'] = $updatedPaidAmount + $checkinData->facility->processing_fee;
                        }
                        return $checkinData;
                    }

                    if ($checkinData->paid_type == '1' || $checkinData->paid_type == 1) {
                        if ($checkinData->paid_hour != '') {
                            //$diff_in_hours = $diff_in_hours - $checkinData->paid_hour;
                            if ($rate['price'] <= $checkinData->paid_amount) {
                                $checkinData['payment'] = 1;
                                $rate['price'] = "0";
                                $rate['facility'] = $checkinData->facility;
                                $checkinData['rate'] = $rate;
                                $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                return $checkinData;
                            }
                            $rate['price'] = $rate['price'];
                        }
                    }


                    if ($checkinData->paid_type == '2' || $checkinData->paid_type == 2) {
                        $checkinData['payment'] = 1;
                        $rate['price'] = $rate['price'];
                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            return $checkinData;
                        }
                    }
                    if ($checkinData->paid_type == '3' || $checkinData->paid_type == 3) {
                        $checkinData['payment'] = 1;

                        $percentageAmount = (($rate['price'] * $checkinData->paid_percentage) / 100);
                        if ($percentageAmount <= $checkinData->paid_amount) {
                            $rate['price'] = number_format($rate['price'], 2);
                        } else {
                            $rate['price'] = number_format($rate['price'], 2);
                        }

                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            return $checkinData;
                        }
                    }
                }

                $rate['price'] =  $rate['price'];
            }
        } else {
            if ($rate['price'] == "0.00" || $rate['price'] == 0) {
                $rate['price'] = 0.00;
            } else {
                if ($checkinData->paid_by != '') {
                    // Full  Amount Validate
                    if ($checkinData->paid_type == '0' || $checkinData->paid_type == 0) {
                        $checkinData['payment'] = 1;
                        if ($rate['price'] > "0") {
                            $updatedPaidAmount = $rate['price'];
                        }
                        $rate['price'] = "0.00";
                        $rate['facility'] = $checkinData->facility;
                        $checkinData['rate'] = $rate;
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);

                        if ($updatedPaidAmount <= "0") {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = "0.00";
                            $checkinData['overstay_amount'] = "0.00";
                            $checkinData['paid_amount'] = $updatedPaidAmount;
                        } else {
                            $checkinData['payable_amount'] = "0.00";
                            $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
                            $checkinData['tax_rate'] = "0.00";
                            $checkinData['parking_amount'] = number_format($updatedPaidAmount, 2);
                            $checkinData['paid_amount'] = $updatedPaidAmount + $checkinData->facility->processing_fee;
                        }
                        return $checkinData;
                    }

                    if ($checkinData->paid_type == '1' || $checkinData->paid_type == 1) {
                        if ($checkinData->paid_hour != '') {
                            //$diff_in_hours = $diff_in_hours - $checkinData->paid_hour;
                            if ($rate['price'] <= $checkinData->paid_amount) {
                                $checkinData['payment'] = 1;
                                $rate['price'] = "0";
                                $rate['facility'] = $checkinData->facility;
                                $checkinData['rate'] = $rate;
                                $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                return $checkinData;
                            }
                            $rate['price'] = $rate['price'];
                        }
                    }


                    if ($checkinData->paid_type == '2' || $checkinData->paid_type == 2) {
                        $checkinData['payment'] = 1;
                        $rate['price'] = $rate['price'];
                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            return $checkinData;
                        }
                    }
                    if ($checkinData->paid_type == '3' || $checkinData->paid_type == 3) {
                        $checkinData['payment'] = 1;

                        $percentageAmount = (($rate['price'] * $checkinData->paid_percentage) / 100);
                        if ($percentageAmount <= $checkinData->paid_amount) {
                            $rate['price'] = number_format($rate['price'], 2);
                        } else {
                            $rate['price'] = number_format($rate['price'], 2);
                        }

                        if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                            $rate['price'] = "0";
                            $rate['facility'] = $checkinData->facility;
                            $checkinData['rate'] = $rate;
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                            return $checkinData;
                        }
                    }
                }
                $rate['price'] =  $rate['price'];
            }
        }
        $this->user = $checkinData->user;
        if (($checkinData->anet_transaction_id != '') && ($request->total > 0)) {
            $paymentStatus = $this->makePlanetPayment($this->request, $checkinData);
            //$paymentStatus = $this->makeDataCapPayment($this->request);

            if (isset($paymentStatus['error']) && $paymentStatus['error'] != '') {
                throw new ApiGenericException("Payment failed! Please enter valid card details.");
            }
            /*
            if (isset($paymentStatus) && $paymentStatus == "Payment Failed.") {
                throw new ApiGenericException("Payment failed! Please enter valid card details.");
            }
			*/
            // set overStay Flag 
            $request->request->add(['is_overstay' => '1']);
            $this->paymentStatus = $paymentStatus;
            $ticket = $this->saveOverstayTicket();
            $transaction = $this->saveTransactionData();
            $transaction->overstay_ticket_id = $ticket->id;
            $transaction->save();
            Artisan::queue('email:townsend-checkin-payment', array('id' => $ticket->id, 'type' => 'overstay'));
            return $ticket;
        }
        //dd($rate['price'],$request->rate_price);
        if ((float) $rate['price'] != $request->rate_price) {
            throw new ApiGenericException('Sent rate does not match database rate, please reset parking options and try again.');
        }

        $total = $rate['price'];
        //$this->facility = Facility::find($this->request->facility_id);

        if ($this->user) {

            $this->user->name = isset($this->request->name_on_card) ? $this->request->name_on_card : '';
            //$this->user->email = $this->request->email;
            $this->user->save();
        } else {

            throw new ApiGenericException("Payment failed! Invalid user.");
        }
        if ($total > 0) {
            try {
                //$paymentStatus = $this->makeDataCapPayment($this->request);
                $paymentStatus = $this->makePlanetPayment($this->request, $checkinData);
                //dd($paymentStatus,$this->request->all());

                if (isset($paymentStatus['error']) && $paymentStatus['error'] != '') {
                    throw new ApiGenericException("Payment failed! Please enter valid card details.");
                }
                /*
                if (isset($paymentStatus) && $paymentStatus == "Payment Failed.") {
                    throw new ApiGenericException("Payment failed! Please enter valid card details.");
                }
				*/
                $ticket = $this->saveTicket();

                $ticket->anet_transaction_id = $paymentStatus->id;
                if (isset($paymentStatus->id)) {
                    $ticket->card_type = $paymentStatus->card_type;
                    $ticket->card_last_four = $paymentStatus->payment_last_four;
                    $ticket->expiry = $paymentStatus->expiration;
                }
                $ticket->save();
                $transaction = $this->saveTransactionData();
                if ($this->request->email != '') {
                    Artisan::queue('email:townsend-checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));
                }
                return $ticket;
            } catch (Exception $e) {
                $this->log->error($e);
                throw new ApiGenericException("Payment error!");
                //$this->log->error("System exception -". $e->getMessage());
            }
        }
    }

    // 4th to scan QR code to checkout from facility
    public function ScanedTicketDetails_backup(Request $request)
    {
        $this->log->info("Scaned Ticket Received request for payment : " . json_encode($request->all()));
        $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        if (!$secret) {
            throw new ApiGenericException('Invalid partner.');
        }
        try {

            if ($request->encrypt) {
                $explode = explode(':', $request->encrypt);
                if (count($explode) < 5) {
                    if (isset($explode[3])) {
                        $exp = explode(".", $explode[3]);
                        $decrypt = base64_decode(trim($exp[0]));
                    } else {
                        return back()->with('danger', "Please scan the right QR code.");
                    }
                } else {
                    $decrypt = base64_decode($explode[4]);
                }

                if ($decrypt) {
                    $decrytArray = json_decode($decrypt);
                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();


                    $this->setCustomTimezone($decrytArray->facility_id);

                    $checkinData = [];
                    if ($gate && isset($gate->gate_type)) {
                        if ($gate->gate_type == 'entry') {
                            throw new ApiGenericException('Please scan the right QR code.');
                        }
                        $is_checkout = Ticket::where('ticket_number', $request->order_number)->where('is_checkout', '1')->first();
                        if ($is_checkout) {
                            throw new ApiGenericException('You have already checked-Out.');
                        }

                        //$this->checkFacilityAvailable($decrytArray->facility_id);

                        $checkinData = Ticket::with(['user', 'facility'])->where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                        if ($checkinData) {

                            if ($checkinData->facility->check_vehicle_enabled == '1') {
                                $gateStatus = $this->isParkEngageGateAvailable($decrytArray->facility_id, $decrytArray->gate, $request['From']);
                                if ($gateStatus === false) {
                                    return true;
                                }
                            }

                            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                            $diff_in_hours = $checkinData->getCheckOutCurrentTime(true);
                            $isMember = 0;
                            $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);

                            if ($checkinData->anet_transaction_id == '' && $checkinData->paid_by == '') {

                                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

                                $diff_in_hours = $arrival_time->diffInHours($from);
                                $diff_in_mins = $arrival_time->diffInMinutes($from);
                                $diff_in_secs = $arrival_time->diffInSeconds($from);

                                if ($diff_in_mins < 60) {
                                    $diff_in_hours = $diff_in_mins / 100;
                                }
                                if ($diff_in_mins > 59) {
                                    $diff_in_hours = number_format($diff_in_mins / 60, 2);
                                }
                                if ($diff_in_secs < 60) {
                                    $diff_in_hours = .01;
                                }

                                $isMember = 0;
                                $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                                if ($rate == false) {
                                    throw new ApiGenericException('Garage is currently closed.');
                                }
                                $tax_rate = $checkinData->facility->tax_rate;
                                $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();

                                if ($rate['price'] == 'N/A') {
                                    $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
                                    if ($rate['price'] == 0.00 || $rate['price'] == "0.00") {
                                    } else {
                                        if ($tax_rate > 0) {
                                            $tax_rate = number_format((($rate['price'] * $checkinData->facility->tax_rate) / 100), 2);
                                        }
                                        if ($overstay) {
                                            $checkinData['overstay_amount'] = $overstay->total;
                                        } else {
                                            $checkinData['overstay_amount'] = '0';
                                        }
                                        if ($rate['price'] <= "0") {
                                            $checkinData['payable_amount'] = "0.00";
                                            $checkinData['processing_fee'] = "0.00";
                                            $checkinData['tax_rate'] = "0.00";
                                        } else {
                                            $checkinData['payable_amount'] = number_format($rate['price'] + $checkinData->facility->processing_fee + $tax_rate - $checkinData->grand_total - $checkinData->paid_amount, 2);
                                            $checkinData['processing_fee'] = $checkinData['processing_fee'];
                                            $checkinData['tax_rate'] = $tax_rate;
                                            if ($rate['price'] <= "0") {
                                                $checkinData['payable_amount'] = "0.00";
                                                $checkinData['processing_fee'] = "0.00";
                                                $checkinData['tax_rate'] = "0.00";
                                            }
                                        }
                                        $checkinData['parking_amount'] = ($checkinData['total'] + $checkinData['paid_amount']) - $checkinData['processing_fee'] - $checkinData['tax_fee'];

                                        $checkinData['payment'] = 0;
                                        $checkinData['rate'] = $rate;
                                        return $checkinData;
                                    }
                                } else {
                                    if ($rate['price'] > 0.00) {
                                        if ($overstay) {
                                            $checkinData['overstay_amount'] = $overstay->total;
                                        } else {
                                            $checkinData['overstay_amount'] = '0';
                                        }
                                        if ($rate['price'] <= "0") {
                                            $checkinData['payable_amount'] = "0.00";
                                            $checkinData['processing_fee'] = "0.00";
                                            $checkinData['tax_rate'] = "0.00";
                                        } else {
                                            $checkinData['payable_amount'] = number_format($rate['price'] + $checkinData->facility->processing_fee + $tax_rate - $checkinData->grand_total - $checkinData->paid_amount, 2);
                                            $checkinData['processing_fee'] = $checkinData['processing_fee'];
                                            $checkinData['tax_rate'] = $tax_rate;
                                            if ($rate['price'] <= "0") {
                                                $checkinData['payable_amount'] = "0.00";
                                                $checkinData['processing_fee'] = "0.00";
                                                $checkinData['tax_rate'] = "0.00";
                                            }
                                        }
                                        $checkinData['parking_amount'] = ($checkinData['total'] + $checkinData['paid_amount']) - $checkinData['processing_fee'] - $checkinData['tax_fee'];

                                        $checkinData['payment'] = 0;
                                        $checkinData['rate'] = $rate;
                                        return $checkinData;
                                    }
                                }
                            } else if ($checkinData->paid_by != '') {

                                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

                                // $diff_in_hours = $arrival_time->diffInHours($from);
                                // $diff_in_mins = $arrival_time->diffInMinutes($from);
                                // $diff_in_secs = $arrival_time->diffInSeconds($from);

                                // if ($diff_in_mins < 60) {
                                //     $diff_in_hours = $diff_in_mins / 100;
                                // }
                                // if ($diff_in_mins > 59) {
                                //     $diff_in_hours = number_format($diff_in_mins / 60, 2);
                                // }
                                // if ($diff_in_secs < 60) {
                                //     $diff_in_hours = .01;
                                // }
                                $diff_in_hours = $checkinData->getCheckOutCurrentTime(true);
                                $isMember = 0;
                                $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);

                                if ($rate == false) {
                                    throw new ApiGenericException('Garage is currently closed.');
                                }

                                $processingFee = $checkinData->getProcessingFee();   // to get prcessing free channel wise need to
                                $taxRate = $checkinData->getTaxRate($rate);          // to get tax price
                                $ticketPrice = $rate['price'] + $processingFee + $taxRate;
                                $parkingAmount = $rate['price'];

                                if ($checkinData->paid_type != '9') {
                                    $validationData = $this->populateValidationFields($checkinData, $rate, $checkinData->facility);
                                }
                                // Full  Amount Validate
                                if ($checkinData->paid_type == '0' || $checkinData->paid_type == 0) {
                                    $checkinData['payment'] = 1;
                                    if ($rate['price'] > "0") {
                                        $updatedPaidAmount = $rate['price'];
                                    } else {
                                        $updatedPaidAmount = "0.00";
                                    }
                                    $rate['price'] = "0.00";
                                    $rate['facility'] = $checkinData->facility;
                                    $checkinData['rate'] = $rate;
                                    $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                    $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                    $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                    $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                    $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);


                                    $ticketData = Ticket::where('id', $checkinData->id)->first();
                                    if ($updatedPaidAmount > "0") {
                                        $ticketData->paid_amount = $updatedPaidAmount + $processingFee;
                                        $ticketData->processing_fee = $parkingAmount > 0 ? $processingFee : '0.00';
                                        // Vijay Added : 27-07-2023
                                        $ticketData->total = isset($validationData) ? $validationData['total'] : $ticketPrice;
                                        $ticketData->length = $diff_in_hours;
                                        $ticketData->grand_total = '0.00';
                                        $ticketData->parking_amount = $parkingAmount;
                                        // Vijay Added : 27-07-2023
                                        $ticketData->save();
                                    } else {
                                        $ticketData->paid_amount = '0.00';
                                        $ticketData->processing_fee = '0.00';
                                        // Vijay Added : 27-07-2023
                                        $ticketData->total = '0.00';
                                        $ticketData->length = $diff_in_hours;
                                        $ticketData->grand_total = '0.00';
                                        $ticketData->parking_amount = '0.00';
                                        // Vijay Added : 27-07-2023
                                        $ticketData->save();
                                    }
                                }

                                // Hour validate
                                if ($checkinData->paid_type == '1' || $checkinData->paid_type == 1) {
                                    if ($checkinData->paid_hour != '') {
                                        $checkinData['payment'] = 0;
                                        $duration_in_hou = $diff_in_hours - $checkinData->paid_hour;
                                        $duration = $this->getEndTime($duration_in_hou);
                                        $duration_in_min1 = $this->getEndTime($diff_in_hours);
                                        $total_amount =  $rate['price'];
                                        $paid_amount = "0.00";
                                        if ($duration == 0) {
                                            $rate['price'] = "0";
                                            $updatedPaidAmount = "0";
                                            $checkinData->paid_amount = $rate['price'];
                                        } else {
                                            $paid_amount = ((($total_amount) / $duration_in_min1) * $duration);
                                            $paid_amount = round($paid_amount);
                                            $checkinData->paid_amount = $paid_amount;
                                        }

                                        if ($rate['price'] <= $paid_amount) {
                                            $checkinData['payment'] = 1;
                                            $rate['price'] = "0";
                                            $rate['facility'] = $checkinData->facility;
                                            $checkinData['rate'] = $rate;
                                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                            $checkinData['payable_amount'] = "0.00";
                                            $checkinData['processing_fee'] = "0.00";
                                            $checkinData['tax_rate'] = "0.00";
                                            $checkinData['parking_amount'] = "0.00";
                                        } else {
                                            $updatedPaidAmount = $rate['price'];
                                            $payable_amount = $updatedPaidAmount - $paid_amount;
                                            if ($checkinData->grand_total > "0") {
                                                $payable_amount = $payable_amount - $checkinData->grand_total;
                                            }
                                            if ($payable_amount > "0") {
                                                $payable_amount = $payable_amount + $processingFee;
                                                $processing_fee = $processingFee;
                                            } else {
                                                $payable_amount = "0.00";
                                                $processing_fee = "0.00";
                                                $checkinData['payment'] = 1;
                                            }
                                            if ($updatedPaidAmount > "0") {
                                                $ticketData = Ticket::where('id', $checkinData->id)->first();
                                                $ticketData->paid_amount =  $validationData['paid_amount'];;
                                                $ticketData->processing_fee = $processingFee;
                                                $ticketData->save();
                                            }
                                            $checkinData['rate'] = $rate;
                                            $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                            $checkinData['processing_fee'] = $processing_fee;
                                            $checkinData['tax_rate'] = "0.00";
                                            $checkinData['parking_amount'] = $updatedPaidAmount;
                                        }
                                        //return $checkinData;
                                    }
                                }

                                //paid amount
                                if ($checkinData->paid_type == '2' || $checkinData->paid_type == 2) {

                                    $checkinData['payment'] = 0;
                                    $rate['price'] = $rate['price'];
                                    if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
                                        $checkinData['payment'] = 1;
                                        $rate['price'] = "0";
                                        $rate['facility'] = $checkinData->facility;
                                        $checkinData['rate'] = $rate;
                                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                        $checkinData['payable_amount'] = "0.00";
                                        $checkinData['processing_fee'] = "0.00";
                                        $checkinData['tax_rate'] = "0.00";
                                        $checkinData['parking_amount'] = "0.00";
                                    } else {
                                        if ($checkinData->paid_amount != '') {
                                            $updatedPaidAmount = $rate['price'];
                                            $payable_amount = $updatedPaidAmount - $checkinData->paid_amount;
                                            if ($checkinData->grand_total > "0") {
                                                $payable_amount = $payable_amount - $checkinData->grand_total;
                                            }
                                            if ($payable_amount >= "0") {
                                                $payable_amount = $payable_amount + $processingFee;
                                                $processing_fee = $processingFee;
                                            } else if ($payable_amount <= $checkinData->paid_amount) {
                                                $checkinData->paid_amount = $updatedPaidAmount + $processingFee;
                                                $processing_fee = $processingFee;
                                                $payable_amount = "0.00";
                                                $checkinData['payment'] = 1;
                                            } else {
                                                $payable_amount = "0.00";
                                                $processing_fee = "0.00";
                                                $checkinData['payment'] = 1;
                                            }
                                            $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                            $checkinData['processing_fee'] = $processingFee;
                                            $checkinData['tax_rate'] = $taxRate;
                                            $checkinData['parking_amount'] = $updatedPaidAmount;
                                            if ($updatedPaidAmount > "0") {
                                                $ticketData = Ticket::where('id', $checkinData->id)->first();
                                                // $ticketData->paid_amount = $updatedPaidAmount + $processingFee; // not clear why we update this again
                                                $ticketData->processing_fee = $processingFee;
                                                // Vijay Added : 27-07-2023
                                                $ticketData->parking_amount = $parkingAmount;
                                                $ticketData->total = $ticketPrice;
                                                $ticketData->length = $diff_in_hours;
                                                $ticketData->grand_total = isset($validationData) ? $validationData['grand_total'] : number_format($payable_amount, 2);
                                                // Vijay Added : 27-07-2023
                                                $ticketData->save();
                                            }
                                        }
                                        $checkinData['rate'] = $rate;
                                    }
                                    //return $checkinData;
                                }

                                // paid percentage
                                if ($checkinData->paid_type == '3' || $checkinData->paid_type == 3) {
                                    $checkinData['payment'] = 0;

                                    $total_amount =  $rate['price'];
                                    if ($total_amount > "0.00") {
                                        $percentageAmount = (($total_amount * $checkinData->paid_percentage) / 100);
                                    } else {
                                        $total_amount = "0.00";
                                        $percentageAmount = "0.00";
                                    }

                                    $rate['price'] = number_format($rate['price'], 2);

                                    if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {

                                        $checkinData['payment'] = 1;
                                        $rate['price'] = "0";
                                        $rate['facility'] = $checkinData->facility;
                                        $checkinData['rate'] = $rate;
                                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                                        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                                        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
                                        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
                                        $checkinData['payable_amount'] = "0.00";
                                        $checkinData['processing_fee'] = "0.00";
                                        $checkinData['tax_rate'] = "0.00";
                                        $checkinData['parking_amount'] = "0.00";
                                    } else {
                                        if ($percentageAmount > '0.00') {
                                            $updatedPaidAmount = $rate['price'];
                                            $payable_amount = $updatedPaidAmount - $percentageAmount;
                                            if ($checkinData->grand_total > "0") {
                                                $payable_amount = $payable_amount - $checkinData->grand_total;
                                            }
                                            if ($payable_amount > "0") {
                                                $payable_amount = $payable_amount + $processingFee;
                                                $processing_fee = $processingFee;
                                            } else {
                                                $payable_amount = "0.00";
                                                $processing_fee = "0.00";
                                                $checkinData['payment'] = 1;
                                            }
                                            $checkinData['payable_amount'] = number_format($payable_amount, 2);
                                            $checkinData['processing_fee'] = $processing_fee;
                                            $checkinData['tax_rate'] = $taxRate;
                                            $checkinData['parking_amount'] = $updatedPaidAmount;
                                            if ($updatedPaidAmount > "0") {
                                                $ticketData = Ticket::where('id', $checkinData->id)->first();
                                                // $ticketData->paid_amount = $updatedPaidAmount + $processingFee; // not clear why its update
                                                $ticketData->processing_fee = $processingFee;
                                                // Vijay Added : 27-07-2023
                                                $ticketData->parking_amount = $parkingAmount;
                                                $ticketData->total = $ticketPrice;
                                                $ticketData->length = $diff_in_hours;
                                                $ticketData->grand_total = isset($validationData) ? $validationData['grand_total'] : number_format($payable_amount, 2);
                                                // Vijay Added : 27-07-2023
                                                $ticketData->save();
                                            }
                                        }
                                        $checkinData['rate'] = $rate;
                                    }
                                    // return $checkinData;
                                }
                            }

                            if ($checkinData->facility->open_gate_enabled == '1') {
                                $gateStatus = $this->isParkEngageGateOpen($decrytArray->facility_id, $decrytArray->gate, '');
                                if ($gateStatus == "true") {
                                } else {
                                    throw new ApiGenericException($gateStatus);
                                }
                            }

                            //$rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
                            $ticketData = Ticket::where('id', $checkinData->id)->first();
                            $ticketData->is_checkout = '1';
                            $ticketData->checkout_gate = $decrytArray->gate;
                            $ticketData->checkout_time = date("Y-m-d H:i:s");
                            $ticketData->checkout_datetime = date("Y-m-d H:i:s");
                            $ticketData->save();
                            //$checkinData->save();
                            // to save Transaction data
                            $this->request->request->add(['ticket_number' => $request->order_number]);
                            $this->saveTransactionData();

                            if ($checkinData) {
                                $checkinData = Ticket::with(['user', 'facility'])->where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->first();

                                $this->log->info("user checkout with ticket number {$checkinData->ticket_number}");
                                // return Redirect::to('townsend-thankyou-checkout/'.base64_encode($checkinData->ticket_number));
                                //return $this->thankyouCheckout(base64_encode($checkinData->ticket_number));
                                if (isset($checkinData->user->phone) && $checkinData->user->phone != '') {
                                    $partnerDetails = UserPaymentGatewayDetail::where('user_id', $checkinData->partner_id)->first();
                                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                    $facilityName = isset($checkinData->facility->full_name) ? ucwords($checkinData->facility->full_name) : '';
                                    $url = env('RECEIPT_URL');
                                    $msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download e-receipt $url/$name/ticket/" . $checkinData->ticket_number;

                                    $this->customeReplySms($msg, $checkinData->user->phone);
                                }

                                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));


                                $diff_in_hours = $arrival_time->diffInHours($from);
                                $diff_in_mins = $arrival_time->diffInMinutes($from);
                                $diff_in_secs = $arrival_time->diffInSeconds($from);

                                //dd($diff_in_hours,$diff_in_mins,$diff_in_secs);
                                if ($diff_in_mins < 60) {
                                    $diff_in_hours = $diff_in_mins / 100;
                                }
                                if ($diff_in_mins > 59) {
                                    $diff_in_hours = number_format($diff_in_mins / 60, 2);
                                }
                                if ($diff_in_secs < 60) {
                                    $diff_in_hours = .01;
                                }

                                $isMember = 0;
                                $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                                if ($rate == false) {
                                    throw new ApiGenericException('Garage is currently closed.');
                                }

                                $tax_rate = $checkinData->facility->tax_rate;
                                if ($tax_rate > 0) {
                                    $tax_rate = number_format((($rate['price'] * $checkinData->facility->tax_rate) / 100), 2);
                                }
                                $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();
                                if ($overstay) {
                                    $checkinData['overstay_amount'] = $overstay->total;
                                } else {
                                    $checkinData['overstay_amount'] = '0';
                                }
                                if ($rate['price'] <= "0") {
                                    $checkinData['payable_amount'] = "0.00";
                                    $checkinData['processing_fee'] = "0.00";
                                    $checkinData['tax_rate'] = "0.00";
                                } else {
                                    $checkinData['payable_amount'] = number_format($rate['price'] + $checkinData->facility->processing_fee + $tax_rate - $checkinData->grand_total - $checkinData->paid_amount, 2);
                                    $checkinData['processing_fee'] = $checkinData['processing_fee'];
                                    $checkinData['tax_rate'] = $tax_rate;
                                    if ($rate['price'] <= "0") {
                                        $checkinData['payable_amount'] = "0.00";
                                        $checkinData['processing_fee'] = "0.00";
                                        $checkinData['tax_rate'] = "0.00";
                                    }
                                }
                                // $checkinData['parking_amount'] = ($checkinData['grand_total']) - $checkinData['processing_fee'] - $tax_rate;
                                $checkinData['parking_amount'] = $ticketData->parking_amount;
                                if ($checkinData['parking_amount'] <= "0") {
                                    $checkinData['parking_amount'] = "0.00";
                                }
                                return $checkinData;
                            } else {
                                //return back()->with('danger', 'Please scan valid scan QR code.');
                                throw new ApiGenericException('Please scan valid scan QR code.');
                            }
                        }
                        /*$checkinData = Ticket::where('ticket_number', $request->order_number)->where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();*/
                    } else {
                        // return Redirect::to('error-checkin');
                        throw new ApiGenericException('Gate Error.');
                    }
                    $checkinData = Ticket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->first();



                    if ($checkinData) {
                        //return Redirect::to('thankyou-checkin');
                        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();
                        if ($overstay) {
                            $checkinData['overstay_amount'] = $overstay->total;
                        } else {
                            $checkinData['overstay_amount'] = '0';
                        }
                        $checkinData['payable_amount'] = "0.00";
                        $checkinData['parking_amount'] = ($checkinData['grand_total']) - $checkinData['processing_fee'] - $checkinData['tax_fee'];
                        if ($checkinData['parking_amount'] <= "0") {
                            $checkinData['parking_amount'] = "0.00";
                            $checkinData['processing_fee'] = "0.00";
                        }
                        $checkinData['processing_fee'] = "0.00";
                        $checkinData['tax_rate'] = "0.00";

                        return $checkinData;
                    } else {
                        throw new ApiGenericException('No recent ticket is found for this user.');
                    }
                }
            } else {
                //return back()->with('danger', 'Please scan valid scan QR code.');
                throw new ApiGenericException('Please scan valid scan QR code.');
            }
        } catch (Exception $e) {
            // return back()->with('danger', 'Please scan valid scan QR code.');
            throw new ApiGenericException('Please scan valid scan QR code.');
        }
    }

    public function customeReplySms($msg, $phone)
    {

        try {
            // $accountSid = env('TWILIO_ACCOUNT_SID');
            // $authToken  = env('TWILIO_AUTH_TOKEN');
            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
            $client = new Client($accountSid, $authToken);
            try {
                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                    // the number you'd like to send the message to
                    $phone,
                    array(
                        // A Twilio phone number you purchased at twilio.com/console
                        'from' => env('TWILIO_PHONE'),
                        // the body of the text message you'd like to send
                        //'body' => "Fine"
                        'body' => "$msg"
                    )
                );
                $this->log->info("Error message : {$msg} sent to $phone");
                return "true";
            } catch (RestException $e) {
                $this->log->error($e->getMessage());
                return "true";
            }
        } catch (RestException $e) {
            $this->log->error($e->getMessage());
            return "true";
        }
    }

    public function isParkEngageGateAvailable($facility_id, $gate, $phone = '')
    {

        $facility = Facility::where('id', $facility_id)->first();

        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

        //check third party gate API
        $gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        if ($gateDetails) {
            if ($gateDetails->host != '') {
                $params = ['gate_id' => $gate->gate];
                $this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
                $response = ParkengageGateApi::isvehicleAvailable($params, $gateDetails->host);
                $this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response" . json_encode($response));
                if ($response['success'] == false) {
                    $msg = "The system is not currently available. Please try again later.";
                    return $msg;
                }
                if (isset($response['data'][0]) && $response['data'][0] == "true") {
                    return true;
                    /*$cmd_params = ['gate_id'=>$gate->gate];
                $this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params". json_encode($cmd_params));
                $command_response = ParkengageGateApi::openGate($cmd_params, $gateDetails->host);
                $this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response". json_encode($command_response));
                if($command_response['success'] == true){
                    if($command_response['data'][0] == "true"){
                        return true;
                    }else{
                        $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                        return $msg;                            
                    }                        
                }else{
                    $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                    return $msg; 
                }*/
                } else {

                    $msg = "Your vehicle must be in the garage in front of the " . ucwords($gate->gate_type) . " gate.";
                    return $msg;
                }
            }
        }
    }

    public function isParkEngageGateOpen($facility_id, $gate, $phone = '')
    {

        $facility = Facility::where('id', $facility_id)->first();

        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

        //check third party gate API
        $gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        if ($gateDetails) {
            if ($gateDetails->host != '') {
                $params = ['gate_id' => $gate->gate];
                $this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
                /*$response = ParkengageGateApi::isvehicleAvailable($params, $gateDetails->host);
            $this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response". json_encode($response));
            if($response['success'] == false){
                $msg = "The system is not currently available. Please try again later.";
                return $msg; 
            }*/
                //if(isset($response['data'][0]) && $response['data'][0] == "true"){
                $cmd_params = ['gate_id' => $gate->gate];
                $this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params" . json_encode($cmd_params));
                $command_response = ParkengageGateApi::openGate($cmd_params, $gateDetails->host);
                $this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response" . json_encode($command_response));
                if ($command_response['success'] == true) {
                    if ($command_response['data'][0] == "true") {
                        $this->log->info("Response Parkengage Gate Service Gate Open : gate has opened");
                        return true;
                    } else {
                        $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                        return $msg;
                    }
                } else {
                    $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                    return $msg;
                }
                /*}else{

                $msg = "Your vehicle must be in the garage in front of the ".ucwords($gate->gate_type)." gate.";
                return $msg; 
            }*/
            }
        }
    }

    public function thankyouCheckout($ticket_number)
    {
        $checkinData = Ticket::with(['user', 'facility'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '1')->first();

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
        $checkinData['is_overstay'] = '0';
        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->orderBy('id', 'DESC')->first();
        if ($overstay) {
            $checkinData['check_in_datetime'] = $overstay->check_in_datetime;
            $checkinData['checkout_datetime'] = $overstay->checkout_datetime;
            $checkinData['length'] = $overstay->length;
            $checkinData['is_overstay'] = '1';
            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['check_in_datetime']);
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['checkout_datetime']);
            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);
        }
        $this->log->info("user successfully checkout and thankyou page showing with ticket number {$checkinData->ticket_number}");
        return $checkinData;
        //return view('townsend.thankyou-checkout', ['data'=>$checkinData]);
    }

    public function makePlanetPayment(Request $request, $checkinData)
    {
        $user_id = $this->user->id;
        $partner_id = $this->user->created_by;
        $validationID = '"' . config('parkengage.TOWNSEND_SECURITY_MERCHANT_ID') . '"';
        $validationCode = '"' . config('parkengage.TOWNSEND_SECURITY_VALIDATION_CODE') . '"';
        $reference = rand(1000, 9999) . rand(1000, 9999) . rand(1000, 9999);
        $request->request->add(['ref_id' => $reference]);
        $total = ($request->total) * 100;
        //$total = 100;
        if ($request->payment_profile_id != '') // saved cards
        {
            $cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $partner_id)->where('token', $request->payment_profile_id)->first();
            if (!$cardCheck) {
                throw new ApiGenericException("Card details are invalid");
            }

            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => config('parkengage.PLANET_PROFILE_SAVE_URL'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                  "Request": {
                    "Type": "payrequestnocardread",
                    "Version": "W2MXG520",
                      "Credentials": {
                        "ValidationID": ' . $validationID . ',
                        "ValidationCode": ' . $validationCode . ',
                        "ValidationCodeHash": null
                      },
                      "Params": {
                        "PaymentOkUrl": "",
                        "CardNumber": "' . $request->payment_profile_id . '",
                        "CardExpiryDateMMYY": "",
                        "CardStartDateMMYY": "",
                        "CardIssueNumber": "",
                        "CardCvv2": "",
                        "CardholderStreetAddress1": "",
                        "CardholderCity": "",
                        "CardholderState": "",
                        "CardholderZipCode": "",
                        "CardholderNameFirst": "",
                        "CardholderNameLast": "",
                        "Amount": "' . $total . '",
                        "Currency": "USD",
                        "RequesterTransRefNum": "Payment Using Payment Profile ID",
                        "UserData1": "",
                        "UserData2": "",
                        "UserData3": "",
                        "UserData4": "",
                        "UserData5": "",
                        "OptionFlags": "P"

                        }
                }
            }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
                ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            $this->log->info("Response Data Planet (Saved Cards): " . json_encode($response));

            $refundstatus = json_decode($response, TRUE);
            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                return ['error' => 'payment failed.'];
            }

            Session::put('card_last_four', $cardCheck->card_last_four);
            Session::put('card_type', $cardCheck->card_name);
            Session::put('expiry', $cardCheck->expiry);
        } else {
            if ($request->nonce) {
                $this->setDecryptedCard($request);
            }
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => config('parkengage.PLANET_PROFILE_SAVE_URL'),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => '{
                  "Request": {
                    "Type": "EftAuthorization",
                    "Version": "W2MXG520",
                      "Credentials": {
                        "ValidationID": ' . $validationID . ',
                        "ValidationCode": ' . $validationCode . ',
                        "ValidationCodeHash": null
                      },
                      "Params": {
                          "PaymentOkUrl": "",
                          "CardNumber": "' . $request->card_number . '",
                          "CardExpiryDateMMYY": "' . str_replace('/', '', $request->expiration_date) . '",
                          "CardStartDateMMYY": "",
                          "CardIssueNumber": "",
                          "CardCvv2": "' . $request->security_code . '",
                          "CardholderStreetAddress1": "",
                          "CardholderCity": "",
                          "CardholderState": "",
                          "CardholderZipCode": "",
                          "CardholderNameFirst": "",
                          "CardholderNameLast": "",
                          "Amount": "' . $total . '",
                          "Currency": "USD",
                          "RequesterTransRefNum": "TEST AUTH 001",
                          "UserData1": "",
                          "UserData2": "",
                          "UserData3": "",
                          "UserData4": "",
                          "UserData5": "",
                          "OptionFlags": "G"
                        }
                }
            }',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
                ),
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $this->log->info("Response Data Planet (Non Saved Cards): " . json_encode($response));

            $refundstatus = json_decode($response, TRUE);

            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                return ['error' => 'payment failed.'];
            }
        }
        $authorized_anet_transaction = new AuthorizeNetTransaction();
        $authorized_anet_transaction->sent = '1';
        $authorized_anet_transaction->user_id = $user_id;
        $authorized_anet_transaction->ip_address = \Request::ip();
        $authorized_anet_transaction->total = $total / 100;
        $authorized_anet_transaction->description = "Townsend payment: " . $user_id;
        $authorized_anet_transaction->card_type = $refundstatus["Response"]["Params"]["CardSchemeName"];
        $authorized_anet_transaction->ref_id = $this->request->ref_id;
        $authorized_anet_transaction->anet_trans_id = $refundstatus["Response"]["Params"]["TxID"];
        $authorized_anet_transaction->method = "card";
        $authorized_anet_transaction->payment_last_four = isset($refundstatus["Response"]["Params"]["CardNumberLast4"]) ? $refundstatus["Response"]["Params"]["CardNumberLast4"] : '0';
        $authorized_anet_transaction->expiration = isset($refundstatus["Response"]["Params"]["CardExpiryDateMMYY"]) ? $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"] : '0';

        $authorized_anet_transaction->save();
        if (isset($refundstatus["Response"]["Params"]["Token"])) {
            $userSessionExist = UserSession::where("user_id", $user_id)->where("session_id", $refundstatus["Response"]["Params"]["Token"])->first();
            if (!$userSessionExist) {
                $userSession = new UserSession();
                $userSession->user_id = $user_id;
                $userSession->partner_id = $partner_id;
                $userSession->session_id = $refundstatus["Response"]["Params"]["Token"];
                $userSession->save();

                $checkinData->session_id = $refundstatus["Response"]["Params"]["Token"];
                $checkinData->payment_gateway = "planet";
                $checkinData->save();
            }
        }
        return $authorized_anet_transaction;
    }



    public function getCheckinDetailsPaymentThankyou($request, $ticket_number)
    {
        $checkinData = Ticket::with(['facility', 'user'])->where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            throw new ApiGenericException("No recent ticket is found for this user.");
        }
        if ($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != '') {
            // return Redirect::to('townsend-pay/'.$ticket_number);  
            return $this->getCheckinTicketDetails($request, $ticket_number);
        }
        if ($checkinData->is_checkout == '1') {
            throw new ApiGenericException("Already Checkout.");
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInMinutes($endDate);

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

        $diff_in_hours = $arrival_time->diffInHours($from);
        $diff_in_mins = $arrival_time->diffInMinutes($from);
        $diff_in_secs = $arrival_time->diffInSeconds($from);

        //dd($diff_in_hours,$diff_in_mins,$diff_in_secs);
        if ($diff_in_mins < 60) {
            $diff_in_hours = $diff_in_mins / 100;
        }
        if ($diff_in_mins > 59) {
            $diff_in_hours = number_format($diff_in_mins / 60, 2);
        }
        if ($diff_in_secs < 60) {
            $diff_in_hours = .01;
        }

        $checkinData['length'] = $diff_in_hours;
        //$facility = Facility::find($checkinData->facility_id);

        $isMember = 0;
        $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
        if ($rate == false) {
            //return Redirect::to('townsend-error-facility');
            throw new ApiGenericException("Rate not found.");
        }

        //return current availabilit
        if ($rate['price'] == 'N/A') {
            $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            //    $rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
        } else {
            //    $rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
        }
        //returning  message as per availibility 
        $checkinData['rate'] = $rate;
        $tax_rate = $checkinData->facility->tax_rate;
        if ($tax_rate > 0) {
            $tax_rate = number_format((($rate['price'] * $checkinData->facility->tax_rate) / 100), 2);
        }
        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();
        if ($overstay) {
            $checkinData['overstay_amount'] = $overstay->total;
        } else {
            $checkinData['overstay_amount'] = '0';
        }
        if ($rate['price'] <= "0") {
            $checkinData['payable_amount'] = "0.00";
            $checkinData['processing_fee'] = "0.00";
            $checkinData['tax_rate'] = "0.00";
        } else {
            $checkinData['payable_amount'] = number_format($rate['price'] + $checkinData->facility->processing_fee + $tax_rate - $checkinData->grand_total - $checkinData->paid_amount, 2);
            $checkinData['processing_fee'] = $checkinData->facility->processing_fee;
            $checkinData['tax_rate'] = $tax_rate;
            if ($rate['price'] <= "0") {
                $checkinData['payable_amount'] = "0.00";
                $checkinData['processing_fee'] = "0.00";
                $checkinData['tax_rate'] = "0.00";
            }
        }
        $checkinData['parking_amount'] = $rate['price'];
        $checkinData['DATACAP_SCRIPT_URL'] = config('parkengage.DATACAP_SCRIPT_URL');
        $checkinData['DATACAP_TOKEN'] = $checkinData->facility->ecommerce_token;
        $checkinData['DATACAP_OTU_URL'] = config('parkengage.DATACAP_OTU_URL');

        return $checkinData;
        //return view('townsend.thankyou-payment', ['data'=>$checkinData]);
    }


    protected function checkFacilityAvailable($facility_id)
    {
        $facility = Facility::where('id', $facility_id)->where('active', '1')->first();
        if (count($facility) == 0) {
            return false;
        }
        if ($facility->is_available != '1') {
            return false;
        }
        return true;
    }

    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = isset($cardData[4]) ? $cardData[4] : '';
        $request->request->add(
            [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode
            ]
        );

        $this->request = $request;
    }

    protected function saveTicket()
    {
        $ticket = Ticket::with(['facility', 'user'])->where('ticket_number', $this->request->ticket_number)->first();
        if (!$ticket) {
            return false;
        }
        $currentTime = Carbon::parse('now');
        $estimated_checkout = Carbon::parse($currentTime)->addMinutes($ticket->facility->grace_period_minute)->format('Y-m-d H:i:s');
        $priceBreakUp = $ticket->priceBreakUp($this->rate, $this->request->total);

        $ticket->rate_id = isset($this->rate['id']) ? $this->rate['id'] : '';
        $ticket->rate_description = isset($this->rate['description']) ? $this->rate['description'] : '';
        $ticket->length = $this->request->length;
        $ticket->ticket_security_code = rand(1000, 9999);
        $ticket->total = $priceBreakUp['total'];
        $ticket->grand_total = isset($priceBreakUp) ? $priceBreakUp['grand_total'] : $this->request->total;
        $ticket->processing_fee = $priceBreakUp['processing_fee'];
        $ticket->parking_amount = $priceBreakUp['parking_amount'];
        $ticket->paid_amount = isset($priceBreakUp) ? $priceBreakUp['paid_amount'] : '0.0';
        $ticket->tax_fee = $priceBreakUp['tax_rate'];
        $ticket->checkout_datetime = date('Y-m-d H:i:s');

        // $ticket->estimated_checkout = date('Y-m-d H:i:s', strtotime("+" . $ticket->facility->grace_period_minute . " hours"));
        $ticket->estimated_checkout = $estimated_checkout;
        $ticket->save();

        return $ticket;
    }

    protected function saveTicket_backup()
    {
        $ticket = Ticket::with(['facility', 'user'])->where('ticket_number', $this->request->ticket_number)->first();
        if (!$ticket) {
            return false;
        }
        //$total = ($this->request->total - $this->facility->processing_fee);
        $tax_rate = $ticket->facility->tax_rate;
        if ($tax_rate > 0) {
            $tax_rate = number_format((($this->rate['price'] * $ticket->facility->tax_rate) / 100), 2);
        }

        if ($this->rate['price'] <= "0") {
            $payable_amount = "0.00";
            $processing_fee = "0.00";
            $tax_rate = "0.00";
        } else {
            $payable_amount = number_format($this->rate['price'] + $ticket->facility->processing_fee + $tax_rate - $ticket->grand_total - $ticket->paid_amount, 2);
            $processing_fee = $ticket->facility->processing_fee;
            $tax_rate = $tax_rate;
        }
        $parking_amount = $this->rate['price'];
        if ($ticket->paid_type != 9) {
            $validationData = $this->populateValidationFields($ticket, $this->rate, $ticket->facility);
        }
        $ticket->rate_description = isset($this->rate['id']) ? $this->rate['id'] : '';
        $ticket->length = $this->request->length;
        $ticket->ticket_security_code = rand(1000, 9999);
        $ticket->total = isset($validationData) ? $validationData['total'] : $payable_amount;
        $ticket->grand_total = isset($validationData) ? $validationData['grand_total'] : $this->request->total;
        $ticket->processing_fee = $processing_fee;
        $ticket->parking_amount = $parking_amount;
        $ticket->paid_amount = isset($validationData) ? $validationData['paid_amount'] : '0.0';
        $ticket->tax_fee = $tax_rate;
        $ticket->checkout_datetime = date('Y-m-d H:i:s');
        $ticket->estimated_checkout = date('Y-m-d H:i:s', strtotime("+1 hours"));
        $ticket->save();

        return $ticket;
    }
    public  function updatePhoneNumber()
    {

        // $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        // if(!$secret){
        //     throw new ApiGenericException('Invalid partner.');
        // }
        $ticket = Ticket::where('ticket_number', $this->request->ticket_number)->first();
        if (!$ticket) {
            throw new ApiGenericException('Invalid Ticket Number.');
        }
        $this->countryCode = QueryBuilder::appendCountryCode();
        $phone = $this->countryCode . $this->request->phone;
        $user = User::find($ticket->user_id);
        $user->phone = $phone;
        $user->save();
        return $user;
    }

    // Datacap payment code start

    // Datacap Payment 

    public function makeDataCapPayment(Request $request, $checkinData)
    {
        if ($request->payment_profile_id != '') // saved cards
        {
            $cardCheck = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $request->user_id)->where('card_holder_id', $request->payment_profile_id)->first();
            //dd($cardCheck);
            if (!$cardCheck) {
                throw new ApiGenericException("Payment Profile Not Found.");
            }
            $this->log->info("Payment Profile Data --" . $cardCheck);


            //$amount = $request->total;
            //$amount = '3.66';
            $amount = (config('parkengage.DATACAP_PAYMENT_ENV') == 'test') ? '3.66' : $request->total;
            $data['Token'] = $cardCheck->token;
            if ($amount > 0) {
                $amount = number_format($amount, 2);
                $data['Amount'] = $amount;
                $data['Token'] = $cardCheck->token;
                //dd($paymentStatus);
                $paymentResponse = $this->makePaymentDataCap($data);

                //dd($paymentResponse);

                if ($paymentResponse['Status'] == 'Approved') {
                    $brand = str_replace('/', '', $paymentResponse['Brand']);
                    $authorized_anet_transaction = new AuthorizeNetTransaction();
                    $authorized_anet_transaction->sent = '1';
                    $authorized_anet_transaction->user_id = $this->user->id;
                    $authorized_anet_transaction->ip_address = $cardCheck->ip_address;
                    $authorized_anet_transaction->total = $request->total;
                    $authorized_anet_transaction->description = "Payment Done User : " . $this->user->id;
                    $authorized_anet_transaction->card_type = $brand;
                    $authorized_anet_transaction->ref_id = $paymentResponse["RefNo"];
                    $authorized_anet_transaction->method = "card";
                    $authorized_anet_transaction->payment_last_four = isset($cardCheck->card_last_four) ? $cardCheck->card_last_four : '0';
                    $authorized_anet_transaction->auth_code = $paymentResponse["AuthCode"];
                    $authorized_anet_transaction->response_code = $paymentResponse["ReturnCode"];
                    $authorized_anet_transaction->response_message = "Processed";
                    $authorized_anet_transaction->invoice_number = $paymentResponse["InvoiceNo"];
                    $authorized_anet_transaction->anet_trans_id = $paymentResponse["InvoiceNo"];
                    $authorized_anet_transaction->status_message = $paymentResponse["Status"];
                    $authorized_anet_transaction->expiration = $cardCheck->expiry;

                    $authorized_anet_transaction->save();
                    $this->log->info("Payment Transaction Data  --" . $authorized_anet_transaction);
                    if (isset($paymentResponse["CardHolderID"])) {
                        $userSessionExist = UserSession::where("user_id", $this->user->id)->where("session_id", $paymentResponse["CardHolderID"])->first();
                        if (!$userSessionExist) {
                            $userSession = new UserSession();
                            $userSession->user_id = $this->user->id;
                            $userSession->partner_id = $this->user->created_by;
                            $userSession->session_id = $paymentResponse["CardHolderID"];
                            $userSession->save();

                            $checkinData->session_id = $paymentResponse["CardHolderID"];
                            $checkinData->payment_gateway = "datacap";
                            $checkinData->save();
                        }
                    }
                    return $authorized_anet_transaction;
                } else {
                    return $paymentResponse;
                    return $data['error'] = "Payment Failed.";
                    return $data;
                }
            }
        } else if ($request->nonce) {
            $this->log->info("Request Data Datacap: " . json_encode($request->all()));
            //$partner_id = Auth::user()->created_by;
            $partner_id = $this->secret->partner_id;
            //$amount = '3.66';
            $amount = (config('parkengage.DATACAP_PAYMENT_ENV') == 'test') ? '3.66' : $request->total;
            //$amount = $request->total;
            $amount = number_format($amount, 2);
            $data['Amount'] = $amount;
            $data['Token'] = $request->token;

            $paymentResponse = $this->makePaymentDataCap($data);
            if ($paymentResponse['Status'] == 'Approved') {
                $authorized_anet_transaction = new AuthorizeNetTransaction();
                $authorized_anet_transaction->sent = '1';
                $authorized_anet_transaction->user_id = $this->user->id;
                $authorized_anet_transaction->ip_address = \Request::ip();
                $authorized_anet_transaction->total = $request->total;
                $authorized_anet_transaction->description = "Payment Done User : " . $this->user->id;
                $authorized_anet_transaction->card_type = $request->brand;
                $authorized_anet_transaction->ref_id = $paymentResponse["RefNo"];
                $authorized_anet_transaction->method = "card";
                $authorized_anet_transaction->payment_last_four = isset($request->card_number) ? $request->card_number : '0';
                $authorized_anet_transaction->auth_code = $paymentResponse["AuthCode"];
                $authorized_anet_transaction->response_code = $paymentResponse["ReturnCode"];
                $authorized_anet_transaction->response_message = "Processed";
                $authorized_anet_transaction->invoice_number = $paymentResponse["InvoiceNo"];

                $authorized_anet_transaction->status_message = $paymentResponse["Status"];
                $authorized_anet_transaction->expiration = $request->expiration_date;

                $authorized_anet_transaction->save();
                $this->log->info("Payment Transaction Data  --" . $authorized_anet_transaction);
                if (isset($paymentResponse["CardHolderID"])) {
                    $userSessionExist = UserSession::where("user_id", $this->user->id)->where("session_id", $paymentResponse["CardHolderID"])->first();
                    if (!$userSessionExist) {
                        $userSession = new UserSession();
                        $userSession->user_id = $this->user->id;
                        $userSession->partner_id = $this->user->created_by;
                        $userSession->session_id = $paymentResponse["CardHolderID"];
                        $userSession->save();

                        $checkinData->session_id = $paymentResponse["CardHolderID"];
                        $checkinData->payment_gateway = "datacap";
                        $checkinData->save();
                    }
                }
                return $authorized_anet_transaction;
            } else {
                return $data['error'] = "Payment Failed.";
                return $data;
            }
        } else if ($request->datacap_token) {
            $this->log->info("Request Data Datacap: " . json_encode($request->all()));
            //$partner_id = Auth::user()->created_by;
            $partner_id = $this->secret->partner_id;
            // $amount = '3.66';
            $amount = (config('parkengage.DATACAP_PAYMENT_ENV') == 'test') ? '3.66' : $request->total;
            //$amount = $request->total;
            $amount = number_format($amount, 2);
            $data['Amount'] = $amount;
            $data['Token'] = $request->datacap_token["Token"];
            $paymentResponse = $this->makePaymentDataCap($data);
            //dd($paymentResponse);
            if ($paymentResponse['Status'] == 'Approved') {
                $brand = str_replace('/', '', $paymentResponse['Brand']);
                $authorized_anet_transaction = new AuthorizeNetTransaction();
                $authorized_anet_transaction->sent = '1';
                $authorized_anet_transaction->user_id = $this->user->id;
                $authorized_anet_transaction->ip_address = \Request::ip();
                $authorized_anet_transaction->total = $request->total;
                $authorized_anet_transaction->description = "Payment Done User : " . $request->user_id;
                $authorized_anet_transaction->card_type = $brand;
                $authorized_anet_transaction->ref_id = $paymentResponse["RefNo"];
                $authorized_anet_transaction->method = "card";
                $authorized_anet_transaction->payment_last_four = isset($request->datacap_token['Last4']) ? $request->datacap_token['Last4'] : '0';
                $authorized_anet_transaction->auth_code = $paymentResponse["AuthCode"];
                $authorized_anet_transaction->response_code = $paymentResponse["ReturnCode"];
                $authorized_anet_transaction->response_message = "Processed";
                $authorized_anet_transaction->invoice_number = $paymentResponse["InvoiceNo"];

                $authorized_anet_transaction->status_message = $paymentResponse["Status"];
                $authorized_anet_transaction->expiration = $request->datacap_token['ExpirationMonth'] . substr($request->datacap_token['ExpirationYear'], -2);

                $authorized_anet_transaction->save();
                if (isset($paymentResponse["CardHolderID"])) {
                    $userSessionExist = UserSession::where("user_id", $this->user->id)->where("session_id", $paymentResponse["CardHolderID"])->first();
                    if (!$userSessionExist) {
                        $userSession = new UserSession();
                        $userSession->user_id = $this->user->id;
                        $userSession->partner_id = $this->user->created_by;
                        $userSession->session_id = $paymentResponse["CardHolderID"];
                        $userSession->save();

                        $checkinData->session_id = $paymentResponse["CardHolderID"];
                        $checkinData->payment_gateway = "datacap";
                        $checkinData->save();
                    }
                }
                //dd($authorized_anet_transaction);
                // $this->log->info("Payment Transaction Data  --". $authorized_anet_transaction);
                //dd($authorized_anet_transaction);
                return $authorized_anet_transaction;
            } else {
                return $data['error'] = "Payment Failed.";
                return $data;
            }
        }
        //return $authorized_anet_transaction;        
    }

    // zero Auth Payment
    public function makeZeroAuthPaymentDataCap()
    {
        $this->log->info("call datacap auth only curl");
        $data['Token'] = $this->request->token;
        $data['Amount'] = 0.00;
        $data["CardHolderID"] = "Allow_V2";
        $mid = $this->facility->ecommerce_mid;
        //$mid = 'PARKESANJ3EP';
        if ($mid == '') {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $vars = json_encode($data);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        //dd($data, $headers,$mid);
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, config('parkengage.DATACAP_AUTHONLY_URL'));
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);
        curl_close($curl);
        dd($response);
        $this->log->info("datacap auth only curl response " . json_encode($response));
        return $response;
    }

    //  Pre Auth Transaction
    public function makePreAuthPaymentDataCap($data)
    {
        $this->log->info("call datacap auth only curl");
        //$data['Token'] = $this->request->token;
        //$data['Amount'] = 0.00;
        //  $data["CardHolderID"]="Allow_V2";
        $mid = $this->facility->ecommerce_mid;
        if ($mid == '') {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $vars = json_encode($data);
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];
        //dd($data,$mid,$headers);
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, config('parkengage.DATACAP_PAYMENT_PRE_AUTH_URL'));
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);
        curl_close($curl);
        //dd($response);
        $this->log->info("datacap auth only curl response " . json_encode($response));
        return $response;
    }


    public function makePaymentDataCap($data)
    {
        $data["CardHolderID"] = "Allow_V2";
        $vars = json_encode($data);
        $mid = $this->facility->ecommerce_mid;
        if ($mid == '') {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
        $headers = [
            'Authorization: ' . $mid,
            'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
            'Content-Type: application/json',
            'Accept: application/json',
            'Accept-Language: en-US,en;q=0.5',
            'Cache-Control: no-cache'
        ];

        // dd($vars,$headers,config('parkengage.DATACAP_PAYMENT_URL',$mid));

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, config('parkengage.DATACAP_PAYMENT_URL'));
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);

        curl_close($curl);
        $this->log->info("Payment Data --" . json_encode($response));
        //dd($response);
        $paymentData = json_decode($response, TRUE);
        return $paymentData;
    }
    // Datacap payment code end
    // Overstay Ticket Entry
    protected function saveOverstayTicket()
    {
        $this->log->info("saveOverstayTicket --");
        $mainTicket = Ticket::where('ticket_number', $this->request->ticket_number)->first();

        $tax_rate = $mainTicket->facility->tax_rate;
        if ($tax_rate > 0) {
            $tax_rate = number_format((($this->rate['price'] * $mainTicket->facility->tax_rate) / 100), 2);
        }
        $currentTime = Carbon::parse('now');
        $estimated_checkout = Carbon::parse($currentTime)->addMinutes($mainTicket->facility->grace_period_minute)->format('Y-m-d H:i:s');

        $ticket = new OverstayTicket();
        $ticket->user_id = $this->user->id;
        $ticket->ticket_id = $mainTicket->id;
        $ticket->facility_id = $this->facility->id;
        $ticket->anet_transaction_id = $this->paymentStatus->id;
        $ticket->length = $this->request->length;
        $ticket->total = $this->request->total;
        $ticket->ticket_number = $this->request->ticket_number;
        $ticket->is_checkin = '1';
        $ticket->tax_fee = $tax_rate;
        $ticket->processing_fee =  $mainTicket->facility->processing_fee;
        $ticket->check_in_datetime = date('Y-m-d H:i:s', strtotime($mainTicket->estimated_checkout));
        $ticket->checkout_datetime = $estimated_checkout;
        $ticket->grand_total = $this->request->total;
        $ticket->save();

        $mainTicket->is_overstay = '1';
        $mainTicket->estimated_checkout = $estimated_checkout;
        $mainTicket->save();
        $this->log->info("saveOverstayTicket --" . json_encode($mainTicket));

        return $ticket;
    }
    protected function saveOverstayTicket_backup()
    {
        $mainTicket = Ticket::where('ticket_number', $this->request->ticket_number)->first();
        $mainTicket->grand_total = $this->request->total + $mainTicket->total;
        $mainTicket->is_overstay = '1';
        $mainTicket->estimated_checkout = date('Y-m-d H:i:s', strtotime("+" . self::GRACE_PERIOD . " hours"));
        $mainTicket->save();

        $tax_rate = $mainTicket->facility->tax_rate;
        if ($tax_rate > 0) {
            $tax_rate = number_format((($this->rate['price'] * $mainTicket->facility->tax_rate) / 100), 2);
        }

        $ticket = new OverstayTicket();
        $ticket->user_id = $this->user->id;
        $ticket->facility_id = $this->facility->id;
        $ticket->anet_transaction_id = $this->paymentStatus->id;
        $ticket->length = $this->request->length;
        $ticket->total = $this->request->total;
        $ticket->ticket_number = $this->request->ticket_number;
        $ticket->is_checkin = '1';
        $ticket->tax_fee = $tax_rate;
        $ticket->processing_fee =  $mainTicket->facility->processing_fee;
        $ticket->check_in_datetime = date('Y-m-d H:i:s', strtotime($mainTicket->check_in_datetime));
        $ticket->checkout_datetime = date('Y-m-d H:i:s');
        $ticket->save();
        return $ticket;
    }

    //Save Transaction Data for Report
    protected function saveTransactionData()
    {
        $mainTicket = Ticket::where('ticket_number', $this->request->ticket_number)->first();

        $rate_id = isset($this->rate['id']) ? $this->rate['id'] : '';
        $rate_description = isset($this->rate['description']) ? $this->rate['description'] : '';
        $rate_amount = $this->rate['price'];
        $tax_rate = $mainTicket->facility->tax_rate;
        if ($tax_rate > 0) {
            $tax_rate = number_format((($this->rate['price'] * $mainTicket->facility->tax_rate) / 100), 2);
        }
        if (isset($this->request->is_overstay) && $this->request->is_overstay == '1') {
            $ticket = TransactionData::where(['ticket_id' => $mainTicket->id])->first();

            $ticket->grand_total = ($ticket->grand_total + $this->request->total);
        } else {
            $ticket = new TransactionData();

            $ticket->grand_total = $this->request->total;
        }
        $ticket->rate_id = $rate_id;
        $ticket->rate_description = $rate_description;
        $ticket->rate_amount = $this->rate['price'];
        $ticket->total = $this->rate['price'];
        $ticket->tax_fee = $tax_rate;
        $ticket->user_id = $mainTicket->user_id;
        $ticket->facility_id = $mainTicket->facility_id;
        $ticket->partner_id = $mainTicket->partner_id;
        $ticket->ticket_id = $mainTicket->id;
        $ticket->processing_fee = $mainTicket->facility->processing_fee;
        $ticket->discount_amount = $mainTicket->paid_amount;
        $ticket->save();
        return $ticket;
    }

    public function downloadQrCodeCheckin($id)
    {
        $data = Ticket::with(['userPass.transaction', 'reservation.transaction', 'transaction', 'facility', 'user'])->where('id', $id)->first();
        //return $data;
        if (!$data) {
            throw new ApiGenericException("Invalid booking details.");
        }
        $pdf = $this->generatePdf($data, Pdf::class);
        return $pdf;
    }

    public function generatePdf($data, $format = Image::class)
    {
        $gates = Gate::where('facility_id', $data->facility_id)->get();
        if ($gates) {
            foreach ($gates as $key => $value) {
                if ($value->gate == $data->checkin_gate) {
                    $data['checkin_gate_name'] = $value->gate_name;
                }
                if ($value->gate == $data->checkout_gate) {
                    $data['checkout_gate_name'] = $value->gate_name;
                }
            }
        } else {
            $data['checkin_gate_name'] = '';
            $data['checkout_gate_name'] = '';
        }
        $data['checkin_time'] = date("d-m-Y, h:i A", strtotime($data->checkin_time));
        $data['checkout_time'] = date("d-m-Y, h:i A", strtotime($data->checkout_time));

        if ($data['reservation_id'] == null && $data['user_pass_id'] == null && $data['checkout_without_checkin'] != '1') {
            $data['drive'] = 1;
            $data['title'] = 'Drive-Up Details:';
        } else {
            $data['check'] = 1;
            $data['title'] = 'Check-In Details:';
        }
        if ($data['is_checkin'] == 0) {
            $data['title'] = 'Check-Out Details:';
        }

        $str = isset($data->transaction->expiration) ? $data->transaction->expiration : '';
        $insertstr = '/';
        $pos = 2;
        if (isset($data->transaction->expiration)) {
            $data['expiration'] = substr($str, 0, $pos) . $insertstr . substr($str, $pos);
        } else {
            $data['expiration'] = '';
        }

        $data = ['data' => $data];
        if ($format == Image::class) {
            $html = view("townsend.townsend_checkin_pdf", $data)->render();
        } else {
            $html = view("townsend.townsend_checkin_pdf", $data)->render();
        }

        $image = app()->make($format);
        return $image->getOutputFromHtmlString($html);
    }

    public function getEndTime($decimalHours)
    {
        // $decimalHours = 2.4;
        $hours = floor($decimalHours);
        $mins = round(($decimalHours - $hours) * 60);
        $timeInMinutes = ($hours * 60) + $mins;
        return $timeInMinutes;
    }

    // vijay - 30-05-2023- 
    public function cashierShiftReport(Request $request)
    {
        // $from_date = date("Y-m-d");
        // $report = DB::select("use inventory_modules");
        // Auth::user()->id;
        if (Auth::user()->user_type == '1') {
            // Super Admin
            $partner_id = isset($request->partner_id) ? $request->partner_id : self::PARTNET_ID;
            $facility = Facility::where(['owner_id' => $request->partner_id])->first();
            $rates = Rate::where(['facility_id' => $facility->id])->orderby('description', 'asc')->get();
            $fromdate = isset($request->fromDate) ? date('Y-m-d', strtotime($request->fromDate))  : date('Y-m-d');
            $toDate = isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate))  : date('Y-m-d');
            // $checkInTime  =  '2023-06-01 00-00-00';
            $checkInTime  = date('Y-m-d', strtotime($fromdate)) . ' 00-00-00';
            // $checkInTime  = $checkinDate . ' 00-00-00';
            $checkOutTime    = date('Y-m-d', strtotime($toDate)) . ' 23-59-59';
            if (!$partner_id) {
                throw new ApiGenericException('Partner detail not found.', 500);
            }
            $brandSetting = BrandSetting::where('user_id', $partner_id)->first();
            $color = $brandSetting->color;
            // if (!$checkinDate) {
            //     throw new ApiGenericException('Please select date.', 500);
            // }

            // Removed daily Revenue reports query 23-08-2023
            // $sql_query = "SELECT count(td.rate_description) as trx_count, td.rate_description,td.rate_amount, t.total as total, t.tax_fee as tax_fee, t.processing_fee as processing_fee, sum(t.grand_total) as grand_total, sum(td.discount_amount) as validate_amonut, f.full_name,f.garage_code
            //    from transaction_data as td
            //    inner join tickets as t on t.id = td.ticket_id
            //    inner join facilities as f on f.id = td.facility_id
            //    where td.partner_id IN ('$partner_id') and t.checkin_time >='$checkInTime' and t.checkin_time <='$checkOutTime' and t.is_checkout ='1' group by td.rate_description"; #td.rate_description
            // // echo $sql_query;
            // // die();
            // $reports = DB::select($sql_query);

            //non validated query :: Tickets Cashiered
            $sql_query5 = "SELECT count(td.rate_description) as trx_count, td.rate_id, td.rate_description,td.rate_amount, t.total as total, t.tax_fee as tax_fee, sum(t.processing_fee) as processing_fee, sum(t.grand_total) as grand_total, sum(t.parking_amount) as net_amount, sum(t.discount_amount) as validate_amonut, f.full_name,f.garage_code,
            (SELECT sum(ot.grand_total) FROM overstay_tickets as ot where ot.ticket_id IN (t.id)) as overstayGrandTotal,
            (SELECT sum(otd.discount_amount) FROM overstay_tickets as otd where otd.ticket_id IN (t.id)) as overstayDiscount
            from transaction_data as td
            inner join tickets as t on t.id = td.ticket_id
            inner join facilities as f on f.id = td.facility_id
            where td.partner_id IN ('$partner_id') and t.checkin_time >='$checkInTime' and t.checkin_time <='$checkOutTime' and td.rate_description != '' and t.paid_type='9' group by td.rate_description,t.is_overstay,t.id"; #td.rate_description
            $nonValidated = DB::select($sql_query5);
            // dd($nonValidated);

            //cash report query
            $sql_query2 = "SELECT SUM(t.grand_total) AS sum_offline_amount,SUM(t.discount_amount) as discountAmount,t.is_offline_payment,t.partner_id ,count(t.id) as ticketCount,SUM(t.processing_fee) as processingFee,
            (SELECT sum(overstay_tickets.grand_total) FROM overstay_tickets where ticket_id IN (t.id)) as overstayGrandTotal,
            (SELECT sum(overstay_tickets.discount_amount) FROM overstay_tickets where ticket_id IN (t.id)) as overstayDiscount
            FROM tickets AS t 
            WHERE t.partner_id IN ('$partner_id') AND t.checkin_time >='$checkInTime' AND t.checkin_time <='$checkOutTime' AND t.is_offline_payment IN('1','2','3')  GROUP BY is_offline_payment,t.id"; #td.rate_description
            $cashReport = DB::select($sql_query2);

            // CC Reports query
            $sql_query3 = "SELECT CASE
                    WHEN card_type IN ('MC', 'MASTERCARD') THEN 'MASTERCARD'
                    when card_type IN ('VS','VISA') THEN 'VISA'
                    ELSE card_type
                END AS combined_card_type, SUM(t.grand_total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                (SELECT sum(overstay_tickets.grand_total) FROM overstay_tickets where ticket_id IN (t.id)) as overstayGrandTotal,
                (SELECT sum(overstay_tickets.discount_amount) FROM overstay_tickets where ticket_id IN (t.id)) as overstayDiscount
                FROM tickets as t
                WHERE t.partner_id IN ('$partner_id') AND t.checkin_time >='$checkInTime' AND t.checkin_time <='$checkOutTime' AND t.card_type IS NOT NULL AND t.is_offline_payment='0' GROUP BY combined_card_type,t.is_overstay"; #td.rate_description     

            $ccReport = DB::select($sql_query3);
            // Validation sum(t.grand_total) as paidAmount,
            $sql_query4 = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                sum(t.total) as total,                
                sum(t.grand_total) as paidAmount,                
                sum(t.parking_amount) as parking_amount,
                sum(t.paid_amount) as validated_amount,
                t.affiliate_business_id, ab.business_name as BusinessName
                FROM tickets as t        
                inner join affiliate_business as ab on ab.id = t.affiliate_business_id 
                WHERE t.checkin_time >='$checkInTime' and t.checkin_time <='$checkOutTime' and t.partner_id IN ('$partner_id') and paid_by is not null and t.affiliate_business_id is not null and t.paid_amount > 0 GROUP BY t.affiliate_business_id order by t.affiliate_business_id";
            $validationReport = DB::select($sql_query4);

            // Get OverStay Details 

            // $overstay = "SELECT SUM(ot.grand_total) AS overstayTotal, SUM(ot.discount_amount) as overstayDiscount
            // FROM tickets AS t 
            // inner join overstay_tickets as ot on t.id = ot.ticket_id
            // WHERE t.checkin_time >='$checkInTime' and t.checkin_time <='$checkOutTime' and t.partner_id IN ('$partner_id') AND t.is_offline_payment IN('1','2','3')  GROUP BY is_offline_payment ";
            // $overstayResult = DB::select($overstay);
            // // dd($validationReport);
            // $offlineOverStayTotal = isset($overstayResult[0]->overstayTotal) ? $overstayResult[0]->overstayTotal : 0;
            // $offlineOverStayDiscount = isset($overstayResult[0]->overstayDiscount) ? $overstayResult[0]->overstayDiscount : 0;
            try {
                // $reports = DB::select($sql_query1);
                // $count = count($reports);
                // if ($count <= 0) {
                //     throw new ApiGenericException('No record found');
                // }
                $count = 0;
                $excelSheetName = ucwords(str_replace(' ', '', 'Daily Revenue Report'));
                $finalCodes1 = [];
                $increment1 = 1;
                $TotalRevenue = 0;
                $totalTickets = 0;
                $netValue = 0;
                $ticketAmount = 0;
                $grossAmount = 0;
                $validatedAmount = 0;
                $netValue = 0;
                $processingFee = 0;
                $cashiredProcessingFee = 0;
                $taxFee = 0;
                $locationName = '';
                $garageCode = '';

                // Non Validated
                $finalCodes5 = [];
                $TotalRevenueNonValidated = 0;

                $totalTicketsNonValidated = 0;
                $netValueNonValidated = 0;
                $ticketAmountNonValidated = 0;
                $grossAmountNonValidated = 0;
                $validatedAmountNonValidated = 0;
                $processingFeeNonValidated = 0;
                $taxFeeNonValidated = 0;

                $excelrowCount =  0;

                $excelSheetName5 = ucwords('Tickets Cashiered');
                foreach ($rates as $key => $rate) {
                    $rr = $ticketAmount = $grossAmount = $processingFees = $netAmonut = $discountAmount = $ticketCount = 0;

                    foreach ($nonValidated as $n_key => $value) {
                        if ($rate->id == $value->rate_id) {
                            $excelrowCount++;
                            $locationName = $value->full_name;
                            $garageCode = $value->garage_code;


                            $ticketCount += intval($value->trx_count);
                            if ($value->rate_description == 'Grace Period') {
                                $finalCodes5[$key]['Rate'] = $value->rate_description;
                                $finalCodes5[$key]['No of Tickets'] = floatval('0.0');
                                $finalCodes5[$key]['Ticket Amount ($)'] = floatval('0.0');
                                $finalCodes5[$key]['Gross Amount ($)'] = floatval('0.0');
                                $finalCodes5[$key]['Processing Fees ($)'] = floatval('0.0');
                                $finalCodes5[$key]['Net Amount ($)'] = floatval('0.0');
                                $finalCodes5[$key]['Discount Amount ($)'] = floatval('0.0');
                            } else {

                                $ticketAmount = floatval($value->total);
                                $grossAmount += floatval($value->grand_total + $value->overstayGrandTotal);
                                $processingFees += floatval($value->processing_fee);
                                $netAmonut += floatval($value->net_amount);
                                $discountAmount += floatval($value->validate_amonut + $value->overstayDiscount);
                                $rr  = $value->rate_description;
                                $finalCodes5[$key]['Rate'] = $value->rate_description;
                                $finalCodes5[$key]['No of Tickets'] = $ticketCount;
                                $finalCodes5[$key]['Ticket Amount ($)'] = floatval($ticketAmount);
                                $finalCodes5[$key]['Gross Amount ($)'] = floatval($grossAmount);
                                $finalCodes5[$key]['Processing Fees ($)'] = floatval($processingFees);
                                $finalCodes5[$key]['Net Amount ($)'] = floatval($netAmonut);
                                $finalCodes5[$key]['Discount Amount ($)'] = $discountAmount > 0 ? floatval($discountAmount) : floatval('0.0');


                                // $this->log->info("rate_description {$value->rate_description} processing_fee {$value->processing_fee}");
                            }
                        }
                    }
                    $this->log->info("rate_description {$rr} GROSS AMOOUNT  {$grossAmount} and PR FEE {$processingFees} ");
                    $totalTicketsNonValidated += $ticketCount;
                    $netValueNonValidated += floatval($netAmonut);
                    $TotalRevenueNonValidated += ($grossAmount);
                    $grossAmountNonValidated += $TotalRevenueNonValidated;
                    $processingFeeNonValidated += floatval($processingFees);
                    $validatedAmountNonValidated += floatval($discountAmount);


                    $ticketAmountNonValidated = '';
                    // $taxFee += $value->tax_fee;
                    // $cashiredProcessingFee += floatval($value->processing_fee);
                    $increment1++;
                }
                $countNonValidated = count($rates);
                // dd($finalCodes5);

                //offline sheet data
                $excelSheetName2 = ucwords('Payment Breakdown');
                $finalCodes2 = [];
                $TotalPaymentReceived = 0;
                $TotalValidatedAmount = 0;
                $cashReportCount = count($cashReport);
                $cardTotal = 0;
                $cardDiscount = 0;
                $cashticketCount = 0;
                $cashTotalRevinue = 0;
                $totalCashServiceAmount = 0;
                $finalCodes2[0]['total'] = 0;
                $finalCodes2[0]['discount'] = 0;
                $finalCodes2[1]['total'] = 0;
                $finalCodes2[1]['discount'] = 0;

                foreach ($cashReport as $key => $value) {
                    if ($value->is_offline_payment == 1) {
                        $finalCodes2[0]['payment_type'] = 'Cash';
                        $finalCodes2[0]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                        $finalCodes2[0]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                    } else if ($value->is_offline_payment == 2 || $value->is_offline_payment == 3) {
                        $finalCodes2[1]['payment_type'] = 'Card';
                        $finalCodes2[1]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                        $finalCodes2[1]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                    }
                    $totalCashServiceAmount += $value->processingFee;
                    $cashticketCount = $cashticketCount + $value->ticketCount;
                    $TotalPaymentReceived +=  $value->sum_offline_amount + $value->overstayGrandTotal;
                    $TotalValidatedAmount +=  $value->discountAmount + $value->overstayDiscount;
                }

                //CC Report sheet data
                // $excelSheetName3 = ucwords(str_replace(' ', '', 'Cc Amount ')) . date('m-d-Y');
                $finalCodes3 = [];
                $TotalCc = 0;
                $ccticketCount = 0;

                $ccReportCount = count($ccReport);
                $cards = ['AMEX', 'DCVR', 'MASTERCARD', 'VISA'];
                $this->log->info("cashierShiftReport " . json_encode($ccReport));
                $this->log->info("cashierShiftReport  CASRD" . json_encode($cards));
                foreach ($cards as $cardkey => $card) {
                    $totalcardPay = $grossAmount = $processingFees = $netAmonut = $discountAmount = $ticketCount = 0;
                    $this->log->info("cashierShiftReport OUTER LOOP  " . $card);
                    foreach ($ccReport as $key => $value) {
                        if ($card == $value->combined_card_type) {
                            $this->log->info("cashierShiftReport INNER LOOP " . json_encode($value->combined_card_type));
                            $ticketCount += intval($value->ticketCount);
                            $discountAmount += floatval($value->discountAmount + $value->overstayDiscount);
                            $processingFees += floatval($value->processingFee);
                            $totalcardPay += floatval($value->total_amount + $value->overstayGrandTotal);

                            $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                            $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                        }
                        // $TotalPaymentReceived = $TotalPaymentReceived+$value->total_amount;
                    }


                    $TotalCc += $totalcardPay;
                    $ccticketCount += $ticketCount;
                    $totalCashServiceAmount += $processingFees;
                    $TotalValidatedAmount += $discountAmount;
                }


                //Non Revenue Report data
                $excelSheetName4 = ucwords('Non Revenue');
                $finalCodes4 = [];

                $validationReportCount = count($validationReport);
                $i = 0;
                $validationAmountTotal = 0;

                $validationPaidAmountTotal = 0;
                $totalGrossAmount = 0;
                $validationTicketTotal = 0;
                $validatedGTotal = 0;
                $totalNetAmount = 0;
                $totalServiceAmount = 0;
                $validatedTotal = 0;

                $gTotal = 0;
                foreach ($validationReport as $key => $value) {

                    $finalCodes4[$i]['Business Name'] = $value->BusinessName;
                    $finalCodes4[$i]['Policy Name'] = '-';
                    $finalCodes4[$i]['No of Tickets'] = floatval($value->ticket_count);
                    $finalCodes4[$i]['Gross Amount ($)'] = floatval($value->total);
                    $finalCodes4[$i]['Processing Fee ($)'] = floatval($value->processingFee);
                    $finalCodes4[$i]['Net Amount ($)'] = floatval($value->total - $value->processingFee);
                    $finalCodes4[$i]['Validated Amount ($)'] = floatval($value->validated_amount);
                    $finalCodes4[$i]['Paid Amount ($)'] = floatval($value->paidAmount);
                    $finalCodes4[$i]['Total Revenue ($)'] =  floatval($value->paidAmount + $value->validated_amount);


                    // $gTotal = $value->paidAmount + $value->validated_amount;
                    $grossTotal = ($value->parking_amount + $value->processingFee) * $value->ticket_count;
                    // $serviceFee = floatval($value->processingFee * $value->ticket_count);

                    $validationTicketTotal += $value->ticket_count;
                    $totalGrossAmount += floatval($value->total);
                    $totalServiceAmount += floatval($value->processingFee);
                    $totalNetAmount += floatval($value->total - $value->processingFee);
                    $validationAmountTotal += floatval($value->validated_amount);
                    $validationPaidAmountTotal += floatval($value->paidAmount);
                    $validatedGTotal += floatval($value->paidAmount + $value->validated_amount);;

                    // $totalGrossAmount += $grossTotal;


                    // policy query according to business
                    $policy_query = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                    sum(t.total) as total,
                    sum(t.grand_total) as paidAmount,
                    sum(t.parking_amount) as parking_amount,
                    sum(t.paid_amount) as validated_amount,t.affiliate_business_id,t.policy_id,
                    p.policy_name as policyName,
                    ab.business_name as BusinessName
                    FROM tickets as t
                    inner join affiliate_business as ab on ab.id = t.affiliate_business_id
                    inner join business_policy as p on p.id = t.policy_id 
                    WHERE t.checkin_time >='$checkInTime' and t.checkin_time <='$checkOutTime' and t.partner_id IN ('$partner_id') and paid_by is not null and t.affiliate_business_id =$value->affiliate_business_id and t.paid_amount > 0 GROUP BY t.affiliate_business_id,t.policy_id order by t.affiliate_business_id";

                    $policyReport = DB::select($policy_query);

                    if (isset($policyReport) && !empty($policyReport)) {
                        $i++;
                        foreach ($policyReport as $k => $policy) {
                            // $gTotal = $policy->paidAmount + $policy->validated_amount;
                            // $grossTotal = ($policy->parking_amount + $policy->processingFee) * $policy->ticket_count;
                            // $serviceFee = floatval($policy->processingFee * $policy->ticket_count);
                            $finalCodes4[$i]['Business Name'] = '';
                            $finalCodes4[$i]['Policy Name'] = $policy->policyName;
                            $finalCodes4[$i]['No of Ticket'] = floatval($policy->ticket_count);
                            $finalCodes4[$i]['Gross Total ($)'] = floatval($policy->total);
                            $finalCodes4[$i]['Service Fees ($)'] = floatval($policy->processingFee);
                            $finalCodes4[$i]['Net Amount ($)'] = floatval($policy->total - $policy->processingFee);
                            $finalCodes4[$i]['Validation Amount ($)'] = floatval($policy->validated_amount);
                            $finalCodes4[$i]['Paid Amount ($)'] = floatval($policy->paidAmount);
                            $finalCodes4[$i]['Total Revenue ($)'] =  floatval($policy->paidAmount + $policy->validated_amount);
                            $i++;
                        }
                    }
                    $i++;
                }


                Excel::create(
                    $excelSheetName,
                    function ($excel) use (
                        $finalCodes1,
                        $color,
                        $finalCodes2,
                        $excelSheetName,
                        $excelSheetName2,
                        $count,
                        $cashReportCount,
                        $totalTickets,
                        $TotalRevenue,
                        $netValue,
                        $processingFee,
                        $taxFee,
                        $locationName,
                        $garageCode,
                        $checkInTime,
                        $ticketAmount,
                        $ticketAmountNonValidated,
                        $grossAmount,
                        $validatedAmount,
                        $TotalPaymentReceived,
                        $finalCodes3,
                        $TotalCc,
                        $ccReportCount,
                        $cashticketCount,
                        $ccticketCount,
                        $excelSheetName4,
                        $finalCodes4,
                        $validationReportCount,
                        $validationPaidAmountTotal,
                        $totalServiceAmount,
                        $validationAmountTotal,
                        $validationTicketTotal,
                        $totalGrossAmount,
                        $TotalValidatedAmount,
                        $validatedGTotal,
                        $totalNetAmount,
                        $totalCashServiceAmount,
                        $grossAmountNonValidated,
                        $countNonValidated,
                        $processingFeeNonValidated,
                        $taxFeeNonValidated,
                        $finalCodes5,
                        $excelSheetName5,
                        $totalTicketsNonValidated,
                        $TotalRevenueNonValidated,
                        $netValueNonValidated,
                        $validatedAmountNonValidated,
                        $cashiredProcessingFee
                    ) {
                        if (isset($finalCodes5) && !empty($finalCodes5)) {



                            // Tickets Cashiered 
                            $excel->sheet($excelSheetName5, function ($sheet5) use (
                                $finalCodes5,
                                $color,
                                $countNonValidated,
                                $totalTicketsNonValidated,
                                $TotalRevenueNonValidated,
                                $netValueNonValidated,
                                $taxFeeNonValidated,
                                $locationName,
                                $garageCode,
                                $ticketAmountNonValidated,
                                $validatedAmountNonValidated,
                                $grossAmountNonValidated,
                                $processingFeeNonValidated
                            ) {

                                $sheet5->cell('A8:D8', function ($row) use ($color) {
                                    $row->setBackground($color);
                                    $row->setFontColor('#ffffff');
                                });
                                $sheet5->cell('A10:G10', function ($row) use ($color) {
                                    $row->setBackground($color);
                                    $row->setFontColor('#ffffff');
                                });
                                $colorCode = $countNonValidated + 11;
                                $row_name = 'A' . $colorCode . ':G' . $colorCode;
                                $sheet5->cell($row_name, function ($row) use ($color) {
                                    $row->setBackground($color);
                                    $row->setFontColor('#ffffff');

                                    $row->setFont(array(
                                        'family'     => 'Calibri',
                                        'size'       => '12',
                                        'bold'       =>  true
                                    ));
                                });

                                $sheet5->row(1, array('', ''));
                                $sheet5->row(2, array('', ''));
                                $sheet5->row(3, array('Date', date('d-m-Y', strtotime('now'))));
                                $sheet5->row(4, array('Location Name', $locationName));
                                $sheet5->row(5, array('Location ID', $garageCode));
                                $sheet5->row(6, array('', '', '', ''));
                                $sheet5->row(7, array('', '', '', ''));
                                $sheet5->row(8, array('Transient Counter', $totalTicketsNonValidated, 'Ticket Revenue', $TotalRevenueNonValidated));

                                $sheet5->row(9, array('', '', '', ''));
                                $sheet5->fromArray($finalCodes5, null, 'A10', false, true);
                                $sheet5->row($countNonValidated + 11, array('Total', $totalTicketsNonValidated, '', $TotalRevenueNonValidated, $processingFeeNonValidated, $netValueNonValidated, $validatedAmountNonValidated));
                            });

                            // Validation Report sheet
                            $excel->sheet($excelSheetName4, function ($sheet4) use ($garageCode, $color, $locationName, $excelSheetName4, $finalCodes4, $validationReportCount, $validationPaidAmountTotal, $totalServiceAmount, $validationAmountTotal, $validationTicketTotal, $totalGrossAmount, $validatedGTotal, $totalNetAmount) {
                                $sheet4->cell('A7:B7', function ($row) use ($color) {
                                    $row->setBackground($color);
                                    $row->setFontColor('#ffffff');
                                });
                                $colorCode = count($finalCodes4);
                                $row_name = 'A9:I9';
                                $sheet4->cell($row_name, function ($row) use ($color) {
                                    $row->setBackground($color);
                                    $row->setFontColor('#ffffff');
                                    $row->setFont(array(
                                        'family'     => 'Calibri',
                                        'size'       => '12',
                                        'bold'       =>  true
                                    ));
                                });
                                $count = count($finalCodes4);
                                $colorCode = $count + 10;
                                $row_name = 'A' . $colorCode . ':I' . $colorCode;
                                $sheet4->cell($row_name, function ($row) use ($color) {
                                    $row->setBackground($color);
                                    $row->setFontColor('#ffffff');
                                    $row->setFont(array(
                                        'family'     => 'Calibri',
                                        'size'       => '12',
                                        'bold'       =>  true
                                    ));
                                });

                                $sheet4->row(1, array('', ''));
                                $sheet4->row(2, array('', ''));
                                $sheet4->row(3, array('Date', date('d-m-Y', strtotime('now'))));
                                $sheet4->row(4, array('Location Name', $locationName));
                                $sheet4->row(5, array('Location ID', $garageCode));
                                $sheet4->row(7, array('Transient Counter', $validationTicketTotal));
                                $sheet4->fromArray($finalCodes4, null, 'A9', false, true);
                                $sheet4->row(count($finalCodes4) + 10, array("", 'Total', $validationTicketTotal, $totalGrossAmount, $totalServiceAmount, $totalNetAmount, $validationAmountTotal, $validationPaidAmountTotal, $validatedGTotal));
                            });

                            // Payment Breakdown

                            $excel->sheet($excelSheetName2, function ($sheet2) use (
                                $TotalPaymentReceived,
                                $cashticketCount,
                                $color,
                                $finalCodes2,
                                $finalCodes3,
                                $TotalCc,
                                $ccReportCount,
                                $locationName,
                                $garageCode,
                                $ccticketCount,
                                $validationTicketTotal,
                                $totalGrossAmount,
                                $validationPaidAmountTotal,
                                $validationAmountTotal,
                                $TotalValidatedAmount,
                                $totalCashServiceAmount,
                                $totalServiceAmount,
                                $processingFeeNonValidated,
                                $grossAmountNonValidated

                            ) {

                                $row_name = 'A11:B11';
                                $sheet2->setWidth(array(
                                    'A'     => 25,
                                    'B'     =>  32,
                                    'C'     =>  15,
                                    'D'     =>  14,
                                    'E'     =>  16,
                                    'F'     =>  12,
                                ));
                                $sheet2->cell($row_name, function ($row) use ($color) {

                                    $row->setFont(array(
                                        'family'     => 'Calibri',
                                        'size'       => '12',
                                        'bold'       =>  true
                                    ));

                                    $row->setFontWeight('bold');
                                    // $row->setValignment('center');

                                });

                                // $sheet2->getStyle('A:B')->getAlignment()->setHorizontal('center');

                                // $row_name = 'A12:B12';
                                // $sheet2->cell($row_name, function ($row) use($color){
                                //     $row->setBackground($color);
                                //     $row->setFontColor('#ffffff');
                                // });

                                // $row_name = 'D12:F12';
                                // $sheet2->cell($row_name, function ($row) use($color){
                                //     $row->setBackground($color);
                                //     $row->setFontColor('#ffffff');
                                // });

                                $row_name = 'A7:A8';
                                $sheet2->cell($row_name, function ($row) use ($color) {
                                    $row->setFont(array(
                                        'bold'       =>  true
                                    ));
                                });

                                $row_name = 'E7:E8';
                                $sheet2->cell($row_name, function ($row) use ($color) {
                                    $row->setFont(array(
                                        'bold'       =>  true
                                    ));
                                });

                                $row_name = 'D11:F11';
                                $sheet2->cell($row_name, function ($row) use ($color) {
                                    $row->setFont(array(
                                        'family'     => 'Calibri',
                                        'size'       => '12',
                                        'bold'       =>  true
                                    ));
                                    $row->setAlignment('center');
                                    $row->setFontWeight('bold');
                                });

                                // cash total color
                                $count = count($finalCodes2);
                                $colorCode = $count + 13;
                                $row_name = 'D' . $colorCode . ':F' . $colorCode;
                                $sheet2->cell($row_name, function ($row) use ($color) {
                                    $row->setBackground($color);
                                    $row->setFontColor('#ffffff');
                                    $row->setFont(array(
                                        'family'     => 'Calibri',
                                        'size'       => '12',
                                        'bold'       =>  true
                                    ));
                                });

                                // card total
                                $count2 = count($finalCodes3);
                                $colorCode = $count2 + 13;
                                $row_name = 'A' . $colorCode . ':B' . $colorCode;
                                $sheet2->cell($row_name, function ($row) use ($color) {
                                    $row->setBackground($color);
                                    $row->setFontColor('#ffffff');
                                    $row->setFont(array(
                                        'family'     => 'Calibri',
                                        'size'       => '12',
                                        'bold'       =>  true
                                    ));
                                });
                                $sheet2->cell('A12:F12', function ($row) use ($color) {
                                    $row->setFont(array(
                                        'bold'       =>  true
                                    ));
                                });



                                $sheet2->row(1, array('', ''));
                                $sheet2->row(2, array('', ''));
                                $sheet2->row(3, array('Date', date('d-m-Y', strtotime('now'))));
                                $sheet2->row(4, array('Location Name', $locationName));
                                $sheet2->row(5, array('Location ID', $garageCode));
                                $sheet2->row(6, array('', ''));
                                $sheet2->row(7, array('', ''));

                                // Total Payment Received
                                $totalPayment = $TotalPaymentReceived + $TotalCc;
                                // $totalPayment = $grossAmountNonValidated;
                                $sheet2->setCellValue('A7', 'Total Payment Received ($)');
                                $sheet2->setCellValue('B7', $totalPayment);
                                // total validated Amount
                                $sheet2->setCellValue('A8', 'Total Validated Amt ($)');
                                $sheet2->setCellValue('B8', $validationAmountTotal);

                                $sheet2->setCellValue('E7', 'Total Revenue ($)');
                                $sheet2->setCellValue('F7', $totalPayment + $validationAmountTotal);

                                $sheet2->setCellValue('E8', 'Net Revenue ($)');
                                $sheet2->setCellValue('F8', floatval(($totalPayment + $validationAmountTotal) - ($totalServiceAmount + $processingFeeNonValidated)));

                                // $sheet2->setCellValue('G7', 'processingFee ($)');
                                // $sheet2->setCellValue('H7', $processingFeeNonValidated);

                                // $sheet2->setCellValue('G8', 'totalServiceAmount ($)');
                                // $sheet2->setCellValue('H8', $totalServiceAmount);

                                $sheet2->cell('D11:F11', function ($cell) use ($color) {
                                    $cell->setAlignment('center');
                                    $cell->setFontWeight('bold');
                                });
                                $sheet2->mergeCells('A11:B11');
                                $sheet2->mergeCells('D11:F11');

                                $sheet2->row(11, array('Non-Cash Receipts'));
                                $sheet2->setCellValue('D11', 'Cash Receipts');
                                $sheet2->setCellValue('A12', 'Card Type');
                                $sheet2->setCellValue('B12', 'Total ($)');

                                $sheet2->setCellValue('D12', 'Payment Type');
                                $sheet2->setCellValue('E12', 'Total ($)');
                                $sheet2->setCellValue('F12', 'Discount ($)');


                                $i = 13;
                                $j = 13;
                                foreach ($finalCodes3 as $key => $value) {
                                    $sheet2->setCellValue('A' . $i, $value['no_cash_receipts']);
                                    $sheet2->setCellValue('B' . $i, $value['total']);
                                    $i++;
                                }
                                foreach ($finalCodes2 as $key => $val) {
                                    $sheet2->setCellValue('D' . $j, $val['payment_type']);
                                    $sheet2->setCellValue('E' . $j, $val['total']);
                                    $sheet2->setCellValue('F' . $j, $val['discount']);
                                    $j++;
                                }

                                $sheet2->row($i, array('Total', $TotalCc));

                                $sheet2->setCellValue('D' . $j, 'Total');

                                $sheet2->setCellValue('E' . $j, $TotalPaymentReceived);

                                $sheet2->setCellValue('F' . $j, ($TotalValidatedAmount));
                            });

                            // CC Revinue Report
                            // $excel->sheet($excelSheetName3, function ($sheet3) use ($excelSheetName3,$TotalPaymentReceived,$cashticketCount,$color,$finalCodes2,$finalCodes3,$TotalCc,$ccReportCount,$locationName, $garageCode,$ccticketCount,
                            // $validationTicketTotal,$totalGrossAmount,$validationPaidAmountTotal,$totalServiceAmount,$validationAmountTotal,$TotalValidatedAmount) {


                            // });




                        }
                        // else {
                        //     $this->log->info("No Record Found ");
                        // }
                    }
                )->store('xls')->download('xls');
                // exit();
            } catch (\Throwable $th) {
                // echo $th->getMessage();
                // $this->log->error('Exception ' . $th->getMessage());
                throw new ApiGenericException($th->getMessage());
            }
        } else {
            // Partner
            $partner_id = isset($request->partner_id) ? $request->partner_id : self::PARTNET_ID;
            $fromdate = isset($request->fromDate) ? date('Y-m-d', strtotime($request->fromDate))  : date('Y-m-d');
            $toDate = isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate))  : date('Y-m-d');
            // $checkInTime  =  '2023-06-01 00-00-00';
            $checkInTime  = date('Y-m-d', strtotime($fromdate)) . ' 00-00-00';
            // $checkInTime  = $checkinDate . ' 00-00-00';
            $checkOutTime    = date('Y-m-d', strtotime($toDate)) . ' 23-59-59';
            if (!$partner_id) {
                throw new ApiGenericException('Partner detail not found.', 500);
            }
            // if (!$checkinDate) {
            //     throw new ApiGenericException('Please select date.', 500);
            // }
            $sql_query = "SELECT count(td.rate_description) as trx_count, td.rate_description,td.rate_amount, t.total as total, t.tax_fee as tax_fee, t.processing_fee as processing_fee, sum(t.grand_total) as grand_total, sum(t.discount_amount) as discountAmount, f.full_name,f.garage_code,
            IF(sum(t.paid_amount) >0 ,sum(t.paid_amount), 0) as validated_amount
               from transaction_data as td
               inner join tickets as t on t.id = td.ticket_id
               inner join facilities as f on f.id = td.facility_id
               where td.partner_id IN ('$partner_id') and t.checkin_time >='$checkInTime' and t.checkin_time <='$checkOutTime' and t.is_checkout ='1' group by td.rate_description"; #td.rate_description
            // echo $sql_query;
            // die();
            $reports = DB::select($sql_query);
            // return $reports;
            try {
                // $reports = DB::select($sql_query1);
                $count = count($reports);
                if ($count <= 0) {
                    throw new ApiGenericException('No record found');
                }
                $excelSheetName = ucwords(str_replace(' ', '', 'DailyRevenueReport')) . date('m-d-Y');
                $finalCodes1 = [];
                $increment1 = 1;
                $TotalRevenue = 0;
                $totalTickets = 0;
                $netValue = 0;
                $ticketAmount = 0;
                $grossAmount = 0;
                $validatedAmount = 0;
                $netValue = 0;
                $processingFee = 0;
                $taxFee = 0;
                $locationName = '';
                $garageCode = '';
                foreach ($reports as $key => $value) {
                    // $finalCodes1[$key]['No.'] = $increment1;
                    //$finalCodes1[$key]['month.'] = $value->months;
                    $locationName = $value->full_name;
                    $garageCode = $value->garage_code;
                    $finalCodes1[$key]['Rate'] = $value->rate_description;
                    $finalCodes1[$key]['No of Tickets'] = intval($value->trx_count);

                    // $finalCodes1[$key]['Tax Fee'] = intval($value->tax_fee);
                    if ($value->rate_description == 'Grace Period') {
                        $finalCodes1[$key]['Ticket Amount'] = 0;
                        $finalCodes1[$key]['Gross Amount'] = 0;
                        $finalCodes1[$key]['Processing Fee'] = 0;
                        $finalCodes1[$key]['Net Value'] = 0;
                        $finalCodes1[$key]['Validated Amount'] = 0;
                        // $finalCodes1[$key]['Total'] = 0;
                    } else {
                        $finalCodes1[$key]['Ticket Amount'] = floatval($value->total);;
                        $finalCodes1[$key]['Gross Amount'] = floatval($value->grand_total);;
                        $finalCodes1[$key]['Processing Fee'] = floatval($value->processing_fee * $value->trx_count);
                        $finalCodes1[$key]['Net Value'] = intval($value->rate_amount * $value->trx_count);
                        $finalCodes1[$key]['Validated Amount'] = floatval($value->validated_amount);;
                        // $finalCodes1[$key]['Total'] = (floatval($value->grand_total) - floatval($value->validate_amonut));
                        $TotalRevenue += ($value->grand_total);
                        $this->log->info(" rate_description {$value->rate_description} processing_fee {$value->processing_fee}");
                    }
                    $totalTickets += $value->trx_count;
                    $netValue += ($value->rate_amount * $value->trx_count);
                    $ticketAmount += $value->total;
                    $grossAmount += $value->grand_total;
                    $validatedAmount += $value->validated_amount;
                    // $taxFee += $value->tax_fee;
                    $processingFee += floatval($value->processing_fee * $value->trx_count);
                    $increment1++;
                }
                // return $finalCodes1;
                // $finalCodes1[$count + 1]['Rate'] = '';
                // $finalCodes1[$count + 1]['No of Tickets'] = '0';
                // $finalCodes1[$count + 1]['Net Value'] = '0';
                // $finalCodes1[$count + 1]['Processing Fee'] = '0';
                // $finalCodes1[$count + 1]['Total'] = '0';
                Excel::create(
                    $excelSheetName,
                    function ($excel) use ($finalCodes1, $excelSheetName, $count, $totalTickets, $TotalRevenue, $netValue, $processingFee, $taxFee, $locationName, $garageCode, $checkInTime, $ticketAmount, $grossAmount, $validatedAmount) {
                        if (isset($finalCodes1) && !empty($finalCodes1)) {
                            $excel->sheet($excelSheetName, function ($sheet) use ($finalCodes1, $count, $totalTickets, $TotalRevenue, $netValue, $processingFee, $taxFee, $locationName, $garageCode, $checkInTime, $ticketAmount, $grossAmount, $validatedAmount) {
                                $sheet->cell('A8:D8', function ($row) {
                                    $row->setBackground('#0000FF');
                                    $row->setFontColor('#ffffff');
                                });
                                $sheet->cell('A10:G10', function ($row) {
                                    $row->setBackground('#0000FF');
                                    $row->setFontColor('#ffffff');
                                });
                                $colorCode = $count + 11;
                                $row_name = 'A' . $colorCode . ':G' . $colorCode;
                                $sheet->cell($row_name, function ($row) {
                                    $row->setBackground('#0000FF');
                                    $row->setFontColor('#ffffff');
                                    // $row->setFontSize(16);
                                    // Set font
                                    $row->setFont(array(
                                        'family'     => 'Calibri',
                                        'size'       => '12',
                                        'bold'       =>  true
                                    ));
                                });
                                // Format a range with e.g. leading zeros
                                // $sheet->setColumnFormat(array(
                                //     'B10:F10' => '0.00'
                                // ));
                                $sheet->row(1, array('', 'Daily Revenue Report'));
                                $sheet->row(2, array('', ''));
                                $sheet->row(3, array('Date', date('d-m-Y', strtotime('now'))));
                                $sheet->row(4, array('Location Name', $locationName));
                                $sheet->row(5, array('Location ID', $garageCode));
                                $sheet->row(6, array('', '', '', ''));
                                $sheet->row(7, array('', '', '', ''));
                                $sheet->row(8, array('Transient Counter', $totalTickets, 'Ticket Revenue', $TotalRevenue));
                                // $sheet->row(7, array('Total', $totalTickets, 'Total Revenue', $TotalRevenue));
                                $sheet->row(9, array('', '', '', ''));
                                $sheet->fromArray($finalCodes1, null, 'A10', false, true);
                                $sheet->row($count + 11, array('Total', $totalTickets, $ticketAmount, $grossAmount, $processingFee, $netValue, $validatedAmount));
                            });
                        } else {
                            $this->log->info("No Record Found ");
                        }
                    }
                )->store('xls')->download('xls');
                // exit();
            } catch (\Throwable $th) {
                // echo $th->getMessage();
                // $this->log->error('Exception ' . $th->getMessage());
                throw new ApiGenericException($th->getMessage());
            }
        }

        // return $reports;
    }

    // vijay - 30-06-2023- NOt In use for now
    public function validationReportClerkWise(Request $request)
    {

        $this->log->info("Received request for payment : " . json_encode($request->all()));
        $ClientSecret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();

        $fromDate = isset($request->fromDate) ? date('Y-m-d', strtotime($request->fromDate))  : date('Y-m-') . '01';
        $toDate = isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate))  : date('Y-m-') . '31';
        $fromDate  = $fromDate . ' 00-00-00';
        $toDate  = $toDate . ' 23-59-59';

        $partner_id = isset($request->partner_id) ? $request->partner_id : self::PARTNET_ID;
        $business_id = isset($request->business_id) ? $request->business_id : 0;
        $clerk_id = isset($request->clerk_id) ? $request->clerk_id : 0;
        $facility = isset($request->facility_id) ? $request->facility_id : '0';
        // $validateBy = isset($request->validateBy) ? $request->validateBy : '0';


        if (!$partner_id) {
            throw new ApiGenericException('Partner detail not found.', 500);
        }
        $AffiliateBusiness = $Cleark = '';
        if ($business_id > 0) {
            // $AffiliateBusiness  = AffiliateBusiness::where(['id' => $business_id])->first();
            $AffiliateBusiness  = User::where(['created_by' => $partner_id, 'user_type' => '10', 'business_id' => $business_id])->first();
        }

        $validateByQuery = '';
        if ($partner_id > 0 && $facility  > 0 && $business_id > 0 && $clerk_id > 0) {
            $validateByQuery = " AND t.partner_id ='$partner_id' AND t.facility_id ='$facility' AND (t.affiliate_business_id ='$business_id' AND t.paid_by ='$clerk_id') ";
        } else if ($partner_id > 0 && $facility  > 0 && $business_id > 0) {
            $validateByQuery = " AND t.partner_id ='$partner_id' AND t.facility_id ='$facility' AND t.affiliate_business_id ='$business_id'    ";
        } else if ($partner_id  > 0 && $business_id > 0 && $clerk_id > 0) {
            if ($clerk_id && $business_id) {
                $validateByQuery = " AND t.partner_id ='$partner_id' AND (t.affiliate_business_id ='$business_id' AND t.paid_by ='$clerk_id') ";
            } else if ($AffiliateBusiness) {
                $validateByQuery = " AND t.partner_id ='$partner_id' AND t.affiliate_business_id ='$business_id'  ";
            } else if ($clerk_id) {
                $validateByQuery = " AND t.partner_id ='$partner_id' AND  t.paid_by ='$clerk_id' ";
            }
        } else if ($partner_id > 0 && $facility  > 0) {
            $validateByQuery = " AND t.partner_id ='$partner_id' AND t.facility_id ='$facility' ";
        } else if ($partner_id > 0 && $business_id > 0) {
            $validateByQuery = " AND t.partner_id ='$partner_id' AND t.affiliate_business_id ='$business_id' ";
        } else {
            $validateByQuery = "AND t.partner_id ='$partner_id' ";
        }
        // echo $validateByQuery;
        // die;

        $sql_query = "SELECT t.ticket_number, f.full_name as GarageName, t.checkin_time,t.checkout_time as actual_checkout_datetime ,t.length as total_length, t.orignal_length, t.processing_fee, t.tax_fee,t.total, t.grand_total,t.discount_amount, t.promocode, t.paid_amount as validate_amount, t.payment_date, IF(t.paid_by >0 ,(SELECT IF(u.user_type='10',ab.business_name,u.name) as name  from users as u inner join affiliate_business as ab ON ab.id = u.business_id where u.id IN (t.paid_by) and u.user_type in (10,8 ,4,3 ) ),'-') as paid_by_affiliate,
        IF(t.policy_id > 0, (SELECT policy_name from business_policy WHERE id IN (t.policy_id)),'' ) as policyName,
        t.paid_remark, t.paid_date,t.remark,ab.business_name
        FROM tickets as t
        inner join facilities as f on f.id = t.facility_id
        Left join affiliate_business ab on ab.id=t.affiliate_business_id 
        WHERE t.paid_amount > 0 and t.checkin_time >='$fromDate' and t.checkin_time <='$toDate' $validateByQuery order by t.id DESC,t.affiliate_business_id ASC";
        $reports = DB::select($sql_query);
        // return $reports;
        // die();
        try {
            // $reports = DB::select($sql_query1);
            $count = count($reports);
            if ($count <= 0) {
                throw new ApiGenericException('No record found');
            }
            $excelSheetName = ucwords(str_replace(' ', '', 'ValidationReport')) . date('m-d-Y');
            $finalCodes1 = [];
            $increment1 = 1;
            $ticketAmount  = $amountPaid = $processingFee = $validatedAmount = 0;
            foreach ($reports as $key => $value) {
                $finalCodes1[$key]['Garage Name'] = $value->GarageName;
                $finalCodes1[$key]['Business Name'] = $value->business_name;
                $finalCodes1[$key]['Validation Name'] = $value->policyName;
                $finalCodes1[$key]['Validator Name'] = $value->paid_by_affiliate;
                $finalCodes1[$key]['Validation Date'] = Carbon::parse($value->paid_date)->format('m-d-Y H:i:s');;
                $finalCodes1[$key]['Ticket Number'] = $value->ticket_number;
                $finalCodes1[$key]['Check-In Date'] = Carbon::parse($value->checkin_time)->format('m-d-Y H:i:s');
                $finalCodes1[$key]['Check-Out Date'] = Carbon::parse($value->actual_checkout_datetime)->format('m-d-Y H:i:s');;
                $finalCodes1[$key]['Ticket Amount ($)'] = floatval($value->total);
                $finalCodes1[$key]['Amount Paid ($)'] = floatval($value->grand_total);
                $finalCodes1[$key]['Processing Fee ($)'] = floatval($value->processing_fee);
                $finalCodes1[$key]['Validated Amount ($)'] = floatval($value->validate_amount);
                // $finalCodes1[$key]['Remark'] = $value->remark;

                $increment1++;
                $ticketAmount += floatval($value->total);
                $amountPaid += floatval($value->grand_total);
                $processingFee += floatval($value->processing_fee);
                $validatedAmount += floatval($value->validate_amount);
            }
            $finalCodes1[$count + 1]['Garage Name'] = '';
            $finalCodes1[$count + 1]['Business Name'] = '';
            $finalCodes1[$count + 1]['Validation Name'] = '';
            $finalCodes1[$count + 1]['Validator Name'] = '';
            $finalCodes1[$count + 1]['Validation Date'] = '';
            $finalCodes1[$count + 1]['Ticket Number'] = '';
            $finalCodes1[$count + 1]['Check-In Date'] = '';
            $finalCodes1[$count + 1]['Check-Out Date'] = 'Total';
            $finalCodes1[$count + 1]['Ticket Amount ($)'] = floatval($ticketAmount);
            $finalCodes1[$count + 1]['Amount Paid ($)'] = floatval($amountPaid);
            $finalCodes1[$count + 1]['Processing Fee ($)'] = floatval($processingFee);
            $finalCodes1[$count + 1]['Validated Amount ($)'] = floatval($validatedAmount);
            $row = $count + 2;
            Excel::create(
                $excelSheetName,
                function ($excel) use ($finalCodes1, $excelSheetName, $row) {
                    if (isset($finalCodes1) && !empty($finalCodes1)) {
                        $excel->sheet($excelSheetName, function ($sheet) use ($finalCodes1, $row) {
                            $colorCode = $row;
                            $row_name = 'I' . $colorCode . ':L' . $colorCode;
                            $sheet->cell($row_name, function ($row) {
                                // Set font
                                $row->setFont(array(
                                    'bold'       =>  true
                                ));
                            });
                            $sheet->fromArray($finalCodes1, null, 'A1', false, true);
                        });
                    } else {
                        $this->log->info("No Record Found ");
                    }
                }
            )->store('xls')->download('xls');
            // exit();
        } catch (\Throwable $th) {
            // echo $th->getMessage();
            // $this->log->error('Exception ' . $th->getMessage());
            throw new ApiGenericException($th->getMessage());
        }
        // return $reports;
    }

    public function validationReportClerkWisePDF(Request $request)
    {

        $this->log->info("Received request for payment : " . json_encode($request->all()));
        $ClientSecret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();

        $fromDate = isset($request->fromDate) ? date('Y-m-d', strtotime($request->fromDate))  : date('Y-m-') . '01';
        $toDate = isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate))  : date('Y-m-') . '31';
        $fromDate  = $fromDate . ' 00-00-00';
        $toDate  = $toDate . ' 23-59-59';

        $partner_id = isset($request->partner_id) ? $request->partner_id : self::PARTNET_ID;
        $business_id = isset($request->business_id) ? $request->business_id : 0;
        $clerk_id = isset($request->clerk_id) ? $request->clerk_id : 0;
        $facility = isset($request->facility_id) ? $request->facility_id : '0';

        if (!$partner_id) {
            throw new ApiGenericException('Partner detail not found.', 500);
        }
        $AffiliateBusiness = $Cleark = '';
        if ($business_id > 0) {
            // $AffiliateBusiness  = AffiliateBusiness::where(['id' => $business_id])->first();
            $AffiliateBusiness  = User::where(['created_by' => $partner_id, 'user_type' => '10', 'business_id' => $business_id])->first();
        }

        $validateByQuery = '';
        if ($partner_id > 0 && $facility  > 0 && $business_id > 0 && $clerk_id > 0) {
            $validateByQuery = " AND t.partner_id ='$partner_id' AND t.facility_id ='$facility' AND (t.affiliate_business_id ='$business_id' AND t.paid_by ='$clerk_id') ";
        } else if ($partner_id > 0 && $facility  > 0 && $business_id > 0) {
            $validateByQuery = " AND t.partner_id ='$partner_id' AND t.facility_id ='$facility' AND t.affiliate_business_id ='$business_id'    ";
        } else if ($partner_id  > 0 && $business_id > 0 && $clerk_id > 0) {
            if ($clerk_id && $business_id) {
                $validateByQuery = " AND t.partner_id ='$partner_id' AND (t.affiliate_business_id ='$business_id' AND t.paid_by ='$clerk_id') ";
            } else if ($AffiliateBusiness) {
                $validateByQuery = " AND t.partner_id ='$partner_id' AND t.affiliate_business_id ='$business_id'  ";
            } else if ($clerk_id) {
                $validateByQuery = " AND t.partner_id ='$partner_id' AND  t.paid_by ='$clerk_id' ";
            }
        } else if ($partner_id > 0 && $facility  > 0) {
            $validateByQuery = " AND t.partner_id ='$partner_id' AND t.facility_id ='$facility' ";
        } else if ($partner_id > 0 && $business_id > 0) {
            $validateByQuery = " AND t.partner_id ='$partner_id' AND t.affiliate_business_id ='$business_id' ";
        } else {
            $validateByQuery = "AND t.partner_id ='$partner_id' ";
        }
        // echo $validateByQuery;
        // die;

        $sql_query = "SELECT t.ticket_number, f.full_name as GarageName, t.checkin_time,t.checkout_time as actual_checkout_datetime ,t.length as total_length, t.orignal_length, t.processing_fee, t.tax_fee,t.total, t.grand_total,t.discount_amount, t.promocode, t.paid_amount as validate_amount, t.payment_date, IF(t.paid_by >0 ,(SELECT IF(u.user_type='10',ab.business_name,u.name) as name  from users as u inner join affiliate_business as ab ON ab.id = u.business_id where u.id IN (t.paid_by) and u.user_type in (10,8 ,4,3 ) ),'-') as paid_by_affiliate,
        IF(t.policy_id > 0, (SELECT policy_name from business_policy WHERE id IN (t.policy_id)),'' ) as policyName,
        t.paid_remark, t.paid_date,t.remark,t.affiliate_business_id as business_id,ab.business_name
        FROM tickets as t
        inner join facilities as f on f.id = t.facility_id        
        Left join affiliate_business ab on ab.id=t.affiliate_business_id 
        WHERE t.paid_amount > 0 and t.checkin_time >='$fromDate' and t.checkin_time <='$toDate' $validateByQuery order by t.id DESC,t.affiliate_business_id ASC";
        $reports = DB::select($sql_query);
        // return $reports;
        // die();
        try {
            // $reports = DB::select($sql_query1);
            $this->log->info("Excel Validation Report Query : " . $sql_query);
            $count = count($reports);
            if ($count <= 0) {
                throw new ApiGenericException('No record found');
            }
            if ($request->partner_id) {
                $partner_id = $request->partner_id;
            } else {
                $partner_id = Auth::user()->id;
                if (Auth::user()->user_type == '4') {
                    $partner_id = Auth::user()->created_by;
                }
            }
            // 
            if ($business_id > 0) {
                $AffiliateBusiness = AffiliateBusiness::select('id', 'business_name')->where(['partner_id' => $partner_id, 'id' => $business_id])->get();
            } else {
                $AffiliateBusiness = AffiliateBusiness::select('id', 'business_name')->where(['partner_id' => $partner_id])->get();
            }
            // $AffiliateBusiness = AffiliateBusiness::select('id', 'business_name')->where(['partner_id' => $partner_id])->get();
            $settings = BrandSetting::where('user_id', $partner_id)->first();
            $html = view("download.atlanta-checkin-checkout", ["validationReport" => $reports,  'brand_setting' => $settings, "request_param" => $request, 'AffiliateBusiness' => $AffiliateBusiness])->render();
            $image = app()->make(Pdf::class);
            $pdf = $image->getOutputFromHtmlString($html);
            return $pdf;
            // exit();
        } catch (\Throwable $th) {
            // echo $th->getMessage();
            // $this->log->error('Exception ' . $th->getMessage());
            throw new ApiGenericException($th->getMessage());
        }
        // return $reports;
    }


    function populateValidationFields($ticket, $rate, $facility)
    {
        $newPrice = '0.0';
        $totalPrice = $this->request->total;
        $processingFee = $ticket->getProcessingFee();   // to get prcessing free channel wise need to
        $taxRate = $ticket->getTaxRate($rate);          // to get tax price
        $diffInHours = $ticket->getCheckOutCurrentTime(true);
        $ticketPrice = ($rate['price'] + $processingFee + $taxRate);
        $grandTotal = $ticket->grand_total > 0 ? $ticket->grand_total : $totalPrice;
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

        $returnData = [];
        if ($ticket->paid_type == '0') {    // Full Validation case 
            if ($rate['price'] > 0) {
                $returnData['total'] = $ticketPrice;
                $returnData['parking_amount'] = $rate['price'];
                $returnData['paid_amount'] = ($totalPrice + $processingFee + $taxRate);
            } else {
                $returnData['total'] = $newPrice;
                $returnData['parking_amount'] = $newPrice;
                $returnData['paid_amount'] = $newPrice;
            }
            $returnData['grand_total'] = $newPrice;
        } else if ($ticket->paid_type == '1') { // Hours  
            $diff_in_hours = $diffInHours - $ticket->paid_hour;
            $hoursRate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, 0);
            $discountPrice = $rate['price'] - $hoursRate['price'];
            $returnData['total'] = $ticketPrice;
            $returnData['parking_amount'] = $rate['price'];
            $returnData['paid_amount'] = $discountPrice;
            $returnData['length'] = $diff_in_hours;
            $returnData['grand_total'] = $grandTotal;
        } else if ($ticket->paid_type == '2') { // Amount
            $returnData['total'] = $ticketPrice;
            $returnData['parking_amount'] = $rate['price'];
            $returnData['paid_amount'] = $ticket->paid_amount;
            $returnData['grand_total'] = $grandTotal;
        } else if ($ticket->paid_type == '3') { // Percentage
            $payableAmount = 0;
            $perGrandTotal = 0;
            $validatedAmount = number_format((($ticketPrice * $ticket->paid_percentage) / 100), 2);
            if (($ticket->max_validated_amount > 0) && $ticket->max_validated_amount != '') {
                if ($validatedAmount <= $ticket->max_validated_amount) {
                    $payableAmount = number_format($ticketPrice - $validatedAmount, 2);
                } else {
                    $payableAmount = number_format($ticketPrice - $ticket->max_validated_amount, 2);
                    $validatedAmount = $ticket->max_validated_amount;
                }
            } else {
                $payableAmount = number_format($ticketPrice - $validatedAmount, 2);
            }
            if ($ticket->paid_percentage == '100') { // if 100 % then grand total is zero 
                if ($ticket->max_validated_amount > 0) {
                    $perGrandTotal =  $payableAmount;
                } else {
                    $perGrandTotal =  '0.00';
                }
            } else {
                $perGrandTotal =  $payableAmount;
            }
            $returnData['total'] = $ticketPrice;
            $returnData['parking_amount'] = $rate['price'];
            $returnData['paid_amount'] = $validatedAmount;
            $returnData['grand_total'] = $perGrandTotal;
        } else {
            $returnData['total'] = $ticketPrice;
            $returnData['parking_amount'] = $rate['price'];
            $returnData['paid_amount'] = $newPrice;
            $returnData['grand_total'] = $totalPrice;
        }

        return $returnData;
    }

    //Save Transaction Data for Report
    protected function saveOverstayTransactionData($ticket, $overstayTicket)
    {
        $mainTicket = Ticket::where('ticket_number', $this->request->ticket_number)->first();

        $arrival_time = $ticket->checkin_time;
        $diff_in_hours = $ticket->length;
        $isMember = 0;
        $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);

        $rate_id = isset($rate['id']) ? $rate['id'] : '';
        $rate_description = isset($rate['description']) ? $rate['description'] : '';
        $rate_amount = $this->rate['price'];

        $transaction = TransactionData::where("ticket_id", $ticket->id)->first();
        if (!$transaction) {
            return true;
        }

        $transaction->rate_id = $rate_id;
        $transaction->rate_description = $rate_description;
        $transaction->rate_amount = $this->rate['price'];
        $transaction->total = $ticket->total + $overstayTicket->total;
        $transaction->grand_total = $ticket->grand_total + $overstayTicket->total;
        $transaction->save();
        return $transaction;
    }
}
