<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\ParkEngage\Gate;
use App\Models\ParkEngage\GateHistory;
use App\Classes\ParkengageGateApi;
use App\Models\Facility;
use App\Exceptions\ApiGenericException;
use App\Models\User;

class GateController extends Controller
{


    public function index(Request $request)
    {
        $facility = '';
        $partner_id = '';
        if ($request->partner_id) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12') {
                $facility = \DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                $partner_id = Auth::user()->created_by;
            } elseif (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            }
        }

        //return $partner_id;
        $gate = Gate::with(['cruise'])->whereNull('deleted_at');
        if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12') {
            //$gate = $gate->whereIn("facility_id", $facility);//
            if ($request->facility_id) {
                //	$gate = $gate->where("facility_id", $request->facility_id);//
            } else {
                $gate = $gate->where(function ($query) use ($facility) {
                    //	$query->whereIn("facility_id", $facility);
                });
            }
        }
        if ($request->search != 'NO') {
            if ($request->search == 'Active' || $request->search == 'active') {
                $gate = $gate->where('partner_id', $partner_id)->where('is_active', 1);
            } elseif ($request->search == 'In-Active' || $request->search == 'in-active' || $request->search == 'inactive') {

                $gate = $gate->where('partner_id', $partner_id)->where('is_active', 0);
            } elseif ($request->search == 'exit' || $request->search == 'entry') {
                $gate = $gate->where('partner_id', $partner_id)->where('gate_type', 'like', "%$request->search%");
            } elseif ($request->search == 'Level 1' || $request->search == 'Level') {
                $gate = $gate->where('partner_id', $partner_id)->where('gate_name', 'like', "%$request->search%");
            } else {
                if ($request->search != '') {
                    $gate = $gate->where('partner_id', $partner_id)->where('gate', 'like', "%$request->search%")
                        ->orWhere('remarks', 'like', "%$request->search%");
                }
            }
            if (isset($partner_id) && $partner_id != '') {
                $gate = $gate->where(function ($query) use ($partner_id) {
                    $query->where('partner_id', $partner_id);
                });
            }
        }
        if (isset($partner_id) && $partner_id != '') {
            $gate = $gate->where('partner_id', $partner_id);
        }

        if ($request->sort != '') {

            $gate = $gate->orderBy($request->sort, $request->sortBy);
        } else {
            $gate = $gate->orderBy("id", "DESC");
        }
        $gate = $gate->paginate(10);

        // dd($gate);
        return $gate;
    }

    public function store(Request $request)
    {
        //return $request->all();
        $this->validate($request, Gate::$validation);
        $gateRes = Gate::where('gate', $request->gate_number)->where('facility_id', $request->facility_id)->first();
        if ($gateRes) {
            throw new ApiGenericException("Gate Number Already exists.");
        }
        if (Auth::User()->user_type == '12') {
            $partner_id = Auth::user()->created_by;
        } else {
            $partner_id = Auth::user()->id;
        }
        $data['gate_name'] = $request->gate_name;
        $data['gate'] = $request->gate_number;
        $data['gate_type'] = $request->gate_type;
        $data['facility_id'] = $request->facility_id;
        $data['remarks'] = $request->remarks;
        $data['is_active'] = $request->is_active;
        $data['partner_id'] = $partner_id;
        $data['cruise_id'] = $request->cruise_id;
        $data['is_external_gate'] = $request->is_external_gate == '1' ? $request->is_external_gate : '0';
        $gate = Gate::create($data);
        return $gate;
    }

    public function show($id)
    {
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }
        if (Auth::user()->user_type == '1') {
            $gate = Gate::find($id);
        } else {
            $gate = Gate::where('partner_id', $partner_id)->find($id);
        }

        if (!$gate) {
            throw new ApiGenericException("Gate id  not found.");
        }

        return $gate;
    }

    public function update(Request $request)
    {

        $gate = Gate::find($request->id);

        if (!$gate) {
            throw new ApiGenericException("Gate not found.");
        }


        if ($gate->gate == $request->gate_number) {
            $gate->gate_name = $request->gate_name;
            $gate->gate = $request->gate_number;
            $gate->gate_type = $request->gate_type;
            $gate->facility_id = $request->facility_id;
            $gate->remarks = $request->remarks;
            $gate->is_active = $request->is_active;
            $gate->cruise_id = $request->cruise_id;
            $gate->is_external_gate = $request->is_external_gate == '1' ? $request->is_external_gate : '0';
            $gate->save();
            return $gate;
        } else {
            $gate_number = Gate::where('gate', $request->gate_number)->first();
            if (!$gate_number) {

                $gate->gate_name = $request->gate_name;
                $gate->gate = $request->gate_number;
                $gate->gate_type = $request->gate_type;
                $gate->facility_id = $request->facility_id;
                $gate->remarks = $request->remarks;
                $gate->is_active = $request->is_active;
                $gate->cruise_id = $request->cruise_id;
                $gate->is_external_gate = $request->is_external_gate == '1' ? $request->is_external_gate : '0';
                $gate->save();
                return $gate;
            } else {
                throw new ApiGenericException("Gate Number Already exists.");
            }
        }
    }

    public function destroy($id)
    {
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }
        if (Auth::user()->user_type == '1') {
            $gate = Gate::find($id);
        } else {
            $gate = Gate::where('partner_id', $partner_id)->find($id);
        }

        if ($gate) {
            $gate->delete();
            return "Data successfully deleted.";
        }
        throw new ApiGenericException("Adam not found.");
    }


    public function openGate(Request $request)
    {
        $gateRes = Gate::where('id', $request->gate_id)->where('facility_id', $request->facility_id)->first();
        if (!$gateRes) {
            throw new ApiGenericException("Invalid gate details.");
        }

        if ($request->check_vehicle_enabled == '1') {
            $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $gateRes->gate, '');
            if ($gateStatus == "true") {
            } else {
                throw new ApiGenericException($gateStatus);
            }
        }

        if (Auth::User()->user_type == '12') {
            $partner_id = Auth::user()->created_by;
        } elseif (Auth::User()->user_type == '3') {
            $partner_id = Auth::user()->id;
        } else {
            $partner_id = Auth::user()->id;
        }
        $data['gate_name'] = $gateRes->gate_name;
        $data['gate'] = $gateRes->gate_number;
        $data['gate_type'] = $gateRes->gate_type;
        $data['facility_id'] = $request->facility_id;
        $data['remarks'] = $request->remarks;
        $data['partner_id'] = $partner_id;
        $data['opened_by'] = Auth::user()->id;
        $gate = GateHistory::create($data);


        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $gateRes->gate, '');
        if ($gateStatus == "true") {
        } else {
            throw new ApiGenericException($gateStatus);
        }

        return $gate;
    }


    public function isParkEngageGateAvailable($facility_id, $gate, $phone = '')
    {
        $facility = Facility::where('id', $facility_id)->first();
        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();
        //check third party gate API
        if ($facility->adam_host != '') {
            $params = ['gate_id' => $gate->gate];
            //$this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
            $response = ParkengageGateApi::isvehicleAvailable($params, $facility->adam_host);
            //$this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response" . json_encode($response));
            if ($response['success'] == false) {
                $msg = "The system is not currently available. Please try again later.";
                return $msg;
            }
            if (isset($response['data'][0]) && $response['data'][0] == "true") {
                return true;
            } else {
                $msg = "Your vehicle must be in the garage in front of the " . ucwords($gate->gate_type) . " gate.";
                return $msg;
            }
        }
    }

    public function isParkEngageGateOpen($facility_id, $gate, $phone = '')
    {
        $facility = Facility::where('id', $facility_id)->first();
        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();
        //check third party gate API
        if ($facility->adam_host != '') {
            $params = ['gate_id' => $gate->gate];
            //$this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
            $cmd_params = ['gate_id' => $gate->gate];
            //$this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params" . json_encode($cmd_params));
            if($gate->is_external_gate == '1' || $gate->is_external_gate == 1){
                $command_response = ParkengageGateApi::openExternalGate($cmd_params, $facility->adam_host);
            }else{
                $command_response = ParkengageGateApi::openGate($cmd_params, $facility->adam_host);
            }
            
            //$this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response" . json_encode($command_response));
            if ($command_response['success'] == true) {
                if ($command_response['data'][0] == "true") {
                    return true;
                } else {
                    $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                    return $msg;
                }
            } else {
                $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                return $msg;
            }
        }
    }


    public function gatedFacilityList(Request $request)
    {
        $owner_id = 0;
        $userData = User::where('id', Auth::user()->id)->where('status', 1)->first();
        if ($userData->user_type == '1' || $userData->user_type == '2') {
            if (isset($request->partner_id)) {
                $facilities = Facility::with('facilityType', 'photos')->select('id', 'short_name', 'full_name', 'facility_type_id', 'active')->whereNull('deleted_at')->where('active', 1)->where('is_gated_facility', '1');
                if (isset($request->rm_id)) {
                    $facility = \DB::table('user_facilities')->where('user_id', $request->rm_id)->whereNull('deleted_at')->pluck('facility_id');
                    $facilities = $facilities->where(function ($query) use ($facility) {
                        $query->whereIn('id', $facility);
                    });
                }
                $facilities = $facilities->orderBy('full_name', 'ASC')->get();
            } else {
                $facilities = Facility::with('facilityType', 'photos')->select('id', 'short_name', 'full_name', 'facility_type_id', 'active')->whereNull('deleted_at')->where('active', 1)->where('is_gated_facility', '1')->orderBy('id', 'Desc')->get();
            }
        } else {
            $facility = [];
            if ($userData->user_type == '3') {
                $owner_id = $userData->id;
            } else if ($userData->user_type == '4') {
                $owner_id = $userData->created_by;
                $facility = \DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
            } else if ($userData->user_type == '12') {
                $owner_id = $userData->created_by;
                $facility = \DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
            }
            $facilities = Facility::select('id', 'short_name', 'full_name', 'facility_type_id', 'active')->whereNull('deleted_at')->where('active', 1)->where('is_gated_facility', '1')->where('owner_id', $owner_id);
            if (isset($facility) && !empty($facility)) {
                $facilities = $facilities->where(function ($query) use ($facility) {
                    $query->whereIn('id', $facility);
                });
            }
            $facilities = $facilities->where('is_gated_facility', '1')->orderBy('id', 'Desc')->get();
        }
        return ['facilities' => $facilities];
    }

    public function getFacilityGates(Request $request)
    {
        $gate = Gate::with(['parkingDevice', 'adam'])->where("is_active", "1")->where('facility_id', $request->facility_id)->orderBy('gate_name', "ASC")->get();
        return $gate;
    }
}
