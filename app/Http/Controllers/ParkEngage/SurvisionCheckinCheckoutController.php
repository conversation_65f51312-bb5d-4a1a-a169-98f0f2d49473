<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Auth;
use Response;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use Artisan;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Classes\ParkengageGateApi;
use Storage;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\OauthClient;
use App\Classes\DatacapPaymentGateway;
use App\Jobs\SendSms;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Models\ParkEngage\WorldportLicensePlate;
use App\Models\ParkEngage\KstreetLicensePlate;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\PermitRequest;
//use App\Models\ParkEngage\ColonialLicensePlates;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\FacilityOvernightDuration;
use App\Models\HoursOfOperation;
use App\Models\ParkEngage\TransactionData;
use App\Models\ParkEngage\LPRFeed;
use App\Models\ParkEngage\FacilitySlot;
use App\Services\LPR\LprTransientService;

class SurvisionCheckinCheckoutController extends Controller
{

    protected $log;
    protected $user;
    protected $facility;

    const  PARTNER_ID = 2980;

    use DispatchesJobs;
    const QUEUE_NAME = 'sms-send';
    const QUEUE_ENTRY = 'read-license-plate';

    const  ANTIPASSBACK_COUNT = 2;

    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->request = $request;
        $this->log = $logFactory->setPath('logs/survision')->createLogger('survision');
    }


    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($facility->timezone != '') {
                config(['app.timezone' => $facility->timezone]);
                date_default_timezone_set($facility->timezone);
            } else if ($partnerTimezone->timezone != '') {
                config(['app.timezone' => $partnerTimezone->timezone]);
                date_default_timezone_set($partnerTimezone->timezone);
            }
        }
    }
    //UPBL-87
    public function parkengageSurvisionFeed(Request $request)
    {

        try {
            $this->log->info("parkengageSurvisionFeed Header data --" . json_encode($request->headers->all()));
            $this->log->info("parkengageSurvisionFeed Request received --" . json_encode($request->all()));
            $requestData  = QueryBuilder::removeAtPrefixes($request->all(), $is_header = 0);
            $headerData  = QueryBuilder::removeAtPrefixes($request->headers->all(), $is_header = 1);

            $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("lane_id", $headerData['survision-serial-number'])->first();
            //$exitCount = FacilitySlot::where('license_plate', $requestData['anpr']['decision']['plate'])
            //->where('entry_type', "exit")->count();

            //$entryCount = FacilitySlot::where('license_plate', $requestData['anpr']['decision']['plate'])
            // ->where('entry_type', "entry")->count();
            //check permit is exist or not 
            //$checkExistingPermitTrue = new LprTransientService();
            // $permit = $checkExistingPermitTrue->checkExistingPermit($requestData['anpr']['decision']['plate'], $gate->facility_id, $gate->partner_id);
            // if ($gate->gate_type == "entry" && $exitCount < $entryCount) {
            //     $this->log->info("Duplicate Feed");
            //     return "Duplicate Feed";
            // } else
            // if ($gate->gate_type == "exit" && count($permit) > 0) {
            //     $this->log->info("Permit exists, skipping entry/exit validation.");
            // }
            //  elseif ($gate->gate_type == "exit" && (($entryCount - $exitCount) != 1)) {
            //     $this->log->info("Entry not found");
            //     return "Entry not found";
            // }

            if ($gate) {
                $this->setCustomTimezone($gate->facility_id);
                $this->log->info("Request : " . date("Y-m-d H:i:s"));
                $facility = Facility::find($gate->facility_id);
                if ($facility->is_lpr_enabled == '0' || $facility->is_lpr_enabled == 0) {
                    $this->log->info("lpr is disabled");
                    return $gate->facility_id;
                }
                $res = $this->saveLicensePlate($requestData, $headerData, $gate);


                return $res;
            } else {
                $this->log->info("Lane Id not matched");
            }
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            return $msg;
        }
    }

    public function saveLicensePlate($requestData, $headerData, $gate)
    {
        try {
            $facility = Facility::with(['facilityConfiguration'])->find($gate->facility_id);
            $license['event_id'] = $headerData['survision-serial-number'];
            $license['license_plate'] =  $requestData['anpr']['decision']['plate'];
            $license['event_created_at'] = $this->getTimeByLprfeed($requestData['anpr']['date']);
            //PIMS-14859
            $license['event_timestamp'] = $this->getTimeByLprfeed($requestData['anpr']['date']);
            //PIMS-14859
            $license['base_sixty_four_image'] = $requestData['anpr']['decision']['jpeg'];
            $license['camera_name'] = $headerData['survision-sensor-name'];
            $license['event_type'] = $gate->gate_type;
            $license['camera_type'] = 1;
            $license['facility_id'] = $gate->facility_id;
            $license['partner_id'] = $gate->partner_id;
            $result = LPRFeed::create($license);
            $ticket = ['facility' => $facility, "license_plate" => $license, "gate" => $gate];
            if ((isset($facility->facilityConfiguration->is_facility_slot_enabled) && $facility->facilityConfiguration->is_facility_slot_enabled) && $gate->gate_type == "entry") {
                $this->log->info('Check For slot ');
                QueryBuilder::updateFacilitySlot($ticket, $gate->gate_type, "LPR");

                if ($facility->open_gate_enabled == '1') {
                    $this->log->info('open gate inside external entry gate');
                    $checkExistingPermitTrue = new LprTransientService();
                    $permit = $checkExistingPermitTrue->checkExistingPermit($requestData['anpr']['decision']['plate'], $gate->facility_id, $gate->partner_id);
                    if (count($permit) > 0) {
                        $gateStatus = $checkExistingPermitTrue->isParkEngageGateOpen($gate->facility_id, $gate->gate, '');
                    }
                }

                $this->log->info('Check For slot Updated');
            } else {
                $this->log->info('Check For slot exit');
                QueryBuilder::updateFacilitySlot($ticket, $gate->gate_type, "LPR");
                //gate 
                if ($facility->open_gate_enabled == '1') {
                    $checkExistingPermitTrue = new LprTransientService();
                    $permit = $checkExistingPermitTrue->checkExistingPermit($requestData['anpr']['decision']['plate'], $gate->facility_id, $gate->partner_id);
                    if (count($permit) > 0) {
                        $gateStatus = $checkExistingPermitTrue->isParkEngageGateOpen($gate->facility_id, $gate->gate, '');
                    }
                }
                $this->log->info('Check For exit slot Updated');
            }
            return $result;
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            return $msg;
        }
    }


    public function getTimeByLprfeed($time)
    {
        $timestamp = trim($time);
        $timestamp = $timestamp / 1000;
        $datetime = Carbon::createFromTimestamp($timestamp, 'UTC')
            ->setTimezone('America/New_York');
        return $datetime->format('Y-m-d H:i:s');
    }
}
