<?php

namespace App\Http\Controllers\ParkEngage;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Hash;
use Intervention\Image\Facades\Image;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserNotFound;
use App\Models\Wallet;
use App\Models\ParkEngage\Vehicle;
use App\Models\Devicetoken;
use App\Models\ParkEngage\ClubMembershipDetail;
use App\Models\ParkEngage\ClubUserMembership;
use App\Classes\PushNotification;
use Illuminate\Support\Facades\Storage;
use App\Services\LoggerFactory;
use App\Classes\AuthorizeNet\Cim;
use App\Http\Helpers\QueryBuilder;
use Artisan;
use App\Models\UserEventsLog;
use App\Models\ParkEngage\Configuration;
use App\Models\CheckInCheckOut;
use App\Models\ParkEngage\MobileDeviceVersion;


class UserController extends Controller
{

    protected $log;
    protected $log_cron;
    const USER_CARD_COUNT = 0;
    const PROFILE_UPDATED_CLOUD = '1';
    const PROMOCODE_API_KEY = "gNWEZhxnqapL9IJuTGRzIpEpt";


    public function __construct(LoggerFactory $logFactory, Cim $cim)
    {
        $this->cim = $cim;
        $this->log = $logFactory->setPath('logs/user-profile')->createLogger('user-profile');
        $this->log_cron = $logFactory->setPath('logs/admin-crons')->createLogger('admin-crons');
    }

    public function updateDevice(Request $request)
    {

        $this->validate($request, Devicetoken::$validationRules);
        $user = User::where('id', $request->user_id)->first();
        $data = $request->all();

        if (!$user) {
            throw new UserNotFound('No corresponding user found for this account.');
        }


        $device =  Devicetoken::where(array('user_id' => $user->id, 'device_token' => $data['device_token']))->first();

        if (!$device) {
            $deviceUpdated = Devicetoken::where('device_id', $data['device_id'])->first();
            if (!$deviceUpdated) {
                $deviceUpdated = new Devicetoken();
            } else {
                $existingUser = User::where('id', $deviceUpdated->user_id)->first();
                $existingUser->is_token_updated = 1;
                $existingUser->save();
            }

            $deviceUpdated->device_token = $data['device_token'];
            $deviceUpdated->device_id = $data['device_id'];
            $deviceUpdated->device_type = $data['device_type'];
            $deviceUpdated->user_id = $user->id;
            $deviceUpdated->save();

            $user->is_token_updated = 1;
            $user->save();
        }

        //update device
        $this->log->info("user updated device : " . json_encode($device));
        if ($device) {
            return $device;
        }
        return $deviceUpdated;
    }


    /**
     * Register api
     *
     * @return \Illuminate\Http\Response
     */
    public function updateUserProfile(Request $request)
    {
        $this->validate($request, User::$validationRules);

        $photostr = request('photo_url');
        $image = $request->file('photo_url');
        $filename = '';
        if ($image != '') {

            $filename = time() . "_" . $image->getClientOriginalName();
            Image::make($image->getRealPath())->save(storage_path('app/public/' . $filename));
        }
        $user = Auth::user();
        if (!$user) {
            throw new UserNotFound('No corresponding user found for this account.');
        }

        try {

            $data = $request->all();

            if (isset($data['membership_account']) && isset($data['membership_expire'])) {
                $membership = ClubMembershipDetail::clubMembership($data, $user);

                if ($membership['status'] == false) {
                    throw new ApiGenericException($membership['msg']);
                }
            }
            $user->name = $request->name;
            $user->address = $request->address;
            $user->city = $request->city;
            $user->state = $request->state;
            $user->zip = $request->zipcode;
            $user->phone = $request->phone;
            $user->is_profile_updated_on_cloud = self::PROFILE_UPDATED_CLOUD;

            if ($photostr != '') {
                $user->photo_url = $filename;
            }

            $user->save();

            if (isset($data['membership_account']) && isset($data['membership_expire'])) {
                ClubUserMembership::userMembership($user, $data);
            }
        } catch (Exception $e) {
            throw new ApiGenericException("Could not add request, " . $e->getMessage());
        }

        $userMemberShip = ClubUserMembership::with(array('ClubMembershipDetail' => function ($query) {
            $query->select('id', 'account as user_account', 'end_date');
        }))->select('club_membership_detail_id')->where('user_id', $user->id)->first();

        if ($userMemberShip) {
            $user['club_user_membership'] = $userMemberShip;
        }
        $this->log->info("user updated profile : " . json_encode($user));

        return $user;
    }


    /**
     * Register api
     *
     * @return \Illuminate\Http\Response
     */
    public function getUserProfileData(Request $request)
    {
        try {
            $this->log->info("user edit profile received : ");
            $user = Auth::user();
            if (!$user) {
                throw new UserNotFound('No corresponding user found for this account.');
            }

            $userData =  User::where('id', $user->id)->select('id', 'name', 'email', 'phone', 'address', 'city', 'state', 'pincode', 'photo_url', 'referral_code', 'qr_code_number')->with(array('clubUserMembership' => function ($query) {
                $query->select('id', 'club_membership_detail_id', 'user_id')->with(array('ClubMembershipDetail' => function ($query) {
                    $query->select('id', 'account as user_account', 'end_date');
                }));
            }))->first();
            //get Promocode from Config Table
            $congigurationData = Configuration::getConfig();
            $userData->promocode_user_api_key = isset($congigurationData['promocode_api_key']) ? $congigurationData['promocode_api_key'] : self::PROMOCODE_API_KEY;
            if(isset($request->app_name)){
                $mobileData = MobileDeviceVersion::select("app_partner_name","app_web_url_name", "app_copyrights","app_web_url")
                                ->where("partner_id", $user->created_by)
                                ->where("app_name", $request->app_name)->first();
                if($mobileData){
                    $userData->app_partner_name =     $mobileData->app_partner_name;
                    $userData->app_web_url_name =     $mobileData->app_web_url_name;
                    $userData->app_copyrights =     $mobileData->app_copyrights;
                    $userData->app_web_url =     $mobileData->app_web_url;
                }
            }
            
            return $userData;
        } catch (\Exception $e) {
            $this->log->info($e->getMessage());
            throw new ApiGenericException($e->getMessage());
        }
    }


    /**
     * details api
     *
     * @return \Illuminate\Http\Response
     */
    public function details()
    {
        return Auth::user();
    }

    /**
     * details api
     *
     * @return \Illuminate\Http\Response
     */
    public function changePassword(Request $request)
    {
        $this->validate($request, User::$changePasswordRules);

        $user = Auth::user();
        $input = $request->all();

        // Check password matches
        if (!Hash::check($request->oldpassword, $user->password)) {

            throw new ApiGenericException("Old password does not match");
        }

        if ($request->password) {
            $user->password = Hash::make($request->password);
            //$user->legacy_authentication = 0;
            $user->save();
        }
        $user = Auth::user();
        if (isset($user->id) && $user->id != '') {

            return $user;
        } else {

            throw new ApiGenericException("Password not change");
        }
    }

    public function getProfileDashboard()
    {
        $user = Auth::user();
        $filledColumn = array();
        $user_card_count = self::USER_CARD_COUNT;
        $userWallet = 0;

        if (!$user) {
            throw new UserNotFound('No corresponding user found for this account.');
        }

        $user_fields = array('name', 'email', 'photo_url', 'address', 'city', 'state', 'zip', 'phone', 'qr_code_number');


        foreach ($user_fields as $key => $value) {

            if ($user->$value) {
                $filledColumn[] = $value;
            }
        }
        $profile = $this->cim->setUser($user)->getCustomerProfile();

        if (isset($profile['payments']) && count($profile['payments']) > 0) {
            $user_card_count = count($profile['payments']);
        }

        $wallet = Wallet::getUserWallet($user->id);
        if ($wallet) {
            $userWallet = $wallet->balance;
        }

        $transactions = Wallet::getUserTransactions($user->id);

        $profile_complete = (count($filledColumn) / count($user_fields) * 100);
        $total_vehicle =  Vehicle::where('user_id', $user->id)->count();
        $result['id'] = $user->id;
        $result['name'] = $user->name;
        $result['email'] = $user->email;
        $result['profileImageUrl'] = $user->profileImageUrl;
        $result['profile_complete_percent'] = round($profile_complete);
        $result['total_user_vehicles'] = $total_vehicle;
        $result['user_cards'] = $user_card_count;
        $result['total_wallet_amount'] = $userWallet;
        $result['user_transation_history'] = $transactions;

        return $result;
    }

    public function getUserMembership(Request $request)
    {
        $this->validate($request, User::$ValidateEmail);

        $result = array();
        $email = $request->email;

        $clubMembership = ClubMembershipDetail::where('email', $email)->first();

        if (!$clubMembership) {
            throw new UserNotFound('No corresponding member found for this account.');
        }

        try {

            $result['email'] = $email;
            $result['membership_name'] = $clubMembership->name ? $clubMembership->name : '';
            $result['membership_account'] = $clubMembership->account;
            $result['membership_expire'] = $clubMembership->end_date;

            return $result;
        } catch (Exception $e) {
            throw new ApiGenericException("Could not add request, " . $e->getMessage());
        }
    }

    public function deleteUserDevice(Request $request)
    {

        //$this->validate($request, Devicetoken::$validationAppLogoutRules);

        $user = User::where('id', $request->user_id)->first();

        $data = $request->all();

        if (!$user) {
            throw new UserNotFound('No corresponding user found for this account.');
        }

        DB::table('oauth_access_tokens')->where('user_id', $user->id)->delete();
        return array("success" => true);

        try {
            $record = Devicetoken::where(array('user_id' => $data['user_id'], 'device_token' => $data['device_token']))->first();
            if ($record) {
                $this->log->info("user App Logout : " . json_encode($record));
                $record->delete();

                $user->is_token_updated = 1;
                $user->save();
            } else {

                throw new ApiGenericException('device Not belongs to user');
            }
        } catch (\Exception $e) {

            throw new ApiGenericException($e->getMessage());
        }


        $result['message'] = "user App Logout";

        return $result;
    }

    /**
     * Register api
     *
     * @return \Illuminate\Http\Response
     */
    public function checkOutSyncToLogout(Request $request)
    {
        $response = array();
        Artisan::call('update:server_check_out_data_from_cloud_to_local');
        return $response;
    }


    /**
     * Register api
     *
     * @return \Illuminate\Http\Response
     */
    public function paymentNotificationAdmin(Request $request)
    {
        $order_number  = $request->order_number;
        $response = array();
        $this->log_cron->info("Payment notification order number" . $order_number);
        Artisan::call('send:payment_push_notification', array('order_number' => $order_number));
        return $response;
    }


    //add event log in DB

    public function addUserEvent(Request $request)
    {

        $nUserEventsLog = new UserEventsLog();
        $nUserEventsLog->user_id = (int) $request->user_id;
        $nUserEventsLog->event_name = $request->event_name;
        $nUserEventsLog->app_version = $request->app_version;
        $nUserEventsLog->error_message = $request->error_message;
        $nUserEventsLog->device = $request->device;
        $nUserEventsLog->device_id = $request->device_id;

        $result = $nUserEventsLog->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Nothing Saved');
        }

        return [
            'message' => 'Event log successfully updated.'
        ];
    }


    public function deleteUserAccount(Request $request)
    {

        $user = Auth::user();
        if (!$user) {
            throw new UserNotFound('No corresponding user found for this account.');
        }

        $checkOut = CheckInCheckOut::where('user_id', $user->id)->where('is_checkout', '0')->whereNull('check_out_time')->first();
        if ($checkOut) {
            throw new ApiGenericException('You have active check-in, you can not delete your account until you check-out.');
        }
        $this->log->info("user about to delete account, user details : " . json_encode($user));
        $profile = $this->cim->setUser($user)->getCustomerProfile();
        if ($profile && isset($profile['payments'])) {
            $cim = new Cim();
            if (count($profile['payments']) > 0) {
                foreach ($profile['payments'] as $key => $value) {
                    $valid = $cim
                        ->setUser($user)
                        ->deleteCustomerPaymentProfile($value['payment_profile_id']);
                }
            }
            $this->log->info("user payment profile deleted user id : " . json_encode($user->id));
        }

        $clubMember = ClubMembershipDetail::where('email', $user->email)->first();
        if ($clubMember) {
            $clubMember->delete();
        }
        DB::table('oauth_access_tokens')->where('user_id', $user->id)->delete();
        $user->delete();
        $this->log->info("user account deleted");
        return array("success" => true);
    }

    //logut mobile app 
    public function logoutUser(Request $request)
    {
        $user = Auth::user();
        $user = User::where('id', $user->id)->first();
        if (!$user) {
            throw new UserNotFound('No corresponding user found for this account.');
        }
        try {
            $record = Devicetoken::where(array('user_id' => $user->id, 'device_id' => $request->device_id))->first();
            if ($record) {
                $this->log->info("user App Logout : " . json_encode($record));
                $record->delete();
            } else {

                throw new ApiGenericException('device Not belongs to user');
            }
        } catch (Exception $e) {

            throw new ApiGenericException($e->getMessage());
        }


        $result['message'] = "User logout Successfully ";

        return $result;
    }

    public function paymentVerifyBeforePayment(Request $request)
    {
        // Register user case
        $response = [];
        if (isset($request->phone) && !empty($request->phone)) {
            if (QueryBuilder::checkValidMobileLength($request->phone)) {
                $user = User::where(['phone' => $request->phone])->first();
                if (!$user) {
                    $response['is_active'] = 0;
                    $response['message'] = "No Active check-In for this user.";
                    return $response;
                    throw new ApiGenericException('Invalid user details.');
                }
                $request->request->add(['user_id' => $user->id]);
            } else {
                throw new ApiGenericException('Invalid user phone no');
            }
        }
        if (isset($request->user_id)) {
            $user = User::find($request->user_id);
            if (!$user) {
                throw new ApiGenericException('Invalid user details.');
            }
            $activeCheckin = $user->activeCheckin();
            if ($activeCheckin) {
                $response['is_active'] = 1;
                $response['message'] = "You already have one active parking session. Your ticket number is {$activeCheckin->ticket_number}";
                // $response['tickte_number'] = $activeCheckin;
            } else {
                $response['is_active'] = 0;
                $response['message'] = "No Active check-In for this user.";
            }
            return $response;
        }
        throw new ApiGenericException('Invalid user details.');
        // also Need To handel Guest user case 

    }
}
