<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Crypt;
use Hash;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redirect;
use App\Models\ParkEngage\Gate;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Configuration;
use App\Models\PermitTicket;
use App\Models\PermitRequest;
use App\Classes\AbacusWebService;
use Twilio\Exceptions\RestException;
use App\Models\UserPass;
use App\Models\AuthorizeNetTransaction;
use App\Models\UserEventsLog;
use App\Models\OauthClient;
use App\Models\ParkEngage\TicketCitation;
use App\Services\Pdf;
use App\Classes\InlineViewCss;
use App\Services\Image;
use App\Models\Rate;
use App\Models\FacilityFee;
use App\Exceptions\ApiGenericException;
use App\Models\Promotion;
use App\Classes\PromoCodeLib;
use App\Models\Photo;
use Auth;
use App\Http\Helpers\QueryBuilder;
use App\Classes\PlanetPaymentGateway;
use App\Classes\DatacapPaymentGateway;
use DB;
use App\Models\ParkEngage\Warning;
use App\Models\ParkEngage\DatacapTransaction;
use App\Classes\HeartlandPaymentGateway;

class DemoDiamondTransientApiController extends Controller
{

    protected $log;
    protected $logPayment;
    protected $user;
    protected $partnerPaymentDetails;
    protected $authNet;
    protected $facility;
    protected $sendAnet = false;
    protected $anonymousAnet = false;
    protected $paymentProfileId;
    protected $cim;

    const RESERVATION_THRESHOLD_TYPE = 2;
    const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE = 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO = 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR = 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "Sold Out At This Time.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    const  GRACE_PERIOD = 1;

    const  RESIDENT_ID = 292;
    const  NON_RESIDENT_ID = 293;
    const  PAVE_PARTNER_ID = 359007;
    const  UNITED_PARTNER_ID = 361376;

    public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim, User $userModel)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->cim = $cim;
        $this->userModel = $userModel;

        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        $this->log = $logFactory->setPath('logs/parkengage/transient-api')->createLogger('transient-api');

        $this->logPayment = $logFactory->setPath('logs/parkengage/transient-api-payment-logs')->createLogger('transient-api-payment-logs');
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();

        if (($facility) && ($facility->timezone != '')) {
            date_default_timezone_set($facility->timezone);
        } else {
            if ($partnerTimezone) {
                if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }


    protected function checkFacilityAvailableForTwilio($facility_id)
    {
        $facility = Facility::where('id', $facility_id)->where('active', '1')->first();
        if (count($facility) == 0) {
            return false;
        } else {
            if ($facility->is_available != '1') {
                return false;
            }

            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $diff_in_hours = $arrival_time->diffInRealHours($from);
            if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
            }
            $checkinData['length'] = $diff_in_hours;
            $facility = Facility::find($facility_id);
            /** this function is used to get Availability Information for respective facility **/
            $rateData = $this->updateRateInformationWithAvailibilty($arrival_time, $diff_in_hours, $facility);
            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);

            if ($rate == false) {
                return false;
            }

            //return current availabilit
            if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
                $rate['availability'] = $rateData['availability'];
            } else {
                $rate['availability'] = self::DEFAULT_VALUE;
            }
            if ($rate['price'] == 'N/A') {
                return false;
            }

            //returning  message as per availibility 
            $rate['availability_msg'] = '';
            $rate['availability_msg_some_space'] = '';
            if ((float)$rate['price'] >  self::DEFAULT_VALUE) {
                if ($rate['availability'] == self::DEFAULT_VALUE) {
                    return false;
                }/*else if($rate['availability'] < self::MIN_AVAILABILITY)
            {
                return false;
            }*/ else {
                    return true;
                    //return Redirect::to('error-facility');
                }
            } else if ($rate['price'] == 'N/A') {
                return false;
            } else if ($rate['availability'] == self::DEFAULT_VALUE) {
                return false;
            }
            return true;
        }
    }

    protected function checkFacilityAvailable($facility_id)
    {
        $facility = Facility::where('id', $facility_id)->where('active', '1')->first();
        if (count($facility) == 0) {
            return false;
        }
        if ($facility->is_available != '1') {
            return false;
        }
        return true;
    }



    public function getCheckinDetailsOnCheckout($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != '') {
            return Redirect::to('atlanta-pay/' . $ticket_number);
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);
        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($checkinData->user_id) ? $checkinData->user_id : '0';
        $arrEvents['event_name'] = "Touchless Drive Up Checkout Scan Screen";
        $arrEvents['facility_id'] = isset($checkinData->facility_id) ? $checkinData->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Checkout scan screen Function getCheckinDetailsOnCheckout Request " . $checkinData->ticket_number;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);


        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('autogate-checkin-checkout.checkout-scan-screen', ['data' => $checkinData]);
    }

    public function getCheckinDetailsPaymentThankyou($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != '') {
            return Redirect::to('autogate-error-checkin/' . $ticket_number);
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('autogate-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);
        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($checkinData->user_id) ? $checkinData->user_id : '0';
        $arrEvents['event_name'] = "Touchless Drive Up Thank You Screen After Payment";
        $arrEvents['facility_id'] = isset($checkinData->facility_id) ? $checkinData->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Thank you after payment success Function getCheckinDetailsPaymentThankyou Request " . $checkinData->ticket_number;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('diamond-checkin-checkout.thankyou-payment', ['data' => $checkinData]);
    }


    public function thankyouCheckout($ticket_number)
    {


        Session::forget('email');
        Session::forget('phone');
        Session::forget('no_of_days');
        Session::forget('license_plate');
        Session::forget('car_model');
        Session::forget('gate');
        Session::forget('check_in_datetime');
        Session::forget('check_out_datetime');
        Session::forget('is_autopay');

        $checkinData = Ticket::with(['user', 'facility'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '1')->first();

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $checkinData['is_overstay'] = '0';
        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->orderBy('id', 'DESC')->first();
        /*if($overstay){
          $checkinData['check_in_datetime'] = $overstay->check_in_datetime;
          $checkinData['checkout_datetime'] = $overstay->checkout_datetime;
          $checkinData['length'] = $overstay->length;
          $checkinData['is_overstay'] = '1';
          $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['check_in_datetime']);
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['checkout_datetime']);
            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        }*/
        $this->log->info("user successfully checkout and thankyou page showing with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('diamond-checkin-checkout.thankyou-checkout', ['data' => $checkinData]);
    }


    public function thankyouCitationCheckout($citation)
    {

        $checkinData = TicketCitation::with(['facility'])->where('citation_number', base64_decode($citation))->where('is_checkin', '1')/*->where('is_checkout', '1')*/->first();

        if (!$checkinData) {
            return Redirect::to('autogate-error-checkout');
        }
        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkin_time);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_time);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        //$checkinData['is_overstay'] = '0';
        //$overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->orderBy('id', 'DESC')->first();
        /*if($overstay){*/
        $checkinData['check_in_datetime'] = $checkinData->checkin_time;
        $checkinData['checkout_datetime'] = $checkinData->checkout_time;
        //$checkinData['length'] = $overstay->length;
        //$checkinData['is_overstay'] = '1';
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['checkin_time']);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['checkout_time']);
        if ($startDate->diffInDays($endDate) == 0) {
            $checkinData['diff_in_days'] = 1;
            $checkinData['diff_in_hours'] = 0;
            $checkinData['diff_in_minutes'] = 0;
        } else {
            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        }


        $this->log->info("user successfully checkout and thankyou page showing with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('diamond-checkin-checkout.thankyou-citation-checkout', ['data' => $checkinData]);
    }



    public function errorFacility()
    {
        return view('diamond-checkin-checkout.error-facility');
    }


    public function getBeforeCheckinPaymentDetails($ticket_number)
    {
        if (Session::get('phone') == '') {
            return Redirect::to('diamond-error-facility');
        } else {
        }

        $checkinData = [];
        $now = date("Y-m-d H:i:s");
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($now . ' + ' . Session::get('no_of_days') . ' hours')));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours > 24) {
            $diff_in_hours = 24;
        }
        $facility = Facility::find(Session::get('facility_id'));
        $checkinData['facility'] = $facility;
        /** this function is used to get Availability Information for respective facility **/
        /*$rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);*/
        $isMember = 0;
        //$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
        $rate = [];
        $is_resident_user = '0';
        $categoryId = '';
        if (Session::get('driving_license') != '') {
            $mystring = Session::get('driving_license');
            $mystring = trim($mystring); //removes any spaces from beginning of the string
            $number = substr($mystring, 1);

            if (ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring) == 9) {
                $is_resident_user = '1';
                $categoryId = self::RESIDENT_ID;
            } else {
                $is_resident_user = '0';
                $categoryId = self::NON_RESIDENT_ID;
            }
        }

        $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
        if (!$residentRate) {
            return Redirect::to('diamond-error-facility');
        }
        if ($diff_in_hours >= $residentRate->free_hours) {
            $price_in_hours = $diff_in_hours - $residentRate->free_hours;
            $rate['price'] = $residentRate->price * $price_in_hours;
        } else {
            $rate['price'] = $residentRate->price * $diff_in_hours;
        }

        $facilityFee = FacilityFee::where("facility_id", $facility->id)->where("name", "processing_fee")->first();
        $facilityProcessingFee = 0.00;
        if ($facilityFee) {
            $facilityProcessingFee = $facilityFee['val'];
        }
        $checkinData['length'] = $diff_in_hours;
        //return current availabilit
        /*if($rate['price'] == 'N/A'){
                return Redirect::to('diamond-error-facility');
            }*/
        if ($rate['price'] == 0) {
            $processingFee = $facilityProcessingFee;
            $taxFee = "0.00";
            $checkinData['parking_amount'] = "0.00";
            $rate['price'] = "0.00";
            $checkinData['rate'] = $rate;
            $checkinData['processing_fee'] = $processingFee;
            $checkinData['tax_fee'] = $taxFee;
            //$checkinData['free_hours'] = $residentRate->free_hours == '' ? 0 : $residentRate->free_hours;

        } else {
            //$processingFee = Session::get('no_of_days') * $facility->processing_fee;
            $processingFee = $facilityProcessingFee;
            //$taxFee = Session::get('no_of_days') * $facility->tax_rate;
            $taxFee = $facility->tax_rate;
            $checkinData['parking_amount'] = $rate['price'];
            $rate['price'] = $rate['price'] + $taxFee + $processingFee;
            $checkinData['rate'] = $rate;
            $checkinData['processing_fee'] = $processingFee;
            $checkinData['tax_fee'] = $taxFee;
            //$checkinData['free_hours'] = $residentRate->free_hours == '' ? 0 : $residentRate->free_hours;
        }
        /*$processingFee = Session::get('no_of_days') * $facility->processing_fee;
            $taxFee = Session::get('no_of_days') * $facility->tax_rate;
            $checkinData['parking_amount'] = $rate['price'];
            $rate['price'] = $rate['price'] + $taxFee + $processingFee;
            $checkinData['rate'] = $rate;
            $checkinData['processing_fee'] = $processingFee;
            $checkinData['tax_fee'] = $taxFee;*/

        //if pass is already used then will not send pass list
        $checkinData['free_hours'] = $residentRate->free_hours == '' ? 0 : round($residentRate->free_hours);
        $checkinData['check_in_datetime'] = date("Y-m-d H:i:s");
        $checkinData['check_out_datetime'] = $from;
        Session::put('check_in_datetime', $checkinData['check_in_datetime']);
        Session::put('check_out_datetime', $checkinData['check_out_datetime']);
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $arrival_time);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $from);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $checkinData['email'] = Session::get('email');
        $checkinData['phone'] = Session::get('phone');
        $checkinData['license_plate'] = Session::get('license_plate');
        $checkinData['car_model'] = Session::get('car_model');
        $checkinData['driving_license'] = Session::get('driving_license');
        $checkinData['is_resident_user'] = $is_resident_user;
        return view('diamond-checkin-checkout.after-checkin-payment-screen', ['data' => $checkinData]);
    }





    public function errorExtend()
    {
        return view('diamond-checkin-checkout.error-extend');
    }



    public function getOverstayPaymentDetails(Request $request, $citation)
    {
        $citation = TicketCitation::with(['facility.photos', 'ticket.user'])->where('citation_number', $citation)->first();
        if ($citation->is_closed == '1') {
            return Redirect::to('diamond-error-facility')->with('danger', "You have already made the payment for this citation.");
        }
        $now = date("Y-m-d H:i:s");
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', $citation->checkin_time);
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        $diff_in_mins = $arrival_time->diffInRealMinutes($from);
        $diff_in_sec = $arrival_time->diffInSeconds($from);
        if ($diff_in_hours > 24) {
            $diff_in_hours = 24;
        }

        if ($diff_in_hours == 0 && $diff_in_mins != 0) {
            $diff_in_hours = 1;
        }

        if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
            $diff_in_hours = 1;
        }

        $facility = Facility::find($citation->facility_id);
        $citation['facility'] = $facility;
        /** this function is used to get Availability Information for respective facility **/
        /*$rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);*/

        /*$isMember = 0;
        $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
        
        if($rate == false){
            return Redirect::to('diamond-error-facility');
        }*/

        $rate = [];
        $is_resident_user = '0';
        /*if(Session::get('driving_license') != ''){
            
            $mystring = Session::get('driving_license');
            $mystring = trim($mystring);//removes any spaces from beginning of the string
            $number = substr($mystring,1);
            
            if(ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring)==9){
                $is_resident_user = '1';
            }else{
                $is_resident_user = '0';
            }
        }*/

        $categoryId = self::NON_RESIDENT_ID;
        $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
        if (!$residentRate) {
            return Redirect::to('diamond-error-facility');
        }
        //$price_in_hours = $diff_in_hours - $residentRate->free_hours;
        /*if($diff_in_hours > $residentRate->free_hours){
            $price_in_hours = $diff_in_hours - $residentRate->free_hours;
        }*/
        $rate['price'] = $residentRate->price * $diff_in_hours;

        $citation['length'] = $diff_in_hours;

        //return current availabilit
        if ($rate['price'] === 'N/A') {
            return Redirect::to('diamond-error-facility');
        }
        $citation['parking_amount'] = $rate['price'];
        $days = $diff_in_hours;

        $facilityFee = FacilityFee::where("facility_id", $facility->id)->where("name", "citation_processing_fee")->first();
        $facilityProcessingFee = 0.00;
        if ($facilityFee) {
            $facilityProcessingFee = $facilityFee['val'];
        }

        if ($rate['price'] > '0.00') {
            $processingFee = $facilityProcessingFee;
            $taxFee = $facility->tax_rate;
            $rate['price'] = $rate['price'] + $facility->penalty_fee + $taxFee + $processingFee;
            $citation['rate'] = $rate;
            $citation['processing_fee'] = $processingFee;
            $citation['tax_fee'] = $taxFee;
        } else {
            $processingFee = '0.00';
            $taxFee = '0.00';
            $rate['price'] = '0.00';
            $citation['rate'] = $rate;
            $citation['processing_fee'] = '0.00';
            $citation['tax_fee'] = '0.00';
        }

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $citation->checkin_time);
        if ($startDate->diffInDays($endDate) == 0) {
            $citation['diff_in_days'] = 1;
            $citation['diff_in_hours'] = 0;
            $citation['diff_in_minutes'] = 0;
        } else {
            $citation['diff_in_days'] = $startDate->diffInDays($endDate);
            $citation['diff_in_hours'] = $startDate->copy()->addDays($citation['diff_in_days'])->diffInRealHours($endDate);
            $citation['diff_in_minutes'] = $startDate->copy()->addDays($citation['diff_in_days'])->addHours($citation['diff_in_hours'])->diffInRealMinutes($endDate);
        }
        return view('diamond-checkin-checkout.overstay-payment-screen', ['data' => $citation]);
    }

    public function getPrepaidCheckinDetails($ticket_number)
    {
        $reservation = Reservation::where('ticketech_code', base64_decode($ticket_number))->whereNull('cancelled_at')->first();


        if (!$reservation) {
            return Redirect::to('error-checkin');
        }

        $this->checkFacilityAvailable($reservation->facility_id);

        $this->setCustomTimezone($reservation->facility_id);

        $config = Configuration::where('field_name', 'prepaid-checkin-time')->first();
        if (count($config) > 0) {
            $prepaidCheckinTime = $config->field_value;
        } else {
            $prepaidCheckinTime = 15;
        }

        $today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);
        $reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
        $reservationEndDate = $reservationstartDate->addHours($reservation->length);

        if (strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)) {
            return Redirect::to('error-prepaid-checkin');
        }

        if (strtotime($today) < strtotime($reservation->start_timestamp)) {
            return Redirect::to('error-prepaid-checkin');
        }


        $checkinData = Ticket::with(['user', 'reservation'])->where('reservation_id', $reservation->id)->first();
        if ($checkinData && $checkinData->is_checkin == '1') {
            return Redirect::to('prepaid-checkout-details/' . base64_encode($checkinData->ticket_number));
        }
        if ($checkinData && $checkinData->is_checkout == '1') {
            return Redirect::to('thankyou-checkout/' . base64_encode($checkinData->ticket_number));
        }
        if (!$checkinData) {

            $ticket['user_id'] = $reservation->user_id;
            $ticket['reservation_id'] = $reservation->id;
            $ticket['facility_id'] = $reservation->facility_id;
            $ticket['length'] = $reservation->length;
            $ticket['ticket_security_code'] = rand(1000, 9999);
            $ticket['total'] = $reservation->total;
            $ticket['grand_total'] = $reservation->total;
            $ticket['check_in_datetime'] = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
            $ticket['check_in_datetime'] = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
            $ticket['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
            $ticket['ticket_number'] = 'PE' . rand(100, 999) . rand(100, 999);
            $ticket['user_pass_id'] = $reservation->user_pass_id;
            $checkinData = Ticket::create($ticket);

            $reservation->is_ticket = '1';
            $reservation->save();
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('checkin-checkout.prepaid-booking-screen', ['data' => $checkinData]);
    }

    public function getPrepaidConfirmCheckinDetails($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();

        if ($checkinData && $checkinData->is_checkout == '1') {
            return Redirect::to('thankyou-checkout/' . base64_encode($checkinData->ticket_number));
        }

        $this->setCustomTimezone($checkinData->facility_id);

        return view('checkin-checkout.prepaid-confirm-screen', ['data' => $checkinData]);
    }

    public function getCheckinPaymentDetailsDesign($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', $ticket_number)->whereNull('anet_transaction_id')->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        $checkinData['lenght'] = $diff_in_hours;
        $facility = Facility::find($checkinData->facility_id);
        $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
        $checkinData['rate'] = $rate;
        return view('checkin-checkout.payment-design-screen', ['data' => $checkinData]);
    }

    /* code start from here for Api */
    public function signUp(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $countryCode = QueryBuilder::appendCountryCode();
        $existUser = User::where('email', $request->email)->where('created_by', $secret->partner_id)->first();
        if ($existUser) {
            throw new ApiGenericException("The email has already been taken.");
        } else {
            $user = User::where('phone', $countryCode . $request->phone)->where('created_by', $secret->partner_id)->first();
            if ($user) {
                if ($user->email == '' || $user->email == $request->email) {
                    $user->name = $request->first_name . " " . $request->last_name;
                    $user->email =     $request->email;
                    $user->password = Hash::make($request->confirm_password);
                    $user->anon = 0;
                    $user->address = isset($request->address1) ? $request->address1 : '';
                    $user->address2 = isset($request->address2) ? $request->address2 : '';
                    $user->city = isset($request->city) ? $request->city : '';
                    $user->state = isset($request->state) ? $request->state : '';
                    $user->country = isset($request->country) ? $request->country : '';
                    $user->pincode = isset($request->zipcode) ? $request->zipcode : '';
                    $user->company_name = isset($request->company_name) ? $request->company_name : '';
                    $user->qr_code_number = $this->userModel->generateQrCode();
                    //$user->is_notification_enabled = $request->is_notification_enabled;
                    $user->save();
                    $this->user = $user;
                } else {
                    $errorMsg = 'User with entered phone number has already registered.';
                    throw new ApiGenericException($errorMsg);
                }
            }
        }
        if (!$this->user) {
            $this->user = User::create(
                [
                    'name' => $request->first_name . " " . $request->last_name,
                    'email' => $request->email,
                    'phone' => $countryCode . $request->phone,
                    'password' => Hash::make($request->confirm_password),
                    'address' => isset($request->address1) ? $request->address1 : '',
                    'address2' => isset($request->address2) ? $request->address2 : '',
                    'city' => isset($request->city) ? $request->city : '',
                    'state' => isset($request->state) ? $request->state : '',
                    'country' => isset($request->country) ? $request->country : '',
                    'pincode' => isset($request->zipcode) ? $request->zipcode : '',
                    'company_name' => isset($request->company_name) ? $request->company_name : '',
                    'anon' => false,
                    'user_type' => '5',
                    'created_by' => $secret->partner_id,
                    'qr_code_number' => $this->userModel->generateQrCode(),
                ]
            );
        }
        $facility_id = '';
        if ($request->facility_slug) {
            $facility = Facility::where('short_name', $request->facility_slug)->first();
            $facility_id = $facility->id;
        }
        /*
        $signup =[
            'user_id'=> $this->user->id,
            'password'=> $request->confirm_password,
            'facility_id'=> isset($request->facility_id)?$request->facility_id:'',
            'partner_id' => isset($secret->partner_id) ? $secret->partner_id : ''
        ];
        */
        if ($request->base_url != '') {
            $url_slug = explode('/', $request->base_url);
            $facility_slug = end($url_slug);
            $user = User::where('slug', $facility_slug)->where('user_type', '12')->where('created_by', $secret->partner_id)->first();
            if ($user) {
                $facilities = DB::table('user_facilities')->where('user_id', $user->id)->whereNull('deleted_at')->pluck('facility_id');
                $facility_id = end($facilities);
            }
        }

        Artisan::queue('email:gated-ungated-user-registration', array('id' => $this->user->id, 'password' => $request->confirm_password, 'base_url' => $request->base_url, 'facility_id' => $facility_id));
        //Artisan::queue('checkin-email-send', array('signup' => $signup, 'slug' => 'user-register','id'=>'','reset'=>''));//new cron for common approach
        //Artisan::queue('email:user-registration', array('id' => $this->user->id));
        return $this->user;
    }

    public function getFacility($id)
    {
        //$facility_id = Crypt::encrypt('199');
        $id = Crypt::decrypt($id);
        $facility = Facility::find($id);
        if (!$facility) {
            throw new ApiGenericException('Facility Not Found');
        }
        return $facility;
    }
    public function TransientConfirmCheckin(Request $request)
    {


        $decrypt = base64_decode($request->facility);
        $decrypt = json_decode($decrypt);

        $facility =  Facility::where('id', $decrypt->facility_id)->first();
        if (!$facility) {
            throw new ApiGenericException('Facility not Found.');
        }
        $facilityData   =  Facility::with('photos')->find($facility->id);

        $requestValuesLogs = $request->all();
        $gate = Gate::where('gate', $decrypt->gate)->where('facility_id', $decrypt->facility_id)->first();
        $checkinData = [];
        if ($gate && isset($gate->gate_type)) {
            if ($gate->gate_type == 'entry') {
                $checkFacilityOwner = Facility::where('id', $decrypt->facility_id)->first();


                $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
                if ($geoLocation['geoplugin_countryCode'] == 'IN') {
                    $countryCode = "+91";
                } elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
                    $countryCode = "+1";
                } else {
                    $countryCode = "+1";
                }

                $codePhone = substr($request->phone, 0, 1);
                if ($codePhone == "+") {
                    $phone = $request->phone;
                } else {
                    $phone =  $countryCode . $request->phone;
                }
                $user = User::where('phone', $phone)->where('created_by', $checkFacilityOwner->owner_id)->first();

                if (!$user) {
                    throw new ApiGenericException('User Not Found');
                }

                /*
                    if(!$user){
                        $user =  User::create(
                            [
                            'name' => '',
                            'email' => $request->email,
                            'phone' => $phone,
                            'password' => Hash::make(str_random(60)),
                            'anon' => false,
                            'user_type' => '5',
                            'created_by' => $checkFacilityOwner->owner_id
                            ]
                        );
                    }
                   
                    */

                $checkinExist = Ticket::/*where('user_id', $user->id)->*/whereDate('check_in_datetime', '>=', date("Y-m-d"))->whereDate('checkout_datetime', '<=', date("Y-m-d"))->where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->first();
                if ($checkinExist) {
                    throw new ApiGenericException("You have already Checked-In, Your planned checkout date is " . date("m/d/Y", strtotime($checkinExist->checkout_datetime)) . " " . date('g:i A', strtotime($checkinExist->checkout_datetime)));
                }

                $citationExist = TicketCitation::/*whereDate('created_at', '=', date("Y-m-d"))->*/where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->where('is_closed', '0')->orderBy("id", "DESC")->first();
                if ($citationExist) {
                    if (date("Y-m-d", strtotime($citationExist->created_at)) < date("Y-m-d")) {
                        throw new ApiGenericException("Citation already issued against this license plate. Please contact to attendant.");
                    }
                }

                $user->email = $request->email;
                $user->save();
                if ($user->created_by == '') {
                    $facility = Facility::where('id', $decrytArray->facility_id)->first();
                    if ($user->user_type == '5') {
                        $user->created_by = $facility->owner_id;
                        $user->save();
                    }
                    $data['partner_id'] = $facility->owner_id;
                } else {
                    $data['partner_id'] = $user->created_by;
                }
                $data['email'] = $user->email;
                $data['phone'] = $user->phone;
                $data['no_of_days'] = $request->no_of_days;
                $data['license_plate'] = $request->license_plate;
                $data['car_model'] = $request->car_model;
                $data['facility_id'] = $decrypt->facility_id;
                $data['is_autopay'] = isset($request->is_autopay) ? "1" : "";
                $data['driving_license'] = $request->driving_license;
                $data['promocode'] = isset($request->promocode) ? $request->promocode : '';
                $data['client_id'] = isset($request->client_id) ? $request->client_id : '';

                //rate calculation
                $checkinData = $this->rateCalculation($data);
                $data['checkinData'] = $checkinData;
                $data['facility_data'] = $facilityData;

                return $data;
            } else {
                throw new ApiGenericException('Please scan the right QR code.');
            }
        } else {
            throw new ApiGenericException('Sorry, Something wrong with parking.'); //if gate type not found
        }
    }

    public function rateCalculation($data)
    {

        $checkinData = [];
        $now = date("Y-m-d H:i:s");
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($now . ' + ' . $data['no_of_days'] . ' hours')));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours > 24) {
            $diff_in_hours = 24;
        }
        $facility = Facility::find($data['facility_id']);
        $checkinData['facility'] = $facility;
        /** this function is used to get Availability Information for respective facility **/
        /*$rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);*/
        $isMember = 0;
        //$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
        $rate = [];
        $is_resident_user = '0';
        $categoryId = '';
        if ($data['driving_license'] != '') {
            $mystring = $data['driving_license'];
            $mystring = trim($mystring); //removes any spaces from beginning of the string
            $number = substr($mystring, 1);

            if (ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring) == 9) {
                $is_resident_user = '1';
                $categoryId = self::RESIDENT_ID;
            } else {
                $is_resident_user = '0';
                $categoryId = self::NON_RESIDENT_ID;
            }
        }

        $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();

        if (!$residentRate) {
            throw new ApiGenericException('Rate not found'); //if 
        }
        if ($diff_in_hours >= $residentRate->free_hours) {
            $price_in_hours = $diff_in_hours - $residentRate->free_hours;
            $rate['price'] = $residentRate->price * $price_in_hours;
        } else {
            $rate['price'] = $residentRate->price * $diff_in_hours;
        }

        $checkinData['length'] = $diff_in_hours;
        $facilityFee = FacilityFee::where("facility_id",  $facility->id)->where("name", "processing_fee")->first();
        $facilityProcessingFee = 0.00;
        if ($facilityFee) {
            $facilityProcessingFee = $facilityFee['val'];
        }





        if ($rate['price'] == 0) {
            $processingFee = $facilityProcessingFee;
            $taxFee = "0.00";
            $checkinData['parking_amount'] = "0.00";
            $rate['price'] = "0.00";
            $checkinData['rate'] = $rate;
            $checkinData['processing_fee'] = $processingFee;
            $checkinData['tax_fee'] = $taxFee;
            //$checkinData['free_hours'] = $residentRate->free_hours == '' ? 0 : $residentRate->free_hours;

        } else {
            //$processingFee = Session::get('no_of_days') * $facility->processing_fee;
            //$processingFee = $facility->processing_fee;
            $processingFee = $facilityProcessingFee;
            //$taxFee = Session::get('no_of_days') * $facility->tax_rate;
            $taxFee = $facility->tax_rate;
            $checkinData['parking_amount'] = $rate['price'];
            $rate['price'] = $rate['price'] + $taxFee + $processingFee;
            $checkinData['rate'] = $rate;
            $checkinData['processing_fee'] = $processingFee;
            $checkinData['tax_fee'] = $taxFee;
            //$checkinData['free_hours'] = $residentRate->free_hours == '' ? 0 : $residentRate->free_hours;
        }

        if (isset($data['promocode']) && $data['promocode'] != '') {

            $this->request->request->add(['amount' => $rate['price']]);
            $data['amount'] = $rate['price'];
            $this->validate($this->request, Promotion::$checkPromoValidationRulesThirdParty);
            $response = PromoCodeLib::validatePromoCodeThirdParty($this->request);
            //return $response;
            if (isset($response->getData()->is_promocode_valid) == '1') {
                //return [$effective_pro_rate,$response->getData()->discount_in_dollar];
                if ($rate['price'] < $response->getData()->discount_in_dollar) {
                    $data = $response->getData();
                    $rate['promocode_discount'] = $data;
                    $rate['price'] = '0.00';
                    $rate['promocode_discount_amount'] = number_format(($rate['price'] + $taxFee + $processingFee), 2);
                } else {
                    $new_pro_rate = ($rate['price']) - ($response->getData()->discount_in_dollar);
                    $rate['price'] = $new_pro_rate > 0 ? $new_pro_rate : '0.00';
                    $data = $response->getData();
                    $rate['promocode_discount'] = $data;
                    $rate['promocode_discount_amount'] = number_format($response->getData()->discount_in_dollar, 2);
                }
            }
            $checkinData['rate'] = $rate;
        }



        //if pass is already used then will not send pass list
        $checkinData['free_hours'] = $residentRate->free_hours == '' ? 0 : round($residentRate->free_hours);
        $checkinData['check_in_datetime'] = date("Y-m-d H:i:s");
        $checkinData['check_out_datetime'] = $from;
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $arrival_time);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $from);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);

        return $checkinData;
    }


    public function makePayment(Request $request)
    {





        $this->setDecryptedCard($request);
        if ($request->payment_profile_id == '') {

            if (strlen($this->request->expiration_date) < 4) {
                throw new ApiGenericException('Error! Invalid expiry date.');
            }
            if (strlen($this->request->card_number) < 13) {
                throw new ApiGenericException('Error! Invalid card number.');
            }
            if (strlen($this->request->security_code) < 3) {
                throw new ApiGenericException('Error! The security code must be between 3 and 4 digits.');
            }
        }

        $this->checkFacilityAvailable($this->request->facility_id);

        $this->setCustomTimezone($this->request->facility_id);

        if (preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $this->request->email)) {
        } else {
            throw new ApiGenericException('Please enter valid email.');
        }

        //$this->validate($this->request, $this->billingValidation);
        $facility = Facility::find($this->request->facility_id);
        if (!$facility) {
            throw new ApiGenericException('Facility Not Found.');
        }
        $this->facility = $facility;

        $this->user = User::where('phone', $this->request->phone)->where('created_by', $this->facility->owner_id)->first();
        if ($this->request->name_on_card != '') {
            $this->user->name = $this->request->name_on_card;
        }
        $this->user->email = $this->request->email;
        $this->user->license_number =  $request->driving_license;
        $this->user->save();


        $now = date("Y-m-d H:i:s");
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($now . ' + ' . $request->no_of_days . ' hours')));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours > 24) {
            $diff_in_hours = 24;
        }
        /** this function is used to get Availability Information for respective facility **/
        /*$rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);*/

        /*$isMember = 0;
        $rate = $this->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
        if($rate == false){
            return Redirect::to('diamond-error-facility');
        }*/

        $rate = [];
        $is_resident_user = '0';
        $categoryId = '';
        if ($request->driving_license != '') {

            $mystring =  $request->driving_license;
            $mystring = trim($mystring); //removes any spaces from beginning of the string
            $number = substr($mystring, 1);

            if (ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring) == 9) {
                $is_resident_user = '1';
                $categoryId = self::RESIDENT_ID;
            } else {
                $is_resident_user = '0';
                $categoryId = self::NON_RESIDENT_ID;
            }
        }


        $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $this->facility->id)->first();
        if (!$residentRate) {
            throw new ApiGenericException('diamond-error-facility.');
        }
        //$price_in_hours = $diff_in_hours - $residentRate->free_hours;
        /*if($diff_in_hours > $residentRate->free_hours){
                $price_in_hours = $diff_in_hours - $residentRate->free_hours;
            }*/

        if ($diff_in_hours >= $residentRate->free_hours) {
            $price_in_hours = $diff_in_hours - $residentRate->free_hours;
            $rate['price'] = $residentRate->price * $price_in_hours;
        } else {
            $rate['price'] = $residentRate->price * $diff_in_hours;
        }

        //$rate['price'] = $residentRate->price * $diff_in_hours;
        /*if($is_resident_user == '1'){
                $rate['price'] = '0.00';
            }else{
                $rate['price'] = $residentRate->price * $diff_in_hours ;
            }*/

        $checkinData['length'] = $diff_in_hours;

        //return current availabilit
        if ($rate['price'] === 'N/A') {
            throw new ApiGenericException('Rate does not match in our database');
        }

        $facilityFee = FacilityFee::where("facility_id",  $this->facility->id)->where("name", "processing_fee")->first();
        $facilityProcessingFee = 0.00;
        if ($facilityFee) {
            $facilityProcessingFee = $facilityFee['val'];
        }
        $processingFee = $facilityProcessingFee;
        $taxFee = $this->facility->tax_rate;
        if ($rate['price'] == "0.00") {
            $rate['price'] = "0.00";
        } else {
            $rate['price'] = $rate['price'] + $taxFee + $processingFee;
        }
        //if($rate['price'] != $this->request->total){
        //return back()->with("danger","Rate does not match in our database.")->withInput();
        //}

        $is_partner = 0;
        if ($this->user->user_type == 3) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
            $is_partner = 1;
        } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
            if ($this->partnerPaymentDetails) {
                $is_partner = 1;
            } else {
                throw new NotFoundException('No payment gateway details found for this partner.');
            }
        } else {
            $is_partner = 0;
        }

        if ($this->request->is_autopay != '') {

            // Set up our cim class
            $customerProfile = $this->user->cim;

            try {
                $this->cim
                    ->setUser($this->user)
                    ->setFacility($this->facility)
                    ->isReservation()
                    ->setBillingAddress($this->getBillingArray())
                    ->isPartner($this->partnerPaymentDetails)
                    ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
            } catch (AuthorizeNetException $e) {
                throw new NotFoundException('Unable to authorize the credit card details. Please check the card info or use another card and try again.');
            }
            if (!$customerProfile) {  // Create a cim for this user so they can store their details and reuse them

                try {
                    $details = $this->cim->createCustomerProfile()->isPartner($this->partnerPaymentDetails)->executeCustomerProfileRequest();
                    if (isset($details['payment_profile_id'])) {
                        // $this->paymentProfileId = $details['payment_profile_id'];

                    } else {
                        throw new NotFoundException('Unable to authorize the credit card details. Please check the card info or use another card and try again.');
                    }
                } catch (AuthorizeNetException $e) {
                    return back()->with("danger", "Unable to authorize the credit card details. Please check the card info or use another card and try again.");
                }
            } else { // If the user has a cim already, just add a new payment profile
                try {
                    // $details = $this->cim->createPaymentProfile()->isPartner($this->partnerPaymentDetails)->executePaymentProfileRequest();
                } catch (AuthorizeNetException $e) {
                    throw new NotFoundException('Unable to authorize the credit card details. Please check the card info or use another card and try again.');
                }
            }
        }

        //log all request in event table
        $requestValuesLogs = $request->all();
        unset($requestValuesLogs['nonce']);
        unset($requestValuesLogs['name_on_card']);
        unset($requestValuesLogs['card_number']);
        unset($requestValuesLogs['expiration_date']);
        unset($requestValuesLogs['security_code']);
        $this->log->info("Transient  User Touchless Payment request --" . json_encode($requestValuesLogs));

        $this->logPayment->info("Make Payment Request" . json_encode($request));
        //saving to event table after check in payment functions

        $arrEvents = array();

        $arrEvents['user_id'] = isset($this->user->id) ? $this->user->id : '0';
        $arrEvents['event_name'] = "Diamond Make Payment After drive up check in";
        $arrEvents['facility_id'] = isset($this->request->facility_id) ? $this->request->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Diamond Make Payment Function makePayment Request " . json_encode($requestValuesLogs);
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = isset($this->user->email) ? $this->user->email : '';

        $this->saveEventsLogs($arrEvents);


        if ($this->request->total > 0) {
            try {

                if (!$this->request->payment_profile_id) {

                    if ($is_partner == 1) {

                        $this->authNet
                            ->setUser($this->user)
                            ->isReservation()
                            ->setFacility($this->facility)
                            ->isPartner($this->partnerPaymentDetails)
                            ->setBillingAddress($this->getBillingArray());
                    } else {
                        $this->authNet
                            ->setUser($this->user)
                            ->isReservation()
                            ->setFacility($this->facility)
                            ->setBillingAddress($this->getBillingArray());
                    }

                    if (isset($this->request->payment_profile_id) & !empty($this->request->payment_profile_id)) { // Use our logged in users profile
                        //$this->paymentProfileId = $this->request->payment_profile_id;

                        // $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);

                    } else {  // Otherwise set transaction details manually

                        $this->authNet->isPartner($this->partnerPaymentDetails)
                            ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                    }
                }

                if (isset($this->request->payment_profile_id) && !empty($this->request->payment_profile_id)) {
                    // Use our logged in users profile
                    $customerProfile = $this->user->cim;

                    if (!$customerProfile) {  // Create a cim for this user so they can store their details and reuse them
                        throw new ApiGenericException('No customer payment profile found for this user.');
                    }
                    $profiles = \DB::table('anet_payment_profiles')->where('payment_profile', $this->request->payment_profile_id)->orderBy("id", "DESC")->first();
                    if (!$profiles) {
                        throw new ApiGenericException('No payment profile found for this user.');
                    }

                    $this->paymentProfileId = $profiles->payment_profile;

                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->isPartner($this->partnerPaymentDetails)
                        ->setBillingAddress($this->getBillingArray());
                    $this->authNet->setPaymentProfile($this->paymentProfileId);
                }
            } catch (AuthorizeNetException $e) {
                $this->log->error($e);
                // $request->session()->flash('alert-danger',
                // 'Payment error!');
                // return back()->withError(['errMsg'=>$e]);
            }
        }


        $ticket = $this->saveTicket();
        if ($this->request->total > 0) {
            $reservationMode = "Ticketless Parking";
            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else {
                $ticketech_guid = rand(100000, 999999);
            }

            try {

                // Use our database rate price to create the transaction
                try {
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();
                    // Fire off transaction - this method will throw an exception if errors occur
                } catch (AuthorizeNetException $e) {
                    $ticket->delete();
                    $this->log->error($e->getMessage());
                    return back()->with('danger', $e->getMessage());
                }
            } catch (AuthorizeNetException $e) {
                // Charge successful, save transaction relationship to it
                /*dd($e);
                return back()->withError(['errMsg'=>$e]);
                dd(111);
                $transaction = $this->authNet->getTransaction();
                $ticket->anet_transaction_id = $transaction->id;
                $ticket->save();       
                $ticket->delete();
                return back()->withError(['errMsg'=>$e]);*/
                $ticket->delete();
                $this->log->error($e);
                throw new ApiGenericException('Error! Payment failed due to invalid details.' . $e->getMessage());
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->is_checkout = '1';
            $ticket->payment_date = date("Y-m-d H:i:s");
            $ticket->save();

            $citationExist = TicketCitation::/*whereDate('created_at', '=', date("Y-m-d"))->*/where('license_plate', $request->license_plate)->where('is_closed', '0')->orderBy("id", "DESC")->first();
            if ($citationExist) {
                if (date("Y-m-d", strtotime($citationExist->created_at)) < date("Y-m-d")) {
                    throw new ApiGenericException('Citation already issued against this license plate. Please contact to attendant.');
                }
                $citationExist->is_closed = '1';
                $citationExist->comment = 'User checked-in same day after citation created';
                $citationExist->save();
            }

            $this->log->info("Transient user done payment ticket id {$ticket->id} ticket number {$ticket->ticket_number} and checkout with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {
                Artisan::queue('email:transient-parking-autogate-checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));
                return $ticket;
            }
        } else {
            if ($rate['price'] == $this->request->total) {

                // Charge successful, save transaction relationship to it
                $authorized_anet_transaction = new AuthorizeNetTransaction();

                $authorized_anet_transaction->sent = $this->sendAnet;
                $authorized_anet_transaction->anonymous = $this->anonymousAnet;
                $authorized_anet_transaction->user_id = $this->user->id;
                $authorized_anet_transaction->ip_address = \Request::ip();
                $authorized_anet_transaction->total = 0;
                $authorized_anet_transaction->name = $this->getBillingName();

                $authorized_anet_transaction->description = "Pass purchase {$ticket->id}";

                $authorized_anet_transaction->response_message = "Zero amount transaction";
                $authorized_anet_transaction->save();

                $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

                $ticket->anet_transaction_id = $authorized_anet_transaction->id;
                $ticket->is_checkout = '1';
                $ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->save();

                $citationExist = TicketCitation::/*whereDate('created_at', '=', date("Y-m-d"))->*/where('license_plate', $request->license_plate)->where('is_closed', '0')->orderBy("id", "DESC")->first();
                if ($citationExist) {
                    if (date("Y-m-d", strtotime($citationExist->created_at)) < date("Y-m-d")) {
                        throw new ApiGenericException('Citation already issued against this license plate. Please contact to attendant.');
                    }
                    $citationExist->is_closed = '1';
                    $citationExist->comment = 'User checked-in same day after citation created';
                    $citationExist->save();
                }

                $this->log->info("Transient user done payment ticket id {$ticket->id} ticket number {$ticket->ticket_number} and checkout with browser {$_SERVER['HTTP_USER_AGENT']}");
                if ($ticket) {
                    Artisan::queue('email:diamond-parking-autogate-checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));
                    return $ticket;
                }
            }
            throw new ApiGenericException('Error! Payment failed due to invalid details.');
        }
    }

    public function getExtendCheckinPaymentDetails($ticket_number)
    {

        $checkinData =  Ticket::with('facility')->where('ticket_number', base64_decode($ticket_number))->orderBy('id', 'DESC')->first();
        if (!$checkinData) {
            throw new ApiGenericException('Ticket Number Not Found');
        }

        if ($checkinData->is_closed == '1') {
            throw new ApiGenericException('Ticket Number is closed');
        }

        $facility = $checkinData->facility;
        $checkinData['facility'] = $facility;
        /** this function is used to get Availability Information for respective facility **/
        /*$rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);*/
        $now = date("Y-m-d H:i:s");
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        $diff_in_mins = $arrival_time->diffInRealMinutes($from);
        $diff_in_sec = $arrival_time->diffInSeconds($from);
        if ($diff_in_hours > 24) {
            $diff_in_hours = 24;
        }

        if ($diff_in_hours == 0 && $diff_in_mins != 0) {
            $diff_in_hours = 1;
        }

        if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
            $diff_in_hours = 1;
        }

        //$facility = Facility::find($citation->facility_id);
        //$citation['facility'] = $facility;
        /** this function is used to get Availability Information for respective facility **/
        /*$rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);*/

        /*$isMember = 0;
         $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
         
         if($rate == false){
             return Redirect::to('diamond-error-facility');
         }*/

        $rate = [];
        $is_resident_user = '0';


        $categoryId = self::NON_RESIDENT_ID;
        $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
        if (!$residentRate) {
            throw new ApiGenericException('Rate Not Found');
        }
        //$price_in_hours = $diff_in_hours - $residentRate->free_hours;
        /*if($diff_in_hours > $residentRate->free_hours){
             $price_in_hours = $diff_in_hours - $residentRate->free_hours;
         }*/
        $rate['price'] = $residentRate->price * $diff_in_hours;

        $facilityFee = FacilityFee::where("facility_id",  $facility->id)->where("name", "citation_processing_fee")->first();
        $facilityProcessingFee = 0.00;
        if ($facilityFee) {
            $facilityProcessingFee = $facilityFee['val'];
        }
        $processingFee = $facilityProcessingFee;

        $checkinData['length'] = $diff_in_hours;

        //return current availabilit
        if ($rate['price'] == 'N/A') {
            return Redirect::to('diamond-error-facility');
        }

        $checkinData['parking_amount'] = $rate['price'];
        //$days = ceil($diff_in_hours/24);
        //$processingFee = $days * $facility->processing_fee;
        $taxFee = $facility->tax_rate;
        $rate['price'] = $rate['price'] + $taxFee + $processingFee;
        $checkinData['rate'] = $rate;
        $checkinData['processing_fee'] = $processingFee;
        $checkinData['tax_fee'] = $taxFee;

        return $checkinData;
        //if pass is already used then will not send pass list

        /*$checkinData['check_in_datetime'] = date("Y-m-d H:i:s");
         $checkinData['check_out_datetime'] = $from;
         $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $arrival_time);
         $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $from);
         $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
         $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
         $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);*/
    }

    public function makeExtendPayment(Request $request)
    {


        $ticket = Ticket::with(['user', 'facility'])->where('ticket_number', $request->ticket_number)->orderBy('id', 'desc')->first();
        if (!$ticket) {
            throw new ApiGenericException('Ticket Number Not Found');
        }


        $this->setDecryptedCard($request);

        if ($request->payment_profile_id == '') {

            if (strlen($this->request->expiration_date) < 4) {
                throw new ApiGenericException('Error! Invalid expiry date.');
            }
            if (strlen($this->request->card_number) < 13) {
                throw new ApiGenericException('Error! Invalid card number.');
            }
            if (strlen($this->request->security_code) < 3) {
                throw new ApiGenericException('Error! The security code must be between 3 and 4 digits.');
            }
        }
        $this->checkFacilityAvailable($this->request->facility_id);

        $this->setCustomTimezone($this->request->facility_id);


        $this->facility = $ticket->facility;
        $this->user = $ticket->user;

        if ($this->request->name_on_card != '') {
            $this->user->name = $this->request->name_on_card;
        }
        //$this->user->email = $this->request->email;
        $this->user->save();

        $is_partner = 0;
        if ($this->user->user_type == 3) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
            $is_partner = 1;
        } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
            if ($this->partnerPaymentDetails) {
                $is_partner = 1;
            } else {
                throw new NotFoundException('No payment gateway details found for this partner.');
            }
        } else {
            $is_partner = 0;
        }

        //log all request in event table
        $requestValuesLogs = $request->all();
        unset($requestValuesLogs['nonce']);
        unset($requestValuesLogs['name_on_card']);
        unset($requestValuesLogs['card_number']);
        unset($requestValuesLogs['expiration_date']);
        unset($requestValuesLogs['security_code']);
        $this->log->info("Transient User Touchless Payment request --" . json_encode($requestValuesLogs));

        $this->logPayment->info("Make Payment Request" . json_encode($request));
        //saving to event table after check in payment functions

        $arrEvents = array();

        $arrEvents['user_id'] = isset($this->user->id) ? $this->user->id : '0';
        $arrEvents['event_name'] = "Make Payment After drive up check in";
        $arrEvents['facility_id'] = isset($this->request->facility_id) ? $this->request->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Make Payment Function makePayment Request " . json_encode($requestValuesLogs);
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = isset($this->user->email) ? $this->user->email : '';

        $this->saveEventsLogs($arrEvents);


        if ($this->request->total > 0) {
            try {




                if ($is_partner == 1) {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->isPartner($this->partnerPaymentDetails)
                        ->setBillingAddress($this->getBillingArray());
                } else {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->setBillingAddress($this->getBillingArray());
                }

                if (isset($this->request->payment_profile_id) && !empty($this->request->payment_profile_id)) { // Use our logged in users profile

                    $customerProfile = $this->user->cim;

                    if (!$customerProfile) {  // Create a cim for this user so they can store their details and reuse them
                        throw new ApiGenericException('No customer payment profile found for this user.');
                    }
                    $profiles = \DB::table('anet_payment_profiles')->where('payment_profile', $this->request->payment_profile_id)->orderBy("id", "DESC")->first();
                    if (!$profiles) {
                        throw new ApiGenericException('No payment profile found for this user.');
                    }


                    $this->paymentProfileId = $profiles->payment_profile;
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    $this->authNet->isPartner($this->partnerPaymentDetails)
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            } catch (Exception $e) {
                $this->log->error($e);

                throw new ApiGenericException(['errMsg' => $e]);
            }
        }

        $now = date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime, strtotime("+1 hours")));
        $arrival_time = Carbon::createFromFormat("Y-m-d H:i:s", date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime, strtotime("+1 hours"))));
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($now . ' + ' . $this->request->no_of_days . ' hours')));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours > 24) {
            $diff_in_hours = 24;
        }


        $rate = [];
        $is_resident_user = '0';
        if ($ticket->user->license_number != '') {

            $mystring = $ticket->user->license_number;
            $mystring = trim($mystring); //removes any spaces from beginning of the string
            $number = substr($mystring, 1);

            if (ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring) == 9) {
                $is_resident_user = '1';
            } else {
                $is_resident_user = '0';
            }
        }


        $residentRate = Rate::where('is_resident', $is_resident_user)->where('facility_id', $this->facility->id)->first();
        if (!$residentRate) {
            throw new ApiGenericException('Rate Not Found.');
        }
        //$price_in_hours = $diff_in_hours - $residentRate->free_hours;
        /*if($diff_in_hours > $residentRate->free_hours){
            $price_in_hours = $diff_in_hours - $residentRate->free_hours;
        }*/
        $rate['price'] = $residentRate->price * $diff_in_hours;

        $facilityFee = FacilityFee::where("facility_id",  $this->facility->id)->where("name", "citation_processing_fee")->first();
        $facilityProcessingFee = 0.00;
        if ($facilityFee) {
            $facilityProcessingFee = $facilityFee['val'];
        }
        $processingFee = $facilityProcessingFee;

        $total = $rate['price'] + $this->facility->tax_rate + $processingFee;
        //my code
        //$total= $total+ ($this->request->no_of_days*2);
        //my end code
        if ($total > 0) {
            $reservationMode = "Ticketless Parking";
            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else {
                $ticketech_guid = rand(100000, 999999);
            }

            try {

                // Use our database rate price to create the transaction
                try {
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();
                    // Fire off transaction - this method will throw an exception if errors occur
                } catch (AuthorizeNetException $e) {
                    $this->log->error($e->getMessage());
                    throw new ApiGenericException(['errMsg' => $e->getMessage()]);
                }
            } catch (Exception $e) {

                $this->log->error($e);
                throw new ApiGenericException(['errMsg' => $e->getMessage()]);
            }
            // Charge successful, save transaction relationship to it
            $ticket->length = $ticket->length + $diff_in_hours;
            $ticket->total = $ticket->total + $total;
            $ticket->grand_total = $ticket->grand_total + $total;
            $ticket->checkout_datetime = date("Y-m-d H:i:s", strtotime($from));
            $ticket->estimated_checkout = date("Y-m-d H:i:s", strtotime($from));
            $ticket->checkout_time = date("Y-m-d H:i:s", strtotime($from));
            $ticket->old_anet_transaction_id = $ticket->anet_transaction_id;
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->is_checkout = '1';
            $ticket->save();
            $this->log->info("user extend payment done payment ticket id {$ticket->id} ticket number {$ticket->ticket_number} and checkout with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {

                Artisan::queue('email:transient-parking-autogate-checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));

                return $ticket;
            }
        } else {
            throw new ApiGenericException('Payment failed due to invalid details.');
        }
    }

    public function makeCitationPayment(Request $request)
    {


        $this->setDecryptedCard($request);

        if (strlen($this->request->expiration_date) < 4) {
            throw new ApiGenericException('Error! Invalid expiry date.');
        }
        if (strlen($this->request->card_number) < 13) {
            throw new ApiGenericException('Error! Invalid card number.');
        }
        if (strlen($this->request->security_code) < 3) {
            throw new ApiGenericException('Error! The security code must be between 3 and 4 digits.');
        }
        //$this->checkFacilityAvailable($this->request->facility_id);

        $this->setCustomTimezone($this->request->facility_id);

        if (preg_match('/^[0-9]{10}+$/', $request->phone)) {
            if ($request->phone == "0000000000") {

                throw new ApiGenericException('Please enter valid phone number.');
            }
        } else {
            throw new ApiGenericException('Please enter valid phone number.');
        }
        if ($this->request->email != '') {
            if (preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $this->request->email)) {
            } else {
                throw new ApiGenericException('Please enter valid email.');
            }
        }

        $ticket = TicketCitation::where('citation_number', $request->citation_number)->first();
        if (!$ticket) {
            throw new ApiGenericException('Invalid citation.');
        }
        if ($ticket->is_closed == '1') {
            throw new ApiGenericException('Error! Citation already paid and closed.');
        }

        //$this->validate($this->request, $this->billingValidation);
        $this->facility = Facility::find($ticket->facility_id);
        $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
        if ($geoLocation['geoplugin_countryCode'] == 'IN') {
            $countryCode = "+91";
        } elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
            $countryCode = "+1";
        } else {
            $countryCode = "+1";
        }
        $exist = User::where('phone', $countryCode . $this->request->phone)->where('created_by', $this->facility->owner_id)->first();
        if ($exist) {
            $this->user = $exist;
            if ($this->request->name_on_card != '') {
                $this->user->name = $this->request->name_on_card;
            }
            if ($this->request->email != '') {
                $this->user->email = $this->request->email;
            }
            $this->user->save();
        } else {
            $this->user =  User::create(
                [
                    'name' => $this->request->name_on_card,
                    'email' => $request->email != '' ? $request->email : '',
                    'phone' => $countryCode . $request->phone,
                    'password' => Hash::make(str_random(60)),
                    'anon' => false,
                    'user_type' => '5',
                    'created_by' => $this->facility->owner_id
                ]
            );
        }

        //log all request in event table
        $requestValuesLogs = $request->all();
        unset($requestValuesLogs['nonce']);
        unset($requestValuesLogs['name_on_card']);
        unset($requestValuesLogs['card_number']);
        unset($requestValuesLogs['expiration_date']);
        unset($requestValuesLogs['security_code']);
        $this->log->info("Autogate User Touchless Payment request --" . json_encode($requestValuesLogs));

        $this->logPayment->info("Make Payment Request" . json_encode($request));
        //saving to event table after check in payment functions

        $arrEvents = array();

        $arrEvents['user_id'] = isset($this->user->id) ? $this->user->id : '0';
        $arrEvents['event_name'] = "Make Payment After drive up check in";
        $arrEvents['facility_id'] = isset($this->request->facility_id) ? $this->request->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Make Payment Function makePayment Request " . json_encode($requestValuesLogs);
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = isset($this->user->email) ? $this->user->email : '';

        $this->saveEventsLogs($arrEvents);


        $now = date("Y-m-d H:i:s");
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($now)));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        $diff_in_mins = $arrival_time->diffInRealMinutes($from);
        $diff_in_sec = $arrival_time->diffInSeconds($from);
        if ($diff_in_hours > 24) {
            $diff_in_hours = $diff_in_hours;
        }

        if ($diff_in_hours == 0 && $diff_in_mins != 0) {
            $diff_in_hours = 1;
        }

        if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
            $diff_in_hours = 1;
        }



        $rate = [];
        $is_resident_user = '0';
        $categoryId = self::NON_RESIDENT_ID;
        $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $this->facility->id)->first();
        if (!$residentRate) {
            throw new ApiGenericException('diamond-error-facility');
        }
        //$price_in_hours = $diff_in_hours - $residentRate->free_hours;
        /*if($diff_in_hours > $residentRate->free_hours){
            $price_in_hours = $diff_in_hours - $residentRate->free_hours;
        }*/
        $rate['price'] = $residentRate->price * $diff_in_hours;

        $facilityFee = FacilityFee::where("facility_id",  $this->facility->id)->where("name", "citation_processing_fee")->first();
        $facilityProcessingFee = 0.00;
        if ($facilityFee) {
            $facilityProcessingFee = $facilityFee['val'];
        }
        $processingFee = $facilityProcessingFee;

        $total = $rate['price'] + $this->facility->tax_rate + $processingFee + $this->facility->penalty_fee;

        //return current availabilit
        if ($rate['price'] == 'N/A') {
            throw new ApiGenericException('Rate does not match in our database.');
        }
        $days = $diff_in_hours;
        //$processingFee = $this->facility->citation_processing_fee;
        $taxFee = $this->facility->tax_rate;
        /*$rate['price'] = $rate['price'] + $this->facility->penalty_fee + $taxFee + $processingFee;
        if($rate['price'] != $this->request->total){
          return back()->with("danger","Rate does not match in our database.")->withInput();
        }*/

        //Amount validate here
        if ($total != $this->request->total) {
            throw new ApiGenericException('Amount Invalide');
        }


        if ($this->request->total > 0) {
            try {

                $is_partner = 0;
                if ($this->user->user_type == 3) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if ($this->partnerPaymentDetails) {
                        $is_partner = 1;
                    } else {
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                } else {
                    $is_partner = 0;
                }
                if ($is_partner == 1) {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->isPartner($this->partnerPaymentDetails)
                        ->setBillingAddress($this->getBillingArray());
                } else {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->setBillingAddress($this->getBillingArray());
                }

                if (isset($this->paymentProfileId)) { // Use our logged in users profile
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    $this->authNet->isPartner($this->partnerPaymentDetails)
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            } catch (Exception $e) {
                $this->log->error($e);
                throw new ApiGenericException('Unable to authorize the credit card details. Please check the card info or use another card and try again.');
            }
        }

        //$ticket = $this->saveTicket();
        if ($this->request->total > 0) {
            $reservationMode = "Ticketless Parking Citation";
            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else {
                $ticketech_guid = rand(100000, 999999);
            }

            try {

                // Use our database rate price to create the transaction
                try {
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->citation_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();
                    // Fire off transaction - this method will throw an exception if errors occur
                } catch (AuthorizeNetException $e) {
                    $this->log->error($e->getMessage());
                    return back()->with('danger', $e->getMessage());
                }
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                /*dd($e);
                return back()->withError(['errMsg'=>$e]);
                dd(111);
                $transaction = $this->authNet->getTransaction();
                $ticket->anet_transaction_id = $transaction->id;
                $ticket->save();       
                $ticket->delete();
                return back()->withError(['errMsg'=>$e]);*/
                //$ticket->delete();
                $this->log->error($e);
                return back()->with('danger', $e->getMessage());
            }
            // Charge successful, save transaction relationship to it

            $ticket->user_id = $this->user->id;
            $ticket->checkout_time = date("Y-m-d H:i:s");
            $ticket->estimated_checkout = date("Y-m-d H:i:s");
            $ticket->total = $this->request->total;
            $ticket->grand_total = $this->request->total;
            $ticket->partner_id = $this->facility->owner_id;
            $ticket->save();


            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->is_checkout = '1';
            $ticket->is_closed = '1';
            $ticket->processing_fee = $processingFee;
            $ticket->tax_fee = $taxFee;
            $ticket->penalty_fee = $this->facility->penalty_fee;
            $ticket->payment_date = date("Y-m-d H:i:s");
            $ticket->save();
            $this->log->info("user done citation payment citation id {$ticket->id} citation number {$ticket->ticket_number} and checkout with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {
                Artisan::queue('email:diamond-parking-autogate-checkin-payment', array('id' => $ticket->id, 'type' => 'citation'));
                return $ticket;
            }
        } else {
            throw new ApiGenericException('Error! Payment failed due to invalid details.');
        }
    }



    public function citationRate(Request $request)
    {
        if (substr($request->citation_number, 0, 2) === 'WA') {
            $ticket = Warning::with(['warningInfraction.warningInfractionReason', 'warningInfractionOther'])->where('warning_number', $request->citation_number)->first();
            if (!$ticket) {
                throw new ApiGenericException('Invalid warning.');
            }

            $this->facility = Facility::with('faciltyPaymentDetail')->where('id', $ticket->facility_id)->first();

            $data['ticket'] = $ticket;
            $data['facility'] =  $this->facility;
        } else {
            $ticket = TicketCitation::with(['ticketCitationInfraction.ticketCitationInfractionReason', 'ticketCitationInfractionOther', 'ticketCitationImage', 'appeal'])->where('citation_number', $request->citation_number)->first();
            if (!$ticket) {
                throw new ApiGenericException('Invalid citation.');
            }
            /*if($ticket->anet_transaction_id != ''){
                throw new ApiGenericException('You have already done the payment.');  
            }*/
            $this->facility = Facility::with('faciltyPaymentDetail')->where('id', $ticket->facility_id)->first();

            $amount = number_format($ticket->penalty_fee, 2);
            $data['amount'] =  (float) $amount;
            $total = $ticket->penalty_fee;
            $data['ticket'] = $ticket;
            $data['rate'] = $total;
            $data['facility'] =  $this->facility;
        }


        $citation_history = TicketCitation::where('license_plate', $ticket->license_plate)->orderBy("id", "DESC")->get();
        $warning_history = Warning::where('license_plate', $ticket->license_plate)->orderBy("id", "DESC")->get();
        $data['citation_history'] = $citation_history;
        $data['warning_history'] = $warning_history;

        return $data;
    }

    protected function checkReferenceNumber()
    {
        $reference = rand(1000, 9999) . rand(1000, 9999) . rand(1000, 9999);;
        $resRefrenceExist = TicketCitation::where("mer_reference", $reference)->first();
        if ($resRefrenceExist) {
            $this->checkReferenceNumber();
        }
        return $reference;
    }


    public function postSuccess(Request $request)
    {
        $this->log->info("Diamond Payment Success call Back :" . json_encode($request->all()));
        $this->request = $request;
        if ($this->request->TokenNo == '' || $this->request->AuthorisationCode == '') {
            $this->log->error("Invalid token in Diamond Payment Success call Back");
            throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
        }

        $ticket = TicketCitation::with('user')->where("mer_reference", $this->request->ref)->first();
        if (isset($ticket) && !empty($ticket)) {
            $this->log->info("Citiation Ticket confirmed:" . json_encode($request->all()));
            $amount = $this->request->Amount;
            $amount = (float) $amount;
            $user = User::find($ticket->user_id);
            if ($amount > 0) {
                // Charge successful, save transaction relationship to it						
                if ($this->request->TokenNo != '') {
                    $authorized_anet_transaction = new AuthorizeNetTransaction();
                    $authorized_anet_transaction->sent = '1';
                    $authorized_anet_transaction->user_id = $ticket->user_id;
                    $authorized_anet_transaction->total = $amount;
                    $authorized_anet_transaction->description = "Citation Payment  Done : " . $ticket->citation_number;
                    $authorized_anet_transaction->card_type = $this->request->CardType;
                    $authorized_anet_transaction->ref_id = $this->request->ref;
                    $authorized_anet_transaction->anet_trans_id = $this->request->TxID;
                    $authorized_anet_transaction->method = "card";
                    $authorized_anet_transaction->payment_last_four = isset($this->request->card_pan_last4digits) ? $this->request->card_pan_last4digits : '0';
                    $authorized_anet_transaction->auth_code = isset($this->request->AuthorisationCode) ? $this->request->AuthorisationCode : '0';
                    $authorized_anet_transaction->ip_address = \Request::ip();
                    $authorized_anet_transaction->save();
                    $ticket->anet_transaction_id = $authorized_anet_transaction->id;
                    $ticket->payment_status = '1';
                    $ticket->is_closed = '1';
                    $ticket->save();
                    $user->session_id = $this->request->TokenNo;
                    $user->save();

                    $userSession = UserSession::where("session_id", $this->request->TokenNo)->where('partner_id', $user->created_by)->first();
                    if (!$userSession) {
                        $userSession = new UserSession();
                        $userSession->user_id = $user->id;
                        $userSession->partner_id = $user->created_by;
                        $userSession->session_id = $this->request->TokenNo;
                        $userSession->save();
                        $this->log->info("User session saved :" . json_encode($request->all()));
                    } else {
                        $this->log->error("Diamond Payment Session Found");
                    }
                    // Email Send Code Start
                    $this->log->info("Diamond citation payment email start");
                    Artisan::queue('email:diamond-parking-autogate-checkin-payment', array('id' => $ticket->id, 'type' => 'citation'));
                    $this->log->info("Diamond citation payment email Sent");
                    // Email Send Code End

                }
            } else {
                throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
            }
        }
    }

    public function postFail(Request $request)
    {
        $this->log->info("Diamond Payment Fail call Back :" . json_encode($request->all()));
        $ticket = TicketCitation::where('mer_reference', $this->request->ref)->first();
        if (!$ticket) {
            throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
        }
        $ticket->payment_status = '2';
        $ticket->save();
        return 1;
    }


    //before function changed by vikrant now payment will cut by this API either datacap or planet
    public function updateCitationBeforePayment(Request $request)
    {
        $this->log->info("Diamond Before Payment request received :" . json_encode($request->all()));
        $this->request = $request;
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            $citation = TicketCitation::with('facility.facilityPaymentDetails')->where("citation_number", $this->request->citation_number)->first();
            if (!$citation) {
                throw new NotFoundException('Invalid citation.');
            }

            if ($citation->anet_transaction_id != '') {
                throw new ApiGenericException('You have already done the payment.');
            }

            $this->countryCode = QueryBuilder::appendCountryCode();
            $user = User::where('email', $this->request->email)->where('created_by', $secret->partner_id)->first();
            if (!$user) {
                $this->user = User::create(
                    [
                        'name' => $this->request->name,
                        'email' => $this->request->email,
                        'phone' => $this->countryCode . $this->request->phone,
                        'password' => Hash::make(str_random(60)),
                        'anon' => false,
                        'user_type' => '5',
                        'created_by' => $secret->partner_id,
                    ]
                );
            } else {
                $this->user = $user;
            }

            $citation->user_id = $this->user->id;
            $citation->save();
        } else {
            throw new NotFoundException('No partner found.');
        }

        if (isset($citation) && !empty($citation)) {
            $amount = $citation->penalty_fee;
            if ($citation->discount_amount > 0) {
                $amount = $citation->discount_amount;
            }

            if ($amount > 0) {
                $this->setDecryptedCard($this->request);
                if (isset($citation->facility->facilityPaymentDetails) && ($citation->facility->facilityPaymentDetails->facility_payment_type_id == '1')) {
                    $this->log->info("Make Planet Payment new  ");
                    $refundstatus = PlanetPaymentGateway::planetPaymentByCrediCard($citation, $amount, $this->request);
                    $this->log->info("Response Data Planet Planet (Non Saved Cards): " . json_encode($refundstatus));
                    $saveTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $citation->user_id);
                    if ($saveTransaction) {
                        $citation->anet_transaction_id = $saveTransaction->id;
                        $citation->save();
                    }
                } else if (isset($citation->facility->facilityPaymentDetails) && ($citation->facility->facilityPaymentDetails->facility_payment_type_id == '2')) {
                    $this->log->info("Make Datacap Payment new  ");
                    $card_month = substr($this->request->expiration_date, 0, 2);
                    $card_year = substr($this->request->expiration_date, -2);
                    $this->request->request->add(['expiration_month' => $card_month]);
                    $this->request->request->add(['expiration_year' => $card_year]);

                    $datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($this->request, $citation->facility);

                    if ($datacapPaymentToken["Token"]) {
                        $amount = ($citation->facility->facilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $amount;
                        $data['Amount'] = $amount;
                        $data['Token'] = $datacapPaymentToken["Token"];
                        $data["CardHolderID"] = "Allow_V2";
                        $ecommerce_mid = $citation->facility->facilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $citation->facility->facilityPaymentDetails->datacap_script_url;
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("Datacap Citation Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        } else if ($paymentResponse["Status"] == "Approved") {
                            $card_last_four = substr($paymentResponse['Account'], -4);
                            $this->request->request->add(['expiration' => $this->request->expiration_date]);
                            $this->request->request->add(['card_last_four' => $card_last_four]);
                        } else {
                            throw new ApiGenericException("Error in Making Payment.");
                        }
                    } else {
                        throw new ApiGenericException("Payment Token Not Generated. Please try again after sometime.");
                    }

                    if ($paymentResponse['Status'] == 'Approved') {
                        $user_id = $citation->user_id;

                        $paymentStatus = DatacapPaymentGateway::saveDatacapTransaction($this->request, $paymentResponse, $user_id, '');
                        $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($paymentStatus));

                        if ($paymentStatus) {
                            $citation->anet_transaction_id = $paymentStatus->id;
                            $citation->save();
                        }
                    } else {
                        throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                    }
                } elseif (isset($citation->facility->facilityPaymentDetails) && ($citation->facility->facilityPaymentDetails->facility_payment_type_id == '4')) {
                    $this->log->info("Make autopay Heartland Payment ");

                    $this->setDecryptedCard($request);
                    $card_month = substr($request->expiration_date, 0, 2);
                    $card_year = substr($request->expiration_date, -2);
                    $request->request->add(['expiration_month' => $card_month]);
                    $request->request->add(['expiration_year' => $card_year]);
                    $amount = number_format($amount, 2);
                    $request->request->add(['Amount' => $amount]);
                    $request->request->add(['expiration' => $request->expiration_date]);
                    $request->request->add(['expiration_date' => $request->expiration_date]);
                    $card_last_four = substr($request->card_number, -4);
                    $request->request->add(['card_last_four' => $card_last_four]);
                    $request->request->add(['total' => $amount]);
                    // otuPaymentTokenHeartland
                    $otu_token = HeartlandPaymentGateway::otuPaymentTokenHeartland($this->request, $citation->facility);
                    //dd($otu_token);
                    $this->log->info("Heartland OTU Token: " . json_encode($otu_token));

                    if ($otu_token) {
                        $request->request->add(['token' => $otu_token['token_value']]);
                        try {
                            $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($this->request, $citation->facility);
                            $this->log->info("Heartland card Payment Response: " . json_encode($paymentResponse));
                        } catch (Exception $e) {
                            $this->log->info("Error in Heartland Payment with card --" . json_encode($e->getMessage()));
                            throw new ApiGenericException($e->getMessage());
                        }
                    } else {
                        throw new ApiGenericException("Please contact to Admin");
                    }

                    if ($paymentResponse->responseMessage == 'APPROVAL') {
                        $user_id = $this->user->id;
                        $paymentStatus = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $user_id);
                        $this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($paymentStatus));
                        /*$cardCheck = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $citation->partner_id)->where('card_last_four', $request->card_last_four)->first();
                        
                        /if ($request->user_consent == 1 && !$cardCheck) {
                            $response = HeartlandPaymentGateway::saveHeartlandCard($paymentResponse, $user_id, $request);
                        }*/

                        if ($paymentStatus) {
                            $citation->anet_transaction_id = $paymentStatus->id;
                            $citation->save();
                        }
                        // return $paymentStatus;
                    } else {
                        throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                    }
                } else {
                    throw new ApiGenericException("Payment details not defined for this partner.");
                }

                $citation->grand_total = $amount;
                $citation->payment_status = '1';
                $citation->is_closed = '1';
                $citation->closed_date = date("Y-m-d H:i:s");
                $citation->save();

                if ($citation->parent_id != '') {
                    TicketCitation::where(['parent_id' => $citation->parent_id])->update(['is_closed' => '1', "closed_date" => date("Y-m-d H:i:s")]);
                    TicketCitation::where(['id' => $citation->parent_id])->update(['is_closed' => '1', "closed_date" => date("Y-m-d H:i:s")]);
                }

                Artisan::queue('email:diamond-parking-autogate-checkin-payment', array('id' => $citation->id, 'type' => 'citation'));
                return $citation;
            } else {

                if ($citation->discount_amount > 0) {
                    $citation->grand_total = $amount;
                    $citation->payment_status = '1';
                    $citation->is_closed = '1';
                    $citation->closed_date = date("Y-m-d H:i:s");
                    $citation->save();

                    if ($citation->parent_id != '') {
                        TicketCitation::where(['parent_id' => $citation->parent_id])->update(['is_closed' => '1', "closed_date" => date("Y-m-d H:i:s")]);
                        TicketCitation::where(['id' => $citation->parent_id])->update(['is_closed' => '1', "closed_date" => date("Y-m-d H:i:s")]);
                    }

                    Artisan::queue('email:diamond-parking-autogate-checkin-payment', array('id' => $citation->id, 'type' => 'citation'));
                    return $citation;
                }

                throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
            }
        }
    }

    public function paymentSuccess(Request $request)
    {

        $this->log->info("Diamond After Payment Success request received : " . json_encode($request->all()));
        if ($request->header('X-ClientSecret') != '') {
            $this->log->info("Diamond After Payment Success X-ClientSecret Found : " . $request->header('X-ClientSecret'));
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                //throw new NotFoundException('No partner found.');
                $this->log->info("No partner found");
            }

            if ($this->request->TokenNo == '' || $this->request->AuthorisationCode == '') {
                //throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
                $this->log->info("Sorry! Payment not successfully done, There is some issue in payment response.");
            }

            if (!$this->request->MerchantRef) {
                $this->log->info("MerchantRef Not Found");
            }
            $citation = TicketCitation::with(['user', 'transaction'])->where('partner_id', $secret->partner_id)->where('mer_reference', $this->request->MerchantRef)->first();
            if (!$citation) {
                throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
            }

            return $citation;
        } else {
            throw new ApiGenericException("Invalid partner.");
        }
    }


    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }


    protected function saveTicket()
    {
        $ticket = new Ticket();
        $ticket->user_id = $this->user->id;
        $ticket->facility_id = $this->facility->id;
        $ticket->length = $this->request->length;
        $ticket->ticket_security_code = rand(1000, 9999);
        $ticket->total = $this->request->total;
        $ticket->grand_total = $this->request->total;
        $ticket->check_in_datetime = date('Y-m-d H:i:s');
        $ticket->checkout_datetime = date('Y-m-d H:i:s', strtotime($this->request->check_out_datetime));
        $ticket->checkin_time = date('Y-m-d H:i:s');
        $ticket->checkout_time = date('Y-m-d H:i:s', strtotime($this->request->check_out_datetime));
        $ticket->estimated_checkout = date('Y-m-d H:i:s', strtotime($this->request->check_out_datetime));
        $ticket->ticket_number = $this->checkTicketNumber();
        $ticket->is_checkin = '1';
        //$ticket->checkin_gate = Session::get('gate');
        $ticket->partner_id = $this->facility->owner_id;
        $ticket->license_plate = $this->request->license_plate;
        $ticket->car_model = $this->request->car_model;
        $ticket->is_autopay = $this->request->is_autopay;
        $ticket->processing_fee = $this->request->processing_fee;
        $ticket->tax_fee = $this->request->tax_fee;
        $ticket->is_vehicle_present = '1';
        $ticket->scan_date = date("Y-m-d H:i:s");

        $ticket->save();

        return $ticket;
    }

    protected function saveOverstayTicket()
    {
        $ticket = new OverstayTicket();
        $ticket->user_id = $this->user->id;
        $ticket->facility_id = $this->facility->id;
        $ticket->length = $this->request->length;
        $ticket->total = $this->request->total;
        $ticket->ticket_number = $this->request->ticket_number;
        $ticket->is_checkin = '1';
        $ticket->check_in_datetime = date('Y-m-d H:i:s', strtotime($this->request->check_in_datetime));
        $ticket->checkout_datetime = date('Y-m-d H:i:s');
        $ticket->save();

        $mainTicket = Ticket::where('ticket_number', $this->request->ticket_number)->first();
        $mainTicket->grand_total = $this->request->grand_total + $ticket->total;
        $mainTicket->is_overstay = '1';
        $mainTicket->estimated_checkout = date('Y-m-d H:i:s', strtotime("+" . self::GRACE_PERIOD . " hours"));
        $mainTicket->save();
        return $ticket;
    }


    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = isset($cardData[4]) ? $cardData[4] : '';
        $request->request->add(
            [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode
            ]
        );

        $this->request = $request;
    }

    public function customDecrypt(Request $request)
    {
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt('{"ct":"6uXkt0HGZoKrvURZeQ8pIQ==","iv":"06a933e8dfd1994452a6b72a5a83a7ec","s":"50846949bac62518"}');
        dd($decryptedNonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = isset($cardData[4]) ? $cardData[4] : '';
        $request->request->add(
            [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode
            ]
        );

        dd($request);
    }


    public function makeOverstayPayment(Request $request)
    {
        $this->setDecryptedCard($request);
        if (strlen($this->request->expiration_date) < 5) {
            return back()->with('danger', "Error! Invalid expiry date.");
        }
        if (strlen($this->request->card_number) < 13) {
            return back()->with('danger', "Error! Invalid card number.");
        }
        if (strlen($this->request->security_code) < 3) {
            return back()->with('danger', "Error! The security code must be between 3 and 4 digits.");
        }

        $this->setCustomTimezone($this->request->facility_id);

        if (preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $this->request->email)) {
        } else {
            return back()->with('danger', "Please enter valid email.");
        }

        //$this->validate($this->request, $this->billingValidation);
        $this->user = User::getUserByPhone($this->request->email, $this->request->phone);
        $this->facility = Facility::find($this->request->facility_id);
        if ($this->request->total > 0) {
            try {
                /*$is_partner = 0;
                if($this->user->user_type == 3){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                }elseif($this->user->user_type == 4 || $this->user->user_type == 5){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if($this->partnerPaymentDetails){
                        $is_partner = 1;
                    }else{
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                }else{
                    $is_partner = 0;
                }
                if($is_partner == 1){
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()                
                    ->setFacility($this->facility)
                    ->isPartner($this->partnerPaymentDetails)
                    ->setBillingAddress($this->getBillingArray());
                }else{
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());
                }*/
                $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());

                if (isset($this->paymentProfileId)) { // Use our logged in users profile
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    /*$this->authNet->isPartner($this->partnerPaymentDetails)
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);*/
                    $this->authNet
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            } catch (Exception $e) {
                $request->session()->flash(
                    'alert-danger',
                    'Payment error!'
                );
                return back()->withError(['danger' => $e]);
            }
        }

        $ticket = $this->saveOverstayTicket();
        if ($this->request->total > 0) {
            $reservationMode = "Ticketles Overstay Parking";
            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else {
                $ticketech_guid = rand(100000, 999999);
            }

            try {

                // Use our database rate price to create the transaction
                try {
                    /*$charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();*/
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->executeTransaction();
                    // Fire off transaction - this method will throw an exception if errors occur
                } catch (AuthorizeNetException $e) {
                    $ticket->delete();
                    return back()->with('danger', $e->getMessage());
                }
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                /*dd($e);
                return back()->withError(['errMsg'=>$e]);
                dd(111);
                $transaction = $this->authNet->getTransaction();
                $ticket->anet_transaction_id = $transaction->id;
                $ticket->save();       
                $ticket->delete();
                return back()->withError(['errMsg'=>$e]);*/
                $ticket->delete();
                return back()->with('danger', $e->getMessage());
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->save();
            $this->log->info("user have paid overstay payment and redirect to checkout page with ticket number {$ticket->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {
                Artisan::queue('email:checkin-payment', array('id' => $ticket->id, 'type' => 'overstay'));
                return redirect('thankyou-payment/' . base64_encode($ticket->ticket_number))->with('success', 'Success! Overstay payment successfully done.');
            }
        } else {
            return back()->with('danger', "Please use valid ticket number with amount.");
        }
    }


    public function confirmPrepaidCheckin(Request $request)
    {
        /*$request = $request->all();
        dd($request);*/
        try {
            if (isset($request->encrypt)) {
                $explode = explode(':', $request->encrypt);
                if (count($explode) <= 1) {
                    return back()->with('danger', "Please scan the right QR code.");
                }
                $decrypt = base64_decode($explode[2]);
                if ($decrypt) {
                    $decrytArray = json_decode($decrypt);
                    $this->checkFacilityAvailable($decrytArray->facility_id);

                    $this->setCustomTimezone($decrytArray->facility_id);

                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                    $checkinData = [];
                    if ($gate && isset($gate->gate_type)) {
                        if ($gate->gate_type == 'entry') {

                            $facility = Facility::where('id', $decrytArray->facility_id)->first();
                            if (!$facility) {
                                return back()->with('danger', "Invalid garage.");
                            }

                            //check third party gate API
                            /*$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                        if($gateDetails){
                            if($gateDetails->host !=''){
                                $params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate];
                                $this->log->info("Abacus Web Service : vehicle command aboout to send checkin entry facility {$facility->id} params ".json_encode($params));
                                $response = AbacusWebService::isvehicleAvailable($params, $gateDetails->host);
                                if($response['success'] == false){
                                    return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                }
                                $this->log->info("Abacus Web Service : vehicle command complete checkin entry facility {$facility->id} response". json_encode($response));
                                if($response['data']['IsLoopVon'] == '1' || $response['data']['IsLoopVon'] == '0'){
                                    $guid = AbacusWebService::createGuid();
                                    $cmd_params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate,'command'=>'45','iParameter1'=>'0','iParameter2'=>'0','iParameter3'=>'0','iParameter4'=>'0','textParameter1'=>'0','textParameter2'=>'0', 'guidParameter1'=>$guid, 'guidParameter2'=>$guid];
                                    $this->log->info("Abacus Web Service : command run success checkin entry facility {$facility->id} params". json_encode($cmd_params));
                                $command_response = AbacusWebService::commandStatus($cmd_params, $gateDetails->host);
                                if($command_response['success'] == true){
                                    if($command_response['data'][0] == "0" || $command_response['data'][0] == "1"){
                            
                                    }else{
                                        return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                    }
                                    
                                }else{
                                    return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                }
                                }

                            }
                        }*/

                            $checkinData = Ticket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '0')->first();
                            if ($checkinData) {
                                $checkinData->checkin_gate = $decrytArray->gate;
                                $checkinData->is_checkin = '1';
                                $checkinData->save();
                                Artisan::queue('email:confirm-checkin', array('id' => $checkinData->id, 'type' => 'checkin'));
                                return redirect('prepaid-checkin-success/' . base64_encode($checkinData->ticket_number))->with('success', 'Success! You have successfully check-in.');
                            } else {
                                return back()->with('danger', 'You have already checkin.');
                            }
                        } else {
                            return back()->with('danger', "Please scan the right QR code.");
                        }
                    } else {
                        return Redirect::to('error-checkin');
                    }
                }
            } else {
                return back()->with('danger', 'Please scan valid scan QR code.');
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid scan QR code.');
        }
    }

    public function getPrepaidConfirmCheckinThankyou($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);

        $currentStartDate = Carbon::createFromFormat('Y-m-d H:i:s', ($checkinData->checkin_time == '' ? date('Y-m-d H:i:s') : $checkinData->checkin_time));
        $currentEndDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['current_check_in_datetime'] = $currentStartDate;
        $checkinData['current_checkout_datetime'] = $currentEndDate;
        $checkinData['current_diff_in_days'] = $currentStartDate->diffInDays($currentEndDate);
        $checkinData['current_diff_in_hours'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->diffInRealHours($currentEndDate);
        $checkinData['current_diff_in_minutes'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->addHours($checkinData['current_diff_in_hours'])->diffInRealMinutes($currentEndDate);

        return view('atlanta-checkin-checkout.prepaid-confirm-checkin', ['data' => $checkinData]);
    }

    public function getPrepaidConfirmCheckinSuccess($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('checkin-checkout.prepaid-checkin-success', ['data' => $checkinData]);
    }

    public function getPrepaidCheckoutDetails($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('checkin-checkout.prepaid-checkout-screen', ['data' => $checkinData]);
    }



    public function getPrepaidCheckinCheckoutDetails($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        if (Session::get('is_sms_direct_checkout') == '1') {
            $checkinData->is_checkout = '1';
            $checkinData->checkout_time = date("Y-m-d H:i:s");
            $checkinData->save();

            $reservation = Reservation::where('id', $checkinData->reservation_id)->first();
            $reservation->is_ticket = '2';
            $reservation->save();
            Session::forget('is_sms_direct_checkout');
            return redirect('thankyou-prepaid-checkout/' . base64_encode($checkinData->ticket_number));
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('checkin-checkout.thankyou-confirm-checkin', ['data' => $checkinData]);
    }


    public function confirmPrepaidCheckout(Request $request)
    {
        //$request = $request->all();
        try {
            if ($request->encrypt) {
                $explode = explode('/', $request->encrypt);
                if (count($explode) < 5) {
                    return back()->with('danger', "Please scan the right QR code.");
                }
                $decrypt = base64_decode($explode[4]);
                if ($decrypt) {
                    $decrytArray = json_decode($decrypt);
                    if ($decrytArray) {

                        $this->setCustomTimezone($decrytArray->facility_id);

                        $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                        $checkinData = [];
                        if ($gate && isset($gate->gate_type)) {
                            if ($gate->gate_type == 'exit') {
                                $is_checkout = Ticket::where('ticket_number', $request->order_number)->where('is_checkout', '1')->first();
                                if ($is_checkout) {
                                    return Redirect::to('already-checkout');
                                }


                                $facility = Facility::where('id', $decrytArray->facility_id)->first();
                                if (!$facility) {
                                    return back()->with('danger', "Invalid garage.");
                                }

                                //check third party gate API
                                $gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                                if ($gateDetails) {
                                    if ($gateDetails->host != '') {
                                        $params = ['user' => $gateDetails->username, 'pwd' => $gateDetails->password, 'System' => $gate->system, 'Tcc' => $gate->gate];
                                        $this->log->info("Abacus Web Service : vehicle command aboout to send checkout exit facility {$facility->id} params " . json_encode($params));
                                        $response = AbacusWebService::isvehicleAvailable($params, $gateDetails->host);
                                        if ($response['success'] == false) {
                                            return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                        }
                                        $this->log->info("Abacus Web Service : vehicle command complete checkout exit facility {$facility->id} response" . json_encode($response));
                                        if ($response['data']['IsLoopVon'] == '1' || $response['data']['IsLoopVon'] == '0') {
                                            $guid = AbacusWebService::createGuid();
                                            $cmd_params = ['user' => $gateDetails->username, 'pwd' => $gateDetails->password, 'System' => $gate->system, 'Tcc' => $gate->gate, 'command' => '45', 'iParameter1' => '0', 'iParameter2' => '0', 'iParameter3' => '0', 'iParameter4' => '0', 'textParameter1' => '0', 'textParameter2' => '0', 'guidParameter1' => $guid, 'guidParameter2' => $guid];
                                            $this->log->info("Abacus Web Service : command run success checkout exit facility {$facility->id} params" . json_encode($cmd_params));
                                            $command_response = AbacusWebService::commandStatus($cmd_params, $gateDetails->host);
                                            if ($command_response['success'] == true) {
                                                if ($command_response['data'][0] == "0" || $command_response['data'][0] == "1") {
                                                } else {
                                                    return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                                }
                                            } else {
                                                return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                            }
                                        }
                                    }
                                }



                                $checkinData = Ticket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                                if ($checkinData) {

                                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                    $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                                    if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                        $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                    }
                                    $checkinData['length'] = $diff_in_hours;
                                    $facility = Facility::find($checkinData->facility_id);
                                    if (strtotime(date("Y-m-d H:i:s")) > strtotime($checkinData->estimated_checkout)) {
                                        $this->log->info("Prepaid : user have overstay time with ticket number {$checkinData->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                        return redirect('touchless-parking-atlanta-overstay-pay/' . base64_encode($checkinData->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                    }

                                    $checkinData->is_checkout = '1';
                                    $checkinData->checkout_gate = $decrytArray->gate;
                                    $checkinData->checkout_time = date("Y-m-d H:i:s");
                                    //$checkinData->checkout_datetime = date("Y-m-d H:i:s");
                                    $checkinData->save();

                                    $reservation = Reservation::where('id', $checkinData->reservation_id)->first();
                                    $reservation->is_ticket = '2';
                                    $reservation->save();
                                    if ($checkinData) {
                                        $overstayTicket = OverstayTicket::where('ticket_number', $request->order_number)->get();
                                        if ($overstayTicket) {
                                            foreach ($overstayTicket as $key => $value) {
                                                $value->is_checkout = '1';
                                                $value->checkout_gate = $decrytArray->gate;
                                                $value->save();
                                            }
                                        }
                                        $this->log->info("Prepaid : user prepaid checkout with ticket number {$checkinData->ticket_number} and go to the thankyou page  with browser {$_SERVER['HTTP_USER_AGENT']}");
                                        return Redirect::to('thankyou-prepaid-checkout/' . base64_encode($checkinData->ticket_number));
                                    } else {
                                        return back()->with('danger', 'Please scan valid scan QR code.');
                                    }
                                }
                            }
                        } else {
                            return Redirect::to('error-checkin');
                        }
                    } else {
                        return back()->with('danger', 'Please scan valid scan QR code.');
                    }
                }
            } else {
                return back()->with('danger', 'Please scan valid scan QR code.');
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid scan QR code.');
        }
    }

    public function getPrepaidCheckinPaymentDetailsForOverstay($ticket_number)
    {
        $checkinData = Ticket::with('user')->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->estimated_checkout);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
            $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
        }

        $checkinData['overstay_length'] = $diff_in_hours;
        //$checkinData['overstay_check_in_datetime'] = $arrival_time;
        $facility = Facility::find($checkinData->facility_id);
        $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
        if ($rate) {
            $checkinData['overstay_price'] = $rate['price'];
        }
        $checkinData['rate'] = $rate;
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_hours'] = $startDate->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $this->log->info("Prepaid : user is now overstay payment screen after checkout with ticket number {$checkinData->ticket_number}  with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('checkin-checkout.prepaid-overstay-payment-screen', ['data' => $checkinData]);
    }

    public function makePrepaidOverstayPayment(Request $request)
    {
        $this->setDecryptedCard($request);
        if (strlen($this->request->expiration_date) < 5) {
            return back()->with('danger', "Error! Invalid expiry date.");
        }
        if (strlen($this->request->card_number) < 13) {
            return back()->with('danger', "Error! Invalid card number.");
        }
        if (strlen($this->request->security_code) < 3) {
            return back()->with('danger', "Error! The security code must be between 3 and 4 digits.");
        }

        $this->setCustomTimezone($this->request->facility_id);

        if (preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $this->request->email)) {
        } else {
            return back()->with('danger', "Please enter valid email.");
        }

        $this->user = User::getUserByPhone($this->request->email, $this->request->phone);
        $this->facility = Facility::find($this->request->facility_id);
        if ($this->request->total > 0) {
            try {
                /*$is_partner = 0;
                if($this->user->user_type == 3){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                }elseif($this->user->user_type == 4 || $this->user->user_type == 5){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if($this->partnerPaymentDetails){
                        $is_partner = 1;
                    }else{
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                }else{
                    $is_partner = 0;
                }
                if($is_partner == 1){
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()                
                    ->setFacility($this->facility)
                    ->isPartner($this->partnerPaymentDetails)
                    ->setBillingAddress($this->getBillingArray());
                }else{
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());
                }*/

                $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());

                if (isset($this->paymentProfileId)) { // Use our logged in users profile
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    /*$this->authNet->isPartner($this->partnerPaymentDetails)
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);*/
                    $this->authNet
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            } catch (Exception $e) {
                $request->session()->flash(
                    'alert-danger',
                    'Payment error!'
                );
                return back()->withError(['danger' => $e]);
            }
        }

        $ticket = $this->saveOverstayTicket();
        if ($this->request->total > 0) {
            $reservationMode = "Prepaid Overstay Parking";
            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else {
                $ticketech_guid = rand(100000, 999999);
            }

            try {

                // Use our database rate price to create the transaction
                try {
                    /*$charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();*/
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->executeTransaction();
                    // Fire off transaction - this method will throw an exception if errors occur
                } catch (AuthorizeNetException $e) {
                    $ticket->delete();
                    return back()->with('danger', $e->getMessage());
                }
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                $ticket->delete();
                return back()->with('danger', $e->getMessage());
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->save();
            $this->log->info("Prepaid : user {$this->user->email} have paid overstay payment and redirect to checkout page with ticket number {$ticket->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {
                Artisan::queue('email:confirm-checkin', array('id' => $ticket->id, 'type' => 'overstay'));

                if (Session::get('is_sms_direct_checkout') == '1') {
                    $ticket->is_checkout = '1';
                    //$ticket->checkout_gate = $decrytArray->gate;
                    $ticket->checkout_datetime = date("Y-m-d H:i:s");
                    $ticket->save();

                    $mainTicket = Ticket::where('ticket_number', $ticket->ticket_number)->first();
                    $mainTicket->is_checkout = '1';
                    $mainTicket->checkout_gate = $ticket->checkout_gate;
                    $mainTicket->checkout_time = date("Y-m-d H:i:s");
                    $mainTicket->save();

                    $reservation = Reservation::where('id', $mainTicket->reservation_id)->first();
                    $reservation->is_ticket = '2';
                    $reservation->save();

                    Session::forget('is_sms_direct_checkout');
                    return redirect('thankyou-prepaid-checkout/' . base64_encode($mainTicket->ticket_number));
                }
                return redirect('touchless-parking-atlanta-prepaid-checkout-screen/' . base64_encode($ticket->ticket_number))->with('success', 'Success! Overstay payment successfully done.');
            }
        } else {
            return back()->with('danger', "Please use valid ticket number with amount.");
        }
    }

    public function updateRateInformationWithAvailibilty($arrival_time, $length_of_stay, Facility $facility)
    {
        $returnResultArr = array();

        $returnResultArr['coupon_threshold_price'] = 0;
        $returnResultArr['is_coupon_threshold_price_percentage'] = 0;
        $returnResultArr['availability'] = 0;
        $returnResultArr['is_coupon_threshold_applied'] = 0;

        $inventory = new Inventory();
        $date_time_out = Carbon::parse($arrival_time)->addMinutes((number_format($length_of_stay, 2) * 60));

        $realtimeWindow = $facility->realtime_window;
        $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;

        $timeDifference = date_diff(date_create($arrival_time), Carbon::now());

        $isAvailable = true;

        $thresholdAvailability = self::DEFAULT_VALUE;

        if ($isAvailable == true) {
            //check how many slots does entry and exit time occupies
            //$difference = date_diff(date_create($arrival_time), date_create($date_time_out));
            $difference = date_diff(date_create(date('Y-m-d', strtotime($arrival_time))), date_create(date('Y-m-d', strtotime(($date_time_out)))));

            if ($difference->d > 0) {
                //                $dates   = $inventory->generateArrayOfDates(
                //                '', date($arrival_time), date($date_time_out));

                $dates   = $inventory->generateArrayOfDates(
                    ($difference->d + self::ADD_EXTRA_DAY_COUNT),
                    date('Y-m-d H:i:s', strtotime($arrival_time))
                );

                $dayDifference = $difference->d;

                foreach ($dates as $key => $date) {
                    $facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();

                    if ($facilityAvailability) {
                        $inventory = json_decode($facilityAvailability->availability);

                        if ($key == 0) {
                            /**
                             * because this is the first day in the dates provided
                             * we should check from each time_slot starting
                             * from the hour provided in the api call
                             */
                            $i = date('G', strtotime($arrival_time));
                            while ($i <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$i})) {
                                    if ($inventory->{$i} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$i}) {
                                            $thresholdAvailability = $inventory->{$i};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$i};
                                    }
                                }
                                $i++;
                            }
                        } elseif ($key == $dayDifference) {
                            $i = date('G', strtotime($date_time_out));
                            $minutes = date('i', strtotime($date_time_out));
                            if ($minutes >= 30) {
                                $i++;
                            }
                            /**
                             * because this is the last day in the dates provided
                             * we should check from each time_slot starting
                             * till the hour provided in the api call
                             */
                            $j = 0;
                            while ($j < $i) {
                                if (isset($inventory->{$j})) {
                                    if ($inventory->{$j} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$j}) {
                                            $thresholdAvailability = $inventory->{$j};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$j};
                                    }
                                }
                                $j++;
                            }
                        } else {
                            /**
                             * because this could be any day except first and last in
                             * the dates provided we should check from whole day
                             */
                            $k = 0;
                            while ($k <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$k})) {
                                    if ($inventory->{$k} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$k}) {
                                            $thresholdAvailability = $inventory->{$k};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$k};
                                    }
                                }
                                $k++;
                            }
                        }
                    }
                }
            } else {
                $startingHour = date('G', strtotime($arrival_time));
                $endingHour   = date('G', strtotime($date_time_out));
                $facilityAvailability     = FacilityAvailability::where(
                    ['facility_id' => $facility->id, 'date' => date('Y-m-d', strtotime($arrival_time))]
                )->first();

                if ($facilityAvailability) {
                    $availability = json_decode($facilityAvailability->availability, true);

                    while ($startingHour <= $endingHour) {
                        if (isset($availability[$startingHour])) {
                            if (($availability[$startingHour] < 1)) {
                                $isAvailable = false;
                            }
                            if ($thresholdAvailability > 0) {
                                if ($thresholdAvailability > $availability[$startingHour]) {
                                    $thresholdAvailability = $availability[$startingHour];
                                }
                            } else {
                                $thresholdAvailability = $availability[$startingHour];
                            }
                        }
                        $startingHour++;
                    }
                }
            }

            if ($thresholdAvailability < self::DEFAULT_VALUE) {
                $thresholdAvailability = self::DEFAULT_VALUE;
            }

            if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {

                $dateIn = date('Y-m-d', strtotime($arrival_time));
                $facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();

                if ($facilityAvailability) {
                    //$availabilities = json_decode($facilityAvailability->availability, true);

                    if ($thresholdAvailability >= 0) {

                        $couponThresholdsNew = $facility->facilityCouponThreshold;
                        $couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');

                        $thresholds       = array();
                        if ($couponThresholdsNew) {
                            $thresholdCounter = self::DEFAULT_VALUE;
                            foreach ($couponThresholdsNew as $couponThreshold) {

                                if ($couponThreshold->uptick_type !== 'deleted') {
                                    $thresholds[$thresholdCounter] =
                                        ['threshold' => $couponThreshold->threshold, 'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
                                    $thresholdCounter++;
                                }
                            }
                        }
                        $thresholdPrice = 0;
                        $currentAvailability = $thresholdAvailability;
                        foreach ($thresholds as $key => $threshold) {
                            if ($thresholdAvailability <= $threshold['threshold']) {
                                if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
                                    if ($threshold['uptick_type'] == 'price') {
                                        $thresholdPrice = $threshold['uptick'];

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    } else if ($threshold['uptick_type'] == 'percentage') {
                                        $thresholdPrice =  $threshold['uptick'];
                                        $returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG;

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    }
                                }
                            }
                        }
                        $returnResultArr['coupon_threshold_price'] = $thresholdPrice;

                        $returnResultArr['availability'] = $currentAvailability;
                    }
                }
            }
        }

        return $returnResultArr;
    }

    public function getTouchlessParkingUser($key)
    {

        $decrypt = json_decode(base64_decode($key));
        if ($decrypt) {
            if ($this->checkFacilityAvailable($decrypt->facility_id) == false) {
                $this->log->info("{$decrypt->facility_id} not availabe right now.");
                Redirect::to('error-facility');
            }
        } else {
            return Redirect::to('error-checkin');
        }

        $this->setCustomTimezone($decrypt->facility_id);

        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = '0';
        $arrEvents['event_name'] = "Autogate Touchless Checkin By Email/Phone";
        $arrEvents['facility_id'] = isset($decrypt->facility_id) ? $decrypt->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Get Email/Phone Screen for checkin Function getTouchlessParkingUser Request ";
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);

        $facility = Facility::where('id', $decrypt->facility_id)->first();
        if (!$facility) {
            Redirect::to('error-facility');
        }
        $updatedEncryptedKey = '';
        //$decrytArray['facility_id'] = $request->facility_id;
        //$availFacility = $this->checkFacilityAvailable($decrytArray->facility_id);
        //if(!$availFacility){
        //return back()->with('danger', "Garage is not available.");
        //}
        $updatedFacilityArray = ["facility_id" => $facility->id, "gate" => $decrypt->gate];
        $updatedEncryptedKey = base64_encode(json_encode($updatedFacilityArray));
        //$facility = Facility::where('owner_id', $facility->owner_id)->first();

        return view('diamond-checkin-checkout.touchless-parking-user-details', ['data' => $updatedEncryptedKey, 'facility' => $facility]);
    }

    public function getTouchlessParkingDemoUser($key)
    {

        $decrypt = json_decode(base64_decode($key));
        if ($decrypt) {
            if ($this->checkFacilityAvailable($decrypt->facility_id) == false) {
                $this->log->info("{$decrypt->facility_id} not availabe right now.");
                Redirect::to('error-facility');
            }
        } else {
            return Redirect::to('error-checkin');
        }
        return view('checkin-checkout.touchless-parking-demo-user-details', ['data' => $key]);
    }

    public function confirmTouchlessCheckin(Request $request)
    {

        try {

            if (isset($request->encrypt)) {
                $request->phone = preg_replace('/\D+/', '', $request->phone);
                if (preg_match('/^[0-9]{10}+$/', $request->phone)) {
                    if ($request->phone == "0000000000") {
                        return back()->with('danger', "Please enter valid phone number.")->withInput();
                    }
                } else {
                    return back()->with('danger', "Please enter valid phone number.")->withInput();
                }
                if (preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $request->email)) {
                } else {
                    return back()->with('danger', "Please enter valid email.")->withInput();
                }
                if (!isset($request->is_autopay)) {
                    return back()->with('danger', "Please check autopay consent.")->withInput();
                }
                if ($request->no_of_days == '') {
                    return back()->with('danger', "Please select no of days.")->withInput();
                }
                $explode = $request->encrypt;
                $decrypt = base64_decode($explode);
                $updatedEncryptedKey = '';
                if ($decrypt) {
                    $decrytArray = json_decode($decrypt);
                    //$decrytArray['facility_id'] = $request->facility_id;
                    //$availFacility = $this->checkFacilityAvailable($decrytArray->facility_id);
                    //if(!$availFacility){
                    //return back()->with('danger', "Garage is not available.");
                    //}
                    $updatedFacilityArray = ["facility_id" => $request->facility_id, "gate" => $decrytArray->gate];
                    $updatedEncryptedKey = base64_encode(json_encode($updatedFacilityArray));
                    $this->setCustomTimezone($request->facility_id);

                    //log all request in event table
                    $requestValuesLogs = $request->all();
                    $arrEvents = array();
                    $arrEvents['user_id'] = '0';
                    $arrEvents['event_name'] = "Diamond Touchless Direct Confirm Checkin Drive Up/Prepaid";
                    $arrEvents['facility_id'] = isset($request->facility_id) ? $request->facility_id : '';
                    $arrEvents['os_version'] = "-1";
                    $arrEvents['error_message'] = "Diamond Confirm touchless checkin by email/phone Function confirmTouchlessCheckin Request " .  json_encode($requestValuesLogs);
                    $arrEvents['device'] = "Web";
                    $arrEvents['email_id'] = '';

                    $this->saveEventsLogs($arrEvents);

                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $request->facility_id)->first();
                    $checkinData = [];
                    if ($gate && isset($gate->gate_type)) {
                        if ($gate->gate_type == 'entry') {
                            $checkFacilityOwner = Facility::where('id', $request->facility_id)->first();


                            $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
                            if ($geoLocation['geoplugin_countryCode'] == 'IN') {
                                $countryCode = "+91";
                            } elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
                                $countryCode = "+1";
                            } else {
                                $countryCode = "+1";
                            }
                            $user = User::where('phone', $countryCode . $request->phone)->where('created_by', $checkFacilityOwner->owner_id)->first();
                            if (!$user) {
                                $user =  User::create(
                                    [
                                        'name' => '',
                                        'email' => $request->email,
                                        'phone' => $countryCode . $request->phone,
                                        'password' => Hash::make(str_random(60)),
                                        'anon' => false,
                                        'user_type' => '5',
                                        'created_by' => $checkFacilityOwner->owner_id
                                    ]
                                );
                            }
                            //check already checkin or not
                            /*$isAlreadyCheckin = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->get();
                        if(count($isAlreadyCheckin) > 0){
                            return back()->with('danger', "You have already Checked-In, Please Check-Out the existing parking first through Check-Out link shared on your registered email.");
                        }*/

                            $checkinExist = Ticket::/*where('user_id', $user->id)->*/whereDate('check_in_datetime', '>=', date("Y-m-d"))->whereDate('checkout_datetime', '<=', date("Y-m-d"))->where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->first();
                            if ($checkinExist) {
                                return back()->with('danger', "You have already Checked-In, Your planned checkout date is " . date("m/d/Y", strtotime($checkinExist->checkout_datetime)) . " " . date('g:i A', strtotime($checkinExist->checkout_datetime)))->withInput();
                            }

                            $citationExist = TicketCitation::/*whereDate('created_at', '=', date("Y-m-d"))->*/where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->where('is_closed', '0')->orderBy("id", "DESC")->first();
                            if ($citationExist) {
                                if (date("Y-m-d", strtotime($citationExist->created_at)) < date("Y-m-d")) {
                                    return back()->with('danger', "Citation already issued against this license plate. Please contact to attendant.")->withInput();
                                }
                            }

                            $user->email = $request->email;
                            $user->save();
                            if ($user->created_by == '') {
                                $facility = Facility::where('id', $decrytArray->facility_id)->first();
                                if ($user->user_type == '5') {
                                    $user->created_by = $facility->owner_id;
                                    $user->save();
                                }
                                $data['partner_id'] = $facility->owner_id;
                            } else {
                                $data['partner_id'] = $user->created_by;
                            }
                            if ($checkFacilityOwner->facility_booking_type == '1' || $checkFacilityOwner->facility_booking_type == '2') {
                                Session::put('email', $user->email);
                                Session::put('phone', $user->phone);
                                Session::put('no_of_days', $request->no_of_days);
                                Session::put('license_plate', $request->license_plate);
                                Session::put('car_model', $request->car_model);
                                //Session::put('gate', $decrytArray->gate);
                                Session::put('facility_id', $request->facility_id);
                                Session::put('is_autopay', isset($request->is_autopay) ? "1" : "");
                                //Session::put('facility_id', $request->facility_id);
                                Session::put('driving_license', $request->driving_license);
                                $encrypt = $updatedEncryptedKey == '' ? $request->encrypt : $updatedEncryptedKey;
                                return Redirect::to('diamond-payment/' . $encrypt);
                            } else {
                                return back()->with('danger', "Sorry, Something wrong with parking.");
                            }
                        } else {
                            return back()->with('danger', "Please scan the right QR code.");
                        }
                    } else {
                        return back()->with('danger', "Sorry, Something wrong with parking.");
                    }
                }
            } else {
                return back()->with('danger', 'Please scan valid scan QR code.');
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid scan QR code.');
        }
    }


    protected function checkTicketNumber()
    {
        $ticket = 'PE' . rand(100, 999) . rand(100, 999);
        $isExist = Ticket::where('ticket_number', $ticket)->first();
        if ($isExist) {
            $this->checkTicketNumber();
        }
        return $ticket;
    }


    public function getTouchlessParkingCheckinDetails($ticket_number)
    {

        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        if ($checkinData->anet_transaction_id != '') {
            return Redirect::to('touchless-parking-atlanta-payment-success/' . base64_encode($checkinData->ticket_number));
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $memberUser = User::with('memberUser')->where('id', $checkinData->user_id)->first();

        if ($memberUser->is_member == '1') {
            $checkinData['member_id'] = $memberUser->memberUser->member_id;
        } else {
            $checkinData['member_id'] = '';
        }

        /*if($checkinData && $checkinData->is_checkin == '1'){
            return Redirect::to('prepaid-checkout-details/'.base64_encode($checkinData->ticket_number));
        }
        if($checkinData && $checkinData->is_checkout == '1'){
            return Redirect::to('thankyou-checkout/'.base64_encode($checkinData->ticket_number));
        }*/

        return view('atlanta-checkin-checkout.touchless-parking-checkin-details', ['data' => $checkinData]);
    }


    public function getTouchlessParkingCheckoutDetails($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        if ($checkinData->anet_transaction_id != '') {
            return Redirect::to('touchless-parking-atlanta-payment-success/' . $ticket_number);
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
            $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
        }

        $facility = Facility::find($checkinData->facility_id);
        /** this function is used to get Availability Information for respective facility **/
        $rateData = $this->updateRateInformationWithAvailibilty($arrival_time, $diff_in_hours, $facility);

        $memberUser = User::with('memberUser')->where('id', $checkinData->user_id)->first();

        if ($memberUser->is_member == '1') {
            $isMember = 1;
            $checkinData['member_id'] = $memberUser->memberUser->member_id;
        } else {
            $isMember = 0;
            $checkinData['member_id'] = '';
        }
        $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE, $isMember);
        if ($rate == false) {
            return Redirect::to('error-facility');
        }
        $checkinData['length'] = 24;
        //return current availabilit
        if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
            $rate['availability'] = $rateData['availability'];
        } else {
            $rate['availability'] = self::DEFAULT_VALUE;
        }
        if ($rate['price'] == 'N/A') {
            return Redirect::to('error-facility');
        }
        //returning  message as per availibility 
        $rate['availability_msg'] = '';
        $rate['availability_msg_some_space'] = '';
        if ((float)$rate['price'] >  self::DEFAULT_VALUE) {
            if ($rate['availability'] == self::DEFAULT_VALUE) {
                return Redirect::to('error-facility');
            }/*else if($rate['availability'] < self::MIN_AVAILABILITY)
        {
            return Redirect::to('error-facility');
        }*/ else {
                //return Redirect::to('error-facility');
            }
        } else if ($rate['price'] == 'N/A') {
            return Redirect::to('error-facility');
        } else if ($rate['availability'] == self::DEFAULT_VALUE) {
            return Redirect::to('error-facility');
        }
        $checkinData['rate'] = $rate;
        //get member pass details

        $pass = UserPass::where('user_id', $memberUser->id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
        if ($pass) {
            if ($pass->remaining_days == 0) {
                $checkinData['pass'] = [];
            } else {
                $checkinData['pass'] = $pass;
            }
        } else {
            $checkinData['pass'] = [];
        }


        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $this->log->info("Touhcless : user is on payment screen on after checkout click with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");

        return view('atlanta-checkin-checkout.touchless-parking-checkout-details', ['data' => $checkinData]);
    }

    public function makeTouchlessParkingPayment(Request $request)
    {

        if ($this->request->pass_id == '') {
            $this->setDecryptedCard($request);
            if (strlen($this->request->expiration_date) < 5) {
                return back()->with('danger', "Error! Invalid expiry date.");
            }
            if (strlen($this->request->card_number) < 13) {
                return back()->with('danger', "Error! Invalid card number.");
            }
            if (strlen($this->request->security_code) < 3) {
                return back()->with('danger', "Error! The security code must be between 3 and 4 digits.");
            }
        }

        $this->setCustomTimezone($this->request->facility_id);

        $this->checkFacilityAvailable($this->request->facility_id);

        if (preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $this->request->email)) {
        } else {
            return back()->with('danger', "Please enter valid email.");
        }

        //$this->validate($this->request, $this->billingValidation);
        $this->facility = Facility::find($this->request->facility_id);
        $this->user = User::where('phone', $this->request->phone)->where('created_by', $this->facility->owner_id)->first();
        if ($this->request->name_on_card != '') {
            $this->user->name = $this->request->name_on_card;
        }
        $this->user->email = $this->request->email;
        $this->user->save();


        if ($this->request->total > 0) {
            try {

                $is_partner = 0;
                if ($this->user->user_type == 3) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if ($this->partnerPaymentDetails) {
                        $is_partner = 1;
                    } else {
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                } else {
                    $is_partner = 0;
                }
                if ($is_partner == 1) {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->isPartner($this->partnerPaymentDetails)
                        ->setBillingAddress($this->getBillingArray());
                } else {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->setBillingAddress($this->getBillingArray());
                }

                if (isset($this->paymentProfileId)) { // Use our logged in users profile
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    $this->authNet->isPartner($this->partnerPaymentDetails)
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            } catch (Exception $e) {
                $this->logPayment->error($e);
                $request->session()->flash(
                    'alert-danger',
                    'Payment error!'
                );
                return back()->withError(['errMsg' => $e]);
            }
        }

        $ticket = $this->saveTicket();
        if ($this->request->total > 0) {
            $reservationMode = "Touchless Parking";
            try {

                // Use our database rate price to create the transaction
                try {
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();
                    // Fire off transaction - this method will throw an exception if errors occur
                } catch (AuthorizeNetException $e) {
                    $this->logPayment->error($e->getMessage());
                    return back()->with('danger', $e->getMessage());
                }
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                /*dd($e);
                return back()->withError(['errMsg'=>$e]);
                dd(111);
                $transaction = $this->authNet->getTransaction();
                $ticket->anet_transaction_id = $transaction->id;
                $ticket->save();       
                $ticket->delete();
                return back()->withError(['errMsg'=>$e]);*/
                $this->logPayment->error($e);
                return back()->with('danger', $e->getMessage());
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->save();
            $this->log->info("user done payment ticket id {$ticket->id} ticket number {$ticket->ticket_number} and go to the checkout scan screen with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {
                Artisan::queue('email:touchless-parking-atlanta-checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));

                if (Session::get('is_direct_checkout') == '1') {
                    $ticket->is_checkout = '1';
                    $ticket->checkout_datetime = date("Y-m-d H:i:s");
                    $ticket->save();

                    if ($ticket->reservation_id != '') {
                        $reservation = Reservation::where('id', $ticket->reservation_id)->first();

                        $reservation->is_ticket = '2';
                        $reservation->save();
                    }
                    Session::forget('is_direct_checkout');
                    Session::forget('email');
                    Session::forget('phone');
                    return redirect('touchless-parking-atlanta-thankyou-checkout/' . base64_encode($ticket->ticket_number));
                }

                return redirect('touchless-parking-atlanta-payment-success/' . base64_encode($ticket->ticket_number))->with('success', 'Success! Payment Successfully done.');
            }
        } else {

            if ($this->request->pass_id == '') {
                return back()->with('danger', "Please use valid ticket number with amount.");
            } else {

                $userPass = UserPass::where('id', $this->request->pass_id)->first();
                if (!$userPass) {
                    return back()->with('danger', "Sorry! Invalid Pass.");
                }
                // Charge successful, save transaction relationship to it
                $authorized_anet_transaction = new AuthorizeNetTransaction();

                $authorized_anet_transaction->sent = $this->sendAnet;
                $authorized_anet_transaction->anonymous = $this->anonymousAnet;
                $authorized_anet_transaction->user_id = $this->user->id;
                $authorized_anet_transaction->ip_address = \Request::ip();
                $authorized_anet_transaction->total = 0;
                $authorized_anet_transaction->name = $this->getBillingName();

                $reservationMode = "Touchless Parking Pass Ticket Payment";
                $authorized_anet_transaction->description = "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}";

                $authorized_anet_transaction->response_message = "Zero amount transaction";
                $authorized_anet_transaction->save();

                $charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
                $ticket->anet_transaction_id = $authorized_anet_transaction->id;
                $ticket->user_pass_id = $userPass->id;
                $ticket->save();
                $userPass->consume_days = $userPass->consume_days + 1;
                $userPass->remaining_days = $userPass->remaining_days - 1;
                $userPass->save();

                $this->log->info("user done payment by pass ticket id {$ticket->id} ticket number {$ticket->ticket_number} and go to the checkout scan screen with browser {$_SERVER['HTTP_USER_AGENT']}");
                if ($ticket) {
                    Artisan::queue('email:touchless-parking-atlanta-checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));

                    if (Session::get('is_direct_checkout') == '1') {
                        $ticket->is_checkout = '1';
                        $ticket->checkout_datetime = date("Y-m-d H:i:s");
                        $ticket->save();

                        if ($ticket->reservation_id != '') {
                            $reservation = Reservation::where('id', $ticket->reservation_id)->first();

                            $reservation->is_ticket = '2';
                            $reservation->save();
                        }
                        Session::forget('is_direct_checkout');
                        Session::forget('email');
                        Session::forget('phone');
                        return redirect('touchless-parking-atlanta-thankyou-checkout/' . base64_encode($ticket->ticket_number));
                    }

                    return redirect('touchless-parking-atlanta-payment-success/' . base64_encode($ticket->ticket_number))->with('success', 'Success! Payment Successfully done.');
                }
            }
        }
    }

    protected function getBillingName()
    {
        $name = $this->request->name_on_card ?: $this->user->name;

        $nameArray = explode(' ', trim($name));

        return (reset($nameArray) . " " . end($nameArray));
    }

    public function getTouchlessParkingPaymentThankyou($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != '') {
            return Redirect::to('touchless-parking-atlanta-checkin-details/' . $ticket_number);
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('atlanta-checkin-checkout.touchless-parking-payment-success', ['data' => $checkinData]);
    }


    public function getTouchlessParkingCheckout($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != '') {
            return Redirect::to('touchless-parking-atlanta-checkin-details/' . $ticket_number);
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($checkinData->user_id) ? $checkinData->user_id : '0';
        $arrEvents['event_name'] = "Touchless Drive Up Checkout Scan Screen After Overstay Payment";
        $arrEvents['facility_id'] = isset($checkinData->facility_id) ? $checkinData->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Touchless Drive Up Checkout Scan Screen After Overstay Payment Function getTouchlessParkingCheckout Request " . $checkinData->ticket_number;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);


        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('atlanta-checkin-checkout.touchless-parking-checkout', ['data' => $checkinData]);
    }


    public function confirmTouchlessParkingCheckout(Request $request)
    {
        //$request = $request->all();
        try {
            if (isset($request->encrypt)) {

                $explode = explode('/', $request->encrypt);
                if (count($explode) < 5) {
                    return back()->with('danger', "Please scan the right QR code.");
                }
                $decrypt = base64_decode($explode[4]);
                if ($decrypt) {
                    $decrytArray = json_decode($decrypt);
                    if ($decrytArray) {

                        $this->setCustomTimezone($decrytArray->facility_id);

                        $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                        $checkinData = [];
                        if ($gate && isset($gate->gate_type)) {
                            if ($gate->gate_type == 'exit') {

                                //check third party gate API
                                $facility = Facility::where('id', $decrytArray->facility_id)->first();

                                $is_checkout = Ticket::where('ticket_number', $request->order_number)->first();
                                if (!$is_checkout) {
                                    //return Redirect::to('already-checkout');
                                    return back()->with('danger', 'Please scan valid scan QR code.');
                                }
                                if ($is_checkout->is_checkout == '1') {
                                    return Redirect::to('already-checkout');
                                }
                                //log all request in event table

                                $arrEvents = array();
                                $arrEvents['user_id'] = isset($is_checkout->user_id) ? $is_checkout->user_id : '0';
                                $arrEvents['event_name'] = "Touchless Drive Up Checkout Scan Confirm";
                                $arrEvents['facility_id'] = isset($is_checkout->facility_id) ? $is_checkout->facility_id : '';
                                $arrEvents['os_version'] = "-1";
                                $arrEvents['error_message'] = "Checkout scan confirm Function confirmTouchlessParkingCheckout Request " . $is_checkout->ticket_number;
                                $arrEvents['device'] = "Web";
                                $arrEvents['email_id'] = '';

                                $this->saveEventsLogs($arrEvents);

                                $checkinData = Ticket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                                if ($checkinData) {

                                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                    $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                                    if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                        $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                    }
                                    $checkinData['length'] = $diff_in_hours;
                                    $facility = Facility::find($checkinData->facility_id);
                                    if (strtotime(date("Y-m-d H:i:s")) > strtotime(date("Y-m-d H:i:s", strtotime($checkinData->estimated_checkout)))) {
                                        $this->log->info("Touchless : user have overstay time with ticket number {$checkinData->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                        return redirect('touchless-parking-atlanta-overstay-pay/' . base64_encode($checkinData->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                    }


                                    $gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                                    /*if($gateDetails){
                                    if($gateDetails->host !=''){
                                        $params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate];
                                        $this->log->info("Abacus Web Service : vehicle command aboout to send checkout exit facility {$facility->id} params ".json_encode($params));
                                        $response = AbacusWebService::isvehicleAvailable($params, $gateDetails->host);
                                        if($response['success'] == false){
                                            return back()->with('danger', "Something wrong in parking.");
                                        }
                                        $this->log->info("Abacus Web Service : vehicle command complete checkout exit facility {$facility->id} response". json_encode($response));
                                        if($response['data']['IsLoopVon'] == '1' || $response['data']['IsLoopVon'] == '0'){
                                            $guid = AbacusWebService::createGuid();
                                            $cmd_params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate,'command'=>'45','iParameter1'=>'0','iParameter2'=>'0','iParameter3'=>'0','iParameter4'=>'0','textParameter1'=>'0','textParameter2'=>'0', 'guidParameter1'=>$guid, 'guidParameter2'=>$guid];
                                            $this->log->info("Abacus Web Service : command run success checkout exit facility {$facility->id} params". json_encode($cmd_params));
                                        $command_response = AbacusWebService::commandStatus($cmd_params, $gateDetails->host);
                                        $this->log->info("Abacus Web Service Command 2 Response: checkin entry response". json_encode($command_response));
                                        if($command_response['success'] == true){
                                            if($command_response['data'][0] == "0" || $command_response['data'][0] == "1"){
                                    
                                            }else{
                                                return back()->with('danger', "Something wrong in parking.");
                                            }
                                            
                                        }else{
                                            return back()->with('danger', "Something wrong in parking.");
                                        }
                                        }else{
                                            return back()->with('danger', "Sorry! Vehicle not present.");
                                        }

                                    }
                                }*/

                                    $checkinData->is_checkout = '1';
                                    $checkinData->checkout_gate = $decrytArray->gate;
                                    //$checkinData->checkout_datetime = date("Y-m-d H:i:s");
                                    $checkinData->checkout_time = date("Y-m-d H:i:s");
                                    $checkinData->save();
                                    if ($checkinData) {
                                        $overstayTicket = OverstayTicket::where('ticket_number', $request->order_number)->get();
                                        if ($overstayTicket) {
                                            foreach ($overstayTicket as $key => $value) {
                                                $value->is_checkout = '1';
                                                $value->checkout_gate = $decrytArray->gate;
                                                $value->save();
                                            }
                                        }
                                        $this->log->info("Touchless : user prepaid checkout with ticket number {$checkinData->ticket_number} and go to the thankyou page  with browser {$_SERVER['HTTP_USER_AGENT']}");
                                        return Redirect::to('touchless-parking-atlanta-thankyou-checkout/' . base64_encode($checkinData->ticket_number));
                                    } else {
                                        return back()->with('danger', 'Please scan valid scan QR code.');
                                    }
                                }
                            } else {
                                return back()->with('danger', 'Please scan valid QR code.');
                            }
                        } else {
                            return Redirect::to('error-checkin');
                        }
                    } else {
                        return back()->with('danger', 'Please scan valid QR code.');
                    }
                }
            } else {
                return back()->with('danger', 'Please scan valid QR code.');
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid QR code.');
        }
    }


    public function thankyouTouchlessParkingCheckout($ticket_number)
    {
        $checkinData = Ticket::with(['user', 'facility'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '1')->first();

        $this->setCustomTimezone($checkinData->facility_id);

        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($checkinData->user_id) ? $checkinData->user_id : '0';
        $arrEvents['event_name'] = "Touchless Thank you checkout screen";
        $arrEvents['facility_id'] = isset($checkinData->facility_id) ? $checkinData->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Thank you checkout screen Function thankyouTouchlessParkingCheckout Request " . $checkinData->ticket_number;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $checkinData['is_overstay'] = '0';

        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->orderBy('id', 'DESC')->first();
        if ($overstay) {
            $checkinData['check_in_datetime'] = $overstay->check_in_datetime;
            $checkinData['checkout_datetime'] = $overstay->checkout_datetime;
            $checkinData['length'] = $overstay->length;
            $checkinData['is_overstay'] = '1';
            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['check_in_datetime']);
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['checkout_datetime']);
            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        }
        $this->log->info("Prepaid : user successfully checkout and thankyou page showing with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('atlanta-checkin-checkout.touchless-parking-thankyou-checkout', ['data' => $checkinData]);
    }

    public function getTouchlessParkingDetailsForOverstay($ticket_number)
    {
        $checkinData = Ticket::with('user')->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();
        /*$checkinData = Ticket::with('user')->where('ticket_number', base64_decode($ticket_number))->first();*/
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($checkinData->user_id) ? $checkinData->user_id : '0';
        $arrEvents['event_name'] = "Touchless Drive Up Overstay Payment Screen";
        $arrEvents['facility_id'] = isset($checkinData->facility_id) ? $checkinData->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Get Overstay Checkin Payment Details Function getTouchlessParkingDetailsForOverstay Request " . $checkinData->ticket_number;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->estimated_checkout);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
            $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
        }
        $checkinData['overstay_length'] = $diff_in_hours;
        //$checkinData['overstay_check_in_datetime'] = $arrival_time;
        $facility = Facility::find($checkinData->facility_id);
        $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
        if ($rate) {
            $checkinData['overstay_price'] = $rate['price'];
        }

        $checkinData['rate'] = $rate;
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $this->log->info("Touchless user is now overstay payment screen after checkout with ticket number {$checkinData->ticket_number}  with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('atlanta-checkin-checkout.touchless-parking-overstay-payment', ['data' => $checkinData]);
    }

    public function makeTouchlessParkingOverstayPayment(Request $request)
    {
        $this->setDecryptedCard($request);
        if (strlen($this->request->expiration_date) < 5) {
            return back()->with('danger', "Error! Invalid expiry date.");
        }
        if (strlen($this->request->card_number) < 13) {
            return back()->with('danger', "Error! Invalid card number.");
        }
        if (strlen($this->request->security_code) < 3) {
            return back()->with('danger', "Error! The security code must be between 3 and 4 digits.");
        }

        $this->setCustomTimezone($this->request->facility_id);

        if (preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $this->request->email)) {
        } else {
            return back()->with('danger', "Please enter valid email.");
        }

        //$this->validate($this->request, $this->billingValidation);
        $this->facility = Facility::find($this->request->facility_id);
        $this->user = User::where('phone', $this->request->phone)->where('created_by', $this->facility->owner_id)->first();
        if ($this->user) {
            $this->user->name = $this->request->name_on_card;
            $this->user->email = $this->request->email;
            $this->user->save();
        } else {
            return back()->with('danger', "Invalid User.");
        }

        //log all request in event table
        $requestValuesLogs = $request->all();
        unset($requestValuesLogs['nonce']);
        unset($requestValuesLogs['name_on_card']);
        unset($requestValuesLogs['card_number']);
        unset($requestValuesLogs['expiration_date']);
        unset($requestValuesLogs['security_code']);
        $this->log->info(" User Touchless Overstay Payment request --" . json_encode($requestValuesLogs));

        $this->logPayment->info("Make Overstay Payment Request" . json_encode($request));
        //saving to event table after check in payment functions

        $arrEvents = array();

        $arrEvents['user_id'] = isset($this->user->id) ? $this->user->id : '0';
        $arrEvents['event_name'] = "Make Overstay Payment After drive up check in";
        $arrEvents['facility_id'] = isset($this->request->facility_id) ? $this->request->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Make Overstay Payment Function makeTouchlessParkingOverstayPayment Request " . json_encode($requestValuesLogs);
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = isset($this->user->email) ? $this->user->email : '';

        $this->saveEventsLogs($arrEvents);

        if ($this->request->total > 0) {
            try {
                $is_partner = 0;
                if ($this->user->user_type == 3) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if ($this->partnerPaymentDetails) {
                        $is_partner = 1;
                    } else {
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                } else {
                    $is_partner = 0;
                }
                if ($is_partner == 1) {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->isPartner($this->partnerPaymentDetails)
                        ->setBillingAddress($this->getBillingArray());
                } else {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->setBillingAddress($this->getBillingArray());
                }

                if (isset($this->paymentProfileId)) { // Use our logged in users profile
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    if ($is_partner == 1) {
                        $this->authNet->isPartner($this->partnerPaymentDetails)
                            ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                    } else {
                        $this->authNet->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                    }
                }
            } catch (Exception $e) {
                $request->session()->flash(
                    'alert-danger',
                    'Payment error!'
                );
                return back()->withError(['danger' => $e]);
            }
        }

        $ticket = $this->saveOverstayTicket();
        if ($this->request->total > 0) {
            $reservationMode = "Zoo Atlanta Touchless Parking Overstay Parking";
            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else {
                $ticketech_guid = rand(100000, 999999);
            }

            try {

                // Use our database rate price to create the transaction
                try {
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();

                    // Fire off transaction - this method will throw an exception if errors occur

                } catch (AuthorizeNetException $e) {
                    $ticket->delete();
                    return back()->with('danger', $e->getMessage());
                }
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                $ticket->delete();
                return back()->with('danger', $e->getMessage());
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->save();
            $this->log->info("Touchless parking user have paid overstay payment and redirect to checkout page with ticket number {$ticket->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {
                Artisan::queue('email:touchless-parking-atlanta-checkin-payment', array('id' => $ticket->id, 'type' => 'overstay'));

                if (Session::get('is_direct_checkout') == '1') {
                    $ticket->is_checkout = '1';
                    //$ticket->checkout_gate = $decrytArray->gate;
                    $ticket->checkout_datetime = date("Y-m-d H:i:s");
                    $ticket->save();

                    $mainTicket = Ticket::where('ticket_number', $ticket->ticket_number)->first();
                    $mainTicket->is_checkout = '1';
                    $mainTicket->checkout_gate = $ticket->checkout_gate;
                    $mainTicket->checkout_datetime = date("Y-m-d H:i:s");
                    $mainTicket->checkin_time = date("Y-m-d H:i:s");
                    $mainTicket->checkout_time = date("Y-m-d H:i:s");
                    $mainTicket->save();

                    Session::forget('is_direct_checkout');
                    Session::forget('email');
                    Session::forget('phone');
                    return redirect('touchless-parking-atlanta-thankyou-checkout/' . base64_encode($mainTicket->ticket_number));
                }
                return redirect('touchless-parking-atlanta-checkout/' . base64_encode($ticket->ticket_number))->with('success', 'Success! Overstay payment successfully done.');            /*   $ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_datetime = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            Session::forget('is_direct_checkout');
                                            return redirect('touchless-parking-thankyou-checkout/'.base64_encode($ticket->ticket_number));  */
            }
        } else {
            return back()->with('danger', "Please use valid ticket number with amount.");
        }
    }


    public function getTouchlessParkingPrepaidCheckinDetails($ticket_number, $encrypt = null)
    {
        $reservation = Reservation::with(['facility', 'user'])->where('ticketech_code', base64_decode($ticket_number))->whereNull('cancelled_at')->first();


        if (!$reservation) {
            return Redirect::to('error-checkin');
        }

        $this->checkFacilityAvailable($reservation->facility_id);

        $this->setCustomTimezone($reservation->facility_id);

        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($reservation->user_id) ? $reservation->user_id : '0';
        $arrEvents['event_name'] = "Touchless Before Prepaid Checkin Screen";
        $arrEvents['facility_id'] = isset($reservation->facility_id) ? $reservation->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Touchless before prepaid checkin  Function getTouchlessParkingPrepaidCheckinDetails Request " . base64_decode($ticket_number) . ' and encryt ' . $encrypt;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);


        if ($encrypt == '') {
            $config = Configuration::where('field_name', 'prepaid-checkin-time')->first();
            if (count($config) > 0) {
                $prepaidCheckinTime = $config->field_value;
            } else {
                $prepaidCheckinTime = 15;
            }
            $today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);
            $reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
            $reservationEndDate = $reservationstartDate->addHours($reservation->length);
            //dd($today, $reservationstartDate, $reservationEndDate);
            if (strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)) {
                return Redirect::to('error-prepaid-checkin');
            }

            if (strtotime($today) < strtotime($reservation->start_timestamp)) {
                return Redirect::to('error-prepaid-checkin');
            }
        }

        /*$checkinData = Ticket::with(['user','reservation'])->where('reservation_id', $reservation->id)->first();
        if($checkinData && $checkinData->is_checkin == '1'){
            return Redirect::to('prepaid-checkout-details/'.base64_encode($checkinData->ticket_number));
        }
        if($checkinData && $checkinData->is_checkout == '1'){
            return Redirect::to('thankyou-checkout/'.base64_encode($checkinData->ticket_number));
        }
        if(!$checkinData){

            $ticket['user_id'] = $reservation->user_id;
            $ticket['reservation_id'] = $reservation->id;
            $ticket['facility_id'] = $reservation->facility_id;
            $ticket['length'] = $reservation->length;
            $ticket['ticket_security_code'] = rand(1000, 9999);
            $ticket['total'] = $reservation->total;
            $ticket['grand_total'] = $reservation->total;
            $ticket['check_in_datetime'] = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
            $ticket['check_in_datetime'] = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
            $ticket['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
            $ticket['ticket_number'] = 'PE'.rand(100,999).rand(100,999);
            $checkinData = Ticket::create($ticket);
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);*/

        /*$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);*/
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
        $from = $startDate->addHours($reservation->length);
        $diff_in_hours = $from->diffInRealHours($arrival_time);
        if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
            $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
        }
        $reservation['check_in_datetime'] = $reservation->start_timestamp;
        $reservation['checkout_datetime'] = $from->subSeconds(1);
        $reservation['diff_in_days'] = $arrival_time->diffInDays($from);
        $reservation['diff_in_hours'] = $arrival_time->copy()->addDays($reservation['diff_in_days'])->diffInRealHours($from);
        $reservation['diff_in_minutes'] = $arrival_time->copy()->addDays($reservation['diff_in_days'])->addHours($reservation['diff_in_hours'])->diffInRealMinutes($from);
        return view('atlanta-checkin-checkout.touchless-parking-prepaid', ['data' => $reservation]);
    }


    public function getTouchelessParkingPrepaidCheckin($ticket_number, $encrypt = null)
    {

        $reservation = Reservation::with(['facility', 'user'])->where('ticketech_code', base64_decode($ticket_number))->whereNull('cancelled_at')->first();

        if (!$reservation) {
            return Redirect::to('error-checkin');
        }

        $alreadyCheckin = Ticket::where('reservation_id', $reservation->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if ($alreadyCheckin) {
            return Redirect::to('touchless-parking-atlanta-prepaid-checkin-success/' . base64_encode($alreadyCheckin->ticket_number));
        }

        $this->setCustomTimezone($reservation->facility_id);

        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($reservation->user_id) ? $reservation->user_id : '0';
        $arrEvents['event_name'] = "Touchless Prepaid Checkin Success";
        $arrEvents['facility_id'] = isset($reservation->facility_id) ? $reservation->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Touchless prepaid checkin  Function getTouchelessParkingPrepaidCheckin Request " . base64_decode($ticket_number) . ' and encryt ' . $encrypt;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);

        if ($encrypt != '') {
            $explode = $encrypt;
            $decrypt = base64_decode($explode);
            if ($decrypt) {
                $decrytArray = json_decode($decrypt);
                $this->checkFacilityAvailable($decrytArray->facility_id);
                $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                $checkinData = [];
                if ($gate && isset($gate->gate_type)) {


                    //check third party gate API
                    $facility = Facility::where('id', $decrytArray->facility_id)->first();
                    if (!$facility) {
                        return "Invalid garage.";
                    }
                    /*$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                    if($gateDetails){
                        if($gateDetails->host !=''){
                            $params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate];
                            $this->log->info("Abacus Web Service : vehicle command aboout to send checkin entry reservation code {$reservation->ticketech_code} user {$reservation->user_id} facility {$facility->id} params ".json_encode($params));
                            $response = AbacusWebService::isvehicleAvailable($params, $gateDetails->host);
                            if($response['success'] == false){
                                return back()->with('danger', "Sorry! We are having issue on gate opening.");
                            }
                            $this->log->info("Abacus Web Service : vehicle command complete checkin entry reservation code {$reservation->ticketech_code} user {$reservation->user_id} facility {$facility->id} response". json_encode($response));
                            if($response['data']['IsLoopVon'] == '1' || $response['data']['IsLoopVon'] == '0'){
                                $guid = AbacusWebService::createGuid();
                                $cmd_params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate,'command'=>'45','iParameter1'=>'0','iParameter2'=>'0','iParameter3'=>'0','iParameter4'=>'0','textParameter1'=>'0','textParameter2'=>'0', 'guidParameter1'=>$guid, 'guidParameter2'=>$guid];
                                $this->log->info("Abacus Web Service : command run success checkin entry facility {$facility->id} params". json_encode($cmd_params));
                            $command_response = AbacusWebService::commandStatus($cmd_params, $gateDetails->host);
                            if($command_response['success'] == true){
                                if($command_response['data'][0] == "0" || $command_response['data'][0] == "1"){
                                    
                                }else{
                                    return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                }
                                
                            }else{
                                return back()->with('danger', "Sorry! We are having issue on gate opening.");
                            }
                            }else{
                                return back()->with('danger', "Sorry! Vehicle not present.");
                            }

                        }
                    }*/

                    if ($gate->gate_type == 'entry') {
                        $data['user_id'] = $reservation->user_id;
                        $data['checkin_gate'] = $decrytArray->gate;
                        $data['facility_id'] = $decrytArray->facility_id;
                        $data['is_checkin'] = 1;
                        $data['ticket_number'] = 'PE' . rand(100, 999) . rand(100, 999);
                        $data['check_in_datetime'] = $reservation->start_timestamp;
                        $data['checkin_time'] = date('Y-m-d H:i:s');
                        $checkoutTime = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                        $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime("-1 second", strtotime($checkoutTime)));
                        $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime("-1 second", strtotime($checkoutTime)));
                        $data['ticket_security_code'] = rand(1000, 9999);
                        $data['reservation_id'] = $reservation->id;
                        $data['total'] = $reservation->total;
                        $data['grand_total'] = $reservation->total;
                        $data['user_pass_id'] = $reservation->user_pass_id;
                        $data['partner_id'] = $reservation->partner_id;
                        $checkinData = Ticket::create($data);

                        $reservation->is_ticket = '1';
                        $reservation->save();
                        Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $checkinData->id, 'type' => 'checkin'));
                        return Redirect::to('touchless-parking-atlanta-prepaid-checkin-success/' . base64_encode($checkinData->ticket_number));
                    } else {
                        return back()->with('danger', "Sorry! Please scan valid QR code.");
                    }
                }
            }
        }

        return view('atlanta-checkin-checkout.touchless-parking-prepaid-checkin', ['data' => $reservation]);
    }

    public function confirmTouchelessParkingPrepaidCheckin(Request $request)
    {
        /*$request = $request->all();
        dd($request);*/
        try {
            if (isset($request->encrypt)) {
                $explode = explode('/', $request->encrypt);
                if (count($explode) <= 1) {
                    return back()->with('danger', "Please scan the right QR code.");
                }
                $decrypt = base64_decode($explode[4]);
                if ($decrypt) {
                    $decrytArray = json_decode($decrypt);
                    $this->checkFacilityAvailable($decrytArray->facility_id);

                    $this->setCustomTimezone($decrytArray->facility_id);

                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                    $checkinData = [];
                    if ($gate && isset($gate->gate_type)) {
                        if ($gate->gate_type == 'entry') {

                            $reservation = Reservation::where('ticketech_code', $request->order_number)->whereNull('cancelled_at')->first();

                            if (!$reservation) {
                                return Redirect::to('error-checkin');
                            }

                            $this->checkFacilityAvailable($reservation->facility_id);


                            $ticket['user_id'] = $reservation->user_id;
                            $ticket['reservation_id'] = $reservation->id;
                            $ticket['facility_id'] = $reservation->facility_id;
                            $ticket['length'] = $reservation->length;
                            $ticket['ticket_security_code'] = rand(1000, 9999);
                            $ticket['total'] = $reservation->total;
                            $ticket['grand_total'] = $reservation->total;
                            $ticket['check_in_datetime'] = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
                            $ticket['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                            $ticket['checkin_time'] = date("Y-m-d H:i:s");
                            $ticket['ticket_number'] = 'PE' . rand(100, 999) . rand(100, 999);
                            $ticket['user_pass_id'] = $reservation->user_pass_id;
                            $checkinData = Ticket::create($ticket);
                            if ($checkinData) {
                                $checkinData->checkin_gate = $decrytArray->gate;
                                $checkinData->is_checkin = '1';
                                $checkinData->save();

                                $reservation->is_ticket = '1';
                                $reservation->save();
                                Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $checkinData->id, 'type' => 'checkin'));
                                return redirect('touchless-parking-atlanta-prepaid-checkin-success/' . base64_encode($checkinData->ticket_number))->with('success', 'Success! You have successfully check-in.');
                            } else {
                                return back()->with('danger', 'You have already checkin.');
                            }
                        } else {
                            return back()->with('danger', "Please scan the right QR code.");
                        }
                    } else {
                        return Redirect::to('error-checkin');
                    }
                }
            } else {
                return back()->with('danger', 'Please scan valid scan QR code.');
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid scan QR code.');
        }
    }


    public function getTouchelessParkingPrepaidConfirmCheckinSuccess($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('atlanta-checkin-checkout.touchless-parking-prepaid-checkin-success', ['data' => $checkinData]);
    }


    public function getTouchelessParkingPrepaidCheckoutDetails($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($checkinData->user_id) ? $checkinData->user_id : '0';
        $arrEvents['event_name'] = "Touchless SMS Drive Prepaid checkin/checkout Link By Mobile/Email";
        $arrEvents['facility_id'] = isset($checkinData->facility_id) ? $checkinData->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Get Prepaid Booking & Checkin Details Function getTouchelessParkingPrepaidCheckoutDetails Request " . $checkinData->ticket_number;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);

        $currentStartDate = Carbon::createFromFormat('Y-m-d H:i:s', ($checkinData->checkin_time == '' ? date('Y-m-d H:i:s') : $checkinData->checkin_time));
        $currentEndDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['current_check_in_datetime'] = $currentStartDate;
        $checkinData['current_checkout_datetime'] = $currentEndDate;
        $checkinData['current_diff_in_days'] = $currentStartDate->diffInDays($currentEndDate);
        $checkinData['current_diff_in_hours'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->diffInRealHours($currentEndDate);
        $checkinData['current_diff_in_minutes'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->addHours($checkinData['current_diff_in_hours'])->diffInRealMinutes($currentEndDate);

        return view('atlanta-checkin-checkout.touchless-parking-prepaid-checkout-details', ['data' => $checkinData]);
    }


    public function getTouchelessParkingPrepaidCheckout($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($checkinData->user_id) ? $checkinData->user_id : '0';
        $arrEvents['event_name'] = "Touchless Prepaid After Checkin Success";
        $arrEvents['facility_id'] = isset($checkinData->facility_id) ? $checkinData->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Prepaid checkin success Function getTouchelessParkingPrepaidCheckout Request " . $checkinData->ticket_number;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);

        if (Session::get('is_direct_checkout') == '1') {

            $facility = Facility::where('id', $checkinData->facility_id)->first();
            if (!$facility) {
                return back()->with('danger', "Invalid garage.");
            }
            $gate = Gate::where('gate', $checkinData->checkout_gate)->where('facility_id', $facility->id)->first();
            if (!$gate) {
                return back()->with('danger', "Something wrong in gate.");
            }
            //check third party gate API
            /*$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
            if($gateDetails){
                if($gateDetails->host !=''){
                    $params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate];
                    $this->log->info("Abacus Web Service : vehicle command aboout to send checkout exit facility {$facility->id} params ".json_encode($params));
                    $response = AbacusWebService::isvehicleAvailable($params, $gateDetails->host);
                    if($response['success'] == false){
                        return back()->with('danger', "Something wrong in parking.");
                    }
                    $this->log->info("Abacus Web Service : vehicle command complete checkout exit facility {$facility->id} response". json_encode($response));
                    if($response['data']['IsLoopVon'] == '1' || $response['data']['IsLoopVon'] == '0'){
                        $guid = AbacusWebService::createGuid();
                        $cmd_params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate,'command'=>'45','iParameter1'=>'0','iParameter2'=>'0','iParameter3'=>'0','iParameter4'=>'0','textParameter1'=>'0','textParameter2'=>'0', 'guidParameter1'=>$guid, 'guidParameter2'=>$guid];
                        $this->log->info("Abacus Web Service : command run success checkout exit facility {$facility->id} params". json_encode($cmd_params));
                    $command_response = AbacusWebService::commandStatus($cmd_params, $gateDetails->host);
                    $this->log->info("Abacus Web Service Command 2 Response: checkin entry response". json_encode($command_response));
                    if($command_response['success'] == true){
                        if($command_response['data'][0] == "0" || $command_response['data'][0] == "1"){
                
                        }else{
                            return back()->with('danger', "Something wrong in parking.");
                        }
                        
                    }else{
                        return back()->with('danger', "Something wrong in parking.");
                    }
                    }else{
                        return back()->with('danger', "Sorry! Vehicle not present.");
                    }

                }
            }*/


            $reservation = Reservation::where('id', $checkinData->reservation_id)->first();
            $reservation->is_ticket = '2';
            $reservation->save();

            $checkinData->is_checkout = '1';
            $checkinData->checkout_time = date("Y-m-d H:i:s");
            $checkinData->save();

            Session::forget('is_direct_checkout');
            Session::forget('email');
            Session::forget('phone');
            return redirect('touchless-parking-atlanta-prepaid-checkout-success/' . base64_encode($checkinData->ticket_number));
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        //$endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('atlanta-checkin-checkout.touchless-parking-prepaid-checkout', ['data' => $checkinData]);
    }

    public function getTouchelessParkingPrepaidCheckoutScreen($ticket_number)
    {
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        if ($checkinData->is_checkout == '1') {
            return Redirect::to('atlanta-error-checkout');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        //log all request in event table
        $arrEvents = array();
        $arrEvents['user_id'] = isset($checkinData->user_id) ? $checkinData->user_id : '0';
        $arrEvents['event_name'] = "Touchless Prepaid Checkout Scan Screen";
        $arrEvents['facility_id'] = isset($checkinData->facility_id) ? $checkinData->facility_id : '';
        $arrEvents['os_version'] = "-1";
        $arrEvents['error_message'] = "Touchless Prepaid Checkout Scan Screen Function getTouchelessParkingPrepaidCheckoutScreen Request " . $checkinData->ticket_number;
        $arrEvents['device'] = "Web";
        $arrEvents['email_id'] = '';

        $this->saveEventsLogs($arrEvents);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('atlanta-checkin-checkout.touchless-parking-prepaid-checkout-screen', ['data' => $checkinData]);
    }

    public function confirmTouchelessParkingPrepaidCheckout(Request $request)
    {
        //$request = $request->all();
        try {

            if (isset($request->encrypt)) {
                $explode = explode('/', $request->encrypt);
                if (count($explode) < 5) {
                    $exp = explode(" : ", $explode[0]);
                    if (strpos($exp[1], '.') == true) {
                        $updatedExplode = explode(".", $exp[1]);
                        $decrypt = base64_decode($updatedExplode[0]);
                    } else {
                        return back()->with('danger', "Please scan the right QR code.");
                    }
                } else {
                    $decrypt = base64_decode($explode[4]);
                }

                if ($decrypt) {
                    $decrytArray = json_decode($decrypt);
                    if ($decrytArray) {

                        $this->setCustomTimezone($decrytArray->facility_id);

                        $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                        $checkinData = [];
                        if ($gate && isset($gate->gate_type)) {
                            if ($gate->gate_type == 'exit') {

                                $is_checkout = Ticket::where('ticket_number', $request->order_number)->first();
                                if (!$is_checkout) {
                                    //return Redirect::to('already-checkout');
                                    return back()->with('danger', 'Please scan valid scan QR code.');
                                }
                                if ($is_checkout->is_checkout == '1') {
                                    return Redirect::to('already-checkout');
                                }
                                //log all request in event table

                                $arrEvents = array();
                                $arrEvents['user_id'] = isset($is_checkout->user_id) ? $is_checkout->user_id : '0';
                                $arrEvents['event_name'] = "Touchless Prepaid Checkout Scan Confirm";
                                $arrEvents['facility_id'] = isset($is_checkout->facility_id) ? $is_checkout->facility_id : '';
                                $arrEvents['os_version'] = "-1";
                                $arrEvents['error_message'] = "Prepaid Checkout scan confirm Function confirmTouchelessParkingPrepaidCheckout Request " . $is_checkout->ticket_number;
                                $arrEvents['device'] = "Web";
                                $arrEvents['email_id'] = '';

                                $this->saveEventsLogs($arrEvents);

                                $checkinData = Ticket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                                if ($checkinData) {

                                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                    $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                                    if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                        $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                    }
                                    $checkinData['length'] = $diff_in_hours;
                                    /*$facility = Facility::find($checkinData->facility_id);
                                $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);*/

                                    if (strtotime(date("Y-m-d H:i:s")) > strtotime(date("Y-m-d H:i:s", strtotime($checkinData->estimated_checkout)))) {
                                        $this->log->info("Touchless Prepaid : user have overstay time with ticket number {$checkinData->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                        return redirect('touchless-parking-atlanta-prepaid-overstay-pay/' . base64_encode($checkinData->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                    }



                                    $facility = Facility::where('id', $checkinData->facility_id)->first();
                                    if (!$facility) {
                                        return back()->with('danger', 'Invalid Garage.');
                                    }
                                    /*$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                                if($gateDetails){
                                    if($gateDetails->host !=''){
                                        $params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate];
                                        $this->log->info("Abacus Web Service : vehicle command aboout to send checkout exit facility {$facility->id} params ".json_encode($params));
                                        $response = AbacusWebService::isvehicleAvailable($params, $gateDetails->host);
                                        if($response['success'] == false){
                                            return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                        }
                                        $this->log->info("Abacus Web Service : vehicle command complete checkout exit facility {$facility->id} response". json_encode($response));
                                        if($response['data']['IsLoopVon'] == '1' || $response['data']['IsLoopVon'] == '0'){
                                            $guid = AbacusWebService::createGuid();
                                            $cmd_params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate,'command'=>'45','iParameter1'=>'0','iParameter2'=>'0','iParameter3'=>'0','iParameter4'=>'0','textParameter1'=>'0','textParameter2'=>'0', 'guidParameter1'=>$guid, 'guidParameter2'=>$guid];
                                            $this->log->info("Abacus Web Service : command run success checkout exit facility {$facility->id} params". json_encode($cmd_params));
                                        $command_response = AbacusWebService::commandStatus($cmd_params, $gateDetails->host);
                                        $this->log->info("Abacus Web Service Command 2 Response: checkin entry response". json_encode($command_response));
                                        if($command_response['success'] == true){
                                            if($command_response['data'][0] == "0" || $command_response['data'][0] == "1"){
                                                
                                            }else{
                                                return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                            }
                                            
                                        }else{
                                            return back()->with('danger', "Sorry! We are having issue on gate opening.");
                                        }
                                        }else{
                                            return back()->with('danger', "Sorry! Vehicle not present.");
                                        }

                                    }
                                }*/

                                    $checkinData->is_checkout = '1';
                                    $checkinData->checkout_gate = $decrytArray->gate;
                                    $checkinData->checkout_time = date("Y-m-d H:i:s");
                                    $checkinData->save();

                                    $reservation = Reservation::where('id', $checkinData->reservation_id)->first();
                                    $reservation->is_ticket = '2';
                                    $reservation->save();
                                    if ($checkinData) {
                                        $overstayTicket = OverstayTicket::where('ticket_number', $request->order_number)->get();
                                        if ($overstayTicket) {
                                            foreach ($overstayTicket as $key => $value) {
                                                $value->is_checkout = '1';
                                                $value->checkout_gate = $decrytArray->gate;
                                                $value->save();
                                            }
                                        }
                                        $this->log->info("Prepaid : user prepaid checkout with ticket number {$checkinData->ticket_number} and go to the thankyou page  with browser {$_SERVER['HTTP_USER_AGENT']}");
                                        return Redirect::to('touchless-parking-atlanta-prepaid-checkout-success/' . base64_encode($checkinData->ticket_number));
                                    } else {
                                        return back()->with('danger', 'Please scan valid scan QR code.');
                                    }
                                }
                            } else {
                                return back()->with('danger', 'Please scan valid QR code.');
                            }
                        } else {
                            return Redirect::to('error-checkin');
                        }
                    } else {
                        return back()->with('danger', 'Please scan valid QR code.');
                    }
                }
            } else {
                return back()->with('danger', 'Please scan valid QR code.');
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid QR code.');
        }
    }


    public function touchlessParkingPrepaidCheckoutSuccess($ticket_number)
    {
        $checkinData = Ticket::with(['user', 'facility'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '1')->first();

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $checkinData['is_overstay'] = '0';

        $currentStartDate = Carbon::createFromFormat('Y-m-d H:i:s', ($checkinData->checkin_time == '' ? date('Y-m-d H:i:s') : $checkinData->checkin_time));
        $currentEndDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_time);
        $checkinData['current_check_in_datetime'] = $currentStartDate;
        $checkinData['current_checkout_datetime'] = $currentEndDate;
        $checkinData['current_diff_in_days'] = $currentStartDate->diffInDays($currentEndDate);
        $checkinData['current_diff_in_hours'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->diffInRealHours($currentEndDate);
        $checkinData['current_diff_in_minutes'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->addHours($checkinData['current_diff_in_hours'])->diffInRealMinutes($currentEndDate);

        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->orderBy('id', 'DESC')->first();
        if ($overstay) {
            $checkinData['check_in_datetime'] = $overstay->check_in_datetime;
            $checkinData['checkout_datetime'] = $overstay->checkout_datetime;
            $checkinData['length'] = $overstay->length;
            $checkinData['is_overstay'] = '1';
            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['check_in_datetime']);
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['checkout_datetime']);
            $checkinData['diff_in_hours'] = $startDate->diffInRealHours($endDate);
            $checkinData['diff_in_minutes'] = $startDate->copy()->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        }
        $this->log->info("Prepaid : user successfully checkout and thankyou page showing with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('atlanta-checkin-checkout.touchless-parking-prepaid-checkout-success', ['data' => $checkinData]);
    }

    public function getTouchlessParkingPrepaidCheckinPaymentDetailsForOverstay($ticket_number)
    {
        $checkinData = Ticket::with('user')->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->estimated_checkout);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
            $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
        }
        $checkinData['overstay_length'] = $diff_in_hours;
        //$checkinData['overstay_check_in_datetime'] = $arrival_time;
        $facility = Facility::find($checkinData->facility_id);
        $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
        if ($rate) {
            $checkinData['overstay_price'] = $rate['price'];
        }

        $checkinData['rate'] = $rate;
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_hours'] = $startDate->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $this->log->info("Prepaid : user is now overstay payment screen after checkout with ticket number {$checkinData->ticket_number}  with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('atlanta-checkin-checkout.touchless-parking-prepaid-overstay-payment', ['data' => $checkinData]);
    }

    public function makeTouchlessParkingPrepaidOverstayPayment(Request $request)
    {
        $this->setDecryptedCard($request);
        if (strlen($this->request->expiration_date) < 5) {
            return back()->with('danger', "Error! Invalid expiry date.");
        }
        if (strlen($this->request->card_number) < 13) {
            return back()->with('danger', "Error! Invalid card number.");
        }
        if (strlen($this->request->security_code) < 3) {
            return back()->with('danger', "Error! The security code must be between 3 and 4 digits.");
        }

        $this->setCustomTimezone($this->request->facility_id);

        if (preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $this->request->email)) {
        } else {
            return back()->with('danger', "Please enter valid email.");
        }

        $this->facility = Facility::find($this->request->facility_id);
        $this->user = User::where('phone', $this->request->phone)->where('created_by', $this->facility->owner_id)->first();
        if ($this->request->total > 0) {
            try {
                $is_partner = 0;
                if ($this->user->user_type == 3) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if ($this->partnerPaymentDetails) {
                        $is_partner = 1;
                    } else {
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                } else {
                    $is_partner = 0;
                }
                if ($is_partner == 1) {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->isPartner($this->partnerPaymentDetails)
                        ->setBillingAddress($this->getBillingArray());
                } else {
                    $this->authNet
                        ->setUser($this->user)
                        ->isReservation()
                        ->setFacility($this->facility)
                        ->setBillingAddress($this->getBillingArray());
                }

                if (isset($this->paymentProfileId)) { // Use our logged in users profile
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    $this->authNet->isPartner($this->partnerPaymentDetails)
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            } catch (Exception $e) {
                $request->session()->flash(
                    'alert-danger',
                    'Payment error!'
                );
                return back()->withError(['danger' => $e]);
            }
        }

        $ticket = $this->saveOverstayTicket();
        if ($this->request->total > 0) {
            $reservationMode = "Touchless Prepaid Overstay Parking";
            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else {
                $ticketech_guid = rand(100000, 999999);
            }

            try {

                // Use our database rate price to create the transaction
                try {
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();

                    // Fire off transaction - this method will throw an exception if errors occur
                } catch (AuthorizeNetException $e) {
                    $ticket->delete();
                    return back()->with('danger', $e->getMessage());
                }
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                $ticket->delete();
                return back()->with('danger', $e->getMessage());
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->save();
            $this->log->info("Touchless Prepaid : user {$this->user->email} have paid overstay payment and redirect to checkout page with ticket number {$ticket->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {
                Artisan::queue('email:touchless-parking-prepaid-confirm-checkin', array('id' => $ticket->id, 'type' => 'overstay'));

                if (Session::get('is_direct_checkout') == '1') {
                    $ticket->is_checkout = '1';
                    $ticket->checkout_datetime = date("Y-m-d H:i:s");
                    $ticket->save();

                    $mainTicket = Ticket::where('ticket_number', $ticket->ticket_number)->first();
                    $mainTicket->is_checkout = '1';
                    $mainTicket->checkout_gate = $ticket->checkout_gate;
                    $mainTicket->checkout_time = date("Y-m-d H:i:s");
                    $mainTicket->save();

                    $reservation = Reservation::where('id', $mainTicket->reservation_id)->first();
                    $reservation->is_ticket = '2';
                    $reservation->save();

                    Session::forget('is_direct_checkout');
                    Session::forget('email');
                    Session::forget('phone');
                    return redirect('touchless-parking-atlanta-prepaid-checkout-success/' . base64_encode($mainTicket->ticket_number));
                }

                return redirect('touchless-parking-atlanta-prepaid-checkout-screen/' . base64_encode($ticket->ticket_number))->with('success', 'Success! Overstay payment successfully done.');
            }
        } else {
            return back()->with('danger', "Please use valid ticket number with amount.");
        }
    }

    public function getTouchlessParkingVerifyCheckout($key)
    {

        $decrypt = json_decode(base64_decode($key));
        if ($decrypt) {
            if (isset($decrypt->facility_id)) {
                $gate = Gate::where('gate', $decrypt->gate)->where('facility_id', $decrypt->facility_id)->first();
                if ($gate && isset($gate->gate_type)) {
                    if ($gate->gate_type != 'exit') {
                        Redirect::to('error-facility');
                    }

                    if ($this->checkFacilityAvailable($decrypt->facility_id) == false) {
                        $this->log->info("{$decrypt->facility_id} not availabe right now.");
                        Redirect::to('error-facility');
                    }
                }
            } else {
                return Redirect::to('error-checkin');
            }
        } else {
            return Redirect::to('error-checkin');
        }
        if (Session::get('email') != '' && Session::get('phone') != '') {
            try {
                if (isset($key)) {
                    $explode = $key;
                    $decrypt = base64_decode($explode);
                    if ($decrypt) {
                        $decrytArray = json_decode($decrypt);
                        $this->checkFacilityAvailable($decrytArray->facility_id);

                        $this->setCustomTimezone($decrytArray->facility_id);

                        $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                        $checkinData = [];
                        if ($gate && isset($gate->gate_type)) {
                            if ($gate->gate_type == 'exit') {
                                $checkFacilityOwner = Facility::where('id', $decrytArray->facility_id)->first();
                                $existUser = User::where('phone', Session::get('phone'))->where('created_by', $checkFacilityOwner->owner_id)->first();
                                if ($existUser) {
                                    $ticket = Ticket::where('user_id', $existUser->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                                    if ($ticket) {
                                        Session::put('is_direct_checkout', '1');
                                        $ticket->checkout_gate = $decrytArray->gate;
                                        $ticket->save();
                                        if ($ticket->reservation_id != '') {
                                            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                                $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            /*$facility = Facility::find($ticket->facility_id);
                                                $isMember = 0;
                                                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE,$isMember);*/
                                            if (strtotime(date("Y-m-d H:i:s")) > strtotime(date("Y-m-d H:i:s", strtotime($ticket->estimated_checkout)))) {
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('touchless-parking-atlanta-prepaid-overstay-pay/' . base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            } else {
                                                /*$ticket->is_checkout = '1';
                                                $ticket->checkout_gate = $decrytArray->gate;
                                                $ticket->checkout_time = date("Y-m-d H:i:s");
                                                $ticket->save();

                                                Session::forget('is_direct_checkout');
                                                Session::forget('email');
                                                Session::forget('phone');*/
                                                return redirect('touchless-parking-atlanta-prepaid-checkout-details/' . base64_encode($ticket->ticket_number));
                                            }
                                        } else {
                                            if ($ticket->anet_transaction_id != '') {

                                                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                                $diff_in_hours = $arrival_time->diffInRealHours($from);
                                                if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                                    $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                                }
                                                $ticket['length'] = $diff_in_hours;
                                                $facility = Facility::find($ticket->facility_id);
                                                $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                                if (strtotime(date("Y-m-d H:i:s")) > strtotime(date("Y-m-d H:i:s", strtotime($ticket->estimated_checkout)))) {
                                                    $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                    return redirect('touchless-parking-atlanta-overstay-pay/' . base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                                } else {

                                                    $ticket->is_checkout = '1';
                                                    $ticket->checkout_gate = $decrytArray->gate;
                                                    $ticket->checkout_datetime = date("Y-m-d H:i:s");

                                                    $ticket->checkin_time = date("Y-m-d H:i:s");
                                                    $ticket->checkout_time = date("Y-m-d H:i:s");
                                                    $ticket->save();

                                                    Session::forget('is_direct_checkout');
                                                    Session::forget('email');
                                                    Session::forget('phone');
                                                    return redirect('touchless-parking-atlanta-thankyou-checkout/' . base64_encode($ticket->ticket_number));
                                                }
                                            } else {
                                                return redirect('touchless-parking-atlanta-checkin-details/' . base64_encode($ticket->ticket_number));
                                            }
                                        }
                                    } else {
                                        return back()->with('danger', 'Sorry! You do not have any active checkin.');
                                    }
                                } else {
                                    return back()->with('danger', 'Please scan valid scan QR code.');
                                }
                            } else {
                                return back()->with('danger', "Please scan the right QR code.");
                            }
                        } else {
                            return Redirect::to('error-checkin');
                        }
                    }
                } else {
                    return back()->with('danger', 'Please scan valid scan QR code.');
                }
            } catch (Exception $e) {
                return back()->with('danger', 'Please scan valid scan QR code.');
            }
        }
        return view('atlanta-checkin-checkout.touchless-parking-verify-checkout', ['data' => $key]);
    }

    public function confirmTouchlessParkingDirectCheckout(Request $request)
    {
        /*$request = $request->all();
        dd($request);*/
        try {
            if (isset($request->encrypt)) {
                $explode = $request->encrypt;
                $decrypt = base64_decode($explode);
                if ($decrypt) {

                    $request->phone = preg_replace('/\D+/', '', $request->phone);
                    if (preg_match('/^[0-9]{10}+$/', $request->phone)) {
                        if ($request->phone == "0000000000") {
                            return back()->with('danger', "Please enter valid phone number.");
                        }
                    } else {
                        return back()->with('danger', "Please enter valid phone number.");
                    }
                    if (preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $request->email)) {
                    } else {
                        return back()->with('danger', "Please enter valid email.");
                    }
                    $decrytArray = json_decode($decrypt);
                    $this->checkFacilityAvailable($decrytArray->facility_id);

                    $this->setCustomTimezone($decrytArray->facility_id);

                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                    $checkinData = [];
                    if ($gate && isset($gate->gate_type)) {
                        if ($gate->gate_type == 'exit') {
                            $checkFacilityOwner = Facility::where('id', $decrytArray->facility_id)->first();

                            $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
                            if ($geoLocation['geoplugin_countryCode'] == 'IN') {
                                $countryCode = "+91";
                            } elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
                                $countryCode = "+1";
                            } else {
                                $countryCode = "+1";
                            }

                            $existUser = User::where('phone', $countryCode . $request->phone)->where('created_by', $checkFacilityOwner->owner_id)->first();
                            if ($existUser) {
                                $ticket = Ticket::where('user_id', $existUser->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                                if ($ticket) {
                                    Session::put('is_direct_checkout', '1');
                                    $ticket->checkout_gate = $decrytArray->gate;
                                    $ticket->save();
                                    if ($ticket->reservation_id != '') {
                                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                        $diff_in_hours = $arrival_time->diffInRealHours($from);
                                        if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                            $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                        }
                                        $ticket['length'] = $diff_in_hours;
                                        /*$facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);*/
                                        if (strtotime(date("Y-m-d H:i:s")) > strtotime(date("Y-m-d H:i:s", strtotime($ticket->estimated_checkout)))) {
                                            $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                            return redirect('touchless-parking-atlanta-prepaid-overstay-pay/' . base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                        } else {
                                            /*$ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_time = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');*/
                                            return redirect('touchless-parking-atlanta-prepaid-checkout-details/' . base64_encode($ticket->ticket_number));
                                        }
                                    } else {
                                        if ($ticket->anet_transaction_id != '') {

                                            $facility = Facility::where('id', $decrytArray->facility_id)->first();
                                            if (!$facility) {
                                                return back()->with('danger', "Invalid garage.");
                                            }


                                            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                                $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            /*$rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            if($rate['price'] != $ticket->grand_total){
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('touchless-parking-atlanta-overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            }else{*/


                                            //check third party gate API
                                            /*$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                                            if($gateDetails){
                                                if($gateDetails->host !=''){
                                                    $params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate];
                                                    $this->log->info("Abacus Web Service : vehicle command aboout to send checkout exit facility {$facility->id} params ".json_encode($params));
                                                    $response = AbacusWebService::isvehicleAvailable($params, $gateDetails->host);
                                                    if($response['success'] == false){
                                                        return back()->with('danger', "Something wrong in parking.");
                                                    }
                                                    $this->log->info("Abacus Web Service : vehicle command complete checkout exit facility {$facility->id} response". json_encode($response));
                                                    if($response['data']['IsLoopVon'] == '1' || $response['data']['IsLoopVon'] == '0'){
                                                        $guid = AbacusWebService::createGuid();
                                                        $cmd_params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate,'command'=>'45','iParameter1'=>'0','iParameter2'=>'0','iParameter3'=>'0','iParameter4'=>'0','textParameter1'=>'0','textParameter2'=>'0', 'guidParameter1'=>$guid, 'guidParameter2'=>$guid];
                                                        $this->log->info("Abacus Web Service : command run success checkout exit facility {$facility->id} params". json_encode($cmd_params));
                                                    $command_response = AbacusWebService::commandStatus($cmd_params, $gateDetails->host);
                                                    $this->log->info("Abacus Web Service Command 2 Response: checkin entry response". json_encode($command_response));
                                                    if($command_response['success'] == true){
                                                        if($command_response['data'][0] == "0" || $command_response['data'][0] == "1"){
                                                
                                                        }else{
                                                            return back()->with('danger', "Something wrong in parking.");
                                                        }
                                                        
                                                    }else{
                                                        return back()->with('danger', "Something wrong in parking.");
                                                    }
                                                    }else{
                                                        return back()->with('danger', "Sorry! Vehicle not present.");
                                                    }

                                                }
                                            }*/


                                            $ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_datetime = date("Y-m-d H:i:s");
                                            $ticket->checkout_time = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            if ($ticket->reservation_id != '') {
                                                $reservation = Reservation::where('id', $ticket->reservation_id)->first();
                                                $reservation->is_ticket = '2';
                                                $reservation->save();
                                            }

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');
                                            return redirect('touchless-parking-atlanta-thankyou-checkout/' . base64_encode($ticket->ticket_number));

                                            //}


                                        } else {
                                            return redirect('touchless-parking-atlanta-checkin-details/' . base64_encode($ticket->ticket_number));
                                        }
                                    }
                                } else {
                                    return back()->with('danger', 'Sorry! You do not have any active checkin.');
                                }
                            } else {
                                return back()->with('danger', 'Please scan valid scan QR code.');
                            }
                        } else {
                            return back()->with('danger', "Please scan the right QR code.");
                        }
                    } else {
                        return Redirect::to('error-checkin');
                    }
                }
            } else {
                return back()->with('danger', 'Please scan valid scan QR code.');
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid scan QR code.');
        }
    }

    public function isUserSessionExist($encrypt)
    {
        /*$request = $request->all();
        dd($request);*/
        try {
            if (isset($encrypt)) {
                $explode = $encrypt;
                $decrypt = base64_decode($explode);
                if ($decrypt) {
                    $decrytArray = json_decode($decrypt);
                    $this->checkFacilityAvailable($decrytArray->facility_id);

                    $this->setCustomTimezone($decrytArray->facility_id);

                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                    $checkinData = [];
                    if ($gate && isset($gate->gate_type)) {
                        if ($gate->gate_type == 'exit') {
                            $existUser = User::where('email', Session::get('email'))->first();
                            if ($existUser) {
                                $ticket = Ticket::where('user_id', $existUser->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                                if ($ticket) {
                                    Session::put('is_direct_checkout', '1');
                                    $ticket->checkout_gate = $decrytArray->gate;
                                    $ticket->save();
                                    if ($ticket->reservation_id != '') {
                                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                        $diff_in_hours = $arrival_time->diffInRealHours($from);
                                        if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                            $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                        }
                                        $ticket['length'] = $diff_in_hours;
                                        $facility = Facility::find($ticket->facility_id);
                                        $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                        if ($rate['price'] != $ticket->grand_total) {
                                            $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                            return redirect('touchless-parking-prepaid-overstay-pay/' . base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                        } else {
                                            $ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_time = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');
                                            return redirect('touchless-parking-prepaid-checkout-success/' . base64_encode($ticket->ticket_number));
                                        }
                                    } else {
                                        if ($ticket->anet_transaction_id != '') {

                                            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                                $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            if ($rate['price'] != $ticket->grand_total) {
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('touchless-parking-overstay-pay/' . base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            } else {
                                                $ticket->is_checkout = '1';
                                                $ticket->checkout_gate = $decrytArray->gate;
                                                $ticket->checkout_datetime = date("Y-m-d H:i:s");
                                                $ticket->save();

                                                Session::forget('is_direct_checkout');
                                                Session::forget('email');
                                                Session::forget('phone');
                                                return redirect('touchless-parking-thankyou-checkout/' . base64_encode($ticket->ticket_number));
                                            }
                                        } else {
                                            return redirect('touchless-parking-checkin-details/' . base64_encode($ticket->ticket_number));
                                        }
                                    }
                                } else {
                                    return back()->with('danger', 'Sorry! You do not have any active checkin.');
                                }
                            } else {
                                return back()->with('danger', 'Please scan valid scan QR code.');
                            }
                        } else {
                            return back()->with('danger', "Please scan the right QR code.");
                        }
                    } else {
                        return Redirect::to('error-checkin');
                    }
                }
            } else {
                return back()->with('danger', 'Please scan valid scan QR code.');
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid scan QR code.');
        }
    }

    public function getPrepaidUserCheckin($ticket_number)
    {

        return view('checkin-checkout.prepaid-user-details', ['data' => $ticket_number]);
    }


    public function prepaidUserCheckinConfirm(Request $request)
    {
        /*$request = $request->all();
        dd($request);*/
        try {

            if (isset($request->encrypt)) {
                if ($request->encrypt) {
                    $ticket = Ticket::where('ticket_number', base64_decode($request->encrypt))->first();

                    $this->setCustomTimezone($ticket->facility_id);

                    $checkFacilityOwner = Facility::where('id', $ticket->facility_id)->first();
                    $existUser = User::where('email', $request->email)->where('created_by', $checkFacilityOwner->owner_id)->first();
                    if (!$existUser) {
                        $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
                        if ($geoLocation['geoplugin_countryCode'] == 'IN') {
                            $countryCode = "+91";
                        } elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
                            $countryCode = "+1";
                        } else {
                            $countryCode = "+1";
                        }
                        $user = User::where('phone', $countryCode . $request->phone)->first();
                        if (!$user) {
                            return back()->with('danger', 'User not associate with this phone. Please checkin again.');
                        }
                        $existUser = $user;
                    }
                    if ($existUser) {

                        $reservation = Reservation::where('user_id', $existUser->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->orderBy('id', 'Desc')->first();
                        if (count($reservation) > 0) {

                            $alreadyAssociateReservation = Ticket::where('reservation_id', $reservation->id)->where('is_checkout', '1')->first();
                            if ($alreadyAssociateReservation) {
                                return redirect('checkin-pay/' . base64_encode($ticket->ticket_number));
                            }

                            $start = date("Y-m-d H:i:s", strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp)->subMinutes(15)));

                            $end = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));

                            $d = date('Y-m-d H:i:s');
                            if (strtotime($d) >= strtotime($start) && strtotime($d) <= strtotime($end)) {
                                $ticket->reservation_id = $reservation->id;
                                //$ticket->checkin_time = $ticket->checkin_time;
                                $ticket->checkout_time = date('Y-m-d H:i:s');
                                $ticket->check_in_datetime = $reservation->start_timestamp;
                                $ticket->checkout_datetime = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                                $ticket->total = $reservation->total;
                                $ticket->grand_total = $reservation->total;
                                $ticket->length = $reservation->length;
                                $ticket->user_pass_id = $reservation->user_pass_id;
                                $ticket->save();

                                $reservation = Reservation::where('id', $ticket->reservation_id)->first();
                                $reservation->is_ticket = '1';
                                $reservation->save();

                                return redirect('prepaid-checkout-details/' . base64_encode($ticket->ticket_number));
                            } else {
                                return redirect('checkin-pay/' . base64_encode($ticket->ticket_number));
                            }
                        } else {
                            /*$geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
                                if($geoLocation['geoplugin_countryCode'] == 'IN'){
                                    $countryCode = "+91";
                                }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
                                    $countryCode = "+1";
                                }else{
                                    $countryCode = "+1";
                                }
                                $user = User::where('phone', $countryCode.$request->phone)->first();
                                if(!$user){
                                    return back()->with('danger', 'User not associate with this phone. Please checkin again.');        
                                }*/
                            /*$emailExist = User::where('email', $request->email)->where('created_by', $checkFacilityOwner->owner_id)->where('id','!=', $user->id)->first();
                                if($emailExist){
                                   return back()->with('danger', 'Email already associate with other user. Please checkin again.');         
                                }
                                $user->email = $request->email;
                                $user->save();*/
                            return redirect('checkin-pay/' . base64_encode($ticket->ticket_number));
                        }
                    } else {
                        return back()->with('danger', 'User not found.');
                    }
                } else {
                    return back()->with('danger', "Please scan the right QR code.");
                }
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid scan QR code.');
        }
    }


    public function getCheckinPayment($ticket_number)
    {
        $checkinData = Ticket::with('user')->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();

        if (count($checkinData) == 0) {
            $this->log->error("error on checkin screen");
            return Redirect::to('atlanta-error-checkin');
        }

        $this->setCustomTimezone($checkinData->facility_id);

        if ($this->checkFacilityAvailable($checkinData->facility_id) == false) {
            Redirect::to('error-facility');
        }
        if ($checkinData->anet_transaction_id != '') {
            return redirect('atlanta-thankyou-payment/' . base64_encode($checkinData->ticket_number));
        }
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
            $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
        }

        $facility = Facility::find($checkinData->facility_id);
        /** this function is used to get Availability Information for respective facility **/
        $rateData = $this->updateRateInformationWithAvailibilty($arrival_time, $diff_in_hours, $facility);

        $memberUser = User::with('memberUser')->where('id', $checkinData->user_id)->first();

        if ($memberUser->is_member == '1') {
            $isMember = 1;
            $checkinData['member_id'] = $memberUser->memberUser->member_id;
        } else {
            $isMember = 0;
            $checkinData['member_id'] = '';
        }
        $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE, $isMember);
        if ($rate == false) {
            return Redirect::to('error-facility');
        }
        $checkinData['length'] = 24;

        if ($rate == false) {
            return Redirect::to('error-facility');
        }

        //return current availabilit
        if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
            $rate['availability'] = $rateData['availability'];
        } else {
            $rate['availability'] = self::DEFAULT_VALUE;
        }
        if ($rate['price'] == 'N/A') {
            return Redirect::to('error-facility');
        }
        //returning  message as per availibility 
        $rate['availability_msg'] = '';
        $rate['availability_msg_some_space'] = '';
        if ((float)$rate['price'] >  self::DEFAULT_VALUE) {
            if ($rate['availability'] == self::DEFAULT_VALUE) {
                return Redirect::to('error-facility');
            }/*else if($rate['availability'] < self::MIN_AVAILABILITY)
        {
            return Redirect::to('error-facility');
        }*/ else {
                //return Redirect::to('error-facility');
            }
        } else if ($rate['price'] == 'N/A') {
            return Redirect::to('error-facility');
        } else if ($rate['availability'] == self::DEFAULT_VALUE) {
            return Redirect::to('error-facility');
        }
        $checkinData['rate'] = $rate;
        $this->log->info("user is on checkin screen on sms click with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('atlanta-checkin-checkout.payment-screen', ['data' => $checkinData]);
    }

    public function getPrepaidUserCheckout($key)
    {
        Session::put('is_sms_direct_checkout', '1');
        return view('atlanta-checkin-checkout.prepaid-user-checkout-details', ['data' => $key]);
    }


    public function prepaidUserCheckoutConfirm(Request $request)
    {

        try {
            if (isset($request->encrypt)) {
                $explode = $request->encrypt;
                $decrypt = base64_decode($explode);
                if ($decrypt) {

                    /*$request->phone = preg_replace('/\D+/', '', $request->phone);
                if(preg_match('/^[0-9]{10}+$/', $request->phone)){
                    if($request->phone == "0000000000"){
                        return back()->with('danger', "Please enter valid phone number.");    
                    }
                }else{
                    return back()->with('danger', "Please enter valid phone number.");
                }
                if(preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $request->email)){
                    
                }else{
                    return back()->with('danger', "Please enter valid email.");
                }*/

                    $decrytArray = json_decode($decrypt);
                    $this->checkFacilityAvailable($decrytArray->facility_id);

                    $this->setCustomTimezone($decrytArray->facility_id);

                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                    $checkinData = [];
                    if ($gate && isset($gate->gate_type)) {
                        if ($gate->gate_type == 'exit') {

                            $checkFacilityOwner = Facility::where('id', $decrytArray->facility_id)->first();
                            $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
                            if ($geoLocation['geoplugin_countryCode'] == 'IN') {
                                $countryCode = "+91";
                            } elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
                                $countryCode = "+1";
                            } else {
                                $countryCode = "+1";
                            }

                            $existUser = User::where('phone', $countryCode . $request->phone)->where('created_by', $checkFacilityOwner->owner_id)->first();
                            if ($existUser) {
                                $ticket = Ticket::where('user_id', $existUser->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                                if ($ticket) {
                                    Session::put('is_direct_checkout', '1');
                                    $ticket->checkout_gate = $decrytArray->gate;
                                    $ticket->save();
                                    if ($ticket->reservation_id != '') {
                                        /*$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            if($rate['price'] != $ticket->grand_total){
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('touchless-parking-prepaid-overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            }else{*/
                                        /*$ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_time = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');*/
                                        return redirect('touchless-parking-atlanta-prepaid-checkout-details/' . base64_encode($ticket->ticket_number));
                                        //}


                                    } else {
                                        if ($ticket->anet_transaction_id != '') {

                                            $facility = Facility::where('id', $decrytArray->facility_id)->first();
                                            if (!$facility) {
                                                return back()->with('danger', "Invalid garage.");
                                            }


                                            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                                                $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            /*if($rate['price'] != $ticket->grand_total){
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('touchless-parking-atlanta-overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            }else{*/


                                            //check third party gate API
                                            /* $gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                                            if($gateDetails){
                                                if($gateDetails->host !=''){
                                                    $params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate];
                                                    $this->log->info("Abacus Web Service : vehicle command aboout to send checkout exit facility {$facility->id} params ".json_encode($params));
                                                    $response = AbacusWebService::isvehicleAvailable($params, $gateDetails->host);
                                                    if($response['success'] == false){
                                                        return back()->with('danger', "Something wrong in parking.");
                                                    }
                                                    $this->log->info("Abacus Web Service : vehicle command complete checkout exit facility {$facility->id} response". json_encode($response));
                                                    if($response['data']['IsLoopVon'] == '1' || $response['data']['IsLoopVon'] == '0'){
                                                        $guid = AbacusWebService::createGuid();
                                                        $cmd_params = ['user'=>$gateDetails->username,'pwd'=>$gateDetails->password,'System'=>$gate->system,'Tcc'=>$gate->gate,'command'=>'45','iParameter1'=>'0','iParameter2'=>'0','iParameter3'=>'0','iParameter4'=>'0','textParameter1'=>'0','textParameter2'=>'0', 'guidParameter1'=>$guid, 'guidParameter2'=>$guid];
                                                        $this->log->info("Abacus Web Service : command run success checkout exit facility {$facility->id} params". json_encode($cmd_params));
                                                    $command_response = AbacusWebService::commandStatus($cmd_params, $gateDetails->host);
                                                    $this->log->info("Abacus Web Service Command 2 Response: checkin entry response". json_encode($command_response));
                                                    if($command_response['success'] == true){
                                                        if($command_response['data'][0] == "0" || $command_response['data'][0] == "1"){
                                                
                                                        }else{
                                                            return back()->with('danger', "Something wrong in parking.");
                                                        }
                                                        
                                                    }else{
                                                        return back()->with('danger', "Something wrong in parking.");
                                                    }
                                                    }else{
                                                        return back()->with('danger', "Sorry! Vehicle not present.");
                                                    }

                                                }
                                            }   */


                                            $ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_datetime = date("Y-m-d H:i:s");
                                            $ticket->save();


                                            $reservation = Reservation::where('id', $ticket->reservation_id)->first();
                                            $reservation->is_ticket = '2';
                                            $reservation->save();

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');
                                            return redirect('atlanta-thankyou-checkout/' . base64_encode($ticket->ticket_number));

                                            /*}*/
                                        } else {
                                            return redirect('touchless-parking-atlanta-checkin-details/' . base64_encode($ticket->ticket_number));
                                        }
                                    }
                                } else {
                                    return back()->with('danger', 'Sorry! You do not have any active checkin.');
                                }
                            } else {
                                return back()->with('danger', 'Please scan valid scan QR code.');
                            }
                        } else {
                            return back()->with('danger', 'Please scan valid scan QR code.');
                        }
                    }
                }
            }
        } catch (Exception $e) {
            return back()->with('danger', 'Please scan valid scan QR code.');
        }
    }

    public function saveEventsLogs($arrEvents)
    {

        UserEventsLog::create($arrEvents);
        return true;
    }


    public function getCheckinCheckoutReceipt($ticket_number)
    {
        $checkinData = Ticket::with(['transaction', 'reservation.transaction', 'userPass.transaction'])->where('ticket_number', $ticket_number)->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        return view('atlanta-checkin-checkout.checkin-checkout-receipt', ['data' => $checkinData]);
    }

    public function downloadReceipt($ticket_number)
    {
        $checkinData = Ticket::with(['transaction', 'reservation.transaction', 'userPass.transaction'])->where('ticket_number', $ticket_number)->first();
        if (count($checkinData) == 0) {
            return Redirect::to('error-checkin');
        }
        $html = view('atlanta-checkin-checkout.checkin-checkout-pdf', ['data' => $checkinData])->render();
        $image = app()->make(Pdf::class);
        $filename = $checkinData->ticket_number . ".pdf";
        header("Content-type:application/pdf");
        header('Content-Disposition: inline; filename="' . $filename . '"');
        return $this->respondWithPdf($image->getOutputFromHtmlString($html));
    }
    /*
    public function generateQrCode() 
    {
		$code = 'P'.rand(10, 99).rand(100,999).rand(100,999).'S';
		$isExist = User::where('qr_code_number', $code)->first();
		if($isExist){
		  $this->generateQrCode();
		}
		return $code;

    }
    */
}
