<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use Response;
use Hash;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Models\CustomText;
use App\Models\UserPass;
use Excel;
use App\Models\ParkEngage\RevenueBalance;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use App\Classes\ParkengageGateApi;
use Storage;
use App\Models\AuthorizeNetTransaction;
use App\Models\OauthClient;
use App\Models\ParkEngage\LicensePlate;
use App\Models\ParkEngage\TicketCitation;
use App\Models\ParkEngage\PartnerDevice;

class AutogateApiController extends Controller
{

    protected $log;
    protected $user;
    protected $partnerPaymentDetails;
    protected $authNet;
    protected $facility;
    protected $paymentProfileId;
    protected $cim;
    protected $licensePlateLog;

    const RESERVATION_THRESHOLD_TYPE = 2;
    const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE = 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO = 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR = 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "Sold Out At This Time.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->cim = $cim;
        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        $this->log = $logFactory->setPath('logs/parkengage/touchless-flow')->createLogger('new-touchless-flow');
        $this->licensePlateLog = $logFactory->setPath('logs/parkengage/license-plate')->createLogger('license-plate');
    }


    public function uploadLicensePlate(Request $request)
    {

        try {
            if ($request->header('X-ClientSecret') != '') {
                $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    $this->licensePlateLog->error("Error partner not found");
                    throw new NotFoundException('Invalid partner.');
                }
            }
            //$this->validate($request, ['image' => 'mimes:jpeg,png,jpg,svg'], ['image.mimes' => 'vehicle image must be a file of type : .jpeg, .png, .jpg' ]);    
            $this->log->info("license plate about to save");

            $image = $request->file('image');
            if ($image != '') {
                $file_extension = $image->getClientOriginalExtension();
                $file_name =  $request->gate_id . '_' . $request->license_plate . '_' . rand(1001, 9999) . '.' . $file_extension;
                $destination_path = storage_path("app/license-plate");
                $data['image'] = $file_name;
                if (!$image->move($destination_path, $file_name)) {
                    $this->licensePlateLog->error("image not uploaded");
                    throw new ApiGenericException("Something went wrong while upload image.");
                }
                $this->licensePlateLog->info("image uploaded");
            }

            LicensePlate::where("facility_id", $request->facility_id)->where("gate", $request->gate_id)->delete();

            $gate = Gate::where("facility_id", $request->facility_id)->where("gate", $request->gate_id)->first();
            if ($gate) {
                $data['gate_type'] = $gate->gate_type;
                $data['entry_time'] = date("Y-m-d H:i:s");
            }

            $data['license_plate'] = $request->license_plate;
            $data['partner_id'] = $secret->partner_id;
            $data['make'] = $request->make;
            $data['model'] = $request->model;
            $data['year'] = $request->year;
            $data['color'] = $request->color;
            $data['facility_id'] = $request->facility_id;
            $data['gate'] = $request->gate_id;
            /*if($request->header('X-FacilityID') != '')
            {
                $data['facility_id'] = $request->header('X-FacilityID');
            }*/
            LicensePlate::create($data);
            $this->licensePlateLog->info("License plate saved");
            return "License plate successfully updated.";
        } catch (\Exception $e) {
            $this->licensePlateLog->error($e);
            throw new ApiGenericException("License plate not saved.");
        }
    }

    public function validateLicensePlateCheckin(Request $request, $license_plate)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $ticket = Ticket::with(['user', 'citation'])->where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->first();
            if (!$ticket) {
                $ticket = [];
                $citation = TicketCitation::with('user')->where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->first();
                if (!$citation) {
                    throw new ApiGenericException('No vehicle plate number found.');
                }
                $citation->is_vehicle_present = '1';
                $citation->scan_date = date("Y-m-d H:i:s");
                $citation->save();
                $ticket['check_in_datetime'] = $citation->checkin_time;
                $ticket['checkout_datetime'] = $citation->checkout_time;
                $citation['check_in_datetime'] = $citation->checkin_time;
                $citation['checkout_datetime'] = $citation->checkout_time;
                //$ticket['is_overstay'] = '1';
                $ticket['is_autopay'] = '0';
                $ticket['citation'] = $citation;
                $now = date("Y-m-d") . "23:59:59";
                //dd($now, $citation->check_in_datetime);
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $citation->check_in_datetime);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                if ($diff_in_hours < 24) {
                    $diff_in_hours = 24;
                }

                $isMember = 0;
                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember);

                if ($rate == false) {
                    return Redirect::to('autogate-error-facility');
                }

                $days = ceil($diff_in_hours / 24);
                $processingFee = $days * $facility->citation_processing_fee;
                $taxFee = $days * $facility->tax_rate;

                $ticket['overstay_length'] = $diff_in_hours;
                $ticket['parking_amount'] = number_format($rate['price'], 2);
                $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                $ticket['is_overstay'] = '1';
                $ticket['is_vehicle_present'] = '1';
                $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                $citation['citation_processing_fee'] = number_format($processingFee, 2);
                $citation['tax_fee'] = number_format($taxFee, 2);
                $ticket['user'] = $citation->user;
                return $ticket;
            }

            $ticket->is_vehicle_present = '1';
            $ticket->scan_date = date("Y-m-d H:i:s");
            $ticket->save();
            if ($ticket->is_checkout == '1') {
                $now = date("Y-m-d H:i:s");
                if (strtotime(date("Y-m-d", strtotime($now))) > strtotime(date("Y-m-d", strtotime($ticket->checkout_datetime)))) {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                    if ($diff_in_hours < 24) {
                        $diff_in_hours = 24;
                    }

                    $isMember = 0;
                    $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember);

                    if ($rate == false) {
                        return Redirect::to('autogate-error-facility');
                    }

                    $days = ceil($diff_in_hours / 24);
                    $processingFee = $days * $facility->citation_processing_fee;
                    $taxFee = $days * $facility->tax_rate;

                    $ticket['overstay_length'] = $diff_in_hours;
                    $ticket['parking_amount'] = number_format($rate['price'], 2);
                    $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                    $ticket['is_overstay'] = '1';
                    $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                    $ticket['citation_processing_fee'] = number_format($processingFee, 2);
                    $ticket['tax_fee'] = number_format($taxFee, 2);
                    return $ticket;
                }
                return $ticket;
            }

            if ($ticket->is_checkin == '1' && $ticket->is_checkout == '0') {
                $now = date("Y-m-d H:i:s");
                if (strtotime($now) > strtotime($ticket->checkout_datetime)) {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                    if ($diff_in_hours < 24) {
                        $diff_in_hours = 24;
                    }

                    $isMember = 0;
                    $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember);

                    if ($rate == false) {
                        return Redirect::to('autogate-error-facility');
                    }

                    $days = ceil($diff_in_hours / 24);
                    $processingFee = $days * $facility->citation_processing_fee;
                    $taxFee = $days * $facility->tax_rate;

                    //$ticket['overstay_length'] = $diff_in_hours; 
                    $ticket['parking_amount'] = number_format($rate['price'], 2);
                    $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                    $ticket['is_overstay'] = '1';
                    $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                    $ticket['citation_processing_fee'] = number_format($processingFee, 2);
                    $ticket['tax_fee'] = number_format($taxFee, 2);
                    return $ticket;
                }
                return $ticket;
            }
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }

    public function markCitation(Request $request)
    {

        $this->validate($request, ['license_plate' => 'required'], ['license_plate.required' => 'License plate is required.']);

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
        }
        $data['violation'] = "NO PAYMENT";
        if ($request->ticket_number != '') {
            $ticket = Ticket::where("ticket_number", $request->ticket_number)->orderBy('id', 'DESC')->first();

            $data['ticket_id'] = $ticket->id;
            $data['user_id'] = $ticket->user_id;
            $data['facility_id'] = $ticket->facility_id;
            $data['partner_id'] = $ticket->partner_id;
            $data['violation'] = "OVERSTAY";
        }
        if ($request->header('X-FacilityID') != '') {
            $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
        } else {
            $facility = Facility::where('owner_id', $secret->partner_id)->first();
        }

        $now = date("Y-m-d H:i:s");
        $data['checkin_time'] = $now;
        $data['checkout_time'] = date("Y-m-d", strtotime($now)) . " 23:59:59";
        $data['estimated_checkout'] = date("Y-m-d", strtotime($now)) . " 23:59:59";
        $data['is_checkin'] = '1';
        $data['license_plate'] = $request->license_plate;
        $data['citation_number'] = $this->checkTicketNumber();
        $data['facility_id'] = $facility->id;
        $data['partner_id'] = $facility->owner_id;
        $data['is_vehicle_present'] = '1';
        $data['scan_date'] = $now;
        $data['updated_by'] = Auth::user()->id;

        $citation = TicketCitation::create($data);


        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours < 24) {
            $diff_in_hours = 24;
        }


        $isMember = 0;
        $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember);

        if ($rate == false) {
            throw new ApiGenericException('Invalid rate.');
        }
        $citation['rate'] = $rate['price'] + $facility->penalty_fee + $facility->citation_processing_fee + $facility->tax_rate;
        $citation['penalty_fee'] = $facility->penalty_fee;
        $citation['citation_processing_fee'] = $facility->citation_processing_fee;
        $citation['tax_fee'] = $facility->tax_rate;

        return $citation;
    }

    public function dashboard(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            $now = date("Y-m-d");
            $yesterday = date("Y-m-d", strtotime("-1 days"));
            $data['today_checkin'] = Ticket::whereDate('checkout_datetime', '=', $now)->where('facility_id', $facility->id)->count();
            $data['yesterday_checkin'] = Ticket::whereDate('checkout_datetime', '=', $yesterday)->where('facility_id', $facility->id)->count();
            $data['open_citation'] = TicketCitation::where('is_closed', '0')->where('facility_id', $facility->id)->count();
            $data['closed_citation'] = TicketCitation::where('is_closed', '1')->where('facility_id', $facility->id)->count();
            $data['checkin_vehicle_present'] = Ticket::where('is_vehicle_present', '0')->where('is_closed', '0')->where('facility_id', $facility->id)->count();
            $data['citation_vehicle_present'] = TicketCitation::where('is_vehicle_present', '0')->where('is_closed', '0')->where('facility_id', $facility->id)->count();
            return $data;
        }
    }


    public function citationList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            /*$today = date("Y-m-d");
            $yesterday = date("Y-m-d",strtotime("-1 days"));*/
            $data['open_citation'] = TicketCitation::/*whereDate('checkin_time', '=', $today)->*/with('user')->where('is_closed', '0')->where('facility_id', $facility->id)->orderBy("id", "DESC")->get();
            $data['closed_citation'] = TicketCitation::/*whereDate('checkin_time', '=', $yesterday)->*/with('user')->where('is_closed', '1')->where('facility_id', $facility->id)->orderBy("id", "DESC")->get();
            return $data;
        }
    }


    protected function checkTicketNumber()
    {
        $ticket = 'CI' . rand(100, 999) . rand(100, 999);
        $isExist = TicketCitation::where('citation_number', $ticket)->first();
        if ($isExist) {
            return $ticket = $this->checkTicketNumber();
        }
        return $ticket;
    }


    public function autopayOverstayPayment(Request $request)
    {

        $ticket = Ticket::where('ticket_number', $request->ticket_number)->orderBy('id', "DESC")->first();
        if (!$ticket) {
            throw new NotFoundException('Invalid ticket details.');
        }


        $this->user = User::find($ticket->user_id);
        $this->facility = Facility::find($ticket->facility_id);
        //$now = date("Y-m-d H:i:s");
        $now = date("Y-m-d") . " 23:59:59";
        if (strtotime(date("Y-m-d", strtotime($now))) == strtotime(date('Y-m-d', strtotime($ticket->checkout_datetime)))) {
            throw new ApiGenericException('Sorry! You do not in overstay time.');
        }
        $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime)))->addSeconds(1);
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours < 24) {
            $diff_in_hours = 24;
        }
        /** this function is used to get Availability Information for respective facility **/
        /*$rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);*/

        $isMember = 0;
        $rate = $this->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember);

        if ($rate == false) {
            throw new ApiGenericException('Rates not available for this garage.');
        }
        //return current availabilit
        if ($rate['price'] == 'N/A') {
            throw new ApiGenericException('Rates not available for this garage.');
        }

        $days = ceil($diff_in_hours / 24);
        $processingFee = $days * $this->facility->citation_processing_fee;
        $taxFee = $days * $this->facility->tax_rate;

        $total = $rate['price'] + $this->facility->penalty_fee + $processingFee + $taxFee;
        $customerProfile = $this->user->cim;
        if (!$customerProfile) {  // Create a cim for this user so they can store their details and reuse them
            throw new ApiGenericException('No customer payment profile found for this user.');
        }
        $is_partner = 0;
        if ($this->user->user_type == 3) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
            $is_partner = 1;
        } elseif ($this->user->user_type == 4 || $this->user->user_type == 5) {
            $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
            if ($this->partnerPaymentDetails) {
                $is_partner = 1;
            } else {
                throw new NotFoundException('No payment gateway details found for this partner.');
            }
        } else {
            $is_partner = 0;
        }
        $profiles = \DB::table('anet_payment_profiles')->where('cim_id', $customerProfile->id)->orderBy("id", "DESC")->first();
        if (!$profiles) {
            throw new ApiGenericException('No payment profile found for this user.');
        }
        $this->paymentProfileId = $profiles->payment_profile;
        $this->authNet
            ->setUser($this->user)
            ->isReservation()
            ->setFacility($this->facility)
            ->isPartner($this->partnerPaymentDetails)
            ->setBillingAddress($this->getBillingArray());
        $this->authNet->setPaymentProfile($this->paymentProfileId);

        try {
            $charge = $this->authNet->createTransaction(
                $total,
                "Overstay Autopay Payment {$ticket->id}, Ticket Number {$ticket->ticket_number}",
                config('icon.reservations_account')
            )->isPartner($this->partnerPaymentDetails)->executeTransaction();
        } catch (AuthorizeNetException $e) {
            throw new NotFoundException($e->getMessage());
        }

        if ($charge) {

            $transaction = $this->authNet->getTransaction();
            $overstay = new OverstayTicket();
            $overstay->ticket_id = $ticket->id;
            $overstay->user_id = $ticket->user_id;
            $overstay->facility_id = $this->facility->id;
            $overstay->length = $diff_in_hours;
            $overstay->total = $total;
            $overstay->ticket_number = $ticket->ticket_number;
            $overstay->is_checkin = '1';
            $overstay->is_checkout = '1';
            $overstay->check_in_datetime = date('Y-m-d H:i:s', strtotime($arrival_time));
            $overstay->checkout_datetime = date('Y-m-d H:i:s', strtotime($now));
            $overstay->anet_transaction_id = $transaction->id;
            $overstay->partner_id = $ticket->partner_id;
            $overstay->penalty_fee = $this->facility->penalty_fee;
            $overstay->processing_fee = $processingFee;
            $overstay->tax_fee = $taxFee;
            $overstay->save();

            $ticket->checkout_datetime = date('Y-m-d H:i:s', strtotime($now));
            $ticket->estimated_checkout = date('Y-m-d H:i:s', strtotime($now));
            $ticket->checkout_time = date('Y-m-d H:i:s', strtotime($now));
            $ticket->is_overstay = '0';
            $ticket->grand_total = $ticket->grand_total + $total;
            $ticket->save();

            Artisan::queue('email:touchless-parking-autogate-checkin-payment', array('id' => $overstay->id, 'type' => 'overstay'));

            return $overstay;
        } else {
            throw new NotFoundException("Amount should be greater than 0.");
        }
    }


    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }


    public function getNoOfDaysRate(Request $request)
    {
        $ticket = Ticket::where('ticket_number', $request->ticket_number)->orderBy('id', 'Desc')->first();
        $now = date("Y-m-d H:i:s");
        if (!$ticket) {
        }

        if ($request->days == '') {
            $zeroAmout = 0;
            $data['tax_fee'] = number_format($zeroAmout, 2);
            $data['parking_amount'] = number_format($zeroAmout, 2);
            $data['processing_fee'] = number_format($zeroAmout, 2);
            $data['rate'] = number_format($zeroAmout, 2);;
            $data['checkout_datetime'] = date("m/d/Y", strtotime($ticket->checkout_datetime)) . ' ' . date("g:i A", strtotime($ticket->checkout_datetime));
            return $data;
        }
        $now = date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime, strtotime("+1 days")));
        $arrival_time = Carbon::createFromFormat("Y-m-d H:i:s", date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime, strtotime("+1 days"))));
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($now . ' + ' . $request->days . ' days')));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours < 24) {
            $diff_in_hours = 24;
        }
        $isMember = 0;
        $facility =  Facility::find($ticket->facility_id);
        $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember);

        if ($rate == false) {
        }
        $data['tax_fee'] = $facility->tax_rate;
        $data['parking_amount'] = number_format($rate['price'], 2);
        $days = ceil($diff_in_hours / 24);
        $processingFee = $days * $facility->processing_fee;
        $data['processing_fee'] = number_format($processingFee, 2);
        $taxFee = $days * $facility->tax_rate;
        //$rate['price'] = $rate['price'] + $taxFee + $processingFee;        
        $data['rate'] = number_format($rate['price'] + $taxFee + $processingFee, 2);
        $data['checkout_datetime'] = date("m/d/Y", strtotime($from)) . ' ' . date("g:i A", strtotime($from));
        return $data;
    }


    public function checkoutList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $now = date("Y-m-d");
            $yesterday = date("Y-m-d", strtotime("-1 days"));
            $data['today_checkout'] = Ticket::with(['user'])->whereDate('checkout_datetime', '=', $now)->where('facility_id', $facility->id)->get();
            $data['yesterday_checkout'] = Ticket::with(['user'])->whereDate('checkout_datetime', '=', $yesterday)->where('facility_id', $facility->id)->get();
            return $data;
        }
    }

    public function unavailableVehicleList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $ticket = Ticket::with(['user'])/*->where('is_vehicle_present', '0')*/->where('is_closed', '0')->whereDate('scan_date', '!=', date("Y-m-d"))->where('facility_id', $facility->id)->get();
            $citaition = TicketCitation::with(['user'])/*->where('is_vehicle_present', '0')*/->where('is_closed', '0')->whereDate('scan_date', '!=', date("Y-m-d"))->where('facility_id', $facility->id)->get();
            $data = [];
            if (count($ticket) > 0) {
                foreach ($ticket as $key => $value) {
                    $data[] = $value;
                }
            }
            if (count($citaition) > 0) {
                foreach ($citaition as $key => $value) {
                    $data[] = $value;
                }
            }
            if (!$data) {
                return  $ticket;
            }

            return $data;
        }
    }

    public function citationUpdate(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            $ticket = TicketCitation::where('citation_number', $request->citation_number)->where('facility_id', $facility->id)->first();
            if (!$ticket) {
                throw new ApiGenericException('Invalid citation.');
            }
            $ticket->comment = $request->comment;
            $result = $ticket->save();
            $ticket->updated_by = Auth::user()->id;
            $ticket->delete();
            return "Citation deleted successfully.";
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }


    public function unavailableVehicleUpdate(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            if ($request->all == '1') {
                Ticket::where("is_vehicle_present", '0')->update(["is_closed" => "1", "remark" => $request->comment, "closed_date" => date("Y-m-d H:i:s")]);
                TicketCitation::where("is_vehicle_present", '0')->update(["is_closed" => "1", "comment" => $request->comment, "closed_date" => date("Y-m-d H:i:s")]);
                return "All open checkin/citation closed successfully.";
            } else {
                if ($request->type == 'checkin') {
                    $ticket = Ticket::where('ticket_number', $request->ticket_number)->where('facility_id', $facility->id)->first();
                    if (!$ticket) {
                        throw new ApiGenericException('Invalid ticket.');
                    }
                    $ticket->remark = $request->comment;
                    $ticket->is_closed = '1';
                    $ticket->closed_date = date("Y-m-d H:i:s");
                    $ticket->updated_by = Auth::user()->id;
                    $ticket->save();
                    //$ticket->delete();
                    return "Ticket closed successfully.";
                } elseif ($request->type == 'citation') {
                    $ticket = TicketCitation::where('citation_number', $request->citation_number)->where('facility_id', $facility->id)->first();
                    if (!$ticket) {
                        throw new ApiGenericException('Invalid citation.');
                    }
                    $ticket->comment = $request->comment;
                    $ticket->is_closed = '1';
                    $ticket->closed_date = date("Y-m-d H:i:s");
                    $ticket->updated_by = Auth::user()->id;
                    $result = $ticket->save();
                    //$ticket->delete();
                    return "Citation closed successfully.";
                } else {
                    throw new ApiGenericException('Invalid type of ticket/citation.');
                }
            }
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }

    public function getPartnerFacility(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            $facilities = \DB::table('facilities')
                ->select('id as id', 'full_name as name')
                ->where('owner_id', $secret->partner_id)
                ->where('active', '=', '1')
                ->get();
            return $facilities;
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }


    public function reopenStatusUpdate(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            if ($request->citation_number != '') {
                $ticket = TicketCitation::where('citation_number', $request->citation_number)->where('facility_id', $facility->id)->first();
                $msg = "Ticket ";
            }
            if ($request->ticket_number != '') {
                $ticket = Ticket::where('ticket_number', $request->ticket_number)->where('facility_id', $facility->id)->first();
                $msg = "Citation ";
            }

            if (!$ticket) {
                throw new ApiGenericException('Invalid citation.');
            }
            //$ticket->comment = $request->comment;
            $ticket->is_closed = '0';
            $ticket->scan_date = date("Y-m-d H:i:s");
            $ticket->updated_by = Auth::user()->id;
            $result = $ticket->save();
            return $msg . "reopen successfully.";
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }


    public function validatePartnerDevice(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            $device = PartnerDevice::where('partner_id', $secret->partner_id);
            if ($request->header('X-FacilityID') != '') {
                $device->where('facility_id', $request->header('X-FacilityID'));
            }
            $device = $device->get();
            $facilityExist = Facility::find($request->header('X-FacilityID'));
            if ($facilityExist->app_installation > 0) {

                $deviceExist = PartnerDevice::where('facility_id', $request->header('X-FacilityID'))->where('serial_number', $request->serial_number)->first();
                if ($deviceExist) {
                    return "Valid device.";
                }

                if (count($device) == $facilityExist->app_installation) {
                    throw new ApiGenericException('The maximum number of allocated Enforcement App licenses has been exhausted. Please reach out to ParkEngage support to increase the licenses.');
                }

                $device = new PartnerDevice();
                $device->serial_number = $request->serial_number;
                $device->partner_id = $secret->partner_id;
                $device->facility_id = $facilityExist->id;
                $device->device = $request->device;
                $device->save();
            } else {
                throw new ApiGenericException('The maximum number of allocated Enforcement App licenses has been exhausted. Please reach out to ParkEngage support to increase the licenses.');
            }
            /*if(!$device){
                throw new ApiGenericException('Device not found under this partner.');        
            }*/
            return "Valid device.";
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }


    public function todayClosedVehicleList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $ticket = Ticket::with(['user'])->where('is_closed', '1')->whereDate('closed_date', '=', date("Y-m-d"))->where('facility_id', $facility->id)->get();
            $citaition = TicketCitation::with(['user'])->where('is_closed', '1')->whereDate('closed_date', '=', date("Y-m-d"))->where('facility_id', $facility->id)->get();
            $data = [];
            if (count($ticket) > 0) {
                foreach ($ticket as $key => $value) {
                    $data[] = $value;
                }
            }
            if (count($citaition) > 0) {
                foreach ($citaition as $key => $value) {
                    $data[] = $value;
                }
            }
            if (!$data) {
                return  $ticket;
            }

            return $data;
        }
    }

    public function getMapcoFailedRecord()
    {
        $var = '"email":null';
        $result = \DB::select("SELECT count(ip), ip, event_name, error_message, created_at FROM inventory_modules.user_events_log where event_name like '%MAPCO - RESPONSE PAGE - PAYMENT SUCCESS%' and error_message like '%" . $var . "%' group by ip");

        if (count($result) > 0) {
            $increment1 = 1;
            foreach ($result as $val) {
                $finalCodes1[] = [
                    'No.' => $increment1,
                    'IP' => $val->ip,
                    'Event Name' => $val->event_name,
                    'Error Message' => $val->error_message,
                    'Created Date' => date("m/d/Y, h:i A", strtotime($val->created_at))
                ];

                $increment1++;
            }

            Excel::create(
                "Error Logs",
                function ($excel) use ($finalCodes1) {

                    // Set the spreadsheet title, creator, and description
                    $excel->setTitle("Error Logs");
                    $excel->setCreator('Error Logs')->setCompany('ParkEngage');
                    $excel->setDescription('Error Logs');

                    // Build the spreadsheet, passing in the payments array
                    if (empty($finalCodes1)) {
                        throw new ApiGenericException('Sorry! No Data Found.');
                    } else {
                        if (isset($finalCodes1) && !empty($finalCodes1)) {
                            $excel->sheet(
                                'Eror Logs',
                                function ($sheet) use ($finalCodes1) {
                                    $sheet->fromArray($finalCodes1, null, 'A1', false, true);
                                }
                            );
                        }
                    }
                }
            )->store('xls')->download('xls');
        }
        return $result;
    }

    public function clearLicensePlate(Request $request)
    {


        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('Invalid partner.');
            }
        }
        LicensePlate::where("facility_id", $request->facility_id)->where("gate", $request->gate_id)->delete();
        return "License plate successfully cleared.";
    }
}
