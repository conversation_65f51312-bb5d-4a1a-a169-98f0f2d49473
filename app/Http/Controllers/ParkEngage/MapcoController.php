<?php

namespace App\Http\Controllers\ParkEngage;

use App\Classes\CommonFunctions;
use Hash;
use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Exception;
use Artisan;
use Carbon\Carbon;
use App\Models\Event;
use App\Models\Rate;
use App\Models\OauthClient;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use DB;
use Mail;
use App\Services\Pdf;
use App\Models\User;
use App\Models\Facility;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\Reservation;
use Illuminate\Support\Facades\Storage;
use Response;
use App\Models\ParkEngage\EventCategory;
use App\Models\ParkEngage\EventCategoryEvent;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\AuthorizeNetTransaction;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\Gate;
use App\Models\Ticket;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Classes\ParkengageGateApi;
use Excel;
use App\Models\Otp;
use App\Services\Image;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\RevenueBalance;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Models\OverstayTicket;
use App\Models\UserPass;
use App\Models\Promotion;
use App\Classes\PromoCodeLib;
use App\Http\Helpers\QueryBuilder;
use App\Models\ParkEngage\MapcoQrcodeCheckin;
use App\Models\ParkEngage\MapcoBeforeQrcode;
use App\Models\PermitVehicle;
use App\Models\PermitRequest;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\PlatformNotification;
use App\Models\ParkEngage\Cruise;
use App\Models\ParkEngage\CruiseSchedule;
use App\Models\ParkEngage\TicketExtend;
use App\Jobs\SendSms;
use App\Models\ParkEngage\DatacapTransaction;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Fluent;
use App\Http\Helpers\GatewayHelper;
use App\Jobs\ManageBookingHistory\SendBookingExcelEmail;

class MapcoController extends Controller
{
	protected $log;

	const RESERVATION_THRESHOLD_TYPE = 2;
	const TYPE_ALL = 0;
	const TYPE_CHECKIN = 1;
	const TYPE_CHECKOUT = 2;
	const TYPE_DRIVEUP = 3;
	const EVENT_THRESHOLD_TYPE = 1;

	const USERTYPE = 10;
	const BUSINESS_CLERK = 8;
	const SUPERADMIN = 1;
	const SUBORDINATE = 4;
	const PARTNER = 3;
	const REGIONAL_MANAGER = 12;
	const EVENT_ID = array(35);

	use DispatchesJobs;
	const QUEUE_NAME = 'sms-send';
	const BOOKING_HISTORY_QUEUE_NAME = 'booking-history-email-send';

	public function __construct(Request $request, LoggerFactory $logFactory)
	{
		$this->request = $request;
		$this->log = $logFactory->setPath('logs/parkengage/mapco')->createLogger('mapco');
	}

	public function index(Request $request)
	{
		if ($request->header('X-ClientSecret') != '' && $request->event_type) {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();

			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}

			#PIMS: 11296
			#Kuldeep
			$request->facility_id = (array) $request->facility_id;

			#DD: pims-11406/11459(CS-439) 
			if (isset($request->all_event) && $request->all_event == "1" && isset($request->campus_id) && !empty($request->campus_id)) {
				$facilityIds = Facility::where('neighborhood_id', $request->campus_id)->where('owner_id', $secret->partner_id)
					->pluck('id')
					->toArray();
				$request->facility_id = $facilityIds;
			}
			$today = date("Y-m-d");
			if ($request->event_type == 'event') {
				$allevents = [];
				//$event = Event::with(['eventCategoryEvent.eventCategory', 'facilities'])->where('partner_id', $secret->partner_id)/*->whereDate("start_time", "<=", $today)*/->whereDate("end_time", ">=", $today)->orderBy('start_time', 'ASC')->where('is_active', '1')->get();
				$event = Event::with(['eventCategoryEvent.eventCategory', 'facilities', 'facilities.FacilityPaymentDetails', 'eventFacility'])->where('partner_id', $secret->partner_id)/*->whereDate("start_time", "<=", $today)*/->whereDate("end_time", ">=", $today)->where('is_active', '1')->orderBy('start_time', 'ASC');

				if ($request->facility_id) {
					$event = $event->WhereHas(
						'eventFacility',
						function ($query) use ($request) {
							$query->whereIn('facility_id', $request->facility_id);
						}
					);
				}
				$event = $event->get();

				foreach ($event as $events) {
					#DD: pims-11406/11459(CS-439) 
					if ($request->facility_id && isset($request->all_event) && $request->all_event == "1") {
						$matchedFacilities = [];
						$unmatchedFacilities = [];
						$facilitiesArray = $events->facilities->toArray();

						foreach ($events->facilities as $facility) {
							if (in_array($facility->id, $request->facility_id)) {
								$matchedFacilities[] = $facility; // Facilities matching request
							} else {
								$unmatchedFacilities[] = $facility; // Other facilities
							}
						}

						$facilitiesArray = array_merge($matchedFacilities, $unmatchedFacilities);
						if (count($facilitiesArray) > 0) {
							unset($events->facilities);
						}
						$events->facilities = collect($facilitiesArray); // Reassign as a collection
					}
					$month =  date('m', strtotime($events->start_time));
					$monthName = date('M', strtotime($events->start_time));
					$yearName = date('Y', strtotime($events->start_time));
					//changes for USM specific done by vikrant after discuss by trapti mam
					if ($events->partner_id == config('parkengage.PARTNER_USM')) {

						if (!isset($events->eventCategoryEvent)) {
							if (isset($events->parking_start_time) && !empty($events->parking_start_time) && isset($events->parking_end_time) && !empty($events->parking_end_time)) {
								$events->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($events->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($events->parking_end_time));
							} else {
								$events->parking_time_message = '';
							}
							$allevents[$monthName . "-" . $yearName][] = $events;
						} else {
							if (isset($events->eventCategoryEvent->event_category_id)) {
								if ($events->event_rate > 0) {
									if (isset($events->parking_start_time) && !empty($events->parking_start_time) && isset($events->parking_end_time) && !empty($events->parking_end_time)) {
										$events->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($events->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($events->parking_end_time));
									} else {
										$events->parking_time_message = '';
									}
									$allevents[$monthName . "-" . $yearName][] = $events;
								} else {
									$rateEventCategoryExist = Rate::where("event_category_id", $events->eventCategoryEvent->event_category_id)->first();
									if ($rateEventCategoryExist) {
									} else {
										if (isset($events->parking_start_time) && !empty($events->parking_start_time) && isset($events->parking_end_time) && !empty($events->parking_end_time)) {
											$events->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($events->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($events->parking_end_time));
										} else {
											$events->parking_time_message = '';
										}
										$allevents[$monthName . "-" . $yearName][] = $events;
									}
								}
							}
						}
					} else {

						if ($request->facility_id) {
							$facilityConfig = FacilityConfiguration::select("booking_duration_hours")->whereIn("facility_id", $request->facility_id)->first();
							if ($facilityConfig) {
								$events->booking_duration_hours =   $facilityConfig->booking_duration_hours;
							}
						}
						if (isset($events->parking_start_time) && !empty($events->parking_start_time) && isset($events->parking_end_time) && !empty($events->parking_end_time)) {
							$events->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($events->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($events->parking_end_time));
						} else {
							$events->parking_time_message = '';
						}
						$allevents[$monthName . "-" . $yearName][] = $events;
					}
				}
				if (count($allevents) < 1) {
					throw new ApiGenericException('Event not found');
				}
				return $allevents;
			}
			if ($request->event_type == 'bundle-pass') {
				$allrates = [];
				$rate = Rate::with(['eventCategory.eventCategoryEvent.event', 'event' => function ($query) use ($today) {
					$query->whereDate("start_time", ">=", $today);
				}])->whereIn('facility_id', $request->facility_id)->where('id', $request->pass_id)->orderBy('start_date', 'ASC')->first();
				$new = [];
				foreach ($rate->eventCategory->eventCategoryEvent as $value) {
					if ($value->event) {
						if ($value->event->is_active == '1') {
							if ($value->event->start_time >= $today) {
								if (isset($value->event->parking_start_time) && !empty($value->event->parking_start_time) && isset($value->event->parking_end_time) && !empty($value->event->parking_end_time)) {
									$value->event->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($value->event->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($value->event->parking_end_time));
								} else {
									$value->event->parking_time_message = '';
								}
								$new[] = $value->event;
							}
						}
					}
				}
				for ($j = 0; $j < count($new); $j++) {
					for ($i = 0; $i < count($new) - 1; $i++) {
						if (strtotime($new[$i]['start_time']) > strtotime($new[$i + 1]['start_time'])) {
							$temp = $new[$i + 1];
							$new[$i + 1] = $new[$i];
							$new[$i] = $temp;
						}
					}
				}
				$rate['all_event'] = $new;
				return $rate;
			}

			if ($request->event_type == 'party-pass') {
				$allrates = [];
				$rate = Rate::with(['eventCategory.eventCategoryEvent.event', 'event' => function ($query) use ($today) {
					$query->whereDate("start_time", ">=", $today);
				}])->whereIn('facility_id', $request->facility_id)->where('id', $request->pass_id)->first();
				$new = [];
				if ($rate->eventCategory) {
					foreach ($rate->eventCategory->eventCategoryEvent as $value) {
						if ($value->event) {
							if ($value->event->is_active == '1') {
								if ($value->event->start_time >= $today) {
									if (isset($value->event->parking_start_time) && !empty($value->event->parking_start_time) && isset($value->event->parking_end_time) && !empty($value->event->parking_end_time)) {
										$value->event->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($value->event->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($value->event->parking_end_time));
									} else {
										$value->event->parking_time_message = '';
									}
									$new[] = $value->event;
								}
							}
						}
					}
					for ($j = 0; $j < count($new); $j++) {
						for ($i = 0; $i < count($new) - 1; $i++) {

							if (strtotime($new[$i]['start_time']) > strtotime($new[$i + 1]['start_time'])) {
								$temp = $new[$i + 1];
								$new[$i + 1] = $new[$i];
								$new[$i] = $temp;
							}
						}
					}
				}
				if ($rate->event) {
					$new[] = $rate->event;
					$rate['all_event'] = $new;
				}
				$rate['all_event'] = $new;
				return $rate;
			}
			if ($request->event_type == 'simple-pass') {
				$allrates = [];
				$rate = Rate::with(['event' => function ($query) use ($today) {
					$query->whereDate("start_time", ">=", $today);
				}])->whereIn('facility_id', $request->facility_id)->where('id', $request->pass_id)->first();
				$new = [];
				$new[] = $rate->event;
				$rate['all_event'] = $new;
				return $rate;
			}
		}
	}

	// Bundle pass   for testing
	public function getBundlePassEvent(Request $request)
	{
		if ($request->header('X-ClientSecret') != '' && $request->event_type) {

			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();

			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}


			if ($request->event_type == 'event') {
				$allevents = [];
				$event = Rate::with('eventCategory', 'event')->where('facility_id', $request->facility_id)->get();
				foreach ($event as $events) {

					$month =  date('m', strtotime($events->start_time));
					$monthName = date('M', strtotime($events->start_time));
					$allevents[$monthName][] = $events;
				}
				return $allevents;
			}


			if ($request->event_type == 'bundle-pass') {
				/*$allrates = [];
                $rate = Rate::with(['eventCategory','event'])->where('facility_id',$request->facility_id)->where('event_category_id','!=',Null)->get();
              
                foreach($rate as $rates){

                     
                      $monthName = date('M',strtotime($rates->start_date));
                      $allrates[$monthName][]= $rates;
                   
                    //$allrates[$monthName];

                }
                 return $allrates;*/

				// test for send email
				$id = '714';
				// $data = \DB::table('mapco_qrcodes')
				//    ->join('reservations as rev','rev.id','=','mapco_qrcodes.reservation_id') 
				//    ->join('events','events.id','=','mapco_qrcodes.event_id')  
				//    ->join('event_categories','event_categories.id','=','mapco_qrcodes.event_category_id')  
				//    ->join('users','users.id','=','reservations.user_id')        
				//    ->select('mapco_qrcodes.*','reservations.user_id','reservations.facility_id','reservations.rate_id','reservations.total','reservations.user_pass_id','reservations.partner_id','reservations.created_at','users.name','users.email')
				//    ->where('mapco_qrcodes.reservation_id', $id)
				//    ->where('mapco_qrcodes.deleted_at', Null)
				//    ->get();
				//    return $data;

				$data = ['email' => '<EMAIL>'];
				try {
					$status = Mail::send(
						"mapco.test",
						['data' => $data],
						function ($message) use ($data) {
							$message->to('<EMAIL>')->subject("Mapco Reservation Booking Details : ");
							$message->from(config('parkengage.default_sender_email'));
						}
					);
					//$this->log->info("Mail sent to ".$data->email);
				} catch (Exception $e) {
					//$msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
					//$this->log->error($msg);
					// $this->log->info("Queue ended");            
				}
			}
		}
	}


	public function getAllPass(Request $request)
	{

		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}
			#DD: pims-11406/11459(CS-439) 

			$request->facility_id = (array) $request->facility_id;
			if (isset($request->all_event) && $request->all_event == "1" && isset($request->campus_id) && !empty($request->campus_id)) {
				$facilityIds = Facility::where('neighborhood_id', $request->campus_id)->where('owner_id', $secret->partner_id)
					->pluck('id')
					->toArray();
				$request->facility_id = $facilityIds;
			}
			$startDate = date("Y-m-d");
			$endDate = date("Y-m-d");
			$today = date("Y-m-d");
			if (isset($request->start_date) && isset($request->end_date)) {
				$startDate = $request->start_date;
				$endDate = $request->end_date;
			}
			if ($secret->partner_id != config('parkengage.PARTNER_MAPCO')) {
				$rate = Rate::with(['rateType', 'category', 'eventCategory', 'event', 'facility.facilityPaymentDetails'])
					->whereIn("facility_id", $request->facility_id)
					->whereDate("start_date", "<=", $startDate)->whereDate("end_date", ">=", $endDate)
					->where("active", '1')->where("is_device_specific_rate", '0')
					->where("rate_type_id", '7')->get();
			} else {
				$rate = Rate::with(['rateType', 'category', 'eventCategory', 'event', 'facility.facilityPaymentDetails'])->whereDate("start_date", "<=", $today)->whereDate("end_date", ">=", $today)->whereIn("facility_id", $request->facility_id)->where("active", '1')->where("is_device_specific_rate", '0')->get();
			}

			foreach ($rate as $key => $value) {
				$reservation = Reservation::where("rate_id", $value->id)->whereNull("cancelled_at")->count();
				if ($reservation >= $value->no_of_pass) {
					$value['is_pass_sold'] = '1';
				} else {
					$value['is_pass_sold'] = '0';
				}
			}
			return $rate;
		}
	}

	public function postSuccess(Request $request)
	{
		$today = date('Y-m-d');
		$this->log->info("Planet Payment Success call Back1 :" . json_encode($request->all()));
		if ($this->request->Amount > 0) {
			if ($this->request->TokenNo == '' || $this->request->AuthorisationCode == '') {
				throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
			}

			$alreadyTransactionDone  = AuthorizeNetTransaction::where('ref_id', $this->request->ref)->first();

			if ((count($alreadyTransactionDone) == 0) && isset($this->request->TxID) && !empty($this->request->TxID)) {
				$alreadyTransactionDone  = AuthorizeNetTransaction::where('anet_trans_id', $this->request->TxID)->first();
			}

			if (count($alreadyTransactionDone) > 0) {
				$message = "Booking already done by the user.";
				return ["message" => $message];
			}

			$details = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->where('mer_reference', $this->request->ref)->first();
			$user = User::where('id', $details['user_id'])->first();
			$checkFacility = Facility::where('id', $details['facility_id'])->first();
			$this->facility = $checkFacility;
			$beforeData = MapcoBeforeQrcode::where('reservation_id', $details['id'])->where('mer_reference', $this->request->ref)->first();
			//return $beforeData;
			$this->log->info("post success 2 :");
			if (($this->request->TokenNo != '') && isset($details) && !empty($details)) {

				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $user->id;
				$authorized_anet_transaction->total = $details->total;
				$authorized_anet_transaction->description = "Mapco Booking Payment Done User : " . $user->id;
				$authorized_anet_transaction->card_type = $this->request->CardType;
				$authorized_anet_transaction->ref_id = $this->request->ref;
				$authorized_anet_transaction->anet_trans_id = $this->request->TxID;
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->payment_last_four = isset($this->request->card_pan_last4digits) ? $this->request->card_pan_last4digits : '0';
				$authorized_anet_transaction->auth_code = isset($this->request->AuthorisationCode) ? $this->request->AuthorisationCode : '0';
				$authorized_anet_transaction->save();

				$details->anet_transaction_id = $authorized_anet_transaction->id;
				$details->save();

				$user->session_id = $this->request->TokenNo;
				$user->save();
				//planet card save code
				if ($details->user_consent == '1') {
					$cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $details->user_id)->where('card_last_four', $this->request->card_pan_last4digits)->first();
					if ($cardCheck) {
						$this->log->info("Card Already Added for this user");
					} else {
						$planetData['user_id'] = $details->user_id;
						$planetData['name'] = isset($user->name) ? $user->name : '';
						$planetData['partner_id'] = $details->partner_id;
						$planetData['card_last_four'] = isset($this->request->card_pan_last4digits) ? $this->request->card_pan_last4digits : '0';
						$planetData['card_type'] = isset($this->request->CardType) ? $this->request->CardType : '0';
						$planetData['card_name'] = isset($this->request->CardTypeName) ? $this->request->CardTypeName : '0';
						$planetData['expiry'] = $details->ex_month . $details->ex_year;
						$planetData['token'] = isset($this->request->TokenNo) ? $this->request->TokenNo : '0';
						$planetData['tx_state'] = isset($this->request->TxState) ? $this->request->TxState : '0';
						$planetData['currency_used'] = isset($this->request->CurrencyCode) ? $this->request->CurrencyCode : '0';

						$result = PlanetPaymentProfile::create($planetData);
						//return $result;
					}
				}

				//  QrCode Generation Start
				$this->log->info("post success 2 : event type" . $beforeData->event_type);
				if ($beforeData->event_type == 'event') {
					$data['reservation_id'] = $details['id'];
					$data['event_id'] = $beforeData->event_id ?  $beforeData->event_id : '';
					$data['event_category_id'] = $beforeData->event_category_id ?  $beforeData->event_category_id : '';
					$data['qrcode'] = $this->checkQrCode();
					$data['total_usage'] = '1';
					$data['remain_usage'] = '1';
					MapcoQrcode::create($data);
				}

				if ($beforeData->event_type == 'bundle-pass') {
					try {
						$rate = Rate::find($beforeData->rate_id);
						if ($rate->is_full_season == '0') {
							$eventIds = $beforeData->event_ids;
							$eventIds = explode(",", $eventIds);
							$eventIds = array_slice($eventIds, 0, 18);
							$eventHours = 0;
							$selectedEvent = Event::whereIn('id', $eventIds)->whereDate('start_time', '>=', $today)->where('is_active', '1')->get();
							for ($j = 0; $j < count($selectedEvent); $j++) {
								for ($i = 0; $i < count($selectedEvent) - 1; $i++) {
									if (strtotime($selectedEvent[$i]['start_time']) >= strtotime($today)) {
										if (strtotime($selectedEvent[$i]['start_time']) > strtotime($selectedEvent[$i + 1]['start_time'])) {
											$temp = $selectedEvent[$i + 1];
											$selectedEvent[$i + 1] = $selectedEvent[$i];
											$selectedEvent[$i] = $temp;
										}
									}
								}
							}
							if (count($selectedEvent) > 0) {

								foreach ($selectedEvent as $key => $value) {
									$data['reservation_id'] = $details['id'];
									$data['event_id'] = $value->id;
									$data['event_category_id'] = $beforeData->event_category_id ?  $beforeData->event_category_id : '';
									$data['qrcode'] = $this->checkQrCode();
									$data['total_usage'] = '1';
									$data['remain_usage'] = '1';
									MapcoQrcode::create($data);
									$currentEvent = Event::find($value->id);
									$eventHours += $currentEvent->base_event_hours;
								}

								//save event start date end date in reservation after issue raised by fufa
								$startEvent = Event::find($selectedEvent[0]->id);
								$lastEventKey = count($selectedEvent) - 1;
								$endEvent = Event::find($selectedEvent[$lastEventKey]->id);
								$resStartAt = $startEvent->start_time;
								$resEndAt = $endEvent->end_time;
								$reservation = Reservation::find($details['id']);
								$reservation->start_timestamp = $resStartAt;
								$reservation->end_timestamp = $resEndAt;
								$reservation->length = $eventHours;
								$reservation->save();
							}
						} else {
							$this->log->info("post success 2 : bundle type");
							$eventCategory = EventCategoryEvent::where('event_category_id', $beforeData->event_category_id)->get();
							$eventIds = [];
							foreach ($eventCategory as $key => $value) {
								$eventIds[] = $value->event_id;
							}
							$selectedEvent = Event::whereIn('id', $eventIds)->whereDate('start_time', '>=', $today)->where('is_active', '1')->get();
							for ($j = 0; $j < count($selectedEvent); $j++) {
								for ($i = 0; $i < count($selectedEvent) - 1; $i++) {
									if (strtotime($selectedEvent[$i]['start_time']) >= strtotime($today)) {
										if (strtotime($selectedEvent[$i]['start_time']) > strtotime($selectedEvent[$i + 1]['start_time'])) {
											$temp = $selectedEvent[$i + 1];
											$selectedEvent[$i + 1] = $selectedEvent[$i];
											$selectedEvent[$i] = $temp;
										}
									}
								}
							}
							$eventHours = 0;
							foreach ($selectedEvent as $key => $value) {

								$data['reservation_id'] = $details['id'];
								$data['event_id'] = $value->id;
								$data['event_category_id'] = $beforeData->event_category_id ?  $beforeData->event_category_id : '';
								$data['qrcode'] = $this->checkQrCode();
								$data['total_usage'] = '1';
								$data['remain_usage'] = '1';
								$this->log->info("save event : bundle type" . json_encode($data));
								MapcoQrcode::create($data);
								$currentEvent = Event::find($value->id);
								$eventHours += $currentEvent->base_event_hours;
							}

							//save event start date end date in reservation after issue raised by fufa
							$startEvent = Event::find($selectedEvent[0]->id);
							$lastEventKey = count($selectedEvent) - 1;
							$endEvent = Event::find($selectedEvent[$lastEventKey]->id);
							$resStartAt = $startEvent->start_time;
							$resEndAt = $endEvent->end_time;
							$reservation = Reservation::find($details['id']);
							$reservation->start_timestamp = $resStartAt;
							$reservation->end_timestamp = $resEndAt;
							$reservation->length = $eventHours;
							$reservation->save();
						}
					} catch (\Exception $e) {
						$this->log->error("save event : bundle type --" . $e->getMessage() . '---' . $e->getFile() . '---' . $e->getLine());
						throw $e;
					}
				}
				if ($beforeData->event_type == 'party-pass') {
					$rate = Rate::find($beforeData->rate_id);
					$data['reservation_id'] = $details['id'];
					$data['event_id'] = '';
					$data['event_category_id'] = $beforeData->event_category_id ?  $beforeData->event_category_id : '';
					$data['qrcode'] = $this->checkQrCode();
					$data['total_usage'] = $rate->total_usage;
					$data['remain_usage'] = $rate->total_usage;
					MapcoQrcode::create($data);
				}
				if ($beforeData->event_type == 'simple') {
					$data['reservation_id'] = $details['id'];
					$data['event_id'] = $beforeData->event_id ?  $beforeData->event_id : '';
					$data['event_category_id'] = $beforeData->event_category_id ?  $beforeData->event_category_id : '';
					$data['qrcode'] = $this->checkQrCode();
					MapcoQrcode::create($data);
				}

				// QrCode Generation End 

				// Email  Send Code Start
				$qrcode = MapcoQrcode::with(['event', 'eventCategory.eventCategoryEvent.event'])->where('reservation_id', $details['id'])->get();
				//send QR code to user			
				$this->log->info("Check Mapco qrcode count");
				if (count($qrcode) > 0) {
					$this->log->info("Mapco Email & SMS Send Start");
					Artisan::queue('mapco:reservation-email', array('id' => $details['id']));
					$smsTotal = number_format($details['total'], 2);
					$msg = "Thank you for booking your parking with " .  ucwords($checkFacility->full_name) . ".\nBooking # : " . $details['ticketech_code'] . " \nAmount Charged : $$smsTotal";
					$this->customeReplySms($msg, $user->phone);
					$this->log->info("Mapco Email & SMS Send Done");
				}
				//  Email Send Code End		  
			} else {
				$this->log->info("Booking Not Found");
			}
		}
		return 1;
	}


	public function postFail(Request $request)
	{
		$this->log->info("Planet Payment Fail call Back :" . json_encode($request->all()));
		$details = Reservation::where('mer_reference', $this->request->ref)->delete();
		return 1;
	}

	protected function checkReferenceNumber()
	{
		$reference = rand(1000, 9999) . rand(10000000, 99999999);
		$resRefrenceExist = Reservation::where("mer_reference", $reference)->first();
		if ($resRefrenceExist) {
			$this->checkReferenceNumber();
		}
		return $reference;
	}

	public function planetPaymentSession(Request $request)
	{
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Invalid garage.');
		}
		$price = 0;
		if ($request->rate_id != '') {
			$rate = Rate::find($request->rate_id);
			if (!$rate) {
				throw new ApiGenericException('Rate does not match in our database.');
			}
			$tax_rate = $facility->processing_fee * $rate->total_usage;
			$price = $rate->price + $tax_rate;
		} else {
			if ($request->event_id != '') {
				$event = Event::find($request->event_id);
				if (!$event) {
					throw new ApiGenericException('Event does not match in our database.');
				}
				if ($event->event_rate == '' || $event->event_rate == '0.00') {
					$price = $facility->base_event_rate;
				} else {
					$price = $event->event_rate;
				}
			}
			if ($price > 0) {
				$tax_rate = (float) $facility->processing_fee;
				$price = $price + $tax_rate;
			} else {
				$tax_rate = (float) 0.00;
				$price = "0.00";
			}
		}
		$request->request->add(['amount' => $price]);
		$request->request->add(['tax_rate' => $tax_rate]);
		$discountedAmount = 0;
		if (isset($request->promocode)) {
			$this->validate($request, Promotion::$checkPromoValidationRulesThirdParty);
			$response = PromoCodeLib::validatePromoCodeThirdParty($request);
			//dd($response);
			if (isset($response->getData()->is_promocode_valid) == '1') {
				if ($request->amount < $response->getData()->discount_in_dollar) {
					$data1 = $response->getData();
					//$data1->discount_in_dollar = $request->amount;
					// $amount = $request->amount;
					$amount = 0;
					$discountedAmount = $response->getData()->discount_in_dollar;
					$response->setData($data1);
					//return $response;
				} else {
					$amount = $price - $response->getData()->discount_in_dollar;
					$discountedAmount = $response->getData()->discount_in_dollar;
				}
			}
		} else {
			$amount = $price;
		}

		if ($request->amount == 0 || $request->amount < 0) {
			$amount = 0;
		} else {
			//$amount = number_format($amount, 2);      
			$amount = $amount;
		}
		$posturl_success = config('parkengage.MAPCO_PLANET_POST_SUCCESS_URL');
		$posturl_fail = config('parkengage.MAPCO_PLANET_POST_FAIL_URL');
		$payment_success = config('parkengage.MAPCO_PLANET_SUCCESS_URL');
		$payment_fail = config('parkengage.MAPCO_PLANET_FAIL_URL');

		$security_emerchant_id = config('parkengage.MAPCO_PLANET_MERCHANT_ID');
		$security_validation_code = config('parkengage.MAPCO_PLANET_VALIDATION_CODE');
		$template_id = config('parkengage.MAPCO_PLANET_TEMPLATE_ID');

		$userDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();

		$cent = $amount * 100;
		// $reference = rand(1000, 9999).rand(1000, 9999).rand(1000, 9999);
		$reference = $this->checkReferenceNumber();
		$params = [
			'security_emerchant_id' => "$security_emerchant_id",
			'security_validation_code' => "$security_validation_code",
			'template_id' => "$template_id",
			'trx_amount_currency_code' => "USD",
			'trx_amount_value' => "$cent",
			'trx_options' => "G",
			'posturl_success' => "$posturl_success",
			'posturl_failure' => "$posturl_fail",
			'redirect_approved' => "$payment_success",
			'redirect_declined' => "$payment_fail",
			'trx_merchant_reference' => "$reference",
			'service_action' => "pay"
		];

		$url = config('parkengage.MAPCO_PLANET_SESSION_URL');
		$ch = curl_init($url);

		$headers = array(
			"Content-Type: application/json"
		);
		$this->log->info("Planet Params Request:" . json_encode($params));
		$this->log->info("Planet URL:" . json_encode($url));
		$this->log->info("Planet Headers Request:" . json_encode($headers));
		curl_setopt($ch, CURLOPT_HEADER, false);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

		curl_setopt($ch, CURLOPT_POST, 1);
		if ($params !== null) {
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
		}
		$response = curl_exec($ch);
		$this->log->info("Planet Session API Response :" . json_encode($response));
		$result['success'] = true;
		$explode = explode(",", json_decode($response, TRUE));
		$ipgSession = explode(":", $explode[0]);
		if (!isset($ipgSession[1])) {
			throw new ApiGenericException('There is some issue with payment gateway.');
		}
		$new = ltrim($ipgSession[1], '"');
		$new = rtrim($new, '"');

		if ($new == 'null') {
			throw new ApiGenericException('There is some issue with payment gateway.');
		}

		$charid = strtoupper(md5(uniqid(rand(), true)));
		$hyphen = chr(45);
		$uuid = chr(123)
			. substr($charid, 0, 8) . $hyphen
			. substr($charid, 8, 4) . $hyphen
			. substr($charid, 12, 4) . $hyphen
			. substr($charid, 16, 4) . $hyphen
			. substr($charid, 20, 12)
			. chr(125);
		$data['ipgSession'] =  $new;
		$data['guid'] =  $uuid;
		$data['api_url'] =  config('parkengage.MAPCO_PLANET_PAYMENT_URL');
		$data['tax_rate'] =  $tax_rate;
		$data['amount'] =  (float) $amount;
		$data['discounted_amount'] =  (float) $discountedAmount;
		$data['reference'] =  $reference;



		return $data;
	}


	public function makePaymentSuccess(Request $request)
	{


		$today = date('Y-m-d');

		$this->log->info("MapCo payment request received : " . json_encode($request->all()));
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}

			$checkFacility = Facility::where('id', $request->facility_id)->where('owner_id', $secret->partner_id)->first();
			if (!$checkFacility) {
				throw new NotFoundException('No garage found with this partner.');
			}
			$this->facility = $checkFacility;
			if ($checkFacility->active != '1') {
				throw new ApiGenericException('Garage is not available for any booking');
			}
			if ($checkFacility->is_available != '1') {
				throw new ApiGenericException('Garage is not available for any booking');
			}
			if ($checkFacility->facility_booking_type == '1') {
				throw new ApiGenericException('Garage is not available for any booking');
			}
			if (!$checkFacility->accept_cc) {
				throw new ApiGenericException("Garage does not accept credit cards");
			}


			if ($this->request->total > 0) {
				if ($this->request->TokenNo == '' || $this->request->AuthorisationCode == '') {
					throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
				}

				$alreadyTransactionDone  = AuthorizeNetTransaction::where('ref_id', $this->request->MerchantRef)->first();
				if (count($alreadyTransactionDone) == 0) {
					$alreadyTransactionDone  = AuthorizeNetTransaction::where('anet_trans_id', $this->request->TxID)->first();
				}
				if (count($alreadyTransactionDone) > 0) {
					$message = "Booking already done by the user.";
					return ["message" => $message];
				}
			}
			if ($this->request->arrival == '') {
				throw new ApiGenericException("Event start date can not empty.");
			}

			if ($this->request->phone != '') {
				/*
		$geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
        if($geoLocation['geoplugin_countryCode'] == 'IN'){
              $this->countryCode = "+91";
        }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
              $this->countryCode = "+1";
        }else{
              $this->countryCode = "+1";
        }
		*/
				$this->countryCode = "+1";
				$existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $checkFacility->owner_id)->first();
				if ($existPhone) {
					if ($this->request->email != '') {
						$existPhone->email = $this->request->email;
					}
					$existPhone->name = $this->request->name;
					$existPhone->phone = $this->countryCode . $this->request->phone;
					$existPhone->save();
					$this->user = $existPhone;
				} else {

					$this->user = User::create(
						[
							'name' => $this->request->name,
							'email' => $this->request->email,
							'phone' => $this->countryCode . $this->request->phone,
							'password' => Hash::make(str_random(60)),
							'anon' => true,
							'user_type' => '5',
							'created_by' => $checkFacility->owner_id
						]
					);
				}

				$details = $this->makeReservation();


				if ($details) {
					if ($this->request->event_type == 'event') {
						$data['reservation_id'] = $details['id'];
						$data['event_id'] = $this->request->event_id ?  $this->request->event_id : '';
						$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
						$data['qrcode'] = $this->checkQrCode();
						$data['total_usage'] = '1';
						$data['remain_usage'] = '1';
						MapcoQrcode::create($data);
					}
					if ($this->request->event_type == 'bundle-pass') {
						$eventIds = $this->request->event_ids;
						try {
							$rate = Rate::find($this->request->rate_id);
							if ($rate->is_full_season == '0') {

								$selectedEvent = Event::whereIn('id', $eventIds)->whereDate('start_time', '>=', $today)->where('is_active', '1')->get();
								for ($j = 0; $j < count($selectedEvent); $j++) {
									for ($i = 0; $i < count($selectedEvent) - 1; $i++) {

										if (strtotime($selectedEvent[$i]['start_time']) >= strtotime($today)) {
											if (strtotime($selectedEvent[$i]['start_time']) > strtotime($selectedEvent[$i + 1]['start_time'])) {
												$temp = $selectedEvent[$i + 1];
												$selectedEvent[$i + 1] = $selectedEvent[$i];
												$selectedEvent[$i] = $temp;
											}
										}
									}
								}
								foreach ($selectedEvent as $key => $value) {
									$data['reservation_id'] = $details['id'];
									$data['event_id'] = $value->id;
									$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
									$data['qrcode'] = $this->checkQrCode();
									$data['total_usage'] = '1';
									$data['remain_usage'] = '1';
									MapcoQrcode::create($data);
								}
							} else {
								$eventCategory = EventCategoryEvent::where('event_category_id', $this->request->event_category_id)->get();
								$eventIds = [];
								foreach ($eventCategory as $key => $value) {
									$eventIds[] = $value->event_id;
								}
								$selectedEvent = Event::whereIn('id', $eventIds)->whereDate('start_time', '>=', $today)->where('is_active', '1')->get();
								for ($j = 0; $j < count($selectedEvent); $j++) {
									for ($i = 0; $i < count($selectedEvent) - 1; $i++) {
										if (strtotime($selectedEvent[$i]['start_time']) >= strtotime($today)) {
											if (strtotime($selectedEvent[$i]['start_time']) > strtotime($selectedEvent[$i + 1]['start_time'])) {
												$temp = $selectedEvent[$i + 1];
												$selectedEvent[$i + 1] = $selectedEvent[$i];
												$selectedEvent[$i] = $temp;
											}
										}
									}
								}
								foreach ($selectedEvent as $key => $value) {
									$data['reservation_id'] = $details['id'];
									$data['event_id'] = $value->id;
									$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
									$data['qrcode'] = $this->checkQrCode();
									$data['total_usage'] = '1';
									$data['remain_usage'] = '1';
									MapcoQrcode::create($data);
								}
							}
						} catch (\Exception $e) {
							throw $e;
						}
					}
					if ($this->request->event_type == 'party-pass') {
						$rate = Rate::find($this->request->rate_id);
						$data['reservation_id'] = $details['id'];
						$data['event_id'] = '';
						$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
						$data['qrcode'] = $this->checkQrCode();
						$data['total_usage'] = $rate->total_usage;
						$data['remain_usage'] = $rate->total_usage;
						MapcoQrcode::create($data);
					}
					if ($this->request->event_type == 'simple') {
						$data['reservation_id'] = $details['id'];
						$data['event_id'] = $this->request->event_id ?  $this->request->event_id : '';
						$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
						$data['qrcode'] = $this->checkQrCode();
						MapcoQrcode::create($data);
					}
					$qrcode = MapcoQrcode::with(['event', 'eventCategory.eventCategoryEvent.event'])->where('reservation_id', $details['id'])->get();
					if ($this->request->event_type == 'party-pass') {
						foreach ($qrcode as $key => $val) {
							$val->parking_time_message = '*Event parking starts 3 hours before the Event starts, & Ends 1 hour before the Event ends.';
						}
					}
					foreach ($qrcode as $key => $val) {
						if (isset($val->event->parking_start_time) && !empty($val->event->parking_start_time) && isset($val->event->parking_end_time) && !empty($val->event->parking_end_time)) {
							$val->event->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($val->event->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($val->event->parking_end_time));
						}
					}

					$details['mapco_qrcode'] = $qrcode;

					if (isset($details['rate_id'])) {
						$rateDetails = Rate::where('id', $details['rate_id'])->first();
						$details['rates'] = $rateDetails;
					} else {
						$details['rates'] = [];
					}
					//send QR code to user
					if (count($qrcode) > 0) {
						Artisan::queue('mapco:reservation-email', array('id' => $details['id']));
						$smsTotal = number_format($details['total'], 2);
						$msg = "Thank you for booking your parking with " . ucwords($checkFacility->full_name) . ".\nBooking # : " . $details['ticketech_code'] . " \nAmount Charged : $$smsTotal";
						$this->customeReplySms($msg, $this->user->phone);
					}

					if ($this->request->event_id != '') {
						$event = Event::find($this->request->event_id);
						if ($event) {
							$details['actual_amount'] = number_format(($event->event_rate + $this->facility->processing_fee) - $this->request->total, 2);
						}
					}
					if ($this->request->rate_id != '') {
						$rate = Rate::find($this->request->rate_id);
						if ($rate) {
							$tax_rate = $this->facility->processing_fee * $rate->total_usage;
							$details['actual_amount'] = number_format(($rate->price + $tax_rate) - $this->request->total, 2);
						}
					}
					return $details;
				} else {
					throw new ApiGenericException("Error in booking.");
				}
			} else {
				throw new ApiGenericException("Please enter valid phone number.");
			}
		}
	}

	protected function checkQrCode()
	{
		$code = 'MA' . rand(10, 99) . rand(100, 999) . rand(100, 999);
		$isExist = MapcoQrcode::where('qrcode', $code)->first();
		if ($isExist) {
			$this->checkQrCode();
		}
		return $code;
	}


	protected function makeReservation()
	{
		$reservation = $this->saveReservation();
		//check if the total amount is 0
		if ($this->request->total > 0) {
			$reservation->ticketech_guid = rand(100000, 999999);
			//$reservation->partner_id = $this->user->user_type == '3'? $this->user->id : $this->user->created_by;            
			$reservation->save();
		} else {
			//throw new ApiGenericException("Amount can not be 0.");
		}
		if ($this->request->total > 0) {
			if ($this->request->TokenNo != '') {
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $this->user->id;
				$authorized_anet_transaction->total = $this->request->total;
				$authorized_anet_transaction->description = "Mapco Booking Payment Done User : " . $this->user->id;
				$authorized_anet_transaction->card_type = $this->request->CardType;
				$authorized_anet_transaction->ref_id = isset($this->request->reference) ? $this->request->reference : '';
				$authorized_anet_transaction->anet_trans_id = $this->request->TxID;
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->payment_last_four = isset($this->request->CardNumberLast4) ? $this->request->CardNumberLast4 : '0';
				$authorized_anet_transaction->auth_code = isset($this->request->AuthorisationCode) ? $this->request->AuthorisationCode : '0';
				$authorized_anet_transaction->save();
				$reservation->anet_transaction_id = $authorized_anet_transaction->id;

				$reservation->save();

				$this->user->session_id = $this->request->TokenNo;
				$this->user->save();
			}
		}
		// 100 % promocode apply case
		if ($this->request->is_booking_direct == '1') {
			$authorized_anet_transaction = new AuthorizeNetTransaction();
			$authorized_anet_transaction->sent = '1';
			$authorized_anet_transaction->user_id = $this->user->id;
			$authorized_anet_transaction->total = $this->request->total;
			$authorized_anet_transaction->description = "Mapco Booking Using Promocode Done User : " . $this->user->id;
			$authorized_anet_transaction->ref_id = isset($this->request->reference) ? $this->request->reference : '';
			$authorized_anet_transaction->save();
			$reservation->anet_transaction_id = $authorized_anet_transaction->id;
			$reservation->save();
		}

		// Send email to user

		$this->log->info("MapCo Booking successfull : " . $reservation->id);
		return $reservation->withRelations();
		/*return [
        'reservation' => $reservation->withRelations(),
        'ticketech_guid' => isset($ticketech_guid) ? $ticketech_guid : ''
    ];*/
	}

	/**
	 * Save the processed reservation
	 *
	 * @return [type] [description]
	 */
	protected function saveReservation(): Reservation
	{
		$reservation = new Reservation(
			[
				'user_id' => $this->user->id,
				'facility_id' => $this->facility->id,
				'start_timestamp' => $this->request->arrival,
				'length' => $this->request->length ? $this->request->length : '24',
				'discount' => 0.00,
				'credit_used' => 0.00,
				'company_affilate_id' => 0.00,
				'partner_id' => $this->facility->owner_id,
				'ticketech_code' => $this->checkReservationNumber()
			]
		);
		//for processing fee
		if ($this->facility->processing_fee > 0) {
			$reservation->processing_fee = $this->facility->processing_fee;
		}

		/*if (is_a($rate, Rate::class)) {
        $reservation->rate_id = $rate->id;
    }

    if (is_array($rate) && isset($rate['id'])) {
        $reservation->rate_id = $rate['id'];
    }*/

		// If this is higher the apply bonus will get double applied
		$reservation->total = $this->request->total;
		$reservation->rate_id = isset($this->request->rate_id) ? $this->request->rate_id : '';
		$reservation->payment_gateway = 'planet';
		$reservation->mer_reference = isset($this->request->reference) ? $this->request->reference : '';
		$reservation->ex_month = isset($this->request->ex_month) ? $this->request->ex_month : '';
		$reservation->ex_year = isset($this->request->ex_year) ? $this->request->ex_year : '';
		$reservation->user_consent = (isset($this->request->user_consent) && $this->request->user_consent == true) ? 1 : 0;
		$reservation->save();
		return $reservation;
	}

	public function downloadQrCode($reservation_id)
	{

		$reservation = Reservation::with('transaction', 'facility', 'facility.facilityConfiguration', 'user', 'rate', 'mapcoQrCode.event', 'mapcoQrCode.eventCategory.eventCategoryEvent.event')->where('id', $reservation_id)->first();
		if (!$reservation) {
			throw new ApiGenericException("Invalid booking details.");
		}
		$cruise = Cruise::select('id', 'cruise_name')->where('partner_id', $reservation->partner_id)->where('id', $reservation->cruise_id)->first();
		if ($cruise) {
			$reservation->cruise_name = isset($cruise->cruise_name) ? $cruise->cruise_name : '';
			//CruiseSchedule
			$cruiseSchedule = CruiseSchedule::where('cruise_id', $cruise->id)->where('is_active', 1)->whereNull('deleted_at')->first();
			$reservation->cruiseSchedule = isset($cruiseSchedule) ? $cruiseSchedule : '';
		}

		if (isset($reservation->mapcoQrCode) && count($reservation->mapcoQrCode) > 0) {
			$pdf = (new MapcoQrcode())->generatePdf($reservation, Pdf::class);
		} else {

			// $template = PlatformNotification::with('notificationType')->where('slug_name', 'reservation')->first();
			//$reservation->template = $template;
			$brand_setting = FacilityBrandSetting::where('facility_id', $reservation->facility->id)->first();
			if ($brand_setting) {
				$reservation->facility_logo_id = ($brand_setting->id) ? $brand_setting->id : '';
				$rgb_color = json_decode($brand_setting->rgb_color, true);
				$background_color = 'rgba(' . $rgb_color['r'] . ',' . $rgb_color['g'] . ',' . $rgb_color['b'] . ',' . $rgb_color['a'] . ')';
				$brand_setting->rgb_color = $background_color;
				$reservation->brandSetting = $brand_setting;
				$reservation->brandSetting->rgb_color = $background_color;
			} else {
				$brand_setting = BrandSetting::where('user_id', $reservation->partner_id)->first();
				$reservation->logo_id = $brand_setting->id;
				$rgb_color = json_decode($brand_setting->rgb_color, true);
				$background_color = 'rgba(' . $rgb_color['r'] . ',' . $rgb_color['g'] . ',' . $rgb_color['b'] . ',' . $rgb_color['a'] . ')';
				$brand_setting->rgb_color = $background_color;
				$reservation->brandSetting = $brand_setting;
				$reservation->brandSetting->rgb_color = $background_color;
			}
			$reservation->message = "Booking ID";
			$reservation->addressLink = $reservation->facility->generateAddressLink();
			$reservation->hours = $reservation->facility->hoursOfOperation;

			$imageBarcodeFileName = str_random(10) . '_common.png';
			$barcode = "";
			if (isset($reservation->thirdparty_code) && $reservation->thirdparty_code != '') {
				$barcode = $reservation->thirdparty_code;
			} else {
				$barcode = $reservation->ticketech_code;
			}
			if (isset($reservation->on_behalf) && !empty($reservation->on_behalf)) {
				$email = User::where('id', $reservation->on_behalf)->pluck('email')->first();
				$reservation->email_on_behalf = $email;
			}
			// Mobile number format
			$phone = QueryBuilder::formatPhoneNumber(substr($reservation->user->phone, -10), false);
			$reservation->user->phone = $phone;

			Storage::put($imageBarcodeFileName, $this->generateBarcodeJpgNew($barcode));
			$reservation->updated_qrcode = $imageBarcodeFileName;
			$pdf = (new MapcoQrcode())->generateEmailPdf($reservation, Pdf::class);
		}

		return $pdf;
	}

	public function showQrCodeImage($photo)
	{
		if (!$photo) {
			throw new NotFoundException('No image with that name found.');
		}

		$file = Storage::disk('local')->get($photo) ?: null;
		// create response and add encoded image data
		$response = Response::make($file);
		// getting content type e.g image/jpeg
		$file_extension = mime_content_type(storage_path('app/' . $photo));
		// set content-type
		$response->header('Content-Type', $file_extension);
		// output
		return $response;
	}

	public function mapcoTicketCheckinCheckout(Request $request)
	{
		$this->log->info("mapcoTicketCheckinCheckout -- " . json_encode($request->all()));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Sorry,Invalid garage.');
		}
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();

		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}

		if (substr($request->ticket_id, 0, 2) === 'MA') {
		} else {
			throw new ApiGenericException('Sorry, Invalid QR Code.');
		}
		$this->log->info("11");
		if ($request->ticket_id === 'MAPCO10224') {
			if (date("Y-m-d") > "2024-02-10") {
				throw new ApiGenericException('Sorry, Qr code expired.');
			}
			if (isset($gate) && $gate->gate_type == "entry") {
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				$facilityName = ucwords($facility->full_name);
				$data = ['msg' => "Welcome to $facilityName."];
				return $data;
			} elseif (isset($gate) && $gate->gate_type == "exit") {
				$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				$data = ['msg' => "Thank you for visiting " . $facilityName . "."];
				return $data;
			} else {
				throw new ApiGenericException('Sorry, Invalid QR Code.');
			}
		}

		//check gate api
		if ($facility->check_vehicle_enabled == '1') {
			$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}
		}
		$this->log->info("12");
		if (isset($gate) && $gate->gate_type == "entry") {
			$this->log->info("13");
			$qrCode = MapcoQrcode::with(['reservation.user', 'event', 'eventCategory.eventCategoryEvent.event'])->where("qrcode", $request->ticket_id)->first();
			if (!$qrCode) {
				throw new ApiGenericException('Sorry, No prepaid booking found.');
			} else {
				if ($qrCode->remain_usage == '0' || $qrCode->remain_usage == 0) {
					throw new ApiGenericException('Sorry, You have already consumed your ticket.');
				}
				if (!isset($qrCode->reservation)) {
					throw new ApiGenericException('Sorry, No prepaid booking found.');
				}
				if ($qrCode->reservation->cancelled_at != '') {
					throw new ApiGenericException('Sorry, Your booking has been canceled.');
				}
				$this->log->info("14");
				$today = date("Y-m-d");
				if ($qrCode->event_id != '0') {
					$event = Event::whereDate("start_time", '<=', $today)->whereDate("end_time", '>=', $today)->where('id', $qrCode->event_id)->first();
					if (!$event) {
						throw new ApiGenericException('Sorry, No event found.');
					}
					$event_id = $qrCode->event_id;
				}
				if ($qrCode->event_id == '0' && ($qrCode->event_category_id != '0' || $qrCode->event_category_id != '')) {
					$this->log->info("16");
					//$today = date("Y-m-d");
					//$today = "2022-12-07";
					$eventCategoryEvent = EventCategoryEvent::leftJoin('events', 'events.id', '=', 'event_category_events.event_id')
						->select('events.id', 'events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode->event_category_id)->first();
					if (!isset($eventCategoryEvent->start_time)) {
						throw new ApiGenericException('Sorry, No event found.');
					}
					$event_id = $eventCategoryEvent->id;
				}

				$currentEvent = Event::find($event_id);

				$user = $qrCode->reservation->user;
				$this->log->info("17");

				$data['user_id'] = $user->id;
				$data['checkin_gate'] = $request->gate_id;
				$data['facility_id'] = $request->facility_id;
				$data['is_checkin'] = 1;
				$data['ticket_number'] = $this->checkTicketNumber();
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['ticket_security_code'] = rand(1000, 9999);
				$data['partner_id'] = $facility->owner_id;
				$data['reservation_id'] = $qrCode->reservation_id;
				$this->log->info("18");
				//$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
				$data['check_in_datetime'] = $currentEvent->start_time;
				$data['checkout_datetime'] = $currentEvent->end_time;
				$data['estimated_checkout'] = $currentEvent->end_time;
				$data['total'] = "0.00";
				$data['grand_total'] = "0.00";
				$data['length'] = $currentEvent->base_event_hours;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['event_id'] = $event_id;
				$data['device_type'] = "IM30";
				$this->log->info("19");
				$result = Ticket::create($data);
				$this->log->info("20");
				$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
				$qrCode->save();

				//$reservation->is_ticket = '1';
				//$reservation->save();            

			}

			$facilityName = ucwords($facility->full_name);
			$this->log->info("21");
			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$this->log->info("22");
			Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			//$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
			//$this->customeReplySms($msg, $user->phone);

			$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");



			//$data = ['msg' => "Welcome to $facilityName. #$result->ticket_number."];
			$data = ['msg' => "Welcome to $facilityName."];
			return $data;
		} elseif (isset($gate) && $gate->gate_type == "exit") {
			$qrCode = MapcoQrcode::with(['reservation.user', 'event', 'eventCategory.eventCategoryEvent.event'])->where("qrcode", $request->ticket_id)->first();
			if (!$qrCode) {
				throw new ApiGenericException('Sorry, No prepaid booking found.');
			}

			if ($qrCode->reservation->cancelled_at != '') {
				throw new ApiGenericException('Sorry, Your booking has been canceled.');
			}
			if ($qrCode->total_usage > 1) {

				$ticket = Ticket::where('reservation_id', $qrCode->reservation->id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
				if (!$ticket) {

					if ($qrCode->remain_usage == 0) {
						throw new ApiGenericException('Sorry, You have consumed your ticket.');
					}
					$event_id = '';
					if ($qrCode->event_id == '0' && ($qrCode->event_category_id != '0' || $qrCode->event_category_id != '')) {
						$today = date("Y-m-d");
						$eventCategoryEvent = EventCategoryEvent::leftJoin('events', 'events.id', '=', 'event_category_events.event_id')
							->select('events.id', 'events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode->event_category_id)->first();
						if (!isset($eventCategoryEvent->start_time)) {
							throw new ApiGenericException('Sorry, No event found.');
						}
						$event_id = $eventCategoryEvent->id;
					}

					$ticket = Ticket::where('reservation_id', $qrCode->reservation->id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
					if (!$ticket) {
						if ($qrCode->remain_usage == 0) {
							throw new ApiGenericException('Sorry, You have consumed your ticket.');
						}

						$eventData = Event::find($event_id);
						if (!$eventData) {
							throw new ApiGenericException('Sorry, Invalid event associate with this ticket.');
						}
						$user = $qrCode->reservation->user;

						$data['user_id'] = $user->id;
						$data['checkout_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkout'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['reservation_id'] = $qrCode->reservation_id;
						//$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
						$data['check_in_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->start_time));
						$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
						$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
						$data['total'] = "0.00";
						$data['grand_total'] = "0.00";
						$data['length'] = $eventData->base_event_hours;
						//$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['checkout_time'] = date('Y-m-d H:i:s');
						$data['event_id'] = $eventData->id;
						$data['checkout_without_checkin'] = '1';
						//dd($data);
						$result = Ticket::create($data);

						$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
						$qrCode->save();

						$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
						$msg = "Thank you for visiting " . $facilityName . ".";

						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						$this->customeReplySms($msg, $user->phone);

						$data = ['msg' => $msg];
						return $data;
					}
				}
			} elseif ($qrCode->total_usage == 1) {

				if ($qrCode->remain_usage == 1) {

					$today = date("Y-m-d");
					if ($qrCode->event_id != '0') {
						$event = Event::whereDate("start_time", '<=', $today)->whereDate("end_time", '>=', $today)->where('id', $qrCode->event_id)->first();
						if (!$event) {
							throw new ApiGenericException('Sorry, No event found.');
						}
					}
					$ticket = Ticket::where('reservation_id', $qrCode->reservation->id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
					if (!$ticket) {
						if ($qrCode->remain_usage == 0) {
							throw new ApiGenericException('Sorry, You have consumed your ticket.');
						}
						$user = $qrCode->reservation->user;

						$data['user_id'] = $user->id;
						$data['checkout_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkout'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['reservation_id'] = $qrCode->reservation_id;
						//$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
						$data['check_in_datetime'] = date("Y-m-d H:i:s", strtotime($qrCode->event->start_time));
						$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($qrCode->event->end_time));
						$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($qrCode->event->end_time));
						$data['total'] = "0.00";
						$data['grand_total'] = "0.00";
						$data['length'] = $qrCode->event->base_event_hours;
						//$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['checkout_time'] = date('Y-m-d H:i:s');
						$data['event_id'] = $qrCode->event_id;
						$data['checkout_without_checkin'] = '1';
						//dd($data);
						$result = Ticket::create($data);

						$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
						$qrCode->save();

						$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
						$msg = "Thank you for visiting " . $facilityName . ".";

						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						$this->customeReplySms($msg, $user->phone);

						$data = ['msg' => $msg];
						return $data;
					} else {
					}
				}
			}

			$ticket = Ticket::where('reservation_id', $qrCode->reservation->id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
			if (!$ticket) {
				throw new ApiGenericException('Sorry, No checkin found against booking.');
			}
			if ($ticket->is_checkout == '1') {
				throw new ApiGenericException('Sorry, You have already checked out.');
			}

			if ($qrCode->event_id != '0') {
				if ($ticket->event_id != $qrCode->event_id) {
					throw new ApiGenericException('Sorry, Please scan valid QR code.');
				}
			}

			$ticket->is_checkout = '1';
			$ticket->checkout_gate = $request->gate_id;
			$ticket->checkout_time = date("Y-m-d H:i:s");
			$ticket->save();

			$user = $qrCode->reservation->user;
			$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
			$msg = "Thank you for visiting " . $facilityName . ".";

			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}

			$this->customeReplySms($msg, $user->phone);


			$data = ['msg' => $msg];
			return $data;
		} else {
			throw new ApiGenericException('Sorry,Invalid garage or ticket details.');
		}
		return "Success";
	}

	protected function checkTicketNumber()
	{
		$ticket = 'PE' . rand(100, 999) . rand(100, 999);
		$isExist = Ticket::where('ticket_number', $ticket)->first();
		if ($isExist) {
			//$this->checkTicketNumber();
			$ticket = $this->checkTicketNumber();
			$isExist = Ticket::where('ticket_number', $ticket)->first();
			if ($isExist) {
				//$this->checkTicketNumber();
				$ticket = $this->checkTicketNumber();
				$isExist = Ticket::where('ticket_number', $ticket)->first();
				if ($isExist) {
					return $ticket = $this->checkTicketNumber();
				}
			}
		}
		return $ticket;
	}

	protected function checkReservationNumber()
	{
		$ticket = 'MO' . rand(100, 999) . rand(100, 999);
		$isExist = Reservation::where('ticketech_code', $ticket)->first();
		if ($isExist) {
			return $ticket = $this->checkReservationNumber();
		}
		return $ticket;
	}


	public function mapcoSessionCheckinCheckout(Request $request)
	{
		$this->log->info("mapcoSessionCheckinCheckout -- " . json_encode($request->all()));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Sorry,Invalid garage.');
		}
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();

		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}
		//check gate api
		if ($facility->check_vehicle_enabled == '1') {
			$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}
		}
		$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->orderBy("id", "DESC")->first();
		if (!$user) {

			if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
				throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
			}

			$user = User::create(
				[
					'name' => '',
					'email' => '',
					'phone' => '',
					'password' => Hash::make(str_random(60)),
					'anon' => true,
					'user_type' => '5',
					'created_by' => $facility->owner_id,
					'session_id' => $request->session_id,
				]
			);
		}
		if (isset($gate) && $gate->gate_type == "entry") {

			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}
			$reservation = Reservation::where('user_id', $user->id)->orderBy("id", "DESC")->first();
			if (!$reservation) {
				if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
					throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
				}
				if ($facility->facility_booking_type == '0') {
					throw new ApiGenericException('Sorry, Drive-Up booking is not allowed.');
				}
				if ($facility->is_prepaid_first == '2') {
					$today = date("Y-m-d");
					$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
					if (!$todayEvent) {
						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
					$parkingNowTime = date("Y-m-d H:i:s");
					$parkingStartTime = date("Y-m-d") . " " . $todayEvent->parking_start_time;
					$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
					$eventStartTime = $todayEvent->start_time;
					$eventEndTime = $todayEvent->end_time;

					//dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
					if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
						//$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
						$driveupRate = 0;
						if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
							$driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
						} else {
							$driveupRate = $facility->base_rate + $facility->processing_fee;
						}
						$data['price'] =  $driveupRate;

						return $data;
					} else {

						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}
						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
				}
				if ($facility->is_prepaid_first == '0') {
					$today = date("Y-m-d");
					$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
					if (!$todayEvent) {
						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}
						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
					$data['user_id'] = $user->id;
					$data['checkin_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					$data['is_checkin'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber();
					$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					$data['check_in_datetime'] = date('Y-m-d H:i:s');
					$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['event_id'] = $todayEvent->id;
					$data['vp_device_checkin'] = '1';

					$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
					$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
					if (isset($request->CardType)) {
						$card_type = '';
						if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
							$card_type = 'VISA';
						} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
							$card_type = 'MASTERCARD';
						} else if (strtolower($request->CardType) == "jcb") {
							$card_type = 'JCB';
						} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
							$card_type = 'AMEX';
						} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
							$card_type = 'DISCOVER';
						} else {
							$card_type = $request->CardType;
						}
						$data['card_type'] = $card_type;
					}

					$result = Ticket::create($data);

					$facilityName = ucwords($facility->full_name);

					$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}
					Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

					$data = ['msg' => "Welcome to $facilityName."];
					return $data;
				}
				if ($facility->is_prepaid_first == '1') {
					$today = date("Y-m-d");
					$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
					if (!$todayEvent) {
						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}
						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
					$parkingNowTime = date("Y-m-d H:i:s");
					$parkingStartTime = date("Y-m-d") . " " . $todayEvent->parking_start_time;
					$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
					$eventStartTime = $todayEvent->start_time;
					$eventEndTime = $todayEvent->end_time;

					//dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
					if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
						//$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
						$driveupRate = 0;
						if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
							$driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
						} else {
							$driveupRate = $facility->base_rate + $facility->processing_fee;
						}
						$data['price'] =  $driveupRate;
						return $data;
					} else {

						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}
						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
				}
			}

			if ($reservation->cancelled_at != '') {
				throw new ApiGenericException("Sorry, Your booking has been canceled.");
			}

			$qrCode = MapcoQrcode::with(['event', 'eventCategory.eventCategoryEvent.event'])->where("reservation_id", $reservation->id)->where("remain_usage", '>', '0')->get();
			if (count($qrCode) == 0) {
				if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
					throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
				}
				$ticket = Ticket::where('reservation_id', $reservation->id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
				if ($ticket) {
					throw new ApiGenericException('Sorry, You have already checked-in.');
				}

				if ($facility->is_prepaid_first == '2') {
					$today = date("Y-m-d");
					$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
					if (!$todayEvent) {
						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
					$parkingNowTime = date("Y-m-d H:i:s");
					$parkingStartTime = date("Y-m-d") . " " . $todayEvent->parking_start_time;
					$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
					$eventStartTime = $todayEvent->start_time;
					$eventEndTime = $todayEvent->end_time;

					//dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
					if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
						//$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
						$driveupRate = 0;
						if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
							$driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
						} else {
							$driveupRate = $facility->base_rate + $facility->processing_fee;
						}
						$data['price'] =  $driveupRate;
						return $data;
					} else {
						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
				}
				if ($facility->is_prepaid_first == '0') {
					$today = date("Y-m-d");
					$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
					if (!$todayEvent) {
						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
					$data['user_id'] = $user->id;
					$data['checkin_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					$data['is_checkin'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber();
					$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					$data['check_in_datetime'] = date('Y-m-d H:i:s');
					$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['event_id'] = $todayEvent->id;
					$data['vp_device_checkin'] = '1';

					$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
					$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
					if (isset($request->CardType)) {
						$card_type = '';
						if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
							$card_type = 'VISA';
						} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
							$card_type = 'MASTERCARD';
						} else if (strtolower($request->CardType) == "jcb") {
							$card_type = 'JCB';
						} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
							$card_type = 'AMEX';
						} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
							$card_type = 'DISCOVER';
						} else {
							$card_type = $request->CardType;
						}
						$data['card_type'] = $card_type;
					}

					$result = Ticket::create($data);

					$facilityName = ucwords($facility->full_name);

					$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

					$data = ['msg' => "Welcome to $facilityName."];
					return $data;
				}
				if ($facility->is_prepaid_first == '1') {
					$today = date("Y-m-d");
					$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
					if (!$todayEvent) {
						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
					$parkingNowTime = date("Y-m-d H:i:s");
					$parkingStartTime = date("Y-m-d") . " " . $todayEvent->parking_start_time;
					$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
					$eventStartTime = $todayEvent->start_time;
					$eventEndTime = $todayEvent->end_time;

					//dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
					if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
						//$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
						$driveupRate = 0;
						if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
							$driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
						} else {
							$driveupRate = $facility->base_rate + $facility->processing_fee;
						}
						$data['price'] =  $driveupRate;
						return $data;
					} else {

						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

						$data = ['msg' => "Welcome to $facilityName."];
						return $data;
					}
				}
			} else {

				$today = date("Y-m-d");
				if ($qrCode[0]->event_id != '0') {

					//$event = Event::whereDate("start_time", '>=', $today)->whereDate("end_time", '<=', $today)->where('id',$qrCode[0]->event_id)->first();
					$event = Event::whereDate("start_time", '=', $today)->where('id', $qrCode[0]->event_id)->first();
					if (!$event) {
						//throw new ApiGenericException('Sorry, No event found.');    
						if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
							throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
						}


						if ($facility->is_prepaid_first == '2') {
							$today = date("Y-m-d");
							$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
							if (!$todayEvent) {
								$data['user_id'] = $user->id;
								$data['checkin_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 1;
								$data['ticket_number'] = $this->checkTicketNumber();
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								$data['check_in_datetime'] = date('Y-m-d H:i:s');
								$data['checkin_time'] = date('Y-m-d H:i:s');

								$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
								$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
								if (isset($request->CardType)) {
									$card_type = '';
									if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
										$card_type = 'VISA';
									} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
										$card_type = 'MASTERCARD';
									} else if (strtolower($request->CardType) == "jcb") {
										$card_type = 'JCB';
									} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
										$card_type = 'AMEX';
									} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
										$card_type = 'DISCOVER';
									} else {
										$card_type = $request->CardType;
									}
									$data['card_type'] = $card_type;
								}

								$result = Ticket::create($data);

								$facilityName = ucwords($facility->full_name);

								$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

								$data = ['msg' => "Welcome to $facilityName."];
								return $data;
							}
							$parkingNowTime = date("Y-m-d H:i:s");
							$parkingStartTime = date("Y-m-d") . " " . $todayEvent->parking_start_time;
							$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
							$eventStartTime = $todayEvent->start_time;
							$eventEndTime = $todayEvent->end_time;

							//dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
							if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
								//$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
								$driveupRate = 0;
								if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
									$driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
								} else {
									$driveupRate = $facility->base_rate + $facility->processing_fee;
								}
								$data['price'] =  $driveupRate;
								return $data;
							} else {
								$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $eventStartTime);
								$from = Carbon::createFromFormat('Y-m-d H:i:s', $eventEndTime);
								if ($todayEvent->base_event_hours != '') {
									$diff_in_hours = $todayEvent->base_event_hours;
								} else {
									$diff_in_hours = $arrival_time->diffInRealHours($from);
									if ($diff_in_hours < self::EVENT_THRESHOLD_TYPE) {
										$diff_in_hours = self::EVENT_THRESHOLD_TYPE;
									}
								}

								/** this function is used to get Availability Information for respective facility **/
								$rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);
								$data['price'] =  $rate['price'] + $facility->processing_fee;
								return $data;
							}
						}
						if ($facility->is_prepaid_first == '0') {
							$today = date("Y-m-d");
							$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
							if (!$todayEvent) {
								$data['user_id'] = $user->id;
								$data['checkin_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 1;
								$data['ticket_number'] = $this->checkTicketNumber();
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								$data['check_in_datetime'] = date('Y-m-d H:i:s');
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['vp_device_checkin'] = '1';

								$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
								$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
								if (isset($request->CardType)) {
									$card_type = '';
									if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
										$card_type = 'VISA';
									} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
										$card_type = 'MASTERCARD';
									} else if (strtolower($request->CardType) == "jcb") {
										$card_type = 'JCB';
									} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
										$card_type = 'AMEX';
									} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
										$card_type = 'DISCOVER';
									} else {
										$card_type = $request->CardType;
									}
									$data['card_type'] = $card_type;
								}

								$result = Ticket::create($data);

								$facilityName = ucwords($facility->full_name);

								$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

								$data = ['msg' => "Welcome to $facilityName."];
								return $data;
							}
							$data['user_id'] = $user->id;
							$data['checkin_gate'] = $request->gate_id;
							$data['facility_id'] = $request->facility_id;
							$data['is_checkin'] = 1;
							$data['ticket_number'] = $this->checkTicketNumber();
							$data['checkin_time'] = date('Y-m-d H:i:s');
							$data['ticket_security_code'] = rand(1000, 9999);
							$data['partner_id'] = $facility->owner_id;
							$data['check_in_datetime'] = date('Y-m-d H:i:s');
							$data['checkin_time'] = date('Y-m-d H:i:s');
							$data['event_id'] = $todayEvent->id;
							$data['vp_device_checkin'] = '1';

							$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
							$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
							if (isset($request->CardType)) {
								$card_type = '';
								if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
									$card_type = 'VISA';
								} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
									$card_type = 'MASTERCARD';
								} else if (strtolower($request->CardType) == "jcb") {
									$card_type = 'JCB';
								} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
									$card_type = 'AMEX';
								} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
									$card_type = 'DISCOVER';
								} else {
									$card_type = $request->CardType;
								}
								$data['card_type'] = $card_type;
							}

							$result = Ticket::create($data);

							$facilityName = ucwords($facility->full_name);

							$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

							$data = ['msg' => "Welcome to $facilityName."];
							return $data;
						}
						if ($facility->is_prepaid_first == '1') {
							$today = date("Y-m-d");
							$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
							if (!$todayEvent) {
								$data['user_id'] = $user->id;
								$data['checkin_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 1;
								$data['ticket_number'] = $this->checkTicketNumber();
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								$data['check_in_datetime'] = date('Y-m-d H:i:s');
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['vp_device_checkin'] = '1';

								$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
								$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
								if (isset($request->CardType)) {
									$card_type = '';
									if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
										$card_type = 'VISA';
									} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
										$card_type = 'MASTERCARD';
									} else if (strtolower($request->CardType) == "jcb") {
										$card_type = 'JCB';
									} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
										$card_type = 'AMEX';
									} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
										$card_type = 'DISCOVER';
									} else {
										$card_type = $request->CardType;
									}
									$data['card_type'] = $card_type;
								}

								$result = Ticket::create($data);

								$facilityName = ucwords($facility->full_name);

								$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

								$data = ['msg' => "Welcome to $facilityName."];
								return $data;
							}
							$parkingNowTime = date("Y-m-d H:i:s");
							$parkingStartTime = date("Y-m-d") . " " . $todayEvent->parking_start_time;
							$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
							$eventStartTime = $todayEvent->start_time;
							$eventEndTime = $todayEvent->end_time;

							//dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
							if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
								//$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
								$driveupRate = 0;
								if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
									$driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
								} else {
									$driveupRate = $facility->base_rate + $facility->processing_fee;
								}
								$data['price'] =  $driveupRate;
								return $data;
							} else {

								$data['user_id'] = $user->id;
								$data['checkin_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 1;
								$data['ticket_number'] = $this->checkTicketNumber();
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								$data['check_in_datetime'] = date('Y-m-d H:i:s');
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['vp_device_checkin'] = '1';

								$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
								$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
								if (isset($request->CardType)) {
									$card_type = '';
									if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
										$card_type = 'VISA';
									} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
										$card_type = 'MASTERCARD';
									} else if (strtolower($request->CardType) == "jcb") {
										$card_type = 'JCB';
									} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
										$card_type = 'AMEX';
									} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
										$card_type = 'DISCOVER';
									} else {
										$card_type = $request->CardType;
									}
									$data['card_type'] = $card_type;
								}

								$result = Ticket::create($data);

								$facilityName = ucwords($facility->full_name);

								$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

								$data = ['msg' => "Welcome to $facilityName."];
								return $data;
							}
						}
					}
					$event_id = $qrCode[0]->event_id;
				}
				if ($qrCode[0]->event_id == '0' && ($qrCode[0]->event_category_id != '0' || $qrCode[0]->event_category_id != '')) {
					//$today = date("Y-m-d");
					//$today = "2022-12-07";
					$eventCategoryEvent = EventCategoryEvent::leftJoin('events', 'events.id', '=', 'event_category_events.event_id')
						->select('events.id', 'events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode[0]->event_category_id)->first();
					if (!isset($eventCategoryEvent->start_time)) {
						//throw new ApiGenericException('Sorry, No event found.');    
						if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
							throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
						}


						if ($facility->is_prepaid_first == '2') {
							$today = date("Y-m-d");
							$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
							if (!$todayEvent) {
								$data['user_id'] = $user->id;
								$data['checkin_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 1;
								$data['ticket_number'] = $this->checkTicketNumber();
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								$data['check_in_datetime'] = date('Y-m-d H:i:s');
								$data['checkin_time'] = date('Y-m-d H:i:s');

								$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
								$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
								if (isset($request->CardType)) {
									$card_type = '';
									if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
										$card_type = 'VISA';
									} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
										$card_type = 'MASTERCARD';
									} else if (strtolower($request->CardType) == "jcb") {
										$card_type = 'JCB';
									} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
										$card_type = 'AMEX';
									} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
										$card_type = 'DISCOVER';
									} else {
										$card_type = $request->CardType;
									}
									$data['card_type'] = $card_type;
								}

								$result = Ticket::create($data);

								$facilityName = ucwords($facility->full_name);

								$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

								$data = ['msg' => "Welcome to $facilityName."];
								return $data;
							}
							$parkingNowTime = date("Y-m-d H:i:s");
							$parkingStartTime = date("Y-m-d") . " " . $todayEvent->parking_start_time;
							$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
							$eventStartTime = $todayEvent->start_time;
							$eventEndTime = $todayEvent->end_time;

							//dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
							if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
								//$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
								$driveupRate = 0;
								if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
									$driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
								} else {
									$driveupRate = $facility->base_rate + $facility->processing_fee;
								}
								$data['price'] =  $driveupRate;
								return $data;
							} else {
								$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
								$from = Carbon::createFromFormat('Y-m-d H:i:s', $eventEndTime);
								if ($todayEvent->base_event_hours != '') {
									$diff_in_hours = $todayEvent->base_event_hours;
								} else {
									$diff_in_hours = $arrival_time->diffInRealHours($from);
									if ($diff_in_hours < self::EVENT_THRESHOLD_TYPE) {
										$diff_in_hours = self::EVENT_THRESHOLD_TYPE;
									}
								}

								/** this function is used to get Availability Information for respective facility **/
								$rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);
								$data['price'] =  $rate['price'] + $facility->processing_fee;
								return $data;
							}
						}
						if ($facility->is_prepaid_first == '0') {
							$today = date("Y-m-d");
							$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
							if (!$todayEvent) {
								$data['user_id'] = $user->id;
								$data['checkin_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 1;
								$data['ticket_number'] = $this->checkTicketNumber();
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								$data['check_in_datetime'] = date('Y-m-d H:i:s');
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['vp_device_checkin'] = '1';

								$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
								$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
								if (isset($request->CardType)) {
									$card_type = '';
									if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
										$card_type = 'VISA';
									} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
										$card_type = 'MASTERCARD';
									} else if (strtolower($request->CardType) == "jcb") {
										$card_type = 'JCB';
									} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
										$card_type = 'AMEX';
									} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
										$card_type = 'DISCOVER';
									} else {
										$card_type = $request->CardType;
									}
									$data['card_type'] = $card_type;
								}

								$result = Ticket::create($data);

								$facilityName = ucwords($facility->full_name);

								$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

								$data = ['msg' => "Welcome to $facilityName."];
								return $data;
							}
							$data['user_id'] = $user->id;
							$data['checkin_gate'] = $request->gate_id;
							$data['facility_id'] = $request->facility_id;
							$data['is_checkin'] = 1;
							$data['ticket_number'] = $this->checkTicketNumber();
							$data['checkin_time'] = date('Y-m-d H:i:s');
							$data['ticket_security_code'] = rand(1000, 9999);
							$data['partner_id'] = $facility->owner_id;
							$data['check_in_datetime'] = date('Y-m-d H:i:s');
							$data['checkin_time'] = date('Y-m-d H:i:s');
							$data['event_id'] = $todayEvent->id;
							$data['vp_device_checkin'] = '1';

							$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
							$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
							if (isset($request->CardType)) {
								$card_type = '';
								if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
									$card_type = 'VISA';
								} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
									$card_type = 'MASTERCARD';
								} else if (strtolower($request->CardType) == "jcb") {
									$card_type = 'JCB';
								} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
									$card_type = 'AMEX';
								} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
									$card_type = 'DISCOVER';
								} else {
									$card_type = $request->CardType;
								}
								$data['card_type'] = $card_type;
							}

							$result = Ticket::create($data);

							$facilityName = ucwords($facility->full_name);

							$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));

							$data = ['msg' => "Welcome to $facilityName."];
							return $data;
						}
						if ($facility->is_prepaid_first == '1') {
							$today = date("Y-m-d");
							$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
							if (!$todayEvent) {
								$data['user_id'] = $user->id;
								$data['checkin_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 1;
								$data['ticket_number'] = $this->checkTicketNumber();
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								$data['check_in_datetime'] = date('Y-m-d H:i:s');
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['vp_device_checkin'] = '1';

								$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
								$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
								if (isset($request->CardType)) {
									$card_type = '';
									if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
										$card_type = 'VISA';
									} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
										$card_type = 'MASTERCARD';
									} else if (strtolower($request->CardType) == "jcb") {
										$card_type = 'JCB';
									} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
										$card_type = 'AMEX';
									} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
										$card_type = 'DISCOVER';
									} else {
										$card_type = $request->CardType;
									}
									$data['card_type'] = $card_type;
								}

								$result = Ticket::create($data);

								$facilityName = ucwords($facility->full_name);

								$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								$data = ['msg' => "Welcome to $facilityName."];
								return $data;
							}
							$parkingNowTime = date("Y-m-d H:i:s");
							$parkingStartTime = date("Y-m-d") . " " . $todayEvent->parking_start_time;
							$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
							$eventStartTime = $todayEvent->start_time;
							$eventEndTime = $todayEvent->end_time;

							//dd($parkingNowTime, $parkingStartTime, $parkingEndTime);
							if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
								//$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
								$driveupRate = 0;
								if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
									$driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee;
								} else {
									$driveupRate = $facility->base_rate + $facility->processing_fee;
								}
								$data['price'] =  $driveupRate;
								return $data;
							} else {

								$data['user_id'] = $user->id;
								$data['checkin_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 1;
								$data['ticket_number'] = $this->checkTicketNumber();
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								$data['check_in_datetime'] = date('Y-m-d H:i:s');
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['vp_device_checkin'] = '1';

								$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
								$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
								if (isset($request->CardType)) {
									$card_type = '';
									if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
										$card_type = 'VISA';
									} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
										$card_type = 'MASTERCARD';
									} else if (strtolower($request->CardType) == "jcb") {
										$card_type = 'JCB';
									} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
										$card_type = 'AMEX';
									} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
										$card_type = 'DISCOVER';
									} else {
										$card_type = $request->CardType;
									}
									$data['card_type'] = $card_type;
								}

								$result = Ticket::create($data);

								$facilityName = ucwords($facility->full_name);

								$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}
								$data = ['msg' => "Welcome to $facilityName."];
								return $data;
							}
						}
					}
					$event_id = $eventCategoryEvent->id;
				}

				$user = $reservation->user;
				$data['user_id'] = $user->id;
				$data['checkin_gate'] = $request->gate_id;
				$data['facility_id'] = $request->facility_id;
				$data['is_checkin'] = 1;
				$data['ticket_number'] = $this->checkTicketNumber();
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['ticket_security_code'] = rand(1000, 9999);
				$data['partner_id'] = $facility->owner_id;
				//if($reservation){
				$data['reservation_id'] = $reservation->id;
				$data['anet_transaction_id'] = $reservation->anet_transaction_id;
				$data['check_in_datetime'] = $reservation->start_timestamp;
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['total'] = $reservation->total;
				$data['grand_total'] = $reservation->total;
				$data['length'] = $reservation->length;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['event_id'] = $event_id;

				$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
				$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
				if (isset($request->CardType)) {
					$card_type = '';
					if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
						$card_type = 'VISA';
					} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
						$card_type = 'MASTERCARD';
					} else if (strtolower($request->CardType) == "jcb") {
						$card_type = 'JCB';
					} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
						$card_type = 'AMEX';
					} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
						$card_type = 'DISCOVER';
					} else {
						$card_type = $request->CardType;
					}
					$data['card_type'] = $card_type;
				}

				$result = Ticket::create($data);

				$qrCode[0]->remain_usage = $qrCode[0]->remain_usage == 0 ? 0 : $qrCode[0]->remain_usage - 1;
				$qrCode[0]->save();

				//$reservation->is_ticket = '1';
				//$reservation->save();            

				//}
				/*$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
          if($ticket){
            throw new ApiGenericException('You have already checked-in.');
          }*/
			}

			$facilityName = ucwords($facility->full_name);

			Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			//$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
			//$this->customeReplySms($msg, $user->phone);

			$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			//$data = ['msg' => "Welcome to $facilityName. #$result->ticket_number."];
			$data = ['msg' => "Welcome to $facilityName."];
			return $data;
			//return "Welcome to $facilityName. #$result->ticket_number.";

		} elseif (isset($gate) && $gate->gate_type == "exit") {
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkout', '0')->orderBy("id", "Desc")->first();
			if (!$ticket) {
				throw new ApiGenericException('Sorry, No Check-In found. Please contact to attendant.');

				$today = date("Y-m-d");
				$todayEvent = Event::whereDate('start_time', '=', $today)->where("partner_id", $facility->owner_id)->first();
				if (!$todayEvent) {
					$data['price'] =  $facility->base_rate + $facility->processing_fee;
					return $data;
				}

				$reservation = Reservation::where('user_id', $user->id)->orderBy("id", "DESC")->first();
				if (!$reservation) {
					if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
						throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
					}
					$data['price'] =  $facility->base_rate + $facility->processing_fee;
					return $data;
					/*$today = date("Y-m-d");
                $todayEvent = Event::whereDate('start_time', '=', $today)->where("partner_id", $facility->owner_id)->first();
                if(!$todayEvent){
                    //throw new ApiGenericException("Sorry, No event found.");
                    $data['price'] =  $facility->base_rate + $facility->processing_fee;
                    return $data;
                }
                if($todayEvent->event_rate == 0 || $todayEvent->event_rate == ''){
                    $data['price'] =  $facility->base_event_rate + $facility->processing_fee;
                }else{
                    $data['price'] =  $todayEvent->event_rate + $facility->processing_fee;
                }
                $data['id'] = $todayEvent->id;
                return $data;*/

					//
				}

				if ($reservation->cancelled_at != '') {
					throw new ApiGenericException("Sorry, Your booking has been canceled.");
				}


				$qrCode = MapcoQrcode::with(['event', 'eventCategory.eventCategoryEvent.event'])->where("reservation_id", $reservation->id)->where("remain_usage", '>', '0')->get();
				if (count($qrCode) == 0) {
					if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
						throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
					}
					$data['price'] =  $facility->base_rate + $facility->processing_fee;
					return $data;
					/*$today = date("Y-m-d");
                    $todayEvent = Event::whereDate('start_time', '=', $today)->where("partner_id", $facility->owner_id)->first();
                    if(!$todayEvent){
                        throw new ApiGenericException("Sorry, No event found.");
                    }
                    if($todayEvent->event_rate == 0 || $todayEvent->event_rate == ''){
                        $data['price'] =  $facility->base_event_rate + $facility->processing_fee;
                    }else{
                        $data['price'] =  $todayEvent->event_rate + $facility->processing_fee;
                    }
                    $data['id'] = $todayEvent->id;
                    return $data;*/
				} else {

					$today = date("Y-m-d");
					if ($qrCode[0]->event_id != '0') {

						//$event = Event::whereDate("start_time", '>=', $today)->whereDate("end_time", '<=', $today)->where('id',$qrCode[0]->event_id)->first();
						$event = Event::whereDate("start_time", '=', $today)->where('id', $qrCode[0]->event_id)->first();
						if (!$event) {
							if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
								throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
							}
							$data['price'] =  $facility->base_rate + $facility->processing_fee;
							return $data;
							//throw new ApiGenericException('Sorry, No event found.');    
						}
						$event_id = $qrCode[0]->event_id;
					}
					if ($qrCode[0]->event_id == '0' && ($qrCode[0]->event_category_id != '0' || $qrCode[0]->event_category_id != '')) {
						//$today = date("Y-m-d");
						//$today = "2022-12-07";
						$eventCategoryEvent = EventCategoryEvent::leftJoin('events', 'events.id', '=', 'event_category_events.event_id')
							->select('events.id', 'events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode[0]->event_category_id)->first();
						if (!isset($eventCategoryEvent->start_time)) {
							if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
								throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
							}
							$data['price'] =  $facility->base_rate + $facility->processing_fee;
							return $data;
							//throw new ApiGenericException('Sorry, No event found.');    
						}
						$event_id = $eventCategoryEvent->id;
					}

					$user = $reservation->user;
					$data['user_id'] = $user->id;
					$data['checkout_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					$data['is_checkout'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber();
					//$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					//if($reservation){
					$data['reservation_id'] = $reservation->id;
					$data['anet_transaction_id'] = $reservation->anet_transaction_id;
					$data['check_in_datetime'] = $reservation->start_timestamp;
					$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
					$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
					$data['total'] = $reservation->total;
					$data['grand_total'] = $reservation->total;
					$data['length'] = $reservation->length;
					$data['checkout_time'] = date('Y-m-d H:i:s');
					$data['event_id'] = $event_id;
					$data['checkout_without_checkin'] = '1';
					$result = Ticket::create($data);

					$qrCode[0]->remain_usage = $qrCode[0]->remain_usage == 0 ? 0 : $qrCode[0]->remain_usage - 1;
					$qrCode[0]->save();

					//$reservation->is_ticket = '1';
					//$reservation->save();            

					//}
					/*$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                if($ticket){
                  throw new ApiGenericException('You have already checked-in.');
                }*/

					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
					$msg = "Thank you for visiting " . $facilityName . ".";
					$this->customeReplySms($msg, $user->phone);
					//return $msg;
					$data = ['msg' => $msg];
					return $data;
				}


				$data['price'] =  $facility->base_rate + $facility->processing_fee;
				return $data;
				/*if($todayEvent->event_rate == 0 || $todayEvent->event_rate == ''){
                  $data['price'] =  $facility->base_event_rate + $facility->processing_fee;
              }else{
                  $data['price'] =  $todayEvent->event_rate + $facility->processing_fee;
              }
              $data['id'] = $todayEvent->id;
              return $data;*/
				//$data['price'] =  $facility->base_rate + $facility->processing_fee;
				//return $data;
			}
			if ($ticket->anet_transaction_id == '' && $ticket->reservation_id == '') {
				$today = date("Y-m-d");
				$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
				if (!$todayEvent) {
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
					$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
					$diff_in_hours = $arrival_time->diffInRealHours($from);
					if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
						$diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
					}
					$checkinData['length'] = $diff_in_hours;
					/** this function is used to get Availability Information for respective facility **/
					$rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);

					if ($rate) {
						if ($rate['price'] == 0 || $rate['price'] == "0.00") {
							$ticket->is_checkout = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->total = 0.00;
							$ticket->grand_total = 0.00;
							$ticket->length = $diff_in_hours;
							$ticket->save();

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "Thank you for visiting " . $facilityName . ".";
							$this->customeReplySms($msg, $user->phone);
							$data = ['msg' => $msg];
							return $data;
						}
					}

					return $rate;
				}
				$parkingNowTime = date("Y-m-d H:i:s");
				$parkingStartTime = date("Y-m-d", strtotime($todayEvent->start_time)) . " " . $todayEvent->parking_start_time;
				$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
				$eventStartTime = $todayEvent->start_time;
				$eventEndTime = $todayEvent->end_time;

				$checkinTime = $ticket->checkin_time;

				//dd($parkingNowTime, $parkingStartTime, $parkingEndTime, $ticket->checkin_time);
				if (strtotime($checkinTime) >= strtotime($parkingStartTime) && strtotime($checkinTime) <= strtotime($parkingEndTime)) {
					$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
					return $data;
				} else {
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
					if ($facility->is_prepaid_first == '0') {
						$diff_in_hours = $arrival_time->diffInRealHours($from);
						if ($diff_in_hours < self::EVENT_THRESHOLD_TYPE) {
							$diff_in_hours = self::EVENT_THRESHOLD_TYPE;
						}
					} else {
						/*if($todayEvent->base_event_hours != ''){
                    $diff_in_hours = $todayEvent->base_event_hours;
                  }else{*/
						$diff_in_hours = $arrival_time->diffInRealHours($from);
						$diff_in_mins = $arrival_time->diffInRealMinutes($from);

						if ($diff_in_mins > 0) {
							$diff_in_hours = number_format($diff_in_mins / 60, 2);
						}
						if ($diff_in_hours < self::EVENT_THRESHOLD_TYPE) {
							$diff_in_mins = $arrival_time->diffInRealMinutes($from);
							$diff_in_hours = number_format($diff_in_mins / 60, 2);
						}
						//}
					}

					//dd($arrival_time, $diff_in_hours, $ticket->checkin_time);
					/** this function is used to get Availability Information for respective facility **/
					$rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);

					if ($rate) {
						if ($rate['price'] == 0 || $rate['price'] == "0.00") {
							$ticket->is_checkout = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->total = 0.00;
							$ticket->grand_total = 0.00;
							$ticket->length = $diff_in_hours;
							$ticket->save();

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "Thank you for visiting " . $facilityName . ".";
							$this->customeReplySms($msg, $user->phone);
							$data = ['msg' => $msg];
							return $data;
						}
					}

					$data['price'] =  $rate['price'] + $facility->processing_fee;
					return $data;
				}
			} elseif ($ticket->anet_transaction_id != '') {

				if ($ticket->event_id != '' || $ticket->event_id != '0') {

					$parkingNowTime = date("Y-m-d H:i:s");
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);

					$diff_in_hours = $arrival_time->diffInRealHours($from);
					$diff_in_mins = $arrival_time->diffInRealMinutes($from);
					if ($diff_in_mins > 0) {
						$diff_in_hours = number_format($diff_in_mins / 60, 2);
					}
					if ($diff_in_hours < self::EVENT_THRESHOLD_TYPE) {
						$diff_in_mins = $arrival_time->diffInRealMinutes($from);
						$diff_in_hours = number_format($diff_in_mins / 60, 2);
					}
					//dd($from,  $ticket->checkin_time, $diff_in_hours, $diff_in_mins);  
					$todayEvent = Event::find($ticket->event_id);
					//dd($todayEvent->base_event_hours, $diff_in_hours);
					if ($todayEvent->base_event_hours >= $diff_in_hours) {
					} else {
						$new_arrival_time = $arrival_time->addHours($todayEvent->base_event_hours);
						$diff_in_hours = $diff_in_hours - $todayEvent->base_event_hours;
						//$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
						/** this function is used to get Availability Information for respective facility **/
						$rate = $facility->rateForReservation($new_arrival_time, $diff_in_hours, false, false,  false, true, false, 0, self::EVENT_THRESHOLD_TYPE);
						$data['price'] =  $rate['price'];
						$data['is_overstay'] =  '1';
						return $data;
					}
				}
			}

			$ticket->is_checkout = '1';
			$ticket->checkout_gate = $request->gate_id;
			$ticket->checkout_time = date("Y-m-d H:i:s");
			$ticket->save();

			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}

			$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
			$msg = "Thank you for visiting " . $facilityName . ".";
			$this->customeReplySms($msg, $user->phone);
			//return $msg;
			$data = ['msg' => $msg];
			return $data;
		} else {
			throw new ApiGenericException('Sorry! Invalid gate details.');
		}
		//return "Success";
	}


	public function customeReplySms($msg, $phone, $imageURL = '')
	{
		return "success";
		try {
			if ($phone == '') {
				$this->log->error("Phone is required");
				return "success";
			}
			$this->log->info("sms about to send");
			$accountSid = env('TWILIO_ACCOUNT_SID');
			$authToken  = env('TWILIO_AUTH_TOKEN');
			$client = new Client($accountSid, $authToken);
			try {

				// Use the client to do fun stuff like send text messages!
				$client->messages->create(
					// the number you'd like to send the message to
					$phone,
					array(
						// A Twilio phone number you purchased at twilio.com/console
						'from' => env('TWILIO_PHONE'),
						// the body of the text message you'd like to send
						//'body' => "Fine"
						'body' => "$msg",
						//'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)

					)
				);
				$this->log->info("Message : {$msg} sent to $phone");
				return "success";
			} catch (RestException $e) {
				//echo "Error: " . $e->getMessage();
				$this->log->error($e->getMessage());
				return "success";
			}
		} catch (RestException $e) {
			//echo "Error: " . $e->getMessage();
			$this->log->error($e->getMessage());
			return "success";
		}
	}


	public function sendUserEmail($reservation_id)
	{

		$resp = Reservation::find($reservation_id);
		if ($resp) {
			//in_array($resp->partner_id, [config('parkengage.PARTNER_MAPCO'), config('parkengage.PARTNER_PCI'), config('parkengage.PARTNER_CLASSIC'), config('parkengage.PARTNER_INTRAPARK')]) && 
			if (count($resp->mapcoQrCode) > 0) {
				Artisan::queue('mapco:reservation-email', array('id' => $reservation_id));
				return 'Email successfully sent to the user.';
			} else {

				$client = DB::table('oauth_clients')->where('partner_id', $resp->partner_id)->first();
				if ($client) {
					$clent_secret = $client->secret;
					$resp->emailReservationToPartnerUser($clent_secret);
					//Artisan::queue('reservation:email',array('reservationId' => $reservation_id));
					return 'Email successfully sent to the user.';
				}
			}
		} else {

			throw new ApiGenericException('Reservation id not found');
		}
	}


	public function cancelBooking($reservation_id)
	{
		$reservation = Reservation::with(['ticket'])->where("id", $reservation_id)->whereNull("cancelled_at")->first();
		if ($reservation) {
			if (count($reservation->ticket) > 0) {
				throw new ApiGenericException('Sorry, this booking can’t be canceled as check-in against this booking is already done.');
			}

			if ($reservation->total == 0.00 || $reservation->total == 0 || $reservation->total == NULL) {
				//return $reservation->total;
				$reservation->refund_amount = $reservation->total;
				$reservation->cancelled_at = date("Y-m-d H:i:s");
				$reservation->save();
				Artisan::queue('mapco:cancel-reservation-email', array('id' => $reservation_id));
				//return 'User booking successfully canceled.';  
				return $reservation;
			} else {
				// return $reservation->total;
				$refundstatus = $this->refundAmount($reservation_id);
				//return $refundstatus;
				if ($refundstatus) {
					foreach ($refundstatus as $val) {
						//print_r($val['Type']);
						//  if( $val['Params']['ResultReason'] == "APPROVED"){
						if ($val['Params']['TxState'] == 'AR' || $val['Params']['TxState'] == 'CQ') {
							$reservation->refund_transaction_id = $val['Params']['TxID'];
							$reservation->refund_status = $val['Params']['ResultReason'];
							$reservation->cancelled_at = date("Y-m-d H:i:s");
							$reservation->refund_amount = $reservation->total;
							$reservation->save();
							Artisan::queue('mapco:cancel-reservation-email', array('id' => $reservation_id));
							$this->log->info("Payment refund  to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($val['Params']));
							//return 'User booking successfully canceled.';  
							return $reservation;
						} else {
							$reservation->refund_transaction_id = $val['Params']['TxID'];
							$reservation->refund_status = $val['Params']['ResultReason'];
							$reservation->refund_amount = $reservation->total;
							$reservation->save();
							$this->log->info("Payment refund fail to Booking Id #:" . $reservation->ticketech_code . "--" . json_encode($val['Params']));
							throw new ApiGenericException('Refund Fail due to' . " " . $val['Params']['ResultReason']);
						}
					}
				} else {
					throw new ApiGenericException('Refund Payment Gateway Issue');
				}
			}
		} else {
			throw new ApiGenericException('Reservation not found');
		}
	}

	public function refundAmount($reservation_id)
	{
		$reservation = Reservation::with('transaction')->where("id", $reservation_id)->first();
		$tran_id = $reservation->transaction->anet_trans_id;
		$amount = - ($reservation->total * 100);

		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => config('parkengage.MAPCO_PLANET_REFUND_URL') . $tran_id,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => '{
          "Request": {
              "Type": "payrequestnocardreadbytxid",
              "Version": "W2MXG520",
              "Credentials": {
                  "ValidationID": "MAPCOParking",
                  "ValidationCode": "P@rk3ng4g3",
                  "ValidationCodeHash": null
              },
              "Params": {
                  "RequesterTransRefNum": "NAU TEST PAYMENT 001",
                   "Amount": "' . $amount . '",
                  "Currency": "USD"
                  }
          }
      }',
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);
		//return gettype($response);
		return $refundstatus = json_decode($response, TRUE);
	}


	public function mapcoDriveupCheckinCheckout(Request $request)
	{

		$this->log->info("Request received --" . json_encode($request));
		$facility = Facility::find($request->facility_id);

		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}

		$facilityName =  ucwords($facility->full_name);
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			$this->saveAnetTransaction($request);
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			$this->saveAnetTransaction($request);
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}

		if ($facility->check_vehicle_enabled == '1') {
			$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}
		}


		$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
		if (!$user) {
			$user = User::create(
				[
					'name' => '',
					'email' => '',
					'phone' => '',
					'password' => Hash::make(str_random(60)),
					'anon' => true,
					'user_type' => '5',
					'created_by' => $facility->owner_id,
					'session_id' => $request->session_id,
				]
			);
		}

		if (isset($gate) && $gate->gate_type == "entry") {

			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				$this->saveAnetTransaction($request);
				throw new ApiGenericException('You have already checked-in.');
			}
			$today = date("Y-m-d");
			$event = Event::whereDate("start_time", '=', $today)->first();

			if (!$event) {
				//throw new ApiGenericException('Sorry, No event found.');
			}
			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber();
			$data['checkin_time'] = date('Y-m-d H:i:s');
			if ($event) {
				$data['check_in_datetime'] = $event->end_time;
				$data['estimated_checkout'] = $event->end_time;
				$data['event_id'] = $event->id;
			}
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['vp_device_checkin'] = '1';
			$data['partner_id'] = $facility->owner_id;
			$result = Ticket::create($data);

			if (isset($request->payment_details)) {
				$ticket = Ticket::where('id', $result->id)->first();
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Mapco Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
				$authorized_anet_transaction->status_code = isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
				$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
				$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
				$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
				$ticket->payment_date = date('Y-m-d H:i:s');
				$ticket->save();
			}
			$this->log->info("Mapco driveup checkin done --" . json_encode($result));

			if ($result) {

				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				//$response = ["msg" => "Welcome to $facilityName. #$result->ticket_number."];
				$response = ['msg' => "Welcome to $facilityName."];
				return $response;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}

		if (isset($gate) && $gate->gate_type == "exit") {
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if (!$ticket) {

				if (isset($request->payment_details)) {

					$data['user_id'] = $user->id;
					$data['checkout_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					$data['is_checkout'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber();
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					//$data['reservation_id'] = $qrCode->reservation_id;
					//$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
					//$data['check_in_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->start_time));
					//$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
					//$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
					//$data['total'] = $qrCode->reservation->total;
					//$data['grand_total'] = $qrCode->reservation->total;
					//$data['length'] = $qrCode->reservation->length;
					//$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['checkout_time'] = date('Y-m-d H:i:s');
					//$data['event_id'] = $eventData->id;
					$data['checkout_without_checkin'] = '1';
					//dd($data);
					$ticket = Ticket::create($data);
					$this->log->info("payment details request --" . json_encode($request->payment_details));
					$authorized_anet_transaction = new AuthorizeNetTransaction();
					$authorized_anet_transaction->sent = '1';
					$authorized_anet_transaction->user_id = $user->id;
					$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
					$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
					$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
					$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
					$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
					$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
					$authorized_anet_transaction->ref_id = isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
					$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
					$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
					$authorized_anet_transaction->method = "card";
					$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
					$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
					$authorized_anet_transaction->status_code = isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
					$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
					$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
					$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
					$authorized_anet_transaction->save();
					$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
					$ticket->anet_transaction_id = $authorized_anet_transaction->id;
					$ticket->total = $request->payment_details['TransactionAmount'];
					$ticket->grand_total = $request->payment_details['TransactionAmount'];
					$ticket->terminal_id = $request->payment_details['TerminalID'];
					$ticket->payment_date = date('Y-m-d H:i:s');
					$ticket->save();
				}
				//$user = $qrCode->reservation->user;



				//$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
				//$qrCode->save();

				$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
				$msg = "Thank you for visiting " . $facilityName . ".";
				$this->customeReplySms($msg, $user->phone);

				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				$data = ['msg' => $msg];
				return $data;

				//  throw new ApiGenericException('No checkin found for this user.');    

			}
			/*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }*/
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$diff_in_hours = $arrival_time->diffInRealHours($from);
			if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
				$diff_in_hours = 2;
			}
			$ticket->checkout_gate = $request->gate_id;
			$ticket->is_checkout = 1;
			$ticket->length = $diff_in_hours;
			$ticket->checkout_time = date('Y-m-d H:i:s');
			if (isset($request->payment_details)) {
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
				$authorized_anet_transaction->status_code = isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
				$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
				$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
				$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

				if ($request->is_overstay == '1') {

					$parkingNowTime = date("Y-m-d H:i:s");
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
					$diff_in_hours = $arrival_time->diffInRealHours($from);
					$diff_in_mins = $arrival_time->diffInRealMinutes($from);

					$diff_in_hours = number_format($diff_in_mins / 60, 2);
					$todayEvent = Event::find($ticket->event_id);
					$diff_in_hours = $diff_in_hours - $todayEvent->base_event_hours;


					$overstay = new OverstayTicket();
					$overstay->user_id = $ticket->user_id;
					$overstay->facility_id = $ticket->facility_id;
					//$overstay->length = $this->request->length;
					$overstay->total = $request->payment_details['TransactionAmount'];
					$overstay->ticket_number = $ticket->ticket_number;
					$overstay->is_checkin = '1';
					$overstay->is_checkout = '1';
					$overstay->check_in_datetime = date('Y-m-d H:i:s', strtotime($ticket->checkin_time));
					$overstay->checkout_datetime = date('Y-m-d H:i:s');
					$overstay->partner_id = $ticket->partner_id;
					$overstay->ticket_id = $ticket->id;
					$overstay->anet_transaction_id = $authorized_anet_transaction->id;
					$overstay->length = $diff_in_hours;
					$overstay->save();

					$ticket->grand_total = $ticket->grand_total + $request->payment_details['TransactionAmount'];
					$ticket->is_checkout = '1';
					$ticket->save();
				} else {
					$ticket->anet_transaction_id = $authorized_anet_transaction->id;
					$ticket->total = $request->payment_details['TransactionAmount'];
					$ticket->grand_total = $request->payment_details['TransactionAmount'];
					$ticket->terminal_id = $request->payment_details['TerminalID'];
					$ticket->payment_date = date('Y-m-d H:i:s');
				}
			}

			$result = $ticket->save();
			$this->log->info("Mapco driveup checkout done --" . json_encode($ticket));
			if ($result) {
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				//"Thank you for visiting ".$facilityName.".";
				if ($request->is_overstay == '1') {
					$response = ["msg" => "Thank you for visiting " . $facilityName . ". Extra $" . $request->payment_details['TransactionAmount'] . " is charged for overstay."];
				} else {
					$response = ["msg" => "Thank you for visiting " . $facilityName . "."];
				}


				return $response;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
	}

	public function saveAnetTransaction($request)
	{

		if (isset($request->payment_details)) {
			$facility = Facility::find($request->facility_id);
			$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
			$this->log->info("Save payment details before checkin  --" . json_encode($request->payment_details));
			$authorized_anet_transaction = new AuthorizeNetTransaction();
			$authorized_anet_transaction->sent = '1';
			$authorized_anet_transaction->user_id = $user->id;
			$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
			$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
			$authorized_anet_transaction->description = "Payment details  save before checkin";
			$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
			$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
			$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
			$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
			$authorized_anet_transaction->ref_id = $request->payment_details['RequesterTransRefNum'];
			$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
			$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
			$authorized_anet_transaction->method = "card";
			$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
			$authorized_anet_transaction->status_code =  isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
			$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
			$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
			$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
			$authorized_anet_transaction->save();
		}
		$this->log->info("Save payment details before checkin  done  --" . json_encode($request->payment_details));
		return $authorized_anet_transaction;
	}

	function getTotalRevenue(Request $request)
	{
		$month = date('m');
		$from_date  = date('Y-' . $month . '-01');
		$mid_date  = date('Y-' . $month . '-15');
		$to_date  = date('Y-' . $month . '-t');
		$midDateString = strtotime($mid_date);
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		if (isset($request->from_date) && $request->from_date != '') {
			$from_date = $request->from_date;
			$to_date = $request->to_date;
		}

		$result = [];
		if (!isset($request->partner_id)) {
			if (Auth::user()->user_type == '1') {

				$result['total'] = 0;
				$result['data'] = [];
				return $result;
			} elseif (Auth::user()->user_type == '3') {
				$partner_id = Auth::user()->id;
			} elseif (Auth::user()->user_type == '4') {
				$partner_id = Auth::user()->created_by;
			} else {
				$result['total'] = 0;
				$result['data'] = [];
				return $result;
			}
		} else {
			$partner_id = $request->partner_id;
		}

		$partner_id = $partner_id;

		if ($request->request_type == '0') {
			//for pass revenue
			if ($request->sort != '') {
				$revenueTickets = Reservation::with(['user', 'rate', 'mapcoQrCode.event'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('rate_id', '!=', '0')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy($request->sort, $request->sortBy)->paginate(20);
			} else {
				$revenueTickets = Reservation::with(['user', 'rate', 'mapcoQrCode.event'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('rate_id', '!=', '0')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->paginate(20);
			}
		} elseif ($request->request_type == '1') {
			//for booking revenue

			if ($request->sort != '') {
				$revenueTickets = Reservation::with(['user', 'rate', 'mapcoQrCode.event'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('rate_id', '0')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy($request->sort, $request->sortBy)->paginate(20);
			} else {
				$revenueTickets = Reservation::with(['user', 'rate', 'mapcoQrCode.event'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('rate_id', '0')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->paginate(20);
			}
		} elseif ($request->request_type == '2') {
			//for driveup revenue        
			if ($request->sort != '') {
				$revenueTickets = Ticket::with(['user', 'event', 'transaction'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('reservation_id')->orderBy($request->sort, $request->sortBy)->paginate(20);
			} else {
				$revenueTickets = Ticket::with(['user', 'event', 'transaction'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('reservation_id')->orderBy('id', "DESC")->paginate(20);
			}
		} else {
			throw new ApiGenericException('Sorry! Invalid type of revenue.');
		}
		return $revenueTickets;
	}


	public function getRevenueCount(Request $request)
	{

		$result = [];
		if (!isset($request->partner_id)) {
			if (Auth::user()->user_type == '1') {
				$result['totalPassPurchaseCount'] = 0;
				$result['totalCheckinCount'] = 0;
				$result['totalRevenue'] = 0;
				$result['shareRevenue'] = 0;
				$result['totalRredeemPassCount'] = 0;
				return $result;
			} elseif (Auth::user()->user_type == '3') {
				$partner_id = Auth::user()->id;
			} elseif (Auth::user()->user_type == '4') {
				$partner_id = Auth::user()->created_by;
			} else {
				$result['totalPassPurchaseCount'] = 0;
				$result['totalCheckinCount'] = 0;
				$result['totalRevenue'] = 0;
				$result['shareRevenue'] = 0;
				$result['totalRredeemPassCount'] = 0;
				return $result;
			}
		} else {
			$partner_id = $request->partner_id;
		}

		$partner_id = $partner_id;

		$month = date('m');
		$from_date  = date('Y-' . $month . '-01');
		$mid_date  = date('Y-' . $month . '-15');
		$to_date  = date('Y-' . $month . '-t');
		$midDateString = strtotime($mid_date);
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		if (isset($request->from_date) && $request->from_date != '') {
			$from_date = $request->from_date;
			$to_date = $request->to_date;
		}

		$userConfig = UserPaymentGatewayDetail::where('user_id', $partner_id)->first();
		if (!$userConfig) {
			$result['totalPassPurchaseCount'] = 0;
			$result['totalCheckinCount'] = 0;
			$result['totalRevenue'] = 0;
			$result['shareRevenue'] = 0;
			$result['totalRredeemPassCount'] = 0;
			return $result;
		}
		/*$checkinShare = $userConfig->checkin_share ? $userConfig->checkin_share : self::SHARE_TICKET_AMOUNT;
        $passShare = $userConfig->pass_share ? $userConfig->pass_share : self::SHARE_PASS_AMOUNT;
        $passCheckinShare = $userConfig->pass_checkin_share ? $userConfig->pass_checkin_share : 0;*/

		$userPasses = $revenueTickets = Reservation::with(['user', 'rate', 'mapcoQrCode.event'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('rate_id', '!=', '0')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->get();
		$passRevenue = 0.00;
		$totalShareRevenue = 0.00;
		if (count($userPasses) > 0) {
			foreach ($userPasses as $key => $value) {
				$passRevenue += $value->total;
				//$totalShareRevenue += ($passShare + $passCheckinShare) * $value->total_days;
			}
		}
		//for booking revenue
		$reservations = Reservation::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('rate_id', '0')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->get();
		$bookingRevenue = 0;
		if (count($reservations) > 0) {
			foreach ($reservations as $key => $value) {
				$bookingRevenue += $value->total;
				//$totalShareRevenue += $checkinShare;
			}
		}

		$tickets = Ticket::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->count();

		//$passRedeem = Reservation::where('partner_id',$partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNotNull('user_pass_id')->orderBy('id', "DESC")->count();

		//$ticket = Ticket::where('partner_id',$partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->orderBy('id', "DESC")->get();
		$ticket = Ticket::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('reservation_id')->whereNull('user_pass_id')->orderBy('id', "DESC")->get();

		$totalDriveUpRevenue = 0;

		if (count($ticket) > 0) {
			foreach ($ticket as $key => $value) {
				$totalDriveUpRevenue += $value->total;
			}
		}

		$result['totalRevenue'] = $bookingRevenue + $passRevenue + $totalDriveUpRevenue;
		//      $result['totalRevenue'] = number_format(($bookingRevenue + $passRevenue + $totalDriveUpRevenue), 2);
		$result['totalPassRevenue'] = sprintf("%.2f", $passRevenue);
		$result['totalBookingRevenue'] = sprintf("%.2f", $bookingRevenue);
		$result['shareRevenue'] =  sprintf("%.2f", $totalShareRevenue);
		$result['totalDriveUpRevenue'] =  sprintf("%.2f", $totalDriveUpRevenue);
		$result['totalCheckinCount'] = $tickets;
		$result['totalPassPurchaseCount'] = count($userPasses);
		$result['totalRredeemPassCount'] = 0;
		return $result;
	}


	public function demoMapcoTicketCheckinCheckout(Request $request)
	{

		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Sorry,Invalid garage.');
		}
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();

		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}
		//check gate api
		/*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }*/
		if (isset($gate) && $gate->gate_type == "entry") {
			$qrCode = MapcoQrcode::with(['reservation.user', 'event', 'eventCategory.eventCategoryEvent.event'])->where("qrcode", $request->ticket_id)->first();
			if (!$qrCode) {
				throw new ApiGenericException('Sorry, No prepaid booking found.');
			} else {
				if ($qrCode->remain_usage == '0' || $qrCode->remain_usage == 0) {
					throw new ApiGenericException('You have already checked-in.');
				}
				if (!isset($qrCode->reservation)) {
					throw new ApiGenericException('Sorry, No prepaid booking found.');
				}
				if ($qrCode->reservation->cancelled_at != '') {
					throw new ApiGenericException('Sorry, Your booking has been cancelled.');
				}
				$today = date("Y-m-d");
				if ($qrCode->event_id != '0') {
					$event = Event::whereDate("start_time", '<=', $today)->whereDate("end_time", '>=', $today)->where('id', $qrCode->event_id)->first();
					if (!$event) {
						throw new ApiGenericException('Sorry, No event found.');
					}
					$event_id = $qrCode->event_id;
				}
				if ($qrCode->event_id == '0' && ($qrCode->event_category_id != '0' || $qrCode->event_category_id != '')) {
					//$today = date("Y-m-d");
					//$today = "2022-12-07";
					$eventCategoryEvent = EventCategoryEvent::leftJoin('events', 'events.id', '=', 'event_category_events.event_id')
						->select('events.id', 'events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode->event_category_id)->first();
					if (!isset($eventCategoryEvent->start_time)) {
						throw new ApiGenericException('Sorry, No event found.');
					}
					$event_id = $eventCategoryEvent->id;
				}

				$user = $qrCode->reservation->user;


				$data['user_id'] = $user->id;
				$data['checkin_gate'] = $request->gate_id;
				$data['facility_id'] = $request->facility_id;
				$data['is_checkin'] = 1;
				$data['ticket_number'] = $this->checkTicketNumber();
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['ticket_security_code'] = rand(1000, 9999);
				$data['partner_id'] = $facility->owner_id;
				$data['reservation_id'] = $qrCode->reservation_id;
				$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
				$data['check_in_datetime'] = $qrCode->reservation->start_timestamp;
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($qrCode->reservation->start_timestamp->addHour($qrCode->reservation->length)->subSeconds(1)));
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($qrCode->reservation->start_timestamp->addHour($qrCode->reservation->length)->subSeconds(1)));
				$data['total'] = $qrCode->reservation->total;
				$data['grand_total'] = $qrCode->reservation->total;
				$data['length'] = $qrCode->reservation->length;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['event_id'] = $event_id;
				$result = Ticket::create($data);

				$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
				$qrCode->save();

				//$reservation->is_ticket = '1';
				//$reservation->save();            

			}

			$facilityName = ucwords($facility->full_name);

			Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			//$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
			//$this->customeReplySms($msg, $user->phone);

			$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


			//check gate api
			/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }*/
			$data = ['msg' => "Welcome to $facilityName. #$result->ticket_number."];
			return $data;
		} elseif (isset($gate) && $gate->gate_type == "exit") {

			$qrCode = MapcoQrcode::with(['reservation.user', 'event', 'eventCategory.eventCategoryEvent.event'])->where("qrcode", $request->ticket_id)->first();
			if (!$qrCode) {
				throw new ApiGenericException('Sorry, No prepaid booking found.');
			}
			$ticket = Ticket::where('reservation_id', $qrCode->reservation->id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
			if (!$ticket) {
				throw new ApiGenericException('Sorry, No checkin found against booking.');
			}
			if ($ticket->is_checkout == '1') {
				throw new ApiGenericException('Sorry, You have already checked out.');
			}

			$ticket->is_checkout = '1';
			$ticket->checkout_gate = $request->gate_id;
			$ticket->checkout_time = date("Y-m-d H:i:s");
			$ticket->save();

			$user = $qrCode->reservation->user;
			$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
			$msg = "Thank you for visiting " . $facilityName . ".";
			$this->customeReplySms($msg, $user->phone);

			//check gate api
			/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }*/
			$data = ['msg' => $msg];
			return $data;
		} else {
			throw new ApiGenericException('Sorry,Invalid garage or ticket details.');
		}
		return "Success";
	}


	public function demoMapcoSessionCheckinCheckout(Request $request)
	{

		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Sorry,Invalid garage.');
		}
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();

		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}
		//check gate api
		/*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }*/

		$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->orderBy("id", "DESC")->first();
		if (!$user) {

			$today = date("Y-m-d");
			$todayEvent = Event::whereDate('start_time', '=', $today)->where("partner_id", $facility->owner_id)->first();
			if (!$todayEvent) {
				throw new ApiGenericException("Sorry, No event found.");
			}
			if ($todayEvent->event_rate == 0 || $todayEvent->event_rate == '') {
				$data['price'] =  $facility->base_event_rate + $facility->processing_fee;
			} else {
				$data['price'] =  $todayEvent->event_rate + $facility->processing_fee;
			}
			$data['id'] = $todayEvent->id;
			return $data;
			//throw new ApiGenericException("Sorry, No prepaid booking found.");
		}
		if (isset($gate) && $gate->gate_type == "entry") {

			$reservation = Reservation::where('user_id', $user->id)->orderBy("id", "DESC")->first();
			if (!$reservation) {
				$today = date("Y-m-d");
				$todayEvent = Event::whereDate('start_time', '=', $today)->where("partner_id", $facility->owner_id)->first();
				if (!$todayEvent) {
					throw new ApiGenericException("Sorry, No event found.");
				}
				if ($todayEvent->event_rate == 0 || $todayEvent->event_rate == '') {
					$data['price'] =  $facility->base_event_rate + $facility->processing_fee;
				} else {
					$data['price'] =  $todayEvent->event_rate + $facility->processing_fee;
				}
				$data['id'] = $todayEvent->id;
				return $data;

				//
			}

			if ($reservation->cancelled_at != '') {
				throw new ApiGenericException("Sorry, Your booking has been cancelled.");
			}

			$qrCode = MapcoQrcode::with(['event', 'eventCategory.eventCategoryEvent.event'])->where("reservation_id", $reservation->id)->where("remain_usage", '>', '0')->get();
			if (count($qrCode) == 0) {
				$ticket = Ticket::where('reservation_id', $reservation->id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
				if ($ticket) {
					throw new ApiGenericException('Sorry, You have already checked-in.');
				}
				$today = date("Y-m-d");
				$todayEvent = Event::whereDate('start_time', '=', $today)->where("partner_id", $facility->owner_id)->first();
				if (!$todayEvent) {
					throw new ApiGenericException("Sorry, No event found.");
				}
				if ($todayEvent->event_rate == 0 || $todayEvent->event_rate == '') {
					$data['price'] =  $facility->base_event_rate + $facility->processing_fee;
				} else {
					$data['price'] =  $todayEvent->event_rate + $facility->processing_fee;
				}
				$data['id'] = $todayEvent->id;
				return $data;
			} else {

				$today = date("Y-m-d");
				if ($qrCode[0]->event_id != '0') {

					$event = Event::whereDate("start_time", '<=', $today)->whereDate("end_time", '>=', $today)->where('id', $qrCode[0]->event_id)->first();
					if (!$event) {
						throw new ApiGenericException('Sorry, No event found.');
					}
					$event_id = $qrCode->event_id;
				}
				if ($qrCode[0]->event_id == '0' && ($qrCode[0]->event_category_id != '0' || $qrCode[0]->event_category_id != '')) {
					//$today = date("Y-m-d");
					//$today = "2022-12-07";
					$eventCategoryEvent = EventCategoryEvent::leftJoin('events', 'events.id', '=', 'event_category_events.event_id')
						->select('events.id', 'events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode[0]->event_category_id)->first();
					if (!isset($eventCategoryEvent->start_time)) {
						throw new ApiGenericException('Sorry, No event found.');
					}
					$event_id = $eventCategoryEvent->id;
				}

				$user = $reservation->user;
				$data['user_id'] = $user->id;
				$data['checkin_gate'] = $request->gate_id;
				$data['facility_id'] = $request->facility_id;
				$data['is_checkin'] = 1;
				$data['ticket_number'] = $this->checkTicketNumber();
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['ticket_security_code'] = rand(1000, 9999);
				$data['partner_id'] = $facility->owner_id;
				//if($reservation){
				$data['reservation_id'] = $reservation->id;
				$data['anet_transaction_id'] = $reservation->anet_transaction_id;
				$data['check_in_datetime'] = $reservation->start_timestamp;
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['total'] = $reservation->total;
				$data['grand_total'] = $reservation->total;
				$data['length'] = $reservation->length;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['event_id'] = $event_id;
				$result = Ticket::create($data);

				$qrCode[0]->remain_usage = $qrCode[0]->remain_usage == 0 ? 0 : $qrCode[0]->remain_usage - 1;
				$qrCode[0]->save();

				//$reservation->is_ticket = '1';
				//$reservation->save();            

				//}
				/*$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
            if($ticket){
              throw new ApiGenericException('You have already checked-in.');
            }*/
			}

			$facilityName = ucwords($facility->full_name);

			Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			//$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
			//$this->customeReplySms($msg, $user->phone);

			$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


			//check gate api
			/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }*/
			$data = ['msg' => "Welcome to $facilityName. #$result->ticket_number."];
			return $data;
			//return "Welcome to $facilityName. #$result->ticket_number.";

		} elseif (isset($gate) && $gate->gate_type == "exit") {
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkout', '0')->orderBy("id", "Desc")->first();
			if (!$ticket) {
				throw new ApiGenericException('Sorry! No checkin found.');
			}

			$ticket->is_checkout = '1';
			$ticket->checkout_gate = $request->gate_id;
			$ticket->checkout_time = date("Y-m-d H:i:s");
			$ticket->save();

			$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
			$msg = "Thank you for visiting " . $facilityName . ".";
			$this->customeReplySms($msg, $user->phone);
			//return $msg;
			$data = ['msg' => $msg];
			return $data;
		} else {
			throw new ApiGenericException('Sorry! Invalid gate details.');
		}
		//return "Success";
	}


	public function demoMapcoDriveupCheckinCheckout(Request $request)
	{
		$this->log->info("Request received --" . json_encode($request));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}
		$facilityName =  ucwords($facility->full_name);
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}

		$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
		if (!$user) {
			$user = User::create(
				[
					'name' => '',
					'email' => '',
					'phone' => '',
					'password' => Hash::make(str_random(60)),
					'anon' => true,
					'user_type' => '5',
					'created_by' => $facility->owner_id,
					'session_id' => $request->session_id,
				]
			);
		}

		if (isset($gate) && $gate->gate_type == "entry") {

			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}

			/*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }*/

			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber();
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['check_in_datetime'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['vp_device_checkin'] = '1';
			$data['partner_id'] = $facility->owner_id;

			$result = Ticket::create($data);

			if (isset($request->payment_details)) {
				$ticket = Ticket::where('id', $result->id)->first();
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Mapco Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = $request->payment_details['processorReference'];
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
				$ticket->save();
			}
			$this->log->info("Mapco driveup checkin done --" . json_encode($result));

			if ($result) {

				//check gate api
				/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }*/
				$response = ["msg" => "Welcome to $facilityName. #$result->ticket_number."];
				return $response;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}

		if (isset($gate) && $gate->gate_type == "exit") {
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if (!$ticket) {
				throw new ApiGenericException('No checkin found for this user.');
			}
			/*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }*/
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$diff_in_hours = $arrival_time->diffInRealHours($from);
			if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
				$diff_in_hours = 2;
			}
			$ticket->checkout_gate = $request->gate_id;
			$ticket->is_checkout = 1;
			$ticket->length = $diff_in_hours;
			$ticket->checkout_time = date('Y-m-d H:i:s');
			if (isset($request->payment_details)) {
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = $request->payment_details['processorReference'];
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
			}

			$result = $ticket->save();
			$this->log->info("Mapco driveup checkout done --" . json_encode($ticket));
			if ($result) {
				//check gate api
				/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            } */
				//"Thank you for visiting ".$facilityName.".";
				$response = ["msg" => "Thank you for visiting " . $facilityName . "."];
				return $response;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
	}


	public function isParkEngageGateAvailable($facility_id, $gate, $phone = '')
	{

		$facility = Facility::where('id', $facility_id)->first();

		$gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

		//check third party gate API
		$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
		if ($gateDetails) {
			if ($gateDetails->host != '') {
				$params = ['gate_id' => $gate->gate];
				$this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
				$response = ParkengageGateApi::isVehicleAvailable($params, $gateDetails->host);
				$this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response" . json_encode($response));
				if ($response['success'] == false) {
					$msg = "The system is not currently available. Please try again later.";
					return $msg;
				}
				if (isset($response['data'][0]) && $response['data'][0] == "true") {
					return true;
					/*$cmd_params = ['gate_id'=>$gate->gate];
                    $this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params". json_encode($cmd_params));
                    $command_response = ParkengageGateApi::openGate($cmd_params, $gateDetails->host);
                    $this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response". json_encode($command_response));
                    if($command_response['success'] == true){
                        if($command_response['data'][0] == "true"){
                            return true;
                        }else{
                            $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                            return $msg;                            
                        }                        
                    }else{
                        $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                        return $msg; 
                    }*/
				} else {

					$msg = "Your vehicle must be in the garage in front of the " . ucwords($gate->gate_type) . " gate.";
					return $msg;
				}
			}
		}
	}

	public function isParkEngageGateOpen($facility_id, $gate, $phone = '')
	{

		$facility = Facility::where('id', $facility_id)->first();

		$gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

		//check third party gate API
		$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
		if ($gateDetails) {
			if ($gateDetails->host != '') {
				$params = ['gate_id' => $gate->gate];
				$this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
				/*$response = ParkengageGateApi::isvehicleAvailable($params, $gateDetails->host);
                $this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response". json_encode($response));
                if($response['success'] == false){
                    $msg = "The system is not currently available. Please try again later.";
                    return $msg; 
                }*/
				//if(isset($response['data'][0]) && $response['data'][0] == "true"){
				$cmd_params = ['gate_id' => $gate->gate];
				$this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params" . json_encode($cmd_params));
				$command_response = ParkengageGateApi::openGate($cmd_params, $gateDetails->host);
				$this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response" . json_encode($command_response));
				if ($command_response['success'] == true) {
					if ($command_response['data'][0] == "true") {
						return true;
					} else {
						$msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
						return $msg;
					}
				} else {
					$msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
					return $msg;
				}
				/*}else{

                    $msg = "Your vehicle must be in the garage in front of the ".ucwords($gate->gate_type)." gate.";
                    return $msg; 
                }*/
			}
		}
	}


	//driveup checkin checkout list
	public function getDriveUpCheckinCheckoutList(Request $request)
	{

		/*$tickets = Ticket::with(['anet_transactions']);  */

		$tickets = Ticket::leftJoin('anet_transactions', 'anet_transactions.id', '=', 'tickets.anet_transaction_id')
			/*->leftJoin('accounts' , 'accounts.id' , '=' , 'assets.account_id')*/

			->select("anet_transactions.anet_trans_id as transaction_id", "ticket_number", "checkin_time"/*\DB::raw("DATE_FORMAT(checkin_time, '%m/%d/%Y %H:%i:%s') as checkin_time")*/, "checkout_time"/*\DB::raw("DATE_FORMAT(checkout_time, '%m/%d/%Y %H:%i:%s') as checkout_time")*/, 'anet_transactions.payment_last_four as payment_last_four', "anet_transactions.card_type as card_type", 'anet_transactions.expiration as expiration', "tickets.total as total_amount", "tickets.total as total_amount", 'anet_transactions.reader_used', "tickets.terminal_id", \DB::raw("anet_transactions.response_message as status"), "tickets.id as ticket_id");


		if ($request->payment_last_four != '') {
			$tickets = $tickets->where('anet_transactions.payment_last_four', $request->payment_last_four);
		}
		if ($request->card_type != '') {
			$tickets = $tickets->where('anet_transactions.card_type', $request->card_type);
		}
		if ($request->expiration != '') {
			$tickets = $tickets->where('anet_transactions.expiration', $request->expiration);
		}
		if ($request->checkin_between != '') {
			$today = date("Y-m-d");
			$checkinTime = explode("-", $request->checkin_between);
			$start_time = $today . " " . date("H:i:s", strtotime($checkinTime[0]));
			$end_time = $today . " " . date("H:i:s", strtotime($checkinTime[1]));
			$tickets = $tickets->where('tickets.checkin_time', '>=', $start_time)->where('tickets.checkin_time', '<=', $end_time);
		}

		$tickets = $tickets->where('partner_id', Auth::user()->id)->where('vp_device_checkin', '1');

		if ($request->sort != '') {
			if ($request->sort == 'transaction_id') {
				$tickets = $tickets->orderBy("anet_transactions.anet_trans_id", $request->sortBy);
			}
			if ($request->sort == 'card_type') {
				$tickets = $tickets->orderBy("anet_transactions.card_type", $request->sortBy);
			}
			if ($request->sort == 'ticket_number') {
				$tickets = $tickets->orderBy("tickets.$request->sort", $request->sortBy);
			}
			if ($request->sort == 'checkin_time') {
				$tickets = $tickets->orderBy("tickets.$request->sort", $request->sortBy);
			}
			if ($request->sort == 'checkout_time') {
				$tickets = $tickets->orderBy("tickets.$request->sort", $request->sortBy);
			}
		} else {
			$tickets = $tickets->orderBy("tickets.id", "DESC");
		}

		/*$authenticated_user = Auth::user();
        $tickets = $tickets->where(function($query) use($authenticated_user) {
            $query->where('partner_id', $authenticated_user->id);
        });*/
		$tickets = $tickets->paginate(20);
		return $tickets;
	}


	public function downloadRevenueExcel(Request $request)
	{

		$month = date('m');

		$from_date  = date('Y-' . $month . '-01');
		$to_date  = date('Y-' . $month . '-t');

		if (isset($request->from_date) && $request->from_date != '') {
			$from_date = $request->from_date;
			$to_date = $request->to_date;
		}
		$result = [];
		if (!isset($request->partner_id)) {
			if (Auth::user()->user_type == '1') {
				$result['totalPassPurchaseCount'] = 0;
				$result['totalCheckinCount'] = 0;
				$result['totalRevenue'] = 0;
				$result['shareRevenue'] = 0;
				$result['totalRredeemPassCount'] = 0;
				return $result;
			} elseif (Auth::user()->user_type == '3') {
				$partner_id = Auth::user()->id;
			} elseif (Auth::user()->user_type == '4') {
				$partner_id = Auth::user()->created_by;
			} else {
				$result['totalPassPurchaseCount'] = 0;
				$result['totalCheckinCount'] = 0;
				$result['totalRevenue'] = 0;
				$result['shareRevenue'] = 0;
				$result['totalRredeemPassCount'] = 0;
				return $result;
			}
		} else {
			$partner_id = $request->partner_id;
		}

		$partner_id = $partner_id;

		$userConfig = UserPaymentGatewayDetail::where('user_id', $partner_id)->first();
		if (!$userConfig) {
			$result['totalPassPurchaseCount'] = 0;
			$result['totalCheckinCount'] = 0;
			$result['totalRevenue'] = 0;
			$result['shareRevenue'] = 0;
			$result['totalRredeemPassCount'] = 0;
			return $result;
		}
		//$checkinShare = $userConfig->checkin_share ? $userConfig->checkin_share : self::SHARE_TICKET_AMOUNT;
		//$passShare = $userConfig->pass_share ? $userConfig->pass_share : self::SHARE_PASS_AMOUNT;
		//$passCheckinShare = $userConfig->pass_checkin_share ? $userConfig->pass_checkin_share : 0;

		//$userPasses = UserPass::with('user')->where('partner_id',$partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->orderBy('id', "DESC")->get();
		$userPasses = $revenueTickets = Reservation::with(['user', 'rate', 'mapcoQrCode.event'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('rate_id', '!=', '0')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->get();
		$passRevenue = 0.00;
		$totalShareRevenue = 0.00;
		/*if(count($userPasses) > 0){  
        foreach ($userPasses as $key => $value) {
            $passRevenue += $value->total;
            $totalShareRevenue += ($passShare + $passCheckinShare) * $value->total_days;
        }
      }*/
		//for booking revenue
		//$reservations = Reservation::with('user')->where('partner_id',$partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('user_pass_id')->whereNull('cancelled_at')->orderBy('id', "DESC")->get();
		$reservations = $revenueTickets = Reservation::with(['user', 'rate', 'mapcoQrCode.event'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('rate_id', '0')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->get();
		$bookingRevenue = 0;
		/*if(count($reservations) > 0){  
          foreach ($reservations as $key => $value) {
              $bookingRevenue += $value->total;
              $totalShareRevenue += $checkinShare;
          }
        }*/

		$ticket = Ticket::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('reservation_id')->whereNull('user_pass_id')->orderBy('id', "DESC")->get();

		$totalDriveUpRevenue = 0;


		$finalCodes1 = [];
		$finalCodes2 = [];
		$finalCodes3 = [];
		$increment1 = 1;
		$increment2 = 1;
		$increment3 = 1;
		$totalRevenue = 0;
		if (count($userPasses) > 0) {
			foreach ($userPasses as $userPass) {
				$finalCodes1[] = [
					'No.' => $increment1,
					'Booking Id' => $userPass->ticketech_code,
					'Pass Rate($)' => $userPass->total,
					'Pass Purchase Date' => date("m/d/Y", strtotime($userPass->created_at)),
					'Pass Type' => isset($userPass->rate->description) ? $userPass->rate->description : '',
					//'Days' => $userPass->total_days,
					'User' => isset($userPass->user->email) ? $userPass->user->email : '',
				];
				$passRevenue += $userPass->total;
				//$totalShareRevenue += ($passShare + $passCheckinShare) * $userPass->total_days; 
				$increment1++;
			}
		}
		if (count($reservations) > 0) {
			foreach ($reservations as $reservation) {
				$finalCodes2[] = [
					'No.' => $increment2,
					'Booking Id' => $reservation->ticketech_code,
					'Booking Amount($)' => $reservation->total,
					'Booking Date' => date("m/d/Y", strtotime($reservation->created_at)),
					'Event' => isset($reservation->mapcoQrCode[0]->event->slug) ? $reservation->mapcoQrCode[0]->event->slug : '',
					'User' => isset($reservation->user->email) ? $reservation->user->email : '',
				];
				$bookingRevenue += $reservation->total;
				//$totalShareRevenue += $checkinShare;
				$increment2++;
			}
		}

		if (count($ticket) > 0) {
			foreach ($ticket as $key => $value) {
				$finalCodes3[] = [
					'No.' => $increment3,
					'Booking Id' => $value->ticket_number,
					'Driveup Amount($)' => $value->total,
					'Driveup Date' => date("m/d/Y", strtotime($value->created_at)),
					//'Days' => $userPass->total_days,
					'User' => isset($value->user->email) ? $value->user->email : '',
				];
				$totalDriveUpRevenue += $value->total;
				$increment3++;
			}
		}


		$finalCodes1[] = [
			'No.' => '',
			'Booking Id' => '',
			'Pass Rate($)' => '',
			'Pass Purchase Date' => '',
			'User' => '',
		];
		$finalCodes1[] = [
			'No.' => '',
			'Booking Id' => '',
			'Pass Rate($)' => '',
			'Pass Purchase Date' => '',
			'User' => '',
		];
		$finalCodes1[] = [
			'No.' => '',
			'Booking Id' => 'Revenue With Pass',
			'Pass Rate($)' => '$' . number_format($passRevenue, 2),
			'Pass Purchase Date' => '',

			'User' => '',
		];
		$finalCodes1[] = [
			'No.' => '',
			'Booking Id' => 'Revenue Without Pass',
			'Pass Rate($)' => '$' . number_format($bookingRevenue, 2),
			'Pass Purchase Date' => '',
			'User' => '',
		];

		$finalCodes1[] = [
			'No.' => '',
			'Booking Id' => 'Revenue With Driveup',
			'Pass Rate($)' => '$' . number_format($totalDriveUpRevenue, 2),
			'Pass Purchase Date' => '',
			'User' => '',
		];

		$finalCodes1[] = [
			'No.' => '',
			'Booking Id' => 'Total Revenue',
			'Pass Rate($)' => '$' . number_format($bookingRevenue + $passRevenue + $totalDriveUpRevenue, 2),
			'Pass Purchase Date' => '',
			'User' => '',
		];

		$finalCodes2[] = [
			'No.' => '',
			'Booking Id' => '',
			'Pass Rate($)' => '',
			'Pass Purchase Date' => '',
			'User' => '',
		];
		$finalCodes2[] = [
			'No.' => '',
			'Booking Id' => '',
			'Pass Rate($)' => '',
			'Pass Purchase Date' => '',
			'User' => '',
		];

		$finalCodes2[] = [
			'No.' => '',
			'Booking Id' => 'Revenue Without Pass',
			'Pass Rate($)' => '$' . number_format($bookingRevenue, 2),
			'Pass Purchase Date' => '',
			'User' => '',
		];
		$finalCodes3[] = [
			'No.' => '',
			'Booking Id' => '',
			'Pass Rate($)' => '',
			'Pass Purchase Date' => '',
			'User' => '',
		];
		$finalCodes3[] = [
			'No.' => '',
			'Booking Id' => '',
			'Pass Rate($)' => '',
			'Pass Purchase Date' => '',
			'User' => '',
		];
		$finalCodes3[] = [
			'No.' => '',
			'Booking Id' => 'Revenue With Driveup',
			'Pass Rate($)' => '$' . number_format($totalDriveUpRevenue, 2),
			'Pass Purchase Date' => '',
			'User' => '',
		];

		if (Auth::user()->user_type == '1') {
			$finalCodes1[] = [
				'No.' => '',
				'Booking Id' => 'ParkEngage Share',
				'Pass Rate($)' => '$' . number_format($totalShareRevenue, 2),
				'Pass Purchase Date' => '',
				'User' => '',
			];
		}

		$excelSheetName = ucwords(str_replace(' ', '', 'Revenue'));

		Excel::create(
			$excelSheetName,
			function ($excel) use ($finalCodes1, $finalCodes2, $finalCodes3, $excelSheetName) {

				// Set the spreadsheet title, creator, and description
				$excel->setTitle($excelSheetName);
				$excel->setCreator('RevenueDetails')->setCompany('ParkEngage');
				$excel->setDescription('List Of Revenue');

				// Build the spreadsheet, passing in the payments array

				$excel->sheet(
					'With Pass',
					function ($sheet) use ($finalCodes1) {
						$sheet->fromArray($finalCodes1, null, 'A1', false, true);
					}
				);

				$excel->sheet(
					'Without Pass',
					function ($sheet) use ($finalCodes2) {
						$sheet->fromArray($finalCodes2, null, 'A1', false, true);
					}
				);

				$excel->sheet(
					'With Driveup',
					function ($sheet) use ($finalCodes3) {
						$sheet->fromArray($finalCodes3, null, 'A1', false, true);
					}
				);
			}
		)->store('xls')->download('xls');
	}


	//Send otp on mobile and Email to user
	public function sendOtp(Request $request)
	{
		//  dd($request->all());
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}

			//  dd($request->all());
			$isValidTicketCode = Reservation::where('ticketech_code', $request->ticketech_code)->first();
			if (!$isValidTicketCode) {
				throw new ApiGenericException('Please Enter a valid booking ID');
			}
			if ($isValidTicketCode->cancelled_at != '') {
				throw new ApiGenericException("Ticket for booking ID" . " " . $request->ticketech_code . " " . "already canceled");
			}

			if ($request->phone != '') {
				$geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
				if ($geoLocation['geoplugin_countryCode'] == 'IN') {
					//$this->countryCode = "+91";
					$this->countryCode = "+1";
				} elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
					$this->countryCode = "+1";
				} else {
					$this->countryCode = "+1";
				}
			}

			//dd($this->countryCode.$request->phone, $request->email, $isValidTicketCode->user_id);
			$isValidateUser = User::where('email', $request->email)->where('phone', $this->countryCode . $request->phone)->where("id", $isValidTicketCode->user_id)->first();
			if (!$isValidateUser) {
				throw new ApiGenericException('Please Enter the valid Email and Phone Number.');
			}

			//send otp
			$generatedOtp = rand(111111, 999999);
			Artisan::queue('mapco:otp-email', array('id' => $request->ticketech_code, 'email' => $request->email, 'otp' => $generatedOtp));

			$otpResp = Otp::where('email', $request->email)->first();
			if (!$otpResp) {
				$otpstatus = Otp::create([
					'email' => $request->email,
					'phone' => $this->countryCode . $request->phone,
					'otp_number' => $generatedOtp
				]);
			} else {
				$otpstatus = Otp::where('email', $request->email)->update(['otp_number' => $generatedOtp]);
			}


			//  return  $otpstatus;
			$msg = "$generatedOtp" . " " . "is OTP for downloading the MAPCO Parking Ticket. DO NOT disclose it to anyone.";

			$phone = $this->countryCode . $request->phone;
			//$phone ="+918178568977";

			// return $this->sendOtpOnMobile($msg, $phone);
			$this->customeReplySms($msg, $phone);

			return "OTP has been sent on the registered email and mobile number.";
			// return $status;


		}
	}






	// validate otp from Email and MObile
	public function confirmOtp(Request $request)
	{

		// dd($request->all());

		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}



			if ($request->phone != '') {
				$geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip=' . $_SERVER['REMOTE_ADDR']));
				if ($geoLocation['geoplugin_countryCode'] == 'IN') {
					//$this->countryCode = "+91";
					$this->countryCode = "+1";
				} elseif ($geoLocation['geoplugin_countryCode'] == 'US') {
					$this->countryCode = "+1";
				} else {
					$this->countryCode = "+1";
				}
			}

			// return $this->countryCode.$request->phone;
			$isOtp = Otp::where('email', $request->email)->first();
			//dd($isOtp);
			if (!$isOtp) {
				throw new ApiGenericException('Otp Not Found.');
			}
			if ($isOtp->otp_number == $request->otp_number) {
				Otp::where('email', $request->email)->where('phone', $this->countryCode . $request->phone)->update(['otp_number' => NUll]);
				$reservation =  Reservation::where('ticketech_code', $request->ticketech_code)->first();
				$reservation['otp_status'] = "OTP Match";
				$reservation['reservation_id'] = $reservation->id;
				return $reservation;
			} else {
				throw new ApiGenericException('Invalid OTP.');
			}
		}
	}

	// save Declined Trans
	public function saveDeclinedTransaction(Request $request)
	{
		//return $request->session_id;
		$this->log->info("Request received for payment declined --" . json_encode($request));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}
		$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
		if (!$user) {
			throw new ApiGenericException("User Not Found.");
		}
		if (isset($request->payment_details)) {
			$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
			$this->log->info("Decline payment details request --" . json_encode($request->payment_details));
			$authorized_anet_transaction = new AuthorizeNetTransaction();
			$authorized_anet_transaction->sent = '1';
			$authorized_anet_transaction->user_id = $user->id;
			$authorized_anet_transaction->total = isset($request->payment_details['TransactionAmount']) ? $request->payment_details['TransactionAmount'] : '';
			$authorized_anet_transaction->name = isset($request->payment_details['MerchantName']) ? $request->payment_details['MerchantName'] : '';
			$authorized_anet_transaction->description = "Payment declined";
			$authorized_anet_transaction->response_message = isset($request->payment_details['ProcessorMessage']) ? $request->payment_details['ProcessorMessage'] : '';
			$authorized_anet_transaction->expiration = isset($request->payment_details['expiry']) ? $request->payment_details['expiry'] : '';
			$authorized_anet_transaction->card_type = isset($request->payment_details['CardType']) ? $request->payment_details['CardType'] : '';
			$authorized_anet_transaction->anet_trans_hash = $request->payment_details['processorReference'];
			$authorized_anet_transaction->ref_id = $request->payment_details['RequesterTransRefNum'];
			$authorized_anet_transaction->anet_trans_id = isset($request->payment_details['TransactionID']) ? $request->payment_details['TransactionID'] : '';
			$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
			$authorized_anet_transaction->method = "card";
			$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
			$authorized_anet_transaction->status_code = $request->payment_details['StatusCode'];
			$authorized_anet_transaction->status_type = $request->payment_details['StatusType'];
			$authorized_anet_transaction->status_message = $request->payment_details['StatusMessage'];
			$authorized_anet_transaction->name = $request->payment_details['CardHolderName'];
			$authorized_anet_transaction->save();
			return "Payment declined data save successfull !";
		} else {
			throw new ApiGenericException("Payment Details Not Found.");
		}
		$this->log->info("Decline payment transaction done --" . json_encode($result));
	}


	public function getBookingUsageDetails(Request $request, $reservation_id)
	{
		if (is_numeric($reservation_id) && $reservation_id > 0 && $reservation_id == round($reservation_id, 0)) {
		} else {
			$reservation_id = base64_decode($reservation_id);
		}
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();

			if (!$secret) {
				throw new ApiGenericException('Invalid partner.');
			}
			$partner_id = $secret->partner_id;

			$reservation = Reservation::with(['facility', 'user', 'userPass', 'rate', 'mapcoQrCode.event', 'mapcoQrCode.eventCategory.eventCategoryEvent.event', 'transaction', 'ticket', 'flightDetails'])->where('partner_id', $partner_id)->where('id', $reservation_id)->first();
		} else {
			if (isset(Auth::user()->user_type)) {
				if (Auth::user()->user_type == '1') {
					$reservation = Reservation::with(['facility', 'user', 'userPass', 'rate', 'mapcoQrCode.event', 'mapcoQrCode.eventCategory.eventCategoryEvent.event', 'transaction', 'ticket', 'flightDetails'])->where('id', $reservation_id)->first();
				} else {
					if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
						$partner_id = Auth::user()->created_by;
					} else if (Auth::user()->user_type == self::SUBORDINATE) {
						$admin_partner_id = Auth::user()->created_by;
						if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
							$partner_id = isset($request->partner_id) ? $request->partner_id : '';
						} else {
							$partner_id = Auth::user()->created_by;
						}
					} else if (Auth::user()->user_type == self::PARTNER) {
						$partner_id = Auth::user()->id;
					}

					$reservation = Reservation::with(['facility', 'user', 'userPass', 'rate', 'mapcoQrCode.event', 'mapcoQrCode.eventCategory.eventCategoryEvent.event', 'transaction', 'ticket', 'flightDetails'])->where('id', $reservation_id);
					$admin_partner_id = Auth::user()->created_by;
					if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
						if ($request->partner_id != '') {
							$partner_id = $request->partner_id;
							$reservation = $reservation->where('partner_id', $partner_id);
						}
					} else {
						$reservation = $reservation->where('partner_id', $partner_id);
					}
					$reservation = $reservation->first();
				}
			} else {
				$reservation = Reservation::with(['facility', 'user', 'userPass', 'rate', 'mapcoQrCode.event', 'mapcoQrCode.eventCategory.eventCategoryEvent.event', 'transaction', 'ticket', 'flightDetails'])->where('id', $reservation_id)->first();
			}
		}

		if (!$reservation) {
			throw new ApiGenericException("Invalid booking details.");
		}

		$today = date('Y-m-d h:i:s');
		if (isset($reservation->mapcoQrCode)) {

			$qrCodes = [];
			$qrCodes1 = [];
			$checkin = [];
			foreach ($reservation->mapcoQrCode as $key => $value) {
				$value->discount_amount = $value->discount;
				if ($value->event_id != '0') {
					$ticket = Ticket::where("reservation_id", $reservation_id)->where("event_id", $value->event_id)->first();

					$qrCodes[] = $ticket;
					$value->qrCodes = $qrCodes;

					if ($value->remain_usage != '0') {

						$event = Event::where('id', $value->event_id)->whereDate('start_time', '=', date('Y-m-d'))->first();
						if ($event) {
							$value['is_checkin'] = 1;
						} else {
							$value['is_checkin'] = 0;
						}
					}
				}
				if ($value->event_id == '0') {
					$ticket = Ticket::where("reservation_id", $reservation_id)->get();
					$qrCodes[] = $ticket;
					$value->qrCodes = $qrCodes;
				}
			}
		}
		$reservation->discount_amount = $reservation->discount;

		// Vehical Details Added
		if ($reservation && isset($reservation->vehicle_id) && $reservation->vehicle_id > 0) {
			$reservation['vehical_details'] = PermitVehicle::where('id', $reservation->vehicle_id)->first();
		}

		$reservation['extend_reservation'] = $reservation->reservationExtend();

		$facility_configurations = FacilityConfiguration::where('facility_id', $reservation->facility->id)->first();
		$reservation['facility_configurations'] = $facility_configurations;
		$payment_status = "";
		if (!empty($reservation->anet_transaction_id)) {
			$payment_status = "Processed";
		} else if ((empty($reservation->anet_transaction_id) || is_null($reservation->anet_transaction_id)) && (!$reservation->is_charged)) {
			if (empty($reservation->promocode)) {
				$payment_status = "Authorized";
			} else {
				$payment_status = "N/A";
			}
		} else {
			$payment_status = "Failed";
		}
		$reservation['payment_status'] = $payment_status;

		// check for card exist or not
		$cardCheck = $cardStatus = null;
		if (empty($reservation->event_id) && !$reservation->thirdparty_integration_id) {
			$payment_type_id = $reservation->facility->FacilityPaymentDetails->facility_payment_type_id;
			if (!isset($reservation->transaction)) {
				$datacapTransaction = DatacapTransaction::select('*', 'expiry as expiration', 'card_last_four as payment_last_four')->where('reservation_id', $reservation_id)->first();
				if ($datacapTransaction) {
					$reservation->setRelation('transaction', $datacapTransaction);
					// check for card exist or not
					$card_last_four = $datacapTransaction->payment_last_four;
					$expiry = $datacapTransaction->expiry;
				}
			} else {
				$card_last_four = $reservation->transaction->payment_last_four;
				$expiry = $reservation->transaction->expiration;
			}
			// dd($payment_type_id, $card_last_four, $expiry, $reservation->user_id, $reservation->facility);
			if (empty($reservation->on_behalf) && empty($reservation->promocode) && $reservation->user->anon == '0') {
				$cardCheck = GatewayHelper::getRegisterUserProfileId($payment_type_id, $card_last_four, $expiry, $reservation->user_id, $reservation->facility);
				$cardStatus = ($cardCheck) ? 0 : 1;  // 0 => Not Deleted, 1 => Card Deleted 
			}
			$reservation['is_card_deleted'] = $cardStatus ?? 1;
		}

		return $reservation;
	}

	public function getBookingListDownload(Request $request)
	{
		$this->log->info("Download PDF Request -- " . json_encode($request->all()));
		if (isset($request->partner_id)) {
			$partner_id = $request->partner_id;
		} else {
			if (Auth::user()->user_type == '1') {
				$result  = [];
				$result["total"] = 0;
				$result["per_page"] = 20;
				$result["current_page"] = 1;
				$result["last_page"] = 1;
				$result["next_page_url"] = Null;
				$result["prev_page_url"] = Null;
				$result["from"] = Null;
				$result["to"] = Null;
				$result["data"] = [];
				return $result;
			} else if (Auth::user()->user_type == '12') {
				$partner_id = Auth::user()->created_by;
			} else if (Auth::user()->user_type == '4') {
				$partner_id = Auth::user()->created_by;
			} else {
				$partner_id = Auth::user()->id;
			}
		}

		$month = date("m");
		$from_date  = date('Y-' . $month . '-01');
		$to_date  = date('Y-' . $month . '-t');
		$mid_date  = date('Y-' . $month . '-15');
		$midDateString = strtotime($mid_date);
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		if (isset($request->from_date) && $request->from_date != '') {
			$from_date = $request->from_date;
			$to_date = $request->to_date;
		}

		$today = $request->from_date;
		$todayQrCode = [];
		if ($request->booking_type == '0') {
		} else {
			if ($request->booking_type == '') {
				$event = Event::whereDate('start_time', '=', $from_date)->where("is_active", '1')->get();
			} else {
				$event = Event::whereDate('start_time', '>=', $from_date)->whereDate('start_time', '<=', $to_date)->where("is_active", '1')->get();
			}
			$todayQrCode = [];
			if (count($event) > 0) {
				foreach ($event as $k => $v) {
					$qrCode = MapcoQrcode::where("event_id", $v->id)->get();
					if (count($qrCode) > 0) {
						foreach ($qrCode as $key => $value) {
							$todayQrCode[] = $value->reservation_id;
						}
					}
				}
			} else {
				if (isset($request->booking_type) && $request->booking_type == '') {
					$todayQrCode[] = 0;
				}
			}
		}

		//$reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->whereNotNull('anet_transaction_id');
		//  ***********commented By Sunil*********************
		$reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'facility', 'transaction']);
		if (isset($request->search) && $request->search != '') {
			// $reservation = $reservation->where('ticketech_code', 'like', "%$request->search%")->where('partner_id', $partner_id);
			$reservation = $reservation->where(function ($query) use ($request, $partner_id) {
				$query->where('ticketech_code', "like", "%" . $request->search . "%");
				// $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%");
				$query->orWhere("license_plate", 'like', "%$request->search%");
				// $query->orWhere("users.phone", 'like', "%$request->search%");
				// $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%");
				$query->Where("partner_id", $partner_id);
			});
			$reservation = $reservation->orWhereHas(
				'user',
				function ($query) use ($request, $partner_id) {
					$query
						->where('phone', 'like', "%{$request->search}%")
						->orWhere('email', 'like', "%{$request->search}%")
						->where('created_by', $partner_id);
				}
			);
			$reservation = $reservation->orWhereHas(
				'transaction',
				function ($query) use ($request) {
					$query
						->where('ref_id', 'like', "%{$request->search}%");
				}
			);
		}
		if ($request->booking_type == '0') {
			$reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
		} elseif ($request->booking_type == '2') {
			$reservation = $reservation->whereDate('end_timestamp', '>=', $from_date)->whereDate('end_timestamp', '<=', $to_date);
		} else {
			$reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
		}
		$reservation = $reservation->where(function ($query) use ($partner_id, $todayQrCode) {
			$query->where('partner_id', $partner_id);
			if (count($todayQrCode) > 0) {
				// Lokesh: 5-Aug-2024 Needs to discuss
				// $query->whereIn('id', $todayQrCode);
			}
		});
		if ($request->sort != '') {
			$reservation = $reservation->orderBy($request->sort, $request->sortBy);
		} else {
			$reservation = $reservation->orderBy('id', 'DESC');
		}

		$reservation = $reservation->get();

		if ($request->booking_type == '0') {
		} else {
			if (count($reservation) > 0) {
				foreach ($reservation as $key => $value) {
					if (isset($value->mapcoQrCode)) {
						foreach ($value->mapcoQrCode as $k => $v) {
							if ($v->event_id != '0') {
								//$event = Event::where("id",$v->event_id)->where("is_active", '1')->first();
								if (count($event) > 0) {
									foreach ($event as $eventKey => $eventValue) {
										if (strtotime(date("Y-m-d", strtotime($eventValue->start_time))) == strtotime($today)) {
											$reservation[$key]['start_timestamp'] = $eventValue->start_time;
										}
									}
								}
							}
						}
					}
				}
			}
		}

		if ($request->sort == 'phone') {
			if (count($reservation) > 0) {
				if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->phone > $reservation[$j]['user']->phone) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				} else {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->phone < $reservation[$j]['user']->phone) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				}
			}
		}
		if ($request->sort == 'email') {
			if (count($reservation) > 0) {
				if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->email > $reservation[$j]['user']->email) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				} else {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->email < $reservation[$j]['user']->email) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				}
			}
		}
		if (count($reservation) > 0) {
			$color = "#2C4293";
			$headerData = array(
				'date' => date('d M, Y', strtotime($from_date)) . '-' . date('d M, Y', strtotime($to_date)),
				'color' => $color,
				'printDate' => date('d M, Y'),
			);
			$settings = BrandSetting::where('user_id', $partner_id)->first();
			$html = view("mapco.booking-details", ["reservation" => $reservation, 'brand_setting' => $settings, 'headerData' => $headerData])->render();
			$image = app()->make(Pdf::class);
			$pdf = $image->getOutputFromHtmlString($html);
			return $pdf;
		} else {
			return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "No record found."]], 500);
		}
	}


	public function checkinCheckoutDownloadPdf(Request $request)
	{

		$owner_id = Auth::user()->id;

		$month = date("m");
		$from_date  = date('Y-' . $month . '-01');
		$to_date  = date('Y-' . $month . '-t');
		$mid_date  = date('Y-' . $month . '-15');
		$midDateString = strtotime($mid_date);
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		if (isset($request->from_date) && $request->from_date != '') {
			$from_date = date('Y-m-d', strtotime($request->from_date));
			$to_date = date('Y-m-d', strtotime($request->to_date));
		}
		$type = $request->type;

		$settings = BrandSetting::where('user_id', $owner_id)->first();
		//return $brandsettings;
		//$ticket_checkin = '';
		//$ticket_checkout = '';
		//$ticket_driveup = '';

		$ticket_checkin = [];
		$ticket_checkout = [];
		$ticket_driveup = [];

		$ticket = Ticket::with(['reservation', 'facility', 'user', 'userPass', 'transaction']);
		if ($request->search) {
			$ticket = $ticket->where('ticket_number', 'like', "%$request->search%")->orWhere('license_plate', 'like', "%$request->search%");
			$ticket = $ticket->orWhereHas(
				'user',
				function ($query) use ($request) {
					$query
						->where('phone', 'like', "%{$request->search}%")
						->orWhere('email', 'like', "%{$request->search}%")
						->where('partner_id', Auth::user()->id);
				}
			);
			$ticket = $ticket->orWhereHas(
				'reservation',
				function ($query) use ($request) {
					$query->where('ticketech_code', 'like', "%{$request->search}%");
				}
			);
		}

		if ($type == self::TYPE_CHECKIN) {
			$ticket_checkin = $ticket->where('partner_id', $owner_id)->where('checkout_without_checkin', '0')->where('vp_device_checkin', '0'); /* ->where('vp_device_checkin', '0') */
			$ticket_checkin = $ticket_checkin->whereDate('checkin_time', '>=', $from_date);
			$ticket_checkin = $ticket_checkin->where(function ($query) use ($to_date) {
				$query->whereDate('checkin_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
			});
			if ($request->sort != '') {
				$ticket_checkin = $ticket_checkin->orderBy($request->sort, $request->sortBy);
			} else {
				$ticket_checkin = $ticket_checkin->orderBy("id", "DESC");
			}
			$ticket_checkin = $ticket_checkin->get();
		} else if ($type == self::TYPE_CHECKOUT) {
			$ticket_checkout = $ticket->where('partner_id', $owner_id)->whereDate('checkout_time', '>=', $from_date)->whereDate('checkout_time', '<=', $to_date)->where('checkout_without_checkin', '1');
			if ($request->sort != '') {
				$ticket_checkout = $ticket_checkout->orderBy($request->sort, $request->sortBy);
			} else {
				$ticket_checkout = $ticket_checkout->orderBy("id", "DESC");
			}
			$ticket_checkout = $ticket_checkout->get();
		} else if ($type == self::TYPE_DRIVEUP) {
			$ticket_driveup = $ticket->where('partner_id', $owner_id)/*->whereDate('checkin_time','>=',$from_date)->whereDate('checkout_time','<=',$to_date)*/->whereNull('reservation_id')->whereNull('user_pass_id')->where('vp_device_checkin', '1');
			$ticket_driveup = $ticket_driveup->whereDate('checkin_time', '>=', $from_date);
			$ticket_driveup = $ticket_driveup->where(function ($query) use ($to_date) {
				$query->whereDate('checkin_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
			});
			if ($request->sort != '') {
				$ticket_driveup = $ticket_driveup->orderBy($request->sort, $request->sortBy);
			} else {
				$ticket_driveup = $ticket_driveup->orderBy("id", "DESC");
			}
			$ticket_driveup = $ticket_driveup->get();
		} else if ($type == self::TYPE_ALL) {

			$ticket_checkin  = Ticket::with(['reservation', 'facility', 'user', 'userPass', 'transaction'])->where('partner_id', $owner_id)/*->whereDate('checkin_time','>=',$from_date)->whereDate('checkin_time','<=',$to_date)*/->where('checkout_without_checkin', '0')->where('vp_device_checkin', '0');
			$ticket_checkin = $ticket_checkin->whereDate('checkin_time', '>=', $from_date);
			$ticket_checkin = $ticket_checkin->where(function ($query) use ($to_date) {
				$query->whereDate('checkin_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
			});
			if ($request->sort != '') {
				$ticket_checkin = $ticket_checkin->orderBy($request->sort, $request->sortBy);
			} else {
				$ticket_checkin = $ticket_checkin->orderBy("id", "DESC");
			}
			$ticket_checkin = $ticket_checkin->get();

			$ticket_checkout = Ticket::with(['reservation', 'facility', 'user', 'userPass', 'transaction'])->where('partner_id', $owner_id)->whereDate('checkout_time', '>=', $from_date)->whereDate('checkout_time', '<=', $to_date)->where('checkout_without_checkin', '1');
			if ($request->sort != '') {
				$ticket_checkout = $ticket_checkout->orderBy($request->sort, $request->sortBy);
			} else {
				$ticket_checkout = $ticket_checkout->orderBy("id", "DESC");
			}
			$ticket_checkout = $ticket_checkout->get();

			$ticket_driveup = Ticket::with(['reservation', 'facility', 'user', 'userPass', 'transaction'])->where('partner_id', $owner_id)/*->whereDate('checkin_time','>=',$from_date)->whereDate('checkout_time','<=',$to_date)*/->whereNull('reservation_id')->whereNull('user_pass_id')->where('vp_device_checkin', '1');
			$ticket_driveup = $ticket_driveup->whereDate('checkin_time', '>=', $from_date);
			$ticket_driveup = $ticket_driveup->where(function ($query) use ($to_date) {
				$query->whereDate('checkin_time', '<=', $to_date)/*>orWhereNull("checkout_time")*/;
			});
			if ($request->sort != '') {
				$ticket_driveup = $ticket_driveup->orderBy($request->sort, $request->sortBy);
			} else {
				$ticket_driveup = $ticket_driveup->orderBy("id", "DESC");
			}
			$ticket_driveup = $ticket_driveup->get();
		}
		if ((count($ticket_checkin) > 0) || (count($ticket_checkout) > 0) || (count($ticket_driveup) > 0)) {
			$html = view("download.checkin-checkout", ["ticket_checkin" => $ticket_checkin, "ticket_checkout" => $ticket_checkout, "ticket_driveup" => $ticket_driveup, 'brand_setting' => $settings, "request_param" => $request])->render();
			$image = app()->make(Pdf::class);
			$pdf = $image->getOutputFromHtmlString($html);
			return $pdf;
		} else {
			throw new ApiGenericException('Sorry! No Data Found.');
		}
	}

	// Download Excel
	public function checkinCheckoutDownloadExcel(Request $request)
	{
		$owner_id = Auth::user()->id;

		$ticket = $checkouticket = $ticketdriveup = $all = [];

		$month = date("m");
		$from_date  = date('Y-' . $month . '-01');
		$to_date  = date('Y-' . $month . '-t');
		$mid_date  = date('Y-' . $month . '-15');
		$midDateString = strtotime($mid_date);
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		if (isset($request->from_date) && $request->from_date != '') {
			$from_date = date('Y-m-d', strtotime($request->from_date));
			$to_date = date('Y-m-d', strtotime($request->to_date));
		}
		$type = $request->type;

		//return $brandsettings;
		$settings = BrandSetting::where('user_id', $owner_id)->first();

		$ticket_checkin = [];
		$ticket_checkout = [];
		$ticket_driveup = [];
		$ticket = Ticket::with(['reservation', 'facility', 'user', 'userPass', 'transaction']);

		if ($request->search != '') {
			$ticket = $ticket->where('ticket_number', 'like', "%$request->search%")->orWhere('license_plate', 'like', "%$request->search%");
			$ticket = $ticket->orWhereHas(
				'user',
				function ($query) use ($request) {
					$query
						->where('phone', 'like', "%{$request->search}%")
						->orWhere('email', 'like', "%{$request->search}%")
						->where('partner_id', Auth::user()->id);
				}
			);
			$ticket = $ticket->orWhereHas(
				'reservation',
				function ($query) use ($request) {
					$query->where('ticketech_code', 'like', "%{$request->search}%");
				}
			);
		}

		if ($type == self::TYPE_CHECKIN) {
			$ticket = $ticket->where('partner_id', $owner_id)->where('checkout_without_checkin', '0')->where('vp_device_checkin', '0');
			$ticket = $ticket->whereDate('checkin_time', '>=', $from_date);
			$ticket = $ticket->where(function ($query) use ($to_date) {
				$query->whereDate('checkin_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
			});
			if ($request->sort != '') {
				$ticket = $ticket->orderBy($request->sort, $request->sortBy);
			} else {
				$ticket = $ticket->orderBy("id", "DESC");
			}
			$ticket = $ticket->get();
		} else if ($type == self::TYPE_CHECKOUT) {
			$checkouticket = $ticket->where('partner_id', $owner_id)->whereDate('checkout_time', '>=', $from_date)->whereDate('checkout_time', '<=', $to_date)->where('checkout_without_checkin', '1');
			if ($request->sort != '') {
				$checkouticket = $checkouticket->orderBy($request->sort, $request->sortBy);
			} else {
				$checkouticket = $checkouticket->orderBy("id", "DESC");
			}
			$checkouticket = $checkouticket->get();
		} else if ($type == self::TYPE_DRIVEUP) {
			$ticketdriveup = $ticket->where('partner_id', $owner_id)/*->whereDate('checkin_time','>=',$from_date)->whereDate('checkout_time','<=',$to_date)*/->whereNull('reservation_id')->whereNull('user_pass_id')->where('vp_device_checkin', '1');
			$ticketdriveup = $ticketdriveup->whereDate('checkin_time', '>=', $from_date);
			$ticketdriveup = $ticketdriveup->where(function ($query) use ($to_date) {
				$query->whereDate('checkin_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
			});
			if ($request->sort != '') {
				$ticketdriveup = $ticketdriveup->orderBy($request->sort, $request->sortBy);
			} else {
				$ticketdriveup = $ticketdriveup->orderBy("id", "DESC");
			}
			$ticketdriveup = $ticketdriveup->get();
		} else if ($type == self::TYPE_ALL) {
			$ticket = Ticket::with('reservation', 'facility', 'user', 'userPass')->where('partner_id', $owner_id)->whereDate('checkin_time', '>=', $from_date)->whereDate('checkin_time', '<=', $to_date)->where('checkout_without_checkin', '0')->where('vp_device_checkin', '0');
			if ($request->sort != '') {
				$ticket = $ticket->orderBy($request->sort, $request->sortBy);
			} else {
				$ticket = $ticket->orderBy("id", "DESC");
			}
			$ticket = $ticket->get();
			$checkouticket =   Ticket::with('reservation', 'facility', 'user', 'userPass')->where('partner_id', $owner_id)->whereDate('checkout_time', '>=', $from_date)->whereDate('checkout_time', '<=', $to_date)->where('checkout_without_checkin', '1');
			if ($request->sort != '') {
				$checkouticket = $checkouticket->orderBy($request->sort, $request->sortBy);
			} else {
				$checkouticket = $checkouticket->orderBy("id", "DESC");
			}
			$checkouticket = $checkouticket->get();
			$ticketdriveup = Ticket::with('reservation', 'facility', 'user', 'userPass', 'transaction')->where('partner_id', $owner_id)/*->whereDate('checkin_time','>=',$from_date)->whereDate('checkout_time','<=',$to_date)*/->whereNull('reservation_id')->whereNull('user_pass_id')->where('vp_device_checkin', '1');
			$ticketdriveup = $ticketdriveup->whereDate('checkin_time', '>=', $from_date);
			$ticketdriveup = $ticketdriveup->where(function ($query) use ($to_date) {
				$query->whereDate('checkin_time', '<=', $to_date)/*>orWhereNull("checkout_time")*/;
			});
			if ($request->sort != '') {
				$ticketdriveup = $ticketdriveup->orderBy($request->sort, $request->sortBy);
			} else {
				$ticketdriveup = $ticketdriveup->orderBy("id", "DESC");
			}
			$ticketdriveup = $ticketdriveup->get();
		}
		//return $ticket;
		$excelSheetName = ucwords(str_replace(' ', '', 'CheckinCheckoutExcel'));
		$finalCodes1 = [];
		$finalCodes2 = [];
		$finalCodes3 = [];
		$increment1 = 1;
		$increment2 = 1;
		$increment3 = 1;
		if (count($ticket) > 0) {
			foreach ($ticket as $val) {
				$finalCodes1[] = [
					'No.' => $increment1,
					'Ticket' => $val['ticket_number'],
					'Booking Id' => $val->reservation['ticketech_code'],
					'Check-In At' => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
					'Check-Out At' => $val->checkout_time != '' ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '',
					'Email' => $val->user->email,
					'Phone' => $val->user->phone,
				];

				$increment1++;
			}
		}

		if (isset($checkouticket) && count($checkouticket) > 0) {
			foreach ($checkouticket as $val) {
				$finalCodes2[] = [
					'No.' => $increment2,
					'Ticket' => $val['ticket_number'],
					'Booking Id' => $val->reservation['ticketech_code'],
					//'Check-In At' => '',
					'Check-Out At' => $val->checkout_time != '' ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '',
					'Email' => $val->user->email,
					'Phone' => $val->user->phone,
				];

				$increment2++;
			}
		}
		if (isset($ticketdriveup) && count($ticketdriveup) > 0) {
			foreach ($ticketdriveup as $val) {
				$finalCodes3[] = [
					'No.' => $increment3,
					'Ticket' => $val['ticket_number'],
					'Check-In At' => date("m/d/Y, h:i A", strtotime($val->checkin_time)),
					'Check-Out At' => $val->checkout_time != '' ? date("m/d/Y, h:i A", strtotime($val->checkout_time)) : '',
					'Card No' => $val->transaction['payment_last_four'],
					'Card Type' => $val->transaction['card_type'],
					'Card Expiry' => $val->transaction['expiration'],
					'Transaction Amount' => $val->transaction['total'],
					'Reader Used' => $val->transaction['reader_used'],
					'Terminal Id' => $val['terminal_id'],
				];

				$increment3++;
			}
		}






		Excel::create(
			$excelSheetName,
			function ($excel) use ($finalCodes1, $finalCodes2, $finalCodes3, $excelSheetName) {

				// Set the spreadsheet title, creator, and description
				$excel->setTitle($excelSheetName);
				$excel->setCreator('RevenueDetails')->setCompany('ParkEngage');
				$excel->setDescription('List Of Checkin');


				if (empty($finalCodes1) && empty($finalCodes2) && empty($finalCodes3)) {

					throw new ApiGenericException('Sorry! No Data Found.');
				} else {
					if (isset($finalCodes1) && !empty($finalCodes1)) {
						$excel->sheet(
							'Check-In Check-Out',
							function ($sheet) use ($finalCodes1) {
								$sheet->fromArray($finalCodes1, null, 'A1', false, true);
							}
						);
					}

					if (isset($finalCodes2) && !empty($finalCodes2)) {
						$excel->sheet(
							'Only Check-Out',
							function ($sheet) use ($finalCodes2) {
								$sheet->fromArray($finalCodes2, null, 'A1', false, true);
							}
						);
					}

					if (isset($finalCodes3) && !empty($finalCodes3)) {
						$excel->sheet(
							'Drive-Up',
							function ($sheet) use ($finalCodes3) {
								$sheet->fromArray($finalCodes3, null, 'A1', false, true);
							}
						);
					}
				}
			}
		)->store('xls')->download('xls');
	}

	// Vijay : 17-01-2025
	public function getBookingListDownloadExcel(Request $request)
	{

		$this->log->info("Download Excell Request -- " . json_encode($request->all()));
		if ($request->email_send == '1') {
			// check if date is Invalid 
			$fdate = Carbon::parse($request->from_date);
			$todate = Carbon::parse($request->to_date);
			if (!$todate->gte($fdate)) {
				$response['status'] = false;
				$response['message'] = 'Please select valid date.';
				return response()->json($response, 200);
			}
			try {
				return $this->dispatchJobForSendingEmail($request, $request->partner_id, $request->facility_id);
			} catch (\Throwable $e) {
				$this->log->info('dispatchJobForSendingEmail Exception :' . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
				throw new ApiGenericException('Somthing went wrong while sending email.');
			}
		}

		if (isset($request->partner_id)) {
			$partner_id = $request->partner_id;
		} else {
			if (Auth::user()->user_type == '1') {
				$result  = [];
				$result["total"] = 0;
				$result["per_page"] = 20;
				$result["current_page"] = 1;
				$result["last_page"] = 1;
				$result["next_page_url"] = Null;
				$result["prev_page_url"] = Null;
				$result["from"] = Null;
				$result["to"] = Null;
				$result["data"] = [];
				//return $result;
			} else if (Auth::user()->user_type == '12') {
				$partner_id = Auth::user()->created_by;
			} elseif (Auth::user()->user_type == '4') {
				$partner_id = Auth::user()->created_by;
			} else {
				$partner_id = Auth::user()->id;
			}
		}
		$this->log->info("Partner ID -- " . $partner_id);

		$month = date("m");
		$from_date  = date('Y-' . $month . '-01');
		$to_date  = date('Y-' . $month . '-t');
		$mid_date  = date('Y-' . $month . '-15');
		$midDateString = strtotime($mid_date);
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		if (isset($request->from_date) && $request->from_date != '' && isset($request->to_date) && $request->to_date != '') {
			$from_date = $request->from_date;
			$to_date = $request->to_date;
		}

		$this->log->info("From Date -- $from_date and  To Date: $to_date");


		$today = $request->from_date;
		$todayQrCode = [];
		if ($request->booking_type == '0') {
		} else {

			if ($request->booking_type == '') {
				//->where("is_active", '1')
				$event = Event::whereDate('start_time', '=', $from_date)->get();
			} else {

				//->where("is_active", '1')
				$event = Event::whereDate('start_time', '>=', $from_date)->whereDate('start_time', '<=', $to_date)->get();
			}
			$todayQrCode = [];
			if (count($event) > 0) {
				foreach ($event as $k => $v) {
					$qrCode = MapcoQrcode::where("event_id", $v->id)->get();
					if (count($qrCode) > 0) {
						foreach ($qrCode as $key => $value) {
							$todayQrCode[] = $value->reservation_id;
						}
					}
				}
			} else {
				if ($request->booking_type == '') {
					$todayQrCode[] = 0;
				}
			}
		}
		$this->log->info("Reached 111");
		// dd($request->booking_type, $todayQrCode);
		// $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->whereNotNull('anet_transaction_id');
		$reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'facility', 'transaction']);
		// if (!$reservation->transaction) {
		//   $datacapTransaction = DatacapTransaction::select('*', 'expiry as expiration', 'card_last_four as payment_last_four')->where('reservation_id', $reservation_id)->first();
		//   if ($datacapTransaction) {
		//     $reservation->setRelation('transaction', $datacapTransaction);
		//   }
		// }
		if (isset($request->search) && $request->search != '') {
			//$reservation = $reservation->where('ticketech_code', 'like', "%$request->search%")->where('partner_id', $partner_id);
			$reservation = $reservation->where(function ($query) use ($request, $partner_id) {
				$query->where('ticketech_code', "like", "%" . $request->search . "%");
				// $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%");
				$query->orWhere("license_plate", 'like', "%$request->search%");
				// $query->orWhere("users.phone", 'like', "%$request->search%");
				// $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%");
				$query->Where("partner_id", $partner_id);
			});
			$reservation = $reservation->orWhereHas(
				'user',
				function ($query) use ($request, $partner_id) {
					$query
						->where('phone', 'like', "%{$request->search}%")
						->orWhere('email', 'like', "%{$request->search}%")
						->where('created_by', $partner_id);
				}
			);
			$reservation = $reservation->orWhereHas(
				'transaction',
				function ($query) use ($request) {
					$query
						->where('ref_id', 'like', "%{$request->search}%");
				}
			);
		}
		$this->log->info("Reached 222");

		if ($request->booking_type == '0') {
			if (isset($request->from_date) && $request->from_date != '' && isset($request->to_date) && $request->to_date != '') {
				$reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
			}
		} elseif ($request->booking_type == '2') {
			if (isset($request->from_date) && $request->from_date != '' && isset($request->to_date) && $request->to_date != '') {
				$reservation = $reservation->whereDate('end_timestamp', '>=', $from_date)->whereDate('end_timestamp', '<=', $to_date);
			}
		} else {
			if (isset($request->from_date) && $request->from_date != '' && isset($request->to_date) && $request->to_date != '') {
				$reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
			}
		}
		if ($request->facility_id) {
			$reservation = $reservation->where('facility_id', $request->facility_id);
		}

		$this->log->info("Reached 444");
		//dd($todayQrCode);
		$reservation = $reservation->where(function ($query) use ($partner_id, $todayQrCode) {
			$query->where('partner_id', $partner_id);
			if (count($todayQrCode) > 0) {
				// $query->whereIn('id', $todayQrCode);
			}
		});
		$this->log->info("Reached 555");

		if ($request->sort != '') {
			$reservation = $reservation->orderBy($request->sort, $request->sortBy);
		} else {
			$reservation = $reservation->orderBy('id', 'DESC');
		}
		$this->log->info("Reached 666");

		$reservation = $reservation->get();
		// dd(count($reservation));
		if ($request->booking_type == '0') {
		} else {
			$this->log->info("Reservation Count: " . count($reservation));
			if (count($reservation) > 0) {
				foreach ($reservation as $key => $value) {
					if (isset($value->mapcoQrCode)) {
						$this->log->info("No mapcoQrCode: " . json_encode($value->mapcoQrCode));
						foreach ($value->mapcoQrCode as $k => $v) {
							if ($v->event_id != '0') {
								//$event = Event::where("id",$v->event_id)->where("is_active", '1')->first();
								if (count($event) > 0) {
									foreach ($event as $eventKey => $eventValue) {
										if (strtotime(date("Y-m-d", strtotime($eventValue->start_time))) == strtotime($today)) {
											$reservation[$key]['start_timestamp'] = $eventValue->start_time;
										}
									}
								}
							}
						}
					} else {
						$this->log->info("No mapcoQrCode:");
					}
				}
			}
		}

		if ($request->sort == 'phone') {
			if (count($reservation) > 0) {
				if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->phone > $reservation[$j]['user']->phone) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				} else {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->phone < $reservation[$j]['user']->phone) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				}
			}
		}
		if ($request->sort == 'email') {
			if (count($reservation) > 0) {
				if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->email > $reservation[$j]['user']->email) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				} else {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->email < $reservation[$j]['user']->email) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				}
			}
		}

		$excelSheetName = ucwords(str_replace(' ', '', 'Booking Details Excel'));
		$finalCodes1 = [];
		$increment1 = 1;
		//dd();

		if (count($reservation) > 0) {
			$paymentStatus = "";
			foreach ($reservation as $val) {
				// dd($val->anet_transaction_id, $val->transaction->card_last_four);
				if (empty($val->anet_transaction_id)  && $val->total > 0) {
					$paymentStatus = 'Authorized';
				} elseif (!empty($val->anet_transaction_id)) {
					$paymentStatus = 'Processed';
				} else {
					$paymentStatus = 'NA';
				}
				//echo $paymentStatus;
				$finalCodes1[] = [
					'No.'                      => $increment1,
					'Facility Name'            => $val->facility->full_name,
					'Booking Id'               => $val->ticketech_code,
					'License Plate'            => $val->license_plate,
					'Creation Date'            => date("m/d/Y h:i A", strtotime($val->created_at)),
					'Reservation Start Date'   => date("m/d/Y h:i A", strtotime($val->start_timestamp)),
					'Reservation End Date'     => date("m/d/Y h:i A", strtotime($val->end_timestamp)),
					'Event Date'               => !empty($val->event_id) ? date("m/d/Y", strtotime($val->start_timestamp)) : '-',
					'Booking Amount Due ($)'   => ($val->is_charged == '0') ? $val->total : '0.00',
					'Booking Amount Paid ($)'  => $val->charged_amount,
					'Processing fee ($)'       => $val->processing_fee,
					'Refund Amount ($)'        => $val->refund_amount,
					'Discount'                 => $val->discount,
					'Promo Code'               => !empty($val->promocode) ? $val->promocode : '-',
					'Payment Status'           => $paymentStatus,
					'Reference Number'         => isset($val->transaction->ref_id) ? $val->transaction->ref_id : '-',
					'Transaction Status'       => $val->cancelled_at != '' ? "Canceled" : '-',
					'Canceled At'              =>  !empty($val->cancelled_at) ? date("m/d/Y", strtotime($val->cancelled_at)) : '-',
					'User Email'                     => isset($val->user->email) ? $val->user->email : '-',
					//'Pass Type'                => isset($val->rate->description) ? $val->rate->description : 'Single Event',
					// 'Card last 4'           => '-',
					// 'Expiry Date'           => '$val->transaction->card_type',

				];
				$increment1++;
			}
		}
		//dd('a');

		Excel::create(
			$excelSheetName,
			function ($excel) use ($finalCodes1, $excelSheetName, $from_date, $to_date) {

				// Set the spreadsheet title, creator, and description
				$excel->setTitle($excelSheetName);
				$excel->setCreator('BookingDetails')->setCompany('ParkEngage');
				$excel->setDescription('List Of Booking');
				// Build the spreadsheet, passing in the payments array
				$color = "#2C4293";
				if (isset($finalCodes1) && !empty($finalCodes1)) {
					$excel->sheet(
						'Booking Details',
						function ($sheet5) use ($finalCodes1, $color, $from_date, $to_date) {
							$sheet5->setWidth(
								array(
									'A' => 28,
									'B' => 45,
									'C' => 22,
									'D' => 22,
									'E' => 17.34,
									'F' => 20,
									'G' => 21,
									'H' => 20,
									'I' => 23,
									'J' => 15,
									'K' => 18,
									'L' => 15,
									'M' => 15,
									'R' => 12,
									'N' => 15,
									'O' => 13,
									'P' => 12,
									// 'Q' => 19,
								)
							);
							// $sheet5->row(1, 'Daily Revenue Report');
							// define width colom A,B, witdh static here
							$sheet5->getColumnDimension('A')->setWidth(6);
							$sheet5->getColumnDimension('B')->setWidth(45);
							$sheet5->getColumnDimension('C')->setWidth(22);
							$sheet5->getColumnDimension('D')->setWidth(22);
							$sheet5->getColumnDimension('E')->setWidth(17.34);
							$sheet5->getColumnDimension('F')->setWidth(20);
							$sheet5->getColumnDimension('G')->setWidth(21);
							$sheet5->getColumnDimension('H')->setWidth(20);
							$sheet5->getColumnDimension('I')->setWidth(23);
							$sheet5->getColumnDimension('J')->setWidth(15);
							$sheet5->getColumnDimension('K')->setWidth(18);
							$sheet5->getColumnDimension('L')->setWidth(15);
							$sheet5->getColumnDimension('M')->setWidth(15);

							$sheet5->getColumnDimension('N')->setWidth(15);
							$sheet5->getColumnDimension('O')->setWidth(13);
							$sheet5->getColumnDimension('P')->setWidth(20);
							$sheet5->getColumnDimension('Q')->setWidth(21);
							$sheet5->getColumnDimension('S')->setWidth(28);
							$sheet5->getStyle("C")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("D")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("H")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("I")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("C3")->getNumberFormat()->setFormatCode('0');
							$sheet5->getStyle("D2")->getNumberFormat()->setFormatCode('0');
							//$sheet5->mergeCells('A1:I1');
							//$sheet5->mergeCells('F1:J1');
							$sheet5->getRowDimension(1)->setRowHeight(80);

							$location = date('d M, Y', strtotime($from_date)) . '-' . date('d M, Y', strtotime($to_date));
							//for daily
							$sheet5->mergeCells('A1:E1');
							$sheet5->setCellValue('A1', "Booking Summary -" . $location);
							$sheet5->getStyle('A1')->getAlignment()->setWrapText(true);
							//print date
							$printDate = "Print Date -" . date('d M, Y');
							$sheet5->mergeCells('F1:M1');
							$sheet5->setCellValue('F1', "$printDate");
							$sheet5->getStyle('F1')->getAlignment()->setWrapText(true);
							//new change
							$sheet5->cell('A1:S1', function ($cell) use ($color) {
								$cell->setAlignment('center');
								$cell->setValignment('center');
								$cell->setFontWeight('bold');
								$cell->setBackground($color);
								$cell->setFontColor('#ffffff');
								$cell->setFontSize('22');
							});
							//end changes for new excel changes
							$sheet5->mergeCells('A2:S2');
							$sheet5->cell('A3:S3', function ($cell) use ($color) {
								$cell->setAlignment('center');
								$cell->setValignment('center');
								$cell->setFontWeight('bold');
								$cell->setBackground('#D9E1F2');
								$cell->setFontColor('#000000');
							});

							$sheet5->fromArray($finalCodes1, null, 'A3', false, true);
							$i = 3;
							$ticketCount = $dueAmount = $ChanrdedAmount = $processingfee = $refundAmount = $DiscountAmount = 0;
							foreach ($finalCodes1 as $Value) {
								//  if (!empty($Value['Booking Id'])) {
								$ticketCount++;
								$dueAmount += is_numeric($Value['Booking Amount Due ($)'])
									? (float)$Value['Booking Amount Due ($)']
									: 0.00;
								$ChanrdedAmount += is_numeric($Value['Booking Amount Paid ($)'])
									? (float)$Value['Booking Amount Paid ($)']
									: 0.00;
								$processingfee += is_numeric($Value['Processing fee ($)'])
									? (float)$Value['Processing fee ($)']
									: 0.00;
								$refundAmount += is_numeric($Value['Refund Amount ($)'])
									? (float)$Value['Refund Amount ($)']
									: 0.00;
								$DiscountAmount += is_numeric($Value['Discount'])
									? (float)$Value['Discount']
									: 0.00;
								//  }

								$i++;
								$sheet5->cell('A' . $i . ':S' . $i, function ($cell) use ($color) {
									$cell->setAlignment('center');
									$cell->setValignment('center');
								});
							}

							// $lastkey = $i + 1;
							// $sheet5->cell('A' . $lastkey . ':R' . $lastkey, function ($cell) use ($color) {
							//   $cell->setAlignment('center');
							//   $cell->setValignment('center');
							// });

							$K = $i + 1;
							$sheet5->cell('A' . $K . ':S' . $K, function ($cell) use ($color) {
								$cell->setAlignment('center');
								$cell->setValignment('center');
								$cell->setFontWeight('bold');
								$cell->setBackground($color);
								$cell->setFontColor('#ffffff');
								$cell->setFontSize('12');
							});
							$sheet5->setCellValue('A' . $K, 'Total');
							$sheet5->setCellValue('B' . $K, '-');
							$sheet5->setCellValue('C' . $K, ($ticketCount > 0) ? count($finalCodes1) : 0);
							$sheet5->setCellValue('D' . $K, '-');
							$sheet5->setCellValue('E' . $K, '-');
							$sheet5->setCellValue('F' . $K, '-');
							$sheet5->setCellValue('G' . $K, '-');
							$sheet5->setCellValue('H' . $K, '-');
							$sheet5->setCellValue('I' . $K, number_format($dueAmount));
							$sheet5->setCellValue('J' . $K, number_format($ChanrdedAmount));
							$sheet5->setCellValue('K' . $K, number_format($processingfee));
							$sheet5->setCellValue('L' . $K, number_format($refundAmount));
							$sheet5->setCellValue('M' . $K, number_format($DiscountAmount));
							$sheet5->setCellValue('N' . $K, '-');
							$sheet5->setCellValue('O' . $K, '-');
							$sheet5->setCellValue('P' . $K, '-');
							$sheet5->setCellValue('Q' . $K, '-');
							$sheet5->setCellValue('R' . $K, '-');
							$sheet5->setCellValue('S' . $K, '-');
							//$sheet5->setCellValue('R' . $K, '-');
						}
					);
				} else {
					throw new ApiGenericException('Sorry! No Data Found.');
				}
			}
		)->store('xls')->download('xls');
	}

	public function backup_getBookingListDownloadExcel(Request $request)
	{

		$this->log->info("Download Excell Request -- " . json_encode($request->all()));
		if (isset($request->partner_id)) {
			$partner_id = $request->partner_id;
		} else {
			if (Auth::user()->user_type == '1') {
				$result  = [];
				$result["total"] = 0;
				$result["per_page"] = 20;
				$result["current_page"] = 1;
				$result["last_page"] = 1;
				$result["next_page_url"] = Null;
				$result["prev_page_url"] = Null;
				$result["from"] = Null;
				$result["to"] = Null;
				$result["data"] = [];
				//return $result;
			} else if (Auth::user()->user_type == '12') {
				$partner_id = Auth::user()->created_by;
			} elseif (Auth::user()->user_type == '4') {
				$partner_id = Auth::user()->created_by;
			} else {
				$partner_id = Auth::user()->id;
			}
		}
		$this->log->info("Partner ID -- " . $partner_id);

		$month = date("m");
		$from_date  = date('Y-' . $month . '-01');
		$to_date  = date('Y-' . $month . '-t');
		$mid_date  = date('Y-' . $month . '-15');
		$midDateString = strtotime($mid_date);
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		if (isset($request->from_date) && $request->from_date != '') {
			$from_date = $request->from_date;
			$to_date = $request->to_date;
		}

		$this->log->info("From Date -- $from_date and  To Date: $to_date");


		$today = $request->from_date;
		$todayQrCode = [];
		if ($request->booking_type == '0') {
		} else {

			if ($request->booking_type == '') {
				//->where("is_active", '1')
				$event = Event::whereDate('start_time', '=', $from_date)->get();
			} else {

				//->where("is_active", '1')
				$event = Event::whereDate('start_time', '>=', $from_date)->whereDate('start_time', '<=', $to_date)->get();
			}
			$todayQrCode = [];
			if (count($event) > 0) {
				foreach ($event as $k => $v) {
					$qrCode = MapcoQrcode::where("event_id", $v->id)->get();
					if (count($qrCode) > 0) {
						foreach ($qrCode as $key => $value) {
							$todayQrCode[] = $value->reservation_id;
						}
					}
				}
			} else {
				if ($request->booking_type == '') {
					$todayQrCode[] = 0;
				}
			}
		}
		$this->log->info("Reached 111");
		// dd($request->booking_type, $todayQrCode);
		// $reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->whereNotNull('anet_transaction_id');
		$reservation = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'facility', 'transaction']);
		// if (!$reservation->transaction) {
		//   $datacapTransaction = DatacapTransaction::select('*', 'expiry as expiration', 'card_last_four as payment_last_four')->where('reservation_id', $reservation_id)->first();
		//   if ($datacapTransaction) {
		//     $reservation->setRelation('transaction', $datacapTransaction);
		//   }
		// }
		if (isset($request->search) && $request->search != '') {
			//$reservation = $reservation->where('ticketech_code', 'like', "%$request->search%")->where('partner_id', $partner_id);
			$reservation = $reservation->where(function ($query) use ($request, $partner_id) {
				$query->where('ticketech_code', "like", "%" . $request->search . "%");
				// $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%");
				$query->orWhere("license_plate", 'like', "%$request->search%");
				// $query->orWhere("users.phone", 'like', "%$request->search%");
				// $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%");
				$query->Where("partner_id", $partner_id);
			});
			$reservation = $reservation->orWhereHas(
				'user',
				function ($query) use ($request, $partner_id) {
					$query
						->where('phone', 'like', "%{$request->search}%")
						->orWhere('email', 'like', "%{$request->search}%")
						->where('created_by', $partner_id);
				}
			);
			$reservation = $reservation->orWhereHas(
				'transaction',
				function ($query) use ($request) {
					$query
						->where('ref_id', 'like', "%{$request->search}%");
				}
			);
		}
		$this->log->info("Reached 222");

		if ($request->booking_type == '0') {
			$reservation = $reservation->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
		} elseif ($request->booking_type == '2') {
			$reservation = $reservation->whereDate('end_timestamp', '>=', $from_date)->whereDate('end_timestamp', '<=', $to_date);
		} else {

			$reservation = $reservation->whereDate('start_timestamp', '>=', $from_date)->whereDate('start_timestamp', '<=', $to_date);
		}
		if ($request->facility_id) {
			$reservation = $reservation->where('facility_id', $request->facility_id);
		}

		$this->log->info("Reached 444");
		//dd($todayQrCode);
		$reservation = $reservation->where(function ($query) use ($partner_id, $todayQrCode) {
			$query->where('partner_id', $partner_id);
			if (count($todayQrCode) > 0) {
				// $query->whereIn('id', $todayQrCode);
			}
		});
		$this->log->info("Reached 555");

		if ($request->sort != '') {
			$reservation = $reservation->orderBy($request->sort, $request->sortBy);
		} else {
			$reservation = $reservation->orderBy('id', 'DESC');
		}
		$this->log->info("Reached 666");

		$reservation = $reservation->get();
		// dd(count($reservation));
		if ($request->booking_type == '0') {
		} else {
			$this->log->info("Reservation Count: " . count($reservation));
			if (count($reservation) > 0) {
				foreach ($reservation as $key => $value) {
					if (isset($value->mapcoQrCode)) {
						$this->log->info("No mapcoQrCode: " . json_encode($value->mapcoQrCode));
						foreach ($value->mapcoQrCode as $k => $v) {
							if ($v->event_id != '0') {
								//$event = Event::where("id",$v->event_id)->where("is_active", '1')->first();
								if (count($event) > 0) {
									foreach ($event as $eventKey => $eventValue) {
										if (strtotime(date("Y-m-d", strtotime($eventValue->start_time))) == strtotime($today)) {
											$reservation[$key]['start_timestamp'] = $eventValue->start_time;
										}
									}
								}
							}
						}
					} else {
						$this->log->info("No mapcoQrCode:");
					}
				}
			}
		}

		if ($request->sort == 'phone') {
			if (count($reservation) > 0) {
				if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->phone > $reservation[$j]['user']->phone) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				} else {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->phone < $reservation[$j]['user']->phone) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				}
			}
		}
		if ($request->sort == 'email') {
			if (count($reservation) > 0) {
				if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->email > $reservation[$j]['user']->email) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				} else {
					for ($i = 0; $i < count($reservation); $i++) {
						for ($j = $i + 1; $j < count($reservation); $j++) {
							if ($reservation[$i]['user']->email < $reservation[$j]['user']->email) {
								$temp = $reservation[$i];
								$reservation[$i] = $reservation[$j];
								$reservation[$j] = $temp;
							}
						}
					}
				}
			}
		}

		$excelSheetName = ucwords(str_replace(' ', '', 'Booking Details Excel'));
		$finalCodes1 = [];
		$increment1 = 1;
		//dd();

		if (count($reservation) > 0) {

			foreach ($reservation as $val) {
				// dd($val->transaction);
				$finalCodes1[] = [
					'No.'                      => $increment1,
					'Booking Id'               => $val->ticketech_code,
					'Booking Amount Due ($)'   => ($val->is_charged == '0') ? $val->total : 0.00,
					'Booking Amount Paid ($)'  => $val->charged_amount,
					'Event Date'               => !empty($val->event_id) ? date("m/d/Y", strtotime($val->start_timestamp)) : '-',
					'Creation Date'            => date("m/d/Y h:i A", strtotime($val->created_at)),
					'Reservation Start Date'   => date("m/d/Y h:i A", strtotime($val->start_timestamp)),
					'Reservation End Date'     => date("m/d/Y h:i A", strtotime($val->end_timestamp)),
					'User Email'                     => isset($val->user->email) ? $val->user->email : '-',
					// 'Pass Type'                => isset($val->rate->description) ? $val->rate->description : 'Single Event',
					'Reference Number'         => isset($val->transaction->ref_id) ? $val->transaction->ref_id : '-',
					// 'Status'                   => $val->cancelled_at != '' ? "Canceled" : '-',
					'Processing fee ($)'       => $val->processing_fee,
					'Promo Code'               => !empty($val->promocode) ? $val->promocode : '-',
					'Discount'                 => $val->discount,
					'Refund Amount ($)'        => $val->refund_amount,
					'License Plate'            => $val->license_plate,
					// 'Card last 4'           => '-',
					// 'Expiry Date'           => '$val->transaction->card_type',
					'Facility Name'            => $val->facility->full_name,
				];
				$increment1++;
			}
		}
		// dd("Final Data", $finalCodes1);

		Excel::create(
			$excelSheetName,
			function ($excel) use ($finalCodes1, $excelSheetName, $from_date, $to_date) {

				// Set the spreadsheet title, creator, and description
				$excel->setTitle($excelSheetName);
				$excel->setCreator('BookingDetails')->setCompany('ParkEngage');
				$excel->setDescription('List Of Booking');
				// Build the spreadsheet, passing in the payments array
				$color = "#2C4293";
				if (isset($finalCodes1) && !empty($finalCodes1)) {
					$excel->sheet(
						'Booking Details',
						function ($sheet5) use ($finalCodes1, $color, $from_date, $to_date) {
							$sheet5->setWidth(
								array(
									'A' => 28,
									'B' => 24,
									'C' => 22,
									'D' => 22,
									'E' => 17.34,
									'F' => 20,
									'G' => 21,
									'H' => 20,
									'I' => 23,
									'J' => 15,
									'K' => 18,
									'L' => 15,
									'M' => 15,
									'R' => 46,
									'N' => 15,
									'O' => 13,
									'P' => 45,
									// 'Q' => 19,
								)
							);
							// $sheet5->row(1, 'Daily Revenue Report');
							// define width colom A,B, witdh static here
							$sheet5->getColumnDimension('A')->setWidth(6);
							$sheet5->getColumnDimension('B')->setWidth(24);
							$sheet5->getColumnDimension('C')->setWidth(22);
							$sheet5->getColumnDimension('D')->setWidth(22);
							$sheet5->getColumnDimension('E')->setWidth(17.34);
							$sheet5->getColumnDimension('F')->setWidth(20);
							$sheet5->getColumnDimension('G')->setWidth(21);
							$sheet5->getColumnDimension('H')->setWidth(20);
							$sheet5->getColumnDimension('I')->setWidth(23);
							$sheet5->getColumnDimension('J')->setWidth(15);
							$sheet5->getColumnDimension('K')->setWidth(18);
							$sheet5->getColumnDimension('L')->setWidth(15);
							$sheet5->getColumnDimension('M')->setWidth(15);

							$sheet5->getColumnDimension('N')->setWidth(15);
							$sheet5->getColumnDimension('O')->setWidth(13);
							$sheet5->getColumnDimension('P')->setWidth(45);
							$sheet5->getColumnDimension('Q')->setWidth(21);
							$sheet5->getColumnDimension('R')->setWidth(45);
							// $sheet5->getStyle("C")->getNumberFormat()->setFormatCode('0.00');
							// $sheet5->getStyle("D")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("H")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("I")->getNumberFormat()->setFormatCode('0.00');
							$sheet5->getStyle("C3")->getNumberFormat()->setFormatCode('0');
							$sheet5->getStyle("D2")->getNumberFormat()->setFormatCode('0');
							//$sheet5->mergeCells('A1:I1');
							//$sheet5->mergeCells('F1:J1');
							$sheet5->getRowDimension(1)->setRowHeight(80);

							$location = date('d M, Y', strtotime($from_date)) . '-' . date('d M, Y', strtotime($to_date));
							//for daily
							$sheet5->mergeCells('A1:E1');
							$sheet5->setCellValue('A1', "Booking Summary -" . $location);
							$sheet5->getStyle('A1')->getAlignment()->setWrapText(true);
							//print date
							$printDate = "Print Date -" . date('d M, Y');
							$sheet5->mergeCells('F1:M1');
							$sheet5->setCellValue('F1', "$printDate");
							$sheet5->getStyle('F1')->getAlignment()->setWrapText(true);
							//new change
							$sheet5->cell('A1:P1', function ($cell) use ($color) {
								$cell->setAlignment('center');
								$cell->setValignment('center');
								$cell->setFontWeight('bold');
								$cell->setBackground($color);
								$cell->setFontColor('#ffffff');
								$cell->setFontSize('22');
							});
							//end changes for new excel changes
							$sheet5->mergeCells('A2:P2');
							$sheet5->cell('A3:P3', function ($cell) use ($color) {
								$cell->setAlignment('center');
								$cell->setValignment('center');
								$cell->setFontWeight('bold');
								$cell->setBackground('#D9E1F2');
								$cell->setFontColor('#000000');
							});

							$sheet5->fromArray($finalCodes1, null, 'A3', false, true);
							$i = 3;
							$ticketCount = $dueAmount = $ChanrdedAmount = $processingfee = $refundAmount = $DiscountAmount = 0;
							foreach ($finalCodes1 as $Value) {
								//  if (!empty($Value['Booking Id'])) {
								$ticketCount++;
								$dueAmount += is_numeric($Value['Booking Amount Due ($)'])
									? (float)$Value['Booking Amount Due ($)']
									: 0.00;
								$ChanrdedAmount += is_numeric($Value['Booking Amount Paid ($)'])
									? (float)$Value['Booking Amount Paid ($)']
									: 0.00;
								$processingfee += is_numeric($Value['Processing fee ($)'])
									? (float)$Value['Processing fee ($)']
									: 0.00;
								$refundAmount += is_numeric($Value['Refund Amount ($)'])
									? (float)$Value['Refund Amount ($)']
									: 0.00;
								$DiscountAmount += is_numeric($Value['Discount'])
									? (float)$Value['Discount']
									: 0.00;
								//  }

								$i++;
								$sheet5->cell('A' . $i . ':Q' . $i, function ($cell) use ($color) {
									$cell->setAlignment('center');
									$cell->setValignment('center');
								});
							}

							// $lastkey = $i + 1;
							// $sheet5->cell('A' . $lastkey . ':R' . $lastkey, function ($cell) use ($color) {
							//   $cell->setAlignment('center');
							//   $cell->setValignment('center');
							// });

							$K = $i + 1;
							$sheet5->cell('A' . $K . ':P' . $K, function ($cell) use ($color) {
								$cell->setAlignment('center');
								$cell->setValignment('center');
								$cell->setFontWeight('bold');
								$cell->setBackground($color);
								$cell->setFontColor('#ffffff');
								$cell->setFontSize('12');
							});
							$sheet5->setCellValue('A' . $K, 'Total');
							$sheet5->setCellValue('B' . $K, ($ticketCount > 0) ? count($finalCodes1) : 0);
							$sheet5->setCellValue('C' . $K, number_format($dueAmount));
							$sheet5->setCellValue('D' . $K, number_format($ChanrdedAmount));
							$sheet5->setCellValue('E' . $K, '-');
							$sheet5->setCellValue('F' . $K, '-');
							$sheet5->setCellValue('G' . $K, '-');
							$sheet5->setCellValue('H' . $K, '-');
							$sheet5->setCellValue('I' . $K, '-');
							$sheet5->setCellValue('J' . $K, '-');
							$sheet5->setCellValue('K' . $K, number_format($processingfee));
							$sheet5->setCellValue('L' . $K, '-');
							$sheet5->setCellValue('M' . $K, number_format($DiscountAmount));
							$sheet5->setCellValue('N' . $K, number_format($refundAmount));
							$sheet5->setCellValue('O' . $K, '-');
							$sheet5->setCellValue('P' . $K, '-');
							// $sheet5->setCellValue('Q' . $K, '-');
							// $sheet5->setCellValue('R' . $K, '-');
						}
					);
				} else {
					throw new ApiGenericException('Sorry! No Data Found.');
				}
			}
		)->store('xls')->download('xls');
	}


	public function getInvoicePdf(Request $request)
	{
		if (Auth::user()->user_type == '4') {
			$admin_partner_id = Auth::user()->created_by;
			if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
				if ($request->partner_id != '') {
				}
			} else {
				$request->request->add(['partner_id' => Auth::user()->created_by]);
			}
		} else if (Auth::user()->user_type == '12') {
			$request->request->add(['partner_id' => Auth::user()->created_by]);
		}
		$billingDetails = User::with('userPaymentGatewayDetail')->where('id', $request->partner_id)->first();

		$from_date  = date($request->from_date . '-01');
		$mid_date  = date($request->from_date . '-15');
		$to_date  = date($request->to_date . '-t');
		$midDateString = strtotime($mid_date);
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		$last_from_date = date("Y-m-d", strtotime($from_date . "-1 month"));
		$last_to_date = date("Y-m", strtotime($from_date . "-1 month"));
		$last_mid_date  = date($last_to_date . '-15');
		$lastMidDateString = strtotime($last_mid_date);
		$lastLastdate = strtotime(date("Y-m-t", $lastMidDateString));
		$last_to_date = date("Y-m-d", $lastLastdate);

		//last month revenye
		$lastRevenueBalance = RevenueBalance::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $last_from_date)->whereDate('to_date', '=', $last_to_date)->orderBy('id', 'DESC')->first();
		$revenueBalance = RevenueBalance::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $from_date)->whereDate('to_date', '=', $to_date)->orderBy('id', 'DESC')->first();
		if ($revenueBalance) {
			$countBookingShare = 0;
			$countpassShare = 0;
			$bookingShareAmount = 0;
			$passShareAmount = 0;
			$passRate = 0;
			$bookingRate = 0;
			$paidAmount = 0;
			$paidDate = '';
			$lastPaidAmount = 0;
			$lastPaidDate = '';

			//last month revenue
			$lastRevenueHistory = RevenuePaymentHistory::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $last_from_date)->whereDate('to_date', '=', $last_to_date)->get();

			if ($lastRevenueHistory) {
				foreach ($lastRevenueHistory as $k => $v) {
					$lastPaidAmount += $v->total;
					$lastPaidDate = $v->created_at;
				}
				$billingDetails->last_paid_amount = $lastPaidAmount;
				$billingDetails->last_paid_amount_date = $lastPaidDate;
			} else {
				$billingDetails->last_paid_amount = $lastPaidAmount;
				$billingDetails->last_paid_amount_date = '';
			}

			$revenueHistory = RevenuePaymentHistory::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $from_date)->whereDate('to_date', '=', $to_date)->get();
			if ($revenueHistory) {
				foreach ($revenueHistory as $k => $v) {
					$paidAmount += $v->total;
					$paidDate = $v->created_at;
				}
				$billingDetails->paid_amount = $paidAmount;
				$billingDetails->paid_amount_date = $paidDate;
			} else {
				$billingDetails->paid_amount = $paidAmount;
				$billingDetails->paid_amount_date = '';
			}
			$billingDetails->revenue_balance = $revenueBalance;
			$billingDetails->last_revenue_balance = $lastRevenueBalance;

			$rate = Rate::where("partner_id", $request->partner_id)->get();
			$passDetails = [];
			$totalEvent = 0;
			if (count($rate) > 0) {
				foreach ($rate as $key => $value) {
					$reservation = Reservation::with("rate")->where('rate_id', $value->id)->where('partner_id', $request->partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull("cancelled_at")->whereNotNull('anet_transaction_id')->count();
					$passDetails[$value->description] = $reservation;
					$totalEvent = $totalEvent + ($reservation * $value->total_usage);
				}
			}
			$billingDetails->passDetails = $passDetails;
			$billingDetails->totalEvent = $totalEvent . '.00';
			$pdf = (new RevenueBalance())->generateMapcoInvoice($billingDetails, Pdf::class);
			return $this->respondWithPdf($pdf);
		} else {
			throw new ApiGenericException('Sorry! No billing generated for this month.');
		}
	}



	public function downloadReport(Request $request)
	{
		// $from_date = date("Y-m-d");
		// $report = DB::select("use inventory_modules");
		$facility_id = '32';
		if ($request->fromDate == 'all') {
			$from_date  =  (isset($request->fromDate) &&  $request->fromDate != 'all') ? date('Y-m-d', strtotime($request->fromDate)) : date('Y-', strtotime('now')) . '01-01';
			$to_date    =  isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate)) : date('Y-m-d', strtotime('now'));
		} else {
			$from_date  = isset($request->fromDate) ? date('Y-m-d', strtotime($request->fromDate)) : date('Y-', strtotime('now')) . '01-01';
			$to_date    = isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate)) : date('Y-m-d', strtotime('now'));
		}
		$usage_type = $request->usage_type;
		$usage =    "";
		if ($request->usage_type == 'all') {
			$usage =    "";
		} else {
			$usage =    "and IF(mapco_qrcodes.total_usage = mapco_qrcodes.remain_usage, 'Unused', 'Used') = '$request->usage_type' ";
		}



		if ($usage_type == 'unused') {
			$sql_query = "SELECT users.email, users.phone, reservations.ticketech_code, If(date(checkin_time) = '','-',checkin_time) as checkin_time,checkin_gate, 
            IF(mapco_qrcodes.total_usage = mapco_qrcodes.remain_usage, 'Unused', 'Used') as status,
             #mapco_qrcodes.total_usage, mapco_qrcodes.remain_usage,
             IF(reservations.rate_id = 0, reservations.total, reservations.total/rates.total_usage) as price, rates.description as PassType ,
             date(events.start_time) As EventDate
             FROM mapco_qrcodes 
             left join events on events.id=mapco_qrcodes.event_id 
             left join reservations on reservations.id=mapco_qrcodes.reservation_id
             left join rates on rates.id=reservations.rate_id 
             left join users on users.id=reservations.user_id 
             #inner join tickets on tickets.reservation_id=reservations.id
             left join tickets on tickets.event_id=mapco_qrcodes.event_id and tickets.reservation_id = mapco_qrcodes.reservation_id
             where tickets.facility_id = '$facility_id'  and date(events.start_time) >= '$from_date'  and date(events.start_time) <= '$to_date' and   mapco_qrcodes.deleted_at is NULL AND reservations.deleted_at is NULL AND reservations.cancelled_at is NULL and IF(mapco_qrcodes.total_usage = mapco_qrcodes.remain_usage, 'Unused', 'Used') = 'Unused'  order by EventDate desc  ";
		} elseif ($usage_type == 'driveup') {

			$sql_query = "SELECT users.email, users.phone, '' as ticketech_code , DATE_FORMAT(tickets.checkin_time,'%m/%d/%Y %H:%i:%s') as checkin_time, tickets.checkin_gate, 'Drive-Up'  as status, tickets.grand_total as price, 'Drive up' as PassType  , date( tickets.checkin_time) As EventDate 
                from tickets
                inner join users on users.id=tickets.user_id
                inner join events 
                ON events.id = tickets.event_id
                where  tickets.reservation_id Is null
                and tickets.facility_id = '$facility_id'  and  date(events.start_time) >= '$from_date'  and date(events.start_time) <= '$to_date' order by EventDate desc";
		} elseif ($usage_type == 'used') {
			$sql_query = "SELECT users.email, users.phone, reservations.ticketech_code, If(date(checkin_time) = '','-',checkin_time) as checkin_time,checkin_gate, 
            IF(mapco_qrcodes.total_usage = mapco_qrcodes.remain_usage, 'Unused', 'Used') as status,
             #mapco_qrcodes.total_usage, mapco_qrcodes.remain_usage,
             IF(reservations.rate_id = 0, reservations.total, reservations.total/rates.total_usage) as price, rates.description as PassType ,
             date(events.start_time) As EventDate
             FROM mapco_qrcodes 
             left join events on events.id=mapco_qrcodes.event_id 
             left join reservations on reservations.id=mapco_qrcodes.reservation_id
             left join rates on rates.id=reservations.rate_id 
             left join users on users.id=reservations.user_id 
             #inner join tickets on tickets.reservation_id=reservations.id
             left join tickets on tickets.event_id=mapco_qrcodes.event_id and tickets.reservation_id = mapco_qrcodes.reservation_id
             where tickets.facility_id = '$facility_id'  and date(events.start_time) >= '$from_date'  and date(events.start_time) <= '$to_date' and   mapco_qrcodes.deleted_at is NULL AND reservations.deleted_at is NULL AND reservations.cancelled_at is NULL $usage    
            UNION
            SELECT users.email, users.phone, reservations.ticketech_code ,tickets.checkin_time,tickets.checkin_gate, 'Used'  as Status, IF(reservations.rate_id = 0, tickets.total, tickets.total/rates.total_usage) as price, rates.description as PassType  ,date( tickets.checkin_time) As EventDate FROM tickets
            INNER JOIN users 
            ON users.id = tickets.user_id
            INNER JOIN events 
            ON events.id = tickets.event_id
            join reservations on reservations.id=tickets.reservation_id left join rates on rates.id=reservations.rate_id where tickets.facility_id='$facility_id' and rates.description='6 Pack' and date(tickets.checkin_time) >= '$from_date' and date(tickets.checkin_time) <= '$to_date' order by EventDate desc";
		} else {
			$sql_query = "SELECT users.email, users.phone, reservations.ticketech_code, If(date(checkin_time) = '','-',checkin_time) as checkin_time,checkin_gate, 
            IF(mapco_qrcodes.total_usage = mapco_qrcodes.remain_usage, 'Unused', 'Used') as status,
             #mapco_qrcodes.total_usage, mapco_qrcodes.remain_usage,
             IF(reservations.rate_id = 0, reservations.total, reservations.total/rates.total_usage) as price, rates.description as PassType ,
             date(events.start_time) As EventDate
             FROM mapco_qrcodes 
             left join events on events.id=mapco_qrcodes.event_id 
             left join reservations on reservations.id=mapco_qrcodes.reservation_id
             left join rates on rates.id=reservations.rate_id 
             left join users on users.id=reservations.user_id 
             #inner join tickets on tickets.reservation_id=reservations.id
             left join tickets on tickets.event_id=mapco_qrcodes.event_id and tickets.reservation_id = mapco_qrcodes.reservation_id
             where tickets.facility_id = '$facility_id'  and date(events.start_time) >= '$from_date'  and date(events.start_time) <= '$to_date' and   mapco_qrcodes.deleted_at is NULL AND reservations.deleted_at is NULL AND reservations.cancelled_at is NULL $usage    
            UNION
            SELECT users.email, users.phone, reservations.ticketech_code ,tickets.checkin_time,tickets.checkin_gate, 'Used'  as Status, IF(reservations.rate_id = 0, tickets.total, tickets.total/rates.total_usage) as price, rates.description as PassType  ,date( tickets.checkin_time) As EventDate FROM tickets
            INNER JOIN users ON users.id = tickets.user_id
            INNER JOIN events ON events.id = tickets.event_id
            join reservations on reservations.id=tickets.reservation_id left join rates on rates.id=reservations.rate_id where tickets.facility_id='$facility_id' and rates.description='6 Pack' and date(tickets.checkin_time) >= '$from_date' and date(tickets.checkin_time) <= '$to_date' 
            UNION
            SELECT users.email, users.phone, '' as ticketech_code , DATE_FORMAT(tickets.checkin_time,'%m/%d/%Y %H:%i:%s') as checkin_time, tickets.checkin_gate, 'Drive-Up'  as status, tickets.grand_total as price, 'Drive up' as PassType  , date( tickets.checkin_time) As EventDate 
                from tickets
                inner join users on users.id=tickets.user_id
                inner join events 
                ON events.id = tickets.event_id
                where  tickets.reservation_id Is null
                and tickets.facility_id = '$facility_id'  and  date(events.start_time) >= '$from_date'  and date(events.start_time) <= '$to_date'  order by EventDate desc";
		}
		// echo $sql_query ;die();

		$reports = DB::select($sql_query);

		// $reports = \DB::select('call GetAllMapcoPass');
		// return $reports; 
		$excelSheetName = ucwords(str_replace(' ', '', 'Event Wise Revenue Report'));
		$finalCodes1 = [];
		$increment1 = 1;
		foreach ($reports as $key => $value) {
			// get gate name 
			$gate_name = '';
			if (!empty($value->checkin_gate)) {
				$gate = Gate::where('gate', $value->checkin_gate)->where('facility_id', $facility_id)->first();
				$gate_name = $gate->gate_name;
			}
			$status = '';
			if ($value->status == 'Unused') {
				$status = 'Passes not used';
			} elseif ($value->status == 'Used') {
				$status = 'Passes used';
			} else {
				$status = 'Drive up';
			}
			$finalCodes1[] = [
				'No.' => $increment1,
				'Email' => isset($value->email) && !empty($value->email) ? $value->email : '-',
				'Phone' => isset($value->phone) && !empty($value->email) ? $value->phone : '-',
				'Booking Id' => !empty($value->ticketech_code) ? $value->ticketech_code : '-',
				'Pass Name' => !empty($value->PassType) ? $value->PassType : 'Single Event',
				'Price ($)' => $value->price == '0.00' ? "0.00" : round($value->price, 2),
				'Event Date' => date("m-d-Y", strtotime($value->EventDate)),
				'Check-In Time' => !empty($value->checkin_time) ? date("m-d-Y H:i:s", strtotime($value->checkin_time)) : '-',
				'Check-In Gate' => !empty($gate_name) ? $gate_name : '-',
				'Status' => $status
			];
			$increment1++;
		}
		Excel::create(
			$excelSheetName,
			function ($excel) use ($finalCodes1, $excelSheetName) {

				// Set the spreadsheet title, creator, and description
				$excel->setTitle($excelSheetName);
				$excel->setCreator('RevenueDetails')->setCompany('ParkEngage');
				$excel->setDescription('List Of Booking');
				// Build the spreadsheet, passing in the payments array
				if (isset($finalCodes1) && !empty($finalCodes1)) {
					$excel->sheet(
						'Revenue Details',
						function ($sheet) use ($finalCodes1) {
							$sheet->fromArray($finalCodes1, null, 'A1', false, true);
						}
					);
				} else {
					throw new ApiGenericException('Sorry! No Data Found.', 500);
				}
			}
		)->store('xls')->download('xls');
		exit();
		return $finalCodes1;
	}

	public function mapcoEvntsDates(Request $request)
	{
		$facility_id = '32';
		$from_date  =  '2023-01-01';
		$to_date    =  date('Y-m-d');
		// $sql_query = "SELECT   date(events.start_time) As EventDate  FROM events where  date(start_time) >= '$from_date' and date(start_time) <= '$to_date' and is_active ='1' ";
		/*  $sqlQueryEvent = "SELECT users.email, users.phone, reservations.ticketech_code, If(date(checkin_time) = '','-',checkin_time) as checkin_time,checkin_gate, 
          IF(mapco_qrcodes.total_usage = mapco_qrcodes.remain_usage, 'Unused', 'Used') as status,
           IF(reservations.rate_id = 0, reservations.total, reservations.total/rates.total_usage) as price, rates.description as PassType ,
           date(events.start_time) As EventDate
           FROM mapco_qrcodes 
           left join events on events.id=mapco_qrcodes.event_id 
           left join reservations on reservations.id=mapco_qrcodes.reservation_id
           left join rates on rates.id=reservations.rate_id 
           left join users on users.id=reservations.user_id 
           left join tickets on tickets.event_id=mapco_qrcodes.event_id and tickets.reservation_id = mapco_qrcodes.reservation_id
           where  date(events.start_time) >= '$from_date'  and date(events.start_time) <= '$to_date' and   mapco_qrcodes.deleted_at is   AND reservations.deleted_at is NULL AND reservations.cancelled_at is NULL and IF(mapco_qrcodes.total_usage = mapco_qrcodes.remain_usage, 'Unused', 'Used') = 'Used'   
          UNION
          SELECT users.email, users.phone, reservations.ticketech_code ,tickets.checkin_time,tickets.checkin_gate, 'Used'  as Status, IF(reservations.rate_id = 0, tickets.total, tickets.total/rates.total_usage) as price, rates.description as PassType  ,date( tickets.checkin_time) As EventDate FROM tickets
          INNER JOIN users 
          ON users.id = tickets.user_id
          INNER JOIN events 
          ON events.id = tickets.event_id
          join reservations on reservations.id=tickets.reservation_id left join rates on rates.id=reservations.rate_id where tickets.facility_id='$facility_id' and rates.description='6 Pack' and date(tickets.checkin_time) > '$from_date' and date(tickets.checkin_time) < '$to_date'"; */

		$sqlQueryEvent = "SELECT date(start_time) As EventDate FROM events where partner_id IN (1553) and is_active =1 and deleted_at is null order by date(start_time) asc ";

		$eventResults = DB::select($sqlQueryEvent);

		$sqlQueryDriveUp = "SELECT users.email, users.phone, '' as ticketech_code , DATE_FORMAT(tickets.checkin_time,'%m/%d/%Y %H:%i:%s') as checkin_time, tickets.checkin_gate, 'Drive-Up'  as status, tickets.grand_total as price, 'Drive up' as PassType  , date( tickets.checkin_time) As EventDate 
              from tickets
              inner join users on users.id=tickets.user_id
              inner join events 
              ON events.id = tickets.event_id
              where  tickets.reservation_id Is null
              and tickets.facility_id = '$facility_id'  and  date(events.start_time) >= '$from_date'  and date(events.start_time) <= '$to_date'  order by EventDate desc";
		$driveResults = DB::select($sqlQueryDriveUp);
		// die;
		$eventsDates = [];
		$driveUpDates = [];

		foreach ($eventResults as $key => $value) {
			if (!in_array($value->EventDate, $eventsDates))
				array_push($eventsDates, $value->EventDate);
		}

		foreach ($driveResults as $key => $value) {
			if (!in_array($value->EventDate, $driveUpDates))
				array_push($driveUpDates, $value->EventDate);
		}
		$result['data'] = ['events' => $eventsDates, 'driveup' => $driveUpDates];
		return $result;
	}

	public function setCustomTimezone($facility_id)
	{
		$facility = Facility::find($facility_id);
		$secret = OauthClient::where('partner_id', $facility->owner_id)->first();
		$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
		if ($partnerTimezone) {
			if ($facility->timezone != '') {
				date_default_timezone_set($facility->timezone);
			} else if ($partnerTimezone->timezone != '') {
				date_default_timezone_set($partnerTimezone->timezone);
			}
		}
	}

	public  function getFacilityEventStartRate(Request $request)
	{

		$parkingData = [];
		$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
		if (!$secret) {
			throw new ApiGenericException('Invalid partner.');
		}
		if ($request->header('X-ClientSecret') != '') {
			$facility = Facility::find($request->facility_id);
			if (!$facility) {
				throw new ApiGenericException('Invalid garage.');
			}

			$this->setCustomTimezone($facility->id);

			$today = date("Y-m-d");
			$facility_id = $facility->id;
			//$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
			$sqlQuery = "SELECT e.*
                FROM events as e
                inner join event_facility as ef on ef.event_id= e.id        
                WHERE e.partner_id IN ({$facility->owner_id}) AND date(e.start_time) = '" . $today . "' AND date(e.end_time) >= '" . $today . "' AND e.deleted_at is null AND ef.facility_id IN (" . $facility_id . ") AND e.is_active='1' AND e.deleted_at is NULL";

			$results =  DB::select($sqlQuery);
			if (count($results) > 0) {
				$todayEvent = new Fluent($results);
				$todayEvent = $todayEvent[0];
				if (!$todayEvent) {
					$data['is_event_started'] =  '0';
					return $data;
				}
				$parkingNowTime = date("Y-m-d H:i:s");
				$parkingStartTime = $todayEvent->parking_start_time;
				$parkingEndTime = $todayEvent->parking_end_time;
				$eventStartTime = $todayEvent->start_time;
				$eventEndTime = $todayEvent->end_time;
				if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
					//$data['price'] =  number_format($todayEvent->event_rate + $facility->processing_fee, 2);
					$data['is_event_started'] =  '1';
					$driveupRate = 0;
					if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
						$rate['price'] = $todayEvent->driveup_event_rate;
						$taxRate = $facility->getTaxRate($rate);          // to get tax price                
						$driveupRate =  number_format($todayEvent->driveup_event_rate + $facility->drive_up_processing_fee + $taxRate, 2);
					} else {
						$rate['price'] = $facility->base_rate;
						$taxRate = $facility->getTaxRate($rate);          // to get tax price                
						$driveupRate =  number_format($facility->base_rate + $facility->processing_fee + $taxRate, 2);
					}
					$data['price'] 		=  $driveupRate;
					$data['event_id'] 	= $todayEvent->id;
					$data['title'] 		= $todayEvent->title;
					return $data;
				} else {
					$data['is_event_started'] =  '0';
					return $data;
				}
			} else {
				$data['is_event_started'] =  '0';
				return $data;
			}
		}
	}



	public function sendUserEmailCheckin(Request $request)
	{

		$id  = $request->id;
		$email  = $request->email;
		$resp = Ticket::with(['userPass.transaction', 'reservation.transaction', 'transaction', 'facility', 'user'])->where('id', $id)->first();
		if (($resp->user_id == '') || ($resp->user_id == NULL)) {
			throw new ApiGenericException('User Details not found');
		}
		if ($resp) {
			$gates = Gate::where('facility_id', $resp->facility_id)->get();
			if ($gates) {
				foreach ($gates as $key => $value) {
					if ($value->gate == $resp->checkin_gate) {
						$resp['checkin_gate_name'] = $value->gate_name;
					}
					if ($value->gate == $resp->checkout_gate) {
						$resp['checkout_gate_name'] = $value->gate_name;
					}
				}
			} else {
				$resp['checkin_gate_name'] = '';
				$resp['checkout_gate_name'] = '';
			}

			if ($resp->partner_id == 45) {
				Artisan::queue('send:checkin-checkout-email', array('id' => $id, 'email' => $email));
				return 'Email successfully sent to the user.';
			} elseif ($resp->partner_id == 2980) {
				Artisan::queue('send:checkin-checkout-email', array('id' => $id, 'email' => $email));
				return 'Email successfully sent to the user.';
			} else if ($resp->partner_id == 3307) {
				Artisan::queue('send:checkin-checkout-email', array('id' => $id, 'email' => $email));
				return 'Email successfully sent to the user.';
			} else if ($resp->partner_id == 3307) {
				Artisan::queue('send:checkin-checkout-email', array('id' => $id, 'email' => $email));
				return 'Email successfully sent to the user.';
			} else if ($resp->partner_id == 3986) {
				Artisan::queue('send:checkin-checkout-email', array('id' => $id, 'email' => $email));
				return 'Email successfully sent to the user.';
			} else if ($resp->partner_id == 4558) {
				Artisan::queue('send:checkin-checkout-email', array('id' => $id, 'email' => $email));
				return 'Email successfully sent to the user.';
			} else if ($resp->partner_id == config('parkengage.PARTNER_MAPCO')) {
				Artisan::queue('mapco:reservation-email-checkin', array('id' => $id, 'email' => $email));
				return 'Email successfully sent to the user.';
			} else {
				Artisan::queue('send:checkin-checkout-email', array('id' => $id, 'email' => $email));
				return 'Email successfully sent to the user.';
			}
		} else {
			throw new ApiGenericException('Ticket id not found');
		}
	}


	public function downloadQrCodeCheckin($id)
	{
		$data = Ticket::with(['userPass.transaction', 'reservation.transaction', 'transaction', 'facility', 'user', 'facility.photos'])->where('id', $id)->first();
		//return $data;
		$data->parkingamount = $data->parking_amount;
		if (!$data) {
			throw new ApiGenericException("Invalid booking details.");
		}
		// $grandTotal = $data->grand_total;
		$grandTotal = $taxFee = $discountAmount = $parkingAmount = $additional_fee = $surcharge_fee = $extendprocessingFee = 0;
		if ($data->is_extended == '1') {
			$ticketExtends = TicketExtend::with(['transaction'])->where('ticket_number', $data->ticket_number)->orderby('id', 'asc')->get();
			foreach ($ticketExtends as $key => $value) {
				$parkingAmount        += ($value->total - ($value->tax_fee + $value->processing_fee + $value->additional_fee + $value->surcharge_fee));
				$discountAmount       += $value->discount_amount;
				$grandTotal           += $value->grand_total;
				$additional_fee       += $value->additional_fee;
				$surcharge_fee        += $value->surcharge_fee;
				$taxFee               += $value->tax_fee;
				$data->checkout_time  = $value->checkout_time;
				$extendprocessingFee   += $value->processing_fee;
				$this->log->info("Ticket Extend Data Found -- " . $grandTotal);
			}
			$data->surcharge_fee      += sprintf("%.2f", $surcharge_fee);
			$data->additional_fee     += sprintf("%.2f", $additional_fee);
			$data->grand_total        += sprintf("%.2f", $grandTotal);
			$data->parking_amount     += sprintf("%.2f", $parkingAmount);
			$data->discount_amount    += sprintf("%.2f", $discountAmount);
			$data->tax_fee            += sprintf("%.2f", $taxFee);
			$data->processing_fee     += sprintf("%.2f", $extendprocessingFee);
			$data->ticket_extends     = $ticketExtends;
			// dd($ticketExtends, $data);
		}
		$discountAmountForTax =   $data->discount_amount;
		$processingFeeForTax = $data->processing_fee;
		$taxForTaxFee = $data->tax_fee;

		if (in_array($data->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
			$data->ticketadditionalinfo = $data->ticketadditionalinfo() ?? null;
			if (!empty($data->ticketadditionalinfo)) {
				$parkingAmount = 0;
				if (!empty($ticketExtends) && $ticketExtends->count() > 0) {
					$parkingAmount = 0;

					foreach ($ticketExtends as $key => $exted) {
						$parkingAmount += ($exted->new_parking_amount);
					}

					$parkingAmount  = $data->parkingamount + $parkingAmount;
				} else {
					$parkingAmount  = $data->parking_amount;
				}
				$data->parking_amount = number_format($parkingAmount, 2);
				$data->processing_fee = $data->ticketadditionalinfo->new_processing_fee;

				$data->tax_fee = $data->ticketadditionalinfo->new_tax_amount;
				$data->discount_amount = $data->ticketadditionalinfo->new_discount_amount;
				$data->total = $data->ticketadditionalinfo->new_processing_fee + $data->ticketadditionalinfo->new_tax_amount + $data->ticketadditionalinfo->new_parking_amount;
			}
		}

		if ($data->facility->tax_with_surcharge_enable == '1') {

			$data->discount_amount = $discountAmountForTax;
			$data->processing_fee = $processingFeeForTax;
			$data->tax_fee = $taxForTaxFee;
			$data->ticketadditionalinfo = null;
			if ($data->discount_amount > 0) {
				$data->total = $discountAmountForTax + $data->total + $data->processingFeeForTax + $taxForTaxFee + $data->surcharge_fee + $data->processing_fee + $data->additional_fee;
			}
		}


		if ($data->promocode) {
			if ($data->discount_hours > 0) {
				$data->promocodeLabel = "Validated Amount(" . $data->discount_hours . " Hrs Free)";
			} else {
				$data->promocodeLabel = "Validated Amount";
			}
		}

		$brand_setting = FacilityBrandSetting::where('facility_id', $data->facility->id)->first();

		if ($brand_setting) {
			$data->facility_logo_id = $brand_setting->id;
			$data->color = $brand_setting->color ?? "#4a90e2";
		} else {
			$brand_setting = BrandSetting::where('user_id', $data->partner_id)->first();
			$data->logo_id = $brand_setting->id;
			$data->color = $brand_setting->color ?? "#4a90e2";
		}
		if (in_array($data->paid_type, [0, 1, 2, 3, 4])) {
			$data->discount_amount = "0.00";
		}
		$data->addressLink = $data->facility->generateAddressLink();
		$data->hours = $data->facility->hoursOfOperation;
		$pdf = (new MapcoQrcodeCheckin())->generatePdf($data, Pdf::class);
		return $pdf;
	}


	public function makeBookingBeforePayment(Request $request)
	{


		$today = date('Y-m-d');

		$this->log->info("MapCo booking request before payment received : " . json_encode($request->all()));
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}

			$checkFacility = Facility::where('id', $request->facility_id)->where('owner_id', $secret->partner_id)->first();
			if (!$checkFacility) {
				throw new NotFoundException('No garage found with this partner.');
			}
			$this->facility = $checkFacility;
			if ($checkFacility->active != '1') {
				throw new ApiGenericException('Garage is not available for any booking');
			}
			if ($checkFacility->is_available != '1') {
				throw new ApiGenericException('Garage is not available for any booking');
			}
			if ($checkFacility->facility_booking_type == '1') {
				throw new ApiGenericException('Garage is not available for any booking');
			}
			if (!$checkFacility->accept_cc) {
				throw new ApiGenericException("Garage does not accept credit cards");
			}

			$this->facility = $checkFacility;

			/*if(!(isset($request->is_third_party_promo)) || !($request->is_third_party_promo)) {
          $promocode = $this->validatePromocodeRequest($request);
      }*/
			//$this->request->promo_applied != '1' && 
			/*if($this->request->total > 0){
        if($this->request->TokenNo == '' || $this->request->AuthorisationCode == ''){
            throw new ApiGenericException("Sorry! Payment not successfully done, There is some issue in payment response.");
        }
  
        $alreadyTransactionDone  = AuthorizeNetTransaction::where('ref_id', $this->request->MerchantRef)->first();
        if(count($alreadyTransactionDone) == 0){
          $alreadyTransactionDone  = AuthorizeNetTransaction::where('anet_trans_id', $this->request->TxID)->first();      
        }
        if(count($alreadyTransactionDone) > 0){
            $message = "Booking already done by the user.";
            return ["message"=>$message];
        }
      }*/
			if ($this->request->arrival == '') {
				throw new ApiGenericException("Event start date can not empty.");
			}

			if ($this->request->phone != '') {
				/*   
          $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
          if($geoLocation['geoplugin_countryCode'] == 'IN'){
                $this->countryCode = "+91";
          }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
                $this->countryCode = "+1";
          }else{
                $this->countryCode = "+1";
          }
		  */
				$this->countryCode = "+1";
				$existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $checkFacility->owner_id)->first();
				if ($existPhone) {
					if ($this->request->email != '') {
						$existPhone->email = $this->request->email;
					}
					$existPhone->name = $this->request->name;
					$existPhone->save();
					$this->user = $existPhone;
				} else {

					$this->user = User::create(
						[
							'name' => $this->request->name,
							'email' => $this->request->email,
							'phone' => $this->countryCode . $this->request->phone,
							'password' => Hash::make(str_random(60)),
							'anon' => true,
							'user_type' => '5',
							'created_by' => $checkFacility->owner_id
						]
					);
				}
				Reservation::where("user_id", $this->user->id)->whereNull("anet_transaction_id")->delete();
				$details = $this->makeReservation();

				if ($this->request->event_ids) {
					$event_ids = implode(",", $this->request->event_ids);
				} else {
					$event_ids = "";
				}
				//return $event_ids;
				// Before Payment Qrcode Request Capture	  
				$data['reservation_id'] = $details['id'];
				$data['event_id'] = $this->request->event_id ?  $this->request->event_id : '';
				$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
				$data['qrcode'] = '';
				$data['total_usage'] = '1';
				$data['remain_usage'] = '1';
				$data['mer_reference'] = $this->request->reference ?  $this->request->reference : '';
				$data['event_type'] = $this->request->event_type ? $this->request->event_type : '';
				$data['rate_id'] = $this->request->rate_id ? $this->request->rate_id : '';
				$data['event_ids'] = $event_ids;
				MapcoBeforeQrcode::create($data);

				return $details;
			} else {
				throw new ApiGenericException("Please enter valid phone number.");
			}
		}
	}

	public function paymentSuccess(Request $request)
	{

		$today = date('Y-m-d');
		$this->log->info("Mapco after payment request received : " . json_encode($request->all()));
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();

			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}

			if ($request->facility_id != '') {
				$checkFacility = Facility::where('id', $request->facility_id)->first();
				$this->facility = $checkFacility;
			}


			if ($this->request->is_booking_direct == '1') {
				$this->log->info("Direct Booking Starts");
				if ($this->request->arrival == '') {
					throw new ApiGenericException("Event start date can not empty.");
				}
				$alreadyTransactionDone  = Reservation::where('mer_reference', $this->request->reference)->first();
				if (count($alreadyTransactionDone) > 0) {
					$message = "Booking already done by the user.";
					return ["message" => $message];
				}

				if ($this->request->phone != '') {

					$this->countryCode = "+1";
					$existPhone = User::where('phone', $this->countryCode . $this->request->phone)->where('created_by', $secret->partner_id)->first();
					if ($existPhone) {
						if ($this->request->email != '') {
							$existPhone->email = $this->request->email;
						}
						$existPhone->name = $this->request->name;
						$existPhone->save();
						$this->user = $existPhone;
					} else {

						$this->user = User::create(
							[
								'name' => $this->request->name,
								'email' => $this->request->email,
								'phone' => $this->countryCode . $this->request->phone,
								'password' => Hash::make(str_random(60)),
								'anon' => true,
								'user_type' => '5',
								'created_by' => $secret->partner_id
							]
						);
					}

					$details = $this->makeReservation();
					$request->request->add(['payment_status' => 'success']);
					$user = User::where('id', $details['user_id'])->first();
					$checkFacility = Facility::where('id', $details['facility_id'])->first();
					$this->facility = $checkFacility;

					if (isset($details) && !empty($details) && $this->request->payment_status == 'success') {

						if ($this->request->event_type == 'event') {
							$data['reservation_id'] = $details['id'];
							$data['event_id'] = $this->request->event_id ?  $this->request->event_id : '';
							$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
							$data['qrcode'] = $this->checkQrCode();
							$data['total_usage'] = '1';
							$data['remain_usage'] = '1';
							MapcoQrcode::create($data);
						}
						if ($this->request->event_type == 'bundle-pass') {
							try {
								$rate = Rate::find($this->request->rate_id);
								if ($rate->is_full_season == '0') {
									$eventIds = $this->request->event_ids;
									$eventIds = array_slice($eventIds, 0, 18);
									$selectedEvent = Event::whereIn('id', $eventIds)->whereDate('start_time', '>=', $today)->where('is_active', '1')->get();
									for ($j = 0; $j < count($selectedEvent); $j++) {
										for ($i = 0; $i < count($selectedEvent) - 1; $i++) {
											if (strtotime($selectedEvent[$i]['start_time']) >= strtotime($today)) {
												if (strtotime($selectedEvent[$i]['start_time']) > strtotime($selectedEvent[$i + 1]['start_time'])) {
													$temp = $selectedEvent[$i + 1];
													$selectedEvent[$i + 1] = $selectedEvent[$i];
													$selectedEvent[$i] = $temp;
												}
											}
										}
									}
									foreach ($selectedEvent as $key => $value) {
										$data['reservation_id'] = $details['id'];
										$data['event_id'] = $value->id;
										$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
										$data['qrcode'] = $this->checkQrCode();
										$data['total_usage'] = '1';
										$data['remain_usage'] = '1';
										MapcoQrcode::create($data);
									}
								} else {
									$eventCategory = EventCategoryEvent::where('event_category_id', $this->request->event_category_id)->get();
									$eventIds = [];
									foreach ($eventCategory as $key => $value) {
										$eventIds[] = $value->event_id;
									}
									$selectedEvent = Event::whereIn('id', $eventIds)->whereDate('start_time', '>=', $today)->where('is_active', '1')->get();
									for ($j = 0; $j < count($selectedEvent); $j++) {
										for ($i = 0; $i < count($selectedEvent) - 1; $i++) {
											if (strtotime($selectedEvent[$i]['start_time']) >= strtotime($today)) {
												if (strtotime($selectedEvent[$i]['start_time']) > strtotime($selectedEvent[$i + 1]['start_time'])) {
													$temp = $selectedEvent[$i + 1];
													$selectedEvent[$i + 1] = $selectedEvent[$i];
													$selectedEvent[$i] = $temp;
												}
											}
										}
									}
									foreach ($selectedEvent as $key => $value) {
										$data['reservation_id'] = $details['id'];
										$data['event_id'] = $value->id;
										$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
										$data['qrcode'] = $this->checkQrCode();
										$data['total_usage'] = '1';
										$data['remain_usage'] = '1';
										MapcoQrcode::create($data);
									}
								}
							} catch (\Exception $e) {
								throw $e;
							}
						}
						if ($this->request->event_type == 'party-pass') {
							$rate = Rate::find($this->request->rate_id);
							$data['reservation_id'] = $details['id'];
							$data['event_id'] = '';
							$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
							$data['qrcode'] = $this->checkQrCode();
							$data['total_usage'] = $rate->total_usage;
							$data['remain_usage'] = $rate->total_usage;
							MapcoQrcode::create($data);
						}
						if ($this->request->event_type == 'simple') {
							$data['reservation_id'] = $details['id'];
							$data['event_id'] = $this->request->event_id ?  $this->request->event_id : '';
							$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
							$data['qrcode'] = $this->checkQrCode();
							MapcoQrcode::create($data);
						}
						$qrcode = MapcoQrcode::with(['event', 'eventCategory.eventCategoryEvent.event'])->where('reservation_id', $details['id'])->get();
						if ($this->request->event_type == 'party-pass') {
							foreach ($qrcode as $key => $val) {
								$val->parking_time_message = '*Event parking starts 3 hours before the Event starts, & Ends 1 hour before the Event ends.';
							}
						}
						foreach ($qrcode as $key => $val) {
							if (isset($val->event->parking_start_time) && !empty($val->event->parking_start_time) && isset($val->event->parking_end_time) && !empty($val->event->parking_end_time)) {
								$val->event->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($val->event->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($val->event->parking_end_time));
							}
						}
						$details['mapco_qrcode'] = $qrcode;

						if (isset($details['rate_id'])) {
							$rateDetails = Rate::where('id', $details['rate_id'])->first();
							$details['rates'] = $rateDetails;
						} else {
							$details['rates'] = [];
						}
						//send QR code to user			
						$this->log->info("Check Mapco qrcode count. Direct Booking");
						if (count($qrcode) > 0) {
							$this->log->info("Mapco Email & SMS Send Start. Direct Booking");
							Artisan::queue('mapco:reservation-email', array('id' => $details['id']));
							$smsTotal = number_format($details['total'], 2);
							$msg = "Thank you for booking your parking with " .  ucwords($checkFacility->full_name) . ".\nBooking # : " . $details['ticketech_code'] . " \nAmount Charged : $$smsTotal";
							$this->customeReplySms($msg, $user->phone);
							$this->log->info("Mapco Email & SMS Send Done. Direct Booking");
						}

						if ($this->request->event_id != '') {
							$event = Event::find($this->request->event_id);
							if ($event) {
								$details['actual_amount'] = number_format(($event->event_rate + $this->facility->processing_fee) - $this->request->total, 2);
							}
						}
						if ($this->request->rate_id != '') {
							$rate = Rate::find($this->request->rate_id);
							if ($rate) {
								$tax_rate = $this->facility->processing_fee * $rate->total_usage;
								$details['actual_amount'] = number_format(($rate->price + $tax_rate) - $this->request->total, 2);
							}
						}
						return $details;
					} else {
						throw new ApiGenericException("Error in booking.");
					}
				} else {
					throw new ApiGenericException("Please enter valid phone number.");
				}
			} else {
				if ($this->request->reference) {
					$details = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->where('mer_reference', $this->request->reference)->first();
				} else {
					throw new ApiGenericException("Merchant Reference Not Found");
				}
				$user = User::where('id', $details['user_id'])->first();
				$checkFacility = Facility::where('id', $details['facility_id'])->first();
				$this->facility = $checkFacility;
				if (isset($details) && !empty($details) && $this->request->payment_status == 'success') {
					/*
					if($this->request->event_type == 'event'){
						$data['reservation_id'] = $details['id'];
						$data['event_id'] = $this->request->event_id ?  $this->request->event_id : '';
						$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
						$data['qrcode'] = $this->checkQrCode();
						$data['total_usage'] = '1';
						$data['remain_usage'] = '1';
						MapcoQrcode::create($data);
					}
					if($this->request->event_type == 'bundle-pass'){
						try{
						  $rate = Rate::find($this->request->rate_id);    
						  if($rate->is_full_season == '0'){
							$eventIds = $this->request->event_ids;
							$eventIds = array_slice($eventIds, 0, 18);
							$selectedEvent = Event::whereIn('id',$eventIds)->whereDate('start_time','>=', $today)->where('is_active', '1')->get();
							for($j = 0; $j < count($selectedEvent); $j ++) {
								for($i = 0; $i < count($selectedEvent)-1; $i ++){ 
									if(strtotime($selectedEvent[$i]['start_time']) >= strtotime($today)) {
										if(strtotime($selectedEvent[$i]['start_time']) > strtotime($selectedEvent[$i+1]['start_time'])) {
											$temp = $selectedEvent[$i+1];
											$selectedEvent[$i+1]=$selectedEvent[$i];
											$selectedEvent[$i]=$temp;
										}
									}
								}
							}
							foreach($selectedEvent as $key=>$value){
								$data['reservation_id'] = $details['id'];
								$data['event_id'] = $value->id;
								$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
								$data['qrcode'] = $this->checkQrCode();
								$data['total_usage'] = '1';
								$data['remain_usage'] = '1';
								MapcoQrcode::create($data);
							}
						  }else{
							$eventCategory = EventCategoryEvent::where('event_category_id' , $this->request->event_category_id)->get();                  
							$eventIds = [];
							foreach($eventCategory as $key=>$value){
								$eventIds[] = $value->event_id;
							}
							$selectedEvent = Event::whereIn('id',$eventIds)->whereDate('start_time','>=',$today)->where('is_active', '1')->get();                  
							for($j = 0; $j < count($selectedEvent); $j ++) {
								for($i = 0; $i < count($selectedEvent)-1; $i ++){
									  if(strtotime($selectedEvent[$i]['start_time']) >= strtotime($today)) {            
										if(strtotime($selectedEvent[$i]['start_time']) > strtotime($selectedEvent[$i+1]['start_time'])) {
											$temp = $selectedEvent[$i+1];
											$selectedEvent[$i+1]=$selectedEvent[$i];
											$selectedEvent[$i]=$temp;
										}     
									}
								}
							}
							foreach($selectedEvent as $key=>$value){
								$data['reservation_id'] = $details['id'];
								$data['event_id'] = $value->id;
								$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
								$data['qrcode'] = $this->checkQrCode();
								$data['total_usage'] = '1';
								$data['remain_usage'] = '1';
								MapcoQrcode::create($data);
							}
						  }
						} catch (\Exception $e) {
							throw $e;
						}
					}
					if($this->request->event_type == 'party-pass'){
						$rate = Rate::find($this->request->rate_id);
						$data['reservation_id'] = $details['id'];
						$data['event_id'] = '';
						$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
						$data['qrcode'] = $this->checkQrCode();
						$data['total_usage'] = $rate->total_usage;
						$data['remain_usage'] = $rate->total_usage;
						MapcoQrcode::create($data);
					}
					if($this->request->event_type == 'simple'){
						$data['reservation_id'] = $details['id'];
						$data['event_id'] = $this->request->event_id ?  $this->request->event_id : '';
						$data['event_category_id'] = $this->request->event_category_id ?  $this->request->event_category_id : '';
						$data['qrcode'] = $this->checkQrCode();
						MapcoQrcode::create($data);
					}
					*/
					$qrcode = MapcoQrcode::with(['event', 'eventCategory.eventCategoryEvent.event'])->where('reservation_id', $details['id'])->get();
					if ($this->request->event_type == 'party-pass') {
						foreach ($qrcode as $key => $val) {
							$val->parking_time_message = '*Event parking starts 3 hours before the Event starts, & Ends 1 hour before the Event ends.';
						}
					}
					foreach ($qrcode as $key => $val) {
						if (isset($val->event->parking_start_time) && !empty($val->event->parking_start_time) && isset($val->event->parking_end_time) && !empty($val->event->parking_end_time)) {
							$val->event->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($val->event->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($val->event->parking_end_time));
						}
					}
					$details['mapco_qrcode'] = $qrcode;

					if (isset($details['rate_id'])) {
						$rateDetails = Rate::where('id', $details['rate_id'])->first();
						$details['rates'] = $rateDetails;
					} else {
						$details['rates'] = [];
					}
					//send QR code to user		
					/*	
					$this->log->info("Check Mapco qrcode count");
					if(count($qrcode) > 0){
						$this->log->info("Mapco Email & SMS Send Start");
						Artisan::queue('mapco:reservation-email',array('id' => $details['id']));
						$smsTotal = number_format($details['total'], 2);
						$msg = "Thank you for booking your parking with ".  ucwords($checkFacility->full_name) .".\nBooking # : ".$details['ticketech_code']." \nAmount Charged : $$smsTotal";
						$this->customeReplySms($msg, $user->phone);      
						$this->log->info("Mapco Email & SMS Send Done"); 		
					}
				    */
					if ($this->request->event_id != '') {
						$event = Event::find($this->request->event_id);
						if ($event) {
							$details['actual_amount'] = number_format(($event->event_rate + $this->facility->processing_fee) - $this->request->total, 2);
						}
					}
					if ($this->request->rate_id != '') {
						$rate = Rate::find($this->request->rate_id);
						if ($rate) {
							$tax_rate = $this->facility->processing_fee * $rate->total_usage;
							$details['actual_amount'] = number_format(($rate->price + $tax_rate) - $this->request->total, 2);
						}
					}
					return $details;
				} else {
					throw new ApiGenericException("Payment Failed. Error in booking.");
				}
			}
		}
	}

	public function updatePaymentStatus(Request $request)
	{

		$this->log->info("Mapco payment status check request received : " . json_encode($request->all()));
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}

			$details = Reservation::with(['ticket', 'user', 'userPass', 'rate.rateType', 'transaction'])->where('partner_id', $secret->partner_id)->where('mer_reference', $this->request->MerchantRef)->first();

			if (isset($details) && !empty($details) && !empty($details->anet_transaction_id)) {
				$qrcode = MapcoQrcode::with(['event', 'eventCategory.eventCategoryEvent.event'])->where('reservation_id', $details['id'])->get();
				$details['mapco_qrcode'] = $qrcode;

				if (isset($details['rate_id'])) {
					$rateDetails = Rate::where('id', $details['rate_id'])->first();
					$details['rates'] = $rateDetails;
				} else {
					$details['rates'] = [];
				}

				return $details;
			} else {
				throw new ApiGenericException("Payment  Status Not Update.");
			}
		}
	}

	public function generateBarcodeJpgNew($qrcode)
	{
		$html = $this->generateBarcodeHtml($qrcode);

		$image = app()->make(Image::class);
		$image->setOption('width', '420');
		return $image->getOutputFromHtmlString($html);
	}
	public function generateBarcodeHtml($qrcode)
	{
		return view('platform.email.Qrcodegenerate', ['barcode' => $qrcode]);
	}

	// pci planet session

	public function planetPaymentSessionPci(Request $request)
	{
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Invalid garage.');
		}
		$price = 0;
		if ($request->rate_id != '') {
			$rate = Rate::find($request->rate_id);
			if (!$rate) {
				throw new ApiGenericException('Rate does not match in our database.');
			}
			$tax_rate = $facility->processing_fee * $rate->total_usage;
			if ($rate->pass_type_status == "0" && ($rate->event_id == "" || $rate->event_id == "0") && ($rate->event_category_id == '' || $rate->event_category_id == '0')) {
				$tax_rate = $facility->pass_processing_fee;
			} else if ($rate->pass_type_status == "1" && ($rate->event_id == "0") && $rate->event_category_id != '') {
				//changes for USM party pass flow by vikrant
				$tax_rate = $facility->pass_processing_fee;
			}
			$price = $rate->price + $tax_rate;
		} else {
			if ($request->event_id != '') {
				$event = Event::find($request->event_id);
				if (!$event) {
					throw new ApiGenericException('Event does not match in our database.');
				}
				if ($event->event_rate == '' || $event->event_rate == '0.00') {
					$price = $facility->base_event_rate;
				} else {
					$price = $event->event_rate;
				}
			}
			if ($price > 0) {
				$tax_rate = (float) $facility->processing_fee;
				$price = $price + $tax_rate;
			} else {
				$tax_rate = (float) 0.00;
				$price = "0.00";
			}
		}
		$request->request->add(['amount' => $price]);
		$request->request->add(['tax_rate' => $tax_rate]);
		$discountedAmount = 0;
		if (isset($request->promocode)) {
			$this->validate($request, Promotion::$checkPromoValidationRulesThirdParty);
			$request->request->add(['amount' => $price - $tax_rate]);
			$request->request->add(['processing_fee' => $tax_rate]);
			$response = PromoCodeLib::validatePromoCodeThirdParty($request);
			if (isset($response->getData()->is_promocode_valid) == '1') {
				if ($request->amount < $response->getData()->discount_in_dollar) {
					$data1 = $response->getData();
					//$data1->discount_in_dollar = $request->amount;
					$amount = $request->amount;
					if ($amount < $response->getData()->discount_in_dollar) {
						$amount = "0.00";
						$discountedAmount = $response->getData()->discount_in_dollar;
					} else {
						if ($response->getData()->is_tax_applicable == "0") {
							$amount = $price - $request->processing_fee - $response->getData()->discount_in_dollar;
							$discountedAmount = $response->getData()->discount_in_dollar;
						} else {
							$amount = $request->amount - $response->getData()->discount_in_dollar;
							$discountedAmount = $response->getData()->discount_in_dollar;
						}
						// $amount = "0.00";
						// $discountedAmount = $response->getData()->discount_in_dollar;
					}
					$response->setData($data1);
					//return $response;
				} else {
					if ($response->getData()->is_tax_applicable == "0") {
						$amount = $price - $response->getData()->discount_in_dollar;
						$discountedAmount = $response->getData()->discount_in_dollar;
					} else {
						if ($response->getData()->promocode->discount_type == "value") {
							$amount = $price - $response->getData()->discount_in_dollar;
						} else {
							$amount = $request->amount - $response->getData()->discount_in_dollar;
						}
						$discountedAmount = $response->getData()->discount_in_dollar;
					}
				}
			}
		} else {
			$amount = $price;
		}

		if ($request->amount == 0 || $request->amount < 0) {
			$amount = 0;
		} else {
			//$amount = number_format($amount, 2);      
			$amount = $amount;
		}

		$userDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();

		$cent = $amount * 100;
		// $reference = rand(1000, 9999).rand(1000, 9999).rand(1000, 9999);
		$reference = $this->checkReferenceNumber();

		$data['tax_rate'] =  $tax_rate;
		$data['amount'] =  (float) $amount;
		$data['discounted_amount'] =  (float) $discountedAmount;
		$data['reference'] =  $reference;
		return $data;
	}
	// Date wise event listing
	public function getEventDateList(Request $request)
	{
		if ($request->header('X-ClientSecret') != '' && $request->event_type) {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();

			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}

			if ($request->start_date == '' || $request->end_date == '') {
				throw new ApiGenericException('Please send date of month');
			}
			$startDate = date("Y-m-d" . " 00:00:00", strtotime($request->start_date));
			$endDate = date("Y-m-d" . " 23:59:59", strtotime($request->end_date));
			#PIMS: 11296
			#Kuldeep
			$request->facility_id = (array) $request->facility_id;

			#DD: pims-11406/11459(CS-439) 
			if (isset($request->all_event) && $request->all_event == "1" && isset($request->campus_id) && !empty($request->campus_id)) {
				$facilityIds = Facility::where('neighborhood_id', $request->campus_id)->where('owner_id', $secret->partner_id)
					->pluck('id')
					->toArray();
				$request->facility_id = $facilityIds;
			}
			//$today = date("Y-m-d");
			if ($request->event_type == 'event') {
				$partner_id = $secret->partner_id;
				$allevents = [];
				// $event = Event::with(['eventCategoryEvent.eventCategory', 'facilities', 'facilities.FacilityPaymentDetails', 'eventFacility'])
				//   ->where('partner_id', $secret->partner_id)->whereDate("start_time", "<=", $startDate)->whereDate("end_time", ">=", $endDate)
				//   ->where('is_active', '1')->orderBy('start_time', 'ASC');
				$event = Event::with(['eventCategoryEvent.eventCategory', 'facilities', 'facilities.FacilityPaymentDetails', 'eventFacility'])
					->where('partner_id', $secret->partner_id)
					->where(function ($query) use ($startDate, $endDate) {
						$query->whereBetween('parking_start_time', [$startDate, $endDate])
							->orWhereBetween('parking_end_time', [$startDate, $endDate])
							->orWhere(function ($subQuery) use ($startDate, $endDate) {
								$subQuery->where('parking_start_time', '<=', $startDate)
									->where('parking_end_time', '>=', $endDate);
							});
					});
				if ($request->facility_id) {
					$event = $event->WhereHas(
						'eventFacility',
						function ($query) use ($request) {
							$query->whereIn('facility_id', $request->facility_id);
						}
					);
				}
				$event = $event->where('is_active', '1')->orderBy('start_time', 'ASC');
				$event = $event->get();
				foreach ($event as $events) {
					#DD: pims-11406/11459(CS-439) 
					if ($request->facility_id) {
						$matchedFacilities = [];
						$unmatchedFacilities = [];
						$facilitiesArray = $events->facilities->toArray();

						foreach ($events->facilities as $facility) {
							if (in_array($facility->id, $request->facility_id)) {
								$matchedFacilities[] = $facility; // Facilities matching request
							} else {
								$unmatchedFacilities[] = $facility; // Other facilities
							}
						}

						$facilitiesArray = array_merge($matchedFacilities, $unmatchedFacilities);
						if (count($facilitiesArray) > 0) {
							unset($events->facilities);
						}
						$events->facilities = collect($facilitiesArray); // Reassign as a collection
					}
					$month =  date('m', strtotime($startDate));
					$monthName = date('M', strtotime($startDate));
					$yearName = date('Y', strtotime($startDate));
					//changes for USM specific done by vikrant after discuss by trapti mam
					if ($events->partner_id == config('parkengage.PARTNER_USM')) {
						if (!isset($events->eventCategoryEvent)) {
							if (isset($events->parking_start_time) && !empty($events->parking_start_time) && isset($events->parking_end_time) && !empty($events->parking_end_time)) {
								$events->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($events->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($events->parking_end_time));
							} else {
								$events->parking_time_message = '';
							}
							$allevents[$monthName . "-" . $yearName][] = $events;
						} else {
							if (isset($events->eventCategoryEvent->event_category_id)) {
								if ($events->event_rate > 0) {
									if (isset($events->parking_start_time) && !empty($events->parking_start_time) && isset($events->parking_end_time) && !empty($events->parking_end_time)) {
										$events->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($events->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($events->parking_end_time));
									} else {
										$events->parking_time_message = '';
									}
									$allevents[$monthName . "-" . $yearName][] = $events;
								} else {
									$rateEventCategoryExist = Rate::where("event_category_id", $events->eventCategoryEvent->event_category_id)->first();
									if ($rateEventCategoryExist) {
									} else {
										if (isset($events->parking_start_time) && !empty($events->parking_start_time) && isset($events->parking_end_time) && !empty($events->parking_end_time)) {
											$events->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($events->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($events->parking_end_time));
										} else {
											$events->parking_time_message = '';
										}
										$allevents[$monthName . "-" . $yearName][] = $events;
									}
								}
							}
						}
					} else {
						if (isset($events->parking_start_time) && !empty($events->parking_start_time) && isset($events->parking_end_time) && !empty($events->parking_end_time)) {
							$events->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($events->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($events->parking_end_time));
						} else {
							$events->parking_time_message = '';
						}
						$allevents[$monthName . "-" . $yearName][] = $events;
					}
				}
				if (count($allevents) < 1) {
					throw new ApiGenericException('Event not found');
				}
				return $allevents;
			}
			if ($request->event_type == 'bundle-pass') {
				$allrates = [];
				$rate = Rate::with(['facility.FacilityPaymentDetails', 'eventCategory.eventCategoryEvent.event', 'event' => function ($query) use ($startDate, $endDate) {
					$query->whereBetween('parking_start_time', [$startDate, $endDate])
						->orWhereBetween('parking_end_time', [$startDate, $endDate])
						->orWhere(function ($subQuery) use ($startDate, $endDate) {
							$subQuery->where('parking_start_time', '<=', $startDate)
								->where('parking_end_time', '>=', $endDate);
						});
				}])->whereIn('facility_id', $request->facility_id)->where('id', $request->pass_id)->whereDate("start_date", "<=", $startDate)->whereDate("end_date", ">=", $endDate)->orderBy('start_date', 'ASC')->first();
				$new = [];
				if ($rate->eventCategory) {
					foreach ($rate->eventCategory->eventCategoryEvent as $value) {
						if ($value->event) {
							if ($value->event->is_active == '1') {
								//if ($value->event->start_time >= $today) {
								if (isset($value->event->parking_start_time) && !empty($value->event->parking_start_time) && isset($value->event->parking_end_time) && !empty($value->event->parking_end_time)) {
									$value->event->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($value->event->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($value->event->parking_end_time));
								} else {
									$value->event->parking_time_message = '';
								}
								$new[] = $value->event;
								//}
							}
						}
					}
				}
				for ($j = 0; $j < count($new); $j++) {
					for ($i = 0; $i < count($new) - 1; $i++) {
						if (strtotime($new[$i]['start_time']) > strtotime($new[$i + 1]['start_time'])) {
							$temp = $new[$i + 1];
							$new[$i + 1] = $new[$i];
							$new[$i] = $temp;
						}
					}
				}
				$rate['all_event'] = $new;
				return $rate;
			}

			if ($request->event_type == 'party-pass') {
				$allrates = [];
				//added condition for USM party pass by vikrant
				if ($secret->partner_id == config('parkengage.PARTNER_USM')) {
					$rate = Rate::with(['facility.FacilityPaymentDetails', 'eventCategory.eventCategoryEvent.event', 'event' => function ($query) use ($startDate, $endDate) {
						$query->whereBetween('parking_start_time', [$startDate, $endDate])
							->orWhereBetween('parking_end_time', [$startDate, $endDate])
							->orWhere(function ($subQuery) use ($startDate, $endDate) {
								$subQuery->where('parking_start_time', '<=', $startDate)
									->where('parking_end_time', '>=', $endDate);
							});
					}])->whereIn('facility_id', $request->facility_id)->where('id', $request->pass_id)->first();
				} else {
					$rate = Rate::with(['facility.FacilityPaymentDetails', 'eventCategory.eventCategoryEvent.event', 'event' => function ($query) use ($startDate, $endDate) {
						$query->whereBetween('parking_start_time', [$startDate, $endDate])
							->orWhereBetween('parking_end_time', [$startDate, $endDate])
							->orWhere(function ($subQuery) use ($startDate, $endDate) {
								$subQuery->where('parking_start_time', '<=', $startDate)
									->where('parking_end_time', '>=', $endDate);
							});
					}])->whereIn('facility_id', $request->facility_id)->where('id', $request->pass_id)->whereDate("start_date", "<=", $startDate)->whereDate("end_date", ">=", $endDate)->first();
				}
				//dd($today, $request->pass_id, $request->facility_id, $rate);
				$new = [];
				if ($rate->eventCategory) {
					foreach ($rate->eventCategory->eventCategoryEvent as $value) {

						if ($value->event) {
							//echo $value->event->is_active .'--'.$value->event->title .'--'.$value->event->start_time."<br/>";
							if ($value->event->is_active == '1') {
								$startMonth = date("Y-m", strtotime($value->event->start_time));
								$todayMonth = date("Y-m", strtotime($startDate));
								//echo $startDate .'----'.$today."\n";
								//if (strtotime($startMonth) >= strtotime($todayMonth) && strtotime($startMonth) <= strtotime($todayMonth)) {
								if (isset($value->event->parking_start_time) && !empty($value->event->parking_start_time) && isset($value->event->parking_end_time) && !empty($value->event->parking_end_time)) {
									$value->event->parking_time_message = '*Event parking starts at ' . date('g:i A', strtotime($value->event->parking_start_time)) . ' & ends at ' . date('g:i A', strtotime($value->event->parking_end_time));
								} else {
									$value->event->parking_time_message = '';
								}
								$new[] = $value->event;
								//}
							}
						}
					}
					for ($j = 0; $j < count($new); $j++) {
						for ($i = 0; $i < count($new) - 1; $i++) {

							if (strtotime($new[$i]['start_time']) > strtotime($new[$i + 1]['start_time'])) {
								$temp = $new[$i + 1];
								$new[$i + 1] = $new[$i];
								$new[$i] = $temp;
							}
						}
					}
				}
				if ($rate->event) {
					$new[] = $rate->event;
					$rate['all_event'] = $new;
				}
				$rate['all_event'] = $new;
				return $rate;
			}
			if ($request->event_type == 'simple-pass') {
				$allrates = [];
				$rate = Rate::with(['facility.FacilityPaymentDetails', 'event' => function ($query) use ($startDate, $endDate) {
					$query->whereBetween('parking_start_time', [$startDate, $endDate])
						->orWhereBetween('parking_end_time', [$startDate, $endDate])
						->orWhere(function ($subQuery) use ($startDate, $endDate) {
							$subQuery->where('parking_start_time', '<=', $startDate)
								->where('parking_end_time', '>=', $endDate);
						});
				}])->whereIn('facility_id', $request->facility_id)->where('id', $request->pass_id)->first();
				$new = [];
				if ($rate->event) {
					$new[] = $rate->event;
				}
				$rate['all_event'] = $new;
				return $rate;
			}
		}
	}

	/**
	 * Alka PIMS-11748  : Vijay : 23-12-2024
	 * Date: 05 Dec 2024  
	 */
	public function sendDetailUserEmail(Request $request)
	{
		// dd($request->id);
		//$resp = Reservation::find($reservation_id);
		$resp = Reservation::with(['mapcoQrCode.event'])->find($request->id);
		if ($resp) {
			if (count($resp->mapcoQrCode) > 0) {
				Artisan::queue('mapco:reservation-email', array('id' => $request->id));
				return 'Email successfully sent to the user.';
			} else {
				$client = DB::table('oauth_clients')->where('partner_id', $resp->partner_id)->first();
				if ($client) {
					$clent_secret = $client->secret;
					$resp->sendEmailReservationToPartnerUser($clent_secret, $request->all());
					// dd('alka');
					//Artisan::queue('reservation:email',array('reservationId' => $reservation_id));
					return 'Email successfully sent to the user.';
				}
			}
		} else {
			throw new ApiGenericException('Reservation id not found');
		}
	}

	public function dispatchJobForSendingEmail(Request $request, $partner_id, $facility)
	{
		$requestData[] = $request->all();
		dispatch((new SendBookingExcelEmail($request, $partner_id, $facility))->onQueue(self::BOOKING_HISTORY_QUEUE_NAME));
		$response['status'] = 200;
		$response['message'] = 'Email Sent Succesfully';
		return response()->json($response, 200);
	}
}
