<?php

namespace App\Http\Controllers\ParkEngage;

use App\Classes\CommonFunctions;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Promotion;
use App\Exceptions\ApiGenericException;
use App\Http\Helpers\QueryBuilder;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\PromocodeRequest;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\PromoCode;
use App\Models\PromotionUser;
use App\Models\User;

class DemoPromotionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    const SUPERADMIN = 1;
    const SUBORDINATE = 4;
    const PARTNER = 3;
    const REGIONAL_MANAGER = 12;

    const PCI_PARTNER_ID = 2980;
    const PAYMENTPARKING_PARTNER_ID = 3307;
    const CLASSIC_PARTNER_ID = 359814;
    const WORLDPORT_PARTNER_ID = 2980;
    const COLONIAL_PARTNER_ID = 360869;
    const YANKEE_PARTNER_ID = 363362;

    public function getAll(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('No partner found');
            }
            $owner_id = $secret->partner_id;

            $promotions = Promotion::with(['promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers', 'promocodeRequest'])
                ->where('promotions.owner_id', $owner_id);

                if (isset($request->search)) {
                    $search = $request->search;
                    $promotions = $promotions->join('promocode_requests', 'promotions.id', '=', 'promocode_requests.promotion_id')
                        ->where(function ($query) use ($search) {
                            $query->where('promocode_requests.license_plate', 'LIKE', '%' . $search . '%')
                                ->orWhere('promocode_requests.phone', 'LIKE', '%' . $search . '%');
                        })
                        ->select('promotions.*');
                }

            $promotions = $promotions->where('status', 1)->orderBy('promotions.name', 'ASC');
            if (isset($request->search)) {
                $promotions = $promotions->get();
            }else{
                $promotions = $promotions->paginate(20);
            }
            // \App\Classes\CommonFunctions::printQuery($promotions);

            return $promotions;
        } else {
            throw new ApiGenericException('No partner found.');
        }
    }

    public function usagePromocodeDemo(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('No partner found.');
            }

            if (!isset($request->promotion_id)) {
                throw new ApiGenericException('Promocode is missing.');
            }
            $facility = Facility::where(['owner_id' => $secret->partner_id, 'active' => 1])->orderBy('id', 'DESC')->first();
            if ($facility) {
                $input['facility_id'] = $facility->id;
            } else {
                $input['facility_id'] = "";
            }
            $phone = QueryBuilder::appendCountryCode().$request->phone;
            $promocode = PromoCode::where('promotion_id', $request->promotion_id)->orderBy('id', 'DESC')->first();
            $input = $request->all();
            $input['partner_id'] = $secret->partner_id;
            $input['expiry'] = $request->expiry;
            $input['card_last_four'] = $request->card_last_four;
            $input['phone'] = $phone;
            $input['cvv'] = $request->cvv;
            $input['promocode'] = $promocode->promocode;
            $promo_request = PromocodeRequest::create($input);
            if ($promo_request) {
                PromotionUser::create([
                    "promotion_id" => $request->promotion_id,
                    "email" => $request->email,
                    "license_plate" => $request->license_plate,
                    "phone" => $request->phone,
                ]);
            }
            return $promo_request;
        } else {
            throw new ApiGenericException('No partner found.');
        }
    }
}
