<?php
namespace App\Http\Controllers\ParkEngage;

use Auth;
use Artisan;
use Authorizer;
use DB;
use Exception;
use Carbon\Carbon;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\ParkEngage\RoleUser;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use App\Exceptions\UserNotAuthorized;
use App\Models\ParkEngage\MembershipPlan;
use App\Models\ParkEngage\UserMembership;
use App\Models\ParkEngage\MembershipPayment;
use App\Models\ParkEngage\UserMembershipPlanHistory;
use App\Exceptions\ApiGenericException;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use Response;
use App\Services\LoggerFactory;
use App\Models\OauthClient;
use App\Exceptions\NotFoundException;

use App\Models\ParkEngage\WaitingList;

use DateTime;
use App\Models\Facility;
use App\Models\Partner;
use App\Models\Ticket;
use App\Services\Pdf;
use App\Http\Helpers\QueryBuilder;
use Excel;
use App\Models\Rate;
use App\Models\PermitRate;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\PermitRateDescription;
use App\Models\ParkEngage\UserPaymentGatewayDetail;


class WaitingListController extends Controller
{
	
    protected $request;
    protected $log;

	public function __construct(Request $request, LoggerFactory $logFactory)
    {
    	//parent::__construct();

        $this->request = $request;
		$this->log = $logFactory->setPath('logs/waiting-list-permit')->createLogger('waiting-list-permit');
    }


	public function getWaitListedUsers(Request $request)
	{
		$this->log->info("Get Waiting List Request :".json_encode($request->all()));
		$total_open_invitation = $total_sent_invitation = $total_not_respond = $total_usage = $remaining_usage = $total_usage_1 = $remaining_usage_1 = 0;
		
		if(isset($request->partner_id)){
			$partner_id = $request->partner_id;  
		}else{
			if(Auth::user()->user_type == '1'){
				$partner_id = $request->partner_id;  
			}else{
				$partner_id = Auth::user()->id;  
			}
		}
		
		if(isset($request->facility_id)){
			$facilities = Facility::where('id',$request->facility_id)->first();
		}else{
			$facilities = Facility::where('owner_id',$partner_id)->first();
		}
		if(!$facilities){
			throw new ApiGenericException('Facility Not Found.');
		}
		$permit_rates = PermitRate::where('facility_id',$facilities->id);
		if(isset($request->is_resident) && $request->is_resident=='1'){
			$permit_rates = $permit_rates->where('is_resident',1);
		}else if(isset($request->is_resident) && $request->is_resident=='0'){
			$permit_rates = $permit_rates->where('is_resident',0);
		}else{
			$permit_rates = $permit_rates->where('is_resident',0);
		}
		
		$permit_rates = $permit_rates->first();
		if(isset($permit_rates) && count($permit_rates)>0){
			$total_usage = $permit_rates->total_usage;
			$remaining_usage = $permit_rates->remaining_usage;
		}
		
		$permit_rates_non_resident = PermitRate::where('facility_id',$facilities->id)->where('is_resident','0')->first();
		if(isset($permit_rates_non_resident) && count($permit_rates_non_resident)>0){
			$total_usage_1 = $permit_rates_non_resident->total_usage;
			$remaining_usage_1 = $permit_rates_non_resident->remaining_usage;
		}
		
		
		$facility_id = $facilities->id;
		$users = WaitingList::where('active',1);

		if($partner_id){
			$users =$users->where('partner_id', $partner_id);
		}
		
		if($facility_id){
			$users =$users->Where('facility_id', $facility_id);
		}
		if(isset($request->is_resident) && $request->is_resident=='1'){
			$users = $users->where('is_resident',1);
		}else if(isset($request->is_resident) && $request->is_resident=='0'){
			$users = $users->where('is_resident',0);
		}else{
			$users = $users->where('is_resident',0);
		}
		
		if(isset($request->search)){
			  $users = $users->where('name', 'like', "%{$request->search}%")
							  ->orWhere('phone', 'like', "%{$request->search}%")
							  ->orWhere('email', 'like', "%{$request->search}%");
							  
		}
		
		if($request->sort != ''){        
		  $users = $users->orderBy($request->sort,$request->sortBy);        
		}else{
		  $users = $users->orderBy('id','DESC');        
		}
		
		$users_data = $users->paginate(10)->toArray(); 
		
		$open_invitation = WaitingList::where('invite_status',0)->where('active',1);
		if($partner_id){
			$open_invitation =$open_invitation->where('partner_id', $partner_id);
		}

		$sent_invitation = WaitingList::where('invite_status','1')->where('active',1);

		if($partner_id){
			$sent_invitation =$sent_invitation->where('partner_id', $partner_id);
		}
		$not_respond = WaitingList::where('invite_status','2')->where('active',1);

		if($partner_id){
			$not_respond =$not_respond->where('partner_id', $partner_id);
		}
		
		
		if($request->facility_id){
			$open_invitation->Where('facility_id', $facility_id);
			$sent_invitation->Where('facility_id', $facility_id);
			$not_respond->Where('facility_id', $facility_id);
		}
		$total_open_invitation = $open_invitation->count();
		$total_sent_invitation = $sent_invitation->count();
		$total_not_respond = $not_respond->count();
		
		//$result['data'] = $users_data;
		$users_data['total_open_invitation'] = $total_open_invitation;
		$users_data['total_sent_invitation'] = $total_sent_invitation;
		$users_data['total_not_responded'] = $total_not_respond;
		
		$users_data['total_usage'] = $total_usage;
		$users_data['remaining_usage'] = $remaining_usage;
		
		$users_data['total_usage_non_resident'] = $total_usage_1;
		$users_data['remaining_usage_non_resident'] = $remaining_usage_1;
		
		
		$users_data['facility_id'] = $facility_id;
		
		$users_data['is_resident'] = isset($request->is_resident) ?$request->is_resident :'1';	
		
		if(!$request->facility_id){
			$permit_rate_desc_id_resident = PermitRateDescription::where('partner_id',$partner_id)->where('is_resident','1')->pluck('id');
			if($permit_rate_desc_id_resident){
				$permit_count_resident = PermitRate::whereIn('permit_rate_description_id',$permit_rate_desc_id_resident)->select(DB::raw("SUM(total_usage) as total_usage"),DB::raw("SUM(remaining_usage) as remaining_usage"))->where('is_resident','1')->first();
				$users_data['total_usage'] = $permit_count_resident->total_usage;
				$users_data['remaining_usage'] = $permit_count_resident->remaining_usage;
			}
			
			$permit_rate_desc_id_non_resident = PermitRateDescription::where('partner_id',$partner_id)->where('is_resident','0')->pluck('id');
						
			if($permit_rate_desc_id_non_resident){
				$permit_count_nonresident = PermitRate::whereIn('permit_rate_description_id',$permit_rate_desc_id_non_resident)->select(DB::raw("SUM(total_usage) as total_usage"),DB::raw("SUM(remaining_usage) as remaining_usage"))->where('is_resident','0')->first();
				$users_data['total_usage_non_resident'] = $permit_count_nonresident->total_usage;
				$users_data['remaining_usage_non_resident'] = $permit_count_nonresident->remaining_usage;
		
			}	
		}

		return $users_data;
	}


	public function waitListedusers(Request $request)
	{
		
		$this->log->info("Get Waiting List add/update Request :".json_encode($request->all()));
		$users = WaitingList::with('facility')->where('id', $this->request->id)->first();
     	$facilities = Facility::where('id',$request->facility_id)->where('active',1)->first();
		if(!$facilities){
			throw new ApiGenericException('Facility Not Found.');
		}
		// Get country Code
		$this->countryCode = QueryBuilder::appendCountryCode();
		
		if($users){

			
			$users->active = 1;			
			$users->invitation_at = date('Y-m-d H:i:s');
			$users->invite_status = $this->request->invite_status;
			$users->save();

			 // get Partner Slug
			 $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facilities) {
				$join->on('user_facilities.user_id', '=', 'users.id');
				$join->where('user_facilities.facility_id', "=", $facilities->id);
			  })->where('created_by', $facilities->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
			  
			  if($getRM){
				$partnerSlug = $getRM->slug;
			  }else{
				$checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $facilities->owner_id)->first();
				$partnerSlug  =  $checkPaymentUrl->touchless_payment_url;
			  }
			
			// SMS & email Send Code
			if($facilities->id == config('parkengage.WAILUKU_FACILITY')){	
				$msg = "Thank you for showing interest in ".$facilities->full_name.". Permit is now available. You can complete your purchase by filling your details here ". env('WEB_URL_WAILUKU');
			}else{
				$msg = "Thank you for showing interest in ".$facilities->full_name.". Permit is now available. You can complete your purchase by filling your details here ".env('WEB_URL_CUSTOMERPORTAL_PCI').$partnerSlug;
			}
			$this->customeReplySms($msg, $this->countryCode.$this->request->phone); 
			Artisan::queue('diamondwaitinglistinvitation:email',array('waiting_request_id' => $this->request->id));
			
				
			return $users;
		}else{	
			$is_resident_user = 0;
			if($request->license_number != ''){
				
				$mystring = $request->license_number;
				$mystring = trim($mystring);//removes any spaces from beginning of the string
				$number = substr($mystring,1);
				
				$is_resident_user = '';
				
				/*
				if(ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring)==9){
					$is_resident_user = 'Pass';
				}else{
					$is_resident_user = 'Fail';
				}
				*/
				if(strlen($mystring)<7){
					throw new ApiGenericException('Invalid Driving License');
				}else if(strlen($mystring)>13){
					throw new ApiGenericException('Invalid Driving License');
				}else if(ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring)==9){
					$is_resident_user = '1';
				}else{
					$is_resident_user = '0';
				}
            }

			$userDetails ='';
			if($request->header('X-ClientSecret') != ''){
				$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
				//return $secret;
				if(!$secret){
				   throw new NotFoundException('No partner found.');
				}
			}
			$existPhone = User::where('email',$request->email)->where('created_by', $secret->partner_id)->first();
			
			if($existPhone){
				if($request->user_id !=''){
					$userDetails = $existPhone;
				}else{
					if($request->confirm_password !=''){
						$request->request->add(['username' => $request->email]);
						$request->request->add(['password' => $request->confirm_password]);
						$request->request->add(['client_id' => 'jordan-rodewald3986']);
						$request->request->add(['client_secret' => 'caXlcP5A2q506C21h6Sbtq7Qp']);
						$request->request->add(['grant_type' => 'password']);                    
						//$userDetails = $this->loginUser($request);    
					}
				}
			}else{   
				/*     
				$this->user = User::create([
					'name' => $this->request->first_name." ".$this->request->last_name,
					'email' => $this->request->email,
					'phone' => $this->countryCode.$this->request->phone,
					'password' => Hash::make($request->confirm_password),
					'anon' => true,
					'user_type' => '5',
					'license_number' => $request->license_number,
					'address' =>$request->address1,
					'address2' =>$request->address2,
					'city' =>$request->city,
					'state' =>$request->state,
					'pincode' =>$request->zipcode,
					'created_by' => $secret->partner_id,
				]);
				*/
				$request->request->add(['username' => $request->email]);
				$request->request->add(['password' => $request->confirm_password]);
				$request->request->add(['client_id' => 'jordan-rodewald3986']);
			    $request->request->add(['client_secret' => 'caXlcP5A2q506C21h6Sbtq7Qp']);
				$request->request->add(['grant_type' => 'password']);
				//$userDetails = $this->loginUser($request);
			}
			
			$userExist = WaitingList::where('facility_id', $this->request->facility_id)
					 ->Where('email', 'like', "%{$request->email}%")->first();
					 
			//return $userExist;
			if(count($userExist)>0){
				$userExist->active = 1;			
				$userExist->invitation_at = '';
				$userExist->invite_status = '0';
				$userExist->is_resident = $is_resident_user;
				$userExist->save();
				
				// SMS & email Send Code
				//return $request->phone;
				$wait_msg = "Thank you for showing interest in ".$facilities->full_name.". Currently, all the permits are sold out. You will be added to the waiting list and notified once it becomes available.";
				$this->customeReplySms($wait_msg, $this->countryCode.$request->phone);	
				Artisan::queue('diamondwaitinglist:email',array('waiting_request_id' => $userExist->id));
			
				$userdata = [
					'userDetails'=> $userDetails,
					'message' => 'You are already in Waiting List for this facility.'
				];
				return $userdata;
			}else{
				$users = [];
				$users['facility_id'] = $this->request->facility_id;
				$users['partner_id'] = $this->request->partner_id;
				$users['name'] = $this->request->name;
				$users['email'] = $this->request->email;
				$users['phone'] = $this->request->phone;
				$users['active'] = 1;
				$users['is_resident'] = $is_resident_user;
				$userExist = WaitingList::create($users);
				
				// SMS & email Send Code
				
				$wait_msg = "Thank you for showing interest in ".$facilities->full_name.". Currently, all the permits are sold out. You will be added to the waiting list and notified once it becomes available.";
				$this->customeReplySms($wait_msg, $this->countryCode.$request->phone);	
				Artisan::queue('diamondwaitinglist:email',array('waiting_request_id' => $userExist->id));
				
				$userdata = [
					'userDetails'=> $userDetails,
					'message' => "Thank you for showing interest in ".$facilities->full_name.". Currently, all the permits are sold out, You have been added to the Waiting List and will be notified once it is available."
				];
				return $userdata;
			}				
			
		}
	}
	
	public function removeUser(Request $request) {
	  $this->log->info("Get Waiting List Delete Request :".json_encode($request->all()));	
	  $result = WaitingList::where('id', $request->id)->delete();
      if($result){
        return "Data successfully deleted.";
      }else{
        return "Something wrong.";
      }  

	}

	public function getBookingListDownload(Request $request){
		$this->log->info("Get Waiting List Download PDF Request :".json_encode($request->all()));	
		if(isset($request->partner_id)){
		  $partner_id = $request->partner_id;  
		}else{
		  if(Auth::user()->user_type == '1'){
			  $result  = [];
			  $result["total"]=0;
			  $result["per_page"]=20;
			  $result["current_page"]=1;
			  $result["last_page"]=1;
			  $result["next_page_url"]=Null;
			  $result["prev_page_url"]=Null;
			  $result["from"]=Null;
			  $result["to"]=Null;
			  $result["data"]=[];
			  return $result;
		  }else{
			  $partner_id = Auth::user()->id;  
		  }
		}

		$month = date("m");
		$from_date  = date('Y-'.$month.'-01');
		$to_date  = date('Y-'.$month.'-t');
		$mid_date  = date('Y-'.$month.'-15');
		$midDateString = strtotime($mid_date);   
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		if(isset($request->from_date) && $request->from_date!='')
		{
			$from_date = $request->from_date;
			$to_date = $request->to_date;
		}

    	$users = WaitingList::whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('active',1)->where('partner_id', $partner_id);
		
		if(isset($request->search)){
			  $users = $users->where('name', 'like', "%{$request->search}%")
							  ->where('phone', 'like', "%{$request->search}%")
							  ->Where('email', 'like', "%{$request->search}%");
		}
		
   
   
		if($request->sort != ''){        
		  $users = $users->orderBy($request->sort,$request->sortBy);        
		}else{
		  $users = $users->orderBy('id','DESC');        
		}
		$users = $users->get();
    
    	if(count($users)>0){
			//$settings = BrandSetting::where('user_id', $partner_id)->first();
			$settings = '';
			$html = view("download.wait-list-booking-details", ["users" =>$users, 'brand_setting' =>$settings])->render();
			$image = app()->make(Pdf::class);
			$pdf = $image->getOutputFromHtmlString($html);
			return $pdf;
		}else{
			throw new ApiGenericException('Sorry! No Data Found.');
		}
   
     
  }
		

	public function getBookingListDownloadExcel(Request $request){
        $this->log->info("Get Waiting List Download Excel Request :".json_encode($request->all()));	
		if(isset($request->partner_id)){
		  $partner_id = $request->partner_id;  
		}else{
		  if(Auth::user()->user_type == '1'){
			  $result  = [];
			  $result["total"]=0;
			  $result["per_page"]=20;
			  $result["current_page"]=1;
			  $result["last_page"]=1;
			  $result["next_page_url"]=Null;
			  $result["prev_page_url"]=Null;
			  $result["from"]=Null;
			  $result["to"]=Null;
			  $result["data"]=[];
			  return $result;
		  }else{
			  $partner_id = Auth::user()->id;  
		  }
		}

		$month = date("m");
		$from_date  = date('Y-'.$month.'-01');
		$to_date  = date('Y-'.$month.'-t');
		$mid_date  = date('Y-'.$month.'-15');
		$midDateString = strtotime($mid_date);   
		$lastdate = strtotime(date("Y-m-t", $midDateString));
		$to_date = date("Y-m-d", $lastdate);

		if(isset($request->from_date) && $request->from_date!='')
		{
			$from_date = $request->from_date;
			$to_date = $request->to_date;
		}

		$users = WaitingList::whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->where('active',1)->where('partner_id', $partner_id);
		
		if(isset($request->search)){
			  $users = $users->where('name', 'like', "%{$request->search}%")
							  ->where('phone', 'like', "%{$request->search}%")
							  ->Where('email', 'like', "%{$request->search}%");
		}
		
   
   
		if($request->sort != ''){        
		  $users = $users->orderBy($request->sort,$request->sortBy);        
		}else{
		  $users = $users->orderBy('id','DESC');        
		}
		$users = $users->get();
    
		$excelSheetName = ucwords(str_replace(' ', '', 'Wait Listed User Details'));
	    $finalCodes1 = [];
	    $increment1 = 1;
	    if(count($users) > 0){  
		foreach ($users as $val) {
            $finalCodes1[] = [
              'No.' => $increment1,
              'Name' => isset($val->name) ? $val->name: '',
              'Email' => isset($val->email) ? $val->email: '',
              'Phone' => isset($val->phone) ? $val->phone: '',
              'Status' => $val->active != '' ? "Active" : '-',
			  'Booking Day' => date("m/d/Y h:i A", strtotime($val->created_at)),
            ];            
            $increment1++;
        }
      }
	  
	  	  
      Excel::create(
		    $excelSheetName, function ($excel) use ($finalCodes1, $excelSheetName) {

          // Set the spreadsheet title, creator, and description
          $excel->setTitle($excelSheetName);
          $excel->setCreator('WaitingUserList')->setCompany('ParkEngage');
          $excel->setDescription('List Of Booking');
		  // Build the spreadsheet, passing in the payments array
          if(isset($finalCodes1) && !empty($finalCodes1)){
            $excel->sheet(
              'Wait List User Details', function ($sheet) use ($finalCodes1) {
                $sheet->fromArray($finalCodes1, null, 'A1', false, true);
              }
            );
          }else{
			  throw new ApiGenericException('Sorry! No Data Found.');
		  }
        }
	    )->store('xls')->download('xls');

    }
	
	public function customeReplySms($msg, $phone, $imageURL = ''){

        //$accountSid = env('TWILIO_ACCOUNT_SID');
        //$authToken  = env('TWILIO_AUTH_TOKEN');

        $accountSid = "**********************************";
        $authToken  = "989ab85afc8ef729dda64cf8b334d77a";

        $client = new Client($accountSid, $authToken);
        try
            {

                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                // the number you'd like to send the message to
                    $phone,
               array(
                     // A Twilio phone number you purchased at twilio.com/console
                     'from' => env('TWILIO_PHONE'),
                     // the body of the text message you'd like to send
                     //'body' => "Fine"
                     'body' => "$msg",
                     //'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)
                     
                 )
             );
            $this->log->info("Message : {$msg} sent to $phone");
		    return "success";
        }catch (RestException $e)
        {
           // echo "Error: " . $e->getMessage();
		   
            $this->log->error($e->getMessage());
            return "success";
        }
    }
	
	public function waitListeduser(Request $request)
	{
		$users = WaitingList::with('facility')->where('id', $this->request->id)->first();
     	$facilities = Facility::where('id',$request->facility_id)->first();
		
		 $this->countryCode = QueryBuilder::appendCountryCode();
		
		if($users){

			
			$users->active = 1;			
			$users->invitation_at = date('Y-m-d H:i:s');
			$users->invite_status = $this->request->invite_status;
			$users->save();
			
			// SMS & email Send Code
			$msg = "Thank you for showing interest in ".$facilities->full_name.". Permit is now available. You can complete your purchase by filling your details here ".env('WEB_URL_PERMIT');
			$this->customeReplySms($msg, $this->countryCode.$this->request->phone); 
			Artisan::queue('diamondwaitinglistinvitation:email',array('waiting_request_id' => $this->request->id));
			
				
			return $users;
		}else{	

			$userDetails ='';
			if($request->header('X-ClientSecret') != ''){
				$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
				if(!$secret){
				   throw new NotFoundException('No partner found.');
				}
			}
			$existPhone = User::where('email',$request->email)->where('created_by', $secret->partner_id)->first();
			if($existPhone){
				if($request->user_id !=''){
					$userDetails = $existPhone;
				}else{
					if($request->confirm_password !=''){
						$request->request->add(['username' => $request->email]);
						$request->request->add(['password' => $request->confirm_password]);
						$request->request->add(['client_id' => 'jordan-rodewald3986']);
						$request->request->add(['client_secret' => 'caXlcP5A2q506C21h6Sbtq7Qp']);
						$request->request->add(['grant_type' => 'password']);                    
						$userDetails = $this->loginUser($request);    
					}
				}
			}else{            
				$this->user = User::create([
					'name' => $this->request->first_name." ".$this->request->last_name,
					'email' => $this->request->email,
					'phone' => $this->countryCode.$this->request->phone,
					'password' => Hash::make($request->confirm_password),
					'anon' => true,
					'user_type' => '5',
					'license_number' => $request->license_number,
					'address' =>$request->address1,
					'address2' =>$request->address2,
					'city' =>$request->city,
					'state' =>$request->state,
					'pincode' =>$request->zipcode,
					'created_by' => $secret->partner_id,
				]);
        
				$request->request->add(['username' => $request->email]);
				$request->request->add(['password' => $request->confirm_password]);
				$request->request->add(['client_id' => 'jordan-rodewald3986']);
			    $request->request->add(['client_secret' => 'caXlcP5A2q506C21h6Sbtq7Qp']);
				$request->request->add(['grant_type' => 'password']);
				$userDetails = $this->loginUser($request);
			}
			
			$userExist = WaitingList::where('facility_id', $this->request->facility_id)
					 ->Where('phone', 'like', "%{$request->phone}%")
					 ->orWhere('email', 'like', "%{$request->email}%")->first();
					 
			
			if(count($userExist)>0){
				$userExist->active = 1;			
				$userExist->invitation_at = '';
				$userExist->invite_status = '0';
				$userExist->save();
				
				// SMS & email Send Code
				//return $request->phone;
				$wait_msg = "Thank you for showing interest in ".$facilities->full_name.". Currently, all the permits are sold out. You will be added to the waiting list and notified once it becomes available.";
				$this->customeReplySms($wait_msg, $this->countryCode.$request->phone);	
				Artisan::queue('diamondwaitinglist:email',array('waiting_request_id' => $userExist->id));
			
				$userdata = [
					'userDetails'=> $userDetails,
					'message' => 'Data Already Exists in Waiting List'
				];
				return $userdata;
			}else{
				$users = [];
				$users['facility_id'] = $this->request->facility_id;
				$users['partner_id'] = $this->request->partner_id;
				$users['name'] = $this->request->name;
				$users['email'] = $this->request->email;
				$users['phone'] = $this->request->phone;
				$users['active'] = 1;
				WaitingList::create($users);
				
				// SMS & email Send Code
				
				$wait_msg = "Thank you for showing interest in ".$facilities->full_name.". Currently, all the permits are sold out. You will be added to the waiting list and notified once it becomes available.";
				$this->customeReplySms($wait_msg, $this->countryCode.$request->phone);	
				Artisan::queue('diamondwaitinglist:email',array('waiting_request_id' => $userExist->id));
				
				$userdata = [
					'userDetails'=> $userDetails,
					'message' => 'Data Added Successfully'
				];
				return $userdata;
			}				
			
		}
		
	}
	
	public function loginUser(Request $request)
    {
		$this->log->info("Waiting List User Login Request :".json_encode($request->all()));	
        $_SESSION['partner_id'] = '';
     
        if (isset($request->client_id) && $request->client_id=='parkengage-app') {
            // Set our refresh token to expire in one year if the remember me box is set
            Config::set('oauth2.grant_types.refresh_token.refresh_token_ttl', 60 * 60 * 24 * 365);
        }

         // Config::set('oauth2.grant_types.password.access_token_ttl', 60 * 60 * 24 * 365);

         $client = OauthClient::where('secret', $request->client_secret)->first();
         if($client){
            
            $partner = User::where('id', $client->partner_id)->first();
           // return $partner->email;
            if($partner->user_type == '5'){
                $_SESSION['partner_id'] = $client->partner_id;
            }else{
                $_SESSION['partner_id'] = $client->partner_id;
            }
         }
         

         $session = Authorizer::issueAccessToken();        
         $user = Auth::user();
        return array_merge($session, ['user' => $user ? $user->load('photo','membershipPlans')->toArray() : null]);

    }
	
	public function waitListedusersAdmin(Request $request)
	{
		
		$this->log->info("Get Waiting List add/update Request :".json_encode($request->all()));
		$users = WaitingList::with('facility')->where('id', $this->request->id)->first();
     	$facilities = Facility::where('id',$request->facility_id)->first();
		
		 $this->countryCode = QueryBuilder::appendCountryCode();
		
		if($users){

			
			$users->active = 1;			
			$users->invitation_at = date('Y-m-d H:i:s');
			$users->invite_status = $this->request->invite_status;
			$users->save();
			
			// SMS & email Send Code
			if($facilities->owner_id=='358524'){
				$msg = "Thank you for showing interest in ".$facilities->full_name.". Permit is now available. You can complete your purchase by filling your details here ".env('WEB_URL_ARE');
			}else{
				$msg = "Thank you for showing interest in ".$facilities->full_name.". Permit is now available. You can complete your purchase by filling your details here ".env('WEB_URL_PERMIT');
			}
			$this->customeReplySms($msg, $this->countryCode.$this->request->phone); 
			Artisan::queue('diamondwaitinglistinvitation:email',array('waiting_request_id' => $this->request->id));
			
				
			return $users;
		}else{	
			$is_resident_user = 0;
			if($request->license_number != ''){
				
				$mystring = $request->license_number;
				$mystring = trim($mystring);//removes any spaces from beginning of the string
				$number = substr($mystring,1);
				
				$is_resident_user = '';
				
				/*
				if(ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring)==9){
					$is_resident_user = 'Pass';
				}else{
					$is_resident_user = 'Fail';
				}
				*/
				if(strlen($mystring)<7){
					throw new ApiGenericException('Invalid Driving License');
				}else if(strlen($mystring)>13){
					throw new ApiGenericException('Invalid Driving License');
				}else if(ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring)==9){
					$is_resident_user = '1';
				}else{
					$is_resident_user = '0';
				}
            }

			$userDetails ='';
			$partner_id = Auth::user()->id; 
			$request->request->add(['partner_id' => $partner_id]);
			$existPhone = User::where('email',$request->email)->where('created_by', $partner_id)->first();
			
			if($existPhone){
				if($request->user_id !=''){
					$userDetails = $existPhone;
				}else{
					if($request->confirm_password !=''){
						$request->request->add(['username' => $request->email]);
						$request->request->add(['password' => $request->confirm_password]);
						$request->request->add(['client_id' => 'jordan-rodewald3986']);
						$request->request->add(['client_secret' => 'caXlcP5A2q506C21h6Sbtq7Qp']);
						$request->request->add(['grant_type' => 'password']);                    
						//$userDetails = $this->loginUser($request);    
					}
				}
			}else{        
				$this->user = User::create([
					'name' => $this->request->first_name." ".$this->request->last_name,
					'email' => $this->request->email,
					'phone' => $this->countryCode.$this->request->phone,
					'password' => Hash::make($request->confirm_password),
					'anon' => true,
					'user_type' => '5',
					'license_number' => $request->license_number,
					'address' =>$request->address1,
					'address2' =>$request->address2,
					'city' =>$request->city,
					'state' =>$request->state,
					'pincode' =>$request->zipcode,
					'created_by' => $partner_id,
				]);
        
				$request->request->add(['username' => $request->email]);
				$request->request->add(['password' => $request->confirm_password]);
				$request->request->add(['client_id' => 'vikrant-tyagi356560']);
			    $request->request->add(['client_secret' => '9f3Blizhmt8qhkw62Y16qTJXa']);
				$request->request->add(['grant_type' => 'password']);
				//$userDetails = $this->loginUser($request);
			}
			
			$userExist = WaitingList::where('facility_id', $this->request->facility_id)
					 ->Where('email', 'like', "%{$request->email}%")->first();
					 
			//return $userExist;
			if(count($userExist)>0){
				$userExist->active = 1;			
				$userExist->invitation_at = '';
				$userExist->invite_status = '0';
				$userExist->is_resident = $is_resident_user;
				$userExist->save();
				
				// SMS & email Send Code
				//return $request->phone;
				$wait_msg = "Thank you for showing interest in ".$facilities->full_name.". Currently, all the permits are sold out. You will be added to the waiting list and notified once it becomes available.";
				$this->customeReplySms($wait_msg, $this->countryCode.$request->phone);	
				Artisan::queue('diamondwaitinglist:email',array('waiting_request_id' => $userExist->id));
			
				$userdata = [
					'userDetails'=> $userDetails,
					'message' => 'You are already in Waiting List for this facility.'
				];
				return $userdata;
			}else{
				$users = [];
				$users['facility_id'] = $this->request->facility_id;
				$users['partner_id'] = $partner_id;
				$users['name'] = $this->request->first_name." ".$this->request->last_name;
				$users['email'] = $this->request->email;
				$users['phone'] = $this->request->phone;
				$users['active'] = 1;
				$users['is_resident'] = $is_resident_user;
				$userExist = WaitingList::create($users);
				
				// SMS & email Send Code
				
				$wait_msg = "Thank you for showing interest in ".$facilities->full_name.". Currently, all the permits are sold out. You will be added to the waiting list and notified once it becomes available.";
				$this->customeReplySms($wait_msg, $this->countryCode.$request->phone);	
				Artisan::queue('diamondwaitinglist:email',array('waiting_request_id' => $userExist->id));
				
				$userdata = [
					'userDetails'=> $userDetails,
					'message' => "Thank you for showing interest in ".$facilities->full_name.". Currently, all the permits are sold out, You have been added to the Waiting List and will be notified once it is available."
				];
				return $userdata;
			}				
			
		}
	}

	
}
