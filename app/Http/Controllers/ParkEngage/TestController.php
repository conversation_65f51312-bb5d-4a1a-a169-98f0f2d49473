<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\User;
use App\Models\ParkEngage\UserSession;
use App\Models\Reservation;
use App\Models\Ticket;
use App\Models\PermitTicket;
use App\Models\PermitRequest;
use App\Models\UserPass;
use App\Models\PaymentProfile;
use App\Models\UserCim;
use App\Exceptions\ApiGenericException;
use App\Models\ParkEngage\TicketCitation;
use App\Models\PermitVehicle;
use App\Models\ParkEngage\PlanetPaymentProfile;

use App\Classes\ParkengageGateApi;
use App\Jobs\SendPaymentReminder;
use App\Services\LoggerFactory;
use Excel;
use App\Http\Helpers\QueryBuilder;
use App\Models\Event;
use App\Models\EventFacility;
use App\Models\GeoLocation;

class TestController extends Controller
{
    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/ParkengageGateApi')->createLogger('parkengagegateapi');
    }


    public function updateUserSession(Request $request)
    {
        $partner_id = 356560;
        $users = User::where("created_by", $partner_id)->whereNotNull("session_id")->get();

        $count = 0;
        foreach ($users as $key => $user) {
            $userSession = new UserSession();
            $userSession->user_id = $user->id;
            $userSession->partner_id = $user->created_by;
            $userSession->session_id = $user->session_id;
            $userSession->save();
            $count++;
        }
        return "total $count added.";
    }


    public function deleteUserAllDetails(Request $request)
    {

        $users = '';
        $partner_id = 356560;
        // $users = User::where("email", $email)->where("created_by", $partner_id)->get();
        if ($request->get('phone') && $request->get('email') == '') {

            $phone = $request->get('phone');
            $users = User::where('phone', 'like', "%{$phone}%")->where("created_by", $partner_id)->get();
        }
        if ($request->get('email') && $request->get('phone') == '') {
            $email = $request->get('email');
            $users = User::where("email", $email)->where("created_by", $partner_id)->get();
        }

        if ($request->get('phone') && $request->get('email')) {
            $users = User::where("email", $request->get('email'))->where("phone", $request->get('phone'))->where("created_by", $partner_id)->get();
        }
        if ($request->get('license_plate')) {
            $license_plate = $request->get('license_plate');
        }


        if (isset($license_plate) && $license_plate != '') {
            $users = PermitVehicle::where("license_plate_number", $license_plate)->where("partner_id", $partner_id)->get();


            $count = 0;
            if (count($users) > 0) {
                foreach ($users as $key => $user) {
                    Reservation::where("user_id", $user->user_id)->delete();
                    Ticket::where("user_id", $user->user_id)->delete();
                    UserPass::where("user_id", $user->user_id)->delete();
                    PermitRequest::where("user_id", $user->user_id)->delete();
                    PermitVehicle::where("user_id", $user->user_id)->delete();
                    PermitTicket::where("user_id", $user->user_id)->delete();
                    $usercims = UserCim::where("user_id", $user->id)->orderBy("id", "DESC")->first();
                    if ($usercims) {
                        $profiles = PaymentProfile::where('cim_id', $usercims->id)->orderBy("id", "DESC")->delete();
                        $usercims->delete();
                    }

                    $userPayment = PlanetPaymentProfile::where('user_id', $user->id)->first();
                    if ($userPayment) {
                        $userPayment->delete();
                    }

                    $user->delete();
                }
                return "User all details deleted.";
            } else {
                return "No user found.";
            }
        }

        $count = 0;
        if (count($users) > 0) {
            foreach ($users as $key => $user) {
                Reservation::where("user_id", $user->id)->delete();
                Ticket::where("user_id", $user->id)->delete();
                UserPass::where("user_id", $user->id)->delete();
                PermitRequest::where("user_id", $user->id)->delete();
                PermitVehicle::where("user_id", $user->id)->delete();
                PermitTicket::where("user_id", $user->id)->delete();
                $usercims = UserCim::where("user_id", $user->id)->orderBy("id", "DESC")->first();
                if ($usercims) {
                    $profiles = PaymentProfile::where('cim_id', $usercims->id)->orderBy("id", "DESC")->delete();
                    $usercims->delete();
                }
                $userPayment = PlanetPaymentProfile::where('user_id', $user->id)->first();
                if ($userPayment) {
                    $userPayment->delete();
                }
                $user->delete();
            }
            return "User all details deleted.";
        } else {
            return "No user found.";
        }
    }

    public function deleteUserSession()
    {

        $partner_id = 356560;
        $users = User::whereNotNull("session_id")->where("user_type", '5')->where("created_by", $partner_id)->get();

        $count = 0;
        if (count($users) > 0) {
            foreach ($users as $key => $user) {
                $user->session_id = null;
                $user->save();
            }
            return "All session deleted.";
        } else {
            return "No user found.";
        }
    }


    public function updateTicketStatus()
    {
        $ticket1 = Ticket::where("license_plate", "7BQW240")->orderBy("id", "DESC")->first();
        if ($ticket1) {
            $now = date("Y-m-d H:i:s");
            $checkoutTime = date('Y-m-d H:i:s', strtotime('+120 minutes', strtotime($now)));

            $checkinTime = date('Y-m-d H:i:s', strtotime('-10 minutes', strtotime($now)));

            $updatedCheckoutTime = date('Y-m-d H:i:s', strtotime('-10 minutes', strtotime($checkoutTime)));

            $ticket1->checkin_time = $checkinTime;
            $ticket1->check_in_datetime = $checkinTime;
            $ticket1->checkout_datetime = $updatedCheckoutTime;
            $ticket1->estimated_checkout = $updatedCheckoutTime;
            $ticket1->checkout_time = $updatedCheckoutTime;
            $ticket1->save();
        }

        $ticket2 = Ticket::where("license_plate", "8RNU019")->orderBy("id", "DESC")->first();
        if ($ticket2) {
            $now = date("Y-m-d H:i:s");
            $checkoutTime = date('Y-m-d H:i:s', strtotime('+130 minutes', strtotime($now)));

            $checkinTime = date('Y-m-d H:i:s', strtotime('-130 minutes', strtotime($now)));

            $updatedCheckoutTime = date('Y-m-d H:i:s', strtotime('-130 minutes', strtotime($checkoutTime)));

            $lastestCheckoutTime = date('Y-m-d H:i:s', strtotime('-10 minutes', strtotime($updatedCheckoutTime)));

            $ticket2->checkin_time = $checkinTime;
            $ticket2->check_in_datetime = $checkinTime;
            $ticket2->checkout_datetime = $lastestCheckoutTime;
            $ticket2->estimated_checkout = $lastestCheckoutTime;
            $ticket2->checkout_time = $lastestCheckoutTime;
            $ticket2->save();
        }

        $ticket3 = TicketCitation::where("license_plate", "5KDG673")->orderBy("id", "DESC")->first();
        if ($ticket3) {
            $now = date("Y-m-d H:i:s");
            $checkoutTime = date('Y-m-d H:i:s', strtotime('+130 minutes', strtotime($now)));

            $checkinTime = date('Y-m-d H:i:s', strtotime('-130 minutes', strtotime($now)));

            $updatedCheckoutTime = date('Y-m-d H:i:s', strtotime('-130 minutes', strtotime($checkoutTime)));

            $ticket3->checkin_time = $checkinTime;
            //$ticket3->check_in_datetime = $checkinTime;
            //$ticket3->checkout_datetime = $updatedCheckoutTime;
            $ticket3->estimated_checkout = date("Y-m-d") . " " . "23:59:59";
            $ticket3->checkout_time = date("Y-m-d") . " " . "23:59:59";
            $ticket3->scan_date = $checkinTime;
            $ticket3->save();
        }

        $ticket4 = Ticket::where("license_plate", "7HTS233")->orderBy("id", "DESC")->first();
        if ($ticket4) {
            $now = date("Y-m-d H:i:s");
            $checkoutTime = date('Y-m-d H:i:s', strtotime('+150 minutes', strtotime($now)));

            $checkinTime = date('Y-m-d H:i:s', strtotime('-150 minutes', strtotime($now)));

            $updatedCheckoutTime = date('Y-m-d H:i:s', strtotime('-120 minutes', strtotime($checkoutTime)));

            $ticket4->checkin_time = $checkinTime;
            $ticket4->check_in_datetime = $checkinTime;
            $ticket4->checkout_datetime = $updatedCheckoutTime;
            $ticket4->estimated_checkout = $updatedCheckoutTime;
            $ticket4->checkout_time = $updatedCheckoutTime;
            $ticket4->save();
        }

        $ticket5 = Ticket::where("license_plate", "8FFR269")->orderBy("id", "DESC")->first();
        if ($ticket5) {
            $now = date("Y-m-d H:i:s");
            $checkoutTime = date('Y-m-d H:i:s', strtotime('+130 minutes', strtotime($now)));

            $checkinTime = date('Y-m-d H:i:s', strtotime('-130 minutes', strtotime($now)));

            $updatedCheckoutTime = date('Y-m-d H:i:s', strtotime('-130 minutes', strtotime($checkoutTime)));

            $lastestCheckoutTime = date('Y-m-d H:i:s', strtotime('-10 minutes', strtotime($updatedCheckoutTime)));

            $ticket5->checkin_time = $checkinTime;
            $ticket5->check_in_datetime = $checkinTime;
            $ticket5->checkout_datetime = $lastestCheckoutTime;
            $ticket5->estimated_checkout = $lastestCheckoutTime;
            $ticket5->checkout_time = $lastestCheckoutTime;
            $ticket5->save();
        }
        return "Data updated.";
    }


    public function webSocketTest()
    {
        // Server configuration
        $serverIp = '************';
        $serverPort = 10501; // Replace with the correct port

        // Data packet format
        $packet = "\x16\x16\x02"; // SYN, SYN, STX
        $sa = "\x01"; // SA
        $cm = "\x02"; // CM
        $cd = "\x03"; // CD
        $xx = "\x00\x00\x00\x00"; // X, X, X, X (you may need to replace with actual values)
        $cs = $this->calculateChecksum($packet, $sa, $cm, $cd, $xx); // Calculate the checksum
        $etx = "\x03"; // ETX

        // Create the complete data packet
        $dataPacket = $packet . $sa . $cm . $cd . $xx . $cs . $etx;
        // Create a socket and connect to the server
        $socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        if ($socket === false) {
            echo "Failed to create socket: " . socket_strerror(socket_last_error()) . "\n";
        } else {
            $result = socket_connect($socket, $serverIp, $serverPort);
            if ($result === false) {
                echo "Failed to connect to the server: " . socket_strerror(socket_last_error()) . "\n";
            } else {
                // Send the data packet to the server
                socket_write($socket, $dataPacket, strlen($dataPacket));

                // Read the server's response if needed
                $response = socket_read($socket, 1024);
                dd($response);
                // Close the socket
                socket_close($socket);
            }
        }
    }

    // Function to calculate the XOR checksum
    function calculateChecksum()
    {
        $args = func_get_args();
        $checksum = 0;

        foreach ($args as $arg) {
            $checksum ^= ord($arg);
        };

        return chr($checksum);
    }


    public function checkLPRTownsendTicket(Request $request)
    {
        $startDate = date("Y-m-d", strtotime($request->start_date));
        $endDate = date("Y-m-d", strtotime($request->end_date));
        /*try{
            $tickets = Ticket::where("facility_id", 33)->whereDate("checkin_time", '>=', $startDate)->whereDate("checkin_time", '<=', $endDate)->orderBy("id", "DESC")->get();
        }catch(\Exception $e){
            dd($e);
        }*/

        $tickets = Ticket::where("facility_id", 33)->whereDate("checkin_time", '>=', $startDate)->whereDate("checkin_time", '<=', $endDate)->orderBy("id", "DESC")->get();


        $data = [];
        if (count($tickets) > 0) {
            $data['total_checkins'] = count($tickets);
            $matchLicensePlate = 0;
            $onlyCheckinLicensePlate = 0;
            foreach ($tickets as $key => $ticket) {
                if ($ticket->license_plate != '') {
                    if ($ticket->license_plate == $ticket->checkout_license_plate) {
                        $matchLicensePlate++;
                    }

                    $onlyCheckinLicensePlate++;
                }
            }
            $data['total_checkin_checkout_match_license_plate'] = $matchLicensePlate;
            $data['total_only_checkin_license_plate'] = $onlyCheckinLicensePlate;
        }

        return $data;
    }


    public function importEventExcelFile(Request $request)
	{
        if ($request->file('event_list')) {        
			$inputfile = $request->file('event_list');
            $ext = $inputfile->getClientOriginalExtension();
            if ($ext == 'xlsx') {
				$fileName = time() . '_doc79ventlist.' . $ext;
				$destination_path = storage_path("import/");
				$inputfile->move($destination_path, $fileName);
				$file = storage_path('import/') . $fileName;
                $data = Excel::load($file)->get();
                $newdata = $data->toArray();
                //dd($newdata);
                $fileData = [];
				if (!empty($newdata)) {                        
					
					foreach ($data as $key=>$value) {
                        $date = date("Y-m-d", strtotime($value['date']));
                        $monthYear = date("M Y", strtotime($value['date']));
                        $startTime = date("Y-m-d H:i:s", strtotime(date("Y-m-d",strtotime($date)).' '.$value['event_start_time']));
                        $parkingTime = date("Y-m-d 06:00:00", strtotime(date("Y-m-d",strtotime($date))));
                        $startEndTime = date("Y-m-d H:i:s",strtotime('+3 hours', strtotime($startTime)));
                        //dd(date("Y-m-d H:i:s", strtotime("+3 hours", "Y-m-d H:i:s")));
                        $parkingEndTime = date(date("Y-m-d 03:00:00",strtotime('+1 day', strtotime($startTime))));
                        //dd($startTime, $parkingTime, $startEndTime,$parkingEndTime);
                            
                            
                            $event['title'] = $value['event_name']. " ".$monthYear;
                            $event['slug'] = $value['event_name'];
							$event['start_time'] = $startTime;
							$event['end_time'] = $startEndTime;
                            $event['parking_start_time'] = $parkingTime;
                            $event['parking_end_time'] = $parkingEndTime;
							$event['partner_id'] = 6724;
                            $event['created_by'] = 6724;
                            $event['event_rate'] = 60.00;
                            $event['driveup_event_rate'] = 60.00;
                            $event['base_event_hours'] = 21;
                            $event['image'] = "doc79.png";
                            $event['is_active'] = '1';
                            $eventId = Event::create($event);

                            if($eventId){
                                $eventFacility['event_id'] = $eventId->id;
                                $eventFacility['facility_id'] = 158;
                                EventFacility::create($eventFacility);
                            }

                            $geolocation = [];
                            $geolocation['address_1'] = "79 Potomac Avenue SE";
                            $geolocation['city'] = "Washington";
                            $geolocation['state'] = "DC";
                            $geolocation['zip_code'] = "20003";
                            $geolocation['locatable_id'] = $eventId->id;
                            $geolocation['locatable_type'] = "App\Models\Event";
                            $geolocation['longitude'] = "-77.0069227";
                            $geolocation['latitude'] = "38.8713823";
                            //print_r($geolocation);
                            GeoLocation::create($geolocation);
                            /*if($eventId){
                                $eventFacility['event_id'] = $eventId->id;
                                $eventFacility['facility_id'] = 425;
                                EventFacility::create($eventFacility);
                            }*/
                        //yankee event
                        //if(!empty($value['date']) && !empty($value['time'])  && !empty($value['event'])){
							/*$startTime = date("Y-m-d H:i:s", strtotime(date("Y-m-d",strtotime($value['date'])).' '.$value['time']));
                            $day = date("Y-m-d",strtotime($value['date']));
                            $weekDays = ['Monday', 'Tuesday', 'Wednesday', "Thrusday", 'Friday'];
                            if(!in_array($day, $weekDays))
                            {
                                $parkingStartTime = date(date("Y-m-d",strtotime($value['date'])).' H:i:s', strtotime('-6 hours', strtotime($startTime)));
                                $parkingEndTime = date('Y-m-d')." "."23:59:59";
                                $startEndTime = date(date("Y-m-d",strtotime($value['date'])).' H:i:s', strtotime('+4 hours', strtotime($startTime)));
                            }
                            $weekendDays = ['Sunday', 'Saturday'];
                            if(!in_array($day, $weekendDays))
                            {
                                $parkingStartTime = date("Y-m-d",strtotime($value['date']))." "."00:00:01";
                                $parkingEndTime = date("Y-m-d",strtotime($value['date']))." "."23:59:59";
                                $startEndTime = date(date("Y-m-d",strtotime($value['date'])).' H:i:s', strtotime('+4 hours', strtotime($startTime)));
                            }
                            
                            $Cruiseschedule = [];
                            $event['title'] = $value['event'];
                            $event['slug'] = QueryBuilder::slugify($value['event'], '-');
							$event['start_time'] = $startTime;
							$event['end_time'] = $startEndTime;
                            $event['parking_start_time'] = $parkingStartTime;
                            $event['parking_end_time'] = $parkingEndTime;
							$event['partner_id'] = 363362;
                            $event['created_by'] = 363362;
                            $event['event_rate'] = 47.00;
                            $event['driveup_event_rate'] = 47.00;
                            $event['base_event_hours'] = 4;
                            $event['is_active'] = '1';
                            $eventId = Event::create($event);

                            if($eventId){
                                $eventFacility['event_id'] = $eventId->id;
                                $eventFacility['facility_id'] = 405;
                                EventFacility::create($eventFacility);
                            }*/
							
						//}
					}
				} else {
					unlink(storage_path('import/' . $fileName));
					throw new ApiGenericException("Empty Data in File");
				}
                return 'Data Uploaded Successfully';
			}else {
				unlink(storage_path('import/' . $fileName));
				throw new ApiGenericException("Invalid file format");
					
			}
		}
	}

    public function importEventExcelFilebkp(Request $request)
	{
        if ($request->file('event_list')) {        
			$inputfile = $request->file('event_list');
            $ext = $inputfile->getClientOriginalExtension();

			if ($ext == 'xlsx') {
				$fileName = time() . '_yankeeeventlist.' . $ext;
				$destination_path = storage_path("import/");
				$inputfile->move($destination_path, $fileName);
				$file = storage_path('import/') . $fileName;
                $data = Excel::load($file)->get();
                $newdata = $data->toArray();
                $fileData = [];
				if (!empty($newdata)) {                        
					
					foreach ($data as $key=>$value) {
                        if(!empty($value['date']) && !empty($value['time'])  && !empty($value['event'])){
							$cruisedata =[];
                            //$value['date'].' '.$value['time'];
                            //dd($value['date'].' '.$value['time']);
                            $startTime = date("Y-m-d H:i:s", strtotime(date("Y-m-d",strtotime($value['date'])).' '.$value['time']));
                            //dd(date("Y-m-d H:i:s", ))
                            $day = date("Y-m-d",strtotime($value['date']));
                            $weekDays = ['Monday', 'Tuesday', 'Wednesday', "Thrusday", 'Friday'];
                            if(!in_array($day, $weekDays))
                            {
                                $parkingStartTime = date(date("Y-m-d",strtotime($value['date'])).' H:i:s', strtotime('-6 hours', strtotime($startTime)));
                                $parkingEndTime = date('Y-m-d')." "."23:59:59";
                                $startEndTime = date(date("Y-m-d",strtotime($value['date'])).' H:i:s', strtotime('+4 hours', strtotime($startTime)));
                            }
                            $weekendDays = ['Sunday', 'Saturday'];
                            if(!in_array($day, $weekendDays))
                            {
                                $parkingStartTime = date("Y-m-d",strtotime($value['date']))." "."00:00:01";
                                $parkingEndTime = date("Y-m-d",strtotime($value['date']))." "."23:59:59";
                                $startEndTime = date(date("Y-m-d",strtotime($value['date'])).' H:i:s', strtotime('+4 hours', strtotime($startTime)));
                            }
                            
                            $Cruiseschedule = [];
                            $event['title'] = $value['event'];
                            $event['slug'] = QueryBuilder::slugify($value['event'], '-');
							$event['start_time'] = $startTime;
							$event['end_time'] = $startEndTime;
                            $event['parking_start_time'] = $parkingStartTime;
                            $event['parking_end_time'] = $parkingEndTime;
							$event['partner_id'] = 26380;
                            $event['created_by'] = 26380;
                            $event['event_rate'] = 47.00;
                            $event['driveup_event_rate'] = 47.00;
                            $event['base_event_hours'] = 4;
                            $event['is_active'] = '1';
                            $eventId = Event::create($event);

                            if($eventId){
                                $eventFacility['event_id'] = $eventId->id;
                                $eventFacility['facility_id'] = 155;
                                EventFacility::create($eventFacility);
                            }
							
						}
					}
				} else {
					unlink(storage_path('import/' . $fileName));
					throw new ApiGenericException("Empty Data in File");
				}
                return 'Data Uploaded Successfully';
			}else {
				unlink(storage_path('import/' . $fileName));
				throw new ApiGenericException("Invalid file format");
					
			}
		}
	}

    public function callJob($Job_Name)
    {
        $instance = "App\\Jobs\\".$Job_Name; 
        dispatch((new $instance()));
        return "Job runned Successfully";
    }
}
