<?php
namespace App\Http\Controllers\ParkEngage;
use Illuminate\Http\Request;
use App\Http\Requests;
use Auth;
use App\Http\Controllers\Controller;
use Hash;
use Exception;
use Artisan;
use App\Services\Pdf;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use App\Exceptions\NotFoundException;
use PHPExcel_Worksheet_Drawing;
use App\Exceptions\ApiGenericException;
use App\Models\PermitRequest;
use App\Models\AuthorizeNetTransaction;
use App\Services\LoggerFactory;
use App\Models\Facility;
use App\Models\User;
use Mail;
use App\Models\OauthClient;
use App\Models\BlackListedVehicle;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use Response;
use Excel;
use App\Models\ParkEngage\MstMake;
use App\Models\ParkEngage\MstModel;
use App\Models\ParkEngage\MstColor;
use App\Models\ParkEngage\State;
use App\Models\ParkEngage\BrandSetting;


class DiamondBlacklistController extends Controller
{
	/*  
    * Code initiated by <PERSON><PERSON><PERSON>  
    * Date: Dec 17, 2024  
    * Purpose: Notify if a reservation has not been booked within the set notification time.  
    */
    
   
    protected  $request;
    protected  $log;
    protected $errorLog;
    protected $user;


    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/are/blacklisted')->createLogger('blacklistedbulkData');
      
    }

	#PIMS-11709 : Start
	#KT: 17-12-2024

	public function addVehicle(Request $request)
	{
		$user = Auth::user();
		$partner_id = null;

		if (Auth::user()->user_type == '1') {
				$partner_id = isset($request->partner_id) ? $request->partner_id : '';
			} elseif (Auth::user()->user_type == '4') {
				$admin_partner_id = Auth::user()->created_by;
			if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
				$partner_id = isset($request->partner_id) ? $request->partner_id : '';
			} else {
				$partner_id = Auth::user()->created_by;
			}
			} elseif (Auth::user()->user_type == '12') {
				$partner_id = Auth::user()->created_by;
			} elseif (Auth::user()->user_type == '3') {
				$partner_id = Auth::user()->id;
			} else {
				$partner_id = $request->partner_id;
			}

		if (!$request->license_plate_number) {
			throw new ApiGenericException("License plate number is required");
		}

		$license_plate_numbers = explode(",", $request->license_plate_number);
		$created_vehicles = [];

		foreach ($license_plate_numbers as $plate_number) {
			// Check if the vehicle is blacklisted/whitlisted/Bolo
			$blacklisted_vehicle = BlackListedVehicle::where('license_plate_number', $plate_number)->where('partner_id', $partner_id)->first();

			// if ($blacklisted_vehicle) {
			// 	return response()->json([
			// 		'error_msg' => "Vehicle '{$plate_number}' is already in the blacklist, whitelist or BOLO list."
			// 	], 400);
			// }

			if ($blacklisted_vehicle) {
				$message = "Vehicle '{$plate_number}' is already in the blacklist, whitelist, or BOLO list.";

				header('Content-Type: application/json', true, 500);
				echo json_encode([
					'status' => 500,
					'data' => null,
					'errors' => [
						'message' => $message
					]
				]);
				exit;
			}

			$vehicle_data = [
				'partner_id' => $partner_id,
				'license_plate_number' => $plate_number,
				'make_model' => $request->make_model,
				'make_id' => $request->make_id,
				'model_id' => $request->model_id,
				// 'facility_id' => $request->facility_id ?? null,
				'plate_type' => $request->plate_type ?? null,
				'vehicle_type' => $request->vehicle_type ?? null,
				'vehicle_type_id' => $request->vehicle_type_id ?? null,
				'color' => $request->color ?? null,
				'color_id' => $request->color_id,
				'state_id' => $request->state_id,
				'status' => $request->status ?? '1',
				'is_active' => $request->is_active ?? '1',
				'created_by' => Auth::user()->id,
			];

			$created_vehicles[] = BlackListedVehicle::create($vehicle_data);
		}

		return response()->json($created_vehicles, 201);
	}
	#KT:End
	#17-12-2024


	#PIMS-11709 : Start
	#KT: 19-12-2024
	public function getVehicleList(Request $request)
	{
		ini_set('max_execution_time', 0);
		$result = [];
		if (Auth::user()->user_type == '1') {
		$partner_id = isset($request->partner_id) ? $request->partner_id : '';
		} elseif (Auth::user()->user_type == '4') {
		$admin_partner_id = Auth::user()->created_by;
		if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
			$partner_id = isset($request->partner_id) ? $request->partner_id : '';
		} else {
			$partner_id = Auth::user()->created_by;
		}
		} elseif (Auth::user()->user_type == '12') {
		$partner_id = Auth::user()->created_by;
		} elseif (Auth::user()->user_type == '3') {
		$partner_id = Auth::user()->id;
		} else {
		$partner_id = $request->partner_id;
		}

		$month = date("m");
		if (isset($request->from_date) && $request->from_date != '')
		{
			$from_date = $request->from_date;
			$to_date = $request->to_date;
		}

		$vehicleList = BlackListedVehicle::query()
			->select(
				'blacklisted_vehicles.*',
				'users.name as partner_name',
				'mst_makes.name as make_name',
				'mst_models.name as model_name',
				'mst_colors.name as color_name',
				'states.name as state_name'
			)
			->leftJoin('users', 'blacklisted_vehicles.partner_id', '=', 'users.id')
			->leftJoin('mst_makes', 'blacklisted_vehicles.make_id', '=', 'mst_makes.id')
			->leftJoin('mst_models', 'blacklisted_vehicles.model_id', '=', 'mst_models.id')
			->leftJoin('mst_colors', 'blacklisted_vehicles.color_id', '=', 'mst_colors.id')
			->leftJoin('states', 'blacklisted_vehicles.state_id', '=', 'states.id')
			->whereIn('blacklisted_vehicles.status', [0, 1]);

		if ($request->search) {
			$vehicleList->where(function ($query) use ($request) {
				$query->where('blacklisted_vehicles.license_plate_number', 'like', "%{$request->search}%")
					->orWhere('mst_makes.name', 'like', "%{$request->search}%")
					->orWhere('mst_models.name', 'like', "%{$request->search}%")
					->orWhere('blacklisted_vehicles.vehicle_type', 'like', "%{$request->search}%");
			});
		}

		if ($request->plate_type) {
			$vehicleList->where('blacklisted_vehicles.plate_type', $request->plate_type);
		}

		if (isset($request->from_date) && $request->from_date != '') {
			$vehicleList = $vehicleList->whereDate('blacklisted_vehicles.created_at', '>=', $from_date)->whereDate('blacklisted_vehicles.created_at', '<=', $to_date);
		}
	  
		if (isset($partner_id) && $partner_id != '') {
			$vehicleList = $vehicleList->where(function ($query) use ($partner_id) {
				$query->where('blacklisted_vehicles.partner_id', $partner_id);
			});
		}

		if ($request->sort && $request->sortBy) {
			$vehicleList->orderBy($request->sort, $request->sortBy);
		} else {
			$vehicleList->orderBy('blacklisted_vehicles.id', 'DESC');
		}

		if ($request->download_type == '1' || $request->download_type == '2') {
			$allVehicleList = $vehicleList->get();
	  
	  
			if ($request->download_type == '2') {
			  $this->downloadVehiclePdfs($request);
			} else {
			  $this->downloadVehicleExcel($allVehicleList, $request);
			}
		  }
	  
		  $vehicleList = $vehicleList->paginate(20);
		  if ($request->sort == 'name') {
			if (count($vehicleList) > 0) {
			  if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
				for ($i = 0; $i < count($vehicleList); $i++) {
				  for ($j = $i + 1; $j < count($vehicleList); $j++) {
					if ($vehicleList[$i]['user']->name > $vehicleList[$j]['user']->name) {
					  $temp = $vehicleList[$i];
					  $vehicleList[$i] = $vehicleList[$j];
					  $vehicleList[$j] = $temp;
					}
				  }
				}
			  } else {
				for ($i = 0; $i < count($vehicleList); $i++) {
				  for ($j = $i + 1; $j < count($vehicleList); $j++) {
					if ($vehicleList[$i]['user']->name < $vehicleList[$j]['user']->name) {
					  $temp = $vehicleList[$i];
					  $vehicleList[$i] = $vehicleList[$j];
					  $vehicleList[$j] = $temp;
					}
				  }
				}
			  }
			}
		  }

		return $vehicleList;
	}
	#End

	#KT
	public function getPlateTypeLabel($plateType) {
		switch ($plateType) {
			case 1:
				return 'Blacklisted Plate';
			case 2:
				return 'BOLO Plate';
			case 3:
				return 'Whitelisted Plate';
			default:
				return '-';
		}
	}

	#PIMS: 11709 Start:
	#Kuldeep
	public function downloadVehicleExcel($allVehicleList, $request)
	{
		//dd(11);
		$partner_id = $request->partner_id;
		$brandSetting = null;
		$fromDate = isset($request->from_date) ? date('m/d/Y', strtotime($request->from_date)) : date('m/d/Y');
    	$toDate = isset($request->to_date) ? date('m/d/Y', strtotime($request->to_date)) : date('m/d/Y');
		$currentDate = date('m-d-Y');

		$excelSheetName = ucwords(str_replace(' ', '', 'VehicleListExcel'));
		$finalCodes1 = [];
		$increment1 = 1;
		if (count($allVehicleList) > 0) {
		foreach ($allVehicleList as $val) {
			// dd($val);
			$permit_status = '';
			$license_plate_number = '';
			$make = '';
			$model = '';

			$finalCodes1[] = [
				'License Plate' => isset($val->license_plate_number) ? $val->license_plate_number : '-',
				'Plate Type' => isset($val->plate_type) ? $this->getPlateTypeLabel($val->plate_type) : '-',
				'Vehicle Type' => isset($val->vehicle_type) ? $val->vehicle_type : '-',
				'Make' => isset($val->make_name) ? $val->make_name : '-',
				'Model' => isset($val->model_name) ? $val->model_name : '-',
				'Color' => isset($val->color_name) ? $val->color_name : '-',
				'State' => isset($val->state_name) ? $val->state_name : '-',
				'Created Date' => isset($val->created_at) ? date('m/d/Y', strtotime($val->created_at)) : '-',
			];
			$increment1++;
		}
		} else {
			throw new ApiGenericException("No Record Found");
		}
		//dd($finalCodes1);

		$getLogoId = 0;
		$color = "#0C4A7A";
		if (isset($partner_id) && !empty($partner_id)) {
		$brandSetting = BrandSetting::where('user_id', $partner_id)->first();
		if (isset($brandSetting) && !empty($brandSetting)) {
			$getLogoId = $brandSetting->id;
			$color = $brandSetting->color;
		}
		}

		Excel::create(
		$excelSheetName,
		function ($excel) use ($finalCodes1, $excelSheetName, $brandSetting, $color, $fromDate, $toDate) {

			// Set the spreadsheet title, creator, and description
			$excel->setTitle($excelSheetName);
			$excel->setCreator('Vehicle')->setCompany('ParkEngage');
			$excel->setDescription('List Of Vehicle List');
			// Build the spreadsheet, passing in the payments array
			if (empty($finalCodes1)) {
			throw new ApiGenericException('Sorry! No Data Found.');
			} else {
			if (isset($finalCodes1) && !empty($finalCodes1)) {
				$getDateSummary = 'Vehicle List';
				$excel->sheet(
				'Vehicle List',
				function ($sheet) use ($finalCodes1, $brandSetting, $color, $fromDate, $toDate, $getDateSummary) {
					$this->addLogoInExcelHeaders($sheet, $getDateSummary, $brandSetting, $color);
					$headerSpace = 4;
					$this->getBrandHeaderSections($sheet, $color, $fromDate, $toDate);
					// $sheet->getRowDimension(3)->setRowHeight(50);
					$sheet->mergeCells('A3:H3');
					$sheet->setCellValue('A3', 'Vehicle List');
					$sheet->cell('A3:H3', function ($cell) use ($color) {
					$cell->setAlignment('center');
					$cell->setValignment('center');
					$cell->setFontWeight('bold');
					$cell->setBackground('#D9E1F2');
					$cell->setFontColor('#272829');
					$cell->setFontSize('12');
					});
					// Color Row For Heading 
					$sheet->cell("A$headerSpace:H$headerSpace", function ($row) use ($color) {
					$row->setBackground($color);
					$row->setFontColor('#ffffff');
					});

					// Adding data to sheet
					$sheet->fromArray($finalCodes1, null, 'A' . "$headerSpace", false, true);
					$j = count($finalCodes1) + 4;
					for ($i = 4; $i <= $j; $i++) {
					$sheet->cell('A' . $i . ':H' . $i, function ($cell) use ($color) {
						$cell->setAlignment('center');
						$cell->setValignment('center'); 
					});
					}
			
				}
				);
			}
			}
		}
		)->store('xls')->download('xls');
	}
  	#PIMS: 11709 Start:
	#Kuldeep
	public function addLogoInExcelHeaders($excel, $title = "Vehicle List", $brandSetting, $color = '#191D88')
	{
		/* Code for Logo */
		$logoPath = public_path('assets/media/images/logo.png');

		// Check if a brand logo is provided
		if (isset($brandSetting) && !empty($brandSetting->logo)) {
		$dynamicLogoPath = storage_path('app/brand-settings/' . $brandSetting->logo);

		if (file_exists($dynamicLogoPath)) {
			$logoPath = $dynamicLogoPath;
		} else {
			logger()->warning("Brand logo not found at: {$dynamicLogoPath}. Using default logo.");
		}
		} else {
		logger()->info("Brand logo is not set. Using default logo.");
		}

		// Set the logo in Excel
		$drawing = new PHPExcel_Worksheet_Drawing();
		$drawing->setPath($logoPath);
		$drawing->setCoordinates('A1');
		$drawing->setWidth(150);
		$drawing->setHeight(50);
		$drawing->setOffsetX(25);
		$drawing->setOffsetY(10);
		$excel->getDrawingCollection()->append($drawing);

		// Apply styling to header
		$excel->cell('A1', function ($cell) use ($color) {
		$cell->setAlignment('center'); 
		$cell->setValignment('center');
		$cell->setFontWeight('bold');
		$cell->setFontSize(30);
		$cell->setFontColor('#ffffff');
		$cell->setBackground('#ffffff');
		});
	}


	public function getBrandHeaderSections($excel, $color, $fromDate, $toDate)
	{
		$excel->mergeCells('A2:F2');
		$cellValue = "Report Date Range - " . date('m-d-Y', strtotime($fromDate)) . ' - ' . date('m-d-Y', strtotime($toDate));
		$cellValue .= "\nPrint Date - " . date('m-d-Y', strtotime('now'));
		$excel->setCellValue('A2', $cellValue);
		$excel->getStyle('A2')->getAlignment()->setWrapText(true);
		$excel->getRowDimension(2)->setRowHeight(80);

		$excel->cell('A2:H2', function ($cell) use ($color) {
		$cell->setAlignment('center');
		$cell->setValignment('center');
		$cell->setFontWeight('bold');
		$cell->setBackground($color);
		$cell->setFontColor('#ffffff');
		$cell->setFontSize(18);
		});


		// Main title row
		$excel->mergeCells('A1:H1');
		$excel->getRowDimension(1)->setRowHeight(60);
		$excel->setCellValue('A1', "Vehicle List");
		$excel->cell('A1:H1', function ($cell) use ($color) {
		$cell->setAlignment('center');
		$cell->setValignment('center');
		$cell->setFontWeight('bold');
		$cell->setBackground($color);
		$cell->setFontColor('#ffffff');
		$cell->setFontSize(30);
		});
	}
	#PIMS: 11709 End:


	#PIMS: 11709 Start:
	#KT
	public function downloadVehiclePdf(Request $request)
	{

		ini_set('max_execution_time', 0);

		$partner_id = null;

		if (Auth::user()->user_type == '1') {
		$partner_id = isset($request->partner_id) ? $request->partner_id : '';
		} elseif (Auth::user()->user_type == '4') {
		$admin_partner_id = Auth::user()->created_by;
		if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
			$partner_id = isset($request->partner_id) ? $request->partner_id : '';
		} else {
			$partner_id = Auth::user()->created_by;
		}
		} elseif (Auth::user()->user_type == '12') {
		$partner_id = Auth::user()->created_by;
		} elseif (Auth::user()->user_type == '3') {
		$partner_id = Auth::user()->id;
		} else {
		$partner_id = $request->partner_id;
		}

		$month = date("m");
		$from_date  = date('Y-' . $month . '-01');
		$to_date  = date('Y-' . $month . '-t');

		if (isset($request->from_date) && $request->from_date != '') {
		$from_date = $request->from_date;
		$to_date = $request->to_date;
		}

		$vehicleList = BlackListedVehicle::query()
			->select(
				'blacklisted_vehicles.*',
				'users.name as partner_name',
				'mst_makes.name as make_name',
				'mst_models.name as model_name',
				'mst_colors.name as color_name',
				'states.name as state_name'
			)
			->leftJoin('users', 'blacklisted_vehicles.partner_id', '=', 'users.id')
			->leftJoin('mst_makes', 'blacklisted_vehicles.make_id', '=', 'mst_makes.id')
			->leftJoin('mst_models', 'blacklisted_vehicles.model_id', '=', 'mst_models.id')
			->leftJoin('mst_colors', 'blacklisted_vehicles.color_id', '=', 'mst_colors.id')
			->leftJoin('states', 'blacklisted_vehicles.state_id', '=', 'states.id')
			->whereIn('blacklisted_vehicles.status', [0, 1]);

			if (isset($request->search)) {
			$vehicleList->where(function ($query) use ($request) {
				$query->where('blacklisted_vehicles.license_plate_number', 'like', "%{$request->search}%")
					->orWhere('mst_makes.name', 'like', "%{$request->search}%")
					->orWhere('mst_models.name', 'like', "%{$request->search}%")
					->orWhere('blacklisted_vehicles.vehicle_type', 'like', "%{$request->search}%");
			});
		}
		if (isset($partner_id) && $partner_id != '') {
			$vehicleList = $vehicleList->where(function ($query) use ($partner_id) {
				$query->where('blacklisted_vehicles.partner_id', $partner_id);
			});
		}

		if ($request->sort && $request->sortBy) {
			$vehicleList->orderBy($request->sort, $request->sortBy);
		} else {
			$vehicleList->orderBy('blacklisted_vehicles.id', 'DESC');
		}

		$vehicleList = $vehicleList->whereDate('blacklisted_vehicles.created_at', '>=', $from_date)
		->whereDate('blacklisted_vehicles.created_at', '<=', $to_date);

		if (isset($partner_id) && $partner_id != '') {
		$vehicleList = $vehicleList->where('blacklisted_vehicles.partner_id', $partner_id);
		}

		$vehicleList = $vehicleList->orderBy('blacklisted_vehicles.id', 'DESC');

		if ($request->download_type == '1' || $request->download_type == '2') {
		$allVehicleList = $vehicleList->get();

		if ($allVehicleList->isEmpty()) {
			throw new ApiGenericException("No Record Found");
		}

		$finalCodes = [];
		foreach ($allVehicleList as $i => $val) {
			$finalCodes[] = [
				'No'            => $i + 1,
				'License Plate' => isset($val->license_plate_number) ? $val->license_plate_number : '-',
				'Plate Type' 	=> isset($val->plate_type) ? $this->getPlateTypeLabel($val->plate_type) : '-',
				'Vehicle Type'  => isset($val->vehicle_type) ? $val->vehicle_type : '-',
				'Make'          => isset($val->make_name) ? $val->make_name : '-',
				'Model'         => isset($val->model_name) ? $val->model_name : '-',
				'Color'         => isset($val->color_name) ? $val->color_name : '-',
				'State'         => isset($val->state_name) ? $val->state_name : '-',
				'Created Date' => isset($val->created_at) ? date('m/d/Y', strtotime($val->created_at)) : '-',
			];
		}

		// Brand and Header Settings
		$brandSetting = $partner_id ? BrandSetting::where('user_id', $partner_id)->first() : null;
		$getLogoId = $brandSetting ? $brandSetting->id : 0;
		$color = $brandSetting ? $brandSetting->color : "#0C4A7A";

		$headerArr = [
			'from_date' => $from_date,
			'to_date' => $to_date,
		];

		// Render PDF
		$html = view("download.vehicle-details", [
			"data" => $finalCodes,
			'getLogoId' => $getLogoId,
			'color' => $color,
			'headerArr' => $headerArr
		])->render();

		$image = app()->make(Pdf::class);
		$pdf = $image->getOutputFromHtmlString($html);

		return $pdf;
		}

		throw new ApiGenericException("Invalid download type.");
	}
  	#PIMS: 11709 PDF End:

	#KT: PIMS - 11709
	#19-12-2024
	public function getVehicleByid($id)
	{
		$is_vehicle = BlackListedVehicle::where('id', $id)->first();

		if (!$is_vehicle) {
			throw new ApiGenericException("Vehicle Not Found.");
		}

		$partner_name = User::where('id', $is_vehicle->partner_id)->value('name');
		$make_name = MstMake::where('id', $is_vehicle->make_id)->value('name');
		$model_name = MstModel::where('id', $is_vehicle->model_id)->value('name');
		$color_name = MstColor::where('id', $is_vehicle->color_id)->value('name');
		$state_name = State::where('id', $is_vehicle->state_id)->value('name');

		$is_vehicle->partner_name = $partner_name;
		$is_vehicle->make_name = $make_name;
		$is_vehicle->model_name = $model_name;
		$is_vehicle->color_name = $color_name;
		$is_vehicle->state_name = $state_name;

		return $is_vehicle;

	}
	#End


	#KT: PIMS - 11709
	#18-12-2024
	public function updateVehicle(Request $request)
    {
		$partner_id = null;
		if (Auth::user()->user_type == '1') {
				$partner_id = isset($request->partner_id) ? $request->partner_id : '';
			} elseif (Auth::user()->user_type == '4') {
				$admin_partner_id = Auth::user()->created_by;
			if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
				$partner_id = isset($request->partner_id) ? $request->partner_id : '';
			} else {
				$partner_id = Auth::user()->created_by;
			}
			} elseif (Auth::user()->user_type == '12') {
				$partner_id = Auth::user()->created_by;
			} elseif (Auth::user()->user_type == '3') {
				$partner_id = Auth::user()->id;
			} else {
				$partner_id = $request->partner_id;
			}
		$vehicle = BlackListedVehicle::where('id', $request->id)->first();
        if($vehicle){
			$vehicle->partner_id = $partner_id;			
			$vehicle->license_plate_number = $request->license_plate_number;
			// $vehicle->facility_id = $request->facility_id;
			$vehicle->make_model = $request->make_model;
			$vehicle->make_id = $request->make_id;
			$vehicle->model_id = $request->model_id;
			$vehicle->color_id = $request->color_id;
			$vehicle->state_id = $request->state_id;
			$vehicle->plate_type = isset($request->plate_type)? $request->plate_type:'';
			$vehicle->vehicle_type = isset($request->vehicle_type)? $request->vehicle_type:'';
			$vehicle->vehicle_type_id = isset($request->vehicle_type_id)? $request->vehicle_type_id:NULL; #pims-13318
			$vehicle->color =  isset($request->color)? $request->color:'';  
			$vehicle->status = isset($request->status)? $request->status:'1';
			$vehicle->save();
			return $vehicle;
		}else{
			throw new ApiGenericException("Sorry! Record Not Found.");  
		}
     }
 	
     public function deleteVehicle($id)
     {
        
        $is_vehicle = BlackListedVehicle::find($id);
        if(!$is_vehicle)
        {
            throw new ApiGenericException("Vehicle Not Found.");   
        }
         
        $is_vehicle->delete();
         return "Data successfully deleted.";

     }


	public function getBlacklistVehicleImportData(Request $request)
	{
		$user = Auth::user();
		$partner_id = null;

		if (Auth::user()->user_type == '1') {
			$partner_id = isset($request->partner_id) ? $request->partner_id : '';
		} elseif (Auth::user()->user_type == '4') {
			$admin_partner_id = Auth::user()->created_by;
			if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
				$partner_id = isset($request->partner_id) ? $request->partner_id : '';
			} else {
				$partner_id = Auth::user()->created_by;
			}
		} elseif (Auth::user()->user_type == '12') {
			$partner_id = Auth::user()->created_by;
		} elseif (Auth::user()->user_type == '3') {
			$partner_id = Auth::user()->id;
		} else {
			$partner_id = $request->partner_id;
		}

		if ($request->file('input_importfile')) {
			$inputfile = $request->file('input_importfile');
			$ext = $inputfile->getClientOriginalExtension();

			if ($ext == 'csv') {
				$fileName = time() . '_blacklistedfile.' . $ext;
				$destinationPath = storage_path("import/");
				$inputfile->move($destinationPath, $fileName);
				$file = storage_path('import/') . $fileName;

				$data = Excel::load($file)->get();

				if ($data->isEmpty()) {
					unlink($file);
					throw new ApiGenericException("Empty Data in File");
				}

				$headers = $data->first()->keys()->toArray();
				$requiredHeaders = [
					"license_plate_2_to_10_characters_only",
					"plate_type_blacklisted_bolo_whitelisted",
					"vehicle_type",
					"make",
					"model",
					"color",
					"state",
				];

				foreach ($requiredHeaders as $header) {
					if (!in_array($header, $headers)) {
						unlink($file);
						throw new ApiGenericException("Invalid Data Format: Missing header $header");
					}
				}

				$failedData = [];
				$existingPlates = [];
				$createdVehicles = [];

				foreach ($data as $row) {
					$plate = trim($row['license_plate_2_to_10_characters_only']);

					// Validate license plate length
					if (strlen($plate) < 2 || strlen($plate) > 10) {
						$failedData[] = $plate;
						continue;
					}

					$make = null;
					$model = null;
					$color = null;
					$state = null;

					if (!empty($row['make'])) {
						$make = MstMake::where('name', 'LIKE', '%' . $row['make'] . '%')->first();
					}
					if (!empty($row['model'])) {
						$model = MstModel::where('name', 'LIKE', '%' . $row['model'] . '%')->first();
					}
					if (!empty($row['color'])) {
						$color = MstColor::where('name', 'LIKE', '%' . $row['color'] . '%')->first();
					}
					if (!empty($row['state'])) {
						$state = State::where('name', 'LIKE', '%' . $row['state'] . '%')->first();
					}

					$plateType = strtolower(str_replace(' ', '', $row['plate_type_blacklisted_bolo_whitelisted']));
					$plateTypeMap = [
						'blacklisted' => 1,
						'blacklist' => 1,
						'bolo' => 2,
						'whitelisted' => 3,
						'whitelist' => 3,
					];

					$existingVehicle = BlackListedVehicle::where('license_plate_number', $plate)
						->where('partner_id', $partner_id)
						->first();

					if ($existingVehicle) {
						$existingPlates[] = $plate;
						continue;
					}

					$vehicle = new BlackListedVehicle();
					$vehicle->license_plate_number = $plate;
					$vehicle->plate_type = $plateTypeMap[$plateType] ?? null;
					if (is_null($vehicle->plate_type)) {
						$failedData[] = $plate;
						continue;
					}

					$vehicle->vehicle_type = $row['vehicle_type'] ?? '';
					$vehicle->make_id = $make ? $make->id : null;
					$vehicle->model_id = $model ? $model->id : null;
					$vehicle->color_id = $color ? $color->id : null;
					$vehicle->state_id = $state ? $state->id : null;
					$vehicle->status = 1;
					$vehicle->partner_id = $partner_id;
					$vehicle->created_by = Auth::user()->id;
					$vehicle->save();

					$createdVehicles[] = $vehicle;
				}

				unlink($file);

				# If all plates are existing, return 500
				if (count($createdVehicles) === 0 && !empty($existingPlates)) {
					$message = "The following vehicles are already added to the list: " . implode(', ', $existingPlates);

					header('Content-Type: application/json', true, 500);
					echo json_encode([
						'status' => 500,
						'data' => null,
						'errors' => [
							'message' => $message
						]
					]);
					exit;
				}

				# If some succeeded but some failed
				if (!empty($failedData)) {
					$this->log->error('Issue in uploading blacklisted data for the following license plates:', $failedData);
					return response()->json([
						'message' => 'Data Uploaded Successfully with Some Errors',
						'failed_data' => $failedData,
						'data' => $createdVehicles
					], 207);
				}

				return response()->json([
					'message' => 'Data Uploaded Successfully',
					'data' => $createdVehicles
				], 200);
			} else {
				return response()->json(['message' => 'Invalid File Type'], 400);
			}
		}

		return response()->json(['message' => 'No File Selected'], 400);
	}
	 

	public function downloadSampleBlacklistedFile() 
    {
		$excelSheetName = ucwords(str_replace(' ', '', 'sampleBlacklist'));
		$Columns[] = [
			'license_plate_2_to_10_characters_only' => '',
			'plate_type_blacklisted_bolo_whitelisted' => '',
			'vehicle_type' => '',
			'Make' => '',
			'Model' => '',
			'Color' => '',
			'State' => '',
		];

		Excel::create(
		    $excelSheetName, function ($excel) use ($Columns, $excelSheetName) {

          // Set the spreadsheet title, creator, and description
          $excel->setTitle($excelSheetName);
          $excel->setCreator('sampleBlacklist')->setCompany('ParkEngage');
          $excel->setDescription('Sample File of Blacklisted Data');
		  // Build the spreadsheet, passing in the payments array
          if(isset($Columns) && !empty($Columns)){
            $excel->sheet(
              'Blacklisted Data', function ($sheet) use ($Columns) {
                $sheet->fromArray($Columns, null, 'A1', false, true);
				$sheet->freezeFirstRow('A2');

              }
            );
          }else{
			  throw new ApiGenericException('Sorry! No Data Found.');
		  }
        }
	    )->store('csv')->download('csv');

	}

	
}
