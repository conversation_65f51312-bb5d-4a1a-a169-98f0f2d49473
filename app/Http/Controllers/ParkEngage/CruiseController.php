<?php
namespace App\Http\Controllers\ParkEngage;
use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Exception;
use Carbon\Carbon;
use App\Models\ParkEngage\Cruise;
use App\Exceptions\ApiGenericException;
use App\Models\User;
use App\Models\Facility;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\CruiseSchedule;
use App\Models\ParkEngage\CruiseFacility;
use DB;
use Excel;
use App\Models\OauthClient;
use App\Exceptions\NotFoundException;


class CruiseController extends Controller
{
    public function index(Request $request)
    {   
        $facility = '';
        $partner_id = '';
        if($request->partner_id)
        {
            $partner_id = $request->partner_id;
        }
        else{
            if(Auth::user()->user_type == '4' || Auth::user()->user_type == '12'){
                $facility = \DB::table('user_facilities')->where('user_id',Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                $partner_id = Auth::user()->created_by;
            }elseif(Auth::user()->user_type == '3'){
                $partner_id = Auth::user()->id;
            } 
        }
        
        $cruise = Cruise::with('cruseschedule')->whereNull('deleted_at');

      
        if($request->search !=''){
            
            if($request->search == 'Active' || $request->search == 'active')
            {
                $cruise = $cruise->where('partner_id',$partner_id)->where('is_active', 1);
            }elseif($request->search == 'In-Active' || $request->search == 'in-active' || $request->search == 'inactive'){                
                $cruise= $cruise->where('partner_id',$partner_id)->where('is_active', 0);                
            }else{
                 $cruise= $cruise->where('partner_id',$partner_id)->where('cruise_name','like', "%$request->search%");
            }
            if(isset($partner_id) && $partner_id !='' ){
                $cruise = $cruise->where(function ($query) use ($partner_id) {
                    $query->where('partner_id',$partner_id);
                });
            }
        }
        if(isset($partner_id) && $partner_id !='' ){
           $cruise = $cruise->where('partner_id',$partner_id);
        }

        if($request->sort != ''){         
            $cruise = $cruise->orderBy($request->sort,$request->sortBy);
        }else{
            $cruise = $cruise->orderBy("id","DESC");
        }
        $cruise = $cruise->paginate(10);
        return $cruise;
    }

    public function store(Request $request)
    {  
        $this->validate($request, Cruise::$validation);
        $cruiseRes = Cruise::where('cruise_name',$request->cruise_name)->first();
        if($cruiseRes){
             throw new ApiGenericException("Cruise Name Already exists.");
        }
       
        if(Auth::User()->user_type=='12'){
            $partner_id = Auth::user()->created_by;
        }else{
            $partner_id = Auth::user()->id;
        }
        $data['partner_id'] = $partner_id;
        $data['cruise_name'] = $request->cruise_name;
        $data['is_active'] = $request->is_active;
        $data['slug'] = $request->slug;
        $data['description'] = $request->description;
		$data['start_time'] = $request->start_time;
		$data['end_time'] = $request->end_time;
        $cruise = Cruise::create($data);
        $Cruiseschedule = [];
        if(count($request->curise_schedule)>0){
            foreach ($request->curise_schedule as $schedule) {
            $Cruiseschedule['start_time'] = $schedule['start_time'];
            $Cruiseschedule['end_time'] = $schedule['end_time'];
            $Cruiseschedule['parking_start_time'] = $schedule['parking_start_time'];
            $Cruiseschedule['parking_end_time'] = $schedule['parking_end_time'];
            $Cruiseschedule['cruise_id'] = $cruise->id;
            CruiseSchedule::create($Cruiseschedule);
           }
        }
        foreach ($request->facilities as $facility) {
            
            DB::table('cruise_facility')->insert(['cruise_id' => $cruise->id, 'facility_id' => $facility['id']]);
        }

        return $cruise;
    }

    public function show($id)
    {
        $cruise = Cruise::with('cruseschedule')->where('is_active',1)->whereNull('deleted_at')->find($id);
        if(!$cruise){            
            throw new ApiGenericException("Cruise not found.");
        }       
        $cruiseFacility = CruiseFacility::where('cruise_id',$cruise->id)->pluck('facility_id');
        if($cruiseFacility){
            $facility =  Facility::select('id','short_name','full_name','garage_code')->whereIn('id',$cruiseFacility)->get();
        }
        $cruise->facility = isset($facility)?$facility :''; 
        return $cruise;
    }

    public function update(Request $request)
    {   
        if(Auth::User()->user_type=='12'){
            $partner_id = Auth::user()->created_by;
        }else{
            $partner_id = Auth::user()->id;
        }
        $cruise = Cruise::find($request->id);
        if(!$cruise){
            throw new ApiGenericException("Cruise not found.");
        }        
        $exist = Cruise::where('id', '!=' ,$cruise->id)->where('cruise_name', $request->cruise_name)->first();
        if($exist){
            throw new ApiGenericException('Cruise already exist.');
        }
        $cruise->cruise_name = $request->cruise_name;
        $cruise->is_active = $request->is_active;
        $cruise->slug = $request->slug;
        $cruise->description = $request->description;
		$cruise->start_time = $request->start_time;
		$cruise->end_time = $request->end_time;
        $cruise->save();
        $Cruiseschedule = [];
        if(count($request->curise_schedule)>0){
            CruiseSchedule::where('cruise_id',$cruise->id)->delete();
            foreach ($request->curise_schedule as $schedule) {
				$Cruiseschedule['start_time'] = $schedule['start_time'];
				$Cruiseschedule['end_time'] = $schedule['end_time'];
				$Cruiseschedule['parking_start_time'] = $schedule['parking_start_time'];
				$Cruiseschedule['parking_end_time'] = $schedule['parking_end_time'];
				$Cruiseschedule['cruise_id'] = $cruise->id;
				CruiseSchedule::create($Cruiseschedule);
			}
        }
		if($request->facilities){
			CruiseFacility::where('cruise_id',$cruise->id)->delete();	
		}		
        foreach ($request->facilities as $facility) {
			DB::table('cruise_facility')->insert(['cruise_id' => $cruise->id, 'facility_id' => $facility['id']]);	
        }
        return $cruise;              
    }

    public function destroy($id) {
        $cruise = Cruise::find($id);
        if($cruise){
            CruiseSchedule::where('cruise_id',$cruise->id)->delete();
            $cruise->delete();
            return "Data successfully deleted.";
        }
        throw new ApiGenericException("Cruise not found.");
    }

    public function cruiseList($slug)
    {   
		$slugExist = UserPaymentGatewayDetail::where('touchless_payment_url', $slug)->first();
		if ($slugExist) {
            $facility = Facility::where('owner_id',$slugExist->user_id)->where('active',1)->pluck('id');
        }else{
            $rmSlugExist = User::where('slug', $slug)->first();
            if (!$rmSlugExist) {
				throw new NotFoundException('No partner found.');
			}
		    $facility = DB::table('user_facilities')->join('facilities', 'facilities.id', '=', 'user_facilities.facility_id')->where('facilities.active',1)->where('user_facilities.user_id', $rmSlugExist->id)->whereNull('user_facilities.deleted_at')->pluck('user_facilities.facility_id');
        }
        //$facility = config('parkengage.WORLDPORT_FACILITY');// after discuss it is implemented only cruise will be load for worldport facility.
        $cruise = Cruise::select('cruise.*')->join('cruise_facility', 'cruise_facility.cruise_id', '=', 'cruise.id')->whereNull('cruise.deleted_at')->whereIn('cruise_facility.facility_id',$facility)->get();
        if(!$cruise){
            throw new ApiGenericException("Record not found.");
        }
        return $cruise;
    }

    //download sample excel file
	public function downloadSampleCruiseExcelFile()
	{
        if(Auth::user()->user_type == '4' || Auth::user()->user_type == '12'){
            $partner_id = Auth::user()->created_by;
        }elseif(Auth::user()->user_type == '3'){
            $partner_id = Auth::user()->id;
        }

		$excelSheetName = ucwords(str_replace(' ', '', 'sampleCruiseFile'));
		$Columns[] = [
            'Facility'=>'World Cruise Center Parking',
			'Cruise Name' => 'xyz',
			'Cruise Start Date/Time (24 Hrs)' => '03-06-2024 10:04',
            'Cruise End Date/Time (24 Hrs)' => '07-03-2024 23:08',
			'Permissible Parking Start duration (Hrs)' => '2',
			'Permissible Parking End duration (Hrs)' => '3',
            'Description' => '',
            ];

		Excel::create(
			$excelSheetName,
			function ($excel) use ($Columns, $excelSheetName) {

				// Set the spreadsheet title, creator, and description
				$excel->setTitle($excelSheetName);
				//$excel->setCreator('sampleBulkPermit')->setCompany('ParkEngage');
				//$excel->setDescription('Sample File of Bulk Permit Data');
                // Build the spreadsheet, passing in the payments array
				if (isset($Columns) && !empty($Columns)) {
					$excel->sheet(
						'CruiseSchedule',
						function ($sheet) use ($Columns) {
							// $sheet->setColumnFormat(array(
							// 	'N' => 'yyyy-mm-dd',
							// ));
							$sheet->fromArray($Columns, null, 'A1', false, true);
							$sheet->freezeFirstRow('A2');
						}
					);
				} else {
					throw new ApiGenericException('Sorry! No Data Found.');
				}
			}
		)->store('xlsx')->download('xlsx');




	}

    public function importCruiseExcelFile(Request $request)
	{
        if(Auth::user()->user_type == '4' || Auth::user()->user_type == '12'){
            $partner_id = Auth::user()->created_by;
        }elseif(Auth::user()->user_type == '3'){
            $partner_id = Auth::user()->id;
        }
		if ($request->file('input_importfile')) {        
			$inputfile = $request->file('input_importfile');
            
			$ext = $inputfile->getClientOriginalExtension();

			if ($ext == 'xlsx') {
				$fileName = time() . '_cruisedatafile.' . $ext;
				$destination_path = storage_path("import/");
				$inputfile->move($destination_path, $fileName);
				$file = storage_path('import/') . $fileName;
				$data = Excel::load($file)->get();
                $newdata = $data->toArray();
                //dd($data);
                $fileData = [];
				if (!empty($newdata)) {                        
					//cruise name checke here for duplicacy
					foreach ($data as $key => $value) {
						if(!empty($value['facility'])){
							$facilityExist = Facility::where('full_name',$value['facility'])->first();
							if(!isset($facilityExist)){
								throw new ApiGenericException("Facility not found");   
							}	
                            $cruise = Cruise::where('cruise_name',$value['cruise_name'])->first();
                            $cruiseTime = CruiseSchedule::select('start_time','end_time')
                                                        ->where('cruise_id',$cruise['id'])
                                                        ->first();	

                            if( $value['cruise_start_datetime_24_hrs'] == $value['cruise_end_datetime_24_hrs']){
                                throw new ApiGenericException("cruise start date and cruise end date will not be same.Cruise end date always be greater then cruise start date");   
                            }			
				
						}
						
					}
					foreach ($data as $key=>$value) {
						if(!empty($value['facility']) && !empty($value['cruise_name'])  && !empty($value['cruise_start_datetime_24_hrs']) && !empty($value['cruise_end_datetime_24_hrs'])  && !empty($value['permissible_parking_start_duration_hrs']) && !empty($value['permissible_parking_end_duration_hrs'] ) ){
							$cruisedata =[];
							$Cruiseschedule = [];
							$cruise = Cruise::where('cruise_name',$value['cruise_name'])->first();
							if(!$cruise){
								$cruisedata['partner_id'] = $partner_id;
								$cruisedata['cruise_name'] = $value['cruise_name'];
								$cruisedata['is_active'] = 1;
								$cruisedata['slug'] = $value['cruise_name'];
                
                                $cruisedata['start_time'] = $value['permissible_parking_start_duration_hrs'];
                                $cruisedata['end_time'] = $value['permissible_parking_end_duration_hrs'];
								$cruisedata['description'] = isset($value['description'])?$value['description']:'';								
								$cruise = Cruise::create($cruisedata);
							}
							$Cruiseschedule = [];
                            
							$Cruiseschedule['start_time'] = date('Y-m-d H:i:s',strtotime($value['cruise_start_datetime_24_hrs']) ) ;
							$Cruiseschedule['end_time'] = date('Y-m-d H:i:s', strtotime($value['cruise_end_datetime_24_hrs']) );
                            $Cruiseschedule['parking_start_time'] = date('Y-m-d H:i:s', strtotime(- $value['permissible_parking_start_duration_hrs']. 'hours', strtotime($value['cruise_start_datetime_24_hrs'])));
                            $Cruiseschedule['parking_end_time'] = date('Y-m-d H:i:s', strtotime(+ $value['permissible_parking_end_duration_hrs']. 'hours', strtotime($value['cruise_end_datetime_24_hrs'])));
							$Cruiseschedule['cruise_id'] = $cruise->id;
							CruiseSchedule::create($Cruiseschedule);
							$facility = Facility::select('id','full_name')->where('full_name',$value['facility'])->first();
                            
							$facilityExist =  DB::table('cruise_facility')->where(['cruise_id' => $cruise->id, 'facility_id' => $facility['id'] ])->get();
							if(!$facilityExist){
                                // $catilinaFacility = config('parkengage.CATILINA_FACILITY');// BY default map with catilina facility 
                                // $facilityData = [
                                //     ['cruise_id'=> $cruise->id, 'facility_id' => $facility['id'] ],
                                //     ['cruise_id'=> $cruise->id, 'facility_id' => $catilinaFacility ],
                                    
                                // ];
							    DB::table('cruise_facility')->insert(['cruise_id' => $cruise->id, 'facility_id' => $facility['id'] ]);
                               // DB::table('cruise_facility')->insert($facilityData);
							}
						}
					}
				} else {
					unlink(storage_path('import/' . $fileName));
					throw new ApiGenericException("Empty Data in File");
				}
                return 'Data Uploaded Successfully';
			}else {
				unlink(storage_path('import/' . $fileName));
				throw new ApiGenericException("Invalid file format");
					
			}
		}
	}
      
    public function getCruiseSchedule($id)
    {
        $current_date = date('Y-m-d H:i:s');
        $cruise = Cruise::with(['cruseschedule' => function ($query) use ($current_date) {
            $query->where('parking_end_time',">=",$current_date);
        }])->where('is_active',1)->whereNull('deleted_at')->find($id);
        if(!$cruise){            
            throw new ApiGenericException("Cruise not found.");
        }       
        $cruiseFacility = CruiseFacility::where('cruise_id',$cruise->id)->first();
        if($cruiseFacility){
			$facility =  Facility::find($cruiseFacility->facility_id);
			$cruise->facility = $facility;	
		}
        return $cruise;
    }  

    public function setCustomTimezone($facility_id){
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();        
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if($partnerTimezone){
          if($facility->timezone !=''){
            date_default_timezone_set($facility->timezone);
          }else if($partnerTimezone->timezone != ''){
            date_default_timezone_set($partnerTimezone->timezone);
          }          
        }
    }
 
    public function cruiseListAdmin($facility_id)
    {   
		$cruise = Cruise::select('cruise.*')->join('cruise_facility', 'cruise_facility.cruise_id', '=', 'cruise.id')->whereNull('cruise.deleted_at')->whereIn('cruise_facility.facility_id',[$facility_id])->get();
        if(!$cruise){
            throw new ApiGenericException("Record not found.");
        }
        return $cruise;
    }


}
