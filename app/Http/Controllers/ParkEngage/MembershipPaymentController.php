<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use Response;
use App\Models\ParkEngage\MembershipPayment;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use Illuminate\Http\Request;
use App\Services\Image;
use App\Services\Pdf;
use App\Classes\MagicCrypt;

use App\Exceptions\UserNotAuthorized;

class MembershipPaymentController extends Controller
{
   
     protected $request;  
    public function __construct(Request $request)
    {
        $this->request = $request;
    
    }
    
    public function index(Request $request) {

      $membershipPayment = MembershipPayment::with(['service','membershipPlan','user','userMembership','anetTransaction']);
      if ($request->search) {

            $membershipPayment = QueryBuilder::buildSearchQuery($membershipPayment, $request->search, MembershipPayment::$searchFields)
            ->orWhereHas(
                    'service', function ($query) use ($request) {
                        $query
                            ->where('full_name', 'like', "%{$request->search}%")
                            ->orWhere('short_name', 'like', "%{$request->search}%");
                    }
                )->orWhereHas(
                    'membershipPlan', function ($query) use ($request) {
                        $query
                            ->where('name', 'like', "%{$request->search}%");
                    }
                );
        return $membershipPayment->where('user_id', Auth::user()->id)->where('is_success', '1')->orderBy('id', 'Desc')->paginate(20);
            
      }
      return $membershipPayment->where('user_id', Auth::user()->id)->where('is_success', '1')->orderBy('id', 'Desc')->paginate(20);
    }
    

    public function getInvoicePdf($id)
    {
        if (!$id) {
            throw new ApiGenericException('Invalid id. Please check.');
        }
        $membershipPayment = MembershipPayment::with(['service','membershipPlan','user','userMembership','anetTransaction'])->where('user_id',  Auth::user()->id)->where('is_success', '1')->where('id', $id)->first();
        $pdf = (new MembershipPayment())->generateInvoice($membershipPayment, Pdf::class);
        
        return $this->respondWithPdf($pdf);
    }

    public function getInvoiceJpg($id)
    {
        if (!$id) {
            throw new ApiGenericException('Invalid id. Please check.');
        }
        $membershipPayment = MembershipPayment::with(['service','membershipPlan','user','userMembership','anetTransaction'])->where('user_id',  Auth::user()->id)->where('is_success', '1')->where('id', $id)->first();
        $image =(new MembershipPayment())->generateInvoice($membershipPayment, Image::class);
        return $this->respondWithJpg($image);
    }
    
    
    
   public function addPaymentMethod(Request $request)
    {

        $this->setDecryptedPaymentCard($request);

        $paymentType = $request->input('payment_type');
        if (!$paymentType || ($paymentType !== 'credit_card' && $paymentType !== 'bank_account')) {
            throw new ApiGenericException('Must include payment_type with this request, and must be either credit_card or bank_account.');
        }

        if ($paymentType === 'credit_card') {
            /**$validationRules = [
                'name_on_card' => 'string|required',
                'card_number' => 'required|digits_between:13,16',
                'expiration_date' => 'required',
                'security_code' => 'required|digits_between:3,4',
                'zip_code' => 'required'
            ];**/
             $validationRules = [
                'name_on_card' => 'string|required',
                'card_number' => 'required',
                'expiration_date' => 'required',
                'security_code' => 'required',
                'zip_code' => 'required'
            ];
        } else { // bank account
            $validationRules = [
                'name' => 'string|required',
                'account_type' => 'string|required|in:checking,savings,businessChecking',
                'routing' => 'required|digits:9',
                'account_number' => 'required|digits_between:5,17'
            ];
        }
        $this->validate($request, $validationRules);
        $input = $request->all();

        return $paymentType === 'credit_card'
            ? (new MembershipPayment())->addCreditCard($input['card_number'], $input['expiration_date'], $input['security_code'], $input['name_on_card'], $input['zip_code'])
            : (new MembershipPayment())->addBankAccount($input['account_type'], $input['routing'], $input['account_number'], $input['name']);
 
 }
 
  public function deletePaymentMethod($paymentProfileId)
    {
        // Make sure monthly parking account owns the payment profile
        $owns = (new MembershipPayment())->ownsPaymentProfile($paymentProfileId);
        if (!$owns) {
            throw new UserNotAuthorized('Partner does not own this payment method.');
        }

        return (new MembershipPayment())->deleteUserPaymentProfiles($paymentProfileId);
    }

     public function listPaymentMethods(Request $request)
    {
             return (new MembershipPayment())->getPaymentMethods();
    }

    
    public function setDecryptedPaymentCard(Request $request)
    {

        if ($request->card) {
            $nonce = $request->card['nonce'];
            $decryptedNonce = $this->decryptNonce($nonce);

            $cardData = explode(':', $decryptedNonce);
            $zipCode = isset($cardData[4])?$cardData[4]:'';

            $card = array(
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode
                );
            $request->merge(array('card' => $card));
            $this->request = $request;
        }

        if (isset($request->payment_type) && $request->payment_type == "credit_card") {
            $nonce = $request->nonce;
            $decryptedNonce = $this->decryptNonce($nonce);
            $cardData = explode(':', $decryptedNonce);

            $zipCode = isset($cardData[4])?$cardData[4]:'';
            $request->request->add(
                [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode
                    ]
            );

            $this->request = $request;

        }
    }

    public function decryptNonce($nonce)
    {
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        return $mc->decrypt($nonce);
    }
   
 
}
