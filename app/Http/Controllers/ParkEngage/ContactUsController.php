<?php

namespace App\Http\Controllers\ParkEngage;

use App\Models\ParkEngage\ContactUs;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

use Illuminate\Http\Request;
use App\Http\Requests;
use Artisan;
use App\Http\Helpers\QueryBuilder;

class ContactUsController extends Controller
{
   
    
    public function index(Request $request){

      if($request->sort != ''){
        $contactUs = ContactUs::orderBy($request->sort,$request->sortBy);  
      }else{
        $contactUs = ContactUs::orderBy('id', 'Desc');  
      }
      
      if ($request->search) {
            $contactUs = QueryBuilder::buildSearchQuery($contactUs, $request->search, ContactUs::$searchFields);
            return $contactUs->paginate(20);
            
      }
      return $contactUs->paginate(20);

        
    }

   public function store(Request $request) {
      
      $this->validate($request, ContactUs::$validation);  
      
      $data['first_name'] = $request->first_name;
      $data['middle_name'] = $request->middle_name;
      $data['last_name'] = $request->last_name;
      $data['email'] = $request->email;
      $data['phone'] = $request->phone;
      $data['company_name'] = $request->company_name;
      $data['city'] = $request->city;
      $data['state'] = $request->state;
      $data['country'] = $request->country;
      $data['pincode'] = $request->zipcode;
      $data['address1'] = $request->address;      
      
      $result = ContactUs::create($data);
      if($result){
        Artisan::queue('email:contact-us',array('id'=>$result->id));
        return $result;    
      }else{
        return 'Request not saved.';
      }
      
   }
   
	public function getUserDetails($id){
	    return ContactUs::where('id',$id)->first();
	}
   
}
