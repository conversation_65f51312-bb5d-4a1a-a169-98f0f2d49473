<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use Response;
use Hash;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Models\CustomText;
use App\Models\UserPass;
use Excel;
use App\Models\ParkEngage\RevenueBalance;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use App\Classes\ParkengageGateApi;
use Storage;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\ParkEngage\ParkingDeviceTypes;
use App\Models\OauthClient;

use App\Models\Event;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\EventCategoryEvent;
use App\Models\ParkEngage\UserSession;
use App\Models\ParkEngage\State;
use Illuminate\Support\Fluent;
use DB;
use App\Models\ParkEngage\CustomerPortalPermission; #pims-14375
use App\Models\ParkEngage\CustomerPermission;
use App\Models\ParkEngage\PartnerConfiguration;

class CheckinCheckoutController extends Controller
{

	protected $log;
	protected $user;
	protected $partnerPaymentDetails;
	protected $authNet;
	protected $facility;

	const RESERVATION_THRESHOLD_TYPE = 2;
	const TWENTY_FOUR_HOURS = 23;
	const REALTIME_WINDOW   = 2;
	const DEFAULT_VALUE  = 0;
	const DEFAULT_WEB  = "web";
	const DEFAULT_VALUE_STR  = "0";
	const DEFAULT_VALUE_ONE  = 1;
	const DEFAULT_MSG_ONE = 1;
	const DEFAULT_PERCENTAGE_FLAG = 1;
	const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
	const DEFAULT_MSG_TWO = 2;
	const DEFAULT_MSG_THREE = 3;
	const DEFAULT_MSG_FOUR = 4;
	const ADD_EXTRA_DAY_COUNT = 1;
	const COUPON_RATE   = 2;
	const DEFAULT_TIME      = '0000-00-00 00:00:00';
	const  MIN_AVAILABILITY  = 5;
	const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
	const  SOME_SPACE_MSG = "Some spots are available.";
	const  No_SPACE_MSG = "Sold Out At This Time.";
	const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

	const  FACILITY_AVAILABLE = "Facility available for reservation.";
	const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
	const  FACILITY_COMING_SOON = "Facility coming soon.";

	const  SHARE_TICKET_AMOUNT = 0;
	const  SHARE_PASS_AMOUNT = 0;

	const EVENT_THRESHOLD_TYPE = 2;

	public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
	{
		$this->request = $request;
		$this->authNet = $authNet;
		$this->cim = $cim;
		// Use these validation rules if new billing information is being sent through
		$this->billingValidation = PaymentProfile::$creditCardValidation;

		$this->log = $logFactory->setPath('logs/parkengage/touchless-flow')->createLogger('new-touchless-flow');
	}


	public function setDecryptedCard(Request $request)
	{
		if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
			return;
		}
		$key = env('PCI_ENCRYPTION_KEY');
		$mc = new MagicCrypt($key, 256);
		$decryptedNonce = $mc->decrypt($request->nonce);
		$cardData = explode(':', $decryptedNonce);
		$zipCode = isset($cardData[4]) ? $cardData[4] : '';
		$request->request->add(
			[
				'name_on_card' => $cardData[0],
				'card_number' => $cardData[1],
				'expiration_date' => $cardData[2],
				'security_code' => $cardData[3],
				'zip_code' => $zipCode
			]
		);

		$this->request = $request;
	}

	protected function getBillingArray()
	{
		$name = $this->request->name_on_card ?: $this->user->name;
		$zip = $this->request->zip_code ?: false;

		$nameArray = explode(' ', trim($name));

		return [
			'first_name' => reset($nameArray),
			'last_name' => end($nameArray),
			'zip' => $zip,
		];
	}


	protected function checkTicketNumber()
	{
		$ticket = 'PE' . rand(100, 999) . rand(100, 999);
		$isExist = Ticket::where('ticket_number', $ticket)->first();
		if ($isExist) {
			return $ticket = $this->checkTicketNumber();
		}
		return $ticket;
	}



	public function updateRateInformationWithAvailibilty($arrival_time, $length_of_stay, Facility $facility)
	{
		$returnResultArr = array();

		$returnResultArr['coupon_threshold_price'] = 0;
		$returnResultArr['is_coupon_threshold_price_percentage'] = 0;
		$returnResultArr['availability'] = 0;
		$returnResultArr['is_coupon_threshold_applied'] = 0;

		$inventory = new Inventory();
		$date_time_out = Carbon::parse($arrival_time)->addMinutes((number_format($length_of_stay, 2) * 60));

		$realtimeWindow = $facility->realtime_window;
		$realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;

		$timeDifference = date_diff(date_create($arrival_time), Carbon::now());

		$isAvailable = true;

		$thresholdAvailability = self::DEFAULT_VALUE;

		if ($isAvailable == true) {
			//check how many slots does entry and exit time occupies
			//$difference = date_diff(date_create($arrival_time), date_create($date_time_out));
			$difference = date_diff(date_create(date('Y-m-d', strtotime($arrival_time))), date_create(date('Y-m-d', strtotime(($date_time_out)))));

			if ($difference->d > 0) {
				//                $dates   = $inventory->generateArrayOfDates(
				//                '', date($arrival_time), date($date_time_out));

				$dates   = $inventory->generateArrayOfDates(
					($difference->d + self::ADD_EXTRA_DAY_COUNT),
					date('Y-m-d H:i:s', strtotime($arrival_time))
				);

				$dayDifference = $difference->d;

				foreach ($dates as $key => $date) {
					$facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();

					if ($facilityAvailability) {
						$inventory = json_decode($facilityAvailability->availability);

						if ($key == 0) {
							/**
							 * because this is the first day in the dates provided
							 * we should check from each time_slot starting
							 * from the hour provided in the api call
							 */
							$i = date('G', strtotime($arrival_time));
							while ($i <= self::TWENTY_FOUR_HOURS) {
								if (isset($inventory->{$i})) {
									if ($inventory->{$i} < 1) {
										$isAvailable = false;
										break;
									}
									if ($thresholdAvailability > 0) {
										if ($thresholdAvailability > $inventory->{$i}) {
											$thresholdAvailability = $inventory->{$i};
										}
									} else {
										$thresholdAvailability = $inventory->{$i};
									}
								}
								$i++;
							}
						} elseif ($key == $dayDifference) {
							$i = date('G', strtotime($date_time_out));
							$minutes = date('i', strtotime($date_time_out));
							if ($minutes >= 30) {
								$i++;
							}
							/**
							 * because this is the last day in the dates provided
							 * we should check from each time_slot starting
							 * till the hour provided in the api call
							 */
							$j = 0;
							while ($j < $i) {
								if (isset($inventory->{$j})) {
									if ($inventory->{$j} < 1) {
										$isAvailable = false;
										break;
									}
									if ($thresholdAvailability > 0) {
										if ($thresholdAvailability > $inventory->{$j}) {
											$thresholdAvailability = $inventory->{$j};
										}
									} else {
										$thresholdAvailability = $inventory->{$j};
									}
								}
								$j++;
							}
						} else {
							/**
							 * because this could be any day except first and last in
							 * the dates provided we should check from whole day
							 */
							$k = 0;
							while ($k <= self::TWENTY_FOUR_HOURS) {
								if (isset($inventory->{$k})) {
									if ($inventory->{$k} < 1) {
										$isAvailable = false;
										break;
									}
									if ($thresholdAvailability > 0) {
										if ($thresholdAvailability > $inventory->{$k}) {
											$thresholdAvailability = $inventory->{$k};
										}
									} else {
										$thresholdAvailability = $inventory->{$k};
									}
								}
								$k++;
							}
						}
					}
				}
			} else {
				$startingHour = date('G', strtotime($arrival_time));
				$endingHour   = date('G', strtotime($date_time_out));
				$facilityAvailability     = FacilityAvailability::where(
					['facility_id' => $facility->id, 'date' => date('Y-m-d', strtotime($arrival_time))]
				)->first();

				if ($facilityAvailability) {
					$availability = json_decode($facilityAvailability->availability, true);

					while ($startingHour <= $endingHour) {
						if (isset($availability[$startingHour])) {
							if (($availability[$startingHour] < 1)) {
								$isAvailable = false;
							}
							if ($thresholdAvailability > 0) {
								if ($thresholdAvailability > $availability[$startingHour]) {
									$thresholdAvailability = $availability[$startingHour];
								}
							} else {
								$thresholdAvailability = $availability[$startingHour];
							}
						}
						$startingHour++;
					}
				}
			}

			if ($thresholdAvailability < self::DEFAULT_VALUE) {
				$thresholdAvailability = self::DEFAULT_VALUE;
			}

			if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {

				$dateIn = date('Y-m-d', strtotime($arrival_time));
				$facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();

				if ($facilityAvailability) {
					//$availabilities = json_decode($facilityAvailability->availability, true);

					if ($thresholdAvailability >= 0) {

						$couponThresholdsNew = $facility->facilityCouponThreshold;
						$couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');

						$thresholds       = array();
						if ($couponThresholdsNew) {
							$thresholdCounter = self::DEFAULT_VALUE;
							foreach ($couponThresholdsNew as $couponThreshold) {

								if ($couponThreshold->uptick_type !== 'deleted') {
									$thresholds[$thresholdCounter] =
										['threshold' => $couponThreshold->threshold, 'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
									$thresholdCounter++;
								}
							}
						}
						$thresholdPrice = 0;
						$currentAvailability = $thresholdAvailability;
						foreach ($thresholds as $key => $threshold) {
							if ($thresholdAvailability <= $threshold['threshold']) {
								if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
									if ($threshold['uptick_type'] == 'price') {
										$thresholdPrice = $threshold['uptick'];

										$returnResultArr['is_coupon_threshold_applied'] = 1;
										break;
									} else if ($threshold['uptick_type'] == 'percentage') {
										$thresholdPrice =  $threshold['uptick'];
										$returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG;

										$returnResultArr['is_coupon_threshold_applied'] = 1;
										break;
									}
								}
							}
						}
						$returnResultArr['coupon_threshold_price'] = $thresholdPrice;

						$returnResultArr['availability'] = $currentAvailability;
					}
				}
			}
		}

		return $returnResultArr;
	}







	public function getAllFacilityGates(Request $request)
	{
		$gate = Gate::select('gate', 'gate_name', 'gate_type')->where('facility_id', $request->facility_id)->orderBy('gate_name', "ASC")->get();
		return $gate;
	}


	public function ticketCheckinCheckout(Request $request)
	{

		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Sorry,Invalid garage.');
		}
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();

		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}
		//check gate api
		$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
		if ($gateStatus == "true") {
		} else {
			throw new ApiGenericException($gateStatus);
		}

		if (isset($gate) && $gate->gate_type == "entry") {
			$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
			$userPass = [];
			if (!$reservation) {

				$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereNull('cancelled_at')->first();
				if ($reservation) {
					if ($reservation->is_ticket == '1') {
						throw new ApiGenericException('You have already checked-in.');
					} else if ($reservation->is_ticket == '2') {
						throw new ApiGenericException('You have already checkout.');
					} else {
					}
				}
				$userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days', '>', 0)->orderBy('id', 'DESC')->first();
				if (!$userPass) {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}

				$reservation = Reservation::with('user')->where("user_pass_id", $userPass->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->first();
				if ($reservation) {
					if ($reservation->is_ticket == '1') {
						throw new ApiGenericException('You have already checked-in.');
					} else if ($reservation->is_ticket == '2') {
						throw new ApiGenericException('You have already checkout.');
					} else {
					}
					$userPass = [];
					$user = $reservation->user;
				} else {
					$user = $userPass->user;
				}
			} else {
				$user = $reservation->user;
			}
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}

			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber();
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['partner_id'] = $facility->owner_id;
			if ($reservation) {
				$data['reservation_id'] = $reservation->id;
				$data['check_in_datetime'] = $reservation->start_timestamp;
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['total'] = $reservation->total;
				$data['grand_total'] = $reservation->total;
				$data['length'] = $reservation->length;
				$data['user_pass_id'] = $reservation->user_pass_id;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$reservation->is_ticket = '1';
				$reservation->save();
			}

			if (count($userPass) > 0) {
				$data['check_in_datetime'] = date('Y-m-d H:i:s');
				$data['checkout_datetime'] = date('Y-m-d H:i:s');
				$data['user_pass_id'] = $userPass->id;

				$userPass->consume_days = $userPass->consume_days + 1;
				$userPass->remaining_days = $userPass->remaining_days - 1;
				$userPass->save();
			}

			$result = Ticket::create($data);
			$facilityName = ucwords($facility->full_name);
			if ($reservation) {
				Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			} else {
				Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
				$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
				$this->customeReplySms($msg, $user->phone);
			}

			$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


			//check gate api
			$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}

			return "Welcome to $facilityName. #$result->ticket_number.";
		} else if (isset($gate) && $gate->gate_type == "exit") {
			$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')/*->where('is_ticket', '0')*/->first();
			if (!$reservation) {
				$userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))/*->where('remaining_days','>',0)*/->orderBy('id', 'DESC')->first();
				if (!$userPass) {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				$passTicket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkout', '0')->orderBy('id', 'DESC')->first();
				if (!$passTicket) {

					if ($userPass->remaining_days == 0) {
						throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
					}
					$data['user_id'] = $userPass->user_id;
					$data['facility_id'] = $request->facility_id;
					$data['ticket_number'] = $this->checkTicketNumber();
					//$checkout['checkin_time'] = date('Y-m-d H:i:s');
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					$data['check_in_datetime'] = date("Y-m-d H:i:s");
					$data['checkout_datetime'] = date("Y-m-d H:i:s");
					$data['estimated_checkout'] = date("Y-m-d H:i:s");
					$data['checkout_gate'] = $request->gate_id;
					$data['user_pass_id'] = $userPass->id;
					$data['is_checkout'] = 1;
					$data['checkout_time'] = date("Y-m-d H:i:s");
					$data['checkout_without_checkin'] = '1';
					Ticket::create($data);

					$userPass->consume_days = $userPass->consume_days + 1;
					$userPass->remaining_days = $userPass->remaining_days - 1;
					$userPass->save();
				} else {
					$passTicket->is_checkout = '1';
					$data['checkout_gate'] = $request->gate_id;
					$passTicket->checkout_time = date("Y-m-d H:i:s");
					$passTicket->save();
				}
				$user = $userPass->user;

				/*$ticket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
            if(!$ticket){
              throw new ApiGenericException('Sorry, No checkin found against this mobile number.');
            }*/

				$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
				$msg = "Thank you for visiting " . $facilityName . ".";
				$this->customeReplySms($msg, $user->phone);

				//check gate api
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
				return $msg;
			} else {

				if ($reservation->is_ticket == '2') {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				$user = $reservation->user;
				$ticket = Ticket::where('reservation_id', $reservation->id)->first();
				if (!$ticket) {
					$data['user_id'] = $user->id;
					$data['checkout_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					//$data['is_checkin'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber();
					//$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					if ($reservation) {
						$data['reservation_id'] = $reservation->id;
						$data['check_in_datetime'] = $reservation->start_timestamp;
						$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
						$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
						$data['total'] = $reservation->total;
						$data['grand_total'] = $reservation->total;
						$data['length'] = $reservation->length;
						$data['user_pass_id'] = $reservation->user_pass_id;
						$data['is_checkout'] = '1';
						$data['checkout_time'] = date('Y-m-d H:i:s');
						$checkout['checkout_without_checkin'] = '1';

						Ticket::create($data);
						$reservation->is_ticket = '2';
						$reservation->save();
						$facilityName = ucwords($facility->full_name);
						$msg = "Thank you for visiting " . $facilityName . ".";

						//check gate api
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
						$this->customeReplySms($msg, $user->phone);
						//return "Success";

						return $msg;
					}
				} else if ($ticket->is_checkout == '0') {
					$facilityName = ucwords($facility->full_name);
					$ticket->is_checkout = '1';
					$ticket->checkout_time = date('Y-m-d H:i:s');
					$ticket->checkout_gate = $request->gate_id;
					$ticket->save();

					if ($reservation) {
						$reservation->is_ticket = '2';
						$reservation->save();
					}
					$msg = "Thank you for visiting " . $facilityName . ".";
					$this->customeReplySms($msg, $user->phone);
					//check gate api
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
					return $msg;
				} else {
					$msg = "Sorry, No prepaid booking or pass found.";
					$this->customeReplySms($msg, $user->phone);
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				return "Success";
			}/*else{
          throw new ApiGenericException("Invalid request.");
        }*/
			return "Success";
		}
		return "Success";
	}


	public function ticketSessionCheckinCheckout(Request $request)
	{
		$this->log->info("Request received --" . json_encode($request));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}
		$facilityName =  ucwords($facility->full_name);
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}

		$user = User::where('session_id', $request->session_id)->first();
		if (!$user) {
			$user = User::create(
				[
					'name' => '',
					'email' => '',
					'phone' => '',
					'password' => Hash::make(str_random(60)),
					'anon' => true,
					'user_type' => '5',
					'created_by' => $facility->owner_id,
					'session_id' => $request->session_id,
				]
			);
		}

		if (isset($gate) && $gate->gate_type == "entry") {

			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}

			if ($facility->check_vehicle_enabled == '1') {
				$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber();
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['check_in_datetime'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['vp_device_checkin'] = '1';
			$data['partner_id'] = $facility->owner_id;

			$result = Ticket::create($data);

			if (isset($request->payment_details)) {
				$ticket = Ticket::where('id', $result->id)->first();
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "VP6800 Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = $request->payment_details['processorReference'];
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
				$ticket->save();
			}
			$this->log->info("New VP6800 checkin done --" . json_encode($result));

			if ($result) {

				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				return "Welcome to $facilityName. #$result->ticket_number.";
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}

		if (isset($gate) && $gate->gate_type == "exit") {
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if (!$ticket) {
				throw new ApiGenericException('No checkin found for this user.');
			}
			if ($facility->check_vehicle_enabled == '1') {
				$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$diff_in_hours = $arrival_time->diffInRealHours($from);
			if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
				$diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
			}
			$ticket->checkout_gate = $request->gate_id;
			$ticket->is_checkout = 1;
			$ticket->length = $diff_in_hours;
			$ticket->checkout_time = date('Y-m-d H:i:s');
			if (isset($request->payment_details)) {
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = $request->payment_details['processorReference'];
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
			}

			$result = $ticket->save();
			$this->log->info("checkout VP6800 done --" . json_encode($ticket));
			if ($result) {
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				return "Thank you for visiting " . $facilityName . ".";
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
	}


	public function demoTicketCheckinCheckout(Request $request)
	{

		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Sorry,Invalid garage.');
		}
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();

		//check gate api
		/*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
        if($gateStatus == "true"){}else{
          throw new ApiGenericException($gateStatus);
        }*/

		if (isset($gate) && $gate->gate_type == "entry") {
			$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
			if (!$reservation) {

				$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereNull('cancelled_at')->first();
				if ($reservation) {
					if ($reservation->is_ticket == '1') {
						throw new ApiGenericException('You have already checked-in.');
					} else if ($reservation->is_ticket == '2') {
						throw new ApiGenericException('You have already checkout.');
					} else {
					}
				}
				$userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days', '>', 0)->orderBy('id', 'DESC')->first();
				if (!$userPass) {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				$user = $userPass->user;
			} else {
				$user = $reservation->user;
			}
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}

			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber();
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['partner_id'] = $facility->owner_id;
			if ($reservation) {
				$data['reservation_id'] = $reservation->id;
				$data['check_in_datetime'] = $reservation->start_timestamp;
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['total'] = $reservation->total;
				$data['grand_total'] = $reservation->total;
				$data['length'] = $reservation->length;
				$data['user_pass_id'] = $reservation->user_pass_id;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$reservation->is_ticket = '1';
				$reservation->save();
			}

			if (isset($userPass)) {
				$data['check_in_datetime'] = date('Y-m-d H:i:s');
				$data['checkout_datetime'] = date('Y-m-d H:i:s');
				$data['user_pass_id'] = $userPass->id;

				$userPass->consume_days = $userPass->consume_days + 1;
				$userPass->remaining_days = $userPass->remaining_days - 1;
				$userPass->save();
			}

			$result = Ticket::create($data);
			$facilityName = ucwords($facility->full_name);
			if ($reservation) {
				Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			} else {
				Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
				$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
				$this->customeReplySms($msg, $user->phone);
			}

			$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


			//check gate api
			/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
        if($gateStatus == "true"){}else{
          throw new ApiGenericException($gateStatus);
        }*/

			return "Welcome to $facilityName. #$result->ticket_number.";
		} else if (isset($gate) && $gate->gate_type == "exit") {
			$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')/*->where('is_ticket', '0')*/->first();
			if (!$reservation) {
				$userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))/*->where('remaining_days','>',0)*/->orderBy('id', 'DESC')->first();
				if (!$userPass) {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				$passTicket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkout', '0')->orderBy('id', 'DESC')->first();
				if (!$passTicket) {

					if ($userPass->remaining_days == 0) {
						throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
					}
					$data['user_id'] = $userPass->user_id;
					$data['facility_id'] = $request->facility_id;
					$data['ticket_number'] = $this->checkTicketNumber();
					//$checkout['checkin_time'] = date('Y-m-d H:i:s');
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					$data['check_in_datetime'] = date("Y-m-d H:i:s");
					$data['checkout_datetime'] = date("Y-m-d H:i:s");
					$data['estimated_checkout'] = date("Y-m-d H:i:s");
					$data['checkout_gate'] = $request->gate_id;
					$data['user_pass_id'] = $userPass->id;
					$data['is_checkout'] = 1;
					$data['checkout_time'] = date("Y-m-d H:i:s");
					$data['checkout_without_checkin'] = '1';
					Ticket::create($data);

					$userPass->consume_days = $userPass->consume_days + 1;
					$userPass->remaining_days = $userPass->remaining_days - 1;
					$userPass->save();
				} else {
					$passTicket->is_checkout = '1';
					$data['checkout_gate'] = $request->gate_id;
					$passTicket->checkout_time = date("Y-m-d H:i:s");
					$passTicket->save();
				}
				$user = $userPass->user;

				/*$ticket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
            if(!$ticket){
              throw new ApiGenericException('Sorry, No checkin found against this mobile number.');
            }*/

				$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
				$msg = "Thank you for visiting " . $facilityName . ".";
				$this->customeReplySms($msg, $user->phone);

				//check gate api
				/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }*/
				return $msg;
			} else {

				if ($reservation->is_ticket == '2') {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				$user = $reservation->user;
				$ticket = Ticket::where('reservation_id', $reservation->id)->first();
				if (!$ticket) {
					$data['user_id'] = $user->id;
					$data['checkout_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					//$data['is_checkin'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber();
					//$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					if ($reservation) {
						$data['reservation_id'] = $reservation->id;
						$data['check_in_datetime'] = $reservation->start_timestamp;
						$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
						$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
						$data['total'] = $reservation->total;
						$data['grand_total'] = $reservation->total;
						$data['length'] = $reservation->length;
						$data['user_pass_id'] = $reservation->user_pass_id;
						$data['is_checkout'] = '1';
						$data['checkout_time'] = date('Y-m-d H:i:s');
						$checkout['checkout_without_checkin'] = '1';

						Ticket::create($data);
						$reservation->is_ticket = '2';
						$reservation->save();
						$facilityName = ucwords($facility->full_name);
						$msg = "Thank you for visiting " . $facilityName . ".";

						//check gate api
						/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                if($gateStatus == "true"){}else{
                  throw new ApiGenericException($gateStatus);
                }*/
						$this->customeReplySms($msg, $user->phone);
						//return "Success";

						return $msg;
					}
				} else if ($ticket->is_checkout == '0') {
					$facilityName = ucwords($facility->full_name);
					$ticket->is_checkout = '1';
					$ticket->checkout_time = date('Y-m-d H:i:s');
					$ticket->checkout_gate = $request->gate_id;
					$ticket->save();

					if ($reservation) {
						$reservation->is_ticket = '2';
						$reservation->save();
					}
					$msg = "Thank you for visiting " . $facilityName . ".";
					$this->customeReplySms($msg, $user->phone);
					//check gate api
					/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
              if($gateStatus == "true"){}else{
                throw new ApiGenericException($gateStatus);
              }*/
					return $msg;
				} else {
					$msg = "Sorry, No prepaid booking or pass found.";
					$this->customeReplySms($msg, $user->phone);
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				return "Success";
			}/*else{
          throw new ApiGenericException("Invalid request.");
        }*/
			return "Success";
		}
		return "Success";
	}


	public function demoTicketSessionCheckinCheckout(Request $request)
	{
		$this->log->info("Request received --" . json_encode($request));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}
		$facilityName =  ucwords($facility->full_name);
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			throw new ApiGenericException("Invalid gate.");
		}
		$user = User::where('session_id', $request->session_id)->first();
		if (!$user) {
			$user = User::create(
				[
					'name' => '',
					'email' => '',
					'phone' => '',
					'password' => Hash::make(str_random(60)),
					'anon' => true,
					'user_type' => '5',
					'created_by' => $facility->owner_id,
					'session_id' => $request->session_id,
				]
			);
		}

		if (isset($gate) && $gate->gate_type == "entry") {

			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}
			/*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }*/
			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber();
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['check_in_datetime'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['vp_device_checkin'] = '1';
			$data['partner_id'] = $facility->owner_id;

			$result = Ticket::create($data);

			if (isset($request->payment_details)) {
				$ticket = Ticket::where('id', $result->id)->first();
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "VP6800 Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = $request->payment_details['processorReference'];
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
				$ticket->save();
			}
			$this->log->info("New VP6800 checkin done --" . json_encode($result));

			if ($result) {

				//check gate api
				/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }*/
				return "Welcome to $facilityName. #$result->ticket_number.";
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}

		if (isset($gate) && $gate->gate_type == "exit") {
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if (!$ticket) {
				throw new ApiGenericException('No checkin found for this user.');
			}
			/*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }*/
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$diff_in_hours = $arrival_time->diffInRealHours($from);
			if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
				$diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
			}
			$ticket->checkout_gate = $request->gate_id;
			$ticket->is_checkout = 1;
			$ticket->length = $diff_in_hours;
			$ticket->checkout_time = date('Y-m-d H:i:s');
			if (isset($request->payment_details)) {
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = $request->payment_details['processorReference'];
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
			}

			$result = $ticket->save();
			$this->log->info("checkout VP6800 done --" . json_encode($ticket));
			if ($result) {
				//check gate api
				/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
            if($gateStatus == "true"){}else{
              throw new ApiGenericException($gateStatus);
            }  */
				return "Thank you for visiting " . $facilityName . ".";
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
	}

	// device config code here

	public  function getDeviceConfig(Request $request)
	{

		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new ApiGenericException('Invalid partner.');
			}
		}

		if ($request->device_serial_number != '') {

			$parkingdevice = ParkingDevice::with(['gate.adam', 'parkingDeviceRabbitmq'])->where('serial_number', $request->device_serial_number)->where('is_active', '1')->first();
			if (isset($parkingdevice->is_active)) {
				$facility = Facility::with('FacilityPaymentDetails')->where('id', $parkingdevice->facility_id)->first();
				$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
				$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
				$diff_in_hours = $arrival_time->diffInRealHours($from);
				if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
					$diff_in_hours = 24;
				}

				$isMember = 0;
				$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember);
				if ($rate['price'] > 0) {
					$rate['price'] = number_format($rate['facility']['base_rate'] + $rate['facility']['processing_fee'], 2);
				}
				$rate['facility']['polling_interval'] = (int) $rate['facility']['polling_interval'];

				// Get Transient URL from PartnerConfiguration partner wise
				$PartnerConfiguration = PartnerConfiguration::where(['key' => 'TRANSIENT_URL', 'partner_id' => $facility->owner_id])->first();

				$today = date("Y-m-d");
				//$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
				$facility_id = $facility->id;
				$sqlQuery = "SELECT e.*
                    FROM events as e
                    inner join event_facility as ef on ef.event_id= e.id        
                    WHERE e.partner_id IN ({$facility->owner_id}) AND date(e.start_time) = '" . $today . "' AND date(e.end_time) >= '" . $today . "' AND e.deleted_at is null AND ef.facility_id IN (" . $facility_id . ") AND e.is_active='1' AND e.deleted_at is NULL";

				$results =  DB::select($sqlQuery);
				if (count($results) > 0) {
					$todayEvent = new Fluent($results);
					$todayEvent = $todayEvent[0];
					if (!$todayEvent) {
						$rate['is_event_started'] =  '0';
					}
					if ($todayEvent) {
						$parkingNowTime = date("Y-m-d H:i:s");
						$parkingStartTime = $todayEvent->parking_start_time;
						$parkingEndTime = $todayEvent->parking_end_time;
						if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {

							if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
								$rate['price'] = $todayEvent->driveup_event_rate;
								$taxRate = $facility->getTaxRate($rate);          // to get tax price                
								$rate['event_rate'] =  number_format($todayEvent->driveup_event_rate + $facility->drive_up_processing_fee + $taxRate, 2);
							} else {
								$rate['price'] = $facility->base_rate;
								$taxRate = $facility->getTaxRate($rate);          // to get tax price                
								$rate['event_rate'] =  number_format($facility->base_rate + $facility->processing_fee + $taxRate, 2);
							}
							$rate['event_id'] 	= $todayEvent->id;
							$rate['title'] 		= $todayEvent->title;
							$rate['is_event_started'] =  '1';
						} else {
							$rate['is_event_started'] =  '0';
						}
					}
				} else {
					$rate['is_event_started'] =  '0';
				}

				$data['rate'] = $rate;
				$data['transient_url'] = isset($PartnerConfiguration->value) ?  $PartnerConfiguration->value . '/' . $facility->garage_code : '';
				$gate = [];
				if ($facility->is_gated_facility == '0') {
					if ($parkingdevice->gate == '' || $parkingdevice->gate == null) {
						$parkingdevice = ParkingDevice::with(['parkingDeviceRabbitmq'])->where('serial_number', $request->device_serial_number)->where('partner_id', $facility->owner_id)->first();
						$gate['gate_type'] = 'gate_type';
						$gate['gate'] = '0';
						$parkingdevice['gate'] = $gate;
					}
				}

				if (isset($parkingdevice->gate->adam->id)) {
					if ($parkingdevice->gate->adam->local_ip != '' && $parkingdevice->gate->adam->local_port_number != '') {
						$parkingdevice->gate->adam->ip = $parkingdevice->gate->adam->local_ip;
						$parkingdevice->gate->adam->port_number = $parkingdevice->gate->adam->local_port_number;
					}
				}

				$data['parkindata'] = $parkingdevice;
				$data['states'] = State::all();
				$data['mid'] = (isset($facility->FacilityPaymentDetails->datacap_mid) && $facility->FacilityPaymentDetails->datacap_mid != '') ? $facility->facilityPaymentDetails->datacap_mid : $facility->mid;
				$oauthClient = OauthClient::where("partner_id", $facility->owner_id)->first();
				if ($oauthClient) {
					$data['secret'] = $oauthClient->secret;
				}
				#pims-14375 start
				// Add Customer Permissions
				$customerPermissions = CustomerPermission::where("partner_id", $facility->owner_id)
					->whereNull('rm_id')
					->whereNull('subordiante_id')
					->pluck('id');

				$menu = CustomerPortalPermission::where("partner_id", $facility->owner_id)
					->whereNull('rm_id')
					->whereNull('subordiante_id')
					->whereIn('customer_permission_id', $customerPermissions)
					->whereIn('display_name', config('parkengage.PERMISSION_START_DATE_TRANS'))
					->orderBy('list_order', 'ASC')
					->get();
				$data['customer_start_date_permission'] = 0;
				$data['is_fast_track_enabled'] = 0;
				if ($menu) {
					foreach ($menu as $key => $val) {
						if (isset($val->name) && $val->name == 'Display Desired Start Date - Ungated') {
							$data['customer_start_date_permission'] = 1;
							#pims-14375 end
						}
						if (isset($val->name) && $val->name == 'Enable Fast-Track Checkin') {
							$data['is_fast_track_enabled'] = 1;
						}
					}
				}
				return $data;
			} else {
				throw new ApiGenericException('This Device is not active, Please contact with admin');
			}
		}
	}






	public function getSessionTicketPaymentDetails(Request $request)
	{

		$user = User::where("session_id", $request->session_id)->first();
		if (!$user) {
			throw new ApiGenericException('Invalid user.');
		}
		$checkinData = Ticket::where("user_id", $user->id)->orderBy("id", "DESC")->first();
		$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
		$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
		$diff_in_hours = $arrival_time->diffInRealHours($from);
		if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
			$diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
		}
		$facility = Facility::find($checkinData->facility_id);
		$isMember = 0;
		$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE, $isMember);
		if ($rate == false) {
			throw new ApiGenericException('Garage is not available.');
		}
		$data = [];
		$data['session_id'] = $request->session_id;
		$data['rate'] = $rate['price'];
		$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
		$endDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
		$data['diff_in_days'] = $startDate->diffInDays($endDate);
		$data['diff_in_hours'] = $startDate->copy()->addDays($data['diff_in_days'])->diffInRealHours($endDate);
		$data['diff_in_minutes'] = $startDate->copy()->addDays($data['diff_in_days'])->addHours($data['diff_in_hours'])->diffInRealMinutes($endDate);

		return $data;
	}

	public function generateBarcodeJpgNew($encrypt)
	{
		$html = $this->generateBarcodeHtml($encrypt);

		$image = app()->make(Image::class);
		$image->setOption('width', '700');
		return $image->getOutputFromHtmlString($html);
	}

	public function generateBarcodeHtml($encrypt)
	{
		return '<img src="data:image/png;base64,{{\DNS2D::getBarcodePNG($encrypt, "QRCODE", "200","200")}}"  align="center" border="0" alt="barcode" style="text-decoration: none; -ms-interpolation-mode: bicubic; height: auto; border: 0;"/>';
	}


	public function customeReplySms($msg, $phone, $imageURL = '')
	{

		try {
			if ($phone == '') {
				return "success";
			}
			$this->log->info("sms about to send");
			$accountSid = env('TWILIO_ACCOUNT_SID');
			$authToken  = env('TWILIO_AUTH_TOKEN');
			$client = new Client($accountSid, $authToken);
			try {

				/*$imageBarcode = $this->generateBarcodeJpgNew("111");

              $imageBarcodeFileName = str_random(10) . '_qrcode.png';
            Storage::put($imageBarcodeFileName, $imageBarcode);
              $imageBarcodeFileName = str_random(10) . '_qrcode.png';
              Storage::put($imageBarcodeFileName, $imageBarcode);
              dd($imageBarcodeFileName, $imageBarcode);
              $data['bar_image_path'] = $imageBarcodeFileName;*/

				// Use the client to do fun stuff like send text messages!
				$client->messages->create(
					// the number you'd like to send the message to
					$phone,
					array(
						// A Twilio phone number you purchased at twilio.com/console
						'from' => env('TWILIO_PHONE'),
						// the body of the text message you'd like to send
						//'body' => "Fine"
						'body' => "$msg",
						//'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)

					)
				);
				$this->log->info("Message : {$msg} sent to $phone");
				return "success";
			} catch (RestException $e) {
				//echo "Error: " . $e->getMessage();
				$this->log->error($e->getMessage());
				return "success";
			}
		} catch (RestException $e) {
			//echo "Error: " . $e->getMessage();
			$this->log->error($e->getMessage());
			return "success";
		}
	}


	public function qrImage($image)
	{
		$file = Storage::disk('local')->get($image) ?: null;
		// create response and add encoded image data
		$response = Response::make($file);
		// getting content type e.g image/jpeg
		$file_extension = mime_content_type(storage_path("app/" . $image));
		// set content-type
		$response->header('Content-Type', $file_extension);
		// output
		return $response;
	}



	public function isParkEngageGateAvailable($facility_id, $gate, $phone = '')
	{

		$facility = Facility::where('id', $facility_id)->first();

		$gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

		//check third party gate API
		$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
		if ($gateDetails) {
			if ($gateDetails->host != '') {
				$params = ['gate_id' => $gate->gate];
				$this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
				$response = ParkengageGateApi::isvehicleAvailable($params, $gateDetails->host);
				$this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response" . json_encode($response));
				if ($response['success'] == false) {
					$msg = "The system is not currently available. Please try again later.";
					return $msg;
				}
				if (isset($response['data'][0]) && $response['data'][0] == "true") {
					return true;
					/*$cmd_params = ['gate_id'=>$gate->gate];
                    $this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params". json_encode($cmd_params));
                    $command_response = ParkengageGateApi::openGate($cmd_params, $gateDetails->host);
                    $this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response". json_encode($command_response));
                    if($command_response['success'] == true){
                        if($command_response['data'][0] == "true"){
                            return true;
                        }else{
                            $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                            return $msg;                            
                        }                        
                    }else{
                        $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                        return $msg; 
                    }*/
				} else {

					$msg = "Your vehicle must be in the garage in front of the " . ucwords($gate->gate_type) . " gate.";
					return $msg;
				}
			}
		}
	}

	public function isParkEngageGateOpen($facility_id, $gate, $phone = '')
	{

		$facility = Facility::where('id', $facility_id)->first();

		$gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

		//check third party gate API
		$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
		if ($gateDetails) {
			if ($gateDetails->host != '') {
				$params = ['gate_id' => $gate->gate];
				$this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
				/*$response = ParkengageGateApi::isvehicleAvailable($params, $gateDetails->host);
                $this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response". json_encode($response));
                if($response['success'] == false){
                    $msg = "The system is not currently available. Please try again later.";
                    return $msg; 
                }*/
				//if(isset($response['data'][0]) && $response['data'][0] == "true"){
				$cmd_params = ['gate_id' => $gate->gate];
				$this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params" . json_encode($cmd_params));
				$command_response = ParkengageGateApi::openGate($cmd_params, $gateDetails->host);
				$this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response" . json_encode($command_response));
				if ($command_response['success'] == true) {
					if ($command_response['data'][0] == "true") {
						return true;
					} else {
						$msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
						return $msg;
					}
				} else {
					$msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
					return $msg;
				}
				/*}else{

                    $msg = "Your vehicle must be in the garage in front of the ".ucwords($gate->gate_type)." gate.";
                    return $msg; 
                }*/
			}
		}
	}

	public function mapcoTicketCheckinCheckout(Request $request)
	{

		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Sorry,Invalid garage.');
		}
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();

		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}
		//check gate api
		/*$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
        if($gateStatus == "true"){}else{
          throw new ApiGenericException($gateStatus);
        }*/
		if (isset($gate) && $gate->gate_type == "entry") {
			$qrCode = MapcoQrcode::with(['reservation.user', 'event', 'eventCategory.eventCategoryEvent.event'])->where("qrcode", $request->ticket_id)->first();
			if (!$qrCode) {
				throw new ApiGenericException('Sorry, No prepaid booking found.');
			} else {
				if ($qrCode->remain_usage == '0' || $qrCode->remain_usage == 0) {
					throw new ApiGenericException('You have already checked-in.');
				}
				if (!isset($qrCode->reservation)) {
					throw new ApiGenericException('Sorry, No prepaid booking found.');
				}
				if ($qrCode->reservation->cancelled_at != '') {
					throw new ApiGenericException('Sorry, Your booking has been cancelled.');
				}
				$today = date("Y-m-d");
				if ($qrCode->event_id != '0') {
					$event = Event::whereDate("start_time", '<=', $today)->whereDate("end_time", '>=', $today)->where('id', $qrCode->event_id)->first();
					if (!$event) {
						throw new ApiGenericException('Sorry, No event found.');
					}
					$event_id = $qrCode->event_id;
				}
				if ($qrCode->event_id == '0' && ($qrCode->event_category_id != '0' || $qrCode->event_category_id != '')) {
					//$today = date("Y-m-d");
					//$today = "2022-12-07";
					$eventCategoryEvent = EventCategoryEvent::leftJoin('events', 'events.id', '=', 'event_category_events.event_id')
						->select('events.id', 'events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode->event_category_id)->first();
					if (!isset($eventCategoryEvent->start_time)) {
						throw new ApiGenericException('Sorry, No event found.');
					}
					$event_id = $eventCategoryEvent->id;
				}

				$user = $qrCode->reservation->user;


				$data['user_id'] = $user->id;
				$data['checkin_gate'] = $request->gate_id;
				$data['facility_id'] = $request->facility_id;
				$data['is_checkin'] = 1;
				$data['ticket_number'] = $this->checkTicketNumber();
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['ticket_security_code'] = rand(1000, 9999);
				$data['partner_id'] = $facility->owner_id;
				$data['reservation_id'] = $qrCode->reservation_id;
				$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
				$data['check_in_datetime'] = $qrCode->reservation->start_timestamp;
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($qrCode->reservation->start_timestamp->addHour($qrCode->reservation->length)->subSeconds(1)));
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($qrCode->reservation->start_timestamp->addHour($qrCode->reservation->length)->subSeconds(1)));
				$data['total'] = $qrCode->reservation->total;
				$data['grand_total'] = $qrCode->reservation->total;
				$data['length'] = $qrCode->reservation->length;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['event_id'] = $event_id;
				$result = Ticket::create($data);

				$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
				$qrCode->save();

				//$reservation->is_ticket = '1';
				//$reservation->save();            

			}

			$facilityName = ucwords($facility->full_name);

			Artisan::queue('email:touchless-parking-mapco-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			//$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
			//$this->customeReplySms($msg, $user->phone);

			$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


			//check gate api
			/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
        if($gateStatus == "true"){}else{
          throw new ApiGenericException($gateStatus);
        }*/

			return "Welcome to $facilityName. #$result->ticket_number.";
		} elseif (isset($gate) && $gate->gate_type == "exit") {

			$qrCode = MapcoQrcode::with(['reservation.user', 'event', 'eventCategory.eventCategoryEvent.event'])->where("qrcode", $request->ticket_id)->first();
			if (!$qrCode) {
				throw new ApiGenericException('Sorry, No prepaid booking found.');
			}
			$ticket = Ticket::where('reservation_id', $qrCode->reservation->id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
			if (!$ticket) {
				throw new ApiGenericException('Sorry, No checkin found against booking.');
			}
			if ($ticket->is_checkout == '1') {
				throw new ApiGenericException('Sorry, You have already checked out.');
			}

			$ticket->is_checkout = '1';
			$ticket->checkout_gate = $request->gate_id;
			$ticket->checkout_time = date("Y-m-d H:i:s");
			$ticket->save();

			$user = $qrCode->reservation->user;
			$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
			$msg = "Thank you for visiting " . $facilityName . ".";
			$this->customeReplySms($msg, $user->phone);

			//check gate api
			/*$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
          if($gateStatus == "true"){}else{
            throw new ApiGenericException($gateStatus);
          }*/
			return $msg;
		} else {
			throw new ApiGenericException('Sorry,Invalid garage or ticket details.');
		}
		return "Success";
	}


	public function zooTicketCheckinCheckout(Request $request)
	{

		$this->log->info("zooTicketCheckinCheckout Request received --" . json_encode($request->all()));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Sorry,Invalid garage.');
		}
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();

		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}
		//check gate api
		if ($facility->check_vehicle_enabled == '1') {
			$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}
		}

		if (isset($gate) && $gate->gate_type == "entry") {

			if ($facility->facility_booking_type == '1') {
				throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');
			}
			$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
			$userPass = [];
			if (!$reservation) {

				$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereNull('cancelled_at')->first();
				if ($reservation) {
					if ($reservation->is_ticket == '1') {
						throw new ApiGenericException('You have already checked-in.');
					} else if ($reservation->is_ticket == '2') {
						throw new ApiGenericException('You have already checkout.');
					} else {
					}
				}


				/*$userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days','>',0)->orderBy('id', 'DESC')->first();
          if(!$userPass){
            throw new ApiGenericException('Sorry, No prepaid booking or pass found.');    
          }*/
				$userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
				if (!$userPass) {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				$ticket = Ticket::where("user_pass_id", $userPass->id)->get();
				if ($userPass->total_days <= count($ticket)) {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}

				$reservation = Reservation::with('user')->where("user_pass_id", $userPass->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->orderBy("id", "DESC")->first();
				if ($reservation) {
					if ($reservation->is_ticket == '1') {
						throw new ApiGenericException('You have already checked-in.');
					}/*else if($reservation->is_ticket == '2'){
              throw new ApiGenericException('You have already checkout.');    
            }*/ else {
					}
					$userPass = [];
					$user = $reservation->user;
				} else {
					$user = $userPass->user;
				}
			} else {
				$user = $reservation->user;
			}
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}

			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber();
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['partner_id'] = $facility->owner_id;
			if ($reservation) {
				$data['reservation_id'] = $reservation->id;
				$data['check_in_datetime'] = $reservation->start_timestamp;
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['total'] = $reservation->total;
				$data['grand_total'] = $reservation->total;
				$data['length'] = $reservation->length;
				$data['user_pass_id'] = $reservation->user_pass_id;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$reservation->is_ticket = '1';
				$reservation->save();
			}

			if (count($userPass) > 0) {
				$data['check_in_datetime'] = date('Y-m-d H:i:s');
				$data['checkout_datetime'] = date('Y-m-d H:i:s');
				$data['user_pass_id'] = $userPass->id;

				$userPass->consume_days = $userPass->consume_days + 1;
				$userPass->remaining_days = $userPass->remaining_days - 1;
				$userPass->save();
			}

			$result = Ticket::create($data);
			$facilityName = ucwords($facility->full_name);
			if ($reservation) {
				Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			} else {
				Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
				$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
				$this->customeReplySms($msg, $user->phone);
			}

			$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}

			$msg =  "Welcome to $facilityName. #$result->ticket_number.";
			$data = ['msg' => $msg];
			return $data;
		} else if (isset($gate) && $gate->gate_type == "exit") {
			$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')/*->where('is_ticket', '0')*/->first();
			if (!$reservation) {
				$userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))/*->where('remaining_days','>',0)*/->orderBy('id', 'DESC')->first();
				if (!$userPass) {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				$passTicket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkout', '0')->orderBy('id', 'DESC')->first();
				if (!$passTicket) {

					if ($userPass->remaining_days <= 0) {
						throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
					}
					$data['user_id'] = $userPass->user_id;
					$data['facility_id'] = $request->facility_id;
					$data['ticket_number'] = $this->checkTicketNumber();
					//$checkout['checkin_time'] = date('Y-m-d H:i:s');
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					$data['check_in_datetime'] = date("Y-m-d H:i:s");
					$data['checkout_datetime'] = date("Y-m-d H:i:s");
					$data['estimated_checkout'] = date("Y-m-d H:i:s");
					$data['checkout_gate'] = $request->gate_id;
					$data['user_pass_id'] = $userPass->id;
					$data['is_checkout'] = 1;
					$data['checkout_time'] = date("Y-m-d H:i:s");
					$data['checkout_without_checkin'] = '1';
					Ticket::create($data);

					$userPass->consume_days = $userPass->consume_days + 1;
					$userPass->remaining_days = $userPass->remaining_days - 1;
					$userPass->save();
				} else {
					$passTicket->is_checkout = '1';
					$passTicket->checkout_gate = $request->gate_id;
					$passTicket->checkout_time = date("Y-m-d H:i:s");
					$passTicket->save();
				}
				$user = $userPass->user;

				/*$ticket = Ticket::where('user_pass_id', $userPass->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
            if(!$ticket){
              throw new ApiGenericException('Sorry, No checkin found against this mobile number.');
            }*/

				$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
				$msg = "Thank you for visiting " . $facilityName . ".";
				$this->customeReplySms($msg, $user->phone);

				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				$data = ['msg' => $msg];
				return $data;
			} else {

				if ($facility->facility_booking_type == '1') {
					throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');
				}

				if ($reservation->is_ticket == '2') {
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				$user = $reservation->user;
				$ticket = Ticket::where('reservation_id', $reservation->id)->first();
				if (!$ticket) {
					$data['user_id'] = $user->id;
					$data['checkout_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					//$data['is_checkin'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber();
					//$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					if ($reservation) {
						$data['reservation_id'] = $reservation->id;
						$data['check_in_datetime'] = $reservation->start_timestamp;
						$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
						$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
						$data['total'] = $reservation->total;
						$data['grand_total'] = $reservation->total;
						$data['length'] = $reservation->length;
						$data['user_pass_id'] = $reservation->user_pass_id;
						$data['is_checkout'] = '1';
						$data['checkout_time'] = date('Y-m-d H:i:s');
						$data['checkout_without_checkin'] = '1';

						Ticket::create($data);
						$reservation->is_ticket = '2';
						$reservation->save();
						$facilityName = ucwords($facility->full_name);
						$msg = "Thank you for visiting " . $facilityName . ".";

						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}
						$this->customeReplySms($msg, $user->phone);
						//return "Success";
						$data = ['msg' => $msg];
						return $data;
						//return $msg;            
					}
				} else if ($ticket->is_checkout == '0') {

					if ($facility->facility_booking_type == '1') {
						throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');
					}
					$facilityName = ucwords($facility->full_name);
					$ticket->is_checkout = '1';
					$ticket->checkout_time = date('Y-m-d H:i:s');
					$ticket->checkout_gate = $request->gate_id;
					$ticket->save();

					if ($reservation) {
						$reservation->is_ticket = '2';
						$reservation->save();
					}
					$msg = "Thank you for visiting " . $facilityName . ".";
					$this->customeReplySms($msg, $user->phone);
					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}
					$data = ['msg' => $msg];
					return $data;
					//return $msg;
				} else {
					$msg = "Sorry, No prepaid booking or pass found.";
					$this->customeReplySms($msg, $user->phone);
					throw new ApiGenericException('Sorry, No prepaid booking or pass found.');
				}
				return "Success";
			}/*else{
          throw new ApiGenericException("Invalid request.");
        }*/
			return "Success";
		}
		return "Success";
	}


	// save Declined Trans
	public function saveDeclinedTransaction(Request $request)
	{
		//return $request->session_id;
		$this->log->info("Request received for payment declined --" . json_encode($request));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}
		$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
		if (!$user) {
			throw new ApiGenericException("User Not Found.");
		}
		if (isset($request->payment_details)) {
			$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
			$this->log->info("Decline payment details request --" . json_encode($request->payment_details));
			$authorized_anet_transaction = new AuthorizeNetTransaction();
			$authorized_anet_transaction->sent = '1';
			$authorized_anet_transaction->user_id = $user->id;
			$authorized_anet_transaction->total = isset($request->payment_details['TransactionAmount']) ? $request->payment_details['TransactionAmount'] : '';
			$authorized_anet_transaction->name = isset($request->payment_details['MerchantName']) ? $request->payment_details['MerchantName'] : '';
			$authorized_anet_transaction->description = "Payment declined";
			$authorized_anet_transaction->response_message = isset($request->payment_details['ProcessorMessage']) ? $request->payment_details['ProcessorMessage'] : '';
			$authorized_anet_transaction->expiration = isset($request->payment_details['expiry']) ? $request->payment_details['expiry'] : '';
			$authorized_anet_transaction->card_type = isset($request->payment_details['CardType']) ? $request->payment_details['CardType'] : '';
			$authorized_anet_transaction->anet_trans_hash = $request->payment_details['processorReference'];
			$authorized_anet_transaction->ref_id = $request->payment_details['RequesterTransRefNum'];
			$authorized_anet_transaction->anet_trans_id = isset($request->payment_details['TransactionID']) ? $request->payment_details['TransactionID'] : '';
			$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
			$authorized_anet_transaction->method = "card";
			$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
			$authorized_anet_transaction->status_code = $request->payment_details['StatusCode'];
			$authorized_anet_transaction->status_type = $request->payment_details['StatusType'];
			$authorized_anet_transaction->status_message = $request->payment_details['StatusMessage'];
			$authorized_anet_transaction->name = $request->payment_details['CardHolderName'];
			$authorized_anet_transaction->save();
			return "Payment declined data save successfull !";
		} else {
			throw new ApiGenericException("Payment Details Not Found.");
		}
		$this->log->info("Decline payment transaction done --" . json_encode($result));
	}

	public function zooTicketDriveupCheckinCheckout(Request $request)
	{
		$this->log->info("Request received --" . json_encode($request));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}
		$facilityName =  ucwords($facility->full_name);
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}

		if ($request->session_id == '') {
			throw new ApiGenericException('Card info not found. Please try again.');
		}

		$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
		if (!$user) {
			$user = User::create(
				[
					'name' => '',
					'email' => '',
					'phone' => '',
					'password' => Hash::make(str_random(60)),
					'anon' => true,
					'user_type' => '5',
					'created_by' => $facility->owner_id,
					'session_id' => $request->session_id,
				]
			);
		}

		$isMember = 0;
		if ($user->member_user_id != '') {
			$isMember = 1;
		}

		if (isset($gate) && $gate->gate_type == "entry") {

			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}

			if ($facility->check_vehicle_enabled == '1') {
				$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber();
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['check_in_datetime'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['vp_device_checkin'] = '1';
			$data['partner_id'] = $facility->owner_id;

			$result = Ticket::create($data);
			$paidAmount = '';
			if (isset($request->payment_details)) {
				$ticket = Ticket::where('id', $result->id)->first();
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "VP6800 Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
				$authorized_anet_transaction->status_code = isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
				$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
				$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
				$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
				$ticket->save();

				$paidAmount = isset($request->payment_details['TransactionAmount']) ? $request->payment_details['TransactionAmount'] : '';
			}
			$this->log->info("New VP6800 checkin done --" . json_encode($result));

			if ($result) {

				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				if ($paidAmount == '') {
					$msg =  "Welcome to $facilityName. #$result->ticket_number.";
				} else {
					$msg =  "Welcome to $facilityName $$paidAmount charged. #$result->ticket_number.";
				}
				$data = ['msg' => $msg];
				return $data;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}

		if (isset($gate) && $gate->gate_type == "exit") {
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();


			if (!$ticket) {

				if (isset($request->payment_details)) {

					$data['user_id'] = $user->id;
					$data['checkout_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					$data['is_checkout'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber();
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					//$data['reservation_id'] = $qrCode->reservation_id;
					//$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
					//$data['check_in_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->start_time));
					//$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
					//$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($eventData->end_time));
					//$data['total'] = $qrCode->reservation->total;
					//$data['grand_total'] = $qrCode->reservation->total;
					//$data['length'] = $qrCode->reservation->length;
					//$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['checkout_time'] = date('Y-m-d H:i:s');
					//$data['event_id'] = $eventData->id;
					$data['checkout_without_checkin'] = '1';
					//dd($data);
					$ticket = Ticket::create($data);
					$this->log->info("payment details request --" . json_encode($request->payment_details));
					$authorized_anet_transaction = new AuthorizeNetTransaction();
					$authorized_anet_transaction->sent = '1';
					$authorized_anet_transaction->user_id = $user->id;
					$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
					$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
					$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
					$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
					$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
					$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
					$authorized_anet_transaction->ref_id = isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
					$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
					$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
					$authorized_anet_transaction->method = "card";
					$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
					$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
					$authorized_anet_transaction->status_code = isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
					$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
					$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
					$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
					$authorized_anet_transaction->save();
					$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
					$ticket->anet_transaction_id = $authorized_anet_transaction->id;
					$ticket->total = $request->payment_details['TransactionAmount'];
					$ticket->grand_total = $request->payment_details['TransactionAmount'];
					$ticket->terminal_id = $request->payment_details['TerminalID'];
					$ticket->save();
				}
				//$user = $qrCode->reservation->user;



				//$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
				//$qrCode->save();

				$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
				$msg = "Thank you for visiting " . $facilityName . " $" . $request->payment_details['TransactionAmount'] . " charged.";
				$this->customeReplySms($msg, $user->phone);

				$data = ['msg' => $msg];
				return $data;

				//  throw new ApiGenericException('No checkin found for this user.');    

			}





			if ($facility->check_vehicle_enabled == '1') {
				$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$diff_in_hours = $arrival_time->diffInRealHours($from);
			if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
				$diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
			}
			$ticket->checkout_gate = $request->gate_id;
			$ticket->is_checkout = 1;
			$ticket->length = $diff_in_hours;
			$ticket->checkout_time = date('Y-m-d H:i:s');
			$paidAmount = '';
			if (isset($request->payment_details)) {
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
				$authorized_anet_transaction->status_code = isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
				$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
				$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
				$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();


				if ($request->is_overstay == '1') {

					$checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
					$checkoutTime = date("Y-m-d", strtotime($ticket->checkin_time)) . " 23:59:59";
					$parkingNowTime = date("Y-m-d H:i:s");
					$parkingStartTime = date("Y-m-d") . " 00:00:00";
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $parkingStartTime);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
					$diff_in_hours = $arrival_time->diffInRealHours($from);
					$diff_in_mins = $arrival_time->diffInRealMinutes($from);

					$diff_in_hours = number_format($diff_in_mins / 60, 2);


					$overstay = new OverstayTicket();
					$overstay->user_id = $ticket->user_id;
					$overstay->facility_id = $ticket->facility_id;
					$overstay->total = $request->payment_details['TransactionAmount'];
					$overstay->ticket_number = $ticket->ticket_number;
					$overstay->is_checkin = '1';
					$overstay->is_checkout = '1';
					$overstay->check_in_datetime = date('Y-m-d H:i:s', strtotime($parkingStartTime));
					$overstay->checkout_datetime = date('Y-m-d H:i:s');
					$overstay->partner_id = $ticket->partner_id;
					$overstay->ticket_id = $ticket->id;
					$overstay->anet_transaction_id = $authorized_anet_transaction->id;
					$overstay->length = $diff_in_hours;
					$overstay->save();

					$ticket->grand_total = $ticket->grand_total + $request->payment_details['TransactionAmount'];
					$ticket->is_checkout = '1';
					$ticket->is_overstay = '1';
					$ticket->save();
				} else {
					$ticket->anet_transaction_id = $authorized_anet_transaction->id;
					$ticket->total = $request->payment_details['TransactionAmount'];
					$ticket->grand_total = $request->payment_details['TransactionAmount'];
					$ticket->terminal_id = $request->payment_details['TerminalID'];
					$paidAmount =  $request->payment_details['TransactionAmount'];
				}
			}

			if ($ticket->vp_device_checkin == '1') {
				$ticket->checkout_datetime = date('Y-m-d H:i:s');
			}
			$result = $ticket->save();
			$this->log->info("checkout VP6800 done --" . json_encode($ticket));
			if ($result) {
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}

				if ($request->is_overstay == '1') {
					$response = ["msg" => "Thank you for visiting " . $facilityName . ". Extra $" . $request->payment_details['TransactionAmount'] . " is charged for overstay."];
				} else {

					if ($paidAmount == '') {
						$response = ["msg" => "Thank you for visiting " . $facilityName . "."];
					} else {
						$response = ["msg" => "Thank you for visiting " . $facilityName . " $$paidAmount charged."];
					}
				}

				return $response;
				/*$msg =  "Thank you for visiting ".$facilityName.".";
          $data = ['msg' => $msg];
          return $data;*/
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
	}


	public function zooTicketSessionCheckinCheckout(Request $request)
	{
		//try{
		$this->log->info("Request received --" . json_encode($request->all()));
		$this->log->info("System Not available error test - 1 -");
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}
		$facilityName =  ucwords($facility->full_name);
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}

		if ($request->session_id == '') {
			throw new ApiGenericException('Card info not found. Please try again.');
		}

		$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
		if (!$user) {
			$user = User::create(
				[
					'name' => '',
					'email' => '',
					'phone' => '',
					'password' => Hash::make(str_random(60)),
					'anon' => true,
					'user_type' => '5',
					'created_by' => $facility->owner_id,
					'session_id' => $request->session_id,
				]
			);
		}
		$this->log->info("System Not available error test - 2 -");
		$isMember = 0;
		if ($user->member_user_id != '') {
			$isMember = 1;
		}


		if (isset($gate) && $gate->gate_type == "entry") {

			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}
			$reservation = Reservation::with('user')->where("user_id", $user->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
			$userPass = [];
			if (!$reservation) {

				/*$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereNull('cancelled_at')->first();
        if($reservation){
          if($reservation->is_ticket == '1'){
            throw new ApiGenericException('You have already checked-in.');    
          }else if($reservation->is_ticket == '2'){
            throw new ApiGenericException('You have already checkout.');    
          }else{

          }
        }*/
				if ($facility->facility_booking_type == '0') {
					throw new ApiGenericException('Sorry, Drive-Up booking is not allowed.');
				}

				$userPass = UserPass::with('user')->where("user_id", $user->id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days', '>', 0)->orderBy('id', 'DESC')->first();
				if (!$userPass) {
					if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
						throw new ApiGenericException('Sorry, No prepaid booking found against this card.');
					}

					$this->log->info("System Not available error test - 3 -");
					if ($facility->is_prepaid_first == '0') {

						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber();
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}

						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}
						if ($user->email != '') {
							Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						}
						$msg = "Welcome to $facilityName.";
						if ($user->phone != '') {
							$this->customeReplySms($msg, $user->phone);
						}

						$data = ['msg' => $msg];
						return $data;
					}
					$this->log->info("System Not available error test - 4 -");
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
					$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
					$diff_in_hours = $arrival_time->diffInRealHours($from);
					if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
						$diff_in_hours = 24;
					}

					//$isMember = 0;
					$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
					if ($rate == false) {
						throw new ApiGenericException('Garage is not available.');
					}
					$data = [];
					//$data ['session_id'] = $request->session_id;
					//$data ['price'] = $rate['price']  + $facility->processing_fee;
					$data['price'] = $rate['price'] + $facility->drive_up_processing_fee;
					return $data;
				}

				/*$reservation = Reservation::with('user')->where("user_pass_id", $userPass->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->first();
        if($reservation){
          if($reservation->is_ticket == '1'){
            throw new ApiGenericException('You have already checked-in.');    
          }else if($reservation->is_ticket == '2'){
            throw new ApiGenericException('You have already checkout.');    
          }else{

          }
          $userPass = [];
          $user = $reservation->user;          
        }else{
          $user = $userPass->user;
        }*/

				$this->log->info("System Not available error test - 5 -");
			}

			if ($facility->facility_booking_type == '1') {
				throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');
			}

			if ($facility->check_vehicle_enabled == '1') {
				$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber();
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['check_in_datetime'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['vp_device_checkin'] = '1';
			$data['partner_id'] = $facility->owner_id;
			$this->log->info("System Not available error test - 6 -");
			if ($reservation) {
				$data['reservation_id'] = $reservation->id;
				$data['check_in_datetime'] = $reservation->start_timestamp;
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['total'] = $reservation->total;
				$data['grand_total'] = $reservation->total;
				$data['length'] = $reservation->length;
				$data['user_pass_id'] = $reservation->user_pass_id;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['vp_device_checkin'] = '0';
				$reservation->is_ticket = '1';
				$reservation->save();
			}
			$this->log->info("System Not available error test - 7 -");
			if (count($userPass) > 0) {
				$data['check_in_datetime'] = date('Y-m-d H:i:s');
				$data['checkout_datetime'] = date('Y-m-d H:i:s');
				$data['user_pass_id'] = $userPass->id;
				$data['vp_device_checkin'] = '0';

				$userPass->consume_days = $userPass->consume_days + 1;
				$userPass->remaining_days = $userPass->remaining_days - 1;
				$userPass->save();
			}

			$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
			$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
			if (isset($request->CardType)) {
				$card_type = '';
				if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
					$card_type = 'VISA';
				} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
					$card_type = 'MASTERCARD';
				} else if (strtolower($request->CardType) == "jcb") {
					$card_type = 'JCB';
				} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
					$card_type = 'AMEX';
				} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
					$card_type = 'DISCOVER';
				} else {
					$card_type = $request->CardType;
				}
				$data['card_type'] = $card_type;
			}
			$result = Ticket::create($data);

			$this->log->info("Zoo session checkin done --" . json_encode($result));
			$this->log->info("System Not available error test - 8 -");
			if ($result) {
				$this->log->info("System Not available error test - 21 -");
				$facilityName = ucwords($facility->full_name);
				if ($reservation) {
					$this->log->info("System Not available error test - 22 -");
					Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
				} else {
					try {
						$this->log->info("System Not available error test - 23 -");
						Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						$this->log->info("System Not available error test - 26 -");
						$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
						$this->log->info("System Not available error test - 27 -");
						$this->customeReplySms($msg, $user->phone);
						$this->log->info("System Not available error test - 28 -");
					} catch (\Exception $e) {
						$this->log->error("System exception -" . $e->getMessage());
					}
				}
				$this->log->info("System Not available error test - 24 -");
				$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				$this->log->info("System Not available error test - 25 -");
				//return "Welcome to $facilityName. #$result->ticket_number.";
				$msg =  "Welcome to $facilityName. #$result->ticket_number.";
				$data = ['msg' => $msg];
				$this->log->info("System Not available error test - 9 -");
				return $data;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
		$this->log->info("System Not available error test - 10 -");
		if (isset($gate) && $gate->gate_type == "exit") {
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			/*if(!$ticket){
            
              //throw new ApiGenericException('No checkin found for this user.');    
              $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
              $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
              $diff_in_hours = $arrival_time->diffInRealHours($from);
              if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                $diff_in_hours = 24;
              }
              
              $isMember = 0;
              $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE,$isMember);
              if($rate == false){
                  throw new ApiGenericException('Garage is not available.');    
              }
              $data = [];
              $data ['price'] = $rate['price']  + $facility->processing_fee;
              return $data;
          }*/

			if (!$ticket) {

				$this->log->info("System Not available error test - 11 -");
				$reservation = Reservation::with('user')->where("user_id", $user->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
				$userPass = [];
				if (!$reservation) {

					$userPass = UserPass::with('user')->where("user_id", $user->id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days', '>', 0)->orderBy('id', 'DESC')->first();
					if (!$userPass) {
						throw new ApiGenericException('Sorry, No check-in ticket found for this credit card.');
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
						$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
						$diff_in_hours = $arrival_time->diffInRealHours($from);
						if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
							$diff_in_hours = 24;
						}

						//$isMember = 0;
						$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
						if ($rate == false) {
							throw new ApiGenericException('Garage is not available.');
						}
						$data = [];
						//$data ['session_id'] = $request->session_id;
						$data['price'] = $rate['price']  + $facility->drive_up_processing_fee;
						return $data;
					}
				}
				if ($facility->facility_booking_type == '1') {
					throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');
				}
				$this->log->info("System Not available error test - 12 -");
				$data['user_id'] = $user->id;
				$data['checkout_gate'] = $request->gate_id;
				$data['facility_id'] = $request->facility_id;
				//$data['is_checkin'] = 1;
				$data['ticket_number'] = $this->checkTicketNumber();
				$data['ticket_security_code'] = rand(1000, 9999);
				$data['vp_device_checkin'] = '0';
				$data['partner_id'] = $facility->owner_id;
				$data['is_checkout'] = 1;
				$data['checkout_without_checkin'] = '1';
				$data['checkout_time'] = date('Y-m-d H:i:s');
				if ($reservation) {
					$data['reservation_id'] = $reservation->id;
					$data['check_in_datetime'] = $reservation->start_timestamp;
					$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
					$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
					$data['total'] = $reservation->total;
					$data['grand_total'] = $reservation->total;
					$data['length'] = $reservation->length;
					$data['user_pass_id'] = $reservation->user_pass_id;
					//$data['checkin_time'] = date('Y-m-d H:i:s');
					$reservation->is_ticket = '1';
					$reservation->save();
				}
				$this->log->info("System Not available error test - 3 -");
				if (count($userPass) > 0) {
					$data['check_in_datetime'] = date('Y-m-d H:i:s');
					$data['checkout_datetime'] = date('Y-m-d H:i:s');
					$data['user_pass_id'] = $userPass->id;

					$userPass->consume_days = $userPass->consume_days + 1;
					$userPass->remaining_days = $userPass->remaining_days - 1;
					$userPass->save();
				}
				$result = Ticket::create($data);

				$this->log->info("Zoo session checkin done --" . json_encode($result));

				if ($result) {

					$facilityName = ucwords($facility->full_name);
					/*if($reservation){
                  Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
                }else{
                  Artisan::queue('email:touchless-parking-atlanta-confirm-checkin',array('id'=>$result->id, 'type'=>'checkin'));
                  $msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                  $this->customeReplySms($msg, $user->phone);
                }*/


					$this->log->info("direct checkout SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
					//check gate api

					//return "Welcome to $facilityName. #$result->ticket_number.";
					$msg =  "Thank you for visiting " . $facilityName . ".";
					$this->customeReplySms($msg, $user->phone);
					$data = ['msg' => $msg];
					$this->log->info("System Not available error test - 14 -");
					return $data;
				} else {
					throw new ApiGenericException('Something wrong.');
				}
			}

			if ($facility->facility_booking_type == '1') {
				throw new ApiGenericException('Sorry, Prepaid booking is not allowed.');
			}

			if ($ticket->anet_transaction_id == '') {

				if ($ticket->user_pass_id != '' || $ticket->reservation_id != '') {
					$this->log->info("System Not available error test - 15 -");
					$checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
					$checkoutTime = date("Y-m-d", strtotime($ticket->checkin_time)) . " 23:59:59";
					$parkingNowTime = date("Y-m-d H:i:s");
					$parkingStartTime = date("Y-m-d") . " 00:00:00";
					//dd($checkinTime, $checkoutTime, $parkingNowTime, $parkingStartTime);
					if (strtotime($parkingNowTime) > strtotime($checkoutTime)) {
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $parkingStartTime);
						$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
						$diff_in_hours = $arrival_time->diffInRealHours($from);
						$diff_in_mins = $arrival_time->diffInRealMinutes($from);
						if ($diff_in_mins > 0) {
							$diff_in_hours = number_format($diff_in_mins / 60, 2);
						}
						if ($diff_in_hours < self::EVENT_THRESHOLD_TYPE) {
							$diff_in_mins = $arrival_time->diffInRealMinutes($from);
							$diff_in_hours = number_format($diff_in_mins / 60, 2);
						}
						//  dd($arrival_time, $diff_in_hours);
						/** this function is used to get Availability Information for respective facility **/
						$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, 0, $isMember);
						$data['price'] =  $rate['price']  + $facility->processing_fee;
						$data['is_overstay'] =  '1';
						return $data;
					}
				} else {
					$this->log->info("System Not available error test - 16 -");
					$checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
					$parkingNowTime = date("Y-m-d H:i:s");

					//$checkinTime = "2023-02-07 00:48:54";
					//$parkingNowTime = "2023-02-07 01:06:19";
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinTime);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);

					$diff_in_hours = $arrival_time->diffInRealHours($from);
					$diff_in_mins = $arrival_time->diffInRealMinutes($from);
					$diff_in_secs = $arrival_time->diffInSeconds($from);
					if ($diff_in_mins < 60) {
						$diff_in_hours = $diff_in_mins / 100;
					}
					if ($diff_in_mins > 59) {
						$diff_in_hours = number_format($diff_in_mins / 60, 2);
					}
					if ($diff_in_secs < 60) {
						$diff_in_hours = .01;
					}


					/*if($diff_in_hours < self::EVENT_THRESHOLD_TYPE){
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_hours = number_format($diff_in_mins/60, 2);     
                  }*/
					//dd(($diff_in_hours));
					/** this function is used to get Availability Information for respective facility **/
					//  $member_id = 0;
					$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
					if ($rate) {
						if ($rate['price'] == 0 || $rate['price'] == "0.00") {
							$ticket->is_checkout = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->total = 0.00;
							$ticket->grand_total = 0.00;
							$ticket->length = $diff_in_hours;
							$ticket->save();

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "Thank you for visiting " . $facilityName . ".";
							$this->customeReplySms($msg, $user->phone);
							$data = ['msg' => $msg];
							return $data;
						}
					}
					//$result['price'] =  $rate['price']  + $facility->processing_fee;
					$result['price'] =  $rate['price'] + $facility->drive_up_processing_fee;
					return $result;
				}
			}
			if ($ticket->anet_transaction_id != '') {
				$this->log->info("System Not available error test - 17 -");
				$checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
				$checkoutTime = date("Y-m-d", strtotime($ticket->checkin_time)) . " 23:59:59";
				$parkingNowTime = date("Y-m-d H:i:s");
				$parkingStartTime = date("Y-m-d") . " 00:00:00";
				//dd($checkinTime, $checkoutTime, $parkingNowTime, $parkingStartTime);
				if (strtotime($parkingNowTime) > strtotime($checkoutTime)) {
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $parkingStartTime);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
					$diff_in_hours = $arrival_time->diffInRealHours($from);
					$diff_in_mins = $arrival_time->diffInRealMinutes($from);
					if ($diff_in_mins > 0) {
						$diff_in_hours = number_format($diff_in_mins / 60, 2);
					}
					if ($diff_in_hours < self::EVENT_THRESHOLD_TYPE) {
						$diff_in_mins = $arrival_time->diffInRealMinutes($from);
						$diff_in_hours = number_format($diff_in_mins / 60, 2);
					}
					//dd($diff_in_hours);
					/** this function is used to get Availability Information for respective facility **/
					$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
					$data['price'] =  $rate['price'];
					$data['is_overstay'] =  '1';
					return $data;
				}
			}

			if ($facility->check_vehicle_enabled == '1') {
				$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$this->log->info("System Not available error test - 18 -");
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$diff_in_hours = $arrival_time->diffInRealHours($from);
			if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
				$diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
			}
			$ticket->checkout_gate = $request->gate_id;
			$ticket->is_checkout = 1;
			$ticket->length = $diff_in_hours;
			$ticket->checkout_time = date('Y-m-d H:i:s');
			if (isset($request->payment_details)) {
				$this->log->info("System Not available error test - 19 -");
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = $request->payment_details['processorReference'];
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
			}

			$result = $ticket->save();
			$this->log->info("checkout VP6800 done --" . json_encode($ticket));
			if ($result) {
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				//return "Thank you for visiting ".$facilityName.".";
				$msg =  "Thank you for visiting " . $facilityName . ".";
				$data = ['msg' => $msg];
				$this->log->info("System Not available error test - 20 -");
				return $data;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}

		/*}catch(\Exception $e){
      $this->log->error("error received --".$e->getMessage());  
      throw new ApiGenericException("The system is not currently available. Please try again later.");
    }*/
	}
}
