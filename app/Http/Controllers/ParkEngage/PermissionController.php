<?php

namespace App\Http\Controllers\ParkEngage;

use App\Models\Permission;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

use Illuminate\Http\Request;
use App\Http\Requests;

class PermissionController extends Controller
{
   
   public function index() {
      return Permission::with('membeshipPlan')->get();

   } 
   public function store(Request $request) {
      
      $this->validate($request, Permission::$validation);
      
      $data['name'] = $request->name;
      $data['membership_plan_id'] = $request->membership_plan_id;
      $data['display_name'] = $request->display_name;
      $data['description'] = $request->description;
      $result = Permission::create($data);

      return $result;
   }

   public function update(Request $request) {
      
      $this->validate($request, Permission::$validation);
      
      $permission = Permission::where('id', $request->id)->first();
      if (!$permission) {
        throw new NotFoundException('No permission id found.');
      }
      $permission->name = $request->name;
      $permission->membership_plan_id = $request->membership_plan_id;
      $permission->display_name = $request->display_name;
      $permission->description = $request->description;
      $permission->save();
      return $permission;
    
   }
   
}
