<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Services\LoggerFactory;
use App\Classes\MagicCrypt;


use App\Exceptions\ApiGenericException;
use Response;


class PlanetProfileController extends Controller
{
	public function __construct(Request $request, LoggerFactory $logFactory)
	{
		$this->log = $logFactory->setPath('logs/planet')->createLogger('planet');
		$this->request = $request;
	}

	public function index(Request $request)
	{
		$user_id = Auth::user()->id;

		$result = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id);

		if ($request->sort != '') {
			$result = $result->orderBy($request->sort, $request->sortBy);
		} else {
			$result = $result->orderBy("id", "DESC");
		}
		// $result = $result->paginate(10);
		$result = $result->get();
		if (!$result) {
			throw new ApiGenericException("Card Not Found");
		}
		$data = [];

		$data['payments'] = $result;

		return $data;
	}

	public function store(Request $request)
	{
		$this->log->info("Request Data Planet: " . json_encode($request->all()));
		$user_id = Auth::user()->id;
		$partner_id = Auth::user()->created_by;
		if ($request->nonce) {
			$this->setDecryptedCard();
		}
		$validationID = '"' . config('parkengage.TOWNSEND_SECURITY_MERCHANT_ID') . '"';
		$validationCode = '"' . config('parkengage.TOWNSEND_SECURITY_VALIDATION_CODE') . '"';

		//dd($request->all());
		//dd($request->card_number, $request->expiration_date, $request->security_code,config('parkengage.PLANET_PROFILE_SAVE_URL'),$validationID,$validationCode);
		$curl = curl_init();

		curl_setopt_array($curl, array(
			CURLOPT_URL => config('parkengage.PLANET_PROFILE_SAVE_URL'),
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => '{
				"Request": {
					"Type": "EftAuthorization",
					"Version": "W2MXG520",
					"Credentials": {
					  "ValidationID": ' . $validationID . ',
					  "ValidationCode": ' . $validationCode . ',
					  "ValidationCodeHash": null
					},
					"Params": {
						"PaymentOkUrl": "",
						"CardNumber": "' . $request->card_number . '",
						"CardExpiryDateMMYY": "' . $request->expiration_date . '",
						"CardStartDateMMYY": "",
						"CardIssueNumber": "",
						"CardCvv2": "' . $request->security_code . '",
						"CardholderStreetAddress1": "",
						"CardholderCity": "",
						"CardholderState": "",
						"CardholderZipCode": "",
						"CardholderNameFirst": "",
						"CardholderNameLast": "",
						"Amount": "000",
						"Currency": "USD",
						"RequesterTransRefNum": "TEST AUTH 001",
						"UserData1": "",
						"UserData2": "",
						"UserData3": "",
						"UserData4": "",
						"UserData5": "",
						"OptionFlags": "G"
					  }
			  }
		  }',
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json',
				'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
			),
		));

		$response = curl_exec($curl);

		curl_close($curl);
		//return gettype($response);
		//dd($response);
		$refundstatus = json_decode($response, TRUE);
		//dd($refundstatus);
		//["Response"]["Params"]

		if (!$refundstatus) {
			throw new ApiGenericException("Card Details Not Added");
		}

		$cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $partner_id)->where('card_last_four', $refundstatus["Response"]["Params"]["CardNumberLast4"])->first();

		if ($cardCheck) {
			throw new ApiGenericException("Card Details Already Added");
		}

		if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
			//. $refundstatus["Response"]["Params"]["ResultReason"] 
			throw new ApiGenericException("Card details are invalid");
		}

		$data['user_id'] = $user_id;
		$data['partner_id'] = $partner_id;
		$data['name'] = isset($request->name_on_card) ? $request->name_on_card : '';
		$data['card_last_four'] = $refundstatus["Response"]["Params"]["CardNumberLast4"];
		$data['card_type'] = $refundstatus["Response"]["Params"]["CardSchemeId"];
		$data['card_name'] = $refundstatus["Response"]["Params"]["CardSchemeName"];
		$data['expiry'] = $request->expiration_date;
		$data['token'] = $refundstatus["Response"]["Params"]["Token"];
		$data['tx_state_text'] = $refundstatus["Response"]["Params"]["TxStateText"];
		$data['tx_state'] = $refundstatus["Response"]["Params"]["TxState"];
		$data['result_reason'] = $refundstatus["Response"]["Params"]["ResultReason"];
		$data['currency_used'] = $refundstatus["Response"]["Params"]["CurrencyUsed"];

		$result = PlanetPaymentProfile::create($data);
		if (!$result) {
			throw new ApiGenericException("Record Not Added");
		}
		return $result;
	}


	public function destroy($id)
	{
		$result = PlanetPaymentProfile::find($id);

		if ($result) {
			$result->delete();
			//return "Card Successfully Deleted.";
			return response(
				[
					"message" => 'Card Successfully Deleted.'
				]
			);
		}
		throw new ApiGenericException("Card Not Found.");
	}

	public function getPlanetPaymentProfile(Request $request)
	{
		$user_id = Auth::user()->id;
		$result = PlanetPaymentProfile::Select('card_last_four', 'expiry', 'token', 'user_id', 'card_type', 'card_name', 'tx_state_text', 'tx_state', 'result_reason', 'currency_used')->whereNull('deleted_at')->where('user_id', $user_id)->get();
		return $result;
	}

	public function setDecryptedCard()
	{
		if (isset($this->request->payment_profile_id) && $this->request->payment_profile_id != "") {
			return;
		}
		$key = env('PCI_ENCRYPTION_KEY');
		$mc = new MagicCrypt($key, 256);
		$decryptedNonce = $mc->decrypt($this->request->nonce);
		$cardData = explode(':', $decryptedNonce);
		$zipCode = isset($cardData[4]) ? $cardData[4] : '';
		$this->request->request->add(
			[
				'name_on_card' => $cardData[0],
				'card_number' => $cardData[1],
				'expiration_date' => $cardData[2],
				'security_code' => $cardData[3],
				'zip_code_on_card' => $zipCode
			]
		);
	}
}
