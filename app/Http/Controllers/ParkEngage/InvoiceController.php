<?php

namespace App\Http\Controllers\ParkEngage;

use Illuminate\Support\Facades\Auth;
use Artisan;
use Authorizer;
use config;
use Illuminate\Support\Facades\DB;
use DateTime;
use Exception;
use Excel;
use File;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Storage;
use Response;
use Carbon\Carbon;

use Illuminate\Http\Request;
use Illuminate\Support\Arr;

use App\Http\Requests;
use App\Http\Controllers\Controller;

use App\Classes\PromoCodeLib;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\MagicCrypt;
use App\Classes\PlanetPaymentGateway;
use App\Classes\DatacapPaymentGateway;
use App\Classes\HeartlandPaymentGateway;


use App\Services\LoggerFactory;
use App\Services\Pdf;

use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;

use App\Models\PermitRequest;
use App\Models\AuthorizeNetTransaction;
use App\Models\Facility;
use App\Models\User;
use App\Models\OauthClient;
use App\Models\PermitRateDescription;
use App\Models\PermitRate;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\PermitVehicle;
use App\Models\PermitRateDiscount;

use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;

use App\Http\Helpers\QueryBuilder;

use App\Models\ParkEngage\SpelmanUser;
use App\Models\Promotion;
use App\Models\FacilityFee;
use App\Models\ParkEngage\WaitingList;
use App\Models\ParkEngage\AreQrcode;
use App\Models\Ticket;
use App\Models\ParkEngage\Gate;
use App\Models\BlackListedVehicle;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\UserPermitRequest;
use App\Models\UserPermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\ParkEngage\PermitTypeDoumentList;
use App\Models\ParkEngage\PermitTypeMaster;
use App\Models\ParkEngage\PermitTypeFacilityMapping;
use App\Models\ParkEngage\PermitRequestDocuments;
use App\Models\UserPermitVehicleMapping;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\ParkEngage\AuthorizedDriver;
use App\Models\ParkEngage\RateInformation;
use App\Models\PermitRequestRenewHistory;
use App\Models\ParkEngage\FacilityPaymentDetail;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\PermitServices;
#PIMS-10032 dushyant
use App\Models\ParkEngage\UserPermitBundleMapping;
use App\Models\ParkEngage\UserPermitTypeMapping;
use App\Models\ParkEngage\UserPaymentBundleMapping;

class InvoiceController extends Controller
{
	protected  $request;
	protected  $log;

	const SUPERADMIN = 1;
	const SUBORDINATE = 4;
	const PARTNER = 3;
	const REGIONAL_MANAGER = 12;

	public function __construct(Request $request, LoggerFactory $logFactory)
	{
		$this->log = $logFactory->setPath('logs/invoice/permit')->createLogger('monthly-invoice');
		$this->request = $request;
	}

	public function customeReplySms($msg, $phone, $imageURL = '')
	{
		$accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
		$authToken  = config('parkengage.TWILIO_AUTH_TOKEN');

		$client = new Client($accountSid, $authToken);
		try {
			// Use the client to do fun stuff like send text messages!
			$client->messages->create(
				// the number you'd like to send the message to
				$phone,
				array(
					// A Twilio phone number you purchased at twilio.com/console
					'from' => env('TWILIO_PHONE'),
					// the body of the text message you'd like to send
					'body' => "$msg",
					//'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)
				)
			);
			$this->log->info("Message : {$msg} sent to $phone");
			return "success";
		} catch (RestException $e) {
			$this->log->error($e->getMessage());
			return "success";
		}
	}

	public function setCustomTimezone($facility_id)
	{
		$facility = Facility::find($facility_id);
		$secret = OauthClient::where('partner_id', $facility->owner_id)->first();
		$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();

		if (($facility) && ($facility->timezone != '')) {
			date_default_timezone_set($facility->timezone);
		} else {
			if ($partnerTimezone) {
				if ($partnerTimezone->timezone != '') {
					date_default_timezone_set($partnerTimezone->timezone);
				}
			}
		}
	}

	public function getUserActivePermitList(Request $request)
	{
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('No partner found.');
			}
			if ($secret->partner_id != Auth::user()->created_by) {
				throw new ApiGenericException('There is no invoice available at this moment.');
			}
		}
		$facility = '';
		#dushyant 21-05-2024
		#add flag to show pay button or not
		$now = Carbon::today();
		//$fiveDaysBefore = $now->copy()->subDays(5);
		$fiveDaysAfter = $now->copy()->addDays(24);
		#END add flag to show pay button or not
		if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
			$partner_id = Auth::user()->created_by;
			$facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
		} else if (Auth::user()->user_type == '3') {
			$partner_id = Auth::user()->id;
		} else if (Auth::user()->user_type == '1') {
			$partner_id = $request->partner_id;
		} else {
			$partner_id = Auth::user()->created_by;
		}
		#22-08-2024 dushyant
		$permit_renew_date_visible = config('parkengage.PERMIT_RENEW_DATE');
		$currentDate = Carbon::now()->format('Y-m-d');
		$fiveDaysBefore = $now->copy()->addDays(5);

		if (Auth::user()->user_type == '5') {
			//$result = DB::table('permit_requests')->select('id','user_id','partner_id','account_number','facility_id','status','user_consent','desired_start_date','desired_end_date','permit_rate_id','permit_rate','status','cancelled_at','permit_type_name','permit_final_amount','processing_fee','negotiated_amount')->selectRaw("IF(desired_end_date BETWEEN ? AND ?, 1, 0) as permit_renew_pay", [$fiveDaysBefore, $fiveDaysAfter])->whereNotNull('anet_transaction_id')->where('user_id', Auth::user()->id)->where('partner_id', $partner_id);
			$result = DB::table('permit_requests')->select('id', 'anet_transaction_id', 'user_id', 'partner_id', 'account_number', 'facility_id', 'status', 'user_consent', 'desired_start_date', 'desired_end_date', 'permit_rate_id', 'permit_rate', 'status', 'cancelled_at', 'permit_type_name', 'permit_final_amount', 'processing_fee', 'negotiated_amount', 'business_id');
			if (isset($permit_renew_date_visible)) {
				$result = $result->selectRaw("
					IF(
						(business_id is null || business_id=0) AND
						((DATE_FORMAT(?, '%Y-%m') = DATE_FORMAT(desired_end_date, '%Y-%m') AND DAY(?) >= ?) OR
						(DATE(?) > DATE(desired_end_date))),
						1, 0
					) as permit_renew_pay", [$currentDate, $currentDate, $permit_renew_date_visible, $currentDate]);
			} else {
				$result = $result->selectRaw(" IF((business_id is null || business_id=0) and desired_end_date <= ?, 1, 0) as permit_renew_pay", [$fiveDaysBefore]);
			}
			$result = $result->whereNotNull('anet_transaction_id')->where('user_id', Auth::user()->id)->where('partner_id', $partner_id);
		} else {
			$result =  DB::table('permit_requests')->select('id', 'anet_transaction_id', 'user_id', 'partner_id', 'account_number', 'facility_id', 'status', 'user_consent', 'desired_start_date', 'desired_end_date', 'permit_rate_id', 'permit_rate', 'status', 'cancelled_at', 'permit_type_name', 'permit_final_amount', 'processing_fee', 'negotiated_amount', 'business_id');
			if (isset($permit_renew_date_visible)) {
				$result = $result->selectRaw("
					IF(
						(business_id is null || business_id=0) AND
						((DATE_FORMAT(?, '%Y-%m') = DATE_FORMAT(desired_end_date, '%Y-%m') AND DAY(?) >= ?) OR
						(DATE(?) > DATE(desired_end_date))),
						1, 0
					) as permit_renew_pay", [$currentDate, $currentDate, $permit_renew_date_visible, $currentDate]);
			} else {
				$result = $result->selectRaw(" IF((business_id is null || business_id=0) and desired_end_date <= ?, 1, 0) as permit_renew_pay", [$fiveDaysBefore]);
			}
			$result = $result->whereNotNull('anet_transaction_id')->where('partner_id', $partner_id);
		}

		if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
			$result = $result->whereIn('facility_id', $facility);
		}
		$result = $result->orderBy('id', 'DESC')->get();

		$totalBill = $totalBillCurrent = [];
		if ($result) {
			foreach ($result as $ke => $va) {
				$transaction = AuthorizeNetTransaction::where('id', $va->anet_transaction_id)->first();
				$currentMonth = Carbon::now()->format('m');
				$m = strtotime($va->desired_start_date);
				$permitMonth = date("m", $m);
				$diff = $currentMonth - $permitMonth;

				$facilityData = Facility::where('id', $va->facility_id)->first();
				$processing_fee  = isset($facilityData->permit_processing_fee) ? $facilityData->permit_processing_fee : '0.00';
				$total_permit_amount = '0';
				$total_permit_amount += $va->permit_rate;
				// Alka, PIMS-10549, Date::14 oct 2024
				//	if ($va->permit_rate_id == config('parkengage.WEEKLY.PERMIT_RATE_ID')) {
				if (in_array($va->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_ID'))) {

					if ($va->business_id == 0 || $va->business_id == null) {

						$permitStartDate = Carbon::parse($va->desired_start_date);
						$permitEndDate = Carbon::parse($va->desired_end_date);

						$currentDate = Carbon::now()->format('Y-m-d');
						$fourthDay = $permitStartDate->copy()->addDays(3);

						$permitRequestRenew = PermitRequestRenewHistory::where('permit_request_id', $va->id)->orderBy('id', 'desc')->first();
						$daysDifference = 0;
						$oldEndDate = $va->desired_end_date;
						$newEndDate = $va->desired_end_date;

						if (isset($permitRequestRenew) && !empty($permitRequestRenew)) {
							$oldEndDate = Carbon::parse($permitRequestRenew->desired_end_date);
							$newStartDate = Carbon::parse($va->desired_start_date);
							$newEndDate = Carbon::parse($va->desired_end_date);
							$daysDifference = $oldEndDate->diffInDays($newStartDate, false);
						}
						if ($permitMonth == $currentMonth && $fourthDay->between($permitStartDate, $permitEndDate) && (Carbon::parse($currentDate) >= $fourthDay && (($newEndDate >= $oldEndDate) || ($daysDifference != 1)))) {

							$va->permit_renew_pay = 1;

							$endDate = new DateTime($permitEndDate);

							// Add one day to the end date
							$desiredStartDate = $endDate->modify('+1 day');
							$desired_start_date_new = $desiredStartDate->format('Y-m-d');
							$desired_start_date = $desired_start_date_new;

							$desiredEndDate = $desiredStartDate->modify('+6 day');
							$desired_end_date = $desiredEndDate->format('Y-m-d');

							$billing_start_date = $desired_start_date;
							$billing_end_date = $desired_end_date;
						} else {
							$va->permit_renew_pay = 0;
							$billing_start_date = '';
							$billing_end_date = '';
						}
					}
				} else {
					$currentMonth = Carbon::now()->format('m');
					$m = strtotime($va->desired_start_date);
					$permitMonth = date("m", $m);
					$diff = $currentMonth - $permitMonth;

					$billing_month = 0;
					if ($diff == 0) {
						$billing_month = 1;
					} else if ($diff == 1) {
						$billing_month = $diff;
					} else if ($diff > 1) {
						$billing_month = $diff + 1;
					}
					$billing_start_date = date("Y-m-01", strtotime("+" . $billing_month . " month", $m));
					$billing_end_date = date("Y-m-t", strtotime("+" . $billing_month . " month", $m));
					# PIMS-14421 - change for permit end date for yearly permit in USM Parking
					if (($va->partner_id == config('parkengage.PARTNER_USM')) && in_array($va->permit_rate_id, config('parkengage.USM_YEARLY_PERMIT_RATE_ID'))) {
						$billing_end_date = date(config('parkengage.USM_YEARLY_END_DATE'));
					}
					# PIMS-14421 - change for permit end date for yearly permit in USM Parking
				}

				// For USM Services
				if ($partner_id == config('parkengage.PARTNER_USM')) {
					$permit_services = PermitRequestServiceMapping::where('permit_request_id', $va->id)->pluck('permit_service_id');

					$services  = PermitServices::with([
						'permit_service_criteria_mappings' => function ($query) use ($permit_services) {
							$query->whereIn('permit_service_id', $permit_services)
								->with('criteria');
						}
					])
						->select('permit_services.*')
						->whereIn('id', $permit_services)
						->orderBy('permit_services.id', 'asc')
						->get();

					if (count($services) > 0) {
						foreach ($services as $permitService) {
							$total_permit_amount += (float) $permitService->permit_service_rate;
						}
					}
					$per_month_permit_amount = $total_permit_amount;

					if ($diff > 1) {
						$total_permit_amount = $total_permit_amount * $diff;
						$total_permit_amount += (float) $per_month_permit_amount;
					}
					$total_permit_amount += (float) $processing_fee;
					$per_month_permit_amount += (float) $processing_fee;
				} else {
					$per_month_permit_amount = $total_permit_amount;

					if ($diff > 1) {
						$total_permit_amount = $total_permit_amount * $diff;
						$total_permit_amount += (float) $per_month_permit_amount;
					}
					if ($total_permit_amount > 0) {
						$total_permit_amount += (float) $processing_fee;
					}
					$per_month_permit_amount += (float) $processing_fee;
				}

				$paymentDetail = '';
				$facility_payment_type = FacilityPaymentDetail::where('facility_id', $va->facility_id)->first();
				if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '1')) {
					$paymentDetails = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $va->user_id)->where('partner_id', $va->partner_id)->first();
				} else if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '2')) {
					$paymentDetails =  DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $va->user_id)->where('partner_id', $va->partner_id)->first();
				} else if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '4')) {
					$paymentDetails =  HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $va->user_id)->where('partner_id', $va->partner_id)->first();
				}
				$permitRate = PermitRate::where('id', $va->permit_rate_id)->first();
				$month = date('m');
				$m = strtotime($va->desired_start_date);
				$permitMonth = date("m", $m);
				//dd($month,$m,$permitMonth);
				$diff = $month - $permitMonth;
				$rate_amount = number_format($permitRate->rate, 2);

				// dd($rate_amount);
				/*
				if($month == $permitMonth){
					$rate_amount = "0.00";
				}else if($diff >1){
					$rate_amount = "0.00";
				}else{
					$rate_amount = number_format($permitRate->rate, 2);
				}
				*/
				if ($va->negotiated_amount > 0) {
					$permit_paid_amount = $va->negotiated_amount;
					$total_permit_amount = 0;
				} else {
					$permit_paid_amount = $va->permit_final_amount;
				}
				$paymentDate = isset($transaction->created_at) ? $transaction->created_at : '-';
				if ($paymentDate == '-') {
				} else {
					$paymentDate = date("Y-m-d H:i:s", strtotime($paymentDate));
				}
				// dd($paymentDate);
				#dushyant issue in weekly doc/permit
				if (($permitRate->rate == 0 || empty($permitRate->rate)) && empty($va->discount_amount)  && $va->permit_final_amount <= 0) {
					$total_permit_amount = 0;
				}

				if ($total_permit_amount <= 0) {
					$va->permit_renew_pay = 0;
				} //disable permit button if price is 0
				if (($va->partner_id == config('parkengage.PARTNER_USM')) && in_array($va->permit_rate_id, config('parkengage.USM_YEARLY_RESTRICTED_PERMIT_RATE_ID'))) {
					$va->permit_renew_pay = 0;
				}
				$totalBillCurrent = [];
				$totalBillCurrent[$ke]['account_number'] = $va->account_number;
				$totalBillCurrent[$ke]['from_date'] = $va->desired_start_date;
				$totalBillCurrent[$ke]['to_date'] = $va->desired_end_date;
				$totalBillCurrent[$ke]['due_from_date'] = $billing_start_date;
				$totalBillCurrent[$ke]['due_to_date'] = $billing_end_date;
				$totalBillCurrent[$ke]['permit_rate'] = sprintf("%.2f", $va->permit_rate);
				$totalBillCurrent[$ke]['paid_amount'] = sprintf("%.2f", $permit_paid_amount);
				$totalBillCurrent[$ke]['processing_fee'] = sprintf("%.2f", $va->processing_fee);
				$totalBillCurrent[$ke]['bill_amount'] = $total_permit_amount;
				$totalBillCurrent[$ke]['permit_renew_pay'] = $va->permit_renew_pay;
				$totalBillCurrent[$ke]['renewal_due_amount'] = $total_permit_amount;
				$totalBillCurrent[$ke]['permit_type_name'] = $va->permit_type_name;

				$totalBillCurrent[$ke]['per_month_permit_amount'] = $per_month_permit_amount;
				$totalBillCurrent[$ke]['total_permit_amount'] = $total_permit_amount;
				$totalBillCurrent[$ke]['payment_date'] = $paymentDate;

				$totalBillCurrent[$ke]['name_on_card'] = isset($paymentDetails->name) ? $paymentDetails->name : '-';
				$totalBillCurrent[$ke]['card_last_four'] = isset($paymentDetails->card_last_four) ? $paymentDetails->card_last_four : '-';
				$totalBillCurrent[$ke]['card_name'] = isset($paymentDetails->card_name) ? $paymentDetails->card_name : '-';
				$totalBillCurrent[$ke]['card_expiry'] = isset($paymentDetails->expiry) ? $paymentDetails->expiry : '-';
				$totalBillCurrent[$ke]['weekly_billing_details'] = [];

				//$totalBillCurrent[$ke]['is_weeklyPermit'] = ($va->permit_rate_id == config('parkengage.WEEKLY.PERMIT_RATE_ID')) ? 1 : 0;
				$totalBillCurrent[$ke]['is_weeklyPermit'] = (in_array($va->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_ID'))) ? 1 : 0;

				$revenueBalance = PermitRequestRenewHistory::with(['transaction'])->where('partner_id', $partner_id)->where('user_id', Auth::user()->id)->where('account_number', $va->account_number)->orderBy('id', 'DESC')->limit(12)->get();

				if ($revenueBalance) {
					$totalBill = [];
					foreach ($revenueBalance as $ke1 => $val) {
						$total_permit_amount += $val->permit_rate;
						$paymentDetail = '';
						$facility_payment_type = FacilityPaymentDetail::where('facility_id', $val->facility_id)->first();
						if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '1')) {
							$paymentDetails = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $val->user_id)->where('partner_id', $val->partner_id)->first();
						} else if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '2')) {
							$paymentDetails =  DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $val->user_id)->where('partner_id', $val->partner_id)->first();
						} else if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '4')) {
							$paymentDetails =  HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $val->user_id)->where('partner_id', $val->partner_id)->first();
						}
						$permitRateHis = PermitRate::where('id', $val->permit_rate_id)->first();
						$rate_amount = "0.00";
						if ($val->negotiated_amount > 0) {
							$permit_paid_amount = $val->negotiated_amount;
						} else {
							$permit_paid_amount = $val->permit_final_amount;
						}
						$paymentDate = isset($val->transaction->created_at) ? $val->transaction->created_at : '-';
						if ($paymentDate == '-') {
						} else {
							$paymentDate = date("Y-m-d H:i:s", strtotime($paymentDate));
						}
						$totalBill[$ke1]['account_number'] = $val->account_number;
						$totalBill[$ke1]['from_date'] = $val->desired_start_date;
						$totalBill[$ke1]['to_date'] = $val->desired_end_date;
						$totalBill[$ke1]['due_from_date'] = $billing_start_date;
						$totalBill[$ke1]['due_to_date'] = $billing_end_date;
						$totalBill[$ke1]['permit_rate'] = sprintf("%.2f", $val->permit_rate);
						$totalBill[$ke1]['paid_amount'] = sprintf("%.2f", $val->permit_final_amount);
						$totalBill[$ke1]['bill_amount'] = $rate_amount;
						$totalBill[$ke1]['permit_renew_pay'] = $va->permit_renew_pay;
						$totalBill[$ke1]['renewal_due_amount'] = $permitRateHis->rate;
						$totalBill[$ke1]['permit_type_name'] = $val->permit_type_name;
						$totalBill[$ke1]['name_on_card'] = isset($paymentDetails->name) ? $paymentDetails->name : '-';
						$totalBill[$ke1]['card_last_four'] = isset($paymentDetails->card_last_four) ? $paymentDetails->card_last_four : '-';
						$totalBill[$ke1]['card_name'] = isset($paymentDetails->card_name) ? $paymentDetails->card_name : '-';
						$totalBill[$ke1]['card_expiry'] = isset($paymentDetails->expiry) ? $paymentDetails->expiry : '-';
						$totalBill[$ke1]['payment_date'] = $paymentDate;
						//	$totalBill[$ke1]['is_weeklyPermit'] = ($va->permit_rate_id == config('parkengage.WEEKLY.PERMIT_RATE_ID')) ? 1 : 0;
						$totalBill[$ke1]['is_weeklyPermit'] = (in_array($va->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_ID'))) ? 1 : 0;


						//	if ($va->permit_rate_id == config('parkengage.WEEKLY.PERMIT_RATE_ID')) {
						if (in_array($va->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_ID'))) {
							$weeklyRevenueBalance = PermitRequestRenewHistory::with(['transaction'])->where('partner_id', $partner_id)->where('user_id', Auth::user()->id)->where('account_number', $va->account_number)->orderBy('id', 'DESC')->limit(12)->get();


							if ($va->permit_rate_id == config('parkengage.WEEKLY.PERMIT_RATE_ID')) {
								if (in_array($va->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_ID'))) {
									$totalBillCurrent[$ke]['weekly_billing_details']  = $totalBill;
								} else {
									$totalBillCurrent[$ke]['weekly_billing_details'] = [];
									$totalBillCurrent = array_merge($totalBillCurrent, $totalBill);
								}
							}
						}
					}

					$va->billing_details = array_values($totalBillCurrent);
				}

				$va->billing_details = $totalBillCurrent;
			}
			$data['permit_list']    = $result;
			return $data;
		}

		if (isset($request->month_year)) {
			$monthYear = $request->month_year;
			$from_date  = date($monthYear . '-01');
			$mid_date  = date($monthYear . '-15');
			$to_date  = date($monthYear . '-t');
			$midDateString = strtotime($mid_date);
			$lastdate = strtotime(date("Y-m-t", $midDateString));
			$to_date = date("Y-m-d", $lastdate);

			#22-08-2024 dushyant
			$fiveDaysBefore = $now->copy()->addDays(5);
			$permit_renew_date_visible = config('parkengage.PERMIT_RENEW_DATE');
			$currentDate = Carbon::now()->format('Y-m-d');

			$revenueBalance = PermitRequest::with(['transaction'])->select('id', 'account_number', 'facility_id', 'status', 'user_consent', 'desired_start_date', 'desired_end_date', 'permit_rate', 'permit_rate_id', 'permit_rate', 'status', 'cancelled_at', 'permit_type_name', 'permit_final_amount', 'processing_fee', 'negotiated_amount');
			if (isset($permit_renew_date_visible)) {
				$revenueBalance = $revenueBalance->selectRaw("
					IF(
						(business_id is null || business_id=0) AND
						((DATE_FORMAT(?, '%Y-%m') = DATE_FORMAT(desired_end_date, '%Y-%m') AND DAY(?) >= ?) OR
						(DATE(?) > DATE(desired_end_date))),
						1, 0
					) as permit_renew_pay", [$currentDate, $currentDate, $permit_renew_date_visible, $currentDate]);
			} else {
				$revenueBalance = $revenueBalance->selectRaw("IF((business_id is null || business_id=0) and desired_end_date <= ?, 1, 0) as permit_renew_pay", [$fiveDaysBefore]);
			}
			$revenueBalance = $revenueBalance->where('partner_id', $partner_id)->where('user_id', Auth::user()->id)->whereDate('desired_start_date', '>=', $from_date)->whereDate('desired_end_date', '<=', $to_date)->get();

			if ($revenueBalance) {
				$totalAmount = 0;
				foreach ($revenueBalance as $key => $value) {
					$currentMonth = Carbon::now()->format('m');
					$m = strtotime($value->desired_start_date);
					$permitMonth = date("m", $m);
					$diff = $currentMonth - $permitMonth;
					$facilityData = Facility::where('id', $value->facility_id)->first();
					$processing_fee  = isset($facilityData->permit_processing_fee) ? $facilityData->permit_processing_fee : '0.00';

					$total_permit_amount = '0';

					$total_permit_amount += $value->permit_rate;


					// For USM Services
					if ($partner_id == config('parkengage.PARTNER_USM')) {
						$permit_services = PermitRequestServiceMapping::where('permit_request_id', $value->id)->pluck('permit_service_id');

						$services  = PermitServices::with([
							'permit_service_criteria_mappings' => function ($query) use ($permit_services) {
								$query->whereIn('permit_service_id', $permit_services)
									->with('criteria');
							}
						])
							->select('permit_services.*')
							->whereIn('id', $permit_services)
							->orderBy('permit_services.id', 'asc')
							->get();

						if (count($services) > 0) {
							foreach ($services as $permitService) {
								$total_permit_amount += (float) $permitService->permit_service_rate;
							}
						}
						$per_month_permit_amount = $total_permit_amount;
						if ($diff > 1) {
							$total_permit_amount = $total_permit_amount * $diff;
						}
						$total_permit_amount += (float) $processing_fee;
						$total_permit_amount += (float) $per_month_permit_amount;
						$per_month_permit_amount += (float) $processing_fee;
					} else {
						$per_month_permit_amount = $total_permit_amount;
						$per_month_permit_amount += (float) $processing_fee;
						if ($diff > 1) {
							$total_permit_amount = $total_permit_amount * $diff;
						}
						$total_permit_amount += (float) $processing_fee;
						$total_permit_amount += (float) $per_month_permit_amount;
						$per_month_permit_amount += (float) $processing_fee;
					}

					$paymentDetail = '';
					$facility_payment_type = FacilityPaymentDetail::where('facility_id', $value->facility_id)->first();
					if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '1')) {
						$paymentDetails = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $value->user_id)->where('partner_id', $value->partner_id)->first();
					} else if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '2')) {
						$paymentDetails =  DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $value->user_id)->where('partner_id', $value->partner_id)->first();
					} else if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '4')) {
						$paymentDetails =  HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $value->user_id)->where('partner_id', $value->partner_id)->first();
					}
					$permitRate = PermitRate::where('id',  $value->permit_rate_id)->first();
					$month = date('m');
					$m = strtotime($value->desired_start_date);
					$permitMonth = date("m", $m);
					if ($month == $permitMonth) {
						$rate_amount = "0.00";
					} else {
						$rate_amount = number_format($permitRate->rate, 2);
					}
					if ($value->negotiated_amount > 0) {
						$permit_paid_amount = $value->negotiated_amount;
					} else {
						$permit_paid_amount = $value->permit_final_amount;
					}
					$paymentDate = isset($value->transaction->created_at) ? $value->transaction->created_at : '-';
					if ($paymentDate == '-') {
					} else {
						$paymentDate = date("Y-m-d H:i:s", strtotime($paymentDate));
					}

					//if ($value->permit_rate_id == config('parkengage.WEEKLY.PERMIT_RATE_ID')) {
					if (in_array($value->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_ID'))) {
						if ($value->business_id == 0 || $value->business_id == null) {

							$permitStartDate = Carbon::parse($value->desired_start_date);
							$permitEndDate = Carbon::parse($value->desired_end_date);

							$currentDate = Carbon::now()->format('Y-m-d');
							$fourthDay = $permitStartDate->copy()->addDays(3);

							$permitRequestRenew = PermitRequestRenewHistory::where('permit_request_id', $value->id)->orderBy('id', 'desc')->first();
							$daysDifference = 0;
							$oldEndDate = $value->desired_end_date;
							$newEndDate = $value->desired_end_date;

							if (isset($permitRequestRenew) && !empty($permitRequestRenew)) {
								$oldEndDate = Carbon::parse($permitRequestRenew->desired_end_date);
								$newStartDate = Carbon::parse($value->desired_start_date);
								$newEndDate = Carbon::parse($value->desired_end_date);
								$daysDifference = $oldEndDate->diffInDays($newStartDate, false);
							}
							if ($permitMonth == $currentMonth && $fourthDay->between($permitStartDate, $permitEndDate) && (Carbon::parse($currentDate) >= $fourthDay && ($newEndDate >= $oldEndDate || $daysDifference != 1))) {
								$value->permit_renew_pay = 1;

								$endDate = new DateTime($permitEndDate);

								// Add one day to the end date
								$desiredStartDate = $endDate->modify('+1 day');
								$desired_start_date_new = $desiredStartDate->format('Y-m-d');
								$desired_start_date = $desired_start_date_new;

								$desiredEndDate = $desiredStartDate->modify('+6 day');
								$desired_end_date = $desiredEndDate->format('Y-m-d');

								$billing_start_date = $desired_start_date;
								$billing_end_date = $desired_end_date;
							} else {
								$value->permit_renew_pay = 0;
								$billing_start_date = '';
								$billing_end_date = '';
							}
						}
					} else {
						$currentMonth = Carbon::now()->format('m');
						$m = strtotime($value->desired_start_date);
						$permitMonth = date("m", $m);
						$diff = $currentMonth - $permitMonth;

						$billing_month = 0;
						if ($diff == 0) {
							$billing_month = 1;
						} else if ($diff == 1) {
							$billing_month = $diff;
						} else if ($diff > 1) {
							$billing_month = $diff + 1;
						}
						$billing_start_date = date("Y-m-01", strtotime("+" . $billing_month . " month", $m));
						$billing_end_date = date("Y-m-t", strtotime("+" . $billing_month . " month", $m));
						# PIMS-14421 - change for permit end date for yearly permit in USM Parking
						if (($value->partner_id == config('parkengage.PARTNER_USM')) && in_array($value->permit_rate_id, config('parkengage.USM_YEARLY_PERMIT_RATE_ID'))) {
							$billing_end_date = date(config('parkengage.USM_YEARLY_END_DATE'));
						}
						# PIMS-14421 - change for permit end date for yearly permit in USM Parking
					}

					$totalBillCurrent[$key]['from_date'] = $value->desired_start_date;
					$totalBillCurrent[$key]['to_date'] = $value->desired_end_date;
					$totalBillCurrent[$key]['due_from_date'] = $billing_start_date;
					$totalBillCurrent[$key]['due_to_date'] = $billing_end_date;
					$totalBillCurrent[$key]['permit_rate'] = sprintf("%.2f", $value->permit_rate);
					$totalBillCurrent[$key]['paid_amount'] = sprintf("%.2f", $permit_paid_amount);
					$totalBillCurrent[$key]['bill_amount'] = number_format($rate_amount, 2);
					$totalBillCurrent[$key]['renewal_due_amount'] = $permitRate->rate;
					$totalBillCurrent[$key]['total_permit_amount'] = $total_permit_amount;
					$totalBillCurrent[$key]['name_on_card'] = isset($paymentDetails->name) ? $paymentDetails->name : '-';
					$totalBillCurrent[$key]['card_last_four'] = isset($paymentDetails->card_last_four) ? $paymentDetails->card_last_four : '-';
					$totalBillCurrent[$key]['card_name'] = isset($paymentDetails->card_name) ? $paymentDetails->card_name : '-';
					$totalBillCurrent[$key]['card_expiry'] = isset($paymentDetails->expiry) ? $paymentDetails->expiry : '-';
					$totalBillCurrent[$key]['payment_date'] = $paymentDate;
					$value->billing_details = $totalBillCurrent;
				}
			}
		} else {

			$defaultMonths = 12;

			$totalBill = [];
			for ($i = 1; $i <= $defaultMonths; $i++) {
				$currentMonth = Carbon::now()->format('m');
				$monthYear =  date('Y-m', strtotime("-$i month"));
				$from_date  = date($monthYear . '-01');
				$mid_date  = date($monthYear . '-15');
				$to_date  = date($monthYear . '-t');
				$midDateString = strtotime($mid_date);
				$lastdate = strtotime(date("Y-m-t", $midDateString));
				$to_date = date("Y-m-d", $lastdate);
				$revenueBalance = PermitRequestRenewHistory::with(['transaction'])->select('id', 'account_number', 'facility_id', 'user_consent', 'desired_start_date', 'desired_end_date', 'permit_rate', 'permit_rate_id', 'permit_rate', 'cancelled_at', 'permit_type_name', 'permit_final_amount', 'processing_fee', 'negotiated_amount')->where('partner_id', $partner_id)->where('user_id', Auth::user()->id)->whereDate('desired_start_date', '>=', $from_date)->whereDate('desired_end_date', '<=', $to_date)->get();
				$total_permit_amount = '0';
				if ($revenueBalance) {
					$totalAmount = 0;
					foreach ($revenueBalance as $key => $value) {
						$total_permit_amount += $value->permit_rate;
						$paymentDetail = '';
						$facility_payment_type = FacilityPaymentDetail::where('facility_id', $value->facility_id)->first();
						if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '1')) {
							$paymentDetails = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $value->user_id)->where('partner_id', $value->partner_id)->first();
						} else if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '2')) {
							$paymentDetails =  DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $value->user_id)->where('partner_id', $value->partner_id)->first();
						} else if (isset($facility_payment_type) && ($facility_payment_type->facility_payment_type_id == '4')) {
							$paymentDetails =  HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $value->user_id)->where('partner_id', $value->partner_id)->first();
						}
						$permitRate = PermitRate::where('id',  $value->permit_rate_id)->first();
						$month = date('m');
						$m = strtotime($value->desired_start_date);
						$permitMonth = date("m", $m);
						if ($month == $permitMonth) {
							$rate_amount = "0.00";
						} else {
							$rate_amount = $total_permit_amount;
						}
						if ($value->negotiated_amount > 0) {
							$permit_paid_amount = $value->negotiated_amount;
						} else {
							$permit_paid_amount = $value->permit_final_amount;
						}
						$paymentDate = isset($value->transaction->created_at) ? $value->transaction->created_at : '-';
						if ($paymentDate == '-') {
						} else {
							$paymentDate = date("Y-m-d H:i:s", strtotime($paymentDate));
						}

						//	if ($value->permit_rate_id == config('parkengage.WEEKLY.PERMIT_RATE_ID')) {
						if (in_array($value->permit_rate_id, config('parkengage.WEEKLY.PERMIT_RATE_ID'))) {
							if ($value->business_id == 0 || $value->business_id == null) {

								$permitStartDate = Carbon::parse($value->desired_start_date);
								$permitEndDate = Carbon::parse($value->desired_end_date);

								$currentDate = Carbon::now()->format('Y-m-d');
								$fourthDay = $permitStartDate->copy()->addDays(3);

								$permitRequestRenew = PermitRequestRenewHistory::where('permit_request_id', $value->id)->orderBy('id', 'desc')->first();
								$daysDifference = 0;
								$oldEndDate = $value->desired_end_date;
								$newEndDate = $value->desired_end_date;

								if (isset($permitRequestRenew) && !empty($permitRequestRenew)) {
									$oldEndDate = Carbon::parse($permitRequestRenew->desired_end_date);
									$newStartDate = Carbon::parse($value->desired_start_date);
									$newEndDate = Carbon::parse($value->desired_end_date);
									$daysDifference = $oldEndDate->diffInDays($newStartDate, false);
								}
								if ($permitMonth == $currentMonth && $fourthDay->between($permitStartDate, $permitEndDate) && (Carbon::parse($currentDate) >= $fourthDay && ($newEndDate >= $oldEndDate || $daysDifference != 1))) {
									$value->permit_renew_pay = 1;

									$endDate = new DateTime($permitEndDate);

									// Add one day to the end date
									$desiredStartDate = $endDate->modify('+1 day');
									$desired_start_date_new = $desiredStartDate->format('Y-m-d');
									$desired_start_date = $desired_start_date_new;

									$desiredEndDate = $desiredStartDate->modify('+6 day');
									$desired_end_date = $desiredEndDate->format('Y-m-d');

									$billing_start_date = $desired_start_date;
									$billing_end_date = $desired_end_date;
								} else {
									$value->permit_renew_pay = 0;
									$billing_start_date = '';
									$billing_end_date = '';
								}
							}
						} else {
							$currentMonth = Carbon::now()->format('m');
							$m = strtotime($value->desired_start_date);
							$permitMonth = date("m", $m);
							$diff = $currentMonth - $permitMonth;

							$billing_month = 0;
							if ($diff == 0) {
								$billing_month = 1;
							} else if ($diff == 1) {
								$billing_month = $diff;
							} else if ($diff > 1) {
								$billing_month = $diff + 1;
							}
							$billing_start_date = date("Y-m-01", strtotime("+" . $billing_month . " month", $m));
							$billing_end_date = date("Y-m-t", strtotime("+" . $billing_month . " month", $m));
							# PIMS-14421 - change for permit end date for yearly permit in USM Parking
							if (($value->partner_id == config('parkengage.PARTNER_USM')) && in_array($value->permit_rate_id, config('parkengage.USM_YEARLY_PERMIT_RATE_ID'))) {
								$billing_end_date = date(config('parkengage.USM_YEARLY_END_DATE'));
							}
							# PIMS-14421 - change for permit end date for yearly permit in USM Parking
						}

						$totalBillCurrent = [];
						$totalBillCurrent[$key]['from_date'] = $value->desired_start_date;
						$totalBillCurrent[$key]['to_date'] = $value->desired_end_date;
						$totalBillCurrent[$key]['due_from_date'] = $billing_start_date;
						$totalBillCurrent[$key]['due_to_date'] = $billing_end_date;
						$totalBillCurrent[$key]['permit_rate'] = sprintf("%.2f", $value->permit_rate);
						$totalBillCurrent[$key]['paid_amount'] = sprintf("%.2f", $value->permit_final_amount);
						$totalBillCurrent[$key]['bill_amount'] = $rate_amount;
						$totalBillCurrent[$key]['renewal_due_amount'] = $permitRate->rate;
						$totalBillCurrent[$key]['total_permit_amount'] = $total_permit_amount;
						$totalBillCurrent[$key]['name_on_card'] = isset($paymentDetails->name) ? $paymentDetails->name : '-';
						$totalBillCurrent[$key]['card_last_four'] = isset($paymentDetails->card_last_four) ? $paymentDetails->card_last_four : '-';
						$totalBillCurrent[$key]['card_name'] = isset($paymentDetails->card_name) ? $paymentDetails->card_name : '-';
						$totalBillCurrent[$key]['card_expiry'] = isset($paymentDetails->expiry) ? $paymentDetails->expiry : '-';
						$totalBillCurrent[$key]['payment_date'] = $paymentDate;
						$value->billing_details = $totalBillCurrent;
					}
				} else {
					throw new ApiGenericException('There is no invoice available at this moment.');
				}
			}
		}
		if ($revenueBalance->count() > 0) {
			$data['permit_list']    = $revenueBalance;
			return $data;
		} else {
			throw new ApiGenericException('There is no invoice available at this moment.');
		}
	}

	public function getInvoicePdf(Request $request)
	{
		$from_date     = date($request->from_date . '-01');
		$mid_date      = date($request->from_date . '-15');
		$to_date       = date($request->to_date . '-t');
		$midDateString = strtotime($mid_date);
		$lastdate      = strtotime(date("Y-m-t", $midDateString));
		$to_date       = date("Y-m-d", $lastdate);

		$last_from_date = date("Y-m-d", strtotime($from_date . "-1 month"));
		$last_to_date = date("Y-m", strtotime($from_date . "-1 month"));
		$last_mid_date  = date($last_to_date . '-15');
		$lastMidDateString = strtotime($last_mid_date);
		$lastLastdate = strtotime(date("Y-m-t", $lastMidDateString));
		$last_to_date = date("Y-m-d", $lastLastdate);

		$brand_setting = BrandSetting::where('user_id', $request->partner_id)->first();
		$partnerDetails = User::where('id', $request->partner_id)->first();

		//last month revenye
		$revenueBalance = PermitRequest::with(['facility', 'user', 'transaction'])->where('partner_id', $request->partner_id)->where('account_number', $request->permit_number)->whereDate('desired_start_date', '>=', $from_date)->whereDate('desired_end_date', '<=', $to_date)->first();

		if ($revenueBalance) {
			$currentMonth = Carbon::now()->format('m');
			$m = strtotime($revenueBalance->desired_start_date);
			$permitMonth = date("m", $m);
			$diff = $currentMonth - $permitMonth;

			$billing_month = 0;

			if ($diff == 0) {
				$billing_month = 1;
			} else if ($diff == 1) {
				$billing_month = $diff;
			} else if ($diff > 1) {
				$billing_month = $diff + 1;
			}

			$revenueBalance->billing_start_date = date("Y-m-01", strtotime("+" . $billing_month . " month", $m));
			$revenueBalance->billing_end_date = date("Y-m-t", strtotime("+" . $billing_month . " month", $m));

			$revenueBalance->brand_setting = $brand_setting;
			$revenueBalance->partner_details = $partnerDetails;

			#PIMS-10032 dushyant
			$revenueBalance->permit_rate = ($revenueBalance->negotiated_amount ?? $revenueBalance->permit_final_amount);

			$lastrevenueBalance = PermitRequestRenewHistory::with(['facility', 'user', 'transaction'])->where('partner_id', $request->partner_id)->where('account_number', $request->permit_number)->whereDate('desired_start_date', '>=', $last_from_date)->whereDate('desired_end_date', '<=', $last_to_date)->first();
			if ($lastrevenueBalance) {
				$revenueBalance->lastrevenueBalance = $lastrevenueBalance;
			}
			$pdf = (new PermitRequest())->generatePermitInvoice($revenueBalance, Pdf::class);
			return $this->respondWithPdf($pdf);
		} else {
			//last month revenue
			$lastRevenueHistory = PermitRequestRenewHistory::with(['facility', 'user', 'transaction'])->where('partner_id', $request->partner_id)->where('account_number', $request->permit_number)->whereDate('desired_start_date', '>=', $from_date)->whereDate('desired_end_date', '<=', $to_date)->first();
			if ($lastRevenueHistory) {

				$currentMonth = Carbon::now()->format('m');
				$m = strtotime($lastRevenueHistory->desired_start_date);
				$permitMonth = date("m", $m);
				$diff = $currentMonth - $permitMonth;

				$billing_month = 0;
				if ($diff == 0) {
					$billing_month = 1;
				} else if ($diff == 1) {
					$billing_month = $diff;
				} else if ($diff > 1) {
					$billing_month = $diff + 1;
				}

				$lastRevenueHistory->billing_start_date = date("Y-m-01", strtotime("+" . $billing_month . " month", $m));
				$lastRevenueHistory->billing_end_date = date("Y-m-t", strtotime("+" . $billing_month . " month", $m));

				$lastRevenueHistory->brand_setting = $brand_setting;
				$lastRevenueHistory->partner_details = $partnerDetails;
				$lastrevenueBalance = PermitRequestRenewHistory::with(['facility', 'user', 'transaction'])->where('partner_id', $request->partner_id)->where('account_number', $request->permit_number)->whereDate('desired_start_date', '>=', $last_from_date)->whereDate('desired_end_date', '<=', $last_to_date)->first();
				if ($lastrevenueBalance) {
					$lastRevenueHistory->lastrevenueBalance = $lastrevenueBalance;
				}
				$pdf = (new PermitRequest())->generatePermitInvoice($lastRevenueHistory, Pdf::class);
				return $this->respondWithPdf($pdf);
			} else {
				$errror = [
					"status" => 202,
					"data" => null,
					"errors" => array("message" => 'Sorry! No Permit Invoice generated for this month')
				];
				return response()->json($errror, 202);
				//return response()->json(['message' => 'Sorry! No Permit Invoice generated for this month'], 422);

				//throw new ApiGenericException('Sorry! No Permit Invoice generated for this month.');
			}
		}
	}
	#PIMS-10032 dushyant
	public function getBusinessInvoicePdf(Request $request)
	{

		$businessAccounts = User::where('attendent_type', 'business_account')->where('is_active', 1)->where('id', '370136')->get();
		if ($businessAccounts) {
			foreach ($businessAccounts as $businessAccount) {
				$business_id = $businessAccount->id;
				$partner_id = $businessAccount->created_by;

				$brand_setting = BrandSetting::where('user_id', $partner_id)->first();
				$partnerDetails = User::where('id', $partner_id)->first();

				$facility = '';
				if (isset($business_id) && !empty($business_id)) {
				} else {
					continue;
				}

				$user_id = $business_id;

				$permitBundleDetails = UserPermitBundleMapping::where('user_id', $user_id)->first();
				if (isset($permitBundleDetails) && isset($permitBundleDetails->permit_bundle)) {
					$permitBundle = $permitBundleDetails->permit_bundle;
				} else {
					$permitBundle = NULL;
				}
				/* permit allocation */
				if ($permitBundle) {
					$result = [];
					$userTotal = UserPermitTypeMapping::where('user_id', $user_id)
						->join('permit_rates as prs', function ($join) use ($user_id) {
							$join->on('permit_type_id', '=', 'prs.id');
						})
						->select(
							'prs.name as permit_rates',
							'permit_type_id',
							'capacity as no_of_users',
							DB::raw('CASE WHEN permit_negotiable_price IS NULL THEN capacity * permit_price ELSE capacity * permit_negotiable_price END as purchase_amount'),
							DB::raw('CASE WHEN permit_negotiable_price IS NULL THEN permit_price ELSE  permit_negotiable_price END as permit_final_amount')

						)
						->get();
					//return $userTotal;
					foreach ($userTotal as $permitAllocation) {
						$permitCnt = PermitRequest::where('business_id', '=', $user_id)
							->whereNotNull('anet_transaction_id')
							->whereNull('deleted_at')
							->where('permit_rate_id', $permitAllocation->permit_type_id)->count();
						$no_of_users = $permitAllocation->no_of_users - $permitCnt;
						$permitAllocation->no_of_users = $no_of_users;
						$permitAllocation->purchase_amount = $permitAllocation->permit_final_amount  * $no_of_users;
					}
					$result = $userTotal;
				} else {
					$results = DB::table('permit_requests as pr')
						->leftjoin('users_permit_type_mapping as uptm', function ($join) use ($user_id) {
							$join->on('uptm.permit_type_id', '=', 'pr.permit_rate_id')
								->where('pr.business_id', '=', $user_id)
								->where('uptm.user_id', '=', $user_id)
								->whereNull('uptm.deleted_at');
						})
						->join('permit_rates as prs', function ($join) use ($user_id) {
							$join->on('uptm.permit_type_id', '=', 'prs.id');
						})
						->select(
							'pr.permit_rate_id',
							DB::raw('MAX(prs.name) as permit_rates'),
							DB::raw('COUNT(pr.permit_rate_id) as no_of_users'),
							DB::raw('MAX(pr.permit_final_amount) as permit_final_amount'),
							DB::raw('MAX(pr.permit_final_amount) * COUNT(pr.permit_rate_id) as purchase_amount'),
							DB::raw('MAX(uptm.permit_negotiable_price) as permit_negotiable_price'),
							DB::raw('MAX(uptm.permit_price) as permit_price'),
							DB::raw('MAX(uptm.capacity) as capacity'),
							DB::raw('MAX(uptm.remaining_capacity) as remaining_capacity')
						)
						->where('pr.business_id', '=', $user_id)
						->whereNull('pr.anet_transaction_id')
						->whereNull('pr.deleted_at')
						->whereNull('pr.cancelled_at')
						->groupBy('pr.permit_rate_id');

					$result = $results->orderBy('permit_rate_id', 'DESC')->get();
				}


				$data['permit_allocation'] = $result;

				if ($permitBundle) {
					if (isset($facility_id)) {
						$facility = Facility::with(['facilityConfiguration'])->select('id', 'short_name', 'license_format', 'license_min_lenght', 'license_max_lenght', 'fix_length', 'permit_processing_fee')->where('id', $facility_id)->first();
						$permit_processing_fee = isset($facility->permit_processing_fee) ? $facility->permit_processing_fee : '0.00';
					} else {
						$permit_processing_fee = '0.00';
					}

					$userTotal = UserPermitTypeMapping::where('user_id', $user_id)
						->select(
							DB::raw('SUM(capacity) as total_count'),
							DB::raw('SUM(CASE WHEN permit_negotiable_price IS NULL THEN capacity * permit_price ELSE capacity * permit_negotiable_price END) as total_amount')
						)
						->first();
					if ($userTotal->total_count > 0) {
						$processing_fee = $permit_processing_fee * $userTotal->total_count;
						$userTotal->permit_processing_fee = $processing_fee;
						$userTotal->permit_amount =  $userTotal->total_amount - $processing_fee;
						$data['total_max'] = $userTotal;
					}
					$userPayment = UserPaymentBundleMapping::where('user_id', $user_id)
						->select(
							DB::raw('SUM(capacity) as total_count,SUM(final_amount) as total_amount')
						)
						->first();
					if ($userPayment->total_count > 0) {
						$processing_fee = $permit_processing_fee * $userPayment->total_count;
						$userPayment->permit_processing_fee = $processing_fee;
						$userPayment->permit_amount =  $userPayment->total_amount - $processing_fee;
						$data['user_payment'] = $userPayment;
					}

					if (($userTotal->total_count - $userPayment->total_count) > 0) {
						$unpaid = [
							"total_amount" => $userTotal->total_amount - $userPayment->total_amount,
							"total_count" => $userTotal->total_count - $userPayment->total_count,
							"permit_processing_fee" => $userTotal->permit_processing_fee - $userPayment->permit_processing_fee,
							"permit_amount" => $userTotal->permit_amount - $userPayment->permit_amount,
						];
						$data['payment_unpaid'] = (object) $unpaid;
					} else {
						if (isset($data['total_max'])) {
							$data['payment_total'] = $data['total_max'];
							$data['payment_unpaid'] = [];
						} else {
							$unpaid = PermitRequest::where('business_id', $user_id)->whereNull('anet_transaction_id')
								->select(DB::raw('SUM(permit_final_amount) as total_amount, COUNT(*) as total_count,sum(processing_fee) as permit_processing_fee,SUM(permit_final_amount) - SUM(processing_fee) as permit_amount'))
								->first();
							if ($unpaid->total_count > 0) {
								$unpaid->permit_bundle = $permitBundle;
								$data['payment_unpaid'] = $unpaid;
							}
						}
					}
				} else {
					$unpaid = PermitRequest::where('business_id', $user_id)->whereNull('anet_transaction_id')
						->select(DB::raw('SUM(permit_final_amount) as total_amount, COUNT(*) as total_count,sum(processing_fee) as permit_processing_fee,SUM(permit_final_amount) - SUM(processing_fee) as permit_amount'))
						->first();
					if ($unpaid->total_count > 0) {
						$unpaid->permit_bundle = $permitBundle;
						$data['payment_unpaid'] = $unpaid;
					} else {
						$data['payment_unpaid'] = [];
						$total = PermitRequest::where('business_id', $user_id)->whereNotNull('anet_transaction_id')
							->select(DB::raw('SUM(permit_final_amount) as total_amount, COUNT(*) as total_count,sum(processing_fee) as permit_processing_fee,SUM(permit_final_amount) - SUM(processing_fee) as permit_amount'))
							->first();
						if ($total->total_count > 0) {
							$total->permit_bundle = $permitBundle;
							$data['payment_total'] = $total;
						} else {
							$data['payment_total'] = [];
						}
					}
				}
				$month = date('Y-m', strtotime("-1 month"));
				$from_date  = date($month . '-01');
				$mid_date  = date($month . '-15');
				$to_date  = date($month . '-t');
				$midDateString = strtotime($mid_date);
				$lastdate = strtotime(date("Y-m-t", $midDateString));
				$to_date = date("Y-m-d", $lastdate);

				if (isset($data['payment_unpaid']) && count($data['payment_unpaid']) > 0) {
					$paymentUnpaid = $data['payment_unpaid'];
					$data = ['billingDetail' => $result, 'user' => $businessAccount, 'partnerDetails' => $partnerDetails, 'brandSetting' => $brand_setting, 'paymentUnpaid' => $paymentUnpaid, 'from_date' => $from_date, 'to_date' => $to_date];

					$pdf = (new PermitRequest())->generateBusinessPermitReminderInvoice($data, Pdf::class);
					$fileName = 'ParkEngage - Invoice-' . $business_id . date('Ym')  . '.pdf';

					try {
						Mail::send(
							"permitInvoice.invoice-email-report",
							$data,
							function ($message) use ($businessAccount, $pdf, $from_date, $to_date) {
								$message->bcc(['<EMAIL>'])->to([$businessAccount->email])->subject("Invoice Reminder Details Of " . date("M j, Y", strtotime($from_date)) . ' To ' . date("M j, Y", strtotime($to_date)));

								$message->from(config('parkengage.default_sender_email'));
								$message->attachData($pdf, "Parking - Invoice - " . date("Ym", strtotime($from_date)) . ".pdf");
							}
						);
					} catch (Exception $e) {
						$this->log->error($e);
					}

					return  view("permitInvoice.reminder-billing", $data);
				} else {
					return 1;
					continue;
				}
			}
		}
	}
}
