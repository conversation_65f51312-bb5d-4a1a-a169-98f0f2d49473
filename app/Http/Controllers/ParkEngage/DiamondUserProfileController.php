<?php

namespace App\Http\Controllers\ParkEngage;

use Illuminate\Http\Request;
use App\Http\Requests;
use Auth;
use App\Http\Controllers\Controller;
use Hash;
use Exception;
use Artisan;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Models\PermitRequest;
use App\Services\LoggerFactory;
use App\Models\Facility;
use App\Models\User;
use Mail;
use App\Models\OauthClient;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\UserPermitVehicle;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use Response;
use Excel;
use App\Models\UserPass;
use App\Models\Reservation;
use App\Http\Helpers\QueryBuilder;
use App\Models\Ticket;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\MstMake;
use App\Models\ParkEngage\MstModel;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\HeartlandPaymentProfile;

class DiamondUserProfileController extends Controller
{


    protected  $request;
    protected  $log;
    protected $errorLog;
    protected $user;


    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/diamond/profile')->createLogger('user-profile');
    }



    public function getUserProfileById(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $current_date = date('Y-m-d');
        $userData = Auth::user();
        $userData = User::with('permit')->where('id', $userData->id)->first();
        $permitCount = PermitRequest::where('user_id', $userData->id)->where('partner_id', $secret->partner_id)->count();
        // ->whereNull("cancelled_at")
        #$activePermitCount = PermitRequest::where('user_id', $userData->id)->whereDate('desired_end_date', '>=', $current_date)->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->where('status', 1)->count();
        $activePermitCount = PermitRequest::where('user_id', $userData->id)->whereDate('grace_end_date', '>=', $current_date)->whereNotNull('anet_transaction_id')->where('status', 1)->count();

        $permitRequest = PermitRequest::where('user_id', $userData->id)->pluck('id');
        //return $permitRequest;
        // ->where('anon',$userData->anon) - temparory remove this check for usm vehicle by pass
        // $vehicleCount = PermitVehicle::whereIn('permit_request_id', $permitRequest)->count(); // PIMS-13575 : addded anon check
        $vehicleCount = PermitVehicle::whereIn('permit_request_id', $permitRequest)->where('anon', '0')->count();
        if ($vehicleCount == 0) {
            $vehicleCount = PermitVehicle::where('user_id', $userData->id)->where('anon', '0')->count();
        }
        $activePassCount = UserPass::where('user_id', $userData->id)->whereDate('end_date', '>=', $current_date)->count();

        //return $vehicleCount;
        $userData['permit_count']           = $permitCount;
        $userData['active_permit_count']    = $activePermitCount;
        $userData['vehicle_count']          = $vehicleCount;
        $userData['active_pass_count']      = $activePassCount;
        return  $userData;
    }

    public function updateUserProfile(Request $request)
    {
        $this->log->info("updateUserProfile : " . json_encode($request->all()));
        $userData = [];
        $user = User::find(Auth::user()->id);

        if (!$user) {
            throw new ApiGenericException("User not exists");
        }

        // Get country Code
        $countryCode = QueryBuilder::appendCountryCode();

        // Check for Phone No. Already Exist
        if (isset($request->phone)) {
            $phone = $countryCode . $request->phone;
            $checkPhone = User::where('phone', $phone)->where("created_by", $user->created_by)->first();
            if (!$checkPhone) {
                $user->phone = $phone;
            } else {
                if ($user->id != $checkPhone->id) {
                    if ($checkPhone->anon == 1 || $checkPhone->anon == "1") {
                        $oldUserId = $checkPhone->id;
                        $checkPhone->delete();
                        $ticket = Ticket::where("user_id", $oldUserId)->update(["user_id" => $user->id]);
                        $authorizeNetTransaction = AuthorizeNetTransaction::where("user_id", $oldUserId)->update(["user_id" => $user->id]);
                        $user->phone = $phone;
                    } else if ($request->phone == '') {
                    } else {
                        throw new ApiGenericException("Phone number already exists");
                    }
                }
            }
        }

        if (isset($request->license_number)) {
            $user->license_number = $request->license_number;
        }
        if (isset($request->address)) {
            $user->address = $request->address;
        }
        if (isset($request->address2)) {
            $user->address2 = $request->address2;
        }
        if (isset($request->city)) {
            $user->city = $request->city;
        }
        if (isset($request->state)) {
            $user->state = $request->state;
        }
        if (isset($request->zipcode)) {
            $user->pincode = $request->zipcode;
        }
        if (isset($request->user_consent)) {
            $user->user_consent = $request->user_consent;
        }
        if (isset($request->company_name)) {
            $user->company_name = $request->company_name;
        }
        if (isset($request->user_prefrences)) {
            $user->user_prefrences = $request->user_prefrences;
        }
        if (isset($request->country)) {
            $user->country = $request->country;
        }
        $msg = '';
        if (isset($request->is_auto_checkout_enabled)) {
            $user->is_auto_checkout_enabled = $request->is_auto_checkout_enabled;
            if ($request->is_auto_checkout_enabled == '1' || $request->is_auto_checkout_enabled == 1) {
                $vehicleExist = PermitVehicle::where("user_id", $user->id)->first();
                if (!$vehicleExist) {
                    $msg = "Pls add vehicle and/or payment method details in your account for this feature to work properly.";
                }

                $planetCard = PlanetPaymentProfile::where("user_id", $user->id)->first();
                $dcCard = DatacapPaymentProfile::where("user_id", $user->id)->first();
                $hlCard = HeartlandPaymentProfile::where("user_id", $user->id)->first();
                if (!$planetCard && !$dcCard && !$hlCard) {
                    $msg = "Pls add vehicle and/or payment method details in your account for this feature to work properly.";
                }
            }
        }
        $user->save();
        $PermitRequest = '';
        if (($request->permit_id != '') && ($request->permit_id != 'undefined')) {
            $PermitRequest = PermitRequest::find($request->permit_id);

            if ($request->file('image_front')) {
                //   $this->validate($request, ['image_front' => 'mimes:png|jpeg|gif|SVG'], ['images.image_front' => 'Image  must be a file of type : .png,jpeg,gif,SVG']);
                $front = $request->file('image_front');
                $file_extension = $front->getClientOriginalExtension();
                $file_name = $PermitRequest->account_number . '_front.' . $file_extension;

                $destination_path = storage_path("app/monthly-permit-user");
                if (!$front->move($destination_path, $file_name)) {
                    throw new ApiGenericException("Something went wrong while uploading image");
                }
                $PermitRequest->image_front = $file_name;
            }

            if ($request->file('image_back')) {

                //  $this->validate($request, ['image_back' => 'mimes:png|jpeg|gif|SVG'], ['images.image_back' => 'Image  must be a file of type : .png,jpeg,gif,SVG']);
                $back = $request->file('image_back');
                $file_extension = $back->getClientOriginalExtension();
                $file_name = $PermitRequest->account_number . '_back.' . $file_extension;
                $destination_path = storage_path("app/monthly-permit-user");
                if (!$back->move($destination_path, $file_name)) {
                    throw new ApiGenericException("Something went wrong while uploading image");
                }
                $PermitRequest->image_back = $file_name;
            }
            $PermitRequest->user_consent = $request->user_consent;
            $PermitRequest->save();
        }

        return $userData = ['user' => $user, 'permit' => $PermitRequest];
    }


    public function addVehicle(Request $request)
    {
        $this->log->info("Add Vehicle Request: " . json_encode($request->all()));
        $user = User::find(Auth::user()->id);
        $vehicle_count = PermitVehicle::where('user_id', Auth::user()->id)->get();
        $vehicle_input   = count($request->vehicleList);
        if ($vehicle_input > 5) {
            throw new ApiGenericException("Vehicle Count Limit Exceed.");
        }
        if ($vehicle_count && ($user->created_by != config('parkengage.PARTNER_USM'))) {
            if ($vehicle_count->count() == 5) {
                throw new ApiGenericException("Vehicle Count Limit Exceed.");
            } else if ($vehicle_input > 5) {
                throw new ApiGenericException("Vehicle Count Limit Exceed.");
            } else if (($vehicle_count->count() - $vehicle_input) == 0) {
                $vehicle_present = $vehicle_count->count();
                $count1 = $vehicle_present - $vehicle_input;
                $count2 = $vehicle_input - $vehicle_present;
            }
        }

        $permit = PermitRequest::where('user_id', $user->id)->get();
        /*
		if(!$permit){
            throw new ApiGenericException("Please ask admin to approve the Permit before adding the Vehicle.");  
        }
		*/
        $vehicle = [];
        //return $request->vehicleList;
        if ($request->vehicleList) {
            //return $request->vehicleList;
            foreach ($request->vehicleList as $key => $value) {
                $vehicle_obj = PermitVehicle::where('license_plate_number', $value['license_plate'])->where('partner_id', $user->created_by)->first();
                if ($vehicle_obj) {
                    #PIMS-10864 DD 26-09-2024
                    $partner_id = isset($user->created_by) ? $user->created_by : '';
                    $val = (object) $value;
                    $vehicle     = PermitVehicle::permitVehicleFilterData($val, $user->id, $partner_id, 1);
                    PermitVehicle::where('id', $vehicle_obj->id)->update($vehicle);
                    if ($permit) {
                        foreach ($permit as $ke => $va) {
                            //$mapping = new PermitVehicleMapping();
                            $mapping = PermitVehicleMapping::firstOrNew(['permit_request_id' => $va->id, 'permit_vehicle_id' => $vehicle_obj->id]);
                            $mapping->permit_request_id = $va->id;
                            $mapping->permit_vehicle_id = $vehicle_obj->id;
                            $mapping->save();
                        }
                    }
                    throw new ApiGenericException("Vehicle already exists");
                } else {
                    #PIMS-10864 DD 26-09-2024
                    $partner_id = isset($user->created_by) ? $user->created_by : '';
                    $val = (object) $value;
                    $vehicle     = PermitVehicle::permitVehicleFilterData($val, $user->id, $partner_id, 1);
                    $vehicleData =  PermitVehicle::create($vehicle);
                    $userVehicleData =  UserPermitVehicle::create($vehicle);
                    if ($permit) {
                        foreach ($permit as $ke => $va) {
                            // $mapping = new PermitVehicleMapping();
                            $mapping = PermitVehicleMapping::firstOrNew(['permit_request_id' => $va->id, 'permit_vehicle_id' => $vehicleData->id]);

                            //  $mapping->permit_request_id = $va->id;
                            //  $mapping->permit_vehicle_id = $vehicleData->id;
                            $mapping->save();
                        }
                    }
                }
            }
            $vehicle = PermitVehicle::where('user_id', Auth::user()->id)->get();
            return $vehicle;
        } else {
            throw new ApiGenericException("Invalid Data Format");
        }
    }

    public function getVehicleList(Request $request)
    {
        $limit = isset($request->latest) ? $request->latest : '';
        $user = User::find(Auth::user()->id);
        //$vehicleList = PermitVehicle::with('permit.facility')->where('user_id',$user->id)->paginate(10);

        $distinctVehicles = PermitVehicle::select('id', 'license_plate_number')
            ->where('user_id', $user->id)
            ->where('anon', '0')
            ->groupBy('license_plate_number')
            ->get();

        $distinctVehicleIds = $distinctVehicles->pluck('id');

        $vehicleList = PermitVehicle::with('permit.facility')
            ->whereIn('id', $distinctVehicleIds);
        if ($limit == '5') {
            $vehicleList = $vehicleList->orderBy('id', 'desc')->paginate(5);
        } else {
            if ($user->created_by = config('parkengage.PARTNER_USM')) {
                $vehicleList = $vehicleList->paginate(100);
            } else {
                $vehicleList = $vehicleList->paginate(10);
            }
        }
        return $vehicleList;
    }

    public function getVehicleByid($id)
    {

        $user = User::find(Auth::user()->id);
        $is_vehicle = PermitVehicle::where('id', $id)->where('user_id', $user->id)->first();
        if (!$is_vehicle) {
            throw new ApiGenericException("Vehicle Not Found.");
        }

        return $is_vehicle;
    }


    public function updateVehicle(Request $request)
    {
        $vehicle = PermitVehicle::where('id', $request->id)->first();
        if ($vehicle) {
            #01-10-2024 DD model,make changes
            $vehicles     = (object) PermitVehicle::permitVehicleFilterData($request, $request->user_id, NULL, 0);
            $vehicle->license_plate_number = $vehicles->license_plate_number;
            $vehicle->make      =   $vehicles->make;
            $vehicle->make_id   =   $vehicles->make_id;
            $vehicle->model_id  =   $vehicles->model_id;
            $vehicle->color_id  =   $vehicles->color_id;
            $vehicle->model     =   $vehicles->model;
            $vehicle->color     =   $vehicles->color;
            $vehicle->user_id   =   $vehicles->user_id;
            $vehicle->permit_request_id = isset($request->permit_request_id) ? $request->permit_request_id : '';
            //	$vehicle->vehicle_type = $request->vehicle_type;
            //	$vehicle->status = 1;
            #PIMS-10905 DD
            if (isset($request->status_default) && $request->status_default) {
                PermitVehicle::where(['user_id' => $vehicles->user_id, 'partner_id' => $vehicle->partner_id])->where('id', '!=', $request->id)->update(['is_default' => 0]);
                $vehicle->is_default = 1;
            }
            $vehicle->save();
            return $vehicle;
        } else {
            throw new ApiGenericException("Sorry! Record Not Found.");
        }
    }

    public function deleteVehicle($id)
    {

        $user = User::find(Auth::user()->id);
        $is_vehicle = PermitVehicle::find($id);
        if (!$is_vehicle) {
            throw new ApiGenericException("Vehicle Not Found.");
        }

        $vehicleCount = PermitVehicle::where('user_id', $user->id)->get();
        if (count($vehicleCount) == 1) {
            $userVehicle = PermitVehicle::where('user_id', $user->id)->first();
            if ($userVehicle) {
                $userVehicle->is_default = 1;
                $userVehicle->save();
            }
            throw new ApiGenericException('Default Vehicle Can not be Deleted');
        } else {
            $mapping = PermitVehicleMapping::where('permit_vehicle_id', $is_vehicle->id)->delete();
            $is_vehicle->delete();
            return "Data successfully deleted.";
        }

        //$is_vehicle->delete();
        // return "Data successfully deleted.";

    }


    public function getFrontUrl($id)
    {
        if (!$id) {
            throw new NotFoundException('Please send valid id.');
        }
        $permitRequest = PermitRequest::find($id);
        if (!$permitRequest) {
            throw new NotFoundException('No logo image with that name found.');
        }
        $file = Storage::disk('local')->get("monthly-permit-user/" . $permitRequest->image_front) ?: null;
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/monthly-permit-user/' . $permitRequest->image_front));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }

    public function getBackUrl($id)
    {
        if (!$id) {
            throw new NotFoundException('Please send valid id.');
        }
        $permitRequest = PermitRequest::find($id);
        if (!$permitRequest) {
            throw new NotFoundException('No logo image with that name found.');
        }
        $file = Storage::disk('local')->get("monthly-permit-user/" . $permitRequest->image_back) ?: null;
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/monthly-permit-user/' . $permitRequest->image_back));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }




    public function getpermitImportData(Request $request)
    {
        $file = storage_path('import/') . 'Book1.xlsx';
        $data = Excel::load($file)->get();
        $vehicleList = [];
        foreach ($data as $key => $value) {
            if ($key == 'license_plate') {
                $vehicleList['license_plate'] = $value['license_plate'];
            }
        }
        $data = array_merge($data, $vehicleList);
        return $data;
    }


    public function getUserBooking(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
            if ($secret->partner_id != Auth::user()->created_by) {
                throw new ApiGenericException('No Record Found.');
            }
        }
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
                $result  = [];
                $result["total"] = 0;
                $result["per_page"] = 20;
                $result["current_page"] = 1;
                $result["last_page"] = 1;
                $result["next_page_url"] = Null;
                $result["prev_page_url"] = Null;
                $result["from"] = Null;
                $result["to"] = Null;
                $result["data"] = [];
                return $result;
            } else {
                //  $partner_id = Auth::user()->id; 
                $partner_id = '';
            }
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }

        $reservation = Reservation::with(['ticket', 'facility.facilityConfiguration', 'user', 'userPass', 'rate.rateType', 'transaction'])
            // As discussed with Vijay: Loeksh: 10-Oct-2024
            // ->whereNotNull('anet_transaction_id')
            ->where(function ($query) {
                $query->where('user_id', Auth::user()->id)
                    ->orWhere('on_behalf', Auth::user()->id);
            });

        if (isset($request->search)) {
            $reservation = $reservation->where('ticketech_code', 'like', "%$request->search%");
            $reservation = $reservation->orWhereHas(
                'user',
                function ($query) use ($request) {
                    $query
                        ->where('phone', 'like', "%{$request->search}%")
                        ->orWhere('email', 'like', "%{$request->search}%");
                }
            );
            $reservation = $reservation->orWhereHas(
                'userPass',
                function ($query) use ($request) {
                    $query
                        ->where('pass_code', 'like', "%{$request->search}%");
                }
            );
            $reservation = $reservation->orWhereHas(
                'transaction',
                function ($query) use ($request) {
                    $query
                        ->where('ref_id', 'like', "%{$request->search}%");
                }
            );
        }

        if ($partner_id) {
            $reservation = $reservation->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
        }
        if ($request->sort != '') {
            $reservation = $reservation->orderBy($request->sort, $request->sortBy);
        } else {
            $reservation = $reservation->orderBy('id', 'DESC');
        }

        $reservation = $reservation->paginate(20);
        return $reservation;
    }


    public function updateVehicleDetails(Request $request)
    {
        $user = User::find(Auth::user()->id);
        if ($request->vehicleList) {

            foreach ($request->vehicleList as $key => $value) {

                $vehicleExistCheck = PermitVehicle::where([
                    ['license_plate_number', '=', $value['license_plate']],
                    ['partner_id', '=', $user->created_by],
                    ['user_id', '!=', $user->id]
                ])->first();

                if ($vehicleExistCheck) {
                    throw new ApiGenericException("Vehicle already exists");
                }

                $vehicle_obj = PermitVehicle::where([
                    ['partner_id', '=', $user->created_by],
                    ['id', '=', $value['id']]
                ])->first();

                if ($vehicle_obj) {
                    #PIMS-10864 DD 26-09-2024
                    $partner_id = isset($user->created_by) ? $user->created_by : '';
                    $val = (object) $value;
                    $vehicle     = PermitVehicle::permitVehicleFilterData($val, $user->id, $partner_id, 1);
                    PermitVehicle::where('id', $vehicle_obj->id)->update($vehicle);
                    //make default and update default status 0 which has status 1 by vikrant
                    if (isset($value['status_default']) && $value['status_default'] == "1") {
                        PermitVehicle::where('user_id', $user->id)->where("id", '!=', $vehicle_obj->id)->update(['is_default' => '0']);
                    }
                    return "Vehicle updated successfully.";
                } else {
                    throw new ApiGenericException("Vehicle details not exists");
                }
            }
            // return "Vehicle updated successfully.";	
        } else {
            throw new ApiGenericException("Invalid Data Format");
        }
    }
}
