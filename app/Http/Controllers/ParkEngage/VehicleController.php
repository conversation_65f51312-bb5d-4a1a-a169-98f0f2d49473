<?php

namespace App\Http\Controllers\ParkEngage;

use App\Classes\CommonFunctions;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\ParkEngage\Vehicle;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserNotFound;
use App\Services\LoggerFactory;
use App\Models\OauthClient;
use App\Models\ParkEngage\MstModel;
use App\Models\PermitRequest;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\Ticket;

class VehicleController extends Controller
{


    protected $log;
    public function __construct(LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/user-vehicle')->createLogger('vehicle');
    }

    /**
     * Register api
     *
     * @return \Illuminate\Http\Response
     */
    public function addVehicle(Request $request)
    {
        //dd($request->all());
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        //  $this->validate($request, Vehicle::$addValidation);
        $user = Auth::user();

        if (!$user) {
            throw new UserNotFound('No corresponding user found for this account.');
        }

        try {
            //$user_vehicle = Vehicle::where('user_id', $user->id)->first();
            $user_vehicle = PermitVehicle::where('user_id', $user->id)->first();

            $vehicleExist = PermitVehicle::where('user_id', $user->id)->where('license_plate_number', $request->license_plate_number)->first();

            if ($vehicleExist) {
                throw new ApiGenericException("vehicle already exist");
            }

            $status = 0;

            //  $vehicle = Vehicle::firstOrNew(['user_id' => $user->id, 'licence_plate_no' => $request->licence_plate_no]);
            $vehicle = PermitVehicle::firstOrNew(['user_id' => $user->id, 'license_plate_number' => $request->license_plate_number]);
            //$vehicle->licence_plate_no = $request->licence_plate_no;
            $vehicle->license_plate_number = $request->license_plate_number;
            $vehicle->make = $request->make;
            $vehicle->model = $request->model;
            $vehicle->user_id = $user->id;
            //  $vehicle->make = $request->make;
            $vehicle->color = $request->color;
            $vehicle->partner_id = $secret->partner_id;

            $vehicle->state_id = $request->state_id;
            $vehicle->state_name = $request->state_name;

            if (!$user_vehicle) {
                $vehicle->is_default = 1;
            } else {
                if (is_numeric($request->is_default) || is_numeric($request->status_default) || ($request->status_default == true)) {

                    if (is_numeric($request->is_default)) {
                        $status = $request->is_default;
                    }

                    if (is_numeric($request->status_default)) {
                        $status = $request->status_default;
                    }

                    if ($request->status_default == true) {
                        $status = 1;
                    }

                    if (!empty($status)) {

                        PermitVehicle::where(['user_id' => $user->id])->update(array('is_default' => 0));
                    }


                    $vehicle->is_default = $status;
                }
            }

            $vehicle->save();

            //add vehicle
            $this->log->info("user add vehicle : " . json_encode($vehicle));

            return $vehicle;
        } catch (\Exception $e) {

            throw new ApiGenericException($e->getMessage());
        }
    }

    public function getVehicle(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $user = Auth::user();

        return PermitVehicle::where('user_id', $user->id)->where('partner_id', $secret->partner_id)->get();
    }

    public function getDefaultVehicle(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $user = Auth::user();
        $result = array();
        $vehicle = PermitVehicle::where('user_id', $user->id)->where('is_default', 1)->where('partner_id', $secret->partner_id)->first();

        if ($vehicle) {
            $result = $vehicle;
        }

        return $result;
    }

    public function editVehicle(Request $request)
    {

        $vehicle_id = $request->vehicle_id;
        //$rule = PermitVehicle::$editValidation;
        //$rule['licence_plate_no'] = 'unique:vehicles,licence_plate_no,' . $vehicle_id;

        //$this->validate($request, $rule);

        $user = Auth::user();

        if (!$user) {
            throw new UserNotFound('No corresponding user found for this account.');
        }

        $isVehicle = PermitVehicle::where('user_id', $user->id)->where('license_plate_number', $request->license_plate_number)->whereNotIn('id', [$vehicle_id])->first();
        if ($isVehicle) {
            throw new ApiGenericException('Vehicle already exist');
        }

        try {
            $status = isset($request->status_default) ? $request->status_default : 0;
            if ($status) {
                PermitVehicle::where('user_id', $user->id)->update(array('is_default' => 0));
            }

            $vehicle = PermitVehicle::where('user_id', $user->id)->where('id', $vehicle_id)->first();

            if ($vehicle) {
                $vehicleCount = PermitVehicle::where('user_id', $user->id)->count();
                if (($vehicleCount > 1) && ($status == 0)) {
                    $vehicleNext = PermitVehicle::where('user_id', $user->id)->where('id', '!=', $vehicle_id)->first();
                    if ($vehicleNext) {
                        //$vehicleNext->is_default = '1';
                        $vehicleNext->save();
                        $vehicle->is_default = $status;
                    }
                } else if (($vehicleCount == 1) && ($status == 0)) {
                    $vehicle->is_default = '1';
                } else {
                    $vehicle->is_default = $status;
                }
                $vehicle->license_plate_number = $request->license_plate_number;
                //$vehicle->make_model = $request->make_model;
                $vehicle->make = $request->make;
                $vehicle->model = $request->model;
                $vehicle->color = $request->color;
                // $vehicle->is_default = $status;

                $vehicle->state_id = $request->state_id;
                $vehicle->state_name = $request->state_name;

                $vehicle->save();

                //edit vehicle
                $this->log->info("user edit vehicle : " . json_encode($vehicle));

                if (isset($request->status_default) && $request->status_default == true || $request->status_default == 1) {

                    $userVehicle = PermitVehicle::where('user_id', $user->id)->get();
                    foreach ($userVehicle as $val) {
                        if ($val->id == $request->vehicle_id) {
                            PermitVehicle::where('id', $request->vehicle_id)->update(array('is_default' => 1));
                        } else {
                            PermitVehicle::where('id', $val->id)->update(array('is_default' => 0));
                        }
                    }
                }

                return $vehicle;
            } else {

                throw new ApiGenericException('vehicle Not exist');
            }
        } catch (\Exception $e) {

            throw new ApiGenericException($e->getMessage());
        }
    }


    public function deleteVehicle(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }
        }
        $user = Auth::user();
        $data = array();
        $id = $request->id;

        if (!$user) {
            throw new UserNotFound('No corresponding user found for this account.');
        }
        try {
            $vehicle = PermitVehicle::where('id', $id)->where('user_id', $user->id)->where('partner_id', $secret->partner_id)->first();

            if ($vehicle) {
                $this->log->info("user delete  vehicle : " . json_encode($vehicle));
                $vehicle->delete();
                $data['message'] = "vehicle deleted";
                //check if user not has only one vehcile so make it default
                $vehicleCount = Vehicle::where('user_id', $user->id)->get();
                if (count($vehicleCount) == 1) {
                    $userVehicle = PermitVehicle::where('user_id', $user->id)->where('partner_id', $secret->partner_id)->first();
                    if ($userVehicle) {
                        $userVehicle->is_default = 1;
                        $userVehicle->save();
                    }
                }

                return $data;
            } else {

                throw new ApiGenericException('vehicle Not belongs to user');
            }
        } catch (\Exception $e) {

            throw new ApiGenericException($e->getMessage());
        }
    }

    // Find unmapped ticket for user
    public function mapUserWithTicket($user, $license_plate) {
        $this->log->info("mapUserWithTicket: user " . json_encode($user) . "License Plate: $license_plate");
        $tickets = Ticket::where('license_plate',$license_plate)->whereNull('user_id')->get();
        if(count($tickets) > 0){
            foreach($tickets as $ticket){
                $this->log->info("User update for {$ticket->license_plate}" . json_encode($user) . "License Plate: $license_plate");
                $ticket->user_id = $user->id;
                $ticket->save();
            }
        } else {
            $this->log->info("not Ticket Found for this user" . json_encode($user) . "License Plate: $license_plate");
        }
    }

    public function addVehicleROC(Request $request)
    {
        $this->log->info("Add Vehicle Request: " . json_encode($request->all()));

        $clientSecret = $request->header('X-ClientSecret');
        if (!empty($clientSecret)) {
            $secret = OauthClient::where('secret', $clientSecret)->first();
            if (!$secret) {
                throw new ApiGenericException('No partner found.');
            }
        }

        $user = Auth::user();
        if (!$user) {
            throw new ApiGenericException('No corresponding user found for this account.');
        }
        // dd($user->id, $user->email, $user->phone, $user->created_by, $secret->partner_id);

        $vehicleData = PermitVehicle::where('user_id', $user->id)->where('partner_id', $secret->partner_id)->get();
        $vehicle_input   = count($request->vehicleList);

        $vehicle_count = CommonFunctions::getVehicleLimit($secret->partner_id, null, $this->log);
        $this->log->info("Vehicle limit in database: " . json_encode($vehicle_count));


        $isNotUSMPartner = $user->created_by != config('parkengage.PARTNER_USM');

        // Check if vehicle input exceeds allowed count
        if ($vehicle_count > 0 && $vehicle_input > $vehicle_count && $isNotUSMPartner) {
            throw new ApiGenericException("Vehicle Limit Exceed.");
        }

        // Single vehicle logic
        $message = $duplicateMsg = "";


        // Handle both single vehicle and vehicle list cases
        $vehicles = $request->has('vehicleList') ? $request->input('vehicleList') : [$request->all()];

        $permit = PermitRequest::where('user_id', $user->id)->get(); #DD PIMS-10864 add permitvehiclemapping

        $addedVehicles = [];
        $duplicateVehicles = [];

        if (count($vehicles) === 1) {
            $vehicleData = $vehicles[0];

            // Check if the vehicle already exists
            $existingVehicle = PermitVehicle::where('user_id', $user->id)->where('partner_id', $secret->partner_id)
                ->where('license_plate_number', $vehicleData['license_plate'])
                ->first();

            // dd($existingVehicle, $user->id, $secret->partner_id, $vehicleData['license_plate']);

            if ($existingVehicle) {
                // lokesh: marking as comment this code as disscussed with Ujjwal and dushyant for UPBL-114
                // $this->processPermitVehicleMapping($permit, $existingVehicle->id); #DD PIMS-10864

                // mapped ticket with revPass
                $this->mapUserWithTicket($user, $existingVehicle->license_plate_number);
                
                // If it's a single vehicle and already exists, throw 500 error
                throw new ApiGenericException("Vehicle with license plate {$vehicleData['license_plate']} already exists");
            }
            // Process the makeModel
            $this->processMakeModel($vehicleData);

            // Process the vehicle
            $vehicle = $this->processVehicle($vehicleData, $user, $secret);

            // mapped ticket with revPass
            $this->mapUserWithTicket($user, $vehicle->license_plate_number);

            #PIMS-14916 || Dev: Sagar || 13/08/2025
            if(isset($vehicleData['permit_ids']) && count($vehicleData['permit_ids']) > 0) {
                $permitIds = $vehicleData['permit_ids'];
                $this->processPermitVehicleMappingByIds($permitIds, $vehicle->id);
            }

            // lokesh: marking as comment this code as disscussed with Ujjwal and dushyant for UPBL-114
            // $this->processPermitVehicleMapping($permit, $vehicle->id); #DD PIMS-10864

            return "Vehicle added successfully";
        } else {
            // Multiple vehicle logic
            foreach ($vehicles as $vehicleData) {
                // Check if the vehicle already exists
                $existingVehicle = PermitVehicle::where('user_id', $user->id)->where('partner_id', $secret->partner_id)
                    ->where('license_plate_number', $vehicleData['license_plate'])
                    ->first();

                if ($existingVehicle) {
                    if($secret->partner_id != config('parkengage.PARTNER_MANLO')){ #pims-13995 don't mapp vehicle with all exist permit in case of menlo
                        // $this->processPermitVehicleMapping($permit, $existingVehicle->id); #DD PIMS-10864
                    }
                    $duplicateMsg = "{$vehicleData['license_plate']}, ";
                    continue; // Skip this vehicle and continue to the next one
                }

                // Process the makeModel
                $this->processMakeModel($vehicleData);

                // Process the vehicle
                $vehicle = $this->processVehicle($vehicleData, $user, $secret);

                #PIMS-14916 || Dev: Sagar || 13/08/2025
                if(isset($vehicleData['permit_ids']) && count($vehicleData['permit_ids']) > 0) {
                    $permitIds = $vehicleData['permit_ids'];
                    $this->processPermitVehicleMappingByIds($permitIds, $vehicle->id);
                }

                // if($secret->partner_id != config('parkengage.PARTNER_MANLO')){ 
                //     $this->processPermitVehicleMapping($permit, $vehicle->id); #DD PIMS-10864
                // }
            }
            $message .= "Vehicle added successfully";
            if (!empty($duplicateMsg)) {
                $message .= " but license plate $duplicateMsg already exists";
            }
            return $message;
        }
    }

    #DD PIMS-10864

    private function processPermitVehicleMapping($permit,$vehicleId){
        if ($permit) {
            foreach ($permit as $ke => $va) {
                $mapping = PermitVehicleMapping::firstOrNew(['permit_request_id' => $va->id, 'permit_vehicle_id' => $vehicleId]);
                $mapping->permit_request_id = $va->id;
                $mapping->permit_vehicle_id = $vehicleId;
                $mapping->save();
            }
        }
    }

    #PIMS-14916 || Dev: Sagar || 13/08/2025
    private function processPermitVehicleMappingByIds($permitIds,$vehicleId){
        if ($permitIds) {
            foreach ($permitIds as $ke => $pId) {
                $mapping = PermitVehicleMapping::firstOrNew(['permit_request_id' => $pId, 'permit_vehicle_id' => $vehicleId]);
                $mapping->permit_request_id = $pId;
                $mapping->permit_vehicle_id = $vehicleId;
                $mapping->save();
            }
        }
    }
    

    #End DD PIMS-10864
    
    private function processVehicle($vehicleData, $user, $secret)
    {
        $isDefault = $this->resolveDefaultStatus($vehicleData, $user, $secret->partner_id);
    
        $vehicle = PermitVehicle::firstOrNew([
            'user_id'              => $user->id, 
            'license_plate_number' => $vehicleData['license_plate'],
            'partner_id' => $secret->partner_id
        ]);
    
        $vehicle->fill([
            'license_plate_number' => $vehicleData['license_plate'],
            'make_id'                 => $vehicleData['make_id'] ?? NULL,
            'model_id'                => $vehicleData['model_id'] ?? NULL,
            'color_id'                => $vehicleData['color_id'] ?? NULL,
            'country'                   => $vehicleData['country'] ?? NULL,
            'partner_id'           => $secret->partner_id ?? null,
            'state_id'             => $vehicleData['state_id'] ?? NULL,
            'state_name'           => $vehicleData['state_name'] ?? NULL,
            'style'                   => $vehicleData['style'] ?? NULL,
            'style_id'                => $vehicleData['style_id'] ?? NULL,
            'is_default'           => $isDefault,
            'vehicle_type_id'      => $vehicleData['vehicle_type_id'] ?? NULL #pims-13318 #DD
        ]);
    
        $vehicle->save();

        return $vehicle; #DD PIMS-10864
    
        $this->log->info("User added vehicle: " . json_encode($vehicle));
    }

    private function processMakeModel(array &$vehicleData)  {
        if(isset($vehicleData['make']) && !empty($vehicleData['make'])){
            $makeModleData = PermitVehicle::createOrUpdateMakeAndModel($vehicleData['make'], $vehicleData['model']);
            $vehicleData['model_id'] = $makeModleData['model_id'];
            $vehicleData['make_id'] = $makeModleData['make_id'];
            unset($vehicleData['make'], $vehicleData['model']);
        } else if(isset($vehicleData['model']) && !empty($vehicleData['model'])){
            $modelData = MstModel::findOrCreateModel($vehicleData['model'], $vehicleData['make_id']);
            $vehicleData['model_id'] = $modelData->id;
            unset($vehicleData['model']);
        }
    }
     
    private function resolveDefaultStatus($vehicleData, $user, $partner_id)
    {
        // Check if the user has a vehicle or if the request marks this as default
        $hasExistingVehicle = PermitVehicle::where(['user_id' => $user->id, 'partner_id' => $partner_id])->exists();
        $isDefault = !$hasExistingVehicle ? 1 : 0;
    
        if (is_numeric($vehicleData['is_default'] ?? null) || is_numeric($vehicleData['status_default'] ?? null) || ($vehicleData['status_default'] ?? false) == true) {
            $status = $vehicleData['is_default'] ?? $vehicleData['status_default'];
            $isDefault = ($status == true || $status == 1) ? 1 : 0;
    
            if ($isDefault) {
                // Reset other vehicles' default status
                PermitVehicle::where(['user_id' => $user->id, 'partner_id' => $partner_id])->update(['is_default' => 0]);
            }
        }
    
        return $isDefault;
    }
}
