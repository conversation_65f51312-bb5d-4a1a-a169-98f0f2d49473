<?php
namespace App\Http\Controllers\ParkEngage;
use Auth;
use Artisan;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use Exception;
use Carbon\Carbon;
use App\Models\ParkEngage\ParkingDevice;
use App\Exceptions\ApiGenericException;


class ParkingDeviceController extends Controller
{
	

    public function index()
    {
         $parkingdevice = ParkingDevice::with('facility','gates')->paginate(10);

        return $parkingdevice;
    }
    public function store(Request $request)
    {  
        return "yes";
        
        $par_device= ParkingDevice::create([
            'serial_number'=> $request->serial_number,
            'facility_id'=> 132,
            'partner_id'=> Auth::user()->id,
            'gate_id'=> "", // this is settled for syntax error: lokesh : 8Aug2025
            'ip'=> $request->device_ip,
            'port_number'=>$request->port_number,
            'gate_type'=>$request->gate_type,
            'terminal_id'=> $request->terminal_id,
            'is_active'=>$request->terminal_status
        ]);

      return $par_device;
    }



}
