<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use Response;
use Hash;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Models\CustomText;
use App\Models\UserPass;
use Excel;
use App\Models\ParkEngage\RevenueBalance;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use App\Classes\ParkengageGateApi;
use Storage;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\ParkEngage\ParkingDeviceTypes;
use App\Models\OauthClient;

use App\Models\Event;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\ParkEngage\EventCategoryEvent;
use App\Models\ParkEngage\UserSession;
use App\Models\HoursOfOperation;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\PermitRequest;
use App\Models\PermitTicket;
use App\Models\ParkEngage\LicensePlate;
use App\Models\ParkEngage\TransactionData;
use App\Classes\PlanetPaymentGateway;
use App\Models\Devicetoken;
use App\Models\ParkEngage\UserFacility;
use App\Models\ParkEngage\WorldportLicensePlate;
use App\Jobs\SendSms;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Models\ParkEngage\KstreetLicensePlate;
use Illuminate\Support\Fluent;
use DB;
use App\Models\ParkEngage\PromocodeRequest;
use App\Classes\LatestPromoCodeLib;
use App\Models\ParkEngage\WhitelistUser;
use App\Models\PromoCode;
use App\Models\PromoUsage;
use App\Models\Promotion;
use App\Classes\DatacapPaymentGateway;
use App\Models\ParkEngage\PartnerPaymentGateway;
use App\Models\ParkEngage\WhitelistUserCard;

// VP:PIMS-14662
use App\Services\IM30\Im30Service;
use App\Models\ParkEngage\LPRFeed;
use App\Services\LPR\LprTransientService;
use App\Services\Transient\TicketHelper;

class ColonialCheckinCheckoutController extends Controller
{

	protected $log;
	protected $user;
	protected $partnerPaymentDetails;
	protected $authNet;
	protected $facility;
	protected $im30Service;
	protected $request;
	protected $lprService;
	protected $ticketHelper;

	const RESERVATION_THRESHOLD_TYPE = 2;
	const TWENTY_FOUR_HOURS = 23;
	const REALTIME_WINDOW   = 2;
	const DEFAULT_VALUE  = 0;
	const DEFAULT_WEB  = "web";
	const DEFAULT_VALUE_STR  = "0";
	const DEFAULT_VALUE_ONE  = 1;
	const DEFAULT_MSG_ONE = 1;
	const DEFAULT_PERCENTAGE_FLAG = 1;
	const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
	const DEFAULT_MSG_TWO = 2;
	const DEFAULT_MSG_THREE = 3;
	const DEFAULT_MSG_FOUR = 4;
	const ADD_EXTRA_DAY_COUNT = 1;
	const COUPON_RATE   = 2;
	const DEFAULT_TIME      = '0000-00-00 00:00:00';
	const MIN_AVAILABILITY  = 5;
	const LIMITED_SPACE_MSG = "Limited spots still available at this price.";
	const SOME_SPACE_MSG = "Some spots are available.";
	const No_SPACE_MSG = "Sold Out At This Time.";
	const NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

	const FACILITY_AVAILABLE = "Facility available for reservation.";
	const FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
	const FACILITY_COMING_SOON = "Facility coming soon.";

	const SHARE_TICKET_AMOUNT = 0;
	const SHARE_PASS_AMOUNT = 0;

	const EVENT_THRESHOLD_TYPE = 2;
	const CLIENT_LIVE_CARD_SESSION_ID = "6049741011541240372";
	const PICKUP_TYPE = 0;
	const CHECKIN_NOTIFICATION = "IM30_CHECKIN";
	const CHECKOUT_NOTIFICATION = "IM30_CHECKOUT";
	const RES_CHECKIN_NOTIFICATION = "CHECK_IN";
	const RES_CHECKOUT_NOTIFICATION = "CHECKOUT";

	const PARTNER_ID = "2980";
	const BASE_RATE = "Base Rate";
	const HOURLY_RATE = "Hourly Rate";

	use DispatchesJobs;
	const QUEUE_NAME = 'sms-send';
	const QUEUE_ENTRY = 'read-license-plate';

	const ANTIPASSBACK_COUNT = 20;

	public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
	{
		$this->request = $request;
		$this->authNet = $authNet;
		$this->cim = $cim;
		// Use these validation rules if new billing information is being sent through
		$this->billingValidation = PaymentProfile::$creditCardValidation;

		$this->log = $logFactory->setPath('logs/parkengage/im30')->createLogger('colonial');
		$this->im30Service = new Im30Service('im30');
		$this->ticketHelper = new TicketHelper();
	}


	public function setDecryptedCard(Request $request)
	{
		if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
			return;
		}
		$key = env('PCI_ENCRYPTION_KEY');
		$mc = new MagicCrypt($key, 256);
		$decryptedNonce = $mc->decrypt($request->nonce);
		$cardData = explode(':', $decryptedNonce);
		$zipCode = isset($cardData[4]) ? $cardData[4] : '';
		$request->request->add(
			[
				'name_on_card' => $cardData[0],
				'card_number' => $cardData[1],
				'expiration_date' => $cardData[2],
				'security_code' => $cardData[3],
				'zip_code' => $zipCode
			]
		);

		$this->request = $request;
	}

	protected function getBillingArray()
	{
		$name = $this->request->name_on_card ?: $this->user->name;
		$zip = $this->request->zip_code ?: false;

		$nameArray = explode(' ', trim($name));

		return [
			'first_name' => reset($nameArray),
			'last_name' => end($nameArray),
			'zip' => $zip,
		];
	}


	protected function checkTicketNumber($facility_id = '')
	{
		if ($facility_id == '') {
			$ticket = 'PE' . rand(1000, 9999) . rand(1000, 9999);
			$isExist = Ticket::where('ticket_number', $ticket)->first();
			if ($isExist) {
				$ticket = $this->checkTicketNumber();
			}
		} else {
			$facility = Facility::find($facility_id);
			$prefix = $facility->ticket_prefix != '' ? $facility->ticket_prefix : "CO";
			$fromRange = $facility->from_range != '' ? $facility->from_range : 1000000;
			$toRange = $facility->to_range != '' ? $facility->to_range : 9999999;
			$ticket = $prefix . rand($fromRange, $toRange);
			$isExist = Ticket::where('ticket_number', $ticket)->first();
			if ($isExist) {
				$ticket = $this->checkTicketNumber($facility_id);
			}
		}

		return $ticket;
	}



	public function updateRateInformationWithAvailibilty($request, Facility $facility)
	{
		$returnResultArr = array();

		$returnResultArr['coupon_threshold_price'] = 0;
		$returnResultArr['is_coupon_threshold_price_percentage'] = 0;
		$returnResultArr['availability'] = 0;
		$returnResultArr['is_coupon_threshold_applied'] = 0;

		$inventory = new Inventory();
		$date_time_out = Carbon::parse($request->arrival_time)->addMinutes((number_format($request->length_of_stay, 2) * 60));

		$realtimeWindow = $facility->realtime_window;
		$realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;

		$timeDifference = date_diff(date_create($request->arrival_time), Carbon::now());

		$isAvailable = true;

		$thresholdAvailability = self::DEFAULT_VALUE;

		if ($isAvailable == true) {
			//check how many slots does entry and exit time occupies
			//$difference = date_diff(date_create($request->arrival_time), date_create($date_time_out));
			$difference = date_diff(date_create(date('Y-m-d', strtotime($request->arrival_time))), date_create(date('Y-m-d', strtotime(($date_time_out)))));

			if ($difference->d > 0) {
				//                $dates   = $inventory->generateArrayOfDates(
				//                '', date($request->arrival_time), date($date_time_out));

				$dates   = $inventory->generateArrayOfDates(
					($difference->d + self::ADD_EXTRA_DAY_COUNT),
					date('Y-m-d H:i:s', strtotime($request->arrival_time))
				);

				$dayDifference = $difference->d;

				foreach ($dates as $key => $date) {
					$facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();

					if ($facilityAvailability) {
						$inventory = json_decode($facilityAvailability->availability);

						if ($key == 0) {
							/**
							 * because this is the first day in the dates provided
							 * we should check from each time_slot starting
							 * from the hour provided in the api call
							 */
							$i = date('G', strtotime($request->arrival_time));
							while ($i <= self::TWENTY_FOUR_HOURS) {
								if (isset($inventory->{$i})) {
									if ($inventory->{$i} < 1) {
										$isAvailable = false;
										break;
									}
									if ($thresholdAvailability > 0) {
										if ($thresholdAvailability > $inventory->{$i}) {
											$thresholdAvailability = $inventory->{$i};
										}
									} else {
										$thresholdAvailability = $inventory->{$i};
									}
								}
								$i++;
							}
						} elseif ($key == $dayDifference) {
							$i = date('G', strtotime($date_time_out));
							$minutes = date('i', strtotime($date_time_out));
							if ($minutes >= 30) {
								$i++;
							}
							/**
							 * because this is the last day in the dates provided
							 * we should check from each time_slot starting
							 * till the hour provided in the api call
							 */
							$j = 0;
							while ($j < $i) {
								if (isset($inventory->{$j})) {
									if ($inventory->{$j} < 1) {
										$isAvailable = false;
										break;
									}
									if ($thresholdAvailability > 0) {
										if ($thresholdAvailability > $inventory->{$j}) {
											$thresholdAvailability = $inventory->{$j};
										}
									} else {
										$thresholdAvailability = $inventory->{$j};
									}
								}
								$j++;
							}
						} else {
							/**
							 * because this could be any day except first and last in
							 * the dates provided we should check from whole day
							 */
							$k = 0;
							while ($k <= self::TWENTY_FOUR_HOURS) {
								if (isset($inventory->{$k})) {
									if ($inventory->{$k} < 1) {
										$isAvailable = false;
										break;
									}
									if ($thresholdAvailability > 0) {
										if ($thresholdAvailability > $inventory->{$k}) {
											$thresholdAvailability = $inventory->{$k};
										}
									} else {
										$thresholdAvailability = $inventory->{$k};
									}
								}
								$k++;
							}
						}
					}
				}
			} else {
				$startingHour = date('G', strtotime($request->arrival_time));
				$endingHour   = date('G', strtotime($date_time_out));
				$facilityAvailability     = FacilityAvailability::where(
					['facility_id' => $facility->id, 'date' => date('Y-m-d', strtotime($request->arrival_time))]
				)->first();

				if ($facilityAvailability) {
					$availability = json_decode($facilityAvailability->availability, true);

					while ($startingHour <= $endingHour) {
						if (isset($availability[$startingHour])) {
							if (($availability[$startingHour] < 1)) {
								$isAvailable = false;
							}
							if ($thresholdAvailability > 0) {
								if ($thresholdAvailability > $availability[$startingHour]) {
									$thresholdAvailability = $availability[$startingHour];
								}
							} else {
								$thresholdAvailability = $availability[$startingHour];
							}
						}
						$startingHour++;
					}
				}
			}

			if ($thresholdAvailability < self::DEFAULT_VALUE) {
				$thresholdAvailability = self::DEFAULT_VALUE;
			}

			if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {

				$dateIn = date('Y-m-d', strtotime($request->arrival_time));
				$facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();

				if ($facilityAvailability) {
					//$availabilities = json_decode($facilityAvailability->availability, true);

					if ($thresholdAvailability >= 0) {

						$couponThresholdsNew = $facility->facilityCouponThreshold;
						$couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');

						$thresholds       = array();
						if ($couponThresholdsNew) {
							$thresholdCounter = self::DEFAULT_VALUE;
							foreach ($couponThresholdsNew as $couponThreshold) {

								if ($couponThreshold->uptick_type !== 'deleted') {
									$thresholds[$thresholdCounter] =
										['threshold' => $couponThreshold->threshold, 'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
									$thresholdCounter++;
								}
							}
						}
						$thresholdPrice = 0;
						$currentAvailability = $thresholdAvailability;
						foreach ($thresholds as $key => $threshold) {
							if ($thresholdAvailability <= $threshold['threshold']) {
								if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
									if ($threshold['uptick_type'] == 'price') {
										$thresholdPrice = $threshold['uptick'];

										$returnResultArr['is_coupon_threshold_applied'] = 1;
										break;
									} else if ($threshold['uptick_type'] == 'percentage') {
										$thresholdPrice =  $threshold['uptick'];
										$returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG;

										$returnResultArr['is_coupon_threshold_applied'] = 1;
										break;
									}
								}
							}
						}
						$returnResultArr['coupon_threshold_price'] = $thresholdPrice;

						$returnResultArr['availability'] = $currentAvailability;
					}
				}
			}
		}

		//check realtime availability
		/*if ($timeDifference->h <= $realtimeWindow) {

           if($facility->realtime_minimum_availability > $returnResultArr['availability']){
             $returnResultArr['coupon_threshold_price'] = 0;        
             $returnResultArr['is_coupon_threshold_price_percentage'] = 0;        
             $returnResultArr['availability'] = 0;
             $returnResultArr['is_coupon_threshold_applied'] = 0;
           }
       }*/
		return $returnResultArr;
	}

	public function getAllFacilityGates(Request $request)
	{
		$gate = Gate::select('gate', 'gate_name', 'gate_type')->where('facility_id', $request->facility_id)->orderBy('gate_name', "ASC")->get();
		return $gate;
	}

	// device config code here
	public  function getDeviceConfig(Request $request)
	{

		$parkingData = [];
		$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
		if (!$secret) {
			throw new ApiGenericException('Invalid partner.');
		}
		if ($request->header('X-ClientSecret') != ''  && $request->device_serial_number != '') {


			// $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
			$facility = Facility::where('owner_id', $secret->partner_id)->first();
			$parkingdevice = ParkingDevice::with('gate')->where('serial_number', $request->device_serial_number)->where('facility_id', $facility->id)->first();
			if (isset($parkingdevice->is_active) == '1') {

				$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
				$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
				$diff_in_hours = $arrival_time->diffInRealHours($from);
				if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
					$diff_in_hours = 24;
				}

				$isMember = 0;
				$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember);
				if ($rate['price'] > 0) {
					$rate['price'] = number_format($rate['facility']['base_rate'] + $rate['facility']['processing_fee'], 2);
				}
				$rate['facility']['polling_interval'] = (int) $rate['facility']['polling_interval'];

				/*if($request->device_serial_number == "1640009093"){
            $rate['facility']['check_vehicle_enabled'] = '0';
          }*/

				$today = date("Y-m-d");
				$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->first();
				if (!$todayEvent) {
					$rate['is_event_started'] =  '0';
					//return $data;      
				}
				if ($todayEvent) {
					$parkingNowTime = date("Y-m-d H:i:s");
					$parkingStartTime = date("Y-m-d") . " " . $todayEvent->parking_start_time;
					$parkingEndTime = date("Y-m-d", strtotime($todayEvent->end_time)) . " " . $todayEvent->parking_end_time;
					$eventStartTime = $todayEvent->start_time;
					$eventEndTime = $todayEvent->end_time;
					if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
						$rate['event_rate'] =  number_format($todayEvent->event_rate + $facility->processing_fee, 2);
						$rate['is_event_started'] =  '1';
						//return $data;
					} else {
						$rate['is_event_started'] =  '0';
						//return $data;      
					}
				}
				$data['rate'] = $rate;
				$data['parkindata'] = $parkingdevice;
				return $data;
			} else {
				throw new ApiGenericException('This Device is not active, Please contact with admin');
			}
		}



		// $parkingdevice = ParkingDevice::with(['user','citation'])->where('license_plate', $license_plate)
		//   ->where('facility_id', $facility->id)->orderBy('id', 'DESC')->first();


	}

	public function getSessionTicketPaymentDetails(Request $request)
	{

		$user = User::where("session_id", $request->session_id)->first();
		if (!$user) {
			throw new ApiGenericException('Invalid user.');
		}
		$checkinData = Ticket::where("user_id", $user->id)->orderBy("id", "DESC")->first();
		$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
		$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
		$diff_in_hours = $arrival_time->diffInRealHours($from);
		if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
			$diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
		}
		$facility = Facility::find($checkinData->facility_id);
		$isMember = 0;
		$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE, $isMember);
		if ($rate == false) {
			throw new ApiGenericException('Garage is not available.');
		}
		$data = [];
		$data['session_id'] = $request->session_id;
		$data['rate'] = $rate['price'];
		$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
		$endDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
		$data['diff_in_days'] = $startDate->diffInDays($endDate);
		$data['diff_in_hours'] = $startDate->copy()->addDays($data['diff_in_days'])->diffInRealHours($endDate);
		$data['diff_in_minutes'] = $startDate->copy()->addDays($data['diff_in_days'])->addHours($data['diff_in_hours'])->diffInRealMinutes($endDate);

		return $data;
	}

	public function generateBarcodeJpgNew($encrypt)
	{
		$html = $this->generateBarcodeHtml($encrypt);

		$image = app()->make(Image::class);
		$image->setOption('width', '700');
		return $image->getOutputFromHtmlString($html);
	}

	public function generateBarcodeHtml($encrypt)
	{
		return '<img src="data:image/png;base64,{{\DNS2D::getBarcodePNG($encrypt, "QRCODE", "200","200")}}"  align="center" border="0" alt="barcode" style="text-decoration: none; -ms-interpolation-mode: bicubic; height: auto; border: 0;"/>';
	}


	public function customeReplySms($msg, $phone, $imageURL = '')
	{

		try {
			if ($phone == '') {
				return "success";
			}
			$this->log->info("sms about to send");
			$accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
			$authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
			$client = new Client($accountSid, $authToken);
			try {

				/*$imageBarcode = $this->generateBarcodeJpgNew("111");

              $imageBarcodeFileName = str_random(10) . '_qrcode.png';
            Storage::put($imageBarcodeFileName, $imageBarcode);
              $imageBarcodeFileName = str_random(10) . '_qrcode.png';
              Storage::put($imageBarcodeFileName, $imageBarcode);
              dd($imageBarcodeFileName, $imageBarcode);
              $data['bar_image_path'] = $imageBarcodeFileName;*/

				// Use the client to do fun stuff like send text messages!
				$client->messages->create(
					// the number you'd like to send the message to
					$phone,
					array(
						// A Twilio phone number you purchased at twilio.com/console
						'from' => config('parkengage.TWILIO_PHONE'),
						// the body of the text message you'd like to send
						//'body' => "Fine"
						'body' => "$msg",
						//'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)

					)
				);
				$this->log->info("Message : {$msg} sent to $phone");
				return "success";
			} catch (RestException $e) {
				//echo "Error: " . $e->getMessage();
				$this->log->error($e->getMessage());
				return "success";
			}
		} catch (RestException $e) {
			//echo "Error: " . $e->getMessage();
			$this->log->error($e->getMessage());
			return "success";
		}
	}


	public function qrImage($image)
	{
		$file = Storage::disk('local')->get($image) ?: null;
		// create response and add encoded image data
		$response = Response::make($file);
		// getting content type e.g image/jpeg
		$file_extension = mime_content_type(storage_path("app/" . $image));
		// set content-type
		$response->header('Content-Type', $file_extension);
		// output
		return $response;
	}

	public function isParkEngageGateAvailable($facility_id, $gate, $phone = '')
	{

		$facility = Facility::where('id', $facility_id)->first();

		$gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

		//check third party gate API
		//$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
		//$gateDetails = Facility::find($facility_id);
		if ($facility) {
			if ($facility->adam_host != '') {
				$params = ['gate_id' => $gate->gate];
				$this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
				$response = ParkengageGateApi::isvehicleAvailable($params, $facility->adam_host);
				$this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response" . json_encode($response));
				if ($response['success'] == false) {
					$msg = "The system is not currently available. Please try again later.";
					return $msg;
				}
				if (isset($response['data'][0]) && $response['data'][0] == "true") {
					return true;
					/*$cmd_params = ['gate_id'=>$gate->gate];
                    $this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params". json_encode($cmd_params));
                    $command_response = ParkengageGateApi::openGate($cmd_params, $gateDetails->host);
                    $this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response". json_encode($command_response));
                    if($command_response['success'] == true){
                        if($command_response['data'][0] == "true"){
                            return true;
                        }else{
                            $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                            return $msg;                            
                        }                        
                    }else{
                        $msg = "Seems some issue with the ".ucwords($gate->gate_type)." gate. Please contact the attendant.";
                        return $msg; 
                    }*/
				} else {

					$msg = "Your vehicle must be in the garage in front of the " . ucwords($gate->gate_type) . " gate.";
					return $msg;
				}
			}
		}
	}

	public function isParkEngageGateOpen($facility_id, $gate, $phone = '')
	{

		$facility = Facility::where('id', $facility_id)->first();

		$gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

		//check third party gate API
		//$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
		if ($facility) {
			if ($facility->adam_host != '') {
				$params = ['gate_id' => $gate->gate];
				$this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
				/*$response = ParkengageGateApi::isvehicleAvailable($params, $gateDetails->host);
                $this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response". json_encode($response));
                if($response['success'] == false){
                    $msg = "The system is not currently available. Please try again later.";
                    return $msg; 
                }*/
				//if(isset($response['data'][0]) && $response['data'][0] == "true"){
				$cmd_params = ['gate_id' => $gate->gate];
				$this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params" . json_encode($cmd_params));
				$command_response = ParkengageGateApi::openGate($cmd_params, $facility->adam_host);
				$this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response" . json_encode($command_response));
				if ($command_response['success'] == true) {
					if ($command_response['data'][0] == "true") {
						return true;
					} else {
						$msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
						return $msg;
					}
				} else {
					$msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
					return $msg;
				}
				/*}else{

                    $msg = "Your vehicle must be in the garage in front of the ".ucwords($gate->gate_type)." gate.";
                    return $msg; 
                }*/
			}
		}
	}

	public function setCustomTimezone($facility_id)
	{
		$facility = Facility::find($facility_id);
		$secret = OauthClient::where('partner_id', $facility->owner_id)->first();
		$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
		if ($partnerTimezone) {
			if ($facility->timezone != '') {
				date_default_timezone_set($facility->timezone);
			} else if ($partnerTimezone->timezone != '') {
				date_default_timezone_set($partnerTimezone->timezone);
			}
		}
	}

	public function ticketSessionCheckinCheckout(Request $request)
	{
		//try{
		$this->setCustomTimezone($request->facility_id);
		$this->log->info("Request received --" . json_encode($request->all()));
		$facility = Facility::with('FacilityPaymentDetails.facilityPaymentType', 'facilityConfiguration')->find($request->facility_id);

		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}
		$paymentGateway = '';
		if (isset($facility->FacilityPaymentDetails->facilityPaymentType->id)) {
			$paymentGateway = $facility->FacilityPaymentDetails->facilityPaymentType->payment_type;
		}

		$facilityName =  ucwords($facility->full_name);
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}
		//PMIS-13403
		if ((isset($request->is_pull_ticket) && $request->is_pull_ticket == 1) && (isset($gate) && $gate->gate_type == "entry")) {
			return $this->pullTicket($request, $paymentGateway, $facility);
		}
		if ($request->session_id == '' && $request->CustomerData == '') {
			throw new ApiGenericException('Card info not found. Please try again.');
		}

		if (!isset($request->session_id)) {
			$request->request->add(['session_id' => $request->CustomerData]);
		}

		if (isset($gate) && $gate->gate_type == "entry") {

			$permitRequest = PermitRequest::with(['user'])->where('session_id', $request->session_id)->where('facility_id', $request->facility_id)->whereNull("cancelled_at")->orderBy("id", "DESC")->first();
			//->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))
			if ($permitRequest) {
				if (strtotime($permitRequest->desired_start_date) <= strtotime(date("Y-m-d")) && strtotime($permitRequest->grace_end_date) >= strtotime(date("Y-m-d"))) {
					if ($permitRequest->is_antipass_enabled == 0 || $permitRequest->is_antipass_enabled == "0") {
						$permitTicket = Ticket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
						if ($permitTicket) {
							throw new ApiGenericException('You have already checked-in.');
						}
					}
					if ($request->license_plate == '') {
						$mappings = PermitVehicleMapping::where("permit_request_id", $permitRequest->id)->orderBy("id", "DESC")->get();
						if (count($mappings) > 1) {
							$vehicle = PermitVehicle::find($mappings[0]->permit_vehicle_id);
							if ($vehicle) {
								$request->request->add(['license_plate' => $vehicle->license_plate_number]);
							}
						} else {
							$vehicle = PermitVehicle::find($mappings[0]->permit_vehicle_id);
							if ($vehicle) {
								$request->request->add(['license_plate' => $vehicle->license_plate_number]);
							}
						}
					}

					$facilityName = ucwords($facility->full_name);

					$data['user_id'] = $permitRequest->user_id;
					$data['checkin_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					$data['is_checkin'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
					$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['check_in_datetime'] = date('Y-m-d H:i:s');
					//$data['checkout_datetime'] = $permitRequest->desired_end_date;
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					//$data['total'] = $permitRequest->permit_rate;
					//$data['grand_total'] = $permitRequest->permit_rate;
					$data['permit_request_id'] = $permitRequest->id;
					$data['license_plate'] = $request->license_plate;
					$data['device_type'] = 'IM30';
					$data['session_id'] = $request->session_id;
					$result = Ticket::create($data);
					//Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
					$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
					if (isset($permitRequest->user->phone)) {
						dispatch((new SendSms($msg, $permitRequest->user->phone))->onQueue(self::QUEUE_NAME));
					}

					//$msg =  "Welcome to $facilityName. #$result->ticket_number.";
					$msg =  "Welcome, Permit #" . $permitRequest->account_number;
					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}
					$data = [
						'msg' => $msg,
						'permit_number' => $permitRequest->account_number,
						'checkin_time' => date("g:i A", strtotime($result->checkin_time)),
						'booking_type' => "permit",
						'is_check_in_ontime' => "1",
						'eticket_id' => $result->ticket_number
					];
					$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
					return $data;
				} else {
					throw new ApiGenericException('Your Permit is expired or canceled.');
				}
			}

			$reservation = Reservation::with('user')->where("session_id", $request->session_id)->where("facility_id", $request->facility_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->orderBy("id", "DESC")->first();
			if ($reservation) {
				$user = $reservation->user;
				$config = Configuration::where('field_name', 'prepaid-checkin-time')->where("facility_id", $facility->id)->first();
				if (count($config) > 0) {
					$prepaidCheckinTime = $config->field_value;
				} else {
					$prepaidCheckinTime = 15;
				}
				$today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);
				$reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
				// $reservationEndDate = $reservationstartDate->addHours($reservation->length);
				// converted the decimal lenght to hours and minutes by Ashutosh
				$time = Carbon::parse($reservation->start_timestamp)->addHours($reservation->length);
				if (intval($reservation->length) != $reservation->length) {
					$timarr = explode('.', $reservation->length);
					$time->addMinutes($timarr[1]);
				}
				$reservationEndDate = $time->format('Y-m-d H:i:s');

				if (strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)) {
					if (strtotime($today) < strtotime($reservation->start_timestamp)) {
					} else {
						$ticket = Ticket::where('user_id', $user->id)->where("facility_id", $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
						if ($ticket) {
							throw new ApiGenericException('You have already checked-in.');
						}

						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						if ($reservation) {
							$data['reservation_id'] = $reservation->id;
							$data['check_in_datetime'] = $reservation->start_timestamp;
							$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
							$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
							$data['payment_date'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
							$data['total'] = $reservation->total;
							$data['grand_total'] = $reservation->total;
							$data['length'] = $reservation->length;
							$data['user_pass_id'] = $reservation->user_pass_id;
							$data['checkin_time'] = date('Y-m-d H:i:s');
							$data['license_plate'] = $request->license_plate != '' ? $request->license_plate : $reservation->license_plate;
							$reservation->is_ticket = '1';
							$reservation->save();
						}

						$data['device_type'] = 'IM30';
						$data['session_id'] = $request->session_id;
						$data['payment_token'] = $request->payment_token;
						$data['customer_data'] = $request->CustomerData;
						$result = Ticket::create($data);
						$facilityName = ucwords($facility->full_name);
						if ($reservation) {
							//Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						} else {
							Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
							$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
							dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
						}

						$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						// send Notification 
						$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
						if ($deviceToken) {
							$updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::RES_CHECKIN_NOTIFICATION];
							Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
						}

						//$msg =  "Welcome to $facilityName. #$result->ticket_number.";
						$msg =  "WELCOME. #$result->ticket_number";
						$data = ['msg' => $msg];
						$data['booking_number'] = $reservation->ticketech_code;
						$data['booking_start_time'] = date("g:i A", strtotime($result->check_in_datetime));
						$data['booking_exit_time'] = date("g:i A", strtotime('+1 second', strtotime($result->checkout_datetime)));
						$data['booking_entry_time'] = date("g:i A", strtotime($result->checkin_time));
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						$data['booking_type'] = 'reservation';
						$data['is_check_in_ontime'] = '1';
						$data['eticket_id'] = $result->ticket_number;
						$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
						return $data;
					}
				} else {
					if (strtotime($today) < strtotime($reservation->start_timestamp)) {
					} else {
						$ticket = Ticket::where('user_id', $user->id)->where("facility_id", $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
						if ($ticket) {
							throw new ApiGenericException('You have already checked-in.');
						}

						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						if ($reservation) {
							$data['reservation_id'] = $reservation->id;
							$data['check_in_datetime'] = $reservation->start_timestamp;
							$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
							$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
							$data['payment_date'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
							$data['total'] = $reservation->total;
							$data['grand_total'] = $reservation->total;
							$data['length'] = $reservation->length;
							$data['user_pass_id'] = $reservation->user_pass_id;
							$data['checkin_time'] = date('Y-m-d H:i:s');
							$data['license_plate'] = $request->license_plate != '' ? $request->license_plate : $reservation->license_plate;
							$reservation->is_ticket = '1';
							$reservation->save();
						}

						$data['device_type'] = 'IM30';
						$data['session_id'] = $request->session_id;
						$data['payment_token'] = $request->payment_token;
						$data['customer_data'] = $request->CustomerData;
						$result = Ticket::create($data);
						$facilityName = ucwords($facility->full_name);
						if ($reservation) {
							//Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						} else {
							Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
							$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
							dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
						}

						$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						// send Notification 
						$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
						if ($deviceToken) {
							$updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::RES_CHECKIN_NOTIFICATION];
							Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
						}

						//$msg =  "Welcome to $facilityName. #$result->ticket_number.";
						$msg =  "WELCOME. #$result->ticket_number";
						$data = ['msg' => $msg];
						$data['booking_number'] = $reservation->ticketech_code;
						$data['booking_start_time'] = date("g:i A", strtotime($result->check_in_datetime));
						$data['booking_exit_time'] = date("g:i A", strtotime('+1 second', strtotime($result->checkout_datetime)));
						$data['booking_entry_time'] = date("g:i A", strtotime($result->checkin_time));
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						$data['booking_type'] = 'reservation';
						$data['is_check_in_ontime'] = '1';
						$data['eticket_id'] = $result->ticket_number;
						$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
						return $data;
					}
				}
			}
		}
		if (isset($gate) && $gate->gate_type == "exit") {
			$ticketFound = 0;
			if ($request->CustomerData != '') {
				$ticket = Ticket::with("user")->where('customer_data', $request->CustomerData)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
				if (!$ticket) {
					if (isset($request->eticket_id)) {
						$ticket = Ticket::with("user")->where('ticket_number', $request->eticket_id)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
						if (!$ticket) {
							throw new ApiGenericException('You have already checkout.');
						}
					} else {
						throw new ApiGenericException('Sorry! No checkin found against this card.');
					}
					$ticketFound = 1;
				} else {
					$request->request->add(['session_id' => $ticket->session_id]);
				}
			}
			if ($ticketFound == 0) {
				$ticket = Ticket::with(["reservation", "user"])->where('session_id', $request->session_id)->where('facility_id', $request->facility_id)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
			}
			if ($ticket) {
				$user = $ticket->user;

				if ($ticket->permit_request_id != '') {
					$ticket->is_checkout = '1';
					$ticket->checkout_gate = $request->gate_id;
					$ticket->checkout_time = date("Y-m-d H:i:s");
					$ticket->checkout_datetime = date("Y-m-d H:i:s");
					$ticket->estimated_checkout = date("Y-m-d H:i:s");
					$ticket->payment_date = date("Y-m-d H:i:s");
					$ticket->checkout_license_plate = $request->license_plate;
					$ticket->checkout_mode = '3';
					$ticket->is_transaction_status = '0';
					$ticket->save();

					if ($request->license_plate != '') {
						if ($facility->license_plate_model != '') {
							$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
							$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
						}
					}

					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
					$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

					$data = ['msg' => $msg];
					$data['is_phone_linked'] = '0';
					$data['phone_linked_msg'] = '';
					if (isset($user->phone)) {
						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						if ($partnerDetails->user_id == self::PARTNER_ID) {
							$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
								$join->on('user_facilities.user_id', '=', 'users.id');
								$join->where('user_facilities.facility_id', "=", $facility->id);
							})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
							$name = ($getRM->slug) ? $getRM->slug : 'receipt';
						} else {
							$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						}
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					}
					$data["eticket_id"] = $ticket->ticket_number;
					$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
					$this->log->info("SMS sent, response sent");
					return $data;
				}

				if (!isset($facility->FacilityPaymentDetails->id)) {
					throw new ApiGenericException('Payment details not defined against this garage.');
				}

				//overstay case

				if ($ticket->estimated_checkout != '' && $facility->is_gated_facility != '2') {
					$this->log->info("eticket already paid.");
					$overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();

					if ($overstayExist) {
						if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

							$ticket->is_checkout = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->checkout_license_plate = $request->license_plate;
							$ticket->checkout_mode = '3';
							$ticket->is_transaction_status = '0';
							$ticket->save();

							if (isset($ticket->reservation->id)) {
								$ticket->reservation->is_ticket = '2';
								$ticket->reservation->save();
							}

							if ($request->license_plate != '') {
								if ($facility->license_plate_model != '') {
									$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
									$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
								}
							}

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

							$data = ['msg' => $msg];
							$data['is_phone_linked'] = '0';
							$data['phone_linked_msg'] = '';
							if (isset($user->phone)) {
								$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
								if ($partnerDetails->user_id == self::PARTNER_ID) {
									$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
										$join->on('user_facilities.user_id', '=', 'users.id');
										$join->where('user_facilities.facility_id', "=", $facility->id);
									})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
									$name = ($getRM->slug) ? $getRM->slug : 'receipt';
								} else {
									$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
								}
								$url = env('RECEIPT_URL');
								$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
								//changes for breeze specific URL from DB
								if ($dynamicReceiptUrl) {
									$url = $dynamicReceiptUrl->value;
								}
								$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

								dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
								$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
								$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
							}
							$data["eticket_id"] = $ticket->ticket_number;
							$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
							$this->log->info("SMS sent, response sent");
							return $data;
						}
						$this->log->info("eticket overstay created.");
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->estimated_checkout);
						$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
						$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->estimated_checkout);
						/** this function is used to get Availability Information for respective facility **/
						$isMember = 0;

						$this->log->info("get Diff in Hours : {$diff_in_hours}");
						$rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
						$this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");


						$isMember = 0;
						if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
							$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
						} else {
							$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, $isMember);
						}
						$taxRate = $ticket->getTaxRate($rate);          // to get tax price
						$data['price'] =  $rate['price'] + $taxRate;
						$data['eticket_id'] =  $ticket->ticket_number;
						$data['is_overstay'] =  '1';
						return $data;
					} else {
						if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
							$ticket->is_checkout = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->checkout_license_plate = $request->license_plate;
							$ticket->checkout_mode = '3';
							$ticket->is_transaction_status = '0';
							$ticket->save();

							if (isset($ticket->reservation->id)) {
								$ticket->reservation->is_ticket = '2';
								$ticket->reservation->save();
							}

							if ($request->license_plate != '') {
								if ($facility->license_plate_model != '') {
									$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
									$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
								}
							}

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

							$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
							if ($partnerDetails->user_id == self::PARTNER_ID) {
								$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
									$join->on('user_facilities.user_id', '=', 'users.id');
									$join->where('user_facilities.facility_id', "=", $facility->id);
								})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
								$name = ($getRM->slug) ? $getRM->slug : 'receipt';
							} else {
								$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
							}
							$url = env('RECEIPT_URL');
							$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
							//changes for breeze specific URL from DB
							if ($dynamicReceiptUrl) {
								$url = $dynamicReceiptUrl->value;
							}
							$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

							dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
							$this->log->info("SMS sent, response sent");
							$data = ['msg' => $msg];
							$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
							$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
							$data["eticket_id"] = $ticket->ticket_number;
							$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
							return $data;
						}
						$this->log->info("overstay created.");
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout);
						$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
						$diff_in_hours = $ticket->getCheckOutCurrentTime(true, ($ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout));
						/** this function is used to get Availability Information for respective facility **/
						$isMember = 0;

						$this->log->info("get Diff in Hours : {$diff_in_hours}");
						$rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
						$this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");
						$isMember = 0;
						if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
							$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
						} else {
							$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, $isMember);
						}
						$taxRate = $ticket->getTaxRate($rate);          // to get tax price
						$data['price'] =  $rate['price'] + $taxRate;
						$data['eticket_id'] =  $ticket->ticket_number;
						$data['is_overstay'] =  '1';
						$ticket->save();
						return $data;
					}
				}



				$arrival_time = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
				$parkingNowTime = date("Y-m-d H:i:s");

				$diff_in_hours = $ticket->getCheckOutCurrentTime(true);

				$isMember = 0;
				if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
					$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
					$rate['description'] = self::HOURLY_RATE;
				} else {
					$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
					if (!isset($rate['description'])) {
						$rate['description'] = self::BASE_RATE;
					}
				}
				$rateId = isset($rate['id']) ? $rate['id'] : '';
				$rateDescription 	= isset($rate['description']) ? $rate['description'] : '';
				$priceBreakUp 		= $ticket->priceBreakUp($rate);
				$this->log->info("eticket priceBreakUp " . json_encode($priceBreakUp));
				$promotionRequest = $this->getPromocodeRequestDetails($request, $priceBreakUp, $ticket);
				if (isset($promotionRequest->promocode_msg)) {
				} else {
					if ($promotionRequest) {
						if ($ticket->discount_amount >= $priceBreakUp['payable_amount'] && $ticket->promocode != '') {
							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$ticket->processing_fee  = $priceBreakUp['processing_fee'];
							$ticket->tax_fee        = $priceBreakUp['tax_rate'];
							$ticket->parking_amount  = $priceBreakUp['parking_amount'];
							//$ticket->paid_amount     = $priceBreakUp['paid_amount'];
							//$ticket->discount_amount = $priceBreakUp['discount_amount'];
							$ticket->grand_total     = "0.00";
							$ticket->total     = $priceBreakUp['total'];
							$ticket->length     = $diff_in_hours;

							$ticket->checkout_mode = '3';
							$ticket->is_checkout = '1';
							$ticket->is_checkin = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->is_cloud_payment = '0';
							$ticket->checkout_license_plate = $request->license_plate;
							$ticket->rate_id = $rateId;
							$ticket->rate_description = $rateDescription;
							$ticket->is_transaction_status = '0';
							$ticket->save();

							if ($request->license_plate != '') {
								if ($facility->license_plate_model != '') {
									$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
									$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
								}
							}

							$this->saveTransactionData($rate, $ticket);

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "THANK YOU FOR VISITING, #$ticket->ticket_number";
							if ($ticket->promocode != '') {
								$promocodeRequest = PromocodeRequest::where('promocode', $ticket->promocode)->orderBy("id", "desc")->first();
								if ($promocodeRequest) {
									$promotion = Promotion::find($promocodeRequest->promotion_id);
									if ($promotion) {
										$msg = "THANK YOU FOR VISITING, " . $promotion->name . " applied.#$ticket->ticket_number";
									}
									$promocodeRequest->delete();
								}
							}
							if (isset($user->phone)) {
								$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
								$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
								$url = env('RECEIPT_URL');
								$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
								//changes for breeze specific URL from DB
								if ($dynamicReceiptUrl) {
									$url = $dynamicReceiptUrl->value;
								}
								$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

								dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
							}
							$this->log->info("SMS sent, response sent");
							$data = ['msg' => $msg];
							$data['is_phone_linked'] = isset($user->phone) ? '1' : '0';
							$data['phone_linked_msg'] = isset($user->phone) != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
							$data["eticket_id"] = $ticket->ticket_number;
							$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
							$this->log->info("User checkout, response sent");
							return $data;
						} else {
							$priceBreakUp['discount_amount'] = $ticket->discount_amount;
							$priceBreakUp['payable_amount'] = sprintf("%.2f", ($priceBreakUp['payable_amount'] - $ticket->discount_amount));
						}
					}
				}

				if ($priceBreakUp['payable_amount'] > 0) {

					if ($facility->is_cloud_payment_enabled != '1' || $facility->is_cloud_payment_enabled != 1) {
						if ($priceBreakUp['payable_amount'] > $facility->facilityConfiguration->max_transaction_capping_amount) {
							throw new ApiGenericException("The parking amount exceeds the permissible amount, Please contact the attendant.");
						}
						// $ticket->processing_fee  = $priceBreakUp['processing_fee'];
						// $ticket->tax_fee        = $priceBreakUp['tax_rate'];
						// $ticket->parking_amount  = $priceBreakUp['parking_amount'];
						// $ticket->paid_amount     = $priceBreakUp['paid_amount'];
						// $ticket->discount_amount = $priceBreakUp['discount_amount'];
						// $ticket->grand_total     = $priceBreakUp['payable_amount'];
						// $ticket->total     = $priceBreakUp['total'];
						// $ticket->length     = $diff_in_hours;
						// $ticket->save();
						QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
						$data['price'] =  sprintf("%.2f", ($priceBreakUp['payable_amount']));
						$data['eticket_id'] =  $ticket->ticket_number;
						$data['payment_token'] =  $ticket->payment_token;
						$this->log->info("price sent, response sent");
						return $data;
					}

					QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
					$data['price'] 			=  sprintf("%.2f", ($priceBreakUp['payable_amount']));
					$data['eticket_id'] 	=  $ticket->ticket_number;
					$data['payment_token'] 	=  $ticket->payment_token;
					$this->log->info("price sent, response sent");
					return $data;
					// $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $priceBreakUp['payable_amount'], $request);
					// $this->log->info("Payment Response :" . json_encode($refundstatus));
					// if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
					//   throw new ApiGenericException("Payment failed. Please try again");
					// }
					// $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
					// $ticket->anet_transaction_id = $planetTransaction->id;
				}

				if ($ticket->is_offline_payment == '0' && $ticket->paid_type == '9') {
					if ($priceBreakUp['total'] > 0) {
						$result['price'] =  $priceBreakUp['total'];
						$result['eticket_id'] =  $ticket->ticket_number;
						$result['payment_token'] =  $ticket->payment_token;
						QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
						// $ticket->processing_fee  = $priceBreakUp['processing_fee'];
						// $ticket->tax_fee        = $priceBreakUp['tax_rate'];
						// $ticket->parking_amount  = $priceBreakUp['parking_amount'];
						// $ticket->paid_amount     = $priceBreakUp['paid_amount'];
						// $ticket->discount_amount = $priceBreakUp['discount_amount'];
						// $ticket->grand_total     = $priceBreakUp['total'];
						// $ticket->total     = $priceBreakUp['total'];
						// $ticket->length     = $diff_in_hours;
						// $ticket->rate_id = $rateId;
						// $ticket->rate_description = $rateDescription;
						// $ticket->save();
						$this->log->info("eticket if payment declined and hit api again, price sent, response sent");
						return $result;
					}
				}

				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}

				$ticket->processing_fee  = $priceBreakUp['processing_fee'];
				$ticket->tax_fee        = $priceBreakUp['tax_rate'];
				$ticket->parking_amount  = $priceBreakUp['parking_amount'];
				$ticket->paid_amount     = $priceBreakUp['paid_amount'];
				$ticket->discount_amount = $priceBreakUp['discount_amount'];
				$ticket->grand_total     = $priceBreakUp['payable_amount'];
				$ticket->total     = $priceBreakUp['total'];
				$ticket->length     = $diff_in_hours;

				$ticket->checkout_mode = '3';
				$ticket->is_checkout = '1';
				$ticket->is_checkin = '1';
				$ticket->checkout_gate = $request->gate_id;
				$ticket->checkout_time = date("Y-m-d H:i:s");
				$ticket->checkout_datetime = date("Y-m-d H:i:s");
				$ticket->estimated_checkout = date("Y-m-d H:i:s");
				$ticket->is_cloud_payment = '1';
				$ticket->checkout_license_plate = $request->license_plate;
				$ticket->rate_id = $rateId;
				$ticket->rate_description = $rateDescription;
				$ticket->is_transaction_status = '0';
				$ticket->save();

				if ($request->license_plate != '') {
					if ($facility->license_plate_model != '') {
						$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
						$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
					}
				}

				$this->saveTransactionData($rate, $ticket);

				$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
				$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
				if (isset($user->phone)) {
					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				}
				$this->log->info("SMS sent, response sent");
				$data = ['msg' => $msg];
				$data['is_phone_linked'] = isset($user->phone) ? '1' : '0';
				$data['phone_linked_msg'] = isset($user->phone) != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data["eticket_id"] = $ticket->ticket_number;
				$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
				$this->log->info("User checkout, response sent");
				return $data;
			}
		}


		if ($request->CustomerData != '' && $gate->gate_type == "exit") {
			$ticket = Ticket::with("user")->where('customer_data', $request->CustomerData)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
			if (!$ticket) {
				throw new ApiGenericException('You have already checkout.');
			}
			$user = $ticket->user;
		} else {
			$userSessionExist = UserSession::where("session_id", $request->session_id)->where('partner_id', $facility->owner_id)->orderBy("id", "DESC")->first();
			if ($userSessionExist) {
				$user = User::where('id', $userSessionExist->user_id)->first();
			} else {
				$user = User::create(
					[
						'name' => '',
						'email' => '',
						'phone' => '',
						'password' => Hash::make(str_random(60)),
						'anon' => true,
						'user_type' => '5',
						'created_by' => $facility->owner_id,
						'session_id' => $request->session_id,
					]
				);

				$userSession = new UserSession();
				$userSession->user_id = $user->id;
				$userSession->partner_id = $user->created_by;
				$userSession->session_id = $request->session_id;
				$userSession->save();
			}
		}

		// VP:PIMS-14662
		// Add HYBRID LOGIC HERE START 
		$this->log->info("Start Hybrid");
		if ($facility->is_gated_facility == '2') {
			$this->log->info("Start Hybrid point 1");
			// Check Temp eticket 
			$eticket = $this->im30Service->checkTempEticket($request);
			$this->log->info("Start Hybrid point 2 " . json_encode($eticket));
			if ($eticket) {
				$this->log->info("Start Hybrid point 3");
				// if Temp Ticket found Create New PE Tickets 
				$entryFeed 	= LPRFeed::find($this->im30Service->getLPRFeedId($request));
				$exitFeed 	= LPRFeed::Where(['license_plate' => $request->license_plate])->orderBy('id', 'desc')->first();
				// $hyTicket 	= Ticket::where('session_id', $request->session_id)->where('facility_id', $request->facility_id)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
				$this->log->info("Start Hybrid point 4");
				if ($entryFeed) {
					$this->log->info("Start Hybrid point 5");
					$request->merge(
						[
							'user_id' => $user->id,
							'checkin_time' => $this->im30Service->getUTCToFacilityTimezone($entryFeed->event_timestamp, $facility),
							'estimated_checkout' => $this->im30Service->getUTCToFacilityTimezone($exitFeed->event_timestamp, $facility)
						]
					);
					$length_of_stay  = $this->ticketHelper->getLenghtOfStay($request->checkin_time, $request->estimated_checkout);
					$this->log->info("before get amount due check request : " . json_encode($request->all()));


					// Calculate price again 
					$amountDue = $this->im30Service->getAmountDue($request, $facility);
					$rate['price'] 		= $amountDue['price'];
					$this->log->info("length_of_stay : {$length_of_stay} parking Amount : " . $rate['price']);
					// Now calculate Tax and Fees 
					$parkingAmount      = $rate['price'];
					$processingFee      = $facility->getProcessingFee('0');       // pass 1 to get the processing fee // pass 1 to get the processing fee
					$surchargeFee       = $facility->getSurchargeFee($rate);
					$newRate['price']   = ($rate['price'] + $surchargeFee);
					$tax_rate           = $facility->getTaxRate($newRate, '0');      // fetch tax from getTicketRate dinamicaly
					$additionalFee      = $facility->getAdditionalFee($rate);     // Addition Fee Introduce
					$payableAmount      = ($parkingAmount + $processingFee + $surchargeFee + $tax_rate + $additionalFee);

					// Add this in Request
					$request->merge(
						[
							'total' 			=> $payableAmount,
							'grand_total' 		=> $payableAmount,
							'parking_amount' 	=> $parkingAmount,
							'tax_fee' 			=> $tax_rate,
							'processing_fee' 	=> $processingFee,
							'additional_fee' 	=> $additionalFee,
							'surcharge_fee' 	=> $surchargeFee,
							'rate_id'			=> isset($amountDue['rate_id']) ? $amountDue['rate_id'] : 0,
							'rate_description'	=> isset($amountDue['rate_description']) ? $amountDue['rate_description'] : 'base_price'
						]
					);

					// Save Ticket
					// if ($request->is_overstay == '1') {
					// 	$ticket = Ticket::where(['license_plate' => $request->license_plate, 'is_checkout' => '1'])->where('facility_id', $request->facility_id)->orderBy("id", "DESC")->first();
					// 	if ($ticket) {
					// 		$request->merge(
					// 			[
					// 				'ticket_id' 		=> $ticket->id,
					// 				'ticket_number' 	=> $ticket->ticket_number
					// 			]
					// 		);
					// 	}
					// 	$ticket = $this->im30Service->createOverstay($request, $facility);
					// } else {
					// 	$ticket = $this->im30Service->createTicket($request, $facility);
					// }
					$this->log->info("Start Hybrid point 6");

					// QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
					$hybrid['price'] 			=  sprintf("%.2f", ($payableAmount));
					$hybrid['eticket_id'] 		=  $request->eticket_id;
					$hybrid['payment_token'] 	=  $request->session_id;
					$this->log->info("Data returen : " . json_encode($hybrid));
					$this->log->info("price sent, response sent");
					return $hybrid;
				} else {
					$this->log->info("Start Hybrid Invalid Request");
					// Retrun Invalid Request
					throw new ApiGenericException('No Checkin found for this license plate');
				}
			} else {
				// Here We need to handle Overstay flow 
				$this->log->info("Start Hybrid Invalida TEMP ID ");
				throw new ApiGenericException('Invalid Request');
			}
		}
		// HYBRID LOGIC HERE CLOSE 

		$isMember = 0;
		if (isset($gate) && $gate->gate_type == "entry") {

			/*if ($request->license_plate != '') {
				$licensePlate = WorldportLicensePlate::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->first();
				if (!$licensePlate) {
				$request->request->add(['license_plate' => '']);
				}
			}*/

			$arrival_time = date("Y-m-d H:i:s");

			$length_of_stay = .01;

			$existHoursOfOperation = HoursOfOperation::where('facility_id', $facility->id)->get();
			if (count($existHoursOfOperation) > 0) {
				$weekday = date('N', strtotime($arrival_time));
				$time = date('H:i:s', strtotime($arrival_time));
				if ($weekday > 6) {
					$weekday = 7 - $weekday;
				}
				$hoursOfOperation = HoursOfOperation::where('day_of_week', $weekday)->where('facility_id', $facility->id)->get();

				if (count($hoursOfOperation) <= 0) {
					throw new ApiGenericException('Garage is currently closed.');
				} else {
					foreach ($hoursOfOperation as $key => $value) {
						if ($value->open_time <= $time && $value->close_time >= $time) {
						} else {
							throw new ApiGenericException('Garage is currently closed.');
						}
					}
				}
			}
			//if checkin by lpr queue and user link card that checkin
			if ($request->eticket_id != '') {
				$ticket = Ticket::where('ticket_number', $request->eticket_id)->where('is_checkin', '0')->where('is_checkout', '0')->first();
				if (!$ticket) {
					throw new ApiGenericException('Invalid checkin details.');
				}

				$ticket->vp_device_checkin = $ticket->reservation_id != '' ? '0' : '1';
				$ticket->license_plate = isset($request->license_plate) ? $request->license_plate : '';
				$ticket->card_last_four = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
				$ticket->expiry = isset($request->expiry) ? $request->expiry : '';
				if (isset($request->CardType)) {
					$card_type = '';
					if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
						$card_type = 'VISA';
					} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
						$card_type = 'MASTERCARD';
					} else if (strtolower($request->CardType) == "jcb") {
						$card_type = 'JCB';
					} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
						$card_type = 'AMEX';
					} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
						$card_type = 'DISCOVER';
					} else {
						$card_type = $request->CardType;
					}
					$ticket->card_type = $card_type;
				}
				$ticket->device_type = "IM30";
				$ticket->terminal_id = isset($request->TerminalID) ? $request->TerminalID : '';
				$ticket->payment_gateway = $paymentGateway;
				$ticket->user_id = $user->id;
				$ticket->session_id = $request->session_id;
				$ticket->payment_token = $request->payment_token;
				$ticket->customer_data = $request->CustomerData;
				$ticket->is_checkin = '1';
				$ticket->save();

				$facilityName = ucwords($facility->full_name);

				$this->log->info("eticket session user update  checkedin with ticket number {$ticket->ticket_number}");
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				if ($user->email != '') {
					Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
				}
				$msg =  "WELCOME. #$ticket->ticket_number";
				if ($user->phone != '') {
					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					if ($partnerDetails->user_id == self::PARTNER_ID) {
						$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
							$join->on('user_facilities.user_id', '=', 'users.id');
							$join->where('user_facilities.facility_id', "=", $facility->id);
						})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
						$name = ($getRM->slug) ? $getRM->slug : 'receipt';
					} else {
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					}
					$url = env('TOUCHLESS_WEB_URL');
					$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
					if ($dynamicCheckinUrl) {
						$url = $dynamicCheckinUrl->value;
					}
					$grace_period = $facility->grace_period_minute;
					$ticket_number = base64_encode($ticket->ticket_number);
					$sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				}

				$data = ['msg' => $msg];
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data["eticket_id"] = $ticket->ticket_number;
				$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '1');
				$this->log->info("eticket Checkin update done, response sent.");
				return $data;
			}
			$ticket = Ticket::where('user_id', $user->id)->where('facility_id', $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}
			$reservation = Reservation::with('user')->where('facility_id', $request->facility_id)->where("user_id", $user->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
			$userPass = [];
			if (!$reservation) {
				if ($facility->facility_booking_type == '0') {
					throw new ApiGenericException('Drive-Up booking is not allowed.');
				}

				$userPass = UserPass::with('user')->where('facility_id', $request->facility_id)->where("user_id", $user->id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days', '>', 0)->orderBy('id', 'DESC')->first();
				if (!$userPass) {
					if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
						throw new ApiGenericException('No prepaid booking found against this card.');
					}

					if ($facility->is_prepaid_first == '2') {
						$today = date("Y-m-d");
						//$todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
						$facility_id = $facility->id;
						$sqlQuery = "SELECT e.*
                      FROM events as e
                      inner join event_facility as ef on ef.event_id= e.id        
                      WHERE e.partner_id IN ({$facility->owner_id}) AND date(e.start_time) = '" . $today . "' AND date(e.end_time) >= '" . $today . "' AND e.deleted_at is null AND ef.facility_id IN (" . $facility_id . ") AND e.is_active='1' AND e.deleted_at is NULL";

						$results =  DB::select($sqlQuery);
						if (count($results) > 0) {
							$todayEvent = new Fluent($results);
							$todayEvent = $todayEvent[0];
							if (!$todayEvent) {
								$data['user_id'] = $user->id;
								$data['checkin_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 1;
								$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
								$data['checkin_time'] = date('Y-m-d H:i:s');
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								$data['check_in_datetime'] = date('Y-m-d H:i:s');
								$data['vp_device_checkin'] = '1';

								$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
								$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
								if (isset($request->CardType)) {
									$card_type = '';
									if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
										$card_type = 'VISA';
									} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
										$card_type = 'MASTERCARD';
									} else if (strtolower($request->CardType) == "jcb") {
										$card_type = 'JCB';
									} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
										$card_type = 'AMEX';
									} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
										$card_type = 'DISCOVER';
									} else {
										$card_type = $request->CardType;
									}
									$data['card_type'] = $card_type;
								}

								$data['device_type'] = "IM30";
								$data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
								$data['payment_gateway'] = $paymentGateway;
								$data['session_id'] = $request->session_id;
								$data['payment_token'] = $request->payment_token;
								$data['customer_data'] = $request->CustomerData;
								$result = Ticket::create($data);

								$facilityName = ucwords($facility->full_name);

								$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}
								//Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
								if ($user->email != '') {
									Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
								}
								$msg =  "WELCOME. #$result->ticket_number";
								if ($user->phone != '') {
									//$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
									$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
									if ($partnerDetails->user_id == self::PARTNER_ID) {
										$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
											$join->on('user_facilities.user_id', '=', 'users.id');
											$join->where('user_facilities.facility_id', "=", $facility->id);
										})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
										$name = ($getRM->slug) ? $getRM->slug : 'receipt';
									} else {
										$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
									}
									$url = env('TOUCHLESS_WEB_URL');
									$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
									if ($dynamicCheckinUrl) {
										$url = $dynamicCheckinUrl->value;
									}
									$grace_period = $facility->grace_period_minute;
									$ticket_number = base64_encode($result->ticket_number);
									$sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
									dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
								}

								$data = ['msg' => $msg];
								$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
								$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
								$data["eticket_id"] = $result->ticket_number;
								$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
								$this->log->info("Checkin done, response sent.");
								return $data;
							}

							$parkingNowTime = date("Y-m-d H:i:s");
							$parkingStartTime = $todayEvent->parking_start_time;
							$parkingEndTime = $todayEvent->parking_end_time;
							$eventStartTime = $todayEvent->start_time;
							$eventEndTime = $todayEvent->end_time;
							$this->log->info("event price enterd");
							if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
								//$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
								$driveupRate = 0;
								if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {

									$whitelistUser = WhitelistUserCard::where("expiry", $request->expiry)
										->where("card_last_four", substr($request->MaskedPAN, -4))
										//where("session_id", $request->session_id)
										//where("card_type", $request->CardType)
										//->where("facility_id", $request->facility_id)
										->where("partner_id", $facility->owner_id)
										->first();
									if (isset($whitelistUser->id)) {
										$this->log->info("whitelist event price enterd");
										$rate['price'] = $facility->base_rate;
										$taxRate = $facility->getTaxRate($rate);          // to get tax price                
										$driveupRate = $facility->base_rate + $facility->processing_fee + $taxRate;
									} else {
										$this->log->info("event price got");
										$rate['price'] = $todayEvent->driveup_event_rate;
										$taxRate = $facility->getTaxRate($rate);          // to get tax price                
										$driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee + $taxRate;
									}
								} else {
									$rate['price'] = $facility->base_rate;
									$taxRate = $facility->getTaxRate($rate);          // to get tax price                
									$driveupRate = $facility->base_rate + $facility->processing_fee + $taxRate;
								}
								$data['price'] =  $driveupRate;
								return $data;
							}
						} else {
							$data['user_id'] = $user->id;
							$data['checkin_gate'] = $request->gate_id;
							$data['facility_id'] = $request->facility_id;
							$data['is_checkin'] = 1;
							$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
							$data['checkin_time'] = date('Y-m-d H:i:s');
							$data['ticket_security_code'] = rand(1000, 9999);
							$data['partner_id'] = $facility->owner_id;
							$data['check_in_datetime'] = date('Y-m-d H:i:s');
							$data['vp_device_checkin'] = '1';

							$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
							$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
							if (isset($request->CardType)) {
								$card_type = '';
								if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
									$card_type = 'VISA';
								} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
									$card_type = 'MASTERCARD';
								} else if (strtolower($request->CardType) == "jcb") {
									$card_type = 'JCB';
								} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
									$card_type = 'AMEX';
								} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
									$card_type = 'DISCOVER';
								} else {
									$card_type = $request->CardType;
								}
								$data['card_type'] = $card_type;
							}
							$data['device_type'] = "IM30";
							$data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
							$data['payment_gateway'] = $paymentGateway;
							$data['session_id'] = $request->session_id;
							$data['payment_token'] = $request->payment_token;
							$data['customer_data'] = $request->CustomerData;
							$result = Ticket::create($data);

							$facilityName = ucwords($facility->full_name);

							$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}
							//Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
							if ($user->email != '') {
								Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
							}
							$msg =  "WELCOME. #$result->ticket_number";
							if ($user->phone != '') {
								//$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
								$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
								if ($partnerDetails->user_id == self::PARTNER_ID) {
									$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
										$join->on('user_facilities.user_id', '=', 'users.id');
										$join->where('user_facilities.facility_id', "=", $facility->id);
									})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
									$name = ($getRM->slug) ? $getRM->slug : 'receipt';
								} else {
									$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
								}
								$url = env('TOUCHLESS_WEB_URL');
								$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
								if ($dynamicCheckinUrl) {
									$url = $dynamicCheckinUrl->value;
								}
								$grace_period = $facility->grace_period_minute;
								$ticket_number = base64_encode($result->ticket_number);
								$sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
								dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
							}
							$data = ['msg' => $msg];
							$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
							$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
							$data["eticket_id"] = $result->ticket_number;
							$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
							$this->log->info("Checkin done, response sent.");
							return $data;
						}
						/*if(!$todayEvent){      
                
              }*/
					}



					if ($facility->is_prepaid_first == '0') {
						$data['user_id'] = $user->id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						$data['vp_device_checkin'] = '1';

						$data['license_plate'] = isset($request->license_plate) ? $request->license_plate : '';
						$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
						$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
						if (isset($request->CardType)) {
							$card_type = '';
							if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
								$card_type = 'VISA';
							} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
								$card_type = 'MASTERCARD';
							} else if (strtolower($request->CardType) == "jcb") {
								$card_type = 'JCB';
							} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
								$card_type = 'AMEX';
							} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
								$card_type = 'DISCOVER';
							} else {
								$card_type = $request->CardType;
							}
							$data['card_type'] = $card_type;
						}
						$data['device_type'] = "IM30";
						$data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
						$data['payment_gateway'] = $paymentGateway;
						$data['session_id'] = $request->session_id;
						$data['payment_token'] = $request->payment_token;
						$data['customer_data'] = $request->CustomerData;
						$result = Ticket::create($data);

						$facilityName = ucwords($facility->full_name);

						$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}
						if ($user->email != '') {
							Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						}
						$msg =  "WELCOME. #$result->ticket_number";
						if ($user->phone != '') {
							//$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
							$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
							if ($partnerDetails->user_id == self::PARTNER_ID) {
								$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
									$join->on('user_facilities.user_id', '=', 'users.id');
									$join->where('user_facilities.facility_id', "=", $facility->id);
								})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
								$name = ($getRM->slug) ? $getRM->slug : 'receipt';
							} else {
								$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
							}
							$url = env('TOUCHLESS_WEB_URL');
							$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
							if ($dynamicCheckinUrl) {
								$url = $dynamicCheckinUrl->value;
							}
							$grace_period = $facility->grace_period_minute;
							$ticket_number = base64_encode($result->ticket_number);
							$sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
							dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
						}

						$data = ['msg' => $msg];
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						$data["eticket_id"] = $result->ticket_number;
						$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
						$this->log->info("Checkin done, response sent.");
						return $data;
					}
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
					$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
					$diff_in_hours = $arrival_time->diffInRealHours($from);

					//$isMember = 0;
					$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
					if ($rate == false) {
						throw new ApiGenericException('Garage is not available.');
					}
					$data = [];
					//$data ['session_id'] = $request->session_id;
					//$data ['price'] = $rate['price']  + $facility->processing_fee;
					$data['price'] = $rate['price']  + $facility->processing_fee;
					$this->log->info("price sent, response sent.");
					return $data;
				}
			}

			if ($facility->facility_booking_type == '1') {
				throw new ApiGenericException('Prepaid booking is not allowed.');
			}

			if ($facility->check_vehicle_enabled == '1') {
				$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['check_in_datetime'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['vp_device_checkin'] = '1';
			$data['partner_id'] = $facility->owner_id;

			if ($reservation) {
				$data['reservation_id'] = $reservation->id;
				$data['check_in_datetime'] = $reservation->start_timestamp;
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
				$data['total'] = $reservation->total;
				$data['grand_total'] = $reservation->total;
				$data['length'] = $reservation->length;
				$data['user_pass_id'] = $reservation->user_pass_id;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['vp_device_checkin'] = '0';
				$reservation->is_ticket = '1';
				$reservation->save();
			}
			if (count($userPass) > 0) {
				$data['check_in_datetime'] = date('Y-m-d H:i:s');
				$data['checkout_datetime'] = date('Y-m-d H:i:s');
				$data['user_pass_id'] = $userPass->id;
				$data['vp_device_checkin'] = '0';

				$userPass->consume_days = $userPass->consume_days + 1;
				$userPass->remaining_days = $userPass->remaining_days - 1;
				$userPass->save();
			}

			$data['license_plate'] = isset($request->license_plate) ? $request->license_plate : '';
			$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
			$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
			if (isset($request->CardType)) {
				$card_type = '';
				if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
					$card_type = 'VISA';
				} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
					$card_type = 'MASTERCARD';
				} else if (strtolower($request->CardType) == "jcb") {
					$card_type = 'JCB';
				} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
					$card_type = 'AMEX';
				} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
					$card_type = 'DISCOVER';
				} else {
					$card_type = $request->CardType;
				}
				$data['card_type'] = $card_type;
			}

			$data['device_type'] = "IM30";
			$data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
			$data['payment_gateway'] = $paymentGateway;
			$data['session_id'] = $request->session_id;
			$data['payment_token'] = $request->payment_token;
			$data['customer_data'] = $request->CustomerData;
			$result = Ticket::create($data);

			$this->log->info("checkin done --" . json_encode($result));
			if ($result) {
				$facilityName = ucwords($facility->full_name);
				if ($reservation) {
					//Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
				} else {
					try {

						Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						if ($partnerDetails->user_id == self::PARTNER_ID) {
							$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
								$join->on('user_facilities.user_id', '=', 'users.id');
								$join->where('user_facilities.facility_id', "=", $facility->id);
							})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
							$name = ($getRM->slug) ? $getRM->slug : 'receipt';
						} else {
							$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						}
						$url = env('TOUCHLESS_WEB_URL');
						$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
						if ($dynamicCheckinUrl) {
							$url = $dynamicCheckinUrl->value;
						}
						$grace_period = $facility->grace_period_minute;
						$ticket_number = base64_encode($result->ticket_number);
						$sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
					} catch (\Exception $e) {
						$this->log->error("System exception -" . $e->getMessage());
					}
				}
				$this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				//return "Welcome to $facilityName. #$result->ticket_number.";
				$msg =  "WELCOME. #$result->ticket_number";

				$data = ['msg' => $msg];
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data["eticket_id"] = $result->ticket_number;
				$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
				$this->log->info("Checkin done, response sent.");
				return $data;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
		if (isset($gate) && $gate->gate_type == "exit") {

			//checkout when user does not tap checkin
			if ($request->eticket_id != '') {
				$ticket = Ticket::where('ticket_number', $request->eticket_id)->where('is_checkout', '0')->first();
				if (!$ticket) {
					throw new ApiGenericException('You have already checkout.');
				}

				if (isset($user->id)) {
					$ticket->user_id = $user->id;
				}
				$ticket->session_id = $request->session_id;
				$ticket->save();

				if (!isset($facility->FacilityPaymentDetails->id)) {
					throw new ApiGenericException('Payment details not defined against this garage.');
				}


				//overstay case

				if ($ticket->estimated_checkout != '') {
					$this->log->info("eticket already paid.");
					$overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();

					if ($overstayExist) {
						if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

							$ticket->is_checkout = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->checkout_license_plate = $request->license_plate;
							$ticket->checkout_mode = '3';
							$ticket->is_transaction_status = '0';
							$ticket->save();

							if ($request->license_plate != '') {
								if ($facility->license_plate_model != '') {
									$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
									$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
								}
							}

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

							$data = ['msg' => $msg];
							$data['is_phone_linked'] = '0';
							$data['phone_linked_msg'] = '';
							if (isset($user->phone)) {
								$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
								if ($partnerDetails->user_id == self::PARTNER_ID) {
									$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
										$join->on('user_facilities.user_id', '=', 'users.id');
										$join->where('user_facilities.facility_id', "=", $facility->id);
									})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
									$name = ($getRM->slug) ? $getRM->slug : 'receipt';
								} else {
									$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
								}
								$url = env('RECEIPT_URL');
								$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
								//changes for breeze specific URL from DB
								if ($dynamicReceiptUrl) {
									$url = $dynamicReceiptUrl->value;
								}
								$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

								dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
								$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
								$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
							}
							$data["eticket_id"] = $ticket->ticket_number;
							$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');

							$this->log->info("SMS sent, response sent");
							return $data;
						}
						$this->log->info("eticket overstay created.");
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->estimated_checkout);
						$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
						$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->estimated_checkout);
						/** this function is used to get Availability Information for respective facility **/
						$isMember = 0;

						$this->log->info("get Diff in Hours : {$diff_in_hours}");
						$rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
						$this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

						$isMember = 0;
						if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
							$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
						} else {
							$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, $isMember);
						}
						$taxRate = $ticket->getTaxRate($rate);          // to get tax price
						$data['price'] =  $rate['price'] + $taxRate;
						$data['eticket_id'] =  $ticket->ticket_number;
						$data['is_overstay'] =  '1';
						return $data;
					} else {
						if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

							$ticket->is_checkout = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->checkout_license_plate = $request->license_plate;
							$ticket->checkout_mode = '3';
							$ticket->is_transaction_status = '0';
							$ticket->save();

							if ($request->license_plate != '') {
								if ($facility->license_plate_model != '') {
									$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
									$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
								}
							}

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

							$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
							if ($partnerDetails->user_id == self::PARTNER_ID) {
								$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
									$join->on('user_facilities.user_id', '=', 'users.id');
									$join->where('user_facilities.facility_id', "=", $facility->id);
								})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
								$name = ($getRM->slug) ? $getRM->slug : 'receipt';
							} else {
								$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
							}
							$url = env('RECEIPT_URL');
							$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
							//changes for breeze specific URL from DB
							if ($dynamicReceiptUrl) {
								$url = $dynamicReceiptUrl->value;
							}
							$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

							dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
							$this->log->info("SMS sent, response sent");
							$data = ['msg' => $msg];
							$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
							$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
							$data["eticket_id"] = $ticket->ticket_number;
							$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
							return $data;
						}
						$this->log->info("overstay created.");
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date);
						$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
						$diff_in_hours = $ticket->getCheckOutCurrentTime(true);
						/** this function is used to get Availability Information for respective facility **/
						$isMember = 0;

						$this->log->info("get Diff in Hours : {$diff_in_hours}");
						$rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
						$this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

						$isMember = 0;
						if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
							$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
						} else {
							$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, $isMember);
						}
						$taxRate = $ticket->getTaxRate($rate);          // to get tax price
						$data['price'] =  $rate['price'] + $taxRate;
						$data['eticket_id'] =  $ticket->ticket_number;
						$data['is_overstay'] =  '1';
						return $data;
					}
				}



				$arrival_time = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
				$parkingNowTime = date("Y-m-d H:i:s");

				$diff_in_hours = $ticket->getCheckOutCurrentTime(true);

				$isMember = 0;
				if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
					$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
					$rate['description'] = self::HOURLY_RATE;
				} else {
					$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
					if (!isset($rate['description'])) {
						$rate['description'] = self::BASE_RATE;
					}
				}

				$rateId = isset($rate['id']) ? $rate['id'] : '';
				$rateDescription = isset($rate['description']) ? $rate['description'] : '';
				$priceBreakUp = $ticket->priceBreakUp($rate);
				$this->log->info("eticket priceBreakUp " . json_encode($priceBreakUp));
				if ($priceBreakUp['payable_amount'] > 0) {

					if ($facility->is_cloud_payment_enabled != '1' || $facility->is_cloud_payment_enabled != 1) {

						// $ticket->processing_fee  = $priceBreakUp['processing_fee'];
						// $ticket->tax_fee        = $priceBreakUp['tax_rate'];
						// $ticket->parking_amount  = $priceBreakUp['parking_amount'];
						// $ticket->paid_amount     = $priceBreakUp['paid_amount'];
						// $ticket->discount_amount = $priceBreakUp['discount_amount'];
						// $ticket->grand_total     = $priceBreakUp['payable_amount'];
						// $ticket->total     = $priceBreakUp['total'];
						// $ticket->length     = $diff_in_hours;
						// $ticket->save();
						QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
						$data['price'] =  $priceBreakUp['payable_amount'];
						$data['eticket_id'] =  $ticket->ticket_number;
						$data['payment_token'] =  $ticket->payment_token;
						$this->log->info("price sent, response sent");
						return $data;
					}

					$refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $priceBreakUp['payable_amount'], $request);
					$this->log->info("Payment Response :" . json_encode($refundstatus));
					if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
						throw new ApiGenericException("Payment failed. Please try again");
					}
					$planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
					$ticket->anet_transaction_id = $planetTransaction->id;
				}

				if ($ticket->is_offline_payment == '0' && $ticket->paid_type == '9') {
					if ($priceBreakUp['total'] > 0) {
						$result['price'] =  $priceBreakUp['total'];
						$result['eticket_id'] =  $ticket->ticket_number;
						$result['payment_token'] =  $ticket->payment_token;
						QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
						// $ticket->processing_fee  = $priceBreakUp['processing_fee'];
						// $ticket->tax_fee        = $priceBreakUp['tax_rate'];
						// $ticket->parking_amount  = $priceBreakUp['parking_amount'];
						// $ticket->paid_amount     = $priceBreakUp['paid_amount'];
						// $ticket->discount_amount = $priceBreakUp['discount_amount'];
						// $ticket->grand_total     = $priceBreakUp['total'];
						// $ticket->total     = $priceBreakUp['total'];
						// $ticket->length     = $diff_in_hours;
						// $ticket->rate_id = $rateId;
						// $ticket->rate_description = $rateDescription;
						// $ticket->save();
						$this->log->info("eticket if payment declined and hit api again, price sent, response sent");
						return $result;
					}
				}

				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}

				$ticket->processing_fee  = $priceBreakUp['processing_fee'];
				$ticket->tax_fee        = $priceBreakUp['tax_rate'];
				$ticket->parking_amount  = $priceBreakUp['parking_amount'];
				$ticket->paid_amount     = $priceBreakUp['paid_amount'];
				$ticket->discount_amount = $priceBreakUp['discount_amount'];
				$ticket->grand_total     = $priceBreakUp['payable_amount'];
				$ticket->total     = $priceBreakUp['total'];
				$ticket->length     = $diff_in_hours;

				$ticket->checkout_mode = '3';
				$ticket->is_checkout = '1';
				$ticket->is_checkin = '1';
				$ticket->checkout_gate = $request->gate_id;
				$ticket->checkout_time = date("Y-m-d H:i:s");
				$ticket->checkout_datetime = date("Y-m-d H:i:s");
				$ticket->is_cloud_payment = '1';
				$ticket->checkout_license_plate = $request->license_plate;
				$ticket->rate_id = $rateId;
				$ticket->rate_description = $rateDescription;
				$ticket->is_transaction_status = '0';
				$ticket->save();

				if ($request->license_plate != '') {
					if ($facility->license_plate_model != '') {
						$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
						$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
					}
				}

				$this->saveTransactionData($rate, $ticket);

				$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
				$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
				if (isset($user->phone)) {
					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				}
				$this->log->info("SMS sent, response sent");
				$data = ['msg' => $msg];
				$data['is_phone_linked'] = isset($user->phone) ? '1' : '0';
				$data['phone_linked_msg'] = isset($user->phone) != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data["eticket_id"] = $ticket->ticket_number;
				$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
				$this->log->info("User checkout, response sent");
				return $data;
			}
			$ticket = Ticket::with(["facility", "user"])->where('user_id', $user->id)->where('facility_id', $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();

			$updatedPaidAmount = 0;
			if (!$ticket) {

				$reservation = Reservation::with('user')->where('facility_id', $request->facility_id)->where("user_id", $user->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
				$userPass = [];
				if (!$reservation) {

					$userPass = UserPass::with('user')->where('facility_id', $request->facility_id)->where("user_id", $user->id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days', '>', 0)->orderBy('id', 'DESC')->first();
					if (!$userPass) {
						throw new ApiGenericException("'NO TICKET FOUND' USE THE SAME CARD USED AT ENTRY");
					}
				}
				if ($facility->facility_booking_type == '1') {
					throw new ApiGenericException('Prepaid booking is not allowed.');
				}

				$data['user_id'] = $user->id;
				$data['checkout_gate'] = $request->gate_id;
				$data['facility_id'] = $request->facility_id;
				//$data['is_checkin'] = 1;
				$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
				$data['ticket_security_code'] = rand(1000, 9999);
				$data['vp_device_checkin'] = '0';
				$data['partner_id'] = $facility->owner_id;
				$data['is_checkout'] = 1;
				$data['checkout_without_checkin'] = '1';
				$data['checkout_time'] = date('Y-m-d H:i:s');
				if ($reservation) {
					$data['reservation_id'] = $reservation->id;
					$data['check_in_datetime'] = $reservation->start_timestamp;
					$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
					$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
					$data['total'] = $reservation->total;
					$data['grand_total'] = $reservation->total;
					$data['length'] = $reservation->length;
					$data['user_pass_id'] = $reservation->user_pass_id;
					//$data['checkin_time'] = date('Y-m-d H:i:s');
					$reservation->is_ticket = '1';
					$reservation->save();
				}

				if (count($userPass) > 0) {
					$data['check_in_datetime'] = date('Y-m-d H:i:s');
					$data['checkout_datetime'] = date('Y-m-d H:i:s');
					$data['user_pass_id'] = $userPass->id;

					$userPass->consume_days = $userPass->consume_days + 1;
					$userPass->remaining_days = $userPass->remaining_days - 1;
					$userPass->save();
				}

				$data['payment_gateway'] = $paymentGateway;
				$data['session_id'] = $request->session_id;
				$result = Ticket::create($data);

				$this->log->info("townsend checkin done --" . json_encode($result));

				if ($result) {

					$facilityName = ucwords($facility->full_name);

					$this->log->info("direct checkout SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
					//check gate api

					//return "Welcome to $facilityName. #$result->ticket_number.";
					$msg =  "THANK YOU FOR VISITING. #$result->ticket_number";
					dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
					$data = ['msg' => $msg];
					$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
					$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					$data["eticket_id"] = $result->ticket_number;
					$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '0');
					$this->log->info("SMS sent, response sent");
					return $data;
				} else {
					throw new ApiGenericException('Something wrong.');
				}
			}


			if ($ticket->anet_transaction_id == '') {

				if ($ticket->user_pass_id != '' || $ticket->reservation_id != '') {
					$this->log->info("Session Reservation case");
					$checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
					$checkoutTime = date("Y-m-d H:i:S", strtotime($ticket->estimated_checkout));
					$parkingNowTime = date("Y-m-d H:i:s");
					if (strtotime($parkingNowTime) > strtotime($checkoutTime)) {
						if ($ticket->payment_date != '') {
							$parkingStartTime = $ticket->payment_date;
						} else {
							$parkingStartTime = $ticket->estimated_checkout;
						}

						$overstayTicketExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "ASC")->first();
						if ($overstayTicketExist) {
							$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $$overstayTicketExist->payment_date);
							$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayTicketExist->check_in_datetime);
						} else {
							$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $parkingStartTime);
							$diff_in_hours = $ticket->getCheckOutCurrentTime(true);
						}

						$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);

						/** this function is used to get Availability Information for respective facility **/


						if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
							$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
							$rate['description'] = self::HOURLY_RATE;
						} else {
							$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, 0, $isMember);
							if (!isset($rate['description'])) {
								$rate['description'] = self::BASE_RATE;
							}
						}


						//overstay section start
						if ($ticket->estimated_checkout != '') {
							$overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();
							if ($overstayExist) {
								if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
									$ticket->is_checkout = '1';
									$ticket->checkout_gate = $request->gate_id;
									$ticket->checkout_time = date("Y-m-d H:i:s");
									$ticket->checkout_datetime = date("Y-m-d H:i:s");
									$ticket->checkout_license_plate = $request->license_plate;
									$ticket->checkout_mode = '3';
									$ticket->is_transaction_status = '0';
									$ticket->save();

									if ($request->license_plate != '') {
										if ($facility->license_plate_model != '') {
											$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
											$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
										}
									}

									//check gate api
									if ($facility->open_gate_enabled == '1') {
										$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
										if ($gateStatus == "true") {
										} else {
											throw new ApiGenericException($gateStatus);
										}
									}

									$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
									$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

									$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
									if ($partnerDetails->user_id == self::PARTNER_ID) {
										$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
											$join->on('user_facilities.user_id', '=', 'users.id');
											$join->where('user_facilities.facility_id', "=", $facility->id);
										})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
										$name = ($getRM->slug) ? $getRM->slug : 'receipt';
									} else {
										$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
									}
									$url = env('RECEIPT_URL');
									$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
									//changes for breeze specific URL from DB
									if ($dynamicReceiptUrl) {
										$url = $dynamicReceiptUrl->value;
									}
									$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

									dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
									$this->log->info("SMS sent, response sent");
									$data = ['msg' => $msg];
									$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
									$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
									$data["eticket_id"] = $ticket->ticket_number;
									$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
									return $data;
								} else {
									$this->log->info("eticket overstay created.");
									$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->payment_date);
									$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
									$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->payment_date);
									$isMember = 0;
									if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {

										$rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
										$rate['description'] = self::HOURLY_RATE;
									} else {
										$rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
										if (!isset($rate['description'])) {
											$rate['description'] = self::BASE_RATE;
										}
									}
									$taxRate = $ticket->getTaxRate($rate);          // to get tax price
									$rate['price'] = $rate['price'] + $taxRate;
									if ($ticket->facility->is_cloud_payment_enabled == '1') {

										$this->log->info("is_lpr_cloud_payment_enabled entered for overstay");
										if ($ticket->session_id == '') {
											$data['msg'] = "Amount Due";
											$data['amount'] =  $rate['price'];
											$data['eticket_id'] =  $ticket->ticket_number;
											$data['is_overstay'] =  '1';
											$data['response_type'] =  '3';
											$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
											$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
											$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
											$ticket->is_transaction_status = '0';
											$ticket->save();
											return $data;
										}

										$refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $rate['price'], $data);
										$this->log->info("Overstay Payment Response :" . json_encode($refundstatus));
										//if payment error start
										if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
											$this->log->error(" Overstay Payment failed :");

											$data['msg'] = "Amount Due";
											$data['amount'] =  $rate['price'];
											$data['eticket_id'] =  $ticket->ticket_number;
											$data['is_overstay'] =  '1';
											$data['response_type'] =  '3';
											$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
											$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
											$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
											$ticket->is_transaction_status = '0';
											$ticket->save();
											return $data;
										}

										$planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, '1');

										$overstay = $this->saveOverstayTicketDetails($ticket, $rate['price'], $diff_in_hours);
										$overstay->total = $rate['price'];
										$overstay->grand_total = $rate['price'];
										$overstay->anet_transaction_id = $planetTransaction->id;
										$overstay->length = $diff_in_hours;
										$overstay->tax_fee = $taxRate;
										$overstay->save();

										$this->saveOverstayTransactionData($ticket, $overstay, $ticket->facility);

										$ticket->checkout_mode = '3';
										$ticket->is_checkout = '1';
										$ticket->checkout_gate = $request->gate_id;
										$ticket->checkout_time = date("Y-m-d H:i:s");
										$ticket->checkout_datetime = date("Y-m-d H:i:s");
										$ticket->is_cloud_payment = '1';
										$ticket->checkout_license_plate = $request->license_plate;
										$ticket->is_transaction_status = '0';
										$ticket->save();

										if ($request->license_plate != '') {
											if ($facility->license_plate_model != '') {
												$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
												$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
											}
										}

										$this->saveTransactionData($rate, $ticket);

										$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
										$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

										$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
										if ($partnerDetails->user_id == self::PARTNER_ID) {
											$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
												$join->on('user_facilities.user_id', '=', 'users.id');
												$join->where('user_facilities.facility_id', "=", $facility->id);
											})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
											$name = ($getRM->slug) ? $getRM->slug : 'receipt';
										} else {
											$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
										}
										$url = env('RECEIPT_URL');
										$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
										//changes for breeze specific URL from DB
										if ($dynamicReceiptUrl) {
											$url = $dynamicReceiptUrl->value;
										}
										$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

										dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
										$this->log->info("SMS sent, response sent");
										$data = ['msg' => $msg];
										$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
										$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
										$data["eticket_id"] = $ticket->ticket_number;
										$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
										$this->log->info("User checkout, response sent");
										return $data;
									}

									//$data['msg'] = "Amount Due";
									$data['price'] =  $rate['price'];
									$data['eticket_id'] =  $ticket->ticket_number;
									$data['is_overstay'] =  '1';
									return $data;
								}
							} else {

								if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
									$ticket->is_checkout = '1';
									$ticket->checkout_gate = $request->gate_id;
									$ticket->checkout_time = date("Y-m-d H:i:s");
									$ticket->checkout_datetime = date("Y-m-d H:i:s");
									$ticket->checkout_license_plate = $request->license_plate;
									$ticket->checkout_mode = '3';
									$ticket->is_transaction_status = '0';
									$ticket->save();

									if ($request->license_plate != '') {
										if ($facility->license_plate_model != '') {
											$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
											$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
										}
									}

									//check gate api
									if ($facility->open_gate_enabled == '1') {
										$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
										if ($gateStatus == "true") {
										} else {
											throw new ApiGenericException($gateStatus);
										}
									}

									$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
									$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

									$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
									if ($partnerDetails->user_id == self::PARTNER_ID) {
										$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
											$join->on('user_facilities.user_id', '=', 'users.id');
											$join->where('user_facilities.facility_id', "=", $facility->id);
										})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
										$name = ($getRM->slug) ? $getRM->slug : 'receipt';
									} else {
										$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
									}
									$url = env('RECEIPT_URL');
									$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
									//changes for breeze specific URL from DB
									if ($dynamicReceiptUrl) {
										$url = $dynamicReceiptUrl->value;
									}
									$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

									dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
									$this->log->info("SMS sent, response sent");
									$data = ['msg' => $msg];
									$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
									$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
									$data["eticket_id"] = $ticket->ticket_number;
									$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
									return $data;
								} else {
									$this->log->info("overstay created.");

									$overstayCheckinTime = $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout;

									$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayCheckinTime);
									$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
									$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayCheckinTime);

									$isMember = 0;
									if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {
										$rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
										$rate['description'] = self::HOURLY_RATE;
									} else {
										$rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
										if (!isset($rate['description'])) {
											$rate['description'] = self::BASE_RATE;
										}
									}

									$taxRate = $ticket->getTaxRate($rate);          // to get tax price
									$rate['price'] = $rate['price'] + $taxRate;
									if ($ticket->facility->is_cloud_payment_enabled == '1') {

										$this->log->info("is_cloud_payment_enabled entered for overstay");
										if ($ticket->session_id == '') {
											$data['msg'] = "Amount Due";
											$data['amount'] =  $rate['price'];
											$data['eticket_id'] =  $ticket->ticket_number;
											$data['is_overstay'] =  '1';
											$data['response_type'] =  '3';
											$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
											$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
											$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
											$ticket->is_transaction_status = '0';
											$ticket->save();
											return $data;
										}

										$refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $rate['price'], $data);
										$this->log->info("Overstay Payment Response :" . json_encode($refundstatus));
										//if payment error start
										if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
											$this->log->error(" Overstay Payment failed :");

											$data['msg'] = "Amount Due";
											$data['amount'] =  $rate['price'];
											$data['eticket_id'] =  $ticket->ticket_number;
											$data['is_overstay'] =  '1';
											$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
											$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
											$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
											$ticket->is_transaction_status = '0';
											$ticket->save();
											return $data;
										}

										$planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, '1');

										$overstay = $this->saveOverstayTicketDetails($ticket, $rate['price'], $diff_in_hours);
										$overstay->total = $rate['price'];
										$overstay->grand_total = $rate['price'];
										$overstay->anet_transaction_id = $planetTransaction->id;
										$overstay->length = $diff_in_hours;
										$overstay->tax_fee = $taxRate;
										$overstay->save();

										$this->saveOverstayTransactionData($ticket, $overstay, $ticket->facility);

										$ticket->checkout_mode = '3';
										$ticket->is_checkout = '1';
										$ticket->checkout_gate = $request->gate_id;
										$ticket->checkout_time = date("Y-m-d H:i:s");
										$ticket->checkout_datetime = date("Y-m-d H:i:s");
										$ticket->is_cloud_payment = '1';
										$ticket->checkout_license_plate = $request->license_plate;
										$ticket->is_transaction_status = '0';
										$ticket->save();

										if ($request->license_plate != '') {
											if ($facility->license_plate_model != '') {
												$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
												$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
											}
										}

										$this->saveTransactionData($rate, $ticket);

										$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
										$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

										$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
										if ($partnerDetails->user_id == self::PARTNER_ID) {
											$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
												$join->on('user_facilities.user_id', '=', 'users.id');
												$join->where('user_facilities.facility_id', "=", $facility->id);
											})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
											$name = ($getRM->slug) ? $getRM->slug : 'receipt';
										} else {
											$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
										}
										$url = env('RECEIPT_URL');
										$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
										//changes for breeze specific URL from DB
										if ($dynamicReceiptUrl) {
											$url = $dynamicReceiptUrl->value;
										}
										$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

										dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
										$this->log->info("SMS sent, response sent");
										$data = ['msg' => $msg];
										$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
										$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
										$data["eticket_id"] = $ticket->ticket_number;
										$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
										$this->log->info("User checkout, response sent");
										return $data;
									}

									//$data['msg'] = "Amount Due";
									$data['price'] =  $rate['price'];
									$data['eticket_id'] =  $ticket->ticket_number;
									$data['is_overstay'] =  '1';
									return $data;
								}
							}
						}

						//overstay section end


						$data['eticket_id'] =  $ticket->ticket_number;
						$data['price'] =  $rate['price'];
						$data['is_overstay'] =  '1';
						return $data;
					}
				} else {

					if ($ticket->is_offline_payment != '0') {
						$this->log->info("admin payment already paid.");
						$overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();

						if ($overstayExist) {
							if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
								$ticket->is_checkout = '1';
								$ticket->checkout_gate = $request->gate_id;
								$ticket->checkout_time = date("Y-m-d H:i:s");
								$ticket->checkout_datetime = date("Y-m-d H:i:s");
								$ticket->checkout_license_plate = $request->license_plate;
								$ticket->checkout_mode = '3';
								$ticket->is_transaction_status = '0';
								$ticket->save();

								if ($request->license_plate != '') {
									if ($facility->license_plate_model != '') {
										$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
										$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
									}
								}

								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
								$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

								$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
								if ($partnerDetails->user_id == self::PARTNER_ID) {
									$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
										$join->on('user_facilities.user_id', '=', 'users.id');
										$join->where('user_facilities.facility_id', "=", $facility->id);
									})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
									$name = ($getRM->slug) ? $getRM->slug : 'receipt';
								} else {
									$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
								}
								$url = env('RECEIPT_URL');
								$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
								//changes for breeze specific URL from DB
								if ($dynamicReceiptUrl) {
									$url = $dynamicReceiptUrl->value;
								}
								$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

								dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
								$this->log->info("SMS sent, response sent");
								$data = ['msg' => $msg];
								$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
								$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
								$data["eticket_id"] = $ticket->ticket_number;
								$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
								return $data;
							} else {
								$this->log->info("eticket overstay created.");
								$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->payment_date);
								$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
								$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->payment_date);
								$isMember = 0;
								if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {
									$rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
									$rate['description'] = self::HOURLY_RATE;
								} else {
									$rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
									if (!isset($rate['description'])) {
										$rate['description'] = self::BASE_RATE;
									}
								}
								$taxRate = $ticket->getTaxRate($rate);          // to get tax price
								$rate['price'] = $rate['price'] + $taxRate;
								if ($ticket->facility->is_cloud_payment_enabled == '1') {

									$this->log->info("is_lpr_cloud_payment_enabled entered for overstay");
									if ($ticket->session_id == '') {
										$data['msg'] = "Amount Due";
										$data['amount'] =  $rate['price'];
										$data['eticket_id'] =  $ticket->ticket_number;
										$data['is_overstay'] =  '1';
										$data['response_type'] =  '3';
										$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
										$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
										$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
										$ticket->is_transaction_status = '0';
										$ticket->save();
										return $data;
									}

									$refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $rate['price'], $data);
									$this->log->info("Overstay Payment Response :" . json_encode($refundstatus));
									//if payment error start
									if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
										$this->log->error(" Overstay Payment failed :");

										$data['msg'] = "Amount Due";
										$data['amount'] =  $rate['price'];
										$data['eticket_id'] =  $ticket->ticket_number;
										$data['is_overstay'] =  '1';
										$data['response_type'] =  '3';
										$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
										$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
										$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
										$ticket->is_transaction_status = '0';
										$ticket->save();
										return $data;
									}

									$planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, '1');

									$overstay = $this->saveOverstayTicketDetails($ticket, $rate['price'], $diff_in_hours);
									$overstay->total = $rate['price'];
									$overstay->grand_total = $rate['price'];
									$overstay->anet_transaction_id = $planetTransaction->id;
									$overstay->length = $diff_in_hours;
									$overstay->tax_fee = $taxRate;
									$overstay->save();

									$this->saveOverstayTransactionData($ticket, $overstay, $ticket->facility);

									$ticket->checkout_mode = '3';
									$ticket->is_checkout = '1';
									$ticket->checkout_gate = $request->gate_id;
									$ticket->checkout_time = date("Y-m-d H:i:s");
									$ticket->checkout_datetime = date("Y-m-d H:i:s");
									$ticket->is_cloud_payment = '1';
									$ticket->checkout_license_plate = $request->license_plate;
									$ticket->is_transaction_status = '0';
									$ticket->save();

									if ($request->license_plate != '') {
										if ($facility->license_plate_model != '') {
											$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
											$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
										}
									}

									//$this->saveTransactionData($rate, $ticket);

									$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
									$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

									$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
									if ($partnerDetails->user_id == self::PARTNER_ID) {
										$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
											$join->on('user_facilities.user_id', '=', 'users.id');
											$join->where('user_facilities.facility_id', "=", $facility->id);
										})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
										$name = ($getRM->slug) ? $getRM->slug : 'receipt';
									} else {
										$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
									}
									$url = env('RECEIPT_URL');
									$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
									//changes for breeze specific URL from DB
									if ($dynamicReceiptUrl) {
										$url = $dynamicReceiptUrl->value;
									}
									$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

									dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
									$this->log->info("SMS sent, response sent");
									$data = ['msg' => $msg];
									$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
									$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
									$data["eticket_id"] = $ticket->ticket_number;
									$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
									$this->log->info("User checkout, response sent");
									return $data;
								}

								//$data['msg'] = "Amount Due";
								$data['price'] =  $rate['price'];
								$data['eticket_id'] =  $ticket->ticket_number;
								$data['is_overstay'] =  '1';
								return $data;
							}
						} else {

							if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
								$ticket->is_checkout = '1';
								$ticket->checkout_gate = $request->gate_id;
								$ticket->checkout_time = date("Y-m-d H:i:s");
								$ticket->checkout_datetime = date("Y-m-d H:i:s");
								$ticket->checkout_license_plate = $request->license_plate;
								$ticket->checkout_mode = '3';
								$ticket->is_transaction_status = '0';
								$ticket->save();

								if ($request->license_plate != '') {
									if ($facility->license_plate_model != '') {
										$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
										$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
									}
								}

								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
								$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

								$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
								if ($partnerDetails->user_id == self::PARTNER_ID) {
									$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
										$join->on('user_facilities.user_id', '=', 'users.id');
										$join->where('user_facilities.facility_id', "=", $facility->id);
									})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
									$name = ($getRM->slug) ? $getRM->slug : 'receipt';
								} else {
									$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
								}
								$url = env('RECEIPT_URL');
								$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
								//changes for breeze specific URL from DB
								if ($dynamicReceiptUrl) {
									$url = $dynamicReceiptUrl->value;
								}
								$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

								dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
								$this->log->info("SMS sent, response sent");
								$data = ['msg' => $msg];
								$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
								$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
								return $data;
							} else {
								$this->log->info("overstay created.");

								$overstayCheckinTime = $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout;

								$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayCheckinTime);
								$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
								$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayCheckinTime);

								$isMember = 0;
								if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {
									$rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
								} else {
									$rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
								}

								$taxRate = $ticket->getTaxRate($rate);          // to get tax price
								$rate['price'] = $rate['price'] + $taxRate;
								if ($ticket->facility->is_cloud_payment_enabled == '1') {

									$this->log->info("is_cloud_payment_enabled entered for overstay");
									if ($ticket->session_id == '') {
										$data['msg'] = "Amount Due";
										$data['amount'] =  $rate['price'];
										$data['eticket_id'] =  $ticket->ticket_number;
										$data['is_overstay'] =  '1';
										$data['response_type'] =  '3';
										$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
										$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
										$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
										$ticket->is_transaction_status = '0';
										$ticket->save();
										return $data;
									}

									$refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $rate['price'], $data);
									$this->log->info("Overstay Payment Response :" . json_encode($refundstatus));
									//if payment error start
									if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
										$this->log->error(" Overstay Payment failed :");

										$data['msg'] = "Amount Due";
										$data['amount'] =  $rate['price'];
										$data['eticket_id'] =  $ticket->ticket_number;
										$data['is_overstay'] =  '1';
										$data['response_type'] =  '3';
										$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
										$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
										$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
										$ticket->is_transaction_status = '0';
										$ticket->save();
										return $data;
									}

									$planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, '1');

									$overstay = $this->saveOverstayTicketDetails($ticket, $rate['price'], $diff_in_hours);
									$overstay->total = $rate['price'];
									$overstay->grand_total = $rate['price'];
									$overstay->anet_transaction_id = $planetTransaction->id;
									$overstay->length = $diff_in_hours;
									$overstay->tax_fee = $taxRate;
									$overstay->save();

									$this->saveOverstayTransactionData($ticket, $overstay, $ticket->facility);

									$ticket->checkout_mode = '3';
									$ticket->is_checkout = '1';
									$ticket->checkout_gate = $request->gate_id;
									$ticket->checkout_time = date("Y-m-d H:i:s");
									$ticket->checkout_datetime = date("Y-m-d H:i:s");
									$ticket->is_cloud_payment = '1';
									$ticket->checkout_license_plate = $request->license_plate;
									$ticket->is_transaction_status = '0';
									$ticket->save();

									if ($request->license_plate != '') {
										if ($facility->license_plate_model != '') {
											$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
											$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
										}
									}

									//$this->saveTransactionData($rate, $ticket);

									$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
									$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

									$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
									if ($partnerDetails->user_id == self::PARTNER_ID) {
										$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
											$join->on('user_facilities.user_id', '=', 'users.id');
											$join->where('user_facilities.facility_id', "=", $facility->id);
										})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
										$name = ($getRM->slug) ? $getRM->slug : 'receipt';
									} else {
										$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
									}
									$url = env('RECEIPT_URL');
									$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
									//changes for breeze specific URL from DB
									if ($dynamicReceiptUrl) {
										$url = $dynamicReceiptUrl->value;
									}
									$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

									dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
									$this->log->info("SMS sent, response sent");
									$data = ['msg' => $msg];
									$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
									$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
									$data["eticket_id"] = $ticket->ticket_number;
									$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
									$this->log->info("User checkout, response sent");
									return $data;
								}

								//$data['msg'] = "Amount Due";
								$data['price'] =  $rate['price'];
								$data['eticket_id'] =  $ticket->ticket_number;
								$data['is_overstay'] =  '1';
								return $data;
							}
						}
					}

					$checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
					$parkingNowTime = date("Y-m-d H:i:s");

					//$checkinTime = "2023-02-07 00:48:54";
					//$parkingNowTime = "2023-02-07 01:06:19";
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinTime);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);

					$diff_in_hours = $arrival_time->diffInRealHours($from);
					$diff_in_mins = $arrival_time->diffInRealMinutes($from);
					$diff_in_secs = $arrival_time->diffInSeconds($from);

					if ($diff_in_mins < 60) {
						$diff_in_hours = $diff_in_mins / 100;
					}
					if ($diff_in_mins > 59) {
						if ($diff_in_hours > 0) {
							$diffInMints = $diff_in_mins - ($diff_in_hours * 60);
							$diff_in_hours = $diff_in_hours . '.' . $diffInMints;
						} else {
							$diff_in_hours = number_format($diff_in_mins / 60, 2);
						}
					}
					if ($diff_in_secs < 60) {
						$diff_in_hours = .01;
					}

					$isMember = 0;
					if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
						$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
						$rate['description'] = self::HOURLY_RATE;
					} else {
						$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
						if (!isset($rate['description'])) {
							$rate['description'] = self::BASE_RATE;
						}
					}

					if ($rate['price'] == "N/A") {
						throw new ApiGenericException('Garage is currently closed.');
					}
					$parkingAmount = $rate['price'];
					$rateId = isset($rate['id']) ? $rate['id'] : '';
					$rateDescription = isset($rate['description']) ? $rate['description'] : '';

					$processingFee = $ticket->getProcessingFee($rate, '0');   // to get prcessing free channel wise need to
					$taxRate = $ticket->getTaxRate($rate);          // to get tax price

					if ($rate) {

						$priceBreakUp = $ticket->priceBreakUp($rate);
						$this->log->info("priceBreakUp " . json_encode($priceBreakUp));

						if ($priceBreakUp['payable_amount'] > 0) {

							if ($facility->is_cloud_payment_enabled == '1') {
								if ($ticket->session_id == '') {
									$result['price'] =  $priceBreakUp['payable_amount'];
									$result['eticket_id'] =  $ticket->ticket_number;
									// $ticket->paid_amount = $updatedPaidAmount;
									// $ticket->parking_amount = $parkingAmount;
									// $ticket->rate_id = $rateId;
									// $ticket->rate_description = $rateDescription;
									// $ticket->save();
									QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
									$this->log->info("price sent, response sent");
									return $result;
								}

								if (!isset($facility->FacilityPaymentDetails->id)) {
									throw new ApiGenericException('Payment details not defined against this garage.');
								}
								$refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $priceBreakUp['payable_amount'], $request);
								$this->log->info("Payment Response :" . json_encode($refundstatus));
								if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
									//throw new ApiGenericException("Payment not done. Please try again");
									$ticket->is_transaction_status = '0';
									$ticket->save();
									$msg = "Amount Due, Please use different card.";
									$data = ['msg' => $msg];
									$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
									$data['eticket_id'] = $ticket->ticket_number;
									$data['amount'] = $priceBreakUp['payable_amount'];
									$data['response_type'] = '3';
									$data['license_plate'] = $request->license_plate;
									$this->log->info("Error in payment, response sent" . json_encode($data));
									return response()->json($data, 203);
								}

								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								$planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
								$ticket->anet_transaction_id = $planetTransaction->id;

								$ticket->processing_fee  = $priceBreakUp['processing_fee'];
								$ticket->tax_fee        = $priceBreakUp['tax_rate'];
								$ticket->parking_amount  = $priceBreakUp['parking_amount'];
								$ticket->paid_amount     = $priceBreakUp['paid_amount'];
								$ticket->discount_amount = $priceBreakUp['discount_amount'];
								$ticket->grand_total     = $priceBreakUp['payable_amount'];
								$ticket->total     = $priceBreakUp['total'];
								$ticket->length     = $diff_in_hours;

								$ticket->checkout_mode = '3';
								$ticket->is_checkout = '1';
								$ticket->checkout_gate = $request->gate_id;
								$ticket->checkout_time = date("Y-m-d H:i:s");
								$ticket->checkout_datetime = date("Y-m-d H:i:s");
								$ticket->is_cloud_payment = '1';
								$ticket->checkout_license_plate = $request->license_plate;
								$ticket->rate_id = $rateId;
								$ticket->rate_description = $rateDescription;
								$ticket->is_transaction_status = '0';
								$ticket->save();

								if ($request->license_plate != '') {
									if ($facility->license_plate_model != '') {
										$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
										$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
									}
								}

								//$this->saveTransactionData($rate, $ticket);

								$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
								$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

								$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
								if ($partnerDetails->user_id == self::PARTNER_ID) {
									$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
										$join->on('user_facilities.user_id', '=', 'users.id');
										$join->where('user_facilities.facility_id', "=", $facility->id);
									})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
									$name = ($getRM->slug) ? $getRM->slug : 'receipt';
								} else {
									$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
								}
								$url = env('RECEIPT_URL');
								$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
								//changes for breeze specific URL from DB
								if ($dynamicReceiptUrl) {
									$url = $dynamicReceiptUrl->value;
								}
								$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

								dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
								$this->log->info("SMS sent, response sent");
								$data = ['msg' => $msg];
								$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
								$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
								$data["eticket_id"] = $ticket->ticket_number;
								$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
								$this->log->info("User checkout, response sent");
								return $data;
							}


							$result['price'] =  $priceBreakUp['payable_amount'];
							$result['eticket_id'] =  $ticket->ticket_number;
							QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
							// $ticket->processing_fee  = $priceBreakUp['processing_fee'];
							// $ticket->tax_fee        = $priceBreakUp['tax_rate'];
							// $ticket->parking_amount  = $priceBreakUp['parking_amount'];
							// $ticket->paid_amount     = $priceBreakUp['paid_amount'];
							// $ticket->discount_amount = $priceBreakUp['discount_amount'];
							// $ticket->grand_total     = $priceBreakUp['payable_amount'];
							// $ticket->total     = $priceBreakUp['total'];
							// $ticket->length     = $diff_in_hours;
							// $ticket->rate_id = $rateId;
							// $ticket->rate_description = $rateDescription;
							$ticket->save();
							$this->log->info("price sent, response sent");
							return $result;
						}


						if ($ticket->is_offline_payment == '0' && $ticket->paid_type == '9') {
							if ($priceBreakUp['total'] > 0) {
								$result['price'] =  $priceBreakUp['total'];
								$result['eticket_id'] =  $ticket->ticket_number;
								QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
								// $ticket->processing_fee  = $priceBreakUp['processing_fee'];
								// $ticket->tax_fee        = $priceBreakUp['tax_rate'];
								// $ticket->parking_amount  = $priceBreakUp['parking_amount'];
								// $ticket->paid_amount     = $priceBreakUp['paid_amount'];
								// $ticket->discount_amount = $priceBreakUp['discount_amount'];
								// $ticket->grand_total     = $priceBreakUp['total'];
								// $ticket->total     = $priceBreakUp['total'];
								// $ticket->length     = $diff_in_hours;
								// $ticket->rate_id = $rateId;
								// $ticket->rate_description = $rateDescription;
								// $ticket->save();
								$this->log->info("if payment declined and hit api again, price sent, response sent");
								return $result;
							}
						}

						$this->log->info("before checkout");
						$ticket->processing_fee  = $priceBreakUp['processing_fee'];
						$ticket->tax_fee        = $priceBreakUp['tax_rate'];
						$ticket->parking_amount  = $priceBreakUp['parking_amount'];
						$ticket->paid_amount     = $priceBreakUp['paid_amount'];
						$ticket->discount_amount = $priceBreakUp['discount_amount'];
						$ticket->grand_total     = $priceBreakUp['payable_amount'];
						$ticket->total     = $priceBreakUp['total'];
						$ticket->length     = $diff_in_hours;

						$ticket->is_checkout = '1';
						$ticket->checkout_gate = $request->gate_id;
						$ticket->checkout_time = date("Y-m-d H:i:s");
						$ticket->checkout_datetime = date("Y-m-d H:i:s");
						$ticket->rate_id = $rateId;
						$ticket->rate_description = $rateDescription;
						$ticket->checkout_mode = '3';
						$ticket->is_transaction_status = '0';
						$this->log->info(json_encode($ticket));
						$ticket->save();

						$this->saveTransactionData($rate, $ticket);

						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}


						$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
						$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						if ($partnerDetails->user_id == self::PARTNER_ID) {
							$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
								$join->on('user_facilities.user_id', '=', 'users.id');
								$join->where('user_facilities.facility_id', "=", $facility->id);
							})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
							$name = ($getRM->slug) ? $getRM->slug : 'receipt';
						} else {
							$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						}
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
						$this->log->info("SMS sent, response sent");
						$data = ['msg' => $msg];
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						$data["eticket_id"] = $ticket->ticket_number;
						$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
						$this->log->info("User checkout, response sent");
						return $data;
					} else {
						throw new ApiGenericException("Rate not found.");
					}
					//$result['price'] =  $rate['price']  + $facility->processing_fee;
					$result['price'] =  $rate['price'];
					$this->log->info("price sent, response sent");
					return $result;
				}
			}
			if ($ticket->anet_transaction_id != '') {
				$this->log->info("System Not available error test - 17 -");
				$checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
				$checkoutTime = $ticket->estimated_checkout;
				$parkingNowTime = date("Y-m-d H:i:s");

				if (strtotime($parkingNowTime) > strtotime($checkoutTime)) {
					if ($ticket->payment_date != '') {
						$parkingStartTime = $ticket->payment_date;
					} else {
						$parkingStartTime = $ticket->estimated_checkout;
					}

					$overstayTicketExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "ASC")->first();
					if ($overstayTicketExist) {
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayTicketExist->payment_date);
						$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayTicketExist->check_in_datetime);
					} else {
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $parkingStartTime);
						$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $parkingStartTime);
					}
					/** this function is used to get Availability Information for respective facility **/
					$processingFee = 0;
					$taxRate = 0;

					if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
						$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
						$rate['description'] = self::HOURLY_RATE;
					} else {
						$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, $isMember);
						if (!isset($rate['description'])) {
							$rate['description'] = self::BASE_RATE;
						}
					}
					$taxRate = $ticket->getTaxRate($rate);          // to get tax price
					//$data['msg'] = "Amount Due";
					$data['price'] =  $rate['price'] + $taxRate;
					$data['eticket_id'] =  $ticket->ticket_number;
					$data['is_overstay'] =  '1';
					return $data;
				}
			}

			if ($facility->check_vehicle_enabled == '1') {
				$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$diff_in_hours = $arrival_time->diffInRealHours($from);
			if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
				$diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
			}
			$ticket->checkout_gate = $request->gate_id;
			$ticket->is_checkout = 1;
			$ticket->length = $diff_in_hours;
			$ticket->checkout_time = date('Y-m-d H:i:s');
			if (isset($request->payment_details)) {
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = $request->payment_details['processorReference'];
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
			}

			$result = $ticket->save();
			$this->log->info("checkout VP6800 done --" . json_encode($ticket));
			if ($result) {
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				//return "Thank you for visiting ".$facilityName.".";
				$msg =  "THANK YOU FOR VISITING. #$ticket->ticket_number";

				$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
				$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
				$url = env('RECEIPT_URL');
				$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
				//changes for breeze specific URL from DB
				if ($dynamicReceiptUrl) {
					$url = $dynamicReceiptUrl->value;
				}
				$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

				dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));

				$data = ['msg' => $msg];
				$this->log->info("SMS sent, response sent");
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data["eticket_id"] = $ticket->ticket_number;
				$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
				return $data;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}

		/*}catch(\Exception $e){
			$this->log->error("error received --".$e->getMessage());  
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}*/
	}


	public function ticketDriveupCheckinCheckout(Request $request)
	{
		$this->log->info("Driveup Request received --" . json_encode($request->all()));
		$this->setCustomTimezone($request->facility_id);
		$facility = Facility::with('FacilityPaymentDetails.facilityPaymentType')->find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}

		$paymentGateway = '';
		if (isset($facility->FacilityPaymentDetails->facilityPaymentType->id)) {
			$paymentGateway = $facility->FacilityPaymentDetails->facilityPaymentType->payment_type;
		}

		$facilityName =  ucwords($facility->full_name);
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		$this->log->info("Driveup Request received --11");

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}

		if ($request->session_id == '') {
			throw new ApiGenericException('Card info not found. Please try again.');
		}

		$this->log->info("Driveup Request received --13");
		// VP:PIMS-14662
		$eticket = $this->im30Service->checkTempEticket($request);
		if ($eticket && $facility->is_gated_facility == '2') {
			$userSessionExist = UserSession::where("session_id", $request->session_id)->where('partner_id', $facility->owner_id)->orderBy("id", "DESC")->first();
			if ($userSessionExist) {
				$user = User::where('id', $userSessionExist->user_id)->first();
			} else {
				$user = User::create(
					[
						'name' => '',
						'email' => '',
						'phone' => '',
						'password' => Hash::make(str_random(60)),
						'anon' => true,
						'user_type' => '5',
						'created_by' => $facility->owner_id,
						'session_id' => $request->session_id,
					]
				);
			}


			$this->log->info("Start Hybrid point 3.1");
			// if Temp Ticket found Create New PE Tickets 
			$entryFeed 	= LPRFeed::find($this->im30Service->getLPRFeedId($request));
			$exitFeed 	= LPRFeed::Where(['license_plate' => $request->license_plate])->orderBy('id', 'desc')->first();
			// $hyTicket 	= Ticket::where('session_id', $request->session_id)->where('facility_id', $request->facility_id)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
			$this->log->info("Start Hybrid point 4.1");
			if ($entryFeed) {
				$this->log->info("Start Hybrid point 5.1");
				$request->merge(
					[
						'user_id' => $user->id,
						'checkin_time' => $this->im30Service->getUTCToFacilityTimezone($entryFeed->event_timestamp, $facility),
						'estimated_checkout' => $this->im30Service->getUTCToFacilityTimezone($exitFeed->event_timestamp, $facility)
					]
				);

				// Calculate price again 
				$amountDue = $this->im30Service->getAmountDue($request, $facility);
				$rate['price'] 		= $amountDue['price'];
				$this->log->info("Start Hybrid point 4.1 Amount due " . $rate['price']);

				// Now calculate Tax and Fees 
				$parkingAmount      = $rate['price'];
				$processingFee      = $facility->getProcessingFee('0');       // pass 1 to get the processing fee // pass 1 to get the processing fee
				$surchargeFee       = $facility->getSurchargeFee($rate);
				$newRate['price']   = ($rate['price'] + $surchargeFee);
				$tax_rate           = $facility->getTaxRate($newRate, '0');      // fetch tax from getTicketRate dinamicaly
				$additionalFee      = $facility->getAdditionalFee($rate);     // Addition Fee Introduce
				$payableAmount      = ($parkingAmount + $processingFee + $surchargeFee + $tax_rate + $additionalFee);

				// Add this in Request
				$request->merge(
					[
						'total' 			=> $payableAmount,
						'grand_total' 		=> $payableAmount,
						'parking_amount' 	=> $parkingAmount,
						'tax_fee' 			=> $tax_rate,
						'processing_fee' 	=> $processingFee,
						'additional_fee' 	=> $additionalFee,
						'surcharge_fee' 	=> $surchargeFee,
						'rate_id'			=> isset($amountDue['rate_id']) ? $amountDue['rate_id'] : 0,
						'rate_description'	=> isset($amountDue['rate_description']) ? $amountDue['rate_description'] : 'base_price'
					]
				);

				// Save Ticket
				if ($request->is_overstay == '1') {
					$ticket = Ticket::where(['license_plate' => $request->license_plate, 'is_checkout' => '1'])->where('facility_id', $request->facility_id)->orderBy("id", "DESC")->first();
					if ($ticket) {
						$request->merge(
							[
								'ticket_id' 		=> $ticket->id,
								'ticket_number' 	=> $ticket->ticket_number
							]
						);
					}
					$ticket = $this->im30Service->createOverstay($request, $facility);
				} else {
					$ticket = $this->im30Service->createTicket($request, $facility);
				}
				// Save Transation 
				if (isset($request->payment_details)) {
					$payments = $this->ticketHelper->savePaymentTransaction($request, $facility, $ticket);
					$ticket->is_checkout = '1';
					$ticket->save();
					$this->log->info("Start Hybrid point 6");

					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}
					//return "Thank you for visiting ".$facilityName.".";
					$msg =  "THANK YOU FOR VISITING. #$ticket->ticket_number";

					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);

					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

					// dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
					$paidAmount 				= $payments->total;
					$data = ['msg' => $msg];
					$this->log->info("SMS sent, response sent hybrid ");
					$data['is_phone_linked'] 	= $user->phone != '' ? '1' : '0';
					$data['phone_linked_msg']	= $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					$data["eticket_id"] 		= $ticket->ticket_number;
					$data['booking_type'] 		= "driveup";
					$data['ticket_id'] 			= $ticket->ticket_number;
					$data['amount'] 			= $paidAmount == '' ? "0.00" : number_format($paidAmount, 2);
					$data['checkin_time'] 		= date("g:i A", strtotime($ticket->checkin_time));
					$data["print_receipt"] 		= QueryBuilder::setPrintReceipt($ticket, '0');
					$this->log->info("Response data " . json_encode($data));
					return $data;
				}

				$this->log->info("Start Hybrid point 7");

				// QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
				$hybrid['price'] 			=  sprintf("%.2f", ($payableAmount));
				$hybrid['eticket_id'] 		=  $request->eticket_id;
				$hybrid['payment_token'] 	=  $request->session_id;
				$this->log->info("Data returen : " . json_encode($hybrid));
				$this->log->info("price sent, response sent");
				return $hybrid;
			} else {
				$this->log->info("Start Hybrid Invalid Request");
				// Retrun Invalid Request
				throw new ApiGenericException('No Checkin found for this license plate');
			}
		} else {
			// Here We need to handle Overstay flow 
			$this->log->info("Start Hybrid Invalida TEMP ID ");
			throw new ApiGenericException('Invalid Request');
		}
		$this->log->info("Start Hybrid point 2 " . json_encode($eticket));
		// VP:PIMS-14662   
		// $eticket == false   this will handle all old case. 

		if ($request->eticket_id != '' && $eticket == false) {
			$ticket = Ticket::with('user')->where('ticket_number', $request->eticket_id)->where('is_checkout', '0')->first();
			if (!$ticket) {
				throw new ApiGenericException('Invalid ticket details.');
			}
			$user = $ticket->user;
		} else {
			$userSessionExist = UserSession::where("session_id", $request->session_id)->orWhere('customer_data', $request->CustomerData)->where('partner_id', $facility->owner_id)->orderBy("id", "DESC")->first();
			if ($userSessionExist) {
				$user = User::where('id', $userSessionExist->user_id)->first();
			} else {
				throw new ApiGenericException('Invalid user.');
			}
			//$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
			if (!$user) {
				$user = User::create(
					[
						'name' => '',
						'email' => '',
						'phone' => '',
						'password' => Hash::make(str_random(60)),
						'anon' => true,
						'user_type' => '5',
						'created_by' => $facility->owner_id,
						'session_id' => $request->session_id,
					]
				);
			}
		}

		$isMember = 0;

		if (isset($gate) && $gate->gate_type == "entry") {

			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				$this->saveAnetTransaction($request);
				throw new ApiGenericException('You have already checked-in.');
			}
			$today = date("Y-m-d");
			//$event = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
			$facility_id = $facility->id;
			/**
			 * #DD
			 * pims-13537
			 * making changes for event duration dynamic
			 */
			/*
			$sqlQuery = "SELECT e.*
						FROM events as e
						inner join event_facility as ef on ef.event_id= e.id        
						WHERE e.partner_id IN ({$facility->owner_id}) AND date(e.start_time) = '" . $today . "' AND date(e.end_time) >= '" . $today . "' AND e.deleted_at is null AND ef.facility_id IN (" . $facility_id . ") AND e.is_active='1' AND e.deleted_at is NULL";
			*/

			$sqlQuery = "SELECT e.*
                FROM events as e
                inner join event_facility as ef on ef.event_id= e.id        
                WHERE e.partner_id IN ({$facility->owner_id}) AND date(e.parking_start_time) = '" . $today . "' AND date(e.parking_end_time) >= '" . $today . "' AND e.deleted_at is null AND ef.facility_id IN (" . $facility_id . ") AND e.is_active='1' AND e.deleted_at is NULL";

			$results =  DB::select($sqlQuery);
			if (count($results) > 0) {
				$todayEvent = new Fluent($results);
				$event = $todayEvent[0];
				if (!$event) {
					$rate['price'] = $facility->base_rate;
					$taxRate = $facility->getTaxRate($rate);          // to get tax price                
					$driveupRate = $facility->base_rate + $facility->processing_fee + $taxRate;
					$processingFee = $facility->processing_fee;
					$taxFee = $taxRate;
					$data['length'] = 24;
				} else {
					$parkingNowTime = date("Y-m-d H:i:s");
					$parkingStartTime = $event->parking_start_time;
					$parkingEndTime = $event->parking_end_time;
					$eventStartTime = $event->start_time;
					$eventEndTime = $event->end_time;

					if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
						$driveupRate = 0;
						if ($event->driveup_event_rate != '' && $event->driveup_event_rate > 0) {

							$whitelistUser = WhitelistUserCard::where("expiry", $request->expiry)
								->where("card_last_four", substr($request->MaskedPAN, -4))
								//where("session_id", $request->session_id)
								//where("card_type", $request->CardType)
								//->where("facility_id", $request->facility_id)
								->where("partner_id", $facility->owner_id)
								->first();
							if (isset($whitelistUser->id)) {
								$this->log->info("driviup whitelist event price enterd");
								$rate['price'] = $facility->base_rate;
								$taxRate = $facility->getTaxRate($rate);          // to get tax price                
								$driveupRate = $facility->base_rate + $facility->processing_fee + $taxRate;
								$processingFee = $facility->processing_fee;
								$taxFee = $taxRate;
							} else {
								$rate['price'] = $event->driveup_event_rate;
								$taxRate = $facility->getTaxRate($rate);          // to get tax price                
								$driveupRate = $event->driveup_event_rate + $facility->drive_up_processing_fee + $taxRate;
								$processingFee = $facility->drive_up_processing_fee;
								$taxFee = $taxRate;
							}
						} else {
							$rate['price'] = $facility->base_rate;
							$taxRate = $facility->getTaxRate($rate);          // to get tax price                
							$driveupRate = $facility->base_rate + $facility->processing_fee + $taxRate;
							$processingFee = $facility->processing_fee;
							$taxFee = $taxRate;
						}
					}
				}
			} else {
				$rate['price'] = $facility->base_rate;
				$taxRate = $facility->getTaxRate($rate);          // to get tax price                
				$driveupRate = $facility->base_rate + $facility->processing_fee + $taxRate;
				$processingFee = $facility->processing_fee;
				$taxFee = $taxRate;
				$data['length'] = 24;
			}

			$this->log->info("Driveup Request received --14");

			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
			$data['checkin_time'] = date('Y-m-d H:i:s');
			if ($event) {
				$data['check_in_datetime'] = $event->start_time;
				/***
				 * #DD
				 * pims-13537
				 */
				if ($event->base_event_hours > 0) {
					$startTime = Carbon::parse('now');
					$data['check_in_datetime'] = $startTime;
					$baseHours = explode('.', $event->base_event_hours);
					if (count($baseHours) > 1) {
						$minutes = ($baseHours[0] * 60) + $baseHours[1];
						$endTime = $startTime->copy()->addMinutes($minutes);
					} else {
						$endTime = $startTime->copy()->addHours($event->base_event_hours);
					}
					$from = Carbon::createFromFormat('Y-m-d H:i:s', $endTime);
					$maxStay = $diff_in_hours = $event->base_event_hours;
					$data['estimated_checkout'] = $data['checkout_datetime'] = $data['payment_date'] = $from;
				} else {
					$data['estimated_checkout'] = $event->parking_end_time;
					$data['checkout_datetime'] = $event->parking_end_time;
					$data['payment_date'] = $event->parking_end_time;
					//condition for colonial overstay
					if ($facility->owner_id == 363362 || $facility->owner_id == 26380) {
						$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime('+24 hours'));
						$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime('+24 hours'));
						//$data['checkout_time'] = date("Y-m-d H:i:s", strtotime('+24 hours'));
						$data['payment_date'] = date("Y-m-d H:i:s", strtotime('+24 hours'));
					}
				}

				//$data['checkout_time'] = $event->end_time;
				$data['event_id'] = $event->id;
			} else {
				$ticketStartDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
				$ticketEndDate = $ticketStartDate->addHours(24);
				$data['check_in_datetime'] = date('Y-m-d H:i:s');
				$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($ticketEndDate));
				$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($ticketEndDate));
				//$data['checkout_time'] = date("Y-m-d H:i:s", strtotime($ticketEndDate));
			}
			$this->log->info("Driveup Request received --16");
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['vp_device_checkin'] = '1';
			$data['partner_id'] = $facility->owner_id;
			$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
			$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
			$this->log->info("Driveup Request received --17");
			if (isset($request->CardType)) {
				$card_type = '';
				if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
					$card_type = 'VISA';
				} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
					$card_type = 'MASTERCARD';
				} else if (strtolower($request->CardType) == "jcb") {
					$card_type = 'JCB';
				} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
					$card_type = 'AMEX';
				} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
					$card_type = 'DISCOVER';
				} else {
					$card_type = $request->CardType;
				}
				$data['card_type'] = $card_type;
			}
			$this->log->info("Driveup Request received --18");
			/*$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $data['checkin_time']);
			if($data['checkin_time'] != ''){
				$endDate = Carbon::createFromFormat('Y-m-d H:i:s', $data['checkout_time']);
			}else{
				$endDate = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
			}*/
			$this->log->info("Driveup Request received --19");
			$data['device_type'] = "IM30";
			$data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
			$data['payment_gateway'] = $paymentGateway;
			$data['session_id'] = $request->session_id;
			$data['payment_token'] = $request->payment_token;
			$result = Ticket::create($data);
			$paidAmount = '';
			if (isset($request->payment_details)) {
				$ticket = Ticket::where('id', $result->id)->first();
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
				$authorized_anet_transaction->status_code = isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
				$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
				$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
				$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();
				$ticket->anet_transaction_id = $authorized_anet_transaction->id;
				$ticket->total = $request->payment_details['TransactionAmount'];
				$ticket->grand_total = $request->payment_details['TransactionAmount'];
				$ticket->terminal_id = $request->payment_details['TerminalID'];
				$ticket->payment_date = date("Y-m-d H:i:s");
				$ticket->tax_fee = $taxFee;
				$ticket->processing_fee = $processingFee;
				$ticket->parking_amount = $request->payment_details['TransactionAmount'] - ($processingFee + $taxFee);

				$ticket->card_type = $request->payment_details['CardType'];
				$ticket->card_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$ticket->expiry = $request->payment_details['expiry'];
				$ticket->customer_data = isset($request->payment_details['CustomerData']) ? $request->payment_details['CustomerData'] : NULL;
				$ticket->save();
				$paidAmount = $request->payment_details['TransactionAmount'];
			}
			$this->log->info("driveup checkin done --" . json_encode($result));

			if ($result) {

				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				if ($paidAmount == '') {
					$msg =  "WELCOME. #$result->ticket_number";
				} else {
					$msg =  "WELCOME CHARGED: $$paidAmount. #$result->ticket_number";
				}
				$data['msg'] = $msg;
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data['booking_type'] = "driveup";
				$data['ticket_id'] = $result->ticket_number;
				$data['amount'] = $paidAmount == '' ? "0.00" : number_format($paidAmount, 2);
				$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
				$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
				if ($ticket->checkout_time != '') {
					$endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_time);
				} else {
					$endDate = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
				}


				$length_of_stay = '';
				$diff_in_days = $startDate->diffInDays($endDate);
				$diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
				$diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);
				if ($diff_in_days > 0) {
					if ($diff_in_days == 1) {
						$length_of_stay .= $diff_in_days . " day ";
					} else {
						$length_of_stay .= $diff_in_days . " days ";
					}
				}
				if ($diff_in_hours > 0) {
					$length_of_stay .= $diff_in_hours . " hr ";
				}

				if ($diff_in_minutes > 0) {
					$length_of_stay .= $diff_in_minutes . " min";
				}
				if ($ticket->length == '') {
					$ticket->length = $diff_in_hours;
					$ticket->save();
				}

				$data['length_of_stay'] = $length_of_stay;
				////Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
				if ($user->phone != '') {
					//$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Welcome to " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $result->ticket_number;
					$this->customeReplySms($sms_msg, $user->phone);
				}

				$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
				return $data;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}

		if (isset($gate) && $gate->gate_type == "exit") {

			if ($request->eticket_id != '') {
				$this->log->info("Driveup Eticket scenario");
				$ticket = Ticket::with(['reservation', 'user'])->where('ticket_number', $request->eticket_id)->where('is_checkout', '0')->first();

				if (!$ticket) {
					throw new ApiGenericException("'NO TICKET FOUND' USE THE SAME CARD USED AT ENTRY");
				}

				$checkinDate = date("Y-m-d", strtotime($ticket->checkin_time));

				$updatedCheckinTime = '';
				if (strtotime(date("Y-m-d")) != strtotime($checkinDate)) {
					$updatedCheckinTime = date("d M", strtotime($checkinDate)) . ' ' . date("g:i A", strtotime($ticket->checkin_time));
				} else {
					$updatedCheckinTime = date("g:i A", strtotime($ticket->checkin_time));
				}

				$ticketNumber = $ticket->ticket_number;

				if ($facility->check_vehicle_enabled == '1') {
					$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}

				$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
				$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
				$diff_in_hours = $arrival_time->diffInRealHours($from);
				$diff_in_mins = $arrival_time->diffInRealMinutes($from);
				$diff_in_secs = $arrival_time->diffInSeconds($from);

				$diff_in_hours = $ticket->getCheckOutCurrentTime(true);

				if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
					$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
					$rate['description'] = self::HOURLY_RATE;
				} else {
					$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
					if (!isset($rate['description'])) {
						$rate['description'] = self::BASE_RATE;
					}
				}

				$ticket->checkout_gate = $request->gate_id;
				$ticket->is_checkout = 1;
				$ticket->length = $diff_in_hours;
				$ticket->checkout_time = date('Y-m-d H:i:s');
				$paidAmount = '';
				if (isset($request->payment_details)) {
					$this->log->info("payment details request --" . json_encode($request->payment_details));
					$authorized_anet_transaction = new AuthorizeNetTransaction();
					$authorized_anet_transaction->sent = '1';
					$authorized_anet_transaction->user_id = $ticket->user_id;
					$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
					$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
					$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
					$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
					$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
					$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
					$authorized_anet_transaction->ref_id = isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
					$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
					$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
					$authorized_anet_transaction->method = "card";
					$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
					$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
					$authorized_anet_transaction->status_code = isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
					$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
					$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
					$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
					$authorized_anet_transaction->save();
					$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();


					if ($request->is_overstay == '1') {

						$overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();
						if ($overstayExist) {
							$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->payment_date);
							$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->payment_date);
						} else {
							$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout);
							$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout);
						}

						$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));

						if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
							$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
							$rate['description'] = self::HOURLY_RATE;
						} else {
							$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
							if (!isset($rate['description'])) {
								$rate['description'] = self::BASE_RATE;
							}
						}
						$rate_id = isset($rate['id']) ? $rate['id'] : '';
						$rate_description = isset($rate['description']) ? $rate['description'] : '';

						$currentTime = Carbon::parse('now');
						$estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
						$overstay = new OverstayTicket();
						$overstay->user_id = $ticket->user_id;
						$overstay->facility_id = $ticket->facility_id;
						$overstay->total = $request->payment_details['TransactionAmount'];
						$overstay->grand_total = $request->payment_details['TransactionAmount'];
						$overstay->ticket_number = $ticket->ticket_number;
						$overstay->is_checkin = '1';
						$overstay->is_checkout = '1';
						$overstay->check_in_datetime = $ticket->estimated_checkout;
						$overstay->checkout_datetime = $estimated_checkout;
						$overstay->estimated_checkout = $estimated_checkout;
						$overstay->partner_id = $ticket->partner_id;
						$overstay->ticket_id = $ticket->id;
						$overstay->anet_transaction_id = $authorized_anet_transaction->id;
						$overstay->length = $diff_in_hours;
						$overstay->payment_date = date("Y-m-d H:i:s");
						$overstay->rate_id = $rate_id;
						$overstay->rate_description = $rate_description;
						$overstay->reservation_id = $ticket->reservation_id;
						$overstay->tax_fee = $ticket->getTaxRate($rate);          // to get tax price
						$overstay->save();

						//$ticket->grand_total = $ticket->grand_total;
						$ticket->is_checkout = '1';
						$ticket->is_overstay = '1';
						//$ticket->estimated_checkout = $estimated_checkout;

						$ticket->save();

						$this->saveOverstayTransactionData($ticket, $overstay, $facility);
					} else {

						$rate_id = isset($rate['id']) ? $rate['id'] : '';
						$rate_description = isset($rate['description']) ? $rate['description'] : '';
						$ticket->rate_id = $rate_id;
						$ticket->rate_description = $rate_description;

						$ticket->estimated_checkout = date('Y-m-d H:i:s');
						$ticket->checkout_cardholder_name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
						$ticket->checkout_session_id = $request->session_id;
						$ticket->checkout_card_last_four = substr($request->payment_details['MaskedPAN'], -4);
						$ticket->checkout_expiry = $request->payment_details['expiry'];
						$ticket->checkout_card_type = $request->payment_details['CardType'];
						$ticket->is_transaction_status = '0';

						$processingFee = $ticket->getProcessingFee('0');   // to get prcessing free channel wise need to
						$taxRate = $ticket->getTaxRate($rate);          // to get tax price
						$ticket->processing_fee = $processingFee;
						$ticket->tax_fee = $taxRate;

						$ticket->anet_transaction_id = $authorized_anet_transaction->id;
						//$ticket->total = $request->payment_details['TransactionAmount'];
						//$ticket->grand_total = $request->payment_details['TransactionAmount'];
						$ticket->terminal_id = $request->payment_details['TerminalID'];
						$paidAmount =  $request->payment_details['TransactionAmount'];
						//$ticket->total = $rate['price'] + $processingFee + $taxRate;

						$tempTicket = QueryBuilder::getTicketTemporaryDetails($ticket);
						if ($tempTicket) {
							$ticket->processing_fee  = $tempTicket->processing_fee;
							$ticket->tax_fee        = $tempTicket->tax_fee;
							$ticket->parking_amount  = $tempTicket->parking_amount;
							$ticket->paid_amount     = $tempTicket->paid_amount;
							$ticket->discount_amount = $tempTicket->discount_amount;
							$ticket->grand_total     = $tempTicket->grand_total;
							$ticket->total     = $tempTicket->total;
							$ticket->length     = $tempTicket->length;
						}
					}
				}

				if ($ticket->vp_device_checkin == '1') {
					$ticket->checkout_datetime = date('Y-m-d H:i:s');
				}
				$ticket->checkout_license_plate = $request->license_plate;
				$ticket->checkout_mode = '3';
				$ticket->payment_token = $request->payment_token;

				$result = $ticket->save();

				$this->saveTransactionData($rate, $ticket);

				if (isset($ticket->reservation->id)) {
					$ticket->reservation->is_ticket = '2';
					$ticket->reservation->save();
				}

				if ($request->license_plate != '') {
					if ($facility->license_plate_model != '') {
						$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
						$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
					}
				}

				//$this->saveTransactionData($rate, $ticket);
				$this->log->info("eticket checkout VP6800 done --" . json_encode($ticket));
				if ($result) {
					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					if ($request->is_overstay == '1') {
						$response = ["msg" => "Thank you for visiting " . $facilityName . ". Extra $" . $request->payment_details['TransactionAmount'] . " is charged for overstay."];
					} else {

						if ($paidAmount == '') {
							$response = ["msg" => "THANK YOU FOR VISITING. #" . $ticketNumber];
						} else {
							$response = ["msg" => "THANK YOU FOR VISITING CHARGED: $" . number_format($paidAmount, 2) . " #" . $ticketNumber];
						}
					}

					if ($ticket->promocode != '') {
						$promocodeRequest = PromocodeRequest::where('promocode', $ticket->promocode)->orderBy("id", "desc")->first();
						if ($promocodeRequest) {
							$promotion = Promotion::find($promocodeRequest->promotion_id);
							if ($promotion) {
								$response = ["msg" => "THANK YOU FOR VISITING CHARGED: $" . number_format($paidAmount, 2) . ", " . $promotion->name . " applied #" . $ticketNumber];
							}
							$promocodeRequest->delete();
						}
					}

					$response['booking_type'] = "driveup";
					$response['ticket_id'] = $ticketNumber;
					$response['amount'] = $paidAmount == '' ? "0.00" : number_format($paidAmount, 2);
					$response['checkin_time'] = $updatedCheckinTime;
					$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
					$endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_time);

					$length_of_stay = '';
					$diff_in_days = $startDate->diffInDays($endDate);
					$diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
					$diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);

					if ($diff_in_days > 0) {
						if ($diff_in_days == 1) {
							$length_of_stay .= $diff_in_days . " day ";
						} else {
							$length_of_stay .= $diff_in_days . " days ";
						}
					}
					if ($diff_in_hours > 0) {
						$length_of_stay .= $diff_in_hours . " hr ";
					}
					if ($diff_in_minutes <= 0) {
						$length_of_stay .= "0 min";
					}
					if ($diff_in_minutes > 0) {
						$length_of_stay .= $diff_in_minutes . " min";
					}
					$response['length_of_stay'] = $length_of_stay;


					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					if ($partnerDetails->user_id == self::PARTNER_ID) {
						$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
							$join->on('user_facilities.user_id', '=', 'users.id');
							$join->where('user_facilities.facility_id', "=", $facility->id);
						})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
						$name = ($getRM->slug) ? $getRM->slug : 'receipt';
					} else {
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					}
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticketNumber;

					if (isset($user->phone)) {
						$response['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$response['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
					}
					$response["eticket_id"] = $ticket->ticket_number;
					$response["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
					$this->log->info("SMS sent, driveup response sent");
					return $response;
					/*$msg =  "Thank you for visiting ".$facilityName.".";
					$data = ['msg' => $msg];
					return $data;*/
				} else {
					throw new ApiGenericException('Something wrong.');
				}
			}
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if (!$ticket) {
				throw new ApiGenericException("'NO TICKET FOUND' USE THE SAME CARD USED AT ENTRY");
			}



			$ticketNumber = $ticket->ticket_number;

			if ($facility->check_vehicle_enabled == '1') {
				$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$diff_in_hours = $arrival_time->diffInRealHours($from);
			$diff_in_mins = $arrival_time->diffInRealMinutes($from);
			$diff_in_secs = $arrival_time->diffInSeconds($from);

			if ($diff_in_mins < 60) {
				$diff_in_hours = $diff_in_mins / 100;
			}
			if ($diff_in_mins > 59) {
				if ($diff_in_hours > 0) {
					$diffInMints = $diff_in_mins - ($diff_in_hours * 60);
					$diff_in_hours = $diff_in_hours . '.' . $diffInMints;
				} else {
					$diff_in_hours = number_format($diff_in_mins / 60, 2);
				}
			}
			if ($diff_in_secs < 60) {
				$diff_in_hours = .01;
			}

			if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
				$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
				$rate['description'] = self::HOURLY_RATE;
			} else {
				$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
				if (!isset($rate['description'])) {
					$rate['description'] = self::BASE_RATE;
				}
			}


			$ticket->checkout_gate = $request->gate_id;
			$ticket->is_checkout = 1;
			$ticket->length = $diff_in_hours;
			$ticket->checkout_time = date('Y-m-d H:i:s');
			$paidAmount = '';
			if (isset($request->payment_details)) {
				$this->log->info("payment details request --" . json_encode($request->payment_details));
				$authorized_anet_transaction = new AuthorizeNetTransaction();
				$authorized_anet_transaction->sent = '1';
				$authorized_anet_transaction->user_id = $ticket->user_id;
				$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
				$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
				$authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
				$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
				$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
				$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
				$authorized_anet_transaction->ref_id = isset($request->payment_details['RequesterTransRefNum']) ? $request->payment_details['RequesterTransRefNum'] : '';
				$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
				$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$authorized_anet_transaction->method = "card";
				$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
				$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
				$authorized_anet_transaction->status_code = isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
				$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
				$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
				$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
				$authorized_anet_transaction->save();
				$charge = AuthorizeNetTransaction::where('id', $authorized_anet_transaction->id)->first()->toArray();

				$processingFee = $ticket->getProcessingFee($rate, '0');   // to get prcessing free channel wise need to
				$taxRate = $ticket->getTaxRate($rate);          // to get tax price
				$ticket->processing_fee = $processingFee;
				$ticket->tax_fee = $taxRate;
				$ticket->checkout_cardholder_name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
				$ticket->checkout_session_id = $request->session_id;
				$ticket->checkout_card_last_four = substr($request->payment_details['MaskedPAN'], -4);
				$ticket->checkout_expiry = $request->payment_details['expiry'];
				$ticket->checkout_card_type = $request->payment_details['CardType'];
				$ticket->is_transaction_status = '0';
				if ($request->is_overstay == '1') {

					$checkinTime = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
					$checkoutTime = date("Y-m-d", strtotime($ticket->checkin_time)) . " 23:59:59";
					$parkingNowTime = date("Y-m-d H:i:s");
					$parkingStartTime = date("Y-m-d") . " 00:00:00";
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $parkingStartTime);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', $parkingNowTime);
					$diff_in_hours = $arrival_time->diffInRealHours($from);
					$diff_in_mins = $arrival_time->diffInRealMinutes($from);

					$diff_in_hours = number_format($diff_in_mins / 60, 2);


					$overstay = new OverstayTicket();
					$overstay->user_id = $ticket->user_id;
					$overstay->facility_id = $ticket->facility_id;
					$overstay->total = $request->payment_details['TransactionAmount'];
					$overstay->ticket_number = $ticket->ticket_number;
					$overstay->is_checkin = '1';
					$overstay->is_checkout = '1';
					$overstay->check_in_datetime = date('Y-m-d H:i:s', strtotime($parkingStartTime));
					$overstay->checkout_datetime = date('Y-m-d H:i:s');
					$overstay->partner_id = $ticket->partner_id;
					$overstay->ticket_id = $ticket->id;
					$overstay->anet_transaction_id = $authorized_anet_transaction->id;
					$overstay->length = $diff_in_hours;
					$overstay->save();

					$ticket->grand_total = $ticket->grand_total + $request->payment_details['TransactionAmount'];
					$ticket->is_checkout = '1';
					$ticket->is_overstay = '1';
					$ticket->save();

					$this->saveOverstayTransactionData($ticket, $overstay, $facility);
				} else {
					$ticket->anet_transaction_id = $authorized_anet_transaction->id;
					//$ticket->total = $request->payment_details['TransactionAmount'];
					$ticket->grand_total = $request->payment_details['TransactionAmount'];
					$ticket->terminal_id = $request->payment_details['TerminalID'];
					$paidAmount =  $request->payment_details['TransactionAmount'];
					$ticket->total = $rate['price'] + $processingFee + $taxRate;

					$this->saveTransactionData($rate, $ticket);

					$tempTicket = QueryBuilder::getTicketTemporaryDetails($ticket);
					if ($tempTicket) {
						$ticket->processing_fee  = $tempTicket->processing_fee;
						$ticket->tax_fee        = $tempTicket->tax_fee;
						$ticket->parking_amount  = $tempTicket->parking_amount;
						$ticket->paid_amount     = $tempTicket->paid_amount;
						$ticket->discount_amount = $tempTicket->discount_amount;
						$ticket->grand_total     = $tempTicket->grand_total;
						$ticket->total     = $tempTicket->total;
						$ticket->length     = $tempTicket->length;
					}
				}
			}

			if ($ticket->vp_device_checkin == '1') {
				$ticket->checkout_datetime = date('Y-m-d H:i:s');
			}
			$ticket->payment_date = date('Y-m-d H:i:s');
			$result = $ticket->save();

			//$this->saveTransactionData($rate, $ticket);
			$this->log->info("checkout VP6800 done --" . json_encode($ticket));
			if ($result) {
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}

				if ($request->is_overstay == '1') {
					$response = ["msg" => "Thank you for visiting " . $facilityName . ". Extra $" . $request->payment_details['TransactionAmount'] . " is charged for overstay."];
				} else {

					if ($paidAmount == '') {
						$response = ["msg" => "THANK YOU FOR VISITING. #" . $ticketNumber];
					} else {
						$response = ["msg" => "THANK YOU FOR VISITING CHARGED: $" . number_format($paidAmount, 2) . " #" . $ticketNumber];
					}
				}


				$response['booking_type'] = "driveup";
				$response['ticket_id'] = $ticketNumber;
				$response['amount'] = $paidAmount == '' ? "0.00" : number_format($paidAmount, 2);
				$response['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
				$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
				$endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_time);

				$length_of_stay = '';
				$diff_in_days = $startDate->diffInDays($endDate);
				$diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
				$diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);

				if ($diff_in_days > 0) {
					if ($diff_in_days == 1) {
						$length_of_stay .= $diff_in_days . " day ";
					} else {
						$length_of_stay .= $diff_in_days . " days ";
					}
				}
				if ($diff_in_hours > 0) {
					$length_of_stay .= $diff_in_hours . " hr ";
				}
				if ($diff_in_minutes <= 0) {
					$length_of_stay .= "0 min";
				}
				if ($diff_in_minutes > 0) {
					$length_of_stay .= $diff_in_minutes . " min";
				}
				$response['length_of_stay'] = $length_of_stay;


				$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
				$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
				$url = env('RECEIPT_URL');
				$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
				//changes for breeze specific URL from DB
				if ($dynamicReceiptUrl) {
					$url = $dynamicReceiptUrl->value;
				}
				$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticketNumber;

				if (isset($user->phone)) {
					$response['is_phone_linked'] = $user->phone != '' ? '1' : '0';
					$response['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				} else {
					$response['is_phone_linked'] = '0';
					$response['phone_linked_msg'] = '';
				}
				$this->log->info("SMS sent, driveup response sent");
				$response["eticket_id"] = $ticket->ticket_number;
				$response["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
				return $response;
				/*$msg =  "Thank you for visiting ".$facilityName.".";
					$data = ['msg' => $msg];
					return $data;*/
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
	}


	public function userCheckinByQrCode($user, $request, $facility)
	{

		if (isset($user->id)) {
			$ticket = Ticket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
			if ($ticket) {
				throw new ApiGenericException('You have already checked-in.');
			}
		}

		$paymentGateway = '';
		if (isset($facility->FacilityPaymentDetails->facilityPaymentType->id)) {
			$paymentGateway = $facility->FacilityPaymentDetails->facilityPaymentType->payment_type;
		}

		if ($facility->check_vehicle_enabled == '1') {
			$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}
		}

		$data['user_id'] = $user->id;
		$data['checkin_gate'] = $request->gate_id;
		$data['facility_id'] = $request->facility_id;
		$data['is_checkin'] = 1;
		$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
		$data['checkin_time'] = date('Y-m-d H:i:s');
		$data['check_in_datetime'] = date('Y-m-d H:i:s');
		$data['ticket_security_code'] = rand(1000, 9999);
		$data['vp_device_checkin'] = '1';
		$data['partner_id'] = $facility->owner_id;

		$data['license_plate'] = isset($request->license_plate) ? $request->license_plate : '';
		$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
		$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
		if (isset($request->CardType)) {
			$card_type = '';
			if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
				$card_type = 'VISA';
			} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
				$card_type = 'MASTERCARD';
			} else if (strtolower($request->CardType) == "jcb") {
				$card_type = 'JCB';
			} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
				$card_type = 'AMEX';
			} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
				$card_type = 'DISCOVER';
			} else {
				$card_type = $request->CardType;
			}
			$data['card_type'] = $card_type;
		}

		$data['device_type'] = "IM30";
		$data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
		$data['payment_gateway'] = $paymentGateway;
		$data['session_id'] = $request->session_id;

		$reservation = Reservation::with('user')->where("facility_id", $request->facility_id)->where("user_id", $user->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
		$userPass = [];
		if (!$reservation) {

			$userPass = UserPass::with('user')->where("facility_id", $request->facility_id)->where("user_id", $user->id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
			if (!$userPass) {

				//driveup checkin

				$result = Ticket::create($data);

				$this->log->info("worldport user based checkin done --" . json_encode($result));
				if ($result) {

					// send Notification 
					$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
					if ($deviceToken) {
						$updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::CHECKIN_NOTIFICATION];
						Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
					}

					$facilityName = ucwords($facility->full_name);
					try {
						Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Welcome to " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $result->ticket_number;
						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
					} catch (\Exception $e) {
						$this->log->error("System exception -" . $e->getMessage());
					}

					$this->log->info("user based checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}
					//return "Welcome to $facilityName. #$result->ticket_number.";
					$msg =  "WELCOME. #$result->ticket_number";

					$data = ['msg' => $msg];
					$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
					$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					$data['booking_type'] = 'driveup';
					if (isset($ticket->reservation->id)) {
						$data['booking_type'] = 'reservation';
					}
					$data['is_check_in_ontime'] = '1';
					$this->log->info("Checkin done, response sent.");
					return $data;
				} else {
					throw new ApiGenericException('Something wrong.');
				}
			}
			$ticket = Ticket::where("user_pass_id", $userPass->id)->get();
			if ($userPass->total_days == count($ticket)) {
				throw new ApiGenericException('No prepaid booking or pass found.');
			}

			$reservation = Reservation::with('user')->where("user_pass_id", $userPass->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->orderBy("id", "DESC")->first();
			if ($reservation) {
				if ($reservation->is_ticket == '1') {
					throw new ApiGenericException('You have already checked-in.');
				} else if ($reservation->is_ticket == '2') {
					throw new ApiGenericException('You have already checkout.');
				} else {
				}
				$userPass = [];
				$user = isset($reservation->user) ? $reservation->user : [];
			} else {
				$user = isset($userPass->user) ? $reservation->user : [];
			}
		}

		$config = Configuration::where('field_name', 'prepaid-checkin-time')->where("facility_id", $facility->id)->first();
		if (count($config) > 0) {
			$prepaidCheckinTime = $config->field_value;
		} else {
			$prepaidCheckinTime = 15;
		}
		//  $prepaidCheckinTime = '15';
		$today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);
		$reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);

		// converted the decimal lenght to hours and minutes by Ashutosh
		$time = Carbon::parse($reservation->start_timestamp)->addHours($reservation->length);
		if (intval($reservation->length) != $reservation->length) {
			$timarr = explode('.', $reservation->length);
			// $minute = ('.' . $timarr[1]) * 60; 
			$time->addMinutes($timarr[1]);
		}
		$reservationEndDate = $time->format('Y-m-d H:i:s');

		if (strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)) {

			$result = Ticket::create($data);

			$this->log->info("early reservation worldport user based checkin done --" . json_encode($result));
			if ($result) {

				// send Notification 
				$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
				if ($deviceToken) {
					$updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::CHECKIN_NOTIFICATION];
					Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
				}

				$facilityName = ucwords($facility->full_name);
				try {
					Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Welcome to " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $result->ticket_number;
					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				} catch (\Exception $e) {
					$this->log->error("System exception -" . $e->getMessage());
				}

				$this->log->info("user based checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				//return "Welcome to $facilityName. #$result->ticket_number.";
				$msg =  "WELCOME. #$result->ticket_number";

				$data = ['msg' => $msg];
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data['booking_type'] = 'driveup';
				if (isset($ticket->reservation->id)) {
					$data['booking_type'] = 'reservation';
				}
				$data['is_check_in_ontime'] = '1';
				$this->log->info("Checkin done, response sent.");
				return $data;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}

		if (strtotime($today) > strtotime($reservation->start_timestamp)) {
			$result = Ticket::create($data);

			$this->log->info("early reservation 2 worldport user based checkin done --" . json_encode($result));
			if ($result) {

				// send Notification 
				$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
				if ($deviceToken) {
					$updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::CHECKIN_NOTIFICATION];
					Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
				}

				$facilityName = ucwords($facility->full_name);
				try {
					Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Welcome to " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $result->ticket_number;
					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				} catch (\Exception $e) {
					$this->log->error("System exception -" . $e->getMessage());
				}

				$this->log->info("user based checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}
				//return "Welcome to $facilityName. #$result->ticket_number.";
				$msg =  "WELCOME. #$result->ticket_number";

				$data = ['msg' => $msg];
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data['booking_type'] = 'driveup';
				if (isset($ticket->reservation->id)) {
					$data['booking_type'] = 'reservation';
				}
				$data['is_check_in_ontime'] = '1';
				$this->log->info("Checkin done, response sent.");
				return $data;
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
		if ($reservation) {
			$data['reservation_id'] = $reservation->id;
			$data['check_in_datetime'] = $reservation->start_timestamp;
			$data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
			$data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
			$data['total'] = $reservation->total;
			$data['grand_total'] = $reservation->total;
			$data['length'] = $reservation->length;
			$data['user_pass_id'] = $reservation->user_pass_id;
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['vp_device_checkin'] = '0';
			$reservation->is_ticket = '1';
			$reservation->save();
		}

		$result = Ticket::create($data);

		$this->log->info("worldport user based checkin done --" . json_encode($result));
		if ($result) {

			// send Notification 
			$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
			if ($deviceToken) {
				$updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::CHECKIN_NOTIFICATION];
				Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
			}

			$facilityName = ucwords($facility->full_name);
			if ($reservation) {
				//Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			} else {
				try {
					Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Welcome to " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $result->ticket_number;
					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				} catch (\Exception $e) {
					$this->log->error("System exception -" . $e->getMessage());
				}
			}
			$this->log->info("user based checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			//return "Welcome to $facilityName. #$result->ticket_number.";
			$msg =  "WELCOME. #$result->ticket_number";

			$data = ['msg' => $msg];
			$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
			$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
			$data['booking_type'] = 'driveup';
			if (isset($ticket->reservation->id)) {
				$data['booking_type'] = 'reservation';
			}
			$data['is_check_in_ontime'] = '1';
			$this->log->info("User Checkin done, response sent.");
			return $data;
		} else {
			throw new ApiGenericException('Something wrong.');
		}
	}

	public function userCheckoutByQrCode($user, $request, $facility)
	{
		$this->log->info("userCheckoutByQrCode " . json_encode($request->all()));
		$ticket = Ticket::with('reservation')->where("user_id", $user->id)->where("facility_id", $facility->id)->orderBy("id", "DESC")->first();
		if (!$ticket) {
			throw new ApiGenericException('No checkin found against this booking.');
		}

		$user = $ticket->user;

		if ($ticket->permit_request_id != '') {
			$ticket->is_checkout = '1';
			$ticket->checkout_gate = $request->gate_id;
			$ticket->checkout_time = date("Y-m-d H:i:s");
			$ticket->checkout_datetime = date("Y-m-d H:i:s");
			$ticket->estimated_checkout = date("Y-m-d H:i:s");
			$ticket->payment_date = date("Y-m-d H:i:s");
			$ticket->checkout_license_plate = $request->license_plate;
			$ticket->checkout_mode = '3';
			$ticket->is_transaction_status = '0';
			$ticket->save();

			if ($request->license_plate != '') {
				if ($facility->license_plate_model != '') {
					$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
					$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
				}
			}

			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}

			$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
			$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

			$data = ['msg' => $msg];
			$data['is_phone_linked'] = '0';
			$data['phone_linked_msg'] = '';
			if (isset($user->phone)) {
				$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
				if ($partnerDetails->user_id == self::PARTNER_ID) {
					$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
						$join->on('user_facilities.user_id', '=', 'users.id');
						$join->where('user_facilities.facility_id', "=", $facility->id);
					})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
					$name = ($getRM->slug) ? $getRM->slug : 'receipt';
				} else {
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
				}
				$url = env('RECEIPT_URL');
				$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
				//changes for breeze specific URL from DB
				if ($dynamicReceiptUrl) {
					$url = $dynamicReceiptUrl->value;
				}
				$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

				dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';

				// send Notification 
				$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
				if ($deviceToken) {

					$updateJobParams = ['user_id' => $ticket->user_id, 'ticket_number' => $ticket->ticket_number, 'notification_type' => self::CHECKOUT_NOTIFICATION];
					Artisan::queue('customer:checkout-pushnotification', $updateJobParams);
				}
			}

			$this->log->info("SMS sent, response sent");
			return $data;
		}

		if (!isset($facility->FacilityPaymentDetails->id)) {
			throw new ApiGenericException('Payment details not defined against this garage.');
		}

		//overstay case

		if ($ticket->estimated_checkout != '') {
			$this->log->info("eticket already paid.");
			$overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();

			if ($overstayExist) {
				if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

					$ticket->is_checkout = '1';
					$ticket->checkout_gate = $request->gate_id;
					$ticket->checkout_time = date("Y-m-d H:i:s");
					$ticket->checkout_datetime = date("Y-m-d H:i:s");
					$ticket->checkout_license_plate = $request->license_plate;
					$ticket->checkout_mode = '3';
					$ticket->is_transaction_status = '0';
					$ticket->save();

					if (isset($ticket->reservation->id)) {
						$ticket->reservation->is_ticket = '2';
						$ticket->reservation->save();
					}

					if ($request->license_plate != '') {
						if ($facility->license_plate_model != '') {
							$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
							$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
						}
					}

					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
					$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

					$data = ['msg' => $msg];
					$data['is_phone_linked'] = '0';
					$data['phone_linked_msg'] = '';
					if (isset($user->phone)) {
						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						if ($partnerDetails->user_id == self::PARTNER_ID) {
							$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
								$join->on('user_facilities.user_id', '=', 'users.id');
								$join->where('user_facilities.facility_id', "=", $facility->id);
							})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
							$name = ($getRM->slug) ? $getRM->slug : 'receipt';
						} else {
							$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						}
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					}

					$this->log->info("SMS sent, response sent");
					return $data;
				}
				$this->log->info("eticket overstay created.");
				$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->estimated_checkout);
				$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
				$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->estimated_checkout);
				/** this function is used to get Availability Information for respective facility **/
				$isMember = 0;

				$this->log->info("get Diff in Hours : {$diff_in_hours}");
				$rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
				$this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");


				$isMember = 0;
				if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
					$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
				} else {
					$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, $isMember);
				}
				$taxRate = $ticket->getTaxRate($rate);          // to get tax price
				$data['price'] =  $rate['price'] + $taxRate;
				$data['eticket_id'] =  $ticket->ticket_number;
				$data['is_overstay'] =  '1';
				return $data;
			} else {
				if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
					$ticket->is_checkout = '1';
					$ticket->checkout_gate = $request->gate_id;
					$ticket->checkout_time = date("Y-m-d H:i:s");
					$ticket->checkout_datetime = date("Y-m-d H:i:s");
					$ticket->checkout_license_plate = $request->license_plate;
					$ticket->checkout_mode = '3';
					$ticket->is_transaction_status = '0';
					$ticket->save();

					if (isset($ticket->reservation->id)) {
						$ticket->reservation->is_ticket = '2';
						$ticket->reservation->save();
					}

					if ($request->license_plate != '') {
						if ($facility->license_plate_model != '') {
							$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
							$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
						}
					}

					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
					$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					if ($partnerDetails->user_id == self::PARTNER_ID) {
						$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
							$join->on('user_facilities.user_id', '=', 'users.id');
							$join->where('user_facilities.facility_id', "=", $facility->id);
						})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
						$name = ($getRM->slug) ? $getRM->slug : 'receipt';
					} else {
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					}
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
					$this->log->info("SMS sent, response sent");
					$data = ['msg' => $msg];
					$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
					$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					// send Notification 
					$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
					if ($deviceToken) {
						$updateJobParams = ['user_id' => $ticket->user_id, 'ticket_number' => $ticket->ticket_number, 'notification_type' => self::CHECKOUT_NOTIFICATION];
						Artisan::queue('customer:checkout-pushnotification', $updateJobParams);
					}
					return $data;
				}
				$this->log->info("overstay created.");
				$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout);
				$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
				$diff_in_hours = $ticket->getCheckOutCurrentTime(true, ($ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout));
				/** this function is used to get Availability Information for respective facility **/
				$isMember = 0;

				$this->log->info("get Diff in Hours : {$diff_in_hours}");
				$rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
				$this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");
				$isMember = 0;
				if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
					$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
				} else {
					$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, $isMember);
				}
				$taxRate = $ticket->getTaxRate($rate);          // to get tax price
				$data['price'] =  $rate['price'] + $taxRate;
				$data['eticket_id'] =  $ticket->ticket_number;
				$data['is_overstay'] =  '1';
				$ticket->save();
				return $data;
			}
		}



		$arrival_time = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
		$parkingNowTime = date("Y-m-d H:i:s");

		$diff_in_hours = $ticket->getCheckOutCurrentTime(true);

		$isMember = 0;
		if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
			$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
			$rate['description'] = self::HOURLY_RATE;
		} else {
			$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
			if (!isset($rate['description'])) {
				$rate['description'] = self::BASE_RATE;
			}
		}
		$rateId = isset($rate['id']) ? $rate['id'] : '';
		$rateDescription = isset($rate['description']) ? $rate['description'] : '';
		$priceBreakUp = $ticket->priceBreakUp($rate);
		$this->log->info("eticket priceBreakUp " . json_encode($priceBreakUp));
		$promotionRequest = $this->getPromocodeRequestDetails($request, $priceBreakUp, $ticket);
		if (isset($promotionRequest->promocode_msg)) {
		} else {
			if ($promotionRequest) {
				if ($ticket->discount_amount >= $priceBreakUp['payable_amount'] && $ticket->promocode != '') {
					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					$ticket->processing_fee  = $priceBreakUp['processing_fee'];
					$ticket->tax_fee        = $priceBreakUp['tax_rate'];
					$ticket->parking_amount  = $priceBreakUp['parking_amount'];
					//$ticket->paid_amount     = $priceBreakUp['paid_amount'];
					//$ticket->discount_amount = $priceBreakUp['discount_amount'];
					$ticket->grand_total     = "0.00";
					$ticket->total     = $priceBreakUp['total'];
					$ticket->length     = $diff_in_hours;

					$ticket->checkout_mode = '3';
					$ticket->is_checkout = '1';
					$ticket->is_checkin = '1';
					$ticket->checkout_gate = $request->gate_id;
					$ticket->checkout_time = date("Y-m-d H:i:s");
					$ticket->checkout_datetime = date("Y-m-d H:i:s");
					$ticket->is_cloud_payment = '0';
					$ticket->checkout_license_plate = $request->license_plate;
					$ticket->rate_id = $rateId;
					$ticket->rate_description = $rateDescription;
					$ticket->is_transaction_status = '0';
					$ticket->save();

					if ($request->license_plate != '') {
						if ($facility->license_plate_model != '') {
							$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
							$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
						}
					}

					//$this->saveTransactionData($rate, $ticket);

					$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
					$msg = "THANK YOU FOR VISITING, #$ticket->ticket_number";
					if ($ticket->promocode != '') {
						$promocodeRequest = PromocodeRequest::where('promocode', $ticket->promocode)->orderBy("id", "desc")->first();
						if ($promocodeRequest) {
							$promotion = Promotion::find($promocodeRequest->promotion_id);
							if ($promotion) {
								$msg = "THANK YOU FOR VISITING, " . $promotion->name . " applied.#$ticket->ticket_number";
							}
							$promocodeRequest->delete();
						}
					}
					if (isset($user->phone)) {
						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
					}
					$this->log->info("SMS sent, response sent");
					$data = ['msg' => $msg];
					$data['is_phone_linked'] = isset($user->phone) ? '1' : '0';
					$data['phone_linked_msg'] = isset($user->phone) != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';

					$this->log->info("User checkout, response sent");
					return $data;
				} else {
					$priceBreakUp['discount_amount'] = $ticket->discount_amount;
					$priceBreakUp['payable_amount'] = sprintf("%.2f", ($priceBreakUp['payable_amount'] - $ticket->discount_amount));
				}
			}
		}

		if ($priceBreakUp['payable_amount'] > 0) {

			if ($facility->is_cloud_payment_enabled != '1' || $facility->is_cloud_payment_enabled != 1) {

				// $ticket->processing_fee  = $priceBreakUp['processing_fee'];
				// $ticket->tax_fee        = $priceBreakUp['tax_rate'];
				// $ticket->parking_amount  = $priceBreakUp['parking_amount'];
				// $ticket->paid_amount     = $priceBreakUp['paid_amount'];
				// $ticket->discount_amount = $priceBreakUp['discount_amount'];
				// $ticket->grand_total     = $priceBreakUp['payable_amount'];
				// $ticket->total     = $priceBreakUp['total'];
				// $ticket->length     = $diff_in_hours;
				// $ticket->save();
				QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
				$data['price'] =  sprintf("%.2f", ($priceBreakUp['payable_amount']));
				$data['eticket_id'] =  $ticket->ticket_number;
				$data['payment_token'] =  $ticket->payment_token;
				$this->log->info("price sent, response sent");
				return $data;
			}

			$refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $priceBreakUp['payable_amount'], $request);
			$this->log->info("Payment Response :" . json_encode($refundstatus));
			if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
				throw new ApiGenericException("Payment failed. Please try again");
			}
			$planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
			$ticket->anet_transaction_id = $planetTransaction->id;
		}

		if ($ticket->is_offline_payment == '0' && $ticket->paid_type == '9') {
			if ($priceBreakUp['total'] > 0) {
				$result['price'] =  $priceBreakUp['total'];
				$result['eticket_id'] =  $ticket->ticket_number;
				$result['payment_token'] =  $ticket->payment_token;
				QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
				// $ticket->processing_fee  = $priceBreakUp['processing_fee'];
				// $ticket->tax_fee        = $priceBreakUp['tax_rate'];
				// $ticket->parking_amount  = $priceBreakUp['parking_amount'];
				// $ticket->paid_amount     = $priceBreakUp['paid_amount'];
				// $ticket->discount_amount = $priceBreakUp['discount_amount'];
				// $ticket->grand_total     = $priceBreakUp['total'];
				// $ticket->total     = $priceBreakUp['total'];
				// $ticket->length     = $diff_in_hours;
				// $ticket->rate_id = $rateId;
				// $ticket->rate_description = $rateDescription;
				// $ticket->save();
				$this->log->info("eticket if payment declined and hit api again, price sent, response sent");
				return $result;
			}
		}

		//check gate api
		if ($facility->open_gate_enabled == '1') {
			$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}
		}

		$ticket->processing_fee  = $priceBreakUp['processing_fee'];
		$ticket->tax_fee        = $priceBreakUp['tax_rate'];
		$ticket->parking_amount  = $priceBreakUp['parking_amount'];
		$ticket->paid_amount     = $priceBreakUp['paid_amount'];
		$ticket->discount_amount = $priceBreakUp['discount_amount'];
		$ticket->grand_total     = $priceBreakUp['payable_amount'];
		$ticket->total     = $priceBreakUp['total'];
		$ticket->length     = $diff_in_hours;

		$ticket->checkout_mode = '3';
		$ticket->is_checkout = '1';
		$ticket->is_checkin = '1';
		$ticket->checkout_gate = $request->gate_id;
		$ticket->checkout_time = date("Y-m-d H:i:s");
		$ticket->checkout_datetime = date("Y-m-d H:i:s");
		$ticket->is_cloud_payment = '1';
		$ticket->checkout_license_plate = $request->license_plate;
		$ticket->rate_id = $rateId;
		$ticket->rate_description = $rateDescription;
		$ticket->is_transaction_status = '0';
		$ticket->save();

		if ($request->license_plate != '') {
			if ($facility->license_plate_model != '') {
				$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
				$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
			}
		}

		//$this->saveTransactionData($rate, $ticket);

		$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
		$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
		if (isset($user->phone)) {
			$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
			$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
			$url = env('RECEIPT_URL');
			$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
			//changes for breeze specific URL from DB
			if ($dynamicReceiptUrl) {
				$url = $dynamicReceiptUrl->value;
			}
			$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

			dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));

			// send Notification 
			$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
			if ($deviceToken) {
				$updateJobParams = ['user_id' => $ticket->user_id, 'ticket_number' => $ticket->ticket_number, 'notification_type' => self::CHECKOUT_NOTIFICATION];
				Artisan::queue('customer:checkout-pushnotification', $updateJobParams);
			}
		}
		$this->log->info("SMS sent, response sent");
		$data = ['msg' => $msg];
		$data['is_phone_linked'] = isset($user->phone) ? '1' : '0';
		$data['phone_linked_msg'] = isset($user->phone) != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';

		$this->log->info("User checkout, response sent");

		return $data;
	}

	public function ticketCheckinCheckout(Request $request)
	{
		$this->log->info("ticketCheckinCheckout --" . json_encode($request->all()));
		$facility = Facility::with('FacilityPaymentDetails.facilityPaymentType', 'facilityConfiguration')->find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException('Sorry,Invalid garage.');
		}
		$facilityName =  ucwords($facility->full_name);
		// get all facility of RM from facility id (Ashutosh 19-09-2023)
		$facilityArray = UserFacility::getRMFacilities($request->facility_id);
		$this->setCustomTimezone($facility->id); //set custom Time Zone added by Ashutosh
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}

		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}
		//check gate api
		if ($facility->check_vehicle_enabled == '1') {
			//$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
			$lprService = new LprTransientService();
			$gateStatus = $lprService->isParkEngageGateAvailable($facility->id, $facility->adam_host, $gate->gate, $gate->gate_type);
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}
		}

		$this->log->info("gate check --" . json_encode($gate->gate_type));
		// ******************************Entry Gate code******************************
		if (isset($gate) && $gate->gate_type == "entry") {

			if ($facility->facility_booking_type == '1') {
				throw new ApiGenericException('Prepaid booking is not allowed.');
			}
			//  ********************************Third Party Permit Code if is_thirdparty_permit=1 or PROX card Code
			if (($request->is_thirdparty_permit == '1' || $request->is_thirdparty_permit == 1) && $request->ticket_id != '') {
				$this->log->info("1 is_thirdparty_permit check");
				if (isset($facility->facilityConfiguration->hid_card_enabled) && $facility->facilityConfiguration->hid_card_enabled == '0') {
					throw new ApiGenericException('This facility does not currently support HID card verification.');
				}
				$permitRequest = PermitRequest::with(['user'])->where('hid_card_number', $request->ticket_id)->orWhere('hid_card_number', ltrim($request->ticket_id, '0'))->where('facility_id', $request->facility_id)->where('status', 1)->whereNull("cancelled_at")->orderBy("id", "DESC")->first();
				$this->log->info("permit check --" . json_encode($permitRequest));
				//->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))
				if ($permitRequest) {
					//$this->log->info("permit check--" . json_encode($permitRequest));
					if (strtotime($permitRequest->desired_start_date) <= strtotime(date("Y-m-d")) && strtotime($permitRequest->grace_end_date) >= strtotime(date("Y-m-d"))) {
						if (($permitRequest->is_antipass_enabled == 0 || $permitRequest->is_antipass_enabled == "0") && $facility->facilityConfiguration->is_antipass_enabled == 0) {
							$permitTicket = Ticket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
							if ($permitTicket) {
								throw new ApiGenericException('You have already checked-in.');
							}
						}
						if ($request->license_plate == '') {
							$this->log->info("normal permit check 2");
							$mapping = PermitVehicleMapping::select('permit_vehicles.license_plate_number')
								->join('permit_vehicles', 'permit_vehicles.id', '=', 'permit_vehicle_mapping.permit_vehicle_id')
								->where('permit_vehicle_mapping.permit_request_id', $permitRequest->id)
								->orderBy('permit_vehicle_mapping.id', 'DESC')
								->first();

							if ($mapping && $mapping->license_plate_number) {
								$request->request->add(['license_plate' => $mapping->license_plate_number]);
							}
						}
						if ($permitRequest->is_antipass_enabled == 1 || $permitRequest->is_antipass_enabled == "1" || $facility->facilityConfiguration->is_antipass_enabled == 1 || $facility->facilityConfiguration->is_antipass_enabled == "1") {
							$this->log->info("is_antipass_enabled is_thirdparty_permit check");
							// $permitRequest->is_antipass_enabled = "0";
							// $permitRequest->save();

							$openTicket = Ticket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
							if ($openTicket) {
								$openTicket->is_checkout = '1';
								$openTicket->checkout_gate = $request->gate_id;
								$openTicket->checkout_time = date("Y-m-d H:i:s");
								$openTicket->checkout_datetime = date("Y-m-d H:i:s");
								$openTicket->estimated_checkout = date("Y-m-d H:i:s");
								$openTicket->checkout_license_plate = $request->license_plate;
								$openTicket->checkout_mode = '3';
								$openTicket->is_transaction_status = '0';
								$openTicket->checkout_remark = 'Closed due to Anti passback';
								$openTicket->save();
							}
						}

						$facilityName = ucwords($facility->full_name);
						$data['user_id'] = $permitRequest->user_id;
						$data['checkin_gate'] = $request->gate_id;
						$data['facility_id'] = $request->facility_id;
						$data['is_checkin'] = 1;
						$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
						$data['checkin_time'] = date('Y-m-d H:i:s');
						$data['check_in_datetime'] = date('Y-m-d H:i:s');
						//$data['checkout_datetime'] = $permitRequest->desired_end_date;
						$data['ticket_security_code'] = rand(1000, 9999);
						$data['partner_id'] = $facility->owner_id;
						//$data['total'] = $permitRequest->permit_rate;
						//$data['grand_total'] = $permitRequest->permit_rate;
						$data['permit_request_id'] = $permitRequest->id;
						$data['license_plate'] = $request->license_plate;
						$data['device_type'] = 'IM30/PROX';
						$result = Ticket::create($data);
						$this->log->info("4 Ticket::create sucessfully");
						if ($result) {

							//update pass back status 0 default 1 for checkin 2 for checkout 3 for reset
							$permitRequest->passback_status = "1";
							$permitRequest->save();
							//$this->log->info("check ticket number-----1");
							// $ticketCount = Ticket::where("user_id", $permitRequest->user_id)->where("is_checkout", "0")->count();
							// $this->log->info("check ticket number-----2");
							// if ($ticketCount >= self::ANTIPASSBACK_COUNT) {
							// 	$permitRequest->is_antipass_enabled = "0";
							// 	$permitRequest->save();
							// }
						}
						//Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
								$this->log->info("get open succesfully.");
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}
						$this->log->info("4 is_thirdparty_permit check");
						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
							$join->on('user_facilities.user_id', '=', 'users.id');
							$join->where('user_facilities.facility_id', "=", $facility->id);
						})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
						if (isset($getRM->slug)) {
							$name = ($getRM->slug) ? $getRM->slug : 'receipt';
						} else {
							$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						}
						$url = env('TOUCHLESS_WEB_URL');
						$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
						if ($dynamicCheckinUrl) {
							$url = $dynamicCheckinUrl->value;
						}
						$grace_period = $facility->grace_period_minute;
						$ticket_number = base64_encode($result->ticket_number);
						$smsUrl = "{$url}/{$name}/{$ticket_number}";
						$msg = "Thank you for the Check-in at $facilityName. This check-in is created against Permit #{$permitRequest->account_number}. Use {$smsUrl} to view your ticket details.";
						if (isset($permitRequest->user->phone)) {
							dispatch((new SendSms($msg, $permitRequest->user->phone))->onQueue(self::QUEUE_NAME));
						}
						//$msg =  "Welcome to $facilityName. #$result->ticket_number.";
						$msg =  "WELCOME. #$result->ticket_number";
						//check gate api

						$data = [
							'msg' => $msg,
							'permit_number' => $permitRequest->account_number,
							'checkin_time' => date("g:i A", strtotime($result->checkin_time)),
							'booking_type' => "permit",
							'is_check_in_ontime' => "1",
							'eticket_id' => $result->ticket_number
						];

						$data['is_phone_linked'] = '0';
						$data['phone_linked_msg'] = '';
						if (isset($permitRequest->user->phone) && $permitRequest->user->phone != '') {
							$data['is_phone_linked'] = '1';
							$data['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
						}
						$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
						return $data;
					} else {
						throw new ApiGenericException('Your Permit is expired or canceled.');
					}
				}
			}
			//  ********************************End *********Third Party Permit Code if is_thirdparty_permit=1 or PROX card Code
			$this->log->info("ticketcheckincheckout 2");
			// **************** Normal Permit is_thirdparty_permit=0 or LPR Feed Code ******************
			$permitRequest = PermitRequest::with(['user'])->where('account_number', $request->ticket_id)->where('facility_id', $request->facility_id)->whereNull("cancelled_at")->orderBy("id", "DESC")->first();
			//->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))
			if ($permitRequest) {
				$this->log->info("normal permit check 1");
				if (strtotime($permitRequest->desired_start_date) <= strtotime(date("Y-m-d")) && strtotime($permitRequest->grace_end_date) >= strtotime(date("Y-m-d"))) {
					if (($permitRequest->is_antipass_enabled == 0 || $permitRequest->is_antipass_enabled == "0") && $facility->facilityConfiguration->is_antipass_enabled == 0) {
						$permitTicket = Ticket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
						if ($permitTicket) {
							throw new ApiGenericException('You have already checked-in.');
						}
					}
					if ($request->license_plate == '') {
						$this->log->info("normal permit check 2");
						$mapping = PermitVehicleMapping::select('permit_vehicles.license_plate_number')
							->join('permit_vehicles', 'permit_vehicles.id', '=', 'permit_vehicle_mapping.permit_vehicle_id')
							->where('permit_vehicle_mapping.permit_request_id', $permitRequest->id)
							->orderBy('permit_vehicle_mapping.id', 'DESC')
							->first();

						if ($mapping && $mapping->license_plate_number) {
							$request->request->add(['license_plate' => $mapping->license_plate_number]);
						}
					}

					if ($permitRequest->is_antipass_enabled == 1 || $permitRequest->is_antipass_enabled == "1" || $facility->facilityConfiguration->is_antipass_enabled == 1 || $facility->facilityConfiguration->is_antipass_enabled == "1") {
						$this->log->info("normal permit check is_antipass_enabled");
						// $permitRequest->is_antipass_enabled = "0";
						// $permitRequest->save();

						$openTicket = Ticket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
						if ($openTicket) {
							$openTicket->is_checkout = '1';
							$openTicket->checkout_gate = $request->gate_id;
							$openTicket->checkout_time = date("Y-m-d H:i:s");
							$openTicket->checkout_datetime = date("Y-m-d H:i:s");
							$openTicket->estimated_checkout = date("Y-m-d H:i:s");
							$openTicket->checkout_license_plate = $request->license_plate;
							$openTicket->checkout_mode = '3';
							$openTicket->is_transaction_status = '0';
							$openTicket->checkout_remark = 'Closed due to Anti passback';
							$openTicket->save();
						}
					}

					$facilityName = ucwords($facility->full_name);

					$data['user_id'] = $permitRequest->user_id;
					$data['checkin_gate'] = $request->gate_id;
					$data['facility_id'] = $request->facility_id;
					$data['is_checkin'] = 1;
					$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
					$data['checkin_time'] = date('Y-m-d H:i:s');
					$data['check_in_datetime'] = date('Y-m-d H:i:s');
					//$data['checkout_datetime'] = $permitRequest->desired_end_date;
					$data['ticket_security_code'] = rand(1000, 9999);
					$data['partner_id'] = $facility->owner_id;
					//$data['total'] = $permitRequest->permit_rate;
					//$data['grand_total'] = $permitRequest->permit_rate;
					$data['permit_request_id'] = $permitRequest->id;
					$data['license_plate'] = $request->license_plate;
					$data['device_type'] = 'IM30';
					$result = Ticket::create($data);
					$this->log->info("normal permit checkin done");
					if ($result) {
						//update pass back status 0 default 1 for checkin 2 for checkout 3 for reset
						$permitRequest->passback_status = "1";
						$permitRequest->save();

						// $ticketCount = Ticket::where("user_id", $permitRequest->user_id)->where("is_checkout", "0")->count();
						// if ($ticketCount >= self::ANTIPASSBACK_COUNT) {
						// 	$permitRequest->is_antipass_enabled = "0";
						// 	$permitRequest->save();
						// }
					}
					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}
					//Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
						$join->on('user_facilities.user_id', '=', 'users.id');
						$join->where('user_facilities.facility_id', "=", $facility->id);
					})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
					if (isset($getRM->slug)) {
						$name = ($getRM->slug) ? $getRM->slug : 'receipt';
					} else {
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					}
					$url = env('TOUCHLESS_WEB_URL');
					$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
					if ($dynamicCheckinUrl) {
						$url = $dynamicCheckinUrl->value;
					}
					$grace_period = $facility->grace_period_minute;
					$ticket_number = base64_encode($result->ticket_number);
					//$msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
					$smsUrl = "{$url}/{$name}/{$ticket_number}";
					$msg = "Thank you for the Check-in at $facilityName. This check-in is created against Permit #{$permitRequest->account_number}. Use {$smsUrl} to view your ticket details.";
					if (isset($permitRequest->user->phone)) {
						dispatch((new SendSms($msg, $permitRequest->user->phone))->onQueue(self::QUEUE_NAME));
					}

					//$msg =  "Welcome to $facilityName. #$result->ticket_number.";
					$msg =  "WELCOME. #$result->ticket_number";

					$data = [
						'msg' => $msg,
						'permit_number' => $permitRequest->account_number,
						'checkin_time' => date("g:i A", strtotime($result->checkin_time)),
						'booking_type' => "permit",
						'is_check_in_ontime' => "1",
						'eticket_id' => $result->ticket_number
					];
					$data['is_phone_linked'] = '0';
					$data['phone_linked_msg'] = '';
					if (isset($permitRequest->user->phone) && $permitRequest->user->phone != '') {
						$data['is_phone_linked'] = '1';
						$data['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
					}
					$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
					return $data;
				} else {
					throw new ApiGenericException('Your Permit is expired or canceled.');
				}
			}
			// ****************END  Normal Permit is_thirdparty_permit=0 or LPR Feed Code ******************
			$this->log->info("ticketcheckincheckout 3");
			//  ********************************Start MapcoQR Code Changes*****************************
			$qrCode = MapcoQrcode::with(['reservation.user', 'event', 'eventCategory.eventCategoryEvent.event'])->where("qrcode", $request->ticket_id)->first();

			if ($qrCode) {
				$this->log->info("qrCode entered --" . $qrCode->id);
				if ($qrCode->remain_usage == '0' || $qrCode->remain_usage == 0) {
					throw new ApiGenericException('You have already consumed your ticket.');
				}

				if (!isset($qrCode->reservation)) {
					throw new ApiGenericException('No prepaid booking found.');
				}
				if ($qrCode->reservation->cancelled_at != '') {
					throw new ApiGenericException('Your booking has been canceled.');
				}

				$today = date("Y-m-d");
				if ($qrCode->event_id != '0') {
					$this->log->info("event entered 1");
					$event = Event::whereDate("start_time", '<=', $today)->whereDate("end_time", '>=', $today)->where('id', $qrCode->event_id)->first();
					if (!$event) {
						throw new ApiGenericException('No event found.');
					}

					if (strtotime(date("Y-m-d H:i:s")) < strtotime($event->parking_start_time)) {
						throw new ApiGenericException('No event found.');
					} else if (strtotime(date("Y-m-d H:i:s")) > strtotime($event->parking_end_time)) {
						throw new ApiGenericException('No event found.');
					}

					$event_id = $qrCode->event_id;
				}
				if ($qrCode->event_id == '0' && ($qrCode->event_category_id != '0' || $qrCode->event_category_id != '')) {
					$this->log->info("event entered 2");
					//$today = date("Y-m-d");
					//$today = "2022-12-07";
					$eventCategoryEvent = EventCategoryEvent::leftJoin('events', 'events.id', '=', 'event_category_events.event_id')
						->select('events.id', 'events.start_time')->whereDate('events.start_time', '=', $today)->where('event_category_events.event_category_id',  $qrCode->event_category_id)->first();
					if (!isset($eventCategoryEvent->start_time)) {
						throw new ApiGenericException('No event found.');
					}
					$event_id = $eventCategoryEvent->id;
				}

				$user = $qrCode->reservation->user;

				$this->log->info("qrCode entered 11--");
				$data['user_id'] = $user->id;
				$data['checkin_gate'] = $request->gate_id;
				$data['facility_id'] = $request->facility_id;
				$data['is_checkin'] = 1;
				$data['ticket_number'] = $this->checkTicketNumber();
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['ticket_security_code'] = rand(1000, 9999);
				$data['partner_id'] = $facility->owner_id;
				$data['reservation_id'] = $qrCode->reservation_id;
				$data['anet_transaction_id'] = $qrCode->reservation->anet_transaction_id;
				$data['check_in_datetime'] = $qrCode->reservation->start_timestamp;
				/*13537 dd*/
				$resHours = (int) $qrCode->reservation->length;
				$diff = ($qrCode->reservation->length - $resHours);
				$resMinutes = $diff * 100;
				$data['checkout_datetime'] = $qrCode->reservation->start_timestamp->addHours($resHours)->addMinutes($resMinutes)->subSeconds(1)->format('Y-m-d H:i:s');
				$data['estimated_checkout'] = $qrCode->reservation->start_timestamp->addHours($resHours)->addMinutes($resMinutes)->subSeconds(1)->format('Y-m-d H:i:s');
				/*13537 dd*/
				$data['total'] = $qrCode->reservation->total;
				$data['grand_total'] = $qrCode->reservation->total;
				$data['length'] = $qrCode->reservation->length;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['event_id'] = $event_id;
				$result = Ticket::create($data);
				$this->log->info("event entered checkin done");
				$qrCode->remain_usage = $qrCode->remain_usage == 0 ? 0 : $qrCode->remain_usage - 1;
				$qrCode->save();

				$this->log->info("qrCode entered 22--");
				$msg =  "WELCOME. #$result->ticket_number";

				if ($user->phone != '') {
					//$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					if ($partnerDetails->user_id == self::PARTNER_ID) {
						$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
							$join->on('user_facilities.user_id', '=', 'users.id');
							$join->where('user_facilities.facility_id', "=", $facility->id);
						})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
						$name = ($getRM->slug) ? $getRM->slug : 'receipt';
					} else {
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					}
					$url = env('TOUCHLESS_WEB_URL');
					$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
					if ($dynamicCheckinUrl) {
						$url = $dynamicCheckinUrl->value;
					}
					$grace_period = $facility->grace_period_minute;
					$ticket_number = base64_encode($result->ticket_number);
					$sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				}

				$data = ['msg' => $msg];
				$data['booking_number'] = $qrCode->reservation->ticketech_code;
				$data['booking_start_time'] = date("g:i A", strtotime($result->check_in_datetime));
				$data['booking_exit_time'] = date("g:i A", strtotime('+1 second', strtotime($result->checkout_datetime)));
				$data['booking_entry_time'] = date("g:i A", strtotime($result->checkin_time));
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data['booking_type'] = 'reservation';
				$data['is_check_in_ontime'] = '1';
				$this->log->info("qrCode entered 44--");
				return $data;
			}
			//  ********************************End  MapcoQR Code Changes*****************************    
			//  ********************************Start  userQrCode Code Changes*****************************  
			$userQrCode = [];
			if ($request->is_thirdparty_permit == '0') {
				$userQrCode = User::where("qr_code_number", $request->ticket_id)->first();
			}
			if ($userQrCode) {
				return $this->userCheckinByQrCode($userQrCode, $request, $facility);
			}
			//  ********************************End  userQrCode Code Changes*****************************  

			//  ********************************Start  Reservation code Code Changes*****************************  
			$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
			$userPass = [];
			if (!$reservation) {
				$this->log->info("not reservation check");
				// $reservation = Reservation::with('user')->where("facility_id", $request->facility_id)->where("ticketech_code", $request->ticket_id)->whereNull('cancelled_at')->first();
				// Remove the facility check for RM based checkin (Ashutosh 19-09-2023)
				$userPass = UserPass::with('user')->where("facility_id", $request->facility_id)->where("pass_code", $request->ticket_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
				if (!$userPass) {
					throw new ApiGenericException('No prepaid booking or pass found.');
				}
				$ticket = Ticket::where("user_pass_id", $userPass->id)->get();
				if ($userPass->total_days == count($ticket)) {
					throw new ApiGenericException('You have already consumed your pass.');
				}

				// $reservation = Reservation::with('user')->where("facility_id", $request->facility_id)->where("user_pass_id", $userPass->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->orderBy("id", "DESC")->first();
				//  Remove the facility check for RM (Ashutosh 19-09-2023)
				$user = $userPass->user;
			} else {
				$this->log->info("reservation check");
				// checked the facility are in same RM or not (Ashutosh 19-09-2023)
				if (!in_array($reservation->facility_id, $facilityArray)) {
					throw new ApiGenericException('Facility not in the same RM');
				}
				$user = isset($reservation->user) ? $reservation->user : [];

				$config = Configuration::where('field_name', 'prepaid-checkin-time')->where("facility_id", $facility->id)->first();
				if (count($config) > 0) {
					$prepaidCheckinTime = $config->field_value;
				} else {
					$prepaidCheckinTime = 15;
				}
				$today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);
				$reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
				// $reservationEndDate = $reservationstartDate->addHours($reservation->length);
				// converted the decimal lenght to hours and minutes by Ashutosh
				$time = Carbon::parse($reservation->start_timestamp)->addHours($reservation->length);
				if (intval($reservation->length) != $reservation->length) {
					$timarr = explode('.', $reservation->length);
					$time->addMinutes($timarr[1]);
				}
				$reservationEndDate = $time->format('Y-m-d H:i:s');

				if (strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)) {
					$data['is_check_in_ontime'] = '0';
					$data['booking_number'] = $reservation->ticketech_code;
					$data['booking_start_time'] = date("g:i A", strtotime($reservation->start_timestamp));
					$data['booking_type'] = 'reservation';
					$data['msg'] = 'EARLY FOR RESERVATION';
					return $data;
				}

				if (strtotime($today) < strtotime($reservation->start_timestamp)) {
					$data['is_check_in_ontime'] = '0';
					$data['booking_number'] = $reservation->ticketech_code;
					$data['booking_start_time'] = date("g:i A", strtotime($reservation->start_timestamp));
					$data['booking_type'] = 'reservation';
					$data['msg'] = 'EARLY FOR RESERVATION';
					return $data;
				}
			}
			//  ********************************End  Reservation code Code Changes*****************************  

			$this->log->info("reservation check 2");
			if (isset($user->id)) {
				$ticket = Ticket::where('user_id', $user->id)->where("facility_id", $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
				if ($ticket) {
					throw new ApiGenericException('You have already checked-in.');
				}
			}

			//lpr case when checkin by lpr and now we updated details on the basis of license plate
			$existLicenseTicket = Ticket::where('license_plate', $request->license_plate)->where("facility_id", $request->facility_id)->where('is_checkin', '0')->where('is_checkout', '0')->orderBy("id", "DESC")->first();
			if ($existLicenseTicket) {
				$this->log->info("reservation check existLicenseTicket");
				$existLicenseTicket->user_id = isset($user->id) ? $user->id : '';
				$existLicenseTicket->checkin_gate = $request->gate_id;
				$existLicenseTicket->facility_id = $request->facility_id;
				$existLicenseTicket->is_checkin = 1;
				if ($reservation) {
					$existLicenseTicket->reservation_id = $reservation->id;
					$existLicenseTicket->check_in_datetime = $reservation->start_timestamp;
					$existLicenseTicket->checkout_datetime = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
					$existLicenseTicket->estimated_checkout = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
					//$existLicenseTicket->payment_date = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
					//$existLicenseTicket->total = $reservation->total;
					//$existLicenseTicket->grand_total = $reservation->total;
					$existLicenseTicket->length = $reservation->length;
					//$existLicenseTicket->user_pass_id = $reservation->user_pass_id;
					//$data['checkin_time'] = date('Y-m-d H:i:s');
					$existLicenseTicket->license_plate = $request->license_plate != '' ? $request->license_plate : $reservation->license_plate;

					$existLicenseTicket->save();
					$result = $existLicenseTicket;
					$reservation->is_ticket = '1';
					$reservation->save();
				}

				$this->log->info("reservation check 5");
				$facilityName = ucwords($facility->full_name);
				if (isset($user->id)) {
					if ($reservation) {
						//Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
					} else {
						Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
						$msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
						dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
					}
				}

				$this->log->info("checkin SMS send to user with ticket number {$result->ticket_number}");


				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}

				// send Notification 
				if (isset($user->id)) {
					$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
					if ($deviceToken) {
						$updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::RES_CHECKIN_NOTIFICATION];
						Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
					}
				}

				//$msg =  "Welcome to $facilityName. #$result->ticket_number.";
				$msg =  "WELCOME. #$result->ticket_number";
				$data = ['msg' => $msg];
				$data['booking_number'] = $reservation->ticketech_code;
				$data['booking_start_time'] = date("g:i A", strtotime($result->check_in_datetime));
				$data['booking_exit_time'] = date("g:i A", strtotime('+1 second', strtotime($result->checkout_datetime)));
				$data['booking_entry_time'] = date("g:i A", strtotime($result->checkin_time));
				if (isset($user->id)) {
					$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
					$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				}
				$data['booking_type'] = 'reservation';
				$data['is_check_in_ontime'] = '1';
				$this->log->info("reservation check 6");
				return $data;
			}

			$data['user_id'] = isset($user->id) ? $user->id : '';
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['partner_id'] = $facility->owner_id;
			if ($reservation) {
				$this->log->info("reservation check 7");
				$data['reservation_id'] = $reservation->id;
				$data['check_in_datetime'] = $reservation->start_timestamp;
				/*13537 dd*/
				$resHours = (int) $reservation->length;
				$diff = ($reservation->length - $resHours);
				$resMinutes = $diff * 100;
				$data['checkout_datetime']  = $reservation->start_timestamp->addHours($resHours)->addMinutes($resMinutes)->subSeconds(1)->format('Y-m-d H:i:s');
				$data['estimated_checkout'] = $reservation->start_timestamp->addHours($resHours)->addMinutes($resMinutes)->subSeconds(1)->format('Y-m-d H:i:s');
				/*13537 dd end*/
				//$data['payment_date'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHours($resHours)->addMinutes($resMinutes)));
				//$data['total'] = $reservation->total;
				//$data['grand_total'] = $reservation->total;
				$data['length'] = $reservation->length;
				//$data['user_pass_id'] = $reservation->user_pass_id;
				$data['checkin_time'] = date('Y-m-d H:i:s');
				$data['license_plate'] = $request->license_plate != '' ? $request->license_plate : $reservation->license_plate;
				$reservation->is_ticket = '1';
				$reservation->save();
			}

			if (count($userPass) > 0) {
				$data['check_in_datetime'] = $userPass->start_time;
				$data['checkout_datetime'] = $userPass->end_time;
				$data['user_pass_id'] = $userPass->id;

				$userPass->consume_days = $userPass->consume_days + 1;
				$userPass->remaining_days = $userPass->remaining_days - 1;
				$userPass->save();
			}
			$data['device_type'] = 'IM30';
			$result = Ticket::create($data);
			$this->log->info("reservation check 8");
			$facilityName = ucwords($facility->full_name);
			if (isset($user->id)) {
				if ($reservation) {
					//Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
				} else {
					//Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
					$url = env('TOUCHLESS_WEB_URL');
					$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
					if ($dynamicCheckinUrl) {
						$url = $dynamicCheckinUrl->value;
					}
					$grace_period = $facility->grace_period_minute;
					$ticket_number = base64_encode($result->ticket_number);
					$msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
					dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
				}
			}

			$this->log->info("checkin SMS send to user with ticket number {$result->ticket_number}");


			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}

			// send Notification 
			if (isset($user->id)) {
				$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
				if ($deviceToken) {
					$updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::RES_CHECKIN_NOTIFICATION];
					Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
				}
			}

			//$msg =  "Welcome to $facilityName. #$result->ticket_number.";
			$msg =  "WELCOME. #$result->ticket_number";
			$data = ['msg' => $msg];
			if ($reservation) {
				$data['booking_number'] = $reservation->ticketech_code;
				$data['booking_start_time'] = date("g:i A", strtotime($result->check_in_datetime));
				$data['booking_exit_time'] = date("g:i A", strtotime('+1 second', strtotime($result->checkout_datetime)));
				$data['booking_entry_time'] = date("g:i A", strtotime($result->checkin_time));
				$data['booking_type'] = 'reservation';
			}
			if (isset($user->id)) {
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
			}
			$data['is_check_in_ontime'] = '1';
			return $data;
		} else if (isset($gate) && $gate->gate_type == "exit") {

			$this->log->info("exit ticketcheckicheckout");
			if (($request->is_thirdparty_permit == '1' || $request->is_thirdparty_permit == 1) && $request->ticket_id != '') {
				$this->log->info("exit ticketcheckicheckout is_thirdparty_permit");
				if (isset($facility->facilityConfiguration->hid_card_enabled) && $facility->facilityConfiguration->hid_card_enabled == '0') {
					throw new ApiGenericException('This facility does not currently support HID card verification.');
				}
				$permitRequest = PermitRequest::with(['user'])->where('hid_card_number', $request->ticket_id)->orWhere('hid_card_number', ltrim($request->ticket_id, '0'))->where('facility_id', $request->facility_id)->whereNull("cancelled_at")->orderBy("id", "DESC")->first();
				//->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))
				if ($permitRequest) {
					if (strtotime($permitRequest->desired_start_date) <= strtotime(date("Y-m-d")) && strtotime($permitRequest->grace_end_date) >= strtotime(date("Y-m-d"))) {
						$ticket = Ticket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
						if (!$ticket) {
							if ($permitRequest->is_antipass_enabled == "1" || $permitRequest->is_antipass_enabled == 1) {
								$this->log->info("exit ticketcheckicheckout is_thirdparty_permit is_antipass_enabled");
								$data['user_id'] = $permitRequest->user_id;
								$data['checkout_gate'] = $request->gate_id;
								$data['facility_id'] = $request->facility_id;
								$data['is_checkin'] = 0;
								$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
								//$data['checkin_time'] = date('Y-m-d H:i:s');
								//$data['check_in_datetime'] = date('Y-m-d H:i:s');
								//$data['checkout_datetime'] = $permitRequest->desired_end_date;
								$data['ticket_security_code'] = rand(1000, 9999);
								$data['partner_id'] = $facility->owner_id;
								//$data['total'] = $permitRequest->permit_rate;
								//$data['grand_total'] = $permitRequest->permit_rate;
								$data['permit_request_id'] = $permitRequest->id;
								$data['license_plate'] = $request->license_plate;
								$data['device_type'] = 'IM30';
								$data['is_checkout'] = '1';
								$data['checkout_time'] = date("Y-m-d H:i:s");
								$data['checkout_datetime'] = date("Y-m-d H:i:s");
								$data['checkout_license_plate'] = $request->license_plate;
								$data['checkout_mode'] = '3';
								$data['is_transaction_status'] = '0';
								$data['checkout_remark'] = 'Closed due to Anti passback';
								$result = Ticket::create($data);
								$ticket = $result;
								if ($result) {

									//update pass back status 0 default 1 for checkin 2 for checkout 3 for reset
									$permitRequest->passback_status = "2";
									$permitRequest->save();
								}
								if ($request->license_plate != '') {
									if ($facility->license_plate_model != '') {
										$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
										$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
									}
								}

								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								$user = $ticket->user;
								$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
								$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
								/*
							$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
							if ($partnerDetails->user_id == self::PARTNER_ID) {
							$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
								$join->on('user_facilities.user_id', '=', 'users.id');
								$join->where('user_facilities.facility_id', "=", $facility->id);
							})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
							$name = ($getRM->slug) ? $getRM->slug : 'receipt';
							} else {
							$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
							}
							$url = env('RECEIPT_URL');
									$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
									//changes for breeze specific URL from DB
									if($dynamicReceiptUrl){
										$url = $dynamicReceiptUrl->value;
									}
							$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

							dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));*/
								$this->log->info("SMS sent, response sent");
								$data = ['msg' => $msg];
								$data['is_phone_linked'] = '0';
								$data['phone_linked_msg'] = '';
								//nre changes
								$data['prox_card_number'] = isset($permitRequest->hid_card_number) ? $permitRequest->hid_card_number : '';

								$data["eticket_id"] =  $ticket->ticket_number;
								$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
								return $data;
							}
							$checkinTime = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
							if ($facility->facilityConfiguration->is_check_cloud_permit == '1') {
								$this->log->info("history before checkin 8");
								//check permit exist
								$permit = $this->checkExistingPermit($request->license_plate, $gate->facility_id, $gate->partner_id);
								if ($permitRequest->count() > 0) {
									$ticket = new Ticket();
									$this->log->info("history before checkin 10");
									$ticket->permit_request_id = $permitRequest->id;
									$ticket->user_id = $permitRequest->user_id;
									$this->log->info("history before checkin 11");
									$ticket->check_in_datetime  = date('Y-m-d H:i:s');
									$ticket->checkin_time       = date('Y-m-d H:i:s');
									$ticket->checkout_time      = date('Y-m-d H:i:s');
									$ticket->checkout_datetime  = date('Y-m-d H:i:s');
									$ticket->estimated_checkout = date('Y-m-d H:i:s');
									$ticket->is_checkin         = '1';
									$ticket->is_checkout        = '1';
									$ticket->facility_id = $gate->facility_id;
									$ticket->partner_id = $facility->owner_id;
									$ticketNumber = $this->checkTicketNumber($gate->facility_id);
									$isExist = Ticket::where('ticket_number', $ticketNumber)->first();
									if ($isExist) {
										$ticket->ticket_number = $this->checkTicketNumber($gate->facility_id);
									} else {
										$ticket->ticket_number = $ticketNumber;
									}
									$ticket->license_plate = $request->license_plate;
									$ticket->checkout_license_plate = $request->license_plate;
									$ticket->checkout_gate = $gate->gate;
									$ticket->device_type = "PROX Card";
									$ticket->checkout_mode = "4";
									$ticket->checkout_remark = "Ticket is created at exit as License Plate was not recognized at entry.";
									$ticket->save();
									if ($facility->open_gate_enabled == '1') {
										$gateStatus = $this->isParkEngageGateOpen($gate->facility_id, $gate->gate, '');
									}
									//update pass back status 0 default 1 for checkin 2 for checkout 3 for reset  always 2 due to not found 
									$permitRequest->passback_status = "2";
									$permitRequest->save();
								}
								$this->log->info("history before checkin 19");
								$msg = [];

								// $response_type = '1';
								$msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
								$msg['is_phone_linked'] = '0';
								$msg['phone_linked_msg'] = '';
								if (isset($ticket->user->id) && $ticket->user->phone != '') {
									$msg['is_phone_linked'] = '1';
									$msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
								}
								$msg['booking_type'] = 'permit';
								$msg['is_check_in_ontime'] = '1';
								$msg['eticket_id'] = $ticket->ticket_number;

								$msg['prox_card_number']         = isset($permitRequest->hid_card_number) ? $permitRequest->hid_card_number : '';
								$msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
								$gate_type = "Exit";
								// $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, self::QUEUE_NAME, $gate_type);
								//$msg['msg'] = "Thank you for visiting " . $facilityName;

								return $msg;
							} else {
								throw new ApiGenericException('No prepaid booking or pass found.');
							}
						}

						if ($request->license_plate == '') {
							$mappings = PermitVehicleMapping::where("permit_request_id", $permitRequest->id)->orderBy("id", "DESC")->get();
							if (count($mappings) > 1) {
								$vehicle = PermitVehicle::find($mappings[0]->permit_vehicle_id);
								if ($vehicle) {
									$request->request->add(['license_plate' => $vehicle->license_plate_number]);
								}
							} else {
								if (count($mappings) > 0) {
									$vehicle = PermitVehicle::find($mappings[0]->permit_vehicle_id);
									if ($vehicle) {
										$request->request->add(['license_plate' => $vehicle->license_plate_number]);
									}
								}
							}
						}

						$ticket->is_checkout = '1';
						$ticket->checkout_gate = $request->gate_id;
						$ticket->checkout_time = date("Y-m-d H:i:s");
						$ticket->checkout_datetime = date("Y-m-d H:i:s");
						$ticket->checkout_license_plate = $request->license_plate;
						$ticket->checkout_mode = '3';
						$ticket->is_transaction_status = '0';
						$ticket->save();

						if ($permitRequest->is_antipass_enabled == 1 || $permitRequest->is_antipass_enabled == "1" || $facility->facilityConfiguration->is_antipass_enabled == 1 || $facility->facilityConfiguration->is_antipass_enabled == "1") {

							if ($ticket) {
								$openTicket = Ticket::where("user_id", $permitRequest->user_id)->orderBy("id", "DESC")->first();
								if ($openTicket) {
									$openTicket->is_checkout = '1';
									$openTicket->checkout_gate = $request->gate_id;
									$openTicket->checkout_time = date("Y-m-d H:i:s");
									$openTicket->checkout_datetime = date("Y-m-d H:i:s");
									$openTicket->estimated_checkout = date("Y-m-d H:i:s");
									$openTicket->checkout_license_plate = $request->license_plate;
									$openTicket->checkout_mode = '3';
									$openTicket->is_transaction_status = '0';
									$openTicket->checkout_remark = 'Closed due to Anti passback';
									$openTicket->save();

									// $permitRequest->is_antipass_enabled = "0";
									// $permitRequest->save();
								}
							}
						}

						//update pass back status 0 default 1 for checkin 2 for checkout 3 for reset
						$permitRequest->passback_status = "2";
						$permitRequest->save();

						if ($request->license_plate != '') {
							if ($facility->license_plate_model != '') {
								$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
								$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
							}
						}

						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						$user = $ticket->user;
						$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
						$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						if ($partnerDetails->user_id == self::PARTNER_ID) {
							$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
								$join->on('user_facilities.user_id', '=', 'users.id');
								$join->where('user_facilities.facility_id', "=", $facility->id);
							})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
							$name = ($getRM->slug) ? $getRM->slug : 'receipt';
						} else {
							$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						}
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
						$this->log->info("SMS sent, response sent");
						$data = ['msg' => $msg];
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						$data["eticket_id"] =  $ticket->ticket_number;
						$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
						$data['prox_card_number'] = isset($permitRequest->hid_card_number) ? $permitRequest->hid_card_number : '';

						return $data;
					} else {
						throw new ApiGenericException('Your Permit is expired or canceled.');
					}
				}
			}
			// end third party permit  
			$permitRequest = PermitRequest::with(['user'])->where('account_number', $request->ticket_id)->where('facility_id', $request->facility_id)->whereNull("cancelled_at")->orderBy("id", "DESC")->first();
			//->whereDate('desired_end_date', '>=', date("Y-m-d H:i:s"))
			if ($permitRequest) {
				$this->log->info("exit ticketcheckicheckout nromal permit");
				if (strtotime($permitRequest->desired_start_date) <= strtotime(date("Y-m-d")) && strtotime($permitRequest->grace_end_date) >= strtotime(date("Y-m-d"))) {
					$ticket = Ticket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
					if (!$ticket) {
						if ($permitRequest->is_antipass_enabled == "1" || $permitRequest->is_antipass_enabled == 1) {
							$this->log->info("exit ticketcheckicheckout nromal permit is_antipass_enabled");
							$data['user_id'] = $permitRequest->user_id;
							$data['checkout_gate'] = $request->gate_id;
							$data['facility_id'] = $request->facility_id;
							$data['is_checkin'] = 0;
							$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
							//$data['checkin_time'] = date('Y-m-d H:i:s');
							//$data['check_in_datetime'] = date('Y-m-d H:i:s');
							//$data['checkout_datetime'] = $permitRequest->desired_end_date;
							$data['ticket_security_code'] = rand(1000, 9999);
							$data['partner_id'] = $facility->owner_id;
							//$data['total'] = $permitRequest->permit_rate;
							//$data['grand_total'] = $permitRequest->permit_rate;
							$data['permit_request_id'] = $permitRequest->id;
							$data['license_plate'] = $request->license_plate;
							$data['device_type'] = 'IM30';
							$data['is_checkout'] = '1';
							$data['checkout_time'] = date("Y-m-d H:i:s");
							$data['checkout_datetime'] = date("Y-m-d H:i:s");
							$data['checkout_license_plate'] = $request->license_plate;
							$data['checkout_mode'] = '3';
							$data['is_transaction_status'] = '0';
							$data['checkout_remark'] = 'Closed due to Anti passback';
							$result = Ticket::create($data);
							$ticket = $result;
							if ($result) {

								//update pass back status 0 default 1 for checkin 2 for checkout 3 for reset
								$permitRequest->passback_status = "2";
								$permitRequest->save();
							}
							if ($request->license_plate != '') {
								if ($facility->license_plate_model != '') {
									$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
									$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
								}
							}

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$user = $ticket->user;
							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
							/*
							$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
							if ($partnerDetails->user_id == self::PARTNER_ID) {
								$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
								$join->on('user_facilities.user_id', '=', 'users.id');
								$join->where('user_facilities.facility_id', "=", $facility->id);
								})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
								$name = ($getRM->slug) ? $getRM->slug : 'receipt';
							} else {
								$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
							}
							$url = env('RECEIPT_URL');
										$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
										//changes for breeze specific URL from DB
										if($dynamicReceiptUrl){
											$url = $dynamicReceiptUrl->value;
										}
							$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

							dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));*/
							$this->log->info("SMS sent, response sent");
							$data = ['msg' => $msg];
							$data['is_phone_linked'] = '0';
							$data['phone_linked_msg'] = '';
							$data["eticket_id"] =  $ticket->ticket_number;
							$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
							return $data;
						}
						throw new ApiGenericException('You have already checked-out.');
					}

					if ($request->license_plate == '') {
						$mappings = PermitVehicleMapping::where("permit_request_id", $permitRequest->id)->orderBy("id", "DESC")->get();
						if (count($mappings) > 1) {
							$vehicle = PermitVehicle::find($mappings[0]->permit_vehicle_id);
							if ($vehicle) {
								$request->request->add(['license_plate' => $vehicle->license_plate_number]);
							}
						} else {
							$vehicle = PermitVehicle::find($mappings[0]->permit_vehicle_id);
							if ($vehicle) {
								$request->request->add(['license_plate' => $vehicle->license_plate_number]);
							}
						}
					}

					$ticket->is_checkout = '1';
					$ticket->checkout_gate = $request->gate_id;
					$ticket->checkout_time = date("Y-m-d H:i:s");
					$ticket->checkout_datetime = date("Y-m-d H:i:s");
					$ticket->checkout_license_plate = $request->license_plate;
					$ticket->checkout_mode = '3';
					$ticket->is_transaction_status = '0';
					$ticket->save();

					if ($permitRequest->is_antipass_enabled == "1" || $permitRequest->is_antipass_enabled == 1) {
						if ($ticket) {
							$openTicket = Ticket::where("user_id", $permitRequest->user_id)->orderBy("id", "DESC")->first();
							if ($openTicket) {
								$openTicket->is_checkout = '1';
								$openTicket->checkout_gate = $request->gate_id;
								$openTicket->checkout_time = date("Y-m-d H:i:s");
								$openTicket->checkout_datetime = date("Y-m-d H:i:s");
								$openTicket->estimated_checkout = date("Y-m-d H:i:s");
								$openTicket->checkout_license_plate = $request->license_plate;
								$openTicket->checkout_mode = '3';
								$openTicket->is_transaction_status = '0';
								$openTicket->checkout_remark = 'Closed due to Anti passback';
								$openTicket->save();

								// $permitRequest->is_antipass_enabled = "0";
								// $permitRequest->save();
							}
						}
					}

					//update pass back status 0 default 1 for checkin 2 for checkout 3 for reset
					$permitRequest->passback_status = "2";
					$permitRequest->save();

					if ($request->license_plate != '') {
						if ($facility->license_plate_model != '') {
							$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
							$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
						}
					}

					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					$user = $ticket->user;
					$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
					$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					if ($partnerDetails->user_id == self::PARTNER_ID) {
						$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
							$join->on('user_facilities.user_id', '=', 'users.id');
							$join->where('user_facilities.facility_id', "=", $facility->id);
						})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
						$name = ($getRM->slug) ? $getRM->slug : 'receipt';
					} else {
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					}
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
					$this->log->info("SMS sent, response sent");
					$data = ['msg' => $msg];
					$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
					$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					$data["eticket_id"] =  $ticket->ticket_number;
					$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
					return $data;
				} else {
					throw new ApiGenericException('Your Permit is expired or canceled.');
				}
			}

			if (str_contains($request->ticket_id, 'http')) {
				$receiptUrl = env('RECEIPT_URL');
				$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
				//changes for breeze specific URL from DB
				if ($dynamicReceiptUrl) {
					$receiptUrl = $dynamicReceiptUrl->value;
				}
				if (str_contains($request->ticket_id, env('WEB_ADMIN_URL')) || str_contains($request->ticket_id, $receiptUrl)) {

					if (str_contains($request->ticket_id, env('WEB_ADMIN_URL'))) {
						$link = $request->ticket_id;
						$link_array = explode('=', $link);
						$ticket_id = end($link_array);
					} else if (str_contains($request->ticket_id, $receiptUrl)) {
						$link = $request->ticket_id;
						$link_array = explode('/', $link);
						$ticket_id = end($link_array);
					} else {
						throw new ApiGenericException('Please scan valid QR code.');
					}

					$ticket = Ticket::with(['user'])->where('ticket_number', $ticket_id)->where('facility_id', $request->facility_id)->where('is_checkout', '0')->first();
					if (!$ticket) {
						throw new ApiGenericException('No checkin found against this booking.');
					}
					$user = $ticket->user;

					//overstay case
					if ($ticket->estimated_checkout != '') {
						$this->log->info("eticket already paid.");
						$overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();

						if ($overstayExist) {
							if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

								$ticket->is_checkout = '1';
								$ticket->checkout_gate = $request->gate_id;
								$ticket->checkout_time = date("Y-m-d H:i:s");
								$ticket->checkout_datetime = date("Y-m-d H:i:s");
								$ticket->checkout_license_plate = $request->license_plate;
								$ticket->checkout_mode = '3';
								$ticket->is_transaction_status = '0';
								$ticket->save();

								if (isset($ticket->reservation->id)) {
									$ticket->reservation->is_ticket = '2';
									$ticket->reservation->save();
								}

								if ($request->license_plate != '') {
									if ($facility->license_plate_model != '') {
										$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
										$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
									}
								}

								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
								$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

								$data = ['msg' => $msg];
								$data['is_phone_linked'] = '0';
								$data['phone_linked_msg'] = '';
								if (isset($user->phone)) {
									$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
									if ($partnerDetails->user_id == self::PARTNER_ID) {
										$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
											$join->on('user_facilities.user_id', '=', 'users.id');
											$join->where('user_facilities.facility_id', "=", $facility->id);
										})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
										$name = ($getRM->slug) ? $getRM->slug : 'receipt';
									} else {
										$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
									}
									$url = env('RECEIPT_URL');
									$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
									//changes for breeze specific URL from DB
									if ($dynamicReceiptUrl) {
										$url = $dynamicReceiptUrl->value;
									}
									$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

									dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
									$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
									$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
								}

								$this->log->info("SMS sent, response sent");
								return $data;
							}
							$this->log->info("eticket overstay created.");
							$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->estimated_checkout);
							$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
							$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->estimated_checkout);
							/** this function is used to get Availability Information for respective facility **/
							$isMember = 0;

							$this->log->info("get Diff in Hours : {$diff_in_hours}");
							$rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
							$this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

							$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
							$data['msg'] = "Amount Due";
							$data['price'] =  $rate['price'];
							$data['eticket_id'] =  $ticket->ticket_number;
							$data['is_overstay'] =  '1';
							return $data;
						} else {
							if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

								$ticket->is_checkout = '1';
								$ticket->checkout_gate = $request->gate_id;
								$ticket->checkout_time = date("Y-m-d H:i:s");
								$ticket->checkout_datetime = date("Y-m-d H:i:s");
								$ticket->checkout_license_plate = $request->license_plate;
								$ticket->checkout_mode = '3';
								$ticket->is_transaction_status = '0';
								$ticket->save();

								if (isset($ticket->reservation->id)) {
									$ticket->reservation->is_ticket = '2';
									$ticket->reservation->save();
								}

								if ($request->license_plate != '') {
									if ($facility->license_plate_model != '') {
										$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
										$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
									}
								}

								//check gate api
								if ($facility->open_gate_enabled == '1') {
									$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
									if ($gateStatus == "true") {
									} else {
										throw new ApiGenericException($gateStatus);
									}
								}

								$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
								$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

								$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
								if ($partnerDetails->user_id == self::PARTNER_ID) {
									$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
										$join->on('user_facilities.user_id', '=', 'users.id');
										$join->where('user_facilities.facility_id', "=", $facility->id);
									})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
									$name = ($getRM->slug) ? $getRM->slug : 'receipt';
								} else {
									$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
								}
								$url = env('RECEIPT_URL');
								$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
								//changes for breeze specific URL from DB
								if ($dynamicReceiptUrl) {
									$url = $dynamicReceiptUrl->value;
								}
								$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

								$data['is_phone_linked'] = '0';
								$data['phone_linked_msg'] = '';
								if (isset($user->phone)) {
									$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
									$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
									dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
								}

								$this->log->info("SMS sent, response sent");
								$data = ['msg' => $msg];
								return $data;
							}
							$this->log->info("overstay created.");
							$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout);
							$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
							$diff_in_hours = $ticket->getCheckOutCurrentTime(true, ($ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout));
							/** this function is used to get Availability Information for respective facility **/
							$isMember = 0;

							$this->log->info("get Diff in Hours : {$diff_in_hours}");
							$rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
							$this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");


							if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
								$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
								$rate['description'] = self::HOURLY_RATE;
							} else {
								$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, $isMember);
								if (!isset($rate['description'])) {
									$rate['description'] = self::BASE_RATE;
								}
							}
							$data['msg'] = "Amount Due";
							$data['price'] =  $rate['price'];
							$data['eticket_id'] =  $ticket->ticket_number;
							$data['is_overstay'] =  '1';
							$ticket->is_transaction_status = '1';
							$ticket->save();
							return $data;
						}
					}

					$arrival_time = date("Y-m-d H:i:s", strtotime($ticket->checkin_time));
					$parkingNowTime = date("Y-m-d H:i:s");

					$diff_in_hours = $ticket->getCheckOutCurrentTime(true);

					$isMember = 0;
					if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
						$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
						$rate['description'] = self::HOURLY_RATE;
					} else {
						$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
						if (!isset($rate['description'])) {
							$rate['description'] = self::BASE_RATE;
						}
					}

					$rateId = isset($rate['id']) ? $rate['id'] : '';
					$rateDescription = isset($rate['description']) ? $rate['description'] : '';
					$priceBreakUp = $ticket->priceBreakUp($rate);
					$this->log->info("eticket priceBreakUp " . json_encode($priceBreakUp));
					if ($priceBreakUp['payable_amount'] > 0) {

						if ($facility->is_cloud_payment_enabled != '1' || $facility->is_cloud_payment_enabled != 1) {

							$ticket->processing_fee  = $priceBreakUp['processing_fee'];
							$ticket->tax_fee        = $priceBreakUp['tax_rate'];
							$ticket->parking_amount  = $priceBreakUp['parking_amount'];
							$ticket->paid_amount     = $priceBreakUp['paid_amount'];
							$ticket->discount_amount = $priceBreakUp['discount_amount'];
							$ticket->grand_total     = $priceBreakUp['payable_amount'];
							$ticket->total     = $priceBreakUp['total'];
							$ticket->length     = $diff_in_hours;
							$ticket->is_transaction_status = '1';
							$ticket->save();
							$data['msg'] = "Amount Due";
							$data['price'] =  $priceBreakUp['payable_amount'];
							$data['eticket_id'] =  $ticket->ticket_number;
							$this->log->info("price sent, response sent");
							return $data;
						}

						$refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $priceBreakUp['payable_amount'], $request);
						$this->log->info("Payment Response :" . json_encode($refundstatus));
						if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
							throw new ApiGenericException("Payment failed. Please try again");
						}
						$planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
						$ticket->anet_transaction_id = $planetTransaction->id;
					}

					if ($ticket->is_offline_payment == '0' && $ticket->paid_type == '9') {
						if ($priceBreakUp['total'] > 0) {
							$result['msg'] = "Amount Due";
							$result['price'] =  $priceBreakUp['total'];
							$result['eticket_id'] =  $ticket->ticket_number;
							$ticket->is_transaction_status = '1';
							$ticket->processing_fee  = $priceBreakUp['processing_fee'];
							$ticket->tax_fee        = $priceBreakUp['tax_rate'];
							$ticket->parking_amount  = $priceBreakUp['parking_amount'];
							$ticket->paid_amount     = $priceBreakUp['paid_amount'];
							$ticket->discount_amount = $priceBreakUp['discount_amount'];
							$ticket->grand_total     = $priceBreakUp['total'];
							$ticket->total     = $priceBreakUp['total'];
							$ticket->length     = $diff_in_hours;
							$ticket->rate_id = $rateId;
							$ticket->rate_description = $rateDescription;
							$ticket->save();
							$this->log->info("eticket if payment declined and hit api again, price sent, response sent");
							return $result;
						}
					}

					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					$ticket->processing_fee  = $priceBreakUp['processing_fee'];
					$ticket->tax_fee        = $priceBreakUp['tax_rate'];
					$ticket->parking_amount  = $priceBreakUp['parking_amount'];
					$ticket->paid_amount     = $priceBreakUp['paid_amount'];
					$ticket->discount_amount = $priceBreakUp['discount_amount'];
					$ticket->grand_total     = $priceBreakUp['payable_amount'];
					$ticket->total     = $priceBreakUp['total'];
					$ticket->length     = $diff_in_hours;

					$ticket->checkout_mode = '3';
					$ticket->is_checkout = '1';
					$ticket->is_checkin = '1';
					$ticket->checkout_gate = $request->gate_id;
					$ticket->checkout_time = date("Y-m-d H:i:s");
					$ticket->checkout_datetime = date("Y-m-d H:i:s");
					$ticket->is_cloud_payment = '1';
					$ticket->checkout_license_plate = $request->license_plate;
					$ticket->rate_id = $rateId;
					$ticket->rate_description = $rateDescription;
					$ticket->is_transaction_status = '0';
					$ticket->save();

					if ($request->license_plate != '') {
						if ($facility->license_plate_model != '') {
							$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
							$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
						}
					}

					//$this->saveTransactionData($rate, $ticket);

					$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
					$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
					if (isset($user->phone)) {
						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
					}
					$this->log->info("SMS sent, response sent");
					$data = ['msg' => $msg];
					$data['is_phone_linked'] = isset($user->phone) ? '1' : '0';
					$data['phone_linked_msg'] = isset($user->phone) != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';

					$this->log->info("User checkout, response sent");
					return $data;
				} else {
					throw new ApiGenericException('Please scan valid QR code.');
				}
			}


			$qrCode = MapcoQrcode::with(['reservation.user', 'event', 'eventCategory.eventCategoryEvent.event'])->where("qrcode", $request->ticket_id)->first();

			if ($qrCode) {
				$this->log->info("exit qrCode entered --" . $qrCode->id);

				if (!isset($qrCode->reservation)) {
					throw new ApiGenericException('No prepaid booking found.');
				}
				if ($qrCode->reservation->cancelled_at != '') {
					throw new ApiGenericException('Your booking has been canceled.');
				}

				$ticket = Ticket::with(['user'])->where("reservation_id", $qrCode->reservation->id)->where("event_id", $qrCode->event_id)->orderBy("id", "DESC")->where("is_checkout", "0")->first();

				if (!$ticket) {
					throw new ApiGenericException('You have already checked-out.');
				}
				$user = $ticket->user;
				//overstay condition

				$this->log->info("qrcode event overstay condtion.");
				$overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();

				if ($overstayExist) {
					if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

						$ticket->is_checkout = '1';
						$ticket->checkout_gate = $request->gate_id;
						$ticket->checkout_time = date("Y-m-d H:i:s");
						$ticket->checkout_datetime = date("Y-m-d H:i:s");
						$ticket->checkout_license_plate = $request->license_plate;
						$ticket->checkout_mode = '3';
						$ticket->is_transaction_status = '0';
						$ticket->save();

						if ($request->license_plate != '') {
							if ($facility->license_plate_model != '') {
								$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
								$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
							}
						}

						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
						$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						if ($partnerDetails->user_id == self::PARTNER_ID) {
							$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
								$join->on('user_facilities.user_id', '=', 'users.id');
								$join->where('user_facilities.facility_id', "=", $facility->id);
							})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
							$name = ($getRM->slug) ? $getRM->slug : 'receipt';
						} else {
							$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						}
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
						$this->log->info("SMS sent, response sent");
						$data = ['msg' => $msg];
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						return $data;
					}
					$this->log->info("qrcode event overstay created.");
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->payment_date);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
					$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->payment_date);
					/** this function is used to get Availability Information for respective facility **/
					$isMember = 0;

					if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
						$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
					} else {
						$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
					}

					$taxRate = $ticket->getTaxRate($rate);          // to get tax price
					$data['msg'] = "Amount Due";
					$data['amount'] =  $rate['price'] + $taxRate;
					$data['eticket_id'] =  $ticket->ticket_number;
					$data['is_overstay'] =  '1';
					$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
					$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
					$ticket->is_transaction_status = '0';
					$ticket->save();
					return $data;
				} else {
					if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

						$ticket->is_checkout = '1';
						$ticket->checkout_gate = $request->gate_id;
						$ticket->checkout_time = date("Y-m-d H:i:s");
						//$ticket->checkout_datetime = date("Y-m-d H:i:s");
						$ticket->checkout_license_plate = $request->license_plate;
						$ticket->checkout_mode = '3';
						$ticket->is_transaction_status = '0';
						$ticket->save();

						if ($request->license_plate != '') {
							if ($facility->license_plate_model != '') {
								$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
								$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
							}
						}

						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
						$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						if ($partnerDetails->user_id == self::PARTNER_ID) {
							$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
								$join->on('user_facilities.user_id', '=', 'users.id');
								$join->where('user_facilities.facility_id', "=", $facility->id);
							})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
							$name = ($getRM->slug) ? $getRM->slug : 'receipt';
						} else {
							$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						}
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
						$this->log->info("SMS sent, response sent");
						$data = ['msg' => $msg];
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						return $data;
					}
					$this->log->info("qrcode event 2 overstay created.");
					$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout);
					$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
					$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->estimated_checkout);
					/** this function is used to get Availability Information for respective facility **/
					$isMember = 0;
					if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
						$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
					} else {
						$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
					}
					$taxRate = $ticket->getTaxRate($rate);          // to get tax price
					$data['msg'] = "Amount Due";
					$data['amount'] =  $rate['price'] + $taxRate;
					$data['eticket_id'] =  $ticket->ticket_number;
					$data['is_overstay'] =  '1';
					$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
					$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
					$ticket->is_transaction_status = '0';
					$ticket->save();
					return $data;
				}

				$facilityName = ucwords($facility->full_name);
				$ticket->is_checkout = '1';
				$ticket->checkout_time = date('Y-m-d H:i:s');
				$ticket->checkout_gate = $request->gate_id;
				$ticket->save();

				if ($reservation) {
					$reservation->is_ticket = '2';
					$reservation->save();
				}
				//$msg = "Thank you for visiting ".$facilityName.".";
				//dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}


				$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
				$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
				$url = env('RECEIPT_URL');
				$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
				//changes for breeze specific URL from DB
				if ($dynamicReceiptUrl) {
					$url = $dynamicReceiptUrl->value;
				}
				$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

				dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				$this->log->info("SMS sent, driveup response sent");

				$msg = "THANK YOU FOR VISITING. #" . $ticket->ticket_number;
				$data = ['msg' => $msg];
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				$data['booking_type'] = 'reservation';
				$data['is_check_in_ontime'] = '1';
				return $data;
			}



			if ($userQrCode) {
				return $this->userCheckoutByQrCode($userQrCode, $request, $facility);
			}

			//  $reservation = Reservation::with('user')->where("facility_id", $request->facility_id)->where("ticketech_code", $request->ticket_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->where('is_ticket', '0')->first();
			$reservation = Reservation::with('user')->where("ticketech_code", $request->ticket_id)->whereNull('cancelled_at')->first();
			if (!$reservation) {

				$userPass = UserPass::with('user')->where("pass_code", $request->ticket_id)->first();

				if (!$userPass) {
					throw new ApiGenericException('No prepaid booking or pass found.');
				}
				$user = $userPass->user;
				$ticket = Ticket::where("user_pass_id", $userPass->id)->orderBy("id", "DESC")->first();

				if (!$ticket) {
					throw new ApiGenericException('No checkin found.');
				}

				if ($ticket->is_checkout == '1') {
					throw new ApiGenericException('You have already checked-out.');
				}

				$ticket->is_checkout = '1';
				$ticket->checkout_gate = $request->gate_id;
				$ticket->checkout_time = date("Y-m-d H:i:s");
				//$ticket->checkout_datetime = date("Y-m-d H:i:s");
				$ticket->checkout_license_plate = $request->license_plate;
				$ticket->checkout_mode = '3';
				$ticket->is_transaction_status = '0';
				$ticket->save();

				if ($request->license_plate != '') {
					if ($facility->license_plate_model != '') {
						$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
						$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
					}
				}

				//check gate api
				if ($facility->open_gate_enabled == '1') {
					$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
					if ($gateStatus == "true") {
					} else {
						throw new ApiGenericException($gateStatus);
					}
				}

				$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
				$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

				$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
				if ($partnerDetails->user_id == self::PARTNER_ID) {
					$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
						$join->on('user_facilities.user_id', '=', 'users.id');
						$join->where('user_facilities.facility_id', "=", $facility->id);
					})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
					$name = ($getRM->slug) ? $getRM->slug : 'receipt';
				} else {
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
				}
				$url = env('RECEIPT_URL');
				$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
				//changes for breeze specific URL from DB
				if ($dynamicReceiptUrl) {
					$url = $dynamicReceiptUrl->value;
				}
				$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

				dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
				$this->log->info("SMS sent, response sent");
				$data = ['msg' => $msg];
				$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
				$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
				return $data;
			} else {
				if (!in_array($reservation->facility_id, $facilityArray)) {
					throw new ApiGenericException('Facility not in the same RM');
				}
				if ($facility->facility_booking_type == '1') {
					throw new ApiGenericException('Prepaid booking is not allowed.');
				}
				if ($reservation->is_ticket == '2' || $reservation->is_ticket == '0') {
					throw new ApiGenericException('No prepaid booking or pass found.');
				}

				$user = isset($reservation->user) ? $reservation->user : [];
				$ticket = Ticket::where('reservation_id', $reservation->id)->orderBy("id", "DESC")->first();
				if (!$ticket) {
					throw new ApiGenericException('No checkin found against this booking.');
				} else if ($ticket->facility_id != $request->facility_id) { // checked same facility checkout (Ashutosh 20-09-2023)
					throw new ApiGenericException('Invalid Facility');
				} else if ($ticket->is_checkout == '0') {
					if ($facility->facility_booking_type == '1') {
						throw new ApiGenericException('Prepaid booking is not allowed.');
					}



					//overstay condition

					$this->log->info("ticketcheckincheckout overstay condtion.");
					$overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();

					if ($overstayExist) {
						if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

							$ticket->is_checkout = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->checkout_license_plate = $request->license_plate;
							$ticket->checkout_mode = '3';
							$ticket->is_transaction_status = '0';
							$ticket->save();

							if ($request->license_plate != '') {
								if ($facility->license_plate_model != '') {
									$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
									$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
								}
							}

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

							$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
							if ($partnerDetails->user_id == self::PARTNER_ID) {
								$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
									$join->on('user_facilities.user_id', '=', 'users.id');
									$join->where('user_facilities.facility_id', "=", $facility->id);
								})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
								$name = ($getRM->slug) ? $getRM->slug : 'receipt';
							} else {
								$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
							}
							$url = env('RECEIPT_URL');
							$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
							//changes for breeze specific URL from DB
							if ($dynamicReceiptUrl) {
								$url = $dynamicReceiptUrl->value;
							}
							$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

							dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
							$this->log->info("SMS sent, response sent");
							$data = ['msg' => $msg];
							$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
							$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
							return $data;
						}
						$this->log->info("overstay created.");
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->payment_date);
						$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
						$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->payment_date);
						/** this function is used to get Availability Information for respective facility **/
						$isMember = 0;

						if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
							$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
						} else {
							$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
						}

						$taxRate = $ticket->getTaxRate($rate);          // to get tax price
						$data['msg'] = "Amount Due";
						$data['amount'] =  $rate['price'] + $taxRate;
						$data['eticket_id'] =  $ticket->ticket_number;
						$data['is_overstay'] =  '1';
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
						$ticket->is_transaction_status = '0';
						$ticket->save();
						return $data;
					} else {
						if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {

							$ticket->is_checkout = '1';
							$ticket->checkout_gate = $request->gate_id;
							$ticket->checkout_time = date("Y-m-d H:i:s");
							//$ticket->checkout_datetime = date("Y-m-d H:i:s");
							$ticket->checkout_license_plate = $request->license_plate;
							$ticket->checkout_mode = '3';
							$ticket->is_transaction_status = '0';
							$ticket->save();

							if ($reservation) {
								$reservation->is_ticket = '2';
								$reservation->save();
							}

							if ($request->license_plate != '') {
								if ($facility->license_plate_model != '') {
									$NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
									$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
								}
							}

							//check gate api
							if ($facility->open_gate_enabled == '1') {
								$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
								if ($gateStatus == "true") {
								} else {
									throw new ApiGenericException($gateStatus);
								}
							}

							$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
							$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

							$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
							if ($partnerDetails->user_id == self::PARTNER_ID) {
								$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
									$join->on('user_facilities.user_id', '=', 'users.id');
									$join->where('user_facilities.facility_id', "=", $facility->id);
								})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
								$name = ($getRM->slug) ? $getRM->slug : 'receipt';
							} else {
								$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
							}
							$url = env('RECEIPT_URL');
							$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
							//changes for breeze specific URL from DB
							if ($dynamicReceiptUrl) {
								$url = $dynamicReceiptUrl->value;
							}
							$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

							$this->log->info("SMS sent, response sent");
							if (isset($user->id)) {
								dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
								$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
								$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
							}
							$data = ['msg' => $msg];

							return $data;
						}
						$this->log->info("overstay created.");
						$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout);
						$from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
						$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->estimated_checkout);
						/** this function is used to get Availability Information for respective facility **/
						$isMember = 0;
						if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
							$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
						} else {
							$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
						}
						$taxRate = $ticket->getTaxRate($rate);          // to get tax price
						$data['msg'] = "Amount Due";
						$data['amount'] =  $rate['price'] + $taxRate;
						$data['eticket_id'] =  $ticket->ticket_number;
						$data['is_overstay'] =  '1';
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						$data['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
						$ticket->is_transaction_status = '0';
						$ticket->save();
						return $data;
					}




					$facilityName = ucwords($facility->full_name);
					$ticket->is_checkout = '1';
					$ticket->checkout_time = date('Y-m-d H:i:s');
					$ticket->checkout_gate = $request->gate_id;
					$ticket->save();

					if ($reservation) {
						$reservation->is_ticket = '2';
						$reservation->save();
					}
					//$msg = "Thank you for visiting ".$facilityName.".";
					//dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
					//check gate api
					if ($facility->open_gate_enabled == '1') {
						$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
						if ($gateStatus == "true") {
						} else {
							throw new ApiGenericException($gateStatus);
						}
					}

					// send Notification 
					$deviceToken =  Devicetoken::where('user_id', $user->id)->first();
					if ($deviceToken) {
						$updateJobParams = ['user_id' => $ticket->user_id, 'ticket_number' => $ticket->ticket_number, 'notification_type' => self::RES_CHECKOUT_NOTIFICATION];
						Artisan::queue('customer:checkout-pushnotification', $updateJobParams);
					}

					$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
					$url = env('RECEIPT_URL');
					$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
					//changes for breeze specific URL from DB
					if ($dynamicReceiptUrl) {
						$url = $dynamicReceiptUrl->value;
					}
					$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

					dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
					$this->log->info("SMS sent, driveup response sent");

					$msg = "THANK YOU FOR VISITING. #" . $ticket->ticket_number;
					$data = ['msg' => $msg];
					$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
					$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
					$data['booking_type'] = 'reservation';
					$data['is_check_in_ontime'] = '1';
					return $data;
					//return $msg;
				} else {
					$msg = "No prepaid booking or pass found.";
					if (isset($user->id)) {
						dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
					}
					throw new ApiGenericException('No prepaid booking or pass found.');
				}
				return "Success";
			}/*else{
        throw new ApiGenericException("Invalid request.");
         }*/
			return "Success";
		}
		return "Success";
	}

	// save Declined Trans
	public function saveDeclinedTransaction(Request $request)
	{
		//return $request->session_id;
		$this->log->info("Request received for payment declined --" . json_encode($request));
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}
		$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
		if (!$user) {
			throw new ApiGenericException("User Not Found.");
		}
		if (isset($request->payment_details)) {
			$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
			$this->log->info("Decline payment details request --" . json_encode($request->payment_details));
			$authorized_anet_transaction = new AuthorizeNetTransaction();
			$authorized_anet_transaction->sent = '1';
			$authorized_anet_transaction->user_id = $user->id;
			$authorized_anet_transaction->total = isset($request->payment_details['TransactionAmount']) ? $request->payment_details['TransactionAmount'] : '';
			$authorized_anet_transaction->name = isset($request->payment_details['MerchantName']) ? $request->payment_details['MerchantName'] : '';
			$authorized_anet_transaction->description = "Payment declined";
			$authorized_anet_transaction->response_message = isset($request->payment_details['ProcessorMessage']) ? $request->payment_details['ProcessorMessage'] : '';
			$authorized_anet_transaction->expiration = isset($request->payment_details['expiry']) ? $request->payment_details['expiry'] : '';
			$authorized_anet_transaction->card_type = isset($request->payment_details['CardType']) ? $request->payment_details['CardType'] : '';
			$authorized_anet_transaction->anet_trans_hash = $request->payment_details['processorReference'];
			$authorized_anet_transaction->ref_id = $request->payment_details['RequesterTransRefNum'];
			$authorized_anet_transaction->anet_trans_id = isset($request->payment_details['TransactionID']) ? $request->payment_details['TransactionID'] : '';
			$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
			$authorized_anet_transaction->method = "card";
			$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
			$authorized_anet_transaction->status_code = $request->payment_details['StatusCode'];
			$authorized_anet_transaction->status_type = $request->payment_details['StatusType'];
			$authorized_anet_transaction->status_message = $request->payment_details['StatusMessage'];
			$authorized_anet_transaction->name = $request->payment_details['CardHolderName'];
			$authorized_anet_transaction->save();
			return "Payment declined data save successfull !";
		} else {
			throw new ApiGenericException("Payment Details Not Found.");
		}
		$this->log->info("Decline payment transaction done --" . json_encode($result));
	}




	protected function saveOverstayTicketDetails($ticket, $rate, $length)
	{

		$OverstayTicket = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();
		$estimatedCheckout = isset($OverstayTicket->estimated_checkout) ? $OverstayTicket->estimated_checkout : $ticket->estimated_checkout;
		$estimatedCheckout = Carbon::parse($estimatedCheckout)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
		$lengthInHours = $length;
		if ($OverstayTicket) {
			$lengthInOldFormat = $ticket->getCheckOutCurrentTime(true, $estimatedCheckout);
			$lengthInHours = QueryBuilder::getLengthInHours($lengthInOldFormat);  // now convert in hours format 
		}

		$overstay = new OverstayTicket();
		$overstay->user_id = $ticket->user_id > 0 ? $ticket->user_id : 0;
		$overstay->facility_id = $ticket->facility_id;
		$overstay->length = $lengthInHours;
		$overstay->total = $rate;
		$overstay->grand_total = $rate;
		$overstay->discount_amount = "0.00";
		$overstay->ticket_number = $ticket->ticket_number;
		$overstay->ticket_id = $ticket->id;
		$overstay->is_checkin = '1';
		$overstay->check_in_datetime = date('Y-m-d H:i:s', strtotime($estimatedCheckout));
		$overstay->comment = "";
		$overstay->save();
		return $overstay;
	}

	//Save Transaction Data for Report
	protected function saveOverstayTransactionData($ticket, $overstayTicket, $facility)
	{
		$arrival_time = $ticket->checkin_time;
		$diff_in_hours = $ticket->length;
		$isMember = 0;
		if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
			$rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
		} else {
			$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
		}

		$rate_id = isset($rate['id']) ? $rate['id'] : '';
		$rate_description = isset($rate['description']) ? $rate['description'] : '';
		$rate_amount = $rate['price'];

		$transaction = TransactionData::where("ticket_id", $ticket->id)->first();
		if (!$transaction) {
			return true;
		}

		$transaction->rate_id = $rate_id;
		$transaction->rate_description = $rate_description;
		$transaction->rate_amount = $rate['price'];
		$transaction->total = $ticket->total + $overstayTicket->total;
		$transaction->grand_total = $ticket->grand_total + $overstayTicket->grand_total;
		$transaction->save();
		return $transaction;
	}


	protected function checkPermitTicketNumber()
	{
		$ticket = 'SC' . rand(100, 999) . rand(100, 999);
		$isExist = PermitTicket::where('ticket_number', $ticket)->first();
		if ($isExist) {
			$this->checkPermitTicketNumber();
		}
		return $ticket;
	}


	public function lprCheckinCheckout(Request $request)
	{


		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('Invalid partner.');
			}
		}
		//$this->validate($request, ['image' => 'mimes:jpeg,png,jpg,svg'], ['image.mimes' => 'vehicle image must be a file of type : .jpeg, .png, .jpg' ]);    

		$image = $request->file('image');
		if ($image != '') {
			$file_extension = $image->getClientOriginalExtension();
			$file_name =  $request->license_plate . '_' . rand(1001, 9999) . '.' . $file_extension;
			$destination_path = storage_path("app/license-plate");
			$data['image'] = $file_name;
			if (!$image->move($destination_path, $file_name)) {
				throw new ApiGenericException("Something went wrong while upload image.");
			}
		}
		$data['license_plate'] = $request->license_plate;
		$data['partner_id'] = $secret->partner_id;
		$data['make'] = $request->make;
		$data['model'] = $request->model;
		$data['year'] = $request->year;
		$data['color'] = $request->color;
		$data['facility_id'] = $request->facility_id;
		$data['gate'] = $request->gate_id;

		$licensePlate = WorldportLicensePlate::create($data);

		if ($licensePlate) {
			$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
			$checkinData = [];
			if ($gate && isset($gate->gate_type)) {
				if ($gate->gate_type == 'entry') {
					return "License plate successfully updated.";
				} else if ($gate->gate_type == 'exit') {
					$this->checkoutByLicensePlate($request, $licensePlate, $gate);
				} else {
					throw new ApiGenericException("Invalid gate.");
				}
			}
		} else {
			throw new ApiGenericException("Something went wrong while upload image.");
		}
	}


	public function checkoutByLicensePlate($request, $licensePlate, $gate)
	{
		//check gate api
		$facility = Facility::find($request->facility_id);
		$partner_id = $request->partner_id;
		if ($facility->check_vehicle_enabled == '1') {
			$gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}
		}

		$permitVehicles = PermitVehicle::where("license_plate_number", $request->license_plate)->where("partner_id", $partner_id)->get();
		if (count($permitVehicles) > 0) {
			foreach ($permitVehicles as $key => $permitVehicle) {
				$permitRequests = PermitRequest::where('user_id', $permitVehicle->user_id)->where('facility_id', $request->facility_id)->whereDate('grace_end_date', '>=', date("Y-m-d H:i:s"))->whereNull("cancelled_at")->orderBy("id", "DESC")->get();
				if (count($permitRequests) > 0) {
					foreach ($permitRequests as $key => $permitRequest) {
						$permitTicket = Ticket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
						if (!$permitTicket) {
							continue;
						}

						$mapping = PermitVehicleMapping::where("permit_vehicle_id", $permitVehicle->id)->where("permit_request_id", $permitRequest->id)->first();
						if (!$mapping) {
							continue;
						}

						$facilityName = ucwords($facility->full_name);
						$permitTicket->is_checkout = '1';
						$permitTicket->checkout_time = date('Y-m-d H:i:s');
						$permitTicket->checkout_datetime = date('Y-m-d H:i:s');
						$permitTicket->checkout_gate = $request->gate_id;
						$permitTicket->save();

						//Artisan::queue('customer:checkout-pushnotification',array('user_id'=>$ticket->user_id,'ticket_number'=>$ticket->ticket_number));

						//$msg = "Thank you for visiting ".$facilityName.". #".$permitTicket->ticket_number;
						$msg = "Thank you for visiting ";
						//dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}
						$data = [
							'msg' => $msg,
							'booking_type' => "permit"
						];

						return $data;
					}
				}
			}
		}

		$ticket = Ticket::with(['reservation', 'user'])->where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->where('is_checkout', '0')->orderBy("id", "DESC")->first();

		if (!$ticket) {
			throw new ApiGenericException('Sorry! No checkin found against this card.');
		} else {
			$user = $ticket->user;
			$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
			$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
			$diff_in_hours = $arrival_time->diffInRealHours($from);
			$diff_in_mins = $arrival_time->diffInRealMinutes($from);
			$diff_in_secs = $arrival_time->diffInSeconds($from);

			if ($diff_in_mins < 60) {
				$diff_in_hours = $diff_in_mins / 100;
			}
			if ($diff_in_mins > 59) {
				if ($diff_in_hours > 0) {
					$diffInMints = $diff_in_mins - ($diff_in_hours * 60);
					$diff_in_hours = $diff_in_hours . '.' . $diffInMints;
				} else {
					$diff_in_hours = number_format($diff_in_mins / 60, 2);
				}
			}
			if ($diff_in_secs < 60) {
				$diff_in_hours = .01;
			}

			$request->request->add(['diff_in_hours' => $diff_in_hours]);

			if ($ticket->reservation_id == '') {
				if ($ticket->anet_transaction_id == '' && $ticket->paid_by == '') {
					throw new ApiGenericException('Sorry! No prepaid booking found against this card.');
				} elseif ($ticket->anet_transaction_id != '') {
					$this->checkoutWithSMS($request, $ticket, $facility, $user);
				} elseif ($ticket->paid_by != '') {
					$isMember = 0;
					$rate = $facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);

					$parkingAmount = $rate['price'];
					$rateId = isset($rate['id']) ? $rate['id'] : '';
					if ($rate['price'] > 0) {
						$rate['price'] = $rate['price'] + $facility->processing_fee + $facility->tax_rate;
					} else {
						$rate['price'] = "0.00";
					}

					if ($ticket->paid_type == '1' || $ticket->paid_type == 1) {
						if ($ticket->paid_hour != '') {
							if ($diff_in_hours <= $ticket->paid_hour) {
								$rate['price'] = "0.00";
							} else {
								throw new ApiGenericException('Sorry! No prepaid booking found against this card.');
							}
						}
					}

					if ($ticket->paid_type == '0' || $ticket->paid_type == 0) {
						$updatedPaidAmount = $rate['price'];
						$rate['price'] = "0";
					}

					if ($ticket->paid_type == '2' || $ticket->paid_type == 2) {
						if ($rate['price'] <= 0 || $rate['price'] <= 0.00) {
							$rate['price'] = "0";
						} else {
							if ($rate['price'] <= $ticket->paid_amount) {
								$rate['price'] = "0";
							} else {
								throw new ApiGenericException('Sorry! No prepaid booking found against this card.');
							}
							$updatedPaidAmount = 0;
						}
					}
					if ($ticket->paid_type == '3' || $ticket->paid_type == 3) {
						$percentageAmount = number_format((($rate['price'] * $ticket->paid_percentage) / 100), 2);
						if (($ticket->max_validated_amount > 0) && $ticket->max_validated_amount != '') {
							if ($percentageAmount <= $ticket->max_validated_amount) {
								$rate['price'] = number_format($rate['price'] - $percentageAmount, 2);
								$updatedPaidAmount = $percentageAmount;
							} else {
								$rate['price'] = number_format($rate['price'] - $ticket->max_validated_amount, 2);
								$updatedPaidAmount = $ticket->max_validated_amount;
							}
						} else {
							$rate['price'] = number_format($rate['price'] - $percentageAmount, 2);
							$updatedPaidAmount = $percentageAmount;
						}

						if ($rate['price'] > 0) {
							throw new ApiGenericException('Sorry! No prepaid booking found against this card.');
						}
					}

					if ($rate['price'] == '0' || $rate['price'] == 0) {
						$ticket->is_checkout = '1';
						$ticket->checkout_gate = $request->gate_id;
						$ticket->checkout_time = date("Y-m-d H:i:s");
						$ticket->checkout_datetime = date("Y-m-d H:i:s");
						$ticket->total = 0.00;
						$ticket->grand_total = 0.00;
						$ticket->length = $diff_in_hours;
						if ($updatedPaidAmount > 0) {
							$ticket->paid_amount = $updatedPaidAmount;
						}
						$ticket->parking_amount = $parkingAmount;
						$ticket->rate_description = $rateId;
						$ticket->save();

						//check gate api
						if ($facility->open_gate_enabled == '1') {
							$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
							if ($gateStatus == "true") {
							} else {
								throw new ApiGenericException($gateStatus);
							}
						}

						$facilityName = isset($facility->full_name) ? ucwords($facility->full_name) : '';
						$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";

						$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
						$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
						$url = env('RECEIPT_URL');
						$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
						//changes for breeze specific URL from DB
						if ($dynamicReceiptUrl) {
							$url = $dynamicReceiptUrl->value;
						}
						$sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

						dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
						$this->log->info("SMS sent, response sent");
						$data = ['msg' => $msg];
						$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
						$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
						return $data;
					}
				}
				$this->checkoutWithSMS($request, $ticket, $facility, $user);
			} else {
				throw new ApiGenericException('Something wrong.');
			}
		}
	}



	public function checkoutWithSMS($request, $ticket, $facility, $user)
	{

		$ticket->checkout_gate = $request->gate_id;
		$ticket->is_checkout = 1;
		$ticket->length = $request->diff_in_hours;
		$ticket->checkout_time = date('Y-m-d H:i:s');

		$result = $ticket->save();

		if (isset($ticket->reservation->is_ticket)) {
			$ticket->reservation->is_ticket = '2';
			$ticket->reservation->save();
		}

		$facilityName =  ucwords($facility->full_name);

		$this->log->info("checkout LPR done --" . json_encode($ticket));
		if ($result) {
			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			//return "Thank you for visiting ".$facilityName.".";
			$msg = "THANK YOU FOR VISITING. #$ticket->ticket_number";
			$data = ['msg' => $msg];
			$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
			$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
			$data['booking_type'] = 'reservation';
			$data['is_check_in_ontime'] = '1';

			if ($user->phone != '') {
				$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
				$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
				$url = env('RECEIPT_URL');
				$dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
				//changes for breeze specific URL from DB
				if ($dynamicReceiptUrl) {
					$url = $dynamicReceiptUrl->value;
				}
				$sms_msg = "Welcome to " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
				dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
			}
			return $data;
		}
	}

	//Save Transaction Data for Report
	protected function saveTransactionData($rate, $mainTicket)
	{
		$transactionExist = TransactionData::where("ticket_id", $mainTicket->id)->first();
		if ($transactionExist) {
			return $transactionExist;
		}
		$this->log->info("Save transaction " . json_encode($rate));
		$rate_id = isset($rate['id']) ? $rate['id'] : '109';
		$rate_description = isset($rate['description']) ? $rate['description'] : 'Daily Max';
		$rate_amount = $rate['price'];
		$tax_rate = $mainTicket->tax_rate == '' ? 0.00 : $mainTicket->tax_rate;
		$processing_fee = $mainTicket->processing_fee == '' ? 0.00 : $mainTicket->processing_fee;
		$total = $rate_amount + $tax_rate + $processing_fee;
		$ticket = new TransactionData();

		$ticket->user_id = $mainTicket->user_id;
		$ticket->facility_id = $mainTicket->facility_id;
		$ticket->partner_id = $mainTicket->partner_id;
		$ticket->ticket_id = $mainTicket->id;
		$ticket->rate_id = $rate_id;
		$ticket->rate_description = $rate_description;
		$ticket->rate_amount = $rate['price'];
		$ticket->total = $total;
		$ticket->tax_fee = $tax_rate;
		$ticket->processing_fee = $processing_fee;
		$ticket->discount_amount = $mainTicket->paid_amount;
		$ticket->grand_total = $mainTicket->grand_total;
		$ticket->save();
		return $ticket;
	}


	public function openGateCheckinCheckout(Request $request)
	{

		$this->log->info("openGateCheckinCheckout Request received --" . json_encode($request->all()));
		if ($request->header('X-ClientSecret') != '') {
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if (!$secret) {
				throw new NotFoundException('Invalid partner.');
			}
		}
		$facility = Facility::find($request->facility_id);
		if (!$facility) {
			throw new ApiGenericException("Invalid garage.");
		}

		$partner_id = $facility->owner_id;
		$gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
		if (!$gate) {
			throw new ApiGenericException("The system is not currently available. Please try again later.");
		}
		if (isset($gate) && $gate->active == '0') {
			throw new ApiGenericException('The system is not currently available. Please try again later.');
		}

		$ticket = Ticket::where("ticket_number", $request->eticket_id)->first();
		if (!$ticket) {
			throw new ApiGenericException('No ticket found.');
		}

		//check gate api
		if ($facility->open_gate_enabled == '1') {
			$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
			if ($gateStatus == "true") {
			} else {
				throw new ApiGenericException($gateStatus);
			}
		}
		$this->log->info("Gate opened successfully.");
		$data['msg'] = "Gate opened successfully.";
		return $data;
	}

	public function checkOverstayStatus($ticket)
	{
		$OverstayTickets = OverstayTicket::where("ticket_number", $ticket->ticket_number)->orderBy("id", "DESC")->get();
		if ($OverstayTickets->count() > 0)
			return true;
		else
			return false;
	}

	public function saveAnetTransaction($request)
	{

		if (isset($request->payment_details)) {
			$facility = Facility::find($request->facility_id);
			$user = User::where('session_id', $request->session_id)->where('created_by', $facility->owner_id)->first();
			$this->log->info("Save payment details before checkin  --" . json_encode($request->payment_details));
			$authorized_anet_transaction = new AuthorizeNetTransaction();
			$authorized_anet_transaction->sent = '1';
			$authorized_anet_transaction->user_id = $user->id;
			$authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
			$authorized_anet_transaction->name = $request->payment_details['MerchantName'];
			$authorized_anet_transaction->description = "Payment details  save before checkin";
			$authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
			$authorized_anet_transaction->expiration = $request->payment_details['expiry'];
			$authorized_anet_transaction->card_type = $request->payment_details['CardType'];
			$authorized_anet_transaction->anet_trans_hash = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
			$authorized_anet_transaction->ref_id = $request->payment_details['RequesterTransRefNum'];
			$authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
			$authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
			$authorized_anet_transaction->method = "card";
			$authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
			$authorized_anet_transaction->status_code =  isset($request->payment_details['StatusCode']) ? $request->payment_details['StatusCode'] : '';
			$authorized_anet_transaction->status_type = isset($request->payment_details['StatusType']) ? $request->payment_details['StatusType'] : '';
			$authorized_anet_transaction->status_message = isset($request->payment_details['StatusMessage']) ? $request->payment_details['StatusMessage'] : '';
			$authorized_anet_transaction->name = isset($request->payment_details['CardHolderName']) ? $request->payment_details['CardHolderName'] : '';
			$authorized_anet_transaction->save();
		}
		$this->log->info("Save payment details before checkin  done  --" . json_encode($request->payment_details));
		return $authorized_anet_transaction;
	}

	public function getPromocodeRequestDetails($request, $priceBreakUp, $ticket)
	{
		$card_last_four = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
		$expiry = isset($request->expiry) ? $request->expiry : '';
		$promocodeRequest = PromocodeRequest::where('card_last_four', $card_last_four)->where('expiry', $expiry)->orderBy("id", "desc")->first();
		$tax_rate = $priceBreakUp['tax_rate'];
		$processingFee = $priceBreakUp['processing_fee'];

		if ($promocodeRequest) {
			//$secret = OauthClient::where('partner_id', $checkinData->partner_id)->first();

			//if (isset($request->promocode)) {
			$request->request->add(['promocode' => $promocodeRequest->promocode]);
			$request->request->add(['amount' => $priceBreakUp['parking_amount']]);
			$request->request->add(['client_id' => $request->header('X-ClientSecret')]);
			$request->request->add(['email' => $promocodeRequest->email]);

			// Vijay : 05-07-2024 Start  :
			$request->request->add(['tax_amount' => $tax_rate]);
			$request->request->add(['processing_fee' => $processingFee]);
			//  Close !!!  : 

			$this->validate($request, Promotion::$checkPromoValidationRulesGuestThirdParty);
			try {
				$response = LatestPromoCodeLib::validatePromoCodeThirdParty($request);
			} catch (ApiGenericException $e) {
				if (str_contains($e->getMessage(), "Sorry, this validation code is valid only for transactions of")) {
					$ticket->promocode_msg = "Sorry, this validation code is valid only for transactions of";
					return $ticket;
				}
				throw new ApiGenericException($e->getMessage());
			}

			if (isset($response->getData()->is_promocode_valid) == '1') {

				if ($response->getData()->discount_in_hours > 0) {
					// $arrival_time = $checkinData->checkin_time;
					// $diff_in_hours = $checkinData->length - $response->getData()->discount_in_hours;
					// $isMember = 0;
					// if ($checkinData->facility->is_hourly_rate == '1' || $checkinData->facility->is_hourly_rate == 1) {
					//     $rate = $checkinData->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
					// } else {
					//     $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE, $isMember);
					// }
					// $priceBreakUp = $checkinData->priceBreakUp($rate);
					// $tax_rate     = $priceBreakUp['tax_rate'];
					// $processing_fee  = $priceBreakUp['processing_fee'];
					// $base_amount = ($rate['price'] + $tax_rate + $processing_fee);
					// if ($checkinData->grand_total == $base_amount) {
					//     $discount_amount = number_format((float) $checkinData->grand_total, 2);
					// } else {
					//     $discount_amount = number_format((float) $checkinData->parking_amount - $rate['price'], 2);
					// }

					// $checkinData->discount_amount = $discount_amount <= 0 ? 0 : number_format($discount_amount, 2);
					// $checkinData->discount_hours = $response->getData()->discount_in_hours;
					// $checkinData->promocode = $request->promocode;
					// $checkinData->save();

					// $request->request->add(['discount_amount' => $discount_amount]);
					// $promoUsage = PromoUsage::where('ticket_id', $checkinData->id)->delete();
					// $this->updatePromocodeUsage($checkinData, $checkinData->user->email);
				} else {

					$new_pro_rate = ($ticket->grand_total) - ($response->getData()->discount_in_dollar);
					$discount_amount = number_format($response->getData()->discount_in_dollar, 2);
				}
			}
			$request->request->add(['discount_amount' => $discount_amount]);
			// $promoUsage = PromoUsage::where('ticket_id', $checkinData->id)->delete();
			// $this->updatePromocodeUsage($checkinData, $checkinData->user->email);
			//}
			// remove promocode if apply
			// $checkinData->discount_amount = isset($discount_amount) ? $discount_amount : '';
			$ticket->promocode = $request->promocode;
			$ticket->discount_amount =  $discount_amount;
			$ticket->save();
		}
		return $ticket;
	}

	public function updatePromocodeUsage($ticket, $email)
	{
		try {
			$promoUsage = new PromoUsage();
			$promoUsage->user_id = isset($ticket->user_id) ? $ticket->user_id : '0';
			$promoUsage->partner_id = $ticket->partner_id;
			$promoUsage->promocode = $this->request->promocode;
			$promoUsage->email = $email != '' ? $email : '';
			$promoUsage->ticket_id = $ticket->id;
			$promoUsage->discount_amount = $this->request->discount_amount;
			$promoUsage->save();
		} catch (Exception $e) {
			throw $e;
		}
	}


	public function validateWhitelistUsers(Request $request)
	{
		$this->log->info("validateWhitelistUsers received --" . json_encode($request->all()));

		$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
		if (!$secret) {
			throw new ApiGenericException('Invalid partner.');
		}

		$name = $request->name;
		$countryCode = QueryBuilder::appendCountryCode();
		$phone = $countryCode . $request->phone;
		$email = $request->email;

		$partner_id = $secret->partner_id;
		$existPhone = WhitelistUser::where('phone', $phone)->where('partner_id', $partner_id)->first();
		if (isset($existPhone->id)) {
			$this->log->info("validateWhitelistUsers already exist --");
			//throw new ApiGenericException('User already exist.');
			$user = $existPhone;
		} else {
			$this->log->info("validateWhitelistUsers new user --");
			$data['name'] = $name;
			$data['phone'] = $phone;
			if (isset($email) && $email != '') {
				$data['email'] = $email;
			}

			$data['partner_id'] = $partner_id;
			$user = WhitelistUser::create($data);
		}
		$total = "0.00";

		$this->log->info("validateWhitelistUsers created --");
		if (count($request->cards_data) > 0 && isset($user->id)) {
			$this->log->info("validateWhitelistUsers enter --");
			foreach ($request->cards_data as $key => $card) {

				$card_last_four     = $card['Last4'];
				$expiry     = $card['ExpirationMonth'] . substr($card['ExpirationYear'], -2);

				//check card exist
				$cardExist = WhitelistUserCard::where("card_last_four", $card_last_four)->where("expiry", $expiry)->where("partner_id", $partner_id)->first();
				if ($cardExist) {
					continue;
				}
				$cardData['payment_token'] = $card['Token'];
				$cardData['session_id'] = "";
				$brand = $card['Brand'];

				$cardData['card_type'] = $brand;
				$cardData['card_last_four'] = $card_last_four;
				$cardData['expiry'] = $expiry;
				$cardData['whitelist_user_id'] = $user->id;
				$cardData['partner_id'] = $partner_id;
				WhitelistUserCard::create($cardData);
			}
			$this->log->info("validateWhitelistUsers done --");
		}
		return $user;
	}

	public function validateWhitelistUsersCardExist(Request $request)
	{
		$this->log->info("validateWhitelistUsersCardExist received --" . json_encode($request->all()));
		$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
		if (!$secret) {
			throw new ApiGenericException('Invalid partner.');
		}
		$partner_id = $secret->partner_id;
		$card_last_four     = $request->Last4;
		$expiry     = $request->ExpirationMonth . substr($request->ExpirationYear, -2);
		//check card exist
		$cardExist = WhitelistUserCard::where("card_last_four", $card_last_four)->where("expiry", $expiry)->where("partner_id", $partner_id)->first();
		if ($cardExist) {
			throw new ApiGenericException('Card already registered with another employee.');
		}
		return "Success";
	}
	public function validateWhitelistUsersbkp(Request $request)
	{
		$this->log->info("validateWhitelistUsers received --" . json_encode($request->all()));

		$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
		if (!$secret) {
			throw new ApiGenericException('Invalid partner.');
		}

		$name = $request->name;
		$countryCode = QueryBuilder::appendCountryCode();
		$phone = $countryCode . $request->phone;

		$partner_id = $secret->partner_id;
		$existPhone = WhitelistUser::where('phone', $phone)->where('partner_id', $partner_id)->first();
		if (isset($existPhone->id)) {
			throw new ApiGenericException('User already exist.');
		}
		$total = "0.00";
		$paymentGateways = PartnerPaymentGateway::where(['partner_id' => $partner_id])->get();
		$cardResponse = [];
		$setResponseFlag = false;  // While Moving this code in prod please Connect with Vijay : this on only for staging testing : 09-12-2024

		if ($paymentGateways->count() > 0) {
			foreach ($paymentGateways as $key => $paymentGateway) {
				if (count($paymentGateway->facility_ids) <= 0)
					continue;

				$facilityId 		= $paymentGateway->facility_ids[0];

				$request->request->add(['facility_payment_type_id'  	=> $paymentGateway->facility_payment_type_id]);
				$request->request->add(['partner_id'  					=> $partner_id]);
				$request->request->add(['partner_payment_gateway_id' 	=> $paymentGateway->id]);

				$this->setDecryptedCard($request);

				$this->log->info("Loop Start for Card Save flow KEY {$key} : for MID :  {$paymentGateway->payment_mid} And Row ID : {$paymentGateway->id} ");

				$facility = Facility::with(['FacilityPaymentDetails'])->where(['owner_id' => $partner_id, 'id' => $facilityId, 'active' => 1])->first();

				if ($paymentGateway->facility_payment_type_id == '2') { 	// Datcap Gateway
					$this->log->info("Start Save Card For Datacap : ");

					$card_month 	= substr($request->expiration_date, 0, 2);
					$card_year 		= substr($request->expiration_date, -2);
					$request->request->add(['expiration_month' => $card_month]);
					$request->request->add(['expiration_year'  => $card_year]);

					$card_last_four = substr($request->card_number, -4);
					//create datacap otu token
					try {
						$datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($request, $facility);
						$this->log->error("Datacap OTU Creation :" . json_encode($datacapPaymentToken));
					} catch (\Throwable $th) {

						$this->log->error("Error Exception  in datacap OTU creation :{$th->getMessage()} ");
						throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
					}

					try {
						if (isset($datacapPaymentToken["Token"]) && $datacapPaymentToken["Token"]) {
							$request->request->add(['token' => $datacapPaymentToken["Token"]]);
							$paymentResponse = DatacapPaymentGateway::makeZeroAuthPaymentDataCap($request, $facility);

							if ($paymentResponse['Status'] == 'Approved') {
								$this->log->info("Datacap Card Response : " . json_encode($paymentResponse));
								if (is_object($paymentResponse)) {
									$paymentResponse = (array)$paymentResponse;
								}
								$data['payment_token']                           = $paymentResponse['Token'];
								$data['session_id']                  = $paymentResponse['CardHolderID'];
								$brand              = str_replace('/', '', $paymentResponse['Brand']);
								$card_last_four     = substr($paymentResponse['Account'], -4);
								$data['name'] = $name;
								$data['phone'] = $phone;
								$data['partner_id'] = $secret->partner_id;
								$data['card_type'] = $brand;
								$data['card_last_four'] = $card_last_four;
								$data['expiry'] = $request->expiration_date;
								WhitelistUser::create($data);
								//$saveDatacapCard = DatacapPaymentGateway::saveDatacapCard($paymentResponse, $user_id, $request);
								$this->log->info("Datacap Card Saved Successfully : " . json_encode($data));
								$cardResponse = $data;
								// $setResponseFlag = true;
							} else {
								$this->log->info("The Credit card information entered is not correct. Please try again or use another card. =>  Datacap Card");
								throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
							}
						} else {
							throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
						}
					} catch (\Throwable $th) {
						$this->log->error("Error in Datacap Card Save : {$th->getMessage()}");
						throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
						// if ($setResponseFlag == false) {
						// 	throw new ApiGenericException("The Credit card information entered is not correct. Please try again or use another card.");
						// }
					}
				}
				return $cardResponse;
			}
		} else {
			throw new ApiGenericException('Payment details not defined.');
		}
	}

	//PMIS-13403
	public function pullTicket($request, $paymentGateway, $facility)
	{
		//$permitRequest = PermitRequest::with(['user'])->where('facility_id', $request->facility_id)->whereNull("cancelled_at")->orderBy("id", "DESC")->first();
		//$user  = User::where('id',$permitRequest->user_id)->first();

		$userSessionExist = UserSession::where("session_id", $request->session_id)->where('partner_id', $facility->owner_id)->orderBy("id", "DESC")->first();
		if ($userSessionExist && (isset($request->session_id) && !empty($request->session_id))) {
			$user = User::where('id', $userSessionExist->user_id)->first();
		} else {
			$userCreate = new User();
			$request->merge([
				'owner_id' => $facility->owner_id
			]);
			$user = $userCreate->createUser($request);
			$userSession = new UserSession();
			$userSession->user_id = $user->id;
			$userSession->partner_id = $user->created_by;
			$userSession->session_id = isset($request->session_id) ? $request->session_id : 0;
			$userSession->save();
		}

		if ($request->eticket_id != '') {
			$ticket = Ticket::where('ticket_number', $request->eticket_id)->where('is_checkin', '0')->where('is_checkout', '0')->first();
			if (!$ticket) {
				throw new ApiGenericException('Invalid checkin details.');
			}

			$ticket->vp_device_checkin = $ticket->reservation_id != '' ? '0' : '1';
			$ticket->license_plate = isset($request->license_plate) ? $request->license_plate : '';
			$ticket->card_last_four = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
			$ticket->expiry = isset($request->expiry) ? $request->expiry : '';
			if (isset($request->CardType)) {
				$card_type = '';
				if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
					$card_type = 'VISA';
				} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
					$card_type = 'MASTERCARD';
				} else if (strtolower($request->CardType) == "jcb") {
					$card_type = 'JCB';
				} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
					$card_type = 'AMEX';
				} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
					$card_type = 'DISCOVER';
				} else {
					$card_type = $request->CardType;
				}
				$ticket->card_type = $card_type;
			}
			$ticket->device_type = "IM30";
			$ticket->terminal_id = isset($request->TerminalID) ? $request->TerminalID : '';
			$ticket->payment_gateway = $paymentGateway;
			$ticket->user_id = $user->id;
			$ticket->session_id = $request->session_id;
			$ticket->payment_token = $request->payment_token;
			$ticket->customer_data = $request->CustomerData;
			$ticket->is_checkin = '1';
			$ticket->save();

			$facilityName = ucwords($facility->full_name);

			$this->log->info("eticket session user update  checkedin with ticket number {$ticket->ticket_number}");
			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			if ($user->email != '') {
				Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
			}
			$msg =  "WELCOME. #$ticket->ticket_number";
			if ($user->phone != '') {
				$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
				if ($partnerDetails->user_id == self::PARTNER_ID) {
					$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
						$join->on('user_facilities.user_id', '=', 'users.id');
						$join->where('user_facilities.facility_id', "=", $facility->id);
					})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
					$name = ($getRM->slug) ? $getRM->slug : 'receipt';
				} else {
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
				}
				$url = env('TOUCHLESS_WEB_URL');
				$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
				if ($dynamicCheckinUrl) {
					$url = $dynamicCheckinUrl->value;
				}
				$grace_period = $facility->grace_period_minute;
				$ticket_number = base64_encode($ticket->ticket_number);
				$sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
				dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
			}

			$data = ['msg' => $msg];
			$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
			$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
			$data["eticket_id"] = $ticket->ticket_number;
			$data["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '1');
			$this->log->info("eticket Checkin update done, response sent.");
			return $data;
		} else {
			$data['user_id'] = $user->id;
			$data['checkin_gate'] = $request->gate_id;
			$data['facility_id'] = $request->facility_id;
			$data['is_checkin'] = 1;
			$data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
			$data['checkin_time'] = date('Y-m-d H:i:s');
			$data['ticket_security_code'] = rand(1000, 9999);
			$data['partner_id'] = $facility->owner_id;
			$data['check_in_datetime'] = date('Y-m-d H:i:s');
			$data['vp_device_checkin'] = '1';

			$data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
			$data['expiry'] = isset($request->expiry) ? $request->expiry : '';
			if (isset($request->CardType)) {
				$card_type = '';
				if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
					$card_type = 'VISA';
				} else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
					$card_type = 'MASTERCARD';
				} else if (strtolower($request->CardType) == "jcb") {
					$card_type = 'JCB';
				} else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
					$card_type = 'AMEX';
				} else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
					$card_type = 'DISCOVER';
				} else {
					$card_type = $request->CardType;
				}
				$data['card_type'] = $card_type;
			}

			$data['device_type'] = "IM30";
			$data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
			$data['payment_gateway'] = $paymentGateway;
			$data['session_id'] = $request->session_id;
			$data['payment_token'] = $request->payment_token;
			$data['customer_data'] = $request->CustomerData;
			if (isset($request->event_id) && !empty($request->event_id)) {
				$data['event_id'] = $request->event_id;
			}
			$result = Ticket::create($data);

			$facilityName = ucwords($facility->full_name);

			$this->log->info("session user checkedin with ticket number {$result->ticket_number}");
			//check gate api
			if ($facility->open_gate_enabled == '1') {
				$gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
				if ($gateStatus == "true") {
				} else {
					throw new ApiGenericException($gateStatus);
				}
			}
			//Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
			if ($user->email != '') {
				Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
			}
			$msg =  "WELCOME. #$result->ticket_number";
			if ($user->phone != '') {
				//$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
				$partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
				if ($partnerDetails->user_id == self::PARTNER_ID) {
					$getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
						$join->on('user_facilities.user_id', '=', 'users.id');
						$join->where('user_facilities.facility_id', "=", $facility->id);
					})->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
					$name = ($getRM->slug) ? $getRM->slug : 'receipt';
				} else {
					$name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
				}
				$url = env('TOUCHLESS_WEB_URL');
				$dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
				if ($dynamicCheckinUrl) {
					$url = $dynamicCheckinUrl->value;
				}
				$grace_period = $facility->grace_period_minute;
				$ticket_number = base64_encode($result->ticket_number);
				$sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
				dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
			}

			$data = ['msg' => $msg];
			$data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
			$data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
			$data["eticket_id"] = $result->ticket_number;
			$data["print_receipt"] = QueryBuilder::setPrintReceipt($result, '1');
			$this->log->info("Checkin done, response sent.");
			return $data;
		}
	}

	public function checkExistingPermit($plate, $facility_id, $partner_id)
	{
		if ($plate == "8B03096" || $plate == "8BO3O96" || $plate == "8BO3096" || $plate == "8B03O96") {
			$plate = "8B03096";
		}
		$permitVehicles = PermitVehicle::where("license_plate_number", $plate)->where("partner_id", $partner_id)->get();
		if (count($permitVehicles) > 0) {
			foreach ($permitVehicles as $key => $permitVehicle) {
				$permitRequests = PermitRequest::with(['user'])->where('user_id', $permitVehicle->user_id)->where('facility_id', $facility_id)->whereDate('desired_end_date', '>=', date("Y-m-d"))->whereNull("cancelled_at")->orderBy("id", "DESC")->get();
				if (count($permitRequests) > 0) {
					foreach ($permitRequests as $key => $permitRequest) {

						$mapping = PermitVehicleMapping::where("permit_vehicle_id", $permitVehicle->id)->where("permit_request_id", $permitRequest->id)->first();
						if (!$mapping) {
							continue;
						}
						return $permitRequest;
					}
				} else {
					return $permitRequests;
				}
			}
		}
		return $permitVehicles;
	}

	public function sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type)
	{
		$this->log->info("before queue {$response_type}");
		$msgRespone = [];
		$msgRespone = (object) $msg;
		$this->log->info("Json response  " . json_encode(['eticket_id' => $ticket['ticket_number'], 'license_plate' => $ticket['license_plate'], 'data' => $msgRespone, 'response_type' => $response_type]));
		$myArray = json_encode(['eticket_id' => $ticket['ticket_number'], 'license_plate' => $ticket['license_plate'], 'data' => $msgRespone, 'response_type' => $response_type]);
		$queueManager = app('queue');
		$queue = $queueManager->connection('rabbitmq');
		if ($queue_name == '') {
			$queue_name = self::QUEUE_ENTRY;
		}
		$queue->pushRaw($myArray, $queue_name);
		$this->log->info("data send " . $queue_name);
		//Artisan::queue('read-license-plate', array('license_plate' => $ticket->license_plate, 'is_checkin_or_checkout' => '1', 'ticket' => $ticket, 'response_type' => $response_type, "msg" => $msg, 'queue_name' => $queue_name, 'gate_type' => $gate_type));
		return $ticket;
	}
}
