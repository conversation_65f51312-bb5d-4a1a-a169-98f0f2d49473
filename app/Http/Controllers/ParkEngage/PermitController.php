<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Crypt;
use Hash;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redirect;
use App\Models\ParkEngage\Gate;
use App\Models\PermitTicket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\PermitRequest;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Configuration;

class PermitController extends Controller
{

   protected $log; 
   protected $user;
   protected $partnerPaymentDetails;
   protected $authNet;
   protected $facility;

   const RESERVATION_THRESHOLD_TYPE = 2;
   const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE= 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO= 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR= 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "Sold Out At This Time.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

   public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->cim = $cim;
        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        $this->log = $logFactory->setPath('logs/parkengage/touchless-flow')->createLogger('touchless-flow');
    }
 
  
    public function receiveSms(Request $request)
    {
        $this->log->info("SMS receive from twilio");
        try
        {
        $request = $request->all();
        $explode = explode(" : ",$request['Body']);
        if(isset($explode[1])){
        $decrypt = json_decode(base64_decode($explode[1]));
        if($decrypt){
            if($this->checkFacilityAvailableForTwilio($decrypt->facility_id) == false){
                $this->log->info("{$decrypt->facility_id} not availabe right now.");
                return "Garage is not available.";
            }
            //$user = User::getAnonUserByPhone($request['From']);
            $facility = Facility::where('id', $decrypt->facility_id)->first();
            $user = User::where('phone', $request['From'])->where('created_by', $facility->owner_id)->first();
            if($user){

            }else{
                $user = User::create(
                    [
                    'name' => '',
                    'email' => '',
                    'phone' => $request['From'],
                    'password' => Hash::make(str_random(60)),
                    'anon' => true,
                    'user_type' => '5',
                    'created_by' => $facility->owner_id
                    ]
                );
            }
            if($user->created_by == ''){
                $user->created_by = $facility->owner_id;
                $user->save();
                $data['partner_id'] = $facility->owner_id;
            }else{
                $data['partner_id'] = $user->created_by;    
            }
            $data['user_id'] = $user->id;
            $data['checkin_gate'] = $decrypt->gate;
            $data['facility_id'] = $decrypt->facility_id;
            $data['is_checkin'] = 1;
            $data['ticket_number'] = 'PE'.rand(100,999).rand(100,999);
            $data['check_in_datetime'] = date('Y-m-d H:i:s');
            $data['checkin_time'] = date('Y-m-d H:i:s');
            $data['ticket_security_code'] = rand(1000, 9999);
            $result = Ticket::create($data);
            if($result){
                $ticket_number = base64_encode($result->ticket_number);
                $accountSid = env('TWILIO_ACCOUNT_SID');
                $authToken  = env('TWILIO_AUTH_TOKEN');
                $url = env('TOUCHLESS_APP_URL');
                $client = new Client($accountSid, $authToken);
                /*$encrypt = Crypt::encrypt(json_encode(['gate'=>'1', 'facility_id'=> $result->facility_id]));*/
                try
                {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                    // the number you'd like to send the message to
                        $request['From'],
                   array(
                         // A Twilio phone number you purchased at twilio.com/console
                         'from' => env('TWILIO_PHONE'),
                         // the body of the text message you'd like to send
                         //'body' => "Fine"
                         'body' => "Thank you for Check-In with ParkEngage. Your ticket number is $result->ticket_number. Use the following link to Pay and Check-Out. $url/pay/$ticket_number"
                     )
                 );
                $this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");
            }catch (Exception $e)
            {
                echo "Error: " . $e->getMessage();
                $this->log->error($e->getMessage());
            }
        }
    }
      return "done";
  }
        }catch (Exception $e)
        {
            echo "Error: " . $e->getMessage();
        }
    }

    protected function checkFacilityAvailableForTwilio($facility_id){
        $facility = Facility::where('id', $facility_id)->where('active', '1')->first();
        if(count($facility) == 0){
            return false;
        }else{
            if($facility->is_available != '1'){
                return false;
            }

            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $diff_in_hours = $arrival_time->diffInRealHours($from);
            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
            }
            $checkinData['length'] = $diff_in_hours;
            $facility = Facility::find($facility_id);
            /** this function is used to get Availability Information for respective facility **/
            $rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);
            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);

            if($rate == false){
                return false;
            }
                
            //return current availabilit
            if((isset($rateData['availability'])) && ($rateData['availability']>0))
            {
              $rate['availability'] = $rateData['availability']; 
            }else{            
               $rate['availability'] = self::DEFAULT_VALUE;  
            }
            if($rate['price'] == 'N/A'){
                return false;
            }

            //returning  message as per availibility 
           $rate['availability_msg'] = '';
           $rate['availability_msg_some_space'] = '';
           if((double)$rate['price'] >  self::DEFAULT_VALUE)
           { 
            if($rate['availability'] == self::DEFAULT_VALUE)
            {
                 return false;
            }else if($rate['availability'] < self::MIN_AVAILABILITY)
            {
                return false;
            }else{
                return true;
                 //return Redirect::to('error-facility');
            }
           }else if($rate['price'] == 'N/A'){
                 return false;
           }else if($rate['availability'] == self::DEFAULT_VALUE){
                  return false;
           }
         return true;
        }
    }

    protected function checkFacilityAvailable($facility_id){
        $facility = Facility::where('id', $facility_id)->where('active', '1')->first();
        if(count($facility) == 0){
            return false;
        }
        if($facility->is_available != '1'){
          return false;  
        }
        return true;
        
    }

    public function thankyouPermitCheckout($ticket_number){
        $checkinData = PermitTicket::with(['user','facility'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '1')->first();

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        
        $currentStartDate = Carbon::createFromFormat('Y-m-d H:i:s', ($checkinData->checkin_time == ''? date('Y-m-d H:i:s') : $checkinData->checkin_time));
        $currentEndDate = Carbon::createFromFormat('Y-m-d H:i:s', ($checkinData->checkout_time == ''? date('Y-m-d H:i:s') : $checkinData->checkout_time));
        $checkinData['current_check_in_datetime'] = $currentStartDate;
        $checkinData['current_checkout_datetime'] = $currentEndDate;
        $checkinData['current_diff_in_days'] = $currentStartDate->diffInDays($currentEndDate);
        $checkinData['current_diff_in_hours'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->diffInRealHours($currentEndDate);
        $checkinData['current_diff_in_minutes'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->addHours($checkinData['current_diff_in_hours'])->diffInRealMinutes($currentEndDate);

        $this->log->info("Prepaid : user successfully checkout and thankyou page showing with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
      return view('permit-checkin-checkout.thankyou-permit-checkout', ['data'=>$checkinData]);
    }

    public function alreadyCheckout(){
      return view('checkin-checkout.already-checkout');
    }

    public function errorPermitCheckin(){
      return view('permit-checkin-checkout.error-checkin');
    }

    public function errorCheckout(){
      return view('checkin-checkout.error-checkout');
    }

    public function errorFacility(){
      return view('checkin-checkout.error-facility');
    }


    public function getPermitCheckinDetails($ticket_number){
        $checkinData = PermitTicket::with(['user','permit','facility'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if(count($checkinData) == 0){
            $this->log->error("error on permit checkin screen");
            return Redirect::to('permit-error-checkin');
        }

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);

        $currentStartDate = Carbon::createFromFormat('Y-m-d H:i:s', ($checkinData->checkin_time == ''? date('Y-m-d H:i:s') : $checkinData->checkin_time));
        $currentEndDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['current_check_in_datetime'] = $currentStartDate;
        $checkinData['current_checkout_datetime'] = $currentEndDate;
        $checkinData['current_diff_in_days'] = $currentStartDate->diffInDays($currentEndDate);
        $checkinData['current_diff_in_hours'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->diffInRealHours($currentEndDate);
        $checkinData['current_diff_in_minutes'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->addHours($checkinData['current_diff_in_hours'])->diffInRealMinutes($currentEndDate);
        //dd($checkinData);
        ///-----------------
        $this->log->info("user is on pre checkout screen on sms click with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('permit-checkin-checkout.pre-checkout-screen', ['data'=>$checkinData]);
    }

    
    public function getPermitCheckoutDetails($ticket_number){
        $checkinData = PermitTicket::where('ticket_number', base64_decode($ticket_number))->first();
        
        if($checkinData && $checkinData->is_checkout == '1'){
            return Redirect::to('permit-thankyou-checkout/'.base64_encode($checkinData->ticket_number));
        }

        if(Session::get('is_sms_multiple_checkout')== '1'){
            $checkinData->is_checkout = '1';
            $checkinData->checkout_time = date("Y-m-d H:i:s");
            $checkinData->save();

            Session::forget('is_sms_multiple_checkout');
            $this->log->info("Permit : user direct checkout with ticket number {$checkinData->ticket_number} and go to the thankyou page  with browser {$_SERVER['HTTP_USER_AGENT']}");
            return Redirect::to('thankyou-permit-checkout/'.base64_encode($checkinData->ticket_number));
        }
        return view('permit-checkin-checkout.checkout-screen', ['data'=>$checkinData]);
    }

    

    public function confirmPermitCheckout(Request $request){
        //$request = $request->all();
        try
        {
        if($request->encrypt){
            $explode = explode('/', $request->encrypt);
            if(count($explode) < 5){
                return back()->with('danger', "Please scan the right QR code.");
            }
            $decrypt = base64_decode($explode[4]);
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                if($decrytArray){
                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                    $checkinData = [];
                    if($gate && isset($gate->gate_type)){
                        if($gate->gate_type == 'exit'){
                            $is_checkout = PermitTicket::where('ticket_number', $request->order_number)->where('is_checkout', '1')->first();
                            if($is_checkout){
                                return Redirect::to('permit-already-checkout');
                            }
                            $checkinData = PermitTicket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                            if($checkinData){

                                /*$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                $diff_in_hours = $arrival_time->diffInRealHours($from);
                                if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                   $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                }*/
                                $checkinData->is_checkout = '1';
                                $checkinData->checkout_gate = $decrytArray->gate;
                                $checkinData->checkout_time = date("Y-m-d H:i:s");
                                $checkinData->save();

                                if($checkinData){
                                    $this->log->info("Permit : user prepaid checkout with ticket number {$checkinData->ticket_number} and go to the thankyou page  with browser {$_SERVER['HTTP_USER_AGENT']}");
                                    return Redirect::to('thankyou-permit-checkout/'.base64_encode($checkinData->ticket_number));
                                }else{
                                    return back()->with('danger', 'Please scan valid scan QR code.');
                                }
                            }

                        }
                    }else{
                        return Redirect::to('error-checkin');
                    }   
                }else{
                    return back()->with('danger', 'Please scan valid scan QR code.');
                 }             
            }            
         }else{
            return back()->with('danger', 'Please scan valid scan QR code.');
         }
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }
    }

    

    public function updateRateInformationWithAvailibilty($arrival_time, $length_of_stay, Facility $facility)
    {
        $returnResultArr = array();
        
        $returnResultArr['coupon_threshold_price'] = 0;        
        $returnResultArr['is_coupon_threshold_price_percentage'] = 0;        
        $returnResultArr['availability'] = 0;
        $returnResultArr['is_coupon_threshold_applied'] = 0;
        
        $inventory = new Inventory();
        $date_time_out = Carbon::parse($arrival_time)->addMinutes((number_format($length_of_stay, 2) * 60));
        
        $realtimeWindow = $facility->realtime_window;
        $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;
        
        $timeDifference = date_diff(date_create($arrival_time), Carbon::now());
        
        $isAvailable = true;
    
        $thresholdAvailability = self::DEFAULT_VALUE;
        
        if ($isAvailable == true) {
            //check how many slots does entry and exit time occupies
              //$difference = date_diff(date_create($arrival_time), date_create($date_time_out));
            $difference = date_diff(date_create(date('Y-m-d',strtotime($arrival_time))), date_create(date('Y-m-d',strtotime(($date_time_out)))));
            
            if ($difference->d > 0) {
//                $dates   = $inventory->generateArrayOfDates(
//                '', date($arrival_time), date($date_time_out));
                
                 $dates   = $inventory->generateArrayOfDates(
                 ($difference->d + self::ADD_EXTRA_DAY_COUNT), date('Y-m-d H:i:s', strtotime($arrival_time)));
                  
                $dayDifference = $difference->d;
                
                foreach ($dates as $key => $date) {                    
                    $facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();
                    
                    if ($facilityAvailability) {
                    $inventory = json_decode($facilityAvailability->availability);

                    if ($key == 0) {
                    /**
                    * because this is the first day in the dates provided
                    * we should check from each time_slot starting
                    * from the hour provided in the api call
                    */
                    $i = date('G', strtotime($arrival_time));
                    while ($i <= self::TWENTY_FOUR_HOURS) {
                     if(isset($inventory->{$i}))
                     {
                      if ($inventory->{$i} < 1) {
                        $isAvailable = false;
                        break;
                      }
                        if($thresholdAvailability>0)
                        {
                            if($thresholdAvailability > $inventory->{$i})
                            {
                                $thresholdAvailability = $inventory->{$i};
                            }

                        }else{
                            $thresholdAvailability = $inventory->{$i};
                        }
                     }
                      $i++;
                    }
                  } elseif ($key == $dayDifference) {
                    $i = date('G', strtotime($date_time_out));
                    $minutes = date('i', strtotime($date_time_out));
                    if ($minutes >= 30) {
                        $i++;
                    }
                    /**
                     * because this is the last day in the dates provided
                     * we should check from each time_slot starting
                     * till the hour provided in the api call
                     */
                    $j = 0;
                    while ($j < $i) {
                     if(isset($inventory->{$j}))
                     {
                      if ($inventory->{$j} < 1) {
                        $isAvailable = false;
                        break;
                      }
                      if($thresholdAvailability>0)
                        {
                            if($thresholdAvailability > $inventory->{$j})
                            {
                                $thresholdAvailability = $inventory->{$j};
                            }

                        }else{
                            $thresholdAvailability = $inventory->{$j};
                        }
                     }
                     $j++;
                    }
                  } else {
                  /**
                   * because this could be any day except first and last in
                   * the dates provided we should check from whole day
                   */
                  $k = 0;
                  while ($k <= self::TWENTY_FOUR_HOURS) {
                   if(isset($inventory->{$k}))
                   {
                    if ($inventory->{$k} < 1) {
                      $isAvailable = false;
                      break;
                    }
                     if($thresholdAvailability>0)
                        {
                            if($thresholdAvailability > $inventory->{$k})
                            {
                                $thresholdAvailability =$inventory->{$k};
                            }

                        }else{
                            $thresholdAvailability = $inventory->{$k};
                        }
                   }
                    $k++;
                  }
                }
               }
              }
             
            }else{
                 $startingHour = date('G', strtotime($arrival_time));
                 $endingHour   = date('G', strtotime($date_time_out));
                 $facilityAvailability     = FacilityAvailability::where(
                    ['facility_id' => $facility->id, 'date' => date('Y-m-d',strtotime($arrival_time))])->first();
               
                 if ($facilityAvailability) {
                  $availability = json_decode($facilityAvailability->availability, true);
                 
                  while ($startingHour <= $endingHour) {
                   if(isset($availability[$startingHour]))
                   {
                    if (($availability[$startingHour] < 1)) {
                      $isAvailable = false;
                    }
                    if($thresholdAvailability>0)
                    {
                        if($thresholdAvailability > $availability[$startingHour])
                        {
                            $thresholdAvailability = $availability[$startingHour];
                        }
                        
                    }else{
                        $thresholdAvailability = $availability[$startingHour];
                    }
                   }
                    $startingHour++;
                  }
                }
                
            } 
            
            if($thresholdAvailability < self::DEFAULT_VALUE)
            {
                $thresholdAvailability = self::DEFAULT_VALUE;  
            }
            
            if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {  
                
              $dateIn = date('Y-m-d', strtotime($arrival_time));
              $facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();
              
              if ($facilityAvailability) {
                //$availabilities = json_decode($facilityAvailability->availability, true);
           
                if ($thresholdAvailability >= 0) {
                    
                   $couponThresholdsNew = $facility->facilityCouponThreshold;
                   $couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');
                   
                    $thresholds       = array();
                    if ($couponThresholdsNew) {
                        $thresholdCounter = self::DEFAULT_VALUE;
                      foreach ($couponThresholdsNew as $couponThreshold) {
                          
                        if($couponThreshold->uptick_type !=='deleted')
                        {
                           $thresholds[$thresholdCounter] =
                            ['threshold'=>$couponThreshold->threshold,'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
                            $thresholdCounter++;
                        }
                       }
                    }
                    $thresholdPrice = 0;
                    $currentAvailability = $thresholdAvailability;
                    foreach($thresholds as $key => $threshold) {
                    if ($thresholdAvailability <= $threshold['threshold']) {
                      if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
                        if ($threshold['uptick_type'] == 'price') {
                           $thresholdPrice = $threshold['uptick'];
                                                  
                           $returnResultArr['is_coupon_threshold_applied'] = 1;                           
                           break;
                        } else if ($threshold['uptick_type'] == 'percentage') {
                          $thresholdPrice =  $threshold['uptick'];
                          $returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG; 
                         
                          $returnResultArr['is_coupon_threshold_applied'] = 1;
                          break;
                        }
                      }
                    }
                  }
                  $returnResultArr['coupon_threshold_price'] = $thresholdPrice;        
                       
                  $returnResultArr['availability'] = $currentAvailability;
                }
              }
            }
            
        }
        
        return $returnResultArr;
       
         
    }

    




    public function isUserSessionExist($encrypt){
        /*$request = $request->all();
        dd($request);*/
        try
        {
        if(isset($encrypt)){
            $explode = $encrypt;
            $decrypt = base64_decode($explode);
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                $this->checkFacilityAvailable($decrytArray->facility_id);
                $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                $checkinData = [];
                if($gate && isset($gate->gate_type)){
                    if($gate->gate_type == 'exit'){
                        $existUser = User::where('email', Session::get('email'))->first();
                        if($existUser){                            
                            $ticket = Ticket::where('user_id', $existUser->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                            if($ticket){
                                Session::put('is_direct_checkout', '1');
                                $ticket->checkout_gate = $decrytArray->gate;
                                $ticket->save();
                                if($ticket->reservation_id != ''){
                                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            if($rate['price'] != $ticket->grand_total){
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('touchless-parking-prepaid-overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            }else{
                                            $ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_time = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');
                                            return redirect('touchless-parking-prepaid-checkout-success/'.base64_encode($ticket->ticket_number));
                                            }
                                            

                                }else{
                                    if($ticket->anet_transaction_id != ''){
                                        
                                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            if($rate['price'] != $ticket->grand_total){
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('touchless-parking-overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            }else{
                                                $ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_datetime = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');
                                            return redirect('touchless-parking-thankyou-checkout/'.base64_encode($ticket->ticket_number));
                                            }
                                            
                                        
                                    }else{
                                        return redirect('touchless-parking-checkin-details/'.base64_encode($ticket->ticket_number));
                                        
                                    }   
                                }
                            }else{
                                return back()->with('danger', 'Sorry! You do not have any active checkin.');
                            }
                        }else{
                            return back()->with('danger', 'Please scan valid scan QR code.');
                        }
                        
                    }else{
                        return back()->with('danger', "Please scan the right QR code.");
                    }
                }else{
                    return Redirect::to('error-checkin');
                }
            }  
        }else{
            return back()->with('danger', 'Please scan valid scan QR code.');
         }
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }      
    }

    public function getPrepaidUserCheckin($ticket_number){      
        
        return view('checkin-checkout.prepaid-user-details', ['data'=>$ticket_number]);
    }


    public function prepaidUserCheckinConfirm(Request $request){
        /*$request = $request->all();
        dd($request);*/
        try
        {
             
        if(isset($request->encrypt)){
            if($request->encrypt){
                        $ticket = Ticket::where('ticket_number', base64_decode($request->encrypt))->first();
                        $checkFacilityOwner = Facility::where('id', $ticket->facility_id)->first();
                        $existUser = User::where('email', $request->email)->where('created_by', $checkFacilityOwner->owner_id)->first();
                        if(!$existUser){
                            $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
                                if($geoLocation['geoplugin_countryCode'] == 'IN'){
                                    $countryCode = "+91";
                                }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
                                    $countryCode = "+1";
                                }else{
                                    $countryCode = "+1";
                                }
                                $user = User::where('phone', $countryCode.$request->phone)->first();
                                if(!$user){
                                    return back()->with('danger', 'User not associate with this phone. Please checkin again.');        
                                }
                                $existUser = $user;
                                
                        }
                        if($existUser){

                            $reservation = Reservation::where('user_id', $existUser->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->orderBy('id', 'Desc')->first();
                            if(count($reservation) > 0){

                                $alreadyAssociateReservation = Ticket::where('reservation_id', $reservation->id)->where('is_checkout', '1')->first();
                                if($alreadyAssociateReservation){
                                    return redirect('checkin-pay/'.base64_encode($ticket->ticket_number));
                                }

                                $start = date("Y-m-d H:i:s", strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp)->subMinutes(15)));
                                
                                $end = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                                
                                $d = date('Y-m-d H:i:s');
                                if(strtotime($d) >= strtotime($start) && strtotime($d) <= strtotime($end)){
                                    $ticket->reservation_id = $reservation->id;
                                    //$ticket->checkin_time = $ticket->checkin_time;
                                    $ticket->checkout_time = date('Y-m-d H:i:s');
                                    $ticket->check_in_datetime = $reservation->start_timestamp;
                                    $ticket->checkout_datetime = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                                    $ticket->total = $reservation->total;
                                    $ticket->grand_total = $reservation->total;
                                    $ticket->length = $reservation->length;
                                    $ticket->save();

                                    $reservation = Reservation::where('id', $ticket->reservation_id)->first();
                                    $reservation->is_ticket = '1';
                                    $reservation->save();

                                    return redirect('prepaid-checkout-details/'.base64_encode($ticket->ticket_number));
                                    
                                }else{
                                    return redirect('checkin-pay/'.base64_encode($ticket->ticket_number));
                                }
                                
                                
                            }else{
                                /*$geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
                                if($geoLocation['geoplugin_countryCode'] == 'IN'){
                                    $countryCode = "+91";
                                }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
                                    $countryCode = "+1";
                                }else{
                                    $countryCode = "+1";
                                }
                                $user = User::where('phone', $countryCode.$request->phone)->first();
                                if(!$user){
                                    return back()->with('danger', 'User not associate with this phone. Please checkin again.');        
                                }*/
                                /*$emailExist = User::where('email', $request->email)->where('created_by', $checkFacilityOwner->owner_id)->where('id','!=', $user->id)->first();
                                if($emailExist){
                                   return back()->with('danger', 'Email already associate with other user. Please checkin again.');         
                                }
                                $user->email = $request->email;
                                $user->save();*/
                                return redirect('checkin-pay/'.base64_encode($ticket->ticket_number));                                
                            }
                        }else{
                            return back()->with('danger', 'User not found.');
                        }
                        
                    }else{
                        return back()->with('danger', "Please scan the right QR code.");
                    }
                }
           
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }      
    }


    public function getCheckinPayment($ticket_number){
        $checkinData = Ticket::with('user')->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();
        
        if(count($checkinData) == 0){
            $this->log->error("error on checkin screen");
            return Redirect::to('error-checkin');
        }
        
        if($this->checkFacilityAvailable($checkinData->facility_id) == false){
            Redirect::to('error-facility');
        }
        if($checkinData->anet_transaction_id != ''){
            return redirect('thankyou-payment/'.base64_encode($checkinData->ticket_number));
        }
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
           $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
        }
        $checkinData['length'] = $diff_in_hours;
        $facility = Facility::find($checkinData->facility_id);
        /** this function is used to get Availability Information for respective facility **/
        $rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);
        $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);

        if($rate == false){
            return Redirect::to('error-facility');
        }
            
        //return current availabilit
        if((isset($rateData['availability'])) && ($rateData['availability']>0))
        {
          $rate['availability'] = $rateData['availability']; 
        }else{            
           $rate['availability'] = self::DEFAULT_VALUE;  
        }
        if($rate['price'] == 'N/A'){
            return Redirect::to('error-facility');
        }
        //returning  message as per availibility 
       $rate['availability_msg'] = '';
       $rate['availability_msg_some_space'] = '';
       if((double)$rate['price'] >  self::DEFAULT_VALUE)
       { 
        if($rate['availability'] == self::DEFAULT_VALUE)
        {
             return Redirect::to('error-facility');
        }else if($rate['availability'] < self::MIN_AVAILABILITY)
        {
            return Redirect::to('error-facility');
        }else{
             //return Redirect::to('error-facility');
        }
       }else if($rate['price'] == 'N/A'){
             return Redirect::to('error-facility');
       }else if($rate['availability'] == self::DEFAULT_VALUE){
              return Redirect::to('error-facility');
       }
        $checkinData['rate'] = $rate;
        $this->log->info("user is on checkin screen on sms click with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('checkin-checkout.payment-screen', ['data'=>$checkinData]);
    }

    public function getPrepaidUserCheckout($key){      
        Session::put('is_sms_direct_checkout', '1');   
        return view('checkin-checkout.prepaid-user-checkout-details', ['data'=>$key]);
    }


    public function prepaidUserCheckoutConfirm(Request $request){
        /*$request = $request->all();
        dd($request);*/

        try
        {
        if(isset($request->encrypt)){
            $explode = $request->encrypt;
            $decrypt = base64_decode($explode);
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                $this->checkFacilityAvailable($decrytArray->facility_id);
                $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                $checkinData = [];
                if($gate && isset($gate->gate_type)){
                    if($gate->gate_type == 'exit'){

                        $checkFacilityOwner = Facility::where('id', $decrytArray->facility_id)->first();
                        $existUser = User::where('email', $request->email)->where('created_by', $checkFacilityOwner->owner_id)->first();
                        if(!$existUser){
                            $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
                                if($geoLocation['geoplugin_countryCode'] == 'IN'){
                                    $countryCode = "+91";
                                }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
                                    $countryCode = "+1";
                                }else{
                                    $countryCode = "+1";
                                }
                                $user = User::where('phone', $countryCode.$request->phone)->first();
                                if(!$user){
                                    return back()->with('danger', 'User not associate with this phone. Please checkin again.');        
                                }
                                $existUser = $user;                 
                        }
                        if($existUser){

                            /*$reservation = Reservation::where('user_id', $existUser->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->orderBy('id', 'Desc')->first();*/
                            $ticket = Ticket::where('user_id', $existUser->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                            if($ticket){
                                $ticket->checkout_gate = $decrytArray->gate;
                                $ticket->save();
                                if($ticket->reservation_id != ''){
                                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            if($rate['price'] != $ticket->grand_total){
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('prepaid-overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            }else{
                                            /*$ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_time = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');*/
                                            return redirect('prepaid-checkout-details/'.base64_encode($ticket->ticket_number));
                                            }
                                            

                                }else{
                                    if($ticket->anet_transaction_id != ''){
                                        
                                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            if($rate['price'] != $ticket->grand_total){
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            }else{
                                                $ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_datetime = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            Session::forget('is_direct_checkout');
                                            /*Session::forget('email');
                                            Session::forget('phone');*/
                                            return redirect('thankyou-checkout/'.base64_encode($ticket->ticket_number));
                                            }
                                            
                                        
                                    }else{
                                        return redirect('payment/'.base64_encode($ticket->ticket_number));
                                        
                                    }   
                                }
                            }else{
                                return back()->with('danger', 'Sorry! You do not have any active checkin');
                            }
                            
                        }else{
                            return back()->with('danger', 'User not found.');
                        }
                    }else{
                        return back()->with('danger', 'Please scan valid scan QR code.');
                    }
                }
            }
        }
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }   
    }


    public function getTouchlessParkingUser($key){      
        
        $decrypt = json_decode(base64_decode($key));
        if($decrypt){
            if($this->checkFacilityAvailable($decrypt->facility_id) == false){
                $this->log->info("{$decrypt->facility_id} not availabe right now.");
                Redirect::to('error-facility');
            }
        }else{
            return Redirect::to('error-checkin');            
        }
        return view('permit-checkin-checkout.touchless-parking-user-details', ['data'=>$key]);
    }

    public function confirmTouchlessCheckin(Request $request){
        /*$request = $request->all();
        dd($request);*/
        try
        {
        if(isset($request->encrypt)){
            $explode = $request->encrypt;
            $decrypt = base64_decode($explode);
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                $this->checkFacilityAvailable($decrytArray->facility_id);
                $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                $checkinData = [];
                if($gate && isset($gate->gate_type)){
                    if($gate->gate_type == 'entry'){
                        $checkFacilityOwner = Facility::where('id', $decrytArray->facility_id)->first();
                        $geoLocation = unserialize(file_get_contents('http://www.geoplugin.net/php.gp?ip='.$_SERVER['REMOTE_ADDR']));
                        if($geoLocation['geoplugin_countryCode'] == 'IN'){
                            $countryCode = "+91";
                        }elseif($geoLocation['geoplugin_countryCode'] == 'US'){
                            $countryCode = "+1";
                        }else{
                            $countryCode = "+1";
                        }
                        $existUser = User::where('phone', $countryCode.$request->phone)->where('created_by', $checkFacilityOwner->owner_id)->first();
                        if($existUser){

                            if($request->name != ''){
                                $existUser->name = $request->name;
                                $existUser->save();                                
                            }
                            if($request->company_name != ''){
                                $existUser->company_name = $request->company_name;
                                $existUser->save();
                            }
                            Session::put('email', $existUser->email);
                            Session::put('phone', $existUser->phone);
                            $alreadyPrepaidReservation = PermitRequest::where('user_id', $existUser->id)->where('facility_id', $decrytArray->facility_id)->orderBy('id', 'desc')->first();
                            //dd($alreadyPrepaidReservation);
                            //->whereDate('start_timestamp', '<=', date('Y-m-d H:i:s', strtotime($today)))->whereDate('start_timestamp', '<=', date('Y-m-d H:i:s', strtotime($todayExtend)))
                            
                            if(count($alreadyPrepaidReservation) > 0){
                                $isResCompleted = PermitTicket::where('permit_request_id', $alreadyPrepaidReservation->id)->first();
                                    if(count($isResCompleted) > 0){
                                        if($isResCompleted->is_checkout == '0'){
                                            return back()->with('danger', "You have already Checked-In, Please Check-Out the existing parking first through Check-Out link shared on your registered email.");    
                                        }else{
                                        
                                        return redirect('touchless-parking-permit-prepaid/'.base64_encode($alreadyPrepaidReservation->account_number).'/'.$request->encrypt); 
                                        }
                                    }else{
                                        echo "3";
                                        return redirect('touchless-parking-permit-prepaid/'.base64_encode($alreadyPrepaidReservation->account_number).'/'.$request->encrypt); 
                                    }

                            }/*else{
                                return back()->with('danger', "Sorry, Please schedule your parking at Zoo Atlanta website");
                            }*/
                            
                        }/*else{
                            return back()->with('danger', "You are not register with us. Please register & schedule your parking at Zoo Atlanta website");
                        }*/
                        //dd($checkFacilityOwner->owner_id);
                        $user = User::where('phone', $countryCode.$request->phone)->where('created_by', $checkFacilityOwner->owner_id)->first();
                        if(!$user){

                            $user = User::create(
                            [
                                'name' => '',
                                'email' => $request->email,
                                'phone' => $countryCode.$request->phone,
                                'password' => Hash::make(str_random(60)),
                                'anon' => false,
                                'user_type' => '5',
                                'created_by' => $checkFacilityOwner->owner_id
                            ]
                            );

                        }
                        //check already checkin or not
                        $isAlreadyCheckin = PermitTicket::where('user_id', $user->id)->where('is_checkin', '1')->where('is_checkout', '0')->get();
                        if(count($isAlreadyCheckin) > 0){
                            return back()->with('danger', "You have already Checked-In, Please Check-Out the existing parking first through Check-Out link shared on your registered email.");
                        }
                        if($request->name != ''){
                            $user->name = $request->name;
                            $user->save();                                
                        }
                        if($request->company_name != ''){
                            $user->company_name = $request->company_name;
                            $user->save();
                        }
                        if($user->created_by == ''){
                            $facility = Facility::where('id', $decrytArray->facility_id)->first();
                            if($user->user_type == '5'){
                                $user->created_by = $facility->owner_id;
                                $user->save();
                            }                           
                            $data['partner_id'] = $facility->owner_id;
                        }else{
                            $data['partner_id'] = $user->created_by;    
                        }
                        Session::put('email', $user->email);
                        Session::put('phone', $user->phone);
                        $data['user_id'] = $user->id;
                        $data['checkin_gate'] = $decrytArray->gate;
                        $data['facility_id'] = $decrytArray->facility_id;
                        $data['is_checkin'] = 1;
                        $data['ticket_number'] = 'PE'.rand(100,999).rand(100,999);
                        $data['check_in_datetime'] = date('Y-m-d H:i:s');
                        $data['ticket_security_code'] = rand(1000, 9999);
                        $checkinData = PermitTicket::create($data);
                        Artisan::queue('email:touchless-parking-permit-confirm-checkin',array('id'=>$checkinData->id, 'type'=>'checkin'));

                        if($checkinData){
                            $ticket_number = base64_encode($checkinData->ticket_number);
                            $facilityName =  ucwords($checkFacilityOwner->full_name);
                            $accountSid = env('TWILIO_ACCOUNT_SID');
                            $authToken  = env('TWILIO_AUTH_TOKEN');
                            $url = env('TOUCHLESS_APP_URL');
                            $client = new Client($accountSid, $authToken);
                            /*$encrypt = Crypt::encrypt(json_encode(['gate'=>'1', 'facility_id'=> $result->facility_id]));*/
                            try
                            {
                                // Use the client to do fun stuff like send text messages!
                                $client->messages->create(
                                // the number you'd like to send the message to
                                    $countryCode.$request->phone,
                               array(
                                     // A Twilio phone number you purchased at twilio.com/console
                                     'from' => env('TWILIO_PHONE'),
                                     // the body of the text message you'd like to send
                                     //'body' => "Fine"
                                     'body' => "Thank you for Check-In with $facilityName. Your ticket number is $checkinData->ticket_number. Use the following link to Pay and Check-Out. $url/touchless-parking-permit-checkout-details/$ticket_number"
                                 )
                             );
                            $this->log->info("permit direct checkin SMS send to user {$user->phone} with ticket number {$checkinData->ticket_number}");
                            }catch (Exception $e)
                            {
                                echo "Error: " . $e->getMessage();
                                $this->log->error($e->getMessage());
                            }
                        }
                        return redirect('touchless-parking-permit-checkin-details/'.base64_encode($checkinData->ticket_number))->with('success', 'Success! You have successfully check-in.');
                    }else{
                        return back()->with('danger', "Please scan the right QR code.");
                    }
                }else{
                    return Redirect::to('error-checkin');
                }
            }  
        }else{
            return back()->with('danger', 'Please scan valid scan QR code.');
         }
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }      
    }

    public function getTouchlessParkingCheckinDetails($ticket_number){
        
        $checkinData = PermitTicket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('error-checkin');
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('error-checkout');
        }

        if($checkinData->anet_transaction_id != ''){
            return Redirect::to('touchless-parking-permit-payment-success/'.base64_encode($checkinData->ticket_number));
        }

        /*if($checkinData && $checkinData->is_checkin == '1'){
            return Redirect::to('prepaid-checkout-details/'.base64_encode($checkinData->ticket_number));
        }
        if($checkinData && $checkinData->is_checkout == '1'){
            return Redirect::to('thankyou-checkout/'.base64_encode($checkinData->ticket_number));
        }*/
        
        return view('permit-checkin-checkout.touchless-parking-checkin-details', ['data'=>$checkinData]);
    }

    public function getTouchlessParkingCheckoutDetails($ticket_number){
        $checkinData = PermitTicket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('error-checkin');
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('error-checkout');
        }

        if($checkinData->anet_transaction_id != ''){
            return Redirect::to('touchless-parking-permit-payment-success/'.base64_encode($ticket_number));
        }

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
           $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
        }
        $checkinData['length'] = $diff_in_hours;
        $facility = Facility::find($checkinData->facility_id);
        /** this function is used to get Availability Information for respective facility **/
        $rateData = $this->updateRateInformationWithAvailibilty($arrival_time,$diff_in_hours , $facility);
        
        $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
        if($rate == false){
            return Redirect::to('error-facility');
        }
            
        //return current availabilit
        if((isset($rateData['availability'])) && ($rateData['availability']>0))
        {
          $rate['availability'] = $rateData['availability']; 
        }else{            
           $rate['availability'] = self::DEFAULT_VALUE;  
        }
        if($rate['price'] == 'N/A'){
            return Redirect::to('error-facility');
        }
        //returning  message as per availibility 
       $rate['availability_msg'] = '';
       $rate['availability_msg_some_space'] = '';
       if((double)$rate['price'] >  self::DEFAULT_VALUE)
       { 
        if($rate['availability'] == self::DEFAULT_VALUE)
        {
             return Redirect::to('error-facility');
        }else if($rate['availability'] < self::MIN_AVAILABILITY)
        {
            return Redirect::to('error-facility');
        }else{
             //return Redirect::to('error-facility');
        }
       }else if($rate['price'] == 'N/A'){
             return Redirect::to('error-facility');
       }else if($rate['availability'] == self::DEFAULT_VALUE){
              return Redirect::to('error-facility');
       }
        $checkinData['rate'] = $rate;


        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);

        $this->log->info("Permit Touhcless : user is on payment screen on after checkout click with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");

        return view('permit-checkin-checkout.touchless-parking-checkout-details', ['data'=>$checkinData]);
    }

    public function getTouchlessParkingPaymentThankyou($ticket_number){
        $checkinData = PermitTicket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('error-checkin');
        }
        if($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != ''){
            return Redirect::to('touchless-parking-permit-checkin-details/'.$ticket_number);   
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('error-checkout');   
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('permit-checkin-checkout.touchless-parking-payment-success', ['data'=>$checkinData]);
    }


    public function makeTouchlessParkingPayment(Request $request){
        
        $this->checkFacilityAvailable($this->request->facility_id);
        $this->setDecryptedCard($request);

        //$this->validate($this->request, $this->billingValidation);
        $this->user = User::getAnonUserEmailOrPhone($this->request->email, $this->request->phone);
        $this->user->name = $this->request->name_on_card;
        $this->user->save();
        $this->facility = Facility::find($this->request->facility_id);
        if($this->request->total>0)
        {
            try{

                /*$is_partner = 0;
                if($this->user->user_type == 3){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                }elseif($this->user->user_type == 4 || $this->user->user_type == 5){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if($this->partnerPaymentDetails){
                        $is_partner = 1;
                    }else{
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                }else{
                    $is_partner = 0;
                }
                if($is_partner == 1){
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()                
                    ->setFacility($this->facility)
                    ->isPartner($this->partnerPaymentDetails)
                    ->setBillingAddress($this->getBillingArray());
                }else{
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());
                }*/
                $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());


                if (isset($this->paymentProfileId)) { // Use our logged in users profile
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    /*$this->authNet->isPartner($this->partnerPaymentDetails)
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);*/
                        $this->authNet
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            } catch (Exception $e) {
                $this->log->error($e);
                $request->session()->flash('alert-danger',
                'Payment error!');
                return back()->withError(['errMsg'=>$e]);
            }
        }

        $ticket = $this->saveTicket();
        if($this->request->total>0)
        {
            $reservationMode = "Permit Touchless Parking";
            // Send reservation to ticketech
            if($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else{
                $ticketech_guid = rand(100000, 999999);
            }
    
            try {
                
                // Use our database rate price to create the transaction
                    try {
                    /*$charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();*/
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
                    }catch (AuthorizeNetException $e) {
                        $this->log->error($e->getMessage());
                        return back()->with('danger', $e->getMessage());
                    }
                
                
            } catch (Exception $e) {
            // Charge successful, save transaction relationship to it
                /*dd($e);
                return back()->withError(['errMsg'=>$e]);
                dd(111);
                $transaction = $this->authNet->getTransaction();
                $ticket->anet_transaction_id = $transaction->id;
                $ticket->save();       
                $ticket->delete();
                return back()->withError(['errMsg'=>$e]);*/
                $this->log->error($e);
                return back()->with('danger', $e->getMessage());
            }
             // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->save();
            $this->log->info("permit touchless user done payment ticket id {$ticket->id} ticket number {$ticket->ticket_number} and go to the checkout scan screen with browser {$_SERVER['HTTP_USER_AGENT']}");
            if($ticket){
                Artisan::queue('email:touchless-parking-permit-checkin-payment',array('id'=>$ticket->id, 'type'=>'normal'));

                if(Session::get('is_direct_checkout')== '1'){
                    $ticket->is_checkout = '1';
                    $ticket->checkout_datetime = date("Y-m-d H:i:s");
                    $ticket->save();

                    /*if($ticket->reservation_id != ''){
                        $reservation = Reservation::where('id', $ticket->reservation_id)->first();
                    
                        $reservation->is_ticket = '2';
                        $reservation->save();
                    }*/
                    Session::forget('is_direct_checkout');
                    Session::forget('email');
                    Session::forget('phone');
                    return redirect('touchless-parking-permit-thankyou-checkout/'.base64_encode($ticket->ticket_number));
                }

                return redirect('touchless-parking-permit-payment-success/'.base64_encode($ticket->ticket_number))->with('success', 'Success! Payment Successfully done.');                
            }
            
        }else{
            return back()->with('danger', "Please use valida ticket number with amount.");
        }
        
    }

    protected function saveTicket()
    {
        $ticket = PermitTicket::where('ticket_number', $this->request->ticket_number)->first();
        if(!$ticket){
            return false;
        }
        $ticket->user_id = $this->user->id;
        $ticket->facility_id = $this->facility->id;
        $ticket->length = $this->request->length;
        $ticket->ticket_security_code = rand(1000, 9999);
        $ticket->total = $this->request->total;
        $ticket->grand_total = $this->request->total;
        $ticket->checkout_datetime = date('Y-m-d H:i:s', strtotime($this->request->checkout_datetime));
        $ticket->save();

        return $ticket;
    }


    public function getTouchlessParkingCheckout($ticket_number){
        $checkinData = PermitTicket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('error-checkin');
        }
        if($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != ''){
            return Redirect::to('touchless-parking-permit-checkin-details/'.$ticket_number);   
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('error-checkout');
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('permit-checkin-checkout.touchless-parking-checkout', ['data'=>$checkinData]);
    }

    public function confirmTouchlessParkingCheckout(Request $request){
        //$request = $request->all();
        try
        {
        if(isset($request->encrypt)){
            $explode = explode('/', $request->encrypt);
            if(count($explode) < 5){
                return back()->with('danger', "Please scan the right QR code.");
            }
            $decrypt = base64_decode($explode[4]);
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                if($decrytArray){
                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                    $checkinData = [];
                    if($gate && isset($gate->gate_type)){
                        if($gate->gate_type == 'exit'){
                            $is_checkout = PermitTicket::where('ticket_number', $request->order_number)->where('is_checkout', '1')->first();
                            if($is_checkout){
                                return Redirect::to('already-checkout');
                            }
                            $checkinData = PermitTicket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                            if($checkinData){

                                /*$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                $diff_in_hours = $arrival_time->diffInRealHours($from);
                                if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                   $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                }
                                $checkinData['length'] = $diff_in_hours;
                                $facility = Facility::find($checkinData->facility_id);
                                $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);*/
                                /*if($rate['price'] != $checkinData->grand_total){
                                    $this->log->info("Touchless : user have overstay time with ticket number {$checkinData->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                    return redirect('touchless-parking-overstay-pay/'.base64_encode($checkinData->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                }*/
                                $checkinData->is_checkout = '1';
                                $checkinData->checkout_gate = $decrytArray->gate;
                                //$checkinData->checkout_datetime = date("Y-m-d H:i:s");
                                $checkinData->save();
                                if($checkinData){
                                    /*$overstayTicket = OverstayTicket::where('ticket_number', $request->order_number)->get();
                                    if($overstayTicket){
                                        foreach ($overstayTicket as $key => $value) {
                                            $value->is_checkout = '1';
                                            $value->checkout_gate = $decrytArray->gate;
                                            $value->save();
                                        }
                                    }*/
                                    $this->log->info("Permit Touchless : user prepaid checkout with ticket number {$checkinData->ticket_number} and go to the thankyou page  with browser {$_SERVER['HTTP_USER_AGENT']}");
                                    return Redirect::to('touchless-parking-permit-thankyou-checkout/'.base64_encode($checkinData->ticket_number));
                                }else{
                                    return back()->with('danger', 'Please scan valid scan QR code.');
                                }
                            }

                        }
                    }else{
                        return Redirect::to('error-checkin');
                    }   
                }else{
                    return back()->with('danger', 'Please scan valid scan QR code.');
                 }             
            }            
         }else{
            return back()->with('danger', 'Please scan valid scan QR code.');
         }
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }
    }

    public function thankyouTouchlessParkingCheckout($ticket_number){
        $checkinData = PermitTicket::with(['user','facility'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '1')->first();

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $checkinData['is_overstay'] = '0';

        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->orderBy('id', 'DESC')->first();
        if($overstay){
          $checkinData['check_in_datetime'] = $overstay->check_in_datetime;
          $checkinData['checkout_datetime'] = $overstay->checkout_datetime;
          $checkinData['length'] = $overstay->length;
          $checkinData['is_overstay'] = '1';
          $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['check_in_datetime']);
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['checkout_datetime']);
            $checkinData['diff_in_hours'] = $startDate->diffInRealHours($endDate);
            $checkinData['diff_in_minutes'] = $startDate->copy()->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        }
        $this->log->info("Permit Prepaid : user successfully checkout and thankyou page showing with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
      return view('permit-checkin-checkout.touchless-parking-thankyou-checkout', ['data'=>$checkinData]);
    }


    public function getTouchlessParkingPrepaidCheckinDetails($ticket_number, $encrypt = null){
        $reservation = PermitRequest::with(['facility','user'])->where('account_number', base64_decode($ticket_number))->first();

        
        if(!$reservation){
           return Redirect::to('error-checkin'); 
        }

        $this->checkFacilityAvailable($reservation->facility_id);
        
        if($encrypt == ''){
            $config = Configuration::where('field_name','prepaid-checkin-time')->first();
            if(count($config) > 0){
                $prepaidCheckinTime = $config->field_value;
            }else{
                $prepaidCheckinTime = 15;
            }
            $today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);
            $reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->desired_start_date);
            $reservationEndDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->desired_end_date);
            //dd($today, $reservationstartDate, $reservationEndDate);
            if(strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)){
                return Redirect::to('error-prepaid-checkin'); 
            }

            if(strtotime($today) < strtotime($reservation->start_timestamp)){
                return Redirect::to('error-prepaid-checkin'); 
            }
        }

        /*$checkinData = Ticket::with(['user','reservation'])->where('reservation_id', $reservation->id)->first();
        if($checkinData && $checkinData->is_checkin == '1'){
            return Redirect::to('prepaid-checkout-details/'.base64_encode($checkinData->ticket_number));
        }
        if($checkinData && $checkinData->is_checkout == '1'){
            return Redirect::to('thankyou-checkout/'.base64_encode($checkinData->ticket_number));
        }
        if(!$checkinData){

            $ticket['user_id'] = $reservation->user_id;
            $ticket['reservation_id'] = $reservation->id;
            $ticket['facility_id'] = $reservation->facility_id;
            $ticket['length'] = $reservation->length;
            $ticket['ticket_security_code'] = rand(1000, 9999);
            $ticket['total'] = $reservation->total;
            $ticket['grand_total'] = $reservation->total;
            $ticket['check_in_datetime'] = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
            $ticket['check_in_datetime'] = date('Y-m-d H:i:s', strtotime($reservation->start_timestamp));
            $ticket['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
            $ticket['ticket_number'] = 'PE'.rand(100,999).rand(100,999);
            $checkinData = Ticket::create($ticket);
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);*/

        /*$startDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);*/
        
        $arrival_time = Carbon::createFromFormat('Y-m-d', $reservation->desired_start_date);
        $startDate = Carbon::createFromFormat('Y-m-d', $reservation->desired_start_date);
        $from = Carbon::createFromFormat('Y-m-d', $reservation->desired_end_date);
            $diff_in_hours = $from->diffInRealHours($arrival_time);
            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
            }
            $reservation['check_in_datetime'] = $reservation->desired_start_date;
        //$reservation['checkout_datetime'] = $from->subSeconds(1);
            $reservation['checkout_datetime'] = $from;
        $reservation['diff_in_days'] = $arrival_time->diffInDays($from);
        $reservation['diff_in_hours'] = $arrival_time->copy()->addDays($reservation['diff_in_days'])->diffInRealHours($from);
        $reservation['diff_in_minutes'] = $arrival_time->copy()->addDays($reservation['diff_in_days'])->addHours($reservation['diff_in_hours'])->diffInRealMinutes($from);
        return view('permit-checkin-checkout.touchless-parking-prepaid', ['data'=>$reservation]);
    }


    public function getTouchelessParkingPrepaidCheckin($ticket_number, $encrypt = null){

        $reservation = PermitRequest::with(['facility','user'])->where('account_number', base64_decode($ticket_number))->first();

        if(!$reservation){
           return Redirect::to('error-checkin'); 
        }

        $alreadyCheckin = PermitTicket::where('permit_request_id', $reservation->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if($alreadyCheckin){
            return Redirect::to('touchless-parking-permit-prepaid-checkin-success/'.base64_encode($alreadyCheckin->ticket_number)); 
        }

        if($encrypt != ''){
            $explode = $encrypt;
            $decrypt = base64_decode($explode);
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                $this->checkFacilityAvailable($decrytArray->facility_id);
                $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                $checkinData = [];
                if($gate && isset($gate->gate_type)){


                    //check third party gate API
                    $facility = Facility::where('id', $decrytArray->facility_id)->first();
                    if(!$facility){
                        return "Invalid garage."; 
                    }
                    if($gate->gate_type == 'entry'){
                        $data['user_id'] = $reservation->user_id;
                        $data['checkin_gate'] = $decrytArray->gate;
                        $data['facility_id'] = $decrytArray->facility_id;
                        $data['is_checkin'] = 1;
                        $data['ticket_number'] = 'PE'.rand(100,999).rand(100,999);
                        $data['check_in_datetime'] = $reservation->desired_start_date;
                        $data['checkin_time'] = date('Y-m-d H:i:s');
                        $checkoutTime = date("Y-m-d", strtotime($reservation->desired_end_date));
                        $data['checkout_datetime'] = date("Y-m-d", strtotime($checkoutTime));
                        $data['ticket_security_code'] = rand(1000, 9999);
                        $data['permit_request_id'] = $reservation->id;
                        $data['total'] = $reservation->permit_rate;
                        $data['grand_total'] = $reservation->permit_rate;
                        $checkinData = PermitTicket::create($data);

                        /*$reservation->is_ticket = '1';
                        $reservation->save();*/
                        Artisan::queue('email:touchless-parking-permit-prepaid-confirm-checkin',array('id'=>$checkinData->id, 'type'=>'checkin'));

                        $ticket_number = base64_encode($checkinData->ticket_number);
                        $facilityName =  ucwords($facility->full_name);
                        $accountSid = env('TWILIO_ACCOUNT_SID');
                        $authToken  = env('TWILIO_AUTH_TOKEN');
                        $url = env('TOUCHLESS_APP_URL');
                        $client = new Client($accountSid, $authToken);
                        
                        try
                        {
                            // Use the client to do fun stuff like send text messages!
                            $client->messages->create(
                            // the number you'd like to send the message to
                                $reservation->user->phone,
                           array(
                                 // A Twilio phone number you purchased at twilio.com/console
                                 'from' => env('TWILIO_PHONE'),
                                 // the body of the text message you'd like to send
                                 //'body' => "Fine"
                                 'body' => "Thank you for Check-In with $facilityName. Your ticket number is $checkinData->ticket_number. Use the following link to Check-Out. $url/touchless-parking-permit-prepaid-checkout-details/$ticket_number"
                             )
                         );
                        $this->log->info("direct checkin SMS send with ticket number {$checkinData->ticket_number}");
                        }catch (Exception $e)
                        {
                            echo "Error: " . $e->getMessage();
                            $this->log->error($e->getMessage());
                        }

                        return Redirect::to('touchless-parking-permit-prepaid-checkin-success/'.base64_encode($checkinData->ticket_number)); 
                    }
                }
            }
        }

        return view('permit-checkin-checkout.touchless-parking-prepaid-checkin', ['data'=>$reservation]);
    }

    public function getTouchelessParkingPrepaidConfirmCheckinSuccess($ticket_number){
        $checkinData = PermitTicket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('error-checkin');
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('error-checkout');   
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('permit-checkin-checkout.touchless-parking-prepaid-checkin-success', ['data'=>$checkinData]);
    }


    public function confirmTouchelessParkingPrepaidCheckin(Request $request){
        /*$request = $request->all();
        dd($request);*/
        try
        {
        if(isset($request->encrypt)){
            $explode = explode('/', $request->encrypt);
            if(count($explode) <= 1){
                return back()->with('danger', "Please scan the right QR code.");
            }
            $decrypt = base64_decode($explode[4]);
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                $this->checkFacilityAvailable($decrytArray->facility_id);
                $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                $checkinData = [];
                if($gate && isset($gate->gate_type)){
                    if($gate->gate_type == 'entry'){

                       $reservation = PermitRequest::where('account_number', $request->order_number)->first();
                        
                            if(!$reservation){
                                    return Redirect::to('error-checkin'); 
                            }

                            $this->checkFacilityAvailable($reservation->facility_id);
             

                        $ticket['user_id'] = $reservation->user_id;
                        $ticket['permit_request_id'] = $reservation->id;
                        $ticket['facility_id'] = $reservation->facility_id;
                        $ticket['length'] = $reservation->length;
                        $ticket['ticket_security_code'] = rand(1000, 9999);
                        $ticket['total'] = $reservation->permit_rate;
                        $ticket['grand_total'] = $reservation->permit_rate;
                        $ticket['check_in_datetime'] = date('Y-m-d H:i:s', strtotime($reservation->desired_start_date));
                        $ticket['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->desired_end_date));
                        $ticket['checkin_time'] = date("Y-m-d H:i:s");
                        $ticket['ticket_number'] = 'PE'.rand(100,999).rand(100,999);
                        $checkinData = PermitTicket::create($ticket);
                        if($checkinData){
                            $checkinData->checkin_gate = $decrytArray->gate;
                            $checkinData->is_checkin = '1';
                            $checkinData->save();

                            /*$reservation->is_ticket = '1';
                            $reservation->save();*/
                            Artisan::queue('email:touchless-parking-permit-prepaid-confirm-checkin',array('id'=>$checkinData->id, 'type'=>'checkin'));
                            return redirect('touchless-parking-permit-prepaid-checkin-success/'.base64_encode($checkinData->ticket_number))->with('success', 'Success! You have successfully check-in.');
                        }else{
                            return back()->with('danger', 'You have already checkin.');
                        }
                    }else{
                        return back()->with('danger', "Please scan the right QR code.");
                    }
                }else{
                    return Redirect::to('error-checkin');
                }
            }  
        }else{
            return back()->with('danger', 'Please scan valid scan QR code.');
         }
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }      
    }

    public function getTouchelessParkingPrepaidCheckoutDetails($ticket_number){
        $checkinData = PermitTicket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('error-checkin');
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('error-checkout');   
        }
        
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);

        $currentStartDate = Carbon::createFromFormat('Y-m-d H:i:s', ($checkinData->checkin_time == ''? date('Y-m-d H:i:s') : $checkinData->checkin_time));
        $currentEndDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['current_check_in_datetime'] = $currentStartDate;
        $checkinData['current_checkout_datetime'] = $currentEndDate;
        $checkinData['current_diff_in_days'] = $currentStartDate->diffInDays($currentEndDate);
        $checkinData['current_diff_in_hours'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->diffInRealHours($currentEndDate);
        $checkinData['current_diff_in_minutes'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->addHours($checkinData['current_diff_in_hours'])->diffInRealMinutes($currentEndDate);
        
        return view('permit-checkin-checkout.touchless-parking-prepaid-checkout-details', ['data'=>$checkinData]);
    }


    public function getTouchelessParkingPrepaidCheckout($ticket_number){
        $checkinData = PermitTicket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('error-checkin');
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('error-checkout');
        }

        if(Session::get('is_direct_checkout')== '1'){

            $facility = Facility::where('id', $checkinData->facility_id)->first();
            if(!$facility){
                return back()->with('danger', "Invalid garage.");
            }
            $gate = Gate::where('gate', $checkinData->checkout_gate)->where('facility_id', $facility->id)->first();
            if(!$gate){
                return back()->with('danger', "Something wrong in gate.");
            }
            

            /*$reservation = PermitRequest::where('id', $checkinData->permit_request_id)->first();
            $reservation->is_ticket = '2';
            $reservation->save();*/

            $checkinData->is_checkout = '1';
            $checkinData->checkout_time = date("Y-m-d H:i:s");
            $checkinData->save();

            Session::forget('is_direct_checkout');
            Session::forget('email');
            Session::forget('phone');
            return redirect('touchless-parking-permit-prepaid-checkout-success/'.base64_encode($checkinData->ticket_number));
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        //$endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('permit-checkin-checkout.touchless-parking-prepaid-checkout', ['data'=>$checkinData]);
    }


    public function touchlessParkingPrepaidCheckoutSuccess($ticket_number){
        $checkinData = PermitTicket::with(['user','facility'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '1')->first();

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $checkinData['is_overstay'] = '0';

        $currentStartDate = Carbon::createFromFormat('Y-m-d H:i:s', ($checkinData->checkin_time == ''? date('Y-m-d H:i:s') : $checkinData->checkin_time));
        $currentEndDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_time);
        $checkinData['current_check_in_datetime'] = $currentStartDate;
        $checkinData['current_checkout_datetime'] = $currentEndDate;
        $checkinData['current_diff_in_days'] = $currentStartDate->diffInDays($currentEndDate);
        $checkinData['current_diff_in_hours'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->diffInRealHours($currentEndDate);
        $checkinData['current_diff_in_minutes'] = $currentStartDate->copy()->addDays($checkinData['current_diff_in_days'])->addHours($checkinData['current_diff_in_hours'])->diffInRealMinutes($currentEndDate);

        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->orderBy('id', 'DESC')->first();
        if($overstay){
          $checkinData['check_in_datetime'] = $overstay->check_in_datetime;
          $checkinData['checkout_datetime'] = $overstay->checkout_datetime;
          $checkinData['length'] = $overstay->length;
          $checkinData['is_overstay'] = '1';
          $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['check_in_datetime']);
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['checkout_datetime']);
            $checkinData['diff_in_hours'] = $startDate->diffInRealHours($endDate);
            $checkinData['diff_in_minutes'] = $startDate->copy()->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        }
        $this->log->info("Permit Prepaid : user successfully checkout and thankyou page showing with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
      return view('permit-checkin-checkout.touchless-parking-prepaid-checkout-success', ['data'=>$checkinData]);
    }

    public function getTouchelessParkingPrepaidCheckoutScreen($ticket_number){
        $checkinData = PermitTicket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('error-checkin');
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('error-checkout');
        }
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('permit-checkin-checkout.touchless-parking-prepaid-checkout-screen', ['data'=>$checkinData]);
    }


    public function confirmTouchelessParkingPrepaidCheckout(Request $request){
        //$request = $request->all();
        try
        {
        if(isset($request->encrypt)){
            $explode = explode('/', $request->encrypt);
            if(count($explode) < 5){
                return back()->with('danger', "Please scan the right QR code.");
            }
            $decrypt = base64_decode($explode[4]);
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                if($decrytArray){
                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                    $checkinData = [];
                    if($gate && isset($gate->gate_type)){
                        if($gate->gate_type == 'exit'){
                            $is_checkout = PermitTicket::where('ticket_number', $request->order_number)->where('is_checkout', '1')->first();
                            if($is_checkout){
                                return Redirect::to('already-checkout');
                            }
                            $checkinData = PermitTicket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                            if($checkinData){

                                /*$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                $diff_in_hours = $arrival_time->diffInRealHours($from);
                                if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                   $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                }
                                $checkinData['length'] = $diff_in_hours;
                                $facility = Facility::find($checkinData->facility_id);*/
                                /*$rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);*/
                                /*if($rate['price'] != $checkinData->grand_total){
                                    $this->log->info("Touchless Prepaid : user have overstay time with ticket number {$checkinData->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                    return redirect('touchless-parking-prepaid-overstay-pay/'.base64_encode($checkinData->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                }*/


                                $facility = Facility::where('id', $checkinData->facility_id)->first();
                                if(!$facility){
                                    return back()->with('danger', 'Invalid Garage.');
                                }
                                

                                $checkinData->is_checkout = '1';
                                $checkinData->checkout_gate = $decrytArray->gate;
                                $checkinData->checkout_time = date("Y-m-d H:i:s");
                                $checkinData->save();

                                /*$reservation = Reservation::where('id', $checkinData->reservation_id)->first();
                                $reservation->is_ticket = '2';
                                $reservation->save();*/
                                if($checkinData){
                                    /*$overstayTicket = OverstayTicket::where('ticket_number', $request->order_number)->get();
                                    if($overstayTicket){
                                        foreach ($overstayTicket as $key => $value) {
                                            $value->is_checkout = '1';
                                            $value->checkout_gate = $decrytArray->gate;
                                            $value->save();
                                        }
                                    }*/
                                    $this->log->info("Permit Prepaid : user prepaid checkout with ticket number {$checkinData->ticket_number} and go to the thankyou page  with browser {$_SERVER['HTTP_USER_AGENT']}");
                                    return Redirect::to('touchless-parking-permit-prepaid-checkout-success/'.base64_encode($checkinData->ticket_number));
                                }else{
                                    return back()->with('danger', 'Please scan valid scan QR code.');
                                }
                            }

                        }
                    }else{
                        return Redirect::to('error-checkin');
                    }   
                }else{
                    return back()->with('danger', 'Please scan valid scan QR code.');
                 }             
            }            
         }else{
            return back()->with('danger', 'Please scan valid scan QR code.');
         }
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }
    }


    public function getTouchlessParkingVerifyCheckout($key){      
        
        $decrypt = json_decode(base64_decode($key));
        if($decrypt){
            if(isset($decrypt->facility_id)){
                $gate = Gate::where('gate', $decrypt->gate)->where('facility_id', $decrypt->facility_id)->first();
                if($gate && isset($gate->gate_type)){
                    if($gate->gate_type != 'exit'){
                        Redirect::to('error-facility');
                    }

                    if($this->checkFacilityAvailable($decrypt->facility_id) == false){
                        $this->log->info("{$decrypt->facility_id} not availabe right now.");
                        Redirect::to('error-facility');
                    }
                }
                    
            }else{
                return Redirect::to('error-checkin');
            }
            
        }else{
            return Redirect::to('error-checkin');
        }

        if(Session::get('email') != '' && Session::get('phone') !='')
        {
            try
            {
            if(isset($key)){
                $explode = $key;
                $decrypt = base64_decode($explode);
                if($decrypt){
                    $decrytArray = json_decode($decrypt);
                    $this->checkFacilityAvailable($decrytArray->facility_id);
                    $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                    $checkinData = [];
                    if($gate && isset($gate->gate_type)){
                        if($gate->gate_type == 'exit'){
                            $checkFacilityOwner = Facility::where('id', $decrytArray->facility_id)->first();
                            $existUser = User::where('email', Session::get('email'))->where('created_by', $checkFacilityOwner->owner_id)->first();
                            if($existUser){                            
                                $ticket = PermitTicket::where('user_id', $existUser->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                                if($ticket){
                                    Session::put('is_direct_checkout', '1');
                                    $ticket->checkout_gate = $decrytArray->gate;
                                    $ticket->save();
                                    if($ticket->permit_request_id != ''){
                                        /*$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                                $diff_in_hours = $arrival_time->diffInRealHours($from);
                                                if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                                   $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                                }
                                                $ticket['length'] = $diff_in_hours;
                                                $facility = Facility::find($ticket->facility_id);
                                                $isMember = 0;
                                                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE,$isMember);*/
                                                /*if($rate['price'] != $ticket->grand_total){
                                                    $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                    return redirect('touchless-parking-prepaid-overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                                }else{*/
                                                /*$ticket->is_checkout = '1';
                                                $ticket->checkout_gate = $decrytArray->gate;
                                                $ticket->checkout_time = date("Y-m-d H:i:s");
                                                $ticket->save();

                                                Session::forget('is_direct_checkout');
                                                Session::forget('email');
                                                Session::forget('phone');*/
                                                return redirect('touchless-parking-permit-prepaid-checkout-details/'.base64_encode($ticket->ticket_number));
                                                //}
                                                

                                    }else{
                                        if($ticket->anet_transaction_id != ''){


                                            $facility = Facility::where('id', $ticket->facility_id)->first();
                                            if(!$facility){
                                                return back()->with('danger', 'Invalid Garage.');
                                            }
                                            
                                                $ticket->is_checkout = '1';
                                                $ticket->checkout_gate = $decrytArray->gate;
                                                $ticket->checkout_datetime = date("Y-m-d H:i:s");
                                                $ticket->save();

                                                Session::forget('is_direct_checkout');
                                                Session::forget('email');
                                                Session::forget('phone');
                                                return redirect('touchless-parking-permit-thankyou-checkout/'.base64_encode($ticket->ticket_number));
                                            
                                                
                                            
                                        }else{
                                            return redirect('touchless-parking-permit-checkin-details/'.base64_encode($ticket->ticket_number));
                                            
                                        }   
                                    }
                                }else{
                                    return back()->with('danger', 'Sorry! You do not have any active checkin.');
                                }
                            }else{
                                return back()->with('danger', 'Please scan valid scan QR code.');
                            }
                            
                        }else{
                            return back()->with('danger', "Please scan the right QR code.");
                        }
                    }else{
                        return Redirect::to('error-checkin');
                    }
                }  
            }else{
                return back()->with('danger', 'Please scan valid scan QR code.');
             }
            }catch (Exception $e)
            {
                return back()->with('danger', 'Please scan valid scan QR code.');
                
            }

        }
        return view('permit-checkin-checkout.touchless-parking-verify-checkout', ['data'=>$key]);
    }


    public function confirmTouchlessParkingDirectCheckout(Request $request){
        /*$request = $request->all();
        dd($request);*/
        try
        {
        if(isset($request->encrypt)){
            $explode = $request->encrypt;
            $decrypt = base64_decode($explode);
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                $this->checkFacilityAvailable($decrytArray->facility_id);
                $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();
                $checkinData = [];
                if($gate && isset($gate->gate_type)){
                    if($gate->gate_type == 'exit'){
                        $checkFacilityOwner = Facility::where('id', $decrytArray->facility_id)->first();
                        $existUser = User::where('email', $request->email)->where('created_by', $checkFacilityOwner->owner_id)->first();
                        if($existUser){                            
                            $ticket = PermitTicket::where('user_id', $existUser->id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                            if($ticket){
                                Session::put('is_direct_checkout', '1');
                                $ticket->checkout_gate = $decrytArray->gate;
                                $ticket->save();
                                if($ticket->permit_request_id != ''){
                                    /*$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            if($rate['price'] != $ticket->grand_total){
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('touchless-parking-prepaid-overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            }else{*/
                                            /*$ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_time = date("Y-m-d H:i:s");
                                            $ticket->save();

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');*/
                                            return redirect('touchless-parking-permit-prepaid-checkout-details/'.base64_encode($ticket->ticket_number));
                                            //}
                                            

                                }else{
                                    if($ticket->anet_transaction_id != ''){

                                        $facility = Facility::where('id', $decrytArray->facility_id)->first();
                                        if(!$facility){
                                            return back()->with('danger', "Invalid garage.");
                                        }
                                        
                                        
                                        
                                        /*$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                                            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                                               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                                            }
                                            $ticket['length'] = $diff_in_hours;
                                            $facility = Facility::find($ticket->facility_id);
                                            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
                                            if($rate['price'] != $ticket->grand_total){
                                                $this->log->info("user have overstay time with ticket number {$ticket->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                                return redirect('touchless-parking-overstay-pay/'.base64_encode($ticket->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                                            }else{*/
                                                $ticket->is_checkout = '1';
                                            $ticket->checkout_gate = $decrytArray->gate;
                                            $ticket->checkout_datetime = date("Y-m-d H:i:s");
                                            $ticket->save();


                                            /*$reservation = Reservation::where('id', $ticket->reservation_id)->first();
                                            $reservation->is_ticket = '2';
                                            $reservation->save();*/

                                            Session::forget('is_direct_checkout');
                                            Session::forget('email');
                                            Session::forget('phone');
                                            return redirect('touchless-parking-permit-thankyou-checkout/'.base64_encode($ticket->ticket_number));
                                            //}
                                            
                                        
                                    }else{
                                        return redirect('touchless-parking-permit-checkin-details/'.base64_encode($ticket->ticket_number));
                                        
                                    }   
                                }
                            }else{
                                return back()->with('danger', 'Sorry! You do not have any active checkin.');
                            }
                        }else{
                            return back()->with('danger', 'Please scan valid scan QR code.');
                        }
                        
                    }else{
                        return back()->with('danger', "Please scan the right QR code.");
                    }
                }else{
                    return Redirect::to('error-checkin');
                }
            }  
        }else{
            return back()->with('danger', 'Please scan valid scan QR code.');
         }
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }      
    }


    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = isset($cardData[4])?$cardData[4]:'';
        $request->request->add(
            [
            'name_on_card' => $cardData[0],
            'card_number' => $cardData[1],
            'expiration_date' => $cardData[2],
            'security_code' => $cardData[3],
            'zip_code' => $zipCode
            ]
        );

        $this->request = $request;
    }

    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }

}
