<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Auth;
use Response;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use Artisan;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Classes\ParkengageGateApi;
use Storage;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\OauthClient;
use App\Classes\DatacapPaymentGateway;
use App\Jobs\SendSms;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Models\ParkEngage\WorldportLicensePlate;
use App\Models\ParkEngage\KstreetLicensePlate;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\PermitRequest;
use App\Models\ParkEngage\WoodmanLicensePlate;
use App\Models\ParkEngage\TicketExtend;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\DatacapTransaction;
use App\Services\Image;

class WoodmanCheckinCheckoutController extends Controller
{

  protected $log;
  protected $user;
  protected $facility;

  const  PARTNER_ID = 2980;

  use DispatchesJobs;
  const QUEUE_NAME = 'sms-send';
  const QUEUE_ENTRY = 'read-license-plate';

  public function __construct(Request $request, LoggerFactory $logFactory)
  {
    $this->request = $request;
    
    $this->log = $logFactory->setPath('logs/woodman')->createLogger('woodman');
  }


  public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($facility->timezone != '') {
                config(['app.timezone' => $facility->timezone]);
                date_default_timezone_set($facility->timezone);
            } else if ($partnerTimezone->timezone != '') {
                config(['app.timezone' => $partnerTimezone->timezone]);
                date_default_timezone_set($partnerTimezone->timezone);
            }
        }
    }

  public function uploadLicensePlate(Request $request){

    $this->log->info("woodman Request received --" . json_encode($request->all()));
    //dd($request->license_plate);
    //start time $request->transit['timestamps']['start']
    $this->setCustomTimezone($request->facility_id);
    $this->saveLicensePlate($request);
    $gate = Gate::where("gate", $request->gate_id)->first();
    if($request->gate_type == "entry"){
        $this->saveCheckin($request, $gate);
    }elseif($request->gate_type == "exit"){
        $this->saveCheckout($request, $gate);
    }else{
        return "Invalid Gate details.";
    }
    return "License plate successfully updated.";
  }


  public function ungatedTattileFeed(Request $request){

    $this->log->info("ungatedTattileFeed Request received --" . json_encode($request->all()));
    //dd($plate);
    //start time $request->transit['timestamps']['start']
    $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("lane_id", $request->transit['lane'])->first();
    if ($gate) {
        $this->setCustomTimezone($gate->facility_id);
        $this->log->info("Request : ".date("Y-m-d H:i:s"));

        $facility = Facility::find($gate->facility_id);
        if($facility->is_lpr_enabled == '0' || $facility->is_lpr_enabled == 0){
            $this->log->info("lpr is disabled");
            return true;
        }
        $this->saveLicensePlate($request, $gate);
      
      if ($gate->gate_type == 'entry') {
        $lastTicket = Ticket::where("facility_id", $gate->facility_id)->whereNull("checkout_time")->orderBy("id", "DESC")->first();  
        if(isset($lastTicket->license_plate) && $lastTicket->license_plate == $request->transit['plate']['text'] && $lastTicket->is_checkout == '0'){
            return "Duplicate Feed";
        }
        $this->saveCheckin($request, $gate);
        echo "Checkin Done";
        $this->log->info("Checkin Done");
      } elseif ($gate->gate_type == 'exit') {
          $this->saveCheckout($request, $gate);
          echo "Checkout Done";
          $this->log->info("Checkout Done");
      } else {
          echo "Invalid Gate";
          $this->log->info("Invalid Gate");
      }
    } else {
        echo "Lane Id not matched";
        $this->log->info("Lane Id not matched");
    }
    return "Feed done";
  }

  public function saveCheckin($request, $gate)
    {
        $this->log->info("before checkin");
        try {
            $plate = $request->transit['plate']['text'];
            $alreadyCheckin = $this->alreadyCheckinStatus($plate, $gate->facility_id);
            if ($alreadyCheckin) {
                $this->log->info("You have already checked-in.");
                return true;
            }
            $ticket = new Ticket();
            $facility = Facility::with(['facilityConfiguration'])->find($gate->facility_id);
            $isPermit = 0;
            if($facility->facilityConfiguration->is_check_cloud_permit == '1'){
                //check permit exist
                $permit = $this->checkExistingPermit($request, $gate);
                if(count($permit) > 0){
                    $isPermit = 1;
                    $ticket->permit_request_id = $permit->id;
                    $ticket->user_id = $permit->user_id;
                }
            }
            $checkinTime = date("Y-m-d H:i:s", strtotime($request->entry_time));
            $ticket->check_in_datetime = $checkinTime;
            $ticket->checkin_time = $checkinTime;
            $ticket->is_checkin = '0';
            
            $reservation = [];
            if($isPermit != 1){
                $reservation = $this->checkExistingReservation($plate, $gate->facility_id);
                if (count($reservation) > 0) {
                    $response_type = '1';
                    $this->log->info("reservation checkin");
                    $resevationExitTime = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                    $resevationExitTime = Carbon::parse($resevationExitTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
                    $ticket->reservation_id = $reservation->id;
                    $ticket->check_in_datetime = $reservation->start_timestamp;
                    $ticket->checkout_datetime = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->estimated_checkout = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->payment_date = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->length = $reservation->length;
                    $ticket->is_checkin = '1';
                    $ticket->is_checkout = '1';
                    $ticket->user_id = $reservation->user_id;
                    $ticket->session_id = $reservation->session_id;
                    $this->log->info("reservation check : ".$reservation->id);

                    $ticket->facility_id = $gate->facility_id;
                    $ticket->partner_id = self::PARTNER_ID;
                    $ticketNumber = $this->checkTicketNumber($gate->facility_id);
                    $isExist = Ticket::where('ticket_number', $ticketNumber)->first();
                    if($isExist){
                        $ticket->ticket_number = $this->checkTicketNumber($gate->facility_id);
                    }else{
                        $ticket->ticket_number = $ticketNumber;
                    }
                    $ticket->license_plate = $plate;
                    $ticket->checkin_gate = $gate->gate;
                    $ticket->device_type = "LPR";
                    $ticket->save();

                    if (count($reservation) > 0) {
                        $reservation->is_ticket = '1';
                        $reservation->save();
                    }

                    if ($ticket->user_id != '') {

                        $user = User::find($ticket->user_id);
                        if ($user->email != '') {
                            Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
                        }
                        
                        $facilityName = ucwords($facility->full_name);
                        //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                        if ($partnerDetails->user_id == self::PARTNER_ID) {
                        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                            $join->on('user_facilities.user_id', '=', 'users.id');
                            $join->where('user_facilities.facility_id', "=", $facility->id);
                        })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                        $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                        } else {
                        $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        }
                        $url = env('TOUCHLESS_WEB_URL');
                        $grace_period = $facility->grace_period_minute;
                        $ticket_number = base64_encode($ticket->ticket_number);
                        $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                        if($isPermit == 1){
                            $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number.";
                        }
                        dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
                    }
                    
                    $this->log->info("checkin done {$ticket->ticket_number}");
                    // return true;
                }
            }
            
            if($isPermit == 1){
                $ticket->is_checkin = '1';
                $response_type = '1';
                $ticket->facility_id = $gate->facility_id;
                $ticket->partner_id = self::PARTNER_ID;
                $ticketNumber = $this->checkTicketNumber($gate->facility_id);
                $isExist = Ticket::where('ticket_number', $ticketNumber)->first();
                if($isExist){
                    $ticket->ticket_number = $this->checkTicketNumber($gate->facility_id);
                }else{
                    $ticket->ticket_number = $ticketNumber;
                }
                $ticket->license_plate = $plate;
                $ticket->checkin_gate = $gate->gate;
                $ticket->device_type = "LPR";
                $ticket->save();

                if ($ticket->user_id != '') {

                    $user = User::find($ticket->user_id);
                    if ($user->email != '') {
                        Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
                    }
                    
                    $facilityName = ucwords($facility->full_name);
                    //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                    $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                    if ($partnerDetails->user_id == self::PARTNER_ID) {
                      $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                      })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                      $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                    } else {
                      $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                    }
                    $url = env('TOUCHLESS_WEB_URL');
                    $grace_period = $facility->grace_period_minute;
                    $ticket_number = base64_encode($ticket->ticket_number);
                    $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                    if($isPermit == 1){
                        $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number.";
                    }
                    dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
                }

                if ($license_plate != '') {
                    if ($facility->license_plate_model != '') {
                        $NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
                        $licensePlate = $NamespacedModel::where("license_plate", $plate)->where("gate_type", "entry")->delete();
                    }
                }

                $this->log->info("checkin done {$ticket->ticket_number}");
                return true;
            }
            
            // Dispatch the single email notification for the specific license plate
            if (!empty($plate)) {
                Artisan::queue('notification:ungated-lpr-notification-email', [
                    'partnerId' => $gate->partner_id,
                    'facilityId' => $facility->id,
                    'licensePlate' => $plate
                ]);
                $this->log->info("Notification dispatched for plate: {$plate}");
            }

            return true;
            
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error("error received --" . $msg);
            Mail::send(
                "parkengage.notify-mq-listner",
                ['is_failed' => '1', 'msg' => $msg],
                function ($message) {
                    $message->to(['<EMAIL>', '<EMAIL>'])->subject("Woodland Info : Woodland Exception");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            return true;
        }
    }

  public function bkpsaveCheckin($request, $gate)
    {
        $this->log->info("before checkin");
        try {
            $alreadyCheckin = $this->alreadyCheckinStatus($request);
            if ($alreadyCheckin) {
                $this->log->info("You have already checked-in.");
                return true;
            }
            $ticket = new Ticket();
            $facility = Facility::with(['facilityConfiguration'])->find($gate->facility_id);
            $isPermit = 0;
            if($facility->facilityConfiguration->is_check_cloud_permit == '1'){
                //check permit exist
                $permit = $this->checkExistingPermit($request);
                if(count($permit) > 0){
                    $isPermit = 1;
                    $ticket->permit_request_id = $permit->id;
                    $ticket->user_id = $permit->user_id;
                }
            }
            $checkinTime = date("Y-m-d H:i:s", strtotime($request->entry_time));
            $ticket->check_in_datetime = $checkinTime;
            $ticket->checkin_time = $checkinTime;
            $ticket->is_checkin = '0';
            
            $reservation = [];
            if($isPermit != 1){
                $reservation = $this->checkExistingReservation($request->license_plate, $request->facility_id);
                if (count($reservation) > 0) {
                    $response_type = '1';
                    $this->log->info("reservation checkin");
                    $resevationExitTime = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                    $resevationExitTime = Carbon::parse($resevationExitTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
                    $ticket->reservation_id = $reservation->id;
                    $ticket->check_in_datetime = $reservation->start_timestamp;
                    $ticket->checkout_datetime = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->estimated_checkout = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->payment_date = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                    $ticket->length = $reservation->length;
                    $ticket->is_checkin = '1';
                    $ticket->is_checkout = '1';
                    $ticket->user_id = $reservation->user_id;
                    $ticket->session_id = $reservation->session_id;
                    $this->log->info("reservation check : ".$reservation->id);

                    $ticket->facility_id = $gate->facility_id;
                    $ticket->partner_id = self::PARTNER_ID;
                    $ticketNumber = $this->checkTicketNumber($gate->facility_id);
                    $isExist = Ticket::where('ticket_number', $ticketNumber)->first();
                    if($isExist){
                        $ticket->ticket_number = $this->checkTicketNumber($gate->facility_id);
                    }else{
                        $ticket->ticket_number = $ticketNumber;
                    }
                    $ticket->license_plate = $request->license_plate;
                    $ticket->checkin_gate = $gate->gate;
                    $ticket->device_type = "LPR";
                    $ticket->save();

                    if (count($reservation) > 0) {
                        $reservation->is_ticket = '1';
                        $reservation->save();
                    }

                    if ($ticket->user_id != '') {

                        $user = User::find($ticket->user_id);
                        if ($user->email != '') {
                            Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
                        }
                        
                        $facilityName = ucwords($facility->full_name);
                        //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                        if ($partnerDetails->user_id == self::PARTNER_ID) {
                        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                            $join->on('user_facilities.user_id', '=', 'users.id');
                            $join->where('user_facilities.facility_id', "=", $facility->id);
                        })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                        $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                        } else {
                        $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        }
                        $url = env('TOUCHLESS_WEB_URL');
                        $grace_period = $facility->grace_period_minute;
                        $ticket_number = base64_encode($ticket->ticket_number);
                        $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                        if($isPermit == 1){
                            $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number.";
                        }
                        dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
                    }
                    
                    $this->log->info("checkin done {$ticket->ticket_number}");
                    return true;
                }
            }
            
            if($isPermit == 1){
                $ticket->is_checkin = '1';
                $response_type = '1';
                $ticket->facility_id = $gate->facility_id;
                $ticket->partner_id = self::PARTNER_ID;
                $ticketNumber = $this->checkTicketNumber($gate->facility_id);
                $isExist = Ticket::where('ticket_number', $ticketNumber)->first();
                if($isExist){
                    $ticket->ticket_number = $this->checkTicketNumber($gate->facility_id);
                }else{
                    $ticket->ticket_number = $ticketNumber;
                }
                $ticket->license_plate = $request->license_plate;
                $ticket->checkin_gate = $gate->gate;
                $ticket->device_type = "LPR";
                $ticket->save();

                if ($ticket->user_id != '') {

                    $user = User::find($ticket->user_id);
                    if ($user->email != '') {
                        Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
                    }
                    
                    $facilityName = ucwords($facility->full_name);
                    //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                    $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                    if ($partnerDetails->user_id == self::PARTNER_ID) {
                      $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                      })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                      $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                    } else {
                      $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                    }
                    $url = env('TOUCHLESS_WEB_URL');
                    $grace_period = $facility->grace_period_minute;
                    $ticket_number = base64_encode($ticket->ticket_number);
                    $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                    if($isPermit == 1){
                        $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number.";
                    }
                    dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
                }

                if ($request->license_plate != '') {
                    if ($facility->license_plate_model != '') {
                        $NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
                        $licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate_type", "entry")->delete();
                    }
                }

                $this->log->info("checkin done {$ticket->ticket_number}");
                return true;
            }
            return true;
            
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error("error received --" . $msg);
            Mail::send(
                "parkengage.notify-mq-listner",
                ['is_failed' => '1', 'msg' => $msg],
                function ($message) {
                    $message->to(['<EMAIL>', '<EMAIL>'])->subject("Woodland Info : Woodland Exception");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            return true;
        }
    }

    public function checkExistingReservation($plate, $facility_id)
    {
        $reservation = Reservation::with('user')->where("license_plate", $plate)->where("facility_id", $facility_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
        if (!$reservation) {
            return $reservation;
        }
        return $reservation;
    }

    public function alreadyCheckinStatus($plate, $facility_id)
    {   
        $ticket = Ticket::where("license_plate", $plate)->where("facility_id", $facility_id)->where("is_checkin", '1')->where("is_checkout", '0')->first();
        return $ticket;
    }


    public function sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type)
    {
        $this->log->info("before queue {$response_type}");
        $msgRespone = [];
        $msgRespone = (object) $msg;
        $this->log->info("Json response  ".json_encode(['eticket_id'=>$ticket['ticket_number'],'license_plate'=>$ticket['license_plate'], 'data'=>$msgRespone, 'response_type' => $response_type]));
        $myArray = json_encode(['eticket_id'=>$ticket['ticket_number'],'license_plate'=>$ticket['license_plate'], 'data'=>$msgRespone, 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
		    if($queue_name == ''){
                $queue_name = self::QUEUE_ENTRY;
            }
        $queue->pushRaw($myArray, $queue_name);
        $this->log->info("data send ". $queue_name);
		//Artisan::queue('read-license-plate', array('license_plate' => $ticket->license_plate, 'is_checkin_or_checkout' => '1', 'ticket' => $ticket, 'response_type' => $response_type, "msg" => $msg, 'queue_name' => $queue_name, 'gate_type' => $gate_type));
        return $ticket;
    }


    //tickets number is total 7 digit number for kstreet
    protected function checkTicketNumber($facility_id = '')
    {
        if ($facility_id == '') {
            $ticket = 'WP' . rand(100, 999) . rand(100, 999);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $this->checkTicketNumber();
            }
        } else {
            $facility = Facility::find($facility_id);
            $prefix = $facility->ticket_prefix != '' ? $facility->ticket_prefix : "KT";
            $fromRange = $facility->from_range != '' ? $facility->from_range : 100000;
            $toRange = $facility->to_range != '' ? $facility->to_range : 999999;
            $ticket = $prefix . rand($fromRange, $toRange);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $this->checkTicketNumber($facility_id);
            }
        }

        return $ticket;
    }


    public function saveCheckout($request, $gate)
    {

        $this->log->info("checkout start");
        $plate = $request->transit['plate']['text'];
        $ticket = Ticket::with(['facility.FacilityPaymentDetails', 'reservation','user'])->where('license_plate', $plate)->where('facility_id', $gate->facility_id)->where('is_closed', '0')->orderBy("id", "DESC")->first();
        if ($ticket) {

            $is_our_ticket = '1';
            if ($ticket->is_checkin == '0' && $ticket->is_checkout == '0') {
                $is_our_ticket = '0';
            }

            $checkinDate = date("Y-m-d", strtotime($ticket->checkin_time));
            $today = date("Y-m-d", strtotime($request->entry_time));
            $checkoutTime = date("Y-m-d H:i:s", strtotime($request->entry_time));
            
            $updatedCheckinTime = '';
            if (strtotime($today) != strtotime($checkinDate)) {
                $updatedCheckinTime = date("d M", strtotime($checkinDate)) . ' ' . date("g:i A", strtotime($ticket->checkin_time));
            } else {
                $updatedCheckinTime = date("g:i A", strtotime($ticket->checkin_time));
            }


            //permit check before checkout

            if($ticket->permit_request_id != ''){
                $ticket->is_checkout = '1';
                $ticket->checkout_gate = $gate->gate;
                $ticket->checkout_time = $checkoutTime;
                $ticket->checkout_datetime = $checkoutTime;
                $ticket->estimated_checkout = $checkoutTime;
                $ticket->payment_date = $checkoutTime;
                $ticket->checkout_license_plate = $plate;
                $ticket->checkout_session_id = $ticket->session_id;
                $ticket->checkout_mode = '4';
                $ticket->is_transaction_status = '0';
                $ticket->is_closed = '1';
                $this->log->info(json_encode($ticket));
                $ticket->save();

                
                if(isset($ticket->user->phone)){
                    $this->log->info("SMS condition entered");
                    $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                    if ($partnerDetails->user_id == self::PARTNER_ID) {
                        $facility = $ticket->facility;
                      $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                      })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                      $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                    } else {
                      $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                    }
                    $url = env('RECEIPT_URL');
                    $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                    $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
        
                    dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));

                    if ($request->license_plate != '') {
                        if ($ticket->facility->license_plate_model != '') {
                            $NamespacedModel = 'App\\Models\\ParkEngage\\' . $ticket->facility->license_plate_model;
                            $licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate_type", "exit")->delete();
                        }
                    }
                }
                return true;
            }

            //overstay section start
            if ($ticket->estimated_checkout != '') {
                $overstayExist = TicketExtend::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();
                if ($overstayExist) {
                    if (strtotime($overstayExist->checkout_time) >= strtotime($checkoutTime)) {
                        $ticket->is_checkout = '1';
                        $ticket->checkout_gate = $gate->gate;
                        //$ticket->checkout_time = $checkoutTime;
                        //$ticket->checkout_datetime = $checkoutTime;
                        $ticket->checkout_license_plate = $plate;
                        $ticket->checkout_session_id = $ticket->session_id;
                        $ticket->checkout_mode = '4';
                        $ticket->is_transaction_status = '0';
                        $ticket->is_closed = '1';
                        $this->log->info(json_encode($ticket));
                        $ticket->save();

                        return true;
                    } else {
                        $this->log->info("eticket already extend created.");
                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->checkout_time);
                        $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->checkout_time);
                        $checkoutTime = Carbon::createFromFormat('Y-m-d H:i:s', $checkoutTime);
                        $isMember = 0;
                        if ($ticket->facility->rate_duration_in_hours > 0 && $ticket->facility->rate_per_hour > 0 && $ticket->facility->rate_free_minutes > 0 && $ticket->facility->rate_daily_max_amount > 0) {
                            $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
                        } else {
                            $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
                        }

                        if ($rate['price'] <= 0) {
                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            //$ticket->checkout_time = $checkoutTime;
                            //$ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $plate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $ticket->is_closed = '1';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();
                            return true;
                        }

                        $taxRate = $ticket->getTaxRate($rate);          // to get tax price
                        $rate['price'] = $rate['price'] + $taxRate;
                        $maxStay = isset($rate['max_stay']) ? $rate['max_stay'] : $diff_in_hours;
                        if ($ticket->facility->is_lpr_cloud_payment_enabled == '1') {

                            //$ticket->is_transaction_status = '1';
                            //$ticket->save();
                            $this->log->info("is_lpr_cloud_payment_enabled entered for overstay");
                            
                            if(isset($ticket->facility->FacilityPaymentDetails->facility_payment_type_id) && $ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2'){
                                
                                $cardCheck = DatacapTransaction::whereNull('deleted_at')->where('ticket_id', $ticket->id)->orderBy("id", "DESC")->first();
                                if (!$cardCheck) {
                                    throw new ApiGenericException("Payment Profile Not Found.");
                                }
                                $request->request->add(['expiration' => $cardCheck->expiry]);
                                $request->request->add(['card_last_four' => $cardCheck->card_last_four]);
                                $card_month = substr($request->expiry, 0, 2);
                                $card_year = substr($request->expiry, -2);
                                $request->request->add(['expiration_month' => $card_month]);
                                $request->request->add(['expiration_year' => $card_year]);
                                $this->log->info("Payment Profile Data --" . $cardCheck);

                                $data['Token'] = $cardCheck->token;
                                $total = $rate['price'];
                                if ($total > 0) {
                                    $amount = ($ticket->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $total;
                                    $data['Amount'] = $amount;
                                    $data['Token'] = $cardCheck->token;
                                    $data['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                    $data["CardHolderID"] = "Allow_V2";
                                    $ecommerce_mid = $ticket->facility->facilityPaymentDetails->datacap_ecommerce_mid;
                                    $url = $ticket->facility->facilityPaymentDetails->datacap_script_url;
                                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                    if ($paymentResponse["Status"] == "Error") {
                                        $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                        } else {
                                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                        }
                                    }
                                } else {
                                    throw new ApiGenericException("Payment Token Not Generated. Please try again after sometime.");
                                }

                                if ($paymentResponse['Status'] == 'Approved') {
                                    $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $ticket->user_id, '');
                                    $this->log->info("Payment Transaction Data  --". $authorized_anet_transaction);  
                                    $ticketextend = new TicketExtend();

                                    $ticketextend->anet_transaction_id = $authorized_anet_transaction->id;  

                                    $ticketextend->ticket_id = $ticket->id;
                                    $ticketextend->length = $diff_in_hours;
                                    $ticketextend->ticket_number = $ticket->ticket_number;
                                    $ticketextend->facility_id = $ticket->facility->id;
                                    $ticketextend->partner_id = $ticket->partner_id;
                                    $ticketextend->total = $total;
                                    $ticketextend->grand_total = $total;
                                    $ticketextend->checkin_time = date("Y-m-d H:i:s", strtotime($arrival_time));
                                    $ticketextend->checkout_time = date("Y-m-d H:i:s", strtotime($checkoutTime));
                                    $ticketextend->tax_fee = $taxRate;
                                    $ticketextend->processing_fee = 0.00;
                                    $ticketextend->is_priceband_apply = (isset($request->rate_band_id) && !empty($request->rate_band_id)) ? 1 : 0;
                                    $ticketextend->base_length = $maxStay;
                                    $ticketextend->save();
                                    
                                    $ticket->is_extended = '1';
                                    $ticket->is_closed = '1';
                                    $ticket->save();
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }   
                            }else if(isset($ticket->facility->FacilityPaymentDetails->facility_payment_type_id) && $ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4'){
                                
                                $cardCheck = DatacapTransaction::whereNull('deleted_at')->where('ticket_id', $ticket->id)->first();
                                $this->log->info("Payment Profile Data --". $cardCheck);
                                if($cardCheck){
                                    $data['transactionId'] = $cardCheck->trans_id;
                                    $final_amount = $rate['price'];
                                    $amount = ($ticket->facility->FacilityPaymentDetails->heartland_payment_env=='test') ? '3.00': $final_amount;
                                    if($final_amount>0){
                                        try{
                                            $amount = number_format($amount, 2); 
                                            $paymentRequest = new Request([
                                                'token'   => $cardCheck->token,
                                                'Amount' => $amount
                                            ]);
                                            
                                            $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($paymentRequest,$ticket->facility);
                                            $this->log->info("Payment record -- " . json_encode($paymentResponse));
                                            
                                            if ($paymentResponse->responseMessage == 'APPROVAL') {
                                                $request = new Request([
                                                    'total'   => $final_amount,
                                                    'card_last_four' => $cardCheck->card_last_four,
                                                    'expiration' => $cardCheck->expiry,
                                                    'expiration_date' => $cardCheck->expiry
                                                ]);
                                                $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request,$paymentResponse, $ticket->user_id);
                                                $this->log->info("Payment Transaction Data  --". $authorized_anet_transaction);  
                                                $ticketextend = new TicketExtend();

                                                $ticketextend->anet_transaction_id = $authorized_anet_transaction->id;  

                                                $ticketextend->ticket_id = $ticket->id;
                                                $ticketextend->length = $diff_in_hours;
                                                $ticketextend->ticket_number = $ticket->ticket_number;
                                                $ticketextend->facility_id = $ticket->facility->id;
                                                $ticketextend->partner_id = $ticket->partner_id;
                                                $ticketextend->total = $final_amount;
                                                $ticketextend->grand_total = $final_amount;
                                                $ticketextend->checkin_time = date("Y-m-d H:i:s", strtotime($arrival_time));
                                                $ticketextend->checkout_time = date("Y-m-d H:i:s", strtotime($checkoutTime));
                                                $ticketextend->tax_fee = $taxRate;
                                                $ticketextend->processing_fee = 0.00;
                                                $ticketextend->is_priceband_apply = (isset($request->rate_band_id) && !empty($request->rate_band_id)) ? 1 : 0;
                                                $ticketextend->base_length = $maxStay;
                                                $ticketextend->save();
                                                
                                                $ticket->is_extended = '1';
                                                $ticket->is_closed = '1';
                                                $ticket->save();
                                            }
                                            
                                        }catch(\Exception $e) {
                                            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                                            $this->log->error($msg);            
                                            dd($msg);
                                        }
                                    }                                
                                }          
                            }

                            
                                            
                            return true;
                        }

                        return true;
                    }
                } else {
                    if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
                    } else {
                        $this->log->info("eticket extend created.");
                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->estimated_checkout);
                        $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->estimated_checkout);
                        $checkoutTime = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                        $isMember = 0;
                        if ($ticket->facility->rate_duration_in_hours > 0 && $ticket->facility->rate_per_hour > 0 && $ticket->facility->rate_free_minutes > 0 && $ticket->facility->rate_daily_max_amount > 0) {
                            $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
                        } else {
                            $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
                        }
                        if ($rate['price'] <= 0) {
                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            //$ticket->checkout_time = $checkoutTime;
                            //$ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $plate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $ticket->is_closed = '1';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();
                            return true;
                        }

                        $taxRate = $ticket->getTaxRate($rate);          // to get tax price
                        $rate['price'] = $rate['price'] + $taxRate;
                        $maxStay = isset($rate['max_stay']) ? $rate['max_stay'] : $diff_in_hours;
                        if ($ticket->facility->is_lpr_cloud_payment_enabled == '1') {

                            //$ticket->is_transaction_status = '1';
                            //$ticket->save();
                            $this->log->info("is_lpr_cloud_payment_enabled entered for overstay");
                            if(isset($ticket->facility->FacilityPaymentDetails->facility_payment_type_id) && $ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2'){
                                
                                $cardCheck = DatacapTransaction::whereNull('deleted_at')->where('ticket_id', $ticket->id)->orderBy("id", "DESC")->first();
                                if (!$cardCheck) {
                                    throw new ApiGenericException("Payment Profile Not Found.");
                                }
                                $request->request->add(['expiration' => $cardCheck->expiry]);
                                $request->request->add(['card_last_four' => $cardCheck->card_last_four]);
                                $card_month = substr($request->expiry, 0, 2);
                                $card_year = substr($request->expiry, -2);
                                $request->request->add(['expiration_month' => $card_month]);
                                $request->request->add(['expiration_year' => $card_year]);
                                $this->log->info("Payment Profile Data --" . $cardCheck);

                                $data['Token'] = $cardCheck->token;
                                $total = $rate['price'];
                                if ($total > 0) {
                                    $amount = ($ticket->facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $total;
                                    $data['Amount'] = $amount;
                                    $data['Token'] = $cardCheck->token;
                                    $data['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                                    $data["CardHolderID"] = "Allow_V2";
                                    $ecommerce_mid = $ticket->facility->facilityPaymentDetails->datacap_ecommerce_mid;
                                    $url = $ticket->facility->facilityPaymentDetails->datacap_script_url;
                                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                                    if ($paymentResponse["Status"] == "Error") {
                                        $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                        } else {
                                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                        }
                                    }
                                } else {
                                    throw new ApiGenericException("Payment Token Not Generated. Please try again after sometime.");
                                }

                                if ($paymentResponse['Status'] == 'Approved') {
                                    $authorized_anet_transaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $ticket->user_id, '');
                                    $this->log->info("Payment Transaction Data  --". $authorized_anet_transaction);  
                                    $ticketextend = new TicketExtend();

                                    $ticketextend->anet_transaction_id = $authorized_anet_transaction->id;  

                                    $ticketextend->ticket_id = $ticket->id;
                                    $ticketextend->length = $diff_in_hours;
                                    $ticketextend->ticket_number = $ticket->ticket_number;
                                    $ticketextend->facility_id = $ticket->facility->id;
                                    $ticketextend->partner_id = $ticket->partner_id;
                                    $ticketextend->total = $total;
                                    $ticketextend->grand_total = $total;
                                    $ticketextend->checkin_time = date("Y-m-d H:i:s", strtotime($arrival_time));
                                    $ticketextend->checkout_time = date("Y-m-d H:i:s", strtotime($checkoutTime));
                                    $ticketextend->tax_fee = $taxRate;
                                    $ticketextend->processing_fee = 0.00;
                                    $ticketextend->is_priceband_apply = (isset($request->rate_band_id) && !empty($request->rate_band_id)) ? 1 : 0;
                                    $ticketextend->base_length = $maxStay;
                                    $ticketextend->save();
                                    
                                    $ticket->is_extended = '1';
                                    $ticket->is_closed = '1';
                                    $ticket->save();
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }   
                            }else{
                            $cardCheck = DatacapTransaction::whereNull('deleted_at')->where('ticket_id', $ticket->id)->first();
                            $this->log->info("Payment Profile Data --". $cardCheck);
                            if($cardCheck){
                                $data['transactionId'] = $cardCheck->trans_id;
                                $final_amount = $rate['price'];
                                $amount = ($ticket->facility->FacilityPaymentDetails->heartland_payment_env=='test') ? '3.00': $final_amount;
                                if($final_amount>0){
                                    try{
                                        $amount = number_format($amount, 2); 
                                        $paymentRequest = new Request([
                                            'token'   => $cardCheck->token,
                                            'Amount' => $amount
                                        ]);
                                        
                                        $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($paymentRequest,$ticket->facility);
                                        $this->log->info("Payment record -- " . json_encode($paymentResponse));
                                        
                                        if ($paymentResponse->responseMessage == 'APPROVAL') {
                                            $request = new Request([
                                                'total'   => $final_amount,
                                                'card_last_four' => $cardCheck->card_last_four,
                                                'expiration' => $cardCheck->expiry,
                                                'expiration_date' => $cardCheck->expiry
                                            ]);
                                            $authorized_anet_transaction = HeartlandPaymentGateway::saveTransaction($request,$paymentResponse, $ticket->user_id);
                                            $this->log->info("Payment Transaction Data  --". $authorized_anet_transaction);  
                                            $ticketextend = new TicketExtend();

                                            $ticketextend->anet_transaction_id = $authorized_anet_transaction->id;  

                                            $ticketextend->ticket_id = $ticket->id;
                                            $ticketextend->length = $diff_in_hours;
                                            $ticketextend->ticket_number = $ticket->ticket_number;
                                            $ticketextend->facility_id = $ticket->facility->id;
                                            $ticketextend->partner_id = $ticket->partner_id;
                                            $ticketextend->total = $final_amount;
                                            $ticketextend->grand_total = $final_amount;
                                            $ticketextend->checkin_time = date("Y-m-d H:i:s", strtotime($arrival_time));
                                            $ticketextend->checkout_time = date("Y-m-d H:i:s", strtotime($checkoutTime));
                                            $ticketextend->tax_fee = $taxRate;
                                            $ticketextend->processing_fee = 0.00;
                                            $ticketextend->is_priceband_apply = (isset($request->rate_band_id) && !empty($request->rate_band_id)) ? 1 : 0;
                                            $ticketextend->base_length = $maxStay;
                                            $ticketextend->save();
                                            
                                            $ticket->is_extended = '1';
                                            $ticket->is_closed = '1';
                                            $ticket->save();
                                        }
                                        
                                    }catch(\Exception $e) {
                                        $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                                        $this->log->error($msg);
                                    }
                                }                                
                            }
                        }
                            return true;
                        }
                    }
                }
            }

            if ($plate != '') {
                if ($ticket->facility->license_plate_model != '') {
                    $NamespacedModel = 'App\\Models\\ParkEngage\\' . $ticket->facility->license_plate_model;
                    $licensePlate = $NamespacedModel::where("license_plate", $plate)->where("gate_type", "entry")->delete();
                }
            }
            //overstay section end

            $ticket->checkout_license_plate = $plate;
            //$ticket->checkout_session_id = $ticket->session_id;
            $ticket->checkout_mode = '4';
            $ticket->is_transaction_status = '0';
            //$ticket->is_closed = '1';
            $this->log->info("main ticket checkout : ". $ticket->ticket_number);
            $ticket->save();
            

            return true;
        } else {
            $this->log->info("no checkin found in checkout function");
        }
    }


    public function saveOverstayDetails($rate, $ticket, $facility)
    {

        $rate_id = isset($rate['id']) ? $rate['id'] : '';
        $rate_description = isset($rate['description']) ? $rate['description'] : '';

        $currentTime = Carbon::parse('now');
        $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
        $overstay = new OverstayTicket();
        $overstay->user_id = $ticket->user_id;
        $overstay->facility_id = $ticket->facility_id;
        $overstay->ticket_number = $ticket->ticket_number;
        $overstay->is_checkin = '1';
        $overstay->is_checkout = '1';
        $overstay->check_in_datetime = $ticket->estimated_checkout;
        $overstay->checkout_datetime = $estimated_checkout;
        $overstay->estimated_checkout = $estimated_checkout;
        $overstay->partner_id = $ticket->partner_id;
        $overstay->ticket_id = $ticket->id;
        $overstay->payment_date = date("Y-m-d H:i:s");
        $overstay->rate_id = $rate_id;
        $overstay->rate_description = $rate_description;
        $overstay->reservation_id = $ticket->reservation_id;
        $overstay->save();

        return $overstay;
    }

    

    public function saveLicensePlate($request, $gate)
    {
        //WoodmanLicensePlate::where("facility_id", $request->facility_id)->delete();

        // $image = $request->file('image');
        // if($image != ''){
        //     $file_extension = $image->getClientOriginalExtension();
        //     $file_name =  $request->gate_id.'_'.$request->license_plate. '_'.rand(1001, 9999).'.' . $file_extension;
        //     $destination_path = storage_path("app/woodman-license-plate");
        //     $license['image'] = $file_name;
        //     if(!$image->move($destination_path, $file_name)) {
        //         throw new ApiGenericException("Something went wrong while upload image.");
        //     }
        //     $this->log->info("image uploaded");
        // }
        
        $license['license_plate'] = $request->transit['plate']['text'];
        $license['partner_id'] =  self::PARTNER_ID;
        //$license['make'] = $request->make;
        //$license['model'] = $request->model;
        //$license['year'] = $request->year;
        //$license['color'] = $request->color;
        $license['facility_id'] =  $gate->facility_id;
        $license['gate'] = $gate->gate;
        $license['gate_type'] = $gate->gate_type;
        $license['entry_time'] = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
        $result = WoodmanLicensePlate::create($license);
        return $result;
    }


    public function bkpsaveLicensePlate($request)
    {
        //WoodmanLicensePlate::where("facility_id", $request->facility_id)->delete();

        $image = $request->file('image');
        if($image != ''){
            $file_extension = $image->getClientOriginalExtension();
            $file_name =  $request->gate_id.'_'.$request->license_plate. '_'.rand(1001, 9999).'.' . $file_extension;
            $destination_path = storage_path("app/woodman-license-plate");
            $license['image'] = $file_name;
            if(!$image->move($destination_path, $file_name)) {
                throw new ApiGenericException("Something went wrong while upload image.");
            }
            $this->log->info("image uploaded");
        }
        
        $license['license_plate'] = $request->license_plate;
        $license['partner_id'] =  self::PARTNER_ID;
        $license['make'] = $request->make;
        $license['model'] = $request->model;
        $license['year'] = $request->year;
        $license['color'] = $request->color;
        $license['facility_id'] =  $request->facility_id;
        $license['gate'] = $request->gate_id;
        $license['gate_type'] = $request->gate_type;
        $license['entry_time'] = date("Y-m-d H:i:s", strtotime($request->entry_time));
        $result = WoodmanLicensePlate::create($license);
        return $result;
    }


    public function checkExistingPermit($request, $facility_id)
    {
        $permitVehicles = PermitVehicle::where("license_plate_number", $request->transit['plate']['text'])->where("partner_id", self::PARTNER_ID)->get();
        if (count($permitVehicles) > 0) {
            foreach ($permitVehicles as $key => $permitVehicle) {
                $permitRequests = PermitRequest::where('user_id', $permitVehicle->user_id)->where('facility_id', $facility_id)->whereDate('grace_end_date', '>=', date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start'])))->whereNull("cancelled_at")->orderBy("id", "DESC")->get();
                if (count($permitRequests) > 0) {
                    foreach ($permitRequests as $key => $permitRequest) {
                    $mapping = PermitVehicleMapping::where("permit_vehicle_id", $permitVehicle->id)->where("permit_request_id", $permitRequest->id)->first();
                    if (!$mapping) {
                        continue;
                    }
                    return $permitRequest;
                    }
                }else{
                    return $permitRequests;
                }
            }
        }
        return $permitVehicles;
       
    }

}
