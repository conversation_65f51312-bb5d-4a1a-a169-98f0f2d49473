<?php
namespace App\Http\Controllers\ParkEngage;

use Auth;
use Exception;
use Response;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Carbon\Carbon;
use App\Services\LoggerFactory;
use App\Classes\DataTransGateway;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

class DataTransController extends Controller
{
	
	public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/datatrans')->createLogger('datatrans');
        $this->request = $request;
    }

    public function index(Request $request)
    {
		$user_id = Auth::user()->id;		
    }

    public function store(Request $request)
    {  
        $this->log->info("Request Data Trans: " . json_encode($request->all()));	
        $user_id = Auth::user()->id;
        $partner_id = Auth::user()->created_by;		        
    }


    public function destroy($id) {
		
	}
   
	public function getDataTransProfile(Request $request)
    {
		$user_id = Auth::user()->id;		
    }
	
	// New API for Payment Token in Google Pay and Apple Pay

	public function getDataTransPaymentToken(Request $request)
    {
		$facility = Facility::with(['FacilityPaymentDetails'])->find($request->facility_id);
		
		if (($facility->FacilityPaymentDetails->data_trans_username == '') && ($facility->FacilityPaymentDetails->data_trans_password =='')) {
            throw new ApiGenericException('Payment Details Not Set for this facility.');
        }
		$result = DataTransGateway::dataTransPaymentToken($facility, $request);
		return $result;
    }

	// New API for Payment Token in Google Pay and Apple Pay Web

	public function getDataTransPaymentTokenWeb(Request $request)
    {
		if($request->header('X-ClientSecret') !='')
		{ 
			$secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
			if(!$secret){
				throw new NotFoundException('No partner found.');
			}
			$facility = Facility::with(['FacilityPaymentDetails'])->find($request->facility_id);
		
			if (($facility->FacilityPaymentDetails->data_trans_username == '') && ($facility->FacilityPaymentDetails->data_trans_password =='')) {
				throw new ApiGenericException('Payment Details Not Set for this facility.');
			}
			$result = DataTransGateway::dataTransPaymentToken($facility, $request);
			return $result;
		}else{
			throw new ApiGenericException('No partner found.');
		}
		
    }

	

  
}
