<?php

namespace App\Http\Controllers\ParkEngage;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Classes\TCP\TCPConnection;
//use App\Classes\TCP\TCPCallback as TCPCallback;

class TCP<PERSON>ontroller extends Controller// implements TCPCallback
{

   public $socket;

    public function index() {
        $this->socket = new TCPConnection("*************", 10200, $this);
        $this->socket->connect(); //connection to socket
        $data = array("username" => "foo", "pass" => "bar");
        //TCPConnection::send("testing socket TCP dari viyancs"); // format $only
        //TCPConnection::emit("login",  array((object)($data),  (object)($data1))); //multy dimension json
        $this->socket->emit("login", array($data)); //one array dimension json
        //$this->socket->emit("login", array($data));

        $isGateOpened = false;
        try
        {
          // Create Session Command
          $lengthHeader = "00060";
          $headerType = "0";
          $consecutiveNumber = "00000";
          $facility = "1709138";
          $date = date("Ymdhs");
          $requestType = "0";
          $messageGroup = "000";
          $messageNumber = "0000";
          $version = "01";
          $reserved = "             ";
          $length = "00005";
          $gate = 23;

          // Manual Open Command
          $messageNumber = "0000";
          $deviceID = "00" + $gate; //"0023";
          $length = "00004";
          $consecutiveNumber = "00001";
          $messageGroup = "003";
          $headerGateOpen = $lengthHeader.$headerType.$facility.$date.$requestType.$consecutiveNumber.$messageGroup.$messageNumber.$version.$length.$reserved.$deviceID;
          
          //$serverStream = $this->socket->GetStream();
          $this->socket->send($this->socket->create, $headerGateOpen); //connection to socket
          /*dd(socket_read($this->socket->getSocket(), 512, PHP_NORMAL_READ));
          $serverStream  = stream_get_contents();
          dd(ord($headerGateOpen));*/
          /*byte[] outStream = System.Text.Encoding.ASCII.GetBytes(HeaderGateOpen);
                        serverStream.Write(outStream, 0, outStream.Length);
*/
        }catch (Exception $e)
        {
            echo "Error: " . $e->getMessage();
        }


    }

    public function onConnect() {
        echo "1 socket is connected \n";
        echo "-------------------------------------------------------------\n";
    }

    public function onDisconnect() {
        echo "2 socket is disconnected \n";
        echo "-------------------------------------------------------------\n";
    }

    public function onError($err) {
        echo "something wrong : " . $err .'\n';
    }

    public function onJSON($json) {
        echo "3 receive data from server \n";
        echo "=============================================================\n";
        echo "4 the data is " . $json . '\n';
        echo "=============================================================\n";
    }

    public function onJSONEvent($event, $jsonArray) {
        echo "receive data from server </br>";
        echo "=============================================================\n";
        echo "the event is " . $event . '\n';
        foreach ($jsonArray as $key => $value) {
            echo "Key: $key; \n";
            var_dump($value);
            echo '\n';
        }
        echo "=============================================================\n";
    }

    public function onMessage($message) {
        echo "message from sever :" . $message .'\n';
    }

    public function onSend($msg) {
          $address = "*************";
          $port = 10200;
      //    $socket = new TCPConnection();
          $this->socket = new TCPConnection("*************", 10200, $this);
        /*echo "sending data to server \n";
        echo "=============================================================\n";*/
        //echo 'data = ' . $msg . '\n';
        
   
        socket_write($this->socket->create(), $msg, strlen($msg));
        echo "message write";
    }


    public function tcpRead() {
      $socket = new TCPConnection();
      if (!socket_last_error($socket->getSocket())) {
          if ($buffer = socket_read($socket->getSocket(), 512, PHP_NORMAL_READ)) {
              $json = json_decode($buffer, true);
              if ($json === null) {
                  //$this->getCallback()->onMessage($buffer);
                  echo 'ai';
              } else if ($json !== null) {

                  /*if (array_key_exists("name", $json) === true && array_key_exists("args", $json) === true) {
                      $this->getCallback()->onJSONEvent($json['name'], $json['args']);
                  } else {
                      $this->getCallback()->onJSON($json);
                  }*/
                  echo 'haloo';
              }
          }
      }
    }
    /*$client = new TCPClient();
    $client->index();*/
      
}
