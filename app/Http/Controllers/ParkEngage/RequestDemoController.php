<?php

namespace App\Http\Controllers\ParkEngage;

use App\Models\ParkEngage\Service;
use App\Models\ParkEngage\RequestDemo;
use App\Models\ParkEngage\Country;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

use Illuminate\Http\Request;
use App\Http\Requests;
use Artisan;
use App\Http\Helpers\QueryBuilder;

class RequestDemoController extends Controller
{
   
    public function getServices($is_offline = '0'){
      if($is_offline == '0'){
        $service = Service::with(['membershipPlan' => function($query) {
          //$query->where('status', '1');
          $query->where('is_display', '1');
        }])->where('is_offline', $is_offline)->orderBy('list_order', 'Asc')->get();
      }else{
        $service = Service::with(['membershipPlan' => function($query) {
          //$query->where('status', '1');
          $query->where('is_display', '1');
        }])->orderBy('list_order', 'Asc')->get();
      }
        
        return $service;
    }

    public function getCountries(){
        return Country::select('name','country_code','phonecode')->orderBy('name', 'Asc')->get();
    }

    public function getRequestDemoDetails(Request $request){

      if($request->sort != ''){              
        if($request->sort == 'full_name'){
          $requestDemo = RequestDemo::with('services')->orderBy('id', 'Desc');
        }else{
          $requestDemo = RequestDemo::with('services')->orderBy($request->sort,$request->sortBy);
        }
      }else{
        $requestDemo = RequestDemo::with('services')->orderBy('id', 'Desc');
      }
      if ($request->search) {
            $requestDemo = QueryBuilder::buildSearchQuery($requestDemo, $request->search, RequestDemo::$searchFields)
            ->orWhereHas(
                    'services', function ($query) use ($request) {
                        $query
                            ->where('full_name', 'like', "%{$request->search}%")
                            ->orWhere('short_name', 'like', "%{$request->search}%");
                    }
                );
            $requestDemo = $requestDemo->paginate(20);
            if($request->sort == 'full_name'){
              if(count($requestDemo) > 0){
                if($request->sortBy == 'asc' || $request->sortBy == 'Asc'){
                for ($i = 0; $i < count($requestDemo); $i++) {
                for ($j = $i + 1; $j < count($requestDemo); $j++) {
                    if ($requestDemo[$i]['services']->full_name > $requestDemo[$j]['services']->full_name) {
                        $temp = $requestDemo[$i];
                        $requestDemo[$i] = $requestDemo[$j];
                        $requestDemo[$j] = $temp;
                    }
                }
              }
              }else{
                for ($i = 0; $i < count($requestDemo); $i++) {
                for ($j = $i + 1; $j < count($requestDemo); $j++) {
                    if ($requestDemo[$i]['services']->full_name < $requestDemo[$j]['services']->full_name) {
                        $temp = $requestDemo[$i];
                        $requestDemo[$i] = $requestDemo[$j];
                        $requestDemo[$j] = $temp;
                    }
                }
              }
              }

              }
            }      
          return $requestDemo;
            
      }

      $requestDemo = $requestDemo->paginate(20);
      if($request->sort == 'full_name'){
        if(count($requestDemo) > 0){
          if($request->sortBy == 'asc' || $request->sortBy == 'Asc'){
          for ($i = 0; $i < count($requestDemo); $i++) {
          for ($j = $i + 1; $j < count($requestDemo); $j++) {
              if ($requestDemo[$i]['services']->full_name > $requestDemo[$j]['services']->full_name) {
                  $temp = $requestDemo[$i];
                  $requestDemo[$i] = $requestDemo[$j];
                  $requestDemo[$j] = $temp;
              }
          }
        }
        }else{
          for ($i = 0; $i < count($requestDemo); $i++) {
          for ($j = $i + 1; $j < count($requestDemo); $j++) {
              if ($requestDemo[$i]['services']->full_name < $requestDemo[$j]['services']->full_name) {
                  $temp = $requestDemo[$i];
                  $requestDemo[$i] = $requestDemo[$j];
                  $requestDemo[$j] = $temp;
              }
          }
        }
        }

        }
      }      
      return $requestDemo;
        
    }


   public function store(Request $request) {
      
      if($request->form_status == 'Footer'){
        $this->validate($request, RequestDemo::$validationFooter);  
      }else{
        $this->validate($request, RequestDemo::$validation);  
      }
      
      $data['email'] = $request->email;
      $data['service_id'] = $request->service_id;
      if($request->form_status != 'Footer'){
        $data['name'] = $request->name;
        $data['mobile'] = $request->mobile;
        $data['company_name'] = $request->company_name;
        $data['city'] = $request->city;
        $data['state'] = $request->state;
        $data['country'] = $request->country;
      }
      $result = RequestDemo::create($data);
      if($result){
        Artisan::queue('email:request-demo',array('id'=>$result->id));
        return $result;    
      }else{
        return 'Request not saved.';
      }
      
   }

   public function getRequestDemoDetailsById($id){

      $requestDemo = RequestDemo::with('services')->where('id', $id)->first();
      return $requestDemo;
        
    }
   
}
