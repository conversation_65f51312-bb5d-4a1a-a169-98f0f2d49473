<?php

namespace App\Http\Controllers\ParkEngage;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Storage;
use App\Services\LoggerFactory;
use App\Models\Facility;
use App\Models\PaymentProfile;
use App\Models\AccountNameMaster;
use App\Models\User;
use App\Models\OauthClient;
use App\Models\PermitRate;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\PermitVehicle;
use App\Models\PermitRateDiscount;
use App\Models\PermitTypeMasterRateDescMapping;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\Ticket;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\UserPermitRequest;
use App\Models\UserPermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\ParkEngage\PermitTypeDoumentList;
use App\Models\ParkEngage\PermitTypeMaster;
use App\Models\ParkEngage\PermitTypeFacilityMapping;
use App\Models\ParkEngage\PermitRequestDocuments;
use App\Models\UserPermitVehicleMapping;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Models\ParkEngage\AuthorizedDriver;
use App\Models\PermitRequestRenewHistory;
use Illuminate\Support\Facades\Auth;
use App\Models\Reservation;
use App\Models\Devicetoken;
use Illuminate\Support\Facades\Session;
use App\Models\ParkEngage\Vehicle;
use App\Models\ParkEngage\UserSession;
use DB;
use Hash;

class GoogleUserDeleteController extends Controller
{
	
	public function __construct(Request $request, LoggerFactory $logFactory)
	{
		$this->log = $logFactory->setPath('logs/user-delete-google-app')->createLogger('user-delete-google-app');
	}
	public function userDelete($slug)
    {
		$users  = '';
        $sucessData = 0;
       // dd(Hash::make('Test@123'));
       if( !empty($slug)){
			$slugExist = UserPaymentGatewayDetail::where('touchless_payment_url', $slug)->first();
            if ($slugExist) {
				$users = User::with(['brandSetting', 'clients'])->where('id', $slugExist->user_id)->where('user_type', '=', '3')
					->whereNull('deleted_at')
					->first();

			} else {
				$rmSlugExist = User::where('slug', $slug)->first();
				if (!$rmSlugExist) {
					throw new NotFoundException('No partner found.');
				}
				$facility = DB::table('user_facilities')->where('user_id', $rmSlugExist->id)->whereNull('deleted_at')->pluck('facility_id');
				
				$users = User::with(['brandSetting'])->where('id', $rmSlugExist->id)->whereIn('user_type', ['4','12'])
					->whereNull('deleted_at')
					->first();
				$partner_details = User::with(['brandSetting'])->where('id', $users->created_by)->first();
				$users->partner_details = $partner_details;
				$brandSettings = FacilityBrandSetting::whereIn('facility_id', $facility)->first();
                
				$users->setRelation('brandSetting', $brandSettings);
				
			}
			
			return view('deleteAccount.user-delete',compact('users','sucessData'));
	   }
    }

    public function userDeleteAccount(Request $request)
    {
        
        $user = User::where('email',$request->email)->whereNull('deleted_at')->first();
        if(!$user){
            return back()->withErrors(['email' => 'Email is invalid!']);
        }
        if ($user->user_type == '3' || $user->user_type == '12') {
            return back()->withErrors(['Sorry! You can not delete your user account.']);
        }
        $email= $request->email;
        $password= $request->password;
        if (!Auth::attempt(['email' => $email, 'password' => $password])) {
            return back()->withErrors(['email' => 'Email and password are invalid.']);
        }

        // Log user in successfully
        $this->log->info("User credentials are matched");

        $reservation = Reservation::where('user_id', $user->id)->whereDate("start_timestamp", '>=', date("Y-m-d"))->where("is_ticket", '0')->whereNull("cancelled_at")->first();
        if ($reservation) {
            return back()->withErrors(['You have Reservation in our System. So, You can not delete your user account.']);
        }
        $ticket = Ticket::where('user_id', $user->id)->where('is_checkout', '0')->first();
        if($ticket) {
            return back()->withErrors(['You have a active Checkin. So, You can not delete your user account.']);
        }
        
        $payment_profile = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user->id)->first();
        if ($payment_profile) {
            $payment_profile->delete();
            $this->log->info("payment profile deleted");
        }
        $vehicle = Vehicle::where('user_id', $user->id)->first();
        if ($vehicle) {
            $vehicle->delete();
            $this->log->info("vehicle deleted");
        }
        $device = Devicetoken::where('user_id', $user->id)->delete();
        if($device){
            UserSession::where("user_id", $user->id)->delete();
        }
        $user->delete();
        $this->log->info("user account deleted : " . $user->id);
        Session::put('sucessData',1);//get this value on use-delete page to show sucess message div, after get delete session 
        return redirect()->back()->with('message', 'Account deleted successfully.');
       
    }
    



}
