<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Services\LoggerFactory;
use App\Classes\MagicCrypt;
use App\Exceptions\ApiGenericException;
use Response;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\Facility;
use App\Classes\DatacapPaymentGateway;


class DatacapController extends Controller
{
	public function __construct(Request $request, LoggerFactory $logFactory)
	{
		$this->log = $logFactory->setPath('logs/parkengage/datacap')->createLogger('datacap');
		$this->request = $request;
		error_reporting('~E_DEPRECATED');
	}

	public function index(Request $request)
	{
		$user_id = Auth::user()->id;

		$result = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id);

		if ($request->sort != '') {
			$result = $result->orderBy($request->sort, $request->sortBy);
		} else {
			$result = $result->orderBy("id", "DESC");
		}
		// $result = $result->paginate(10);
		$result = $result->get();
		if (!$result) {
			throw new ApiGenericException("Card Not Found");
		}
		$data = [];

		$data['payments'] = $result;

		return $data;
	}


	public function makePaymentDataCap()
	{
		$this->log->info("call datacap auth only curl");
		$data['Token'] = $this->request->token;
		$data['Amount'] = 0.00;
		$data["CardHolderID"] = "Allow_V2";
		$mid = $this->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
		$vars = json_encode($data);
		$headers = [
			'Authorization: ' . $mid,
			'User-Agent: ' . config('parkengage.DATACAP_User_Agent'),
			'Content-Type: application/json',
			'Accept: application/json',
			'Accept-Language: en-US,en;q=0.5',
			'Cache-Control: no-cache'
		];

		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, isset($this->facility->FacilityPaymentDetails->datacap_authonly_url) ? $this->facility->FacilityPaymentDetails->datacap_authonly_url : config('parkengage.DATACAP_AUTHONLY_URL'));
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $vars);  //Post Fields
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

		$response = curl_exec($curl);
		curl_close($curl);
		//dd($response);
		$this->log->info("datacap auth only curl response " . json_encode($response));
		return $response;
	}

	public function store(Request $request)
	{
		$this->log->info("Request Data Datacap: " . json_encode($request->all()));
		$user_id = Auth::user()->id;
		$partner_id = Auth::user()->created_by;
		if ($request->nonce) {
			$this->setDecryptedCard();
		}
		$facility = Facility::with('FacilityPaymentDetails')->where('owner_id', $partner_id)->where('active', '1')->first();
		$this->facility = $facility;
		$card_month = substr($request->expiration_date, 0, 2);
		$card_year = substr($request->expiration_date, -2);
		$request->request->add(['expiration_month' => $card_month]);
		$request->request->add(['expiration_year' => $card_year]);
		//datacap otu token
		if(isset($request->is_mobile_origin) && ($request->is_mobile_origin !="mobile")){
			$datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($request, $this->facility);
			//dd($datacapPaymentToken,$request->all());
			$this->log->info("Datacap token log: " . json_encode($datacapPaymentToken));
			if ($datacapPaymentToken["Token"]) {
				$request->request->add(['token' => $datacapPaymentToken["Token"]]);
			} else {
				throw new ApiGenericException("Payment Token Not Generated.");
			}	
		}
		

		$paymentStatus = $this->makePaymentDataCap();
		$paymentRecord = json_decode($paymentStatus, TRUE);
		//dd($paymentRecord,$request->all());
		if (isset($paymentRecord['Status']) && $paymentRecord['Status'] == 'Approved') {

			$cardCheck = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('card_holder_id', $paymentRecord['CardHolderID'])->first();
			if (!$cardCheck) {
			}
		} else {
			throw new ApiGenericException("Please enter valid card details.");
		}

		if ($cardCheck) {
			throw new ApiGenericException("Card details are already added");
		}

		$status = 0;

		$user_card = DatacapPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->first();


		$brand = str_replace('/', '', $paymentRecord['Brand']);
		$data['user_id'] = $user_id;
		$data['name'] = isset($request->name_on_card) ? $request->name_on_card : '';
		$data['partner_id'] = $partner_id;
		$data['card_last_four'] = isset($request->card_number) ? substr($request->card_number, -4) : '';
		$data['card_type'] = $brand;
		$data['card_name'] = $brand;
		$data['expiry'] = $request->expiration_date;
		$data['token'] = $paymentRecord['Token'];
		$data['card_holder_id'] = $paymentRecord['CardHolderID'];
		$data['result_reason'] = $paymentRecord['Message'];
		$data['currency_used'] = "USD";
		$data['RefNo'] = $paymentRecord['RefNo'];
		$data['InvoiceNo'] = $paymentRecord['InvoiceNo'];
		$data['AuthCode'] = $paymentRecord['AuthCode'];
		if (!$user_card) {
			$data['is_default'] = 1;
		} else {
			if (is_numeric($request->is_default)) {

				if (is_numeric($request->is_default)) {
					$status = $request->is_default;
				}

				if (!empty($status)) {

					DatacapPaymentProfile::where(['user_id' => $user_id])->update(array('is_default' => 0));
				}

				$data['is_default'] = $status;
			}
		}
		$result = DatacapPaymentProfile::create($data);

		if (!$result) {
			throw new ApiGenericException("Record Not Added");
		}
		return $result;
	}


	public function destroy($id)
	{
		$result = DatacapPaymentProfile::find($id);

		if ($result) {
			$result->delete();
			//return "Card Successfully Deleted.";
			return response(
				[
					"message" => 'Card Successfully Deleted.'
				]
			);
		}
		throw new ApiGenericException("Card Not Found.");
	}

	public function getDatacapProfile(Request $request)
	{
		$user_id = Auth::user()->id;
		$result = DatacapPaymentProfile::Select('id', 'card_last_four', 'expiry', 'token', 'user_id', 'card_type', 'card_name', 'result_reason', 'currency_used', 'card_holder_id', 'RefNo', 'InvoiceNo', 'AuthCode')->whereNull('deleted_at')->where('user_id', $user_id)->get();
		foreach ($result as $key => $val) {
			$val->is_payment_complete = 1;
			$cardTran = DatacapTransaction::select('is_payment_complete')->where('user_id', $user_id)->where('trans_id', $val->AuthCode)->where('is_payment_complete', 0)->orderby('id', 'DESC')->first();
			if ($cardTran) {
				$val->is_payment_complete = $cardTran->is_payment_complete;
			}
		}
		return $result;
	}

	public function setDecryptedCard()
	{
		if (isset($this->request->payment_profile_id) && $this->request->payment_profile_id != "") {
			return;
		}
		//$key = env('PCI_ENCRYPTION_KEY');
		$key = config('parkengage.PCI_ENCRYPTION_KEY');
		$mc = new MagicCrypt($key, 256);
		$decryptedNonce = $mc->decrypt($this->request->nonce);
		$cardData = explode(':', $decryptedNonce);
		//dd($cardData);
		$first_string = substr($cardData[2], 0, 2);
		$last_string = substr($cardData[2], -2);

		//dd($first_string,$last_string);
		$this->request->request->add(
			[
				'name_on_card' => $cardData[0],
				'card_number' => $cardData[1],
				'expiration_date' => $first_string . $last_string,
				'security_code' => $cardData[3]
			]
		);
	}
}
