<?php

namespace App\Http\Controllers\ParkEngage;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Hash;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Models\PaymentProfile;
use Illuminate\Support\Facades\Artisan;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Services\LoggerFactory;
use App\Models\CustomText;
use Excel;
use App\Models\ParkEngage\RevenueBalance;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use App\Classes\ParkengageGateApi;
use Illuminate\Support\Facades\Storage;
use App\Models\AuthorizeNetTransaction;
use App\Models\OauthClient;
use App\Models\ParkEngage\LicensePlate;
use App\Models\ParkEngage\TicketCitation;
use App\Models\ParkEngage\PartnerDevice;
use App\Models\PermitVehicle;
use App\Models\Rate;
use App\Models\ParkEngage\Warning;
use App\Models\PermitRequest;
use App\Models\PermitRate;
use App\Models\FacilityFee;
use App\Models\ParkEngage\Infraction;
use App\Models\PermitVehicleMapping;
use App\Models\ParkEngage\TicketCitationInfraction;
use App\Models\ParkEngage\TicketCitationInfractionReason;
use App\Models\ParkEngage\TicketCitationImage;
use App\Models\ParkEngage\TicketCitationInfractionOther;
use App\Models\ParkEngage\WarningImage;
use App\Models\ParkEngage\WarningInfraction;
use App\Models\ParkEngage\WarningInfractionReason;
use App\Models\ParkEngage\WarningInfractionOther;
use Illuminate\Support\Facades\DB;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\InfractionFacility;
use App\Models\ParkEngage\ScanedVehicle;
use App\Classes\PlanetPaymentGateway;
use App\Classes\DatacapPaymentGateway;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\ParkEngage\TicketExtend;
//use App\Models\ParkEngage\FacilityZone;
use App\Models\ParkEngage\State;
use App\Models\ParkEngage\MstMake;
use App\Models\ParkEngage\MstModel;
use App\Models\ParkEngage\MstColor;
use App\Models\ParkEngage\MstStyle;
use App\Models\ParkEngage\MstVehicleType; #pims-13318
use App\Models\ParkEngage\Country;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\CustomerPortalPermission;
use App\Models\ParkEngage\PermitServicesFacilityMapping;
use App\Models\ParkEngage\UserPermitTypeMapping;
use App\Models\BlackListedVehicle;
use Illuminate\Support\Facades\Redirect;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\ParkEngage\CitationVoidCode;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\ParkEngage\PartnerMakeModelMapping;
use App\Models\BusinessFacilityPolicy;
use App\Models\UserFastTrackDetails;

class DiamondGateApiController extends Controller
{

    protected $log;
    protected $user;
    protected $partnerPaymentDetails;
    protected $authNet;
    protected $facility;
    protected $paymentProfileId;
    protected $cim;
    protected $request;
    protected $billingValidation;

    const RESERVATION_THRESHOLD_TYPE = 2;
    const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE = 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO = 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR = 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "Sold Out At This Time.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    const  RESIDENT_ID = 292;
    const  NON_RESIDENT_ID = 293;
    const  SHOW_TICKET_LIST_PARTNER = 7395;
    const  MANLO_CITATION_EXPIRE_DAYS = 7;

    public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->cim = $cim;
        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        $this->log = $logFactory->setPath('logs/parkengage/touchless-flow')->createLogger('diamond');
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($facility->timezone != '') {
                date_default_timezone_set($facility->timezone);
            } else if ($partnerTimezone->timezone != '') {
                date_default_timezone_set($partnerTimezone->timezone);
            }
        }
    }

    public function uploadLicensePlate(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('Invalid partner.');
            }
        }
        $this->validate($request, ['image' => 'mimes:jpeg,png,jpg,svg'], ['image.mimes' => 'vehicle image must be a file of type : .jpeg, .png, .jpg']);

        $image = $request->file('image');
        $file_extension = $image->getClientOriginalExtension();
        $file_name =  $request->license_plate . '_' . rand(1001, 9999) . '.' . $file_extension;
        $destination_path = storage_path("app/license-plate");
        $data['license_plate'] = $request->license_plate;
        $data['image'] = $file_name;
        $data['partner_id'] = $secret->partner_id;
        if ($request->header('X-FacilityID') != '') {
            $data['facility_id'] = $request->header('X-FacilityID');
        }
        LicensePlate::create($data);
        if (!$image->move($destination_path, $file_name)) {
            throw new ApiGenericException("Something went wrong while upload image.");
        }
        return "License plate successfully updated.";
    }

    public function validateLicensePlateCheckin(Request $request, $license_plate)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $ticket = Ticket::with(['user', 'citation'])->where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->first();
            if (!$ticket) {
                $ticket = [];
                $citation = TicketCitation::with('user')->where('license_plate', $license_plate)->where('facility_id', $facility->id)->whereDate('payment_date', '=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
                if (!$citation) {
                    $citation = TicketCitation::with('user')->where('license_plate', $license_plate)->where('facility_id', $facility->id)->whereNull('anet_transaction_id')->orderBy('id', 'DESC')->first();
                    if (!$citation) {
                        throw new ApiGenericException('No vehicle plate number found.');
                    }
                }
                $citation->is_vehicle_present = '1';
                $citation->scan_date = date("Y-m-d H:i:s");
                $citation->save();
                $ticket['check_in_datetime'] = $citation->checkin_time;
                $ticket['checkout_datetime'] = $citation->checkout_time;
                $citation['check_in_datetime'] = $citation->checkin_time;
                $citation['checkout_datetime'] = $citation->checkout_time;
                //$ticket['is_overstay'] = '1';
                $ticket['is_autopay'] = '0';
                $ticket['citation'] = $citation;
                $now = date("Y-m-d H:i:s");
                //dd($now, $citation->check_in_datetime);
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $citation->check_in_datetime);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                $diff_in_sec = $arrival_time->diffInSeconds($from);
                if ($diff_in_hours > 24) {
                    $diff_in_hours = 24;
                }

                if ($diff_in_hours == 0 && $diff_in_mins != 0) {
                    $diff_in_hours = 1;
                }

                if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
                    $diff_in_hours = 1;
                }

                /* $isMember = 0;
            $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
            
            if($rate == false){
                return Redirect::to('autogate-error-facility');
            }*/

                $rate = [];
                $is_resident_user = '0';
                /*if(Session::get('driving_license') != ''){
                
                $mystring = Session::get('driving_license');
                $mystring = trim($mystring);//removes any spaces from beginning of the string
                $number = substr($mystring,1);
                
                if(ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring)==9){
                    $is_resident_user = '1';
                }else{
                    $is_resident_user = '0';
                }
            }*/

                $categoryId = self::NON_RESIDENT_ID;
                $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
                if (!$residentRate) {
                    return Redirect::to('diamond-error-facility');
                }

                $days = $diff_in_hours;
                $rate['price'] = $residentRate->price * $days;
                //$processingFee = $facility->citation_processing_fee;
                $facilityFee = FacilityFee::where("facility_id",  $this->facility->id)->where("name", "citation_processing_fee")->first();
                $facilityProcessingFee = 0.00;
                if ($facilityFee) {
                    $facilityProcessingFee = $facilityFee['val'];
                }
                $processingFee = $facilityProcessingFee;
                $taxFee = $facility->tax_rate;

                $ticket['overstay_length'] = $diff_in_hours;
                $ticket['parking_amount'] = number_format($rate['price'], 2);
                $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                $ticket['is_overstay'] = '1';
                $ticket['is_vehicle_present'] = '1';
                $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                $citation['citation_processing_fee'] = number_format($processingFee, 2);
                $citation['tax_fee'] = number_format($taxFee, 2);
                $ticket['user'] = $citation->user;
                return $ticket;
            }
            $ticket->is_vehicle_present = '1';
            $ticket->scan_date = date("Y-m-d H:i:s");
            $ticket->save();
            if ($ticket->is_checkout == '1') {
                $now = date("Y-m-d H:i:s");
                if (strtotime(date("Y-m-d H:i:s", strtotime($now))) > strtotime(date("Y-m-d H:i:s", strtotime($ticket->checkout_datetime)))) {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_sec = $arrival_time->diffInSeconds($from);
                    if ($diff_in_hours > 24) {
                        $diff_in_hours = 24;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins != 0) {
                        $diff_in_hours = 1;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
                        $diff_in_hours = 1;
                    }
                    /*if($diff_in_hours > 24){
                   $diff_in_hours = 24;     
                }*/

                    /*$isMember = 0;
                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
                
                if($rate == false){
                    return Redirect::to('autogate-error-facility');
                }*/

                    $rate = [];
                    $is_resident_user = '0';

                    $categoryId = self::NON_RESIDENT_ID;
                    $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
                    if (!$residentRate) {
                        return Redirect::to('diamond-error-facility');
                    }
                    $days = $diff_in_hours;
                    $rate['price'] = $residentRate->price * $days;
                    $facilityFee = FacilityFee::where("facility_id",  $this->facility->id)->where("name", "citation_processing_fee")->first();
                    $facilityProcessingFee = 0.00;
                    if ($facilityFee) {
                        $facilityProcessingFee = $facilityFee['val'];
                    }
                    $processingFee = $facilityProcessingFee;
                    $taxFee = $facility->tax_rate;

                    $ticket['overstay_length'] = $diff_in_hours;
                    $ticket['parking_amount'] = number_format($rate['price'], 2);
                    $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                    $ticket['is_overstay'] = '1';
                    $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                    $ticket['processing_fee'] = number_format($processingFee, 2);
                    $ticket['tax_fee'] = number_format($taxFee, 2);
                    return $ticket;
                }
                return $ticket;
            }

            if ($ticket->is_checkin == '1' && $ticket->is_checkout == '0') {
                $now = date("Y-m-d H:i:s");
                if (strtotime($now) > strtotime($ticket->checkout_datetime)) {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_sec = $arrival_time->diffInSeconds($from);
                    if ($diff_in_hours > 24) {
                        $diff_in_hours = 24;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins != 0) {
                        $diff_in_hours = 1;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
                        $diff_in_hours = 1;
                    }
                    /*if($diff_in_hours > 24){
                   $diff_in_hours = 24;     
                }*/

                    /*$isMember = 0;
                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
                
                if($rate == false){
                    return Redirect::to('autogate-error-facility');
                }*/

                    $rate = [];
                    $is_resident_user = '0';

                    $categoryId = self::NON_RESIDENT_ID;
                    $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
                    if (!$residentRate) {
                        return Redirect::to('diamond-error-facility');
                    }
                    $days = $diff_in_hours;
                    $rate['price'] = $residentRate->price * $days;

                    //$days = $diff_in_hours;
                    $facilityFee = FacilityFee::where("facility_id",  $this->facility->id)->where("name", "citation_processing_fee")->first();
                    $facilityProcessingFee = 0.00;
                    if ($facilityFee) {
                        $facilityProcessingFee = $facilityFee['val'];
                    }
                    $processingFee = $facilityProcessingFee;
                    $taxFee = $facility->tax_rate;

                    //$ticket['overstay_length'] = $diff_in_hours; 
                    $ticket['parking_amount'] = number_format($rate['price'], 2);
                    $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                    $ticket['is_overstay'] = '1';
                    $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                    $ticket['citation_processing_fee'] = number_format($processingFee, 2);
                    $ticket['tax_fee'] = number_format($taxFee, 2);
                    return $ticket;
                }
                return $ticket;
            }
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }

    // public function markCitation(Request $request){

    //     $this->log->info(json_encode($request->all()));
    //     $this->validate($request, ['license_plate' => 'required'], ['license_plate.required' => 'License plate is required.' ]);

    //     if($request->header('X-ClientSecret') != '')
    //     { 
    //         $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
    //         if(!$secret){
    //            throw new ApiGenericException('Invalid partner.');
    //         }
    //     }
    //     $data['violation'] = "NO PAYMENT";
    //     /*if($request->ticket_number != ''){
    //         $ticket = Ticket::where("ticket_number", $request->ticket_number)->orderBy('id', 'DESC')->first();

    //         $data['ticket_id'] = $ticket->id;
    //         $data['user_id'] = $ticket->user_id;
    //         $data['facility_id'] = $ticket->facility_id;            
    //         $data['partner_id'] = $ticket->partner_id;            
    //         $data['violation'] = "OVERSTAY";
    //     }*/
    //     if($request->header('X-FacilityID') != '')
    //     {
    //         $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
    //     }else{
    //         $facility = Facility::where('owner_id', $secret->partner_id)->first();
    //     }
    //     if($request->type == "warning"){

    //         $now = date("Y-m-d H:i:s");
    //         $data['license_plate'] = $request->license_plate;
    //         $data['warning_number'] = $this->checkWarningNumber();
    //         $data['facility_id'] = $facility->id;
    //         $data['partner_id'] = $facility->owner_id;
    //         $data['is_vehicle_present'] = '1';
    //         $data['scan_date'] = $now;
    //         $data['updated_by'] = Auth::user()->id;
    //         $data['comment'] = $request->comment;
    //         $data['violation'] = "NO PAYMENT";

    //         $data['infraction_name'] = $request->infraction_name;
    //         $data['reason'] = $request->reason;
    //         $data['statutory_code'] = $request->statutory_code;
    //         $data['description1'] = $request->description1;
    //         $data['description2'] = $request->description2;
    //         $data['for_question_ticket'] = $request->for_question_ticket;
    //         $data['make'] = $request->make;
    //         $data['model'] = $request->model;
    //         $data['style'] = $request->style;
    //         $data['color'] = $request->color;
    //         $data['year'] = $request->year;
    //         $data['state'] = $request->state;
    //         $data['penalty_fee'] = $request->penalty_fee;
    //         //$data['total'] = $request->penalty_fee;
    //         //$data['grand_total'] = $request->penalty_fee;

    //         $image = $request->file('license_plate_image');
    //         if($image != ''){
    //             $file_extension = $image->getClientOriginalExtension();
    //             $file_name =  $request->license_plate. '_'.rand(1001, 9999).'.' . $file_extension;
    //             $destination_path = storage_path("app/warnings");
    //             $data['license_plate_image'] = $file_name;

    //             if(!$image->move($destination_path, $file_name)) {

    //             }
    //         }

    //         $warning = Warning::create($data);
    //         return $warning;
    //     }

    //     if($request->type == "citation"){

    //         $now = date("Y-m-d H:i:s");
    //         $data['checkin_time'] = $now;
    //         $data['checkout_time'] = date("Y-m-d", strtotime($now)). " 23:59:59";
    //         $data['estimated_checkout'] = date("Y-m-d", strtotime($now)). " 23:59:59";
    //         $data['is_checkin'] = '1';
    //         $data['license_plate'] = $request->license_plate;
    //         $data['citation_number'] = $this->checkTicketNumber();
    //         $data['facility_id'] = $facility->id;
    //         $data['partner_id'] = $facility->owner_id;
    //         $data['is_vehicle_present'] = '1';
    //         $data['scan_date'] = $now;
    //         $data['updated_by'] = Auth::user()->id;

    //         $data['meter_number'] = $request->meter_number;
    //         $data['beat'] = $request->beat;
    //         $data['longitude'] = $request->longitude;
    //         $data['latitude'] = $request->latitude;

    //         $data['infraction_name'] = $request->infraction_name;
    //         $data['reason'] = $request->reason;
    //         $data['statutory_code'] = $request->statutory_code;
    //         $data['description1'] = $request->description1;
    //         $data['description2'] = $request->description2;
    //         $data['for_question_ticket'] = $request->for_question_ticket;
    //         $data['make'] = $request->make;
    //         $data['model'] = $request->model;
    //         $data['style'] = $request->style;
    //         $data['color'] = $request->color;
    //         $data['year'] = $request->year;
    //         $data['state'] = $request->state;
    //         $data['penalty_fee'] = $request->penalty_fee;

    //         //$this->validate($request, ['license_plate_image' => 'mimes:jpeg,png,jpg,svg'], ['license_plate_image.mimes' => 'vehicle image must be a file of type : .jpeg, .png, .jpg' ]);    

    //         $image = $request->file('license_plate_image');
    //         if($image != ''){
    //             $file_extension = $image->getClientOriginalExtension();
    //             $file_name =  $request->license_plate. '_'.rand(1001, 9999).'.' . $file_extension;
    //             $destination_path = storage_path("app/citations");
    //             $data['license_plate_image'] = $file_name;

    //             if(!$image->move($destination_path, $file_name)) {

    //             }
    //         }


    //     $citation = TicketCitation::create($data);

    //     $image1 = $request->file('image_1');
    //     if($image1 != ''){
    //         $file_extension = $image1->getClientOriginalExtension();
    //         $file_name =  $citation->id. '_'.rand(1001, 9999).'.' . $file_extension;
    //         $destination_path = storage_path("app/citations");

    //         if($image1->move($destination_path, $file_name)) {
    //             $ticketCitationImage['ticket_citation_id'] = $citation->id;
    //             $ticketCitationImage['image_name'] = $file_name;
    //             TicketCitationImage::create($ticketCitationImage);
    //         }
    //     }

    //     $image2 = $request->file('image_2');
    //     if($image2 != ''){
    //         $file_extension = $image2->getClientOriginalExtension();
    //         $file_name =  $citation->id. '_'.rand(1001, 9999).'.' . $file_extension;
    //         $destination_path = storage_path("app/citations");

    //         if($image2->move($destination_path, $file_name)) {
    //             $ticketCitationImage['ticket_citation_id'] = $citation->id;
    //             $ticketCitationImage['image_name'] = $file_name;
    //             TicketCitationImage::create($ticketCitationImage);
    //         }
    //     }

    //     $image3 = $request->file('image_3');
    //     if($image3 != ''){
    //         $file_extension = $image3->getClientOriginalExtension();
    //         $file_name =  $citation->id. '_'.rand(1001, 9999).'.' . $file_extension;
    //         $destination_path = storage_path("app/citations");

    //         if($image3->move($destination_path, $file_name)) {
    //             $ticketCitationImage['ticket_citation_id'] = $citation->id;
    //             $ticketCitationImage['image_name'] = $file_name;
    //             TicketCitationImage::create($ticketCitationImage);
    //         }
    //     }

    //     $image4 = $request->file('image_4');
    //     if($image4 != ''){
    //         $file_extension = $image4->getClientOriginalExtension();
    //         $file_name =  $citation->id. '_'.rand(1001, 9999).'.' . $file_extension;
    //         $destination_path = storage_path("app/citations");

    //         if($image4->move($destination_path, $file_name)) {
    //             $ticketCitationImage['ticket_citation_id'] = $citation->id;
    //             $ticketCitationImage['image_name'] = $file_name;
    //             TicketCitationImage::create($ticketCitationImage);
    //         }
    //     }

    //     $infractions = json_decode($request->infractions);
    //     if(count($infractions) > 0){
    //         foreach($infractions as $key=>$infraction){
    //             $infractionData['ticket_citation_id'] = $citation->id;
    //             $infractionData['infraction_id'] = $infraction->infraction_id;
    //             $infractionData['infraction_name'] = $infraction->infraction_name;
    //             $infractionData['penalty_fee'] = $infraction->penalty_fee;
    //             $ticketCitationInfraction = TicketCitationInfraction::create($infractionData);
    //             if(isset($infraction->reasons) && count($infraction->reasons) > 0){
    //                 foreach($infraction->reasons as $k=>$reason){
    //                     $reasonData['ticket_citation_infraction_id'] = $ticketCitationInfraction->id;
    //                     $reasonData['ticket_citation_id'] = $citation->id;
    //                     $reasonData['infraction_id'] = $infraction->infraction_id;
    //                     $reasonData['reason'] = $reason->reason;
    //                     $reasonData['penalty_fee'] = $reason->penalty_fee;
    //                     $reasonData['infraction_reason_id'] = $reason->infraction_reason_id;
    //                     TicketCitationInfractionReason::create($reasonData);
    //                 }
    //             }
    //         }
    //     }

    //     $other_infractions = json_decode($request->other_infractions);
    //     if(count($other_infractions) > 0){
    //         foreach($other_infractions as $key=>$infraction){
    //             $infractionDataOther['ticket_citation_id'] = $citation->id;
    //             $infractionDataOther['infraction_name'] = $infraction->infraction_name;
    //             $infractionDataOther['reason'] = $infraction->reason;
    //             $infractionDataOther['penalty_fee'] = $infraction->penalty_fee;
    //             $ticketCitationInfraction = TicketCitationInfractionOther::create($infractionDataOther);                    
    //         }
    //     }
    //         if($request->warning_number != ''){

    //             $warning = Warning::where("warning_number", $request->warning_number)->first();
    //             if($warning){
    //                 $time = date("Y-m-d H:i:s", strtotime($warning->created_at));
    //                 $warning->ticket_citation_id = $citation->id;
    //                 $warning->is_closed = '1';
    //                 $warning->save();

    //                 $citation->checkin_time = $time;
    //                 $citation->save();
    //                 $from = Carbon::createFromFormat('Y-m-d H:i:s', $time);
    //             }else{
    //                 $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
    //             }
    //         }else{
    //             $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
    //         }


    //     }


    //         $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $now);

    //         $diff_in_hours = $arrival_time->diffInRealHours($from);
    //             $diff_in_mins = $arrival_time->diffInRealMinutes($from);
    //             $diff_in_sec = $arrival_time->diffInSeconds($from);
    //             if($diff_in_hours > 24){
    //             $diff_in_hours = 24;     
    //             }

    //             if($diff_in_hours == 0 && $diff_in_mins != 0){
    //                 $diff_in_hours = 1;     
    //             }

    //             if($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0){
    //                 $diff_in_hours = 1;     
    //             }


    //             /*if($diff_in_hours > 24){
    //                $diff_in_hours = 24;     
    //             }*/

    //             /*$isMember = 0;
    //             $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);

    //             if($rate == false){
    //                 return Redirect::to('autogate-error-facility');
    //             }*/

    //             /*$rate = [];
    //             $is_resident_user = '0';

    //             $categoryId = self::NON_RESIDENT_ID;
    //             $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
    //             if(!$residentRate){
    //                 throw new ApiGenericException('Invalid rate.');
    //             }
    //             $days = $diff_in_hours;
    //             $rate['price'] = $residentRate->price * $days;

    //             //$days = $diff_in_hours;
    //             $facilityFee = FacilityFee::where("facility_id", $facility->id)->where("name", "citation_processing_fee")->first();
    //             $facilityProcessingFee = 0.00;
    //             if($facilityFee){
    //                 $facilityProcessingFee = $facilityFee['val'];
    //             }*/
    //             //$processingFee = $facilityProcessingFee;
    //         //$citation['rate'] = $rate['price'] + $facility->penalty_fee + $processingFee + $facility->tax_rate;
    //         //$citation['penalty_fee'] = $facility->penalty_fee;
    //         //$citation['citation_processing_fee'] = $processingFee;
    //         //$citation['tax_fee'] = $facility->tax_rate;
    //         $citation['rate'] = 0.00;
    //         $citation['penalty_fee'] = $citation->penalty_fee;
    //         $citation['citation_processing_fee'] = 0.00;
    //         $citation['tax_fee'] = 0.00;

    //     return $citation;
    // }

    public function markCitation(Request $request)
    {

        $this->log->info(json_encode($request->all()));
        $this->validate($request, ['license_plate' => 'required', 'type' => 'required|in:citation,warning'], ['license_plate.required' => 'License plate is required.']);
        if ((Auth::user()->user_type == '12')) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
        } else if ((Auth::user()->user_type == '4')) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else if (Auth::user()->user_type == '3') {
            $partner_id = Auth::user()->id;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        } else if (Auth::user()->user_type == '9') {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else {
            $partner_id = $request->partner_id;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        }
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
        }

        /*if($request->ticket_number != ''){
            $ticket = Ticket::where("ticket_number", $request->ticket_number)->orderBy('id', 'DESC')->first();

            $data['ticket_id'] = $ticket->id;
            $data['user_id'] = $ticket->user_id;
            $data['facility_id'] = $ticket->facility_id;            
            $data['partner_id'] = $ticket->partner_id;            
            $data['violation'] = "OVERSTAY";
        }*/
        if ($request->header('X-FacilityID') != '') {
            $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
        } else {
            $facility = Facility::where('owner_id', $request->partner_id)->first();
        }

        $this->setCustomTimezone($facility->id);


        if (isset($request->make)) {
            $existMake = MstMake::where("name", $request->make)->first();
            if (!$existMake) {
                $make = new MstMake();
                $make->name = ucwords($request->make);
                $make->save();

                $partnerMakeModelMapping = PartnerMakeModelMapping::where("partner_id", $partner_id)->count();
                if ($partnerMakeModelMapping > 0) {
                    //add partner make model mapping
                    $mapping = new PartnerMakeModelMapping();
                    $mapping->mst_make_id = $make->id;
                    $mapping->partner_id = $partner_id;
                    $mapping->save();
                }

                if (isset($request->model)) {
                    $model = new MstModel();
                    $model->make_id = $make->id;
                    $model->name = ucwords($request->model);
                    $model->save();

                    /*$model['make_id'] = $make->id;
                    $model['name'] = ucwords($request->model);
                    MstModel::create($model);*/
                }
            }

            $existColor = MstColor::where("name", $request->color)->first();
            if (!$existColor) {
                $color = new MstColor();
                $color->name = $request->color;
                $color->save();
            }
            $existStyle = MstStyle::where("name", $request->style)->first();
            if (!$existStyle) {
                $style = new MstStyle();
                $style->name = $request->style;
                $style->save();
            }
        }



        $now = date("Y-m-d H:i:s");
        $data['violation'] = "NO PAYMENT";
        $data['license_plate'] = $request->license_plate;
        $data['facility_id'] = $facility->id;
        $data['partner_id'] = $facility->owner_id;
        $data['is_vehicle_present'] = '1';
        $data['scan_date'] = $now;
        $data['updated_by'] = Auth::user()->id;
        $data['meter_number'] = $request->meter_number;
        $data['beat'] = $request->beat;
        $data['attendant_name'] = Auth::user()->name;
        $data['badge'] = $request->badge;
        if (isset($request->vin)) {
            $data['vin'] = $request->vin; #DD PIMS-12423
        } else {
            $data['vin'] = substr($request->license_plate, -4); #DD PIMS-12423
        }

        $permit = PermitRequest::where("account_number", $request->permit_request_id)->first();
        $permit_request_id = '';
        if (isset($permit->id)) {
            $permit_request_id = $permit->id;
        }

        $reservation = Reservation::where("ticketech_code", $request->reservation_id)->first();
        $reservation_id = '';
        if (isset($reservation->id)) {
            $reservation_id = $reservation->id;
        }

        $lastScanedVehicle = ScanedVehicle::where('license_plate', $request->license_plate)->where('facility_id', $facility->id)->orderBy("id", "DESC")->first();

        if ($request->type == "warning") {
            $ticketType = 'warning_id';
            $ticketInfractionIdName = 'warning_infraction_id';
            $data['warning_number'] = $this->checkWarningNumber();
            $data['comment'] = $request->comment;
            $data['violation'] = "NO PAYMENT";

            $data['infraction_name'] = $request->infraction_name;
            $data['reason'] = $request->reason;
            $data['statutory_code'] = $request->statutory_code;
            $data['description1'] = $request->description1;
            $data['description2'] = $request->description2;
            $data['for_question_ticket'] = $request->for_question_ticket;
            $data['make'] = $request->make;
            $data['model'] = $request->model;
            $data['style'] = $request->style;
            $data['color'] = $request->color;
            $data['year'] = $request->year;
            $data['state'] = $request->state;
            $data['penalty_fee'] = $request->penalty_fee;
            //$data['total'] = $request->penalty_fee;
            //$data['grand_total'] = $request->penalty_fee;
            $data['rm_id'] = $rm_id;
            $image = $request->file('license_plate_image');
            if ($image != '') {
                $file_extension = $image->getClientOriginalExtension();
                $file_name =  $request->license_plate . '_' . rand(1001, 9999) . '.' . $file_extension;
                $destination_path = storage_path("app/warnings");
                $data['license_plate_image'] = $file_name;

                if (!$image->move($destination_path, $file_name)) {
                }
            }

            $data['permit_request_id'] = $permit_request_id;
            $data['reservation_id'] = $reservation_id;
            $data['lot_id'] = $request->lot_id;
            $data['lot_name'] = $request->lot_name;
            $ticket = Warning::create($data);
            $infractions = json_decode($request->infractions);
            // dd($infractions);
            if (count($infractions) > 0) {

                foreach ($infractions as $key => $infraction) {

                    $infractionData['warning_id'] = $ticket->id;
                    $infractionData['infraction_id'] = $infraction->infraction_id;
                    $infractionData['infraction_name'] = $infraction->infraction_name;
                    #PIMS-12423 #DD
                    if (empty($request->infraction_name)) {
                        $ticket->infraction_name = $infraction->infraction_name;
                        $ticket->save();
                    }
                    $infractionData['penalty_fee'] = $infraction->penalty_fee;
                    $warningInfraction = WarningInfraction::create($infractionData);

                    if (isset($infraction->reasons) && count($infraction->reasons) > 0) {
                        foreach ($infraction->reasons as $k => $reason) {
                            $reasonData['warning_infraction_id'] = $warningInfraction->id;
                            $reasonData['warning_id'] = $ticket->id;
                            $reasonData['infraction_id'] = $infraction->infraction_id;
                            $reasonData['reason'] = $reason->reason;
                            $reasonData['penalty_fee'] = $reason->penalty_fee;
                            $reasonData['infraction_reason_id'] = $reason->infraction_reason_id;
                            WarningInfractionReason::create($reasonData);
                        }
                    }
                }
            }

            $other_infractions = json_decode($request->other_infractions);
            if (count($other_infractions) > 0) {
                foreach ($other_infractions as $key => $infraction) {
                    $infractionDataOther['warning_id'] = $ticket->id;
                    $infractionDataOther['infraction_name'] = $infraction->infraction_name;
                    $infractionDataOther['reason'] = $infraction->reason;
                    $infractionDataOther['penalty_fee'] = $infraction->penalty_fee;
                    $ticketCitationInfraction = WarningInfractionOther::create($infractionDataOther);
                }
            }

            $scanedVehicle = ScanedVehicle::where('license_plate', $request->license_plate)->where('facility_id', $facility->id)->whereDate("created_at", "=", date("Y-m-d"))->orderBy("created_at", "ASC")->first();
            if ($scanedVehicle) {
                $ticket->first_scan_date = date("Y-m-d H:i:s", strtotime($scanedVehicle->created_at));
            } else {
                $ticket->first_scan_date = date("Y-m-d H:i:s");
            }
            $ticket->save();


            if ($lastScanedVehicle) {
                $lastScanedVehicle->warning_id = $ticket->id;
                $lastScanedVehicle->save();
            }
            // return $warning;
        }

        if ($request->type == "citation") {
            $ticketType = 'ticket_citation_id';
            $ticketInfractionIdName = 'ticket_citation_infraction_id';
            $data['checkin_time'] = $now;
            $data['checkout_time'] = date("Y-m-d", strtotime($now)) . " 23:59:59";
            $data['estimated_checkout'] = date("Y-m-d", strtotime($now)) . " 23:59:59";
            if ($facility->owner_id == config('parkengage.PARTNER_MANLO')) {
                $data['checkout_time'] = date("Y-m-d", strtotime("+" . self::MANLO_CITATION_EXPIRE_DAYS . " day", strtotime($now))) . " 23:59:59";
                $data['estimated_checkout'] = date("Y-m-d", strtotime("+" . self::MANLO_CITATION_EXPIRE_DAYS . " day", strtotime($now))) . " 23:59:59";
            }
            $data['is_checkin'] = '1';

            $data['citation_number'] = $this->checkTicketNumber($facility);

            $data['longitude'] = $request->longitude;
            $data['latitude'] = $request->latitude;

            $data['infraction_name'] = $request->infraction_name;
            $data['reason'] = $request->reason;
            $data['statutory_code'] = $request->statutory_code;
            $data['description1'] = $request->description1;
            $data['description2'] = $request->description2;
            $data['for_question_ticket'] = $request->for_question_ticket;
            $data['make'] = $request->make;
            $data['model'] = $request->model;
            $data['style'] = $request->style;
            $data['color'] = $request->color;
            $data['year'] = $request->year;
            $data['state'] = $request->state;
            $data['penalty_fee'] = $request->penalty_fee;
            $data['rm_id'] = $rm_id;
            $data['is_admin_citation'] = isset($request->is_admin_citation) ? $request->is_admin_citation : '0';
            $data['permit_request_id'] = $permit_request_id;
            $data['reservation_id'] = $reservation_id;

            $data['lot_id'] = $request->lot_id;
            $data['lot_name'] = $request->lot_name;
            //$this->validate($request, ['license_plate_image' => 'mimes:jpeg,png,jpg,svg'], ['license_plate_image.mimes' => 'vehicle image must be a file of type : .jpeg, .png, .jpg' ]);    

            $image = $request->file('license_plate_image');
            if ($image != '') {
                $file_extension = $image->getClientOriginalExtension();
                $file_name =  $request->license_plate . '_' . rand(1001, 9999) . '.' . $file_extension;
                $destination_path = storage_path("app/citations");
                $data['license_plate_image'] = $file_name;

                if (!$image->move($destination_path, $file_name)) {
                }
            }


            $ticket = TicketCitation::create($data);


            $scanedVehicle = ScanedVehicle::where('license_plate', $request->license_plate)->where('facility_id', $facility->id)->whereDate("created_at", "=", date("Y-m-d"))->orderBy("created_at", "ASC")->first();
            if ($scanedVehicle) {
                $ticket->first_scan_date = date("Y-m-d H:i:s", strtotime($scanedVehicle->created_at));
            } else {
                $ticket->first_scan_date = date("Y-m-d H:i:s");
            }
            $ticket->save();

            if ($lastScanedVehicle) {
                $lastScanedVehicle->ticket_citation_id = $ticket->id;
                $lastScanedVehicle->save();
            }

            $infractions = json_decode($request->infractions);
            if (count($infractions) > 0) {
                foreach ($infractions as $key => $infraction) {
                    $infractionData['ticket_citation_id'] = $ticket->id;
                    $infractionData['infraction_id'] = $infraction->infraction_id;
                    #PIMS-12423 #DD
                    if (empty($request->infraction_name)) {
                        $ticket->infraction_name = $infraction->infraction_name;
                        $ticket->save();
                    }
                    $infractionData['infraction_name'] = $infraction->infraction_name;
                    $infractionData['penalty_fee'] = $infraction->penalty_fee;
                    $ticketCitationInfraction = TicketCitationInfraction::create($infractionData);
                    if (isset($infraction->reasons) && count($infraction->reasons) > 0) {
                        foreach ($infraction->reasons as $k => $reason) {
                            $reasonData['ticket_citation_infraction_id'] = $ticketCitationInfraction->id;
                            $reasonData['ticket_citation_id'] = $ticket->id;
                            $reasonData['infraction_id'] = $infraction->infraction_id;
                            $reasonData['reason'] = $reason->reason;
                            $reasonData['penalty_fee'] = $reason->penalty_fee;
                            $reasonData['infraction_reason_id'] = $reason->infraction_reason_id;
                            TicketCitationInfractionReason::create($reasonData);
                        }
                    }
                }
            }

            $other_infractions = json_decode($request->other_infractions);
            if (count($other_infractions) > 0) {
                foreach ($other_infractions as $key => $infraction) {
                    $infractionDataOther['ticket_citation_id'] = $ticket->id;
                    $infractionDataOther['infraction_name'] = $infraction->infraction_name;
                    $infractionDataOther['reason'] = $infraction->reason;
                    $infractionDataOther['penalty_fee'] = $infraction->penalty_fee;
                    $ticketCitationInfraction = TicketCitationInfractionOther::create($infractionDataOther);
                }
            }
            if ($request->warning_number != '' && $request->type == 'citation') {

                $warning = Warning::where("warning_number", $request->warning_number)->first();
                if ($warning) {
                    $time = date("Y-m-d H:i:s", strtotime($warning->created_at));
                    $warning->ticket_citation_id = $ticket->id;
                    $warning->is_closed = '1';
                    $warning->save();

                    ///$ticket->checkin_time = $time;
                    $ticket->checkin_time = $now;
                    $ticket->permit_request_id = $warning->permit_request_id;
                    $ticket->reservation_id = $warning->reservation_id;
                    $ticket->save();
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $time);
                } else {
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                }
            } else {
                $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
            }
            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $now);

            $diff_in_hours = $arrival_time->diffInRealHours($from);
            $diff_in_mins = $arrival_time->diffInRealMinutes($from);
            $diff_in_sec = $arrival_time->diffInSeconds($from);
            if ($diff_in_hours > 24) {
                $diff_in_hours = 24;
            }

            if ($diff_in_hours == 0 && $diff_in_mins != 0) {
                $diff_in_hours = 1;
            }

            if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
                $diff_in_hours = 1;
            }
        }

        $ticketImageData = array();
        if ($request->hasFile('image_1')) {
            $image1 = $request->file('image_1');
            $file_extension = $image1->getClientOriginalExtension();
            $file_name =  $ticket->id . '_' . rand(1001, 9999) . '.' . $file_extension;
            $destination_path = storage_path("app/citations");

            if ($image1->move($destination_path, $file_name)) {
                $ticketImage[$ticketType] = $ticket->id;
                $ticketImage['image_name'] = $file_name;
                array_push($ticketImageData, $ticketImage);
                // TicketCitationImage::create($ticketCitationImage);
            }
        }


        if ($request->hasFile('image_2')) {
            $image2 = $request->file('image_2');
            $file_extension = $image2->getClientOriginalExtension();
            $file_name =  $ticket->id . '_' . rand(1001, 9999) . '.' . $file_extension;
            $destination_path = storage_path("app/citations");

            if ($image2->move($destination_path, $file_name)) {
                $ticketImage[$ticketType] = $ticket->id;
                $ticketImage['image_name'] = $file_name;
                // TicketCitationImage::create($ticketCitationImage);
                array_push($ticketImageData, $ticketImage);
            }
        }


        if ($request->hasFile('image_3')) {
            $image3 = $request->file('image_3');
            $file_extension = $image3->getClientOriginalExtension();
            $file_name =  $ticket->id . '_' . rand(1001, 9999) . '.' . $file_extension;
            $destination_path = storage_path("app/citations");

            if ($image3->move($destination_path, $file_name)) {
                $ticketImage[$ticketType] = $ticket->id;
                $ticketImage['image_name'] = $file_name;
                // TicketCitationImage::create($ticketCitationImage);
                array_push($ticketImageData, $ticketImage);
            }
        }


        if ($request->hasFile('image_4')) {
            $image4 = $request->file('image_4');
            $file_extension = $image4->getClientOriginalExtension();
            $file_name =  $ticket->id . '_' . rand(1001, 9999) . '.' . $file_extension;
            $destination_path = storage_path("app/citations");

            if ($image4->move($destination_path, $file_name)) {
                $ticketImage[$ticketType] = $ticket->id;
                $ticketImage['image_name'] = $file_name;
                array_push($ticketImageData, $ticketImage);
                // TicketCitationImage::create($ticketCitationImage);
            }
        }

        if (isset($ticketImageData) && !empty($ticketImageData) && $request->type == 'citation') {
            $this->citationImageSave($ticketImageData);
            $ticket['rate'] = 0.00;
            $ticket['penalty_fee'] = $ticket->penalty_fee;
            $ticket['citation_processing_fee'] = 0.00;
            $ticket['tax_fee'] = 0.00;
        } elseif (isset($ticketImageData) && !empty($ticketImageData) && $request->type == 'warning') {
            $this->warningImageSave($ticketImageData);
        }


        if ($facility->license_plate_model != '') {
            $NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
            $licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate_type", "entry")->delete();
        }


        /*if($diff_in_hours > 24){
                   $diff_in_hours = 24;     
                }*/

        /*$isMember = 0;
                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
                
                if($rate == false){
                    return Redirect::to('autogate-error-facility');
                }*/

        /*$rate = [];
                $is_resident_user = '0';

                $categoryId = self::NON_RESIDENT_ID;
                $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
                if(!$residentRate){
                    throw new ApiGenericException('Invalid rate.');
                }
                $days = $diff_in_hours;
                $rate['price'] = $residentRate->price * $days;

                //$days = $diff_in_hours;
                $facilityFee = FacilityFee::where("facility_id", $facility->id)->where("name", "citation_processing_fee")->first();
                $facilityProcessingFee = 0.00;
                if($facilityFee){
                    $facilityProcessingFee = $facilityFee['val'];
                }*/
        //$processingFee = $facilityProcessingFee;
        //$citation['rate'] = $rate['price'] + $facility->penalty_fee + $processingFee + $facility->tax_rate;
        //$citation['penalty_fee'] = $facility->penalty_fee;
        //$citation['citation_processing_fee'] = $processingFee;
        //$citation['tax_fee'] = $facility->tax_rate;


        return $ticket;
    }

    public function citationImageSave($imageDate)
    {
        return TicketCitationImage::insert($imageDate);
    }

    public function warningImageSave($imageDate)
    {
        return WarningImage::insert($imageDate);
    }

    public function dashboard(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $this->setCustomTimezone($facility->id);

            if (Auth::check()) {
                if (Auth::user()->signature != '') {
                    $data['signature_url'] = Auth::user()->signature_url;
                } else {
                    $data['signature_url'] = '';
                }
            }
            $now = date("Y-m-d");
            $yesterday = date("Y-m-d", strtotime("-1 days"));
            $data['today_checkin'] = Ticket::whereDate('checkout_datetime', '=', $now)->where('facility_id', $facility->id)->count();
            $data['yesterday_checkin'] = Ticket::whereDate('checkout_datetime', '=', $yesterday)->where('facility_id', $facility->id)->count();
            $data['open_citation'] = TicketCitation::where('is_closed', '0')->where('facility_id', $facility->id)->where('is_avoid', '0')->count();
            $data['closed_citation'] = TicketCitation::where('is_closed', '1')->where('facility_id', $facility->id)->count();
            $data['checkin_vehicle_present'] = Ticket::where('is_vehicle_present', '0')->where('is_closed', '0')->where('facility_id', $facility->id)->count();
            $data['citation_vehicle_present'] = TicketCitation::where('is_vehicle_present', '0')->where('is_closed', '0')->where('is_avoid', '0')->where('facility_id', $facility->id)->count();
            $data['open_warning'] = Warning::where('is_closed', '0')->where('facility_id', $facility->id)->where('is_avoid', '0')->count();
            $data['closed_warning'] = Warning::where('is_closed', '1')->where('facility_id', $facility->id)->count();
            $data['citation_void_codes'] = CitationVoidCode::where("partner_id", $facility->owner_id)->where("status", "1")->get();
            $data['ticket_list'] = 0;
            if (self::SHOW_TICKET_LIST_PARTNER  == $secret->partner_id || $secret->partner_id == 358642 || $secret->partner_id == 2980) {
                $data['ticket_list'] = 1;
            }

            $data['today_scaned_vehicle'] = count(ScanedVehicle::select(DB::raw('count(scaned_vehicles.license_plate) as count'))->whereDate('created_at', '=', $now)->where('facility_id', $facility->id)->groupBy("license_plate")->get());
            $data['facility'] = $facility;
            $data['small_width'] = 0;
            if ($secret->partner_id == config('parkengage.PARTNER_PCI')) {
                $data['small_width'] = 1;
            }
            if ($secret->partner_id == config('parkengage.PARTNER_MANLO')) {
                $data['small_width'] = 2;
            }
            if ($secret->partner_id == config('parkengage.PARTNER_RevPass')) {
                $data['small_width'] = 2;
            }
            #PIMS-12423 DD
            /* if ($secret->partner_id  == config('parkengage.PARTNER_DIAMOND')) {
                $data['is_show_lot'] = '1';
            } */
            $showLot = QueryBuilder::getPartnerConfig("IS_SHOW_LOT", $secret->partner_id);
            if (isset($showLot->key)) {
                $data['is_show_lot'] =  $showLot->value;
            } else {
                $data['is_show_lot'] = '0';
            }
            return $data;
        }
    }


    public function citationList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            /*$today = date("Y-m-d");
            $yesterday = date("Y-m-d",strtotime("-1 days"));*/
            $open_citation = TicketCitation::/*whereDate('checkin_time', '=', $today)->*/with('user')->where('is_closed', '0')->where('is_avoid', '0')->where('facility_id', $facility->id)->orderBy("id", "DESC")->paginate(20);
            $closed_citation = TicketCitation::/*whereDate('checkin_time', '=', $yesterday)->*/with('user')->where('is_closed', '1')->where('facility_id', $facility->id)->orderBy("id", "DESC")->paginate(20);
            $data['open_citation'] = $open_citation;
            $data['closed_citation'] = $closed_citation;
            return $data;
        }
    }


    protected function checkTicketNumber($facility)
    {
        $ticket = 'CI' . rand(100, 999) . rand(100, 999);
        $config = FacilityConfiguration::where("facility_id", $facility->id)->first();
        if ($config) {
            $prefix = $config->citation_prefix != '' ? $config->citation_prefix : "CI";
            $fromRange = $config->citation_from_range != '' ? $config->citation_from_range : 100000;
            $toRange = $config->citation_to_range != '' ? $config->citation_to_range : 999999;
            $ticket = $prefix . rand($fromRange, $toRange);
        }
        $isExist = TicketCitation::where('citation_number', $ticket)->first();
        if ($isExist) {
            $this->checkTicketNumber($facility);
        }
        return $ticket;
    }

    protected function checkWarningNumber()
    {
        $ticket = 'WA' . rand(100, 999) . rand(100, 999);
        $isExist = Warning::where('warning_number', $ticket)->first();
        if ($isExist) {
            $this->checkWarningNumber();
        }
        return $ticket;
    }



    public function autopayOverstayPayment(Request $request)
    {
        $this->log->info("autopayOverstayPayment  " . json_encode($request->all()));
        $this->request = $request;
        $ticket = Ticket::with(['facility.facilityPaymentDetails', 'user'])->where('ticket_number', $request->ticket_number)->orderBy('id', "DESC")->first();
        if (!$ticket) {
            throw new NotFoundException('Invalid ticket details.');
        }
        $this->setCustomTimezone($ticket->facility_id);
        $this->user = User::find($ticket->user_id);
        $facility = $ticket->facility;
        $now = date("Y-m-d H:i:s");
        if (strtotime(date("Y-m-d H:i:s", strtotime($now))) <= strtotime(date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime)))) {
            throw new ApiGenericException('Sorry! You do not in overstay time.');
        }


        $existTicketExtend = TicketExtend::where("ticket_id", $ticket->id)->orderBy("id", "Desc")->first();
        if ($existTicketExtend) {
            $checkoutTime = $existTicketExtend->checkout_time;
        } else {
            $checkoutTime = $ticket->checkout_datetime;
        }
        if (strtotime(date("Y-m-d H:i:s", strtotime($now))) > strtotime(date("Y-m-d H:i:s", strtotime($checkoutTime)))) {
            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkoutTime);
            $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);

            $diff_in_mins = $arrival_time->diffInRealMinutes($from);
            $diff_in_hours = QueryBuilder::getLengthInHours($diff_in_mins);;
        }


        //$from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
        //$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime)));
        //$diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->checkout_datetime);
        $isMember = 0;
        if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0 && $facility->is_price_band == 0) {
            $rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
        } else {
            $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE, $isMember);
        }

        $taxFee       = $facility->getTaxRate($rate);
        $processingFee  = 0.00;  // drive up processing fee

        $total = $rate['price'] + $taxFee + $processingFee;
        $overstay = new TicketExtend();
        if (isset($facility->facilityPaymentDetails) && ($facility->facilityPaymentDetails->facility_payment_type_id == '1')) {
            $this->log->info("Make autopay Planet Payment new  ");

            $cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $ticket->user_id)->orderBy("id", "DESC")->first();
            if (!$cardCheck) {
                throw new ApiGenericException("Card details are invalid");
            }
            $this->request->request->add(['payment_profile_id' => $cardCheck->token]);
            $response = PlanetPaymentGateway::planetPaymentByProfile($ticket, $total, $request);
            $this->log->info("Response Data Planet (Saved Cards): " . json_encode($response));

            $refundstatus = $response;
            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
            }

            $saveTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
            if ($saveTransaction) {
                $overstay->anet_transaction_id = $saveTransaction->id;
                $overstay->save();
            }
        } else if (isset($facility->facilityPaymentDetails) && ($facility->facilityPaymentDetails->facility_payment_type_id == '2')) {
            $this->log->info("Make autopay Datacap Payment new  ");
            $card_month = substr($this->request->expiration_date, 0, 2);
            $card_year = substr($this->request->expiration_date, -2);
            $this->request->request->add(['expiration_month' => $card_month]);
            $this->request->request->add(['expiration_year' => $card_year]);

            $cardCheck = DatacapTransaction::whereNull('deleted_at')->where('ticket_id', $ticket->id)->orderBy("id", "DESC")->first();
            if (!$cardCheck) {
                throw new ApiGenericException("Payment Profile Not Found.");
            }
            $request->request->add(['expiration' => $cardCheck->expiry]);
            $request->request->add(['card_last_four' => $cardCheck->card_last_four]);
            $card_month = substr($request->expiry, 0, 2);
            $card_year = substr($request->expiry, -2);
            $request->request->add(['expiration_month' => $card_month]);
            $request->request->add(['expiration_year' => $card_year]);
            $this->log->info("Payment Profile Data --" . $cardCheck);

            $data['Token'] = $cardCheck->token;
            if ($total > 0) {
                $amount = ($facility->FacilityPaymentDetails->datacap_payment_env == 'test') ? '3.00' : $total;
                $data['Amount'] = $amount;
                $data['Token'] = $cardCheck->token;
                $data['ecommerce_mid'] = $facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                $data["CardHolderID"] = "Allow_V2";
                $ecommerce_mid = $facility->facilityPaymentDetails->datacap_ecommerce_mid;
                $url = $facility->facilityPaymentDetails->datacap_script_url;
                $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($data, $ecommerce_mid, $url);
                if ($paymentResponse["Status"] == "Error") {
                    $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                    if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                        throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                    } else {
                        throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                    }
                }
            } else {
                throw new ApiGenericException("Payment Token Not Generated. Please try again after sometime.");
            }

            if ($paymentResponse['Status'] == 'Approved') {
                $paymentStatus = DatacapPaymentGateway::saveDatacapTransaction($this->request, $paymentResponse, $ticket->user_id, '');
                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($paymentStatus));
                if ($paymentStatus) {
                    $overstay->anet_transaction_id = $paymentStatus->id;
                    $overstay->save();
                }
            } else {
                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
            }
        } elseif (isset($facility->facilityPaymentDetails) && ($facility->facilityPaymentDetails->facility_payment_type_id == '4')) {
            $this->log->info("Make autopay Heartland Payment ");

            $cardCheck = DatacapTransaction::whereNull('deleted_at')->where('ticket_id', $ticket->id)->first();
            $this->log->info("Payment Profile Data --" . $cardCheck);
            if ($cardCheck) {
                $data['transactionId'] = $cardCheck->trans_id;
                $final_amount = $total;
                $amount = ($facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $final_amount;
                if ($final_amount > 0) {
                    try {
                        $amount = number_format($amount, 2);
                        $paymentRequest = new Request([
                            'token'   => $cardCheck->token,
                            'Amount' => $amount
                        ]);

                        $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($paymentRequest, $facility);
                        $this->log->info("Payment record -- " . json_encode($paymentResponse));

                        if ($paymentResponse->responseMessage == 'APPROVAL') {
                            $request = new Request([
                                'total'   => $final_amount,
                                'card_last_four' => $cardCheck->card_last_four,
                                'expiration' => $cardCheck->expiry,
                                'expiration_date' => $cardCheck->expiry
                            ]);
                            $saveTransaction = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $ticket->user_id);
                            $this->log->info("Payment Transaction Data  --" . $saveTransaction);
                            if ($saveTransaction) {
                                $overstay->anet_transaction_id = $saveTransaction->id;
                                $overstay->save();
                            }
                        }
                    } catch (\Exception $e) {
                        $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
                        $this->log->error($msg);
                        throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                    }
                }
            }
        } else {
            throw new ApiGenericException("Payment details not defined for this partner.");
        }

        //$checkoutTime = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime)))->addHours($diff_in_hours);
        $overstay->ticket_id = $ticket->id;
        $overstay->facility_id = $facility->id;
        $overstay->length = $diff_in_hours;
        $overstay->total = $total;
        $overstay->grand_total = $total;
        $overstay->ticket_number = $ticket->ticket_number;
        $overstay->checkin_time = date('Y-m-d H:i:s', strtotime($arrival_time));
        $currentTime = Carbon::parse('now');
        $checkoutTime = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
        $overstay->checkout_time = $checkoutTime;
        $overstay->partner_id = $ticket->partner_id;
        $overstay->processing_fee = $processingFee;
        $overstay->tax_fee = $taxFee;
        $overstay->comment = ucwords(Auth::user()->name) . " autopay amount charged $" . $total;
        $overstay->base_length = $diff_in_hours;
        $overstay->save();

        $ticket->is_extended = '1';
        $ticket->is_closed = '0';
        $ticket->save();
        Artisan::queue('email:pave-parking-ungated-checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));

        return $overstay;
    }


    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }


    public function getNoOfDaysRate(Request $request)
    {
        $ticket = Ticket::where('ticket_number', $request->ticket_number)->orderBy('id', 'Desc')->first();
        $now = date("Y-m-d H:i:s");
        if (!$ticket) {
        }

        if ($request->days == '') {
            $zeroAmout = 0;
            $data['tax_fee'] = number_format($zeroAmout, 2);
            $data['parking_amount'] = number_format($zeroAmout, 2);
            $data['processing_fee'] = number_format($zeroAmout, 2);
            $data['rate'] = number_format($zeroAmout, 2);;
            $data['checkout_datetime'] = date("m/d/Y", strtotime($ticket->checkout_datetime)) . ' ' . date("g:i A", strtotime($ticket->checkout_datetime));
            return $data;
        }
        $now = date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime, strtotime("+1 hours")));
        $arrival_time = Carbon::createFromFormat("Y-m-d H:i:s", date('Y-m-d H:i:s', strtotime($ticket->checkout_datetime, strtotime("+1 hours"))));
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s', strtotime($now . ' + ' . $request->days . ' hours')));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours > 24) {
            $diff_in_hours = 24;
        }
        $isMember = 0;
        $facility =  Facility::find($ticket->facility_id);
        /*$rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
        
        if($rate == false){
            
        }*/
        $rate = [];
        $categoryId = '';
        $is_resident_user = '0';
        if ($ticket->user->license_number != '') {

            $mystring = $ticket->user->license_number;
            $mystring = trim($mystring); //removes any spaces from beginning of the string
            $number = substr($mystring, 1);

            if (ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring) == 9) {
                $is_resident_user = '1';
                $categoryId = self::RESIDENT_ID;
            } else {
                $is_resident_user = '0';
                $categoryId = self::NON_RESIDENT_ID;
            }
        }


        $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
        if (!$residentRate) {
            return Redirect::to('diamond-error-facility');
        }
        //$price_in_hours = $diff_in_hours - $residentRate->free_hours;
        /*if($diff_in_hours > $residentRate->free_hours){
                $price_in_hours = $diff_in_hours - $residentRate->free_hours;
            }*/
        $rate['price'] = $residentRate->price * $diff_in_hours;

        $data['tax_fee'] = $facility->tax_rate;
        $data['parking_amount'] = number_format($rate['price'], 2);
        $days = $diff_in_hours;
        //$processingFee = $facility->processing_fee;


        $facilityFee = FacilityFee::where("facility_id",  $facility->id)->where("name", "citation_processing_fee")->first();
        $facilityProcessingFee = 0.00;
        if ($facilityFee) {
            $facilityProcessingFee = $facilityFee['val'];
        }
        $processingFee = $facilityProcessingFee;
        $data['processing_fee'] = number_format($processingFee, 2);
        $taxFee = $facility->tax_rate;
        //$rate['price'] = $rate['price'] + $taxFee + $processingFee;        
        $data['rate'] = number_format($rate['price'] + $taxFee + $processingFee, 2);
        $data['checkout_datetime'] = date("m/d/Y", strtotime($from)) . ' ' . date("g:i A", strtotime($from));
        return $data;
    }


    public function checkoutList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $now = date("Y-m-d");
            $yesterday = date("Y-m-d", strtotime("-1 days"));
            $ticket = Ticket::with(['user', 'ticketExtend'])->whereDate('checkout_datetime', '=', $now)->where('facility_id', $facility->id)->orderBy('check_in_datetime', "DESC")->paginate(20);
            if (count($ticket) > 0) {
                foreach ($ticket as $key => $value) {
                    $value['extend_amount'] = $value->grand_total;
                    $extendAmount = 0;
                    $extendProcessingFee = 0;
                    $extendTaxFee = 0;
                    $extendParkingAmount = 0;
                    $existTicketExtend = $value->ticketExtend;
                    if (isset($value->ticketExtend) && count($value->ticketExtend) > 0) {
                        $ticket[$key]['checkout_time'] = $existTicketExtend[0]->checkout_time;
                        $ticket[$key]['checkout_datetime'] = $existTicketExtend[0]->checkout_time;
                        foreach ($existTicketExtend as $k => $v) {
                            $extendAmount += $v->grand_total;
                            $extendProcessingFee += $v->processing_fee;
                            $extendTaxFee += $v->tax_fee;
                            $extendParkingAmount += $v->grand_total - $v->processing_fee - $v->tax_fee;
                        }

                        $ticket[$key]['parking_amount'] = sprintf("%.2f", $extendParkingAmount + $value['parking_amount']);
                        $ticket[$key]['processing_fee'] = sprintf("%.2f", $extendProcessingFee + $value['processing_fee']);
                        $ticket[$key]['tax_fee'] = sprintf("%.2f", $extendTaxFee + $value['tax_fee']);
                        $ticket[$key]['extend_amount'] = sprintf("%.2f", $extendAmount + $value['grand_total']);
                    }
                }
            }

            $data['today_checkout'] = $ticket;
            $ticket = Ticket::with(['user', 'ticketExtend'])->whereDate('checkout_datetime', '=', $yesterday)->where('facility_id', $facility->id)->orderBy('check_in_datetime', "DESC")->paginate(20);
            if (count($ticket) > 0) {
                foreach ($ticket as $key => $value) {
                    $value['extend_amount'] = $value->grand_total;
                    $extendAmount = 0;
                    $extendProcessingFee = 0;
                    $extendTaxFee = 0;
                    $extendParkingAmount = 0;
                    $existTicketExtend = $value->ticketExtend;
                    if (isset($value->ticketExtend) && count($value->ticketExtend) > 0) {
                        $ticket[$key]['checkout_time'] = $existTicketExtend[0]->checkout_time;
                        $ticket[$key]['checkout_datetime'] = $existTicketExtend[0]->checkout_time;
                        foreach ($existTicketExtend as $k => $v) {
                            $extendAmount += $v->grand_total;
                            $extendProcessingFee += $v->processing_fee;
                            $extendTaxFee += $v->tax_fee;
                            $extendParkingAmount += $v->grand_total - $v->processing_fee - $v->tax_fee;
                        }

                        $ticket[$key]['parking_amount'] = sprintf("%.2f", $extendParkingAmount + $value['parking_amount']);
                        $ticket[$key]['processing_fee'] = sprintf("%.2f", $extendProcessingFee + $value['processing_fee']);
                        $ticket[$key]['tax_fee'] = sprintf("%.2f", $extendTaxFee + $value['tax_fee']);
                        $ticket[$key]['extend_amount'] = sprintf("%.2f", $extendAmount + $value['grand_total']);
                    }
                }
            }
            $data['yesterday_checkout'] = $ticket;
            return $data;
        }
    }

    public function unavailableVehicleList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            $this->setCustomTimezone($facility->id);
            $ticket = Ticket::with(['user'])/*->where('is_vehicle_present', '0')*/->where('is_closed', '0')->whereDate('scan_date', '!=', date("Y-m-d"))->where('facility_id', $facility->id)->paginate(20);
            $citaition = TicketCitation::with(['user'])/*->where('is_vehicle_present', '0')*/->where('is_closed', '0')->where('is_avoid', '0')->whereDate('scan_date', '!=', date("Y-m-d"))->where('facility_id', $facility->id)->paginate(20);
            $data = [];
            if (count($ticket) > 0) {
                foreach ($ticket as $key => $value) {
                    $value['extend_amount'] = $value->grand_total;
                    $extendAmount = 0;
                    $extendProcessingFee = 0;
                    $extendTaxFee = 0;
                    $extendParkingAmount = 0;
                    $existTicketExtend = $value->ticketExtend;
                    if (isset($value->ticketExtend) && count($value->ticketExtend) > 0) {
                        $ticket[$key]['checkout_time'] = $existTicketExtend[0]->checkout_time;
                        $ticket[$key]['checkout_datetime'] = $existTicketExtend[0]->checkout_time;
                        foreach ($existTicketExtend as $k => $v) {
                            $extendAmount += $v->grand_total;
                            $extendProcessingFee += $v->processing_fee;
                            $extendTaxFee += $v->tax_fee;
                            $extendParkingAmount += $v->grand_total - $v->processing_fee - $v->tax_fee;
                        }

                        $ticket[$key]['parking_amount'] = sprintf("%.2f", $extendParkingAmount + $value['parking_amount']);
                        $ticket[$key]['processing_fee'] = sprintf("%.2f", $extendProcessingFee + $value['processing_fee']);
                        $ticket[$key]['tax_fee'] = sprintf("%.2f", $extendTaxFee + $value['tax_fee']);
                        $ticket[$key]['extend_amount'] = sprintf("%.2f", $extendAmount + $value['grand_total']);
                    }
                }
                foreach ($ticket as $key => $value) {
                    $ticket[] = $value;
                }
            }
            if (count($citaition) > 0) {
                foreach ($citaition as $key => $value) {
                    $ticket[] = $value;
                }
            }

            return $ticket;
        }
    }


    public function unavailableVehicleCount(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            $this->setCustomTimezone($facility->id);
            $ticket = Ticket::with(['user'])/*->where('is_vehicle_present', '0')*/->where('is_closed', '0')->whereDate('scan_date', '!=', date("Y-m-d"))->where('facility_id', $facility->id)->count();
            $citaition = TicketCitation::with(['user'])/*->where('is_vehicle_present', '0')*/->where('is_closed', '0')->where('is_avoid', '0')->whereDate('scan_date', '!=', date("Y-m-d"))->where('facility_id', $facility->id)->count();


            return $ticket + $citaition;
        }
    }


    public function unavailableVehicleSearch(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            $this->setCustomTimezone($facility->id);
            $ticket = [];
            $ticket = Ticket::with(['user'])->where('is_closed', '0')->whereDate('scan_date', '!=', date("Y-m-d"))->where('facility_id', $facility->id)->where("license_plate", $request->search)->orWhere("ticket_number", $request->search)->paginate(20);
            $citaition = TicketCitation::with(['user'])->where('is_avoid', '0')->where('is_closed', '0')->whereDate('scan_date', '!=', date("Y-m-d"))->where('facility_id', $facility->id)->where("license_plate", $request->search)->orWhere("citation_number", $request->search)->paginate(20);
            $data = [];

            if (count($ticket) > 0) {
                foreach ($ticket as $key => $value) {
                    $value['extend_amount'] = $value->grand_total;
                    $extendAmount = 0;
                    $extendProcessingFee = 0;
                    $extendTaxFee = 0;
                    $extendParkingAmount = 0;
                    $existTicketExtend = $value->ticketExtend;
                    if (isset($value->ticketExtend) && count($value->ticketExtend) > 0) {
                        $ticket[$key]['checkout_time'] = $existTicketExtend[0]->checkout_time;
                        $ticket[$key]['checkout_datetime'] = $existTicketExtend[0]->checkout_time;
                        foreach ($existTicketExtend as $k => $v) {
                            $extendAmount += $v->grand_total;
                            $extendProcessingFee += $v->processing_fee;
                            $extendTaxFee += $v->tax_fee;
                            $extendParkingAmount += $v->grand_total - $v->processing_fee - $v->tax_fee;
                        }

                        $ticket[$key]['parking_amount'] = sprintf("%.2f", $extendParkingAmount + $value['parking_amount']);
                        $ticket[$key]['processing_fee'] = sprintf("%.2f", $extendProcessingFee + $value['processing_fee']);
                        $ticket[$key]['tax_fee'] = sprintf("%.2f", $extendTaxFee + $value['tax_fee']);
                        $ticket[$key]['extend_amount'] = sprintf("%.2f", $extendAmount + $value['grand_total']);
                    }
                }
            }

            /*if (count($ticket) > 0) {
                foreach ($ticket as $key => $value) {
                    $ticket[] = $value;
                }
            }*/
            if (count($citaition) > 0) {
                foreach ($citaition as $k => $value) {
                    $ticket[] = $value;
                }
            }

            return $ticket;
        }
    }

    public function citationUpdate(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            $ticket = TicketCitation::where('citation_number', $request->citation_number)->where('facility_id', $facility->id)->first();
            if (!$ticket) {
                throw new ApiGenericException('Invalid citation.');
            }
            $ticket->comment = $request->comment;
            $ticket->updated_by = Auth::user()->id;
            $ticket->avoid_comment = $request->comment;
            $ticket->is_avoid = '1';
            //for menlo specific
            if (isset($request->citation_void_code_id)) {
                $ticket->citation_void_code_id = $request->citation_void_code_id;
            }
            $ticket->save();
            return "Citation void successfully.";
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }


    public function unavailableVehicleUpdate(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            $this->setCustomTimezone($facility->id);
            if ($request->all == '1') {
                Ticket::where('facility_id', $facility->id)->whereDate('scan_date', '!=', date("Y-m-d"))->update(["is_closed" => "1", "remark" => $request->comment, "closed_date" => date("Y-m-d H:i:s")]);
                TicketCitation::where('facility_id', $facility->id)->whereDate('scan_date', '!=', date("Y-m-d"))->update(["is_closed" => "1", "comment" => $request->comment, "closed_date" => date("Y-m-d H:i:s")]);
                return "All open checkin/citation closed successfully.";
            } else {
                if ($request->type == 'checkin') {
                    $ticket = Ticket::where('ticket_number', $request->ticket_number)->where('facility_id', $facility->id)->first();
                    if (!$ticket) {
                        throw new ApiGenericException('Invalid ticket.');
                    }
                    $ticket->remark = $request->comment;
                    $ticket->is_closed = '1';
                    $ticket->closed_date = date("Y-m-d H:i:s");
                    $ticket->updated_by = Auth::user()->id;
                    $ticket->save();
                    //$ticket->delete();
                    return "Ticket closed successfully.";
                } elseif ($request->type == 'citation') {
                    $ticket = TicketCitation::where('citation_number', $request->citation_number)->where('facility_id', $facility->id)->first();
                    if (!$ticket) {
                        throw new ApiGenericException('Invalid citation.');
                    }
                    $ticket->comment = $request->comment;
                    $ticket->is_closed = '1';
                    $ticket->closed_date = date("Y-m-d H:i:s");
                    $ticket->updated_by = Auth::user()->id;
                    $result = $ticket->save();
                    //$ticket->delete();
                    return "Citation closed successfully.";
                } else {
                    throw new ApiGenericException('Invalid type of ticket/citation.');
                }
            }
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }

    public function getPartnerFacility(Request $request)
    {

        if (Auth::check() && Auth::user()->user_type == '10') {
            $secret = OauthClient::where('partner_id', Auth::user()->created_by)->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            $facility_ids =  BusinessFacilityPolicy::where('business_id', Auth::user()->business_id)->pluck('facility_id');
        } else if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            $facility_ids = PermitRate::get()->pluck('facility_id');
        } else {
            throw new ApiGenericException('Invalid partner.');
        }


        $facilities = DB::table('facilities')
            ->join('geolocations', 'geolocations.locatable_id', '=', 'facilities.id')
            ->leftjoin('facility_brand_settings', 'facility_brand_settings.facility_id', '=', 'facilities.id')
            ->select('facilities.id as id', 'facilities.full_name as name', 'facilities.entrance_location', 'facilities.between_streets', 'facilities.citation_format', 'geolocations.district', 'facility_brand_settings.logo', 'facility_brand_settings.color', 'facility_brand_settings.id as brand_setting_id', 'geolocations.state')
            ->where('facilities.owner_id', $secret->partner_id)
            ->where('facilities.active', '=', '1')
            ->where('geolocations.locatable_type', 'App\\Models\\Facility');
        //->wherein('id',$facility_ids)
        if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12' || Auth::user()->user_type == '9') {
            $facility = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
            if ($facility) {
                $facilities =   $facilities->wherein('facilities.id', $facility);
            }
        } else if (Auth::user()->user_type == '10') {
            $facilities =   $facilities->wherein('facilities.id', $facility_ids);
        }

        $facilities =   $facilities->get();
        if (count($facilities) > 0) {
            foreach ($facilities as $key => $facility) {
                if ($facility->logo != '') {
                    $facility->logo = config('parkengage.APP_URL') . "/facility-brand-settings-logo/" . $facility->brand_setting_id;
                } else {
                    $brandSetting = BrandSetting::where("user_id", $secret->partner_id)->first();
                    $facility->logo = config('parkengage.APP_URL') . "/brand-settings-logo/" . $brandSetting->id;
                }
                if ($facility->color != '') {
                } else {
                    $brandSetting = BrandSetting::where("user_id", $secret->partner_id)->first();
                    $facility->color = $brandSetting->color;
                }
            }
        }
        return $facilities;
    }

    public function getPartnerPermitFacility(Request $request, $slug = '', $business_id = '', $permit_type_id = '')
    {
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            $facility_id = [];
            if ($slug != '') {
                $rmExist = User::where('slug', $slug)->where('status', '1')->first();

                if ($rmExist) {
                    $facility_id = DB::table('user_facilities')->where('user_id', $rmExist->id)->whereNull('deleted_at')->pluck('facility_id');
                }
            }

            if (count($facility_id) > 0) {
                $facility_ids = PermitRate::whereIn('facility_id', $facility_id)->get()->pluck('facility_id');
            } else {
                $facility_ids = PermitRate::get()->pluck('facility_id');
            }

            #05-08-2024 PIMS-10032 dushyant add business_id for filter permit on business id basis
            if (isset($business_id) && !empty($business_id)) {
                $user_id = $business_id;
                $user_permit_remaning = UserPermitTypeMapping::where('user_id', $user_id)->get();
                if ($user_permit_remaning != null) {
                    $permitTypeIds             = $user_permit_remaning->pluck('permit_type_id');
                    $facility_ids             = PermitRate::whereIn('id', $permitTypeIds)->pluck('facility_id');
                }
            }

            $permitServiceIds = explode(',', $business_id);

            if ($secret->partner_id == config('parkengage.BUYOUT.PARTNER_ID') && isset($business_id) && !empty($business_id) && array_intersect($permitServiceIds, config('parkengage.PERMIT_SERVICES.PERMIT_SERVICES_IDS'))) {

                #05-11-2024 DD pims-11105
                if (isset($permit_type_id) && !empty($permit_type_id)) {
                    $facility_ids = PermitServicesFacilityMapping::join('permit_services_facility_rate_desc_mapping as psfm', function ($join)  use ($permit_type_id) {
                        $join->on('psfm.permit_services_facility_id', '=', 'permit_services_facility_mapping.id')
                            ->where('psfm.permit_rate_description_id', '=', $permit_type_id);
                    })
                        ->whereIn('permit_service_id', $permitServiceIds)
                        ->groupBy('facility_id')
                        ->get()
                        ->pluck('facility_id');
                } else {
                    $facility_ids = PermitServicesFacilityMapping::whereIn('permit_service_id', $permitServiceIds)->groupBy('facility_id')->get()->pluck('facility_id');
                }
            }

            $facilities = Facility::with(['facilityConfiguration', 'FacilityPaymentDetails'])->select(
                'id',
                'short_name',
                'full_name',
                'active',
                'license_format',
                'license_min_lenght',
                'license_max_lenght',
                'fix_length',
                'is_permit_purchase_enabled'
            )->where('active', 1)->with(array('geolocations' => function ($query) {
                $query->select('address_1', 'address_2', 'city', 'state', 'zip_code', 'locatable_id');
            }))->where('owner_id', $secret->partner_id)->wherein('id', $facility_ids)->where('is_available', '1')->whereIn('facility_booking_type', [0, 2])->orderByRaw("CASE WHEN id = " . config('parkengage.USM_FACILITY') . " THEN 0 ELSE 1 END")->get();
            #PIMS-11259 DD fist index 167/407 prod/stag facility to come
            if ($request->rm_id) {
                $customer_menu = CustomerPortalPermission::where("partner_id", $secret->partner_id)->where('rm_id', $request->rm_id)->orderBy('list_order', 'ASC')->get();
            } else {
                $customer_menu = CustomerPortalPermission::where("partner_id", $secret->partner_id)->whereNull('rm_id')->orderBy('list_order', 'ASC')->get();
            }

            $customer_permissions = [];
            if ($customer_menu) {
                foreach ($customer_menu as $key => $value) {
                    if ($value->type == '0') {
                        $customer_permissions["Main Menu"][] = $value;
                    }
                    if ($value->type == '1') {
                        $customer_permissions["Account Menu"][] = $value;
                    }
                    if ($value->type == '2') {
                        $customer_permissions["Access Permissions"][] = $value;
                    }
                    if ($value->type == '3') {
                        $customer_permissions["Restricted Permissions"][] = $value;
                    }
                }
                if ($request->is_mobile_request == '1') {
                    $data['facilities'] = $facilities;
                    $data['customer_portal_permission'] = $customer_permissions;
                    return $data;
                }
            }
            return $facilities;
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }


    public function reopenStatusUpdate(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
                if (!$facility) {
                    throw new ApiGenericException('Invalid Facility.');
                }
            }

            $this->setCustomTimezone($facility->id);
            if ($request->citation_number != '') {
                $ticket = TicketCitation::where('citation_number', $request->citation_number)->where('facility_id', $facility->id)->first();
                $msg = "Ticket ";
                if (!$ticket) {
                    throw new ApiGenericException('Invalid citation.');
                }
                $ticket->is_reopen = '1';
            }
            if ($request->ticket_number != '') {
                $ticket = Ticket::where('ticket_number', $request->ticket_number)->where('facility_id', $facility->id)->first();
                $msg = "Citation ";
                if (!$ticket) {
                    throw new ApiGenericException('Invalid ticket.');
                }
            }

            //$ticket->comment = $request->comment;
            $ticket->is_closed = '0';
            $ticket->scan_date = date("Y-m-d H:i:s");
            $ticket->updated_by = Auth::user()->id;
            $result = $ticket->save();
            return $msg . "reopen successfully.";
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }


    public function validatePartnerDevice(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            $device = PartnerDevice::where('partner_id', $secret->partner_id);

            if ($request->header('X-FacilityID') != '') {
                $device->where('facility_id', $request->header('X-FacilityID'));
            }
            $device = $device->get();

            $facilityExist = Facility::find($request->header('X-FacilityID'));

            if ($facilityExist->app_installation > 0) {

                $deviceExist = PartnerDevice::where('facility_id', $request->header('X-FacilityID'))->where('serial_number', $request->serial_number)->first();

                if ($deviceExist) {
                    return "Valid device.";
                }

                if (count($device) == $facilityExist->app_installation) {
                    throw new ApiGenericException('The maximum number of allocated Enforcement App licenses has been exhausted. Please reach out to ParkEngage support to increase the licenses.');
                }

                $device = new PartnerDevice();
                $device->serial_number = $request->serial_number;
                $device->partner_id = $secret->partner_id;
                $device->facility_id = $facilityExist->id;
                $device->device = $request->device;
                $device->save();
            } else {
                throw new ApiGenericException('The maximum number of allocated Enforcement App licenses has been exhausted. Please reach out to ParkEngage support to increase the licenses.');
            }
            /*if(!$device){
                throw new ApiGenericException('Device not found under this partner.');        
            }*/
            return "Valid device.";
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }


    public function todayClosedVehicleList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }

            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $this->setCustomTimezone($facility->id);

            $ticket = Ticket::with(['user'])->where('is_closed', '1')->whereDate('closed_date', '=', date("Y-m-d"))->where('facility_id', $facility->id)->orderBy("id", "DESC")->paginate(20);
            $citaition = TicketCitation::with(['user'])->where('is_closed', '1')->whereDate('closed_date', '=', date("Y-m-d"))->where('facility_id', $facility->id)->orderBy("id", "DESC")->paginate(20);
            $data = [];
            if (count($ticket) > 0) {
                foreach ($ticket as $key => $value) {
                    $ticket[] = $value;
                }
            }
            if (count($citaition) > 0) {
                foreach ($citaition as $key => $value) {
                    $ticket[] = $value;
                }
            }
            if (!$ticket) {
                return  $ticket;
            }

            return $ticket;
        }
    }

    /*
    public function allPermitData(Request $request)
    {
        $query = PermitVehicle::query();
        //for date
        if (isset($request->license_plate_number) && ($request->license_plate_number != '')) {
            $query->where('license_plate_number', 'like', "{$request->license_plate_number}%");
        }
        //expired
        $today = date('Y-m-d');
        if (isset($request->expired_flag)) {
            if ($request->expired_flag == '1') {
                $query->whereHas('vehicles', function ($q) use ($today) {
                    $q->where('desired_end_date', '>', $today);
                });
            }
            if ($request->expired_flag == '2') {
                $query->whereHas('vehicles', function ($q) use ($today) {
                    $q->where('desired_end_date', '<=', $today);
                });
            }
        }

        $query->whereHas('vehicles', function ($q) {
            $q->whereNull('deleted_at');
        });

        $query->with('vehicles');
        return $query->paginate(20);
    }
    */

    /*
    public function checkUserPermit(Request $request)
    {
        $permitVehicle = PermitVehicle::with([
            'vehicles' => function ($query) {
                $query->where('desired_end_date', '>=', date("Y-m-d"));
            }
        ])->where('license_plate_number', $request->license_plate_number)->orderBy('id', 'DESC')->first();

        if (!$permitVehicle) {
            throw new ApiGenericException('No permit found.');
        }
        if (isset($permitVehicle->vehicles->desired_end_date)) {

            $today = strtotime(date("Y-m-d H:i:s"));
            $endDate = strtotime($permitVehicle->vehicles->desired_end_date);
            $permitVehicle->vehicles['is_expired'] = 0;
            if ($today > $endDate) {
                $permitVehicle->vehicles['is_expired'] = 1;
            }
            $permitVehicle->vehicles['desired_start_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_start_date);
            $permitVehicle->vehicles['desired_end_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_end_date);
            return $permitVehicle;
        } else {

            $permitVehicle = PermitVehicle::with('vehicles')->where('license_plate_number', $request->license_plate_number)->orderBy('id', 'DESC')->first();
            if (isset($permitVehicle->vehicles->desired_end_date)) {

                $today = strtotime(date("Y-m-d H:i:s"));
                $endDate = strtotime($permitVehicle->vehicles->desired_end_date);
                $permitVehicle->vehicles['is_expired'] = 0;
                if ($today > $endDate) {
                    $permitVehicle->vehicles['is_expired'] = 1;
                }
                $permitVehicle->vehicles['desired_start_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_start_date);
                $permitVehicle->vehicles['desired_end_date'] = $this->getDaySufixFormat($permitVehicle->vehicles->desired_end_date);
                return $permitVehicle;
            } else {
                throw new ApiGenericException('This permit has been expired or not valid.');
            }
        }
    }
    */

    public function markWarning(Request $request)
    {
        if ((Auth::user()->user_type == '12')) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
        } else if ((Auth::user()->user_type == '4')) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else if (Auth::user()->user_type == '3') {
            $partner_id = Auth::user()->id;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        } else if (Auth::user()->user_type == '9') {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else {
            $partner_id = $request->partner_id;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        }
        $this->validate($request, ['license_plate' => 'required'], ['license_plate.required' => 'License plate is required.']);
        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
        }
        if ($request->header('X-FacilityID') != '') {
            $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
        } else {
            $facility = Facility::where('owner_id', $secret->partner_id)->first();
        }

        $this->setCustomTimezone($facility->id);

        $now = date("Y-m-d H:i:s");
        $data['license_plate'] = $request->license_plate;
        $data['warning_number'] = $this->checkWarningNumber();
        $data['facility_id'] = $facility->id;
        $data['partner_id'] = $facility->owner_id;
        $data['is_vehicle_present'] = '1';
        $data['scan_date'] = $now;
        $data['updated_by'] = Auth::user()->id;
        $data['comment'] = $request->comment;
        $data['violation'] = "NO PAYMENT";
        $data['rm_id'] = $rm_id;
        $warning = Warning::create($data);
        return $warning;
    }

    public function warningList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            $this->setCustomTimezone($facility->id);
            //$data = Warning::where("facility_id", $facility->id)->get();
            $data['open_warning'] = Warning::/*whereDate('checkin_time', '=', $today)->*/with('user')->where('is_closed', '0')->where('facility_id', $facility->id)->orderBy("id", "DESC")->paginate(20);
            $data['closed_warning'] = Warning::/*whereDate('checkin_time', '=', $yesterday)->*/with('user')->where('is_closed', '1')->where('facility_id', $facility->id)->orderBy("id", "DESC")->paginate(20);
            return $data;
        }
    }

    public function warningCitationDetails(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $neighborhoodIds = ($facility && $facility->neighborhood_id > 1)
                ? Facility::where('neighborhood_id', $facility->neighborhood_id)->pluck('id')->toArray()
                : [$facility->id];

            $this->setCustomTimezone($facility->id);
            $data = [];
            //$user = [];
            if ($request->warning_number != '') {
                $warning = Warning::with(['warningInfraction.warningInfractionReason', 'warningInfractionOther', 'warningImage'])->where("warning_number", $request->warning_number)->whereIn('facility_id', $neighborhoodIds)->first();
                if (!$warning) {
                    throw new ApiGenericException('Invalid warning number.');
                }
                $warning = $warning->toArray();
                $warning['ticket_citation_infraction'] = $warning['warning_infraction'];

                unset($warning['warning_infraction']);

                foreach ($warning['ticket_citation_infraction'] as $key => $val) {
                    $warning['ticket_citation_infraction'][$key]['ticket_citation_infraction_reason'] = $val['warning_infraction_reason'];
                    unset($warning['ticket_citation_infraction'][$key]['warning_infraction_reason']);
                }
                $warning['ticket_citation_infraction_other'] = $warning['warning_infraction_other'];
                #PIMS-12423 DD
                if ($secret->partner_id == config('parkengage.PARTNER_MANLO')) {
                    $warning['top_heading'] = $facility->top_heading != '' ? $facility->top_heading : "Parking Citation";
                    $warning['top_subheading'] = "This warning is for a violation of California Vehicle Code or local parking ordinance. You must respond within 21 calendar days.";
                } else {
                    $warning['top_heading'] = $facility->top_heading != '' ? $facility->top_heading : "Notice of Parking Infraction(s)";
                }
                unset($warning['warning_infraction_other']);

                $warning['printer_type'] = "zebra";
                $warning['show_warning_qrcode'] = "0";
                $warning['small_width'] = 0;
                if (self::SHOW_TICKET_LIST_PARTNER  == $secret->partner_id) {
                    $warning['show_warning_qrcode'] = "1";
                    $warning['printer_type'] = "star";
                }

                if ($secret->partner_id == config('parkengage.PARTNER_PCI')) {
                    $warning['show_warning_qrcode'] = 1;
                    $warning['printer_type'] = "zebra";
                    $warning['small_width'] = 1;
                }

                if ($secret->partner_id == config('parkengage.PARTNER_MANLO')) {
                    $warning['show_warning_qrcode'] = 1;
                    $warning['printer_type'] = "zebra";
                    $warning['small_width'] = 2;
                }

                if ($secret->partner_id == config('parkengage.PARTNER_RevPass')) {
                    $warning['show_warning_qrcode'] = 1;
                    $warning['printer_type'] = "zebra";
                    $warning['small_width'] = 2;
                }

                $warning['account_number'] = "";
                $warning['ticketech_code'] = "";
                $warning = (object) $warning;
                if ($warning->permit_request_id != '') {
                    $permitRequest = PermitRequest::find($warning->permit_request_id);
                    if (isset($permitRequest->id)) {
                        $warning->account_number = $permitRequest->account_number;
                    }
                }
                if ($warning->reservation_id != '') {
                    $reservation = Reservation::find($warning->reservation_id);
                    if (isset($reservation->id)) {
                        $warning->ticketech_code = $reservation->ticketech_code;
                    }
                }
                $data['details'] = $warning;
                //$citation = TicketCitation::with(['user'])->where("citation_number", $request->citation_number)->where("facility_id", $facility->id)->get();
                $data['warning_history'] = Warning::where("warning_number", '!=', $request->warning_number)->where("license_plate", $warning->license_plate)->whereIn('facility_id', $neighborhoodIds)->get();

                $citation = TicketCitation::with(['user', 'appeal'])->where("license_plate", $warning->license_plate)->whereIn('facility_id', $neighborhoodIds)->get();
                if (count($citation) > 0) {
                    foreach ($citation as $key => $value) {
                        if (isset($value->user->id)) {
                            $data['user'] = $value->user;
                        }
                    }
                }
                $data['citation_history'] = $citation;
                //$data['user'] = $user;
                if ($warning->updated_by != '') {
                    $employee = User::select("emp_id")->where("id", $warning->updated_by)->first();
                    if ($employee) {
                        $data['emp_id'] = $employee->emp_id;
                    }
                }
            }
            if ($request->citation_number != '') {
                $citation = TicketCitation::with(['ticketCitationInfraction.ticketCitationInfractionReason', 'ticketCitationInfractionOther', 'ticketCitationImage', 'appeal'])->where("citation_number", $request->citation_number)->whereIn('facility_id', $neighborhoodIds)->first();

                if (!$citation) {
                    throw new ApiGenericException('Invalid citation number.');
                }

                $permitRequest = PermitRequest::where('id', $citation->permit_request_id)->first();
                //$citation = TicketCitation::with(['user'])->where("citation_number", $request->citation_number)->where("facility_id", $facility->id)->get();
                $citation['show_qrcode'] = "0";
                $citation['printer_type'] = "zebra";
                $citation['small_width'] = 0;
                if (self::SHOW_TICKET_LIST_PARTNER  == $secret->partner_id) {
                    $citation['show_qrcode'] = 1;
                    $citation['printer_type'] = "star";
                }
                if ($secret->partner_id == config('parkengage.PARTNER_PCI')) {
                    $citation['show_qrcode'] = 1;
                    $citation['printer_type'] = "zebra";
                    $citation['small_width'] = 1;
                }

                if ($secret->partner_id == config('parkengage.PARTNER_MANLO')) {
                    $citation['citation_pay_text'] = "Discount if pay by day 7; fine increases on day 21. If pay or contest after day 7, <U>www.pticket.com/menlocollege</U>. If mail: PO Box 11113, SJ, CA 95103. Include citation copy and address for notification.";
                    $citation['show_qrcode'] = 1;
                    $citation['printer_type'] = "zebra";
                    $citation['small_width'] = 2;
                }
                if ($secret->partner_id == config('parkengage.PARTNER_RevPass')) {
                    $citation['citation_pay_text'] = "Discount if pay by day 7; fine increases on day 21.";
                    $citation['show_qrcode'] = 1;
                    $citation['printer_type'] = "zebra";
                    $citation['small_width'] = 2;
                }

                if ($secret->partner_id == config('parkengage.PARTNER_DIAMOND')) {
                    if ($citation->is_closed == '1') {
                        $citation['show_qrcode'] = 0;
                    } elseif ($citation->penalty_fee > 0 && $citation->is_closed == '0') {
                        $ticketCitationInfraction = TicketCitationInfraction::where("ticket_citation_id", $citation->id)
                            ->where("infraction_id", '50')->first();
                        if ($ticketCitationInfraction) {
                            $citation['show_qrcode'] = 1;
                        }
                    }
                    $citation['printer_type'] = "zebra";
                }

                #PIMS-12423 DD
                if ($secret->partner_id == config('parkengage.PARTNER_MANLO')) {
                    $citation['top_heading'] = $facility->top_heading != '' ? $facility->top_heading : "Parking Citation";
                    $citation['top_subheading'] = "This citation is for a violation of California Vehicle Code or local parking ordinance. You must respond within 21 calendar days.";
                } else if ($secret->partner_id == config('parkengage.PARTNER_RevPass')) {
                    $citation['top_heading'] = $facility->top_heading != '' ? $facility->top_heading : "Parking Citation";
                    $citation['top_subheading'] = "This citation is for a violation of California Vehicle Code or local parking ordinance. You must respond within 21 calendar days.";
                } else {
                    $citation['top_heading'] = $facility->top_heading != '' ? $facility->top_heading : "Notice of Parking Infraction(s)";
                }

                $citation['account_number'] = "";
                $citation['ticketech_code'] = "";
                if (isset($permitRequest->id)) {
                    $citation['account_number'] = $permitRequest->account_number;
                }
                if ($citation->reservation_id != '') {
                    $reservation = Reservation::find($citation->reservation_id);
                    if (isset($reservation->id)) {
                        $citation['ticketech_code'] = $reservation->ticketech_code;
                    }
                }

                $data['details'] = $citation;

                $data['details']['formatted_desired_start_date'] = null;
                $data['details']['formatted_desired_end_date'] = null;
                if (isset($permitRequest) && !empty($permitRequest)) {
                    $data['details']['formatted_desired_start_date'] = date('m/d/y', strtotime($permitRequest->formatted_desired_start_date));
                    $data['details']['formatted_desired_end_date'] = date('m/d/y', strtotime($permitRequest->formatted_desired_end_date));
                }


                $data['warning_history'] = Warning::where("license_plate", $citation->license_plate)->whereIn('facility_id', $neighborhoodIds)->get();

                $citation_history = TicketCitation::with(['user', 'ticketCitationInfraction.ticketCitationInfractionReason', 'ticketCitationInfractionOther', 'appeal'])->where("citation_number", '!=', $request->citation_number)->where("license_plate", $citation->license_plate)->whereIn('facility_id', $neighborhoodIds)->get();
                if (count($citation_history) > 0) {
                    foreach ($citation_history as $key => $value) {
                        if (isset($value->user->id)) {
                            $data['user'] = $value->user;
                        }
                    }
                }
                $data['citation_history'] = $citation_history;
                //$data['user'] = $user;
                if ($citation->updated_by != '') {
                    $employee = User::select("emp_id")->where("id", $citation->updated_by)->first();
                    if ($employee) {
                        $data['emp_id'] = $employee->emp_id;
                    }
                }
            }

            return $data;
        }
    }


    public function validateLicensePlateCheckinUsm(Request $request, $license_plate, $facility_zone)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $this->setCustomTimezone($facility->id);
            $data = [];

            $facilityZone = FacilityZone::where("name", $facility_zone)->first();
            if (!$facilityZone) {
                throw new ApiGenericException('Invalid zone details.');
            }

            $permitVehicle = PermitVehicle::where('license_plate_number', $license_plate)->orderBy('id', 'DESC')->first();
            if ($permitVehicle) {

                $permitVehicleMapping = PermitVehicleMapping::where('permit_vehicle_id', $permitVehicle->id)->orderBy('id', 'DESC')->first();
                if (isset($permitVehicleMapping->permit_request_id)) {
                    // ->whereNull('cancelled_at')
                    $permitRequest = PermitRequest::with(['user', 'PermitVehicle.vehicle'])->where('facility_id', $facility->id)->where('id', $permitVehicleMapping->permit_request_id)->whereDate('desired_start_date', '<=', date("Y-m-d"))->whereDate('desired_end_date', '>=', date("Y-m-d"))->first();

                    if ($permitRequest) {

                        if ($permitRequest->facility_zone_id != $facilityZone->id) {
                            $permitRequest->zone_message = "Violation - Parked in wrong zone.";
                        }
                        $citation = TicketCitation::with('user')->where('permit_request_id', $permitRequest->account_number)->where('facility_id', $facility->id)->where('is_checkout', '0')->where('is_closed', '0')->orderBy('id', 'DESC')->first();

                        $data['permit_request'] = $permitRequest;
                        $data['permit_request']['citation'] = null;
                        if (isset($citation) && !empty($citation)) {
                            $data['permit_request']['citation'] = $citation;
                        }

                        $data['user'] = isset($permitRequest->user) ? $permitRequest->user : '';


                        return $data;
                    }
                }
            }
            $ticket = Ticket::with(['user', 'citation', 'overstay'])->where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->first();
            if (!$ticket) {
                $ticket = [];
                $citation = TicketCitation::with('user')->where('license_plate', $license_plate)->where('facility_id', $facility->id)->whereDate('payment_date', '=', date("Y-m-d"))->where('is_checkout', '0')->where('is_closed', '0')->orderBy('id', 'DESC')->first();
                if (!$citation) {
                    $citation = TicketCitation::with('user')->where('license_plate', $license_plate)->where('facility_id', $facility->id)->where('is_checkout', '0')->where('is_closed', '0')/*->whereNull('anet_transaction_id')*/->orderBy('id', 'DESC')->first();
                    if (!$citation) {
                        //throw new ApiGenericException('1No vehicle plate number found.');    
                        $user = [];
                        $citationHistory = TicketCitation::with('user')->where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->get();
                        if (count($citationHistory) > 0) {
                            foreach ($citationHistory as $key => $value) {
                                if (isset($value->user)) {
                                    $data['user'] =  $value->user;
                                    break;
                                } else {
                                    $data['user'] = $value->user;
                                }
                            }
                        } else {
                            $data['user'] = null;
                        }

                        $warningHistory = Warning::where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->get();

                        $warning = Warning::where('license_plate', $license_plate)->where('facility_id', $facility->id)->where('is_closed', '0')->orderBy('id', 'DESC')->first();

                        if (!$warning) {
                            $scanedVehicle = new ScanedVehicle();
                            $scanedVehicle->facility_id = $facility->id;
                            $scanedVehicle->partner_id = $facility->owner_id;
                            $scanedVehicle->license_plate = $license_plate;
                            $scanedVehicle->save();
                            $existScanedVehicle = ScanedVehicle::where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy("created_at", "ASC")->first();
                            if ($existScanedVehicle) {
                                $data['first_scan_date'] = date("Y-m-d H:i:s", strtotime($existScanedVehicle->created_at));
                            } else {
                                $data['first_scan_date'] = date("Y-m-d H:i:s");
                            }
                        }

                        $data['citation_history'] = $citationHistory;
                        $data['warning_history'] = $warningHistory;
                        //$data['ticket'] = $ticket;
                        //$data['user'] = $user;
                        $data['warning'] = $warning;
                        $data['citation'] = $citation;
                        return $data;
                    }
                }
                $citation->is_vehicle_present = '1';
                $citation->scan_date = date("Y-m-d H:i:s");
                $citation->save();
                // $ticket['check_in_datetime'] = $citation->checkin_time;
                // $ticket['checkout_datetime'] = $citation->checkout_time;
                $citation['check_in_datetime'] = $citation->checkin_time;
                $citation['checkout_datetime'] = $citation->checkout_time;
                //$ticket['is_overstay'] = '1';
                // $ticket['is_autopay'] = '0';
                //$ticket['citation'] = $citation;
                $now = date("Y-m-d H:i:s");
                //dd($now, $citation->check_in_datetime);
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $citation->check_in_datetime);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                $diff_in_sec = $arrival_time->diffInSeconds($from);

                $rate = [];
                $is_resident = '0';
                $isMember = 0;
                //dd($arrival_time, $diff_in_hours);
                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember, $is_resident);
                /*$categoryId = self::NON_RESIDENT_ID;
              $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
              if(!$residentRate){
                  return Redirect::to('diamond-error-facility');
              }*/

                //$days = $diff_in_hours;


                /* if($diff_in_hours > 24){
              $diff_in_hours = 24;     
              }
  
              if($diff_in_hours == 0 && $diff_in_mins != 0){
                  $diff_in_hours = 1;     
              }
  
              if($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0){
                  $diff_in_hours = 1;     
              }*/

                $rate['price'] = $rate['price'];
                $processingFee = $facility->citation_processing_fee;
                $taxFee = $facility->tax_rate;

                $citation['overstay_length'] = $diff_in_hours;
                $citation['parking_amount'] = number_format($rate['price'], 2);
                $citation['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                $citation['is_overstay'] = '1';
                $citation['is_vehicle_present'] = '1';
                $citation['penalty_fee'] = $citation->penalty_fee;
                $citation['citation_processing_fee'] = number_format($processingFee, 2);
                $citation['tax_fee'] = number_format($taxFee, 2);
                $citation['user'] = $citation->user;

                //get history of citation & warning

                $citationHistory = TicketCitation::where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->get();

                $warningHistory = Warning::where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->get();

                $warning = Warning::where('license_plate', $license_plate)->where('facility_id', $facility->id)->where("is_closed", '0')->orderBy('id', 'DESC')->first();

                $data['citation_history'] = $citationHistory;
                $data['warning_history'] = $warningHistory;
                //$data['ticket'] = $ticket;
                $data['user'] = isset($ticket->user) ? $ticket->user : (isset($citation->user) ? $citation->user : null);
                $data['citation'] = $citation;
                $data['warning'] = $warning;
                return $data;
            }

            $ticket->is_vehicle_present = '1';
            $ticket->scan_date = date("Y-m-d H:i:s");
            $ticket->save();

            if ($ticket->facility_zone_id != $facilityZone->id) {
                $ticket->zone_message = "Violation - Parked in wrong zone.";
            }

            if ($ticket->is_checkout == '1') {
                $now = date("Y-m-d H:i:s");
                if (strtotime(date("Y-m-d H:i:s", strtotime($now))) > strtotime(date("Y-m-d H:i:s", strtotime($ticket->checkout_datetime)))) {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_sec = $arrival_time->diffInSeconds($from);
                    /*if($diff_in_hours > 24){
                     $diff_in_hours = 24;     
                  }*/

                    /*$isMember = 0;
                  $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
                  
                  if($rate == false){
                      return Redirect::to('autogate-error-facility');
                  }*/

                    /* $rate = [];
                  $is_resident_user = '0';
  
                  $categoryId = self::NON_RESIDENT_ID;
                  $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
                  if(!$residentRate){
                      return Redirect::to('diamond-error-facility');
                  }
                  if($diff_in_hours > 24){
                    $diff_in_hours = 24;     
                    }
        
                    if($diff_in_hours == 0 && $diff_in_mins != 0){
                        $diff_in_hours = 1;     
                    }
        
                    if($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0){
                        $diff_in_hours = 1;     
                    }
                    
                  $rate['price'] = $residentRate->price * $diff_in_hours;*/

                    if ($diff_in_hours > 24) {
                        $diff_in_hours = 24;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins != 0) {
                        $diff_in_hours = 1;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
                        $diff_in_hours = 1;
                    }

                    $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, '0');

                    $processingFee = $facility->processing_fee;
                    $taxFee = $facility->tax_rate;

                    $ticket['overstay_length'] = $diff_in_hours;
                    $ticket['parking_amount'] = number_format($rate['price'], 2);
                    $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                    $ticket['is_overstay'] = '1';
                    $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                    $ticket['processing_fee'] = number_format($processingFee, 2);
                    $ticket['tax_fee'] = number_format($taxFee, 2);

                    $data['ticket'] = $ticket;
                    $data['user'] = $ticket->user;
                    return $data;
                }

                $data['ticket'] = $ticket;
                $data['user'] = $ticket->user;
                return $data;
                //return $ticket;
            }

            if ($ticket->is_checkin == '1' && $ticket->is_checkout == '0') {
                $now = date("Y-m-d H:i:s");
                if (strtotime($now) > strtotime($ticket->checkout_datetime)) {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_sec = $arrival_time->diffInSeconds($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_sec = $arrival_time->diffInSeconds($from);
                    /*if($diff_in_hours > 24){
                     $diff_in_hours = 24;     
                  }*/

                    /*$isMember = 0;
                  $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
                  
                  if($rate == false){
                      return Redirect::to('autogate-error-facility');
                  }*/

                    $rate = [];
                    $is_resident_user = '0';

                    $categoryId = self::NON_RESIDENT_ID;
                    $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
                    if (!$residentRate) {
                        return Redirect::to('diamond-error-facility');
                    }
                    if ($diff_in_hours > 24) {
                        $diff_in_hours = 24;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins != 0) {
                        $diff_in_hours = 1;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
                        $diff_in_hours = 1;
                    }

                    $rate['price'] = $residentRate->price * $diff_in_hours;
                    $processingFee = $facility->processing_fee;
                    $taxFee = $facility->tax_rate;

                    $ticket['overstay_length'] = $diff_in_hours;
                    $ticket['parking_amount'] = number_format($rate['price'], 2);
                    $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                    $ticket['is_overstay'] = '1';
                    $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                    $ticket['citation_processing_fee'] = number_format($processingFee, 2);
                    $ticket['tax_fee'] = number_format($taxFee, 2);
                    //return $ticket;
                    $data['ticket'] = $ticket;
                    $data['user'] = $ticket->user;
                    return $data;
                }
                $data['ticket'] = $ticket;
                $data['user'] = $ticket->user;
                return $data;
            }
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }

    public function validateLicensePlateCheckinTest(Request $request, $license_plate)
    {
        $this->log->info("request " . json_encode($request->all()) . $license_plate);

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $this->setCustomTimezone($facility->id);
            $data = [];
            $permitVehicle = PermitVehicle::where('license_plate_number', $license_plate)->orderBy('id', 'DESC')->first();

            if ($permitVehicle) {

                $permitVehicleMapping = PermitVehicleMapping::where('permit_vehicle_id', $permitVehicle->id)->orderBy('id', 'DESC')->first();

                if (isset($permitVehicleMapping->permit_request_id)) {
                    // ->whereNull('cancelled_at')
                    $permitRequest = PermitRequest::with(['user', 'PermitVehicle.vehicle'])->where('facility_id', $facility->id)->where('id', $permitVehicleMapping->permit_request_id)->whereDate('desired_start_date', '<=', date("Y-m-d"))->whereDate('desired_end_date', '>=', date("Y-m-d"))->first();
                    if ($permitRequest) {

                        $citation = TicketCitation::with('user')->where('permit_request_id', $permitRequest->id)->where('facility_id', $facility->id)->where('is_checkout', '0')->where('is_closed', '0')->orderBy('id', 'DESC')->first();

                        $data['permit_request'] = $permitRequest;
                        $data['permit_request']['citation'] = null;
                        $data['permit_request']['warning'] = null;
                        if (isset($citation) && !empty($citation)) {
                            $data['permit_request']['citation'] = $citation;
                        }

                        $warning = Warning::with('user')->where('permit_request_id', $permitRequest->id)->where('facility_id', $facility->id)->where('is_closed', '0')->orderBy('id', 'DESC')->first();
                        if (isset($warning) && !empty($warning)) {
                            $data['permit_request']['warning'] = $warning;
                        }

                        $data['user'] = isset($permitRequest->user) ? $permitRequest->user : '';


                        return $data;
                    }
                }
            }
            $ticket = Ticket::with(['user', 'citation', 'overstay', 'facility'])->where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->first();
            if (!$ticket) {
                $ticket = [];
                $citation = TicketCitation::with('user')->where('license_plate', $license_plate)->where('facility_id', $facility->id)->whereDate('payment_date', '=', date("Y-m-d"))->where('is_checkout', '0')->where('is_closed', '0')->orderBy('id', 'DESC')->first();
                if (!$citation) {
                    $citation = TicketCitation::with('user')->where('license_plate', $license_plate)->where('facility_id', $facility->id)->where('is_checkout', '0')->where('is_closed', '0')/*->whereNull('anet_transaction_id')*/->orderBy('id', 'DESC')->first();
                    if (!$citation) {
                        //throw new ApiGenericException('1No vehicle plate number found.');    
                        $user = [];
                        $citationHistory = TicketCitation::with('user')->where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->get();
                        if (count($citationHistory) > 0) {
                            foreach ($citationHistory as $key => $value) {
                                if (isset($value->user)) {
                                    $data['user'] =  $value->user;
                                    break;
                                } else {
                                    $data['user'] = $value->user;
                                }
                            }
                        } else {
                            $data['user'] = null;
                        }

                        $warningHistory = Warning::where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->get();

                        $warning = Warning::where('license_plate', $license_plate)->where('facility_id', $facility->id)->where('is_closed', '0')->orderBy('id', 'DESC')->first();

                        if (!$warning) {
                            $scanedVehicle = new ScanedVehicle();
                            $scanedVehicle->facility_id = $facility->id;
                            $scanedVehicle->partner_id = $facility->owner_id;
                            $scanedVehicle->license_plate = $license_plate;
                            $scanedVehicle->save();
                            $existScanedVehicle = ScanedVehicle::where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy("created_at", "ASC")->first();
                            if ($existScanedVehicle) {

                                $data['first_scan_date'] = date("Y-m-d H:i:s");
                                if (date("Y-m-d") == date("Y-m-d", strtotime($existScanedVehicle->created_at))) {
                                    $data['first_scan_date'] = date("Y-m-d H:i:s", strtotime($existScanedVehicle->created_at));
                                }
                            } else {
                                $data['first_scan_date'] = date("Y-m-d H:i:s");
                            }
                        }

                        $data['citation_history'] = $citationHistory;
                        $data['warning_history'] = $warningHistory;
                        //$data['ticket'] = $ticket;
                        //$data['user'] = $user;
                        $data['warning'] = $warning;
                        $data['citation'] = $citation;
                        return $data;
                    }
                }
                $citation->is_vehicle_present = '1';
                $citation->scan_date = date("Y-m-d H:i:s");
                $citation->save();
                // $ticket['check_in_datetime'] = $citation->checkin_time;
                // $ticket['checkout_datetime'] = $citation->checkout_time;
                $citation['check_in_datetime'] = $citation->checkin_time;
                $citation['checkout_datetime'] = $citation->checkout_time;
                //$ticket['is_overstay'] = '1';
                // $ticket['is_autopay'] = '0';
                //$ticket['citation'] = $citation;
                $now = date("Y-m-d H:i:s");
                //dd($now, $citation->check_in_datetime);
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $citation->check_in_datetime);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                $diff_in_sec = $arrival_time->diffInSeconds($from);

                $rate = [];
                $is_resident = '0';
                $isMember = 0;
                //dd($arrival_time, $diff_in_hours);
                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember, $is_resident);
                /*$categoryId = self::NON_RESIDENT_ID;
              $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
              if(!$residentRate){
                  return Redirect::to('diamond-error-facility');
              }*/

                //$days = $diff_in_hours;


                /* if($diff_in_hours > 24){
              $diff_in_hours = 24;     
              }
  
              if($diff_in_hours == 0 && $diff_in_mins != 0){
                  $diff_in_hours = 1;     
              }
  
              if($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0){
                  $diff_in_hours = 1;     
              }*/

                $rate['price'] = $rate['price'];
                $processingFee = $facility->citation_processing_fee;
                $taxFee = $facility->tax_rate;

                $citation['overstay_length'] = $diff_in_hours;
                $citation['parking_amount'] = number_format($rate['price'], 2);
                $citation['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                $citation['is_overstay'] = '1';
                $citation['is_vehicle_present'] = '1';
                $citation['penalty_fee'] = $citation->penalty_fee;
                $citation['citation_processing_fee'] = number_format($processingFee, 2);
                $citation['tax_fee'] = number_format($taxFee, 2);
                $citation['user'] = $citation->user;

                //get history of citation & warning

                $citationHistory = TicketCitation::where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->get();

                $warningHistory = Warning::where('license_plate', $license_plate)->where('facility_id', $facility->id)->orderBy('id', 'DESC')->get();

                $warning = Warning::where('license_plate', $license_plate)->where('facility_id', $facility->id)->where("is_closed", '0')->orderBy('id', 'DESC')->first();

                $data['citation_history'] = $citationHistory;
                $data['warning_history'] = $warningHistory;
                //$data['ticket'] = $ticket;
                $data['user'] = isset($ticket->user) ? $ticket->user : (isset($citation->user) ? $citation->user : null);
                $data['citation'] = $citation;
                $data['warning'] = $warning;
                return $data;
            }
            $ticket->is_vehicle_present = '1';
            $ticket->scan_date = date("Y-m-d H:i:s");
            $ticket->save();
            if ($ticket->is_checkout == '1') {
                $now = date("Y-m-d H:i:s");
                $existTicketExtend = TicketExtend::where("ticket_id", $ticket->id)->orderBy("id", "Desc")->first();
                if ($existTicketExtend) {
                    $checkoutTime = $existTicketExtend->checkout_time;
                } else {
                    $checkoutTime = $ticket->checkout_datetime;
                }
                if (strtotime(date("Y-m-d H:i:s", strtotime($now))) > strtotime(date("Y-m-d H:i:s", strtotime($checkoutTime)))) {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkoutTime);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);

                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_hours = QueryBuilder::getLengthInHours($diff_in_mins);;
                    $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, 0);

                    $processingFee = $facility->processing_fee;
                    $taxFee = $facility->tax_rate;

                    $priceBreakUp = $ticket->unGatedPriceBreakUp($rate, $ticket->facility, $request, 0, 'detail');
                    $this->log->info("getCheckinCheckoutDetails ungated priceBreakUp " . json_encode($priceBreakUp));

                    $ticket['is_overstay'] = '1';
                    $ticket['parking_amount'] = number_format($rate['price'], 2);
                    $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                    $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                    $ticket['processing_fee'] = number_format($processingFee, 2);
                    $ticket['tax_fee'] = number_format($taxFee, 2);

                    if ($ticket->paid_by != '') {
                        if (isset($priceBreakUp['amount_paid'])) {
                            $ticket['is_overstay'] = '0';
                            $ticket['parking_amount'] = isset($priceBreakUp['amount_paid']) ? $priceBreakUp['amount_paid'] : number_format($rate['price'], 2); // ungated
                            $ticket['rate'] = "0.00";
                            $ticket['penalty_fee'] = "0.00";
                            $ticket['processing_fee'] = "0.00";
                            $ticket['tax_fee'] = "0.00";
                        }
                    }

                    //$ticket['overstay_length'] = $diff_in_hours; 
                    $ticket->checkout_time = $checkoutTime;
                    $ticket->checkout_datetime = $checkoutTime;
                    $ticket->estimated_checkout = $checkoutTime;
                    $data['ticket'] = $ticket;
                    $data['user'] = $ticket->user;
                    return $data;
                }
                $ticket->checkout_time = $checkoutTime;
                $ticket->checkout_datetime = $checkoutTime;
                $ticket->estimated_checkout = $checkoutTime;
                $data['ticket'] = $ticket;
                $data['user'] = $ticket->user;
                return $data;
                //return $ticket;
            }

            if ($ticket->is_checkin == '1' && $ticket->is_checkout == '0') {
                $now = date("Y-m-d H:i:s");
                if (strtotime($now) > strtotime($ticket->checkout_datetime)) {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_sec = $arrival_time->diffInSeconds($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_sec = $arrival_time->diffInSeconds($from);
                    /*if($diff_in_hours > 24){
                     $diff_in_hours = 24;     
                  }*/

                    /*$isMember = 0;
                  $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
                  
                  if($rate == false){
                      return Redirect::to('autogate-error-facility');
                  }*/

                    $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, 0);

                    if ($diff_in_hours > 24) {
                        $diff_in_hours = 24;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins != 0) {
                        $diff_in_hours = 1;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
                        $diff_in_hours = 1;
                    }


                    $processingFee = $facility->processing_fee;
                    $taxFee = $facility->tax_rate;

                    $priceBreakUp = $ticket->unGatedPriceBreakUp($rate, $ticket->facility, $request, 0, 'detail');
                    $this->log->info("getCheckinCheckoutDetails ungated priceBreakUp " . json_encode($priceBreakUp));

                    $ticket['is_overstay'] = '1';
                    $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                    $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                    $ticket['processing_fee'] = number_format($processingFee, 2);
                    $ticket['tax_fee'] = number_format($taxFee, 2);

                    if (isset($priceBreakUp['amount_paid'])) {
                        $ticket['is_overstay'] = '0';
                        $ticket['parking_amount'] = isset($priceBreakUp['amount_paid']) ? $priceBreakUp['amount_paid'] : number_format($rate['price'], 2); // ungated
                        $ticket['rate'] = "0.00";
                        $ticket['penalty_fee'] = "0.00";
                        $ticket['processing_fee'] = "0.00";
                        $ticket['tax_fee'] = "0.00";
                    }

                    $ticket['overstay_length'] = $diff_in_hours;

                    //return $ticket;
                    $data['ticket'] = $ticket;
                    $data['user'] = $ticket->user;
                    return $data;
                }
                $data['ticket'] = $ticket;
                $data['user'] = $ticket->user;
                return $data;
            }
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }

    public function getUserStatus(Request $request)
    {
        if ($request->driving_license != '') {
            $mystring = $request->driving_license;
            $mystring = trim($mystring); //removes any spaces from beginning of the string
            $number = substr($mystring, 1);
            $status = '';
            if (ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring) == 9) {
                $status = "Resident";
            } else {
                $status = "Non-Resident";
            }
        }
        return $status;
    }



    public function getFacilityInfractions($facility_id)
    {
        $infractionFacility = InfractionFacility::where('facility_id', $facility_id)->pluck('infraction_id');
        if (!$infractionFacility) {
            throw new ApiGenericException('Facility not found.');
        }
        $infractions = Infraction::with(['infractionReason', 'facilityInfraction'])->whereIn('id', $infractionFacility)->where("status", '1')->orderBy("id", "DESC")->get();;
        return $infractions;
    }

    public function uploadSignature(Request $request)
    {


        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('Invalid partner.');
            }
        }
        $this->validate($request, ['signature' => 'mimes:jpeg,png,jpg,svg'], ['signature.mimes' => 'Signature must be a file of type : .jpeg, .png, .jpg']);
        $user = User::find(Auth::user()->id);
        $image = $request->file('signature');
        $file_extension = $image->getClientOriginalExtension();
        $file_name =  Auth::user()->id . '_' . rand(1001, 9999) . '.' . $file_extension;
        $destination_path = storage_path("app/signature");
        if (!$image->move($destination_path, $file_name)) {
            throw new ApiGenericException("Something went wrong while upload image.");
        }
        if ($user->signature != '') {
            unlink(storage_path("app/signature/" . $user->signature));
        }
        $user->signature = $file_name;
        $user->save();
        return "Signature successfully uploaded.";
    }


    public function getSignatureUrl($signature)
    {
        if (!$signature) {
            throw new NotFoundException('Please send valid id.');
        }
        $user = User::where("signature", $signature)->first();
        if (!$user) {
            throw new NotFoundException('No signature found.');
        }
        $file = Storage::disk('local')->get("signature/" . $user->signature) ?: null;
        //$file =storage_path('app/brand-settings/'.$brandSetting->logo);    

        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/signature/' . $user->signature));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }

    public function getLicensePlateImageUrl($image)
    {
        if (!$image) {
            throw new NotFoundException('Please send valid id.');
        }
        $citation = TicketCitation::where("license_plate_image", $image)->first();
        if (!$citation) {
            throw new NotFoundException('No signature found.');
        }
        $file = Storage::disk('local')->get("citations/" . $citation->license_plate_image) ?: null;
        //$file =storage_path('app/brand-settings/'.$brandSetting->logo);    

        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/citations/' . $citation->license_plate_image));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }

    public function getWarningLicensePlateImageUrl($image)
    {
        if (!$image) {
            throw new NotFoundException('Please send valid id.');
        }
        $warning = Warning::where("license_plate_image", $image)->first();
        if (!$warning) {
            throw new NotFoundException('No image found.');
        }
        $file = Storage::disk('local')->get("warnings/" . $warning->license_plate_image) ?: null;
        //$file =storage_path('app/brand-settings/'.$brandSetting->logo);    

        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/warnings/' . $warning->license_plate_image));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }


    public function getWarningImageUrl($image)
    {
        if (!$image) {
            throw new NotFoundException('Please send valid id.');
        }
        $warning = WarningImage::where("image_name", $image)->first();
        if (!$warning) {
            throw new NotFoundException('No image found.');
        }
        $file = Storage::disk('local')->get("citations/" . $warning->image_name) ?: null;
        //$file =storage_path('app/brand-settings/'.$brandSetting->logo);    

        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/citations/' . $warning->image_name));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }

    public function getCitationList(Request $request)
    {

        if ((Auth::user()->user_type == '12')) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
        } else if ((Auth::user()->user_type == '4')) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else if (Auth::user()->user_type == '3') {
            $partner_id = Auth::user()->id;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        } else {
            $partner_id = $request->partner_id;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        }
        $ticket = TicketCitation::with(['user', 'transaction', 'ticketCitationInfraction.ticketCitationInfractionReason', 'ticketCitationInfractionOther', 'ticketCitationImage', 'facility', 'warnings', 'permit', 'voidCodes']);
        if ($partner_id) {
            $ticket = $ticket->where('partner_id', $partner_id);
        }

        if ($rm_id) {
            $ticket = $ticket->where('rm_id', $rm_id);
        }

        if (isset($request->is_admin_citation)) {
            $ticket = $ticket->where('is_admin_citation', $request->is_admin_citation);
        }

        if ($request->search) {
            if (strtolower($request->search) == 'open') {
                $ticket = $ticket->where('is_closed', 0);
            } else if (strtolower($request->search) == 'closed') {
                $ticket = $ticket->where('is_closed', 1);
            } else {
                $ticket = $ticket->where('citation_number', 'like', "%$request->search%")->orWhere('license_plate', 'like', "%$request->search%")->orWhere('infraction_name', 'like', "%$request->search%")->orWhere('reason', 'like', "%$request->search%")->orWhere('penalty_fee', 'like', "%$request->search%");
            }
        }
        // $ticket = $ticket->where('partner_id',$partner_id);
        if ($request->facility_id) {
            $ticket = $ticket->Where('facility_id', $request->facility_id);
        }
        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date('Y-m-d', strtotime($request->from_date));
            $to_date = date('Y-m-d', strtotime($request->to_date));
        }
        $ticket = $ticket->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);

        if ($request->sort != '') {

            $ticket = $ticket->orderBy($request->sort, $request->sortBy);
        } else {
            $ticket = $ticket->orderBy("id", "DESC");
        }
        if ($request->download_type == '1') {
            $ticket = $ticket->get();
            foreach ($ticket as $key => $val) {
                $citation = $val->ticketCitationInfraction->toArray();
                $infractionNameArray = array_column($citation, 'infraction_name');
                $inflationName = implode(',', $infractionNameArray);
                $val->all_infraction_name = $inflationName;
            }

            if ($request->sort == 'infraction_name') {
                if (count($ticket) > 0) {
                    if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                        for ($i = 0; $i < count($ticket['data']); $i++) {
                            for ($j = $i + 1; $j < count($ticket['data']); $j++) {
                                str_replace(' ', '-', $ticket['data'][$i]['all_infraction_name']);
                                str_replace(' ', '-', $ticket['data'][$j]['all_infraction_name']);
                                if (strtoupper($ticket['data'][$i]['all_infraction_name']) > strtoupper($ticket['data'][$j]['all_infraction_name'])) {
                                    $temp = $ticket['data'][$i];
                                    $ticket['data'][$i] = $ticket['data'][$j];
                                    $ticket['data'][$j] = $temp;
                                }
                            }
                        }
                    } else {
                        for ($i = 0; $i < count($ticket['data']); $i++) {
                            for ($j = $i + 1; $j < count($ticket['data']); $j++) {
                                str_replace('-', '', $ticket['data'][$i]['all_infraction_name']);
                                str_replace('-', '', $ticket['data'][$j]['all_infraction_name']);

                                str_replace('_', '', $ticket['data'][$i]['all_infraction_name']);
                                str_replace('_', '', $ticket['data'][$j]['all_infraction_name']);
                                if (strtoupper($ticket['data'][$i]['all_infraction_name']) < strtoupper($ticket['data'][$j]['all_infraction_name'])) {
                                    $temp = $ticket['data'][$i];
                                    $ticket['data'][$i] = $ticket['data'][$j];
                                    $ticket['data'][$j] = $temp;
                                }
                            }
                        }
                    }
                }
            }

            $this->downloadCitation($ticket);
        }
        $ticket = $ticket->paginate(20);

        $ticket = $ticket->toArray();
        foreach ($ticket['data'] as $key => $val) {
            $infractionNameArray = array_column($val['ticket_citation_infraction'], 'infraction_name');
            $inflationName = implode(',', $infractionNameArray);
            $ticket['data'][$key]['all_infraction_name'] = $inflationName;
        }

        if ($request->sort == 'infraction_name') {
            if (count($ticket) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($ticket['data']); $i++) {
                        for ($j = $i + 1; $j < count($ticket['data']); $j++) {
                            str_replace('-', '', $ticket['data'][$i]['all_infraction_name']);
                            str_replace('-', '', $ticket['data'][$j]['all_infraction_name']);
                            str_replace('_', '', $ticket['data'][$i]['all_infraction_name']);
                            str_replace('_', '', $ticket['data'][$j]['all_infraction_name']);
                            if (strtoupper($ticket['data'][$i]['all_infraction_name']) > strtoupper($ticket['data'][$j]['all_infraction_name'])) {
                                $temp = $ticket['data'][$i];
                                $ticket['data'][$i] = $ticket['data'][$j];
                                $ticket['data'][$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($ticket['data']); $i++) {
                        for ($j = $i + 1; $j < count($ticket['data']); $j++) {
                            str_replace('-', '', $ticket['data'][$i]['all_infraction_name']);
                            str_replace('-', '', $ticket['data'][$j]['all_infraction_name']);
                            str_replace('_', '', $ticket['data'][$i]['all_infraction_name']);
                            str_replace('_', '', $ticket['data'][$j]['all_infraction_name']);
                            if (strtoupper($ticket['data'][$i]['all_infraction_name']) < strtoupper($ticket['data'][$j]['all_infraction_name'])) {
                                $temp = $ticket['data'][$i];
                                $ticket['data'][$i] = $ticket['data'][$j];
                                $ticket['data'][$j] = $temp;
                            }
                        }
                    }
                }
            }
        }

        return $ticket;
    }

    public function allWarningList(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }
            $this->setCustomTimezone($facility->id);
            /*$today = date("Y-m-d");
            $yesterday = date("Y-m-d",strtotime("-1 days"));*/
            $data['open_warning'] = Warning::/*whereDate('checkin_time', '=', $today)->*/with('user')->where('is_closed', '0')->where('facility_id', $facility->id)->orderBy("id", "DESC")->get();
            $data['closed_warning'] = Warning::/*whereDate('checkin_time', '=', $yesterday)->*/with('user')->where('is_closed', '1')->where('facility_id', $facility->id)->orderBy("id", "DESC")->get();
            return $data;
        }
    }


    public function getWarningList(Request $request)
    {

        if ((Auth::user()->user_type == '12')) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
        } else if ((Auth::user()->user_type == '4')) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else if (Auth::user()->user_type == '3') {
            $partner_id = Auth::user()->id;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        } else {
            $partner_id = $request->partner_id;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        }
        //joined warningInfraction in query
        $warning = Warning::with(['user', 'facility', 'citation', 'warningImage', 'warningInfraction.warningInfractionReason', 'permit']);

        if ($request->search) {
            //$warning= $warning->Where('warning_number', 'like', "%$request->search%")->orWhere('license_plate', 'like', "%$request->search%")->orWhere('infraction_name', 'like', "%$request->search%")->orWhere('reason', 'like', "%$request->search%");

            if (strtolower($request->search) == 'open') {
                $warning = $warning->where('is_closed', 0);
            } else if (strtolower($request->search) == 'closed') {
                $warning = $warning->where('is_closed', 1);
            } else {
                $warning = $warning->Where('warning_number', 'like', "%$request->search%")->orWhere('license_plate', 'like', "%$request->search%")->orWhere('infraction_name', 'like', "%$request->search%")->orWhere('reason', 'like', "%$request->search%");
            }
        }
        $warning = $warning->where('partner_id', $partner_id);
        if ($request->facility_id) {
            $warning = $warning->Where('facility_id', $request->facility_id);
        }
        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date('Y-m-d', strtotime($request->from_date));
            $to_date = date('Y-m-d', strtotime($request->to_date));
        }
        $warning = $warning->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
        if ($request->sort != '') {

            $warning = $warning->orderBy($request->sort, $request->sortBy);
        } else {
            $warning = $warning->orderBy("id", "DESC");
        }
        if ($request->download_type == '1') {
            $warning = $warning->get();

            foreach ($warning as $key => $val) {
                $warning_name = $val->warningInfraction->toArray();
                $infractionNameArray = array_column($warning_name, 'infraction_name');
                $inflationName = implode(',', $infractionNameArray);
                $val->all_infraction_name = $inflationName;
            }
            if ($request->sort == 'infraction_name') {
                if (count($warning['data']) > 0) {
                    if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                        for ($i = 0; $i < count($warning['data']); $i++) {
                            for ($j = $i + 1; $j < count($warning['data']); $j++) {
                                str_replace(' ', '-', $warning['data'][$i]['all_infraction_name']);
                                str_replace(' ', '-', $warning['data'][$j]['all_infraction_name']);
                                if (strtoupper($warning['data'][$i]['all_infraction_name']) > strtoupper($warning['data'][$j]['all_infraction_name'])) {
                                    $temp = $warning['data'][$i];
                                    $warning['data'][$i] = $warning['data'][$j];
                                    $warning['data'][$j] = $temp;
                                }
                            }
                        }
                    } else {
                        for ($i = 0; $i < count($warning['data']); $i++) {
                            for ($j = $i + 1; $j < count($warning['data']); $j++) {
                                str_replace(' ', '-', $warning['data'][$i]['all_infraction_name']);
                                str_replace(' ', '-', $warning['data'][$j]['all_infraction_name']);
                                if (strtoupper($warning['data'][$i]['all_infraction_name']) < strtoupper($warning['data'][$j]['all_infraction_name'])) {
                                    $temp = $warning['data'][$i];
                                    $warning['data'][$i] = $warning['data'][$j];
                                    $warning['data'][$j] = $temp;
                                }
                            }
                        }
                    }
                }
            }
            $this->downloadWarning($warning);
        }

        $warning = $warning->paginate(20);

        // added the all all_infraction_name by joining warning_infraction 
        $warning = $warning->toArray();
        foreach ($warning['data'] as $key => $val) {
            $infractionNameArray = array_column($val['warning_infraction'], 'infraction_name');
            $inflationName = implode(',', $infractionNameArray);
            $warning['data'][$key]['all_infraction_name'] = $inflationName;
        }

        if ($request->sort == 'infraction_name') {
            if (count($warning['data']) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($warning['data']); $i++) {
                        for ($j = $i + 1; $j < count($warning['data']); $j++) {
                            str_replace(' ', '-', $warning['data'][$i]['all_infraction_name']);
                            str_replace(' ', '-', $warning['data'][$j]['all_infraction_name']);
                            if (strtoupper($warning['data'][$i]['all_infraction_name']) > strtoupper($warning['data'][$j]['all_infraction_name'])) {
                                $temp = $warning['data'][$i];
                                $warning['data'][$i] = $warning['data'][$j];
                                $warning['data'][$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($warning['data']); $i++) {
                        for ($j = $i + 1; $j < count($warning['data']); $j++) {
                            str_replace(' ', '-', $warning['data'][$i]['all_infraction_name']);
                            str_replace(' ', '-', $warning['data'][$j]['all_infraction_name']);
                            if (strtoupper($warning['data'][$i]['all_infraction_name']) < strtoupper($warning['data'][$j]['all_infraction_name'])) {
                                $temp = $warning['data'][$i];
                                $warning['data'][$i] = $warning['data'][$j];
                                $warning['data'][$j] = $temp;
                            }
                        }
                    }
                }
            }
        }

        return $warning;
    }

    public function getCitationImageUrl($id)
    {

        if (!$id) {
            throw new NotFoundException('Please send valid id.');
        }
        $citation = TicketCitationImage::where("id", $id)->first();
        if (!$citation) {
            throw new NotFoundException('No signature found.');
        }
        $file = Storage::disk('local')->get("citations/" . $citation->image_name) ?: null;
        //$file =storage_path('app/brand-settings/'.$brandSetting->logo);    

        // create response and add encoded image data
        $response = Response::make($file);
        // getting content type e.g image/jpeg
        $file_extension = mime_content_type(storage_path('app/citations/' . $citation->image_name));
        // set content-type
        $response->header('Content-Type', $file_extension);
        // output
        return $response;
    }

    public function downloadCitation($data)
    {

        $excelSheetName = ucwords(str_replace(' ', '', 'CitationListExcel'));
        $finalCodes1 = [];
        $increment1 = 1;
        $status = '0';
        $payment_status = '0';
        if (count($data) > 0) {
            foreach ($data as $val) {
                if ($val->is_avoid == "1") {
                    $status = 'Void';
                } else if ($val->paid_date != '') {
                    $status = 'Offline';
                } else if ($val->is_closed == '1') {
                    $status = 'Closed';
                } else if ($val->is_reopen == '1') {
                    $status = 'Re-open';
                } else {
                    $status = 'Open';
                }

                if ($val->is_avoid == "1") {
                    $payment_status = '-';
                } else if ($val->paid_date != "" && $val->amount > 0) {
                    $payment_status = '-';
                } else if ($val->payment_status == "1") {
                    $payment_status = 'Success';
                } else if ($val->payment_status == "0") {
                    $payment_status = 'Pending';
                }

                $reason_data = '';
                $reasons = DB::table('ticket_citation_infraction_reasons')->where('ticket_citation_id', $val->id)->pluck('reason');
                if (count($reasons) > 0) {
                    $reason_data = implode(",", $reasons);
                }

                $finalCodes1[] = [
                    'No.' => $increment1,
                    'Infraction Name' => isset($val->all_infraction_name) ? strip_tags($val->all_infraction_name) : '-',
                    'Penalty Fee' => isset($val->penalty_fee) ? strip_tags($val->penalty_fee) : '-',
                    'Citation Number' => isset($val->citation_number) ? strip_tags($val->citation_number) : '-',
                    'License Plate' => isset($val->license_plate) ? strip_tags($val->license_plate) : '-',
                    'Payment Status' => $payment_status,
                    'Status' => $status,
                    'Reason' => $reason_data,
                    'Amount' => isset($val->amount) ? $val->amount : '0.00',
                    'Amount Comment' => isset($val->amount_comment) ? $val->amount_comment : '-',
                    'Paid Date' => isset($val->paid_date) ? date('Y-m-d', strtotime($val->paid_date)) : '-',

                ];
                $increment1++;
            }
        } else {
            throw new ApiGenericException("No Record Found");
        }

        Excel::create(
            $excelSheetName,
            function ($excel) use ($finalCodes1, $excelSheetName) {

                // Set the spreadsheet title, creator, and description
                $excel->setTitle($excelSheetName);
                $excel->setCreator('citationList')->setCompany('ParkEngage');
                $excel->setDescription('List Of Citation List');

                // Build the spreadsheet, passing in the payments array
                if (empty($finalCodes1)) {
                    throw new ApiGenericException('Sorry! No Data Found.');
                } else {
                    if (isset($finalCodes1) && !empty($finalCodes1)) {
                        $excel->sheet(
                            'Citation Listing',
                            function ($sheet) use ($finalCodes1) {
                                $sheet->fromArray($finalCodes1, null, 'A1', false, true);
                            }
                        );
                    }
                }
            }
        )->store('xls')->download('xls');
    }

    public function downloadWarning($data)
    {
        $excelSheetName = ucwords(str_replace(' ', '', 'WarningListExcel'));
        $finalCodes1 = [];
        $increment1 = 1;
        $status = '0';
        if (count($data) > 0) {
            foreach ($data as $val) {
                if ($val->is_avoid == '1') {
                    $status = 'Void';
                } else if ($val->is_closed == '1') {
                    $status = 'Closed';
                } else if ($val->is_reopen == '1') {
                    $status = 'Re-open';
                } else {
                    $status = 'Open';
                }
                $finalCodes1[] = [
                    'No.' => $increment1,
                    'Infraction Name' => isset($val->all_infraction_name) ? strip_tags($val->all_infraction_name) : '-',
                    'Facility' => isset($val->facility->full_name) ? strip_tags($val->facility->full_name) : '-',
                    'Warning Number' => isset($val->warning_number) ? strip_tags($val->warning_number) : '-',
                    'License Plate' => isset($val->license_plate) ? strip_tags($val->license_plate) : '-',
                    'Status' => $status,
                    'Created date' => isset($val->created_at) ? date("m/d/Y", strtotime($val->created_at)) : '-',
                    'Close date' => isset($val->closed_date) ? date("m/d/Y", strtotime($val->closed_date)) : '-',
                ];
                $increment1++;
            }
        } else {
            throw new ApiGenericException("No Record Found");
        }

        Excel::create(
            $excelSheetName,
            function ($excel) use ($finalCodes1, $excelSheetName) {

                // Set the spreadsheet title, creator, and description
                $excel->setTitle($excelSheetName);
                $excel->setCreator('WarningList')->setCompany('ParkEngage');
                $excel->setDescription('List Of Warning List');

                // Build the spreadsheet, passing in the payments array
                if (empty($finalCodes1)) {
                    throw new ApiGenericException('Sorry! No Data Found.');
                } else {
                    if (isset($finalCodes1) && !empty($finalCodes1)) {
                        $excel->sheet(
                            'Warning Listing',
                            function ($sheet) use ($finalCodes1) {
                                $sheet->fromArray($finalCodes1, null, 'A1', false, true);
                            }
                        );
                    }
                }
            }
        )->store('xls')->download('xls');
    }

    public function validateLicensePlateCheckinNew(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $neighborhoodIds = ($facility && $facility->neighborhood_id > 1)
                ? Facility::where('neighborhood_id', $facility->neighborhood_id)->pluck('id')->toArray()
                : [$facility->id];

            $this->setCustomTimezone($facility->id);
            $data = [];

            //close warning before any thing
            if ($secret->partner_id == 358642 || $secret->partner_id == 2980) {
                //stop close warning for woodman or breeze partner
            } else {
                Warning::whereIn('facility_id', $neighborhoodIds)->whereDate("created_at", "<", date("Y-m-d"))->update(['is_closed' => '1']);
            }


            $license_plate = str_replace(array('\'', '"', ',', ';', '<', '>', '&', '*', '-', '!', '@', '_', '+', '^', '#', '₹', '$'), '', $request->license_plate);
            $request->request->add(['license_plate' => $license_plate]);
            $data['license_plate'] = $license_plate;
            //check if vehicle whitelist in DB
            $whitelistVehicle = BlackListedVehicle::where("license_plate_number", $license_plate)->where("plate_type", "3")->where("status", "1")->where("partner_id", $facility->owner_id)->orderBy("id", "DESC")->first();
            if ($whitelistVehicle) {
                $data['is_whitelist'] = "1";
                $data['whiteListMessage'] = "This License plate is in our White List, you can't create Citation/Warning against this license plate.";
                return $data;
            }
            $permitVehicle = PermitVehicle::where('license_plate_number', $request->license_plate)->where('partner_id', $facility->owner_id)->orderBy('id', 'DESC')->first();

            if ($permitVehicle) {

                $neighborhoodIds = ($facility && $facility->neighborhood_id > 1)
                ? Facility::where('neighborhood_id', $facility->neighborhood_id)->pluck('id')->toArray()
                : [$facility->id];

                $permitVehicleMapping = PermitVehicleMapping::where('permit_vehicle_id', $permitVehicle->id)->orderBy('id', 'DESC')->get();
                if (count($permitVehicleMapping) > 1) {
                    $totalCount = count($permitVehicleMapping);
                    foreach ($permitVehicleMapping as $key => $value) {
                        // ->whereNull('cancelled_at')
                        $permitRequest = PermitRequest::with(['user', 'PermitVehicle.vehicle', 'Rate'])->whereIn('facility_id', $neighborhoodIds)->where('id', $value->permit_request_id)->whereDate('desired_start_date', '<=', date("Y-m-d"))->whereDate('desired_end_date', '>=', date("Y-m-d"))->first();
                        if ($permitRequest) {

                            $alreadyScanedVehicle = ScanedVehicle::where('license_plate', $request->license_plate)->whereDate("created_at", '=', date("Y-m-d"))->where("permit_request_id", $permitRequest->id)->first();

                            if ($alreadyScanedVehicle) {
                                $alreadyScanedVehicle = ScanedVehicle::where('license_plate', '!=', $request->license_plate)->whereDate("created_at", '=', date("Y-m-d"))->where("permit_request_id", $permitRequest->id)->get();
                                if ($alreadyScanedVehicle) {

                                    if ($permitRequest->permit_rate_id != '') {
                                        //$search = new PermitRate(date("Y-m-d H:i:s"), $permitRequest);
                                        PermitRate::validatePermitRate($permitRequest, date("Y-m-d H:i:s"));
                                        if ($permitRequest->permit_valid_day_status == false) {
                                            $permitRequest->wrong_time_message = "Permit is not active on this day.";
                                        }
                                        if ($permitRequest->permit_valid_entry_exit_status == false) {
                                            $explode = explode(":", $permitRequest->Rate->entry_time_begin);
                                            $startTime = '';
                                            if ($explode[0] <= 24) {
                                                $startTime = date('h:i A', strtotime($permitRequest->Rate->entry_time_begin));
                                            } else {
                                                $remainderHour = $explode[0] - 24;
                                                if ($remainderHour >= 12) {
                                                    $startTime = $remainderHour . ':00 PM';
                                                } else {
                                                    $startTime = $remainderHour . ':00 AM';
                                                }
                                            }

                                            $explode = explode(":", $permitRequest->Rate->entry_time_end);
                                            $endTime = '';
                                            if ($explode[0] <= 24) {
                                                $endTime = date('h:i A', strtotime($permitRequest->Rate->entry_time_end));
                                            } else {
                                                $remainderHour = $explode[0] - 24;
                                                if ($remainderHour >= 12) {
                                                    $endTime = $remainderHour . ':00 PM';
                                                } else {
                                                    $endTime = $remainderHour . ':00 AM';
                                                }
                                            }

                                            $permitRequest->wrong_time_message = "Unauthorized entry (allowed time " . $startTime . "-" . $endTime . ")";
                                        }
                                    }
                                    $scanedVehicle = new ScanedVehicle();
                                    $scanedVehicle->facility_id = $facility->id;
                                    $scanedVehicle->partner_id = $facility->owner_id;
                                    $scanedVehicle->license_plate = $request->license_plate;
                                    $scanedVehicle->permit_request_id = $permitRequest->id;
                                    $scanedVehicle->longitude = $request->longitude;
                                    $scanedVehicle->latitude = $request->latitude;
                                    $scanedVehicle->device_name = $request->device_name;
                                    $scanedVehicle->username = ucwords(Auth::user()->name);
                                    $scanedVehicle->lot_id = $request->lot_id;
                                    $scanedVehicle->save();


                                    $data['permit_request'] = $permitRequest;
                                    $data['permit_request']['citation'] = null;
                                    $data['permit_request']['warning'] = null;

                                    $licensePlateArray = [];
                                    foreach ($alreadyScanedVehicle as $k => $v) {
                                        if (in_array($v->license_plate, $licensePlateArray)) {
                                            continue;
                                        } else {
                                            $licensePlateArray[] = $v->license_plate;
                                        }
                                    }
                                    $licensePlateString = implode(", ", array_unique($licensePlateArray));
                                    if (strlen($licensePlateString) > 0) {
                                        $data['permit_request']['scaned_message'] = "License No- " . $licensePlateString . " already scanned today against this permit";
                                    }

                                    $citation = TicketCitation::with('user')->where('permit_request_id', $permitRequest->id)->where('license_plate', $request->license_plate)->where('facility_id', $facility->id)->where('is_checkout', '0')->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                                    if (isset($citation) && !empty($citation)) {
                                        $data['permit_request']['citation'] = $citation;
                                    }

                                    $warning = Warning::with('user')->where('permit_request_id', $permitRequest->id)->where('facility_id', $facility->id)->where('license_plate', $request->license_plate)->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                                    if (isset($warning) && !empty($warning)) {
                                        $data['permit_request']['warning'] = $warning;
                                    }

                                    $data['user'] = isset($permitRequest->user) ? $permitRequest->user : '';
                                    return $data;
                                }
                            } else {
                                $alreadyScanedVehicle = ScanedVehicle::whereDate("created_at", '=', date("Y-m-d"))->where("permit_request_id", $permitRequest->id)->get();
                                if (count($alreadyScanedVehicle) <= 0) {

                                    if ($permitRequest->permit_rate_id != '') {
                                        //$search = new PermitRate(date("Y-m-d H:i:s"), $permitRequest);
                                        PermitRate::validatePermitRate($permitRequest, date("Y-m-d H:i:s"));
                                        if ($permitRequest->permit_valid_day_status == false) {
                                            if ($key == ($totalCount - 1)) {
                                            } else {
                                                continue;
                                            }
                                            $permitRequest->wrong_time_message = "Permit is not active on this day.";
                                        }
                                        if ($permitRequest->permit_valid_entry_exit_status == false) {
                                            if ($key == ($totalCount - 1)) {
                                            } else {
                                                continue;
                                            }
                                            $explode = explode(":", $permitRequest->Rate->entry_time_begin);
                                            $startTime = '';
                                            if ($explode[0] <= 24) {
                                                $startTime = date('h:i A', strtotime($permitRequest->Rate->entry_time_begin));
                                            } else {
                                                $remainderHour = $explode[0] - 24;
                                                if ($remainderHour >= 12) {
                                                    $startTime = $remainderHour . ':00 PM';
                                                } else {
                                                    $startTime = $remainderHour . ':00 AM';
                                                }
                                            }

                                            $explode = explode(":", $permitRequest->Rate->entry_time_end);
                                            $endTime = '';
                                            if ($explode[0] <= 24) {
                                                $endTime = date('h:i A', strtotime($permitRequest->Rate->entry_time_end));
                                            } else {
                                                $remainderHour = $explode[0] - 24;
                                                if ($remainderHour >= 12) {
                                                    $endTime = $remainderHour . ':00 PM';
                                                } else {
                                                    $endTime = $remainderHour . ':00 AM';
                                                }
                                            }

                                            $permitRequest->wrong_time_message = "Unauthorized entry (allowed time " . $startTime . "-" . $endTime . ")";
                                        }
                                    }

                                    $scanedVehicle = new ScanedVehicle();
                                    $scanedVehicle->facility_id = $facility->id;
                                    $scanedVehicle->partner_id = $facility->owner_id;
                                    $scanedVehicle->license_plate = $request->license_plate;
                                    $scanedVehicle->permit_request_id = $permitRequest->id;
                                    $scanedVehicle->longitude = $request->longitude;
                                    $scanedVehicle->latitude = $request->latitude;
                                    $scanedVehicle->device_name = $request->device_name;
                                    $scanedVehicle->username = ucwords(Auth::user()->name);
                                    $scanedVehicle->lot_id = $request->lot_id;
                                    $scanedVehicle->save();

                                    $data['permit_request'] = $permitRequest;
                                    $data['permit_request']['citation'] = null;
                                    $data['permit_request']['warning'] = null;

                                    $citation = TicketCitation::with('user')->where('permit_request_id', $permitRequest->id)->where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_checkout', '0')->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                                    if (isset($citation) && !empty($citation)) {
                                        $data['permit_request']['citation'] = $citation;
                                    }

                                    $warning = Warning::with('user')->where('permit_request_id', $permitRequest->id)->whereIn('facility_id', $neighborhoodIds)->where('license_plate', $request->license_plate)->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                                    if (isset($warning) && !empty($warning)) {
                                        $data['permit_request']['warning'] = $warning;
                                    }

                                    $data['user'] = isset($permitRequest->user) ? $permitRequest->user : '';
                                    return $data;
                                } else {
                                    if ($key == ($totalCount - 1)) {
                                        if ($permitRequest->permit_rate_id != '') {
                                            //$search = new PermitRate(date("Y-m-d H:i:s"), $permitRequest);
                                            PermitRate::validatePermitRate($permitRequest, date("Y-m-d H:i:s"));
                                            if ($permitRequest->permit_valid_day_status == false) {
                                                $permitRequest->wrong_time_message = "Permit is not active on this day.";
                                            }
                                            if ($permitRequest->permit_valid_entry_exit_status == false) {
                                                $explode = explode(":", $permitRequest->Rate->entry_time_begin);
                                                $startTime = '';
                                                if ($explode[0] <= 24) {
                                                    $startTime = date('h:i A', strtotime($permitRequest->Rate->entry_time_begin));
                                                } else {
                                                    $remainderHour = $explode[0] - 24;
                                                    if ($remainderHour >= 12) {
                                                        $startTime = $remainderHour . ':00 PM';
                                                    } else {
                                                        $startTime = $remainderHour . ':00 AM';
                                                    }
                                                }

                                                $explode = explode(":", $permitRequest->Rate->entry_time_end);
                                                $endTime = '';
                                                if ($explode[0] <= 24) {
                                                    $endTime = date('h:i A', strtotime($permitRequest->Rate->entry_time_end));
                                                } else {
                                                    $remainderHour = $explode[0] - 24;
                                                    if ($remainderHour >= 12) {
                                                        $endTime = $remainderHour . ':00 PM';
                                                    } else {
                                                        $endTime = $remainderHour . ':00 AM';
                                                    }
                                                }

                                                $permitRequest->wrong_time_message = "Unauthorized entry (allowed time " . $startTime . "-" . $endTime . ")";
                                            }
                                        }



                                        $data['permit_request'] = $permitRequest;
                                        $data['permit_request']['citation'] = null;
                                        $data['permit_request']['warning'] = null;

                                        $citation = TicketCitation::with('user')->where('permit_request_id', $permitRequest->id)->where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_checkout', '0')->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                                        if (isset($citation) && !empty($citation)) {
                                            $data['permit_request']['citation'] = $citation;
                                        }

                                        $warning = Warning::with('user')->where('permit_request_id', $permitRequest->id)->whereIn('facility_id', $neighborhoodIds)->where('license_plate', $request->license_plate)->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                                        if (isset($warning) && !empty($warning)) {
                                            $data['permit_request']['warning'] = $warning;
                                        }

                                        $data['user'] = isset($permitRequest->user) ? $permitRequest->user : '';
                                        $alreadyScanedVehicle = ScanedVehicle::where('license_plate', '!=', $request->license_plate)->whereDate("created_at", '=', date("Y-m-d"))->where("permit_request_id", $permitRequest->id)->get();
                                        $licensePlateArray = [];
                                        foreach ($alreadyScanedVehicle as $k => $v) {
                                            if (in_array($v->license_plate, $licensePlateArray)) {
                                                continue;
                                            } else {
                                                $licensePlateArray[] = $v->license_plate;
                                            }
                                        }
                                        $licensePlateString = implode(", ", array_unique($licensePlateArray));

                                        if (strlen($licensePlateString) > 0) {
                                            $data['permit_request']['scaned_message'] = "License No- " . $licensePlateString . " already scanned today against this permit";
                                        }

                                        $scanedVehicle = new ScanedVehicle();
                                        $scanedVehicle->facility_id = $facility->id;
                                        $scanedVehicle->partner_id = $facility->owner_id;
                                        $scanedVehicle->license_plate = $request->license_plate;
                                        $scanedVehicle->permit_request_id = $permitRequest->id;
                                        $scanedVehicle->longitude = $request->longitude;
                                        $scanedVehicle->latitude = $request->latitude;
                                        $scanedVehicle->device_name = $request->device_name;
                                        $scanedVehicle->username = ucwords(Auth::user()->name);
                                        $scanedVehicle->lot_id = $request->lot_id;
                                        $scanedVehicle->save();

                                        return $data;
                                    } else {
                                        continue;
                                    }
                                }
                            }
                        }
                    }
                } else {
                    if (isset($permitVehicleMapping[0]->permit_request_id)) {
                        // ->whereNull('cancelled_at')
                        $permitRequest = PermitRequest::with(['user', 'PermitVehicle.vehicle', 'Rate'])->whereIn('facility_id', $neighborhoodIds)->where('id', $permitVehicleMapping[0]->permit_request_id)->whereDate('desired_start_date', '<=', date("Y-m-d"))->whereDate('desired_end_date', '>=', date("Y-m-d"))->first();
                        if ($permitRequest) {

                            if ($permitRequest->permit_rate_id != '') {
                                //$search = new PermitRate(date("Y-m-d H:i:s"), $permitRequest);
                                PermitRate::validatePermitRate($permitRequest, date("Y-m-d H:i:s"));
                                if ($permitRequest->permit_valid_day_status == false) {
                                    $permitRequest->wrong_time_message = "Permit is not active on this day.";
                                }
                                if ($permitRequest->permit_valid_entry_exit_status == false) {

                                    $explode = explode(":", $permitRequest->Rate->entry_time_begin);
                                    $startTime = '';
                                    if ($explode[0] <= 24) {
                                        $startTime = date('h:i A', strtotime($permitRequest->Rate->entry_time_begin));
                                    } else {
                                        $remainderHour = $explode[0] - 24;
                                        if ($remainderHour >= 12) {
                                            $startTime = $remainderHour . ':00 PM';
                                        } else {
                                            $startTime = $remainderHour . ':00 AM';
                                        }
                                    }

                                    $explode = explode(":", $permitRequest->Rate->entry_time_end);
                                    $endTime = '';
                                    if ($explode[0] <= 24) {
                                        $endTime = date('h:i A', strtotime($permitRequest->Rate->entry_time_end));
                                    } else {
                                        $remainderHour = $explode[0] - 24;
                                        if ($remainderHour >= 12) {
                                            $endTime = $remainderHour . ':00 PM';
                                        } else {
                                            $endTime = $remainderHour . ':00 AM';
                                        }
                                    }

                                    $permitRequest->wrong_time_message = "Unauthorized entry (allowed time " . $startTime . "-" . $endTime . ")";
                                }
                            }

                            $citation = TicketCitation::with('user')->where('permit_request_id', $permitRequest->id)->where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_checkout', '0')->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();

                            $data['permit_request'] = $permitRequest;
                            $data['permit_request']['citation'] = null;
                            $data['permit_request']['warning'] = null;
                            if (isset($citation) && !empty($citation)) {
                                $data['permit_request']['citation'] = $citation;
                            }

                            $warning = Warning::with('user')->where('permit_request_id', $permitRequest->id)->whereIn('facility_id', $neighborhoodIds)->where('license_plate', $request->license_plate)->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                            if (isset($warning) && !empty($warning)) {
                                $data['permit_request']['warning'] = $warning;
                            }

                            $data['user'] = isset($permitRequest->user) ? $permitRequest->user : '';
                            $alreadyScanedVehicle = ScanedVehicle::where("permit_request_id", $permitRequest->id)->where('license_plate', '!=', $request->license_plate)->whereDate('created_at', '=', date("Y-m-d"))->get();

                            if (count($alreadyScanedVehicle) > 0) {
                                $licensePlateArray = [];
                                foreach ($alreadyScanedVehicle as $key => $value) {
                                    if (in_array($value->license_plate, $licensePlateArray)) {
                                        continue;
                                    } else {
                                        $licensePlateArray[] = $value->license_plate;
                                    }
                                }
                                $licensePlateString = implode(", ", $licensePlateArray);
                                $data['permit_request']['scaned_message'] = "License No- " . $licensePlateString . " already scanned today against this permit";
                            } else {
                                $data['permit_request']['scaned_message'] = null;
                            }
                            $scanedVehicle = new ScanedVehicle();
                            $scanedVehicle->facility_id = $facility->id;
                            $scanedVehicle->partner_id = $facility->owner_id;
                            $scanedVehicle->license_plate = $request->license_plate;
                            $scanedVehicle->permit_request_id = $permitRequest->id;
                            $scanedVehicle->longitude = $request->longitude;
                            $scanedVehicle->latitude = $request->latitude;
                            $scanedVehicle->device_name = $request->device_name;
                            $scanedVehicle->username = ucwords(Auth::user()->name);
                            $scanedVehicle->lot_id = $request->lot_id;
                            $scanedVehicle->save();

                            return $data;
                        }
                    }
                }
            }

            $reservation = Reservation::with(['user', 'ticket'])->where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->whereDate("start_timestamp", "=", date("Y-m-d"))->whereNull("cancelled_at")->orderBy("id", "desc")->first();
            if ($reservation) {
                $data['reservation'] = $reservation;
                $data['user'] = $reservation->user;
                $data['checkin'] = $reservation->ticket;

                $scanedVehicle = new ScanedVehicle();
                $scanedVehicle->facility_id = $facility->id;
                $scanedVehicle->partner_id = $facility->owner_id;
                $scanedVehicle->license_plate = $request->license_plate;
                $scanedVehicle->reservation_id = $reservation->id;
                $scanedVehicle->longitude = $request->longitude;
                $scanedVehicle->latitude = $request->latitude;
                $scanedVehicle->device_name = $request->device_name;
                $scanedVehicle->username = ucwords(Auth::user()->name);
                $scanedVehicle->lot_id = $request->lot_id;
                $scanedVehicle->save();

                $reservation->is_ticket = '2';
                $reservation->save();

                $citation = TicketCitation::with('user')->where('reservation_id', $reservation->id)->where('is_checkout', '0')->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                $data['reservation']['citation'] = $citation;

                $warning = Warning::with('user')->where('reservation_id', $reservation->id)->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                $data['reservation']['warning'] = $warning;

                return $data;
            }

            $scanedVehicle = new ScanedVehicle();
            $scanedVehicle->facility_id = $facility->id;
            $scanedVehicle->partner_id = $facility->owner_id;
            $scanedVehicle->license_plate = $request->license_plate;
            $scanedVehicle->longitude = $request->longitude;
            $scanedVehicle->latitude = $request->latitude;
            $scanedVehicle->device_name = $request->device_name;
            $scanedVehicle->username = ucwords(Auth::user()->name);
            $scanedVehicle->lot_id = $request->lot_id;
            $scanedVehicle->save();

            $ticket = Ticket::with(['user', 'citation', 'overstay', 'facility'])->where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_closed', '0')->orderBy('id', 'DESC')->first();
            if (!$ticket) {
                $ticket = [];
                #PIMS-14772 || Dev: Sagar || 30/07/2025
                $getUserDetailByLicensePlate = PermitVehicle::with('userFastTrackDetails')
                    ->where('license_plate_number', $request->license_plate)
                    ->where('partner_id', $facility->owner_id)
                    ->first();

                if ($getUserDetailByLicensePlate && $getUserDetailByLicensePlate->userFastTrackDetails) {
                    return [
                        'is_whitelist' => "1",
                        'whiteListMessage' => "Fast track has been enabled on this license plate, you can't create Citation/Warning against this license plate."
                    ];
                }
                $citation = TicketCitation::with('user')->where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->whereDate('payment_date', '=', date("Y-m-d"))->where('is_checkout', '0')->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                if (!$citation) {
                    $citation = TicketCitation::with('user')->where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_checkout', '0')->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();
                    if (!$citation) {
                        //throw new ApiGenericException('1No vehicle plate number found.');    
                        $user = [];
                        $citationHistory = TicketCitation::with('user')->where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_avoid', '0')->orderBy('id', 'DESC')->get();
                        if (count($citationHistory) > 0) {
                            foreach ($citationHistory as $key => $value) {
                                if (isset($value->user)) {
                                    $data['user'] =  $value->user;
                                    break;
                                } else {
                                    $data['user'] = $value->user;
                                }
                            }
                        } else {
                            $data['user'] = null;
                        }

                        $warningHistory = Warning::where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_avoid', '0')->orderBy('id', 'DESC')->get();

                        $warning = Warning::where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_closed', '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();

                        if (!$warning) {

                            $existScanedVehicle = ScanedVehicle::where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->orderBy("created_at", "ASC")->first();
                            if ($existScanedVehicle) {
                                $data['first_scan_date'] = date("Y-m-d H:i:s");
                                if (date("Y-m-d") == date("Y-m-d", strtotime($existScanedVehicle->created_at))) {
                                    $data['first_scan_date'] = date("Y-m-d H:i:s", strtotime($existScanedVehicle->created_at));
                                }
                            } else {
                                $data['first_scan_date'] = date("Y-m-d H:i:s");
                            }
                        }

                        $data['citation_history'] = $citationHistory;
                        $data['warning_history'] = $warningHistory;
                        //$data['ticket'] = $ticket;
                        //$data['user'] = $user;
                        $data['warning'] = $warning;
                        $data['citation'] = $citation;
                        return $data;
                    }
                }
                $citation->is_vehicle_present = '1';
                $citation->scan_date = date("Y-m-d H:i:s");
                $citation->save();
                // $ticket['check_in_datetime'] = $citation->checkin_time;
                // $ticket['checkout_datetime'] = $citation->checkout_time;
                $citation['check_in_datetime'] = $citation->checkin_time;
                $citation['checkout_datetime'] = $citation->checkout_time;
                //$ticket['is_overstay'] = '1';
                // $ticket['is_autopay'] = '0';
                //$ticket['citation'] = $citation;
                $now = date("Y-m-d H:i:s");
                //dd($now, $citation->check_in_datetime);
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $citation->check_in_datetime);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                $diff_in_hours = $arrival_time->diffInRealHours($from);
                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                $diff_in_sec = $arrival_time->diffInSeconds($from);

                $rate = [];
                $is_resident = '0';
                $isMember = 0;
                //dd($arrival_time, $diff_in_hours);
                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, $isMember, $is_resident);
                /*$categoryId = self::NON_RESIDENT_ID;
              $residentRate = Rate::where('category_id', $categoryId)->where('facility_id', $facility->id)->first();
              if(!$residentRate){
                  return Redirect::to('diamond-error-facility');
              }*/

                //$days = $diff_in_hours;


                /* if($diff_in_hours > 24){
              $diff_in_hours = 24;     
              }
  
              if($diff_in_hours == 0 && $diff_in_mins != 0){
                  $diff_in_hours = 1;     
              }
  
              if($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0){
                  $diff_in_hours = 1;     
              }*/

                $rate['price'] = $rate['price'];
                $processingFee = $facility->citation_processing_fee;
                $taxFee = $facility->tax_rate;

                $citation['overstay_length'] = $diff_in_hours;
                $citation['parking_amount'] = number_format($rate['price'], 2);
                $citation['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                $citation['is_overstay'] = '1';
                $citation['is_vehicle_present'] = '1';
                $citation['penalty_fee'] = $citation->penalty_fee;
                $citation['citation_processing_fee'] = number_format($processingFee, 2);
                $citation['tax_fee'] = number_format($taxFee, 2);
                $citation['user'] = $citation->user;

                //get history of citation & warning

                $citationHistory = TicketCitation::where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_avoid', '0')->orderBy('id', 'DESC')->get();

                $warningHistory = Warning::where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where('is_avoid', '0')->orderBy('id', 'DESC')->get();

                $warning = Warning::where('license_plate', $request->license_plate)->whereIn('facility_id', $neighborhoodIds)->where("is_closed", '0')->where('is_avoid', '0')->orderBy('id', 'DESC')->first();

                $data['citation_history'] = $citationHistory;
                $data['warning_history'] = $warningHistory;
                //$data['ticket'] = $ticket;
                $data['user'] = isset($ticket->user) ? $ticket->user : (isset($citation->user) ? $citation->user : null);
                $data['citation'] = $citation;
                $data['warning'] = $warning;
                return $data;
            }
            $ticket->is_vehicle_present = '1';
            $ticket->scan_date = date("Y-m-d H:i:s");
            $ticket->save();

            $scanedVehicle->ticket_id = $ticket->id;
            $scanedVehicle->save();

            //skip is_checkout == '1' for new implementation for ungated
            //if ($ticket->is_checkout == '1') {
            //$ticket['grand_total'] = $ticket['grand_total'] - ($ticket['paid_amount'] > 0 ? $ticket['paid_amount'] : 0.00);
            $ticket['extend_amount'] = $ticket['grand_total'] - ($ticket['paid_amount'] > 0 ? $ticket['paid_amount'] : 0.00);
            $now = date("Y-m-d H:i:s");
            $existTicketExtend = TicketExtend::where("ticket_id", $ticket->id)->orderBy("id", "Desc")->get();
            $extendAmount = 0;
            $extendProcessingFee = 0;
            $extendTaxFee = 0;
            $extendParkingAmount = 0;
            if (count($existTicketExtend) > 0) {
                $checkoutTime = $existTicketExtend[0]->checkout_time;
                foreach ($existTicketExtend as $key => $value) {
                    $extendAmount += $value->grand_total;
                    $extendProcessingFee += $value->processing_fee;
                    $extendTaxFee += $value->tax_fee;
                    $extendParkingAmount += $value->grand_total - $value->processing_fee - $value->tax_fee;
                }

                $ticket['parking_amount'] = sprintf("%.2f", $extendParkingAmount + $ticket['parking_amount']);
                $ticket['processing_fee'] = sprintf("%.2f", $extendProcessingFee + $ticket['processing_fee']);
                $ticket['tax_fee'] = sprintf("%.2f", $extendTaxFee + $ticket['tax_fee']);
                $ticket['extend_amount'] = sprintf("%.2f", $ticket['extend_amount'] + $extendAmount);
            } else {
                $checkoutTime = $ticket->checkout_datetime;
            }

            if ($ticket->reservation_id != '') {
                $isThirdPartyRes = Reservation::find($ticket->reservation_id);
                if ($isThirdPartyRes->thirdparty_integration_id == '1') {
                    $ticket->thirdparty_ticket = '1';
                } else {
                    $ticket->thirdparty_ticket = '0';
                }
            }

            if (strtotime(date("Y-m-d H:i:s", strtotime($now))) > strtotime(date("Y-m-d H:i:s", strtotime($checkoutTime)))) {
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkoutTime);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);

                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                $diff_in_hours = QueryBuilder::getLengthInHours($diff_in_mins);;
                $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, 0);

                $processingFee = 0.00;
                $taxFee = $ticket->getTaxRate($rate);          // to get tax price

                $priceBreakUp = $ticket->unGatedPriceBreakUp($rate, $ticket->facility, $request, 0, 'detail');
                $this->log->info("getCheckinCheckoutDetails ungated priceBreakUp " . json_encode($priceBreakUp));

                $ticket['is_overstay'] = '1';
                $ticket['parking_amount'] = number_format($rate['price'], 2);
                $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                //$ticket['processing_fee'] = number_format($processingFee, 2);
                $ticket['tax_fee'] = $taxFee;          // to get tax price

                /*if ($ticket->paid_by != '') {
                        if (isset($priceBreakUp['amount_paid'])) {
                            $ticket['is_overstay'] = '0';
                            $ticket['parking_amount'] = isset($priceBreakUp['amount_paid']) ? $priceBreakUp['amount_paid'] : number_format($rate['price'], 2); // ungated
                            $ticket['rate'] = "0.00";
                            $ticket['penalty_fee'] = "0.00";
                            $ticket['processing_fee'] = "0.00";
                            $ticket['tax_fee'] = "0.00";
                        }
                    }*/

                //$ticket['overstay_length'] = $diff_in_hours; 
                $ticket->checkout_time = $checkoutTime;
                $ticket->checkout_datetime = $checkoutTime;
                $ticket->estimated_checkout = $checkoutTime;
                $data['ticket'] = $ticket;
                $data['user'] = $ticket->user;
                return $data;
            }
            $ticket->checkout_time = $checkoutTime;
            $ticket->checkout_datetime = $checkoutTime;
            $ticket->estimated_checkout = $checkoutTime;
            $ticket->grand_total = sprintf("%.2f", $ticket['grand_total']);
            $data['ticket'] = $ticket;
            $data['user'] = $ticket->user;
            return $data;
            //return $ticket;
            //}

            if ($ticket->is_checkin == '1' && $ticket->is_checkout == '0') {
                $now = date("Y-m-d H:i:s");
                if (strtotime($now) > strtotime($ticket->checkout_datetime)) {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', $now);
                    $diff_in_hours = $arrival_time->diffInRealHours($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_sec = $arrival_time->diffInSeconds($from);
                    $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                    $diff_in_sec = $arrival_time->diffInSeconds($from);
                    /*if($diff_in_hours > 24){
                     $diff_in_hours = 24;     
                  }*/

                    /*$isMember = 0;
                  $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24,$isMember);
                  
                  if($rate == false){
                      return Redirect::to('autogate-error-facility');
                  }*/

                    $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 24, 0);

                    if ($diff_in_hours > 24) {
                        $diff_in_hours = 24;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins != 0) {
                        $diff_in_hours = 1;
                    }

                    if ($diff_in_hours == 0 && $diff_in_mins == 0 && $diff_in_sec != 0) {
                        $diff_in_hours = 1;
                    }


                    $processingFee = $facility->processing_fee;
                    $taxFee = $facility->tax_rate;

                    $priceBreakUp = $ticket->unGatedPriceBreakUp($rate, $ticket->facility, $request, 0, 'detail');
                    $this->log->info("getCheckinCheckoutDetails ungated priceBreakUp " . json_encode($priceBreakUp));

                    $ticket['is_overstay'] = '1';
                    $ticket['rate'] = number_format($rate['price'] + $facility->penalty_fee + $processingFee + $taxFee, 2);
                    $ticket['penalty_fee'] = number_format($facility->penalty_fee, 2);
                    $ticket['processing_fee'] = number_format($processingFee, 2);
                    $ticket['tax_fee'] = number_format($taxFee, 2);

                    if (isset($priceBreakUp['amount_paid'])) {
                        $ticket['is_overstay'] = '0';
                        $ticket['parking_amount'] = isset($priceBreakUp['amount_paid']) ? $priceBreakUp['amount_paid'] : number_format($rate['price'], 2); // ungated
                        $ticket['rate'] = "0.00";
                        $ticket['penalty_fee'] = "0.00";
                        $ticket['processing_fee'] = "0.00";
                        $ticket['tax_fee'] = "0.00";
                    }

                    $ticket['overstay_length'] = $diff_in_hours;

                    //return $ticket;
                    $data['ticket'] = $ticket;
                    $data['user'] = $ticket->user;
                    return $data;
                }
                $data['ticket'] = $ticket;
                $data['user'] = $ticket->user;
                return $data;
            }
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }

    public function getScanedVehicles(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Invalid partner.');
            }
            if ($request->header('X-FacilityID') != '') {
                $facility = Facility::where('id', $request->header('X-FacilityID'))->first();
            } else {
                $facility = Facility::where('owner_id', $secret->partner_id)->first();
            }

            $this->setCustomTimezone($facility->id);
            $today = date("Y-m-d");
            //$today = "2024-01-24";
            if ($request->scan_date != '') {
                $today = date("Y-m-d", strtotime($request->scan_date));
            }
            $allScanedVehicle = ScanedVehicle::where("facility_id", $facility->id)->whereDate("created_at", '=', $today)->orderBy("id", "DESC")->get();
            if (count($allScanedVehicle) > 0) {
                foreach ($allScanedVehicle as $key => $value) {

                    if ($value->warning_id != '') {
                        $warning = Warning::find($value->warning_id);

                        if (isset($warning->warning_number)) {
                            $value['warning_number'] = $warning->warning_number;
                        }
                    }
                    if ($value->ticket_citation_id != '') {
                        $citation = TicketCitation::find($value->ticket_citation_id);
                        if (isset($citation->citation_number)) {
                            $value['citation_number'] = $citation->citation_number;
                        }
                    }
                }
            }
            return $allScanedVehicle;
        }
    }


    public function getPartnerScanedVehicles(Request $request)
    {

        if ($request->facility_id != '') {
            $this->setCustomTimezone($request->facility_id);
        }

        $today = date("Y-m-d");
        //$today = "2024-01-24";
        if ($request->scan_date != '') {
            $today = date("Y-m-d", strtotime($request->scan_date));
        }
        $allScanedVehicle = ScanedVehicle::whereDate("created_at", '=', $today);
        if ($request->facility_id != '') {
            $allScanedVehicle = $allScanedVehicle->where("facility_id", $request->facility_id);
        }
        if ($request->partner_id != '') {
            $allScanedVehicle = $allScanedVehicle->where("partner_id", $request->partner_id);
        }
        $allScanedVehicle = $allScanedVehicle->orderBy("id", "DESC")->get();
        return $allScanedVehicle;
    }


    public function getPermitDetails(Request $request)
    {
        if ($request->permit_request_id != '') {
            $permitRequest = PermitRequest::with(['PermitVehicle.vehicle'])->select('id', 'account_number', 'desired_start_date', 'desired_end_date', 'phone', 'email', 'name', 'permit_rate', 'user_campus_id')->where('id', $request->permit_request_id)->first();
            return $permitRequest;
        } else if ($request->reservation_id != '') {
            $reservation = Reservation::select('id', 'user_id', 'ticketech_code', 'start_timestamp', 'total', 'license_plate', 'facility_id')->where('id', $request->reservation_id)->first();
            $user = User::find($reservation->user_id);

            if (isset($user->id)) {
                $reservation->name = $user->name;
                $reservation->phone = $user->phone;
                $reservation->email = $user->email;
            }
            return $reservation;
        } else if ($request->ticket_id != '') {
            $ticket = Ticket::with(['ticketExtend'])->select('id', 'user_id', 'ticket_number', 'checkin_time', 'checkout_time', 'grand_total', 'license_plate', 'facility_id')->where('id', $request->ticket_id)->first();
            $user = User::find($ticket->user_id);
            if (isset($user->id)) {
                $ticket->name = $user->name;
                $ticket->phone = $user->phone;
                $ticket->email = $user->email;
            }
            if (isset($ticket->ticketExtend) && count($ticket->ticketExtend) > 0) {
                $existTicketExtend = $ticket->ticketExtend;
                $ticket['checkout_time'] = $existTicketExtend[0]->checkout_time;
                $ticket['checkout_datetime'] = $existTicketExtend[0]->checkout_time;
                foreach ($existTicketExtend as $key => $value) {
                    $extendAmount += $value->grand_total;
                    $extendProcessingFee += $value->processing_fee;
                    $extendTaxFee += $value->tax_fee;
                    $extendParkingAmount += $value->grand_total - $value->processing_fee - $value->tax_fee;
                }

                $ticket['parking_amount'] = $extendParkingAmount + $ticket['parking_amount'];
                $ticket['processing_fee'] = $extendProcessingFee + $ticket['processing_fee'];
                $ticket['tax_fee'] = $extendTaxFee + $ticket['tax_fee'];
                $ticket['extend_amount'] = $extendAmount + $ticket['grand_total'];
            }
            return $ticket;
        } else {
            return true;
        }
    }

    public function getReservationDetails(Request $request)
    {
        // => function ($query)  { $query->select('users.name', 'users.phone', 'email');}
        $reservation = Reservation::select('id', 'user_id', 'ticketech_code', 'start_timestamp', 'total', 'license_plate', 'facility_id')->where('id', $request->reservation_id)->first();
        $user = User::find($reservation->user_id);

        if (isset($user->id)) {
            $reservation->name = $user->name;
            $reservation->phone = $user->phone;
            $reservation->email = $user->email;
        }

        return $reservation;
    }

    public function getCitationDetails($citation_id)
    {
        $citation = TicketCitation::with(['user', 'transaction', 'ticketCitationInfraction.ticketCitationInfractionReason', 'ticketCitationInfractionOther', 'ticketCitationImage', 'facility', 'warnings', 'permit'])->where("id", $citation_id)->first();
        if ($citation) {
            $infractionNameArray = [];
            foreach ($citation->ticketCitationInfraction as $key => $value) {
                $infractionNameArray[] = $value->infraction_name;
            }
            $inflationName = implode(',', $infractionNameArray);
            $citation->all_infraction_name = $inflationName;
        }
        return $citation;
    }

    public function getWarningDetails($warning_id)
    {
        $warning = Warning::with(['user', 'facility', 'citation', 'warningImage', 'warningInfraction.warningInfractionReason', 'permit'])->where("id", $warning_id)->first();

        if ($warning) {
            $infractionNameArray = [];
            foreach ($warning->warningInfraction as $key => $value) {
                $infractionNameArray[] = $value->infraction_name;
            }
            $inflationName = implode(',', $infractionNameArray);
            $warning->all_infraction_name = $inflationName;
        }

        return $warning;
    }

    public function getAllMakeModelsData(Request $request,  $country_id = '')
    {

        $data = [];
        if ($request->header('X-ClientSecret') != '' || isset($request->partner_id)) {
            $partner_id = '';
            $country_id = '';
            if ($request->header('X-ClientSecret') != '') {
                $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('Invalid partner.');
                }
                $partner_id = $secret->partner_id;
            }
            if (isset($request->partner_id)) {
                $partner_id = $request->partner_id;
            }
            if (isset($request->country_id)) {
                $country_id = $request->country_id;
            }

            $facility = DB::table('facilities')
                ->select('id', 'full_name', 'short_name')
                ->where('owner_id', $partner_id)
                ->where('active', '=', '1')
                ->where('is_lot', '=', '1');
            $facility = $facility->get();
            if (count($facility)) {
                $data['facility']    = $facility;
            }

            if ($country_id != '') {
                $state   =  State::where('name', '<>', '')->where("country_id", $country_id)->orderBy('name', 'ASC')->get();
            } else {
                $state   =  State::where('name', '<>', '')->where("country_id", 231)->orderBy('name', 'ASC')->get();
            }

            $country =  Country::where('name', '<>', '')->select('id', 'name', 'country_code', 'phonecode')->orderBy('name', 'Asc')->get();
            $data['state']    = $state;
            $data['country']    = $country;
            $mstMake1 = '';
            $partnerMake = PartnerMakeModelMapping::where("partner_id", $partner_id)->pluck("mst_make_id");
            if (count($partnerMake) > 0) {
                $mstMake = MstMake::select('id', 'name')->whereIn('id', $partnerMake)->orderBy('name', 'Asc')->get();
            } else {
                $mstMake = MstMake::select('id', 'name')->where('name', '<>', '')->orderBy('name', 'Asc')->whereRaw('name NOT REGEXP "^[0-9]+$"')->get();
                $mstMake1 = MstMake::select('id', 'name')->where('name', '<>', '')->orderBy('name', 'Asc')->whereRaw('name REGEXP "^[0-9]+$"')->get();
            }
            if (!in_array($partner_id, config('parkengage.SKIP_MAKE_MODELS_PARTNER'))) {
                $mstMake = $mstMake->load(['mstModel' => function ($query) {
                    $query->whereRaw('name NOT REGEXP "^[0-9]+$"')->orderBy('name', 'asc');
                }]);
                if ($mstMake1) {
                    $mstMake1 = $mstMake1->load(['mstModel' => function ($query) {
                        $query->whereRaw('name REGEXP "^[0-9]+$"')->orderBy('name', 'asc');
                    }]);
                }
            } else {
                //$mstMake['mst_model'] = []; 
            }
            if ($mstMake1) {
                $mergedResults = $mstMake->merge($mstMake1);
                $data['make']    = $mergedResults;
            } else {
                $data['make']    = $mstMake;
            }

            if ($partner_id != '') {
                $color   =  MstColor::where('name', '<>', '')->where("partner_id", $partner_id)->orderBy('name', 'ASC')->get();
                if (count($color) <= 0) {
                    $color   =  MstColor::where('name', '<>', '')->whereNull("partner_id")->orderBy('name', 'ASC')->get();
                }
                $style   =  MstStyle::where('name', '<>', '')->where("partner_id", $partner_id)->orderBy('name', 'ASC')->get();
                if (count($style) <= 0) {
                    $style   =  MstStyle::where('name', '<>', '')->whereNull("partner_id")->orderBy('name', 'ASC')->get();
                }
                $mstVehicleType   =  MstVehicleType::where('name', '<>', '')->where("partner_id", $partner_id)->orderBy('name', 'ASC')->get();
                if (count($mstVehicleType) <= 0) { //in case if vehicletype not found for partner then send default vehicle type
                    $mstVehicleType   =  MstVehicleType::where('name', '<>', '')->whereNull("partner_id")->orderBy('name', 'ASC')->get();
                }
            } else {
                $color   =  MstColor::where('name', '<>', '')->whereNull("partner_id")->orderBy('name', 'ASC')->get();
                $style   =  MstStyle::where('name', '<>', '')->whereNull("partner_id")->orderBy('name', 'ASC')->get();
                $mstVehicleType   =  MstVehicleType::where('name', '<>', '')->whereNull("partner_id")->orderBy('name', 'ASC')->get();
            }


            //$data['make']    = $make;
            //$data['state']    = $state;
            $data['color']    = $color;
            $data['style']    = $style;
            $data['vehicletype']    = $mstVehicleType;
            //$data['country']    = $country;


            return $data;
        } else {
            $mstMake = MstMake::select('id', 'name')->where('name', '<>', '')->orderBy('name', 'Asc')->get();
            $mstMake = $mstMake->load(['mstModel' => function ($query) {
                $query->orderBy('name', 'asc');
            }]);
            $data = [];
            $make    = $mstMake;
            if ($country_id != '') {
                $state   =  State::where('name', '<>', '')->where("country_id", $country_id)->orderBy('name', 'ASC')->get();
            } else {
                $state   =  State::where('name', '<>', '')->where("country_id", 231)->orderBy('name', 'ASC')->get();
            }
            #pims-13318 #DD
            $mstVehicleType   =  MstVehicleType::where('name', '<>', '')->whereNull("partner_id")->orderBy('name', 'ASC')->get();
            $color   =  MstColor::where('name', '<>', '')->whereNull("partner_id")->orderBy('name', 'ASC')->get();
            $style   =  MstStyle::where('name', '<>', '')->whereNull("partner_id")->orderBy('name', 'ASC')->get();
            $country =  Country::where('name', '<>', '')->select('id', 'name', 'country_code', 'phonecode')->orderBy('name', 'Asc')->get();
            $data['make']    = $make;
            $data['state']    = $state;
            $data['color']    = $color;
            $data['style']    = $style;
            $data['vehicletype']    = $mstVehicleType;
            $data['country']    = $country;
            return $data;
        }
    }

    //this api use for update the waning  and citation
    public function updateWarningCitation(Request $request)
    {

        //is_flag = 1 for warning table, is_flag = 2 for ticket_citations table update.
        $this->request = $request;
        if ($this->request->is_flag == '1') {
            $warning = Warning::find($this->request->id);
            if (!$warning) {
                throw new ApiGenericException("Id not found.");
            }
            $this->setCustomTimezone($warning->facility_id);
            $warning->amount_comment = $this->request->comment;
            $warning->comment = $this->request->comment;
            $warning->closed_date = date("Y-m-d H:i:s");
            $warning->is_closed = "1";
            $warning->save();
            return  $warning;
        } else if ($this->request->is_flag == '2') {
            $citation = TicketCitation::find($this->request->id);
            if (!$citation) {
                throw new ApiGenericException("Id not found.");
            }
            $this->setCustomTimezone($citation->facility_id);
            $citation->amount = $this->request->amount;
            $citation->amount_comment = $this->request->comment;
            $citation->paid_date = date('Y-m-d', strtotime($this->request->paid_date));
            $citation->closed_date = date("Y-m-d H:i:s");
            $citation->is_closed = '1';
            if (isset($request->citation_void_code_id)) {
                $citation->citation_void_code_id = $request->citation_void_code_id;
            }
            $citation->save();
            return  $citation;
        }
        //avoid comment code start form here this is the other feature for warning and citation
        if ($this->request->is_avoid == '1') {
            $warning = Warning::find($this->request->id);
            if (!$warning) {
                throw new ApiGenericException("Id not found.");
            }
            $warning->avoid_comment = $this->request->avoid_comment;
            $warning->is_avoid = "1";
            $warning->closed_date = date("Y-m-d H:i:s");
            $warning->is_closed = '1';
            $warning->save();
            return  $warning;
        } else if ($this->request->is_avoid == '2') {
            $citation = TicketCitation::find($this->request->id);
            if (!$citation) {
                throw new ApiGenericException("Id not found.");
            }
            $citation->avoid_comment = $this->request->avoid_comment;
            $citation->is_avoid = 1;
            if (isset($request->citation_void_code_id)) {
                $citation->citation_void_code_id = $request->citation_void_code_id;
            }
            $citation->save();
            return  $citation;
        }
    }

    /**
     * Alka
     * Date: 15 Nov 2024
     * Method to get list of facilities 
     * CS-313
     */
    public function getAdminPartnerPermitFacility(Request $request, $slug = '', $business_id = '', $permit_type_id = '')
    {
        if (!Auth::user()) {
            throw new ApiGenericException('Invalid partner.');
        }
        $authUser = Auth::user();
        if ($authUser) {
            if (isset($request->partner_id) && !empty($request->partner_id)) {
                $partner_id = $request->partner_id;
            } else if ($authUser->user_type == 1) {
                $partner_id = '';
            } else if ($authUser->user_type == 3) {
                $partner_id = $authUser->id;
            } else {
                $partner_id = $authUser->created_by;
            }

            $facility_id = [];
            if ($slug != '') {
                $rmExist = User::where('slug', $slug)->where('status', '1')->first();

                if ($rmExist) {
                    $facility_id = DB::table('user_facilities')->where('user_id', $rmExist->id)->whereNull('deleted_at')->pluck('facility_id');
                }
            }

            if (count($facility_id) > 0) {
                $facility_ids = PermitRate::whereIn('facility_id', $facility_id)->get()->pluck('facility_id');
            } else {
                $facility_ids = PermitRate::get()->pluck('facility_id');
            }

            if (isset($business_id) && !empty($business_id)) {
                $user_id = $business_id;
                $user_permit_remaning = UserPermitTypeMapping::where('user_id', $user_id)->get();
                if ($user_permit_remaning != null) {
                    $permitTypeIds             = $user_permit_remaning->pluck('permit_type_id');
                    $facility_ids             = PermitRate::whereIn('id', $permitTypeIds)->pluck('facility_id');
                }
            }

            $permitServiceIds = explode(',', $business_id);

            if ($partner_id == config('parkengage.BUYOUT.PARTNER_ID') && isset($business_id) && !empty($business_id) && array_intersect($permitServiceIds, config('parkengage.PERMIT_SERVICES.PERMIT_SERVICES_IDS'))) {

                if (isset($permit_type_id) && !empty($permit_type_id)) {
                    $facility_ids = PermitServicesFacilityMapping::join('permit_services_facility_rate_desc_mapping as psfm', function ($join)  use ($permit_type_id) {
                        $join->on('psfm.permit_services_facility_id', '=', 'permit_services_facility_mapping.id')
                            ->where('psfm.permit_rate_description_id', '=', $permit_type_id);
                    })
                        ->whereIn('permit_service_id', $permitServiceIds)
                        ->groupBy('facility_id')
                        ->get()
                        ->pluck('facility_id');
                } else {
                    $facility_ids = PermitServicesFacilityMapping::whereIn('permit_service_id', $permitServiceIds)->groupBy('facility_id')->get()->pluck('facility_id');
                }
            }

            $facilities = Facility::with(['facilityConfiguration', 'FacilityPaymentDetails'])->select(
                'id',
                'short_name',
                'full_name',
                'active',
                'license_format',
                'license_min_lenght',
                'license_max_lenght',
                'fix_length',
                'is_permit_purchase_enabled'
            )->where('active', 1)->with(array('geolocations' => function ($query) {
                $query->select('address_1', 'address_2', 'city', 'state', 'zip_code', 'locatable_id');
            }))->where('owner_id', $partner_id)->wherein('id', $facility_ids)->where('is_available', '1')->whereIn('facility_booking_type', [0, 2])->orderByRaw("CASE WHEN id = " . config('parkengage.USM_FACILITY') . " THEN 0 ELSE 1 END")->get();
            #PIMS-11259 DD fist index 167/407 prod/stag facility to come
            if ($request->rm_id) {
                $customer_menu = CustomerPortalPermission::where("partner_id", $partner_id)->where('rm_id', $request->rm_id)->orderBy('list_order', 'ASC')->get();
            } else {
                $customer_menu = CustomerPortalPermission::where("partner_id", $partner_id)->whereNull('rm_id')->orderBy('list_order', 'ASC')->get();
            }

            $customer_permissions = [];
            if ($customer_menu) {
                foreach ($customer_menu as $key => $value) {
                    if ($value->type == '0') {
                        $customer_permissions["Main Menu"][] = $value;
                    }
                    if ($value->type == '1') {
                        $customer_permissions["Account Menu"][] = $value;
                    }
                    if ($value->type == '2') {
                        $customer_permissions["Access Permissions"][] = $value;
                    }
                    if ($value->type == '3') {
                        $customer_permissions["Restricted Permissions"][] = $value;
                    }
                }
                if ($request->is_mobile_request == '1') {
                    $data['facilities'] = $facilities;
                    $data['customer_portal_permission'] = $customer_permissions;
                    return $data;
                }
            }
            return $facilities;
        } else {
            throw new ApiGenericException('Invalid partner.');
        }
    }

    public function getCitationVoidCodes($partner_id = '')
    {
        if ($partner_id == '') {
            if (Auth::user()->user_type == 3) {
                $partner_id = Auth::user()->id;
            } else {
                $partner_id = Auth::user()->created_by;
            }
        }
        return CitationVoidCode::where("partner_id", $partner_id)->where("status", "1")->get();
    }
}
