<?php

namespace App\Http\Controllers\ParkEngage;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Support\Facades\Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\BusinessPolicy;
use Illuminate\Support\Facades\Hash;
use App\Models\PolicyDay;
use App\Models\BusinessFacilityPolicy;
use App\Exceptions\UserNotAuthorized;
use App\Services\LoggerFactory;
use App\Models\BusinessQrCode;
use Illuminate\Support\Facades\DB;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

class BusinessPolicyController extends Controller
{

    const USERTYPE_BUSINESS = 10;
    const SUPERADMIN = 1;
    const SUBORDINATE = 4;
    const PARTNER = 3;
    const REGIONAL_MANAGER = 12;
    const BUSINESS_CLERK = 8;

    protected $log;

    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/parkengage/business-policy')->createLogger('business-policy');
    }

    public function index(Request $request)
    {
        if (!Auth::user()) {
            throw new UserNotAuthorized("Invalid User!");
        }
        if (Auth::user()->status == '0') {
            throw new UserNotAuthorized("User is inactive. Please contact to admin.");
        }

        if (Auth::user()->user_type == self::USERTYPE_BUSINESS) {
            $partner_id = Auth::user()->created_by;
            # PIMS-14566 - check for policy id
            $policy_id = DB::table('business_facility_policy')->where('business_id', Auth::user()->business_id)->pluck('policy_id');
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id)->whereIn('id', $policy_id)->get();
        } else if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at');
            if ($partner_id) {
                $result = $result->where('partner_id', $partner_id);
            }
            $result = $result->get();
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id)->get();
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id)->get();
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            # PIMS-14566 - check for policy id
            $policy_id = DB::table('business_facility_policy')->where('business_id', Auth::user()->business_id)->pluck('policy_id');
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id)->whereIn('id', $policy_id)->get();
        } else {
            $partner_id = Auth::user()->id;
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id)->get();
        }
        //dd($partner_id);

        foreach ($result as $value) {
            if ($value->is_validity_start_end == 1) {
                if (strtotime($value->validity_end_date) < strtotime(date("Y-m-d"))) {
                    BusinessPolicy::where('id', $value->id)->update(['status' => 0]);
                }
            }
        }
        //update status if policy is expired
        $result = BusinessPolicy::with('policyday')->whereNull('deleted_at');

        if (Auth::user()->user_type == self::USERTYPE_BUSINESS) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
            # PIMS-14566 - check for policy id
            $policy_id = DB::table('business_facility_policy')->where('business_id', Auth::user()->business_id)->pluck('policy_id');
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id)->whereIn('id', $policy_id);
            if ($rm_id) {
                $result = $result->where('rm_id', $rm_id);
            }
        } else if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at');
            if ($partner_id) {
                $result = $result->where('partner_id', $partner_id);
            }
            if ($rm_id) {
                $result = $result->where('rm_id', $rm_id);
            }
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id);
            if ($rm_id) {
                $result = $result->where('rm_id', $rm_id);
            }
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
            //$result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id)->where('rm_id', $rm_id);
            $result = BusinessPolicy::with('policyday')
                ->whereNull('deleted_at')
                ->where('partner_id', $partner_id)
                ->where(function ($query) use ($rm_id) {
                    $query->whereNull('rm_id')
                        ->orWhere('rm_id', $rm_id);
                });
        } else if (Auth::user()->user_type == self::PARTNER) {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id);
            if ($rm_id) {
                $result = $result->where('rm_id', $rm_id);
            }
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
            # PIMS-14566 - check for policy id
            $policy_id = DB::table('business_facility_policy')->where('business_id', Auth::user()->business_id)->pluck('policy_id');
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('partner_id', $partner_id)->whereIn('id', $policy_id);
            if ($rm_id) {
                $result = $result->where('rm_id', $rm_id);
            }
        } else {
            throw new UserNotAuthorized("Invalid User");
        }

        if ($request->search != '') {
            if (strtolower($request->search) == "full_ticket") {
                $result = $result->Where('discount_type', 0);
                if ($partner_id) {
                    $result = $result->Where('partner_id', $partner_id);
                }
            } elseif ($request->search == "Percentage") {
                $result = $result->Where('discount_type', 3);
                if ($partner_id) {
                    $result = $result->Where('partner_id', $partner_id);
                }
            } elseif ($request->search == "Amount") {
                $result = $result->Where('discount_type', 2);
                if ($partner_id) {
                    $result = $result->Where('partner_id', $partner_id);
                }
            } elseif ($request->search == "Hours" || $request->search == "hours") {

                $result = $result->Where('duration_type', strtolower($request->search));
                if (isset($partner_id) && !empty($partner_id)) {
                    $result = $result->Where('partner_id', $partner_id);
                }
            } elseif ($request->search == "Active" || $request->search == "active") {
                $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('status', 1);
                if (isset($partner_id) && !empty($partner_id)) {
                    $result = $result->Where('partner_id', $partner_id);
                }
            } elseif ($request->search == "Inactive" || $request->search == "inactive") {
                $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('status', 0);
                if (isset($partner_id) && !empty($partner_id)) {
                    $result = $result->Where('partner_id', $partner_id);
                }
            } else {

                $result = $result->Where('policy_name', 'like', '%' . trim($request->search) . '%')->orWhere('consumption_channel', 'like', '%' . trim($request->search) . '%');
                if (isset($partner_id) && !empty($partner_id)) {
                    $result = $result->Where('partner_id', $partner_id);
                }
            }
        }
        /*
        //filter on basic on RM id
        if(isset($request->rm_id) && !empty($request->rm_id) ){
            $result = $result->Where('rm_id', $request->rm_id);
        }
        */
        //when status given 
        if ($request->status != '') {
            $result = $result->Where('status', $request->status);
            if ($partner_id) {
                $result = $result->where('partner_id', $partner_id);
            }
            $result = $result->get();
            return $result;
        }
        if ($request->sort != '') {
            $result = $result->orderBy($request->sort, $request->sortBy);
        } else {
            $result = $result->orderBy("id", "DESC");
        }
        //when status given 
        $result = $result->paginate(10);
        if (!$result) {
            throw new ApiGenericException("Business Policy Not Found");
        }
        return $result;
    }

    public function store(Request $request)
    {
        $this->log->error('Create Business Policy' . json_encode($request->all()));
        if (Auth::user()->user_type == self::USERTYPE_BUSINESS) {
            $partner_id = Auth::user()->created_by;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        } else if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        } else {
            $partner_id = Auth::user()->id;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        }
        $policyDay = [];
        $checkPolicy = BusinessPolicy::whereNull('deleted_at')->where('policy_name', $request->policy_name)->where('partner_id', $partner_id)->first();

        if ($checkPolicy) {
            throw new ApiGenericException("Business Policy Already Added");
        }

        $data['policy_name'] = $request->policy_name;
        $data['user_type'] = isset($request->user_type) ? $request->user_type : 0;
        $data['consumption_channel'] = isset($request->consumption_channel) ? $request->consumption_channel : null;
        if ($request->discount_type == "1") {
            $data['duration_type'] = $request->duration_type;
            $data['duration_value'] = $request->duration_value;
        }
        $data['discount_type'] = isset($request->discount_type) ? $request->discount_type : null;
        $data['discount_value'] = isset($request->discount_value) ? $request->discount_value : null;
        $data['discount_min'] = isset($request->discount_min) ? $request->discount_min : null;
        $data['discount_max'] = isset($request->discount_max) ? $request->discount_max : null;
        $data['validity_start_date'] = $request->validity_start_date;
        $data['validity_end_date'] = $request->validity_end_date;
        $data['booking_start_date'] = $request->booking_start_date;
        $data['booking_end_date'] = $request->booking_end_date;
        $data['is_booking_start'] = ($request->is_booking_start == true) ? 1 : 0;
        $data['is_booking_end'] = ($request->is_booking_end) ? 1 : 0;
        $data['remarks'] = $request->remarks;
        $data['status'] = $request->status;
        $data['partner_id'] = $partner_id;
        $data['is_validity_start_end'] = $request->is_validity_start_end;
        $data['created_by'] = Auth::user()->id;
        $data['is_tax_applicable'] = $request->is_tax_applicable;
        $data['rm_id'] = $rm_id;

        $result = BusinessPolicy::create($data);
        if ($request->entry_exit_time) {
            foreach ($request->entry_exit_time as $key => $val) {

                $policyDay['business_policy_id'] = $result->id;
                $policyDay['days'] = $val['days'];
                $policyDay['start_time'] = ($val['start_time'] != '') ? $val['start_time'] : Null;
                $policyDay['end_time'] = ($val['end_time'] != '') ? $val['end_time'] : null;
                if ($val['next_day'] == true) {
                    $policyDay['next_day'] = 1;
                } else {
                    $policyDay['next_day'] = 0;
                }
                $policyDay['max_hour'] = ($val['max_hour']) ? $val['max_hour'] : NULL;
                $policyDay['daysIds'] = $val['daysIds'];
                PolicyDay::create($policyDay);
            }
        }

        if (!$result) {
            throw new ApiGenericException("Record Not Added");
        }
        return $result;
    }

    public function destroy($id)
    {
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }
        if (Auth::user()->user_type == '1') {
            $result = BusinessPolicy::find($id);
        } else {
            $result = BusinessPolicy::where('partner_id', $partner_id)->find($id);
        }

        if ($result) {
            $result->delete();
            BusinessFacilityPolicy::where('policy_id', $id)->delete();
            return "Data successfully deleted.";
        }
        throw new ApiGenericException("Record Not Found.");
    }

    public function update(Request $request)
    {
        $this->log->error('Update Business Policy' . json_encode($request->all()));
        if (Auth::user()->user_type == self::USERTYPE_BUSINESS) {
            $partner_id = Auth::user()->created_by;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        } else if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            $rm_id = $request->rm_id;
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->user_parent_id;
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $rm_id = Auth::user()->id;
        } else if (Auth::user()->user_type == self::BUSINESS_CLERK) {
            $partner_id = Auth::user()->created_by;
            $rm_id = isset($request->rm_id) ? $request->rm_id : '';
        } else {
            $partner_id = Auth::user()->id;
            $rm_id = $request->rm_id;
        }
        $checkBusinessPolicy = BusinessPolicy::whereNull('deleted_at')->where('id', $request->id)->where('partner_id', $partner_id)->first();

        if (!$checkBusinessPolicy) {
            throw new ApiGenericException("Record Not Found");
        }

        $checkBusinessPolicy->policy_name = $request->policy_name;
        $checkBusinessPolicy->user_type = $request->user_type;
        $checkBusinessPolicy->consumption_channel = $request->consumption_channel;
        if ($request->discount_type == "1") {
            $checkBusinessPolicy->duration_type = $request->duration_type;
            $checkBusinessPolicy->duration_value = $request->duration_value;
        }
        $checkBusinessPolicy->discount_type = $request->discount_type;
        $checkBusinessPolicy->discount_value = $request->discount_value;

        $checkBusinessPolicy->discount_min = $request->discount_min;
        $checkBusinessPolicy->discount_max = $request->discount_max;
        $checkBusinessPolicy->validity_start_date = $request->validity_start_date;
        $checkBusinessPolicy->validity_end_date = $request->validity_end_date;
        $checkBusinessPolicy->booking_start_date = $request->booking_start_date;
        $checkBusinessPolicy->booking_end_date = $request->booking_end_date;
        $checkBusinessPolicy->remarks = $request->remarks;
        $checkBusinessPolicy->status = $request->status;
        $checkBusinessPolicy->is_booking_start = ($request->is_booking_start == true) ? 1 : 0;
        $checkBusinessPolicy->is_booking_end = ($request->is_booking_end) ? 1 : 0;
        $checkBusinessPolicy->partner_id = $partner_id;
        $checkBusinessPolicy->is_validity_start_end = $request->is_validity_start_end;
        $checkBusinessPolicy->is_tax_applicable = $request->is_tax_applicable;
        $checkBusinessPolicy->save();

        if ($request->entry_exit_time) {
            PolicyDay::where('business_policy_id', $request->id)->delete();
            foreach ($request->entry_exit_time as $key => $val) {

                $policyDay['business_policy_id'] = $request->id;
                $policyDay['days'] = $val['days'];
                $policyDay['start_time'] = ($val['start_time'] != '') ? $val['start_time'] : Null;
                $policyDay['end_time'] = ($val['end_time'] != '') ? $val['end_time'] : null;
                if ($val['next_day'] == true) {
                    $policyDay['next_day'] = 1;
                } else {
                    $policyDay['next_day'] = 0;
                }
                $policyDay['max_hour'] = ($val['max_hour']) ? $val['max_hour'] : NULL;
                $policyDay['daysIds'] = $val['daysIds'];

                PolicyDay::create($policyDay);
            }
        } else {

            PolicyDay::where('business_policy_id', $request->id)->delete();
        }
        if ($request->status == 0) {
            BusinessFacilityPolicy::where('policy_id', $request->id)->delete();
        }
        //check businessQR code if policy will be inactive then QR code will be deleted.
        $policyQR = BusinessQrCode::where('policy_id', $request->id)->first();
        if ($policyQR) {

            if ($request->status == 0) {
                BusinessQrCode::where('policy_id', $request->id)->delete();
            }
        }

        return $checkBusinessPolicy;
    }

    public function show($id)
    {
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }
        if (Auth::user()->user_type == '1') {
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->get();
        } else {
            $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('id', $id)->where('partner_id', $partner_id)->get();
        }
        if ($result->isEmpty()) {
            throw new NotFoundException("Records not found.");
        }

        foreach ($result as $value) {
            if ($value->is_validity_start_end == 1) {
                if (strtotime($value->validity_end_date) < strtotime(date("Y-m-d"))) {
                    BusinessPolicy::where('id', $value->id)->update(['status' => 0]);
                }
            }
        }
        //update status if policy is expired
        $result = BusinessPolicy::with('policyday')->whereNull('deleted_at')->where('id', $id)->first();
        $policyExist = BusinessFacilityPolicy::whereNull('deleted_at')->where('policy_id', $id)->pluck('policy_id');
        $result['is_policy'] = (count($policyExist) > 0) ? 1 : 0;

        return $result;
    }
}
