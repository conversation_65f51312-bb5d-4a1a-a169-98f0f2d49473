<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Crypt;
use Hash;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redirect;
use App\Models\ParkEngage\Gate;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Configuration;
use App\Models\PermitTicket;
use App\Models\PermitRequest;
use App\Classes\AbacusWebService;
use Twilio\Exceptions\RestException;
use App\Models\UserPass;
use App\Models\AuthorizeNetTransaction;
use App\Models\UserEventsLog;
use App\Models\OauthClient;
use App\Models\ParkEngage\TicketCitation;
use App\Services\Pdf;
use App\Classes\InlineViewCss;
use App\Services\Image;

class TownsendWebController extends Controller
{

   protected $log;
   protected $logPayment; 
   protected $user;
   protected $partnerPaymentDetails;
   protected $authNet;
   protected $facility;
   protected $sendAnet=false;
   protected $anonymousAnet=false;
   protected $paymentProfileId;
   protected $cim;

   const RESERVATION_THRESHOLD_TYPE = 2;
   const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE= 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO= 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR= 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "Sold Out At This Time.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    const  GRACE_PERIOD = 1;
    const  FACILITY_ID = 33;

   public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->cim = $cim;
        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        $this->log = $logFactory->setPath('logs/parkengage/townsend-web')->createLogger('townsend-web');

        $this->logPayment = $logFactory->setPath('logs/parkengage/townsend-web-payment-logs')->createLogger('townsend-web-payment-logs');
    }

    public function getTicketReceipt(){
        return view('townsend.checkin-checkout-search-receipt');
        
    }

    public function checkinCheckoutReceipt(Request $request){
        $checkinData = Ticket::with(['transaction','facility'])->where('facility_id', self::FACILITY_ID)->where('card_last_four', $request->card_last_four)->where('expiry', $request->card_expiry)->orderBy("id", "DESC")->first();
        if(count($checkinData) == 0){
            return back()->with('danger', "Please enter valid card details.");
        }
        return view('townsend.checkin-checkout-receipt', ['data'=>$checkinData]);
        
    }

    public function downloadReceipt($ticket_number){
        $checkinData = Ticket::with(['transaction', 'facility'])->where('facility_id', self::FACILITY_ID)->where('ticket_number', $ticket_number)->orderBy("id", "DESC")->first();
        if(count($checkinData) == 0){
            return Redirect::to('error-checkin');
        }
        $html = view('townsend.checkin-checkout-pdf', ['data'=>$checkinData])->render();
        $image = app()->make(Pdf::class);
        $filename = $checkinData->ticket_number.".pdf";
        header("Content-type:application/pdf");
        header('Content-Disposition: inline; filename="' . $filename . '"');
        return $this->respondWithPdf($image->getOutputFromHtmlString($html));
    }

    public function errorCheckin(){
        return view('townsend.error-checkin');
    }
    
    protected function checkFacilityAvailable($facility_id){
        $facility = Facility::where('id', $facility_id)->where('active', '1')->first();
        if(count($facility) == 0){
            return false;
        }
        if($facility->is_available != '1'){
          return false;  
        }
        return true;
        
    }

    public function setCustomTimezone($facility_id){
		$facility = Facility::find($facility_id);
		$secret = OauthClient::where('partner_id', $facility->owner_id)->first();        
		$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
		
		if(($facility) && ($facility->timezone!='')){
		  date_default_timezone_set($facility->timezone);
		}else{
			if($partnerTimezone){
				if($partnerTimezone->timezone != ''){
					date_default_timezone_set($partnerTimezone->timezone);
				}
			}
		}    
	}
    
      public function getCheckinDetailsPaymentThankyou($ticket_number){
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('townsend-error-checkin');
        }
        if($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != ''){
            return Redirect::to('townsend-pay/'.$ticket_number);   
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('townsend-error-checkout');   
        }

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('townsend.thankyou-payment', ['data'=>$checkinData]);
    }

    public function getCheckinPaymentDetails($ticket_number){
        
        Session::forget('is_direct_checkout');
        Session::forget('is_sms_direct_checkout');
        Session::forget('email');
        Session::forget('phone');

        $checkinData = Ticket::with(['user','facility.photos'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if(count($checkinData) == 0){
            $this->log->error("error on checkin screen");
            return Redirect::to('townsend-error-checkin');
        }
        
       if($this->checkFacilityAvailable($checkinData->facility_id) == false){
            Redirect::to('error-facility');
        }
        $this->setCustomTimezone($checkinData->facility_id);

        if($checkinData->anet_transaction_id != ''){
            return redirect('townsend-thankyou-payment/'.base64_encode($checkinData->ticket_number));
        }
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        $diff_in_mins = $arrival_time->diffInRealMinutes($from);
        $diff_in_secs = $arrival_time->diffInSeconds($from);

        //dd($diff_in_hours,$diff_in_mins,$diff_in_secs);
        if($diff_in_mins < 60){
          $diff_in_hours = $diff_in_mins/100;
        }
        if($diff_in_mins > 59){
          $diff_in_hours = number_format($diff_in_mins/60,2);
        }
        if($diff_in_secs < 60){
          $diff_in_hours = .01;
        }

        $checkinData['length'] = $diff_in_hours;
        //$facility = Facility::find($checkinData->facility_id);

        $isMember = 0;
        $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
        if($rate == false){
            return Redirect::to('townsend-error-facility');
        }
            
        //return current availabilit
        if($rate['price'] == 'N/A'){
            $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            $rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
        }else{
            $rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
        }
        //returning  message as per availibility 
        $checkinData['rate'] = $rate;
        $this->log->info("user is on checkin screen on sms click with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('townsend.payment-screen', ['data'=>$checkinData]);
    }


    public function getAfterCheckinPaymentDetails($ticket_number){
        $checkinData = Ticket::with('user')->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '0')->first();
        if(count($checkinData) == 0){
            return Redirect::to('townsend-error-checkin');
        }
        if($checkinData->anet_transaction_id != ''){
            return redirect('townsend-thankyou-payment/'.base64_encode($checkinData->ticket_number));
        }
        $this->setCustomTimezone($checkinData->facility_id);

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        
        
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        $diff_in_mins = $arrival_time->diffInRealMinutes($from);
        $diff_in_secs = $arrival_time->diffInSeconds($from);

        //dd($diff_in_hours,$diff_in_mins,$diff_in_secs);
        if($diff_in_mins < 60){
          $diff_in_hours = $diff_in_mins/100;
        }
        if($diff_in_mins > 59){
          $diff_in_hours = number_format($diff_in_mins/60,2);
        }
        if($diff_in_secs < 60){
          $diff_in_hours = .01;
        }

        $checkinData['length'] = $diff_in_hours;
        $facility = Facility::find($checkinData->facility_id);

        /** this function is used to get Availability Information for respective facility **/
        /*if($checkinData->paid_by != ''){
            if($checkinData->paid_type == '0' || $checkinData->paid_type == 0){
                return redirect('touchless-parking-checkout/'.base64_encode($checkinData->ticket_number));            
            }
        }
        if($checkinData->paid_type == '1' || $checkinData->paid_type == 1){
            if($checkinData->paid_hour != ''){
                $diff_in_hours = $diff_in_hours - $checkinData->paid_hour;
                if($diff_in_hours <= 0 || $diff_in_hours <= 0.00){
                    return redirect('touchless-parking-checkout/'.base64_encode($checkinData->ticket_number));
                }
            }
        }*/
        $isMember = 0;
        $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
        if($rate == false){
            return Redirect::to('townsend-error-facility');
        }
            
        //return current availabilit
        if($rate['price'] == 'N/A'){
            $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
            if($rate['price'] == "0.00" || $rate['price'] == 0){
                return redirect('townsend-scan-screen/'.base64_encode($checkinData->ticket_number))/*->with('success', 'You have successfull get some .')*/;
            }
            $rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
            
        }else{
            if($rate['price'] == "0.00" || $rate['price'] == 0){
                return redirect('townsend-scan-screen/'.base64_encode($checkinData->ticket_number))/*->with('success', 'You have successfull get some .')*/;
                
            }
            $rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
        }
        
       //retialer task
       //if($checkinData->paid_amount != '' || $checkinData->paid_amount >= 0){
        /*if($checkinData->paid_by != ''){
            if($checkinData->paid_type == '0' || $checkinData->paid_type == 0){
                return redirect('touchless-parking-checkout/'.base64_encode($checkinData->ticket_number));            
            }else{
                $rate['price'] = number_format($rate['price'] - $checkinData->paid_amount, 2);
            }
        }*/
        $checkinData['rate'] = $rate;

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $this->log->info("user is on payment screen after click on checkout button on checkin screen with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
        return view('townsend.after-checkin-payment-screen', ['data'=>$checkinData]);
    }

    public function errorFacility(){
        return view('townsend.error-facility');
    }

    public function alreadyCheckout(){
        return view('townsend.already-checkout');
      }

    public function makePayment(Request $request){
        
        $this->checkFacilityAvailable($this->request->facility_id);
        $this->setDecryptedCard($request);

        //$this->validate($this->request, $this->billingValidation);

        $this->setCustomTimezone($this->request->facility_id);

        $checkinData = Ticket::with(['facility','user'])->where('ticket_number', $this->request->ticket_number)->where('is_checkin', '1')->where('is_checkout', '0')->first();

        if(!$checkinData){
            return back()->with('danger', "Invalid ticket detials.");
        }

        //$this->facility = Facility::find($this->request->facility_id);
        $this->user = $checkinData->user;
        if($this->user){

            $this->user->name = $this->request->name_on_card;
            $this->user->email = $this->request->email;
            $this->user->save();
        }else{

            $this->user = User::create(
                [
                'name' => $this->request->name_on_card,
                'email' => $this->request->email,
                'phone' => $this->request->phone,
                'password' => Hash::make(str_random(60)),
                'anon' => true,
                'user_type' => '5',
                'created_by' => $this->facility->owner_id
                ]
            );
        }
        if($this->request->total>0)
        {
            try{

                $paymentStatus = $this->makePlanetPayment($this->request);
                if(isset($paymentStatus['error']) && $paymentStatus['error'] !=''){
                    return back()->with('danger', "Payment failed! Please enter valid card details.");
                }

                $ticket = $this->saveTicket();

                $ticket->anet_transaction_id = $paymentStatus->id;
                $ticket->save();
                if($this->request->email != ''){
                    Artisan::queue('email:townsend-checkin-payment',array('id'=>$ticket->id, 'type'=>'normal'));
                }
                

                return redirect('townsend-thankyou-payment/'.base64_encode($ticket->ticket_number))->with('success', 'Success! Payment Successfully done.');                
                
            } catch (Exception $e) {
                $this->log->error($e);
                $request->session()->flash('alert-danger',
                'Payment error!');
                return back()->withError(['errMsg'=>$e]);
            }
        }

        
    }


    public function getCheckinDetails($ticket_number){
        $checkinData = Ticket::where('ticket_number', base64_decode($ticket_number))->first();
        if(count($checkinData) == 0){
            return Redirect::to('townsend-error-checkin');
        }
        if($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != ''){

            $this->setCustomTimezone($checkinData->facility_id);

            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            
            
            $diff_in_hours = $arrival_time->diffInRealHours($from);
            $diff_in_mins = $arrival_time->diffInRealMinutes($from);
            $diff_in_secs = $arrival_time->diffInSeconds($from);

            //dd($diff_in_hours,$diff_in_mins,$diff_in_secs);
            if($diff_in_mins < 60){
            $diff_in_hours = $diff_in_mins/100;
            }
            if($diff_in_mins > 59){
            $diff_in_hours = number_format($diff_in_mins/60,2);
            }
            if($diff_in_secs < 60){
            $diff_in_hours = .01;
            }

            $checkinData['length'] = $diff_in_hours;
            $facility = Facility::find($checkinData->facility_id);

            $isMember = 0;
            $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
            if($rate == false){
                return Redirect::to('townsend-error-facility');
            }
            //return current availabilit
            if($rate['price'] == 'N/A'){
                $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
                
                if($rate['price'] == 0.00 || $rate['price'] == "0.00"){

                }else{
                    return Redirect::to('townsend-pay/'.$ticket_number);   
                }
                //$rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
            }
            
        }
        if($checkinData->is_checkout == '1'){
            return Redirect::to('townsend-error-checkout');
        }
        //$this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        return view('townsend.scan-screen', ['data'=>$checkinData]);
    }


    public function confirmCheckout(Request $request){
        
        //$request = $request->all();
        try
        {
            //dd($request->encrypt);
            
        if($request->encrypt){
            $explode = explode(':', $request->encrypt);
            if(count($explode) < 5){
                if(isset($explode[3])){
                    $exp = explode(".",$explode[3]);
                $decrypt = base64_decode(trim($exp[0]));
                }else{
                    return back()->with('danger', "Please scan the right QR code.");
                }
            }else{
                $decrypt = base64_decode($explode[4]);    
            }
            if($decrypt){
                $decrytArray = json_decode($decrypt);
                $gate = Gate::where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();


                $this->setCustomTimezone($decrytArray->facility_id);

                $checkinData = [];
                if($gate && isset($gate->gate_type)){
                    if($gate->gate_type == 'entry'){
                        return back()->with('danger', "Please scan the right QR code.");
                    }
                    $is_checkout = Ticket::where('ticket_number', $request->order_number)->where('is_checkout', '1')->first();
                    if($is_checkout){
                        return Redirect::to('townsend-already-checkout');
                    }


                    
                    
                        $this->checkFacilityAvailable($decrytArray->facility_id);

                        $checkinData = Ticket::with('facility')->where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                        if($checkinData){


                            if($checkinData->anet_transaction_id == '' && $checkinData->check_in_datetime != ''){

                                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                                
                                
                                $diff_in_hours = $arrival_time->diffInRealHours($from);
                                $diff_in_mins = $arrival_time->diffInRealMinutes($from);
                                $diff_in_secs = $arrival_time->diffInSeconds($from);
                    
                                //dd($diff_in_hours,$diff_in_mins,$diff_in_secs);
                                if($diff_in_mins < 60){
                                $diff_in_hours = $diff_in_mins/100;
                                }
                                if($diff_in_mins > 59){
                                $diff_in_hours = number_format($diff_in_mins/60,2);
                                }
                                if($diff_in_secs < 60){
                                $diff_in_hours = .01;
                                }
                    
                                $isMember = 0;
                                $rate = $checkinData->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                                if($rate == false){
                                    return Redirect::to('townsend-error-facility');
                                }
                                
                                if($rate['price'] == 'N/A'){
                                    $rate = $checkinData->facility->rateForReservationOnMarkerTownsend($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
                                    if($rate['price'] == 0.00 || $rate['price'] == "0.00"){
                    
                                    }else{
                                        return Redirect::to('townsend-pay/'.base64_encode($request->order_number));   
                                    }
                                    //$rate['price'] =  $rate['price'] + $checkinData->facility->processing_fee;
                                }else{
                                    if($rate['price'] > 0.00){
                                        return Redirect::to('townsend-pay/'.base64_encode($request->order_number));   
                                    }
                                }
                                
                            }

                            /*$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
                            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                            $diff_in_hours = $arrival_time->diffInRealHours($from);
                            if($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE){
                               $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;     
                            }
                            
                            if(strtotime(date("Y-m-d H:i:s")) > strtotime(date("Y-m-d H:i:s", strtotime($checkinData->estimated_checkout)))){
                                $this->log->info("user have overstay time with ticket number {$checkinData->ticket_number} and will redirect to the overstay payment page with browser {$_SERVER['HTTP_USER_AGENT']}");
                                return redirect('overstay-pay/'.base64_encode($checkinData->ticket_number))->with('danger', 'Your booking time has been exceeded. Please pay the overstay amount.');
                            }*/
                            
                            $checkinData->is_checkout = '1';
                            $checkinData->checkout_gate = $decrytArray->gate;
                            $checkinData->checkout_time = date("Y-m-d H:i:s");
                            $checkinData->checkout_datetime = date("Y-m-d H:i:s");
                            $checkinData->save();
                            if($checkinData){
                                /*$overstayTicket = OverstayTicket::where('ticket_number', $request->order_number)->get();
                                if($overstayTicket){
                                    foreach ($overstayTicket as $key => $value) {
                                        $value->is_checkout = '1';
                                        $value->checkout_gate = $decrytArray->gate;
                                        $value->save();
                                    }
                                }*/
                                $this->log->info("user checkout with ticket number {$checkinData->ticket_number} and go to the thankyou page  with browser {$_SERVER['HTTP_USER_AGENT']}");
                                return Redirect::to('townsend-thankyou-checkout/'.base64_encode($checkinData->ticket_number));
                            }else{
                                return back()->with('danger', 'Please scan valid scan QR code.');
                            }
                        }
                    /*$checkinData = Ticket::where('ticket_number', $request->order_number)->where('gate', $decrytArray->gate)->where('facility_id', $decrytArray->facility_id)->first();*/
                           
                }else{
                    return Redirect::to('error-checkin');
                }
                $checkinData = Ticket::where('ticket_number', $request->order_number)->where('facility_id', $decrytArray->facility_id)->first();   
                if($checkinData){
                    return Redirect::to('thankyou-checkin');
                }else{
                    return Redirect::to('error-checkin');
                }

            }            
         }else{
            return back()->with('danger', 'Please scan valid scan QR code.');
         }
        }catch (Exception $e)
        {
            return back()->with('danger', 'Please scan valid scan QR code.');
            
        }
    }
    

    public function thankyouCheckout($ticket_number){
        $checkinData = Ticket::with(['user','facility'])->where('ticket_number', base64_decode($ticket_number))->where('is_checkin', '1')->where('is_checkout', '1')->first();

        $this->setCustomTimezone($checkinData->facility_id);

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        $checkinData['is_overstay'] = '0';
        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->orderBy('id', 'DESC')->first();
        if($overstay){
          $checkinData['check_in_datetime'] = $overstay->check_in_datetime;
          $checkinData['checkout_datetime'] = $overstay->checkout_datetime;
          $checkinData['length'] = $overstay->length;
          $checkinData['is_overstay'] = '1';
          $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['check_in_datetime']);
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData['checkout_datetime']);
            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
        }
        $this->log->info("user successfully checkout and thankyou page showing with ticket number {$checkinData->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
      return view('townsend.thankyou-checkout', ['data'=>$checkinData]);
    }

    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }


    protected function saveTicket()
    {
        $ticket = Ticket::where('ticket_number', $this->request->ticket_number)->first();
        if(!$ticket){
            return false;
        }

        $ticket->length = $this->request->length;
        $ticket->ticket_security_code = rand(1000, 9999);
        $ticket->total = $this->request->total;
        $ticket->grand_total = $this->request->total;
        $ticket->checkout_datetime = date('Y-m-d H:i:s');
        $ticket->estimated_checkout = date('Y-m-d H:i:s', strtotime("+1 hours"));
        $ticket->save();

        return $ticket;
    }

    


    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = isset($cardData[4])?$cardData[4]:'';
        $request->request->add(
            [
            'name_on_card' => $cardData[0],
            'card_number' => $cardData[1],
            'expiration_date' => $cardData[2],
            'security_code' => $cardData[3],
            'zip_code' => $zipCode
            ]
        );

        $this->request = $request;
    }


    public function makePlanetPayment(Request $request)
    {
        $reference = rand(1000, 9999).rand(1000, 9999).rand(1000, 9999);
        $request->request->add(['ref_id' => $reference]);
        $total = ($request->total)*100;
        //$total = 100;   
        
        $user_id = $this->user->id;
        $partner_id = $this->user->created_by;
        
        
       $validationID = '"'.config('parkengage.TOWNSEND_SECURITY_MERCHANT_ID').'"';
        $validationCode = '"'.config('parkengage.TOWNSEND_SECURITY_VALIDATION_CODE').'"';
        
            if($request->nonce){
                $this->setDecryptedCard($request);	
            }
            $curl = curl_init();
            curl_setopt_array($curl, array(
              CURLOPT_URL => config('parkengage.PLANET_PROFILE_SAVE_URL'),
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_ENCODING => '',
              CURLOPT_MAXREDIRS => 10,
              CURLOPT_TIMEOUT => 0,
              CURLOPT_FOLLOWLOCATION => true,
              CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
              CURLOPT_CUSTOMREQUEST => 'POST',
              CURLOPT_POSTFIELDS =>'{
                  "Request": {
                    "Type": "EftAuthorization",
                    "Version": "W2MXG520",
                      "Credentials": {
                        "ValidationID": '.$validationID.',
                        "ValidationCode": '.$validationCode.',
                        "ValidationCodeHash": null
                      },
                      "Params": {
                          "PaymentOkUrl": "",
                          "CardNumber": "'.$request->card_number.'",
                          "CardExpiryDateMMYY": "'.str_replace('/', '', $request->expiration_date).'",
                          "CardStartDateMMYY": "",
                          "CardIssueNumber": "",
                          "CardCvv2": "'.$request->security_code.'",
                          "CardholderStreetAddress1": "",
                          "CardholderCity": "",
                          "CardholderState": "",
                          "CardholderZipCode": "",
                          "CardholderNameFirst": "",
                          "CardholderNameLast": "",
                          "Amount": "'.$total.'",
                          "Currency": "USD",
                          "RequesterTransRefNum": "TEST AUTH 001",
                          "UserData1": "",
                          "UserData2": "",
                          "UserData3": "",
                          "UserData4": "",
                          "UserData5": "",
                          "OptionFlags": "G"
                        }
                }
            }',
              CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
              ),
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $this->log->info("Response Data Planet (Non Saved Cards): " . json_encode($response));	

            $refundstatus = json_decode($response,TRUE);
            
            if(!in_array($refundstatus["Response"]["Params"]["TxState"],['AP','AA','CQ'])){
               return ['error'=>'payment failed.'];
            }

            $authorized_anet_transaction=new AuthorizeNetTransaction();           
            $authorized_anet_transaction->sent='1';
            $authorized_anet_transaction->user_id=$user_id;
            $authorized_anet_transaction->ip_address=\Request::ip();
            $authorized_anet_transaction->total=$total/100;
            $authorized_anet_transaction->description="Townsend payment: ".$user_id;
            $authorized_anet_transaction->card_type=$refundstatus["Response"]["Params"]["CardSchemeId"];
            $authorized_anet_transaction->ref_id=$this->request->ref_id;
            $authorized_anet_transaction->anet_trans_id=$refundstatus["Response"]["Params"]["TxID"];
            $authorized_anet_transaction->method="card";
            $authorized_anet_transaction->payment_last_four=isset($refundstatus["Response"]["Params"]["CardNumberLast4"]) ? $refundstatus["Response"]["Params"]["CardNumberLast4"] : '0';
            $authorized_anet_transaction->expiration = isset($refundstatus["Response"]["Params"]["CardExpiryDateMMYY"]) ? $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"] : '0';
        
            $authorized_anet_transaction->save();

            /*$authorized_anet_transaction=new AuthorizeNetTransaction();           
            $authorized_anet_transaction->sent='1';
            $authorized_anet_transaction->user_id=$user_id;
            $authorized_anet_transaction->ip_address=\Request::ip();
            $authorized_anet_transaction->total=$total/100;
            $authorized_anet_transaction->description="Townsend payment: ".$user_id;
            $authorized_anet_transaction->card_type="TEST";
            $authorized_anet_transaction->ref_id=$this->request->ref_id;
            $authorized_anet_transaction->anet_trans_id=rand(1000000, 9999999);
            $authorized_anet_transaction->method="card";
            $authorized_anet_transaction->payment_last_four=rand(1000, 9999);
            $authorized_anet_transaction->expiration = rand(1000, 9999);
        
            $authorized_anet_transaction->save();*/
            return $authorized_anet_transaction;
        
    }

   

  }
