<?php

namespace App\Http\Controllers\ParkEngage;

use Mail;
use Crypt;
use Hash;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;
use App\Http\Controllers\Controller;
use App\Models\Ticket;
use App\Models\Facility;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Models\PaymentProfile;
use Artisan;
use App\Exceptions\AuthorizeNetException;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use Twilio\Exceptions\RestException;
use App\Models\AuthorizeNetTransaction;
use App\Models\OauthClient;
use App\Exceptions\ApiGenericException;
use App\Http\Helpers\QueryBuilder;
use Illuminate\Support\Facades\Auth;
use App\Models\ParkEngage\MapcoQrcode;
use App\Models\Event;
use App\Models\TicketDetails;
use App\Models\TicketFailedEvent;

class OfflineTicketController extends Controller
{

    protected $log;
    protected $reference_key;
    protected $logPayment;
    protected $user;
    protected $partnerPaymentDetails;
    protected $authNet;
    protected $facility;
    protected $sendAnet = false;
    protected $anonymousAnet = false;
    protected $paymentProfileId;
    protected $cim;
    protected $request;
    protected $rate;

    const RESERVATION_THRESHOLD_TYPE = 2;
    const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE = 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO = 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR = 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const  MIN_AVAILABILITY  = 5;
    const  LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const  SOME_SPACE_MSG = "Some spots are available.";
    const  No_SPACE_MSG = "Sold Out At This Time.";
    const  NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";
    const ADD_MINUTES_FOR_DECIMAL_LENGTH = 30;
    const CONST_CLOSE_MIN_CHECK = 30;
    const CONST_CLOSEING_SOON_MIN_CHECK1 = 30;
    const EARLY_BIRD_MESSAGE = "You have selected our “Early Bird Special Rates” if you arrive after the post Early Bird time at this garage you will be automatically charged additionally at the correct online rate based on your arrival time. ";
    const CLOSING_SOON_MSG = "Please note that this garage closes very close to your pickup time - see garage hours below. Are you sure you want to book this reservation?";
    const OVERNIGHT_STAY_SOON_MSG = "Please note that this garage is not open during the entirety of your reservation. The garage hours are shown below. You will not be able to drop off or pick up your vehicle while the location is closed. Are you sure you want to book this reservation?";
    const OVERNIGHT_STAY_SOON_MSG_COMBINE = "Please note that this garage closes very close to your pickup time and this garage is not open during the entirety of your reservation. The garage hours are shown below. You will not be able to drop off or pick up your vehicle while the location is closed. Are you sure you want to book this reservation?";
    const CLOSING_SOON_MSG_EMAIL = "Please note that this garage closes very close to your pickup time - please check garage hours.";
    const OVERNIGHT_STAY_SOON_MSG_EMAIL = "Please note that this garage is not open during the entirety of your reservation. Please check garage hours. You will not be able to drop off or pick up your vehicle while the location is closed.";
    const OVERNIGHT_STAY_SOON_MSG_COMBINE_EMAIL = "Please note that this garage closes very close to your pickup time and this garage is not open during the entirety of your reservation. Please check garage hours. You will not be able to drop off or pick up your vehicle while the location is closed.";
    const CLOSING_SOON_ON = 1;
    const  MAX_CHECKIN_HOURS = 24;

    const  GRACE_PERIOD = 1;
    const  FACILITY_ID = 33;
    const  PARTNET_ID = 2980;
    const PROCESSING_FEE = '0.00';

    // Rate Band Exception Array
    const RATE_BAND_EXCEPTION_ID = array(1, 2, 3);
    const PER_HOUR_RATE_BAND = array(1, 2, 3);

    const USERTYPE_BUSINESS = 10;
    const SUPERADMIN = 1;
    const SUBORDINATE = 4;
    const PARTNER = 3;
    const REGIONAL_MANAGER = 12;

    public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
    {
        $this->request = $request;
        //$this->authNet = $authNet;
        //$this->cim = $cim;
        //$this->reference_key = '';
        // Use these validation rules if new billing information is being sent through
        //$this->billingValidation = PaymentProfile::$creditCardValidation;

        $this->log = $logFactory->setPath('logs/parkengage/offline-tickets')->createLogger('offline-tickets');

        //$this->logPayment = $logFactory->setPath('logs/parkengage/townsend-web-payment-logs')->createLogger('townsend-web-payment-logs');
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($partnerTimezone->timezone != '') {
                if ($facility->timezone != '') {
                    date_default_timezone_set($facility->timezone);
                } else if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }

    public function customeReplySms($msg, $phone)
    {

        try {
            // $accountSid = env('TWILIO_ACCOUNT_SID');
            // $authToken  = env('TWILIO_AUTH_TOKEN');
            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
            $client = new Client($accountSid, $authToken);
            try {
                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                    // the number you'd like to send the message to
                    $phone,
                    array(
                        // A Twilio phone number you purchased at twilio.com/console
                        'from' => env('TWILIO_PHONE'),
                        // the body of the text message you'd like to send
                        //'body' => "Fine"
                        'body' => "$msg"
                    )
                );
                $this->log->info("Error message : {$msg} sent to $phone");
                return "true";
            } catch (RestException $e) {
                $this->log->error($e->getMessage());
                return "true";
            }
        } catch (RestException $e) {
            $this->log->error($e->getMessage());
            return "true";
        }
    }

   

    protected function checkTicketNumber()
    {
        $ticket = 'PE' . rand(1000, 9999) . rand(1000, 9999);
        $isExist = Ticket::where('ticket_number', $ticket)->first();
        if ($isExist) {
            // $this->checkTicketNumber();
            $ticket = $this->checkTicketNumber();
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $ticket = $this->checkTicketNumber();
                $isExist = Ticket::where('ticket_number', $ticket)->first();
                if ($isExist) {
                    $this->checkTicketNumber();
                }
            }
        }
        return $ticket;
    }

    public function syncOfflineTickets(Request $request)
    {
        $this->log->info("syncOfflineCheckin request received : " . json_encode($request->all()));
        $tickets = $request->ticketData;
        
        foreach($tickets as $key=>$ticket){
            $this->log->info("syncOfflineCheckin TICKET request received : " . json_encode($ticket));
            try{
                $ticketExist = TicketDetails::where("ref_number", $ticket['ref_number'])->first();
                
                if($ticketExist){
                    continue;
                }
                $facility = Facility::with('facilityConfiguration')->find($ticket['facility_id']);
                
                $this->facility = $facility;
                if (!$facility) {
                    throw new ApiGenericException('Sorry,Invalid garage.');
                }
                $this->setCustomTimezone($ticket['facility_id']);
                
                if ($ticket['phone'] != '') {
                    $this->countryCode = QueryBuilder::appendCountryCode();
                    $existPhone = User::where('phone', $this->countryCode . $ticket['phone'])->where('created_by', $facility->owner_id)->first();
                    if ($existPhone) {
                        if ($ticket['email'] != '') {
                            $existPhone->email = $ticket['email'];
                        }
                        $existPhone->name = $ticket['name'];
                        $existPhone->save();
                        $this->user = $existPhone;
                    } else {
                        $this->user = User::create(
                            [
                                'name' => $ticket['name'],
                                'email' => $ticket['email'],
                                'phone' => $this->countryCode . $ticket['phone'],
                                'password' => Hash::make(str_random(60)),
                                'anon' => true,
                                'user_type' => '5',
                                'created_by' => $facility->owner_id
                            ]
                        );
                    }
                }
                $length = 0;
                $checkinTime = date('Y-m-d H:i:s', strtotime($ticket['arrival']));
                if(isset($ticket['event_id']) && $ticket['event_id'] != ''){
                    $event = Event::find($ticket['event_id']);
                    //->addMinutes($facility->grace_period_minute)
                    if ($event->partner_id == config('parkengage.PARTNER_PCI') && $facility->id == config('parkengage.PCC_FACILITY')) 
                    {
                        $thirtyDaysLater = Carbon::now()->addDays(30);
                        $endTime = Carbon::parse($event->parking_end_time);
                        
                        if ($thirtyDaysLater->lessThan($endTime)) 
                        {
                            $estimated_checkout = $thirtyDaysLater->format('Y-m-d H:i:s');
                        } 
                        else 
                        {
                            $estimated_checkout = Carbon::parse($event->parking_end_time)->format('Y-m-d H:i:s');
                        }
                    } 
                    else 
                    {
                        $estimated_checkout = Carbon::parse($event->parking_end_time)->format('Y-m-d H:i:s');
                    }
                   
                    $length = $event->base_event_hours;
                }else{
                    $length = $ticket['length'];
                    $lengthInMints = QueryBuilder::getLengthInMints($ticket['length']);
                    $estimated_checkout = $from = Carbon::parse($ticket['arrival'])->addMinutes($lengthInMints)->format('Y-m-d H:i:s');

                    if ($this->facility->is_hourly_rate == '1' || $this->facility->is_hourly_rate == 1) {
                        $rate = $this->facility->rateForReservationByPassRateEngine($ticket['arrival'], $length, false, false, null, false, false, '0', 0);
                    } else {
                        $rate = $this->facility->rateForReservationOnMarker($ticket['arrival'], $length, false, false, false, true, false, self::RESERVATION_THRESHOLD_TYPE, 0);
                    }
                }
                if (empty($this->user->id)) {
                    $this->user = (object) [];
                    $this->user->id = 0;
                }
                
            if($ticket['type'] == 'event')
            {
                // Reservation::where("user_id", $this->user->id)->whereNull("anet_transaction_id")->delete();
                // $details = $this->makeReservation($ticket,$this->user->id,$facility,$this->user);
                // if (!empty($ticket['is_cash_payment']) && $ticket['is_cash_payment'] == '1') {
                //     $details->processing_fee = '0.00';
                //     $details->save();
                // }
                // $details->event_id = isset($ticket['event_id']) ? $ticket['event_id'] : NULL;
                // $details->end_timestamp = $estimated_checkout;
                // $details->estimated_checkout_time = $estimated_checkout;
                // $details->event_user_id = Auth::user()->id;
                // $details->device_type   = isset($ticket['device_type']) ? $ticket['device_type'] : NULL;
                // $details->save();

                // $eventDetails = Reservation::with(['facility.facilityConfiguration', 'mapcoQrCode.event', 'user', 'transaction'])->where('id', $details['id'])->first();
               
                // if (isset($eventDetails) && !empty($eventDetails)) 
                // {

                //     $qrcode = isset($eventDetails->mapcoQrCode[0]->qrcode) ? $eventDetails->mapcoQrCode[0]->qrcode : '';
                  
                //     if ($qrcode) 
                //     {
                //         $ticket = $this->validateEventQrCode($qrcode);
                //         //dd($ticket,$ticket->print_reciept_url);
                //         $eventDetails->is_event_scan = '1';
                //         $eventDetails->is_ticket = '1';
                //         $eventDetails->event_scan_at = date('Y-m-d H:i:s');
                //         $eventDetails->event_user_id = $this->user->id;
                //         $eventDetails->save();
                //     }
                // }

                // $data['reservation_id'] = $details['id'];
            }

                $tax = 0;
                $processingFee = 0;
                $ccFee = 0;
                $surchargeFee = 0;
                if(!isset($ticket['is_cash_payment']) || empty($ticket['is_cash_payment'])){
                    $tax = $ticket['tax'];
                    $processingFee = $ticket['processing_fee'];
                    //PIMS-14961 Dev:Sagar
                    $ccFee = $ticket['ccFee'];
                    $surchargeFee = $ticket['surchargeFee'];
                }

               
                $data['user_id'] = isset($this->user->id) ? $this->user->id : '';
                $data['facility_id'] = $ticket['facility_id'];
                $data['is_checkin'] = 1;
                $data['is_checkout'] = 1;
                $data['ticket_number'] = $this->checkTicketNumber();
                $data['check_in_datetime'] = $checkinTime;
                $data['estimated_checkout'] = $estimated_checkout;
                $data['checkout_datetime'] = $estimated_checkout;
                $data['checkout_time'] = $estimated_checkout;
                $data['checkin_time'] = $checkinTime;
                if (isset($this->facility->facilityConfiguration) && ($this->facility->facilityConfiguration->is_sbm_event_qrcode == '1' || $this->facility->facilityConfiguration->is_sbm_event_qrcode == '2')) {
                    $data['ticket_security_code'] = $this->checkQrCode($ticket, $checkinTime, $estimated_checkout);
                } else {
                    $data['ticket_security_code'] = rand(1000, 9999);
                }
                $data['partner_id'] = $facility->owner_id;
                $data['device_type'] = $ticket['device_type'];
                $data['processing_fee'] = $processingFee;
                $data['tax_fee'] = $tax;
                $data['surcharge_fee'] = $surchargeFee; //PIMS-14961 Dev:Sagar
                $data['additional_fee'] = $ccFee; //PIMS-14961 Dev:Sagar
                $data['total'] = $ticket['total'];
                $data['grand_total'] = $ticket['total'];
                $data['parking_amount'] = $ticket['total'] - ($tax + $processingFee + $ccFee + $surchargeFee);
                $data['base_length'] = $length;
                $data['length'] = $length;
                $data['rate_id'] = isset($rate['rate_id']) ? $rate['rate_id'] : null;
                $data['rate_description'] = isset($rate['rate_description']) ? $rate['rate_description'] : null;
                $data['event_user_id'] = Auth::user()->id;
                $data['event_id'] = isset($ticket['event_id']) ? $ticket['event_id'] : null;
                $data['vp_device_checkin'] = 0;
                $data['license_plate'] = isset($ticket['license_plate']) ? $ticket['license_plate'] : null;
                $data['payment_date'] = $checkinTime;
                $this->log->info("driveup data --" . json_encode($data));

                $result = Ticket::create($data);
                if($result){
                    $ticketDetails = new TicketDetails();
                    $ticketDetails->ref_number = $ticket['ref_number'];
                    $ticketDetails->ticket_id = $result->id;
                    $ticketDetails->save();
                }
                if (isset($ticket['payment_details'])) {
                    $this->log->info("payment details request --" . json_encode($ticket['payment_details']));
                    $authorized_anet_transaction = new AuthorizeNetTransaction();
                    $authorized_anet_transaction->sent = '1';
                    $authorized_anet_transaction->user_id = $result->user_id;
                    $authorized_anet_transaction->total = $ticket['payment_details']['TransactionAmount'];
                    $authorized_anet_transaction->name = $ticket['payment_details']['MerchantName'];
                    $authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $result->id;
                    $authorized_anet_transaction->response_message = $ticket['payment_details']['ProcessorMessage'];
                    $authorized_anet_transaction->expiration = $ticket['payment_details']['expiry'];
                    $authorized_anet_transaction->card_type = isset($ticket['payment_details']['CardType']) ? $ticket['payment_details']['CardType'] : 'NA';
                    $authorized_anet_transaction->ref_id = isset($ticket['payment_details']['RequesterTransRefNum']) ? $ticket['payment_details']['RequesterTransRefNum'] : '';
                    $authorized_anet_transaction->anet_trans_id = $ticket['payment_details']['TransactionID'];
                    $authorized_anet_transaction->payment_last_four = substr($ticket['payment_details']['MaskedPAN'], -4);
                    $authorized_anet_transaction->method = "card";
                    $authorized_anet_transaction->reader_used = $ticket['payment_details']['ReaderUsed'];
                    $authorized_anet_transaction->anet_trans_hash = isset($ticket['payment_details']['processorReference']) ? $ticket['payment_details']['processorReference'] : '';
                    $authorized_anet_transaction->status_code = isset($ticket['payment_details']['StatusCode']) ? $ticket['payment_details']['StatusCode'] : '';
                    $authorized_anet_transaction->status_type = isset($ticket['payment_details']['StatusType']) ? $ticket['payment_details']['StatusType'] : '';
                    $authorized_anet_transaction->status_message = isset($ticket['payment_details']['StatusMessage']) ? $ticket['payment_details']['StatusMessage'] : '';
                    $authorized_anet_transaction->name = isset($ticket['payment_details']['CardHolderName']) ? $ticket['payment_details']['CardHolderName'] : '';
                    $authorized_anet_transaction->save();

                    $result->cardholder_name = isset($ticket['payment_details']['CardHolderName']) ? $ticket['payment_details']['CardHolderName'] : '';
                    $result->card_last_four = substr($ticket['payment_details']['MaskedPAN'], -4);
                    $result->expiry = $ticket['payment_details']['expiry'];
                    $result->card_type = $ticket['payment_details']['CardType'];
                    $result->anet_transaction_id = $authorized_anet_transaction->id;
                    $result->session_id = isset($ticket['payment_details']['token']) ? $ticket['payment_details']['token'] : '';
                    $result->payment_token = isset($ticket['payment_details']['token']) ? $ticket['payment_details']['token'] : '';
                    $result->save();
                } else if (isset($ticket['is_cash_payment']) &&  $ticket['is_cash_payment'] == '1') {
                    $result->is_offline_payment = $ticket['is_cash_payment'];
                    $result->remark = $ticket['payment_comment'];
                    $result->save();
                }

                $this->log->info("data save : ".  json_encode($result));
            }catch(\Exception $e){
                $this->log->info("Exception : ".  $e);
                $faileddata['deviceID'] = isset($request->deviceID) ? $request->deviceID : '';
                $faileddata['facility_id'] = isset($ticket['facility_id']) ? $ticket['facility_id'] : '';
                $faileddata['arrival'] = isset($ticket['arrival']) ? $ticket['arrival'] : '';;
                $faileddata['device_type'] = isset($ticket['device_type']) ? $ticket['device_type'] : '';
                $faileddata['processing_fee'] = isset($ticket['processing_fee']) ? $ticket['processing_fee'] : '';
                $faileddata['tax'] = isset($ticket['tax']) ? $ticket['tax'] : '';
                $faileddata['total'] = isset($ticket['total']) ? $ticket['total'] : '';
                //$faileddata['length'] = $length;
                $faileddata['event_id'] = isset($ticket['event_id']) ? $ticket['event_id'] : null;
                $faileddata['event_id'] = isset($ticket['event_id']) ? $ticket['event_id'] : null;
                $faileddata['payment_comment'] = isset($ticket['payment_comment']) ? $ticket['payment_comment'] : null;
                $faileddata['payment_details'] = isset($ticket['payment_details']) ? json_encode($ticket['payment_details']) : null;
                $faileddata['error_message'] = $e->getLine()."--".$e->getMessage();
                $faileddata['request'] = isset($ticket['payment_details']) ? json_encode($ticket['payment_details']) : null;
                $faileddata['ref_number'] = isset($ticket['ref_number']) ? $ticket['ref_number'] : null;
                $faileddata['is_cash_payment'] = isset($ticket['is_cash_payment']) ? $ticket['is_cash_payment'] : null;
                ///$faileddata['license_plate'] = isset($ticket['license_plate']) ? $ticket['license_plate'] : null;
                TicketFailedEvent::create($faileddata);
                // return($e->getMessage().'line no:'.$e->getLine());
            }
        }
        return "Data synced successfully";
    }

    protected function checkQrCode($ticket, $checkinTime, $estimated_checkout)
    {
        $code = 'ET' . rand(10, 99) . rand(100, 999) . rand(100, 999);
        $isExist = MapcoQrcode::where('qrcode', $code)->first();
        if ($isExist) {
            $this->checkQrCode($ticket, $checkinTime, $estimated_checkout);
        }
        if (isset($this->facility->facilityConfiguration) && $this->facility->facilityConfiguration->is_sbm_event_qrcode == '1') {
            // $parking_facility_identifier =  $this->facility->garage_code;
            // $parking_facility_identifier = config('parkengage.Facility_ID');
            // $product_identifier = config('parkengage.PRODUCT_IDENTIFIER');
            $parking_facility_identifier = $this->facility->facilityConfiguration->product_identifier_id;
            $product_identifier = $this->facility->facilityConfiguration->product_identifier;
            $valid_from = date('m') . date('d') . date('y') . '0000';
            $valid_to =   date('d') . date('m') . date('y') . '2359';
            $unique_qr_code_identifier = rand(10, 99) . rand(100, 999) . rand(100, 999);
            $updated_code = $parking_facility_identifier . $product_identifier . $valid_from . $valid_to . $unique_qr_code_identifier;
            $this->log->info("is_sbm_event_qrcode 1 Event Updated QRCode : " . $updated_code);
            return $updated_code;
        }
        if (isset($this->facility->facilityConfiguration) && $this->facility->facilityConfiguration->is_sbm_event_qrcode == '2') {
            // $parking_facility_identifier =  $this->facility->garage_code;
            $parking_facility_identifier = $this->facility->facilityConfiguration->product_identifier_id;
            $product_identifier = $this->facility->facilityConfiguration->product_identifier;
            $valid_from = date('m', strtotime($checkinTime)) . date('d', strtotime($checkinTime)) . date('y', strtotime($checkinTime)) . date('Hi', strtotime($checkinTime));
            $valid_to =   date('m', strtotime($estimated_checkout)) . date('d', strtotime($estimated_checkout)) . date('y', strtotime($estimated_checkout)) . date('Hi', strtotime($estimated_checkout));
            $unique_qr_code_identifier = rand(10, 99) . rand(100, 999) . rand(10, 99);
            $amount = (int) 100 * $ticket['total'];
            $updated_code = $parking_facility_identifier . $product_identifier . $valid_from . $amount . $valid_to . $unique_qr_code_identifier;
            $this->log->info("is_sbm_event_qrcode 2 Event Updated QRCode : " . $updated_code);
            return $updated_code;
        }
        return $code;
    }

    public function getTicketDetails(Request $request){
        $ticket = TicketDetails::with(['ticket.user','ticket.transaction'])->where("ref_number", $request->ref_number)->first();
        if(isset($ticket->ticket->grand_total)){
            $ticket->ticket->amount_paid = $ticket->ticket->grand_total;
            $ticket->ticket->payable_amount = "0.00";
        }
        $data['ticket'] = $ticket;
        $data['facility'] = Facility::with("geolocations")->select("id", "full_name", "short_name")->where("id", $request->facility_id)->first();
        return $data;
    }
}
