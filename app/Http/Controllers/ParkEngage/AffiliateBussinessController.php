<?php

namespace App\Http\Controllers\ParkEngage;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use App\Http\Requests;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\AffiliateBusiness;
use App\Models\BusinessType;
use App\Models\BusinessFacilityPolicy;
use App\Models\BusinessPolicy;
use App\Exceptions\ApiGenericException;
use App\Models\User;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\UserFacility;
use App\Exceptions\UserNotAuthorized;
use App\Models\PolicyDay;
use App\Models\Ticket;
use App\Models\Facility;
use App\Models\ParkEngage\UserValidateMaster;
use App\Http\Helpers\QueryBuilder;
use App\Services\LoggerFactory;
use App\Models\BusinessQrCode;
use App\Services\Pdf;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\BrandSetting;
use App\Http\Controllers\ParkEngage\SubordinateController;
use Illuminate\Support\Facades\Auth;
use App\Classes\CommonFunctions;
use App\PaymentGateways\PaymentGatewayFactory;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\UserMembership;
use App\Models\ParkEngage\SubPartnerPermission;
use App\Models\ParkEngage\MembershipPlan;
use App\Models\SuperSet\PartnerMappedDashboardList;

class AffiliateBussinessController extends Controller
{

	const USERTYPE = 10;
	const BUSINESS_CLERK = 8;
	const SUPERADMIN = 1;
	const RESERVATION_THRESHOLD_TYPE = 2;
	const PASSWORD_PATTERN = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%&*()';
	const SUBORDINATE = 4;
	const PARTNER = 3;
	const REGIONAL_MANAGER = 12;


	const TRIAL_PLAN_DAYS = 15;
	const MONTHLY_PLAN_DAYS = 30;
	const ANNUAL_PLAN_DAYS = 365;

	public $log;
	public $subordinate;
	protected $countryCode;
	protected $licensePlateLog;
	public $request;

	public function __construct(Request $request, LoggerFactory $logFactory, SubordinateController $subordinate)
	{
		$this->subordinate = $subordinate;
		$this->request = $request;
		$this->log = $logFactory->setPath('logs/parkengage/affiliate-business')->createLogger('afffiliate-business');
	}


	public function index(Request $request)
	{
		if (!Auth::user()) {
			throw new UserNotAuthorized("Invalid User!");
		}
		if (Auth::user()->status == '0') {
			throw new UserNotAuthorized("User is inactive. Please contact to admin.");
		}
		if (Auth::user()->user_type == self::USERTYPE) {
			$partner_id = Auth::user()->created_by;
			$business_id = Auth::user()->business_id;
			$result = AffiliateBusiness::with(['BusinessFacilityPolicy.policies'])->where('partner_id', $partner_id)->where('id', $business_id);
		} else if (Auth::user()->user_type == self::SUBORDINATE) {
			$partner_id = Auth::user()->created_by;
			$rm_id = Auth::user()->user_parent_id;
			$facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
			$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');
			$result = AffiliateBusiness::with(['BusinessFacilityPolicy.policies'])->whereIn('status', [0, 1]);
			if ($partner_id) {
				$result = $result->where('partner_id', $partner_id);
			}
			if ($rm_id) {
				$result = $result->where('rm_id', $rm_id);
			}
		} else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {

			$partner_id = Auth::user()->created_by;
			$rm_id = Auth::user()->id;
			$facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
			$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');

			//	$result = AffiliateBusiness::with(['BusinessFacilityPolicy.policies'])->whereIn('status', [0, 1])->where('rm_id', $rm_id);

			$result = AffiliateBusiness::with(['BusinessFacilityPolicy.policies'])
				->whereIn('status', [0, 1])
				->where(function ($query) use ($rm_id) {
					$query->whereNull('rm_id')
						->orWhere('rm_id', $rm_id);
				});

			if ($partner_id) {
				$result = $result->where('partner_id', $partner_id);
			}
		} else if (Auth::user()->user_type == self::SUPERADMIN) {
			$partner_id = $request->partner_id;
			$result = AffiliateBusiness::with(['BusinessFacilityPolicy.policies'])->whereIn('status', [0, 1]);
			if ($partner_id) {
				$result = $result->where('partner_id', $partner_id);
			}
			$rm_id = $request->rm_id;

			if ($rm_id) {
				$facilities = DB::table('user_facilities')->where('user_id', $rm_id)->whereNull('deleted_at')->pluck('facility_id');
				if ($facilities) {
					$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');
					$result = $result->where('rm_id', $rm_id);
				} else {
					throw new ApiGenericException("Facility is not mapped with RM");
				}
			}
		} else {
			$partner_id = Auth::user()->id;
			$result = AffiliateBusiness::with(['BusinessFacilityPolicy.policies'])->where('partner_id', $partner_id);
			$rm_id = $request->rm_id;
			if ($rm_id) {
				$facilities = DB::table('user_facilities')->where('user_id', $rm_id)->whereNull('deleted_at')->pluck('facility_id');
				if ($facilities) {
					$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');
					$result = $result->where('rm_id', $rm_id);
				} else {
					throw new ApiGenericException("Facility is not mapped with RM");
				}
			}
		}


		if ($request->search) {
			//$result = $result->Where('business_name', 'like', '%' . trim($request->search) . '%')->orWhere('owner_name', 'like', '%' . trim($request->search) . '%')->orWhere('email', 'like', '%' . trim($request->search) . '%')->orWhere('phone', 'like', '%' . trim($request->search) . '%');
			$result = $result->where('partner_id', $partner_id)
				->where(function ($query) use ($request) {
					$search = trim($request->search);
					$query->where('business_name', 'like', "%$search%")
						->orWhere('owner_name', 'like', "%$search%")
						->orWhere('email', 'like', "%$search%")
						->orWhere('phone', 'like', "%$search%");
				});
		}
		if ($request->status) {
			$resultData = BusinessFacilityPolicy::pluck('business_id');
			$data = DB::table('affiliate_business')->select('id', 'business_name', 'owner_name', 'email', 'phone', 'business_type')->where('status', 1)->whereNull("deleted_at");
			if (Auth::user()->user_type == self::USERTYPE) {
				$data = $data->where('id', Auth::user()->business_id);
			} else if (Auth::user()->user_type == self::SUPERADMIN) {
				$partner_id = $request->partner_id;
				if ($partner_id) {
					$data = $data->where('partner_id', $partner_id);
				}
				$rm_id = $request->rm_id;
				if ($rm_id) {
					$facilities = DB::table('user_facilities')->where('user_id', $rm_id)->whereNull('deleted_at')->pluck('facility_id');
					if ($facilities) {
						if ($request->facility_id) {
							$business_id = DB::table('business_facility_policy')->where('facility_id', $request->facility_id)->pluck('business_id');
						} else {
							$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');
						}
						$data = $data->whereIn('id', $business_id);
					} else {
						throw new ApiGenericException("Facility is not mapped with RM");
					}
				} else {
					if ($request->facility_id) {
						$business_id = DB::table('business_facility_policy')->where('facility_id', $request->facility_id)->pluck('business_id');
						$data = $data->whereIn('id', $business_id);
					}
				}
			} else if (Auth::user()->user_type == self::SUBORDINATE) {
				$partner_id = Auth::user()->created_by;
				if ($partner_id) {
					$data = $data->where('partner_id', $partner_id);
				}
				$facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
				if ($request->facility_id) {
					$business_id = DB::table('business_facility_policy')->where('facility_id', $request->facility_id)->pluck('business_id');
				} else {
					$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');
				}
				$data = $data->whereIn('id', $business_id);
			} else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
				$partner_id = Auth::user()->created_by;
				if ($partner_id) {
					$data = $data->where('partner_id', $partner_id);
				}
				$facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
				if ($request->facility_id) {
					$business_id = DB::table('business_facility_policy')->where('facility_id', $request->facility_id)->pluck('business_id');
				} else {
					$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');
				}
				$data = $data->whereIn('id', $business_id);
			} else {
				$partner_id = $request->partner_id;
				if ($partner_id) {
					$data = $data->where('partner_id', $partner_id);
				}
				$rm_id = $request->rm_id;
				if ($rm_id) {
					$facilities = DB::table('user_facilities')->where('user_id', $rm_id)->whereNull('deleted_at')->pluck('facility_id');
					if ($facilities) {
						$business_id = DB::table('business_facility_policy')->whereIn('facility_id', $facilities)->pluck('business_id');
						$data = $data->whereIn('id', $business_id);
					} else {
						throw new ApiGenericException("Facility is not mapped with RM");
					}
				} else {
					if ($request->facility_id) {
						$business_id = DB::table('business_facility_policy')->where('facility_id', $request->facility_id)->pluck('business_id');
						$data = $data->whereIn('id', $business_id);
					} else {
						$data = $data->whereIn('id', $resultData);
					}
				}
			}
			// if url get page type then pagination number 
			if ($request->has('page')) {
				$data = $data->paginate(10);
			} else {
				$data = $data->get();
			}
			if ($data) {
				return $data;
			} else {
				throw new NotFoundException('Sorry! No Record Found');
			}
		}

		if ($request->sort != '') {
			$result = $result->orderBy($request->sort, $request->sortBy);
		} else {
			$result = $result->orderBy("id", "DESC");
		}
		$result = $result->paginate(10);

		return $result;
	}

	public function store(Request $request)
	{
		$this->log->error('Create Business ' . json_encode($request->all()));
		DB::beginTransaction();

		if ($request->file('logo') != '') {
			$this->validate($request, ['logo' => 'mimes:jpeg,png,jpg,svg|dimensions:min_width=55,min_height=55', 'banner' => 'mimes:jpeg,png,jpg,svg'], ['logo.mimes' => 'Brand logo must be a file of type : .jpeg, .png, .jpg', 'logo.dimensions' => 'Brand logo must be at least 55 x 55 pixels.', 'banner.mimes' => 'Brand banner must be a file of type : .jpeg, .png, .jpg']);
		} elseif ($request->file('banner') != '') {
			$this->validate($request, ['logo' => 'mimes:jpeg,png,jpg,svg', 'banner' => 'mimes:jpeg,png,jpg,svg|dimensions:min_width=1462,min_height=242'], ['logo.mimes' => 'Brand logo must be a file of type : .jpeg, .png, .jpg', 'banner.mimes' => 'Brand banner must be a file of type : .jpeg, .png, .jpg', 'banner.dimensions' => 'Brand banner must be at least 1462 x 242 pixels.']);
		} elseif ($request->file('favicon') != '') {
			$this->validate($request, ['logo' => 'mimes:jpeg,png,jpg,svg|dimensions:min_width=55,min_height=55', 'banner' => 'mimes:jpeg,png,jpg,svg'], ['logo.mimes' => 'Brand logo must be a file of type : .jpeg, .png, .jpg', 'logo.dimensions' => 'Brand logo must be at least 55 x 55 pixels.', 'banner.mimes' => 'Brand banner must be a file of type : .jpeg, .png, .jpg']);
		} else {
		}

		try {
			if (Auth::user()->user_type == self::SUPERADMIN) {
				$partner_id = $request->partner_id;
				$rm_id = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
				$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
			} else if (Auth::user()->user_type == self::SUBORDINATE) {
				$partner_id = Auth::user()->created_by;
				$rm_id = Auth::user()->user_parent_id;
				$subordinate_id = Auth::user()->id;
			} else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
				$partner_id = Auth::user()->created_by;
				$rm_id = Auth::user()->id;
				$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
			} else {
				$partner_id = Auth::user()->id;
				$rm_id = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
				$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
			}

			$dataCheck = AffiliateBusiness::where('business_name', $request->business_name)->where('partner_id', $partner_id)->first();
			if ($dataCheck) {
				throw new ApiGenericException("Business name already added.");
			}
			$emailExist = AffiliateBusiness::where('email', $request->email)->where('partner_id', $partner_id)->first();
			if ($emailExist) {
				throw new ApiGenericException("Email is already exist.");
			}
			$email = User::whereNull('deleted_at')->where('email', $request->email)->first();
			if ($email) {
				throw new ApiGenericException("Email already exist.");
			}

			if ($request->file('logo')) {
				$logo = $request->file('logo');
				$file_extension = $logo->getClientOriginalExtension();
				$file_name = Auth::user()->id . '_' . date('YmdHis') . '_logo.' . $file_extension;
				$destination_path = storage_path("app/business-brand-settings");
				if (!$logo->move($destination_path, $file_name)) {
					throw new ApiGenericException("Something went wrong while importing users sheet");
				}
				$data['logo'] = $file_name;
			}
			if ($request->file('banner')) {
				$banner = $request->file('banner');
				$file_extension = $banner->getClientOriginalExtension();
				$file_name = Auth::user()->id . '_' . date('YmdHis') . '_banner.' . $file_extension;
				$destination_path = storage_path("app/business-brand-settings");
				if (!$banner->move($destination_path, $file_name)) {
					throw new ApiGenericException("Something went wrong while importing users sheet");
				}
				$data['banner'] = $file_name;
			}
			if ($request->file('favicon')) {
				$favicon = $request->file('favicon');
				$file_extension = $favicon->getClientOriginalExtension();
				$file_name = Auth::user()->id . '_' . date('YmdHis') . '_favicon.' . $file_extension;
				$destination_path = storage_path("app/business-brand-settings");
				if (!$favicon->move($destination_path, $file_name)) {
					throw new ApiGenericException("Something went wrong while importing users sheet");
				}
				$data['favicon'] = $file_name;
			}

			$data['color'] = $request->color;
			$data['rgb_color'] = $request->rgb_color;
			$data['secondary_color'] = $request->secondary_color;
			$data['secondary_rgb_color'] = $request->secondary_rgb_color;


			$data['business_name'] = $request->business_name;
			$data['owner_name'] = $request->owner_name;
			$data['email'] = $request->email;
			$data['phone'] = isset($request->phone) ? $request->phone : '';
			$data['web_url'] = isset($request->web_url) ? $request->web_url : '';
			$data['address'] = isset($request->address) ? $request->address : '';
			$data['address2'] = isset($request->address2) ? $request->address2 : '';
			$data['city'] = isset($request->city) ? $request->city : '';
			$data['state'] = isset($request->state) ? $request->state : '';
			$data['country'] = isset($request->country) ? $request->country : '';
			$data['pincode'] = isset($request->zip_code) ? $request->zip_code : '';
			$data['partner_id'] = $partner_id;
			$data['created_by'] = Auth::user()->id;
			$data['rm_id'] = $rm_id;
			$data['subordinate_id'] = $subordinate_id;
			$data['status'] = $request->status;
			$data['business_type'] = isset($request->business_type) ? $request->business_type : '0';
			$result = AffiliateBusiness::create($data);

			if (!$result) {
				throw new ApiGenericException("Record Not Added");
			}

			if ($request->facilities) {
				foreach (json_decode($request->facilities) as $rec) {

					$policyArray = explode(',', $rec->policies);

					foreach ($policyArray as $val) {
						$mappingData['business_id'] = $result->id;
						$mappingData['facility_id'] = $rec->id;
						$mappingData['policy_id'] = $val;
						BusinessFacilityPolicy::create($mappingData);
					}
				}
			}

			// create user and login
			//$random_password = rand(1111,99999);
			$random_password = substr(str_shuffle(self::PASSWORD_PATTERN), 0, 8);

			// Get country Code
			$this->countryCode = QueryBuilder::appendCountryCode();

			$data['name'] = $request->owner_name;
			$data['email'] = $request->email;
			$data['phone'] = $this->countryCode . $request->phone;
			$data['password'] = Hash::make($random_password);
			$data['anon'] = false;
			$data['user_type'] = self::USERTYPE;
			$data['created_by'] = $partner_id;
			$data['business_id'] = $result->id;
			$data['status'] = $request->status;
			$data['user_parent_id'] = $rm_id;
			$data['subordinate_id'] = $subordinate_id;
			$resultUser = User::create($data);

			if (!$result) {
				throw new ApiGenericException("Record Not Added");
			}
			DB::table('role_user')->insert([
				'user_id' => $resultUser->id,
				'role_id' => '10'
			]);
			if ($request->facilities) {
				foreach (json_decode($request->facilities) as $rec) {
					DB::table('user_facilities')->insert([
						'user_id' => $resultUser->id,
						'facility_id' => $rec->id
					]);
				}
			}


			$permission = $request->permission;
			if ($permission) {
				$membershipPlan = UserMembership::where('user_id', $partner_id)->first();
				if ($membershipPlan) {
					$this->request = $request;
					$this->attachMembershipPlanToUser($resultUser, $membershipPlan->membership_plan_id);
				}
				foreach (json_decode($permission) as $key => $value) {

					if ($membershipPlan) {
						$memberships['membership_plan_id'] =  $membershipPlan->membership_plan_id;
					} else {
						$memberships['membership_plan_id'] = $request->membership_plan_id;
					}
					$memberships['partner_id'] = $partner_id;
					$memberships['web'] = $value->web;
					$memberships['display_name'] = $value->display_name;
					$memberships['parent_id'] = isset($value->parent_id) ? $value->parent_id : NULL;
					$memberships['user_id'] = $resultUser->id;
					$memberships['type'] = $value->type;
					$memberships['list_order'] = isset($value->list_order) ? $value->list_order : 0;

					SubPartnerPermission::create($memberships);
				}
			}

			Artisan::queue('businessemail:email', ['id' => $resultUser->id, 'password' => $random_password]);
			DB::commit();
			$result->user = $resultUser;
			return $result;
		} catch (Exception $e) {
			DB::rollback();
			throw new ApiGenericException($e->getMessage());
		}
	}


	public function show($id)
	{
		if (!Auth::user()) {
			throw new UserNotAuthorized("Invalid User!");
		}
		if (Auth::user()->status == '0') {
			throw new UserNotAuthorized("User is inactive. Please contact to admin.");
		}
		if (isset($request->partner_id)) {
			$partner_id = $request->partner_id;
		} else {
			if (Auth::user()->user_type == '1') {
			} else {
				if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
					$partner_id = Auth::user()->created_by;
				} else if ((Auth::user()->user_type == '3')) {
					$partner_id = Auth::user()->id;
				} else {
					$partner_id = Auth::user()->created_by;
				}
			}
		}
		if (Auth::user()->user_type == '1') {
			$result = AffiliateBusiness::where('id', $id)->first();
		} else {
			$result = AffiliateBusiness::where('id', $id)->where('partner_id', $partner_id)->first();
		}


		if (!$result) {
			throw new ApiGenericException("Record Not Found.");
		}
		$businessExist = User::where('user_type', SELF::BUSINESS_CLERK)->where('business_id', $result->id)->pluck('id');


		$resultData = BusinessFacilityPolicy::with(['facilities'])->join('business_policy', 'business_facility_policy.policy_id', '=', 'business_policy.id')->where('business_id', $id)->select('business_id', 'facility_id', DB::raw('group_concat(business_policy.policy_name) as policy_name'), DB::raw('group_concat(business_facility_policy.policy_id) as policy_id'))->groupBy('business_facility_policy.facility_id')->get();

		$record = [];
		foreach ($resultData as $key => $val) {

			$record[$key]['id'] = $val->facilities['id'];
			$record[$key]['full_name'] = $val->facilities['full_name'];
			$record[$key]['commaSepPolicies'] = $val->policy_name;
			$record[$key]['policies'] = $val->policy_id;
		}
		$businessExist = User::where('user_type', SELF::USERTYPE)->where('business_id', $result->id)->pluck('id');
		$permission = SubPartnerPermission::whereIn('user_id', $businessExist)->get();
		$result->facilities = json_encode($record);
		$result->permission = $permission;
		$result['is_clerk'] = (count($businessExist) > 0) ? 1 : 0;

		//assign dashboard permission | DATA-28 | Dev:Sagar
		$parentId = !empty($result->subordinate_id) ? $result->subordinate_id
           : (!empty($result->rm_id) ? $result->rm_id
           : $result->partner_id);
		
		$getDashboardPermissions = PartnerMappedDashboardList::join('da_dashboards', 'da_dashboards.id', '=', 'partner_mapped_dashboard_list.dashboard_id')
			->select('da_dashboards.id', 'partner_mapped_dashboard_list.partner_id', 'partner_mapped_dashboard_list.dashboard_id', 'da_dashboards.name')
			->where('partner_mapped_dashboard_list.partner_id', $parentId)
			->get();
		
		$result->dashboard_permissions = $getDashboardPermissions;

		$getDashboardData = PartnerMappedDashboardList::join('da_dashboards', 'da_dashboards.id', '=', 'partner_mapped_dashboard_list.dashboard_id')
			->select('da_dashboards.id', 'partner_mapped_dashboard_list.partner_id', 'partner_mapped_dashboard_list.dashboard_id', 'da_dashboards.name', 'partner_mapped_dashboard_list.id as mapped_id')
			->where('partner_mapped_dashboard_list.partner_id', $parentId)
			->get();

		$result->selected_dashboard_permissions = $getDashboardData;

		return $result;
	}

	public function update(Request $request)
	{
		$this->log->error('Update Business ' . json_encode($request->all()));
		$result = AffiliateBusiness::find($request->id);
		//$partner_id = Auth::user()->id;
		if (Auth::user()->user_type == self::SUPERADMIN) {
			$partner_id = $request->partner_id;
			$rm_id = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		} else if (Auth::user()->user_type == self::SUBORDINATE) {
			$partner_id = Auth::user()->created_by;
			$rm_id = Auth::user()->user_parent_id;
			$subordinate_id = Auth::user()->id;
		} else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
			$partner_id = Auth::user()->created_by;
			$rm_id = Auth::user()->id;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		} else {
			$partner_id = Auth::user()->id;
			$rm_id = (isset($request->rm_id) && !empty($request->rm_id)) ? $request->rm_id : NULL;
			$subordinate_id = (isset($request->subordinate_id) && !empty($request->subordinate_id)) ? $request->subordinate_id : NULL;
		}
		if (!$result) {
			throw new ApiGenericException("Record Not Found.");
		}

		if ($result->business_name != $request->business_name) {
			$businessExist = AffiliateBusiness::where('business_name', $request->business_name)->where('partner_id', $partner_id)->first();
			if ($businessExist) {
				throw new ApiGenericException("Business name already added.");
			}
		}

		if ($result->email != $request->email) {
			$emailExist = AffiliateBusiness::where('email', $request->email)->where('partner_id', $partner_id)->first();
			if ($emailExist) {
				throw new ApiGenericException("Email is already exist.");
			}
		}

		$checkUserEmail = User::where('email', $result->email)->where('user_type', self::USERTYPE)->first();

		if (!$checkUserEmail) {
			throw new ApiGenericException("User email not Found.");
		}

		// for business brand details
		if ($request->file('logo')) {
			if ($result->logo != '') {
				$file_path = storage_path("app/business-brand-settings/" . $result->logo);
				if (is_file($file_path)) {
					unlink($file_path);
				}
			}


			$logo = $request->file('logo');
			$file_extension = $logo->getClientOriginalExtension();
			$file_name = Auth::user()->id . '_' . date('YmdHis') . '_logo.' . $file_extension;
			$destination_path = storage_path("app/business-brand-settings");
			if (!$logo->move($destination_path, $file_name)) {
				throw new ApiGenericException("Something went wrong while importing users sheet");
			}
			$result->logo = $file_name;
		}
		if ($request->file('banner')) {

			if ($result->banner != '') {
				$file_path = storage_path("app/business-brand-settings/" . $result->banner);
				if (is_file($file_path)) {
					unlink($file_path);
				}
			}

			$banner = $request->file('banner');
			$file_extension = $banner->getClientOriginalExtension();
			$file_name = Auth::user()->id . '_' . date('YmdHis') . '_banner.' . $file_extension;
			$destination_path = storage_path("app/business-brand-settings");
			if (!$banner->move($destination_path, $file_name)) {
				throw new ApiGenericException("Something went wrong while importing users sheet");
			}
			$result->banner = $file_name;
		}
		if ($request->banner_remove == '1') {

			if ($result->banner != '') {
				$file_path = storage_path("app/business-brand-settings/" . $result->banner);
				if (is_file($file_path)) {
					unlink($file_path);
				}
			}
			$result->banner = '';
		}
		if ($request->file('favicon')) {

			if ($result->favicon != '') {
				$file_path = storage_path("app/business-brand-settings/" . $result->favicon);
				if (is_file($file_path)) {
					unlink($file_path);
				}
			}

			$favicon = $request->file('favicon');
			$file_extension = $favicon->getClientOriginalExtension();
			$file_name = Auth::user()->id . '_' . date('YmdHis') . '_favicon.' . $file_extension;
			$destination_path = storage_path("app/business-brand-settings");
			if (!$favicon->move($destination_path, $file_name)) {
				throw new ApiGenericException("Something went wrong while importing users sheet");
			}
			$result->favicon = $file_name;
		}

		$result->color = $request->color;
		$result->rgb_color = $request->rgb_color;

		$result->secondary_color = $request->secondary_color;
		$result->secondary_rgb_color = $request->secondary_rgb_color;

		$result->business_name = $request->business_name;
		$result->owner_name = $request->owner_name;
		$result->email = $request->email;
		$result->phone = isset($request->phone) ? $request->phone : '';
		$result->web_url = isset($request->web_url) ? $request->web_url : '';
		$result->address = isset($request->address) ? $request->address : '';
		$result->address2 = isset($request->address2) ? $request->address2 : '';
		$result->city = isset($request->city) ? $request->city : '';
		$result->state = isset($request->state) ? $request->state : '';
		$result->country = isset($request->country) ? $request->country : '';
		$result->pincode = isset($request->zip_code) ? $request->zip_code : '';
		$result->partner_id = $partner_id;
		$result->status = $request->status;
		$result->rm_id = $rm_id;
		$result->subordinate_id = $subordinate_id;
		$result->business_type = isset($request->business_type) ? $request->business_type : '0';
		$result->save();


		if ($request->facilities) {
			$users = User::selectRaw('GROUP_CONCAT(id) as user_ids')->where('business_id', $request->id)->first();
			//$users = User::where('business_id',$request->id)->where('user_type',self::BUSINESS_CLERK)->pluck('id');

			if ($users) {
				$facility = UserFacility::selectRaw('GROUP_CONCAT(facility_id) as facility_ids')->wherein('user_id', [$users->user_ids])->first();
			}

			$resultData = BusinessFacilityPolicy::where('business_id', $request->id)->delete();

			foreach (json_decode($request->facilities) as $rec) {
				$policyArray = explode(',', $rec->policies);

				foreach ($policyArray as $val) {
					$mappingData['business_id'] = $result->id;
					$mappingData['facility_id'] = $rec->id;
					$mappingData['policy_id'] = $val;
					BusinessFacilityPolicy::create($mappingData);
				}
			}
			UserFacility::where('user_id', $checkUserEmail->id)->delete();
			if ($request->facilities) {
				foreach (json_decode($request->facilities) as $rec) {
					DB::table('user_facilities')->insert([
						'user_id' => $checkUserEmail->id,
						'facility_id' => $rec->id
					]);
				}
			}
		} else {
			$resultData = BusinessFacilityPolicy::where('business_id', $request->id)->delete();
		}

		// Get country Code
		$this->countryCode = QueryBuilder::appendCountryCode();

		$checkUserEmail->name = $request->owner_name;
		$checkUserEmail->email = $request->email;
		$checkUserEmail->phone = $this->countryCode . $request->phone;
		$checkUserEmail->anon = false;
		$checkUserEmail->user_type = self::USERTYPE;
		$checkUserEmail->created_by = $partner_id;
		$checkUserEmail->business_id = $result->id;
		$checkUserEmail->status = $request->status;
		$checkUserEmail->user_parent_id = $rm_id;
		$checkUserEmail->subordinate_id = $subordinate_id;
		$checkUserEmail->save();

		if ($request->status == 0) {
			$getClerk = User::where('business_id', $request->id)->where('user_type', self::BUSINESS_CLERK)->pluck('id');
			if ($getClerk) {
				//	UserFacility::whereIn('user_id',$getClerk)->delete();
			}

			$userData = User::where('business_id', $request->id)->where('user_type', self::BUSINESS_CLERK)->get();
			if ($userData) {
				foreach ($userData as $userVal) {
					$userVal->status = '0';
					$userVal->Save();
				}
			}
		}

		// if business will be inactive then qr code will be deleted
		if ($request->status == 0) {
			BusinessQrCode::where('business_id', $request->id)->delete();
		} else {
			//check QR code facility and policy 
			$businessQR = BusinessQrCode::where('business_id', $request->id)->first();
			$is_exist = 0;
			$is_facility = '';
			if ($businessQR) {
				$is_facility = $businessQR->facility_id;
				foreach (json_decode($request->facilities, true) as $rec) {
					$policyArray = explode(',', $rec['policies']);
					if ($rec['id'] == $is_facility) {
						$is_exist = 1;

						if (!in_array($businessQR->policy_id, $policyArray)) {
							$businessFacility = BusinessQrCode::where('business_id', $request->id)->where('facility_id', $is_facility)->delete();
						}
					}
				}
				if ($is_exist == 0) {
					$businessFacility = BusinessQrCode::where('business_id', $request->id)->where('facility_id', $is_facility)->delete();
				}
			}
		}

		$permission = $request->permission;
		if ($permission) {
			$membershipPlan = UserMembership::where('user_id', $partner_id)->first();
			if ($membershipPlan) {
				$this->request = $request;
				$this->attachMembershipPlanToUser($users, $membershipPlan->membership_plan_id);
			}
			SubPartnerPermission::where('user_id', $checkUserEmail->id)->delete();
			foreach (json_decode($permission) as $key => $value) {
				$memberships['partner_id'] = $partner_id;
				$memberships['membership_plan_id'] = $membershipPlan->membership_plan_id;
				$memberships['web'] = $value->web;
				$memberships['display_name'] = $value->display_name;
				$memberships['parent_id'] = isset($value->parent_id) ? $value->parent_id : NULL;
				$memberships['user_id'] = $checkUserEmail->id;
				$memberships['type'] = $value->type;
				$memberships['list_order'] = isset($value->list_order) ? $value->list_order : 0;
				SubPartnerPermission::create($memberships);
			}
		}

		return $result;
	}

	public function profileupdate(Request $request)
	{
		$this->log->error('Update Business ' . json_encode($request->all()));

		$result = AffiliateBusiness::find($request->id);

		if (!$result) {
			throw new ApiGenericException("Record Not Found.");
		}


		// for business brand details
		if ($request->file('logo')) {
			if ($result->logo != '') {
				$file_path = storage_path("app/business-brand-settings/" . $result->logo);
				if (is_file($file_path)) {
					unlink($file_path);
				}
			}


			$logo = $request->file('logo');
			$file_extension = $logo->getClientOriginalExtension();
			$file_name = Auth::user()->id . '_' . date('YmdHis') . '_logo.' . $file_extension;
			$destination_path = storage_path("app/business-brand-settings");
			if (!$logo->move($destination_path, $file_name)) {
				throw new ApiGenericException("Something went wrong while importing users sheet");
			}
			$result->logo = $file_name;
		}
		if ($request->file('banner')) {

			if ($result->banner != '') {
				$file_path = storage_path("app/business-brand-settings/" . $result->banner);
				if (is_file($file_path)) {
					unlink($file_path);
				}
			}

			$banner = $request->file('banner');
			$file_extension = $banner->getClientOriginalExtension();
			$file_name = Auth::user()->id . '_' . date('YmdHis') . '_banner.' . $file_extension;
			$destination_path = storage_path("app/business-brand-settings");
			if (!$banner->move($destination_path, $file_name)) {
				throw new ApiGenericException("Something went wrong while importing users sheet");
			}
			$result->banner = $file_name;
		}

		if ($request->file('favicon')) {

			if ($result->favicon != '') {
				$file_path = storage_path("app/business-brand-settings/" . $result->favicon);
				if (is_file($file_path)) {
					unlink($file_path);
				}
			}

			$favicon = $request->file('favicon');
			$file_extension = $favicon->getClientOriginalExtension();
			$file_name = Auth::user()->id . '_' . date('YmdHis') . '_favicon.' . $file_extension;
			$destination_path = storage_path("app/business-brand-settings");
			if (!$favicon->move($destination_path, $file_name)) {
				throw new ApiGenericException("Something went wrong while importing users sheet");
			}
			$result->favicon = $file_name;
		}

		if (!empty($request->color)) {
			$result->color = $request->color;
		}
		if (!empty($request->rgb_color)) {
			$result->rgb_color = $request->rgb_color;
		}
		if (!empty($request->secondary_color)) {
			$result->secondary_color = $request->secondary_color;
		}
		if (!empty($request->secondary_rgb_color)) {
			$result->secondary_rgb_color = $request->secondary_rgb_color;
		}

		$result->save();

		return $result;
	}

	public function destroy($id)
	{
		if (isset($request->partner_id)) {
			$partner_id = $request->partner_id;
		} else {
			if (Auth::user()->user_type == '1') {
			} else {
				if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
					$partner_id = Auth::user()->created_by;
				} else {
					$partner_id = Auth::user()->id;
				}
			}
		}
		if (Auth::user()->user_type == '1') {
			$result = AffiliateBusiness::find($id);
		} else {
			$result = AffiliateBusiness::where('partner_id', $partner_id)->find($id);
		}

		if ($result) {
			BusinessQrCode::where('business_id', $id)->delete();
			$result->delete();

			$checkUserEmail = User::where('business_id', $id)->where('user_type', self::USERTYPE)->first();
			if ($checkUserEmail) {
				$checkUserEmail->delete();
			}

			BusinessFacilityPolicy::where('business_id', $id)->delete();

			$getClerk = User::where('business_id', $id)->where('user_type', self::BUSINESS_CLERK)->pluck('id');
			if ($getClerk) {
				UserFacility::whereIn('user_id', $getClerk)->delete();
			}

			User::where('business_id', $id)->where('user_type', self::BUSINESS_CLERK)->delete();

			return "Data successfully deleted.";
		} else {
			return "Record Not Found.";
		}
	}
	/*
	public function getBusinessType()
	{
		$device = BusinessType::where('status', '1')->get();
		return $device;
	}
	*/
	public function getBusinessDetails(Request $request)
	{

		$this->setCustomTimezone($request->facility_id);

		$ticket = Ticket::where('ticket_number', $request->ticket_number)->first();
		if (!$ticket) {
			throw new ApiGenericException("Ticket not Found.");
		}
		$diff_in_hours = $ticket->length;

		$this->log->info("condition check 1: " . json_encode($ticket->length == '' || $ticket->checkout_datetime == ''));
		if ($ticket->length == '' || $ticket->checkout_datetime == '') {
			$arrival_times = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
			$from_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

			$diff_in_hours = $arrival_times->diffInRealHours($from_time);
			$diff_in_mins = $arrival_times->diffInRealMinutes($from_time);
			if ($diff_in_mins > 0) {
				$diff_in_hours = number_format($diff_in_mins / 60, 2);
			}
			if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
				$diff_in_mins = $arrival_times->diffInRealMinutes($from_time);
				$diff_in_hours = number_format($diff_in_mins / 60, 2);
			}
			$diff_in_hours = $diff_in_hours;
		}


		$facilities = [];
		$current_date = date('Y-m-d') . ' 23:59:59';
		$this->log->info("condition check 2: " . json_encode(Auth::check()));
		if (Auth::check()) {
			if (Auth::user()->user_type == self::BUSINESS_CLERK) {
				$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
				$business_id = Auth::user()->business_id;
			}
			if (Auth::user()->user_type == self::USERTYPE) {
				$facilities = DB::table('business_facility_policy')->where('business_id', Auth::user()->business_id)->pluck('facility_id');
			}
			if (Auth::user()->user_type == self::SUBORDINATE) {
				$partner_id = Auth::user()->created_by;
				$facilities = DB::table('user_facilities')->whereNull('deleted_at')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
				$business_id = $request->business_id;
			}
			if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
				$partner_id = Auth::user()->created_by;
				$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
				$business_id = $request->business_id;
			}

			if (Auth::user()->user_type == self::SUPERADMIN) {
				$partner_id = $request->partner_id;
				$business_id = $request->business_id;
				$facilities = DB::table('business_facility_policy')->where('business_id', $business_id)->pluck('facility_id');
			}

			if (Auth::user()->user_type == self::PARTNER) {
				$partner_id = Auth::user()->id;
				$business_id = $request->business_id;
				$facilities = DB::table('business_facility_policy')->where('business_id', $business_id)->pluck('facility_id');
			} else if (Auth::user()->user_type == self::USERTYPE) {
				$business_id = Auth::user()->business_id;
			}

			$userValidation = UserValidateMaster::where('user_id', Auth::user()->id)->first();
		} else {
			$business_id = $request->business_id;
		}
		/*
		if(!$userValidation){
			throw new ApiGenericException("User Cappping Not Found.");
		}
		*/

		//$request->request->add(['paid_type' => $userValidation->paid_type]);
		$result = AffiliateBusiness::select('id', 'business_name', 'created_by')->with(
			[
				'BusinessFacilityPolicy',
				'BusinessFacilityPolicy.policies' => function ($query) use ($request) {

					if ($request->channel != 'both') {
						$query = $query->where('consumption_channel', $request->channel);
						$query = $query->orWhere('consumption_channel', 'both');
					} else {
						$query = $query->Where('consumption_channel', 'both');
					}
				},
				'BusinessFacilityPolicy' => function ($query) use ($request, $facilities) {
					if ($facilities) {
						$query->whereIn('facility_id', $facilities);
					} else {
						$query->where('facility_id', $request->facility_id);
					}
					if (isset($request->policy_id)) {
						$query->where('policy_id', $request->policy_id);
					}
				}
			]
		)->where('id', $business_id);

		$results =  $result->get();
		// $result = $result->first();
		$validity = $booking_start = $user_check = $day_true = $duration_hrs = $clerk_paid_type = $status = $validity_start_end = 0;

		foreach ($results as $key => $result) {
			# code...
			$this->log->info("condition check 3: Business id: $business_id " . json_encode(isset($result->BusinessFacilityPolicy) && $result->BusinessFacilityPolicy != ''));
			if (isset($result->BusinessFacilityPolicy) && $result->BusinessFacilityPolicy != '') {

				$this->log->info("condition check 4: " . count($result->BusinessFacilityPolicy));
				foreach ($result->BusinessFacilityPolicy as $key => $val) {

					if ($val->policies != '' || $val->policies != null) {


						if ($val->policies->is_booking_start == '1') {

							if ($val->policies->booking_start_date <= $current_date) {
							} else {
								$validity = 1;
							}
						}
						if ($val->policies->status != '1') {

							$status = 1;
						}



						if ($val->policies->is_validity_start_end == '1') {

							if ($val->policies->validity_start_date <= Carbon::now() && $val->policies->validity_end_date >= Carbon::now()) {
							} else {
								$validity_start_end = 1;
							}
						}

						if ($val->policies->is_booking_end == '1') {

							if ($val->policies->booking_end_date >= $current_date) {
							} else {
								$booking_start = 1;
							}
						}
						// check user type

						if ($val->policies->user_type != '2') {
							if ($val->policies->user_type != $request->user_type) {
								$user_check = 1;
							}
						}


						//validate hours and days

						if ($val->policies->discount_type == '1' && $val->policies->duration_type == 'hours') {

							if ($ticket->length < $val->duration_value) {
								$duration_hrs = 1;
							}
						}

						if ($val->policies->discount_type == '1' && $val->policies->duration_type == 'days') {

							$hrs = ($val->duration_value) * 24;
							if ($ticket->length < $hrs) {
								$duration_hrs = 1;
							}
						}
						//validaty clerk paid type

						if (isset($userValidation) && !empty($userValidation)) {
							if ($val->policies->discount_type != $userValidation->paid_type) {
								$clerk_paid_type = 1;
							}
						}
						//policyDay validate
						$policydays = PolicyDay::where('business_policy_id', $val->policy_id)->get();
						if ($policydays->count() > 0) {

							$from_time = date('H:i:s');
							$entry_exite_time = 0;
							$policy_day = 0;
							$days = date('l');
							//$today = date("Y-m-d");
							//$hour = date('H:i:s');
							$hour = date('H:i:s', strtotime($ticket->check_in_datetime));
							$chekout_hrs = date('H:i:s', strtotime($ticket->checkout_datetime));


							foreach ($policydays as $policyday) {

								$days_data = explode(",", $policyday->days);

								$current = strtolower(end($days_data)); //get last day name from array
								$nextDay = strtotime('+1 day' . $current);
								$dayName = date('l', $nextDay);
								// when next day value is 0 then 
								if ($policyday->next_day != '1') {

									if (in_array($days, $days_data)) {
										$policy_day = 1;


										if ($ticket->checkout_datetime != '') {


											if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
												if ($hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time && $ticket->length <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
												if ($hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
												if ($hour >= $policyday->start_time && $ticket->length <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
												if ($chekout_hrs <= $policyday->end_time  &&  $ticket->length <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '') {
												if ($hour >= $policyday->start_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->end_time != '') {
												if ($chekout_hrs <= $policyday->end_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->max_hour != '') {
												if ($ticket->length  <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											}
										} else {
											// For gated Facility if not length and checkout time found 
											if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
												if ($hour >= $policyday->start_time && $from_time <= $policyday->end_time && $diff_in_hours <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
												if ($hour >= $policyday->start_time && $from_time <= $policyday->end_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
												if ($hour >= $policyday->start_time && $diff_in_hours <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
												if ($from_time <= $policyday->end_time  &&  $diff_in_hours <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '') {
												if ($hour >= $policyday->start_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->end_time != '') {
												if ($from_time <= $policyday->end_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->max_hour != '') {
												if ($diff_in_hours  <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											}
										}
									}
								} else {

									if (in_array($days, $days_data)) {
										$policy_day = 1;
										if ($ticket->checkout_datetime != '') {


											if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
												if ($hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time && $ticket->length <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
												if ($hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
												if ($hour >= $policyday->start_time && $ticket->length <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
												if ($chekout_hrs <= $policyday->end_time  &&  $ticket->length <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '') {
												if ($hour >= $policyday->start_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->end_time != '') {
												if ($chekout_hrs <= $policyday->end_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->max_hour != '') {
												if ($ticket->length  <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											}
										} else {
											// For gated Facility if not length and checkout time found 
											if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
												if ($hour >= $policyday->start_time && $from_time <= $policyday->end_time && $diff_in_hours <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
												if ($hour >= $policyday->start_time && $from_time <= $policyday->end_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
												if ($hour >= $policyday->start_time && $diff_in_hours <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
												if ($from_time <= $policyday->end_time  &&  $diff_in_hours <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->start_time != '') {
												if ($hour >= $policyday->start_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->end_time != '') {
												if ($from_time <= $policyday->end_time) {
													$entry_exite_time = 1;
												}
											} elseif ($policyday->max_hour != '') {
												if ($diff_in_hours  <= $policyday->max_hour) {
													$entry_exite_time = 1;
												}
											}
										}
									} else {

										if ($ticket->checkout_datetime != '') {
											if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
												if ($dayName == $days && $hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time && $ticket->length <= $policyday->max_hour) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
												if ($dayName == $days && $hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
												if ($dayName == $days && $hour >= $policyday->start_time && $ticket->length <= $policyday->max_hour) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
												if ($dayName == $days && $chekout_hrs <= $policyday->end_time  &&  $ticket->length <= $policyday->max_hour) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->start_time != '') {
												if ($dayName == $days && $hour >= $policyday->start_time) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->end_time != '') {
												if ($dayName == $days && $chekout_hrs <= $policyday->end_time) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->max_hour != '') {
												if ($dayName == $days && $ticket->length  <= $policyday->max_hour) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											}
										} else {

											if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
												if ($dayName == $days &&  $hour >= $policyday->start_time && $from_time <= $policyday->end_time && $diff_in_hours <= $policyday->max_hour) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
												if ($dayName == $days &&  $hour >= $policyday->start_time && $from_time <= $policyday->end_time) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
												if ($dayName == $days &&  $hour >= $policyday->start_time && $diff_in_hours <= $policyday->max_hour) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
												if ($dayName == $days && $from_time <= $policyday->end_time  &&  $diff_in_hours <= $policyday->max_hour) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->start_time != '') {
												if ($dayName == $days &&  $hour >= $policyday->start_time) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->end_time != '') {
												if ($dayName == $days && $from_time <= $policyday->end_time) {
													$$entry_exite_time = 1;
													$policy_day = 1;
												}
											} elseif ($policyday->max_hour != '') {
												if ($dayName == $days && $diff_in_hours  <= $policyday->max_hour) {
													$entry_exite_time = 1;
													$policy_day = 1;
												}
											}
										}
									}
								}
							}

							if ($policy_day == 0 || $entry_exite_time == 0) {
								unset($val->policies);
								$val['policies'] = null;
							}
						}
						//policyDay validate end


						if ($validity == '1' ||  $booking_start == '1' || $user_check == '1' || $day_true == '1' || $duration_hrs == '1'  || $status == '1' || $validity_start_end == '1') {

							unset($val->policies);
							$val['policies'] = null;
						}

						$validity = $booking_start = $user_check = $day_true = $duration_hrs =  $status = $validity_start_end = 0;
					}
				}
			}
		}

		if (!$result) {
			throw new ApiGenericException("Record Not Found.");
		}
		return $result;
	}

	public function getBusinessDetailsForMultipleTickets(Request $request)
	{

		$this->setCustomTimezone($request->facility_id);

		$ticket_numbers = $request->ticket_numbers;
		if (strpos($ticket_numbers, '[') !== false) $ticket_numbers = array_map('trim', explode(',', trim($ticket_numbers, "[]")));
		else $ticket_numbers = [$ticket_numbers];

		$commonPolicies = [];
		foreach ($ticket_numbers as $ticket_number) {
			$ticket = Ticket::where('ticket_number', $ticket_number)->first();
			if (!$ticket) {
				throw new ApiGenericException("Ticket not Found.");
			}
			$diff_in_hours = $ticket->length;

			if ($ticket->length == '' || $ticket->checkout_datetime == '') {
				$arrival_times = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
				$from_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

				$diff_in_mins = $arrival_times->diffInRealMinutes($from_time);
				$diff_in_hours = number_format($diff_in_mins / 60, 2);

				if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
					$diff_in_hours = number_format($diff_in_mins / 60, 2);
				}
			}


			$facilities = [];
			$current_date = date('Y-m-d') . ' 23:59:59';
			$this->log->info("condition check 2: " . json_encode(Auth::check()));
			if (Auth::check()) {
				if (Auth::user()->user_type == self::BUSINESS_CLERK) {
					$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
					$business_id = Auth::user()->business_id;
				}
				if (Auth::user()->user_type == self::USERTYPE) {
					$facilities = DB::table('business_facility_policy')->where('business_id', Auth::user()->business_id)->pluck('facility_id');
				}
				if (Auth::user()->user_type == self::SUBORDINATE) {
					$partner_id = Auth::user()->created_by;
					$facilities = DB::table('user_facilities')->whereNull('deleted_at')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
					$business_id = $request->business_id;
				}
				if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
					$partner_id = Auth::user()->created_by;
					$facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id', Auth::user()->id)->pluck('facility_id');
					$business_id = $request->business_id;
				}

				if (Auth::user()->user_type == self::SUPERADMIN) {
					$partner_id = $request->partner_id;
					$business_id = $request->business_id;
					$facilities = DB::table('business_facility_policy')->where('business_id', $business_id)->pluck('facility_id');
				}

				if (Auth::user()->user_type == self::PARTNER) {
					$partner_id = Auth::user()->id;
					$business_id = $request->business_id;
					$facilities = DB::table('business_facility_policy')->where('business_id', $business_id)->pluck('facility_id');
				} else if (Auth::user()->user_type == self::USERTYPE) {
					$business_id = Auth::user()->business_id;
				}

				$userValidation = UserValidateMaster::where('user_id', Auth::user()->id)->first();
			} else {
				$business_id = $request->business_id;
			}
			/*
			if(!$userValidation){
				throw new ApiGenericException("User Cappping Not Found.");
			}
			*/

			//$request->request->add(['paid_type' => $userValidation->paid_type]);
			$result = AffiliateBusiness::select('id', 'business_name', 'created_by')->with(
				[
					'BusinessFacilityPolicy',
					'BusinessFacilityPolicy.policies' => function ($query) use ($request) {

						if ($request->channel != 'both') {
							$query = $query->where('consumption_channel', $request->channel);
							$query = $query->orWhere('consumption_channel', 'both');
						} else {
							$query = $query->Where('consumption_channel', 'both');
						}
					},
					'BusinessFacilityPolicy' => function ($query) use ($request, $facilities) {
						if ($facilities) {
							$query->whereIn('facility_id', $facilities);
						} else {
							$query->where('facility_id', $request->facility_id);
						}
						if (isset($request->policy_id)) {
							$query->where('policy_id', $request->policy_id);
						}
					}
				]
			)->where('id', $business_id);

			$results =  $result->get();
			// $result = $result->first();
			$validity = $booking_start = $user_check = $day_true = $duration_hrs = $clerk_paid_type = $status = $validity_start_end = 0;

			foreach ($results as $key => $result) {
				# code...
				$this->log->info("condition check 3: Business id: $business_id " . json_encode(isset($result->BusinessFacilityPolicy) && $result->BusinessFacilityPolicy != ''));
				if (isset($result->BusinessFacilityPolicy) && $result->BusinessFacilityPolicy != '') {

					$this->log->info("condition check 4: " . count($result->BusinessFacilityPolicy));
					foreach ($result->BusinessFacilityPolicy as $key => $val) {

						if ($val->policies != '' || $val->policies != null) {


							if ($val->policies->is_booking_start == '1') {

								if ($val->policies->booking_start_date <= $current_date) {
								} else {
									$validity = 1;
								}
							}
							if ($val->policies->status != '1') {

								$status = 1;
							}



							if ($val->policies->is_validity_start_end == '1') {

								if ($val->policies->validity_start_date <= Carbon::now() && $val->policies->validity_end_date >= Carbon::now()) {
								} else {
									$validity_start_end = 1;
								}
							}

							if ($val->policies->is_booking_end == '1') {

								if ($val->policies->booking_end_date >= $current_date) {
								} else {
									$booking_start = 1;
								}
							}
							// check user type

							if ($val->policies->user_type != '2') {
								if ($val->policies->user_type != $request->user_type) {
									$user_check = 1;
								}
							}


							//validate hours and days

							if ($val->policies->discount_type == '1' && $val->policies->duration_type == 'hours') {

								if ($ticket->length < $val->duration_value) {
									$duration_hrs = 1;
								}
							}

							if ($val->policies->discount_type == '1' && $val->policies->duration_type == 'days') {

								$hrs = ($val->duration_value) * 24;
								if ($ticket->length < $hrs) {
									$duration_hrs = 1;
								}
							}
							//validaty clerk paid type

							if (isset($userValidation) && !empty($userValidation)) {
								if ($val->policies->discount_type != $userValidation->paid_type) {
									$clerk_paid_type = 1;
								}
							}
							//policyDay validate
							$policydays = PolicyDay::where('business_policy_id', $val->policy_id)->get();
							if ($policydays->count() > 0) {

								$from_time = date('H:i:s');
								$entry_exite_time = 0;
								$policy_day = 0;
								$days = date('l');
								//$today = date("Y-m-d");
								//$hour = date('H:i:s');
								$hour = date('H:i:s', strtotime($ticket->check_in_datetime));
								$chekout_hrs = date('H:i:s', strtotime($ticket->checkout_datetime));


								foreach ($policydays as $policyday) {

									$days_data = explode(",", $policyday->days);

									$current = strtolower(end($days_data)); //get last day name from array
									$nextDay = strtotime('+1 day' . $current);
									$dayName = date('l', $nextDay);
									// when next day value is 0 then 
									if ($policyday->next_day != '1') {

										if (in_array($days, $days_data)) {
											$policy_day = 1;


											if ($ticket->checkout_datetime != '') {


												if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
													if ($hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time && $ticket->length <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
													if ($hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
													if ($hour >= $policyday->start_time && $ticket->length <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
													if ($chekout_hrs <= $policyday->end_time  &&  $ticket->length <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '') {
													if ($hour >= $policyday->start_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->end_time != '') {
													if ($chekout_hrs <= $policyday->end_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->max_hour != '') {
													if ($ticket->length  <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												}
											} else {
												// For gated Facility if not length and checkout time found 
												if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
													if ($hour >= $policyday->start_time && $from_time <= $policyday->end_time && $diff_in_hours <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
													if ($hour >= $policyday->start_time && $from_time <= $policyday->end_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
													if ($hour >= $policyday->start_time && $diff_in_hours <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
													if ($from_time <= $policyday->end_time  &&  $diff_in_hours <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '') {
													if ($hour >= $policyday->start_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->end_time != '') {
													if ($from_time <= $policyday->end_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->max_hour != '') {
													if ($diff_in_hours  <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												}
											}
										}
									} else {

										if (in_array($days, $days_data)) {
											$policy_day = 1;
											if ($ticket->checkout_datetime != '') {


												if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
													if ($hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time && $ticket->length <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
													if ($hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
													if ($hour >= $policyday->start_time && $ticket->length <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
													if ($chekout_hrs <= $policyday->end_time  &&  $ticket->length <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '') {
													if ($hour >= $policyday->start_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->end_time != '') {
													if ($chekout_hrs <= $policyday->end_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->max_hour != '') {
													if ($ticket->length  <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												}
											} else {
												// For gated Facility if not length and checkout time found 
												if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
													if ($hour >= $policyday->start_time && $from_time <= $policyday->end_time && $diff_in_hours <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
													if ($hour >= $policyday->start_time && $from_time <= $policyday->end_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
													if ($hour >= $policyday->start_time && $diff_in_hours <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
													if ($from_time <= $policyday->end_time  &&  $diff_in_hours <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->start_time != '') {
													if ($hour >= $policyday->start_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->end_time != '') {
													if ($from_time <= $policyday->end_time) {
														$entry_exite_time = 1;
													}
												} elseif ($policyday->max_hour != '') {
													if ($diff_in_hours  <= $policyday->max_hour) {
														$entry_exite_time = 1;
													}
												}
											}
										} else {

											if ($ticket->checkout_datetime != '') {
												if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
													if ($dayName == $days && $hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time && $ticket->length <= $policyday->max_hour) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
													if ($dayName == $days && $hour >= $policyday->start_time && $chekout_hrs <= $policyday->end_time) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
													if ($dayName == $days && $hour >= $policyday->start_time && $ticket->length <= $policyday->max_hour) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
													if ($dayName == $days && $chekout_hrs <= $policyday->end_time  &&  $ticket->length <= $policyday->max_hour) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->start_time != '') {
													if ($dayName == $days && $hour >= $policyday->start_time) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->end_time != '') {
													if ($dayName == $days && $chekout_hrs <= $policyday->end_time) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->max_hour != '') {
													if ($dayName == $days && $ticket->length  <= $policyday->max_hour) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												}
											} else {

												if ($policyday->start_time != '' && $policyday->end_time != '' && $policyday->max_hour != '') {
													if ($dayName == $days &&  $hour >= $policyday->start_time && $from_time <= $policyday->end_time && $diff_in_hours <= $policyday->max_hour) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->end_time != '') {
													if ($dayName == $days &&  $hour >= $policyday->start_time && $from_time <= $policyday->end_time) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->start_time != '' && $policyday->max_hour != '') {
													if ($dayName == $days &&  $hour >= $policyday->start_time && $diff_in_hours <= $policyday->max_hour) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->end_time != '' && $policyday->max_hour != '') {
													if ($dayName == $days && $from_time <= $policyday->end_time  &&  $diff_in_hours <= $policyday->max_hour) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->start_time != '') {
													if ($dayName == $days &&  $hour >= $policyday->start_time) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->end_time != '') {
													if ($dayName == $days && $from_time <= $policyday->end_time) {
														$$entry_exite_time = 1;
														$policy_day = 1;
													}
												} elseif ($policyday->max_hour != '') {
													if ($dayName == $days && $diff_in_hours  <= $policyday->max_hour) {
														$entry_exite_time = 1;
														$policy_day = 1;
													}
												}
											}
										}
									}
								}

								if ($policy_day == 0 || $entry_exite_time == 0) {
									unset($val->policies);
									$val['policies'] = null;
								}
							}
							//policyDay validate end


							if ($validity == '1' ||  $booking_start == '1' || $user_check == '1' || $day_true == '1' || $duration_hrs == '1'  || $status == '1' || $validity_start_end == '1') {

								unset($val->policies);
								$val['policies'] = null;
							}

							$validity = $booking_start = $user_check = $day_true = $duration_hrs =  $status = $validity_start_end = 0;
						}
					}
				}
			}

			if (!$result) {
				throw new ApiGenericException("Record Not Found.");
			}
			$currentPolicies = collect($result->BusinessFacilityPolicy)->filter(function ($policy) {
				return !is_null($policy->policies); // Remove policies where 'policies' is null
			});

			if (empty($commonPolicies)) {
				$commonPolicies = $currentPolicies;
			} else {
				$commonPolicies = $commonPolicies->filter(function ($commonPolicy) use ($currentPolicies) {
					return $currentPolicies->filter(function ($currentPolicy) use ($commonPolicy) {
						// Replace 'id' with the property you want to compare
						return $commonPolicy->id === $currentPolicy->id;
					});
				});
			}
		}
		if ($commonPolicies->isEmpty()) {
			throw new ApiGenericException("No Common Policies Found.");
		}
		return $commonPolicies->values();
	}

	public function updatenew(Request $request)
	{
		$result = AffiliateBusiness::find($request->id);
		$partner_id = Auth::user()->id;
		if (!$result) {
			throw new ApiGenericException("Record Not Found.");
		}

		if ($result->business_name != $request->business_name) {
			$businessExist = AffiliateBusiness::where('business_name', $request->business_name)->where('partner_id', $partner_id)->first();
			if ($businessExist) {
				throw new ApiGenericException("Business name already added.");
			}
		}


		if ($result->email != $request->email) {
			$emailExist = AffiliateBusiness::where('email', $request->email)->where('partner_id', $partner_id)->first();
			if ($emailExist) {
				throw new ApiGenericException("Email is already exist.");
			}
		}

		$checkUserEmail = User::where('email', $result->email)->where('user_type', self::USERTYPE)->first();

		if (!$checkUserEmail) {
			throw new ApiGenericException("User email already exist.");
		}

		$result->business_name = $request->business_name;
		$result->owner_name = $request->owner_name;
		$result->email = $request->email;
		$result->phone = isset($request->phone) ? $request->phone : '';
		$result->web_url = isset($request->web_url) ? $request->web_url : '';
		$result->address = isset($request->address) ? $request->address : '';
		$result->address2 = isset($request->address2) ? $request->address2 : '';
		$result->city = isset($request->city) ? $request->city : '';
		$result->state = isset($request->state) ? $request->state : '';
		$result->country = isset($request->country) ? $request->country : '';
		$result->pincode = isset($request->zip_code) ? $request->zip_code : '';
		$result->created_by = $partner_id;
		$result->status = $request->status;
		$result->save();


		if ($request->facilities) {
			$users = User::selectRaw('GROUP_CONCAT(id) as user_ids')->where('business_id', $request->id)->first();
			//$users = User::where('business_id',$request->id)->where('user_type',self::BUSINESS_CLERK)->pluck('id');

			if ($users) {
				$facility = UserFacility::selectRaw('GROUP_CONCAT(facility_id) as facility_ids')->wherein('user_id', [$users->user_ids])->first();
			}

			$resultData = BusinessFacilityPolicy::where('business_id', $request->id)->delete();

			foreach ($request->facilities as $rec) {
				$policyArray = explode(',', $rec['policies']);

				foreach ($policyArray as $val) {
					$mappingData['business_id'] = $result->id;
					$mappingData['facility_id'] = $rec['id'];
					$mappingData['policy_id'] = $val;
					BusinessFacilityPolicy::create($mappingData);
				}
			}
		}

		// Get country Code
		$this->countryCode = QueryBuilder::appendCountryCode();

		$checkUserEmail->name = $request->owner_name;
		$checkUserEmail->email = $request->email;
		$checkUserEmail->phone = $this->countryCode . $request->phone;
		$checkUserEmail->anon = false;
		$checkUserEmail->user_type = self::USERTYPE;
		$checkUserEmail->created_by = $partner_id;
		$checkUserEmail->business_id = $result->id;
		$checkUserEmail->status = $request->status;
		$checkUserEmail->save();

		return $result;
	}

	public function setCustomTimezone($facility_id)
	{
		$facility = Facility::find($facility_id);
		$secret = OauthClient::where('partner_id', $facility->owner_id)->first();
		$partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();

		if (($facility) && ($facility->timezone != '')) {
			date_default_timezone_set($facility->timezone);
		} else {
			if ($partnerTimezone) {
				if ($partnerTimezone->timezone != '') {
					date_default_timezone_set($partnerTimezone->timezone);
				}
			}
		}
	}

	public function handleImageforQrcode($request, $image_name)
	{
		$image = $request->file('qrcode_logo');
		if ($image) {
			$file_extension = $image->getClientOriginalExtension();
			$file_name = $request->policy_id . '_' . rand(1001, 9999) . '.' . $file_extension;
			$destination_path = storage_path('app/business_qrcode');

			if (!$image->move($destination_path, $file_name)) {
				$this->licensePlateLog->error("image not uploaded");
				throw new ApiGenericException("Something went wrong while upload image.");
			}
			return $file_name;
		} else {
			return $image_name;
		}
	}

	public function createBusinessQRCodeNew(Request $request)
	{
		$this->log->info('Create Business QRCode: ' . json_encode($request->all()));

		// Check for comma-separated values (only one ID should be passed)
		if (strpos($request->id, ',') !== false || strpos($request->policy_id, ',') !== false) {
			throw new ApiGenericException("System error, Please contact admin.");
		}

		// Determine if this is a create or update operation
		$id = $request->id ?? 0;
		if ($id) {
			$businessQrCode = BusinessQrCode::where(['id' => $id, 'enabled_ticket_creation_flow' => $request->enabled_ticket_creation_flow ?? 0])->latest('id')->first();
			if (!$businessQrCode) {
				throw new ApiGenericException("QR Code not found with ID: $id");
			}
		} else {
			$businessQrCode = new BusinessQrCode();
			$businessQrCode->business_qrcode = $this->generateQRCode();
		}

		// Assign values from request
		$businessQrCode->title = $request->title;
		$businessQrCode->business_id = $request->business_id;
		$businessQrCode->policy_id = $request->policy_id;
		$businessQrCode->facility_id = $request->facility_id;
		$businessQrCode->enabled_ticket_creation_flow = $request->enabled_ticket_creation_flow ?? 0;
		$businessQrCode->qrcode_logo = $this->handleImageforQrcode($request, $businessQrCode->qrcode_logo ?? "");
		$businessQrCode->save();

		// Get updated/created record
		$result = BusinessQrCode::where('id', $businessQrCode->id)->latest('id')->first();
		$result->qrCode = $result->qrCode;

		// If admin, return all QR codes for the business
		// if ($request->has('is_admin') && $request->is_admin == '1') {
		// 	$data = BusinessQrCode::where('business_id', $request->business_id)->get();
		// 	return [
		// 		'bussiness_qrcode' => $data,
		// 		'result' => $result
		// 	];
		// }

		// Return only the current QR code by default
		return $result;
	}

	public function createBusinessQRCode(Request $request)
	{
		$this->log->info('Create Business  QRCode' . json_encode($request->all()));
		$facilities = explode(",", $request->facility_id);
		$arr = [];
		// dd($request->id);
		if (isset($request->id) && !empty($request->id)) {
			$resultData = BusinessQrCode::where('business_id', $request->business_id)->get();
			if (!$resultData) {
				throw new ApiGenericException("Id not found.");
			}
			if (strpos($request->id, ',') !== false) {
				throw new ApiGenericException("System error, Please contact to admin.");
			}

			$result = BusinessQrCode::find($request->id);
			$image_name = $result->qrcode_logo ?? "";
			//$resultData = $resultData->where('facility_id', $request->facility_id);
			if (count($resultData) > 0) {
				//if facility is same but policy is deffirent then new QR code generated.
				foreach ($resultData as $p) {
					if ($request->policy_id != $p['policy_id']) {
						if (!isset($request->enabled_ticket_creation_flow) && $request->enabled_ticket_creation_flow == '0') {
							$data['business_qrcode'] = $this->generateQRCode();
						}
						$data['facility_id'] = $request->facility_id;
						$data['policy_id'] = $request->policy_id;
						$data['business_id'] = $request->business_id;
						$data['title'] = $request->title;
					} else {
						if (!isset($request->enabled_ticket_creation_flow) && $request->enabled_ticket_creation_flow == '0') {
							BusinessQrCode::where('id', $request->id)->delete();
							$data['business_qrcode'] = $this->generateQRCode();
						}
						$data['facility_id'] = $request->facility_id;
						$data['policy_id'] = $request->policy_id;
						$data['business_id'] = $request->business_id;
						$data['title'] = $request->title;
					}
				}
			} else {
				//if facility is deffirent
				if (!isset($request->enabled_ticket_creation_flow) && $request->enabled_ticket_creation_flow == '0') {
					$data['business_qrcode'] = $this->generateQRCode();
				}
				$data['facility_id'] = $request->facility_id;
				$data['policy_id'] = $request->policy_id;
				$data['business_id'] = $request->business_id;
				$data['title'] = $request->title;
			}


			//regenerate QR code

			$image = $request->file('qrcode_logo');
			if ($image != '') {
				$file_extension = $image->getClientOriginalExtension();
				$file_name =  $request->policy_id . '_' . rand(1001, 9999) . '.' . $file_extension;
				$destination_path = storage_path('app/business_qrcode');
				$data['qrcode_logo'] = $file_name;
				if (!$image->move($destination_path, $file_name)) {
					$this->licensePlateLog->error("image not uploaded");
					throw new ApiGenericException("Something went wrong while upload image.");
				}
			} else {
				$data['qrcode_logo'] = $image_name;
			}

			if (!isset($request->enabled_ticket_creation_flow) && $request->enabled_ticket_creation_flow == '0') {
				$result = BusinessQrCode::create($data);
			}
			if (isset($request->is_admin) && ($request->is_admin == '1')) {
				$data =	BusinessQrCode::where('business_id', $request->business_id)->get();
				$arr['bussiness_qrcode'] = $data;
				$arr['result'] = $result;
				return $arr;
			}
			return $result;
		} else {
			$rec = BusinessQrCode::where('policy_id', $request->policy_id)->where('business_id', $request->business_id)->get();

			foreach ($rec as $k => $v) {
				$db_facility = explode(",", $v->facility_id);
				$result = array_intersect($facilities, $db_facility);
				if ($result) {
					$v->delete();
				} else {
					BusinessQrCode::whereIn('facility_id', $facilities)->whereIn('policy_id', $facilities)->where('business_id', $request->business_id)->delete();
				}
			}

			$data['business_qrcode'] = $this->generateQRCode();
			$data['facility_id'] = $request->facility_id;
			$data['policy_id'] = $request->policy_id;
			$data['business_id'] = $request->business_id;
			$data['title'] = $request->title;
			$image = $request->file('qrcode_logo');
			if ($image != '') {
				$file_extension = $image->getClientOriginalExtension();
				$file_name =  $request->facility_id . '_' . $request->policy_id . '_' . rand(1001, 9999) . '.' . $file_extension;
				$destination_path = storage_path('app/business_qrcode');
				$data['qrcode_logo'] = $file_name;
				if (!$image->move($destination_path, $file_name)) {
					$this->licensePlateLog->error("image not uploaded");
					throw new ApiGenericException("Something went wrong while upload image.");
				}
			}
			if (!isset($request->enabled_ticket_creation_flow) && $request->enabled_ticket_creation_flow == '0') {
			}
			$result = BusinessQrCode::create($data);
			if (isset($request->is_admin) && ($request->is_admin == '1')) {
				$data =	BusinessQrCode::where('business_id', $request->business_id)->get();
				$arr['bussiness_qrcode'] = $data;
				$arr['result'] = $result;
				return $arr;
			}
			return $result;
		}
	}

	protected function generateQRCode()
	{
		$qrcode = 'BE' . rand(1000, 9999) . rand(1000, 9999);
		$isExist = BusinessQrCode::where('business_qrcode', $qrcode)->first();
		if ($isExist) {
			$this->generateQRCode();
		}
		return $qrcode;
	}

	public function businessEmailQRCode(Request $request)
	{

		$this->log->error('QRCode get request ' . json_encode($request->all()));
		$data = BusinessQrCode::with('Business')->where('id', $request->id)->first();
		if (!$data) {
			$this->log->info("Invalid QR Code, Please scan a valid QR!.");
			throw new ApiGenericException("Invalid QR Code, Please scan a valid QR!.");
		}
		Artisan::queue('businessQrcode:email', ['id' => $request->id, 'email' => $request->email]);
		$this->log->info('QRCode send email success.');
		return "Email sent successfully !";
	}

	public function getBusinessLogoUrl($id)
	{
		if (!$id) {
			throw new NotFoundException('Please send valid id.');
		}
		$photo = BusinessQrCode::find($id);
		if (!$photo) {
			throw new NotFoundException('No logo image with that name found.');
		}
		$file = Storage::disk('local')->get("business_qrcode/" . $photo->qrcode_logo) ?: null;
		$response = Response::make($file);
		$file_extension = mime_content_type(storage_path('app/business_qrcode/' . $photo->qrcode_logo));
		$response->header('Content-Type', $file_extension);
		return $response;
	}

	public function businessPdfQRCode($id, $enabled_ticket_creation_flow = false)
	{
		$this->log->info('QRCode pdf download start');
		$this->request->request->add(['enabled_ticket_creation_flow' =>  $enabled_ticket_creation_flow]);
		$data = BusinessQrCode::with('Business')->where('id', $id)->first();
		if (!$data) {
			$this->log->info("Invalid QR Code, Please scan a valid QR!.");
			return response()->json([
				'status' => 500,
				'data' => null,
				'errors' => [
					'message' => 'Invalid QR Code, Please scan a valid QR!',
				],
			]);
			// throw new ApiGenericException("Invalid QR Code, Please scan a valid QR!.");
		}
		$qr_facility = explode(",", $data->facility_id);
		$brand_setting = FacilityBrandSetting::whereIn('facility_id', $qr_facility)->first();
		if ($brand_setting) {
			$data->facility_logo_id = $brand_setting->id;
		} else {
			$brand_setting = BrandSetting::where('user_id', $data->Business->partner_id)->first();
			$data->brandsetting_id = $brand_setting->id;
		}

		$html = view("download.businesss_qrcode_pdf", ['data' => $data])->render();
		$image = app()->make(Pdf::class);
		$pdf = $image->getOutputFromHtmlString($html);
		return $pdf;
	}

	// public function getQrcodeValidateDetails($qrcode)
	// {
	// 	$data = BusinessQrCode::where('business_qrcode',base64_decode($qrcode))->first();
	// 	if (!$data) {
	// 			$this->log->info("Invalid QR Code, Please scan a valid QR!.");
	// 			throw new ApiGenericException("Invalid QR Code, Please scan a valid QR!.");
	// 	}

	// 	return $data;
	// }

	public function getTicketSearch(Request $request)
	{
		$data = BusinessQrCode::where('business_qrcode', base64_decode($request->business_qrcode))->first();
		if (!$data) {
			$this->log->info("Invalid QR Code, Please scan a valid QR!.");
			throw new ApiGenericException("Invalid QR Code, Please scan a valid QR!.");
		}
		$this->log->info(json_encode($data) . json_encode($request->all()));
		$qr_facility = explode(",", $data->facility_id);

		/*$ticket = Ticket::with(['user' => function ($query) {
			$query->select('id', 'name', 'email', 'phone', 'anon');
		}])->where('ticket_number', $request->ticket)->first();

		if (!$ticket) {
			throw new ApiGenericException("Ticket number not found.");
		}
		if (!in_array($ticket->facility_id, $qr_facility)){
			throw new ApiGenericException("Facility not matched");
		}*/

		if ($request->ticket != '') {
			$ticket = Ticket::with(['user' => function ($query) {
				$query->select('id', 'name', 'email', 'phone', 'anon');
			}])->where('ticket_number', $request->ticket)->first();

			if (!$ticket) {
				throw new ApiGenericException("Ticket number not found.");
			}
		} else {
			$ticket = Ticket::with(['user' => function ($query) {
				$query->select('id', 'name', 'email', 'phone', 'anon');
			}])->whereIn("facility_id", $qr_facility);
			$msgType = "phone.";
			if ($request->cardLast4 != '' && $request->cardExpiry != '') {
				$ticket = $ticket->where('card_last_four', $request->cardLast4)->where('expiry', $request->cardExpiry);
				$msgType = "card.";
			}
			if ($request->phone != '') {
				$ticket = $ticket->WhereHas(
					'user',
					function ($query) use ($request) {
						$query->where('phone', 'like', "%{$request->phone}%");
					}
				);
			}
			$ticket = $ticket->orderBy("id", "desc")->first();
			if (!$ticket) {
				throw new ApiGenericException("No ticket found against this " . $msgType);
			}
		}
		if (!in_array($ticket->facility_id, $qr_facility)) {
			throw new ApiGenericException("Facility not matched");
		}
		$request->request->add(['partner_id' => $ticket->partner_id]);
		$request->request->add(['facility_id' => $ticket->facility_id]);
		$search_ticket = $this->subordinate->ticketSearch($request);
		return $search_ticket;
	}
	// public function getPolicyData(Request $request)
	// {
	// 	$this->log->error('Get request for business policy ' . json_encode($request->all()));
	// 	$data = $this->getBusinessDetails($request);
	// 	return $data;
	// }
	public function validateQrbasedTicket(Request $request)
	{
		$this->log->info('request Received for Validate QR based ticket ' . json_encode($request->all()));

		$decodedBusinessQR = base64_decode($request->business_qrcode);
		//04/07/2024 - Shalu: checking if one or more of the 'special characters' found in $string
		if (preg_match('/[\'^£$%&*()}{@#~?><>,|=_+¬-]/', $decodedBusinessQR)) {
			throw new ApiGenericException("Please scan the right QR code.");
		}
		//04/07/2024 - Shalu
		try {
			$data = BusinessQrCode::where('business_qrcode', $decodedBusinessQR)->first();
			if (!$data) {
				$this->log->info("This QR code is not valid. Pls contact the Help Desk.");
				throw new ApiGenericException("This QR code is not valid. Pls contact the Help Desk.");
			}
		} catch (\Exception $e) {
			$this->log->info("This QR code is not valid. Pls contact the Help Desk.");
			throw new ApiGenericException("This QR code is not valid.");
		}
		$qr_facility = explode(",", $data->facility_id);
		$this->log->info("Reached 1111");

		$ticket = Ticket::with(['user' => function ($query) {
			$query->select('id', 'name', 'email', 'phone', 'anon');
		}])->where('ticket_number', $request->ticket_number)->first();

		if (!$ticket) {
			throw new ApiGenericException("Ticket number not found.");
		} else {
			$policy = BusinessPolicy::find($data->policy_id);

			if ($policy->user_type == "1" && isset($ticket->user) && ($ticket->user->anon)) {
				throw new ApiGenericException("Invalid QR Code for this user, Please scan a valid QR!.");
			}

			if ($policy->user_type == "1" && $ticket->user_id == "0") {
				throw new ApiGenericException("Invalid QR Code for this user, Please scan a valid QR!.");
			}
		}
		$this->log->info("Reached 222");
		// Check Ticket Status Before making any Validation Start... : Vijay :17-12-2024
		$this->setCustomTimezone($ticket->facility_id);					// set timezone here.

		if ($ticket->facility->is_gated_facility == '1') {
			if ($ticket->is_checkout == '1') {
				throw new ApiGenericException('Ticket has been already checked out.');
			}
		}
		// PIMS-11195 
		// This Apple Payment Case : 
		// We make refund for Apple PAY in case validation to skip the anet check.   
		if (in_array($ticket->is_offline_payment, [1, 2, 3]) || ($ticket->anet_transaction_id != '') && isset($ticket->transaction->method) && in_array($ticket->transaction->method, [config('parkengage.APPLE_PAY'), config('parkengage.GOOGLE_PAY_HL')])) {
			throw new ApiGenericException('Ticket can not be validated as it is already paid by the user');
		}
		// PIMS-11195 : Close Here...


		if (($ticket->discount_amount > '0.00')) {
			throw new ApiGenericException('The Ticket is already validated.');
		}
		// Close here...

		if (!in_array($ticket->facility_id, $qr_facility)) {
			//if ($ticket->facility_id != $data->facility_id) {
			throw new ApiGenericException("Facility not matched");
		} elseif ($ticket->is_offline_payment != '0') {
			throw new ApiGenericException("Ticket can not be validated , As payment is already done for this ticket");
		} elseif ($ticket->paid_date != NULL) {
			throw new ApiGenericException("This ticket is already validated.");
		} elseif ($ticket->reservation_id != NULL) {
			throw new ApiGenericException("Ticket number is not eligible for validation process.");
		}
		$this->log->info("Reached 333");
		// dd($request->all());

		// call getPoloicy 
		$request->request->add(['facility_id' => $ticket->facility_id]);
		$request->request->add(['partner_id' => $ticket->partner_id]);
		$request->request->add(['business_id' => $data->business_id]);
		$request->request->add(['policy_id' => $data->policy_id]);
		if (isset($ticket->user->email) && isset($ticket->user->phone_number)) {
			$request->request->add(['user_type' => ($ticket->user->email == '' && $ticket->user->phone_number == '') ? '2' : '1']);
		}
		$this->log->info("Reached 444");
		$policyData = $this->getBusinessDetails($request);
		$this->log->info("Reached 555" . json_encode($policyData));

		// return $policyData;

		if (isset($policyData->BusinessFacilityPolicy) && $policyData->BusinessFacilityPolicy != '' && $policyData->BusinessFacilityPolicy->count() > 0) {
			foreach ($policyData->BusinessFacilityPolicy as $key => $val) {
				if ($val->policies != '' || $val->policies != null) {
					$request->request->add(['business_id' => $data->business_id]);
					if ($val->policies->discount_type === '0') {
						$validateTicket = $this->subordinate->makeCheckout($request);
					} elseif ($val->policies->discount_type === '1' && $val->policies->duration_type == 'hours') {
						$request->request->add(['hours' => $val->policies->discount_value]);
						$validateTicket = $this->subordinate->makeCheckout($request);
					} elseif ($val->policies->discount_type === '2') {
						$request->request->add(['amount' => $val->policies->discount_value]);
						$validateTicket = $this->subordinate->makeCheckout($request);
					} elseif ($val->policies->discount_type === '3') {
						$request->request->add(['percent' => $val->policies->discount_value]);
						$validateTicket = $this->subordinate->makeCheckout($request);
					}
					$this->log->info("Print validateTicket :  " . json_encode($validateTicket));

					//04/07/2024 :  Shalu add payable and other amount related fixed done
					$rate['price'] = $validateTicket->parking_amount;
					$ticket = Ticket::where(['ticket_number' => $request->ticket_number])->first();
					if ($ticket->facility->is_gated_facility == '1') {
						$priceBreakup = $ticket->priceBreakUp($rate);
					} else {
						$priceBreakup = $ticket->unGatedPriceBreakUp($rate, $ticket->facility, 'get');
					}

					$this->log->info("Print Price Break up :  " . json_encode($priceBreakup));

					if (count($priceBreakup) > 0) {
						$validateTicket->tax_rate 			= $priceBreakup['tax_rate'] ?? '';
						$validateTicket->parking_amount 	= $priceBreakup['parking_amount'] ?? '';
						$validateTicket->total 				= $priceBreakup['total'] ?? '';
						$validateTicket->grand_total 		= $priceBreakup['grand_total'] ?? '';
						$validateTicket->payable_amount 	= $priceBreakup['payable_amount'] ?? '';
						$validateTicket->amount_paid 	    = $priceBreakup['amount_paid'] ?? '';
					}
					//End 04/07/2024 :  Shalu add payable and other amount related fixed done
					$this->log->info("Reached 666 ");

					// Refund API for APL Case 
					if (isset($ticket->transaction->method) && in_array($ticket->transaction->method, [config('parkengage.APPLE_PAY'), config('parkengage.GOOGLE_PAY_HL')]) && $validateTicket->paid_amount > 0  && $validateTicket->paid_by > 0) {
						$this->log->info("Going To make Refund : ");
						$refundAmount = $validateTicket->paid_amount;   // Validated Amount we will refund
						// $refundstatus = DatacapPaymentGateway::datacapPaymentTicketRefund($refundAmount, $validateTicket, $ticket->facility->FacilityPaymentDetails->datacap_ios_mid, $ticket->facility->FacilityPaymentDetails->datacap_refund_url);
						// Dynamically create the gateway instance
						$gatewayName = '';
						// Add Facility Object :
						$request->request->add(['ticket' => $ticket]);
						$request->request->add(['reference_key' => $ticket->ticket_number]);

						$options['mid'] 			= $ticket->facility->FacilityPaymentDetails->wallet_datacap_mid;
						$options['url'] 			= $ticket->facility->FacilityPaymentDetails->datacap_refund_url;
						$options['payment_token'] 	= $ticket->payment_token;

						$config = config('payment.gateways.' . strtolower($gatewayName), []);
						// Create an instance of the PG dynamic and the we can use the PG Class Fucnations
						try {
							$this->log->info("Create payment gateway Instance : ");
							// Create Class Instance 
							$paymentGateway = PaymentGatewayFactory::create($ticket->facility->FacilityPaymentDetails->facility_payment_type_id, $config);

							// Initiate refund 
							$refundResponse = $paymentGateway->processRefund($request, $refundAmount, $options);
							$this->log->info("Print Refund Response  : " . $refundResponse);

							// Check and Decode response  
							$refundResponse = json_decode($refundResponse);
							if ($refundResponse->Status == 'Approved' && $refundResponse->TranCode == 'Return') {
								// Save Refund data 
								$dataSaved = $paymentGateway->saveRefundTransactions($request, $refundResponse, 'refund');
								$this->log->info("Print Saved Data  : " . $dataSaved);
							}

							$this->log->error("Error inexception : ");
						} catch (\Throwable $th) {
							// Revert Validation 
							$revertValidation = Ticket::where(['ticket_number' => $request->ticket_number])->first();
							$revertValidation->paid_by	 	= null;
							$revertValidation->paid_amount 	= null;
							$revertValidation->paid_remark 	= null;
							$revertValidation->paid_date 	= null;
							$revertValidation->paid_type 	= null;
							$revertValidation->save();

							$this->log->error("Error inexception : " . $th->getMessage() . ' File Name ' . $th->getFile() . ' Line No ' . $th->getLine());

							throw new ApiGenericException("Somthing went wrong while validating ticket");
							throw $th->getMessage();
						}
					}
					// Refund Section Close here only for APL/GPAY case.

					return $validateTicket;
				} else {
					//policy not get  then throw error
					throw new ApiGenericException("Business policy not full fill the condition.");
				}
			}
		} else {
			throw new ApiGenericException("This QR code is not valid. Pls contact the Help Desk");
		}
	}

	public function businessQRCodeLogoDelete(Request $request)
	{
		$data = BusinessQrCode::find($request->id);
		if (!$data) {
			throw new ApiGenericException("Id not found.");
		}
		$file = $data->qrcode_logo;
		$data->qrcode_logo = NULL;
		$data->save();
		unlink(storage_path('app/business_qrcode/' . $file));
		return $data;
	}

	public function getLogoUrl($id)
	{
		if (!$id) {
			throw new NotFoundException('Please send valid id.');
		}
		$brandSetting = AffiliateBusiness::find($id);
		if (!$brandSetting) {
			throw new NotFoundException('No logo image with that name found.');
		}
		if ($brandSetting->logo != NULL) {
			$file = Storage::disk('local')->get("business-brand-settings/" . $brandSetting->logo) ?: null;

			// create response and add encoded image data
			$response = Response::make($file);
			// getting content type e.g image/jpeg
			$file_extension = mime_content_type(storage_path('app/business-brand-settings/' . $brandSetting->logo));
			// set content-type
			$response->header('Content-Type', $file_extension);
			// output
			return $response;
		} else {
			return NULL;
		}
	}

	public function getBannerUrl($id)
	{
		if (!$id) {
			throw new NotFoundException('Please send valid id.');
		}
		$brandSetting = AffiliateBusiness::find($id);
		if (!$brandSetting) {
			throw new NotFoundException('No banner image with that name found.');
		}
		if ($brandSetting->banner != NULL) {
			$file = Storage::disk('local')->get("business-brand-settings/" . $brandSetting->banner);
			// create response and add encoded image data
			$response = Response::make($file);

			// getting content type e.g image/jpeg
			$file_extension = mime_content_type(storage_path('app/business-brand-settings/' . $brandSetting->banner));
			// set content-type
			$response->header('Content-Type', $file_extension);
			// output
			return $response;
		} else {
			return NULL;
		}
	}

	public function getFaviconUrl($id)
	{
		if (!$id) {
			throw new NotFoundException('Please send valid id.');
		}
		$brandSetting = AffiliateBusiness::find($id);
		if (!$brandSetting) {
			throw new NotFoundException('No banner image with that name found.');
		}
		if ($brandSetting->favicon != NULL) {
			$file = Storage::disk('local')->get("business-brand-settings/" . $brandSetting->favicon) ?: null;

			// create response and add encoded image data
			$response = Response::make($file);

			// getting content type e.g image/jpeg
			$file_extension = mime_content_type(storage_path('app/business-brand-settings/' . $brandSetting->favicon));
			// set content-type
			$response->header('Content-Type', $file_extension);
			// output
			return $response;
		} else {
			return NULL;
		}
	}

	private function attachMembershipPlanToUser($user, $membership_plan_id, $is_active = 1)
	{
		$membership_plan = MembershipPlan::find($membership_plan_id);
		if ($membership_plan) {
			$start_date = Carbon::now();
			$days = 0;
			if ($this->request->plan_type == '0') {
				$days = self::TRIAL_PLAN_DAYS;
			} elseif ($this->request->plan_type == '1') {
				$days = self::MONTHLY_PLAN_DAYS;
			} elseif ($this->request->plan_type == '2') {
				$days = self::ANNUAL_PLAN_DAYS;
			} else {
				$days = 0;
			}
			$end_date = $start_date->copy()->addDays($days);

			if (!$user->membershipPlans->contains($membership_plan->id)) {
				$user->membershipPlans()->save($user, [
					'membership_plan_id' => $membership_plan_id,
					'start_date' => $start_date,
					'end_date' => $end_date,
					'is_active' => $is_active,
					'plan_type' => isset($this->request->plan_type) ? $this->request->plan_type : '0'
				]);
				$membership_plan->start_date = $start_date->format('Y-m-d H:i:s');
				$membership_plan->end_date = $end_date->format('Y-m-d H:i:s');
				return $membership_plan;
			}
		}
		return null;
	}
}
