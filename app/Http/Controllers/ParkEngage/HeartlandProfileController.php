<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use Artisan;
use Exception;
use Response;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Services\LoggerFactory;
use App\Classes\MagicCrypt;
use App\Models\Ticket;
use App\Exceptions\ApiGenericException;
use Carbon\Carbon;
use GlobalPayments\Api\ServiceConfigs\Gateways\PorticoConfig;
use GlobalPayments\Api\ServicesContainer;
use GlobalPayments\Api\Entities\Address;
use GlobalPayments\Api\Entities\Customer;
use GlobalPayments\Api\PaymentMethods\CreditCardData;
use GlobalPayments\Api\PaymentMethods\RecurringPaymentMethod;
use GlobalPayments\Api\Entities\Transaction;
use GlobalPayments\Api\Entities\Enums\PaymentDataSourceType;
use GlobalPayments\Api\Entities\Enums\MobilePaymentMethodType;
use GlobalPayments\Api\Entities\Enums\Environment;
use GlobalPayments\Api\Entities\Enums\Channel;
use GlobalPayments\Api\Entities\Enums\TransactionModifier;
use GlobalPayments\Api\ServiceConfigs\Gateways\GpApiConfig;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\FacilityPaymentDetail;
use GlobalPayments\Api\Utils\Logging\Logger;
use GlobalPayments\Api\Utils\Logging\SampleRequestLogger;

class HeartlandProfileController extends Controller
{
	protected $log;
	protected $request;
	public function __construct(Request $request, LoggerFactory $logFactory)
	{
		$this->log = $logFactory->setPath('logs/heartland')->createLogger('heartland');
		$this->request = $request;
	}

	public function heartlandConfig()
	{
		$config = new PorticoConfig();
		$config->secretApiKey = config('parkengage.HEARTLAND_SECRET_KEY');
		$config->developerId = config('parkengage.HEARTLAND_DEVELOPER_ID');
		$config->versionNumber = config('parkengage.HEARTLAND_VERSION_NUMBER');
		$config->serviceUrl = config('parkengage.HEARTLAND_SERVICE_URL');
		ServicesContainer::configureService($config);
	}

	public function setDecryptedCard()
	{
		if (isset($this->request->payment_profile_id) && $this->request->payment_profile_id != "") {
			return;
		}
		$key = env('PCI_ENCRYPTION_KEY');
		$mc = new MagicCrypt($key, 256);
		$decryptedNonce = $mc->decrypt($this->request->nonce);
		$cardData = explode(':', $decryptedNonce);
		$zipCode = (isset($cardData[4]) && $cardData[4] !='undefined') ? $cardData[4] : '';
		$this->request->request->add(
			[
				'name_on_card' => $cardData[0],
				'card_number' => $cardData[1],
				'expiration_date' => $cardData[2],
				'security_code' => $cardData[3],
				'zip_code_on_card' => $zipCode
			]
		);
	}

	public function index(Request $request)
	{
		$user_id = Auth::user()->id;

		$result = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id);

		if ($request->sort != '') {
			$result = $result->orderBy($request->sort, $request->sortBy);
		} else {
			$result = $result->orderBy("id", "DESC");
		}
		$result = $result->get();
		if (!$result) {
			throw new ApiGenericException("Card Not Found");
		}
		$data = [];
		foreach ($result as $key => $value) {
			$ticket = Ticket::where("session_id", $value->token)->where("user_id", $user_id)->where("is_checkout", '0')->first();
			$value['is_payment_complete'] = "1";
			if ($ticket) {
				$value['is_payment_complete'] = "0";
			}
		}
		$data['payments'] = $result;

		return $data;
	}

	public function store(Request $request)
	{
		$this->log->info("Request Data Planet: " . json_encode($request->all()));
		$user_id = Auth::user()->id;
		$partner_id = Auth::user()->created_by;
		if ($request->nonce) {
			$this->setDecryptedCard();
		}

		$refundstatus = json_decode($response, TRUE);

		if (!$refundstatus) {
			throw new ApiGenericException("Card Details Not Added");
		}

		$cardCheck = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $partner_id)->where('card_last_four', $refundstatus["Response"]["Params"]["CardNumberLast4"])->first();

		if ($cardCheck) {
			throw new ApiGenericException("Card details are already added.");
		}
		$status = 0;

		$user_card = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->first();

		$this->log->info("Request Data Planet: " . json_encode($refundstatus));
		if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
			throw new ApiGenericException("Card details are invalid");
		}

		$data['user_id']        = $user_id;
		$data['partner_id']     = $partner_id;
		$data['name']           = isset($request->name_on_card) ? $request->name_on_card : '';
		$data['card_last_four'] = substr($request->card_number, -4);
		$data['card_type']      = $refundstatus["Response"]["Params"]["CardSchemeId"];
		$data['card_name']      = $refundstatus["Response"]["Params"]["CardSchemeName"];
		$data['expiry']         = $request->expiration_date;
		$data['token']          = $refundstatus["Response"]["Params"]["Token"];
		$data['tx_state_text']  = $refundstatus["Response"]["Params"]["TxStateText"];
		$data['tx_state']       = $refundstatus["Response"]["Params"]["TxState"];
		$data['result_reason']  = $refundstatus["Response"]["Params"]["ResultReason"];
		$data['currency_used']  = $refundstatus["Response"]["Params"]["CurrencyUsed"];
		if (!$user_card) {
			$data['is_default'] = 1;
		} else {
			if (is_numeric($request->is_default)) {
				if (is_numeric($request->is_default)) {
					$status = $request->is_default;
				}
				if (!empty($status)) {
					HeartlandPaymentProfile::where(['user_id' => $user_id])->update(array('is_default' => 0));
				}
				$data['is_default'] = $status;
			}
		}
		$result = HeartlandPaymentProfile::create($data);
		if (!$result) {
			throw new ApiGenericException("Record Not Added");
		}
		return $result;
	}


	public function destroy($id)
	{
		$result = HeartlandPaymentProfile::find($id);

		if ($result) {
			$result->delete();
			return response(
				[
					"message" => 'Card Successfully Deleted.'
				]
			);
		}
		throw new ApiGenericException("Card Not Found.");
	}

	public function getHeartlandPaymentProfile(Request $request)
	{
		$user_id = Auth::user()->id;
		$result = HeartlandPaymentProfile::Select('card_last_four', 'expiry', 'token', 'user_id', 'card_type', 'card_name', 'tx_state_text', 'tx_state', 'result_reason', 'currency_used')->whereNull('deleted_at')->where('user_id', $user_id)->get();
		return $result;
	}

	public function testHeartland(Request $request)
	{
		//dd('i am here');
		// call config for heartland 
		$this->heartlandConfig();

		$card = new CreditCardData();
		$card->number = "****************";
		$card->expMonth = "12";
		$card->expYear = "2030";
		$card->cvn = "123";

		$address = new Address();
		$address->postalCode = "201301";

		$response = $card->charge(1)
			->withCurrency("USD")
			->withAddress($address)
			->execute();
		dd($response);
	}

	public function getHeartlandCustomers()
	{
		// call config for heartland 
		$this->heartlandConfig();
		$customers = Customer::findAll();
		return $customers;
	}

	public function chargePaymentByCard(Request $request)
	{
		// call config for heartland 
		$this->heartlandConfig();

		$this->log->info("Request Data Heartland: " . json_encode($request->all()));
		if ($request->nonce) {
			$this->setDecryptedCard();
		}
		$card_month = substr($request->expiration_date, 0, 2);
		$card_year = substr($request->expiration_date, -2);

		//dd($request->all(),$card_month,$card_year);

		$card = new CreditCardData();
		$card->number = $request->card_number;
		$card->expMonth = $card_month;
		$card->expYear = '20' . $card_year;
		$card->cvn = $request->security_code;
		//dd($card);
		$address = new Address();
		$address->postalCode = "201301";
		//dd($card,$address);
		$response = $card->charge(1)
			->withCurrency("USD")
			->withAddress($address)
			->execute();
		//dd($response);
		return response()->json($response);
	}

	public function chargePaymentByToken(Request $request)
	{
		// call config for heartland 
		$this->heartlandConfig();
		//dd($request->token);
		$card = new CreditCardData();
		$card->token = $request->token;

		$address = new Address();
		$address->postalCode = $request->postalCode;

		$response = $card->charge($request->TransactionAmount)
			->withCurrency("USD")
			->withAddress($address)
			->execute();
		return response()->json($response);
	}


	public function authCardPayment(Request $request)
	{

		$this->log->info("Request Data Heartland: " . json_encode($request->all()));
		// call config for heartland 
		$this->heartlandConfig();
		if ($request->nonce) {
			$this->setDecryptedCard();
		}
		$card_month = substr($request->expiration_date, 0, 2);
		$card_year = substr($request->expiration_date, -2);

		//dd($request->all(),$card_month,$card_year);

		$card = new CreditCardData();
		$card->number = $request->card_number;
		$card->expMonth = $card_month;
		$card->expYear = '20' . $card_year;
		$card->cvn = $request->security_code;
		//dd($card);
		$address = new Address();
		$address->postalCode = $request->postalCode;
		//dd($card,$address);
		$response = $card->authorize($request->Amount)
			->withCurrency("USD")
			->withAddress($address)
			->execute();


		return response()->json($response);
	}

	public function chargePreauthCardPayment(Request $request)
	{

		$this->log->info("Request Data Heartland: " . json_encode($request->all()));
		// call config for heartland 
		$this->heartlandConfig();

		$response = Transaction::fromId($request->transactionId)
			->capture($request->Amount)
			->execute();

		return response()->json($response);
	}


	public function cardPayment(Request $request)
	{

		$this->log->info("Request Data Heartland: " . json_encode($request->all()));
		// call config for heartland 
		$this->heartlandConfig();
		if ($request->nonce) {
			$this->setDecryptedCard();
		}
		$card_month = substr($request->expiration_date, 0, 2);
		$card_year = substr($request->expiration_date, -2);

		//dd($request->all(),$card_month,$card_year);

		$card = new CreditCardData();
		$card->number = $request->card_number;
		$card->expMonth = $card_month;
		$card->expYear = '20' . $card_year;
		$card->cvn = $request->security_code;
		//dd($card);
		$address = new Address();
		$address->postalCode = $request->postalCode;
		$address->streetAddress1 = "6860";
		$address->city = "Dallas";
		$address->state = "Dallas";

		$response = $card->charge($request->Amount)
			->withCurrency("USD")
			->withAddress($address)
			->execute();
		return response()->json($response);
	}

	public function saveHeartlandCard(Request $request)
	{
		$this->log->info("Request Data Heartland: " . json_encode($request->all()));
		// call config for heartland 
		$this->heartlandConfig();
		if ($request->nonce) {
			$this->setDecryptedCard();
		}
		$card_month = substr($request->expiration_date, 0, 2);
		$card_year = substr($request->expiration_date, -2);

		//dd($request->all(),$card_month,$card_year);

		$card = new CreditCardData();
		$card->number = $request->card_number;
		$card->expMonth = $card_month;
		$card->expYear = '20' . $card_year;
		$card->cvn = $request->security_code;
		//dd($card);
		$address = new Address();
		$address->postalCode = $request->postalCode;

		$response = $card->verify()
			->withAddress($address)
			->withRequestMultiUseToken(true)
			->execute();
		return response()->json($response);
	}

	public function refundHeartlandPayment(Request $request)
	{
		$this->log->info("Request Data Heartland: " . json_encode($request->all()));
		// call config for heartland 
		$this->heartlandConfig();
		//dd($request->all(),$request->transactionId,$request->Amount);
		/*
		$response = Transaction::fromId($request->transactionId)
			->refund($request->Amount)
			->execute();
		*/
		$response = Transaction::fromId($request->transactionId)
			->reverse($request->Amount)
			->execute();
		//$this->log->info("Refund Response Heartland: " . json_encode($response));
		return response()->json($response);
	}

	public function surChargeHeartlandPayment(Request $request)
	{
		$this->log->info("Request Data Heartland: " . json_encode($request->all()));
		// call config for heartland 
		$this->heartlandConfig();
		if ($request->nonce) {
			$this->setDecryptedCard();
		}
		$card_month = substr($request->expiration_date, 0, 2);
		$card_year = substr($request->expiration_date, -2);

		//dd($request->all(),$card_month,$card_year);

		$card = new CreditCardData();
		$card->number = $request->card_number;
		$card->expMonth = $card_month;
		$card->expYear = '20' . $card_year;
		$card->cvn = $request->security_code;
		//dd($card);
		$address = new Address();
		$address->postalCode = $request->postalCode;
		$surcharge_amount = $request->Amount;
		$surcharge = $request->surcharge;
		$response = $card->authorize($surcharge_amount)
			->withCurrency("USD")
			->withAddress($address)
			->withSurchargeAmount($surcharge)
			->execute();
		return response()->json($response);
	}

	public function returnHeartlandPayment(Request $request)
	{
		$this->log->info("Request Data Heartland: " . json_encode($request->all()));
		// call config for heartland 
		$this->heartlandConfig();
		if ($request->nonce) {
			$this->setDecryptedCard();
		}
		$card_month = substr($request->expiration_date, 0, 2);
		$card_year = substr($request->expiration_date, -2);

		//dd($request->all(),$card_month,$card_year);

		$card = new CreditCardData();
		$card->number = $request->card_number;
		$card->expMonth = $card_month;
		$card->expYear = '20' . $card_year;
		$card->cvn = $request->security_code;
		//dd($card);
		$address = new Address();
		$address->postalCode = $request->postalCode;
		$amount = $request->Amount;
		/*
		$response = $card->charge($amount)
			->withCurrency("USD")
			->withAddress($address)
			->execute();
		*/
		$transID = 200068137427;
		$response = Transaction::fromId($transID)
			->refund($amount)
			->withCurrency("USD")
			->execute();

		return response()->json($response);
	}

	public function verifyCardHeartlandPayment(Request $request)
	{
		$this->log->info("Request Data Heartland: " . json_encode($request->all()));
		// call config for heartland 
		$this->heartlandConfig();

		$card = new CreditCardData();
		$card->token = $request->token;

		$address = new Address();
		$address->postalCode = $request->postalCode;

		$response = $card->verify()
			->withCurrency("USD")
			->withAddress($address)
			->withRequestMultiUseToken(true)
			->execute();

		return response()->json($response);
	}

	public function chargePaymentByTokenPay(Request $request)
	{
		// call config for heartland 
		$this->heartlandConfig();
		//dd($request->token);
		$card = new CreditCardData();
		$card->token = $request->paymentReference;
		$card->paymentSource = PaymentDataSourceType::GOOGLEPAYWEB;

		try {
			$response = $card->charge(15)
				->withCurrency('USD')
				->withAllowDuplicates(true)
				->execute();
			return response()->json($response);
			//echo "<h1>Success!</h1>" . PHP_EOL;
			$this->log->info("Response Data Heartland: " . $response);
			//echo "<b>Your transaction Id is: </b>" . $response->transactionId;
		} catch (Exception $e) {
			$this->log->info("Response Data Heartland Error: " . json_encode($e->getMessage()));
			throw new ApiGenericException($e->getMessage());
			//echo 'Failure: ' . $e->getMessage();
			exit;
		}
	}

	public function chargePaymentByAppleToken(Request $request)
	{
		// call config for heartland 
		$this->heartlandConfig();
		//dd($request->token);
		$card = new CreditCardData();
		$card->mobileType = MobilePaymentMethodType::APPLEPAY;
		$card->paymentSource = PaymentDataSourceType::APPLEPAYWEB;
		$card->token = $request->token;

		$address = new Address();
		$address->postalCode = $request->postalCode;

		$response = $card->charge($request->TransactionAmount)
			->withCurrency("USD")
			->withAddress($address)
			->execute();
		return response()->json($response);
	}
	/*
	public function applePayCharge(Request $request){
		
		$config = new PorticoConfig();
        $config->secretApiKey = config('parkengage.HEARTLAND_SECRET_KEY');
	//	$config->secretApiKey = "skapi_cert_MXjRBQBoeXIAa7m-Vq2N_rPWGgTCwFUArzY-VYfXvA";
        $config->developerId = config('parkengage.HEARTLAND_DEVELOPER_ID');
        $config->versionNumber = config('parkengage.HEARTLAND_VERSION_NUMBER');
        $config->serviceUrl = config('parkengage.HEARTLAND_SERVICE_URL');
	//	$config->serviceUrl = "https://cert.api2.heartlandportico.com";
	//	dd($config);
		// call config for heartland 
      //  $this->heartlandConfig();	
		$card = new CreditCardData();
        $card->mobileType = MobilePaymentMethodType::APPLEPAY;
		//$card->paymentSource = PaymentDataSourceType::APPLEPAYAPP;
        $card->paymentSource = PaymentDataSourceType::APPLEPAYWEB;
        $card->token = $request->token;
		ServicesContainer::configureService($config);  
		//dd($card);
		//dd( $card->token);
		$config->requestLogger     = new SampleRequestLogger(new Logger("logs"));
	//	$logger = new Logger(storage_path('logs'));
		//$logger->requestSent('', '', '', '', $data)		
		//dd($logger);
        $response = $card->charge($request->amount)
        ->withCurrency('USD')
        ->withInvoiceNumber($request->invoice_number)
        ->withAllowDuplicates(true)
        ->execute();
		return response()->json($response);	
		//dd($response);
		//$config->responseReceived($response);
      //  return $response;
	}*/

	public function applePaySession(Request $request)
	{
		// verify request required params
		// dd($request->all());
		$this->validate($request, HeartlandPaymentProfile::$validateHLApplePayRequest);
		$FacilityPaymentDetail = FacilityPaymentDetail::with('facility')->where(['heartland_applepay_identifier' => $request->merchantIdentifier, 'facility_id' => $request->facility_id])->first();
		if (!isset($FacilityPaymentDetail->facility->user->userPaymentGatewayDetail[0]->touchless_payment_url)) {
			throw new ApiGenericException('Invalid request, please contact admin');
		}
		$request->request->add(['slug' => $FacilityPaymentDetail->facility->user->userPaymentGatewayDetail[0]->touchless_payment_url]);

		if (empty($FacilityPaymentDetail->hl_apple_pay_cert)) {
			throw new ApiGenericException('Invalid request, certificate not found');
		}
		if (empty($FacilityPaymentDetail->hl_apple_pay_key)) {
			throw new ApiGenericException('Invalid request,  certificate key not found');
		}

		// call config for heartland 
		$this->heartlandConfig();
		$FacilityPaymentDetail = $FacilityPaymentDetail;
		$hlResponse = HeartlandPaymentGateway::heartlandApplePaySession($request, $FacilityPaymentDetail);
		return 	$hlResponse;
	}

	public function getHLTransactionList(Request $request)
	{
		$facilityId = $request->facility_id; //442
		$totalDays = $request->total_days; //10
		// $response = HeartlandPaymentGateway::getPaymentList($facilityId, $totalDays);
		$response = HeartlandPaymentGateway::getPaymentList($request);
		return response()->json($response);
	}
}
