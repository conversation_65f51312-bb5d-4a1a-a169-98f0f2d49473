<?php

namespace App\Http\Controllers\ParkEngage;

use App\Http\Controllers\Controller;
use App\Models\ParkEngage\Configuration;
use App\Exceptions\ApiGenericException;
use App\Http\Requests\Request;
use App\Models\Facility;
use App\Models\ParkEngage\FacilityPaymentDetail;
use App\Models\ParkEngage\FacilityConfiguration;

class ConfigurationController extends Controller
{

    /**
     * 
     *
     * @return \Illuminate\Http\Response
     */
    public function configurationDetails()
    {
        $configuration = Configuration::get();
        $result = array();
        foreach ($configuration as $key => $value) {

            $result[$value->field_name] = $value->field_value;
        }
        return $result;
    }

    //get facility payment details
    public function facilityPaymentDetails($facility_id)
    {
        $paymentConfiguration = FacilityPaymentDetail::with(['facilityPaymentType'])->select('wallet_datacap_mid', 'wallet_datacap_token', 'wallet_datacap_ios_merchant', 'wallet_datacap_gpay_merchant', 'facility_payment_type_id', 'data_trans_merchant_name', 'data_trans_gpay_merchant')
            ->where("facility_id", $facility_id)->first();
        if (!$paymentConfiguration) {
            throw new ApiGenericException("Invalid facility details.");
        }
        $configuration = FacilityConfiguration::where("facility_id", $facility_id)->first();
        //to show apple pay google pay button
        $paymentConfiguration['show_google_pay_button'] = '0';
        $paymentConfiguration['show_apple_pay_button'] = '0';
        if ($configuration) {
            $paymentConfiguration['show_google_pay_button'] = $configuration->show_google_pay_button;
            $paymentConfiguration['show_apple_pay_button'] = $configuration->show_apple_pay_button;
        }
        return $paymentConfiguration;
    }
}
