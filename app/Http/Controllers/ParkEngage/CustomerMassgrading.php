<?php

namespace App\Http\Controllers\ParkEngage;

use App\Exceptions\ApiGenericException;
use App\Http\Controllers\Controller;
use DB;
use Auth;
use Illuminate\Http\Request;
use Config;

use Authorizer;

use App\Classes\LoyaltyProgram;
use App\Models\LoyaltyUserAccounts;

use App\Http\Requests;
use App\Models\OauthClient;
use App\Models\User;
use App\Models\Devicetoken;
use App\Http\Helpers\QueryBuilder;

class CustomerMassgrading extends Controller
{
    public function grantOauthToken(Request $request)
    {
        //return $request->client_id;
        $_SESSION['partner_id'] = '';
        $menu = '';
        if ($request->remember_me) {
            // Set our refresh token to expire in one year if the remember me box is set
            Config::set('oauth2.grant_types.refresh_token.refresh_token_ttl', 60 * 60 * 24 * 365);
            Config::set('oauth2.grant_types.refresh_token.access_token_ttl', 60 * 60 * 24 * 365);
        }

        if (isset($request->client_id) && $request->client_id == 'parkengage-app') {
            // Set our refresh token to expire in one year if the remember me box is set
            Config::set('oauth2.grant_types.refresh_token.refresh_token_ttl', 60 * 60 * 24 * 365);
        }

        Config::set('oauth2.grant_types.password.access_token_ttl', 60 * 60 * 24 * 365);

        $client = OauthClient::where('secret', $request->client_secret)->first();
        if ($client && $client->partner_id != '') {

            $partner = User::where('id', $client->partner_id)->first();
            $userPartner = User::where('email', $request->username)->first();

            if ($partner && ($partner->user_type == '3' || $partner->user_type == '1')) {
                $_SESSION['partner_id'] = $client->partner_id;
            } else {
                $_SESSION['partner_id'] = $client->partner_id;
            }
            // change for clerk user & business user			
            if ($userPartner && ($userPartner->user_type == '8' || $userPartner->user_type == '10')) {
                $_SESSION['partner_id'] = $userPartner->created_by;
            }
        }


        $session = Authorizer::issueAccessToken();
        $user = Auth::user();
        if ((($user->qr_code_number == NULL) || ($user->qr_code_number == '')) && ($user->user_type == '5')) {
            $user->qr_code_number =  $user->generateQrCode();
            $user->save();
        }
        //dd($user);
        $is_device_updated = false;

        if (isset($request->device_token) && $request->device_token != '' && isset($request->device_key) && $request->device_key != '' && isset($request->device_type) && $request->device_type != '') {
            // check id device_key already exists in database
            $device_key = $request->device_key;
            $device = Devicetoken::findDeviceWithKey($device_key);


            // Create New Device Token Entry
            if (!$device) {
                $device = new Devicetoken();
            }

            //	$device->fill($request->only(['user_id', 'device_token', 'device_key', 'device_type']));
            $device->user_id = $user->id;
            $device->device_token = $request->device_token;
            $device->device_id = $request->device_key;
            $device->device_type = $request->device_type;
            $device->save();
            $is_device_updated = true;

            //fetch menu to show

        }
        $menu = DB::table('app_menu')->select('menu_name', 'status')->where('partner_id', $user->created_by)->get();
        $menuArray = [];

      

      

        $authDetails = [];
        if ($user->user_type == '1' || $user->user_type == '3') {
            $client = OauthClient::where('partner_id', $user->id)->first();
            $authDetails['client_id'] = $client->id;
            $authDetails['client_secret'] = $client->secret;
            $showLot = QueryBuilder::getPartnerConfig("IS_SHOW_LOT", $user->id);  #PIMS-12423 DD
        } else {
            $client = OauthClient::where('partner_id', $user->created_by)->first();
            $authDetails['client_id'] = $client->id;
            $authDetails['client_secret'] = $client->secret;
            $showLot = QueryBuilder::getPartnerConfig("IS_SHOW_LOT", $user->created_by);  #PIMS-12423 DD
        }

        $otherDetails = [];
        /* $otherDetails['is_show_lot'] = '0';
        if ($user->id == '358811' || $user->created_by == '358811') {
            $otherDetails['is_show_lot'] = '1';
        } */
        #PIMS-12423 DD      
        if(isset($showLot->key)){
            $otherDetails['is_show_lot'] =  $showLot->value;
        }else{
            $otherDetails['is_show_lot'] = '0';
        }
        return array_merge($session, ["other_details" => $otherDetails, "auth_details" => $authDetails]);
    }
  
    public function customerMass(Request $request)
    {


        $id = "";
        if (!empty($request->user_id) && !empty($request->patner_id)) {
            if (!empty($request->auth_id)) {
                $id = $request->auth_id;
            }
            $accessToken = DB::table('oauth_sessions')
                ->join('oauth_access_tokens', 'oauth_sessions.id', '=', 'oauth_access_tokens.session_id')
                ->join('oauth_refresh_tokens', 'oauth_access_tokens.id', '=', 'oauth_refresh_tokens.access_token_id')
                ->join('oauth_clients', 'oauth_sessions.client_id', '=', 'oauth_clients.id')
                ->where('oauth_sessions.owner_id', $request->user_id)
                ->select('oauth_refresh_tokens.*', 'oauth_sessions.owner_id', 'oauth_sessions.client_id', 'oauth_clients.secret')
                ->first();
            // dd($accessToken);
            if (!empty($accessToken)) {
                $request->request->add(['grant_type' => "user_id_login", "client_id" => $accessToken->client_id, "client_secret" => $accessToken->secret, "username" => $request->username, "client_id" => $accessToken->client_id, "patner_id" => $request->patner_id, "password" => "",]);
                //dd($request);
                $getAccessToken =  $this->grantOauthToken($request);
                // dd($getAccessToken);
                $access_token_id = $getAccessToken['access_token'];        //end
                $refreshToken = $getAccessToken['refresh_token'];
                $gettouchlessurl =   DB::table('users_payment_gateway_details')->where('user_id', $request->patner_id)
                    ->select('users_payment_gateway_details.touchless_payment_url')
                    ->first();
                //get secret of client
                if ($request->patner_id == '3156') {
                    $getclientSecret =  DB::table('oauth_clients')->where('partner_id', $request->patner_id)
                        ->select('oauth_clients.secret')
                        ->first();
                    $endurl = $getclientSecret->secret;
                    $redirectionurl = env('WEB_URL_WAILUKU') . '/navigating/' . $access_token_id . '/' . $refreshToken . '/0/' . $id . '/' . $endurl;
                } else {
                    $endurl = $gettouchlessurl->touchless_payment_url;
                    if ($request->patner_id == '2980') {
                        $redirecturl = env('WEB_URL_CUSTOMERPORTAL_PCI');
                    } else {
                        $redirecturl = env('WEB_MASS_GRADE_URL');
                    }
                    if ($request->patner_id == '1553') {
                        $endurl = 'roc';
                    }
                    //215900

                    $urlparameter = $access_token_id . '/' . $refreshToken . '/0/0/' . $id . '/' . $endurl;
                    $redirectionurl = $redirecturl . 'navigating/' . $urlparameter;
                }
                //end secret
                $data['loginurl'] =  $redirectionurl;
                $response = array(
                    'status' => 200,
                    'data' => $data
                );
                return json_encode($response);
            } else {
                $data['message'] = "Acess token not found.";
                $response = array(
                    'status' => 500,
                    'data' => null,
                    'errors' => $data
                );
                return json_encode($response);
            }
        } else {
            $data['message'] = "Partner id or User id required.";
            $response = array(
                'status' => 500,
                'data' => null,
                'errors' => $data
            );
            return json_encode($response);
        }
    }
}
