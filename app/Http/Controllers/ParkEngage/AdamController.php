<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\ParkEngage\Adam;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

class AdamController extends Controller
{


    public function index(Request $request)
    {
        $facility = '';
        $partner_id = '';
        if ($request->partner_id) {
            $partner_id = $request->partner_id;
        } else {

            if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12') {
                $facility = \DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
                $partner_id = Auth::user()->created_by;
            } elseif (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            }
        }

        $adam = Adam::with(['gates', 'facility']);
        if (Auth::user()->user_type == '4' || Auth::user()->user_type == '12') {
            $adam = $adam->whereIn("facility_id", $facility);
        }
        if (isset($request->port_number) && $request->port_number != '') {
            $adam = $adam->Where('port_number', 'like', '%' . trim($request->port_number) . '%');
            if (isset($partner_id) && $partner_id != '') {
                $adam = $adam->where('partner_id', $partner_id);
            }
        }


        if ($request->search != 'NO') {
            if (isset($partner_id) && !empty($partner_id)) {
                $adam = $adam->where(function ($query) use ($request, $partner_id) {
                    $query->where('name', "like", "%" . $request->search . "%")->where('partner_id', $partner_id);
                    $query->orWhere('ip', "like", "%" . $request->search . "%")->where('partner_id', $partner_id);
                    $query->orWhere('port_number', "like", "%" . $request->search . "%")->where('partner_id', $partner_id);
                    $query->orWhere('remarks', "like", "%" . $request->search . "%")->where('partner_id', $partner_id);
                });
            } else {
                $adam = $adam->where(function ($query) use ($request, $partner_id) {
                    $query->where('name', "like", "%" . $request->search . "%");
                    $query->orWhere('ip', "like", "%" . $request->search . "%");
                    $query->orWhere('port_number', "like", "%" . $request->search . "%");
                    $query->orWhere('remarks', "like", "%" . $request->search . "%");
                });
            }

            $adam = $adam->orWhereHas(
                'gates',
                function ($query) use ($request) {
                    $query->where('gate_name', 'like', "%{$request->search}%");
                }
            );

            if (isset($partner_id) && $partner_id != '') {
                if ($facility) {
                    $adam = $adam->where(function ($query) use ($partner_id, $facility) {
                        $query->where('partner_id', $partner_id);
                        $query->whereIn("facility_id", $facility);
                    });
                } else {
                    $adam = $adam->where(function ($query) use ($partner_id) {
                        $query->where('partner_id', $partner_id);
                    });
                }
            }
        }

        if (isset($partner_id) && $partner_id != '') {
            $adam = $adam->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
        }

        if ($request->sort != '') {
            $adam = $adam->orderBy($request->sort, $request->sortBy);
        } else {
            $adam = $adam->orderBy("id", "DESC");
        }
        $adam = $adam->paginate(10);
        return $adam;
    }



    public function store(Request $request)
    {
        $this->validate($request, Adam::$validation);
        if (Auth::User()->user_type == '12') {
            $partner_id = Auth::user()->created_by;
        } else {
            $partner_id = Auth::user()->id;
        }
        $gateExist = Adam::where(['facility_id' => $request->facility_id, 'gate_id' => $request->gate_id])->first();
        if ($gateExist) {
            throw new ApiGenericException("This gate is already assign to " . $gateExist->name . ".");
        }
        $adamRes = Adam::where(['ip' => $request->ip, 'port_number' => $request->port_number])->first();
        if (!$adamRes) {
            $data['facility_id'] = $request->facility_id;
            $data['partner_id'] = $partner_id;
            $data['gate_id'] = $request->gate_id;
            $data['name'] = $request->name;
            $data['ip'] = $request->ip;
            $data['port_number'] = $request->port_number;
            $data['remarks'] = $request->remarks;
            $data['is_active'] = $request->is_active;
            $adams = Adam::create($data);
            return $adams;
        } else {
            throw new ApiGenericException("Adam with same IP and Port already exist.");
        }
    }

    public function show($id)
    {
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }

        if (Auth::user()->user_type == '1') {
            $adam = Adam::with(['gates', 'facility'])->where('id', $id)->first();
        } else {
            $adam = Adam::with(['gates', 'facility'])->where('partner_id', $partner_id)->where('id', $id)->first();
        }
        if (!$adam) {
            throw new NotFoundException("Records not found.");
        }
        return $adam;
    }

    public function update(Request $request)
    {
        $adam = Adam::find($request->id);
        if (!$adam) {
            throw new ApiGenericException("Adam not found.");
        }

        $gateExist = Adam::where('facility_id', $request->facility_id)->where('gate_id', $request->gate_id)->where('id', '!=', $request->id)->first();
        if ($gateExist) {
            throw new ApiGenericException("This gate is already assign to " . $gateExist->name . ".");
        }

        if ($adam->port_number == $request->port_number && $adam->ip == $request->ip) {

            $adam->facility_id = $request->facility_id;
            $adam->gate_id = $request->gate_id;
            $adam->name = $request->name;
            $adam->ip = $request->ip;
            $adam->port_number = $request->port_number;
            $adam->remarks = $request->remarks;
            $adam->is_active = $request->is_active;
            $adam->save();
            return $adam;
        }

        if ($adam->port_number != $request->port_number && $adam->ip == $request->ip) {
            $adamRes = Adam::where('port_number', $request->port_number)->first();
            if ($adamRes) {
                throw new ApiGenericException("Port Number Already Exists.");
            } else {

                $adam->facility_id = $request->facility_id;
                $adam->gate_id = $request->gate_id;
                $adam->name = $request->name;
                $adam->ip = $request->ip;
                $adam->port_number = $request->port_number;
                $adam->remarks = $request->remarks;
                $adam->is_active = $request->is_active;
                $adam->save();
                return $adam;
            }
        }

        if ($adam->ip != $request->ip && $adam->port_number == $request->port_number) {
            $adamRes = Adam::where('ip', $request->ip)->where('port_number', $request->port_number)->first();
            if ($adamRes) {
                throw new ApiGenericException("Ip Address Already Exists.");
            } else {

                $adam->facility_id = $request->facility_id;
                $adam->gate_id = $request->gate_id;
                $adam->name = $request->name;
                $adam->ip = $request->ip;
                $adam->port_number = $request->port_number;
                $adam->remarks = $request->remarks;
                $adam->is_active = $request->is_active;
                $adam->save();
                return $adam;
            }
        }

        if ($adam->ip != trim($request->ip) && $adam->port_number != trim($request->port_number)) {

            $adamRes = Adam::where('ip', $request->ip)->where('port_number', $request->port_number)->first();
            if ($adamRes) {
                throw new ApiGenericException("Ip  Address and Port Number Already Exists.");
            } else {


                $adam->facility_id = $request->facility_id;
                $adam->gate_id = $request->gate_id;
                $adam->name = $request->name;
                $adam->ip = $request->ip;
                $adam->port_number = $request->port_number;
                $adam->remarks = $request->remarks;
                $adam->is_active = $request->is_active;
                $adam->save();
                return $adam;
            }
        }
    }

    public function destroy($id)
    {
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
        } else {
            if (Auth::user()->user_type == '1') {
            } else {
                if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
                    $partner_id = Auth::user()->created_by;
                } else {
                    $partner_id = Auth::user()->id;
                }
            }
        }
        if (Auth::user()->user_type == '1') {
            $adam = Adam::find($id);
        } else {
            $adam = Adam::where('partner_id', $partner_id)->find($id);
        }

        if ($adam) {
            $adam->delete();
            return "Data successfully deleted.";
        }
        throw new ApiGenericException("Adam not found.");
    }


    public function GetAdamConfig(Request $request)
    {
        $adamRes = Adam::where('facility_id', $request->facility_id)->first();
        if (isset($adamRes) && count($adamRes) > 0) {
            $adam = Adam::with(['gates'])->where('facility_id', $request->facility_id)->orderby('id', 'DESc')->get();
            return $adam;
        } else {
            return "Facility id not found.";
        }
    }
}
