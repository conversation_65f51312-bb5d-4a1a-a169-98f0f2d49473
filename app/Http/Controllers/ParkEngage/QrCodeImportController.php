<?php
/* 
  *  Copyright (c) 2024 ParkEngage Inc. All rights reserved.
  *  
  *  This software is the confidential and proprietary information of
  *  ParkEngage Inc. ("Confidential Information"). You shall not
  *  disclose such Confidential Information and shall use it only in
  *  accordance with the terms of the license agreement you entered into
  *  with ParkEngage Inc.
  *  
  *  This source code is a valuable trade secret of ParkEngage Inc.
  *  
  *  THIS SOFTWARE IS PROVIDED BY ParkEngage Inc. ``AS IS'' AND ANY
  *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  *  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  *  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL [YOUR COMPANY NAME] OR
  *  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  *  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  *  PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  *  OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *  
  *  The views and conclusions contained in the software and documentation
  *  are those of the authors and should not be interpreted as representing
  *  *official policies, either expressed or implied, of ParkEngage Inc.
*/

namespace App\Http\Controllers\ParkEngage;

use App\Exceptions\ApiGenericException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Facility;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\FacilityQrcode;
use App\Models\ParkEngage\Gate;
use App\Models\ParkEngage\MasterFacilityShortCode;
use App\Models\ParkEngage\ShortCodeFacilityMapping;
use App\Models\User;
use App\Services\LoggerFactory;
use App\Services\Pdf;
use Exception;
use Excel;
use Illuminate\Support\Facades\App;

class QrCodeImportController extends Controller
{
    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/parkengage/qrimport')->createLogger('qrimport_log_file');
    }

    /**
     * PIMS-13879
     * Import QR Codes from Excel file with duplicate checking
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
     public function importQrCodes(Request $request)
    {
        try {
            // Validate the uploaded file
            $this->validate($request, [
                'input_importfile' => 'required|mimes:csv,txt,xlsx,xls'
            ]);

            // Get the file
            $file = $request->file('input_importfile');
            $extension = strtolower($file->getClientOriginalExtension());

            // Log file details
            $this->log->info("Starting QR code import -- File: {$file->getClientOriginalName()}, Extension: {$extension}, Path: {$file->getRealPath()}");

            // Load the file using Laravel Excel 2.1
            $data = Excel::load($file->getRealPath(), function ($reader) use ($extension) {
                if ($extension === 'csv' || $extension === 'txt') {
                    $reader->setDelimiter(',')->setEnclosure('"');
                }
            })->get();

            if (!$data || $data->isEmpty()) {
                $this->log->warning("No data found in file -- File: {$file->getClientOriginalName()}");
                throw new Exception("No data found in the file");
            }

            // Log parsed data
            $this->log->info("Parsed CSV data -- " . json_encode($data->toArray()));

            $imported = 0;
            $skipped = 0;
            $duplicates = 0;
            $updated = 0;
            $processedCodes = [];

            // Process each row (no header skip)
            foreach ($data as $index => $row) {
                // Log each row
                $this->log->info("Processing row -- Index: {$index}, Data: " . json_encode($row->toArray()));

                // Access columns with case-insensitive fallbacks
                $qrCodeId = isset($row->{'qr_code_id'}) ? trim($row->{'qr_code_id'}) : 
                            (isset($row->{'QR Code ID'}) ? trim($row->{'QR Code ID'}) : '');
                $email = isset($row->{'partner_email'}) ? trim($row->{'partner_email'}) : 
                         (isset($row->{'Partner Email'}) ? trim($row->{'Partner Email'}) : '');

                // Log extracted data
                $this->log->info("Extracted row data -- Index: {$index}, QR Code ID: {$qrCodeId}, Email: {$email}");

                // Skip if QR Code ID is empty
                if (empty($qrCodeId)) {
                    $this->log->warning("Skipping row due to empty QR Code ID -- Index: {$index}");
                    $skipped++;
                    continue;
                }

                // Check for duplicates in the file
                if (in_array($qrCodeId, $processedCodes)) {
                    $this->log->warning("Duplicate QR Code ID found in file -- QR Code ID: {$qrCodeId}");
                    $duplicates++;
                    continue;
                }

                $processedCodes[] = $qrCodeId;

                // Find user by email
                $user = !empty($email) ? User::where('email', $email)->first() : null;
                $partnerId = $user ? $user->id : null;

                // Log user lookup result
                $this->log->info("User lookup -- Email: {$email}, User Found: " . (!is_null($user) ? 'Yes' : 'No') . ", Partner ID: " . ($partnerId ?? 'null'));

                // Check if QR code exists
                $existing = MasterFacilityShortCode::where('short_code', $qrCodeId)->first();

                if (!$existing) {
                    // Create new QR code
                    try {
                        MasterFacilityShortCode::create([
                            'short_code' => $qrCodeId,
                            'email' => !empty($email) ? $email : null,
                            'partner_id' => $partnerId,
                            'type' => 1,
                            'status' => 1
                        ]);
                        $imported++;
                        $this->log->info("Created new QR code -- QR Code ID: {$qrCodeId}");
                    } catch (Exception $e) {
                        $this->log->error("Failed to create QR code -- QR Code ID: {$qrCodeId}, Error: {$e->getMessage()}");
                        $skipped++;
                        continue;
                    }
                } else {
                    // Update existing QR code if needed
                    $needsUpdate = false;

                    if (empty($existing->email) && !empty($email)) {
                        $existing->email = $email;
                        $needsUpdate = true;
                    }

                    if (empty($existing->partner_id) && $partnerId) {
                        $existing->partner_id = $partnerId;
                        $needsUpdate = true;
                    }

                    if ($needsUpdate) {
                        try {
                            $existing->save();
                            $updated++;
                            $this->log->info("Updated QR code -- QR Code ID: {$qrCodeId}");
                        } catch (Exception $e) {
                            $this->log->error("Failed to update QR code -- QR Code ID: {$qrCodeId}, Error: {$e->getMessage()}");
                            $skipped++;
                            continue;
                        }
                    } else {
                        $this->log->info("Skipped existing QR code (no updates needed) -- QR Code ID: {$qrCodeId}");
                        $skipped++;
                    }
                }
            }

            $msg = "Imported {$imported}, Updated {$updated} QR codes successfully. Skipped {$skipped} entries. Found {$duplicates} duplicates in file.";
            $this->log->info("Import completed -- Imported: {$imported}, Updated: {$updated}, Skipped: {$skipped}, Duplicates: {$duplicates}");

            return $msg;

        } catch (Exception $e) {
            $this->log->error("QR Code Import Error -- Message: {$e->getMessage()}, Trace: {$e->getTraceAsString()}");
            throw new Exception("Error uploading QR codes series: {$e->getMessage()}");
        }
    }


    /**
     * Map QR Codes to Facility and Partner
     * PIMS-13879
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function qrcodeFaciltyMapping(Request $request)
    {
        try {
            // Validate the request
            $this->validate($request, [
                'partner_id' => 'required|integer|min:1',
                'facility_id' => 'required|integer|min:1',
                'qrcodes' => 'required|array|min:1',
                'qrcodes.*' => 'integer|min:1'
            ]);

            $partnerId = $request->partner_id;
            $facilityId = $request->facility_id;
            $qrCodeArrList = $request->qrcodes; // Array like [1, 2, 3, 4]

            $mapped = 0;
            $skipped = 0;
            $invalid = 0;

            // Get valid short_code IDs from master_facility_short_code
            $validShortCodeIds = MasterFacilityShortCode::whereIn('id', $qrCodeArrList)
                ->pluck('id')
                ->toArray();

            foreach ($qrCodeArrList as $shortCodeId) {
                // Check if short_code_id exists in master_facility_short_code
                if (!in_array($shortCodeId, $validShortCodeIds)) {
                    $invalid++;
                    continue;
                }

                // Check if mapping already exists
                $exists = ShortCodeFacilityMapping::where([
                    'short_code_id' => $shortCodeId,
                    'facility_id' => $facilityId,
                    'partner_id' => $partnerId
                ])->exists();

                if ($exists) {
                    $skipped++;
                    continue;
                }

                // Create new mapping
                ShortCodeFacilityMapping::create([
                    'short_code_id' => $shortCodeId,
                    'facility_id' => $facilityId,
                    'partner_id' => $partnerId
                ]);

                $mapped++;
            }
            $msg =  "Mapped {$mapped} QR codes successfully. Skipped {$skipped} existing mappings. {$invalid} invalid QR code IDs.";

            return $msg;
        } catch (\Exception $e) {
            throw new ApiGenericException("Error mapping QR codes");
        }
    }

    /**
     * Update QR Code Facility Mappings by ID
     * PIMS-13879
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateQrcodeFacilityMapping(Request $request, $id)
    {
        // Retrieve the existing mapping by ID
        $mapping = ShortCodeFacilityMapping::where('id', $id)
            ->whereNull('deleted_at')
            ->first();

        if (!$mapping) {
            throw new ApiGenericException("QR code mapping not found.");
        }

        // Fields allowed to be updated in ShortCodeFacilityMapping
        $updatableFields = ['partner_id', 'facility_id', 'short_code_id'];
        $dataToUpdate = [];

        // Populate only fields that are present in the request for ShortCodeFacilityMapping
        foreach ($updatableFields as $field) {
            if ($request->has($field)) {
                $dataToUpdate[$field] = $request->input($field);
            }
        }

        // Handle 'qrcodes' array if sent
        if ($request->has('qrcodes') && is_array($request->input('qrcodes')) && count($request->input('qrcodes')) > 0) {
            // Assuming the first QR code ID should be used as 'short_code_id'
            $dataToUpdate['short_code_id'] = $request->input('qrcodes')[0];
        }

        // Update status in MasterFacilityShortCode if provided
        if ($request->has('status')) {
            $masterFacilityShortCode = MasterFacilityShortCode::where('id', $mapping->short_code_id)
                ->where('partner_id', $mapping->partner_id)
                ->first();

            if (!$masterFacilityShortCode) {
                throw new ApiGenericException("Master facility short code not found.");
            }

            $masterFacilityShortCode->update(['status' => $request->input('status')]);
        }

        // Update ShortCodeFacilityMapping if there are valid fields
        if (!empty($dataToUpdate)) {
            $mapping->update($dataToUpdate);
        } elseif (!$request->has('status')) {
            throw new ApiGenericException("No valid fields provided for update.");
        }

        return response()->json(['message' => 'Updated successfully!'], 200);
    }



    /**
     * Delete QR Code Facility Mappings by ID
     * PIMS-13879
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroyQrcodeFacilityMapping(Request $request, $id)
    {
        try {
            // Find existing mapping by ID
            $mapping = ShortCodeFacilityMapping::where('id', $id)
                ->whereNull('deleted_at')
                ->first();

            if (!$mapping) {
                throw new ApiGenericException("Mapping not found or already deleted");
            }

            // Soft delete mapping
            $mapping->delete();
            $msg = 'QR code mapping deleted successfully';
            return $msg;
        } catch (\Exception $e) {
            throw new ApiGenericException("Error deleting QR code mapping");
        }
    }

    /**
     * Get Details of QR Code Facility Mappings by ID
     * PIMS-13879
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function listQrcodeFacilityMappings(Request $request, $page)
    {
        try {
            $perPage = $request->get('per_page', 20);

            $query = ShortCodeFacilityMapping::with(['shortCode', 'facility', 'user'])
                ->whereNull('deleted_at')
                ->whereHas('shortCode', function ($query) {
                    $query->where('type', 1);
                });

            if ($request->has('partner_id')) {
                $query->where('partner_id', $request->get('partner_id'));
            }

            if ($request->has('facility_id')) {
                $query->where('facility_id', $request->get('facility_id'));
            }

            if ($request->has('search') && !empty($request->search)) {
                $searchText = $request->get('search');
                $query->where(function ($q) use ($searchText) {
                    // Search by short_code
                    $q->whereHas('shortCode', function ($subQuery) use ($searchText) {
                        $subQuery->where('short_code', 'like', '%' . $searchText . '%');
                    })
                        // Search by facility name
                        ->orWhereHas('facility', function ($subQuery) use ($searchText) {
                            $subQuery->where('full_name', 'like', '%' . $searchText . '%');
                        })
                        // Search by user name
                        ->orWhereHas('user', function ($subQuery) use ($searchText) {
                            $subQuery->where('name', 'like', '%' . $searchText . '%');
                        });
                });
            }

            $query = $query->orderBy('id', 'desc');
            // Perform pagination at DB level
            $paginator = $query->paginate($perPage, ['*'], 'page', $page);

            // Transform items and set back on paginator
            $transformed = $paginator->getCollection()->map(function ($mapping) {
                return [
                    'id' => $mapping->id,
                    'short_code_id' => $mapping->short_code_id,
                    'facility_id' => $mapping->facility_id,
                    'partner_id' => $mapping->partner_id,
                    'created_at' => $mapping->created_at ? $mapping->created_at->toIso8601String() : null,
                    'updated_at' => $mapping->updated_at ? $mapping->updated_at->toIso8601String() : null,
                    'deleted_at' => $mapping->deleted_at ? $mapping->deleted_at->toIso8601String() : null,
                    'short_code' => $mapping->shortCode ?: null,
                    'facility' => $mapping->facility ?: null,
                    'user' => $mapping->user ?: null
                ];
            });

            $paginator->setCollection($transformed);

            // Return paginator with metadata and transformed data
            return response()->json([
                'total' => $paginator->total(),
                'per_page' => $paginator->perPage(),
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
                'next_page_url' => $paginator->nextPageUrl(),
                'prev_page_url' => $paginator->previousPageUrl(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
                'data' => $paginator->items()
            ]);
        } catch (\Exception $e) {
            throw new ApiGenericException("Error listing QR code mappings");
        }
    }

    /**
     * Display QR code facility mapping by ID.
     *
     * This method retrieves a mapping between a short code and a facility,
     * including related shortCode, facility, and user information,
     * provided the short code is of type 1 and the record has not been soft deleted.
     *
     * @param int $id The ID of the mapping to retrieve.
     * @return array The mapping data including relationships.
     * @throws ApiGenericException If any error occurs during the process.
     * PIMS-13879
     */
    public function showQrcodeFacilityMapping($id)
    {
        try {
            $query = ShortCodeFacilityMapping::with(['shortCode', 'facility', 'user'])
                ->whereNull('deleted_at')
                ->where('id', $id)
                ->whereHas('shortCode', function ($query) {
                    $query->where('type', 1);  // Filter shortCode with type = 1
                });

            $mappings = $query->get();

            $data = [];

            foreach ($mappings as $mapping) {
                $data[] =
                    [
                        'id' => $mapping->id,
                        'short_code_id' => $mapping->short_code_id,
                        'facility_id' => $mapping->facility_id,
                        'partner_id' => $mapping->partner_id,
                        'created_at' => $mapping->created_at ? $mapping->created_at->toIso8601String() : null,
                        'updated_at' => $mapping->updated_at ? $mapping->updated_at->toIso8601String() : null,
                        'deleted_at' => $mapping->deleted_at ? $mapping->deleted_at->toIso8601String() : null,
                        'short_code' => $mapping->shortCode ? $mapping->shortCode  : null,
                        'facility' => $mapping->facility ? $mapping->facility : null,
                        'user' => $mapping->user ? $mapping->user : null
                    ];
            }

            return $data;
        } catch (\Exception $e) {
            throw new ApiGenericException("Error listing QR code mappings");
        }
    }

    /**
     * Get Unmapped QR Code Series with Type 1
     * PIMS-13879
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnMappedQrCodeSeries($id)
    {
        try {
            // Get mapped short_code_ids from short_code_facility_mapping
            $mappedShortCodeIds = ShortCodeFacilityMapping::whereNull('deleted_at')
                ->pluck('short_code_id')
                ->toArray();

            // Get unmapped QR code series (type = 1) from master_facility_short_code
            $unmappedSeries = MasterFacilityShortCode::where('type', 1)
                ->whereNotIn('id', $mappedShortCodeIds)
                ->where('partner_id', $id)
                ->select('id', 'short_code', 'partner_id')
                ->orderBy('id', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'short_code' => $item->short_code,
                        'partner_id' => $item->partner_id
                    ];
                })
                ->toArray();

            if (count($unmappedSeries) == 0) {
                // throw new ApiGenericException("QR Code series not available!");
                if (count($unmappedSeries) == 0) {
                    return response()->json([
                        'status' => 404,
                        'data' => null,
                        'errors' => [
                            'message' => 'Sorry! No Data Found.'
                        ]
                    ], 404);
                }
            }

            return response()->json([
                'status' => 200,
                'data' => $unmappedSeries
            ], 200);
        } catch (\Exception $e) {
            throw new ApiGenericException("Error retrieving unmapped QR code series");
        }
    }

    /**
     * Generate QR Code PDF for a given facility mapping ID.
     *
     * This function retrieves the facility mapping, short code, and brand setting,
     * constructs a URL, and then generates a PDF containing the QR code and relevant information.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id The ID of the ShortCodeFacilityMapping record.
     * @return \Illuminate\Http\Response PDF response containing the QR code.
     * @throws ApiGenericException If any error occurs during processing.
     * PIMS-13879
     */
    public function genrateQrCode(Request $request, $id)
    {
        // try {
            $shortCodeMapping = ShortCodeFacilityMapping::with(['shortCode', 'facility'])
                ->whereNull('deleted_at')
                ->where('id', $id)
                ->whereHas('shortCode', function ($query) {
                    $query->where('type', 1);
                })->first();
            // return $shortCodeMapping;

            $getBrandSetting = BrandSetting::where('user_id', $shortCodeMapping->partner_id)->first();

            if (!$shortCodeMapping) {
                return response()->json([
                    'success' => false,
                    'message' => 'QR Code mapping not found.'
                ], 404);
            }

            $shortCode = MasterFacilityShortCode::where('id', $shortCodeMapping->short_code_id)->first();

            if (!$shortCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Short code not found.'
                ], 404);
            }

            $url = $getBrandSetting->url ? rtrim($getBrandSetting->url, '/') . '/' . $shortCode->short_code : $shortCode->short_code;

            $data = [
                'qrcode' => $url,
                'facility_name' => $shortCodeMapping->facility->full_name,
                'brand_setting' => $getBrandSetting
            ];

            $pdf = (new FacilityQrcode())->generatePdfForQRSeries($data, Pdf::class);
            return $pdf;
        // } catch (\Exception $e) {
        //     throw new ApiGenericException("Something went wrong");
        // }
    }

    /**
     * Download a sample CSV file for QR Code Mapping upload.
     *
     * This function generates and returns a downloadable CSV file
     * containing sample headers for QR code mapping (Email and QR Code ID).
     * Useful for users to understand the expected format for bulk uploads.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     * @throws \Exception If there is no data to export.
     * PIMS-13879
     */
    public function downloadSampleQrCodeMappingFile()
    {
        $excelSheetName = 'SampleQrCodeMapping'; // Cleaned filename for clarity
        $Columns = [
            [
                'Partner Email' => '',
                'QR Code ID' => ''
            ]
        ];

        return Excel::create(
            $excelSheetName,
            function ($excel) use ($Columns, $excelSheetName) {
                // Set the spreadsheet title, creator, and description
                $excel->setTitle($excelSheetName);
                $excel->setCreator('SampleQrCodeMappingList')->setCompany('ParkEngage');
                $excel->setDescription('Sample file of QR code Mapping Data');

                // Build the spreadsheet
                if (!empty($Columns)) {
                    $excel->sheet(
                        'QR code Mapping Data',
                        function ($sheet) use ($Columns) {
                            // Set the header row
                            $sheet->fromArray($Columns, null, 'A1', true, true);

                            // Auto-size columns for better visibility (affects processing, not CSV output)
                            $sheet->setAutoSize(true);

                            // Freeze first row (not applicable in CSV, but retained for consistency)
                            $sheet->freezeFirstRow();
                        }
                    );
                } else {
                    throw new Exception('Sorry! No Data Found.');
                }
            }
        )->download('csv'); // Download as CSV
    }

    /**
     * Download all QR codes of type 1 as a CSV file.
     *
     * This function fetches all short codes with `type = 1` from the `MasterFacilityShortCode` table,
     * formats them into a CSV with headers 'Email' and 'QR Code ID', and returns the file
     * as a downloadable response.
     *
     * @return \Illuminate\Http\Response CSV file download response.
     * @throws \App\Exceptions\ApiGenericException If any error occurs during CSV generation.
     * PIMS-13879
     */
    public function downloadQrCodes()
    {
        try {
            // Generate CSV content in memory
            $output = '';
            $file = fopen('php://temp', 'r+'); // Use a temporary stream

            // Add CSV headers
            fputcsv($file, ['Email', 'QR Code ID']);

            // Fetch data
            $qrCodes = MasterFacilityShortCode::select('email', 'short_code')->where('type', 1)->get();

            // Write data to CSV
            foreach ($qrCodes as $qrCode) {
                fputcsv($file, [
                    $qrCode->email,
                    $qrCode->short_code,
                ]);
            }

            // Get the CSV content
            rewind($file);
            $output = stream_get_contents($file);
            fclose($file);

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="qr_codes_' . date('Ymd_His') . '.csv"',
            ];

            return response($output, 200, $headers);
        } catch (\Exception $e) {
            throw new \App\Exceptions\ApiGenericException("Error generating QR codes CSV file");
        }
    }

    /**
     * Upload and process a CSV file to map QR Codes to facilities.
     *
     * The CSV must contain the columns: 'Partner ID', 'Facility ID', and 'QR Code ID'.
     * The function validates the file, parses its content, checks existing mappings and short codes,
     * and inserts new mappings if valid and not already existing.
     *
     * @param \Illuminate\Http\Request $request
     * @return string Summary message about the number of mappings processed.
     * @throws \App\Exceptions\ApiGenericException If file is invalid or an error occurs during processing.
     * PIMS-13879
     */
    public function qrcodeFacilityMappingCsv(Request $request)
    {
        try {
            // Validate the request
            $this->validate($request, [
                'input_importfile' => 'required|file|mimes:csv,txt|max:2048'
            ]);

            $file = $request->file('input_importfile');

            // Initialize counters
            $mapped = 0;
            $skipped = 0;
            $invalid = 0;

            // Read CSV file
            $csvData = array_map('str_getcsv', file($file->getRealPath()));

            // Assuming first row is header
            $header = array_shift($csvData);

            // Validate header
            if (!in_array('Partner Email', $header) || !in_array('Garage Code', $header) || !in_array('QR Code ID', $header)) {
                throw new ApiGenericException("CSV file must contain 'Partner Email', 'Garage Code', and 'QR Code ID' columns");
            }

            // Get column indices
            $partnerEmailIndex = array_search('Partner Email', $header);
            $garageCodeIndex = array_search('Garage Code', $header);
            $shortCodeIndex = array_search('QR Code ID', $header);

            // Collect unique values for lookups
            $shortCodes = [];
            $partnerEmails = [];
            $garageCodes = [];

            foreach ($csvData as $row) {
                if (!empty($row[$shortCodeIndex])) $shortCodes[] = $row[$shortCodeIndex];
                if (!empty($row[$partnerEmailIndex])) $partnerEmails[] = $row[$partnerEmailIndex];
                if (!empty($row[$garageCodeIndex])) $garageCodes[] = $row[$garageCodeIndex];
            }

            $shortCodes = array_unique($shortCodes);
            $partnerEmails = array_unique($partnerEmails);
            $garageCodes = array_unique($garageCodes);

            // Fetch reference data
            $partnerMap = User::whereIn('email', $partnerEmails)->pluck('id', 'email')->toArray();
            $facilityMap = Facility::whereIn('garage_code', $garageCodes)
                ->get()
                ->keyBy('garage_code');

            // Fetch existing short codes
            $existingShortCodes = MasterFacilityShortCode::whereIn('short_code', $shortCodes)
                ->get()
                ->keyBy('short_code');

            // Process each row
            foreach ($csvData as $row) {
                $shortCode = $row[$shortCodeIndex] ?? null;
                $partnerEmail = $row[$partnerEmailIndex] ?? null;
                $garageCode = $row[$garageCodeIndex] ?? null;

                if (empty($shortCode) || empty($partnerEmail) || empty($garageCode)) {
                    $invalid++;
                    continue;
                }

                if (!isset($partnerMap[$partnerEmail]) || !isset($facilityMap[$garageCode])) {
                    $invalid++;
                    continue;
                }

                $partnerId = $partnerMap[$partnerEmail];
                $facility = $facilityMap[$garageCode];

                // Check owner match
                if ($facility->owner_id != $partnerId) {
                    $skipped++;
                    continue;
                }

                $facilityId = $facility->id;

                // Insert short code if not exists
                if (!isset($existingShortCodes[$shortCode])) {
                    $newShortCode = MasterFacilityShortCode::create([
                        'short_code'    => $shortCode,
                        'partner_id'    => $partnerId,
                        'email'         => $partnerEmail,
                        'status'        => 1,
                        'type'          => 1,
                    ]);
                    $existingShortCodes[$shortCode] = $newShortCode;
                }

                $shortCodeRecord = $existingShortCodes[$shortCode];

                // Check partner match
                if ($shortCodeRecord->partner_id != $partnerId) {
                    $skipped++;
                    continue;
                }

                // Check if mapping exists
                $exists = ShortCodeFacilityMapping::where([
                    'short_code_id' => $shortCodeRecord->id,
                    'facility_id' => $facilityId,
                    'partner_id' => $partnerId
                ])->exists();

                if ($exists) {
                    $skipped++;
                    continue;
                }

                // Create new mapping
                ShortCodeFacilityMapping::create([
                    'short_code_id' => $shortCodeRecord->id,
                    'facility_id' => $facilityId,
                    'partner_id' => $partnerId
                ]);

                $mapped++;
            }

            $msg = "Mapped {$mapped} QR codes successfully. Skipped {$skipped} due to existing records, partner mismatch, or facility ownership mismatch. {$invalid} rows were invalid.";
            return $msg;
        } catch (\Exception $e) {
            throw new ApiGenericException("Error mapping QR codes from CSV: " . $e->getMessage());
        }
    }

    /**
     * Download a sample CSV file template for QR Code, Partner, and Facility mapping.
     *
     * This template helps users format their CSV uploads correctly by providing 
     * the required columns: 'Partner ID', 'Facility ID', and 'QR Code ID'.
     * The file is generated using the Laravel Excel package and downloaded as a CSV.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse CSV file download response.
     * @throws \Exception If column data is unexpectedly empty (unlikely due to hardcoded sample).
     * PIMS-13879
     */
    public function downloadSampleQrCodePartnerAndFacilityMappingFile()
    {
        $excelSheetName = 'SampleQrCodeMapping'; // Cleaned filename for clarity
        $Columns = [
            [
                'Partner Email' => '',
                'Garage Code' => '',
                'QR Code ID' => ''
            ]
        ];

        return Excel::create(
            $excelSheetName,
            function ($excel) use ($Columns, $excelSheetName) {
                // Set the spreadsheet title, creator, and description
                $excel->setTitle($excelSheetName);
                $excel->setCreator('SampleQrCodeMappingList')->setCompany('ParkEngage');
                $excel->setDescription('Sample file of QR code Mapping Data');

                // Build the spreadsheet
                if (!empty($Columns)) {
                    $excel->sheet(
                        'QR code Mapping Data',
                        function ($sheet) use ($Columns) {
                            // Set the header row
                            $sheet->fromArray($Columns, null, 'A1', true, true);

                            // Auto-size columns for better visibility (affects processing, not CSV output)
                            $sheet->setAutoSize(true);

                            // Freeze first row (not applicable in CSV, but retained for consistency)
                            $sheet->freezeFirstRow();
                        }
                    );
                } else {
                    throw new Exception('Sorry! No Data Found.');
                }
            }
        )->download('csv'); // Download as CSV
    }
}
