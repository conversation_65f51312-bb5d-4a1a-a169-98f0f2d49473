<?php
/*
 * -----------------------------------------------------------------------------
 * Copyright (c) 2024 ParkEngage Inc. All rights reserved.
 * -----------------------------------------------------------------------------
 *
 * This software is the confidential and proprietary information of
 * ParkEngage Inc. ("Confidential Information"). You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms
 * of the license agreement you entered into with ParkEngage Inc.
 *
 * This source code is a valuable trade secret of ParkEngage Inc.
 *
 * DISCLAIMER:
 * THIS SOFTWARE IS PROVIDED BY PARKENGAGE INC. "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL PARKENGAGE INC. OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are
 * those of the authors and should not be interpreted as representing official
 * policies, either expressed or implied, of ParkEngage Inc.
 * -----------------------------------------------------------------------------
 */

namespace App\Http\Controllers\ParkEngage;

use Illuminate\Support\Facades\Auth;
use Response;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Exception;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Requests;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use Illuminate\Support\Facades\Artisan;
use App\Exceptions\AuthorizeNetException;
use Illuminate\Support\Facades\Session;
use App\Classes\Inventory;
use App\Models\FacilityAvailability;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Models\CustomText;
use App\Models\UserPass;
use App\Models\PromoCode;
use Excel;
use PHPExcel_Worksheet_Drawing;
use App\Models\ParkEngage\RevenueBalance;
use App\Models\ParkEngage\RevenuePaymentHistory;
use App\Services\Pdf;
use App\Services\Image;
use App\Classes\ParkengageGateApi;
use App\Models\ParkEngage\TicketCitation;
use App\Models\ParkEngage\Warning;
use App\Models\BlackListedVehicle;
use App\Models\OauthClient;
use App\Models\ParkEngage\TransactionData;
use App\Models\PermitTicket;
use App\Models\PermitRequest;
use App\Models\PermitVehicle;
use App\Classes\PlanetPaymentGateway;
use App\Classes\DatacapPaymentGateway;
use App\Models\ParkEngage\TicketExtend;
use App\Classes\HeartlandPaymentGateway;
use App\Models\ParkEngage\HeartlandPaymentProfile;
use App\Exceptions\UserNotAuthorized;
use App\Models\ParkEngage\UserFacility;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\UserSession;
use App\Models\Devicetoken;
use App\Models\ParkEngage\DeviceTokenGateMapping;
use App\Models\PromoUsage;
use App\Models\ParkEngage\BrandSetting;
use App\Models\PermitVehicleMapping;
use App\Jobs\SendSms;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Models\Event;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Http\Helpers\GatewayHelper;
use App\Models\BusinessPolicy;

class PartnerCheckinCheckoutController extends Controller
{

    protected $log;
    protected $rate;
    protected $user;
    protected $partnerPaymentDetails;
    protected $authNet;
    protected $facility;
    protected $currentTime;
    protected $request;
    protected $log_sms;
    protected $cim;
    protected $attendantLog;
    protected $billingValidation;

    const RESERVATION_THRESHOLD_TYPE = 2;
    const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE = 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO = 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR = 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const MIN_AVAILABILITY  = 5;
    const LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const SOME_SPACE_MSG = "Some spots are available.";
    const No_SPACE_MSG = "Sold Out At This Time.";
    const NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const FACILITY_AVAILABLE = "Facility available for reservation.";
    const FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const FACILITY_COMING_SOON = "Facility coming soon.";

    const SHARE_TICKET_AMOUNT = 0;
    const SHARE_PASS_AMOUNT = 0;
    const GRACE_PERIOD = 60;   // Mints 
    const TOWNSEND_PARTNER_ID = 2980;

    const USERTYPE_BUSINESS = 10;
    const SUPERADMIN = 1;
    const SUBORDINATE = 4;
    const PARTNER = 3;
    const REGIONAL_MANAGER = 12;

    use DispatchesJobs;
    const QUEUE_NAME = 'sms-send';

    public function __construct(Request $request, LoggerFactory $logFactory, AuthorizeNet $authNet, Cim $cim)
    {
        $this->request = $request;
        $this->authNet = $authNet;
        $this->cim = $cim;
        $this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');;
        // Use these validation rules if new billing information is being sent through
        $this->billingValidation = PaymentProfile::$creditCardValidation;

        $this->log = $logFactory->setPath('logs/parkengage/touchless-flow')->createLogger('touchless-flow');
        $this->log_sms = $logFactory->setPath('logs/are/sms')->createLogger('sms');
        $this->attendantLog = $logFactory->setPath('logs/parkengage/attendant-payment')->createLogger('attendant');
    }

    public function index(Request $request)
    {

        /*if($request->sort != ''){
        $service = Service::with('membershipPlan')->orderBy($request->sort,$request->sortBy);
      }else{
        $service = Service::with('membershipPlan')->orderBy('id', 'Desc');
      }
      
      if ($request->search) {
        $service = QueryBuilder::buildSearchQuery($service, $request->search, Service::$searchFields);
        }
      return $service->paginate(20);*/
    }


    public function getPartnerFacility($id = '')
    {
        $facilty = '';
        $partner_id = isset($id) ? $id : Auth::user()->id;
        $userDetails = User::where('id', $partner_id)->where('status', 1)->first();
        if (!$userDetails) {
            throw new ApiGenericException('Invalid Partner.');
        }

        if (($userDetails->user_type == '4') || ($userDetails->user_type == '12')) {
            $partner_id =  $userDetails->created_by;
            $facilty = DB::table('user_facilities')->where('user_id', $userDetails->id)->whereNull('deleted_at')->pluck('facility_id');
        }

        $facilities = DB::table('facilities')
            ->select('id as val', 'full_name as name', 'is_gated_facility')
            ->where('owner_id', $partner_id)
            ->where('active', '=', '1');
        if (($userDetails->user_type == '4') || ($userDetails->user_type == '12')) {
            $facilities = $facilities->whereIn('id', $facilty);
        }
        $facilities = $facilities->get();
        if (!count($facilities)) {
            return response()->json([], 200);
        }
        return $facilities;
    }

    public function getFacilityEntryGates($facility_id)
    {
        $gates = DB::table('gates')
            ->select('gate as val', 'gate_name as name')
            ->where('facility_id', $facility_id)
            ->where('gate_type', 'entry')
            ->get();
        if ($gates) {
            return $gates;
        } else {
            throw new ApiGenericException('Gate not found for this facility.');
        }
    }

    public function getFacilityExitGates($facility_id)
    {
        $gates = DB::table('gates')
            ->select('gate as val', 'gate_name as name')
            ->where('facility_id', $facility_id)
            ->where('gate_type', 'exit')
            ->whereNull('deleted_at')
            ->get();
        if (!count($gates)) {
            return response()->json([], 200);
        }
        return $gates;
    }

    public function getCheckinCheckoutList(Request $request)
    {
        $facilities = [];
        $partner_id = '';
        if (Auth::user()->user_type == '4') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        } else if (Auth::user()->user_type == '12') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        } else if (Auth::user()->user_type == '1') {
            $partner_id = isset($request->partner_id) ? $request->partner_id : '';
        } else {
            $partner_id = isset($request->partner_id) ? $request->partner_id : Auth::user()->id;
        }

        $tickets = Ticket::with(['reservation', 'facility', 'user', 'userPass', 'transaction', 'permit']);

        if (isset($partner_id) && !empty($partner_id)) {
            $tickets = $tickets->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
        }
        if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
            $tickets = $tickets->where(function ($query) use ($facilities) {
                $query->whereIn('facility_id', $facilities);
            });
        }


        if ($request->search_filter == '1') {

            if (isset($request->ticket_number) && $request->ticket_number != '') {
                $tickets = $tickets->where('ticket_number', 'like', "%$request->ticket_number%");
            }
            if (isset($request->license_plate) && $request->license_plate != '') {
                $tickets = $tickets->where('license_plate', 'like', "%$request->license_plate%");
            }
            if (isset($request->phone_number) && $request->phone_number != ''  || isset($request->customer_name) && $request->customer_name != '' || isset($request->validated_by) && $request->validated_by != '') {
                $tickets = $tickets->WhereHas(
                    'user',
                    function ($query) use ($request) {
                        $query
                            ->where('phone', 'like', "%{$request->phone_number}%")
                            ->where('name', 'like', "%{$request->customer_name}%")
                            //  ->where('name', 'like', "%{$request->validated_by}%")
                            ->where('partner_id', isset($request->partner_id) ? $request->partner_id : Auth::user()->id);
                    }
                );
            }
            if (isset($request->is_validated) && $request->is_validated == '1') {
                $tickets = $tickets->where('paid_by', '<>', '');
            }
            if (isset($request->payment_status) && strtolower($request->payment_status) == 'processed') {
                $tickets = $tickets->where('anet_transaction_id', '<>', '');
            }
            if (isset($request->payment_status) && strtolower($request->payment_status) == 'authorized') {
                $tickets = $tickets->where('anet_transaction_id', '=', '')->where('card_last_four', '<>', '');
            }
        }


        if ($request->search) {

            $tickets = $tickets->where('ticket_number', 'like', "%$request->search%")->where('vp_device_checkin', '0')->where('is_checkin', '!=', '0');
            if ($tickets->count() == 0) {
                $tickets = $tickets->orwhere('license_plate', 'like', "%$request->search%")->where('vp_device_checkin', '0');
            }

            if (isset($request->facility_id) && !empty($request->facility_id)) {
                $tickets = $tickets->where(function ($query) use ($request, $partner_id) {
                    $query->where('partner_id', $partner_id)->where('facility_id', $request->facility_id);
                });
            }

            $tickets = $tickets->orWhereHas(
                'reservation',
                function ($query) use ($request) {
                    $query->where('ticketech_code', 'like', "%{$request->search}%");
                }
            );
            if ($tickets->count() == 0) {
                $tickets = $tickets->WhereHas(
                    'userPass',
                    function ($query) use ($request) {
                        $query->where('pass_code', 'like', "%{$request->search}%");
                    }
                );
            }
            $tickets = $tickets->where('is_checkin', '!=', '0')->orWhereHas(
                'user',
                function ($query) use ($request) {
                    $query
                        ->where('phone', 'like', "%{$request->search}%")
                        ->where('partner_id', isset($request->partner_id) ? $request->partner_id : Auth::user()->id)
                        ->orWhere('email', 'like', "%{$request->search}%");
                }
            );
            if (isset($partner_id) && !empty($partner_id)) {
                $tickets = $tickets->where(function ($query) use ($partner_id) {
                    $query->where('partner_id', $partner_id);
                });
            }
        }
        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date("Y-m-d", strtotime($request->from_date));
            $to_date = date("Y-m-d", strtotime($request->to_date));
            if ($request->checkout_without_checkin == '1') {
                $tickets = $tickets->whereDate('checkout_time', '>=', $from_date)->whereDate('checkout_time', '<=', $to_date);
            } else {

                if (isset($request->is_checkin_checkout)  && $request->is_checkin_checkout == '1') {
                    $tickets = $tickets->whereDate('checkout_time', '>=', $from_date);
                    $tickets = $tickets->where(function ($query) use ($to_date) {
                        $query->whereDate('checkout_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
                    });
                } else {
                    $tickets = $tickets->whereDate('checkin_time', '>=', $from_date);
                    $tickets = $tickets->where(function ($query) use ($to_date) {
                        $query->whereDate('checkin_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
                    });
                }
            }
        }
        //dd($request->all());
        //filter based on event or reservation
        if (isset($request->checkin_type_filter)  && isset($request->event_id)) {
            $tickets = $tickets->where('partner_id', $partner_id)->where('event_id', $request->event_id);
        } elseif (isset($request->checkin_type_filter)  && $request->checkin_type_filter == 'event') {
            $tickets = $tickets->where('partner_id', $partner_id)->whereNotNull('event_id');
        }
        if (isset($request->checkin_type_filter)  && $request->checkin_type_filter == 'against_reservation') {
            $tickets = $tickets->where('partner_id', $partner_id)->whereNull('event_id')->whereNotNull('reservation_id');
        }
        if ($request->facility_id != '') {
            $tickets = $tickets->where('partner_id', $partner_id)->where('vp_device_checkin', '0')->where("facility_id", $request->facility_id);
        } elseif (isset($partner_id) && !empty($partner_id)) {
            $tickets = $tickets->where('partner_id', $partner_id)->where('vp_device_checkin', '0');
        }

        // filter data only checkin 
        if (isset($request->is_checkin_checkout)  && $request->is_checkin_checkout == '0') {
            $tickets = $tickets->where('is_checkin', '1')->where('is_checkout', $request->is_checkin_checkout);
        }
        // filter data only checkout
        if (isset($request->is_checkin_checkout)  && $request->is_checkin_checkout == '1') {
            $tickets = $tickets->where('is_checkin', '1')->where('is_checkout', $request->is_checkin_checkout);
        }

        // filter on deveice type
        if (isset($request->device_type)  && $request->device_type != '') {
            $tickets = $tickets->where('device_type', $request->device_type);
        }


        if ($request->checkout_without_checkin == '1') {
            $tickets = $tickets->where('checkout_without_checkin', '1');
        } else {
            $tickets = $tickets->where('checkout_without_checkin', '0');
        }
        $tickets = $tickets->where('is_checkin', '!=', '0')->whereNull("permit_request_id");
        if ($request->sort != '') {
            $tickets = $tickets->orderBy($request->sort, $request->sortBy);
        } else {
            $tickets = $tickets->orderBy("id", "DESC");
        }

        $tickets = $tickets->paginate(20);

        foreach ($tickets as $ticket) {
            $overstayTotal = $overstayGrandTotal = $extendGrandTotal = $extendTotal = 0;

            if ($ticket->is_overstay == '1') {
                $overstayGrandTotal = OverstayTicket::where('ticket_number', $ticket->ticket_number)->sum('grand_total');
                $overstayTotal = OverstayTicket::where('ticket_number', $ticket->ticket_number)->sum('total');
                $ticket->total += $overstayTotal > 0 ? $overstayTotal : 0;
                $ticket->grand_total += $overstayGrandTotal > 0 ? $overstayGrandTotal : 0;
            } elseif ($ticket->is_extended == '1') {
                $extendGrandTotal = TicketExtend::where('ticket_number', $ticket->ticket_number)->sum('grand_total');
                $ticket->grand_total += $extendGrandTotal > 0 ? $extendGrandTotal : 0;
                $extendTotal = TicketExtend::where('ticket_number', $ticket->ticket_number)->sum('total');
                $ticket->total += $extendTotal > 0 ? $extendTotal : 0;
            }
        }
        return $tickets;
    }

    //VP6800 checkin checkout list
    public function getDriveUpCheckinCheckoutList(Request $request)
    {
        $facilities = [];
        if (Auth::user()->user_type == '4') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        }
        if (Auth::user()->user_type == '12') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        }
        if (Auth::user()->user_type == '1') {

            $partner_id = isset($request->partner_id) ? $request->partner_id : '';
        } else {
            $partner_id = isset($request->partner_id) ? $request->partner_id : Auth::user()->id;
        }

        $tickets = Ticket::leftJoin('anet_transactions', 'anet_transactions.id', '=', 'tickets.anet_transaction_id')
            ->join('facilities', 'facilities.id', '=', 'tickets.facility_id')
            // ->leftJoin('overstay_tickets', 'tickets.id', '=', 'overstay_tickets.ticket_id')
            ->leftJoin('users', 'users.id', '=', 'tickets.user_id')
            ->leftJoin('events', 'events.id', '=', 'tickets.event_id')
            ->select(
                "anet_transactions.anet_trans_id as transaction_id",
                "anet_transactions.ref_id",
                "users.phone",
                "tickets.ticket_number",
                "checkin_time",
                "checkout_time",
                'anet_transactions.payment_last_four as payment_last_four',
                "anet_transactions.card_type as card_type",
                'anet_transactions.expiration as expiration',
                "tickets.total as total_amount",
                "tickets.anet_transaction_id",
                "tickets.is_overstay",
                'anet_transactions.reader_used',
                "tickets.terminal_id",
                "tickets.vp_device_checkin",
                DB::raw("anet_transactions.response_message as status"),
                "tickets.id as ticket_id",
                "tickets.is_checkout",
                "tickets.is_checkin",
                "tickets.user_id",
                "tickets.partner_id",
                "users.email",
                "tickets.card_type as ticket_card_type",
                "tickets.expiry",
                "tickets.card_last_four",
                "tickets.license_plate",
                "tickets.facility_id",
                "tickets.estimated_checkout",
                "tickets.event_id",
                'facilities.timezone',
                "tickets.is_offline_payment",
                "facilities.full_name as facility_name",
                "events.title",
                "events.description",
                "events.slug",
                "tickets.grand_total" //grand_total added by Ashutosh 10-10-2023 for duplicate record

                // DB::raw("if(overstay_tickets.grand_total > 0 ,(tickets.grand_total + overstay_tickets.grand_total), tickets.grand_total) as grand_total")


            );
        $tickets = $tickets->whereNull('checkout_by');

        if (isset($partner_id) && !empty($partner_id)) {
            $tickets = $tickets->where(function ($query) use ($partner_id) {
                $query->where('tickets.partner_id', $partner_id);
            });
        }

        if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
            $tickets = $tickets->where(function ($query) use ($facilities) {
                $query->whereIn('tickets.facility_id', $facilities);
            });
        }

        if ($request->payment_last_four != '' && $request->card_type != '' && $request->expiration != '' && $request->checkin_between != '' && $request->facility_id != '') {


            $tickets = $tickets->Where('tickets.card_last_four', $request->payment_last_four)
                ->Where('tickets.card_type', $request->card_type)
                ->Where('tickets.expiry', $request->expiration)
                ->Where('tickets.facility_id', $request->facility_id);
            $checkinTime = explode("-", $request->checkin_between);
            $start_time =  date("H:i:s", strtotime($checkinTime[0]));
            $end_time = date("H:i:s", strtotime($checkinTime[1]));
            $tickets = $tickets->whereTime('tickets.checkin_time', '>=', $start_time)->whereTime('tickets.checkin_time', '<=', $end_time);
        }

        if ($request->payment_last_four != '' && $request->card_type != '' && $request->expiration != '' && $request->checkin_between != '') {


            $tickets = $tickets->Where('tickets.card_last_four', $request->payment_last_four)
                ->Where('tickets.card_type', $request->card_type)
                ->Where('tickets.expiry', $request->expiration);
            $checkinTime = explode("-", $request->checkin_between);
            $start_time =  date("H:i:s", strtotime($checkinTime[0]));
            $end_time = date("H:i:s", strtotime($checkinTime[1]));
            $tickets = $tickets->whereTime('tickets.checkin_time', '>=', $start_time)->whereTime('tickets.checkin_time', '<=', $end_time);
        }
        /* case 3 */


        if ($request->payment_last_four != '' && $request->card_type != '' && $request->expiration != '' && $request->checkin_between == '') {
            $tickets = $tickets->Where('tickets.card_last_four', $request->payment_last_four)
                ->Where('tickets.card_type', $request->card_type)
                ->Where('tickets.expiry', $request->expiration);
        }
        /* case 3  end*/

        /* case 2 */

        if ($request->facility_id != '' && $request->checkin_between != '' && $request->payment_last_four == '' && $request->card_type == '' && $request->expiration == '') {
            $tickets = $tickets->Where('tickets.facility_id', $request->facility_id);
            $checkinTime = explode("-", $request->checkin_between);

            $start_time =  date("H:i:s", strtotime($checkinTime[0]));
            $end_time = date("H:i:s", strtotime($checkinTime[1]));
            $tickets = $tickets->whereTime('tickets.checkin_time', '>=', $start_time)->whereTime('tickets.checkin_time', '<=', $end_time);
        }


        if ($request->facility_id != ''  && $request->payment_last_four != '' && $request->checkin_between == '' && $request->card_type == '' && $request->expiration == '') {
            $tickets = $tickets->Where('tickets.facility_id', $request->facility_id)->Where('tickets.card_last_four', $request->payment_last_four);
        }

        if ($request->facility_id != '' &&  $request->card_type != '' && $request->payment_last_four == '' && $request->checkin_between == ''  && $request->expiration == '') {
            $tickets = $tickets->Where('tickets.facility_id', $request->facility_id)->Where('tickets.card_type', $request->card_type);
        }

        if ($request->facility_id != '' &&  $request->expiration == '' && $request->card_type == '' && $request->payment_last_four == '' && $request->checkin_between == '') {
            $tickets = $tickets->Where('tickets.facility_id', $request->facility_id);
        }



        if ($request->payment_last_four != '' && $request->card_type != '' && $request->checkin_between == '' && $request->expiration == '' && $request->facility_id == '') {
            $tickets = $tickets->Where('tickets.card_last_four', $request->payment_last_four)->Where('tickets.card_type', 'like', "%$request->card_type%");
        }
        if ($request->payment_last_four != '' && $request->expiration != '' && $request->card_type == '' && $request->checkin_between == ''  && $request->facility_id == '') {
            $tickets = $tickets->where('tickets.card_last_four', $request->payment_last_four)->Where('tickets.expiry', $request->expiration);
        }

        //  /* case 2 end */

        if ($request->payment_last_four != '') {
            $tickets = $tickets->Where('tickets.card_last_four', $request->payment_last_four);
        }

        if ($request->card_type != '') {
            $tickets = $tickets->Where('tickets.card_type', 'like', "%$request->card_type%");
        }

        if ($request->expiration != '') {
            $tickets = $tickets->Where('tickets.expiry', $request->expiration);
        }
        if ($request->checkin_between != '') {
            $checkinTime = explode("-", $request->checkin_between);

            $start_time =  date("H:i:s", strtotime($checkinTime[0]));
            $end_time = date("H:i:s", strtotime($checkinTime[1]));
            $tickets = $tickets->whereTime('tickets.checkin_time', '>=', $start_time)->whereTime('tickets.checkin_time', '<=', $end_time);
        }

        // filter data only checkin 
        if (isset($request->is_checkin_checkout)  && $request->is_checkin_checkout == '0') {
            $tickets = $tickets->where('tickets.is_checkin', '1')->where('tickets.is_checkout', $request->is_checkin_checkout);
        }
        // filter data only checkout
        if (isset($request->is_checkin_checkout)  && $request->is_checkin_checkout == '1') {
            $tickets = $tickets->where('tickets.is_checkin', '1')->where('tickets.is_checkout', $request->is_checkin_checkout);
        }
        // filter for closed ticket
        if (isset($request->is_checkin_checkout)  && $request->is_checkin_checkout == '2') {
            $tickets = $tickets->whereNotNull('checkout_time');
        }


        // filter on deveice type
        if (isset($request->device_type)  && $request->device_type != '') {
            $tickets = $tickets->where('tickets.device_type', $request->device_type);
        }


        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->is_checkin_checkout)  && $request->is_checkin_checkout == '1') {

            if (isset($request->from_date) && $request->from_date != '') {
                $from_date = date("Y-m-d", strtotime($request->from_date));
                $to_date = date("Y-m-d", strtotime($request->to_date));
                $tickets = $tickets->whereDate('checkout_time', '>=', $from_date);
                $tickets = $tickets->where(function ($query) use ($to_date) {
                    $query->whereDate('checkout_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
                });
            }
        } else {
            if (isset($request->from_date) && $request->from_date != '') {
                $from_date = date("Y-m-d", strtotime($request->from_date));
                $to_date = date("Y-m-d", strtotime($request->to_date));
                $tickets = $tickets->whereDate('checkin_time', '>=', $from_date);
                $tickets = $tickets->where(function ($query) use ($to_date) {
                    $query->whereDate('checkin_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
                });
            }
        }

        // if ($request->search_filter == '1') {

        if (isset($request->ticket_number) && $request->ticket_number != '') {
            $tickets = $tickets->where('tickets.ticket_number', 'like', "%$request->ticket_number%");
        }
        if (isset($request->license_plate) && $request->license_plate != '') {
            $tickets = $tickets->where('tickets.license_plate', 'like', "%$request->license_plate%");
        }
        if (isset($request->phone_number) && $request->phone_number != ''  || isset($request->customer_name) && $request->customer_name != '' || isset($request->validated_by) && $request->validated_by != '') {
            $tickets = $tickets->WhereHas(
                'user',
                function ($query) use ($request) {
                    $query
                        ->where('users.phone', 'like', "%{$request->phone_number}%")
                        ->where('users.name', 'like', "%{$request->customer_name}%")
                        ->where('users.name', 'like', "%{$request->validated_by}%")
                        ->where('tickets.partner_id', isset($request->partner_id) ? $request->partner_id : Auth::user()->id);
                }
            );
        }
        if (isset($request->is_validated) && $request->is_validated == '1') {
            $tickets = $tickets->where('tickets.paid_by', '<>', '');
        }
        if (isset($request->payment_status) && strtolower($request->payment_status) == 'processed') {
            $tickets = $tickets->where('tickets.anet_transaction_id', '<>', '');
        }
        if (isset($request->payment_status) && strtolower($request->payment_status) == 'authorized') {
            $tickets = $tickets->whereNull('tickets.anet_transaction_id')->whereNotNull('tickets.card_last_four');
        }
        $tickets = $tickets->Where("tickets.is_checkin", '!=', '0');


        // }



        //return $tickets;
        if ($request->search != '') {

            $tickets = $tickets->where(function ($query) use ($request, $partner_id) {
                $query->where('tickets.card_type', "like", "%" . $request->search . "%");
                $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%")->Where("tickets.is_checkin", '!=', '0');
                $query->orWhere('tickets.ticket_number', "like", "%" . $request->search . "%")->Where("tickets.is_checkin", '!=', '0');
                $query->orWhere("tickets.license_plate", 'like', "%$request->search%")->Where("tickets.is_checkin", '!=', '0');
                $query->orWhere("users.phone", 'like', "%$request->search%")->Where("tickets.is_checkin", '!=', '0');
                $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%")->Where("tickets.is_checkin", '!=', '0');
                $query->Where("tickets.partner_id", $partner_id);
                $query->Where("tickets.vp_device_checkin", '1');
                $query->Where("tickets.is_checkin", '!=', '0');
            });
        } else {
            $tickets = $tickets->where('tickets.partner_id', $partner_id)->where('vp_device_checkin', '1');
        }
        //dd($request->event_id);
        if (isset($request->event_id) && !empty($request->event_id)) {
            $tickets = $tickets->where('tickets.event_id', $request->event_id);
        }
        $tickets = $tickets->where('vp_device_checkin', '1');
        if ($request->sort != '') {
            if ($request->sort == 'transaction_id') {
                $tickets = $tickets->orderBy("anet_transactions.anet_trans_id", $request->sortBy);
            }
            if ($request->sort == 'card_type') {
                $tickets = $tickets->orderBy("anet_transactions.card_type", $request->sortBy);
            }
            if ($request->sort == 'ticket_number') {
                $tickets = $tickets->orderBy("tickets.$request->sort", $request->sortBy);
            }
            if ($request->sort == 'checkin_time') {
                $tickets = $tickets->orderBy("tickets.$request->sort", $request->sortBy);
            }
            if ($request->sort == 'checkout_time') {
                $tickets = $tickets->orderBy("tickets.$request->sort", $request->sortBy);
            }
            if ($request->sort == 'facility_name') {
                $tickets = $tickets->orderBy("facilities.full_name", $request->sortBy);
            }
        } else {
            $tickets = $tickets->orderBy("tickets.id", "DESC");
        }

        /*$authenticated_user = Auth::user();
      $tickets = $tickets->where(function($query) use($authenticated_user) {
          $query->where('partner_id', $authenticated_user->id);
      });*/
        //return $tickets->toSql();

        $tickets = $tickets->paginate(20);
        foreach ($tickets as $key => $val) {
            if ($val->status == null) {
                if ($val->is_offline_payment == '1') {
                    $val->status = 'Offline';
                } else if ($val->is_offline_payment == '2' || $val->is_offline_payment == '3') {
                    $val->status = 'Offline/Processed';
                } else if ($val->anet_transaction_id != '') {
                    $val->status = 'Processed';
                } else if ($val->anet_transaction_id == '') {
                    if ($val->card_last_four != '') {
                        $val->status = 'Authorized';
                    } else {
                        $val->status = '';
                    }
                }
            } else if (strtolower($val->status) == 'approval' || strtolower($val->status) == 'approved') {
                $val->status = 'Processed';
            }
            // added the query to fixe the duplicate entry issue
            $overstayPrice = DB::table('overstay_tickets')->where('ticket_id', $val->ticket_id)->sum('grand_total');
            // $val->grand_total = number_format($val->grand_total + $overstayPrice, 2);
            $val->grand_total = sprintf("%.2f", ($val->grand_total + $overstayPrice));
        }
        return $tickets;
    }

    // Admin Detail Screen
    public function getCheckinCheckoutDetails($id)
    {
        $this->log->info("partner checkincheckoutController getCheckinCheckoutDetails {$id}");
        if (!in_array(Auth::user()->user_type, [1, 3, 4, 8, 10, 12])) {
            throw new UserNotAuthorized("You dont have permission to log in. Please contact your Admin for further details.");
        }

        if (Auth::user()->user_type == '1') {
        } else {
            if (Auth::user()->user_type == '4') {
                $admin_partner_id = Auth::user()->created_by;
                if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                } else {
                    $partner_id = Auth::user()->created_by;
                }
            } else if (Auth::user()->user_type == '12') {
                $partner_id = Auth::user()->created_by;
            } else if (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            } else {
                $partner_id = Auth::user()->created_by;
            }
        }

        if (Auth::user()->user_type == '1') {
            $ticket = Ticket::with(['facility', 'user', 'userPass', 'transaction', 'reservation.rate', 'ticketExtend', 'ticketExtend.transaction', 'overstay', 'reservation.transaction', 'PermitVehicle', 'permit', 'event', 'warning', 'voidSale', 'businessPolicy'])->where('id', $id)->first();
            $ticket->ticketadditionalinfo = $ticket->ticketadditionalinfo() ?? null;
            if (!empty($ticket->promocode)) {
                $promocodeid = isset($ticket->PromotionDetails) ? $ticket->PromotionDetails->id : null;
                if (!empty($promocodeid)) {

                    $promocodedetail = PromoCode::where('promotion_id', $promocodeid)->first();
                    if (!empty($promocodedetail)) {
                        $ticket->promocode = $promocodedetail->promocode;
                    }
                }
            }
        } else {
            $ticket = Ticket::with(['facility', 'user', 'userPass', 'transaction', 'reservation.rate', 'ticketExtend', 'ticketExtend.transaction', 'overstay', 'reservation.transaction', 'PermitVehicle', 'permit', 'event', 'warning', 'voidSale', 'businessPolicy'])->where('id', $id);
            $admin_partner_id = Auth::user()->created_by;
            if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
            } else {
                $ticket = $ticket->where('partner_id', $partner_id);
            }
            $ticket = $ticket->first();
            $ticket->ticketadditionalinfo = $ticket->ticketadditionalinfo() ?? null;
            if (!empty($ticket->promocode)) {
                $promocodeid = isset($ticket->PromotionDetails) ? $ticket->PromotionDetails->id : null;
                if (!empty($promocodeid)) {
                    $promocodedetail = PromoCode::where('promotion_id', $promocodeid)->first();
                    if (!empty($promocodedetail)) {
                        $ticket->promocode = $promocodedetail->promocode;
                    }
                }
            }
            // dd($ticket,$partner_id,$id);
        }

        if (!$ticket) {
            throw new NotFoundException('Please use valid ticket number.');
        }

        if ($ticket) {
            // $this->log->info("getCheckinCheckoutDetails " . json_encode($ticket));
            $this->log->info("is ticket checked out or not :  " . $ticket->is_checkout);
            // $processingFee = 0;
            // if ($ticket->facility->vp_device_checkin  == '1' || $ticket->facility->vp_device_checkin  == 1) {
            //   $processingFee = $ticket->facility->drive_up_processing_fee == '' ? 0.00 : $ticket->facility->drive_up_processing_fee;
            // } else {
            //   $processingFee = $ticket->facility->processing_fee == '' ? 0.00 : $ticket->facility->processing_fee;
            // }
            // $processingFee = $ticket->getProcessingFee();   // to get prcessing free channel wise need to
            $refundUser  = User::select('name')->where('id', $ticket->refund_by)->first();
            $ticket->refund_by_user = isset($refundUser->name) ? $refundUser->name : '';
            #PIMS-8246 dushyant autostart api if facility not present
            if (empty($ticket->facility_id) || $ticket->facility_id == null) {
                $ticket['total']            = '0.00';
                $ticket['payable_amount']   = '0.00';
                $ticket['processing_fee']   = '0.00';
                $ticket['tax_rate']         = '0.00';
                $ticket['parking_amount']   = '0.00';
                $ticket['paid_amount']      = '0.00';
                $ticket['discount_amount']  = '0.00';

                if ($ticket->is_offline_payment == '1') {
                    $ticket->status = 'Offline';
                } else if ($ticket->is_offline_payment == '2' || $ticket->is_offline_payment == '3') {
                    $ticket->status = 'Offline/Processed';
                } else if ($ticket->anet_transaction_id != '') {
                    $ticket->status = 'Processed';
                } else if ($ticket->anet_transaction_id == '') {
                    if ($ticket->card_last_four != '') {
                        $ticket->status = 'Authorized';
                    } else {
                        $ticket->status = '';
                    }
                    if (($ticket->refund_status == 'Refunded') && ($ticket->refund_transaction_id == NULL) && ($ticket->is_checkout == '0')) {
                        $ticket->status = 'Authorised';
                        $ticket->refund_status = 'Authorised';
                    } else  if (($ticket->refund_status == 'Refunded') && ($ticket->refund_transaction_id == NULL) && ($ticket->is_checkout == '1')) {
                        $ticket->status = 'Processed';
                        $ticket->refund_status = 'Processed';
                    }
                } else if (strtolower($ticket->transaction->response_message) == 'approval' || strtolower($ticket->transaction->response_message) == 'approved') {
                    $ticket->status = 'Processed';
                }
                return $ticket;
                $this->setPartnerCustomTimezone($ticket->partner_id);
            } else {
                $this->setCustomTimezone($ticket->facility_id);
            }
            $gates = Gate::where('facility_id', $ticket->facility_id)->get();
            if ($gates) {
                foreach ($gates as $key => $value) {
                    if ($value->gate == $ticket->checkin_gate) {
                        $ticket['checkin_gate_name'] = $value->gate_name;
                    }
                    if ($value->gate == $ticket->checkout_gate) {
                        $ticket['checkout_gate_name'] = $value->gate_name;
                    }
                }
            } else {
                $ticket['checkin_gate_name'] = '';
                $ticket['checkout_gate_name'] = '';
            }
            #PIMS-8246 dushyant if checkout facility different from checkin/qr facility
            if (!is_null($ticket->checkout_facility_id)) {
                $gatescheckout = Gate::where('facility_id', $ticket->checkout_facility_id)->get();
                if ($gatescheckout) {
                    foreach ($gatescheckout as $valueCheckout) {
                        if ($valueCheckout->gate == $ticket->checkout_gate) {
                            $ticket['checkout_gate_name'] = $valueCheckout->gate_name;
                        }
                    }
                }
            }

            $user = User::where('id', $ticket->paid_by)->first();

            $overstay = OverstayTicket::where('ticket_number', $ticket->ticket_number)->get();
            $ticket['overstay'] = $overstay;
            if ($user) {
                $ticket['paid_by'] = (isset($user->name) && $user->name != '') ? $user->name : 'Customer';
            } else {
                $ticket['paid_by'] = '';
            }
            if ($overstay) {
                $ticket['overstay_amount'] = isset($overstay->total) ? $overstay->total : "0.00";
            } else {
                $ticket['overstay_amount'] = "0.00";
            }
        }

        // $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        // Added this below case to handle checkout without checkin :checkout_without_checkin=='1'
        $diff_in_hours = 0;
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', !empty($ticket->checkin_time) ? $ticket->checkin_time : date('Y-m-d H:i:s'));
        $diff_in_hours = $ticket->getCheckOutCurrentTime(true);

        $this->log->info("get Diff in Hours : {$diff_in_hours}");
        $rateDiffInHour = $diff_in_hours;
        $this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

        $extendTicketAmount = $isMember = 0;
        if (isset($ticket->facility->is_gated_facility) && $ticket->is_checkout == '0' && empty($ticket->checkout_time) && $ticket->facility->is_gated_facility == '1') {
            if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {

                $this->log->info("getCheckinCheckoutDetails Get Price 111");

                $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
                if ($ticket->is_checkin == '0' && $ticket->is_checkout == '0' && $ticket->checkout_time != '') {
                    //zeag ticket
                    $rate = [];
                    $rate['price'] = $ticket->parking_amount;
                }
            } else {
                $this->log->info("getCheckinCheckoutDetails Get Price 222");
                $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, $isMember);
            }
            // $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
            if ($rate == false) {
                throw new ApiGenericException('Garage is currently closed.');
            }

            $priceBreakUp = $ticket->priceBreakUp($rate);  // 0 passed in case of checked out
            $this->log->info("getCheckinCheckoutDetails11 priceBreakUp " . json_encode($priceBreakUp));
        } else {
            $rate['price'] = $ticket->parking_amount;

            if (isset($ticket->facility->is_gated_facility) && $ticket->facility->is_gated_facility == '1') {
                \Log::info("=>>>>>>>>gated facility Partner Checkin checkout controller price break up ");
                $priceBreakUp = $ticket->priceBreakUp($rate, 0);  // 0 passed in case of checked out
                $this->log->info("getCheckinCheckoutDetails22 priceBreakUp " . json_encode($priceBreakUp));
            } else {
                \Log::info("================>ungated facility Partner Checkin checkout controller price break up ");
                $rate['price'] = $ticket->parking_amount;
                $priceBreakUp = $ticket->unGatedPriceBreakUp($rate, $ticket->facility, 'get', '0', 'adminDetail');
                $this->log->info("unGatedPriceBreakUp priceBreakUp " . json_encode($priceBreakUp));

                // ungated checkout time 
                $extendTickets = TicketExtend::where("ticket_number", $ticket->ticket_number)->orderBy("id", "DESC")->first();
                if ($extendTickets) {
                    $ticket->checkout_time = $extendTickets->checkout_time;
                }

                $extendedTickets = TicketExtend::where("ticket_number", $ticket->ticket_number)->orderBy("id", "DESC")->get();
                if ($extendedTickets) {
                    foreach ($extendedTickets as $extendedTicket) {
                        $extendTicketAmount = $extendTicketAmount + $extendedTicket['total'];
                    }
                }
            }
        }
        \Log::info("Partner Checkin checkout controller price break up " . json_encode($priceBreakUp));

        // $taxRate = $ticket->getTaxRate($rate);



        // $ticketPrice = $rate['price'] + $processingFee + $taxRate;
        if (isset($ticket->facility->is_gated_facility) && $ticket->facility->is_gated_facility == '1') {
            $ticket['overstay_amount'] = $priceBreakUp['overstay_amount'];
            if ($priceBreakUp['overstay_amount'] > 0) {
                $ticket['is_overstay']    = 1;
            }
            //
            //changes for IM30 if ant_transcation_id is null
            if (empty($ticket->anet_transaction_id) && $ticket->paid_type == '9' && $ticket->is_offline_payment == '0') {
                $ticket->grand_total  = '0.00';
            }
            $ticket['amount_paid']      = $ticket->grand_total > 0 ? $ticket->grand_total : '0.00';
        } else {
            $ticket['extend_amount'] = isset($priceBreakUp['extend_amount']) ? $priceBreakUp['extend_amount'] : '0.00';
            if (isset($priceBreakUp['extend_amount']) && $priceBreakUp['extend_amount'] > 0) {
                $ticket['is_extended'] = 1;
            }
            // $ticket['payable_amount']      = isset($priceBreakUp['amount_paid']) ? $priceBreakUp['amount_paid'] : '0.00';
            // $ticket['amount_paid'] = '0.00';
            if ($extendedTickets) {
                $ticket['extend_amount'] = $extendTicketAmount;
            }
        }

        if ($ticket->is_checkout == '0' && $ticket->is_checkin == '0') {
            $ticket['total']            = '';
            $ticket['payable_amount']   = '';
            $ticket['processing_fee']   = '';
            $ticket['tax_rate']         = '';
            $ticket['parking_amount']   = '';
            $ticket['paid_amount']      = '';
            $ticket['discount_amount']  = '';
            $ticket->amount_paid        = '';
        } else {

            // $ticket['payable_amount']   = $priceBreakUp['payable_amount'];
            if ($ticket['promocode'] != '' && $priceBreakUp['amount_paid'] == 0.00) {
                $ticket['total']            = $priceBreakUp['total'];
                $ticket['processing_fee']   = $priceBreakUp['processing_fee'];
                $ticket['tax_rate']         = $priceBreakUp['tax_rate'];
                $ticket['discount_amount']  = $priceBreakUp['discount_amount'];
            } else {
                $ticket['total']            = $priceBreakUp['total'];
                $ticket['processing_fee']   = $priceBreakUp['processing_fee'];
                $ticket['tax_rate']         = $priceBreakUp['tax_rate'];
                $ticket['discount_amount']  = $priceBreakUp['discount_amount'];
            }
            $ticket['parking_amount']   = $priceBreakUp['parking_amount'];
            $ticket['paid_amount']      = $priceBreakUp['paid_amount'];

            $ticket['additional_fee']   = $priceBreakUp['additional_fee'] ?? 0;
            $ticket['surcharge_fee']    = $priceBreakUp['surcharge_fee'] ?? 0;
            // if (isset($priceBreakUp['amount_paid'])) 
            // {
            //     $ticket->amount_paid     = $priceBreakUp['amount_paid'];
            // }
            if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                if (isset($ticket->ticketadditionalinfo) && ($ticket->anet_transaction_id == NULL)) {
                    $ticket['amount_paid']  = $ticket->ticketadditionalinfo->new_parking_amount + $ticket->ticketadditionalinfo->new_tax_amount + $ticket->ticketadditionalinfo->new_processing_fee;
                }
            }
        }

        if ($ticket->is_offline_payment == '1') {
            $ticket->status = 'Offline';
        } else if ($ticket->is_offline_payment == '2' || $ticket->is_offline_payment == '3') {
            $ticket->status = 'Offline/Processed';
        } else if ($ticket->anet_transaction_id != '') {
            $ticket->status = 'Processed';
        } else if ($ticket->anet_transaction_id == '') {
            if ($ticket->card_last_four != '') {
                $ticket->status = 'Authorized';
            } else {
                $ticket->status = '';
            }
            if (($ticket->refund_status == 'Refunded') && ($ticket->refund_transaction_id == NULL) && ($ticket->is_checkout == '0')) {
                $ticket->status = 'Authorised';
                $ticket->refund_status = 'Authorised';
            } else  if (($ticket->refund_status == 'Refunded') && ($ticket->refund_transaction_id == NULL) && ($ticket->is_checkout == '1')) {
                $ticket->status = 'Processed';
                $ticket->refund_status = 'Processed';
            }
        } else if (strtolower($ticket->transaction->response_message) == 'approval' || strtolower($ticket->transaction->response_message) == 'approved') {
            $ticket->status = 'Processed';
        }

        if ($ticket->is_extended == '1' && $ticket->ticketExtend->count() > 0) {
            foreach ($ticket->ticketExtend as $key => $extend) {
                if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                    $extend->grand_total = ($extend->new_parking_amount - $extend->discount_amount - $extend->validated_amount)  + $extend->tax_fee;
                } else {
                    $extend->grand_total = ($extend->grand_total - $extend->discount_amount - $extend->validated_amount) >= 0 ? ($extend->grand_total - $extend->discount_amount - $extend->validated_amount) : $extend->grand_total;
                }

                $extend->grand_total = sprintf("%.2f", $extend->grand_total);
                if ($extend->transaction == NULL) {
                    $datacapTransaction = DatacapTransaction::select(['id', 'user_id', 'ticket_id', 'name', 'partner_id', 'card_last_four as payment_last_four', 'card_type', 'card_name', 'expiry as expiration'])->where('ticket_id', $ticket->id)->first();
                    if ($datacapTransaction) {
                        $extend->setRelation('transaction', $datacapTransaction);
                    }
                }
            }
        }

        if (in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
            if (!empty($extendedTickets) && $extendedTickets->count() > 0) {
                $parkingAmount = 0;

                foreach ($extendedTickets as $key => $exted) {
                    $parkingAmount += ($exted->new_parking_amount);
                }
                $parkingAmount  += $rate['price'];
            } else {
                $parkingAmount  = $ticket->parking_amount;
            }
            $ticket->parking_amount = number_format($parkingAmount, 2);

            if (!empty($ticket->ticketadditionalinfo) && $ticket->ticketadditionalinfo->new_processing_fee > 0 && $ticket->ticketadditionalinfo->new_tax_amount > 0) {
                $ticket->total = $ticket->parking_amount + $ticket->ticketadditionalinfo->new_processing_fee + $ticket->ticketadditionalinfo->new_tax_amount;
            } else {
                // Old Flow : Sunil :Vijay no change new will handel here
            }
        }

        if (($ticket->total == $ticket->discount_amount) &&  empty($ticket->ticketadditionalinfo)) {
            $ticket->total = $ticket->parking_amount;
            $ticket->discount_amount = $ticket->parking_amount;
            $ticket->processing_fee = "0.00";
            $ticket->tax_fee = "0.00";
            $ticket->tax_rate = "0.00";
        }

        if (!empty($ticket->ticketadditionalinfo) && in_array($ticket->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
            $ticket->ticketadditionalinfo->new_discount_amount = $ticket->discount_amount;
        }

        if ($ticket->is_checkout == '0') {
            // $ticket->payable_amount = $ticket->amount_paid > 0 ? $ticket->amount_paid : $ticket->payable_amount;
            $ticket->payable_amount = $priceBreakUp['payable_amount'];
            $ticket->amount_paid = 0;
        } else if ($ticket->is_checkout == '1') {
            $ticket->payable_amount = 0;
            $ticket->amount_paid = $priceBreakUp['amount_paid'];;
        }


        // dd($ticket->total, $ticket->discount_amount, $ticket->amount_paid, $ticket->payable_amount);
        // PISM -13266 linked : UPBL - 134 : Sunil Done by vijay 
        // This restrict ticketadditionalinfo object
        if ($ticket->facility->tax_with_surcharge_enable == '1') {
            $ticket->net_parking_amount = isset($ticket->ticketadditionalinfo->new_parking_amount) ? $ticket->ticketadditionalinfo->new_parking_amount : 0;
            $ticket->makeHidden(['ticketadditionalinfo']);
            $ticket->total              = $priceBreakUp['total'];
            $ticket->amount_paid        = $priceBreakUp['amount_paid'];
            $ticket->payable_amount     = $priceBreakUp['payable_amount'];
        }

        //PIMS-14829 || Dev: Sagar|| 07/08/2025
        $ticket->facility->oversize_fee_amount_type = $ticket->facility->facilityConfiguration->oversize_fee_amount_type ?? 0;
        return $ticket;
    }

    function getProcessingFee($checkinData, $channel = null)
    {
        $processingFee = 0;
        if ($checkinData->vp_device_checkin  == '1' || $checkinData->vp_device_checkin  == 1) {
            $processingFee = $checkinData->facility->drive_up_processing_fee == '' ? 0.00 : $checkinData->facility->drive_up_processing_fee;
        } else {
            $processingFee = $checkinData->facility->processing_fee == '' ? 0.00 : $checkinData->facility->processing_fee;
        }
        return $processingFee;
    }

    protected function saveOverstayTicketDetails($ticket, $request)
    {
        //Vijay : 24-09-2023 : To handel overstay in case of multiple 
        $this->log->error('Partnercheckincheckout saveOverstayTicketDetails : ' . json_encode($request->all()));
        $discountAmount =  (isset($request->discount_amount) && $request->discount_amount > 0) ? $request->discount_amount : 0;

        $OverstayTicket = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();
        $estimatedCheckout = isset($OverstayTicket->estimated_checkout) ? $OverstayTicket->estimated_checkout : $ticket->estimated_checkout;
        $lengthInHours = $ticket->getCheckOutCurrentTime(true, $estimatedCheckout);

        $overstay = new OverstayTicket();
        $overstay->user_id      = $ticket->user_id > 0 ? $ticket->user_id : 0; // Vijay : to handel spot here pay from admin 
        $overstay->facility_id  = $ticket->facility_id;
        $overstay->length       = $lengthInHours;
        $overstay->total        = $request->total + $discountAmount;
        $overstay->grand_total  = $request->total;
        $overstay->discount_amount = $discountAmount;
        $overstay->ticket_number = $ticket->ticket_number;
        $overstay->ticket_id    = $ticket->id;
        $overstay->is_checkin   = '1';
        $overstay->tax_fee      = $request->tax_fee;
        $overstay->parking_amount   =  ($request->total - !empty($request->tax_fee) ? $request->tax_fee : 0);
        $overstay->check_in_datetime = date('Y-m-d H:i:s', strtotime($estimatedCheckout));
        $overstay->payment_date = date("Y-m-d H:i:s");
        //$overstay->checkout_datetime = $estimated_checkout;
        $overstay->comment = $request->remark;
        $overstay->save();
        return $overstay;
    }
    // Deployed : Vijay : 08-01-2024
    public function confirmCheckout(Request $request)
    {
        $this->log->error('confirmCheckout : ' . json_encode($request->all()));
        $discountAmount =  (isset($request->discount_amount) && $request->discount_amount > 0) ? $request->discount_amount : 0;
        $ticket = Ticket::with('facility.FacilityPaymentDetails', 'overstay')->where('id', $request->id)->first();
        $request->request->add(['ticket_number' => $ticket->ticket_number]);

        if (!$ticket) {
            throw new NotFoundException('Please use valid ticket number.');
        }

        if ($ticket->is_checkout == '1') {
            throw new NotFoundException('Ticket already checkout.');
        }
        $this->setCustomTimezone($ticket->facility_id);


        if ($request->phone != '') {
            $countryCode = '';

            // Get country Code
            $countryCode = QueryBuilder::appendCountryCode();

            $user = User::where("phone", $countryCode . $request->phone)->where("created_by", $ticket->partner_id)->first();
            if ($user) {
                throw new ApiGenericException('Phone already associated with other user.');
            }

            // checked condition of user for spothero 09-10-2023 by Ashutosh
            if (isset($ticket->user_id) && !empty($ticket->user_id)) {
                $ticketUser = User::where("id", $ticket->user_id)->first();
                $ticketUser->phone = $countryCode . $request->phone;
                $ticketUser->save();
            }
        }

        $this->facility = $facility = $ticket->facility;
        //check gate api
        if (isset($request->manual_checkout) && $request->manual_checkout == 1) {
            if (isset($request->direct_checkout) && $request->direct_checkout == 0) {
                if ($request->gate == '' || $request->gate == '0') {
                    throw new ApiGenericException('Please select gate.');
                }
                if ($ticket->facility->check_vehicle_enabled == 1) {
                    $gateStatus = $this->isParkEngageGateAvailable($ticket->facility_id, $request->gate, '');
                    if ($gateStatus == "true") {
                    } else {
                        throw new ApiGenericException('Currently Gate is not available.');
                    }
                }
            }


            if ($ticket->anet_transaction_id == '' && $ticket->is_offline_payment == '0' && $ticket->paid_by != '' && $request->is_overstay != "1") {

                // vikrant - 14-09-2023 change change when amount full validate and user checkout manually
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
                $processingFee = $this->getProcessingFee($ticket); // to get prcessing free channel wise need to

                $isMember = 0;
                $this->log->info("get Diff in Hours : {$diff_in_hours}");
                $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
                $this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

                if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {
                    $this->log->info("getCheckinCheckoutDetails Get Price 111");
                    $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
                } else {
                    $this->log->info("confirmCheckout Get Price 222");
                    $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false, false, true, false, 0, $isMember);
                }
                if ($rate == false) {
                    throw new ApiGenericException('Garage is currently closed.');
                }
                $this->rate = $rate;
                $this->log->error('confirmCheckout : rate  ' . $rate['price']);

                // 0 =>Amout 1 => Percentage as per admin confing.
                $tax_rate =  0;
                if ($ticket->facility->tax_rate_type == '0') {
                    if ($ticket->facility->tax_rate > 0) {
                        $tax_rate = $ticket->facility->tax_rate;
                    }
                } else {
                    if ($ticket->facility->tax_rate > 0) {
                        $tax_rate = number_format((($rate['price'] * $ticket->facility->tax_rate) / 100), 2);
                    }
                }
                $newPrice = 0;
                $ticketPrice = ($rate['price'] + $processingFee + $tax_rate);
                $this->log->info("PRINTE HOURS {$diff_in_hours}  AND  RATE {$ticketPrice}");

                $priceBreakUp = $ticket->priceBreakUp($rate);
                $this->log->info("getCheckinPaymentDetails priceBreakUp " . json_encode($priceBreakUp));

                $ticket->parking_amount  = $priceBreakUp['parking_amount'];
                $ticket->paid_amount     = $priceBreakUp['paid_amount'];
                $ticket->processing_fee  = $priceBreakUp['processing_fee'];
                $ticket->tax_fee         = $priceBreakUp['tax_rate'];
                $ticket->total           = $priceBreakUp['total'];
                $ticket->grand_total     = $priceBreakUp['payable_amount'];
                $ticket->length     = $rateDiffInHour;
                $newPrice = $priceBreakUp['payable_amount'];

                $this->log->info("manual getCheckinPaymentDetails parking_amount " . $ticket->parking_amount);
            }


            $transactionDataExist = TransactionData::where("ticket_id", $ticket->id)->first();
            if (!$transactionDataExist) {
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
                $processingFee = $this->getProcessingFee($ticket); // to get prcessing free channel wise need to

                $isMember = 0;
                $this->log->error("confirmCheckout : manual checkout rate for hours  {$diff_in_hours} and ticket {$ticket->ticket_number}");
                $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
                if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {
                    $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
                } else {
                    $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false, false, true, false, 0, $isMember);
                }

                //added condition in case for event payment by vikrant
                if ($ticket->anet_transaction_id == '') {
                    $this->rate = $rate;
                    $ticket->length = $rateDiffInHour;
                    $ticket->rate_id = isset($this->rate['id']) ? $this->rate['id'] : '';
                    $ticket->rate_description = isset($this->rate['description']) ? $this->rate['description'] : '';

                    //save transaction data with 0 amount for grace period
                    $saveTransactionData = new TransactionData();
                    $saveTransactionData->user_id = $ticket->user_id;
                    $saveTransactionData->facility_id = $ticket->facility->id;
                    $saveTransactionData->partner_id = $ticket->partner_id;
                    $saveTransactionData->ticket_id = $ticket->id;
                    $saveTransactionData->rate_id = isset($this->rate['id']) ? $this->rate['id'] : '';
                    $saveTransactionData->rate_description = isset($this->rate['description']) ? $this->rate['description'] : '';
                    $saveTransactionData->rate_amount = $this->rate['price'];
                    $saveTransactionData->total = $this->rate['price'];
                    $saveTransactionData->tax_fee = "0.00";
                    $saveTransactionData->processing_fee = "0.00";
                    $saveTransactionData->discount_amount = "0.00";
                    $saveTransactionData->grand_total = "0.00";
                    $saveTransactionData->save();

                    $ticket->total = "0.00";
                    $ticket->grand_total = "0.00";
                }
            }

            $ticket->checkout_time = date("Y-m-d H:i:s");
            $ticket->is_checkout = '1';
            $ticket->checkout_datetime = $ticket->checkout_datetime == '' ? date("Y-m-d H:i:s") : $ticket->checkout_datetime;
            $ticket->checkout_by = Auth::user()->id;
            $ticket->checkout_remark = isset($request->remark) ? $request->remark : 'Admin Remark Checkout';
            $ticket->checkout_gate = $request->gate;
            $ticket->checkout_mode = '5';

            $ticket->save();
            // Reservation is_ticket updated after checkout Ashutosh 29-09-2023
            if ($ticket->reservation_id != '') {
                $ticket->checkout_time = date('Y-m-d H:i:s');
                $reservation = Reservation::where('id', $ticket->reservation_id)->first();
                $reservation->is_ticket = 2;
                $reservation->save();
            }
            $this->log->error('manual confirmCheckout check data');
            return $ticket;
        }

        //overstay condition
        if ($request->is_overstay == "1") {

            if ($request->direct_checkout == '1' && $request->total == "0.00" && ($discountAmount <= 0)) {
                $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
                $ticket->is_checkout = '1';
                $ticket->checkout_datetime = $ticket->getCheckOutCurrentTime();
                $ticket->checkout_time = $ticket->getCheckOutCurrentTime();
                $ticket->checkout_by = Auth::user()->id;
                $ticket->checkout_remark = isset($request->remark) ? $request->remark : 'Admin Remark Checkout';
                $ticket->length = $diff_in_hours;
                $this->log->error('confirmCheckout check data before save :  ');
                $ticket->checkout_gate = $request->gate;
                $ticket->checkout_mode = '5';
                $ticket->save();

                if ($ticket->is_checkout == '1') {
                    if (isset($ticket->user_id) && $ticket->user_id > 0) {
                        $facilityName = ucwords($facility->full_name);
                        $user = User::find($ticket->user_id);
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                        if ($partnerDetails->user_id == self::TOWNSEND_PARTNER_ID) {
                            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                $join->on('user_facilities.user_id', '=', 'users.id');
                                $join->where('user_facilities.facility_id', "=", $facility->id);
                            })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                            $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                        } else {
                            $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        }
                        $url = env('RECEIPT_URL');
                        $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                        $this->customeReplySms($sms_msg, $user->phone);
                    }
                }
                return $ticket;
            }

            // commit due to overstay implementation for Sport herro reservation 
            // $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date); 
            $diff_in_hours = '';
            if (isset($ticket->payment_date) && !empty($ticket->payment_date)) {
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date);
                $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->payment_date);
            } else {
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->estimated_checkout);
                $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->estimated_checkout);
            }

            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

            $request->request->add(['length' => $diff_in_hours]);
            $isMember = 0;
            $this->log->info("get Diff in Hours : {$diff_in_hours}");
            $rateDiffInHour = $diff_in_hours;
            // $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
            $this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

            if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
                $this->log->info("getCheckinCheckoutDetails Get Price 111");
                $rate = $facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
            } else {
                $this->log->info("confirmCheckout Get Price 222");
                $rate = $facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, true, false, true, false, 0, $isMember);
            }
            $processingFee = 0.00;
            $tax_rate = 0.00;
            $parkingAmount = $rate['price'];
            $overstayTicket = [];
            $currentTime = Carbon::parse('now');
            $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');

            $planetTransactionId = 0;
            if (isset($request->only_payment) && $request->only_payment > 0) {

                $overstayDiscountAmount = 0;
                if ($request->discount_amount != '') {
                    $overstayDiscountAmount = $request->discount_amount;
                }
                if ($request->nonce != '') {

                    $this->setDecryptedCard($request);

                    if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                        throw new NotFoundException('Payment details not defined against this garage.');
                    }

                    if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                        $refundstatus = PlanetPaymentGateway::planetPaymentByCrediCard($ticket, $request->total, $request);
                        $this->log->info("Payment Response :" . json_encode($refundstatus));
                        if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                            throw new NotFoundException("Payment failed. Please try again.");
                        }
                        $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                        $planetTransactionId = $planetTransaction->id;
                    } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {

                        $card_month = substr($request->expiration_date, 0, 2);
                        $card_year = substr($request->expiration_date, -2);
                        $request->request->add(['expiration_month' => $card_month]);
                        $request->request->add(['expiration_year' => $card_year]);

                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        //datacap otu token
                        $datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($request, $ticket->facility);

                        if ($datacapPaymentToken["Token"]) {
                            $amount = number_format($request->total, 2);
                            $datacap['Amount'] = $amount;
                            $datacap['Token'] = $datacapPaymentToken["Token"];
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            if ($paymentResponse["Status"] == "Error") {
                                $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }
                            } else if ($paymentResponse["Status"] == "Approved") {
                                $card_last_four = substr($paymentResponse['Account'], -4);
                                $brand = str_replace('/', '', $paymentResponse['Brand']);
                                $request->request->add(['expiration' => $request->expiration_date]);
                                $request->request->add(['card_last_four' => $card_last_four]);
                                $user_id = $ticket->user_id;

                                $datacapTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($datacapTransaction));
                                $planetTransactionId = $datacapTransaction->id;
                                $ticket->checkout_cardholder_name = isset($request->name_on_card) ? $request->name_on_card : NULL;
                                $ticket->checkout_card_last_four = $card_last_four;
                                $ticket->checkout_expiry = isset($request->expiration_date) ? $request->expiration_date : NULL;
                                $ticket->checkout_card_type = $brand;
                                $ticket->checkout_session_id = $paymentResponse['Token'];
                                $ticket->payment_token = $paymentResponse['Token'];

                                $ticket->card_last_four = isset($ticket->card_last_four) ? $ticket->card_last_four : $card_last_four;
                                $ticket->expiry = isset($ticket->expiry) ? $ticket->expiry : $request->expiration_date;
                                $ticket->card_type = isset($ticket->card_type) ? $ticket->card_type : $brand;

                                $ticket->card_type = $brand;
                                $ticket->save();
                            } else {
                                throw new ApiGenericException("Error in Making Payment.");
                            }
                        } else {
                            throw new ApiGenericException("Payment Token Not Generated.");
                        }
                    } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                        $this->log->info("Make Heartland Payment new  ");
                        $paymentStatus = $this->makeHeartlandPayment($request);
                        $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                        $planetTransactionId = $paymentStatus->id;
                    }


                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->anet_transaction_id = $planetTransactionId;
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->is_offline_payment = '2';
                    $overstayTicket->tax_fee   = $request->tax_fee;
                    $overstayTicket->save();
                } elseif ($request->session_id != '') {

                    if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                        throw new NotFoundException('Payment details not defined against this garage.');
                    }

                    if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                        $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                        $this->log->info("Payment Response :" . json_encode($refundstatus));
                        if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                            throw new NotFoundException("Payment failed. Please try again.");
                        }
                        $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                        $planetTransactionId = $planetTransaction->id;
                    } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        $amount = number_format($request->total, 2);
                        $datacap['Amount'] = $amount;
                        $datacap['Token'] = $ticket->payment_token;
                        $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        } else if ($paymentResponse['Status'] == 'Approved') {
                            $user_id = $ticket->user_id;
                            $card_last_four = substr($paymentResponse['Account'], -4);
                            $brand = str_replace('/', '', $paymentResponse['Brand']);
                            $request->request->add(['expiration' => $ticket->expiry]);
                            $datacapTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                            $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($datacapTransaction));
                            $planetTransactionId = $datacapTransaction->id;

                            $ticket->checkout_card_last_four = $card_last_four;
                            $ticket->checkout_expiry = isset($request->expiration) ? $request->expiration : NULL;
                            $ticket->checkout_card_type = $brand;
                            $ticket->checkout_session_id = $paymentResponse['Token'];

                            $ticket->card_last_four = isset($ticket->card_last_four) ? $ticket->card_last_four : $card_last_four;
                            $ticket->expiry = isset($ticket->expiry) ? $ticket->expiry : $request->expiration_date;
                            $ticket->card_type = isset($ticket->card_type) ? $ticket->card_type : $brand;

                            $ticket->save();
                        } else {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        }
                    } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                        $request->request->add(['payment_profile_id' => $request->session_id]);
                        $this->log->info("Make Heartland Payment new  ");
                        $paymentStatus = $this->makeHeartlandPayment($request);
                        $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                        $planetTransactionId = $paymentStatus->id;
                    }

                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->anet_transaction_id = $planetTransactionId;
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->is_offline_payment = '3';
                    $overstayTicket->tax_fee   = $request->tax_fee;
                    $overstayTicket->save();
                } else {
                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->is_offline_payment = '1';
                    $overstayTicket->tax_fee   = $request->tax_fee;
                    $overstayTicket->save();
                }

                $currentTime = Carbon::parse('now');
                $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');

                $overstayTicket->estimated_checkout = $estimated_checkout;
                $overstayTicket->payment_date = date("Y-m-d H:i:s");
                $overstayTicket->save();
                $ticket->is_checkout = '0';

                //$ticket->estimated_checkout = date('Y-m-d H:i:s', strtotime($estimated_checkout));
                //$ticket->length = $ticket->getCheckOutCurrentTime(true);
                //$ticket->processing_fee = $ticket->processing_fee + $processingFee;
                //$ticket->tax_fee = $ticket->tax_fee + $tax_rate;
                //$ticket->grand_total = $ticket->grand_total + $request->total;
                //$ticket->total = $ticket->total + $request->total + $overstayDiscountAmount;
                //$ticket->grand_total = $ticket->grand_total;
                //$ticket->total = $ticket->total;
                //$ticket->admin_payment_comment = isset($request->remark) ? $request->remark : 'Admin Remark Only Pay';
                //$ticket->parking_amount = $ticket->parking_amount + $parkingAmount;
                $ticket->is_overstay = '1';
                //$ticket->estimated_checkout = $estimated_checkout;
                //$ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->save();

                $this->saveOverstayTransactionData($ticket, $overstayTicket);

                return $ticket;
            } else {
                $this->log->error('pay & checkout');
                $overstayDiscountAmount = 0;
                if ($request->discount_amount != '') {
                    $overstayDiscountAmount = $request->discount_amount;
                }

                if ($request->nonce) {
                    $this->setDecryptedCard($request);

                    if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                        throw new NotFoundException('Payment details not defined against this garage.');
                    }

                    if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                        $refundstatus = PlanetPaymentGateway::planetPaymentByCrediCard($ticket, $request->total, $request);
                        $this->log->info("Payment Response :" . json_encode($refundstatus));
                        if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                            throw new NotFoundException("Payment failed. Please try again.");
                        }
                        $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                        $planetTransactionId = $planetTransaction->id;
                    } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {

                        $card_month = substr($request->expiration_date, 0, 2);
                        $card_year = substr($request->expiration_date, -2);
                        $request->request->add(['expiration_month' => $card_month]);
                        $request->request->add(['expiration_year' => $card_year]);

                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        //datacap otu token
                        $datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($request, $ticket->facility);

                        if ($datacapPaymentToken["Token"]) {
                            $amount = number_format($request->total, 2);
                            $datacap['Amount'] = $amount;
                            $datacap['Token'] = $datacapPaymentToken["Token"];
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            if ($paymentResponse["Status"] == "Error") {
                                $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }
                            } else if ($paymentResponse["Status"] == "Approved") {
                                $card_last_four = substr($paymentResponse['Account'], -4);
                                $request->request->add(['expiration' => $request->expiration_date]);
                                $request->request->add(['card_last_four' => $card_last_four]);
                                $brand = str_replace('/', '', $paymentResponse['Brand']);

                                $ticket->checkout_cardholder_name = isset($request->name_on_card) ? $request->name_on_card : NULL;
                                $ticket->checkout_card_last_four = $card_last_four;
                                $ticket->checkout_expiry = isset($request->expiration) ? $request->expiration : NULL;
                                $ticket->checkout_card_type = $brand;
                                $ticket->checkout_session_id = $paymentResponse['Token'];
                                $ticket->payment_token = $paymentResponse['Token'];

                                $ticket->card_last_four = isset($ticket->card_last_four) ? $ticket->card_last_four : $card_last_four;
                                $ticket->expiry = isset($ticket->expiry) ? $ticket->expiry : $request->expiration_date;
                                $ticket->card_type = isset($ticket->card_type) ? $ticket->card_type : $brand;

                                $ticket->save();
                            } else {
                                throw new ApiGenericException("Error in Making Payment.");
                            }
                        } else {
                            throw new ApiGenericException("Payment Token Not Generated.");
                        }
                        /*
                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        $amount = number_format($request->total, 2);
                        $datacap['Amount'] = $amount;
                        $datacap['Token'] = $ticket->session_id;
                        $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }

                            if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;

                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $planetTransactionId = $planetTransaction->id;
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        }
                        */
                    } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                        $this->log->info("Make Heartland Payment new  ");
                        $paymentStatus = $this->makeHeartlandPayment($request);
                        $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                        $planetTransactionId = $paymentStatus->id;
                    }
                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->anet_transaction_id = $planetTransactionId;
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->payment_date = date("Y-m-d H:i:s");
                    //$overstayTicket->processing_fee = $processingFee;
                    $overstayTicket->tax_fee = $request->tax_fee;
                    $overstayTicket->save();
                    $ticket->is_offline_payment = '2';
                } elseif ($request->session_id != '') {

                    if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                        throw new NotFoundException('Payment details not defined against this garage.');
                    }

                    if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                        $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                        $this->log->info("Payment Response :" . json_encode($refundstatus));
                        if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                            throw new NotFoundException("Payment failed. Please try again.");
                        }
                        $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                        $planetTransactionId = $planetTransaction->id;
                    } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        $amount = number_format($request->total, 2);
                        $datacap['Amount'] = $amount;
                        $datacap['Token'] = $ticket->payment_token;
                        $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }

                            if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;
                                $card_last_four = substr($paymentResponse['Account'], -4);
                                $request->request->add(['card_last_four' => $card_last_four]);
                                $request->request->add(['expiration' => $ticket->expiry]);
                                $datacapTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($datacapTransaction));
                                $planetTransactionId = $datacapTransaction->id;

                                $brand = str_replace('/', '', $paymentResponse['Brand']);
                                $ticket->checkout_card_last_four = $card_last_four;
                                $ticket->checkout_expiry = isset($request->expiration) ? $request->expiration : NULL;
                                $ticket->checkout_card_type = $brand;
                                $ticket->checkout_session_id = $paymentResponse['Token'];

                                $ticket->card_last_four = isset($ticket->card_last_four) ? $ticket->card_last_four : $card_last_four;
                                $ticket->expiry = isset($ticket->expiry) ? $ticket->expiry : $request->expiration_date;
                                $ticket->card_type = isset($ticket->card_type) ? $ticket->card_type : $brand;

                                $ticket->card_type = $brand;
                                $ticket->save();
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        }
                    } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                        $request->request->add(['payment_profile_id' => $request->session_id]);
                        $this->log->info("Make Heartland Payment new  ");
                        $paymentStatus = $this->makeHeartlandPayment($request);
                        $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                        $planetTransactionId = $paymentStatus->id;
                    }

                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->anet_transaction_id = $planetTransactionId;
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->payment_date = date("Y-m-d H:i:s");
                    //$overstayTicket->processing_fee = $processingFee;
                    //$overstayTicket->tax_fee = $tax_rate;
                    $overstayTicket->save();
                    $ticket->is_offline_payment = '3';
                } else {
                    $this->log->error('offline ');
                    $ticket->is_offline_payment = '1';
                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->payment_date = date("Y-m-d H:i:s");
                    //$overstayTicket->anet_transaction_id = $planetTransaction->id;
                    //$overstayTicket->processing_fee = $processingFee;
                    //$overstayTicket->tax_fee = $tax_rate;
                    $overstayTicket->save();
                }


                // $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
                $ticket->is_checkout = '1';
                $ticket->checkout_datetime = $ticket->getCheckOutCurrentTime();
                $ticket->checkout_time = $ticket->getCheckOutCurrentTime();
                $ticket->checkout_by = Auth::user()->id;
                $ticket->checkout_remark = isset($request->remark) ? $request->remark : 'Admin Remark Checkout';
                //$ticket->length = $diff_in_hours;

                $ticket->checkout_gate = $request->gate;
                $currentTime = Carbon::parse('now');
                $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:s:i');
                //$ticket->estimated_checkout = date('Y-m-d H:s:i', strtotime($estimated_checkout));
                //$ticket->length = $ticket->getCheckOutCurrentTime(true);
                //$ticket->processing_fee = $ticket->processing_fee + $processingFee;
                //$ticket->tax_fee = $ticket->tax_fee + $tax_rate;
                //$ticket->grand_total = $ticket->grand_total + $request->total;
                //$ticket->total = $ticket->total + $request->total + $overstayDiscountAmount;
                //$ticket->parking_amount = $ticket->parking_amount + $parkingAmount;
                //$ticket->estimated_checkout = $estimated_checkout;
                //$ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->is_overstay = '1';
                $ticket->save();
                // $overstayTicket->length = $diff_in_hours;
                $overstayTicket->is_checkout = '1';
                $overstayTicket->checkout_datetime = $ticket->getCheckOutCurrentTime();
                $overstayTicket->rate_id = isset($rate['id']) ? $rate['id'] : '';
                $overstayTicket->rate_description = isset($rate['description']) ? $rate['description'] : '';
                $overstayTicket->save();



                $this->log->error('overstay confirmCheckout check data before save all checkout overstay :  ');

                $this->saveOverstayTransactionData($ticket, $overstayTicket);


                if ($ticket->is_checkout == '1') {
                    $ticket->checkout_mode = '5';
                    $ticket->save();
                    if (isset($request->direct_checkout) && $request->direct_checkout == 0) {
                        if ($request->gate == '' || $request->gate == '0') {
                            throw new ApiGenericException('Please select gate.');
                        }
                        $this->log->error('confirmCheckout checkout');
                        if ($ticket->facility->check_vehicle_enabled == 1) {
                            $gateStatus = $this->isParkEngageGateAvailable($ticket->facility_id, $request->gate, '');
                            if ($gateStatus == "true") {
                            } else {
                                throw new ApiGenericException('Currently Gate is not available.');
                            }
                        }
                    }

                    if (isset($ticket->user_id) && $ticket->user_id > 0) {
                        $facilityName = ucwords($facility->full_name);
                        $user = User::find($ticket->user_id);
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                        if ($partnerDetails->user_id == self::TOWNSEND_PARTNER_ID) {
                            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                $join->on('user_facilities.user_id', '=', 'users.id');
                                $join->where('user_facilities.facility_id', "=", $facility->id);
                            })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                            $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                        } else {
                            $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        }
                        $url = env('RECEIPT_URL');
                        $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                        $this->customeReplySms($sms_msg, $user->phone);
                    }
                }

                return $ticket;
            }
        }

        // vijay - 26-06-2023 change for only payment 0 => without only payment 1 => only payment
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours  = $ticket->getCheckOutCurrentTime(true);


        $isMember = 0;
        $this->log->error("confirmCheckout : rate for hours  {$diff_in_hours} ");
        // $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
        $rateDiffInHour = $diff_in_hours;
        $this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

        if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {
            $this->log->info("getCheckinCheckoutDetails Get Price 111");
            $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
        } else {
            $this->log->info("confirmCheckout Get Price 222");
            $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false, false, true, false, 0, $isMember);
        }
        if ($rate == false) {
            throw new ApiGenericException('Garage is currently closed.');
        }

        $this->rate = $rate;
        $this->log->error('confirmCheckout : rate  ' . $rate['price']);

        $processingFee  = $ticket->facility->getProcessingFee('0'); // to get prcessing free channel wise need to
        $tax_rate         = $ticket->facility->getTaxRate($rate); // to get prcessing free channel wise need to

        // 0 =>Amout 1 => Percentage as per admin confing.
        // $tax_rate =  0;
        // if ($ticket->facility->tax_rate_type == '0') {
        //   if ($ticket->facility->tax_rate > 0) {
        //     $tax_rate = $ticket->facility->tax_rate;
        //   }
        // } else {
        //   if ($ticket->facility->tax_rate > 0) {
        //     $tax_rate = number_format((($rate['price'] * $ticket->facility->tax_rate) / 100), 2);
        //   }
        // }
        // to get tax and prcessing free Service wise two params are required
        $tax_rate = $ticket->facility->getTaxRate($rate);
        $processingFee = $ticket->facility->getProcessingFee($rate);
        $newPrice = 0;
        $ticketPrice = ($rate['price'] + $processingFee + $tax_rate);

        $this->log->info("PRINTE HOURS {$diff_in_hours}  AND  RATE {$ticketPrice}");
        /*$priceBreakUp = $ticket->priceBreakUp($rate);
    $this->log->info("getCheckinPaymentDetails priceBreakUp " . json_encode($priceBreakUp));

    $ticket->parking_amount  = $priceBreakUp['parking_amount'];
    $ticket->paid_amount     = $priceBreakUp['paid_amount'];
    $ticket->processing_fee  = $priceBreakUp['processing_fee'];
    $ticket->tax_fee         = $priceBreakUp['tax_rate'];
    $ticket->total           = $priceBreakUp['total'];
    $ticket->grand_total     = $priceBreakUp['payable_amount'];
    $newPrice = $priceBreakUp['payable_amount'];

    $this->log->info("getCheckinPaymentDetails parking_amount " . $ticket->parking_amount);
    $this->log->info("getCheckinPaymentDetails paid_amount " . $ticket->parking_amount);
    $this->log->info("getCheckinPaymentDetails processing_fee " . $ticket->processing_fee);
    $this->log->info("getCheckinPaymentDetails total " . $ticket->total);
    $this->log->info("getCheckinPaymentDetails grand_total " . $ticket->grand_total);
    $this->log->info("getCheckinPaymentDetails newPrice " . $newPrice);
    */

        if ($ticket->paid_type == '0') {
            if ($rate['price'] > 0) {
                $ticket->total = ($rate['price'] + $processingFee + $tax_rate);
                $ticket->parking_amount = $rate['price'];
                $ticket->paid_amount =  $ticketPrice;
            } else {
                $ticket->total = '0.0';
                $ticket->parking_amount = '0.0';
                $ticket->paid_amount = '0.0';;
            }
            $ticket->grand_total = '0.0';
            $newPrice = '0.00';

            $this->log->info('confirmCheckout : ticket validate as full amount  ' . $newPrice);
        } else if ($ticket->paid_type == '1') { // Hours  
            $paidDiffInHours = $ticket->getDiffInHour($ticket->paid_hour);

            $diff_in_hours = $diff_in_hours - $paidDiffInHours;
            $this->log->error("confirmCheckout : rate for hours  {$diff_in_hours} ");
            $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
            $this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

            if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
                $this->log->info("getCheckinCheckoutDetails Get Price 111");
                $hoursRate = $facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
            } else {
                $this->log->info("confirmCheckout Get Price 222");
                $hoursRate = $facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, 0);
            }
            $this->log->info("Base Rate : {$rate['price']} New Rate : {$hoursRate['price']}");
            $discountPrice = $rate['price'] - $hoursRate['price'];
            if ($hoursRate['price'] > 0) {
                $ticket->paid_amount = $discountPrice + $processingFee + $tax_rate;
                $ticket->total = ($rate['price'] + $processingFee + $tax_rate);
                $ticket->grand_total = $ticket->grand_total > 0 ? $ticket->grand_total : $request->total;
                $ticket->parking_amount = $rate['price'];
                $ticket->length = $diff_in_hours;
                $newPrice = ($hoursRate['price'] + $processingFee + $tax_rate);
            } else {
                $ticket->paid_amount = $discountPrice;
                $ticket->total = ($rate['price'] + $processingFee + $tax_rate);
                $ticket->grand_total = "0.00";
                $ticket->parking_amount = $rate['price'];
                $ticket->length = $diff_in_hours;
                $newPrice = "0.00";
            }
            $this->log->info('confirmCheckout : ticket validate as hours ' . ' Validate Hours ' . $ticket->paid_hour . ' New Price ' . $newPrice);
        } else if ($ticket->paid_type == '2') { // Amount
            $ticket->total = $ticketPrice;
            $ticket->parking_amount = $rate['price'];
            // dd($ticketPrice, $ticket->grand_total, $ticket->paid_amount);
            if ($ticket->grand_total > 0) {
                $newPrice = ($ticketPrice) - ($ticket->grand_total + $ticket->paid_amount);
            } else {
                $newPrice = (($ticketPrice) - $ticket->paid_amount);
            }

            if ($ticket->grand_total > 0 && $request->total > 0) {
                $ticket->grand_total = number_format($ticket->grand_total + $request->total, 2);
            } else {
                $ticket->grand_total = $ticket->grand_total > 0 ? $ticket->grand_total : $request->total;
            }
            $this->log->info('confirmCheckout : ticket validate as Amount ' . ' Validate Amount ' . $ticket->paid_amount . ' New Price' . $newPrice);
        } else if ($ticket->paid_type == '3') { // Percentage
            $totalAmount = $rate['price'] + $facility->processing_fee + $tax_rate;

            $percentageAmount = number_format((($totalAmount * $ticket->paid_percentage) / 100), 2);
            $payableAmount = 0;
            if (($ticket->max_validated_amount > 0) && $ticket->max_validated_amount != '') {
                if ($percentageAmount <= $ticket->max_validated_amount) {
                    $payableAmount = number_format($totalAmount - $percentageAmount, 2);
                    $updatedPaidAmount = $percentageAmount;
                } else {
                    $payableAmount = number_format($totalAmount - $ticket->max_validated_amount, 2);
                    $updatedPaidAmount = $ticket->max_validated_amount;
                }
            } else {
                $payableAmount = number_format($totalAmount - $percentageAmount, 2);
                $updatedPaidAmount = $percentageAmount;
            }
            //update discount amount if percentage is 100 percent and discount is > 0 by vikrant 10-04-25 issue raised by fufa
            if ($request->discount_amount > 0 && $payableAmount > 0) {
                $payableAmount = $payableAmount - $request->discount_amount;
            }
            $ticket->total = ($rate['price'] + $processingFee + $tax_rate);
            if ($ticket->paid_percentage == '100') { // if 100 % then grand total is zero 
                if ($ticket->max_validated_amount > 0) {
                    $ticket->grand_total =  $payableAmount;
                    $newPrice = $payableAmount;
                } else {
                    $ticket->grand_total =  '0.0';
                    $newPrice = '0.00';
                }
            } else {
                $ticket->grand_total =  $ticket->grand_total > 0 ? $ticket->grand_total : $request->total;
                $newPrice = $payableAmount;
            }
            $ticket->parking_amount = $rate['price'];
            $ticket->paid_amount =  $updatedPaidAmount;

            $this->log->info('confirmCheckout : ticket validate as Percentage ' . ' Validate Percentage ' . $ticket->paid_percentage . ' New Price' . $newPrice);
        } else {
            if ($rate['price'] > 0) {
                if ($ticket->is_offline_payment == '0') {
                    $ticket->total = ($rate['price'] + $processingFee + $tax_rate);

                    $ticket->grand_total = ($rate['price'] + $processingFee + $tax_rate) - $discountAmount;
                    $ticket->parking_amount = $rate['price'];
                }

                $newPrice = ($rate['price'] + $processingFee + $tax_rate);
            } else {
                $ticket->total = '0.0';
                $ticket->grand_total = '0.0';
                $ticket->parking_amount = '0.0';
                $newPrice = '0.00';
            }
            $this->log->info("ADMIN CHECKOUT ELSE SECTION RATE : {$request->total}");
            $this->log->info("ADMIN CHECKOUT ELSE SECTION RATE111 : {$newPrice}");
        }
        // dd($diff_in_hours, $ticket->total, $ticket->grand_total, $newPrice);
        $this->log->info('confirmCheckout : before return ');
        // $this->log->info(json_encode($ticket));
        $ticket->processing_fee = $rate['price'] > 0 ? $processingFee : '0.0';
        $ticket->tax_fee = $tax_rate;
        if (isset($request->only_payment) && $request->only_payment > 0) {
            $planetTransaction = [];
            if ($request->nonce != '') {
                $this->setDecryptedCard($request);

                if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                    throw new NotFoundException('Payment details not defined against this garage.');
                }

                if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                    $refundstatus = PlanetPaymentGateway::planetPaymentByCrediCard($ticket, $request->total, $request);
                    $this->log->info("Payment Response :" . json_encode($refundstatus));
                    if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                        throw new NotFoundException("Payment failed. Please try again.");
                    }
                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                    $ticket->anet_transaction_id = $planetTransaction->id;
                    $ticket->card_last_four = $refundstatus["Response"]["Params"]["CardNumberLast4"];
                    $ticket->card_type = $refundstatus["Response"]["Params"]["CardSchemeName"];
                    $ticket->expiry = $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"];
                } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {

                    $card_month = substr($request->expiration_date, 0, 2);
                    $card_year = substr($request->expiration_date, -2);
                    $request->request->add(['expiration_month' => $card_month]);
                    $request->request->add(['expiration_year' => $card_year]);

                    $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                    //datacap otu token
                    $datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($request, $ticket->facility);
                    if ($datacapPaymentToken["Token"]) {
                        $amount = number_format($request->total, 2);
                        $datacap['Amount'] = $amount;
                        $datacap['Token'] = $datacapPaymentToken["Token"];
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        } else if ($paymentResponse["Status"] == "Approved") {
                            $card_last_four = substr($paymentResponse['Account'], -4);
                            $request->request->add(['expiration' => $request->expiration_date]);
                            $request->request->add(['card_last_four' => $card_last_four]);

                            $user_id = $ticket->user_id;

                            $datacapTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                            $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($datacapTransaction));
                            $ticket->anet_transaction_id = $datacapTransaction->id;

                            $brand = str_replace('/', '', $paymentResponse['Brand']);

                            $ticket->checkout_card_last_four = $card_last_four;
                            $ticket->checkout_expiry = isset($request->expiration) ? $request->expiration : NULL;
                            $ticket->checkout_card_type = $brand;
                            $ticket->checkout_session_id = $paymentResponse['Token'];
                            $ticket->payment_token = $paymentResponse['Token'];

                            $ticket->card_last_four = isset($ticket->card_last_four) ? $ticket->card_last_four : $card_last_four;
                            $ticket->expiry = isset($ticket->expiry) ? $ticket->expiry : $request->expiration_date;
                            $ticket->card_type = isset($ticket->card_type) ? $ticket->card_type : $brand;

                            $ticket->save();
                        } else {
                            throw new ApiGenericException("Error in Making Payment.");
                        }
                    } else {
                        throw new ApiGenericException("Payment Token Not Generated.");
                    }
                } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                    $this->log->info("Make Heartland Payment new  ");
                    $paymentStatus = $this->makeHeartlandPayment($request);
                    $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));

                    $ticket->anet_transaction_id = $paymentStatus->id;
                    $ticket->card_last_four = $paymentStatus->payment_last_four;
                    $ticket->card_type = $paymentStatus->card_type;
                    $ticket->expiry = $paymentStatus->expiration;
                }
                $ticket->discount_amount = $request->discount_amount;
                $ticket->is_offline_payment = '2';
            } elseif ($request->session_id != '') {

                if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                    throw new NotFoundException('Payment details not defined against this garage.');
                }
                if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                    $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                    $this->log->info("Payment Response :" . json_encode($refundstatus));
                    if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                        throw new NotFoundException("Payment failed. Please try again.");
                    }
                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                    $ticket->anet_transaction_id = $planetTransaction->id;
                    $ticket->card_last_four = $refundstatus["Response"]["Params"]["CardNumberLast4"];
                    $ticket->card_type = $refundstatus["Response"]["Params"]["CardSchemeName"];
                    $ticket->expiry = $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"];
                } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                    $this->log->info("datacap Payment  Data --");
                    $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                    $amount = number_format($request->total, 2);
                    $datacap['Amount'] = $amount;
                    $datacap['Token'] = $ticket->payment_token;
                    $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $datacap["CardHolderID"] = "Allow_V2";
                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                    $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                    if ($paymentResponse["Status"] == "Error") {
                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        } else {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        }
                    } else if ($paymentResponse['Status'] == 'Approved') {
                        $user_id = $ticket->user_id;
                        $card_last_four = substr($paymentResponse['Account'], -4);
                        $request->request->add(['card_last_four' => $card_last_four]);
                        $request->request->add(['expiration' => $ticket->expiry]);
                        $datacapTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                        $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($datacapTransaction));
                        $ticket->anet_transaction_id = $datacapTransaction->id;

                        $brand = str_replace('/', '', $paymentResponse['Brand']);

                        $ticket->checkout_card_last_four = $card_last_four;
                        $ticket->checkout_expiry = isset($request->expiration) ? $request->expiration : NULL;
                        $ticket->checkout_card_type = $brand;
                        $ticket->checkout_session_id = $paymentResponse['Token'];

                        $ticket->card_last_four = isset($ticket->card_last_four) ? $ticket->card_last_four : $card_last_four;
                        $ticket->expiry = isset($ticket->expiry) ? $ticket->expiry : $request->expiration_date;
                        $ticket->card_type = isset($ticket->card_type) ? $ticket->card_type : $brand;

                        $ticket->save();
                    } else {
                        throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                    }
                } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                    $request->request->add(['payment_profile_id' => $request->session_id]);
                    $this->log->info("Make Heartland Payment new  ");
                    $paymentStatus = $this->makeHeartlandPayment($request);
                    $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                    $ticket->anet_transaction_id = $paymentStatus->id;
                    $ticket->card_last_four = $paymentStatus->payment_last_four;
                    $ticket->card_type = $paymentStatus->card_type;
                    $ticket->expiry = $paymentStatus->expiration;
                }

                $ticket->discount_amount = $request->discount_amount;
                $ticket->is_offline_payment = '3';
            } else {
                $ticket->discount_amount = $request->discount_amount;
                $ticket->is_offline_payment = '1';
            }

            $currentTime = Carbon::parse('now');
            $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:s:i');
            $ticket->is_checkout = '0';

            $ticket->estimated_checkout = date('Y-m-d H:s:i', strtotime($estimated_checkout));
            $ticket->length = $ticket->getCheckOutCurrentTime(true);
            $processingFee = 0;

            if ($ticket->vp_device_checkin  == '1' || $ticket->vp_device_checkin  == 1) {
                $ticket->processing_fee =  $ticket->facility->getProcessingFee($rate, '0');;
            } else {
                $ticket->processing_fee = $ticket->facility->getProcessingFee($rate, '1');;
            }
            // $ticket->processing_fee = $processingFee;

            $ticket->tax_fee = $tax_rate;
            $ticket->admin_payment_comment = isset($request->remark) ? $request->remark : 'Admin Remark Only Pay';
            $ticket->grand_total = $request->total;

            $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
            $ticket->rate_id = isset($this->rate['id']) ? $this->rate['id'] : '';
            $ticket->rate_description = isset($this->rate['description']) ? $this->rate['description'] : '';

            $this->log->error('confirmCheckout check data before save :  ');

            $newTotal = $request->total;
            if ($request->discount_amount != '' || $request->discount_amount > 0) {
                $newTotal = $request->total + $request->discount_amount;
            }

            if (filter_var($newPrice, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $newTotal) {
                throw new ApiGenericException(
                    'Sent rate does not match database rate, please refresh and try again.',
                    422,
                    ['sent_rate' => $request->total, 'database_rate' => $newPrice, 'isratemismatch' => 1]
                );
            }

            //}
            $ticket->payment_date = date("Y-m-d H:i:s");
            $ticket->payment_by = Auth::user()->id;
            $ticket->save();
            // process report data for now specific to twonsend ; 
            $transactionData = TransactionData::where("ticket_id", $ticket->id)->first();
            if (!$transactionData) {
                $this->saveTransactionData();
            }
        } else {
            $onlyCheckout = 0;
            if ($request->nonce) {
                $this->setDecryptedCard($request);

                if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                    throw new NotFoundException('Payment details not defined against this garage.');
                }

                if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                    $refundstatus = PlanetPaymentGateway::planetPaymentByCrediCard($ticket, $request->total, $request);
                    $this->log->info("Payment Response :" . json_encode($refundstatus));
                    if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                        throw new NotFoundException("Payment failed. Please try again.");
                    }
                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                    $ticket->anet_transaction_id = $planetTransaction->id;
                    $ticket->card_last_four = $refundstatus["Response"]["Params"]["CardNumberLast4"];
                    $ticket->card_type = $refundstatus["Response"]["Params"]["CardSchemeName"];
                    $ticket->expiry = $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"];
                } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {

                    $card_month = substr($request->expiration_date, 0, 2);
                    $card_year = substr($request->expiration_date, -2);
                    $request->request->add(['expiration_month' => $card_month]);
                    $request->request->add(['expiration_year' => $card_year]);

                    $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                    //datacap otu token
                    $datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($request, $ticket->facility);
                    if ($datacapPaymentToken["Token"]) {
                        $amount = number_format($request->total, 2);
                        $datacap['Amount'] = $amount;
                        $datacap['Token'] = $datacapPaymentToken["Token"];
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                        $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        } else if ($paymentResponse["Status"] == "Approved") {
                            $card_last_four = substr($paymentResponse['Account'], -4);
                            $request->request->add(['expiration' => $request->expiration_date]);
                            $request->request->add(['card_last_four' => $card_last_four]);

                            $user_id = $ticket->user_id;

                            $datacapTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                            $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($datacapTransaction));
                            $ticket->anet_transaction_id = $datacapTransaction->id;
                            //  $ticket->card_last_four = $request->card_last_four;
                            //$ticket->card_type = $request->expiration;
                            // $ticket->expiry = $request->expiration_date;
                            $request->request->add(['expiration' => $request->expiration_date]);
                            $brand = str_replace('/', '', $paymentResponse['Brand']);

                            $ticket->checkout_card_last_four = $card_last_four;
                            $ticket->checkout_expiry = isset($request->expiration) ? $request->expiration : NULL;
                            $ticket->checkout_card_type = $brand;
                            $ticket->checkout_session_id = $paymentResponse['Token'];
                            $ticket->payment_token = $paymentResponse['Token'];
                            $ticket->card_last_four = isset($ticket->card_last_four) ? $ticket->card_last_four : $card_last_four;
                            $ticket->expiry = isset($ticket->expiry) ? $ticket->expiry : $request->expiration_date;
                            $ticket->card_type = isset($ticket->card_type) ? $ticket->card_type : $brand;

                            $ticket->save();
                        } else {
                            throw new ApiGenericException("Error in Making Payment.");
                        }
                    } else {
                        throw new ApiGenericException("Payment Token Not Generated.");
                    }
                } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                    $this->log->info("Make Heartland Payment new  ");
                    $paymentStatus = $this->makeHeartlandPayment($request);
                    $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));

                    $ticket->anet_transaction_id = $paymentStatus->id;
                    $ticket->card_last_four = $paymentStatus->payment_last_four;
                    $ticket->card_type = $paymentStatus->card_type;
                    $ticket->expiry = $paymentStatus->expiration;
                }


                $ticket->discount_amount = $request->discount_amount;
                $ticket->is_offline_payment = '2';

                $ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->payment_by = Auth::user()->id;
            } elseif ($request->session_id != '') {

                if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                    throw new NotFoundException('Payment details not defined against this garage.');
                }

                if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                    $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                    $this->log->info("Payment Response :" . json_encode($refundstatus));
                    if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                        throw new NotFoundException("Payment failed. Please try again.");
                    }
                    $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                    $ticket->anet_transaction_id = $planetTransaction->id;
                } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                    $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                    $amount = number_format($request->total, 2);
                    $datacap['Amount'] = $amount;
                    $datacap['Token'] = $ticket->payment_token;
                    $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $datacap["CardHolderID"] = "Allow_V2";
                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                    $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                    if ($paymentResponse["Status"] == "Error") {
                        $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        } else {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        }
                    } elseif ($paymentResponse['Status'] == 'Approved') {
                        $user_id = $ticket->user_id;
                        $card_last_four = substr($paymentResponse['Account'], -4);
                        $request->request->add(['expiration' => $ticket->expiry]);
                        $request->request->add(['card_last_four' => $card_last_four]);

                        $datacapTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                        //$this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                        $ticket->anet_transaction_id = $datacapTransaction->id;

                        $brand = str_replace('/', '', $paymentResponse['Brand']);

                        $ticket->checkout_card_last_four = $card_last_four;
                        $ticket->checkout_expiry = isset($request->expiration) ? $request->expiration : NULL;
                        $ticket->checkout_card_type = $brand;
                        $ticket->checkout_session_id = $paymentResponse['Token'];

                        $ticket->card_last_four = isset($ticket->card_last_four) ? $ticket->card_last_four : $card_last_four;
                        $ticket->expiry = isset($ticket->expiry) ? $ticket->expiry : $request->expiration_date;
                        $ticket->card_type = isset($ticket->card_type) ? $ticket->card_type : $brand;

                        $ticket->save();
                    } else {
                        throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                    }
                } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                    $request->request->add(['payment_profile_id' => $request->session_id]);
                    $this->log->info("Make Heartland Payment new  ");
                    $paymentStatus = $this->makeHeartlandPayment($request);
                    $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                    $ticket->anet_transaction_id = $paymentStatus->id;
                    $ticket->card_last_four = $paymentStatus->payment_last_four;
                    $ticket->card_type = $paymentStatus->card_type;
                    $ticket->expiry = $paymentStatus->expiration;
                }
                //$ticket->anet_transaction_id = $planetTransaction->id;
                $ticket->discount_amount = $request->discount_amount;
                $ticket->is_offline_payment = '3';

                //$ticket->card_last_four = $refundstatus["Response"]["Params"]["CardNumberLast4"];
                //$ticket->card_type = $refundstatus["Response"]["Params"]["CardSchemeName"];
                //$ticket->expiry = $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"];
                $ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->payment_by = Auth::user()->id;
            } else {
                $ticket->is_offline_payment = '1';
                $onlyCheckout = 1;
                $ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->discount_amount = $discountAmount;
                $ticket->payment_by = Auth::user()->id;
            }

            $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
            $ticket->is_checkout = '1';
            $ticket->checkout_datetime = $ticket->getCheckOutCurrentTime();
            $ticket->checkout_time = $ticket->getCheckOutCurrentTime();
            $ticket->estimated_checkout = $ticket->getCheckOutCurrentTime();
            $ticket->checkout_by = Auth::user()->id;
            $ticket->checkout_remark = isset($request->remark) ? $request->remark : 'Admin Remark Checkout';
            $ticket->length = $diff_in_hours;
            $ticket->checkout_mode = '5';
            $this->log->error('confirmCheckout check data before save :  ');
            // $this->log->error('confirmCheckout : rate  ' . json_encode($ticket));
            //dd($ticket->anet_transaction_id, $ticket->estimated_checkout, date("Y-m-d H:i:s"));
            if (($ticket->is_offline_payment == '1') && (strtotime($ticket->estimated_checkout)) >= strtotime(date("Y-m-d H:i:s"))) {
            } else {
                if (($ticket->anet_transaction_id != '') && (strtotime($ticket->estimated_checkout)) >= strtotime(date("Y-m-d H:i:s"))) {
                } else {
                    $newTotal = $request->total;
                    if ($request->discount_amount != '' || $request->discount_amount > 0) {
                        $newTotal = $request->total + $discountAmount;
                    }
                    if ($newPrice <= 0) {
                        $newPrice = '0.00';
                    }
                    if (filter_var($newPrice, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $newTotal) {
                        throw new ApiGenericException(
                            'Sent rate does not match database rate, please refresh and try again.',
                            422,
                            ['sent_rate' => $request->total, 'database_rate' => $newPrice, 'isratemismatch' => 1]
                        );
                    }
                }
            }


            if ($onlyCheckout == 0) {
                $ticket->rate_id = isset($this->rate['id']) ? $this->rate['id'] : '';
                $ticket->rate_description = isset($this->rate['description']) ? $this->rate['description'] : '';
                $ticket->grand_total = $request->total;
                $this->log->error('only checkout  :  ' . $request->total);
                // process report data for now specific to twonsend ;         
            }
            $ticket->save();

            $transactionData = TransactionData::where("ticket_id", $ticket->id)->first();
            if (!$transactionData) {
                $this->saveTransactionData();
            }
        }
        $ticket->checkout_gate = $request->gate;
        if ($ticket->reservation_id != '') {
            $ticket->checkout_time = date('Y-m-d H:i:s');

            $reservation = Reservation::where('id', $ticket->reservation_id)->first();
            $reservation->is_ticket = 2;
            $reservation->save();
        } else {

            //check if pass need to used
            if ($request->is_pass == '1') {
                $pass = UserPass::where('user_id', $ticket->user_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
                if ($pass) {
                    if ($pass->remaining_days == 0) {
                        throw new NotFoundException('User Pass is not valid.');
                    } else {
                        $ticket->user_pass_id = $pass->id;
                        $ticket->save();
                    }
                } else {
                    throw new NotFoundException('User is not having any valid pass.');
                }
            }
            // vijay commented and because used above  
            // $ticket->checkout_datetime = date('Y-m-d H:i:s'); 
            // $ticket->checkout_time = date('Y-m-d H:i:s');
        }

        $ticket->save();

        if ($ticket->is_checkout == '1') {

            if (isset($request->direct_checkout) && $request->direct_checkout == 0) {
                if ($request->gate == '' || $request->gate == '0') {
                    throw new ApiGenericException('Please select gate.');
                }
                $this->log->error('confirmCheckout checkout');
                if ($ticket->facility->check_vehicle_enabled == 1) {
                    $gateStatus = $this->isParkEngageGateAvailable($ticket->facility_id, $request->gate, '');
                    if ($gateStatus == "true") {
                    } else {
                        throw new ApiGenericException('Currently Gate is not available.');
                    }
                }
            }

            if (isset($ticket->user_id) && $ticket->user_id > 0) {
                $facilityName = ucwords($facility->full_name);
                $user = User::find($ticket->user_id);
                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                if ($partnerDetails->user_id == self::TOWNSEND_PARTNER_ID) {
                    $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                    })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                } else {
                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                }
                $url = env('RECEIPT_URL');
                $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                $this->customeReplySms($sms_msg, $user->phone);
            }
        }
        return $ticket;
    }

    public function confirmCheckin(Request $request)
    {
        //$this->validate($request, Ticket::$partnerValidation);
        if ($this->checkFacilityAvailableForTwilio($request->facility_id) == false) {
            $this->log->info("{$request->facility_id} not availabe right now.");
            throw new NotFoundException('Garage is not available.');
        }

        $existUser = User::where('email', $request->email)->first();

        // Get country Code
        $countryCode = QueryBuilder::appendCountryCode();

        if (!$existUser) {
            $user = User::where('phone', $countryCode . $request->phone)->first();
            if (!$user) {
                $user = User::create(
                    [
                        'name' => '',
                        'email' => $request->email,
                        'phone' => $countryCode . $request->phone,
                        'password' => Hash::make(str_random(60)),
                        'anon' => false,
                        'user_type' => '5',
                        'created_by' => Auth::user()->id
                    ]
                );
            }
            $existUser = $user;
        }
        $ticket = Ticket::where('user_id', $existUser->id)->where('is_checkout', '0')->first();
        if ($ticket) {
            throw new ApiGenericException('User already checkin. Please checkout that ticket.');
        }
        $data['user_id'] = $existUser->id;
        $data['checkin_gate'] = $request->gate;
        $data['facility_id'] = $request->facility_id;
        $data['is_checkin'] = 1;
        $data['ticket_number'] = $this->checkTicketNumber();
        $data['check_in_datetime'] = date('Y-m-d H:i:s');
        $data['checkin_time'] = date('Y-m-d H:i:s');
        $data['ticket_security_code'] = rand(1000, 9999);
        $data['partner_id'] = Auth::user()->id;

        //check if user done any reservation
        $reservation = Reservation::where('user_id', $existUser->id)->where('is_ticket', '0')->whereDate('start_timestamp', '=', date('Y-m-d'))->whereNull('cancelled_at')->orderBy('id', 'Desc')->first();
        if (count($reservation) > 0) {

            $alreadyAssociateReservation = Ticket::where('reservation_id', $reservation->id)->first();
            if (isset($alreadyAssociateReservation->is_checkout) && $alreadyAssociateReservation->is_checkout == '0') {
                throw new ApiGenericException('User already checkin. Please checkout that ticket.');
            } elseif (isset($alreadyAssociateReservation->is_checkout) && $alreadyAssociateReservation->is_checkout == '1') {
                //throw new ApiGenericException('User already checkin. Please checkout that ticket.');            
            } else {
                $config = Configuration::where('field_name', 'prepaid-checkin-time')->first();
                if (count($config) > 0) {
                    $prepaidCheckinTime = $config->field_value;
                } else {
                    $prepaidCheckinTime = 15;
                }
                $start = date("Y-m-d H:i:s", strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp)->subMinutes($prepaidCheckinTime)));

                $end = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));

                $todayDate = date('Y-m-d H:i:s');
                if (strtotime($todayDate) >= strtotime($start) && strtotime($todayDate) <= strtotime($end)) {
                    $data['reservation_id'] = $reservation->id;
                    $data['checkin_time'] = date('Y-m-d H:i:s');
                    //$data['checkout_time'] = date('Y-m-d H:i:s');
                    $data['check_in_datetime'] = $reservation->start_timestamp;
                    $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                    $data['total'] = $reservation->total;
                    $data['grand_total'] = $reservation->total;
                    $data['length'] = $reservation->length;

                    $reservation->is_ticket = '1';
                    $reservation->save();
                }
            }
        }
        $result = Ticket::create($data);
        if ($result) {
            $ticket_number = base64_encode($result->ticket_number);
            $accountSid = env('TWILIO_ACCOUNT_SID');
            $authToken  = env('TWILIO_AUTH_TOKEN');
            $url = env('TOUCHLESS_APP_URL');
            $client = new Client($accountSid, $authToken);
            try {
                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                    // the number you'd like to send the message to
                    $countryCode . $request->phone,
                    array(
                        // A Twilio phone number you purchased at twilio.com/console
                        'from' => env('TWILIO_PHONE'),
                        // the body of the text message you'd like to send
                        //'body' => "Fine"
                        'body' => "Thank you for Check-In with ParkEngage. Your ticket number is $result->ticket_number. Use the following link to Pay and Check-Out. $url/pay/$ticket_number"
                    )
                );
                $this->log->info("checkin SMS send to user {$countryCode}{$request->phone} with ticket number {$result->ticket_number}");
                return $result;
            } catch (RestException $e) {
                $result->delete();
                $this->log->error($e->getMessage());
                throw new ApiGenericException('Something wrong with the phone number. Please check.');
            }
        }
    }


    public function getOverstayTicketDetails($ticket, $rate, $facility)
    {
        // Vijay : 18-09-2023
        $priceBreakUp = $ticket->priceBreakUp($rate);
        $this->log->info("getOverstayTicketDetails priceBreakUp " . json_encode($priceBreakUp));
        if ($ticket->reservation_id != '') {
            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
            // $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
            // Vijay : 24-09-2023 : To Handel Tentative CHeckout time in case of  Reservation we always showing current datetime.
            if (!empty($ticket->reservation_id)) {

                $ticket->estimated_checkout = $this->currentTime;
                $ticket->checkout_datetime = $this->currentTime;
                $ticket->checkout_time = $this->currentTime;
                $endDate = $this->currentTime;
            } else {
                $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
            }
            $ticket['checkout_time'] = date('Y-m-d H:i:s', strtotime($endDate));
            $ticket['diff_in_days'] = $startDate->diffInDays($endDate);
            $ticket['diff_in_hours'] = $startDate->copy()->addDays($ticket['diff_in_days'])->diffInRealHours($endDate);
            $ticket['diff_in_minutes'] = $startDate->copy()->addDays($ticket['diff_in_days'])->addHours($ticket['diff_in_hours'])->diffInRealMinutes($endDate);

            $ticket['amount_paid'] = $ticket->grand_total;

            $this->log->info("reservation get Diff in Hours : ");
            $ticket->payable_amount = $priceBreakUp['payable_amount'];
            $ticket->overstay_amount = $priceBreakUp['overstay_amount'];
            $ticket->tax_fee  = $priceBreakUp['tax_fee'];
            $ticket->tax_rate = $priceBreakUp['tax_rate'];
            $ticket['is_overstay'] = '1';
            $ticket['rate'] = [];
            return $ticket;
        }

        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $ticket['checkout_time'] = date('Y-m-d H:i:s', strtotime($endDate));
        $ticket['diff_in_days'] = $startDate->diffInDays($endDate);
        $ticket['diff_in_hours'] = $startDate->copy()->addDays($ticket['diff_in_days'])->diffInRealHours($endDate);
        $ticket['diff_in_minutes'] = $startDate->copy()->addDays($ticket['diff_in_days'])->addHours($ticket['diff_in_hours'])->diffInRealMinutes($endDate);

        $ticket['amount_paid'] = $ticket->grand_total;

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date);
        $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->payment_date);
        $this->log->info("overstay get Diff in Hours : {$diff_in_hours}");

        // $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true,  false, true, false, 0, 0);

        // Vijay : 02-11-2023 To handle Overnight Overstay
        if ($priceBreakUp['payable_amount'] == 0) {
            $ticket->payable_amount = "0.00";
            $ticket->overstay_amount = "0.00";
            $ticket['is_overstay'] = '0';
            $ticket['rate'] = $rate;
            return $ticket;
        }
        $payableAmount = $priceBreakUp['payable_amount'];
        $overstayAmount = $priceBreakUp['overstay_amount'];
        $ticket->payable_amount = number_format($payableAmount, 2);
        $ticket->overstay_amount = number_format($overstayAmount, 2);
        $ticket->parking_amount = $priceBreakUp['parking_amount'];;
        $ticket->amount_paid     = $priceBreakUp['amount_paid'];
        $ticket['is_overstay'] = '1';
        $ticket['rate'] = $rate;
        return $ticket;
        /* if ($rate['price'] == 0) {
      $ticket->payable_amount = "0.00";
      $ticket->overstay_amount = "0.00";
      $ticket['is_overstay'] = '0';
      $ticket['rate'] = $rate;
      return $ticket;
    }
    $payableAmount = $rate['price'];
    $ticket->payable_amount = number_format($payableAmount, 2);
    $ticket->overstay_amount = number_format($rate['price'], 2);
    $ticket['is_overstay'] = '1';
    $ticket['rate'] = $rate; */
        // return $ticket;

    }


    public function getCheckinPaymentDetails($id)
    {
        $this->log->info("partner checkincheckout Controller getCheckinPaymentDetails {$id}");
        $checkinData = Ticket::with(['user', 'reservation', 'overstay'])->where('id', $id)->where('is_checkout', '0')->first();

        if (count($checkinData) == 0) {
            throw new ApiGenericException('Something wrong with this ticket.');
        }
        $this->setCustomTimezone($checkinData->facility_id);
        /*if ($this->checkFacilityAvailable($checkinData->facility_id) == false) {
        throw new ApiGenericException('Garage is not available');
        }*/

        $facility = Facility::find($checkinData->facility_id);
        $this->log->info("getCheckinPaymentDetails11");
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkin_time);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $is_overstay = 0;

        if ($checkinData->partner_id == "45") {
            $checkinData['payable_amount'] = "0.00";
            $checkinData['amount_paid'] = $checkinData->grand_total;
            $checkinData['facility'] = $facility;

            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkin_time);
            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
            $checkinData['checkout_time'] = date('Y-m-d H:i:s', strtotime($endDate));
            $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
            $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
            $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
            return $checkinData;
        }
        if ($checkinData->estimated_checkout != '') {

            $overstayExist = OverstayTicket::where("ticket_id", $checkinData->id)->orderBy("id", "DESC")->first();
            if ($overstayExist) {
                $rate['price'] = 0;
                $priceBreakUp = $checkinData->priceBreakUp($rate['price']);
                // dd($priceBreakUp);
                if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
                    $checkinData['payable_amount'] = "0.00";
                    // $checkinData['amount_paid'] = $checkinData->grand_total;   : Vijay : 26-04-2024
                    if (isset($checkinData->reservation->total) && $checkinData->reservation->total > 0) {
                        $checkinData['amount_paid'] = $checkinData->reservation->total;
                    }
                    $checkinData['facility'] = $facility;

                    $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkin_time);
                    $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                    $checkinData['checkout_time'] = date('Y-m-d H:i:s', strtotime($endDate));
                    $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                    $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
                    $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);

                    // Vijay : 26-04-2024       
                    $checkinData->payable_amount  = $priceBreakUp['payable_amount'];
                    $checkinData->overstay_amount = $priceBreakUp['overstay_amount'];
                    $checkinData->parking_amount  = $priceBreakUp['parking_amount'];
                    $checkinData->amount_paid     = $priceBreakUp['amount_paid'];

                    return $checkinData;
                } else {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->payment_date);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                    $is_overstay = 1;
                }
            } else {

                if (strtotime($checkinData->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
                    // Sport hero if not in overstay then goes here
                    $checkinData['payable_amount'] = "0.00";
                    if ($checkinData->paid_amount > 0) {
                        $amount_paid = $checkinData->grand_total - $checkinData->paid_amount;
                        if ($amount_paid < '0.00') {
                            $amount_paid = '0.00';
                        }
                    } else {
                        $amount_paid = $checkinData->grand_total;
                    }
                    $checkinData['amount_paid'] = $amount_paid;
                    if (isset($checkinData->reservation->total) && $checkinData->reservation->total > 0) {
                        $checkinData['amount_paid'] = $checkinData->reservation->total;
                    }
                    $checkinData['facility'] = $facility;

                    $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkin_time);
                    $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                    $checkinData['checkout_time'] = date('Y-m-d H:i:s', strtotime($endDate));
                    $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
                    $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
                    $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);
                    return $checkinData;
                } else {
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->estimated_checkout);
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                    $is_overstay = 1;
                }
            }
        }

        $diff_in_hours = $checkinData->getCheckOutCurrentTime(true);
        $this->log->error("Normal : rate for hours  {$diff_in_hours} ");
        if ($is_overstay == 1) {
            $diff_in_hours = $checkinData->getCheckOutCurrentTime(true, $checkinData->payment_date);
            $this->log->error("OverStay : rate for hours  {$diff_in_hours} ");
            $rate['price'] = 0; // because reset in from price break up
            return $this->getOverstayTicketDetails($checkinData, $rate, $facility);
        }


        $checkinData['length'] = $diff_in_hours;
        $checkinData['facility'] = $facility;
        //$facility = Facility::find($checkinData->facility_id);

        $processingFee = 0;
        if ($checkinData->vp_device_checkin  == '1' || $checkinData->vp_device_checkin  == 1) {
            $processingFee = $facility->drive_up_processing_fee == '' ? 0.00 : $facility->drive_up_processing_fee;
        } else {
            $processingFee = $facility->processing_fee == '' ? 0.00 : $facility->processing_fee;
        }


        $rateDiffInHour = $diff_in_hours;
        // $rateDiffInHour = '0.13';
        // $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
        $this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");
        $isMember = 0;
        if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0) {
            $this->log->info("Get Price 111");
            $rate = $facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
        } else {
            $this->log->info("Get Price 222");
            if ($is_overstay == 1) {
                // townsend overnight overstay handle change
                $rate = $facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, true,  false, true, false, 0, $isMember);
            } else {
                $rate = $facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, $isMember);
            }
        }
        // $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, 0);
        // dd($arrival_time, $diff_in_hours, $rate['price']);
        $this->log->info('RATES ' . $rate['price']);

        if ($is_overstay == 1) {
            return $this->getOverstayTicketDetails($checkinData, $rate, $facility);
        }

        $priceBreakUp = $checkinData->priceBreakUp($rate);
        $this->log->info("getCheckinPaymentDetails priceBreakUp " . json_encode($priceBreakUp));

        // if ($rate['price'] == 'N/A') {
        //   $rate['availability'] = self::DEFAULT_VALUE;
        // }


        if ((isset($rate['coupon_price'])) && ($rate['coupon_price'] > self::DEFAULT_VALUE)) {
            $rate['coupon_price_applied'] = "$" . number_format($rate['coupon_price'], 0) . " WITH COUPON";
        } else {
            //return if coupon price got applied
            $rate['coupon_price_applied'] = self::DEFAULT_VALUE;
        }


        if (!$rate) {

            throw new NotFoundException('No valid rates for those parameters.');
        }
        // Vijay Comment : 09-04-2024 : due to get non numric value error 
        // if ($rate['price'] != 'N/A') {
        //   $rate['price'] = number_format($rate['price'], 2);
        // }

        $checkinData['rate'] = $rate;

        //normal
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkin_time);
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
        $checkinData['checkout_time'] = date('Y-m-d H:i:s', strtotime($endDate));
        $checkinData['diff_in_days'] = $startDate->diffInDays($endDate);
        $checkinData['diff_in_hours'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->diffInRealHours($endDate);
        $checkinData['diff_in_minutes'] = $startDate->copy()->addDays($checkinData['diff_in_days'])->addHours($checkinData['diff_in_hours'])->diffInRealMinutes($endDate);

        $pass = UserPass::where('user_id', $checkinData->user_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
        if ($pass) {
            if ($pass->remaining_days == 0) {
                $checkinData['pass'] = [];
            } else {
                $checkinData['pass'] = $pass;
            }
        } else {
            $checkinData['pass'] = [];
        }
        //prepaid
        if ($checkinData->reservation_id != '') {
            $prepaidStartDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->check_in_datetime);
            $prepaidEndDate = Carbon::createFromFormat('Y-m-d H:i:s', $checkinData->checkout_datetime);
            $checkinData['res_diff_in_days'] = $prepaidStartDate->diffInDays($prepaidEndDate);
            $checkinData['res_diff_in_hours'] = $prepaidStartDate->copy()->addDays($checkinData['res_diff_in_days'])->diffInRealHours($prepaidEndDate);
            $checkinData['res_diff_in_minutes'] = $prepaidStartDate->copy()->addDays($checkinData['res_diff_in_days'])->addHours($checkinData['res_diff_in_hours'])->diffInRealMinutes($prepaidEndDate);
        }

        // Change for Grand_total at checkout screen
        if (($checkinData->paid_type == '2') && ($checkinData->paid_amount != '')) {

            if ($checkinData->grand_total >= $checkinData->paid_amount) {
                $grand_total = ($checkinData->grand_total - $checkinData->paid_amount);
            } else {
                $grand_total = $checkinData->grand_total;
            }
            // $checkinData->grand_total = $grand_total;
        } else if (($checkinData->paid_type == '1') && ($checkinData->paid_hour != '') && isset($rate['price'])) {
            // dd($checkinData->length, $checkinData->paid_hour);
            if ($checkinData->length >= $checkinData->paid_hour) {
                $diff_in_hours = ($checkinData->length - $checkinData->paid_hour);
            } else {
                $diff_in_hours = $checkinData->length;
            }

            $this->log->error(" : rate for hours  {$diff_in_hours} ");
            $rateDiffInHour = $diff_in_hours;
            // $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
            $this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

            $isMember = 0;
            if ($facility->rate_duration_in_hours > 0 && $facility->rate_per_hour > 0 && $facility->rate_free_minutes > 0 && $facility->rate_daily_max_amount > 0) {
                $this->log->info("Get Price 111");
                $hoursRate = $facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
            } else {
                $this->log->info("Get Price 222");
                $hoursRate = $facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, $isMember);
            }
            // $hoursRate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, 0);
            // dd($rate['price'], $diff_in_hours, $hoursRate['price']);
            if ($checkinData->grand_total >= $hoursRate['price']) {
                $grand_total = ($checkinData->grand_total - $hoursRate['price']);
            } else {
                $grand_total = $checkinData->grand_total;
            }
            $checkinData->grand_total = $grand_total;
        }
        // $this->log->info('RATES 11 ' . $hoursRate['price']);
        // $checkinData->payable_amount = number_format(($rate['price'] + $checkinData->processing_fee + $checkinData->tax_fee) - $checkinData->grand_total - $checkinData->paid_amount - $checkinData->discount_amount, 2);
        $processingFee = $facility->processing_fee;
        if ($rate['price'] <= 0) {
            $checkinData->payable_amount = "0.00";
        } else {
            $checkinData->payable_amount = number_format(($rate['price'] + $processingFee + $checkinData->tax_fee) - $checkinData->grand_total - $checkinData->paid_amount - $checkinData->discount_amount, 2);
        }
        if ($checkinData->payable_amount <= "0") {
            $checkinData->payable_amount = "0.00";
        }
        if ($checkinData->paid_amount > 0) {
            $amount_paid = $checkinData->grand_total - $checkinData->paid_amount;
            if ($amount_paid < '0.00') {
                $amount_paid = '0.00';
            }
        } else {
            $amount_paid = $checkinData->grand_total;
        }
        $checkinData->amount_paid =   $amount_paid > 0 ? $amount_paid  : "0.00";
        // $ticketPrice = $rate['price'] + $processingFee;
        // $this->log->info("HOURS {$diff_in_hours} AND RATES {$ticketPrice} ");
        $checkinData->parking_amount  = $priceBreakUp['parking_amount'];
        $checkinData->paid_amount     = $priceBreakUp['paid_amount'];
        $checkinData->processing_fee  = $priceBreakUp['processing_fee'];
        $checkinData->tax_rate        = $priceBreakUp['tax_rate'];
        $checkinData->payable_amount  = $priceBreakUp['payable_amount'];
        $checkinData->validate_amount = $priceBreakUp['paid_amount'];
        if (isset($priceBreakUp['amount_paid'])) {
            $checkinData->amount_paid     = $priceBreakUp['amount_paid'];
        }

        $this->log->info('data before retun ');
        $this->log->info("Parking Amount {$checkinData->parking_amount}");
        $this->log->info("Paid Amount {$checkinData->paid_amount}");
        $this->log->info("Parking Amount {$checkinData->parking_amount}");
        $this->log->info("processing Fee {$checkinData->processing_fee}");
        $this->log->info("Payable Amount {$checkinData->payable_amount}");
        $this->log->info("Validate Amount {$checkinData->validate_amount}");
        // dd($checkinData->parking_amount, $hoursRate['price'], $checkinData->paid_amount, $checkinData->payable_amount, $checkinData->validate_amount);
        $overstay = OverstayTicket::where('ticket_number', $checkinData->ticket_number)->first();
        if ($overstay) {
            $checkinData['overstay_amount'] = $overstay->total;
        } else {
            $checkinData['overstay_amount'] = "0.00";
        }
        $checkinData->discount_hours = $checkinData->discount_hours;

        return $checkinData;
    }

    protected function checkFacilityAvailable($facility_id)
    {
        $facility = Facility::where('id', $facility_id)->where('active', '1')->first();
        if (count($facility) == 0) {
            return false;
        }
        if ($facility->is_available != '1') {
            return false;
        }
        return true;
    }

    public function getPrepaidUserCheckout($key)
    {
        Session::put('is_sms_direct_checkout', '1');
        return view('checkin-checkout.prepaid-user-checkout-details', ['data' => $key]);
    }


    public function makePayment(Request $request)
    {

        $this->setDecryptedCard($request);

        //$this->validate($this->request, $this->billingValidation);
        $ticket = Ticket::where('id', $request->id)->first();
        $this->user = User::where('id', $ticket->user_id)->first();
        $this->user->name = $this->request->name_on_card;
        $this->user->save();
        $this->facility = Facility::find($ticket->facility_id);
        if ($this->request->total > 0) {
            try {

                /*$is_partner = 0;
                if($this->user->user_type == 3){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                }elseif($this->user->user_type == 4 || $this->user->user_type == 5){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if($this->partnerPaymentDetails){
                        $is_partner = 1;
                    }else{
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                }else{
                    $is_partner = 0;
                }
                if($is_partner == 1){
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()                
                    ->setFacility($this->facility)
                    ->isPartner($this->partnerPaymentDetails)
                    ->setBillingAddress($this->getBillingArray());
                }else{
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());
                }*/
                $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());


                if (isset($this->paymentProfileId)) { // Use our logged in users profile
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    /*$this->authNet->isPartner($this->partnerPaymentDetails)
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);*/
                    $this->authNet
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            } catch (Exception $e) {
                $this->log->error($e);
                throw new ApiGenericException($e);
            }
        }

        //$ticket = $this->saveTicket();
        if ($this->request->total > 0) {
            $reservationMode = "Ticketles Parking";
            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else {
                $ticketech_guid = rand(100000, 999999);
            }

            try {

                // Use our database rate price to create the transaction
                try {
                    /*$charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();*/
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->executeTransaction(); // Fire off transaction - this method will throw an exception if errors occur
                } catch (AuthorizeNetException $e) {
                    $this->log->error($e->getMessage());
                    throw new ApiGenericException($e->getMessage());
                }
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                /*dd($e);
                return back()->withError(['errMsg'=>$e]);
                dd(111);
                $transaction = $this->authNet->getTransaction();
                $ticket->anet_transaction_id = $transaction->id;
                $ticket->save();       
                $ticket->delete();
                return back()->withError(['errMsg'=>$e]);*/
                $this->log->error($e);
                throw new ApiGenericException($e->getMessage());
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;

            $ticket->total = $this->request->total;
            $ticket->grand_total = $this->request->total;
            $ticket->length = $this->request->length;
            $ticket->checkout_datetime = date('Y-m-d H:i:s');
            $ticket->checkout_time = date('Y-m-d H:i:s');
            $ticket->save();
            $this->log->info("Partner done payment ticket id {$ticket->id} ticket number {$ticket->ticket_number}  with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {
                Artisan::queue('email:checkin-payment', array('id' => $ticket->id, 'type' => 'normal'));
                return $ticket;
            }
        } else {
            throw new ApiGenericException('Please use valid ticket number.');
        }
    }


    public function getoverstayDetails($id)
    {
        $ticket = Ticket::with(['facility', 'user'])->where('id', $id)->first();
        if (!$ticket) {
            throw new NotFoundException('Please use valid ticket number.');
        }
        if ($ticket->is_checkout == '1') {
            throw new NotFoundException('Ticket already checkout.');
        }

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $arrival_time->diffInRealHours($from);
        if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
            $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
        }
        //$checkinData['overstay_check_in_datetime'] = $arrival_time;
        $facility = Facility::find($ticket->facility_id);
        $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);
        if ($rate) {
            $ticket['checkout_time'] = $ticket->checkout_time != '' ? $ticket->checkout_time : date('Y-m-d H:i:s');
            if ($rate['price'] != $ticket->grand_total) {
                $ticket['overstay_length'] = $diff_in_hours - $ticket->length;
                $ticket['overstay_price'] = $rate['price'] - $ticket->grand_total;
                $ticket['total_price'] = number_format($ticket['overstay_price'] + $ticket->grand_total, 2);
                $ticket['is_overstay'] = '1';
                $ticket['rate'] = $rate;
                $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                $endDate = Carbon::createFromFormat('Y-m-d H:i:s', Carbon::now());
                $ticket['diff_in_hours'] = $startDate->diffInRealHours($endDate);
                $ticket['diff_in_minutes'] = $startDate->copy()->addHours($ticket['diff_in_hours'])->diffInRealMinutes($endDate);

                if ($ticket->reservation_id != '') {
                    $prepaidStartDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->check_in_datetime);
                    $prepaidEndDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_datetime);
                    $checkinData['res_diff_in_days'] = $prepaidStartDate->diffInDays($prepaidEndDate);
                    $ticket['res_diff_in_hours'] = $prepaidStartDate->copy()->addDays($ticket['res_diff_in_days'])->diffInRealHours($prepaidEndDate);
                    $ticket['res_diff_in_minutes'] = $prepaidStartDate->copy()->addDays($ticket['res_diff_in_days'])->addHours($ticket['res_diff_in_hours'])->diffInRealMinutes($prepaidEndDate);
                }
                $this->log->info("user have overstay payment with ticket number {$ticket->ticket_number}  with browser {$_SERVER['HTTP_USER_AGENT']}");
            } else {
                $ticket['rate'] = $rate;
                $ticket['is_overstay'] = '0';
            }
            return $ticket;
        }
    }


    public function makeOverstayPayment(Request $request)
    {
        $this->setDecryptedCard($request);
        //$this->validate($this->request, $this->billingValidation);
        $ticket = Ticket::where('id', $request->id)->first();
        $this->user = User::where('id', $ticket->user_id)->first();
        $this->user->name = $this->request->name_on_card;
        $this->user->save();
        $this->facility = Facility::find($ticket->facility_id);

        if ($this->request->total > 0) {
            try {
                /*$is_partner = 0;
                if($this->user->user_type == 3){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
                    $is_partner = 1;
                }elseif($this->user->user_type == 4 || $this->user->user_type == 5){
                    $this->partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
                    if($this->partnerPaymentDetails){
                        $is_partner = 1;
                    }else{
                        throw new NotFoundException('No payment gateway details found for this partner.');
                    }
                }else{
                    $is_partner = 0;
                }
                if($is_partner == 1){
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()                
                    ->setFacility($this->facility)
                    ->isPartner($this->partnerPaymentDetails)
                    ->setBillingAddress($this->getBillingArray());
                }else{
                    $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());
                }*/
                $this->authNet
                    ->setUser($this->user)
                    ->isReservation()
                    ->setFacility($this->facility)
                    ->setBillingAddress($this->getBillingArray());

                if (isset($this->paymentProfileId)) { // Use our logged in users profile
                    $this->authNet->isPartner($this->partnerPaymentDetails)->setPaymentProfile($this->paymentProfileId);
                } else {  // Otherwise set transaction details manually
                    /*$this->authNet->isPartner($this->partnerPaymentDetails)
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);*/
                    $this->authNet
                        ->setCreditCard($this->request->card_number, $this->request->expiration_date, $this->request->security_code);
                }
            } catch (Exception $e) {
                throw new ApiGenericException($e);
            }
        }

        $ticket = $this->saveOverstayTicket();
        if ($this->request->total > 0) {
            $reservationMode = "Ticketless Overstay Parking";
            // Send reservation to ticketech
            if ($this->facility->ticketech_id) {
                $ticketech_guid = rand(100000, 999999);
            }
            // Send reservation to parkonect
            else {
                $ticketech_guid = rand(100000, 999999);
            }

            try {

                // Use our database rate price to create the transaction
                try {
                    /*$charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->isPartner($this->partnerPaymentDetails)->executeTransaction();*/
                    $charge = $this->authNet->createTransaction(
                        // $rate['price'],
                        $this->request->total,
                        "{$reservationMode} {$ticket->id}, Code {$ticket->ticket_number}",
                        config('icon.reservations_account')
                    )->executeTransaction();
                    // Fire off transaction - this method will throw an exception if errors occur
                } catch (AuthorizeNetException $e) {
                    $ticket->delete();
                    throw new ApiGenericException($e->getMessage());
                }
            } catch (Exception $e) {
                // Charge successful, save transaction relationship to it
                /*dd($e);
                return back()->withError(['errMsg'=>$e]);
                dd(111);
                $transaction = $this->authNet->getTransaction();
                $ticket->anet_transaction_id = $transaction->id;
                $ticket->save();       
                $ticket->delete();
                return back()->withError(['errMsg'=>$e]);*/
                $ticket->delete();
                throw new ApiGenericException($e->getMessage());
            }
            // Charge successful, save transaction relationship to it
            $transaction = $this->authNet->getTransaction();
            $ticket->anet_transaction_id = $transaction->id;
            $ticket->save();
            $this->log->info("admin paid overstay payment with ticket number {$ticket->ticket_number} with browser {$_SERVER['HTTP_USER_AGENT']}");
            if ($ticket) {
                Artisan::queue('email:checkin-payment', array('id' => $ticket->id, 'type' => 'overstay'));
                return $ticket;
            }
        } else {
            throw new ApiGenericException("Please use valid ticket number with amount.");
        }
    }

    protected function saveOverstayTicket()
    {
        $mainTicket = Ticket::where('id', $this->request->id)->first();
        if (!$mainTicket) {
            throw new ApiGenericException("Please use valid ticket number.");
        }
        $ticket = new OverstayTicket();
        $ticket->user_id = $this->user->id;
        $ticket->facility_id = $this->facility->id;
        $ticket->length = $this->request->length;
        $ticket->total = $this->request->total;
        $ticket->ticket_number = $mainTicket->ticket_number;
        $ticket->is_checkin = '1';
        $ticket->check_in_datetime = date('Y-m-d H:i:s', strtotime($mainTicket->check_in_datetime));
        $ticket->checkout_datetime = date('Y-m-d H:i:s');
        $ticket->save();

        $mainTicket->grand_total = $this->request->grand_total;
        $mainTicket->save();
        return $ticket;
    }


    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $zipCode = (isset($cardData[4]) && $cardData[4] != 'undefined') ? $cardData[4] : '';
        $request->request->add(
            [
                'name_on_card' => $cardData[0],
                'card_number' => $cardData[1],
                'expiration_date' => $cardData[2],
                'security_code' => $cardData[3],
                'zip_code' => $zipCode
            ]
        );

        $this->request = $request;
    }

    protected function getBillingArray()
    {
        $name = $this->request->name_on_card ?: $this->user->name;
        $zip = $this->request->zip_code ?: false;

        $nameArray = explode(' ', trim($name));

        return [
            'first_name' => reset($nameArray),
            'last_name' => end($nameArray),
            'zip' => $zip,
        ];
    }


    protected function checkTicketNumber($facility_id = '')
    {
        if ($facility_id == '') {
            $ticket = 'PE' . rand(100, 999) . rand(100, 999);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $this->checkTicketNumber();
            }
        } else {
            $facility = Facility::find($facility_id);
            $prefix = $facility->ticket_prefix != '' ? $facility->ticket_prefix : "PE";
            $fromRange = $facility->from_range != '' ? $facility->from_range : 100000;
            $toRange = $facility->to_range != '' ? $facility->to_range : 999999;
            $ticket = $prefix . rand($fromRange, $toRange);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $this->checkTicketNumber($facility_id);
            }
        }

        return $ticket;
    }

    protected function checkFacilityAvailableForTwilio($facility_id)
    {
        $facility = Facility::where('id', $facility_id)->where('active', '1')->first();
        if (count($facility) == 0) {
            return false;
        } else {
            if ($facility->is_available != '1') {
                return false;
            }

            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $diff_in_hours = $arrival_time->diffInRealHours($from);
            if ($diff_in_hours < self::RESERVATION_THRESHOLD_TYPE) {
                $diff_in_hours = self::RESERVATION_THRESHOLD_TYPE;
            }
            $checkinData['length'] = $diff_in_hours;
            $facility = Facility::find($facility_id);
            /** this function is used to get Availability Information for respective facility **/
            $rateData = $this->updateRateInformationWithAvailibilty($arrival_time, $diff_in_hours, $facility);
            $rate = $facility->rateForReservation($arrival_time, $diff_in_hours, false, false,  false, true, array(), 0, self::RESERVATION_THRESHOLD_TYPE);

            if ($rate == false) {
                return false;
            }

            //return current availabilit
            if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
                $rate['availability'] = $rateData['availability'];
            } else {
                $rate['availability'] = self::DEFAULT_VALUE;
            }
            if ($rate['price'] == 'N/A') {
                return false;
            }

            //returning  message as per availibility 
            $rate['availability_msg'] = '';
            $rate['availability_msg_some_space'] = '';
            if ((float)$rate['price'] >  self::DEFAULT_VALUE) {
                if ($rate['availability'] == self::DEFAULT_VALUE) {
                    return false;
                } else if ($rate['availability'] < self::MIN_AVAILABILITY) {
                    return false;
                } else {
                    return true;
                    //return Redirect::to('error-facility');
                }
            } else if ($rate['price'] == 'N/A') {
                return false;
            } else if ($rate['availability'] == self::DEFAULT_VALUE) {
                return false;
            }
            return true;
        }
    }

    public function updateRateInformationWithAvailibilty($arrival_time, $length_of_stay, Facility $facility)
    {
        $returnResultArr = array();

        $returnResultArr['coupon_threshold_price'] = 0;
        $returnResultArr['is_coupon_threshold_price_percentage'] = 0;
        $returnResultArr['availability'] = 0;
        $returnResultArr['is_coupon_threshold_applied'] = 0;

        $inventory = new Inventory();
        $date_time_out = Carbon::parse($arrival_time)->addMinutes((number_format($length_of_stay, 2) * 60));

        $realtimeWindow = $facility->realtime_window;
        $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;

        $timeDifference = date_diff(date_create($arrival_time), Carbon::now());

        $isAvailable = true;

        $thresholdAvailability = self::DEFAULT_VALUE;

        if ($isAvailable == true) {
            //check how many slots does entry and exit time occupies
            //$difference = date_diff(date_create($arrival_time), date_create($date_time_out));
            $difference = date_diff(date_create(date('Y-m-d', strtotime($arrival_time))), date_create(date('Y-m-d', strtotime(($date_time_out)))));

            if ($difference->d > 0) {
                //                $dates   = $inventory->generateArrayOfDates(
                //                '', date($arrival_time), date($date_time_out));

                $dates   = $inventory->generateArrayOfDates(
                    ($difference->d + self::ADD_EXTRA_DAY_COUNT),
                    date('Y-m-d H:i:s', strtotime($arrival_time))
                );

                $dayDifference = $difference->d;

                foreach ($dates as $key => $date) {
                    $facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();

                    if ($facilityAvailability) {
                        $inventory = json_decode($facilityAvailability->availability);

                        if ($key == 0) {
                            /**
                             * because this is the first day in the dates provided
                             * we should check from each time_slot starting
                             * from the hour provided in the api call
                             */
                            $i = date('G', strtotime($arrival_time));
                            while ($i <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$i})) {
                                    if ($inventory->{$i} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$i}) {
                                            $thresholdAvailability = $inventory->{$i};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$i};
                                    }
                                }
                                $i++;
                            }
                        } elseif ($key == $dayDifference) {
                            $i = date('G', strtotime($date_time_out));
                            $minutes = date('i', strtotime($date_time_out));
                            if ($minutes >= 30) {
                                $i++;
                            }
                            /**
                             * because this is the last day in the dates provided
                             * we should check from each time_slot starting
                             * till the hour provided in the api call
                             */
                            $j = 0;
                            while ($j < $i) {
                                if (isset($inventory->{$j})) {
                                    if ($inventory->{$j} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$j}) {
                                            $thresholdAvailability = $inventory->{$j};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$j};
                                    }
                                }
                                $j++;
                            }
                        } else {
                            /**
                             * because this could be any day except first and last in
                             * the dates provided we should check from whole day
                             */
                            $k = 0;
                            while ($k <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$k})) {
                                    if ($inventory->{$k} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$k}) {
                                            $thresholdAvailability = $inventory->{$k};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$k};
                                    }
                                }
                                $k++;
                            }
                        }
                    }
                }
            } else {
                $startingHour = date('G', strtotime($arrival_time));
                $endingHour   = date('G', strtotime($date_time_out));
                $facilityAvailability     = FacilityAvailability::where(
                    ['facility_id' => $facility->id, 'date' => date('Y-m-d', strtotime($arrival_time))]
                )->first();

                if ($facilityAvailability) {
                    $availability = json_decode($facilityAvailability->availability, true);

                    while ($startingHour <= $endingHour) {
                        if (isset($availability[$startingHour])) {
                            if (($availability[$startingHour] < 1)) {
                                $isAvailable = false;
                            }
                            if ($thresholdAvailability > 0) {
                                if ($thresholdAvailability > $availability[$startingHour]) {
                                    $thresholdAvailability = $availability[$startingHour];
                                }
                            } else {
                                $thresholdAvailability = $availability[$startingHour];
                            }
                        }
                        $startingHour++;
                    }
                }
            }

            if ($thresholdAvailability < self::DEFAULT_VALUE) {
                $thresholdAvailability = self::DEFAULT_VALUE;
            }

            if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {

                $dateIn = date('Y-m-d', strtotime($arrival_time));
                $facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();

                if ($facilityAvailability) {
                    //$availabilities = json_decode($facilityAvailability->availability, true);

                    if ($thresholdAvailability >= 0) {

                        $couponThresholdsNew = $facility->facilityCouponThreshold;
                        $couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');

                        $thresholds       = array();
                        if ($couponThresholdsNew) {
                            $thresholdCounter = self::DEFAULT_VALUE;
                            foreach ($couponThresholdsNew as $couponThreshold) {

                                if ($couponThreshold->uptick_type !== 'deleted') {
                                    $thresholds[$thresholdCounter] =
                                        ['threshold' => $couponThreshold->threshold, 'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
                                    $thresholdCounter++;
                                }
                            }
                        }
                        $thresholdPrice = 0;
                        $currentAvailability = $thresholdAvailability;
                        foreach ($thresholds as $key => $threshold) {
                            if ($thresholdAvailability <= $threshold['threshold']) {
                                if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
                                    if ($threshold['uptick_type'] == 'price') {
                                        $thresholdPrice = $threshold['uptick'];

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    } else if ($threshold['uptick_type'] == 'percentage') {
                                        $thresholdPrice =  $threshold['uptick'];
                                        $returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG;

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    }
                                }
                            }
                        }
                        $returnResultArr['coupon_threshold_price'] = $thresholdPrice;

                        $returnResultArr['availability'] = $currentAvailability;
                    }
                }
            }
        }

        return $returnResultArr;
    }


    public function getRevenueCount(Request $request)
    {

        $result = [];
        if (!isset($request->partner_id)) {
            if (Auth::user()->user_type == '1') {
                $result['totalPassPurchaseCount'] = 0;
                $result['totalCheckinCount'] = 0;
                $result['totalRevenue'] = 0;
                $result['shareRevenue'] = 0;
                $result['totalRredeemPassCount'] = 0;
                return $result;
            } elseif (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            } else {
                $result['totalPassPurchaseCount'] = 0;
                $result['totalCheckinCount'] = 0;
                $result['totalRevenue'] = 0;
                $result['shareRevenue'] = 0;
                $result['totalRredeemPassCount'] = 0;
                return $result;
            }
        } else {
            $partner_id = $request->partner_id;
        }

        $partner_id = $partner_id;

        $month = date('m');
        $from_date  = date('Y-' . $month . '-01');
        $mid_date  = date('Y-' . $month . '-15');
        $to_date  = date('Y-' . $month . '-t');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }

        $userConfig = UserPaymentGatewayDetail::where('user_id', $partner_id)->first();
        if (!$userConfig) {
            $result['totalPassPurchaseCount'] = 0;
            $result['totalCheckinCount'] = 0;
            $result['totalRevenue'] = 0;
            $result['shareRevenue'] = 0;
            $result['totalRredeemPassCount'] = 0;
            return $result;
        }
        $checkinShare = $userConfig->checkin_share ? $userConfig->checkin_share : self::SHARE_TICKET_AMOUNT;
        $passShare = $userConfig->pass_share ? $userConfig->pass_share : self::SHARE_PASS_AMOUNT;
        $passCheckinShare = $userConfig->pass_checkin_share ? $userConfig->pass_checkin_share : 0;

        $userPasses = UserPass::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->get();
        $passRevenue = 0.00;
        $totalShareRevenue = 0.00;
        if (count($userPasses) > 0) {
            foreach ($userPasses as $key => $value) {
                $passRevenue += $value->total;
                $totalShareRevenue += ($passShare + $passCheckinShare) * $value->total_days;
            }
        }
        //for booking revenue
        $reservations = Reservation::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('user_pass_id')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->get();
        $bookingRevenue = 0;
        if (count($reservations) > 0) {
            foreach ($reservations as $key => $value) {
                $bookingRevenue += $value->total;
                $totalShareRevenue += $checkinShare;
            }
        }

        $tickets = Ticket::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->count();

        $passRedeem = Reservation::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNotNull('user_pass_id')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->count();

        $ticket = Ticket::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('reservation_id')->whereNull('user_pass_id')->orderBy('id', "DESC")->get();

        $totalDriveUpRevenue = 0;

        if (count($ticket) > 0) {
            foreach ($ticket as $key => $value) {
                $totalDriveUpRevenue += $value->total;
            }
        }

        $result['totalRevenue'] = $bookingRevenue + $passRevenue + $totalDriveUpRevenue;
        $result['totalPassRevenue'] = $passRevenue;
        $result['totalBookingRevenue'] = $bookingRevenue;
        $result['totalDriveUpRevenue'] = $totalDriveUpRevenue;
        $result['shareRevenue'] = $totalShareRevenue;
        $result['totalCheckinCount'] = $tickets;
        $result['totalPassPurchaseCount'] = count($userPasses);
        $result['totalRredeemPassCount'] = $passRedeem;
        return $result;
    }

    function getTotalRevenue(Request $request)
    {
        $month = date('m');
        $from_date  = date('Y-' . $month . '-01');
        $mid_date  = date('Y-' . $month . '-15');
        $to_date  = date('Y-' . $month . '-t');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }

        $result = [];
        if (!isset($request->partner_id)) {
            if (Auth::user()->user_type == '1') {

                $result['total'] = 0;
                $result['data'] = [];
                return $result;
            } elseif (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            } else {
                $result['total'] = 0;
                $result['data'] = [];
                return $result;
            }
        } else {
            $partner_id = $request->partner_id;
        }

        $partner_id = $partner_id;

        if ($request->request_type == '0') {
            //for pass revenue
            if ($request->sort != '') {
                $revenueTickets = UserPass::with('user')->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNotNull('anet_transaction_id')->orderBy($request->sort, $request->sortBy)->paginate(20);
            } else {
                $revenueTickets = UserPass::with('user')->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->paginate(20);
            }
        } elseif ($request->request_type == '1') {
            //for booking revenue

            if ($request->sort != '') {
                $revenueTickets = Reservation::with('user')->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('user_pass_id')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy($request->sort, $request->sortBy)->paginate(20);
            } else {
                $revenueTickets = Reservation::with('user')->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('user_pass_id')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->paginate(20);
            }
        } elseif ($request->request_type == '2') {
            //for driveup revenue        
            if ($request->sort != '') {


                if ($request->sort == 'expiration') {
                    $request->request->add(['sort' => 'expiry']);
                }
                $revenueTickets = Ticket::with(['user', 'transaction'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('reservation_id')->orderBy($request->sort, $request->sortBy)->paginate(20);
            } else {
                $revenueTickets = Ticket::with(['user', 'transaction'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('reservation_id')->orderBy('id', "DESC")->paginate(20);
            }
        } else {
            throw new ApiGenericException('Sorry! Invalid type of revenue.');
        }
        return $revenueTickets;
    }

    public function getUserPasses(Request $request)
    {
        ini_set('max_execution_time', 0);
        $result = [];
        if (Auth::user()->user_type == '1') {
            $partner_id = isset($request->partner_id) ? $request->partner_id : '';
        } elseif (Auth::user()->user_type == '4') {
            $admin_partner_id = Auth::user()->created_by;
            if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                $partner_id = isset($request->partner_id) ? $request->partner_id : '';
            } else {
                $partner_id = Auth::user()->created_by;
            }
        } elseif (Auth::user()->user_type == '12') {
            $partner_id = Auth::user()->created_by;
        } elseif (Auth::user()->user_type == '3') {
            $partner_id = Auth::user()->id;
        } else {
            $partner_id = $request->partner_id;
        }

        $month = date("m");
        //  $from_date  = date('Y-' . $month . '-01');
        //  $to_date  = date('Y-' . $month . '-t');
        //  $mid_date  = date('Y-' . $month . '-15');
        //  $midDateString = strtotime($mid_date);
        //  $lastdate = strtotime(date("Y-m-t", $midDateString));
        //  $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }
        $pass_type = '0';
        if (isset($request->pass_type)) {
            $pass_type = $request->pass_type;
        }
        //->whereNotNull('anet_transaction_id')
        $pass = UserPass::with(['user', 'vehicle', 'transaction', 'facility'])
            //->leftjoin('refund_transaction', 'refund_transaction.refund_ticket_id', '=', 'user_passes.id')
            #PIMS: 11295 Start:
            #Kuldeep
            ->leftjoin('rates', 'rates.id', '=', 'user_passes.rate_id')
            ->leftjoin('anet_transactions', 'anet_transactions.id', '=', 'user_passes.anet_transaction_id')
            //->select('user_passes.*', 'rates.description', 'refund_transaction.refund_type', 'refund_transaction.refund_amount', 'refund_transaction.refund_type', 'refund_transaction.refund_remarks', 'refund_transaction.refund_date', 'refund_transaction.refund_by', 'refund_transaction.refund_transaction_id', 'refund_transaction.refund_status', 'anet_transactions.payment_last_four', 'anet_transactions.card_type', 'anet_transactions.expiration')
            ->select('user_passes.*', 'rates.description', 'anet_transactions.payment_last_four', 'anet_transactions.card_type', 'anet_transactions.expiration')
            ->where('pass_type', $pass_type);
        if (isset($request->search)) {
            $pass = $pass->where(function ($query) use ($request, $pass_type) {
                $query->where('user_passes.id', 'like', "%{$request->search}%")
                    ->orWhere('user_passes.pass_code', 'like', "%{$request->search}%")
                    ->orWhere('user_passes.license_plate', 'like', "%{$request->search}%")
                    ->orWhere('user_passes.phone', 'like', "%{$request->search}%")
                    ->orWhereHas('user', function ($q) use ($request) {
                        $q->where('name', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%");
                    })
                    ->orWhereHas('rate', function ($q) use ($request) {
                        $q->where('description', 'like', "%{$request->search}%");
                    })
                    ->orWhereHas('transaction', function ($q) use ($request) {
                        $q->where('payment_last_four', 'like', "%{$request->search}%");
                    })
                    ->where('pass_type', $pass_type);
            });
        }

        // $pass = $pass->whereDate('user_passes.created_at', '>=', $from_date)->whereDate('user_passes.created_at', '<=', $to_date);

        if (isset($request->from_date) && $request->from_date != '') {
            $pass = $pass->whereDate('user_passes.created_at', '>=', $from_date)->whereDate('user_passes.created_at', '<=', $to_date);
        }

        if (isset($request->facility_id) && !empty($request->facility_id)) {
            $pass = $pass->where(function ($query) use ($request) {
                $query->where('user_passes.facility_id', $request->facility_id);
            });
        }

        if (isset($partner_id) && $partner_id != '') {
            $pass = $pass->where(function ($query) use ($partner_id) {
                $query->where('user_passes.partner_id', $partner_id);
                //  $query->whereNotNull('anet_transaction_id'); 
            });
        }

        if (isset($request->payment_last_four) && !empty($request->payment_last_four)) {
            $pass = $pass->whereHas('transaction', function ($query) use ($request) {
                $query->where('anet_transactions.payment_last_four', $request->payment_last_four);
            });
        }
        if (isset($request->card_type) && !empty($request->card_type)) {
            $pass = $pass->whereHas('transaction', function ($query) use ($request) {
                $query->where('anet_transactions.card_type', $request->card_type);
            });
        }

        if (isset($request->expiration) && !empty($request->expiration)) {
            $pass = $pass->whereHas('transaction', function ($query) use ($request) {
                $query->where('anet_transactions.expiration', $request->expiration);
            });
        }

        if (isset($request->cardholder_name) && !empty($request->cardholder_name)) {
            $pass = $pass->whereHas('transaction', function ($query) use ($request) {
                $query->where('anet_transactions.name', $request->cardholder_name);
            });
        }
        #PIMS: 11295 End:
        if ($request->sort != '') {
            if ($request->sort == 'email') {
                $pass = $pass->orderBy($request->sort, $request->sortBy);
            }
        } else {
            $pass = $pass->orderBy('user_passes.id', 'DESC');
        }

        if ($request->download_type == '1' || $request->download_type == '2') {
            $permitList = $pass->get();


            if ($request->download_type == '2') {
                $this->downloadUserPassPdf($permitList, $request);
            } else {
                $this->downloadUserPermit($permitList, $request);
            }
        }

        $pass = $pass->paginate(20);
        if ($request->sort == 'name') {
            if (count($pass) > 0) {
                if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                    for ($i = 0; $i < count($pass); $i++) {
                        for ($j = $i + 1; $j < count($pass); $j++) {
                            if ($pass[$i]['user']->name > $pass[$j]['user']->name) {
                                $temp = $pass[$i];
                                $pass[$i] = $pass[$j];
                                $pass[$j] = $temp;
                            }
                        }
                    }
                } else {
                    for ($i = 0; $i < count($pass); $i++) {
                        for ($j = $i + 1; $j < count($pass); $j++) {
                            if ($pass[$i]['user']->name < $pass[$j]['user']->name) {
                                $temp = $pass[$i];
                                $pass[$i] = $pass[$j];
                                $pass[$j] = $temp;
                            }
                        }
                    }
                }
            }
        }

        return $pass;
    }

    #PIMS: 11319 Start:
    #Kuldeep
    public function downloadUserPassPdf(Request $request)
    {

        ini_set('max_execution_time', 0);

        $partner_id = null;

        if (Auth::user()->user_type == '1') {
            $partner_id = isset($request->partner_id) ? $request->partner_id : '';
        } elseif (Auth::user()->user_type == '4') {
            $admin_partner_id = Auth::user()->created_by;
            if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                $partner_id = isset($request->partner_id) ? $request->partner_id : '';
            } else {
                $partner_id = Auth::user()->created_by;
            }
        } elseif (Auth::user()->user_type == '12') {
            $partner_id = Auth::user()->created_by;
        } elseif (Auth::user()->user_type == '3') {
            $partner_id = Auth::user()->id;
        } else {
            $partner_id = $request->partner_id;
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }

        $pass = UserPass::with(['user', 'vehicle', 'transaction', 'facility'])
            ->leftjoin('refund_transaction', 'refund_transaction.refund_ticket_id', '=', 'user_passes.id')
            #PIMS: 11295 Start:
            #Kuldeep
            ->leftjoin('rates', 'rates.id', '=', 'user_passes.rate_id')
            ->leftjoin('anet_transactions', 'anet_transactions.id', '=', 'user_passes.anet_transaction_id')
            ->select('user_passes.*', 'rates.description', 'refund_transaction.refund_type', 'refund_transaction.refund_amount', 'refund_transaction.refund_type', 'refund_transaction.refund_remarks', 'refund_transaction.refund_date', 'refund_transaction.refund_by', 'refund_transaction.refund_transaction_id', 'refund_transaction.refund_status', 'anet_transactions.payment_last_four', 'anet_transactions.card_type', 'anet_transactions.expiration');

        if (isset($request->search)) {
            $pass = $pass->where(function ($query) use ($request) {
                $query->where('user_passes.id', 'like', "%{$request->search}%")
                    ->orWhere('user_passes.pass_code', 'like', "%{$request->search}%")
                    ->orWhere('user_passes.license_plate', 'like', "%{$request->search}%")
                    ->orWhereHas('user', function ($q) use ($request) {
                        $q->where('name', 'like', "%{$request->search}%")
                            ->orWhere('email', 'like', "%{$request->search}%");
                    })
                    ->orWhereHas('rate', function ($q) use ($request) {
                        $q->where('description', 'like', "%{$request->search}%");
                    });
            });
        }

        $pass = $pass->whereDate('user_passes.created_at', '>=', $from_date)
            ->whereDate('user_passes.created_at', '<=', $to_date);

        if (isset($partner_id) && $partner_id != '') {
            $pass = $pass->where('user_passes.partner_id', $partner_id);
        }

        $pass = $pass->orderBy('user_passes.id', 'DESC');

        if ($request->download_type == '1' || $request->download_type == '2') {
            $permitList = $pass->get();

            //if ($permitList->isEmpty()) {
            //  throw new ApiGenericException("No Record Found");
            //}
            if ($permitList->isEmpty()) {
                return response()->json([
                    'status' => 404,
                    'data' => null,
                    'errors' => [
                        'message' => 'Sorry! No Data Found.'
                    ]
                ], 404);
            }


            $finalCodes = [];
            foreach ($permitList as $i => $val) {
                $finalCodes[] = [
                    'No'            => $i + 1,
                    'Pass ID'       => $val->pass_code ?? '-',
                    //'Pass Name'     => $val->description ?? '-',
                    'License Plate' => $val->license_plate ?? '-',
                    // 'User Name'     => $val->user->name ?? '-',
                    'Email ID'    => $val->user->email ?? '-',
                    'Total Days'    => $val->total_days ?? '-',
                    'Consumed'      => $val->consume_days ?? '-',
                    'Amount'        => $val->total ?? '-',
                    'Processing Fee' => $val->processing_fee ?? '-',
                    'Purchased Date' => $val->purchased_on ?? '-',
                    'Discount' => isset($val->discount_amount) ? $val->discount_amount : '-',
                    'Refund Amount' => isset($val->refund_amount) ? $val->refund_amount : '-',
                    'Refund date' => isset($val->refund_date) ? $val->refund_date : '-',
                    // 'Expiry Date'   => $val->end_time ?? '-',
                ];
            }

            // Brand and Header Settings
            $brandSetting = $partner_id ? BrandSetting::where('user_id', $partner_id)->first() : null;
            $getLogoId = $brandSetting ? $brandSetting->id : 0;
            $color = $brandSetting ? $brandSetting->color : "#0C4A7A";

            $headerArr = [
                'from_date' => $from_date,
                'to_date' => $to_date,
                'locationName' => 'All',
                'garageCode' => 'All',
            ];

            // Render PDF
            $html = view("download.pass-details", [
                "data" => $finalCodes,
                'getLogoId' => $getLogoId,
                'color' => $color,
                'headerArr' => $headerArr
            ])->render();

            $image = app()->make(Pdf::class);
            $pdf = $image->getOutputFromHtmlString($html);

            return $pdf;
        }

        throw new ApiGenericException("Invalid download type.");
    }
    #PIMS: 11319 PDF End:
    #Kuldeep

    #PIMS: 11319 Start:
    #Kuldeep
    public function downloadUserPermit($permitList, $request)
    {
        $partner_id = $request->partner_id;
        $facility_id = $request->facility_id;
        $brandSetting = null;
        $fromDate = isset($request->from_date) ? date('Y-m-d', strtotime($request->from_date))  : date('Y-m-d');
        $toDate = isset($request->to_date) ? date('Y-m-d', strtotime($request->to_date))  : date('Y-m-d');
        $locationName = 'All';
        $garageCode = 'All';
        $currentDate = date('m-d-Y');

        $excelSheetName = ucwords(str_replace(' ', '', 'PassListExcel'));
        $finalCodes1 = [];
        $increment1 = 1;
        if (count($permitList) > 0) {
            foreach ($permitList as $val) {
                // dd($val);
                $permit_status = '';
                $license_plate_number = '';
                $make = '';
                $model = '';

                $finalCodes1[] = [
                    'Pass ID' => isset($val->pass_code) ? $val->pass_code : '-',
                    // 'Pass Name' => isset($val->description) ? $val->description : '-',
                    'License Plate' => isset($val->license_plate) ? $val->license_plate : '-',
                    'User Name' => isset($val->user->name) ? $val->user->name : '-',
                    'Email ID' => isset($val->user->email) ? $val->user->email : '-',
                    'Total Days' => isset($val->total_days) ? $val->total_days : '-',
                    'Consumed' => isset($val->consume_days) ? $val->consume_days : '-',
                    'Card No (Last 4 Digits)' => isset($val->payment_last_four) ? $val->payment_last_four : '-',
                    'Card Type' => isset($val->card_type) ? $val->card_type : '-',
                    'Card Expiry' => isset($val->expiration) ? $val->expiration : '-',
                    'Amount' => isset($val->total) ? $val->total : '-',
                    'Processing Fee' => isset($val->processing_fee) ? $val->processing_fee : '-',
                    'Discount' => isset($val->discount_amount) ? $val->discount_amount : '-',
                    'PromoCode' => isset($val->promocode) ? $val->promocode : '-',
                    'Refund Amount' => isset($val->refund_amount) ? $val->refund_amount : '-',
                    'Facility Name' => isset($val->facility->full_name) ? $val->facility->full_name : '-',
                    'Purchased Date' => isset($val->purchased_on) ? $val->purchased_on : '-',
                    'Expiry Date' => isset($val->end_time) ? $val->end_time : '-',
                    'Refund Date' => isset($val->refund_date) ? $val->refund_date : '-',
                    'Refund Remarks' => isset($val->refund_remarks) ? $val->refund_remarks : '-',

                ];
                $increment1++;
            }
        } else {
            throw new NotFoundException("Sorry! No Record Found");
        }

        $getLogoId = 0;
        $color = "#0C4A7A";
        if (isset($partner_id) && !empty($partner_id)) {
            $brandSetting = BrandSetting::where('user_id', $partner_id)->first();
            if (isset($brandSetting) && !empty($brandSetting)) {
                $getLogoId = $brandSetting->id;
                $color = $brandSetting->color;
            }
        }

        Excel::create(
            $excelSheetName,
            function ($excel) use ($finalCodes1, $excelSheetName, $brandSetting, $color, $fromDate, $toDate, $locationName, $garageCode) {

                // Set the spreadsheet title, creator, and description
                $excel->setTitle($excelSheetName);
                $excel->setCreator('UserPass')->setCompany('ParkEngage');
                $excel->setDescription('List Of User Pass List');
                // Build the spreadsheet, passing in the payments array
                if (empty($finalCodes1)) {
                    throw new NotFoundException('Sorry! No Data Found.');
                } else {
                    if (isset($finalCodes1) && !empty($finalCodes1)) {
                        $getDateSummary = 'User Pass List';
                        $excel->sheet(
                            'User Pass',
                            function ($sheet) use ($finalCodes1, $brandSetting, $color, $fromDate, $toDate, $locationName, $garageCode, $getDateSummary) {
                                $this->addLogoInExcelHeaders($sheet, $getDateSummary, $brandSetting, $color);
                                $headerSpace = 4;
                                $this->getBrandHeaderSections($sheet, $color, $fromDate, $toDate, $locationName, $garageCode);
                                // $sheet->getRowDimension(3)->setRowHeight(50);
                                $sheet->mergeCells('A3:S3');
                                $sheet->setCellValue('A3', 'User Pass List');
                                $sheet->cell('A3:S3', function ($cell) use ($color) {
                                    $cell->setAlignment('center'); // Center horizontally
                                    $cell->setValignment('center');
                                    $cell->setFontWeight('bold');
                                    $cell->setBackground('#D9E1F2');
                                    $cell->setFontColor('#272829');
                                    $cell->setFontSize('12');
                                });
                                // $sheet->mergeCells('A3:S3');
                                // Color Row For Heading 
                                $sheet->cell("A$headerSpace:S$headerSpace", function ($row) use ($color) {
                                    $row->setBackground($color);
                                    $row->setFontColor('#ffffff');
                                });

                                // Adding data to sheet
                                $sheet->fromArray($finalCodes1, null, 'A' . "$headerSpace", false, true);
                                $j = count($finalCodes1) + 4;
                                for ($i = 4; $i <= $j; $i++) {
                                    $sheet->cell('A' . $i . ':S' . $i, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center'); // Center vertically
                                    });
                                }
                                // $i = $i;
                                // $sheet->cell('A' . $i . ':K' . $i, function ($cell) use ($color) {
                                //   $cell->setAlignment('center'); // Center horizontally
                                //   $cell->setValignment('center'); // Center vertically
                                // });
                            }
                        );
                    }
                }
            }
        )->store('xls')->download('xls');
    }
    #PIMS: 11319 Start:
    #Kuldeep
    public function addLogoInExcelHeaders($excel, $title = "Pass List", $brandSetting, $color = '#191D88')
    {
        /* Code for Logo */
        $logoPath = public_path('assets/media/images/breeze.png');

        // Check if a brand logo is provided
        if (isset($brandSetting) && !empty($brandSetting->logo)) {
            $dynamicLogoPath = storage_path('app/brand-settings/' . $brandSetting->logo);

            if (file_exists($dynamicLogoPath)) {
                $logoPath = $dynamicLogoPath;
            } else {
                logger()->warning("Brand logo not found at: {$dynamicLogoPath}. Using default logo.");
            }
        } else {
            logger()->info("Brand logo is not set. Using default logo.");
        }

        // Set the logo in Excel
        $drawing = new PHPExcel_Worksheet_Drawing();
        $drawing->setPath($logoPath);
        $drawing->setCoordinates('A1');
        $drawing->setWidth(150);
        $drawing->setHeight(50);
        $drawing->setOffsetX(25);
        $drawing->setOffsetY(10);
        $excel->getDrawingCollection()->append($drawing);

        /* End Code for Logo */

        // Header Title Section
        // $excel->mergeCells('D1:Q1'); // Merge cells for title
        // $excel->getRowDimension(1)->setRowHeight(60);
        // $excel->setCellValue('A1', $title); // Set the header title

        // Apply styling to header
        $excel->cell('A1', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            $cell->setFontSize(30);
            $cell->setFontColor('#ffffff');
            $cell->setBackground('#ffffff'); // Set background color
        });
    }


    public function getBrandHeaderSections($excel, $color, $fromDate, $toDate, $locationName, $garageCode)
    {
        $excel->mergeCells('A2:F2');
        $cellValue = "Report Date Range - " . date('m-d-Y', strtotime($fromDate)) . ' - ' . date('m-d-Y', strtotime($toDate));
        $cellValue .= "\nPrint Date - " . date('m-d-Y', strtotime('now'));
        $excel->setCellValue('A2', $cellValue);
        $excel->getStyle('A2')->getAlignment()->setWrapText(true);
        $excel->getRowDimension(2)->setRowHeight(80);

        $excel->cell('A2:Q2', function ($cell) use ($color) {
            $cell->setAlignment('center');
            $cell->setValignment('center');
            $cell->setFontWeight('bold');
            $cell->setBackground($color);
            $cell->setFontColor('#ffffff');
            $cell->setFontSize(18);
        });

        // Add location name
        $location  = "Location Name \n"  .  $locationName;
        $excel->mergeCells('G2:N2');
        $excel->setCellValue('G2', $location);
        $excel->getStyle('G2')->getAlignment()->setWrapText(true);

        $excel->cell('G2:N2', function ($cell) use ($color) {
            $cell->setAlignment('center'); // Center horizontally
            $cell->setValignment('center'); // Center vertically
            $cell->setFontWeight('bold');
            $cell->setBackground($color);
            $cell->setFontColor('#ffffff');
        });

        // Add garage code
        $locationId = "Location ID \n" . $garageCode;
        $excel->mergeCells('O2:S2');
        $excel->setCellValue('O2', $locationId);
        $excel->getStyle('O2')->getAlignment()->setWrapText(true);

        $excel->cell('O2:S2', function ($cell) use ($color) {
            $cell->setAlignment('center');
            $cell->setValignment('center');
        });

        // Main title row
        $excel->mergeCells('A1:S1');
        $excel->getRowDimension(1)->setRowHeight(60);
        $excel->setCellValue('A1', "User Pass List");
        $excel->cell('A1:S1', function ($cell) use ($color) {
            $cell->setAlignment('center');
            $cell->setValignment('center');
            $cell->setFontWeight('bold');
            $cell->setBackground($color);
            $cell->setFontColor('#ffffff');
            $cell->setFontSize(30);
        });
    }

    #PIMS: 11319 End:
    #Kuldeep


    public function downloadRevenueExcel(Request $request)
    {

        $month = date('m');

        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }
        $result = [];
        if (!isset($request->partner_id)) {
            if (Auth::user()->user_type == '1') {
                $result['totalPassPurchaseCount'] = 0;
                $result['totalCheckinCount'] = 0;
                $result['totalRevenue'] = 0;
                $result['shareRevenue'] = 0;
                $result['totalRredeemPassCount'] = 0;
                return $result;
            } elseif (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            } else {
                $result['totalPassPurchaseCount'] = 0;
                $result['totalCheckinCount'] = 0;
                $result['totalRevenue'] = 0;
                $result['shareRevenue'] = 0;
                $result['totalRredeemPassCount'] = 0;
                return $result;
            }
        } else {
            $partner_id = $request->partner_id;
        }

        $partner_id = $partner_id;

        $userConfig = UserPaymentGatewayDetail::where('user_id', $partner_id)->first();
        if (!$userConfig) {
            $result['totalPassPurchaseCount'] = 0;
            $result['totalCheckinCount'] = 0;
            $result['totalRevenue'] = 0;
            $result['shareRevenue'] = 0;
            $result['totalRredeemPassCount'] = 0;
            return $result;
        }
        $checkinShare = $userConfig->checkin_share ? $userConfig->checkin_share : self::SHARE_TICKET_AMOUNT;
        $passShare = $userConfig->pass_share ? $userConfig->pass_share : self::SHARE_PASS_AMOUNT;
        $passCheckinShare = $userConfig->pass_checkin_share ? $userConfig->pass_checkin_share : 0;

        $userPasses = UserPass::with('user')->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->get();
        $passRevenue = 0.00;
        $totalShareRevenue = 0.00;
        /*if(count($userPasses) > 0){  
        foreach ($userPasses as $key => $value) {
            $passRevenue += $value->total;
            $totalShareRevenue += ($passShare + $passCheckinShare) * $value->total_days;
        }
      }*/
        //for booking revenue
        $reservations = Reservation::with('user')->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('user_pass_id')->whereNull('cancelled_at')->whereNotNull('anet_transaction_id')->orderBy('id', "DESC")->get();
        $bookingRevenue = 0;
        /*if(count($reservations) > 0){  
          foreach ($reservations as $key => $value) {
              $bookingRevenue += $value->total;
              $totalShareRevenue += $checkinShare;
          }
        }*/

        $ticket = Ticket::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->whereNull('reservation_id')->whereNull('user_pass_id')->orderBy('id', "DESC")->get();
        $totalDriveUpRevenue = 0;


        $finalCodes1 = [];
        $finalCodes2 = [];
        $finalCodes3 = [];
        $increment1 = 1;
        $increment2 = 1;
        $increment3 = 1;
        $totalRevenue = 0;
        if (count($userPasses) > 0) {
            foreach ($userPasses as $userPass) {
                $finalCodes1[] = [
                    'No.' => $increment1,
                    'Pass Id' => $userPass->pass_code,
                    'Pass Rate($)' => $userPass->total,
                    'Pass Purchase Date' => date("m/d/Y", strtotime($userPass->created_at)),
                    'Days' => $userPass->total_days,
                    'User' => isset($userPass->user->email) ? $userPass->user->email : '',
                ];
                $passRevenue += $userPass->total;
                $totalShareRevenue += ($passShare + $passCheckinShare) * $userPass->total_days;
                $increment1++;
            }
        }
        if (count($reservations) > 0) {
            foreach ($reservations as $reservation) {
                $finalCodes2[] = [
                    'No.' => $increment2,
                    'Booking Id' => $reservation->ticketech_code,
                    'Booking Amount($)' => $reservation->total,
                    'Booking Date' => date("m/d/Y", strtotime($reservation->created_at)),
                    'User' => isset($reservation->user->email) ? $reservation->user->email : '',
                ];
                $bookingRevenue += $reservation->total;
                $totalShareRevenue += $checkinShare;
                $increment2++;
            }
        }
        if (count($ticket) > 0) {
            foreach ($ticket as $key => $value) {
                $finalCodes3[] = [
                    'No.' => $increment3,
                    'Booking Id' => $value->ticket_number,
                    'Driveup Amount($)' => $value->total == '' ? '0.00' : $value->total,
                    'Driveup Date' => date("m/d/Y", strtotime($value->created_at)),
                    'User' => isset($value->user->email) ? $value->user->email : '',
                ];
                $totalDriveUpRevenue += $value->total;
                $increment3++;
            }
        }


        $finalCodes1[] = [
            'No.' => '',
            'Pass Id' => '',
            'Pass Rate($)' => '',
            'Pass Purchase Date' => '',
            'Days' => '',
            'User' => '',
        ];
        $finalCodes1[] = [
            'No.' => '',
            'Pass Id' => '',
            'Pass Rate($)' => '',
            'Pass Purchase Date' => '',
            'Days' => '',
            'User' => '',
        ];
        $finalCodes1[] = [
            'No.' => '',
            'Pass Id' => 'Revenue With Pass',
            'Pass Rate($)' => '$' . number_format($passRevenue, 2),
            'Pass Purchase Date' => '',
            'Days' => '',
            'User' => '',
        ];
        $finalCodes1[] = [
            'No.' => '',
            'Pass Id' => 'Revenue Without Pass',
            'Pass Rate($)' => '$' . number_format($bookingRevenue, 2),
            'Pass Purchase Date' => '',
            'Days' => '',
            'User' => '',
        ];
        $finalCodes1[] = [
            'No.' => '',
            'Booking Id' => 'Revenue With Driveup',
            'Pass Rate($)' => '$' . number_format($totalDriveUpRevenue, 2),
            'Pass Purchase Date' => '',
            'User' => '',
        ];
        $finalCodes1[] = [
            'No.' => '',
            'Pass Id' => 'Total Revenue',
            'Pass Rate($)' => '$' . number_format($bookingRevenue + $passRevenue  + $totalDriveUpRevenue, 2),
            'Pass Purchase Date' => '',
            'Days' => '',
            'User' => '',
        ];
        $finalCodes2[] = [
            'No.' => '',
            'Booking Id' => '',
            'Booking Amount($)' => '',
            'Booking Date' => '',
            'User' => '',
        ];
        $finalCodes2[] = [
            'No.' => '',
            'Booking Id' => '',
            'Pass Rate($)' => '',
            'Pass Purchase Date' => '',
            'User' => '',
        ];

        $finalCodes2[] = [
            'No.' => '',
            'Booking Id' => 'Revenue Without Pass',
            'Pass Rate($)' => '$' . number_format($bookingRevenue, 2),
            'Pass Purchase Date' => '',
            'User' => '',
        ];
        $finalCodes3[] = [
            'No.' => '',
            'Booking Id' => '',
            'Driveup Amount($)' => '',
            'Driveup Date' => '',
            'User' => '',
        ];
        $finalCodes3[] = [
            'No.' => '',
            'Booking Id' => '',
            'Pass Rate($)' => '',
            'Pass Purchase Date' => '',
            'User' => '',
        ];
        $finalCodes3[] = [
            'No.' => '',
            'Booking Id' => 'Revenue With Driveup',
            'Pass Rate($)' => '$' . number_format($totalDriveUpRevenue, 2),
            'Pass Purchase Date' => '',
            'User' => '',
        ];
        if ($partner_id == self::TOWNSEND_PARTNER_ID) {
            $finalCodes3[] = [
                'No.' => '',
                'Booking Id' => 'Total Revenue',
                'Pass Rate($)' => '$' . number_format($totalDriveUpRevenue, 2),
                'Pass Purchase Date' => '',
                'User' => '',
            ];
        }

        if (Auth::user()->user_type == '1') {
            $finalCodes1[] = [
                'No.' => '',
                'Pass Id' => 'ParkEngage Share',
                'Pass Rate($)' => '$' . number_format($totalShareRevenue, 2),
                'Pass Purchase Date' => '',
                'Days' => '',
                'User' => '',
            ];
        }

        $excelSheetName = ucwords(str_replace(' ', '', 'Revenue'));

        Excel::create(
            $excelSheetName,
            function ($excel) use ($finalCodes1, $finalCodes2, $finalCodes3, $excelSheetName, $partner_id) {

                // Set the spreadsheet title, creator, and description
                $excel->setTitle($excelSheetName);
                $excel->setCreator('RevenueDetails')->setCompany('ParkEngage');
                $excel->setDescription('List Of Revenue');
                if ($partner_id != self::TOWNSEND_PARTNER_ID) {
                    // Build the spreadsheet, passing in the payments array
                    $excel->sheet(
                        'With Pass',
                        function ($sheet) use ($finalCodes1) {
                            $sheet->fromArray($finalCodes1, null, 'A1', false, true);
                        }
                    );

                    $excel->sheet(
                        'Without Pass',
                        function ($sheet) use ($finalCodes2) {
                            $sheet->fromArray($finalCodes2, null, 'A1', false, true);
                        }
                    );
                }

                $excel->sheet(
                    'With Driveup',
                    function ($sheet) use ($finalCodes3) {
                        $sheet->fromArray($finalCodes3, null, 'A1', false, true);
                    }
                );
            }
        )->store('xls')->download('xls');
    }


    public function saveRevenuePaymentHistory(Request $request)
    {
        $userConfig = UserPaymentGatewayDetail::where('user_id', $request->partner_id)->first();
        if (!$userConfig) {
            throw new NotFoundException('Invalid Partner.');
        }

        if ($request->paid_amount > $userConfig->total_balance) {
            throw new ApiGenericException('Paid amount can not greater than total amount.');
        }

        $from_date  = date($request->from_date . '-01');
        $mid_date  = date($request->from_date . '-15');
        $to_date  = date($request->to_date . '-t');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);


        $data['total'] = $request->paid_amount;
        $data['partner_id'] = $request->partner_id;
        $data['created_by'] = Auth::user()->id;
        $data['comment'] = $request->comment;
        $data['from_date'] = $from_date;
        $data['to_date'] = $to_date;
        $result = RevenuePaymentHistory::create($data);

        $remainingBalance = $userConfig->total_balance - $request->paid_amount;
        $userConfig->total_balance = $remainingBalance;
        $userConfig->remainder = $remainingBalance;
        $userConfig->last_paid_amount = $request->paid_amount;
        $userConfig->last_payment_date = date("Y-m-d");
        $userConfig->save();

        return $result;
    }


    public function getBillingHistory(Request $request)
    {
        if (Auth::user()->user_type == '4') {
            $admin_partner_id = Auth::user()->created_by;
            if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                if ($request->partner_id != '') {
                }
            } else {
                $request->request->add(['partner_id' => Auth::user()->created_by]);
            }
        } else if (Auth::user()->user_type == '12') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
        }

        $userConfig = UserPaymentGatewayDetail::select('total_balance', 'current_month_balance', 'remainder')->where('user_id', $request->partner_id)->first();
        if (!$userConfig) {
            throw new NotFoundException('Sorry! Bill is not generated for this partner.');
        }
        $totalBill = [];
        if (isset($request->month_year)) {
            $monthYear = $request->month_year;
            $from_date  = date($monthYear . '-01');
            $mid_date  = date($monthYear . '-15');
            $to_date  = date($monthYear . '-t');
            $midDateString = strtotime($mid_date);
            $lastdate = strtotime(date("Y-m-t", $midDateString));
            $to_date = date("Y-m-d", $lastdate);

            $revenueBalance = RevenueBalance::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $from_date)->whereDate('to_date', '=', $to_date)->get();
            if ($revenueBalance) {
                $totalShare = 0;
                foreach ($revenueBalance as $key => $value) {
                    $totalShare += $value->total_share;
                    //$totalBill['previous_balance'] = $value->remainder;
                }
                $totalBill['from_date'] = $from_date;
                $totalBill['to_date'] = $to_date;
                $totalBill['bill_amount'] = number_format($totalShare, 2);
                //$totalBill['previous_balance'] = $totalShare;
            }
        } else {
            $defaultMonths = 36;
            $j = 0;
            $totalBill = [];
            for ($i = 1; $i <= $defaultMonths; $i++) {
                //for ($i = 12; $i <= $defaultMonths; $i++) {
                $monthYear =  date('Y-m', strtotime("-$i month"));
                //$monthYear =  date('Y-m', strtotime("m"));
                $from_date  = date($monthYear . '-01');
                $mid_date  = date($monthYear . '-15');
                $to_date  = date($monthYear . '-t');
                $midDateString = strtotime($mid_date);
                $lastdate = strtotime(date("Y-m-t", $midDateString));
                $to_date = date("Y-m-d", $lastdate);
                $revenueBalance = RevenueBalance::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $from_date)->whereDate('to_date', '=', $to_date)->get();
                if ($revenueBalance) {
                    $totalShare = 0;
                    foreach ($revenueBalance as $key => $value) {
                        $totalShare += $value->total_share;
                        $remainder = $value->remainder;
                    }

                    if ($totalShare > 0) {
                        $totalBill[$j]['from_date'] = $from_date;
                        $totalBill[$j]['to_date'] = $to_date;
                        $totalBill[$j]['bill_amount'] = number_format($totalShare, 2);
                        $totalBill[$j]['previous_balance'] = number_format($remainder, 2);

                        $paymentHistory = RevenuePaymentHistory::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $from_date)->whereDate('to_date', '=', $to_date)->get();
                        $paidAmount = 0;
                        if ($paymentHistory) {
                            foreach ($paymentHistory as $k => $v) {
                                $paidAmount += $v->total;
                            }
                            $totalBill[$j]['paid_amount'] = number_format($paidAmount, 2);
                        } else {
                            $totalBill[$j]['paid_amount'] = number_format($paidAmount, 2);
                        }
                    }
                }
                $j++;
            }
        }
        $result['remaining_balance'] = $userConfig;
        $result['billingDetails'] = $totalBill;
        return $result;
    }


    public function getInvoicePdf(Request $request)
    {
        if (Auth::user()->user_type == '4') {
            $admin_partner_id = Auth::user()->created_by;
            if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                if ($request->partner_id != '') {
                }
            } else {
                $request->request->add(['partner_id' => Auth::user()->created_by]);
            }
        } else if (Auth::user()->user_type == '12') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
        }
        $billingDetails = User::with('userPaymentGatewayDetail')->where('id', $request->partner_id)->first();

        $from_date  = date($request->from_date . '-01');
        $mid_date  = date($request->from_date . '-15');
        $to_date  = date($request->to_date . '-t');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        $last_from_date = date("Y-m-d", strtotime($from_date . "-1 month"));
        $last_to_date = date("Y-m", strtotime($from_date . "-1 month"));
        $last_mid_date  = date($last_to_date . '-15');
        $lastMidDateString = strtotime($last_mid_date);
        $lastLastdate = strtotime(date("Y-m-t", $lastMidDateString));
        $last_to_date = date("Y-m-d", $lastLastdate);

        //last month revenye
        $lastRevenueBalance = RevenueBalance::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $last_from_date)->whereDate('to_date', '=', $last_to_date)->orderBy('id', 'DESC')->first();
        $revenueBalance = RevenueBalance::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $from_date)->whereDate('to_date', '=', $to_date)->orderBy('id', 'DESC')->first();
        if ($revenueBalance) {
            $countBookingShare = 0;
            $countpassShare = 0;
            $bookingShareAmount = 0;
            $passShareAmount = 0;
            $passRate = 0;
            $bookingRate = 0;
            $paidAmount = 0;
            $paidDate = '';
            $lastPaidAmount = 0;
            $lastPaidDate = '';

            //last month revenue
            $lastRevenueHistory = RevenuePaymentHistory::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $last_from_date)->whereDate('to_date', '=', $last_to_date)->get();

            if ($lastRevenueHistory) {
                foreach ($lastRevenueHistory as $k => $v) {
                    $lastPaidAmount += $v->total;
                    $lastPaidDate = $v->created_at;
                }
                $billingDetails->last_paid_amount = $lastPaidAmount;
                $billingDetails->last_paid_amount_date = $lastPaidDate;
            } else {
                $billingDetails->last_paid_amount = $lastPaidAmount;
                $billingDetails->last_paid_amount_date = '';
            }

            $revenueHistory = RevenuePaymentHistory::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $from_date)->whereDate('to_date', '=', $to_date)->get();
            if ($revenueHistory) {
                foreach ($revenueHistory as $k => $v) {
                    $paidAmount += $v->total;
                    $paidDate = $v->created_at;
                }
                $billingDetails->paid_amount = $paidAmount;
                $billingDetails->paid_amount_date = $paidDate;
            } else {
                $billingDetails->paid_amount = $paidAmount;
                $billingDetails->paid_amount_date = '';
            }
            $billingDetails->revenue_balance = $revenueBalance;
            $billingDetails->last_revenue_balance = $lastRevenueBalance;
            $pdf = (new RevenueBalance())->generateInvoice($billingDetails, Pdf::class);
            return $this->respondWithPdf($pdf);
        } else {
            throw new ApiGenericException('Sorry! No billing generated for this month.');
        }
    }


    public function getAutogateInvoicePdf(Request $request)
    {
        if (Auth::user()->user_type == '12') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
        }
        if (Auth::user()->user_type == '4') {
            $admin_partner_id = Auth::user()->created_by;
            if ($admin_partner_id == config('parkengage.PARTNER_SUPERADMIN')) {
                if ($request->partner_id != '') {
                }
            } else {
                $request->request->add(['partner_id' => Auth::user()->created_by]);
            }
        }
        $billingDetails = User::with('userPaymentGatewayDetail')->where('id', $request->partner_id)->first();

        $from_date  = date($request->from_date . '-01');
        $mid_date  = date($request->from_date . '-15');
        $to_date  = date($request->to_date . '-t');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        $last_from_date = date("Y-m-d", strtotime($from_date . "-1 month"));
        $last_to_date = date("Y-m", strtotime($to_date . "-1 month"));
        $last_mid_date  = date($last_to_date . '-15');
        $lastMidDateString = strtotime($last_mid_date);
        $lastLastdate = strtotime(date("Y-m-t", $lastMidDateString));
        $last_to_date = date("Y-m-d", $lastLastdate);

        //last month revenye
        $lastRevenueBalance = RevenueBalance::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $last_from_date)->whereDate('to_date', '=', $last_to_date)->orderBy('id', 'DESC')->first();
        $revenueBalance = RevenueBalance::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $from_date)->whereDate('to_date', '=', $to_date)->orderBy('id', 'DESC')->first();
        if ($revenueBalance) {
            $countBookingShare = 0;
            $countpassShare = 0;
            $bookingShareAmount = 0;
            $passShareAmount = 0;
            $passRate = 0;
            $bookingRate = 0;
            $paidAmount = 0;
            $paidDate = '';
            $lastPaidAmount = 0;
            $lastPaidDate = '';

            //last month revenue
            $lastRevenueHistory = RevenuePaymentHistory::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $last_from_date)->whereDate('to_date', '=', $last_to_date)->get();

            if ($lastRevenueHistory) {
                foreach ($lastRevenueHistory as $k => $v) {
                    $lastPaidAmount += $v->total;
                    $lastPaidDate = $v->created_at;
                }
                $billingDetails->last_paid_amount = $lastPaidAmount;
                $billingDetails->last_paid_amount_date = $lastPaidDate;
            } else {
                $billingDetails->last_paid_amount = $lastPaidAmount;
                $billingDetails->last_paid_amount_date = '';
            }

            $revenueHistory = RevenuePaymentHistory::where('partner_id', $request->partner_id)->whereDate('from_date', '=', $from_date)->whereDate('to_date', '=', $to_date)->get();
            if ($revenueHistory) {
                foreach ($revenueHistory as $k => $v) {
                    $paidAmount += $v->total;
                    $paidDate = $v->created_at;
                }
                $billingDetails->paid_amount = $paidAmount;
                $billingDetails->paid_amount_date = $paidDate;
            } else {
                $billingDetails->paid_amount = $paidAmount;
                $billingDetails->paid_amount_date = '';
            }
            $billingDetails->revenue_balance = $revenueBalance;
            $billingDetails->last_revenue_balance = $lastRevenueBalance;
            $pdf = (new RevenueBalance())->generateAutogateInvoice($billingDetails, Pdf::class);
            return $this->respondWithPdf($pdf);
        } else {
            throw new ApiGenericException('Sorry! No billing generated for this month.');
        }
    }


    public function confirmAdminCheckin(Request $request)
    {
        $reservation = Reservation::find($request->id);
        if (!$reservation) {
            throw new ApiGenericException('Invalid booking ID');
        }

        $alreadyCheckin = Ticket::where(function ($query) use ($reservation) {
            if (isset($reservation->user_id) && !empty($reservation->user_id)) {
                $query->where('user_id', $reservation->user_id);
            } else if (isset($reservation->id) && !empty($reservation->id)) {
                $query->where('reservation_id', $reservation->id);
            }
        })->where('facility_id', $reservation->facility_id)->where('is_checkout', '0')->first();

        if ($alreadyCheckin) {
            throw new ApiGenericException('User already checkin. Please checkout first.');
        }

        // checked strat time before checkin (Ashutosh 18-09-2023)
        $this->setCustomTimezone($reservation->facility_id); //added the time zone according to facility for checkin 
        $config = Configuration::where('field_name', 'prepaid-checkin-time')->where("facility_id", $reservation->facility_id)->first();
        if (count($config) > 0) {
            $prepaidCheckinTime = $config->field_value;
        } else {
            $prepaidCheckinTime = 15;
        }
        $today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);

        $time = Carbon::parse($reservation->start_timestamp)->addHours($reservation->length);
        if (intval($reservation->length) != $reservation->length) {
            $timarr = explode('.', $reservation->length);
            // $minute = ('.' . $timarr[1]) * 60; 
            $time->addMinutes($timarr[1]);
        }
        $reservationEndDate = $time->format('Y-m-d H:i:s');

        if (strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)) {
            throw new ApiGenericException('Early for reservation.');
        }

        if (strtotime($today) < strtotime($reservation->start_timestamp)) {
            throw new ApiGenericException('Early for reservation.');
        }

        $this->log->info("admin about to checkin against booking : " . $reservation->id);
        //check gate api
        if ($reservation->facility->check_vehicle_enabled == '1') {
            $gateStatus = $this->isParkEngageGateAvailable($reservation->facility_id, $request->gate, '');
            if ($gateStatus == "true") {
            } else {
                throw new ApiGenericException($gateStatus);
            }
        }

        $data['user_id'] = $reservation->user_id;
        $data['reservation_id'] = $reservation->id;
        $data['facility_id'] = $reservation->facility_id;
        $data['is_checkin'] = 1;
        $data['ticket_number'] = $this->checkTicketNumber();
        $data['checkin_time'] = date('Y-m-d H:i:s');
        $data['ticket_security_code'] = rand(1000, 9999);
        $data['partner_id'] = $reservation->partner_id;
        $data['check_in_datetime'] = $reservation->start_timestamp;
        $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
        $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)->subSeconds(1)));
        $data['total'] = $reservation->thirdparty_integration_id == 0 ? $reservation->total : '0';
        $data['grand_total'] = $reservation->thirdparty_integration_id == 0 ? $reservation->total : '0';
        $data['parking_amount'] = $reservation->parking_amount;
        $data['tax_fee'] = $reservation->tax_fee;
        $data['processing_fee'] = $reservation->processing_fee;
        $data['discount_amount'] = $reservation->discount;
        $data['length'] = $reservation->length;
        $data['checkin_gate'] = $request->gate;
        $data['user_pass_id'] = $reservation->user_pass_id;
        $data['remark'] = $request->remark;
        $data['checkin_by'] = Auth::user()->id;
        $data['device_type'] = isset($request->device_type) ? $request->device_type : 'admin';

        // added the license_plate from reservation by Ashutosh 29-09-2023
        if (isset($reservation->license_plate) && !empty($reservation->license_plate)) {
            $data['license_plate'] = $reservation->license_plate;
        }
        $ticket = Ticket::create($data);

        $reservation->is_ticket = '1';
        $reservation->save();

        if ($ticket) {
            Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
        }
        $this->log->info("admin checkin done ticket is : " . $ticket->ticket_number);
        return $ticket;
    }


    public function isParkEngageGateAvailable($facility_id, $gate, $phone = '')
    {

        $facility = Facility::where('id', $facility_id)->first();

        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

        //check third party gate API
        //$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        if ($facility) {
            if ($facility->adam_host != '') {
                $params = ['gate_id' => $gate->gate];
                $this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
                $response = ParkengageGateApi::isvehicleAvailable($params, $facility->adam_host);
                $this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response" . json_encode($response));
                if ($response['success'] == false) {
                    $msg = "The system is not currently available. Please try again later.";
                    return $msg;
                }
                if (isset($response['data'][0]) && $response['data'][0] == "true") {
                    $cmd_params = ['gate_id' => $gate->gate];
                    $this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params" . json_encode($cmd_params));
                    if ($gate->is_external_gate == '1' || $gate->is_external_gate == 1) {
                        $command_response = ParkengageGateApi::openExternalGate($cmd_params, $facility->adam_host);
                    } else {
                        $command_response = ParkengageGateApi::openGate($cmd_params, $facility->adam_host);
                    }
                    $this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response" . json_encode($command_response));
                    if ($command_response['success'] == true) {
                        if ($command_response['data'][0] == "true") {
                            return true;
                        } else {
                            $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                            return $msg;
                        }
                    } else {
                        $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                        return $msg;
                    }
                } else {

                    $msg = "Your vehicle must be in the garage in front of the " . ucwords($gate->gate_type) . " gate.";
                    return $msg;
                }
            }
        }
    }


    public function getcitationList(Request $request)
    {

        $tickets = TicketCitation::with(['user', 'transaction', 'ticketCitationInfraction.ticketCitationInfractionReason', 'permit']);

        if ($request->search) {
            $tickets = $tickets->where('citation_number', 'like', "%$request->search%")->orWhere('license_plate', 'like', "%$request->search%");
            $tickets = $tickets->orWhereHas(
                'user',
                function ($query) use ($request) {
                    $query
                        ->where('phone', 'like', "%{$request->search}%")
                        ->orWhere('email', 'like', "%{$request->search}%");
                }
            );
        }

        $tickets = $tickets->where('partner_id', Auth::user()->id);
        if ($request->is_closed != '') {
            $tickets = $tickets->where('is_closed', $request->is_closed);
        }
        if ($request->facility_id) {
            $tickets = $tickets->where(function ($query) use ($request) {
                $query->where('facility_id', $request->facility_id);
            });
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date('Y-m-d', strtotime($request->from_date));
            $to_date = date('Y-m-d', strtotime($request->to_date));
        }
        $tickets = $tickets->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);
        if ($request->sort != '') {
            $tickets = $tickets->orderBy($request->sort, $request->sortBy);
        } else {
            $tickets = $tickets->orderBy("id", "DESC");
        }

        /*$authenticated_user = Auth::user();
      $tickets = $tickets->where(function($query) use($authenticated_user) {
          $query->where('partner_id', $authenticated_user->id);
      });*/
        $tickets = $tickets->paginate(20);
        return $tickets;
    }


    public function getAutogateRevenueCount(Request $request)
    {

        $result = [];
        if (!isset($request->partner_id)) {
            if (Auth::user()->user_type == '1') {
                $result['totalPassPurchaseCount'] = 0;
                $result['totalCheckinCount'] = 0;
                $result['totalRevenue'] = 0;
                $result['shareRevenue'] = 0;
                $result['totalRredeemPassCount'] = 0;
                return $result;
            } elseif (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            } else {
                $result['totalPassPurchaseCount'] = 0;
                $result['totalCheckinCount'] = 0;
                $result['totalRevenue'] = 0;
                $result['shareRevenue'] = 0;
                $result['totalRredeemPassCount'] = 0;
                return $result;
            }
        } else {
            $partner_id = $request->partner_id;
        }

        $partner_id = $partner_id;

        $month = date('m');
        $from_date  = date('Y-' . $month . '-01');
        $mid_date  = date('Y-' . $month . '-15');
        $to_date  = date('Y-' . $month . '-t');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }

        $userConfig = UserPaymentGatewayDetail::where('user_id', $partner_id)->first();
        if (!$userConfig) {
            $result['totalPassPurchaseCount'] = 0;
            $result['totalCheckinCount'] = 0;
            $result['totalRevenue'] = 0;
            $result['shareRevenue'] = 0;
            $result['totalRredeemPassCount'] = 0;
            return $result;
        }
        $checkinShare = $userConfig->checkin_share ? $userConfig->checkin_share : self::SHARE_TICKET_AMOUNT;
        $citationShare = $userConfig->citation_share ? $userConfig->citation_share : self::SHARE_PASS_AMOUNT;
        //$passCheckinShare = $userConfig->pass_checkin_share ? $userConfig->pass_checkin_share : 0;*/

        $totalShareRevenue = 0;
        $tickets = Ticket::with(['overstay'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->get();

        $ticketRevenue = 0;
        if (count($tickets) > 0) {
            foreach ($tickets as $key => $value) {
                if (count($value->overstay) == '0') {
                    if (strtotime(date("Y-m-d", strtotime($value->check_in_datetime))) == strtotime(date("Y-m-d", strtotime($value->checkout_datetime)))) {
                        $totalShareRevenue += $checkinShare;
                        $ticketRevenue += $value->total;
                        //echo $ticketRevenue.'--1-';
                    } else {
                        $checkinDate = Carbon::parse(date("Y-m-d", strtotime($value->check_in_datetime)));
                        $checkoutDate = date("Y-m-d", strtotime($value->checkout_datetime));
                        $totalShareRevenue += $checkinDate->diffInDays($checkoutDate) * $checkinShare;
                        $ticketRevenue += $value->total;
                    }
                } else {
                    if ($value->overstay) {
                        $OverstayTotal = 0;
                        $checkinDate = Carbon::parse(date("Y-m-d", strtotime($value->check_in_datetime)));
                        $checkoutDate = date("Y-m-d", strtotime($value->checkout_datetime));
                        $totalTicketDays = $checkinDate->diffInDays($checkoutDate) + 1;
                        $totalOverstayDays = 0;
                        foreach ($value->overstay as $k => $v) {
                            $overstayCheckinDate = Carbon::parse(date("Y-m-d", strtotime($v->check_in_datetime)));
                            $overstayCheckoutDate = date("Y-m-d", strtotime($v->checkout_datetime));
                            $totalOverstayDays += $overstayCheckinDate->diffInDays($overstayCheckoutDate) + 1;
                            $ticketRevenue += $v->total;
                        }
                        //dd($totalTicketDays, $totalOverstayDays);
                        $ticketDays = $totalTicketDays - $totalOverstayDays;
                        $ticketRevenue += $value->total;
                        $totalShareRevenue += ($ticketDays) * $checkinShare;
                        $totalShareRevenue += $totalOverstayDays * $citationShare;
                    }
                }
            }
        }
        //echo $totalShareRevenue;
        $citations = TicketCitation::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->get();

        $citationRevenue = 0;
        if (count($citations) > 0) {
            foreach ($citations as $key => $value) {
                $totalShareRevenue += $citationShare;
                if (strtotime(date("Y-m-d", strtotime($value->checkin_time))) == strtotime(date("Y-m-d", strtotime($value->checkout_time)))) {
                    if ($value->anet_transaction_id != '') {
                        $totalShareRevenue += $citationShare;
                        $citationRevenue += $value->total;
                    }
                } else {
                    if ($value->anet_transaction_id != '') {
                        $checkinDate = Carbon::parse(date("Y-m-d", strtotime($value->checkin_time)));
                        $checkoutDate = date("Y-m-d", strtotime($value->checkout_time));
                        $totalShareRevenue += $citationShare + $checkinDate->diffInDays($checkoutDate) * $citationShare;
                        $citationRevenue += $value->total;
                    }
                }
            }
        }
        //echo "--".$totalShareRevenue;
        $overstayTickets = OverstayTicket::where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date)->count();


        $result['totalRevenue'] = number_format($ticketRevenue + $citationRevenue, 2);
        /*$result['totalPassRevenue'] = $passRevenue;
          $result['totalBookingRevenue'] = $bookingRevenue;*/
        $result['shareRevenue'] = number_format($totalShareRevenue, 2);
        $result['totalCheckinCount'] = count($tickets);
        $result['totalCitationsCount'] = count($citations);
        $result['totalOverstayCount'] = $overstayTickets;
        return $result;
    }


    function getAutogateTotalRevenue(Request $request)
    {
        $month = date('m');
        $from_date  = date('Y-' . $month . '-01');
        $mid_date  = date('Y-' . $month . '-15');
        $to_date  = date('Y-' . $month . '-t');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = $request->from_date;
            $to_date = $request->to_date;
        }

        $result = [];
        if (!isset($request->partner_id)) {
            if (Auth::user()->user_type == '1') {

                $result['total'] = 0;
                $result['data'] = [];
                return $result;
            } elseif (Auth::user()->user_type == '3') {
                $partner_id = Auth::user()->id;
            } else {
                $result['total'] = 0;
                $result['data'] = [];
                return $result;
            }
        } else {
            $partner_id = $request->partner_id;
        }

        $partner_id = $partner_id;

        $tickets = Ticket::with(['user', 'overstay'])->where('partner_id', $partner_id)->whereDate('created_at', '>=', $from_date)->whereDate('created_at', '<=', $to_date);

        if ($request->sort != '') {
            $tickets = $tickets->orderBy($request->sort, $request->sortBy);
        } else {
            $tickets = $tickets->orderBy("id", "DESC");
        }
        $tickets = $tickets->paginate(20);
        return $tickets;
    }


    public function customeReplySms($msg, $phone, $imageURL = '')
    {
        try {
            if ($phone == '') {
                return "success";
            }
            $this->log->info("sms about to send");
            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
            $client = new Client($accountSid, $authToken);
            try {

                /*$imageBarcode = $this->generateBarcodeJpgNew("111");

            $imageBarcodeFileName = str_random(10) . '_qrcode.png';
          Storage::put($imageBarcodeFileName, $imageBarcode);
            $imageBarcodeFileName = str_random(10) . '_qrcode.png';
            Storage::put($imageBarcodeFileName, $imageBarcode);
            dd($imageBarcodeFileName, $imageBarcode);
            $data['bar_image_path'] = $imageBarcodeFileName;*/

                // Use the client to do fun stuff like send text messages!
                $client->messages->create(
                    // the number you'd like to send the message to
                    $phone,
                    array(
                        // A Twilio phone number you purchased at twilio.com/console
                        'from' => config('parkengage.TWILIO_PHONE'),
                        // the body of the text message you'd like to send
                        //'body' => "Fine"
                        'body' => "$msg",
                        //'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)

                    )
                );
                $this->log->info("Message : {$msg} sent to $phone");
                return "success";
            } catch (RestException $e) {
                //echo "Error: " . $e->getMessage();
                $this->log->error($e->getMessage());
                return "success";
            }
        } catch (RestException $e) {
            //echo "Error: " . $e->getMessage();
            $this->log->error($e->getMessage());
            return "success";
        }
    }


    public function driveUpCheckinCheckout(Request $request)
    {
        if ($request->case == 1) {
            $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
            if (!$gate) {
                throw new ApiGenericException("The system is not currently available. Please try again later.");
            }
            if (isset($gate) && $gate->active == '0') {
                throw new ApiGenericException('The system is not currently available. Please try again later.');
            }
            if (isset($gate) && $gate->gate_type == "entry") {
                $permit_request = PermitRequest::with('PermitVehicle', 'user')->where('id', $request->id)->orderBy('id', 'DESC')->first();  // check permit or permit request.
                //return $permit_request;

                $permitCheckin = PermitTicket::where('user_id', $permit_request->user_id)->where('is_checkin', '1')->where('is_checkout', '0')->orderBy('id', 'DESC')->first();
                if ($permitCheckin) {
                    throw new NotFoundException('Already Checkin.');
                }

                $data['partner_id'] = $permit_request->partner_id;
                $data['user_id'] = $permit_request->user_id;
                $data['checkin_gate'] = $request->gate_id;
                $data['facility_id'] = $permit_request->facility_id;
                $data['is_checkin'] = 1;
                $data['ticket_number'] = $this->checkPermitTicketNumber();
                $data['check_in_datetime'] = date('Y-m-d H:i:s');
                //$data['checkout_datetime'] = $userPass->desired_end_date;
                $data['checkin_time'] = date('Y-m-d H:i:s');
                $data['ticket_security_code'] = rand(1000, 9999);
                //$data['permit_request_id'] = $permit_request->id;
                // $data['total'] = $userPass->permit_rate;
                //$data['grand_total'] = $userPass->permit_rate;
                $result = PermitTicket::create($data);
                $ticketnum = $result->ticket_number;
            }
            if (isset($gate) && $gate->gate_type == "exit") {
                throw new ApiGenericException("You can't checkout drive-up in Enforcement app.");
            }
            $driveup['type'] = 'driveup';
            $driveup['account_number'] = $ticketnum;
            $driveup['name'] = $permit_request->user->name;
            $driveup['phone'] = $permit_request->user->phone;
            $cdate = date('Y-m-d H:i:s');
            $driveup['formatted_desired_start_date'] = date("jS F,Y H:i:s", strtotime($cdate));

            if ($permit_request->user->phone) {
                $msg = "Thank you for Check-in. Your Ticket Number #" . $ticketnum;
                $this->customeReplySms($msg, $permit_request->user->phone);
            }

            return  $driveup;
        } elseif ($request->case == 2) {
            $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
            if (!$gate) {
                throw new ApiGenericException("The system is not currently available. Please try again later.");
            }
            if (isset($gate) && $gate->active == '0') {
                throw new ApiGenericException('The system is not currently available. Please try again later.');
            }
            if (isset($gate) && $gate->gate_type == "entry") {
                $userPass = UserPass::with('user')->where("id", $request->id)->orderBy('id', 'DESC')->first(); // check user pass

                $permitCheckin = PermitTicket::where('user_id', $userPass->user_id)->where('is_checkin', '1')->where('is_checkout', '0')->orderBy('id', 'DESC')->first();
                if ($permitCheckin) {
                    throw new NotFoundException('Already Checkin.');
                }

                $data['partner_id'] = $userPass->partner_id;
                $data['user_id'] = $userPass->user_id;
                $data['checkin_gate'] = $request->gate_id;
                $data['facility_id'] = $userPass->facility_id;
                $data['is_checkin'] = 1;
                $data['ticket_number'] = $this->checkPermitTicketNumber();
                $data['check_in_datetime'] = date('Y-m-d H:i:s');
                //$data['checkout_datetime'] = $userPass->desired_end_date;
                $data['checkin_time'] = date('Y-m-d H:i:s');
                $data['ticket_security_code'] = rand(1000, 9999);
                //$data['user_pass_id'] = $upid;
                // $data['total'] = $userPass->permit_rate;
                //$data['grand_total'] = $userPass->permit_rate;
                $result = PermitTicket::create($data);
                $ticketnum = $result->ticket_number;
            }
            if (isset($gate) && $gate->gate_type == "exit") {
                $userPass = UserPass::with('user')->where("id", $request->id)->orderBy('id', 'DESC')->first(); // check user pass
                $permitCheckin = PermitTicket::where('user_id', $userPass->user_id)->where('is_checkout', "0")->orderBy('id', 'DESC')->first();
                if ($permitCheckin) {
                    $permitCheckin->is_checkout = 1;
                    $permitCheckin->checkout_gate = $request->gate_id;
                    $permitCheckin->checkout_time = date("Y-m-d H:i:s");
                    $permitCheckin->save();


                    $userPass['type'] = 'pass';
                    $userPass['account_number'] = $userPass->pass_code;
                    $userPass['name'] = $userPass->user->name;
                    $userPass['formatted_desired_start_date'] = date("jS F,Y", strtotime($userPass->start_date));
                    $userPass['formatted_desired_end_date'] = date("jS F,Y", strtotime($userPass->end_date));
                    return $userPass;
                } else {
                    throw new ApiGenericException("You can't checkout drive-up in Enforcement app.");
                }
            }

            $driveup['type'] = 'driveup';
            $driveup['account_number'] = $ticketnum;
            $driveup['name'] = $userPass->user->name;
            $driveup['phone'] = $userPass->user->phone;
            $cdate = date('Y-m-d H:i:s');
            $driveup['formatted_desired_start_date'] = date("jS F,Y H:i:s", strtotime($cdate));

            if ($userPass->user->phone) {
                $msg = "Thank you for Check-in. Your Ticket Number #" . $ticketnum;
                $this->customeReplySms($msg, $userPass->user->phone);
            }
            return  $driveup;
        } else {

            $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();

            if (!$gate) {
                throw new ApiGenericException("The system is not currently available. Please try again later.");
            }
            if (isset($gate) && $gate->active == '0') {
                throw new ApiGenericException('The system is not currently available. Please try again later.');
            }


            if (isset($gate) && $gate->gate_type == "entry") {
                $user = User::where('license_number', $request->license_plate)->orderBy('id', 'DESC')->first();
                //return $user->id;
                if ($user) {
                    $permitCheckin = Ticket::where('user_id', $user->id)->where('facility_id', $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->orderBy('id', 'DESC')->first();
                    if ($permitCheckin) {
                        throw new NotFoundException('Already checkin.');
                    } else {
                        $data['partner_id'] = $gate->partner_id;
                        $data['checkin_gate'] = $request->gate_id;
                        $data['facility_id'] = $request->facility_id;
                        $data['user_id'] = $user->id;
                        $data['is_checkin'] = 1;
                        $data['ticket_number'] = $this->checkTicketNumber();
                        $data['check_in_datetime'] = date('Y-m-d H:i:s');
                        //$data['checkout_datetime'] = $userPass->desired_end_date;
                        $data['checkin_time'] = date('Y-m-d H:i:s');
                        $data['ticket_security_code'] = rand(1000, 9999);
                        $data['vp_device_checkin'] = '1';
                        $result = Ticket::create($data);
                        $ticketnum = $result->ticket_number;
                    }
                } else {

                    $data['user_type'] = 5;
                    $data['license_number'] = $request->license_plate;
                    $result = User::create($data);
                    $userid = $result->id;

                    $data['partner_id'] = $gate->partner_id;
                    $data['checkin_gate'] = $request->gate_id;
                    $data['facility_id'] = $request->facility_id;
                    $data['user_id'] = $userid;
                    $data['is_checkin'] = 1;
                    $data['ticket_number'] = $this->checkTicketNumber();
                    $data['check_in_datetime'] = date('Y-m-d H:i:s');
                    //$data['checkout_datetime'] = $userPass->desired_end_date;
                    $data['checkin_time'] = date('Y-m-d H:i:s');
                    $data['ticket_security_code'] = rand(1000, 9999);
                    $data['vp_device_checkin'] = '1';
                    $result = Ticket::create($data);
                    $ticketnum = $result->ticket_number;
                }
            }
            if (isset($gate) && $gate->gate_type == "exit") {
                throw new ApiGenericException("You can't checkout drive-up in Enforcement app.");
            }

            $driveup['type'] = 'driveup';
            $driveup['account_number'] = $ticketnum;
            $cdate = date('Y-m-d H:i:s');
            $driveup['formatted_desired_start_date'] = date("jS F,Y H:i:s", strtotime($cdate));
            return  $driveup;
        }
    }

    protected function checkPermitTicketNumber()
    {
        $ticket = 'SC' . rand(100, 999) . rand(100, 999);
        $isExist = PermitTicket::where('ticket_number', $ticket)->first();
        if ($isExist) {
            $this->checkPermitTicketNumber();
        }
        return $ticket;
    }

    public function VehicleDetailsOnCheckin(Request $request)
    {

        if ($request->header('X-ClientSecret') != '') {

            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new NotFoundException('No partner found.');
            }

            $license_plate = $request->license_number;
            $case = 0;
            $vehicle = BlackListedVehicle::where('license_plate_number', $license_plate)->orderBy('id', 'DESC')->first();
            if (!$vehicle) // Vehicle Not found in Blacklisted
            {
                $permit_vehicle = PermitVehicle::where("license_plate_number", $license_plate)->orderBy('id', 'DESC')->first(); // check License Plate
                if ($permit_vehicle) {
                    $permit_request = PermitRequest::with('PermitVehicle')->where('id', $permit_vehicle->permit_request_id)->orderBy('id', 'DESC')->first();  // check permit or permit request.
                    $userPass = UserPass::with('user')->where("vehicle_id", $permit_vehicle->id)->orderBy('id', 'DESC')->first(); // check user pass

                    if ($permit_request)  // permit Found case
                    {
                        if (strtotime(date("Y-m-d", strtotime($permit_request->grace_end_date))) < strtotime(date("Y-m-d"))) // permit Expired
                        {
                            $vehicleData = array(
                                "license_plate" => $request->license_number,
                                "gate_id" => $request->gate_id,
                                "facility_id" => $request->facility_id,
                                "user_id" => $permit_request->user_id,
                                "id" => $permit_request->id,
                                "case" => 1,
                            );
                            $request->request->add($vehicleData);
                            return $result = $this->driveUpCheckinCheckout($request);
                        }
                        $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
                        if (!$gate) {
                            throw new ApiGenericException("The system is not currently available. Please try again later.");
                        }
                        if (isset($gate) && $gate->active == '0') {
                            throw new ApiGenericException('The system is not currently available. Please try again later.');
                        }

                        if (isset($gate) && $gate->gate_type == "entry") {
                            $permitCheckin = PermitTicket::where('permit_request_id', $permit_request->id)->where('is_checkin', '1')->where('is_checkout', '0')->orderBy('id', 'DESC')->first();
                            if ($permitCheckin) {
                                //return "Already permit checkin."; 
                                throw new NotFoundException('Already permit checkin.');
                            }
                            $data['partner_id'] = $permit_request->partner_id;
                            $data['user_id'] = $permit_request->user_id;
                            $data['checkin_gate'] = $request->gate_id;
                            $data['facility_id'] = $permit_request->facility_id;
                            $data['is_checkin'] = 1;
                            $data['ticket_number'] = $this->checkPermitTicketNumber();
                            $data['check_in_datetime'] = $permit_request->desired_start_date;
                            $data['checkout_datetime'] = $permit_request->grace_end_date;
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['ticket_security_code'] = rand(1000, 9999);
                            $data['permit_request_id'] = $permit_request->id;
                            $data['total'] = $permit_request->permit_rate;
                            $data['grand_total'] = $permit_request->permit_rate;
                            $result = PermitTicket::create($data);
                        }

                        if (isset($gate) && $gate->gate_type == "exit") {
                            $permitCheckin = PermitTicket::where('permit_request_id', $permit_request->id)->orderBy('id', 'DESC')->first();
                            //return $permitCheckin;
                            if ($permitCheckin->is_checkin == 1 && $permitCheckin->is_checkout == 1) {
                                throw new NotFoundException('Already permit checkout.');
                            }
                            if ($permitCheckin->is_checkin == 1 && $permitCheckin->is_checkout == 0) {
                                $permitCheckin->is_checkout = 1;
                                $permitCheckin->checkout_gate = $request->gate_id;
                                $permitCheckin->checkout_time = date("Y-m-d H:i:s");
                                $permitCheckin->save();
                            }
                        }

                        return $permit_request;
                    } elseif ($userPass)   // pass found case
                    {
                        $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
                        if (!$gate) {
                            throw new ApiGenericException("The system is not currently available. Please try again later.");
                        }
                        if (strtotime(date("Y-m-d", strtotime($userPass->end_date))) < strtotime(date("Y-m-d")) || $userPass->remaining_days == 0) {

                            if ($gate->gate_type == "entry") {
                                $vehicleData = array(
                                    "license_plate" => $request->license_number,
                                    "gate_id" => $request->gate_id,
                                    "facility_id" => $request->facility_id,
                                    "user_id" => $userPass->user_id,
                                    "id" => $userPass->id,
                                    "case" => 0,
                                );
                            } else {
                                $vehicleData = array(
                                    "license_plate" => $request->license_number,
                                    "gate_id" => $request->gate_id,
                                    "facility_id" => $request->facility_id,
                                    "user_id" => $userPass->user_id,
                                    "id" => $userPass->id,
                                    "case" => 2,
                                );
                            }
                            /* $vehicleData = array(
                      "license_plate" => $request->license_number, 
                      "gate_id" => $request->gate_id,
                      "facility_id" => $request->facility_id,
                      "user_id" => $userPass->user_id,
                      "id" => $userPass->id,
                      "case" => 2,
                      );
                      $request->request->add($vehicleData); */
                            $request->request->add($vehicleData);
                            return $result = $this->driveUpCheckinCheckout($request);
                        }
                        $upid = $userPass->id;
                        $userPass['type'] = 'pass';
                        $userPass['account_number'] = $userPass->pass_code;
                        $userPass['name'] = $userPass->user->name;
                        $userPass['formatted_desired_start_date'] = date("jS F,Y", strtotime($userPass->start_date));
                        $userPass['formatted_desired_end_date'] = date("jS F,Y", strtotime($userPass->end_date));

                        $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
                        //return $gate; 
                        if (!$gate) {
                            throw new ApiGenericException("The system is not currently available. Please try again later.");
                        }
                        if (isset($gate) && $gate->active == '0') {
                            throw new ApiGenericException('The system is not currently available. Please try again later.');
                        }
                        if (isset($gate) && $gate->gate_type == "entry") {
                            $passCheckin = PermitTicket::where('user_pass_id', $userPass->id)->where('is_checkin', '1')->where('is_checkout', '0')->orderBy('id', 'DESC')->first();

                            if ($passCheckin) {
                                throw new NotFoundException('Already pass checkin.');
                            }
                            $data['partner_id'] = $userPass->partner_id;
                            $data['user_id'] = $userPass->user_id;
                            $data['checkin_gate'] = $request->gate_id;
                            $data['facility_id'] = $userPass->facility_id;
                            $data['is_checkin'] = 1;
                            $data['ticket_number'] = $this->checkPermitTicketNumber();
                            $data['check_in_datetime'] = $userPass->desired_start_date;
                            $data['checkout_datetime'] = $userPass->desired_end_date;
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['ticket_security_code'] = rand(1000, 9999);
                            $data['user_pass_id'] = $upid;
                            $data['total'] = $userPass->permit_rate;
                            $data['grand_total'] = $userPass->permit_rate;
                            $result = PermitTicket::create($data);

                            $consume_days = $userPass->consume_days + 1;
                            $remaining_days = $userPass->remaining_days - 1;
                            userPass::where('vehicle_id', $permit_vehicle->id)->update(array('consume_days' => $consume_days, 'remaining_days' => $remaining_days));
                        }

                        if (isset($gate) && $gate->gate_type == "exit") {
                            $passCheckin = PermitTicket::where('user_pass_id', $userPass->id)->orderBy('id', 'DESC')->first();

                            //return $passCheckin;
                            if ($passCheckin->is_checkin == 1 && $passCheckin->is_checkout == 1) {
                                throw new NotFoundException('Already pass checkout.');
                            }
                            if ($passCheckin->is_checkin == 1 && $passCheckin->is_checkout == 0) {
                                $passCheckin->is_checkout = 1;
                                $passCheckin->checkout_gate = $request->gate_id;
                                $passCheckin->checkout_time = date("Y-m-d H:i:s");
                                $passCheckin->save();
                            }
                        }

                        return $userPass;
                    } else { // case when no permit and no pass but vehicle found in db 
                        $vehicleData = array(
                            "license_plate" => $request->license_number,
                            "gate_id" => $request->gate_id,
                            "facility_id" => $request->facility_id
                        );
                        $request->request->add($vehicleData);
                        return $result = $this->driveUpCheckinCheckout($request);
                    }
                } else {      // Vehicle or License Plate Not Found (Drive up case)      
                    $vehicleData = array(
                        "license_plate" => $request->license_number,
                        "gate_id" => $request->gate_id,
                        "facility_id" => $request->facility_id
                    );
                    $request->request->add($vehicleData);
                    return $result = $this->driveUpCheckinCheckout($request);
                }
            } else { // Vehicle found in Blacklisted

                if ($vehicle->plate_type == 1)
                    $vehicletype = 'Blacklisted';
                if ($vehicle->plate_type == 2)
                    $vehicletype = 'BOLO';
                Artisan::queue('vehicledetailscheckin:email', ['vehicletype' => $vehicletype, 'request' => $request->all(), 'vehicleinfo' => $vehicle]);
                throw new ApiGenericException("License Plate found in " . $vehicletype . ' List.');
            }
        }
    }

    //  Get Permit CheckinCheckout List
    public function getPermitCheckinCheckoutList(Request $request)
    {
        $facilities = [];
        if (Auth::user()->user_type == '4') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        } else if (Auth::user()->user_type == '12') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        } else if (Auth::user()->user_type == '1') {
            $partner_id = isset($request->partner_id) ? $request->partner_id : '';
        } else {
            $partner_id = isset($request->partner_id) ? $request->partner_id : Auth::user()->id;
        }

        $tickets = Ticket::with(['permit', 'facility', 'user', 'transaction']);
        $partner_id = isset($request->partner_id) ? $request->partner_id : Auth::user()->id;
        if ($request->search) {
            $tickets = $tickets->where('ticket_number', 'like', "%$request->search%")->orWhere('license_plate', 'like', "%$request->search%")->where('partner_id', $partner_id);
            $tickets = $tickets->orWhereHas(
                'user',
                function ($query) use ($request, $partner_id) {
                    $query
                        ->where('phone', 'like', "%{$request->search}%")
                        ->orWhere('email', 'like', "%{$request->search}%")
                        ->where('partner_id', $partner_id);
                }
            );
            $tickets = $tickets->orWhereHas(
                'permit',
                function ($query) use ($request, $partner_id) {
                    $query->where('account_number', 'like', "%{$request->search}%");
                    $query->orWhere('tracking_code', 'like', "%{$request->search}%");
                    $query->where('partner_id', $partner_id);
                }
            );
        }
        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date("Y-m-d", strtotime($request->from_date));
            $to_date = date("Y-m-d", strtotime($request->to_date));
            //$tickets = $tickets->whereDate('checkin_time', '>=', $from_date)->whereDate('checkout_time', '<=', $to_date);
            $tickets = $tickets->whereDate('checkin_time', '>=', $from_date);
            $tickets = $tickets->where(function ($query) use ($to_date) {
                $query->whereDate('checkin_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
            });
        } else {
            $tickets = $tickets->whereDate('checkin_time', '>=', $from_date)->whereDate('checkout_time', '<=', $to_date);
        }

        $tickets = $tickets->where('partner_id', $partner_id)->whereNotNull("permit_request_id");
        if ($request->is_checkin == '1') {
            $tickets = $tickets->where('is_checkin', '1');
        }
        if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
            $tickets = $tickets->where(function ($query) use ($facilities) {
                $query->whereIn('facility_id', $facilities);
            });
        }

        if ($request->is_checkout == '1') {
            $tickets = $tickets->where('is_checkout', '1');
        }
        if ($request->facility_id) {
            $tickets = $tickets->where('facility_id', $request->facility_id);
        }

        if ($request->sort != '') {
            $tickets = $tickets->orderBy($request->sort, $request->sortBy);
        } else {
            $tickets = $tickets->orderBy("id", "DESC");
        }

        $tickets = $tickets->paginate(20);
        return $tickets;
    }

    public function getPermitCheckinCheckoutDetails($id)
    {
        $ticket = Ticket::with(['permit', 'facility', 'user', 'transaction'])->where('id', $id)->first();
        if ($ticket) {
            $gates = Gate::where('facility_id', $ticket->facility_id)->get();
            if ($gates) {
                foreach ($gates as $key => $value) {
                    if ($value->gate == $ticket->checkin_gate) {
                        $ticket['checkin_gate_name'] = $value->gate_name;
                    }
                    if ($value->gate == $ticket->checkout_gate) {
                        $ticket['checkout_gate_name'] = $value->gate_name;
                    }
                }
            } else {
                $ticket['checkin_gate_name'] = '';
                $ticket['checkout_gate_name'] = '';
            }
        }
        return $ticket;
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        $this->log->error('Facility ID ' . $facility_id);
        $this->log->error('Facility Time Zome ' . $facility->timezone);
        if (($facility) && ($facility->timezone != '')) {
            $this->log->error('Facility Time Zome22 ' . $facility->timezone);
            date_default_timezone_set($facility->timezone);
            $this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
        } else {
            if ($partnerTimezone) {
                if ($partnerTimezone->timezone != '') {
                    $this->log->error('Facility Time Zome2233 ' . $facility->timezone);
                    date_default_timezone_set($partnerTimezone->timezone);
                    $this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
                }
            }
        }
    }

    #PIMS-8246 dushyant autostart

    public function setPartnerCustomTimezone($parnter_id)
    {
        $secret = OauthClient::where('partner_id', $parnter_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($partnerTimezone->timezone != '') {
                date_default_timezone_set($partnerTimezone->timezone);
                $this->currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
            }
        }
    }

    public function getDriveOffPaymenttList(Request $request)
    {
        $facilities = [];
        if (Auth::user()->user_type == '4') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        }
        if (Auth::user()->user_type == '12') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        }
        if (Auth::user()->user_type == '1') {
            $partner_id = isset($request->partner_id) ? $request->partner_id : '';
        } else {
            $partner_id = isset($request->partner_id) ? $request->partner_id : Auth::user()->id;
        }

        $tickets = Ticket::select("ticket_number", "checkout_by", "remark", "checkout_remark", "checkin_time", "checkout_time", "tickets.total as total_amount", "tickets.anet_transaction_id", "tickets.grand_total", "tickets.is_overstay",  "tickets.terminal_id", "tickets.id as ticket_id", "tickets.is_checkout", "tickets.user_id", "tickets.partner_id", "tickets.card_type as ticket_card_type", "tickets.expiry", "tickets.card_last_four", "tickets.license_plate", "tickets.facility_id", "tickets.estimated_checkout", "tickets.is_offline_payment", "tickets.paid_amount as validated_amount");
        $tickets = $tickets->whereNotNull("tickets.checkout_by");
        $tickets = $tickets->whereNotNull("tickets.checkout_time");

        if (isset($partner_id) && !empty($partner_id)) {
            $tickets = $tickets->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
        }

        if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
            $tickets = $tickets->where(function ($query) use ($facilities) {
                $query->whereIn('facility_id', $facilities);
            });
        }

        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date("Y-m-d", strtotime($request->from_date));
            $to_date = date("Y-m-d", strtotime($request->to_date));
            $tickets = $tickets->whereDate('checkout_time', '>=', $from_date);
            $tickets = $tickets->where(function ($query) use ($to_date) {
                $query->whereDate('checkout_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
            });
        }

        if ($request->facility_id != '' && $request->checkin_between != '') {
            $tickets = $tickets->Where('tickets.facility_id', $request->facility_id);
            $checkinTime = explode("-", $request->checkin_between);

            $start_time =  date("H:i:s", strtotime($checkinTime[0]));
            $end_time = date("H:i:s", strtotime($checkinTime[1]));
            $tickets = $tickets->whereTime('tickets.checkin_time', '>=', $start_time)->whereTime('tickets.checkin_time', '<=', $end_time);
        }

        if ($request->facility_id != '') {
            $tickets = $tickets->Where('tickets.facility_id', $request->facility_id);
        }

        if ($request->checkin_between != '') {
            $checkinTime = explode("-", $request->checkin_between);

            $start_time =  date("H:i:s", strtotime($checkinTime[0]));
            $end_time = date("H:i:s", strtotime($checkinTime[1]));
            $tickets = $tickets->whereTime('tickets.checkin_time', '>=', $start_time)->whereTime('tickets.checkin_time', '<=', $end_time);
        }


        if ($request->search != '') {

            $tickets = $tickets->where(function ($query) use ($request, $partner_id) {
                $query->where('tickets.card_type', "like", "%" . $request->search . "%");
                $query->orWhere('tickets.card_last_four', "like", "%" . $request->search . "%");
                $query->orWhere('tickets.ticket_number', "like", "%" . $request->search . "%");
                $query->orWhere("tickets.license_plate", 'like', "%$request->search%");
                // $query->orWhere("users.phone", 'like', "%$request->search%");
                // $query->orWhere("anet_transactions.ref_id", 'like', "%$request->search%");
                $query->Where("tickets.partner_id", $partner_id);
                // $query->Where("tickets.vp_device_checkin", '1');
            });
        } else {
            $tickets = $tickets->where('partner_id', $partner_id);
            // $tickets = $tickets->where('partner_id', $partner_id)->where('vp_device_checkin', '1');
        }


        if ($request->sort != '') {
            if ($request->sort == 'transaction_id') {
                // $tickets = $tickets->orderBy("anet_transactions.anet_trans_id", $request->sortBy);
            }
            if ($request->sort == 'card_type') {
                // $tickets = $tickets->orderBy("anet_transactions.card_type", $request->sortBy);
            }
            if ($request->sort == 'ticket_number') {
                $tickets = $tickets->orderBy("tickets.$request->sort", $request->sortBy);
            }
            if ($request->sort == 'checkin_time') {
                $tickets = $tickets->orderBy("tickets.$request->sort", $request->sortBy);
            }
            if ($request->sort == 'checkout_time') {
                $tickets = $tickets->orderBy("tickets.$request->sort", $request->sortBy);
            }
        } else {
            $tickets = $tickets->orderBy("tickets.id", "DESC");
        }


        $tickets = $tickets->paginate(20);
        // foreach ($tickets as $key => $val) {
        //   if ($val->status == null) {
        //     if ($val->is_offline_payment == '1') {
        //       $val->status = 'Offline';
        //     } else if ($val->is_offline_payment == '2' || $val->is_offline_payment == '3') {
        //       $val->status = 'Offline/Processed';
        //     } else if ($val->anet_transaction_id != '') {
        //       $val->status = 'Processed';
        //     } else if ($val->anet_transaction_id == '') {
        //       if ($val->card_last_four != '') {
        //         $val->status = 'Authorized';
        //       } else {
        //         $val->status = '';
        //       }
        //     }
        //   } else if (strtolower($val->status) == 'approval' || strtolower($val->status) == 'approved') {
        //     $val->status = 'Processed';
        //   }
        // }
        return $tickets;
    }

    //Save Transaction Data for Report
    protected function saveTransactionData()
    {
        $mainTicket = Ticket::where('ticket_number', $this->request->ticket_number)->first();

        $rate_id = isset($this->rate['id']) ? $this->rate['id'] : '109';
        $rate_description = isset($this->rate['description']) ? $this->rate['description'] : 'Daily Max';
        $rate_amount = $this->rate['price'];
        $tax_rate = $mainTicket->facility->tax_rate;
        if ($tax_rate > 0) {
            $tax_rate = number_format((($this->rate['price'] * $mainTicket->facility->tax_rate) / 100), 2);
        }

        $ticket = new TransactionData();

        $ticket->user_id = $mainTicket->user_id;
        $ticket->facility_id = $mainTicket->facility->id;
        $ticket->partner_id = $mainTicket->partner_id;
        $ticket->ticket_id = $mainTicket->id;
        $ticket->rate_id = $rate_id;
        $ticket->rate_description = $rate_description;
        $ticket->rate_amount = $this->rate['price'];
        $ticket->total = ($this->rate['price'] + $mainTicket->processing_fee + $mainTicket->tax_fee);
        $ticket->tax_fee = $tax_rate;
        $ticket->processing_fee = $mainTicket->facility->processing_fee;
        $ticket->discount_amount = $mainTicket->paid_amount;
        $ticket->grand_total = $mainTicket->grand_total;
        $ticket->save();
        return $ticket;
    }

    //Save Transaction Data for Report
    protected function saveOverstayTransactionData($ticket, $overstayTicket)
    {
        $mainTicket = Ticket::where('ticket_number', $this->request->ticket_number)->first();

        $arrival_time = $ticket->checkin_time;
        $diff_in_hours = $ticket->length;
        $isMember = 0;
        $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);

        $rate_id = isset($rate['id']) ? $rate['id'] : '';
        $rate_description = isset($rate['description']) ? $rate['description'] : '';
        $rate_amount = $this->rate['price'];

        $transaction = TransactionData::where("ticket_id", $ticket->id)->first();
        if (!$transaction) {
            return true;
        }

        $transaction->rate_id = $rate_id;
        $transaction->rate_description = $rate_description;
        $transaction->rate_amount = $this->rate['price'];
        $transaction->total = $ticket->total + $overstayTicket->total;
        $transaction->grand_total = $ticket->grand_total + $overstayTicket->grand_total;
        $transaction->save();
        return $transaction;
    }

    public  function getUserValidated(Request $request)
    {
        //dd(Auth::user()->user_type); 
        $total_user = [];
        $users =  User::whereNotNull('name');
        if (Auth::user()->user_type == self::USERTYPE_BUSINESS) {
            $partner_id = Auth::user()->created_by;
            $users =  $users->where('created_by', $partner_id)->whereIn('user_type', ['8']);
            $users =  $users->Orwhere('id', Auth::user()->id);
        } else if (Auth::user()->user_type == self::SUPERADMIN) {
            $partner_id = $request->partner_id;
            if (!$partner_id) {
                throw new ApiGenericException("Invalid Partner");
            }
            $users =  $users->where(
                function ($query) use ($partner_id) {
                    $query->where('created_by', $partner_id)->whereIn('user_type', ['10', '8', '4', '12'])
                        ->Orwhere('id', $partner_id);
                }
            );
        } else if (Auth::user()->user_type == self::SUBORDINATE) {
            $partner_id = Auth::user()->created_by;
            $auth_id = Auth::user()->id;

            $users =  $users->where(
                function ($query) use ($auth_id, $partner_id) {
                    $query->where('created_by', $partner_id)->whereIn('user_type', ['10', '8'])
                        ->Orwhere('id', Auth::user()->id);
                }
            );
        } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $partner_id = Auth::user()->created_by;
            $auth_id = Auth::user()->id;
            $users =  $users->where(
                function ($query) use ($auth_id, $partner_id) {
                    $query->where('created_by', $partner_id)->whereIn('user_type', ['10', '8', '4'])
                        ->Orwhere('id', $auth_id);
                }
            );
        } else {
            $partner_id = Auth::user()->id;
            $users =  $users->where(
                function ($query) use ($partner_id) {
                    $query->where('created_by', $partner_id)->whereIn('user_type', ['10', '8', '4'])
                        ->Orwhere('id', $partner_id);
                }
            );
        }

        if ($request->facility_id) {
            $userFacilities = DB::table('user_facilities')->where('facility_id', $request->facility_id)->whereNull('deleted_at')->pluck('user_id');
            //dd($userFacilities);
            // $users =  $users->whereIn('id', $userFacilities);

            $users->where(
                function ($query) use ($partner_id, $userFacilities) {
                    $query->whereIn('id', $userFacilities)
                        ->Orwhere('id', $partner_id);
                }
            );
        }

        if ($request->facility_id) {
            $userbusiness = DB::table('business_facility_policy')->where('facility_id', $request->facility_id)->whereNull('deleted_at')->pluck('business_id');
            //dd($userbusiness);
            $users =  $users->OrWhereIn('business_id', $userbusiness);
        }
        // dd($users->toSql());
        $users =  $users->get();
        if (!count($users)) {
            return response()->json([], 200);
        }
        $subName = "";
        foreach ($users as $key => $val) {

            if (!empty($val->name)) {
                if ($val->user_type == '1') {
                    $subName = "Superadmin";
                } elseif ($val->user_type == '3') {
                    $subName = "Partner";
                } elseif ($val->user_type == '4') {
                    $subName = "Subpartner";
                } elseif ($val->user_type == '5') {
                    $subName = "";
                } elseif ($val->user_type == '7') {
                    $subName = "Valet ";
                } elseif ($val->user_type == '8') {
                    $subName = "Business clerk";
                } elseif ($val->user_type == '9') {
                    $subName = "Attendant  ";
                } elseif ($val->user_type == '10') {
                    $subName = "Business user";
                } elseif ($val->user_type == '12') {
                    $subName = "RM";
                }
                $total_user[$key]['id'] = $val->id;
                $total_user[$key]['name'] = $val->name . ' -' . $subName;
                $total_user[$key]['email'] = $val->email;
            }
        }

        return $total_user;
    }

    public function getOpenCheckinCheckoutList(Request $request)
    {
        $facilities = [];
        $partner_id = '';
        if (Auth::user()->user_type == '4') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        } else if (Auth::user()->user_type == '12') {
            $request->request->add(['partner_id' => Auth::user()->created_by]);
            $partner_id = Auth::user()->created_by;
            $facilities = DB::table('user_facilities')->where('user_id', Auth::user()->id)->whereNull('deleted_at')->pluck('facility_id');
        } else if (Auth::user()->user_type == '1') {
            $partner_id = isset($request->partner_id) ? $request->partner_id : '';
        } else {
            $partner_id = isset($request->partner_id) ? $request->partner_id : Auth::user()->id;
        }
        $tickets = Ticket::with(['reservation', 'facility', 'user', 'userPass', 'transaction']);

        if (isset($partner_id) && !empty($partner_id)) {
            $tickets = $tickets->where(function ($query) use ($partner_id) {
                $query->where('partner_id', $partner_id);
            });
        }
        if ((Auth::user()->user_type == '4') || (Auth::user()->user_type == '12')) {
            $tickets = $tickets->where(function ($query) use ($facilities) {
                $query->whereIn('facility_id', $facilities);
            });
        }


        if ($request->search_filter == '1') {

            if (isset($request->ticket_number) && $request->ticket_number != '') {
                $tickets = $tickets->where('ticket_number', 'like', "%$request->ticket_number%");
            }
            if (isset($request->license_plate) && $request->license_plate != '') {
                $tickets = $tickets->where('license_plate', 'like', "%$request->license_plate%");
            }
            if (isset($request->phone_number) && $request->phone_number != ''  || isset($request->customer_name) && $request->customer_name != '' || isset($request->validated_by) && $request->validated_by != '') {
                $tickets = $tickets->WhereHas(
                    'user',
                    function ($query) use ($request) {
                        $query
                            ->where('phone', 'like', "%{$request->phone_number}%")
                            ->where('name', 'like', "%{$request->customer_name}%")
                            //  ->where('name', 'like', "%{$request->validated_by}%")
                            ->where('partner_id', isset($request->partner_id) ? $request->partner_id : Auth::user()->id);
                    }
                );
            }
        }


        if ($request->search) {
            $tickets = $tickets->where('ticket_number', 'like', "%$request->search%")->where('vp_device_checkin', '0');
            if ($tickets->count() == 0) {
                $tickets = $tickets->orwhere('license_plate', 'like', "%$request->search%")->where('vp_device_checkin', '0');
            }




            $tickets = $tickets->orWhereHas(
                'user',
                function ($query) use ($request) {
                    $query
                        ->where('phone', 'like', "%{$request->search}%")
                        ->orWhere('email', 'like', "%{$request->search}%")
                        ->where('partner_id', isset($request->partner_id) ? $request->partner_id : Auth::user()->id);
                }
            );

            if (isset($request->facility_id) && !empty($request->facility_id)) {
                $tickets = $tickets->where(function ($query) use ($request, $partner_id) {
                    $query->where('partner_id', $partner_id)->where('facility_id', $request->facility_id);
                });
            }

            if (isset($partner_id) && !empty($partner_id)) {
                $tickets = $tickets->where(function ($query) use ($partner_id) {
                    $query->where('partner_id', $partner_id);
                });
            }
        }
        $month = date("m");
        $from_date  = date('Y-' . $month . '-01');
        $to_date  = date('Y-' . $month . '-t');
        $mid_date  = date('Y-' . $month . '-15');
        $midDateString = strtotime($mid_date);
        $lastdate = strtotime(date("Y-m-t", $midDateString));
        $to_date = date("Y-m-d", $lastdate);

        if (isset($request->from_date) && $request->from_date != '') {
            $from_date = date("Y-m-d", strtotime($request->from_date));
            $to_date = date("Y-m-d", strtotime($request->to_date));
            $tickets = $tickets->whereDate('checkin_time', '>=', $from_date);
            $tickets = $tickets->where(function ($query) use ($to_date) {
                $query->whereDate('checkin_time', '<=', $to_date)/*->orWhereNull("checkout_time")*/;
            });
        }
        // return $tickets;
        if ($request->facility_id != '') {
            $tickets = $tickets->where('partner_id', $partner_id)->where('vp_device_checkin', '0')->where("facility_id", $request->facility_id);
        } elseif (isset($partner_id) && !empty($partner_id)) {
            $tickets = $tickets->where('partner_id', $partner_id)->where('vp_device_checkin', '0');
        }


        $tickets = $tickets->where('is_checkin', '0')->where('is_checkout', '0');

        if ($request->sort != '') {
            $tickets = $tickets->orderBy($request->sort, $request->sortBy);
        } else {
            $tickets = $tickets->orderBy("id", "DESC");
        }

        $tickets = $tickets->paginate(20);
        return $tickets;
    }

    // Heartland Payment API Call
    public function makeHeartlandPayment($request)
    {
        $amount = ($this->facility->FacilityPaymentDetails->heartland_payment_env == 'test') ? '3.00' : $request->total;

        if ($request->payment_profile_id != '') // saved cards
        {
            $cardCheck = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $request->user_id)->where('token', $request->payment_profile_id)->first();
            if (!$cardCheck) {
                throw new ApiGenericException("Payment Profile Not Found.");
            }
            $request->request->add(['expiration' => $cardCheck->expiry]);
            $request->request->add(['card_last_four' => $cardCheck->card_last_four]);

            $card_month = substr($cardCheck->expiry, 0, 2);
            $card_year = substr($cardCheck->expiry, -2);
            $request->request->add(['expiration_month' => $card_month]);
            $request->request->add(['expiration_year' => $card_year]);
            $this->log->info("Payment Profile Data --" . $cardCheck);

            //$amount = $request->total;
            $data['token'] = $cardCheck->token;
            if ($amount > 0) {
                $amount = number_format($amount, 2);
                $request->request->add(['Amount' => $amount]);
                $request->request->add(['token' => $cardCheck->token]);
                try {
                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($request, $this->facility);
                } catch (Exception $e) {
                    $this->log->info("Error in Heartland Payment with payment profile id --" . json_encode($e->getMessage()));
                    throw new ApiGenericException($e->getMessage());
                }
            }
        } else if ($request->nonce) {
            $this->log->info("Request Data Heartland: " . json_encode($request->all()));
            $this->setDecryptedCard($request);
            $card_month = substr($request->expiration_date, 0, 2);
            $card_year = substr($request->expiration_date, -2);
            $request->request->add(['expiration_month' => $card_month]);
            $request->request->add(['expiration_year' => $card_year]);
            $amount = number_format($amount, 2);
            $request->request->add(['Amount' => $amount]);
            $request->request->add(['expiration' => $request->expiration_date]);
            $card_last_four = substr($request->card_number, -4);
            $request->request->add(['card_last_four' => $card_last_four]);
            // otuPaymentTokenHeartland
            $otu_token = HeartlandPaymentGateway::otuPaymentTokenHeartland($this->request, $this->facility);
            //dd($otu_token);
            $this->log->info("Heartland OTU Token: " . json_encode($otu_token));

            if ($otu_token) {
                $request->request->add(['token' => $otu_token['token_value']]);
                try {
                    $paymentResponse = HeartlandPaymentGateway::heartlandPaymentByToken($this->request, $this->facility);
                    $this->log->info("Heartland card Payment Response: " . json_encode($paymentResponse));
                } catch (Exception $e) {
                    $this->log->info("Error in Heartland Payment with card --" . json_encode($e->getMessage()));
                    throw new ApiGenericException($e->getMessage());
                }
            } else {
                throw new ApiGenericException("Please contact to Admin");
            }
        }
        if ($paymentResponse->responseMessage == 'APPROVAL') {
            $user_id = $this->user->id;
            $paymentStatus = HeartlandPaymentGateway::saveTransaction($request, $paymentResponse, $user_id);
            $this->log->info("Payment Transaction Data make Heartland Payment -- " . json_encode($paymentStatus));
            $this->log->info("Make Heartland Payment Request Before save" . json_encode($request->all()));

            $cardCheck = HeartlandPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $request->partner_id)->where('card_last_four', $request->card_last_four)->first();

            if ($request->user_consent == 1 && !$cardCheck) {
                $response = HeartlandPaymentGateway::saveHeartlandCard($paymentResponse, $user_id, $request);
            }

            //save promocode in promousage table
            if ($this->request->promocode != '' && $this->request->discount_amount != '') {
                $this->updatePromocodeUsage($ticket, $this->user->email);
                $request->request->add(['total' => floatval($request->total + $request->discount_amount)]);
            }
            return $paymentStatus;
        } else {
            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
        }
        //return $paymentStatus;
    }

    public function updatePromocodeUsage($ticket, $email)
    {
        try {
            $promoUsage = new PromoUsage();
            $promoUsage->user_id = isset($ticket->user_id) ? $ticket->user_id : '0';
            $promoUsage->partner_id = $ticket->partner_id;
            $promoUsage->promocode = $this->request->promocode;
            $promoUsage->email = $email != '' ? $email : '';
            $promoUsage->ticket_id = $ticket->id;
            $promoUsage->discount_amount = $this->request->discount_amount;
            $promoUsage->save();
        } catch (Exception $e) {
            throw $e;
        }
    }

    public function attendantConfirmCheckout(Request $request)
    {
        $this->attendantLog->info('attendantConfirmCheckout : ' . json_encode($request->all()));
        $discountAmount =  (isset($request->discount_amount) && $request->discount_amount > 0) ? $request->discount_amount : 0;
        $ticket = Ticket::with('facility.FacilityPaymentDetails', 'overstay')->where('ticket_number', $request->ticket_number)->first();
        if (!$ticket) {
            throw new NotFoundException('Please use valid ticket number.');
        }
        if ($ticket->is_checkout == '1') {
            throw new NotFoundException('Ticket already checkout.');
        }
        $this->setCustomTimezone($ticket->facility_id);


        if ($request->phone != '') {
            $countryCode = '';

            // Get country Code
            $countryCode = QueryBuilder::appendCountryCode();

            $user = User::where("phone", $countryCode . $request->phone)->where("created_by", $ticket->partner_id)->first();
            if ($user) {
                throw new ApiGenericException('Phone already associated with other user.');
            }

            // checked condition of user for spothero 09-10-2023 by Ashutosh
            if (isset($ticket->user_id) && !empty($ticket->user_id)) {
                $ticketUser = User::where("id", $ticket->user_id)->first();
                $ticketUser->phone = $countryCode . $request->phone;
                $ticketUser->save();
            }
        }

        $this->facility = $facility = $ticket->facility;
        //check gate api
        if (isset($request->manual_checkout) && $request->manual_checkout == 1) {
            if (isset($request->direct_checkout) && $request->direct_checkout == 0) {
                if ($request->gate == '' || $request->gate == '0') {
                    throw new ApiGenericException('Please select gate.');
                }
                if ($ticket->facility->check_vehicle_enabled == 1) {
                    $gateStatus = $this->attendantOpenGate($ticket->facility_id, $request->gate, '');
                    if ($gateStatus == "true") {
                    } else {
                        throw new ApiGenericException('Either vehicle is not present or error in gate opening.');
                    }
                }
            }

            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
            $diff_in_hours = $ticket->getCheckOutCurrentTime(true);

            if ($ticket->anet_transaction_id == '' && $ticket->is_offline_payment == '0' && $ticket->paid_by != '' && $request->is_overstay != "1") {

                // annednta vikrant - 23-05-2024 change change when amount full validate and user checkout manually
                //$arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                //$from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                //$diff_in_hours = $ticket->getCheckOutCurrentTime(true);
                $processingFee = $this->getProcessingFee($ticket); // to get prcessing free channel wise need to

                $isMember = 0;
                $this->attendantLog->info("attendantLog get Diff in Hours : {$diff_in_hours}");
                $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
                $this->attendantLog->info("attendantLog get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

                if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {
                    $this->attendantLog->info("attendantLog Get Price 111");
                    $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
                } else {
                    $this->attendantLog->info("attendantLog Get Price 222");
                    $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false, false, true, false, 0, $isMember);
                }
                if ($rate == false) {
                    throw new ApiGenericException('Garage is currently closed.');
                }
                $this->rate = $rate;
                $this->attendantLog->info('attendantLog : rate  ' . $rate['price']);

                // 0 =>Amout 1 => Percentage as per admin confing.
                $tax_rate =  0;
                if ($ticket->facility->tax_rate_type == '0') {
                    if ($ticket->facility->tax_rate > 0) {
                        $tax_rate = $ticket->facility->tax_rate;
                    }
                } else {
                    if ($ticket->facility->tax_rate > 0) {
                        $tax_rate = number_format((($rate['price'] * $ticket->facility->tax_rate) / 100), 2);
                    }
                }
                $newPrice = 0;
                $ticketPrice = ($rate['price'] + $processingFee + $tax_rate);
                $this->attendantLog->info("attendantLog PRINTE HOURS {$diff_in_hours}  AND  RATE {$ticketPrice}");

                $priceBreakUp = $ticket->priceBreakUp($rate);
                $this->attendantLog->info("attendantLog priceBreakUp " . json_encode($priceBreakUp));

                /*$ticket->parking_amount  = $priceBreakUp['parking_amount'];
        $ticket->paid_amount     = $priceBreakUp['paid_amount'];
        $ticket->processing_fee  = $priceBreakUp['processing_fee'];
        $ticket->tax_fee         = $priceBreakUp['tax_rate'];
        $ticket->total           = $priceBreakUp['total'];
        $ticket->grand_total     = $priceBreakUp['payable_amount'];
        $ticket->length     = $rateDiffInHour;
        $newPrice = $priceBreakUp['payable_amount'];
        */
                $this->attendantLog->info("attendantLog manual parking_amount " . $ticket->parking_amount);
                return $priceBreakUp;
            }

            //comment because it is using on in townsend
            /*$transactionDataExist = TransactionData::where("ticket_id", $ticket->id)->first();
      if (!$transactionDataExist) {
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
        $processingFee = $this->getProcessingFee($ticket); // to get prcessing free channel wise need to

        $isMember = 0;
        $this->log->error("confirmCheckout : manual checkout rate for hours  {$diff_in_hours} and ticket {$ticket->ticket_number}");
        $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
        if ($ticket->facility->rate_duration_in_hours > 0 && $ticket->facility->rate_per_hour > 0 && $ticket->facility->rate_free_minutes > 0 && $ticket->facility->rate_daily_max_amount > 0) {
          $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
        } else {
          $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false, false, true, false, 0, $isMember);
        }

        $this->rate = $rate;
        $ticket->length = $rateDiffInHour;
        $ticket->rate_id = isset($this->rate['id']) ? $this->rate['id'] : '';
        $ticket->rate_description = isset($this->rate['description']) ? $this->rate['description'] : '';

        //save transaction data with 0 amount for grace period
        $saveTransactionData = new TransactionData();
        $saveTransactionData->user_id = $ticket->user_id;
        $saveTransactionData->facility_id = $ticket->facility->id;
        $saveTransactionData->partner_id = $ticket->partner_id;
        $saveTransactionData->ticket_id = $ticket->id;
        $saveTransactionData->rate_id = isset($this->rate['id']) ? $this->rate['id'] : '';
        $saveTransactionData->rate_description = isset($this->rate['description']) ? $this->rate['description'] : '';
        $saveTransactionData->rate_amount = $this->rate['price'];
        $saveTransactionData->total = $this->rate['price'];
        $saveTransactionData->tax_fee = "0.00";
        $saveTransactionData->processing_fee = "0.00";
        $saveTransactionData->discount_amount = "0.00";
        $saveTransactionData->grand_total = "0.00";
        $saveTransactionData->save();
      }*/

            $ticket->length = $diff_in_hours;
            $ticket->checkout_time = date("Y-m-d H:i:s");
            $ticket->is_checkout = '1';
            $ticket->checkout_datetime = $ticket->checkout_datetime == '' ? date("Y-m-d H:i:s") : $ticket->checkout_datetime;
            $ticket->checkout_by = Auth::user()->id;
            $ticket->checkout_remark = isset($request->remark) ? $request->remark : 'Attendant Remark Checkout';
            $ticket->checkout_gate = $request->gate;
            $ticket->checkout_mode = '8';
            $ticket->event_user_id = Auth::user()->id;
            $ticket->checkout_datetime = $ticket->estimated_checkout == '' ? date("Y-m-d H:i:s") : $ticket->estimated_checkout;
            if ($ticket->grand_total == '') {
                $ticket->grand_total = "0.00";
            }
            if ($ticket->total == '') {
                $ticket->total = "0.00";
            }

            $ticket->save();
            //call for capping deduct
            if ($ticket->facility->is_gated_facility == '1' && $ticket->paid_date != '' && $ticket->anet_transaction_id == '' && $ticket->is_offline_payment == '0') {
                Ticket::clerkCappingUpdate($ticket->ticket_number);
            }
            // Reservation is_ticket updated after checkout Ashutosh 29-09-2023
            if ($ticket->reservation_id != '') {
                $ticket->checkout_time = date('Y-m-d H:i:s');
                $reservation = Reservation::where('id', $ticket->reservation_id)->first();
                $reservation->is_ticket = 2;
                $reservation->save();
            }
            $ticket->msg = "Thank you for checkout.";
            $this->attendantLog->info('attendantLog manual check data');
            return $ticket;
        }

        //overstay condition
        if ($request->is_overstay == "1") {

            if ($request->direct_checkout == '1' && $request->total == "0.00" && ($discountAmount <= 0)) {
                $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
                $ticket->is_checkout = '1';
                $ticket->checkout_datetime = $ticket->getCheckOutCurrentTime();
                $ticket->checkout_time = $ticket->getCheckOutCurrentTime();
                $ticket->checkout_by = Auth::user()->id;
                $ticket->checkout_remark = isset($request->remark) ? $request->remark : 'Attendant Checkout';
                $ticket->length = $diff_in_hours;
                $this->log->error('confirmCheckout check data before save :  ');
                $ticket->checkout_gate = $request->gate;
                $ticket->checkout_mode = '8';
                $ticket->save();

                if ($ticket->is_checkout == '1') {
                    $ticket->msg = "Thank you for the checkout.";
                    if (isset($ticket->user_id) && $ticket->user_id > 0) {
                        $facilityName = ucwords($facility->full_name);
                        $user = User::find($ticket->user_id);
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                        if ($partnerDetails->user_id == self::TOWNSEND_PARTNER_ID) {
                            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                $join->on('user_facilities.user_id', '=', 'users.id');
                                $join->where('user_facilities.facility_id', "=", $facility->id);
                            })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                            $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                        } else {
                            $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        }
                        $url = env('RECEIPT_URL');
                        $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                        $this->customeReplySms($sms_msg, $user->phone);
                    }
                }
                return $ticket;
            }

            // commit due to overstay implementation for Sport herro reservation 
            // $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date); 
            $diff_in_hours = '';
            if (isset($ticket->payment_date) && !empty($ticket->payment_date)) {
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->payment_date);
                $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->payment_date);
            } else {
                $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->estimated_checkout);
                $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $ticket->estimated_checkout);
            }

            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

            $request->request->add(['length' => $diff_in_hours]);
            $isMember = 0;
            $this->log->info("get Diff in Hours : {$diff_in_hours}");
            $rateDiffInHour = $diff_in_hours;
            // $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
            $this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

            if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
                $this->log->info("getCheckinCheckoutDetails Get Price 111");
                $rate = $facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
            } else {
                $this->log->info("confirmCheckout Get Price 222");
                $rate = $facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, true, false, true, false, 0, $isMember);
            }
            $processingFee = 0.00;
            $tax_rate = 0.00;
            $parkingAmount = $rate['price'];
            $overstayTicket = [];
            $currentTime = Carbon::parse('now');
            $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');

            $planetTransactionId = 0;
            if (isset($request->only_payment) && $request->only_payment > 0) {

                $overstayDiscountAmount = 0;
                if ($request->discount_amount != '') {
                    $overstayDiscountAmount = $request->discount_amount;
                }
                if ($request->nonce != '') {

                    $this->setDecryptedCard($request);

                    if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                        throw new NotFoundException('Payment details not defined against this garage.');
                    }

                    if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                        $refundstatus = PlanetPaymentGateway::planetPaymentByCrediCard($ticket, $request->total, $request);
                        $this->log->info("Payment Response :" . json_encode($refundstatus));
                        if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                            throw new NotFoundException("Payment failed. Please try again.");
                        }
                        $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                        $planetTransactionId = $planetTransaction->id;
                    } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {

                        $card_month = substr($request->expiration_date, 0, 2);
                        $card_year = substr($request->expiration_date, -2);
                        $request->request->add(['expiration_month' => $card_month]);
                        $request->request->add(['expiration_year' => $card_year]);

                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        //datacap otu token
                        $datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($request, $ticket->facility);

                        if ($datacapPaymentToken["Token"]) {
                            $amount = number_format($request->total, 2);
                            $datacap['Amount'] = $amount;
                            $datacap['Token'] = $datacapPaymentToken["Token"];
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            if ($paymentResponse["Status"] == "Error") {
                                $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }
                            } else if ($paymentResponse["Status"] == "Approved") {
                                $card_last_four = substr($paymentResponse['Account'], -4);
                                $request->request->add(['expiration' => $request->expiration_date]);
                                $request->request->add(['card_last_four' => $card_last_four]);
                                $user_id = $ticket->user_id;

                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $planetTransactionId = $planetTransaction->id;
                            } else {
                                throw new ApiGenericException("Error in Making Payment.");
                            }
                        } else {
                            throw new ApiGenericException("Payment Token Not Generated.");
                        }
                    } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                        $this->log->info("Make Heartland Payment new  ");
                        $paymentStatus = $this->makeHeartlandPayment($request);
                        $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                        $planetTransactionId = $paymentStatus->id;
                    }


                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->anet_transaction_id = $planetTransactionId;
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->is_offline_payment = '2';
                    $overstayTicket->tax_fee   = $request->tax_fee;
                    $overstayTicket->save();
                } elseif ($request->session_id != '') {

                    if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                        throw new NotFoundException('Payment details not defined against this garage.');
                    }

                    if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {

                        if ($ticket->payment_gateway == "Datacap" || $ticket->payment_gateway == "datacap") {
                            $ecommerce_mid = $ticket->facility->ecommerce_mid;
                            $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                            $amount = number_format($request->total, 2);
                            $datacap['Amount'] = $amount;
                            $datacap['Token'] = $ticket->payment_token;
                            $datacap['ecommerce_mid'] = $ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            $this->attendantLog->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Status"] == "Error") {

                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }
                            } else if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;

                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $planetTransactionId = $planetTransaction->id;
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        } else {
                            $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                            $this->log->info("Payment Response :" . json_encode($refundstatus));
                            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                throw new NotFoundException("Payment failed. Please try again.");
                            }
                            $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                            $planetTransactionId = $planetTransaction->id;
                        }
                    } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        $amount = number_format($request->total, 2);
                        $datacap['Amount'] = $amount;
                        $datacap['Token'] = $ticket->payment_token;
                        $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        } else if ($paymentResponse['Status'] == 'Approved') {
                            $user_id = $ticket->user_id;

                            $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                            $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                            $planetTransactionId = $planetTransaction->id;
                        } else {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        }
                    } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                        $request->request->add(['payment_profile_id' => $request->session_id]);
                        $this->log->info("Make Heartland Payment new  ");
                        $paymentStatus = $this->makeHeartlandPayment($request);
                        $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                        $planetTransactionId = $paymentStatus->id;
                    }

                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->anet_transaction_id = $planetTransactionId;
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->is_offline_payment = '3';
                    $overstayTicket->tax_fee   = $request->tax_fee;
                    $overstayTicket->save();
                } else {
                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->is_offline_payment = '1';
                    $overstayTicket->tax_fee   = $request->tax_fee;
                    $overstayTicket->save();
                }

                $currentTime = Carbon::parse('now');
                $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');

                $overstayTicket->estimated_checkout = $estimated_checkout;
                $overstayTicket->payment_date = date("Y-m-d H:i:s");
                $overstayTicket->save();
                $ticket->is_checkout = '0';

                //$ticket->estimated_checkout = date('Y-m-d H:i:s', strtotime($estimated_checkout));
                //$ticket->length = $ticket->getCheckOutCurrentTime(true);
                //$ticket->processing_fee = $ticket->processing_fee + $processingFee;
                //$ticket->tax_fee = $ticket->tax_fee + $tax_rate;
                //$ticket->grand_total = $ticket->grand_total + $request->total;
                //$ticket->total = $ticket->total + $request->total + $overstayDiscountAmount;
                //$ticket->grand_total = $ticket->grand_total;
                //$ticket->total = $ticket->total;
                //$ticket->admin_payment_comment = isset($request->remark) ? $request->remark : 'Admin Remark Only Pay';
                //$ticket->parking_amount = $ticket->parking_amount + $parkingAmount;
                $ticket->is_overstay = '1';
                //$ticket->estimated_checkout = $estimated_checkout;
                //$ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->save();

                $this->saveOverstayTransactionData($ticket, $overstayTicket);
                $ticket->msg = "Thank you for the payment.";
                return $ticket;
            } else {
                $this->attendantLog->error('pay & checkout');
                $overstayDiscountAmount = 0;
                if ($request->discount_amount != '') {
                    $overstayDiscountAmount = $request->discount_amount;
                }

                if ($request->nonce) {
                    $this->setDecryptedCard($request);

                    if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                        throw new NotFoundException('Payment details not defined against this garage.');
                    }

                    if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                        $refundstatus = PlanetPaymentGateway::planetPaymentByCrediCard($ticket, $request->total, $request);
                        $this->log->info("Payment Response :" . json_encode($refundstatus));
                        if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                            throw new NotFoundException("Payment failed. Please try again.");
                        }
                        $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                        $planetTransactionId = $planetTransaction->id;
                    } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {

                        $card_month = substr($request->expiration_date, 0, 2);
                        $card_year = substr($request->expiration_date, -2);
                        $request->request->add(['expiration_month' => $card_month]);
                        $request->request->add(['expiration_year' => $card_year]);

                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        //datacap otu token
                        $datacapPaymentToken = DatacapPaymentGateway::otuPaymentTokenDataCap($request, $ticket->facility);

                        if ($datacapPaymentToken["Token"]) {
                            $amount = number_format($request->total, 2);
                            $datacap['Amount'] = $amount;
                            $datacap['Token'] = $datacapPaymentToken["Token"];
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            if ($paymentResponse["Status"] == "Error") {
                                $this->log->info("Payment Error Data --" . json_encode($paymentResponse));
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }
                            } else if ($paymentResponse["Status"] == "Approved") {
                                $card_last_four = substr($paymentResponse['Account'], -4);
                                $request->request->add(['expiration' => $request->expiration_date]);
                                $request->request->add(['card_last_four' => $card_last_four]);
                            } else {
                                throw new ApiGenericException("Error in Making Payment.");
                            }
                        } else {
                            throw new ApiGenericException("Payment Token Not Generated.");
                        }

                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        $amount = number_format($request->total, 2);
                        $datacap['Amount'] = $amount;
                        $datacap['Token'] = $ticket->session_id;
                        $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }

                            if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;

                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $planetTransactionId = $planetTransaction->id;
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        }
                    } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                        $this->log->info("Make Heartland Payment new  ");
                        $paymentStatus = $this->makeHeartlandPayment($request);
                        $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                        $planetTransactionId = $paymentStatus->id;
                    }
                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->anet_transaction_id = $planetTransactionId;
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->payment_date = date("Y-m-d H:i:s");
                    //$overstayTicket->processing_fee = $processingFee;
                    $overstayTicket->tax_fee = $request->tax_fee;
                    $overstayTicket->save();
                    $ticket->is_offline_payment = '2';
                } elseif ($request->session_id != '') {

                    if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                        throw new NotFoundException('Payment details not defined against this garage.');
                    }

                    if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {

                        if ($ticket->payment_gateway == "Datacap" || $ticket->payment_gateway == "datacap") {
                            $ecommerce_mid = $ticket->facility->ecommerce_mid;
                            $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                            $amount = number_format($request->total, 2);
                            $datacap['Amount'] = $amount;
                            $datacap['Token'] = $ticket->payment_token;
                            $datacap['ecommerce_mid'] = $ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            $this->attendantLog->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Status"] == "Error") {

                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }
                            } else if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;

                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $planetTransactionId = $planetTransaction->id;
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        } else {
                            $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                            $this->log->info("Payment Response :" . json_encode($refundstatus));
                            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                throw new NotFoundException("Payment failed. Please try again.");
                            }
                            $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                            $planetTransactionId = $planetTransaction->id;
                        }
                    } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        $amount = number_format($request->total, 2);
                        $datacap['Amount'] = $amount;
                        $datacap['Token'] = $ticket->payment_token;
                        $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }

                            if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;

                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $planetTransactionId = $planetTransaction->id;
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }
                        }
                    } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                        $request->request->add(['payment_profile_id' => $request->session_id]);
                        $this->log->info("Make Heartland Payment new  ");
                        $paymentStatus = $this->makeHeartlandPayment($request);
                        $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                        $planetTransactionId = $paymentStatus->id;
                    }

                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->anet_transaction_id = $planetTransactionId;
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->payment_date = date("Y-m-d H:i:s");
                    //$overstayTicket->processing_fee = $processingFee;
                    //$overstayTicket->tax_fee = $tax_rate;
                    $overstayTicket->save();
                    $ticket->is_offline_payment = '3';
                } else {
                    $this->log->error('offline ');
                    $ticket->is_offline_payment = '1';
                    $overstayTicket = $this->saveOverstayTicketDetails($ticket, $request);
                    $overstayTicket->checkout_datetime = $estimated_checkout;
                    $overstayTicket->payment_date = date("Y-m-d H:i:s");
                    //$overstayTicket->anet_transaction_id = $planetTransaction->id;
                    //$overstayTicket->processing_fee = $processingFee;
                    //$overstayTicket->tax_fee = $tax_rate;
                    $overstayTicket->save();
                }


                // $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
                $ticket->is_checkout = '1';
                $ticket->checkout_datetime = $ticket->getCheckOutCurrentTime();
                $ticket->checkout_time = $ticket->getCheckOutCurrentTime();
                $ticket->checkout_by = Auth::user()->id;
                $ticket->checkout_remark = isset($request->remark) ? $request->remark : 'Admin Remark Checkout';
                //$ticket->length = $diff_in_hours;

                $ticket->checkout_gate = $request->gate;
                $currentTime = Carbon::parse('now');
                $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:s:i');
                //$ticket->estimated_checkout = date('Y-m-d H:s:i', strtotime($estimated_checkout));
                //$ticket->length = $ticket->getCheckOutCurrentTime(true);
                //$ticket->processing_fee = $ticket->processing_fee + $processingFee;
                //$ticket->tax_fee = $ticket->tax_fee + $tax_rate;
                //$ticket->grand_total = $ticket->grand_total + $request->total;
                //$ticket->total = $ticket->total + $request->total + $overstayDiscountAmount;
                //$ticket->parking_amount = $ticket->parking_amount + $parkingAmount;
                //$ticket->estimated_checkout = $estimated_checkout;
                //$ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->is_overstay = '1';
                $ticket->save();
                // $overstayTicket->length = $diff_in_hours;
                $overstayTicket->is_checkout = '1';
                $overstayTicket->checkout_datetime = $ticket->getCheckOutCurrentTime();
                $overstayTicket->rate_id = isset($rate['id']) ? $rate['id'] : '';
                $overstayTicket->rate_description = isset($rate['description']) ? $rate['description'] : '';
                $overstayTicket->save();



                $this->log->error('overstay confirmCheckout check data before save all checkout overstay :  ');

                $this->saveOverstayTransactionData($ticket, $overstayTicket);


                if ($ticket->is_checkout == '1') {
                    $ticket->checkout_mode = '8';
                    $ticket->save();
                    if (isset($request->direct_checkout) && $request->direct_checkout == 0) {
                        if ($request->gate == '' || $request->gate == '0') {
                            throw new ApiGenericException('Please select gate.');
                        }
                        $this->log->error('confirmCheckout checkout');
                        if ($ticket->facility->check_vehicle_enabled == 1) {
                            $gateStatus = $this->isParkEngageGateAvailable($ticket->facility_id, $request->gate, '');
                            if ($gateStatus == "true") {
                            } else {
                                throw new ApiGenericException('Currently Gate is not available.');
                            }
                        }
                    }
                    $ticket->msg = "Thank you for the checkout.";
                    if (isset($ticket->user_id) && $ticket->user_id > 0) {
                        $facilityName = ucwords($facility->full_name);
                        $user = User::find($ticket->user_id);
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                        if ($partnerDetails->user_id == self::TOWNSEND_PARTNER_ID) {
                            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                $join->on('user_facilities.user_id', '=', 'users.id');
                                $join->where('user_facilities.facility_id', "=", $facility->id);
                            })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                            $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                        } else {
                            $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        }
                        $url = env('RECEIPT_URL');
                        $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                        $this->customeReplySms($sms_msg, $user->phone);
                    }
                }

                return $ticket;
            }
        }

        // vijay - 26-06-2023 change for only payment 0 => without only payment 1 => only payment
        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
        $diff_in_hours  = $ticket->getCheckOutCurrentTime(true);


        $isMember = 0;
        $this->attendantLog->error("confirmCheckout : rate for hours  {$diff_in_hours} ");
        // $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
        $rateDiffInHour = $diff_in_hours;
        $this->attendantLog->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

        if ($ticket->facility->is_hourly_rate == '1' || $ticket->facility->is_hourly_rate == 1) {
            $this->attendantLog->info("getCheckinCheckoutDetails Get Price 111");
            $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
        } else {
            $this->attendantLog->info("confirmCheckout Get Price 222");
            $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false, false, true, false, 0, $isMember);
        }
        if ($rate == false) {
            throw new ApiGenericException('Garage is currently closed.');
        }

        $this->rate = $rate;
        $this->attendantLog->error('confirmCheckout : rate  ' . $rate['price']);

        $processingFee  = $ticket->facility->getProcessingFee('0'); // to get prcessing free channel wise need to
        $tax_rate         = $ticket->facility->getTaxRate($rate); // to get prcessing free channel wise need to

        // 0 =>Amout 1 => Percentage as per admin confing.
        // $tax_rate =  0;
        // if ($ticket->facility->tax_rate_type == '0') {
        //   if ($ticket->facility->tax_rate > 0) {
        //     $tax_rate = $ticket->facility->tax_rate;
        //   }
        // } else {
        //   if ($ticket->facility->tax_rate > 0) {
        //     $tax_rate = number_format((($rate['price'] * $ticket->facility->tax_rate) / 100), 2);
        //   }
        // }
        // to get tax and prcessing free Service wise two params are required
        $tax_rate = $ticket->facility->getTaxRate($rate, '0');
        $processingFee = $ticket->facility->getProcessingFee($rate, '0');
        $newPrice = 0;
        $ticketPrice = ($rate['price'] + $processingFee + $tax_rate);

        $this->attendantLog->info("PRINTE HOURS {$diff_in_hours}  AND  RATE {$ticketPrice}");

        if ($ticket->paid_type == '0') {
            if ($rate['price'] > 0) {
                $ticket->total = ($rate['price'] + $processingFee + $tax_rate);
                $ticket->parking_amount = $rate['price'];
                $ticket->paid_amount =  $ticketPrice;
            } else {
                $ticket->total = '0.0';
                $ticket->parking_amount = '0.0';
                $ticket->paid_amount = '0.0';;
            }
            $ticket->grand_total = '0.0';
            $newPrice = '0.00';

            $this->log->info('confirmCheckout : ticket validate as full amount  ' . $newPrice);
        } else if ($ticket->paid_type == '1') { // Hours  
            $paidDiffInHours = $ticket->getDiffInHour($ticket->paid_hour);

            $diff_in_hours = $diff_in_hours - $paidDiffInHours;
            $this->log->error("confirmCheckout : rate for hours  {$diff_in_hours} ");
            $rateDiffInHour = QueryBuilder::getFormatedLengthForPrice($diff_in_hours);
            $this->log->info("get Diff in Hours in new format rateDiffInHour : {$rateDiffInHour}");

            if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
                $this->log->info("getCheckinCheckoutDetails Get Price 111");
                $hoursRate = $facility->rateForReservationByPassRateEngine($arrival_time, $rateDiffInHour, false, false, null, false, false, '0', $isMember);
            } else {
                $this->log->info("confirmCheckout Get Price 222");
                $hoursRate = $facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  false, true, false, 0, 0);
            }
            $this->log->info("Base Rate : {$rate['price']} New Rate : {$hoursRate['price']}");
            $discountPrice = $rate['price'] - $hoursRate['price'];
            if ($hoursRate['price'] > 0) {
                $ticket->paid_amount = $discountPrice + $processingFee + $tax_rate;
                $ticket->total = ($rate['price'] + $processingFee + $tax_rate);
                $ticket->grand_total = $ticket->grand_total > 0 ? $ticket->grand_total : $request->total;
                $ticket->parking_amount = $rate['price'];
                $ticket->length = $diff_in_hours;
                $newPrice = ($hoursRate['price'] + $processingFee + $tax_rate);
            } else {
                $ticket->paid_amount = $discountPrice;
                $ticket->total = ($rate['price'] + $processingFee + $tax_rate);
                $ticket->grand_total = "0.00";
                $ticket->parking_amount = $rate['price'];
                $ticket->length = $diff_in_hours;
                $newPrice = "0.00";
            }
            $this->log->info('confirmCheckout : ticket validate as hours ' . ' Validate Hours ' . $ticket->paid_hour . ' New Price ' . $newPrice);
        } else if ($ticket->paid_type == '2') { // Amount
            $ticket->total = $ticketPrice;
            $ticket->parking_amount = $rate['price'];
            // dd($ticketPrice, $ticket->grand_total, $ticket->paid_amount);
            if ($ticket->grand_total > 0) {
                $newPrice = ($ticketPrice) - ($ticket->grand_total + $ticket->paid_amount);
            } else {
                $newPrice = (($ticketPrice) - $ticket->paid_amount);
            }

            if ($ticket->grand_total > 0 && $request->total > 0) {
                $ticket->grand_total = number_format($ticket->grand_total + $request->total, 2);
            } else {
                $ticket->grand_total = $ticket->grand_total > 0 ? $ticket->grand_total : $request->total;
            }
            $this->log->info('confirmCheckout : ticket validate as Amount ' . ' Validate Amount ' . $ticket->paid_amount . ' New Price' . $newPrice);
        } else if ($ticket->paid_type == '3') { // Percentage
            $totalAmount = $rate['price'] + $facility->processing_fee + $tax_rate;

            $percentageAmount = number_format((($totalAmount * $ticket->paid_percentage) / 100), 2);
            $payableAmount = 0;
            if (($ticket->max_validated_amount > 0) && $ticket->max_validated_amount != '') {
                if ($percentageAmount <= $ticket->max_validated_amount) {
                    $payableAmount = number_format($totalAmount - $percentageAmount, 2);
                    $updatedPaidAmount = $percentageAmount;
                } else {
                    $payableAmount = number_format($totalAmount - $ticket->max_validated_amount, 2);
                    $updatedPaidAmount = $ticket->max_validated_amount;
                }
            } else {
                $payableAmount = number_format($totalAmount - $percentageAmount, 2);
                $updatedPaidAmount = $percentageAmount;
            }
            $ticket->total = ($rate['price'] + $processingFee + $tax_rate);
            if ($ticket->paid_percentage == '100') { // if 100 % then grand total is zero 
                if ($ticket->max_validated_amount > 0) {
                    $ticket->grand_total =  $payableAmount;
                    $newPrice = $payableAmount;
                } else {
                    $ticket->grand_total =  '0.0';
                    $newPrice = '0.00';
                }
            } else {
                $ticket->grand_total =  $ticket->grand_total > 0 ? $ticket->grand_total : $request->total;
                $newPrice = $payableAmount;
            }
            $ticket->parking_amount = $rate['price'];
            $ticket->paid_amount =  $updatedPaidAmount;

            $this->log->info('confirmCheckout : ticket validate as Percentage ' . ' Validate Percentage ' . $ticket->paid_percentage . ' New Price' . $newPrice);
        } else {
            if ($rate['price'] > 0) {
                if ($ticket->is_offline_payment == '0') {
                    $ticket->total = ($rate['price'] + $processingFee + $tax_rate);

                    $ticket->grand_total = ($rate['price'] + $processingFee + $tax_rate) - $discountAmount;
                    $ticket->parking_amount = $rate['price'];
                }

                $newPrice = ($rate['price'] + $processingFee + $tax_rate);
            } else {
                $ticket->total = '0.0';
                $ticket->grand_total = '0.0';
                $ticket->parking_amount = '0.0';
                $newPrice = '0.00';
            }
            $this->log->info("ADMIN CHECKOUT ELSE SECTION RATE : {$request->total}");
            $this->log->info("ADMIN CHECKOUT ELSE SECTION RATE111 : {$newPrice}");
        }
        // dd($diff_in_hours, $ticket->total, $ticket->grand_total, $newPrice);
        $this->attendantLog->info('confirmCheckout : before return ');
        // $this->log->info(json_encode($ticket));
        $ticket->processing_fee = $rate['price'] > 0 ? $processingFee : '0.0';
        $ticket->tax_fee = $tax_rate;
        $newTotal = $request->total;
        if ($request->discount_amount != '' || $request->discount_amount > 0) {
            $newTotal = $request->total + $discountAmount;
        }

        if (isset($request->payment_details)) {
        } else {
            if (filter_var($newPrice, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $newTotal) {
                throw new ApiGenericException(
                    'Sent rate does not match database rate, please refresh and try again.',
                    422,
                    ['sent_rate' => $request->total, 'database_rate' => $newPrice, 'isratemismatch' => 1]
                );
            }
        }

        if (isset($request->only_payment) && $request->only_payment > 0) {
            $this->attendantLog->info('only_payment case');
            $planetTransaction = [];
            if ($request->session_id != '') {

                if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                    throw new NotFoundException('Payment details not defined against this garage.');
                }
                if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {

                    if ($ticket->payment_gateway == "Datacap" || $ticket->payment_gateway == "datacap") {
                        if ($ticket->session_id == $request->session_id) {
                            //check if ticket session and request session is same for planet
                            $ecommerce_mid = $ticket->facility->ecommerce_mid;
                            $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                            $amount = number_format($request->total, 2);
                            $datacap['Amount'] = $amount;
                            $datacap['Token'] = $ticket->payment_token;
                            $datacap['ecommerce_mid'] = $ticket->facility->ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            $this->attendantLog->info("planet datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Status"] == "Error") {
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }
                            }
                            if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;
                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->attendantLog->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));

                                $planetTransaction->payment_last_four = $ticket->card_last_four;
                                $planetTransaction->expiration = $ticket->expiry;
                                $planetTransaction->card_type = $ticket->card_type;
                                $planetTransaction->save();

                                $ticket->anet_transaction_id = $planetTransaction->id;
                                $ticket->checkout_card_last_four = $ticket->card_last_four;
                                $ticket->checkout_expiry = $ticket->expiry;
                                $ticket->checkout_card_type = $ticket->card_type;
                                $ticket->checkout_session_id = $ticket->session_id;
                                $ticket->checkout_payment_token = $ticket->payment_token;
                            }
                        } else {
                            $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                            $this->attendantLog->info("Payment Response :" . json_encode($refundstatus));
                            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                throw new NotFoundException("Payment failed. Please try again.");
                            }
                            $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                            $ticket->anet_transaction_id = $planetTransaction->id;
                            $ticket->card_last_four = $refundstatus["Response"]["Params"]["CardNumberLast4"];
                            $ticket->card_type = $refundstatus["Response"]["Params"]["CardSchemeName"];
                            $ticket->expiry = $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"];
                        }
                    } else {
                        $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                        $this->attendantLog->info("Payment Response :" . json_encode($refundstatus));
                        if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                            throw new NotFoundException("Payment failed. Please try again.");
                        }
                        $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                        $ticket->anet_transaction_id = $planetTransaction->id;
                        $ticket->card_last_four = $refundstatus["Response"]["Params"]["CardNumberLast4"];
                        $ticket->card_type = $refundstatus["Response"]["Params"]["CardSchemeName"];
                        $ticket->expiry = $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"];
                    }
                } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                    $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                    $amount = number_format($request->total, 2);
                    $datacap['Amount'] = $amount;
                    $datacap['Token'] = $request->session_id;
                    $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $datacap["CardHolderID"] = "Allow_V2";
                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                    if ($paymentResponse["Status"] == "Error") {
                        $this->attendantLog->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        } else {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        }

                        if ($paymentResponse['Status'] == 'Approved') {
                            $user_id = $ticket->user_id;

                            $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                            $this->attendantLog->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                            $ticket->anet_transaction_id = $planetTransaction->id;
                        } else {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        }
                    }
                } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                    $request->request->add(['payment_profile_id' => $request->session_id]);
                    $this->attendantLog->info("Make Heartland Payment new  ");
                    $paymentStatus = $this->makeHeartlandPayment($request);
                    $this->attendantLog->info("Heartland Payment Response: " . json_encode($paymentStatus));
                    $ticket->anet_transaction_id = $paymentStatus->id;
                    $ticket->card_last_four = $paymentStatus->payment_last_four;
                    $ticket->card_type = $paymentStatus->card_type;
                    $ticket->expiry = $paymentStatus->expiration;
                }


                $ticket->discount_amount = $request->discount_amount;
                $ticket->is_offline_payment = '3';
            } elseif (isset($request->payment_details)) {
                $this->attendantLog->info("payment details request --" . json_encode($request->payment_details));
                if ($request->payment_details['CardHolderID'] != '') {
                    $terminalID = isset($request->payment_details['TerminalID']) ? $request->payment_details['TerminalID'] : '';
                    $authorized_anet_transaction = new AuthorizeNetTransaction();
                    $authorized_anet_transaction->sent = '1';
                    $authorized_anet_transaction->user_id = isset($ticket->user->id) ? $ticket->user->id : "NULL";
                    $authorized_anet_transaction->ip_address = \Request::ip();
                    $authorized_anet_transaction->payment_profile_id = $request->payment_details['CardHolderID'];
                    $authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
                    $authorized_anet_transaction->name = $request->payment_details['CardHolderName'];
                    $authorized_anet_transaction->description = "VP6800 Drive-Up Payment Done Ticket ID : " . $ticket->id;
                    $authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
                    $authorized_anet_transaction->expiration = $request->payment_details['expiry'];
                    $authorized_anet_transaction->card_type = isset($request->payment_details['CardType']) ? $request->payment_details['CardType'] : 'NA';
                    $authorized_anet_transaction->ref_id = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
                    $authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
                    $authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
                    $authorized_anet_transaction->method = "card";
                    $authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
                    $authorized_anet_transaction->save();

                    $ticket->anet_transaction_id = $authorized_anet_transaction->id;
                    $ticket->terminal_id = $terminalID;
                    $ticket->checkout_session_id = $request->payment_details['CardHolderID'];
                    $ticket->checkout_payment_token = $request->payment_details['token'];

                    $ticket->checkout_card_last_four = substr($request->payment_details['MaskedPAN'], -4);
                    $ticket->checkout_expiry = $request->payment_details['expiry'];
                    $ticket->checkout_card_type = isset($request->payment_details['CardType']) ? $request->payment_details['CardType'] : 'NA';
                }
                $ticket->discount_amount = $request->discount_amount;
            } else {
                $ticket->discount_amount = $request->discount_amount;
                $ticket->is_offline_payment = '1';
            }

            $currentTime = Carbon::parse('now');
            $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:s:i');
            $ticket->is_checkout = '0';

            $ticket->estimated_checkout = date('Y-m-d H:s:i', strtotime($estimated_checkout));
            $ticket->length = $ticket->getCheckOutCurrentTime(true);
            $processingFee = 0;

            if ($ticket->vp_device_checkin  == '1' || $ticket->vp_device_checkin  == 1) {
                $ticket->processing_fee =  $ticket->facility->getProcessingFee($rate, '0');;
            } else {
                $ticket->processing_fee = $ticket->facility->getProcessingFee($rate, '1');;
            }
            // $ticket->processing_fee = $processingFee;

            $ticket->tax_fee = $tax_rate;
            $ticket->admin_payment_comment = isset($request->remark) ? $request->remark : 'Admin Remark Only Pay';
            $ticket->grand_total = $request->total;

            $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
            $ticket->rate_id = isset($this->rate['id']) ? $this->rate['id'] : '';
            $ticket->rate_description = isset($this->rate['description']) ? $this->rate['description'] : '';

            $this->attendantLog->error('confirmCheckout check data before save :  ');

            //}
            $ticket->payment_date = date("Y-m-d H:i:s");
            $ticket->payment_by = Auth::user()->id;

            //$ticket->msg = "Thank you for the payment.";

            $ticket->save();
            // process report data for now specific to twonsend ; 
            /*$transactionData = TransactionData::where("ticket_id", $ticket->id)->first();
      if (!$transactionData) {
        $this->saveTransactionData();
      }*/
        } else {
            $onlyCheckout = 0;
            if ($request->session_id != '') {

                if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                    throw new NotFoundException('Payment details not defined against this garage.');
                }

                if ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '1') {
                    //added code for checkin DC device and payment need to cut by mid from faciluty table
                    if ($ticket->payment_gateway == "Datacap" || $ticket->payment_gateway == "datacap") {
                        if ($ticket->session_id == $request->session_id) {
                            $ecommerce_mid = $ticket->facility->ecommerce_mid;
                            $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                            $amount = number_format($request->total, 2);
                            $datacap['Amount'] = $amount;
                            $datacap['Token'] = $ticket->payment_token;
                            $datacap['ecommerce_mid'] = $ticket->facility->ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            if ($paymentResponse["Status"] == "Error") {
                                $this->attendantLog->info("1 planet datacap Payment Error Data --" . json_encode($paymentResponse));
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                } else {
                                    throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                                }
                            } elseif ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;

                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                //$this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $ticket->anet_transaction_id = $planetTransaction->id;
                            } else {
                                throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                            }

                            $planetTransaction->payment_last_four = $ticket->card_last_four;
                            $planetTransaction->expiration = $ticket->expiry;
                            $planetTransaction->card_type = $ticket->card_type;
                            $planetTransaction->save();

                            $ticket->anet_transaction_id = $planetTransaction->id;
                            $ticket->checkout_card_last_four = $ticket->card_last_four;
                            $ticket->checkout_expiry = $ticket->expiry;
                            $ticket->checkout_card_type = $ticket->card_type;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_payment_token = $ticket->payment_token;
                        } else {
                            $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                            $this->attendantLog->info("Payment Response :" . json_encode($refundstatus));
                            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                throw new NotFoundException("Payment failed. Please try again.");
                            }
                            $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                            $ticket->anet_transaction_id = $planetTransaction->id;
                        }
                    } else {
                        $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $request->total, $request);
                        $this->attendantLog->info("Payment Response :" . json_encode($refundstatus));
                        if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                            throw new NotFoundException("Payment failed. Please try again.");
                        }
                        $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, $ticket->user_id);
                        $ticket->anet_transaction_id = $planetTransaction->id;
                    }
                } elseif ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '2') {
                    $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                    $amount = number_format($request->total, 2);
                    $datacap['Amount'] = $amount;
                    $datacap['Token'] = $request->session_id;
                    $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                    $datacap["CardHolderID"] = "Allow_V2";
                    $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                    if ($paymentResponse["Status"] == "Error") {
                        $this->attendantLog->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                        if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        } else {
                            throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                        }
                    } elseif ($paymentResponse['Status'] == 'Approved') {
                        $user_id = $ticket->user_id;

                        $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                        //$this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                        $ticket->anet_transaction_id = $planetTransaction->id;
                    } else {
                        throw new ApiGenericException("Invalid payment information. Please verify and try again or use another card.");
                    }
                } else if (isset($ticket->facility->FacilityPaymentDetails) && ($ticket->facility->FacilityPaymentDetails->facility_payment_type_id == '4')) {
                    $request->request->add(['payment_profile_id' => $request->session_id]);
                    $this->log->info("Make Heartland Payment new  ");
                    $paymentStatus = $this->makeHeartlandPayment($request);
                    $this->log->info("Heartland Payment Response: " . json_encode($paymentStatus));
                    $ticket->anet_transaction_id = $paymentStatus->id;
                    $ticket->card_last_four = $paymentStatus->payment_last_four;
                    $ticket->card_type = $paymentStatus->card_type;
                    $ticket->expiry = $paymentStatus->expiration;
                }

                $ticket->discount_amount = $request->discount_amount;
                $ticket->is_offline_payment = '3';
                $ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->payment_by = Auth::user()->id;
            } elseif (isset($request->payment_details)) {
                $this->attendantLog->info("payment details request --" . json_encode($request->payment_details));

                if ($request->payment_details['CardHolderID'] != '') {
                    $terminalID = isset($request->payment_details['TerminalID']) ? $request->payment_details['TerminalID'] : '';
                    $authorized_anet_transaction = new AuthorizeNetTransaction();
                    $authorized_anet_transaction->sent = '1';
                    $authorized_anet_transaction->user_id = $ticket->user->id;
                    $authorized_anet_transaction->ip_address = \Request::ip();
                    $authorized_anet_transaction->payment_profile_id = $request->payment_details['CardHolderID'];
                    $authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
                    $authorized_anet_transaction->name = $request->payment_details['CardHolderName'];
                    $authorized_anet_transaction->description = "VP6800 Drive-Up Payment Done Ticket ID : " . $ticket->id;
                    $authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
                    $authorized_anet_transaction->expiration = $request->payment_details['expiry'];
                    $authorized_anet_transaction->card_type = isset($request->payment_details['CardType']) ? $request->payment_details['CardType'] : 'NA';
                    $authorized_anet_transaction->ref_id = isset($request->payment_details['processorReference']) ? $request->payment_details['processorReference'] : '';
                    $authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
                    $authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
                    $authorized_anet_transaction->method = "card";
                    $authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
                    $authorized_anet_transaction->save();

                    $ticket->anet_transaction_id = $authorized_anet_transaction->id;
                    $ticket->terminal_id = $terminalID;
                    $ticket->checkout_session_id = $request->payment_details['CardHolderID'];
                    $ticket->checkout_payment_token = $request->payment_details['token'];

                    $ticket->checkout_card_last_four = substr($request->payment_details['MaskedPAN'], -4);
                    $ticket->checkout_expiry = $request->payment_details['expiry'];
                    $ticket->checkout_card_type = isset($request->payment_details['CardType']) ? $request->payment_details['CardType'] : 'NA';
                }
                $ticket->discount_amount = $request->discount_amount;
            } else {
                $ticket->is_offline_payment = '1';
                $onlyCheckout = 1;
                $ticket->payment_date = date("Y-m-d H:i:s");
                $ticket->discount_amount = $discountAmount;
                $ticket->payment_by = Auth::user()->id;
            }


            $diff_in_hours = $ticket->getCheckOutCurrentTime(true);
            $ticket->is_checkout = '1';
            $ticket->checkout_datetime = $ticket->getCheckOutCurrentTime();
            $ticket->checkout_time = $ticket->getCheckOutCurrentTime();
            $ticket->estimated_checkout = $ticket->getCheckOutCurrentTime();
            $ticket->checkout_by = Auth::user()->id;
            $ticket->checkout_remark = isset($request->remark) ? $request->remark : 'Admin Remark Checkout';
            $ticket->length = $diff_in_hours;
            $ticket->checkout_mode = '8';
            $ticket->event_user_id = Auth::user()->id;
            $this->attendantLog->error('confirmCheckout check data before save :  ');
            // $this->log->error('confirmCheckout : rate  ' . json_encode($ticket));
            //dd($ticket->anet_transaction_id, $ticket->estimated_checkout, date("Y-m-d H:i:s"));
            if (($ticket->is_offline_payment == '1') && (strtotime($ticket->estimated_checkout)) >= strtotime(date("Y-m-d H:i:s"))) {
            } else {
                if (($ticket->anet_transaction_id != '') && (strtotime($ticket->estimated_checkout)) >= strtotime(date("Y-m-d H:i:s"))) {
                } else {
                    $newTotal = $request->total;
                    if ($request->discount_amount != '' || $request->discount_amount > 0) {
                        $newTotal = $request->total + $discountAmount;
                    }
                    if ($newPrice <= 0) {
                        $newPrice = '0.00';
                    }
                    if (filter_var($newPrice, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) != $newTotal) {
                        throw new ApiGenericException(
                            'Sent rate does not match database rate, please refresh and try again.',
                            422,
                            ['sent_rate' => $request->total, 'database_rate' => $newPrice, 'isratemismatch' => 1]
                        );
                    }
                }
            }

            if ($onlyCheckout == 0) {
                $ticket->rate_description = isset($this->rate['id']) ? $this->rate['id'] : '';
                $ticket->grand_total = $request->total;
                // process report data for now specific to twonsend ;         
            }

            //$ticket->msg = "Thank you for the checkout.";

            $ticket->save();

            $transactionData = TransactionData::where("ticket_id", $ticket->id)->first();
            if (!$transactionData) {
                $this->saveTransactionData();
            }
        }
        $ticket->checkout_gate = $request->gate;
        if ($ticket->reservation_id != '') {
            $ticket->checkout_time = date('Y-m-d H:i:s');

            $reservation = Reservation::where('id', $ticket->reservation_id)->first();
            $reservation->is_ticket = 2;
            $reservation->save();
        } else {

            //check if pass need to used
            if ($request->is_pass == '1') {
                $pass = UserPass::where('user_id', $ticket->user_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->orderBy('id', 'DESC')->first();
                if ($pass) {
                    if ($pass->remaining_days == 0) {
                        throw new NotFoundException('User Pass is not valid.');
                    } else {
                        $ticket->user_pass_id = $pass->id;
                        $ticket->save();
                    }
                } else {
                    throw new NotFoundException('User is not having any valid pass.');
                }
            }
            // vijay commented and because used above  
            // $ticket->checkout_datetime = date('Y-m-d H:i:s'); 
            // $ticket->checkout_time = date('Y-m-d H:i:s');
        }

        $ticket->save();
        $ticket->msg = "Thank you for the payment.";
        if ($ticket->is_checkout == '1') {
            $ticket->msg = "Thank you for the checkout.";
            if (isset($request->direct_checkout) && $request->direct_checkout == 0) {
                if ($request->gate == '' || $request->gate == '0') {
                    throw new ApiGenericException('Please select gate.');
                }
                $this->attendantLog->error('confirmCheckout checkout');
                if ($ticket->facility->check_vehicle_enabled == 1) {
                    $gateStatus = $this->attendantOpenGate($ticket->facility_id, $request->gate, '');
                    if ($gateStatus == "true") {
                    } else {
                        throw new ApiGenericException('Currently Gate is not available.');
                    }
                }
            }

            if (isset($ticket->user_id) && $ticket->user_id > 0) {
                $facilityName = ucwords($facility->full_name);
                $user = User::find($ticket->user_id);
                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                if ($partnerDetails->user_id == self::TOWNSEND_PARTNER_ID) {
                    $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                    })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                } else {
                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                }
                $url = env('RECEIPT_URL');
                $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;
                $this->customeReplySms($sms_msg, $user->phone);
            }
        }
        return $ticket;
    }


    public function attendantConfirmCheckin(Request $request)
    {
        //try{
        $this->setCustomTimezone($request->facility_id);
        $this->attendantLog->info("Request received --" . json_encode($request->all()));
        $facility = Facility::with('FacilityPaymentDetails.facilityPaymentType')->find($request->facility_id);

        if (!$facility) {
            throw new ApiGenericException("Invalid garage.");
        }
        $paymentGateway = '';
        if (isset($facility->FacilityPaymentDetails->facilityPaymentType->id)) {
            $paymentGateway = $facility->FacilityPaymentDetails->facilityPaymentType->payment_type;
        }

        $facilityName =  ucwords($facility->full_name);
        $gate = Gate::where('gate', $request->gate_id)->where('facility_id', $request->facility_id)->first();
        if (!$gate) {
            throw new ApiGenericException("The system is not currently available. Please try again later.");
        }

        if (isset($gate) && $gate->active == '0') {
            throw new ApiGenericException('The system is not currently available. Please try again later.');
        }
        if ($request->session_id == '' && $request->phone == '' && $request->license_plate == '') {
            throw new ApiGenericException('Card info or phone not found. Please try again.');
        }
        if (isset($gate) && $gate->gate_type == "entry") {
            // ->whereNull("cancelled_at")
            $permitRequest = PermitRequest::with(['user'])->where('session_id', $request->session_id)->where('facility_id', $request->facility_id)->orderBy("id", "DESC")->first();
            //->whereDate('grace_end_date', '>=', date("Y-m-d H:i:s"))
            if ($permitRequest) {
                if (strtotime($permitRequest->desired_start_date) <= strtotime(date("Y-m-d")) && strtotime($permitRequest->grace_end_date) >= strtotime(date("Y-m-d"))) {
                    if ($permitRequest->is_antipass_enabled == 0 || $permitRequest->is_antipass_enabled == "0") {
                        $permitTicket = Ticket::where("permit_request_id", $permitRequest->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
                        if ($permitTicket) {
                            throw new ApiGenericException('You have already checked-in.');
                        }
                    }
                    if ($request->license_plate == '') {
                        $mappings = PermitVehicleMapping::where("permit_request_id", $permitRequest->id)->orderBy("id", "DESC")->get();
                        if (count($mappings) > 1) {
                            $vehicle = PermitVehicle::find($mappings[0]->permit_vehicle_id);
                            if ($vehicle) {
                                $request->request->add(['license_plate' => $vehicle->license_plate_number]);
                            }
                        } else {
                            $vehicle = PermitVehicle::find($mappings[0]->permit_vehicle_id);
                            if ($vehicle) {
                                $request->request->add(['license_plate' => $vehicle->license_plate_number]);
                            }
                        }
                    }

                    $facilityName = ucwords($facility->full_name);

                    $data['user_id'] = $permitRequest->user_id;
                    $data['checkin_gate'] = $request->gate_id;
                    $data['facility_id'] = $request->facility_id;
                    $data['is_checkin'] = 1;
                    $data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
                    $data['checkin_time'] = date('Y-m-d H:i:s');
                    $data['check_in_datetime'] = date('Y-m-d H:i:s');
                    //$data['checkout_datetime'] = $permitRequest->desired_end_date;
                    $data['ticket_security_code'] = rand(1000, 9999);
                    $data['partner_id'] = $facility->owner_id;
                    //$data['total'] = $permitRequest->permit_rate;
                    //$data['grand_total'] = $permitRequest->permit_rate;
                    $data['permit_request_id'] = $permitRequest->id;
                    $data['license_plate'] = $request->license_plate;
                    $data['device_type'] = $request->device_type;
                    $data['session_id'] = $request->session_id;
                    $result = Ticket::create($data);
                    Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                    $msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                    if (isset($permitRequest->user->phone)) {
                        dispatch((new SendSms($msg, $permitRequest->user->phone))->onQueue(self::QUEUE_NAME));
                    }

                    //$msg =  "Welcome to $facilityName. #$result->ticket_number.";
                    $msg =  "Welcome, Permit #" . $permitRequest->account_number;
                    //check gate api
                    if ($facility->open_gate_enabled == '1') {
                        $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                        if ($gateStatus == "true") {
                        } else {
                            throw new ApiGenericException($gateStatus);
                        }
                    }
                    $data = [
                        'msg' => $msg,
                        'permit_number' => $permitRequest->account_number,
                        'checkin_time' => date("g:i A", strtotime($result->checkin_time)),
                        'booking_type' => "permit",
                        'is_check_in_ontime' => "1"
                    ];
                    return $data;
                } else {
                    throw new ApiGenericException('Your Permit is expired or canceled.');
                }
            }

            $reservation = Reservation::with('user')->where("session_id", $request->session_id)->where("facility_id", $request->facility_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->orderBy("id", "DESC")->first();
            if ($reservation) {
                $user = $reservation->user;
                $config = Configuration::where('field_name', 'prepaid-checkin-time')->where("facility_id", $facility->id)->first();
                if (count($config) > 0) {
                    $prepaidCheckinTime = $config->field_value;
                } else {
                    $prepaidCheckinTime = 15;
                }
                $today = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'))->addMinutes($prepaidCheckinTime);
                $reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);
                // $reservationEndDate = $reservationstartDate->addHours($reservation->length);
                // converted the decimal lenght to hours and minutes by Ashutosh
                $time = Carbon::parse($reservation->start_timestamp)->addHours($reservation->length);
                if (intval($reservation->length) != $reservation->length) {
                    $timarr = explode('.', $reservation->length);
                    $time->addMinutes($timarr[1]);
                }
                $reservationEndDate = $time->format('Y-m-d H:i:s');

                if (strtotime($today) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)) {
                    if (strtotime($today) < strtotime($reservation->start_timestamp)) {
                    } else {
                        $ticket = Ticket::where('user_id', $user->id)->where("facility_id", $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                        if ($ticket) {
                            throw new ApiGenericException('You have already checked-in.');
                        }

                        $data['user_id'] = $user->id;
                        $data['checkin_gate'] = $request->gate_id;
                        $data['facility_id'] = $request->facility_id;
                        $data['is_checkin'] = 1;
                        $data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
                        $data['checkin_time'] = date('Y-m-d H:i:s');
                        $data['ticket_security_code'] = rand(1000, 9999);
                        $data['partner_id'] = $facility->owner_id;
                        if ($reservation) {
                            $data['reservation_id'] = $reservation->id;
                            $data['check_in_datetime'] = $reservation->start_timestamp;
                            $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                            $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                            $data['payment_date'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                            $data['total'] = $reservation->total;
                            $data['grand_total'] = $reservation->total;
                            $data['length'] = $reservation->length;
                            $data['user_pass_id'] = $reservation->user_pass_id;
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['license_plate'] = $request->license_plate != '' ? $request->license_plate : $reservation->license_plate;
                            $reservation->is_ticket = '1';
                            $reservation->save();
                        }

                        $data['device_type'] = $request->device_type;
                        $data['session_id'] = $request->session_id;
                        $data['payment_token'] = $request->payment_token;
                        $result = Ticket::create($data);
                        $facilityName = ucwords($facility->full_name);
                        if ($reservation) {
                            Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                        } else {
                            Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                            $msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                            dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
                        }

                        $this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


                        //check gate api
                        if ($facility->open_gate_enabled == '1') {
                            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                            if ($gateStatus == "true") {
                            } else {
                                throw new ApiGenericException($gateStatus);
                            }
                        }

                        // send Notification 
                        $deviceToken =  Devicetoken::where('user_id', $user->id)->first();
                        if ($deviceToken) {
                            $updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::RES_CHECKIN_NOTIFICATION];
                            Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
                        }

                        //$msg =  "Welcome to $facilityName. #$result->ticket_number.";
                        $msg =  "WELCOME. #$result->ticket_number";
                        $data = ['msg' => $msg];
                        $data['booking_number'] = $reservation->ticketech_code;
                        $data['booking_start_time'] = date("g:i A", strtotime($result->check_in_datetime));
                        $data['booking_exit_time'] = date("g:i A", strtotime('+1 second', strtotime($result->checkout_datetime)));
                        $data['booking_entry_time'] = date("g:i A", strtotime($result->checkin_time));
                        $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                        $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                        $data['booking_type'] = 'reservation';
                        $data['is_check_in_ontime'] = '1';
                        return $data;
                    }
                } else {
                    if (strtotime($today) < strtotime($reservation->start_timestamp)) {
                    } else {
                        $ticket = Ticket::where('user_id', $user->id)->where("facility_id", $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
                        if ($ticket) {
                            throw new ApiGenericException('You have already checked-in.');
                        }

                        $data['user_id'] = $user->id;
                        $data['checkin_gate'] = $request->gate_id;
                        $data['facility_id'] = $request->facility_id;
                        $data['is_checkin'] = 1;
                        $data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
                        $data['checkin_time'] = date('Y-m-d H:i:s');
                        $data['ticket_security_code'] = rand(1000, 9999);
                        $data['partner_id'] = $facility->owner_id;
                        if ($reservation) {
                            $data['reservation_id'] = $reservation->id;
                            $data['check_in_datetime'] = $reservation->start_timestamp;
                            $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                            $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                            $data['payment_date'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                            $data['total'] = $reservation->total;
                            $data['grand_total'] = $reservation->total;
                            $data['length'] = $reservation->length;
                            $data['user_pass_id'] = $reservation->user_pass_id;
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['license_plate'] = $request->license_plate != '' ? $request->license_plate : $reservation->license_plate;
                            $reservation->is_ticket = '1';
                            $reservation->save();
                        }

                        $data['device_type'] = $request->device_type;
                        $data['session_id'] = $request->session_id;
                        $data['payment_token'] = $request->payment_token;
                        $result = Ticket::create($data);
                        $facilityName = ucwords($facility->full_name);
                        if ($reservation) {
                            Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                        } else {
                            Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                            $msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                            dispatch((new SendSms($msg, $user->phone))->onQueue(self::QUEUE_NAME));
                        }

                        $this->log->info("checkin SMS send to user {$user->phone} with ticket number {$result->ticket_number}");


                        //check gate api
                        if ($facility->open_gate_enabled == '1') {
                            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                            if ($gateStatus == "true") {
                            } else {
                                throw new ApiGenericException($gateStatus);
                            }
                        }

                        // send Notification 
                        $deviceToken =  Devicetoken::where('user_id', $user->id)->first();
                        if ($deviceToken) {
                            $updateJobParams = ['user_id' => $user->id, 'request_id' => $user->email, 'type' => self::PICKUP_TYPE, 'facility_name' => $facility->full_name, 'notification_type' => self::RES_CHECKIN_NOTIFICATION];
                            Artisan::queue('customer:checkin-pushnotification', $updateJobParams);
                        }

                        //$msg =  "Welcome to $facilityName. #$result->ticket_number.";
                        $msg =  "WELCOME. #$result->ticket_number";
                        $data = ['msg' => $msg];
                        $data['booking_number'] = $reservation->ticketech_code;
                        $data['booking_start_time'] = date("g:i A", strtotime($result->check_in_datetime));
                        $data['booking_exit_time'] = date("g:i A", strtotime('+1 second', strtotime($result->checkout_datetime)));
                        $data['booking_entry_time'] = date("g:i A", strtotime($result->checkin_time));
                        $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                        $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                        $data['booking_type'] = 'reservation';
                        $data['is_check_in_ontime'] = '1';
                        return $data;
                    }
                }
            }
        }
        $licensePlateOnly = 0;
        if (isset($request->phone) && $request->phone != '') {
            if ($request->session_id != '') {
                $paymentGateway = "Datacap";
            }
            $countryCode = '';
            // Get country Code
            $countryCode = QueryBuilder::appendCountryCode();
            $user = User::where("phone", $countryCode . $request->phone)->where("created_by", $facility->owner_id)->first();
            if (!$user) {
                $user = User::create(
                    [
                        'name' => '',
                        'email' => '',
                        'phone' => $request->phone,
                        'password' => Hash::make(str_random(60)),
                        'anon' => true,
                        'user_type' => '5',
                        'created_by' => $facility->owner_id,
                        'session_id' => $request->session_id,
                    ]
                );
                if ($request->session_id != '') {
                    $userSession = new UserSession();
                    $userSession->user_id = $user->id;
                    $userSession->partner_id = $user->created_by;
                    $userSession->session_id = $request->session_id;
                    $userSession->save();
                }
            } else {
                $existSession = UserSession::where("user_id", $user->id)->where("session_id", $request->session_id)->first();
                if (!$existSession) {
                    if ($request->session_id != '') {
                        $userSession = new UserSession();
                        $userSession->user_id = $user->id;
                        $userSession->partner_id = $user->created_by;
                        $userSession->session_id = $request->session_id;
                        $userSession->save();
                    }
                }
            }
        } elseif (isset($request->session_id) && $request->session_id != '') {
            $paymentGateway = "Datacap";
            $userSessionExist = UserSession::where("session_id", $request->session_id)->where('partner_id', $facility->owner_id)->orderBy("id", "DESC")->first();
            if ($userSessionExist) {
                $user = User::where('id', $userSessionExist->user_id)->first();
            } else {
                $user = User::create(
                    [
                        'name' => '',
                        'email' => '',
                        'phone' => $request->phone,
                        'password' => Hash::make(str_random(60)),
                        'anon' => true,
                        'user_type' => '5',
                        'created_by' => $facility->owner_id,
                        'session_id' => $request->session_id,
                    ]
                );

                $userSession = new UserSession();
                $userSession->user_id = $user->id;
                $userSession->partner_id = $user->created_by;
                $userSession->session_id = $request->session_id;
                $userSession->save();
            }
            // if ($request->device_type == 'ANDROID_TAB') {
            //     $ticket->payment_gateway = "Datacap";
            // }

        } else {
            $permitVehicle = PermitVehicle::with(['user'])->where("license_plate_number", $request->license_plate)->where("partner_id", $facility->owner_id)->first();
            if ($permitVehicle && isset($permitVehicle->user->id)) {
                $user = $permitVehicle->user;
            } else {
                $licensePlateOnly = 1;
            }
        }



        $isMember = 0;
        if (isset($gate) && $gate->gate_type == "entry") {

            /*if ($request->license_plate != '') {
            $licensePlate = WorldportLicensePlate::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->first();
            if (!$licensePlate) {
            $request->request->add(['license_plate' => '']);
            }
            }*/

            $arrival_time = date("Y-m-d H:i:s");

            $length_of_stay = .01;

            //if checkin by lpr queue and user link card that checkin
            if ($request->eticket_id != '') {
                $ticket = Ticket::where('ticket_number', $request->eticket_id)->where('is_checkin', '0')->where('is_checkout', '0')->first();
                if (!$ticket) {
                    throw new ApiGenericException('Invalid checkin details.');
                }

                $ticket->vp_device_checkin = $ticket->reservation_id != '' ? '0' : '1';
                $ticket->license_plate = isset($request->license_plate) ? $request->license_plate : '';
                $ticket->card_last_four = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                $ticket->expiry = isset($request->expiry) ? $request->expiry : '';
                if (isset($request->CardType)) {
                    $card_type = '';
                    if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
                        $card_type = 'VISA';
                    } else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
                        $card_type = 'MASTERCARD';
                    } else if (strtolower($request->CardType) == "jcb") {
                        $card_type = 'JCB';
                    } else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
                        $card_type = 'AMEX';
                    } else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
                        $card_type = 'DISCOVER';
                    } else {
                        $card_type = $request->CardType;
                    }
                    $ticket->card_type = $card_type;
                }
                $ticket->device_type = $request->device_type;
                $ticket->terminal_id = isset($request->TerminalID) ? $request->TerminalID : '';
                $ticket->payment_gateway = $paymentGateway;
                $ticket->user_id = $user->id;
                $ticket->session_id = $request->session_id;
                $ticket->payment_token = $request->payment_token;
                $ticket->is_checkin = '1';
                $ticket->save();

                $facilityName = ucwords($facility->full_name);

                $this->attendantLog->info("eticket session user update  checkedin with ticket number {$ticket->ticket_number}");
                //check gate api
                if ($facility->open_gate_enabled == '1') {
                    $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                    if ($gateStatus == "true") {
                    } else {
                        throw new ApiGenericException($gateStatus);
                    }
                }
                if ($user->email != '') {
                    Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
                }
                $msg =  "WELCOME. #$ticket->ticket_number";
                if ($user->phone != '') {
                    $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                    $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                    })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                    if (isset($getRM->slug)) {
                        $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                    } else {
                        $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                    }

                    $url = env('TOUCHLESS_WEB_URL');
                    $grace_period = $facility->grace_period_minute;
                    $ticket_number = base64_encode($ticket->ticket_number);
                    $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                    dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
                }

                $data = ['msg' => $msg];
                $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                $this->attendantLog->info("eticket Checkin update done, response sent.");
                return $data;
            }
            if ($licensePlateOnly == 1) {
                $ticket = Ticket::where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
            } else {
                $ticket = Ticket::where('user_id', $user->id)->where('facility_id', $request->facility_id)->where('is_checkin', '1')->where('is_checkout', '0')->first();
            }

            if ($ticket) {
                throw new ApiGenericException('You have already checked-in.');
            }
            if ($licensePlateOnly == 1) {
                $reservation = Reservation::with('user')->where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
            } else {
                $reservation = Reservation::with('user')->where('facility_id', $request->facility_id)->where("user_id", $user->id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
            }

            $userPass = [];
            if (!$reservation) {
                if ($facility->facility_booking_type == '0') {
                    throw new ApiGenericException('Drive-Up booking is not allowed.');
                }
                if ($licensePlateOnly == 1) {
                    $userPass = UserPass::with('user')->where('license_plate', $request->license_plate)->where('facility_id', $request->facility_id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days', '>', 0)->orderBy('id', 'DESC')->first();
                } else {
                    $userPass = UserPass::with('user')->where('facility_id', $request->facility_id)->where("user_id", $user->id)->whereDate('end_date', '>=', date("Y-m-d"))->whereDate('start_date', '<=', date("Y-m-d"))->where('remaining_days', '>', 0)->orderBy('id', 'DESC')->first();
                }

                if (!$userPass) {
                    if (isset($request->is_prepaid_booking) && $request->is_prepaid_booking == '1') {
                        throw new ApiGenericException('No prepaid booking found against this card.');
                    }

                    if ($facility->is_prepaid_first == '2') {
                        $today = date("Y-m-d");
                        $todayEvent = Event::whereDate('start_time', '=', $today)->whereDate('end_time', '>=', $today)->where("partner_id", $facility->owner_id)->where("is_active", '1')->first();
                        if (!$todayEvent) {
                            $data['user_id'] = isset($user->id) ? $user->id : NULL;
                            $data['checkin_gate'] = $request->gate_id;
                            $data['facility_id'] = $request->facility_id;
                            $data['is_checkin'] = 1;
                            $data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['ticket_security_code'] = rand(1000, 9999);
                            $data['partner_id'] = $facility->owner_id;
                            $data['check_in_datetime'] = date('Y-m-d H:i:s');
                            $data['vp_device_checkin'] = '1';

                            $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                            $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                            if (isset($request->CardType)) {
                                $card_type = '';
                                if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
                                    $card_type = 'VISA';
                                } else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
                                    $card_type = 'MASTERCARD';
                                } else if (strtolower($request->CardType) == "jcb") {
                                    $card_type = 'JCB';
                                } else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
                                    $card_type = 'AMEX';
                                } else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
                                    $card_type = 'DISCOVER';
                                } else {
                                    $card_type = $request->CardType;
                                }
                                $data['card_type'] = $card_type;
                            }
                            $data['device_type'] = $request->device_type;
                            $data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
                            $data['payment_gateway'] = $paymentGateway;
                            $data['session_id'] = $request->session_id;
                            $data['payment_token'] = $request->payment_token;
                            $result = Ticket::create($data);

                            $facilityName = ucwords($facility->full_name);

                            $this->attendantLog->info("session user checkedin with ticket number {$result->ticket_number}");
                            //check gate api
                            if ($facility->open_gate_enabled == '1') {
                                $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                                if ($gateStatus == "true") {
                                } else {
                                    throw new ApiGenericException($gateStatus);
                                }
                            }
                            //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
                            if (isset($user->email) && $user->email != '') {
                                Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                            }
                            $msg =  "WELCOME. #$result->ticket_number";
                            if (isset($user->phone) && $user->phone != '') {
                                //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                    $join->on('user_facilities.user_id', '=', 'users.id');
                                    $join->where('user_facilities.facility_id', "=", $facility->id);
                                })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                                if (isset($getRM->slug)) {
                                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                                } else {
                                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                }
                                $url = env('TOUCHLESS_WEB_URL');
                                $grace_period = $facility->grace_period_minute;
                                $ticket_number = base64_encode($result->ticket_number);
                                $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                                dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
                                $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                                $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                            }
                            $data = ['msg' => $msg];

                            $this->attendantLog->info("Checkin done, response sent.");
                            return $data;
                        }

                        $parkingNowTime = date("Y-m-d H:i:s");
                        $parkingStartTime = $todayEvent->parking_start_time;
                        $parkingEndTime = $todayEvent->parking_end_time;
                        $eventStartTime = $todayEvent->start_time;
                        $eventEndTime = $todayEvent->end_time;

                        if (strtotime($parkingNowTime) >= strtotime($parkingStartTime) && strtotime($parkingNowTime) <= strtotime($parkingEndTime)) {
                            //$data['price'] =   $todayEvent->event_rate + $facility->processing_fee;
                            $driveupRate = 0;
                            if ($todayEvent->driveup_event_rate != '' && $todayEvent->driveup_event_rate > 0) {
                                $rate['price'] = $todayEvent->driveup_event_rate;
                                $taxRate = $facility->getTaxRate($rate);          // to get tax price                
                                $driveupRate = $todayEvent->driveup_event_rate + $facility->drive_up_processing_fee + $taxRate;
                            } else {
                                $rate['price'] = $facility->base_rate;
                                $taxRate = $facility->getTaxRate($rate);          // to get tax price                
                                $driveupRate = $facility->base_rate + $facility->processing_fee + $taxRate;
                            }
                            $data['price'] =  $driveupRate;

                            return $data;
                        } else {


                            $data['user_id'] = isset($user->id) ? $user->id : NULL;
                            $data['checkin_gate'] = $request->gate_id;
                            $data['facility_id'] = $request->facility_id;
                            $data['is_checkin'] = 1;
                            $data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
                            $data['checkin_time'] = date('Y-m-d H:i:s');
                            $data['ticket_security_code'] = rand(1000, 9999);
                            $data['partner_id'] = $facility->owner_id;
                            $data['check_in_datetime'] = date('Y-m-d H:i:s');
                            $data['vp_device_checkin'] = '1';

                            $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                            $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                            if (isset($request->CardType)) {
                                $card_type = '';
                                if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
                                    $card_type = 'VISA';
                                } else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
                                    $card_type = 'MASTERCARD';
                                } else if (strtolower($request->CardType) == "jcb") {
                                    $card_type = 'JCB';
                                } else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
                                    $card_type = 'AMEX';
                                } else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
                                    $card_type = 'DISCOVER';
                                } else {
                                    $card_type = $request->CardType;
                                }
                                $data['card_type'] = $card_type;
                            }

                            $data['device_type'] = $request->device_type;
                            $data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
                            $data['payment_gateway'] = $paymentGateway;
                            $data['session_id'] = $request->session_id;
                            $data['payment_token'] = $request->payment_token;

                            $result = Ticket::create($data);

                            $facilityName = ucwords($facility->full_name);

                            $this->attendantLog->info("session user checkedin with ticket number {$result->ticket_number}");
                            //check gate api
                            if ($facility->open_gate_enabled == '1') {
                                $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                                if ($gateStatus == "true") {
                                } else {
                                    throw new ApiGenericException($gateStatus);
                                }
                            }
                            //Artisan::queue('checkin-email-send', array('id' => $result->id,'slug'=>'checkin','signup' => '','reset'=>''));
                            if (isset($user->email) && $user->email != '') {
                                Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                            }
                            $msg =  "WELCOME. #$result->ticket_number";
                            if (isset($user->phone) && $user->phone != '') {
                                //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                    $join->on('user_facilities.user_id', '=', 'users.id');
                                    $join->where('user_facilities.facility_id', "=", $facility->id);
                                })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                                if (isset($getRM->slug)) {
                                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                                } else {
                                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                }
                                $url = env('TOUCHLESS_WEB_URL');
                                $grace_period = $facility->grace_period_minute;
                                $ticket_number = base64_encode($result->ticket_number);
                                $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                                dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
                                $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                                $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                            }

                            $data = ['msg' => $msg];

                            $this->log->info("Checkin done, response sent.");
                            return $data;
                        }
                    }




                    if ($facility->is_prepaid_first == '0') {
                        $data['user_id'] = isset($user->id) ? $user->id : NULL;
                        $data['checkin_gate'] = $request->gate_id;
                        $data['facility_id'] = $request->facility_id;
                        $data['is_checkin'] = 1;
                        $data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
                        $data['checkin_time'] = date('Y-m-d H:i:s');
                        $data['ticket_security_code'] = rand(1000, 9999);
                        $data['partner_id'] = $facility->owner_id;
                        $data['check_in_datetime'] = date('Y-m-d H:i:s');
                        $data['vp_device_checkin'] = '1';

                        $data['license_plate'] = isset($request->license_plate) ? $request->license_plate : '';
                        $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
                        $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
                        if (isset($request->CardType)) {
                            $card_type = '';
                            if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
                                $card_type = 'VISA';
                            } else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
                                $card_type = 'MASTERCARD';
                            } else if (strtolower($request->CardType) == "jcb") {
                                $card_type = 'JCB';
                            } else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
                                $card_type = 'AMEX';
                            } else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
                                $card_type = 'DISCOVER';
                            } else {
                                $card_type = $request->CardType;
                            }
                            $data['card_type'] = $card_type;
                        }
                        $data['device_type'] = $request->device_type;
                        $data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
                        $data['payment_gateway'] = $paymentGateway;
                        $data['session_id'] = $request->session_id;
                        $data['payment_token'] = $request->payment_token;

                        $result = Ticket::create($data);

                        $facilityName = ucwords($facility->full_name);

                        $this->attendantLog->info("session user checkedin with ticket number {$result->ticket_number}");
                        //check gate api
                        if ($facility->open_gate_enabled == '1') {
                            $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                            if ($gateStatus == "true") {
                            } else {
                                throw new ApiGenericException($gateStatus);
                            }
                        }
                        if (isset($user->email) && $user->email != '') {
                            Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                        }
                        $msg =  "WELCOME. #$result->ticket_number";
                        if (isset($user->phone) && $user->phone != '') {
                            //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                            $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                $join->on('user_facilities.user_id', '=', 'users.id');
                                $join->where('user_facilities.facility_id', "=", $facility->id);
                            })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                            if (isset($getRM->slug)) {
                                $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                            } else {
                                $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                            }
                            $url = env('TOUCHLESS_WEB_URL');
                            $grace_period = $facility->grace_period_minute;
                            $ticket_number = base64_encode($result->ticket_number);
                            $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                            dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
                            $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                            $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                        }

                        $data = ['msg' => $msg];

                        $this->attendantLog->info("Checkin done, response sent.");
                        return $data;
                    }
                    $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                    $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));
                    $diff_in_hours = $arrival_time->diffInRealHours($from);

                    //$isMember = 0;
                    $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
                    if ($rate == false) {
                        throw new ApiGenericException('Garage is not available.');
                    }
                    $data = [];
                    //$data ['session_id'] = $request->session_id;
                    //$data ['price'] = $rate['price']  + $facility->processing_fee;
                    $data['price'] = $rate['price']  + $facility->processing_fee;
                    $this->attendantLog->info("price sent, response sent.");
                    return $data;
                }
            }

            if ($facility->facility_booking_type == '1') {
                throw new ApiGenericException('Prepaid booking is not allowed.');
            }

            if ($facility->check_vehicle_enabled == '1') {
                $gateStatus = $this->isParkEngageGateAvailable($request->facility_id, $request->gate_id, '');
                if ($gateStatus == "true") {
                } else {
                    throw new ApiGenericException($gateStatus);
                }
            }
            $data['user_id'] = isset($user->id) ? $user->id : NULL;
            $data['checkin_gate'] = $request->gate_id;
            $data['facility_id'] = $request->facility_id;
            $data['is_checkin'] = 1;
            $data['ticket_number'] = $this->checkTicketNumber($request->facility_id);
            $data['checkin_time'] = date('Y-m-d H:i:s');
            $data['check_in_datetime'] = date('Y-m-d H:i:s');
            $data['ticket_security_code'] = rand(1000, 9999);
            $data['vp_device_checkin'] = '1';
            $data['partner_id'] = $facility->owner_id;

            if ($reservation) {
                $data['reservation_id'] = $reservation->id;
                $data['check_in_datetime'] = $reservation->start_timestamp;
                $data['checkout_datetime'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                $data['estimated_checkout'] = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                $data['total'] = $reservation->total;
                $data['grand_total'] = $reservation->total;
                $data['length'] = $reservation->length;
                $data['user_pass_id'] = $reservation->user_pass_id;
                $data['checkin_time'] = date('Y-m-d H:i:s');
                $data['vp_device_checkin'] = '0';
                $reservation->is_ticket = '1';
                $reservation->save();
            }
            if (count($userPass) > 0) {
                $data['check_in_datetime'] = date('Y-m-d H:i:s');
                $data['checkout_datetime'] = date('Y-m-d H:i:s');
                $data['user_pass_id'] = $userPass->id;
                $data['vp_device_checkin'] = '0';

                $userPass->consume_days = $userPass->consume_days + 1;
                $userPass->remaining_days = $userPass->remaining_days - 1;
                $userPass->save();
            }

            $data['license_plate'] = isset($request->license_plate) ? $request->license_plate : '';
            $data['card_last_four'] = isset($request->MaskedPAN) ? substr($request->MaskedPAN, -4) : '';
            $data['expiry'] = isset($request->expiry) ? $request->expiry : '';
            if (isset($request->CardType)) {
                $card_type = '';
                if (strtolower($request->CardType) == "vs" || strtolower($request->CardType) == "visa") {
                    $card_type = 'VISA';
                } else if (strtolower($request->CardType) == "mc" || strtolower($request->CardType) == "mastercard") {
                    $card_type = 'MASTERCARD';
                } else if (strtolower($request->CardType) == "jcb") {
                    $card_type = 'JCB';
                } else if (strtolower($request->CardType) == "ax" || strtolower($request->CardType) == "amex"  || strtolower($request->CardType) == "american express") {
                    $card_type = 'AMEX';
                } else if (strtolower($request->CardType) == "ds" || strtolower($request->CardType) == "di" || strtolower($request->CardType) == "discover") {
                    $card_type = 'DISCOVER';
                } else {
                    $card_type = $request->CardType;
                }
                $data['card_type'] = $card_type;
            }

            $data['device_type'] = $request->device_type;
            $data['terminal_id'] = isset($request->TerminalID) ? $request->TerminalID : '';
            $data['payment_gateway'] = $paymentGateway;
            $data['session_id'] = $request->session_id;
            $data['payment_token'] = $request->payment_token;
            $result = Ticket::create($data);

            $this->log->info("checkin done --" . json_encode($result));
            if ($result) {
                $facilityName = ucwords($facility->full_name);
                if ($reservation) {
                    Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                } else {
                    try {

                        Artisan::queue('email:touchless-parking-atlanta-confirm-checkin', array('id' => $result->id, 'type' => 'checkin'));
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                            $join->on('user_facilities.user_id', '=', 'users.id');
                            $join->where('user_facilities.facility_id', "=", $facility->id);
                        })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                        if (isset($getRM->slug)) {
                            $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                        } else {
                            $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        }
                        $url = env('TOUCHLESS_WEB_URL');
                        $grace_period = $facility->grace_period_minute;
                        $ticket_number = base64_encode($result->ticket_number);
                        $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $result->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. $url/$name/$ticket_number";
                        if (isset($user->phone)) {
                            dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
                        }
                    } catch (\Exception $e) {
                        $this->attendantLog->error("System exception -" . $e->getMessage());
                    }
                }
                $this->attendantLog->info("checkin SMS send to user {@$user->phone} with ticket number {$result->ticket_number}");
                //check gate api
                if ($facility->open_gate_enabled == '1') {
                    $gateStatus = $this->isParkEngageGateOpen($request->facility_id, $request->gate_id, '');
                    if ($gateStatus == "true") {
                    } else {
                        throw new ApiGenericException($gateStatus);
                    }
                }
                //return "Welcome to $facilityName. #$result->ticket_number.";
                $msg =  "WELCOME. #$result->ticket_number";

                $data = ['msg' => $msg];
                if (isset($user->phone)) {
                    $data['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                    $data['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                }

                $this->log->info("Checkin done, response sent.");
                return $data;
            } else {
                throw new ApiGenericException('Something wrong.');
            }
        }


        /*}catch(\Exception $e){
        $this->log->error("error received --".$e->getMessage());  
        throw new ApiGenericException("The system is not currently available. Please try again later.");
      }*/
    }

    public function isParkEngageGateOpen($facility_id, $gate, $phone = '')
    {

        $facility = Facility::where('id', $facility_id)->first();

        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();
        //check third party gate API
        //$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        if ($facility) {
            if ($facility->adam_host != '') {
                $params = ['gate_id' => $gate->gate];
                $this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
                $cmd_params = ['gate_id' => $gate->gate];
                $this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params" . json_encode($cmd_params));
                if ($gate->is_external_gate == '1' || $gate->is_external_gate == 1) {
                    $command_response = ParkengageGateApi::openExternalGate($cmd_params, $facility->adam_host);
                } else {
                    $command_response = ParkengageGateApi::openGate($cmd_params, $facility->adam_host);
                }
                $this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response" . json_encode($command_response));
                if ($command_response['success'] == true) {
                    if ($command_response['data'][0] == "true") {
                        return true;
                    } else {
                        $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                        return $msg;
                    }
                } else {
                    $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                    return $msg;
                }
            }
        }
    }


    public function saveAttendantGateWithDeviceToken(Request $request)
    {
        $this->attendantLog->info('saveAttendantGateWithDeviceToken : ' . json_encode($request->all()));

        $deviceToken =  Devicetoken::where('device_id', $request->device_id)->where('user_id', $request->user_id)->orderBy("id", "desc")->first();
        if (!$deviceToken) {
            throw new ApiGenericException('Invalid device id or token details.');
        }

        DeviceTokenGateMapping::where("device_token_id", $deviceToken->id)->where('user_id', $request->user_id)->where('facility_id', $request->facility_id)->delete();

        $gateArray = explode(",", $request->gates);
        $data = [];
        if (count($gateArray) > 0) {
            foreach ($gateArray as $key => $value) {
                $data["device_token_id"] = $deviceToken->id;
                $data["user_id"] = $deviceToken->user_id;
                $data["facility_id"] = $request->facility_id;
                $data["gate_id"] = $value;
                DeviceTokenGateMapping::create($data);
            }
        }
        return "Gate mapped successfully.";
    }


    public function attendantOpenGate($facility_id, $gate, $phone = '')
    {

        $facility = Facility::where('id', $facility_id)->first();

        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

        //check third party gate API
        //$gateDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
        if ($facility) {
            if ($facility->adam_host != '') {
                $params = ['gate_id' => $gate->gate];
                $this->attendantLog->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
                /*$response = ParkengageGateApi::isvehicleAvailable($params, $facility->adam_host);
        $this->log->info("Response Parkengage Gate Service is Vehicle Available  : vehicle command complete facility {$facility->id} response" . json_encode($response));
        if ($response['success'] == false) {
          $msg = "The system is not currently available. Please try again later.";
          return $msg;
        }*/
                //if (isset($response['data'][0]) && $response['data'][0] == "true") {
                if (true) {
                    $cmd_params = ['gate_id' => $gate->gate];
                    $this->attendantLog->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params" . json_encode($cmd_params));
                    if ($gate->is_external_gate == '1' || $gate->is_external_gate == 1) {
                        $command_response = ParkengageGateApi::openExternalGate($cmd_params, $facility->adam_host);
                    } else {
                        $command_response = ParkengageGateApi::openGate($cmd_params, $facility->adam_host);
                    }
                    $this->attendantLog->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response" . json_encode($command_response));
                    if ($command_response['success'] == true) {
                        if ($command_response['data'][0] == "true") {
                            return true;
                        } else {
                            $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                            return $msg;
                        }
                    } else {
                        $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                        return $msg;
                    }
                } else {

                    $msg = "Your vehicle must be in the garage in front of the " . ucwords($gate->gate_type) . " gate.";
                    return $msg;
                }
            }
        }
    }


    public function getTicketOverstayList($ticket_number)
    {
        $this->attendantLog->info("getTicketOverstayList: {$ticket_number}");
        $mainTicket = Ticket::where("ticket_number", $ticket_number)->orderBy("id", "ASC")->first();
        $finaleArray = [];
        $baseArray = [];
        if ($mainTicket->is_overstay == '1') {
            $extends = OverstayTicket::where("ticket_number", $ticket_number)->orderBy("id", "ASC")->get();
            $baseArray['checkin_time']  = $mainTicket->checkin_time;
            $baseArray['checkout_time'] = $mainTicket->estimated_checkout;
            $baseArray['length']        = $mainTicket->length;
            $baseArray['total']         = number_format($mainTicket->grand_total, 2);
            $baseArray['grand_total']         = number_format($mainTicket->grand_total, 2);
            $baseArray['is_overstay']         = '1';
            $baseArray['payment_method']         = $mainTicket->card_type;
            array_push($finaleArray, $baseArray);
            foreach ($extends as $key => $extend) {
                $extend->checkin_time = $extend->check_in_datetime;
                $extend->checkout_time = $extend->estimated_checkout;
                $extend->payment_method = $mainTicket->card_type;
                array_push($finaleArray, $extend);
            }
            // $extends = array_merge($baseArray, $extends);
            $extends = $finaleArray;
        } else {
            if ($mainTicket->anet_transaction_id == '' && $mainTicket->is_offline_payment == '0' && $mainTicket->paid_type == '9') {
                return $extends;
            }
            $baseArray['checkin_time']  = $mainTicket->checkin_time;
            $baseArray['checkout_time'] = $mainTicket->estimated_checkout;
            $baseArray['length']        = $mainTicket->length;
            $baseArray['total']         = number_format($mainTicket->grand_total, 2);
            $baseArray['grand_total']         = number_format($mainTicket->grand_total, 2);
            array_push($finaleArray, $baseArray);
            $extends = $finaleArray;
            return $extends;
        }
        if (count($extends) > 1) {
            return $extends;
        } else {
            $extend = [];
            return response()->json($extend, 200);
        }
    }

    public function getTicketUserCardList($ticket_number)
    {
        $ticket = Ticket::with(['facility.FacilityPaymentDetails'])->where("ticket_number", $ticket_number)->first();
        if (!$ticket) {
            throw new ApiGenericException('Ticket not found.');
        }
        if ($ticket->user_id != '') {
            $payment_type_id    = $ticket->facility->FacilityPaymentDetails->facility_payment_type_id;
            $profiles = GatewayHelper::getAllRegisterUserProfiles($payment_type_id, $ticket->user_id, $ticket->facility);
            //$profile = PlanetPaymentProfile::where("user_id", $ticket->user_id)->get();
            return $profiles;
        } else {
            throw new ApiGenericException('Ticket not found.');
        }
    }
}
