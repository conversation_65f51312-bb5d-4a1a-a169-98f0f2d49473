<?php

namespace App\Http\Controllers\ParkEngage;

use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests;
use Artisan;
use Exception;
use Carbon\Carbon;
use App\Models\ParkEngage\FacilitySlot;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\FacilityConfiguration;

class FacilitySlotController extends Controller
{


    public function facilitySlotCount($facility_id)
    {
        if($facility_id == '' || $facility_id == 0){
            throw new ApiGenericException('Invalid facility details.');
        }
        $slot = FacilitySlot::select("slot")->where("facility_id", $facility_id)->orderBy("id", "DESC")->first();
        $config = FacilityConfiguration::select("is_lot_full")->where("facility_id", $facility_id)->orderBy("id", "DESC")->first();
        if(!$slot){
            $slot['slot'] = 0;
            //throw new ApiGenericException('No slot found against this facility.');
        }
        $slot['is_lot_full'] = $config->is_lot_full;
        return $slot;
    }
    
}
