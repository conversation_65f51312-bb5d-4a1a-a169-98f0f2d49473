<?php

namespace App\Http\Controllers\ParkEngage;

use Authorizer;
use Artisan;
use DB;
use Auth;
use App\Models\Facility;
use App\Models\User;
use App\Http\Controllers\Controller;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Models\ParkEngage\MembershipPlan;
use App\Models\ParkEngage\UserMembership;
use App\Models\Promotion;

use Illuminate\Http\Request;
//use App\Http\Requests;

use App\Models\ParkEngage\SubPartnerPermission;
use App\Models\AuthorizeNetTransaction;
use App\Classes\AuthorizeNet\Transactions as AuthorizeNet;
use App\Classes\AuthorizeNet\TransactionsApplePay as AuthorizeNetApplePay;
use App\Classes\AuthorizeNet\Cim;
use App\Classes\MagicCrypt;
use App\Models\PaymentProfile;
use App\Exceptions\UserNotAuthorized;

class DashboardController extends Controller
{
 
  protected $request;  
   public function __construct(Request $request)
    {
        $this->request = $request;
    
    }
   public function index() {
      
	  if(Auth::user()->user_type == '1' || Auth::user()->user_type == '2' ){
        $data['facility'] = Facility::where('active', '1')->count();
        $data['membershipPlan'] = MembershipPlan::count();
        $data['users'] = User::count();  
        $data['promotions'] = Promotion::where('status', '1')->count();
      }
      if(Auth::user()->user_type == '3'){
        $data['facility'] = Facility::where('owner_id', Auth::user()->id)->where('active', '1')->count();
        $data['membershipPlan'] = UserMembership::where('user_id', Auth::user()->id)->count();
        $data['users'] = User::where('created_by', Auth::user()->id)->where('user_type','4')->count();
        $data['rm'] = User::where('created_by', Auth::user()->id)->where('user_type','12')->count();
        $data['promotions'] = Promotion::where('status', '1')->where('owner_id', Auth::user()->id)->count();
      }
      if(Auth::user()->user_type == '4'){
        $facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id',Auth::user()->id)->pluck('facility_id');
			
        $data['facility'] = Facility::where('owner_id', Auth::user()->created_by)->whereIn('id', $facilities)->where('active', '1')->count();
        $data['membershipPlan'] = UserMembership::where('user_id', Auth::user()->id)->count();
        $data['users'] = User::where('created_by', Auth::user()->id)->count();
        $data['promotions'] = Promotion::where('status', '1')->where('owner_id', Auth::user()->created_by)->count();
      }
      if(Auth::user()->user_type == '12'){
        $facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id',Auth::user()->id)->pluck('facility_id');
			  $data['facility'] = Facility::where('owner_id', Auth::user()->created_by)->whereIn('id', $facilities)->where('active', '1')->count();
        $data['membershipPlan'] = UserMembership::where('user_id', Auth::user()->id)->count();
        $data['users'] = User::where('created_by', Auth::user()->id)->count();
        $data['promotions'] = Promotion::where('status', '1')->where('owner_id', Auth::user()->created_by)->count();
      }
      if(Auth::user()->user_type == '10'){
        $facilities = DB::table('business_facility_policy')->whereNull('deleted_at')->where('business_id',Auth::user()->business_id)->pluck('facility_id');
		$data['facility'] = Facility::where('owner_id', Auth::user()->created_by)->whereIn('id', $facilities)->where('active', '1')->count();
		$data['membershipPlan'] = UserMembership::where('user_id', Auth::user()->id)->count();
        $data['users'] = User::where('created_by', Auth::user()->id)->count();
        $data['promotions'] = Promotion::where('status', '1')->where('owner_id', Auth::user()->created_by)->count();
      }
	    if(Auth::user()->user_type == '8'){
        $facilities = DB::table('user_facilities')->whereNull('deleted_at')->where('user_id',Auth::user()->id)->pluck('facility_id');
		$data['facility'] = Facility::where('owner_id', Auth::user()->created_by)->whereIn('id', $facilities)->where('active', '1')->count();
        $data['membershipPlan'] = UserMembership::where('user_id', Auth::user()->id)->count();
        $data['users'] = User::where('created_by', Auth::user()->id)->count();
        $data['promotions'] = Promotion::where('status', '1')->where('owner_id', Auth::user()->created_by)->count();
      }      
      return $data;
   } 


      
}
