<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Classes\MagicCrypt;

use DB;
use CreditCard;

use App\Models\PaymentProfile;
use App\Models\Facility;
use App\Models\MonthlyRequest;
use App\Models\MonthlyRequestCim;
use App\Models\AuthorizeNetTransaction;
use App\Models\User;

use App\Classes\AuthorizeNet\AuthorizeNetMonthlyRequest;

use App\Http\Helpers\QueryBuilder;

use App\Exceptions\ApiGenericException;

use App\Models\MonthlyCampaign;

use App\Services\LoggerFactory;

class MonthlyRequestController extends Controller
{

    const RESULTS_PER_PAGE = 20;
    protected $log;

    public function __construct(LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/monthly-campaign')->createLogger('monthly-request');
    }

    public function index(Request $request)
    {
        $monthlyRequests = MonthlyRequest::query()->orderBy('created_at', 'desc');

        if ($request->search) {
            $monthlyRequests = QueryBuilder::buildSearchQuery($monthlyRequests, $request->search, MonthlyRequest::$searchFields);
        }

        return $monthlyRequests->with('facility')->paginate(self::RESULTS_PER_PAGE);
    }

    public function getById(MonthlyRequest $monthlyRequest)
    {
        return $monthlyRequest->loadRelations();
    }

    public function create(Request $request, Facility $facility, User $user)
    {
        $this->setDecryptedCard($request);

        if (!$facility->accept_cc) {
            throw new ApiGenericException("Facility does not accept credit cards");
        }

        $this->validate($request, array_merge(MonthlyRequest::$validParams, AuthorizeNetTransaction::$cardValidParams), MonthlyRequest::$validParamsMessages);

        $rate=$facility->monthly_rate;
        foreach($facility->facilityRate as $facilityRate){
            if($facilityRate->rate==$request->monthly_rate){
                $rate=$facilityRate->rate;
            }
        }
        
        // Create our monthly request object
        $monthlyRequest = new MonthlyRequest;
        $monthlyRequest->fill($request->all());
        $monthlyRequest->facility()->associate($facility);
        $monthlyRequest->user()->associate($user);
        //$monthlyRequest->monthly_rate = $facility->monthly_rate;
        
        //monthly campaign price
        if((isset($request->monthly_campaign)) && ($request->monthly_campaign==1)){
            $rate=$facility->monthly_campaign_price;
            $monthlyRequest->monthly_campaign = '1';
        }
        $monthlyRequest->monthly_rate = $rate;

        DB::transaction(
            function () use ($request, $monthlyRequest) {
                $monthlyRequest->save();
                $monthlyRequest = $this->createPaymentProfile($request, $monthlyRequest);
                $monthlyRequest->save();
            }
        );

        //update monthly campaign spaces
        if((isset($request->monthly_campaign)) && ($request->monthly_campaign==1)){
            $monthlyCampaign = MonthlyCampaign::where('facility_id','=',$facility->id)->first();
            if($monthlyCampaign){
                $monthlyCampaign->used_availability = $monthlyCampaign->used_availability + 1;
                $monthlyCampaign->save();
                $this->log->info("[$user->id]MonthlyRequestData: ". json_encode($request->all()));
            }
        }
        
        //end


        $monthlyRequest->sendAdminEmail()->sendSubmittedEmail();

        return $monthlyRequest->loadRelations();
    }

    public function setDecryptedCard(Request $request)
    {
        if (isset($request->payment_profile_id) && $request->payment_profile_id != "") {
            return;
        }
        $key = env('PCI_ENCRYPTION_KEY');
        $mc = new MagicCrypt($key, 256);
        $decryptedNonce = $mc->decrypt($request->nonce);
        $cardData = explode(':', $decryptedNonce);
        $request->request->add(
            [
            'name_on_card' => $cardData[0],
            'card_number' => $cardData[1],
            'expiration_date' => $cardData[2],
            'security_code' => $cardData[3]
            ]
        );

        // $this->request = $request;
    }

    protected function createPaymentProfile(Request $request, MonthlyRequest $monthlyRequest)
    {
        // Set up our Authorize.Net CIM helper class
        $authorizeNet = (new AuthorizeNetMonthlyRequest())
            ->setMonthlyRequest($monthlyRequest)
            ->unsetPaymentProfile()
            ->setCreditCard($request->card_number, $request->expiration_date, $request->security_code)
            ->setBillingAddress(
                [
                'first_name' => $monthlyRequest->first_name,
                'last_name' => $monthlyRequest->last_name
                ]
            );

        // Validate their credit card manually
        $authorizeNet->validateCard("Authorization test for monthly request {$request->email}");

        $response = MonthlyRequestCim::where('monthly_request_id', $monthlyRequest->id)->first()
            ? $authorizeNet->createPaymentProfile()->executePaymentProfileRequest()
            : $authorizeNet->createCustomerProfile()->executeCustomerProfileRequest();

        $payment = PaymentProfile::where('payment_profile', $response['payment_profile_id'])->first();

        // Associate the payment profile with the user
        $monthlyRequest->paymentProfile()->associate($payment);

        $monthlyRequest->payment_type = CreditCard::validCreditCard($request->card_number)['type'];
        $monthlyRequest->payment_last_four = substr($request->card_number, -4);

        return $monthlyRequest->loadRelations();
    }

    public function update(Request $request, MonthlyRequest $monthlyRequest)
    {
        $monthlyRequest->fill($request->all());

        if ($request->monthly_rate) {
            $monthlyRequest->monthly_rate = $request->monthly_rate;
        }

        $monthlyRequest->save();

        if ($request->card_number) {
            DB::transaction(
                function () use ($request, $monthlyRequest) {
                    $monthlyRequest = $this->createPaymentProfile($request, $monthlyRequest);
                    $monthlyRequest->save();
                }
            );
        }

        return $monthlyRequest->loadRelations();
    }

    public function charge(MonthlyRequest $monthlyRequest)
    {
        if ($monthlyRequest->transaction) {
            throw new ApiGenericException("Transaction has already been processed for this request.");
        }

        $description = "Monthly Sale {$monthlyRequest->first_name} {$monthlyRequest->last_name}";
        $invoice = $monthlyRequest->facility->x999_account ?: '';


        // Charge their credit card - will throw exception if it fails
        $authorizeNet = new AuthorizeNetMonthlyRequest();
        $authorizeNet
            ->setMonthlyRequest($monthlyRequest)
            ->createTransaction($monthlyRequest->monthly_rate, $description, $invoice)
            ->executeTransaction();

        $monthlyRequest->transaction()->associate($authorizeNet->getTransaction());
        $monthlyRequest->approved_on = Carbon::now()->toDateTimeString();
        $monthlyRequest->save();

        return $monthlyRequest->loadRelations();
    }

    public function delete(MonthlyRequest $monthlyRequest)
    {
        $monthlyRequest->delete();
         if($monthlyRequest->monthly_campaign){
            $monthlyCampaign = MonthlyCampaign::where('facility_id','=',$monthlyRequest->facility_id)->first();
            if($monthlyCampaign){
                $monthlyCampaign->used_availability = $monthlyCampaign->used_availability - 1;
                $monthlyCampaign->save();
                $this->log->info("MonthlyCampaign used_availability decreases by 1 for facility: ". json_encode($monthlyRequest->facility_id));
            }
        }

        
        return $monthlyRequest;
    }
}
