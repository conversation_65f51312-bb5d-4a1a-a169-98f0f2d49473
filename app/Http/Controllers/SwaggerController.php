<?php
/*
 * -----------------------------------------------------------------------------
 * Copyright (c) 2024 ParkEngage Inc. All rights reserved.
 * -----------------------------------------------------------------------------
 *
 * This software is the confidential and proprietary information of
 * ParkEngage Inc. ("Confidential Information"). You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms
 * of the license agreement you entered into with ParkEngage Inc.
 *
 * This source code is a valuable trade secret of ParkEngage Inc.
 *
 * DISCLAIMER:
 * THIS SOFTWARE IS PROVIDED BY PARKENGAGE INC. "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL PARKENGAGE INC. OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are
 * those of the authors and should not be interpreted as representing official
 * policies, either expressed or implied, of ParkEngage Inc.
 * -----------------------------------------------------------------------------
 */

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;

use App\Models\OauthClient;
use App\Models\Ticket;
use App\Models\ParkEngage\TicketExtend;
use App\Models\ParkEngage\State;
use App\Models\User;
use App\Models\Facility;
use App\Models\PermitVehicle;
use App\Models\PermitRequest;
use App\Models\Reservation;
use App\Models\UserPass;
use App\Models\BlackListedVehicle;
use Carbon\Carbon;
use App\Http\Helpers\QueryBuilder;
use DateTime;
use App\Services\LoggerFactory;
use stdClass;
use App\Models\UsmCitation;
use App\Models\Event;
use App\Models\ParkEngage\EventCategoryEvent;
use App\Models\ParkEngage\FacilityConfiguration;
use App\Models\ParkEngage\MapcoQrcode;
use Illuminate\Support\Facades\DB;


class SwaggerController extends Controller
{
    const NO_DATA = [];
    const RevpassPartherID = ['372885', '215900'];
    protected $log;
    public function __construct(LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/swagger')->createLogger('swagger');
    }

    public function authenticatePartner(Request $request)
    {
        $oauthClient = OauthClient::where('secret', $request->secret)->first();
        return $oauthClient;
    }

    // Vijay Deplyed : 15-04-2024
    public function getTicketDetails(Request $request)
    {
        $this->log->info('log Request for getTicketDetails ' . date('Y-m-d H:i:s'));
        $this->log->info('remote IP ' . $request->ip());
        $this->log->info('Secret ' . $request->secret);
        $this->log->info(json_encode($request->all()));

        $currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
        $oauthClient = OauthClient::where('secret', $request->secret)->first();

        if (is_null($oauthClient)) {
            throw new ApiGenericException('Invalid Secret', 422);
        }

        if ($request->zone_id != '') {
            $facility = Facility::where("garage_code", $request->zone_id)->first();
            if (!$facility) {
                throw new ApiGenericException('Invalid zone_id. Please enter valid zone_id.', 422);
            }
        }

        if (!isset($request->from_datetime) ||  $request->from_datetime == '') {
            throw new ApiGenericException('Invalid from datetime, Please enter valid from datetime.', 422);
        }
        if (!isset($request->to_datetime) ||  $request->to_datetime == '') {
            throw new ApiGenericException('Invalid to datetime, Please enter valid to datetime.', 422);
        }

        try {
            $fdate = Carbon::parse($request->from_datetime)->format('Y-m-d H:i:s');
            $todate = Carbon::parse($request->to_datetime)->format('Y-m-d H:i:s');


            $check_in_datetime = isset($request->from_datetime) ? date('Y-m-d H:i:s', strtotime($request->from_datetime)) : date('Y-m-d H:i:s', strtotime('last 1 hour'));
            $checkout_datetime = isset($request->to_datetime) ? date('Y-m-d H:i:s', strtotime($request->to_datetime)) : date('Y-m-d H:i:s', strtotime('now'));
        } catch (\Throwable $th) {
            throw new ApiGenericException('Please enter valid datetime format.', 422);
        }

        $zoneIdCondition = "";
        if (isset($request->zone_id) && !empty($request->zone_id)) {
            $zoneIdCondition = " and f.garage_code= '" . $request->zone_id . "'";
        }

        $ticketNumberCondition = "";
        if (isset($request->ticket_number) && !empty($request->ticket_number)) {
            $ticketNumberCondition = "and t.ticket_number='" . $request->ticket_number . "'";
        }

        $licensePlate = '';
        if (isset($request->license_plate) && !empty($request->license_plate)) {
            $licensePlate = "and t.license_plate='" . $request->license_plate . "'";
        }

        $query = "SELECT t.event_id, t.id, e.title as event_title, e.start_time as event_start_time, e.end_time as event_end_time, t.ticket_number, t.permit_request_id, t.user_pass_id, t.reservation_id, f.full_name, f.timezone, IF(f.is_gated_facility='1','gated', 'ungated') as is_gated_facility , f.garage_code as zone_id,  t.length as total_length, t.orignal_length, t.grand_total,t.parking_amount, (t.processing_fee + t.additional_fee) as processing_fee , t.surcharge_fee as `city_surcharge`, t.tax_fee,t.tax_fee, t.discount_amount, t.promocode, t.paid_amount as validate_amount, t.payment_date,
        uaff.name as paid_by_affiliate, 
        t.paid_remark,
            CASE t.paid_type 
                WHEN '9' THEN 'Ticket is not validated' 
                WHEN '1' THEN 'Affiliate validated hours'
                WHEN '2' THEN 'Affiliate validated partial amount'
                WHEN '3' THEN 'Affiliate validated percentage'
                WHEN '4' THEN 'Affiliate validated days'
                WHEN '0' THEN 'Affiliate validated full amount'
                ELSE 'Ticket is not validated' END as paid_type, 
            t.card_type,t.expiry,t.card_last_four, t.is_extended, IF(t.is_autopay='1','Active','InActive') as autopay, s.name as state_name, u.phone as user_mobile_number,CASE WHEN pv.license_plate_number is null THEN t.license_plate ELSE pv.license_plate_number END as vehicle_plate, pv.make as vehicle_make, pv.model as vehicle_model, pv.color as vehicle_color,
            t.check_in_datetime,  
            stop_parking_time as `end_parking_time`,
            estimated_checkout as `estimated_checkout_datetime`,
            IF(t.is_checkout = '1',t.checkout_datetime,'') as actual_checkout_datetime ,
            DATE_FORMAT(CONVERT_TZ(check_in_datetime, f.timezone, 'UTC'), '%m/%d/%Y %H:%i:%sZ') AS utc_check_in_datetime,
            DATE_FORMAT(CONVERT_TZ(stop_parking_time, f.timezone, 'UTC'), '%m/%d/%Y %H:%i:%sZ') AS utc_end_parking_time,
            DATE_FORMAT(CONVERT_TZ(estimated_checkout, f.timezone, 'UTC'), '%m/%d/%Y %H:%i:%sZ') AS utc_estimated_checkout_datetime,
            IF(t.is_checkout = '1',DATE_FORMAT(CONVERT_TZ(estimated_checkout, f.timezone, 'UTC'), '%m/%d/%Y %H:%i:%sZ'),'') AS utc_actual_checkout_datetime   
            FROM tickets as t 
            inner join facilities as f on f.id = t.facility_id  
            left join events as e on e.id = t.event_id 
            left join users as u on u.id = t.user_id 
            left join permit_vehicles as pv on pv.id = t.vehicle_id 
            left join states as s on s.id = pv.state_id 
            left join users as uaff on uaff.id = t.paid_by and  t.paid_by > 0
            WHERE t.deleted_at is null AND t.partner_id ='$oauthClient->partner_id' $zoneIdCondition  $ticketNumberCondition $licensePlate  and ((checkin_time >='$check_in_datetime' and checkin_time <='$checkout_datetime') OR (estimated_checkout >='$check_in_datetime' and estimated_checkout <='$checkout_datetime')) and is_checkin='1' order by t.id DESC ";

        $tickets = DB::select($query);
        $this->log->info('Log Response ' . $query);
        // $this->log->info('Log Response ' );and checkout_time >= '$currentTime'
        $this->log->info(json_encode($tickets));

        if (count($tickets) <= 0) {
            throw new ApiGenericException('No record found', 422);
        }

        foreach ($tickets as $key => $ticket) {
            // $state = State::find($ticket->state_id);
            // $affiliate = User::find($ticket->paid_by_affiliate);
            /// $ticket->state_name = isset($state->name) ? $state->name : '';
            // $ticket->paid_by_affiliate = isset($affiliate->name) ? $affiliate->name : '';
            // $ticket->utc_check_in_datetime = Carbon::parse($ticket->check_in_datetime, $ticket->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
            // $ticket->utc_actual_checkout_datetime = Carbon::parse($ticket->actual_checkout_datetime, $ticket->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');


            // if (!empty($ticket->actual_checkout_datetime)) {
            //     if (strtotime($ticket->actual_checkout_datetime) > strtotime('now')) {
            //         $ticket->actual_checkout_datetime = '';
            //     }
            // }
            // unset($ticket->state_id);
            // $requestData = DB::table('ticket_extends')->select('length', 'grand_total', 'checkin_time as check_in_datetime', 'checkout_time as estimated_checkout_datetime', 'tax_fee as tax', 'created_at')->where('ticket_id', $ticket->id)->first();
            // $requestData = DB::select("SELECT  length, grand_total, checkin_time as check_in_datetime, checkout_time as estimated_checkout_datetime,tax_fee as tax, created_at from ticket_extends where ticket_id ='$ticket->id' and length > 0 ")->first();


            $requestData = DB::table('tickets')->select('length', 'grand_total', 'checkin_time as check_in_datetime', 'checkout_time as estimated_checkout_datetime', DB::raw('(processing_fee + additional_fee) as processing_fee'), 'surcharge_fee as city_surcharge', 'tax_fee as tax', 'created_at')->where('id', $ticket->id)->first();


            $ticketsExtends = DB::select("SELECT length, grand_total, checkin_time as check_in_datetime, checkout_time as estimated_checkout_datetime, (processing_fee +additional_fee) as processing_fee, surcharge_fee as `city_surcharge`, tax_fee as tax, created_at from ticket_extends where ticket_id ='$ticket->id' and length > 0 LIMIT 50 OFFSET 0");
            $ticket->request_data = $requestData;

            if (isset($requestData) && count($requestData) > 0) {
                if (!in_array($oauthClient->partner_id, self::RevpassPartherID)) {
                    // Removes keys for partner specific
                    unset($requestData->processing_fee);
                    unset($requestData->city_surcharge);
                }
            }
            $ticket->ticket_extend = $ticketsExtends;

            if (isset($ticketsExtends) && count($ticketsExtends) > 0) {
                $tax_fee = $grandTotal = $parkingAmount = 0;
                $checkoutTime = 0;
                $extendLength = 0;
                foreach ($ticketsExtends as $key => $ticketExtend) {
                    $tax_fee        += sprintf("%.2f", $ticketExtend->tax);
                    $parkingAmount  += ($ticketExtend->grand_total - ($ticketExtend->processing_fee + $ticketExtend->tax + $ticketExtend->city_surcharge));
                    if (!in_array($oauthClient->partner_id, self::RevpassPartherID)) {
                        // Removes keys for partner specific
                        unset($ticketExtend->processing_fee);
                        unset($ticketExtend->city_surcharge);
                    }

                    $grandTotal     += $ticketExtend->grand_total;
                    $extendLength   += QueryBuilder::getLengthInMints($ticketExtend->length);
                    $ticketExtend->utc_check_in_datetime = Carbon::parse($ticketExtend->check_in_datetime, $ticket->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
                    $ticketExtend->utc_estimated_checkout_datetime = Carbon::parse($ticketExtend->estimated_checkout_datetime, $ticket->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
                }
                $ticket->tax_fee            += sprintf("%.2f", $tax_fee);
                $ticket->parking_amount     += sprintf("%.2f", $parkingAmount);
                $ticket->grand_total        += sprintf("%.2f", $grandTotal);
                $ticket->total_length = QueryBuilder::getLengthInHours(QueryBuilder::getLengthInMints($ticket->total_length) + $extendLength);
            }
            $ticket->tax_fee            = sprintf("%.2f", $ticket->tax_fee);
            $ticket->grand_total        = sprintf("%.2f", $ticket->grand_total);

            if (!in_array($oauthClient->partner_id, self::RevpassPartherID)) {
                // Removes keys for partner specific
                unset($ticket->city_surcharge);
            }
        }



        return $tickets;
    }

    // New One For Active Record
    // Deployed : on 29-04-2024 
    public function getActiveTicketDetails(Request $request)
    {
        $this->log->info('log Request for getActiveTicketDetails : ' . date('Y-m-d H:i:s'));
        $this->log->info('remote IP ' . $request->ip());
        $this->log->info('Secret ' . $request->secret);
        $this->log->info(json_encode($request->all()));
        $currentTime = Carbon::parse('now')->format('Y-m-d H:i:s');
        $oauthClient = OauthClient::where('secret', $request->secret)->first();
        if (is_null($oauthClient)) {
            throw new ApiGenericException('Invalid Secret', 422);
        }

        if ($request->zone_id != '') {
            $facility = Facility::where("garage_code", $request->zone_id)->first();
            if (!$facility) {
                throw new ApiGenericException('Invalid zone_id. Please enter valid zone_id.', 422);
            }
        }


        if (isset($request->to_datetime) &&  $request->to_datetime != '') {
            try {
                $check_in_datetime = isset($request->from_datetime) ? date('Y-m-d H:i:s', strtotime($request->from_datetime)) : date('Y-m-d H:i:s', strtotime('last 1 hour'));
                $checkout_datetime = isset($request->to_datetime) ? date('Y-m-d H:i:s', strtotime($request->to_datetime)) : date('Y-m-d H:i:s', strtotime('now'));
                $checkout_datetime = Carbon::parse($request->to_datetime)->format('Y-m-d H:i:s');
            } catch (\Throwable $th) {
                throw new ApiGenericException('Please enter valid datetime format.', 422);
            }
        }

        $checkoutDate = '';
        if (!empty($checkout_datetime)) {
            $checkoutDate = " and ((checkout_time <= '$checkout_datetime' ) OR (checkout_time <= '$checkout_datetime' ))";
        }

        $zoneIdCondition = "";
        if (isset($request->zone_id) && !empty($request->zone_id)) {
            $zoneIdCondition = " and f.garage_code= '" . $request->zone_id . "'";
        }
        $facilityIdCondition = "";
        if (isset($request->facility_id) && !empty($request->facility_id)) {
            $facilityIdCondition = "and f.id='" . $request->facility_id . "'";
        }

        $ticketNumberCondition = "";
        if (isset($request->ticket_number) && !empty($request->ticket_number)) {
            $ticketNumberCondition = "and t.ticket_number='" . $request->ticket_number . "'";
        }

        $query = "SELECT t.id, t.ticket_number,t.permit_request_id,t.user_pass_id,t.reservation_id,f.full_name,f.timezone, IF(f.is_gated_facility='1','gated', 'ungated') as is_gated_facility , f.garage_code as zone_id, t.check_in_datetime, t.processing_fee,t.checkout_datetime as actual_checkout_datetime ,t.length as total_length, t.orignal_length, t.grand_total, (t.processing_fee + t.additional_fee) as processing_fee , t.surcharge_fee as `city_surcharge`,t.tax_fee,t.discount_amount, t.promocode,t.paid_amount as validate_amount, t.payment_date, t.paid_by as paid_by_affiliate,t.paid_remark,
            CASE t.paid_type 
                WHEN '9' THEN 'Ticket is not validated' 
                WHEN '1' THEN 'Affiliate validated hours'
                WHEN '2' THEN 'Affiliate validated partial amount'
                WHEN '3' THEN 'Affiliate validated percentage'
                WHEN '4' THEN 'Affiliate validated days'
                WHEN '0' THEN 'Affiliate validated full amount'
                ELSE 'Ticket is not validated' END as paid_type, 
                t.card_type,t.expiry,t.card_last_four, t.is_extended, IF(t.is_autopay='1','Active','InActive') as autopay, s.name as state_name, u.phone as user_mobile_number, pv.license_plate_number as vehicle_plate, pv.make as vehicle_make, pv.model as vehicle_model, pv.color as vehicle_color   
            FROM tickets as t 
            inner join facilities as f on f.id = t.facility_id  
            inner join users as u on u.id = t.user_id 
            left join permit_vehicles as pv on pv.id = t.vehicle_id 
            left join states as s on s.id = pv.state_id 
            WHERE t.deleted_at is null AND t.partner_id ='$oauthClient->partner_id' $zoneIdCondition  $ticketNumberCondition and checkout_time >= '$currentTime' $checkoutDate  order by t.id DESC ";
        $tickets = DB::select($query);
        $this->log->info('Log Response ' . $query);
        // $this->log->info('Log Response ' );
        $this->log->info(json_encode($tickets));

        if (count($tickets) <= 0) {
            // $response['errors']['message'] = 'No record found';
            // return response()->json($response, 200);
            throw new ApiGenericException('No record found', 422);
        }

        foreach ($tickets as $key => $ticket) {
            // $state = State::find($ticket->state_id);
            $affiliate = User::find($ticket->paid_by_affiliate);
            $ticket->utc_check_in_datetime = Carbon::parse($ticket->check_in_datetime, $ticket->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
            $ticket->utc_actual_checkout_datetime = Carbon::parse($ticket->actual_checkout_datetime, $ticket->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
            /// $ticket->state_name = isset($state->name) ? $state->name : '';
            $ticket->paid_by_affiliate = isset($affiliate->name) ? $affiliate->name : '';
            if (!empty($ticket->actual_checkout_datetime)) {
                if (strtotime($ticket->actual_checkout_datetime) > strtotime('now')) {
                    $ticket->actual_checkout_datetime = '';
                }
            }
            unset($ticket->state_id);
            // $requestData = DB::table('ticket_extends')->select('length', 'grand_total', 'checkin_time as check_in_datetime', 'checkout_time as estimated_checkout_datetime', 'tax_fee as tax', 'created_at')->where('ticket_id', $ticket->id)->first();
            $requestData = DB::table('tickets')->select('length', 'grand_total', 'checkin_time as check_in_datetime', 'checkout_time as estimated_checkout_datetime', DB::raw('(processing_fee + additional_fee) as processing_fee'), 'surcharge_fee as city_surcharge', 'tax_fee as tax', 'created_at')->where('id', $ticket->id)->first();
            // $requestData = DB::select("SELECT  length, grand_total, checkin_time as check_in_datetime, checkout_time as estimated_checkout_datetime,tax_fee as tax, created_at from ticket_extends where ticket_id ='$ticket->id' and length > 0 ")->first();
            $ticketsExtends = DB::select("SELECT length, grand_total, checkin_time as check_in_datetime, checkout_time as estimated_checkout_datetime, (processing_fee +additional_fee) as processing_fee, surcharge_fee as `city_surcharge`, tax_fee as tax, created_at from ticket_extends where ticket_id ='$ticket->id' and length > 0 LIMIT 50 OFFSET 0");
            $ticket->request_data = $requestData;

            if (isset($requestData) && count($requestData) > 0) {
                if (!in_array($oauthClient->partner_id, self::RevpassPartherID)) {
                    // Removes keys for partner specific
                    unset($requestData->processing_fee);
                    unset($requestData->city_surcharge);
                }
            }

            $ticket->ticket_extend = $ticketsExtends;

            if (isset($ticketsExtends) && count($ticketsExtends) > 0) {
                $grandTotal = 0;
                $checkoutTime = 0;
                $extendLength = 0;
                foreach ($ticketsExtends as $key => $ticketExtend) {
                    if (isset($ticketExtend) && count($ticketExtend) > 0) {
                        if (!in_array($oauthClient->partner_id, self::RevpassPartherID)) {
                            // Removes keys for partner specific
                            unset($ticketExtend->processing_fee);
                            unset($ticketExtend->city_surcharge);
                        }
                    }

                    $grandTotal += $ticketExtend->grand_total;
                    $extendLength += QueryBuilder::getLengthInMints($ticketExtend->length);
                }
                $ticket->grand_total += sprintf("%.2f", $grandTotal);
                $ticket->total_length = QueryBuilder::getLengthInHours(QueryBuilder::getLengthInMints($ticket->total_length) + $extendLength);
                $ticketExtend->utc_check_in_datetime = Carbon::parse($ticketExtend->check_in_datetime, $ticket->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
                $ticketExtend->utc_estimated_checkout_datetime = Carbon::parse($ticketExtend->estimated_checkout_datetime, $ticket->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
            }
            $ticket->grand_total = sprintf("%.2f", $ticket->grand_total);
            if (!in_array($oauthClient->partner_id, self::RevpassPartherID)) {
                // Removes keys for partner specific
                unset($ticket->city_surcharge);
            }
        }

        return $tickets;
    }

    public function graceCheckinDays($facilityId)
    {
        $graceCheckinDays = 0;
        if (!is_null($facilityId)) {
            $facility = Facility::with('facilityConfiguration')->where("garage_code", $facilityId)->first();
            if (!$facility) {
                throw new ApiGenericException('Invalid zone_id. Please enter valid zone_id.', 422);
            }
            $graceCheckinDays = $facility->facilityConfiguration()->value('permit_checkin_grace_days');
        }
        return $graceCheckinDays;
    }

    // Lokesh:
    public function getPermitDetails(Request $request)
    {
        $this->log->info('getPermitDetails Request', [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $request->ip(),
            'secret' => $request->secret,
            'input' => $request->all()
        ]);

        $oauthClient = OauthClient::where('secret', $request->secret)->firstOrFail();

        // Date filters
        $fromDate = $request->from_datetime ? str_replace('T', ' ', $request->from_datetime) : null;
        $toDate = $request->to_datetime ? str_replace('T', ' ', $request->to_datetime) : null;


        $checkIn = $fromDate ? date('Y-m-d 00:00:00', strtotime($fromDate)) : date('Y-m-d 00:00:00', strtotime('-1 hour'));
        $checkOut = $toDate ? date('Y-m-d H:i:s', strtotime($toDate)) : date('Y-m-d H:i:s');


        $partnerId = $oauthClient->partner_id;
        $accountNumber = isset($request->account_number) && !empty($request->account_number) ? $request->account_number : NULL;
        $facilityId = isset($request->zone_id) && !empty($request->zone_id) ? $request->zone_id : null;
        $licensePlate = isset($request->license_plate) && !empty($request->license_plate) ? $request->license_plate : null;
        $status = isset($request->status) && !empty($request->status) ? $request->status : null;
        $from = $checkIn;
        $to = $checkOut;

        $graceCheckinDays = $this->graceCheckinDays($facilityId);

        $permits = DB::select('CALL sp_get_permit_details(?, ?, ?, ?, ?, ?, ?)', [
            $partnerId,
            $facilityId,
            $accountNumber,
            $licensePlate,
            $from,
            $to,
            $status
        ]);

        if (empty($permits)) {
            return response()->json([
                'status' => 200,
                'data' => null,
                'errors' => [
                    'message' => 'No record found'
                ]
            ]);
        }

        foreach ($permits as &$permit) {
            if (!empty($permit->permit_time_restriction)) {
                $permit->permit_time_restriction = json_decode($permit->permit_time_restriction);
            } else {
                $permit->permit_time_restriction = [];
            }

            // Convert license plate JSON string or array to comma-separated string
            if (!empty($permit->license_plate)) {
                // $permit->license_plate = explode(",", $permit->license_plate);
                $plates = json_decode($permit->license_plate, true);
                $licenseData = [];

                foreach ($plates as $plate) {
                    $licenseData[] = [
                        'license_plate' => $plate['license_plate'] ?? null,
                        'created_at'    => $plate['created_at'] ?? null,
                        'updated_at'    => $plate['updated_at'] ?? null,
                        'deleted_at'    => $plate['deleted_at'] ?? null,
                    ];
                }

                $permit->license_plate = $licenseData;
            } else {
                $permit->license_plate = [];
            }

            if ($graceCheckinDays == 0) {
                $graceCheckinDays = $this->graceCheckinDays($permit->garage_code);
            }

            if ($graceCheckinDays > 0 && !$permit->cancelled_at) {
                $endDate = Carbon::parse($permit->end_date)->endOfDay();
                $permit->grace_time = $endDate->copy()->addDays($graceCheckinDays)->format("m/d/Y H:i:s");
                $permit->utc_grace_time = $endDate->copy()->addDays($graceCheckinDays)->format("m/d/Y H:i:s\Z");
            }
        }

        return $permits;
    }
    


    public function getPermitDetailsV1(Request $request)
    {
        $this->log->info('getPermitDetails Request', [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $request->ip(),
            'secret' => $request->secret,
            'input' => $request->all()
        ]);

        $oauthClient = OauthClient::where('secret', $request->secret)->firstOrFail();

        // Date filters
        $fromDate = $request->from_datetime ? str_replace('T', ' ', $request->from_datetime) : null;
        $toDate = $request->to_datetime ? str_replace('T', ' ', $request->to_datetime) : null;


        $checkIn = $fromDate ? date('Y-m-d 00:00:00', strtotime($fromDate)) : date('Y-m-d 00:00:00', strtotime('-1 hour'));
        $checkOut = $toDate ? date('Y-m-d H:i:s', strtotime($toDate)) : date('Y-m-d H:i:s');


        $partnerId = $oauthClient->partner_id;
        $accountNumber = isset($request->account_number) && !empty($request->account_number) ? $request->account_number : NULL;
        $facilityId = isset($request->zone_id) && !empty($request->zone_id) ? $request->zone_id : null;
        $licensePlate = isset($request->license_plate) && !empty($request->license_plate) ? $request->license_plate : null;
        $status = isset($request->status) && !empty($request->status) ? $request->status : null;
        $from = $checkIn;
        $to = $checkOut;

        $graceCheckinDays = $this->graceCheckinDays($facilityId);

        $permits = DB::select('CALL sp_get_permit_details(?, ?, ?, ?, ?, ?, ?)', [
            $partnerId,
            $facilityId,
            $accountNumber,
            $licensePlate,
            $from,
            $to,
            $status
        ]);

        if (empty($permits)) {
            return response()->json([
                'status' => 200,
                'data' => null,
                'errors' => [
                    'message' => 'No record found'
                ]
            ]);
        }

        foreach ($permits as &$permit) {
            if (!empty($permit->permit_time_restriction)) {
                $permit->permit_time_restriction = json_decode($permit->permit_time_restriction);
            } else {
                $permit->permit_time_restriction = [];
            }

            // Convert license plate JSON string or array to comma-separated string
            if (!empty($permit->license_plate)) {
                $plates = json_decode($permit->license_plate, true);
                $onlyPlates = [];
                
                if (is_array($plates)) {
                    foreach ($plates as $plate) {
                        // Only include plates where deleted_at is null
                        $deletedAt = array_key_exists('deleted_at', $plate) ? $plate['deleted_at'] : null;
                        if (is_null($deletedAt) || $deletedAt === '') {
                            $onlyPlates[] = isset($plate['license_plate']) ? $plate['license_plate'] : null;
                        }
                    }
                }
                $permit->license_plate = $onlyPlates;
            } else {
                $permit->license_plate = [];
            }

            if ($graceCheckinDays == 0) {
                $graceCheckinDays = $this->graceCheckinDays($permit->garage_code);
            }

            if ($graceCheckinDays > 0 && !$permit->cancelled_at) {
                $endDate = Carbon::parse($permit->end_date)->endOfDay();
                $permit->grace_time = $endDate->copy()->addDays($graceCheckinDays)->format("m/d/Y H:i:s");
                $permit->utc_grace_time = $endDate->copy()->addDays($graceCheckinDays)->format("m/d/Y H:i:s\Z");
            }
        }

        return $permits;
    }


    public function getPermitDetailsV2(Request $request)
    {
        $this->log->info('getPermitDetails Request', [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $request->ip(),
            'secret' => $request->secret,
            'input' => $request->all()
        ]);

        $oauthClient = OauthClient::where('secret', $request->secret)->firstOrFail();

        // Date filters
        $fromDate = $request->from_datetime ? str_replace('T', ' ', $request->from_datetime) : null;
        $toDate = $request->to_datetime ? str_replace('T', ' ', $request->to_datetime) : null;


        $checkIn = $fromDate ? date('Y-m-d 00:00:00', strtotime($fromDate)) : date('Y-m-d 00:00:00', strtotime('-1 hour'));
        $checkOut = $toDate ? date('Y-m-d H:i:s', strtotime($toDate)) : date('Y-m-d H:i:s');


        $partnerId = $oauthClient->partner_id;
        $accountNumber = isset($request->account_number) && !empty($request->account_number) ? $request->account_number : NULL;
        $facilityId = isset($request->zone_id) && !empty($request->zone_id) ? $request->zone_id : null;
        $licensePlate = isset($request->license_plate) && !empty($request->license_plate) ? $request->license_plate : null;
        $status = isset($request->status) && !empty($request->status) ? $request->status : null;
        $from = $checkIn;
        $to = $checkOut;

        $graceCheckinDays = $this->graceCheckinDays($facilityId);

        $permits = DB::select('CALL sp_get_permit_details(?, ?, ?, ?, ?, ?, ?)', [
            $partnerId,
            $facilityId,
            $accountNumber,
            $licensePlate,
            $from,
            $to,
            $status
        ]);

        if (empty($permits)) {
            return response()->json([
                'status' => 200,
                'data' => null,
                'errors' => [
                    'message' => 'No record found'
                ]
            ]);
        }

        foreach ($permits as &$permit) {
            if (!empty($permit->permit_time_restriction)) {
                $permit->permit_time_restriction = json_decode($permit->permit_time_restriction);
            } else {
                $permit->permit_time_restriction = [];
            }

            // Convert license plate JSON string or array to comma-separated string
            if (!empty($permit->license_plate)) {
                // $permit->license_plate = explode(",", $permit->license_plate);
                $plates = json_decode($permit->license_plate, true);
                $licenseData = [];

                foreach ($plates as $plate) {
                    $licenseData[] = [
                        'plate' => $plate['license_plate'] ?? null,
                        'created_at'    => $plate['created_at'] ?? null,
                        'updated_at'    => $plate['updated_at'] ?? null,
                        'deleted_at'    => $plate['deleted_at'] ?? null,
                    ];
                }

                $permit->license_plate = $licenseData;
            } else {
                $permit->license_plate = [];
            }

            if ($graceCheckinDays == 0) {
                $graceCheckinDays = $this->graceCheckinDays($permit->garage_code);
            }

            if ($graceCheckinDays > 0 && !$permit->cancelled_at) {
                $endDate = Carbon::parse($permit->end_date)->endOfDay();
                $permit->grace_time = $endDate->copy()->addDays($graceCheckinDays)->format("m/d/Y H:i:s");
                $permit->utc_grace_time = $endDate->copy()->addDays($graceCheckinDays)->format("m/d/Y H:i:s\Z");
            }
        }

        return $permits;
    }

    #KT | Get Permit Details Api
    # Update on : 29-05-2025
    // public function getPermitDetails(Request $request)
    // {
    //     $this->log->info('getPermitDetails log Request ' . date('Y-m-d H:i:s'));
    //     $this->log->info('remote IP ' . $request->ip());
    //     $this->log->info('Secret ' . $request->secret);
    //     $this->log->info(json_encode($request->all()));

    //     $oauthClient = OauthClient::where('secret', $request->secret)->first();
    //     if (is_null($oauthClient)) {
    //         throw new ApiGenericException('Invalid Secret', 422);
    //     }

    //     if ($request->zone_id != '') {
    //         $facility = Facility::where("garage_code", $request->zone_id)->first();
    //         if (!$facility) {
    //             throw new ApiGenericException('Invalid zone_id. Please enter valid zone_id.', 422);
    //         }
    //     }

    //     #KT: PIMS-12843 | Start
    //     #Change: 14-05-2025 for the filter status
    //     $statusMap = ['active' => 'active', 'expired' => 'expired', 'cancelled' => 'cancelled'];
    //     $statusFilter = null;
    //     if ($request->has('status') && !empty($request->status)) {
    //         $inputStatus = strtolower($request->status);
    //         if (!array_key_exists($inputStatus, $statusMap)) {
    //             throw new ApiGenericException('Invalid status. Use "active", "expired", or "cancelled".', 422);
    //         }
    //         $statusFilter = $inputStatus;
    //     }

    //     $endTime = $request->to_datetime ? str_replace('T', ' ', $request->to_datetime) : null;
    //     $startTime = $request->from_datetime ? str_replace('T', ' ', $request->from_datetime) : null;

    //     // $check_in_datetime = $startTime ? date('Y-m-d H:i:s', strtotime($startTime)) : date('Y-m-d H:i:s', strtotime('last 1 hour'));
    //     $check_in_datetime = $startTime ? date('Y-m-d 00:00:00', strtotime($startTime)) : date('Y-m-d 00:00:00', strtotime('last 1 hour'));
    //     $checkout_datetime = $endTime ? date('Y-m-d H:i:s', strtotime($endTime)) : date('Y-m-d H:i:s', strtotime('now'));

    //     $currentDate = date('Y-m-d');

    //     $zoneIdCondition = $request->zone_id && !empty($request->zone_id)  ? 'and p.facility_id=' . $facility->id : '';
    //     $accountNumberCondition = $request->account_number && !empty($request->account_number) ? "and p.account_number='$request->account_number' " : '';


    //     $licensePlateCondition = "";
    //     if ($request->license_plate && !empty($request->license_plate)) {
    //         $getPermitData = PermitVehicle::where('license_plate_number', 'like', "%$request->license_plate%")->pluck('id');
    //         if ($getPermitData->count()) {
    //             $tsrId = implode(',', $getPermitData->toArray());
    //             $licensePlateCondition = "and pvm.permit_vehicle_id in ($tsrId) ";
    //         } else {
    //             $licensePlateCondition = "and pvm.permit_vehicle_id in (0) ";
    //         }
    //     }

    //     $statusCondition = "";
    //     // This condition to check for Active Permints with end is > to current time.
    //     if ($checkout_datetime >= $currentDate) {
    //         if ($statusFilter === 'active') {
    //             $statusCondition = "and p.status = 1 and DATE(p.desired_end_date) >= '$currentDate' and p.cancelled_at is null";
    //         } elseif ($statusFilter === 'expired') {
    //             $statusCondition = "and DATE(p.desired_end_date) < '$currentDate' and p.cancelled_at is null";
    //         } elseif ($statusFilter === 'cancelled') {
    //             $statusCondition = "and p.cancelled_at is not null";
    //         }
    //     } else {
    //         if ($statusFilter === 'active') {
    //             $statusCondition = "and p.status = 1 and DATE(p.desired_end_date) >= date('$checkout_datetime') and p.cancelled_at is null";
    //         } elseif ($statusFilter === 'expired') {
    //             $statusCondition = "and DATE(p.desired_end_date) < date('$checkout_datetime') and p.cancelled_at is null";
    //         } elseif ($statusFilter === 'cancelled') {
    //             $statusCondition = "and p.cancelled_at is not null";
    //         }
    //     }

    //     $dateCondition = "";
    //     //dd($request->all());
    //     if ($request->from_datetime || $request->to_datetime) {
    //         $requestArr = [
    //             'from_datetime' => $request->from_datetime ?? '',
    //             'to_datetime' => $request->to_datetime ?? ''
    //         ];
    //         $getArr = $this->dateTimeValidation($requestArr);
    //         if ($getArr['proceed'] != 1) {
    //             throw new ApiGenericException($getArr['mesg'], 422);
    //         }
    //         $dateCondition = "and ((p.desired_start_date >= '$check_in_datetime' and p.desired_start_date <= '$checkout_datetime') OR (p.desired_end_date >= '$check_in_datetime' and p.desired_end_date <= '$checkout_datetime') OR (p.desired_start_date <= '$check_in_datetime' and p.desired_end_date >= '$checkout_datetime'))";
    //     } elseif (!$request->has('status')) {
    //         throw new ApiGenericException('Either status or both from_datetime and to_datetime are required', 422);
    //     }

    //     $query = "SELECT f.full_name as garage_name,p.facility_id,f.garage_code,f.timezone,p.account_number as account_number,prd.name as permit_type ,prd.campus_id as permit_type_id,p.email, p.desired_start_date as start_date, p.desired_end_date as end_date,p.permit_rate_id, p.permit_rate as amount,f.full_name, p.cancelled_at,p.created_at,
    //         GROUP_CONCAT(DISTINCT pvm.permit_vehicle_id) as permit_vehicle_ids,
    //         p.status
    //         FROM permit_requests as p 
    //         inner join facilities as f on f.id = p.facility_id  
    //         inner join permit_vehicle_mapping as pvm on p.id = pvm.permit_request_id  
    //         left join permit_rates as pr on p.permit_rate_id = pr.id
    //         left join permit_rate_descriptions as prd on pr.permit_rate_description_id = prd.id
    //         WHERE p.deleted_at is null 
    //         and p.anet_transaction_id is not null 
    //         and p.partner_id ='$oauthClient->partner_id' 
    //         $statusCondition 
    //         $dateCondition 
    //         $zoneIdCondition 
    //         $accountNumberCondition 
    //         $licensePlateCondition 
    //         group by p.account_number, p.permit_rate_id 
    //         order by p.id DESC";

    //     $permits = DB::select($query);
    //     if (count($permits) <= 0) {
    //         throw new ApiGenericException('No record found', 200);
    //     }

    //     foreach ($permits as $permit) {
    //         $timezone = Facility::where('id', $permit->facility_id)->value('timezone');
    //         if (!$timezone) {
    //             throw new ApiGenericException('something went wrong, with timezone please try again later.  ', 500);
    //         }
    //         $graceCheckinDays = FacilityConfiguration::where('facility_id', $permit->facility_id)->value('permit_checkin_grace_days');
    //         $pv_arr = explode(',', $permit->permit_vehicle_ids);
    //         $permit->license_plate = PermitVehicle::whereIn('id', $pv_arr)->pluck('license_plate_number')->toArray();

    //         #KT 27-05-2025 | Update status display
    //         if ($permit->cancelled_at != '') {
    //             $permit->status = "Cancelled";
    //         } else {
    //             $desiredEndDate = Carbon::parse($permit->end_date);
    //             $graceEndDate = $desiredEndDate->copy();
    //             if ($graceCheckinDays > 0) {
    //                 $graceEndDate->addDays($graceCheckinDays);
    //             }
    //             $permit->status = Carbon::now()->gt($graceEndDate) ? "Expired" : "Active";
    //         }

    //         // Time restrictions
    //         if ($permit->permit_rate_id) {
    //             $criteria = DB::select("SELECT prc.label, prc.days, prc.entry_time_begin, prc.entry_time_end, prc.exit_time_begin, prc.exit_time_end 
    //                 from permit_rate_criterias as prc 
    //                 inner join permit_rate_criteria_mapping as prcm on prcm.permit_rate_criteria_id = prc.id 
    //                 where prcm.permit_rate_id = $permit->permit_rate_id 
    //                 and prc.deleted_at is null and prcm.deleted_at is null");
    //             foreach ($criteria as $c) {
    //                 $c->days = QueryBuilder::convertNumberToDays($c->days);
    //             }
    //             $permit->permit_time_restriction = $criteria;
    //         } else {
    //             $permit->permit_time_restriction = [];
    //         }

    //         // Timezone handling
    //         $permit_start_date = $permit->start_date . " 00:00:00";
    //         $permit_end_date = $permit->end_date . " 23:59:59";
    //         $timezone = Facility::where('id', $permit->facility_id)->value('timezone');

    //         // if ($graceCheckinDays > 0 && !$permit->cancelled_at) {
    //         //     $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $permit_end_date);
    //         //     $permit->grace_time = $endDate->copy()->addDays($graceCheckinDays)->format("m/d/Y H:i:s");
    //         //     $permit->utc_grace_time = $endDate->copy()->addDays($graceCheckinDays)->format("m/d/Y H:i:s\Z");
    //         // } else {
    //         //     $permit->grace_time = "";
    //         //     $permit->utc_grace_time = "";
    //         // }

    //         if ($graceCheckinDays > 0) {
    //             if ($permit->cancelled_at != '') {
    //                 $permit->grace_time = "";
    //                 $permit->utc_grace_time = "";
    //             } else {
    //                 $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $permit_end_date, $timezone);
    //                 $permit->grace_time = $endDate->copy()->addDays($graceCheckinDays)->setTimezone($timezone)->format("m/d/Y H:i:s");
    //                 $permit->utc_grace_time = $endDate->copy()->addDays($graceCheckinDays)->setTimezone('UTC')->format("m/d/Y H:i:s\Z");
    //             }
    //         }

    //         if ($timezone) {
    //             $permit->utc_start_date = Carbon::createFromFormat('Y-m-d H:i:s', $permit_start_date, $timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
    //             $permit->utc_end_date = Carbon::createFromFormat('Y-m-d H:i:s', $permit_end_date, $timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
    //         } else {
    //             $permit->utc_start_date = Carbon::parse($permit_start_date)->format('m/d/Y H:i:s\Z');
    //             $permit->utc_end_date = Carbon::parse($permit_end_date)->format('m/d/Y H:i:s\Z');
    //         }

    //         $permit->start_date = $permit_start_date;
    //         $permit->end_date = $permit_end_date;
    //         $permit->created_at = QueryBuilder::convertToUTC($permit->facility_id, $permit->created_at);

    //         unset($permit->permit_vehicle_ids, $permit->facility_id, $permit->timezone, $permit->permit_rate_id, $permit->full_name);
    //     }

    //     return $permits;
    // }

    public function getPermitActivityDetails(Request $request)
    {
        $this->log->info('getPermitActivityDetails log Request ' . date('Y-m-d H:i:s'));
        $this->log->info('remote IP ' . $request->ip());
        $this->log->info('Secret ' . $request->secret);
        $this->log->info(json_encode($request->all()));
        $oauthClient = OauthClient::where('secret', $request->secret)->first();
        if (is_null($oauthClient)) {
            throw new ApiGenericException('Invalid Secret', 422);
        }
        $facilityId = '';
        if ($request->zone_id != '') {
            $facility = Facility::where(["garage_code" => $request->zone_id, 'owner_id' => $oauthClient->partner_id])->first();
            if (!$facility) {
                throw new ApiGenericException('Invalid zone id', 422);
            }
            $facilityId = $facility->id;
        }
        $explodeEndTIme = explode(" ", $request->to_datetime);
        $countexplodeEndTIme = count($explodeEndTIme);
        if ($countexplodeEndTIme == 1) {
            $endTime = $explodeEndTIme[0] . ' 23:59:59';
        } else {
            $endTime = $request->to_datetime;
        }

        $explodeStartTime = explode(" ", $request->from_datetime);
        $countexplodeStartTime = count($explodeStartTime);
        if ($countexplodeStartTime == 1) {
            $StartTime = $explodeStartTime[0] . ' 00:00:00';
        } else {
            $StartTime = $request->from_datetime;
        }
        // dd($endTime);
        $check_in_datetime = isset($request->from_datetime) ? date('Y-m-d H:i:s', strtotime($StartTime)) : date('Y-m-d H:i:s', strtotime('last 1 hour'));
        $checkout_datetime = isset($request->to_datetime) ? date('Y-m-d H:i:s', strtotime($endTime)) : date('Y-m-d H:i:s', strtotime('now'));
        // dinamic condition for zone id ashutosh-18-08-2023
        $zoneIdCondition = "";
        $accountNumberCondition = "";
        $nameCondition = "";
        $emailCondition = "";
        $phoneCondition = "";
        $userCondition = "";
        if ($facilityId != '') {
            $zoneIdCondition = 'and t.facility_id=' . $facility->id;
        }
        $permitId = '';
        if (isset($request->account_number) && !empty($request->account_number)) {
            $permitRequest = PermitRequest::where("account_number", $request->account_number)->first();
            if (!$permitRequest) {
                throw new ApiGenericException('Invalid account number', 422);
            }
            $accountNumberCondition = "and t.permit_request_id='$permitRequest->id' ";
        }


        $query = "SELECT f.full_name as garage_name,f.timezone, t.ticket_number as ticket_id, t.checkin_time as start_date, t.checkout_time as end_date
            
            FROM permit_tickets as t 
            inner join facilities as f on f.id = t.facility_id  
            WHERE t.partner_id ='$oauthClient->partner_id' and (checkin_time <>'$check_in_datetime' OR checkout_time <> '$checkout_datetime' ) $zoneIdCondition $accountNumberCondition order by t.id DESC ";
        $tickets = DB::select($query);
        if (count($tickets) <= 0) {
            throw new ApiGenericException('No record found', 422);
        }
        foreach ($tickets as $key => $ticket) {

            date_default_timezone_set("UTC");
            $ticket->start_date = date("Y-m-d H:i:s", strtotime($ticket->start_date));
            $ticket->end_date = $ticket->end_date != '' ? date("Y-m-d H:i:s", strtotime($ticket->end_date)) : '';
            //unset($permit->permit_vehicle_id);
            //unset($permit->full_name);
        }
        $activity['tickets'] = $tickets;

        $query = "SELECT f.full_name as garage_name,f.timezone, t.citation_number as citation_id, t.checkin_time as start_date, t.checkout_time as end_date
            
            FROM ticket_citations as t 
            inner join facilities as f on f.id = t.facility_id  
            WHERE t.partner_id ='$oauthClient->partner_id' and (checkin_time <>'$check_in_datetime' OR checkout_time <> '$checkout_datetime' ) $zoneIdCondition $accountNumberCondition order by t.id DESC ";
        $citations = DB::select($query);
        if (count($citations) <= 0) {
            throw new ApiGenericException('No record found', 422);
        }
        foreach ($citations as $key => $citation) {

            date_default_timezone_set("UTC");
            $citation->start_date = date("Y-m-d H:i:s", strtotime($citation->start_date));
            $citation->end_date = $citation->end_date != '' ? date("Y-m-d H:i:s", strtotime($citation->end_date)) : '';
            //unset($permit->permit_vehicle_id);
            //unset($permit->full_name);
        }
        $activity['tickets'] = $tickets;
        $activity['citations'] = $citations;
        return $activity;
    }

    public function getPermitAccountDetails(Request $request)
    {
        $this->log->info('getPermitAccountDetails log Request ' . date('Y-m-d H:i:s'));
        $this->log->info('remote IP ' . $request->ip());
        $this->log->info('Secret ' . $request->secret);
        $this->log->info(json_encode($request->all()));

        $oauthClient = OauthClient::where('secret', $request->secret)->first();
        if (is_null($oauthClient)) {
            throw new ApiGenericException('Invalid Secret', 422);
        }

        if ($request->zone_id != '') {
            $facility = Facility::where("garage_code", $request->zone_id)->first();
            if (!$facility) {
                throw new ApiGenericException('Invalid zone_id. Please enter valid zone_id.', 422);
            }
        }
        if (isset($request->to_datetime) && !empty($request->to_datetime)) {
        } else {
            $this->log->info("PAD: Please provide the datetime");
            throw new ApiGenericException('Please provide to_datetime', 422);
        }

        if (isset($request->from_datetime) && !empty($request->from_datetime)) {
        } else {
            $this->log->info("PAD: Please provide the datetime");
            throw new ApiGenericException('Please provide from_datetime', 422);
        }

        $endTime = str_replace('T', ' ', $request->to_datetime);
        $StartTime = str_replace('T', ' ', $request->from_datetime);
        $check_in_datetime = isset($request->from_datetime) ? date('Y-m-d H:i:s', strtotime($StartTime)) : date('Y-m-d H:i:s', strtotime('last 1 hour'));
        $checkout_datetime = isset($request->to_datetime) ? date('Y-m-d H:i:s', strtotime($endTime)) : date('Y-m-d H:i:s', strtotime('now'));

        if (strtotime($checkout_datetime) < strtotime($check_in_datetime)) {
            throw new ApiGenericException('Please select `to datetime` greater or equal to `from datetime`', 422);
        }

        // dynamic condition for zone id ashutosh-18-08-2023
        $zoneIdCondition = "";
        $accountNumberCondition = "";
        $permitConfig  = new stdClass();
        $permitsData = [];

        $requestArr = ['from_datetime' => $request->from_datetime ?? '', 'to_datetime' => $request->to_datetime ?? ''];
        $getArr = $this->dateTimeValidation($requestArr);

        if ($getArr['proceed'] == 1) {

            if (isset($request->zone_id) && !empty($request->zone_id)) {
                $zoneIdCondition = 'and p.facility_id=' . $facility->id;
            }
            if (isset($request->account_number) && !empty($request->account_number)) {
                $accountNumberCondition = "and p.account_number='$request->account_number' ";
            }

            $licensePlateCondition = "";
            if (isset($request->license_plate) && !empty($request->license_plate)) {
                $tsrId = '';
                $getPermitData = PermitVehicle::where('license_plate_number', 'like', "%$request->license_plate%")->pluck('id');
                if ($getPermitData) {
                    $tsrId = implode(',', $getPermitData->toArray());
                }
                $licensePlateCondition = "and pvm.permit_vehicle_id in ('$tsrId') ";
            }


            $query = "SELECT p.id, p.facility_id, 
                    p.account_number as account_number,
                    prd.name as permit_type,
                    prd.campus_id as permit_type_id,
                    p.email, 
                    user_type_id as university_id,
                    DATE_FORMAT(CONVERT_TZ(p.desired_start_date, '+00:00', '+04:00'), '%m/%d/%YT%H:%i:%sZ') as start_date, 
                    DATE_FORMAT(CONVERT_TZ(CONCAT(p.desired_end_date, ' 23:59:59'), '+00:00', '+04:00'), '%m/%d/%YT%H:%i:%sZ') as end_date, 
                    p.permit_rate as amount,
                    p.permit_rate_id, 
                    hid_card_number as prox_card_no,  
                    p.name as account_name, 
                    p.cancelled_at,
                    f.full_name, 
                    p.created_at,
                    f.timezone,
                    GROUP_CONCAT(DISTINCT pvm.permit_vehicle_id) as permit_vehicle_ids
                FROM permit_requests as p 
                inner join facilities as f on f.id = p.facility_id  
                inner join permit_vehicle_mapping as pvm on p.id = pvm.permit_request_id  
                left join permit_rates as pr on p.permit_rate_id = pr.id
                left join permit_rate_descriptions as prd on pr.permit_rate_description_id  =  prd.id
                WHERE p.deleted_at is null and p.anet_transaction_id is not null and p.cancelled_at is null and  p.status = 1 and p.partner_id ='$oauthClient->partner_id' and ((desired_start_date >= '$check_in_datetime' and desired_start_date <= '$checkout_datetime')  OR (desired_end_date >= '$check_in_datetime' and desired_end_date <= '$checkout_datetime')) $zoneIdCondition $accountNumberCondition $licensePlateCondition group by account_number,p.permit_rate_id order by p.id DESC ";
            $permits = DB::select($query);
            if (count($permits) <= 0) {
                throw new ApiGenericException('No record found', 422);
            }

            /*  $sqlQueryPermitFacility = "SELECT id, full_name, garage_code, timezone FROM facilities WHERE owner_id IN ( $oauthClient->partner_id) AND deleted_at is null and active ='1' ";
            $permitFacilities = DB::select($sqlQueryPermitFacility);

            foreach ($permitFacilities as $key => $facility) {

                $sqlQueryPermitRats = "SELECT pr.id, pr.permit_rate_description_id, pr.name, rate, prd.name as permit_type, prd.campus_id as permit_type_id,prd.description
                FROM permit_rates as pr
                left join permit_rate_descriptions as prd ON prd.id = pr.permit_rate_description_id                
                WHERE facility_id IN ($facility->id) AND pr.active ='1' AND prd.active_status ='1' and prd.deleted_at is null ";
                $permitRates = DB::select($sqlQueryPermitRats);
                if (count($permitRates) > 0) {
                    foreach ($permitRates as $key => $permitRate) {
                        // $sqlQueryPermitType = "SELECT name as permit_type, description, label, campus_id as permit_type_id FROM permit_rate_descriptions WHERE id IN ($permitRate->permit_rate_description_id) AND active_status ='1' and deleted_at is null ";
                        // $permitType = DB::select($sqlQueryPermitType);
                        // $permitRate->permitType = $permitType;

                        // Permit Criterias 
                        $sqlQueryPermitCriteria = "SELECT prc.label,prc.days,prc.entry_time_begin, prc.entry_time_end,prc.exit_time_begin,prc.exit_time_end from permit_rate_criterias as prc inner join permit_rate_criteria_mapping as prcm  on prcm.permit_rate_criteria_id = prc.id 
                        where prcm.permit_rate_id IN ($permitRate->id)  and prc.deleted_at is null and prcm.deleted_at is null";

                        $permitCateria = DB::select($sqlQueryPermitCriteria);
                        if (count($permitCateria) > 0) {
                            foreach ($permitCateria as $pc_key => $pc_val) {
                                $pc_val->days = QueryBuilder::convertNumberToDays($pc_val->days);
                            }
                            $permitRate->permitTimeRestriction = $permitCateria;
                        } else {
                            $permitRate->permitTimeRestriction = [];
                        }

                        unset($permitRate->id);
                        unset($permitRate->permit_rate_description_id);
                    }
                }

                // Service 
                $sqlQueryPermitServices = "SELECT ps.id, ps.permit_service_name,ps.permit_service_rate FROM permit_services as ps
                    inner join permit_services_facility_mapping as psfm ON  psfm.permit_service_id = ps.id WHERE psfm.facility_id IN ($facility->id) AND ps.is_status ='1' ";
                $permitServices = DB::select($sqlQueryPermitServices);
                foreach ($permitServices as $key => $service) {
                    // Permit Service Criterias 
                    $sqlQueryServicePermitCriteria = "SELECT prc.label,prc.days,prc.entry_time_begin, prc.entry_time_end,prc.exit_time_begin,prc.exit_time_end from permit_service_criterias as prc inner join permit_service_criteria_mapping as prcm  on prcm.permit_service_criteria_id = prc.id 
                  where prcm.permit_service_id IN ($service->id)  and prc.deleted_at is null and prcm.deleted_at is null";

                    $permitServiceCateria = DB::select($sqlQueryServicePermitCriteria);
                    if (count($permitServiceCateria) > 0) {
                        foreach ($permitServiceCateria as $pc_key => $pc_val) {
                            $pc_val->days = QueryBuilder::convertNumberToDays($pc_val->days);
                        }
                        $service->serviceTimeRestriction = $permitServiceCateria;
                    } else {
                        $service->serviceTimeRestriction = [];
                    }
                    unset($service->id);
                }
                $facility->permission_time_restrictions = array_merge($permitServices, $permitRates);
                // $facility->permitRates = $permitRates;

                // remove Ids 
                unset($facility->id);
            } */

            // return $permitFacilities;


            foreach ($permits as $key => $permit) {
                $typeAndServiceArray = [];
                // $permit->permitConfig = [];
                // 
                $pv_arr = explode(',', $permit->permit_vehicle_ids);
                // if(count($pv_arr) >1) {
                $permitVehicle = PermitVehicle::whereIn('id', $pv_arr)->pluck('license_plate_number')->toArray();
                //     dd($permitVehicle);
                // }


                if ($permit->cancelled_at != '') {
                    $permit->status = "Canceled";
                } else {
                    // $today = date("m/d/Y");
                    // $currentMonthLastDate= date("m/t/Y", strtotime($today));
                    // die(date("m/t/Y"));
                    if (strtotime($permit->end_date) >= strtotime('now')) {
                        $permit->status = "Active";
                    } else {
                        $permit->status = "InActive";
                        // $permit->status = "Expired";
                    }
                }

                if ($permitVehicle) {
                    $permit->license_plate = $permitVehicle;
                } else {
                    $permit->license_plate = [];
                }

                if (isset($permit->id) && !empty($permit->id)) {
                    $serviceSql = "SELECT ps.id, ps.permit_service_name as name, ps.permit_service_name as type, ps.permit_service_rate as rate
                        from permit_request_service_mapping as prsm 
                        inner join permit_services as ps  on prsm.permit_service_id = ps.id 
                        where prsm.permit_request_id IN ($permit->id) ";
                    $serviceData = DB::select($serviceSql);
                }

                if (isset($serviceData) && !empty($permit->id)) {
                    foreach ($serviceData as $key => $service) {
                        // Permit Service Criterias 
                        $sqlQueryServicePermitCriteria = "SELECT prc.label,prc.days,prc.entry_time_begin, prc.entry_time_end,prc.exit_time_begin,prc.exit_time_end, f.garage_code as locationId from permit_service_criterias as prc inner join permit_service_criteria_mapping as prcm  on prcm.permit_service_criteria_id = prc.id 
                        left join facilities as f  on prcm.facility_id = f.id 
                        where  prcm.facility_id IN (select id from facilities where owner_id IN ($oauthClient->partner_id) and active ='1' and deleted_at is null) and
                      prcm.permit_service_id IN ($service->id)  and prc.deleted_at is null and prcm.deleted_at is null";

                        $permitServiceCateria = DB::select($sqlQueryServicePermitCriteria);
                        if (count($permitServiceCateria) > 0) {
                            foreach ($permitServiceCateria as $pc_key => $pc_val) {
                                $pc_val->days = QueryBuilder::convertNumberToDays($pc_val->days);
                            }

                            $service->permission_time_restrictions = $permitServiceCateria;
                        } else {
                            $service->permission_time_restrictions = [];
                        }
                        array_push($typeAndServiceArray, $service);

                        unset($service->id);
                    }

                    $permit->permissions = $serviceData;
                }


                // $service->permission_time_restrictions = $permitServiceCateria;


                if (isset($permit->permit_rate_id) && !empty($permit->permit_rate_id)) {

                    $getPermitIdData = DB::table('permit_rates as pr1')
                        ->join('permit_rates as pr2', 'pr1.permit_rate_description_id', '=', 'pr2.permit_rate_description_id')
                        ->where('pr2.id', $permit->permit_rate_id)
                        ->pluck('pr1.id');

                    if ($getPermitIdData) {
                        $permitRatesId = implode(',', $getPermitIdData);
                    }

                    $permitCateriaData = DB::select("SELECT prc.label,prc.days,prc.entry_time_begin, prc.entry_time_end,prc.exit_time_begin,prc.exit_time_end , f.garage_code as locationId
                    from permit_rate_criterias as prc 
                    inner join permit_rate_criteria_mapping as prcm  on prcm.permit_rate_criteria_id = prc.id
                    left join facilities as f  on prcm.facility_id = f.id 
                    where  prcm.facility_id IN (select id from facilities where owner_id IN ($oauthClient->partner_id) and active ='1' and deleted_at is null) and  prcm.permit_rate_id IN ($permitRatesId) and prc.deleted_at is null and prcm.deleted_at is null");
                }
                #prcm.permit_rate_id IN ($permit->permit_rate_id)
                if (isset($permitCateriaData) && !empty($permit->permit_rate_id)) {
                    foreach ($permitCateriaData as $pc_key => $pc_val) {
                        $pc_val->days = QueryBuilder::convertNumberToDays($pc_val->days);
                    }
                    #dushyant 19-06-2024 if criteria not exit then add static all days
                    /* if(empty($permitCateriaData)){
                        $permitCateriaData = config('parkengage.All_Days_Criteria');
                    } */
                    #end dushyant 19-06-2024

                    //     $permit->permit_time_restriction = $permitCateriaData;
                } else {
                    #dushyant 19-06-2024 if criteria not exit then add static all days
                    /* if(empty($permitCateriaData)){
                        $permitCateriaData = config('parkengage.All_Days_Criteria');
                        $permit->permit_time_restriction = $permitCateriaData;
                    } */
                    #end dushyant 19-06-2024 
                    //     $permit->permit_time_restriction = [];
                }

                // make Permit type
                $permitType['name'] =    $permit->permit_type;
                $permitType['type'] =    $permit->permit_type;
                $permitType['rate'] =    $permit->amount;
                $permitType['permission_time_restrictions'] =   $permitCateriaData;

                array_push($typeAndServiceArray, $permitType);

                //$permit_start_date =  $permit->start_date . " 00:00:00";
                // $utc_start_time = QueryBuilder::convertToUTC($permit->facility_id, $permit_start_date);
                //$permit_end_date =  $permit->end_date . " 23:59:59";
                // $utc_end_time = QueryBuilder::convertToUTC($permit->facility_id, $permit_end_date);
                // $permit->start_date = $utc_start_time;
                // $permit->end_date = $utc_end_time;
                //$permit->start_date = $permit_start_date;
                //$permit->end_date = $permit_end_date;
                $utc_created_at = QueryBuilder::convertToUTC($permit->facility_id, $permit->created_at);
                $permit->created_at = $utc_created_at;
                unset($permit->permit_vehicle_ids, $permit->facility_id, $permit->created_at,  $permit->permit_rate_id, $permit->id);
                unset($permit->full_name);
                $permit->permissions = $typeAndServiceArray;
                // $permit->garageConfig = $permitFacilities;
            }


            // $permitsData['permits'] = $permits;
            // $permitsData['config'] = $permitFacilities;
            // return $permitsData;
            return $permits;
        } else {
            throw new ApiGenericException($getArr['mesg'], 422);
        }
    }


    public function getPassDetails(Request $request)
    {
        $this->log->info('getPassDetails log Request ' . date('Y-m-d H:i:s'));
        $this->log->info('remote IP ' . $request->ip());
        $this->log->info('Secret ' . $request->secret);
        $this->log->info(json_encode($request->all()));

        $oauthClient = OauthClient::where('secret', $request->secret)->first();
        if (is_null($oauthClient)) {
            throw new ApiGenericException('Invalid Secret', 422);
        }
        $facilityId = '';
        if ($request->zone_id != '') {
            $facility = Facility::where("garage_code", $request->zone_id)->first();
            if (!$facility) {
                throw new ApiGenericException('Invalid zone id', 422);
            }
            $facilityId = $facility->id;
        }
        $explodeEndTIme = explode(" ", $request->to_datetime);
        $countexplodeEndTIme = count($explodeEndTIme);
        if ($countexplodeEndTIme == 1) {
            $endTime = $explodeEndTIme[0] . ' 23:59:59';
        } else {
            $endTime = $request->to_datetime;
        }

        $explodeStartTime = explode(" ", $request->from_datetime);
        $countexplodeStartTime = count($explodeStartTime);
        if ($countexplodeStartTime == 1) {
            $StartTime = $explodeStartTime[0] . ' 00:00:00';
        } else {
            $StartTime = $request->from_datetime;
        }
        // dd($endTime);
        $check_in_datetime = isset($request->from_datetime) ? date('Y-m-d H:i:s', strtotime($StartTime)) : date('Y-m-d H:i:s', strtotime('last 1 hour'));
        $checkout_datetime = isset($request->to_datetime) ? date('Y-m-d H:i:s', strtotime($endTime)) : date('Y-m-d H:i:s', strtotime('now'));
        // dinamic condition for zone id ashutosh-18-08-2023
        $zoneIdCondition = "";
        $passIdCondition = "";
        if ($facilityId != '') {
            $zoneIdCondition = 'and p.facility_id=' . $facility->id;
        }
        $passId = '';
        if (isset($request->pass_number) && !empty($request->pass_number)) {
            $permitRequest = UserPass::where("pass_code", $request->pass_number)->first();
            if (!$permitRequest) {
                throw new ApiGenericException('Invalid pass code', 422);
            }
            $passIdCondition = "and p.id='$permitRequest->id' ";
        }

        #KT: 31-03-2025 | PIMS-13370
        $query = "SELECT f.full_name as garage_name, f.garage_code,f.timezone, p.phone as phone, p.email as email,p.pass_code as pass_number, p.total_days as total_usage, p.consume_days as total_consumption, p.remaining_days as remaining_pass, p.start_date as start_date, p.end_date as end_date,license_plate, pass_type, p.parking_amount, p.pass_amount, p.promocode, p.discount_amount, p.tax_fee, p.processing_fee, p.total, p.id as pass_id
            FROM user_passes as p 
            inner join facilities as f on f.id = p.facility_id
            WHERE p.deleted_at IS NULL AND p.partner_id ='$oauthClient->partner_id' and (date(start_date) >='$check_in_datetime' AND date(end_date) <= '$checkout_datetime' OR date(end_date) >= '$checkout_datetime') $zoneIdCondition $passIdCondition order by p.id DESC ";
        $passes = DB::select($query);
        //inner join users as u on u.id = p.user_id  

        if (count($passes) <= 0) {
            throw new ApiGenericException('No record found', 422);
        }
        foreach ($passes as $key => $pass) {
            $pass->pass_type = $pass->pass_type == '3' ? 'Hotel' : 'Standard';
            date_default_timezone_set("UTC");
            $pass->start_date = date("Y-m-d", strtotime($pass->start_date));
            $pass->end_date = $pass->end_date != '' ? date("Y-m-d", strtotime($pass->end_date)) : '';
            $pass->phone = $pass->phone != '' ? "+1" . $pass->phone : '';
            //unset($permit->permit_vehicle_id);
            //unset($permit->full_name);
            // Convert time in UTC : Update : 23-04-2025
            $pass->utc_start_date = Carbon::parse($pass->start_date, $pass->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
            $pass->utc_end_date = Carbon::parse($pass->end_date, $pass->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
            $qrcodes = MapcoQrcode::select('id', 'event_id', 'event_category_id')->where("user_pass_id", $pass->pass_id)->first();
            if ($qrcodes) {
                $eventLists = EventCategoryEvent::select("event_id")->where("event_category_id", $qrcodes->event_category_id)->get();
                if (count($eventLists) > 0) {
                    $newEvents = [];
                    foreach ($eventLists as $eventList) {
                        $event = Event::select("title", "parking_start_time", "parking_end_time", "start_time", "end_time", "base_event_hours as event_duration")
                            ->where("id", $eventList->event_id)
                            ->first();
                        $eventArray = $event->toArray(); // Convert model to array
                        $eventArray['parking_start_time'] = date("m/d/Y H:i:s", strtotime($eventArray['parking_start_time']));
                        $eventArray['parking_end_time'] = date("m/d/Y H:i:s", strtotime($eventArray['parking_end_time']));
                        $eventArray['start_time'] = date("m/d/Y H:i:s", strtotime($eventArray['start_time']));
                        $eventArray['end_time'] = date("m/d/Y H:i:s", strtotime($eventArray['end_time']));
                        unset($eventArray['event_length'], $eventArray['event_arrival'], $eventArray['image_url']); // Remove unwanted keys
                        $newEvents[] = $eventArray;
                    }
                }
                $pass->events = $newEvents;
            }
            unset($pass->pass_id);
        }
        return $passes;
    }


    public function getReservationDetails(Request $request)
    {
        $this->log->info('getReservationDetails log Request ' . date('Y-m-d H:i:s'));
        $this->log->info('remote IP ' . $request->ip());
        $this->log->info('Secret ' . $request->secret);
        $this->log->info(json_encode($request->all()));

        $oauthClient = OauthClient::where('secret', $request->secret)->first();
        if (is_null($oauthClient)) {
            throw new ApiGenericException('Invalid Secret', 422);
        }
        $facilityId = '';
        if ($request->zone_id != '') {
            $facility = Facility::where("garage_code", $request->zone_id)->first();
            if (!$facility) {
                throw new ApiGenericException('Invalid zone id', 422);
            }
            $facilityId = $facility->id;
        }
        $explodeEndTIme = explode(" ", $request->to_datetime);
        $countexplodeEndTIme = count($explodeEndTIme);
        if ($countexplodeEndTIme == 1) {
            $endTime = $explodeEndTIme[0] . ' 23:59:59';
        } else {
            $endTime = $request->to_datetime;
        }

        $explodeStartTime = explode(" ", $request->from_datetime);
        $countexplodeStartTime = count($explodeStartTime);
        if ($countexplodeStartTime == 1) {
            $StartTime = $explodeStartTime[0] . ' 00:00:00';
        } else {
            $StartTime = $request->from_datetime;
        }
        // dd($endTime);
        $check_in_datetime = isset($request->from_datetime) ? date('Y-m-d H:i:s', strtotime($StartTime)) : date('Y-m-d H:i:s', strtotime('last 1 hour'));
        $checkout_datetime = isset($request->to_datetime) ? date('Y-m-d H:i:s', strtotime($endTime)) : date('Y-m-d H:i:s', strtotime('now'));
        // dinamic condition for zone id ashutosh-18-08-2023
        $zoneIdCondition = "";
        $bookingIdCondition = "";
        if ($facilityId != '') {
            $zoneIdCondition = 'and r.facility_id=' . $facility->id;
        }
        $passId = '';
        if (isset($request->booking_number) && !empty($request->booking_number)) {
            $reservation = Reservation::where("ticketech_code", $request->booking_number)->first();
            if (!$reservation) {
                throw new ApiGenericException('Invalid booking number', 422);
            }
            $bookingIdCondition = "and r.id='$reservation->id' ";
        }
        // \DB::enableQueryLog();

        #KT: 31-03-2025 | PIMS-13370
        #VP: 02-05-2025
        $query = "SELECT 
                    f.full_name as garage_name, 
                    f.garage_code, 
                    f.timezone, 
                    r.email, 
                    r.phone,                     
                    r.length,
                    r.license_plate, 
                    r.ticketech_code as booking_number,                     
                    r.reservation_amount, 
                    r.parking_amount, 
                    r.tax_fee, 
                    r.processing_fee, 
                    r.promocode, 
                    r.discount,  
                    r.created_at,
                    r.start_timestamp as start_time,
                    r.cancelled_at,
                    e.title as `event_name`,
                    e.start_time as `event_start_time`,
                    e.end_time as `event_end_time`,
                    e.parking_start_time as `event_parking_start_time`,
                    e.parking_end_time as `event_parking_end_time`,
                    e.created_at as `event_created_at`  
            FROM reservations as r 
            INNER JOIN facilities as f ON f.id = r.facility_id
            LEFT JOIN events AS e ON e.id = r.event_id
            LEFT JOIN permit_vehicles as pv ON pv.id = r.vehicle_id
            WHERE r.deleted_at IS NULL AND r.partner_id ='$oauthClient->partner_id' and ( date(start_timestamp) >='$check_in_datetime' AND date(start_timestamp) <= '$checkout_datetime' OR date(start_timestamp) >= '$check_in_datetime' ) $zoneIdCondition $bookingIdCondition order by r.id DESC ";

        $reservations = DB::select($query);
        // print_r(DB::getQueryLog());

        if (count($reservations) <= 0) {
            throw new ApiGenericException('No record found', 422);
        }

        foreach ($reservations as $key => $reservation) {
            // date_default_timezone_set("UTC");
            $reservation->start_time = date("Y-m-d H:i:s", strtotime($reservation->start_time));
            $reservation->end_time = date("Y-m-d H:i:s", strtotime(Carbon::parse($reservation->start_time)->addHours($reservation->length)));

            // Convert time in UTC : Update : 23-04-2025
            $reservation->utc_start_time = Carbon::parse($reservation->start_time, $reservation->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
            $reservation->utc_end_time = Carbon::parse($reservation->end_time, $reservation->timezone)->setTimezone('UTC')->format('m/d/Y H:i:s\Z');
        }
        return $reservations;
    }



    public function getScannedVehiclesUserList(Request $request)
    {

        //  \DB::enableQueryLog();
        // dd($request->all());


        $this->log->info('log Request ' . date('Y-m-d H:i:s'));
        $this->log->info('remote IP ' . $request->ip());

        if ($request->header('X-ClientSecret') != '') {
            $request->merge(['secret' => $request->header('X-ClientSecret')]);
        }
        $this->log->info('Secret ' . $request->secret);
        $this->log->info(json_encode($request->all()));
        $oauthClient = OauthClient::where('secret', $request->secret)->first();
        if (is_null($oauthClient)) {
            throw new ApiGenericException('Invalid Secret', 422);
        }

        if ($request->zone_id != '') {
            $facility = Facility::where(["garage_code" => $request->zone_id, 'owner_id' => $oauthClient->partner_id])->first();
            if (!$facility) {
                throw new ApiGenericException('Invalid zone id', 422);
            }
        }


        $requestArr = ['from_datetime' => $request->from_datetime ?? '', 'to_datetime' => $request->to_datetime ?? ''];
        $getArr = $this->dateTimeValidation($requestArr);
        if ($getArr['proceed'] == 1) {

            $endTime = str_replace('T', ' ', $request->to_datetime);

            $StartTime = str_replace('T', ' ', $request->from_datetime);

            // dd($StartTime);
            $start_datetime = (isset($request->from_datetime) && !empty($request->from_datetime)) ? date('Y-m-d H:i:s', strtotime($StartTime)) : date('Y-m-d H:i:s', strtotime('last 1 day'));
            $end_datetime = (isset($request->to_datetime) && !empty($request->to_datetime)) ? date('Y-m-d H:i:s', strtotime($endTime)) : date('Y-m-d H:i:s', strtotime('now'));
            $partner_id = $oauthClient->partner_id;
            // $partner_id =  358811;
            // - License Plate, Time and date, lat, long, device name, and user name
            date_default_timezone_set("UTC");

            $query = "SELECT username AS `UserName`,facilities.full_name as facility_name,facilities.garage_code as garage_code ,lot.garage_code as lot_id,lot.full_name as lot_name,scaned_vehicles.license_plate as `LicensePlate`,scaned_vehicles.latitude as `Latitude`,scaned_vehicles.longitude  as `Longitude`,scaned_vehicles.device_name as `DeviceName`, DATE_FORMAT(scaned_vehicles.created_at, '%m/%d/%Y %H:%i:%s') as `CreatedDateAndTime` from scaned_vehicles
                left join facilities on facilities.id=scaned_vehicles.facility_id 
                left join facilities as lot  on lot.id=scaned_vehicles.lot_id 
                where scaned_vehicles.partner_id = '$partner_id'";

            $zoneIdCondition = "";
            if (isset($request->zone_id) && !empty($request->zone_id)) {
                $zoneIdCondition = "and facilities.garage_code='" . $request->zone_id . "'";
            }

            if (isset($start_datetime) && !empty($start_datetime)) {
                //  dd($start_datetime, $end_datetime);
                if ($request->from_datetime == $request->to_datetime) {
                    $fromTime = explode(" ", $start_datetime);
                    $toTime = explode(" ", $end_datetime);
                    if ($fromTime[1] == "00:00:00" && $toTime[1] == "00:00:00") {
                        $end_datetime = $toTime[0] . " 23:59:59";
                        $query .= " and scaned_vehicles.created_at >= '$start_datetime' and scaned_vehicles.created_at <= '$end_datetime'  $zoneIdCondition  order by scaned_vehicles.id desc";
                    } else {
                        $query .= " and scaned_vehicles.created_at = '$start_datetime'  $zoneIdCondition  order by scaned_vehicles.id desc";
                    }
                } else {
                    $query .= " and scaned_vehicles.created_at >= '$start_datetime' and scaned_vehicles.created_at <= '$end_datetime'  $zoneIdCondition  order by scaned_vehicles.id desc";
                }
            } else {
                $query .= " and scaned_vehicles.created_at <= '$end_datetime'  $zoneIdCondition  order by scaned_vehicles.id desc";
            }




            $users = \DB::select($query);
            // print_r(DB::getQueryLog());

            $this->log->info('Log Response ' . $request->secret);
            $this->log->info(json_encode($users));
            if (count($users) <= 0) {
                throw new ApiGenericException('No record found', 200);
            }
            return $users;
        } else {
            throw new ApiGenericException($getArr['mesg'], 422);
        }
    }

    public  function isValidDate($date)
    {
        // $date="2012-09-12";
        $format = 'm/d/Y H:i:s';
        $d = \DateTime::createFromFormat($format, $date);
        $check =  $d && $d->format($format) == $date;
        if ($check === false) {
            $format = 'n/j/Y H:i:s';
            $d = \DateTime::createFromFormat($format, $date);
            $check =  $d && $d->format($format) == $date;
        }
        return $check;
    }


    public function dateTimeValidation($request)
    {
        $proceed = 1;
        $mesg = '';
        // if (isset($request['from_datetime']) && !empty($request['from_datetime']) && isset($request['to_datetime']) && !empty($request['from_datetime'])) {
        if (isset($request['to_datetime']) && !empty($request['to_datetime'])) {
            // if (!($this->isValidDate(str_replace('T', ' ', $request['from_datetime'])))) {
            //     $proceed = 0;
            //     $mesg = "Please enter valid datetime format.";
            // }
            // if (!($this->isValidDate($request['to_datetime']))) {
            if (!($this->isValidDate(str_replace('T', ' ', $request['to_datetime'])))) {
                $proceed = 0;
                $mesg = "Please enter valid datetime format.";
            }

            // if ($proceed == 1) {
            //     $diff_date = strtotime($request['to_datetime']) - strtotime($request['from_datetime']);
            //     if ($diff_date < 0) {
            //         $proceed = 0;
            //         $mesg = "to_datetime cannot be less than the from_datetime.";
            //     }
            // }
        } else {
            $proceed = 0;
            $mesg = "Both from_datetime and from_datetime are required.";
        }

        if (isset($request['from_datetime']) && !empty($request['from_datetime'])) {
            if (!($this->isValidDate(str_replace('T', ' ', $request['from_datetime'])))) {
                $proceed = 0;
                $mesg = "Please enter valid datetime format.";
            }
        } else {
            $proceed = 0;
            $mesg = "Both from_datetime and from_datetime are required.";
        }

        // dd($mesg,$proceed);
        return ['proceed' => $proceed, 'mesg' => $mesg];
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if (($facility) && ($facility->timezone != '')) {
            date_default_timezone_set($facility->timezone);
        } else {
            if ($partnerTimezone) {
                if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }

    public function isValidUTCTimestamp($timestamp)
    {
        //validate timestamp utc
        $date = DateTime::createFromFormat('Y-m-d\TH:i:s\Z', $timestamp);
        if ($date && $date->format('Y-m-d\TH:i:s\Z') === $timestamp) {
            return true;
        }
        return false;
    }

    #KT 23-12-2024
    public function getWhitelistedVehicle(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $request->merge(['secret' => $request->header('X-ClientSecret')]);
        }
        $this->log->info('Secret ' . $request->secret);
        $this->log->info(json_encode($request->all()));
        $oauthClient = OauthClient::where('secret', $request->secret)->first();
        if (is_null($oauthClient)) {
            throw new ApiGenericException('Invalid Secret', 422);
        }

        $fromDate = date('Y-m-d', strtotime($request->from_date));
        $toDate = date('Y-m-d', strtotime($request->to_date));

        $vehicle = BlackListedVehicle::query()
            ->select(
                'blacklisted_vehicles.license_plate_number',
                'blacklisted_vehicles.vehicle_type',
                'mst_makes.name as make',
                'mst_models.name as model',
                'mst_colors.name as color',
                'states.name as state',
                'blacklisted_vehicles.created_at',
                'blacklisted_vehicles.updated_at'
            )
            ->leftJoin('users', 'blacklisted_vehicles.partner_id', '=', 'users.id')
            ->leftJoin('mst_makes', 'blacklisted_vehicles.make_id', '=', 'mst_makes.id')
            ->leftJoin('mst_models', 'blacklisted_vehicles.model_id', '=', 'mst_models.id')
            ->leftJoin('mst_colors', 'blacklisted_vehicles.color_id', '=', 'mst_colors.id')
            ->leftJoin('states', 'blacklisted_vehicles.state_id', '=', 'states.id')
            ->where('blacklisted_vehicles.plate_type', '=', 3)
            ->whereDate('blacklisted_vehicles.created_at', '>=', $fromDate)
            ->whereDate('blacklisted_vehicles.created_at', '<=', $toDate)
            ->orderBy('blacklisted_vehicles.id', 'DESC')
            ->get();

        if (!$vehicle) {
            return response()->json([
                'status' => 404,
                'data' => null,
                'message' => 'No vehicle found for the given criteria.',
            ], 404);
        }

        return  $vehicle;
    }

    public function storecitation(Request $request)
    {
        if ($request->header('X-ClientSecret') != '') {
            $request->merge(['secret' => $request->header('X-ClientSecret')]);
        }
        $this->log->info('Secret ' . $request->secret);
        $this->log->info(json_encode($request->all()));
        $oauthClient = OauthClient::where('secret', $request->secret)->first();
        if (is_null($oauthClient)) {
            throw new ApiGenericException('Invalid Secret', 422);
        }

        $data = $request->all();

        $citation = UsmCitation::create([
            'citation_number'         => $data['notice_number'],
            'issue_date'              => $data['issue_date'],
            // 'location_name'           => $data['location_name'],
            'license_plate'           => $data['license_plate'],
            // 'make'                    => $data['make'],
            // 'model'                   => $data['model'],
            // 'color'                   => $data['color'],
            // 'state'                   => $data['state'],
            // 'violation_details'       => $data['violation_details'],
            // 'penalty_amount'          => $data['penalty_amount'],
            // 'remarks'                 => $data['remarks'],
            // 'deadline_for_compliance' => $data['deadline_for_compliance'],
        ]);

        return $citation;
    }
}
