<?php
namespace App\Http\Controllers\GoogleWallet;

use App\Http\Controllers\Controller;

use Exception;
use Auth;
use Illuminate\Http\Request;
use App\Services\LoggerFactory;


use App\Models\User;
use App\Models\GoogleWallet\LoyaltyPass;

use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Exceptions\DuplicateEntryException;
use App\Exceptions\UserNotAuthorized;




/**
*   Loyalty Object Controller handling all requests for loyalty object
*/
class LoyaltyObjectController extends Controller
{


	public function __construct(LoggerFactory $logFactory)
    {
        $this->log = $logFactory->setPath('logs/loyalty/googlewallet/loyaltypass')->createLogger('loyalty');
    }
	

	public function addLoyaltyCard(Request $request) {
  
    $user = Auth::user();
        
		
		if (!$user) {
			throw new NotFoundException('Error Occurred, No User Found with this User Id.');
		}
	
        // 2. Check if user exists or not
	  $loyaltypass = LoyaltyPass::where('loyalty_object_id',$request->loyalty_object_id)->first();
                      
		if (isset($loyaltypass) && $loyaltypass) {
			throw new DuplicateEntryException('Loyalty Object is already associated with a user');
		}
      
    try{
        $loyaltypass = new LoyaltyPass();
		    $loyaltypass->loyalty_object_id = $request->loyalty_object_id;
               //////var_dump($user->id); die("hello");
        $loyaltypass->user_id = $user->id;
        $loyaltypass->location = $request->url;
                
		    $loyaltypass->save();
      } catch (Exception $e){
                
         throw new ApiGenericException('There was some error adding loyalty card', 422);
      } 
      return $loyaltypass;
                 
 
	}
        public function getLoyaltyObject(Request $request){
           $user = Auth::user();
           $loyalty = new \LoyaltyObject();
           $service = $loyalty->getWalletLoyaltyObject();
           $loyaltypass = LoyaltyPass::where('loyalty_object_id', $request->loyalty_object_id)->first();
           //var_dump($loyaltypass); die("h");
           if (is_null($loyaltypass)) {
                throw new ApiGenericException('There is no loyalty object with the user', 422);
           } 
          $loyaltyPoints = 0;
          if ($user->is_loyalty) {
            $accountData = LoyaltyProgram::getUserAccounts($user->email, $user->id);
            if($accountData['success'] && isset($accountData['data']['accounts'][0]['loyalty'][0]['amount'])) {
                $loyaltyPoints = $accountData['data']['accounts'][0]['loyalty'][0]['amount'];
            }
      
          $dataSet = ['loyaltyPoints'=> $loyaltyPoints, 'loyaltyObjectId'=> $request->loyalty_object_id];
          try { 
             $response = $service->loyaltyobject->update($request->loyalty_object_id, \Loyalty::updateLoyaltyObjectPoints('3323399842768045437','icon22298012', $dataSet));
          } catch(Exception $e){
             $this->log->info('['.$user->id.'] There was error updating'.$loyaltyPoints.' points to loyalty object for user'.$e->getMessage());
             throw new ApiGenericException('There was some error adding points to loyalty card',  422);
          }

           return [
               'loyalty' => $loyaltypass,
                'loyalty_points' => $loyaltyPoints
                ];

          }
      }

      


}
