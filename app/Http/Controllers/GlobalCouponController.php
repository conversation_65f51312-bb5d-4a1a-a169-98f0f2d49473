<?php

namespace App\Http\Controllers;

use App\Exceptions\NotFoundException;

use App\Http\Requests;

use App\Models\GlobalCoupon;
use App\Models\VisitorCode;

use Illuminate\Http\Request;

use Storage;
use Mail;

class GlobalCouponController extends Controller
{

    public function showJpg(Request $request, string $slug)
    {
        if (!($coupon = GlobalCoupon::where('slug', $slug)->first())) {
            throw new NotFoundException("No global coupon with that slug found.");
        }

        return $this->respondWithJpg(
            $coupon->generateJpg(
                VisitorCode::firstOrNew(['visitor_code' => $request->header(config('headers.visitor'))])
            )
        );
    }
    public function sendGlobalPageEmail(Request $request)
    {   
        $globalCoupon = GlobalCoupon::where('rate_id',$request->id)->first();
        if (!$globalCoupon || !$globalCoupon->rate_id) {
            throw new NotFoundException(NotFoundException::NOT_FOUND);
        }
        
        $visitorCode = $request->header('X-Big-Apple');
        $this->validate($request, ['email' => 'required|email']);
        $globalCoupon->sendEmailGlobalPage($request->email, $visitorCode);
        return "Success";
    }
    
    public function sendGlobalImagePageEmail(Request $request)
    {   
        $email = $request->email;
        $this->validate($request, ['email' => 'required|email', 'image' => 'required|url']);
        $image = file_get_contents($request->image);
        $couponFileName = str_random(10) . '_coupon.jpg';
        Storage::put($couponFileName, $image);

        $data = ['coupon_file' => $couponFileName];

        Mail::send(
            "coupons.email", $data, function ($message) use ($email, $image) {
                $message->to($email)->subject("Your Icon Parking Coupon Has Arrived!");
                $message->from(env('MAIL_DEFAULT_FROM'));
                $message->attachData($image, "parking-coupon.jpg", ['mime' => 'image/jpeg']);
            }
        );
        Storage::delete($couponFileName);
        return "Success";
    }

    
}
