<?php

namespace App\Http\Controllers;

use App\Classes\LatestPromoCodeLib;
use Illuminate\Http\Request;

use App\Http\Requests;
use App\Models\Promotion;
use App\Models\PromoCode;
use App\Models\PromoUsage;
use App\Models\User;
use App\Models\PromotionUser;
use App\Classes\PromoCodeLib;

use Validator;
use Exception;
use App\Exceptions\ApiGenericException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Excel;
use App\Models\PromotionVerification;
use App\Http\Helpers\QueryBuilder;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use App\Models\PromotionDay;
use App\Services\Pdf;
use App\Services\Image;
use App\Models\ParkEngage\BrandSetting;
use App\Exceptions\UserNotFound;
use App\Exceptions\UserNotAuthorized;
use App\Models\Facility;
use App\Models\ParkEngage\UserFacility;
use App\Models\PromotionFacility;
use App\Models\Reservation;

class LatestPromotionController extends Controller
{
    protected $request;
    protected $log;
    const FULL_DAY_HOUR_VAL = 24;

    public function __construct(Request $request, LoggerFactory $logFactory)
    {
        $this->request = $request;
        $this->log = $logFactory->setPath('logs/parkengage/Latestpromotions')->createLogger('promotions');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    const SUPERADMIN = 1;
    const SUBORDINATE = 4;
    const PARTNER = 3;
    const REGIONAL_MANAGER = 12;
    #PIMS-12606 DD 05-02-2025
    const BUSINESSUSER = 10;
    const BUSINESSCLERK = 8;

    public function index()
    {
        $promotions = Promotion::where('status', 1)->get();
        return ['promotions' => $promotions];
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create() {}

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $deletedPromocode = PromoCode::withTrashed()
                    ->select('promo_codes.*', 'promotions.id as promotion_id')
                    ->leftJoin('promotions', 'promo_codes.promotion_id', '=', 'promotions.id')
                    ->where('promo_codes.promocode', $request->couponscode)
                    ->where('promotions.owner_id', $request->partner_id)
                    ->whereNotNull('promo_codes.deleted_at')
                    ->get();

        if (count($deletedPromocode) > 0) {
            throw new ApiGenericException('Promocode deleted by admin. Please contact to admin for activate.');
        }

        // $this->validate($request, Promotion::$ValidationRules, Promotion::$ValidationMessage);
        $validationRules = $request->has('validity_check') && $request->input('validity_check') == '1'
        ? Promotion::$ValidationRules
        : Promotion::$withoutValidDateCheckValidationRules;

        $this->validate($request, $validationRules, Promotion::$ValidationMessage);

        if ($request->valid_from > $request->valid_to) {
            throw new ApiGenericException('Error Occured, Invalid Validity Date Provided');
        }

        if ($request->has('rate_id')) {
            $rateId = $request->input('rate_id');
            $request->merge(['rate_category_id' => $rateId]);
        }

        if ($request->has('tusage')) {
            $tusage = $request->input('tusage');
            $request->merge(['usage_type' => $tusage]);
        }

        if ($request->has('validity_check')) {
            $validityStatus = $request->input('validity_check');
            $request->merge(['is_validity_check' => $validityStatus]);
        } else {
            $request->merge(['is_validity_check' => 0]);
        }

        if ($request->specific_user_type == '1') {
            //comment this code by trapti mam due to max_lifetime_discount optional in admin
            // if (($request->percentage_off_discount > $request->max_lifetime_discount && $request->discount_type == 'percentage') || ($request->discount_value > $request->max_lifetime_discount) && $request->discount_type == 'value') {
            //     throw new ApiGenericException('Max Lifetime value should be greater discount amount or discount value');
            // }

            if (!$request->PromotionCodeEmail && !$request->PromotionCodeEmailSheet) {
                throw new ApiGenericException('Users email or excel file is required.');
            }

            if ($request->PromotionCodeEmailSheet) {

                $destination_path = public_path() . '/assets/promotion-emails-spreadsheet/';
                $file = $request->file('PromotionCodeEmailSheet');
                $file_extension = $file->getClientOriginalExtension();

                /* File validation: */
                $validation = Validator::make(
                    ['file' => $file, 'extension' => $file_extension],
                    ['file' => 'required', 'extension' => 'required|in:xlsx,xls']
                );
                if ($validation->fails()) {
                    throw new ApiGenericException("Please upload valid spreadsheet");
                }
            }
        }

        if ($request->couponscode != '') {
            if (Auth::user()->user_type == self::SUPERADMIN || Auth::user()->user_type == 2) {
                $partner_id = $request->partner_id;
            }
            else if (Auth::user()->user_type == self::PARTNER) {
                $partner_id = Auth::user()->id;
            }
            else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
                $partner_id = Auth::user()->created_by;
            }
            else if (Auth::user()->user_type == self::SUBORDINATE) {
                $partner_id = Auth::user()->created_by;
            }
            else if (Auth::user()->user_type == self::BUSINESSUSER) {
                $partner_id = Auth::user()->created_by;
            }
            else if (Auth::user()->user_type == self::BUSINESSCLERK) {
                $partner_id = Auth::user()->created_by;
            }
            else
            {
                $partner_id = 0;
            }
            $request->couponscode = strtoupper($request->couponscode);
            $dacouponscode = PromoCode::select('promo_codes.*', 'promotions.id as promotion_id')

            ->leftJoin('promotions', 'promo_codes.promotion_id', '=', 'promotions.id')

            ->where('promo_codes.promocode', $request->couponscode)

            ->where('promotions.owner_id', $partner_id)

            ->exists();

            if ($dacouponscode) {
                throw new ApiGenericException('This promocode already exist in record.');
            }
        }

        $promo_type_id = $request->promo_type_id;
        $promotion = new Promotion();
        $promotion->fill($request->only(['promo_type_id', 'channel_partner_id', 'name', 'description', 'total_coupons', 'valid_from', 'valid_to', 'user_type', 'percentage_off_discount', 'dollar_off_discount', 'owner_id', 'max_lifetime_discount', 'status', 'is_tax_applicable', 'hours_off_discount', 'rate_category_id', 'usage_type', 'is_validity_check', 'no_of_times', 'applicable_to_hours', 'vaild_hours', 'first_hours_apply']));
        $promotion->status = $request->status;
        $promotion->created_by = Auth::user()->id;
        if (Auth::user()->user_type == self::SUPERADMIN || Auth::user()->user_type == 2) {
            $promotion->owner_id = $request->partner_id;
            $promotion->rm_id = isset($request->rm_id) ? $request->rm_id : '';
        }
        if (Auth::user()->user_type == self::PARTNER) {
            $promotion->owner_id = Auth::user()->id;
            $promotion->rm_id = isset($request->rm_id) ? $request->rm_id : '';
        }
        if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
            $promotion->owner_id = Auth::user()->created_by;
            $promotion->rm_id = Auth::user()->id;
        }
        if (Auth::user()->user_type == self::SUBORDINATE) {
            $promotion->owner_id = Auth::user()->created_by;
            $promotion->rm_id = Auth::user()->user_parent_id;
        }

        if (Auth::user()->user_type == self::BUSINESSUSER) {
            $promotion->owner_id = Auth::user()->created_by;
            $promotion->rm_id = Auth::user()->user_parent_id;
        }

        if (Auth::user()->user_type == self::BUSINESSCLERK) {
            $promotion->owner_id = Auth::user()->created_by;
            $promotion->rm_id = Auth::user()->user_parent_id;
        }

        if ($request->valid_from == '1000-01-01' || $request->valid_from == '') {
            $promotion->valid_from = date("Y-m-d");
        }
        if ($request->valid_to == '1000-01-01' || $request->valid_to == '') {
            $promotion->valid_to = '';
        }

        switch ($promo_type_id) {
            case 1:
                // Validate Against Static Type Promo Codes
                $this->validate($request, Promotion::$staticValidationRules);
                $promotion->fill($request->only(['usage', 'discount_type', 'discount_value']));
                break;
            case 2:
                // Validate Against Single Use Promo Codes
                $this->validate($request, Promotion::$singleValidationRules);
                $promotion->fill($request->only(['usage', 'discount_type', 'discount_value']));
                break;
            case 3:
                // Validate Against Promo Type Promo Codes
                $this->validate($request, Promotion::$promoValidationRules);
                $promotion->fill($request->only(['base_price', 'promo_price']));
                break;
            default:
                // code...
                break;
        }

        $promotion->save();
        if ($request->discount_type == 'hours') {
            if (!empty($request->no_of_times) && !empty($request->no_of_times)) {
                $promotion->no_of_times = $request->no_of_times;
                $promotion->applicable_to_hours = $request->applicable_to_hours;
                $promotion->vaild_hours = $request->vaild_hours;
                $promotion->first_hours_apply = $request->first_hours_apply;
            }
        }
        if ($request->specific_user_type == '1') {
            $promotion->specific_user_type = 1;
            $promotion->promotion_validation_type = $request->promotion_validation_type;
            $promotion->save();

            if ($request->PromotionCodeEmail) {
                $emails = explode(",", $request->PromotionCodeEmail);
                $emails = array_unique($emails);
                $promotionEmails = [];
                foreach ($emails as $key => $value) {
                    if (trim($value) != '') {
                        $promotionEmails[$key]['email'] = trim($value);
                        $promotionEmails[$key]['promotion_id'] = $promotion->id;
                    }
                }
                PromotionUser::insert($promotionEmails);
            }

            if ($request->PromotionCodeEmailSheet) {

                $destination_path = public_path() . '/assets/promotion-emails-spreadsheet/';
                $file = $request->file('PromotionCodeEmailSheet');
                $file_extension = $file->getClientOriginalExtension();

                /* File validation: */
                $validation = Validator::make(
                    ['file' => $file, 'extension' => $file_extension],
                    ['file' => 'required', 'extension' => 'required|in:xlsx,xls']
                );
                if ($validation->fails()) {
                    throw new ApiGenericException("Please upload valid spreadsheet");
                }

                $file_name = 'promotion-emails.' . $file_extension;
                if (!$file->move($destination_path, $file_name)) {
                    throw new ApiGenericException("Something went wrong while importing users sheet");
                }

                $uploaded_file = $destination_path . $file_name;

                $result = Excel::load(
                    $uploaded_file,
                    function ($reader) {}
                )->get()->toArray();

                if (empty($result)) {
                    throw new ApiGenericException("No data found in the spreadsheet.");
                }

                $promotionEmails = [];
                $uniqueEmails = [];
                foreach ($result as $row) {
                    foreach ($row as $excel_email) {
                        $excel_email = trim($excel_email);
                        if ($excel_email) {
                            $uniqueEmails[] = $excel_email;
                        }
                    }
                }
                $uniqueEmails = array_unique($uniqueEmails);
                foreach ($uniqueEmails as $key => $value) {
                    $promotionEmails[] = ['email' => $value, 'promotion_id' => $promotion->id];
                }

                PromotionUser::insert($promotionEmails);
            }
        }

        //Add custom coupon code
        if ($request->couponscode != '') {
            $request->couponscode = strtoupper($request->couponscode);
            $dacouponscode = PromoCode::select('promo_codes.*', 'promotions.id as promotion_id')

            ->leftJoin('promotions', 'promo_codes.promotion_id', '=', 'promotions.id')

            ->where('promo_codes.promocode', $request->couponscode)

            ->where('promotions.owner_id', $request->partner_id)

            ->first();

            if ($dacouponscode) {
                throw new ApiGenericException('This promocode already exist in record.');
            }
            $promotion->couponscode = $request->couponscode;
        } else {
            $promotion->couponscode = "";
        }

        // new code for section Entry Exit Time
        $entryexitArr = json_decode($request->entry_exit_time);
        $promotionDay = [];
        if ($request->entry_exit_time) {
            foreach ($entryexitArr as $key => $val) {
                $promotionDay['promotion_id'] = $promotion->id;
                $promotionDay['days'] = $val->days;
                $promotionDay['start_time'] = $val->start_time;
                $promotionDay['end_time'] = $val->end_time;
                if ($val->next_day == true) {
                    $promotionDay['next_day'] = 1;
                } else {
                    $promotionDay['next_day'] = 0;
                }
                $promotionDay['max_hour'] = $val->max_hour;
                $promotionDay['daysIds'] = $val->daysIds;
                PromotionDay::create($promotionDay);
            }
        }
        // new code for section Entry Exit Time

        // Alka:PIMS-10950
        if (isset($request->facility_ids) && !empty($request->facility_ids)) {
            $facilityIds = explode(',', $request->facility_ids);

            $data = array_map(function ($facilityId) {
                return [
                    'facility_id' => $facilityId,
                ];
            }, $facilityIds);

            $promotion->facilities = $promotion->promotionFacilities()->createMany($data);
        }
        // End
        return PromoCodeLib::generatePromoCodes($promotion);
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function showOld($id)
    {
        if (!$id) {
            throw new ApiGenericException('Please Provide Valid Promo Type Id');
        }

        $promotype = Promotion::where(['id' => $id, 'status' => 1])->first();

        if (!$promotype) {
            throw new ApiGenericException('Couldn\'t Find Any Promo Type With Given Id');
        }

        return [
            'success' => true,
            'promoType' => $promotype
        ];
    }

    public function show($id)
    {
        if (!$id) {
            throw new ApiGenericException('Please Provide Valid Promotion Id');
        }
        $rm_id = '';
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
            $rm_id      = isset($request->rm_id) ? $request->rm_id : '';
        } else {
            if (Auth::user()->user_type == self::SUPERADMIN) {
            } else {
                if (Auth::user()->user_type == self::SUBORDINATE) {
                    $partner_id = Auth::user()->created_by;
                    $rm_id = Auth::user()->user_parent_id;
                } else if (Auth::user()->user_type == self::REGIONAL_MANAGER) {
                    $partner_id = Auth::user()->created_by;
                    $rm_id      = Auth::user()->id;
                } else if (Auth::user()->user_type == self::BUSINESSUSER) {  #PIMS-12606 DD 05-02-2025
                    $partner_id = Auth::user()->created_by;
                    $rm_id = Auth::user()->user_parent_id;
                } else if (Auth::user()->user_type == self::BUSINESSCLERK) {
                    $partner_id = Auth::user()->created_by;
                    $rm_id = Auth::user()->user_parent_id;
                } else {
                    $partner_id = Auth::user()->id;
                    $rm_id      = isset($request->rm_id) ? $request->rm_id : '';
                }
            }
        }
        $promotion = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers', 'promotionday', 'promotionFacilities.facility')->where(['id' => $id]);
        if (Auth::user()->user_type == self::SUPERADMIN) {
            $promotion = $promotion->first();
        } else {
            if ($partner_id != '') {
                $promotion = $promotion->where('owner_id', $partner_id);
            }
            if ($rm_id > 0) {
                if ($partner_id == config('parkengage.PARTNER_USM')) {
                } else {
                    $promotion = $promotion->where('rm_id', $rm_id);
                }
            }
            $promotion = $promotion->first();
        }
        if (!$promotion) {
            throw new ApiGenericException('Data not found');
        }
        if ($promotion->total_coupons <= 1) {
            $promotion->is_custom = 1;
        } else {
            $promotion->is_custom = 0;
        }
        $total_usage = 0;
        $total_redeem_amount = 0;
        if ($promotion->promoCode) {
            foreach ($promotion->promoCode as $key => $value) {
                $promocode = PromoUsage::where('promocode', $value->promocode)->get();
                if ($promocode) {
                    $value->usage = count($promocode);
                    $total_usage += count($promocode);
                    foreach ($promocode as $k => $v) {
                        $total_redeem_amount += $v->discount_amount;
                        $promotion->promocode_usage_users = $promocode;
                    }
                }
            }
        }
        $promotion->total_usage = $total_usage;
        $promotion->total_redeem_amount = number_format($total_redeem_amount, 2);

        if (!$promotion) {
            throw new ApiGenericException('Couldn\'t Find Any Promotion With Given Id');
        }

        return [
            'success' => true,
            'Promotion' => $promotion
        ];
    }

    /**
     * 
     * @param type validateUserPromo
     * @ success redirect on website or fail
     * @throws ApiGenericException
     */
    public function validateUserPromo($activaionCode = NULL)
    {
        $message = "";
        if (!$activaionCode) {
            $message = "Link is not valid or has been expired.";
            return view('email-promocode-activated', array("is_error" => '1', 'message' => $message));
        }

        $promotionData = PromotionVerification::where(['activation_code' => $activaionCode, 'status' => '0'])->first();

        if (!$promotionData) {
            $message = "Link is not valid or has been expired.";
            return view('email-promocode-activated', array("is_error" => '1', 'message' => $message));
        }

        $message = "Your email has been validated to use promocode: " . $promotionData->promocode;
        $promotionData->status = 1;
        $promotionData->save();
        return view('email-promocode-activated', array("is_error" => '0', 'message' => $message));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id) {}

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int                      $id
     * @return \Illuminate\Http\Response
     */
    public function updateOld(Request $request, $id)
    {
        $this->validate($request, Promotion::$updateValidationRules);

        $promotype = Promotion::where('id', $request->id)->first();

        if (!$promotype) {
            throw new ApiGenericException('Couldn\'t Find Any Promo Type With Given Id');
        }

        $promotype->fill($request->only(['name', 'description']));
        $result = $promotype->save();

        if (!$result) {
            throw new ApiGenericException('Error Occured, Promo Type Could Not Be Updated');
        }

        return [
            'success' => true,
            'promoType' => $promotype
        ];
    }

    public function update(Request $request)
    {
        $this->validate($request, Promotion::$excelValidationRules);

        $dateFields = ['valid_from', 'valid_to'];
        foreach ($dateFields as $field) {
            if ($request->has($field)) {
                $formattedDate = Carbon::parse($request->input($field))->format('Y-m-d');
                $request->merge([$field => $formattedDate]);
            }
        }

        if ($request->valid_from > $request->valid_to) {
            throw new ApiGenericException('Error Occured, Invalid Validity Date Provided');
        }

        if ($request->has('rate_category_id')) {
            $rateId = $request->input('rate_category_id');
            $rateCategoryId = empty($rateId) || $rateId == "null" ? null : $rateId;
            $request->merge(['rate_category_id' => $rateCategoryId]);
        }

        if ($request->has('tusage')) {
            $tusage = $request->input('tusage');
            $usage = ($tusage == '3') ? 1 : $request->usage;
            $request->merge([
                'usage_type' => $tusage,
                'usage' => $usage,
            ]);
        }

        if ($request->has('validity_check')) {
            $validityStatus = $request->input('validity_check');
            $request->merge(['is_validity_check' => $validityStatus]);
        }

        if ($request->specific_user_type == '1') {
            //comment this code by trapti mam due to max_lifetime_discount optional in admin
            // if (($request->percentage_off_discount > $request->max_lifetime_discount && $request->discount_type == 'percentage') || ($request->discount_value > $request->max_lifetime_discount) && $request->discount_type == 'value') {
            //     throw new ApiGenericException('Max Lifetime value should be greater discount amount or discount value');
            // }

            if (!$request->PromotionCodeEmail && !$request->PromotionCodeEmailSheet) {
                throw new ApiGenericException('Users email or excel file is required.');
            }

            if ($request->PromotionCodeEmailSheet) {

                $destination_path = public_path() . '/assets/promotion-emails-spreadsheet/';
                $file = $request->file('PromotionCodeEmailSheet');
                $file_extension = $file->getClientOriginalExtension();

                /* File validation: */
                $validation = Validator::make(
                    ['file' => $file, 'extension' => $file_extension],
                    ['file' => 'required', 'extension' => 'required|in:xlsx,xls']
                );
                if ($validation->fails()) {
                    throw new ApiGenericException("Please upload valid spreadsheet");
                }
            }
        }
        if ($request->has('no_of_times')) {
            $no_of_times = $request->input('no_of_times');
            $request->merge(['no_of_times' => $no_of_times]);
        }
        if ($request->has('applicable_to_hours')) {
            $applicable_to_hours = $request->input('applicable_to_hours');
            $request->merge(['applicable_to_hours' => $applicable_to_hours]);
        }
        if ($request->has('vaild_hours')) {
            $vaild_hours = $request->input('vaild_hours');
            $request->merge(['vaild_hours' => $vaild_hours]);
        }
        if ($request->has('first_hours_apply')) {
            $first_hours_apply = $request->input('first_hours_apply');
            $request->merge(['first_hours_apply' => $first_hours_apply]);
        }
        $promotion = Promotion::where('id', $request->id)->first();
        if (!$promotion) {
            throw new ApiGenericException('Couldn\'t Find Any Promotion With Given Id');
        }

        $promotion->fill($request->only(['status', 'valid_from', 'valid_to', 'description', 'discount_type', 'discount_value', 'usage', 'percentage_off_discount', 'dollar_off_discount', 'max_lifetime_discount', 'is_tax_applicable', 'promo_type_id', 'hours_off_discount', 'user_type', 'channel_partner_id', 'rate_category_id', 'usage_type', 'is_validity_check', 'no_of_times', 'applicable_to_hours', 'vaild_hours', 'first_hours_apply']));
        $result = $promotion->save();


        // new code for section Entry Exit Time
        $entryexitArr = json_decode($request->entry_exit_time);
        $promotionDay = [];
        if ($request->entry_exit_time) {
            PromotionDay::where('promotion_id', $request->id)->delete();
            foreach ($entryexitArr as $key => $val) {
                $promotionDay['promotion_id'] = $val->promotion_id;
                $promotionDay['days'] = $val->days;
                $promotionDay['start_time'] = $val->start_time;
                $promotionDay['end_time'] = $val->end_time;
                if ($val->next_day == true) {
                    $promotionDay['next_day'] = 1;
                } else {
                    $promotionDay['next_day'] = 0;
                }
                $promotionDay['max_hour'] = $val->max_hour;
                $promotionDay['daysIds'] = $val->daysIds;
                PromotionDay::create($promotionDay);
            }
        } else {
            PromotionDay::where('promotion_id', $request->id)->delete();
        }
        // new code for section Entry Exit Time

        try {

            if ($request->specific_user_type == '1') {
                $promotion->specific_user_type = 1;
                $promotion->promotion_validation_type = $request->promotion_validation_type;
                $promotion->save();
                //PromotionUser::where('promotion_id', $promotion->id)->delete();
                if ($request->PromotionCodeEmail) {
                    $emails = explode(",", $request->PromotionCodeEmail);
                    $emails = array_unique($emails);
                    $promotionEmails = [];
                    if (!empty($promotion->id) && !empty($emails)) {
                        //remove code by trapti mam due to store last record
                        //PromotionUser::where('promotion_id', $promotion->id)->whereNotIn('email', ($emails))->delete();
                    }
                    foreach ($emails as $key => $value) {
                        if (trim($value) != '') {
                            $alreadyExist = PromotionUser::where('promotion_id', $promotion->id)->where('email', trim($value))->first();

                            if (!$alreadyExist) {
                                $promotionEmails[$key]['email'] = trim($value);
                                $promotionEmails[$key]['promotion_id'] = $promotion->id;
                            }
                        }
                    }

                    if (count($promotionEmails) > 0) {
                        PromotionUser::insert($promotionEmails);
                    }
                }

                if ($request->PromotionCodeEmailSheet) {
                    $destination_path = public_path() . '/assets/promotion-emails-spreadsheet/';
                    $file = $request->file('PromotionCodeEmailSheet');
                    $file_extension = $file->getClientOriginalExtension();

                    /* File validation: */
                    $validation = Validator::make(
                        ['file' => $file, 'extension' => $file_extension],
                        ['file' => 'required', 'extension' => 'required|in:xlsx,xls']
                    );
                    if ($validation->fails()) {
                        throw new ApiGenericException("Please upload valid spreadsheet");
                    }

                    $file_name = 'promotion-emails.' . $file_extension;
                    if (!$file->move($destination_path, $file_name)) {
                        throw new ApiGenericException("Something went wrong while importing users sheet");
                    }

                    $uploaded_file = $destination_path . $file_name;

                    $result = Excel::load(
                        $uploaded_file,
                        function ($reader) {}
                    )->get()->toArray();

                    if (empty($result)) {
                        throw new ApiGenericException("No data found in the spreadsheet.");
                    }

                    $promotionEmails = [];
                    $uniqueEmails = [];
                    foreach ($result as $row) {
                        foreach ($row as $excel_email) {
                            $excel_email = trim($excel_email);
                            if ($excel_email) {
                                $alreadyExist = PromotionUser::where('promotion_id', $promotion->id)->where('email', $excel_email)->first();
                                if (!$alreadyExist) {
                                    $uniqueEmails[] = $excel_email;
                                }
                            }
                        }
                    }
                    $uniqueEmails = array_unique($uniqueEmails);
                    foreach ($uniqueEmails as $key => $value) {
                        $promotionEmails[] = ['email' => $value, 'promotion_id' => $promotion->id];
                    }

                    PromotionUser::insert($promotionEmails);
                }
            } else {
                $promotion->specific_user_type = 0;
                $promotion->save();
                PromotionUser::where('promotion_id', $promotion->id)->delete();
            }

            if ((!isset($request->discount_value)) || ($request->discount_value == '')) {
                $request->discount_value = 0;
            }

            $existPromo = DB::table('promo_codes')->where('promotion_id', $request->id)->first();
            if (strtoupper($existPromo->promocode) != strtoupper($request->couponscode)) {
                // $status = DB::table('promo_codes')->where('promocode', $request->couponscode)->first();
                $status = PromoCode::select('promo_codes.*', 'promotions.id as promotion_id')

                ->leftJoin('promotions', 'promo_codes.promotion_id', '=', 'promotions.id')

                ->where('promo_codes.promocode', $request->couponscode)

                ->where('promotions.owner_id', $request->partner_id)

                ->exists();
                if ($status) {
                    throw new ApiGenericException("Promocode is already Exist.");
                }
            }
            DB::table('promo_codes')->where('promotion_id', $request->id)
                ->update(['usage' => $request->usage, 'valid_from' => $request->valid_from, 'valid_to' => $request->valid_to, 'discount_type' => $request->discount_type, 'discount_value' => $request->discount_value, 'usage' => $request->usage, 'max_lifetime_discount' => $request->max_lifetime_discount]);

            if ($request->couponscode) {
                DB::table('promo_codes')->where('promotion_id', $request->id)
                    ->update(['promocode' => $request->couponscode]);
            }
        } catch (Exception $e) {
        }

        // Alka:PIMS-10950
        if (isset($request->facility_ids) && !empty($request->facility_ids)) {
            $facilityIds = explode(',', $request->facility_ids);
            $data = PromotionFacility::where('promotion_id', $request->id)
                ->whereNotIn('facility_id', $facilityIds)
                ->delete();

            $existingFacilities = PromotionFacility::where('promotion_id', $request->id)
                ->pluck('facility_id')
                ->toArray();

            $toInsert = array_diff($facilityIds, $existingFacilities);

            if (!empty($toInsert)) {
                $data = array_map(function ($facilityId) use ($request) {
                    return [
                        'facility_id' => $facilityId,
                        'promotion_id' => $request->id,
                    ];
                }, $toInsert);
                PromotionFacility::insert($data);
            }
        } else {
            $data = PromotionFacility::where('promotion_id', $request->id)
                ->delete();
        }
        // End

        if (!$result) {
            throw new ApiGenericException('Error Occured, Promotion Could Not Be Updated');
        }

        return [
            'success' => true,
            'Promotion' => $promotion
        ];
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id, Request $request)
    {
        if (!$request->id) {
            throw new ApiGenericException('Please Provide Valid Promo Type Id');
        }

        $promotype = Promotion::where('id', $request->id)->first();

        if (!$promotype) {
            throw new ApiGenericException('Couldn\'t Find Any Promo Type With Given Id');
        }

        $result = $promotype->delete();
        PromoCode::where('promotion_id', $request->id)->delete();
        PromotionUser::where('promotion_id', $request->id)->delete();
        /*if ($request->forcedelete) {
            $result = $promotype->forceDelete();
        } else {
            $result = $promotype->delete();
            PromoCode::where('promotion_id', $request->id)->delete();
            PromotionUser::where('promotion_id', $request->id)->delete();
        }*/


        if (!$result) {
            throw new ApiGenericException('Error Occured, Promo Type Could Not Be Deleted');
        }

        return [
            'success' => true,
        ];
    }

    public function downloadExcel($promotionId)
    {
        // $this->validate($request, Promotion::$excelValidationRules);

        $promotion = Promotion::with('promotionUsers')->where('id', $promotionId)->first();

        if (!$promotion) {
            throw new ApiGenericException('Error Occured, Could Not Find Promo Codes For This Promotion');
        }

        $promotionEmails = '';
        if (count($promotion->promotionUsers) > 0) {
            foreach ($promotion->promotionUsers as $key => $value) {
                $promotionEmails .= $value->email . ', ';
            }
            $promotionEmails = rtrim($promotionEmails, ", ");
        }

        $promoCodes = PromoCode::select(
            'promocode',
            'discount_type',
            'discount_value',
            'valid_from',
            'valid_to',
            'created_at'
        )->where('promotion_id', $promotionId)->get();

        $finalCodes = [];
        $increment = 1;
        foreach ($promoCodes as $code) {
            $finalCodes[] = [
                'No.' => $increment,
                'Promotion' => $promotion->name,
                'PromoCode' => $code->promocode,
                'Promo Type' => ucfirst($code->discount_type) . ' Based',
                'Promo Value' => $code->discount_value,
                'Valid From' => $code->valid_from,
                'Valid To' => $code->valid_to,
                'Max Percentage off discount' => ($promotion->percentage_off_discount > 0) ? $promotion->percentage_off_discount : '-',
                'Minimum Transaction Amount($)' => ($promotion->dollar_off_discount > 0) ? $promotion->dollar_off_discount : '-',
                'Generated On' => $code->created_at,
                'Max Life Discount' => $promotion->max_lifetime_discount,
                'Specific Users' => $promotionEmails
            ];
            $increment++;
        }

        $excelSheetName = ucwords($promotion->name);

        Excel::create(
            $excelSheetName,
            function ($excel) use ($finalCodes, $excelSheetName) {

                // Set the spreadsheet title, creator, and description
                $excel->setTitle($excelSheetName);
                $excel->setCreator('IconGo')->setCompany('Outworx Solutions Pvt Ltd');
                $excel->setDescription('List Of Promo Codes');

                // Freeze first row
                // $excel->freezeFirstRow();

                // Build the spreadsheet, passing in the payments array
                $excel->sheet(
                    'Promo Codes',
                    function ($sheet) use ($finalCodes) {
                        $sheet->fromArray($finalCodes, null, 'A1', false, true);
                    }
                );
            }
        )->store('xls')->download('xls');
    }

    public function checkPromo(Request $request)
    {
        $this->validate($request, Promotion::$checkPromoValidationRules);
        return PromoCodeLib::validatePromoCode($request);
    }

    public function checkPromoThirdParty(Request $request)
    {
        $this->log->info("Request Data Promocode:- " . json_encode($request->all()));
        if (!is_null($request->header('X-ClientSecret')) && $request->header('X-ClientSecret') !== "") {
            $this->log->info("Header Found:");
            if ($request->client_id == '') {
                throw new ApiGenericException('Something went wrong.');
            }
            $request->request->add(['client_id' => $request->client_id]);
        } else {
            $this->log->info("Header Not Found:");
            $request->request->add(['client_id' => $request->client_id]);
        }

        $this->validate($request, Promotion::$checkPromoValidationRulesThirdParty);
        // Vijay : 05-09-2023 : Change for is_tax_promotion config 
        $this->log->info("Request Data :- ");
        $this->log->info(json_encode($request->all()));

        $total = isset($request->amount) ? $request->amount : 0;
        $totalWithoutTax = $total;
        $processingFee = isset($request->processing_fee) ? $request->processing_fee : 0;
        if ($processingFee > 0) {
            if (isset($request->is_extend) && $request->is_extend) {
            } else {
                $totalWithoutTax -= $processingFee;
            }
        }

        $reservation = Reservation::find($request->reservation_id);

        $taxAmount = isset($request->tax_amount) ? $request->tax_amount : 0;
        if ($taxAmount > 0) {
            if (isset($request->is_extend) && $request->is_extend) {
                // dd($totalWithoutTax, $taxAmount , $reservation->tax_fee);
                // $totalWithoutTax += ($taxAmount - $reservation->tax_fee);
            } else {
                $totalWithoutTax -= $taxAmount;
            }
        }



        // $totalWithoutTax = ($total);
        $request->request->add(['amount' => $totalWithoutTax]);

        // dd($total, $totalWithoutTax, $processingFee, $taxAmount, $request->amount, $request->tax_amount);
        $this->log->info("Updated Request :- " . json_encode($request->all()));

        // $result =  PromoCodeLib::validatePromoCodeThirdParty($request);              // Vijay : 26-07-2024
        $result =  LatestPromoCodeLib::validatePromoCodeThirdParty($request);           // Because update code in Latest class for USM Reservation         
        $this->log->info("Afer Applied Response " . json_encode($result->getData()));
        return $result;
    }

    public function updatePromocodeUsage(Request $request)
    {
        $db_logs_data['request'] = json_encode($request->all());
        $db_logs_data['api_type'] = 'usage';
        $db_logs_data['created_at'] = Carbon::now();
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/partner_promocode_api_logs')->createLogger('usage_api_logs');

        try {
            $log->info("Request: " . $db_logs_data['request']);
            $this->validate($request, Promotion::$checkPromoUsageValidationRulesThirdParty);
            $promocodeData = PromoCodeLib::validatePromoCodeUsageThirdParty($request);
            try {
                $promoUsage = new PromoUsage();
                $reservation = Reservation::where("ticketech_code", $request->transaction_id)->first();
                if ($reservation) {
                    $promoUsage->user_id   = $reservation->user_id;
                } else {
                    if ($request->email != '') {
                        $user = User::where("email", $request->email)->where("created_by", $promocodeData['partner_id'])->first();
                        if ($user) {
                            $promoUsage->user_id   = $user->id;
                        } else {
                            $promoUsage->user_id   = $promocodeData['partner_id'];
                        }
                    } else {
                        $promoUsage->user_id   = $promocodeData['partner_id'];
                    }
                }
                $promoUsage->partner_id = $promocodeData['partner_id'];
                $promoUsage->rm_id     = isset($request->rm_id) ? $request->rm_id : '';
                $promoUsage->promocode = $request->promocode;
                $promoUsage->email = $request->email;
                $promoUsage->reservation_id = $request->transaction_id;
                $promoUsage->discount_amount = $promocodeData['applicable_discount'];
                $promoUsage->save();
            } catch (Exception $e) {
                throw $e;
            }

            $response = [
                'success' => true,
                'message' => $promocodeData['message'],
                'amount' => $request->amount,
                'applicable_discount' => $promocodeData['applicable_discount']
            ];

            $db_logs_data['response'] = json_encode($response);
            $log->info("Response: " . $db_logs_data['response']);
            DB::table('partner_promocode_api_logs')->insert($db_logs_data);

            if ($promocodeData['applicable_discount'] < $request->amount) {
                return response()->json($response, 202);
            }
            return response()->json($response, 200);
        } catch (Exception $e) {
            $log->error("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
            $db_logs_data['is_error'] = 1;
            $db_logs_data['error_message'] = $e->getMessage();
            DB::table('partner_promocode_api_logs')->insert($db_logs_data);
            throw $e;
        }
    }


    public function prepareArrayData(array $data)
    {
        $tempData = [];
        for ($i = 0; $i < count($data); $i++) {
            $particular = (array) $data[$i];
            $tempData[$particular['id']] = $particular;
        }

        return $tempData;
    }

    public function getTotalUsage($promotions, $request = '')
    {
        foreach ($promotions as $key => $promotion) {
            if ($promotion->total_coupons <= 1) {
                $promotion->is_custom = 1;
            } else {
                $promotion->is_custom = 0;
            }
            if ($promotion->is_custom == 1) {
                $total_usage = 0;
                $total_redeem_amount = 0;
                if ($promotion->promoCode) {
                    foreach ($promotion->promoCode as $key => $value) {

                        if (Auth::user()->user_type != '1') {
                            // $promocode = PromoUsage::where('promocode', $value->promocode)->where('partner_id',Auth::user()->id)->get();
                            $promocode = PromoUsage::where('promocode', $value->promocode)->where('partner_id', $promotion->owner_id)->get();
                        } else {
                            $promocode = PromoUsage::where('promocode', $value->promocode)->get();
                        }

                        $value->usage = count($promocode);
                        $total_usage += count($promocode);
                        foreach ($promocode as $k => $v) {
                            $total_redeem_amount += $v->discount_amount;
                        }
                        $promotion->promocode_usage_users = $promocode;
                    }
                }
                $promotion->total_usage = $total_usage;
                $promotion->total_redeem_amount = $total_redeem_amount;
            } else {
                $total_usage = 0;
                $total_used_coupon_count = 0;
                $total_redeem_amount = 0;
                $total_promocode = 0;
                if ($promotion->promoCode) {

                    foreach ($promotion->promoCode as $key => $value) {
                        $promocode = PromoUsage::where('promocode', $value->promocode)->get();
                        if ($promocode) {
                            $value->usage = count($promocode);
                            $total_usage += count($promocode);
                            foreach ($promocode as $k => $v) {
                                if ($k == 0) {
                                    $total_used_coupon_count += 1;
                                }
                                $total_redeem_amount += $v->discount_amount;
                            }
                            $promotion->promocode_usage_users = $promocode;
                        }
                    }
                    $total_promocode = count($promotion->promoCode);
                }
                $promotion->total_used_coupon_count = $total_used_coupon_count;
                $promotion->total_usage = $total_usage;
                $promotion->total_redeem_amount = $total_redeem_amount;
                $promotion->total_promocode = $total_promocode;
            }
        }

        if ($request != '') {
            if ($request->sortBy == 'asc' || $request->sortBy == 'Asc') {
                for ($i = 0; $i < count($promotions); $i++) {
                    for ($j = $i + 1; $j < count($promotions); $j++) {
                        if ($promotions[$i]->total_usage > $promotions[$j]->total_usage) {
                            $temp = $promotions[$i];
                            $promotions[$i] = $promotions[$j];
                            $promotions[$j] = $temp;
                        }
                    }
                }
            } else {
                for ($i = 0; $i < count($promotions); $i++) {
                    for ($j = $i + 1; $j < count($promotions); $j++) {
                        if ($promotions[$i]->total_usage < $promotions[$j]->total_usage) {
                            $temp = $promotions[$i];
                            $promotions[$i] = $promotions[$j];
                            $promotions[$j] = $temp;
                        }
                    }
                }
            }
        }
        return $promotions;
    }

    public function getTotalPromocodeUsage($promotions)
    {
        if ($promotions) {
            foreach ($promotions as $key => $value) {
                $total_usage = 0;
                $total_redeem_amount = 0;
                $promocode = PromoUsage::where('promocode', $value->promocode)->get();
                $total_usage += count($promocode);
                foreach ($promocode as $k => $v) {
                    $total_redeem_amount += $v->discount_amount;
                }
                $value->promocode_usage_users = $promocode;
                $value->total_usage = $total_usage;
                $value->total_redeem_amount = $total_redeem_amount;
            }

            return $promotions;
        }
    }

    public function getAll(Request $request)
    {
        if (!in_array(Auth::user()->user_type, [1, 3, 4, 12, 8, 10])) {
            throw new UserNotAuthorized("You dont have permission to log in. Please contact your Admin for further details.");
        }
        $owner_id = 0;

        $userData = User::where('id', Auth::user()->id)->first();

        if ($userData->user_type == self::SUPERADMIN || $userData->user_type == '2') {
            $rm_id      = isset($request->rm_id) ? $request->rm_id : '';
            $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers');
            if ($rm_id != '') {
                $promotions = $promotions->where('rm_id', $rm_id);
            }
            if ($request->search) {
                if ($request->search == 'NO') {
                    $promotions =  Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('owner_id', $request->owner_id);
                    if ($rm_id != '') {
                        $promotions = $promotions->where('rm_id', $rm_id);
                    }
                    if ($request->sort != '') {
                        $promotions = $promotions->orderBy($request->sort, $request->sortBy)->paginate(20);
                    } else {
                        $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);
                    }
                    return $this->getTotalUsage($promotions);
                } else {
                    $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers');

                    $promotions->where('name', 'LIKE', '%' . $request->search . '%');
                    $promotions = $promotions->where('owner_id', $request->owner_id)->orWhereHas(
                        'promoCode',
                        function ($query) use ($request) {
                            $query
                                ->where('promocode', 'like', "%{$request->search}%");
                        }
                    );
                    if ($request->owner_id != '') {
                        $promotions->where('owner_id', $request->owner_id);
                    }
                    if ($rm_id != '') {
                        $promotions = $promotions->where('rm_id', $rm_id);
                    }
                    if ($request->search == "active" || $request->search == "Active") {
                        $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('owner_id', $request->owner_id)->where('status', 1);
                    } elseif ($request->search == "inactive" || $request->search == "Inactive") {
                        $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('owner_id', $request->owner_id)->where('status', 0);
                    }
                    /*$promotions = QueryBuilder::buildSearchQuery($promotions, $request->search, Promotion::$searchFields)
                        ->orWhereHas(
                        'users', function ($query) use ($request) {
                            $query
                                ->where('name', 'like', "%{$request->search}%")
                                ->orWhere('email', 'like', "%{$request->search}%");
                        }
                    );
                    */
                    if ($request->sort != '') {
                        $promotions = $promotions->orderBy($request->sort, $request->sortBy)->paginate(20);
                    } else {
                        $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);
                    }
                    return $this->getTotalUsage($promotions);
                }
            }
            if ($request->sort != '') {
                $promotions = $promotions->orderBy($request->sort, $request->sortBy)->paginate(20);
            } else {
                $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);
            }
            return $this->getTotalUsage($promotions);
        } else {
            if ($userData->user_type == self::PARTNER) {
                $owner_id = $userData->id;
                $rm_id    = isset($request->rm_id) ? $request->rm_id : '';
            } else if ($userData->user_type == self::SUBORDINATE) {
                $owner_id = $userData->created_by;
                $rm_id    = isset($userData->user_parent_id) ? $userData->user_parent_id : '';
            } else if ($userData->user_type == self::REGIONAL_MANAGER) {
                $owner_id = $userData->created_by;
                $rm_id    = $userData->id;
            } else if ($userData->user_type == self::BUSINESSUSER) { #PIMS-12606 DD 05-02-2025
                $owner_id = $userData->created_by;
                $rm_id    = isset($userData->user_parent_id) ? $userData->user_parent_id : '';
            } else if ($userData->user_type == self::BUSINESSCLERK) {
                $owner_id = $userData->created_by;
                $rm_id    = isset($userData->user_parent_id) ? $userData->user_parent_id : '';
            }
            #PIMS-12580
            else if ($userData->user_type == self::BUSINESSUSER) {
                $owner_id = $userData->created_by;
                $rm_id    = isset($userData->user_parent_id) ? $userData->user_parent_id : '';
            } else if ($userData->user_type == self::BUSINESSCLERK) {
                $owner_id = $userData->created_by;
                $rm_id    = isset($userData->user_parent_id) ? $userData->user_parent_id : '';
            }

            $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('owner_id', $owner_id);

            if ($rm_id > 0) {
                $promotions = $promotions->where('rm_id', $rm_id)->get();
            }

            $userFacilityIds = UserFacility::select('facility_id')
                ->where('user_id', $request->owner_id)
                ->pluck('facility_id');

            if ($request->search == 'NO') {
                $promotions =  Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('owner_id', $owner_id);

                if ($userData->user_type == self::SUBORDINATE) {
                    if (isset($userFacilityIds) && !empty($userFacilityIds) && count($userFacilityIds) > 0) {

                        $promotionDatas = $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                            $query->whereIn('facility_id', $userFacilityIds);
                        })->where('created_by', $request->owner_id)->get();

                        if (count($promotionDatas) > 0) {
                            $promotions =  $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                                $query->whereIn('facility_id', $userFacilityIds);
                            })->where('created_by', $request->owner_id);
                        }
                    }

                    $promotions =  Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('created_by', $request->owner_id);

                    // $promotions = $promotions->where('created_by',$request->owner_id);
                }

                if ($rm_id > 0) {
                    //	$promotions = $promotions->where('rm_id',$rm_id);
                    if ($owner_id == config('parkengage.PARTNER_USM')) {
                    } else {
                        $promotions = $promotions->where('rm_id', $rm_id);
                    }
                }

                if ($request->sort != '') {
                    $promotions = $promotions->orderBy($request->sort, $request->sortBy)->paginate(20);
                } else {
                    $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);
                }

                return $this->getTotalUsage($promotions);
            } else {
                // if ($request->search !='') {

                $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers');
                $promotions = QueryBuilder::buildSearchQueryForPartner($promotions, $request->search, Promotion::$searchFields);

                $promotions->where(function ($query) use ($owner_id) {
                    $query->where('owner_id', $owner_id);
                });

                if ($userData->user_type == self::SUBORDINATE) {
                    if (isset($userFacilityIds) && !empty($userFacilityIds)) {

                        $promotionDatas = $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                            $query->whereIn('facility_id', $userFacilityIds);
                        })->where('created_by', $request->owner_id)->get();

                        if (count($promotionDatas) > 0) {
                            $promotions =  $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                                $query->whereIn('facility_id', $userFacilityIds);
                            })->where('created_by', $request->owner_id);
                        } else {
                            $promotions =  Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('owner_id', $owner_id);
                        }
                    }
                    // $promotions = $promotions->where('created_by',$request->owner_id);
                }
                /* if(isset($userFacilityIds) && !empty($userFacilityIds)){
                    $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                        $query->whereIn('facility_id', $userFacilityIds);
                    });
                } */

                // $promotions->where('name', 'LIKE', '%' . $request->search . '%');
                // $promotions = $promotions->orWhereHas(
                //     'promoCode',
                //     function ($query) use ($request, $owner_id) {
                //         $query
                //             ->where('promocode', 'like', "%{$request->search}%");
                //         $query->where('owner_id', $owner_id);
                //     }
                // );

                #KT: 22-01-2025 | PIMS: 12391 | Note: Issue resolved for sub ordinates
                $promotions->where('name', 'LIKE', '%' . $request->search . '%');

                $promotions = $promotions->where('created_by', $userData->id)->whereHas(
                    'promoCode',
                    function ($query) use ($request, $owner_id) {
                        $query->where('promocode', 'like', "%{$request->search}%");
                    }
                );

                if ($request->search == "active" || $request->search == "Active") {
                    $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers', 'promotionFacilities')->where('owner_id', $owner_id)->where('status', 1);
                } elseif ($request->search == "inactive" || $request->search == "Inactive") {
                    $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers', 'promotionFacilities')->where('owner_id', $owner_id)->where('status', 0);
                }


                if ($request->sort != '') {
                    $promotions = $promotions->orderBy($request->sort, $request->sortBy)->paginate(20);
                } else {
                    $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);
                }
                return $this->getTotalUsage($promotions);
            }
        }
        if ($request->sort != '') {
            $promotions = $promotions->orderBy($request->sort, $request->sortBy)->paginate(20);
        } else {
            $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);
        }
        return $this->getTotalUsage($promotions);
    }

    public function getAllPromocodes(Request $request)
    {
        if (!in_array(Auth::user()->user_type, [1, 3, 4, 12,8, 10])) {
            throw new UserNotAuthorized("You dont have permission to log in. Please contact your Admin for further details.");
        }
        $owner_id = 0;
        $userData = User::where('id', Auth::user()->id)->first();

        if ($userData->user_type == '1' || $userData->user_type == '2') {
            $rm_id      = isset($request->rm_id) ? $request->rm_id : '';
            $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers');
            if ($rm_id != '') {
                $promotions = $promotions->where('rm_id', $rm_id);
            }
            if ($request->search) {
                if ($request->search == 'NO') {

                    $promotions =  Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers');
                    if ($rm_id != '') {
                        $promotions = $promotions->where('rm_id', $rm_id);
                    }
                    if ($request->status != '') {
                        $promotions->where('status', $request->status);
                    }

                    if ($request->owner_id != '') {
                        $promotions->where('owner_id', $request->owner_id);
                    }

                    $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);

                    return $this->getTotalUsage($promotions, $request);
                } else {
                    $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers');
                    if ($rm_id != '') {
                        $promotions = $promotions->where('rm_id', $rm_id);
                    }
                    if ($request->owner_id != '') {
                        $promotions->where('owner_id', $request->owner_id);
                    }

                    $promotions = $promotions->where('name', 'LIKE', '%' . $request->search . '%');
                    if ($request->status != '') {
                        $promotions->where('status', $request->status);
                    }
                    $promotions = $promotions->orWhereHas(
                        'promoCode',
                        function ($query) use ($request) {
                            $query
                                ->where('promocode', 'like', "%{$request->search}%");
                        }
                    );

                    $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);

                    return $this->getTotalUsage($promotions, $request);
                }
            }

            $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);

            return $this->getTotalUsage($promotions, $request);
        } else {
            if ($userData->user_type == self::PARTNER) {
                $owner_id = $userData->id;
                $rm_id    = isset($request->rm_id) ? $request->rm_id : '';
            } else if ($userData->user_type == self::SUBORDINATE) {
                $owner_id = $userData->created_by;
                $rm_id    = isset($userData->user_parent_id) ? $userData->user_parent_id : '';
            } else if ($userData->user_type == self::REGIONAL_MANAGER) {
                $owner_id = $userData->created_by;
                $rm_id    = $userData->id;
            } else if (Auth::user()->user_type == self::BUSINESSUSER) {
                $partner_id = $userData->created_by;
                $rm_id      = isset($request->rm_id) ? $request->rm_id : '';
            } else if (Auth::user()->user_type == self::BUSINESSCLERK) {
                $partner_id = $userData->created_by;
                $rm_id      = isset($request->rm_id) ? $request->rm_id : '';
            }

            $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('owner_id', $owner_id);

            if ($rm_id != '') {
                $promotions = $promotions->where('rm_id', $rm_id);
            }

            $userFacilityIds = UserFacility::select('facility_id')
                ->where('user_id', $userData->id)
                ->pluck('facility_id');
            // dd($userFacilityIds);

            if ($request->search) {

                if ($request->search == 'NO') {

                    $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('owner_id', $owner_id);

                    if ($request->status != '') {
                        $promotions->where('status', $request->status);
                    }

                    if ($userData->user_type == self::SUBORDINATE) {
                        if (isset($userFacilityIds) && !empty($userFacilityIds)) {

                            $promotionDatas = $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                                $query->whereIn('facility_id', $userFacilityIds);
                            })->where('created_by', $userData->id)->get();

                            if (count($promotionDatas) > 0) {
                                $promotions =  $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                                    $query->whereIn('facility_id', $userFacilityIds);
                                })->where('created_by', $userData->id);
                            }
                        }
                        // dd($promotions->get());
                        $promotions =  Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('created_by', $userData->id);

                        // $promotions = $promotions->where('created_by',$request->owner_id);
                    }

                    $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);

                    return $this->getTotalUsage($promotions, $request);
                } else {
                    $promotions = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers');

                    /* if(isset($userFacilityIds) && !empty($userFacilityIds)){
                        $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                            $query->whereIn('facility_id', $userFacilityIds);
                        });
                    } */
                    if ($userData->user_type == self::SUBORDINATE) {
                        if (isset($userFacilityIds) && !empty($userFacilityIds)) {

                            $promotionDatas = $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                                $query->whereIn('facility_id', $userFacilityIds);
                            })->where('created_by', $userData->id)->get();

                            if (count($promotionDatas) > 0) {
                                $promotions =  $promotions->whereHas('promotionFacilities', function ($query) use ($userFacilityIds) {
                                    $query->whereIn('facility_id', $userFacilityIds);
                                })->where('created_by', $userData->id);
                            }
                        }

                        $promotions =  Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where('created_by', $userData->id);

                        // $promotions = $promotions->where('created_by',$request->owner_id);
                        $promotions = $promotions->where('name', 'LIKE', '%' . $request->search . '%');
                        $promotions = $promotions->where('created_by', $userData->id)->orWhereHas(
                            'promoCode',
                            function ($query) use ($request) {
                                $query
                                    ->where('promocode', 'like', "%{$request->search}%");
                            }
                        );
                        $promotions->where(function ($query) use ($userData, $request) {
                            $query->where('created_by', $userData->id);
                            $query->where('status', $request->status);
                        });
                    } else {

                        $promotions = $promotions->where('name', 'LIKE', '%' . $request->search . '%');
                        $promotions = $promotions->where('owner_id', $owner_id)->orWhereHas(
                            'promoCode',
                            function ($query) use ($request) {
                                $query
                                    ->where('promocode', 'like', "%{$request->search}%");
                            }
                        );
                        $promotions->where(function ($query) use ($owner_id, $request) {
                            $query->where('owner_id', $owner_id);
                            $query->where('status', $request->status);
                        });
                    }
                    $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);
                    return $this->getTotalUsage($promotions, $request);
                }
            }
        }
        $promotions = $promotions->orderBy('id', 'DESC')->paginate(20);
        return $this->getTotalUsage($promotions, $request);
    }


    public function getPromocodeByName($name)
    {
        if (!$name) {
            throw new ApiGenericException('Please Provide Valid Promocode');
        }

        $promotion = PromoCode::with('promotype', 'channelpartner', 'promotion')->where(['promocode' => $name])->first();

        if ($promotion) {
            //foreach ($promotion->promoCode as $key => $value) {
            $total_usage = 0;
            $total_redeem_amount = 0;
            $promocode = PromoUsage::where('promocode', $promotion->promocode)->get();
            $total_usage += count($promocode);
            foreach ($promocode as $k => $v) {
                $total_redeem_amount += $v->discount_amount;
            }
            //}
            $promotion->promocode_usage_users = $promocode;
            $promotion->total_usage = $total_usage;
            $promotion->total_redeem_amount = $total_redeem_amount;
        }

        if (!$promotion) {
            throw new ApiGenericException('Couldn\'t Find Any Promotion With Given Id');
        }

        return [
            'success' => true,
            'Promocode' => $promotion
        ];
    }

    public function getPromocodeByPromotion($id, Request $request)
    {
        if (!$id) {
            throw new ApiGenericException('Please Provide Valid Promotion Id');
        }
        if (isset($request->partner_id)) {
            $partner_id = $request->partner_id;
            $rm_id      = isset($request->rm_id) ? $request->rm_id : '';
        } else {
            if (Auth::user()->user_type == '1') {
                $rm_id = '';
            } else {
                if (Auth::user()->user_type == '12') {
                    $partner_id = Auth::user()->created_by;
                    $rm_id = Auth::user()->id;
                } else if (Auth::user()->user_type == '4') {
                    $partner_id = Auth::user()->created_by;
                    $rm_id = Auth::user()->user_parent_id;
                } else {
                    $partner_id = Auth::user()->id;
                    $rm_id      = isset($request->rm_id) ? $request->rm_id : '';
                }
            }
        }
        if (Auth::user()->user_type == '1') {
            $promotion = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where(['id' => $id]);
            if ($rm_id > 0) {
                $promotion = $promotion->where('rm_id', $rm_id);
            }
            $promotion = $promotion->first();
        } else {
            $promotion = Promotion::with('promotype', 'channelpartner', 'users', 'promoCode', 'promotionUsers')->where(['id' => $id])->where('owner_id', $partner_id);
            // if ($rm_id > 0) {
            //     $promotion = $promotion->where('rm_id', $rm_id);
            // }
            $promotion = $promotion->first();
        }
        if ($promotion->total_coupons <= 1) {
            $promotion->is_custom = 1;
        } else {
            $promotion->is_custom = 0;
        }
        $total_usage = 0;
        $total_redeem_amount = 0;
        if ($promotion->promoCode) {
            $all_promocode_usage = [];
            $allPromocodes = [];
            foreach ($promotion->promoCode as $key => $value) {
                $allPromocodes[] = $value->promocode;
                // $promocode = PromoUsage::where('promocode', $value->promocode)->get();
                $promocode = PromoUsage::select(
                    'promo_usages.*',
                    't.ticket_number',
                    't.facility_id',
                    'r.facility_id',
                    'fr.full_name',
                    'fr.garage_code',
                    'ft.full_name as facility_name',
                    'ft.garage_code as facility_garage_code',
                    'r.license_plate as reservation_license_plate',
                    't.license_plate as ticket_license_plate'
                )
                    ->leftJoin('tickets as t', 't.id', '=', 'promo_usages.ticket_id')
                    ->leftJoin('reservations as r', 'r.ticketech_code', '=', 'promo_usages.reservation_id')
                    ->leftJoin('facilities as fr', 'fr.id', '=', 'r.facility_id')
                    ->leftJoin('facilities as ft', 'ft.id', '=', 't.facility_id')
                    ->where('promo_usages.promocode', $value->promocode)
                    ->get();

                if ($promocode) {
                    array_push($all_promocode_usage, $promocode);
                    $value->usage = count($promocode);
                    $total_usage += count($promocode);
                    foreach ($promocode as $k => $v) {
                        $total_redeem_amount += $v->discount_amount;
                        $promotion->promocode_usage_users = $promocode;
                    }
                }
            }
            // $response = PromoUsage::whereIn('promocode', $allPromocodes)->get();
            $response = PromoUsage::select(
                'promo_usages.*',
                't.ticket_number',
                't.facility_id',
                'r.facility_id',
                'fr.full_name',
                'fr.garage_code',
                'ft.full_name as facility_name',
                'ft.garage_code as facility_garage_code',
                'r.license_plate as reservation_license_plate',
                't.license_plate as ticket_license_plate'
            )
                ->leftJoin('tickets as t', 't.id', '=', 'promo_usages.ticket_id')
                ->leftJoin('reservations as r', 'r.ticketech_code', '=', 'promo_usages.reservation_id')
                ->leftJoin('facilities as fr', 'fr.id', '=', 'r.facility_id')
                ->leftJoin('facilities as ft', 'ft.id', '=', 't.facility_id')
                ->where('promo_usages.promocode', $allPromocodes)
                ->get();
            $promotion->promocode_usage_users = $response;
        }
        $promotion->total_usage = $total_usage;
        $promotion->total_redeem_amount = $total_redeem_amount;

        if (!$promotion) {
            throw new ApiGenericException('Couldn\'t Find Any Promotion With Given Id');
        }

        if ($request->download_excel == '1' || $request->download_excel == 1) {
            $excelSheetName = ucwords($promotion->name);
            $excelData = [];
            $increment = 1;
            foreach ($promotion->promocode_usage_users as $promoValue) {

                $excelData[] = [
                    'No.' => $increment,
                    'Promocode' => $promotion->name,
                    'Description' => $promotion->description,
                    // 'Date Used' => $promoValue->,
                    'Ticket' => $promoValue->ticket_number,
                    'Email Id' => $promoValue->email,

                    'License' => !empty($promoValue->ticket_license_plate) ? $promoValue->ticket_license_plate : $promoValue->reservation_license_plate,

                    'Facility/Lot Name' => !empty($promoValue->full_name) ? $promoValue->full_name : $promoValue->facility_name,

                    'Zone Id' => !empty($promoValue->garage_code) ? $promoValue->garage_code : $promoValue->facility_garage_code,

                    'Start Time' => $promotion->valid_from,
                    'End Time' => $promotion->valid_to,
                    'Amount' => $promoValue->discount_amount,
                ];
                $increment++;
            }
            Excel::create(
                $excelSheetName,
                function ($excel) use ($excelData, $excelSheetName) {

                    // Set the spreadsheet title, creator, and description
                    $excel->setTitle($excelSheetName);
                    $excel->setCreator('IconGo')->setCompany('Outworx Solutions Pvt Ltd');
                    $excel->setDescription('List Of Promo Codes');

                    // Freeze first row
                    // $excel->freezeFirstRow();

                    // Build the spreadsheet, passing in the payments array
                    $excel->sheet(
                        'Promo Codes',
                        function ($sheet) use ($excelData) {
                            $sheet->fromArray($excelData, null, 'A1', false, true);
                        }
                    );
                }
            )->store('xls')->download('xls');
        }

        return [
            'success' => true,
            'Promotion' => $promotion
        ];
    }

    public function getPromocodeUsers(Request $request)
    {
        $promocodes = PromoCode::where('promotion_id', $request->promotion_id)->get();

        $allPromocodes = [];
        foreach ($promocodes as $key => $value) {
            $allPromocodes[] = $value->promocode;
        }

        $promotion = Promotion::find($request->promotion_id);

        if ($request->search != 'NO') {
            /* $response  = PromoUsage::query()
            ->select('promo_usages.*', 'tickets.ticket_number')  
            ->leftJoin('tickets', 'promo_usages.ticket_id', '=', 'tickets.id')
            ->whereIn('promo_usages.promocode', $allPromocodes)
            ->where('promo_usages.email', 'like', '%' .  $request->search . '%')
            ->paginate(20); */
            /* // ->leftJoin('tickets', function($join) use ($promotion) {
                //     $join->on('promo_usages.ticket_id', '=', 'tickets.id')
                //          ->where('tickets.partner_id', '=', $promotion->owner_id); // Applying condition inside join
                // })
                // ->leftJoin('permit_requests', function($join) use ($promotion) {
                //     $join->on('promo_usages.ticket_id', '=', 'permit_requests.id')
                //          ->where('permit_requests.partner_id', '=', $promotion->owner_id); // Applying condition inside join
                // }) */
            $response  = PromoUsage::query()
                ->select(
                    'promo_usages.*',
                    'tickets.ticket_number',
                    'pa.pass_code',
                    'pr.account_number',
                    'tickets.facility_id',
                    'tickets.license_plate as ticket_license_plate',
                    'r.facility_id',
                    // 'r.license_plate as reservation_license_plate',
                    'pv.license_plate_number as reservation_license_plate',
                    'pa.license_plate as pass_license_plate',
                    'pc.valid_from',
                    'pc.valid_to',
                    'pc.usage',
                    'pro.description',
                    'users.email',
                    DB::raw(
                        "( CASE 
                            WHEN promo_usages.ticket_id > 0 THEN ft.full_name 
                            WHEN promo_usages.reservation_id !='' THEN fr.full_name 
                            WHEN promo_usages.permit_request_id > 0 THEN fp.full_name  
                            WHEN promo_usages.pass_id > 0 THEN fpa.full_name  
                            ELSE '-' END ) AS full_name"
                    ),
                    DB::raw(
                        "(CASE 
                            WHEN promo_usages.ticket_id > 0 THEN ft.garage_code 
                            WHEN promo_usages.reservation_id !='' THEN fr.garage_code 
                            WHEN promo_usages.permit_request_id > 0 THEN fp.garage_code  
                            WHEN promo_usages.pass_id > 0 THEN fpa.garage_code  
                            ELSE '' END ) AS garage_code"
                    )
                )
                ->leftJoin('tickets', 'promo_usages.ticket_id', '=', 'tickets.id')
                ->leftJoin('reservations as r', 'r.ticketech_code', '=', 'promo_usages.reservation_id')
                ->leftJoin('permit_vehicles as pv', 'pv.id', '=', 'r.vehicle_id')
                ->leftJoin('users as users', 'users.id', '=', 'tickets.user_id')
                ->leftJoin('facilities as fr', 'fr.id', '=', 'r.facility_id')
                ->leftJoin('facilities as ft', 'ft.id', '=', 'tickets.facility_id')
                ->leftJoin('promo_codes as pc', 'pc.promocode', '=', 'promo_usages.promocode')
                ->leftJoin('promotions as pro', 'pro.name', '=', 'promo_usages.promocode')
                ->leftJoin('permit_requests as pr', 'promo_usages.permit_request_id', '=', 'pr.id')
                ->leftJoin('facilities as fp', 'fp.id', '=', 'pr.facility_id')
                ->leftJoin('user_passes as pa', 'promo_usages.pass_id', '=', 'pa.id')
                ->leftJoin('facilities as fpa', 'fpa.id', '=', 'pa.facility_id')
                ->whereIn('promo_usages.promocode', $allPromocodes)
                // ->where('promo_usages.email', 'like', '%' .  $request->search . '%')

                ->where(function ($query) use ($request) {
                    $query->where('promo_usages.email', 'like', '%' . $request->search . '%')
                        ->orWhere('tickets.ticket_number', 'like', '%' . $request->search . '%')
                        ->orWhere('tickets.license_plate', 'like', '%' . $request->search . '%')
                        ->orWhere('pv.license_plate_number', 'like', '%' . $request->search . '%')
                        ->orWhere('fr.full_name', 'like', '%' . $request->search . '%')
                        ->orWhere('ft.full_name', 'like', '%' . $request->search . '%')
                        ->orWhere('ft.garage_code', 'like', '%' . $request->search . '%')
                        ->orWhere('fr.garage_code', 'like', '%' . $request->search . '%')
                        ->orWhere('r.ticketech_code', 'like', '%' . $request->search . '%');
                })
                ->whereRaw("((tickets.partner_id ='$promotion->owner_id')
                 OR (r.partner_id ='$promotion->owner_id')
                 OR (fr.owner_id ='$promotion->owner_id')
                 OR (ft.owner_id ='$promotion->owner_id')
                 OR (fp.owner_id ='$promotion->owner_id')
                 OR (fpa.owner_id ='$promotion->owner_id'))");
        } else {
            /* $response = PromoUsage::query()
            ->select('promo_usages.*', 'tickets.ticket_number')  
            ->leftJoin('tickets', 'promo_usages.ticket_id', '=', 'tickets.id')
            ->whereIn('promo_usages.promocode', $allPromocodes)->paginate(20); */
            $response  = PromoUsage::query()
                ->select(
                    'promo_usages.*',
                    'tickets.ticket_number',
                    'pa.pass_code',
                    'pr.account_number',
                    'tickets.facility_id',
                    'tickets.license_plate as ticket_license_plate',
                    'r.facility_id',
                    // 'r.license_plate as reservation_license_plate',
                    'pv.license_plate_number as reservation_license_plate',
                    'pa.license_plate as pass_license_plate',
                    'pc.valid_from',
                    'pc.valid_to',
                    'pc.usage',
                    'pro.description',
                    'users.email',
                    DB::raw(
                        "( CASE 
                            WHEN promo_usages.ticket_id > 0 THEN ft.full_name 
                            WHEN promo_usages.reservation_id !='' THEN fr.full_name 
                            WHEN promo_usages.permit_request_id > 0 THEN fp.full_name  
                            WHEN promo_usages.pass_id > 0 THEN fpa.full_name  
                            ELSE '-' END ) AS full_name"
                    ),
                    DB::raw(
                        "(CASE 
                            WHEN promo_usages.ticket_id > 0 THEN ft.garage_code 
                            WHEN promo_usages.reservation_id !='' THEN fr.garage_code 
                            WHEN promo_usages.permit_request_id > 0 THEN fp.garage_code  
                            WHEN promo_usages.pass_id > 0 THEN fpa.garage_code  
                            ELSE '' END ) AS garage_code"
                    )
                )
                ->leftJoin('tickets', 'promo_usages.ticket_id', '=', 'tickets.id')
                ->leftJoin('reservations as r', 'r.ticketech_code', '=', 'promo_usages.reservation_id')
                ->leftJoin('permit_vehicles as pv', 'pv.id', '=', 'r.vehicle_id')
                ->leftJoin('users as users', 'users.id', '=', 'promo_usages.user_id')
                ->leftJoin('facilities as fr', 'fr.id', '=', 'r.facility_id')
                ->leftJoin('facilities as ft', 'ft.id', '=', 'tickets.facility_id')
                ->leftJoin('promo_codes as pc', 'pc.promocode', '=', 'promo_usages.promocode')
                ->leftJoin('promotions as pro', 'pro.name', '=', 'promo_usages.promocode')
                ->leftJoin('permit_requests as pr', 'promo_usages.permit_request_id', '=', 'pr.id')
                ->leftJoin('facilities as fp', 'fp.id', '=', 'pr.facility_id')
                ->leftJoin('user_passes as pa', 'promo_usages.pass_id', '=', 'pa.id')
                ->leftJoin('facilities as fpa', 'fpa.id', '=', 'pa.facility_id')
                ->whereIn('promo_usages.promocode', $allPromocodes)
                ->whereRaw("((tickets.partner_id ='$promotion->owner_id')
                 OR (r.partner_id ='$promotion->owner_id')
                 OR (fr.owner_id ='$promotion->owner_id')
                 OR (ft.owner_id ='$promotion->owner_id')
                 OR (fp.owner_id ='$promotion->owner_id')
                 OR (fpa.owner_id ='$promotion->owner_id'))");
        }

        if ($request->download_excel == '1' || $request->download_excel == 1) {
            $response = $response->get();
            $excelData = [];
            $increment = 1;
            foreach ($response as $promoValue) {

                $excelSheetName = ucwords($promoValue['promocode']);
                $reference = '';
                $license_plate = '';
                if ($promoValue['ticket_number'] != '') {
                    $reference = $promoValue['ticket_number'];
                    $license_plate = $promoValue['ticket_license_plate'];
                }
                if ($promoValue['reservation_id'] != '') {
                    $reference = $promoValue['reservation_id'];
                    $license_plate = $promoValue['reservation_license_plate'];
                }
                if ($promoValue['account_number'] != '') {
                    $reference = $promoValue['account_number'];
                }
                if ($promoValue['pass_code'] != '') {
                    $reference = $promoValue['pass_code'];
                    $license_plate = $promoValue['pass_license_plate'];
                }
                $excelData[] = [
                    'No.' => $increment,
                    'Promocode' => $promoValue['promocode'],
                    'Description' => $promoValue['description'],
                    'Date Used' => $promoValue['created_at'],
                    'Transaction Reference No' => $reference,
                    'Email Id' => $promoValue['email'],

                    'License' => $license_plate,

                    // 'Facility/Lot Name' => !empty($promoValue['full_name']) ? $promoValue['full_name'] : $promoValue['facility_name'],
                    'Facility/Lot Name' => !empty($promoValue['full_name']) ? $promoValue['full_name'] : $promoValue['full_name'],

                    // 'Zone Id' => !empty($promoValue['garage_code']) ? $promoValue['garage_code'] : $promoValue['facility_garage_code'],
                    'Zone Id' => !empty($promoValue['garage_code']) ? $promoValue['garage_code'] : $promoValue['garage_code'],

                    'Start Time' => $promoValue['valid_from'],
                    'End Time' => $promoValue['valid_to'],
                    'Amount' => $promoValue['discount_amount'],
                ];
                $increment++;
            }
            Excel::create(
                $excelSheetName,
                function ($excel) use ($excelData, $excelSheetName) {

                    // Set the spreadsheet title, creator, and description
                    $excel->setTitle($excelSheetName);
                    $excel->setCreator('IconGo')->setCompany('Outworx Solutions Pvt Ltd');
                    $excel->setDescription('List Of Promo Codes');

                    // Build the spreadsheet, passing in the payments array
                    $excel->sheet(
                        'Monitor Promo Codes',
                        function ($sheet) use ($excelData) {
                            $sheet->fromArray($excelData, null, 'A1', false, true);
                        }
                    );
                }
            )->store('xls')->download('xls');
        }
        $response  = $response->paginate(20);
        return $response;
    }


    /*public function getAllPromocodes(Request $request)
    {  
        $owner_id = 0;
        $userData = User::where('id',Auth::user()->id)->first();
        if($userData->user_type =='1' || $userData->user_type=='2')
        {
            $promotions = PromoCode::with('promotype','channelpartner','promotion');     
            if ($request->search) {
                 if($request->search == 'NO'){
                    $promotions = PromoCode::with(['promotype','channelpartner','promotion' => function($query) use($request){
                        $query->select('promotions.*');
                        if($request->owner_id != ''){
                        $query->where('promotions.owner_id', $request->owner_id);
                        }
                    }])->where('status', $request->status)->orderBy('id','DESC')->paginate(20);     
                        return $this->getTotalPromocodeUsage($promotions);
                 }else{
                    $promotions = PromoCode::with(['promotype','channelpartner','promotion' => function($query) use($request){
                        $query->select('promotions.*');
                        if($request->owner_id != ''){
                        $query->where('promotions.owner_id', $request->owner_id);
                        }
                    }])->where('status', $request->status);     
                    
                        $promotions = QueryBuilder::buildSearchQuery($promotions, $request->search, PromoCode::$searchFields)->orWhereHas(
                    'promotion', function ($query) use ($request) {
                        $query
                            ->where('owner_id', '=', "{$request->owner_id}");
                    }
                );
                   $promotions = $promotions->orderBy('id','DESC')->paginate(20);    
                    return $this->getTotalPromocodeUsage($promotions);
                 }
                
          }
          $promotions  = $promotions->orderBy('id','DESC')->paginate(20);          
          return $this->getTotalPromocodeUsage($promotions);

        }else{
            if($userData->user_type =='3')
            {
                $owner_id = $userData->id;
                
            }else if($userData->user_type =='4')
            {
                $owner_id = $userData->created_by;
            }
            $partnerPromo = Promotion::select('id')->where('owner_id', $owner_id)->get();
            $promotions = PromoCode::with('promotype','channelpartner','promotion')->whereIn('promotion_id', $partnerPromo);     

            if ($request->search) {
                
             $promotions = PromoCode::with('promotype','channelpartner','promotion')->whereIn('promotion_id', $partnerPromo);     
             $promotions = QueryBuilder::buildSearchQuery($promotions, $request->search, PromoCode::$searchFields);
            $promotions = $promotions->orderBy('id','DESC')->paginate(20);
            return $this->getTotalPromocodeUsage($promotions);
            
            }
        }
        $promotions = $promotions->orderBy('id','DESC')->paginate(20);
        return $this->getTotalPromocodeUsage($promotions);
    }*/

    /*public function getAll()
    {  
            $promotions =DB::select(
                   "select id,name,total_coupons,status,valid_from,valid_to,promo_type_id,percentage_off_discount, dollar_off_discount,
                (CASE 
                WHEN promo_type_id = 2  
                THEN (select count(distinct(PC.promocode)) as promo_exposed from promo_codes 
                as PC where PC.is_expired = 1 and PC.promotion_id = promotions.id) 
                WHEN promo_type_id = 1 
                THEN (select count(distinct(PU.user_id)) as promo_exposed from promo_codes 
                as PC, promo_usages as PU where PC.promocode = PU.promocode and PC.promotion_id = promotions.id)              
                ELSE 0 
                END)as consume_promo,
               (CASE 
                WHEN promo_type_id = 2  
                THEN 0 
                WHEN promo_type_id = 1 
                THEN (select promocode as promocode from promo_codes 
                as PC where PC.promotion_id = promotions.id)              
                ELSE 0 
                END)as promo_code

                 from promotions"
               );

             $newPromotions = $this->prepareArrayData($promotions);
             $savepromotions =DB::select(
                   "select count(*) as 'Saved_Promocodes', p.name as 'Promotion' , p.id as 'id' from 
                    users_promo_codes as upc, promo_codes as pc, 
                    promotions as p  where 
                    upc.promo_code_id = pc.id and 
                    pc.promotion_id = p.id
                    group by p.id"
               );
        $newSavePromotions = $this->prepareArrayData($savepromotions);
         
        foreach ($newPromotions as $id => $value) {
            if (isset($newSavePromotions[$id])) {
                $newPromotions[$id]['Saved_Promocodes'] = $newSavePromotions[$id]['Saved_Promocodes'];
            } else {
                $newPromotions[$id]['Saved_Promocodes'] = 0;
            }
        }
    

          return ['promotions' => $newPromotions];
    }*/

    public function generatePromotionQrCode($id)
    {
        $promotion = Promotion::with('promoCode')->where(['id' => $id])->first();

        if ($promotion->total_coupons <= 1) {
            $promotion->is_custom = 1;
        } else {
            $promotion->is_custom = 0;
        }
        //return $promotion;

        if (!$promotion) {
            throw new ApiGenericException('Please Provide Valid Promotion Id');
        }

        if ($promotion->is_custom == '1') {
            $settings = BrandSetting::where('user_id', $promotion->owner_id)->first();
            $promotion->brand_setting = $settings;
            $pdf = $this->generatePromocodePdf($promotion, Pdf::class);
            return $pdf;
        } else {
            throw new ApiGenericException('Promocode is not Custom Type');
        }
    }

    public function generatePromocodePdf($data, $format = Image::class)
    {
        $data = ['data' => $data];

        //return $data;
        if ($format == Image::class) {
            $html = view("download.promocode_pdf", $data)->render();
        } else {
            $html = view("download.promocode_pdf", $data)->render();
        }

        $image = app()->make($format);
        return $image->getOutputFromHtmlString($html);
    }
}
