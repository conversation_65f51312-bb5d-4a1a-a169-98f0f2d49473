<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

use Illuminate\Http\Request;

use App\Http\Requests;

use App\Models\PasswordReset;
use App\Models\User;

use App\Services\Mailers\UserMailer;

use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserNotAuthorized;
use App\Http\Helpers\MailHelper;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\BrandSetting;
use App\Models\ParkEngage\FacilityBrandSetting;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Helpers\QueryBuilder;

/**
 * Note that we are reimplementing the default laravel password reset
 * functionality because laravel password resets work with views,
 * which are not compatible with single page apps
 */
class PasswordController extends Controller
{

    /**
     * Send email to reset a user's password
     *
     * @param Request $request [description]
     */
    public function sendPasswordResetEmail(Request $request)
    {
        $brand_setting = '';
        $partner_id = '';
        //get the partner id from admin
        if (isset($request->partner_id) && !empty($request->partner_id)) {
            $user = User::where('email', $request->email);
            if ($request->user_type) {
                $user = $user->where('user_type', $request->user_type);
            }
            $user = $user->first();
            // ->where('created_by', $request->partner_id)
            if ($user->user_type == '3') {
                $brand_setting = BrandSetting::where('user_id', $user->id)->first();
                if (!$brand_setting) {
                    //    throw new ApiGenericException('Partner Brand Setting not Configured Properly.');
                }
                $partner_id = $user->id;
            } else {
                $user = User::where('email', $request->email)->where('created_by', $request->partner_id)->first();
            }
            
            if ($user->user_type == '4' && $user->created_by == '1') {
                $partner_id = $user->created_by;
            } else {
                $brand_setting = BrandSetting::where('user_id', $request->partner_id)->first();
                if (!$brand_setting) {
                    throw new ApiGenericException('Partner Brand Setting not Configured Properly.');
                }
                $partner_id = $request->partner_id;
            }
        } else {
            $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
            if (!$secret) {
                throw new ApiGenericException('Partner not found.');
            }
            $partner_id = $secret->partner_id;

            if ($secret) {
                $brand_setting = BrandSetting::where('user_id', $secret->partner_id)->first();
            }
        }
        $this->validate(
            $request,
            [
                'email' => 'required|email|exists:users,email'
            ],
            [
                'email.exists' => 'This email is not registered with us, please provide correct email id'
            ]
        );

        // Generate password reset token
        $reset = PasswordReset::firstOrNew(['email' => $request->email]);
        $reset->generatePasswordResetToken();
        $reset->created_at = Carbon::now()->toDateTimeString();
        $reset->save();

        if (isset($request->is_admin_user) && $request->is_admin_user == 1) {
            $user = User::where('email', $request->email)->first();
            if (!$user) {
                throw new NotFoundException('We couldn\'t find any user with this email');
            }
        } else {
            // $user = User::where(['email' => $request->email, 'anon' => '0'])->where('created_by', $partner_id)->first(); // PIMS - 13230 
            // if (!$user) {
            //     throw new NotFoundException('We couldn\'t find any user with this email');
            // }
            if (isset($secret) && $secret->id == 'inventory-demo') {
                $user = User::where(['email' => $request->email, 'anon' => '0'])->where('user_type', '!=', '5')->first(); // PIMS - 13230 
            } else {

                if ($partner_id == '1') {
                    $user = User::where(['email' => $request->email, 'anon' => '0'])->where('user_type', '!=', '3')->first(); // PIMS - 13230 
                } else if (isset($user) && ($partner_id == $user->id) && ($user->user_type == '3')) {
                } else {
                    $user = User::where(['email' => $request->email, 'anon' => '0'])->where('created_by', $partner_id)->where('user_type', '!=', '3')->first(); // PIMS - 13230 
                }
                if (!$user) {
                    throw new NotFoundException('No user with the given email address found or guest user.');
                }
                if ($user->id == $partner_id) {
                } else {
                    $user = User::where(['email' => $request->email, 'anon' => '0'])->where('created_by', $partner_id)->first();
                }
            }
			if (!$user) {
                throw new NotFoundException('We couldn\'t find any user with this email');
            }
        }

        if ($user->user_type == '3') {
            // For partner
            $brand_setting = BrandSetting::where('user_id', $user->id)->first();
            $secret = OauthClient::where('partner_id', $user->id)->first();
            $partnerId = $user->id;
        } else {
            // for all others user type 
            $brand_setting = BrandSetting::where('user_id', $user->created_by)->first();
            $secret = OauthClient::where('partner_id', $user->created_by)->first();
            $partnerId = $user->created_by;
        }
        // if (empty($brand_setting)) {
        //     $brand_setting = BrandSetting::where('user_id', $user->created_by)->first();
        // }
        // if ($brand_setting) {
        //     $secret = OauthClient::where('partner_id', $user->created_by)->first();
        // } else {
        //     $secret = OauthClient::where('secret', $request->secret)->first();
        // }

        $facility_brand_setting = '';
        if ($user->user_type == '5') {
            $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("CUSTOMERPORTAL_URL", $secret->partner_id);
        } else {
            $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("WEB_URL", $partner_id);
        }
        if (isset($secret) && !empty($secret)) {
            $payment_gateway = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
            if (isset($payment_gateway) && $payment_gateway->reset_password_url != '') {
                if (isset($brand_setting) && $brand_setting->user_id == config('parkengage.PARTNER_DIAMOND')) {
                    $reset_password_url = env('WEB_URL_WAILUKU') . "/reset-password/";
                } else {
                    if ($payment_gateway->user_id == config('parkengage.PARTNER_PCI')) {
                        if ($request->facility_id) {
                            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($request, $payment_gateway) {
                                $join->on('user_facilities.user_id', '=', 'users.id');
                                $join->where('user_facilities.facility_id', "=", $request->facility_id);
                            })->where('created_by', $payment_gateway->user_id)->whereNotNull('slug')->where('user_type', 12)->first();
                            $reset_password_url = env('WEB_URL_CUSTOMERPORTAL_PCI') . $getRM->slug . "/reset-password/";
                            $facility_brand_setting = FacilityBrandSetting::where('user_id', $payment_gateway->user_id)->first();
                        } else {
                            $reset_password_url = env('WEB_URL_CUSTOMERPORTAL_PCI') . $payment_gateway->reset_password_url . "/reset-password/";
                        }
                    } else if ($payment_gateway->user_id == config('parkengage.PARTNER_MAPCO')) {
                        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->where('created_by', $payment_gateway->user_id)->whereNotNull('slug')->where('user_type', 12)->first();
                        if ($getRM) {
                            $facility_brand_setting = FacilityBrandSetting::where('user_id', $payment_gateway->user_id)->first();
                            $reset_password_url = env('WEB_URL_CUSTOMERPORTAL') . "/" . $getRM->slug . "/reset-password/";
                        } else {
                            $reset_password_url = env('WEB_URL_CUSTOMERPORTAL') . "/" . $payment_gateway->reset_password_url . "/reset-password/";
                        }
                    } else {

                        if ($dynamicReceiptUrl) {
                            $url = $dynamicReceiptUrl->value;
                            $reset_password_url = $url . "/" . $payment_gateway->reset_password_url . "/reset-password/";
                        } else {
                            $reset_password_url = env('WEB_URL_CUSTOMERPORTAL') . "/" . $payment_gateway->reset_password_url . "/reset-password/";
                        }
                    }
                }
            } else {
                if ($dynamicReceiptUrl) {
                    $url = $dynamicReceiptUrl->value;
                    $reset_password_url = $url . "/reset-password/";
                } else {
                    $reset_password_url = config('parkengage.password_reset_url') . "/reset-password/";
                }
            }
        } else {
            if ($dynamicReceiptUrl) {
                $url = $dynamicReceiptUrl->value;
                $reset_password_url = $url;
            } else {
                $reset_password_url = config('parkengage.password_reset_url');
            }
        }



        if ($user->user_type != '5') {
            //   $reset_password_url = config('parkengage.password_reset_url');
        }
		//dd($user->created_by,$user,$request->base_url);
        //dd($request->base_url,'https://preprod-backoffice.rev-pass.com',$partner_id);
		// get the base url from frontend with partner/RM Slug
        if ($request->base_url) {
            if ($user->user_type == '3') {
                if ($user->id != config('parkengage.PARTNER_RevPass')) {
                    $reset_password_url = config('parkengage.password_reset_url');
                } else if ($user->id == config('parkengage.PARTNER_PCI')) {
                    $reset_password_url = env('WEB_URL_CUSTOMERPORTAL_PCI') . "reset-password/";
                } else {
                    if ($dynamicReceiptUrl) {
                        // $url = $dynamicReceiptUrl->value;
                        // $reset_password_url = $url . "/reset-password/";
                        $reset_password_url = $request->base_url . "/reset-password/";
                    } else {
                        $reset_password_url = $request->base_url . "/reset-password/";
                    }
                }
            } else {
                if ($user->created_by != config('parkengage.PARTNER_RevPass')) {
                    $reset_password_url = config('parkengage.password_reset_url');
                    if ($request->base_url) {
                        $reset_password_url = $request->base_url . "/reset-password/";
                    }
                } else if ($user->created_by == config('parkengage.PARTNER_PCI')) {
                    $reset_password_url = env('WEB_URL_CUSTOMERPORTAL_PCI') . "reset-password/";
                } else {
                    if ($dynamicReceiptUrl) {
                        // $url = $dynamicReceiptUrl->value;
                        // $reset_password_url = $url . "/reset-password/";
                        $reset_password_url = $request->base_url . "/reset-password/";
                    } else {
                        $reset_password_url = $request->base_url . "/reset-password/";
                    }
                }
            }
        } else if (!isset($request->base_url) && isset($request->partner_id) && ($user->user_type != '5')) {
            if ($dynamicReceiptUrl) {
                $url = $dynamicReceiptUrl->value;
                $reset_password_url = $url . "/reset-password/";
            } else {
                $reset_password_url = config('parkengage.password_reset_url');
            }

            //    dd('11', $user->user_type, $reset_password_url, $payment_gateway, $payment_gateway->reset_password_url, $dynamicReceiptUrl);
        }

        $mailer = new UserMailer($user);
        $subject = "Password Reset Request";
        $data = [
            'subject' => $subject,
            'token' => $reset->token,
            'email' => $request->email,
            //  'resetLink' => config('parkengage.password_reset_url') . $reset->token
            'resetLink' => $reset_password_url . $reset->token,
            'brand_setting' => $brand_setting,
            'facility_brand_setting' => $facility_brand_setting,
            'partner_id'   => isset($secret->partner_id) ? $secret->partner_id : $user->created_by
        ];

        Log::info("Issue in email sending : {$user->email}");
        //Artisan::queue('checkin-email-send', array('id' => '','slug'=>'forgot-password','reset'=>$data,'signup' => ''));

        // Change Request PIMS-12502 : Vijay - 30-01-2025 
        // $partnerId = $user->created_by;

        try {
            if ($brand_setting) {
                MailHelper::sendEmail($user->email, "parkengage.password-reset-partner", $data, $partnerId);

                // Mail::send(
                //     "parkengage.password-reset-partner",
                //     $data,
                //     function ($message) use ($user) {
                //         $message->to($user->email)->subject("Password Reset Request");
                //         $message->from(config('parkengage.default_sender_email'));
                //     }
                // );
            } else {
                MailHelper::sendEmail($user->email, "parkengage.password-reset", $data, $partnerId);

                // Mail::send(
                //     "parkengage.password-reset",
                //     $data,
                //     function ($message) use ($user) {
                //         $message->to($user->email)->subject("Password Reset Request");
                //         $message->from(config('parkengage.default_sender_email'));
                //     }
                // );
            }
        } catch (\Throwable $th) {
            $msg = "Error Message: " . $th->getMessage() . ", File: " . $th->getFile() . ", Line Number: " . $th->getLine();
            Log::error("Issue in email sending : {$msg}");
            throw new NotFoundException('Email send fail, please check after some time.');
            return ['sent' => false, 'message' => 'Email send fail, please check after some time.' . $msg];
        }
        // Change Close : PIMS-12502



        return ['sent' => true, 'message' => 'Email has been sent to your email address.'];
    }

    /**
     * Reset user's password
     *
     * @param Request $request [description]
     */
    public function resetPassword(Request $request)
    {
        $this->validate(
            $request,
            [
                'token' => 'required|string',
                'password' => 'required|string|min:4',
                'password_confirmation' => 'required|string|min:4|same:password'
            ]
        );

        $reset = PasswordReset::where('token', $request->token)->first();

        if (!$reset) {
            throw new NotFoundException('The password creation link is expired. Please get in touch with your Administrator for further assistance.');
        }
        // PIMS - 13230 : Vijay
        $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
        // $user = User::where(['email' => $reset->email, 'created_by' => $secret->partner_id, 'anon' => '0']);
        // Ujjwal Change : 26-03-2025 Deployed
        if (isset($secret) && !empty($secret)) {
            $user = User::where(['email' => $reset->email, 'created_by' => $secret->partner_id, 'anon' => '0']);
            if ($secret->id == "inventory-demo" && ($secret->secret == "wqhqdz9pmkmhwug9pz0oowvrz" || $secret->secret == "Wqhqdz9PMKMHWUG9pZ0Oowvrz")) {
                // return $secret->secret;
                $user = User::where(['email' => $reset->email, 'anon' => '0']);  // this for admin users only 
            }
            // dd($reset->email, $user->first());
            $partner_user = User::where('id', $secret->partner_id)->first();
            if (isset($partner_user) && ($partner_user->user_type == '3')) {
                $partner_id = $partner_user->id;
                $user = $user->where('created_by', $partner_id);
            }
        } else {
            // else part
            $user = User::where(['email' => $reset->email, 'anon' => '0']);
        }
        $user = $user->first();

        if (!$user) {
            throw new NotFoundException('No user with the given email address found.');
        }

        if (Hash::check($request->password, $user->password)) {
            throw new NotFoundException('New password and old password should not be same.');
        }

        // Update password
        $user->password = Hash::make($request->password);
        $user->legacy_authentication = 0;
        $user->anon = 0;
        $user->save();

        // Delete reset token
        $reset->delete();

        return $user;
    }

    /**
     * user password reset from admin
     *
     * @param  Request $request
     * @return mixed
     */
    public function resetPasswordAdmin(Request $request, User $user)
    {
        $this->validate(
            $request,
            [
                'password' => 'required|string|min:4',
                'password_confirmation' => 'required|string|min:4|same:password'
            ]
        );

        if ($user->hasCmsAccess && !Auth::user()->isAdmin) {
            throw new UserNotAuthorized("You must be an admin to change passwords of users with backend access.");
        }

        // Update to new password
        $user->password = Hash::make($request->password);
        $user->legacy_authentication = 0;
        $user->save();

        return $user;
    }

    /**
     * Reset a password if their current password has been provided
     *
     * @param Request $request [description]
     * @param User    $user    [description]
     */
    public function resetPasswordFromCurrent(Request $request, User $user)
    {
        $this->validate(
            $request,
            [
                'old_password' => 'string|required',
                'password' => 'required|string|min:4',
                'password_confirmation' => 'required|string|min:4|same:password'
            ]
        );

        // Check authed user is this user
        if (Auth::user()->id !== $user->id) {
            throw new UserNotAuthorized('User is not authorized to make changes to this account.');
        }

        // Check password matches
        if (!Hash::check($request->old_password, $user->password)) {
            throw new ApiGenericException('Old password does not match.');
        }

        if (Hash::check($request->password, $user->password)) {
            throw new ApiGenericException('Old password and new password can not be same.');
        }

        // Update to new password
        $user->password = Hash::make($request->password);
        $user->legacy_authentication = 0;
        $user->save();

        return $user;
    }
}
