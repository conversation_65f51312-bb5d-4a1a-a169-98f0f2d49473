<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests;

use App\Exceptions\ApiGenericException;
use Illuminate\Support\Facades\Validator;
use App\Models\Facility;
use App\Models\CouponThreshold;

class CouponThresholdController extends Controller
{
    public function getThreshold($facility_id)
    {
        return CouponThreshold::where('facility_id',$facility_id)->get();
    }
    
    public function addThreshold(Request $request)
    {
        if($request->ids){
            $validator = Validator::make(
            $request->all(), ['facility_id' => 'required|numeric|min:1']);
            if ($validator->fails()) {
                foreach ($validator->errors()->getMessages() as $key => $error) {
                    $errors = $error[0];
                }
                throw new ApiGenericException($errors, 422);
            }
        }else{
            $validator = Validator::make(
            $request->all(), ['facility_id'           => 'required|numeric|min:1', 'threshold' => 'required|array',
                                  'threshold.*.threshold' => 'sometimes|required|numeric',
                                  'threshold.*.uptick'    => 'sometimes|numeric',], $messages =
                    ['threshold.*.threshold.numeric'  => 'Minimum threshold accepts only numbers',
                     'threshold.*.threshold.required' => 'Minimum threshold is a required field',
                     'threshold.*.threshold.min'      => 'Minimum Threshold cannot be less than 0',
                     'threshold.*.uptick.numeric'     => 'Values field should contain only numbers',
                     'threshold.*.uptick.min'         => 'Values field cannot be zero',
                     'threshold.*.uptick.required'    => 'Values is a required field',]);
            if ($validator->fails()) {
                foreach ($validator->errors()->getMessages() as $key => $error) {
                    $errors = $error[0];
                }
                throw new ApiGenericException($errors, 422);
            }
        }
    	
    	if(count($request->threshold)>6){
    		throw new ApiGenericException('Only 6 records are allowed to maintain', 422);
    	}
    	foreach ($request->threshold as $threshold) {
            if ($threshold['uptick_type'] == '' || !in_array($threshold['uptick_type'], ['price', 'percentage'])) {
                throw new ApiGenericException('Please choose either price/percentage for $/% field', 422);
            } 

        }

        if($request->ids){
            foreach ($request->ids as $id) {
                if($id!==0){
                    $couponThreshold = CouponThreshold::find($id);
                    
                    if(!$couponThreshold){
                        throw new ApiGenericException("Coupon Threshold id does not exist.", 422);
                    }
                    $couponThreshold->delete();
                }
            }
        }

    	foreach ($request->threshold as $threshold) {
           $result = $this->addUpdateThreshold($threshold, $request->facility_id);
        }
        return $this->getThreshold($request->facility_id);
    }

    public function deleteThreshold(Request $request)
    {
    	$validator = Validator::make(
            $request->all(), ['facility_id' => 'required|numeric|min:1', 'ids' => 'required|array']);
    	if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }
            throw new ApiGenericException($errors, 422);
        }   	
    	try {
    		foreach ($request->ids as $id) {
	    		if($id!==0){
		    		$couponThreshold = CouponThreshold::find($id);
		    		
		    		if(!$couponThreshold){
    					throw new ApiGenericException("Coupon Threshold id does not exist.", 422);
    				}
		    		$couponThreshold->delete();
		    	}
        	}
    	}catch(ApiGenericException $e){
    		throw new ApiGenericException($e->getMessage(), 422);
    	}
        return $this->getThreshold($request->facility_id);
        
    }

    public function addUpdateThreshold($threshold, $facility_id)
    {
    	if($threshold['id']!==0){
    		$couponThreshold = CouponThreshold::find($threshold['id']);
    		if(!$couponThreshold){
    			throw new ApiGenericException("Coupon Threshold id does not exist.", 422);
    		}
    	}
    	else{
    		$couponThreshold = new CouponThreshold();
    		$couponThreshold->facility_id = $facility_id;
    	}
	  	$couponThreshold->threshold = $threshold['threshold'];
    	$couponThreshold->uptick_type = $threshold['uptick_type'];
    	$couponThreshold->uptick = $threshold['uptick'];
    	$couponThreshold->save();
        return $couponThreshold;
    }
}
