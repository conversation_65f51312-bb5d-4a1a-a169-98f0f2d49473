<?php

namespace App\Exceptions;

// Use for cases where we are trying to create a record that already exists.
// e.g. if we try to create an account with an account ID that already exists
// in the database, or we try to create an auth net cim for a cim taht already exists
class DuplicateEntryException extends ApiException
{
    protected $status = 404;

    public function __construct($message, array $detail = [])
    {
        parent::__construct($message, $this->status, $detail);
    }
}
