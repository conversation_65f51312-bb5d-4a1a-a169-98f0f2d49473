<?php

namespace App\Classes;

use App\Models\ElimiwaitAccount;
use App\Models\ElimiwaitRequest;
use App\Models\ElimiwaitToken;
use App\Models\FacilityType;
use App\Models\MonthlyParkingUser;

use App\Exceptions\ApiGenericException;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Carbon\Carbon;

class Elimiwait
{

    const ALL_LOCATIONS_ACTIVE = 'all';

    public $body;

    protected $baseUrl;
    protected $elimiwaitAccount;
    protected $facility;
    protected $tenantSpot;

    public function __construct(ElimiwaitAccount $elimiwaitAccount)
    {
        $this->elimiwaitAccount = $elimiwaitAccount;
        $this->facility = $this->elimiwaitAccount->monthlyParkingUser->facility;

        $this->baseUrl = config('elimiwait.base_url');
        $this->client = $this->createClient();
    }

    public function requestCar($time)
    {
        $this->body = [
            'CompanyId' => config('elimiwait.credentials.id'),
            'LocationId' => $this->facility->elimiwait_location_id,
            'PickupRequestTime' => $time,
            'TicketNumber' => $this->elimiwaitAccount->tenant_spot,
            'From' => $this->elimiwaitAccount->cell
        ];

        $json = $this->makeRequest('post', '/request');

        $elimiwaitRequest = new ElimiwaitRequest();
        $elimiwaitRequest->elimiwait_account_id = $this->elimiwaitAccount->id;
        $elimiwaitRequest->request_id = $json->RequestId;
        $elimiwaitRequest->pick_up_time = $time;
        $elimiwaitRequest->save();

        return $elimiwaitRequest;
    }

    public function getActiveRequests()
    {
        $companyId = $this->facility->elimiwaitCompanyId();
        $locationId = $this->facility->elimiwait_location_id;
        $tenantSpot = $this->elimiwaitAccount->tenant_spot;

        $endpoint = "/site/{$companyId}/location/{$locationId}/ticket/{$tenantSpot}?offset=0&limit=100";

        $response = $this->makeRequest('get', $endpoint);

        return $response;
    }

    protected function createClient()
    {
        return new Client(
            [
            'base_uri' => $this->baseUrl,
            'headers' => [
            'Authorization' => 'Bearer ' . $this->getAccessToken()
            ]
            ]
        );
    }

    protected function getAccessToken()
    {
        if ($token = ElimiwaitToken::current()->first()) {
            return $token->access_token;
        }

        try {
            $this->body =  [
                'grant_type' => 'password',
                'username' => config('elimiwait.credentials.username'),
                'password' => config('elimiwait.credentials.password')
            ];

            $response = (new Client(['base_uri' => $this->baseUrl]))->request('POST', '/token', ['form_params' => $this->body]);

            if ($response->getStatusCode() != 200) {
                throw new ApiGenericException($response->getBody()->getContents());
            }

            $json = json_decode($response->getBody());

            $token = ElimiwaitToken::firstOrNew([]);
            $token->access_token = $json->access_token;
            $token->save();

            return $token->access_token;
        } catch (ClientException $e) {
            $response = $e->getResponse();
            throw new ApiGenericException($response->getBody()->getContents());
        }
    }

    protected function makeRequest($type, $url)
    {
        try {
            $response = $this->client->$type($url, $this->body ? ['json' => $this->body] : []);
            if ($response->getStatusCode() == 200) {
                return json_decode($response->getBody());
            }
            throw new ApiGenericException($response->getStatusCode());
        } catch (ClientException $e) {
            $response = $e->getResponse();
            throw new ApiGenericException($response->getBody()->getContents());
        }
    }
}
