<?php

namespace App\Classes;
use SoapClient;
use Carbon\Carbon;
use View;

use App\Models\Reservation;
use App\Models\Rate;
use App\Models\Facility;
use App\Models\TicketechResponse;

use App\Exceptions\TicketechException;
use Log;
use App\Services\LoggerFactory;
use Auth;
/**
 * Utility class for interacting with the Ticketech APIs
 */
class Ticketech
{

    /**
     * Reservation statuses. Not sure why active is 0 and cancel is 1
     * but taken from old telecharge codebase so not going to change it
     */
    const STATUS_ACTIVE = 0;
    const STATUS_CANCEL = 1;

    /**
     * WSDL for interacting with coupons
     *
     * @var [type]
     */
    protected $couponsWsdl;
    
    protected $log;
    protected $log_ticketech;
    
    /**
     * WSDL for interacting with reservations
     *
     * @var [type]
     */
    protected $reservationsWsdl;

    protected $guid;

    protected $username;

    protected $password;

    protected $locations;

    protected $coupon;

    protected $ticketech_promo_code;

    public function __construct()
    {
        $this->couponsWsdl  =  config('ticketech.wsdl');
        $this->guid         =  config('ticketech.guid');
        $this->username     =  config('ticketech.username');
        $this->password     =  config('ticketech.password');
        $this->locations    = ['MLH000']; // Default location code valid for all locations, overriden if a coupon is set
        $this->status       = self::STATUS_ACTIVE;
        $logFactory         =  new  LoggerFactory(); 
        $this->log = $logFactory->setPath('logs/ticketech_coupos_code')->createLogger('ticketech_coupos_code');
        $this->log_ticketech = $logFactory->setPath('logs/ticketech_response_data')->createLogger('ticketech_response_data');
    }

    public function setCoupon(Rate $coupon)
    {
        $this->coupon = $coupon;
        $this->ticketech_promo_code = $coupon->ticketech_coupon_code;
        $this->price = $coupon->price;
        $this->expiration_date = strtotime('+1 day', strtotime('2017-01-01 00:00:00'));

        if (isset($coupon->facility->ticketech_id) && !empty($coupon->facility->ticketech_id)) {
            $this->locations = [$coupon->facility->ticketech_id];
        }
        $this->log->info("coupon ID - " .$coupon->id." price - ".$this->price." and ticketechid - ".implode(" ",$this->locations));
       
        return $this;
    }

    /**
     * Generates coupon codes from the Ticketech API
     *
     * The entire method is wrapped with a try / catch to trap any errors that might occur that are unknown and unexpected.
     * Although Laravel was catching exceptions first so the try / catch might be effective. Requires further testing
     *  todo : troubleshoot try / catch trapping for SoapFault and Exceptions before laravel traps them. Possible global handler.
     *
     * @return array|bool           : Function returns false on error, otherwise will return an array of codes
     */
    public function generateCouponCode()
    {
        if (!$this->validateGenerationAction()) {
            return false;
        }

        try {
            $client = new SoapClient($this->couponsWsdl, array('trace' => 0, "exceptions" => 0));

            $data = new \stdClass();
            $data->ProviderGUID = $this->guid;
            $data->UserName = $this->username;
            $data->Password = $this->password;
            $data->Locations = $this->locations;
            $data->CouponQuantity = 1;
            $data->CouponExpirationDate = date("Y-m-d", strtotime("+30 days"));
            $data->ProgramId = $this->ticketech_promo_code;
            $data->CouponValue = 0;
            $data->CouponType = 1;
            $data->PromotionID = "";
            $data->CouponPaidPrice = $this->price;

            // call the ticketech service for the result
            $resultSet = $client->GenerateCoupons($data);

            // convert the csv string result into an array
            $codes = explode(",", $resultSet->GenerateCouponsResult);

            // shift the first index off and that will indicate if an error was encountered
            $result = array_shift($codes);

            // the value of "E" indicates an error (example: "E,INVALID PROGRAMID").
            // the value of "A" indicates success.
            // other values unknown
            switch ($result) {
            case "A":
                return $this->codes = $codes[0];
            case "E":
                return $this->error($codes[0]);
            default:
                return $this->error('unknown error');
            }
        } catch (\Exception $e) {
            return $this->error("Unknown error: " . $e->getMessage());
        }
    }

    public function makeReservation(Reservation $reservation, $username=null)
    {
        $this->status = self::STATUS_ACTIVE;
        return $this->sendReservation($reservation, $username);
    }

    public function cancelReservation(Reservation $reservation, $username=null) 
    {
        $this->status = self::STATUS_CANCEL;
        return $this->sendReservation($reservation, $username);
    }

    /**
     * Attempt to register a reservation with the ticketech API
     * If the facility associated with the reservation does not have a ticketech ID, return false
     * Otherwise return the ticketech reservation GUID. Can cancel and create a reservation depending on
     * $this->status
     *
     * @param  Reservation $reservation [description]
     * @return bool|string False if no reservation could be made, ticketech GUID otherwise
     */
    protected function sendReservation(Reservation $reservation, $username=null)
    {
        $facility_id = $reservation->facility->ticketech_id;

         if (!isset($facility_id) || !$facility_id) {
            //return false;
            if($this->status===self::STATUS_ACTIVE){
                throw new TicketechException(
                    'Could not make reservation with ticketing service. Facility Id does not exist',
                    500,
                    ['detail' => 'Facility Id does not exist', 'reservation_id' => null]
                );
            }
            else{
                return false;
            }
            
        }

        $now = Carbon::now();
        $startTime = new Carbon($reservation->start_timestamp);
        $dateFormat = 'm/d/Y h:i:s A';

        if(!$username || $username==null)
        {
            if($this->status == self::STATUS_CANCEL)
            {
                if(isset($reservation->transaction->payment_profile_id) && ($reservation->transaction->payment_profile_id!=''))
                { 
                  $username = isset($reservation->user->name)?$reservation->user->name:'';
                }
                
                if(!$username || $username==null)
                { 
                    $username = isset($reservation->transaction->name)?$reservation->transaction->name:'';
                }
           
            }else{
                $username = isset($reservation->transaction->name)?$reservation->transaction->name:'';
            }
            
            if(!$username || $username==null)
            {
                $userDataVal = Auth::user();
                if($userDataVal)
                {
                     $username = isset($userDataVal->name)?$userDataVal->name:'';
                }
            }
            
        }
        
        if (!is_string($username)) { 
            $firstName = false;
            $lastName = false;
        } else {
            $payment_name_check = $name = explode(' ', trim($username));
            $firstName = array_shift($name);
            $lastName = count($name) ? array_pop($name) :array_pop($payment_name_check) ;

        }

        $data = View::make('ticketech.reservation', [
            'guid' => $reservation->ticketech_guid ?: '',
            'status' => $this->status,
            'location' => $reservation->facility->ticketech_id,
            'barcode' => $reservation->ticketech_code,
            'offerStart' => $startTime->format($dateFormat),
            'offerEnd' => $reservation->end_time->format($dateFormat),
            'firstName' => $firstName,
            'lastName' => $lastName,
            'email' => $reservation->user->email,
            'modified' => $now->format($dateFormat),
            'oversize' => $reservation->loyalty_point_used ? 'OVERSIZE' : '',
            'amount' => $reservation->total,
            'couponCode' => ($reservation->rate && $reservation->rate->is_coupon) ? $reservation->rate->coupon_code : false
        ])->render();
        
        if ($reservation->ticketech_code && substr($reservation->ticketech_code, 0, 2) == 'IR')
        {
            $requestData = [
                'ProviderGuid' => config('ticketech.loyalty.guid'),
                'Username' => config('ticketech.loyalty.username'),
                'Password' => config('ticketech.loyalty.password'),
                'Data' => trim($data)
            ];
        }
        else {
            $requestData = [
                'ProviderGuid' => config('ticketech.reservations.guid'),
                'Username' => config('ticketech.reservations.username'),
                'Password' => config('ticketech.reservations.password'),
                'Data' => trim($data)
            ];
        }
        Log::info(json_encode($requestData));
        $this->log_ticketech->info("Ticketech Request Data".json_encode($requestData));
       
        try{
            $client = new SoapClient(config('ticketech.reservations.wsdl'), ['trace' => 1, 'connection_timeout ' => 30]);
            $resultSet = $client->SendReservation($requestData);

        }catch (\Exception $e) {
             throw new TicketechException(
                'Could not make reservation with ticketing service. Your amount has been refunded, Please try again in some time.',
                500,
                ['detail' => 'Ticketech GUID is null', 'reservation_id' => null]
            );
        }
        
        Log::info(json_encode($resultSet));
        $this->log_ticketech->info("Ticketech Response-Reservation-".json_encode($resultSet));
         
         
        $result = simplexml_load_string($resultSet->SendReservationResult);

        $reservation_id = (string) $result->ResultData->Reservation->tktReservationGUID;

         //check if ticketech GUID is null
        if((!$reservation_id || $reservation_id == '') && ($this->status==self::STATUS_ACTIVE)){
            throw new TicketechException(
                'Could not make reservation with ticketing service. Your amount has been refunded, Please try again in some time.',
                500,
                ['detail' => 'Ticketech GUID is null', 'reservation_id' => null]
            );
                   
        }
        
        // Update reservation with ticketech GUID
        $reservation->ticketech_guid = $reservation_id;
        $reservation->save();

        // Log ticketech response to the database
        $this->logTicketechReservationResponse($reservation, $result);

        // Check result
        $resultCode = $result->ResultData->Reservation->ResultCode;
        Log::info(json_encode($resultCode));

        $this->log_ticketech->info("Ticketech result-code-".json_encode($resultCode));
        
        if ($resultCode != 0) {
            $message = $result->ResultData->Reservation->Message;
            throw new TicketechException(
                'Could not make reservation with ticketing service.',
                500,
                ['detail' => $message, 'reservation_id' => $reservation->id]
            );
        }

        return $reservation_id;
    }

    /**
     * $result will be an object similar to the following:
     * SimpleXMLElement Object
     * (
     *     [ResultCode] => 0
     *     [ResultData] => SimpleXMLElement Object
     *         (
     *             [Reservation] => SimpleXMLElement Object
     *                 (
     *                     [ResultCode] => 0
     *                     [Message] => OK
     *                     [tktReservationGUID] => *************-2222-2222-************
     *                     [tktBarcode] => 12345
     *                     [tktStartDatetime] => 11/1/2015 9:26:00 AM
     *                     [tktEndDatetime] => 11/1/2015 5:26:00 PM
     *                 )
     *         )
     * )
     */
    protected function logTicketechReservationResponse(Reservation $reservation, \SimpleXMLElement $result)
    {
        $data = $result->ResultData->Reservation;
        $tktDateFormat = 'n/j/Y g:i:s A';

        TicketechResponse::create(
            [
            'reservation_id' => $reservation->id,
            'result_code' => $data->ResultCode,
            'message' => $data->Message,
            'guid' => $data->tktReservationGUID,
            'barcode' => $data->tktBarcode,
            'start_time' => Carbon::createFromFormat($tktDateFormat, $data->tktStartDatetime)->toDateTimeString(),
            'end_time' => Carbon::createFromFormat($tktDateFormat, $data->tktEndDatetime)->toDateTimeString(),
            ]
        );
    }

    private function error($message)
    {
        $this->error = $message;

        return false;
    }

    private function validateGenerationAction()
    {
        // todo: configuration validation
        // input property validation.
        if (!$this->ticketech_promo_code) {
            return $this->error("Invalid ticketech promo code");
        }
        if (!$this->price) {
            return $this->error("Invalid price");
        }
        if (!$this->expiration_date) {
            return $this->error("Invalid expiration date");
        }
        return true;
    }
}