<?php
namespace App\Classes;

class GoogleMaps
{
    public $size;
    public $center;
    public $client;
    public $zoom = 18;

    const BASE_API_URL = 'https://maps.googleapis.com/maps/api/staticmap';
    const BASE_MAPS_URL = 'https://maps.google.com';

    public function generateImageScr()
    {
        return $this->generateQueryString();
    }

    public function generateQueryString()
    {
        return self::BASE_API_URL.'?'.http_build_query(['center' => $this->center, 'size' => $this->size, 'zoom' => $this->zoom, 'markers' => $this->center, 'key' => config('google.api_key')]);
    }

    public function generateAddressLink($facility)
    {
        return self::BASE_MAPS_URL.'?'.http_build_query(['q' => $facility->geolocations->address_1 . ' ' . $facility->geolocations->city . ' ' . $facility->geolocations->state . ' ' . $facility->geolocations->zip_code ]);
    }

    public function setGeolocation($lat, $lng)
    {
        $this->center = $lat.','.$lng;
    }

    public function setMapSize($width, $height)
    {
        $this->size = $width.'x'.$height;
    }
    public function setZoom($zoom)
    {
        $this->zoom = $zoom;
    }
}
