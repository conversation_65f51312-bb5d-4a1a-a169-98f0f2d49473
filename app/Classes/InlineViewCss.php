<?php

namespace App\Classes;

use TijsVerkoyen\CssToInlineStyles\CssToInlineStyles;

class InlineViewCss
{
    /**
     * Convert to inlined CSS
     *
     * @return string
     * @throws \TijsVerkoyen\CssToInlineStyles\Exception
     */
    public static function convert($view, array $data)
    {
        $view = view($view, $data)->render();
        $converter = new CssToInlineStyles();
        $converter->setUseInlineStylesBlock();
        $converter->setCleanup();
        $converter->setStripOriginalStyleTags();
        $converter->setHTML($view);
        return $converter->convert();
    }
}
