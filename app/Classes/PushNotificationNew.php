<?php
 
namespace App\Classes;

use App\Exceptions\ApiGenericException;
use Log;
use App\Models\ParkEngage\Notificationbadge;
use Illuminate\Http\Request;
use Auth;
use DB;
use App\Models\ParkEngage\Notification;



class PushNotificationNew
{
    
    public static function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    public static function getAccessToken($keyFilePath) {
        $keyFile = file_get_contents($keyFilePath);
        $keyData = json_decode($keyFile, true);
    
        $now = time();
        $jwtHeader = self::base64UrlEncode(json_encode(['alg' => 'RS256', 'typ' => 'JWT']));
        $jwtClaim = self::base64UrlEncode(json_encode([
            'iss' => $keyData['client_email'],
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
            'aud' => 'https://oauth2.googleapis.com/token',
            'exp' => $now + 3600,
            'iat' => $now,
        ]));
    
        $jwtSignatureInput = $jwtHeader . '.' . $jwtClaim;
        openssl_sign($jwtSignatureInput, $jwtSignature, $keyData['private_key'], 'SHA256');
        $jwt = $jwtSignatureInput . '.' . self::base64UrlEncode($jwtSignature);
    
        $postData = http_build_query([
            'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'assertion' => $jwt,
        ]);
    
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    
        $response = curl_exec($ch);
        if ($response === false) {
            throw new ApiGenericException('Failed to obtain access token: ' . curl_error($ch));
        }
    
        $responseData = json_decode($response, true);
        curl_close($ch);
    
        if (!isset($responseData['access_token'])) {
            throw new ApiGenericException('Invalid access token response: ' . $response);
        }
    
        return $responseData['access_token'];
    }

     // public static function makes curl request to firebase servers
    public static function sendAndroidPushNotification($deviceToken, $msg_payload, $config = null)
    {
        // optional payload
        $payload = array();
        $payload['team'] = '';
        $payload['score'] = '';
        $set_payload = $msg_payload['set_payload'];
        if(isset($set_payload) && !empty($set_payload)) {
            foreach ($set_payload as $key => $value) {
                  $payload["$key"] = $value;
            }
        }
        //get and set message;
        // $res = array();
        // $res['data']['title'] = "";
        // $res['data']['is_background'] = false;
        // $res['data']['message'] = $msg_payload['message'];
        // $res['data']['image'] = '';
        // $res['data']['payload'] = $payload;
        // $res['data']['timestamp'] = date('Y-m-d G:i:s');
        
        Log::info("Android Device token: ".$deviceToken);

        /*$fields = array(
            'to' => $deviceToken,
            'data' => $res,
        );*/
        $fields = json_encode([
            'message' => [
                'token' => $deviceToken,
                'notification' => [
                    'title' => $msg_payload['title'],
                    'body' => $msg_payload['message'],
                ],
                'data' => [
                    'ticket_number' => $payload['ticket_number'],
                    'license_plate' => $payload['license_plate'],
                    'gate_number' => $payload['gate_number'],
                    'gate_type' => $payload['gate_type'],
                    'gate_name' => $payload['gate_name']
                ]
            ],
        ]);
        // Set POST variables
        if(isset($config->facility_id)){
            $url = "$config->android_new_url";
            $keyFilePath = config('parkengage.DIRECTORY_BASE_URL').'app/Classes/'.$config->json_key_file_name;
            $accessToken = self::getAccessToken($keyFilePath);
            //Set Server API KEY
            $authKey = "Authorization: Bearer ".$accessToken;
            $headers = array(
                $authKey,
                'Content-Type: application/json'
            );
            Log::info("Android Device token auth key: ".json_encode($headers));
        }else{
            $url = 'https://fcm.googleapis.com/fcm/send';
            //Set Server API KEY
        
            $headers = array(
                'Authorization: key=AAAAy_2jnJc:APA91bHoVvtiYoqyX8o7-i_wQh1rqDjGp_sc7w9wgW7ZwAEJLM2XgZdDv6RI7DJUDvmNjjO6BuQGD41mdLphlCGwzZkdgPrgnywCOTp5Fp0J5l4Vtc85d0ZAdnNSwxbikWbGJa7GX-E4',
                'Content-Type: application/json'
            );
        }
        
        

        Log::info("Reached Android");
        // Open connection
        $ch = curl_init();
 
        // Set the url, number of POST vars, POST data
        curl_setopt($ch, CURLOPT_URL, $url);
 
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
 
        // Disabling SSL Certificate support temporarly
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POSTFIELDS, ($fields));
        
        // Execute post
        $result = curl_exec($ch);

        curl_close($ch);
        Log:info(json_encode($result));

        if ($result) {
            return [
                'device_token' => $deviceToken,
                'message' => $msg_payload['message'],
                'status' => 'Push Notification Sent Successfully',
                'response' => $result
            ];
        } else {
            return [
                'device_token' => $deviceToken,
                'message' => $msg_payload['message'],
                'status' => 'Push Notification Not Sent',
                'response' => curl_error($ch)
            ];
        }
    }

    public static function sendIphonePush($deviceToken, $msg_payload, $badge = 0, $check= 0, $userid, $config = null)
    {                     
        if(isset($config->facility_id)){
            $keyfile = config('parkengage.DIRECTORY_BASE_URL').'app/Classes/'.$config->auth_key_file_name;
            $keyid = "$config->key_id";                            # <- Your Key ID
            $teamid = "$config->team_id";                           # <- Your Team ID (see Developer Portal)
            $bundleid = "$config->bundle_id";                # <- Your Bundle ID    (new , applied on 10-05-2023)
            $url = "$config->ios_url";  # <- development url, or use http://api.push.apple.com for production environment
        }else{
            $keyfile = config('parkengage.DIRECTORY_BASE_URL').'app/Classes/AuthKey_QL427YB49Z.p8';               # <- Your AuthKey file
            $keyid = 'QL427YB49Z';                            # <- Your Key ID
            $teamid = 'CWA7VA7V37';                           # <- Your Team ID (see Developer Portal)
            $bundleid = 'com.parkengage.parkingpayments';                # <- Your Bundle ID    (new , applied on 10-05-2023)
            $url = 'https://api.sandbox.push.apple.com';  # <- development url, or use http://api.push.apple.com for production environment
        }
          

          Log::info("IOS Device token: ".$deviceToken. '-'. $keyfile. '-'. $keyid. '-'. $teamid. '-'. $bundleid. '-'. $url);


        // new code
        $notificationsData = Notification::whereNull('deleted_at')->where('user_id',$userid)->where('is_read',0)->get();

        if(count($notificationsData) > 0)
        {
            $fbadge = count($notificationsData)+1;  
            Log::info("User: ".$userid.' notification send count '.$fbadge);          
        }
        else{
            Log::info("else part");            
            $fbadge = 1;
        }
        // new code


          $message_text = $msg_payload['message'];
          $title = $msg_payload['title'];

            // $payload['payload'] = $payload;

            $payload['aps'] = array('alert' => ['title'=>$title,'body'=>$message_text], 'sound' => 'default', 'badge' => $fbadge);
            $set_payload = $msg_payload['set_payload'];
            if(isset($set_payload) && !empty($set_payload)) {
                foreach ($set_payload as $key => $value) {
                      $payload["$key"] = $value;
                }

            }
          $message = json_encode($payload);

          $key = openssl_pkey_get_private('file://'.$keyfile);

          $header = ['alg'=>'ES256','kid'=>$keyid];
          $claims = ['iss'=>$teamid,'iat'=>time()];

          $header_encoded = self::base64($header);
          $claims_encoded = self::base64($claims);

          $signature = '';
          openssl_sign($header_encoded . '.' . $claims_encoded, $signature, $key, 'sha256');
          $jwt = $header_encoded . '.' . $claims_encoded . '.' . base64_encode($signature);

          // only needed for PHP prior to 5.5.24
          if (!defined('CURL_HTTP_VERSION_2_0')) {
              define('CURL_HTTP_VERSION_2_0', 3);
          }

          $http2ch = curl_init();
          curl_setopt_array($http2ch, array(
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_2_0,
            CURLOPT_URL => "$url/3/device/$deviceToken",
            CURLOPT_PORT => 443,
            CURLOPT_HTTPHEADER => array(
              "apns-topic: {$bundleid}",
              "authorization: bearer $jwt"
            ),
            CURLOPT_POST => TRUE,
            CURLOPT_POSTFIELDS => $message,
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HEADER => 1
          ));

          $result = curl_exec($http2ch);
          
          if ($result === FALSE) {
            throw new Exception("Curl failed: ".curl_error($http2ch));
          }
         
          $status = curl_getinfo($http2ch, CURLINFO_HTTP_CODE);
          $curlInfo = curl_getinfo($http2ch);
          Log::info(json_encode($curlInfo));
            Log::info(" IOS After Result device token ". $deviceToken. " - status ". $status);
            Log::info("result info ".json_encode($result));
          
           return [
           'device_token' => $deviceToken,
            'status' => $status,
          // 'message' => $msg_payload['message'],
            'response' => $result
           ];
         
    }

    public static function base64($data) {
        return rtrim(strtr(base64_encode(json_encode($data)), '+/', '-_'), '=');
    }

    /*public static function sendIphonePush($deviceToken, $msg_payload, $badge = 0, $check= 0)
    {
       //echo $deviceToken;  die;                              //Noted by Santosh kumar
        $apnsHost = 'gateway.sandbox.push.apple.com';    //development phase
        //$apnsHost = 'gateway.push.apple.com';            //distribution phase
        $apnsPort = '2195';                                //.pem file ko project root per paste karna hai
        // $apnsCert = url('/assets/iosCert/pushcert.pem'); //'pushcert.pem';                            //certificate pem file
         $apnsCert = env('PEM_FILE_PATH');
        //$apnsCert = 'ck1.pem';
        $passPhrase = '';

        //$passPhrase = '12345678';                            //cetificate password
        $streamContext = stream_context_create();
        stream_context_set_option($streamContext, 'ssl', 'local_cert', $apnsCert);

        $apnsConnection = stream_socket_client('ssl://' . $apnsHost . ':' . $apnsPort, $error, $errorString, 60, STREAM_CLIENT_CONNECT, $streamContext);
        // die('comin ddgzczx');
        if ($apnsConnection == false) {
            $error="Failed to connect {$error} {$errorString}\n";
            error_log($error.chr(13), 3, "push-errors.log");
        } else {
        }
        Log::info("Socket opened ios");
        $message = $msg_payload['message'];

        // $payload['payload'] = $payload;

        $payload['aps'] = array('alert' => $message, 'sound' => 'default', 'badge' => 1);
        $set_payload = $msg_payload['set_payload'];
        if(isset($set_payload) && !empty($set_payload)) {
            foreach ($set_payload as $key => $value) {
                  $payload["$key"] = $value;
            }

        }
        $payload = json_encode($payload);
        try {
            if ($message != "") {
                $apnsMessage = chr(0) . pack("n", 32) . pack('H*', str_replace(' ', '', $deviceToken)) . pack("n", strlen($payload)) . $payload;
                $fwrite = fwrite($apnsConnection, $apnsMessage);
                if ($fwrite) {   
                    $response = $fwrite;
                    $status = 'Push Notification Sent Successfully';
                } else {
                    $response = $fwrite;
                    $status = 'Push Notification Not Sent';
                    error_log($fwrite.chr(13), 3, "push-errors.log");
                }
            }
        } catch (Exception $e) {
            $status = 'Push Notification Not Sent';
            $response = $e->getMessage();
            error_log($e->getMessage().chr(13), 3, "push-errors.log");
        }

        return [
            'device_token' => $deviceToken,
            'message' => $msg_payload['message'],
            'status' => $status,
            'response' => $response
        ];
    }*/
    
    public static function sendIphonePushAppleWallet($deviceToken, $passIdentify, $badge = 0, $check= 0)
    {
        
 
        //echo $deviceToken;  die;                              //Noted by Santosh kumar
        $apnsHost = 'gateway.push.apple.com';    //development phase
        //$apnsHost = 'gateway.push.apple.com';            //distribution phase
        $apnsPort = '2195';                                //.pem file ko project root per paste karna hai
        // $apnsCert = url('/assets/iosCert/pushcert.pem'); //'pushcert.pem';                            //certificate pem file
        $apnsCert = '/var/www/sites/iqp-api/app/Classes/pushcert.pem';
        //$apnsCert = 'ck1.pem';
        $passPhrase = '';

        //$passPhrase = '12345678';                            //cetificate password

        $arrContextOptions=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );

        $streamContext = stream_context_create($arrContextOptions);
        stream_context_set_option($streamContext, 'ssl', 'local_cert', $apnsCert);
try{
        $apnsConnection = stream_socket_client('ssl://' . $apnsHost . ':' . $apnsPort, $error, $errorString, 60, STREAM_CLIENT_CONNECT, $streamContext);
}catch(\Exception $e) {

dd($e);
}     //die('comin ddgzczx');
        if ($apnsConnection == false) {
            $error="Failed to connect {$error} {$errorString}\n";
            error_log($error.chr(13), 3, "push-errors.log");
        } else {
        }
 //      Log::info("Ios wallet pass");
        $msg_payload = array(
                            'message' => "TEST",
                            'set_payload'=> array('reservation_id' =>1)  // add new payload
                        );
        $message = $msg_payload['message'];
          $payload['aps'] = array('alert' => $message, 'sound' => 'default', 'badge' => 1);
        $set_payload = $msg_payload['set_payload'];
        if(isset($set_payload) && !empty($set_payload)) {
            foreach ($set_payload as $key => $value) {
                  $payload["$key"] = $value;
            }
//
        }
       //: $payload = json_encode($payload);
     //echo "here";die;
        $payload = '{}';
        //$payload = ['aps' => ['alert'=>'test']];
    $payload = json_encode($payload);
        try {
            if ($passIdentify != "") {
                $apnsMessage = chr(0) . pack("n", 32) . pack('H*', str_replace(' ', '', $deviceToken)) . pack("n", strlen($payload)) . $payload. pack("n", strlen($passIdentify)) . $passIdentify;
                $fwrite = fwrite($apnsConnection, $apnsMessage);
                if ($fwrite) {
                    $response = $fwrite;
                    $status = 'Push Notification Sent Successfully';
                } else {
                    $response = $fwrite;
                    $status = 'Push Notification Not Sent';
                    error_log($fwrite.chr(13), 3, "push-errors.log");
                }
            }
        } catch (Exception $e) {
            $status = 'Push Notification Not Sent';
            $response = $e->getMessage();
            error_log($e->getMessage().chr(13), 3, "push-errors.log");
        }

        return [
            'device_token' => $deviceToken,
            'status' => $status,
//             'message' => $msg_payload['message'],
            'response' => $response
        ];
    }

    /* Author santosh 11-08-2017 */
    //santosh
    public static function sendPushNotification($deviceType, $deviceToken, $msg_payload, $userid, $config = null)
    {

        if (strtolower($deviceType) == 'android') {
            $response = self::sendAndroidPushNotification($deviceToken, $msg_payload, $config);
            
            return true;
        } elseif (strtolower($deviceType) == 'ios') {
            $response = self::sendIphonePush($deviceToken, $msg_payload, $badge = 0, $check = 0, $userid, $config);
            return true;
        } else {
            $response = self::sendWebPushNotification($deviceToken, $msg_payload, $badge = 0, $check = 0);
            return true;
        }
    }

    public static function sendWebPushNotification($deviceToken, $msg_payload)
    {
       $payload = array();
       $set_payload = $msg_payload['set_payload'];
        if(isset($set_payload) && !empty($set_payload)) {
            foreach ($set_payload as $key => $value) {
                  $payload["$key"] = $value;
            }
        }       

       $msg = array(
              'title'     => $msg_payload['title'],
              'body'         => $msg_payload['message'],
              'payload'         => count($payload) == 0 ? '' : $payload
      );

$fields = array(
          'to'      => $deviceToken,
          'notification' => $msg
      );
 
        
        // Set POST variables
        $url = 'https://fcm.googleapis.com/fcm/send';
        //Set Server API KEY
     
         $headers = array(
            'Authorization: key=AAAAPNaX4j4:APA91bGakLGJDB-GFvjuDQM-riD1_aYKtS1SYptlUb4F8YuZisYzJxvdVyq8iyzf0ULZP4kAJPmEGbcbny7lF8kBuSEK5aX7YnKr0n09X98iWMGRYitqfYhjcVClhHO35cJWB4-PW6ut',
            'Content-Type: application/json'
        );

        Log::info("Reached Web");
        // Open connection
        $ch = curl_init();

      curl_setopt($ch,CURLOPT_URL, $url); //For firebase, use https://fcm.googleapis.com/fcm/send
      curl_setopt($ch,CURLOPT_POST, true);
      curl_setopt($ch,CURLOPT_HTTPHEADER, $headers);
      curl_setopt($ch,CURLOPT_RETURNTRANSFER, true);
      curl_setopt($ch,CURLOPT_SSL_VERIFYPEER, false);
      curl_setopt($ch,CURLOPT_POSTFIELDS, json_encode($fields));
      $result = curl_exec($ch);
      curl_close($ch);

      Log:info(json_encode($result));

        if ($result) {
            return [
                'device_token' => $deviceToken,
                'message' => $msg_payload['message'],
                'status' => 'Push Notification Sent Successfully',
                'response' => $result
            ];
        } else {
            return [
                'device_token' => $deviceToken,
                'message' => $msg_payload['message'],
                'status' => 'Push Notification Not Sent',
                'response' => curl_error($ch)
            ];
        }
    }
}
