<?php
namespace App\Classes;
use App\Models\PromoCode;
use App\Models\PromoUsage;
use App\Models\Promotion;
use App\Models\User;
use App\Models\PromotionUser;
use App\Exceptions\ApiGenericException;
use App\Services\Mailers\UserMailer;
use App\Models\PromotionVerification;
use App\Classes\MagicCrypt;
use App\Services\LoggerFactory;
use Auth;
use DB;
use Exception;
use Carbon\Carbon;

class PromoCodeLib
{

    public static $is_promocode_valid = true;
    public static $is_promocode_success = false;
    public static $coupon_length = 8;
    public static $promo_check_response = 'Promo Code is valid';
    public static $confirmation_code = 0;
    public static $is_opt_out = 0;
    public static $user_type = 1;


    public static function generatePromoCodes($promotion)
    {
        $promoCode = [
            'promotion_id' => $promotion->id,
            'promo_type_id' => $promotion->promo_type_id,
            'channel_partner_id' => $promotion->channel_partner_id,
            'valid_from' => $promotion->valid_from,
            'valid_to' => $promotion->valid_to,
            'is_expired' => 0,
            'status' => 1,
            'max_lifetime_discount' => $promotion->max_lifetime_discount,
        ];

        switch ($promotion->promo_type_id) {
        case 1:
            $promoCode['usage'] = $promotion->usage;
            $promoCode['discount_type'] = $promotion->discount_type;
            $promoCode['discount_value'] = $promotion->discount_value;
            $codeIdentifier = 1;
            break;
        case 2:
            $promoCode['discount_type'] = $promotion->discount_type;
            $promoCode['discount_value'] = $promotion->discount_value;
            $codeIdentifier = 2;
            break;
        case 3:
            $promoCode['base_price'] = $promotion->base_price;
            $promoCode['promo_price'] = $promotion->promo_price;
            $codeIdentifier = 3;
            break;
        default:
            // code...
            break;
        }

        try {
            if($promotion->couponscode=='') { //couponscode
                $promoCodes = self::randomPromoCodes($promoCode, $promotion->total_coupons);
            } else{
                $promoCodes=$promoCode;
                $promoCodes['promocode'] = $promotion->couponscode;
                $promoCodes['created_at'] = date('Y-m-d H:i:s');
                $promoCodes['updated_at'] = date('Y-m-d H:i:s');
            }
            $result = self::storePromoCodes($promoCodes);
        } catch (Exception $e) {
            // Remove the Promotion Entry ...
            $promotion->delete();
            throw new ApiGenericException($e->getMessage());
        }

        if (!$result) {
            throw new ApiGenericException('Error Occured, Promo Codes Could Not Be Generated');
        }

        return [
            'success' => true,
            'promotion' => $promotion,
            'generated_promos' => count($promoCodes),
        ];
    }

    public static function randomPromoCodes($promoCodeStructure, $total_coupons)
    {
        $promoCodes = [];
        $symbols = '1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $promoCodeLength = PromoCodeLib::$coupon_length - 1;
        $symbols_length = strlen($symbols) - 1;

        for ($p = 0; $p < $total_coupons; $p++) {
            $randomPromoCode = '';
            for ($i = 0; $i < $promoCodeLength; $i++) {
                $n = rand(0, $symbols_length);
                if ($i == 1) {
                    $randomPromoCode .= $promoCodeStructure['promo_type_id'];
                }
                $randomPromoCode .= $symbols[$n];
            }
            $promoCodeStructure['promocode'] = $randomPromoCode;
            $promoCodeStructure['created_at'] = date('Y-m-d H:i:s');
            $promoCodeStructure['updated_at'] = date('Y-m-d H:i:s');
            $promoCodes[] = $promoCodeStructure;
        }

        return $promoCodes;
    }

    public static function storePromoCodes($promoCodes)
    {
        return PromoCode::insert($promoCodes);
    }

    public static function validatePromoCode($request)
    {

        $promoCode = PromoCode::where('promocode', $request->promocode)->first();
        
        if(isset($request->user_id) && strpos(strtolower($request->promocode), config('loyalty.promocode_key')) !== false){
            $user = User::where('id', $request->user_id)->where('is_loyalty', 1)->first();
            if(!$user){
                throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.');
            }
        }

        if(isset($request->email) && $request->email!=''  && (strpos(strtolower($request->promocode), config('loyalty.promocode_key')) !== false)){
            $user = User::where('email', $request->email)->where('is_loyalty', 1)->first();
            if(!$user){
                throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.');
            }
        }

        if (!$promoCode) {
            throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.');
        }

        $currentDate = date('Y-m-d');

        $promotion = Promotion::where('id', $promoCode->promotion_id)->first();
        
        if (!$promotion) {
            throw new ApiGenericException('Please enter a valid Promotion Code. Please note that a prepaid voucher is not a Promo Code.');
        }
        if ($promotion->status != 1) {
            throw new ApiGenericException('Sorry, the validation code you entered is not applicable.');
        }

        if ($currentDate < $promotion->valid_from) {
            self::$is_promocode_valid = false;
            self::$promo_check_response = 'Sorry, the validation code you entered is not applicable.';
            throw new ApiGenericException('Sorry, the validation code you entered is not applicable.');
        }        

        if ($currentDate > $promotion->valid_to) {            
            self::$is_promocode_valid = false;            
            self::$promo_check_response = 'Sorry, but it looks like this promotion has already expired.';  
            throw new ApiGenericException('Sorry, but it looks like this promotion has already expired.');
             
        }

        if (self::$is_promocode_valid) {
            switch ($promoCode->promo_type_id) {
            case 1:
                self::validateStaticPromoCode($request, $promoCode);
                break;
            case 2:
                self::validateSingleUsePromoCode($request, $promoCode);
                break;
            case 3:
                self::validatePromoValuePromoCode($request, $promoCode);
                break;
            default:
                // code...
                break;
            }
        }
        //get discount mx limit
          $percentageDiscount = isset($promotion->percentage_off_discount)?$promotion->percentage_off_discount:0;
          $dollar_off_discounts = isset($promotion->dollar_off_discount)?$promotion->dollar_off_discount:0;

        return [
            'is_promocode_valid' => self::$is_promocode_valid,
            'promocode' => $promoCode,
            'message' => self::$promo_check_response,
            'is_opt_out' => self::$is_opt_out,
            'is_promocode_success' => self::$is_promocode_success,
            'percentage_max_discount' => $percentageDiscount,
            'dollar_min_discount' => $dollar_off_discounts,
            'user_type_promotion'  => isset($promotion->user_type)?$promotion->user_type:'0',
            'minimun_amount_error_msg' => "This promotional code can only be used on transactions of at least $".$dollar_off_discounts.".",
            'max_percentage_amount_error_msg' => "You have reached the maximum discount on this promotion."
        ];


        // 1. Check If Promocode Exists or Not [DONE]
        // 2. Check If Current Date is Matching With PromoCode Validity or Not [DONE]
        // 3. Check Promocode Type [DONE]
        //     4.1 If Static Use Code
        //         1. Check If User Id Is Provided or Not [DONE]
        //         2. Check If User Max Usage Limit Is Not Exceeded or Not [DONE]
        //     4.2 If Single Use Code
        //         1. Check If Coupon Is Expired Or Not [DONE]
        //     4.3 If Promo Value Promo Code [DONE]
    }

    public static function validatePromoCodeThirdParty($request)
    {
		$db_logs_data['request'] = json_encode($request->all());
		$db_logs_data['api_type'] = 'apply';
		$db_logs_data['created_at'] = Carbon::now();
		$logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/partner_promocode_api_logs')->createLogger('apply_api_logs');
        try {
			
            $log->info("Request: " . $db_logs_data['request']);
        
            $promoCode = PromoCode::where('promocode', $request->promocode)->first();


            if (!$promoCode) {
                throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.', 404);
            }

            $currentDate = date('Y-m-d');

            $client = DB::table('oauth_clients')->where('secret', $request->client_id)->orderBy('created_at', 'desc')->first();
			if(!$client) {
                throw new ApiGenericException('Promocode not authorized for user', 401);
			}
            $partner_id = $client->partner_id;

            $promotion = Promotion::where('id', $promoCode->promotion_id)->where('owner_id', $partner_id)->first();
            
            if (!$promotion) {
                throw new ApiGenericException('Promocode not authorized for user', 401);
            }
			
			$is_guest = isset($request->is_guest) ? $request->is_guest : 0;
			if($is_guest && $promotion->user_type == 0) {
				throw new ApiGenericException('Promocode not authorized for guest users', 401);
			}

            if($promotion->specific_user_type == 1 && !(self::checkPromoEmailValidity($promotion->id, $request->email))) {
                throw new ApiGenericException('Promocode not authorized for user', 401);
            }

            if ($promotion->status != 1) {
                throw new ApiGenericException('Sorry, the validation code you entered is not applicable.', 406);
            }
  
            if ($currentDate < $promotion->valid_from) {
                throw new ApiGenericException('Promocode has not been activated yet', 406);
            }        

            // remove is_promocode_valid and promo_check_response
            if ($currentDate > $promotion->valid_to) {            
                throw new ApiGenericException('Sorry, but it looks like this promotion has already expired', 406);
            }

            switch ($promoCode->promo_type_id) {
            case 1:
                // self::validateStaticPromoCode($request, $promoCode);
                self::checkPromoCodeUsageForEmail($partner_id, $promoCode, $request->email);
                break;
            case 2:
                self::validateSingleUsePromoCodeForEmail($partner_id, $promoCode, $request->email);
                break;
            case 3:
                self::validatePromoValuePromoCode($request, $promoCode);
                break;
            default:
                // code...
                break;
            }

            //get discount mx limit
            $percentageDiscount = isset($promotion->percentage_off_discount)?$promotion->percentage_off_discount:0;
            $dollar_off_discounts = isset($promotion->dollar_off_discount)?$promotion->dollar_off_discount:0;

            $amount = $request->amount;
			if($promotion->is_tax_applicable == 1) {
				if(isset($request->tax_amount) && $request->tax_amount > 0) {
					$amount += $request->tax_amount;
				}
			}
			
            $discount_in_dollar = 0;
            $max_percentage_discount_exceeded = false;
            $max_lifetime_discount_limit = $promotion->max_lifetime_discount;

            if($promoCode->discount_type === 'percentage') {
                $discount_in_dollar = ($promoCode->discount_value / 100) * $amount;
                if($discount_in_dollar > $percentageDiscount) {
					$max_percentage_discount_exceeded = true;
                    $discount_in_dollar = $percentageDiscount;
                }
            }
            else {
                if($amount >= $dollar_off_discounts) {
                    $discount_in_dollar = $promoCode->discount_value;
                }
                else {
					throw new ApiGenericException("Sorry, this validation code is valid only for transactions of $" . $dollar_off_discounts. " or more.", 400);
                }
            }
            
            if($promotion->specific_user_type == 1 && $max_lifetime_discount_limit > 0) {
                $available_discount_limit = self::getAvailableDiscountLimit($partner_id, $promoCode, $promotion, $request->email);
                if($available_discount_limit === 0) {
                    throw new ApiGenericException("You have already reached the maximum discount on this promotion.", 403);
                }
            }

			
            self::sanitizePromocodeData($promoCode);
            
			$max_lifetime_discount_exceeded = false;
			if($promotion->specific_user_type == 1) {
				if($max_lifetime_discount_limit > 0 && $available_discount_limit < $discount_in_dollar) {
					$max_lifetime_discount_exceeded = true;
					$max_percentage_discount_exceeded = false;
					$discount_in_dollar = $available_discount_limit;
					$message = 'You have reached the maximum overall discount allowed on this promocode';
				}
				else if($max_percentage_discount_exceeded) {
					$message = "You have reached the maximum discount per transaction on this promocode";
				}
				else {
					$message = "Promocode successfully applied";
				}
			}
			else {
				if($max_percentage_discount_exceeded) {
					$message = "You have reached the maximum discount per transaction on this promocode";
				}
				else {
					$message = "Promocode successfully applied";
				}
			}

            $discount_in_dollar = (float)number_format((float)$discount_in_dollar, 2, '.', '');
			$response  = [
            'is_promocode_valid' => true,
            'promocode' => $promoCode,
            'message' => $message,
            'discount_in_dollar' => $discount_in_dollar,
            'max_percentage_discount' => $percentageDiscount,
            'min_amount_required_for_dollar_discount' => $dollar_off_discounts,
            'max_lifetime_discount' => $max_lifetime_discount_limit,
            'is_max_lifetime_discount_exceeded' => $max_lifetime_discount_exceeded,
            'is_max_percentage_discount_exceeded' => $max_percentage_discount_exceeded,
			'is_tax_applicable' => $promotion->is_tax_applicable
        ];
		
		$db_logs_data['response'] = json_encode($response);
		
		$log->info("Response: " . $db_logs_data['response']);
		
		DB::table('partner_promocode_api_logs')->insert($db_logs_data);
		
		if($max_percentage_discount_exceeded || $max_lifetime_discount_exceeded) {
			$response["max_discount_limit_exceeded"] = 1;
			return response()->json($response, 202);
		}
		return response()->json($response, 200);
    }
    catch(Exception $e) {
		$log->error("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
		$db_logs_data['is_error'] = 1;
		$db_logs_data['error_message'] = $e->getMessage();
		DB::table('partner_promocode_api_logs')->insert($db_logs_data);
		throw $e;
    }
  }


  public static function validatePromoCodeUsageThirdParty($request)
  {
      $db_logs_data['request'] = json_encode($request->all());
      $db_logs_data['api_type'] = 'usage';
      $db_logs_data['created_at'] = Carbon::now();
      $logFactory = new LoggerFactory();
      $log = $logFactory->setPath('logs/partner_promocode_api_logs')->createLogger('usage_api_logs');
      try {
          
          $promoCode = PromoCode::where('promocode', $request->promocode)->first();

          if (!$promoCode) {
              throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.', 404);
          }

          $currentDate = date('Y-m-d');

          $client = DB::table('oauth_clients')->where('secret', $request->client_id)->orderBy('created_at', 'desc')->first();
          $partner_id = $client->partner_id;

          $promotion = Promotion::where('id', $promoCode->promotion_id)->where('owner_id', $partner_id)->first();
          
          if (!$promotion) {
              throw new ApiGenericException('Promocode not authorized for user', 401);
          }

          if($promotion->specific_user_type == 1 && !(self::checkPromoEmailValidity($promotion->id, $request->email))) {
            throw new ApiGenericException('Promocode not authorized for user', 401);
          }
		  
		  $is_guest = isset($request->is_guest) ? $request->is_guest : 0;
		  if($is_guest && $promotion->user_type == 0) {
            throw new ApiGenericException('Promocode not authorized for guest users', 401);
		  }

          if ($promotion->status != 1) {
                throw new ApiGenericException('Sorry, the validation code you entered is not applicable.', 406);
          }

          if ($currentDate < $promotion->valid_from) {
              throw new ApiGenericException('Promocode has not been activated yet', 406);
          }        

          if ($currentDate > $promotion->valid_to) {            
              throw new ApiGenericException('Sorry, but it looks like this promotion has already expired', 406);
          }

            switch ($promoCode->promo_type_id) {
            case 1:
                // self::validateStaticPromoCode($request, $promoCode);
                self::checkPromoCodeUsageForEmail($partner_id, $promoCode, $request->email);
                break;
            case 2:
            self::validateSingleUsePromoCodeForEmail($partner_id, $promoCode, $request->email);
                break;
            case 3:
                self::validatePromoValuePromoCode($request, $promoCode);
                break;
            default:
                // code...
                break;
            }

          $max_lifetime_discount_limit = $promotion->max_lifetime_discount;
          if($promotion->specific_user_type == 1 && $max_lifetime_discount_limit > 0) {
            $available_discount_limit = self::getAvailableDiscountLimit($partner_id, $promoCode, $promotion, $request->email);
            if($available_discount_limit === 0) {
                throw new ApiGenericException("You have already reached the maximum discount on this promotion.", 403);
            }
          }
          
          $discount_in_dollar = $request->amount;
          $message = "Promocode amount successfully used.";
		  
		if($promotion->specific_user_type == 1) {
          if($max_lifetime_discount_limit > 0 && $available_discount_limit < $discount_in_dollar) {
              $discount_in_dollar = $available_discount_limit;
              $message = 'You have reached the maximum overall discount allowed on this promocode';
          }
		}

          $discount_in_dollar = (float)number_format((float)$discount_in_dollar, 2, '.', '');
          $response  = [
            'promocode' => $promoCode,
            'applicable_discount' => $discount_in_dollar,
            'partner_id' => $partner_id,
            'message' => $message
          ];
          return $response;
  }
  catch(Exception $e) {
      $log->error("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
      $db_logs_data['is_error'] = 1;
      $db_logs_data['error_message'] = $e->getMessage();
      DB::table('partner_promocode_api_logs')->insert($db_logs_data);
      throw $e;
  }
}


    public static function validateStaticPromoCode($request, $promoCode)
    {
        
        $promotion = Promotion::where('id', $promoCode->promotion_id)->first();
        if($promotion->user_type == self::$user_type)
        {
            
             self::$is_opt_out = 1;
           
             if (!$request->user_id) {

                  self::validateNewStaticGuestPromoCode($request, $promoCode);

                }else{
                 
                 self::validateNewStaticUserPromoCode($request, $promoCode);
             } 
        }else{
            
               if (!$request->user_id) {
                 throw new ApiGenericException('Please log in or create an account to redeem this promo code.');
               }
               self::validateUser($request->user_id);
               self::checkPromoCodeUsage($request->user_id, $promoCode);
        }
        
    }

    public static function validateSingleUsePromoCode($request, $promoCode)
    {
        if ($promoCode->is_expired) {
            self::$is_promocode_valid = false;
            self::$promo_check_response = 'You have reached the maximum discount on this promotion.';
        }
    }

    public static function validateSingleUsePromoCodeForEmail($user_id, $promoCode, $email)
    {
        $promoUsage = PromoUsage::where(['user_id' => $user_id, 'promocode' => $promoCode->promocode, 'email' => $email])->first();
        if ($promoUsage) {
            throw new ApiGenericException("Maximum usage of this promocode has already been exceeded", 403);
        }
    }

    public static function validatePromoValuePromoCode($request, $promoCode)
    {
        throw new ApiGenericException('Error Occured, Promo Code Type Is Currently Not Supported');
    }

    public static function validateUser($user_id)
    {
        $user = User::where('id', $user_id)->first();
        if (!$user) {
            throw new ApiGenericException('Please login with a valid user Id.');
        }
        return $user;
    }

    public static function checkPromoCodeUsage($user_id, $promoCode)
    {
        $promoUsages = PromoUsage::where(['user_id' => $user_id, 'promocode' => $promoCode->promocode])->get();
        if (!empty($promoUsages->toArray())) {
            $totalUsage = count($promoUsages->toArray());
            if ($totalUsage >= $promoCode->usage) {
                self::$is_promocode_valid = false;
                self::$promo_check_response = 'You have reached the maximum discount on this promotion.';
            }
        }
    }

    public static function checkPromoCodeUsageForEmail($user_id, $promoCode, $email)
    {
        $promoUsages = PromoUsage::where(['user_id' => $user_id, 'promocode' => $promoCode->promocode, 'email' => $email])->get();
        if (!empty($promoUsages->toArray())) {
            $totalUsage = count($promoUsages->toArray());
            if ($totalUsage >= $promoCode->usage) {
                throw new ApiGenericException("Maximum usage of this promocode has already been exceeded", 403);
            }
        }
    }

    public static function checkPromoEmailValidity($promotion_id, $email)
    {
        $email_exists = PromotionUser::where(['promotion_id' => $promotion_id, 'email' => $email])->count();
        return $email_exists ? true : false;
    }

    public static function getAvailableDiscountLimit($user_id, $promoCode, $promotion, $email)
    {
        $available_limit = $promotion->max_lifetime_discount;
        if($promotion->specific_user_type == 1) {
            $promoUsages = PromoUsage::where(['user_id' => $user_id, 'promocode' => $promoCode->promocode, 'email' => $email])->get();
        }
        else {
            $promoUsages = PromoUsage::where(['user_id' => $user_id, 'promocode' => $promoCode->promocode])->get();
        }
        if (!empty($promoUsages->toArray())) {
            $total_discount_used = $promoUsages->sum('discount_amount');
            if($total_discount_used < $promotion->max_lifetime_discount) {
                $available_limit = $promotion->max_lifetime_discount - $total_discount_used;
            }
            else {
                $available_limit = 0;
            }
        }
        return $available_limit;
    }

    
    public static function validateNewStaticGuestPromoCode($request, $promoCode)
    {         
      if(isset($request->is_landing_page) && ($request->is_landing_page) == '1')
      { 
           return $promoCode;
      }
      
       if(!$request->is_auto_apply)
       {
           
           if (!$request->email) {
                throw new ApiGenericException('Please enter your email address to apply this promo code.');
           }
           
           $user= User::getAnonUser($request->email); 
           
           if (!$user) {
                throw new ApiGenericException('Sorry! Please enter your email address to apply this promo code.');
           }
           
           $promoUsages = PromoUsage::where(['user_id' => $user->id, 'promocode' => $promoCode->promocode])->get();
           
          if (count($promoUsages)>0) {           
            self::$is_promocode_valid = false;
            self::$promo_check_response = 'Our records show you have already used this promo code.';
          }          
          return $promoCode;
      }     
      return $promoCode;
        
    }
    
    public static function validateNewStaticUserPromoCode($request, $promoCode)
    {
        self::validateUser($request->user_id);
        self::checkPromoCodeUsage($request->user_id, $promoCode);
//        $promoUsages = PromoUsage::where(['user_id' => $request->user_id, 'promocode' => $promoCode->promocode])->get();
//         if (count($promoUsages)>0) { 
//            self::$is_promocode_valid = false;
//            self::$promo_check_response = 'Promo Code has been expired.';
//        }
    }

	private static function sanitizePromocodeData($promoCode)
	{
		unset($promoCode->promotion_id);
		unset($promoCode->promo_type_id);
		unset($promoCode->channel_partner_id);
		unset($promoCode->deleted_at);
		unset($promoCode->created_by);
		unset($promoCode->created_at);
		unset($promoCode->updated_at);
		unset($promoCode->updated_by);
		unset($promoCode->deleted_by);
		unset($promoCode->expired_at);
    }
    
}
