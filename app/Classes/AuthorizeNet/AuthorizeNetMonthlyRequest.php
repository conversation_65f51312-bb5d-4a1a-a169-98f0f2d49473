<?php

namespace App\Classes\AuthorizeNet;

use App\Models\MonthlyRequestCim;
use App\Models\MonthlyRequest;
use App\Models\PaymentProfile;

use App\Exceptions\AuthorizeNetException;

class AuthorizeNetMonthlyRequest extends Cim
{
    public $monthlyRequest;

    public function getUserCimId()
    {
        $cim = MonthlyRequestCim::where('monthly_request_id', $this->monthlyRequest->id)->first();
        return $cim ? $cim->cim_id : false;
    }

    public function setMonthlyRequest(MonthlyRequest $monthlyRequest)
    {
        $this->monthlyRequest = $monthlyRequest;

        $this
            ->setUser($monthlyRequest->user)
            ->setFacility($monthlyRequest->facility);

        if ($monthlyRequest->paymentProfile) {
            $this->setPaymentProfile($monthlyRequest->paymentProfile->payment_profile);
        }

        return $this;
    }

    protected function saveCim($id = false)
    {
        if (!$this->cimResponse && !$id) {
            return false;
        }

        if (!$id) {
            $id = $this->cimResponse->getCustomerProfileId();
        }

        MonthlyRequestCim::create(
            [
            'monthly_request_id' => $this->monthlyRequest->id,
            'cim_id' => $id
            ]
        );

        return $this;
    }

    protected function savePaymentProfile($paymentProfileId)
    {
        $paymentProfile = new PaymentProfile();
        $paymentProfile->payment_profile = $paymentProfileId;

        $cim = MonthlyRequestCim::where('monthly_request_id', $this->monthlyRequest->id)->first();
        $cim->paymentProfile()->save($paymentProfile);

        return $this;
    }

    public function setPaymentProfile($paymentProfileId)
    {
        $this->verifyUserOwnsPayment($paymentProfileId);
        return $this->setApiPaymentProfile($paymentProfileId);
    }

    public function validateCard($description)
    {
        $this->auth();

        try {
            $response = $this->createTransaction('0.01', $description)->executeTransaction();
        } catch (\Exception $e) {
            throw new AuthorizeNetException("Could not validate payment method. Processor returned error: {$e->getMessage()}");
        }

        // Void test transaction
        (new Transactions())
            ->setUser($this->monthlyRequest->user)
            ->setFacility($this->monthlyRequest->facility)
            ->setTransaction($this->authorizeNetTransaction)
            ->void();

        $this->capture(); // Reset back to standard capture mode in case we are using this object to capture the transaction as well

        return $response;
    }
}
