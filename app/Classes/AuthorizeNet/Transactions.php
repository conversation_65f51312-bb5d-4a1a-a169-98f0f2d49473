<?php

namespace App\Classes\AuthorizeNet;

use App\Models\AuthorizeNetTransaction;
use App\Models\AuthNetType;

use App\Classes\AuthorizeNet\Authentication;
use App\Classes\AuthorizeNet;

use App\Exceptions\AuthorizeNetException;
use App\Exceptions\ApiGenericException;

use net\authorize\api\contract\v1 as AnetApi;
use net\authorize\api\controller as AnetController;

// Manages refunding and voiding auth net transactions
class Transactions extends AuthorizeNet
{

    const STATUS_SETTLED = 'settledSuccessfully';
    const STATUS_UNSETTLED = 'capturedPendingSettlement';
    const STATUS_REFUNDED = 'refundSettledSuccessfully';
    const STATUS_REFUND_PENDING = 'refundPendingSettlement';
    const STATUS_VOIDED = 'voided';
    const STATUS_DECLINED = 'declined';
    const STATUS_EXPIRED = 'expired';
    const STATUS_RETURNED = 'returnedItem';

    protected $transaction = null;
    protected $authNetTransaction = null;

    public function setTransaction(AuthorizeNetTransaction $transaction)
    {
        $this->transaction = $transaction;

        $this->total = $transaction->total;
        $this->description = $transaction->description;

        return $this;
    }

    /**
     * Get transaction information from authorize.net
     *
     * @return array
     */
    public function get($paymentDetails = null)
    {
        $this->verifyTransactionSet();

        $request = new AnetApi\GetTransactionDetailsRequest();
        if($paymentDetails){
          $this->merchantAuthentication->setName($paymentDetails->authorize_net_login);
        $this->merchantAuthentication->setTransactionKey($paymentDetails->authorize_net_transaction_key);
          $request->setMerchantAuthentication($this->merchantAuthentication);  
        }else{
            $request->setMerchantAuthentication($this->merchantAuthentication);
        }
        $request->setTransId($this->transaction->anet_trans_id);

        $controller = new AnetController\GetTransactionDetailsController($request);
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);

        $this->checkAuthNetResponse($response);

        $transaction = $response->getTransaction();
        $order = $transaction->getOrder();

        $card = $transaction->getPayment()->getCreditCard();
        $bank = $transaction->getPayment()->getBankAccount();

        $paymentType = $card ? 'card' : 'bank';

        if ($transaction->getReturnedItems()) {
            $returnedItems = collect($transaction->getReturnedItems())->map(
                function ($returned) {
                    return [
                    'id' => $returned->getId(),
                    'code' => $returned->getCode(),
                    'description' => $returned->getDescription()
                    ];
                }
            );
        }

        $this->authNetTransaction = [
            'transaction_id' => $transaction->getTransId(),
            'status' => $transaction->getTransactionStatus(),
            'response_code' => $transaction->getResponseCode(),
            'settle_amount' => $transaction->getSettleAmount(),
            'payment_type' => $paymentType,
            'returned_items' => isset($returnedItems) ? $returnedItems : false,
            'payment' => [
                'card_number' => $card ? $card->getCardNumber() : false,
                'account_number' => $bank ? $bank->getAccountNumber() : false
            ],
            'order' => [
                'invoice' => $order->getInvoiceNumber(),
                'description' => $order->getDescription()
            ]
        ];

        return $this->authNetTransaction;
    }

    /**
     * Refund a transaction. Currently only supports credit card transactions
     *
     * @return array
     */
    public function refund($paymentDetails = null)
    {
        $creditCard = new AnetApi\CreditCardType();
        $creditCard->setCardNumber($this->authNetTransaction['payment']['card_number']);
        $creditCard->setExpirationDate("XXXX");

        $payment = new AnetApi\PaymentType();
        $payment->setCreditCard($creditCard);

        // Set description and invoice based on the original request
        $order = new AnetApi\OrderType();
        $order->setDescription($this->authNetTransaction['order']['description']);
        if (isset($this->authNetTransaction['order']['invoice'])) {
            $order->setInvoiceNumber($this->authNetTransaction['order']['invoice']);
        }

        $this->transactionType = new AnetApi\TransactionRequestType();
        $this->transactionType->setTransactionType(AuthNetType::REFUND);
        $this->transactionType->setRefTransId($this->transaction->anet_trans_id);
        $this->transactionType->setPayment($payment);
        $this->transactionType->setAmount($this->transaction->total);
        $this->transactionType->setOrder($order);

        return $this->setupTransaction($paymentDetails)->executeTransaction();
    }

    public function void($paymentDetails = null)
    {
        $this->transactionType = new AnetApi\TransactionRequestType();
        $this->transactionType->setTransactionType(AuthNetType::VOID);
        $this->transactionType->setRefTransId($this->transaction->anet_trans_id);

        return $this->setupTransaction($paymentDetails)->executeTransaction();
    }

    public function captureAuthed()
    {
        $this->transactionType = new AnetApi\TransactionRequestType();
        $this->transactionType->setTransactionType(AuthNetType::CAPTURE_AUTHED);
        $this->transactionType->setRefTransId($this->transaction->anet_trans_id);

        return $this->setupTransaction()->executeTransaction();
    }

    /**
     * Prepare a non-capture authorize net transaction to be sent.
     *
     * @return $this
     */
    protected function setupTransaction($paymentDetails = null)
    {
        $this->verifyTransactionSet();

        $this->transaction = new AnetApi\CreateTransactionRequest();
        if($paymentDetails){
            $this->merchantAuthentication->setName($paymentDetails->authorize_net_login);
            $this->merchantAuthentication->setTransactionKey($paymentDetails->authorize_net_transaction_key);
            $this->transaction->setMerchantAuthentication($this->merchantAuthentication);
        }else{
            $this->transaction->setMerchantAuthentication($this->merchantAuthentication);
        }
        $this->transaction->setTransactionRequest($this->transactionType);

        return $this;
    }

    public function refundOrVoid($paymentDetails = null)
    {
        $this->verifyTransactionSet();

        $transaction = $this->get($paymentDetails);
        switch ($transaction['status']) {
        case self::STATUS_SETTLED:
            return $this->refund($paymentDetails);
        case self::STATUS_UNSETTLED:
            return $this->void($paymentDetails);
        default:
            throw new AuthorizeNetException('Transaction cannot be refunded or voided, returned status ' . $transaction['status']);
        }
    }

    protected function verifyTransactionSet()
    {
        if (!isset($this->transaction) || !$this->transaction) {
            throw new ApiGenericException('Must set a transaction before calling this function.');
        }

        if ($this->transaction->reservation) {
            $this->isReservation();
            $this->user = $this->transaction->reservation->user;
        }

        /*if ($this->transaction->monthlyParkingPayment) {
            // Monthly parking user could be soft deleted
            $this->setMonthlyParkingUser($this->transaction->monthlyParkingPayment->monthlyParkingUser()->withTrashed()->first());
        }*/

        return $this;
    }
}
