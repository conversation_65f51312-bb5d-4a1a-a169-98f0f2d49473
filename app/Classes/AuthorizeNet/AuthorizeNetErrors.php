<?php

namespace App\Classes\AuthorizeNet;

class AuthorizeNetErrors
{

    const ERROR_CODE_INVALID_ABA = 'E00027';

    public static $errorMap = [
        self::ERROR_CODE_INVALID_ABA => 'The routing number is invalid.'
    ];

    // Transform AuthorizeNet error messages into something more understandable for end users
    public static function transformErrorMessage(string $code, string $message): string
    {
        if (array_key_exists($code, self::$errorMap)) {
            return self::$errorMap[$code];
        }

        return "Response : " . $code . "  " . $message . "\n";
    }
}
