<?php

namespace App\Classes;

use Illuminate\Http\Request;
use App\Models\Facility;
use App\Models\AttractionType;
use App\Models\Event;
use App\Models\Neighborhood;
use App\Models\CMSPage;
use App\Models\Partner;
use XMLWriter;

class SiteMapGenerator
{
    private $xmlArray;

    private $webPageData = [
        ['title' => 'Search', 'slug' => 'search'],
        ['title' => 'Login', 'slug' => 'login'],
        ['title' => 'Create Account', 'slug' => 'create-account'],
        ['title' => 'Reset Password', 'slug' => 'reset-password'],
        ['title' => 'Yankee Reservations', 'slug' => 'yankee-reservations'],
        ['title' => 'Contact', 'slug' => 'contact'],
        ['title' => 'Monthly Parking', 'slug' => 'monthly-parking'],
        ['title' => 'Monthly Parking Request', 'slug' => 'monthly-parking/request']
    ];

    const XMLNS = 'http://www.sitemaps.org/schemas/sitemap/0.9';

    public function __construct()
    {
        $this->CMS();
        $this->webSlugs();
        $this->facilities();
        $this->attractions();
        $this->events();
        $this->neighborhoods();
    }

    public function getXmlSiteMap($path = false)
    {
        $path = $path ?: env('WEB_ROOT');
        $writer = new XMLWriter();
        $writer->openUri($path . '/site-map.xml');
        $writer->startDocument('1.0', 'UTF-8');
        $writer->setIndent(4);
        //Urlset wrap
        $writer->startElement('urlset');
        $writer->writeAttribute('xmlns', self::XMLNS);

        foreach ($this->xmlArray as $xmls) {
            foreach ($xmls['data'] as $xml) {
                $writer->startElement('url');

                $writer->writeElement('loc', $xml['loc']);
                if (array_key_exists('lastmod', $xml)) {
                    $writer->writeElement('lastmod', $xml['lastmod']);
                }

                $writer->endElement();
            }
        }
        $writer->endElement();
        $writer->endDocument();
    }

    public function getJsonSiteMap()
    {
        return $this->xmlArray;
    }

    public function webSlugs()
    {
        $this->xmlArray['Content'] = [];
        $this->xmlArray['Content']['data'] = [];
        $this->xmlArray['Content']['link'] = false;
        foreach ($this->webPageData as $webPage) {
            $xmlData = [];
            $xmlData['loc'] = config('app.web_url') .'/'. $webPage['slug'];
            $xmlData['title'] = $webPage['title'];
            $xmlData['slug'] = $webPage['slug'];
            array_push($this->xmlArray['Content']['data'], $xmlData);
        }
    }

    public function facilities()
    {
        $facilities = Facility::all()->makeVisible('updated_at');
        $this->xmlArray['facilities'] = [];
        $this->xmlArray['facilities']['data'] = [];
        $this->xmlArray['facilities']['link'] = 'facilities';
        foreach ($facilities as $facility) {
            $xmlData = [];
            $xmlData['loc'] = config('app.web_url') . '/facility/' . $facility->slug;
            $xmlData['lastmod'] = $facility->updated_at->toDateString();
            $xmlData['title'] = $facility->full_name;
            $xmlData['slug'] = 'facility/' . $facility->slug;
            array_push($this->xmlArray['facilities']['data'], $xmlData);
        }
    }

    public function attractions()
    {
        $attractionTypes = AttractionType::with('attractions')->get();
        $this->xmlArray['attractions'] = [];
        $this->xmlArray['attractions']['data'] = [];
        $this->xmlArray['attractions']['link'] = 'attractions';
        foreach ($attractionTypes as $type) {
            foreach ($type->attractions as $attraction) {
                $xmlData = [];
                $xmlData['loc'] = config('app.web_url') .'/attractions/'. $type->slug . '/' . $attraction->slug;
                $xmlData['lastmod'] = $attraction->updated_at->toDateString();
                $xmlData['title'] = $attraction->name;
                $xmlData['slug'] = $type->slug . '/' . $attraction->slug;
                array_push($this->xmlArray['attractions']['data'], $xmlData);
            }
        }
    }

    public function events()
    {
        $events = Event::all()->makeVisible('updated_at');
        $this->xmlArray['events'] = [];
        $this->xmlArray['events']['data'] = [];
        $this->xmlArray['events']['link'] = false;
        foreach ($events as $event) {
            $xmlData = [];
            $xmlData['loc'] = config('app.web_url') . '/events/' . $event->slug;
            $xmlData['lastmod'] = $event->updated_at->toDateString();
            $xmlData['title'] = $event->title;
            $xmlData['slug'] = 'events/' . $event->slug;
            array_push($this->xmlArray['events']['data'], $xmlData);
        }
    }

    public function neighborhoods()
    {
        $neighborhoods = Neighborhood::all()->makeVisible('updated_at');
        $this->xmlArray['neighborhoods'] = [];
        $this->xmlArray['neighborhoods']['data'] = [];
        $this->xmlArray['neighborhoods']['link'] = false;
        foreach ($neighborhoods as $neighborhood) {
            $xmlData = [];
            $xmlData['loc'] = config('app.web_url') . '/neighborhoods/' . $neighborhood->slug;
            $xmlData['lastmod'] = $neighborhood->updated_at->toDateString();
            $xmlData['title'] = $neighborhood->title;
            $xmlData['slug'] = 'neighborhoods/' . $neighborhood->slug;
            array_push($this->xmlArray['neighborhoods']['data'], $xmlData);
        }
    }

    public function CMS()
    {
        $CMSPages = CMSPage::all()->makeVisible('updated_at');
        $this->xmlArray['Content'] = [];
        $this->xmlArray['Content']['data'] = [];
        $this->xmlArray['Content']['link'] = false;
        foreach ($CMSPages as $page) {
            $xmlData = [];
            $xmlData['loc'] = config('app.web_url') .'/'. $page->slug;
            $xmlData['lastmod'] = $page->updated_at->toDateString();
            $xmlData['title'] = $page->title;
            $xmlData['slug'] = $page->slug;
            array_push($this->xmlArray['Content']['data'], $xmlData);
        }
    }
}
