<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use App\Services\LoggerFactory;
/**
 * Utility class for interacting with the Ticketech APIs
 */
class GetCountryCode
{

    protected $log; 
    public function __construct()
    {
        //$logFactory         =  new  LoggerFactory(); 
        //$this->log = $logFactory->setPath('logs/parkengage/get-country-code')->createLogger('get-country-code');
    }

    protected static function baseCall($ip)
    {
        //$runfile = 'http://www.geoplugin.net/json.gp?ip=' . $ip;   
        $runfile = "http://ipinfo.io/".$ip; 
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $runfile);
        curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);
        $response = curl_exec ($ch);
        $response = json_decode($response);
        $result = '';
        if(isset($response->country)){
            $result = $response->country;
        }
        curl_close ($ch); 
        return $result;
    }

    public static function getCountryCode($ip)
    {
        return self::baseCall($ip);
    }
    
}