<?php

namespace App\Classes;

use Carbon\Carbon;

use App\Models\SentMessage;

/**
 * Keeps track of sent messages and limits the rate at which
 * they can be sent
 */
class MessageRateLimiter
{

    /**
     * Number of mesages that can be sent within $limitTimeFrame
     *
     * @var [type]
     */
    protected $rateLimit;

    /**
     * Number of minutes to wait before deleting a sent message
     *
     * @var [type]
     */
    protected $limitTimeFrame;

    /**
     * Sent messages
     *
     * @var Collection([SentMessage])
     */
    protected $sentMessages;

    public function __construct()
    {
        $this->rateLimit = config('twilio.message_limit', 5);
        $this->limitTimeFrame = config('twilio.message_time_frame', 5);
    }

    /**
     * Make sure that the current message doesn't hit our rate limit
     *
     * @return boolean
     */
    public function isOverRateLimit($to, $ip)
    {
        // Delete any old messages
        $this->deleteOldMessages();

        // Get any messages left with the given IP and to
        $sent = SentMessage::where('to', $to)->where('ip', $ip);

        return $sent ? $sent->count() >= $this->rateLimit : false;
    }

    /**
     * Track the current message send for rate limiting purposes
     *
     * @return [type] [description]
     */
    public function track($to, $ip)
    {
        return SentMessage::create(
            [
            'to' => $to,
            'ip' => $ip
            ]
        );
    }

    /**
     * Delete all messages old than the time period given
     * by $this->limitTimeFrame
     *
     * @return [type] [description]
     */
    public function deleteOldMessages()
    {
        $expired = Carbon::now()->subMinutes($this->limitTimeFrame);

        SentMessage::where('created_at', '<=', $expired->toDateTimeString())->delete();
    }
}
