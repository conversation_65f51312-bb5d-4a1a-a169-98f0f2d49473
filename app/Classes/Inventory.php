<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use App\Models\FacilityAvailability;
use App\Models\FacilityInventory;
use App\Models\FacilityPartnerAvailability;
use App\Models\FacilityPartnerAvailabilityCron;
use App\Models\HoursOfOperation;
use App\Models\FacilityInventorySpecificDate;
use App\Models\Facility;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Collection;

class Inventory
{
    const DAY_START_HOUR    = 0;
    const DAY_END_HOUR      = 23;
    const TWENTY_FOUR_HOURS = 24;
    const DAYS_IN_WEEK      = 7;
    const NINETY_DAYS       = 98;


    public function getSpectialDates($facilityId)
    {
        Carbon::setWeekStartsAt(Carbon::SUNDAY);
        $facilitySpecify = FacilityInventorySpecificDate::where('facility_id', $facilityId)->where('date', '>=', Carbon::now()->startOfWeek()->format('Y-m-d'))->select('date')->get()->toArray();
        $SpectialDates = [];
        foreach ($facilitySpecify as $SpectialDate) {
            $SpectialDates[] = $SpectialDate['date'];
        }
        return $SpectialDates;
    }

    /**
     * @param $facilityId
     * @return array
     * @throws \Exception
     */
    public function getInventory($facilityId)
    {

        Carbon::setWeekStartsAt(Carbon::SUNDAY);

        //to get special dates    
        $SpectialDates = $this->getSpectialDates($facilityId);
        $startDate = Carbon::today()->startOfWeek()->format('Y-m-d');
        do {
            $dates = $this->generateArrayOfDates(self::DAYS_IN_WEEK, $startDate);
            $date_periods = [];
            foreach ($dates as $key => $value) {
                $date_periods[] = $value->format('Y-m-d');
            }
            $startDate = Carbon::parse($startDate)->addDays(self::DAYS_IN_WEEK)->format('Y-m-d');
        } while (count(array_intersect($SpectialDates, $date_periods)));

        $results = FacilityInventory::where('facility_id', $facilityId)->whereIn(
            'date',
            $date_periods
        )->get();
        foreach ($results as $result) {
            $response[$result->date] = json_decode($result->availability);
        }

        if (!isset($response)) {
            $response = 'Data not available';
        } else {
            $dates = $this->generateArrayOfDates(self::DAYS_IN_WEEK, Carbon::today()->startOfWeek()->format('Y-m-d'));
            $i = 0;
            $date_periods = [];
            foreach ($dates as $key => $value) {
                $date_periods[] = $value->format('Y-m-d');
            }
            $result = [];
            foreach ($response as $key => $value) {
                $result[$date_periods[$i]] = $value;
                $i++;
            }
            $response = $result;
        }

        return $response;
    }

    /**
     * @param $facilityId
     * @return array
     * @throws \Exception
     */
    public function getInventoryForSpecialDates($facilityId)
    {
        $facilitySpecify = FacilityInventorySpecificDate::where('facility_id', $facilityId)->where('date', '>=', Carbon::now()->format('Y-m-d'))->select('date')->get();

        Carbon::setWeekStartsAt(Carbon::SUNDAY);
        $results = FacilityInventory::where('facility_id', $facilityId)->whereIn(
            'date',
            $facilitySpecify
        )->get();
        foreach ($results as $result) {
            $response[$result->date] = json_decode($result->availability);
        }

        if (!isset($response)) {
            $response = collect([]);
        }

        return $response;
    }

    /**
     * @param $request
     * @return string
     * @throws \Exception
     * method is invoked when admin selects dow as option on admin screen
     */
    public function setInventoryForDay($request)
    {

        $errorFlag = true;

        $SpectialDates = $this->getSpectialDates($request->facility_id);

        //generate array of dates for ninety eight days
        $this->generateInventoryForNinetyDays($request->facility_id, $request->availability, $request->day);
        //get the date for the selected day
        if (date('w', strtotime($request->day)) < date('w', strtotime('today'))) {
            $date = date('Y-m-d', strtotime('last ' . $request->day));
        } else {
            $date = date('Y-m-d', strtotime($request->day));
        }

        $today = $request->day;

        for ($i = 0; $i <= 14; $i++) {

            \Log::info($date);
            $partnerData       =
                FacilityPartnerAvailabilityCron::firstOrNew(['facility_id' => $request->facility_id, 'date' => $date]);
            $inventory         =
                FacilityInventory::firstOrNew(['facility_id' => $request->facility_id, 'date' => $date]);
            $inventoryArray    = json_decode($inventory->availability, true);
            $available         =
                FacilityAvailability::firstOrNew(['facility_id' => $request->facility_id, 'date' => $date]);
            $availabilityArray = json_decode($available->availability, true);

            $spots = (int)$request->availability;

            //fetch operational hours of this facility
            $hours =
                HoursOfOperation::where('facility_id', $request->facility_id)->orderBy('day_of_week', 'ASC')->get();

            //fetch the operational hours for this facility
            $getOperationalHours = $this->getOperationalHours($request->facility_id, $hours);
            $operationalHours    = $getOperationalHours['operationalHours'];
            //if the facility works after 12 am save those hours into an array
            $remainder       = $getOperationalHours['remainder'];
            $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
            $arrayForPartner = [];

            //multiple HOO
            foreach ($operationalHoursArray[$today] as $operationalHour) {

                $currentInventory = isset($availabilityArray[$operationalHour]) ? $availabilityArray[$operationalHour] : 0;
                if ($currentInventory < $spots) {
                    $difference        = isset($inventoryArray[$operationalHour]) ? ($inventoryArray[$operationalHour] - $currentInventory) : 0;
                    $inventoryOpenTime = isset($inventoryArray[$operationalHour]) ? $inventoryArray[$operationalHour] : 0;
                    $availabilityOpenTime = isset($availabilityArray[$operationalHour]) ? $availabilityArray[$operationalHour] : 0;
                    $booked            = $inventoryOpenTime - $availabilityOpenTime;
                    $finalAvailability = isset($inventoryArray[$operationalHour]) ? $inventoryArray[$operationalHour] : 0 - $booked;
                    isset($availabilityArray[$operationalHour]) ? $availabilityArray[$operationalHour] = $spots - $difference :
                        $availabilityArray[$operationalHour] = $spots;

                    \Log::info(
                        'difference ' . $difference . ' booked ' . $booked . ' final availability ' . $finalAvailability
                    );
                } elseif ($spots < $currentInventory) {
                    $difference        = $inventoryArray[$operationalHour] - $spots;
                    $booked            = $inventoryArray[$operationalHour] - $availabilityArray[$operationalHour];
                    $finalAvailability = $inventoryArray[$operationalHour] - $booked;
                    \Log::info(
                        'difference ' . $difference . ' booked ' . $booked . ' final availability ' . $finalAvailability
                    );
                    isset($availabilityArray[$operationalHour]) ?
                        $availabilityArray[$operationalHour] = $finalAvailability - $difference :
                        $availabilityArray[$operationalHour] = $spots;
                }
                $arrayForPartner[$operationalHour] = $spots > 0 ? 1 : 0;
                $inventoryArray[$operationalHour]  = $spots;

                $errorFlag = false;
            }

            if (!in_array($date, $SpectialDates)) {

                $inventory->availability = json_encode($inventoryArray, JSON_FORCE_OBJECT);
                $inventory->save();

                $available->availability = json_encode($availabilityArray, JSON_FORCE_OBJECT);
                $available->save();

                //update avaliablity when inventory_threshold has value by vikrant
                $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($inventory->availability, true), $availabilityArray, $request->facility_id);
                if ($newPartnerAvailability) {
                    $partnerData->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                    $partnerData->save();
                }
            }
            Carbon::setWeekStartsAt(Carbon::SUNDAY);
            $lastDate = Carbon::now()->startOfWeek()->addDays(self::NINETY_DAYS)->format('Y-m-d');

            $date = new \DateTime($date);
            $date->modify("next {$today}");
            $date = $date->format('Y-m-d');
            if (strtotime($date) > strtotime($lastDate)) {
                break;
            }
        }
        if ($errorFlag) {
            throw new ApiGenericException("Inventory can not be updated for non operational day.", 422);
        }

        return 'success';
    }

    /**
     * @param $request
     * @return string
     * @throws \Exception
     * method is invoked when admin selects hr as option on admin screen
     */
    public function setInventoryForHour($request)
    {
        $SpectialDates = $this->getSpectialDates($request->facility_id);
        $dates = $this->generateArrayOfDates(self::NINETY_DAYS, '', '', true);
        $request->availability = (int)$request->availability;
        $hours               = HoursOfOperation::where('facility_id', $request->facility_id)->get();
        $getOperationalHours = $this->getOperationalHours($request->facility_id, $hours);
        $operationalHours    = $getOperationalHours['operationalHours'];
        $remainder           =  $getOperationalHours['remainder'];
        $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
        $errorFlag = true;

        foreach ($dates as $key => $date) {
            $today = $date->format('l');
            $workingHoursRange = $operationalHoursArray[$today];

            if (in_array($request->hr, $workingHoursRange)) {
                if (!in_array($date->format('Y-m-d'), $SpectialDates)) {

                    $inventory                = FacilityInventory::where(
                        ['facility_id' => $request->facility_id, 'date' => $date->format('Y-m-d')]
                    )->first();
                    $facility                 = FacilityAvailability::where(
                        ['facility_id' => $request->facility_id, 'date' => $date->format('Y-m-d')]
                    )->first();
                    $partnerAvailabilityModel = FacilityPartnerAvailability::where(
                        ['facility_id' => $request->facility_id, 'date' => $date->format('Y-m-d')]
                    )->first();

                    if ($inventory && $facility) {
                        $inventoryAvailability = json_decode($inventory->availability, true);
                        $availability          = json_decode($facility->availability, true);

                        $currentInventory =
                            isset($inventoryAvailability[$request->hr]) ? $inventoryAvailability[$request->hr] : 0;

                        if ($request->availability > $currentInventory) {
                            $difference        = $request->availability - $currentInventory;
                            $booked            =
                                isset($inventoryAvailability[$request->hr]) ? $inventoryAvailability[$request->hr] : 0 - isset($availability[$request->hr]) ? $availability[$request->hr] : 0;
                            $finalAvailability =
                                isset($availability[$request->hr]) ? $availability[$request->hr] : 0 - $booked;

                            $availability[$request->hr]          = $finalAvailability + $difference;
                            $inventoryAvailability[$request->hr] = $request->availability;
                        } elseif ($request->availability < $currentInventory) {
                            $difference        = $currentInventory - $request->availability;
                            $booked            = $inventoryAvailability[$request->hr] - $availability[$request->hr];
                            $finalAvailability = $inventoryAvailability[$request->hr] - $booked;

                            $availability[$request->hr]          = $finalAvailability - $difference;
                            $inventoryAvailability[$request->hr] = $request->availability;
                        } else {
                            $availability[$request->hr]          = $request->availability;
                            $inventoryAvailability[$request->hr] = $request->availability;
                        }

                        $inventory->availability = json_encode($inventoryAvailability, JSON_FORCE_OBJECT);
                        $inventory->save();

                        $facility->availability = json_encode($availability, JSON_FORCE_OBJECT);
                        $facility->save();
                        $errorFlag = false;
                    }

                    if ($partnerAvailabilityModel) {
                        //update avaliablity when inventory_threshold has value by vikrant
                        $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($inventory->availability, true), $availability, $request->facility_id);
                        if ($newPartnerAvailability) {

                            $partnerAvailabilityModel->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                            $partnerAvailabilityModel->save();
                        }
                    }
                }
            }
        }

        if ($errorFlag) {
            throw new ApiGenericException("Inventory can not be updated for non operational hour.", 422);
        }

        return 'success';
    }



    public function setInventoryForHourByMatrixForSpecilaDate($hr, $facility_id, $day, $value)
    {
        //to get special dates    
        $SpectialDates = $this->getSpectialDates($facility_id);
        $dates = $this->generateArrayOfDates(self::NINETY_DAYS, '', '', true);

        $hours               = HoursOfOperation::where('facility_id', $facility_id)->get();
        $getOperationalHours = $this->getOperationalHours($facility_id, $hours);
        $operationalHours    = $getOperationalHours['operationalHours'];
        $remainder           =  $getOperationalHours['remainder'];
        $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
        $value = (int)$value;

        $date = Carbon::parse($day);
        $today = $date->format('l');

        if (in_array($date->format('Y-m-d'), $SpectialDates)) {

            $workingHoursRange = $operationalHoursArray[$today];

            if (in_array($hr, $workingHoursRange)) {
                $inventory                = FacilityInventory::where(
                    ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')]
                )->first();
                $facility                 = FacilityAvailability::where(
                    ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')]
                )->first();
                $partnerAvailabilityModel = FacilityPartnerAvailability::where(
                    ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')]
                )->first();

                if ($inventory && $facility) {
                    $inventoryAvailability = json_decode($inventory->availability, true);
                    $availability          = json_decode($facility->availability, true);

                    $currentInventory =
                        isset($inventoryAvailability[$hr]) ? $inventoryAvailability[$hr] : 0;

                    if ($value > $currentInventory) {
                        $difference        = $value - $currentInventory;
                        $booked            =
                            isset($inventoryAvailability[$hr]) ? $inventoryAvailability[$hr] : 0 - isset($availability[$hr]) ? $availability[$hr] : 0;
                        $finalAvailability =
                            isset($availability[$hr]) ? $availability[$hr] : 0 - $booked;

                        $availability[$hr]          = $finalAvailability + $difference;
                        $inventoryAvailability[$hr] = $value;
                    } elseif ($value < $currentInventory) {
                        $difference        = $currentInventory - $value;
                        $booked            = $inventoryAvailability[$hr] - $availability[$hr];
                        $finalAvailability = $inventoryAvailability[$hr] - $booked;

                        $availability[$hr]          = $finalAvailability - $difference;
                        $inventoryAvailability[$hr] = $value;
                    } else {
                        $availability[$hr]          = $value;
                        $inventoryAvailability[$hr] = $value;
                    }

                    $inventory->availability = json_encode($inventoryAvailability, JSON_FORCE_OBJECT);
                    $inventory->save();

                    $facility->availability = json_encode($availability, JSON_FORCE_OBJECT);
                    $facility->save();
                }

                if ($partnerAvailabilityModel) {

                    //update avaliablity when inventory_threshold has value by vikrant
                    $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($inventory->availability, true), $availability, $facility_id);
                    if ($newPartnerAvailability) {

                        $partnerAvailabilityModel->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                        $partnerAvailabilityModel->save();
                    }
                }
            }
        }

        return 'success';
    }

    public function setInventoryForHourByMatrix($hr, $facility_id, $day, $value)
    {
        //to get special dates    
        $SpectialDates = $this->getSpectialDates($facility_id);
        $dates = $this->generateArrayOfDates(self::NINETY_DAYS, '', '', true);

        $hours               = HoursOfOperation::where('facility_id', $facility_id)->get();
        $getOperationalHours = $this->getOperationalHours($facility_id, $hours);
        $operationalHours    = $getOperationalHours['operationalHours'];
        $remainder           =  $getOperationalHours['remainder'];
        $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
        $value = (int)$value;
        foreach ($dates as $key => $date) {

            $today = $date->format('l');

            if (($today != $day) || in_array($date->format('Y-m-d'), $SpectialDates)) {
                continue;
            } else {

                $workingHoursRange = $operationalHoursArray[$today];

                if (in_array($hr, $workingHoursRange)) {
                    $inventory                = FacilityInventory::where(
                        ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')]
                    )->first();
                    $facility                 = FacilityAvailability::where(
                        ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')]
                    )->first();
                    $partnerAvailabilityModel = FacilityPartnerAvailability::where(
                        ['facility_id' => $facility_id, 'date' => $date->format('Y-m-d')]
                    )->first();


                    if ($inventory && $facility) {
                        $inventoryAvailability = json_decode($inventory->availability, true);
                        $availability          = json_decode($facility->availability, true);

                        $currentInventory =
                            isset($inventoryAvailability[$hr]) ? $inventoryAvailability[$hr] : 0;

                        if ($value > $currentInventory) {
                            $difference        = $value - $currentInventory;
                            $booked            =
                                isset($inventoryAvailability[$hr]) ? $inventoryAvailability[$hr] : 0 - isset($availability[$hr]) ? $availability[$hr] : 0;
                            $finalAvailability =
                                isset($availability[$hr]) ? $availability[$hr] : 0 - $booked;

                            $availability[$hr]          = $finalAvailability + $difference;
                            $inventoryAvailability[$hr] = $value;
                        } elseif ($value < $currentInventory) {
                            $difference        = $currentInventory - $value;
                            $booked            = $inventoryAvailability[$hr] - $availability[$hr];
                            $finalAvailability = $inventoryAvailability[$hr] - $booked;

                            $availability[$hr]          = $finalAvailability - $difference;
                            $inventoryAvailability[$hr] = $value;
                        } else {
                            $availability[$hr]          = $value;
                            $inventoryAvailability[$hr] = $value;
                        }

                        $inventory->availability = json_encode($inventoryAvailability, JSON_FORCE_OBJECT);
                        $inventory->save();

                        $facility->availability = json_encode($availability, JSON_FORCE_OBJECT);
                        $facility->save();
                    }

                    if ($partnerAvailabilityModel) {

                        //update avaliablity when inventory_threshold has value by vikrant
                        $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($inventory->availability, true), $availability, $facility_id);
                        if ($newPartnerAvailability) {

                            $partnerAvailabilityModel->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                            $partnerAvailabilityModel->save();
                        }
                    }
                }
            }
        }

        return 'success';
    }

    public function updateAllInventory($request)
    {
        $errorFlag = false;
        foreach ($request->availability as $key => $availability) {
            $spots = $availability;

            $partnerModel =
                FacilityPartnerAvailability::where(['facility_id' => $request->facility_id, 'date' => $key])->first();

            $inventory         =
                FacilityInventory::where(['facility_id' => $request->facility_id, 'date' => $key])->first();
            $availabilityModel =
                FacilityAvailability::where(['facility_id' => $request->facility_id, 'date' => $key])->first();

            $inventoryAvailability = json_decode($inventory->availability, true);
            $currentAvailability   = json_decode($availabilityModel->availability, true);
            // VP : PIMS - 1434
            // $partnerData           = json_decode($partnerModel->availability, true); 
            foreach ($spots as $spotKey => $spot) {
                if (!isset($inventoryAvailability[$spotKey])) {
                    $errorFlag = true;
                }
            }
            if ($errorFlag) {
                throw new ApiGenericException("Inventory can not be updated for non operational hour.", 422);
            }
            //fetch operational hours of this facility
            $hours =
                HoursOfOperation::where('facility_id', $request->facility_id)->orderBy('day_of_week', 'ASC')->get();

            $getOperationalHours = $this->getOperationalHours($request->facility_id, $hours);
            $operationalHours    = $getOperationalHours['operationalHours'];
            $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
            $today               = date('l', strtotime($key));

            $workingHoursRange = $operationalHoursArray[$today];
            $remainder           =  $getOperationalHours['remainder'];

            try {
                $message = "Facility does not work on given hour";
                foreach ($spots as $spotKey => $spot) {
                    if ($spot > -1) {
                        if (!in_array($spotKey, $workingHoursRange)) {
                            $currentInventory =
                                isset($inventoryAvailability[$spotKey]) ? $inventoryAvailability[$spotKey] : 0;
                            if ($currentInventory < $spot) {
                                $difference        = $spot - $currentInventory;
                                $booked            = $inventoryAvailability[$spotKey] - $currentAvailability[$spotKey];
                            } elseif ($spotKey < $currentInventory) {
                                $difference        = $currentInventory - $spot;
                                $booked            = $inventoryAvailability[$spotKey] - $currentAvailability[$spotKey];
                            }
                        } else {

                            $message = "Value cannot be less than 0";
                        }
                    }
                }
            } catch (\Exception $e) {
                throw new ApiGenericException($message, 422);
            }
            $dayValue = Carbon::parse($key)->format('l');

            foreach ($operationalHoursArray[$today] as $operationalHour) {
                $openTime         = $operationalHour;
                $currentInventory = isset($inventoryAvailability[$openTime]) ? $inventoryAvailability[$openTime] : 0;
                if ((isset($spots[$openTime]))) {
                    if ($spots[$openTime] > -1) {

                        if ($currentInventory < $spots[$openTime]) {

                            $this->setInventoryForHourByMatrix($openTime, $request->facility_id, $dayValue, $spots[$openTime]);
                        } elseif ($spots[$openTime] < $currentInventory) {

                            $this->setInventoryForHourByMatrix($openTime, $request->facility_id, $dayValue, $spots[$openTime]);
                        }
                    } else {
                        throw new ApiGenericException('Value cannot be less than 0', 422);
                    }
                }
            }
        }

        return 'success';
    }

    public function updateAllInventoryForSpecialDate($request)
    {
        $errorFlag = false;
        foreach ($request->spec_availability as $key => $availability) {
            $spots = $availability;

            $partnerModel =
                FacilityPartnerAvailability::where(['facility_id' => $request->facility_id, 'date' => $key])->first();

            $inventory         =
                FacilityInventory::where(['facility_id' => $request->facility_id, 'date' => $key])->first();
            $availabilityModel =
                FacilityAvailability::where(['facility_id' => $request->facility_id, 'date' => $key])->first();

            $inventoryAvailability = json_decode($inventory->availability, true);
            $currentAvailability   = json_decode($availabilityModel->availability, true);
            $partnerData           = json_decode($partnerModel->availability, true);
            foreach ($spots as $spotKey => $spot) {
                if (!isset($inventoryAvailability[$spotKey])) {
                    $errorFlag = true;
                }
            }
            if ($errorFlag) {
                throw new ApiGenericException("Inventory can not be updated for non operational hour.", 422);
            }
            //fetch operational hours of this facility
            $hours =
                HoursOfOperation::where('facility_id', $request->facility_id)->orderBy('day_of_week', 'ASC')->get();

            $getOperationalHours = $this->getOperationalHours($request->facility_id, $hours);
            $operationalHours    = $getOperationalHours['operationalHours'];
            $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
            $today               = date('l', strtotime($key));

            $workingHoursRange = $operationalHoursArray[$today];
            $remainder           =  $getOperationalHours['remainder'];

            try {
                $message = "Facility does not work on given hour";
                foreach ($spots as $spotKey => $spot) {
                    if ($spot > -1) {
                        if (!in_array($spotKey, $workingHoursRange)) {
                            $currentInventory =
                                isset($inventoryAvailability[$spotKey]) ? $inventoryAvailability[$spotKey] : 0;
                            if ($currentInventory < $spot) {
                                $difference        = $spot - $currentInventory;
                                $booked            = $inventoryAvailability[$spotKey] - $currentAvailability[$spotKey];
                            } elseif ($spotKey < $currentInventory) {
                                $difference        = $currentInventory - $spot;
                                $booked            = $inventoryAvailability[$spotKey] - $currentAvailability[$spotKey];
                            }
                        } else {

                            $message = "Value cannot be less than 0";
                        }
                    }
                }
            } catch (\Exception $e) {
                throw new ApiGenericException($message, 422);
            }
            $dayValue = Carbon::parse($key)->format('Y-m-d');

            foreach ($operationalHoursArray[$today] as $operationalHour) {
                $openTime         = $operationalHour;
                $currentInventory = isset($inventoryAvailability[$openTime]) ? $inventoryAvailability[$openTime] : 0;
                if ((isset($spots[$openTime]))) {
                    if ($spots[$openTime] > -1) {

                        if ($currentInventory < $spots[$openTime]) {

                            $this->setInventoryForHourByMatrixForSpecilaDate($openTime, $request->facility_id, $dayValue, $spots[$openTime]);
                        } elseif ($spots[$openTime] < $currentInventory) {

                            $this->setInventoryForHourByMatrixForSpecilaDate($openTime, $request->facility_id, $dayValue, $spots[$openTime]);
                        }
                    } else {
                        throw new ApiGenericException('Value cannot be less than 0', 422);
                    }
                }
            }
        }

        return 'success';
    }

    /**
     * @param $request
     * @return string
     * @throws \Exception
     * method is invoked when admin selects all day as option on admin screen
     */
    public function setInventoryForAllDay($request)
    {
        $request->availability = (int)$request->availability;
        $this->generateInventoryForWeek($request->facility_id, $request->availability, true);
        return 'success';
    }

    /**
     * @param $request
     * @return string
     * @throws \Exception
     * method is invoked when admin selects special date range as option on admin screen
     */
    public function setInventoryForSpecialDate($request)
    {
        $hours_flag = true;

        //to do
        // get date range and update/insert in invetory, avialability
        $request->availability = (int)$request->availability;

        //set week start to sunday to be uniform in all cases
        Carbon::setWeekStartsAt(Carbon::SUNDAY);
        $carbonStartDate = Carbon::parse($request->start_date);
        $carbonEndDate = Carbon::parse($request->end_date)->addDays(1);

        $period = new \DatePeriod(
            new \DateTime($carbonStartDate),
            new \DateInterval('P1D'),
            new \DateTime($carbonEndDate)
        );

        //fetch operational hours of this facility
        $facilityId = (int)$request->facility_id;

        $hours = HoursOfOperation::where('facility_id', $facilityId)->orderBy('day_of_week', 'ASC')->get();
        $getOperationalHours = $this->getOperationalHours($facilityId, $hours);

        //check if some days are closed
        $sd_validation = isset($request->sd_validation) ? $request->sd_validation : true;
        if ($sd_validation) {


            $checkAllDayOpen = false;
            $alldaycount = 0;
            $periodCount = 0;
            foreach ($period as $key => $value) {
                $periodCount++;
                $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
                $today            = $value->format('l');
                if (!count($operationalHoursArray[$today])) {
                    $checkAllDayOpen = true;
                    $alldaycount++;
                }
            }

            if ($alldaycount === $periodCount) {
                throw new ApiGenericException("Inventory can not be updated for non operational day.", 422);
            } else if ($alldaycount > 0) {
                return ['warning' =>  true, 'message' => "Facility is closed in the selected date/date range.",];
            }
        }

        foreach ($period as $key => $value) {
            $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
            $today            = $value->format('l');

            $partnerFacility = FacilityPartnerAvailability::firstOrNew(
                ['facility_id' => $facilityId, 'date' => $value->format('Y-m-d')]
            );

            $facility       =
                FacilityInventory::firstOrNew(['facility_id' => $facilityId, 'date' => $value->format('Y-m-d')]);

            $availabilities = json_decode($facility->availability, true);

            //fetch availability
            $facilityAvailability =
                FacilityAvailability::firstOrNew(['facility_id' => $facilityId, 'date' => $value->format('Y-m-d')]);
            $availabilityDecoded  = json_decode($facilityAvailability->availability, true);

            $resetInventory = [];
            if ($this->is_iterable($availabilities)) {
                foreach ($availabilities as $index => $availability) {
                    $resetInventory[] = -1;
                }
            }
            $spots = $request->availability;

            foreach ($operationalHoursArray[$today] as $operationalHour) {

                //$availabilityDecoded is data from facility_availabilities table and $availabilities is data from facility_inventories table
                $currentInventory =
                    isset($availabilities[$operationalHour]) ? $availabilities[$operationalHour] : 0;
                if ($currentInventory < $spots) {
                    $difference = $spots - $currentInventory;
                    isset($availabilityDecoded[$operationalHour]) ? $availabilityDecoded[$operationalHour] += $difference :
                        $availabilityDecoded[$operationalHour] = $difference;
                } elseif ($spots < $currentInventory) {
                    $difference = $currentInventory - $spots;
                    isset($availabilityDecoded[$operationalHour]) ? $availabilityDecoded[$operationalHour] -= $difference :
                        $availabilityDecoded[$operationalHour] = $difference;
                }

                $availabilities[$operationalHour] = $spots;
            }
            if (count($operationalHoursArray[$today])) {
                $hours_flag = false;
                //update special date table
                $facilitySpecify = FacilityInventorySpecificDate::firstOrNew(['facility_id' => $facilityId, 'date' => $value->format('Y-m-d')]);
                $facilitySpecify->save();
                $facility->availability = json_encode($availabilities, JSON_FORCE_OBJECT);
                $facility->save();
                $facilityAvailability->availability = json_encode($availabilityDecoded, JSON_FORCE_OBJECT);
                $facilityAvailability->save();

                //update avaliablity when inventory_threshold has value by vikrant
                $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($facility->availability, true), $availabilityDecoded, $facilityId);
                if ($newPartnerAvailability) {

                    $partnerFacility->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                    $partnerFacility->save();
                }
            }
        }
        if ($hours_flag) {
            throw new ApiGenericException("Inventory can not be updated for non operational day.", 422);
        }
        return "success";
    }

    /**
     * @param $facilityId
     * @param int $spots
     * @param bool $forAllDay
     * @return string
     * @throws \Exception
     */
    protected function generateInventoryForWeek($facilityId, $spots = 0, $forAllDay = false)
    {
        $SpectialDates = $this->getSpectialDates($facilityId);
        //generate a range of dates
        $period = $this->generateArrayOfDates(self::NINETY_DAYS, '', '', true);

        //fetch operational hours of this facility
        $hours = HoursOfOperation::where('facility_id', $facilityId)->orderBy('day_of_week', 'ASC')->get();

        $getOperationalHours = $this->getOperationalHours($facilityId, $hours);


        /**
         * iterate over each day of this week
         */

        foreach ($period as $key => $value) {
            $operationalHours = $getOperationalHours['operationalHours'];
            $remainder        = $getOperationalHours['remainder'];
            $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];
            $today            = $value->format('l');

            $partnerFacility = FacilityPartnerAvailability::firstOrNew(
                ['facility_id' => $facilityId, 'date' => $value->format('Y-m-d')]
            );
            $partnerDecoded  = json_decode($partnerFacility->availability, true);

            $facility       =
                FacilityInventory::firstOrNew(['facility_id' => $facilityId, 'date' => $value->format('Y-m-d')]);
            $availabilities = json_decode($facility->availability, true);

            //fetch availability
            $facilityAvailability =
                FacilityAvailability::firstOrNew(['facility_id' => $facilityId, 'date' => $value->format('Y-m-d')]);
            $availabilityDecoded  = json_decode($facilityAvailability->availability, true);

            $resetInventory = [];
            if ($this->is_iterable($availabilities)) {
                foreach ($availabilities as $index => $availability) {
                    $resetInventory[] = -1;
                }
            }

            foreach ($operationalHoursArray[$today] as $operationalHour) {

                //$availabilityDecoded is data from facility_availabilities table and $availabilities is data from facility_inventories table
                $currentInventory =
                    isset($availabilities[$operationalHour]) ? $availabilities[$operationalHour] : 0;
                if ($currentInventory < $spots) {
                    $difference = $spots - $currentInventory;
                    isset($availabilityDecoded[$operationalHour]) ? $availabilityDecoded[$operationalHour] += $difference :
                        $availabilityDecoded[$operationalHour] = $difference;
                } elseif ($spots < $currentInventory) {
                    $difference = $currentInventory - $spots;
                    isset($availabilityDecoded[$operationalHour]) ? $availabilityDecoded[$operationalHour] -= $difference :
                        $availabilityDecoded[$operationalHour] = $difference;
                }

                $partnerDecoded[$operationalHour] = ($spots > 0) ? 1 : 0;
                $availabilities[$operationalHour] = $spots;
            }

            if (!in_array($value->format('Y-m-d'), $SpectialDates)) {

                if ($forAllDay == true) {
                    $facility->availability = json_encode($availabilities, JSON_FORCE_OBJECT);
                    $facility->save();
                    $facilityAvailability->availability = json_encode($availabilityDecoded, JSON_FORCE_OBJECT);
                    $facilityAvailability->save();

                    //update avaliablity when inventory_threshold has value by vikrant
                    $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($facility->availability, true), $availabilityDecoded, $facilityId);
                    if ($newPartnerAvailability) {
                        $partnerFacility->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                        $partnerFacility->save();
                    }
                } elseif (!$facility->exists) {
                    $facility->availability = json_encode($resetInventory, JSON_FORCE_OBJECT);
                    $facility->save();
                    $facilityAvailability->availability = json_encode($resetInventory, JSON_FORCE_OBJECT);
                    $facilityAvailability->save();

                    //update avaliablity when inventory_threshold has value by vikrant
                    $newPartnerAvailability = Facility::calculatePartnerAvailability(json_decode($facility->availability, true), $resetInventory, $facilityId);
                    if ($newPartnerAvailability) {
                        $partnerFacility->availability = json_encode($newPartnerAvailability, JSON_FORCE_OBJECT);
                        $partnerFacility->save();
                    }
                }
            }
        }
        return 'success';
    }

    /**
     * @param $facilityId
     * @param int $spots
     * @param $day
     * @param bool $forAllDay
     * @throws \Exception
     */
    protected function generateInventoryForNinetyDays($facilityId, $spots = 0, $day, $forAllDay = false)
    {
        //generate a range of dates
        $period = $this->generateArrayOfDates(self::NINETY_DAYS, '', '', true);

        $dates = [];

        //fetch operational hours of this facility
        $hours = HoursOfOperation::where('facility_id', $facilityId)->orderBy('day_of_week', 'ASC')->get();

        //fetch operational hours of this facility
        //$operationalHours = $this->getOperationalHours($facilityId, $hours);

        $getOperationalHours             = $this->getOperationalHours($facilityId, $hours);
        $operationalHours                = $getOperationalHours['operationalHours'];
        $remainder                       = $getOperationalHours['remainder'];
        $operationalHoursArray    = $getOperationalHours['operationalHoursArray'];

        $operationalHoursForAvailability = $operationalHours;

        /**
         * iterate over each day of this week
         */
        foreach ($period as $key => $value) {
            if ($value->format('l') == $day) {
                $dates[] = $value->format('Y-m-d');
            }
        }

        foreach ($dates as $date) {
            $operationalHours  = $getOperationalHours['operationalHours'];
            $today             = date('l', strtotime($date));
            $facility          = FacilityInventory::firstOrNew(['facility_id' => $facilityId, 'date' => $date]);
            $availabilities    = json_decode($facility->availability);
            $availability      = FacilityAvailability::firstOrNew(['facility_id' => $facilityId, 'date' => $date]);
            $availabilityArray = json_decode($availability->availability);

            $resetInventory = [];
            if ($this->is_iterable($availabilities)) {
                foreach ($availabilities as $key => $availability) {
                    $resetInventory[] = -1;
                }
            }

            foreach ($operationalHoursArray[$today] as $operationalHour) {
                $resetInventory[$operationalHour] = $spots;
            }

            if ($forAllDay == true) {
                $facility->availability = json_encode($resetInventory, JSON_FORCE_OBJECT);
                $facility->save();
            } elseif (!$facility->exists) {
                $facility->availability = json_encode($resetInventory, JSON_FORCE_OBJECT);
                $facility->save();
            }
        }
    }

    /**
     * @param $spots
     * @param $facilityId
     * @param $request
     * @return string
     */
    public function updateInventory($spots, $facilityId, $request)
    {
        //split date and index to be updated
        $data = explode('_', $request->all()[0]);
        $date = $data[0];
        $slot = $data[1];
        //find this facility

        $facility = FacilityInventory::firstOrNew(['facility_id' => $facilityId, 'date' => $date]);
        if ($facility->exists) {
            $availability          = json_decode($facility->availability);
            $availability->{$slot} = $spots;
        } else {
            $availability        = $this->generateInventoryBlock();
            $availability[$slot] = $spots;
        }
        $facility->availability = json_encode($availability, JSON_FORCE_OBJECT);
        $facility->save();

        return 'success';
    }

    /**
     * @param string $numberOfDays
     * @param string $startDate
     * @param string $endDate
     * @param $startOfWeek
     * @return \DatePeriod
     * @throws \Exception
     */
    public function generateArrayOfDates($numberOfDays = '', $startDate = '', $endDate = '', $startOfWeek = false)
    {
        //set week start to sunday to be uniform in all cases
        Carbon::setWeekStartsAt(Carbon::SUNDAY);
        $carbonStartDate =  Carbon::now();
        if ($startDate != '') {
            $carbonStartDate = Carbon::parse($startDate);
        }

        $startOfWeek = $startOfWeek ? Carbon::now()->startOfWeek()->addDays($numberOfDays ? $numberOfDays : self::DAYS_IN_WEEK) : $carbonStartDate->addDays($numberOfDays ? $numberOfDays : self::DAYS_IN_WEEK);
        // dd($carbonStartDate->format('Y-m-d H:i:s'), $numberOfDays, $startOfWeek->format('Y-m-d H:i:s'));
        return new \DatePeriod(
            new \DateTime($startDate ? $startDate : Carbon::now()->startOfWeek()),
            new \DateInterval('P1D'),
            new \DateTime($endDate ? $startOfWeek : $startOfWeek)
        );
    }

    /**
     * @param $request
     * @return mixed
     */
    public function availabilityMatrix($request)
    {
        $validator = Validator::make($request->all(), ['facility_id' => 'required|numeric']);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }
        if ((isset($request->from_date) && !empty($request->from_date) || (isset($request->to_date) && !empty($request->to_date)))) {
            $availabilities = FacilityAvailability::where('facility_id', $request->facility_id)->whereBetween(
                'date',
                [$request->from_date, $request->to_date]
            )->limit(15)->get();
        } else {
            $availabilities = FacilityAvailability::where('facility_id', $request->facility_id)->whereDate(
                'date',
                '>=',
                Carbon::now()->format('Y-m-d')
            )->limit(15)->get();
        }

        foreach ($availabilities as $availability) {
            //find inventory for this facility for this date
            $inventory = FacilityInventory::where(['facility_id' => $request->facility_id, 'date' => $availability->date])->first();
            if ($inventory) {
                $inventoryAvailability = json_decode($inventory->availability, true);
            }
            //availability table
            $data = [];
            $slots = json_decode($availability->availability, true);
            if ($slots) {
                foreach ($slots as $key => $slot) {
                    $booked     = isset($inventoryAvailability[$key]) ? $inventoryAvailability[$key] - $slot : 0;
                    $data[$key] = isset($inventoryAvailability) ? $slot . '/' . $booked : $slot . "/NA";
                }
            }
            $response[$availability->date] = json_decode(json_encode($data, JSON_FORCE_OBJECT));
        }
        if (empty($response)) {
            return 'Data not available';
        }
        return $response;
    }


    /**
     * @param $facilityId
     * @param int $spots
     * @param bool $forAllDay
     * @param null $days
     * @throws \Exception
     */
    public function generateAvailabilityForWeek($facilityId, $spots = 0, $forAllDay = false, $days = null)
    {
        //generate a range of dates
        $period = $this->generateArrayOfDates($days ? $days : self::DAYS_IN_WEEK);

        /*
         * iterate over each day of this week
         */
        foreach ($period as $key => $value) {
            $facility =
                FacilityAvailability::firstOrNew(['facility_id' => $facilityId, 'date' => $value->format('Y-m-d')]);
            if ($forAllDay == true) {
                $facility->availability = json_encode($this->generateInventoryBlock($spots), JSON_FORCE_OBJECT);
                $facility->save();
            } elseif (!$facility->exists) {
                $facility->availability = json_encode($this->generateInventoryBlock($spots), JSON_FORCE_OBJECT);
                $facility->save();
            }
        }
    }

    /**
     * @param int $spots
     * @return array
     */
    public function generateInventoryBlock($spots = 0)
    {
        $i        = 0;
        $response = [];
        while ($i < self::TWENTY_FOUR_HOURS) {
            $response[] = $spots;
            $i++;
        }
        return $response;
    }

    protected function is_iterable($obj)
    {
        return is_array($obj) || $obj instanceof \Traversable;
    }


    /**
     * @param $facilityId
     * @param array $hours
     * @return array
     */
    public function getOperationalHours($facilityId, $hours = []): array
    {
        if (!$hours) {
            //fetch operational hours of this facility
            $hours = HoursOfOperation::where('facility_id', $facilityId)->orderBy('day_of_week', 'ASC')->get();
        }

        $operationalHours = [];
        $operationalHoursArray = [];

        $week = [
            0 => 'Sunday',
            1 => 'Monday',
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday',
            7 => 'Sunday'
        ];
        //to create empty operationalHoursArray with week days        
        foreach (range(0, 6) as $zeroToSix) {
            $operationalHoursArray[$week[$zeroToSix]] = [];
        }
        /**
         * create an associative array of hours of operation having day name as index
         * in case this facility has no matching row in the hours_of_operation table
         * still create an array with default opening and closing hours
         */

        $remainder = [];
        $data      = [];

        foreach ($hours as $hour) {
            if ($hour->close_time > '23:59:59') {
                $remainder[$week[$hour->day_of_week + 1]] = explode(':', $hour->close_time)[0] - 24;
                $hour->close_time                         = '23:59:59';
                for ($i = 0; $i < $remainder[$week[$hour->day_of_week + 1]]; $i++) {
                    $operationalHoursArray[$week[$hour->day_of_week + 1]][] = (string)$i;
                }
            }

            //if close time is less than 23:59:59 then subtract 1 hour from facility closing time
            if ($hour->close_time < '23:59:59') {
                $hour->close_time = date('H:i:s', strtotime('-1 hour', strtotime($hour->close_time)));
            }

            $operationalHours[$week[$hour->day_of_week]] = [
                'open_time'  => date('G', strtotime($hour->open_time)),
                'close_time' => date('G', strtotime($hour->close_time))
            ];
            for ($i = (int)$operationalHours[$week[$hour->day_of_week]]['open_time']; $i <= $operationalHours[$week[$hour->day_of_week]]['close_time']; $i++) {
                $operationalHoursArray[$week[$hour->day_of_week]][] = (string)$i;
            }
        }

        foreach (range(0, 6) as $zeroToSix) {
            if (!isset($operationalHours[$week[$zeroToSix]]) && !count($hours)) {
                $operationalHours[$week[$zeroToSix]] =
                    ['open_time' => self::DAY_START_HOUR, 'close_time' => self::DAY_END_HOUR];

                for ($i = (int)$operationalHours[$week[$zeroToSix]]['open_time']; $i <= $operationalHours[$week[$zeroToSix]]['close_time']; $i++) {
                    $operationalHoursArray[$week[$zeroToSix]][] = (string)$i;
                }
            } else if (!isset($operationalHours[$week[$zeroToSix]]) && count($hours)) {
                $operationalHours[$week[$zeroToSix]] =
                    ['open_time' => self::DAY_END_HOUR, 'close_time' => self::DAY_START_HOUR];
            }
        }

        if (count($remainder)) {
            foreach ($remainder as $key => $remaining) {
                for ($i = 0; $i < $remaining; $i++) {
                    $data[$key][] = true;
                }
            }
        }
        return ['operationalHours' => $operationalHours, 'remainder' => $data, 'operationalHoursArray' => $operationalHoursArray];
    }
}
