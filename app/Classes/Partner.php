<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use App\Exceptions\DuplicateRecordException;
use App\Exceptions\NotFoundException;
use App\Models\Facility;
use App\Models\User;
use App\Models\FacilityAvailability;
use App\Models\HoursOfOperation;
use App\Models\PartnerReservation;
use App\Models\PartnerToken;
use App\Models\Reservation;
use Artisan;
use Carbon\Carbon;
use Faker\Provider\Uuid;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Validator;

class Partner
{
    const TWENTY_FOUR_HOURS             = 23;
    const REALTIME_WINDOW               = 2;
    const DEFAULT_TIME                  = '0000-00-00 00:00:00';
    const ONE_EXTRA_DAY                 = 1;
    const DAY_START_HOUR                = 0;
    const DEFAULT_VALUE                 = 0;
    const DEFAULT_FACILITY_CLOSING_TIME = '23:59:59';
    const RESERVATION_TYPE              = 'PARTNER';

    const CONST_CLOSE_MIN_CHECK = 30;
    
    
    /**
     * @param $request
     * @return array
     * @throws ApiGenericException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function facilityPricing($request)
    {
        $validator = Validator::make(
            $request->all(), ['date_time_in'  => 'required|date|after:now',
                              'date_time_out' => 'required|date|after:' . $request->date_time_in,
                              'garage_codes'  => 'required']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        $inventory = new Inventory();
        
        //take only numeric value from the location ids
        //$locationIds = array_values(array_unique(array_filter(explode(',', $request->location_ids), 'ctype_digit')));
        $garageCodes = array_values(array_unique(array_filter(explode(',', $request->garage_codes), 'ctype_digit')));
        
        $bearerToken = $request->bearerToken();
        $partner     = PartnerToken::where('token', $bearerToken)->first();
        if (!$partner) {
            return response()->json(['error' => 'Unidentified token'], 404);
        }

        $user = User::where('id', $partner->partner_id)->first();
        if($user->user_type == '3'){
            $owner_id = $user->id;
        }elseif($user->user_type == '4'){
            $owner_id = $user->created_by;
        }else{
            $owner_id = $user->id;
        }
        
        $locationIds = Facility::whereIN('garage_code', $garageCodes)->where('owner_id', $owner_id)->get();

        if (!$locationIds) {
            throw new ApiGenericException('Invalid Garage Code', 422);
        }
        
        foreach ($locationIds as $key => $value) {
            if (in_array($value->garage_code, $garageCodes)) 
            {
                unset($garageCodes[array_search($value->garage_code,$garageCodes)]);
            }
        }
        if(count($garageCodes) > 0){
            throw new ApiGenericException('Invalid Garage Code are : '. implode(' ', $garageCodes), 422);
        }

        //$locationIds = Facility::whereIN('garage_code', $garageCodes)->get();
        
        $response = [];

        //iterate over each facility id provided in the request
        foreach ($locationIds as $locationId) {
            $facility             = Facility::find($locationId->id);
            $dayOfWeek            = date('w', strtotime($request->date_time_in));
            $hours                =
                HoursOfOperation::where(['facility_id' => $locationId->id, 'day_of_week' => $dayOfWeek])->first();
            $facilityStartingHour = 0;
            $facilityEndingHour   = self::TWENTY_FOUR_HOURS;
            if ($hours) {
                $facilityStartingHour = date('G', strtotime($hours->open_time));
                $facilityEndingHour   = date('G', strtotime($hours->close_time));
            }
            if ($facility) {

                /*$couponThresholds = json_decode($facility->coupon_threshold);
                $thresholds       = [];
                if ($couponThresholds) {
                    foreach ($couponThresholds as $couponThreshold) {
                        $thresholds[$couponThreshold->threshold] =
                            ['uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
                    }
                }*/


                /*
                 * fetch realtime window of this facility even though
                 * the current setup expects it to be 2 hours default
                 * this section will make it future proof
                 */
                $realtimeWindow = $facility->realtime_window;
                $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;
                $timeDifference = date_diff(date_create($request->date_time_in), Carbon::now());

                $isAvailable = true;
                if ($timeDifference->h <= $realtimeWindow) {
                    $isAvailable = $this->fetchAvailabilityFromTicketech($facility);
                }

                if ($isAvailable === true) {

                    //check how many slots does entry and exit time occupies
                    //$difference = date_diff(date_create($request->date_time_in), date_create($request->date_time_out));
                    $difference = date_diff(
                        date_create(date('Y-m-d', strtotime($request->date_time_in))),
                        date_create(date('Y-m-d', strtotime(($request->date_time_out)))));

                    if ($difference->d > 0) {
                        $dates         = $inventory->generateArrayOfDates(
                            ($difference->d + self::ONE_EXTRA_DAY),
                            date('Y-m-d H:i:s', strtotime($request->date_time_in)));
                        $dayDifference = $difference->d;
                        foreach ($dates as $key => $date) {
                            $facility = FacilityAvailability::where(
                                ['facility_id' => $request->location_id, 'date' => $date->format('Y-m-d')])->first();
                            if ($facility) {
                                $inventory = json_decode($facility->availability);

                                if ($key == 0) {
                                    /**
                                     * because this is the first day in the dates provided
                                     * we should remove 1 from each time_slot starting
                                     * from the hour provided in the api call
                                     */
                                    $i = date('G', strtotime($request->date_time_in));
                                    while ($i <= self::TWENTY_FOUR_HOURS) {
                                        if ($inventory->{$i} < 1) {
                                            $isAvailable = false;
                                        }
                                        $i++;
                                    }
                                } elseif ($key == $dayDifference) {
                                    $i       = date('G', strtotime($request->date_time_out));
                                    $minutes = date('i', strtotime($request->date_time_out));
                                    if ($minutes >= 30) {
                                        $i++;
                                    }
                                    /**
                                     * because this is the last day in the dates provided
                                     * we should remove 1 from each time_slot starting
                                     * till the hour provided in the api call
                                     */
                                    $j = 0;
                                    while ($j < $i) {
                                        if ($inventory->{$j} < 1) {
                                            $isAvailable = false;
                                        }
                                        $j++;
                                    }
                                } else {
                                    /**
                                     * because this could be any day except first and last in
                                     * the dates provided we should remove 1 from whole day
                                     */
                                    $k = 0;
                                    while ($k <= self::TWENTY_FOUR_HOURS) {
                                        if (isset($inventory->{$k})) {
                                            if ($inventory->{$k} < 1) {
                                                $isAvailable = false;
                                            }
                                        }
                                        $k++;
                                    }
                                }
                            }
                        }

                        if ($isAvailable == false) {
                            $response['availability'][$locationId->garage_code] = 'NA';
                        }
                    } else {
                        $startingHour = date('G', strtotime($request->date_time_in));
                        $endingHour   = date('G', strtotime($request->date_time_out));
                        $facility     = FacilityAvailability::where(
                            ['facility_id' => $request->location_id,
                             'date'        => date('Y-m-d', strtotime($request->date_time_in))])->first();
                        if ($facility) {
                            $availability = json_decode($facility->availability, true);
                            while ($startingHour <= $endingHour) {
                                if ($availability[$startingHour] < 1) {
                                    $isAvailable = false;
                                    break;
                                }
                                $startingHour++;
                            }
                        }

                        if ($isAvailable == false) {
                            $response['availability'][$locationId->id] = 'NA';
                        }
                    }

                    if ($isAvailable == true) {
                        $dateIn = explode(' ', $request->date_time_in)[0];
                        //check if we have availability in this facility
                        $facilityAvailability =
                            FacilityAvailability::where('facility_id', $locationId->id)->where('date', $dateIn)->first();
                        if ($facilityAvailability) {
                            $availabilities = json_decode($facilityAvailability->availability, true);
                            //get the hours index from date time in
                            $entranceHour = date('G', strtotime($request->date_time_in));
                            if ($entranceHour >= $facilityStartingHour && $entranceHour <= $facilityEndingHour) {
                                if (isset($availabilities[$entranceHour]) && $availabilities[$entranceHour] > 0) {
                                    //find the rates of this facility using the current logic
                                    $facility     = Facility::find($locationId->id);
                                    $lengthOfstay = date_diff(
                                        date_create($request->date_time_out), date_create($request->date_time_in));
                                    $hourDiff     = round(
                                        (strtotime($request->date_time_out) - strtotime($request->date_time_in)) / 3600,
                                        1);
                                    $results      = $facility->rateForReservation(
                                        $request->date_time_in, $hourDiff, $request->use_bonus);
                                    //check if the current availability falls under any threshold
                                    /*foreach ($thresholds as $key => $threshold) {
                                        if ($availabilities[$entranceHour] < $key) {
                                            if ($threshold['uptick'] > 0) {
                                                if ($threshold['uptick_type'] == 'price') {
                                                    $results['price'] += $threshold['uptick'];
                                                } else {
                                                    $results['price'] += ($results['price'] / 100) * $threshold['uptick'];
                                                }
                                            }
                                        }
                                    }*/
                               
     $response['availability'][$locationId->garage_code] =
                                        $results['price'] < 0 ? 0 : $results['price'];
                                } else {
                                    $response['availability'][$locationId->garage_code] = 'NA';
                                }
                            } else {
                                $response['availability'][$locationId->garage_code] = 'NA';
                            }
                        } else {
                            $response['availability'][$locationId->garage_code] = 'NA';
                        }
                    }
                } else {
                    $response['availability'][$locationId->garage_code] = 'NA';
                }
            }
        }
        return $response;
    }


    /**
     * @param $request
     * @return array|\Illuminate\Http\JsonResponse|string
     * @throws \Exception
     */
    public function reservationConfirmation($request)
    {
        $validator = Validator::make(
            $request->all(),
            ['date_time_in' => 'required|date', 'date_time_out' => 'required|date|after:' . date(
                    'Y-m-d H:i:s', strtotime('+29 minutes', strtotime($request->date_time_in))),
             'garage_code' => 'required|exists:facilities,garage_code', 'provider' => 'required',
             'reservation_number' => 'required|alpha_num', 'amount' => "required|regex:/^\d*(\.\d{1,2})?$/"]);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        $bearerToken = $request->bearerToken();
        $partner     = PartnerToken::where('token', $bearerToken)->first();

        $user = User::where('id', $partner->partner_id)->first();
        $user_owner_id = $user->id;
        if($user->user_type == '3'){
            $owner_id = $user->id;
        }elseif($user->user_type == '4'){
            $owner_id = $user->created_by;
        }else{
            $owner_id = $user->id;
        }
        
        $facilityID = Facility::where('garage_code', $request->garage_code)->where('owner_id', $owner_id)->first();
        
        //$facility = Facility::where('id', $facilityID->id)->first();
        if (!$facilityID) {
            throw new ApiGenericException('Invalid Garage Code', 422);
        }

        if($facilityID->active == 0){
            throw new ApiGenericException('Inactive Garage Code', 400);   
        }

        $inventoryRepository = new Inventory();

        if (!$partner) {
            return response()->json(['error' => 'Unidentified token'], 404);
        }

        //check if the reservation number provided by the vendor does not already exists
        $reservation        = Reservation::where('ticketech_code', $request->reservation_number)->first();
        $partnerReservation = PartnerReservation::where('reservation_number', $request->reservation_number)->first();

        if ($reservation || $partnerReservation) {
            throw new DuplicateRecordException('Duplicate reservation number');
        }
        
        $providedDateIn = $request->date_time_in;
        $providedDateOut = $request->date_time_out;
       
        //removing t (timeone from date time)
        $request->date_time_in = str_replace("T", " ", $request->date_time_in);
        $request->date_time_out = str_replace("T", " ", $request->date_time_out);
        $splitTimeStamp = explode(" ",$request->date_time_in);
        $splitTimeStampOut = explode(" ",$request->date_time_out);
        $updatedInTimeStamp = '00::00::00';
        $dateStampIn = isset($splitTimeStamp[0])?$splitTimeStamp[0]:$request->date_time_in;
        $dateStampOut = isset($splitTimeStampOut[0])?$splitTimeStampOut[0]:$request->date_time_out;
        $timeStampTocheck = isset($splitTimeStamp[1])?$splitTimeStamp[1]:date('H:i:s',strtotime($request->date_time_in));
        $updatedOutTimeStamp = isset($splitTimeStampOut[1])?$splitTimeStampOut[1]:date('H:i:s',strtotime($request->date_time_out));
       
        $operatorType = 0;
        if((strstr($timeStampTocheck, "+")) && ((strstr($timeStampTocheck, "+"))!=''))
        {
            $operatorType = 1;
        }
        if((strstr($timeStampTocheck, "-")) && ((strstr($timeStampTocheck, "-"))!=''))
        {
            $operatorType = 2;
        }
        
        if($operatorType > 0)
        {
           if($operatorType == 1)
           {
                $updatedInTimeStamp = substr($timeStampTocheck, 0, strpos($timeStampTocheck, "+"));
                $updatedOutTimeStamp = substr($updatedOutTimeStamp, 0, strpos($updatedOutTimeStamp, "+"));
        
           }else{
               $updatedInTimeStamp = substr($timeStampTocheck, 0, strpos($timeStampTocheck, "-"));
               $updatedOutTimeStamp = substr($updatedOutTimeStamp, 0, strpos($updatedOutTimeStamp, "-"));
        
           }
          $request->date_time_in = date('Y-m-d H:i:s', strtotime("$dateStampIn $updatedInTimeStamp"));
          $request->date_time_out = date('Y-m-d H:i:s',strtotime("$dateStampOut $updatedOutTimeStamp"));
                 
           //update in out date time
        }
        
        $globalHours  = HoursOfOperation::where(['facility_id' => $facilityID->id])->get();
        if(count($globalHours) > 0){
            $dayIn  = date('w', strtotime($request->date_time_in));
            $hours  = HoursOfOperation::where(['facility_id' => $facilityID->id, 'day_of_week' => $dayIn])->first();
            $dayOut = date('w', strtotime($request->date_time_out));

            if ($hours) {
                if (date('G', strtotime($hours->open_time)) > date('G', strtotime($request->date_time_in))) {
                    throw new ApiGenericException('Facility is closed at the time and duration specified', 400);
                }
            }else{
                throw new ApiGenericException('Facility is closed at the time and duration specified', 400);
            }
        

            $hoursEnd =
                HoursOfOperation::where(['facility_id' => $facilityID->id, 'day_of_week' => $dayOut])->orderBy('id', 'desc')->first();

            if ($hoursEnd) {
                if(date('G', strtotime($hoursEnd->close_time)) < date('G', strtotime($request->date_time_out))){
                    throw new ApiGenericException('Facility is closed at the time and duration specified', 400);
                }
            }else{
                    throw new ApiGenericException('Facility is closed at the time and duration specified', 400);
            }

            $arrival = Carbon::parse($request->date_time_in);
            $exit = Carbon::parse($request->date_time_out);

            if(($this->isTimeOpenBetweenOpenClose($arrival, $facilityID) && $this->isTimeCloseBetweenOpenClose($exit, $arrival, $facilityID)) == false){
                throw new ApiGenericException('Facility is closed at the time and duration specified', 400);
            }
        }

        //$data                   = $request->all();
        $data['amount']     = $request->amount;
        $data['provider']     = $request->provider;
        $data['reservation_number']     = $request->reservation_number;
        $data['sent_date_time_in']     = $providedDateIn;
        $data['sent_date_time_out']     = $providedDateOut;
        $data['date_time_in']     = $request->date_time_in;
        $data['date_time_out']     = $request->date_time_out;
        $data['partner_id']     = $partner->partner_id;
        $data['reservation_by']     = $user->id;
        $data['location_id']     = $facilityID->id;
        
        $partnerReservationData = PartnerReservation::create($data);

        //check how many slots does entry and exit time occupies
        //$difference = date_diff(date_create($request->date_time_in), date_create($request->date_time_out));
        $difference = date_diff(
            date_create(date('Y-m-d', strtotime($request->date_time_in))),
            date_create(date('Y-m-d', strtotime(($request->date_time_out)))));
        //check if someone is parking for more than a day
        if ($difference->d > 0) {
            /*$dates         = $inventoryRepository->generateArrayOfDates(
                '', date($request->date_time_in), date($request->date_time_out));*/
            $dates   = $inventoryRepository->generateArrayOfDates(
                ($difference->d + self::ONE_EXTRA_DAY), date('Y-m-d H:i:s', strtotime($request->date_time_in)));
            $dayDifference = $difference->d;
            foreach ($dates as $key => $date) {

                $dayIn        = date('w', strtotime($date->format('Y-m-d')));
                $hours        =
                    HoursOfOperation::where(['facility_id' => $facilityID->id, 'day_of_week' => $dayIn])->first();
                $startingHour = self::DAY_START_HOUR;
                $endingHour   = self::TWENTY_FOUR_HOURS;

                if ($hours) {
                    $startingHour = date('G', strtotime($hours->open_time));
                    //TODO carry forward remaining hour in case a facility is working for more than 24 hours
                    if ($hours->close_time <= self::DEFAULT_FACILITY_CLOSING_TIME) {
                        $endingHour = date('G', strtotime($hours->close_time));
                    }
                }

                $facility = FacilityAvailability::where(
                    ['facility_id' => $facilityID->id, 'date' => $date->format('Y-m-d')])->first();
                if ($facility) {
                    $inventory = json_decode($facility->availability);
                    if (isset($inventory)) {
                        if ($key == 0) {
                            /**
                             * because this is the first day in the dates provided
                             * we should remove 1 from each time_slot starting
                             * from the hour provided in the api call
                             */
                            $i = date('G', strtotime($request->date_time_in));
                            if ($startingHour > $i) {
                                $i = $startingHour;
                            }

                            $loopEnd = ($endingHour < self::TWENTY_FOUR_HOURS) ? $endingHour : self::TWENTY_FOUR_HOURS;
                            while ($i <= $loopEnd) {
								if(isset($inventory->{$i}))
								{
                                    $inventory->{$i} = $inventory->{$i} - 1;
								}
                                    $i++;
									
                            }
                        } elseif ($key == $dayDifference) {
                            $i       = date('G', strtotime($request->date_time_out));
                            $minutes = date('i', strtotime($request->date_time_out));
                            if ($minutes >= 30) {
                                $i++;
                            }

                            /**
                             * because this is the last day in the dates provided
                             * we should remove 1 from each time_slot starting
                             * till the hour provided in the api call
                             */

                            //$loopEnd = ($endingHour < self::TWENTY_FOUR_HOURS) ? $endingHour : self::TWENTY_FOUR_HOURS;
                            $j = 0;
                            while ($j < $i) {
                                if (isset($inventory->{$j})) {
                                    $inventory->{$j} = $inventory->{$j} - 1;
                                }
                                $j++;
                            }
                        } else {
                            /**
                             * because this could be any day except first and last in
                             * the dates provided we should remove 1 from whole day
                             */
                            $k = 0;
                            while ($k <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$k})) {
                                    $inventory->{$k} = $inventory->{$k} - 1;
                                }
                                $k++;
                            }
                        }
                    }
                    $facility->availability = json_encode($inventory, JSON_FORCE_OBJECT);
                    $facility->save();
                }
            }
        } else {
            $startingHour = date('G', strtotime($request->date_time_in));
            $endingHour   = date('G', strtotime($request->date_time_out));
            $minutes      = date('i', strtotime($request->date_time_out));

            if ($minutes >= 30) {
                $endingHour++;
            }

            $facility = FacilityAvailability::where(
                ['facility_id' => $facilityID->id, 'date' => date('Y-m-d', strtotime($request->date_time_in))])
                                            ->first();
            if ($facility) {
                $availability = json_decode($facility->availability);
                if (isset($availability)) {
                    while ($startingHour < $endingHour) {
					     if(isset($availability->{$startingHour}))
							 {
								$availability->{$startingHour} = $availability->{$startingHour} - 1;
							 }
							$startingHour++;
                    }
                }
                $facility->availability = json_encode($availability, JSON_FORCE_OBJECT);
                $facility->save();
            }
        }

        //calling command to update partner inventory 
        $updateJobParams = ['reservationId' => $partnerReservationData->id, 'type' => self::RESERVATION_TYPE];
        Artisan::queue('cron:update-inventory', $updateJobParams);

        return 'success';
    }


    protected function isTimeOpenBetweenOpenClose(Carbon $time, $facility)
    {
        foreach ($facility->hoursForDay($time->dayOfWeek) as $hours) {
            $open = $time->copy();
            $close = $time->copy();

            if ($time->dayOfWeek != $hours->day_of_week) {
                $open->subDay();
                $close->subDay();
            }

            $open->hour($hours->open_hour)->minute($hours->open_minutes);
            $close->hour($hours->close_hour)->minute($hours->close_minutes);
            $close->subMinutes(self::CONST_CLOSE_MIN_CHECK);

            if ($time->between($open, $close)) {
                return true;
            }
        }

        return false;
    }

    protected function isTimeCloseBetweenOpenClose(Carbon $time, Carbon $arrivalTime, $facility)
    {
        foreach ($facility->hoursForDay($time->dayOfWeek) as $hours) {
            $open = $time->copy();
            $close = $time->copy();

            if ($time->dayOfWeek != $hours->day_of_week) {
                $open->subDay();
                $close->subDay();
            }

            $open->hour($hours->open_hour)->minute($hours->open_minutes);
            $close->hour($hours->close_hour)->minute($hours->close_minutes);

            
            if ($time->between($open, $close)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param $request
     * @return array|\Illuminate\Http\JsonResponse
     * @throws ApiGenericException
     * @throws NotFoundException
     *
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function reservationCancellation($request)
    {
        $validator = Validator::make(
            $request->all(),
            ['garage_code' => 'required', 'provider' => 'required', 'reservation_number' => 'required']);
        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }

        $inventoryRepository = new Inventory();

        $bearerToken = $request->bearerToken();
        $partner     = PartnerToken::where('token', $bearerToken)->first();
        if (!$partner) {
            return response()->json(['error' => 'Unidentified token'], 404);
        }

        $user = User::where('id', $partner->partner_id)->first();
        if($user->user_type == '3'){
            $owner_id = $user->id;
        }elseif($user->user_type == '4'){
            $owner_id = $user->created_by;
        }else{
            $owner_id = $user->id;
        }
        $facilityID = Facility::where('garage_code', $request->garage_code)->where('owner_id', $owner_id)->first();

        if (!$facilityID) {
            throw new ApiGenericException('Invalid Garage Code', 422);
        }

        $data               = $request->all();
        $data['partner_id'] = $partner->partner_id;
        //find the facility by reservation number because that will be a unique string
        $partnerReservation = PartnerReservation::where(
            ['reservation_number' => $request->reservation_number, 'provider' => $request->provider,
             'location_id'        => $facilityID->id])->first();

        if ($partnerReservation && $partnerReservation->cancelled_at == '') {
            //check how many slots does entry and exit time occupies
            $difference = date_diff(
                     date_create(date('Y-m-d', strtotime($partnerReservation->date_time_in))),
                    date_create(date('Y-m-d', strtotime(($partnerReservation->date_time_out)))));
               
             
            //check if this reservation is for more than a day
            if ($difference->d > 0) {
                
                $dates  = $inventoryRepository->generateArrayOfDates(
                           ($difference->d + self::ONE_EXTRA_DAY), date('Y-m-d H:i:s', strtotime($partnerReservation->date_time_in)));
                  
                $dayDifference = $difference->d;
                foreach ($dates as $key => $date) {

                    $startingHour = 0;
                    $endingHour   = self::TWENTY_FOUR_HOURS;
                    $dayOfWeek    = date('w', strtotime($date->format('Y-m-d')));
                    $hours        =
                        HoursOfOperation::where(['facility_id' => $facilityID->id, 'day_of_week' => $dayOfWeek])
                                        ->first();
                    if ($hours) {
                        $startingHour = date('G', strtotime($hours->open_time));
                        //TODO carry forward remaining hour in case a facility is working for more than 24 hours
                        if ($hours->close_time <= self::DEFAULT_FACILITY_CLOSING_TIME) {
                            $endingHour = date('G', strtotime($hours->close_time));
                        }
                    }

                    $facility = FacilityAvailability::where(
                        ['facility_id' => $partnerReservation->location_id, 'date' => $date->format('Y-m-d')])->first();
                    if ($facility) {
                        $inventory = json_decode($facility->availability);
                        if (isset($inventory)) {
                            if (isset($inventory)) {
                                if ($key == 0) {
                                    /**
                                     * because this is the first day in the dates provided
                                     * we should remove 1 from each time_slot starting
                                     * from the hour provided in the api call
                                     */
                                    $i = date('G', strtotime($partnerReservation->date_time_in));
                                    if ($startingHour > $i) {
                                        $i = $startingHour;
                                    }

                                    $loopEnd =
                                        ($endingHour < self::TWENTY_FOUR_HOURS) ? $endingHour : self::TWENTY_FOUR_HOURS;

                                    while ($i <= $loopEnd) {
                                        if (isset($inventory->{$i})) {
                                            $inventory->{$i} = $inventory->{$i} + 1;
                                        }
                                        $i++;
                                    }
                                } elseif ($key == $dayDifference) {
                                    $i       = date('G', strtotime($partnerReservation->date_time_out));
                                    $minutes = date('i', strtotime($partnerReservation->date_time_out));
                                    if ($minutes >= 30) {
                                        $i++;
                                    }

                                    /**
                                     * because this is the last day in the dates provided
                                     * we should remove 1 from each time_slot starting
                                     * till the hour provided in the api call
                                     */

                                    $j = 0;
                                    while ($j < $i) {
                                        if (isset($inventory->{$j})) {
                                            $inventory->{$j} = $inventory->{$j} + 1;
                                        }
                                        $j++;
                                    }
                                } else {
                                    /**
                                     * because this could be any day except first and last in
                                     * the dates provided we should remove 1 from whole day
                                     */
                                    $k = 0;
                                    while ($k <= self::TWENTY_FOUR_HOURS) {
                                        if (isset($inventory->{$k})) {
                                            $inventory->{$k} = $inventory->{$k} + 1;
                                        }
                                        $k++;
                                    }
                                    /*$k = 0;
                                    while ($startingHour <= $endingHour) {
                                        $inventory->{$startingHour} = $inventory->{$startingHour} + 1;
                                        $startingHour++;
                                    }*/
                                }
                            }
                        }
                        $facility->availability = json_encode($inventory, JSON_FORCE_OBJECT);
                        $facility->save();
                    }
                }
            } else {
                $startingHour = date('G', strtotime($partnerReservation->date_time_in));
                $endingHour   = date('G', strtotime($partnerReservation->date_time_out));
                $minutes      = date('i', strtotime($partnerReservation->date_time_out));
                if ($minutes >= 30) {
                    $endingHour++;
                }

                $facility = FacilityAvailability::where(
                    ['facility_id' => $partnerReservation->location_id,
                     'date'        => date('Y-m-d', strtotime($partnerReservation->date_time_in))])->first();
                if ($facility) {
                    $availability = json_decode($facility->availability);
                    if (isset($availability)) {
                        while ($startingHour < $endingHour) {
							if(isset($availability->{$startingHour}))
							{
									$availability->{$startingHour} = $availability->{$startingHour} + 1;
							}
                            $startingHour++;
                        }
                        $facility->availability = json_encode($availability, JSON_FORCE_OBJECT);
                        $facility->save();
                    }
                }
            }
            $partnerReservation->cancelled_at = Carbon::now();
            $partnerReservation->save();

            //calling command to update partner inventory
            $updateJobParams = ['reservationId' => $partnerReservation->id, 'type' => self::RESERVATION_TYPE];
            Artisan::queue('cron:update-inventory', $updateJobParams);

            return ['message' => 'success'];

        } elseif ($partnerReservation && $partnerReservation->cancelled_at != '') {
            return ['message' => 'Reservation already been cancelled'];
        } else {
            throw new NotFoundException('Invalid Reservation Detail');
        }
    }

    /**
     * @param $request
     * @return array
     * @throws ApiGenericException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function iconFacilityPricing($request)
    {

        $validator = Validator::make(
            $request->all(), ['date_time_in' => 'required|date|after:now', 'length' => 'required|numeric',
                              'location_ids' => 'required|regex:/[0-9,]/']);

        if ($validator->fails()) {
            foreach ($validator->errors()->getMessages() as $key => $error) {
                $errors = $error[0];
            }

            throw new ApiGenericException($errors, 422);
        }
        //add date_time_out in request

        $date_time_out = Carbon::parse($request->date_time_in)->addHours($request->length);

        $request->request->add(['date_time_out' => $date_time_out]);

        $locationIds = explode(',', $request->location_ids);
        //take only numeric value from the location ids
        $locationIds = array_values(array_unique(array_filter($locationIds, 'ctype_digit')));

        $inventory = new Inventory();
        $response  = [];

        $thresholdAvailability = self::DEFAULT_VALUE;

        //iterate over each facility id provided in the request
        foreach ($locationIds as $locationId) {
            $facility = Facility::find($locationId);

            $dayOfWeek            = date('w', strtotime($request->date_time_in));
            $hours                =
                HoursOfOperation::where(['facility_id' => $locationId, 'day_of_week' => $dayOfWeek])->first();
            $facilityStartingHour = 0;
            $facilityEndingHour   = self::TWENTY_FOUR_HOURS;
            if ($hours) {
                $facilityStartingHour = date('G', strtotime($hours->open_time));
                $facilityEndingHour   = date('G', strtotime($hours->close_time));
            }

            if ($facility) {
                $couponThresholds = json_decode($facility->coupon_threshold);
                $thresholds       = array();
                if ($couponThresholds) {
                    $thresholdCounter = self::DEFAULT_VALUE;
                    foreach ($couponThresholds as $key => $couponThreshold) {
                        if ($couponThreshold->uptick_type !== 'deleted') {
                            $thresholds[$thresholdCounter] =
                                ['threshold'   => $couponThreshold->threshold, 'uptick' => $couponThreshold->uptick,
                                 'uptick_type' => $couponThreshold->uptick_type];
                            $thresholdCounter++;
                        }
                    }
                }


                /*
                 * fetch realtime window of this facility even though
                 * the current setup expects it to be 2 hours default
                 * this section will make it future proof
                 */
                $realtimeWindow = $facility->realtime_window;
                $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;
                $timeDifference = date_diff(date_create($request->date_time_in), Carbon::now());

                $isAvailable = true;

                if ($timeDifference->h <= $realtimeWindow) {
                    $isAvailable = $this->fetchAvailabilityFromTicketech($facility);
                }

                if ($isAvailable === true) {

                    //check how many slots does entry and exit time occupies
                    //$difference = date_diff(date_create($request->date_time_in), date_create($request->date_time_out));
                    $difference = date_diff(
                        date_create(date('Y-m-d', strtotime($request->date_time_in))),
                        date_create(date('Y-m-d', strtotime(($request->date_time_out)))));

                    if ($difference->d > 0) {
                        $dates         = $inventory->generateArrayOfDates(
                            ($difference->d + self::ONE_EXTRA_DAY),
                            date('Y-m-d H:i:s', strtotime($request->date_time_in)));
                        $dayDifference = $difference->d;
                        foreach ($dates as $key => $date) {
                            $facility = FacilityAvailability::where(
                                ['facility_id' => $request->location_id, 'date' => $date->format('Y-m-d')])->first();
                            if ($facility) {
                                $inventory = json_decode($facility->availability);

                                if ($key == 0) {
                                    /**
                                     * because this is the first day in the dates provided
                                     * we should remove 1 from each time_slot starting
                                     * from the hour provided in the api call
                                     */
                                    $i = date('G', strtotime($request->date_time_in));
                                    while ($i <= self::TWENTY_FOUR_HOURS) {
                                        if (isset($inventory->{$i})) {
                                            if ($inventory->{$i} < 1) {
                                                $isAvailable = false;
                                            }
                                            if ($thresholdAvailability > 0) {
                                                if ($thresholdAvailability > $inventory->{$i}) {
                                                    $thresholdAvailability = $inventory->{$i};
                                                }

                                            } else {
                                                $thresholdAvailability = $inventory->{$i};
                                            }
                                        }
                                        $i++;
                                    }
                                } elseif ($key == $dayDifference) {
                                    $i       = date('G', strtotime($request->date_time_out));
                                    $minutes = date('i', strtotime($request->date_time_out));
                                    if ($minutes >= 30) {
                                        $i++;
                                    }
                                    /**
                                     * because this is the last day in the dates provided
                                     * we should remove 1 from each time_slot starting
                                     * till the hour provided in the api call
                                     */
                                    $j = 0;
                                    while ($j < $i) {
                                        if (isset($inventory->{$j})) {
                                            if ($inventory->{$j} < 1) {
                                                $isAvailable = false;
                                            }
                                            if ($thresholdAvailability > 0) {
                                                if ($thresholdAvailability > $inventory->{$j}) {
                                                    $thresholdAvailability = $inventory->{$j};
                                                }

                                            } else {
                                                $thresholdAvailability = $inventory->{$j};
                                            }
                                        }
                                        $j++;
                                    }
                                } else {
                                    /**
                                     * because this could be any day except first and last in
                                     * the dates provided we should remove 1 from whole day
                                     */
                                    $k = 0;
                                    while ($k <= self::TWENTY_FOUR_HOURS) {
                                        if (isset($inventory->{$k})) {
                                            if ($inventory->{$k} < 1) {
                                                $isAvailable = false;
                                            }
                                            if ($thresholdAvailability > 0) {
                                                if ($thresholdAvailability > $inventory->{$k}) {
                                                    $thresholdAvailability = $inventory->{$k};
                                                }

                                            } else {
                                                $thresholdAvailability = $inventory->{$k};
                                            }
                                        }
                                        $k++;
                                    }
                                }
                            }
                        }

                        if ($isAvailable == false) {
                            $response['availability'][$locationId] = 'NA';
                        }
                    } else {
                        $startingHour = date('G', strtotime($request->date_time_in));
                        $endingHour   = date('G', strtotime($request->date_time_out));
                        $facility     = FacilityAvailability::where(
                            ['facility_id' => $request->location_id,
                             'date'        => date('Y-m-d', strtotime($request->date_time_in))])->first();
                        if ($facility) {
                            $availability = json_decode($facility->availability, true);
                            while ($startingHour <= $endingHour) {
                                if (isset($availability[$startingHour])) {
                                    if ($availability[$startingHour] < 1) {
                                        $isAvailable = false;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $availability[$startingHour]) {
                                            $thresholdAvailability = $availability[$startingHour];
                                        }

                                    } else {
                                        $thresholdAvailability = $availability[$startingHour];
                                    }
                                }
                                $startingHour++;
                            }
                        }
                        if ($isAvailable == false) {
                            $response['availability'][$locationId] = 'NA';
                        }
                    }
                    if ($isAvailable == true) {

                        $dateIn = explode(' ', $request->date_time_in)[0];
                        //check if we have availability in this facility
                        $facilityAvailability =
                            FacilityAvailability::where('facility_id', $locationId)->where('date', $dateIn)->first();
                        if ($facilityAvailability) {
                            $availabilities = json_decode($facilityAvailability->availability, true);
                            //get the hours index from date time in
                            $entranceHour = date('G', strtotime($request->date_time_in));
                            if (isset($availabilities[$entranceHour]) && $availabilities[$entranceHour] > 0) {
                                //find the rates of this facility using the current logic
                                $facility = Facility::find($locationId);

                                $thresholds = $this->fun_array_sort($thresholds, 'threshold');

                                $results = $facility->rateForReservation(
                                    $request->date_time_in, $request->length, $request->use_bonus);
                                //check if the current availability falls under any threshold
                                foreach ($thresholds as $key => $threshold) {
                                    if ($thresholdAvailability <= $threshold['threshold']) {
                                        if ($threshold['uptick'] > self::DEFAULT_VALUE && $thresholdAvailability > self::DEFAULT_VALUE) {
                                            if ($threshold['uptick_type'] == 'price') {
                                                $results['price'] += $threshold['uptick'];
                                                break;
                                            } else if ($threshold['uptick_type'] == 'percentage') {
                                                $results['price'] += ($results['price'] / 100) * $threshold['uptick'];
                                                break;
                                            }
                                        }
                                    }
                                }
                                $response['availability'][$locationId] =
                                    ['price'        => $results['price'] < 0 ? 0 : (double)$results['price'],
                                     'availability' => $availabilities[$entranceHour]];
                            } else {
                                $response['availability'][$locationId] = 'NA';
                            }
                        } else {
                            $response['availability'][$locationId] = 'NA';
                        }
                    }
                } else {
                    $response['availability'][$locationId] = 'NA';
                }
            }
        }
        return $response;
    }


    /**
     * @param $facility
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function fetchAvailabilityFromTicketech($facility)
    {
         return true;
    }

    public function fun_array_sort($array, $on, $order = SORT_ASC)
    {

        $new_array      = array();
        $sortable_array = array();

        if (count($array) > 0) {
            foreach ($array as $k => $v) {
                if (is_array($v)) {
                    foreach ($v as $k2 => $v2) {
                        if ($k2 == $on) {
                            $sortable_array[$k] = $v2;
                        }
                    }
                } else {
                    $sortable_array[$k] = $v;
                }
            }

            switch ($order) {
                case SORT_ASC:
                    asort($sortable_array);
                    break;
                case SORT_DESC:
                    arsort($sortable_array);
                    break;
            }

            foreach ($sortable_array as $k => $v) {
                $new_array[$k] = $array[$k];
            }
        }

        return $new_array;
    }
}
