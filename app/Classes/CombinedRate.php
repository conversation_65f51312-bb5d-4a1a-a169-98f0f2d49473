<?php

namespace App\Classes;

use Illuminate\Support\Collection;

use Exception;
use App\Exceptions\ApiGenericException;

use App\Models\Rate;
use App\Models\VisitorCode;
use App\Models\Partner;
use App\Models\Facility;
use App\Services\Image;

class CombinedRate
{

    public $rates;
    const DISCLAIMER_PRINT_LANGUAGE = "Coupon  not valid on phone or mobile devices. Coupon must be printed in advance and surrendered to cashier at time of payment to receive discount rate.";

    public function __construct($rates)
    {
        $this->rates = collect($rates);
        $this->checkCouponCollectionIntegrity();
    }

    protected $guarded = ['id'];

    public function couponCode()
    {
        $this->rates->last(
            function ($key, $value) {
                $value->coupon_code;
            }
        );
    }

    public function generateHtml($visitorCode)
    {
        // generate barcode from visitor code
        $visitor = VisitorCode::firstOrNew(['visitor_code' => $visitorCode]);
        $barcode = $visitor->barCodeString($this->couponCode());

        $facility =  $this->last(Facility::with('geolocations')->find($this->facility()));

        $rates = Rate::associatedCouponRates($this)->get();
        $rates->prepend($this);

        $restriction = $rates->pluck('coupon_restrictions')->first(
            function ($key, $restriction) {
                return !!$restriction;
            }
        );

        $fields = [ // all data used in coupon generation
            'facility' => $facility,  // This is a problem for global coupons
            'geolocations' => $facility->geolocations,
            'coupons' => $rates,
            'mainCoupon' => $this,
            'couponCode' => $this->generateCouponCode($this),
            'barcode' => $barcode,
            'restriction' => $restriction,
            'DISCLAIMER_REMOVE_TEXT'=>self::DISCLAIMER_PRINT_LANGUAGE
        ];

        // Most partners should have images, but we need to allow for the possibility that some won't
        if ($this->isPartnerCoupon()) {
            if ($photo = Partner::with('photos')->find(
                $this->last(
                    function ($key, $value) {
                        $value->partner_id;
                    }
                )->photos->first()
            )) {
                $fields['logo'] = storage_path("app/" . $photo->image_name);
            };
        }
        return view('coupons.coupon', $fields)->render();
    }

    public function isPartnerCoupon()
    {
        $this->rates->last(
            function ($key, $value) {
                return $value->is_partner_coupon;
            }
        );
    }

    public function generateJpg($visitorCode)
    {
        $html = $this->generateHtml($visitorCode);

        $image = app()->make(Image::class);
        $image->setOption('width', '605');
        return $image->getOutputFromHtmlString($html);
    }


    public function price()
    {
        return $this->rates->reduce(
            function ($carry, $item) {
                return $carry + $item->price;
            }
        );
    }

    public function totalMaxLength()
    {
        return $this->rates->reduce(
            function ($carry, $item) {
                return $carry + $item->max_length;
            }
        );
    }

    public function totalMinLength()
    {
        return $this->rates->reduce(
            function ($carry, $item) {
                return $carry + $item->min_length;
            }
        );
    }

    public function checkCouponCollectionIntegrity()
    {
        if (!($first = $this->rates->first())) {
            return;
        }

        $this->rates->each(
            function ($rate) use ($first) {
                if (!$rate->is_coupon || $rate->coupon_code != $first->coupon_code || $rate->facility_id != $first->facility_id) {
                    throw new ApiGenericException('This rate is not a valid combined rate');
                }
            }
        );

        return true;
    }

    public function facility()
    {
        return $this->rates->first()->facility;
    }
}
