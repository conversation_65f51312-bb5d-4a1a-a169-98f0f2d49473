<?php

namespace App\Classes;

use Mail;

use App\Exceptions\ApiGenericException;

/**
 * Rate limited email
 */
class Email extends Messaging
{

    public static $emailRegex = '/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/';

    /**
     * View to use to send this email
     *
     * @var [type]
     */
    protected $view = 'email.generic';

    /**
     * Subject line for the email
     *
     * @var string
     */
    protected $subject = 'TEST';

    public function __construct()
    {
        parent::__construct();
        $this->subject = "TEST";
    }

    /**
     * Set the view
     *
     * @return $this
     */
    public function setView($view)
    {
        $this->view = $view;
        return $this;
    }

    /**
     * Set subject for the email
     *
     * @param [type] $subject [description]
     */
    public function setSubject($subject)
    {
        $this->subject = $subject;
        return $this;
    }

    /**
     * Send an email
     *
     * @param  $data array optional. Additional data to pass through to the email view.
     * @return [type] [description]
     */
    public function send($data = [])
    {
        if (!$this->to || !$this->body) {
            throw new ApiGenericException('Set to and body before sending a message.');
        }

        // Check we are not over the rate limit
        if ($this->rateLimiter->isOverRateLimit($this->to, $this->ip)) {
            throw new ApiGenericException('You have sent too many text messages to this number, please wait before sending.');
        }

        $data = array_merge(['body' => $this->body], $data);

        Mail::send(
            $this->view, $data, function ($message) {
                $message->to($this->to);
                $message->from(env('MAIL_DEFAULT_FROM'));
                $message->subject($this->subject);
            }
        );

        // Track sent message
        $this->rateLimiter->track($this->to, $this->ip);

        return true;
    }

    /**
     * Validate an email address
     *
     * @return [type] [description]
     */
    public static function validateEmail($email)
    {
        return preg_match(self::$emailRegex, $email);
    }
}
