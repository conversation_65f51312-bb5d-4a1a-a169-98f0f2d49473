<?php
namespace App\Classes;
use App\Exceptions\ApiGenericException;
use App\Models\Facility;
use App\Models\Reservation;
use Artisan;
use Exception;
use Config;

class SpotHero
{
    public function spotheroReservationCron($url,$secretKey){   

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Authorization: Basic '.$secretKey
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $xml_file = simplexml_load_string($response);
        $json = json_encode($xml_file );
        $arrayData = json_decode($json,TRUE);
        return $arrayData;
    }

    public function unitedSpotheroReservationCron($url,$secretKey){   

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Authorization: Basic '.$secretKey
            ),
        ));

        $response = curl_exec($curl);
        \Log::info('URL : '.$url);
        \Log::info('Secret Key :  '.$secretKey);
        \Log::info('Response :  '.json_encode($response));
        curl_close($curl);
        $xml_file = simplexml_load_string($response);
        $json = json_encode($xml_file );
        $arrayData = json_decode($json,TRUE);
        return $arrayData;
    }
}