<?php

namespace App\Classes;

use Exception;
use Log;
use App\Exceptions\ApiGenericException;
use App\Jobs\Reservation\ReservationPreAuthStatus;
use GlobalPayments\Api\ServiceConfigs\Gateways\PorticoConfig;
use GlobalPayments\Api\ServicesContainer;
use GlobalPayments\Api\Entities\Transaction;
use App\Services\LoggerFactory;
use App\Models\ParkEngage\DatacapTransaction;
use GlobalPayments\Api\Entities\Exceptions\ApiException;
use App\Models\RefundTransaction;

class FiservPaymentGateway
{
    const QUEUE_NAME_PRE_AUTH_STATUS = 'reservation-pre-auth-status';
    protected static function getLogger()
    {
        $logFactory = new LoggerFactory();
        return $logFactory->setPath('logs/fiserv')->createLogger('fiserv-payment-gateway');
    }

    protected static function baseCall($payload, $endpoint, $facility)
    {
        $log = self::getLogger();

        $url = config('parkengage.FISERV_URL').$endpoint;
        $authToken = base64_encode($facility->FacilityPaymentDetails->fiserv_username . ':' . $facility->FacilityPaymentDetails->fiserv_password);
        $authToken = config('parkengage.FISERV_AUTH_TOKEN');

        $headers = [
            "Authorization: Basic {$authToken}",
            "Content-Type: application/json"
        ];

        $log->info("Request to Fiserv [{$endpoint}] URL: $url");
        $log->info("Payload: " . json_encode($payload));

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        $log->info("Response: " . $response);
        $result = json_decode($response, true);
        return $result;
    }

    public static function fiservConfig($facility)
    {
        $config = new PorticoConfig();
        $config->serviceUrl = isset($facility->FacilityPaymentDetails->fiserv_server_url) ? $facility->FacilityPaymentDetails->fiserv_server_url : config('parkengage.FISERV_URL');
        $config->serviceUrl = isset($facility->FacilityPaymentDetails->fiserv_auth_token) ? $facility->FacilityPaymentDetails->fiserv_auth_token : config('parkengage.FISERV_AUTH_TOKEN');
        ServicesContainer::configureService($config);
    }

    public static function makePreAuthPaymentFiserv($request, $facility, $capture = false)
    {
        $expiry = str_replace('/', '', $request->expiration_date);

        $payload = [
            'merchid'  => $facility->FacilityPaymentDetails->fiserv_mid,
            'account'  => $request->card_number,
            'expiry'   => $expiry,
            'amount'   => $request->total,
            'currency' => 'USD',
            'name'     => $request->name_on_card ?? 'Card Holder',
            'capture'  => $capture ? 'y' : 'n',
            'receipt'  => 'y',
            'orderid'  => uniqid('ORD'),
        ];

        return self::baseCall($payload, 'auth', $facility);
    }

    public static function makeFiservPaymentTransaction($request, $paymentResponse, $user_id)
    {
        $fiservTransaction = null;
        if (isset($request->reservation_id) && !empty($request->reservation_id)) {
            $fiservTransaction = DatacapTransaction::where('reservation_id', $request->reservation_id)->first();
        }

        if (!$fiservTransaction) {
            $fiservTransaction = new DatacapTransaction();
        }

        $totalAmount = $request->total;
        if (!empty($request->discount_amount) && $request->discount_amount > 0) {
            $discountAmount = $totalAmount - $request->discount_amount;
            $totalAmount = ($totalAmount != $discountAmount) ? $discountAmount : $totalAmount;
        }

        $name = isset($request->card_name) && !empty($request->card_name) ?  $request->card_name : $request->name;

        $fiservTransaction->user_id                      = $user_id;
        $fiservTransaction->partner_id                   = $request->partner_id;
        $fiservTransaction->ip_address                   = \Request::ip();
        $fiservTransaction->total                        = $totalAmount;
        $fiservTransaction->response_message             = $paymentResponse['resptext'];
        $fiservTransaction->ref_id                       = $paymentResponse['retref'] ?? null; 
        $fiservTransaction->trans_id                     = $paymentResponse['retref'] ?? null;
        $fiservTransaction->name                         = $name;
        $fiservTransaction->card_last_four               = isset($request->card_last_four) ? $request->card_last_four : '';
        $fiservTransaction->card_type                    = $paymentResponse['receipt']['nameOnCard'] ?? '';
        $fiservTransaction->card_name                    = $paymentResponse['receipt']['nameOnCard'] ?? '';
        $fiservTransaction->expiry                       = isset($request['expiration_date']) ? $request['expiration_date'] : '';
        $fiservTransaction->token                        = isset($paymentResponse['token']) ? $paymentResponse['token'] : NULL;
        $fiservTransaction->result_reason                = $paymentResponse['resptext'];
        $fiservTransaction->currency_used                = "USD";
        $fiservTransaction->AuthCode                     = $paymentResponse['authcode'] ?? null;
        $fiservTransaction->card_brand_transaction_id    = Null;

        if (isset($request['reservation_id']) && !empty($request['reservation_id'])) {
            $fiservTransaction['reservation_id']           = $request['reservation_id'];
        }

        $fiservTransaction->save();

        if (!$fiservTransaction) {
            throw new ApiGenericException("Record Not Added");
        }

        return $fiservTransaction;
    }

    public static function capture($retref, $amount, $facility)
    {
        $payload = [
            'retref'  => $retref,
            'merchid' => $facility->FacilityPaymentDetails->fiserv_mid,
            'amount'  => $amount,
        ];

        return self::baseCall($payload, 'capture', $facility);
    }

    public static function fiservTicketPaymentRefund($grandTotal = null, $checkinData)
    {
        $payload = [
            'retref'  => $checkinData->transaction->anet_trans_id,
            'merchid' => $checkinData->facility->FacilityPaymentDetails->fiserv_mid,
        ];

        if ($grandTotal) {
            $payload['amount'] = $grandTotal;
        }

        return self::baseCall($payload, 'refund', $checkinData->facility);
    }

    public static function void($retref, $facility)
    {
        $payload = [
            'retref'  => $retref,
            'merchid' => $facility->FacilityPaymentDetails->fiserv_mid,
        ];

        return self::baseCall($payload, 'void', $facility);
    }

    public static function verifyCard($request, $facility)
    {
        $expiry = str_replace('/', '', $request->expiration_date);

        $payload = [
            'merchid'  => $facility->FacilityPaymentDetails->fiserv_mid,
            'account'  => $request->card_number,
            'expiry'   => $expiry,
            'amount'   => "0.00",
            'currency' => 'USD',
            'cvv2'     => $request->security_code ?? null,
            'postal'   => $request->zip_code ?? null,
            'name'     => $request->name_on_card ?? 'Card Holder',
            'capture'  => 'n',
        ];

        return self::baseCall($payload, 'auth', $facility);
    }

    public function processPreAuthRelease($request, $options = [])
    {
        try {
            // dd('processPreAuthRelease');
            $logFactory = new LoggerFactory();
            $log = $logFactory->setPath('logs/PaymentGateways')->createLogger('fiserv');

            $facility = $options['facility'];

            $tran_id = $request->hl_transactionId;
            $amount  = ($facility->FacilityPaymentDetails->fiserv_payment_env == 'test') ? '3' : $request->Amount;

            $log->info("Request PreAuth Release Fiserv -- Amount : {$amount}, Txn ID : {$tran_id} ");

            // call heartland config

            self::FiservConfig($facility);
            $response = $this->void($tran_id, $facility);

            $log->info("Reponse PreAuth Release Fiserv -- " . json_encode($response));
            if (!isset($response['resptext']) || $response['resptext'] !== 'Approval') {
                throw new ApiGenericException('Fiserv Payment Error: ' . ($response['resptext'] ?? 'Unknown error'));
            }
            // Save All Trx.
            $this->saveRefundTransactions($request, $response, 'Released');
            return $response;
        } catch (ApiException $th) {
            $responseCode = $th->responseCode ?? 'N/A';  // Default to 'N/A' if not set
            $responseMessage = $th->responseMessage ?? 'Unknown error';
            $log->error("Exception in PreAuth Release responseCode {$responseCode} AND responseMessage : {$responseMessage}  --" . json_encode($th->getMessage()));

            $arrResponseData = [
                'paymentReleaseRequest' => $request->all(),
                'paymentReleaseResponse' => $th,
                'ticketech_code' => $request->reference_key,
                'refund_for' => $request->preAuthDetails
            ];

            // Convert to JSON string
            $jsonRequestData = json_encode($arrResponseData);

            // Trigger email
            dispatch((new ReservationPreAuthStatus($jsonRequestData))->onQueue(self::QUEUE_NAME_PRE_AUTH_STATUS));

            if ($responseCode == '6') {
                throw new ApiGenericException('Transaction rejected because amount to be returned exceeds the original settlement amount or the return amount is zero');
            }
            throw new ApiGenericException('Transaction rejected because amount to be returned exceeds the original settlement amount or the return amount is zero');
        } catch (\Throwable $th) {

            $arrResponseData = [
                'paymentReleaseRequest' => $request->all(),
                'paymentReleaseResponse' => $th,
                'ticketech_code' => $request->reference_key,
                'refund_for' => $request->preAuthDetails
            ];

            // Convert to JSON string
            $jsonRequestData = json_encode($arrResponseData);

            // Trigger email
            dispatch((new ReservationPreAuthStatus($jsonRequestData))->onQueue(self::QUEUE_NAME_PRE_AUTH_STATUS));

            $log->info("Exception In PreAuth : " . $th->getMessage() . ' File Name ' . $th->getFile() . ' Line No ' . $th->getLine());
            return false;
        }
    }

    // Save Refund Transaction
    public function saveRefundTransactions($request, $responseData, $requestType)
    {
        // $responseData = json_decode($responseData);
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/PaymentGateways')->createLogger('fiserv');

        try {
            $refundTransaction = new RefundTransaction();

            // Recieved from Datacap/Manual Start
            $refundTransaction->sent                            = '1';
            $refundTransaction->ip_address                      = \Request::ip();
            $refundTransaction->user_id                         = $request->user_id;
            $refundTransaction->reference_key                   = $request->reference_key;
            $refundTransaction->total                           = $request->Amount;
            $refundTransaction->original_transaction_id         = $request->hl_transactionId;
            $refundTransaction->description                     = isset($request->txnType) ? "Fiserv {$request->txnType} Done for User ID : {$request->user_id}" : "Fiserv Refund Done for User ID : {$request->user_id}";
            $refundTransaction->method                          = isset($request->txnType) ? 'Released' : "Refund";
            $refundTransaction->payment_gateway                 = 'fiserv';
            // Recieved from Datacap/Manual End


            // Recieved from Fiserv Start
            $refundTransaction->auth_code                       = $responseData['authcode'];
            $refundTransaction->response_code                   = $responseData['respcode'];
            $refundTransaction->anet_trans_id                   = $responseData['retref'];
            $refundTransaction->status_message                  = $responseData['resptext'];
            $refundTransaction->response_message                = isset($request->txnType) ? $request->txnType : "Processed";
            // Recieved from Heartland End
            
            $refundTransaction->save();
            $log->info("Refund Transaction Saved -- " . json_encode($refundTransaction));
            return $refundTransaction;
        } catch (\Throwable $th) {
            $log->error("Exception Save data  : " . $th->getMessage() . ' File Name ' . $th->getFile() . ' Line No ' . $th->getLine());
        }
    }
}