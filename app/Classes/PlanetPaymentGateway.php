<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use App\Services\LoggerFactory;
use App\Models\AuthorizeNetTransaction;
use App\Models\ParkEngage\PlanetPaymentProfile;
use App\Models\Reservation;

/**
 * Utility class for interacting with the Ticketech APIs
 */
class PlanetPaymentGateway
{

    protected $log;
    public function __construct()
    {
        $logFactory         =  new  LoggerFactory();
        $this->log = $logFactory->setPath('logs/parkengage/planet-payment-gateway')->createLogger('planet-payment-gateway');
    }

    protected static function baseCall($params)
    {

        $validationID = '"' . $params['validationID'] . '"';
        $validationCode = '"' . $params['validationCode'] . '"';

        $cardNumber = $params['cardNumber'];
        $expirationDate = $params['expirationDate'];
        $securityCode = $params['securityCode'];

        $total = $params['total'] * 100;
        $host = $params['host'];
        $type = '"' . $params['type'] . '"';
        $OptionFlags = '"' . $params['OptionFlags'] . '"';

        $transRefNo = rand(1000, 9999) . rand(1000, 9999) . rand(1000, 9999);

        //dd($validationID, $validationCode, $cardNumber, $expirationDate, $securityCode, $total,$host);
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $host,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
              "Request": {
                "Type": ' . $type . ',
                "Version": "W2MXG520",
                  "Credentials": {
                    "ValidationID": ' . $validationID . ',
                    "ValidationCode": ' . $validationCode . ',
                    "ValidationCodeHash": null
                  },
                  "Params": {
                      "PaymentOkUrl": "",
                      "CardNumber": "' . $cardNumber . '",
                      "CardExpiryDateMMYY": "' . $expirationDate . '",
                      "CardStartDateMMYY": "",
                      "CardIssueNumber": "",
                      "CardCvv2": "' . $securityCode . '",
                      "CardholderStreetAddress1": "",
                      "CardholderCity": "",
                      "CardholderState": "",
                      "CardholderZipCode": "",
                      "CardholderNameFirst": "",
                      "CardholderNameLast": "",
                      "Amount": "' . $total . '",
                      "Currency": "USD",
                      "RequesterTransRefNum": "' . $transRefNo . '",
                      "UserData1": "",
                      "UserData2": "",
                      "UserData3": "",
                      "UserData4": "",
                      "UserData5": "",
                      "OptionFlags": ' . $OptionFlags . '
                    }
            }
        }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);


        $refundstatus = json_decode($response, TRUE);

        \Log::info("Planet Payment Request  : " . json_encode($params));
        \Log::info("Planet Payment Response : " . $response);
        return $refundstatus;
        //$obj = new PlanetPaymentGateway();
        //$obj->logData($params, json_encode($response), curl_getinfo($curl, CURLINFO_HTTP_CODE));
        //dd($response,$refundstatus);
        /*if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
            return $refundstatus;
        }
        $this->log->info("Response Data Planet (Saved Cards): " . json_encode($response));
        return $refundstatus;*/
    }

    public function logData($params, $result, $status_code)
    {
        $this->log->info('Planet Payment Gateway Class Log Params:' . json_encode($params));
        $this->log->info('Planet Payment Gateway Class Log Response :' . json_encode($result) . 'status code : ' . $status_code);
    }


    public static function planetPaymentByCrediCard($ticket, $total, $request)
    {
        $params = [];
        $params['validationID'] = $ticket->facility->FacilityPaymentDetails->planet_merchant_id;
        $params['validationCode'] = $ticket->facility->FacilityPaymentDetails->planet_validation_code;
        $params['host'] = $ticket->facility->FacilityPaymentDetails->planet_payment_url;
        $params['total'] = $total;
        $params['cardNumber'] = $request->card_number;
        $params['expirationDate'] = str_replace('/', '', $request->expiration_date);
        $params['securityCode'] = $request->security_code;
        $params['type'] = "EftAuthorization";
        $params['OptionFlags'] = "G";
        return self::baseCall($params);
    }


    public static function savePlanetTransaction($refundstatus, $user_id)
    {
        $total = floatval($refundstatus["Response"]["Params"]["AmountUsed"] / 100);
        $authorized_anet_transaction                    = new AuthorizeNetTransaction();
        $authorized_anet_transaction->sent              = '1';
        $authorized_anet_transaction->user_id           = $user_id;
        $authorized_anet_transaction->ip_address        = \Request::ip();
        $authorized_anet_transaction->total             = $total;
        $authorized_anet_transaction->description       = "Planet payment: " . $user_id;
        $authorized_anet_transaction->card_type         = $refundstatus["Response"]["Params"]["CardSchemeName"];
        $authorized_anet_transaction->ref_id            = $refundstatus["Response"]["Params"]["RequesterTransRefNum"];
        $authorized_anet_transaction->anet_trans_id     = $refundstatus["Response"]["Params"]["TxID"];
        $authorized_anet_transaction->method            = "card";
        $authorized_anet_transaction->payment_last_four = isset($refundstatus["Response"]["Params"]["CardNumberLast4"]) ? $refundstatus["Response"]["Params"]["CardNumberLast4"] : '0';
        $authorized_anet_transaction->expiration        = isset($refundstatus["Response"]["Params"]["CardExpiryDateMMYY"]) ? $refundstatus["Response"]["Params"]["CardExpiryDateMMYY"] : '0';
        $authorized_anet_transaction->response_message  = "Processed";
        $authorized_anet_transaction->save();

        return $authorized_anet_transaction;
    }


    public static function planetPaymentByToken($ticket, $total, $request)
    {
        $params = [];
        $params['validationID'] = $ticket->facility->FacilityPaymentDetails->planet_merchant_id;
        $params['validationCode'] = $ticket->facility->FacilityPaymentDetails->planet_validation_code;
        $params['host'] = $ticket->facility->FacilityPaymentDetails->planet_payment_url;
        $params['total'] = $total;
        if (isset($request->session_id) && $request->session_id != '') {
            $params['cardNumber'] = $request->session_id;
        } else {
            $params['cardNumber'] = $ticket->checkout_session_id != '' ? $ticket->checkout_session_id : $ticket->session_id;
        }
        $params['expirationDate'] = "";
        $params['securityCode'] = "";
        $params['type'] = "payrequestnocardread";
        $params['OptionFlags'] = "P";
        return self::baseCall($params);
    }

    public static function planetPaymentReservationByCrediCard($facility, $total, $request)
    {
        $params = [];
        $params['validationID']     = $facility->FacilityPaymentDetails->planet_merchant_id;
        $params['validationCode']   = $facility->FacilityPaymentDetails->planet_validation_code;
        $params['host']             = $facility->FacilityPaymentDetails->planet_payment_url;
        $params['total']            = $total;
        $params['cardNumber']       = $request->card_number;
        $params['expirationDate']   = str_replace('/', '', $request->expiration_date);
        $params['securityCode']     = $request->security_code;
        $params['type']             = "EftAuthorization";
        $params['OptionFlags']      = "G";
        return self::baseCall($params);
    }

    public static function planetPaymentReservationByToken($facility, $total, $request)
    {
        $params = [];
        $params['validationID'] = $facility->FacilityPaymentDetails->planet_merchant_id;
        $params['validationCode'] = $facility->FacilityPaymentDetails->planet_validation_code;
        $params['host'] = $facility->FacilityPaymentDetails->planet_payment_url;
        $params['total'] = $total;
        $params['cardNumber'] = $request->payment_profile_id;
        $params['expirationDate'] = "";
        $params['securityCode'] = "";
        $params['type'] = "payrequestnocardread";
        $params['OptionFlags'] = "P";
        return self::baseCall($params);
    }

    public static function planetPaymentByProfile($ticket, $total, $request)
    {
        $params = [];
        $params['validationID'] = $ticket->facility->FacilityPaymentDetails->planet_merchant_id;
        $params['validationCode'] = $ticket->facility->FacilityPaymentDetails->planet_validation_code;
        $params['host'] = $ticket->facility->FacilityPaymentDetails->planet_payment_url;
        $params['total'] = $total;
        $params['cardNumber'] = $request->payment_profile_id;
        $params['expirationDate'] = "";
        $params['securityCode'] = "";
        $params['type'] = "payrequestnocardread";
        $params['OptionFlags'] = "P";
        return self::baseCall($params);
    }

    public static function savePlanetCard($refundstatus, $user_id, $partner_id, $request)
    {

        // $cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $partner_id)->where('card_last_four', $refundstatus["Response"]["Params"]["CardNumberLast4"])->first();

        // if ($cardCheck) {
        //     return $cardCheck;
        // }
        // $status = 0;

        // $user_card = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->first();

        $data['user_id']                        = $user_id;
        $data['partner_id']                     = $partner_id;
        $data['facility_payment_type_id']       = $request->facility_payment_type_id;
        $data['partner_payment_gateway_id']     = $request->partner_payment_gateway_id;
        $data['name']                           = isset($request->name_on_card) ? $request->name_on_card : '';
        $data['card_last_four']                 = $refundstatus["Response"]["Params"]["CardNumberLast4"];
        $data['card_type']                      = $refundstatus["Response"]["Params"]["CardSchemeId"];
        $data['card_name']                      = $refundstatus["Response"]["Params"]["CardSchemeName"];
        $data['expiry']                         = $request->expiration_date;
        $data['token']                          = $refundstatus["Response"]["Params"]["Token"];
        $data['tx_state_text']                  = $refundstatus["Response"]["Params"]["TxStateText"];
        $data['tx_state']                       = $refundstatus["Response"]["Params"]["TxState"];
        $data['result_reason']                  = $refundstatus["Response"]["Params"]["ResultReason"];
        $data['currency_used']                  = $refundstatus["Response"]["Params"]["CurrencyUsed"];

        // Condition check for make default card if no card added than make this card as default. 
        // Vijay : 05-12-2024 New Add Card flow
        $cardDetails = PlanetPaymentProfile::whereNull('deleted_at')->where(['partner_id' => $partner_id, 'user_id' => $user_id])->first();
        if (!$cardDetails) {
            $data['is_default'] = 1;
        } else {
            if (isset($request->is_default) && $request->is_default == '1') {
                $data['is_default'] = 1;

                // Remove Other card from default
                PlanetPaymentProfile::where(['partner_id' => $partner_id, 'user_id' => $user_id])->update(array('is_default' => 0));
            }
        }
        $result = PlanetPaymentProfile::create($data);
        if ($result && isset($request->is_default) && $request->is_default == 1) {
            PlanetPaymentProfile::where(['user_id' => $user_id, 'card_last_four' => $result->card_last_four, 'expiry' => $result->expiry])->update(array('is_default' => 1));
        }
        return $result;
        // Close !!!
        // Below is code is improve with above 

        if (!$user_card) {
            $data['is_default'] = 1;
        } else {
            if (is_numeric($request->is_default)) {

                if (is_numeric($request->is_default)) {
                    $status = $request->is_default;
                }

                if (!empty($status)) {
                    PlanetPaymentProfile::where(['user_id' => $user_id])->update(array('is_default' => 0));
                }

                $data['is_default'] = $status;
            }
        }
        $result = PlanetPaymentProfile::create($data);

        return $result;
    }

    // common refund function  ashutosh  15-09-2023
    protected static function refundBaseCall($params)
    {

        $url =  $params['host'] . $params['tran_id'];
        $validationID = '"' . $params['validationID'] . '"';
        $validationCode = '"' . $params['validationCode'] . '"';
        $amount = $params['amount'];
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
            "Request": {
                "Type": "payrequestnocardreadbytxid",
                "Version": "W2MXG520",
                "Credentials": {
                    "ValidationID": ' . $validationID . ',
                    "ValidationCode": ' . $validationCode . ',
                    "ValidationCodeHash": null
                },
                "Params": {
                    "RequesterTransRefNum": "NAU TEST PAYMENT 001",
                     "Amount": "' . $amount . '",
                    "Currency": "USD"
                    }
            }
        }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        return $refundstatus = json_decode($response, TRUE);
    }

    protected static function refundBaseCallWithSession($params)
    {

        $url =  $params['host'];
        $validationID = '"' . $params['validationID'] . '"';
        $validationCode = '"' . $params['validationCode'] . '"';
        $amount = $params['amount'];
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
            "Request": {
                "Type": "payrequestnocardread",
                "Version": "W2MXG520",
                "Credentials": {
                    "ValidationID": ' . $validationID . ',
                    "ValidationCode": ' . $validationCode . ',
                    "ValidationCodeHash": null
                },
                "Params": {
                    "RequesterTransRefNum": "NAU TEST PAYMENT 001",
                    "Amount": "' . $amount . '",
					"Currency": "USD",
					"PaymentOkUrl": "", 
					"CardNumber": "' . $params['CardNumber'] . '",
					"CardExpiryDateMMYY": "", 
                    "CardStartDateMMYY": "", 
                    "CardIssueNumber": "", 
                    "CardCvv2": "", 
                    "CardholderStreetAddress1": "", 
                    "CardholderCity": "", 
                    "CardholderState": "", 
                    "CardholderZipCode": "", 
                    "CardholderNameFirst": "", 
                    "CardholderNameLast": "",
                    "UserData1": "", 
                    "UserData2": "", 
                    "UserData3": "", 
                    "UserData4": "", 
                    "UserData5": "", 	
					"OptionFlags": "' . $params['OptionFlags'] . '" 
                   
                    }
            }
        }',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Cookie: BIGipServerP_W2P-UAT-mxg=1527648684.47873.0000; TS017b0d4e=0158714ae9f8a3de5ace415ce407ba9a2715f731ff7355b753f81146bd2f0eca97fde64169e77f071b6aff0cfa613580e13af57cb059f24e78bd2ee77a4e01c6e14d370040'
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        return $refundstatus = json_decode($response, TRUE);
    }

    public static function planetPaymentRefund($reservation_id)
    {
        $params = [];
        $reservation = Reservation::with('transaction', 'facility.FacilityPaymentDetails')->where("id", $reservation_id)->whereNotNull('anet_transaction_id')->first();
        if (isset($reservation) && !empty($reservation)) {
            $tran_id = $reservation->transaction->anet_trans_id;
            $amount = - ($reservation->total * 100);
            $params['validationID'] = $reservation->facility->FacilityPaymentDetails->planet_merchant_id;
            $params['validationCode'] = $reservation->facility->FacilityPaymentDetails->planet_validation_code;
            $params['tran_id'] = $tran_id;
            $params['amount'] = $amount;
            $params['host'] = $reservation->facility->FacilityPaymentDetails->planet_refund_url;
            return self::refundBaseCall($params);
        } else {
            return false;
        }
    }

    // save card VP Device
    public static function savePlanetCardVpDevice($payment_details, $user_id, $partner_id)
    {

        $card_last_four = substr($payment_details['MaskedPAN'], -4);
        $card_holder_name = stripslashes($payment_details['CardHolderName']);
        $card_holder_name = str_replace('/', ' ', $card_holder_name);

        $cardCheck = PlanetPaymentProfile::whereNull('deleted_at')->where('user_id', $user_id)->where('partner_id', $partner_id)->where('card_last_four', $card_last_four)->first();

        if ($cardCheck) {
            return $cardCheck;
        }
        $data['user_id'] = $user_id;
        $data['partner_id'] = $partner_id;
        $data['name'] = $card_holder_name;
        $data['card_last_four'] = $card_last_four;
        $data['card_type'] = $payment_details['CardType'];
        $data['card_name'] = $payment_details['CardType'];
        $data['expiry'] = $payment_details['expiry'];
        $data['token'] = $payment_details["Token"];
        $data['tx_state_text'] = $payment_details['ProcessorMessage'];
        $data['tx_state'] = $payment_details['tx_state'];
        $data['result_reason'] = $payment_details['StatusMessage'];
        $data['currency_used'] = "USD";

        $result = PlanetPaymentProfile::create($data);

        return $result;
    }

    public static function planetPaymentByProfileEvent($facility, $request)
    {
        $params = [];
        $params['validationID'] = $facility->FacilityPaymentDetails->planet_merchant_id;
        $params['validationCode'] = $facility->FacilityPaymentDetails->planet_validation_code;
        $params['host'] = $facility->FacilityPaymentDetails->planet_payment_url;
        $params['total'] = $request->total;
        $params['cardNumber'] = $request->payment_profile_id;
        $params['expirationDate'] = "";
        $params['securityCode'] = "";
        $params['type'] = "payrequestnocardread";
        $params['OptionFlags'] = "P";
        return self::baseCall($params);
    }

    public static function planetPaymentByCrediCardEvent($facility, $request)
    {
        $params = [];
        $params['validationID'] = $facility->FacilityPaymentDetails->planet_merchant_id;
        $params['validationCode'] = $facility->FacilityPaymentDetails->planet_validation_code;
        $params['host'] = $facility->FacilityPaymentDetails->planet_payment_url;
        $params['total'] = $request->total;
        $params['cardNumber'] = $request->card_number;
        $params['expirationDate'] = str_replace('/', '', $request->expiration_date);
        $params['securityCode'] = $request->security_code;
        $params['type'] = "EftAuthorization";
        $params['OptionFlags'] = "G";
        return self::baseCall($params);
    }

    public static function planetPaymentTicketRefund($grandTotal, $checkinData)
    {
        $params = [];
        $tran_id = $checkinData->transaction->anet_trans_id;
        $amount = - ($grandTotal * 100);
        $params['validationID'] = $checkinData->facility->FacilityPaymentDetails->planet_merchant_id;
        $params['validationCode'] = $checkinData->facility->FacilityPaymentDetails->planet_validation_code;

        $params['amount'] = $amount;
        $params['host'] = $checkinData->facility->FacilityPaymentDetails->planet_refund_url;
        if ($checkinData->session_id) {
            $params['CardNumber'] = $checkinData->session_id;
            $params['OptionFlags'] = "P";
            return self::refundBaseCallWithSession($params);
        } else if ($checkinData->checkout_session_id) {
            $params['CardNumber'] = $checkinData->checkout_session_id;
            $params['OptionFlags'] = "P";
            return self::refundBaseCallWithSession($params);
        } else {
            $tran_id = $checkinData->transaction->anet_trans_id;
            $params['tran_id'] = $tran_id;
            return self::refundBaseCall($params);
        }
    }
}
