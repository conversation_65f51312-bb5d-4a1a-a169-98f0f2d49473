<?php

namespace App\Classes;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class ProPayService
{
    protected $client;

    public function __construct()
    {
        // Initialize Guzzle client
        $this->client = new Client();
    }

    public function createMerchantAccount(array $data)
    {
        try {
            $response = $this->client->put(env('PROFAC_BASE_REST_URL') . '/propayapi/signup', [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'),
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/json'
                ]
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (\Exception $e) {
            Log::error('ProPayService Error: ' . $e->getMessage());
            throw new \Exception('Error occurred: ' . $e->getMessage());
        }
    }

    public function preparePayload(array $requestData)
    {
        return [
            "PersonalData" => [
                "FirstName" => $requestData['personalFirstName'],
                "MiddleInitial" => $requestData['personalMiddleInitial'] ?? null,
                "LastName" => $requestData['personalLastName'],
                "DateOfBirth" => $requestData['personalDateOfBirth'],
                "SocialSecurityNumber" => $requestData['socialSecurityNumber'],
                "SourceEmail" => $requestData['sourceEmail'],
                "PhoneInformation" => [
                    "DayPhone" => $requestData['dayPhone'],
                    "EveningPhone" => $requestData['eveningPhone'] ?? null,
                ],
                "USCitizen" => $requestData['usCitizen'],
                "BOAttestation" => true,
                "TermsAcceptanceIP" => $requestData['termsAcceptanceIP'],
                "TermsAcceptanceTimeStamp" => $requestData['termsAcceptanceTimeStamp'],
                "TermsVersion" => $requestData['termsVersion'],
                "NotificationEmail" => $requestData['notificationEmail'],
                "TimeZone" => $requestData['timeZone'] ?? 'UTC'
            ],
            "SignupAccountData" => [
                "CurrencyCode" => $requestData['currencyCode'],
                "Tier" => $requestData['tier']
            ],
            "BusinessData" => [
                "BusinessLegalName" => $requestData['businessLegalName'],
                "DoingBusinessAs" => $requestData['doingBusinessAs'] ?? null,
                "EIN" => $requestData['ein'],
                "MerchantCategoryCode" => $requestData['merchantCategoryCode'] ?? null,
                "BusinessType" => $requestData['businessType'] ?? null,
                "WebsiteURL" => $requestData['websiteURL'] ?? null,
                "BusinessDescription" => $requestData['businessDescription'] ?? null,
                "MonthlyBankCardVolume" => $requestData['monthlyBankCardVolume'] ?? null,
                "AverageTicket" => $requestData['averageTicket'] ?? null,
                "HighestTicket" => $requestData['highestTicket'] ?? null,
                "BusinessPhoneNumber" => $requestData['businessPhoneNumber'],
                "EstimatedCardUsageData" => [
                    "CardPresent" => $requestData['cardPresent'] ?? null,
                    "CardNotPresentKeyed" => $requestData['cardNotPresentKeyed'] ?? null,
                    "CardNotPresentEcommerce" => $requestData['cardNotPresentEcommerce'] ?? null
                ]
            ],
            "Address" => [
                "Address1" => $requestData['personalAddress1'],
                "Address2" => $requestData['personalAddress2'] ?? null,
                "City" => $requestData['personalCity'],
                "State" => $requestData['personalState'],
                "Country" => $requestData['personalCountry'],
                "Zip" => $requestData['personalZip']
            ],
            "MailAddress" => [
                "Address1" => $requestData['mailAddress1'],
                "Address2" => $requestData['mailAddress2'] ?? null,
                "City" => $requestData['mailCity'],
                "State" => $requestData['mailState'],
                "Country" => $requestData['mailCountry'],
                "Zip" => $requestData['mailZip']
            ],
            "BusinessAddress" => [
                "Address1" => $requestData['businessAddress1'],
                "Address2" => $requestData['businessAddress2'] ?? null,
                "City" => $requestData['businessCity'],
                "State" => $requestData['businessState'],
                "Country" => $requestData['businessCountry'],
                "Zip" => $requestData['businessZip']
            ],
            "BankAccount" => [
                "AccountCountryCode" => $requestData['accountCountryCode'] ?? 'USA',
                "BankAccountNumber" => $requestData['bankAccountNumber'],
                "RoutingNumber" => $requestData['routingNumber'],
                "AccountOwnershipType" => $requestData['accountOwnershipType'] ?? null,
                "BankName" => $requestData['bankName'],
                "AccountType" => $requestData['accountType'],
                "AccountName" => $requestData['accountName'] ?? null,
                "Description" => $requestData['description'] ?? null
            ],
            "Devices" => [[
                "Name" => $requestData['deviceName'] ?? null,
                "Quantity" => $requestData['deviceQuantity'] ?? null
            ]],
            "BeneficialOwnerData" => $requestData['BeneficialOwnerData'] ?? null
        ];
    }

    public function prepareEditBankAccountPayload(array $requestData, $AccountNumber)
    {
        return [
            "AccountNumber" => $AccountNumber,
            "IsSecondaryBankAccount" => $requestData['IsSecondaryBankAccount'],
            "BankAccount" => [
                "AccountCountryCode" => $requestData['BankAccount']['AccountCountryCode'],
                "BankAccountNumber" => $requestData['BankAccount']['BankAccountNumber'],
                "RoutingNumber" => $requestData['BankAccount']['RoutingNumber'],
                "AccountOwnershipType" => $requestData['BankAccount']['AccountOwnershipType'] ?? null,
                "BankName" => $requestData['BankAccount']['BankName'],
                "AccountType" => $requestData['BankAccount']['AccountType']
            ]
        ];
    }

    /**
     * Sends the request to edit the merchant bank account in ProPay.
     *
     * @param array $data
     * @param string $AccountNumber
     * @return array
     * @throws \Exception
     */
    public function editMerchantBankAccount(array $data, $AccountNumber)
    {
        try {
            // Send the PUT request to the ProPay API
            $response = $this->client->put(env('PROFAC_BASE_REST_URL') . "/propayAPI/MerchantBankAccount/{$AccountNumber}", [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'), // Use your ProPay API key
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/json'
                ]
            ]);

            // Decode the JSON response
            return json_decode($response->getBody()->getContents(), true);
        } catch (\Exception $e) {
            Log::error('ProPayService Error: ' . $e->getMessage());
            throw new \Exception('Error occurred while editing the merchant bank account: ' . $e->getMessage());
        }
    }

    public function editMerchantContactInfo(array $data, $AccountNumber)
    {
        try {
            // Prepare API URL (sandbox or live depending on your environment)
            $url = env('PROFAC_BASE_REST_URL') . "/propayAPI/MerchantContactInfo/{$AccountNumber}";

            // Send the PUT request to ProPay API
            $response = $this->client->put($url, [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'), // Use your ProPay API key
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/json',
                ]
            ]);

            // Decode the JSON response
            $responseBody = json_decode($response->getBody()->getContents(), true);

            // Check if the response status is successful
            if (isset($responseBody['Status']) && $responseBody['Status'] == '00') {
                return [
                    'status' => 'success',
                    'data' => $responseBody,
                ];
            } else {
                throw new \Exception('Failed to update contact info. Status: ' . $responseBody['Status']);
            }
        } catch (\Exception $e) {
            Log::error('ProPay Edit Merchant Contact Info Error: ' . $e->getMessage());
            throw new \Exception('Error occurred while updating merchant contact info: ' . $e->getMessage());
        }
    }

    public function editMerchantAddress(array $data, $AccountNumber)
    {
        try {
            // Prepare API URL (sandbox or live depending on your environment)
            $url = env('PROFAC_BASE_REST_URL') . "/propayAPI/MerchantAddress/{$AccountNumber}";

            // Send the PUT request to ProPay API
            $response = $this->client->put($url, [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'), // Use your ProPay API key
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/json',
                ]
            ]);

            // Decode the JSON response
            $responseBody = json_decode($response->getBody()->getContents(), true);

            // Check if the response status is successful
            if (isset($responseBody['Status']) && $responseBody['Status'] == '00') {
                return [
                    'status' => 'success',
                    'data' => $responseBody,
                ];
            } else {
                throw new \Exception('Failed to update address info. Status: ' . $responseBody['Status']);
            }
        } catch (\Exception $e) {
            Log::error('ProPay Edit Merchant Address Error: ' . $e->getMessage());
            throw new \Exception('Error occurred while updating merchant address info: ' . $e->getMessage());
        }
    }

    public function editMerchantBusinessInfo(array $data, $AccountNumber)
    {
        try {
            // Prepare API URL (sandbox or live depending on your environment)
            $url = env('PROFAC_BASE_REST_URL') . "/propayAPI/MerchantBusinessInfo/{$AccountNumber}";

            // Send the PUT request to ProPay API
            $response = $this->client->put($url, [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'), // Use your ProPay API key
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/json',
                ]
            ]);

            // Decode the JSON response
            $responseBody = json_decode($response->getBody()->getContents(), true);

            // Check if the response status is successful
            if (isset($responseBody['Status']) && $responseBody['Status'] == '00') {
                return [
                    'status' => 'success',
                    'data' => $responseBody,
                ];
            } else {
                throw new \Exception('Failed to update business info. Status: ' . $responseBody['Status']);
            }
        } catch (\Exception $e) {
            Log::error('ProPay Edit Merchant Business Info Error: ' . $e->getMessage());
            throw new \Exception('Error occurred while updating merchant business info: ' . $e->getMessage());
        }
    }

    public function uploadChargebackDocument(array $data)
    {
        try {
            // Send the PUT request to ProPay API
            $response = $this->client->put(env('PROFAC_BASE_REST_URL') . '/propayAPI/DocumentUpload', [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'), // Use your ProPay API key
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/json',
                ]
            ]);

            // Decode the JSON response
            return json_decode($response->getBody()->getContents(), true);
        } catch (\Exception $e) {
            Log::error('ProPay Upload Document Error: ' . $e->getMessage());
            throw new \Exception('Error occurred while uploading chargeback document: ' . $e->getMessage());
        }
    }

    public function getTransactionDetails(array $data)
    {
        // Prepare XML data
        $xml = new \SimpleXMLElement('<XMLRequest/>');
        $xml->addChild('certStr', env('CERSTR'));
        $xml->addChild('termid', env('TERMID'));
        $xml->addChild('class', 'partner');
        $xml->addChild('XMLTrans');
        $xml->XMLTrans->addChild('transType', '34');
        \Log::info('Transaction Detail data', $data);
        // Add required elements
        $xml->XMLTrans->addChild('accountNum', $data['accountNum']);

        // Add optional elements if they exist
        if (!empty($data['amount'])) {
            $xml->XMLTrans->addChild('amount', $data['amount']);
        }
        if (!empty($data['ccNumLastFour'])) {
            $xml->XMLTrans->addChild('ccNumLastFour', $data['ccNumLastFour']);
        }
        if (!empty($data['comment1'])) {
            $xml->XMLTrans->addChild('comment1', $data['comment1']);
        }
        if (!empty($data['invNum'])) {
            $xml->XMLTrans->addChild('invNum', $data['invNum']);
        }
        if (!empty($data['invoiceExternalRefNum'])) {
            $xml->XMLTrans->addChild('invoiceExternalRefNum', $data['invoiceExternalRefNum']);
        }
        if (!empty($data['payerName'])) {
            $xml->XMLTrans->addChild('payerName', $data['payerName']);
        }
        if (!empty($data['transNum'])) {
            $xml->XMLTrans->addChild('transNum', $data['transNum']);
        }
        if (!empty($data['transType'])) {
            $xml->XMLTrans->addChild('transType', $data['transType']);
        }

        try {
            // Send the XML request to ProPay's API
            $response = $this->client->post(env('PROFAC_BASE_XML_URL'), [
                'body' => $xml->asXML(),
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'), // Use your ProPay API key
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/xml',
                ]
            ]);

            // Parse XML response
            $responseXml = simplexml_load_string($response->getBody()->getContents());

            // Convert XML to JSON-friendly array
            $responseArray = json_decode(json_encode($responseXml), true);

            return $responseArray;
        } catch (\Exception $e) {
            Log::error('ProPay Get Transaction Details Error: ' . $e->getMessage());
            throw new \Exception('Error occurred while getting transaction details: ' . $e->getMessage());
        }
    }

    public function uploadDocument(array $data)
    {
        // Prepare XML data
        $xml = new \SimpleXMLElement('<XMLRequest/>');
        $xml->addChild('certStr', env('CERSTR'));
        $xml->addChild('termid', env('TERMID'));
        $xml->addChild('class', 'partner');
        $xml->addChild('XMLTrans');
        $xml->XMLTrans->addChild('transType', '47');

        // Add required elements
        $xml->XMLTrans->addChild('AccountNum', $data['AccountNum']);
        $xml->XMLTrans->addChild('DocumentName', $data['DocumentName']);
        $xml->XMLTrans->addChild('DocCategory', $data['DocCategory']);
        $xml->XMLTrans->addChild('DocType', $data['DocType']);
        $xml->XMLTrans->addChild('Document', $data['Document']);

        try {
            // Send the XML request to ProPay's API
            $response = $this->client->post(env('PROFAC_BASE_XML_URL'), [
                'body' => $xml->asXML(),
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'), // Use your ProPay API key
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/xml',
                ]
            ]);

            // Parse XML response
            $responseXml = simplexml_load_string($response->getBody()->getContents());

            // Convert XML to JSON-friendly array
            $responseArray = json_decode(json_encode($responseXml), true);

            return $responseArray;
        } catch (\Exception $e) {
            Log::error('ProPay Document Upload Error: ' . $e->getMessage());
            throw new \Exception('Error occurred while uploading document: ' . $e->getMessage());
        }
    }

    public function getSSOAuthToken(array $data)
    {
        // Prepare XML data
        $xml = new \SimpleXMLElement('<XMLRequest/>');
        $xml->addChild('certStr', env('CERSTR'));
        $xml->addChild('termid', env('TERMID'));
        $xml->addChild('class', 'partner');
        $xml->addChild('XMLTrans');
        $xml->XMLTrans->addChild('transType', '300');

        // Add required elements
        $xml->XMLTrans->addChild('accountNum', $data['AccountNum']);
        $xml->XMLTrans->addChild('IpAddress', $data['IpAddress']);
        $xml->XMLTrans->addChild('ReferrerUrl', $data['ReferrerUrl']);
        $xml->XMLTrans->addChild('IpSubnetMask', $data['IpSubnetMask']);

        try {
            // Send the XML request to ProPay's API
            $response = $this->client->post(env('PROFAC_BASE_XML_URL'), [
                'body' => $xml->asXML(),
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'), // Use your ProPay API key
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/xml',
                ]
            ]);

            // Parse XML response
            $responseXml = simplexml_load_string($response->getBody()->getContents());

            // Convert XML to JSON-friendly array
            $responseArray = json_decode(json_encode($responseXml), true);

            return $responseArray;
        } catch (\Exception $e) {
            Log::error('SSO Auth Token get Error: ' . $e->getMessage());
            throw new \Exception('Error occurred while getting SSO Auth Token: ' . $e->getMessage());
        }
    }

    public function getMerchantAccountDetail($AccountNumber)
    {
        try {
            // Send the GET request to ProPay's API
            $response = $this->client->get(env('PROFAC_BASE_REST_URL') . "/propayapi/EnhancedAccountPing/{$AccountNumber}", [
                'headers' => [
                    'Authorization' => 'Basic ' . env('PROFAC_AUTH_TOKEN'), // Use your ProPay API key
                    'X509Certificate' => env('PROFAC_X509'),
                    'Content-Type' => 'application/json',
                ]
            ]);

            // Decode the JSON response
            $responseBody = json_decode($response->getBody()->getContents(), true);

            return [
                'status' => 'success',
                'data' => $responseBody
            ];
        } catch (\Exception $e) {
            Log::error('ProPay Get Account Details Error: ' . $e->getMessage());
            throw new \Exception('Error occurred while getting account details: ' . $e->getMessage());
        }
    }
}
