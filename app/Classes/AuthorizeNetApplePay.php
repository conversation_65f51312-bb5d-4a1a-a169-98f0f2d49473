<?php

namespace App\Classes;

use Exception;
use Request;

use Monolog\Logger;
use Monolog\Handler\StreamHandler;

use App\Models\User;
use App\Models\Facility;
use App\Models\AuthorizeNetTransaction;
use App\Models\AuthNetType;
use App\Models\PaymentProfile;
use App\Models\UserCim as Cim;
use App\Models\MonthlyParkingCim;
use App\Models\MonthlyParkingUser;

use App\Exceptions\AuthorizeNetException;

use App\Classes\AuthorizeNet\Cim as CustomerProfile;
use App\Classes\AuthorizeNet\Transactions;
use App\Classes\AuthorizeNet\AuthorizeNetErrors;

use App\Services\LoggerFactory;

use net\authorize\api\constants\ANetEnvironment as AnetConstants;
use net\authorize\api\contract\v1 as AnetApi;
use net\authorize\api\controller as AnetController;
use net\authorize\api\controller\CreateCustomerProfileController;
use net\authorize\api\controller\GetCustomerPaymentProfileController;
use net\authorize\api\controller\GetCustomerProfileController;
use net\authorize\api\controller\GetUnsettledTransactionListController;

/**
 * Wrapper class for interactively with the oh-so-lovely AuthorizeNet SDK
 * Code examples follow:
 * Charging a card: https://github.com/AuthorizeNet/sample-code-php/blob/master/PaymentTransactions/charge-credit-card.php
 *
 * To charge a credit card, do the following:
 * $user = User::find($user_id); // or current user, whatever you need here
 * $authNet = new AuthorizeNet();
 * // Set transaction details
 * $authNet->setUser($user)->setCreditCard($number, $expiration, $code)->createTransaction($total, $description);
 * // Fire off transaction
 * try {
 *   $charge = $authNet->sendTransaction();
 * } catch (AuthorizeNetException $e) {
 *     // handle exception here
 * }
 *
 * // If you get to here the charge was successful. It's already logged, so you can return response or exit.
 */
class AuthorizeNetApplePay
{
    // Auth net transaction type constants
    const CAPTURE_TRANSACTION = 'authCaptureTransaction';
    const AUTH_TRANSACTION = 'authOnlyTransaction';
    const AUTH_DATA_DESCRIPTOR = "COMMON.APPLE.INAPP.PAYMENT";
    /**
     * Authorize.net response constants
    * see https://developer.authorize.net/api/reference/index.html#payment-transactions-charge-a-credit-card
    */
    const RESPONSE_OK = "1";
    const RESPONSE_DECLINED = "2";
    const RESPONSE_ERROR = "3";
    const RESPONSE_HELD = "4";

    // Payment types for description field in authorize.net monthly parking account transactions
    const PAYMENT_AUTO = 'Auto';
    const PAYMENT_ONE_TIME = 'One-time';
    const PAYMENT_MANUAL_ACH = 'Manual';

    public static $addressFields = ['first_name', 'last_name', 'address', 'city', 'state', 'zip', 'county', 'phone_number', 'company'];

    protected $merchantAuthentication;
    protected $authNetEnvironment;

    /**
     * Merchant reference ID for this transaction
     *
     * @var [type]
     */
    protected $refId;

    /**
     * Payment type to use for the transaction
     *
     * @var AnetAPI\PaymentType
     */
    protected $payment = null;

    /**
     * Customer information (id and email)
     *
     * @var [type]
     */
    protected $customerInfo;

    /**
     * [$creditCard description]
     *
     * @var AnetAPI\CreditCardType
     */
    protected $creditCard;

    /**
     * Bank account, if provided
     *
     * @var AnetAPI\BankAccountType
     */
    protected $bankAccount;

    /**
     * The Anet Order
     *
     * @var AnetAPI\OrderType()
     */
    protected $order;

    /**
     * Set to true if this is handling a reservation transaction
     *
     * @var boolean
     */
    protected $reservation = false;

    /**
     * Transaction type object
     *
     * @var AnetAPI\TransactionRequestType(
     */
    protected $transactionType;

    /**
     * Actual transaction request .
     *
     * @var AnetAPI\CreateTransactionRequest
     */
    protected $transaction;

    /**
     * The transaction response from Authorize.Net
     *
     * @var [type]
     */
    public $tresponse;

    /**
     * Billing address
     *
     * @var AnetApi\CustomerAddressType(
     */
    protected $billingAddress = null;

    /**
     * Instance of authorizeNetTransaction model that
     * represents this transaction
     *
     * @var AuthorizeNetTransaction
     */
    protected $authorizeNetTransaction;

    /**
     * Whether or not the current transaction has been sent
     *
     * @var Bool
     */
    protected $sent = false;

    /**
     * User that this charge is for
     *
     * @var App\Models\User
     */
    protected $user;

    /**
     * Monthly parking account we are managing.
     * Either this or user should be set, not both
     *
     * @var [type]
     */
    protected $monthlyParkingUser;

    /**
     * The facility we are using to send this request
     *
     * @var App\Models\Facility
     */
    protected $facility;

    /**
     * Amount to charge
     *
     * @var [type]
     */
    protected $total;

    /**
     * Description for this transaction
     *
     * @var [type]
     */
    protected $description;

    /**
     * Bole
     *
     * @var Bo
     */
    protected $anonymous;

    /**
     * Payment profile to use for this transaction, if applicable
     *
     * @var [type]
     */
    protected $paymentProfile;

    /**
     * Whether to authorize or capture
     *
     * @var string
     */
    protected $transactionMode;

    protected $log;
    protected $auth_data_descriptor;

    public function __construct()
    {
        // Set our default merchant authentication here
        $this->merchantAuthentication = new AnetApi\MerchantAuthenticationType();
        $this->merchantAuthentication->setName(env('AUTHORIZE_NET_LOGIN_APPLE_PAY'));
        $this->merchantAuthentication->setTransactionKey(env('AUTHORIZE_NET_TRANSACTION_KEY_APPLE_PAY'));

        $this->authNetEnvironment = env('AUTHORIZE_NET_LIVE_APPLE') ? AnetConstants::PRODUCTION : AnetConstants::SANDBOX;

        $this->creditCard = new AnetApi\CreditCardType();
        $this->bankAccount = new AnetApi\BankAccountType();
        $this->customerInfo = new AnetAPI\CustomerDataType();
        $this->order = new AnetApi\OrderType();
        $this->transactionType = new AnetAPI\TransactionRequestType();
        $this->transaction = new  AnetApi\CreateTransactionRequest();

        $this->transactionMode = self::CAPTURE_TRANSACTION;
        
        $this->auth_data_descriptor = self::AUTH_DATA_DESCRIPTOR;

        $logFormat = "[%datetime%][%level_name%][id:%id%][login:%login%][key:%key%][total:%total%][is_reservation:%reservation%][%description%] %message% %context% %extra%\n";
        $this->log = (new LoggerFactory)->setPath('logs/authorizenet')->createLogger(
            'authorizenet', $logFormat, function ($record) {
                $record['id'] = $this->authorizeNetTransaction->id;
                $record['total'] = $this->authorizeNetTransaction->total;
                $record['description'] = str_replace("\n", "", $this->authorizeNetTransaction->description);
                $record['reservation'] = $this->reservation;

                // Note - we need some insight into which account we are authenticating to, but we can't store the full
                // name/password here - this is a compromise
                $record['login'] = substr($this->merchantAuthentication->getName(), 0, 3);
                $record['key'] = substr($this->merchantAuthentication->getTransactionKey(), 0, 3);

                return $record;
            }
        );

        $this->setRefId();
    }

    public function getAuthentication()
    {
        return $this->merchantAuthentication;
    }

    public function auth()
    {
        $this->transactionMode = self::AUTH_TRANSACTION;
        return $this;
    }

    public function capture()
    {
        $this->transactionMode = self::CAPTURE_TRANSACTION;
        return $this;
    }

    /**
     * Set the reference ID
     *
     * @param [type] $id [description]
     */
    public function setRefId($id = false)
    {
        if (!$id) {
            $this->refId = 'ref' . time();
            return $this;
        }

        $this->refId = $id;
        return $this;
    }

    /**
     * Set the user for this transaction
     *
     * @param User   $user        [description]
     * @param number $total       [description]
     * @param string $description [description]
     */
    public function setUser($user)
    {
        if (is_a($user, 'App\Models\MonthlyParkingUser')) {
            return $this->setMonthlyParkingUser($user);
        }

        $this->monthlyParkingUser = null;
        $this->user = $user;

        $this->customerInfo->setId($user->id);
        $this->customerInfo->setEmail($user->email);

        $this->setRefId('ref' . $this->user->id . ':' . time());

        return $this;
    }

    /**
     * Set a monthly parking user to handle transacations for
     * These users have individual cims per monthly parking account and so need to be handled differently
     * from a regular user
     * Only a user or a monthly parking user can be set at one time
     */
    public function setMonthlyParkingUser(MonthlyParkingUser $monthlyUser)
    {
        $this->user = null;
        $this->monthlyParkingUser = $monthlyUser;

        $this->customerInfo->setId($monthlyUser->id);
        $this->customerInfo->setEmail($monthlyUser->user->email);
        $this->setRefId('ref' . $monthlyUser->id . ':' . time());

        if ($monthlyUser->facility) {
            $this->setFacility($monthlyUser->facility);
        }

        return $this;
    }

    /**
     * Set the facility to use for transactions
     * Facilities have different auth net accounts, and may or may not accept credit cards
     *
     * @param  Facility $facility The facility to set for this transaction
     * @param  array    $options  'use_facility_authnet' Set to false to use standard icon parking authorize.net creds
     * @return $this;
     */
    public function setFacility(Facility $facility)
    {
        $this->facility = $facility;

        // Throw exception if we don't have credentials for this facility
        if (!$this->reservation && (!$facility->authorize_net_transaction_key || !$facility->authorize_net_login)) {
            throw new AuthorizeNetException("No credentials available for {$facility->full_name}, cannot process payments.");
        }

        // Return if we are not in production
        if (!config('authorizenet.monthly_parking.live')) {
            return $this;
        }

        // Reset merchant authentication for this facility
        if (!$this->reservation) {
            $this->merchantAuthentication->setName(env('AUTHORIZE_NET_LOGIN_APPLE_PAY'));
            $this->merchantAuthentication->setTransactionKey(env('AUTHORIZE_NET_TRANSACTION_KEY_APPLE_PAY'));
        }
        return $this;
    }

    /**
     * Set appropriate authorize.net credentials for a reservation
     *
     * @return boolean [description]
     */
    public function isReservation($isReservation = true)
    {
        if (!$isReservation) {
            $this->reservation = false;
            $this->merchantAuthentication->setName(env('AUTHORIZE_NET_LOGIN_APPLE_PAY'));
            $this->merchantAuthentication->setTransactionKey(env('AUTHORIZE_NET_TRANSACTION_KEY_APPLE_PAY'));
            $this->authNetEnvironment = env('AUTHORIZE_NET_LIVE_APPLE') ? AnetConstants::PRODUCTION : AnetConstants::SANDBOX;
            return $this;
        }

        $this->reservation = true;
        $this->merchantAuthentication->setName(env('AUTHORIZE_NET_LOGIN_APPLE_PAY'));
        $this->merchantAuthentication->setTransactionKey(env('AUTHORIZE_NET_TRANSACTION_KEY_APPLE_PAY'));
        $this->authNetEnvironment = env('AUTHORIZE_NET_LIVE_APPLE') ? AnetConstants::PRODUCTION : AnetConstants::SANDBOX;
        return $this;
    }

    /**
     * Return the cim ID of the currently set user or monthly parking user
     * Useful for update and delete functions
     *
     * @return integer|boolean false if no user/cim, otherwise the ID of the cim
     */
    public function getUserCimId()
    {
        if ($this->user) {
            $this->user->load('cim'); // This is necessary, or laravel wont' find a newly created cim
            $cim = $this->user->cim;
            return $cim ? $cim->cim_id : false;
        }

        if ($this->monthlyParkingUser) {
            $cim = $this->monthlyParkingUser->cim;
            return $cim ? $cim->cim_id : false;
        }

        return false;
    }

    /**
     * Return the CIM of the set user or monthly parking user
     *
     * @return integer|boolean false if no user/cim, otherwise the ID of the cim
     */
    public function getUserCim()
    {
        $cimId = $this->getUserCimId();

        if (!$cimId) {
            return false;
        }

        if ($this->user) {
            return Cim::where('cim_id', $cimId)->first();
        }

        return MonthlyParkingCim::where('cim_id', $cimId)->first();
    }

    /**
     * Get the base user, even if we have a monthly parking user set
     *
     * @return App\Models\User
     */
    public function getUser()
    {
        if ($this->monthlyParkingUser) {
            return $this->monthlyParkingUser->user;
        }

        return $this->user;
    }

    /**
     * Get the user ID for the current user or monthly parking user
     *
     * @return [type] [description]
     */
    public function getUserId()
    {
        if (!$this->user && !$this->monthlyParkingUser) {
            return false;
        }

        return $this->user ? $this->user->id : $this->monthlyParkingUser->user->id;
    }

    /**
     * Get ID of currently set facility, or 0 if there is no facility set
     *
     * @return [type] [description]
     */
    public function getFacilityId()
    {
        return $this->facility ? $this->facility->id : 0;
    }

    /**
     * Set billing information
     *
     * @param array $billing Should contain keys 'first_name', 'last_name', 'address', 'city', 'state', 'zip'
     */
    public function setBillingAddress(array $billing)
    {
        $this->billingAddress = new AnetApi\CustomerAddressType();

        foreach (self::$addressFields as $field) {
            if (!isset($billing[$field])) {
                continue;
            }

            // Make the authorize.net SDK method name
            $setMethod = camel_case("set$field");
            $this->billingAddress->$setMethod($billing[$field]);
        }

        return $this;
    }

    /**
     * Set the credit card information for this payment
     * If you use this, do not call setBankAccount as well
     *
     * @param [type] $cardNumber   [description]
     * @param [type] $expiration   [description]
     * @param [type] $securityCode [description]
     */
    public function setCreditCard($cardNumber, $expiration, $securityCode = false)
    {
        if (strlen($cardNumber) > 19) {
            throw new AuthorizeNetException('Credit card number must be 19 characters or fewer.');
        }

        if (is_string($cardNumber)) {
            $cardNumber = str_replace(' ', '', $cardNumber);
        }

        if (is_string($expiration)) {
            $expiration = str_replace(' ', '', $expiration);
            $expiration = str_replace('/', '', $expiration);
        }

        $this->creditCard->setCardNumber($cardNumber);
        $this->creditCard->setExpirationDate($expiration);

        if ($securityCode) {
            $this->creditCard->setCardCode($securityCode);
        }

        $this->payment = new AnetApi\PaymentType(); // Reset in case we set another payment already
        $this->payment->setCreditCard($this->creditCard);
        return $this;
    }

    /**
     * Set the bank acocunt for this transaction
     * Either bank account or credit card should be set, not both
     *
     * @return $this
     */
    public function setBankAccount($accountType, $routing, $number, $name)
    {
        if (strlen($name) > 22) {
            throw new AuthorizeNetException('Bank Account name must be 22 characters or fewer.');
        }

        $this->bankAccount->setAccountType($accountType);
        $this->bankAccount->setRoutingNumber($routing);
        $this->bankAccount->setAccountNumber($number);
        $this->bankAccount->setNameOnAccount($name);

        $this->payment = new AnetApi\PaymentType(); // Reset in case we set another payment already
        $this->payment->setBankAccount($this->bankAccount);
        return $this;
    }

    /**
     * Set the payment profile for this transaction
     *
     * @param PaymentProfile $profile [description]
     */
    public function setPaymentProfile($paymentProfileId)
    {
        if (!$this->getUser()) {
            throw new AuthorizeNetException('Set user before calling AuthorizeNet::setPaymentProfile');
        }

        // Verify that the payment profile ID belongs to the current user
        $cim = new CustomerProfile();
        $cim->isReservation($this->reservation);

        if ($this->user) {
            $cim->setUser($this->user);
            $cim->verifyUserOwnsPayment($paymentProfileId); // throws an AuthorizeNetException if not valid
        } else {
            $cim->setMonthlyParkingUser($this->monthlyParkingUser)->verifyUserOwnsPayment($paymentProfileId);
        }

        // Set billing address name so we get the right name added to this transaction
        $profile = $cim->getPaymentProfile($paymentProfileId);
        if ($profile['address']) {
            $this->setBillingAddress($profile['address']);
        }

        if ($card = $profile['card']) {
            $this->setCreditCard($card['card_number'], $card['card_expiration'], null);
        }

        return $this->setApiPaymentProfile($paymentProfileId);
    }

    public function unsetPaymentProfile()
    {
        unset($this->paymentProfile);
        return $this;
    }

    protected function setApiPaymentProfile($paymentProfileId)
    {
        $paymentProfile = new AnetApi\PaymentProfileType();
        $paymentProfile->setPaymentProfileId($paymentProfileId);

        $profileToCharge = new AnetApi\CustomerProfilePaymentType();
        $profileToCharge->setCustomerProfileId($this->getUserCimId());
        $profileToCharge->setPaymentProfile($paymentProfile);

        $this->paymentProfile = $profileToCharge;

        return $this;
    }

    /**
     * Set up the transaction type and the transaction request
     *
     * @param  [type] $total       [description]
     * @param  [type] $description [description]
     * @return [type]              [description]
     */
    public function createTransaction($nonce, $total, $description, $invoice = false)
    {
        $this->total = $total;
        $this->description = $description;

        $this->order->setDescription($description);
        if ($invoice) {
            $this->order->setInvoiceNumber($invoice);
        }

        $op = new AnetApi\OpaqueDataType();
        $op->setDataDescriptor($this->auth_data_descriptor);
        $op->setDataValue($nonce);        
        $paymentOne = new AnetApi\PaymentType();
        $paymentOne->setOpaqueData($op);   
        
        // Set up the transaction type
        $this->transactionType->setTransactionType($this->transactionMode);
        $this->transactionType->setAmount($total);
        $this->transactionType->setPayment($paymentOne);  
        $this->transactionType->setCustomer($this->customerInfo);
        $this->transactionType->setOrder($this->order);
        if (isset($this->billingAddress)) {
                $this->transactionType->setBillTo($this->billingAddress);
        }      
        // Set up the actual transaction
        $this->transaction->setMerchantAuthentication($this->merchantAuthentication);
        $this->transaction->setRefId($this->refId);
        $this->transaction->setTransactionRequest($this->transactionType);
        return $this;
    }

    /**
     * Fire off the actual transaction request
     *
     * @return array $chargeDetail See $this->getChargeDetails()
     */
    
    public function executeTransaction()
    {
        $this->saveTransactionPreSend(); // Save before sending in case errors occur with authorize.net request

        $controller = new AnetController\CreateTransactionController($this->transaction);

        // Send request to production or sandbox based on environment set in .env
        $response = $controller->executeWithApiResponse($this->authNetEnvironment);
        $this->sent = true;

        if ($response === null) {
            throw new AuthorizeNetException('No response returned from Authorize.Net');
        }

        $this->tresponse = $response->getTransactionResponse();

        if (!$this->tresponse) {
            throw new AuthorizeNetException('Invalid response returned from Authorize.Net');
        }

        // Save the response, whether it was valid or invalid
        $this->saveTransactionPostSend();

        // Check our response code and make sure this is an ok transaction, otherwise throw an exception
        $this->isTransactionResponseCodeOk($this->tresponse->getResponseCode(), $response);

        return $this->getChargeDetails();
    }

  
    public function isTransactionResponseCodeOk($statusCode, $response)
    {
        if ($statusCode === self::RESPONSE_OK || $statusCode === self::RESPONSE_HELD) {
            return true;
        }

        $errors = $this->tresponse->getErrors();

        $errorText = $errors !== null && count($errors)
            ? $errors[0]->getErrorText()
            : $response->getMessages()->getMessage()[0]->getText();

        throw new AuthorizeNetException("Charge unsuccessful. " . $errorText);
    }


    /**
     * Manually run a .01 auth transaction against a credit card to verify that it is valid.
     * We need to do this manually because of the information we are sending to Authorize.Net -
     * using their built-in validation requires sending zip code information for some card types
     */
    public function validateCard($description)
    {
        $this->auth();

        try {
            $response = $this->createTransaction('0.01', $description)->executeTransaction();
        } catch (\Exception $e) {
            throw new AuthorizeNetException("Could not validate payment method. Processor returned error: {$e->getMessage()}");
        }

        // Void test transaction
        $transactions =  new Transactions();

        if ($this->monthlyParkingUser) {
            $transactions->setMonthlyParkingUser($this->monthlyParkingUser);
        } else {
            $transactions->setUser($this->getUser());
        }
        $transactions->setTransaction($this->authorizeNetTransaction)->void();

        $this->capture(); // Reset back to standard capture mode in case we are using this object to capture the transaction as well

        return $response;
    }

    /**
     * Return an array of details about the current charge
     *
     * @return [type] [description]
     */
    public function getChargeDetails()
    {
        return array_merge($this->getBasicChargeDetails(), $this->getCompletedChargeDetails());
    }

    /**
     *  the basic details about the charge. Can be called before a charge is sent.
     *
     * @return array
     */
    public function getBasicChargeDetails()
    {
        $details = [
            'sent' => $this->sent,
            'user_id' => $this->getUserId(),
            'total' => $this->total,
            'description' => $this->description,
            'ref_id' => $this->refId
        ];

        // Save payment profile ID if set
        if (isset($this->paymentProfile) && $this->paymentProfile) {
            $profile = PaymentProfile::where('payment_profile', $this->paymentProfile->getPaymentProfile()->getPaymentProfileId())->first();
            $details['payment_profile_id'] = $profile ? $profile->id : null;
        }

        if ($this->payment) {
            if ($payment = $this->payment->getCreditCard()) {
                $details['method'] = AuthorizeNetTransaction::METHOD_CARD;
                $details['payment_last_four'] = substr($payment->getCardNumber(), -4);
                $details['expiration'] = $payment->getExpirationDate();
            } else {
                $payment = $this->payment->getBankAccount();
                $details['method'] = AuthorizeNetTransaction::METHOD_BANK;
                $details['payment_last_four'] = substr($payment->getAccountNumber(), -4);
            }
        }

        if ($this->billingAddress) {
            $details['name'] = $this->billingAddress->getFirstName() . ' ' . $this->billingAddress->getLastName();
        }

        return $details;
    }

    /**
     * Given a transaction response, return the releveant details in an array
     *
     * @param  [type] $tresponse [description]
     * @return [type]            [description]
     */
    public function getCompletedChargeDetails()
    {
        if (!isset($this->tresponse) || !$this->tresponse) {
            return [];
        }

        $messages = $this->tresponse->getMessages();

        if ($messages && count($messages)) {
            $message = $messages[0]->getDescription();
        } else {
            $errors = $this->tresponse->getErrors();
            $message = count($errors) ? $errors[0]->getErrorText() : '';
        }

        return [
            'response_code' => $this->tresponse->getResponseCode(),
            'response_message' => $message,
            'auth_code' => $this->tresponse->getAuthCode(),
            'avs_code' => $this->tresponse->getAvsResultCode(),
            'cvv_code' => $this->tresponse->getCvvResultCode(),
            'cavv_code' => $this->tresponse->getCavvResultCode(),
            'anet_trans_id' => $this->tresponse->getTransId(),
            'anet_trans_hash' => $this->tresponse->getTransHash(),
            'card_type' => $this->tresponse->getAccountType()
        ];
    }

    /**
     * Return the saved auth net transaction
     *
     * @return [type] [description]
     */
    public function getTransaction()
    {
        return $this->authorizeNetTransaction;
    }

    /**
     * Save the transaction before sending
     *
     * @return [type] [description]
     */
    protected function saveTransactionPreSend()
    {
        $this->authorizeNetTransaction = new AuthorizeNetTransaction();
        $this->authorizeNetTransaction->fill($this->getBasicChargeDetails());
        $this->authorizeNetTransaction->ip_address = Request::ip();

        $user = $this->getUser();
        $this->authorizeNetTransaction->anonymous = $user->anon;

        // Set type
        $type = AuthNetType::where(
            [
            'type' => $transactionType = $this->transactionType->getTransactionType()
            ]
        )->first();

        if ($type) {
            $this->authorizeNetTransaction->anet_type_id = $type->id;
        }

        $this->authorizeNetTransaction->save();

        $this->log->info("About to send transaction.");

        return $this->authorizeNetTransaction;
    }

    /**
     * Save transaction details post send
     *
     * @param  $details array Array of details about this transaction
     * @return App\Models\AuthorizeNetTransaction
     */
    protected function saveTransactionPostSend()
    {
        // Try to find an authorize net transaction with our ref ID if we don't have one set already
        if (!isset($this->authorizeNetTransaction) || !$this->authorizeNetTransaction) {
            $this->authorizeNetTransaction = AuthorizeNetTransaction::where('ref_id', $this->refId)->first();
        }

        // Could not find previous transaction to update, create new one
        if (!$this->authorizeNetTransaction) {
            $this->authorizeNetTransaction = new AuthorizeNetTransaction();
        }

        $details = $this->getChargeDetails();

        $this->authorizeNetTransaction->fill($details);
        $this->authorizeNetTransaction->save();

        $this->log->info("Sent transaction, received response {$this->authorizeNetTransaction->response_code}.");

        return $this->authorizeNetTransaction;
    }

    /**
     * Given a response from authnet, check that it is valid and throw an exception
     * if not
     *
     * @param  [type] $response [description]
     * @return [type]           [description]
     */
    protected function checkAuthNetResponse($response)
    {
        // No response from authorize.net
        if (!$response || $response === null) {
            throw new AuthorizeNetException('No response returned from Authorize.Net create customer profile request.');
        }

        // Not ok response
        if ($response->getMessages()->getResultCode() !== 'Ok') {
            $errorMessages = $response->getMessages()->getMessage();
            $message = AuthorizeNetErrors::transformErrorMessage($errorMessages[0]->getCode(), $errorMessages[0]->getText());
            throw new AuthorizeNetException($message);
        }
    }

    // Give a single name, split into first name last name array
    public static function splitName($name)
    {
        $name = explode(' ', $name);
        return [
            'first_name' => array_shift($name),
            'last_name' => implode(' ', $name)
        ];
    }
}
