<?php

namespace App\Classes;

use App\Models\PromoCode;
use App\Models\PromoUsage;
use App\Models\Promotion;
use App\Models\User;
use App\Models\PromotionUser;
use App\Exceptions\ApiGenericException;
use App\Services\Mailers\UserMailer;
use App\Models\PromotionVerification;
use App\Classes\MagicCrypt;
use App\Services\LoggerFactory;
use Auth;
use DB;
use Exception;
use Carbon\Carbon;
use App\Models\Ticket;
use App\Models\ParkEngage\TicketExtend;
use App\Models\ParkEngage\UserFacility;
use App\Models\Rate;
use App\Models\Reservation;
use App\Models\Facility;
use App\Models\OverstayTicket;
use App\Http\Helpers\QueryBuilder;
use App\Models\PermitRequest;
use App\Models\PermitVehicle;
use App\Models\PromotionFacility;
use App\Models\PromotionDay;

class LatestPromoCodeLib
{

    public static $is_promocode_valid = true;
    public static $is_promocode_success = false;
    public static $coupon_length = 8;
    public static $promo_check_response = 'Promo Code is valid';
    public static $confirmation_code = 0;
    public static $is_opt_out = 0;
    public static $user_type = 1;
    const FULL_DAY_HOUR_VAL = 24;


    public static function generatePromoCodes($promotion)
    {
        $promoCode = [
            'promotion_id' => $promotion->id,
            'promo_type_id' => $promotion->promo_type_id,
            'channel_partner_id' => $promotion->channel_partner_id,
            'valid_from' => $promotion->valid_from,
            'valid_to' => $promotion->valid_to,
            'is_expired' => 0,
            'status' => 1,
            'max_lifetime_discount' => $promotion->max_lifetime_discount,
        ];

        switch ($promotion->promo_type_id) {
            case 1:
                $promoCode['usage'] = $promotion->usage;
                $promoCode['discount_type'] = $promotion->discount_type;
                $promoCode['discount_value'] = $promotion->discount_value;
                $codeIdentifier = 1;
                break;
            case 2:
                $promoCode['discount_type'] = $promotion->discount_type;
                $promoCode['discount_value'] = $promotion->discount_value;
                $codeIdentifier = 2;
                break;
            case 3:
                $promoCode['base_price'] = $promotion->base_price;
                $promoCode['promo_price'] = $promotion->promo_price;
                $codeIdentifier = 3;
                break;
            default:
                // code...
                break;
        }

        try {
            if ($promotion->couponscode == '') { //couponscode
                $promoCodes = self::randomPromoCodes($promoCode, $promotion->total_coupons);
            } else {
                $promoCodes = $promoCode;
                $promoCodes['promocode'] = $promotion->couponscode;
                $promoCodes['created_at'] = date('Y-m-d H:i:s');
                $promoCodes['updated_at'] = date('Y-m-d H:i:s');
            }
            $result = self::storePromoCodes($promoCodes);
        } catch (Exception $e) {
            // Remove the Promotion Entry ...
            $promotion->delete();
            throw new ApiGenericException($e->getMessage());
        }

        if (!$result) {
            throw new ApiGenericException('Error Occured, Promo Codes Could Not Be Generated');
        }

        return [
            'success' => true,
            'promotion' => $promotion,
            'generated_promos' => count($promoCodes),
        ];
    }

    public static function randomPromoCodes($promoCodeStructure, $total_coupons)
    {
        $promoCodes = [];
        $symbols = '1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $promoCodeLength = PromoCodeLib::$coupon_length - 1;
        $symbols_length = strlen($symbols) - 1;

        for ($p = 0; $p < $total_coupons; $p++) {
            $randomPromoCode = '';
            for ($i = 0; $i < $promoCodeLength; $i++) {
                $n = rand(0, $symbols_length);
                if ($i == 1) {
                    $randomPromoCode .= $promoCodeStructure['promo_type_id'];
                }
                $randomPromoCode .= $symbols[$n];
            }
            $promoCodeStructure['promocode'] = $randomPromoCode;
            $promoCodeStructure['created_at'] = date('Y-m-d H:i:s');
            $promoCodeStructure['updated_at'] = date('Y-m-d H:i:s');
            $promoCodes[] = $promoCodeStructure;
        }

        return $promoCodes;
    }

    public static function storePromoCodes($promoCodes)
    {
        return PromoCode::insert($promoCodes);
    }

    public static function validatePromoCode($request)
    {

        $promoCode = PromoCode::where('promocode', $request->promocode)->first();

        if (isset($request->user_id) && strpos(strtolower($request->promocode), config('loyalty.promocode_key')) !== false) {
            $user = User::where('id', $request->user_id)->where('is_loyalty', 1)->first();
            if (!$user) {
                throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.');
            }
        }

        if (isset($request->email) && $request->email != ''  && (strpos(strtolower($request->promocode), config('loyalty.promocode_key')) !== false)) {
            $user = User::where('email', $request->email)->where('is_loyalty', 1)->first();
            if (!$user) {
                throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.');
            }
        }

        if (!$promoCode) {
            throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.');
        }

        $currentDate = date('Y-m-d');

        $promotion = Promotion::where('id', $promoCode->promotion_id)->first();

        if (!$promotion) {
            throw new ApiGenericException('Please enter a valid Promotion Code. Please note that a prepaid voucher is not a Promo Code.');
        }
        if ($promotion->status != 1) {
            throw new ApiGenericException('Sorry, the validation code you entered is not applicable.');
        }

        if ($promotion->is_validity_check == 1 && $currentDate < $promotion->valid_from) {
            self::$is_promocode_valid = false;
            self::$promo_check_response = 'Sorry, the validation code you entered is not applicable.';
            throw new ApiGenericException('Sorry, the validation code you entered is not applicable.');
        }

        if ($promotion->is_validity_check == 1 && $currentDate > $promotion->valid_to) {
            self::$is_promocode_valid = false;
            self::$promo_check_response = 'Sorry! but it looks like this promotion has already expired.';
            throw new ApiGenericException('Sorry! but it looks like this promotion has already expired.');
        }

        if (self::$is_promocode_valid) {
            switch ($promoCode->promo_type_id) {
                case 1:
                    self::validateStaticPromoCode($request, $promoCode);
                    break;
                case 2:
                    self::validateSingleUsePromoCode($request, $promoCode);
                    break;
                case 3:
                    self::validatePromoValuePromoCode($request, $promoCode);
                    break;
                default:
                    // code...
                    break;
            }
        }
        //get discount mx limit
        $percentageDiscount = isset($promotion->percentage_off_discount) ? $promotion->percentage_off_discount : 0;
        $dollar_off_discounts = isset($promotion->dollar_off_discount) ? $promotion->dollar_off_discount : 0;

        return [
            'is_promocode_valid' => self::$is_promocode_valid,
            'promocode' => $promoCode,
            'message' => self::$promo_check_response,
            'is_opt_out' => self::$is_opt_out,
            'is_promocode_success' => self::$is_promocode_success,
            'percentage_max_discount' => $percentageDiscount,
            'dollar_min_discount' => $dollar_off_discounts,
            'user_type_promotion'  => isset($promotion->user_type) ? $promotion->user_type : '0',
            'minimun_amount_error_msg' => "Sorry, this validation code is valid only for transactions of $" . $dollar_off_discounts . " or more.",
            'max_percentage_amount_error_msg' => "You have reached the maximum discount on this promotion."
        ];


        // 1. Check If Promocode Exists or Not [DONE]
        // 2. Check If Current Date is Matching With PromoCode Validity or Not [DONE]
        // 3. Check Promocode Type [DONE]
        //     4.1 If Static Use Code
        //         1. Check If User Id Is Provided or Not [DONE]
        //         2. Check If User Max Usage Limit Is Not Exceeded or Not [DONE]
        //     4.2 If Single Use Code
        //         1. Check If Coupon Is Expired Or Not [DONE]
        //     4.3 If Promo Value Promo Code [DONE]
    }

    public static function validatePromoCodeThirdPartyOld($request)
    {
        $db_logs_data['request'] = json_encode($request->all());
        $db_logs_data['api_type'] = 'apply';
        $db_logs_data['created_at'] = Carbon::now();
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/partner_promocode_api_logs')->createLogger('apply_api_logs');

        \Log::info("validatePromoCodeThirdParty");
        \Log::info(json_encode($request->all()));
        try {
            $decrypt = base64_decode($request->encrypt);
            $decrytArray = json_decode($decrypt);

            $facilityId = !empty($decrytArray->facility_id) ? $decrytArray->facility_id : $request->facility_id;

            $log->info("Request: " . $db_logs_data['request']);

            $currentDate = date('Y-m-d');

            $client = DB::table('oauth_clients')->where('secret', $request->client_id)->orderBy('created_at', 'desc')->first();
            if (!$client) {
                throw new ApiGenericException('Promocode not authorized for user', 500);
            }
            $partner_id = $client->partner_id;

            //promcode change by trapti mam check with partner id for same promocode

            // $promoCode = PromoCode::where('promocode', $request->promocode)->first();

            // if (!$promoCode) {
            //     throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.', 500);
            // }

            $promoCode = PromoCode::select('promo_codes.*', 'promotions.id as promotion_id')
                ->leftJoin('promotions', 'promo_codes.promotion_id', '=', 'promotions.id')
                ->where('promo_codes.promocode', $request->promocode)
                ->where('promotions.owner_id', $partner_id)
                ->first();

            if (!$promoCode) {
                throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.', 500);
            }

            $promotion = Promotion::where('id', $promoCode->promotion_id)->where('owner_id', $partner_id)->first();
            //check for hourlypromo code validation for hours applicable

            if (isset($request->rateHours) && !empty($request->rateHours) && !empty($promotion) && $promotion->discount_type == 'hours' && $promotion->applicable_to_hours == 1) {
                if ($request->rateHours < $promotion->vaild_hours) {
                    $hours =  self::convertHoursDynamic($promotion->vaild_hours);
                    throw new ApiGenericException('This validation code is only valid for booking of  ' . $hours . ' or more.', 500);
                }
            }
            // Alka: 07 Nov 2024, PIMS-10950
            if (isset($request->facility_id) || $facilityId) { // Lokesh: PIMS-11510 : 19 Nov 2024
                if (empty($promotion)) {
                    throw new ApiGenericException('Sorry, the code you entered is not applicable for this partner or facility.', 500);
                }
                $promocodeFacilityId = PromotionFacility::where('promotion_id', $promotion->id)->pluck('facility_id')->toArray();

                if (isset($promocodeFacilityId) && !empty($promocodeFacilityId) && !in_array($facilityId, $promocodeFacilityId)) {
                    throw new ApiGenericException('Sorry, the validation code you entered is not valid for this facility.', 500);
                }
            }
            // End

            //new changes to handle promo code issue in extend screen
            $ticketid = "";
            $totalUsedAmountByTicket = 0;
            $extendCase = 0;
            $ticketIdforHours = "";
            $get_ticket_id = 0;
            $promoin = '0';
            $promovalue = 0;
            $maxvalue = 0;

            if (!empty($request->ticket_number)) {
                $ticketid = Ticket::where('ticket_number', $request->ticket_number)->where('partner_id', $partner_id)->first();
                if (!empty($ticketid)) {
                    $getFacilityType = Facility::where('id', $ticketid->facility_id)->pluck('is_gated_facility');
                }
                //@ check for gated promo code overstay case
                if ($getFacilityType[0] === "1") {
                    $getAmountForExtent = OverstayTicket::where('ticket_id', $ticketid->id)
                        ->sum('discount_amount');
                    if ($getAmountForExtent > 0) {
                        $totalUsedAmountByTicket = $getAmountForExtent + $ticketid->discount_amount;
                    } else {
                        $totalUsedAmountByTicket = $ticketid->discount_amount;
                    }
                    $ticketIdforHours =  $ticketid->id;
                    $extendCase = 1;
                } else {
                    // for  ungated extend screnn
                    $getAmountForExtent = TicketExtend::where('ticket_id', $ticketid->id)
                        ->sum('discount_amount');
                    if ($getAmountForExtent > 0) {
                        $totalUsedAmountByTicket = $getAmountForExtent + $ticketid->discount_amount;
                    } else {
                        $totalUsedAmountByTicket = $ticketid->discount_amount;
                    }
                    $ticketIdforHours =  $ticketid->id;
                    $extendCase = 1;
                }
            }

            // This Section of code moved here from old class :: Start 

            // check secret for Rm
            $isRm = User::find($partner_id);
            if (isset($isRm->user_type) && $isRm->user_type == '12') {
                $promotion = Promotion::where('id', $promoCode->promotion_id)->where('rm_id', $partner_id)->first();
                if (!$promotion) {
                    $promotion = Promotion::where('id', $promoCode->promotion_id)->where('owner_id', $isRm->created_by)->first();
                }
            } else {
                if (isset($promotion->rm_id) && $promotion->rm_id > 0) {
                    if ($request->facility_id != '') {
                        $rmFacility = UserFacility::where("facility_id", $request->facility_id)->where("user_id", $promotion->rm_id)->first();
                        if (!$rmFacility) {
                            throw new ApiGenericException('Please enter valid promocode.', 500);
                        }
                    }
                }
            }
            // !!!! close   
            //end
            if (!$promotion) {
                throw new ApiGenericException('Promocode not authorized for user', 500);
            }

            $is_guest = isset($request->is_guest) ? $request->is_guest : 0;
            if ($is_guest && $promotion->user_type == 0) {
                throw new ApiGenericException('Promocode not authorized for guest users', 500);
            }

            if (isset($request->phone) || isset($request->license_plate)) {
                if ($promotion->promotion_validation_type == '1') {
                    $request->request->add(['email' => $request->phone]);
                } elseif ($promotion->promotion_validation_type == '2') {
                    $request->request->add(['email' => $request->license_plate]);
                }
            }
            if ($promotion->specific_user_type == 1 && !(self::checkPromoEmailValidity($promotion->id, $request->email))) {
                throw new ApiGenericException('Promocode not authorized for user', 500);
            }

            if ($promotion->status != 1) {
                throw new ApiGenericException('Sorry, the validation code you entered is not applicable.', 500);
            }

            if ($promotion->is_validity_check == 1 && $currentDate < $promotion->valid_from) {
                throw new ApiGenericException('Promocode has not been activated yet', 500);
            }

            // remove is_promocode_valid and promo_check_response
            if ($promotion->is_validity_check == 1 && $currentDate > $promotion->valid_to) {
                throw new ApiGenericException('Sorry! but it looks like this promotion has already expired', 500);
            }
            if (isset($request->phone) && !empty($request->phone)) {
                $countryCode = QueryBuilder::appendCountryCode($request->phone);
                $request->request->add(['phone' => $countryCode]);
            }
            switch ($promoCode->promo_type_id) {
                case 1:
                    // self::validateStaticPromoCode($request, $promoCode);
                    self::checkPromoCodeUsageForEmail($partner_id, $promoCode, $request, $promotion, $ticketid);
                    break;
                case 2:
                    self::validateSingleUsePromoCodeForEmail($partner_id, $promoCode, $request->email, $promotion, $ticketid, $request->reservation_id, $request);
                    break;
                case 3:
                    self::validatePromoValuePromoCode($request, $promoCode);
                    break;
                default:
                    // code...
                    break;
            }

            //get discount mx limit
            $percentageDiscount = isset($promotion->percentage_off_discount) ? $promotion->percentage_off_discount : 0;
            $dollar_off_discounts = isset($promotion->dollar_off_discount) ? $promotion->dollar_off_discount : 0;

            $amountBeforeDiscount = $amount = $request->amount;
            // dd('Promocode', $promoCode, $request->all(), in_array($client->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT')));
            $netParkingAmount = $taxFee = $processingFee = $additionalFee = $surchargeFee = 0;
            if (in_array($client->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                // if ($promotion->is_tax_applicable == 1) {
                //     if (isset($request->tax_amount) && $request->tax_amount > 0) {
                //         $amountBeforeDiscount += $request->tax_amount;
                //     }
                //     if (isset($request->processing_fee) && $request->processing_fee > 0) {
                //         $amountBeforeDiscount += $request->processing_fee;
                //     }
                // }
            } else {
                // Old Flow.
                if ($promotion->is_tax_applicable == 1) {
                    if (isset($request->tax_amount) && $request->tax_amount > 0) {
                        $amountBeforeDiscount = $amount += $request->tax_amount;
                    }
                    if (isset($request->processing_fee) && $request->processing_fee > 0) {
                        $amountBeforeDiscount = $amount += $request->processing_fee;
                    }
                }
            }

            \Log::info("is_tax_applicable");
            \Log::info("Amount :  {$amount} , amountBeforeDiscount : {$amountBeforeDiscount} ");

            $discount_in_dollar = 0;
            $discount_in_hours = 0;
            $remaning_payable_amount = 0;
            $max_percentage_discount_exceeded = false;
            $max_lifetime_discount_limit = $promotion->max_lifetime_discount;

            if ($promoCode->discount_type === 'percentage') {

                $discount_in_dollar = ($promoCode->discount_value / 100) * $amount;
                //check first if due to check maximum amount for extend case
                $addAppliedAmountOnRate = $totalUsedAmountByTicket + $amount;

                if ($addAppliedAmountOnRate > $percentageDiscount && $extendCase == 1) {
                    $max_percentage_discount_exceeded = true;
                    if ($percentageDiscount > $totalUsedAmountByTicket) {
                        $discount_in_dollar = $percentageDiscount - $totalUsedAmountByTicket;
                    } else {
                        $discount_in_dollar = 0.00;
                    }
                }

                if ($addAppliedAmountOnRate > $percentageDiscount && $extendCase == 1) {
                    $max_percentage_discount_exceeded = true;
                    if ($percentageDiscount > $totalUsedAmountByTicket) {
                        $discount_in_dollar = $percentageDiscount - $totalUsedAmountByTicket;
                    } else {
                        $discount_in_dollar = 0.00;
                    }
                }
                //end if
                if ($discount_in_dollar > $percentageDiscount) {
                    $max_percentage_discount_exceeded = true;
                    $discount_in_dollar = $percentageDiscount;
                }
                //if  single promocode enable and apply one time then 
                if ($promoCode->promo_type_id == 2) {
                    $promoUsage = PromoUsage::where(['user_id' => $partner_id, 'promocode' => $promoCode->promocode])->get();
                    $totalUsage = count($promoUsage);
                    if ($totalUsage >= 1) {
                        $discount_in_dollar = 0.00;
                    }
                }

                $promoin = '1';
                $promovalue = $promoCode->discount_value;
                $maxvalue = $promotion->percentage_off_discount;

                // Calculate Fees for VP : PIMS - 14210
                $netParkingAmount = ($amountBeforeDiscount - $discount_in_dollar);
                \Log::info("Amount :  {$amount} - amountBeforeDiscount : {$amountBeforeDiscount} - netParkingAmount : {$netParkingAmount}");
                if (in_array($client->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                    // $taxFee = $processingFee = $additionalFee = $surchargeFee = 0;
                    if ($promotion->is_tax_applicable == 1 && $netParkingAmount > 0) {
                        if (isset($request->tax_amount) && $request->tax_amount > 0) {
                            $taxFee = ($promoCode->discount_value / 100) * $request->tax_amount;
                        }
                        if (isset($request->processing_fee) && $request->processing_fee > 0) {
                            $processingFee += ($promoCode->discount_value / 100) * $request->processing_fee;
                        }
                    }
                    // Need to update in RevPass Case
                    $amountBeforeDiscount += ($taxFee + $processingFee);
                }
                // Close Calculate Fees for VP : PIMS - 14210

                //end promocode single uses
            } elseif ($promoCode->discount_type === 'hours') {
                $discount_in_hours = $promoCode->discount_value;
                //get ticket id for handle hours

                $get_ticket_id = $ticketIdforHours;
                //if  single promocode enable and apply one time then 

                if ($promoCode->promo_type_id === "2") {
                    $promoUsage = PromoUsage::where(['user_id' => $partner_id, 'promocode' => $promoCode->promocode])->get();
                    $totalUsage = count($promoUsage);
                    if ($totalUsage >= 1) {
                        $discount_in_hours = 0;
                    }
                }
                $promoin = '2';
                $promovalue = $discount_in_hours;
                $maxvalue = 0;
                //end promocode single uses
            } elseif ($promoCode->discount_type == 'discount_rate') {

                $length = $request->length;

                $catgoryId = $promoCode->discount_value;
                $getBoardRates = Rate::where('rate_type_id', 12)
                    ->where('promo_category_id', $catgoryId)
                    ->get();

                $matchingRate = null;
                $nearestRate = null;
                $nearestDiff = PHP_INT_MAX;
                // dd($getBoardRates);
                foreach ($getBoardRates as $rate) {
                    if ($length >= $rate->min_stay && $length <= $rate->max_stay) {
                        $matchingRate = $rate;
                        break;
                    }

                    $diff = min(abs($length - $rate->min_stay), abs($length - $rate->max_stay));
                    if ($diff < $nearestDiff) {
                        $nearestDiff = $diff;
                        $nearestRate = $rate;
                    }
                }

                if ($promoCode->promo_type_id === "2") {
                    $promoUsage = PromoUsage::where(['user_id' => $partner_id, 'promocode' => $promoCode->promocode])->get();
                    $totalUsage = count($promoUsage);
                    if ($totalUsage >= 1) {
                        $discount_in_hours = 0;
                    }
                }
            } else {
                if (!isset($request->extend_request)) {
                    if ($amount >= $dollar_off_discounts) {   // check for min txn. value. 
                        $discount_in_dollar = $promoCode->discount_value;
                        if ($discount_in_dollar >= $amount) {
                            $discount_in_dollar = $amount;
                        }
                    } else {
                        throw new ApiGenericException("Sorry, this validation code is valid only for transactions of $" . $dollar_off_discounts . " or more.", 500);
                    }
                }

                $promoin = '3';
                $promovalue = $discount_in_dollar;
                $maxvalue = 0;
            }

            if ($promotion->specific_user_type == 1 && $max_lifetime_discount_limit > 0) {
                $available_discount_limit = self::getAvailableDiscountLimit($partner_id, $promoCode, $promotion, $request->email);
                if ($available_discount_limit === 0) {
                    throw new ApiGenericException("You have already reached the maximum discount on this promotion.", 500);
                }
            }


            self::sanitizePromocodeData($promoCode);

            $max_lifetime_discount_exceeded = false;
            if ($promotion->specific_user_type == 1) {
                if ($max_lifetime_discount_limit > 0 && $available_discount_limit < $discount_in_dollar) {
                    $max_lifetime_discount_exceeded = true;
                    $max_percentage_discount_exceeded = false;
                    $discount_in_dollar = $available_discount_limit;
                    $message = 'You have reached the maximum overall discount allowed on this promocode';
                } else if ($max_percentage_discount_exceeded) {
                    $message = "You have reached the maximum discount per transaction on this promocode";
                } else {
                    $message = "Promocode successfully applied";
                    if ($partner_id == 54682 || $partner_id == "54682") {
                        $message = "Coupon successfully applied";
                    }
                }
            } else {
                if ($max_percentage_discount_exceeded) {
                    $message = "You have reached the maximum discount per transaction on this promocode";
                } else {
                    $message = "Promocode successfully applied";
                    if ($partner_id == 54682 || $partner_id == "54682") {
                        $message = "Coupon successfully applied";
                    }
                }
            }

            $discount_in_dollar = (float)number_format((float)$discount_in_dollar, 2, '.', '');

            //$payable_amount = (($request->amount - $discount_in_dollar) <= 0) ? 0.00 : ($request->amount - $discount_in_dollar);
            // $payable_amount = (($amount - $discount_in_dollar) <= 0) ? 0.00 : ($amount - $discount_in_dollar);
            $payable_amount = (($amountBeforeDiscount - $discount_in_dollar) <= 0) ? 0.00 : ($amountBeforeDiscount - $discount_in_dollar);
            $response  = [
                'is_promocode_valid' => true,
                'promocode' => $promoCode,
                'message' => $message,
                'discount_in_dollar' => $discount_in_dollar,
                'max_percentage_discount' => $percentageDiscount,
                'min_amount_required_for_dollar_discount' => $dollar_off_discounts,
                'max_lifetime_discount' => $max_lifetime_discount_limit,
                'is_max_lifetime_discount_exceeded' => $max_lifetime_discount_exceeded,
                'is_max_percentage_discount_exceeded' => $max_percentage_discount_exceeded,
                'is_tax_applicable' => $promotion->is_tax_applicable,
                'discount_in_hours' => $discount_in_hours,
                'get_ticket_id' => $get_ticket_id,
                'no_of_times' => $promotion->no_of_times,
                'applicable_to_hours' => $promotion->applicable_to_hours,
                'first_hours_apply' => $promotion->first_hours_apply,
                'promoin' => $promoin,
                'promovalue' => $promovalue,
                'maxvalue' => $maxvalue,
                'net_parking_amount' => $netParkingAmount,
                'tax_fee' => $taxFee,
                'processing_fee' => $processingFee,
                'additional_fee' => $additionalFee,
                'surcharge_fee' => $surchargeFee
            ];

            // Vijay 
            $alreadyFeeCharged = 0;
            $status = 202;
            if (isset($request->reservation_id) && !empty($request->reservation_id)) {
                $reservation = Reservation::find($request->reservation_id);
                if ($reservation->facility_id == config('parkengage.ROC_FACILITY')) {
                    $status = 200;
                }
                if (isset($request->reservation_mode) && $request->reservation_mode == '2')
                    $alreadyFeeCharged = $reservation->tax_fee + $reservation->processing_fee;

                if ($promotion->is_tax_applicable == 0) {
                    if (isset($request->is_extend) && $request->is_extend) {
                    } else {
                        $payable_amount = $payable_amount + ($request->tax_amount + $request->processing_fee) - $alreadyFeeCharged;
                    }
                }
            } else {
                if (isset($request->facility_id) && $request->facility_id == config('parkengage.ROC_FACILITY')) {
                    $status = 200;
                }
                // $payable_amount = $payable_amount + ($request->tax_amount + $request->processing_fee) - $alreadyFeeCharged;
                if ($promotion->is_tax_applicable == 0) {
                    $payable_amount = $payable_amount + ($request->tax_amount + $request->processing_fee) - $alreadyFeeCharged;
                }
            }
            $response['payable_amount'] = round($payable_amount, 2);

            // dd($promotion->is_tax_applicable == 0, $promotion->is_tax_applicable, $payable_amount, $response['discount_in_dollar'], $alreadyFeeCharged);

            \Log::info("is_tax_applicable check resposen ");
            \Log::info(json_encode($response));
            $db_logs_data['response'] = json_encode($response);

            $log->info("Response: " . $db_logs_data['response']);

            DB::table('partner_promocode_api_logs')->insert($db_logs_data);

            if ($max_percentage_discount_exceeded || $max_lifetime_discount_exceeded) {
                $response["max_discount_limit_exceeded"] = 1;
                return response()->json($response, $status);
            }
            return response()->json($response, 200);
        } catch (Exception $e) {
            $log->error("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
            $db_logs_data['is_error'] = 1;
            $db_logs_data['error_message'] = $e->getMessage();
            DB::table('partner_promocode_api_logs')->insert($db_logs_data);
            throw $e;
        }
    }

    public static function validatePromoCodeThirdParty($request)
    {
        $db_logs_data['request'] = json_encode($request->all());
        $db_logs_data['api_type'] = 'apply';
        $db_logs_data['created_at'] = Carbon::now();
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/partner_promocode_api_logs')->createLogger('apply_api_logs');
        \Log::info("Performance check point 1");
        \Log::info("validatePromoCodeThirdParty");
        \Log::info(json_encode($request->all()));
        \Log::info("Performance check point 2");
        try {
            $decrypt = base64_decode($request->encrypt);
            $decrytArray = json_decode($decrypt);

            $facilityId = !empty($decrytArray->facility_id) ? $decrytArray->facility_id : $request->facility_id;

            $log->info("Request: " . $db_logs_data['request']);

            $currentDate = date('Y-m-d');

            $client = DB::table('oauth_clients')->where('secret', $request->client_id)->orderBy('created_at', 'desc')->first();
            if (!$client) {
                throw new ApiGenericException('Promocode not authorized for user', 500);
            }
            $partner_id = $client->partner_id;
            \Log::info("Performance check point 3");
            //promcode change by trapti mam check with partner id for same promocode

            // $promoCode = PromoCode::where('promocode', $request->promocode)->first();

            // if (!$promoCode) {
            //     throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.', 500);
            // }

            $promoCode = PromoCode::select('promo_codes.*', 'promotions.id as promotion_id')
                ->leftJoin('promotions', 'promo_codes.promotion_id', '=', 'promotions.id')
                ->where('promo_codes.promocode', $request->promocode)
                ->where('promotions.owner_id', $partner_id)
                ->first();

            if (!$promoCode) {
                throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.', 500);
            }
            \Log::info("Performance check point 4");
            $promotion = Promotion::where('id', $promoCode->promotion_id)->where('owner_id', $partner_id)->first();
            //check for hourlypromo code validation for hours applicable
            // $promotion->applicable_to_hours  [0,1,2]  // 0 => less than, 1 => Equal/greator, 2 => Equal;
            if (isset($request->rateHours) && !empty($request->rateHours) && !empty($promotion) && $promotion->discount_type == 'hours' && in_array($promotion->applicable_to_hours, [1, 2])) {
                if ($request->rateHours < $promotion->vaild_hours) {
                    $hours =  self::convertHoursDynamic($promotion->vaild_hours);
                    throw new ApiGenericException('This validation code is only valid for booking of  ' . $hours . ' or more.', 500);
                }
            }
            \Log::info("Performance check point 5");
            // Alka: 07 Nov 2024, PIMS-10950
            if (isset($request->facility_id) || $facilityId) { // Lokesh: PIMS-11510 : 19 Nov 2024
                if (empty($promotion)) {
                    throw new ApiGenericException('Sorry, the code you entered is not applicable for this partner or facility.', 500);
                }
                $promocodeFacilityId = PromotionFacility::where('promotion_id', $promotion->id)->pluck('facility_id')->toArray();

                if (isset($promocodeFacilityId) && !empty($promocodeFacilityId) && !in_array($facilityId, $promocodeFacilityId)) {
                    throw new ApiGenericException('Sorry, the validation code you entered is not valid for this facility.', 500);
                }
            }

            $facility = Facility::find(isset($request->facility_id) ? $request->facility_id : $facilityId);
            \Log::info("Performance check point 6");
            // End

            //new changes to handle promo code issue in extend screen
            $ticketid = "";
            $totalUsedAmountByTicket = 0;
            $extendCase = 0;
            $ticketIdforHours = "";
            $get_ticket_id = 0;
            $promoin = '0';
            $promovalue = 0;
            $maxvalue = 0;

            if (!empty($request->ticket_number)) {
                $ticketid = Ticket::where('ticket_number', $request->ticket_number)->where('partner_id', $partner_id)->first();
                if (!empty($ticketid)) {
                    $getFacilityType = Facility::where('id', $ticketid->facility_id)->pluck('is_gated_facility');
                }
                //@ check for gated promo code overstay case
                if ($getFacilityType[0] === "1") {
                    $getAmountForExtent = OverstayTicket::where('ticket_id', $ticketid->id)
                        ->sum('discount_amount');
                    if ($getAmountForExtent > 0) {
                        $totalUsedAmountByTicket = $getAmountForExtent + $ticketid->discount_amount;
                    } else {
                        $totalUsedAmountByTicket = $ticketid->discount_amount;
                    }
                    $ticketIdforHours =  $ticketid->id;
                    $extendCase = 1;
                } else {
                    // for  ungated extend screnn
                    $getAmountForExtent = TicketExtend::where('ticket_id', $ticketid->id)
                        ->sum('discount_amount');
                    if ($getAmountForExtent > 0) {
                        $totalUsedAmountByTicket = $getAmountForExtent + $ticketid->discount_amount;
                    } else {
                        $totalUsedAmountByTicket = $ticketid->discount_amount;
                    }
                    $ticketIdforHours =  $ticketid->id;
                    $extendCase = 1;
                }
            }

            // This Section of code moved here from old class :: Start 

            // check secret for Rm
            $isRm = User::find($partner_id);
            if (isset($isRm->user_type) && $isRm->user_type == '12') {
                $promotion = Promotion::where('id', $promoCode->promotion_id)->where('rm_id', $partner_id)->first();
                if (!$promotion) {
                    $promotion = Promotion::where('id', $promoCode->promotion_id)->where('owner_id', $isRm->created_by)->first();
                }
            } else {
                if (isset($promotion->rm_id) && $promotion->rm_id > 0) {
                    if ($request->facility_id != '') {
                        $rmFacility = UserFacility::where("facility_id", $request->facility_id)->where("user_id", $promotion->rm_id)->first();
                        if (!$rmFacility) {
                            throw new ApiGenericException('Please enter valid promocode.', 500);
                        }
                    }
                }
            }
            \Log::info("Performance check point 6");
            // !!!! close   
            //end
            if (!$promotion) {
                throw new ApiGenericException('Promocode not authorized for user', 500);
            }

            $is_guest = isset($request->is_guest) ? $request->is_guest : 0;
            if ($is_guest && $promotion->user_type == 0) {
                throw new ApiGenericException('Promocode not authorized for guest users', 500);
            }

            if (isset($request->phone) || isset($request->license_plate)) {
                if ($promotion->promotion_validation_type == '1') {
                    $request->request->add(['email' => $request->phone]);
                } elseif ($promotion->promotion_validation_type == '2') {
                    $request->request->add(['email' => $request->license_plate]);
                }
            }
            if ($promotion->specific_user_type == 1 && !(self::checkPromoEmailValidity($promotion->id, $request->email))) {
                throw new ApiGenericException('Promocode not authorized for user', 500);
            }

            if ($promotion->status != 1) {
                throw new ApiGenericException('Sorry, the validation code you entered is not applicable.', 500);
            }

            if ($promotion->is_validity_check == 1 && $currentDate < $promotion->valid_from) {
                throw new ApiGenericException('Promocode has not been activated yet', 500);
            }

            // remove is_promocode_valid and promo_check_response
            if ($promotion->is_validity_check == 1 && $currentDate > $promotion->valid_to) {
                throw new ApiGenericException('Sorry! but it looks like this promotion has already expired', 500);
            }
            if (isset($request->phone) && !empty($request->phone)) {
                $countryCode = QueryBuilder::appendCountryCode($request->phone);
                $request->request->add(['phone' => $countryCode]);
            }
            \Log::info("Performance check point 7 : {$promoCode->promo_type_id}");
            switch ($promoCode->promo_type_id) {
                case 1:
                    // self::validateStaticPromoCode($request, $promoCode);
                    self::checkPromoCodeUsageForEmail($partner_id, $promoCode, $request, $promotion, $ticketid);
                    break;
                case 2:
                    self::validateSingleUsePromoCodeForEmail($partner_id, $promoCode, $request->email, $promotion, $ticketid, $request->reservation_id, $request);
                    break;
                case 3:
                    self::validatePromoValuePromoCode($request, $promoCode);
                    break;
                default:
                    // code...
                    break;
            }
            \Log::info("Performance check point 8");

            //get discount mx limit
            $percentageDiscount = isset($promotion->percentage_off_discount) ? $promotion->percentage_off_discount : 0;
            $dollar_off_discounts = isset($promotion->dollar_off_discount) ? $promotion->dollar_off_discount : 0;

            $amountBeforeDiscount = $amount = $request->amount;
            // dd('Promocode', $promoCode, $request->all(), in_array($client->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT')));
            $netParkingAmount = $taxFee = $processingFee = $additionalFee = $surchargeFee = 0;
            //  dd($client->partner_id,config('parkengage.PARTNER_GROUP_DISCOUNT'));
            if (in_array($client->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                // if ($promotion->is_tax_applicable == 1) {
                //     if (isset($request->tax_amount) && $request->tax_amount > 0) {
                //         $amountBeforeDiscount += $request->tax_amount;
                //     }
                //     if (isset($request->processing_fee) && $request->processing_fee > 0) {
                //         $amountBeforeDiscount += $request->processing_fee;
                //     }
                // }
            } else {
                // Old Flow.
                if ($promotion->is_tax_applicable == 1) {
                    if (isset($request->tax_amount) && $request->tax_amount > 0) {
                        $amountBeforeDiscount = $amount += $request->tax_amount;
                    }
                    if (isset($request->processing_fee) && $request->processing_fee > 0) {
                        $amountBeforeDiscount = $amount += $request->processing_fee;
                    }
                }
            }

            \Log::info("Performance check point 9");
            \Log::info("is_tax_applicable");
            \Log::info("Amount :  {$amount} , amountBeforeDiscount : {$amountBeforeDiscount} : disconut value : {$promotion->discount_value}");

            $discount_in_dollar = 0;
            $discount_in_hours = 0;
            $remaning_payable_amount = 0;
            $max_percentage_discount_exceeded = false;
            $max_lifetime_discount_limit = $promotion->max_lifetime_discount;
            \Log::info("Performance check point 10");
            //pass("promoCode=>'promocode object'","$promotion->is_tax_applicable=>0 for not apply,1=>apply",is_extend_type='0=>no,1=>yes')
            //$totalUsedAmountByTicket =>perivous used amount,
            $updatedCalculationForTax = [];
            if (in_array($client->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                $updatedCalculationForTax =  self::validatePromoCodeCalculationForTax($promoCode, $promotion, $extendCase, $totalUsedAmountByTicket, $amount, $facility, $request);
                $discount_in_dollar = $updatedCalculationForTax['discount_in_dollar'];
            }

            if ($promoCode->discount_type === 'percentage') {

                $discount_in_dollar = ($promoCode->discount_value / 100) * $amount;
                //check first if due to check maximum amount for extend case
                $addAppliedAmountOnRate = $totalUsedAmountByTicket + $amount;

                if ($addAppliedAmountOnRate > $percentageDiscount && $extendCase == 1) {
                    $max_percentage_discount_exceeded = true;
                    if ($percentageDiscount > $totalUsedAmountByTicket) {
                        $discount_in_dollar = $percentageDiscount - $totalUsedAmountByTicket;
                    } else {
                        $discount_in_dollar = 0.00;
                    }
                }

                if ($addAppliedAmountOnRate > $percentageDiscount && $extendCase == 1) {
                    $max_percentage_discount_exceeded = true;
                    if ($percentageDiscount > $totalUsedAmountByTicket) {
                        $discount_in_dollar = $percentageDiscount - $totalUsedAmountByTicket;
                    } else {
                        $discount_in_dollar = 0.00;
                    }
                }
                //end if
                if ($discount_in_dollar > $percentageDiscount) {
                    $max_percentage_discount_exceeded = true;
                    $discount_in_dollar = $percentageDiscount;
                }
                //if  single promocode enable and apply one time then 
                if ($promoCode->promo_type_id == 2) {
                    $promoUsage = PromoUsage::where(['user_id' => $partner_id, 'promocode' => $promoCode->promocode])->get();
                    $totalUsage = count($promoUsage);
                    if ($totalUsage >= 1) {
                        $discount_in_dollar = 0.00;
                    }
                }

                //     // Calculate Fees for VP : PIMS - 14210
                //     $netParkingAmount = ($amountBeforeDiscount - $discount_in_dollar);
                //     \Log::info("Amount :  {$amount} - amountBeforeDiscount : {$amountBeforeDiscount} - netParkingAmount : {$netParkingAmount}");
                //     if (in_array($client->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                //         // dd('drive_up_processing_fee', $request->facility->drive_up_processing_fee);
                //         // $facility = $request->facility;
                //         // $taxFee = $processingFee = $additionalFee = $surchargeFee = 0;
                //         if ($promotion->is_tax_applicable == 1) {
                //             if ($netParkingAmount > 0) {
                //                 if (isset($request->tax_amount) && $request->tax_amount > 0) {
                //                     // $taxFee = ($promoCode->discount_value / 100) * $request->tax_amount;
                //                     // $taxFee = ($promoCode->discount_value / 100) * $netParkingAmount;

                //                     $rate['price']      = $netParkingAmount;
                //                     $taxFee             = $facility->getTaxRate($rate);

                //                 }
                //                 if (isset($request->processing_fee) && $request->processing_fee > 0) {
                //                     $processingFee = $facility->drive_up_processing_fee - (($promoCode->discount_value / 100) * $facility->drive_up_processing_fee);
                //                     // $processingFee = ($promoCode->discount_value / 100) * $netParkingAmount;
                //                 }
                //             }
                //             // Need to update in RevPass Case
                //             $amountBeforeDiscount += ($taxFee + $processingFee);
                //         } else {
                //             $taxFee         =   $request->tax_amount;
                //             $processingFee  =   $request->processing_fee;
                //         }
                //     }
                //     // Close Calculate Fees for VP : PIMS - 14210

                // Calculate Fees for VP : PIMS - 14210
                $netParkingAmount = ($amountBeforeDiscount - $discount_in_dollar);
                \Log::info("Amount :  {$amount} - amountBeforeDiscount : {$amountBeforeDiscount} - netParkingAmount : {$netParkingAmount}");
                if (in_array($client->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                    // dd('drive_up_processing_fee', $request->facility->drive_up_processing_fee);
                    $facility = $request->facility;
                    $taxFee = $processingFee = $additionalFee = $surchargeFee = 0;
                    if ($promotion->is_tax_applicable == 1) {
                        if ($netParkingAmount > 0) {
                            if (isset($request->tax_amount) && $request->tax_amount > 0) {
                                // $taxFee = ($promoCode->discount_value / 100) * $request->tax_amount;
                                // $taxFee = ($promoCode->discount_value / 100) * $netParkingAmount;

                                $rate['price']      = $netParkingAmount;
                                $taxFee             = $facility->getTaxRate($rate);
                            }
                            if (isset($request->processing_fee) && $request->processing_fee > 0) {
                                $processingFee = $facility->drive_up_processing_fee - (($promoCode->discount_value / 100) * $facility->drive_up_processing_fee);
                                // $processingFee = ($promoCode->discount_value / 100) * $netParkingAmount;
                            }
                        }
                        // Need to update in RevPass Case
                        $amountBeforeDiscount += ($taxFee + $processingFee);
                    } else {
                        $taxFee         =   $request->tax_amount;
                        $processingFee  =   $request->processing_fee;
                    }
                }
                // Close Calculate Fees for VP : PIMS - 14210

                //end promocode single uses
            } elseif ($promoCode->discount_type === 'hours') {
                $discount_in_hours = $promoCode->discount_value;
                //get ticket id for handle hours

                $get_ticket_id = $ticketIdforHours;
                //if  single promocode enable and apply one time then 

                if ($promoCode->promo_type_id === "2") {
                    $promoUsage = PromoUsage::where(['user_id' => $partner_id, 'promocode' => $promoCode->promocode])->get();
                    $totalUsage = count($promoUsage);
                    if ($totalUsage >= 1) {
                        $discount_in_hours = 0;
                    }
                }
                $promoin = '2';
                $promovalue = $discount_in_hours;
                $maxvalue = 0;
                //end promocode single uses
            } else {
                // Value Based Promocode 
                // Calculate Fees for VP : PIMS - 14210

                if (!isset($request->extend_request)) {
                    // Normal Checkin
                    $netParkingAmount   = ($amountBeforeDiscount >= $promotion->discount_value) ? $amountBeforeDiscount - $promotion->discount_value :  0;
                    $discount_in_dollar = ($amountBeforeDiscount >= $promotion->discount_value) ? $promotion->discount_value :  $amountBeforeDiscount;
                    \Log::info("Amount :  {$amount} - amountBeforeDiscount : {$amountBeforeDiscount} : Discount value  - {$promotion->discount_value} :  - netParkingAmount : {$netParkingAmount}");
                    if (in_array($client->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                        if ($promotion->is_tax_applicable == 1) {
                            if ($netParkingAmount > 0) {
                                if (isset($request->tax_amount) && $request->tax_amount > 0) {
                                    $rate['price']      = $netParkingAmount;
                                    $taxFee             = $facility->getTaxRate($rate, '0');
                                }
                                if (isset($request->processing_fee) && $request->processing_fee > 0) {
                                    $processingFee = $facility->drive_up_processing_fee;
                                }
                            }
                            // Need to update in RevPass Case
                            $amountBeforeDiscount += ($taxFee + $processingFee);
                        } else {
                            $taxFee         =   $request->tax_amount;
                            $processingFee  =   $request->processing_fee;
                        }
                    }
                } else {
                    // Only Extend case : 
                    $taxFee             =   $request->tax_amount;
                    $processingFee      =   $request->processing_fee;
                    $netParkingAmount   =   $request->amount;
                }

                // Close Calculate Fees for VP : PIMS - 14210

                if (!isset($request->extend_request)) {
                    if ($amount >= $dollar_off_discounts) {   // check for min txn. value. 
                        $discount_in_dollar = $promoCode->discount_value;
                        if ($discount_in_dollar >= $amount) {
                            $discount_in_dollar = $amount;
                        }
                    } else {
                        throw new ApiGenericException("Sorry, this validation code is valid only for transactions of $" . $dollar_off_discounts . " or more.", 500);
                    }
                }

                $promoin = '3';
                $promovalue = $discount_in_dollar;
                $maxvalue = 0;
            }
            \Log::info("Performance check point 11");
            if ($promotion->specific_user_type == 1 && $max_lifetime_discount_limit > 0) {
                $available_discount_limit = self::getAvailableDiscountLimit($partner_id, $promoCode, $promotion, $request->email);
                if ($available_discount_limit === 0) {
                    throw new ApiGenericException("You have already reached the maximum discount on this promotion.", 500);
                }
            }


            self::sanitizePromocodeData($promoCode);
            \Log::info("Performance check point 13");
            $max_lifetime_discount_exceeded = false;
            if ($promotion->specific_user_type == 1) {
                if ($max_lifetime_discount_limit > 0 && $available_discount_limit < $discount_in_dollar) {
                    $max_lifetime_discount_exceeded = true;
                    $max_percentage_discount_exceeded = false;
                    $discount_in_dollar = $available_discount_limit;
                    $message = 'You have reached the maximum overall discount allowed on this promocode';
                } else if ($max_percentage_discount_exceeded) {
                    $message = "You have reached the maximum discount per transaction on this promocode";
                } else {
                    $message = "Promocode successfully applied";
                    if ($partner_id == 54682 || $partner_id == "54682") {
                        $message = "Coupon successfully applied";
                    }
                }
            } else {
                if ($max_percentage_discount_exceeded) {
                    $message = "You have reached the maximum discount per transaction on this promocode";
                } else {
                    $message = "Promocode successfully applied";
                    if ($partner_id == 54682 || $partner_id == "54682") {
                        $message = "Coupon successfully applied";
                    }
                }
            }
            \Log::info("Performance check point 14 amountBeforeDiscount : {$amountBeforeDiscount} -  discount_in_dollar : {$discount_in_dollar}  ");
            $discount_in_dollar = (float)number_format((float)$discount_in_dollar, 2, '.', '');

            //$payable_amount = (($request->amount - $discount_in_dollar) <= 0) ? 0.00 : ($request->amount - $discount_in_dollar);
            // $payable_amount = (($amount - $discount_in_dollar) <= 0) ? 0.00 : ($amount - $discount_in_dollar);
            $payable_amount = (($amountBeforeDiscount - $discount_in_dollar) <= 0) ? 0.00 : ($amountBeforeDiscount - $discount_in_dollar);
            $netParkingAmount = 0;
            if (!empty($updatedCalculationForTax)) {
                $discount_in_dollar    =  $updatedCalculationForTax['discount_in_dollar'];
                $processingFee         =  $updatedCalculationForTax['processing_fee'];
                $surchargeFee          =  $updatedCalculationForTax['surcharge_fee'];
                $additionalFee         =  $updatedCalculationForTax['additional_fee'];
                $taxFee                =  $updatedCalculationForTax['tax_fee'];
                $netParkingAmount      =  $updatedCalculationForTax['net_parking_amount'];
                $payable_amount        = $updatedCalculationForTax['calculated_payable_amount'];
            }
            $response  = [
                'is_promocode_valid' => true,
                'promocode' => $promoCode,
                'message' => $message,
                'discount_in_dollar' => $discount_in_dollar,
                'max_percentage_discount' => $percentageDiscount,
                'min_amount_required_for_dollar_discount' => $dollar_off_discounts,
                'max_lifetime_discount' => $max_lifetime_discount_limit,
                'is_max_lifetime_discount_exceeded' => $max_lifetime_discount_exceeded,
                'is_max_percentage_discount_exceeded' => $max_percentage_discount_exceeded,
                'is_tax_applicable' => $promotion->is_tax_applicable,
                'discount_in_hours' => $discount_in_hours,
                'get_ticket_id' => $get_ticket_id,
                'no_of_times' => $promotion->no_of_times,
                'applicable_to_hours' => $promotion->applicable_to_hours,
                'first_hours_apply' => $promotion->first_hours_apply,
                'promoin' => $promoin,
                'promovalue' => $promovalue,
                'maxvalue' => $maxvalue,
                'net_parking_amount' => $netParkingAmount,
                'tax_fee' => $taxFee,
                'processing_fee' => $processingFee,
                'additional_fee' => $additionalFee,
                'surcharge_fee' => $surchargeFee
            ];
            // $taxFee = $processingFee = $additionalFee = $surchargeFee = 0;
            \Log::info("Performance check point 15");
            // Vijay 
            $alreadyFeeCharged = 0;
            $status = 202;
            if (isset($request->reservation_id) && !empty($request->reservation_id)) {
                $reservation = Reservation::find($request->reservation_id);
                if ($reservation->facility_id == config('parkengage.ROC_FACILITY')) {
                    $status = 200;
                }
                if (isset($request->reservation_mode) && $request->reservation_mode == '2')
                    $alreadyFeeCharged = $reservation->tax_fee + $reservation->processing_fee;

                if ($promotion->is_tax_applicable == 0) {
                    if (isset($request->is_extend) && $request->is_extend) {
                    } else {
                        $payable_amount = $payable_amount + ($request->tax_amount + $request->processing_fee) - $alreadyFeeCharged;
                    }
                }
            } else {
                if (isset($request->facility_id) && $request->facility_id == config('parkengage.ROC_FACILITY')) {
                    $status = 200;
                }
                // $payable_amount = $payable_amount + ($request->tax_amount + $request->processing_fee) - $alreadyFeeCharged;
                if ($promotion->is_tax_applicable == 0) {
                    $payable_amount = $payable_amount + ($request->tax_amount + $request->processing_fee) - $alreadyFeeCharged;
                }
            }
            $response['payable_amount'] = round($payable_amount, 2);

            // dd($promotion->is_tax_applicable == 0, $promotion->is_tax_applicable, $payable_amount, $response['discount_in_dollar'], $alreadyFeeCharged);
            \Log::info("Performance check point 16");
            \Log::info("is_tax_applicable check resposen ");
            \Log::info(json_encode($response));
            $db_logs_data['response'] = json_encode($response);

            $log->info("Response: " . $db_logs_data['response']);

            DB::table('partner_promocode_api_logs')->insert($db_logs_data);

            if ($max_percentage_discount_exceeded || $max_lifetime_discount_exceeded) {
                $response["max_discount_limit_exceeded"] = 1;
                return response()->json($response, $status);
            }
            \Log::info("Performance check point 17");
            return response()->json($response, 200);
        } catch (Exception $e) {
            $log->error("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
            $db_logs_data['is_error'] = 1;
            $db_logs_data['error_message'] = $e->getMessage();
            DB::table('partner_promocode_api_logs')->insert($db_logs_data);
            throw $e;
        }
    }


    public static function validatePromoCodeUsageThirdParty($request)
    {
        $db_logs_data['request'] = json_encode($request->all());
        $db_logs_data['api_type'] = 'usage';
        $db_logs_data['created_at'] = Carbon::now();
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/partner_promocode_api_logs')->createLogger('usage_api_logs');
        try {

            $promoCode = PromoCode::where('promocode', $request->promocode)->first();

            if (!$promoCode) {
                throw new ApiGenericException('It seems like this promo code either doesn\'t exist or is spelled wrong.', 500);
            }

            $currentDate = date('Y-m-d');

            $client = DB::table('oauth_clients')->where('secret', $request->client_id)->orderBy('created_at', 'desc')->first();
            $partner_id = $client->partner_id;

            $promotion = Promotion::where('id', $promoCode->promotion_id)->where('owner_id', $partner_id)->first();

            if (!$promotion) {
                throw new ApiGenericException('Promocode not authorized for user', 500);
            }

            if ($promotion->specific_user_type == 1 && !(self::checkPromoEmailValidity($promotion->id, $request->email))) {
                throw new ApiGenericException('Promocode not authorized for user', 500);
            }

            $is_guest = isset($request->is_guest) ? $request->is_guest : 0;
            if ($is_guest && $promotion->user_type == 0) {
                throw new ApiGenericException('Promocode not authorized for guest users', 500);
            }

            if ($promotion->status != 1) {
                throw new ApiGenericException('Sorry, the validation code you entered is not applicable.', 500);
            }

            if ($promotion->is_validity_check == 1 && $currentDate < $promotion->valid_from) {
                throw new ApiGenericException('Promocode has not been activated yet', 500);
            }

            if ($promotion->is_validity_check == 1 && $currentDate > $promotion->valid_to) {
                throw new ApiGenericException('Sorry! but it looks like this promotion has already expired', 500);
            }

            switch ($promoCode->promo_type_id) {
                case 1:
                    // self::validateStaticPromoCode($request, $promoCode);
                    self::checkPromoCodeUsageForEmail($partner_id, $promoCode, $request->email);
                    break;
                case 2:
                    self::validateSingleUsePromoCodeForEmail($partner_id, $promoCode, $request->email, $promotion);
                    break;
                case 3:
                    self::validatePromoValuePromoCode($request, $promoCode);
                    break;
                default:
                    // code...
                    break;
            }

            $max_lifetime_discount_limit = $promotion->max_lifetime_discount;
            if ($promotion->specific_user_type == 1 && $max_lifetime_discount_limit > 0) {
                $available_discount_limit = self::getAvailableDiscountLimit($partner_id, $promoCode, $promotion, $request->email);
                if ($available_discount_limit === 0) {
                    throw new ApiGenericException("You have already reached the maximum discount on this promotion.", 500);
                }
            }

            $discount_in_dollar = $request->amount;
            $message = "Promocode amount successfully used.";

            if ($promotion->specific_user_type == 1) {
                if ($max_lifetime_discount_limit > 0 && $available_discount_limit < $discount_in_dollar) {
                    $discount_in_dollar = $available_discount_limit;
                    $message = 'You have reached the maximum overall discount allowed on this promocode';
                }
            }

            $discount_in_dollar = (float)number_format((float)$discount_in_dollar, 2, '.', '');
            $response  = [
                'promocode' => $promoCode,
                'applicable_discount' => $discount_in_dollar,
                'partner_id' => $partner_id,
                'message' => $message
            ];
            return $response;
        } catch (Exception $e) {
            $log->error("Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine());
            $db_logs_data['is_error'] = 1;
            $db_logs_data['error_message'] = $e->getMessage();
            DB::table('partner_promocode_api_logs')->insert($db_logs_data);
            throw $e;
        }
    }


    public static function validateStaticPromoCode($request, $promoCode)
    {

        $promotion = Promotion::where('id', $promoCode->promotion_id)->first();
        if ($promotion->user_type == self::$user_type) {

            self::$is_opt_out = 1;

            if (!$request->user_id) {

                self::validateNewStaticGuestPromoCode($request, $promoCode);
            } else {

                self::validateNewStaticUserPromoCode($request, $promoCode);
            }
        } else {

            if (!$request->user_id) {
                throw new ApiGenericException('Please log in or create an account to redeem this promo code.');
            }
            self::validateUser($request->user_id);
            self::checkPromoCodeUsage($request->user_id, $promoCode);
        }
    }

    public static function validateSingleUsePromoCode($request, $promoCode)
    {
        if ($promoCode->is_expired) {
            self::$is_promocode_valid = false;
            self::$promo_check_response = 'You have reached the maximum discount on this promotion.';
        }
    }

    public static function validateSingleUsePromoCodeForEmail($user_id, $promoCode, $email, $promotion, $ticketid = '', $reservationId = '', $request)
    {
        // $promoUsage = PromoUsage::where(['user_id' => $user_id, 'promocode' => $promoCode->promocode, 'email' => $email])->first();
        // remove by sunil due to promo code usees check in guest and register -04-07-2024

        $promoUsage = PromoUsage::where(['partner_id' => $user_id, 'promocode' => $promoCode->promocode])->get();

        // check 
        if (!empty($reservationId) && empty($ticketid)) {
            $reservationResult =  PromoUsage::where('reservation_id', $reservationId)->where('email', $email)->first();
            if ($reservationResult) {
                return;
            }
        }
        // check ticket is exit inside promo or not
        if (!empty($ticketid)) {
            $ticketidresult =  PromoUsage::where('ticket_id', $ticketid->id)->first();
            if ($ticketidresult) {
                return;
            }
        }
        //end promo ticket check in 
        if (!empty($promoUsage)) {
            $totalUsage = count($promoUsage);
            if ($totalUsage >= $promotion->usage) {
                throw new ApiGenericException("Maximum usage of this promocode has already been exceeded", 500);
            }
        }
    }

    public static function validatePromoValuePromoCode($request, $promoCode)
    {
        throw new ApiGenericException('Error Occured, Promo Code Type Is Currently Not Supported');
    }

    public static function validateUser($user_id)
    {
        $user = User::where('id', $user_id)->first();
        if (!$user) {
            throw new ApiGenericException('Please login with a valid user Id.');
        }
        return $user;
    }

    public static function checkPromoCodeUsage($user_id, $promoCode)
    {
        $promoUsages = PromoUsage::where(['user_id' => $user_id, 'promocode' => $promoCode->promocode])->get();
        if (!empty($promoUsages->toArray())) {
            $totalUsage = count($promoUsages->toArray());
            if ($totalUsage >= $promoCode->usage) {
                self::$is_promocode_valid = false;
                self::$promo_check_response = 'You have reached the maximum discount on this promotion.';
            }
        }
    }

    public static function checkPromoCodeUsageForEmail($user_id, $promoCode, $request, $promotion, $ticketid)
    {
        // remove by sunil due to reservation permit 
        if ($promotion->specific_user_type == '1') {
            if (isset($request->user_id) && !empty($request->user_id)) {
                $userid = $request->user_id;
            } else {
                $userid = $user_id;
            }
            $promoUsages = PromoUsage::where(['user_id' => $userid, 'promocode' => $promoCode->promocode, 'email' => $request->email])->get();
        } else {
            if (isset($request->user_id) && !empty($request->user_id)) {
                $promoUsages = PromoUsage::where(['user_id' => $request->user_id, 'promocode' => $promoCode->promocode])->get();
            } else {
                $promoUsages = PromoUsage::where(['partner_id' => $user_id, 'promocode' => $promoCode->promocode])->get();
            }
        }
        // check ticket is exit inside promo or not
        if (!empty($ticketid)) {
            $ticketidresult =  PromoUsage::where('ticket_id', $ticketid->id)->first();
            if ($ticketidresult) {
                return;
            }
        }
        //end promo ticket check in 
        $resultfinal =  self::checkMobileEmailLiecenseplate($user_id, $promoCode, $request, $promotion);
        //handle guest case for static use code
        if (!empty($promoUsages->toArray()) && !empty($resultfinal)) {
            $totalUsage = count($promoUsages->toArray());
            if ($totalUsage >= $promotion->usage) {
                throw new ApiGenericException("Maximum usage of this promocode has already been exceeded", 500);
            }
        }
    }

    public static function checkPromoEmailValidity($promotion_id, $email)
    {
        $email_exists = PromotionUser::where(['promotion_id' => $promotion_id, 'email' => $email])->count();
        return $email_exists ? true : false;
    }

    public static function getAvailableDiscountLimit($user_id, $promoCode, $promotion, $email)
    {
        $available_limit = $promotion->max_lifetime_discount;
        if ($promotion->specific_user_type == 1) {
            $promoUsages = PromoUsage::where(['user_id' => $user_id, 'promocode' => $promoCode->promocode, 'email' => $email])->get();
        } else {
            $promoUsages = PromoUsage::where(['user_id' => $user_id, 'promocode' => $promoCode->promocode])->get();
        }
        if (!empty($promoUsages->toArray())) {
            $total_discount_used = $promoUsages->sum('discount_amount');
            if ($total_discount_used < $promotion->max_lifetime_discount) {
                $available_limit = $promotion->max_lifetime_discount - $total_discount_used;
            } else {
                $available_limit = 0;
            }
        }
        return $available_limit;
    }


    public static function validateNewStaticGuestPromoCode($request, $promoCode)
    {
        if (isset($request->is_landing_page) && ($request->is_landing_page) == '1') {
            return $promoCode;
        }

        if (!$request->is_auto_apply) {

            if (!$request->email) {
                throw new ApiGenericException('Please enter your email address to apply this promo code.');
            }

            $user = User::getAnonUser($request->email);

            if (!$user) {
                throw new ApiGenericException('Sorry! Please enter your email address to apply this promo code.');
            }

            $promoUsages = PromoUsage::where(['user_id' => $user->id, 'promocode' => $promoCode->promocode])->get();

            if (count($promoUsages) > 0) {
                self::$is_promocode_valid = false;
                self::$promo_check_response = 'Our records show you have already used this promo code.';
            }
            return $promoCode;
        }
        return $promoCode;
    }

    public static function validateNewStaticUserPromoCode($request, $promoCode)
    {
        self::validateUser($request->user_id);
        self::checkPromoCodeUsage($request->user_id, $promoCode);
        //        $promoUsages = PromoUsage::where(['user_id' => $request->user_id, 'promocode' => $promoCode->promocode])->get();
        //         if (count($promoUsages)>0) { 
        //            self::$is_promocode_valid = false;
        //            self::$promo_check_response = 'Promo Code has been expired.';
        //        }
    }

    private static function sanitizePromocodeData($promoCode)
    {
        unset($promoCode->promotion_id);
        // unset($promoCode->promo_type_id);
        unset($promoCode->channel_partner_id);
        unset($promoCode->deleted_at);
        unset($promoCode->created_by);
        unset($promoCode->created_at);
        unset($promoCode->updated_at);
        unset($promoCode->updated_by);
        unset($promoCode->deleted_by);
        unset($promoCode->expired_at);
    }

    // validate promotions type {percentage , hours, else} 

    public static function validatePromotionTypes($request, $promotion)
    {
        $maxDiscountValue = isset($promotion->promocode->discount_value) ? $promotion->promocode->discount_value : 0;
        $dollar_off_discounts = isset($promotion->min_amount_required_for_dollar_discount) ? $promotion->min_amount_required_for_dollar_discount : 0;

        $amount = $promotion->discount_in_dollar;
        if ($promotion->promocode->discount_type === 'percentage') {
            $discount_in_dollar = ($amount * ($promotion->max_percentage_discount / 100));  // calculate percentage discount (1-100);

            if ($discount_in_dollar > $maxDiscountValue) {
                $discount_in_dollar = $maxDiscountValue;
            }
            $promotion->discount_in_dollar = $discount_in_dollar;
        } elseif ($promotion->promocode->discount_type === 'hours') {
            $discount_in_hours = $promotion->discount_value;
        } else {
            if ($amount < $dollar_off_discounts) {
                throw new ApiGenericException("Sorry, this validation code is valid only for transactions of $" . $dollar_off_discounts . " or more.", 500);
            } else if ($amount >= $dollar_off_discounts) {
                $discount_in_dollar = $maxDiscountValue;
            } else {
                $discount_in_dollar = $amount;
            }
            $promotion->discount_in_dollar = $discount_in_dollar;
        }
        return $promotion;
    }


    public static function checkMobileEmailLiecenseplate($user_id, $promoCode, $request, $promotion)
    {
        if (!empty($request->phone) || !empty($request->email)) {
            $userId = User::query();
            if (!empty($request->phone)) {
                $userId = $userId->orwhere('phone', $request->phone);
            }
            if (!empty($request->email)) {
                $userId = $userId->orwhere('email', $request->email);
            }
            $userId = $userId->pluck('id')->first();
        }
        if (isset($request->user_id) && $request->user_id > 0) {
            $userId = $request->user_id;
        }
        $totalUesedByUser = 0;
        if (!empty($userId)) {
            //sagar - function for check total usage 
            $ticketbl = Ticket::where('user_id', $userId)->where('promocode', $promoCode->promocode)->get();
            $totalUesedByUser += $ticketbl->count();
            $reservation = Reservation::where('user_id', $userId)->where('promocode', $promoCode->promocode)->get();
            $totalUesedByUser += $reservation->count();
            $reservation = PermitRequest::where('user_id', $userId)->where('promocode', $promoCode->promocode)->get();
            $totalUesedByUser += $reservation->count();
        }
        //check if licence_plate case
        //check if licence_plate case
        $license_plate = "";
        if (!empty($request->bookingType) && $request->bookingType === "permits") {
            $license_plate = array_column($request->vehicleList, 'license_plate');
        } else if (!empty($request->license_plate)) {
            $license_plate = explode(',', $request->license_plate, 0);
        }
        if (!empty($license_plate)) {
            $getUserData = Ticket::whereIn('license_plate', $license_plate)
                ->where('promocode', $promoCode->promocode)
                ->pluck('user_id');
            //  $totalUsedByUser= $getUserData->count();
            $totalUesedByUser += $getUserData->count();
            // check on 
            $reservation = Reservation::whereIn('license_plate', $license_plate)
                ->where('promocode', $promoCode->promocode)
                ->pluck('user_id');
            //  $totalUsedByUser= $reservation->count();
            $totalUesedByUser += $reservation->count();
            // permit request
            $listofVehical = PermitVehicle::whereIn('license_plate_number', $license_plate)->pluck('user_id');
            if (!empty($listofVehical)) {
                $permit = PermitRequest::whereIn('user_id', $listofVehical)
                    ->where('promocode', $promoCode->promocode)
                    ->pluck('user_id');
                $totalUesedByUser += $permit->count();
            }
        }
        if ($promotion->promo_type_id === '1') {
            // count number of tickets on this promo code
            if (!empty($totalUesedByUser)) {
                if ($promotion->usage_type == 4 || $promotion->usage_type == 3) {
                    $checkDayWiseLimit = QueryBuilder::checkDayWiseUsageLimit($promotion, $promotion->usage_type, $totalUesedByUser, $userId, $promoCode->promocode, $request->license_plate, $request->ticket_number);
                    if ($checkDayWiseLimit) {
                        return;
                    }
                } else {
                    $countPromoUsages = $promotion->usage;
                    if ($totalUesedByUser >= $countPromoUsages) {
                        throw new ApiGenericException("Maximum usage of this promocode has already been exceeded", 500);
                    } else {
                        return;
                    }
                }
            } else {
                return;
            }
        }
        //handle guest case for static use code
    }
    public static function convertHoursDynamic($totalHours)
    {

        $currentMonth = date('n');
        $currentYear = date('Y');

        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $currentMonth, $currentYear);
        // Define constants
        $hoursInDay = 24;

        $months = floor($totalHours / ($hoursInDay * $daysInMonth));
        $remainingHoursAfterMonths = $totalHours % ($hoursInDay * $daysInMonth);

        $days = floor($remainingHoursAfterMonths / $hoursInDay);
        $hours = $remainingHoursAfterMonths % $hoursInDay;

        // Construct readable output
        $result = [];
        if ($months > 0) {
            $result[] = "$months month" . ($months > 1 ? 's' : '');
        }
        if ($days > 0) {
            $result[] = "$days day" . ($days > 1 ? 's' : '');
        }
        if ($hours > 0) {
            $result[] = "$hours hour" . ($hours > 1 ? 's' : '');
        }

        return !empty($result) ? implode(', ', $result) : '0 hours';
    }

    //new flow for discount key manage 
    public static function validatePromoCodeCalculationForTax($promoCode, $promotion, $extendCase, $totalUsedAmountByTicket, $amount, $facility, $request)
    {
        //totalRequestedAmount if not apply at thank you screen
        $updatedCalculationForTax = [];
        $data = [
            'promoCode' => $promoCode,
            'promotion' => $promotion,
            'extendCase' => $extendCase,
            'totalUsedAmountByTicket' => $totalUsedAmountByTicket,
            'amount' => $amount,
            'facility' => $facility,
            'netParkingAmount' => $amount, // initialize
            'discount_in_dollar' => 0,
            'updatedCalculationForTax' => [],
            'calculated_payable_amount' => 0,
        ];
        $netParkingAmount = $totalRequestedAmount = $amount;
        if ($promoCode->discount_type === 'percentage') {
            self::CalculatePercentagePromo($data);
        } elseif ($promoCode->discount_type === 'hours') {
            self::CalculateHourlyPromocode($data, $request);
        } else {
            //handle values case here 
            self::calculateValuePromoCode($data);
        }
        //$updatedCalculationForTax['discount_in_dollar']=$discount_in_dollar;
        self::calculationOnNetParkingAmount($data, $request);


        return $data['updatedCalculationForTax'];
    }

    public static function CalculatePercentagePromo(array &$data)
    {
        $percentageDiscount = isset($data['promotion']->percentage_off_discount) ? $data['promotion']->percentage_off_discount : 0;
        $discount_in_dollar = ($data['promoCode']->discount_value / 100) * $data['amount'];
        //check first if due to check maximum amount for extend case
        $addAppliedAmountOnRate = $data['totalUsedAmountByTicket'] + $data['amount'];
        // dd($data['amount']);
        $max_percentage_discount_exceeded = true;
        if ($percentageDiscount < $discount_in_dollar) {
            //case first if max discount value is greater than  avaiblabe discount which is given to user
            $discount_in_dollar = $percentageDiscount;
        }
        $netParkingAmount = ($addAppliedAmountOnRate - $discount_in_dollar);
        if ($data['promotion']->is_tax_applicable == '0') {
            //this condition handle calculation of additional charges if tax checkbox is not checked.
            $netParkingAmount = $data['amount'];
        }
        $data['netParkingAmount'] = $netParkingAmount;
        $data['discount_in_dollar'] = $discount_in_dollar;
    }


    public static function calculateValuePromoCode(array &$data)
    {

        $netParkingAmount   = ($data['amount'] >= $data['promotion']->discount_value) ? $data['amount'] - $data['promotion']->discount_value :  0;
        $discount_in_dollar = ($data['amount'] >= $data['promotion']->discount_value) ? $data['promotion']->discount_value :  $data['amount'];
        $data['netParkingAmount'] = $netParkingAmount;
        $data['discount_in_dollar'] = $discount_in_dollar;
    }

    public static function calculationOnNetParkingAmount(array &$data, $request)
    {

        $processingFee                           = 0;
        $rate['price']                           = $data['netParkingAmount'];
        //dd( $data['facility']);
        $processingFee                           = $data['facility']->drive_up_processing_fee;
        $surcharge_fee                           = $data['facility']->getSurchargeFee($rate);
        $CCFee                                   = $data['facility']->getAdditionalFee($rate);
        if ($data['facility']->tax_with_surcharge_enable == '1') {
            $rate['price']                           = $rate['price'] + $surcharge_fee;
        }
        $tax_rate                                =  $data['facility']->getTaxRate($rate, '0');
        // dd($rate['price'],$surcharge_fee,$tax_rate);
        if ($data['promotion']->is_tax_applicable == 1) {
            if ($data['promoCode']->discount_type == "percentage") {
                if (isset($request->processing_fee) && $request->processing_fee > 0) {
                    $processingFee = $data['facility']->drive_up_processing_fee - (($data['promoCode']->discount_value / 100) * $data['facility']->drive_up_processing_fee);
                }
            }
            if ($data['amount'] == $data['discount_in_dollar']) {
                $processingFee = 0;
            }
        }

        $data['updatedCalculationForTax']['processing_fee']      = $processingFee;
        $data['updatedCalculationForTax']['surcharge_fee']       = $surcharge_fee;
        $data['updatedCalculationForTax']['additional_fee']      = $CCFee;
        $data['updatedCalculationForTax']['tax_fee']             = $tax_rate;
        $data['updatedCalculationForTax']['net_parking_amount']  = $data['netParkingAmount'];
        $data['updatedCalculationForTax']['discount_in_dollar']  = $data['discount_in_dollar'];
        $data['updatedCalculationForTax']['calculated_payable_amount']  = $data['netParkingAmount'] > 0 ? $data['netParkingAmount'] + $processingFee + $surcharge_fee + $CCFee + $tax_rate : 0.00;
    }

    public static function CalculateHourlyPromocode(array &$data, $request)
    {

        if (isset($data['facility']->is_price_band) && $data['facility']->is_price_band == 0) {
            //calculation here if user select duration not rate band 
            $newrate = $data['facility']->rateForReservationOnMarker($request->arrivalTime, $request->ticketLength, false, false, false, true, false, '0', false, false, false, false);

            if (isset($newrate['id']) && !empty($newrate['id'])) {
                $request->request->add(['rate_id_hourly' => $newrate['id']]);
            } else {
                $request->request->add(['baseprice' => $newrate['price']]);
            }

            self::getUpdatedPriceforHourlyPromoCode($data, $request);
        }
        //dd($request->ticketLength,$request->rateHours,$data['promotion'],$data['facility']->is_price_band);

    }

    public static function getUpdatedPriceforHourlyPromoCode(array &$data, $request)
    {

        if (isset($request->rate_id_hourly)) {
            $rateId =  $request->rate_id_hourly;
            $rate = Rate::where('id', $rateId)->first();
        }
        $payableAMountNewRate = 0;
        if (isset($request->ticketLength) && $request->ticketLength > 0) {
            if (isset($rate) && !empty($rate['price'])) {
                /* $perhours=$rate['price']/$request->ticketLength;
                        per hours price changes due to change in logic parkingAmount/lenthstay per hours
                        if need pervious check is uncomment
                        new logic for first 12 or discount hours logic change
                        new condition */
                $perhours = $rate['price'] / $rate['max_stay'];
                if (isset($data['promotion']->first_hours_apply) && $data['promotion']->first_hours_apply == '1' && $data['promotion']->discount_value <= self::FULL_DAY_HOUR_VAL) {
                    $fullday = self::FULL_DAY_HOUR_VAL;
                    $length = $data['promotion']->discount_value;
                    $length = 24;

                    $freeHoursRate =  self::getUpdatedRate($data['facility']->id, $request->arrivalTime, $length);
                    $perhours = $freeHoursRate['price'] / $fullday;
                    //new changes for promocode

                    if ($request->ticketLength > 0 && $request->ticketLength < self::FULL_DAY_HOUR_VAL) {
                        $remaininglenth = $request->ticketLength - $data['promotion']->discount_value;
                        $rate = self::getUpdatedRate($data['facility']->id, $request->arrival_time, $remaininglenth);
                        $payableAMountNewRate = $rate['price'];
                        //check for discount hours and payable are not same
                        // $checkDiscountAMount=  $request->amount- $payableAMountNewRate;
                    }
                    //end new changes
                } elseif (isset($data['promotion']->first_hours_apply) && $data['promotion']->first_hours_apply == '2') {
                    $length = $rate['max_stay'];
                    $freeHoursRate =  self::getUpdatedRate($data['facility']->id, $request->arrival_time, $length);
                    $perhours = $freeHoursRate['price'] / $length;
                    if ($request->ticketLength > 0 && $request->ticketLength < self::FULL_DAY_HOUR_VAL) {
                        $remaininglenth = $request->ticketLength - $data['promotion']->discount_value;
                        $rate = self::getUpdatedRate($data['facility']->id, $request->arrival_time, $remaininglenth);
                        $payableAMountNewRate = $rate['price'];
                    }
                }
                //end 

                //$perhours=$rate['price']/$request->ticketLength;
                //end new logic
            } else {
                $perhours = $request->baseprice / $request->ticketLength;
            }

            if ($data['promotion']->no_of_times == '0') {
                $dayCount = ceil($request->ticketLength / self::FULL_DAY_HOUR_VAL);
                $applyHours = $dayCount * $data['promotion']->discount_value;
                $freeHours = $perhours * $applyHours;
            } else {
                $freeHours = $perhours * $data['promotion']->discount_value;
            }
            $payableAmount = $request->amount - $freeHours;
            $discountAmount = $freeHours;
            $data->discount_in_dollar = $discountAmount;
            $data->payable_amount = $payableAmount;
            //new payble amount Logic
            if ($payableAMountNewRate > 0) {
                $data->payable_amount = $payableAMountNewRate;
                $data->discount_in_dollar =  $request->amount - $data->payable_amount;
            }
            //    after all data set 
            dd($data->payable_amount, $data->discount_in_dollar, "ad");
        }
    }


    public static function getUpdatedRate($facility_id, $arrival, $length_of_stay)
    {
        // dd($facility_id, $arrival, $length_of_stay);
        $facility = Facility::with(['FacilityPaymentDetails', 'facilityConfiguration'])->find($facility_id);
        return $rate = $facility->rateForReservationOnMarker($arrival, $length_of_stay, false, false, null, false, false, '0', false);
    }
}
