<?php

namespace App\Classes;

/**
 * Generalized rate limited messaging class
 */
abstract class Messaging
{

    // custom message track that rate limits the number of text messages that can be sent per phone & ip-address
    public $rateLimiter;

    // Message parameters
    public $to;
    public $body;
    public $ip;

    public function __construct()
    {
        // Set up rate limiter
        $this->rateLimiter = new MessageRateLimiter();
    }

    /**
     * Set the phone number to send a message to
     *
     * @param [type] $to [description]
     */
    public function setTo($to)
    {
        $this->to = $to;
        return $this;
    }

    /**
     * Set IP address
     *
     * @return $this
     */
    public function setIP($ip)
    {
        $this->ip = $ip;
        return $this;
    }

    /**
     * Set a message body
     *
     * @param [type] $body [description]
     */
    public function setBody($body)
    {
        $this->body = $body;
        return $this;
    }
}
