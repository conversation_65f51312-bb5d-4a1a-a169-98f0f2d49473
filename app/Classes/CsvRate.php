<?php

namespace App\Classes;

use App\Models\RateCategory;
use App\Models\Facility;
use App\Models\Rate;
use App\Models\RateType;
use Exception;
use Storage;
use Validator;
use League\Csv\Reader;
use App\Exceptions\FileFormatException;

class CsvRate
{
    public $csv;
    private $header;
    private $failed_rates = [];
    private $updated_rates = [];
    private $created_rates = [];
    private $deleted_rates = [];
    private $inValidHeaders = [];
    private $errorMessage = "";
    private $row = [];
    const UPDATE = 'Update';
    const DELETE = 'Delete';
    const CREATE = 'Create';

    private $fillable = [ 'id', 'coupon_code', 'garage_code', 'category_name', 'rate_type', 'min_stay',
        'max_stay', 'price', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday',
        'active', 'description', 'entry_time_begin', 'entry_time_end', 'exit_time_begin', 'exit_time_end',
        'details', 'delete'
    ];

    public function __construct($csv = null)
    {
        ini_set('memory_limit', '1G');

        $this->csv = $csv;
    }

    /**
     * returns all rates that could not be updated/create/deleted/
     *
     * @return array
     */
    public function getFailedRates()
    {
        return $this->failed_rates;
    }

    /**
     * returns all updated rates
     *
     * @return array
     */
    public function getUpdatedRates()
    {
        return $this->updated_rates;
    }

    /**
     * returns all created rates
     *
     * @return array
     */
    public function getCreatedRates()
    {
        return $this->created_rates;
    }

    /**
     * retutns all deleted rates
     *
     * @return array
     */
    public function getDeletedRates()
    {
        return $this->deleted_rates;
    }

    /**
     * returns a csv row to insert into generated csv
     *
     * @param $rate
     */
    public function getRateData($rate)
    {
        if (!$facility = Facility::Find($rate->facility_id)) {
            return;
        }

        $category = RateCategory::Find($rate->category_id);
        $rate_type = RateType::Find($rate->rate_type_id);

        $currentCsv['facility_name'] = $facility->full_name;
        $currentCsv['id'] = $rate->id;
        $currentCsv['coupon_code'] = $rate->coupon_code;
        $currentCsv['garage_code'] = $facility->garage_code;
        $currentCsv['category_name'] = $category ? $category->category : "";
        $currentCsv['rate_type'] = $rate_type ? $rate_type->rate_type : "";
        $currentCsv['min_stay'] = $rate->min_stay ?: 0;
        $currentCsv['max_stay'] = $rate->max_stay ?: 0;
        $currentCsv['price'] = $rate->price ?: 0.0;
        $currentCsv['monday'] = $rate->monday;
        $currentCsv['tuesday'] = $rate->tuesday;
        $currentCsv['wednesday'] = $rate->wednesday;
        $currentCsv['thursday'] = $rate->thursday;
        $currentCsv['friday'] = $rate->friday;
        $currentCsv['saturday'] = $rate->saturday;
        $currentCsv['sunday'] = $rate->sunday;
        $currentCsv['active'] = $rate->active;
        $currentCsv['description'] = $rate->description ?: "";
        $currentCsv['entry_time_begin'] = $rate->entry_time_begin ?: "00:00:00";
        $currentCsv['entry_time_end'] = $rate->entry_time_end ?: "23:59:59";
        $currentCsv['exit_time_begin'] = $rate->exit_time_begin ?: "00:00:00";
        $currentCsv['exit_time_end'] = $rate->exit_time_end ?: "23:59:59";
        $currentCsv['details'] = $rate->details ?: "";
        $currentCsv['delete'] = 0;
        return $currentCsv;
    }

    /**
     * returns error message
     *
     * @return string
     */
    public function getErrorMessage()
    {
        return $this->errorMessage;
    }

    /**
     * returns csv of all rates
     *
     * @param $rates
     */
    public function downloadRatesCsv($rates)
    {
        // creates CSV File in memory
        $csv = \League\Csv\Writer::createFromFileObject(new \SplTempFileObject());

        // Adds first line in CSV as Database table column names
        $this->header = $this->fillable;
        array_unshift($this->header, 'facility_name');
        $csv->insertOne($this->header);

        // adds each rate column to the csv
        if (count($rates)) {
            foreach ($rates as $rate) {
                $currentCsv = $this->getRateData($rate);
                $csv->insertOne($currentCsv);
            }
        }
        $csv->output();
    }

    /**
     * main method for csv rate actions
     *
     * @return bool
     * @throws FileFormatException
     */
    public function updateRatesFromCsv()
    {
        $this->validateCsvExtension();

        $rateRows = Reader::createFromString(file_get_contents($this->csv));

        $this->checkRowsForNonUTF8Characters($rateRows);

        $this->header = array_map('strtolower', $rateRows->fetchOne());

        $this->validateHeader();

        $ratesArray = $rateRows->fetchAll(); // convert to array
        array_shift($ratesArray); //shift off the header row

        foreach ($ratesArray as $this->row) {
            $this->row = array_combine($this->header, $this->row); // creates and associate array with the header
            //Check to see if the ID is set
            $this->updateCreateRateCategory();
            $this->updateCreateRateType();
            $this->updateInOutTimes();
            if ($this->row['id'] != "" && $this->row['delete'] != "1") {
                //If there is a rate associated update it
                $this->updateRate();
            } elseif ($this->row['delete'] == "1") {
                $this->deleteRate();
            } else {
                $this->createRate();
            }
        }
        return true;
    }

    private function checkRowsForNonUTF8Characters($rows)
    {
        if (!json_encode($rows)) {
            throw new Exception("Invalid Upload: The CSV file Contains non UTF8 characters.");
        }
    }

    private function updateInOutTimes()
    {
        if (intval($this->row['entry_time_begin']) == 0 && intval($this->row['entry_time_end']) == 24) {
            $this->row['entry_time_end'] = "23:59:59";
        }
        if (intval($this->row['exit_time_begin']) == 0 && intval($this->row['exit_time_end']) == 24) {
            $this->row['exit_time_end'] = "23:59:59";
        }
    }
    /**
     * converts the rate category from the name to the id for database insertion
     */
    private function updateCreateRateCategory()
    {
        $categoryText = $this->row['category_name'];
        $category = RateCategory::where("category", "like", $categoryText)->get()->first();
        if (!$category) {
            $newCategory = new RateCategory();
            $newCategory->category = $categoryText;
            $newCategory->save();
            $this->row['category_id'] = $newCategory->id;
        } else {
            $this->row['category_id'] = $category->id;
        }
    }

    /**
     * converts the rate type from the name to the id for database insertion
     */
    private function updateCreateRateType()
    {
        $rateTypeText = $this->row['rate_type'];
        $rateType = RateType::where("rate_type", "like", $rateTypeText)->get()->first();
        if (!$rateType) {
            try {
                $boardRateType = RateType::where("rate_type", "like", 'Board Rates')->get()->first();
                $this->row['rate_type_id'] = $boardRateType->id;
            } catch (Exception $e) {
                $boardRateType = RateType::frist();
                $this->row['rate_type_id'] = $boardRateType->id;
            }
        } else {
            $this->row['rate_type_id'] = $rateType->id;
        }
    }

    /**
     * Updates rate
     */
    private function updateRate()
    {
        //if id is set get rate associated with it.
        $rate = Rate::find($this->row['id']);
        if ($rate) {
            try {
                $rate->fill(array_only($this->row, $rate->getFillable()));
                $rate->save();
                $this->updated_rates[] = $this->row;
            } catch (Exception $e) {
                $this->recordFailedRate($e->getMessage(), self::UPDATE);
            }
        } else {
            $this->recordFailedRate('ID does not exist', self::UPDATE);
        }
    }

    /**
     * deletes rate
     */
    private function deleteRate()
    {
        $rate = Rate::find($this->row['id']);
        if (!$rate) {
            $this->recordFailedRate('No rates found with that ID', self::DELETE);
        } else {
            try {
                $rate->delete();
                $this->deleted_rates[] = $this->row;
            } catch (Exception $e) {
                $this->recordFailedRate($e->getMessage(), self::DELETE);
            }
        }
    }

    /**
     * creates rate
     */
    private function createRate()
    {
        try {
            $facility = Facility::where('garage_code', $this->row['garage_code'])->get();
            if ($facility->first()) {
                $this->row['facility_id'] = $facility->first()->id;
                $rate = new Rate();
                $newRate = Rate::firstOrNew(array_only($this->row, $rate->getFillable()));
                if ($newRate->exists) {
                    $this->recordFailedRate('Rate already exist', self::CREATE);
                } else {
                    try {
                        $newRate->save();
                        $this->created_rates[] = $this->row;
                    } catch (Exception $e) {
                        $this->recordFailedRate($e->getMessage(), self::CREATE);
                    }
                }
            } else {
                $this->recordFailedRate('Garage code did not match any facilities', self::CREATE);
            }
        } catch (Exception $e) {
            $this->recordFailedRate($e->getMessage(), self::CREATE);
        }
    }

    /**
     * validates whether the file submitted has the correct headers
     *
     * @throws FileFormatException
     */
    private function validateHeader()
    {
        $this->inValidHeaders = array_filter(
            $this->fillable, function ($header) {
                return !in_array($header, $this->header);
            }
        );

        if (count($this->inValidHeaders)) {
            throw new FileFormatException('Missing column(s) '. implode(", ", $this->inValidHeaders));
        }
    }

    /**
     * validates the file extension submitted
     *
     * @return bool
     * @throws FileFormatException
     */
    private function validateCsvExtension()
    {
        $validation_array = ['csv_extension' => $this->csv->getClientOriginalExtension()];

        $validator = Validator::make(
            $validation_array, [
            'csv_extension' => 'in:csv',
            ]
        );

        if ($validator->fails()) {
            throw new FileFormatException('Incorrect File Type.');
        }
        return true;
    }

    /**
     * updates the failed rate array
     * and inserts the error message
     *
     * @param $errorMessage
     * @param $action
     */
    private function recordFailedRate($errorMessage, $action)
    {
        $failedRate['action'] = $action;
        $failedRate['rate_id'] = $this->row['id'];
        $failedRate['garage_code'] =  $this->row['garage_code'];
        $failedRate['error_message'] = $errorMessage;
        $this->failed_rates[] = $failedRate;
    }
}
