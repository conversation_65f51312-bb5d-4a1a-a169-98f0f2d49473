<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use App\Services\LoggerFactory;
use App\Models\AuthorizeNetTransaction;
use App\Models\Reservation;
use App\Models\FacilityPaymentType;
use App\Models\Facility;
use App\Models\Ticket;

/**
 * Utility class for interacting with the Ticketech APIs
 */

class DataTransGateway
{

    protected $log; 
    public function __construct()
    {
        $logFactory         =  new  LoggerFactory(); 
        $this->log = $logFactory->setPath('logs/parkengage/dataTrans')->createLogger('dataTrans');
    }

    protected static function baseCall($vars,$headers,$dataTransUrl)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL,$dataTransUrl);
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS,$vars);  //Post Fields
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

		$response = curl_exec ($curl);

		curl_close ($curl);
        $result = json_decode($response, TRUE);
        //$this->log->info("Payment Data --". json_encode($response));
		return $result;
    }
   
   
    public static function dataTransPaymentToken($facility, $request){
        $cent = $request->amount * 100;  
		$request_arr = [];
		
        $web_url = [
                "successUrl" => $facility->FacilityPaymentDetails->dataTrans_success_url,
                "cancelUrl"  => $facility->FacilityPaymentDetails->dataTrans_cancel_url,
                "errorUrl"   => $facility->FacilityPaymentDetails->dataTrans_error_url
            ]; 
		//dd($web_url);
		$request_arr['amount'] = $cent;
		$request_arr['currency'] = "USD";
		
        if($request->device_type=='web'){
            $request_arr['option'] =  (object) ['returnMobileToken' => false];
            $request_arr['redirect'] = $web_url;
		    $dataTransUserDetails = $facility->FacilityPaymentDetails->data_trans_username_web.":".$facility->FacilityPaymentDetails->data_trans_password_web;
        }else{
            $request_arr['option'] =  (object) ['returnMobileToken' => true];  
			$dataTransUserDetails = $facility->FacilityPaymentDetails->data_trans_username.":".$facility->FacilityPaymentDetails->data_trans_password;
			$request_arr['paymentMethods'] = [$request->paymentMethods];
		}
		
		$request_arr['refno'] = $facility->generateRandomString(16);
		$request_arr['autoSettle'] = false;
        
		// header prepare
        $dataTransUserEncrption = base64_encode($dataTransUserDetails);

		$headers = [
            'Authorization: Basic '. $dataTransUserEncrption,
            'Content-Type: application/json',
            'Accept: application/json',
            'Cache-Control: no-cache'
        ];
        
        //url
        $dataTransUrl = $facility->FacilityPaymentDetails->data_trans_url;
        $vars = json_encode($request_arr); 
		//$vars = $request_arr;
        //dd($headers,$dataTransUrl,$vars);
        return self::baseCall($vars,$headers,$dataTransUrl);
    }

    public static function saveDataTransTransaction($request, $user_id){
        $total = $request->total;
        $authorized_anet_transaction = new AuthorizeNetTransaction();
        $authorized_anet_transaction->sent = '1';
        $authorized_anet_transaction->user_id = $user_id;
        $authorized_anet_transaction->ip_address = \Request::ip();
        $authorized_anet_transaction->total = $total;
        $authorized_anet_transaction->description = "DataTrans payment: " . $user_id;
        $authorized_anet_transaction->anet_trans_hash = $request->mobileToken;
        $authorized_anet_transaction->anet_trans_id = $request->transaction_id;
        $authorized_anet_transaction->method = $request->paymentMethods;
        $authorized_anet_transaction->response_message ="Processed";    
        $authorized_anet_transaction->save();
        return $authorized_anet_transaction;
  
    }

    public static function dataTransPaymentRefund($reservation_id)
    {
        $reservation = Reservation::with('transaction','facility.FacilityPaymentDetails')->where("id", $reservation_id)->whereNotNull('anet_transaction_id')->first(); 
        if(isset($reservation) && !empty($reservation)){
            $tran_id = $reservation->transaction->anet_trans_id;
            $cent = ($reservation->total * 100);
            $cent = ($reservation->facility->FacilityPaymentDetails->data_trans_payment_env=='Test') ? '100': $cent;
							
            // header prepare
            $dataTransUserDetails = $reservation->facility->FacilityPaymentDetails->data_trans_username.":".$reservation->facility->FacilityPaymentDetails->data_trans_password;
            $dataTransUserEncrption = base64_encode($dataTransUserDetails);

            $headers = [
                'Authorization: Basic '. $dataTransUserEncrption,
                'Content-Type: application/json',
                'Accept: application/json',
                'Cache-Control: no-cache'
            ];

            $request_arr['amount'] = $cent;
		    $request_arr['currency'] = "CHF";
		    $request_arr['refno'] = $reservation->facility->generateRandomString(16);
            $vars = json_encode($request_arr); 
            //url
            $dataTransUrl = $reservation->facility->FacilityPaymentDetails->data_trans_url."/".$tran_id.'/credit';
            //dd($vars,$headers,$dataTransUrl);
            return self::baseCall($vars,$headers,$dataTransUrl);
        }else{
            return false;
        }
    }

    public static function dataTransSettleTransaction($reservation_id,$request)
    {
        $reservation = Reservation::with('facility.FacilityPaymentDetails')->where("id", $reservation_id)->first(); 
        if(isset($reservation) && !empty($reservation)){
            $tran_id = $request->transaction_id;
            $cent = ($reservation->total * 100);
            $cent = ($reservation->facility->FacilityPaymentDetails->data_trans_payment_env=='Test') ? '100': $cent;
							
            // header prepare
            $dataTransUserDetails = $reservation->facility->FacilityPaymentDetails->data_trans_username.":".$reservation->facility->FacilityPaymentDetails->data_trans_password;
            $dataTransUserEncrption = base64_encode($dataTransUserDetails);

            $headers = [
                'Authorization: Basic '. $dataTransUserEncrption,
                'Content-Type: application/json',
                'Accept: application/json',
                'Cache-Control: no-cache'
            ];

            $request_arr['amount'] = $cent;
		    $request_arr['currency'] = "USD";
		    $request_arr['refno'] = $reservation->facility->generateRandomString(16);
            $vars = json_encode($request_arr); 
            //url
            $dataTransUrl = $reservation->facility->FacilityPaymentDetails->data_trans_url."/".$tran_id.'/settle';
            //dd($vars,$headers,$dataTransUrl);
            return self::baseCall($vars,$headers,$dataTransUrl);
        }else{
            return false;
        }
    }

    public static function dataTransCancelTransaction($transaction_id,$facility_id)
    {
        $facility = Facility::with('FacilityPaymentDetails')->where("id", $facility_id)->first(); 
        if(isset($facility) && !empty($facility)){
            $tran_id = $transaction_id;
            // header prepare
            $dataTransUserDetails = $facility->FacilityPaymentDetails->data_trans_username.":".$facility->FacilityPaymentDetails->data_trans_password;
            $dataTransUserEncrption = base64_encode($dataTransUserDetails);

            $headers = [
                'Authorization: Basic '. $dataTransUserEncrption,
                'Content-Type: application/json',
                'Accept: application/json',
                'Cache-Control: no-cache'
            ];

            $request_arr['refno'] = $facility->generateRandomString(16);
            $vars = json_encode($request_arr); 
            //url
            $dataTransUrl = $facility->FacilityPaymentDetails->data_trans_url."/".$tran_id.'/cancel';
            //dd($vars,$headers,$dataTransUrl);
            return self::baseCall($vars,$headers,$dataTransUrl);
        }else{
            return false;
        }
    }

    public static function dataTransSettleTransactionTicket($ticket_id,$request)
    {
        $ticket = Ticket::with('facility.FacilityPaymentDetails')->where("id", $ticket_id)->first(); 
        if(isset($ticket) && !empty($ticket)){
            $tran_id = $request->transaction_id;
            $cent = ($ticket->grand_total * 100);
            $cent = ($ticket->facility->FacilityPaymentDetails->data_trans_payment_env=='Test') ? '100': $cent;
							
            // header prepare
            $dataTransUserDetails = $ticket->facility->FacilityPaymentDetails->data_trans_username_web.":".$ticket->facility->FacilityPaymentDetails->data_trans_password_web;
            $dataTransUserEncrption = base64_encode($dataTransUserDetails);

            $headers = [
                'Authorization: Basic '. $dataTransUserEncrption,
                'Content-Type: application/json',
                'Accept: application/json',
                'Cache-Control: no-cache'
            ];

            $request_arr['amount'] = $cent;
		    $request_arr['currency'] = "USD";
		    $request_arr['refno'] = $ticket->facility->generateRandomString(16);
            $vars = json_encode($request_arr); 
            //url
            $dataTransUrl = $ticket->facility->FacilityPaymentDetails->data_trans_url."/".$tran_id.'/settle';
            //dd($vars,$headers,$dataTransUrl);
            return self::baseCall($vars,$headers,$dataTransUrl);
        }else{
            return false;
        }
    }
}