<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use App\Services\LoggerFactory;
/**
 * Utility class for interacting with the Ticketech APIs
 */
class AbacusWebService
{

    protected $log; 
    public function __construct()
    {
        $logFactory         =  new  LoggerFactory(); 
        $this->log = $logFactory->setPath('logs/parkengage/abacus-gate-api')->createLogger('abacus-gate-api');
    }

    protected static function baseCall($endpoint, $params = null, $method = 'GET', $host)
    {
        /*$username = env('GATE_USERNAME');
        $password = env('GATE_PASSWORD');*/
        $base_url = $host;

        if (!$base_url) {
            throw new ApiGenericException('Host is not available.'); //
        }

        $url = "http://".$base_url . $endpoint;
        if ($method == 'GET' && $params !== null) {
            $url = $url . '?' . http_build_query($params);
        }

        $ch = curl_init($url);

        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        //curl_setopt($ch, CURLOPT_HTTPHEADER, array("arapi: " . $key));
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        if ($method == 'POST') {
            curl_setopt($ch, CURLOPT_POST, 1);
            if ($params !== null) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            }
        }

        $response = curl_exec($ch);
        if ($response != false) {
            $obj = new AbacusWebService();
            if(curl_getinfo($ch, CURLINFO_HTTP_CODE) == 404){
               $obj->logData($params, json_encode($response));
                $result['data'] = null;
                $result['status'] = null;
                $result['success'] = false;
                $result['error'] = curl_error($ch);
            }elseif(curl_getinfo($ch, CURLINFO_HTTP_CODE) == 200){
                $obj->logData($params, json_encode(simplexml_load_string($response)));
                $result['success'] = true;
                $json = json_encode(simplexml_load_string($response));
                $result['data'] = json_decode($json,TRUE);
                $result['status'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $result['error'] = null;
            }else{
                $obj->logData($params, json_encode($response));
                $result['data'] = null;
                $result['status'] = null;
                $result['success'] = false;
                $result['error'] = curl_error($ch);
            }         
            
        } else {

            $obj = new AbacusWebService();
            $obj->logData($params, json_encode($response));
            $result['data'] = null;
            $result['status'] = null;
            $result['success'] = false;
            $result['error'] = curl_error($ch);
        }

        return $result;
    }

    public function logData($params, $result)
    {
         $this->log->info('Abacus Service Class Log Params:'. json_encode($params));
         $this->log->info('Abacus Service Class Log Response :'.json_encode($result));
        
    }


    public static function isVehicleAvailable($params, $host)
    {
        return self::baseCall('/AbacusWebService/ServiceSystem.asmx/getApplicState', $params , 'POST', $host);
    }

    public static function commandStatus($params, $host)
    {
        return self::baseCall('/AbacusWebService/ServiceSystem.asmx/sendApplicCommandFull', $params , 'POST', $host);
    }


    public static function createGuid(){
        $guid = '';
        $namespace = rand(11111, 99999);
        $uid = uniqid('', true);
        $data = $namespace;
        $data .= $_SERVER['REQUEST_TIME'];
        $data .= $_SERVER['HTTP_USER_AGENT'];
        $data .= $_SERVER['REMOTE_ADDR'];
        $data .= $_SERVER['REMOTE_PORT'];
        $hash = strtoupper(hash('ripemd128', $uid . $guid . md5($data)));
        $guid = substr($hash,  0,  8) . '-' .
                substr($hash,  8,  4) . '-' .
                substr($hash, 12,  4) . '-' .
                substr($hash, 16,  4) . '-' .
                substr($hash, 20, 12);
        return $guid;
    }
}