<?php

namespace App\Classes;

use Services_Twilio;
use App\Exceptions\ApiGenericException;


class TextMessaging extends Messaging
{

    // regex for email and phone. should match those provided on the  .tpl page
    public static $phoneRegex = '/^[(]{0,1}[0-9]{3}[)]{0,1}[-\s\.]{0,1}[0-9]{3}[-\s\.]{0,1}[0-9]{4}$/';

    /**
     * Twilio account authentication variables
     *
     * @var [type]
     */
    protected $account_sid;
    protected $auth_token;
    protected $twilio_number;

    /**
     * Twilio Client
     *
     * @var Services_Twilio
     */
    protected $client;

    public function __construct()
    {
        parent::__construct();

        // Set up our API authentication vars
        $this->account_sid = config('twilio.account_sid');
        $this->auth_token = config('twilio.auth_token');
        $this->twilio_number = config('twilio.phone_number');

        // Default our IP to localhost for command line usage
        $this->ip = '127.0.0.1';

        // Set up our client
        $this->client = new Services_Twilio($this->account_sid, $this->auth_token);
    }

    /**
     * Set the phone number to send a message to
     *
     * @param [type] $to [description]
     */
    public function setTo($to)
    {
        if (!self::validatePhoneNumber($to)) {
            throw new ApiGenericException('Phone number invalid.');
        }

        $this->to = $to;
        return $this;
    }

    /**
     * Send text message through twilio
     *
     * @param  [type] $to   Phone number to send to
     * @param  [type] $body Body of the message
     * @return [type]       [description]
     */
    public function send()
    {
        if (!$this->to || !$this->body) {
            throw new ApiGenericException('Set to and body before sending a message.');
        }

        // Check we are not over the rate limit
        if ($this->rateLimiter->isOverRateLimit($this->to, $this->ip)) {
            throw new ApiGenericException('You have sent too many text messages to this number, please wait before sending.');
        }

        // Send the message
        $this->client->account->messages->create(
            [
            'To' =>  $this->to,
            'From' => $this->twilio_number,
            'Body' => html_entity_decode(strip_tags($this->body))
            ]
        );

        // Track sent message
        $this->rateLimiter->track($this->to, $this->ip);

        return true;
    }

    /**
     * Validate the phone number
     *
     * @param  [type] $phone [description]
     * @return [type]        [description]
     */
    public static function validatePhoneNumber($phone)
    {
        return preg_match(self::$phoneRegex, $phone);
    }
}
