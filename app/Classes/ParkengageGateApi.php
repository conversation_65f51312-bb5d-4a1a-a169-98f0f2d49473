<?php

namespace App\Classes;

use App\Exceptions\ApiGenericException;
use App\Services\LoggerFactory;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Psr\Http\Message\ResponseInterface;

class ParkengageGateApi
{
    protected static $log;

    /**
     * Initialize the logger.
     */
    public static function initLogger()
    {
        if (!self::$log) {
            $logFactory = new LoggerFactory();
            self::$log = $logFactory->setPath('logs/parkengage/parkengage-gate-api')->createLogger('parkengage-gate-api');
        }
    }

    /**
     * Base API call using Guzzle.
     *
     * @param string $endpoint
     * @param array|null $params
     * @param string $method
     * @param string $host
     * @return array
     * @throws ApiGenericException
     */
    protected static function baseCall($endpoint, $params = null, $method = 'GET', $host)
    {
        self::initLogger(); // Ensure logger is initialized

        if (!$host) {
            throw new ApiGenericException('Host is not available.');
        }

        $client = new Client([
            'timeout' => 30, // Timeout in seconds
            'verify' => false, // Skip SSL Verification
        ]);

        $url = $host . $endpoint;
        $options = ['http_errors' => false]; // Allow error responses to be handled manually

        if ($method === 'POST') {
            $options['form_params'] = $params;
        } elseif ($method === 'GET' && $params !== null) {
            $options['query'] = $params;
        }

        try {
            $response = $client->request($method, $url, $options);
            return self::handleResponse($response, $params, $url);
        } catch (RequestException $e) {
            self::logError($params, $e->getMessage());
            return [
                'success' => false,
                'data' => null,
                'status' => null,
                'error' => $e->getMessage(),
                'host_url' => $url,
            ];
        }
    }

    /**
     * Handle API response and log the results.
     *
     * @param ResponseInterface $response
     * @param array|null $params
     * @param string $url
     * @return array
     */
    protected static function handleResponse(ResponseInterface $response, $params, $url)
    {
        $statusCode = $response->getStatusCode();
        $responseBody = (string) $response->getBody();
        $data = $statusCode === 200 ? json_decode(json_encode(simplexml_load_string($responseBody)), true) : null;

        self::logData($params, $responseBody, $statusCode);

        return [
            'success' => $statusCode === 200,
            'data' => $data,
            'status' => $statusCode,
            'host_url' => $url,
            'error' => $statusCode === 200 ? null : $responseBody,
        ];
    }

    /**
     * Log request and response data.
     *
     * @param array|null $params
     * @param string $result
     * @param int $statusCode
     */
    protected static function logData($params, $result, $statusCode)
    {
        self::$log->info('ParkEngage Gate API Class Log Params: ' . json_encode($params));
        self::$log->info('ParkEngage Gate API Class Log Response: ' . $result . ' Status Code: ' . $statusCode);
    }

    /**
     * Log errors.
     *
     * @param array|null $params
     * @param string $errorMessage
     */
    protected static function logError($params, $errorMessage)
    {
        self::$log->error('ParkEngage Gate API Error Params: ' . json_encode($params));
        self::$log->error('ParkEngage Gate API Error Message: ' . $errorMessage);
    }

    /**
     * API endpoint methods.
     */
    public static function isVehicleAvailable($params, $host)
    {
        return self::baseCall('/ParkEngageGateAPIs.asmx/IsVehiclePresent', $params, 'POST', $host);
    }

    public static function openGate($params, $host)
    {
        return self::baseCall('/ParkEngageGateAPIs.asmx/OpenGate', $params, 'POST', $host);
    }

    public static function isGateDown($params, $host)
    {
        return self::baseCall('/ParkEngageGateAPIs.asmx/CheckAdamConnection', $params, 'POST', $host);
    }

    public static function isGateAlreadyOpen($params, $host)
    {
        return self::baseCall('/ParkEngageGateAPIs.asmx/IsGateOpened', $params, 'POST', $host);
    }

    public static function openExternalGate($params, $host)
    {
        return self::baseCall('/ParkEngageGateAPIs.asmx/OpenExternalGate', $params, 'POST', $host);
    }
     //UPBL-87
    public static function SetLotStatus($params, $host)
    {
        return self::baseCall('/ParkEngageGateAPIs.asmx/SetLotStatus', $params, 'POST', $host);
    }
}