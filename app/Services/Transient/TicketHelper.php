<?php

namespace App\Services\Transient;

use App\Exceptions\ApiGenericException;
use App\Models\AuthorizeNetTransaction;
use App\Models\Facility;
use App\Models\FastTrackVehicleMapping;
use App\Models\PermitVehicle;
use App\Models\Ticket;
use App\Models\UserFastTrackDetails;
use App\Services\LoggerFactory;
use Carbon\Carbon;

class TicketHelper
{
    protected $log;

    public function __construct()
    {
        // $this->request = $request;
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/TicketHelper')->createLogger('helper'); // VP:PIMS-14662
    }

    public function getUTCTime($time, $facility)
    {
        $UTCTime = Carbon::parse($time, $facility->timezone)->setTimezone('UTC')->format('Y-m-d H:i:s');
        return $UTCTime;
    }

    public function getUTCToFacilityTimezone($time, $facility)
    {
        $time = Carbon::parse($time, 'UTC')->setTimezone($facility->timezone)->format('Y-m-d H:i:s');
        return $time;
    }

    //tickets number is total 7 digit number for kstreet
    protected function checkTicketNumber($facility_id = '')
    {
        if ($facility_id == '') {
            $ticket = 'WP' . rand(100, 999) . rand(100, 999);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $ticket = $this->checkTicketNumber();
            }
        } else {
            $facility = Facility::find($facility_id);
            $prefix = $facility->ticket_prefix != '' ? $facility->ticket_prefix : "PE";
            $fromRange = $facility->from_range != '' ? $facility->from_range : 100000;
            $toRange = $facility->to_range != '' ? $facility->to_range : 999999;
            $ticket = $prefix . rand($fromRange, $toRange);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $ticket =  $this->checkTicketNumber($facility_id);
            }
        }

        return $ticket;
    }

    public function createTicket($request, $facility)
    {
        $ticket = new Ticket();
        $ticket->check_in_datetime  = $request->checkin_time;
        $ticket->checkin_time       = $request->checkin_time;
        $ticket->checkout_time      = Carbon::parse('now')->format('Y-m-d H:i:s');
        $ticket->checkout_datetime  = Carbon::parse('now')->format('Y-m-d H:i:s');
        $ticket->estimated_checkout = $request->estimated_checkout;
        $ticket->facility_id        = $facility->id;
        $ticket->partner_id         = $facility->owner_id;
        $ticket->ticket_number      = $this->checkTicketNumber($facility->id);
        $ticket->license_plate      = $request->license_plate;
        $ticket->checkin_gate       = $request->gate;
        $ticket->device_type        = "IM30";
        $ticket->user_id            = $request->user_id;
        $ticket->session_id         = $request->session_id;
        $ticket->total              = $request->total;
        $ticket->grand_total        = $request->grand_total;
        $ticket->parking_amount     = $request->parking_amount;
        $ticket->tax_fee            = $request->tax_fee;
        $ticket->processing_fee     = $request->processing_fee;
        $ticket->additional_fee     = $request->additional_fee;
        $ticket->surcharge_fee      = $request->surcharge_fee;
        $ticket->payment_token      = $request->session_id;
        $ticket->rate_id            = $request->rate_id;
        $ticket->rate_description   = $request->rate_description;
        $ticket->is_checkin         = '1';
        $ticket->save();
        $this->log->info("before checkin 20");
        return $ticket;
    }

    public function getAmountDue($request, $facility)
    {
        $arrival_time           = $request->checkin_time;
        $estimatedCheckout      = $request->estimated_checkout;

        $diffInRealhours = Carbon::parse($arrival_time)->diffInRealhours(Carbon::parse($estimatedCheckout));
        $diffInRealMints = Carbon::parse($arrival_time)->diffInRealMinutes(Carbon::parse($estimatedCheckout));
        $stayOfLength = $diffInRealhours;
        if ($diffInRealhours > 0) {
            $remainingMinutes = $diffInRealMints % 60;
            $stayOfLength = $diffInRealhours . '.' . $remainingMinutes;
        } else {
            $stayOfLength =  '0.' . $diffInRealMints;
        }

        $diff_in_hours          = $stayOfLength;
        $isMember = 0;
        if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
            $rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
        } else {
            $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, '0', $isMember);
        }

        return $rate;
    }

    public function isFastTrackExists($request, $facility)
    {
        // dd('isFastTrackExists', $request->all(), $facility);
        if (isset($request->license_plate)) {
            $PermitVehicle = PermitVehicle::where(['license_plate_number' => $request->license_plate, 'partner_id' => $facility->owner_id])->first();
            if ($PermitVehicle) {
                $fastTrackVehicleMapping = FastTrackVehicleMapping::where(['permit_vehicle_id' => $PermitVehicle->id])->first();

                if ($fastTrackVehicleMapping) {
                    $userFastTrackDetails = UserFastTrackDetails::find($fastTrackVehicleMapping->fast_track_id);
                    // dd($PermitVehicle, $fastTrackVehicleMapping, $userFastTrackDetails);
                    if ($userFastTrackDetails) {
                        return $userFastTrackDetails;
                    }
                }
            }
        }
        return false;
    }

    public function isTicketExits($type, $value)
    {
        // This function will return base on params
        if ($type == 'ticket_number') {
            $ticket = Ticket::where('ticket_number', $value)->first();
        } elseif ($type == 'license_plate') {
            $ticket = Ticket::where('license_plate', $value)->first();
        }
        if ($ticket)
            return $ticket;
        else
            return false;
    }

    public function getLenghtOfStay($inTime, $outTime)
    {

        $diffInRealhours = Carbon::parse($inTime)->diffInRealhours(Carbon::parse($outTime));
        $diffInRealMints = Carbon::parse($inTime)->diffInRealMinutes(Carbon::parse($outTime));
        $stayOfLength = $diffInRealhours;

        if ($diffInRealhours > 0) {
            $remainingMinutes = $diffInRealMints % 60;
            $stayOfLength = $diffInRealhours . '.' . $remainingMinutes;
        } else {
            $stayOfLength =  '0.' . $diffInRealMints;
        }

        return $stayOfLength;
    }

    function savePaymentTransaction($request, $facility, $ticket)
    {
        $this->log->info("payment details request --" . json_encode($request->payment_details));
        $authorized_anet_transaction = new AuthorizeNetTransaction();
        $authorized_anet_transaction->sent = '1';
        $authorized_anet_transaction->user_id = $ticket->user_id;
        $authorized_anet_transaction->total = $request->payment_details['TransactionAmount'];
        $authorized_anet_transaction->name = $request->payment_details['MerchantName'];
        $authorized_anet_transaction->description = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
        $authorized_anet_transaction->response_message = $request->payment_details['ProcessorMessage'];
        $authorized_anet_transaction->expiration = $request->payment_details['expiry'];
        $authorized_anet_transaction->card_type = $request->payment_details['CardType'];
        $authorized_anet_transaction->ref_id = $request->payment_details['processorReference'];
        $authorized_anet_transaction->anet_trans_id = $request->payment_details['TransactionID'];
        $authorized_anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
        $authorized_anet_transaction->method = "card";
        $authorized_anet_transaction->reader_used = $request->payment_details['ReaderUsed'];
        $authorized_anet_transaction->save();


        $ticket->anet_transaction_id = $authorized_anet_transaction->id;
        $ticket->total = $request->payment_details['TransactionAmount'];
        $ticket->grand_total = $request->payment_details['TransactionAmount'];
        $ticket->terminal_id = $request->payment_details['TerminalID'];
        $ticket->save();

        return $authorized_anet_transaction;
    }
}
