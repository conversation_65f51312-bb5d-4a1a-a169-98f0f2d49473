<?php

namespace App\Services\Transient;

use App\Exceptions\ApiGenericException;
use App\Http\Helpers\QueryBuilder;
use App\Models\AuthorizeNetTransaction;
use App\Models\Facility;
use App\Models\FastTrackVehicleMapping;
use App\Models\PermitVehicle;
use App\Models\Ticket;
use App\Models\UserFastTrackDetails;
use App\Services\LoggerFactory;
use Carbon\Carbon;
use App\Models\ParkEngage\TicketExtend;
use GuzzleHttp\Client;
use Twilio\Exceptions\RestException;

class TicketHelper
{
    protected $log;

    public function __construct()
    {
        // $this->request = $request;
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/TicketHelper')->createLogger('helper'); // VP:PIMS-14662
    }

    public function getUTCTime($time, $facility)
    {
        $UTCTime = Carbon::parse($time, $facility->timezone)->setTimezone('UTC')->format('Y-m-d H:i:s');
        return $UTCTime;
    }

    public function getUTCToFacilityTimezone($time, $facility)
    {
        $time = Carbon::parse($time, 'UTC')->setTimezone($facility->timezone)->format('Y-m-d H:i:s');
        return $time;
    }

    //tickets number is total 7 digit number for kstreet
    protected function checkTicketNumber($facility_id = '')
    {
        if ($facility_id == '') {
            $ticket = 'WP' . rand(100, 999) . rand(100, 999);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $ticket = $this->checkTicketNumber();
            }
        } else {
            $facility = Facility::find($facility_id);
            $prefix = $facility->ticket_prefix != '' ? $facility->ticket_prefix : "PE";
            $fromRange = $facility->from_range != '' ? $facility->from_range : 100000;
            $toRange = $facility->to_range != '' ? $facility->to_range : 999999;
            $ticket = $prefix . rand($fromRange, $toRange);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $ticket =  $this->checkTicketNumber($facility_id);
            }
        }

        return $ticket;
    }

    public function createTicket($request, $facility)
    {
        $ticket = new Ticket();
        $ticket->check_in_datetime  = $request->checkin_time;
        $ticket->checkin_time       = $request->checkin_time;
        $ticket->checkout_time      = $request->estimated_checkout;
        $ticket->checkout_datetime  = $request->estimated_checkout;
        $ticket->estimated_checkout = $request->estimated_checkout;
        $ticket->facility_id        = $facility->id;
        $ticket->partner_id         = $facility->owner_id;
        $ticket->ticket_number      = $this->checkTicketNumber($facility->id);
        $ticket->license_plate      = $request->license_plate;
        $ticket->checkin_gate       = $request->gate;
        $ticket->device_type        = "IM30";
        $ticket->user_id            = $request->user_id;
        $ticket->session_id         = $request->session_id;
        $ticket->total              = $request->total;
        $ticket->grand_total        = $request->grand_total;
        $ticket->parking_amount     = $request->parking_amount;
        $ticket->tax_fee            = $request->tax_fee;
        $ticket->processing_fee     = $request->processing_fee;
        $ticket->additional_fee     = $request->additional_fee;
        $ticket->surcharge_fee      = $request->surcharge_fee;
        $ticket->payment_token      = $request->session_id;
        $ticket->rate_id            = $request->rate_id;
        $ticket->rate_description   = $request->rate_description;
        $ticket->is_checkin         = '1';
        $ticket->save();
        $this->log->info("before checkin 20");
        return $ticket;
    }

    public function createOverstay($request, $facility)
    {
        $ticket = new TicketExtend();
        $ticket->checkin_time       = $request->checkin_time;
        $ticket->checkout_time      = $request->estimated_checkout;
        $ticket->facility_id        = $facility->id;
        $ticket->partner_id         = $facility->owner_id;
        $ticket->ticket_number      = $request->ticket_number;
        $ticket->total              = $request->total;
        $ticket->grand_total        = $request->grand_total;
        $ticket->parking_amounts    = $request->parking_amount;
        $ticket->tax_fee            = $request->tax_fee;
        $ticket->processing_fee     = $request->processing_fee;
        $ticket->additional_fee     = $request->additional_fee;
        $ticket->surcharge_fee      = $request->surcharge_fee;
        $ticket->ticket_id          = $request->ticket_id;
        $ticket->save();
        $this->log->info("before checkin 20");
        return $ticket;
    }

    /**
     * To use this method, the required request parameters are:
     * - checkin_time
     * - stayOfLength
     */
    public function getAmountDue($request, $facility)
    {
        if (empty($request->checkin_time)) {
            throw new ApiGenericException('checkin time is required.');
        }
        if (empty($request->length_of_stay)) {
            throw new ApiGenericException('Length of stay is required.');
        }
        $arrival_time           = $request->checkin_time;
        $lenghtOfStay           = $request->length_of_stay;
        $isMember = 0;
        if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
            $rate = $facility->rateForReservationByPassRateEngine($arrival_time, $lenghtOfStay, false, true, null, false, false, '0', $isMember);
        } else {
            $rate = $facility->rateForReservationOnMarker($arrival_time, $lenghtOfStay, false, true, false, true, false, '0', $isMember);
        }

        return $rate;
    }

    public function isFastTrackExists($request, $facility)
    {
        // dd('isFastTrackExists', $request->all(), $facility);
        if (isset($request->license_plate)) {
            $PermitVehicle = PermitVehicle::where(['license_plate_number' => $request->license_plate, 'partner_id' => $facility->owner_id])->first();
            if ($PermitVehicle) {
                $fastTrackVehicleMapping = FastTrackVehicleMapping::where(['permit_vehicle_id' => $PermitVehicle->id])->first();

                if ($fastTrackVehicleMapping) {
                    $userFastTrackDetails = UserFastTrackDetails::find($fastTrackVehicleMapping->fast_track_id);
                    // dd($PermitVehicle, $fastTrackVehicleMapping, $userFastTrackDetails);
                    if ($userFastTrackDetails) {
                        return $userFastTrackDetails;
                    }
                }
            }
        }
        return false;
    }

    public function isTicketExits($type, $value)
    {
        // This function will return base on params
        if ($type == 'ticket_number') {
            $ticket = Ticket::where('ticket_number', $value)->first();
        } elseif ($type == 'license_plate') {
            $ticket = Ticket::where('license_plate', $value)->first();
        }
        if ($ticket)
            return $ticket;
        else
            return false;
    }

    public function getLenghtOfStay($inTime, $outTime)
    {

        $diffInRealhours = Carbon::parse($inTime)->diffInRealhours(Carbon::parse($outTime));
        $diffInRealMints = Carbon::parse($inTime)->diffInRealMinutes(Carbon::parse($outTime));
        $stayOfLength = $diffInRealhours;

        if ($diffInRealhours > 0) {
            $remainingMinutes = $diffInRealMints % 60;
            $stayOfLength = $diffInRealhours . '.' . $remainingMinutes;
        } else {
            $stayOfLength =  '0.' . $diffInRealMints;
        }

        return $stayOfLength;
    }

    public function savePaymentTransaction($request, $facility, $ticket)
    {
        $this->log->info("payment details request --" . json_encode($request->payment_details));
        $anet_transaction = new AuthorizeNetTransaction();
        $anet_transaction->sent              = '1';
        $anet_transaction->user_id           = $ticket->user_id;
        $anet_transaction->total             = $request->payment_details['TransactionAmount'];
        $anet_transaction->name              = $request->payment_details['MerchantName'];
        $anet_transaction->description       = "Drive-Up Payment Done Ticket ID : " . $ticket->id;
        $anet_transaction->response_message  = $request->payment_details['ProcessorMessage'];
        $anet_transaction->expiration        = $request->payment_details['expiry'];
        $anet_transaction->card_type         = $request->payment_details['CardType'];
        $anet_transaction->ref_id            = $request->payment_details['processorReference'];
        $anet_transaction->anet_trans_id     = $request->payment_details['TransactionID'];
        $anet_transaction->payment_last_four = substr($request->payment_details['MaskedPAN'], -4);
        $anet_transaction->method            = "card";
        $anet_transaction->reader_used       = $request->payment_details['ReaderUsed'];
        $anet_transaction->save();


        $ticket->anet_transaction_id    = $anet_transaction->id;
        $ticket->total                  = $request->payment_details['TransactionAmount'];
        $ticket->grand_total            = $request->payment_details['TransactionAmount'];
        $ticket->terminal_id            = $request->payment_details['TerminalID'];
        $ticket->save();

        return $anet_transaction;
    }

    public function isAnetExits($key, $value)
    {
        $anetTransaction = AuthorizeNetTransaction::where($key, $value)->first();
        if ($anetTransaction)
            return true;
        else
            return false;
    }

    public function getFormatedLenghtOfStay($startDate, $endDate)
    {
        $length_of_stay = '';
        $diff_in_days = $startDate->diffInDays($endDate);
        $diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
        $diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);
        if ($diff_in_days > 0) {
            if ($diff_in_days == 1) {
                $length_of_stay .= $diff_in_days . " day ";
            } else {
                $length_of_stay .= $diff_in_days . " days ";
            }
        }
        if ($diff_in_hours > 0) {
            $length_of_stay .= $diff_in_hours . " hr ";
        }

        if ($diff_in_minutes > 0) {
            $length_of_stay .= $diff_in_minutes . " min";
        }
        return $length_of_stay;
    }

    public function customeReplySms($msg, $phone, $imageURL = '')
    {
        try {
            if ($phone == '') {
                return "success";
            }
            $accountSid = config('parkengage.TWILIO_ACCOUNT_SID');
            $authToken  = config('parkengage.TWILIO_AUTH_TOKEN');
            $client = new Client($accountSid, $authToken);
            try {
                if (QueryBuilder::checkValidMobileLength($phone)) {
                    // Use the client to do fun stuff like send text messages!
                    $client->messages->create(
                        // the number you'd like to send the message to
                        $phone,
                        array(
                            // A Twilio phone number you purchased at twilio.com/console
                            'from' => env('TWILIO_PHONE'),
                            // the body of the text message you'd like to send
                            //'body' => "Fine"
                            'body' => "$msg",
                            //'mediaUrl' => storage_path('app/' . $imageBarcodeFileName)
                        )
                    );
                    $this->log->info("Message : {$msg} sent to $phone");
                }
                return "success";
            } catch (RestException $e) {
                //echo "Error: " . $e->getMessage();
                $this->log->error($e->getMessage());
                return "success";
            }
        } catch (RestException $e) {
            //echo "Error: " . $e->getMessage();
            $this->log->error($e->getMessage());
            return "success";
        }
    }
}
