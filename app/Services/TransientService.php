<?php

namespace App\Services;

// use App\Services\PaymentGateways\PaymentGatewayFactory;

use App\Classes\Inventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Http\Helpers\QueryBuilder;
use App\Models\CustomText;
use App\Models\Facility;
use App\Models\FacilityAvailability;
use App\Models\OauthClient;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\ParkEngage\FacilityConfiguration;
use Carbon\Carbon;

// use Psr\Log\LoggerInterface;
use App\Models\ParkEngage\LprTicketMapping;

class TransientService
{

    const TWENTY_FOUR_HOURS = 23;
    const REALTIME_WINDOW   = 2;
    const DEFAULT_VALUE  = 0;
    const IS_FACILITY_CLOSED  = 1;
    const IS_FACILITY_OPEN  = 0;
    const DEFAULT_WEB  = "web";
    const DEFAULT_VALUE_STR  = "0";
    const DEFAULT_VALUE_ONE  = 1;
    const DEFAULT_MSG_ONE = 1;
    const DEFAULT_PERCENTAGE_FLAG = 1;
    const DEFAULT_THRESHOLD_FLAG_IGNORE = 2;
    const DEFAULT_MSG_TWO = 2;
    const DEFAULT_MSG_THREE = 3;
    const DEFAULT_MSG_FOUR = 4;
    const ADD_EXTRA_DAY_COUNT = 1;
    const COUPON_RATE   = 2;
    const DEFAULT_TIME      = '0000-00-00 00:00:00';
    const MIN_AVAILABILITY  = 5;
    const LIMITED_SPACE_MSG = "Limited spots still available at this price.";
    const SOME_SPACE_MSG = "Some spots are available.";
    const No_SPACE_MSG = "All slots are sold out at this moment.";
    const NON_WORKING_HOUR_CLOSED = "Facility does not work at this time.";

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    protected $log;
    protected $request;
    protected $gateway;
    protected $optionArr;
    protected $gatewayType;   ### [ 1, 2, 3, 4 ]
    protected $ticket;    ### [ ticket, Reservation, Permit, event, pass,  ]
    protected $partnerId;
    protected $customMessage;

    public function __construct()
    {
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/Service')->createLogger('TransientService');
        // dd($request->ticket->facility->FacilityPaymentDetails->facility_payment_type_id);
        // dd($request->all());
        $this->partnerId  = 0;
        $this->customMessage  = false;
    }
    // All Make sure before using this servvice need to valisation request in controller. 

    public function updateRateInformationWithAvailibilty($request, Facility $facility)
    {
        $this->log->info("Get Availibilty");
        $returnResultArr = array();

        $returnResultArr['coupon_threshold_price'] = 0;
        $returnResultArr['is_coupon_threshold_price_percentage'] = 0;
        $returnResultArr['availability'] = 0;
        $returnResultArr['is_coupon_threshold_applied'] = 0;

        $inventory = new Inventory();
        // $date_time_out = Carbon::parse($request->arrival_time)->addHOurs((number_format($request->length_of_stay, 2) * 60));
        $date_time_out = Carbon::parse($request->arrival_time)->addHours($request->length_of_stay);

        $realtimeWindow = $facility->realtime_window;
        $realtimeWindow = $realtimeWindow ? $realtimeWindow : self::REALTIME_WINDOW;

        $timeDifference = date_diff(date_create($request->arrival_time), Carbon::now());

        $isAvailable = true;

        $thresholdAvailability = self::DEFAULT_VALUE;
        if ($isAvailable == true) {
            //check how many slots does entry and exit time occupies
            //$difference = date_diff(date_create($request->arrival_time), date_create($date_time_out));
            $difference = date_diff(date_create(date('Y-m-d', strtotime($request->arrival_time))), date_create(date('Y-m-d', strtotime(($date_time_out)))));
            $this->log->info("Difference Update: " . json_encode($difference->d > 0));
            if ($difference->d > 0) {
                //                $dates   = $inventory->generateArrayOfDates(
                //                '', date($request->arrival_time), date($date_time_out));

                $dates   = $inventory->generateArrayOfDates(
                    ($difference->d + self::ADD_EXTRA_DAY_COUNT),
                    date('Y-m-d H:i:s', strtotime($request->arrival_time))
                );

                $dayDifference = $difference->d;

                foreach ($dates as $key => $date) {
                    $facilityAvailability = FacilityAvailability::where(['facility_id' => $facility->id, 'date' => $date->format('Y-m-d')])->first();

                    if ($facilityAvailability) {
                        $inventory = json_decode($facilityAvailability->availability);
                        $this->log->info("Before Update Availability  Key : {$key} : " . json_encode($inventory));
                        if ($key == 0) {
                            /**
                             * because this is the first day in the dates provided
                             * we should check from each time_slot starting
                             * from the hour provided in the api call
                             */
                            $i = date('G', strtotime($request->arrival_time));
                            while ($i <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$i})) {
                                    if ($inventory->{$i} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$i}) {
                                            $thresholdAvailability = $inventory->{$i};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$i};
                                    }
                                    $this->log->info("Check while loop  : {$i} : " . json_encode($thresholdAvailability));
                                }
                                $i++;
                            }
                        } elseif ($key == $dayDifference) {
                            $i = date('G', strtotime($date_time_out));
                            $minutes = date('i', strtotime($date_time_out));
                            if ($minutes >= 30) {
                                $i++;
                            }
                            /**
                             * because this is the last day in the dates provided
                             * we should check from each time_slot starting
                             * till the hour provided in the api call
                             */
                            $j = 0;
                            while ($j < $i) {
                                if (isset($inventory->{$j})) {
                                    if ($inventory->{$j} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$j}) {
                                            $thresholdAvailability = $inventory->{$j};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$j};
                                    }
                                }
                                $j++;
                            }
                        } else {
                            /**
                             * because this could be any day except first and last in
                             * the dates provided we should check from whole day
                             */
                            $k = 0;
                            while ($k <= self::TWENTY_FOUR_HOURS) {
                                if (isset($inventory->{$k})) {
                                    if ($inventory->{$k} < 1) {
                                        $isAvailable = false;
                                        break;
                                    }
                                    if ($thresholdAvailability > 0) {
                                        if ($thresholdAvailability > $inventory->{$k}) {
                                            $thresholdAvailability = $inventory->{$k};
                                        }
                                    } else {
                                        $thresholdAvailability = $inventory->{$k};
                                    }
                                }
                                $k++;
                            }
                        }
                    }
                }
            } else {
                $startingHour = date('G', strtotime($request->arrival_time));
                $endingHour   = date('G', strtotime($date_time_out));
                $facilityAvailability     = FacilityAvailability::where(
                    ['facility_id' => $facility->id, 'date' => date('Y-m-d', strtotime($request->arrival_time))]
                )->first();
                // dd($request->arrival_time, $startingHour, $endingHour, date('Y-m-d', strtotime($request->arrival_time)), $facilityAvailability);
                if ($facilityAvailability) {
                    $availability = json_decode($facilityAvailability->availability, true);
                    $this->log->info("Before Update Availability: " . json_encode($availability));
                    while ($startingHour <= $endingHour) {
                        if (isset($availability[$startingHour])) {
                            if (($availability[$startingHour] < 1)) {
                                $this->log->info("Not Availabe for this hours: " . json_encode($availability[$startingHour]));
                                $isAvailable = false;
                            }
                            if ($thresholdAvailability > 0) {
                                if ($thresholdAvailability > $availability[$startingHour]) {
                                    $thresholdAvailability = $availability[$startingHour];
                                }
                            } else {
                                $thresholdAvailability = $availability[$startingHour];
                            }
                        }
                        $startingHour++;
                    }
                }
            }

            if ($thresholdAvailability < self::DEFAULT_VALUE) {
                $thresholdAvailability = self::DEFAULT_VALUE;
            }

            $this->log->info("Reached 121112323: " . json_encode($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)));
            if ($isAvailable == true || ($thresholdAvailability == self::DEFAULT_VALUE)) {

                $dateIn = date('Y-m-d', strtotime($request->arrival_time));
                $facilityAvailability =  FacilityAvailability::where('facility_id', $facility->id)->where('date', $dateIn)->first();

                if ($facilityAvailability) {
                    //$availabilities = json_decode($facilityAvailability->availability, true);

                    if ($thresholdAvailability >= 0) {

                        $couponThresholdsNew = $facility->facilityCouponThreshold;
                        $couponThresholdsNew = $couponThresholdsNew->sortBy('threshold');

                        $thresholds       = array();
                        if ($couponThresholdsNew) {
                            $thresholdCounter = self::DEFAULT_VALUE;
                            foreach ($couponThresholdsNew as $couponThreshold) {

                                if ($couponThreshold->uptick_type !== 'deleted') {
                                    $thresholds[$thresholdCounter] =
                                        ['threshold' => $couponThreshold->threshold, 'uptick' => $couponThreshold->uptick, 'uptick_type' => $couponThreshold->uptick_type];
                                    $thresholdCounter++;
                                }
                            }
                        }
                        $thresholdPrice = 0;
                        $currentAvailability = $thresholdAvailability;
                        foreach ($thresholds as $key => $threshold) {
                            if ($thresholdAvailability <= $threshold['threshold']) {
                                if ($threshold['uptick'] > 0 && $thresholdAvailability >= 0) {
                                    if ($threshold['uptick_type'] == 'price') {
                                        $thresholdPrice = $threshold['uptick'];

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    } else if ($threshold['uptick_type'] == 'percentage') {
                                        $thresholdPrice =  $threshold['uptick'];
                                        $returnResultArr['is_coupon_threshold_price_percentage'] = self::DEFAULT_PERCENTAGE_FLAG;

                                        $returnResultArr['is_coupon_threshold_applied'] = 1;
                                        break;
                                    }
                                }
                            }
                        }
                        $returnResultArr['coupon_threshold_price'] = $thresholdPrice;

                        $returnResultArr['availability'] = $currentAvailability;
                    }
                }
            }
        }

        //check realtime availability
        /*if ($timeDifference->h <= $realtimeWindow) {

            if($facility->realtime_minimum_availability > $returnResultArr['availability']){
              $returnResultArr['coupon_threshold_price'] = 0;        
              $returnResultArr['is_coupon_threshold_price_percentage'] = 0;        
              $returnResultArr['availability'] = 0;
              $returnResultArr['is_coupon_threshold_applied'] = 0;
            }
        }*/
        return $returnResultArr;
    }

    public function geInventoryCheck(Request $request)
    {
        $this->log->info("Get Rate Request: " . json_encode($request->all()));

        try {
            // $total_amount = $parking_amount = $payable_amount = $amount_paid = $discount = 0;

            $facility = Facility::find($request->facility_id);

            if ($request->header('X-ClientSecret') != '') {
                $secret = OauthClient::where('secret', $request->header('X-ClientSecret'))->first();
                if (!$secret) {
                    throw new NotFoundException('No partner found.');
                }
                $this->partnerId = $secret->partner_id;

                if (!Facility::where('id', $facility->id)->where('owner_id', $secret->partner_id)->first()) {
                    throw new NotFoundException('No garage found with this partner.');
                }
            }

            $rateData = $this->updateRateInformationWithAvailibilty($request, $facility);
            $this->log->info("updateRateInformationWithAvailibilty : " . json_encode($rateData));
            $coupon_threshold_price = 0;
            $is_coupon_threshold_applied = 0;
            $is_coupon_threshold_price_percentage = 0;

            $lengthOfStay = $request->length_of_stay;

            if (!isset($request->length_of_stay)) {
                $lengthOfStay = 24;
            }

            if ($facility->owner_id == 45) {
                $lengthOfStay = 23.59;
            }

            $rate['isFacilityClosed'] = self::IS_FACILITY_OPEN;
            if ($facility->active != 1) {
                $rate['availability'] = self::DEFAULT_VALUE;
                $rate['isFacilityClosed'] = self::IS_FACILITY_CLOSED;
                $errorMessage = CustomText::where('id', self::DEFAULT_MSG_FOUR)->first();
                $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::NON_WORKING_HOUR_CLOSED;
                return $rate;
            }

            if ((isset($rateData['availability'])) && ($rateData['availability'] > 0)) {
                $rate['availability'] = $rateData['availability'];
            } else {
                $rate['availability'] = self::DEFAULT_VALUE;
            }
            $facilityConfig = FacilityConfiguration::where("facility_id", $facility->id)->first();

            if (isset($facilityConfig->is_inventory_check) && !$facilityConfig->is_inventory_check) {
                $rate['availability'] = 1;
                $rate['availability_msg'] = '';
            } else {
                if ($rate['availability'] == self::DEFAULT_VALUE) {

                    // PIMS - 13805 VP : 06-05-2025 
                    $this->customMessage = QueryBuilder::getCustomMessage('zero-slot', $facility->id, $facility->owner_id);

                    if ($this->customMessage != false) {
                        $rate['availability_msg'] = $this->customMessage;
                    } else {
                        $errorMessage = CustomText::where('id', self::DEFAULT_MSG_THREE)->first();
                        $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::No_SPACE_MSG;
                    }
                } else if ($rate['availability'] < self::MIN_AVAILABILITY) {
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_TWO)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::LIMITED_SPACE_MSG;
                } else {
                    $errorMessage = CustomText::where('id', self::DEFAULT_MSG_ONE)->first();
                    $rate['availability_msg'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                    $rate['availability_msg_some_space'] = isset($errorMessage->message) ? $errorMessage->message : self::SOME_SPACE_MSG;
                }
            }
            return $rate;
        } catch (\Exception $e) {
            $this->log->error("Error message" . $e->getMessage());
            throw new ApiGenericException('Something went wrong, please try after some time.');
        }
    }

    public function saveLprCheckinCheckout($request, $type = 0)
    {
        $LprTicketMapping = new LprTicketMapping();
        $LprTicketMapping->ticket_id    = $request['ticket_id'];
        $LprTicketMapping->lpr_feed_id  = $request['lpr_feed_id'];
        $LprTicketMapping->status       = $type;
        $LprTicketMapping->save();
    }
}
