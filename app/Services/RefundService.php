<?php

namespace App\Services;

// use App\Services\PaymentGateways\PaymentGatewayFactory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Exceptions\ApiGenericException;
use App\Models\ParkEngage\DatacapTransaction;

// use Psr\Log\LoggerInterface;

class RefundService
{
    protected $log;
    protected $request;
    protected $gateway;
    protected $optionArr;
    protected $gatewayType;   ### [ 1, 2, 3, 4 ]
    protected $ticket;    ### [ ticket, Reservation, Permit, event, pass,  ]

    public function __construct(Request $request, $ticket, $paymentGatewayObject, $options = [])
    {
        $this->request      = $request;
        $this->ticket       = $ticket;
        $this->gatewayType  = $ticket->facility->FacilityPaymentDetails->facility_payment_type_id;
        $this->gateway      = $paymentGatewayObject;
        $this->optionArr['facility']    = $ticket->facility;

        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/RefundService')->createLogger('RefundService');
        // dd($request->ticket->facility->FacilityPaymentDetails->facility_payment_type_id);
        // dd($request->all());
    }

    public function processRefund($checkinData, $refundAmount)
    {
        // $request = new Request();
        // Set Refund Request For Payment gateway Wise
        if ($this->gatewayType == '1') {
        } else if ($this->gatewayType == '2') {
        } else if ($this->gatewayType == '3') {
        } else if ($this->gatewayType == '4') {

            // refundFor { 0 => only Main tikcet Refund, 1 => Only Extend Refund, 2 => Both Refund}
            if ($this->request->refundFor == '0') {
                // $refundAmount = $checkinData->transaction->total;
                $this->request->request->add([
                    'hl_transactionId'              => $checkinData->transaction->anet_trans_id,
                    'Amount'                        => $refundAmount,
                    'local_anet_transaction_id'     => $checkinData->transaction->anet_trans_id,
                ]);
                // dd($refundAmount, $this->request->all());
                return $this->gateway->processRefund($this->request, $refundAmount, $this->optionArr);
            } else if ($this->request->refundFor == '1') {
                // $refundAmount = $checkinData->transaction->total;
                $this->request->request->add([
                    'hl_transactionId'              => $checkinData->transaction->anet_trans_id,
                    'Amount'                        => $refundAmount,
                    'local_anet_transaction_id'     => $checkinData->transaction->anet_trans_id,
                ]);
                // dd($refundAmount, $this->request->all());
                return $this->gateway->processRefund($this->request, $refundAmount, $this->optionArr);
            } else if ($this->request->refundFor == '2') {
                // $refundAmount = $checkinData->transaction->total;
                $this->request->request->add([
                    'hl_transactionId'              => $checkinData->transaction->anet_trans_id,
                    'Amount'                        => $refundAmount,
                    'local_anet_transaction_id'     => $checkinData->transaction->anet_trans_id,
                ]);
                dd($refundAmount, $this->request->all());
                return $this->gateway->processRefund($this->request, $refundAmount, $this->optionArr);
            }
        }
    }

    public function handleRefundResponse(Request $request, $refundResponse, $checkinData, $totalRefundAmount)
    {
        $this->log->info("handleRefundResponse for : {$request->ticket_number} and Response : " . json_encode($refundResponse));
        if ($refundResponse && $refundResponse->responseCode == '00' && in_array($refundResponse->responseMessage, ['Success', 'APPROVAL'])) {
            if ($request->refundFor == '0') {  // For Main Checkin
                $checkinData->refund_status = "Refunded";
                $checkinData->refund_amount += $totalRefundAmount;
                // $checkinData->refund_date = now();
                $checkinData->refund_remarks = $request->refund_remarks ?? 'NA';
                $checkinData->refund_by = Auth::id();
                $checkinData->refund_release_status     = 1;
            } else if ($request->refundFor == '1') {    // for Extendtions 
                $checkinData->refund_amount             = $totalRefundAmount;
                $checkinData->refund_release_status     = 1;
            }

            $checkinData->save();

            return true;
        }
        return false;

        throw new ApiGenericException('Refund cannot be initiated, try again later.');
    }

    public function handlePreAuthReleaseResponse(Request $request, $ResponseData, $checkinData)
    {
        $this->log->info("handle  Request : " . json_encode($request->all()));
        $this->log->info("handleRefundResponse for : {$request->ticket_number} and Response : " . json_encode($ResponseData));
        if ($request->gateway == '1') {
        } else if ($request->gateway == '2') {
            if ($ResponseData && in_array($ResponseData->Status, ['Success', 'APPROVAL', 'Approved'])) {

                $checkinData->refund_amount             += $request->Amount;
                $checkinData->refund_release_status     = 2;

                $checkinData->save();
            }
            return true;
        } else if ($request->gateway == '3') {
            if ($ResponseData && in_array($ResponseData['resptext'], ['Success', 'Approval'])) {
                $checkinData->refund_amount             = $request->Amount;
                $checkinData->refund_release_status     = 2;
                $checkinData->save();
                return true;
            }
        } else if ($request->gateway == '4') {
            $this->log->info("handleRefundResponse for Code {$ResponseData->responseCode}  : Message : {$ResponseData->responseMessage} ");
            if ($ResponseData && $ResponseData->responseCode == '00' && in_array($ResponseData->responseMessage, ['Success', 'APPROVAL'])) {
                $checkinData->refund_amount             = $request->Amount;
                $checkinData->refund_release_status     = 2;
                $checkinData->save();
                return true;
            }
        }


        return false;

        throw new ApiGenericException('Refund cannot be initiated, try again later.');
    }

    // this will save PreAuth data in Datacap Txn for HL and DC
    public function savePreAuthData($request, $preAuthResponse)
    {
        // Check if a transaction with the given reservation_id already exists
        $heartlandPreAuth = null;
        if (isset($request->reservation_id) && !empty($request->reservation_id)) {
            $heartlandPreAuth = DatacapTransaction::where('reservation_id', $request->reservation_id)->first();
        }

        // If no existing transaction is found, create a new one
        if (!$heartlandPreAuth) {
            $heartlandPreAuth = new DatacapTransaction();
        }

        //to resolve conflict of same key in reservation: Lokesh -11-Oct-2024
        $name = isset($request->card_name) && !empty($request->card_name) ?  $request->card_name : $request->name;

        // Set the transaction details
        $heartlandPreAuth->user_id                      = $request->user_id;
        $heartlandPreAuth->partner_id                   = $request->partner_id;
        $heartlandPreAuth->ip_address                   = Request::ip();
        $heartlandPreAuth->total                        = $request->total;
        $heartlandPreAuth->response_message             = $preAuthResponse->responseMessage;
        $heartlandPreAuth->ref_id                       = $preAuthResponse->referenceNumber;
        $heartlandPreAuth->trans_id                     = $preAuthResponse->transactionReference->transactionId;
        $heartlandPreAuth->name                         = $name;
        $heartlandPreAuth->card_last_four               = isset($request->card_last_four) ? $request->card_last_four : '';
        $heartlandPreAuth->card_type                    = $preAuthResponse->cardType;
        $heartlandPreAuth->card_name                    = $preAuthResponse->cardType;
        $heartlandPreAuth->expiry                       = isset($request->expiration) ? $request->expiration : '';
        $heartlandPreAuth->token                        = isset($preAuthResponse->token) ? $preAuthResponse->token : '';
        $heartlandPreAuth->result_reason                = $preAuthResponse->responseMessage;
        $heartlandPreAuth->currency_used                = "USD";
        $heartlandPreAuth->AuthCode                     = isset($preAuthResponse->transactionReference->authCode) ? $preAuthResponse->transactionReference->authCode : '';
        $heartlandPreAuth->card_brand_transaction_id    = isset($preAuthResponse->cardBrandTransactionId) ? $preAuthResponse->cardBrandTransactionId : '';

        // Set or update the reservation_id if provided
        if (isset($request->reservation_id) && !empty($request->reservation_id)) {
            $heartlandPreAuth->reservation_id           = $request->reservation_id;
        }

        // Save the transaction
        $heartlandPreAuth->save();

        if (!$heartlandPreAuth) {
            throw new ApiGenericException("Record Not Added");
        }

        return $heartlandPreAuth;
    }
}
