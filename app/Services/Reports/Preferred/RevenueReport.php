<?php

namespace App\Services\Reports\Preferred;

use Illuminate\Http\Request;
use App\Http\Requests;
use App\Exceptions\ApiGenericException;
use App\Models\ParkEngage\BrandSetting;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Fluent;
use PHPExcel_Worksheet_Drawing;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\Http\Helpers\ExcelHelper;
use App\Models\Facility;
use App\Models\Rate;
use App\Services\Pdf;


class RevenueReport
{

    const PARTNET_ID = 91860;
    public function PreferredParking(Request $request)
    {
        ini_set('memory_limit', '256M');
        if (!isset($request->facility_id)) {
            throw new ApiGenericException('Please select Facility.', 422);
        } elseif (isset($request->facility_id) && empty($request->facility_id)) {
            throw new ApiGenericException('Please select Facility.', 422);
        }
        // Super Admin
        $partner_id = isset($request->partner_id) ? $request->partner_id : self::PARTNET_ID;

        $facility_id = (isset($request->facility_id) && !empty($request->facility_id)) ? $request->facility_id : 0;
        $loggedInUserId = Auth::user()->id;
        $fromdate = isset($request->fromDate) ? date('Y-m-d', strtotime($request->fromDate))  : date('Y-m-d');
        $toDate = isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate))  : date('Y-m-d');

        $checkInTime  = date('Y-m-d', strtotime($fromdate)) . ' 00:00:00';
        $checkOutTime    = date('Y-m-d', strtotime($toDate)) . ' 23:59:59';

        if (!$partner_id) {
            throw new ApiGenericException('Partner detail not found.', 500);
        }
        $usertypedata = Auth::user()->user_type;
        $subordinateId = Auth::user()->id;

        if ($usertypedata == 3 || $usertypedata == 1 || $usertypedata == 4 || $usertypedata == 8  || $usertypedata == 10) {

            $UserFacilitiesquery = "SELECT id from facilities where facilities.owner_id = $partner_id AND facilities.id in ($request->facility_id) ";
            $UserFacilities = DB::select($UserFacilitiesquery);
            // print_r($UserFacilities);die;
            $getAllFacility = array_column($UserFacilities, 'id');
            $imploadFaclitiesData = implode(",", $getAllFacility);
            // dd($imploadFaclitiesData);
        } else {
            $UserFacilitiesquery = "SELECT facility_id as id from user_facilities where user_facilities.user_id=$loggedInUserId  AND user_facilities.deleted_at is null  and user_facilities.facility_id in ($request->facility_id)";
            $UserFacilities = DB::select($UserFacilitiesquery);
            // print_r(count($UserFacilities));die;
            $getAllFacility = array_unique(array_column($UserFacilities, 'id'));
            $imploadFaclitiesData = implode(",", $getAllFacility);
        }


        $facilitiesarray = explode(',', $request->facility_id);
        if ($facilitiesarray > 1) {
            $facilityID = " AND t.facility_id IN ($imploadFaclitiesData)";
            $locationName = "Multiple Location";
            $garageCode = "Multiple Id";
        } else {
            $facilityID = " AND t.facility_id = '$facility_id' ";
            $facility = Facility::where(['id' => $facility_id])->first();
            $locationName = $facility->full_name;
            $garageCode = $facility->garage_code;
        }
        // dd($facilityID,$locationName,$garageCode,$imploadFaclitiesData);


        $brandSetting = BrandSetting::where('user_id', $partner_id)->first();
        $color = $brandSetting->color;
        $brandSettinglogo = $brandSetting->logo;
        $count  = 0;
        // $rCount = count($reservationData);
        $excelSheetName = ucwords(str_replace(' ', '', 'Daily Revenue Report'));

        // Non Validated
        $finalCodes5 = [];
        $checkinArray = [];
        $TotalRevenueNonValidated = 0;

        $totalTicketsNonValidated = 0;
        $netValueNonValidated = 0;
        $validatedAmountNonValidated = 0;
        $processingFeeNonValidated = 0;
        $totalExtendedAmount = 0;

        $excelSheetName5 = ucwords('Daily Revenue Report');

        // New code
        //$usersarr = array(1, 3, 12);
        // dd($totalTicketsCheckinNonValidated);
        //offline sheet data
        $finalCodes2 = [];
        $TotalPaymentReceived = 0;
        $TotalValidatedAmount = 0;
        // $cashReportCount = count($driveUpCashReport);
        $finalCodes2[0]['total'] = 0;
        $finalCodes2[0]['discount'] = 0;
        // $finalCodes2[1]['total'] = 0;
        // $finalCodes2[1]['discount'] = 0;

        $TotalTicketAmount = 0;
        $totalDriveUpDuration = $totalReservationDurtion = 0;
        $totalDiscountAmountNonValidated = $validatedCheckinAmountNonValidated = $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = 0;
        $finalCodes4 = [];
        $reservationTickets = [];
        if (isset($request->facility_id) && !empty($request->facility_id)) {

            $checkData = "SELECT id from tickets as t where t.facility_id in($imploadFaclitiesData)  and t.deleted_at is null and  t.partner_id IN ('$partner_id') and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime' and t.paid_type='9' group by t.is_overstay,t.id ";
            $checkDataResult = DB::select($checkData);
            if (empty($checkDataResult) || empty($request->facility_id)) {
                return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "There is no data for this date"]], 500);
            } else {

                set_time_limit(0);
                if (count($UserFacilities) > 0) {
                    $validationAmountTotal = 0;

                    $validationPaidAmountTotal = 0;
                    $totalGrossAmount = 0;
                    $validationTicketTotal = 0;
                    $validatedGTotal = 0;
                    $totalNetAmount = 0;
                    $totalServiceAmount = 0;
                    $finalCodes3 = [];
                    $TotalCc = 0;
                    $rTticketCount = $rTicketAmount = $rGrossAmount = $rProcessingFees = $rNetAmonut = $rDiscountAmount = 0;
                    $TotalTicketAmount = 0;
                    $totalExtentionCount = 0;
                    $allgetedFacility = "";

                    $excelCreate =  Excel::create(
                        $excelSheetName,
                        function ($excel) use (
                            $color,
                            $locationName,
                            $garageCode,
                            $finalCodes2,
                            $count,
                            $TotalPaymentReceived,
                            $finalCodes3,
                            $TotalCc,
                            $finalCodes4,
                            $validationPaidAmountTotal,
                            $totalServiceAmount,
                            $validationAmountTotal,
                            $validationTicketTotal,
                            $totalGrossAmount,
                            $TotalValidatedAmount,
                            $validatedGTotal,
                            $totalNetAmount,
                            $processingFeeNonValidated,
                            $finalCodes5,
                            $checkinArray,
                            $excelSheetName5,
                            $totalTicketsNonValidated,
                            $TotalRevenueNonValidated,
                            $netValueNonValidated,
                            $validatedAmountNonValidated,
                            $totalDiscountAmountNonValidated,
                            $fromdate,
                            $toDate,
                            $validatedCheckinAmountNonValidated,
                            $TotalCheckinRevenueNonValidated,
                            $totalTicketsCheckinNonValidated,
                            $reservationTickets,
                            $rTicketAmount,
                            $rGrossAmount,
                            $rProcessingFees,
                            $rNetAmonut,
                            $rDiscountAmount,
                            $rTticketCount,
                            $totalDriveUpDuration,
                            $totalReservationDurtion,
                            $UserFacilities,
                            $checkInTime,
                            $checkOutTime,
                            $partner_id,
                            $TotalTicketAmount,
                            $facility_id,
                            $totalExtendedAmount,
                            $brandSetting,
                            $brandSettinglogo,
                            $totalExtentionCount,
                            $imploadFaclitiesData
                        ) {
                            // foreach ($UserFacilities as $u_key => $UserFacility) {
                            // dd($UserFacility);
                            $totalGrossAmount = $totalServiceAmount = $totalNetAmount = $validationAmountTotal = $validationPaidAmountTotal = $validatedGTotal = 0;
                            //$facilities=[];
                            $totalCashServiceAmount = $TotalPaymentReceived = $TotalCc = $totalServiceAmount = $processingFeeNonValidated = 0;
                            $totalTicketsNonValidated = $validationTicketTotal = $rTticketCount = 0;
                            $finalCodes5 = [];
                            $reservationCheckinArray = [];
                            $finalCodes4 = [];
                            $reservationTickets = [];
                            $eventTicket = [];
                            $TotalValidatedAmount  = $ccticketCount = $TotalRevenueNonValidated = 0;
                            $processingFeeNonValidated = $validatedAmountNonValidated = 0;
                            $facilities = Facility::where(['id' => $facility_id])->first();
                            $rates = Rate::where(['facility_id' => $facility_id])->withTrashed()->orderby('price', 'asc')->get();
                            $facilityID = " AND t.facility_id = '$facility_id' ";
                            $rowKey = 0;
                            $outerLoopIndex = 0;
                            // Drive Up Data
                            $driveupData = "SELECT COUNT(distinct(t.id)) AS ticketCount,
                                    GROUP_CONCAT(distinct(t.id)) as  t_ids,
                                    COALESCE(t.grand_total, 0) AS grand_total,
                                    COALESCE(t.grand_total, 0) AS payAmount,
                                    COALESCE(t.processing_fee, 0) AS processingFeePerTicket,
                                    COALESCE(t.parking_amount, 0) AS parkingAmount,
                                    SUM(COALESCE(t.tax_fee, 0)) AS tax_fee,
                                    (t.processing_fee* COUNT(distinct(t.id))) AS processing_fee,
                                    SUM(COALESCE(t.grand_total, 0)) AS Pay_grand_total,
                                    SUM(COALESCE(IF(t.grand_total > 0,t.parking_amount,t.grand_total), 0)) AS net_amount,
                                    SUM(COALESCE(t.discount_amount, 0)) AS discount_amount,
                                    SUM(COALESCE(t.paid_amount, 0)) AS validated_amount,group_concat(t.id) as t_id,
                                    t.length,
                                    t.total,
                                    t.is_extended,
                                    case when  t.is_extended = '1' then
                                        ((SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) + t.grand_total )
                                    ELSE
                                        t.grand_total
                                    END
                                    AS final_sum,
                                    count(te.id) as no_of_extentions,
                                    sum(te.grand_total)  as overstayGrandTotal, 
                                    sum(te.discount_amount) as overstayDiscount,
                                    CASE
                                        WHEN t.is_extended = '1' THEN sum(CEIL(COALESCE(t.length, 0)  ))
                                        ELSE SUM(CEIL(COALESCE(t.length, 0)))
                                    END AS lenghtInMints             
                                    from tickets as t
                                    left join ticket_extends as te on te.ticket_id = t.id 
                                    where  t.partner_id IN ('$partner_id') 
                                    AND t.facility_id in($imploadFaclitiesData) and t.is_checkout ='1' 
                                    and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime'  
                                    and t.paid_type='9' and t.paid_by is null
                                    and t.deleted_at is null and t.reservation_id is null and t.event_id is null
                                    group by parkingAmount  order by parkingAmount ASC";

                            $dataforCategoryWise = DB::select($driveupData);
                            // dd($dataforCategoryWise);
                            $totalNewRevenueAmount = 0;
                            foreach ($dataforCategoryWise as $key => $value) {
                                $finalCodes5[$rowKey]['Rate ($)']               = floatval($value->parkingAmount);
                                $finalCodes5[$rowKey]['No of Tickets']          = intval($value->ticketCount);
                                $finalCodes5[$rowKey]['Ticket Number']          = '';
                                $finalCodes5[$rowKey]['Transaction Date']    = '-';
                                $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($value->parkingAmount) ? floatval($value->parkingAmount) : '0.00';
                                $finalCodes5[$rowKey]['No of Extensions']        = floatval($value->no_of_extentions);
                                $finalCodes5[$rowKey]['Extension Amount ($)']   = floatval($value->overstayGrandTotal);
                                $finalCodes5[$rowKey]['Net Amount ($)']         = floatval($value->net_amount + ($value->overstayGrandTotal ?? 0));
                                $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($value->processing_fee);
                                $finalCodes5[$rowKey]['Tax ($)']    = floatval($value->tax_fee);
                                $finalCodes5[$rowKey]['Total Collected ($)']    = floatval($value->Pay_grand_total + ($value->overstayGrandTotal ?? 0));
                                $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($value->discount_amount  + ($value->overstayDiscount ?? 0));
                                $totalNewRevenueAmount += floatval($value->net_amount + ($value->overstayGrandTotal ?? 0));
                                $finalCodes5[$rowKey]['Prmocode']    = '-';

                                $driveupDataTickets = "SELECT t.id AS ticketCount,
                                t.discount_amount AS discount_amount,
                                t.ticket_number,
                                t.length,
                                t.total,
                                t.grand_total as grand_total,
                                t.processing_fee,
                                t.tax_fee,
                                t.promocode,
                                t.parking_amount, DATE_FORMAT(ant.created_at,'%m-%d-%Y') as transaction_date,
                                case when  t.is_extended = '1' then
                                    ((SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) + t.grand_total )
                                ELSE
                                    t.grand_total
                                END
                                AS final_sum,
                                count(te.id) as no_of_extentions,
                                sum(te.grand_total)  as overstayGrandTotal, 
                                sum(te.discount_amount) as overstayDiscount
                                from tickets as t
                                left join anet_transactions as ant on ant.id = t.anet_transaction_id
                                left join ticket_extends as te on te.ticket_id = t.id
                                where  t.partner_id IN ($partner_id) 
                                and t.facility_id in ($imploadFaclitiesData) and t.is_checkout ='1'
                                and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime'  
                                and t.paid_type='9' and t.paid_by is null
                                and t.deleted_at is null and t.reservation_id is null and t.event_id is null 
                                and t.parking_amount = $value->parkingAmount group by t.ticket_number";
                                $driveupDataTicketResults = DB::select($driveupDataTickets);
                                $a[] = $driveupDataTicketResults;
                                $processingFeein = $extedGrandTotal = $extedDiscount = $extedNetTotal = 0;
                                $outerLoopIndex = $rowKey;
                                $rowKey++;
                                if (count($driveupDataTicketResults) > 0) {

                                    foreach ($driveupDataTicketResults as $tkey => $ticket) {
                                        $finalCodes5[$rowKey]['Rate ($)']               = '-';
                                        $finalCodes5[$rowKey]['No of Tickets']          = '-';
                                        $finalCodes5[$rowKey]['Ticket Number']          = $ticket->ticket_number;
                                        $finalCodes5[$rowKey]['Transaction Date']    = $ticket->transaction_date;
                                        $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($ticket->parking_amount) ? floatval($ticket->parking_amount) : '0.00';
                                        $finalCodes5[$rowKey]['No of Extensions']       = floatval($ticket->no_of_extentions);
                                        $finalCodes5[$rowKey]['Extention Amount ($)']   = floatval($ticket->overstayGrandTotal);
                                        $finalCodes5[$rowKey]['Net Amount ($)']         = (($ticket->grand_total + $ticket->overstayGrandTotal) > 0) ? floatval($ticket->parking_amount + $ticket->overstayGrandTotal) : 0;
                                        $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($ticket->processing_fee);
                                        $finalCodes5[$rowKey]['Tax ($)']                = floatval($ticket->tax_fee);
                                        $finalCodes5[$rowKey]['Total Collected ($)']    = floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
                                        $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                                        $finalCodes5[$rowKey]['Prmocode']               = $ticket->promocode;

                                        $rowKey++;
                                        $extedNetTotal += (($ticket->grand_total + $ticket->overstayGrandTotal) > 0) ? floatval($ticket->parking_amount + $ticket->overstayGrandTotal) : 0;
                                        $extedGrandTotal += floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
                                        $extedDiscount += floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                                    }
                                }
                                $totalExtendedAmount           += floatval($value->overstayGrandTotal);
                                // $finalCodes5[$outerLoopIndex]['Processing Fees ($)']         = floatval($processingFeein);
                                $finalCodes5[$outerLoopIndex]['Net Amount ($)']         = floatval($extedNetTotal);
                                $finalCodes5[$outerLoopIndex]['Total Collected ($)']    = floatval(($extedGrandTotal));
                                $finalCodes5[$outerLoopIndex]['Discount Amount ($)']    = floatval(($extedDiscount));
                                $TotalTicketAmount              += '0.00';
                                $netValueNonValidated           += '0.00';
                                $totalDriveUpDuration           += $value->lenghtInMints;
                                $totalExtentionCount            += $value->no_of_extentions;
                                $totalTicketsNonValidated       += $value->ticketCount;
                                $TotalRevenueNonValidated       += floatval(($extedGrandTotal));;
                                $processingFeeNonValidated      += floatval($value->processing_fee);
                                $validatedAmountNonValidated    += floatval($value->validated_amount);
                                $totalDiscountAmountNonValidated += floatval(($extedDiscount));
                            }
                            // Cash or Offline payment - for transient
                            $driveUpOfflinePayment = "SELECT SUM(t.grand_total) AS sum_offline_amount, SUM(t.discount_amount) as discountAmount,t.is_offline_payment,t.partner_id ,SUM(t.processing_fee) as processingFee, SUM(tex.grand_total) as overstayGrandTotal, SUM(tex.discount_amount) as overstayDiscount, group_concat(t.id) as ticket_ids
                            FROM tickets AS t
                            left join ticket_extends as tex on t.id = tex.ticket_id
                            where  t.partner_id in ('$partner_id') and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime'  and t.paid_type='9'  and t.is_checkout ='1' and t.deleted_at is null and (t.is_offline_payment IN('1') ) and t.facility_id in($imploadFaclitiesData) order by t.id";

                            $driveUpCashReport = DB::select($driveUpOfflinePayment);
                            // dd($driveUpCashReport);

                            // Non Cash or card breakdown
                            $DriveUpnonCashPayment = "SELECT CASE
                            when ant.card_type IN ('MC', 'MASTERCARD','M/C','DEBIT MASTERCARD') THEN 'MASTERCARD'
                            when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT','VISA CREDIT') THEN 'VISA'
                            when ant.card_type IN ('DCVR','Discover') THEN 'DCVR'
                            WHEN ant.card_type IN ('AMEX','AMEX CREDIT','AMERICAN EXPRESS','AMERICAN','AMEX DEBIT') THEN 'AMEX' 
                            WHEN ant.card_type IN ('Disc') THEN 'DISC'
                            when ant.card_type is null THEN 'OTHERS'
                            ELSE 'ant.card_type'
                            END AS combined_card_type, SUM(t.grand_total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                            (SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) as overstayGrandTotal,
                            (SELECT sum(ticket_extends.discount_amount) FROM ticket_extends where ticket_id IN (t.id)) as overstayDiscount,
                            group_concat(t.id) as ticket_ids
                            FROM tickets as t
                            left join anet_transactions as ant on ant.id = t.anet_transaction_id

                            WHERE t.partner_id IN ('$partner_id') AND t.checkout_time >='$checkInTime' AND t.checkout_time <='$checkOutTime' AND t.is_offline_payment IN ('0','2','3') and t.deleted_at is null  and t.is_checkout ='1' and t.facility_id in($imploadFaclitiesData) GROUP BY combined_card_type,t.id"; #td.rate_description  

                            // dd('asd');   
                            $driveUpCCReport = DB::select($DriveUpnonCashPayment);
                            //Total Discount for Card type Payment
                            $TotalValidatedAmount = (array_sum(array_column($driveUpCCReport, 'discountAmount')) ?? 0) + (array_sum(array_column($driveUpCCReport, 'overstayDiscount')) ?? 0);


                            // Non Cash or card breakdown
                            $checkinDriveUpnonCashPayment = "SELECT CASE
                                    when ant.card_type IN ('MC', 'MASTERCARD','M/C','DEBIT MASTERCARD') THEN 'MASTERCARD'
                                    when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT','VISA CREDIT') THEN 'VISA'
                                    when ant.card_type IN ('DCVR','Discover') THEN 'DCVR'
                                    WHEN ant.card_type IN ('AMEX','AMEX CREDIT','AMERICAN EXPRESS','AMERICAN','AMEX DEBIT') THEN 'AMEX' 
                                    WHEN ant.card_type IN ('Disc') THEN 'DISC'
                                    when ant.card_type is null THEN 'OTHERS'
                                    ELSE ant.card_type
                                    END AS combined_card_type, 
                                    SUM(ant.total) as total_amount,count(r.id) as ticketCount,SUM(r.discount) as discountAmount,SUM(r.processing_fee) as processingFee
                                    FROM  reservations as r
                                    left join anet_transactions as ant on ant.id = r.anet_transaction_id
                                    
                                    WHERE r.partner_id IN ('$partner_id') AND r.start_timestamp >='$checkInTime' AND r.start_timestamp <='$checkOutTime'  and r.deleted_at is null  and r.deleted_at is null and r.deleted_at is null   and r.facility_id in($facility_id) GROUP BY combined_card_type";
                            //dd('ds');
                            $checkinDriveUpCCReport = DB::select($checkinDriveUpnonCashPayment);
                            // dd($checkinDriveUpCCReport);
                            $sql_query4 = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                                sum(t.total) as total,                
                                sum(t.grand_total) as paidAmount,                
                                sum(t.parking_amount) as parking_amount,
                                sum(t.paid_amount) as validated_amount,
                                sum(t.discount_amount) as discount_amount,
                                t.affiliate_business_id, ab.business_name as BusinessName
                                FROM tickets as t        
                                inner join affiliate_business as ab on ab.id = t.affiliate_business_id 
                                WHERE t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime' and t.partner_id IN ('$partner_id')  and t.affiliate_business_id is not null and paid_by > 0 and t.deleted_at is null and t.is_checkout ='1' and t.facility_id in ($imploadFaclitiesData) GROUP BY t.affiliate_business_id order by t.affiliate_business_id";
                            $validationReport = DB::select($sql_query4);

                            $totalCashDiscount = 0;
                            foreach ($driveUpCashReport as $key => $value) {
                                if ($value->is_offline_payment == 1) {
                                    $finalCodes2[0]['payment_type'] = 'Cash';
                                    $finalCodes2[0]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                                    $finalCodes2[0]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                                }
                                //  else if ($value->is_offline_payment == 2 || $value->is_offline_payment == 3) {
                                //     $finalCodes2[1]['payment_type'] = 'Card';
                                //     $finalCodes2[1]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                                //     $finalCodes2[1]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                                // }
                                $totalCashServiceAmount += $value->processingFee;
                                // $cashticketCount = $cashticketCount + $value->ticketCount;
                                $TotalPaymentReceived +=  $value->sum_offline_amount + $value->overstayGrandTotal;
                                $totalCashDiscount += $value->discountAmount + $value->overstayDiscount;
                                $TotalValidatedAmount +=  $value->discountAmount + $value->overstayDiscount;
                            }

                            // dd($driveUpCashReport, $finalCodes2, $TotalPaymentReceived);
                            $totalCardServiceAmount = $ccticketCount = $totalDiscountAmount = 0;
                            $cards = ['AMEX', 'DCVR', 'MASTERCARD', 'VISA', 'DISC', 'OTHERS'];

                            foreach ($cards as $cardkey => $card) {
                                $totalcardPay  = $processingFees  = $discountAmount = $ticketCount = 0;

                                if (isset($driveUpCCReport) && count($driveUpCCReport) > 0) {
                                    foreach ($driveUpCCReport as $key => $value) {
                                        // if ($card == $value->combined_card_type) {
                                        if (strcasecmp($card, $value->combined_card_type) == 0) {
                                            if ($value->total_amount <= 0) continue;
                                            $ticketCount += intval($value->ticketCount);
                                            $discountAmount += floatval($value->discountAmount + $value->overstayDiscount);
                                            $processingFees += floatval($value->processingFee);
                                            $totalcardPay += floatval($value->total_amount + $value->overstayGrandTotal);
                                            $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                            $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                            $TotalValidatedAmount += $discountAmount;
                                        }
                                    }
                                }
                                //  dd($finalCodes3);
                                //  dd($checkinDriveUpCCReport);
                                if (isset($checkinDriveUpCCReport) && count($checkinDriveUpCCReport) > 0) {
                                    foreach ($checkinDriveUpCCReport as $key => $value) {
                                        //dd($value);
                                        if ($card == $value->combined_card_type) {
                                            $totalcardPay += floatval($value->total_amount);
                                            //+ $value->overstayGrandTota
                                            // $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                                            $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                                        }
                                    }
                                }
                                // Permit card Payment
                                $TotalCc += $totalcardPay;
                                $ccticketCount += $ticketCount;
                                $totalCardServiceAmount += $processingFees;
                                // $TotalValidatedAmount += $discountAmount;
                            }

                            // dd($TotalValidatedAmount);
                            // dd($finalCodes2, $TotalPaymentReceived, $finalCodes3, $totalcardPay,$driveUpCCReport,$checkinDriveUpCCReport, $permitCCReportPayment);
                            $i = 0;
                            // dd($validationReport);
                            foreach ($validationReport as $key => $value) {
                                $finalCodes4[$i]['Business Name'] = $value->BusinessName;
                                $finalCodes4[$i]['Policy Name'] = '-';
                                $finalCodes4[$i]['No of Tickets'] = floatval($value->ticket_count);
                                $finalCodes4[$i]['Gross Amount ($)'] = floatval($value->total);
                                $finalCodes4[$i]['Processing Fee ($)'] = floatval($value->processingFee);
                                $finalCodes4[$i]['Net Amount ($)'] = floatval($value->total - $value->processingFee - $value->discount_amount);
                                $finalCodes4[$i]['Validated Amount ($)'] = floatval($value->validated_amount);
                                $finalCodes4[$i]['Paid Amount ($)'] = floatval($value->paidAmount);
                                $finalCodes4[$i]['Total Revenue ($)'] =  floatval($value->paidAmount + $value->validated_amount);

                                // $gTotal = $value->paidAmount + $value->validated_amount;
                                // $grossTotal = ($value->parking_amount + $value->processingFee) * $value->ticket_count;
                                // $serviceFee = floatval($value->processingFee * $value->ticket_count);

                                $validationTicketTotal  += $value->ticket_count;
                                $totalGrossAmount       += floatval($value->total);
                                $totalServiceAmount     += floatval($value->processingFee);
                                $totalNetAmount         += floatval($value->total - $value->processingFee - $value->discount_amount);
                                $validationAmountTotal  += floatval($value->validated_amount);
                                $validationPaidAmountTotal += floatval($value->paidAmount);
                                $validatedGTotal += floatval($value->paidAmount + $value->validated_amount);;

                                // $totalGrossAmount += $grossTotal;
                                // policy query according to business
                                $policy_query = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                                                sum(t.total) as total,
                                                sum(t.grand_total) as paidAmount,
                                                sum(t.parking_amount) as parking_amount,
                                                sum(t.discount_amount) as discount_amount,
                                                sum(t.paid_amount) as validated_amount,t.affiliate_business_id,t.policy_id,
                                                p.policy_name as policyName,
                                                ab.business_name as BusinessName
                                                FROM tickets as t
                                                inner join affiliate_business as ab on ab.id = t.affiliate_business_id
                                                inner join business_policy as p on p.id = t.policy_id 
                                                WHERE t.deleted_at is null and t.user_id > 0  and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime' and t.partner_id IN ('$partner_id')  and t.facility_id in ($imploadFaclitiesData) and t.affiliate_business_id =$value->affiliate_business_id and t.paid_by > 0 GROUP BY t.affiliate_business_id,t.policy_id order by t.affiliate_business_id";

                                $policyReport = DB::select($policy_query);

                                if (isset($policyReport) && !empty($policyReport)) {
                                    $i++;
                                    foreach ($policyReport as $k => $policy) {
                                        // $gTotal = $policy->paidAmount + $policy->validated_amount;
                                        // $grossTotal = ($policy->parking_amount + $policy->processingFee) * $policy->ticket_count;
                                        // $serviceFee = floatval($policy->processingFee * $policy->ticket_count);
                                        $finalCodes4[$i]['Business Name'] = '';
                                        $finalCodes4[$i]['Policy Name'] = $policy->policyName;
                                        $finalCodes4[$i]['No of Ticket'] = floatval($policy->ticket_count);
                                        $finalCodes4[$i]['Gross Total ($)'] = floatval($policy->total);
                                        $finalCodes4[$i]['Processing Fee ($)'] = floatval($policy->processingFee);
                                        $finalCodes4[$i]['Net Amount ($)'] = floatval($policy->total - $policy->processingFee - $value->discount_amount);
                                        $finalCodes4[$i]['Validation Amount ($)'] = floatval($policy->validated_amount);
                                        $finalCodes4[$i]['Paid Amount ($)'] = floatval($policy->paidAmount);
                                        $finalCodes4[$i]['Total Revenue ($)'] =  floatval($policy->paidAmount + $policy->validated_amount);
                                        $i++;
                                    }
                                }
                                $i++;
                            }

                            if (isset($finalCodes5) && !empty($finalCodes5)) {
                                // Tickets Cashiered 
                                $excel->sheet($excelSheetName5, function ($sheet5) use (
                                    $finalCodes5,
                                    $color,
                                    $totalTicketsNonValidated,
                                    $TotalRevenueNonValidated,
                                    $locationName,
                                    $garageCode,
                                    $totalDiscountAmountNonValidated,
                                    $processingFeeNonValidated,
                                    $finalCodes2,
                                    $TotalPaymentReceived,
                                    $finalCodes3,
                                    $TotalCc,
                                    $finalCodes4,
                                    $validationPaidAmountTotal,
                                    $totalServiceAmount,
                                    $validationAmountTotal,
                                    $validationTicketTotal,
                                    $totalGrossAmount,
                                    $TotalValidatedAmount,
                                    $validatedGTotal,
                                    $fromdate,
                                    $toDate,
                                    $totalTicketsCheckinNonValidated,
                                    $totalDriveUpDuration,
                                    $facilities,
                                    $totalExtendedAmount,
                                    $brandSetting,
                                    $brandSettinglogo,
                                    $totalExtentionCount,
                                    $totalNetAmount,
                                    $partner_id,
                                    $checkInTime,
                                    $checkOutTime,
                                    $facility_id,
                                    $totalNewRevenueAmount
                                ) {
                                    // dd('vijay');
                                    $topSpace = 7;
                                    $sheet5->setWidth(array(
                                        'A'     => 21,
                                        'B'     =>  24,
                                        'C'     =>  18,
                                        'D'     =>  17.57,
                                        'E'     =>  17.34,
                                        'F'     =>  15.57,
                                        'G'    =>   21,
                                        'H'    =>   16.86,
                                        'I'    =>   18,
                                        'J'    =>   18,
                                        'K'    =>   18

                                    ));

                                    // $sheet5->row(1, 'Daily Revenue Report');
                                    // define width colom A,B, witdh static here
                                    // $sheet5->getColumnDimension('A')->setWidth(21);
                                    // $sheet5->getColumnDimension('B')->setWidth(24);
                                    // $sheet5->getColumnDimension('C')->setWidth(16);
                                    $sheet5->getColumnDimension('D')->setWidth(17.57);
                                    $sheet5->getColumnDimension('E')->setWidth(17.34);
                                    $sheet5->getColumnDimension('F')->setWidth(15.57);
                                    $sheet5->getColumnDimension('G')->setWidth(21);
                                    $sheet5->getColumnDimension('L')->setWidth(17);
                                    $sheet5->getColumnDimension('H')->setWidth(16.86);
                                    // $sheet5->getColumnDimension('I')->setWidth(18);

                                    // $sheet5->getStyle("A")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("D")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("E")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("F")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("G")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("H")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("I")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("J")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("K")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("L")->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->getStyle("C3")->getNumberFormat()->setFormatCode('0');

                                    //end width for colom here
                                    //set header for excel work                                
                                    $colorCode = count($finalCodes5) + 7;
                                    $row_name = 'A' . $colorCode . ':M' . $colorCode;
                                    // dd(count($finalCodes5), $finalCodes5, $colorCode);
                                    $sheet5->cell($row_name, function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');

                                        $row->setFont(array(
                                            'family'     => 'Calibri',
                                            'size'       => '12',
                                            'bold'       =>  true
                                        ));
                                    });

                                    $sheet5->mergeCells('A1:K1');
                                    $sheet5->getRowDimension(1)->setRowHeight(60);

                                    ExcelHelper::addLogoInExcelHeader($sheet5, $color, $brandSettinglogo);


                                    $sheet5->mergeCells('A2:D2');
                                    $cellValue = "Report Date Range -" .  date('m-d-Y', strtotime($fromdate)) .  ' to ' . date('m-d-Y', strtotime($toDate));
                                    $cellValue .= "\nPrint Date - " . date('m-d-Y');
                                    $sheet5->setCellValue('A2', "$cellValue");
                                    $sheet5->getStyle('A2')->getAlignment()->setWrapText(true);

                                    // Set the height of cell H2 (adjust as needed)
                                    $sheet5->getRowDimension(2)->setRowHeight(80);
                                    $sheet5->getRowDimension(3)->setRowHeight(50);
                                    // $sheet5->getRowDimension(4)->setRowHeight(50);
                                    $sheet5->cell('A2:D2', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center'); // Center vertically
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D6DCE4');
                                        $cell->setFontColor('#000000');
                                        $cell->setFontSize('18');
                                    });
                                    $location = "Location Name \r" . $locationName;
                                    $sheet5->mergeCells('E2:G2');
                                    $sheet5->setCellValue('E2', "$location");
                                    $sheet5->getStyle('E2')->getAlignment()->setWrapText(true);
                                    $sheet5->cell('E2:G2', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center'); // Center vertically
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D6DCE4');
                                        $cell->setFontColor('#000000');
                                        $cell->setFontSize('18');
                                    });
                                    $sheet5->mergeCells('H2:M2');
                                    $locationId = "Location ID \n" . $garageCode;
                                    $sheet5->setCellValue('H2', "$locationId");
                                    $sheet5->getStyle('H2')->getAlignment()->setWrapText(true);

                                    $sheet5->cell('H2:M2', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center'); // Center vertically
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D6DCE4');
                                        $cell->setFontColor('#040D12');
                                        $cell->setFontSize('18');
                                    });


                                    // Ticket Count and Revenue Row Start !!!!
                                    // $sheet5->mergeCells('A3:B3');
                                    $sheet5->setCellValue('A3', "Total Tickets");
                                    $sheet5->cell('A3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('12');
                                    });

                                    // $sheet5->mergeCells('B3');
                                    if ($totalTicketsCheckinNonValidated > 0) {
                                        $totalTicketsNonValidated += $totalTicketsCheckinNonValidated;
                                    }
                                    $sheet5->cell('B3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('12');
                                    });

                                    // !! Event or Permit Counter Start
                                    //$sheet5->mergeCells('D3:E3');

                                    $sheet5->cell('C3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('12');
                                    });

                                    $sheet5->getStyle("F")->getNumberFormat()->setFormatCode('0');
                                    //$sheet5->mergeCells('F3:F3');
                                    $sheet5->cell('A3:K3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('12');
                                    });

                                    $sheet5->mergeCells('G3:H3');
                                    $sheet5->setCellValue('G3', "Total Revenue ($)");
                                    $sheet5->cell('G3:H3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('12');
                                    });
                                    $sheet5->cell('I3:M3', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground($color);
                                        $cell->setFontColor('#ffffff');
                                        $cell->setFontSize('12');
                                    });
                                    // Ticket Count and Revenue Row Close Here  !!!!



                                    // Drive Up Section Start Here.
                                    $sheet5->mergeCells('A4:M4');
                                    $sheet5->setCellValue('A4', 'Tickets Cashiered DriveUp');
                                    $sheet5->cell('A4', function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D9E1F2');
                                        $cell->setFontColor('#272829');
                                        $cell->setFontSize('12');
                                    });

                                    // Color Row For Heading 
                                    $sheet5->cell('A5:M5', function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                    });

                                    $sheet5->fromArray($finalCodes5, [], 'A5', false, true);

                                    //fro center the result
                                    $countAlltick = 4;
                                    $topSpace = 6;
                                    // dd($finalCodes5);
                                    $ir = 0;
                                    $totaalTax = 0;
                                    foreach ($finalCodes5 as $rowkey => $tickets) {
                                        if ($tickets['Rate ($)'] != '-') {
                                            $totaalTax += $tickets['Tax ($)'];
                                        }

                                        $countAlltick++;
                                        $sheet5->cell('A' . $countAlltick . ':M' . $countAlltick, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                        });
                                    }
                                    $countAlltick += 1;
                                    $sheet5->cell('A' . $countAlltick . ':M' . $countAlltick, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center'); // Center vertically
                                    });
                                    $non_colorCode = count($finalCodes5) + $topSpace;
                                    // print_r($non_colorCode);die;
                                    $row_name = 'A' . $non_colorCode . ':M' . $non_colorCode;
                                    $sheet5->cell($row_name, function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                        $row->setAlignment('center');
                                        $row->setValignment('center');

                                        $row->setFont(array(
                                            'family' => 'Calibri',
                                            'size' => '12',
                                            'bold' => true
                                        ));
                                    });
                                    //

                                    //dd($totalDiscountAmountNonValidated);
                                    // Drive Up Total Row
                                    $sheet5->row($non_colorCode, array('Total ($)', $totalTicketsNonValidated, '-',  '-', '-', $totalExtentionCount, $totalExtendedAmount,  $totalNewRevenueAmount, $processingFeeNonValidated,  $totaalTax, $TotalRevenueNonValidated, $totalDiscountAmountNonValidated));
                                    // dd(count($finalCodes5) + 10);
                                    // Drive Up Section Close Here !!!

                                    $totalDiscountAmount = 0;
                                    /* Reservation Related Code */
                                    // dd(count($permitTickets), $permitTickets);



                                    //end event  grid end here

                                    // !!!!!!!! Permit Start here :
                                    // dd($permitrRowNumber);
                                    //$eventSheetRowStart= count($result) + 14;
                                    $permitSheetRowStart = 0;
                                    $permitrRowNumber = $non_colorCode + 1;


                                    //  dd('1',$permitDiscountAmount);
                                    // !!!! Permit close here .


                                    /** Non Revenue Section Start Here : eValidation */
                                    // $revenuSheetRowStart = count($finalCodes5) + count($reservationCheckinArray) + count($permitTickets) + $topSpace + 5;
                                    // $rowNumber = count($finalCodes5) + count($reservationCheckinArray) + count($permitTickets) + $topSpace + 6;
                                    // $validationPaidAmountTotal = 0;
                                    // dd($finalCodes4);
                                    if (count($finalCodes4) > 0) {
                                        $revenuSheetRowStart = $permitrRowNumber + 2;
                                        $rowNumber = $revenuSheetRowStart + 1;
                                        $sheet5->mergeCells('A' . $revenuSheetRowStart . ':K' . $revenuSheetRowStart);
                                        // $sheet5->mergeCells('E' .$revenuSheetRowStart .':I' .$revenuSheetRowStart);
                                        $sheet5->setCellValue('A' . $revenuSheetRowStart, 'Non Revenue Tickets');
                                        $sheet5->cell('A' . $revenuSheetRowStart, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground('#D9E1F2');
                                            $cell->setFontColor('#272829');
                                            $cell->setFontSize('12');
                                        });
                                        $sheet5->cell('A' . $rowNumber . ':K' . $rowNumber, function ($cell) use ($color) {
                                            $cell->setAlignment('center');
                                            $cell->setFontWeight('bold');
                                            $cell->setBackground($color);
                                            $cell->setFontColor('#ffffff');
                                            $cell->setFontSize('12');
                                        });
                                        // dd($count);

                                        $sheet5->setCellValue('A' . $rowNumber, 'Business Name');
                                        $sheet5->setCellValue('B' . $rowNumber, 'Policy Name');
                                        $sheet5->setCellValue('C' . $rowNumber, 'No of Tickets');
                                        $sheet5->setCellValue('D' . $rowNumber, 'Net Amount ($)');
                                        $sheet5->setCellValue('E' . $rowNumber, 'Processing Fee ($)');
                                        $sheet5->setCellValue('F' . $rowNumber, 'Gross Amount ($)');
                                        $sheet5->setCellValue('G' . $rowNumber, 'Validated Amount ($)');
                                        $sheet5->setCellValue('H' . $rowNumber, 'Paid Amount ($)');
                                        // $sheet5->setCellValue('I' . $rowNumber, 'Total Revenue ($)');

                                        $i = $rowNumber + 1;
                                        // dd($i);
                                        $sheet5->getStyle('F')->getNumberFormat()->setFormatCode('0.00');
                                        foreach ($finalCodes4 as $key => $value) {
                                            $sheet5->cell('A' . $i . ':K' . $i, function ($cell) use ($color) {
                                                $cell->setAlignment('center'); // Center horizontally
                                                $cell->setValignment('center'); // Center vertically
                                            });

                                            $sheet5->setCellValue('A' . $i, $value['Business Name']);
                                            $sheet5->setCellValue('B' . $i, $value['Policy Name']);
                                            $sheet5->setCellValue('C' . $i, isset($value['No of Tickets']) ? $value['No of Tickets'] : $value['No of Ticket']);
                                            $sheet5->setCellValue('D' . $i, $value['Net Amount ($)']);
                                            $sheet5->setCellValue('E' . $i, isset($value['Processing Fee ($)']) ? $value['Processing Fee ($)'] : $value['Processing Fee ($)']);
                                            $sheet5->setCellValue('F' . $i, isset($value['Gross Amount ($)']) ? $value['Gross Amount ($)'] : $value['Gross Total ($)']);
                                            $sheet5->setCellValue('G' . $i, isset($value['Validated Amount ($)']) ? $value['Validated Amount ($)'] : $value['Validation Amount ($)']);
                                            $sheet5->setCellValue('H' . $i, $value['Paid Amount ($)']);
                                            // $sheet5->setCellValue('I' . $i, $value['Total Revenue ($)']);
                                            $i++;
                                        }
                                        $sheet5->cell('A' . $i . ':K' . $i, function ($cell) use ($color) {
                                            $cell->setAlignment('center'); // Center horizontally
                                            $cell->setValignment('center'); // Center vertically
                                        });

                                        $sheet5->setCellValue('A' . $i, 'Total ($)');
                                        $sheet5->setCellValue('B' . $i, '');
                                        $sheet5->setCellValue('C' . $i, $validationTicketTotal);
                                        $sheet5->setCellValue('D' . $i, $totalNetAmount);
                                        $sheet5->setCellValue('E' . $i, $totalServiceAmount);
                                        $sheet5->setCellValue('F' . $i, $totalGrossAmount);
                                        $sheet5->setCellValue('G' . $i, $validationAmountTotal);
                                        $sheet5->setCellValue('H' . $i, $validationPaidAmountTotal);
                                        // $sheet5->setCellValue('I' . $i, $validatedGTotal);
                                        /** for color of non revenue total row */
                                        $nonRevenueTotal = count($finalCodes4) + $rowNumber + 1;
                                        $row_name = 'A' . $nonRevenueTotal . ':K' . $nonRevenueTotal;
                                        $sheet5->cell($row_name, function ($row) use ($color) {
                                            $row->setBackground($color);
                                            $row->setFontColor('#ffffff');
                                            $row->setFont(array(
                                                'family' => 'Calibri',
                                                'size' => '12',
                                                'bold' => true
                                            ));
                                        });
                                    } else {
                                        $sheet5->getStyle('F')->getNumberFormat()->setFormatCode('0.00');
                                        $i =  $permitrRowNumber;
                                    }
                                    /** End Non Revenue Section: eValidation */

                                    $totalAmountForNonValidated = $validationPaidAmountTotal + $TotalRevenueNonValidated;

                                    //pass grid section
                                    /** payment breakdown Section Started. **/
                                    $jCell = $i + 2;
                                    // dd($i, $jCell);
                                    $sheet5->mergeCells('A' . $jCell . ':K' . $jCell);
                                    //$sheet5->mergeCells('E' . $jCell . ':I' . $jCell);

                                    $sheet5->setCellValue('A' . $jCell, 'Payment Breakdown');

                                    $sheet5->cell('A' . $jCell, function ($cell) use ($color) {
                                        $cell->setAlignment('center'); // Center horizontally
                                        $cell->setValignment('center');
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#D9E1F2');
                                        $cell->setFontColor('#272829');
                                        $cell->setFontSize('12');
                                    });
                                    $totalPayment =  $TotalRevenueNonValidated;
                                    // $totalPayment = $grossAmountNonValidated;
                                    $newCell = $jCell + 1;
                                    $sheet5->getStyle('B' . $newCell)->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->setCellValue('A' . $newCell, 'Total Payment Received ($)');
                                    $sheet5->setCellValue('B' . $newCell, $TotalCc);
                                    // total validated Amount
                                    $ValidatedCell = $jCell + 2;
                                    $sheet5->getStyle('B' . $ValidatedCell)->getNumberFormat()->setFormatCode('0.00');
                                    $sheet5->setCellValue('A' . $ValidatedCell, 'Total Validated Amt ($)');
                                    $sheet5->setCellValue('B' . $ValidatedCell, $validationAmountTotal);


                                    $DiscountCell = $ValidatedCell + 1;
                                    $refundAmountcell = $DiscountCell + 1;
                                    //$refundamount
                                    // dd( $processingfee , ($processingFeeNonValidated),$eventProcessingFee);
                                    $sheet5->getStyle('B' . $DiscountCell)->getNumberFormat()->setFormatCode('0.00');

                                    $sheet5->setCellValue('E' . $newCell, 'Total Revenue ($)');
                                    $sheet5->setCellValue('F' . $newCell, ($TotalCc));

                                    $sheet5->setCellValue('E' . $ValidatedCell, 'Net Revenue ($)');
                                    $sheet5->setCellValue('F' . $ValidatedCell, floatval($TotalCc  - $processingFeeNonValidated));



                                    $nonCashCell = $DiscountCell + 2;
                                    $sheet5->cell('A' . $nonCashCell . ':K' . $nonCashCell, function ($row) use ($color) {
                                        $row->setFontColor('#ffffff');
                                        $row->setBackground($color);
                                        $row->setFont(array(
                                            'family' => 'Calibri',
                                            'size' => '12',
                                            'bold' => true
                                        ));

                                        $row->setFontWeight('bold');
                                        $row->setValignment('center');
                                    });

                                    $sheet5->row($nonCashCell, array('Non-Cash Receipts'));
                                    $sheet5->setCellValue('D' . $nonCashCell, 'Cash Receipts');
                                    $cardTupeCell = $nonCashCell + 1;
                                    $totalCell = $nonCashCell + 2;
                                    $sheet5->cell($cardTupeCell, function ($cell) use ($color) {
                                        $cell->setFontWeight('bold');
                                        $cell->setBackground('#EDEDED');
                                        $cell->setFontColor('#272829');
                                        $cell->setFontSize('12');
                                    });

                                    $sheet5->cell('B' . $cardTupeCell, function ($cell) use ($color) {
                                        $cell->setAlignment('right'); // Center horizontally
                                        $cell->setValignment('right'); // Center vertically
                                    });
                                    $sheet5->cell('E' . $cardTupeCell, function ($cell) use ($color) {
                                        $cell->setAlignment('right'); // Center horizontally
                                        $cell->setValignment('right'); // Center vertically
                                    });
                                    $sheet5->cell('F' . $cardTupeCell, function ($cell) use ($color) {
                                        $cell->setAlignment('right'); // Center horizontally
                                        $cell->setValignment('right'); // Center vertically
                                    });
                                    $sheet5->setCellValue('A' . $cardTupeCell, 'Card Type');
                                    $sheet5->setCellValue('B' . $cardTupeCell, 'Total ($)');

                                    $sheet5->setCellValue('D' . $cardTupeCell, 'Payment Type');
                                    $sheet5->setCellValue('E' . $cardTupeCell, 'Total ($)');
                                    $sheet5->setCellValue('F' . $cardTupeCell, 'Discount ($)');
                                    $i = $cardTupeCell + 1;
                                    $j = $cardTupeCell + 1;
                                    // dd($finalCodes3);
                                    foreach ($finalCodes3 as $key => $value) {
                                        if ($value['total'] > 0) {
                                            $sheet5->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                                            $sheet5->setCellValue('A' . $i, $value['no_cash_receipts']);
                                            $sheet5->setCellValue('B' . $i, $value['total']);
                                            $i++;
                                        }
                                    }
                                    if (count($finalCodes2) > 0) {
                                        foreach ($finalCodes2 as $key => $val) {
                                            $sheet5->setCellValue('D' . $j, 'Total ($)');
                                            $sheet5->setCellValue('E' . $j, $val['total']);
                                            $sheet5->setCellValue('F' . $j, $val['discount']);
                                            $j++;
                                        }
                                    } else {
                                        $sheet5->setCellValue('D' . $j, 'Total ($)');
                                        $sheet5->setCellValue('E' . $j, 0);
                                        $sheet5->setCellValue('F' . $j, 0);
                                    }

                                    $cellColor = 'A' . $i . ':B' . $i;
                                    $sheet5->cell($cellColor, function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                        $row->setFont(array(
                                            'family' => 'Calibri',
                                            'size' => '12',
                                            'bold' => true
                                        ));
                                    });

                                    $cellColor = 'D' . $j . ':F' . $j;
                                    $sheet5->cell($cellColor, function ($row) use ($color) {
                                        $row->setBackground($color);
                                        $row->setFontColor('#ffffff');
                                        $row->setFont(array(
                                            'family' => 'Calibri',
                                            'size' => '12',
                                            'bold' => true
                                        ));
                                    });
                                    $sheet5->getStyle('B' . $i)->getNumberFormat()->setFormatCode('0.00');
                                    // echo$i;die;

                                    $sheet5->row($i, array('Total ($)', $TotalCc));
                                    // //end payment breakdown issue
                                    //merge cell for formatting last data
                                    if ($i < $j) {
                                        $i = $i + 1;
                                    }
                                    $k = $i + 1;
                                    // $rowVerticalFormate = 'J1:J' . $k;
                                    // $sheet5->mergeCells($rowVerticalFormate);
                                    $sheet5->getStyle("D4")->getNumberFormat()->setFormatCode('0');
                                    $sheet5->getStyle("D3")->getNumberFormat()->setFormatCode('0');

                                    //horigental cell
                                    $rowHorizentalFormate = 'A' . $k . ':K' . $k;
                                    $sheet5->mergeCells($rowHorizentalFormate);

                                    //grid for Above
                                    //  $totalRevenueAmountgrid= $totalAmountForNonValidated+$totalAmountforReservation;
                                    // $totalAmountforReservation
                                    $sheet5->setCellValue('I3', $TotalCc);
                                    if (!isset($validationTicketTotal)) {
                                        $validationTicketTotal = 0;
                                    }
                                    $finalTicket = $totalTicketsNonValidated + $validationTicketTotal;
                                    $sheet5->setCellValue('B3', $finalTicket);
                                });
                            }
                            // else {
                            //     $this->log->info("No Record Found ");
                            // }
                            // }
                        }
                    )->store('xls');
                    if (isset($request->email_send) && $request->email_send == '1') {

                        $path_to_file = storage_path('exports/' . $excelSheetName . '.xls');
                        // $this->sendEmailExcelForPArtner($excelSheetName, $path_to_file, $this->facilityName, $this->fromDate, $this->todate, $this->email);
                        $response['status'] = true;
                        $response['message'] = 'Email send successfully.';
                        return response()->json($response, 200);
                    } else {
                        $downalodExcel = $excelCreate->download('xls');
                    }
                } else {
                    // no Facility Found 
                }
            }
        }
        // }
    }


    public function PreferredParkingpPDF(Request $request)
    {
        set_time_limit(0);
        if (!isset($request->facility_id)) {
            throw new ApiGenericException('Please select Facility.', 422);
        } elseif (isset($request->facility_id) && empty($request->facility_id)) {
            throw new ApiGenericException('Please select Facility.', 422);
        }
        // Super Admin
        $partner_id = isset($request->partner_id) ? $request->partner_id : self::PARTNET_ID;
        $facility_id = (isset($request->facility_id) && !empty($request->facility_id)) ? $request->facility_id : 0;
        $loggedInUserId = Auth::user()->id;
        $usertypedata = Auth::user()->user_type;
        if ($usertypedata == 3 || $usertypedata == 1 || $usertypedata == 4 || $usertypedata == 8  || $usertypedata == 10) {
            $UserFacilitiesquery = "SELECT id from facilities where facilities.owner_id = $partner_id AND facilities.id in ($request->facility_id) ";
            $UserFacilities = DB::select($UserFacilitiesquery);
            // print_r(count($UserFacilities));die;
            $getAllFacility = array_column($UserFacilities, 'id');
            $imploadFaclities = implode(",", $getAllFacility);
        } else {
            $UserFacilitiesquery = "SELECT facility_id as id from user_facilities where user_facilities.user_id=$loggedInUserId  AND user_facilities.deleted_at is null  and user_facilities.facility_id in ($request->facility_id)";
            $UserFacilities = DB::select($UserFacilitiesquery);
            // print_r(count($UserFacilities));die;
            $getAllFacility = array_unique(array_column($UserFacilities, 'id'));
            $imploadFaclities = implode(",", $getAllFacility);
        }

        $rates = Rate::where(['facility_id' => $facility_id])->withTrashed()->orderby('price', 'asc')->get();
        $printFromdate = isset($request->fromDate) ? date('m-d-Y', strtotime($request->fromDate))  : date('m-d-Y');
        $printToDate = isset($request->toDate) ? date('m-d-Y', strtotime($request->toDate))  : date('m-d-Y');

        $fromdate = isset($request->fromDate) ? date('Y-m-d', strtotime($request->fromDate))  : date('Y-m-d');
        $toDate = isset($request->toDate) ? date('Y-m-d', strtotime($request->toDate))  : date('Y-m-d');

        $checkInTime  = date('Y-m-d', strtotime($fromdate)) . ' 00:00:00';
        $checkOutTime    = date('Y-m-d', strtotime($toDate)) . ' 23:59:59';
        if (!$partner_id) {
            throw new ApiGenericException('Partner detail not found.', 500);
        }
        $facilityID = $facility = '';


        $facilitiesarray = explode(',', $request->facility_id);
        if ($facilitiesarray > 1) {
            $facilityID = " AND t.facility_id IN ($imploadFaclities)";
            $locationName = "Multiple Location";
            $garageCode = "Multiple Id";
        } else {
            $facilityID = " AND t.facility_id = '$facility_id' ";
            $facility = Facility::where(['id' => $facility_id])->first();
            $locationName = $facility->full_name;
            $garageCode = $facility->garage_code;
        }

        //return $facility;



        // $rCount = count($reservationData);
        $TotalRevenue = 0;
        // Non Validated
        $finalCodes5 = [];
        $checkinArray = [];
        $TotalRevenueNonValidated = 0;
        $totalExtendedAmount = 0;

        $totalTicketsNonValidated = 0;
        $netValueNonValidated = 0;
        $validatedAmountNonValidated = 0;
        $driveupDiscountAmount = 0;
        $processingFeeNonValidated = 0;
        $usertypedata = Auth::user()->user_type;
        //offline sheet data
        $finalCodes2 = [];
        $TotalPaymentReceived = 0;
        $TotalValidatedAmount = 0;
        $totalCashServiceAmount = 0;
        $finalCodes2[0]['total'] = 0;
        $finalCodes2[0]['discount'] = 0;
        $finalCodes2[1]['total'] = 0;
        $finalCodes2[1]['discount'] = 0;

        $ticketCount = 0;
        $discountAmount = 0;
        $totalDuration = 0;
        $totalDiscountAmountNonValidated = $TotalCheckinRevenueNonValidated = $totalTicketsCheckinNonValidated = 0;
        $finalCodes4 = [];
        $reservationTickets = [];
        $loggedInUserId = Auth::user()->id;
        $usertypedata = Auth::user()->user_type;

        // if (($usertypedata == 3 && $partner_id == 2980) || ($usertypedata == 4 && $partner_id == 2980) || ($userdata ==   6131 && $partner_id == 2980) || ($usertypedata == 1 && $partner_id == 2980)) {
        if (isset($request->facility_id) && !empty($request->facility_id)) {

            $checkData = "SELECT id from tickets as t where t.facility_id in($imploadFaclities)  and t.deleted_at is null and  t.partner_id IN ('$partner_id') and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime' and t.paid_type='9' group by t.is_overstay,t.id ";
            $checkDataResult = DB::select($checkData);

            if (empty($checkDataResult) || empty($request->facility_id)) {
                // throw new ApiGenericException('There is no data for this date.');
                return response()->json(['status' => 500, "data" => null, "errors" => ['message' => "There is no data for this date"]], 500);
            }

            // if (count($UserFacilities) > 0) {
            $validationAmountTotal = 0;
            $validationPaidAmountTotal = 0;
            $totalGrossAmount = 0;
            $validationTicketTotal = 0;
            $validatedGTotal = 0;
            $totalNetAmount = 0;
            $totalServiceAmount = 0;
            $finalCodes3 = [];
            $TotalCc = 0;
            $ccticketCount = 0;
            $totalExtentionCount = 0;
            $reservationAmount = $rTticketCount = $rTicketAmount = $rGrossAmount = $rProcessingFees = $rNetAmonut = $rDiscountAmount = 0;
            $categoryCount = 0;

            // foreach ($UserFacilities as $key => $UserFacility) {
            $validationTicketTotal = $totalDriveUpDuration = $TotalTicketAmount = $totalGrossAmount = $totalServiceAmount = $totalNetAmount = $validationAmountTotal = $validationPaidAmountTotal = $validatedGTotal = 0;
            //$facilities=[];
            $totalCashServiceAmount = $TotalPaymentReceived = $TotalCc = $totalServiceAmount = $processingFeeNonValidated = 0;
            $totalTicketsNonValidated = $validationTicketTotal = $rTticketCount = 0;
            $finalCodes5 = [];
            $checkinArray = [];
            $finalCodes4 = [];
            $reservationTickets = [];
            $TotalValidatedAmount = $totalCashServiceAmount = $ccticketCount = $TotalRevenueNonValidated = 0;
            $grossAmountNonValidated = $processingFeeNonValidated = $validatedAmountNonValidated = 0;

            //for reservation data for admin login

            $rowKey = 0;


            //new cod efor drive up data 
            $facilityID = " AND t.facility_id  in ($imploadFaclities) ";
            $rowKey = 0;
            $outerLoopIndex = 0;
            // Drive Up Data
            // $excelrowCount = $increment1 = 0; -- SUM(COALESCE(t.processing_fee, 0)) AS processing_fee,
            $driveupData = "SELECT COUNT(distinct(t.id)) AS ticketCount,
                                   GROUP_CONCAT(distinct(t.id)) as  t_ids,
                                   COALESCE(t.grand_total, 0) AS grand_total,
                                   COALESCE(t.grand_total, 0) AS payAmount,
                                   COALESCE(t.processing_fee, 0) AS processingFeePerTicket,
                                    COALESCE(t.parking_amount, 0) AS parkingAmount,
                                  SUM(COALESCE(t.tax_fee, 0)) AS tax_fee,
                                    (t.processing_fee* COUNT(distinct(t.id))) AS processing_fee,
                                    SUM(COALESCE(t.grand_total, 0)) AS Pay_grand_total,
                                    SUM(COALESCE(IF(t.grand_total > 0,t.parking_amount,t.grand_total), 0)) AS net_amount,
                                    SUM(COALESCE(t.discount_amount, 0)) AS discount_amount,
                                    SUM(COALESCE(t.paid_amount, 0)) AS validated_amount,group_concat(t.id) as t_id,
                                    t.length,
                                    t.total,
                                    t.is_extended,
                                    case when  t.is_extended = '1' then
                                        ((SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) + t.grand_total )
                                    ELSE
                                        t.grand_total
                                    END
                                    AS final_sum,
                                    count(te.id) as no_of_extentions,
                                    sum(te.grand_total)  as overstayGrandTotal, 
                                    sum(te.discount_amount) as overstayDiscount,
                                    CASE
                                        WHEN t.is_extended = '1' THEN sum(CEIL(COALESCE(t.length, 0)  ))
                                        ELSE SUM(CEIL(COALESCE(t.length, 0)))
                                    END AS lenghtInMints             
                                    from tickets as t
                                    left join ticket_extends as te on te.ticket_id = t.id 
                                    where  t.partner_id IN ('$partner_id') 
                                    $facilityID and t.is_checkout ='1' 
                                    and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime'  
                                    and t.paid_type='9' and t.paid_by is null
                                    and t.deleted_at is null and t.reservation_id is null and t.event_id is null
                                    group by parkingAmount  order by parkingAmount ASC";

            $dataforCategoryWise = DB::select($driveupData);

            $totalNewRevenueAmount = 0;
            foreach ($dataforCategoryWise as $key => $value) {
                // $finalCodes5[$rowKey]['Duration (Hours)']    = intval($value->lenghtInMints);
                $finalCodes5[$rowKey]['Rate ($)']               = floatval($value->parkingAmount);
                $finalCodes5[$rowKey]['No of Tickets']          = intval($value->ticketCount);
                $finalCodes5[$rowKey]['Ticket Number']          = '';
                $finalCodes5[$rowKey]['Transaction Date']    = '-';
                $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($value->parkingAmount) ? floatval($value->parkingAmount) : '0.00';
                $finalCodes5[$rowKey]['No of Extensions']        = floatval($value->no_of_extentions);
                $finalCodes5[$rowKey]['Extension Amount ($)']   = floatval($value->overstayGrandTotal);
                $finalCodes5[$rowKey]['Net Amount ($)']         = floatval($value->net_amount + ($value->overstayGrandTotal ?? 0));
                $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($value->processing_fee);
                $finalCodes5[$rowKey]['Tax ($)']    = floatval($value->tax_fee);
                $finalCodes5[$rowKey]['Total Collected ($)']    = floatval($value->Pay_grand_total + ($value->overstayGrandTotal ?? 0));
                $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($value->discount_amount  + ($value->overstayDiscount ?? 0));
                $totalNewRevenueAmount += floatval($value->net_amount + ($value->overstayGrandTotal ?? 0));
                $finalCodes5[$rowKey]['Prmocode']    = '-';

                $driveupDataTickets = "SELECT t.id AS ticketCount,
                          t.discount_amount AS discount_amount,
                          t.ticket_number,
                          t.length,
                          t.total,
                          t.grand_total as grand_total,
                          t.processing_fee,
                          t.tax_fee,
                          t.promocode,
                          t.parking_amount, DATE_FORMAT(ant.created_at,'%m-%d-%Y') as transaction_date,
                          case when  t.is_extended = '1' then
                              ((SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) + t.grand_total )
                          ELSE
                              t.grand_total
                          END
                          AS final_sum,
                          count(te.id) as no_of_extentions,
                          sum(te.grand_total)  as overstayGrandTotal, 
                          sum(te.discount_amount) as overstayDiscount
                          from tickets as t
                          left join anet_transactions as ant on ant.id = t.anet_transaction_id
                          left join ticket_extends as te on te.ticket_id = t.id
                          where  t.partner_id IN ($partner_id) 
                          $facilityID and t.is_checkout ='1'
                          and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime'  
                          and t.paid_type='9' and t.paid_by is null
                          and t.deleted_at is null and t.reservation_id is null and t.event_id is null 
                          and t.parking_amount = $value->parkingAmount group by t.ticket_number";
                $driveupDataTicketResults = DB::select($driveupDataTickets);
                $a[] = $driveupDataTicketResults;
                $processingFeein = $extedGrandTotal = $extedDiscount = $extedNetTotal = 0;
                $outerLoopIndex = $rowKey;
                $rowKey++;
                // dd($driveupDataTicketResults);
                if (count($driveupDataTicketResults) > 0) {

                    foreach ($driveupDataTicketResults as $tkey => $ticket) {
                        //  $netRev=0.00;
                        // if($ticket->grand_total + ($ticket->overstayGrandTotal)<=0){
                        //  $netRev=0.00;
                        // }
                        // dd($driveupDataTicketResults);
                        $finalCodes5[$rowKey]['Rate ($)']               = '';
                        $finalCodes5[$rowKey]['No of Tickets']          = '-';
                        $finalCodes5[$rowKey]['Ticket Number']          = $ticket->ticket_number;
                        $finalCodes5[$rowKey]['Transaction Date']    = $ticket->transaction_date;
                        $finalCodes5[$rowKey]['Ticket Amount ($)']      = isset($ticket->parking_amount) ? floatval($ticket->parking_amount) : '0.00';
                        $finalCodes5[$rowKey]['No of Extensions']       = floatval($ticket->no_of_extentions);
                        $finalCodes5[$rowKey]['Extention Amount ($)']   = floatval($ticket->overstayGrandTotal);
                        $finalCodes5[$rowKey]['Net Amount ($)']         = (($ticket->grand_total + $ticket->overstayGrandTotal) > 0) ? floatval($ticket->parking_amount + $ticket->overstayGrandTotal) : 0;
                        $finalCodes5[$rowKey]['Processing Fees ($)']    = floatval($ticket->processing_fee);
                        $finalCodes5[$rowKey]['Tax ($)']                = floatval($ticket->tax_fee);
                        $finalCodes5[$rowKey]['Total Collected ($)']    = floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
                        $finalCodes5[$rowKey]['Discount Amount ($)']    = floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                        $finalCodes5[$rowKey]['Prmocode']               = $ticket->promocode;

                        $rowKey++;
                        // $processingFeein += floatval($ticket->parking_amount) ;
                        $extedNetTotal += (($ticket->grand_total + $ticket->overstayGrandTotal) > 0) ? floatval($ticket->parking_amount + $ticket->overstayGrandTotal) : 0;
                        $extedGrandTotal += floatval($ticket->grand_total + ($ticket->overstayGrandTotal ?? 0));
                        $extedDiscount += floatval($ticket->discount_amount  + ($ticket->overstayDiscount ?? 0));
                    }
                }
                $totalExtendedAmount           += floatval($value->overstayGrandTotal);
                // $finalCodes5[$outerLoopIndex]['Processing Fees ($)']         = floatval($processingFeein);
                $finalCodes5[$outerLoopIndex]['Net Amount ($)']         = floatval($extedNetTotal);
                $finalCodes5[$outerLoopIndex]['Total Collected ($)']    = floatval(($extedGrandTotal));
                $finalCodes5[$outerLoopIndex]['Discount Amount ($)']    = floatval(($extedDiscount));
                $TotalTicketAmount              += '0.00';
                $netValueNonValidated           += '0.00';
                $totalDriveUpDuration           += $value->lenghtInMints;
                $totalExtentionCount            += $value->no_of_extentions;
                $totalTicketsNonValidated       += $value->ticketCount;
                $TotalRevenueNonValidated       += floatval(($extedGrandTotal));;
                $processingFeeNonValidated      += floatval($value->processing_fee);
                $validatedAmountNonValidated    += floatval($value->validated_amount);
                $totalDiscountAmountNonValidated += floatval(($extedDiscount));
            }
            $driveUpOfflinePayment = "SELECT SUM(t.grand_total) AS sum_offline_amount, SUM(t.discount_amount) as discountAmount,t.is_offline_payment,t.partner_id ,SUM(t.processing_fee) as processingFee, SUM(tex.grand_total) as overstayGrandTotal, SUM(tex.discount_amount) as overstayDiscount, group_concat(t.id) as ticket_ids
             FROM tickets AS t
             left join ticket_extends as tex on t.id = tex.ticket_id
             where  t.partner_id in ('$partner_id') and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime'  and t.paid_type='9'  and t.is_checkout ='1' and t.deleted_at is null and (t.is_offline_payment IN('1') ) $facilityID order by t.id";

            $driveUpCashReport = DB::select($driveUpOfflinePayment);
            // dd($driveUpCashReport);

            // Non Cash or card breakdown
            $DriveUpnonCashPayment = "SELECT CASE
               when ant.card_type IN ('MC', 'MASTERCARD','M/C','DEBIT MASTERCARD') THEN 'MASTERCARD'
                  when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT','VISA CREDIT') THEN 'VISA'
                          when ant.card_type IN ('DCVR','Discover') THEN 'DCVR'
                     WHEN ant.card_type IN ('AMEX','AMEX CREDIT','AMERICAN EXPRESS','AMERICAN','AMEX DEBIT') THEN 'AMEX' 
               WHEN ant.card_type IN ('Disc') THEN 'DISC'
               when ant.card_type is null THEN 'OTHERS'
                 ELSE 'ant.card_type'
                 END AS combined_card_type, SUM(t.grand_total) as total_amount,count(t.id) as ticketCount,SUM(t.discount_amount) as discountAmount,SUM(t.processing_fee) as processingFee,
                 (SELECT sum(ticket_extends.grand_total) FROM ticket_extends where ticket_id IN (t.id)) as overstayGrandTotal,
                 (SELECT sum(ticket_extends.discount_amount) FROM ticket_extends where ticket_id IN (t.id)) as overstayDiscount,
                 group_concat(t.id) as ticket_ids
                 FROM tickets as t
                left join anet_transactions as ant on ant.id = t.anet_transaction_id
 
                 WHERE t.partner_id IN ('$partner_id') AND t.checkout_time >='$checkInTime' AND t.checkout_time <='$checkOutTime' AND t.is_offline_payment IN ('0','2','3') and t.deleted_at is null  and t.is_checkout ='1' $facilityID GROUP BY combined_card_type,t.id"; #td.rate_description  

            // dd('asd');   
            $driveUpCCReport = DB::select($DriveUpnonCashPayment);
            //Total Discount for Card type Payment
            $TotalValidatedAmount = (array_sum(array_column($driveUpCCReport, 'discountAmount')) ?? 0) + (array_sum(array_column($driveUpCCReport, 'overstayDiscount')) ?? 0);

            //**Pemrit Card Payment  */
            // Non Cash or card breakdown
            $checkinDriveUpnonCashPayment = "SELECT CASE
                   when ant.card_type IN ('MC', 'MASTERCARD','M/C','DEBIT MASTERCARD') THEN 'MASTERCARD'
                   when ant.card_type IN ('VS','VISA','Visa Credit','VISA DEBIT','VISA CREDIT') THEN 'VISA'
                    when ant.card_type IN ('DCVR','Discover') THEN 'DCVR'
                    WHEN ant.card_type IN ('AMEX','AMEX CREDIT','AMERICAN EXPRESS','AMERICAN','AMEX DEBIT') THEN 'AMEX' 
                         WHEN ant.card_type IN ('Disc') THEN 'DISC'
                 when ant.card_type is null THEN 'OTHERS'
                 ELSE ant.card_type
                 END AS combined_card_type, 
                 SUM(ant.total) as total_amount,count(r.id) as ticketCount,SUM(r.discount) as discountAmount,SUM(r.processing_fee) as processingFee
                 FROM  reservations as r
                 left join anet_transactions as ant on ant.id = r.anet_transaction_id
               
                 WHERE r.partner_id IN ('$partner_id') AND r.start_timestamp >='$checkInTime' AND r.start_timestamp <='$checkOutTime'  and r.deleted_at is null  and r.deleted_at is null and r.deleted_at is null    GROUP BY combined_card_type";
            //dd('ds');
            $checkinDriveUpCCReport = DB::select($checkinDriveUpnonCashPayment);
            // dd($checkinDriveUpCCReport);


            $totalCashDiscount = 0;
            foreach ($driveUpCashReport as $key => $value) {
                if ($value->is_offline_payment == 1) {
                    $finalCodes2[0]['payment_type'] = 'Cash';
                    $finalCodes2[0]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                    $finalCodes2[0]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                }
                //  else if ($value->is_offline_payment == 2 || $value->is_offline_payment == 3) {
                //     $finalCodes2[1]['payment_type'] = 'Card';
                //     $finalCodes2[1]['total'] += floatval($value->sum_offline_amount + $value->overstayGrandTotal);
                //     $finalCodes2[1]['discount'] += floatval($value->discountAmount + $value->overstayDiscount);
                // }
                $totalCashServiceAmount += $value->processingFee;
                // $cashticketCount = $cashticketCount + $value->ticketCount;
                $TotalPaymentReceived +=  $value->sum_offline_amount + $value->overstayGrandTotal;
                $totalCashDiscount += $value->discountAmount + $value->overstayDiscount;
                $TotalValidatedAmount +=  $value->discountAmount + $value->overstayDiscount;
            }

            // dd($driveUpCashReport, $finalCodes2, $TotalPaymentReceived);

            //Credit Card Payment Section 

            $totalCardServiceAmount = $ccticketCount = $totalDiscountAmount = 0;
            $cards = ['AMEX', 'DCVR', 'MASTERCARD', 'VISA', 'DISC', 'OTHERS'];
            foreach ($cards as $cardkey => $card) {
                $totalcardPay  = $processingFees  = $discountAmount = $ticketCount = 0;
                // if($usertypedata!='4'){


                if (isset($driveUpCCReport) && count($driveUpCCReport) > 0) {
                    foreach ($driveUpCCReport as $key => $value) {
                        // if ($card == $value->combined_card_type) {
                        if (strcasecmp($card, $value->combined_card_type) == 0) {
                            if ($value->total_amount <= 0) continue;
                            $ticketCount += intval($value->ticketCount);
                            $discountAmount += floatval($value->discountAmount + $value->overstayDiscount);
                            $processingFees += floatval($value->processingFee);
                            $totalcardPay += floatval($value->total_amount + $value->overstayGrandTotal);
                            $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                            $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                            $TotalValidatedAmount += $discountAmount;
                        }
                    }
                }

                if (isset($checkinDriveUpCCReport) && count($checkinDriveUpCCReport) > 0) {
                    foreach ($checkinDriveUpCCReport as $key => $value) {
                        //dd($value);
                        if ($card == $value->combined_card_type) {
                            $totalcardPay += floatval($value->total_amount);
                            //+ $value->overstayGrandTota
                            // $finalCodes3[$cardkey]['no_cash_receipts'] = $value->combined_card_type;
                            $finalCodes3[$cardkey]['total'] = floatval($totalcardPay);
                        }
                    }
                }

                // }

                $TotalCc += $totalcardPay;
                $ccticketCount += $ticketCount;
                $totalCardServiceAmount += $processingFees;
                // $TotalValidatedAmount += $discountAmount;
            }


            // Non Cash or card breakdown


            $sql_query4 = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                             sum(t.total) as total,                
                             sum(t.grand_total) as paidAmount,                
                             sum(t.parking_amount) as parking_amount,
                             sum(t.paid_amount) as validated_amount,
                             sum(t.discount_amount) as discount_amount,
                             t.affiliate_business_id, ab.business_name as BusinessName
                             FROM tickets as t        
                             inner join affiliate_business as ab on ab.id = t.affiliate_business_id 
                             WHERE t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime' and t.partner_id IN ('$partner_id')  and t.affiliate_business_id is not null and t.deleted_at is null and t.is_checkout='1' and paid_by > 0 $facilityID GROUP BY t.affiliate_business_id order by t.affiliate_business_id";

            $validationReport = DB::select($sql_query4);
            $i = 0;
            foreach ($validationReport as $key => $value) {
                $finalCodes4[$i]['Business Name'] = $value->BusinessName;
                $finalCodes4[$i]['Policy Name'] = '-';
                $finalCodes4[$i]['No of Tickets'] = floatval($value->ticket_count);
                $finalCodes4[$i]['Gross Amount ($)'] = floatval($value->total);
                $finalCodes4[$i]['Processing Fee ($)'] = floatval($value->processingFee);
                $finalCodes4[$i]['Net Amount ($)'] = floatval($value->total - $value->processingFee - $value->discount_amount);
                $finalCodes4[$i]['Validated Amount ($)'] = floatval($value->validated_amount);
                $finalCodes4[$i]['Paid Amount ($)'] = floatval($value->paidAmount);
                $finalCodes4[$i]['Total Revenue ($)'] =  floatval($value->paidAmount + $value->validated_amount);
                $validationTicketTotal  += $value->ticket_count;
                $totalGrossAmount       += floatval($value->total);
                $totalServiceAmount     += floatval($value->processingFee);
                $totalNetAmount         += floatval($value->total - $value->processingFee - $value->discount_amount);
                $validationAmountTotal  += floatval($value->validated_amount);
                $validationPaidAmountTotal += floatval($value->paidAmount);
                $validatedGTotal += floatval($value->paidAmount + $value->validated_amount);
                // policy query according to business
                $policy_query = "SELECT COUNT(t.ticket_number) AS ticket_count, t.paid_by,sum(t.processing_fee) as processingFee,
                                         sum(t.total) as total,
                                         sum(t.grand_total) as paidAmount,
                                         sum(t.parking_amount) as parking_amount,
                                         sum(t.discount_amount) as discount_amount,
                                         sum(t.paid_amount) as validated_amount,t.affiliate_business_id,t.policy_id,
                                         p.policy_name as policyName,
                                         ab.business_name as BusinessName
                                         FROM tickets as t
                                         inner join affiliate_business as ab on ab.id = t.affiliate_business_id
                                         inner join business_policy as p on p.id = t.policy_id 
                                         WHERE t.deleted_at is null and t.user_id > 0  and t.checkout_time >='$checkInTime' and t.checkout_time <='$checkOutTime' and t.partner_id IN ('$partner_id') and t.affiliate_business_id =$value->affiliate_business_id and t.paid_by > 0 GROUP BY t.affiliate_business_id,t.policy_id order by t.affiliate_business_id";

                $policyReport = DB::select($policy_query);

                if (isset($policyReport) && !empty($policyReport)) {
                    $i++;
                    foreach ($policyReport as $k => $policy) {
                        // $gTotal = $policy->paidAmount + $policy->validated_amount;
                        // $grossTotal = ($policy->parking_amount + $policy->processingFee) * $policy->ticket_count;
                        // $serviceFee = floatval($policy->processingFee * $policy->ticket_count);
                        $finalCodes4[$i]['Business Name'] = '';
                        $finalCodes4[$i]['Policy Name'] = $policy->policyName;
                        $finalCodes4[$i]['No of Ticket'] = floatval($policy->ticket_count);
                        $finalCodes4[$i]['Gross Total ($)'] = floatval($policy->total);
                        $finalCodes4[$i]['Processing Fee ($)'] = floatval($policy->processingFee);
                        $finalCodes4[$i]['Net Amount ($)'] = floatval($policy->total - $policy->processingFee - $value->discount_amount);
                        $finalCodes4[$i]['Validation Amount ($)'] = floatval($policy->validated_amount);
                        $finalCodes4[$i]['Paid Amount ($)'] = floatval($policy->paidAmount);
                        $finalCodes4[$i]['Total Revenue ($)'] =  floatval($policy->paidAmount + $policy->validated_amount);
                        $i++;
                    }
                }
                $i++;
            }
            $reservationAmount = $rTticketCount = $rTicketAmount = $rGrossAmount = $rProcessingFees = $rNetAmonut = $rDiscountAmount = 0;
            $categoryCount = 0;
            $totalevent = 0;


            // $totalPayment =  $TotalPaymentReceived + $TotalCc;
            // $totalPayment =  $TotalRevenueNonValidated + $permitGrossTotalAmount + $validationPaidAmountTotal;
            $totalPayment =  $TotalRevenueNonValidated;
            $TotalRevenue =  $totalPayment + $validationAmountTotal;
            $totalValidation = $validationAmountTotal;
            $netRevenue = floatval(($totalPayment + $validationAmountTotal));
            //count permit

            //end pass total count


            $finalCodes5ResultArray = [];
            $secondHeaderData = array(
                'Total Tickets' => $totalTicketsNonValidated,
                'Event Count'  => $totalevent,
                'Reservation Count' => isset($totalReservationCount) ? $totalReservationCount : 0.0,
                'Total Revenue ($)' => $TotalCc,

            );
            $data = array(
                'totalPayment' => $totalPayment,
                'TotalRevenue' => $TotalRevenue,
                'totalValidation' => $totalValidation,
                'netRevenue' => $netRevenue - ($processingFeeNonValidated + $totalServiceAmount)
            );
            $cardData = array(
                'total' => $TotalCc,
                'totalPayment' => $TotalPaymentReceived,
                'totalDiscount' => $TotalValidatedAmount,
                'usertypedata' => $usertypedata
            );


            $TicketsCashieredAndNonRevnTotal = array(
                'totalTicketsNonValidated' => $totalTicketsNonValidated + $categoryCount,
                'TotalRevenueUpper' => $TotalRevenueNonValidated,
                'TotalRevenueNonValidated' => $TotalRevenueNonValidated,
                'TotalCheckinRevenueNonValidated' => $TotalCheckinRevenueNonValidated,
                'processingFeeNonValidated' => $processingFeeNonValidated,
                'netValueNonValidated' => $netValueNonValidated,
                'totalDuration' => $totalDuration,
                'validatedAmountNonValidated' => $validatedAmountNonValidated,
                'totalDiscountAmountNonValidated' => $totalDiscountAmountNonValidated,
                'driveupDiscountAmount' => $driveupDiscountAmount,
                'totalExtendedAmount' => $totalExtendedAmount,
                'totalExtentionCount' => $totalExtentionCount,
                //for non validate total Amount
                'validationTicketTotal' => $validationTicketTotal,
                'totalGrossAmount' => $totalGrossAmount,
                'totalNetAmount' => $totalNetAmount,
                'validationAmountTotal' => $validationAmountTotal,
                'validationPaidAmountTotal' => $validationPaidAmountTotal,
                'validatedGTotal' => $validatedGTotal,
                'totalTicketsCheckinNonValidated' => $totalTicketsCheckinNonValidated,
                'overAllTicketsCount' => $totalTicketsNonValidated

            );

            $headerData = array(
                'date' => $printFromdate .  ' - ' . $printToDate,
                'locationName' => $locationName,
                'garageCode' => $garageCode
            );
            //dd($reservationTickets);
            //end new code here
            $finalCodes5ResultArray[$facility_id] = [
                'NonRevenuData' => $finalCodes4,
                'ticketCashired' => $finalCodes5,
                'checkinArray' => $checkinArray,
                'nonCashReceipt' => $finalCodes3,
                'cashReceipt' => $finalCodes2,
                'totalpaymentData' => $data,
                'cardData' => $cardData,
                'ticketsCashieredAndNonRevnTotal' => $TicketsCashieredAndNonRevnTotal,
                'headerData' => $headerData,
                'facility' => $facility,
                'partner_id' => $partner_id,
                'secondHeaderData' => $secondHeaderData
            ];
            // if($checkReportCount>0){
            //     throw new ApiGenericException('There is no data for this date');
            // }

            // dd($finalCodes4,$finalCodes5);

            $color  = '#281875';
            $getLogoId = BrandSetting::select('id', 'color')->where('user_id', $partner_id)->first();
            $color = $getLogoId->color;
            // dd( $finalCodes5ResultArray);
            $html = view("revenue_reports.perferd_pdf", ["getAllPdfData" => $finalCodes5ResultArray, 'getLogoId' => $getLogoId->id, 'color' => $color])->render();
            $image = app()->make(Pdf::class);
            $pdf = $image->getOutputFromHtmlString($html, [
                'orientation' => 'Landscape',
                'page-size' => 'A4'
            ]);
            return $pdf;
        }
    }
}
