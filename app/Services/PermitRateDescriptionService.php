<?php

namespace App\Services;

use App\Exceptions\ApiGenericException;
use App\Models\ParkEngage\permitTenureMapping;
use App\Models\PermitRate;
use App\Models\PermitRateDescription;
use Carbon\Carbon;
use DateTime;

class PermitRateDescriptionService
{
    public function getDesiredEndDate(PermitRateDescription $permitRateDescription, $desiredStartDate, $format = 'Y-m-d')
    {
        if (!$permitRateDescription || !$desiredStartDate) {
            return null;
        }

        $startDate = null;


        if ($date = DateTime::createFromFormat('m-d-Y', $desiredStartDate)) {
            if ($date->format('m-d-Y') === $desiredStartDate) {
                $startDate = Carbon::createFromFormat('m-d-Y', $desiredStartDate);
            }
        } elseif ($date = DateTime::createFromFormat('Y-m-d', $desiredStartDate)) {
            if ($date->format('Y-m-d') === $desiredStartDate) {
                $startDate = Carbon::createFromFormat('Y-m-d', $desiredStartDate);
            }
        }
        elseif ($date = DateTime::createFromFormat('Y/m/d', $desiredStartDate)) {
            if ($date->format('Y/m/d') === $desiredStartDate) {
                $startDate = Carbon::createFromFormat('Y/m/d', $desiredStartDate);
            }
        }
        else{
            throw new ApiGenericException("Invalid date format. Use 'm-d-Y' or 'Y-m-d'");
        }

        // if (!$startDate) {
        //     throw new ApiGenericException("Invalid date format. Use 'm-d-Y' or 'Y-m-d'");
        // }

        $unit = $permitRateDescription->permit_frequency_unit;
        $frequency = $permitRateDescription->permit_frequency;

        if (empty($unit) || is_null($unit)) {
            throw new ApiGenericException("Invalid permit details");
        }

        switch ($unit) {
            case 'day':
                $endDate = $startDate->copy()->addDays($frequency);
                break;

            case 'month':
                $endDate = $startDate->copy()->endOfMonth();
                break;

            case 'quarterly':
            case 'year':
                $endDateRaw = isset($permitRateDescription->permit_end_date) ? $permitRateDescription->permit_end_date : null;
                $endDate = $endDateRaw ? Carbon::parse($endDateRaw) : $startDate->copy();
                break;

            default:
                $endDate = $startDate->copy();
        }

        return [$endDate->format($format), $startDate->diffInDays($endDate)];
    }
}
