<?php

namespace App\Services\IM30;

use Mail;
use Auth;
use Response;
use App\Http\Controllers\Controller;
use App\Exceptions\ApiGenericException;
use App\Exceptions\NotFoundException;
use App\Models\ParkEngage\Gate;
use Illuminate\Http\Request;
use App\Http\Helpers\QueryBuilder;
use Carbon\Carbon;
use App\Classes\CommonFunctions;
use Twilio\Rest\Client;
use Twilio\Jwt\ClientToken;
use Twilio\Exceptions\RestException;
use App\Models\Ticket;
use App\Models\OverstayTicket;
use App\Models\Facility;
use App\Models\Reservation;
use App\Models\User;
use Artisan;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\Configuration;
use App\Services\LoggerFactory;
use App\Classes\ParkengageGateApi;
use Storage;
use App\Models\ParkEngage\ParkingDevice;
use App\Models\OauthClient;
use App\Classes\DatacapPaymentGateway;
use App\Jobs\SendSms;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Models\ParkEngage\WorldportLicensePlate;
use App\Models\ParkEngage\KstreetLicensePlate;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\PermitRequest;
use App\Models\ParkEngage\LicensePlate;
use App\Models\ParkEngage\DatacapPaymentProfile;
use App\Models\ParkEngage\FacilityOvernightDuration;
use App\Models\HoursOfOperation;
use App\Models\ParkEngage\TicketExtend;
use App\Models\ParkEngage\TransactionData;

/*
Class Responsible to handle (LPR and IM30 Transient flow ) 
Camras      : [Tattile, Vangaurd, Serviceions]
Locations   : [Townsend, Woodman, kstreet]
Build Type  : DC
Referance   : TattileCheckinCheckoutController.php
*/

class Im30Service
{
    protected $log;
    protected $param;
    protected $vanGarurd;
    protected $request;
    protected $user;
    protected $facility;
    protected $currentTime;

    const  PARTNER_ID = 2980;

    use DispatchesJobs;
    const QUEUE_NAME = 'sms-send';
    const QUEUE_ENTRY = 'read-license-plate';

    const  ANTIPASSBACK_COUNT = 20;

    public function __construct($param = null)
    {
        // $this->request = $request;
        $logFactory = new LoggerFactory();
        $this->log = $logFactory->setPath('logs/transient/im30')->createLogger('im30'); // VP:PIMS-14662
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();
        if ($partnerTimezone) {
            if ($facility->timezone != '') {
                config(['app.timezone' => $facility->timezone]);
                date_default_timezone_set($facility->timezone);
            } else if ($partnerTimezone->timezone != '') {
                config(['app.timezone' => $partnerTimezone->timezone]);
                date_default_timezone_set($partnerTimezone->timezone);
            }
        }
        $this->currentTime = Carbon::parse('now');
    }

    //tickets number is total 7 digit number for kstreet
    protected function checkTicketNumber($facility_id = '')
    {
        if ($facility_id == '') {
            $ticket = 'WP' . rand(100, 999) . rand(100, 999);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $ticket = $this->checkTicketNumber();
            }
        } else {
            $facility = Facility::find($facility_id);
            $prefix = $facility->ticket_prefix != '' ? $facility->ticket_prefix : "PE";
            $fromRange = $facility->from_range != '' ? $facility->from_range : 100000;
            $toRange = $facility->to_range != '' ? $facility->to_range : 999999;
            $ticket = $prefix . rand($fromRange, $toRange);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $ticket =  $this->checkTicketNumber($facility_id);
            }
        }

        return $ticket;
    }

    public function checkTempEticket(Request $request)
    {
        if (isset($request->eticket_id) && !empty($request->eticket_id)) {
            $eticket = explode("-", $request->eticket_id);
            return $eticket['0'] == 'TEMP' ? true : false;
        }
        // else no eticket found return throught if need 
    }

    // This Only Works if checkTempEticket is true
    public function getLPRFeedId(Request $request)
    {
        if (isset($request->eticket_id) && !empty($request->eticket_id)) {
            $eticket = explode("-", $request->eticket_id);
            return $eticket['1'];
        }
        // else no eticket found return throught if need 
    }

    public function getUTCTime($time, $facility)
    {
        $UTCTime = Carbon::parse($time, $facility->timezone)->setTimezone('UTC')->format('Y-m-d H:i:s');
        return $UTCTime;
    }

    public function getUTCToFacilityTimezone($time, $facility)
    {

        $time = Carbon::parse($time, 'UTC')->setTimezone($facility->timezone)->format('Y-m-d H:i:s');
        return $time;
    }

    public function createTicket($request, $facility)
    {
        $ticket = new Ticket();
        $ticket->check_in_datetime  = $request->checkin_time;
        $ticket->checkin_time       = $request->checkin_time;
        $ticket->checkout_time      = Carbon::parse('now')->format('Y-m-d H:i:s');
        $ticket->checkout_datetime  = $request->checkin_time;
        $ticket->estimated_checkout = $request->estimated_checkout;
        $ticket->facility_id        = $facility->id;
        $ticket->partner_id         = $facility->owner_id;
        $ticket->ticket_number      = $this->checkTicketNumber($facility->id);
        $ticket->license_plate      = $request->license_plate;
        $ticket->checkin_gate       = $request->gate;
        $ticket->device_type        = "IM30";
        $ticket->user_id            = $request->user_id;
        $ticket->session_id         = $request->session_id;
        $ticket->total              = $request->total;
        $ticket->grand_total        = $request->grand_total;
        $ticket->parking_amount     = $request->parking_amount;
        $ticket->tax_fee            = $request->tax_fee;
        $ticket->processing_fee     = $request->processing_fee;
        $ticket->additional_fee     = $request->additional_fee;
        $ticket->surcharge_fee      = $request->surcharge_fee;
        $ticket->payment_token      = $request->session_id;
        $ticket->rate_id            = $request->rate_id;
        $ticket->rate_description   = $request->rate_description;
        $ticket->is_checkin         = '1';
        $ticket->save();
        $this->log->info("before checkin 20");
        return $ticket;
    }

    public function createOverstay($request, $facility)
    {
        $ticket = new TicketExtend();
        $ticket->checkin_time       = $request->checkin_time;
        $ticket->checkout_time      = Carbon::parse('now')->format('Y-m-d H:i:s');
        $ticket->facility_id        = $facility->id;
        $ticket->partner_id         = $facility->owner_id;
        $ticket->ticket_number      = $request->ticket_number;
        $ticket->total              = $request->total;
        $ticket->grand_total        = $request->grand_total;
        $ticket->parking_amounts     = $request->parking_amount;
        $ticket->tax_fee            = $request->tax_fee;
        $ticket->processing_fee     = $request->processing_fee;
        $ticket->additional_fee     = $request->additional_fee;
        $ticket->surcharge_fee      = $request->surcharge_fee;
        $ticket->ticket_id          = $request->ticket_id;
        $ticket->save();
        $this->log->info("before checkin 20");
        return $ticket;
    }

    public function getAmountDue($request, $facility)
    {
        $arrival_time           = $request->checkin_time;
        $estimatedCheckout      = $request->estimated_checkout;

        $diffInRealhours = Carbon::parse($arrival_time)->diffInRealhours(Carbon::parse($estimatedCheckout));
        $diffInRealMints = Carbon::parse($arrival_time)->diffInRealMinutes(Carbon::parse($estimatedCheckout));
        $stayOfLength = $diffInRealhours;
        if ($diffInRealhours > 0) {
            $remainingMinutes = $diffInRealMints % 60;
            $stayOfLength = $diffInRealhours . '.' . $remainingMinutes;
        }

        $diff_in_hours          = $stayOfLength;
        $isMember = 0;
        if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
            $rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
        } else {
            $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, '0', $isMember);
        }
        if (isset($rate['price']) && $rate['price'] === 'N/A') {
            throw new ApiGenericException("Garage is currently closed.");
        }
        return $rate;
    }

    public function isTicketExist($request, $facility)
    {
        if (isset($request->ticket_number)) {
            $ticket = Ticket::where(['ticket_number' => $request->ticket_number, ''])->orderBy('id', 'desc')->first();
        } else if (isset($request->license_plate)) {
            $ticket = Ticket::where(['license_plate' => $request->license_plate])->orderBy('id', 'desc')->first();
        }
        return $ticket;
    }








    // Below Code are not in use be

    /* public function parkengageTattileFeed(Request $request)
    {
        try {
            $this->log->info("parkengageTattileFeed Request received --" . json_encode($request->all()));
            //dd($request->transit['plate']['text']);
            //start time $request->transit['timestamps']['start']

            $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("lane_id", $request->transit['lane'])->first();
            if ($gate) {
                $this->setCustomTimezone($gate->facility_id);
                $this->log->info("Request : " . date("Y-m-d H:i:s"));

                $facility = Facility::find($gate->facility_id);
                if ($facility->is_lpr_enabled == '0' || $facility->is_lpr_enabled == 0) {
                    $this->log->info("lpr is disabled");
                    return true;
                }

                $this->saveLicensePlate($request, $gate);

                if ($gate->gate_type == 'entry') {
                    $lastTicket = Ticket::where("facility_id", $gate->facility_id)->whereNull("checkout_time")->orderBy("id", "DESC")->first();

                    if (isset($lastTicket) && $lastTicket->license_plate == $request->transit['plate']['text'] && $lastTicket->is_checkout == '0') {
                        return "Duplicate Feed";
                    }
                    $this->saveCheckin($request, $gate);
                    $this->log->info("Checkin Done");
                } elseif ($gate->gate_type == 'exit') {
                    $this->saveCheckout($request, $gate);
                    $this->log->info("Checkout Done");
                } else {
                    $this->log->info("Invalid Gate");
                }
            } else {
                $this->log->info("Lane Id not matched");
            }
            return "Feed done";
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            return $msg;
        }
    } */


    public function saveCheckin($request, $gate)
    {
        $this->log->info("before checkin");
        try {
            $queue_name = '';
            $gate_type = '';
            if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
                $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
                $gate_type = $gate->gate_type;
            }
            $facility = Facility::with(['facilityConfiguration'])->find($gate->facility_id);
            $checkinTime = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
            //check if garage is closed
            $this->log->info("before checkin 1");
            $existHoursOfOperation = HoursOfOperation::where('facility_id', $gate->facility_id)->get();
            $arrival_time = $checkinTime;
            if (count($existHoursOfOperation) > 0) {
                $this->log->info("before checkin 2");
                $weekday = date('N', strtotime($arrival_time));
                $time = date('H:i:s', strtotime($arrival_time));
                if ($weekday > 6) {
                    $weekday = 7 - $weekday;
                }
                $hoursOfOperation = HoursOfOperation::where('day_of_week', $weekday)->where('facility_id', $gate->facility_id)->get();

                if (count($hoursOfOperation) <= 0) {
                    $this->log->error("garage is closed 1.");
                    return true;
                } else {
                    foreach ($hoursOfOperation as $key => $value) {
                        if ($value->open_time <= $time && $value->close_time >= $time) {
                        } else {
                            $this->log->error("garage is closed 2.");
                            return true;
                        }
                    }
                }
            }
            $this->log->info("before checkin 3");
            $alreadyCheckin = $this->alreadyCheckinStatus($request->transit['plate']['text'], $gate);
            $this->log->info("before checkin 5");
            if ($alreadyCheckin) {
                $this->log->info("before checkin 6");
                if (isset($alreadyCheckin->permit_request_id) && $alreadyCheckin->permit_request_id != '') {
                    //skip for antipass back flow
                    $this->log->info("before checkin 7");
                } else {
                    $this->log->info("You have already checked-in.");
                    $msg = [];
                    $msg['msg'] = 'You have already checked-in.';
                    $this->sendCheckinCheckoutInQueue($alreadyCheckin, '-1', $msg, $queue_name, $gate_type);

                    $ticket_number = base64_encode($alreadyCheckin->ticket_number);
                    $facilityName = ucwords($facility->full_name);

                    $url = env('TOUCHLESS_WEB_URL');
                    $dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $alreadyCheckin->partner_id);
                    if ($dynamicCheckinUrl) {
                        $url = $dynamicCheckinUrl->value;
                    }
                    $checkPaymentUrl = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                    $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                        $join->on('user_facilities.user_id', '=', 'users.id');
                        $join->where('user_facilities.facility_id', "=", $facility->id);
                    })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                    if (isset($getRM->slug)) {
                        $pay = ($getRM->slug) ? $getRM->slug : 'pay';
                    } else {
                        $pay = ($checkPaymentUrl->touchless_payment_url) ? $checkPaymentUrl->touchless_payment_url : 'pay';
                    }
                    if (isset($alreadyCheckin->user->phone) && $alreadyCheckin->user->phone != '') {
                        $sms_msg = "You already have an active check-in " . $alreadyCheckin->ticket_number . ". Use the following link to Pay and Check-Out. $url/$pay/$ticket_number";
                        dispatch((new SendSms($sms_msg, $alreadyCheckin->user->phone))->onQueue(self::QUEUE_NAME));
                    }
                    return true;
                }
            }
            $ticket = new Ticket();
            $response_type = '0';
            $msg = [];
            $isPermit = 0;
            $licensePlate = $request->transit['plate']['text'];
            if ($request->transit['plate']['text'] == "8B03096" || $request->transit['plate']['text'] == "8BO3096" || $request->transit['plate']['text'] == "8BO3O96" || $request->transit['plate']['text'] == "8B03O96") {
                $licensePlate = "8B03096";
            }
            if ($facility->facilityConfiguration->is_check_cloud_permit == '1') {
                $this->log->info("before checkin 8");
                //check permit exist
                $permit = $this->checkExistingPermit($request->transit['plate']['text'], $gate->facility_id, $gate->partner_id);
                if (count($permit) > 0) {
                    $this->log->info("before checkin 10");
                    //check if permit has 2 license and checkin against permit already done
                    if (!isset($alreadyCheckin->permit_request_id) && isset($permit->id)) {
                        $this->log->info("before checkin 11");
                        $totalPermitOpenCheckin = Ticket::where("permit_request_id", $permit->id)->where("is_checkin", '1')->where("is_checkout", '0')->orderBy("id", "DESC")->count();
                        if ($totalPermitOpenCheckin >= self::ANTIPASSBACK_COUNT) {
                            $this->log->info("if permit checkin with other license.");
                            $msg = [];
                            $msg['msg'] = 'You have already checked-in.';
                            $this->sendCheckinCheckoutInQueue($alreadyCheckin, '-1', $msg, $queue_name, $gate_type);
                            return true;
                        }
                    }

                    //if antipassback is disabled then return already checkin msg
                    if (isset($alreadyCheckin->permit_request_id) && ($permit->is_antipass_enabled == 0 || $permit->is_antipass_enabled == "0")) {
                        $this->log->info("You have already checked-in.");
                        $msg = [];
                        $msg['msg'] = 'You have already checked-in.';
                        $this->sendCheckinCheckoutInQueue($alreadyCheckin, '-1', $msg, $queue_name, $gate_type);
                        return true;
                    }

                    if ($permit->is_antipass_enabled == 1 || $permit->is_antipass_enabled == "1") {
                        $this->log->info("is_antipass_enabled is_thirdparty_permit check");
                        // $permit->is_antipass_enabled = "0";
                        // $permit->save();

                        $openTicket = Ticket::where("permit_request_id", $permit->id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
                        if ($openTicket) {
                            $openTicket->is_checkout = '1';
                            $openTicket->checkout_gate = "0";
                            $openTicket->checkout_time = date("Y-m-d H:i:s");
                            $openTicket->checkout_datetime = date("Y-m-d H:i:s");
                            $openTicket->estimated_checkout = date("Y-m-d H:i:s");
                            $openTicket->checkout_license_plate = $licensePlate;
                            $openTicket->checkout_mode = '4';
                            $openTicket->is_transaction_status = '0';
                            $openTicket->checkout_remark = 'Closed due to Anti passback';
                            $openTicket->save();
                        }
                    }
                    $isPermit = 1;
                    $ticket->permit_request_id = $permit->id;
                    $ticket->user_id = $permit->user_id;
                }
            }
            $this->log->info("before checkin 11");
            $ticket->check_in_datetime = $checkinTime;
            $ticket->checkin_time = $checkinTime;
            $ticket->is_checkin = '0';

            if ($isPermit == 1) {
                $this->log->info("before checkin 12");
                $ticket->is_checkin = '1';
                $response_type = '1';
                $msg['permit_number'] = $permit->account_number;
                $msg['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
                $msg['is_phone_linked'] = '0';
                $msg['phone_linked_msg'] = '';
                if (isset($permit->user->id) && $permit->user->phone != '') {
                    $msg['is_phone_linked'] = '1';
                    $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                }
                $msg['booking_type'] = 'permit';
                $msg['is_check_in_ontime'] = '1';
            }

            $reservation = [];
            if ($isPermit != 1) {
                $this->log->info("before checkin 13");
                $reservation = $this->checkExistingReservation($request->transit['plate']['text'], $gate->facility_id);
                if (count($reservation) > 0) {
                    $this->log->info("reservation entered : " . json_encode($reservation));
                    $config = Configuration::where('field_name', 'prepaid-checkin-time')->where("facility_id", $facility->id)->first();
                    if (count($config) > 0) {
                        $prepaidCheckinTime = $config->field_value;
                    } else {
                        $prepaidCheckinTime = 15;
                    }
                    $today = Carbon::createFromFormat('Y-m-d H:i:s', $checkinTime)->addMinutes($prepaidCheckinTime);
                    $reservationstartDate = Carbon::createFromFormat('Y-m-d H:i:s', $reservation->start_timestamp);

                    // converted the decimal lenght to hours and minutes by Ashutosh
                    $time = Carbon::parse($reservation->start_timestamp)->addHours($reservation->length);
                    if (intval($reservation->length) != $reservation->length) {
                        $timarr = explode('.', $reservation->length);
                        // $minute = ('.' . $timarr[1]) * 60; 
                        $time->addMinutes($timarr[1]);
                    }
                    $reservationEndDate = $time->format('Y-m-d H:i:s');
                    $isReservationOnTime = 1;
                    if (strtotime($checkinTime) > strtotime($reservation->start_timestamp) && strtotime($today) > strtotime($reservationEndDate)) {

                        if (strtotime($checkinTime) > strtotime($reservation->start_timestamp) && strtotime($checkinTime) > strtotime($reservationEndDate)) {
                            $isReservationOnTime = 2;
                        } else {
                            $isReservationOnTime = 1;
                        }
                    }
                    $this->log->info("before checkin 14");
                    if (strtotime($today) < strtotime($reservation->start_timestamp)) {
                        $isReservationOnTime = 2;
                    } else {
                        $isReservationOnTime = 1;
                    }
                    $this->log->info("before checkin 15");
                    if ($isReservationOnTime == 1) {
                        $this->log->info("before checkin 16");
                        $response_type = '1';
                        $this->log->info("reservation checkin");
                        $resevationExitTime = date("Y-m-d H:i:s", strtotime($reservation->start_timestamp->addHour($reservation->length)));
                        $resevationExitTime = Carbon::parse($resevationExitTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
                        $ticket->reservation_id = $reservation->id;
                        $ticket->check_in_datetime = $reservation->start_timestamp;
                        $ticket->checkout_datetime = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                        $ticket->estimated_checkout = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                        //$ticket->payment_date = date("Y-m-d H:i:s", strtotime($resevationExitTime));
                        $ticket->length = $reservation->length;
                        $ticket->is_checkin = '1';
                        $ticket->user_id = $reservation->user_id;
                        $ticket->session_id = $reservation->session_id;
                        $msg['booking_number'] = $reservation->ticketech_code;
                        $msg['booking_start_time'] = date("g:i A", strtotime($reservation->start_timestamp));
                        $msg['booking_exit_time'] = date("g:i A", strtotime($resevationExitTime));
                        $msg['booking_entry_time'] = date("g:i A", strtotime(date('Y-m-d H:i:s')));
                        $msg['is_phone_linked'] = '0';
                        $msg['phone_linked_msg'] = '';
                        if (isset($reservation->user->id) && $reservation->user->phone != '') {
                            $msg['is_phone_linked'] = '1';
                            $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                        }
                        $msg['booking_type'] = 'reservation';
                        $msg['is_check_in_ontime'] = '1';
                        $this->log->info("reservation check : " . json_encode($msg));
                    }
                }
                $this->log->info("before checkin 17");
                //start code for breeze express flow same as for townsend controller vikrant 02-04-2025

                $permitVehicles = PermitVehicle::with(['user'])->where("license_plate_number", $licensePlate)->where("partner_id", $facility->owner_id)->first();
                if ($permitVehicles && isset($permitVehicles->user->id)) {
                    $this->log->info("before checkin 18");
                    $user = $permitVehicles->user;
                    if ($user->is_auto_checkout_enabled == '1') {
                        $ticket->is_checkin = '1';
                        $ticket->user_id = $user->id;
                        $response_type = '1';
                        $msg['booking_type'] = 'driveup';
                        $msg['is_phone_linked'] = $user->phone != '' ? '1' : '0';
                        $msg['phone_linked_msg'] = $user->phone != '' ? 'Your e-Ticket has been sent on your registered phone number.' : '';
                        $msg['is_check_in_ontime'] = '1';
                        $msg['is_our_ticket'] = '1';
                        $msg['checkin_time'] = date("g:i A", strtotime($checkinTime));
                    }
                }
                //end code for breeze express flow same as for townsend controller  vikrant
            }
            $this->log->info("before checkin 19");

            $ticket->facility_id = $gate->facility_id;
            $ticket->partner_id = $facility->owner_id;
            $ticketNumber = $this->checkTicketNumber($gate->facility_id);
            $isExist = Ticket::where('ticket_number', $ticketNumber)->first();
            if ($isExist) {
                $ticket->ticket_number = $this->checkTicketNumber($gate->facility_id);
            } else {
                $ticket->ticket_number = $ticketNumber;
            }
            $ticket->license_plate = $licensePlate != '' ? $licensePlate : $request->transit['plate']['text'];
            $ticket->checkin_gate = $gate->gate;
            $ticket->device_type = "LPR";
            $ticket->save();
            $this->log->info("before checkin 20");
            if (count($reservation) > 0) {
                $reservation->is_ticket = '1';
                $reservation->save();
            }
            if ($isPermit == 1) {
                $this->log->info("before checkin 21");
                //update pass back status 0 default 1 for checkin 2 for checkout 3 for reset
                $permit->passback_status = "1";
                $permit->save();
                $ticketCount = Ticket::where("user_id", $permit->user_id)->where("is_checkout", "0")->count();
                if ($ticketCount >= self::ANTIPASSBACK_COUNT) {
                    $permit->is_antipass_enabled = "0";
                    $permit->save();
                    $this->log->info("disable is_antipass_enabled permit");
                }
            }

            $this->log->info("checkin done {$ticket->ticket_number}");
            $msg['eticket_id'] =  $ticket->ticket_number;
            $msg['msg'] = "WELCOME. #" . $ticket->ticket_number;
            $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '1');
            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

            if ($ticket->user_id != '') {
                $this->log->info("before checkin 22");
                $user = User::find($ticket->user_id);
                if ($user->email != '') {
                    //Artisan::queue('email:touchless-parking-atlanta-prepaid-confirm-checkin', array('id' => $ticket->id, 'type' => 'checkin'));
                }

                $facilityName = ucwords($facility->full_name);
                //$sms_msg = "Thank you for Check-In with $facilityName. Your ticket number is $result->ticket_number.";
                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $facility->owner_id)->first();
                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                    $join->on('user_facilities.user_id', '=', 'users.id');
                    $join->where('user_facilities.facility_id', "=", $facility->id);
                })->where('created_by', $facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                if (isset($getRM->slug)) {
                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                } else {
                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                }

                $url = env('TOUCHLESS_WEB_URL');
                $dynamicCheckinUrl = QueryBuilder::getPartnerConfig("CHECKIN_URL", $facility->owner_id);
                if ($dynamicCheckinUrl) {
                    $url = $dynamicCheckinUrl->value;
                }
                $grace_period = $facility->grace_period_minute;
                $ticket_number = base64_encode($ticket->ticket_number);
                $smsUrl = "{$url}/{$name}/{$ticket_number}";
                if ($isPermit == 1) {
                    $sms_msg = "Thank you for the Check-in at $facilityName. This check-in is created against Permit #{$permit->account_number}. Use {$smsUrl} to view your ticket details.";
                } elseif ($ticket->reservation_id > 0) {
                    $sms_msg = "Thank you for the Check-in at $facilityName. This check-in is created against Reservation #{$reservation->ticketech_code}. Use {$smsUrl} to view your ticket details.";
                } else {
                    $sms_msg = "Thank you for parking at " . $facilityName . ". Your e-ticket number is $ticket->ticket_number. Use the following link to PAY and EXIT. You must exit within $grace_period minutes of making payment. {$smsUrl}";
                }
                dispatch((new SendSms($sms_msg, $user->phone))->onQueue(self::QUEUE_NAME));
            }
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error("error received --" . $msg);
            Mail::send(
                "parkengage.notify-mq-listner",
                ['is_failed' => '1', 'msg' => $msg],
                function ($message) {
                    $message->to(['<EMAIL>', '<EMAIL>'])->subject("Tattile Info : Tattile Exception");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            return true;
        }
    }

    public function checkExistingReservation($plate, $facility_id)
    {
        $reservation = Reservation::with('user')->where("license_plate", $plate)->where("facility_id", $facility_id)->whereDate('start_timestamp', '=', date('Y-m-d'))->where('is_ticket', '0')->whereNull('cancelled_at')->first();
        if (!$reservation) {
            return $reservation;
        }
        return $reservation;
    }

    public function alreadyCheckinStatus($plate, $gate)
    {
        $ticket = Ticket::with(['user'])->where("license_plate", $plate)->where("facility_id", $gate->facility_id)->where("checkin_gate", $gate->gate)->where("is_checkin", '1')->where("is_checkout", '0')->first();
        return $ticket;
    }


    public function sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type)
    {
        $this->log->info("before queue {$response_type}");
        $msgRespone = [];
        $msgRespone = (object) $msg;
        $this->log->info("Json response  " . json_encode(['eticket_id' => $ticket['ticket_number'], 'license_plate' => $ticket['license_plate'], 'data' => $msgRespone, 'response_type' => $response_type]));
        $myArray = json_encode(['eticket_id' => $ticket['ticket_number'], 'license_plate' => $ticket['license_plate'], 'data' => $msgRespone, 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
        if ($queue_name == '') {
            $queue_name = self::QUEUE_ENTRY;
        }
        $queue->pushRaw($myArray, $queue_name);
        $this->log->info("data send " . $queue_name);
        //Artisan::queue('read-license-plate', array('license_plate' => $ticket->license_plate, 'is_checkin_or_checkout' => '1', 'ticket' => $ticket, 'response_type' => $response_type, "msg" => $msg, 'queue_name' => $queue_name, 'gate_type' => $gate_type));
        return $ticket;
    }


    //tickets number is total 7 digit number for kstreet
    protected function backp_27_07_2025checkTicketNumber($facility_id = '')
    {
        if ($facility_id == '') {
            $ticket = 'WP' . rand(100, 999) . rand(100, 999);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $ticket = $this->checkTicketNumber();
            }
        } else {
            $facility = Facility::find($facility_id);
            $prefix = $facility->ticket_prefix != '' ? $facility->ticket_prefix : "PE";
            $fromRange = $facility->from_range != '' ? $facility->from_range : 100000;
            $toRange = $facility->to_range != '' ? $facility->to_range : 999999;
            $ticket = $prefix . rand($fromRange, $toRange);
            $isExist = Ticket::where('ticket_number', $ticket)->first();
            if ($isExist) {
                $ticket =  $this->checkTicketNumber($facility_id);
            }
        }

        return $ticket;
    }


    public function saveCheckout($request, $gate)
    {

        $this->log->info("checkout start");
        $this->log->info("checkout start Print Request " . json_encode($request->all()));
        $queue_name = '';
        $gate_type = '';
        if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
            $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
            $gate_type  = $gate->gate_type;
        }

        // VanGaurd Camra
        // VP:PIMS-14662
        if (isset($request->camera_type) && $request->camera_type == '2') {
            $licensePlate = $request->license_plate;
        } else {
            $licensePlate = $request->transit['plate']['text'];
        }
        if (in_array($licensePlate, ["8B03096", "8BO3096", "8BO3O96", "8B03O96"])) {
            $licensePlate = "8B03096";
        }
        // Close VP:PIMS-14662

        /* if ($request->transit['plate']['text'] == "8B03096" || $request->transit['plate']['text'] == "8BO3096" || $request->transit['plate']['text'] == "8BO3O96" || $request->transit['plate']['text'] == "8B03O96") {
            $licensePlate = "8B03096";
        } */


        // VP:PIMS-14662
        if (isset($request->camera_type) && $request->camera_type == '2') {
            $licensePlate = $request->license_plate;
            $today = date("Y-m-d", strtotime($request->nycLprReadTime));
            $checkoutTime = date("Y-m-d H:i:s", strtotime($request->nycLprReadTime));
        } else {
            $today = date("Y-m-d", strtotime($request->transit['timestamps']['start']));
            $checkoutTime = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
        }
        // Closed VP:PIMS-14662


        $this->log->info("checkout start 22");
        $ticket = Ticket::with(['facility.FacilityPaymentDetails', 'reservation', 'user'])->where('license_plate', $licensePlate)->where('facility_id', $gate->facility_id)->where('is_checkout', '0')->whereNull("checkout_time")->orderBy("id", "DESC")->first();

        if ($ticket) {
            $this->log->info("checkout start 23");
            if ($ticket->is_transaction_status == '1') {
                $this->log->info("Payment already in process " . $ticket->ticket_number);
                return true;
            }

            $is_our_ticket = '1';
            if ($ticket->is_checkin == '0' && $ticket->is_checkout == '0') {
                $is_our_ticket = '0';
            }

            $checkinDate = date("Y-m-d", strtotime($ticket->checkin_time));
            /* $checkinDate = date("Y-m-d", strtotime($ticket->checkin_time));
            $today = date("Y-m-d", strtotime($request->transit['timestamps']['start']));
            $checkoutTime = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start'])); */

            $updatedCheckinTime = '';
            if (strtotime($today) != strtotime($checkinDate)) {
                $updatedCheckinTime = date("d M", strtotime($checkinDate)) . ' ' . date("g:i A", strtotime($ticket->checkin_time));
            } else {
                $updatedCheckinTime = date("g:i A", strtotime($ticket->checkin_time));
            }

            $this->log->info("checkout start 24");
            // permit check section before checkout 
            $facility = Facility::with(['facilityConfiguration'])->find($gate->facility_id);
            if ($facility->facilityConfiguration->is_check_cloud_permit == '1') {
                if ($ticket->permit_request_id != '') {
                    $this->log->info("checkout start 25");
                    $ticket->is_checkout            = '1';
                    $ticket->checkout_gate          = $gate->gate;
                    $ticket->checkout_time          = $checkoutTime;
                    $ticket->checkout_datetime      = $checkoutTime;
                    $ticket->estimated_checkout     = $checkoutTime;
                    $ticket->payment_date           = $checkoutTime;
                    // VP:PIMS-14662
                    // $ticket->checkout_license_plate = $request->transit['plate']['text'];
                    $ticket->checkout_license_plate = $licensePlate;
                    $ticket->checkout_session_id    = $ticket->session_id;
                    $ticket->checkout_mode          = '4';
                    $ticket->is_transaction_status  = '0';
                    $this->log->info(json_encode($ticket));
                    $ticket->save();

                    $permit = PermitRequest::find($ticket->permit_request_id);
                    if ($permit->is_antipass_enabled == "1" || $permit->is_antipass_enabled == 1) {
                        $this->log->info("checkout is_antipass_enabled tickets");
                        if ($ticket) {
                            $openTicket = Ticket::where("user_id", $permit->user_id)->where("is_checkout", '0')->orderBy("id", "DESC")->first();
                            if ($openTicket) {
                                $openTicket->is_checkout = '1';
                                $openTicket->checkout_gate = $gate->gate;
                                $openTicket->checkout_time = date("Y-m-d H:i:s");
                                $openTicket->checkout_datetime = date("Y-m-d H:i:s");
                                $openTicket->estimated_checkout = date("Y-m-d H:i:s");
                                $openTicket->checkout_license_plate = $licensePlate;
                                $openTicket->checkout_mode = '4';
                                $openTicket->is_transaction_status = '0';
                                $openTicket->checkout_remark = 'Closed due to Anti passback';
                                $openTicket->save();

                                $permit->is_antipass_enabled = "0";
                                $permit->save();
                            }
                        }
                    }

                    //update pass back status 0 default 1 for checkin 2 for checkout 3 for reset
                    $permit->passback_status = "2";
                    $permit->save();

                    $response_type = '1';
                    $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                    $msg['is_phone_linked']         = '0';
                    $msg['phone_linked_msg']        = '';
                    if (isset($ticket->user->id) && $ticket->user->phone != '') {
                        $msg['is_phone_linked']     = '1';
                        $msg['phone_linked_msg']    = 'Your e-Ticket has been sent on your registered phone number.';
                    }
                    $msg['booking_type']            = 'permit';
                    $msg['is_check_in_ontime']      = '1';
                    $msg['is_our_ticket']           = $is_our_ticket;
                    $msg['eticket_id']              = $ticket->ticket_number;
                    $msg["print_receipt"]           = QueryBuilder::setPrintReceipt($ticket, '0');
                    $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                    $this->log->info("checkout start 26");
                    if (isset($ticket->user->phone)) {
                        $this->log->info("SMS condition entered");
                        $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                        $facility = $ticket->facility;
                        $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                            $join->on('user_facilities.user_id', '=', 'users.id');
                            $join->where('user_facilities.facility_id', "=", $facility->id);
                        })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                        if (isset($getRM->slug)) {
                            $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                        } else {
                            $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                        }
                        $url = env('RECEIPT_URL');
                        $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
                        //changes for breeze specific URL from DB
                        if ($dynamicReceiptUrl) {
                            $url = $dynamicReceiptUrl->value;
                        }
                        $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                        $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                        dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                    }
                    return true;
                }
            }
            $this->log->info("checkout start 27");
            // !!! Close section close.

            //overstay section start for gated flow
            if ($ticket->estimated_checkout != '') {
                $overstayExist = OverstayTicket::where("ticket_id", $ticket->id)->orderBy("id", "DESC")->first();
                if ($overstayExist) {
                    if (strtotime($overstayExist->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
                        $this->log->info("checkout start 28");
                        $ticket->is_checkout = '1';
                        $ticket->checkout_gate = $gate->gate;
                        $ticket->checkout_time = $checkoutTime;
                        $ticket->checkout_datetime = $checkoutTime;
                        $ticket->checkout_license_plate = $licensePlate;
                        $ticket->checkout_session_id = $ticket->session_id;
                        $ticket->checkout_mode = '4';
                        $ticket->is_transaction_status = '0';
                        $this->log->info(json_encode($ticket));
                        $ticket->save();

                        $response_type = '1';
                        $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                        $msg['is_phone_linked'] = '0';
                        $msg['phone_linked_msg'] = '';
                        if (isset($ticket->user->id) && $ticket->user->phone != '') {
                            $msg['is_phone_linked'] = '1';
                            $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                        }
                        $msg['booking_type'] = 'reservation';
                        $msg['is_check_in_ontime'] = '1';
                        $msg['is_our_ticket'] = $is_our_ticket;
                        $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                        if (isset($ticket->user->phone) && $ticket->user->phone != '') {
                            $this->log->info("SMS condition entered");
                            $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                            $facility = $ticket->facility;
                            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                $join->on('user_facilities.user_id', '=', 'users.id');
                                $join->where('user_facilities.facility_id', "=", $facility->id);
                            })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();

                            if (isset($getRM->slug)) {
                                $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                            } else {
                                $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                            }

                            $url = env('RECEIPT_URL');
                            $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
                            //changes for breeze specific URL from DB
                            if ($dynamicReceiptUrl) {
                                $url = $dynamicReceiptUrl->value;
                            }
                            $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                            $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                            dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                        }
                        return true;
                    } else {
                        $this->log->info("eticket overstay created.");
                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayExist->payment_date);
                        $from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
                        $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayExist->payment_date);
                        $isMember = 0;
                        if ($ticket->facility->is_hourly_rate) {
                            $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
                        } else {
                            $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
                        }

                        if ($rate['price'] <= 0) {
                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();

                            $response_type = '1';
                            $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                $msg['is_phone_linked'] = '1';
                                $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                            }
                            $msg['booking_type'] = 'reservation';
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        $taxRate = $ticket->getTaxRate($rate);          // to get tax price
                        $rate['price'] = $rate['price'] + $taxRate;
                        if ($ticket->facility->is_lpr_cloud_payment_enabled == '1') {

                            $ticket->is_transaction_status = '1';
                            $ticket->save();
                            $this->log->info("is_lpr_cloud_payment_enabled entered for overstay");
                            if ($ticket->session_id == '') {
                                $ticket->is_transaction_status = '0';
                                $ticket->save();
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $ticket->is_transaction_status = '0';
                            $ticket->save();
                            $response_type = '3';
                            $msg = [];
                            $msg['msg'] = "Amount Due";
                            $msg['is_phone_linked'] = '0';
                            $msg['checkin_time'] = $updatedCheckinTime;
                            $msg['amount'] = $rate['price'];
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $msg['is_overstay'] =  '1';
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;

                            $refundstatus = PlanetPaymentGateway::planetPaymentByToken($ticket, $rate['price'], $request);
                            $this->log->info("Overstay Payment Response :" . json_encode($refundstatus));
                            //if payment error start
                            if (!in_array($refundstatus["Response"]["Params"]["TxState"], ['AP', 'AA', 'CQ'])) {
                                $this->log->error(" Overstay Payment failed :");

                                $ticket->is_transaction_status = '0';
                                $ticket->save();

                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due, Please use different card.";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $planetTransaction = PlanetPaymentGateway::savePlanetTransaction($refundstatus, '1');

                            $overstay = $this->saveOverstayDetails($rate, $ticket, $ticket->facility);
                            $overstay->total = $rate['price'];
                            $overstay->grand_total = $rate['price'];
                            $overstay->anet_transaction_id = $planetTransaction->id;
                            $overstay->length = $diff_in_hours;
                            $overstay->tax_fee = $taxRate;
                            $overstay->save();


                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();



                            $response_type = '1';
                            $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                $msg['is_phone_linked'] = '1';
                                $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                            }
                            $msg['booking_type'] = 'reservation';
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        $response_type = '3';
                        $msg = [];
                        $msg['msg'] = "Amount Due";
                        $msg['is_phone_linked'] = '0';
                        $msg['checkin_time'] = $updatedCheckinTime;
                        $msg['amount'] = $rate['price'];
                        $msg['is_our_ticket'] = $is_our_ticket;
                        $msg['is_overstay'] =  '1';
                        $msg['eticket_id'] =  $ticket->ticket_number;
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                        return true;
                    }
                } else {
                    $this->log->info("checkout start 29");
                    if (strtotime($ticket->estimated_checkout) >= strtotime(date('Y-m-d H:i:s'))) {
                        $this->log->info("overstay created1111.");
                        $ticket->is_checkout = '1';
                        $ticket->checkout_gate = $gate->gate;
                        $ticket->checkout_time = $checkoutTime;
                        $ticket->checkout_datetime = $checkoutTime;
                        $ticket->checkout_license_plate = $licensePlate;
                        $ticket->checkout_session_id = $ticket->session_id;
                        $ticket->checkout_mode = '4';
                        $ticket->is_transaction_status = '0';
                        $this->log->info(json_encode($ticket));
                        $ticket->save();

                        if ($ticket->reservation_id != '') {
                            $ticket->reservation->is_ticket = '1';
                            $ticket->reservation->save();
                        }

                        $response_type = '1';
                        $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                        $msg['is_phone_linked'] = '0';
                        $msg['phone_linked_msg'] = '';
                        if (isset($ticket->user->id) && $ticket->user->phone != '') {
                            $msg['is_phone_linked'] = '1';
                            $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                        }
                        $msg['booking_type'] = 'reservation';
                        $msg['is_check_in_ontime'] = '1';
                        $msg['is_our_ticket'] = $is_our_ticket;
                        $msg['eticket_id'] = $ticket->ticket_number;
                        $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

                        if (isset($ticket->user->phone) && $ticket->user->phone != '') {
                            $this->log->info("SMS condition entered");
                            $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                            $facility = $ticket->facility;
                            $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                $join->on('user_facilities.user_id', '=', 'users.id');
                                $join->where('user_facilities.facility_id', "=", $facility->id);
                            })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();

                            if (isset($getRM->slug)) {
                                $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                            } else {
                                $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                            }

                            $url = env('RECEIPT_URL');
                            $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
                            //changes for breeze specific URL from DB
                            if ($dynamicReceiptUrl) {
                                $url = $dynamicReceiptUrl->value;
                            }
                            $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                            $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                            dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                        }

                        return true;
                    } else {
                        $this->log->info("overstay created.");

                        $overstayCheckinTime = $ticket->payment_date != '' ? $ticket->payment_date : $ticket->estimated_checkout;

                        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $overstayCheckinTime);
                        $from = Carbon::createFromFormat('Y-m-d H:i:s', date("Y-m-d H:i:s"));
                        $diff_in_hours = $ticket->getCheckOutCurrentTime(true, $overstayCheckinTime);

                        $this->log->info("get Diff in Hours in new format rateDiffInHour : {$diff_in_hours}");
                        $isOvernight = $this->isOvernightOrNormal($ticket);
                        if ($isOvernight) {
                            $this->log->info("isOvernight yes :");
                            // $rate = $facility->rateForReservationOnMarker($arrival_time, $rateDiffInHour, false, false,  true, true, false, 0, $isMember);
                            $rate['price'] =  0;
                        } else {
                            $isMember = 0;
                            if ($ticket->facility->is_hourly_rate == '1') {
                                $this->log->info("isOvernight no : 1");
                                $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
                            } else {
                                $this->log->info("isOvernight no : 2");
                                $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, 0, $isMember);
                            }
                        }
                        $this->log->info("getovertya parking amount : {$rate['price']}");
                        if ($rate['price'] <= 0) {
                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();

                            $response_type = '1';
                            $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                $msg['is_phone_linked'] = '1';
                                $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                            }
                            $msg['booking_type'] = 'reservation';
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        $taxRate = $ticket->getTaxRate($rate);          // to get tax price
                        $rate['price'] = $rate['price'] + $taxRate;
                        $total = $rate['price'];
                        $this->log->info("getovertya payable amount : {$total}");
                        if (isset($ticket->user->is_auto_checkout_enabled) && $ticket->user->is_auto_checkout_enabled == '1') {

                            $ticket->is_transaction_status = '1';
                            $ticket->save();
                            $this->log->info("is_auto_checkout_enabled entered for overstay");
                            $paymentProfile = DatacapPaymentProfile::where("user_id", $ticket->user_id)->where("partner_id", $ticket->partner_id)->where("is_default", '1')->first();
                            if (!$paymentProfile) {
                                $ticket->is_transaction_status = '0';
                                $ticket->save();
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $msg['eticket_id'] =  $ticket->ticket_number;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $this->log->info("is_auto_checkout_enabled express entered");


                            $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                            $datacap['Amount'] = $total;
                            $datacap['Token'] = $paymentProfile->token;
                            $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);

                            $this->log->info("is_auto_checkout_enabled datacap Payment Response --" . json_encode($paymentResponse));
                            if ($paymentResponse["Status"] == "Error") {
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    $this->log->info("datacap Payment Error Data -- 1");
                                } else {
                                    $this->log->info("datacap Payment Error Data -- 2");
                                }
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $total;
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $msg['eticket_id'] =  $ticket->ticket_number;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            } else if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;
                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                            } else {
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $msg['eticket_id'] =  $ticket->ticket_number;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $overstay = $this->saveOverstayDetails($rate, $ticket, $ticket->facility);
                            $overstay->total = $total;
                            $overstay->grand_total = $total;
                            $overstay->anet_transaction_id = $planetTransaction->id;
                            $overstay->length = $diff_in_hours;
                            $overstay->tax_fee = $taxRate;
                            $overstay->parking_amount = $total - $taxRate;
                            $overstay->partner_id = $ticket->partner_id;
                            $overstay->save();

                            $ticket->is_overstay = '1';
                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            $ticket->checkout_session_id = $paymentProfile->card_holder_id;
                            $ticket->checkout_payment_token = $paymentProfile->token;
                            $ticket->checkout_card_last_four = $paymentProfile->card_last_four;
                            $ticket->checkout_expiry = $paymentProfile->expiry;
                            $ticket->checkout_card_type = $paymentProfile->card_type;
                            $ticket->checkout_mode = '4';
                            $ticket->is_cloud_payment = '1';
                            $ticket->is_transaction_status = '0';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();

                            $this->saveOverstayTransactionData($ticket, $overstay, $facility);

                            $response_type = '1';
                            $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                            $msg['msg'] = "THANK YOU FOR VISITING " . $facilityName . ".  Extra $" . $total . " is charged for overstay.";
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                $msg['is_phone_linked'] = '1';
                                $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                            }
                            $msg['booking_type'] = 'reservation';
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

                            if (isset($ticket->user->phone) && $ticket->user->phone != '') {
                                $this->log->info("SMS condition entered");
                                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                                $facility = $ticket->facility;
                                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                    $join->on('user_facilities.user_id', '=', 'users.id');
                                    $join->where('user_facilities.facility_id', "=", $facility->id);
                                })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();

                                if (isset($getRM->slug)) {
                                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                                } else {
                                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                }

                                $url = env('RECEIPT_URL');
                                $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
                                //changes for breeze specific URL from DB
                                if ($dynamicReceiptUrl) {
                                    $url = $dynamicReceiptUrl->value;
                                }
                                $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                                $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                                dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                            }
                            return true;
                        }
                        //if LPR is enabled
                        else if ($ticket->facility->is_lpr_cloud_payment_enabled == '1') {

                            $ticket->is_transaction_status = '1';
                            $ticket->save();
                            $this->log->info("is_lpr_cloud_payment_enabled entered for overstay");
                            if ($ticket->session_id == '') {
                                $ticket->is_transaction_status = '0';
                                $ticket->save();
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $msg['eticket_id'] =  $ticket->ticket_number;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                            $datacap['Amount'] = $total;
                            $datacap['Token'] = $ticket->payment_token;
                            $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);
                            $this->log->info("datacap Payment Response --" . json_encode($paymentResponse));
                            if ($paymentResponse["Status"] == "Error") {
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    $this->log->info("datacap Payment Error Data -- 1");
                                } else {
                                    $this->log->info("datacap Payment Error Data -- 2");
                                }
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $msg['eticket_id'] =  $ticket->ticket_number;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            } else if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;
                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                            } else {
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $rate['price'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $msg['is_overstay'] =  '1';
                                $msg['eticket_id'] =  $ticket->ticket_number;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $overstay = $this->saveOverstayDetails($rate, $ticket, $ticket->facility);
                            $overstay->total = $total;
                            $overstay->grand_total = $total;
                            $overstay->anet_transaction_id = $planetTransaction->id;
                            $overstay->length = $diff_in_hours;
                            $overstay->tax_fee = $taxRate;
                            $overstay->parking_amount = $total - $taxRate;
                            $overstay->partner_id = $ticket->partner_id;
                            $overstay->save();

                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_transaction_status = '0';
                            $this->log->info(json_encode($ticket));
                            $ticket->save();

                            $this->saveOverstayTransactionData($ticket, $overstay, $facility);

                            $response_type = '1';
                            $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                            $msg['msg'] = "THANK YOU FOR VISITING " . $facilityName . ".  Extra $" . $total . " is charged for overstay.";
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                $msg['is_phone_linked'] = '1';
                                $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                            }
                            $msg['booking_type'] = 'reservation';
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

                            if (isset($ticket->user->phone) && $ticket->user->phone != '') {
                                $this->log->info("SMS condition entered");
                                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                                $facility = $ticket->facility;
                                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                    $join->on('user_facilities.user_id', '=', 'users.id');
                                    $join->where('user_facilities.facility_id', "=", $facility->id);
                                })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();

                                if (isset($getRM->slug)) {
                                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                                } else {
                                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                }

                                $url = env('RECEIPT_URL');
                                $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
                                //changes for breeze specific URL from DB
                                if ($dynamicReceiptUrl) {
                                    $url = $dynamicReceiptUrl->value;
                                }
                                $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                                $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                                dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                            }
                            return true;
                        }

                        $response_type = '3';
                        $msg = [];
                        $msg['msg'] = "Amount Due";
                        $msg['is_phone_linked'] = '0';
                        $msg['checkin_time'] = $updatedCheckinTime;
                        $msg['amount'] = $rate['price'];
                        $msg['is_our_ticket'] = $is_our_ticket;
                        $msg['is_overstay'] =  '1';
                        $msg['eticket_id'] =  $ticket->ticket_number;
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                        return true;
                    }
                }
            }

            //overstay section end for gated flow

            $this->log->info("checkout start 30");
            $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
            $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

            $diff_in_hours = $ticket->getCheckinCheckOutDifference($checkoutTime);
            //$this->log->error($arrival_time.'--'. $diff_in_hours.'--'. $ticket->checkin_time. '--'.$checkoutTime);
            $isMember = 0;
            if ($ticket->facility->is_hourly_rate) {
                $this->log->error("worldport case");
                $rate = $ticket->facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
            } else {
                $this->log->error("townsnend case");
                $rate = $ticket->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false, false, true, false, 0, $isMember);
            }
            if ($rate == false) {
                $this->log->error("rate not found");
            }

            $this->log->error($arrival_time . '--' . $diff_in_hours . '--' . $ticket->checkin_time . '--' . $checkoutTime . '--' . $rate['price']);

            $priceBreakUp = $ticket->priceBreakUp($rate);
            $this->log->info("priceBreakUp " . json_encode($priceBreakUp));
            $rate_id = isset($rate['id']) ? $rate['id'] : '0';
            $rate_description = isset($rate['description']) ? $rate['description'] : '';
            if ($is_our_ticket == '1') {
                $this->log->info("checkout start 31");
                if ($priceBreakUp['payable_amount'] > 0 && $priceBreakUp['payable_amount'] <= $ticket->facility->facilityConfiguration->max_transaction_capping_amount) {
                    //check is express breeze registered
                    if (isset($ticket->facility->facilityConfiguration->is_lpr_registered_user_payment_enabled) && $ticket->facility->facilityConfiguration->is_lpr_registered_user_payment_enabled == '1') {
                        $this->log->info("is_lpr_registered_user_payment_enabled LPR before checkout");
                        if (!isset($ticket->facility->FacilityPaymentDetails->id)) {
                            throw new ApiGenericException('Payment details not defined against this garage.');
                        }
                        $user = $ticket->user;
                        if (isset($user->is_auto_checkout_enabled) && $user->is_auto_checkout_enabled == '1') {

                            //save temorporary amount in ticket temp table to save in driverup
                            QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
                            $ticket->is_transaction_status = '1';

                            $this->log->info("is_lpr_cloud_payment_enabled entered");
                            $paymentProfile = DatacapPaymentProfile::where("user_id", $ticket->user_id)->where("partner_id", $ticket->partner_id)->where("is_default", '1')->first();

                            if (!$paymentProfile) {
                                $this->log->info("is_lpr_cloud_payment_enabled payment profile not found");
                                $ticket->is_transaction_status = '0';
                                $ticket->save();
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $priceBreakUp['payable_amount'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                            $request->request->add(['total' => $priceBreakUp['payable_amount']]);
                            $datacap['Amount'] = $priceBreakUp['payable_amount'];
                            $datacap['Token'] = $paymentProfile->token;
                            $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);

                            $this->log->info("datacap Payment Response --" . json_encode($paymentResponse));

                            if (isset($paymentResponse['Status']) && in_array($paymentResponse['Status'], ['Approved', 'Approval', 'Success'])) {

                                $user_id = $ticket->user_id;
                                $request->request->add(['card_last_four' => $paymentProfile->card_last_four]);
                                $request->request->add(['expiration' => $paymentProfile->expiry]);
                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $ticket->anet_transaction_id = $planetTransaction->id;
                            } else {
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    $this->log->info("datacap Payment Error Data -- 1");
                                } else {
                                    $this->log->info("datacap Payment Error Data -- 2");
                                }
                                $ticket->is_transaction_status = '0';
                                $ticket->save();

                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due, Please use different card.";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $priceBreakUp['payable_amount'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $ticket->processing_fee  = $priceBreakUp['processing_fee'];
                            $ticket->tax_fee        = $priceBreakUp['tax_rate'];
                            $ticket->parking_amount  = $priceBreakUp['parking_amount'];
                            $ticket->paid_amount     = $priceBreakUp['paid_amount'];
                            $ticket->discount_amount = $priceBreakUp['discount_amount'];
                            $ticket->grand_total     = $priceBreakUp['payable_amount'];
                            $ticket->total     = $priceBreakUp['total'];
                            $ticket->length     = $diff_in_hours;

                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->estimated_checkout = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            //$request->transit['plate']['text']
                            $ticket->checkout_session_id = $paymentProfile->card_holder_id;
                            $ticket->checkout_payment_token = $paymentProfile->token;
                            $ticket->checkout_card_last_four = $paymentProfile->card_last_four;
                            $ticket->checkout_expiry = $paymentProfile->expiry;
                            $ticket->checkout_card_type = $paymentProfile->card_type;
                            $ticket->checkout_mode = '4';
                            $ticket->is_cloud_payment = '1';
                            $ticket->is_transaction_status = '0';
                            $ticket->rate_id = $rate_id;
                            $ticket->rate_description = $rate_description;
                            $this->log->info(json_encode($ticket));
                            $ticket->save();

                            $this->saveTransactionData($rate, $ticket);

                            $response_type = '1';
                            $msg['msg'] = "THANK YOU FOR VISITING CHARGED: $" . ($priceBreakUp['payable_amount']) . " #" . $ticket->ticket_number;
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                $msg['is_phone_linked'] = '1';
                                $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                            }
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $msg['ticket_id'] = $ticket->ticket_number;
                            $msg['eticket_id'] = $ticket->ticket_number;
                            $msg['booking_type'] = 'driveup';
                            if ($ticket->reservation_id != '') {
                                $msg['booking_number'] = $ticket->reservation_id != '' ? $ticket->reservation->ticketech_code : '';
                                $msg['booking_start_time'] = date("g:i A", strtotime($ticket->check_in_datetime));
                                $msg['booking_exit_time'] = date("g:i A", strtotime($ticket->checkout_datetime));
                                $msg['booking_entry_time'] = date("g:i A", strtotime($ticket->checkin_time));
                                $msg['booking_type'] = 'reservation';
                            }
                            $msg['amount'] = number_format($priceBreakUp['payable_amount'], 2);
                            $msg['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_time);

                            $length_of_stay = '';
                            $diff_in_days = $startDate->diffInDays($endDate);
                            $diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
                            $diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);

                            if ($diff_in_days > 0) {
                                if ($diff_in_days == 1) {
                                    $length_of_stay .= $diff_in_days . " day ";
                                } else {
                                    $length_of_stay .= $diff_in_days . " days ";
                                }
                            }
                            if ($diff_in_hours > 0) {
                                $length_of_stay .= $diff_in_hours . " hr ";
                            }
                            if ($diff_in_minutes <= 0) {
                                $length_of_stay .= "0 min";
                            }
                            if ($diff_in_minutes > 0) {
                                $length_of_stay .= $diff_in_minutes . " min";
                            }
                            $msg['length_of_stay'] = $length_of_stay;
                            $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

                            if (isset($ticket->user->phone) && $ticket->user->phone != '') {
                                $this->log->info("SMS condition entered");
                                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                                $facility = $ticket->facility;
                                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                    $join->on('user_facilities.user_id', '=', 'users.id');
                                    $join->where('user_facilities.facility_id', "=", $facility->id);
                                })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();

                                if (isset($getRM->slug)) {
                                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                                } else {
                                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                }

                                $url = env('RECEIPT_URL');
                                $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
                                //changes for breeze specific URL from DB
                                if ($dynamicReceiptUrl) {
                                    $url = $dynamicReceiptUrl->value;
                                }
                                $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                                $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                                dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                            }

                            return true;
                        } else if ($ticket->facility->is_lpr_cloud_payment_enabled == '1') {

                            //save temorporary amount in ticket temp table to save in driverup
                            QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
                            $ticket->is_transaction_status = '1';

                            $this->log->info("is_lpr_cloud_payment_enabled entered");
                            if ($ticket->session_id == '') {
                                $ticket->is_transaction_status = '0';
                                $ticket->save();
                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $priceBreakUp['payable_amount'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }


                            $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                            $request->request->add(['total' => $priceBreakUp['payable_amount']]);
                            $datacap['Amount'] = $priceBreakUp['payable_amount'];
                            $datacap['Token'] = $ticket->payment_token;
                            $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                            $datacap["CardHolderID"] = "Allow_V2";
                            $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);

                            $this->log->info("datacap Payment Response --" . json_encode($paymentResponse));

                            if (in_array($paymentResponse['Status'], ['Approved', 'Approval', 'Success'])) {

                                $user_id = $ticket->user_id;
                                $request->request->add(['card_last_four' => $ticket->card_last_four]);
                                $request->request->add(['expiration' => $ticket->expiry]);
                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $ticket->anet_transaction_id = $planetTransaction->id;
                            }
                            if ($paymentResponse["Status"] == "Error") {
                                $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                                if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                    $this->log->info("datacap Payment Error Data -- 1");
                                } else {
                                    $this->log->info("datacap Payment Error Data -- 2");
                                }

                                $ticket->is_transaction_status = '0';
                                $ticket->save();

                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due, Please use different card.";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $priceBreakUp['payable_amount'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            if ($paymentResponse['Status'] == 'Approved') {
                                $user_id = $ticket->user_id;

                                $request->request->add(['card_last_four' => $ticket->card_last_four]);
                                $request->request->add(['expiration' => $ticket->expiry]);

                                $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                                $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                                $ticket->anet_transaction_id = $planetTransaction->id;
                            } else {

                                $ticket->is_transaction_status = '0';
                                $ticket->save();

                                $response_type = '3';
                                $msg = [];
                                $msg['msg'] = "Amount Due, Please use different card.";
                                $msg['is_phone_linked'] = '0';
                                $msg['checkin_time'] = $updatedCheckinTime;
                                $msg['amount'] = $priceBreakUp['payable_amount'];
                                $msg['is_our_ticket'] = $is_our_ticket;
                                $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                                return true;
                            }

                            $ticket->processing_fee  = $priceBreakUp['processing_fee'];
                            $ticket->tax_fee        = $priceBreakUp['tax_rate'];
                            $ticket->parking_amount  = $priceBreakUp['parking_amount'];
                            $ticket->paid_amount     = $priceBreakUp['paid_amount'];
                            $ticket->discount_amount = $priceBreakUp['discount_amount'];
                            $ticket->grand_total     = $priceBreakUp['payable_amount'];
                            $ticket->total     = $priceBreakUp['total'];
                            $ticket->length     = $diff_in_hours;

                            $ticket->is_checkout = '1';
                            $ticket->checkout_gate = $gate->gate;
                            $ticket->checkout_time = $checkoutTime;
                            $ticket->checkout_datetime = $checkoutTime;
                            $ticket->estimated_checkout = $checkoutTime;
                            $ticket->checkout_license_plate = $licensePlate;
                            //$request->transit['plate']['text']
                            $ticket->checkout_session_id = $ticket->session_id;
                            $ticket->checkout_mode = '4';
                            $ticket->is_cloud_payment = '1';
                            $ticket->is_transaction_status = '0';
                            $ticket->rate_id = $rate_id;
                            $ticket->rate_description = $rate_description;
                            $this->log->info(json_encode($ticket));
                            $ticket->save();

                            $this->saveTransactionData($rate, $ticket);

                            $response_type = '1';
                            $msg['msg'] = "THANK YOU FOR VISITING CHARGED: $" . ($priceBreakUp['payable_amount']) . " #" . $ticket->ticket_number;
                            $msg['is_phone_linked'] = '0';
                            $msg['phone_linked_msg'] = '';
                            if (isset($ticket->user->id) && $ticket->user->phone != '') {
                                $msg['is_phone_linked'] = '1';
                                $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                            }
                            $msg['is_check_in_ontime'] = '1';
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $msg['ticket_id'] = $ticket->ticket_number;
                            $msg['booking_type'] = 'driveup';
                            $msg['eticket_id'] = $ticket->ticket_number;
                            if ($ticket->reservation_id != '') {
                                $msg['booking_number'] = $ticket->reservation_id != '' ? $ticket->reservation->ticketech_code : '';
                                $msg['booking_start_time'] = date("g:i A", strtotime($ticket->check_in_datetime));
                                $msg['booking_exit_time'] = date("g:i A", strtotime($ticket->checkout_datetime));
                                $msg['booking_entry_time'] = date("g:i A", strtotime($ticket->checkin_time));
                                $msg['booking_type'] = 'reservation';
                            }
                            $msg['amount'] = number_format($priceBreakUp['payable_amount'], 2);
                            $msg['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
                            $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                            $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_time);

                            $length_of_stay = '';
                            $diff_in_days = $startDate->diffInDays($endDate);
                            $diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
                            $diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);

                            if ($diff_in_days > 0) {
                                if ($diff_in_days == 1) {
                                    $length_of_stay .= $diff_in_days . " day ";
                                } else {
                                    $length_of_stay .= $diff_in_days . " days ";
                                }
                            }
                            if ($diff_in_hours > 0) {
                                $length_of_stay .= $diff_in_hours . " hr ";
                            }
                            if ($diff_in_minutes <= 0) {
                                $length_of_stay .= "0 min";
                            }
                            if ($diff_in_minutes > 0) {
                                $length_of_stay .= $diff_in_minutes . " min";
                            }
                            $msg['length_of_stay'] = $length_of_stay;
                            $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

                            if (isset($ticket->user->phone)) {
                                $this->log->info("SMS condition entered");
                                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                                $facility = $ticket->facility;
                                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                    $join->on('user_facilities.user_id', '=', 'users.id');
                                    $join->where('user_facilities.facility_id', "=", $facility->id);
                                })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                                if (isset($getRM->slug)) {
                                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                                } else {
                                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                                }
                                $url = env('RECEIPT_URL');
                                $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
                                //changes for breeze specific URL from DB
                                if ($dynamicReceiptUrl) {
                                    $url = $dynamicReceiptUrl->value;
                                }
                                $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                                $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                                dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                            }

                            return true;
                        }
                    }
                    //else if not registered
                    else if ($ticket->facility->is_lpr_cloud_payment_enabled == '1') {

                        //save temorporary amount in ticket temp table to save in driverup
                        QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
                        $ticket->is_transaction_status = '1';

                        $this->log->info("is_lpr_cloud_payment_enabled entered");
                        if ($ticket->session_id == '') {
                            $ticket->is_transaction_status = '0';
                            $ticket->save();
                            $response_type = '3';
                            $msg = [];
                            $msg['msg'] = "Amount Due";
                            $msg['is_phone_linked'] = '0';
                            $msg['checkin_time'] = $updatedCheckinTime;
                            $msg['amount'] = $priceBreakUp['payable_amount'];
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }


                        $ecommerce_mid = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $url = $ticket->facility->FacilityPaymentDetails->datacap_script_url;
                        $request->request->add(['total' => $priceBreakUp['payable_amount']]);
                        $datacap['Amount'] = $priceBreakUp['payable_amount'];
                        $datacap['Token'] = $ticket->payment_token;
                        $datacap['ecommerce_mid'] = $ticket->facility->FacilityPaymentDetails->datacap_ecommerce_mid;
                        $datacap["CardHolderID"] = "Allow_V2";
                        $paymentResponse = DatacapPaymentGateway::makePaymentDataCap($datacap, $ecommerce_mid, $url);

                        $this->log->info("datacap Payment Response --" . json_encode($paymentResponse));

                        if ($paymentResponse["Status"] == "Error") {
                            $this->log->info("datacap Payment Error Data --" . json_encode($paymentResponse));
                            if ($paymentResponse["Message"] == "Open Testing Account Number Not Found for this Merchant ID") {
                                $this->log->info("datacap Payment Error Data -- 1");
                            } else {
                                $this->log->info("datacap Payment Error Data -- 2");
                            }

                            $ticket->is_transaction_status = '0';
                            $ticket->save();

                            $response_type = '3';
                            $msg = [];
                            $msg['msg'] = "Amount Due, Please use different card.";
                            $msg['is_phone_linked'] = '0';
                            $msg['checkin_time'] = $updatedCheckinTime;
                            $msg['amount'] = $priceBreakUp['payable_amount'];
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        if ($paymentResponse['Status'] == 'Approved') {
                            $user_id = $ticket->user_id;

                            $request->request->add(['card_last_four' => $ticket->card_last_four]);
                            $request->request->add(['expiration' => $ticket->expiry]);

                            $planetTransaction = DatacapPaymentGateway::saveDatacapTransaction($request, $paymentResponse, $user_id, '');
                            $this->log->info("Payment Transaction Data make Datacap Payment -- " . json_encode($planetTransaction));
                            $ticket->anet_transaction_id = $planetTransaction->id;
                        } else {

                            $ticket->is_transaction_status = '0';
                            $ticket->save();

                            $response_type = '3';
                            $msg = [];
                            $msg['msg'] = "Amount Due, Please use different card.";
                            $msg['is_phone_linked'] = '0';
                            $msg['checkin_time'] = $updatedCheckinTime;
                            $msg['amount'] = $priceBreakUp['payable_amount'];
                            $msg['is_our_ticket'] = $is_our_ticket;
                            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                            return true;
                        }

                        $ticket->processing_fee  = $priceBreakUp['processing_fee'];
                        $ticket->tax_fee        = $priceBreakUp['tax_rate'];
                        $ticket->parking_amount  = $priceBreakUp['parking_amount'];
                        $ticket->paid_amount     = $priceBreakUp['paid_amount'];
                        $ticket->discount_amount = $priceBreakUp['discount_amount'];
                        $ticket->grand_total     = $priceBreakUp['payable_amount'];
                        $ticket->total     = $priceBreakUp['total'];
                        $ticket->length     = $diff_in_hours;

                        $ticket->is_checkout = '1';
                        $ticket->checkout_gate = $gate->gate;
                        $ticket->checkout_time = $checkoutTime;
                        $ticket->checkout_datetime = $checkoutTime;
                        $ticket->estimated_checkout = $checkoutTime;
                        $ticket->checkout_license_plate = $licensePlate;
                        //$request->transit['plate']['text']
                        $ticket->checkout_session_id = $ticket->session_id;
                        $ticket->checkout_mode = '4';
                        $ticket->is_cloud_payment = '1';
                        $ticket->is_transaction_status = '0';
                        $ticket->rate_id = $rate_id;
                        $ticket->rate_description = $rate_description;
                        $this->log->info(json_encode($ticket));
                        $ticket->save();

                        $this->saveTransactionData($rate, $ticket);

                        $response_type = '1';
                        $msg['msg'] = "THANK YOU FOR VISITING CHARGED: $" . ($priceBreakUp['payable_amount']) . " #" . $ticket->ticket_number;
                        $msg['is_phone_linked'] = '0';
                        $msg['phone_linked_msg'] = '';
                        if (isset($ticket->user->id) && $ticket->user->phone != '') {
                            $msg['is_phone_linked'] = '1';
                            $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
                        }
                        $msg['is_check_in_ontime'] = '1';
                        $msg['is_our_ticket'] = $is_our_ticket;
                        $msg['ticket_id'] = $ticket->ticket_number;
                        $msg['eticket_id'] = $ticket->ticket_number;
                        $msg['booking_type'] = 'driveup';
                        if ($ticket->reservation_id != '') {
                            $msg['booking_number'] = $ticket->reservation_id != '' ? $ticket->reservation->ticketech_code : '';
                            $msg['booking_start_time'] = date("g:i A", strtotime($ticket->check_in_datetime));
                            $msg['booking_exit_time'] = date("g:i A", strtotime($ticket->checkout_datetime));
                            $msg['booking_entry_time'] = date("g:i A", strtotime($ticket->checkin_time));
                            $msg['booking_type'] = 'reservation';
                        }
                        $msg['amount'] = number_format($priceBreakUp['payable_amount'], 2);
                        $msg['checkin_time'] = date("g:i A", strtotime($ticket->checkin_time));
                        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkin_time);
                        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $ticket->checkout_time);

                        $length_of_stay = '';
                        $diff_in_days = $startDate->diffInDays($endDate);
                        $diff_in_hours = $startDate->copy()->addDays($diff_in_days)->diffInRealHours($endDate);
                        $diff_in_minutes = $startDate->copy()->addDays($diff_in_days)->addHours($diff_in_hours)->diffInRealMinutes($endDate);

                        if ($diff_in_days > 0) {
                            if ($diff_in_days == 1) {
                                $length_of_stay .= $diff_in_days . " day ";
                            } else {
                                $length_of_stay .= $diff_in_days . " days ";
                            }
                        }
                        if ($diff_in_hours > 0) {
                            $length_of_stay .= $diff_in_hours . " hr ";
                        }
                        if ($diff_in_minutes <= 0) {
                            $length_of_stay .= "0 min";
                        }
                        if ($diff_in_minutes > 0) {
                            $length_of_stay .= $diff_in_minutes . " min";
                        }
                        $msg['length_of_stay'] = $length_of_stay;
                        $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

                        if (isset($ticket->user->phone)) {
                            $this->log->info("SMS condition entered");
                            $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                            if ($partnerDetails->user_id == self::PARTNER_ID) {
                                $facility = $ticket->facility;
                                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                                    $join->on('user_facilities.user_id', '=', 'users.id');
                                    $join->where('user_facilities.facility_id', "=", $facility->id);
                                })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                                $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                            } else {
                                $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                            }
                            $url = env('RECEIPT_URL');
                            $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
                            //changes for breeze specific URL from DB
                            if ($dynamicReceiptUrl) {
                                $url = $dynamicReceiptUrl->value;
                            }
                            $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                            $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                            dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
                        }

                        return true;
                    }
                    //if payment error close

                    QueryBuilder::ticketTemporaryDetails($ticket, $priceBreakUp, $diff_in_hours);
                    $this->log->error("Without payment :");
                    $response_type = '3';
                    $msg = [];
                    $msg['msg'] = "Amount Due";
                    $msg['is_phone_linked'] = '0';
                    $msg['checkin_time'] = $updatedCheckinTime;
                    $msg['amount'] = $priceBreakUp['payable_amount'];
                    $msg['is_our_ticket'] = $is_our_ticket;
                    $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                    return true;
                } else {
                    if ($priceBreakUp['payable_amount'] > 0) {
                        $this->log->info("amount greater log 1");
                        $response_type = '-1';
                        $msg = [];
                        $msg['msg'] = "The parking amount exceeds the permissible amount. Please contact the attendant.";
                        //$msg['is_phone_linked'] = '0';
                        //$msg['checkin_time'] = $updatedCheckinTime;
                        //$msg['amount'] = $priceBreakUp['payable_amount'];
                        //$msg['is_our_ticket'] = $is_our_ticket;
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                        $this->log->info("amount greater log 2");
                        return true;
                    }
                }
            }

            $this->log->info("LPR before checkout");

            if ($ticket->is_checkin == '0' && $ticket->is_checkout == '0') {
                $this->log->info("zeag checkout not done --" . $ticket->ticket_number);

                $ticket->processing_fee  = $priceBreakUp['processing_fee'];
                $ticket->tax_fee        = $priceBreakUp['tax_rate'];
                $ticket->parking_amount  = $priceBreakUp['parking_amount'];
                $ticket->paid_amount     = $priceBreakUp['paid_amount'];
                $ticket->discount_amount = $priceBreakUp['discount_amount'];
                $ticket->grand_total     = $priceBreakUp['payable_amount'];
                $ticket->total     = $priceBreakUp['total'];
                $ticket->length     = $diff_in_hours;

                $ticket->is_checkout = '0';
                $ticket->checkout_gate = $gate->gate;
                $ticket->checkout_time = $checkoutTime;
                $ticket->checkout_datetime = $checkoutTime;
                $ticket->checkout_license_plate = $licensePlate;
                $ticket->checkout_session_id = $ticket->session_id;
                $ticket->checkout_mode = '4';
                $ticket->is_transaction_status = '0';
                $ticket->rate_id = $rate_id;
                $ticket->rate_description = $rate_description;
                $ticket->save();
                $this->log->info("zeag checkout 1--" . $priceBreakUp['payable_amount'] . '--' . $priceBreakUp['payable_amount'] . '--' . $ticket->facility->facilityConfiguration->max_transaction_capping_amount . "--" . $ticket->facility->facilityConfiguration->show_thirdparty_amount_due);
                if ($priceBreakUp['payable_amount'] > 0 && $priceBreakUp['payable_amount'] <= $ticket->facility->facilityConfiguration->max_transaction_capping_amount && $ticket->facility->facilityConfiguration->show_lpr_thirdparty_amount_due == '1') {
                    $response_type = '3';
                    $msg = [];
                    $msg['msg'] = "Amount Due";
                    $msg['is_phone_linked'] = '0';
                    $msg['checkin_time'] = $updatedCheckinTime;
                    $msg['amount'] = $priceBreakUp['payable_amount'];
                    $msg['is_our_ticket'] = $is_our_ticket;
                    $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                }

                /* $msg = [];
                $msg['msg'] = 'Zeag ticket found.';
                $msg['license_plate'] = $request->transit['plate']['text'];
                $this->sendAnonymousCheckinCheckoutInQueue('-2', $msg, $queue_name, $gate_type, $request->transit['plate']['text']);
                */
                return true;
            }

            if ($facility->facilityConfiguration->is_lpr_grace_period_checkout_disabled == '1') {
                return true;
            }
            $ticket->processing_fee     = $priceBreakUp['processing_fee'];
            $ticket->tax_fee            = $priceBreakUp['tax_rate'];
            $ticket->parking_amount     = $priceBreakUp['parking_amount'];
            $ticket->paid_amount        = $priceBreakUp['paid_amount'];
            $ticket->discount_amount    = $priceBreakUp['discount_amount'];
            $ticket->grand_total        = $priceBreakUp['payable_amount'];
            $ticket->total              = $priceBreakUp['total'];
            $ticket->length             = $diff_in_hours;

            $ticket->is_checkout        = '1';
            $ticket->checkout_gate      = $gate->gate;
            $ticket->checkout_time      = $checkoutTime;
            $ticket->checkout_datetime  = $checkoutTime;
            $ticket->estimated_checkout = $checkoutTime;
            // VP:PIMS-14662
            // $ticket->checkout_license_plate = $request->transit['plate']['text']; 
            $ticket->checkout_license_plate     = $licensePlate;
            $ticket->checkout_mode              = '4';
            $ticket->is_transaction_status      = '0';
            $ticket->rate_id                    = $rate_id;
            $ticket->rate_description           = $rate_description;
            $ticket->save();
            $this->log->info("checkout start 32");
            $this->saveTransactionData($rate, $ticket);

            $msg = [];
            $msg['msg'] = "THANK YOU FOR VISITING. #$ticket->ticket_number";

            $msg['is_phone_linked'] = '0';
            $msg['phone_linked_msg'] = '';
            if (isset($ticket->user->id) && $ticket->user->phone != '') {
                $msg['is_phone_linked'] = '1';
                $msg['phone_linked_msg'] = 'Your e-Ticket has been sent on your registered phone number.';
            }
            $msg['booking_type'] = 'reservation';
            $msg['is_check_in_ontime'] = '1';
            $response_type = '1';
            $msg['is_our_ticket'] = $is_our_ticket;
            $msg["print_receipt"] = QueryBuilder::setPrintReceipt($ticket, '0');
            $this->log->info("checkout done {$ticket->ticket_number}");
            $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);

            if (isset($ticket->user->phone)) {
                $this->log->info("SMS condition entered");
                $partnerDetails = UserPaymentGatewayDetail::where('user_id', $ticket->facility->owner_id)->first();
                $facility = $ticket->facility;
                $getRM = User::select('users.id', 'users.slug', 'users.user_type')->join('user_facilities', function ($join) use ($facility) {
                    $join->on('user_facilities.user_id', '=', 'users.id');
                    $join->where('user_facilities.facility_id', "=", $facility->id);
                })->where('created_by', $ticket->facility->owner_id)->whereNotNull('slug')->where('user_type', 12)->first();
                if (isset($getRM->slug)) {
                    $name = ($getRM->slug) ? $getRM->slug : 'receipt';
                } else {
                    $name = isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : 'receipt';
                }
                $url = env('RECEIPT_URL');
                $dynamicReceiptUrl = QueryBuilder::getPartnerConfig("RECEIPT_URL", $facility->owner_id);
                //changes for breeze specific URL from DB
                if ($dynamicReceiptUrl) {
                    $url = $dynamicReceiptUrl->value;
                }
                $facilityName = isset($ticket->facility->full_name) ? ucwords($ticket->facility->full_name) : '';
                $sms_msg = "Thank you for visiting " . $facilityName . ". Please use the following link to download E-Receipt $url/$name/ticket/" . $ticket->ticket_number;

                dispatch((new SendSms($sms_msg, $ticket->user->phone))->onQueue(self::QUEUE_NAME));
            }

            return true;
        } else {
            $this->log->info("Check for License Plate : {$licensePlate}");
            $facility_id = $gate->facility_id;
            $facility = Facility::with(['facilityConfiguration'])->find($facility_id);

            // Start Making change for HYBRID Location : VP:PIMS-14662
            if ($facility->is_gated_facility == '2') {
                // We need to Implement 3 cases here 
                // case 1. customer exit before or till checkout no due and checkout with gated open 
                // case 1. Exit after checkout need to show Amount Due
                // NO checkin ticket found but plate read at exit need to show amount due.

                $hybridTicket = Ticket::with(['facility.FacilityPaymentDetails', 'reservation', 'user'])->where('license_plate', $licensePlate)->where('facility_id', $facility_id)->where('is_checkout', '0')->orderBy("id", "DESC")->first();
                if ($hybridTicket) {
                    // Case 1 & 2  Need to Implement here .
                    // Start Case 1;
                    $this->log->info("CASA 1 for Plate : {$licensePlate} and ticket number : {$hybridTicket->ticket_number} : Estimated : {$hybridTicket->estimated_checkout} and current time " . date('Y-m-d H:i:s'));
                    if ($hybridTicket->estimated_checkout >= Carbon::parse($this->currentTime)->format('Y-m-d H:i:s')) {
                        // Case 1 True Only Need To Open Gate 
                        $this->log->info("CASA 1 for Plate : {$licensePlate} and ticket number : {$hybridTicket->ticket_number}");

                        // Gate Opne Command
                        $this->isParkEngageGateOpen($facility_id, $gate->gate);

                        $hybridTicket->remark = 'CASE 1 Ticket checkout from LRP ';
                        // $hybridTicket->is_checkout  = '1';
                        $hybridTicket->save();
                        $this->log->info("CASA 1 for Plate : {$licensePlate} Gate Opned Succees and remark the ticket");
                    } else if ($hybridTicket->estimated_checkout <=  Carbon::parse($this->currentTime)->format('Y-m-d H:i:s')) {
                        // CASE 2 
                        $this->log->info("CASA 2 for Plate : {$licensePlate} and ticket number : {$hybridTicket->ticket_number}");
                    }
                    $this->log->info("ELSE CASE License Plate : {$licensePlate}");
                } else {
                    $this->log->info("ELSE CASE License Plate 222 : {$licensePlate}");
                    if (isset($request->lpr_id)) {
                        $arrival_time           = $request->lprStartTime;
                        $diff_in_hours          = $request->stayOfLength;
                        $updatedCheckinTime     = $request->nycLprReadTime;
                        $isMember = 0;
                        // $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
                        if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
                            $rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, true, null, false, false, '0', $isMember);
                        } else {
                            $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, true, false, true, false, '0', $isMember);
                        }
                        if (isset($rate['price']) && $rate['price'] === 'N/A') {
                            throw new ApiGenericException("Garage is currently closed.");
                        }
                        // Now calculate Tax and Fees 
                        $parkingAmount      = $rate['price'];
                        $processingFee      = $facility->getProcessingFee('0');       // pass 1 to get the processing fee // pass 1 to get the processing fee
                        $surchargeFee       = $facility->getSurchargeFee($rate);
                        $newRate['price']   = ($rate['price'] + $surchargeFee);
                        $tax_rate           = $facility->getTaxRate($newRate, '0');      // fetch tax from getTicketRate dinamicaly
                        $additionalFee      = $facility->getAdditionalFee($rate);     // Addition Fee Introduce
                        $payableAmount      = ($parkingAmount + $surchargeFee + $tax_rate + $additionalFee);


                        $response_type = '3';
                        $msg = [];
                        $msg['msg']                 = "Amount Due";
                        $msg['is_phone_linked']     = '0';
                        $msg['checkin_time']        = $updatedCheckinTime;
                        $msg['amount']              = $payableAmount;
                        $msg['is_our_ticket']       = 1;
                        $msg['is_overstay']         = '0';
                        $ticket['license_plate']    =    $licensePlate;
                        $ticket['ticket_number']     = 'TEMP-' . $request->lpr_id;
                        // dd($msg, $parkingAmount, $surchargeFee, $tax_rate, $additionalFee);
                        $this->sendCheckinCheckoutInQueue($ticket, $response_type, $msg, $queue_name, $gate_type);
                        return true;
                    } else {
                        $this->log->info("history before checkin 19");
                        $msg = [];
                        $msg['msg'] = 'No checkin found against this license plate.';
                        $msg['license_plate'] = $licensePlate;
                        $this->sendAnonymousCheckinCheckoutInQueue('-2', $msg, $queue_name, $gate_type, $licensePlate);
                        return true;
                    }

                    // Case 3 Need to Implement here .
                }
            }
            // Close HYBRID Change : VP:PIMS-14662

            else {
                // VP:PIMS-14662 Below Code is running for Gated Locations. Added this else for untouched for  gated flow.
                $this->log->info("no checkin found in checkout function");
                $checkinTime = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
                if ($facility->facilityConfiguration->is_check_cloud_permit == '1') {
                    $this->log->info("history before checkin 8");
                    //check permit exist
                    $permit = $this->checkExistingPermit($request->transit['plate']['text'], $gate->facility_id, $gate->partner_id);
                    if (count($permit) > 0) {
                        $ticket = new Ticket();
                        $this->log->info("history before checkin 10");
                        $ticket->permit_request_id = $permit->id;
                        $ticket->user_id = $permit->user_id;
                        $this->log->info("history before checkin 11");
                        $ticket->check_in_datetime = $checkinTime;
                        $ticket->checkin_time = $checkinTime;
                        $ticket->checkout_time = $checkinTime;
                        $ticket->checkout_datetime = $checkinTime;
                        $ticket->estimated_checkout = $checkinTime;
                        $ticket->is_checkin = '1';
                        $ticket->is_checkout = '1';
                        $ticket->facility_id = $gate->facility_id;
                        $ticket->partner_id = $facility->owner_id;
                        $ticketNumber = $this->checkTicketNumber($gate->facility_id);
                        $isExist = Ticket::where('ticket_number', $ticketNumber)->first();
                        if ($isExist) {
                            $ticket->ticket_number = $this->checkTicketNumber($gate->facility_id);
                        } else {
                            $ticket->ticket_number = $ticketNumber;
                        }
                        $ticket->license_plate = $licensePlate != '' ? $licensePlate : $request->transit['plate']['text'];
                        $ticket->checkout_license_plate = $licensePlate != '' ? $licensePlate : $request->transit['plate']['text'];
                        $ticket->checkout_gate = $gate->gate;
                        $ticket->device_type = "LPR";
                        $ticket->checkout_mode = "4";
                        $ticket->checkout_remark = "Ticket is created at exit as License Plate was not recognized at entry.";
                        $ticket->save();
                    }
                }
                $this->log->info("history before checkin 19");
                $msg = [];
                $msg['msg'] = 'No checkin found against this license plate.';
                $msg['license_plate'] = $request->transit['plate']['text'];
                $this->sendAnonymousCheckinCheckoutInQueue('-2', $msg, $queue_name, $gate_type, $request->transit['plate']['text']);
                return true;
            }

            return true;
        }
    }


    public function saveOverstayDetails($rate, $ticket, $facility)
    {
        $this->log->info("saveOverstayDetails 1");
        $rate_id = isset($rate['id']) ? $rate['id'] : '';
        $rate_description = isset($rate['description']) ? $rate['description'] : '';

        $currentTime = Carbon::parse('now');
        $estimated_checkout = Carbon::parse($currentTime)->addMinutes($facility->grace_period_minute)->format('Y-m-d H:i:s');
        $overstay = new OverstayTicket();
        $overstay->user_id = $ticket->user_id;
        $overstay->facility_id = $ticket->facility_id;
        $overstay->ticket_number = $ticket->ticket_number;
        $overstay->is_checkin = '1';
        $overstay->is_checkout = '1';
        $overstay->check_in_datetime = $ticket->estimated_checkout;
        $overstay->checkout_datetime = $estimated_checkout;
        $overstay->estimated_checkout = $estimated_checkout;
        $overstay->partner_id = $ticket->partner_id;
        $overstay->ticket_id = $ticket->id;
        $overstay->payment_date = date("Y-m-d H:i:s");
        $overstay->rate_id = $rate_id;
        $overstay->rate_description = $rate_description;
        $overstay->reservation_id = $ticket->reservation_id;
        $overstay->save();
        $this->log->info("saveOverstayDetails 2");
        return $overstay;
    }



    public function saveLicensePlate($request, $gate)
    {
        $facility = Facility::find($gate->facility_id);
        if ($facility->license_plate_model != '') {
            $NamespacedModel = 'App\\Models\\ParkEngage\\' . $facility->license_plate_model;
            //$licensePlate = $NamespacedModel::where("license_plate", $request->license_plate)->where("gate", $request->gate_id)->delete();
        } else {
            $NamespacedModel = 'App\\Models\\ParkEngage\\' . "LicensePlate";
        }
        $NamespacedModel::where("facility_id", $gate->facility_id)->where("gate", $gate->gate)->delete();
        $license['license_plate'] = $request->transit['plate']['text'];
        $license['partner_id'] =  $gate->partner_id;
        //$license['make'] = $data->Make;
        //$license['model'] = $data->Model;
        $license['facility_id'] =  $gate->facility_id;
        $license['gate'] = $gate->gate;
        $license['gate_type'] = $gate->gate_type;
        $license['entry_time'] = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
        $result = $NamespacedModel::create($license);
        return $result;
    }


    public function checkExistingPermit($plate, $facility_id, $partner_id)
    {
        if ($plate == "8B03096" || $plate == "8BO3O96" || $plate == "8BO3096" || $plate == "8B03O96") {
            $plate = "8B03096";
        }
        $permitVehicles = PermitVehicle::where("license_plate_number", $plate)->where("partner_id", $partner_id)->get();
        if (count($permitVehicles) > 0) {
            foreach ($permitVehicles as $key => $permitVehicle) {
                $permitRequests = PermitRequest::with(['user'])->where('user_id', $permitVehicle->user_id)->where('facility_id', $facility_id)->whereDate('grace_end_date', '>=', date("Y-m-d"))->whereNull("cancelled_at")->orderBy("id", "DESC")->get();
                if (count($permitRequests) > 0) {
                    foreach ($permitRequests as $key => $permitRequest) {

                        $mapping = PermitVehicleMapping::where("permit_vehicle_id", $permitVehicle->id)->where("permit_request_id", $permitRequest->id)->first();
                        if (!$mapping) {
                            continue;
                        }
                        return $permitRequest;
                    }
                } else {
                    return $permitRequests;
                }
            }
        }
        return $permitVehicles;
    }


    public function sendAnonymousCheckinCheckoutInQueue($response_type, $msg, $queue_name, $gate_type, $license_plate)
    {
        $this->log->info("sendAnonymousCheckinCheckoutInQueue queue {$response_type}");
        $msgRespone = [];
        $msgRespone = (object) $msg;
        $this->log->info("Json response  " . json_encode(['data' => $msgRespone, 'response_type' => $response_type]));
        $myArray = json_encode(['eticket_id' => 'KTERROR', 'license_plate' => $license_plate, 'data' => $msgRespone, 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
        if ($queue_name == '') {
            $queue_name = self::QUEUE_ENTRY;
        }
        $queue->pushRaw($myArray, $queue_name);
        $this->log->info("data send " . $queue_name);
        return true;
    }

    public function townsendTattileFeed(Request $request)
    {

        $this->log->info("townsendTattileFeed Request received --" . json_encode($request->all()));
        //dd($request->transit['plate']['text']);
        //start time $request->transit['timestamps']['start']
        $gate = Gate::with('parkingDevice.parkingDeviceRabbitmq')->where("lane_id", $request->transit['lane'])->first();
        if ($gate) {
            $this->setCustomTimezone($gate->facility_id);
            $this->log->info("townsendTattileFeed Request : " . date("Y-m-d H:i:s"));

            $facility = Facility::find($gate->facility_id);

            $this->saveTownsendLicensePlate($request, $gate);

            if ($gate->gate_type == 'entry') {
                $lastTicket = Ticket::where("facility_id", $gate->facility_id)->whereNull("checkout_time")->orderBy("id", "DESC")->first();
                if (isset($lastTicket) && $lastTicket->license_plate == $request->transit['plate']['text'] && $lastTicket->is_checkout == '0') {
                    return "Duplicate Feed";
                }
                $this->saveTownsendCheckin($request, $gate);
                $this->log->info("Checkin Done");
            } elseif ($gate->gate_type == 'exit') {

                if ($facility->is_lpr_enabled == '0' || $facility->is_lpr_enabled == 0) {
                    $this->log->info("townsendTattileFeed lpr is disabled");
                    return true;
                }
                $this->saveTownsendCheckout($request, $gate);
                $this->log->info("Checkout Done");
            } else {
                $this->log->info("Invalid Gate");
            }
        } else {
            $this->log->info("Lane Id not matched");
        }
        return "Feed done";
    }

    public function saveTownsendLicensePlate($request, $gate)
    {
        LicensePlate::where("facility_id", $gate->facility_id)->where("gate", $gate->gate)->delete();
        $license['license_plate'] = $request->transit['plate']['text'];
        $license['partner_id'] =  $gate->partner_id;
        //$license['make'] = $data->Make;
        //$license['model'] = $data->Model;
        $license['facility_id'] =  $gate->facility_id;
        $license['gate'] = $gate->gate;
        $license['gate_type'] = $gate->gate_type;
        $license['entry_time'] = date("Y-m-d H:i:s", strtotime($request->transit['timestamps']['start']));
        $result = LicensePlate::create($license);
        return $result;
    }

    public function saveTownsendCheckin($request, $gate)
    {
        $this->log->info("saveTownsendCheckin before checkin");
        try {
            $queue_name = '';
            $gate_type = '';
            if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
                $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
                $gate_type = $gate->gate_type;
            }

            $this->log->info("gate found");

            $msg['license_plate'] = $request->transit['plate']['text'];
            $msg['is_checkin_or_checkout'] = '1';
            $response_type = 1;
            $this->sendTownsendCheckinCheckoutInQueue($response_type, $msg, $queue_name, $gate_type);
        } catch (\Exception $e) {
            $msg = "Error Message: " . $e->getMessage() . ", File: " . $e->getFile() . ", Line Number: " . $e->getLine();
            $this->log->error("error received --" . $msg);
            Mail::send(
                "parkengage.notify-mq-listner",
                ['is_failed' => '1', 'msg' => $msg],
                function ($message) {
                    $message->to(['<EMAIL>', '<EMAIL>'])->subject("Townsend Tattile Info : Tattile Exception");
                    $message->from(config('parkengage.default_sender_email'));
                }
            );
            return true;
        }
    }


    public function saveTownsendCheckout($request, $gate)
    {

        $this->log->info("saveTownsnendCheckout checkout start");
        $queue_name = '';
        $gate_type = '';
        if (isset($gate->parkingDevice->parkingDeviceRabbitmq->id)) {
            $queue_name = $gate->parkingDevice->parkingDeviceRabbitmq->queue_name;
            $gate_type = $gate->gate_type;
        }
        $this->log->info("saveTownsnendCheckout LPR before checkout");
        $msg = [];
        $msg['license_plate'] = $request->transit['plate']['text'];
        $msg['is_checkin_or_checkout'] = '0';
        $response_type = '1';
        $this->sendTownsendCheckinCheckoutInQueue($response_type, $msg, $queue_name, $gate_type);
        return true;
    }

    public function sendTownsendCheckinCheckoutInQueue($response_type, $msg, $queue_name, $gate_type)
    {
        $this->log->info("townsnend before queue {$response_type}");
        $msgRespone = [];
        $msgRespone = (object) $msg;
        $this->log->info("Json response  " . json_encode(['license_plate' => $msg['license_plate'], 'is_checkin_or_checkout' => $msg['is_checkin_or_checkout'], 'response_type' => $response_type]));
        $myArray = json_encode(['license_plate' => $msg['license_plate'], 'is_checkin_or_checkout' => $msg['is_checkin_or_checkout'], 'response_type' => $response_type]);
        $queueManager = app('queue');
        $queue = $queueManager->connection('rabbitmq');
        if ($queue_name == '') {
            $queue_name = self::QUEUE_ENTRY;
        }
        $queue->pushRaw($myArray, $queue_name);
        $this->log->info("townsend data send " . $queue_name);
        return true;
    }


    public function isOvernightOrNormal($ticket)
    {
        $exitTime = Carbon::parse('now');
        $overnightFallingTime = FacilityOvernightDuration::where(['rate_category_id' => config('parkengage.overnight_cat_id'), 'active' => '1', 'partner_id' => $ticket->facility->owner_id])->first();
        if ($overnightFallingTime) {
            $OverStratTime = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnigth_start_time);
            $OverEndTime = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnight_end_time);
            $entryExitBetweenOvernight =  $exitTime->between($OverStratTime, $OverEndTime);
            if ($ticket->payment_date == '') {
                return false;
            }
            $ticketPaymentDate = Carbon::parse($ticket->payment_date);
            $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
            $OverstayTicket = OverstayTicket::where(['ticket_id' => $ticket->id])->orderBy('id', 'DESC')->first();
            $this->log->info("exitIslowerToEndTime exitIslowerToEndTime {$entryExitBetweenOvernight}");
            if ($OverstayTicket) {
                //  either Reservation or Normal Drive up.
                $ticketPaymentDate = Carbon::parse($OverstayTicket->payment_date);
                $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
                if ($entryExitBetweenOvernight && $paymentStatus) {
                    $this->log->info("isOvernightOrNormal Overstay 111 ");
                    return true;
                } else {
                    $this->log->info("isOvernightOrNormal Overstay 22 ");
                    return false;
                }
            } else {
                if ($ticket->reservation_id != '') {
                    if ($ticket->reservation->thirdparty_integration_id) { // third party pay first pay to come in overstay
                        // because fisrt need to pay
                        return false;
                    }
                    // checkin against reservation 
                    if ($entryExitBetweenOvernight && $paymentStatus) {
                        $this->log->info("return exitIslowerToEndTime exitIslowerToEndTime isOvernightOrNormal 111 ");
                        return true;
                    } else {
                        $this->log->info("return exitIslowerToEndTime exitIslowerToEndTime isOvernightOrNormal 22 ");
                        return false;
                    }
                } else {
                    // drive up
                    if ($entryExitBetweenOvernight && $paymentStatus) {
                        $this->log->info("return exitIslowerToEndTime exitIslowerToEndTime isOvernightOrNormal 111 ");
                        return true;
                    } else {
                        $this->log->info("return exitIslowerToEndTime exitIslowerToEndTime isOvernightOrNormal 22 ");
                        return false;
                    }
                }
            }
        }
        return false;
    }

    protected function timestampToCarbon(Carbon $initial, $time)
    {
        $times = explode(':', $time);

        $hour = $times[0] ?? 0;
        $minute = $times[1] ?? 0;
        $second = $times[2] ?? 0;

        return $initial->copy()->hour($hour)->minute($minute)->second($second);
    }


    //Save Transaction Data for Report
    protected function saveTransactionData($rate, $mainTicket)
    {
        $transactionExist = TransactionData::where("ticket_id", $mainTicket->id)->first();
        if ($transactionExist) {
            return $transactionExist;
        }
        $this->log->info("Save transaction " . json_encode($rate));
        $rate_id = isset($rate['id']) ? $rate['id'] : '109';
        $rate_description = isset($rate['description']) ? $rate['description'] : 'Daily Max';
        $rate_amount = $rate['price'];
        $tax_rate = $mainTicket->tax_rate == '' ? 0.00 : $mainTicket->tax_rate;
        $processing_fee = $mainTicket->processing_fee == '' ? 0.00 : $mainTicket->processing_fee;
        $total = $rate_amount + $tax_rate + $processing_fee;
        $ticket = new TransactionData();

        $ticket->user_id = $mainTicket->user_id;
        $ticket->facility_id = $mainTicket->facility_id;
        $ticket->partner_id = $mainTicket->partner_id;
        $ticket->ticket_id = $mainTicket->id;
        $ticket->rate_id = $rate_id;
        $ticket->rate_description = $rate_description;
        $ticket->rate_amount = $rate['price'];
        $ticket->total = $total;
        $ticket->tax_fee = $tax_rate;
        $ticket->processing_fee = $processing_fee;
        $ticket->discount_amount = $mainTicket->paid_amount;
        $ticket->grand_total = $mainTicket->grand_total;
        $ticket->save();
        return $ticket;
    }


    //Save Transaction Data for Report
    protected function saveOverstayTransactionData($ticket, $overstayTicket, $facility)
    {
        $this->log->info("saveOverstayTransactionData 2");
        $arrival_time = $ticket->checkin_time;
        $diff_in_hours = $ticket->length;
        $isMember = 0;
        if ($facility->is_hourly_rate == '1' || $facility->is_hourly_rate == 1) {
            $rate = $facility->rateForReservationByPassRateEngine($arrival_time, $diff_in_hours, false, false, null, false, false, '0', $isMember);
        } else {
            $rate = $facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, $isMember);
        }

        $rate_id = isset($rate['id']) ? $rate['id'] : '';
        $rate_description = isset($rate['description']) ? $rate['description'] : '';
        $rate_amount = $rate['price'];

        $transaction = TransactionData::where("ticket_id", $ticket->id)->first();
        if (!$transaction) {
            return true;
        }

        $transaction->rate_id = $rate_id;
        $transaction->rate_description = $rate_description;
        $transaction->rate_amount = $rate['price'];
        $transaction->total = $ticket->total + $overstayTicket->total;
        $transaction->grand_total = $ticket->grand_total + $overstayTicket->grand_total;
        $transaction->overstay_ticket_id = $overstayTicket->id;
        $transaction->save();
        $this->log->info("saveOverstayTransactionData 3");
        return $transaction;
    }

    public function isParkEngageGateOpen($facility_id, $gate, $phone = '')
    {
        $facility = Facility::where('id', $facility_id)->first();
        $gate = Gate::where('gate', $gate)->where('facility_id', $facility_id)->first();

        //check third party gate API
        if ($facility->adam_host != '') {
            $params = ['gate_id' => $gate->gate];
            //$this->log->info("Request Parkengage Gate Service is Vehicle Available : vehicle command aboout to send facility {$facility->id} params " . json_encode($params));
            $cmd_params = ['gate_id' => $gate->gate];
            //$this->log->info("Request Parkengage Gate Service Gate Open : gate open command about to run facility {$facility->id} params" . json_encode($cmd_params));
            if ($gate->is_external_gate == '1' || $gate->is_external_gate == 1) {
                $command_response = ParkengageGateApi::openExternalGate($cmd_params, $facility->adam_host);
            } else {
                $command_response = ParkengageGateApi::openGate($cmd_params, $facility->adam_host);
            }

            //$this->log->info("Response Parkengage Gate Service Gate Open : gate open command complete facility {$facility->id} response" . json_encode($command_response));
            if ($command_response['success'] == true) {
                if ($command_response['data'][0] == "true") {
                    return true;
                } else {
                    $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                    return $msg;
                }
            } else {
                $msg = "Seems some issue with the " . ucwords($gate->gate_type) . " gate. Please contact the attendant.";
                return $msg;
            }
        }
    }
}
