<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PermitThirdParkchip extends Model
{
    // Define the table name explicitly (<PERSON><PERSON> assumes pluralized table name)
    protected $table = 'third_party_parkchirp';

    // Define the columns you want to allow mass assignment for
    protected $fillable = [
        'facilityId', 
        'sublotId', 
        'parkerId', 
        'parkerFirstName',
        'parkerLastName',
        'parkerFullName',
        'createdAt', 
        'startDate',
        'endDate', 
        'subscriptionId', 
        'subscriptionName', 
        'accountId', 
        'prox_card_number',
        'vehicles',
        'parkerEmail',
        'parkerPhone', 
        'failed_type',
        'failed_description'
    ];

    public function vehicleList()
    {
        return $this->hasMany(PermitThirdParkchipVehicle::class, 'permit_third_parkchip_id');
        // explicitly define foreign key, since table name and field may not follow convention
    }
}


