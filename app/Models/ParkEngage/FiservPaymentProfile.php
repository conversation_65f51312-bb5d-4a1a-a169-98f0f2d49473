<?php

namespace App\Models\ParkEngage;

use DB;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class FiservPaymentProfile extends Model
{

  protected $table = 'fiserv_payment_profile';
  use SoftDeletes;
  /**
   * Custom primary key is set for the table
   * 
   * @var integer
   */
  protected $primaryKey = 'id';

  /**
   * Maintain created_at and updated_at automatically
   * 
   * @var boolean
   */
  public $timestamps = true;

  /**
   * The attributes that are mass assignable.
   * @var array
   */
  protected $fillable = [];
  /**
   * The attributes that are mass assignable.
   *
   * @var array
   */
  protected $guarded = [];

  public static $validateHLApplePayRequest = [
    'merchantIdentifier'  => 'required',
    'validationURL'       => 'required',
    'displayName'         => 'required',
    'initiative'          => 'required',
    'initiativeContext'   => 'required',
    'facility_id'         => 'required',
  ];
}