<?php

namespace App\Models\ParkEngage;

use DB;
use App\Classes\InlineViewCss;
use Illuminate\Database\Eloquent\Model;
use App\Services\Image;
use App\Classes\AuthorizeNet\Cim;
use App\Models\User;
use Auth;
use App\Models\ExpirationDate;
use App\Exceptions\NotFoundException;
use App\Exceptions\ApiGenericException;
use App\Models\ParkEngage\UserPaymentGatewayDetail;

class MembershipPayment extends Model
{

    protected $table = 'membership_payments';

    /**
     * Custom primary key is set for the table
     * 
     * @var integer
     */
    protected $primaryKey = 'id';

    /**
     * Maintain created_at and updated_at automatically
     * 
     * @var boolean
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    const TEMPLATE_INVOICE = 'invoices.main';
    const TEMPLATE_INVOICE_IMAGE = 'invoices.main_image';

    protected $user;

    public static $searchFields = [
        'total'
    ];

    public function membershipPlan()
    {
        return $this->belongsTo('App\Models\ParkEngage\MembershipPlan');
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id');
    }

    public function service()
    {
        return $this->belongsTo('App\Models\ParkEngage\Service');
    }

    public function userMembership()
    {
        return $this->belongsTo('App\Models\ParkEngage\UserMembership', 'user_membership_id');
    }

    public function anetTransaction()
    {
        return $this->belongsTo('App\Models\AuthorizeNetTransaction', 'anet_transaction_id');
    }

    public function generateInvoice($membershipPayment, $format = Image::class)
    {
        $data = ['membershipPayment' => $membershipPayment];
        if ($format == Image::class) {
            $html = view(self::TEMPLATE_INVOICE_IMAGE, $data)->render();
        } else {
            $html = view(self::TEMPLATE_INVOICE, $data)->render();
        }
        
        $image = app()->make($format);
        return $image->getOutputFromHtmlString($html);
    }
    
    public function addCreditCard($number, $expiration, $security_code, $name, $zip_code = '')
    {
        $user = User::where('id', Auth::user()->id)->first();
        $this->user = $user;
        if ($this->user->user_type == '3') {
            $partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
        } else {
            $partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
        }
        $cim = new Cim();
        $cim->setUser($user)
            ->setBillingAddress($this->getNameAndZip($name, $zip_code))
            ->isPartner($partnerPaymentDetails)
            ->setCreditCard($number, $expiration, $security_code);
        // Run through a test transaction to verify the credit card
        $cim->validateCard("Authorization test for {$user->email}", $partnerPaymentDetails);

        try {
            $paymentProfileId = $this->sendAddPaymentMethodRequest($cim, $partnerPaymentDetails);
        } catch (\Exception $e) {
            if ($e->getMessage() == "Response : E00042  You cannot add more than 10 payment profiles.\n") {
                throw new ApiGenericException("Maximum 10 cards can be added.");
            }
            throw new ApiGenericException($e->getMessage());
        }

        if (!$paymentProfileId) {
            throw new ApiGenericException("Sorry, There is some issue while adding new payment method.");
        }


        $month = (strlen($expiration) === 4) ? substr($expiration, 0, 2) : substr($expiration, 0, 1);
        $year = substr($expiration, -2);

        // Corresponds to format on cim->getPaymentProfiles() so that it can be
        // thrown back into redux state
        return [
            'address' => $this->getName($name),
            'payment_profile_id' => $paymentProfileId['payment_profile_id'],
            'card' => [
                'card_number' => 'XXXX' . substr((string) $number, -4),
                'card_expiration' => $expiration
            ],
            'expiration_month' => $month,
            'expiration_year' => $year
        ];
    }

    public function deleteUserPaymentProfiles($paymentProfileId)
    {
        // Delete the profile - handles deleting in auth net and locally
        $user = User::where('id', Auth::user()->id)->first();
        $this->user = $user;

        $partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
        
        $cim = new Cim();
        $cim->setUser($user)->isPartner($partnerPaymentDetails)->deleteCustomerPaymentProfile($paymentProfileId);

        return $paymentProfileId;
    }
    
    public function ownsPaymentProfile($paymentProfile)
    {
       
        $user = User::where('id', Auth::user()->id)->first();
        $this->user = $user;
        if (!$this->user->cim) {
            return false;
        }

        $profiles = $this->user->cim->paymentProfiles;

        // laravel collection method https://laravel.com/docs/5.1/collections#method-search
        $result = $profiles->search(
            function ($profile) use ($paymentProfile) {
                return (int) $profile->payment_profile === (int) $paymentProfile;
            }
        );

        return $result !== false;
    }
    
    public function getPaymentMethods()
    {
        $user = User::where('id', Auth::user()->id)->first();
        $this->user = $user;
        if ($this->user->user_type == '3') {
            $partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->id)->first();
        } else {
            $partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $this->user->created_by)->first();
        }
        
        $authorizeNet = new Cim();
        $response = $authorizeNet->setUser($user)->isPartner($partnerPaymentDetails)->getCustomerProfile();

        if (!$response['profile_id']) {
           // throw new NotFoundException('No customer profile exists for this monthly parking user.');
            throw new NotFoundException('There are currently no saved payment methods.');
        }

        // Add our stored expiration dates if available
        $response['payments'] = array_map(
            function ($payment) {
                if (!$payment['card']) {
                    return $payment;
                }

                $expirationDate = ExpirationDate::whereHas(
                    'paymentProfile', function ($query) use ($payment) {
                        $query->where('payment_profile', $payment['payment_profile_id']);
                    }
                )->first();
                if (!$expirationDate) {
                    return $payment;
                }

                $payment['expiration_month'] = $expirationDate->expiration_month;
                $payment['expiration_year'] = $expirationDate->expiration_year;
                return $payment;
            }, $response['payments']
        );

        return $response;
    }


    public function addBankAccount($accountType, $routing, $account, $name)
    {
//        $cim = new Cim();
//        $cim->setMonthlyParkingUser($this)
//            ->setBillingAddress($this->getName($name))
//            ->setBankAccount($accountType, $routing, $account, $name);
//
//        $paymentProfileId = $this->sendAddPaymentMethodRequest($cim);
//
//        // Corresponds to format on cim->getPaymentProfiles() so that it can be
//        // thrown back into redux state
//        return [
//            'payment_profile_id' => $paymentProfileId['payment_profile_id'],
//            'bank' => [
//                'bank_routing' => 'XXXX' . substr((string) $routing, -4),
//                'bank_account' => 'XXXX' . substr((string) $account, -4),
//                'bank_type' => $accountType,
//                'bank_name' => $name
//            ],
//            'address' => $this->getName($name)
//        ];
    }
    
    public function getNameAndZip($name, $zip_code)
    {
        $splitName = explode(' ', $name);

        return [
            'first_name' => array_shift($splitName),
            'last_name' => count($splitName) ? implode(' ', $splitName) : '',
            'zip' => $zip_code,
        ];
    }

    public function getName($name)
    {
        $splitName = explode(' ', $name);

        return [
            'first_name' => array_shift($splitName),
            'last_name' => count($splitName) ? implode(' ', $splitName) : ''
        ];
    }
    
     /**
     * Send the request to create a new payment method
     *
     * @param  $cim A prepared AuthorizeNet Cim object
     * @return array $response
     */
    protected function sendAddPaymentMethodRequest($cim, $partnerPaymentDetails)
    {
        if (!$this->user->cim) { // This creates our customer profile and the payment profile in one go
            $response = $cim->createCustomerProfile()->isPartner($partnerPaymentDetails)->executeCustomerProfileRequestAddNewMethod($partnerPaymentDetails);
        } else { // Or just create a new payment profile
            $response = $cim->createPaymentProfile()->isPartner($partnerPaymentDetails)->executePaymentProfileRequestAddNewMethod();
        }

        return $response;  // Response will have payment_profile_id
    }
}
