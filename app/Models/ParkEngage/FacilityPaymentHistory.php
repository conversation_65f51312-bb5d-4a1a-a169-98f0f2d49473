<?php

namespace App\Models\ParkEngage;

Use DB;
//use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use App\Models\Facility;

class FacilityPaymentHistory extends Model {

    protected $table = 'facility_payment_history';
  //   use SoftDeletes;
    /**
     * Custom primary key is set for the table
     * 
     * @var integer
     */
    protected $primaryKey = 'id';

    /**
     * Maintain created_at and updated_at automatically
     * 
     * @var boolean
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [];
   /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $appends = ['facility_name'];

     /**
     * storeData
     * @param 
     * @return array
     * @since 0.1
     * <AUTHOR>
     */

    public function createHistory($data)
    {
        $keyMap = [
            'datacap_mid' => 'mid',
            'fiserv_mid' => 'mid',
            'heartland_mid' => 'mid',
            'planet_merchant_id' => 'mid',
            'planet_refund_url' => 'refund_url',
            'datacap_refund_url' => 'refund_url',
            'heartland_payment_env' => 'payment_env',
            'datacap_payment_env' => 'payment_env',
            'planet_payment_env' => 'payment_env',
        ];

        $historyData = $this->renameArrayKeys($data,$keyMap);
        FacilityPaymentHistory::create($historyData);
    }
    function renameArrayKeys(array $array, array $keyMap): array {
        foreach ($keyMap as $oldKey => $newKey) {
            if (array_key_exists($oldKey, $array)) {
                if (!isset($array[$newKey])) {
                    $array[$newKey] = $array[$oldKey];
                }
                unset($array[$oldKey]);
            }
        }
        return $array;
    }


    public function facilityPaymentType()
    {
        return $this->belongsTo('App\Models\ParkEngage\FacilityPaymentType','facility_payment_type_id');
    }

    public function getFacilityNameAttribute()
    {
        $facility = Facility::query()
            ->where('id', $this->facility_id)
            ->select('id','short_name','full_name','garage_code')
            ->first();
        $data = [];
	  	  if($facility){
            return $facility->full_name;
          }else {
            return null;
          }        
    }


}
