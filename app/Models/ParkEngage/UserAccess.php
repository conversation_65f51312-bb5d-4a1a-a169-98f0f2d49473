<?php

namespace App\Models\ParkEngage;

use Illuminate\Database\Eloquent\Model;
use App\Exceptions\AuthorizeNetException;

class UserAccess extends Model {

    protected $table = 'user_access';

    /**
     * Custom primary key is set for the table
     * 
     * @var integer
     */
    protected $primaryKey = 'id';

    /**
     * Maintain created_at and updated_at automatically
     * 
     * @var boolean
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $appends = [];


    public function users()
    {
        return $this->belongsTo('App\Models\User', 'user_id');
    }    
   
}
