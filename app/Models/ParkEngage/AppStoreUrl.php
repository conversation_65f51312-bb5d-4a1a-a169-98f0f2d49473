<?php

namespace App\Models\ParkEngage;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AppStoreUrl extends Model
{
    use SoftDeletes;

    protected $table = 'app_store_urls';

    protected $fillable = [
        'partner_id',
        'store_url',
        'app_store_provider_id',
        'title',
    ];

    public function provider()
    {
        return $this->belongsTo(AppStoreProvider::class, 'app_store_provider_id');
    }
}
