<?php

namespace App\Models\ParkEngage;

use App\Models\Ticket;
use App\Models\ParkEngage\LPRFeed;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LprTicketMapping extends Model
{
    use SoftDeletes;

    protected $table = 'lpr_ticket_mappings';

    protected $fillable = [
        'ticket_id',
        'lpr_feed_id',
    ];

    public function ticket()
    {
        return $this->belongsTo('App\Ticket', 'ticket_id');
    }

    public function lprFeed()
    {
        return $this->belongsTo('App\LprFeed', 'lpr_feed_id');
    }
}
