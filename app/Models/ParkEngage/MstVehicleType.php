<?php
namespace App\Models\ParkEngage;

use Illuminate\Database\Eloquent\Model;

class MstVehicleType extends Model
{
    public $table = 'mst_vehicle_types';

    protected $primaryKey = 'id';

    /**
     * Maintain created_at and updated_at automatically
     * 
     * @var boolean
     */
    public $timestamps = false;
    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [];
   /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
     
    
    public static function getVehicleType($name , $partner_id)
    {
      $mstVehicleType   =  MstVehicleType::where('name',  $name)->where("partner_id", $partner_id)->orderBy('name', 'ASC')->first();
      if(!$mstVehicleType){ 
          $mstVehicleType   =  MstVehicleType::where('name', $name)->whereNull("partner_id")->orderBy('name', 'ASC')->first();    
      }
      return $mstVehicleType;
    }
}
