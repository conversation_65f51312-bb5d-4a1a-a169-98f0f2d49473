<?php

/* 
  *  Copyright (c) 2024 ParkEngage Inc. All rights reserved.
  *  
  *  This software is the confidential and proprietary information of
  *  ParkEngage Inc. ("Confidential Information"). You shall not
  *  disclose such Confidential Information and shall use it only in
  *  accordance with the terms of the license agreement you entered into
  *  with ParkEngage Inc.
  *  
  *  This source code is a valuable trade secret of ParkEngage Inc.
  *  
  *  THIS SOFTWARE IS PROVIDED BY ParkEngage Inc. ``AS IS'' AND ANY
  *  EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  *  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  *  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL [YOUR COMPANY NAME] OR
  *  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  *  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  *  PROCUREMENT OF <PERSON><PERSON><PERSON><PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  *  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
  *  OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
  *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  *  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *  
  *  The views and conclusions contained in the software and documentation
  *  are those of the authors and should not be interpreted as representing
  *  *official policies, either expressed or implied, of ParkEngage Inc.
*/

namespace App\Models\ParkEngage;

use App\LprTicketMapping;
use App\Models\Facility;
use App\Models\Ticket;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class LPRFeed extends Model
{
    use SoftDeletes;


    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    // public $timestamps = false;
    const CAMERA_TYPE = '2'; // Vanguard take a reference from master table: Cameras

    protected $table = 'lpr_feeds';

    protected $guarded = [];

    public static $validParams = [
        'license_plate' => 'required'
    ];

    /**
     * Create a new RevpassLicensePlate record from the API JSON.
     */
    public static function fromApiPayload(array $payload): self
    {
        $data = $payload['data'];

        if (isset($data['location']['external_id'])) {
            $factility =  Facility::select('id', 'owner_id')->where('garage_code', $data['location']['external_id'])->first();
            if ($factility) {
                $factility_id = $factility->id;
                $partner_id =  $factility->owner_id;
            } else {
                $factility_id = null;
                $partner_id = null;
            }
        }

        return self::create([

            // Event Data
            'event_id' => $data['event']['id'],
            'event_type' => $payload['event_type'],
            'api_version' => $payload['api_version'],
            'event_created_at' => Carbon::parse($payload['created_at']),
            'event_timestamp' => Carbon::parse($data['event']['timestamp']),

            // Camera Data
            'camera_id' => $data['camera']['id'],
            'camera_name' => $data['camera']['name'],
            'camera_direction' => $data['camera']['direction'],
            'camera_type' => self::CAMERA_TYPE,

            // Vehicle Data
            'license_plate' => $data['vehicle']['plate']['lpn'],
            'state' => $data['vehicle']['plate']['state'],
            'make' => $data['vehicle']['details']['make'],
            'model' => $data['vehicle']['details']['model'],
            'vehicle_type' => $data['vehicle']['details']['type'],
            'color' => $data['vehicle']['details']['color'],
            'vehicle_orientation' => $data['vehicle']['details']['orientation'],
            'confidence' => floatval($data['vehicle']['details']['confidence']),
            'plate_image_url' => $data['vehicle']['images']['plate'],
            'vehicle_image_url' => $data['vehicle']['images']['vehicle'],


            // Facility Data
            'location_external_id' => $data['location']['external_id'],
            'location_internal_id' => $data['location']['internal_id'],
            'facility_id' => $factility_id,
            'partner_id' => $partner_id,

            // Location Data
            // 'location_external_id' => $data['location']['internal_id'],
            // 'location_internal_id' => $data['location']['internal_id'],

            // General Data
            'api_version' => $payload['api_version'],
        ]);
    }

    
    public function tickkets() {
        return $this->hasOne('App\Models\Ticket', 'license_plate','license_plate');
    }
     public function permitVechile() {
        return $this->hasOne('App\Models\PermitVehicle', 'license_plate_number','license_plate');
    }
}
