<?php

namespace App\Models;

use App\Exceptions\ApiGenericException;
use App\Http\Helpers\QueryBuilder;
use Authorizer;
use Illuminate\Support\Facades\Hash;
use Validator;
use Config;
use Carbon\Carbon;
use Zizaco\Entrust\Traits\EntrustUserTrait;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Http\Request;

use Illuminate\Database\Eloquent\SoftDeletes;

use App\Services\Mailers\UserMailer;

use App\Models\Traits\HasPhoto;
use App\Models\PermitRequest;
use App\Models\ParkEngage\Vehicle;
use App\Models\Ticket;
use App\Models\Facility;
use App\Models\UserHistory;
use App\Models\ParkEngage\BrandSetting;
use App\Models\OauthClient;
use App\Models\ParkEngage\AppStoreUrl;
use App\Models\ParkEngage\ServiceMaster;
use App\Models\ParkEngage\UserFacility;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\PermitVehicle;
use App\Models\PermitVehicleMapping;
use App\Models\ParkEngage\UserPermitTypeMapping;
use App\Models\ParkEngage\UserPermitBundleMapping;
use App\Models\ParkEngage\UserPermitServiceMapping;


class User extends Authenticatable
{
    use EntrustUserTrait, SoftDeletes {
        SoftDeletes::restore as sfRestore;
        EntrustUserTrait::restore as euRestore;
    }

    use HasPhoto;

    const IMAGE_FOLDER = 'users';

    const DEFAULT_SOCIAL_PASSWORD = '3tV&Lh+^zE}sIcon';

    const CMS_ACCESS_ROLES = [Role::ADMIN, Role::CUSTOMER_SERVICE, Role::ACCOUNTS_RECEIVABLE, Role::MONTHLY_SALES, Role::PARTNER, Role::SUBPARTNER, Role::BUSINESSCLERK, Role::BUSINESSUSER, Role::CASHIERUSER, Role::REGIONAL_MANAGER];

    public $table = 'users';

    const USER_TYPES = [
        'super_admin' => '1',
        'admin' => '2',
        'partner' => '3',
        'partner_user' => '4',
        'regional_manager' => '12',
    ];
    const USERTYPE_CLERK = 8;
    const USERTYPE_BUSINESS = 10;
    const USERTYPE_CASHIER = 11;
    const USERTYPE_REGIONAL_MANAGER = 12;

    //  protected $fillable = ['name', 'email', 'phone', 'anon', 'referral_code', 'referred_by', 'has_reserved','social_id','social_type','photo_url', 'is_partner','pincode', 'dob','address','user_type','created_by','type_id','spelman_user_id','session_id'];
    protected $fillable = ['name', 'email', 'phone', 'password', 'anon', 'referral_code', 'referred_by', 'has_reserved', 'social_id', 'social_type', 'photo_url', 'is_partner', 'pincode', 'dob', 'address', 'address2', 'state', 'city', 'user_type', 'created_by', 'type_id', 'spelman_user_id', 'session_id', 'license_number', 'qr_code_number', 'business_id', 'status', 'signature', 'user_parent_id', 'user_consent', 'attendent_type', 'country', 'company_name'];
    protected $hidden = ['password', 'remember_token', 'confirmation_code', 'pivot'];

    //protected $appends = ['isAdmin', 'account_numbers', 'isMpSales', 'roles', 'hasCmsAccess', 'show_vehicle_requests', 'monthly_parking_facilities'];
    // operation_mode  - add for event app requirement
    protected $appends = ['isAdmin', 'isMpSales', 'roles', 'hasCmsAccess', 'last_name', 'first_name', 'phone_number', 'resident_status', 'profileImageUrl', 'userVehicleCount', 'signatureurl', 'partner_name', 'clerkFacility', 'partner_slug', 'regional_manager', 'operation_mode', 'user_capping'];

    protected $casts = ['is_loyalty' => 'integer', 'is_loyalty_profile_completed' => 'integer', 'is_loyalty_active' => 'integer', 'is_monthly_ir' => 'integer'];

    protected $dates = ['deleted_at'];
    public static $validationRules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email',
        'password' => 'required|string|min:6|max:255',
        //'phone' => 'phone:US'
    ];

    public static $validationSocialLogin = [
        'name' => 'required|string|max:255',
        'email' => 'email',
        'social_id' => 'required',
        'phone' => 'phone:US',
        'social_type' =>  'required'
    ];

    public static $validationUpdateProfile = [
        'email' => 'email|unique:users',

    ];

    public static $validationUpdateProfileMessage = [
        'unique' => 'This E-mail address is already registered, please try with another E-mail.',

    ];

    public static $validationSocialEmailCheck = [
        'social_id' => 'required',
        'social_type' =>  'required'
    ];

    public static $validateUserDevice = [
        'user_id' => 'required',
        'device_token' => 'required',
        'device_key' => 'required',
        'device_type' => 'required'
    ];

    public static $validateUserFavourites = [
        'user_id' => 'required',
        'facility_id' => 'required',
        'is_favourite' => 'required'
    ];

    public static $validateUserId = [
        'user_id' => 'required'
    ];

    public static $validateGetUserFavourites = [
        'user_id' => 'required',
        'arrival_time' => 'required',
        'length_of_stay' => 'required'
    ];

    public static $validateParkEngagePartner = [
        'first_name' => 'required',
        'last_name' => 'required',
        'email' => 'email|unique:users',
        'phone' => 'required',
        'company_name' => 'required',
        'city' => 'required',
        'country' => 'required',
        'membership_plan_id' => 'required|exists:membership_plans,id'
    ];

    public static $validateParkEngageMultiplePayment = [
        'first_name' => 'required',
        'last_name' => 'required',
        'email' => 'email|unique:users',
        'phone' => 'required',
        'company_name' => 'required',
        'city' => 'required',
        'country' => 'required'
    ];


    public static $validateParkEngageUser = [
        'first_name' => 'required',
        'last_name' => 'required',
        //'email' => 'email|unique:users',
        'email' => 'email|required',
        'phone' => 'required',
        'company_name' => 'required_without:partner_id', //if partner id is null then company_name is required
        'city' => 'required',
        'country' => 'required',
    ];

    public static $validateParkEngageExistingUser = [
        'first_name' => 'required',
        'last_name' => 'required',
        'email' => 'email',
        'phone' => 'required',
        'company_name' => 'required',
        'city' => 'required',
        'country' => 'required',
    ];

    public static $validatePasswordResetRequest = [
        'token' => 'required|exists:users,temp_password_reset_token',
        'password' => 'required|min:5|confirmed'
    ];

    public static $validatePassword = [
        'password' => 'required|min:7|confirmed'
    ];

    public static $validateUserPassword = [
        'password' => 'required|min:7',
    ];
    /**
     * Fields that should be searched on when a query comes in
     * with a search param
     *
     * @var array
     */
    public static $searchFields = [
        'name',
        'email',
        'phone',
        'company_name'
    ];

    /**
     * Register model events
     *
     * @return [type] [description]
     */
    protected static function boot()
    {
        parent::boot();

        // Make sure that users have a confirmation code set when they are first created
        static::creating(
            function ($user) {
                $user->generateEmailConfirmationCode();
            }
        );
    }

    // Provides a soft delete restore method, because of the collision with EntrustUser
    public function restore()
    {
        return $this->sfRestore();
    }

    /**
     * Whether or not this user is an admin
     *
     * @return [type] [description]
     */
    public function getIsAdminAttribute()
    {
        return $this->hasRole('admin');
    }

    public function getPhoneNumberAttribute($value)
    {
        return substr($this->phone, -10);
    }
    /**
     * Whether or not this user is an admin
     *
     * @return [type] [description]
     */
    public function getHasCmsAccessAttribute()
    {
        return $this->hasRole(self::CMS_ACCESS_ROLES);
    }

    /**
     * Whether or not this user is an admin
     *
     * @return [type] [description]
     */
    public function getRolesAttribute()
    {
        return $this->roles()->pluck('name');
    }

    /**
     * Whether or not this user is an mp-sales
     *
     * @return [type] [description]
     */
    public function getIsMpSalesAttribute()
    {
        return $this->hasRole('mp_sales');
    }

    /**
     * Returns monthly parking account numbers that belong to this user
     *
     * @return [type] [description]
     */
    public function getAccountNumbersAttribute()
    {
        return MonthlyParkingUser::query()
            ->where('user_id', $this->id)
            ->has('facility')
            ->with('facility')
            ->get()
            ->sort(
                function ($one, $two) {
                    return $one->facility->full_name > $two->facility->full_name ? 1 : -1;
                }
            )
            ->sortByDesc('active')
            ->pluck('account_number');
    }

    public function getElimiwaitMpAccountNumbers()
    {
        return $this->monthlyParkingAccounts()
            ->whereHas(
                'facility',
                function ($query) {
                    $query->whereNotNull('elimiwait_location_id');
                }
            )
            ->with('facility', 'elimiwaitAccounts')
            ->get(['account_number', 'id', 'facility_id'])
            ->sortByDesc('active');
    }

    public function getShowVehicleRequestsAttribute()
    {
        if (!$this->monthlyParkingAccounts->count()) {
            return false;
        }

        return !!$this->monthlyParkingAccounts->first(
            function ($index, $mpUser) {
                return isset($mpUser->facility->is_elimiwait_active) ? $mpUser->facility->is_elimiwait_active : '';
            }
        );
    }

    public function getActiveElimiwaitRequests()
    {
        return $this->elimiwaitAccounts->flatMap(
            function ($account) {
                return $account->getActiveRequests();
            }
        );
    }

    public function elimiwaitAccounts()
    {
        return $this->hasManyThrough('App\Models\ElimiwaitAccount', 'App\Models\MonthlyParkingUser', 'user_id', 'mp_user_id');
    }


    /**
     * User may have many authorize.net cims
     *
     * @return [type] [description]
     */
    public function cim()
    {
        return $this->hasOne('App\Models\UserCim', 'user_id', 'id');
    }

    /**
     * Has many payment profiles through UserCim
     * See https://laravel.com/docs/5.2/eloquent-relationships#has-many-through for description of
     * foreign keys
     *
     * @return [type] [description]
     */
    public function paymentProfiles()
    {
        return $this->hasManyThrough(
            'App\Models\PaymentProfile',
            'App\Models\UserCim',
            'user_id',
            'cim_id',
            'id'
        );
    }

    public function getMonthlyParkingFacilitiesAttribute()
    {
        $result = MonthlyParkingUser::query()
            ->where('user_id', $this->id)
            ->has('facility')
            ->with('facility')
            ->orderBy('account_number')
            ->get();
        $facilities_array = [];

        foreach ($result as $accounts) {
            $facilities_array['facility'][$accounts->facility_id] = [
                'id' => $accounts->facility->id,
                'full_name' => $accounts->facility->full_name,
                'entrance_location' => $accounts->facility->entrance_location,
            ];
            $facilities_array['accounts'][$accounts->facility_id][] = [
                "id" => $accounts->id,
                "user_id" => $accounts->user_id,
                "facility_id" => $accounts->facility_id,
                "garage_code" => $accounts->garage_code,
                "account_number" => $accounts->account_number,
                "balance" => $accounts->balance,
                "curr_month_balance" => $accounts->curr_month_balance,
                "balance_updated" => $accounts->balance_updated,
                "active" => $accounts->active,
                "name" => $accounts->name,
                "address_one" => $accounts->address_one,
                "address_two" => $accounts->address_two,
                "city" => $accounts->city,
                "state" => $accounts->state,
                "zip" => $accounts->zip,
                "company_name" => $accounts->company_name,
                "created_at" => $accounts->created_at,
                "updated_at" => $accounts->updated_at,
                "deleted_at" => $accounts->deleted_at,
                "address_type" => $accounts->address_type,
                "country" => $accounts->country,
                "phone_type_one" => $accounts->phone_type_one,
                "phone_type_two" => $accounts->phone_type_two,
                "phone_number_one" => $accounts->phone_number_one,
                "phone_number_two" => $accounts->phone_number_two,
                "phone_ext_one" => $accounts->phone_ext_one,
                "phone_ext_two" => $accounts->phone_ext_two,
                "phone_contact_one" => $accounts->phone_contact_one,
                "phone_contact_two" => $accounts->phone_contact_two,
                "billing_method" => $accounts->billing_method,
                "address_updated" => $accounts->address_updated,
                "last_autopay" => $accounts->last_autopay,
                "is_valid_ar_account" => $accounts->is_valid_ar_account,
                "autopay" => $accounts->autopay,
                "full_name" => $accounts->facility->full_name,
                "entrance_location" => $accounts->facility->entrance_location
            ];
        }
        $accounts_array = [];
        if (count($facilities_array)) {
            foreach ($facilities_array['facility'] as $k => $v) {
                $accounts_array[] = array_merge($v, ["accounts" => $facilities_array['accounts'][$k]]);
            }
        }

        usort($accounts_array, function ($a, $b) {
            return strcmp($a["full_name"], $b["full_name"]);
        });

        return $accounts_array;
    }

    /**
     * User may have many monthly parking accounts
     *
     * @return [App\Models\MonthlyParkingUser]
     */
    // Commented on: 12th oct, 2018
    // public function monthlyParkingAccounts()
    // {
    // return $this->hasMany('App\Models\MonthlyParkingUser')->with('facility');
    // }

    public function monthlyParkingAccounts()
    {
        return $this->hasMany('App\Models\MonthlyParkingUser')->with([
            'facility' => function ($query) {
                $query->with('photos');
            }
        ]);
    }

    public function userVehicle()
    {
        return $this->hasMany('App\Models\Vehicle')->where('deleted_vehicle', 0);
    }

    /**
     * User may have many monthly parking accounts- for admin panel
     *
     * @return [App\Models\MonthlyParkingUser]
     */
    public function monthlyParkingAccountsAdmin()
    {
        return $this->hasMany('App\Models\MonthlyParkingUser')->has('facility');
    }

    /**
     * Users may have many reservations
     *
     * @return [type] [description]
     */
    public function reservations()
    {
        return $this->hasMany('App\Models\Reservation');
    }

    /**
     * Generate a code that can be used to verify a user's email address
     *
     * @return [type] [description]
     */
    public function generateEmailConfirmationCode()
    {
        $this->confirmation_code = str_random(40);
        $this->email_confirmed = false;
        return $this;
    }

    /**
     * Send an email to this user to verify that their email address is correct
     *
     * @return [type] [description]
     */
    public function sendEmailConfirmation()
    {
        $link = config('app.web_url') . '/user/' . $this->id . '/email/' . $this->confirmation_code;

        $mailer = new UserMailer($this);
        $name = config('icon.name');
        $mailer->sendMail("Confirm email with $name", ['link' => $link, 'username' => $this->name], 'email.email-confirmation');
    }

    /**
     * Send Wallet Notification Email to User
     *
     * @return [type] [description]
     */
    public function sendWalletEmail($subject, $msg, $transactionEntryamount, $transactionEntrytype)
    {
        // $link = config('app.web_url') . '/user/' . $this->id . '/email/' . $this->confirmation_code;

        $mailer = new UserMailer($this);
        $name = config('icon.name');
        if ($transactionEntrytype == 'SIGNUP') {
            $mailer->sendMail($subject, ['msg' => $msg, 'username' => $this->name, 'transactionEntryamount' => $transactionEntryamount], 'email.email-wallettransaction');
        } elseif ($transactionEntrytype == "REFERRAL") {
            $mailer->sendMail($subject, ['msg' => $msg, 'username' => $this->name, 'transactionEntryamount' => $transactionEntryamount], 'email.email-wallettransactionreff');
        } else {

            $mailer->sendMail($subject, ['msg' => $msg, 'username' => $this->name, 'transactionEntryamount' => $transactionEntryamount], 'email.email-wallettransactioncred');
        }
    }

    /**
     * Send Wallet Notification Email to User
     *
     * @return [type] [description]
     */
    public function sendWalletEmailToReferrer($subject, $msg, $transactionEntryamount, $transactionEntrytype, $ref_name = null)
    {
        // $link = config('app.web_url') . '/user/' . $this->id . '/email/' . $this->confirmation_code;

        $mailer = new UserMailer($this);
        $name = config('icon.name');
        //$mailer->sendMail($subject, ['msg' => $msg,'username'=>$this->name,'transactionEntryamount'=>$transactionEntryamount], 'email.email-wallet-referrer-credit');
        $mailer->sendMail($subject, ['msg' => $msg, 'username' => $this->name, 'refname' => $ref_name, 'transactionEntryamount' => $transactionEntryamount], 'email.email-wallettransactionreff');
    }

    /**
     * If the current request has the necessary information, log the user in
     */
    public function logIn(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'client_secret' => 'required',
                'client_id' => 'required',
                'grant_type' => 'required',
                'username' => 'required',
                'password' => 'required'
            ]
        );
        //set refresh token as 1 year expiry for app.
        if (isset($request->client_id) && $request->client_id == 'IconGo') {
            // Set our refresh token to expire in one year if the remember me box is set
            Config::set('oauth2.grant_types.refresh_token.refresh_token_ttl', 60 * 60 * 24 * 365);
        }
        return $validator->fails() ? null : Authorizer::issueAccessToken();
    }

    /**
     * Find or generate an anonymous user with the given email address
     */
    public static function getAnonUser($email)
    {
        // Look for an existing anonymous user with this email
        $user = self::where('email', $email)->first();

        if ($user) {
            return $user;
        }

        $deletedUser = self::withTrashed()->where('email', $email)->whereNotNull('deleted_at')->first();
        if (count($deletedUser) > 0) {
            $deletedUser->deleted_at = NULL;
            $deletedUser->save();
            return $deletedUser;
        }

        // No user, create a new one
        return self::create(
            [
                'name' => $email,
                'email' => $email,
                'password' => Hash::make(str_random(60)),
                'anon' => true,
                'user_type' => '5'
            ]
        );
    }

    public static function getAnonUserByPhone($phone)
    {
        // Look for an existing anonymous user with this email
        $user = self::where('phone', $phone)->first();

        if ($user) {
            return $user;
        }

        $deletedUser = self::withTrashed()->where('phone', $phone)->whereNotNull('deleted_at')->first();
        if (count($deletedUser) > 0) {
            $deletedUser->deleted_at = NULL;
            $deletedUser->save();
            return $deletedUser;
        }

        // No user, create a new one
        return self::create(
            [
                'name' => '',
                'email' => '',
                'phone' => $phone,
                'password' => Hash::make(str_random(60)),
                'anon' => true,
                'user_type' => '5'
            ]
        );
    }

    public static function getAnonUserEmailOrPhone($email, $phone, $owner_id = null)
    {
        // Look for an existing anonymous user with this email
        if ($owner_id != '') {
            $user = self::where('email', $email)->where('created_by', $owner_id)->first();
        } else {
            $user = self::where('email', $email)->first();
        }

        if ($user) {
            $user->phone = $phone;
            $user->save();
            return $user;
        }

        $deletedUser = self::withTrashed()->where('email', $email)->whereNotNull('deleted_at')->first();
        if (count($deletedUser) > 0) {
            $deletedUser->deleted_at = NULL;
            $deletedUser->save();
            return $deletedUser;
        }

        // No user, create a new one
        return self::create(
            [
                'name' => '',
                'email' => $email,
                'phone' => $phone,
                'password' => Hash::make(str_random(60)),
                'anon' => false,
                'user_type' => '5',
                'created_by' => $owner_id == '' ? '' : $owner_id
            ]
        );
    }

    public static function getUserByPhone($email, $phone)
    {
        // Look for an existing anonymous user with this email
        $user = self::where('phone', $phone)->first();

        if ($user) {
            $user->email = $email;
            $user->save();
            return $user;
        }

        $deletedUser = self::withTrashed()->where('phone', $phone)->whereNotNull('deleted_at')->first();
        if (count($deletedUser) > 0) {
            $deletedUser->deleted_at = NULL;
            $deletedUser->save();
            return $deletedUser;
        }

        // No user, create a new one
        return self::create(
            [
                'name' => $email,
                'email' => $email,
                'phone' => $phone,
                'password' => Hash::make(str_random(60)),
                'anon' => true,
                'user_type' => '5'
            ]
        );
    }


    public static function getAnonUserByPartner($email, $partner_id)
    {
        // Look for an existing anonymous user with this email
        $user = self::where('email', $email)->first();
        if ($user->user_type == '3') {
            return $user;
        }

        $user = self::where('email', $email)->where('created_by', $partner_id)->first();

        if ($user) {
            return $user;
        }

        $deletedUser = self::withTrashed()->where('email', $email)->where('created_by', $partner_id)->whereNotNull('deleted_at')->first();
        if (count($deletedUser) > 0) {
            $deletedUser->deleted_at = NULL;
            $deletedUser->save();
            return $deletedUser;
        }

        // No user, create a new one
        return self::create(
            [
                'name' => $email,
                'email' => $email,
                'password' => Hash::make(str_random(60)),
                'anon' => true,
                'user_type' => '5',
                'created_by' => $partner_id
            ]
        );
    }

    public function membershipPlans()
    {
        return $this->belongsToMany('App\Models\ParkEngage\MembershipPlan', 'user_membership')->withPivot(['id', 'start_date', 'end_date', 'is_active', 'plan_type']);
    }

    public function latestMembershipPlan()
    {
        return $this->membershipPlans()->orderBy('created_at', 'DESC')->with('service')->first();
    }

    public function userPermitTypeMapping()
    {
        return $this->hasMany('App\Models\ParkEngage\UserPermitTypeMapping', 'user_id');
    }

    public function userPermitServiceMapping()
    {
        return $this->hasMany('App\Models\ParkEngage\UserPermitServiceMapping', 'user_id');
    }

    public function userPermitBundleMapping()
    {
        return $this->hasMany('App\Models\ParkEngage\UserPermitBundleMapping', 'user_id');
    }

    public function subPartnerPermission()
    {
        return $this->hasMany('App\Models\ParkEngage\SubPartnerPermission', 'user_id');
    }

    public function userMember()
    {
        return $this->hasMany('App\Models\ParkEngage\UserMembership', 'user_id');
    }

    public function lastUpdatedMembershipPlan()
    {
        return $this->membershipPlans()->orderBy('updated_at', 'DESC')->with('service')->first();
    }

    public function brandSetting()
    {
        // return $this->hasOne('App\Models\ParkEngage\BrandSetting', 'user_id');
        if (($this->user_type == self::USERTYPE_CLERK) || ($this->user_type == self::USERTYPE_BUSINESS)) {
            return $this->hasOne('App\Models\ParkEngage\BrandSetting', 'user_id', 'created_by');
        } else {
            return $this->hasOne('App\Models\ParkEngage\BrandSetting', 'user_id');
        }
    }

    public function clients()
    {
        return $this->hasOne('App\Models\OauthClient', 'partner_id');
    }

    public function membershipPayment()
    {
        return $this->hasMany('App\Models\ParkEngage\MembershipPayment', 'user_id');
    }

    public function facilities()
    {
        return $this->hasMany('App\Models\Facility', 'owner_id');
    }

    public function memberUser()
    {
        return $this->belongsTo('App\Models\ParkEngage\MemberUser');
    }

    public function permit()
    {
        return $this->hasMany('App\Models\PermitRequest', 'user_id');
    }

    public function userPaymentGatewayDetail()
    {
        return $this->hasMany('App\Models\ParkEngage\UserPaymentGatewayDetail', 'user_id');
    }

    public function getLastNameAttribute($value)
    {
        if ($this->name != '') {
            $name = explode(" ", $this->name);
            $lastNameArray = array_shift($name);
            $lastName = implode(" ", $name);
            return $lastName;
        } else {
            return $this->name;
        }
    }
    public function getFirstNameAttribute($value)
    {
        if ($this->name != '') {
            $name = explode(" ", $this->name);
            return $name[0];
        } else {
            return $this->name;
        }
    }

    public function getResidentStatusAttribute($value)
    {
        if ($this->license_number != '') {
            $mystring = trim($this->license_number); //removes any spaces from beginning of the string
            $number = substr($mystring, 1);

            if (ctype_alpha($mystring[0]) && is_numeric($number) && strlen($mystring) == 9) {
                return "Resident";
            } else {
                return "Non-Resident";
            }
        } else {
            return $this->license_number;
        }
    }

    public function getProfileImageUrlAttribute()
    {
        if (!empty($this->photo_url))
            return Storage::url($this->photo_url);
    }

    public function getUserVehicleCountAttribute()
    {
        $result = 0;
        $vehicle = PermitVehicle::where('user_id', $this->id)->where('anon', $this->anon)->count();

        if ($vehicle) {
            $result = $vehicle;
        }

        return $result;
    }

    public function getSignatureUrlAttribute()
    {
        return config('app.url') . '/signature/' . $this->signature;
    }

    public function userFacility()
    {
        return $this->hasMany('App\Models\ParkEngage\UserFacility', 'user_id');
    }

    public function affliateBusiness()
    {
        return $this->belongsTo('App\Models\AffiliateBusiness', 'business_id');
    }


    public function clerkLimit()
    {
        return $this->hasOne('App\Models\ParkEngage\UserValidateMaster', 'user_id');
    }

    public function getPartnerNameAttribute()
    {
        return User::query()
            ->where('id', $this->created_by)
            ->select('name', 'id', 'email', 'company_name')
            ->first();
    }
    public function getClerkFacilityAttribute()
    {
        $facility = [];
        $clerkFacility = UserFacility::select('id', 'user_id', 'facility_id')->where('user_id', $this->id)->get();
        if ($clerkFacility) {
            foreach ($clerkFacility as $val) {

                $facilitData = Facility::select('id', 'full_name', 'short_name')->where('id', $val['facility_id'])->first();
                $facility[] = $facilitData['full_name'];
            }
            return  $facility;
        } else {
            return  $facility;
        }
    }

    public function getPartnerSlugAttribute()
    {
        if ($this->user_type == '5') {
            $partnerDetails =  UserPaymentGatewayDetail::select("touchless_payment_url")->where("user_id", $this->created_by)->first();
        } else {
            $partnerDetails =  UserPaymentGatewayDetail::select("touchless_payment_url")->where("user_id", $this->id)->first();
        }
        return isset($partnerDetails->touchless_payment_url) ? $partnerDetails->touchless_payment_url : '';
    }

    public function getRegionalManagerAttribute()
    {
        return User::query()
            ->select('id', 'name', 'user_type')
            ->where('id', $this->user_parent_id)
            ->where('user_type', '12')
            ->select('name', 'id', 'email', 'company_name')
            ->first();
    }

    public function getOperationModeAttribute()
    {
        return config('parkengage.OPERATION_MODE');
    }

    public function generateQrCode()
    {
        $code = 'P' . rand(10, 99) . rand(100, 999) . rand(100, 999) . 'S';
        $isExist = User::where('qr_code_number', $code)->first();
        if ($isExist) {
            $this->generateQrCode();
        }
        return $code;
    }

    public function events()
    {
        return $this->hasMany('App\Models\EventUser', 'user_id');
    }

    public function woodmanLicensePlate()
    {
        return $this->hasOne('App\Models\ParkEngage\WoodmanLicensePlate', 'partner_id');
    }

    public function licensePlate()
    {
        return $this->hasOne('App\Models\ParkEngage\LicensePlate', 'partner_id');
    }

    public function getUserCappingAttribute()
    {
        return UserPermitTypeMapping::query()
            ->where('user_id', $this->id)
            //->select('capacity','remaining_capacity')
            ->get();
    }

    /*   Vijay : 24-09-2024
    *   this funcation is responsible to return active checkin for user no respect to gated or ungated
    */
    public function activeCheckin()
    {
        return $this->hasMany('App\Models\Ticket', 'user_id')->where(['is_checkout' => '0'])->orderBy('id', 'desc')->first();
    }

    /* 
     *  Find or generate an anonymous user with the given email address or phone with partner
     *  Author : Vijay Pippal
     *  Date   : 23-08-2024 
     *  params
     *  partner_id  : this partner for requested user
     *  phoneOrEmail : only one value contains at a time either Email or Phone 
     *  phoneEmailFlag : flag to Indeicate 0 => Email 1 => Phone  
     */
    public static function getAnonUserByEmailOrPhone($partner_id, $phoneOrEmail, $phoneEmailFlag = 0, $name = null)
    {
        // Look for an existing anonymous user with this email
        if ($phoneEmailFlag == 0) {
            $user = self::where('email', $phoneOrEmail)->where('created_by', $partner_id)->first();
            //$deletedUser = self::withTrashed()->where('email', $phoneOrEmail)->whereNotNull('deleted_at')->first();
        } else {
            $user = self::where('phone', $phoneOrEmail)->where('created_by', $partner_id)->first();
            //$deletedUser = self::withTrashed()->where('phone', $phoneOrEmail)->whereNotNull('deleted_at')->first();
        }

        if ($user) {
            // $user->phone = $phone;
            // $user->save();
            return $user;
        }

        #PIMS-12688 as disussed with trapti mam Vijay also know this flow
        // if (count($deletedUser) > 0) {
        //     $deletedUser->deleted_at = NULL;
        //     $deletedUser->save();
        //     return $deletedUser;
        // }

        // No user, create a new one
        return self::create(
            [
                'name' => is_null($name) ? '' : $name,
                'email' => $phoneEmailFlag == 1 ? NULL : $phoneOrEmail,
                'phone' => $phoneEmailFlag == 1 ? $phoneOrEmail : NULL,
                'password' => NULL,
                'anon' => true,
                'user_type' => '5',
                'created_by' => $partner_id
            ]
        );
    }

    public function BusinessPolicy()
    {
        return $this->HasMany('App\Models\BusinessFacilityPolicy', 'business_id', 'business_id')->select('id', 'business_id', 'facility_id', 'policy_id');
    }

    public function saveUserHistroy($request, $common_id, $service_type)
    {
        $service = ServiceMaster::where('service_type', $service_type)->first();

        $userHistory = new UserHistory([
            'user_id'      => $request->user_id,
            'name'         => $request->name,
            'email'        => $request->email,
            'phone'        => $request->phone,
            'anon'         => $request->anon,
            'company_name' => $request->companyName ?? NULL,
            'address'      => $request->address,
            'address2'     => $request->address2,
            'city'         => $request->city,
            'country'      => $request->country,
            'state'        => $request->state,
            'pincode'      => $request->pincode,
            'service_id'   => $service->id,
            'common_id'    => $common_id
        ]);
        $userHistory->save();
    }

    /***
     * function fetchOrCreateUserThirdParty
     * $request object formed from permit api data
     * owner_id partner_id
     * name is user name(first_name last_name)
     * allowLogin keep it false
     * $user user object if exist else blank
     */

    public static function fetchOrCreateUserThirdParty($request, $owner_id, $name = '', $allowLogin = false, $user, $countryCode = "+1")
    {
        $phone = '';
        //$countryCode = QueryBuilder::appendCountryCode();

        if (!isset($name) || empty($name)) {
            $name = $request->first_name . " " . $request->last_name;
        }
        if ($user && $user != '') {
            // Update existing user details
            $user->updateUserDetails($request, $countryCode, $name);
        } else {
            // Create new user
            if (isset($request->phone) && !empty($request->phone)) {
                $phone = $countryCode . $request->phone;
            }
            if (isset($request->email) && !empty($request->email)) {
                $email = $request->email;
            }
            if (!isset($email)) {
                throw new ApiGenericException("Email is mandatory. Please try again with correct data.");
            }


            $user = self::create([
                'name' => $name,
                'email' => $email ?? NULL,
                'phone' => $phone ?? NULL,
                'password' => $request->confirm_password ? Hash::make($request->confirm_password) : NULL,  // If no password, set to NULL
                'anon' => $request->confirm_password ? false : true,  // Set anon true if no password
                'user_type' => '5',
                'created_by' => $owner_id,
                'company_name' => $request->companyName ?? NULL,
                'address' => $request->address ?? NULL,
                'address2' => $request->address2 ?? NULL,
                'city' => $request->city ?? NULL,
                'country' => $request->country ?? NULL,
                'state' => $request->state ?? NULL,
                'pincode' => $request->pincode ?? NULL,
                'thirdparty_userid' => $request->thirdparty_userid ?? NULL,
            ]);
        }

        return $user;
    }

    public function updateUserDetails($request, $countryCode, $name)
    {

        // Update password only if confirm_password is present
        if (isset($request->confirm_password) && isset($request->anon) && $request->confirm_password && !$request->anon) {
            $this->password = Hash::make($request->confirm_password);
            $this->anon = false;
        } else if (isset($request->anon)) {
            // If no password and no confirm_password, set password to NULL and anon to true
            $this->password = NULL;
            $this->anon = true;
        }

        // Update other fields
        $this->name = $name;
        $this->email = $this->email ?: $request->email;

        if (isset($request->phone) && !empty($request->phone)) {
            $this->phone = $this->phone ?: $countryCode . $request->phone;
        }
        $this->company_name = $request->companyName ?? $this->company_name;
        $this->address = $request->address ?? $this->address;
        $this->address2 = $request->address2 ?? $this->address2;
        $this->city = $request->city ?? $this->city;
        $this->country = $request->country ?? $this->country;
        $this->state = $request->state ?? $this->state;
        $this->pincode = $request->pincode ?? $this->pincode;

        $this->save();
    }

    public function dashboardMappings()
    {
        return $this->hasMany(\App\Models\SuperSet\PartnerMappedDashboardList::class, 'partner_id');
    }
    //PMIS-13403
    public function createUser($request)
    {
        return self::create(
            [
                'name' => '',
                'email' => '',
                'phone' => '',
                'password' => Hash::make(str_random(60)),
                'anon' => true,
                'user_type' => '5',
                'created_by' => $request->owner_id,
                'session_id' => isset($request->session_id) ? $request->session_id : 0
            ]
        );
    }


    public function appStoreUrls()
    {
        return $this->hasMany(AppStoreUrl::class, 'partner_id');
    }
}
