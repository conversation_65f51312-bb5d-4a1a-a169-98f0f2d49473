<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Services\Mailers\UserMailer;
use App\Models\PermitVehicleMapping;
use App\Models\Facility;
use App\Models\OauthClient;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Services\Image;
use App\Models\AccountNameMaster;
use App\Models\ParkEngage\PermitRequestServiceMapping;
use App\Models\ParkEngage\PermitTypeMaster;
use App\Models\PermitVehicle;
use App\Models\BlackListedVehicle;
use App\Services\LoggerFactory;
use Exception;
use Carbon\Carbon;
use App\Models\PermitRequestRenewHistory;
use App\Http\Helpers\QueryBuilder;

class PermitRequest extends Model
{
    use SoftDeletes;

    public $table = 'permit_requests';

    protected $fillable = [];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $hidden = ['updated_at'];

    protected $dates = ['deleted_at'];

    protected $appends = ['approved', 'idfront', 'idback', 'formatted_desired_start_date', 'formatted_desired_end_date', 'is_expired', 'account'];

    public static $searchFields = [
        'email',
        'name'
    ];

    public static $validParams = [
        'desired_start_date' => 'required|date|after:yesterday',
        'email' => 'required|email',
    ];

    public static $validParamsMessages = [
        'desired_start_date.after' => 'The desired start date must not be in past'

    ];

    protected static function boot()
    {
        parent::boot();

        static::updating(
            function ($monthlyRequest) {
                if ($monthlyRequest->tracking_code) {
                    return;
                }

                $monthlyRequest->tracking_code = $monthlyRequest->generateTrackingCode();
            }
        );

        static::created(
            function ($monthlyRequest) {
                $monthlyRequest->tracking_code = $monthlyRequest->generateTrackingCode();
                $monthlyRequest->save();
            }
        );
    }

    public function getFormattedDesiredStartDateAttribute()
    {
        $date = $this->desired_start_date;
        $day = date('d', strtotime($date));
        $monthYear = date('F, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
        $second_last_digit = substr($number, -2, 1);
        $suffix = 'th';
        if ($second_last_digit != '1') {
            switch ($last_digit) {
                case '1':
                    $suffix = 'st';
                    break;
                case '2':
                    $suffix = 'nd';
                    break;
                case '3':
                    $suffix = 'rd';
                    break;
                default:
                    break;
            }
        }
        if ((string) $number === '1') $suffix = 'st';
        return $number . $suffix . ' ' . $monthYear;
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'admin_user_id');
    }


    public function getIsExpiredAttribute()
    {
        $this->setCustomTimezone($this->facility_id);
        $today = strtotime(date("Y-m-d"));
        $endDate = strtotime($this->desired_end_date);
        $is_expired = 0;
        if ($today > $endDate) {
            $is_expired = 1;
        }
        return $is_expired;
    }

    public function getFormattedDesiredEndDateAttribute()
    {

        $date = $this->desired_end_date;
        $day = date('d', strtotime($date));
        $monthYear = date('F, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
        $second_last_digit = substr($number, -2, 1);
        $suffix = 'th';
        if ($second_last_digit != '1') {
            switch ($last_digit) {
                case '1':
                    $suffix = 'st';
                    break;
                case '2':
                    $suffix = 'nd';
                    break;
                case '3':
                    $suffix = 'rd';
                    break;
                default:
                    break;
            }
        }
        if ((string) $number === '1') $suffix = 'st';
        return $number . $suffix . ' ' . $monthYear;
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function facility()
    {
        // return $this->belongsTo(Facility::class);
        return $this->belongsTo('App\Models\Facility')->select(['id', 'short_name', 'full_name', 'garage_code', 'timezone', 'owner_id', 'permit_processing_fee', 'additonal_fee', 'additonal_fee_type', 'surcharge_fee', 'surcharge_fee_type', 'tax_rate_type', 'tax_rate']);
    }

    #pims-13916 #dd

    public function category()
    {
        return $this->belongsTo('App\Models\ParkEngage\PermitTypeMaster', 'permit_type_master_id', 'id')->select(['id', 'permit_type_name', 'partner_id']);
    }

    public function monthlyRateType()
    {
        return $this->hasOne(FacilityRate::class, 'id', 'monthly_rate_id');
    }

    public function cim()
    {
        return $this->hasOne(MonthlyRequestCim::class, 'monthly_request_id');
    }

    public function transaction()
    {
        return $this->belongsTo(AuthorizeNetTransaction::class, 'anet_transaction_id')->select(['id', 'total', 'anet_type_id', 'anet_status_id', 'ref_id', 'payment_last_four', 'card_type', 'expiration', 'created_at', 'auth_code', 'anet_trans_id', 'name', 'response_message']);
    }

    public function PermitRate()
    {
        return $this->belongsTo('App\Models\PermitRate', 'permit_rate_id')->select(['id', 'permit_rate_description_id', 'facility_id', 'rate', 'active', 'name', 'is_unlimited', 'rate_type', 'total_count', 'total_usage', 'remaining_usage', 'user_type', 'is_resident', 'sign_up_timeframe', 'created_at', 'updated_at', 'is_promotion', 'is_hide_vehicle', 'is_hide_permit_service', 'is_hide_staff', 'is_display_reason', 'penality_amount', 'prorate_disable', 'is_hide_carpool_toogle']);
    }

    public function getApprovedAttribute()
    {
        return $this->approved_on ? true : false;
    }

    public function loadRelations()
    {
        return $this->load('user', 'facility', 'monthlyRateType.rateDescription', 'transaction', 'cim');
    }

    public function generateTrackingCode()
    {
        return 'SC' .  $this->id . 'F' . $this->facility_id . 'U' . $this->user_id;
    }

    public function getIdfrontAttribute()
    {
        if ($this->image_front) {
            return config('app.url') . '/permit-image-front/' . $this->id;
        } else {
            return '';
        }
    }

    public function getIdbackAttribute()
    {
        if ($this->image_back) {
            return config('app.url') . '/permit-image-back/' . $this->id;
        } else {
            return '';
        }
    }

    public function PermitVehicle()
    {
        return $this->hasMany('App\Models\PermitVehicleMapping', 'permit_request_id');
    }

    public function permitTicket()
    {
        return $this->hasMany('App\Models\PermitTicket', 'permit_request_id');
    }

    public function getIdProofFrontAttribute()
    {
        return config('app.url') . '/permit-id-proof-front/' . $this->id;
    }

    public function getIdProofBackAttribute()
    {
        return config('app.url') . '/permit-id-proof-back/' . $this->id;
    }

    public function getUtilityBillAttribute()
    {
        return config('app.url') . '/permit-id-utility-bill/' . $this->id;
    }

    public function getTaxReturnAttribute()
    {
        return config('app.url') . '/permit-id-tax-return/' . $this->id;
    }

    public function getDriversLicenseAttribute()
    {
        return config('app.url') . '/permit-id-drivers-license/' . $this->id;
    }

    public function getPayStubAttribute()
    {
        return config('app.url') . '/permit-id-pay-stub/' . $this->id;
    }

    public function getProofOfLowIncomeAttribute()
    {
        return config('app.url') . '/permit-id-proof-of-low-income/' . $this->id;
    }

    public function getAnyAttribute()
    {
        return config('app.url') . '/permit-id-any/' . $this->id;
    }

    public function getProofOfLowIncomeExcelAttribute()
    {
        return config('app.url') . '/permit-id-proof-of-low-income-excel/' . $this->id;
    }

    public function getaccountAttribute()
    {
        if ($this->account_name) {
            $account_name = AccountNameMaster::Select('name')->where('id', $this->account_name)->first();
            if ($account_name) {
                return $account_name->name;
            } else {
                return NULL;
            }
        }
    }

    public function permitRequestDocument()
    {
        return $this->hasMany('App\Models\ParkEngage\PermitRequestDocuments');
    }

    public function PermitDoc()
    {
        return $this->hasMany('App\Models\ParkEngage\PermitRequestDocuments');
    }

    public function warning()
    {
        return $this->hasMany('App\Models\ParkEngage\Warning', 'permit_request_id');
    }

    public function citation()
    {
        return $this->hasMany('App\Models\ParkEngage\TicketCitation', 'permit_request_id');
    }

    public function Rate()
    {
        return $this->belongsTo('App\Models\PermitRate', 'permit_rate_id');
    }

    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);
        if ($facility) {
            $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
            $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $secret->partner_id)->first();

            if (($facility) && ($facility->timezone != '')) {
                date_default_timezone_set($facility->timezone);
            } else {
                if ($partnerTimezone) {
                    if ($partnerTimezone->timezone != '') {
                        date_default_timezone_set($partnerTimezone->timezone);
                    }
                }
            }
        }
    }
    //Alka
    public function AuthorizedDriver()
    {
        return $this->hasMany('App\Models\ParkEngage\AuthorizedDriver');
    }

    //dushyant PIMS-6625
    public function PermitRequestServiceMapping()
    {
        return $this->hasMany('App\Models\ParkEngage\PermitRequestServiceMapping', 'permit_request_id');
    }

    public function generatePermitInvoice($billingDetails, $format = Image::class)
    {
        $data = ['billingDetails' => $billingDetails];
        if ($format == Image::class) {
            $html = view("permitInvoice.billing", $data)->render();
        } else {
            $html = view("permitInvoice.billing", $data)->render();
        }

        $image = app()->make($format);
        return $image->getOutputFromHtmlString($html);
    }

    #PIMS-10032 dushyant 

    public function generateBusinessPermitReminderInvoice($data, $format = Image::class)
    {
        //$data = ['billingDetails' => $billingDetails];
        if ($format == Image::class) {
            $html = view("permitInvoice.reminder-billing", $data)->render();
        } else {
            $html = view("permitInvoice.reminder-billing", $data)->render();
        }

        $image = app()->make($format);
        return $image->getOutputFromHtmlString($html);
    }

    public function getPermitRequestRenewHistory()
    {
        return $this->hasMany('App\Models\PermitRequestRenewHistory', 'permit_request_id');
    }

    public function latestRenewHistory()
    {
        return $this->hasOne('App\Models\PermitRequestRenewHistory', 'permit_request_id')
            ->orderBy('created_at', 'desc');
    }

    // Alka
    public function penalityTransaction()
    {
        return $this->belongsTo(AuthorizeNetTransaction::class, 'penality_cancel_id');
    }

    #PIMS-11336 DD
    public function business()
    {
        return $this->belongsTo(User::class, 'business_id', 'id')->select(['id', 'name', 'email', 'phone', 'is_partner', 'pincode', 'user_type', 'company_email', 'city', 'country', 'company_name', 'created_by', 'address', 'address2', 'state', 'business_id', 'user_parent_id', 'slug', 'is_multiple_rm_enabled', 'default_rm', 'is_rm_enabled', 'attendent_type', 'user_consent', 'term_condition_heading', 'is_auto_checkout_enabled', 'local_user_id']);
    }

    #PIMS-12704 DD THIRD PARTY PERMIT INSERT/UPDATE
    public static function savePermitRequestThirdParty($facility, $user, $request, $partner_id, $action = 0, $PermitRequest = null)
    {
        /***
         $facility Facility Model object
            $user User Model object
            $request object formed from permit api data
            $partner_id Partner Id
            $action if cancel permit then 1, if permit update then 2 , if permit new or not status shared then default 0
         **/
        try {

            /********** set time zone **********/
            if ($partner_id != '') {
                if (isset($facility) && ($facility->timezone != '')) {
                    date_default_timezone_set($facility->timezone);
                } else {
                    $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $partner_id)->first();
                    if ($partnerTimezone) {
                        if ($partnerTimezone->timezone != '') {
                            date_default_timezone_set($partnerTimezone->timezone);
                        }
                    }
                }
            }

            if (is_null($PermitRequest) || empty($PermitRequest)) {
                if (isset($request->permit_type_name) && !empty($request->permit_type_name)) {
                    $permit_type_name =  $request->permit_type_name;
                } else {
                    $permit_type_name =  '';
                }

                $PermitRequest = PermitRequest::where('user_id', $user->id)
                    ->where('partner_id', $partner_id)
                    ->where('facility_id', $facility->id)
                    ->where('permit_type_name', $permit_type_name)
                    ->WhereNull('cancelled_at');

                if (!$action) { // if new case
                    $PermitRequest = $PermitRequest->whereDate('desired_end_date', '>=', $request->desired_start_date)->first();
                } else {
                    $PermitRequest = $PermitRequest->first();
                }
            }
            if (isset($request->desired_start_date) && !empty($request->desired_start_date)) {
                $desired_start_date = $request->desired_start_date;
            } else {
                $desired_start_date = date('Y-m-d');
            }
            if (isset($request->desired_end_date) && !empty($request->desired_end_date)) {
                $desired_end_date = $request->desired_end_date;
            } else {
                $desired_end_date = date('Y-m-t');
            }

            $startDate  = Carbon::parse($desired_start_date);
            $endDate    = Carbon::parse($desired_end_date);

            $no_of_days = $startDate->diffInDays($endDate);

            // Create new permit if not exist
            if (!$PermitRequest) {
                if ($action == '1' || $action == '2') { // if cancel / update permit status passed 
                    return 'Permit not exist for email: ' . $user->email;
                }
                $PermitRequest = new self();
                $PermitRequest->facility_id     = $facility->id;
                $PermitRequest->user_id         = $user->id;
                $PermitRequest->approved_on     = date('Y-m-d H:i:s');
                $PermitRequest->email           = $user->email;
                if (isset($user->phone) && !empty($user->phone)) {
                    $PermitRequest->phone           = $user->phone;
                }
                $PermitRequest->permit_rate_id  = isset($request->permit_rate_id) ? $request->permit_rate_id : null;
                $PermitRequest->permit_rate     = isset($request->permit_rate) ? $request->permit_rate : 0;
                $PermitRequest->account_number  = isset($request->account_number) ? $request->account_number : '';
                $PermitRequest->partner_id      = $user->created_by;
                $PermitRequest->user_consent    = isset($request->user_consent) ? $request->user_consent : 0;
                $PermitRequest->is_admin        = isset($request->is_admin) ? $request->is_admin : 0;
                $PermitRequest->account_name    = isset($request->account_name) ? $request->account_name : '';
                $PermitRequest->permit_type_name = $permit_type_name;
                $PermitRequest->user_type_id    = isset($request->user_type_id) ? $request->user_type_id : null;
                $PermitRequest->discount_amount = isset($request->discount_amount) ? $request->discount_amount : NULL;
                $PermitRequest->promocode       = isset($request->promocode) ? $request->promocode : NULL;
                $PermitRequest->negotiated_amount = isset($request->negotiated_amount) ? $request->negotiated_amount : NULL;
                $PermitRequest->processing_fee  = isset($request->processing_fee) ? $request->processing_fee : NULL;

                $PermitRequest->request_reason  = isset($request->request_reason) ? $request->request_reason : null;
                $PermitRequest->is_prorate_apply = isset($request->is_prorate_apply) ? $request->is_prorate_apply : 0;
                $PermitRequest->service_prorate = isset($request->service_prorate) ? $request->service_prorate : NULL;
                $PermitRequest->permit_prorate  = isset($request->permit_prorate) ? $request->permit_prorate : NULL;

                $PermitRequest->third_party_account_id    = isset($request->third_party_account_id) ? $request->third_party_account_id : NULL;
                $PermitRequest->third_party_user_id = isset($request->thirdparty_userid) ? $request->thirdparty_userid : NULL;

                if (isset($request->business_id) && !empty($request->business_id)) {
                    $PermitRequest->business_id = $request->business_id;
                } else {
                    $PermitRequest->business_id = NULL;
                }
            } else {
                if ($action == '1') {  //if action cancel sent 
                    //$PermitRequest->cancelled_at        = date('Y-m-d H:i:s');
                    $PermitRequest->desired_end_date    = isset($request->desired_end_date) ? $request->desired_end_date : date('Y-m-d');
                    $PermitRequest->save();
                    return $PermitRequest;
                }
            }
            if ($action == '2') {  // if update
                if ($PermitRequest->desired_start_date == $desired_start_date && $PermitRequest->desired_end_date == $desired_end_date) {
                    return $PermitRequest;
                }
                if ($PermitRequest->desired_start_date > $desired_start_date) {
                    return "Please check start date( " . $desired_start_date . " ) for email: " . $user->email;
                }
                if ($PermitRequest->desired_end_date != $desired_end_date) { // if renew case
                    if ($PermitRequest->desired_end_date > $desired_end_date) {
                        return "Please check end date( " . $desired_end_date . " ) for email: " . $user->email;
                    }
                    $renewHistory = PermitRequestRenewHistory::where('permit_request_id', $PermitRequest->id)->where('desired_end_date', $desired_end_date)->first();
                    if (!$renewHistory) {
                        $renew = 1;
                        QueryBuilder::createPermitRequestHistoryNew($PermitRequest);
                    } else {
                        return $PermitRequest;
                    }
                } elseif ($PermitRequest->desired_start_date != $desired_start_date && $PermitRequest->desired_end_date == $desired_end_date) {
                    return "Please check start date( " . $desired_start_date . " ) for email: " . $user->email;
                }
                if ($PermitRequest->desired_start_date == $desired_start_date && $PermitRequest->desired_end_date != $desired_end_date) {
                    //$desired_start_date = $PermitRequest->desired_end_date; 
                    //if start date not changed but end date changed then update start date with end date so reports will work fine
                }
                if ($desired_start_date > $desired_end_date) {
                    return "Please check start date( " . $desired_start_date . " ) is greater than end date( " . $desired_end_date . " ) for email: " . $user->email;
                }
            }
            $PermitRequest->no_of_days      = isset($no_of_days) ? $no_of_days : 30;

            $PermitRequest->desired_start_date  = $desired_start_date;
            $PermitRequest->desired_end_date    = $desired_end_date;
            if (isset($request->license_number) && !empty($request->license_number)) {
                $PermitRequest->license_number  = $request->license_number;
            }
            if (isset($request->name) && !empty($request->name)) {
                $PermitRequest->name  = $request->name;
            }
            if (isset($request->third_party_subscription_id) && !empty($request->third_party_subscription_id)) {
                $PermitRequest->third_party_subscription_id  = $request->third_party_subscription_id;
            }
            $PermitRequest->third_party_type    = isset($request->third_party_type) ? $request->third_party_type : 1;
            $PermitRequest->grace_end_date = QueryBuilder::fetchPermitGraceEndDate($PermitRequest);
            $PermitRequest->user_consent = 1;
            $PermitRequest->hid_card_number    = isset($request->hid_card_number) ? $request->hid_card_number : NULL;
            $PermitRequest->save();
            if ($renew) {
                $PermitRequest->renew = 1;
                unset($renew);
            }
            return $PermitRequest;
        } catch (Exception $e) {
            $logFactory = new LoggerFactory();
            $log = $logFactory->setPath('logs/permit-requests')->createLogger('save-permit-data-third-party');
            $log->error("savePermitRequestThirdParty: log for thirdparty Permit Sql Exception Case" . json_encode($e->getMessage()));
            return "There is some issue occured while Creating the permit for email: " . $user->email . " .Please Contact Admin";
        }
    }

    public static function saveOrUpdatePermitVehicle($permitRes, $user, $request, $partner_id)
    {
        /***
         $permitRes PermitRequest object return from above function 
            $user User object
            $request object formed from permit api data
            $partner_id Partner Id
         **/
        $logFactory = new LoggerFactory();
        $log = $logFactory->setPath('logs/thridparty/parkchrip')->createLogger('parkchrip');
        $log->info("vechile data" . json_encode($request->vehicleList));

        $vehicleList = json_decode($request->vehicleList, true); // Decode as an associative array

        if (json_last_error() !== JSON_ERROR_NONE) {
            $log->error("JSON Decode Error: " . json_last_error_msg());
            return 0;
        }

        $log->info("Decoded vehicle data: " . json_encode($vehicleList));
        $response = [];

        foreach ($vehicleList as $key => $value) {
            $newVehicleMapping = 0;
            $log->info("proceed" . $value);

            if (array($value)) {
                $value = json_decode(json_encode($value)); // Ensure it's an object

                $log->info("proceed: " . json_encode($value));
            }

            if (isset($value->licensePlateNumber)) {
                $value->license_plate = $value->licensePlateNumber;
            }

            $value->license_plate = str_replace(' ', '', $value->license_plate); //remove spaces      

            $isExpire = PermitVehicle::with('vehicles')->where('license_plate_number', $value->license_plate)->where('partner_id', $partner_id)->whereNull('deleted_at')->first();
            //dd($isExpire,$value->license_plate,$partner_id);
            $log->info("isExpire" . $isExpire);
            if (isset($isExpire) && !empty($isExpire)) {
                if ($isExpire->user_id != $user->id) {
                    $response['error'][] = "License Plate already exist for different user " . $value->license_plate;
                    $isExpire->save();
                    continue;
                }
                $vehicle_id = $isExpire->id;
                $vehicle     = PermitVehicle::permitVehicleFilterData($value, $user->id, $partner_id, 0);
                $log->info("Requests vehicle data: " . json_encode($vehicle));
                if (isset($permitRes)) {
                    $isPermitVehicleMapping = PermitVehicleMapping::where('permit_vehicle_id', $vehicle_id)->where('permit_request_id', $permitRes->id)->first();
                    $log->info("permisPermitVehicleMappingitRes" . $isPermitVehicleMapping);
                    if (isset($isPermitVehicleMapping) && !empty($isPermitVehicleMapping)) {
                    } else {
                        $newVehicleMapping = 1;
                        $mapping = new PermitVehicleMapping();
                        $mapping->permit_request_id = $permitRes->id;
                        $mapping->permit_vehicle_id = $vehicle_id;
                        $mapping->save();
                    }
                }
                if (strtolower(trim($isExpire->make)) != strtolower(trim($vehicle['make'])) || strtolower(trim($isExpire->color)) != strtolower(trim($vehicle['color']))) {
                    PermitVehicle::where('id', $isExpire->id)->update($vehicle);
                    $log->info("permitRes" . $permitRes);
                } else {
                    if (!$newVehicleMapping) {
                        $response['error'][] = "License Plate already exist " . $value->license_plate;
                        continue;
                    }
                }
            } else {
                $vehicle = PermitVehicle::permitVehicleFilterData($value, $user->id, $partner_id, 1);
                $log->info("Requests vehicle data: " . json_encode($vehicle));
                $res = PermitVehicle::create($vehicle);
                $vehicle_id = $res->id;
                if (isset($permitRes)) {
                    $mapping = new PermitVehicleMapping();
                    $mapping->permit_request_id = $permitRes->id;
                    $mapping->permit_vehicle_id = $vehicle_id;
                    $mapping->save();
                }
            }
            $response['success'][] = "License Plate success: " . $value->license_plate;
        }
        return json_encode($response);
    }

    public static function licensePlateFilter($license_plate, $make, $model)
    {

        if ($license_plate == '') {
            return "Make mandatory for license_plate: " . $license_plate;
        }

        $license_plate = str_replace(' ', '', $license_plate); //remove spaces

        // Check if it contains only digits and alphabets
        if (!preg_match('/^[a-zA-Z0-9]+$/', $license_plate) || strlen($license_plate) < 2 || strlen($license_plate) > 10) {
            return "Invalid License Plate: " . $license_plate;
        }

        $vehicleBlackCheck = BlackListedVehicle::where('license_plate_number', $license_plate)->orderBy('id', 'desc')->first();
        if (isset($vehicleBlackCheck) && !empty($vehicleBlackCheck)) {
            return "Permit Not Created as BlackListed Vehicle: " . $license_plate;
        }

        if (empty($make) && !empty($model)) {
            return "Make mandatory for model: " . $model;
        }

        return 0;
    }

    public static function licenseMaxCheck($facility, $user, $request, $partner_id)
    {
        $currentVehicleCount    = PermitVehicle::where(['user_id' => $user->id, 'anon' => $user->anon])->count();
        $vehicleCountLimit      = $facility->facilityConfiguration->max_vehicle;
        foreach (json_decode($request->vehicleList) as $key => $value) {
            $isExpire = PermitVehicle::where('license_plate_number', $value->license_plate)->where('partner_id', $partner_id)->first();
            if (isset($isExpire) && !empty($isExpire) && $isExpire->user_id == $user->id) {
            } else if (!is_null($vehicleCountLimit)  && $currentVehicleCount >= $vehicleCountLimit) {
                return "Vehicle limit exceeded. You can only add up to $vehicleCountLimit vehicles. " . $value->license_plate;
            }
        }
        return 0;
    }
}
