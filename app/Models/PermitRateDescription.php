<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PermitRateDescription extends Model
{
    use SoftDeletes;

    protected $fillable = [
        "name",'name_alias', "hours_description", "description", "active_status","partner_id",'type_id',"is_resident","label","is_promotion","campus_id","order","is_hide_staff","is_hide_vehicle","is_hide_permit_service","is_purchase","is_negotiable","email_domain_validation","email_domain","permit_start_date","permit_end_date","permit_frequency","is_display_reason","is_hide_validity",
    ];
    protected $hidden = ['created_at', 'updated_at', 'deleted_at'];

    public static $validParams = [
        'name' => 'required',
        'hours_description' => 'required',
        'description' => 'required'        
    ];

     public function getPermitTenureAttribute()
    {
        return $this->permit_frequency.' '.ucwords($this->permit_frequency_unit.($this->permit_frequency > 1 ? 's' : '')); 
    }
  

    public function PermitRate()
    {
        return $this->hasMany('App\Models\PermitRate', 'permit_rate_description_id');
    }

    public function PermitRateDiscount()
    {
        return $this->hasMany('App\Models\PermitRateDiscount', 'permit_rate_description_id');
    }
    public function permitTypeMasterRateDescMapping()
    {
        return $this->hasMany('App\Models\PermitTypeMasterRateDescMapping', 'permit_rate_description_id');
    }

    public static $validateParams = [
        'name' => 'required',
        'hours_description' => 'required',
        'description' => 'required',
        "name_alias" => "required",      
    ];

    public static $deleteValidateParams = [
        'id' => 'required'     
    ];
}
