<?php

namespace App\Models\GoogleWallet;

use Illuminate\Database\Eloquent\Model;
use App\Models\User;
use Illuminate\Database\Eloquent\SoftDeletes;
class LoyaltyPass extends Model
{  
    use SoftDeletes;
    public $table = 'loyalty_pass';

    
    protected $guarded = ['id'];
    protected $hidden = ['created_at', 'updated_at'];
    protected $dates = ['deleted_at'];
    protected $fillable = ['user_id', 'loyalty_object_id'];

     public static $validParams = [
        'loyalty_object_id' => 'required'

        
    ];
   
    /* public function user()
    {
        return $this->belongsTo(User::class);
    }*/
}
