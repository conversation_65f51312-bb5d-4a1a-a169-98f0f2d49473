<?php
/*
 * -----------------------------------------------------------------------------
 * Copyright (c) 2024 ParkEngage Inc. All rights reserved.
 * -----------------------------------------------------------------------------
 *
 * This software is the confidential and proprietary information of
 * ParkEngage Inc. ("Confidential Information"). You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms
 * of the license agreement you entered into with ParkEngage Inc.
 *
 * This source code is a valuable trade secret of ParkEngage Inc.
 *
 * DISCLAIMER:
 * THIS SOFTWARE IS PROVIDED BY PARKENGAGE INC. "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL PARKENGAGE INC. OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are
 * those of the authors and should not be interpreted as representing official
 * policies, either expressed or implied, of ParkEngage Inc.
 * -----------------------------------------------------------------------------
 */

namespace App\Models;

use App\Exceptions\ApiGenericException;
use View;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

use App\Classes\GoogleMaps;
use App\Classes\Elimiwait;
use App\Http\Helpers\QueryBuilder;
use App\LprTicketMapping;
use App\Models\ParkEngage\FacilityOvernightDuration;
use App\Models\User;
use App\Models\OverstayTicket;
use App\Models\ParkEngage\TicketExtend;
use App\Services\LoggerFactory;
use Illuminate\Support\Facades\Log;
use App\Models\Reservation;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Facility;
use App\Models\ParkEngage\UserValidateMaster;
use App\Models\BusinessPolicy;
use App\Models\Promotion;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\ParkEngage\LPRFeed;
use DB;


class Ticket extends Model
{
    use SoftDeletes;
    public $table = 'tickets';

    const RESERVATION_BONUS_HOURS = 1.00;
    const RESERVATION_BONUS_RATE = 5.00;

    const RESERVATION_OVERSTAY_HOURS = 1.00;
    const RESERVATION_OVERSTAY_RATE = 10.00;

    const DRIVE_UP_PROCESSING_FEE = '0';
    const PROCESSING_FEE = '1';   // for Reservation
    const CITATION_PROCESSING_FEE = '2';
    const SKIP_FEE = true;

    public $diffInHours = 0;
    public $oversize_fee;

    protected $fillable = [
        "user_id",
        "facility_id",
        "anet_transaction_id",
        "invoice_number",
        "ticket_number",
        "ticket_security_code",
        "check_in_datetime",
        "estimated_checkout",
        "rate_description",
        "checkout_datetime",
        "reservation_id",
        "is_checkin",
        "is_checkout",
        "checkin_gate",
        "checkout_gate",
        "total",
        "grand_total",
        "length",
        "checkin_time",
        "checkout_time",
        "partner_id",
        "user_pass_id",
        "checkin_by",
        "checkout_without_checkin",
        "vp_device_checkin",
        "payment_date",
        "event_id",
        "slot_no",
        "expiry",
        "card_last_four",
        "card_type",
        "license_plate",
        "is_autopay",
        "checkout_qr_code",
        "vehicle_id",
        "orignal_length",
        "discount_amount",
        "promocode",
        "discount_hours",
        "base_length",
        "device_type",
        "terminal_id",
        "payment_gateway",
        "session_id",
        "cardholder_name",
        "checkout_cardholder_name",
        "checkout_card_last_four",
        "checkout_expiry",
        "checkout_card_type",
        "is_transaction_status",
        // "lot_facility",
        "payment_token",
        "is_priceband_apply",
        "parking_amount",
        "processing_fee",
        "tax_fee",
        "event_user_id",
        "checkout_license_plate",
        "checkout_remark",
        "car_model",
        "checkout_mode",
        "payment_by",
        "is_offline_payment",
        "permit_request_id",
        "customer_data",
        "additional_fee",
        "is_vehicle_oversize",
        "surcharge_fee",
        "oversize_fee"
    ];

    protected $hidden = [
        'created_at',
        'updated_at'
    ];

    public static $searchFields = [
        'ticket_number'
    ];

    protected $appends = ['comment', 'attendent_name', 'is_reopen', 'length_in_days', 'is_expired', 'validate_amount', 'base_amount', 'validate_url', 'encrypt_ticket_number',  'hourly_promocode_used_hours', 'main_ticket', 'total_refund_amount']; #pims-13173 
    // 'lot_facility',

    public static $validateReleaseRefundRequest = [
        'ticket_number'       => 'required',
        'refund_amount'       => 'required',
        'refund_type'         => 'required',
        'refund_remarks'      => 'required',
    ];
    /**
     * Get the facility for this reservation
     *
     * @return [type] [description]
     */
    public function facility()
    {
        return $this->belongsTo('App\Models\Facility');
    }

    /**
     * Get the transaction for this reservation
     *
     * @return [type] [description]
     */
    public function transaction()
    {
        return $this->belongsTo('App\Models\AuthorizeNetTransaction', 'anet_transaction_id')
            ->select(['id', 'total', 'anet_type_id', 'anet_status_id', 'ref_id', 'payment_last_four', 'card_type', 'expiration', 'created_at', 'auth_code', 'anet_trans_id', 'method', 'maskedCardNumber']);
    }




    /**
     * Load relations needed for front end
     */
    public function withRelations()
    {
        return $this->load('facility', 'facility.geolocations', 'facility.photos', 'transaction', 'user');
    }

    public function neighborhood()
    {
        return $this->belongsTo('App\Models\Reservation', 'user_id');
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id');
    }

    public function reservation()
    {
        return $this->belongsTo('App\Models\Reservation', 'id');
    }

    public function userPass()
    {
        return $this->belongsTo('App\Models\UserPass', 'user_pass_id');
    }

    public function citation()
    {
        return $this->hasOne('App\Models\ParkEngage\TicketCitation', 'ticket_id');
    }

    public function overstay()
    {
        return $this->hasMany('App\Models\OverstayTicket', 'ticket_id');
    }

    public function getCommentAttribute()
    {
        return $this->remark;
    }

    public function getLengthInDaysAttribute()
    {
        return $this->length > 0 ? ceil($this->length / 24) : 0;
    }

    /****** pims-13713 ****/
    public function getTotalRefundAmountAttribute()
    { # sprintf("%.2f",
        if (!is_null($this->refund_amount) && !is_null($this->release_amount)) {
            //return number_format($this->refund_amount + $this->release_amount, 2);
            return sprintf("%.2f", ($this->refund_amount + $this->release_amount));
        } else if (!is_null($this->refund_amount) && is_null($this->release_amount)) {
            //  return number_format($this->refund_amount, 2);
            return sprintf("%.2f", $this->refund_amount);
        } else if (is_null($this->refund_amount) && is_null($this->release_amount)) {
            // return number_format($this->release_amount, 2);
            return sprintf("%.2f", $this->release_amount);
        } else {
            return NULL;
        }
    }

    public function getisReopenAttribute()
    {
        if ($this->closed_date != '') {
            $closed_date = date("Y-m-d", strtotime($this->closed_date));
            $date = Carbon::parse($closed_date);
            $now = Carbon::now()->toDateString();
            if ($date->diffInDays($now) > 3) {
                return 0;
            } else {
                return 1;
            }
        } else {
            return 0;
        }
    }

    public function event()
    {
        return $this->belongsTo('App\Models\Event', 'event_id');
    }

    public function gates()
    {
        return $this->belongsTo('App\Models\ParkEngage\Gate', 'facility_id');
    }

    public function vehicleData()
    {

        return $this->belongsTo('App\Models\ParkEngage\Vehicle', 'vehicle_id', 'id')->withTrashed();
    }

    public function getisExpiredAttribute()
    {
        // change in pave issue fixed and prevent data in ticket table  : 10-09-2023 : Vijay
        // if this is not null that means session close by User

        // dd($this->stop_parking_time);
        // Log::info('getisExpiredAttribute *********1111111 ' . $this->ticket_number);
        if ($this->stop_parking_time) {
            return 1;
        } else {
            if ($this->is_extended == '1') {
                $ticketExtends = $this->ticketExtend;
                $extendedCheckinTime =  $this->checkout_time;
                foreach ($ticketExtends as $key => $ticketExtend) {
                    $extendedCheckinTime = $ticketExtend->checkout_time;
                }
                if ($extendedCheckinTime != '') {
                    $checkoutTime = date("Y-m-d H:i:s", strtotime($extendedCheckinTime));
                    $now = date("Y-m-d H:i:s");
                    if (strtotime($checkoutTime) < strtotime($now)) {
                        return 1;
                    } else {
                        return 0;
                    }
                } else {
                    return 0;
                }
            } else {
                // Log::info('checkout_time ' . $this->checkout_time);
                // Log::info('is_checkout ' . $this->is_checkout);
                // if ($this->checkout_time != '' && $this->is_checkout == '1') {
                if (!empty($this->checkout_time)  && $this->is_checkout == '1') {

                    $facility = Facility::find($this->facility_id);
                    if ($facility->is_gated_facility == '1') {
                        return 1;
                    }
                    // Log::info('getisExpiredAttribute ' . $this->ticket_number);
                    $checkoutTime = date("Y-m-d H:i:s", strtotime($this->checkout_time));
                    $now = date("Y-m-d H:i:s");
                    Log::info('checkoutTime' . $checkoutTime);
                    Log::info('now' . $now);
                    if (strtotime($checkoutTime) < strtotime($now)) {
                        // Log::info('getisExpiredAttribute 111');
                        return 1;
                    } else {
                        // Log::info('getisExpiredAttribute 2222');
                        return 0;
                    }
                } else {
                    // Log::info('getisExpiredAttribute 33333');
                    return 0;
                }
            }
        }
    }

    public function getvalidateAmountAttribute()
    {
        return $this->paid_amount;
    }

    // 28-09-2023
    public function getBaseAmountAttribute()
    {
        $total = $this->grand_total == '' ? 0.00 : $this->grand_total;
        $processing_fee = $this->processing_fee == '' ? 0.00 : $this->processing_fee;
        $tax_fee = $this->tax_fee == '' ? 0.00 : $this->tax_fee;
        $credit_used = $this->credit_used == '' ? 0.00 : $this->credit_used;

        $baseAmount = (($total - $processing_fee) - $tax_fee) + $credit_used;
        if ($this->is_extended == '1') {
            $totalTax = 0;
            $processingFee = 0;
            $baseAmount = 0;
            $grand_total = 0;
            $extendTickets = $this->ticketExtend;
            foreach ($extendTickets as $key => $extendTicket) {
                $totalTax += floatval($extendTicket->tax_fee);
                $processingFee += floatval($extendTicket->processing_fee);
                $grand_total += floatval($extendTicket->grand_total);
            }
            $baseAmount = $grand_total - ($totalTax + $processingFee);
            return number_format($baseAmount, 2);
        } else if ($this->is_overstay == '1' || $this->overstay->count() > 0) {
            $totalTax = 0;
            $processingFee = 0;
            $baseAmount = 0;
            $grand_total = 0;
            $ticketOverstays = $this->overstay;
            $totalTax = floatval($this->tax_fee);
            $processingFee = floatval($this->processing_fee);
            foreach ($ticketOverstays as $key => $ticketOverstay) {

                $grand_total += floatval($ticketOverstay->grand_total);
            }
            $baseAmount = ($grand_total + $this->total) - ($totalTax + $processingFee);
            //$baseAmount = $this->total - ($totalTax + $processingFee);
            //dd($baseAmount);
            // dd($this->total, $this->ticket_number);
            return number_format($baseAmount, 2);
        } else {
            if ($this->parking_amount > 0) {
                return number_format($this->parking_amount, 2);
            } else {
                return number_format($baseAmount, 2);
            }
        }
    }

    public function ticketExtend()
    {
        //,'processing_fee'
        return $this->hasMany('App\Models\ParkEngage\TicketExtend', 'ticket_id')->with(['transaction'])->select(['id', 'ticket_id', 'ticket_number', 'length', 'total', 'grand_total', 'tax_fee', 'checkin_time', 'checkout_time', 'created_at', 'processing_fee', 'base_length', 'discount_amount', 'comment', 'additional_fee', 'surcharge_fee', 'anet_transaction_id', 'parking_amounts', 'validated_amount', 'payment_method', 'refund_release_status', 'new_parking_amount']);
    }

    public function getValidateUrlAttribute()
    {
        return config('parkengage.WEB_ADMIN_URL') . "/admin/tickets?ticket_number=" . $this->ticket_number;
    }

    public function getEncryptTicketNumberAttribute()
    {
        return  base64_encode($this->ticket_number);
    }

    function addZero($mints)
    {
        return $mints / 100;
    }

    public function getCheckOutCurrentTime($lengthInMints = false, $overstayCheckingTime = false)
    {
        // checkout without checkin
        if (empty($this->checkin_time) || $this->checkout_without_checkin == '1') {
            return '0';
        }

        if ($lengthInMints) {
            if ($overstayCheckingTime == false) {
                $checkinTime = Carbon::parse($this->checkin_time);
            } else {
                $checkinTime = Carbon::parse($overstayCheckingTime);
            }
            $nowTime = Carbon::parse('now');
            // $nowTime = Carbon::parse('2023-11-20 00:00:01'); //2023-11-16 00:07:35
            $diffInHours = $checkinTime->diffInRealHours($nowTime);
            $diffInMinutes = $checkinTime->diffInRealMinutes($nowTime);
            $diffInSeconds = $checkinTime->diffInSeconds($nowTime);
            if ($diffInMinutes < 60) {
                $diffInHours = $diffInMinutes / 100;
            }
            if ($diffInMinutes > 59) {
                if ($diffInHours > 0) {
                    $diffInMints = $diffInMinutes - ($diffInHours * 60);
                    $NewdiffInMints = $this->addZero($diffInMints);
                    $diffInHours = $diffInHours + $NewdiffInMints;
                } else {
                    $diffInHours = $this->addZero($diffInMinutes);
                }
            }
            if ($diffInSeconds < 60) {
                $diffInHours = .01;
            }
            return $diffInHours;
            // return $this->getSubscriptionEndDate($this->checkin_time);
        }
        return Carbon::parse('now')->format('Y-m-d H:i:s');
    }

    public function  getAttendentNameAttribute()
    {
        $user = '';
        if ($this->checkout_by != '') {
            $user =  User::where('id', $this->checkout_by)->select('id', 'name')->first();
            return $user['name'];
        } else {
            $user = null;
        }
    }
    public function  getCheckoutModeAttribute($checkout_mode)
    {
        $checkoutMode = '';
        if ($checkout_mode == '1') {
            $checkoutMode = 'Web';
        } elseif ($checkout_mode == '2') {
            $checkoutMode = 'SMS';
        } elseif ($checkout_mode == '3') {
            $checkoutMode = 'IM30';
        } elseif ($checkout_mode == '4') {
            $checkoutMode = 'LPR';
        } elseif ($checkout_mode == '5') {
            $checkoutMode = 'ADMIN';
        } elseif ($checkout_mode == '6') {
            $checkoutMode = 'android-App';
        } elseif ($checkout_mode == '7') {
            $checkoutMode = 'IOS-App';
        }
        return $checkoutMode;
    }

    public function getProcessingFee($processingFeeType = null, $skipfee = false)
    {
        $processingFee = 0;
        switch ($processingFeeType) {
            case (self::SKIP_FEE && $skipfee == false):
                $processingFee = '0.00';
                break;
            case (self::DRIVE_UP_PROCESSING_FEE):
                $processingFee = $this->facility->drive_up_processing_fee == '' ? 0.00 : $this->facility->drive_up_processing_fee;
                break;
            case (self::PROCESSING_FEE):
                $processingFee = $this->facility->processing_fee == '' ? 0.00 : $this->facility->processing_fee;
                break;
            case (self::CITATION_PROCESSING_FEE):
                $processingFee = $this->facility->citation_processing_fee == '' ? 0.00 : $this->facility->citation_processing_fee;
                break;
            default:
                $processingFee = $this->facility->drive_up_processing_fee == '' ? 0.00 : $this->facility->drive_up_processing_fee;
                break;
        }
        return $processingFee;
    }

    function getTaxRate($rate)
    {
        $tax_rate =  0;
        if ($this->facility->tax_rate_type == '0') {
            if ($this->facility->tax_rate > 0) {
                $tax_rate = $this->facility->tax_rate;
            }
        } else {
            if ($this->facility->tax_rate > 0) {
                $tax_rate = number_format((($rate['price'] * $this->facility->tax_rate) / 100), 2);
            }
        }
        return $tax_rate;
    }

    function getPercetageValidation($ticketPrice, $taxRate = 0)
    {
        $totalValidatedAmountAbailabledByUser       = $this->paid_amount;       // Already Taken by user.
        $max_validated_amount                       = $this->max_validated_amount;
        $remaingValidationAmount                    = ($max_validated_amount > $totalValidatedAmountAbailabledByUser) ? ($max_validated_amount - $totalValidatedAmountAbailabledByUser) : 0;
        $validatedAmount                            = sprintf("%.2f", (($ticketPrice * $this->paid_percentage) / 100));
        $validatedBaseAmount                        = sprintf("%.2f", ((($ticketPrice - $taxRate) * $this->paid_percentage) / 100));

        if ($remaingValidationAmount > $validatedAmount) {
            if (($max_validated_amount > 0) && $max_validated_amount != '' && $remaingValidationAmount > 0) {
                if ($validatedAmount <= $remaingValidationAmount) {
                    //dd($validatedAmount,$remaingValidationAmount,$ticketPrice,$validatedAmount);
                    $payableAmount = sprintf("%.2f", ($ticketPrice - $validatedBaseAmount));
                    //	$validatedAmount = $validatedBaseAmount;
                } else {
                    $payableAmount = sprintf("%.2f", ($ticketPrice - $max_validated_amount));
                    $validatedAmount = $max_validated_amount;
                }
            } else {
                $payableAmount = sprintf("%.2f", ($ticketPrice - $validatedAmount));
            }
        } else {
            // Vijay : 11-12-2024
            if ($remaingValidationAmount > 0 && $ticketPrice >= $remaingValidationAmount) {
                $payableAmount      =  $ticketPrice - $remaingValidationAmount;
                $validatedAmount    = $remaingValidationAmount;
            } else {
                $payableAmount      = $ticketPrice;
                $validatedAmount    = 0;
            }
            return ['payableAmount' => $payableAmount, 'validatedAmount' => $validatedAmount, 'validatedBaseAmount' => $validatedBaseAmount];
        }
        //	 dd(['payableAmount' => $payableAmount, 'validatedAmount' => $validatedAmount, 'validatedBaseAmount' => $validatedBaseAmount]);
        return ['payableAmount' => $payableAmount, 'validatedAmount' => $validatedAmount, 'validatedBaseAmount' => $validatedBaseAmount];
    }

    function backup_11_12_2024getPercetageValidation($ticketPrice, $taxRate)
    {
        $validatedAmount = number_format((($ticketPrice * $this->paid_percentage) / 100), 2);

        if (($this->max_validated_amount > 0) && $this->max_validated_amount != '') {
            if ($validatedAmount <= $this->max_validated_amount) {
                $payableAmount = number_format($ticketPrice - $validatedAmount, 2);
            } else {
                $payableAmount = number_format($ticketPrice - $this->max_validated_amount, 2);
                $validatedAmount = $this->max_validated_amount;
            }
        } else {
            $payableAmount = number_format($ticketPrice - $validatedAmount, 2);
        }
        return ['payableAmount' => $payableAmount, 'validatedAmount' => $validatedAmount];
    }

    public function getLengthInMints($length)
    {
        $explode = explode(".", $length);
        $diffInHours = $explode[0];
        $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;
        if ($diffInMinutes > 0) {
            $diffInMints =  ($diffInHours * 60) + $diffInMinutes;
        } else {
            $diffInMints = $diffInHours * 60;
        }
        return $diffInMints;
    }

    public function getLengthInHours($length)
    {
        $diffInHours = 0;
        if ($length > 60) {
            $lengthInhours = number_format($length / 60, 2);
            $explode = explode(".", $lengthInhours);
            $diffInHours = isset($explode[0]) ? $explode[0] : intval($lengthInhours);
            $diffInMinutes = isset($explode[1]) ? $explode[1] : 0;
            if ($diffInMinutes > 0) {
                $getRemaingmints = $length - ($diffInHours * 60);
                // dd($getRemaingmints);    
                $diffInMints =  ($diffInMinutes * 60) / 100;
                // $diffInHours = $diffInHours . '.' . $diffInMints;    
                $diffInHours = $diffInHours . '.' . $getRemaingmints;
            }
            // dd($diffInHours, $diffInMinutes);    
            return number_format($diffInHours, 2);
        } else {
            $diffInHours =  '0.' . $length;
        }
        return $diffInHours;
    }

    // overnight userfull
    protected function timestampToCarbon(Carbon $initial, $time)
    {
        $times = explode(':', $time);

        $hour = $times[0] ?? 0;
        $minute = $times[1] ?? 0;
        $second = $times[2] ?? 0;

        return $initial->copy()->hour($hour)->minute($minute)->second($second);
    }

    function isOvernightOrNormalExit($ticket = false)
    {
        $exitTime = Carbon::parse('now');
        // $exitTime = Carbon::parse('2023-11-22 06:04:01'); ///2023-11-16 00:07:35
        $overnightFallingTime = FacilityOvernightDuration::where(['rate_category_id' => '43', 'active' => '1', 'partner_id' => $this->facility->owner_id])->first();
        if ($overnightFallingTime) {
            $OverStratTime = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnigth_start_time);
            $OverEndTime = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnight_end_time);
            $entryExitBetweenOvernight =  $exitTime->between($OverStratTime, $OverEndTime);
            // $exitIsGreatorToStartTime = $exitTime->gte($OverStratTime);
            // $exitIslowerToEndTime = $exitTime->lte($OverEndTime);
            $ticketPaymentDate = Carbon::parse($this->payment_date);
            $paymentStatus = $ticketPaymentDate->between($OverStratTime, $OverEndTime);
            // dd('payment ', $paymentStatus);
            // dd($OverStratTime, $OverEndTime, $exitIsGreatorToStartTime, $exitIslowerToEndTime);

            // check Overstay and payment for normal and overstay
            Log::info("isOvernightOrNormalExit Ticket Number : " . $this->id);
            $OverstayTicket = OverstayTicket::where(['ticket_id' => $this->id])->orderBy('id', 'DESC')->first();
            Log::info("Overstay Data : " . json_encode($OverstayTicket));
            Log::info("exitIslowerToEndTime exitIslowerToEndTime : {$entryExitBetweenOvernight}");
            if ($OverstayTicket) {
                //  either Reservation or Normal Drive up.
                $overstayPaymentDate = Carbon::parse($OverstayTicket->payment_date);
                $paymentStatus = $overstayPaymentDate->between($OverStratTime, $OverEndTime);
                if ($paymentStatus) {
                    $this->overnightOverstayPaymentFlag = true;
                }
                if ($entryExitBetweenOvernight && $paymentStatus) {
                    Log::info("isOvernightOrNormal Overstay 111 ");
                    return true;
                } else {
                    Log::info("isOvernightOrNormal Overstay 22 ");
                    return false;
                }
            } else {
                if ($this->reservation_id != '') {
                    // checkin against reservation 
                    if ($entryExitBetweenOvernight && $paymentStatus) {
                        Log::info("isOvernightOrNormal reservation_id  111 ");
                        return true;
                    } else {
                        Log::info("isOvernightOrNormal reservation_id 22 ");
                        return false;
                    }
                } else {
                    // drive up
                    if ($entryExitBetweenOvernight && $paymentStatus) {
                        Log::info("isOvernightOrNormal 111 ");
                        return true;
                    } else {
                        Log::info("isOvernightOrNormal 22 ");
                        return false;
                    }
                }
            }
        }
        return false;
    }

    function getOverStayAmount($estimatedCheckoutIme, $overNightPaidFlag = false)
    {
        $overstayCheckinTme = $estimatedCheckoutIme;
        // $overnightChargeOrSkip = false;
        Log::info("getOverStayAmount estimatedCheckoutIme {$estimatedCheckoutIme}");
        $diffInHours = $this->getCheckOutCurrentTime(true, $overstayCheckinTme);
        // $diffInHours = '1.03';   
        Log::info("getOverStayAmount Amount Length {$diffInHours}");
        if ($this->facility->is_hourly_rate == '1' || $this->facility->is_hourly_rate == 1) {
            Log::info("getOverStayAmount 1111111 {$diffInHours}");
            $rate = $this->facility->rateForReservationByPassRateEngine($overstayCheckinTme, $diffInHours, false, true, null, false, false, '0', 0);
        } else {
            // To handel Overnight OverStay Payment this pice of implemented 

            $overnightFallingTime = FacilityOvernightDuration::where(['rate_category_id' => '43', 'active' => '1', 'partner_id' => $this->facility->owner_id])->first();
            if ($overnightFallingTime && $this->reservation_id == '') {
                $exitTime = Carbon::parse('now');
                // $exitTime = Carbon::parse('2023-11-20 00:00:01'); //2023-11-16 00:07:35

                $arrivalTime = Carbon::parse($this->checkin_time);
                $payment_date = Carbon::parse($this->payment_date);
                // dd($this->payment_date, $payment_date);
                $arrivalToMidnight = $this->timestampToCarbon($arrivalTime, '23:59:59');
                $nextDayStartTime = Carbon::parse($arrivalToMidnight)->addSeconds(1);
                $paymnetIsBeforeOvernightStart = $payment_date->lte($arrivalToMidnight); // payment done before overnight start
                $paymentIsSameDay   = $arrivalTime->isSameDay($payment_date);
                $isSameDay          = $exitTime->isSameDay($arrivalTime);
                $OverStratTime      = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnigth_start_time);
                $OverEndTime        = $this->timestampToCarbon($exitTime, $overnightFallingTime->overnight_end_time);

                $exitIslowerToEndTime = $exitTime->lte($OverEndTime);
                // $exitIsgreaterToStartTime = $exitTime->gt($OverStratTime);
                // dd($exitTime, $OverEndTime, $exitIslowerToEndTime);
                Log::info("exitIslowerToEndTime exitIslowerToEndTime {$exitIslowerToEndTime}");
                // dd($arrivalTime, $payment_date, $paymentIsSameDay, $arrivalToMidnight, $paymnetIsBeforeOvernightStart, $isSameDay);
                // dd($arrivalTime, $payment_date, $paymentIsSameDay, $arrivalToMidnight, $paymnetIsBeforeOvernightStart, $isSameDay);
                if (strtotime($overstayCheckinTme) >= strtotime($this->payment_date)) {
                    $overstayCheckinTime = date('Y-m-d H:i:s', strtotime($overstayCheckinTme));
                } else {
                    $overstayCheckinTime = date('Y-m-d H:i:s', strtotime($this->payment_date));
                }

                if ($paymentIsSameDay && $paymnetIsBeforeOvernightStart && $isSameDay == false) {
                    // $overnightChargeOrSkip == true; 
                    // $overstayCheckinTime = Carbon::parse($OverStratTime)->addSeconds(1)->format('Y-m-d h:i:s');
                    // $overstayCheckinTime = date('Y-m-d H:i:s', strtotime($this->payment_date));
                    // $overstayCheckinTime = Carbon::parse($this->payment_date)->format('Y-m-d h:i:s');
                    $diffInHours = $this->getCheckOutCurrentTime(true, $overstayCheckinTime);
                    // dd($overstayCheckinTime, $this->payment_date, $diffInHours);
                    Log::info("return exitIslowerToEndTime exitIslowerToEndTime 111 : {$diffInHours}");
                    $rate = $this->facility->rateForReservationOnMarker($overstayCheckinTime, $diffInHours, false, true,  false, true, false, 0, 0, true);
                } else if ($exitIslowerToEndTime) {
                    Log::info("return exitIslowerToEndTime exitIslowerToEndTime 22222 :  {$exitIslowerToEndTime}");
                    // now check payment date is in same day or next day 
                    // Exit in overnight 
                    // 1. payment date is same day as exit date and less than h exit time 
                    // 2.
                    $paymentIsSameDay   = $OverEndTime->isSameDay($payment_date);
                    // dd($OverEndTime, $payment_date, $paymentIsSameDay);
                    // dd(strtotime($payment_date) < strtotime($OverEndTime));
                    if (strtotime($payment_date) < strtotime($OverEndTime) && $paymentIsSameDay) {
                        $rate['price'] = 0;
                    } else {
                        // $overstayCheckinTime = date('Y-m-d H:i:s', strtotime($this->payment_date));
                        $diffInHours = $this->getCheckOutCurrentTime(true, $overstayCheckinTime);
                        $rate = $this->facility->rateForReservationOnMarker($overstayCheckinTime, $diffInHours, false, true,  false, true, false, 0, 0, true);
                    }

                    // return $rate;
                } else {
                    // $overstayCheckinTime = Carbon::parse($OverEndTime)->format('Y-m-d h:i:s');
                    // $overstayCheckinTime = date('Y-m-d H:i:s', strtotime($this->payment_date));
                    $diffInHours = $this->getCheckOutCurrentTime(true, $overstayCheckinTime);
                    // dd($overstayCheckinTime, $diffInHours);
                    Log::info("return exitIslowerToEndTime exitIslowerToEndTime 111222 : {$diffInHours}");

                    $rate = $this->facility->rateForReservationOnMarker($overstayCheckinTime, $diffInHours, false, true,  false, true, false, 0, 0);
                    // Fourth possion is true for Overstay  
                }
            } else {
                // sport hero case also goes here or Rerservation overstay 
                Log::info("getOverStayAmount 222 :  {$diffInHours}");
                Log::info("getOverStayAmount thirdPartyOncePaid :  {$overNightPaidFlag}");
                // dd($overstayCheckinTme, $this->isOvernightOrNormalExit());
                if ($this->isOvernightOrNormalExit() && $overNightPaidFlag) {
                    $rate['price'] = 0;
                } else {
                    // dd($overNightPaidFlag, $this->overnightOverstayPaymentFlag);
                    if ($overNightPaidFlag) {
                        if ($this->overnightOverstayPaymentFlag) {
                            $rate = $this->facility->rateForReservationOnMarker($overstayCheckinTme, $diffInHours, false, true,  false, true, false, 0, 0, false, true);
                        } else {
                            $rate = $this->facility->rateForReservationOnMarker($overstayCheckinTme, $diffInHours, false, true,  false, true, false, 0, 0);
                        }
                    } else {
                        $rate = $this->facility->rateForReservationOnMarker($overstayCheckinTme, $diffInHours, false, true,  false, true, false, 0, 0, true);
                    }
                }
            }

            // Log::info("getOverStayAmount 222 {$diffInHours}");
            // $rate = $this->facility->rateForReservationOnMarker($overstayCheckinTme, $diffInHours, false, false,  false, true, false, 0, 0);
        }
        Log::info("Final Oversaty price is :  " . $rate['price']);
        return  $rate;
    }

    // $paidAmount =  $this->grand_total > 0 ? $this->grand_total : $ticketPrice;  
    /*  
        amount_paid 
        paid_amount 
        tax_rate    
        processing_fee  
        payable_amount  
        parking_amount  
        discount_amount            // this is related to admin checkout new feature     
        total 
    */
    // : 29-11-2023 : Vijay 
    function getRate($arrivalTime, $overstay = false, $overnightOverstayPaid = false)
    {
        $isMember = 0;
        if ($overstay) {
            $diffInHours = $this->getCheckOutCurrentTime(true, $arrivalTime);
        } else {
            $diffInHours = $this->getCheckOutCurrentTime(true);
        }
        if ($this->facility->is_hourly_rate == '1' || $this->facility->is_hourly_rate == 1) {
            $this->log->info("ticket model rate for world port 111");
            $rate = $this->facility->rateForReservationByPassRateEngine($arrivalTime, $diffInHours, false, false, null, false, false, '0', $isMember);
        } else {
            $this->log->info("ticket model rate for townsend 222");
            $rate = $this->facility->rateForReservationOnMarker($arrivalTime, $diffInHours, false, false, false, true, false, 0, $isMember);
        }

        return $rate;
    }

    function priceBreakUp($rate, $total = 0)
    {
        Log::info("Ticket Price Break up Start with total amount {$total} Ticket Number {$this->ticket_number}");
        // $this->printTicketModelLog("Ticket Price Break up Start with total amount {$total} Ticket Number {$this->ticket_number}");
        Log::info(json_encode($rate['price']));

        $newPrice = '0.00';
        $totalPrice = $total;
        $parkingAmount = $rate['price'];
        $taxRate = $this->facility->getTaxRate($rate);          // to get tax price
        $processingFee = $this->facility->getProcessingFee('0');   // to get prcessing free channel wise need to
        $diffInHours = $this->getCheckOutCurrentTime(true);
        $ticketPrice = ($rate['price'] + $processingFee + $taxRate);
        // $currentTime = date('Y-m-d H:i:s');
        $currentTime = date('Y-m-d H:i:s');
        Log::info("Ticket Price Break up        : " . $total);
        Log::info("Ticket Price ticketPrice     : " . $ticketPrice);
        Log::info("Ticket Price processingFee   : " . $processingFee);
        Log::info("Ticket Price taxRate         : " . $taxRate);
        Log::info("Ticket Price currentTime     : " . $currentTime);

        if ($this->grand_total > 0 && $totalPrice > 0) {
            Log::info("Grand Total case 1  : " . $this->grand_total);
            $grandTotal = ($this->grand_total + $totalPrice);
        } else {
            $grandTotal = $this->grand_total > 0 ? $this->grand_total : ($totalPrice > 0 ? $totalPrice : $ticketPrice);
            Log::info("Grand Total case 2  : " . $this->grand_total);
        }

        // New Condition added to handel LPR checkout case 
        if ($this->anet_transaction_id == '' && $this->is_offline_payment == '0' && $this->paid_type == '9') {
            $this->grand_total = 0;
            $grandTotal = $ticketPrice;
            //$grandTotal = $this->grand_total;
            Log::info("Grand Total case 3  : " . $this->grand_total);
            Log::info("Grand Total case 3  grandTotal : " . $grandTotal);
        }

        $arrival_time = Carbon::createFromFormat('Y-m-d H:i:s', !empty($this->checkin_time) ? $this->checkin_time : date('Y-m-d H:i:s'));
        $from = Carbon::createFromFormat('Y-m-d H:i:s', date('Y-m-d H:i:s'));

        $overstay = $this->overstay;
        $latestEstimatedCheckout =  !empty($this->estimated_checkout) ? $this->estimated_checkout : $this->payment_date;
        $latestpaymentTime =  !empty($this->payment_date) ? $this->payment_date : $this->estimated_checkout;
        $amountPaid = 0;
        $overStayAmount = 0;
        $ticketParkingAmount = 0;
        $ticketTotalAmount = 0;

        $overstayDiscountAmount =  $overstayParkingAmount = $overstayTaxFee = $overstayProcessingFee = $overstayTotalAmount = 0;
        if ($overstay->count() > 0) {
            foreach ($overstay as $key => $value) {
                $overstayDiscountAmount += $value->discount_amount;
                $amountPaid             += $value->grand_total;
                $overstayTotalAmount    += $value->total;
                $overstayParkingAmount  += $value->total - ($value->tax_fee + $value->processing_fee);
                $overstayTaxFee         += $value->tax_fee;
                $overstayProcessingFee  += $value->processing_fee;
                $latestEstimatedCheckout = !empty($value->estimated_checkout) ? $value->estimated_checkout : $value->payment_date;
                $latestpaymentTime =  $value->payment_date;
            }
        }
        $ticketParkingAmount    = ($overstayParkingAmount + $this->parking_amount);
        $ticketTotalAmount      = ($overstayTotalAmount + $this->total);

        if (!empty($this->anet_transaction_id) && $this->anet_transaction_id != '' || $this->is_offline_payment != '0') {
            $diffInHours = $this->getCheckOutCurrentTime(true, $latestpaymentTime);
        }

        Log::info("Final Diff in Hours  :   " . $diffInHours);
        Log::info("Estimated time           " . $latestEstimatedCheckout);
        Log::info("Payment date time    :   " . $latestpaymentTime);
        $returnData = [];
        $returnData['length'] = $diffInHours;
        $returnData['overstay_amount']  = '0.00';
        $returnData['discount_amount']  = '0.00';
        $returnData['overstay']         = '0';
        $returnData['amount_paid']      = '0.00';

        $thisIsOverstay = $this->is_overstay;
        Log::info("is_overstay:" . $thisIsOverstay);
        if ($this->is_checkout == '0') {
            Log::info("Ticket is not checkout  ");
            //permit checkin case
            if ($this->permit_request_id != '') {  // for permit specific 
                $returnData['total']            = isset($this->grand_total) ? $this->grand_total : "0.00";
                $returnData['length']           = $diffInHours;
                $returnData['parking_amount']   = $this->parking_amount;
                $returnData['paid_amount']      = $newPrice;
                $returnData['grand_total']      = $this->grand_total;
                $returnData['payable_amount']   = $newPrice;
                $returnData['processing_fee']   = $this->processing_fee;
                $returnData['tax_rate']         = $this->tax_fee;
                $returnData['amount_paid']      = $this->grand_total;
                return $returnData;
            }

            //if checkin through reservation or pass  
            // comment VIjay : Not workin in case of overstay so comment this code   
            if ($this->event_id != '' && $this->facility->is_event_overstay == '0') {  // for mapco specific 
                if ($this->paid_amount > '0.00') {
                    $paid_amount = $this->paid_amount;
                    $grand_total = $this->grand_total - $paid_amount;
                } else {
                    $paid_amount = $newPrice;
                    $grand_total = $this->grand_total;
                }

                $returnData['total']            = isset($this->grand_total) ? $this->grand_total : "0.00";
                $returnData['length']           = $diffInHours;
                $returnData['parking_amount']   = $this->parking_amount;
                $returnData['paid_amount']      = $paid_amount;
                $returnData['grand_total']      = $this->grand_total;
                $returnData['payable_amount']   = $newPrice;
                $returnData['processing_fee']   = $this->processing_fee;
                $returnData['tax_rate']         = $this->tax_fee;
                $returnData['amount_paid']      = $grand_total;
                return $returnData;
            }
            // new validation for townsend and wordpord
            Log::info("Break return for Reservation Oversaty case start ");
            // dd(!empty($latestEstimatedCheckout) && strtotime($latestEstimatedCheckout) <= strtotime($currentTime), $this->is_overstay);
            // if Block is oversaty block with drive tickets  
            if (!empty($latestEstimatedCheckout) && strtotime($latestEstimatedCheckout) <= strtotime($currentTime) && $this->reservation_id == '') {
                Log::info("Break return for Reservation Oversaty case 222222222222222222");
                if ($this->is_overstay == '1') {
                    Log::info("Break return for Reservation Oversaty case 15 ESTIMATE 111111--222  {$latestEstimatedCheckout} CT TIME {$currentTime}");
                    $overStay = $this->getOverStayAmount($latestEstimatedCheckout);
                    Log::info("Break return for Reservation Oversaty case Price  :  " . $overStay['price']);
                    // $returnData['total'] = $this->total;
                    $returnData['total']            = $overStay['price'] > 0 ? ($ticketParkingAmount + $overStay['price'] + $taxRate) : $overStay['price'];
                    $returnData['length']           = $this->getCheckOutCurrentTime(true, $latestEstimatedCheckout);
                    $returnData['parking_amount']   = $overStay['price'] + ($ticketParkingAmount);
                    $returnData['paid_amount']      = $this->paid_amount;
                    $returnData['grand_total']      = $this->discount_amount > 0 ? $this->grand_total + $this->discount_amount : $this->grand_total;
                    $returnData['payable_amount']   = $overStay['price'] > 0 ? ($overStay['price'] + $taxRate) : $overStay['price'];
                    $returnData['processing_fee']   = $this->processing_fee;
                    $returnData['tax_rate']         = $overStay['price'] > 0 ? $taxRate : 0;
                    $returnData['tax_fee']          = $overStay['price'] > 0 ? $taxRate : 0;
                    $returnData['discount_amount']  = $this->discount_amount + $overstayDiscountAmount;
                    $returnData['overstay_amount']  = $overStay['price'];
                    $returnData['overstay'] = '1';
                    $returnData['amount_paid']      = $this->grand_total + $amountPaid;
                    Log::info("PRINT RETURN   " . json_encode($returnData));
                    return $returnData;
                } else {
                    if (!empty($latestEstimatedCheckout) && strtotime($latestEstimatedCheckout) <= strtotime($currentTime)) {
                        Log::info("Break return for Reservation Oversaty case 15 ESTIMATE ESLE  {$latestEstimatedCheckout} CT TIME {$currentTime}");
                        $overStay = $this->getOverStayAmount($latestEstimatedCheckout);
                        Log::info("Break return for Reservation Oversaty case Price    " . $overStay['price']);
                        // $returnData['total'] = $this->total;
                        $returnData['total']            = $overStay['price'] > 0 ? ($ticketParkingAmount + $overStay['price'] + $taxRate) : $overStay['price'];
                        $returnData['length']           = $this->getCheckOutCurrentTime(true, $latestEstimatedCheckout);
                        $returnData['parking_amount']   = $overStay['price'] + ($ticketParkingAmount);
                        $returnData['paid_amount']      = $this->paid_amount;
                        $returnData['grand_total']      = $this->discount_amount > 0 ? $this->grand_total + $this->discount_amount : $this->grand_total;
                        $returnData['payable_amount']   = $overStay['price'] > 0 ? ($overStay['price'] + $taxRate) : $overStay['price'];
                        $returnData['processing_fee']   = '0.00';
                        $returnData['tax_rate']         = $overStay['price'] > 0 ? $taxRate : 0;
                        $returnData['tax_fee']          = $overStay['price'] > 0 ? $taxRate : 0;
                        $returnData['discount_amount']  = $this->discount_amount + $overstayDiscountAmount;
                        $returnData['overstay_amount']  = $overStay['price'];
                        $returnData['overstay']         = '1';
                        $returnData['amount_paid']      = $this->grand_total + $amountPaid;
                        Log::info("PRINT RETURN 11111111  " . json_encode($returnData));
                        return $returnData;
                    }
                }
            } else {
                /// this for rest  neee to remove other case because update codein above section   
                if (($this->reservation_id != '' || $this->user_pass_id != '') || ($this->anet_transaction_id != '')) {
                    //if (($this->event_id != '' && $this->anet_transaction_id != '')) {
                    // to check third party reservation 
                    if (isset($this->reservation->thirdparty_integration_id) && $this->reservation->thirdparty_integration_id) {
                        $returnData['total'] = $this->total;
                    } else {
                        $returnData['total'] = isset($this->reservation->total) ? $this->reservation->total : "0.00";
                    }
                    $returnData['length'] = $diffInHours;
                    $returnData['parking_amount'] = $newPrice;
                    $returnData['paid_amount'] = $newPrice;
                    $returnData['grand_total'] = $newPrice;
                    $returnData['payable_amount'] = $newPrice;
                    $returnData['processing_fee'] = $newPrice;
                    $returnData['tax_rate'] = $newPrice;

                    // need to implement overstay here : vijay : 18-09-2023
                    // Vijay || (isset($this->reservation->thirdparty_integration_id) && $this->reservation->thirdparty_integration_id)
                    // if (isset($this->reservation->thirdparty_integration_id) && $this->reservation->thirdparty_integration_id) {
                    Log::info("Spot Hero Brice Break up and for reservation also  ");
                    if ($this->is_overstay == '1') {
                        Log::info("Break return for Reservation Oversaty case 15 ESTIMATE 111--111  {$latestEstimatedCheckout} CT TIME {$currentTime}");
                        if (!empty($latestEstimatedCheckout) && strtotime($latestEstimatedCheckout) <= strtotime($currentTime)) {
                            if ($amountPaid > 0) {
                                $overStay = $this->getOverStayAmount($latestpaymentTime, true);
                            } else {
                                $overStay = $this->getOverStayAmount($latestpaymentTime);
                            }
                            Log::info("Break return for Reservation Oversaty case Price    " . $overStay['price']);
                            $returnData['total']            = $this->total;
                            $returnData['length']           = $this->getCheckOutCurrentTime(true, $latestpaymentTime);
                            $returnData['parking_amount']   = $newPrice;
                            $returnData['paid_amount']      = $this->paid_amount;
                            $returnData['grand_total']      = $this->discount_amount > 0 ? $this->grand_total + $this->discount_amount : $this->grand_total;
                            $returnData['payable_amount']   = $overStay['price'] > 0 ? $taxRate : 0;
                            $returnData['processing_fee']   = $this->processing_fee;
                            $returnData['tax_rate']         = $this->tax_fee;
                            $returnData['tax_fee']          = $overStay['price'] > 0 ? $taxRate : 0;
                            $returnData['discount_amount']  = $this->discount_amount + $overstayDiscountAmount;
                            $returnData['overstay_amount']  = $overStay['price'];
                            $returnData['amount_paid']      = $this->grand_total + $amountPaid;
                        } else {

                            Log::info("Break return for Reservation Oversaty case Price 000    ");
                            $returnData['total']            = $this->total;
                            $returnData['length']           = $newPrice;
                            $returnData['parking_amount']   = $this->parking_amount + $overstayParkingAmount;
                            $returnData['paid_amount']      = $this->paid_amount;
                            $returnData['grand_total']      = $this->grand_total;
                            $returnData['payable_amount']   = $newPrice;
                            $returnData['processing_fee']   = $this->processing_fee + $overstayProcessingFee;
                            $returnData['tax_rate']         = $this->tax_fee + $overstayTaxFee;
                            $returnData['tax_fee']          = $this->tax_fee + $overstayTaxFee;
                            $returnData['discount_amount']  = $this->discount_amount + $overstayDiscountAmount;
                            $returnData['overstay_amount']  = $newPrice;
                            $returnData['amount_paid']      = $this->grand_total + $amountPaid;
                        }

                        Log::info("Spot hero reservation PRINT RETURN   " . json_encode($returnData));
                        return $returnData;
                    } else {
                        if (!empty($latestEstimatedCheckout) && strtotime($latestEstimatedCheckout) <= strtotime($currentTime)) {
                            Log::info("Break return for Reservation Oversaty case 156 ESTIMATE  {$latestpaymentTime} CT TIME {$currentTime}");
                            if ($amountPaid > 0) {
                                $overStay = $this->getOverStayAmount($latestpaymentTime, true);
                            } else {
                                $overStay = $this->getOverStayAmount($latestpaymentTime);
                            }

                            Log::info("Break return for Reservation Oversaty case Price 33    " . $overStay['price']);
                            $returnData['total']            = $this->total;
                            $returnData['length']           = $this->getCheckOutCurrentTime(true, $latestEstimatedCheckout);;
                            $returnData['parking_amount']   = $newPrice;
                            $returnData['paid_amount']      = $this->paid_amount;
                            $returnData['grand_total']      = $this->discount_amount > 0 ? $this->grand_total + $this->discount_amount : $this->grand_total;
                            $returnData['payable_amount']   = $overStay['price'] + $taxRate;
                            $returnData['processing_fee']   = $newPrice;
                            $returnData['tax_rate']         = $taxRate;
                            $returnData['tax_fee']          = $taxRate;
                            $returnData['discount_amount']  = $this->discount_amount + $overstayDiscountAmount;
                            $returnData['overstay_amount']  = $overStay['price'];
                            $returnData['amount_paid']      = $this->grand_total;
                        } else {
                            $returnData['total']            = $this->total;
                            $returnData['length']           = $newPrice;
                            $returnData['parking_amount']   = $this->parking_amount + $overstayParkingAmount;
                            $returnData['paid_amount']      = $this->paid_amount;
                            $returnData['grand_total']      = $this->grand_total;
                            $returnData['payable_amount']   = $newPrice;
                            $returnData['processing_fee']   = $this->processing_fee + $overstayProcessingFee;
                            $returnData['tax_rate']         = $this->tax_fee + $overstayTaxFee;
                            $returnData['tax_fee']          = $this->tax_fee + $overstayTaxFee;
                            $returnData['discount_amount']  = $this->discount_amount + $overstayDiscountAmount;
                            $returnData['amount_paid']      = $this->grand_total;
                        }
                        Log::info("Sport Hero Break up Response :   " . json_encode($returnData));
                        return $returnData;
                    }
                    // }  // !! Close Sport Hero Reservation...
                    // !!!
                }
            }


            if ($parkingAmount <= 0 && $this->anet_transaction_id == '') {
                $returnData['total'] = $newPrice;
                $returnData['length'] = $diffInHours;
                $returnData['parking_amount'] = $newPrice;
                $returnData['paid_amount'] = $newPrice;
                $returnData['grand_total'] = $newPrice;
                $returnData['payable_amount'] = $newPrice;
                $returnData['processing_fee'] = $newPrice;
                $returnData['tax_rate'] = $newPrice;
                return $returnData;
            }
            // !!!!!!!!!!!!!!!!!!!!!!!!  Vijay : 24-09-2023
            // Checked-In Case on the fly Calulation
            if ($this->paid_type == '0') {    // Full Validation case 
                if ($parkingAmount > 0) {
                    $returnData['total'] = $ticketPrice;
                    $returnData['parking_amount'] = $parkingAmount;
                    $returnData['paid_amount'] = $ticketPrice;
                    $returnData['processing_fee'] = $processingFee;
                    $returnData['tax_rate'] = $taxRate;
                } else {
                    $returnData['total'] = $newPrice;
                    $returnData['parking_amount'] = $newPrice;
                    $returnData['paid_amount'] = $newPrice;
                    $returnData['processing_fee'] = $newPrice;
                    $returnData['tax_rate'] = $newPrice;
                }
                $returnData['grand_total'] = $newPrice;
                $returnData['payable_amount'] = $newPrice;
            } else if ($this->paid_type == '1') { // Hours  
                // $paidDiffInHours = $this->getDiffInHour($this->paid_hour);
                $paidDiffInHours = $this->paid_hour;
                // dd($this->paid_hour, $paidDiffInHours, $diffInHours);
                // dd($diffInHours, $parkingAmount);

                if ($diffInHours >= $paidDiffInHours) {
                    $diff_in_hours = $diffInHours - $paidDiffInHours;

                    $hoursRate = $this->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, 0);
                    // dd($diff_in_hours, $diffInHours, $paidDiffInHours, $hoursRate['price'], $rate['price']);
                    $discountPrice = 0;
                    if ($hoursRate['price'] > 0) {
                        $discountPrice = ($rate['price'] - $hoursRate['price']);
                        $returnData['total'] = $ticketPrice;
                        $returnData['parking_amount'] = $parkingAmount;
                        $returnData['paid_amount'] = $discountPrice;
                        $returnData['length'] = $diff_in_hours;
                        $returnData['grand_total'] = ($ticketPrice - $discountPrice);
                        $returnData['payable_amount'] = ($ticketPrice - $discountPrice);
                        $returnData['processing_fee'] = $processingFee;
                        $returnData['tax_rate'] = $taxRate;
                    } else {
                        $returnData['total'] = $ticketPrice;
                        $returnData['parking_amount'] = $parkingAmount;
                        $returnData['paid_amount'] = $ticketPrice;
                        $returnData['length'] = $diff_in_hours;
                        $returnData['grand_total'] = '0.00';
                        $returnData['payable_amount'] = '0.00';
                        $returnData['processing_fee'] = $processingFee;
                        $returnData['tax_rate'] = '0.00';
                    }
                } else {
                    $returnData['total'] = $ticketPrice;
                    $returnData['parking_amount'] = $parkingAmount;
                    $returnData['paid_amount'] = $ticketPrice;
                    $returnData['processing_fee'] = $processingFee;
                    $returnData['tax_rate'] = $taxRate;
                    $returnData['grand_total'] = $newPrice;
                    $returnData['payable_amount'] = $newPrice;
                }
            } else if ($this->paid_type == '2') { // Amount
                $returnData['total']            = $ticketPrice;
                $returnData['parking_amount']   = $parkingAmount;
                $returnData['paid_amount']      = $this->paid_amount;
                $returnData['grand_total']      = ($ticketPrice - $this->paid_amount);
                $returnData['payable_amount']   = ($ticketPrice - $this->paid_amount) - ($this->grand_total);
                // $returnData['payable_amount'] = ($ticketPrice - $this->paid_amount) - ($this->grand_total);
                $returnData['processing_fee']   = $processingFee;
                $returnData['tax_rate']         = $taxRate;
            } else if ($this->paid_type == '3') { // Percentage
                $payableAmount = 0;
                $validatedAmount = 0;
                $grandTotal = 0;

                $perValidated  = $this->getPercetageValidation($ticketPrice, $taxRate);
                $payableAmount = $perValidated['payableAmount'];
                $validatedAmount = $perValidated['validatedAmount'];
                $grandTotal = $payableAmount;
                // check paid and grand total is any after payment
                if ($this->grand_total > 0) { // if any payment done then minus grand otal from payable 
                    $grandTotal =  $this->grand_total;
                    $payableAmount = $payableAmount - $this->grand_total;
                }
                $returnData['total'] = $ticketPrice;
                $returnData['parking_amount'] = $parkingAmount;
                $returnData['paid_amount'] = $validatedAmount;
                $returnData['grand_total'] = $grandTotal;
                $returnData['payable_amount'] = $payableAmount;
                $returnData['processing_fee'] = $processingFee;
                $returnData['tax_rate'] = $taxRate;
            } else {
                if ($parkingAmount > 0) {
                    // Not In Grace period Case
                    $returnData['total']            = $ticketPrice;
                    $returnData['length']           = '0';
                    $returnData['parking_amount']   = $parkingAmount;
                    $returnData['paid_amount']      = $newPrice;
                    $returnData['grand_total']      = $totalPrice;
                    $returnData['payable_amount']   = $grandTotal - ($this->grand_total + $this->paid_amount);
                    $returnData['processing_fee']   = $processingFee;
                    $returnData['tax_rate']         = $taxRate;
                    $returnData['tax_fee']          = $taxRate;
                    $returnData['amount_paid']      = $this->grand_total;
                } else {
                    // Grace period Case
                    $returnData['total'] = $newPrice;
                    $returnData['length'] = '0';
                    $returnData['parking_amount'] = $newPrice;
                    $returnData['paid_amount'] = $newPrice;
                    $returnData['grand_total'] = $newPrice;
                    $returnData['payable_amount'] = $newPrice;
                    $returnData['processing_fee'] = $newPrice;
                    $returnData['tax_rate'] = $newPrice;
                }
            }
            if (!empty($latestEstimatedCheckout) && strtotime($latestEstimatedCheckout) <= strtotime($currentTime)) {
                Log::info("Ticket is not checkout 11 ");
                $returnData['payable_amount'] = '0.00';
            }
            if ($returnData['payable_amount'] < 0) {  // to handel with -1.50 discount 
                Log::info("Ticket is not checkout 12 ");
                $returnData['payable_amount'] = '0.00';
            }
            // if Offline paymnet is done then all need to send 0 
            /*   Offline payment Flag value 
                1 => Cash, 2 => Saved Card, 3 =>New Card
             */
            if ($this->is_offline_payment != 0 || $this->is_offline_payment != '0') {
                // this case is when user in grace period 
                Log::info("Ticket is not checkout 13 ");
                if (!empty($latestEstimatedCheckout) && strtotime($latestEstimatedCheckout) >= strtotime($currentTime)) {
                    $returnData['total'] = $this->total;
                    $returnData['parking_amount'] = $this->parking_amount;
                    $returnData['paid_amount'] = $this->paid_amount;
                    $returnData['grand_total'] = $this->discount_amount > 0 ? $this->grand_total + $this->discount_amount : $this->grand_total;
                    $returnData['payable_amount'] = $newPrice;
                    $returnData['processing_fee'] = $this->processing_fee;
                    $returnData['tax_rate'] = $this->tax_fee;
                    $returnData['discount_amount'] = $this->discount_amount;
                } else if (!empty($latestEstimatedCheckout) && strtotime($latestEstimatedCheckout) <= strtotime($currentTime)) {
                    Log::info("Ticket is not checkout 15 " . $latestpaymentTime);
                    // this is the case of oversay and user paid some amonut but not checkout with offline payment
                    // $overStay = $this->getOverStayAmount($this->estimated_checkout);// change as we need to change to get price from estimated checkout to paymnet date
                    $overStay = $this->getOverStayAmount($latestpaymentTime);
                    Log::info("Ticket is not checkout 156 " . $overStay['price']);
                    // $rate = $overStay['rate'];
                    // $ticketPrice = $overStay['ticketPrice'];
                    $returnData['total'] = $this->total;
                    $returnData['parking_amount'] = $this->parking_amount;
                    $returnData['paid_amount'] = $this->paid_amount;
                    $returnData['grand_total'] = $this->discount_amount > 0 ? $this->grand_total + $this->discount_amount : $this->grand_total;
                    $returnData['payable_amount'] = $overStay['price'];
                    $returnData['processing_fee'] = $this->processing_fee;
                    $returnData['tax_rate'] = $this->tax_fee;
                    $returnData['discount_amount'] = $this->discount_amount;
                    $returnData['overstay_amount'] = $overStay['price'];
                } else {
                    Log::info("Ticket is not checkout 16 ");
                    $returnData['total'] = $ticketPrice;
                    $returnData['parking_amount'] = $parkingAmount;
                    $returnData['paid_amount'] = $newPrice;
                    $returnData['grand_total'] = $this->discount_amount > 0 ? $this->grand_total + $this->discount_amount : $this->grand_total;
                    $returnData['payable_amount'] = $newPrice;
                    $returnData['processing_fee'] = $processingFee;
                    $returnData['tax_rate'] = $taxRate;
                    $returnData['discount_amount'] = $this->discount_amount;
                }
                $returnData['currentTime'] = $currentTime;
                $returnData['estimated_checkout'] = $this->estimated_checkout;
                return $returnData;
            }

            // Overstay WithOut Offline payment checked
            //  
            Log::info("Ticket Price currentTime Before Overstay " . $currentTime);
            if (!empty($this->anet_transaction_id) && !empty($latestEstimatedCheckout) && strtotime($latestEstimatedCheckout) <= strtotime($currentTime)) {
                Log::info("Ticket is not checkout but in  oversaty ");
                // $latestEstimatedCheckout =  !empty($this->payment_date) ? $this->payment_date : $this->estimated_checkout;
                // $latestEstimatedCheckout =  $latestpaymentTime;
                Log::info("Ticket is not checkout oversaty ********* overStayPaymentTime " . $latestpaymentTime);
                Log::info("Ticket is not checkout oversaty ********* overestimatedCheckoutTime " . $latestEstimatedCheckout);
                $overStay = $this->getOverStayAmount($latestpaymentTime);

                if (strtotime($latestEstimatedCheckout) >= strtotime($currentTime)) {
                    Log::info("Ticket is not checkout oversaty 3333 Overstay but not ableb to pay ");
                    $returnData['total']            =  '0.00';
                    $returnData['parking_amount']   = $this->parking_amount;
                    $returnData['paid_amount']      = $this->paid_amount;
                    $returnData['grand_total']      = $this->grand_total;
                    $returnData['payable_amount']   = '0.00';
                    $returnData['processing_fee']   = '0.00';
                    $returnData['tax_rate']         = '0.00';
                    $returnData['discount_amount']  = $this->discount_amount;
                    $returnData['overstay_amount']  = '0.00';
                    $returnData['overstay']         = '1';
                    $returnData['amount_paid']      = $this->grand_total;
                    return $returnData;
                }
                if ($amountPaid > 0) {
                    Log::info("Ticket is not checkout oversaty 222222 " . $overStay['price']);
                    if ($overStay['price'] > 0) {
                        $returnData['total'] = $overStay['price'];
                        $returnData['parking_amount'] = $overStay['price'];
                        $returnData['paid_amount'] = $this->paid_amount;
                        $returnData['grand_total'] = $this->grand_total;
                        $returnData['payable_amount'] = $overStay['price'];
                        $returnData['processing_fee'] = '0.00';
                        $returnData['tax_rate'] = '0.00';
                        $returnData['discount_amount'] = $this->discount_amount;
                        $returnData['overstay_amount'] = $overStay['price'];
                        $returnData['overstay'] = '1';
                    } else {
                        $returnData['total'] =  ($this->total + $amountPaid);
                        $returnData['parking_amount'] = $this->parking_amount;
                        $returnData['paid_amount'] = $this->paid_amount;
                        $returnData['grand_total'] = $this->grand_total;
                        $returnData['payable_amount'] = '0.00';
                        $returnData['processing_fee'] = '0.00';
                        $returnData['tax_rate'] = '0.00';
                        $returnData['discount_amount'] = $this->discount_amount;
                        $returnData['overstay_amount'] = '0.00';
                        $returnData['overstay'] = '1';
                    }
                } else {
                    Log::info("Ticket is not checkout oversaty 22222 ");
                    $returnData['total'] = $overStay['price'];
                    $returnData['parking_amount'] = $overStay['price'];
                    $returnData['paid_amount'] = $this->paid_amount;
                    $returnData['grand_total'] = $this->grand_total;
                    $returnData['payable_amount'] = $overStay['price'];
                    $returnData['processing_fee'] = '0.00';
                    $returnData['tax_rate'] = '0.00';
                    $returnData['discount_amount'] = $this->discount_amount;
                    $returnData['overstay_amount'] = $overStay['price'];
                    $returnData['overstay'] = '1';
                }
            }
        } else {
            Log::info("Ticket is checked out  ");
            $returnData['total']            = $this->total;
            $returnData['parking_amount']   = $this->parking_amount;
            $returnData['paid_amount']      = $this->paid_amount  > 0 ? $this->paid_amount : '0.00';
            $returnData['grand_total']      = $this->grand_total;
            $returnData['length']           = $this->length;
            $returnData['payable_amount']   = $newPrice;
            $returnData['processing_fee']   = $this->processing_fee;
            $returnData['tax_rate']         = $this->tax_fee;
            $returnData['discount_amount']  = $this->discount_amount;
            $returnData['amount_paid']      =  $this->grand_total;
            if ($this->is_overstay == '1') {
                // $overStay = $this->getOverStayAmount($this->estimated_checkout);
                $overstay = $this->overstay;
                $amountPaid = 0;
                $overStayAmount = 0;
                $overstayParkingAmount = 0;
                $oversatayTaxAmount = 0;
                $oversatayProcessingFee = 0;

                if ($overstay->count() > 0) {
                    foreach ($overstay as $key => $value) {
                        $amountPaid += $value->grand_total;
                        // $overstayParkingAmount += $value->total - ($value->tax_fee + $value->processing_fee+$this->discount_amount);
                        $overstayParkingAmount      += $value->parking_amount;
                        $oversatayTaxAmount         += $value->tax_fee;
                        $oversatayProcessingFee     += $value->processing_fee;
                        // $overstayDiscountAmount     += $value->discount_amount;
                        // $overStayAmount += $value->grand_total;
                    }
                }

                $returnData['total']            = $ticketTotalAmount;
                $returnData['parking_amount']   = $this->parking_amount + $overstayParkingAmount;
                $returnData['paid_amount']      = $this->paid_amount;
                $returnData['grand_total']      = $this->grand_total;
                $returnData['payable_amount']   = $newPrice;
                $returnData['processing_fee']   = $this->processing_fee + $oversatayProcessingFee;
                $returnData['tax_rate']         = $this->tax_fee + $oversatayTaxAmount;
                $returnData['discount_amount']  = $this->discount_amount + $overstayDiscountAmount;
                $returnData['overstay_amount']  = $amountPaid;
                $returnData['amount_paid']      = $amountPaid + $this->grand_total;
            }
        }


        return $returnData;
    }

    public function getDiffInHour($length)
    {
        if ($length) {
            $minutes = $length - (int) $length;
            $hours =  (int) $length;

            if ($hours >= 1) {
                $diffInMints = ($minutes * 100);
                $diffInHours = $hours + ($diffInMints / 60);
            } else {
                $diffInHours = ($minutes * 100) / 60;
            }
            return number_format($diffInHours, 2);
        }
    }

    public function getCheckOutTime($lengthInMints = false, $overstayCheckingTime = false)
    {
        if ($lengthInMints) {
            if ($overstayCheckingTime == false) {
                $checkinTime = Carbon::parse($this->checkin_time);
            } else {
                $checkinTime = Carbon::parse($overstayCheckingTime);
            }
            // $nowTime = Carbon::parse('now');
            $nowTime = $this->checkout_time;

            $diffInHours = $checkinTime->diffInRealHours($nowTime);
            $diffInMinutes = $checkinTime->diffInRealMinutes($nowTime);
            $diffInSeconds = $checkinTime->diffInSeconds($nowTime);

            // dd($diffInHours, $diffInMinutes, $diffInSeconds);
            if ($diffInMinutes < 60) {
                $diffInHours = $diffInMinutes / 100;
            }
            if ($diffInMinutes > 59) {
                if ($diffInHours > 0) {
                    $diffInMints = $diffInMinutes - ($diffInHours * 60);
                    $diffInHours = $diffInHours . '.' . $diffInMints;
                } else {
                    $diffInHours = number_format($diffInMinutes / 60, 2);
                }
            }
            if ($diffInSeconds < 60) {
                $diffInHours = .01;
            }
            return $diffInHours;
            // return $this->getSubscriptionEndDate($this->checkin_time);
        }
        return Carbon::parse('now')->format('Y-m-d H:i:s');
    }

    // Deployed : // Vijay : 15-05-2024
    public function unGatedPriceBreakUp($rate, $facility, $requestType = null, $total = 0, $requestFrom = null)
    {

        Log::info("===============================>>>>>  unGatedPriceBreakUp Start with price " . $this->ticket_number);
        Log::info("===============================>>>>>  unGatedPriceBreakUp Start with price " . $rate['price'] . ' And parking amount : ' . $this->parking_amount);
        // $rate['price']      = $this->parking_amount;
        $this->facility     = $facility;
        // $this->request      = $request;
        $newPrice           = '0.00';
        $grandTotal         = '0.00';
        $totalPrice         = $total;
        $parkingAmount      = isset($rate['price']) ? $rate['price'] : '0.00';
        $taxRate            = $this->facility->getTaxRate($rate, '0');              // to get tax price
        $additionalFee      = $this->facility->getAdditionalFee($rate);             // to get CCFee price
        $surchargeFee       = $this->facility->getSurchargeFee($rate);              // to get CCFee price


        // change for driveup cash booking using event app - Date : 2705-24 
        if (($this->is_offline_payment == '1') && ($this->event_user_id != NULL)) {
            $taxRate = '0.00';
        }
        $isExtended = $this->ticketExtend;

        // $diffInHours = $this->getCheckOutTime(true);

        // $overstay = $this->ticketExtend;
        $additionalFee              = 0;
        $surchargeFee              = 0;
        $toatlAmountPaid            = 0;
        $payableAmount              = 0;
        $amountPaid                 = 0;
        $processingFee              = 0;
        $surchargeTax = $CCFee = $taxFee = 0;
        $overStayAmount             = 0;
        $totalParkingAmount         = 0;
        $totalAmount                = 0;
        $lastExtendGrandTotal       = 0;
        $totalGrandTotal            = 0;
        $extendDiscountAmount       = 0;
        $totalProcessingFee         = 0;
        $totalTaxFee                = 0;
        $totalCCFee                 = 0;
        $totalOversized             = $this->oversize_fee;
        $totalSurchargeFee          = 0;
        $totalLength                = $this->length;
        $totalNetParkingAmount      = 0;


        // Vlidation Related 
        $maxPercentageValdationAmount = 0;

        if ($isExtended->count() > 0) {
            foreach ($isExtended as $key => $value) {
                $totalParkingAmount     += $value->grand_total - ($value->processing_fee + $value->tax_fee + $value->additional_fee + $value->surcharge_fee);
                $toatlAmountPaid        += $value->grand_total;
                $totalAmount            += $value->total;
                $processingFee          += $value->processing_fee;
                $taxFee                 += $value->tax_fee;
                $CCFee                  += $value->additional_fee;
                $surchargeTax           += $value->surcharge_fee;
                $extendDiscountAmount   += $value->discount_amount;
                $lastExtendGrandTotal   = $value->grand_total;
                $totalOversized         += $value->oversize_fee;
                $totalLength            += $value->length;
                $totalNetParkingAmount  += $value->net_parking_amount;
            }
            $toatlAmountPaid                += $this->grand_total;
            $totalParkingAmount             += $this->parking_amount;
            $totalOversized                 += $this->oversize_fee;
            $totalAmount                    += $this->total;
            $totalNetParkingAmount          = $this->net_parking_amount + $totalNetParkingAmount;
            $totalProcessingFee             = $this->processing_fee + $processingFee;
            $totalTaxFee                    = $this->tax_fee  + $taxFee;
            $totalCCFee                     = $this->additional_fee  + $CCFee;
            $totalSurchargeFee              = $this->surcharge_fee  + $surchargeTax;
        } else {
            $toatlAmountPaid                = $this->grand_total;
            $totalParkingAmount             = $this->parking_amount;
            $totalAmount                    = $this->total;
            $totalOversized                 = $this->oversize_fee;
            $totalProcessingFee             = $this->processing_fee;
            $totalTaxFee                    = $this->tax_fee;
            $totalCCFee                     = $this->additional_fee;
            $totalSurchargeFee              = $this->surcharge_fee;
            $totalNetParkingAmount          = $this->net_parking_amount;
        }

        // VP : PIMS - 14210
        // Doing tax and procesing fee zero if net parking amount is zero
        if (!empty($this->promocode)) {
            $promoWithPromotion = DB::table('promo_codes')
                ->join('promotions', 'promo_codes.promotion_id', '=', 'promotions.id')
                ->where('promo_codes.promocode', $this->promocode)
                ->select('promotions.is_tax_applicable') // you can specify fields as needed
                ->first();
        }
        //$totalProcessingFee = $totalTaxFee = $totalCCFee = $totalSurchargeFee = $taxRate = 0;
        // if ($totalNetParkingAmount <= 0 && !empty($this->promocode) && $promoWithPromotion->is_tax_applicable == '1') {
        //     $totalProcessingFee = $totalTaxFee = $totalCCFee = $totalSurchargeFee = $taxRate = 0;
        //     Log::info("Set all free zero because net is zero : and no need to calculate tex fee ");
        // } else {
        $taxRate            = $this->facility->getTaxRate($rate, '0');           // to get tax price
        $additionalFee      = $this->facility->getAdditionalFee($rate);          // to get CCFee price
        $surchargeFee       = $this->facility->getSurchargeFee($rate);          // to get CCFee price
        //}
        //$totalProcessingFee = 33;
        // Close VP : PIMS - 14210
        // dd($totalSurchargeFee);
        // $parkingAmount = $totalParkingAmount;
        Log::info("unGatedPriceBreakUp toatlAmountPaid      :  {$toatlAmountPaid} ");
        Log::info("unGatedPriceBreakUp lastExtendGrandTotal :  {$lastExtendGrandTotal} ");
        Log::info("unGatedPriceBreakUp extendDiscountAmount :  {$extendDiscountAmount} ");
        Log::info("unGatedPriceBreakUp lastExtendGrandTotal :  {$lastExtendGrandTotal} ");
        // $amountPaid = $amountPaid - $this->discount_amount;;



        $returnData = [];
        if ($this->is_extended == '1' || $requestFrom == null) {
            $processingFee = '0.00';   // to get prcessing free channel wise need to
            // To handel Extend Payment Case If Ticket is not paid in First checkin but now ticket is payable in extend  
            // So here we teaking processing fee. : 16-12-2024
            if ($this->grand_total <= 0 && $this->discount_amount <= 0 && $toatlAmountPaid <= 0) {
                $processingFee = $this->facility->getProcessingFee($rate, '0');   // to get prcessing free channel wise need to    
            }
            // Close Here
        } else {
            $processingFee = $this->facility->getProcessingFee($rate, '0');   // to get prcessing free channel wise need to
        }

        // change for driveup cash booking using event app - Date : 2705-24 
        if (($this->is_offline_payment == '1') && ($this->event_user_id != NULL)) {
            $processingFee = '0.00';
        }
        if ($rate['price'] != 'N/A') {
            $ticketPrice = ($rate['price'] + $processingFee + $taxRate + $additionalFee + $surchargeFee);
        } else {
            $ticketPrice = '0.00';
        }

        $currentTime = date('Y-m-d H:i:s');
        // dd($ticketPrice, $rate['price'], $processingFee, $taxRate, $additionalFee, $surchargeFee);
        $returnData['length']               = sprintf("%.2f", $totalLength);
        $returnData['additional_fee']       = '0.00';
        $returnData['surcharge_fee']        = '0.00';
        $returnData['extend_amount']        = '0.00';
        $returnData['discount_amount']      = '0.00';
        $returnData['is_price_applicable']  = '0';
        $returnData['net_parking_amount']   = '0.00';
        $diffInHours                        = $returnData['length'];

        //if checkin through reservation or pass 
        if (!$this->ticket_number) {
            // for first time checkin 
            $returnData['total']            = $ticketPrice;
            $returnData['parking_amount']   = $parkingAmount;
            $returnData['paid_amount']      = '0.00';
            $returnData['grand_total']      = $ticketPrice;
            $returnData['payable_amount']   = $ticketPrice;
            $returnData['processing_fee']   = $processingFee;
            $returnData['tax_rate']         = $taxRate;
            $returnData['discount_amount']  = '0.00';
            $returnData['amount_paid']      = '0.00';
            $returnData['additional_fee']   = '0.00';
            return $returnData;
        }


        if ($this->reservation_id != '' && $this->is_extended != '1' && $this->event_id == '') {
            //if (($this->event_id != '' && $this->anet_transaction_id != '')) {
            // to check third party reservation 
            $returnData['total']            = "0.00";
            $returnData['length']           = "0.00";
            $returnData['parking_amount']   = "0.00";
            $returnData['paid_amount']      = "0.00";
            $returnData['grand_total']      = "0.00";
            $returnData['payable_amount']   = "0.00";
            $returnData['processing_fee']   = "0.00";
            $returnData['tax_rate']         = "0.00";
            $returnData['tax_fee']          = "0.00";
            $returnData['discount_amount']  = "0.00";
            $returnData['overstay_amount']  = "0.00";
            $returnData['amount_paid']      = "0.00";
            return $returnData;
        }


        if ($this->is_checkout == '0') {
            $returnData['is_price_applicable'] = '1';

            Log::info("unGatedPriceBreakUp still not checked out ticketPrice : {$ticketPrice}");
            Log::info("unGatedPriceBreakUp still not checked out count " . $isExtended->count());
            Log::info("unGatedPriceBreakUp still not checked out " . json_encode($isExtended));
            if ($this->is_extended == '0') {
                Log::info("unGatedPriceBreakUp not isExtended ");
                if ($this->grand_total > 0 && $totalPrice > 0) {
                    $grandTotal = ($this->grand_total + $totalPrice);
                    Log::info("Grand Total case 1  : " . $this->grand_total);
                } else {
                    Log::info("Grand Total case 12  : " . $this->grand_total);
                    if ($this->discount_amount > 0) {
                        $this->grand_total = $this->grand_total  - $this->discount_amount;
                    }
                    // $grandTotal = $this->grand_total > 0 ? $this->grand_total : ($totalPrice > 0 ? $totalPrice : $ticketPrice);
                    $grandTotal = $this->grand_total > 0 ? $this->grand_total : 0;
                }
                Log::info("unGatedPriceBreakUp grandTotal  {$grandTotal}");
            }


            if ($parkingAmount <= 0 && $requestType == 'set') {
                $returnData['total']            = $newPrice;
                $returnData['length']           = $this->length;
                $returnData['parking_amount']   = $newPrice;
                $returnData['paid_amount']      = $newPrice;
                $returnData['grand_total']      = $newPrice;
                $returnData['payable_amount']   = $newPrice;
                $returnData['processing_fee']   = $newPrice;
                $returnData['tax_rate']         = $newPrice;
                $returnData['amount_paid']      = $newPrice;
                $returnData['additional_fee']   = $newPrice;
                $returnData['surcharge_fee']    = $newPrice;
                return $returnData;
            }
            Log::info("unGatedPriceBreakUp 111 paid_by      :  {$this->paid_by}");
            Log::info("unGatedPriceBreakUp 111 paid_type    :  {$this->paid_type}");
            Log::info("unGatedPriceBreakUp Amount Paid      :  {$toatlAmountPaid}");

            if ($this->paid_type != '9' && !is_null($this->paid_type)) {
                Log::info("unGatedPriceBreakUp ticket validated  ");
                if ($requestType == 'set') {
                    if ($this->paid_type == '0') {    // Full Validation case 
                        Log::info("unGatedPriceBreakUp full validated parkingAmount {$parkingAmount}");
                        $returnData['additional_fee']       = $newPrice;
                        $returnData['surcharge_fee']        = $newPrice;

                        if ($parkingAmount > 0) {
                            $returnData['total']            = $ticketPrice;
                            // $returnData['parking_amount']   = $this->parking_amount + $parkingAmount;
                            $returnData['parking_amount']   = $parkingAmount;
                            $returnData['paid_amount']      = $ticketPrice;
                            $returnData['processing_fee']   = $processingFee;
                            $returnData['tax_rate']         = $this->tax_fee + $taxRate;
                        } else {
                            $returnData['total']            = $newPrice;
                            $returnData['parking_amount']   = $newPrice;
                            $returnData['paid_amount']      = $newPrice;
                            $returnData['processing_fee']   = $newPrice;
                            $returnData['tax_rate']         = $newPrice;
                        }
                        $returnData['grand_total']          = $newPrice;
                        $returnData['payable_amount']       = $newPrice;
                        $returnData['amount_paid']          = $newPrice;
                        $returnData['discount_amount']      = $newPrice;
                    } else if ($this->paid_type == '1') { // Hours   dd('here now');
                        $arrival_time = Carbon::parse('now')->format('Y-m-d H:i:s');
                        Log::info("unGatedPriceBreakUp Hour Validated  ");
                        $paidDiffInHours = $this->paid_hour;
                        if ($diffInHours >= $paidDiffInHours) { // dd('now here');
                            $diff_in_hours = $diffInHours - $paidDiffInHours;

                            $hoursRate = $this->facility->rateForReservationOnMarker($arrival_time, $diff_in_hours, false, false,  false, true, false, 0, 0);

                            $discountPrice = 0;
                            if ($hoursRate['price'] > 0) {
                                $discountPrice = ($ticketPrice - $hoursRate['price']);
                                $returnData['total'] = $ticketPrice;
                                $returnData['parking_amount'] = $parkingAmount;
                                $returnData['paid_amount'] = $discountPrice;
                                $returnData['length'] = $diff_in_hours;
                                $returnData['grand_total'] = ($ticketPrice - $discountPrice);
                                $returnData['payable_amount'] = ($ticketPrice - $discountPrice);
                                $returnData['processing_fee'] = $processingFee;
                                $returnData['tax_rate'] = $taxRate;
                                $returnData['additional_fee']   = $CCFee;
                                $returnData['surcharge_fee']    = $surchargeFee;
                                $returnData['discount_amount']  = $this->discount_amount;
                            } else {

                                $payableAmount = $ticketPrice + $CCFee + $surchargeFee + $taxRate;
                                $returnData['total'] = $ticketPrice;
                                $returnData['parking_amount'] = $parkingAmount;
                                $returnData['paid_amount'] = isset($this->paid_amount) ? $this->paid_amount : $ticketPrice;
                                $returnData['length'] = $diff_in_hours;
                                $returnData['grand_total'] = '0.00';
                                $returnData['payable_amount'] = $payableAmount;
                                $returnData['processing_fee'] = '0.00';
                                $returnData['additional_fee']   = $CCFee;
                                $returnData['surcharge_fee']    = $surchargeFee;
                                $returnData['tax_rate'] = $taxRate;
                                $returnData['discount_amount']  = $this->discount_amount;
                            }
                        } else {
                            $returnData['total'] = $ticketPrice;
                            $returnData['parking_amount'] = $parkingAmount;
                            $returnData['paid_amount'] = $this->paid_amount;
                            $returnData['processing_fee'] = $processingFee;
                            $returnData['additional_fee']   = $CCFee;
                            $returnData['surcharge_fee']    = $surchargeFee;
                            $returnData['tax_rate'] = $taxRate;
                            $returnData['grand_total'] = $newPrice;
                            $returnData['payable_amount'] = $ticketPrice;
                            $returnData['discount_amount']  = $this->discount_amount;
                        }
                        $returnData['amount_paid']      = $toatlAmountPaid - ($this->discount_amount + $this->paid_amount);
                    } else if ($this->paid_type == '2') { // Amount

                        Log::info("unGatedPriceBreakUp Amount Validated  ");

                        $returnData['total']            = $ticketPrice;
                        $returnData['parking_amount']   = $parkingAmount;
                        $returnData['paid_amount']      = $newPrice;
                        $returnData['grand_total']      = $grandTotal;
                        $returnData['payable_amount']   = $ticketPrice;
                        $returnData['processing_fee']   = $processingFee;
                        $returnData['tax_rate']         = $taxRate;
                        $returnData['discount_amount']  = $this->discount_amount;
                        $returnData['amount_paid']      = $newPrice;
                        $returnData['additional_fee']   = $CCFee;
                        $returnData['surcharge_fee']    = $surchargeFee;
                    } else if ($this->paid_type == '3') { // Percentage
                        // $maxPercentageValdationAmount = $this->max_validated_amount;
                        Log::info("unGatedPriceBreakUp Per Validated  ");
                        $payableAmount = 0;
                        $validatedAmount = 0;
                        $grandTotal = 0;
                        $taxData = $additionalFee + $taxRate;
                        $perValidated  = $this->getPercetageValidation($ticketPrice, $taxData);

                        $payableAmount = $perValidated['payableAmount'];
                        $validatedAmount = $perValidated['validatedAmount'];
                        $validatedBaseAmount = $perValidated['validatedBaseAmount'];
                        // dd($perValidated);
                        // $grandTotal = $payableAmount;
                        // check paid and grand total is any after payment
                        if ($this->grand_total > 0) { // if any payment done then minus grand otal from payable 
                            // $grandTotal =  $this->grand_total;
                            // $payableAmount = $payableAmount - $this->grand_total;
                        }
                        // dd($perValidated, $payableAmount, $validatedAmount);
                        if (in_array($this->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                            if ($validatedBaseAmount > 0) {
                                $rate['price']      = $validatedBaseAmount;
                                $taxRate            = $this->facility->getTaxRate($rate, '0');
                            }
                        } else if ($validatedBaseAmount > 0) {
                            $rate['price']      = $validatedBaseAmount;
                            $taxRate            = $this->facility->getTaxRate($rate, '0');
                        }
                        $returnData['total']            = $ticketPrice;
                        $returnData['parking_amount']   = $parkingAmount;

                        if (in_array($this->partner_id, config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
                            $returnData['paid_amount']      = $validatedBaseAmount;
                        } else if ($validatedBaseAmount > 0) {
                            $returnData['paid_amount']      = $validatedAmount;
                        }
                        $returnData['grand_total']      = $this->grand_total - ($this->discount_amount + $this->paid_amount);
                        $returnData['payable_amount']   = $payableAmount;
                        $returnData['processing_fee']   = $processingFee;
                        $returnData['tax_rate']         = $taxRate;
                        $returnData['discount_amount']  = $this->discount_amount;
                        $returnData['amount_paid']      = $newPrice;
                        $returnData['additional_fee']   = $CCFee;
                        $returnData['surcharge_fee']    = $surchargeFee;
                    }
                } else if ($requestType == 'get') {        // To Payment Page                    
                    Log::info("unGatedPriceBreakUp is_extended 11 ");
                    $returnData['total']            = $totalAmount;
                    $returnData['parking_amount']   = $totalParkingAmount;
                    /*
					if(in_array($this->partner_id,config('parkengage.PARTNER_GROUP_DISCOUNT'))) {
						$newPrice =  $parkingAmount - ($this->discount_amount + $extendDiscountAmount + $this->paid_amount);
						$rate['price']      = $newPrice;
                        $totalTaxFee        = $this->facility->getTaxRate($rate, '0');  
						$returnData['amount_paid']      = $newPrice + $totalProcessingFee + $totalTaxFee + $totalCCFee + $extendDiscountAmount;
					}else{
						$returnData['amount_paid']      = ($toatlAmountPaid) - ($this->discount_amount + $extendDiscountAmount + $this->paid_amount);
					}
                    */
                    $returnData['paid_amount']      = $this->paid_amount;
                    $returnData['grand_total']      = $lastExtendGrandTotal;
                    $returnData['payable_amount']   = '0.00';
                    $returnData['processing_fee']   = $totalProcessingFee;
                    $returnData['tax_rate']         = $totalTaxFee;
                    $returnData['additional_fee']   = $totalCCFee;
                    $returnData['surcharge_fee']    = $totalSurchargeFee;
                    $returnData['discount_amount']  = $this->discount_amount + $extendDiscountAmount;
                    $returnData['extend_amount']    = '0.00';
                    $returnData['amount_paid']      = ($toatlAmountPaid) - ($this->discount_amount + $extendDiscountAmount + $this->paid_amount);
                }

                Log::info("Validated Ticket Ungated Response 11 ");
                Log::info($returnData);
                return $returnData;
            } else {
                Log::info("unGatedPriceBreakUp NOt validated parkingAmount :  {$parkingAmount}");
                if ($parkingAmount > 0) {
                    Log::info("unGatedPriceBreakUp 222222 ");
                    if (($this->reservation_id != '' || $this->user_pass_id != '') || ($this->event_id != '' && $this->anet_transaction_id != '')) {
                        $returnData['total']            = isset($this->reservation->total) ? $this->total : 0.00;
                        $returnData['length']           = $this->length;
                        $returnData['parking_amount']   = $parkingAmount;
                        $returnData['paid_amount']      = $this->paid_amount;
                        $returnData['grand_total']      = ($ticketPrice - $this->discount_amount);
                        $returnData['payable_amount']   = $newPrice;
                        $returnData['processing_fee']   = $this->reservation_id > 0 ? $this->reservation->processing_fee : $this->processing_fee;
                        $returnData['tax_rate']         = $this->reservation_id > 0 ? $this->reservation->tax_fee : $this->tax_fee;
                        $returnData['amount_paid']      = ($this->grand_total) - ($this->paid_amount + $this->discount_amount);
                        return $returnData;
                    }

                    if ($requestType == 'get') {
                        // Not In Grace period Case
                        $amountPaid = 0;
                        $amountPaid = floatval($toatlAmountPaid) - (floatval($this->discount_amount) + floatval($extendDiscountAmount) + floatval($this->paid_amount));

                        $returnData['is_price_applicable'] = '0';
                        $returnData['total']            = $totalAmount;
                        $returnData['parking_amount']   = $totalParkingAmount;
                        $returnData['paid_amount']      = $this->paid_amount;
                        $returnData['grand_total']      = $lastExtendGrandTotal;
                        $returnData['payable_amount']   = '0.00';
                        $returnData['processing_fee']   = $totalProcessingFee;
                        $returnData['tax_rate']         = $totalTaxFee;
                        $returnData['additional_fee']   = $totalCCFee;
                        $returnData['surcharge_fee']    = $totalSurchargeFee;
                        $returnData['discount_amount']  = $this->discount_amount + $extendDiscountAmount;
                        $returnData['extend_amount']    = '0.00';
                        $returnData['amount_paid']      = $amountPaid < 0 ? 0 : QueryBuilder::currencyFormat($amountPaid);
                    } else if ($requestType == 'set') {
                        // Not In Grace period Case
                        $returnData['is_price_applicable'] = '0';
                        $returnData['total']            = $ticketPrice;
                        $returnData['parking_amount']   = $parkingAmount;
                        $returnData['paid_amount']      = $this->paid_amount;
                        $returnData['grand_total']      = $this->discount_amount > 0 ? round($ticketPrice - $this->discount_amount, 2) : $ticketPrice;
                        $returnData['payable_amount']   = $ticketPrice;
                        $returnData['processing_fee']   = $processingFee;
                        $returnData['tax_rate']         = $taxRate;
                        $returnData['additional_fee']   = $CCFee;
                        $returnData['surcharge_fee']    = $surchargeFee;
                        $returnData['discount_amount']  = $this->discount_amount;
                        $returnData['amount_paid']      = $toatlAmountPaid;
                    }
                } else {
                    // Grace period Case
                    Log::info("unGatedPriceBreakUp 3333 ");
                    $returnData['total']            = $newPrice;
                    $returnData['length']           = '0';
                    $returnData['parking_amount']   = $newPrice;
                    $returnData['paid_amount']      = $newPrice;
                    $returnData['grand_total']      = $newPrice;
                    $returnData['payable_amount']   = $newPrice;
                    $returnData['processing_fee']   = $newPrice;
                    $returnData['tax_rate']         = $newPrice;
                    $returnData['discount_amount']  = $newPrice;
                    $returnData['amount_paid']      = $newPrice;
                    $returnData['additional_fee']   = $newPrice;
                    $returnData['surcharge_fee']    = $newPrice;
                }
                $returnData['net_parking_amount'] = $totalNetParkingAmount;

                Log::info("Ticket Model Ungated Response 11111  ");
                Log::info($returnData);

                if ($requestFrom != 'adminDetail') {
                    // Vijay : Commented on : 03-06-2025 due to amount due showing in amount paid.  
                    return $returnData;
                }
            }
        } else {
            // when ticket extended or closed
            Log::info("extened or checkedout tickets ");
            // $returnData['is_price_applicable'] = '1';
            $returnData['total']            = $this->total;
            $returnData['parking_amount']   = $this->parking_amount;
            $returnData['paid_amount']      = $this->paid_amount;
            $returnData['grand_total']      = $this->grand_total;
            $returnData['payable_amount']   = $newPrice;
            $returnData['processing_fee']   = $this->processing_fee;
            $returnData['tax_rate']         = $this->tax_fee;
            $returnData['discount_amount']  = $this->discount_amount;
            $returnData['extend_amount']    = '0.00';
            $returnData['amount_paid']      = $this->grand_total - ($this->paid_amount + $this->discount_amount);
            $returnData['additional_fee']   = $totalCCFee;
            $returnData['surcharge_fee']    = $totalSurchargeFee;
            $returnData['net_parking_amount'] = $totalNetParkingAmount;
            if ($this->is_extended == '1') {
                // $overStay = $this->getOverStayAmount($this->estimated_checkout);
                $overstay = $this->ticketExtend;

                $extendAmountPaid = 0;
                $extendParkingAmount = 0;
                $extendDiscountAmount = 0;
                $taxFee = 0;
                $CCFee = 0;
                $processingFee = 0;
                if ($overstay->count() > 0) {
                    foreach ($overstay as $key => $value) {
                        $extendAmountPaid       += $value->grand_total;
                        $taxFee                 += $value->tax_fee;
                        $processingFee          += $value->processing_fee;
                        $extendDiscountAmount   += $value->discount_amount;
                        $extendParkingAmount    += ($value->total - ($value->tax_fee + $value->processing_fee));
                        // $overStayAmount += $value->grand_total;
                    }
                }

                if (($this->reservation_id != '' || $this->user_pass_id != '') || ($this->event_id != '' && $this->anet_transaction_id != '')) {
                    $returnData['total']            = isset($this->reservation->total) ? $this->reservation->total : "0.00";
                    $returnData['length']           = $this->length;
                    $returnData['parking_amount']   = $newPrice;
                    $returnData['paid_amount']      = $newPrice;
                    $returnData['grand_total']      = $newPrice;
                    $returnData['payable_amount']   = $newPrice;
                    $returnData['processing_fee']   = $this->reservation_id > 0 ? $this->reservation->processing_fee : $this->processing_fee;
                    $returnData['tax_rate']         = $this->reservation_id > 0 ? $this->reservation->tax_fee : $this->tax_fee;
                    $returnData['amount_paid']      = ($extendAmountPaid + $this->grand_total) - ($this->paid_amount + $this->discount_amount + $extendDiscountAmount);
                    return $returnData;
                } else {
                    $returnData['total']            = $totalAmount;
                    $returnData['parking_amount']   = $totalParkingAmount;
                    $returnData['paid_amount']      = $this->paid_amount;
                    $returnData['grand_total']      = $toatlAmountPaid;
                    $returnData['processing_fee']   = $this->processing_fee + $processingFee;
                    $returnData['additional_fee']   = $totalCCFee;
                    $returnData['surcharge_fee']    = $totalSurchargeFee;
                    $returnData['tax_rate']         = $this->tax_fee + $taxFee;
                    $returnData['discount_amount']  = $this->discount_amount + $extendDiscountAmount;
                    $returnData['extend_amount']    = $newPrice;
                    $returnData['amount_paid']      = $newPrice;
                    $returnData['payable_amount']   = ($toatlAmountPaid) - ($this->paid_amount + $this->discount_amount + $extendDiscountAmount);

                    if ($this->payment_date != '') {
                        $returnData['payable_amount']   = $newPrice;
                        $returnData['amount_paid']      = ($toatlAmountPaid) - ($this->paid_amount + $this->discount_amount + $extendDiscountAmount);
                    }
                }
            }
        }

        // if ($requestType == 'get' && $requestFrom == 'adminDetail') {   // To Display Only
        //     // dd($requestType == 'get' && $requestFrom == 'adminDetail', !empty($this->payment_date)  || $this->is_checkout == '1');
        //     if (!empty($this->payment_date)  || $this->is_checkout == '1') {
        //         $returnData['payable_amount']   = $newPrice;
        //         $returnData['amount_paid']      = ($toatlAmountPaid) - ($this->paid_amount + $this->discount_amount + $extendDiscountAmount);
        //     } else {
        //         $returnData['payable_amount']   = ($toatlAmountPaid) - ($this->paid_amount + $this->discount_amount + $extendDiscountAmount);
        //         $returnData['amount_paid']      = $newPrice;
        //     }
        //     // dd($returnData, $toatlAmountPaid, $this->discount_amount, $extendDiscountAmount);
        // } else 
        if ($requestType == 'get') {   // To Display Only
            if (!empty($this->payment_date)  || $this->is_checkout == '1') {
                $returnData['payable_amount']   = $newPrice;
                $returnData['amount_paid']      = ($toatlAmountPaid) - ($this->paid_amount + $this->discount_amount + $extendDiscountAmount);
            } else {
                $returnData['payable_amount']   = ($toatlAmountPaid) - ($this->paid_amount + $this->discount_amount + $extendDiscountAmount);
                $returnData['amount_paid']      = $newPrice;
            }
        } else if ($requestType == 'set') { // To Payment Page

            // if ($this->is_checkout == '1') {
            //     $returnData['payable_amount']   = $newPrice;
            //     $returnData['amount_paid']      = ($toatlAmountPaid) - ($this->paid_amount + $this->discount_amount + $extendDiscountAmount);
            // } else {
            //     $returnData['payable_amount']   = ;
            //     $returnData['amount_paid']      = $newPrice;
            // }
        }


        Log::info("Ticket Model Ungated Response last  ");
        Log::info($returnData);
        return $returnData;
    }

    public function getTotalAttribute($total)
    {
        if ($total > 0) {
            return $total;
        }
        return '0.00';
    }

    // public function getLotFacilityAttribute()
    // {
    //     $lot_facility = '';
    //     if (!empty($this->reservation_id)) {
    //         $reservation = Reservation::with('facility')->where('id', $this->reservation_id)->first();
    //         $lot_facility = ($reservation['facility']) ? $reservation['facility'] : '';
    //     }
    //     return $lot_facility;
    // }

    // Vijay : 19-05-2025 
    // To handel checkout for exted ticket and move in ticket table checkout_time 
    public function getCheckoutTimeAttribute($checkout_time)
    {
        $extendCheckoutTime = $checkout_time;
        if ($this->is_extended == '1'  && $this->ticketExtend->count() > 0) {
            foreach ($this->ticketExtend as $key => $extendTicket) {
                // $extendCheckoutTime =  $extendTicket->checkout_time;
            }
            return $extendCheckoutTime;
        }
        return $extendCheckoutTime;
    }

    public function getCheckinCheckOutDifference($checkoutTime)
    {
        $checkinTime = Carbon::parse($this->checkin_time);
        //$nowTime = Carbon::parse('now');
        $nowTime = Carbon::parse($checkoutTime);

        // $nowTime = Carbon::parse('2023-11-22 06:04:01'); //2023-11-16 00:07:35
        $diffInHours = $checkinTime->diffInRealHours($nowTime);
        $diffInMinutes = $checkinTime->diffInRealMinutes($nowTime);
        $diffInSeconds = $checkinTime->diffInSeconds($nowTime);

        // dd($diffInHours, $diffInMinutes, $diffInSeconds);
        if ($diffInMinutes < 60) {
            $diffInHours = $diffInMinutes / 100;
        }
        // dd($diffInHours);
        if ($diffInMinutes > 59) {
            if ($diffInHours > 0) {
                $diffInMints = $diffInMinutes - ($diffInHours * 60);
                $NewdiffInMints = $this->addZero($diffInMints);
                $diffInHours = $diffInHours + $NewdiffInMints;
            } else {
                $diffInHours = $this->addZero($diffInMinutes);
            }
        }
        if ($diffInSeconds < 60) {
            $diffInHours = .01;
        }
        return $diffInHours;
    }

    public static function clerkCappingUpdate($ticket_number)
    {
        $ticket = Ticket::with(['facility'])->where('ticket_number', $ticket_number)->first();

        $usage = UserValidateMaster::where('user_id', $ticket->paid_by)->first();
        if ($usage) {
            $policy = BusinessPolicy::find($ticket->policy_id);
            if ($ticket->paid_amount  != '') {
                if ($policy->discount_type == '0' && $usage->full_amount_remaining >= $ticket->total) {
                    $usage->full_amount_remaining = $usage->full_amount_remaining - $ticket->total;
                } else if ($policy->discount_type == '1' && $usage->max_remaining_hour >= $ticket->paid_amount) {
                    $usage->max_remaining_hour = $usage->max_remaining_hour - $ticket->paid_amount;
                } else if (($policy->discount_type == '2' || $policy->discount_type == '3') && $usage->max_remaining_dollar >= $ticket->paid_amount) {
                    $usage->max_remaining_dollar = $usage->max_remaining_dollar - $ticket->paid_amount;
                }
            } else {
                $paid_amount = $ticket->total;

                if ($policy->discount_type == '0' && $usage->full_amount_remaining >= $paid_amount) {
                    $usage->full_amount_remaining = $usage->full_amount_remaining - $paid_amount;
                } else if ($policy->discount_type == '1' && $usage->max_remaining_hour >= $paid_amount) {
                    $usage->max_remaining_hour = $usage->max_remaining_hour - $paid_amount;
                } else if (($policy->discount_type == '2' || $policy->discount_type == '3') && $usage->max_remaining_dollar >= $paid_amount) {
                    $usage->max_remaining_dollar = $usage->max_remaining_dollar - $paid_amount;
                }
            }
            $usage->save();
        }
        return true;
    }

    public function PermitVehicle()
    {
        return $this->belongsTo('App\Models\PermitVehicle', 'vehicle_id')->withTrashed();
    }

    public function permit()
    {
        return $this->belongsTo('App\Models\PermitRequest', 'permit_request_id');
    }

    public function warning()
    {
        return $this->hasOne('App\Models\ParkEngage\Warning', 'ticket_id');
    }

    // Create New ti

    /***
     * Shalu:13/06/2024
     * Method to get final_checkout after extend
     * 
     * @return String
     */
    public function getLastCheckoutTime()
    {
        if ($this->is_extended == '1') {
            return $this->hasMany('App\Models\ParkEngage\TicketExtend', 'ticket_id')->latest()->value('checkout_time');
        } elseif ($this->is_overstay == '1') {
            return $this->hasMany('App\Models\OverstayTicket', 'ticket_id')->latest()->value('checkout_datetime');
        } else {
            return $this->checkout_datetime;
        }
    }

    // calculate Hourly Counsumed Promocode

    public function getHourlyPromocodeUsedHoursAttribute()
    {

        if (!empty($this->promocode) && $this->discount_hours > 0) {
            return $this->discount_hours;
        }
        return '0.00';
    }


    public function getPromotionDetails($promocode)
    {
        if (!empty($promocode)) {
            return Promotion::where(['name' => $promocode, 'status' => '1'])->first();
        }
        return false;
    }

    public function PromotionDetails()
    {
        return $this->belongsTo('App\Models\Promotion', 'promocode', 'name');
    }

    public function facilityData()
    {
        return $this->belongsTo('App\Models\Facility', 'facility_id')->select('id', 'short_name', 'full_name', 'garage_code', 'is_gated_facility');
    }

    public function userData()
    {
        return $this->belongsTo('App\Models\User', 'user_id')->select(['id', 'name', 'email', 'phone', 'user_type', 'created_at', 'updated_at', 'is_partner', 'company_email', 'city', 'country', 'created_by', 'address', 'address2', 'state', 'business_id', 'status', 'user_parent_id', 'slug']);
    }

    public function voidSale()
    {
        return $this->belongsTo(RefundTransaction::class, 'ticket_number', 'reference_key');
    }

    public function businessPolicy()
    {
        return $this->belongsTo('App\Models\BusinessPolicy', 'policy_id')->select(['id', 'policy_name']);
    }

    public function hasPaymentMethod(array $methods, $flag = false)
    {
        if (!$flag) {
            return $this->ticketExtend && $this->ticketExtend->whereIn('payment_method', $methods)->count() > 0;
        } else {
            return in_array($this->payment_method, $methods) ? true : false;
        }
    }

    public function getMainTicketAttribute()
    {
        $arr['length']              = $this->length;
        $arr['total']               = $this->total;
        $arr['grand_total']         = $this->grand_total;
        $arr['parking_amount']      = $this->parking_amount;
        $arr['discount_amount']     = $this->discount_amount;
        $arr['paid_amount']         = $this->paid_amount;
        $arr['processing_fee']      = $this->processing_fee;
        $arr['tax_fee']             = $this->tax_fee;
        return $arr;
    }

    public function preAuthData()
    {
        return $this->hasOne('App\Models\ParkEngage\DatacapTransaction', 'ticket_id');
    }

    public function ticketadditionalinfo()
    {
        $data = $this->hasMany('App\Models\TicketAdditionalInfo', 'ticket_id')
            ->selectRaw('
            ticket_id, 
            SUM(new_parking_amount) as new_parking_amount, 
            SUM(new_tax_amount) as new_tax_amount, 
            SUM(new_processing_fee) as new_processing_fee,
            SUM(new_discount_amount) as new_discount_amount
        ')
            ->groupBy('ticket_id')
            ->first(); // We use first() to get only the first result (which is already summed)

        // Return the result as an object (if data exists)
        return $data ? (object) $data->toArray() : null; // Cast to object
    }


    public function ticketDetails()
    {
        return $this->hasOne('App\Models\TicketDetails', 'ticket_id');
    }


}
