<?php
/*
 * -----------------------------------------------------------------------------
 * Copyright (c) 2024 ParkEngage Inc. All rights reserved.
 * -----------------------------------------------------------------------------
 *
 * This software is the confidential and proprietary information of
 * ParkEngage Inc. ("Confidential Information"). You shall not disclose such
 * Confidential Information and shall use it only in accordance with the terms
 * of the license agreement you entered into with ParkEngage Inc.
 *
 * This source code is a valuable trade secret of ParkEngage Inc.
 *
 * DISCLAIMER:
 * THIS SOFTWARE IS PROVIDED BY PARKENGAGE INC. "AS IS" AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
 * EVENT SHALL PARKENGAGE INC. OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * The views and conclusions contained in the software and documentation are
 * those of the authors and should not be interpreted as representing official
 * policies, either expressed or implied, of ParkEngage Inc.
 * -----------------------------------------------------------------------------
 */

namespace App\Models;

use App\Classes\Elimiwait;
use App\Classes\GoogleMaps;
use Carbon\Carbon;
use Faker\Provider\Uuid;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use View;
use App\Models\MonthlyCampaign;
use App\Models\RateSearchAtlanta;
use App\Models\ParkEngage\ParkingDevice;
use App\Http\Helpers\QueryBuilder;
use App\Models\ParkEngage\FacilityPaymentDetail;

class Facility extends Model
{
    use SoftDeletes;

    public $table = 'facilities';

    const IMAGE_FOLDER = 'facility_images';

    // const RESERVATION_BONUS_HOURS = 1.00;
    // const RESERVATION_BONUS_RATE  = 5.00;
    const RESERVATION_BONUS_HOURS = '0';
    const RESERVATION_BONUS_RATE  = '0.00';

    const RESERVATION_OVERSTAY_HOURS = 1.00;
    const RESERVATION_OVERSTAY_RATE  = 10.00;

    const BEACON_ACTIVE   = 1;
    const BEACON_INACTIVE = 0;
    const DEFAULT_VAL = 0;
    const DEFAULT_VAL_ONE = 1;
    const COUPON_RATE   = 2;
    const PRICE_VAL   = 0;
    const RESERVATION_PRICE_VAL   = 2;
    const PERCENTAGE_VAL   = 1;
    const BOARD_RATE  = 1;
    const DEFAULT_INVENTORY_THRESHOLD = 0;

    const DRIVE_UP_PROCESSING_FEE = '0';
    const PROCESSING_FEE = '1';   // for Reservation
    const CITATION_PROCESSING_FEE = '2';

    protected $fillable = [
        "short_name",
        "full_name",
        "garage_code",
        "disclaimer",
        "facility_type_id",
        "between_streets",
        "entrance_location",
        "phone_number",
        "base_rate",
        "active",
        "beacon_status",
        "base_event_rate",
        "oversize_fee",
        "processing_fee",
        "tax_rate",
        "base_event_rate_max_stay",
        "garage_located",
        "accept_echeck",
        "neighborhood_id",
        "monthly_rate",
        "accept_cc",
        'authorize_net_transaction_key',
        'authorize_net_login',
        'ticketech_id',
        'x999_account',
        'reservation_bonus_hours',
        'reservation_bonus_rate',
        'reservation_overstay_rate',
        'reservation_overstay_hours',
        'is_indoor_parking',
        'is_outdoor_parking',
        'is_tesla_charging',
        'is_generic_ev_charging',
        'is_motorcycle_parking',
        'merchant_id',
        'realtime_window',
        'realtime_minimum_availability',
        'inquiry_key_lifetime',
        'parkonect_id',
        'is_available',
        'facility_booking_type',
        'valet_rate',
        'is_gated_facility',
        "geo_distance",
        'penalty_fee',
        'citation_processing_fee',
        'check_vehicle_enabled',
        'is_prepaid_first',
        'license_format',
        'license_min_lenght',
        'license_max_lenght',
        'fix_length',
        'is_multiday_parking',
        'is_end_parking',
        'referral_amout',
        'timezone',
        'capacity',
        'is_parking_extend',
        'mid',
        'ecommerce_mid',
        'ecommerce_token',
        'is_service_update',
        'drive_up_processing_fee',
        'is_price_band',
        'tax_rate_type',
        'grace_period_minute',
        'reservation_grace_period_minute',
        'is_permit_purchase_enabled',
        'rate_daily_max_amount',
        'rate_duration_in_hours',
        'rate_free_minutes',
        'rate_per_hour',
        'owner_id',
        'citation_format',
        'is_lot',
        'is_promocode_validation_enabled',
        'is_hourly_rate',
        'is_rate_days_skip',
        'is_extend_allow',
        'is_checkout_enabled',
        'is_custom_picker_enabled',
        'permit_processing_fee',
        'pass_processing_fee',
        'is_landscape_enabled',
        'additonal_fee',
        'additonal_fee_type',
        'surcharge_fee',
        'surcharge_fee_type',
        'allow_refund',
        'is_refund_hourly',
        'is_refund_day'
    ];

    public static $validParams = [
        'short_name'              => 'required|unique:facilities',
        'full_name'               => 'required|unique:facilities',
        'garage_code'             => 'required|unique:facilities',
        'facility_type_id'        => 'required',
        'phone_number'            => 'required|numeric|digits:10',
        'reservation_bonus_rate'  => 'numeric|min:0',
        'reservation_bonus_hours' => 'numeric|min:0'
    ];

    public static $validMessages = [
        'short_name.required' => 'The Short Name field is required.',
        'short_name.unique' => 'The Short Name already exist.',
        'full_name.required' => 'The Full Name field is required.',
        'full_name.unique' => 'The Garage Name already exist.',
        'garage_code.required' => 'The Garage Code field is required.',
        'phone_number.required'  => 'The Phone Number field is required.',
        'garage_code.unique' => 'The Garage Code already exists.',
    ];

    /**
     * Fields that should not be serialized
     *
     * @var [type]
     */
    protected $hidden = ['created_at', 'updated_at', 'deleted_at', 'authorize_net_transaction_key', 'authorize_net_login', 'ticketech_id', 'x999_account', 'disclaimer'];

    /**
     * Attributes that should be included when model is serialized
     *
     * @var [type]
     */
    protected $appends = ['slug', 'has_ticketech', 'has_monthly', 'is_elimiwait_active', 'display_name', 'animation_gif_url', 'facility_logo_url'];

    public static $searchFields = [
        'full_name',
        'short_name'
    ];

    public function getSlugAttribute()
    {
        $slug = $this->attributes['short_name'];
        $slug = strtolower($slug);
        $slug = preg_replace('/[^a-z0-9 -]+/', '', $slug);
        $slug = str_replace(' ', '-', $slug);
        return trim($slug, '-');
    }

    public function getHasMonthlyAttribute()
    {
        return (float)$this->monthly_rate != 0 ? true : false;
    }

    public function getMonthlyRateAttribute($value)
    {
        return $value == 0 ? 'N/A*' : $value;
    }

    public function getHasTicketechAttribute()
    {
        return !empty($this->ticketech_id);
    }

    public function getIsElimiwaitActiveAttribute()
    {
        $activeLocations = config('elimiwait.api_locations');

        if (!$this->elimiwait_location_id || !$activeLocations) {
            return false;
        }

        if ($activeLocations === Elimiwait::ALL_LOCATIONS_ACTIVE) {
            return true;
        }

        return in_array($this->id, $activeLocations);
    }

    public function getDisplayNameAttribute()
    {
        return $this->geolocations ? $this->geolocations->address_1 : $this->entrance_location;
    }

    public function getReservationBonusRateAttribute($value)
    {
        return $value ?? self::RESERVATION_BONUS_RATE;
    }

    public function getReservationBonusHoursAttribute($value)
    {
        return $value ?? self::RESERVATION_BONUS_HOURS;
    }

    // Only set the bonus rate in the database if it is different from our default
    public function setReservationBonusRateAttribute($value)
    {
        $this->attributes['reservation_bonus_rate'] = $value == self::RESERVATION_BONUS_RATE ? null : $value;
    }

    // Only set the bonus hours in the database if it is different from our default
    public function setReservationBonusHoursAttribute($value)
    {
        $this->attributes['reservation_bonus_hours'] = $value == self::RESERVATION_BONUS_HOURS ? null : $value;
    }

    public function getReservationOverstayRateAttribute($value)
    {
        return $value ?? self::RESERVATION_OVERSTAY_RATE;
    }

    public function getReservationOverstayHoursAttribute($value)
    {
        return $value ?? self::RESERVATION_OVERSTAY_HOURS;
    }

    // Only set the bonus rate in the database if it is different from our default
    public function setReservationOverstayRateAttribute($value)
    {
        $this->attributes['reservation_overstay_rate'] = $value == self::RESERVATION_OVERSTAY_RATE ? null : $value;
    }

    // Only set the bonus hours in the database if it is different from our default
    public function setReservationOverstayHoursAttribute($value)
    {
        $this->attributes['reservation_overstay_hours'] = $value == self::RESERVATION_OVERSTAY_HOURS ? null : $value;
    }

    public function setDistanceFromRadius($distance)
    {
        $this->attributes['distance'] = $distance;
    }

    public function getIntegrations()
    {
        $rate                                  = [];
        $rate['authorize_net_transaction_key'] = $this->authorize_net_transaction_key;
        $rate['authorize_net_login']           = $this->authorize_net_login;
        $rate['ticketech_id']                  = $this->ticketech_id;
        $rate['x999_account']                  = $this->x999_account;
        if (isset($this->parkonect_id) && ($this->parkonect_id != '')) {
            $rate['connection_type'] = 2;
        } else {
            $rate['connection_type'] = 1;
        }
        $rate['parkonect_id']        = $this->parkonect_id;
        return $rate;
    }

    public function geolocations()
    {
        return $this->morphOne('App\Models\GeoLocation', 'locatable');
    }

    public function userMember()
    {
        return $this->hasMany('App\Models\ParkEngage\UserMembership', 'user_id');
    }

    public function facilityType()
    {
        return $this->belongsTo('App\Models\FacilityType', 'facility_type_id');
    }

    public function garageType()
    {
        return $this->belongsTo('App\Models\GarageType', 'garage_type_id');
    }

    public function serviceType()
    {
        return $this->belongsTo('App\Models\ServiceType', 'service_type_id');
    }

    public function vehicleType()
    {
        return $this->belongsTo('App\Models\VehicleType', 'vehicle_type_id');
    }

    public function features()
    {
        return $this->belongsToMany('App\Models\Feature')->withTimestamps();
    }

    public function photos()
    {
        return $this->morphOne('App\Models\Photo', 'imageable');
    }

    public function rates()
    {
        return $this->hasMany('App\Models\Rate', 'facility_id')->orderBy('price', 'desc');
    }

    public function woodmanLicensePlate()
    {
        return $this->hasMany('App\Models\ParkEngage\WoodmanLicensePlate');
    }

    public function licensePlate()
    {
        return $this->hasMany('App\Models\ParkEngage\LicensePlate');
    }

    public function facilityfee()
    {
        return $this->hasMany('App\Models\FacilityFee', 'facility_id');
    }

    public function facilityOnBoardRate()
    {
        return $this->hasMany('App\Models\Rate', 'facility_id')->where('rate_type_id', self::BOARD_RATE)->where('active', 1)->select('id', 'category_id', 'rate_type_id', 'facility_id', 'price', 'description', 'details')->with(['category' => function ($query) {
            $query->select(['id', 'category']);
        }])->groupBy('category_id', 'id');
    }

    public function facilityRate()
    {
        return $this->hasMany('App\Models\FacilityRate');
    }

    public function permitRate()
    {
        return $this->hasMany('App\Models\PermitRate');
    }


    public function user()
    {
        return $this->belongsTo('App\Models\User', 'owner_id');
    }

    public function facilityCouponThreshold()
    {
        return $this->hasMany('App\Models\CouponThreshold');
    }


    public function events()
    {
        return $this->belongsToMany('App\Models\Event');
    }

    public function hasActiveEvent($arrival, $lengthOfStay)
    {
        $arrival = Carbon::createFromTimestamp(strtotime($arrival));
        $exit    = Carbon::createFromTimestamp(strtotime($arrival))->addHours($lengthOfStay);

        if (!$this->events) {
            return false;
        }

        /*foreach ($this->events as $event) {
      if ($event->isActiveEventTime($arrival) && $event->isActiveEventTime($exit)) {
        return true;
      }
    }*/

        return false;
    }

    public function hoursOfOperation()
    {
        return $this->hasMany('App\Models\HoursOfOperation', 'facility_id');
    }

    // Returns base rate or base event rate, depending on if there is an event going on
    public function fallbackRateEventPrice($length_of_stay)
    {
        return $this->eventRatePrice($length_of_stay);
    }

    // Returns base rate or base event rate, depending on if there is an event going on
    public function fallbackRatePrice($length_of_stay)
    {
        return $this->baseRatePrice($length_of_stay);
    }

    public function baseRatePrice($length_of_stay)
    {
        // TODO: What to do in the case that this rate is 0 or does not exist?
        return $this->base_rate * (ceil($length_of_stay / 24));
    }

    public function eventRatePrice($length_of_stay)
    {
        // Default to 24 hour time period for event rates
        $event_rate_hours = $this->base_event_rate_max_stay ?: 24;

        // TODO: What to do in the case that this rate is 0 or does not exist?
        return $this->base_event_rate * (ceil($length_of_stay / $event_rate_hours));
    }

    public function neighborhood()
    {
        return $this->belongsTo('App\Models\Neighborhood', 'neighborhood_id');
    }

    /**
     * Get message body to send in emails and
     * text messages that request information about this facility
     *
     * @return [type] [description]
     */
    public function messageBody()
    {
        return View::make('facility.info', ['facility' => $this, 'location' => $this->geolocations])->render();
    }

    /**
     * Get the optimal rate for a given reservation time and length, and whether or not the user is using the bonus
     * reservation time for that facility
     */
    public function rateForReservation(string $arrivalTime, $length, $useBonus = false, $useOverstay = false, $reservation_id = null, $payment = false, $bonusRequest = [], $coupon_threshold_price = 0, $is_coupon_threshold_price_percentage = 0)
    {
        if ($useBonus && !$payment) {
            $length = $length + self::RESERVATION_BONUS_HOURS;
        }


        $search = new RateSearch([$this->id], $arrivalTime, $length, false, false, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage);

        if (!$search->facilities || !count($search->facilities)) {
            return false;
        }

        $result = $search->facilities[0];


        if (!$result->rate_id) {
            $coupon_threshold_price_return = self::DEFAULT_VAL;
            $is_coupon_threshold_applied_return = self::DEFAULT_VAL;

            if ($useBonus && $coupon_threshold_price > self::DEFAULT_VAL) {
                $coupon_threshold_price_return = $coupon_threshold_price;
                $is_coupon_threshold_applied_return = self::DEFAULT_VAL_ONE;
            }

            $is_event = isset($result->is_event) ? $result->is_event : '0';
            $rate = ['price' => $result->price, 'facility' => $this->load('geolocations', 'photos'), 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => $coupon_threshold_price_return, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => $is_coupon_threshold_applied_return, 'coupon_rate_id' => self::DEFAULT_VAL];
        } else {

            $rate = Rate::find($result->rate_id);
            $couponPrice = self::DEFAULT_VAL;
            //adding threshold if coupon rate got selected and threshold was applied
            if ($coupon_threshold_price > self::DEFAULT_VAL) {
                if ((isset($rate->rate_type_id)) && ($rate->rate_type_id == self::COUPON_RATE)) {
                    $couponPrice =  $rate->price;
                    $rate->coupon_rate_id = $rate->id;
                    if (($is_coupon_threshold_price_percentage == self::PRICE_VAL) || ($is_coupon_threshold_price_percentage == self::RESERVATION_PRICE_VAL)) {
                        $rate->price = round($rate->price + ($coupon_threshold_price), 0);
                    } else if ($is_coupon_threshold_price_percentage == self::PERCENTAGE_VAL) {
                        $rate->price  = round($rate->price  + (($rate->price  * $coupon_threshold_price) / 100), 0);
                    }
                } else if (isset($result->is_coupon_applied_flag) && ($result->is_coupon_applied_flag > self::DEFAULT_VAL)) {
                    $rate->coupon_rate_id = isset($result->with_coupon_price_id) ? $result->with_coupon_price_id : self::DEFAULT_VAL;
                    $couponPrice = isset($result->with_coupon_price_val) ? $result->with_coupon_price_val : self::DEFAULT_VAL;
                }

                //returning coupon threshold  value
                if (isset($rate->rate_type_id)) {
                    if (($is_coupon_threshold_price_percentage == self::PRICE_VAL) || ($is_coupon_threshold_price_percentage == self::RESERVATION_PRICE_VAL)) {
                        $rate->coupon_threshold_price = (string)round($coupon_threshold_price, 0);
                        $rate->is_coupon_threshold_price_percentage = self::DEFAULT_VAL;
                    } else if ($is_coupon_threshold_price_percentage == self::PERCENTAGE_VAL) {
                        $rate->coupon_threshold_price = (string)round((($couponPrice * $coupon_threshold_price) / 100), 0);
                        $rate->is_coupon_threshold_price_percentage = self::DEFAULT_VAL_ONE;
                    }
                } else {
                    $rate->coupon_threshold_price = self::DEFAULT_VAL;
                    $rate->is_coupon_threshold_applied = self::DEFAULT_VAL;
                }
            } else {
                $rate->coupon_threshold_price = self::DEFAULT_VAL;
                $rate->is_coupon_threshold_applied = self::DEFAULT_VAL;
            }

            if ((isset($rate->rate_type_id)) && ($rate->rate_type_id == self::COUPON_RATE)) {
                $rate->coupon_rate_id = $rate->id;
            }

            if (!$rate) {
                return false;
            }

            $rate->load('rateType', 'category', 'facility', 'facility.geolocations', 'facility.photos');

            $rate = $rate->toArray();

            if ($rate['price'] > $couponPrice) {
                $rate['coupon_price'] = $couponPrice;
            } else {
                $rate['coupon_price'] = self::DEFAULT_VAL;
            }
        }

        $monthly_rates = array();
        $monthly_rate = 0;
        $monthly_rates_counter = 0;
        $monthlyTaxPrice = 0;
        $monthlyNetPrice = 0;
        /**   if(isset($this->id))
      {
       foreach ($this->facilityRate as $facilityRate) {

        if($facilityRate->active == 1)
        {
           //calulation net price
           if(isset($this->tax_rate) && ($this->tax_rate>0))
           {
               $monthlyNetPrice = (double)(($facilityRate->rate)/(1+$this->tax_rate));
               //calulation tax rate price
               $monthlyTaxPrice = (double)(($monthlyNetPrice)*($this->tax_rate));
           }else{
               $monthlyNetPrice = $facilityRate->rate;
           }

           $monthly_rates[$monthly_rates_counter]['rate_type']=$facilityRate->rateDescription->name;
           $monthly_rates[$monthly_rates_counter]['rate'] = $facilityRate->rate;
           $monthly_rates[$monthly_rates_counter]['net_rate'] = number_format($monthlyNetPrice, 2,'.', '');
           $monthly_rates[$monthly_rates_counter]['tax_rate'] = number_format($monthlyTaxPrice,2, '.', '');
           $monthly_rates[$monthly_rates_counter]['active']=$facilityRate->active;
           $monthly_rates[$monthly_rates_counter]['facility_id']=$this->id;
           $monthly_rates[$monthly_rates_counter]['id']=$facilityRate->id;
           $monthly_rates[$monthly_rates_counter]['rate_description']=$facilityRate->rateDescription;
           
            if (((double)$monthlyNetPrice < $monthly_rate || $monthly_rate === 0) && $facilityRate->active == 1 && $monthlyNetPrice > 0) {
                $monthly_rate = round($monthlyNetPrice,2);
            }
            $monthly_rates_counter++;
        }
       }
      }**/
        $rate['facilityMonthlyTaxRates'] = $monthly_rates;
        $rate['monthly_rate'] = $monthly_rate;
        $rate['warning_on_reservation'] = isset($result->warning_on_reservation) ? $result->warning_on_reservation : 0;
        $rate['warning_on_reservation_msg'] = isset($result->warning_on_reservation_msg) ? $result->warning_on_reservation_msg : '';
        $rate['warning_on_reservation_msg_email'] = isset($result->warning_on_reservation_msg_email) ? $result->warning_on_reservation_msg_email : '';

        // Return early if the price is closed
        if ($rate['price'] == Rate::CLOSED_RATE) {
            return $rate;
        }


        /**    if ($useBonus) {
      $rate['price'] = $rate['price'] + $this->reservation_bonus_rate;
    } **/

        // Make sure our rate is formatted as a decimal
        // $rate['price'] = number_format($rate['price'], 2);
        if ($useBonus && isset($bonusRequest['is_filter']) && $bonusRequest['is_filter']) {
            $rate['price'] = ($rate['price']);
        } else if ($useBonus == '1' && !$payment) {
            $rate['price'] = $bonusRequest['price'];
        } else {
            $rate['price'] = ($rate['price']);
        }

        //adding ticketech_id as per app team request
        $rate['ticketech_id'] = $this->ticketech_id;
        //die("Hello");
        return $rate;
    }


    /* 
    $useOverstay =  (true, false) {overstay = true}
    $overnightChargeOrSkip =  (true, false) {overnightCharge = true, overnightCharge Skip = flase}
    $overnightPaymentDone =  (true, false) {overnightPaymentDone = true overnightPayment not done = false}
  */

    public function rateForReservationOnMarker(string $arrivalTime, $length, $useBonus = false, $useOverstay = false, $reservation_id = null, $payment = false, $coupon_threshold_price = 0, $is_coupon_threshold_price_percentage = 0, $isMember = 0, $overnightChargeOrSkip = false, $overnightPaymentDone = false)
    {
        if ($useBonus && !$payment) {

            $length = $length + self::RESERVATION_BONUS_HOURS;
        }

        //$search = new RateSearch([$this->id], $arrivalTime, $length);
        $search = new RateSearchAtlanta([$this->id], $arrivalTime, $length, false, false, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage, $isMember, $useOverstay, $overnightChargeOrSkip, $overnightPaymentDone);
        if (!$search->facilities || !count($search->facilities)) {
            return false;
        }

        $result = $search->facilities[0];
        \Log::error("rateForReservationOnMarker 2222 ");
        \Log::error(json_encode($result));
        if (!$result->rate_id) {
            $is_event = isset($result->is_event) ? $result->is_event : '0';
            $facilityOnBoardRate = facility::where('id', $search->facility->id)->with(['facilityConfiguration', 'facilityOnBoardRate', 'im30FacilityConfiguration'])->first();
            $rate = ['price' => $result->price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $facilityOnBoardRate];
        } else {
            $rate = Rate::find($result->rate_id);
            $couponPrice = self::DEFAULT_VAL;
            //adding threshold if coupon rate got selected and threshold was applied
            if ($coupon_threshold_price > self::DEFAULT_VAL) {
                if ((isset($rate->rate_type_id)) && ($rate->rate_type_id == self::COUPON_RATE)) {
                    $couponPrice =  $rate->price;
                    $rate->coupon_rate_id = $rate->id;
                    if (($is_coupon_threshold_price_percentage == self::PRICE_VAL) || ($is_coupon_threshold_price_percentage == self::RESERVATION_PRICE_VAL)) {
                        $rate->price = round($rate->price + ($coupon_threshold_price), 0);
                    } else if ($is_coupon_threshold_price_percentage == self::PERCENTAGE_VAL) {
                        $rate->price  = round($rate->price  + (($rate->price  * $coupon_threshold_price) / 100), 0);
                    }
                } else if (isset($result->is_coupon_applied_flag) && ($result->is_coupon_applied_flag > self::DEFAULT_VAL)) {
                    $rate->coupon_rate_id = isset($result->with_coupon_price_id) ? $result->with_coupon_price_id : self::DEFAULT_VAL;
                    $couponPrice = isset($result->with_coupon_price_val) ? $result->with_coupon_price_val : self::DEFAULT_VAL;
                }

                //returning coupon threshold  value
                if (isset($rate->rate_type_id)) {
                    if (($is_coupon_threshold_price_percentage == self::PRICE_VAL) || ($is_coupon_threshold_price_percentage == self::RESERVATION_PRICE_VAL)) {
                        $rate->coupon_threshold_price = (string)round($coupon_threshold_price, 0);
                        $rate->is_coupon_threshold_price_percentage = self::DEFAULT_VAL;
                    } else if ($is_coupon_threshold_price_percentage == self::PERCENTAGE_VAL) {
                        $rate->coupon_threshold_price = (string)round((($couponPrice * $coupon_threshold_price) / 100), 0);
                        $rate->is_coupon_threshold_price_percentage = self::DEFAULT_VAL_ONE;
                    }
                } else {
                    $rate->coupon_threshold_price = self::DEFAULT_VAL;
                    $rate->is_coupon_threshold_applied = self::DEFAULT_VAL;
                }
            } else {
                $rate->coupon_threshold_price = self::DEFAULT_VAL;
                $rate->is_coupon_threshold_applied = self::DEFAULT_VAL;
            }

            if ((isset($rate->rate_type_id)) && ($rate->rate_type_id == self::COUPON_RATE)) {
                $rate->coupon_rate_id = $rate->id;
            }
            if (!$rate) {
                return false;
            }
            //$rate->load('rateType', 'category', 'facility', 'facility.geolocations', 'facility.photos');
            $rate->load('facility.facilityOnBoardRate', 'facility.facilityConfiguration', 'facility.Im30FacilityConfiguration'); //PMIS-13403

            $rate = $rate->toArray();
            if ($rate['price'] > $couponPrice) {
                $rate['coupon_price'] = $couponPrice;
            } else {
                $rate['coupon_price'] = self::DEFAULT_VAL;
            }
        }

        // Return early if the price is closed
        if ($rate['price'] == Rate::CLOSED_RATE) {
            return $rate;
        }

        // vijay to handel oversaty case 
        if (!$result->rate_type_id) {
            $rate['price'] = ($result->price);
            \Log::error("rateForReservationOnMarker 2222333333 " . $result->price);
        } else {
            $rate['price'] = ($rate['price']);
            \Log::error("rateForReservationOnMarker 22224444444 " . $rate['price']);
        }


        // Adding ticketech_id as per app team request
        $rate['ticketech_id'] = $this->ticketech_id;
        $rate['parkonect_id'] = $this->parkonect_id;

        $rate['warning_on_reservation'] = isset($result->warning_on_reservation) ? $result->warning_on_reservation : 0;
        $rate['warning_on_reservation_msg'] = isset($result->warning_on_reservation_msg) ? $result->warning_on_reservation_msg : '';
        $rate['warning_on_reservation_msg_email'] = isset($result->warning_on_reservation_msg_email) ? $result->warning_on_reservation_msg_email : '';

        return $rate;
    }


    public function rateForReservationOnMarkerTownsend(string $arrivalTime, $length, $useBonus = false, $useOverstay = false, $reservation_id = null, $payment = false, $coupon_threshold_price = 0, $is_coupon_threshold_price_percentage = 0, $isMember = 0)
    {

        if ($useBonus && !$payment) {

            $length = $length + self::RESERVATION_BONUS_HOURS;
        }

        //$search = new RateSearch([$this->id], $arrivalTime, $length);
        $search = new RateSearchTownsend([$this->id], $arrivalTime, $length, false, false, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage, $isMember);
        if (!$search->facilities || !count($search->facilities)) {
            return false;
        }

        $result = $search->facilities[0];
        if (!$result->rate_id) {

            $is_event = isset($result->is_event) ? $result->is_event : '0';
            $facilityOnBoardRate = facility::where('id', $search->facility->id)->with('facilityOnBoardRate')->first();
            $rate = ['price' => $result->price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $facilityOnBoardRate];
        } else {
            $rate = Rate::find($result->rate_id);
            $couponPrice = self::DEFAULT_VAL;
            //adding threshold if coupon rate got selected and threshold was applied
            if ($coupon_threshold_price > self::DEFAULT_VAL) {
                if ((isset($rate->rate_type_id)) && ($rate->rate_type_id == self::COUPON_RATE)) {
                    $couponPrice =  $rate->price;
                    $rate->coupon_rate_id = $rate->id;
                    if (($is_coupon_threshold_price_percentage == self::PRICE_VAL) || ($is_coupon_threshold_price_percentage == self::RESERVATION_PRICE_VAL)) {
                        $rate->price = round($rate->price + ($coupon_threshold_price), 0);
                    } else if ($is_coupon_threshold_price_percentage == self::PERCENTAGE_VAL) {
                        $rate->price  = round($rate->price  + (($rate->price  * $coupon_threshold_price) / 100), 0);
                    }
                } else if (isset($result->is_coupon_applied_flag) && ($result->is_coupon_applied_flag > self::DEFAULT_VAL)) {
                    $rate->coupon_rate_id = isset($result->with_coupon_price_id) ? $result->with_coupon_price_id : self::DEFAULT_VAL;
                    $couponPrice = isset($result->with_coupon_price_val) ? $result->with_coupon_price_val : self::DEFAULT_VAL;
                }

                //returning coupon threshold  value
                if (isset($rate->rate_type_id)) {
                    if (($is_coupon_threshold_price_percentage == self::PRICE_VAL) || ($is_coupon_threshold_price_percentage == self::RESERVATION_PRICE_VAL)) {
                        $rate->coupon_threshold_price = (string)round($coupon_threshold_price, 0);
                        $rate->is_coupon_threshold_price_percentage = self::DEFAULT_VAL;
                    } else if ($is_coupon_threshold_price_percentage == self::PERCENTAGE_VAL) {
                        $rate->coupon_threshold_price = (string)round((($couponPrice * $coupon_threshold_price) / 100), 0);
                        $rate->is_coupon_threshold_price_percentage = self::DEFAULT_VAL_ONE;
                    }
                } else {
                    $rate->coupon_threshold_price = self::DEFAULT_VAL;
                    $rate->is_coupon_threshold_applied = self::DEFAULT_VAL;
                }
            } else {
                $rate->coupon_threshold_price = self::DEFAULT_VAL;
                $rate->is_coupon_threshold_applied = self::DEFAULT_VAL;
            }

            if ((isset($rate->rate_type_id)) && ($rate->rate_type_id == self::COUPON_RATE)) {
                $rate->coupon_rate_id = $rate->id;
            }
            if (!$rate) {
                return false;
            }
            //$rate->load('rateType', 'category', 'facility', 'facility.geolocations', 'facility.photos');
            $rate->load('facility.facilityOnBoardRate');

            $rate = $rate->toArray();
            if ($rate['price'] > $couponPrice) {
                $rate['coupon_price'] = $couponPrice;
            } else {
                $rate['coupon_price'] = self::DEFAULT_VAL;
            }
        }

        // Return early if the price is closed
        if ($rate['price'] == Rate::CLOSED_RATE) {
            return $rate;
        }

        $rate['price'] = ($rate['price']);

        // Adding ticketech_id as per app team request
        $rate['ticketech_id'] = $this->ticketech_id;
        $rate['parkonect_id'] = $this->parkonect_id;

        $rate['warning_on_reservation'] = isset($result->warning_on_reservation) ? $result->warning_on_reservation : 0;
        $rate['warning_on_reservation_msg'] = isset($result->warning_on_reservation_msg) ? $result->warning_on_reservation_msg : '';
        $rate['warning_on_reservation_msg_email'] = isset($result->warning_on_reservation_msg_email) ? $result->warning_on_reservation_msg_email : '';

        return $rate;
    }


    public function rateForReservationSideMap(string $arrivalTime, $length, $useBonus = false, $useOverstay = false, $reservation_id = null, $payment = false, $coupon_threshold_price = 0, $is_coupon_threshold_price_percentage = 0)
    {

        if ($useBonus && !$payment) {
            $length = $length + self::RESERVATION_BONUS_HOURS;
        }


        //$search = new RateSearch([$this->id], $arrivalTime, $length);
        $search = new RateSearch([$this->id], $arrivalTime, $length, false, false, false, $coupon_threshold_price, $is_coupon_threshold_price_percentage);

        if (!$search->facilities || !count($search->facilities)) {
            return false;
        }

        $result = $search->facilities[0];

        if (!$result->rate_id) {

            $is_event = isset($result->is_event) ? $result->is_event : '0';
            $rate = ['price' => $result->price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL];
        } else {
            $rate = Rate::find($result->rate_id);
            $couponPrice = self::DEFAULT_VAL;
            //adding threshold if coupon rate got selected and threshold was applied
            if ($coupon_threshold_price > self::DEFAULT_VAL) {
                if ((isset($rate->rate_type_id)) && ($rate->rate_type_id == self::COUPON_RATE)) {
                    $couponPrice =  $rate->price;
                    $rate->coupon_rate_id = $rate->id;
                    if (($is_coupon_threshold_price_percentage == self::PRICE_VAL) || ($is_coupon_threshold_price_percentage == self::RESERVATION_PRICE_VAL)) {
                        $rate->price = round($rate->price + ($coupon_threshold_price), 0);
                    } else if ($is_coupon_threshold_price_percentage == self::PERCENTAGE_VAL) {
                        $rate->price  = round($rate->price  + (($rate->price  * $coupon_threshold_price) / 100), 0);
                    }
                } else if (isset($result->is_coupon_applied_flag) && ($result->is_coupon_applied_flag > self::DEFAULT_VAL)) {
                    $rate->coupon_rate_id = isset($result->with_coupon_price_id) ? $result->with_coupon_price_id : self::DEFAULT_VAL;
                    $couponPrice = isset($result->with_coupon_price_val) ? $result->with_coupon_price_val : self::DEFAULT_VAL;
                }

                //returning coupon threshold  value
                if (isset($rate->rate_type_id)) {
                    if (($is_coupon_threshold_price_percentage == self::PRICE_VAL) || ($is_coupon_threshold_price_percentage == self::RESERVATION_PRICE_VAL)) {
                        $rate->coupon_threshold_price = (string)round($coupon_threshold_price, 0);
                        $rate->is_coupon_threshold_price_percentage = self::DEFAULT_VAL;
                    } else if ($is_coupon_threshold_price_percentage == self::PERCENTAGE_VAL) {
                        $rate->coupon_threshold_price = (string)round((($couponPrice * $coupon_threshold_price) / 100), 0);
                        $rate->is_coupon_threshold_price_percentage = self::DEFAULT_VAL_ONE;
                    }
                } else {
                    $rate->coupon_threshold_price = self::DEFAULT_VAL;
                    $rate->is_coupon_threshold_applied = self::DEFAULT_VAL;
                }
            } else {
                $rate->coupon_threshold_price = self::DEFAULT_VAL;
                $rate->is_coupon_threshold_applied = self::DEFAULT_VAL;
            }

            if ((isset($rate->rate_type_id)) && ($rate->rate_type_id == self::COUPON_RATE)) {
                $rate->coupon_rate_id = $rate->id;
            }
            if (!$rate) {
                return false;
            }

            $rate = $rate->toArray();
            if ($rate['price'] > $couponPrice) {
                $rate['coupon_price'] = $couponPrice;
            } else {
                $rate['coupon_price'] = self::DEFAULT_VAL;
            }
        }

        $rate['facility_id'] = $result->facility_id;
        // Return early if the price is closed
        if ($rate['price'] == Rate::CLOSED_RATE) {
            return $rate;
        }

        $rate['facility_id'] = $result->facility_id;

        $rate['price'] = ($rate['price']);

        // Adding ticketech_id as per app team request
        $rate['ticketech_id'] = $this->ticketech_id;

        return $rate;
    }


    public function getOpen247Attribute()
    {
        $this->hoursOfOperation->count();

        if ($this->hoursOfOperation->count() === 0) {
            return true;
        }

        if ($this->hoursOfOperation->count() < 7) {
            return false;
        }

        // Loop through each day and check if the facility is open all day that day
        foreach (range(0, 6) as $day) {
            if (!($hours = $this->hoursOfOperation()->where('day_of_week', $day)->first())) {
                return false;
            }

            if (!$hours->all_day) {
                return false;
            }
        }

        return true;
    }

    /**
     * Hours of operation can be greater than 24 hours, e.g. start at 7:00:00 and end at 28:00:00
     * so we need to check for this and return hours whose day of week may not exactly equal the day of
     * week we are searching for
     */
    public function hoursForDay($dayOfWeek)
    {
        return $this->hoursOfOperation->filter(
            function ($hours) use ($dayOfWeek) {
                if ($hours->day_of_week === $dayOfWeek) {
                    return true;
                }
                return $hours->day_of_week === ($dayOfWeek - 1) && (int)$hours->close_hour > 24;
            }
        );
    }

    public function generateStaticMap($height = 200, $width = 200, $zoom = 14)
    {
        $googleMap = new GoogleMaps();
        $googleMap->setGeolocation($this->geolocations->latitude, $this->geolocations->longitude);
        $googleMap->setMapSize($width, $height);
        $googleMap->setZoom($zoom);
        return $googleMap->generateQueryString();
    }

    public function generateStaticMapReservationEmail($height = 162, $width = 300, $zoom = 14)
    {
        $googleMap = new GoogleMaps();
        $googleMap->setGeolocation($this->geolocations->latitude, $this->geolocations->longitude);
        $googleMap->setMapSize($width, $height);
        $googleMap->setZoom($zoom);
        return $googleMap->generateQueryString();
    }

    public function generateAddressLink()
    {
        $googleMap = new GoogleMaps();
        return $googleMap->generateAddressLink($this);
    }

    public function availabilities()
    {
        return $this->hasMany(FacilityAvailability::class, 'facility_id', 'id');
    }

    public function inventories()
    {
        return $this->hasMany(FacilityInventory::class, 'facility_id', 'id');
    }

    public function fetchAvailabilityFromTicketech()
    {
        return true;
    }
    public function fetchAvailabilityFromTicketechSideMap()
    {

        return true;
    }
    // Returns availability for monthly campaign
    public function getMonthlyCampaignAvailabilityAttribute()
    {
        return MonthlyCampaign::where('facility_id', $this->id)->first();
    }

    // Returns availability for Partner percentage
    public static function calculatePartnerAvailability($inventory, $availability_partner, $facility_id)
    {
        $facilityDetails = Facility::where('id', $facility_id)->first();
        $new_availability_partner = [];
        if (!$facilityDetails) {
            return $new_availability_partner;
        }
        if ($availability_partner) {
            foreach ($availability_partner as $key => $value) {

                if ($facilityDetails->inventory_threshold <= self::DEFAULT_INVENTORY_THRESHOLD) {
                    $new_availability_partner[$key] = self::DEFAULT_INVENTORY_THRESHOLD;
                } else {
                    if ($inventory) {
                        $new_availability_partner[$key] = floor((($inventory[$key] * $facilityDetails->inventory_threshold) / 100 - ($inventory[$key] - $value)));
                    }
                    /*if($new_availability_partner[$key] < 0){
                  $new_availability_partner[$key] = self::DEFAULT_INVENTORY_THRESHOLD;
                }*/
                }
            }
            return $new_availability_partner;
        } else {
            return $new_availability_partner;
        }
    }

    public function FacilityPaymentDetails()
    {
        return $this->hasOne('App\Models\ParkEngage\FacilityPaymentDetail', 'facility_id');
    }

    public function service_data()
    {
        return $this->belongsToMany(PermitServices::class, 'permit_services_facility_mapping', 'facility_id', 'permit_service_id');
    }

    public function faciltyBrandSetting()
    {
        return $this->hasOne('App\Models\ParkEngage\FacilityBrandSetting', 'facility_id');
    }

    public function getAnimationGifUrlAttribute()
    {
        return config('parkengage.APP_URL') . "/facility-animation-gifs/" . $this->animation_gif_name;
    }

    public function parkingDevice()
    {
        $parkingDevice = ParkingDevice::where("facility_id", $this->id)->first();
        if ($parkingDevice) {
            $data['is_device_facility'] = '1';
        } else {
            $data['is_device_facility'] = '0';
        }
        return $data;
    }

    public function parkingDevices()
    {
        return $this->hasOne('App\Models\ParkEngage\ParkingDevice', 'facility_id');
    }

    public function rateForReservationByPassRateEngine(string $arrivalTime, $length, $useBonus = false, $useOverstay = false, $reservation_id = null, $payment = false, $coupon_threshold_price = 0, $is_coupon_threshold_price_percentage = 0, $isMember = 0)
    {
        \Log::error("rateForReservationByPassRateEngine lngth " . $length);
        //prod time
        $checkDate = "2025-01-01";
        //staging time
        //$checkDate = "2025-12-24";
        $currentDate = date("Y-m-d");
        $arrivalDate = date("Y-m-d", strtotime($arrivalTime));
        $dailyMaxRate = $this->rate_daily_max_amount;
        if ($this->id == "125" || $this->id == "142") {
            if (strtotime($arrivalDate) < strtotime($checkDate)) {
                //worldport/catalina specific condition 
                $dailyMaxRate = 20.00;
            } else {
                $dailyMaxRate = $this->rate_daily_max_amount;
            }
        }
        $diff_min_in_mins = QueryBuilder::getLengthInMints($length);
        if ($length > 60) {
            \Log::error("inside lngth " . $length . '--00--' . $diff_min_in_mins);
            $length = QueryBuilder::getLengthInHours($diff_min_in_mins);
            \Log::error("inner lngth " . $length);
        }
        $weekday = date('N', strtotime($arrivalTime));
        if ($weekday > 6) {
            $weekday = 7 - $weekday;
        }

        $todayHoursOfOperation = '0';
        $twenty_four_hours = "0";
        $hoursOfOperation = HoursOfOperation::where('day_of_week', $weekday)->where('facility_id', $this->id)->get();
        if (count($hoursOfOperation) <= 0) {
            $allDayHoursOfOperation = HoursOfOperation::where('facility_id', $this->id)->get();
            if (count($allDayHoursOfOperation) <= 0) {
                $twenty_four_hours = "1";
            }
        } else {
            $todayHoursOfOperation = '1';
            $hoursOfOperation = HoursOfOperation::where('open_time', '00:00:00')->whereIn('close_time', array('23:59:59', '24:00:00', '25:00:00', '26:00:00', '27:00:00', '28:00:00', '29:00:00', '30:00:00'))->where('facility_id', $this->id)->where('day_of_week', $weekday)->get();
            if (count($hoursOfOperation) > 0) {
                $twenty_four_hours = "1";
            }
        }

        $is_event = '0';
        $price = 0.00;
        $minutes = 0;
        //overstay case start
        if ($useOverstay == true) {
            $upperLength = ceil($length);
            if ($upperLength > $this->rate_duration_in_hours) {
                $remainder = $upperLength % $this->rate_duration_in_hours;

                if ($remainder == 0) {
                    $rateDurationInHours = number_format($upperLength / $this->rate_duration_in_hours, 2);
                    //$price = number_format($upperLength * $this->rate_daily_max_amount, 2);
                    if ($rateDurationInHours <= 1) {
                        //if day is less than equal to 1(24 hours)
                        $price = number_format($upperLength * $this->rate_per_hour, 2);
                        if ($price > $dailyMaxRate) {
                            $price = number_format($dailyMaxRate, 2);
                        } else {
                            $price = number_format($price - $this->rate_per_hour, 2);
                        }
                        $rate = ['price' => $price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
                        return $rate;
                    } else {
                        $remainder = $upperLength % $this->rate_duration_in_hours;
                        if ($remainder == 0) {
                            $price = number_format($rateDurationInHours * $dailyMaxRate, 2);
                        } else {
                            $beforeRemainder = $upperLength - $remainder;
                            $beforeRemainderDays = $beforeRemainder / $this->rate_duration_in_hours;
                            $price = ($beforeRemainderDays * $dailyMaxRate);

                            $remainderAmount = ($remainder * $this->rate_per_hour);
                            if ($remainderAmount > $dailyMaxRate) {
                                $price = $price + $dailyMaxRate;
                            } else {
                                $price = ($price + ($remainder * $this->rate_per_hour));
                            }
                        }

                        $rate = ['price' => ($price), 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
                        return $rate;
                    }
                } else {
                    $beforeRemainder = $upperLength - $remainder;
                    $beforeRemainderDays = $beforeRemainder / $this->rate_duration_in_hours;
                    $price = ($beforeRemainderDays * $dailyMaxRate);

                    $remainderAmount = number_format($remainder * $this->rate_per_hour, 2);
                    if ($remainderAmount > $dailyMaxRate) {
                        $price = ($price + $dailyMaxRate);
                    } else {
                        $price = ($price + ($remainder * $this->rate_per_hour));
                    }
                }
                $rate = ['price' => ($price), 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
                return $rate;
            }
            $price = number_format($upperLength * $this->rate_per_hour, 2);
            if ($price > $dailyMaxRate) {
                $price = number_format($dailyMaxRate, 2);
            } else {
                $price = number_format($price, 2);
            }
            $rate = ['price' => $price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
            return $rate;
        }
        //overstay case end
        \Log::error($this->rate_free_minutes . "--1");
        if ($this->rate_free_minutes >= 60) {
            $diff_min_in_hours = QueryBuilder::getLengthInHours($this->rate_free_minutes);
            \Log::error("--2-" . $diff_min_in_hours . "--3");
        } else {
            $freeMinutes = (int) $this->rate_free_minutes;
            \Log::error("--4-" . $freeMinutes . "--5");
            // dd($freeMinutes);
            if ($freeMinutes < 10) {
                $minutes = ".0" . $freeMinutes;
            } else {
                $minutes = "." . $freeMinutes;
            }
            \Log::error("--6-" . $minutes . "--7");
            // dd($freeMinutes, $length, $diff_min_in_mins);
            if ($length >= $minutes) {
                $length = $length - $minutes;
            } else {
                $price = ($price);
                $rate = ['price' => $price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
                return $rate;
            }
            \Log::error("--8-" . $length . "--9");
            $upperLength = ceil($length);
            // dd($upperLength, $this->rate_duration_in_hours);

            if ($upperLength > $this->rate_duration_in_hours) {
                $remainder = $upperLength % $this->rate_duration_in_hours;
                \Log::error("--10-" . $upperLength . "--11--" . $this->rate_duration_in_hours . '--12--' . $remainder);
                if ($remainder == 0) {
                    $rateDurationInHours = ($upperLength / $this->rate_duration_in_hours);
                    //$price = number_format($upperLength * $this->rate_daily_max_amount, 2);
                    if ($rateDurationInHours <= 1) {
                        //if day is less than equal to 1(24 hours)
                        $price = ($upperLength * $this->rate_per_hour);
                        if ($price > $dailyMaxRate) {
                            $price = ($dailyMaxRate);
                        } else {
                            $price = ($price - $this->rate_per_hour);
                        }
                        $rate = ['price' => $price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
                        return $rate;
                    } else {
                        $remainder = $upperLength % $this->rate_duration_in_hours;
                        if ($remainder == 0) {
                            $price = ($rateDurationInHours * $dailyMaxRate);
                        } else {
                            $beforeRemainder = $upperLength - $remainder;
                            $beforeRemainderDays = $beforeRemainder / $this->rate_duration_in_hours;
                            $price = ($beforeRemainderDays * $dailyMaxRate);

                            $remainderAmount = ($remainder * $this->rate_per_hour);
                            if ($remainderAmount > $dailyMaxRate) {
                                $price = number_format($price + $dailyMaxRate, 2);
                            } else {
                                $price = ($price + ($remainder * $this->rate_per_hour));
                            }
                        }

                        $rate = ['price' => $price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
                        return $rate;
                    }
                } else {
                    $beforeRemainder = $upperLength - $remainder;
                    $beforeRemainderDays = $beforeRemainder / $this->rate_duration_in_hours;
                    $price = ($beforeRemainderDays * $dailyMaxRate);

                    \Log::error("--13-" . $upperLength . "--133--" . $this->rate_duration_in_hours . '--14--' . $remainder . '--15--' . $beforeRemainder . '--16--' . $beforeRemainderDays . '--17--' . $price);

                    $remainderAmount = ($remainder * $this->rate_per_hour);
                    if ($remainderAmount > $dailyMaxRate) {
                        \Log::error("--20-" . $remainderAmount . "--21--" . $dailyMaxRate . '--22--' . $remainderAmount);
                        $price = ($price + $dailyMaxRate);
                        \Log::error("--20-" . $remainderAmount . "--21--" . $dailyMaxRate . '--22--' . $remainderAmount . '--23--' . $price);
                    } else {
                        \Log::error("--24-" . $remainderAmount . "--25--" . $dailyMaxRate . '--26--' . $remainderAmount . '--27--' . $price);
                        //$price = number_format($price + number_format($remainder * $this->rate_per_hour, 2), 2);
                        $price = ($price + ($remainder * $this->rate_per_hour));
                        \Log::error("--29-" . $price . "--25--" . $remainder . '--30--' . $remainderAmount . '--31--' . $price);
                    }
                }
                \Log::error("--28-" . $price);
                $rate = ['price' => $price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
                return $rate;
            }


            $price = number_format($upperLength * $this->rate_per_hour, 2);
            if ($price > $dailyMaxRate) {
                $price = number_format($dailyMaxRate, 2);
            } else {
                $price = number_format($price, 2);
            }
            $rate = ['price' => $price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
            return $rate;
        }
        // dd($rate, $diff_min_in_mins, $twenty_four_hours);
        // dd($diff_min_in_hours);
        if ($length <= $diff_min_in_hours) {
            //for free mins
            $rate = ['price' => "0.00", 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
            return $rate;
        } else {
            //for above free minutes
            $upperLength = ceil($length);
            $rateDurationInHours = ($upperLength / $this->rate_duration_in_hours);
            if ($rateDurationInHours <= 1) {
                //if day is less than equal to 1(24 hours)
                $price = number_format($upperLength * $this->rate_per_hour, 2);
                if ($price > $dailyMaxRate) {
                    $price = number_format($dailyMaxRate, 2);
                } else {
                    $price = number_format($price - $this->rate_per_hour, 2);
                }
                $rate = ['price' => $price, 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
                return $rate;
            } else {
                $remainder = $upperLength % $this->rate_duration_in_hours;
                if ($remainder == 0) {
                    $price = number_format($rateDurationInHours * $dailyMaxRate, 2);
                } else {
                    $beforeRemainder = $upperLength - $remainder;
                    $beforeRemainderDays = $beforeRemainder / $this->rate_duration_in_hours;
                    $price = ($beforeRemainderDays * $dailyMaxRate);

                    $remainderAmount = number_format($remainder * $this->rate_per_hour, 2);
                    if ($remainderAmount > $dailyMaxRate) {
                        $price = ($price + $dailyMaxRate);
                    } else {
                        $price = ($price + ($remainder * $this->rate_per_hour));
                    }
                }

                $rate = ['price' => ($price), 'rate_type_id' => false, 'is_event' => $is_event, 'coupon_threshold_price' => self::DEFAULT_VAL, 'coupon_price_applied' => self::DEFAULT_VAL, 'is_coupon_threshold_applied' => self::DEFAULT_VAL, 'facility' => $this, 'todayHoursOfOperation' => $todayHoursOfOperation, 'twenty_four_hours' => $twenty_four_hours];
                return $rate;
            }
        }
    }

    // copyed the Processing Fees function from ticket model to find the Multiple processing fees
    public function getProcessingFee($processingFeeType = null)
    {
        $processingFee = 0;
        switch ($processingFeeType) {
            case (self::DRIVE_UP_PROCESSING_FEE):
                $processingFee = $this->drive_up_processing_fee == '' ? 0.00 : $this->drive_up_processing_fee;
                break;
            case (self::PROCESSING_FEE):
                $processingFee = $this->processing_fee == '' ? 0.00 : $this->processing_fee;
                break;
            case (self::CITATION_PROCESSING_FEE):
                $processingFee = $this->citation_processing_fee == '' ? 0.00 : $this->citation_processing_fee;
                break;
            default:
                $processingFee = $this->drive_up_processing_fee == '' ? 0.00 : $this->drive_up_processing_fee;
                break;
        }
        return $processingFee;
    }

    // copyed the Tax Rate function from ticket model to find the tax in % and amount
    function getTaxRate($rate)
    {
        $tax_rate =  0;
        if ($this->tax_rate_type == '0') {
            if ($this->tax_rate > 0) {
                $tax_rate = $this->tax_rate;
            }
        } else {
            if ($this->tax_rate > 0) {
                $tax_rate = number_format((($rate['price'] * $this->tax_rate) / 100), 2);
            }
        }
        return $tax_rate;
    }

    public function getFacilityLogoUrlAttribute()
    {
        return config('parkengage.APP_URL') . "/facility-logo/" . $this->logo;
    }

    public function faciltyPaymentDetail()
    {
        return $this->hasOne('App\Models\ParkEngage\FacilityPaymentDetail', 'facility_id');
    }

    public function generateRandomString($length = 10)
    {
        $randomString = sha1(uniqid(rand(), true));
        $randomString = substr($randomString, 0, $length);
        return $randomString;
    }

    public function gates()
    {
        return $this->hasMany('App\Models\ParkEngage\Gate');
    }

    public function facilityConfiguration()
    {
        return $this->hasOne('App\Models\ParkEngage\FacilityConfiguration', 'facility_id');
    }

    /* 
  $isExitDay  like ['Monday','Tuesday','Wednesday','thursday','Friday','Saturaday','Sunday']
   */
    public function getAllOvernightPrice($isExitDay = null)
    {
        if ($isExitDay != null) {
            return $this->hasMany('App\Models\Rate', 'facility_id')->where(['rate_type_id' => '1', 'category_id' => '43', 'active' => '1', '' . $isExitDay . '' => '1'])->get();
        } else {
            return $this->hasMany('App\Models\Rate', 'facility_id')->where(['category_id' => '43', 'active' => '1'])->get();
        }
    }

    public function PromoProcesingFee($processingFeeType, $request, $rate, $parkingAmount, $PromoResposen)
    {
        $processingFee = 0;
        if ($rate['max_stay'] < $PromoResposen->discount_in_hours) {
            $processingFee = $request['processing_fee'];
        } else if ($rate['max_stay'] > $PromoResposen->discount_in_hours) {
            switch ($processingFeeType) {
                case (self::DRIVE_UP_PROCESSING_FEE):
                    $processingFee = $this->drive_up_processing_fee == '' ? 0.00 : $this->drive_up_processing_fee;
                    break;
                case (self::PROCESSING_FEE):
                    $processingFee = $this->processing_fee == '' ? 0.00 : $this->processing_fee;
                    break;
                case (self::CITATION_PROCESSING_FEE):
                    $processingFee = $this->citation_processing_fee == '' ? 0.00 : $this->citation_processing_fee;
                    break;
                default:
                    $processingFee = $this->drive_up_processing_fee == '' ? 0.00 : $this->drive_up_processing_fee;
                    break;
            }
            $proRatedFee = 0;
            $proRatedFee = $processingFee / $rate['max_stay'];
            $processingFee = sprintf("%.2f", $request['length'] * $proRatedFee);
        }


        return $processingFee;
    }

    /***
     * Shalu:13/06/2024
     * Method to get payment type of facility
     * 
     * @return String
     */
    public function getPaymentType()
    {
        return FacilityPaymentType::join('facility_payment_details', 'facility_payment_type.id', '=', 'facility_payment_details.facility_payment_type_id')->where('facility_payment_details.facility_id', $this->id)->value('payment_type');
    }

    function getAdditionalFee($rate)
    {

        $addFee =  0;
        if ($this->additonal_fee_type == '0') {
            if ($this->additonal_fee > 0) {
                $addFee = $this->additonal_fee;
            }
        } else {
            if ($this->additonal_fee > 0) {
                $addFee = number_format((($rate['price'] * $this->additonal_fee) / 100), 2);
            }
        }
        return $addFee;
    }

    function getAdditionalFeeName()
    {
        $feeName = 'CCFee';
        if ($this->owner_id == '215900') {
            $feeName = 'CCFee';
        }
        return $feeName;
    }

    function getSurchargeFee($rate)
    {

        $addFee =  0;
        if ($this->surcharge_fee_type == '0') {
            if ($this->surcharge_fee > 0) {
                $addFee = $this->surcharge_fee;
            }
        } else {
            if ($this->surcharge_fee > 0) {
                $addFee = number_format((($rate['price'] * $this->surcharge_fee) / 100), 2);
            }
        }
        return $addFee;
    }

    function getSurchargeName()
    {

        $feeName = 'City Surcharge';
        if ($this->owner_id == '215900') {
            $feeName = 'City Surcharge';
        }
        return $feeName;
    }
    //PMIS-13403
    public function im30FacilityConfiguration()
    {
        return $this->hasOne('App\Models\ParkEngage\ImThrityFacilityConfiguration', 'facility_id');
    }

    /**
     * Get facility details with payment details
     *
     * @param int $facilityId
     * @param int $partnerid
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public static function getFacilityDetails($facilityId, $partnerid)
    {
        return Facility::with(['FacilityPaymentDetails'])->where('id', $facilityId)->where('owner_id', $partnerid)->first();
    }
}
