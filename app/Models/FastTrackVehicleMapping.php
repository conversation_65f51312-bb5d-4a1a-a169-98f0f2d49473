<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FastTrackVehicleMapping extends Model
{


    public $table = 'fast_track_vehicle_mapping';

    protected $fillable = [];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $appends = [];

    public $timestamps = false; // disable timestamps

    public function VehicleData()
    {
        return $this->belongsTo('App\Models\PermitVehicle', 'permit_vehicle_id');
    }

    public function fastTrackData()
    {
        return $this->belongsTo('App\Models\UserFastTrackDetails', 'fast_track_id');
    }
}
