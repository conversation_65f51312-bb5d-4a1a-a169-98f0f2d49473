<?php

namespace App\Models;

use App\Http\Helpers\QueryBuilder;
use App\Http\Requests\Request;
use DB;
use Carbon\Carbon;
use App\Models\HoursOfOperation;
use App\Models\ParkEngage\FacilityOvernightDuration;

use function GuzzleHttp\json_encode;

use App\Services\LoggerFactory;

/**
 * Not actually a laravel model - does not refer to a table
 * Used for searching for valid rates on rates, given a set of rates, an arrival time
 * and length of stay
 */
class RateSearchAtlanta
{
    public $facilities = [];
    public $facility;
    public $arrival_time;
    public $exitTime;
    public $length_of_stay;
    public $verbose;
    public $current_lowest_rate;
    public $current_lowest_rate_precise;
    public $highest_event_rate;
    public $highest_event_rate_precise;
    public $rate_description;
    public $fixed_rate;
    public $facility_open;
    public $is_24_hours_open;
    public $coupon_threshold_price;
    public $is_coupon_threshold_price_percentage;
    const COUPON_RATE   = 2;
    const PRICE_VAL   = 0;
    const PRICE_VAL_DEFAULT   = 0;
    const RESERVATION_PRICE_VAL   = 2;
    const PERCENTAGE_VAL   = 1;
    const DEFAULT_VAL   = 0;
    const DEFAULT_VAL_ONE   = 1;
    public $is_coupon_applied = 0;
    public $with_coupon_price = 0;
    public $with_coupon_price_id = 0;
    const FULL_DAY_HOUR_VAL = 24;
    const CONST_CLOSE_MIN_CHECK = 30;
    const CONST_CLOSEING_SOON_MIN_CHECK1 = 30;
    const ADD_MINUTES_FOR_DECIMAL_LENGTH = 30;
    const CONST_CLOSEING_SOON_MIN_CHECK2 = 60;
    public $is_closing_over_nightsoon = 0;
    public $is_closing_overnight_soon_msg = '';
    public $is_closing_overnight_soon_msg_email = '';

    const CLOSING_SOON_MSG = "Please note that this garage closes very close to your pickup time - see garage hours below. Are you sure you want to book this parking?";
    const OVERNIGHT_STAY_SOON_MSG = "Please note that this garage is not open during the entirety of your parking. The garage hours are shown below. You will not be able to drop off or pick up your vehicle while the location is closed. Are you sure you want to book this parking?";
    const OVERNIGHT_STAY_SOON_MSG_COMBINE = "Please note that this garage closes very close to your pickup time and this garage is not open during the entirety of your parking. The garage hours are shown below. You will not be able to drop off or pick up your vehicle while the location is closed. Are you sure you want to book this parking?";

    const CLOSING_SOON_MSG_EMAIL = "Please note that this garage closes very close to your pickup time - please check garage hours.";
    const OVERNIGHT_STAY_SOON_MSG_EMAIL = "Please note that this garage is not open during the entirety of your parking. Please check garage hours. You will not be able to drop off or pick up your vehicle while the location is closed.";
    const OVERNIGHT_STAY_SOON_MSG_COMBINE_EMAIL = "Please note that this garage closes very close to your pickup time and this garage is not open during the entirety of your parking. Please check garage hours. You will not be able to drop off or pick up your vehicle while the location is closed.";


    const CLOSING_SOON_ON = 1;

    const  FACILITY_AVAILABLE = "Facility available for reservation.";
    const  FACILITY_NOT_AVAILABLE = "Facility not available for reservation.";
    const  FACILITY_COMING_SOON = "Facility coming soon.";

    public  $is_member = 0;
    protected $is_overnight;
    protected $is_overnight_weekSkip;
    protected $overnightLength;
    protected $overnight_user_length;
    protected $full_day_length;
    protected $full_day_user_length;
    protected $after_overnight_length;


    protected $over_strat_time;
    protected $over_end_time;
    protected $is_same_day;
    public $overnight_current_rate;
    public $day_change_or_not_diff_inHours;
    public $new_length_for_next_day;

    protected $is_exit_overnight;
    protected $is_exit_normal;
    protected $is_entry_overnight;
    protected $is_entry_normal;
    protected $is_overnight_plus_normal_exit;
    protected $arrival_to_start_length;
    protected $tillOvernightLength;
    protected $overnighyDayBefore;
    protected $entryToMidnight;
    protected $nextDayStartTime;

    protected $first_price_lenght_duration;
    protected $exitIslowerToStartTime;
    protected $exitIslowerToEndTime;
    protected $exitIsgreaterToStartTime;
    protected $exitIsgreaterToEndTime;

    public $rate;
    public $rate_id;
    public $grace_length;
    public $remaningLength;
    public $monthlyRate;
    public $noOfDays;
    public $dayWisePrice;
    public $is_overnight_overstay;
    public $overnightChargeOrSkip;
    public $overnightPaymentDone;

    // Price Variables
    protected $dayStartTime;
    protected $dayEndTime;
    protected $dailyMaxPrice;
    protected $overnightPrice;
    protected $isOvernightSameday;

    public $woodmanEveningPrice;
    public $woodmanDayCount = 0;
    public $woodmanDayRemainder = 0;

    public $log;

    public function __construct($facilityIds = array(), $arrival_time = null, $length_of_stay = 4, $verbose = false, $fixed_rate = false, $is_24_hours_open = false, $coupon_threshold_price = 0, $is_coupon_threshold_price_percentage = 0, $is_member = 0, $is_overstay = false, $overnightChargeOrSkip = false, $overnightPaymentDone = false)
    {
        $this->log = (new LoggerFactory)->setPath('logs/rateSearchAtlanta/')->createLogger('rateSearchAtlnta');
        if (!count($facilityIds) || !$length_of_stay) {
            return null;
        }

        if (!$arrival_time) {
            $arrival_time = date('H:i:s', time() + 3600);
        }
        $this->arrival_time = $arrival_time;
        $this->setExitTime();
        $this->length_of_stay = $length_of_stay;
        $this->verbose = $verbose;
        $this->fixed_rate = $fixed_rate;
        $this->is_24_hours_open = $is_24_hours_open;

        $this->coupon_threshold_price = $coupon_threshold_price;
        $this->is_coupon_threshold_price_percentage = $is_coupon_threshold_price_percentage;

        $this->is_member = $is_member;
        $this->is_overnight = false;
        $this->is_overnight_weekSkip = false; // : 10-03-2024
        $this->overnightLength = 0;
        $this->overnight_user_length = 0;
        $this->full_day_length = 0;
        $this->full_day_user_length = 0;
        $this->after_overnight_length = 0;
        $this->is_entry_normal = false;
        $this->is_entry_overnight = false;
        $this->overnighyDayBefore = false;

        $this->over_strat_time = false;
        $this->over_end_time = false;
        $this->is_same_day = false;
        $this->day_change_or_not_diff_inHours = 0;
        $this->new_length_for_next_day = 0;
        $this->arrival_to_start_length = 0;
        $this->tillOvernightLength = 0;
        $this->first_price_lenght_duration = 0;

        $this->is_exit_overnight = false;
        $this->is_exit_normal = false;


        $this->is_overnight_plus_normal_exit = false;

        $this->is_overnight_overstay = $is_overstay;
        $this->overnightChargeOrSkip = $overnightChargeOrSkip;
        $this->overnightPaymentDone = $overnightPaymentDone;

        $this->exitIslowerToStartTime = false;
        $this->exitIslowerToEndTime = false;
        $this->exitIsgreaterToStartTime = false;
        $this->exitIsgreaterToEndTime = false;
        $this->rate_description = false;
        $this->dayWisePrice = false;
        $this->rate = null;
        $this->rate_id = 0;
        $this->grace_length = 0;
        $this->remaningLength = 0;      // This length is after division of 24 and example : 25 then its is 1 hour
        $this->noOfDays = 0;


        $this->dailyMaxPrice = 0;
        $this->overnightPrice = 0;
        $this->isOvernightSameday = 0;

        // PIMS - 13707
        $this->monthlyRate = null;


        //$this->search($facilityIds);
        $this->searchFacilities($facilityIds);
    }

    // this function exit time as globle.
    function setExitTime()
    {
        $arrival = Carbon::parse($this->arrival_time);
        $lengthOfStayCheck = explode(".", $this->length_of_stay);
        if (count($lengthOfStayCheck) > 1 && (isset($lengthOfStayCheck[1])) && ($lengthOfStayCheck[1] > 0)) {
            $this->exitTime  = $arrival->copy()->addHours($this->length_of_stay, 2)->addMinute($lengthOfStayCheck[1]);
        } else {
            $this->exitTime = $arrival->copy()->addHours($this->length_of_stay, 2);
        }
    }

    public function __toString()
    {
        return json_encode($this);
    }

    protected function search(array $facilityIds = [])
    {

        foreach ($facilityIds as $id) {

            $this->facility = $this->getFacilityData($id);

            if (!$this->facility || !$this->facility->active) {
                continue;
            }

            if (!$this->validFacilityEntryExitTime()) {
                $this->updateRatesArray(Rate::CLOSED_RATE, false, false);
                $this->facility_open = false;
                continue;
            }
            //calulate the 
            $this->calculateLowestRate();
        }
    }

    protected function searchFacilities(array $facilityIds = [])
    {
        $facilities = Facility::with('geolocations')->with([
            'facilityRate' => function ($query) {
                $query = $query->with('rateDescription')
                    ->where('active', 1)
                    ->where('rate', '>', 0);
            }
        ])
            ->with([
                'rates' => function ($query) {
                    $query->whereNull('partner_id')
                        ->orWhere('partner_id', 0);
                }
            ])
            ->where('active', 1)
            ->orderBy(DB::raw('FIELD(`id`, ' . implode(',', $facilityIds) . ')'))
            ->findMany($facilityIds);

        if ($this->is_24_hours_open) {
            $this::isOpen24Hours($facilities, $facilityIds);
        }
        $this->log->info('Facility Task ==================== ');
        // $this->log->info(json_encode($facilities));
        foreach ($facilities as $f) {

            $this->facility = $f;
            if (!$this->validFacilityEntryExitTime()) {
                $this->updateRatesArray(Rate::CLOSED_RATE, false, false);
                $this->facility_open = false;
                continue;
            }
            $this->calculateLowestRate();
        }
    }

    protected function isOpen24Hours($facilities, array $facilityIds = [])
    {

        // 24 hours filter
        $weekday = date('N', strtotime($this->arrival_time));

        if ($weekday > 6) {
            $weekday = 7 - $weekday;
        }

        $facilityHoursOfOperations = HoursOfOperation::whereIn('facility_id', $facilityIds)->where('day_of_week', $weekday)->get();

        foreach ($facilities as $k => $f) {

            foreach ($facilityHoursOfOperations as $fhop) {

                if ($fhop->facility_id == $f->id) {
                    if (($fhop->open_time == "00:00:00") && (in_array($fhop->close_time, $this::CLOSE_HOURS))) {

                        // This means it is open 24 hours.
                    } else {
                        // Remove the facility as it is not open 24 hours

                        unset($facilities[$k]);
                    }
                    break;
                }
            }
        }
    }


    protected function calculateLowestRate()
    {
        $this->current_lowest_rate = null;
        $this->highest_event_rate = null;
        $this->overnight_current_rate = null;
        $overnight = '';
        set_time_limit(0);

        foreach ($this->facility->rates as $key => $rate) {
            if (!$rate->active) {
                continue;
            }
            // $this->log->info('calculateLowestRate LOOP INDEX KEY ' . $rate->id);
            if ($rate->rateType->is_member_rate == $this->is_member) {
                if ($rate->rateType->id == '7') {
                    continue;
                }

                $facility = Facility::where(['id' => $rate->facility_id])->first();
                // PIMS-13707 : VP
                if (isset($facility->facilityConfiguration->day_wise_price) && $facility->facilityConfiguration->day_wise_price == '1') {
                    if ($this->length_of_stay > self::FULL_DAY_HOUR_VAL && intval($rate->max_stay) == self::FULL_DAY_HOUR_VAL) {
                        $this->noOfDays         = (int) ($this->length_of_stay / self::FULL_DAY_HOUR_VAL);
                        $this->length_of_stay   = $this->length_of_stay - ($this->noOfDays * self::FULL_DAY_HOUR_VAL);
                        $this->dailyMaxPrice    = $rate->price;
                        $this->dayWisePrice     = true;
                    }
                }
                // PIMS-13707 : VP

                if (str_contains($rate->description, 'Grace')) {
                    $this->grace_length = $rate->max_stay;
                    if ($this->is_overnight_overstay) { // skip grace if overstay 
                        continue;
                    }
                }

                //added code by vikrant for transient ungated when custom/length enabled not rate band 19-02-2025
                if ($this->is_overnight_overstay) {
                    if ($rate->category_id == config('parkengage.GRACE_CATEGORY')) {
                        $this->grace_length = $rate->max_stay;
                        continue;
                    }
                }


                // set Daily Max Price : Vijay : 21-07-2024
                if (config('parkengage.overnight_cat_id') == $rate->category_id) {
                    $this->dailyMaxPrice = $rate->price;
                }
                // !!! Close here.


                // check first for overnight case else same 

                $overnight = $this->isOvernightApplicable($facility);
                $this->log->info("calculateLowestRate check Overnight Flag :  " . json_encode($overnight));

                if ($overnight && $rate->category_id == config('parkengage.overnight_cat_id')) {
                    $this->log->info('calculateLowestRate overstay night BREAK ');
                    $this->is_overnight_weekSkip = true;

                    // check length is more that overnight Hours
                    $requestedLegth = QueryBuilder::getLengthInMints($this->length_of_stay);
                    $totalLegth = QueryBuilder::getLengthInMints($this->overnightLength);
                    $length_of_stay = $this->length_of_stay;
                    if ($requestedLegth >= $totalLegth) {
                        $length_of_stay = $this->overnightLength;
                    }

                    $rates =  $this->overnightPriceDayWise($facility, $length_of_stay);

                    if ($rates) {
                        $this->overnightPrice = $rates->price;
                        $this->overnight_current_rate = $rates;
                        $this->is_overnight = true;
                        $this->rate_id = $rate->id;
                        $this->log->info('Loop Break ' . ++$key);
                        break;
                    }
                } else {
                    $this->log->info('No OverNight Applied');
                    $overnight = false;
                }

                // !!!!!!!
                if ($this->validLengthOfStay($rate) && $this->validDayOfTheWeek($rate) && $this->validEntryExitTime($rate)) {
                    $this->log->info('Line no : ' . __LINE__);
                    // Event rates behave opposite to normal rates and should always favor the higher rate
                    if ($rate->isEvent && $this->facility->hasActiveEvent($this->arrival_time, $this->length_of_stay)) {

                        if (!$this->highest_event_rate || $this->highest_event_rate->price < $rate->price) {
                            $this->highest_event_rate = $rate;
                        }
                    }
                    //$ratePrice = $rate->price;
                    if ($this->coupon_threshold_price > self::DEFAULT_VAL) {
                        if ((isset($rate->rate_type_id)) && ($rate->rate_type_id == self::COUPON_RATE)) {
                            $this->is_coupon_applied = self::DEFAULT_VAL_ONE;
                            if (($this->with_coupon_price == self::DEFAULT_VAL) || ($this->with_coupon_price > $rate->price)) {
                                $this->with_coupon_price = $rate->price;
                                $this->with_coupon_price_id = $rate->id;
                            }
                            if (($this->is_coupon_threshold_price_percentage == self::PRICE_VAL) || ($this->is_coupon_threshold_price_percentage == self::RESERVATION_PRICE_VAL)) {
                                $rate->price = round($rate->price + ($this->coupon_threshold_price), 0);
                            } else if ($this->is_coupon_threshold_price_percentage == self::PERCENTAGE_VAL) {
                                $rate->price  = round($rate->price  + (($rate->price  * $this->coupon_threshold_price) / 100), 0);
                            }
                        }
                    }
                    if (!$this->current_lowest_rate || ($this->current_lowest_rate->price >= $rate->price)) {
                        $this->log->info('Line no : ' . __LINE__);
                        $this->log->info('Rate Allowed here for type : ' . $rate->description);
                        $this->current_lowest_rate = $rate;
                        $this->rate_description = $rate->description;
                        $this->rate = $rate;
                    }
                }
            }
        }

        if ($overnight &&  !is_null($this->rate)) {
            // "description": "Grace Period",
            $this->log->info('calculateLowestRate overstay night rate not null  :   ' . json_encode($this->rate->id));
            $this->log->info('calculateLowestRate check overnight               :   ' . json_encode($overnight));
            $this->log->info('calculateLowestRate check overnight descr         :   ' . strtolower($this->rate->description));

            if ($overnight && str_contains($this->rate->description, 'Grace') && $this->is_overnight_overstay == false) {
                $this->log->info('calculateLowestRate overstay night rate not null and category rate ID 43 ');

                $this->is_overnight = false;
                $this->highest_event_rate = null;
                $this->overnight_current_rate = null;
                $this->log->info('calculateLowestRate overstay night BREAK 2222222');
            } else {
                $this->log->info('AAAAAAAAAAAAAAAAAAAAAAAAAAAA ');
                $this->current_lowest_rate = null;
                $this->highest_event_rate = null;
            }
        }
        $this->log->info('Line no : ' . __LINE__);
        $this->log->info("###################################################### Daily Max : {$this->dailyMaxPrice} and Overnight Price : {$this->overnightPrice}");

        // PIMS - 13707 : VP
        // Update Price For Day wise 

        if ($this->dayWisePrice) {
            // Fetch Monthly rate for price check 

            $monthlyRate = Rate::where("category_id", config('parkengage.monthlyCategoryId'))->where(['active' => 1, 'facility_id' => $this->facility->id])->first();
            // dd($monthlyRate->price, $monthlyRate->max_stay);
            $this->current_lowest_rate = new Rate(); // Or your model name
            $newPrice = 0;
            $newPrice = ($this->length_of_stay > 0 && isset($this->rate->price)) ? $this->rate->price : 0;
            $this->current_lowest_rate->price = ($this->noOfDays * $this->dailyMaxPrice) + $newPrice;


            if ($monthlyRate && isset($monthlyRate->price) &&  $this->current_lowest_rate->price > $monthlyRate->price) {
                $this->current_lowest_rate->price = $monthlyRate->price;

                //changes for PIMS_13707 >>>>>>>>>>@Sunil 
                //related to add remaing hours and daily max for booking more than one month
                $hours = $this->noOfDays * 24;
                if ($hours >= $monthlyRate->max_stay) {
                    $remainingHoursNow = $hours - $monthlyRate->max_stay;
                    $remaingdays = intdiv($remainingHoursNow, 24);
                    $remainghours = $this->length_of_stay;

                    if ($remaingdays > 0) {
                        $this->current_lowest_rate->price += $remaingdays * $this->dailyMaxPrice;
                    }

                    if ($remainghours > 0) {
                        $this->current_lowest_rate->price +=  $this->rate->price;
                    }
                }
            }
            //changes for PIMS_13707 >>>>>>>>>>@Sunil

        }
        // PIMS - 13707

        if (!$this->fixed_rate) {
            $this->log->info('setRateData calculateLowestRate 44444444 ');
            $this->setRateData();
        } else {
            $this->facility_open = true;
        }
    }

    //function used to calculate if length is greater than 24, second call 
    protected function calculateLowestRatePrecise($length_of_stay = 0, $main_length_of_stay = 0)
    {

        $this->current_lowest_rate_precise = null;
        $this->highest_event_rate_precise = null;

        foreach ($this->facility->rates as $rate) {
            if (!$rate->active) {
                continue;
            }
            if ($rate->rateType->is_member_rate == $this->is_member) {
                if ($rate->rate_type_id == 7) {
                    continue;
                }
                $this->log->info('calculateLowestRatePrecise 11  ' . $rate->id);
                if ($this->validLengthOfStayPrecise($rate, $length_of_stay) && $this->validDayOfTheWeekPrecise($rate, $length_of_stay) && $this->validEntryExitTimePrecise($rate, $length_of_stay)) {

                    // Event rates behave opposite to normal rates and should always favor the higher rate
                    if ($rate->isEvent && $this->facility->hasActiveEvent($this->arrival_time, $main_length_of_stay)) {

                        if (!$this->highest_event_rate_precise || $this->highest_event_rate_precise->price < $rate->price) {
                            $this->highest_event_rate_precise = $rate;
                        }
                    }
                    if (!$this->current_lowest_rate_precise || ($this->current_lowest_rate_precise->price >= $rate->price)) {
                        $this->log->info('calculateLowestRatePrecise select 22 :  ' . $rate->id);
                        //comment by vikrant 06-02-2025
                        //$this->current_lowest_rate_precise = $rate;
                        if ($length_of_stay == self::FULL_DAY_HOUR_VAL) {
                            return $this->facility->base_rate;
                        } else {
                            $this->current_lowest_rate_precise = $rate;
                        }
                    }
                }
            } else {
                $this->log->info('calculateLowestRatePrecise base_rate Select 222   ');
                return $this->facility->base_rate;
            }
        }

        if (!$this->fixed_rate) {
            $this->log->info('calculateLowestRatePrecise 11  fixed_rate');
            return $this->setPreciseRateData($length_of_stay, $main_length_of_stay);
        } else {
            $this->log->info('calculateLowestRatePrecise 11  fixed_rate 22');
            $this->facility_open = true;
        }
    }

    protected function setRateData()
    {
        if ($this->facility->is_rate_days_skip == '1') {
            $this->log->info('setRateData pave is_rate_days_skip.');
            //condition for specific pave partner by vikrant
            $returnRate = $this->paveFacilityRatePrice($this->length_of_stay);
            if ($returnRate > 0) {
                $price  = $returnRate;
                $this->updateRatesArray($price, isset($this->rate->id) ? $this->rate->id : false, false, false, false, true);
            }
        }
        $this->log->info('========================> setRateData ');
        if ($this->is_overnight) {
            /**
             * if the facility has an active event we will use the event base rate
             */
            $this->log->info('========================> setRateData 4444 ');
            if (in_array($this->facility->id, [424])) {
                $price = $this->eveningFacilityRatePrice($this->length_of_stay, $this->facility);
            } else {
                $price = $this->overnightFacilityRatePrice($this->length_of_stay, $this->facility);
            }

            $this->log->info('========================> setRateData 4444 ===========> Price ' . $price);

            $this->updateRatesArray($price, false, false, false, false, true);
        } else if ($this->current_lowest_rate  && $this->highest_event_rate) {
            $this->log->info('setRateData setRateData setRateData 11 ');
            /**
             * If both the event rate and the current rate are set we will take the higher of the two.
             * The rate will be sent to the front end however as an event rate.
             */
            $price = $this->highest_event_rate > $this->current_lowest_rate ? $this->highest_event_rate->price : $this->current_lowest_rate->price;



            $this->updateRatesArray($price, $this->highest_event_rate->id, $this->highest_event_rate->rate_type_id, $this->highest_event_rate->category_id, $this->highest_event_rate->coupon_code, true, $this->is_coupon_applied, $this->with_coupon_price, $this->with_coupon_price_id);
        } else if ($this->current_lowest_rate && !$this->facility->hasActiveEvent($this->arrival_time, $this->length_of_stay)) {
            $this->log->info('setRateData setRateData setRateData  22');
            /**
             * if just the current rate are set then we will sent back the current lowest rate with the rate id and rate_type_id
             */

            $this->updateRatesArray($this->current_lowest_rate->price, $this->current_lowest_rate->id, $this->current_lowest_rate->rate_type_id, $this->current_lowest_rate->category_id, $this->current_lowest_rate->coupon_code, false, $this->is_coupon_applied, $this->with_coupon_price, $this->with_coupon_price_id);
        } else if ($this->facility->hasActiveEvent($this->arrival_time, $this->length_of_stay)) {
            $this->log->info('========================> setRateData  33');
            /**
             * if the facility has an active event we will use the event base rate
             */

            $price = $this->baseEventRatePrice($this->length_of_stay);

            $this->updateRatesArray($price, false, false, false, false, true);
        } else {
            /**
             * the default will be the facility base rate time however many hours the are staying.
             */
            $this->log->info('========================> setRateData  4555 ');

            $price = $this->baseFacilityRatePrice($this->length_of_stay);
            $this->updateRatesArray($price, false, false, false, false);
        }
    }


    //function used to calculate if length is greater than 24, second call 
    protected function setPreciseRateData($length_of_stay = 0, $main_length_of_stay = 0)
    {

        $this->log->info('========================> setPreciseRateData  start here   ');
        if ($this->current_lowest_rate_precise && $this->highest_event_rate_precise) {
            $this->log->info('setPreciseRateData 22  ');
            /**
             * If both the event rate and the current rate are set we will take the higher of the two.
             * The rate will be sent to the front end however as an event rate.
             */
            return $price = $this->highest_event_rate_precise > $this->current_lowest_rate_precise ? $this->highest_event_rate_precise->price : $this->current_lowest_rate_precise->price;
        } else if ($this->current_lowest_rate_precise && !$this->facility->hasActiveEvent($this->arrival_time, $main_length_of_stay)) {
            $this->log->info('setPreciseRateData 33  ');
            /**
             * if just the current rate are set then we will sent back the current lowest rate with the rate id and rate_type_id
             */

            return $this->current_lowest_rate_precise->price;
        } else if ($this->facility->hasActiveEvent($this->arrival_time, $main_length_of_stay)) {
            $this->log->info('setPreciseRateData 44  ');
            /**
             * if the facility has an active event we will use the event base rate
             */


            return $price = $this->facility->fallbackRateEventPrice($length_of_stay);
        } else {
            /**
             * the default will be the facility base rate time however many hours the are staying.
             */
            $this->log->info('setPreciseRateData 55  ');
            return $price = $this->facility->fallbackRatePrice($length_of_stay);
        }
    }

    protected function updateRatesArray($price, $rate_id, $rate_type_id, $category_id = null, $coupon_code = false, $isEvent = false, $is_coupon_applied_flag = 0, $with_coupon_price_val = 0, $with_coupon_price_id = 0)
    {
        //This check is so that will not try and parse N/A as a float.
        $price = is_numeric($price) ? (float) $price : $price;
        $twenty_four_hours = "0";
        $oversize_fee_flag = "0";
        $monthly_flag = "0";
        if (isset($this->facility->oversize_fee) && ($this->facility->oversize_fee > 0)) {
            $oversize_fee_flag = "1";
        }
        /**if(isset($this->facility->monthly_rate) && ($this->facility->monthly_rate>0))
        {
            $monthly_flag="1";
        }
         **/
        //for adding 24 hour open filter
        $weekday = date('N', strtotime($this->arrival_time));
        if ($weekday > 6) {
            $weekday = 7 - $weekday;
        }


        $hoursOfOperation = HoursOfOperation::where('day_of_week', $weekday)->where('facility_id', $this->facility->id)->get();

        if (count($hoursOfOperation) <= 0) {
            $twenty_four_hours = "1";
        } else {
            $hoursOfOperation = HoursOfOperation::where('open_time', '00:00:00')->whereIn('close_time', array('23:59:59', '24:00:00', '25:00:00', '26:00:00', '27:00:00', '28:00:00', '29:00:00', '30:00:00'))->where('facility_id', $this->facility->id)->where('day_of_week', $weekday)->get();
            if (count($hoursOfOperation) > 0) {
                $twenty_four_hours = "1";
            }
        }

        $monthly_rates = array();
        $monthly_rate = 0;
        $monthlyTaxPrice = 0;
        $monthlyNetPrice = 0;
        $i = 0;
        $is_available_msg = '';

        /** foreach($this->facility->facilityRate as $facilityRate){
           
            //calulation net price
            if(isset($this->facility->tax_rate) && ($this->facility->tax_rate>0))
            {
                $monthlyNetPrice = (double)(($facilityRate->rate)/(1+$this->facility->tax_rate));
                //calulation tax rate price
                $monthlyTaxPrice = (double)(($monthlyNetPrice)*($this->facility->tax_rate));
            }else{
                $monthlyNetPrice = $facilityRate->rate;
            }
            
            $monthly_rates[$i]['rate_type']=$facilityRate->rateDescription->name;
            $monthly_rates[$i]['rate'] = $facilityRate->rate;
            $monthly_rates[$i]['rate_monthly_id']=$facilityRate->id;
            $monthly_rates[$i]['hours_description']=$facilityRate->rateDescription->hours_description;
            $monthly_rates[$i]['description']=$facilityRate->rateDescription->description;
            $monthly_rates[$i]['net_rate'] = number_format($monthlyNetPrice,2,'.','');
            $monthly_rates[$i]['tax_rate'] = number_format($monthlyTaxPrice,2,'.','');
            $monthly_rates[$i]['active']=$facilityRate->active;
            if(((double)$monthlyNetPrice < $monthly_rate || $monthly_rate===0) && $facilityRate->active==1 && $monthlyNetPrice>0)
            {
                $monthly_rate = round($monthlyNetPrice,2);
            }
            $i++;
        }**/
        if ($monthly_rate === 0) {
            $monthly_rate = "N/A*";
        }
        if ($this->facility->is_available == '2' || $this->facility->is_available == 2) {
            $is_available_msg = self::FACILITY_NOT_AVAILABLE;
        } elseif ($this->facility->is_available == '3' || $this->facility->is_available == 3) {
            $is_available_msg = self::FACILITY_COMING_SOON;
        } else {
            $is_available_msg = self::FACILITY_AVAILABLE;
        }

        $facilityInfo = [
            'facility_id' => $this->facility->id,
            'price' => $price,
            'monthly_rate' => $monthly_rate,
            'rate_id' => $this->is_overnight == true ? $this->rate_id : $rate_id,
            'rate_type_id' => $rate_type_id,
            'is_overnight' => $this->is_overnight,
            'is_event' =>  $isEvent,
            'category_id' => $category_id,
            'coupon_code' => $coupon_code,
            'is_indoor_parking' => $this->facility->is_indoor_parking,
            'is_outdoor_parking' => $this->facility->is_outdoor_parking,
            'is_tesla_charging' => $this->facility->is_tesla_charging,
            'is_generic_ev_charging' => $this->facility->is_generic_ev_charging,
            'is_motorcycle_parking' => $this->facility->is_motorcycle_parking,
            'is_oversize' => $oversize_fee_flag,
            'is_monthly' => $monthly_flag,
            'is_24hour_open' => $twenty_four_hours,
            'monthly_rates' => $monthly_rates,
            'is_coupon_applied_flag' => $is_coupon_applied_flag,
            'with_coupon_price_val' => $with_coupon_price_val,
            'with_coupon_price_id' => $with_coupon_price_id,
            'warning_on_reservation' => $this->is_closing_over_nightsoon,
            'warning_on_reservation_msg' => $this->is_closing_overnight_soon_msg,
            'warning_on_reservation_msg_email' => $this->is_closing_overnight_soon_msg_email,
            'is_available' => $this->facility->is_available,
            'is_available_msg' => $is_available_msg
        ];

        if ($this->verbose) {
            $facilityInfo = array_merge(
                $facilityInfo,
                [
                    'geolocation' => $this->facility->geolocations->toArray(),
                    'photo_url' => $this->facility->photos ? $this->facility->photos->url : null,
                    'full_name' => $this->facility->full_name,
                    'entrance_location' => $this->facility->entrance_location,
                    //'monthly_rate' => $this->facility->monthly_rate,
                    'monthly_rate' => $monthly_rate,
                    'between_streets' => $this->facility->between_streets,
                    'phone_number' => $this->facility->phone_number,
                    'ticketech_id' => $this->facility->ticketech_id
                ]
            );
        }

        $this->facilities[] = (object) $facilityInfo;
    }


    protected function getFacilityData($id)
    {

        if ($this->is_24_hours_open) {
            //24 hours filter
            $weekday = date('N', strtotime($this->arrival_time));

            if ($weekday > 6) {
                $weekday = 7 - $weekday;
            }

            $hoursOfOperation = HoursOfOperation::where('day_of_week', $weekday)->where('facility_id', $id)->get();

            if (count($hoursOfOperation) > 0) {
                $hoursOfOperation = HoursOfOperation::where('open_time', '00:00:00')->whereIn('close_time', array('23:59:59', '24:00:00', '25:00:00', '26:00:00', '27:00:00', '28:00:00', '29:00:00', '30:00:00'))->where('facility_id', $id)->where('day_of_week', $weekday)->get();
                if (count($hoursOfOperation) <= 0) {
                    return;
                }
            }
        }
        return Facility::with('geolocations')->with(
            ['rates' => function ($query) {
                $query->whereNull('partner_id')->orWhere('partner_id', 0);
            }]
        )->find($id);
    }

    /**
     * Test if the length of stay falls within min and max stay values
     *
     * @param  $rate
     * @param  $length_of_stay
     * @return boolean
     */
    protected function validLengthOfStay($rate)
    {
        // print_r("<br> RT : " . $rate->id);
        if ($rate->min_stay && $this->length_of_stay < $rate->min_stay) {
            return false;
        }

        if ($rate->max_stay && $this->length_of_stay > $rate->max_stay) {
            return false;
        }

        return true;
    }

    protected function validLengthOfStayPrecise($rate, $length_of_stay)
    {
        // $this->log->info("validLengthOfStayPrecise  min stay {$rate->min_stay} and lenght {$length_of_stay} MAX {$rate->max_stay}");
        if ($rate->min_stay && $length_of_stay < $rate->min_stay) {
            return false;
        }

        if ($rate->max_stay && $length_of_stay > $rate->max_stay) {
            return false;
        }

        return true;
    }

    /**
     * Test that the entry and exit times are within the ranges prescribed by the rate
     *
     * @return boolean
     */
    protected function validEntryExitTime($rate)
    {
        $user_arrival_time = Carbon::parse($this->arrival_time);
        $user_exit_time = Carbon::parse($this->arrival_time)->addHours($this->length_of_stay);

        $isBetweenEntry = $this->isBetweenBeginEndTimes($user_arrival_time, $rate->entry_time_begin, $rate->entry_time_end);
        $isBetweenExit = $this->isBetweenBeginEndTimes($user_exit_time, $rate->exit_time_begin, $rate->exit_time_end);

        return $isBetweenEntry && $isBetweenExit;
    }

    protected function validEntryExitTimePrecise($rate, $length_of_stay)
    {
        $this->log->info('validEntryExitTimePrecise : ' . $length_of_stay);
        if ($rate->entry_time_begin != '00:00:00' || $rate->exit_time_begin != '00:00:00' || $rate->entry_time_end != '23:59:59' || $rate->exit_time_end != '23:59:59') {
            $this->log->info('validEntryExitTimePrecise rate ID : ' . $rate->id);
            return false;
        } else {
            $user_arrival_time = Carbon::parse($this->arrival_time);
            $user_exit_time = Carbon::parse($this->arrival_time)->addHours($length_of_stay);

            $isBetweenEntry = $this->isBetweenBeginEndTimes($user_arrival_time, $rate->entry_time_begin, $rate->entry_time_end);
            $isBetweenExit = $this->isBetweenBeginEndTimes($user_exit_time, $rate->exit_time_begin, $rate->exit_time_end);
            return $isBetweenEntry && $isBetweenExit;
        }
    }

    protected function timestampToCarbon(Carbon $initial, $time)
    {
        $times = explode(':', $time);

        $hour = $times[0] ?? 0;
        $minute = $times[1] ?? 0;
        $second = $times[2] ?? 0;

        return $initial->copy()->hour($hour)->minute($minute)->second($second);
    }

    /**
     * Whether or not the given time is between begin and end times
     * Necessary because we need to account for the case where one time is the next day,
     * i.e. if the end time is 'before' the begin time, that time is assumed to be on the next day
     * $begin and $end are timestamps of format HH:mm:ss
     */
    protected function isBetweenBeginEndTimes(Carbon $time, $begin, $end)
    {
        $begin = $this->timestampToCarbon($time, $begin);
        $end = $this->timestampToCarbon($time, $end);

        return $time->between($begin, $end) || $time->copy()->addDays(1)->between($begin, $end);
    }

    /**
     * Test that the rate is valid during the day of the week for this search
     */
    protected function validDayOfTheWeek($rate)
    {
        $startDay = strtolower(date('l', strtotime($this->arrival_time)));
        $endDay = strtolower(date('l', strtotime($this->arrival_time) + $this->length_of_stay * 3600));
        if ($this->facility->is_rate_days_skip == '1') {
            return true;
        }
        return $rate->$startDay && $rate->$endDay;
    }

    protected function validDayOfTheWeekPrecise($rate, $length_of_stay)
    {
        $startDay = strtolower(date('l', strtotime($this->arrival_time)));
        $endDay = strtolower(date('l', strtotime($this->arrival_time) + $length_of_stay * 3600));
        if ($this->facility->is_rate_days_skip == '1') {
            return true;
        }
        // echo "<br>RATE : {$rate->id}, {$startDay} , {$endDay}";
        if ($this->is_overnight_weekSkip) {  // change In : pave 2 Overnigt case : 
            // echo "<br>RATE : {$rate->id}, {$startDay} , {$endDay}";
            // return true;
        }
        return $rate->$startDay && $rate->$endDay;
    }

    /**
     * Test that the facility operates during the arrival time and length of stay for this search
     *
     * @return boolean
     */
    protected function validFacilityEntryExitTime()
    {
        // No hours of operation means facility is open 24/7
        if (!$this->facility->hoursOfOperation->count()) {
            $this->log->info("validFacilityEntryExitTime 11111");
            return true;
        }
        $this->log->info("validFacilityEntryExitTime 222222");
        $arrival = Carbon::parse($this->arrival_time);
        $lengthOfStayCheck = explode(".", $this->length_of_stay);
        if (count($lengthOfStayCheck) > 1 && (isset($lengthOfStayCheck[1])) && ($lengthOfStayCheck[1] > 0)) {
            $this->log->info("validFacilityEntryExitTime 33333333333");
            $exit = $arrival->copy()->addHours($this->length_of_stay, 2)->addMinute(self::ADD_MINUTES_FOR_DECIMAL_LENGTH);
        } else {
            $this->log->info("validFacilityEntryExitTime 44444444444");
            $exit = $arrival->copy()->addHours($this->length_of_stay, 2);
        }

        return $this->isTimeOpenBetweenOpenClose($arrival) && $this->isTimeCloseBetweenOpenClose($exit, $arrival);
    }

    protected function isTimeBetweenOpenClose(Carbon $time)
    {
        foreach ($this->facility->hoursForDay($time->dayOfWeek) as $hours) {
            $open = $time->copy();
            $close = $time->copy();

            if ($time->dayOfWeek != $hours->day_of_week) {
                $open->subDay();
                $close->subDay();
            }

            $open->hour($hours->open_hour)->minute($hours->open_minutes);
            $close->hour($hours->close_hour)->minute($hours->close_minutes);

            if ($time->between($open, $close)) {
                return true;
            }
        }

        return false;
    }
    protected function isTimeCloseBetweenOpenClose(Carbon $time, Carbon $arrivalTime)
    {
        foreach ($this->facility->hoursForDay($time->dayOfWeek) as $hours) {
            $open = $time->copy();
            $close = $time->copy();

            if ($time->dayOfWeek != $hours->day_of_week) {
                $open->subDay();
                $close->subDay();
            }

            $open->hour($hours->open_hour)->minute($hours->open_minutes);
            $close->hour($hours->close_hour)->minute($hours->close_minutes);

            $minutesDiff = $time->diffInRealMinutes($close);
            $lengthOfStay = explode(".", $this->length_of_stay);

            if (($minutesDiff <= self::CONST_CLOSEING_SOON_MIN_CHECK1)) {
                $this->is_closing_over_nightsoon = self::CLOSING_SOON_ON;
                $this->is_closing_overnight_soon_msg = self::CLOSING_SOON_MSG;
                $this->is_closing_overnight_soon_msg_email = self::CLOSING_SOON_MSG_EMAIL;
            }



            $isSameDay = $time->isSameDay($arrivalTime);
            $isgreaterToStart = $time->gt($open);
            if ((!$isSameDay) && ($isgreaterToStart)) {
                $this->is_closing_over_nightsoon = self::CLOSING_SOON_ON;
                if ($this->is_closing_overnight_soon_msg != '') {
                    $this->is_closing_overnight_soon_msg = self::OVERNIGHT_STAY_SOON_MSG_COMBINE;
                    $this->is_closing_overnight_soon_msg_email = self::OVERNIGHT_STAY_SOON_MSG_COMBINE_EMAIL;
                } else {
                    $this->is_closing_overnight_soon_msg = self::OVERNIGHT_STAY_SOON_MSG;
                    $this->is_closing_overnight_soon_msg_email = self::OVERNIGHT_STAY_SOON_MSG_EMAIL;
                }
            }


            if ($time->between($open, $close)) {
                return true;
            }
        }

        return false;
    }

    protected function isTimeOpenBetweenOpenClose(Carbon $time)
    {
        foreach ($this->facility->hoursForDay($time->dayOfWeek) as $hours) {
            $open = $time->copy();
            $close = $time->copy();

            if ($time->dayOfWeek != $hours->day_of_week) {
                $open->subDay();
                $close->subDay();
            }

            $open->hour($hours->open_hour)->minute($hours->open_minutes);
            $close->hour($hours->close_hour)->minute($hours->close_minutes);
            $close->subMinutes(self::CONST_CLOSE_MIN_CHECK);

            if ($time->between($open, $close)) {
                return true;
            }
        }

        return false;
    }

    /** base event rate **>
     * 
     */
    public function baseEventRatePrice($length_of_stay)
    {
        // Default to 24 hour time period for event rates
        $event_rate_hours = $this->facility->base_event_rate_max_stay ?: self::FULL_DAY_HOUR_VAL;
        $dayCount = (int)($length_of_stay / $event_rate_hours);
        $firstPriceTime = self::PRICE_VAL_DEFAULT;
        $priceRemainingHours = self::PRICE_VAL_DEFAULT;
        $hoursReminder = self::PRICE_VAL_DEFAULT;

        if ($dayCount > self::PRICE_VAL_DEFAULT) {
            $firstPriceTime = $event_rate_hours;
            $hoursReminder = ($length_of_stay - ($dayCount * $event_rate_hours));
        } else {
            $firstPriceTime = $length_of_stay;
        }


        $priceFullDays = $this->calculateLowestRatePrecise($firstPriceTime, $length_of_stay);

        if ($dayCount > self::PRICE_VAL_DEFAULT) {
            $priceFullDays = ($priceFullDays * $dayCount);
        }


        if ($hoursReminder > self::PRICE_VAL_DEFAULT && $length_of_stay > $event_rate_hours) {
            $priceRemainingHours = $this->calculateLowestRatePrecise($hoursReminder, $length_of_stay);
        }

        $final_price = ($priceFullDays + $priceRemainingHours);

        return $final_price;
    }

    //pave specific rate changes by vikrant 26-12-2023
    public function paveFacilityRatePrice($length_of_stay)
    {
        $this->log->info('paveFacilityRatePrice pave is_rate_days_skip.');
        if ($length_of_stay < self::FULL_DAY_HOUR_VAL) {
            return 0;
        } else {
            //return $this->base_rate * (ceil($length_of_stay / 24));
            $dayCount = (int)($length_of_stay / self::FULL_DAY_HOUR_VAL);
            $firstPriceTime = self::PRICE_VAL_DEFAULT;
            $priceRemainingHours = self::PRICE_VAL_DEFAULT;
            $hoursReminder = self::PRICE_VAL_DEFAULT;

            if ($dayCount > self::PRICE_VAL_DEFAULT) {
                $firstPriceTime = self::FULL_DAY_HOUR_VAL;
                $hoursReminder = ($length_of_stay - ($dayCount * self::FULL_DAY_HOUR_VAL));
            } else {
                $firstPriceTime = $length_of_stay;
            }
            $daysHours = $dayCount * self::FULL_DAY_HOUR_VAL;
            $priceFullDays = $this->calculateLowestRatePrecise($daysHours, $length_of_stay);

            if ($hoursReminder > 0) {
                $priceFullDays += $this->calculateLowestRatePrecise($hoursReminder, $length_of_stay);
            }
            $final_price = number_format($priceFullDays, 2);
            $this->log->info('paveFacilityRatePrice pave is_rate_days_skip final price : ' . $final_price);
            return $final_price;
        }
    }

    public function baseFacilityRatePrice($length_of_stay)
    {
        //return $this->base_rate * (ceil($length_of_stay / 24));
        $dayCount = (int)($length_of_stay / self::FULL_DAY_HOUR_VAL);

        $firstPriceTime = self::PRICE_VAL_DEFAULT;
        $priceRemainingHours = self::PRICE_VAL_DEFAULT;
        $hoursReminder = self::PRICE_VAL_DEFAULT;

        if ($dayCount > self::PRICE_VAL_DEFAULT) {
            $firstPriceTime =  self::FULL_DAY_HOUR_VAL;
            //comment by sunil due to base rate not getting
            //$firstPriceTime = $dayCount * self::FULL_DAY_HOUR_VAL;
            $hoursReminder = ($length_of_stay - ($dayCount * self::FULL_DAY_HOUR_VAL));
        } else {
            $firstPriceTime = $length_of_stay;
        }

        //vikrant changed base rate to daily max for all partner 12-12-2023

        $dailyMaxRate = Rate::where("category_id", config('parkengage.dailyMaxCategoryid'))->where(['active' => 1, 'facility_id' => $this->facility->id])->first();
        if (!$dailyMaxRate) {
            $priceFullDays = $this->calculateLowestRatePrecise($firstPriceTime, $length_of_stay);
            if ($dayCount > self::PRICE_VAL_DEFAULT) {
                $priceFullDays = ($priceFullDays * $dayCount);
                //comment by sunil due to base rate not getting
                //$priceFullDays = $priceFullDays;
            }
        } else {
            if ($dayCount > self::PRICE_VAL_DEFAULT) {
                $priceFullDays = ($dailyMaxRate->price * $dayCount);
            } else {
                $priceFullDays = ($dailyMaxRate->price);
            }
        }

        //end
        /*
        Old code
        $priceFullDays = $this->calculateLowestRatePrecise($firstPriceTime, $length_of_stay);    
            if ($dayCount > self::PRICE_VAL_DEFAULT) {
                $priceFullDays = ($priceFullDays * $dayCount);
            }
        */
        if ($hoursReminder > self::PRICE_VAL_DEFAULT && $length_of_stay > self::FULL_DAY_HOUR_VAL) {

            $priceRemainingHours = $this->calculateLowestRatePrecise($hoursReminder, $length_of_stay);
        }
        $final_price = ($priceFullDays + $priceRemainingHours);
        return $final_price;
    }

    function getFormatedLength($hoursInmints)
    {
        if ($hoursInmints >= 60) {
            $hours = intval($hoursInmints / 60);
            $mints = $hoursInmints - ($hours * 60);
            $mints_new = $mints / 100;
            $length = $hours + $mints_new;
        } else {
            $length =  $hoursInmints / 100;
        }
        return $length;
    }

    function checkPreviousDayOvernight($overnightFallingTime)
    {
        $arrivalTime = Carbon::parse($this->arrival_time);
        $overnightArrivalTime = Carbon::parse($this->arrival_time)->subDay();
        $OverStratTime = $this->timestampToCarbon($overnightArrivalTime, $overnightFallingTime->overnigth_start_time);
        $OverEndTime = $this->timestampToCarbon($overnightArrivalTime, $overnightFallingTime->overnight_end_time);
        $OverEndTime = $OverEndTime->subSecond();
        $entryToMidnight = $this->timestampToCarbon($overnightArrivalTime, $overnightFallingTime->overnigth_start_time);
        // Lokesh-Vijay: 25-June-2024 
        $nextDayStartTime = $OverEndTime;
        $arrivalTimeInOvernight = $arrivalTime->isBetween($OverStratTime, $OverEndTime);
        if ($arrivalTimeInOvernight) {
            return true;
        }
        return false;
    }
    function checkExitTimePreviousDayOvernight($overnightFallingTime)
    {
        $arrivalTime = Carbon::parse($this->arrival_time);
        $overnightArrivalTime = Carbon::parse($this->arrival_time)->subDay();
        $OverStratTime = $this->timestampToCarbon($overnightArrivalTime, $overnightFallingTime->overnigth_start_time);
        $OverEndTime = $this->timestampToCarbon($overnightArrivalTime, $overnightFallingTime->overnight_end_time);
        $OverEndTime = $OverEndTime->subSecond();
        $entryToMidnight = $this->timestampToCarbon($overnightArrivalTime, $overnightFallingTime->overnigth_start_time);
        // Lokesh-Vijay: 25-June-2024 
        $nextDayStartTime = $OverEndTime;
        $arrivalTimeInOvernight = $this->exitTime->isBetween($OverStratTime, $OverEndTime);
        if ($arrivalTimeInOvernight) {
            return true;
        }
        return false;
    }

    protected function setOvernightStartEndTime($facility, $overnightAgain = false)
    {

        $carbonNow = Carbon::parse('now');
        $arrivalTime = Carbon::parse($this->arrival_time);
        $arrivalTimeTochange = Carbon::parse($this->arrival_time);

        if ($facility->is_gated_facility == '1') {  // Gated 
            $this->exitTime = Carbon::parse('now');
        } else {  // UnGated 
            $this->exitTime = Carbon::parse(QueryBuilder::addMintsInDatetime(Carbon::parse($this->arrival_time), QueryBuilder::getLengthInMints($this->length_of_stay)))->subSecond();
        }

        $isSameDay = $this->exitTime->isSameDay($arrivalTime);
        if ($isSameDay) {
            $this->is_same_day = true;
        } else {
            $this->is_same_day = false;
        }
        // $this->dayStartTime;
        // $this->dayEndTime;

        $overnightFallingTime = FacilityOvernightDuration::where(['rate_category_id' => config('parkengage.overnight_cat_id'), 'active' => '1', 'facility_id' => $facility->id])->first();

        if ($overnightFallingTime) {

            if ($facility->is_gated_facility == '1') {
                $OverStratTime = $this->timestampToCarbon($this->exitTime, $overnightFallingTime->overnigth_start_time);
                $OverEndTime = $this->timestampToCarbon($this->exitTime, $overnightFallingTime->overnight_end_time);
                $this->entryToMidnight = $this->timestampToCarbon($arrivalTime, '23:59:59');
                $this->nextDayStartTime = Carbon::parse($this->entryToMidnight)->addSeconds(1);
            } else {
                $OverStratTime = $this->timestampToCarbon($arrivalTime, $overnightFallingTime->overnigth_start_time);
                $OverEndTime = $this->timestampToCarbon($arrivalTime, $overnightFallingTime->overnight_end_time);
                $OverEndTime = $OverEndTime->subSecond();
                $this->entryToMidnight = $this->timestampToCarbon($arrivalTime, $overnightFallingTime->overnigth_start_time);
                // Lokesh-Vijay: 25-June-2024 
                // $nextDayStartTime = $OverEndTime; #Carbon::parse($entryToMidnight)->addSeconds(1);
                $this->nextDayStartTime = $OverStratTime; #Carbon::parse($entryToMidnight)->addSeconds(1);
                // $isCurrentTimeInOvernight = $arrivalTime->isBetween($OverStratTime, $OverEndTime);
            }

            // $isCurrentTimeInOvernight = $arrivalTime->isBetween($OverStratTime, $OverEndTime);
            $isExitAfterEndTime = $arrivalTime->gte($OverEndTime);
            $isCurrentTimeLowerToOvernightStart = $arrivalTime->lte($OverStratTime);

            $arrivalTimeInOvernight = $arrivalTime->isBetween($OverStratTime, $OverEndTime);  // Current Day Overnight check  

            if (!$arrivalTimeInOvernight && $this->checkPreviousDayOvernight($overnightFallingTime) && $facility->is_gated_facility == '0') {
                $overnightArrivalTime = $arrivalTimeTochange->subDay();
                $OverStratTime = $this->timestampToCarbon($overnightArrivalTime, $overnightFallingTime->overnigth_start_time);
                $OverEndTime = $this->timestampToCarbon($overnightArrivalTime, $overnightFallingTime->overnight_end_time);
                $OverEndTime = $OverEndTime->subSecond();
                $entryToMidnight = $this->timestampToCarbon($overnightArrivalTime, $overnightFallingTime->overnigth_start_time);
                // Lokesh-Vijay: 25-June-2024 
                $nextDayStartTime = $OverEndTime;
            }

            $this->OverStratTime    = $OverStratTime;
            $this->OverEndTime      = $OverEndTime;
            $this->overnightLength  = $OverStratTime->diffInRealHours($OverEndTime->addSecond());
            // $perDayRemainingHours = (self::FULL_DAY_HOUR_VAL - $this->overnightLength);
            $this->dayLength    = (self::FULL_DAY_HOUR_VAL - $this->overnightLength);
            $this->log->error("Arrival Time  : {$this->arrival_time} Exit Time : {$this->exitTime}");
            $this->log->error("O.N - S.T : " . date('Y-m-d H:i:s', strtotime($this->OverStratTime)) . " O.N - E.T " . date('Y-m-d H:i:s', strtotime($this->OverEndTime)) . " O.N - Length {$this->overnightLength} : Day Length {$this->dayLength}");
        } else {
            return false;
        }
    }


    function isOvernightApplicable($facility)
    {

        if (!$facility) {
            return false;
        }
        $this->setOvernightStartEndTime($facility);
        // is same day or not in : Change in for Ungated Flow 
        // $exitTime = Carbon::parse('2023-11-22 06:04:01'); ///2023-11-16 00:07:35
        $arrivalTime = Carbon::parse($this->arrival_time);
        $isCurrentTimeInOvernight = false;

        $arrivalTimeInOvernight = false;
        $exitTimeInOvernight = false;

        $isExitAfterEndTime = false;
        $isCurrentTimeLowerToOvernightStart = false;
        $isDaycheck = false;

        $overnightFallingTime = FacilityOvernightDuration::where(['rate_category_id' => '43', 'active' => '1', 'facility_id' => $facility->id])->first();

        if ($overnightFallingTime) {
            $this->isOvernightSameday = $overnightFallingTime->is_overnight_sameday;
            $this->log->info('isOvernightApplicable Start =========>');


            // check arrival time cases
            $arrivalIslowerToStartTime      = $arrivalTime->lte($this->OverStratTime);     // same day case entry
            $arrivalIslowerToEndTime        = $arrivalTime->lte($this->OverEndTime);     // same day case entry
            $arrivalIsgreaterToStartTime    = $arrivalTime->gte($this->OverStratTime);   // one day before entry 
            $arrivalIsgreaterToEndTime      = $arrivalTime->gte($this->OverEndTime);   // one day before entry 

            $exitIslowerToStartTime         = $this->exitTime->lte($this->OverStratTime);           // same day exit
            $exitIslowerToEndTime           = $this->exitTime->lte($this->OverEndTime);           // same day exit
            $exitIsgreaterToStartTime       = $this->exitTime->gte($this->OverStratTime);         // same day case exit
            $exitIsgreaterToEndTime         = $this->exitTime->gte($this->OverEndTime);         // same day case exit


            $exit_diff_in_mints = 0;
            $exitDiffInSeconds = 0;
            $till_overnight_diff_mints = 0;

            // if ($this->is_same_day == true) { // checkout same day
            $this->log->info('isOvernightApplicable same day ');
            // $diffMints = $arrivalTime->diffInRealMinutes($OverEndTime);
            // $this->arrival_to_start_length = $this->getFormatedLength($diffMints);   // this is length will minus in muliday case
            $this->arrival_to_start_length = 0;
            $this->first_price_lenght_duration = $arrivalTime->diffInRealMinutes($this->entryToMidnight);
            $this->log->info("isOvernightApplicable arrival_to_start_length : {$this->arrival_to_start_length}");

            // $overnightArrivalTime = $this->timestampToCarbon($arrivalTime, $overnightFallingTime->overnigth_start_time);
            /*  Entry in Overnight 
            *   Exit in Afer Overnight : 
             */
            if ($arrivalIsgreaterToStartTime && $arrivalIslowerToEndTime && $exitIsgreaterToEndTime) {
                $this->log->info('Condition 1 :  /*  Entry in Overnight Exit in Afer Overnight :*/');
                $this->is_overnight_plus_normal_exit = true;
                $this->is_entry_overnight = true;

                $diffMints = $arrivalTime->diffInRealMinutes($this->OverEndTime);
                $exit_diff_in_mints = $this->OverEndTime->diffInRealMinutes($this->exitTime);
                $this->arrival_to_start_length  = $this->getFormatedLength($diffMints);
                if ($this->isOvernightSameday) {
                    $this->arrival_to_start_length  = 0;        //UMS change 
                }
                $this->full_day_length          = $this->getFormatedLength($exit_diff_in_mints);
                $this->after_overnight_length   = $this->getFormatedLength($exit_diff_in_mints);

                $exitDiffInSeconds = $this->OverStratTime->diffInSeconds($this->exitTime);
                if ($exit_diff_in_mints <= 0 && $exitDiffInSeconds > 0) { // mint diff is zero and second is more then 0
                    $this->full_day_length = '0.01';
                }
            } else if (($arrivalIsgreaterToStartTime && $arrivalIslowerToEndTime) && $exitIslowerToEndTime) {
                $this->log->info('Condition 2 : /* Entry Overnight and exit in Overnight  */');
                $this->is_exit_overnight = true;
                $this->is_entry_overnight = true;

                if ($this->isOvernightSameday) {
                    $this->full_day_length = 0;
                    return true;
                }
            } else if ($arrivalIslowerToStartTime && $exitIsgreaterToStartTime && $exitIslowerToEndTime) {
                // Entry Time is < 12 AM , exit time falling B/W (12AM - 6AM) and ( checkout time   < 6AM)
                // Reservation 
                $this->is_entry_normal = true;
                $this->is_exit_overnight = true;

                $this->log->info('Condition 3 : /* Entry Before Overnight and exit in Overnight  */ ');
                $till_overnight_diff_mints = $arrivalTime->diffInRealMinutes($this->nextDayStartTime); //
                $exit_diff_in_mints = $this->nextDayStartTime->diffInRealMinutes($this->exitTime);
                // $diffMints = $arrivalTime->diffInRealMinutes($overnightArrivalTime);
                $this->arrival_to_start_length = QueryBuilder::getLengthInHours($till_overnight_diff_mints);   // this is length will minus in muliday case
                $this->full_day_length = $this->getFormatedLength($exit_diff_in_mints);
            }
            // New Cases for Reservation 
            else if ($arrivalIslowerToStartTime && $exitIslowerToStartTime) {
                $this->log->info('Condition 2.3 :  /*  Normal Entry and Exit time  :*/');
                $this->is_entry_normal = true;
                $this->is_exit_normal = true;

                $diffMints = $arrivalTime->diffInRealMinutes($this->OverEndTime);
                $exit_diff_in_mints = $this->OverEndTime->diffInRealMinutes($this->exitTime);

                $this->arrival_to_start_length  = $this->getFormatedLength($diffMints);

                $this->full_day_length          = 0;
                $this->after_overnight_length   = 0;
                return false;
                // $exitDiffInSeconds = $this->OverStratTime->diffInSeconds($this->exitTime);
                // if ($exit_diff_in_mints <= 0 && $exitDiffInSeconds > 0) { // mint diff is zero and second is more then 0
                //     $this->full_day_length = '0.01';
                // }
            }
            // New Cases CLose here !!!!!! 
            else if (($exitIsgreaterToStartTime && $exitIslowerToEndTime)) {
                // exit time falling B/W (12AM - 6AM) and ( checkout time   < 6AM)
                $this->is_exit_overnight = true;
                $this->log->info('Condition 4 : ');
                $till_overnight_diff_mints = $arrivalTime->diffInRealMinutes($this->OverStratTime); //
                $exit_diff_in_mints = $this->OverStratTime->diffInRealMinutes($this->exitTime);
                // $diffMints = $arrivalTime->diffInRealMinutes($overnightArrivalTime);
                $this->arrival_to_start_length = $this->getFormatedLength($till_overnight_diff_mints);   // this is length will minus in muliday case
                $this->full_day_length = $this->getFormatedLength($exit_diff_in_mints);
            } else if ($exitIsgreaterToStartTime && $exitIsgreaterToEndTime) {
                // exit time NNot falling B/W (12AM - 6AM)  only  ( checkout time  > 6AM)
                $this->is_overnight_plus_normal_exit = true;
                $this->log->info('Condition 5 : ');

                $till_overnight_diff_mints          = $arrivalTime->diffInRealMinutes($this->entryToMidnight);
                $this->arrival_to_start_length      = QueryBuilder::getLengthInHours($till_overnight_diff_mints);
                $exit_diff_in_mints                 = $this->nextDayStartTime->diffInRealMinutes($this->exitTime);

                if ($isDaycheck) {
                    $exit_diff_in_mints             = $arrivalTime->diffInRealMinutes($this->exitTime);
                    $this->arrival_to_start_length = 0;
                }


                if ($exit_diff_in_mints > $this->overnightLength * 60) {
                    // $exit_diff_in_hours = $this->getFormatedLength($exit_diff_in_mints);
                    $this->log->info('is_overnight_plus_normal_exit  CASE 2  ' . $exit_diff_in_mints);
                    if ($exit_diff_in_mints < (self::FULL_DAY_HOUR_VAL * 60)) {
                        $overstayLength = ($exit_diff_in_mints - $this->overnightLength * 60);

                        $this->log->info('is_overnight_plus_normal_exit  CASE 3 : ' . $overstayLength);
                        $this->after_overnight_length = QueryBuilder::getLengthInHours($overstayLength);
                        $this->log->info('is_overnight_plus_normal_exit  CASE $   ' . $this->after_overnight_length);
                    }
                    $this->full_day_length = $this->getFormatedLength($exit_diff_in_mints);

                    if ($this->is_same_day == false && $this->arrival_to_start_length > $this->grace_length) {
                        $this->is_entry_normal = true;
                    }
                } elseif ($this->exitTime->gte($this->nextDayStartTime)) {
                    if ($arrivalTime->gte($this->entryToMidnight)) {
                        $this->arrival_to_start_length      = 0;
                    }
                    $this->after_overnight_length = QueryBuilder::getLengthInHours($exit_diff_in_mints);
                    $this->is_entry_overnight = true;
                    //$this->is_entry_normal = false;

                    $this->log->info('vikrant is_overnight_plus_normal_exit  111444 55555 ==>> ' . $exit_diff_in_mints);
                } else {
                    $this->log->info('is_overnight_plus_normal_exit  CASE 4  ');
                    $this->full_day_length = $this->getFormatedLength($exit_diff_in_mints);
                }
            } else {
                // need to handle
                $this->log->info('Not Overnight applicable ');
                // $diffMints = $arrivalTime->diffInRealMinutes($OverEndTime);
                // $this->arrival_to_start_length = $this->getFormatedLength($diffMints);
                // $this->arrival_to_start_length = 0;   // this is length will minus in muliday case
                $this->is_exit_normal = true;
                return false;
            }
            // New check Added On : 14-02-2024 
            // To check for entry time on overnight and day length (if day time length (24-overnight length and compare it))
            // if ($till_overnight_diff_mints >= QueryBuilder::getLengthInMints((self::FULL_DAY_HOUR_VAL - $this->overnightLength))) {
            //     $this->is_entry_overnight = true;
            // } else {
            //     $this->is_entry_normal = true;
            // }
            $exitDiffInSeconds = $this->OverStratTime->diffInSeconds($this->exitTime);
            if ($exit_diff_in_mints <= 0 && $exitDiffInSeconds > 0) { // mint diff is zero and second is more then 0
                $this->full_day_length = '0.01';
            }
            $this->log->info('isOvernightApplicable Next day End');
            // }

            $isgreaterToStart = $this->exitTime->gt($this->OverStratTime);
            $islowerToendTime = $arrivalTime->lt($this->OverEndTime);

            $dhs = $arrivalTime->diffInRealHours($this->OverStratTime);
            if ($isgreaterToStart && $this->is_same_day == false) {
                $this->log->info('day_change_or_not_diff_inHours 11 : ' . $dhs);
                return true;
            } else if ($islowerToendTime  && $this->is_same_day == true) {
                $this->log->info('day_change_or_not_diff_inHours 22 : ' . $dhs);

                if ($isCurrentTimeInOvernight || $isExitAfterEndTime) {
                    // this will check user exit in overnight duration or after overnight time.
                    $this->log->info('day_change_or_not_diff_inHours 33 : ' . $dhs);
                    return true;
                } else {
                    return true;
                }
            }

            return false;
        } else {
            $this->log->info('is Not Applicable Overnight');
        }
        return false;
    }

    public function overnightFacilityRatePrice($length_of_stay, $facility) // total length of stay
    {
        //return $this->base_rate * (ceil($length_of_stay / 24));
        $firstPriceTime         = self::PRICE_VAL_DEFAULT;
        $priceRemainingHours    = self::PRICE_VAL_DEFAULT;
        $hoursReminder          = self::PRICE_VAL_DEFAULT;
        $priceFullDays          = self::PRICE_VAL_DEFAULT;
        $afterOvernightPrice    = self::PRICE_VAL_DEFAULT;
        $overnightPrice         = self::PRICE_VAL_DEFAULT;
        $temp_length            = self::PRICE_VAL_DEFAULT;
        $final_price            = self::PRICE_VAL_DEFAULT;

        $this->log->info(' ########################################## ##########################################');
        $this->log->info('overnightFacilityRatePriceeovernightFacilityRatePricee Start with length_of_stay  ' . $length_of_stay);
        $this->log->info('overnightLength           : ' . $this->overnightLength);
        $this->log->info('arrival_to_start_length   : ' . $this->arrival_to_start_length);
        $this->log->info('full_day_length           : ' . $this->full_day_length);
        $this->log->info('after_overnight_length    : ' . $this->after_overnight_length);
        $this->log->info('grace_length              : ' . $this->grace_length);
        $this->log->info('isOvernightSameday        : ' . $this->isOvernightSameday);

        $this->log->info('function overnightFacilityRatePrice check Same Day flag :  ' . json_encode($this->is_same_day));
        // Only Cases for Like USM Reservations Start 
        if ($this->isOvernightSameday) {
            $this->log->info('reservation function call :  ' . __LINE__);
            $remaningPrice = 0;
            $remaningPrice = $this->calculateAccumulatePrice();
            $final_price = $remaningPrice + $priceRemainingHours;
            $this->log->info('reservation function call Close :  ' . $final_price);
            return $final_price;
        }
        // Close Here for USM 

        if ($this->is_same_day == false) { // not same day.   
            // : 20-11-2023 : To handle exact 12:00 case exit
            if ($this->arrival_to_start_length > 0 && $this->full_day_length > 0) {
                $this->log->info('arrival_to_start_length full_day_length');
                $firstPriceTime     = $this->arrival_to_start_length;
                $temp_length        = $this->full_day_length;
                // $temp_length = $hoursReminder;
                $dayCount = (int)($temp_length / self::FULL_DAY_HOUR_VAL);
            } else {
                $temp_length = QueryBuilder::getLengthInHours((QueryBuilder::getLengthInMints($length_of_stay) - QueryBuilder::getLengthInMints($this->arrival_to_start_length)));
                if ($length_of_stay >= 18) {
                    $overNightDailyMaxOvernight = true;
                    // $temp_length = (QueryBuilder::getLengthInMints($length_of_stay) - QueryBuilder::getLengthInMints($this->arrival_to_start_length));
                    $this->log->info("VVVVVV length_of_stay 111 : {$length_of_stay}");
                    $dayCount = (int)($temp_length / self::FULL_DAY_HOUR_VAL);
                    $hoursReminder = $temp_length;
                } else {
                    if ($this->full_day_length <= 0) {
                        // $this->log->info("VVVVVV length_of_stay 222 : {$length_of_stay}");
                        $this->log->info("VVVVVV temp_length 222 : {$temp_length}");
                        // $dayCount = (int)($temp_length / self::FULL_DAY_HOUR_VAL);
                        // $hoursReminder = ($temp_length - ($dayCount * self::FULL_DAY_HOUR_VAL));
                        $temp_length = $length_of_stay;
                        $dayCount = (int)($temp_length / self::FULL_DAY_HOUR_VAL);
                        $hoursReminder = ($temp_length - ($dayCount * self::FULL_DAY_HOUR_VAL));
                        $firstPriceTime = $length_of_stay;
                    } else {
                        // $this->log->info("VVVVVV length_of_stay 2223333 : {$length_of_stay}");
                        $this->log->info("VVVVVV temp_length 222333 : {$temp_length}");

                        $dayCount = (int)($temp_length / self::FULL_DAY_HOUR_VAL);
                        $hoursReminder = ($temp_length - ($dayCount * self::FULL_DAY_HOUR_VAL));
                    }
                }
            }
            $this->log->info("overnightFacilityRatePrice Day Count {$dayCount}");

            if ($dayCount > 0) {
                $hoursReminder = ($temp_length - ($dayCount * self::FULL_DAY_HOUR_VAL));
                $this->log->info("arrival_to_start_length full_day_length : {$dayCount} ---hoursReminder : {$hoursReminder}");
            }
            if ($this->arrival_to_start_length > self::FULL_DAY_HOUR_VAL) {
                $multidayLength = QueryBuilder::getLengthInHours(QueryBuilder::getLengthInMints($this->arrival_to_start_length) - $this->first_price_lenght_duration);
                if ($multidayLength > self::FULL_DAY_HOUR_VAL) {
                    $firstPriceTime = QueryBuilder::getLengthInHours($this->first_price_lenght_duration);
                    $dayCount = (int)($multidayLength / self::FULL_DAY_HOUR_VAL);
                    $hoursReminder = $this->full_day_length;
                    $this->log->info("multiday case arrival_to_start_length full_day_length {$dayCount} hoursReminder {$hoursReminder}");
                }
            }
        } else {

            if ($this->is_overnight_plus_normal_exit) {
                $this->log->info('is_overnight_plus_normal_exit grace_length ' . $this->grace_length);
                $lengthOfStayInMintes = QueryBuilder::getLengthInMints($length_of_stay);
                // $arrival_to_start_length = QueryBuilder::getLengthInMints($this->arrival_to_start_length);
                if ($lengthOfStayInMintes < 60) {
                    $this->log->info('is_overnight_plus_normal_exit lengthOfStayInMintes ' . $lengthOfStayInMintes);

                    if ($this->grace_length > 0 &&  $lengthOfStayInMintes > $this->grace_length) {
                        // skip grace
                        $temp_length = QueryBuilder::getLengthInHours($lengthOfStayInMintes);
                        $this->log->info('is_overnight_plus_normal_exit Grace check   ' . $temp_length);
                    } else {

                        $temp_length = QueryBuilder::getLengthInHours($lengthOfStayInMintes);
                        $this->log->info('is_overnight_plus_normal_exit Not Grace check   ' . $temp_length);
                    }

                    // $hoursReminder = $temp_length;
                    $firstPriceTime = $temp_length;
                } else {
                    $this->log->info('is_overnight_plus_normal_exit 3333 ');

                    if (QueryBuilder::getLengthInMints($this->arrival_to_start_length) <= QueryBuilder::getLengthInMints($this->grace_length)) {
                        $temp_length = QueryBuilder::getLengthInHours($lengthOfStayInMintes);
                        $firstPriceTime = $temp_length;
                        $this->log->info("O.N - " . __LINE__ . $firstPriceTime);
                    } else {
                        // here we calulate after overnight length 
                        if ($this->is_entry_overnight && $this->is_overnight_plus_normal_exit) {
                            $firstPriceTime = $this->after_overnight_length;
                        } else {
                            $temp_length = (QueryBuilder::getLengthInMints($length_of_stay) - QueryBuilder::getLengthInMints($this->arrival_to_start_length, 2));
                            $firstPriceTime = QueryBuilder::getLengthInHours($temp_length);
                        }
                        $this->log->info("O.N - " . __LINE__ . $firstPriceTime);
                    }
                }
                $dayCount = (int)($firstPriceTime / self::FULL_DAY_HOUR_VAL);
            } else {
                $this->log->info('Same Day  ');
                $temp_length        = $length_of_stay;
                $firstPriceTime     = $length_of_stay;
                $hoursReminder      = $length_of_stay;
                $dayCount           = (int)($length_of_stay / self::FULL_DAY_HOUR_VAL);

                // USM Reservation case : 16-07-2024
                if ($this->isOvernightSameday) {
                    $remaningPrice  = 0;

                    $remaningPrice = $this->calculateAccumulatePrice();
                    $final_price = $remaningPrice + $priceRemainingHours;
                    return $final_price;
                }
            }
        }

        $this->log->info("AAAAAA total length_of_stay   :   {$length_of_stay}");

        $this->log->info("AAAAAA firstPriceTime         :   {$firstPriceTime} ");
        $this->log->info("AAAAAA dayCount               :   {$dayCount}");
        $this->log->info("AAAAAA temp_length            :   {$temp_length}");
        $this->log->info("AAAAAA hoursReminder          :   {$hoursReminder}");
        $this->log->info("AAAAAA is_entry_overnight     :   {$this->is_entry_overnight}");
        $this->log->info("AAAAAA is_exit_overnight      :   {$this->is_exit_overnight}");
        $this->log->info("AAAAAA is_entry_normal FLAG   :   {$this->is_entry_normal}");
        $this->log->info("AAAAAA is_overnight_overstay   :   {$this->is_overnight_overstay}");
        $this->log->info("AAAAAA overnightChargeOrSkip   :   {$this->overnightChargeOrSkip}");
        $this->log->info("AAAAAA overnightPaymentDone   :    {$this->overnightPaymentDone}");
        $this->log->info(' ########################################## ##########################################');

        if ($dayCount > 0) { // case more than 24 hours 
            $this->log->info('overnightFacilityRatePricee overnightFacilityRatePricee 3333333333333 ');

            $perDayRemainingHours = (self::FULL_DAY_HOUR_VAL - $this->overnightLength);
            // $hoursReminder = ($length_of_stay - ($dayCount * self::FULL_DAY_HOUR_VAL));
            $this->log->info("MultiDay Case day Count           :   {$dayCount}");
            $this->log->info("MultiDay overnightLength          :   {$this->overnightLength}");
            $overnightFullDayPrice = 0;

            // $tempHours = 0;
            // $actualLenghtOfStay = $length_of_stay;
            if ($firstPriceTime > self::PRICE_VAL_DEFAULT) {
                $graceCheck = QueryBuilder::getLengthInMints($firstPriceTime);
                $this->log->info('Grace period applicalble 1122 ' . $graceCheck);
                if ($graceCheck <= QueryBuilder::getLengthInMints($this->grace_length)) {
                    $this->log->info('Grace period applicalble in multiday ');
                    $priceFullDays = '0.00';
                } else {
                    if ($this->is_entry_overnight) { // if user entry in overnight + day + Overnight + day exist 
                        $overnightPrice = $this->overnight_current_rate->price;  /// for Because entry in Overnight
                        // Day Charge for entry Overnight 
                        $firstPriceTime = QueryBuilder::getLengthInHours(QueryBuilder::getLengthInMints(self::FULL_DAY_HOUR_VAL - $this->overnightLength) - QueryBuilder::getLengthInMints($this->grace_length));
                        if ($firstPriceTime > 0) {
                            $priceFullDays = $this->calculateOvenightLowestRatePrecise($firstPriceTime, $length_of_stay);
                            $this->log->info('Grace period applicalble 1133 is_entry_overnight ' . $firstPriceTime . ' Price : ' . $priceFullDays . ' Overnight : ' . $overnightPrice);
                        }
                    } else {

                        $firstPriceTime = QueryBuilder::getLengthInHours($graceCheck - QueryBuilder::getLengthInMints($this->grace_length));
                        $priceFullDays = $this->calculateOvenightLowestRatePrecise($firstPriceTime, $length_of_stay);
                    }
                }
            }


            $this->log->info('Overnight priceFullDays ' . $priceFullDays);
            $tempOvernightPrice = $tempPriceRemainingHours = 0;
            for ($i = 0; $i < $dayCount; $i++) {
                $tempOvernightPrice = $this->overnight_current_rate->price;

                if ($perDayRemainingHours > self::PRICE_VAL_DEFAULT) {
                    $tempPriceRemainingHours = $this->calculateOvenightLowestRatePrecise($perDayRemainingHours, $length_of_stay);
                    $this->log->info('In Loop perDay Remaining Hours for ths index ' . $i . ' hours else remaning in Overnight Price :' . $overnightFullDayPrice . ' : Remaning Hour Price :  ' . $tempPriceRemainingHours);
                }
                $overnightFullDayPrice += ($tempOvernightPrice + $tempPriceRemainingHours);
                $this->log->info('Overnight priceFullDays for the index : ' . $i . ' PRICE else : ' . $overnightFullDayPrice);
                // this will calculate only full day price overnight +day price  
            }
            $this->log->info('Overnight Final priceFullDays CASE 1 : ' . $overnightFullDayPrice);
            if ($priceFullDays > self::PRICE_VAL_DEFAULT) {
                $overnightFullDayPrice += $priceFullDays;
            }
            if ($overnightPrice > self::PRICE_VAL_DEFAULT) {
                $overnightFullDayPrice += $overnightPrice;
            }
            $this->log->info('Overnight Final priceFullDays  CASE 2 :  ' . $overnightFullDayPrice);

            $this->log->info('hoursReminder ' . $hoursReminder);
            // if exit time fallin overnight time or exit time is greator than overnight end time 
            if ($hoursReminder >= $this->overnightLength && $this->is_overnight_plus_normal_exit) {

                $priceFullDays = $this->overnight_current_rate->price;
                $hoursReminder = floatval($hoursReminder - $this->overnightLength);
                $this->log->info('Overnight priceFullDays KEY : ' . $hoursReminder);
                $priceRemainingHours = $this->calculateOvenightLowestRatePrecise($hoursReminder, $length_of_stay);
                $overnightFullDayPrice += ($this->overnight_current_rate->price + $priceRemainingHours);

                $this->log->info("hoursReminder priceFullDays Price :   $priceFullDays");
                $this->log->info("hoursReminder : $hoursReminder Price :  $priceRemainingHours");
            } else {
                $priceFullDays = $this->overnight_current_rate->price;
                $overnightFullDayPrice += $priceFullDays;
                $this->log->info("hoursReminder CASE 2 ");
                if ($this->is_overnight_overstay && $this->is_exit_overnight) {
                    $overnightFullDayPrice -= $priceFullDays;
                }
            }

            $this->log->info('Final overnightFullDayPrice +remaing Hours : ' . $overnightFullDayPrice);
            $final_price = $overnightFullDayPrice;
        } else {
            $this->log->info('overnightFacilityRatePricee 22222');
            if ($firstPriceTime > self::PRICE_VAL_DEFAULT) {
                // apply overnight Price here Blindly 
                $this->log->info('!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
                // and apply price for remaing
                if ($this->is_overnight_plus_normal_exit) { //  same day
                    $this->log->info('overnightFacilityRatePricee is_overnight_plus_normal_exit ');
                    if ($firstPriceTime > self::PRICE_VAL_DEFAULT && !$this->is_entry_overnight) {
                        $this->log->info("O.N - " . __LINE__ . $firstPriceTime);
                        $graceCheck = QueryBuilder::getLengthInMints($firstPriceTime);
                        $this->log->info('Grace period applicalble 1133 ' . $graceCheck);
                        $this->log->info('Grace period applicalble 1133 grace length : ' . QueryBuilder::getLengthInMints($this->grace_length));
                        if ($graceCheck <= QueryBuilder::getLengthInMints($this->grace_length)) {
                            $this->log->info('Grace period applicalble ');
                            $priceFullDays = '0.00';
                        } else {
                            // only specific case code
                            $firstPriceTime = QueryBuilder::getLengthInHours($graceCheck - QueryBuilder::getLengthInMints($this->grace_length));
                            $priceFullDays = $this->calculateOvenightLowestRatePrecise($firstPriceTime, $length_of_stay);
                        }
                    }

                    if ($this->after_overnight_length > self::PRICE_VAL_DEFAULT) {
                        $this->log->info('L1 : ' . __LINE__);
                        if (!$this->is_entry_normal) {
                            $priceFullDays = 0;
                            $this->log->info('L2 : ' . __LINE__);
                        }
                        // this should be use in case of day change or Not In Same Day
                        if ($this->is_entry_overnight && $this->is_same_day == false) { // if user entry in overnight + day + Overnight + day exist 
                            $this->log->info('L3 : ' . __LINE__);
                            // Initial Overnight Chanrge Bacause of Entry in Overnight.
                            $priceRemainingHours += $this->overnight_current_rate->price;
                            // Day Charge for entry Overnight 
                            $firstPriceTime = QueryBuilder::getLengthInHours(QueryBuilder::getLengthInMints(self::FULL_DAY_HOUR_VAL - $this->overnightLength) - QueryBuilder::getLengthInMints($this->grace_length));
                            $priceFullDays = $this->calculateOvenightLowestRatePrecise($firstPriceTime, $length_of_stay);
                            $this->log->info("is_entry_overnight  Lenght :  {$firstPriceTime} , Price : {$priceFullDays} ");
                        }
                        $this->log->info('L4 : ' . __LINE__);
                        // : 03-03-2024
                        if ($this->is_entry_overnight && $this->is_same_day == true) {
                            // Same Day Overnight Charge will be applicable once only.
                            $rate = $this->overnightPriceDayWisePrecise($facility, $this->length_of_stay);
                            $this->overnight_current_rate = $rate;
                            $priceRemainingHours += $this->overnight_current_rate->price;
                            $this->log->info('L5 : ' . __LINE__);
                            // New change : 23-07-2024 With USM for gated 
                            if ($this->arrival_to_start_length <= $this->grace_length) {
                                $priceRemainingHours = 0;
                                $this->log->info('L5.1 : ' . __LINE__);
                            }
                        } else {
                            $this->log->info('L6 : ' . __LINE__);
                            if ($this->arrival_to_start_length >= $this->grace_length) {
                                // 2nd Overnight Charge After Day Change or (After 12:00 Am to Overnight end)
                                $rate = $this->overnightPriceDayWisePrecise($facility, $this->length_of_stay);
                                $this->overnight_current_rate = $rate;
                                $priceRemainingHours += $this->overnight_current_rate->price;
                                $this->log->info('L7 : ' . __LINE__);
                            } else if ($this->arrival_to_start_length > 0 && $this->full_day_length >= $this->overnightLength) {
                                $rate = $this->overnightPriceDayWisePrecise($facility, $this->overnightLength);
                                $this->overnight_current_rate = $rate;
                                $priceRemainingHours += $this->overnight_current_rate->price;
                                $this->log->info('L8 : ' . __LINE__);
                            }
                        }

                        if ($this->overnightPaymentDone) {
                            $priceRemainingHours = 0;
                            $this->log->info('L9 : ' . __LINE__);
                        }
                        // charge for After Overnight Close  or Day charges.  
                        $this->log->info('START TO CALULATE LAST AFTER OVERNIG PRICE FOR LENGTH : ' . $this->after_overnight_length);
                        if ($this->is_same_day == false) {
                            // if day changed 
                            $afterOvernightPrice = $this->calculateOvenightLowestRatePrecise($this->after_overnight_length, $length_of_stay, true);
                            $this->log->info('L10 : ' . __LINE__);
                        } else {
                            // Else same day 
                            $afterOvernightPrice = $this->calculateOvenightLowestRatePrecise($this->after_overnight_length, $length_of_stay);
                            $this->log->info('L11 : ' . __LINE__);
                        }
                    } else {

                        if ($hoursReminder > self::PRICE_VAL_DEFAULT) {
                            $priceRemainingHours = $this->calculateOvenightLowestRatePrecise($hoursReminder, $length_of_stay);
                        }
                    }
                } else if ($this->is_exit_overnight) { //  Entry and Exit in overnight With in same day and more then a grace preriod
                    $this->log->info('overnightFacilityRatePricee is_exit_overnight ');

                    if ($this->is_same_day == false) {
                        if ($firstPriceTime > self::PRICE_VAL_DEFAULT) {
                            $graceCheck = QueryBuilder::getLengthInMints($firstPriceTime);
                            $this->log->info('Grace period applicalble 1144 : ' . $graceCheck);
                            $this->log->info('Grace period applicalble 1144 grace length : ' . QueryBuilder::getLengthInMints($this->grace_length));
                            if ($graceCheck <= QueryBuilder::getLengthInMints($this->grace_length)) {
                                $this->log->info('Grace period applicalble 55 ');
                                $priceFullDays = '0.00';
                                // now for next remaing duration duration price 
                                if ($this->full_day_length > 0) {
                                    // overnight applicable after 12 AM
                                    if ($this->is_overnight_overstay) {
                                        // because paymend is done already so that no grace now
                                        $priceFullDays = $this->calculateOvenightLowestRatePrecise($firstPriceTime, $length_of_stay);
                                        $this->log->info('Grace period applicalble 55 11 overnight');
                                    }
                                    $this->log->info('Grace period applicalble 55 222 overnight');
                                    $priceRemainingHours = $this->overnight_current_rate->price; // this for if time is more than a mint also
                                }
                            } else {
                                $this->log->info('Grace period applicalble 66  ');
                                // if firstpriceTime length is more than grace time with checkin in overnight time so that meant is in overstay time 

                                // This is Inital Overnight Price 
                                if ($facility->is_gated_facility == '1') {
                                    // if Overnight entry Bigin then take initial case
                                    if ($this->is_entry_overnight) {
                                        $priceRemainingHours = $this->overnight_current_rate->price;
                                    }
                                } else {
                                    $rate = $this->overnightPriceDayWisePrecise($facility, $this->length_of_stay);
                                    $this->overnight_current_rate = $rate;
                                    $priceRemainingHours = $this->overnight_current_rate->price;
                                }

                                if ($this->is_overnight_overstay && $this->overnightChargeOrSkip && !$this->overnightPaymentDone) {
                                    // Pave Specific condition 
                                    $priceRemainingHours = 0;
                                }
                                if ($this->overnightPaymentDone) {
                                    $priceRemainingHours = 0;
                                }
                                // this is to calulate Day time Price 
                                if ($graceCheck >= QueryBuilder::getLengthInMints(self::FULL_DAY_HOUR_VAL - $this->overnightLength)) {
                                    $firstPriceTime = QueryBuilder::getLengthInHours(QueryBuilder::getLengthInMints(self::FULL_DAY_HOUR_VAL - $this->overnightLength) - QueryBuilder::getLengthInMints($this->grace_length));
                                } else {
                                    $firstPriceTime = QueryBuilder::getLengthInHours($graceCheck - QueryBuilder::getLengthInMints($this->grace_length));
                                }
                                $priceFullDays = $this->calculateOvenightLowestRatePrecise($firstPriceTime, $length_of_stay);
                                /* if ($facility->is_gated_facility == '1') {
                                } else {
                                    $priceFullDays = $this->calculateOvenightLowestRatePrecise($firstPriceTime, $length_of_stay, true);
                                } */
                                // Last Overnight Calulcate Exit in Overnight;

                                // if ($this->full_day_length > 0) {   // Reservation change : 16-07-2024
                                if ($this->full_day_length > 0 && $this->is_exit_overnight == false) {
                                    // !! this below 2 lines added while making change for Pave to calulate new Overnigt rate if day in multilple 
                                    $rate = $this->overnightPriceDayWisePrecise($facility, $this->length_of_stay);
                                    $this->overnight_current_rate = $rate;
                                    $priceRemainingHours += $this->overnight_current_rate->price; // this for if time is more than a mint also 
                                    // !! Close 
                                } else {
                                    // if ($this->arrival_to_start_length >= 18) {
                                    if ($this->arrival_to_start_length >= QueryBuilder::getLengthInHours(QueryBuilder::getLengthInMints(self::FULL_DAY_HOUR_VAL - $this->overnightLength))) {
                                        $afterOvernightPrice = $this->overnight_current_rate->price; // 14-11-2023 comment

                                    } else {
                                        if ($this->is_exit_overnight) {
                                            $afterOvernightPrice = $this->overnight_current_rate->price;
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        $this->log->info('overnightFacilityRatePricee 44444444444 Same Day is_exit_overnight');
                        // $priceFullDays = $this->overnight_current_rate->price;
                        // !! this below 2 lines added while making change for Pave to calulate new Overnigt rate if day in multilple 
                        $rate = $this->overnightPriceDayWisePrecise($facility, $this->length_of_stay);
                        $this->overnight_current_rate = $rate;
                        $priceFullDays = $this->overnight_current_rate->price;
                        // !! Close                        
                    }
                } else {
                    // Day changed
                    $hoursReminder =  $length_of_stay;
                    if (!is_null($this->overnight_current_rate)) {
                        $priceFullDays = $this->overnight_current_rate->price;
                    }
                    $this->log->info('overnightFacilityRatePricee 44444444 ');
                    if ($hoursReminder > self::PRICE_VAL_DEFAULT) {
                        $priceRemainingHours = $this->calculateOvenightLowestRatePrecise($hoursReminder, $length_of_stay);
                    }
                    // $arrivalToOvernightStartTime = $arrivalTimeDiffTillOvernightStartTime + $this->overnightLength;
                    // $hoursReminder = ($length_of_stay - ($arrivalTimeDiffTillOvernightStartTime + $this->overnightLength));
                }
            } else if ($firstPriceTime <= self::PRICE_VAL_DEFAULT && $this->full_day_length > self::PRICE_VAL_DEFAULT) {
                // only overnight if start or overnight 12 am is 0 mints 
                $this->log->info('overnightFacilityRatePricee overnightFacilityRatePricee 2222233333 ');
                $fullLenghtInMints = (QueryBuilder::getLengthInMints($this->full_day_length) - QueryBuilder::getLengthInMints($this->grace_length));
                // QueryBuilder::getLengthInMints($this->grace_length)
                $overNightLengthInMints = $this->overnightLength * 60;
                $this->log->info('overnightFacilityRatePricee overnightFacilityRatePricee fullLenghtInMints         : ' . $fullLenghtInMints);
                $this->log->info('overnightFacilityRatePricee overnightFacilityRatePricee overNightLengthInMints    : ' . $overNightLengthInMints);
                if ($fullLenghtInMints >= $overNightLengthInMints) {
                    // overnight plus and daily rate 
                    $priceRemainingHours = $this->overnight_current_rate->price; // this for if time is more than a mint also 
                    $firstPriceTime = QueryBuilder::getLengthInHours($fullLenghtInMints - $overNightLengthInMints);
                    $priceFullDays = $this->calculateOvenightLowestRatePrecise($firstPriceTime, $length_of_stay);
                } else {
                    // only overnight 
                    if ($this->is_exit_overnight) {
                        $priceRemainingHours = $this->overnight_current_rate->price; // this for if time is more than a mint also 
                    } else {
                        // not in overnight only day price caluculate
                        $firstPriceTime = QueryBuilder::getLengthInHours($fullLenghtInMints);
                        $priceFullDays = $this->calculateOvenightLowestRatePrecise($firstPriceTime, $length_of_stay);
                    }
                }
            }
            $this->log->info('priceFullDays             : ' . $priceFullDays);
            $this->log->info('hoursReminder             : ' . $hoursReminder);
            $this->log->info('priceRemainingHours       : ' . $priceRemainingHours);
            $this->log->info('afterOvernightPrice       : ' . $afterOvernightPrice);

            if ($this->is_overnight_overstay && !$this->overnightChargeOrSkip) {
                $priceFullDays = 0;
                $this->log->info('overnightFacilityRatePricee applied in overstay and exit in overnight time so skip once paid  ');
            }

            // $final_price = ($priceFullDays + $priceRemainingHours);
            if ($afterOvernightPrice > 0) {
                $final_price = ($priceFullDays + $priceRemainingHours + $afterOvernightPrice);
            } else {
                $final_price = ($priceFullDays + $priceRemainingHours);
            }
        }


        return $final_price;
    }

    protected function calculateOvenightLowestRatePrecise($length_of_stay = 0, $main_length_of_stay = 0, $dayWise = false)
    {
        $this->log->info('calculateOvenightLowestRatePrecise  Length Recieve: ' . $length_of_stay . " Main length: $main_length_of_stay");
        $this->current_lowest_rate_precise = null;
        $this->highest_event_rate_precise = null;

        if ($dayWise) {
            // this section will calulate the rate only for specific day.
            $exitDay = $this->getExitDay();
            foreach ($this->facility->rates as $key => $rate) {
                if (!$rate->active) {
                    continue;
                }
                if (str_contains($rate->description, 'Grace')) {
                    continue;   // skip grace after overnight
                }
                if ($rate->rate_type_id == 7) {
                    continue;
                }

                // if ($this->validLengthOfStayPrecise($rate, $length_of_stay) && $this->validDayOfTheWeekPrecise($rate, $length_of_stay) && $this->validEntryExitTimePrecise($rate, $length_of_stay)) {
                if ($this->validLengthOfStayPrecise($rate, $length_of_stay) && $this->validDayOfTheWeekPrecise($rate, $length_of_stay)) {
                    if (!$this->current_lowest_rate_precise || ($this->current_lowest_rate_precise->price >= $rate->price)) {
                        $this->current_lowest_rate_precise = $rate;
                    }
                }
            }
        } else {
            foreach ($this->facility->rates as $key => $rate) {
                if (!$rate->active) {
                    continue;
                }
                if (str_contains($rate->description, 'Grace')) {
                    continue;   // skip grace after overnight
                }
                if ($rate->rate_type_id == 7) {
                    continue;
                }
                if ($rate->rateType->is_member_rate == $this->is_member) {
                    if ($this->validLengthOfStayPrecise($rate, $length_of_stay) && $this->validDayOfTheWeekPrecise($rate, $length_of_stay)) {

                        // Event rates behave opposite to normal rates and should always favor the higher rate
                        if ($rate->isEvent && $this->facility->hasActiveEvent($this->arrival_time, $main_length_of_stay)) {

                            if (!$this->highest_event_rate_precise || $this->highest_event_rate_precise->price < $rate->price) {
                                $this->highest_event_rate_precise = $rate;
                            }
                        }
                        if (!$this->current_lowest_rate_precise || ($this->current_lowest_rate_precise->price >= $rate->price)) {
                            $this->log->info('calculateOvenightLowestRatePrecise Select 22  ' . $rate->id);
                            $this->current_lowest_rate_precise = $rate;
                        }
                    }
                } else {
                    $this->log->info('calculateOvenightLowestRatePrecise 222   ');
                    return $this->facility->base_rate;
                }
            }
        }
        $this->log->info('calculateOvenightLowestRatePrecise 3333  ');

        if (!$this->fixed_rate) {
            $this->log->info('calculateOvenightLowestRatePrecise 11  fixed_rate');
            return $this->setOvernightPreciseRateData($length_of_stay, $main_length_of_stay);
        } else {
            $this->log->info('calculateOvenightLowestRatePrecise 11  fixed_rate 22');
            $this->facility_open = true;
        }
    }

    //function used to calculate if length is greater than 24, second call 
    protected function setOvernightPreciseRateData($length_of_stay = 0, $main_length_of_stay = 0)
    {

        $this->log->info('setOvernightPreciseRateData 11  ');
        if ($this->current_lowest_rate_precise && $this->highest_event_rate_precise) {
            $this->log->info('setOvernightPreciseRateData 22  ');
            /**
             * If both the event rate and the current rate are set we will take the higher of the two.
             * The rate will be sent to the front end however as an event rate.
             */
            return $price = $this->highest_event_rate_precise > $this->current_lowest_rate_precise ? $this->highest_event_rate_precise->price : $this->current_lowest_rate_precise->price;
        } else if ($this->current_lowest_rate_precise && !$this->facility->hasActiveEvent($this->arrival_time, $main_length_of_stay)) {
            $this->log->info('setOvernightPreciseRateData 33  ');
            /**
             * if just the current rate are set then we will sent back the current lowest rate with the rate id and rate_type_id
             */

            return $this->current_lowest_rate_precise->price;
        } else if ($this->facility->hasActiveEvent($this->arrival_time, $main_length_of_stay)) {
            $this->log->info('setOvernightPreciseRateData 44  ');
            /**
             * if the facility has an active event we will use the event base rate
             */


            return $price = $this->facility->fallbackRateEventPrice($length_of_stay);
        } else {
            /**
             * the default will be the facility base rate time however many hours the are staying.
             */
            $this->log->info('setOvernightPreciseRateData 44  ');
            return $price = $this->facility->fallbackRatePrice($length_of_stay);
        }
    }

    protected function overnightPriceDayWise($facility, $length_of_stay)
    {
        $rates = $facility->getAllOvernightPrice();
        foreach ($rates as $key => $rate) {
            if (!$rate->active) {
                continue;
            }
            if (str_contains($rate->description, 'Grace')) {
                continue;
            }
            if ($rate->rate_type_id == 7) {
                continue;
            }

            if ($rate->rateType->is_member_rate == $this->is_member) {
                if ($this->validDayOfTheWeekPrecise($rate, $length_of_stay)) {
                    $this->log->info('ONDW RATE SELECTED : .  ' . $rate->id);
                    return $rate;
                }
            } else {
                $this->log->info('ONDW BASE RATE    ');
                return $this->facility->base_rate;
            }
        }
    }

    protected function overnightPriceDayWisePrecise($facility, $length_of_stay)
    {
        // check exit Day 
        // $exitDateTime = Carbon::
        $arrival = Carbon::parse($this->arrival_time);
        $lengthOfStayCheck = explode(".", $this->length_of_stay);
        if (count($lengthOfStayCheck) > 1 && (isset($lengthOfStayCheck[1])) && ($lengthOfStayCheck[1] > 0)) {
            $exit = $arrival->copy()->addHours($this->length_of_stay, 2)->addMinute($lengthOfStayCheck[1]);
        } else {
            $exit = $arrival->copy()->addHours($this->length_of_stay, 2);
        }
        $isExitDay = $exit->format('l');
        $rates = $facility->getAllOvernightPrice(strtolower($isExitDay));
        foreach ($rates as $key => $rate) {
            if (!$rate->active) {
                continue;
            }
            if (str_contains($rate->description, 'Grace')) {
                continue;
            }
            if ($rate->rate_type_id == 7) {
                continue;
            }

            if ($rate->rateType->is_member_rate == $this->is_member) {
                return $rate;
            } else {
                $this->log->info('ONDW BASE RATE    ');
                return $this->facility->base_rate;
            }
        }
    }

    protected function getExitDay()
    {
        $arrival = Carbon::parse($this->arrival_time);
        $lengthOfStayCheck = explode(".", $this->length_of_stay);
        if (count($lengthOfStayCheck) > 1 && (isset($lengthOfStayCheck[1])) && ($lengthOfStayCheck[1] > 0)) {
            $exit = $arrival->copy()->addHours($this->length_of_stay, 2)->addMinute($lengthOfStayCheck[1]);
        } else {
            $exit = $arrival->copy()->addHours($this->length_of_stay, 2);
        }
        return strtolower($exit->format('l'));
    }

    protected function getDateRateIfDefine($facility_id)
    {

        $rateBand = Rate::select('id', 'facility_id', 'min_stay', 'max_stay', 'price', 'active', 'description', 'entry_time_begin', 'entry_time_end', 'exit_time_begin', 'exit_time_end', 'free_time_begin', 'free_time_end', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday', 'category_id', 'day_type', 'start_date', 'end_date', 'display_entry_time_begin', 'display_entry_time_end', 'display_exit_time_begin', 'display_exit_time_end')->where(['facility_id' => $facility_id->id])->where('active', 1);
        $rateBand = $rateBand->orderBy('price', 'asc')->get();
        $current = Carbon::parse('now');
        $getAllIdOfRateDate = array_column(
            json_decode(json_encode($rateBand), true),
            'id'
        );

        $getRateDefCustom = RateDefCustom::select('id', 'rate_id', 'rate_start_date', 'rate_end_date', 'day_type', 'status', 'facility_id')->where(['facility_id' => $facility_id->id])->where(['status' => '1'])->whereIn('rate_id', $getAllIdOfRateDate)->get();
        // print_r($getRateDefCustom);die;
        $getRateBandId = [];
        if (!empty($getRateDefCustom)) {
            $getCreatedRateBandDate = array_unique(array_column(
                json_decode(json_encode($getRateDefCustom), true),
                'rate_id'
            ));
        }
        foreach ($getRateDefCustom as $val) {
            $dateStart = Carbon::parse($val['rate_start_date']);
            $dateEnd = Carbon::parse($val['rate_end_date']);
            if ($val['day_type'] == 0) {
                if ($current->isSameDay($dateStart)) {
                    $getRateBandId[] = $val['rate_id'];
                }
            } elseif ($val['day_type'] == 1) {
                if ($current->gte($dateStart) &&  $current->lte($dateEnd)) {
                    $getRateBandId[] = $val['rate_id'];
                }
            }
        }
        // print_r($getRateBandId);die;
        return  [$getRateBandId, $getCreatedRateBandDate];
    }


    protected function calculateAccumulatePrice()
    {
        // $this->overnightPrice
        // $this->dailyMaxPrice
        $this->log->info("calculateAccumulatePrice :");

        $dayCount = 0;
        $PriceFullDay = 0;
        if ($this->full_day_length > 0) {
            $dayCount           = (int)($this->full_day_length / self::FULL_DAY_HOUR_VAL);
            if ($dayCount > 0) {
                $this->log->info("calculateAccumulatePrice : 2 ");
                $hoursReminder = ($this->full_day_length - ($dayCount * self::FULL_DAY_HOUR_VAL));
                $this->log->info("calculateAccumulatePrice hoursReminder : 2.11 :  " . $hoursReminder);

                if ($this->arrival_to_start_length > 0) {
                    $this->log->info("calculateAccumulatePrice : 2.1 ");
                    $price = $this->calculateOvenightLowestRatePrecise($this->arrival_to_start_length, $this->length_of_stay);
                    $PriceFullDay += $price;
                }

                // this loop start calculation from overnight start time till Next day overnight.
                for ($i = 0; $i < $dayCount; $i++) {
                    $PriceFullDay +=  ($this->overnightPrice + $this->dailyMaxPrice);

                    // if ($perDayRemainingHours > self::PRICE_VAL_DEFAULT) {
                    //     $tempPriceRemainingHours = $this->calculateOvenightLowestRatePrecise($perDayRemainingHours, $length_of_stay);
                    //     $this->log->info('In Loop perDay Remaining Hours for ths index ' . $i . ' hours else remaning in Overnight Price :' . $overnightFullDayPrice . ' : Remaning Hour Price :  ' . $tempPriceRemainingHours);
                    // }
                    // $overnightFullDayPrice += ($tempOvernightPrice + $tempPriceRemainingHours);
                    $this->log->info('Overnight priceFullDays for the index : ' . $i . ' PRICE else : ' . $PriceFullDay);
                    // this will calculate only full day price overnight +day price  
                }
                $this->log->info('Overnight Final priceFullDays CASE 1 : ' . $PriceFullDay);

                if ($this->is_entry_overnight) {
                    $this->log->info("calculateAccumulatePrice : 2.55 ");
                    $PriceFullDay += $this->overnightPrice;
                    // AFter Overnight To Next day complition.
                    if ($hoursReminder <= $this->dayLength) {
                        // $PriceFullDay += $this->overnightPrice;  // overnight Price Because not it not going in above section
                        $this->log->info("calculateAccumulatePrice : 2.5 ");
                        $price = $this->calculateOvenightLowestRatePrecise($hoursReminder, $this->length_of_stay);
                        $this->log->info("2.5 Day Length : {$hoursReminder} Price : { $price } ");
                        $this->log->info("calculateAccumulatePrice : 2.6 ");
                        $PriceFullDay += $price;
                    }
                } else {
                    // Calculate Overnight Till Overnigth Close 
                    if ($hoursReminder > 0 && $hoursReminder <= $this->overnightLength) {
                        $PriceFullDay += $this->overnightPrice;
                    }

                    // AFter Overnight To Next day complition.
                    if ($hoursReminder >= $this->overnightLength) {
                        $PriceFullDay += $this->overnightPrice;  // overnight Price Because not it not going in above section

                        $hoursReminder -= $this->overnightLength;
                        $this->log->info("calculateAccumulatePrice : 2.5 ");
                        $price = $this->calculateOvenightLowestRatePrecise($hoursReminder, $this->length_of_stay);
                        $this->log->info("2.5 After Overnight Length : {$this->after_overnight_length} Price : { $price } ");
                        $this->log->info("calculateAccumulatePrice : 2.6 ");
                        $PriceFullDay += $price;
                    }
                }

                // if($hoursReminder>)
            } else {
                $this->log->info("calculateAccumulatePrice : 1 ");

                // with in 24 Hours Price Calulation  // Entry Normal Time 
                if ($this->arrival_to_start_length > 0) {
                    $this->log->info("calculateAccumulatePrice : 2 ");
                    $price = $this->calculateOvenightLowestRatePrecise($this->arrival_to_start_length, $this->length_of_stay);
                    $PriceFullDay += $price;
                }

                if ($this->full_day_length <= $this->overnightLength) {
                    $PriceFullDay += $this->overnightPrice;
                    $this->log->info("calculateAccumulatePrice : 3 ");

                    // Entry Overnight And Exit Normal 
                    if ($this->arrival_to_start_length <= 0) {
                        $this->log->info("calculateAccumulatePrice : 3.1 ");
                        $price = $this->calculateOvenightLowestRatePrecise($this->full_day_length, $this->length_of_stay);
                        $PriceFullDay += $price;
                    }
                } elseif ($this->full_day_length > $this->overnightLength) {
                    $PriceFullDay += $this->overnightPrice;
                    $this->log->info("calculateAccumulatePrice : 4 ");

                    if ($this->after_overnight_length > 0 && $this->after_overnight_length >= $this->dayLength) {
                        // Entry Overnight + Day + Again Overnight 
                        $PriceFullDay += $this->overnightPrice + $this->dailyMaxPrice;
                        $this->log->info("calculateAccumulatePrice : 4.1 ");
                    }
                    // Entry Normal Time case
                    else if ($this->after_overnight_length > 0 && $this->after_overnight_length <= $this->dayLength) {

                        $this->log->info("calculateAccumulatePrice : 5 ");
                        $price = $this->calculateOvenightLowestRatePrecise($this->after_overnight_length, $this->length_of_stay);
                        $this->log->info("After Overnight Length : {$this->after_overnight_length} Price : { $price } ");
                        $this->log->info("calculateAccumulatePrice : 6 ");
                        $PriceFullDay += $price;
                    }

                    // Entry Overnight And Exit Normal 
                    else if ($this->arrival_to_start_length <= 0) {
                        $this->log->info("calculateAccumulatePrice : 3.1 ");
                        $price = $this->calculateOvenightLowestRatePrecise($this->full_day_length, $this->length_of_stay);
                        $PriceFullDay += $price;
                    }
                }
            }

            return $PriceFullDay;
        }

        // Entry exit in overnight 
        if ($this->is_exit_overnight && $this->is_entry_overnight) {
            $this->log->info("calculateAccumulatePrice : 2.6 ");
            $PriceFullDay += $this->overnightPrice;
        }

        return $PriceFullDay;
    }
}
