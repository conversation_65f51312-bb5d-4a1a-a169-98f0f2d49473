<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Services\Mailers\UserMailer;
use App\Models\ParkEngage\PermitRequestServiceMapping;

class PermitRequestRenewHistory extends Model
{
    use SoftDeletes;

    public $table = 'permit_requests_renew_history';

    protected $fillable = [];
   /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $hidden = ['updated_at'];

    protected $dates = ['deleted_at'];
    protected $appends = ['approved','idfront', 'idback','formatted_desired_start_date', 'formatted_desired_end_date','is_expired'];

    public static $searchFields = [
        'email', 'name'
    ];

    public static $validParams = [
        'desired_start_date' => 'required|date|after:yesterday',
        'email' => 'required|email',
    ];

    public static $validParamsMessages = [
        'desired_start_date.after' => 'The desired start date must not be in past'        
    ];

    public function getFormattedDesiredStartDateAttribute()
    {
        $date = $this->desired_start_date;
        $day = date('d', strtotime($date));
        $monthYear = date('F, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
          $second_last_digit = substr($number, -2, 1);
          $suffix = 'th';
          if ($second_last_digit != '1')
          {
            switch ($last_digit)
            {
              case '1':
                $suffix = 'st';
                break;
              case '2':
                $suffix = 'nd';
                break;
              case '3':
                $suffix = 'rd';
                break;
              default:
                break;
            }
          }
          if ((string) $number === '1') $suffix = 'st';
          return $number.$suffix . ' '.$monthYear;
    }
    
    public function getIsExpiredAttribute()
    {
        $today = strtotime(date("Y-m-d"));
        $endDate=strtotime($this->desired_end_date);
        $is_expired = 0;
        if($today > $endDate){
           $is_expired = 1;
           
        }
        return $is_expired;
    }
    public function getFormattedDesiredEndDateAttribute()
    {
        
        $date = $this->desired_end_date;
        $day = date('d', strtotime($date));
        $monthYear = date('F, Y', strtotime($date));
        $number = (string) $day;
        $last_digit = substr($number, -1);
          $second_last_digit = substr($number, -2, 1);
          $suffix = 'th';
          if ($second_last_digit != '1')
          {
            switch ($last_digit)
            {
              case '1':
                $suffix = 'st';
                break;
              case '2':
                $suffix = 'nd';
                break;
              case '3':
                $suffix = 'rd';
                break;
              default:
                break;
            }
          }
          if ((string) $number === '1') $suffix = 'st';
          return $number.$suffix . ' '.$monthYear;
    }
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function facility()
    {
        return $this->belongsTo(Facility::class);
    }

    public function monthlyRateType()
    {
        return $this->hasOne(FacilityRate::class, 'id', 'monthly_rate_id');
    }
   
    public function transaction()
    {
        return $this->belongsTo(AuthorizeNetTransaction::class, 'anet_transaction_id');
    }

    public function PermitRate()
    {
        return $this->belongsTo('App\Models\PermitRate','permit_rate_id');
    }

    public function getApprovedAttribute()
    {
        return $this->approved_on ? true : false;
    }

    public function loadRelations()
    {
        return $this->load('user', 'facility', 'monthlyRateType.rateDescription', 'transaction');
    }

    public function getIdfrontAttribute()
    {
		if($this->image_front){
			return config('app.url') . '/permit-image-front/' . $this->id;	
		}else{
			return '';
		}        
    }

    public function getIdbackAttribute()
    {
		if($this->image_back){
			return config('app.url') . '/permit-image-back/' . $this->id;
		}else{
			return '';
		}        
    }

	public function PermitVehicle(){
		return $this->hasMany('App\Models\PermitVehicleMapping','permit_request_id');
	}
    
    public function permitTicket(){
		return $this->hasMany('App\Models\PermitTicket','permit_request_id');
	}

    public function getIdProofFrontAttribute()
    {
        return config('app.url') . '/permit-id-proof-front/' . $this->id;	
	}

    public function getIdProofBackAttribute()
    {
        return config('app.url') . '/permit-id-proof-back/' . $this->id;	
    }

    public function getUtilityBillAttribute()
    {
        return config('app.url') . '/permit-id-utility-bill/' . $this->id;
    }

    public function getTaxReturnAttribute()
    {
        return config('app.url') . '/permit-id-tax-return/' . $this->id;
    }

    public function getDriversLicenseAttribute()
    {
        return config('app.url') . '/permit-id-drivers-license/' . $this->id;
    }

    public function getPayStubAttribute()
    {
        return config('app.url') . '/permit-id-pay-stub/' . $this->id;
    }

    public function getProofOfLowIncomeAttribute()
    {
        return config('app.url') . '/permit-id-proof-of-low-income/' . $this->id;
    }

    public function getAnyAttribute()
    {
        return config('app.url') . '/permit-id-any/' . $this->id;
    }

    public function getProofOfLowIncomeExcelAttribute()
    {
        return config('app.url') . '/permit-id-proof-of-low-income-excel/' . $this->id;
    }

    public function permitRequestDocument()
    {
        return $this->hasMany('App\Models\ParkEngage\PermitRequestDocuments');
    }

    public function PermitDoc(){
		return $this->hasMany('App\Models\ParkEngage\PermitRequestDocuments');
	}

    public function warning(){
		return $this->hasMany('App\Models\ParkEngage\Warning', 'permit_request_id');
	}

  public function citation(){
		return $this->hasMany('App\Models\ParkEngage\TicketCitation', 'permit_request_id');
	}

  public function getPermitRequest()
  {
    return $this->belongsTo('App\Models\PermitRequest','permit_request_id');
  }
  
  public function PermitRequestServiceMapping(){
    return $this->hasMany('App\Models\ParkEngage\PermitRequestServiceMapping', 'permit_request_id');
  }

  public function getPermitRequestRenewHistory()
    {
        return $this->hasMany('App\Models\PermitRequestRenewHistory','permit_request_id');
    }

    public function latestRenewHistory()
    {
        return $this->hasOne('App\Models\PermitRequestRenewHistory', 'permit_request_id')
                    ->orderBy('created_at', 'desc');
    } 
    
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'admin_user_id');
    }

    
}


