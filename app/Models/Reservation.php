<?php

namespace App\Models;

use Carbon\Carbon;

use Auth;
use Artisan;
use Paginator;

use App\Classes\Ticketech;
use App\Classes\LoyaltyProgram;
use App\Classes\AuthorizeNet\Transactions;
use App\Classes\AuthorizeNet\TransactionsApplePay;
use App\Classes\DataTransGateway;
use App\Exceptions\ApiGenericException;
use App\Exceptions\UserNotAuthorized;
use App\Http\Helpers\QueryBuilder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use App\Services\Mailers\UserMailer;
use App\Services\Image;
use App\Models\LoyaltyTransactions;
use Log;
use App\Models\User;
use App\Models\ParkEngage\UserPaymentGatewayDetail;
use App\Models\ParkEngage\BrandSetting;
use App\Models\UserPass;
use App\Models\ParkEngage\Gate;
use App\Models\OauthClient;
use App\Models\Facility;
use App\Models\ParkEngage\Configuration;
use App\Models\ParkEngage\FacilityBrandSetting;
use App\Models\ParkEngage\Cruise;
use App\Models\ParkEngage\DatacapTransaction;
use App\Models\ParkEngage\MapcoQrcode;
use Illuminate\Support\Facades\DB;
use App\Models\ParkEngage\FacilityConfiguration;

class Reservation extends Model
{
    use SoftDeletes;

    const STATUS_FUTURE = 'Future';
    const STATUS_CURRENT = 'Current';
    const STATUS_PAST = 'Past';
    const STATUS_CANCELLED = 'Canceled';

    public $table = 'reservations';

    public static $reservationType = 'PO';

    protected $fillable = [
        'user_id',
        'facility_id',
        'anet_transaction_id',
        'anet_overstay_transaction_id',
        'total',
        'refund_amount',
        'refund_type',
        'refund_remarks',
        'refund_date',
        'refund_by',
        'start_timestamp',
        'length',
        'length_type',
        'altered_length',
        'ticketech_guid',
        'ticketech_code',
        'cancelled_at',
        'deleted_at',
        'vehicle_id',
        'bonus_rate',
        'bonus_hours',
        'overstay_rate',
        'overstay_hours',
        'discount',
        'company_affilate_id',
        'credit_used',
        'is_avaiability_updated',
        'pay_by',
        'partner_id',
        'is_ticket',
        'checkin_status',
        'end_timestamp',
        'days',
        'license_plate_number',
        'make_model',
        'no_of_visitor',
        'processing_fee',
        'ex_month',
        'ex_year',
        'user_consent',
        'license_plate',
        'thirdparty_integration_id',
        'booking_source',
        'reservation_amount',
        'parking_amount',
        'terminal_id',
        'is_event_scan',
        'event_scan_at',
        'cruise_id',
        'event_user_id',
        'event_id',
        'device_type',
        'payment_token',
        'session_id',
        'payment_gateway',
        'promocode',
        'is_update'
    ];

    public static $validationRules = [
        'description' => 'required|string|max:255',
        'facility_id' => 'required|numeric',
        //'arrival' => 'required|date|after:-10 minutes',
        'arrival' => 'required|date',
        'length' => 'required|numeric',
        'total' => 'required|numeric',
        'use_bonus' => 'required|boolean'
        //'overstay' => 'required|boolean'

    ];

    public static $validationMessages = [
        //'after' => 'Your reservation cannot begin in the past.'
    ];

    protected $appends = [
        'formatted_start_time',
        'formatted_start_date',
        'formatted_start_date_time',
        'formatted_end_date_time',
        'formatted_end_time',
        'formatted_end_date',
        'valid_date_range',
        'canceled',
        'status',
        'reservation_code',
        'cancellation_link',
        'view_link',
        'end_time',
        'bonus_used',
        'original_length',
        'original_rate',
        'prepaid_checkin_time',
        'is_townsend_event',
        'formatted_length'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'start_timestamp'
    ];

    public static $searchFields = [
        'ticketech_code'
    ];

    /**
     * Format to use for date strings
     * Note: Naming this dateFormat causes a bug - maybe overriding a default laravel model property?
     *
     * @var string
     */
    protected $dateStringFormat = "m/j/Y";

    /**
     * Format to use for time strings
     *
     * @var string
     */
    protected $timeStringFormat = 'h:i A';

    /**
     * Register event listeners for this model
     *
     * @return [type] [description]
     */
    protected static function boot()
    {
        parent::boot();

        // Ensure reservation has a ticketech code before saving
        self::saving(
            function ($reservation) {
                if (isset($reservation->ticketech_code) && $reservation->ticketech_code) {
                    return $reservation;
                }

                $reservation->generateTicketechCode();
                return $reservation;
            }
        );
    }

    /**
     * Get the user that this reservation belongs to
     *
     * @return [type] [description]
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User');
    }





    /**
     * Get the facility for this reservation
     *
     * @return [type] [description]
     */
    public function facility()
    {
        return $this->belongsTo('App\Models\Facility');
    }

    /**
     * Get the Reservatoin History for this reservation
     *
     * @return [type] [description]
     */
    public function reservations_history()
    {
        return $this->hasMany('App\Models\ReservationHistroy', 'res_id');
    }

    public function promo_usage()
    {
        return $this->belongsTo('App\Models\PromoUsage', 'ticketech_code', 'reservation_id');
    }

    /**
     * Get the loyalty transaction for this reservation
     *
     * @return [type] [description]
     */
    public function loyalty_transactions()
    {
        return $this->belongsTo('App\Models\LoyaltyTransactions', 'id', 'reservation_id');
    }

    /**
     * Get the transaction for this reservation
     *
     * @return [type] [description]
     */
    public function transaction()
    {
        return $this->belongsTo('App\Models\AuthorizeNetTransaction', 'anet_transaction_id');
    }

    public function ticketechResponse()
    {
        return $this->hasOne(TicketechResponse::class);
    }

    public function rate()
    {
        return $this->belongsTo('App\Models\Rate');
    }

    // We are storing start time and length due to a UI decision made at the start of the project.
    // It would be more appropriate to store start and end times now, but the rate engine and too many
    // other systems depend on the start time + length system
    public function getEndTimeAttribute()
    {
        if ($this->end_timestamp) {
            $time = Carbon::parse($this->end_timestamp);
            return $time;
        } else {
            $time = Carbon::parse($this->start_timestamp)->addHours($this->length);

            // If the length has a half hour, add another 30 minutes to the reservation
            if (intval($this->length) != $this->length) {
                //$time->addMinutes(30);
                $timarr = explode('.', $this->length);
                // $minute = ('.' . $timarr[1]) * 60;
                // $time->addMinutes($minute);
                $time->addMinutes($timarr[1]);
            }

            return $time;
        }
    }

    public function getFormattedStartDateAttribute()
    {
        $time = Carbon::parse($this->start_timestamp);
        return $time->format('M d, Y');
    }

    public function getFormattedStartTimeAttribute()
    {
        return Carbon::parse($this->start_timestamp)->format('h:i A');
    }

    public function getFormattedEndDateAttribute()
    {
        $time = Carbon::parse($this->start_timestamp)->addHours($this->length);

        if (intval($this->length) != $this->length) {
            $timarr = explode('.', $this->length);
            // $minute = ('.' . $timarr[1]) * 60;
            // $time->addMinutes($minute);
            $time->addMinutes($timarr[1]);
        }

        return $time->format('M d, Y');
    }

    public function getFormattedEndTimeAttribute()
    {
        return $this->end_time->format('h:i A');
    }

    public function getFormattedStartDateTimeAttribute()
    {
        return Carbon::parse($this->start_timestamp)->format('D, M jS Y g:i A');
    }

    public function getFormattedEndDateTimeAttribute()
    {
        if ($this->end_timestamp) {
            $time = Carbon::parse($this->end_timestamp);
            return $time->format('D, M jS Y g:i A');
        } else {
            $time = Carbon::parse($this->start_timestamp)->addHours($this->length);

            if (intval($this->length) != $this->length) {
                $timarr = explode('.', $this->length);
                // $minute = ('.' . $timarr[1]) * 60;
                // $time->addMinutes($minute);
                $time->addMinutes($timarr[1]);
            }

            return $time->format('D, M jS Y g:i A');
        }
    }

    /**
     * Get formatted created on attribute
     *
     * @return [type] [description]
     */
    public function getFormattedCreatedAtAttribute()
    {
        return Carbon::parse($this->created_at)->format($this->dateStringFormat);
    }

    public function getCanceledAttribute()
    {
        return $this->cancelled_at !== null;
    }

    /**
     * Get formatted date(s) that the reservation is valid
     *
     * @return [type] [description]
     */
    public function getValidDateRangeAttribute()
    {
        $start = Carbon::parse($this->start_timestamp);
        $startDate = $start->format($this->dateStringFormat);
        $endDate = $start->addHours($this->length)->format($this->dateStringFormat);

        return $startDate === $endDate ? $startDate : "$startDate - $endDate";
    }

    function getEndTime($decimalHours)
    {
        $hours = floor($decimalHours);
        $mins = round(($decimalHours - $hours) * 60);
        $timeInMinutes = ($hours * 60) + $mins;

        return $timeInMinutes;
    }

    public function getStatusAttribute()
    {
        // change time zone while getting status
        $this->setCustomTimezone($this->facility_id);
        if ($this->canceled) {
            return self::STATUS_CANCELLED;
        }
        $now = new Carbon();
        $start = Carbon::parse($this->start_timestamp);
        $timeInMinutes = $this->getEndTime($this->length); // get length in mints
        $end = Carbon::parse($this->start_timestamp)->addMinutes($timeInMinutes);
        // $end = Carbon::parse($this->start_timestamp)->addHours($this->length);

        if ($this->is_ticket == '1') {
            // vijay - 27-06-2023 for geted flow.           
            if ($now->gt($end)) {
                // check active Checking if any before update
                if (isset($this->ticket->is_checkout) && $this->ticket->is_checkout == 1 && ($this->thirdparty_integration_id == '' || $this->thirdparty_integration_id == 0)) {    //checked the third parti condition by Ashutosh 10-10-2023
                    if (isset($this->print_reciept_url)) {
                        unset($this->print_reciept_url);
                    }
                    $this->is_ticket = '2';
                    $this->save();
                    return self::STATUS_PAST;
                }
            }
            // close
            return self::STATUS_CURRENT;
        }


        if ($this->is_ticket == '2') {
            return self::STATUS_PAST;
        }

        if ($now->lt($start)) {
            return self::STATUS_FUTURE;
        }

        if ($now->lt($end)) {
            return self::STATUS_CURRENT;
        }

        return self::STATUS_PAST;
    }

    /**
     * Used to cancel the reservation without authorization.
     * Generated from the last 15 characters of the ticketech_guid
     */
    public function getReservationCodeAttribute()
    {
        if (!$this->ticketech_guid) {
            return false;
        }

        return substr(str_replace('-', '', $this->ticketech_guid), -15);
    }

    public function getBonusUsedAttribute()
    {
        return $this->bonus_rate !== null;
    }

    public function getOriginalRateAttribute()
    {
        return $this->total - $this->bonus_rate;
    }

    public function getOriginalLengthAttribute()
    {
        return $this->length - $this->bonus_hours;
    }

    public function getCancellationLinkAttribute()
    {
        return config('app.web_url') . "/reservation/cancel/{$this->reservation_code}/{$this->ticketech_code}";
    }

    public function getViewLinkAttribute()
    {
        return config('app.web_url') . "/reservation/{$this->reservation_code}/{$this->ticketech_code}/print";
    }

    public function getBookedAttribute()
    {
        return $this->anet_transaction_id !== null;
    }

    public function applyBonus()
    {
        $this->verifyBonusEligible();

        $this->bonus_rate = $this->facility->reservation_bonus_rate;
        $this->bonus_hours = $this->facility->reservation_bonus_hours;

        $this->total = $this->total + $this->bonus_rate;
        $this->length = $this->length + $this->bonus_hours;

        return $this;
    }

    public function removeBonus()
    {
        $this->verifyBonusEligible();

        $this->total = $this->total - $this->bonus_rate;
        $this->length = $this->length - $this->bonus_hours;

        $this->bonus_rate = null;
        $this->bonus_hours = null;

        return $this;
    }

    public function applyOverstay()
    {
        $this->verifyOverstayEligible();
        $this->overstay_rate = $this->facility->reservation_overstay_rate;
        $this->overstay_hours = $this->facility->reservation_overstay_hours;

        $this->total = $this->total + $this->overstay_rate;
        $this->length = $this->length + $this->overstay_hours;


        return $this;
    }

    protected function verifyBonusEligible()
    {
        if ($this->booked) {
            throw new ApiGenericException("Cannot apply bonus to a reservation that has already been booked.");
        }

        if ($this->facility->reservation_bonus_rate <= 0) {
            throw new ApiGenericException("Facility does not have a valid reservation bonus rate.");
        }
    }

    protected function verifyOverstayEligible()
    {
        if (!$this->booked) {
            throw new ApiGenericException("Cannot apply overstay to a reservation that is not booked.");
        }


        if ($this->facility->reservation_overstay_rate <= 0) {
            throw new ApiGenericException("Facility does not have a valid reservation overstay rate.");
        }
    }

    public function checkCancellationCode($code)
    {
        return $code === $this->cancellation_code && $code !== false;
    }

    /**
     * Load relations needed for reservation id
     */
    public function withRelationFacilityAndTransaction()
    {
        return $this->load('facility', 'facility.geolocations', 'facility.photos', 'transaction');
    }

    /**
     * Load relations needed for front end
     */
    public function withRelations()
    {
        return $this->load('facility', 'ticket', 'facility.geolocations', 'facility.photos', 'transaction', 'user', 'promo_usage', 'facility.parkingDevices', 'facility.facilityConfiguration');
    }

    /**
     * Generate a random tickech code for this reservation.
     * Should be IQ(random 6 digit number)
     * Should be unique
     *
     * @return [type] [description]
     */
    public function generateTicketechCode()
    {
        $code = self::$reservationType . rand(100000, 999999);
        while (Reservation::where('ticketech_code', $code)->first()) {
            $code = self::$reservationType . rand(100000, 999999);
        }

        $this->ticketech_code = $code;
        return $code;
    }

    public function generateBarCode()
    {

        $ticket_type = config('parkengage.HUB_ZEAG.ticket_type');
        $region_code = config('parkengage.HUB_ZEAG.region_code');
        $parkhouse_id = config('parkengage.HUB_ZEAG.parkhouse_id');
        $mixed_field = config('parkengage.HUB_ZEAG.mixed_field');
        $account_id = config('parkengage.HUB_ZEAG.account_id');
        $unique_id = rand(********, ********);
        $barcode = $ticket_type . $region_code . $parkhouse_id . $mixed_field . $account_id . $unique_id;
        while (Reservation::where('thirdparty_code', $barcode)->first()) {
            $barcode = $this->generateBarCode();
        }

        // dd($barcode , $ticket_type , $region_code , $parkhouse_id , $mixed_field , $account_id , $unique_id);

        return $barcode;
    }

    private function setTranctions()
    {
        if (!$this->transaction) {
            if (config('parkengage.ROC_FACILITY') == $this->facility_id) {
                $this->transaction = DatacapTransaction::select('*', DB::raw('card_last_four as payment_last_four'))->where(['reservation_id' => $this->id])->first();
            }
        }
    }

    public function getReservationVal()
    {
        /*if (!$this->ticketech_guid) {
            throw new ApiGenericException('No ticketech reservation exists for this reservation.');
        }*/

        $this->setTranctions();

        // if (!$this->transaction) {
        //     throw new ApiGenericException('No transaction for this reservation.');
        // }

        $this->facility->load('geolocations');

        $fields = [ // all data used in coupon generation
            'facility' => $this->facility,
            'photo' => $this->facility->photos,
            'geolocation' => $this->facility->geolocations,
            'reservation' => $this,
            'payment' => $this->transaction,
            'staticMap' => $this->facility->generateStaticMapReservationEmail(),
            'hours' => $this->facility->hoursOfOperation,
            'addressLink' => $this->facility->generateAddressLink(),
            'user' => $this->user
        ];
        return $fields;
    }

    /**
     * Generate JPG for stub customers can use to
     * redeem their parking reservation
     *
     * @return [type] [description]
     */
    public function generateStubJpg($client_secret = '')
    {
        $html = $this->generateStubHtml($client_secret);

        $image = app()->make(Image::class);
        $image->setOption('width', '1000');
        $image->setOption("enable-local-file-access", true);
        return $image->getOutputFromHtmlString($html);
    }

    public function generateStubJpgException($message = "404 not found")
    {
        $fields = [
            'message' => $message
        ];
        $html = view('reservation.print-reservation-exceptions', $fields)->render();
        $image = app()->make(Image::class);
        $image->setOption('width', '670');
        return $image->getOutputFromHtmlString($html);
    }

    /**
     * Generate HTML for the reservation stub that customers
     * can use to redeem their parking reservation
     *
     * @return [type] [description]
     */
    public function generateStubHtml($client_secret = '')
    {
        /*if (!$this->ticketech_guid) {
            throw new ApiGenericException('No ticketech reservation exists for this reservation.');
        }*/

        $this->setTranctions();

        // if (!$this->transaction) {
        //     throw new ApiGenericException('No transaction for this reservation.');
        // }

        $this->facility->load('geolocations');

        $warning_on_reservation = $this->warning_on_reservation;
        $warning_on_reservation_msg = '';
        if ($warning_on_reservation) {
            $warning_on_reservation_msg = $this->warning_on_reservation_msg;
        }

        $userpass = [];
        if ($this->user_pass_id != '') {
            $userpass = UserPass::find($this->user_pass_id);
        }
        $brand_setting = BrandSetting::where('user_id', $this->user->created_by)->first();
        # Vijay deployed : 30-10-2023
        $facility_brand_setting = $brand_setting_logo = '';
        $brand_setting_facility = FacilityBrandSetting::where('facility_id', $this->facility->id)->first();
        if ($brand_setting_facility) {
            $facility_brand_setting = $brand_setting_facility->id;
        } else {
            $settings = BrandSetting::where('user_id', $this->user->created_by)->first();
            $brand_setting_logo = $settings->id;
        }
        $cruise = Cruise::select('cruise_name')->where('partner_id', $this->user->created_by)->where('id', $this->cruise_id)->first();
        if ($cruise) {
            $this->cruise_name = isset($cruise->cruise_name) ? $cruise->cruise_name : '';
        }
        //added exit time for cruise schedule
        if ($this->cruise_id != '' && $this->schedule_id != '') {
            $end_time = Carbon::parse($this->start_timestamp)->addHours($this->length);
            if (intval($this->length) != $this->length) {
                $timarr = explode('.', $this->length);
                $end_time->addMinutes($timarr[1]);
            }
            $end_time = $end_time->format('Y-m-d H:i:s');
            $this->cruise_end_time = $end_time;
        }

        if (isset($this->thirdparty_code) && $this->thirdparty_code != '') {
            $this->barcode = $this->thirdparty_code;
        } else {
            $this->barcode = $this->ticketech_code;
        }
        $facilityConfig = FacilityConfiguration::select("facility_footer_email","show_reservation_qrcode")->where("facility_id", $this->facility->id)->first();

        # !! Vijay deployed : 30-10-2023
        $fields = [ // all data used in coupon generation
            'facility' => $this->facility,
            'warning_on_reservation_msg' => $warning_on_reservation_msg,
            'photo' => $this->facility->photos,
            'geolocation' => $this->facility->geolocations,
            'reservation' => $this,
            'payment' => $this->transaction,
            'staticMap' => $this->facility->generateStaticMap(),
            'hours' => $this->facility->hoursOfOperation,
            'addressLink' => $this->facility->generateAddressLink(),
            'user' => $this->user,
            'brand_setting' => $brand_setting,
            'brand_setting_logo' => $brand_setting_logo,
            'facility_brand_setting' => $facility_brand_setting,
            'bar_image_path' => $this->generateBarcodeJpgNew($client_secret),
            'user_pass' => $userpass,
            'email_footer' => isset($facilityConfig) ? $facilityConfig->facility_footer_email : '',
            'show_reservation_qrcode' => isset($facilityConfig) ? $facilityConfig->show_reservation_qrcode : '1'
        ];
        if ($client_secret != '') {
            return view('reservation.print-reservation-atlanta', $fields)->render();
        } else {
            return view('reservation.print-reservation', $fields)->render();
        }

        //return 'hi';
    }

    public function generateBarcodeJpg()
    {
        $html = $this->generateBarcodeHtml();

        $image = app()->make(Image::class);
        $image->setOption('width', '670');
        return $image->getOutputFromHtmlString($html);
    }

    public function generateBarcodeJpgNew($client_secret = '')
    {
        $html = $this->generateBarcodeHtml($client_secret);

        $image = app()->make(Image::class);
        $image->setOption('width', '1000');
        return $image->getOutputFromHtmlString($html);
    }

    public function generateBarcodeHtml($client_secret = '')
    {
        /*if (!$this->ticketech_guid) {
            throw new ApiGenericException('No ticketech reservation exists for this reservation.');
        }*/

        $this->setTranctions();
        // if (!$this->transaction) {
        //     throw new ApiGenericException('No transaction for this reservation.');
        // }
        $barcode = "";
        if (isset($this->thirdparty_code) && $this->thirdparty_code != '') {
            $barcode = $this->thirdparty_code;
        } else {
            $barcode = $this->ticketech_code;
        }
        if ($client_secret != '') {
            return view('reservation.barcode-atlanta', ['barcode' => $barcode]);
        } else {
            return view('reservation.barcode', ['barcode' => $barcode]);
        }
    }

    /**
     * Send the reservation details to the user
     *
     * @return [type] [description]
     */
    public function emailReservationToUser($email = false)
    {

        $options = ['reservationId' => $this->id];

        if ($email) {
            $options['--email'] = $email;
        }
        return Artisan::queue('reservation:email', $options);
    }

    public function emailReservationToPartnerUser($client_secret, $request = NULL)
    {
        $options = ['reservationId' => $this->id];
        if ($client_secret) {
            $options['--client_secret'] = $client_secret;
        }

        if (!is_null($request)) {
            $options['--request'] = $request->all();
        }
        return Artisan::queue('reservation:email', $options);
    }

    /**
     * Send the reservation 1 hour extension details to the user
     *
     * @return [type] [description]
     */
    public function emailReservationExtensionToUser($email = false)
    {

        $options = ['reservationId' => $this->id];

        if ($email) {
            $options['--email'] = $email;
        }

        return Artisan::queue('reservation:email-extend-stay', $options);
    }

    public function emailCancellation($client_secret = '')
    {
        return Artisan::queue('reservation:email-cancellation', ['reservationId' => $this->id, '--client_secret' => $client_secret]);
    }

    public function emailCancellationWithNoGuid()
    {
        return Artisan::queue('reservation:email-cancellation-no-guid', ['reservationId' => $this->id]);
    }

    public function hasPermissionToCancel(User $user = null)
    {
        // Check that this reservation hasn't already been cancelled
        if ($this->canceled) {
            throw new ApiGenericException('Reservation has already been cancelled.');
        }

        // Check that this reservation has not yet started. Admins and customer service can cancel reservations regardless of time
        if ($this->status !== 'Future') {
            if (!$user || (!$user->isAdmin && !$user->hasRole(Role::CUSTOMER_SERVICE) && !$user->hasRole(Role::PARTNER) && !$user->hasRole(Role::SUBPARTNER))) {
                throw new UserNotAuthorized('Reservation has already begun or is already over and cannot be cancelled.');
            }
        }

        return true;
    }

    /**
     * Cancel the reservation, refunding or voiding the authorize.net charge as well
     */
    public function cancel($client_secret = '')
    {
        $this->hasPermissionToCancel(Auth::user());
        // Cancel via ticketech
        //cancel via ticketech if ticketech_guid is not null
        $ticketechResponse = false;
        /*if((isset($this->ticketech_guid)) && ($this->ticketech_guid!=''))
       {
           $ticketechResponse = (new Ticketech())->cancelReservation($this);
       }*/

        $authNetResponse = '';
        $is_partner = 0;
        $partnerPaymentDetails = [];
        $user = User::where('id', $this->transaction->user_id)->first();
        if ($user->user_type == 3) {
            $partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $user->id)->first();
            $is_partner = 1;
        } elseif ($user->user_type == 4 || $user->user_type == 5) {
            $partnerPaymentDetails = UserPaymentGatewayDetail::where('user_id', $user->created_by)->first();
            if ($partnerPaymentDetails) {
                $is_partner = 1;
            } else {
                throw new NotFoundException('No payment gateway details found for this partner.');
            }
        } else {
            $is_partner = 0;
        }
        if ((isset($this->transaction->total)) && (($this->transaction->total) > 0)) {
            // Refund authorize.net transaction
            $authNetResponse = (new Transactions())->isReservation()->setTransaction($this->transaction)->isPartner($partnerPaymentDetails)->refundOrVoid($partnerPaymentDetails);
            /*if($this->pay_by=='1')
            {
                $authNetResponse = (new TransactionsApplePay())->isReservation()->setTransaction($this->transaction)->refundOrVoid();
            }else{
               $authNetResponse = (new Transactions())->isReservation()->setTransaction($this->transaction)->refundOrVoid();
            }*/
        }

        if ($this->user_pass_id != '') {
            $userPass = UserPass::find($this->user_pass_id);
            if ($userPass) {
                $userPass->consume_days = $userPass->consume_days - 1;
                $userPass->remaining_days = $userPass->remaining_days + 1;
                $userPass->save();
            }
        }


        $this->cancelled_at = Carbon::now();

        // revert loyalty points used in reservations if any 
        /*if ($this->loyalty_point_used) {
            $transaction = LoyaltyTransactions::where('reservation_id', $this->id)
                            ->where('type', '=', LoyaltyTransactions::TYPE_REDEEM)
                            ->first();

            if ($transaction) {
                $end = Carbon::parse($this->created_at);
                $now = Carbon::now();
                if ($end->diffInDays($now) <= LoyaltyProgram::REVERSAL_DAYS_LIMIT) {
                    $dataSet = array(
                        "order" => $transaction->reference_no
                    );

                    $transactionData = array(
                        'reservation_id' => $this->id,
                        'transaction_id' => $transaction->id
                    );
                    $response = LoyaltyProgram::voidPoints($dataSet, $this->user_id, $transactionData);
                }
                else {
                    $dataSet = array(
                        "sva" => $transaction->account->account_no,
                        "amount" => $transaction->points
                    );

                    $response = LoyaltyProgram::loadPoints($dataSet, $this->user_id);
                }
            }
        }*/

        $this->save();

        if ($client_secret != '') {
            $this->emailCancellation($client_secret);
        } else {
            $this->emailCancellation();
        }

        //$this->emailCancellation();

        return [
            'cancelled' => true,
            'ticketech_response' => $ticketechResponse,
            'authnet_response' => $authNetResponse,
            'reservation' => $this->withRelations()
        ];
    }

    /**
     * Cancel the reservation, refunding or voiding the authorize.net charge as well
     */
    public function cancelBulkWithNoGuid()
    {
        // Cancel via ticketech
        //cancel via ticketech if ticketech_guid is not null
        $ticketechResponse = false;
        if ((isset($this->ticketech_guid)) && ($this->ticketech_guid != '')) {
            $ticketechResponse = (new Ticketech())->cancelReservation($this);
        }

        $authNetResponse = '';
        if ((isset($this->transaction->total)) && (($this->transaction->total) > 0)) {
            // Refund authorize.net transaction
            $authNetResponse = (new Transactions())->isReservation()->setTransaction($this->transaction)->refundOrVoid();
        }
        $this->cancelled_at = Carbon::now();

        // revert loyalty points used in reservations if any 
        if ($this->loyalty_point_used) {
            $transaction = LoyaltyTransactions::where('reservation_id', $this->id)
                ->where('type', '=', LoyaltyTransactions::TYPE_REDEEM)
                ->first();

            if ($transaction) {
                $end = Carbon::parse($this->created_at);
                $now = Carbon::now();
                if ($end->diffInDays($now) <= LoyaltyProgram::REVERSAL_DAYS_LIMIT) {
                    $dataSet = array(
                        "order" => $transaction->reference_no
                    );

                    $transactionData = array(
                        'reservation_id' => $this->id,
                        'transaction_id' => $transaction->id
                    );
                    $response = LoyaltyProgram::voidPoints($dataSet, $this->user_id, $transactionData);
                } else {
                    $dataSet = array(
                        "sva" => $transaction->account->account_no,
                        "amount" => $transaction->points
                    );

                    $response = LoyaltyProgram::loadPoints($dataSet, $this->user_id);
                }
            }
        }

        $this->save();

        $this->emailCancellationWithNoGuid();

        return [
            'cancelled' => true,
            'ticketech_response' => $ticketechResponse,
            'authnet_response' => $authNetResponse,
            'reservation' => $this->withRelations()
        ];
    }

    // Recreate a reservation, useful for
    public function recreate(Request $request, Reservation $reservation)
    {
        Artisan::call(
            'reservation:create',
            [
                'start_timestamp' => $request->start_timestamp,
                'length' => $request->length,
                'facility_id' => $request->facility_id
            ]
        );
    }


    public function userPass()
    {
        return $this->belongsTo('App\Models\UserPass', 'user_pass_id');
    }

    public function ticket()
    {
        return $this->hasOne('App\Models\Ticket', 'reservation_id');
    }

    public function mapcoQrCode()
    {
        return $this->HasMany('App\Models\ParkEngage\MapcoQrcode', 'reservation_id');
    }

    public function flightDetails()
    {
        return $this->hasOne('App\Models\FlightDetails', 'reservation_id');
    }

    public function vehicle()
    {
        return $this->belongsTo('App\Models\PermitVehicle');
    }

    // added the customTimezone function
    public function setCustomTimezone($facility_id)
    {
        $facility = Facility::find($facility_id);

        $secret = OauthClient::where('partner_id', $facility->owner_id)->first();
        $partnerTimezone = UserPaymentGatewayDetail::where('user_id', $this->partner_id)->first();
        if ($partnerTimezone) {
            if ($partnerTimezone->timezone != '') {
                if ($facility->timezone != '') {
                    date_default_timezone_set($facility->timezone);
                } else if ($partnerTimezone->timezone != '') {
                    date_default_timezone_set($partnerTimezone->timezone);
                }
            }
        }
    }

    public function getPrepaidCheckinTimeAttribute()
    {
        $config = Configuration::where('field_name', 'prepaid-checkin-time')->where("facility_id", $this->facility_id)->first();
        if (count($config) > 0) {
            return $config->field_value;
        } else {
            return 15;
        }
    }

    public function getIsTownsendEventAttribute()
    {
        if (in_array($this->facility_id, [config('parkengage.METRO_FACILITY'), config('parkengage.EVENT_TOWNSEND_FACILITY')])) {
            return true;
        } else {
            return false;
        }
    }

    // Get discount value if any found against reservation
    public function getDiscountedValue($res_id)
    {
        $reservation_history = ReservationHistroy::where('res_id', $res_id)->get();
        if (count($reservation_history) > 0) {
            foreach ($reservation_history as $history) {
                if ($history->discount > 0) {
                    return $history->discount;
                }
            }
        }
        return "0";
    }

    // Get applied processing fee for reservation
    public function getAppliedProcessingFees($res_id)
    {
        $reservation_history = ReservationHistroy::where('res_id', $res_id)->get();
        if (count($reservation_history) > 0) {
            foreach ($reservation_history as $history) {
                if ($history->processing_fee > 0) {
                    return $history->processing_fee;
                }
            }
        }
        return "0";
    }

    // Get applied tax fee for reservation
    public function getAppliedTaxFees($res_id)
    {
        $tax_fee = 0;
        $reservation_history = ReservationHistroy::where('res_id', $res_id)->get();
        if (count($reservation_history) > 0) {
            foreach ($reservation_history as $history) {
                $tax_fee += $history->tax_fee;
            }
        }
        return $tax_fee;
    }

    public static function isConflictingReservation($reservation, $arrival, $endDateTime)
    {
        $start_timestamp = Carbon::parse($reservation->start_timestamp)->format('Y-m-d H:i:s');
        $endtime = Carbon::parse($reservation->start_timestamp)->addHours($reservation->length);

        if (intval($reservation->length) != $reservation->length) {
            $timarr = explode('.', $reservation->length);
            $endtime->addMinutes($timarr[1]);
        }

        $endtimedb = $endtime->subSecond()->format('Y-m-d H:i:s');
        $arrival = Carbon::parse($arrival)->format('Y-m-d H:i:s');

        // dd(($arrival >= $start_timestamp && $arrival <= $endtimedb), ($endDateTime >= $start_timestamp && $endDateTime <= $endtimedb), ($arrival <= $start_timestamp && $endDateTime >= $endtimedb));

        return ($arrival >= $start_timestamp && $arrival <= $endtimedb) ||
            ($endDateTime >= $start_timestamp && $endDateTime <= $endtimedb) ||
            ($arrival <= $start_timestamp && $endDateTime >= $endtimedb);
    }

    public static function calculateEndDateTime($request)
    {
        $time = Carbon::parse($request->arrival)->addHours($request->length);

        if (intval($request->length) != $request->length) {
            $timarr = explode('.', $request->length);
            $time->addMinutes($timarr[1]);
        }

        return $time->subSecond()->format('Y-m-d H:i:s');
    }

    // Optimise by Lokesh: 16-Sep-2024
    public static function checkExistingReservation($request, $user_id, $reservationlog, $importReservation = false)
    {
        $endDateTime = self::calculateEndDateTime($request);
        // check for existing on behalf 
        if (isset($request->creater_id) && !empty($request->creater_id)) {
            $existingReservations = self::where([
                'facility_id' => $request->facility_id,
                'license_plate' => $request->license_plate
            ]);
        } else {
            $existingReservations = self::where([
                // 'user_id' => $user_id,
                'facility_id' => $request->facility_id,
                'license_plate' => $request->license_plate
            ]);
        }
        //end  


        $existingReservations = $existingReservations->whereDate('start_timestamp', '=', date('Y-m-d', strtotime($request->arrival)))
            ->whereNull('cancelled_at')
            ->where('is_ticket', '0')
            ->get();
        foreach ($existingReservations as $reservation) {
            if (self::isConflictingReservation($reservation, $request->arrival, $endDateTime)) {
                $reservationlog->info("Reservation Already Exists with ID: " . $reservation->ticketech_code);
                if ($importReservation) {
                    return true;
                } else {
                    throw new ApiGenericException('Reservation already exists between the same time slot.');
                }
            }
        }

        if ($request->transaction_id) {
            $transactionCancel = DataTransGateway::dataTransCancelTransaction($request->transaction_id, $request->facility_id);
            $reservationlog->info("Cancelled DataTrans Payment: " . json_encode($transactionCancel));
        }
    }
    #PIMS-10727 DD USM GROUPING

    public static function checkExistingUsmReservation($request, $user_id, $reservationlog, $importReservation = false)
    {
        $endDateTime = self::calculateEndDateTime($request);
        $activeSession = 0;
        if (isset($request->creater_id) && !empty($request->creater_id)) {
            $existingReservations = self::where([
                'facility_id' => $request->facility_id,
                'license_plate' => $request->license_plate
            ]);
        } else {
            $existingReservations = self::where([
                //  'user_id' => $user_id,
                'facility_id' => $request->facility_id,
                'license_plate' => $request->license_plate
            ]);
        }
        $existingReservations = $existingReservations->whereDate('start_timestamp', '=', date('Y-m-d', strtotime($request->arrival)))
            ->whereNull('cancelled_at')
            ->where('is_ticket', '0')
            ->get();
        if ($existingReservations) {
            foreach ($existingReservations as $reservation) {
                if (self::isConflictingReservation($reservation, $request->arrival, $endDateTime)) {
                    if (isset($request->neighborhood_id) && $request->neighborhood_id <= 0) {
                        $reservationlog->info("Reservation Already Exists with ID:: " . $reservation->ticketech_code);
                        throw new ApiGenericException('Reservation already exists between the same time slot with ID: ' . $reservation->ticketech_code);
                    }
                    $neighborhoodData = Facility::select('neighborhood_id', 'id')->find($reservation->facility_id);
                    if ($neighborhoodData) {
                        if ($neighborhoodData->neighborhood_id <= 0 || $neighborhoodData->neighborhood_id == $request->neighborhood_id) {
                            $reservationlog->info("Reservation Already Exists with ID: " . $reservation->ticketech_code);
                            if ($importReservation) {
                                return true;
                            } else {
                                throw new ApiGenericException('Reservation already exists between the same time slot with ID: ' . $reservation->ticketech_code);
                            }
                        }
                    }
                }
            }
        }
    }
    #pims-10727 end

    public function getIsReservationOnlyAttribute()
    {
        $eventExist = MapcoQrcode::where("reservation_id", $this->id)->first();
        return $eventExist ? '0' : '1';
    }

    public function reservationExtend()
    {
        $extendData = [];
        $reservationHistoryData = ReservationHistroy::where('res_id', $this->id)
            ->orderBy('id', 'DESC') // Assuming 'id' is the primary key
            ->get();

        $previousDiscount = 0.0; // Initialize previous discount as float

        foreach ($reservationHistoryData as $key => $history) {
            if (in_array($history->length_type, ['3', '0'])) {
                continue;
            }

            $grandTotal = max(0, $history->payable_amount - $previousDiscount);
            $comment = ($history->length_type ==  1) ? 'Extend' : 'Reduce';

            $extendData[] = [
                'length' => $history->altered_length,
                'grand_total' => (float) $grandTotal,
                'total' => (float) $history->payable_amount,
                'ticketech_code' => $history->ticketech_code,
                'discount_amount' => (float) $history->discount,
                'comment' => $comment,
            ];

            // Store the current discount for the next iteration
            $previousDiscount = $history->discount;
        }

        return $extendData;
    }

    public function getFormattedLengthAttribute()
    {
        if ($this->length > 0) {
            return QueryBuilder::getFormatedDurationByLength($this->length);
        } else {
            return 0;
        }
    }

    /**
     * Alka PIMS-11748 : Vijay : 23-12-2024
     * Date: 05 Dec 2024
     */
    public function sendEmailReservationToPartnerUser($client_secret, $request)
    {
        $options = ['reservationId' => $this->id];
        if ($client_secret) {
            $options['--client_secret'] = $client_secret;
        }
        if (isset($request) && !empty($request)) {
            $options['--request'] = $request;
        }
        // dd($options);
        return Artisan::queue('reservation:email', $options);
    }
}
