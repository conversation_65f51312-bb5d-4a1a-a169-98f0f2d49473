<?php

namespace App\Models;

use App\Classes\CommonFunctions;
use App\Models\ParkEngage\MstMake;
use App\Models\ParkEngage\MstModel;
use App\Models\ParkEngage\MstColor;
use App\Models\ParkEngage\MstStyle;
use App\Models\ParkEngage\MstVehicleType;
use App\Exceptions\ApiGenericException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\ParkEngage\State;
use App\Models\User;
use App\Models\ParkEngage\Country; #pims-13916
use App\Models\ParkEngage\PartnerMakeModelMapping;
use App\Models\FastTrackVehicleMapping;

class PermitVehicle extends Model
{

    use SoftDeletes;

    protected $table = 'permit_vehicles';

    /**
     * Custom primary key is set for the table
     * 
     * @var integer
     */
    protected $primaryKey = 'id';

    /**
     * Maintain created_at and updated_at automatically
     * 
     * @var boolean
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [];
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    protected $hidden = ['updated_at'];

    protected $dates = ['deleted_at'];

    protected $appends = ['state_code'];


    /**
     * storeData
     * @param 
     * @return array
     * @since 0.1
     * <AUTHOR>
     */

    public function vehicles()
    {
        return $this->belongsTo('App\Models\PermitRequest', 'permit_request_id');
    }

    public function permit()
    {
        return $this->belongsTo('App\Models\PermitRequest', 'permit_request_id');
    }

    #DD pims-12970 permit restriction
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id');
    }

    #DD pims-13916
    public function countryRelation()
    {
        return $this->belongsTo('App\Models\ParkEngage\Country', 'country', 'country_code');
    }

    public function permitVehicleMapping()
    {
        return $this->hasOne('App\Models\PermitVehicleMapping', 'permit_vehicle_id')->orderby('id', 'desc');
    }

    public function getStateNameAttribute()
    {
        $state = State::find($this->state_id);
        return isset($state->name) ? $state->name : '';
    }

    #10-09-2024 dushyant return statecode

    public function getStateCodeAttribute()
    {
        $state = State::find($this->state_id);
        return isset($state->state_code) ? $state->state_code : '';
    }

    // Optimise by Lokesh: 16-Sep-2024
    public static function handleVehicleDetails($request, $user, $log)
    {
        $log->info("Vehicle Start");

        $facility = Facility::with('facilityConfiguration')->find($request->facility_id);
        $vehicle = null;

        if (!empty($request->vehicle_id)) {
            $vehicle = self::handleVehicleUpdate($request->vehicle_id, $request, $user);
        } else {
            $vehicle = self::handleVehicleCreation($facility, $user, $request, $log);
        }

        // if ($vehicle) {
        //     $reservation->update(['vehicle_id' => $vehicle->id]);
        //     $log->info("Updated Vehicle ID in Reservation: " . $reservation->id . " to Vehicle ID: " . $vehicle->id);
        // }

        $log->info("Vehicle End with id: " . ($vehicle->id ?? 'N/A'));

        return $vehicle;
    }


    // Handle Vehicle Update
    public static function handleVehicleUpdate($vehicle_id, $request, $user)
    {
        $vehicle = self::find($vehicle_id);
        if (!$vehicle) {
            throw new ApiGenericException("Vehicle not found");
        }

        $fieldsToUpdate = ['make', 'model', 'license_plate_number', 'color', 'state_id', 'state_name', 'is_default', 'make_id', 'model_id', 'color_id', 'vehicle_type_id'];

        if (isset($request->make) && !empty($request->make)) {
            $makeModleData = self::createOrUpdateMakeAndModel($request->make, $request->model);
            $request->make_id = $makeModleData['make_id'];
            $request->model_id = $makeModleData['model_id'];
            unset($request->make, $request->model);
        } else if (isset($request->model) && !empty($request->model)) {
            $modelData = MstModel::findOrCreateModel($request->model, $request->make_id);
            $request->model_id = $modelData->id;
            unset($request->model);
        }
        foreach ($fieldsToUpdate as $field) {
            if ($vehicle->partner_id == config('parkengage.PARTNER_MAPCO')) {
                if (!empty($request->$field)) {
                    $vehicle->$field = strtoupper($request->$field);
                }
            } else {
                if (isset($request->$field)) {
                    $vehicle->$field = strtoupper($request->$field);
                }
            }
        }
        if (isset($request->vehicle_country) && !empty($request->vehicle_country)) {
            $vehicle->country = strtoupper($request->vehicle_country);
        } elseif (isset($request->country) && !empty($request->country)) {
            $vehicle->country = strtoupper($request->country);
        }
        if (isset($user->anon) && !$user->anon) $vehicle->anon = 0;  #DD pims-12970 permit restriction

        $vehicle_data = [];

        $vehicle->save();

        return $vehicle;
    }

    // Handle Vehicle Creation
    public static function handleVehicleCreation($facility, $user, $request, $log = null)
    {
        if (!$user->anon) {
            // $vehicle_count = $facility->facilityConfiguration->max_vehicle;
            $vehicle_count = CommonFunctions::getVehicleLimit($facility->owner_id, $facility->facilityConfiguration, $log);
            $current_count = self::where('user_id', $user->id)->count();
            if ($vehicle_count > 0 && $current_count >= $vehicle_count  && ($facility->owner_id != config('parkengage.PARTNER_USM'))) {
                return self::handleMaxVehicleCount($user, $request);
            }
        }
        return self::processVehicleCreation($user, $request, $facility->owner_id);
    }

    // Handle Max Vehicle Count
    private static function handleMaxVehicleCount($user, $request)
    {
        $existingVehicle = self::where([
            'license_plate_number' => $request->license_plate,
            'user_id' => $user->id
        ])->first();
        if ($existingVehicle) {
            return self::updateVehicle($existingVehicle, $request, $user);
        }

        throw new ApiGenericException("Vehicle limit exceeded");
    }

    // Process Vehicle Creation
    private static function processVehicleCreation($user, $request, $owner_id)
    {
        if (empty($request->license_plate)) {
            return null;
        }

        $existingVehicle = self::where([
            'license_plate_number' => $request->license_plate,
            'user_id' => $user->id
        ])->first();

        if ($existingVehicle) {
            return self::updateVehicle($existingVehicle, $request, $user);
        }

        $vehicle_data = self::prepareVehicleData($user, $request, $owner_id);
        return self::create($vehicle_data);
    }

    // Update Vehicle
    private static function updateVehicle($vehicle, $request, $user)
    {
        $vehicle_data = self::prepareVehicleData($vehicle->user, $request, $vehicle->partner_id);
        if (isset($user->anon)) {
            $anon = $user->anon;
            $vehicle_data['anon'] = $anon; #DD pims-12970 permit restriction
        }
        $vehicle->update($vehicle_data);

        return $vehicle;
    }

    // Prepare Vehicle Data
    private static function prepareVehicleData($user, $request, $owner_id)
    {
        #DD PIMS-10864
        $license_plate = NULL;
        if (isset($request->license_plate) && $request->license_plate != '') {
            $license_plate = $request->license_plate;
        } elseif (isset($request->license_plate_number) && $request->license_plate_number != '') {
            $license_plate = $request->license_plate_number;
        }

        #pims-13318  events country save issue
        $country = NULL;

        if (isset($request->vehicle_country)) {
            $country = $request->vehicle_country ?? NULL;
        } else if (isset($request->country)) {
            $country = $request->country ?? NULL;
        }

        $vehicle_data = [
            'license_plate_number' => strtoupper($license_plate ?? NULL),
            'vehicle_type_id' => strtoupper($request->vehicle_type_id ?? NULL),
            'make' => strtoupper($request->make ?? ''),
            'model' => strtoupper($request->model ?? ''),
            'color' => strtoupper($request->color ?? ''),
            'state_id' => $request->state_id ?? NULL,
            'make_id' => $request->make_id ?? NULL,
            'model_id' => $request->model_id ?? NULL,
            'color_id' => $request->color_id ?? NULL,
            'country' => $country,
            'partner_id' => $owner_id,
            'state_name' => strtoupper($request->state_name ?? ''),
            'is_default' => $request->is_default ?? 0,
            'anon'       => $user->anon  ?? 1
        ];
        if (isset($user)) {
            $vehicle_data['user_id'] = $user->id;
        }

        self::processMakeModel($vehicle_data);
        return array_filter($vehicle_data);
    }

    // Create or Update Make and Model
    public static function createOrUpdateMakeAndModel($makeName, $modelName)
    {
        if (empty($makeName)) {
            return;
        }

        $make = MstMake::firstOrCreate(['name' => strtoupper($makeName)], ['name' => strtoupper($makeName)]);
        $modelId = '';
        if (!empty($modelName)) {
            $model = MstModel::firstOrCreate(['name' => strtoupper($modelName), 'make_id' => $make->id], ['name' => strtoupper($modelName), 'make_id' => $make->id]);
            $modelId = $model->id;
        }
        return ['make_id' => $make->id, 'model_id' => $modelId];
    }

    private static function processMakeModel(array &$vehicle_data){
        if (isset($vehicle_data['make']) && !empty($vehicle_data['make'])) {
            $makeModleData = self::createOrUpdateMakeAndModel($vehicle_data['make'], $vehicle_data['model']);
            $vehicle_data['model_id'] = $makeModleData['model_id'];
            $vehicle_data['make_id'] = $makeModleData['make_id'];
            //save partner wise make mapping by vikrant
            self::savePartnerMakeModelMapping($vehicle_data);
            unset($vehicle_data['make'], $vehicle_data['model']);
        } else if (isset($vehicle_data['model']) && !empty($vehicle_data['model'])) {
            $modelData = MstModel::findOrCreateModel($vehicle_data['model'], $vehicle_data['make_id']);
            $vehicle_data['model_id'] = $modelData->id;
            unset($vehicle_data['model']);
        }
    }

    public static function savePartnerMakeModelMapping($vehicle_data){
        $partnerMakeModelMapping = PartnerMakeModelMapping::where("partner_id", $vehicle_data['partner_id'])->count();
        if ($partnerMakeModelMapping > 0) {
            //add partner make model mapping
            $mapping = new PartnerMakeModelMapping();
            $mapping->mst_make_id = $vehicle_data['make_id'];
            $mapping->partner_id = $vehicle_data['partner_id'];
            $mapping->save();
        }
        return true;
    }

    #PIMS-10864 DD start
    public function getColorAttribute($value){
        if (!is_null($this->color_id)) {
            $colorName = MstColor::where('id', $this->color_id)->value('name');
            return $colorName ?? $value;
        }
        return $value;
    }

    public function getMakeAttribute($value){
        if (!is_null($this->make_id)) {
            $makeName = MstMake::where('id', $this->make_id)->value('name');
            return $makeName ?? $value;
        }
        return $value;
    }
    public function getModelAttribute($value){
        if (!is_null($this->model_id)) {
            $modelName = MstModel::where('id', $this->model_id)->value('name');
            return $modelName ?? $value;
        }
        return $value;
    }

    #pims-13318
    public function getVehicleTypeAttribute($value){
        if (!is_null($this->vehicle_type_id)) {
            $vehicleTypeName = MstVehicleType::where('id', $this->vehicle_type_id)->value('name');
            return $vehicleTypeName ?? $value;
        }
        return $value;
    }

    #KT:07-03-2025 |PIMS-13075 
    public function getStyleAttribute($value){
        if (!is_null($this->style_id)) {
            $styleName = MstStyle::where('id', $this->style_id)->value('name');
            return $styleName ?? $value;
        }
        return $value;
    }

    public static function permitVehicleFilterData($value, $userId, $partner_id = NULL, $insertFlag = 1){
        #KT:07-03-2025 |PIMS-13075 
        $model_id = $make_id = $color_id = $style_id = NULL;
        if (isset($value->make_id) && !empty($value->make_id)) {
            $make_id = $value->make_id;
        } elseif (isset($value->make) && !empty($value->make)) {
            $modelName = isset($value->model) ? $value->model : '';
            $makeModleData = self::createOrUpdateMakeAndModel($value->make, $modelName);
            $model_id = $makeModleData['model_id'];
            if (!empty($model_id)) $value->model_id = $makeModleData['model_id'];
            $make_id = $makeModleData['make_id'];
            $value->make_id = $make_id;
        }

        if (isset($value->model_id) && !empty($value->model_id)) {
            $model_id = $value->model_id;
        } else if (isset($value->model) && !empty($value->model)) {
            $modelData = MstModel::findOrCreateModel($value->model, $value->make_id);
            $model_id = $modelData->id;
        }

        if (isset($value->color_id) && !empty($value->color_id)) {
            $color_id = $value->color_id;
        } elseif (isset($value->color)  && !empty($value->color)) {
            $colorData = MstColor::findOrCreateColor($value->color);
            $color_id = $colorData->id;
        }
        
        #KT:07-03-2025 |PIMS-13075 
        if (isset($value->style_id) && !empty($value->style_id)) {
            $style_id = $value->style_id;
        }

        #DD PIMS-13318
        if (isset($value->vehicle_type_id) && !empty($value->vehicle_type_id)) {
            $vehicle_type_id = $value->vehicle_type_id;
        }

        if (isset($value->style) && !empty($value->style)) {
            $style = $value->style;
        }
        #end

        #DD PIMS-10864
        $license_plate = NULL;
        if (isset($value->license_plate) && $value->license_plate != '') {
            $license_plate = $value->license_plate;
        } elseif (isset($value->license_plate_number) && $value->license_plate_number != '') {
            $license_plate = $value->license_plate_number;
        }

        $vehicle['vehicle_type_id']     = isset($vehicle_type_id) ? $vehicle_type_id : NULL; #DD pims-13318
        $vehicle['make_id']     = isset($make_id) ? $make_id : NULL;
        $vehicle['model_id']    = isset($model_id) ? $model_id : NULL;
        $vehicle['color_id']    = isset($color_id) ? $color_id : NULL;
        $vehicle['style_id']    = isset($style_id) ? $style_id : NULL;
        $vehicle['style']       = isset($style) ? $style : NULL;
        $vehicle['license_plate_number'] = $license_plate;
        $vehicle['make']    = isset($value->make) ? $value->make : '';
        $vehicle['model']   = isset($value->model) ? $value->model : '';
        $vehicle['color']   = isset($value->color) ? $value->color : '';
        $vehicle['user_id'] = isset($userId) ? $userId : NULL;
        $vehicle['state_id'] = isset($value->state_id) ? $value->state_id : '';
        $vehicle['is_default'] = isset($value->status_default) ? $value->status_default : 0;
        $vehicle['state_name']  = isset($value->state_name) ? $value->state_name : '';
        if(isset($partner_id) && !is_null($partner_id)){
            $vehicle['partner_id']  = isset($partner_id) ? $partner_id : '';
        }
        $vehicle['country']     = isset($value->country) ? $value->country : '';
        $vehicle['vehicle_number'] = isset($value->vehicle_number) ? $value->vehicle_number : NULL;
        #DD pims-12970 permit restriction
        if (isset($userId)) {
            $users = User::find($userId);
            if (isset($users->anon)) {
                $vehicle['anon'] = $users->anon;
            }
        }

        return $vehicle;
    }
    #PIMS-10864 DD end


    // VP:PIMS-14662
    public function fastTrackMappings()
    {
        return $this->hasMany(FastTrackVehicleMapping::class, 'permit_vehicle_id');
    }

    //PIMS-14772 || Dev: Sagar
    public function userFastTrackDetails()
    {
        return $this->hasOne(UserFastTrackDetails::class, 'user_id', 'user_id');
    }
}
