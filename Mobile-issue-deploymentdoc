### Change PIMS - 14799

CREATE TABLE `app_store_providers` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(100) NOT NULL,
  `slug` VARCHAR(50) NOT NULL UNIQUE,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);

INSERT INTO `app_store_providers` (`name`, `slug`) VALUES
('Apple Pay', 'apple-pay'),
('Google Pay', 'google-pay');

CREATE TABLE `inventory_modules_01_07_2025`.`app_store_urls` (
  `id` INT NOT NULL,
  `partner_id` INT NULL DEFAULT 0,
  `store_url` VARCHAR(255) NULL DEFAULT NULL,
  `created_at` TIMESTAMP NULL DEFAULT NULL,
  `updated_at` TIMESTAMP NULL DEFAULT NULL,
  `deleted_at` TIMESTAMP NULL DEFAULT NULL,
  <PERSON><PERSON>AR<PERSON> KEY (`id`));

  ALTER TABLE `inventory_modules_01_07_2025`.`app_store_urls` 
CHANGE COLUMN `id` `id` INT(11) NOT NULL AUTO_INCREMENT ;
ALTER TABLE `inventory_modules_01_07_2025`.`app_store_urls` 
ADD COLUMN `app_store_provider_id` INT UNSIGNED NULL AFTER `deleted_at`;
ALTER TABLE `inventory_modules_01_07_2025`.`app_store_urls` 
ADD COLUMN `title` VARCHAR(225) NULL DEFAULT NULL AFTER `app_store_provider_id`,
CHANGE COLUMN `app_store_provider_id` `app_store_provider_id` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `store_url`;
ALTER TABLE `inventory_modules_01_07_2025`.`app_store_urls` 
CHANGE COLUMN `title` `title` VARCHAR(225) NULL DEFAULT NULL AFTER `app_store_provider_id`;


INSERT INTO `inventory_modules_01_07_2025`.`app_store_urls` (`partner_id`, `store_url`, `created_at`, `updated_at`) VALUES ('307893', 'https://play.google.com/store/apps/details?id=com.parkengage.Park2Visit', '2025-08-01 19:02:02', '2025-08-01 19:02:02');
INSERT INTO `inventory_modules_01_07_2025`.`app_store_urls` (`partner_id`, `store_url`, `created_at`, `updated_at`) VALUES ('307893', 'https://apps.apple.com/us/app/park2visit/id6745950308', '2025-08-01 19:02:02', '2025-08-01 19:02:02');

INSERT INTO `inventory_modules_01_07_2025`.`app_store_urls` (`id`, `partner_id`, `store_url`, `created_at`, `updated_at`) VALUES (NULL, '307894', 'https://play.google.com/store/apps/details?id=com.parkengage.trirail', '2025-08-01 19:02:02', '2025-08-01 19:02:02');
INSERT INTO `inventory_modules_01_07_2025`.`app_store_urls` (`id`, `partner_id`, `store_url`, `created_at`, `updated_at`) VALUES (NULL, '307894', 'https://apps.apple.com/us/app/trirail-parking/id6746036799', '2025-08-01 19:02:02', '2025-08-01 19:02:02');
INSERT INTO `inventory_modules_01_07_2025`.`app_store_urls` (`id`, `partner_id`, `store_url`, `created_at`, `updated_at`) VALUES (NULL, '91860', 'https://play.google.com/store/apps/details?id=com.parkengage.preferredparking', '2025-08-01 19:02:02', '2025-08-01 19:02:02');
INSERT INTO `inventory_modules_01_07_2025`.`app_store_urls` (`id`, `partner_id`, `store_url`, `created_at`, `updated_at`) VALUES (NULL, '91860', 'https://apps.apple.com/us/app/preferred-parking-app/id6746559005', '2025-08-01 19:02:02', '2025-08-01 19:02:02');


UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `app_store_provider_id` = '2' WHERE (`id` = '1');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `app_store_provider_id` = '1' WHERE (`id` = '2');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `app_store_provider_id` = '2' WHERE (`id` = '3');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `app_store_provider_id` = '1' WHERE (`id` = '4');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `app_store_provider_id` = '2' WHERE (`id` = '5');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `app_store_provider_id` = '1' WHERE (`id` = '6');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `title` = 'Park here often?  Download the app!' WHERE (`id` = '3');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `title` = 'Park here often?  Download the app!' WHERE (`id` = '4');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `title` = 'Save time and download the app!' WHERE (`id` = '1');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `title` = 'Save time and download the app!' WHERE (`id` = '2');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `title` = 'Save time and download the app!' WHERE (`id` = '5');
UPDATE `inventory_modules_01_07_2025`.`app_store_urls` SET `title` = 'Save time and download the app!' WHERE (`id` = '6');



## New Change PIMS -14662
CREATE TABLE lpr_ticket_mappings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT UNSIGNED NOT NULL,
    lpr_feed_id INT NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at timestamp NULL DEFAULT NULL,
    UNIQUE(ticket_id, lpr_feed_id), -- prevent duplicate mapping
    FOREIGN KEY (ticket_id) REFERENCES tickets(id),
    FOREIGN KEY (lpr_feed_id) REFERENCES lpr_feeds(id)
); 
ALTER TABLE `inventory_modules_01_07_2025`.`lpr_ticket_mappings` 
ADD COLUMN `status` TINYINT(1) NULL DEFAULT 0 COMMENT '0=>checkin 1 => checkout' AFTER `lpr_feed_id`;


## New Change PIMS -14602 
CREATE TABLE `admin_alerts` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `partner_id` int(11) NOT NULL,  -- Changed from DEFAULT 0 to NOT NULL
   `facility_ids` json DEFAULT NULL,  -- Changed from text to json for better handling
   `active` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=inactive, 1=active',
   `alert_type` varchar(100) DEFAULT NULL COMMENT 'Type of alert',
   `priority` tinyint(1) DEFAULT 1 COMMENT '1=low, 2=medium, 3=high',
   `start_date` datetime DEFAULT NULL COMMENT 'When alert should start being active',
   `end_date` datetime DEFAULT NULL COMMENT 'When alert should expire',
   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
   `deleted_at` timestamp NULL DEFAULT NULL,
   PRIMARY KEY (`id`),
   KEY `idx_partner_id` (`partner_id`),
   KEY `idx_active` (`active`),
   KEY `idx_dates` (`start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
INSERT INTO `inventory_modules_01_07_2025`.`admin_alerts` (`partner_id`, `facility_ids`, `active`, `alert_type`, `priority`, `created_at`, `updated_at`) VALUES ('215900', '[299,300]', '1', 'payment-not-charge', '3', '2025-07-15 17:20:20', '2025-07-15 17:20:20');
UPDATE `inventory_modules_01_07_2025`.`admin_alerts` SET `alert_type` = 'ticket-not-charged' WHERE (`id` = '1');
ALTER TABLE `inventory_modules_01_07_2025`.`admin_alerts` 
ADD COLUMN `secret` VARCHAR(100) NULL DEFAULT NULL COMMENT 'Partner Secret' AFTER `end_date`;
UPDATE `inventory_modules_01_07_2025`.`admin_alerts` SET `secret` = 'C0Qo6praTGHKJAzEHYh5id7XE' WHERE (`id` = '1');

ALTER TABLE all_failed_transactions ADD INDEX idx_reference_key (reference_key);
UPDATE `inventory_modules_11_07_2025`.`admin_alerts` SET `facility_ids` = '[298,299,300,301,317,318,326,328,352,353,354,355,356,363,365,367]' WHERE (`id` = '1');





#DB Changes
1. partner_payment_gateways  => Done
2. frontend_facilities       => Done
3. refund_transactions       => Done By Ujjwal


ALTER TABLE `datacap_payment_profile` 
ADD COLUMN `facility_payment_type_id` TINYINT(1) NULL DEFAULT 0 AFTER `is_default`;

ALTER TABLE `datacap_payment_profile` 
CHANGE COLUMN `facility_payment_type_id` `facility_payment_type_id` TINYINT(1) NULL DEFAULT '0' AFTER `partner_id`;

ALTER TABLE `datacap_payment_profile` 
ADD COLUMN `partner_payment_gateway_id` TINYINT(1) NULL DEFAULT 0 AFTER `facility_payment_type_id`;

ALTER TABLE `planet_payment_profile` 
ADD COLUMN `facility_payment_type_id` TINYINT(1) NULL DEFAULT 0 AFTER `is_default`;

ALTER TABLE `planet_payment_profile` 
ADD COLUMN `partner_payment_gateway_id` TINYINT(1) NULL DEFAULT 0 AFTER `is_default`;

ALTER TABLE `heartland_payment_profile` 
ADD COLUMN `facility_payment_type_id` TINYINT(1) NULL DEFAULT 0 AFTER `card_brand_transaction_id`,
ADD COLUMN `partner_payment_gateway_id` TINYINT(1) NULL DEFAULT 0 AFTER `facility_payment_type_id`;

### VIJAY transient case for user creation 
ALTER TABLE `users` 
CHANGE COLUMN `password` `password` VARCHAR(255) NULL DEFAULT NULL ;


ALTER TABLE facility_payment_details
ADD COLUMN data_trans_merchant_name varchar(255) DEFAULT NULL AFTER dataTrans_error_url,
ADD COLUMN data_trans_gpay_merchant varchar(255) DEFAULT NULL AFTER data_trans_merchant_name;

ALTER TABLE `tickets` 
ADD COLUMN `payment_method` VARCHAR(45) NULL DEFAULT NULL AFTER `checkout_facility_id`;

ALTER TABLE `overstay_tickets` 
ADD COLUMN `payment_method` varchar(100) DEFAULT NULL;



# Vijay :
ALTER TABLE `facilities` 
ADD COLUMN `additonal_fee` DECIMAL(10,2) NULL DEFAULT 0.00 AFTER `pass_processing_fee`,
ADD COLUMN `additonal_fee_type` TINYINT(1) NULL DEFAULT 1 COMMENT '0 => value, 1 => Percentage' AFTER `additonal_fee`;

UPDATE `facilities` SET `additonal_fee` = '6.52' WHERE (`id` = '297');


ALTER TABLE `tickets` 
ADD COLUMN `additional_fee` DOUBLE(10,2) NULL DEFAULT 0.00 AFTER `payment_method`;


# 18-02-2025 Deployed
ALTER TABLE `ticket_extends` 
ADD COLUMN `additional_fee` DOUBLE(10,2) NULL DEFAULT 0.00 AFTER `is_priceband_apply`;

# Vijay : 24-05-2025
ALTER TABLE `facilities` 
ADD COLUMN `surcharge_fee` DECIMAL(10,2) NULL AFTER `additonal_fee_type`,
ADD COLUMN `surcharge_fee_type` TINYINT(1) NULL DEFAULT 1 COMMENT '0 => value, 1 => Percentage' AFTER `surcharge_fee`;

ALTER TABLE `ticket_extends` 
ADD COLUMN `surcharge_fee` DOUBLE(10,2) NULL DEFAULT 0.00 AFTER `additional_fee`;

#UPDATE `inventory_modules_12_02_2025`.`facilities` SET `additonal_fee` = '4.36', `surcharge_fee` = '1' WHERE (`id` = '297');

# 27-02-2025 Deployed
ALTER TABLE `facility_configurations` 
ADD COLUMN `error_msg` VARCHAR(255) NULL DEFAULT NULL COMMENT 'this message is display in case of facility in not operation or rate not defined' AFTER `is_oversize_enabled`;


#UPDATE `facility_configurations` SET `error_msg` = 'Payment is not required at this time. Please refer to the posted payment requirements.' WHERE (`id` = '220');

#28-02-2025
ALTER TABLE `tickets` 
ADD COLUMN `surcharge_fee` DOUBLE(10,2) NULL DEFAULT 0.00 AFTER `is_vehicle_oversize`;



# Vijay : 07-03-2025
INSERT INTO `service_permissions` (`id`, `service_id`, `display_name`, `web`, `is_default`, `type`, `parent_id`) VALUES (NULL, '3', 'Check-In Against Passes', '/admin/checkin-new#CheckinPasses', '0', '2', '159');

#vijay 
ALTER TABLE `facility_payment_details` 
ADD COLUMN `hl_apple_pay_cert` VARCHAR(225) NULL DEFAULT NULL AFTER `heartland_gpay_merchant`,
ADD COLUMN `hl_apple_pay_key` VARCHAR(225) NULL DEFAULT NULL AFTER `hl_apple_pay_cert`;

ALTER TABLE `inventory_modules_30_01_25`.`ticket_extends` 
ADD COLUMN `payment_method` VARCHAR(45) NULL DEFAULT NULL AFTER `is_priceband_apply`,
ADD COLUMN `validated_amount` DOUBLE(10,2) NULL DEFAULT 0.00 AFTER `payment_method`,
ADD COLUMN `parking_amount` DOUBLE(10,2) NULL DEFAULT 0.00 AFTER `validated_amount`,
ADD COLUMN `refund_release_status` TINYINT(1) NULL DEFAULT 0 COMMENT '0 => Default , 1 => Refund, 2 => released' AFTER `parking_amount`;

ALTER TABLE `inventory_modules_19_03_2025`.`ticket_extends` 
ADD COLUMN `refund_amount` DOUBLE(10,2) NULL DEFAULT 0.00 AFTER `oversize_fee`;
ALTER TABLE `inventory_modules_19_03_2025`.`tickets` 
ADD COLUMN `refund_release_status` TINYINT(1) NULL DEFAULT 0 COMMENT '0 => Default , 1 => Refund, 2 => released' AFTER `oversize_fee`;

ALTER TABLE `inventory_modules_19_03_2025`.`refund_transactions` 
ADD COLUMN `payment_gateway` VARCHAR(50) NULL DEFAULT NULL COMMENT 'one of the value (datacap/planel/heartland)' AFTER `refund_for`,
CHANGE COLUMN `local_anet_transaction_id` `original_transaction_id` BIGINT NULL DEFAULT NULL COMMENT 'this will store for which txn we do the refund and void' ;


ALTER TABLE `refund_transactions`
ADD COLUMN `payment_gateway` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'one of the value (datacap/planel/heartland)';

ALTER TABLE `refund_transactions`
ADD COLUMN `original_transaction_id` bigint(20) DEFAULT NULL COMMENT 'this will store for which txn we do the refund and void'

alter table permit_vehicles add anon tinyint(1) not null default 0;
alter table user_permit_vehicles add anon tinyint(1) not null default 0;

# PIMS-13707
ALTER TABLE `inventory_modules_17_04_2025`.`facility_configurations` 
ADD COLUMN `day_wise_price` TINYINT(1) NULL DEFAULT 0 COMMENT '0 => prevent old logic 1 => new change for days' AFTER `is_attendant_lpr_enabled`;
INSERT INTO `inventory_modules_17_04_2025`.`rate_categories` (`id`, `category`, `created_at`, `updated_at`, `no_of_days`, `total_usage`, `is_resident`, `is_event_enabled`, `is_usage_finite`) VALUES (NULL, 'Monthly', '2025-04-28 06:23:00', '2025-04-28 06:23:00', '0', '0', '0', '0', '0');
UPDATE `inventory_modules_17_04_2025`.`rates` SET `category_id` = '65' WHERE (`id` = '577');
'monthlyCategoryId' 	=> '65',  //Config file
# PIMS-13707



# PIMS - 13575
ALTER TABLE `permit_vehicles` 
CHANGE COLUMN `anon` `anon` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '0 => register user vehicle  1 => Gues user vehicle' ;

# PIMS - 13575


#PIMS - 13805
ALTER TABLE `inventory_modules_17_04_2025`.`custom_text_messages` 
ADD COLUMN `slug` VARCHAR(45) NULL DEFAULT NULL AFTER `message`,
ADD COLUMN `partner_id` INT NULL DEFAULT 0 AFTER `slug`,
ADD COLUMN `facility_id` INT NULL DEFAULT 0 AFTER `partner_id`,
ADD COLUMN `created_at` TIMESTAMP NULL DEFAULT NULL AFTER `facility_id`,
ADD COLUMN `updated_at` TIMESTAMP NULL DEFAULT NULL AFTER `created_at`,
ADD COLUMN `deleted_at` TIMESTAMP NULL DEFAULT NULL AFTER `updated_at`;

UPDATE `inventory_modules_17_04_2025`.`custom_text_messages` SET `facility_id` = '327' WHERE (`id` = '5');

#PIMS - 13805


#PIMS  - 13857
# Deployment : 12-05-2025
## PIMS- 13707
ALTER TABLE `facility_configurations` 
ADD COLUMN `day_wise_price` TINYINT(1) NULL DEFAULT 0 COMMENT '0 => prevent old logic 1 => new change for days' AFTER `is_attendant_lpr_enabled`;
#INSERT INTO `rate_categories` (`id`, `category`, `created_at`, `updated_at`, `no_of_days`, `total_usage`, `is_resident`, `is_event_enabled`, `is_usage_finite`) VALUES (NULL, 'Monthly', '2025-05-12 06:23:00', '2025-05-12 06:23:00', '0', '0', '0', '0', '0');
#UPDATE `rates` SET `category_id` = '65' WHERE (`id` = '577');

#PIMS - UPBL-122

CREATE TABLE master_facility_short_code (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    short_code VARCHAR(255) NOT NULL UNIQUE,
    type TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 = GARAGE_CODE, 1 = QRCODE_SERIES',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '1 = active, 0 = inactive',
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE short_code_facility_mapping (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    short_code_id BIGINT UNSIGNED NOT NULL,
    facility_id BIGINT UNSIGNED NOT NULL,
    partner_id BIGINT UNSIGNED NOT NULL,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (short_code_id) REFERENCES master_facility_short_code(id) ON DELETE CASCADE
); 

START TRANSACTION;
-- Step 1: Insert garage_code into master_facility_short_code
INSERT IGNORE INTO master_facility_short_code (
    short_code, 
    type, 
    created_at, 
    updated_at
)
SELECT 
    garage_code, 
    0, 
    CURRENT_TIMESTAMP, 
    CURRENT_TIMESTAMP
FROM facilities
WHERE garage_code IS NOT NULL
AND garage_code NOT IN (
    SELECT short_code 
    FROM master_facility_short_code 
    WHERE type = 0
);

-- Step 2: Insert mappings into short_code_facility_mapping
INSERT INTO short_code_facility_mapping (
    short_code_id, 
    facility_id, 
    partner_id, 
    created_at, 
    updated_at
)
SELECT 
    m.id AS short_code_id,
    f.id AS facility_id,
    f.owner_id AS partner_id,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM facilities f
INNER JOIN master_facility_short_code m 
    ON f.garage_code = m.short_code 
    AND m.type = 0
WHERE NOT EXISTS (
    SELECT 1 
    FROM short_code_facility_mapping scfm 
    WHERE scfm.short_code_id = m.id 
    AND scfm.facility_id = f.id
);

COMMIT;



# Deployment : 12-05-2025

# Deployment : 08-05-2025
CREATE TABLE ticket_additional_info (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    ticket_id BIGINT(20) DEFAULT NULL,
    ticket_type VARCHAR(255) DEFAULT NULL,
    new_parking_amount DECIMAL(10,2) DEFAULT NULL,
    new_tax_amount DECIMAL(10,2) DEFAULT NULL,
    new_processing_fee DECIMAL(10,2) DEFAULT NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    permit_id BIGINT(20) DEFAULT NULL,
    reservation_id INT(11) DEFAULT NULL,
    user_pass_id INT(11) DEFAULT NULL,
    new_discount_amount DECIMAL(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

ALTER TABLE ticket_extends
ADD COLUMN new_parking_amount DECIMAL(10,2) NULL DEFAULT 0.00;

ALTER TABLE events
ADD display_rate_with_event TINYINT(1) NOT NULL DEFAULT 0 AFTER created_by;

CREATE TABLE `mst_vehicle_types` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`name` varchar(255) NOT NULL,
`code` varchar(255) DEFAULT NULL,
`partner_id` int(10) DEFAULT NULL,
`created_at` timestamp NULL DEFAULT NULL,
`updated_at` timestamp NULL DEFAULT NULL,
`deleted_at` timestamp NULL DEFAULT NULL,
PRIMARY KEY (`id`)
);

alter table permit_vehicles add (vehicle_type_id int(11) default null);
alter table user_permit_vehicles add (vehicle_type_id int(11) default null);
insert into mst_vehicle_types(name,code,partner_id,created_at) VALUES('HEV (Hybrid Electric Vehicle)','HEV',NULL,NOW()),('PHEV (Plug-in Hybrid Electric Vehicle)','PHEV',NULL,NOW());
insert into mst_vehicle_types(name,code,partner_id,created_at) VALUES('HEV (Hybrid Electric Vehicle)','HEV',19349,NOW()),('PHEV (Plug-in Hybrid Electric Vehicle)','PHEV',19349,NOW());
insert into mst_vehicle_types(name,code,partner_id,created_at) VALUES('BEV/EV (Fully Electric)','BEV/EV',NULL,NOW()),('Conventional (Gas or Diesel powered)','Conventional',NULL,NOW());
insert into mst_vehicle_types(name,code,partner_id,created_at) VALUES('BEV/EV (Fully Electric)','BEV/EV',19349,NOW()),('Conventional (Gas or Diesel powered)','Conventional',19349,NOW());
INSERT INTO `customerportal_permissions_master` (`id`, `partner_id`, `name`, `display_name`, `description`, `is_default`, `type`, `list_order`, `created_at`, `updated_at`) VALUES (NULL, '19349', 'Get Vehicle Type', 'Get Vehicle Type', 'Get Vehicle Type', '0', '3', '25', '2025-03-07 10:48:51', '2025-03-07 10:48:51');
alter table blacklisted_vehicles add (vehicle_type_id int(11) default null);

## UPBL-81 Menlo
update permit_rates set active='0' where id=675;  ##//faculty
 update permit_rates set active='0' where id=673; ##//studen
update permit_rate_descriptions set permit_start_date='2025-08-11',permit_end_date='2026-05-18' where id=68;  ##//non freshman
 update permit_rate_descriptions set permit_start_date='2025-08-11',permit_end_date='2026-05-18' where id=69; ##//freshman
 update permit_rate_descriptions set is_promotion='1' where id=71;		##//food service
 update permit_rate_descriptions set is_promotion='1' where id=72;      ##//con
 update permit_rate_descriptions set is_promotion='1' where id=73;      ##//jen
 update permit_rate_descriptions set is_promotion='1' where id=74;      ##//trus
 update permit_rate_descriptions set permit_start_date='2025-05-18',permit_end_date='2025-08-11' where id=85;

### Deployment : 08-05-2025

# Deploymnet : 23-04-2025
ALTER TABLE permit_requests ADD COLUMN third_party_type int(11) default 0;
ALTER TABLE permit_requests
ADD COLUMN third_party_subscription_id VARCHAR(255) NULL default NULL,
ADD COLUMN third_party_user_id VARCHAR(255) NULL default NULL,
ADD COLUMN third_party_account_id VARCHAR(255) NULL default NULL;

ALTER TABLE users ADD COLUMN thirdparty_userid VARCHAR(255) NULL UNIQUE ;
alter table permit_rate_descriptions add is_thirdparty enum('0','1') default '0';
alter table permit_rates add is_thirdparty enum('0','1') default '0';

#INSERT INTO `permit_rate_descriptions` ( `name`, `name_alias`, `hours_description`, `description`, `active_status`, `partner_id`, `type_id`, `is_resident`, `created_at`, `label`, `is_promotion`, `campus_id`, `order`, `is_hide_staff`, `is_hide_vehicle`, `is_hide_permit_service`, `is_purchase`, `is_negotiable`, `email_domain_validation`, `is_display_reason`, `is_hide_validity`, `show_carpool_service`, `is_hide_carpool_toogle`, `permit_start_date`, `permit_frequency`, `is_hide_upload_id`, `is_hide_campusId`, `is_hide_employeeID`, `is_required_campusId`, `is_required_employeeID`, `is_thirdparty`) VALUES ('Employee', 'Employee', 'Monthly', 'Monthly', '1', '40867', '1', '0', '2025-04-14 06:36:48', 'Employee', '0', '40867', '1', '1', '0', '1', '1', '0', '0', '0', '1', '1', '1', '2025-03-01', '6', '1', '0', '1', '1', '0', '1');

select * from permit_rate_descriptions order by id desc limit 2;

#INSERT INTO `permit_rates` ( `permit_rate_description_id`, `facility_id`, `rate`, `active`, `name`, `is_unlimited`, `rate_type`, `total_count`, `total_usage`, `remaining_usage`, `is_resident`, `user_type`, `created_at`, `updated_at`, `is_promotion`, `monday`, `tuesday`, `wednesday`, `thursday`, `friday`, `saturday`, `sunday`, `entry_time_begin`, `entry_time_end`, `exit_time_begin`, `exit_time_end`, `is_hide_staff`, `is_hide_vehicle`, `is_hide_permit_service`, `is_display_reason`, `is_hide_carpool_toogle`, `prorate_disable`, `penality_amount`, `is_thirdparty`) VALUES (84, '272', '0.00', '1', 'Employee', '0', '0', '0', '0', '0', '0', '1', '2025-04-14 02:40:36', '2025-04-14 02:40:36', '0', '0', '0', '0', '0', '0', '0', '0', '00:00:00', '00:00:00', '00:00:00', '00:00:00', '1', '0', '0', '0', '0', '0', '0', '1');

# Deployment : 24-04-2025

ALTER TABLE facility_payment_details
  ADD COLUMN heartland_device_id varchar(255) DEFAULT NULL,
  ADD COLUMN heartland_site_id varchar(255) DEFAULT NULL,
  ADD COLUMN heartland_website_id varchar(255) DEFAULT NULL;
 
UPDATE facility_payment_details SET heartland_device_id = '4855367', heartland_site_id = '497015', heartland_website_id = '33674741' WHERE (id = '166');
UPDATE facility_payment_details SET heartland_device_id = '4855369', heartland_site_id = '497015', heartland_website_id = '33674745' WHERE (id = '167');
UPDATE facility_payment_details SET heartland_device_id = '4855370', heartland_site_id = '497015', heartland_website_id = '33674746' WHERE (id = '168');
UPDATE facility_payment_details SET heartland_device_id = '4870987', heartland_site_id = '497015', heartland_website_id = '33679263' WHERE (id = '169');


 
SET FOREIGN_KEY_CHECKS = 0;
truncate table inventory_modules.master_facility_short_code;
SET FOREIGN_KEY_CHECKS = 1;

#PIMS - 13857

ALTER TABLE `inventory_modules`.`refund_transactions` 
CHANGE COLUMN `original_transaction_id` `original_transaction_id` VARCHAR(255) NULL DEFAULT NULL COMMENT 'this will store for which txn we do the refund and void' ;

#PIMS - 14154
INSERT INTO `inventory_modules_11_06_2025`.`customerportal_permissions_master` (`id`, `partner_id`, `name`, `display_name`, `description`, `is_default`, `type`, `list_order`, `created_at`, `updated_at`) VALUES (NULL, '215900', 'Enabled CC Fee', 'Enabled CC Fee', 'Enabled CC Fee', '0', '3', '25', '2025-06-13 17:48:51', '2025-06-13 17:48:51');
INSERT INTO `inventory_modules_11_06_2025`.`customerportal_permissions_master` (`id`, `partner_id`, `name`, `display_name`, `description`, `is_default`, `type`, `list_order`, `created_at`, `updated_at`) VALUES (NULL, '215900', 'Enabled CitySurcharge', 'Enabled CitySurcharge', 'Enabled CitySurcharge', '0', '3', '25', '2025-06-13 17:48:51', '2025-06-13 17:48:51');
