app/storage

.phpstorm.meta.php
### Vagrant template
.vagrant/

### JetBrains template
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio

*.iml

## Directory-based project format:
.idea/
# if you remove the above rule, at least ignore the following:

# User-specific stuff:
# .idea/workspace.xml
# .idea/tasks.xml
# .idea/dictionaries

# Sensitive or high-churn files:
# .idea/dataSources.ids
# .idea/dataSources.xml
# .idea/sqlDataSources.xml
# .idea/dynamic.xml
# .idea/uiDesigner.xml

# Gradle:
# .idea/gradle.xml
# .idea/libraries

# Mongo Explorer plugin:
# .idea/mongoSettings.xml

## File-based project format:
*.ipr
*.iws

## Plugin-specific files:

# IntelliJ
/out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties

/storage/Fonts/.
.DS_Store
/vendor
/node_modules
/public/storage
/storage/facility_images/.
Homestead.yaml
Homestead.json
storage/
### Node template
# Logs
logs
*.log
npm-debug.log*
/scripts/wkhtmltox-0.12.2_linux-trusty-amd64.deb

# Runtime data
pids
*.pid
*.seed
*.zip
*.txt
!database/sql.txt

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directory
# https://docs.npmjs.com/misc/faq#should-i-check-my-node-modules-folder-into-git
node_modules

.env
.lock
# composer.lock
supervisor/*
#.htaccess

resources/assets/docs/index.html

phpcbf-fixed.diff
/nbproject/private/

#Backup files
app/Classes/ParkengageGateApiBkp_CurlFlow.php
app/Console/Commands/Reports/RevenueReportPermit.php-12-11-2024
resources/views/revenue_reports/usm_pdf.blade.php-19-11-2024
app/Console/Commands/Pci/WoodmanTicketAutoPayPaymentHeartland.php-backup
app/Models/Reservation.php-21-11-2024
public/merchant_identity_cert.pem
public/merchant_identity_key.pem
public/
-2024
.php-
resources/views/mapco/booking-details.blade.php-03-12-2024
resources/views/platform/email/common_pdf.blade.php-04-12-2024
resources/views/reservation/barcode-atlanta.blade.php-04-12-2024
resources/views/reservation/email-atlanta.blade.php-04-12-2024
resources/views/reservation/print-reservation-atlanta.blade.php-04-12-2024

#12-12-2024
app/Console/Commands/Reservation/Email.php-12-12-2024
app/Console/Commands/Reservation/ReservationAutoPayPaymentHeartland.php-12-12-2024
app/Http/Controllers/HubZeag/HubZeagController.php-12-12-2024
resources/views/platform/email/common_pdf.blade.php-12-12-2024
resources/views/reservation/email-atlanta.blade.php-12-12-2024
resources/views/reservation/print-reservation-atlanta.blade.php-12-12-2024

#26-12-2024
app/Console/Commands/HubZeag/HubCheckin.php-19-12-2024
app/Jobs/ManageCheckInCheckout/SendEmail.php-18-12-2024
resources/views/promo/promocode_report.blade.php-23-12-2024

#07-01-2025
app/Http/Controllers/ParkEngage/PaveCheckinCheckoutController.php-06-01-2025
app/Exceptions/Handler.php
# composer.json

#22-01-2025
app/Console/BkpKernel.php
config/bkpparkengage.php
app/Console/Kernel.php-22-01-2025
config/22-01-2025_parkengage.php
# app/Console/Kernel.php
config/parkengage.php

.vscode/
Backup/
app-26-122024/
app/Classes/pushcert.pem
app/Http/Controllers/ParkEngage/ReportParkEngage-1.php
app/Console/Commands/HubZeag/HubCheckin-21-JAN-2025.php

## Vijay : 23-01-2025
# app/Console/Kernel.php
config/parkengage.php

## Vijay : 27-01-2025
app/Console/Kernel.php-24-01-2025
config/parkengage.php-24-01-2025

pull-staging.sh